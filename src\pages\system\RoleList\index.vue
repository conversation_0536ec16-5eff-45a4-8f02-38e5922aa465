<!-- 系统管理-角色管理 -->
<template>
  <a-card>
    <div>
    <div class="content">
      <standard-table
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :selectedRows.sync="selectedRows"
        @change="handleTableChange"
        :pagination="pagination"
        :loading="loading"
        class="mainstyle"
      >
        <span slot="roleName" slot-scope="{ text, record }" @contextmenu.prevent.stop="rightClick1($event,text)">
          <a :title='text' style="color: #000000;padding-right: 10px;"> {{ text }}</a>
          <a-tag color="red" v-if="record.isStatic">系统</a-tag>
          <a-tag color="blue" v-if="record.isDefault">默认</a-tag>
          <a-tag color="blue" v-if="record.isPublic">公开</a-tag>
        </span>
        <template slot="num" slot-scope="{ index }">
        {{(pagination.current-1)*pagination.pageSize+parseInt(index)+1}}
       </template>
        <div slot="action" slot-scope="{ record }">
          <template>
                  <!-- <a v-if="checkPermission('AbpIdentity.Roles.Update')" href="javascript:;" @click="$refs.createModal.openModal(record)" >编辑 
                     <div data-v-50499b08 role="separator" class="ant-divider ant-divider-vertical"></div></a>
                 
                  <a href="javascript:;"  v-if="checkPermission('AbpIdentity.Roles.ManagePermissions')" @click="$refs.permissionModal.openModal(record)">权限  
                    <div data-v-50499b08 role="separator" class="ant-divider ant-divider-vertical"></div></a>  
                    <a-popconfirm title="确定要删除吗？" @confirm="handleDel(record.id)"   >
                      <a href="javascript:;">删除</a>
                    </a-popconfirm> -->
            <a-dropdown>
              <a class="ant-dropdown-link" href="#" >
                操作
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay">
                <!-- v-if="checkPermission('AbpIdentity.Roles.Update')" -->
                <a-menu-item >
                  <a
                    href="javascript:;"
                    @click="$refs.createModal.openModal(record)"
                    style="font-weight:500;color:#000000;"
                    >编辑</a
                  >
                </a-menu-item>
                <!-- v-if="checkPermission('AbpIdentity.Roles.ManagePermissions')" -->
                <a-menu-item >
                  <a href="javascript:;" @click="$refs.permissionModal.openModal(record)"
                  style="font-weight:500;color:#000000">权限</a>
                </a-menu-item>
                <!-- v-if="checkPermission('AbpIdentity.Roles.Delete')" -->
                <a-menu-item >
                  <a-popconfirm
                  :title="deletee"
                    @confirm="handleDel(record.id)"
                    style="font-weight:500;color:#000000"
                  >
                    <a href="javascript:;">删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </template>
        </div>
      </standard-table>
      <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="down">复制</a-menu-item>
        </a-menu> 
      </div>
      <div class="bto">
        <div class="operator">
        <a-button v-if="checkPermission('AbpIdentity.Roles.Create')" @click="$refs.createModal.openModal({})" type="primary"
          >新建</a-button>
      </div>
      </div>
    </div>
    <create-form ref="createModal" @ok="handleOk" />
    <permission-form ref="permissionModal" provider-name="R"/>
  </a-card>
</template>

<script> 
import StandardTable from "@/components/table/StandardTable";
import { getList, del } from "@/services/identity/role";
import CreateForm from "@/pages/system/RoleList/modules/RoleForm";
import PermissionForm from "@/pages/system/components/PermissionForm";
import { checkPermission } from '@/utils/abp';
const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "num" },
    align:'center',
    width:45,
  },
  {
    title: "角色名称",
    dataIndex: "name",
    scopedSlots: { customRender: "roleName" },
    align:'left',
    width:1520,
  },
  {
    title: "操作",
    scopedSlots: { customRender: "action" },
    align:'center',
     width:70,
  },
];
let that;
export default {
  name: "RoleList",
  components: { StandardTable, CreateForm,PermissionForm },
  data() {
    return {
      menuVisible:false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      menuData:{},
      advanced: true,
      columns: columns,
      dataSource: [],
      selectedRows: [],
      // pagination: {
      //   pageSize: 20,
      //   current: 1,
      //   showQuickJumper:true,
      //   showTotal:total => `总计 ${total} 条`
      // },
      pagination: {
          pageSize: 20,
          current: 1,
          total:0,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["20", "50", "100"],//每页中显示的数据
          showTotal: (total) => `总计 ${total} 条`,
      },
      sorter: {
        field: "Name",
        order: "asc",
      },
      loading: false,
      queryParam: {},
      categorys: [],
    };
  },
  // authorize: {
  //   deleteRecord: "delete",
  // },
  created() {
    this.$nextTick(()=>{
      this.handleResize()
    })
  },
  mounted() {
    that=this;
    window.addEventListener('resize', this.handleResize, true)
    this.loadData();
  },
  beforeDestroy(){
    window.removeEventListener('resize', this.handleResize, true)
   },
  methods: {
    handleResize(){
      var mainstyle = document.getElementsByClassName('mainstyle')[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var content = document.getElementsByClassName('content')[0]
      if(window.innerHeight<=911){
        content.style.height = window.innerHeight - 155 +'px'
      }else{
        content.style.height = '761px'
      }  
      if(mainstyle && this.dataSource.length!=0){
        mainstyle.style.height =  window.innerHeight - 190 +'px'
      }else{
        mainstyle.style.height = 0
      }
      var paginnum = ''
        var footerwidth =  window.innerWidth-224
        if(Math.ceil(this.pagination.total/20)>10){
          paginnum = 7
        }else{
          paginnum = Math.ceil(this.pagination.total/20)
        }
        if((((paginnum*50)+310)<footerwidth) ){
        this.pagination.simple=false
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
        }else{
        this.pagination.simple=false
        this.pagination.size = 'small'
        this.pagination.showSizeChanger = false
        this.pagination.showQuickJumper = false
       }
    },
    rightClick1(e,text){
      if(text || text == 0){
        this.text = text 
        this.menuVisible = true;
        this.menuStyle.top = e.clientY- 110 +  "px";
        this.menuStyle.left =  e.clientX - document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
        document.body.addEventListener("click", this.bodyClick);
      }
      
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },    
    down(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
      },
    deletee(){
     return <span style="font-weight: 500;">确定要删除吗？</span>
    },
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    onSelectChange() {
      this.$message.info("选中行改变了");
    },
    handleDel(id) {
      del(id).then((res) => {
        this.loadData();
        this.$message.info("删除成功");
      });
    },
    handleOk() {
      this.loadData();
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      this.loadData();
    },
    loadData() {
      this.loading = true;
      let params = {
        ...this.pagination,
        ...this.queryParam,
        sorter: this.sorter,
      };
      getList(params).then((res) => {      
          const pagination = { ...this.pagination };
          pagination.total = res.totalCount;
          this.pagination = pagination;
          this.dataSource = res.items;
          setTimeout(() => this.handleResize(), 0);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    refresh() {
      this.pagination.current = 1;
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-pagination-prev {
    margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input{
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal{
  margin:0
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager{
  margin: 0;
}
/deep/.ant-pagination-slash {
    margin: 0;
}
.tabRightClikBox{
  border:2px solid rgb(238, 238, 238) !important;
  li{
    height:24px;
    line-height:24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color:#000000
  }
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-table-thead > tr > th{
  padding: 6px !important;  
  // border-right:1px solid #efefef;   
  border-right:1px solid #efefef; 
}
/deep/.ant-table-tbody > tr > td {
  padding: 6px !important;  
  border-right:1px solid #efefef; 
}
/deep/ .ant-card-body{
  padding:0px;
}
/deep/ .ant-form-item{
  padding-bottom:0;
}
/deep/.ant-table-pagination.ant-pagination {   
      margin: 14px 11px;
      float: left;
      position: absolute;
    }
.content{
  border: 2px solid #efefef;
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #F8F8F8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-tbody > tr.ant-table-row-selected td {    
    background: #dfdcdc;
}

}
.bto{
  height:58px;
  border: 3px solid #E9E9F0;
  }
.search {
  margin-bottom: 54px;
}
.fold {
  width: calc(100% - 216px);
  display: inline-block;
}
.operator {
  width: 90px;
  margin-bottom:0;
  padding-top: 6px;
  text-align: center;
  float: right;

}
@media screen and (max-width: 900px) {
  .fold {
    width: 100%;
  }
}
</style>