<!--客诉初审-->
<template>
    <a-spin :spinning="spinning">  
        <div class="Initialreview">
            <a-tabs  :activeKey="activeKey"  @tabClick="tabClick">
            <a-tab-pane key="30" tab="待初审"> 
            </a-tab-pane>
            <a-tab-pane key="40" tab="已初审">
            </a-tab-pane>
        </a-tabs>
        <div style="display: flex;padding-bottom: 7px;">
            <a-input placeholder="请输入订单编号/客户编号" style="width: 250px;margin-left: 15px;"
            @keydown.enter.native="seach()" v-model="searchdata.CommonOrderNo" ></a-input>
            <a-range-picker  :format="'YYYY-MM-DD'"  @change="onChange" v-model="timeValue"
            style="margin-left: 15px;" :placeholder="['客诉开始时间', '客诉结束时间']">
            <template #suffixIcon>
                <a-icon type="calendar" />
            </template>
            </a-range-picker>
            <a-button type="primary" style="margin-left: 15px;" @click="seach()"><a-icon type="search"></a-icon>查询</a-button>
        </div>  
        <review-table :columns="columns" :auditdata="auditdata" :activeKey="activeKey" :pagination="pagination" 
         @tableChange="handleTableChange" @sendclick="sendclick"/>  
        <div class="footerAction" style='user-select:none;margin-left: -2px;'>
          <div>
            <!-- <a-button type="primary" class="box">
                取单
            </a-button>  -->
          </div>             
        </div>      
        </div>
    </a-spin>
</template>
<script>
import {factorycomplaintspagelist,factoryfirstreviewsend} from "@/services/complaint/QualitativeComplaint.js";
import ReviewTable from '@/pages/OrderManagement/Initialreview/module/ReviewTable'
const columns = [
{
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 65,
    ellipsis:true,
    fixed: 'left',
},   
{
    title: "客诉编号",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "订单编号",
    dataIndex: "orderNo",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "生产编号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,   
},
{
    title: "联系人",
    dataIndex: "factoryContactName",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "联系电话",
    dataIndex: "factoryContactNumber",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "投诉日期",
    dataIndex: "complaintDate",
    align: "left",
    ellipsis: true,
    width:180,       
},
{
    title: "客户要求",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:200,       
},
{
    title: "8D报告",
    align: "left",
    ellipsis: true,
    width:80,  
    customRender:(record)=>record.need8DReport?'是':'否'     
},
{
    title: "文件下载",
    scopedSlots:{customRender:'filedown'},
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "状态",
    dataIndex: "statusStr",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "操作",
    align: "center",
    ellipsis: true,
    width:100,       
    scopedSlots:{customRender:'operation'}
},
];
export default {
    name:'Initialreview',
    components:{ReviewTable},
    data(){
        return{
            searchdata:{},
            spinning:false,
            timeValue:undefined,
            columns,
            activeKey:'30',
            pagination: {
                pageSize: 20,
                current: 1,
                total:0,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: [ "20", "50", "100"],//每页中显示的数据
                showTotal: (total) => `总计 ${total} 条`,
            },
            auditdata:[],
        }
    },
    created(){
    },
    mounted(){
        this.getdata({status:this.activeKey})
    },
    methods:{   
        onChange(date, dateString) {
                this.searchdata.StartDate=dateString[0]
                this.searchdata.EndDate=dateString[1]
            },
        sendclick(record){
            factoryfirstreviewsend(record.id).then(res=>{
                if(res.code){
                    this.$message.success(res.message)
                    this.getdata({status:this.activeKey})
                }else{
                   this.$message.error(res.message)
                }
            })
        },     
        tabClick(key){
            this.activeKey = key
            this.searchdata={}
            this.timeValue=undefined
            this.getdata({status:this.activeKey})
        },
        // 订单表变化change
        handleTableChange(pagination, filters, sorter) {
            this.pagination.current=pagination.current
            this.pagination.pageSize=pagination.pageSize
            if(JSON.stringify(this.searchdata) == "{}"){
                this.getdata({status:this.activeKey})
            }else{
                let params = {
                    status:this.activeKey,
                    ...this.searchdata
                }
                this.getdata(params)
            }
        },  
        seach(){
            if(JSON.stringify(this.searchdata)=='{}'){
                this.$message.error('请输入查询条件')
                return
            }
            let params = {
                status:this.activeKey,
                ...this.searchdata
            }
            this.getdata(params)
        },
        getdata(val){
            this.spinning=true
            let params = {
            'PageIndex': this.pagination.current,
            'PageSize' : this.pagination.pageSize,        
            }  
        params = {...params,...val}
        factorycomplaintspagelist(params).then(res=>{
            if(res.code){
                this.auditdata =res.data.items
            }else{
                this.$message.error(res.message)
            }
        }).finally(() => {
            this.spinning = false;
        });
        },
    }
}

</script>
<style lang="less" scoped>
.Initialreview{
    background-color: white;
    width: 100%;
}
.footerAction {
    width: 100%;
    height:46px;
    background: white;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    .box{
        float: right;
        margin-top: 6px;
        margin-right:10px;
        width: 90px;
    }
}
</style>