<template>
  <!-- :title="form.id ? '修改用户' : '添加用户'" -->
  <div ref="SelectBox"> 
    <a-modal    
    :width="780"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
  <h3 name="title" v-if="form.id" style="font-weight:500">修改用户-{{userName}}</h3>
  <h3 name="title" v-else style="font-weight:500">添加用户</h3>  
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        layout="inline"
      >
        <a-tabs tab-position="top">
          <a-tab-pane key="1" tab="用户信息">
            <a-row>
            <a-col :span='8'>
            <a-form-model-item label="登录名" ref="userName" prop="userName" :label-col="{ span: 6}" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-input
                :disabled="editFlg"
                v-model="form.userName"
                placeholder="请输入角色名称"
                @blur="
                  () => {
                    $refs.userName.onFieldBlur();
                  }
                "
              />
            </a-form-model-item>
            </a-col>
            <a-col :span='8'>           
            <a-form-model-item
              label="密码"
              ref="password"
              prop="password"
              
              :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width:100%; margin:0"
            >
              <a-input
                v-model="form.password"
                type="password"
                @blur="
                  () => {
                    $refs.password.onFieldBlur();
                  }
                "
              />
            </a-form-model-item>
            </a-col>
            
            <a-col :span='8'>
            <a-form-model-item label="姓  名" prop="name" ref="name" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
              <a-input v-model="form.name"             
                />
            </a-form-model-item>
            </a-col>
            </a-row>

            <a-row>
              <a-col :span='8'>
            <a-form-model-item label="工号" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-input
                  v-model="form.workNo"
                  placeholder='请输入工号'
              />
            </a-form-model-item>
            </a-col>              
            <a-col :span='8'>
            <a-form-model-item label="邮箱地址" ref="email" prop="email" :label-col="{ span:8}" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
              <a-input
                v-model="form.email"                
              />
            </a-form-model-item>
            </a-col>
            <a-col :span='8'>
            <a-form-model-item label="手机号码" ref="phoneNumber" prop="phoneNumber" :label-col="{ span:8}" :wrapper-col="{ span: 16 }" style="width:100%; margin:0" >
              <a-input v-model="form.phoneNumber" />
            </a-form-model-item>
            </a-col>
            </a-row>

            <a-row>              
              <!-- <a-col :span='13'>
            <a-form-model-item label="所属工厂" prop="postId" :label-col="{ span: 6}" :wrapper-col="{ span: 18 }" style="width:100%; margin:0">
              <a-cascader 
              v-model="factoryData"
              :options="departGroupPostDto"
              placeholder="请选择"
              @change="onAreaChange"  
              :show-search="{ filter }"            
              :field-names="{label: 'label', value: 'id', children: 'children'}"   
              :getPopupContainer="(trigger) => {return trigger.parentElement}" 
               />             
            </a-form-model-item>           
            </a-col> -->
            
            <a-col :span='8'>
            <a-form-model-item label="主集" prop="factoryId" :label-col="{ span: 6}" :wrapper-col="{ span:14 }" style="width:100%; margin:0">
              <a-select v-model="form.factoryId" placeholder="请选择主集"  optionFilterProp="label" showSearch allowClear  @change="facChange" :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option  v-for=" item in comBoxItems" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            </a-col>
            <a-col :span='8'>
            <a-form-model-item label="部门" :label-col="{ span: 8}" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
              <a-select v-model="form.departmentId" placeholder="请选择部门" showSearch allowClear optionFilterProp="label" @change="departChange" :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option  v-for=" item in departmentDtos" :key="item.id" :value="item.id" :label="item.departmentName">
                  {{ item.departmentName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            </a-col>
            <a-col :span='8'>
            <a-form-model-item label="小组"  :label-col="{ span: 8}" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
              <a-select v-model="form.groupId" placeholder="请选择小组" showSearch allowClear optionFilterProp="label" @change="groupIdChange" :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option  v-for=" item in userGroupDtos" :key="item.id" :value="item.id" :label="item.groupName">
                  {{ item.groupName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            </a-col>
            <a-col :span='8'>
            <a-form-model-item label="岗位" prop="postId" :label-col="{ span: 6}" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-select v-model="form.postId" placeholder="请选择岗位" showSearch allowClear optionFilterProp="label" :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option  v-for=" item in userPostDtos" :key="item.id" :value="item.id" :label="item.postName">
                  {{ item.postName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            </a-col>
            <a-col :span='16'>
              <a-form-model-item label="用户首页"  :label-col="{ span:4}" :wrapper-col="{ span: 20 }" style="width:100%; margin:0">
                  <a-input v-model="form.homepage" />
                </a-form-model-item>            
            </a-col>            
            </a-row>
             <a-row>
            
            <a-col :span='24'>
              <a-form-model-item label="授权"  :label-col="{ span:2}" :wrapper-col="{ span: 22}" style="width:100%; margin:0">
              <a-select v-model="form.tradeTypeSrc" mode="multiple" placeholder="请选择" showSearch allowClear optionFilterProp="label" :getPopupContainer="()=>this.$refs.SelectBox">
                   <a-select-option  v-for=" item in comBoxItems" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
              </a-col>
             <!-- <a-col :span='13'>
            <a-form-model-item label="授权工厂" prop="FacAccredit" :label-col="{ span: 6}" :wrapper-col="{ span: 18 }" style="width:100%; margin:0">
              <a-select v-model="form.facAccredit" mode="multiple" placeholder="请选择" showSearch allowClear optionFilterProp="label">
                   <a-select-option  v-for=" item in factoryList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            </a-col> -->            
             </a-row>
             <a-row class="lastRow">
            <a-col :span='5'>
            <a-form-model-item label="工程前端"  :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-checkbox v-model="form.isMakeData_"/>
            </a-form-model-item>
            </a-col>
            <a-col :span='5'>
            <a-form-model-item label="工程后端"  :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-checkbox v-model="form.isMIVerify_"/>
            </a-form-model-item>
            </a-col>
            <a-col :span='5'>
                <a-form-model-item label="工程审核"  :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
                  <a-checkbox v-model="form.isChkData_"/>
                </a-form-model-item>
              </a-col>
              <!-- <a-col :span='8'>
                <a-form-model-item label="工程锣带"  :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
                  <a-checkbox v-model="form.isCncDispatch_"/>
                </a-form-model-item>
              </a-col> -->

              <a-col :span='5'>
            <a-form-model-item label="订单预审"  :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-checkbox v-model="form.isMktPre_"/>
            </a-form-model-item>
            </a-col>
            <a-col :span='4'>
            <a-form-model-item label="手机号登录"  :label-col="{ span: 15 }" :wrapper-col="{ span: 9 }" style="width:100%; margin:0">
              <div v-if="form.phoneNumber">
              <a-checkbox  v-model="form.isPhoneVerified"/>
              </div>
              <div v-else>
              <a-checkbox  disabled/>
              </div>
            </a-form-model-item>
            </a-col>
            </a-row>


          </a-tab-pane>
          <a-tab-pane key="2" tab="角色" class="roleDiv">
            <!-- <a-form-model-item>
              <a-checkbox-group v-model="form.roleNames">
                <a-checkbox
                  v-for="role in assignableRoles"
                  :key="role.id"
                  :label="role.name"
                  :value="role.name"
                  name="roleName"
                >
                  {{ role.name }}
                </a-checkbox>
              </a-checkbox-group>
            </a-form-model-item> -->            
            <a-form-model-item style="height:250px;overflow: auto;width:100%">
              <a-checkbox-group v-model="form.roleNames">
                <!-- <h3 style="margin:2px;font-size:14px;">供应链:</h3> -->
                <a-checkbox
                  v-for="role in other"
                  :key="role.id"
                  :label="role.name"
                  :value="role.name"
                  name="roleName"
                >
                  {{ role.name }}
                </a-checkbox>
                <!-- <h3 style="margin:2px;font-size:14px;">市场:</h3>
                <a-checkbox
                  v-for="role in market"
                  :key="role.id"
                  :label="role.name"
                  :value="role.name"
                  name="roleName"
                >
                  {{ role.name }}
                </a-checkbox>
                <h3 style="margin:2px;font-size:14px;">工程:</h3>
                <a-checkbox
                  v-for="role in engineering"
                  :key="role.id"
                  :label="role.name"
                  :value="role.name"
                  name="roleName"
                >
                  {{ role.name }}
                </a-checkbox>
                <h3 style="margin:2px;font-size:14px;">生产:</h3>
                <a-checkbox
                  v-for="role in production"
                  :key="role.id"
                  :label="role.name"
                  :value="role.name"
                  name="roleName"
                >
                  {{ role.name }}
                </a-checkbox>
                <h3 style="margin:2px;font-size:14px;">车间:</h3>
                <a-checkbox
                  v-for="role in workshop"
                  :key="role.id"
                  :label="role.name"
                  :value="role.name"
                  name="roleName"
                >
                  {{ role.name }}
                </a-checkbox>                
                <h3 style="margin:2px;font-size:14px;">其他:</h3>
                <a-checkbox
                  v-for="role in other"
                  :key="role.id"
                  :label="role.name"
                  :value="role.name"
                  name="roleName"
                >
                  {{ role.name }}
                </a-checkbox> -->
              </a-checkbox-group>
            </a-form-model-item>
          </a-tab-pane>
          <!-- <a-tab-pane key="3" tab="组织单元" :forceRender="true">
            <a-form-model-item>
              <org-tree ref="dialogOrgTree" v-model="form.orgIds" />
            </a-form-model-item>
          </a-tab-pane> -->
        </a-tabs>
      </a-form-model>
    </a-spin>
  </a-modal>
  </div>

</template>

<script>
import {
  get,
  // createUpdate,
  createUser,
  updateUser,
  getAssignableRoles,
  getRolesByUserId,
  getOrganizationsByUserId,
  getFactoryList, getUserInfo,getTradeTypeSrcList,getDepartGroupPostList
} from "@/services/identity/user";
import { email } from "@/plugins/validate.js";
import {mapState,} from 'vuex'
// import OrgTree from "@/components/module/organization/OrganizationTree";
export default {
  // components: { OrgTree },
  props:['factoryList','departGroupPostDto','allData'],
  name: "UserForm",
  inject:['reload'],
  data() {
    const passwordValidator = (rule, value, callback) => {
      console.log('passwordValidator',value,rule)
      if (this.form.id && !value) {   
        callback();
        return;
      }
      if (!value) {
        callback(new Error("密码必须填写"));
        return;
      }
      if (value.length < 6) {
        callback(new Error("密码至少为6个字符."));
        return;
      }
      callback();
    };    
    return {
      showDom:false,
      labelCol: { span: 7 },
      wrapperCol: { span: 10 },
      visible: false,
      confirmLoading: false,
      form: {
        orgIds: [],
        factoryId:'',
        homepage:'',
        // extraProperties:{Sex:1}
        extraProperties: '1',
        departmentId:'',
        groupId:'',
        postId:'',
        password:'',
        provinceData:[],
        isMakeData_:false, 
        isMIVerify_:false,
        isChkData_:false,
      },      
      rules: {
        userName: [
          { required: true, message: "用户名必须填写", trigger: "blur" },
        ],
        name: [
          { required: true, message: "姓名必须填写", trigger: "blur" },
        ],
        password: [
          { validator: passwordValidator, required: true,trigger: ["blur", ],type: 'string', },
        ],
        homepage: [{ required: true, message: '必须填写用户首页', trigger: 'change' }],
        factoryId: [{ required: true, message: '必须选择主集', trigger:["blur", "change"],}],
        postId: [{ required: true, message: '必须选择岗位', trigger:["blur", "change"],}],        
        tradeTypeSrc: [{ required: true, message: '必须选择授权', trigger: 'change' }],        
        email: [{
            type: "email",
            required: true,
            message: "请填写正确的邮箱地址",
            trigger: "blur",
          },
          { type: 'string', message: '长度不能超过32位', trigger: 'blur', max: 32 }
        ],
        phoneNumber: [
          { pattern: /^1\d{10}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
        ],
      },
      assignableRoles: null,
      // factoryList:[],
      tradeTypeSrcList:[],
      httpType:'', // 更新与新建请求方式
      editFlg:false,
      userName:'',
      Supply:[], // 供应链
      workshop:[], // 车间
      production:[], // 生产
      engineering:[], // 工程
      market:[], // 市场
      tool:[], // 工具
      other:[], // 其他
      // departGroupPostDto:[],
      comBoxItems:[],  // 主集
      departmentDtos:[], // 部门  
      userGroupDtos:[], // 小组 
      userPostDtos:[], // 岗位
      factoryData:[],  
      distradeTypeSrc:false,
       
    };
  },
  computed: {
    ...mapState('account', ['user',]),  
  },
  mounted(){
    if(this.user.userName == 'caisheng' || this.user.userName == 'hyk'){
      this.distradeTypeSrc = false
    }else{
      this.distradeTypeSrc = true
    }
  },
  created() {
    // getFactoryList().then(res => {
    //   this.factoryList = res?.data
    //   // console.log('this.factoryList',this.factoryList)
    // });
    // getDepartGroupPostList().then(res=>{
    //   var arrFac = this.factoryList
    //   var arrD = res?.data.departmentDtos
    //   var arrG = res?.data.userGroupDtos
    //   var arrP = res?.data.userPostDtos
    //     var newArrD=[]
    //     var newArrG=[]
    //     var newArrP=[]
    //     for(var f=0;f < arrFac.length;f++){
    //       var objFac={}
    //       newArrD = arrD.filter(item => { return item.factoryId == arrFac[f].valueMember})
    //       objFac.id = arrFac[f].valueMember;
    //       objFac.label = arrFac[f].text;
    //       objFac.children=[];
    //       for(var d=0;d < newArrD.length;d++){
    //       var objD={}
    //       newArrG = arrG.filter(item => { return item.departId == newArrD[d].id })
    //       objD.id = newArrD[d].id;
    //       objD.ID = newArrD[d].factoryId;
    //       objD.label = newArrD[d].departmentName;
    //       objD.level ='D'; 
    //       objD.children=[];
    //       for(var g=0;g < newArrG.length;g++){
    //         var objG={}
    //         newArrP = arrP.filter(ite => { return ite.groupId == newArrG[g].id })
    //         objG.id = newArrG[g].id;
    //         objG.ID = newArrG[g].departId;
    //         objG.label = newArrG[g].groupName;
    //         objG.level ='G'; 
    //         objG.children=[];                   
    //         for(var p=0;p < newArrP.length;p++){
    //           var objP={}
    //           objP.id = newArrP[p].id;
    //           objP.ID = newArrP[p].groupId;
    //           objP.label = newArrP[p].postName;
    //           objP.level ='P'; 
    //           objG.children.push(objP)           
    //         }
    //         objD.children.push(objG)
    //       }  
    //       objFac.children.push(objD)            
    //     } 
    //     this.departGroupPostDto.push(objFac)  
    //     }

         
    //     // console.log('组织架构11',this.departGroupPostDto)
        
    // })
    // getTradeTypeSrcList().then(res => {
    //   this.tradeTypeSrcList = res?.data
    // });
  },
  methods: {
    // pnumber(){
    //   if(!this.form.phoneNumber){

    //   }
    // },
    openModal(model,type) {
      let arr1 = this.allData.comBoxItems.filter(item =>{return item.imp_CtlThicknessInH == 1})  
      this.comBoxItems = arr1
      // this.departmentDtos = this.allData.departmentDtos
      // this.userGroupDtos = this.allData.userGroupDtos       
      this.userPostDtos = this.allData.userPostDtos
      this.userName = model.userName
      this.visible = true;     
      this.httpType = type
      if (model && model.id) {
        this.getFormData(model.id);
        this.editFlg = true; 
      } else {
        this.form = {};
      }
      getAssignableRoles().then((response) => {
        this.assignableRoles = response.items;
        this.assignableRoles.forEach(item =>{
          // if(item.name == '供应链管理' || item.name == '供应链开拓' || item.name == '交期管理' || item.name == '财务查看'){
          //   this.Supply.push(item)
          // }
          // else if(item.name == '成型车间' || item.name == 'AGV调度' || item.name == '磨板管理' || item.name == '钻孔车间'|| item.name == '飞针车间' || item.name == '开料车间' || item.name == '文字车间'){
          //   this.workshop.push(item)
          // }
          // else if(item.name == '生产管理' || item.name == '外发管理' || item.name == '生产报表' ){
          //   this.production.push(item)
          // }
          // else if(item.name == '工程管理' || item.name == '工程前端' || item.name == '工程QAE' || item.name == '工程后端'|| item.name == '协同工程' ){
          //   this.engineering.push(item)
          // }
          // else if(item.name == '市场录单' || item.name == '审单'){
          //   this.market.push(item)
          // }          
          // else{
          //   this.other.push(item)
          // }
          // if(item.name == 'admin' || item.name == 'ICAM制作' || item.name == '供应链管理' || item.name == '供应链开拓' ||item.name == '工程管理' || item.name == '工程前端' || item.name == '工程QAE' || item.name == '工程后端'){
          //   this.Supply.push(item)
          // }                   
          // else{
          //   this.other.push(item)
          // }
          this.other.push(item)
        })
        // console.log('value',this.assignableRoles)
      });
    },
    //选择区域后的操作
    onAreaChange(val, selectedOptions) {
        console.log(val,selectedOptions, 'val, selectedOptions')
        // if(!val[0] || !val[1] || !val[2] || !val[2]){
        //  this.showDom = true
        //  return 
        // }
        this.form.factoryId = Number(val[0])
        this.form.departmentId = val[1]
        this.form.groupId = val[2]
        this.form.postId = val[3]
    },
    // facChange(){
    //   this.departmentDtos = this.departGroupPostDto.filter(item =>{return item.ID == this.form.factoryId})
    // },
    // departChange(){   
    //   this.userGroupDtos = this.departmentDtos.filter(item =>{return item.id == this.form.departmentId})[0].children
    // },
    // GroupChange(){      
    //   this.userPostDtos = this.userGroupDtos.filter(item =>{return item.id == this.form.groupId})[0].children     
    // },
    resetForm() {
      this.form = {};
    },
    groupIdChange(){
      this.$forceUpdate();
    },
    getFormData(id) {
      this.confirmLoading = true;     
      getUserInfo(id)
        .then((res) => {          
          this.form = Object.assign({ roleNames: [] }, res);
          if(this.form.factoryId == null ){
            this.form.factoryId = ''
          }else{
            this.form.factoryId = String(res.factoryId)
          }
          if(this.form.departmentId == 0){
            this.form.departmentId = ''
          }
          if(this.form.groupId == 0){
            this.form.groupId = ''
          }
          if(this.form.postId == 0){
            this.form.postId = ''
          }
          if(this.form.factoryId){
            let arr1 = this.comBoxItems.filter(item =>{return item.valueMember == this.form.factoryId})            
            if(arr1){
              let iType = arr1[0].iType
              if(iType || iType == 0){
              this.departmentDtos = this.allData.departmentDtos.filter(item =>{return item.pId == iType})
              }
            }
          }         
          if(this.form.departmentId){           
            let Id = this.departmentDtos.filter(item =>{return item.id == this.form.departmentId})[0].id 
            if(Id || Id == 0) {
              this.userGroupDtos = this.allData.userGroupDtos.filter(item =>{return item.pId == Id}) 
            } 

          } 
          getRolesByUserId(id).then((response) => {
            response.items.forEach((item) => {
              this.form.roleNames.push(item.name);
            });
          });
          // getOrganizationsByUserId(id).then((response) => {
          //   this.form.orgIds = response.items.map((item) => item.id);
          //   // this.$refs.dialogOrgTree.checkedKeys=this.form.orgIds;
          // });
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
      this.editFlg = false; 
      this.Supply = []; // 供应链
      this.workshop = []; // 车间
      this.production = [];// 生产
      this.engineering = [];// 工程
      this.market = []; // 市场
      this.tool = []; // 工具
      this.other = []; // 其他    
      this.factoryData = [],
      this.$refs.ruleForm.resetFields(); 
    },
    filter(inputValue, path){
      return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
    },
    // 获取cookie缓存登录名
    getcookie(userName){//获取指定名称的cookie的值
      var arrstr = document.cookie.split("; ");
      for(var i = 0;i < arrstr.length;i ++){
        var temp = arrstr[i].split("=");
        if(temp[0] == userName) {          
          this.userName = unescape(temp[1])
          // return unescape(temp[1]);
        }
      }
    },
    handleOk() {
      this.Supply = []; // 供应链
      this.workshop = []; // 车间
      this.production = [];// 生产
      this.engineering = [];// 工程
      this.market = []; // 市场
      this.tool = []; // 工具
      this.other = []; // 其他
      this.form.factoryId = Number(this.form.factoryId)
      if(this.form.departmentId == ''){
        this.form.departmentId = null
      }
      if(this.form.groupId == ''){
        this.form.groupId = null
      }
      const form = this.$refs.ruleForm;      
      if(!this.form.roleNames) {
        this.form['roleNames'] = []
      }   
      // console.log('valid',this.form,this.form.factoryId,this.form.postId)
      this.getcookie('userName')
      console.log('651this.other',this.other);
      console.log('this.user',this.user.userName)
      if(this.user.userName == this.userName ){
        console.log('相同',this.userName) 
          this.confirmLoading = true;
          form.validate((valid) => {        
            if (valid) {
              if(this.form.id){
                updateUser(this.form, )
                .then((res) => {        
                  this.visible = false;
                  form.resetFields();
                  this.$message.info("操作成功");
                  this.factoryData=[]
                  this.$emit("ok");
                  this.$emit('getDepartGroupPostList')
                })
                .finally(() => {
                  this.confirmLoading = false;
                  this.editFlg = false; 
                });

              }else{
                createUser(this.form, this.httpType)
                .then((res) => {                      
                  this.visible = false;
                  form.resetFields();
                  this.$message.info("操作成功");
                  this.factoryData=[]
                  this.$emit("ok");
                  this.$emit('getDepartGroupPostList')
                })
                .finally(() => {
                  this.confirmLoading = false;
                  this.editFlg = false; 
                });
              }
              
            } else {
              this.confirmLoading = false;
              this.factoryData=[]
            }
          });       
      }else{
        console.log('不相同',this.userName)
        location.reload()
      }     
    },
    findElem(arrayToSearch, attr, val) {
    for (var i = 0; i < arrayToSearch.length; i++) {
          if (arrayToSearch[i][attr] == val) {
              return i;
          }
      }
      return -1;
    },
    facChange(){
      const iType = this.comBoxItems.filter(item =>{return item.valueMember == this.form.factoryId})[0].iType
      // console.log('this.comBoxItems',this.form.factoryId,this.comBoxItems,)
      // console.log('this.allData.departmentDtos',iType,this.allData.departmentDtos)
      if(iType || iType == 0){
        this.departmentDtos = this.allData.departmentDtos.filter(item =>{return item.pId == iType})
      }else{
        this.departmentDtos = []
        this.userGroupDtos = []
      }
      // console.log('this.departmentDtos',this.departmentDtos,)
      // console.log('this.form',this.form,this.form.factoryId,)
      if(this.findElem(this.departmentDtos,'id',this.form.departmentId) < 0){
        this.form.departmentId = ''
        this.form.groupId = ''
        this.userGroupDtos = []
      }else{
        const Id = this.departmentDtos.filter(item =>{return item.id == this.form.departmentId})[0].id      
        this.userGroupDtos = this.allData.userGroupDtos.filter(item =>{return item.pId == Id}) 
      }
         
      // console.log('部门',this.findElem(this.departmentDtos,'id',this.form.departmentId),this.form.departmentId,this.departmentDtos)
      
    },
    departChange(){
      if(!this.form.departmentId){
        this.form.groupId = ''
        this.userGroupDtos = []
      }
      if(this.form.departmentId){
        const Id = this.departmentDtos.filter(item =>{return item.id == this.form.departmentId})[0].id      
      this.userGroupDtos = this.allData.userGroupDtos.filter(item =>{return item.pId == Id})   
      if(this.findElem(this.userGroupDtos,'id',this.form.groupId) < 0){
        this.form.groupId = ''
      }
      }
      // console.log('小组',this.form.groupId,this.userGroupDtos)
      
    }
  },
};
</script>

<style scoped lang="less">
  /deep/.ant-tabs{
    color:#000000;
  }
  /deep/.ant-form-item-label > label{
    color:#000000;
  }
  /deep/.ant-checkbox-wrapper{
    color:#000000;
  }
  /deep/.ant-form-item{
    color:#000000;
  }
  /deep/.ant-select{
      color:#000000;
    }
    /deep/.ant-input{
      color:#000000;
    }
    /deep/.ant-cascader-picker{
      color:#000000;
    }
    /deep/.ant-select-selection--multiple .ant-select-selection__choice{
      color:#000000;
    }
.roleDiv{
  /deep/.ant-form-item-control-wrapper {
    width:100%;
  } 
  /deep/.ant-form-item-control{
    width:100%;
  }
  /deep/.ant-checkbox-group{
    width:100%;
  }
}
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  color:#000000;
}
/deep/.ant-input{
  font-weight: 500;
}
.lastRow{
  /deep/.ant-form-item-control{
    line-height:12px;
  }
}
.ant-form label {   
  width: 20%;
  margin-left:9px;
  margin-top:10px;
}
/deep/.ant-checkbox-wrapper{
  margin-top:13px!important;
}
// .ant-form-item{
//   width:100%;
//   // .ant-form-item-control-wrapper{
//   //   width:100% !important;
//   // }
// }
// /deep/.ant-form-inline .ant-form-item > .ant-form-item-control-wrapper{
//   width:100%;
// }
// .ant-checkbox-group{
//   width:100%;
// }
</style>
