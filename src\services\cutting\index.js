import { request, METHOD } from "@/utils/request";
export async function automaticSplicing(params) {
  return request("/api/app/e-mSTPpe-make-pnl-parameter/auto-kL", METHOD.POST, params);
}
export async function automaticImgae(params) {
  return request("/api/app/e-mSTPpe-make-pnl-parameter/auto-kLPanel-output", METHOD.POST, params);
}
export async function getMaterialData(params) {
  return request("/api/app/e-mSTPub-pnl-material-size", METHOD.GET, params);
}
export async function updateMaterialData(params) {
  return request("/api/app/e-mSTPub-pnl-material-size/update", METHOD.POST, params);
}
export async function manualCompositionData(params) {
  return request("/api/app/e-mSTPpe-make-pnl-parameter/manual-kL", METHOD.POST, params);
}
export async function dataSave(params) {
  return request("/api/app/e-mSTPpe-make-pnl-parameter/kl-info-save", METHOD.POST, params);
}
// 获取原有数据
export async function dataOutput(joinFactoryId, params) {
  return request(`/api/app/e-mSTPpe-make-pnl-parameter/data-output/${joinFactoryId}?OrderNo=${params}`, METHOD.POST);
}
// 界面参数配置化
export async function dataInfoOutput(params) {
  return request(`/api/app/e-mSTPpe-make-pnl-parameter/data-info-output`, METHOD.POST, params);
}
// 开料拼版成功后调取拉伸系数接口
export async function settensilecoef(JoinFactoryId,Pdctno) {
  return request(`/api/app/e-mSTPpe-make-pnl-parameter/set-tensilecoef/${JoinFactoryId}?Pdctno=${Pdctno}`, METHOD.POST);
}

export default {
  automaticSplicing,
  automaticImgae,
  getMaterialData,
  updateMaterialData,
  manualCompositionData,
  dataSave,
  settensilecoef,
};
