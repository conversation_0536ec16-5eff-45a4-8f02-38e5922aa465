<!-- 市场管理 - 订单预审- 客户规则 -->
<template>
  <div class="table">
    <div class="leftBox">
      <div class="top">
        <a-table
          :columns="columns1"
          bordered
          rowKey="guid_"
          :pagination="false"
          :data-source="CustomerData"
          :rowClassName="isRedRow"
          :customRow="onClick"
          :orderListTableLoading="orderListTableLoading"
        >
          <template slot="isChange" slot-scope="text, record">
            <a-checkbox v-model="record.isChange" />
          </template>
          <template slot="isChange" slot-scope="text, record">
            <a-checkbox v-model="record.isChange" />
          </template>
        </a-table>
      </div>
      <div class="bto" @contextmenu.prevent="rightClick($event)">
        <a-table
          :columns="columns2"
          :data-source="attData"
          :customRow="onClickRow"
          :pagination="false"
          bordered
          :rowClassName="isRedRow1"
          rowKey="guid_"
        >
        </a-table>
        <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="alypath()">查看附件</a-menu-item>
        </a-menu>
      </div>
    </div>
    <div class="rightBox">
      <template>
        <a-form>
          <a-form-item label="规格类型" :label-col="{ span: 4 }" :wrapper-col="{ span: 8 }">
            <div style="margin-top: 5px; border-radius: 4px; border: 1px solid #d9d9d9; height: 30px; width: 175px">
              <p style="padding-left: 5px; line-height: 28px">{{ selectedRow.ruleSketch_ }}</p>
            </div>
          </a-form-item>

          <a-form-item>
            <a-checkbox :checked="selectedRow.isAble_">是否有效</a-checkbox>
            <a-checkbox :checked="selectedRow.isAuto_">自动规则</a-checkbox>
            <a-checkbox :checked="selectedRow.isMktPre_">订单预审</a-checkbox>
            <a-checkbox :checked="selectedRow.isMIMake_">工程制作</a-checkbox>
            <a-checkbox :checked="selectedRow.riskWarning">风险警告</a-checkbox>
          </a-form-item>
          <a-form-item label="规则描述" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <br />
            <div style="margin-top: -28px; border-radius: 4px; border: 1px solid rgb(217, 217, 217); height: 333px; width: 356px">
              <p style="padding: 5px; line-height: 20px">{{ selectedRow.ruleDescribe_ }}</p>
            </div>
          </a-form-item>
        </a-form>
      </template>
    </div>
  </div>
</template>

<script>
import { getRuleEntry } from "@/services/projectMake";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "规格类型",
    dataIndex: "ruleSketch_",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "是否有效",
    dataIndex: "isAble_",
    width: 80,
    ellipsis: true,
    scopedSlots: { customRender: "isAble_" },
    align: "left",
  },
  {
    title: "自动规则",
    dataIndex: "isAuto_",
    width: 80,
    ellipsis: true,
    scopedSlots: { customRender: "isAuto_" },
    align: "left",
  },
  {
    title: "规则描述",
    dataIndex: "ruleDescribe_",
    width: 400,
    ellipsis: true,
    align: "left",
  },
  {
    title: "修改人",
    dataIndex: "inUser_",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "修改时间",
    dataIndex: "inDate_",
    width: 180,
    ellipsis: true,
    align: "left",
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "附件名",
    dataIndex: "realFileName_",
    width: 120,
    ellipsis: true,
    align: "left",
  },
  {
    title: "录入人",
    dataIndex: "inUser_",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "录入时间",
    dataIndex: "inDate_",
    width: 120,
    ellipsis: true,
    align: "left",
  },
];
export default {
  name: "CustomerRulesInfo",
  props: ["CustomerData"],
  created() {
    // console.log('this.CustomerData:',this.CustomerData)
  },

  data() {
    return {
      columns1,
      columns2,
      isSpecistack: false,
      orderListTableLoading: false,
      orderListTableLoading1: false,
      imgData: [],
      selectedRowKeysArray: [],
      selectedRow: {},
      selectedRowKeysArray1: [],
      imageLoading: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      attData: [],
      selectedRow1: [],
    };
  },

  methods: {
    onClick(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowKeysArray = keys;
            this.selectedRow = record;
            this.attClick(record);
            console.log("this.selectedRow:", this.selectedRow);
          },
        },
      };
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowKeysArray1 = keys;
            this.selectedRow1 = record;
            // window.open(record.alypath,"_blank").location
            console.log("this.selectedRow1:", this.selectedRow1);
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
        },
      };
    },
    attClick(record) {
      getRuleEntry(record.guid_).then(res => {
        if (res.code) {
          this.attData = res.data;
          // console.log('res.data:',res.data)
        } else {
          this.$message.error(res.message);
        }
      });
    },
    alypath() {
      // console.log('this.selectedRow1:',this.selectedRow1)
      window.open(this.selectedRow1.alypath, "_blank").location;
    },
    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      let dom_ = e.target.parentNode;
      // console.log(dom_.getAttribute('class'))
      if (dom_.getAttribute("class").indexOf("rowBackgroundColor") != -1 && dom_.tagName == "TR") {
        this.menuVisible = true;
        console.log("e.clientY", e.clientY);
        this.menuStyle.top = e.clientY - 500 + "px";
        this.menuStyle.left = e.clientX - 400 + "px";
        document.body.addEventListener("click", this.bodyClick);
      }
    },
    isRedRow(record) {
      if (record.guid_ == this.selectedRowKeysArray[0]) {
        return "rowBackgroundColor";
      }
    },
    isRedRow1(record) {
      if (record.guid_ == this.selectedRowKeysArray1[0]) {
        return "rowBackgroundColor";
      }
    },
  },
};
</script>
<style scoped lang="less">
.ant-modal-body {
  padding: 0 !important;
}
/deep/.ant-checkbox + span {
  padding-right: 0;
}
.table {
  /deep/.ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
}
// /deep/.ant-table-empty .ant-table-body{
//   overflow-x: hidden!important;
//   overflow-y: hidden!important;
// }
.ant-modal-body {
  padding: 0 !important;
}
.table {
  overflow: auto;
  width: 930px;
  height: 444px;
  display: flex;
  /deep/ .leftBox {
    width: 50%;
    .top {
      .ant-spin-nested-loading {
        height: 208px;
      }
      .ant-table-placeholder {
        border: 0;
      }
      margin-bottom: 10px;
      overflow: scroll;
      height: 230px;
      border: 1px solid #f0f0f0;
      border-radius: 0 0 4px 4px;
    }
    .bto {
      position: relative;
      .ant-spin-nested-loading {
        height: 188px;
      }
      .ant-table-placeholder {
        border: 0;
      }
      height: 190px;
      border: 1px solid #f0f0f0;
      border-left: 1px solid #f0f0f0;
      .tabRightClikBox {
        border: 2px solid rgb(238, 238, 238) !important;
        li {
          height: 30px;
          line-height: 30px;
          margin-top: 0;
          margin-bottom: 0;
          border-bottom: 1px solid rgb(225, 223, 223);
          background-color: white !important;
          color: #000000;
        }
        .ant-menu-item:not(:last-child) {
          margin-bottom: 4px;
        }
      }
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }
      }
    }
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 4px 4px;
        }
      }
      .ant-table-row:hover {
        td {
          background: #dfdcdc;
        }
      }
    }
  }
  /deep/ .rightBox {
    width: 50%;
    margin-left: 10px;
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-spin-container {
      display: flex;
      .ant-empty {
        margin: 10px auto;
      }
    }
  }
}
</style>
