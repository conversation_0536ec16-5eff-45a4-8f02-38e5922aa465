<template>
  <div style="overflow: hidden;display: flex;position: absolute;left:50%;top:50%;transform: translate(-50%, -50%);">
    <a-spin :tip="spinning ? '授权中...':''" :spinning="spinning" >    
    </a-spin>
    <span v-if="success" >
      <a-icon type="check-circle" style="margin-right:10px;color:green;font-size:22px"></a-icon>
      <div style="font-size:18px;font-weight:500;line-height: 22px;color:green;display: inline-block;">授权成功</div>
    </span>
    <span v-if="error" >
      <a-icon type="close-circle" style="margin-right:10px;color:red;font-size:22px"></a-icon>
      <div style="font-size:18px;font-weight:500;line-height: 22px;color:red;display: inline-block;">授权失败</div>
    </span>
  </div> 
</template>
<script>
import Cookie from 'js-cookie';
import { setopenid } from "@/services/user";
export default {
  name: 'Loading',
  data() {
    return {
      code: '',
      spinning:true,
      success:false,
      error:false,
    };
  },
  mounted() {
    let data = this.$route.query
    if(data.code){
      this.spinning = true
      console.log('微信登录',data.code)
      setopenid(data.code).then(res=>{
        if(res.code){         
          this.success = true
        }else{
          this.error = true
          console.log('授权失败',res)
        }
      }).finally(()=>{
        this.spinning = false
      })
      
    }
  }
}
  
</script>
<style scoped lang="less">
/deep/.ant-spin{
  font-size:18px;
}

</style>