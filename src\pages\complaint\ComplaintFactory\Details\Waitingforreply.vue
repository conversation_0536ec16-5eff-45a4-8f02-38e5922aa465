<!--投诉工厂-待回复详情-->
<template>
    <div class="Waitingforreply">
        <a-collapse :activeKey="'1'" @change="CollapseList">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>投诉详情</div>
                        <div>{{ text }}</div>
                    </div>
                </template>
            <div>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'客户型号'">
                            <a-input disabled ></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'客户联系人'">
                            <a-input disabled v-model="complaintdata.factoryContactName"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'联系电话'">
                            <a-input disabled v-model="complaintdata.factoryContactNumber"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'投诉日期'">
                            <a-input disabled v-model="complaintdata.complaintDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'生产型号'">
                            <a-input disabled v-model="complaintdata.proOrderNo"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货日期'">
                            <a-input disabled v-model="complaintdata.deliveryDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货总数'" >
                            <a-input disabled v-model="complaintdata.deliveryNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'不良总数'">
                            <a-input disabled v-model="complaintdata.badNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'周期'" >
                            <a-input disabled v-model="complaintdata.dateCode"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'问题描述'" >
                            <a-textarea  v-model="complaintdata.problemDescription" disabled :auto-size="{ minRows: 4, maxRows:6 }"></a-textarea >
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <span style="color: #428bca;cursor: pointer;" @click="filedown(complaintdata.verdictFilePath)" 
                            v-if="complaintdata.verdictFilePath"> 
                                <a-icon type="link" style="padding: 0 5px;" ></a-icon>{{complaintdata.verdictFileName}}
                            </span>
                            <span v-else>暂无附件</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'不良批次要求采取的措施'" >
                            <a-radio-group   disabled v-model="complaintdata.takeSteps" name="radioGroup">
                                <a-radio :value="1">退货返工</a-radio>
                                <a-radio :value="2">报废补货</a-radio>
                                <a-radio :value="3">报废扣款</a-radio>
                                <a-radio :value="4">特采</a-radio>
                                <a-radio :value="5">其他</a-radio>
                            </a-radio-group>
                            <div v-show="complaintdata.takeSteps=='2' || complaintdata.takeSteps=='1'">
                                <div v-show="complaintdata.takeSteps=='2'">补货板收货地址及收货人信息：（地址，姓名隔开）</div>
                                <div v-show="complaintdata.takeSteps=='1'">返工 OK 板收货地址及收货人信息：（地址，姓名隔开）</div>
                                <div style="display: flex;justify-content: space-around;">
                                    <a-input placeholder="姓名" v-model="complaintdata.receiveContactName" style="width: 200px;" disabled >
                                        <template #prefix>
                                            <a-icon type="user" style="color:red" />
                                        </template>
                                    </a-input>
                                    <a-input placeholder="电话" v-model="complaintdata.receiveContactNumber" style="width: 280px;" disabled>
                                        <template #prefix>
                                            <a-icon type="phone" style="color:red" />
                                        </template>
                                    </a-input>
                                    <a-input placeholder="地址" v-model="complaintdata.receiveAddress" style="width: 422px;" disabled>
                                        <template #prefix>
                                            <a-icon type="home" style="color:red" />
                                        </template>
                                    </a-input>
                                </div>    
                            </div> 
                            <a-input style="width: 484px;"  disabled v-model="complaintdata.takeStepsName" v-show="complaintdata.takeSteps=='5'"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-show="complaintdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废品形态'" >
                            <a-radio-group  disabled v-model="complaintdata.scrapType" name="radioGroup" >
                                <a-radio value="PCB">PCB</a-radio>
                                <a-radio value="PCBA">PCBA</a-radio>
                                <a-radio value="整机">整机</a-radio>
                                <a-radio value="其它">其它</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'报废品数量描述'" >
                            <a-input disabled v-model="complaintdata.scrapNumDetail"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>   
                <a-row v-show="complaintdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'不良板成本(元)'" >
                            <a-input disabled v-model="complaintdata.scrapCost"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'扣款总金额(元)'" >
                            <a-input disabled v-model="complaintdata.deductMoney"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row> 
                <a-row v-show="complaintdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废总成本(元)'" >
                            <a-input v-model="complaintdata.scrapCost" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row> 
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'是否需要8D报告'" >
                            <a-radio-group  v-model="complaintdata.need8DReport" disabled name="radioGroup" >
                                <a-radio :value="false">否</a-radio>
                                <a-radio :value="true">是</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>    
                    <a-col :span="16" >
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'要求改善报告回复客户时间'" >
                            <a-radio-group  v-model="complaintdata.needReplytime"  disabled name="radioGroup">
                                <a-radio :value="3">3天</a-radio>
                                <a-radio :value="2">2天</a-radio>
                                <a-radio :value="1">1天</a-radio>                              
                                <a-radio :value="0">其他</a-radio>
                            </a-radio-group>
                            <a-input v-model="complaintdata.needReplytimeName" style="width: 100px;"></a-input>
                        </a-form-model-item>
                    </a-col>             
                </a-row>
                <a-row style="margin-top: 15px;">
                    <a-col :span="16">
                            <a-button type="normal" style="margin-left: 185px;" @click="back">返回</a-button>
                    </a-col>
                </a-row>
            </div>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>
<script>
import {factorybyid} from "@/services/complaint/QualitativeComplaint.js";
export default {
    name:'Waitingforreply',
    props:[],
    data() {
        return {
            text:'收起',            
            complaintdata:{},
        }
    },
    mounted(){
        factorybyid(this.$route.query.id).then(res=>{
            if(res.code){
                this.complaintdata=res.data
            }
        })
    },
    methods:{
        filedown(path){         
            if(path){
                window.location.href = path
            }
        },
        back(){
            this.$router.push({path:'ComplaintFactory',query:{} })
        },
        CollapseList(val){
            if(val.length){
                this.text = '收起'
            }else{
                this.text = '展开'
            }
        },
    },
    created(){

    }
}
</script>
<style lang="less" scoped>
.Waitingforreply{
    padding: 10px;
    overflow: auto;
    height: 821px;
    background-color: white;
    border: 1px solid #e8e8e8;
    &::-webkit-scrollbar {
        width: 6px; 
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        background: #ff9900;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #ffffff;
      }
    /deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
    right: 16px !important;
    left: auto;
    }
    /deep/.ant-radio-disabled .ant-radio-inner::after {
        background-color: #ff9900;
    }
    /deep/.ant-radio-disabled .ant-radio-inner {
        background-color: #ffffff;
        border-color: #d9d9d9  !important;
        cursor: not-allowed;
    }
    /deep/.ant-radio-disabled + span {
        color: black;
        cursor: not-allowed;
    }
    /deep/.ant-radio-checked .ant-radio-inner{
        border-color: #ff9900  !important;
    }
    /deep/.ant-divider-horizontal {
        display: block;
        clear: both;
        width: 100%;
        min-width: 100%;
        height: 1px;
        margin: 13px 0;
    }
    /deep/.ant-form-item{
        margin-bottom: 0;
    }
}

</style>