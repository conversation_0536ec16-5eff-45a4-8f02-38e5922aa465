<template>
  <div>
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle" v-if="tabtype != '1'">打印</a-button>
    <div id="pre_qualified_information">
      <a-form-model layout="inline" id="formDataElem1" class="bborder contentInfo">
        <div
          class="div1"
          style="
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            justify-content: start;
            max-height: 512px;
            align-content: flex-start;
          "
        >
          <a-form-model-item
            :class="showData.plateTypeStr ? 'divitem' : ''"
            label="文件名称"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pcbFileName"
            class="pcbFileName"
          >
            <div>
              <a v-if="showData.pcbFileName" :title="showData.pcbFileName" class="tmp">{{ showData.pcbFileName }}</a>
            </div>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.custNo ? 'divitem' : ''"
            label="客户代码"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.custNo"
            class="pcbFileName"
          >
            <div>
              <span v-if="showData.custNo" :title="showData.custNo" class="tmp">{{ showData.custNo }}</span>
            </div>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.plateTypeStr ? 'divitem' : ''"
            label="订单类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.plateTypeStr"
          >
            <span>{{ showData.plateTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fileFormat ? 'divitem' : ''"
            label="文件格式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fileFormat"
          >
            <span>{{ showData.fileFormat }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardThickness ? 'divitem' : ''"
            label="成品板厚"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardThickness"
          >
            <span>{{ showData.boardThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardBrandStr ? 'divitem' : ''"
            label="板材型号"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardBrandStr"
          >
            <span>{{ showData.boardBrandStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardThicknessTol ? 'divitem' : ''"
            label="板厚公差"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardThicknessTol"
          >
            <span>{{ showData.boardThicknessTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardLayers || showData.boardLayers == '0' ? 'divitem' : ''"
            label="层数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardLayers || showData.boardLayers == '0'"
          >
            <span>{{ showData.boardLayers }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="
              showData.cuThickness || (Number(showData.boardLayers) > 2 && showData.innerCopperThickness) || showData.copperThickness ? 'divitem' : ''
            "
            label="成品铜厚(oz)"
            :label-col="{ span: 7 }"
            class="ThicknessTreatmentoz"
            :wrapper-col="{ span: 17 }"
            v-show="showData.cuThickness || (Number(showData.boardLayers) > 2 && showData.innerCopperThickness) || showData.copperThickness"
          >
            <span style="word-break: break-all">
              <span v-if="showData.innerCopperThickness && Number(showData.boardLayers) > 2">[内]{{ showData.innerCopperThickness }} </span>
              <span v-if="showData.copperThickness">[外] {{ showData.copperThickness }}</span>
              <span v-if="showData.cuThickness && !showData.isCopperThickConversion">[铜厚]{{ showData.cuThickness }}</span>
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="
              showData.cuThickness || (Number(showData.boardLayers) > 2 && showData.innerCopperThickness) || showData.copperThickness ? 'divitem' : ''
            "
            label="成品铜厚(um)"
            class="ThicknessTreatmentum"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-if="showData.isCopperThickConversion"
            v-show="showData.cuThickness || (Number(showData.boardLayers) > 2 && showData.innerCopperThickness2) || showData.copperThickness2"
          >
            <span style="word-break: break-all">
              <span v-if="showData.innerCopperThickness2 && Number(showData.boardLayers) > 2">[内]{{ showData.innerCopperThickness2 }} </span>
              <span v-if="showData.copperThickness2">[外] {{ showData.copperThickness2 }}</span>
              <span v-if="showData.cuThickness">[铜厚]{{ showData.cuThickness }}</span>
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.surfaceFinishStr ? 'divitem' : ''"
            class="SurfaceTreatment"
            label="表面处理"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-if="showData.surfaceFinishJsonDto"
            v-show="showData.surfaceFinishStr"
          >
            <span style="width: 70%">
              <span style="width: 30%">
                {{ showData.surfaceFinishStr }}
              </span>
              <span v-if="showData.surfaceFinish == 'hardgoldplating'" style="margin: 0 0.5%; width: 36%"
                >水金:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                  showData.surfaceFinish == 'immersiongold' ||
                  showData.surfaceFinish == 'immersiongoldandosp' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                  showData.surfaceFinish == 'wqpxandjbdj' ||
                  showData.surfaceFinish == 'cjandjbdj' ||
                  showData.surfaceFinish == 'zbdjandosp' ||
                  showData.surfaceFinish == 'tinprecipitation' ||
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'chemicalsilver' ||
                  showData.surfaceFinish == 'fullgoldplating' ||
                  showData.surfaceFinish == 'hardgoldplating' ||
                  showData.surfaceFinish == 'waterplatedgold' ||
                  showData.surfaceFinish == 'tinplating' ||
                  showData.surfaceFinish == 'goldplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'goldplating' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating' ||
                  showData.surfaceFinish == 'goldnickelplatingandchemical' ||
                  showData.surfaceFinish == 'goldnickelplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'fullgilding' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'djszandyqpx' ||
                  showData.surfaceFinish == 'djszandcj' ||
                  showData.surfaceFinish == 'zbcjandcjsz' ||
                  showData.surfaceFinish == 'hjandty' ||
                  showData.surfaceFinish == 'cyandty' ||
                  showData.surfaceFinish == 'djszandwqpx' ||
                  showData.surfaceFinish == 'dnanddjsz' ||
                  showData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                  showData.surfaceFinish == 'electrogold' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'immersionnickelgold' ||
                  showData.surfaceFinish == 'thickgoldplating' ||
                  showData.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                  showData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                  showData.surfaceFinish == 'nickelgoldplating' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  showData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                  showData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                  showData.surfaceFinish == 'Antioxidant+golddeposition' ||
                  showData.surfaceFinish == 'Tinspraying+goldplating' ||
                  showData.surfaceFinish == 'Leadfreetinspraying+goldplating' ||
                  showData.surfaceFinish == 'immersiongoldanddj'
                "
                style="margin: 0 0.5%; width: 26%"
              >
                面积:{{ showData.surfaceFinishJsonDto.platedArea }}%</span
              >
              <span v-if="showData.surfaceFinish == 'djszandcj'" style="margin: 0 0.5%; width: 36%"
                >化金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'wholegoldplating'" style="margin: 0 0.5%; width: 36%"
                >金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiongold' ||
                  showData.surfaceFinish == 'goldplatingandosp' ||
                  showData.surfaceFinish == 'wholegoldplating'
                "
                style="margin: 0 0.5%; width: 26%"
              >
                镀面积:{{ showData.surfaceFinishJsonDto.platedArea }}%</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiongold' ||
                  showData.surfaceFinish == 'goldplatingandosp' ||
                  showData.surfaceFinish == 'djszandyqpx' ||
                  showData.surfaceFinish == 'djszandwqpx' ||
                  showData.surfaceFinish == 'dnanddjsz'
                "
                style="margin: 0 0.5%; width: 36%"
                >镀金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'djszandcj'" style="margin: 0 0.5%; width: 36%"
                >镀金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'goldplatingandimmersiongold'" style="margin: 0 0.5%; width: 26%">
                面积:{{ showData.surfaceFinishJsonDto.platedArea2 }}%</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'immersiongold' ||
                  showData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'nickelgoldplating' ||
                  showData.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                  showData.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                  showData.surfaceFinish == 'immersionnickelgold' ||
                  showData.surfaceFinish == 'thickgoldplating' ||
                  showData.surfaceFinish == 'goldplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'immersiongoldandosp' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating' ||
                  showData.surfaceFinish == 'goldnickelplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandchemical' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'fullgilding' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'electrogold' ||
                  showData.surfaceFinish == 'nbjandcj' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'zbdjandjbelectrogold' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                  showData.surfaceFinish == 'wqpxandjbdj' ||
                  showData.surfaceFinish == 'cjandjbdj' ||
                  showData.surfaceFinish == 'zbdjandosp' ||
                  showData.surfaceFinish == 'haslwithleadandGoldfinger' ||
                  showData.surfaceFinish == 'nbjandgoldplating' ||
                  showData.surfaceFinish == 'immersiongoldanddj' ||
                  (showData.surfaceFinish == 'haslwithfreeandGoldfinger' && this.$route.query.factory != 38) ||
                  showData.surfaceFinish == 'ospandfinger' ||
                  showData.surfaceFinish == 'immersiongoldandty' ||
                  showData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                  showData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                  showData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                  showData.surfaceFinish == 'Antioxidant+golddeposition' ||
                  showData.surfaceFinish == 'Tinspraying+goldplating' ||
                  showData.surfaceFinish == 'Leadfreetinspraying+goldplating'
                "
                style="margin: 0 0.5%; width: 18%"
                >金:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                style="margin: 0 0.5%; width: 20%"
                >薄金:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'nbjandcj' ||
                  showData.surfaceFinish == 'nbjandgoldplating'
                "
                style="margin: 0 0.5%; width: 23%"
                >钯厚:{{ showData.surfaceFinishJsonDto.paThickness }}U"</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'haslwithlead' ||
                  showData.surfaceFinish == 'tinnedcerium' ||
                  showData.surfaceFinish == 'haslwithfree' ||
                  showData.surfaceFinish == 'spraytin' ||
                  showData.surfaceFinish == 'tinplating' ||
                  showData.surfaceFinish == 'haslwithleadandGoldfinger' ||
                  showData.surfaceFinish == 'wqpxandty' ||
                  showData.surfaceFinish == 'yqpxandty' ||
                  showData.surfaceFinish == 'haslwithfreeandGoldfinger'
                "
                style="margin: 0 0.5%; width: 40%"
                >锡厚:{{ showData.surfaceFinishJsonDto.newTinThickness }}um</span
              >
              <span v-if="showData.surfaceFinish == 'tinprecipitation'" style="margin: 0 0.5%; width: 40%"
                >锡厚:{{ showData.surfaceFinishJsonDto.newTinThickness2 }}um</span
              >
              <span v-if="showData.surfaceFinish == 'tinprecipitation'" style="margin: 0 0.5%; width: 40%"
                >锡厚:{{ showData.surfaceFinishJsonDto.newTinThickness2 }}um</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'eletrolyticnickel' ||
                  showData.surfaceFinish == 'hardgoldplating' ||
                  showData.surfaceFinish == 'waterplatedgold' ||
                  showData.surfaceFinish == 'immersiongold' ||
                  showData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                  showData.surfaceFinish == 'immersiongoldandosp' ||
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'nickelgoldplating' ||
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'immersionnickelgold' ||
                  showData.surfaceFinish == 'thickgoldplating' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'nbjandcj' ||
                  showData.surfaceFinish == 'nbjandgoldplating' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'wholegoldplating' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating' ||
                  showData.surfaceFinish == 'goldnickelplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandchemical' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'fullgilding' ||
                  showData.surfaceFinish == 'electrogold' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                  showData.surfaceFinish == 'wqpxandjbdj' ||
                  showData.surfaceFinish == 'cjandjbdj' ||
                  showData.surfaceFinish == 'zbdjandosp' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                  showData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                  showData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                  showData.surfaceFinish == 'Antioxidant+golddeposition' ||
                  showData.surfaceFinish == 'Tinspraying+goldplating' ||
                  showData.surfaceFinish == 'Leadfreetinspraying+goldplating'
                "
                style="margin: 0 0.5%; width: 30%"
              >
                镍:{{ showData.surfaceFinishJsonDto.cjNickelThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'osp' ||
                  showData.surfaceFinish == 'ospandfinger' ||
                  showData.surfaceFinish == 'ospandty' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp'
                "
                style="margin: 0 0.5%; width: 40%"
                >膜厚:{{ showData.surfaceFinishJsonDto.filmThickness }}um</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'chemicalsilver' ||
                  showData.surfaceFinish == 'cyandty' ||
                  showData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                  showData.surfaceFinish == 'silverplating' ||
                  showData.surfaceFinish == 'outsourcingsilverplating' ||
                  showData.surfaceFinish == 'electroplatingsilverelectroplatinggold'
                "
                style="margin: 0 0.5%; width: 40%"
                >银厚:{{ showData.surfaceFinishJsonDto.newSilverThickness }}um</span
              >
              <span v-if="showData.surfaceFinish == 'goldplatingandimmersiontin'" style="margin: 0 0.5%; width: 31%"
                >镀金面积:{{ showData.surfaceFinishJsonDto.platedArea }}</span
              >
              <span v-if="showData.surfaceFinish == 'goldplatingandimmersiontin'" style="margin: 0 0.5%; width: 18%"
                >镀金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiontin' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating'
                "
                style="margin: 0 0 0 0.5%; width: 30%"
                >面积:{{ showData.surfaceFinishJsonDto.platedArea2 }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'fullgoldplating' ||
                  showData.surfaceFinish == 'goldplating' ||
                  showData.surfaceFinish == 'waterplatedgold' ||
                  showData.surfaceFinish == 'zbcjandcjsz' ||
                  showData.surfaceFinish == 'yqpxandcjsz' ||
                  showData.surfaceFinish == 'wqpxandcjsz' ||
                  showData.surfaceFinish == 'ospandcjsz' ||
                  showData.surfaceFinish == 'goldplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'hjandty'
                "
                style="margin: 0 0.5%; width: 36%"
              >
                金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >

              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiongold' ||
                  showData.surfaceFinish == 'nbjandgoldplating' ||
                  showData.surfaceFinish == 'immersiongoldanddj'
                "
                style="margin: 0 0.5%; width: 36%"
                >金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                style="margin: 0 0.5%; width: 36%"
                >厚金:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating'
                "
                style="margin: 0 0.5%; width: 36%"
                >金:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'hardgoldplating'" style="margin: 0 0.5%; width: 36%"
                >硬金:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'hardgoldplating' ||
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold'
                "
                style="margin: 0 0.5%; width: 26%"
              >
                面积:{{ showData.surfaceFinishJsonDto.platedArea2 }}%</span
              >
              <span
                v-if="showData.surfaceFinish == 'hardgoldplating' || showData.surfaceFinish == 'immersiongoldandjbelectrogold'"
                style="margin: 0 0.5%; width: 26%"
              >
                镍:{{ showData.surfaceFinishJsonDto.cjNickelThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderColorStr ? 'divitem' : ''"
            label="阻焊颜色"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderColorStr || showData.solderColorBottomStr"
          >
            <span>
              <span v-if="showData.solderColorStr" style="margin-right: 0.5%">[顶层]</span>{{ showData.solderColorStr }}
              <span v-if="showData.solderColorBottomStr" style="margin: 0 0.5%">[底层]</span>{{ showData.solderColorBottomStr }}</span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderResistInkStr ? 'divitem' : ''"
            label="阻焊油墨"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderResistInkStr"
          >
            <span :title="showData.solderResistInkStr">{{ showData.solderResistInkStr }} </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderInkThickness ? 'divitem' : ''"
            label="阻焊油墨厚:"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderInkThickness"
          >
            <span>{{ showData.solderInkThickness }}um</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderCoverStr ? 'divitem' : ''"
            label="过孔处理"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderCoverStr"
          >
            <span>{{ showData.solderCoverStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isInkNotHalogen ? 'divitem' : ''"
            label="无卤油墨"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isInkNotHalogen"
          >
            <span>{{ showData.isInkNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fontColorStr || showData.fontColorBottomStr ? 'divitem' : ''"
            label="字符颜色"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fontColorStr || showData.fontColorBottomStr"
          >
            <span
              ><span v-if="showData.fontColorStr" style="margin-right: 0.5%">[顶层]</span>{{ showData.fontColorStr }}
              <span v-if="showData.fontColorBottomStr" style="margin: 0 0.5%">[底层]</span>{{ showData.fontColorBottomStr }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.characterResistInkStr ? 'divitem' : ''"
            label="字符油墨"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.characterResistInkStr"
          >
            <span :title="showData.characterResistInkStr">{{ showData.characterResistInkStr }} </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.characterNotHalogen ? 'divitem' : ''"
            label="字符无卤"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.characterNotHalogen"
          >
            <span>{{ showData.characterNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.pinBanType ? 'divitem' : ''"
            label="拼版方式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pinBanType"
          >
            <span>{{ showData.pinBanType }}</span>
            <span v-if="showData.pbt" style="margin-left: 5px">
              <img :src="showData.pbt" style="width: 20px; height: 20px; border: 1px solid red" v-viewer />
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.su ? 'divitem' : ''"
            label="SU数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.su"
          >
            <span>{{ showData.su }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardHeight ? 'divitem' : ''"
            label="单元长度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardHeight"
          >
            <span>{{ showData.boardHeight }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardWidth ? 'divitem' : ''"
            label="单元宽度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardWidth"
          >
            <span>{{ showData.boardWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.setBoardHeight ? 'divitem' : ''"
            label="成品长度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.setBoardHeight"
          >
            <span>{{ showData.setBoardHeight }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.setBoardWidth ? 'divitem' : ''"
            label="成品宽度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.setBoardWidth"
          >
            <span>{{ showData.setBoardWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.formingTypeStr ? 'divitem' : ''"
            label="成型方式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.formingTypeStr"
          >
            <span>{{ showData.formingTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.formingTol ? 'divitem' : ''"
            label="成型公差"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.formingTol"
          >
            <span>{{ showData.formingTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.packagRequireStr ? 'divitem' : ''"
            label="包装方式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.packagRequireStr"
          >
            <span>{{ showData.packagRequireStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardTypeStr ? 'divitem' : ''"
            label="拼版单位"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardTypeStr"
          >
            <span>{{ showData.boardTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.sheetTraderStr ? 'divitem' : ''"
            label="板材厂商"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.sheetTraderStr"
          >
            <span>{{ showData.sheetTraderStr }}</span>
          </a-form-model-item>

          <a-form-model-item
            :class="showData.isCustomerBoard ? 'divitem' : ''"
            label="客供板材"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCustomerBoard"
          >
            <span>{{ showData.isCustomerBoard ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fR4TypeStr ? 'divitem' : ''"
            label="板材类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fR4TypeStr"
          >
            <span>{{ showData.fR4TypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isNotHalogen ? 'divitem' : ''"
            label="无卤板材"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isNotHalogen"
          >
            <span>{{ showData.isNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.cafResistance ? 'divitem' : ''"
            label="耐CAF"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.cafResistance"
          >
            <span>{{ showData.cafResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fR4TgStr ? 'divitem' : ''"
            label="板材TG"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fR4TgStr"
          >
            <span>{{ showData.fR4TgStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.plateCtiStr ? 'divitem' : ''"
            label="板材CTI"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.plateCtiStr"
          >
            <span>{{ showData.plateCtiStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.sheetSize ? 'divitem' : ''"
            label="大料尺寸"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.sheetSize"
          >
            <span>{{ showData.sheetSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.sheetUtilization ? 'divitem' : ''"
            label="利用率(%)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.sheetUtilization"
          >
            <span>{{ showData.sheetUtilization }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.notAcceptPatching ? 'divitem' : ''"
            label="不接受补线"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.notAcceptPatching"
          >
            <span>{{ showData.notAcceptPatching ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.acceptCrossed ? 'divitem' : ''"
            label="不接受叉板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.acceptCrossed"
          >
            <span>{{ showData.acceptCrossed ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ipcLevelStr ? 'divitem' : ''"
            label="验收标准"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ipcLevelStr"
          >
            <span>{{ showData.ipcLevelStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.warpageStr ? 'divitem' : ''"
            label="板翘"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.warpageStr"
          >
            <span>{{ showData.warpageStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.markPositionStr ? 'divitem' : ''"
            label="标记位置"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.markPositionStr"
          >
            <span>{{ showData.markPositionStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ulTypeStr ? 'divitem' : ''"
            label="UL类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ulTypeStr"
          >
            <span>{{ showData.ulTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.markTypeStr ? 'divitem' : ''"
            label="标记类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.markTypeStr"
          >
            <span>{{ showData.markTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.markFaceStr ? 'divitem' : ''"
            label="标记面向"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.markFaceStr"
          >
            <span>{{ showData.markFaceStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.periodicFormatStr ? 'divitem' : ''"
            label="周期格式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.periodicFormatStr"
          >
            <span>{{ showData.periodicFormatStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.productUsageStr ? 'divitem' : ''"
            label="产品用途"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.productUsageStr"
          >
            <span>{{ showData.productUsageStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.custAssignment ? 'divitem' : ''"
            label="客户指定"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.custAssignment"
          >
            <span>{{ showData.custAssignment ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.counterPressure ? 'divitem' : ''"
            label="对压"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.counterPressure"
          >
            <span>{{ showData.counterPressure ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.lineWidth ? 'divitem' : ''"
            label="外层最小线宽mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.lineWidth"
          >
            <span>{{ showData.lineWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.lineSpacing ? 'divitem' : ''"
            label="外层最小线距mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.lineSpacing"
          >
            <span>{{ showData.lineSpacing }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.icSize ? 'divitem' : ''"
            label="最小IC尺寸mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.icSize"
          >
            <span>{{ showData.icSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.bgaSize ? 'divitem' : ''"
            label="最小BGA(mil)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.bgaSize"
          >
            <span>{{ showData.bgaSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.vias ? 'divitem' : ''"
            label="通孔最小孔mm"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.vias"
          >
            <span>{{ showData.vias }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.totalHoleNum ? 'divitem' : ''"
            label="单元通孔孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.totalHoleNum"
          >
            <span>{{ showData.totalHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.slotHoleNum ? 'divitem' : ''"
            label="单元槽(散孔)数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.slotHoleNum"
          >
            <span>{{ showData.slotHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testPointSize ? 'divitem' : ''"
            label="最小测试点mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testPointSize"
          >
            <span>{{ showData.testPointSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testPointNum ? 'divitem' : ''"
            label="单元测试点数量"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testPointNum"
          >
            <span>{{ showData.testPointNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.holetoline ? 'divitem' : ''"
            label="内层孔到线mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.holetoline && showMore"
          >
            <span>{{ showData.holetoline }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.vCutKnifeNum ? 'divitem' : ''"
            label="V-CUT刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.vCutKnifeNum"
          >
            <span>{{ showData.vCutKnifeNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.jumpCutXt ? 'divitem' : ''"
            label="跳V刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.jumpCutXt"
          >
            <span>{{ showData.jumpCutXt }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.impGroupNum ? 'divitem' : ''"
            label="阻抗组数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.impGroupNum"
          >
            <span
              ><span v-if="showData.impGroupNum"> {{ showData.impGroupNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isImpUnTol ? 'divitem' : ''"
            label="阻抗非常规公差"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isImpUnTol"
          >
            <span>{{ showData.isImpUnTol ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.inLineWidth ? 'divitem' : ''"
            label="内层最小线宽mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.inLineWidth && showMore"
          >
            <span>{{ showData.inLineWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.inLineSpacing ? 'divitem' : ''"
            label="内层最小线距mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.inLineSpacing && showMore"
          >
            <span>{{ showData.inLineSpacing }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isChangeLayerPres ? 'divitem' : ''"
            label="层压不可更改"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isChangeLayerPres"
          >
            <span>{{ showData.isChangeLayerPres ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.gbNum ? 'divitem' : ''"
            label="光板数1"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.gbNum"
          >
            <span>{{ showData.gbNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ppNum ? 'divitem' : ''"
            label="PP张数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ppNum"
          >
            <span>{{ showData.ppNum }}</span>
          </a-form-model-item>

          <a-form-model-item
            :class="showData.specialPpNum ? 'divitem' : ''"
            label="特殊PP张数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.specialPpNum"
          >
            <span>{{ showData.specialPpNum }}</span>
          </a-form-model-item>

          <a-form-model-item
            :class="showData.pressTimesStr || showData.drillTimesStr ? 'divitem' : ''"
            label="压合/钻孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pressTimesStr || showData.drillTimesStr"
          >
            <span v-if="showData.drillTimesStr && showData.pressTimesStr">{{ showData.pressTimesStr }}/{{ showData.drillTimesStr }}</span>
            <span v-else-if="showData.drillTimesStr">{{ showData.drillTimesStr }}</span>
            <span v-else>{{ showData.pressTimesStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindBuryOrderStr ? 'divitem' : ''"
            label="盲埋阶数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindBuryOrderStr"
          >
            <span>{{ showData.blindBuryOrderStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindHoleNum ? 'divitem' : ''"
            label="机械盲孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindHoleNum"
          >
            <span>{{ showData.blindHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindHoleMin ? 'divitem' : ''"
            label="最小盲埋孔mm"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindHoleMin"
          >
            <span>{{ showData.blindHoleMin }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindHoleThickMedium ? 'divitem' : ''"
            label="盲孔厚介质"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindHoleThickMedium"
          >
            <span>{{ showData.blindHoleThickMedium ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserOrderStr ? 'divitem' : ''"
            label="激光阶数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserOrderStr"
          >
            <span>{{ showData.laserOrderStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserNumStr || showData.laserTypeStr ? 'divitem' : ''"
            label="激光次数/类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserNumStr || showData.laserTypeStr"
          >
            <span>{{ showData.laserNumStr }}/{{ showData.laserTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserMinHole ? 'divitem' : ''"
            label="激光最小孔mm"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserMinHole"
          >
            <span>{{ showData.laserMinHole }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserHoleNum ? 'divitem' : ''"
            label="激光孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserHoleNum"
          >
            <span>{{ showData.laserHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isDiscHole ? 'divitem' : ''"
            label="盘中孔"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isDiscHole"
          >
            <span>{{ showData.isDiscHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.halfSthNum ? 'divitem' : ''"
            label="半边孔边数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.halfSthNum"
          >
            <span v-if="showData.halfSthNum">{{ showData.halfSthNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.profileHoleNum ? 'divitem' : ''"
            label="异形孔孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.profileHoleNum"
          >
            <span v-if="showData.profileHoleNum">{{ showData.profileHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.intPlateEdge ? 'divitem' : ''"
            label="板边包金边数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.intPlateEdge"
          >
            <span v-if="showData.intPlateEdge">{{ showData.intPlateEdge }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isMetalSlot ? 'divitem' : ''"
            label="金属铣槽"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isMetalSlot"
          >
            <span>{{ showData.isMetalSlot ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.stepHoleNum ? 'divitem' : ''"
            label="沉孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.stepHoleNum"
          >
            <span v-if="showData.stepHoleNum">{{ showData.stepHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.serialNumber ? 'divitem' : ''"
            label="序列号个数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.serialNumber"
          >
            <span
              ><span v-if="showData.serialNumber">{{ showData.serialNumber }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isBlueGum ? 'divitem' : ''"
            label="蓝胶"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isBlueGum"
          >
            <span v-if="!showData.blueGumPercentage">{{ showData.isBlueGum ? "是" : "" }}</span>
            <span v-if="showData.blueGumPercentage">{{ showData.blueGumPercentage }}%</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldenFingerAreaRe ? 'divitem' : ''"
            label="金手指面积(%)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldenFingerAreaRe"
          >
            <span>{{ showData.goldenFingerAreaRe }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldenFingerArea ? 'divitem' : ''"
            label="金手指条数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldenFingerArea"
          >
            <span>{{ showData.goldenFingerArea }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerThickness ? 'divitem' : ''"
            label='金手指金厚U"'
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerThickness"
          >
            <span>{{ showData.goldfingerThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerNieThickness ? 'divitem' : ''"
            label='金手指镍厚U"'
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerNieThickness"
          >
            <span>{{ showData.goldfingerNieThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerSize ? 'divitem' : ''"
            label="金手指尺寸"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerSize"
          >
            <span>{{ showData.goldfingerSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.specialMaterialSpecStr ? 'divitem' : ''"
            label="特殊料规格"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.specialMaterialSpecStr"
          >
            <span>{{ showData.specialMaterialSpecStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isCrimpHole ? 'divitem' : ''"
            label="压接孔"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCrimpHole"
          >
            <span>{{ showData.isCrimpHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.backDrillNum ? 'divitem' : ''"
            label="背钻孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.backDrillNum"
          >
            <span
              ><span v-if="showData.backDrillNum">{{ showData.backDrillNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.throughHoleNum ? 'divitem' : ''"
            label="通孔控深次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.throughHoleNum"
          >
            <span
              ><span v-if="showData.throughHoleNum">{{ showData.throughHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.depthControl ? 'divitem' : ''"
            label="控深深度(mm)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.depthControl"
          >
            <span
              ><span v-if="showData.depthControl">{{ showData.depthControl }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ctrlBlindHole ? 'divitem' : ''"
            label="盲孔控深次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ctrlBlindHole"
          >
            <span
              ><span v-if="showData.ctrlBlindHole">{{ showData.ctrlBlindHole }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.countersinkAngle ? 'divitem' : ''"
            label="沉孔角度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.countersinkAngle"
          >
            <span
              ><span v-if="showData.countersinkAngle">{{ showData.countersinkAngle }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.steppedHoleNum ? 'divitem' : ''"
            label="阶梯孔孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.steppedHoleNum"
          >
            <span
              ><span v-if="showData.steppedHoleNum">{{ showData.steppedHoleNum }}</span></span
            >
          </a-form-model-item>

          <a-form-model-item
            :class="showData.isCarbonOil ? 'divitem' : ''"
            label="碳油"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCarbonOil"
          >
            <span v-if="!showData.carbonOilPercentage">{{ showData.isCarbonOil ? "是" : "" }}</span>
            <span v-if="showData.carbonOilPercentage">{{ showData.carbonOilPercentage }}%</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.pasteRedTape ? 'divitem' : ''"
            label="高温胶(处)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pasteRedTape"
          >
            <span>{{ showData.pasteRedTape }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.innerBevelNum ? 'divitem' : ''"
            label="内斜边刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.innerBevelNum"
          >
            <span>{{ showData.innerBevelNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.conventionBevelNum ? 'divitem' : ''"
            label="常规斜边刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.conventionBevelNum"
          >
            <span>{{ showData.conventionBevelNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindSlotNum ? 'divitem' : ''"
            label="盲槽(个数)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindSlotNum"
          >
            <span>{{ showData.blindSlotNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.depthControlArea ? 'divitem' : ''"
            label="控深面积(m/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.depthControlArea"
          >
            <span>{{ showData.depthControlArea }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.buriedCopper ? 'divitem' : ''"
            label="埋铜"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.buriedCopper"
          >
            <span>{{ showData.buriedCopper ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.silverPlugHoleNum ? 'divitem' : ''"
            label="银浆塞孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.silverPlugHoleNum"
          >
            <span
              ><span v-if="showData.silverPlugHoleNum">{{ showData.silverPlugHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.cuPlugHoleNum ? 'divitem' : ''"
            label="铜浆塞孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.cuPlugHoleNum"
          >
            <span
              ><span v-if="showData.cuPlugHoleNum">{{ showData.cuPlugHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.resinPlugHoleNum ? 'divitem' : ''"
            label="树脂塞孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.resinPlugHoleNum"
          >
            <span
              ><span v-if="showData.resinPlugHoleNum">{{ showData.resinPlugHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.plateNumber ? 'divitem' : ''"
            label="板材张数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.plateNumber"
          >
            <span>{{ showData.plateNumber }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.apertureRatio ? 'divitem' : ''"
            label="板厚孔径比"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.apertureRatio"
          >
            <span>{{ showData.apertureRatio }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.routLength ? 'divitem' : ''"
            label="锣带长度(m/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.routLength"
          >
            <span>{{ showData.routLength }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.poreDensity ? 'divitem' : ''"
            label="孔密度(W/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.poreDensity"
          >
            <span>{{ showData.poreDensity }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testPointsm2 ? 'divitem' : ''"
            label="测试点数(W/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testPointsm2"
          >
            <span>{{ (showData.testPointsm2 / 1000).toFixed(2) || 0 }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isDifficultyBoard ? 'divitem' : ''"
            label="难度板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isDifficultyBoard"
          >
            <span>{{ showData.isDifficultyBoard ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.minHoleCopper ? 'divitem' : ''"
            label="最小孔铜um"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.minHoleCopper"
          >
            <span>{{ showData.minHoleCopper }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.totalLayer ? 'divitem' : ''"
            label="假层"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.totalLayer"
          >
            <span>{{ showData.totalLayer }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.pinBanNum ? 'divitem' : ''"
            label="合拼款数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pinBanNum"
          >
            <span>{{ showData.pinBanNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isFacHp ? 'divitem' : ''"
            label="工厂合拼"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isFacHp"
          >
            <span>{{ showData.isFacHp }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isCoilPlate ? 'divitem' : ''"
            label="线圈板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCoilPlate"
          >
            <span>{{ showData.isCoilPlate ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ppap ? 'divitem' : ''"
            label="PPAP"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ppap"
          >
            <span>{{ showData.ppap ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.thermalConductivity ? 'divitem' : ''"
            label="导热系数w/m.k"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.thermalConductivity"
          >
            <span>{{ showData.thermalConductivity }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isSolderMaskIpc3 ? 'divitem' : ''"
            label="阻焊三级标准"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isSolderMaskIpc3"
          >
            <span>{{ showData.isSolderMaskIpc3 ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.buriedResistance ? 'divitem' : ''"
            label="埋阻"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.buriedResistance"
          >
            <span>{{ showData.buriedResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isTransferOrder ? 'divitem' : ''"
            label="客户转单"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isTransferOrder"
          >
            <span>{{ showData.isTransferOrder ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.lowResistanceTest ? 'divitem' : ''"
            label="低阻测试"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.lowResistanceTest"
          >
            <span>{{ showData.lowResistanceTest ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.confirmWorkingDraft ? 'divitem' : ''"
            label="确认工作稿"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.confirmWorkingDraft"
          >
            <span>{{ showData.confirmWorkingDraft ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.confirmImpedance ? 'divitem' : ''"
            label="确认阻抗"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.confirmImpedance"
          >
            <span>{{ showData.confirmImpedance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.confirmStacking ? 'divitem' : ''"
            label="确认叠层"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.confirmStacking"
          >
            <span>{{ showData.confirmStacking ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.attachedShippingFilm ? 'divitem' : ''"
            label="附出货菲林"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.attachedShippingFilm"
          >
            <span>{{ showData.attachedShippingFilm ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.humidityCardStr ? 'divitem' : ''"
            label="放湿度卡"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.humidityCardStr"
          >
            <span>{{ showData.humidityCardStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardSpacingPaperStr ? 'divitem' : ''"
            label="板间隔纸"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardSpacingPaperStr"
          >
            <span>{{ showData.boardSpacingPaperStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.withoutDesiccantStr ? 'divitem' : ''"
            label="干燥剂"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.withoutDesiccantStr"
          >
            <span>{{ showData.withoutDesiccantStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.packagingQuantity ? 'divitem' : ''"
            label="包装数量(PCS)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.packagingQuantity"
          >
            <span
              ><span v-if="showData.packagingQuantity">{{ showData.packagingQuantity }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.stampETStr ? 'divitem' : ''"
            label="加盖ET章"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.stampETStr"
          >
            <span>{{ showData.stampETStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.spareQuantity ? 'divitem' : ''"
            label="备品数量(PCS)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.spareQuantity"
          >
            <span
              ><span v-if="showData.spareQuantity">{{ showData.spareQuantity }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.relatedBrandStr ? 'divitem' : ''"
            label="相关品牌"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.relatedBrandStr"
          >
            <span>{{ showData.relatedBrandStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.brandMark ? 'divitem' : ''"
            label="品牌标记"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.brandMark"
          >
            <span>{{ showData.brandMark }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerHpNum ? 'divitem' : ''"
            label="金手指合拼款数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerHpNum"
          >
            <span>{{ showData.goldfingerHpNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isOrderReview ? 'divitem' : ''"
            label="订单评审"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isOrderReview"
          >
            <span>{{ showData.isOrderReview ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.freePanel ? 'divitem' : ''"
            label="自由拼版"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.freePanel"
          >
            <span>{{ showData.freePanel ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isLedPlate ? 'divitem' : ''"
            label="LED板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isLedPlate"
          >
            <span>{{ showData.isLedPlate ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isUncoverPlate ? 'divitem' : ''"
            label="揭盖板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isUncoverPlate"
          >
            <span>{{ showData.isUncoverPlate ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isHva ? 'divitem' : ''"
            label="HVA"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isHva"
          >
            <span>{{ showData.isHva ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isImpTestStrip ? 'divitem' : ''"
            label="提供阻抗条"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isImpTestStrip"
          >
            <span>{{ showData.isImpTestStrip ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.npFilmsNum ? 'divitem' : ''"
            label="NP更改菲林数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.npFilmsNum"
          >
            <span>{{ showData.npFilmsNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.npChangeTypeStr ? 'divitem' : ''"
            label="NP更改类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.npChangeTypeStr"
          >
            <span>{{ showData.npChangeTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testTimesDenseStr ? 'divitem' : ''"
            label="测试架倍密"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testTimesDenseStr"
          >
            <span>{{ showData.testTimesDenseStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fourWireTest ? 'divitem' : ''"
            label="四线测试"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fourWireTest"
          >
            <span>{{ showData.fourWireTest ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.layProtectiveFilm ? 'divitem' : ''"
            label="贴保护膜"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.layProtectiveFilm"
          >
            <span>{{ showData.layProtectiveFilm ? "是" : "" }}</span>
          </a-form-model-item>
        </div>
        <div style="width: 100%">
          <div class="div2">
            <a-form-model-item
              label="型号备注"
              :class="showData.note ? 'div22' : ''"
              v-if="showData.note"
              style="border-top: 1px solid #ddd; width: 100%; display: flex"
            >
              <div @contextmenu.prevent="rightClick1($event)">{{ showData.note }}</div>
            </a-form-model-item>
            <a-form-model-item
              label="备注确认"
              :class="showData.noteSure ? 'div22' : ''"
              v-if="showData.noteSure"
              style="border-top: 1px solid #ddd; width: 100%; display: flex"
            >
              <div @contextmenu.prevent="rightClick1($event)">{{ showData.noteSure }}</div>
            </a-form-model-item>
            <a-form-model-item
              label="特殊要求"
              style="border-top: 1px solid #ddd; width: 100%; display: flex"
              :class="showData.specialRequire ? 'div22' : ''"
              v-if="showData.specialRequire"
            >
              <div @contextmenu.prevent="rightClick1($event)">{{ showData.specialRequire }}</div>
            </a-form-model-item>
          </div>
        </div>
      </a-form-model>
      <a-menu :style="menuStyle" v-show="ccopy" class="tabRightClikBox">
        <a-menu-item @click="down">复制</a-menu-item>
      </a-menu>
    </div>
  </div>
</template>
<script>
export default {
  name: "",
  props: ["showData", "tabtype"],
  components: {},
  data() {
    return {
      showMore: false,
      printObj1: {
        id: "pre_qualified_information", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      copytext: "",
      ccopy: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
    };
  },

  created() {},
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.$nextTick(() => {
      this.setStyle();
    });
    if (this.showData.boardLayers > 2) {
      this.showMore = true;
    } else {
      this.showMore = false;
    }
  },
  methods: {
    closePrintTool() {
      document.title = "EMS | 订单报价";
    },
    printpdf() {
      document.title = this.showData.pcbFileName;
    },
    rightClick1(event) {
      this.copytext = event.target.innerText;
      this.ccopy = true;
      const popupElement = document.querySelector("#formDataElem1");
      const popupRect = popupElement.getBoundingClientRect();
      const x = event.clientX - popupRect.left;
      const y = event.clientY - popupRect.top;
      this.menuStyle.top = `${y + 60}px`;
      this.menuStyle.left = `${x + 20}px`;
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        (this.ccopy = false);
      document.body.removeEventListener("click", this.bodyClick);
    },
    down() {
      let input = document.createElement("input");
      input.value = this.copytext.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
      this.ccopy = false;
    },
    setStyle() {
      const elements = document.getElementsByClassName("divitem");
      const num = elements.length;
      var nums = 20;
      var num1 = 0;
      const SurfaceTreatment = document.getElementsByClassName("SurfaceTreatment");
      const OZ = document.getElementsByClassName("ThicknessTreatmentoz");
      const UM = document.getElementsByClassName("ThicknessTreatmentum");
      for (let index = 0; index < SurfaceTreatment.length; index++) {
        const element1 = SurfaceTreatment[index].innerText || SurfaceTreatment[index].textContent; // 考虑兼容性
        if (
          element1.indexOf("水金") != -1 ||
          element1.indexOf("镀金+沉金") != -1 ||
          element1.indexOf("镀水金+镀软金") != -1 ||
          element1.indexOf("整板镀金") != -1 ||
          element1.indexOf("镍钯金+金手指") != -1 ||
          element1.indexOf("镍钯金+镀金") != -1 ||
          element1.indexOf("镀金手指&化金") != -1 ||
          element1.indexOf("沉金+局部电金") != -1 ||
          element1.indexOf("选择性镀金") != -1 ||
          element1.indexOf("镀金+局部厚金") != -1 ||
          element1.indexOf("电镀银+电镀金") != -1 ||
          element1.indexOf("镀硬金") != -1 ||
          element1.indexOf("镍钯金") != -1 ||
          element1.indexOf("镀金不镀镍+局部厚金") != -1 ||
          element1.indexOf("沉金+局部厚金") != -1
        ) {
          SurfaceTreatment[index].children[0].style.height = "50px";
          SurfaceTreatment[index].children[1].children[0].style.height = "50px";
          num1 = 1;
        } else {
          SurfaceTreatment[index].children[0].style.height = "25px";
          SurfaceTreatment[index].children[1].children[0].style.height = "25px";
        }
      }
      if (UM.length > 0) {
        for (let index = 0; index < UM.length; index++) {
          const element1 = UM[index].innerText || UM[index].textContent; // 考虑兼容性;;
          num1 = Math.floor(element1.length / 40) + num1;
          UM[index].children[0].style.height = 25 * Math.ceil(element1.length / 40) + "px";
          UM[index].children[1].children[0].style.height = 25 * Math.ceil(element1.length / 40) + "px";
        }
      }
      if (OZ.length > 0) {
        for (let index = 0; index < OZ.length; index++) {
          const element1 = OZ[index].innerText || OZ[index].textContent; // 考虑兼容性;
          num1 = Math.floor(element1.length / 40) + num1;
          OZ[index].children[0].style.height = 25 * Math.ceil(element1.length / 40) + "px";
          OZ[index].children[1].children[0].style.height = 25 * Math.ceil(element1.length / 40) + "px";
        }
      }
      nums = nums - num1;
      for (var a = 0; a < elements.length; a++) {
        if (a < nums) {
          elements[a].style.width = "40%";
          elements[a].childNodes[0].style.width = "29%";
          elements[a].childNodes[1].style.width = "71%";
        } else {
          elements[a].style.width = "30%";
          elements[a].childNodes[0].style.width = "48%";
          elements[a].childNodes[1].style.width = "52%";
        }
      }
      var div2 = document.getElementsByClassName("div2")[0];
      div2.style.width = "100%";
      var div22 = document.getElementsByClassName("div22");
      for (var i = 0; i < div22.length; i++) {
        div22[i].childNodes[0].style.width = "11.7%";
        div22[i].childNodes[1].style.width = "89.9%";
      }
    },
  },
};
</script>
<style scoped lang="less">
@media print {
  /deep/.ant-form-item-label {
    -webkit-print-color-adjust: exact; /* 强制打印背景色 */
  }
}
.printstyle {
  position: absolute;
  bottom: 8px;
  right: 83px;
}
.tabRightClikBox {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: #fff9e6;
    color: #ff9900;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.tmp {
  word-break: keep-all;
  vertical-align: top;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
  display: inline-block;
}
/deep/.ant-form-item {
  margin-right: 0;
}
/deep/.ant-form label {
  font-family: PingFangSC-Regular, Sans-serif;
  font-weight: 500;
  color: #000000;
  font-size: 13px;
}
.widthclass {
  .select1 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 37px !important;
    }
  }
  .select2 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 56px !important;
    }
  }
}
.bborder {
  border-left: 1px solid #ddd;
  .div2 {
    /deep/.ant-form-item-control {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      line-height: normal;
    }
  }
}
/deep/b {
  font-weight: 500;
}
.div22 {
  /deep/.ant-form-item-control-wrapper {
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    color: #000000;
    max-height: 76px;
    overflow: auto;
  }
  /deep/.ant-form-item-label {
    border-bottom: 1px solid #ddd;
    border-right: 1px solid #ddd;
    background-color: #f1f1f1;
    color: #000000;
  }
}
/deep/.require11 {
  color: red !important;
}
// .cu{
//   /deep/.ant-form-item-control{
//     height:26.67px;
//   }
// }
#formDataElem1 {
  .div1 {
    border-top: 1px solid #ddd;
    /deep/.ant-form-item-label {
      height: 25px;
      line-height: 25px;
      border-bottom: 1px solid #ddd;
      border-right: 1px solid #ddd;
      background-color: #f1f1f1;
    }
    .ant-form-item {
      width: 30%;
      margin-right: 0;
    }
    /deep/ .ant-form-item-control-wrapper {
      font-family: PingFangSC-Regular, Sans-serif;
      font-weight: 500;
      color: #000000;
      font-size: 13px;
      .ant-form-item-control {
        .ant-form-item-children {
          display: block;
          min-height: 13.672px;
        }
        line-height: 20px;
        padding: 2px 4px !important;
        height: 25px;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
      }
    }
    // /deep/.pcbFileName{
    //   .ant-form-item-label{
    //     width:30%;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:70%;
    //   }
    // }
  }
}
/deep/.ant-input-affix-wrapper {
  width: 100%;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 6px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 2px;
}
/deep/.line3 {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
}
.bbb {
  /deep/.ant-form-item-label {
    width: 16.65%;
    // -ms-width: 101px important; // IE
    // -webkit-width:100.5%; //谷歌
    border-left: 1px solid #ddd;
  }
  /deep/.ant-form-item-control-wrapper {
    // -ms-width: 911px!important; // IE
    // -webkit-width:942.0.5%; //谷歌
    width: 83.3%;
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
// .bbb{
//   /deep/.ant-form-item-label{
//     width:105px;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   /deep/.ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     // width:1038px;
//     width:100%;
//   }
//   /deep/textarea.ant-input {
//     min-height:24px;
//   }
//   /deep/.ant-form-item-control{
//     padding: 2px !important;
//     // width: 942px;
//     width:100%;
//   }
//   /deep/.ant-input{
//     height:24px;
//   }
// }

/deep/.ant-select-selection--single {
  height: 22px !important;
}
/deep/.ant-select-item-option-content {
  color: red !important;
}
/deep/.ant-select-selection__rendered {
  line-height: 20px !important;
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.require1 {
  .ant-form-item-label > label {
    color: red !important;
    background-color: greenyellow;
  }
}
// /deep/.bac{
//     .ant-form-item-label > label {
//     color: red!important;
//     background-color: #ff9900;
// }

// }
span {
  font-size: 13px;
}

/deep/.ant-select {
  font-size: 13px !important;
}
/deep/.ant-input {
  font-size: 13px !important;
  font-weight: 500;
}
.contentInfo {
  font-size: 13px;
  //width:1676px;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    font: 12px / 1.14 arial;
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 10.5%;
        text-indent: 0.5%;
        font-size: 13px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 10.5%;
      }
    }
    .special {
      height: 268px;
      width: 456px;
      display: inline-block;
      position: absolute;
      right: 172px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 270px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 261px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 270px;
              width: 75px;
            }
          }
        }
      }
    }
    .special1 {
      height: 268px;
      width: 308px;
      display: inline-block;
      position: absolute;
      right: 320px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 267px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 250px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 267px;
            }
          }
        }
      }
    }
    .ant-card-body {
      // display: flex;
      padding: 0;
      .ant-form {
        // width:80.5%;

        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
        .ant-row {
          .line2 {
            .ant-form-item-label {
              border-bottom: 0px;
            }
          }
          .line {
            .ant-form-item-control {
              border-bottom: 0px;
            }
          }
        }
      }
      .spec {
        width: 19%;
        position: absolute;
        top: 0;
        right: 0;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .editWrapper {
          width: 100%;
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            // width:96px;
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            // width: 96px;
            width: 99%;
          }
        }
        .ant-form-item-label {
          // width: 117px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font: 12px/1.14 "微软雅黑", arial;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          // border-left: 1px solid #ddd;
          label {
            // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
            font-family: PingFangSC-Regular, Sans-serif;
            font-size: 13px;
            font-weight: 500;
            color: #000000;
          }
        }
        .ant-form-item-control-wrapper {
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font-family: PingFangSC-Regular, Sans-serif;
          font-weight: 500;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 6px 4px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
