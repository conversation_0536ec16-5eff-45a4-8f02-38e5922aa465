<!-- 市场管理 - 客服管理 -->
<template>
  <a-spin :spinning="spinning">
    <a-card class="box" :bordered="false">
      <div class="box_left">
        <div class="operator" ref="active">
          <a-input
            placeholder="请输入用户编码"
            v-model="formNew.custNo"
            style="width: 180px; margin-left: 7px; margin-right: 5px; margin-top: 8px"
            @keyup.enter.native="handleOk()"
            allowClear
          ></a-input>
          <a-input
            placeholder="请输入订单号"
            v-model="formNew.orderno"
            style="width: 180px; margin-left: 7px; margin-right: 5px; margin-top: 8px"
            @keyup.enter.native="handleOk()"
            allowClear
          ></a-input>
          <a-select
            v-model="formNew.ordernostate"
            @keyup.enter.native="handleOk"
            showSearch
            allowClear
            optionFilterProp="lable"
            placeholder="请选择"
            style="width: 90px; margin-left: 5px; margin-right: 5px"
          >
            <a-select-option v-for="(item, index) in list" :key="index" :value="item.value" :lable="item.text">{{ item.text }}</a-select-option>
          </a-select>
          <a-button type="primary" @click="handleOk" style="margin-left: 5px; margin-right: 5px" class="showClass"> 查询 </a-button>
          <a-button type="primary" @click="startClick" style="margin-left: 5px; margin-right: 5px" class="showClass"> 开始 </a-button>
          <a-button type="primary" @click="completeClick" style="margin-left: 5px; margin-right: 5px" class="showClass"> 完成 </a-button>
          <a-button type="primary" @click="orderaccess" style="margin-left: 5px; margin-right: 5px" class="showClass"> 订单接入 </a-button>
          <a-button type="primary" @click="refuse" style="margin-left: 5px; margin-right: 5px" class="showClass"> 拒绝 </a-button>
          <span class="box" v-if="showBtn">
            <a-button type="dashed" @click="toggleAdvanced">
              {{ advanced ? "收起" : "展开" }} <a-icon :type="advanced ? 'right' : 'left'" />
            </a-button>
          </span>
          <span v-if="buttonsmenu">
            <a-dropdown>
              <a-button type="primary" style="margin-top: 9px; margin-right: 5px" @click.prevent> 按钮菜单栏 </a-button>
              <template #overlay>
                <a-menu class="tabRightClikBox3">
                  <a-menu-item @click="startClick">开始</a-menu-item>
                  <a-menu-item @click="completeClick">完成</a-menu-item>
                  <a-menu-item @click="orderaccess">订单接入</a-menu-item>
                  <a-menu-item @click="refuse">拒绝</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </span>
        </div>
        <div class="content" style="height: 736px" ref="tableWrapper">
          <a-table
            :columns="columns"
            :dataSource="dataSource"
            :scroll="{ x: 1411, y: 695 }"
            :customRow="customRow"
            :rowKey="'id'"
            :orderListTableLoading="orderListTableLoading"
            :pagination="pagination"
            @change="handleTableChange"
            :rowClassName="isRedRow"
            class="leftstyle"
          >
            <template slot="edit_content" slot-scope="text, record">
              <a-popover :open="visible" title="提交内容" trigger="click" placement="topLeft">
                <template #content>
                  <span @click="hide = false">{{ record.edit_content }}</span>
                </template>
                <a-icon v-if="record.edit_content" type="question-circle" class="userStyle" style="color: #ff9900; font-size: 18px" />
              </a-popover>
            </template>
            <template slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </template>
            <template slot="edit_file_url" slot-scope="text, record">
              <a class="noCopy" style="color: #428bca" @click.stop="downFile(record)" v-if="record.edit_file_url">下载</a>
            </template>
            <template slot="action1" slot-scope="text, record">
              <a-tooltip title="订单报价">
                <a-icon type="container" class="userStyle" style="color: #ff9900; font-size: 18px" @click.stop="goDetail(record)" />
              </a-tooltip>
            </template>
            <template slot="reOrderInput" slot-scope="text, record">
              <a-checkbox :checked="record.reOrderInput" />
            </template>
            <!-- <div slot="order_no" slot-scope="text,record" >   
            <a v-if="record.status != '待预审'" style="color: #428bca" :title="record.order_no" @click.stop="goDetail(record)" >{{record.order_no}}</a>
            <a v-else :title="record.order_no" style="color: black"> {{record.order_no}}</a>&nbsp; 
            <span class="tagNum" style="display:inline-block;height:19px;">
              <span
                v-if="!record.isJiaji" class="userStyle"
                style="font-size: 14px;font-weight: 500; color:#ff9900;padding: 0 2px; margin: 0; display:inline-block;height: 19px;width:14px;margin-right:4px;margin-left:-10px;user-select: none;"
            ><a-icon type="thunderbolt" theme="filled"></a-icon> </span>
            <a-tag
                v-if="record.isBigCus=='是'" class="userStyle"
                style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca"
            >
            KA
            </a-tag>
            <a-tag
                v-if="record.quality =='优品'" class="userStyle"
                style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca"         
            >
              优品
            </a-tag>
            <a-tag
              v-if="record.reverseOrder_" class="userStyle"
                style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca"        
            >
              {{ record.reverseOrder_ | splitFilter}}
            </a-tag>
            </span> 
          </div>    -->
          </a-table>
          <right-copy ref="RightCopy" />
        </div>
        <div class="bto" style="user-select: none"></div>
      </div>
      <!-- 完结信息弹窗 -->
      <a-modal
        :title="'完结信息'"
        :visible="visibleMode"
        @cancel="visibleMode = false"
        @ok="handleOkpay"
        centered
        destroyOnClose
        :maskClosable="false"
        :width="600"
        ok-text="确定"
        cancel-text="取消(Esc)"
      >
        <PayModel ref="paymodel" :allow_to_pay="allow_to_pay" :allow_to_refuse="allow_to_refuse" :Returnorder="Returnorder" :ordertype="ordertype" />
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        title="确认弹窗"
        :visible="confirmdataVisible"
        @cancel="confirmdataVisible = false"
        @ok="confirmhandleOk"
        ok-text="确定"
        cancel-text="取消(Esc)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="color: red; font-weight: bold">[{{ proorderNo }}] {{ promptmessage }}</span>
      </a-modal>
      <!-- 拒绝 -->
      <a-modal
        title="拒绝"
        :visible="refuseVisible"
        @cancel="refuseVisible = false"
        @ok="refusehandleOk"
        ok-text="确定"
        cancel-text="取消(Esc)"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        centered
      >
        <a-form-model-item label="拒绝原因" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
          <a-textarea style="font-weight: 500" v-model="verified_detail" :auto-focus="true" :auto-size="{ minRows: 5, maxRows: 8 }" />
        </a-form-model-item>
      </a-modal>
      <!-- 订单接入 -->
      <a-modal
        title="订单接入"
        :visible="oderdataVisible"
        @cancel="oderdataVisible = false"
        @ok="orderhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="fdloading"
        centered
      >
        <a-form-model-item label="返单更改" :labelCol="{ span: 7 }" :wrapperCol="{ span: 1 }" style="text-align: center; margin-bottom: 0">
          <a-checkbox v-model="Accessform.isReorder"></a-checkbox>
        </a-form-model-item>
        <a-form-model-item label="生产型号：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12 }">
          <a-input v-model="Accessform.proOrderNo" allowClear />
        </a-form-model-item>
        <a-form-model-item label="客户代码" ref="custNo" prop="custNo" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12 }">
          <a-select
            placeholder="请选择客户代码"
            v-model="Accessform.custNo"
            :dropdownMatchSelectWidth="false"
            showSearch
            optionFilterProp="children"
            @popupScroll="handlePopupScroll"
            allowClear
            @search="supValue"
            style="width: 176px"
            :disabled="user.factoryId != 22"
          >
            <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-modal>
    </a-card>
  </a-spin>
</template>
<script>
import RightCopy from "@/pages/RightCopy";
import { pcbordertonew } from "@/services/mkt/PrequalificationProduction.js";
import { mktCustNo, mktcustnobyfAC } from "@/services/mkt/Inquiry.js";
import { getList, pcborderinfostart, pcborderinfoend, getOrderInfo, orderrefuse } from "@/services/mkt/serviceManeg";
import { checkPermission } from "@/utils/abp";
import { mapState } from "vuex";
import PayModel from "@/pages/mkt/ServiceManaging/module/PayModel.vue";
import Cookie from "js-cookie";
import moment from "moment";
const columns = [
  {
    title: "序号",
    dataIndex: "",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
    className: "userStyle",
    // fixed:'left',
  },
  {
    title: "用户编号",
    dataIndex: "customer_no",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "原订单号",
    dataIndex: "order_no",
    ellipsis: true,
    width: 150,
    className: "userStyle",
  },
  {
    title: "改单序号",
    dataIndex: "edit_Index",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "提交内容",
    align: "center",
    ellipsis: true,
    width: 70,
    className: "userStyle",
    scopedSlots: { customRender: "edit_content" },
  },
  {
    title: "客户型号",
    dataIndex: "file_name",
    align: "left",
    ellipsis: true,
    width: 210,
    className: "userStyle",
  },
  {
    title: "生产编号",
    dataIndex: "pdct_no",
    width: 130,
    ellipsis: true,
    align: "left",
    className: "userStyle",
  },
  {
    title: "发起时间",
    dataIndex: "createtime",
    align: "left",
    ellipsis: true,
    width: 150,
    className: "userStyle",
  },
  {
    title: "文件下载",
    scopedSlots: { customRender: "edit_file_url" },
    width: 70,
    ellipsis: true,
    align: "center",
    className: "userStyle noCopy",
  },
  {
    title: "处理人",
    dataIndex: "startname",
    width: 70,
    ellipsis: true,
    align: "left",
    className: "userStyle",
  },
  // {
  //   title: "核价人",
  //   dataIndex: "endname",
  //   align: "left",
  //   ellipsis: true,
  //   width:70,
  //   className:'userStyle',
  // },
  {
    title: "取单时间",
    dataIndex: "startdate",
    align: "left",
    ellipsis: true,
    width: 160,
    className: "userStyle",
  },
  {
    title: "产生费用",
    dataIndex: "cost",
    width: 80,
    ellipsis: true,
    align: "left",
    className: "userStyle",
  },
  {
    title: "订单状态",
    dataIndex: "stateStr",
    width: 80,
    // width: '5%',
    ellipsis: true,
    align: "left",
    className: "userStyle",
  },
  {
    title: "订单类型",
    dataIndex: "ordertype",
    width: 80,
    ellipsis: true,
    align: "left",
    className: "userStyle",
  },
  {
    title: "返单导入",
    scopedSlots: { customRender: "reOrderInput" },
    width: 60,
    ellipsis: true,
    align: "center",
    className: "userStyle",
  },
  {
    title: "预审",
    scopedSlots: { customRender: "action1" },
    align: "center",
    fixed: "right",
    className: "userStyle",
    width: 45,
  },
];
export default {
  name: "ServiceManaging",
  components: { PayModel, RightCopy },
  inject: ["reload"],
  data() {
    return {
      spinning: false,
      ordertype: "",
      advanced: false,
      oderdataVisible: false,
      supList: [],
      frontDataZSupplier: [], // 供应商 100条数据的集合
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      verified_detail: "", //拒绝原因
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      fdloading: false,
      Accessform: {},
      visible: false,
      showBtn: false,
      formNew: {
        custNo: "", //客户编号
        orderno: "", //订单编号
        ordernostate: undefined, //状态
      },
      columns,
      selectedRowKeys: [],
      selectedRowsData: {},
      startIndex: -1,
      shiftKey: false,

      menuData: {},
      buttonsmenu: false,
      dataSource: [],
      orderListTableLoading: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      list: [
        { value: "待审核", text: "待审核" },
        { value: "审核中", text: "审核中" },
        { value: "待付款", text: "待付款" },
        { value: "已生效", text: "已生效" },
        { value: "已拒绝", text: "已拒绝" },
        { value: "全部", text: "全部" },
      ],
      params1: {},
      pageStat: false,
      visibleMode: false,
      confirmdataVisible: false,
      refuseVisible: false,
      state: "",
      proorderNo: "",
      promptmessage: "",
      allow_to_pay: false,
      allow_to_refuse: false,
      Returnorder: false,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      if (elements.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < 7) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      elements[0].style.display = "inline-block";
      if (elements.length * 95 + 500 < window.innerWidth) {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      } else {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
        this.buttonsmenu = true;
      }
      var leftContent = document.getElementsByClassName("content")[0];
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 175 + "px";
      } else {
        leftContent.style.height = "737px";
      }
    });
    this.getdataList();
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  watch: {
    visibleMode: function (val) {
      this.$nextTick(() => {
        document.querySelector(".ant-modal > div").removeAttribute("aria-hidden");
      });
    },
    confirmdataVisible: function (val) {
      this.$nextTick(() => {
        document.querySelector(".ant-modal > div").removeAttribute("aria-hidden");
      });
    },
  },
  mounted() {
    this.getSupplier();
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize);
  },
  methods: {
    checkPermission,
    orderaccess() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.warn("请选择订单");
        return;
      }
      if (!this.selectedRowsData.reOrderInput) {
        this.$message.warn("当前操作仅限需返单导入的任务");
        return;
      }
      this.getSupplier();
      this.oderdataVisible = true;
      this.Accessform = {};
      this.$set(this.Accessform, "proOrderNo", this.selectedRowsData.pdct_no);
    },
    getSupplier() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("mktCustNo"));
      let factory = this.selectedRowsData.joinFactoryId || this.user.factoryId;
      if (
        data &&
        token &&
        data.filter(item => {
          return item.factory == factory;
        }).length
      ) {
        for (let index = 0; index < data.length; index++) {
          if (data[index].token == token && data[index].factory == factory) {
            const element = data[index];
            this.supList = element.data;
            this.frontDataZSupplier = element.data.slice(0, 20);
          }
        }
      } else {
        if (factory == 58 || factory == 59) {
          mktcustnobyfAC(factory).then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplier = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          mktCustNo().then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplier = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr));
              } else {
                data.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data));
              }
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value.toUpperCase();
        let arr = that.supList.filter(m => m.toUpperCase().indexOf(value.toUpperCase()) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.supList.slice(0, 20);
      }
    },
    orderhandleOk() {
      if (!this.Accessform.proOrderNo) {
        this.$message.warning("请输入生产型号！");
        return;
      }
      let formData = {
        proOrderNo: this.Accessform.proOrderNo,
        isReorder: this.Accessform.isReorder,
        custNo: this.Accessform.custNo,
        id: this.selectedRowsData.id,
      };
      this.fdloading = true;
      this.spinning = true;
      pcbordertonew(formData)
        .then(res => {
          if (res.code) {
            this.$message.success("导入成功");
            this.getdataList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.fdloading = false;
          this.spinning = false;
        });
      this.oderdataVisible = false;
    },
    handleResize() {
      var leftstyle = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftstyle1 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[1].children[1].children[0];
      var leftContent = document.getElementsByClassName("content")[0];
      const elements = document.getElementsByClassName("showClass");
      elements[0].style.display = "inline-block";
      if (elements.length * 95 + 700 < window.innerWidth) {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      } else {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
        this.buttonsmenu = true;
      }
      if (leftstyle && this.dataSource.length != 0) {
        leftstyle.style.height = window.innerHeight - 214 + "px";
        leftstyle1.style.height = window.innerHeight - 214 + "px";
      } else {
        leftstyle.style.height = 0;
        leftstyle1.style.height = 0;
      }
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 175 + "px";
      } else {
        leftContent.style.height = "737px";
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      if (window.innerWidth < 1920) {
        if (paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
      this.$forceUpdate();
    },
    handleOk(e) {
      this.params1 = this.formNew;
      this.formNew.PageIndex = "1";
      this.formNew.PageSize = this.pagination.pageSize;
      getList(this.formNew).then(res => {
        this.dataSource = res.data.items;
        if ((this.formNew.custNo || this.formNew.orderno || this.formNew.ordernostate) && this.dataSource.length) {
          this.selectedRowKeys[0] = this.dataSource[0].id;
          this.selectedRowsData = this.dataSource[0];
        }
        this.pagination.current = 1;
        this.pagination.total = res.data.totalCount;
        setTimeout(() => {
          this.handleResize();
        }, 0);
      });
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          if (i < 7) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    //获取列表
    getdataList(queryData) {
      this.spinning = true;
      this.orderListTableLoading = true;
      this.params1 = {};
      this.pageStat = localStorage.getItem("stat_servicemana") == "true" ? true : false;
      let obj = JSON.parse(localStorage.getItem("kfglqueryInfo"));
      if (obj) {
        this.params1 = obj;
        queryData = obj;
      }
      if (this.pageStat) {
        let pageCurrent = localStorage.getItem("pageCurrent_servicemana");
        if (pageCurrent != undefined && pageCurrent != "") {
          this.pagination.current = parseInt(pageCurrent);
        }
        let pageSize = localStorage.getItem("pageSize_servicemana");
        if (pageSize != undefined && pageSize != "") {
          this.pagination.pageSize = parseInt(pageSize);
        }
        let data = localStorage.getItem("queryParam_servicemana");
        if (data != null && data != undefined && data != "") {
          this.params1 = JSON.parse(data);
        }
      }
      this.params1.pageIndex = this.pagination.current;
      this.params1.pageSize = this.pagination.pageSize;
      let data = {
        ...this.params1,
      };

      localStorage.setItem("queryParam_servicemana", JSON.stringify(data));
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      if (queryData) {
        params.order_no = queryData.OrderNo;
        params.ordernostate = queryData.ordernostate;
        params.custNo = queryData.custNo;
      }
      let indexId = localStorage.getItem("id_servicemana");
      let record = localStorage.getItem("record_servicemana");
      getList(params)
        .then(res => {
          if (res.code) {
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            // pagination.current = res.data.pageIndex
            this.pagination = pagination;
            this.dataSource = res.data.items;
            this.selectedRowKeys = [];
            this.selectedRowsData = {};
            localStorage.removeItem("kfglqueryInfo");
            if (indexId !== "" && indexId != null) {
              this.selectedRowKeys[0] = indexId;
            }
            if (record !== "" && record != null) {
              this.selectedRowsData = JSON.parse(record);
            }
            if (this.pageStat) {
              localStorage.removeItem("id_servicemana");
              localStorage.removeItem("record_servicemana");
              localStorage.removeItem("pageCurrent_servicemana");
              localStorage.removeItem("pageSize_servicemana");
              localStorage.removeItem("stat_servicemana");
            }
            setTimeout(() => {
              this.handleResize();
            }, 0);
            for (var x = 0; x < this.dataSource.length; x++) {
              this.dataSource[x].factoryName;
            }
          } else {
            //this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
          this.spinning = false;
        });
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },

    keydown(e) {
      this.shiftKey = e.ctrlKey;
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "67" && !this.isCtrlPressed) {
        //c
        this.isCtrlPressed = false;
        //this.reportHandleCancel()
        //e.preventDefault()
      } else if (e.keyCode == "13" && this.visibleMode) {
        this.handleOkpay();
        e.preventDefault();
      } else if (e.keyCode == "13" && this.confirmdataVisible) {
        //enter
        this.confirmhandleOk();
        e.preventDefault();
      }
    },
    customRow(record, index) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeys = keys;
            this.selectedRowsData = record;
            this.$nextTick(function () {
              localStorage.setItem("pageCurrent_servicemana", this.pagination.current);
              localStorage.setItem("pageSize_servicemana", this.pagination.pageSize);
              localStorage.setItem("id_servicemana", record.id);
              localStorage.setItem("record_servicemana", JSON.stringify(record));
              localStorage.setItem("stat_servicemana", true);
              localStorage.setItem("kfglqueryInfo", JSON.stringify(this.params1));
            });
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      //this.pageStat=false
      localStorage.removeItem("stat");
      localStorage.setItem("kfglqueryInfo", JSON.stringify(this.params1));
      if (JSON.stringify(this.params1) != "{}") {
        this.getdataList(this.params1);
      } else {
        this.getdataList();
      }
    },
    refuse() {
      if (this.selectedRowKeys.length == 1) {
        this.proorderNo = this.selectedRowsData.order_no;
        this.refuseVisible = true;
        this.verified_detail = "";
      } else {
        this.$message.warning("请选择数据");
      }
    },
    refusehandleOk() {
      let params = {
        id: this.selectedRowKeys[0],
        verified_detail: this.verified_detail,
      };
      orderrefuse(params).then(res => {
        if (res.code == 1) {
          this.$message.success("操作成功");
          this.getdataList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    goDetail(record) {
      var id = record.id;
      getOrderInfo(id).then(res => {
        if (res.code == 1) {
          this.$nextTick(function () {
            localStorage.setItem("pageCurrent_servicemana", this.pagination.current);
            localStorage.setItem("pageSize_servicemana", this.pagination.pageSize);
            localStorage.setItem("id_servicemana", record.id);
            localStorage.setItem("record_servicemana", JSON.stringify(record));
            localStorage.setItem("stat_servicemana", true);
            localStorage.setItem("kfglqueryInfo", JSON.stringify(this.params1));
            localStorage.setItem("orderNo_servicemana", res.data.orderNo);
            this.$router.push({ path: "OrderOffer" });
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    startClick() {
      if (this.selectedRowKeys.length == 1) {
        this.state = "开始";
        this.proorderNo = this.selectedRowsData.order_no;
        this.promptmessage = "确定开始吗？";
        this.confirmdataVisible = true;
      } else {
        this.$message.warning("请选择数据");
      }
    },
    completeClick() {
      if (this.selectedRowKeys.length == 1) {
        if (this.selectedRowsData.ordertype == "订单更改" || this.selectedRowsData.ordertype == "订单取消") {
          this.allow_to_pay = true;
        } else {
          this.allow_to_pay = false;
        }
        if (this.selectedRowsData.ordertype == "订单更改") {
          this.allow_to_refuse = true;
        } else {
          this.allow_to_refuse = false;
        }
        if (this.selectedRowsData.ordertype == "返单无改" || this.selectedRowsData.ordertype == "返单更改") {
          this.Returnorder = true;
          this.ordertype = this.selectedRowsData.ordertype == "返单无改" ? "1" : "2";
        } else {
          this.Returnorder = false;
        }
        console.log("this.allow_to_pay", this.allow_to_pay);
        this.visibleMode = true;
      } else {
        this.$message.warning("请选择数据");
      }
    },
    handleOkpay() {
      const form = this.$refs.paymodel.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          this.complete();
          this.visibleMode = false;
        }
      });
    },
    confirmhandleOk() {
      if (this.state == "开始") {
        this.start();
      }
      this.confirmdataVisible = false;
    },
    start() {
      pcborderinfostart(this.selectedRowKeys[0]).then(res => {
        if (res.code == 1) {
          this.$message.success("操作成功");
          this.getdataList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    complete() {
      let params = {
        id: this.selectedRowKeys[0],
        is_refuse: this.$refs.paymodel.form.is_refuse,
        cost: Number(this.$refs.paymodel.form.cost),
        verified_detail: this.$refs.paymodel.form.verified_detail,
        orderType: this.$refs.paymodel.form.orderType,
      };
      pcborderinfoend(params).then(res => {
        if (res.code) {
          this.$message.success("操作成功");
          this.getdataList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel() {
      this.visibleMode = false;
      this.refuseVisible = false;
      this.confirmdataVisible = false;
    },
    downFile(record) {
      var filePath = record.edit_file_url;
      if (!filePath) {
        this.$message.warning("文件不存在");
        return;
      }
      if (filePath.indexOf("myhuaweicloud") != -1) {
        const urlObj = new URL(filePath);
        const path = urlObj.pathname;
        const fileName = path.substring(path.lastIndexOf("/") + 1);
        const fileNameWithoutQuery = decodeURI(fileName.split("?")[0]);
        let a = "";
        if (this.parseQueryParams(filePath).PATH) {
          let path = this.parseQueryParams(filePath).PATH;
          a = path.split(".")[path.split(".").length - 1];
        } else {
          a = filePath.split(".")[filePath.split(".").length - 1];
        }
        const xhr = new XMLHttpRequest();
        xhr.open("GET", filePath, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = record.order_no + record.file_name;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      } else {
        window.location.href = filePath;
      }
    },
    parseQueryParams(url) {
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);
      const queryParams = {};
      for (const [key, value] of params.entries()) {
        queryParams[key] = value;
      }
      return queryParams;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-table-scroll table .ant-table-fixed-columns-in-body:not([colspan]) > * {
  visibility: visible;
}
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.showClass {
  width: 83px;
  padding: 0;
}
/deep/.ant-table-pagination {
  float: left;
  position: absolute;
  margin: 9px 8px;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectackend {
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
/deep/.ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
/deep/.userStyle {
  user-select: none !important;
}
.tabRightClikBox {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 2px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 7px 2px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-card-body {
  padding: 0px !important;
}
.operator {
  margin-bottom: 0px;
  height: 49px;
  padding-left: 6px;
  background: #ffffff;
}
.box_left {
  width: 100%;
  z-index: 10;
}
.content {
  border: 2px solid #e9e9f0;
  /deep/.mintable {
    .ant-table-pagination.ant-pagination {
      margin: 9px 0;
      z-index: 99;
      position: absolute;
      bottom: -54px;
      margin-left: 1%;
    }
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-tbody > tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
  /deep/ .ant-table {
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
  }
  /deep/.ant-table-body {
    .ant-table-fixed {
      .ant-table-row-selected {
        td {
          background: #dfdcdc;
        }
      }
      .userStyle {
        user-select: none !important;
      }
    }
  }
}
.bto {
  width: 100%;
  height: 42px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background: #ffffff;
  border-top: 0;
}
</style>
