<!-- 车间管理-飞针管理-人员登记 -->
<template>
  <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 12}" >
    <a-form-item label="过序密码">
      <a-input   v-model='stepPassword'  :autoFocus="autoFocus"/>
    </a-form-item>
    
  </a-form>
</template>

<script>
export default {
    name:'PersonnelRegistration',
    props:[''],
  data() {
    return {
        stepPassword:'',
        autoFocus:true
    };
  },
  methods: {  
 
  },
};
</script>