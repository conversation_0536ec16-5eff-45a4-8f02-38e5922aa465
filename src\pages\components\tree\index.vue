<template>
  <a-card>
      <div>
        <a-alert message="展示可勾选，可选中，禁用，默认展开等功能" type="success" class='alert'/>
        <tree 
          :treeData='treeData'
          @onSelect="onSelect"
          @onCheck="onCheck"
        ></tree>
      </div>
  </a-card>
</template>
<script>
const treeData = [
  {
    title: 'parent 1',
    key: '0-0',
    children: [
      {
        title: 'parent 1-0',
        key: '0-0-0',
        disabled: true,
        children: [
          { title: 'leaf', key: '0-0-0-0', disableCheckbox: true },
          { title: 'leaf', key: '0-0-0-1' },
        ],
      },
      {
        title: 'parent 1-1',
        key: '0-0-1',
        children: [{ key: '0-0-1-0', slots: { title: 'title0010' } }],
      },
    ],
  },
];
import tree from '@/components/tree/tree'
export default {
  components: {
      tree
  },
  data() {
    return {
      treeData,
    };
  },
  methods: {
    onSelect(info) {
      console.log('selected', info);
    },
    onCheck(info) {
      console.log('onCheck', info);
    },
  },
};
</script>