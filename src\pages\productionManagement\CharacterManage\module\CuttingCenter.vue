<!-- 车间管理-字符管理-右边数据 -->
<template>
  <a-card :bordered="false">
    <div class="machine" style="border: 1px solid #E9E9F0;">
<!--      @contextmenu.prevent="rightClick($event)"-->
      <div class="left" style="border: 1px solid #E9E9F0;" >
        <a-table
            rowKey="iD_"
            :columns="columns"
            :dataSource="tableData"
            :pagination="false"
            :loading="table3Loading"
            :keyboard="false"
            :bordered="true"
            :maskClosable="false"
            :scroll="{ x:300, y: 744 }"
            :customRow="eventTouch"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange,getCheckboxProps: getCheckboxProps}"
            :rowClassName="setRowClassName"
            :class="{'minClass':tableData.length > 0}"
        >
        </a-table>
<!--        <a-menu :style="menuStyle" v-if="menuVisible" class="tabRightClikBox">-->
<!--          <a-menu-item @click="signError">{{statusText}}</a-menu-item>-->
<!--          <a-menu-item @click="BatchModification">批量修改</a-menu-item>-->
<!--        </a-menu>-->
      </div>
      <div class="right" style="border: 1px solid #E9E9F0;">
        <div class="top" style="border-bottom: 3px solid #E9E9F0;">
          <a-table
              :rowKey="(record,index)=>{return index}"
              :columns="columns2"
              :dataSource="machineStatuList"
              :pagination="false"
              :loading="machineStatuLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
          >
          </a-table>
        </div>
        <div class="bot">
          <!-- 机台上列表 -->
          <a-table
              v-if="showAssign"
              :rowKey="(record,index)=>{return index}"
              :columns="columns4"
              :dataSource="dispatchList"
              :pagination="false"
              :loading="dispatchLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :customRow="eventTouch1"
              :rowClassName="setRowClassName1"
              :scroll="{ x: 300,y:685 }"

          >
          <span slot="pnlQty_" slot-scope="record">
           <a-input v-model="record.pnlQty_" @blur="savePnl(record)"/>
          </span>
          <template slot="name" slot-scope="text, record">
            <a-button @click="onSelectChange1(record)" v-if="checkPermission('MES.ProductionModule.Character.CharacterBackSend')">回退</a-button>
          </template>
          </a-table>

        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import { signMachineStatus,updatePnl,BatchModificationQuantity} from "@/services/fly-management";
import { checkPermission } from "@/utils/abp";
import {up4CharacterPnlQty} from "@/services/production";

// 当班统计
const columns2 = [

  {
    title: "状态",
    dataIndex: "state_",
    width: 50,
    align: 'center',
  },
  {
    title: "机台数",
    dataIndex: "muCount_",
    width: 60,
    align: 'center',
  },
  {
    title: "面积(㎡)",
    dataIndex: "area_",
    align: 'center',
    customRender: (text, record) => {
      return text.toFixed(2)
    }
  },
  {
    title: "PNL数",
    dataIndex: "pnL_",
    width: 65,
    align: 'center',
  },
  {
    title: "款数",
    dataIndex: "count_",
    width: 50,
    align: 'center',
  },
]
// 机台上订单
const columns4 = [
  {
    title: "本厂编码",
    dataIndex: "pdctno_",
    ellipsis: true,
    className:'orderClass',
    width: 120,
    align: 'center',
  },
  {
    title: "Pnl数",
    dataIndex:'',
    key: 'pnlQty_',
    width: 45,
    align: 'center',
    scopedSlots: { customRender: 'pnlQty_' },
  },
  {
    title: "分派时间",
    dataIndex: "sendDate_",
    width:100,
    ellipsis: true,
    align: 'center',
    // customRender: (text, record) => {
    //     var dt = new Date(text)

    //   //   yyyy-mm-dd
    //   var y = dt.getFullYear()
    //   var m = dt.getMonth() + 1
    //   if(m < 10 ) {
    //     m = '0' + m
    //   }
    //   var d = dt.getDate()
    //   if(d < 10 ) {
    //     d = '0' + d
    //   }
    //   var hh = dt.getHours()
    //   if(hh < 10 ) {
    //     hh = '0' + hh
    //   }
    //   var mm = dt.getMinutes()
    //   var ss = dt.getSeconds()
    //   return y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ":" + ss
    // }
  },
  // {
  //   title: "款数",
  //   dataIndex: "count4Hp_",
  //   width: 55,
  //   align: 'center',
  // },
  // {
  //   title: "长",
  //   dataIndex: "lSize_",
  //   width: 55,
  //   align: 'center',
  // },
  // {
  //   title: "宽",
  //   dataIndex: "wSize_",
  //   width: 35,
  //   align: 'center',
  // },
  // {
  //   title: "脚本错误",
  //   dataIndex: "message_",
  //   width: 80,
  //   align: 'center',
  // },
  // {
  //   title: "测试点数",
  //   dataIndex: "pnts_",
  //   width: 80,
  //   align: 'center',
  // },
  // {
  //   title: "流程错误",
  //   dataIndex: "flow4Message_",
  //   width: 80,
  //   align: 'center',
  // },
  {
    title: "操作",
    width: 60,
    align: 'center',
    // fixed: 'right',
    scopedSlots: { customRender: 'name' },
  },

]
export default {
  name:'CuttingCenter',
  props:  {
    tableData:{
      type: Array
    },
    table3Loading:{
      type: Boolean
    },
    machineStatuList:{
      type: Array
    },
    machineStatuLoad:{
      type: Boolean
    },
    drillLoad:{
      type: Boolean
    },
    dispatchList:{
      type: Array
    },
    dispatchLoad:{
      type: Boolean
    },
    idList:{
      type: Array
    }
  },
  data () {
    return {
      // 机台列表
      columns: [
        {
          title: "机台",
          dataIndex: "caption_",
          width: 65,
          align: 'center',
          fixed: 'left',
          customCell: (record, rowIndex) => {
            if(record.caption_1 == "1") {
              return { style: { 'background': '#595959', } }
            }
            else if(this.idList.indexOf(record.iD_) != -1) {
              return { style: { 'background': '#ff9900', } }
            }
          }
        },
        {
          title: "资料",
          dataIndex: "numMachine_",
          width: 40,
          align: 'center',
        },
        {
          title: "负责人",
          dataIndex: "listParams_",
          width: 60,
          align: 'center',
          // customCell: (record, rowIndex) => {
          //   if(record.nowPdc && record.nowPdc.indexOf('异常') != -1) {
          //     return { style: { 'background': 'red', } }
          //   }
          // },
        },
        {
          title: "工厂",
          dataIndex: "listName_",
          width:120,
          align: 'center',
        },
        {
          title: "品牌",
          dataIndex: "machineType_",
          width: 60,
          align: 'center',
        },
      ],
      columns2,
      columns4,
      showAssign:false,
      selectedRowKeys:[],
      selectedRowKeys1: [],
      selectedRowKeys2: [],
      rowId:'',
      rowId1:'',
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      menuVisible:false,
      menuData:[],
      statusText: "标记故障",
      rowId2:'',
    }
  },
  methods:{
    checkPermission,
    eventTouch(record,index) {
      return {
        props: {},
        on: { // 事件
          dblclick: () => { //双击
            this.showAssign = true
            this.$emit('getAssignmentList',record.iD_)
            this.rowId =  record.iD_
            // this.$emit('childClick')
          },
           click: () => {
            let rowKeys = this.selectedRowKeys;
            if (record.caption_1 !=1 && record.listParams_) {
              if (rowKeys.length > 0 && rowKeys.includes(record.iD_)) {
                rowKeys.splice(rowKeys.indexOf(record.iD_), 1);
              } else {
                rowKeys.push(record.iD_);
              }
            }
            this.selectedRowKeys = rowKeys;

          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          }
        }
      }
    },
    setRowClassName (record)  {
      return record.iD_ === this.rowId ? 'clickRowStyl' : ''
    },
    eventTouch1(record,index) {
      return {
        props: {},
        on: { // 事件
          click: () => {
            this.rowId2 = record.guid_;
          },

        }
      }
    },
    setRowClassName1 (record)  {
      return record.guid_ === this.rowId2? 'clickRowStyl1' : ''
    },
    //选择待分派机台
    onSelectChange(selectedRowKeys, selectedRows) {
      const newSelectedRowKeys=[], newSelectedRows=[];
      selectedRows.forEach(item => {
        if (item.caption_1 != 1) {
          newSelectedRowKeys.push(item.iD_)
          newSelectedRows.push(item)
        }
      })
      this.selectedRowKeys = newSelectedRowKeys;
      this.selectedRows = newSelectedRows
    },
    //分派回退
    onSelectChange1(record){
      console.log('record',this.rowId)
      this.rowId1 = record.guid_
      this.$emit('getDispatchFallback',{"guid_":this.rowId1, "iD_":this.rowId})
    },
    // 修改pnl数
    savePnl(record){
      console.log(record)
      let params ={
        "id": record.guid_,
        "qty": Number(record.pnlQty_)
      }
      up4CharacterPnlQty(params).then(res => {
        if (res.code == 1) {
          this.$message.success('修改成功')
        } else {
          this.$emit('getAssignmentList',this.rowId)
          this.$message.error(res.message)
        }
      })
    },
    // 设置故障机台不允许发单
    getCheckboxProps(record){
      return ({
        props: {
          disabled: record.caption_1 == 1
        },
      })
    },
    // 右键事件
    rightClick(e){
      if(this.menuData.caption_1 == 1) {
        this.statusText = '取消故障'
      } else {
        this.statusText = '标记故障'
      }
      this.menuVisible = true;
      this.menuStyle.top = e.clientY- 110 +  "px";
      this.menuStyle.left = e.clientX -
          document.getElementsByClassName('fixed-side')[0].offsetWidth -
          document.getElementsByClassName('left')[0].offsetWidth + "px";
          document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    // 编辑故障
    signError(){
      signMachineStatus(this.menuData.iD_).then(res => {
        if(res.code == 1) {
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
        this.$emit('getMesaList')
      })
    },
    // 批量修改
    BatchModification(){
      this.$emit('BatchModification')
    },


  }
}
</script>

<style lang="less" scoped>
.machine {
  height: 100%;
  display: flex;
  .left {
    width: 50%;
    height:779px;
    .minClass{
      /deep/.ant-table-body{
        min-height: 744px;
        // max-height:700px;
        // overflow-y: scroll;
      }
    }
    .tabRightClikBox {
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color:#000000
      }
      // .ant-menu-item {
      //   height: 28px;
      //   line-height: 28px;
      //   margin: 0;
      //   background: #ff9900;
      //   font-size: 14px;
      //   width: 100px;
      //   text-align: center;
      //   font-weight: 500;
      //   &:hover{
      //     color: rgba(0, 0, 0, 0.65);
      //   }
      // }
    }
  }
  .right {
    width: 50%;
    .bot,.top {
      /deep/ .ant-table-body {
        min-height: 0;
      }
    }
    .top {
      /deep/ .ant-table-wrapper .ant-table-thead tr th {
        padding: 5px 0
      }
    }
    button{
      padding:0  5px;
      border-radius: 6px;
      height: 24px;
      line-height: 24px;
      background-color: #ff9900;
      color:white;

    }
  }
  /deep/ .ant-table-tbody {
    .ant-table-row{
      td:first-child{
        user-select: all;
      }
    }
    .clickRowStyl {
      background: #fff9e6;
      //background:#aba5a5;
    }
    .clickRowStyl1 {
      background:#aba5a5;
    }
    .ant-input {
      padding: 0;
      border: 0;
      border-radius: 0;
      margin: -5px 0;
      text-align: center;
    }

  }


}


</style>
