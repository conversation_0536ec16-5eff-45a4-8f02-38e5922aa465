import { request, METHOD } from "@/utils/request";
export function pageList(params) {
  return request("/api/app/e-mime/page-list", METHOD.GET, params);
}

export function receiveEmail() {
  return request("/api/app/e-mime/receive-email", METHOD.POST);
}
export function eMimeOrder(params) {
  return request("/api/app/e-mime/order", METHOD.POST, params);
}
export function consumeorder(params) {
  return request("/api/app/e-mime/consume-order", METHOD.POST, params);
}
export function mailStatslist() {
  return request("/api/app/e-mime/fac-mail-stats-list", METHOD.GET);
}
//邮箱筛选下拉值
export function emaillist() {
  return request("/api/app/e-mime/email-list", METHOD.GET);
}
//邮件列表开始
export function emimestart(params) {
  return request("/api/app/e-mime/start", METHOD.POST, params);
}
//邮件回复
export function replymessage(params) {
  return request(`/api/app/e-mime/reply-message`, METHOD.POST, params);
}
//邮件列表获取附件
export function attList(uid) {
  return request(`/api/app/e-mime/att-list?uid=${uid}`, METHOD.GET);
}
//邮件列表下载附件地址
export function downloadbyattid(mailid, attid) {
  return request(`/api/app/e-mime/download-by-att-id?mailid=${mailid}&attid=${attid}`, METHOD.POST);
}
//根据附件创建订单
export function createorderbyatt(mailid, attid, custkind, memo) {
  return request(`api/app/e-mime/order-by-att?mailid=${mailid}&attid=${attid}&custkind=${custkind}&memo=${memo}`, METHOD.POST);
}
//获取邮件信息
export function emimeconent(mailid) {
  return request(`/api/app/e-mime/conent-v2?mailid=${mailid}`, METHOD.GET);
}
//创建订单最新接口
export function emimeorderv2(params) {
  return request(`/api/app/e-mime/order-v2`, METHOD.POST, params);
}
//更改客户代码获取下拉
export function emailcustno(mailbox) {
  return request(`/api/app/e-mime/email-cust-no?mailbox=${mailbox}`, METHOD.GET);
}
//更改客户代码
export function setmailcustno(mailid, custno) {
  return request(`/api/app/e-mime/set-mail-cust-no?mailid=${mailid}&custno=${custno}`, METHOD.POST);
}
//邮件列表文件替换
export function updatefilepro(mailid, attid) {
  return request(`/api/app/e-mime/update-file-pro?mailid=${mailid}&attid=${attid}`, METHOD.POST);
}
//邮件列表上传附件
export function uploadattachment(mailid, para4IntDelivery) {
  return request(`/api/app/e-mime/${mailid}/upload-attachment?para4IntDelivery=${para4IntDelivery}`, METHOD.POST);
}
//邮件列表下载附件
export function downloademail(mailid) {
  return request(`/api/app/e-mime/${mailid}/down-load-email`, METHOD.POST, { responseType: "blob" });
}
//邮件列表上传附件是否填写交期
export function byupdatedeliverydate(mailid) {
  return request(`/api/app/e-mime/pop-up-window-by-update-delivery-date?mailid=${mailid}`, METHOD.POST);
}
