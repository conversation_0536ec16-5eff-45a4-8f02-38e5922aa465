<template>
  <a-config-provider :locale="locale" :get-popup-container="popContainer">
    <router-view v-if="isRouterAlive" />
  </a-config-provider>
</template>

<script>
import axios from "axios";
// import  version from './version.json'
import { vueversion } from "@/services/analysis";
import { enquireScreen } from "./utils/util";
import { mapState, mapMutations } from "vuex";
import themeUtil from "@/utils/themeUtil";
import { getI18nKey } from "@/utils/routerUtil";
import Cookie from "js-cookie";
import * as signalR from "@microsoft/signalr";
import { setuserName } from "@/utils/request";
import { setAuthorization } from "@/utils/request";
import { getinformationremind } from "@/services/system/InformationRemind.js";
export default {
  name: "App",
  provide() {
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      locale: {},
      signalR: null,
      isRouterAlive: true,
    };
  },
  created() {
    this.setHtmlTitle();
    this.setLanguage(this.lang);
    enquireScreen(isMobile => this.setDevice(isMobile));
  },
  mounted() {
    this.chatTest();
    this.setWeekModeTheme(this.weekMode);
    window.addEventListener("click", this.keyclick);
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("click", this.keyclick, true);
    window.removeEventListener("resize", this.handleResize, true);
    this.signalR.off("ReceiveMessage");
    if (this.signalR) {
      this.signalR
        .stop()
        .then(() => {
          console.log("连接已断开");
        })
        .catch(err => {
          console.error("连接断开失败: ", err);
        });
    }
  },
  watch: {
    weekMode(val) {
      this.setWeekModeTheme(val);
    },
    lang(val) {
      this.setLanguage(val);
      this.setHtmlTitle();
    },
    $route() {
      this.setHtmlTitle();
      const resources = performance.getEntriesByType("resource");
      const jsFiles = resources.filter(r => r.initiatorType === "script" && r.name.endsWith(".js"));
      var timestamp = jsFiles[1].name.split("static/js")[1].split(".")[1];
      // axios({method: 'get',url: window.location.origin+ '/version.json'}).then(res  => {
      //     if(res.version != timestamp && window.location.href.indexOf('localhost')<0){
      //        window.location.reload()
      //     }
      //   })
      vueversion().then(res => {
        if (res.code) {
          if (res.data != timestamp && window.location.href.indexOf("localhost") < 0) {
            window.location.reload();
          }
        }
      });
    },
    "theme.mode": function (val) {
      let closeMessage = this.$message.loading(`您选择了主题模式 ${val}, 正在切换...`);
      themeUtil.changeThemeColor(this.theme.color, val).then(closeMessage);
    },
    "theme.color": function (val) {
      let closeMessage = this.$message.loading(`您选择了主题色 ${val}, 正在切换...`);
      themeUtil.changeThemeColor(val, this.theme.mode).then(closeMessage);
    },
    layout: function () {
      window.dispatchEvent(new Event("resize"));
    },
  },
  computed: {
    ...mapState("setting", ["layout", "theme", "weekMode", "lang"]),
    ...mapState("account", ["user"]),
  },
  methods: {
    keyclick() {
      const userName = Cookie.get("userName");
      //console.log('keyclick当前账号',this.user.userName,'cook账号',userName);
      if (!userName) {
        setuserName({
          token: this.user.userName,
        });
        console.log("11", Cookie.get("userName"));
      }
      if (window.location.href.indexOf("login") < 0 && userName && this.user.userName != userName) {
        this.$router.push("/login");
      }
      const token = Cookie.get("token");
      if (!token) {
        this.$router.push("/login");
        return;
      }
      setAuthorization({
        token: token,
        expireAt: new Date(new Date().getTime() + ********),
      });
      setTimeout(() => {
        if (document.querySelector(".ant-modal > div")) {
          document.querySelector(".ant-modal > div").removeAttribute("aria-hidden");
        }
      }, 1000);
    },
    ...mapMutations("setting", ["setDevice", "setinfolength"]),
    setWeekModeTheme(weekMode) {
      if (weekMode) {
        document.body.classList.add("week-mode");
      } else {
        document.body.classList.remove("week-mode");
      }
    },
    setLanguage(lang) {
      this.$i18n.locale = lang;
      switch (lang) {
        case "CN":
          this.locale = require("ant-design-vue/es/locale-provider/zh_CN").default;
          break;
        case "HK":
          this.locale = require("ant-design-vue/es/locale-provider/zh_TW").default;
          break;
        case "TH":
          this.locale = require("ant-design-vue/es/locale-provider/th_TH").default;
          break;
        case "US":
        default:
          this.locale = require("ant-design-vue/es/locale-provider/en_US").default;
          break;
      }
    },
    setHtmlTitle() {
      const route = this.$route;
      const key = route.path === "/" ? "home.name" : getI18nKey(route.matched[route.matched.length - 1].path);
      document.title = process.env.VUE_APP_NAME + " | " + this.$t(key);
    },
    popContainer() {
      return document.getElementById("popContainer");
    },
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    chatTest() {
      let hubUrl = process.env.VUE_APP_API_BASE_URL + "/signalr-hubs/chat";
      const connection = new signalR.HubConnectionBuilder()
        .withAutomaticReconnect() //断线自动重连
        .withUrl(hubUrl)
        .build();
      //启动
      this.signalR = connection;
      connection.start().catch(err => {
        //console.log("err",err);
      });
      //自动重连成功后的处理
      connection.onreconnected(connectionId => {
        //console.log(connectionId);
      });
      connection.on("ReceiveMessage", (key, message) => {
        if (key == this.user.userName) {
          this.message = message;
          if (this.message.indexOf("客服管理") > -1) {
            getinformationremind({}).then(res => {
              if (res.code) {
                this.setinfolength(res.data.length);
                localStorage.setItem("infolength", res.data.length);
              }
            });
          }
          this.openNotification("bottomRight");
        }
      });
    },
    openNotification(placement) {
      this.$notification.success({
        message: `通知`,
        description: this.message,
        placement,
      });
    },
    handleResize() {
      const className = ".ant-table-fixed-header .ant-table-scroll .ant-table-header";
      let headerList = document.querySelectorAll(className);
      const tableList = document.getElementsByClassName("ant-table-header");
      if (tableList.length == 0) {
        return;
      }
      var tableheaderHeight = document.getElementsByClassName("ant-table-header")[0].offsetHeight;
      const item = headerList[0];
      var itemMarginBottom = -17;
      var margin = Number((53 - tableheaderHeight).toFixed(2));
      let newMarginBottom = itemMarginBottom + margin + "px";
      const marginBottom = newMarginBottom;
      item.style.marginBottom = marginBottom;
    },
  },
};
</script>

<style lang="less">
.infomessage {
  color: #ff9900;
  .ant-modal-confirm-btns {
    margin-top: 10px !important;
  }
}
.tradeTypeSelect {
  width: 683px !important;
}
.publicSelect {
  .ant-select-dropdown-content {
    .ant-select-dropdown-menu {
      &::-webkit-scrollbar {
        //整体样式
        width: 6px; //y轴滚动条粗细
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        // background: #ff9900;
        background: #ff9900;
        // #fff9e6
      }
      &::-webkit-scrollbar-track {
        //轨道的样式
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #ffffff;
      }
    }
  }
}
#popContainer {
  --border-color: #000000;
  --hover-border-color: #ff9900;
  --border-width: 2.5px;
  --hover-border-width: 4px;
  --hover-size: 10px;
  .ant-popover {
    max-width: 800px;
  }
  .ant-calendar-header {
    div {
      display: flex;
      justify-content: space-between;
      a {
        position: static;
      }
    }

    .ant-calendar-prev-year-btn::before,
    .ant-calendar-prev-month-btn::before,
    .ant-calendar-next-year-btn::before,
    .ant-calendar-next-month-btn::before,
    .ant-calendar-prev-year-btn::after,
    .ant-calendar-prev-month-btn::after,
    .ant-calendar-next-year-btn::after,
    .ant-calendar-next-month-btn::after {
      border: 0 solid var(--border-color) !important;
      border-width: var(--border-width) 0 0 var(--border-width) !important;
    }

    .ant-calendar-prev-year-btn:hover::before,
    .ant-calendar-prev-month-btn:hover::before,
    .ant-calendar-next-year-btn:hover::before,
    .ant-calendar-next-month-btn:hover::before,
    .ant-calendar-prev-year-btn:hover::after,
    .ant-calendar-prev-month-btn:hover::after,
    .ant-calendar-next-year-btn:hover::after,
    .ant-calendar-next-month-btn:hover::after {
      border-color: var(--hover-border-color) !important;
      border-width: var(--hover-border-width) 0 0 var(--hover-border-width) !important;
      height: var(--hover-size);
      width: var(--hover-size);
    }
  }
}
</style>
