<template>
    <div class="view">
			<div id="centent">
				<table>
					<thead>
						<tr>
							<th>开始层</th>
							<th>结束层</th>
							<th>类型</th>
							<th>孔数</th>
							<th style="min-width:110px;">最小孔径(mm)</th>
							<th class="operation"><button type="button" @click="addLine()">加行</button></th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
			</div>
			<div id="foot">
				<button type="button" class="affirm" @click="submitData()">确认</button>
				<button type="button" @click="cancel()">取消</button>
			</div>
		</div>
</template>
<script>
    import {QWebChannel} from '@/utils/qwebchannel.js';
    import $ from 'jquery'    
    export default{
    name:'drillStructure',
    inject:['reload'],
    data(){
        return{   
            formSearchObj:{},
            isV3Version:false,      
        }
    }, 
    async created(){
        if(this.$route.query.data){
            this.formSearchObj =this.$route.query
        }
        if(this.$route.query.version == 'iPCB-V3'){
            this.isV3Version = true;
        }
        
    },
    async mounted(){
        this.$nextTick(()=>{
          new QWebChannel(qt.webChannelTransport, function(channel) {
            window.click_listener = channel.objects.click_listener;
            });
        } )
        window.vue = this 
        this.getData()
    },
    methods:{
        getData(){				
				// layui.config({version:true}).use(["layer"], function() {
				// 	layer = layui.layer;
				// })				
				$("#centent table").on("input","input[type='text'][data-verify]",function(e){
					if($(this).attr("data-verify").indexOf('integer') > -1){ //只能输入正整数
						this.value = this.value.replace(/\D/g,'');
					}else if($(this).attr("data-verify").indexOf('number') > -1){ //只能输入数字
						this.value = this.value.replace(/[^\d.]/g,""); //只能数字或.
						this.value = this.value.replace(".","$#$").replace(/\./g,"").replace("$#$","."); //.只能出现一次
					}
				})
				$("#centent table").on("change","select",function(){
					this.blur(); //ie中select选择后会有背景颜色，需要手动失去焦点
				})				
				//console.error('formSearchObj',this.formSearchObj) 
				if(this.formSearchObj && this.formSearchObj.data){ //调用时带了数据
					var data = JSON.parse(this.formSearchObj.data);              
                    //console.log('data',data,typeof(data))
					for(var i = 0; i < data.length; i++){
						this.addLine(function(newTr){
                            //console.log('循环',i,data.length)
							newTr.find("[name]").each(function(){
								this.value = data[i][this.name] != null && data[i][this.name] != undefined ? data[i][this.name] : '';
							})
						})
					}
				}
				
				//需要加最小钻咀直径
				if(this.formSearchObj && this.formSearchObj.isMinDrillToolDia){
					$("#centent table>thead th.operation").before('<th style="min-width:130px;">最小钻咀直径(mm)</th>')
				}
			},
        addLine(callBack){
            //console.log('新增')
            var newTr = $(
                '<tr>'+
                    '<td>'+
                        '<input type="text" name="FromLayer" autocomplete="off" data-verify="number,integer" />'+
                    '</td>'+
                    '<td>'+
                        '<input type="text" name="ToLayer" autocomplete="off" data-verify="number,integer" />'+
                    '</td>'+
                    '<td>'+
                        '<select name="IsLaser">'+
                            '<option value="0">通孔</option>'+
                            '<option value="1">激光</option>'+
                            '<option value="2">盲孔</option>'+
                            '<option value="3">埋孔</option>'+
                        '</select>'+
                    '</td>'+
                    '<td>'+
                        '<input type="text" name="HoleCount" autocomplete="off" data-verify="number,integer" />'+
                    '</td>'+
                    '<td>'+
                        '<input type="text" name="MinHoleSize" autocomplete="off" data-verify="number" />'+
                    '</td>'+
                    
                    (this.formSearchObj && this.formSearchObj.isMinDrillToolDia ? 
                        '<td>'+
                            '<input type="text" name="MinDrillToolDia" autocomplete="off" data-verify="number" />'+
                        '</td>'
                        : ''
                    )+
                    
                    '<td>'+
                        '<button type="button" onclick="vue.removeLine(this)">删行</button>'+
                    '</td>'+
                '</tr>'
            )
            $("#centent table tbody").append(newTr);
            
            if(callBack){
                callBack(newTr);
            }else{
                newTr.find("input[name='FromLayer']").focus();
            }
            newTr = null;
        },        
        removeLine(_this){
            $(_this).parents("tr").remove();
        },        
        submitData(){
            var arr = [];
            var trArr = $("#centent table tbody tr");
            for(var i = 0; i < trArr.length; i++){
                var obj = {
                    index: i + 1
                }
                var tr = $(trArr[i]);
                var nameArr = tr.find("[name]");
                for(var j = 0; j < nameArr.length; j ++){
                    var _this = nameArr[j];
                    _this.value = $.trim(_this.value);
                    if(this.formSearchObj && this.formSearchObj.required){ //需要验证必填时
                        if(_this.nodeName == 'INPUT' && (_this.value === '' || _this.value === '0')){
                            _this.focus();
                            layer.tips("不能为空或0!",_this,{tips:[3,"#d9534f"]});
                            return false;
                        }
                    }
                    
                    obj[_this.name] = _this.value;
                }
                
                arr.push(obj);
                //console.log('arr',arr)
            }   
            if(this.isV3Version){
                if (window.click_listener) {
                    // 发送数据到 C++
                    window.click_listener.DrillStructOK(arr.length > 0 ? JSON.stringify(arr) : '');
                } else {
                    ////console.error('Qt WebEngine 未初始化，钻孔确定按钮');
                }   
            }else{
                window.external.DrillStructOK(arr.length > 0 ? JSON.stringify(arr) : '');
            }     
            
        },
        cancel(){
            if(this.isV3Version){
                if (window.click_listener) {
                    // 发送数据到 C++
                    window.click_listener.DrillStructCancel();
                } else {
                   // //console.error('Qt WebEngine 未初始化，钻孔确定按钮');
                } 
            }else{
                window.external.DrillStructCancel()
            }
        }
    }
}
</script>
<style lang="less" scoped>
 .view {
    width: 100%;
    height: 100%;
    background-color: #F0F0F0;
    // font-family: SimSun,NSimSun,FangSong,Microsoft YaHei !important;
    //font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;
    font: 13px Helvetica Neue,Helvetica,PingFang SC,Tahoma,Microsoft YaHei,Arial,sans-serif;
}
#centent {
    position: fixed;
    top: 0;
    bottom: 60px;
    left: 0;
    right: 0;
    padding: 10px 15px 5px;
    overflow: auto;
}
#centent table {
    border-collapse: collapse;
    border-spacing: 0;
    margin: 0 auto;
    width: 100%;
}
#centent table th {
    border: none;
    background-color: #F0F0F0 !important;
    min-width: 70px;
    padding: 4px 8px;
    text-align: center;
}
/deep/#centent table td{
    border: none;
    background-color: #F0F0F0 !important;
    min-width: 70px;
    padding: 4px 8px;
    text-align: center;
}

/deep/#centent table input {
    width: 100%;
    font-size: 12px;
    color: #000;
    border: 1px solid #ABADB3;
    vertical-align: middle;
    padding: 4px 0 4px 5px;
    box-sizing: border-box;
    outline: none;
}
/deep/#centent table select {
    width: 100%;
    font-size: 12px;
    color: #000;
    border: 1px solid #ABADB3;
    vertical-align: middle;
    padding: 2px 0 4px 2px;
    box-sizing: border-box;
    outline: none;
}
/deep/#centent table button {
    font-size: 12px;
    color: #000;
    padding: 3px 15px;
    border: 1px solid #ABADB3;
    border-radius: 2px;
    background-color: #fff;
    cursor: pointer;
}
#foot {
    width: 100%;
    height: 50px;
    padding: 10px 30px;
    position: fixed;
    bottom: 5px;
    left: 0;
    right: 0;
    text-align: right;
    box-sizing: border-box;
    border-top: 1px solid #d2d2d2;
}
#foot button.affirm {
    background-color: #0092ff;
    border-color: #0092ff;
    color: #fff;
}
#foot button {
    font-size: 12px;
    color: #333;
    padding: 5px 15px;
    background-color: #fff;
    border: 1px solid #ABADB3;
    margin: 0 0 0 15px;
    cursor: pointer;
    border-radius: 2px;
}
#foot button {
    font-size: 12px;
    color: #333;
    padding: 5px 15px;
    background-color: #fff;
    border: 1px solid #ABADB3;
    margin: 0 0 0 15px;
    cursor: pointer;
    border-radius: 2px;
}
</style>