<!--生产管理- 工单合卡 -->
<template>
    <a-spin :spinning="spinning">
        <div class="projectCard">
          <div class="content">           
            <a-input style="width:150px;margin-right:0.5%;" placeholder="管制卡编号" v-model="formData.CardNo"  allowClear @keyup.enter.native="searchClick"></a-input>  
            <a-input style="width:150px;margin-right:0.5%;" placeholder="拼版订单编号" v-model="formData.OrderNo" allowClear @keyup.enter.native="searchClick"></a-input>
            <!-- <a-select  style="width:6%;margin-right:0.5%;" v-model="formData.Status">
                <a-select-option :key="0" > 请选择状态</a-select-option>
                  <a-select-option :key="10" > 生产中</a-select-option>
                  <a-select-option :key="20" > 完成</a-select-option>
                  <a-select-option :key="30" > 作废</a-select-option>
                  <a-select-option :key="40" > 暂停</a-select-option>
                  <a-select-option :key="50" > 待审核</a-select-option>
            </a-select>             -->
              <!-- <a-date-picker  
                  style="margin-right:0.5%;"              
                  format="YYYY-MM-DD HH:mm:ss"                
                  :showTime="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"                
                  placeholder="开始时间(投料时间)"
                  @change="onChange1"
                 
              />
              <a-date-picker  
                  style="margin-right:0.5%;"              
                  format="YYYY-MM-DD HH:mm:ss"                
                  :showTime="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"                
                  placeholder="结束时间(投料时间)"
                  @change="onChange2"
                  
              />      -->
              <a-button type="primary" @click="searchClick" style="margin-right:0.5%;">搜索</a-button>
              <a-button type="primary" @click="mergeClick" style="margin-right:0.5%;">合卡</a-button>
              <!-- <a-button type="primary" @click="delClick" style="margin-right:0.5%;">删除过序</a-button> -->
          </div>
          <div class="leftContent">
            <a-table 
              :columns="columns" 
              :dataSource="cardListData" 
              :customRow="onClickRow"
              :pagination="pagination" 
              :rowKey="'id'"  
              :scroll="{y:704,x:1000}"          
              :loading="cardListTableLoading"
              @change="handleTableChange"
              :rowClassName="isRedRow"
              class="maintable"
            >
              <span slot="num" slot-scope="text, record, index" >
                {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
              </span>
              <template slot="orderNo" slot-scope="record">
                <a  style="color: #428bca" @click.stop="details(record)">{{record.orderNo}} </a>
              </template>
              <template slot="cardNo" slot-scope="record">
                <a  style="color: #428bca" @click.stop="CarDetails(record)">{{record.cardNo}} </a>
              </template>                     
              <template slot="action" slot-scope="" >             
                <p style="color:dodgerblue;" >异常警示</p>
                <p style="color:dodgerblue;" >表面处理~外形</p>
                <p style="color:dodgerblue;" >表面处理~飞针</p>
                <p style="color:dodgerblue;" >飞针~外形</p>
                <p style="color:dodgerblue;" >表面处理~测试架</p>
                <p style="color:dodgerblue;" >外形~测试架</p>
              </template>             
              <template slot="currentStatus" slot-scope="record">
                <span  >{{record.statusStr}} {{record.isPrint == 0 ? '/未打印' : '/已打印'}} </span> 
              </template>
              <template slot="currentNum" slot-scope="record">
                <span  >{{record.currentNum === null ? 0 : record.currentNum }} {{'/' + record.num}} </span> 
              </template>
              <template slot="deliveryType" slot-scope="record" >
                <span >
                  <p>{{record.deliveryType.indexOf("小时") != -1 ? record.deliveryType : record.deliveryType + "天"}} </p>
                </span>
              </template>               
            </a-table>
          </div>
          <div class="bto"></div>
      </div>
    </a-spin>
</template>
<script>
import moment from "moment";
import {mapState,} from 'vuex';
import {
  proCardInfoList,
  deleteStep,
  mergeCardList,
  mergeCard,
} from "@/services/scgl/OrderManagement/ControlCard"
const columns = [
  {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: "center",    
      scopedSlots: { customRender: 'num' },
      width: 45,
    },
    {
      title: "管制卡编号",
      scopedSlots: { customRender: 'cardNo' },   
      className:'userStyle',
      align: "left",
      width: 100,
    },
    {
      title: "拼版编号",
      align: "left",
      width: 120,
      className:'userStyle',
      scopedSlots: { customRender: 'orderNo' },  
    },
    {
      title: "交期类型",
      width: 100,
      align: "left",
      scopedSlots:{customRender:'deliveryType'}
    },
    {
      title: "投料时间",
      dataIndex:"createTime",
      width: 120,
      align: "left",
    }, 
    {
      title: "投料人员",
      dataIndex: "adminName",
      width: 100,
      align: "left",
    },
    {
      title: "特殊需求",
      dataIndex: "remark",
      align: "left",
      width: 120,
    },
  ]
export default{
    name:'',
    inject:['reload'],
    data(){
        return{
          spinning:false,
          formData:{
              // Status:0,
              CardNo:"",
              OrderNo:"",             
              // StartCreateTime:"",
              // EndCreateTime:"",
          },
          columns,
          cardListTableLoading:false,
          pagination: {
            pageSize: 20,
            pageIndex: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
          },
          cardListData:[],
          cardNo:'',
        }
    },
    created(){
      this.$nextTick(()=>{
        this.handleResize()
      })
    },
    mounted(){
      window.addEventListener('resize', this.handleResize, true)
    },
    beforeDestroy(){
    window.removeEventListener('resize', this.handleResize);
   },
    computed: {
    ...mapState('account', ['user',]),  
  }, 
  methods: {
    moment,
    handleResize(){
      var leftstyle = document.getElementsByClassName('maintable')[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var leftContent = document.getElementsByClassName('leftContent')[0]
      if(leftstyle && this.cardListData.length!=0){
        leftstyle.style.height =  window.innerHeight - 214 +'px'
      }else{
        leftstyle.style.height = 0
      }
      if(window.innerHeight<=911 ){
        leftContent.style.height = window.innerHeight - 174 +'px'
      }else{
        leftContent.style.height = '726px'
      }  
      var footerwidth =  window.innerWidth-224
      var paginnum = ''
        if(Math.ceil(this.pagination.total/20)>10){
          paginnum = 7
        }else{
          paginnum = Math.ceil(this.pagination.total/20)
        }
      if((paginnum*50)+310<footerwidth ){
        this.pagination.simple=false
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
      }
      if(window.innerWidth < 1920){
        if(((paginnum*50)+310)<footerwidth){
        this.pagination.simple=false
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
      }else{
        this.pagination.simple=false
        this.pagination.size = 'small'
        this.pagination.showSizeChanger = false
        this.pagination.showQuickJumper = false
      } 
        if((paginnum*25)< window.innerWidth-150  && window.innerWidth>766){
          if(footerwidth < (paginnum*25) +200){
            this.pagination.simple=true
          }else{
            this.pagination.simple=false
          }
        }else{
          if(window.innerWidth>766){
            if(footerwidth < paginnum*45){
            this.pagination.simple=true
            }else{
              this.pagination.simple=false
            }
          }else{
            this.pagination.simple=true
           }
          }
      }else{
       if(window.innerWidth > 1920 ){
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
        this.pagination.simple=false
      }
     }
    },
    onChange1 (value, dateString) {
      this.formData.StartCreateTime = dateString        
    },
    // onOk1 (value) {
    //     console.log('onOk: ', value)
    // },
    onChange2 (value, dateString) {
      this.formData.EndCreateTime = dateString
    },
    // onOk2 (value) {
    //     console.log('onOk: ', value)
    // },  
    // 获取订单
    getOrderList(queryData){
      // let params = {
      //   'PageIndex': this.pagination.current,
      //   'PageSize' : this.pagination.pageSize,
      // }
      let params = {
        ...this.pagination,
      }
      var obj = Object.assign(params,queryData)
      this.cardListTableLoading = true;
      mergeCardList (obj).then(res => {
        if (res.code) {
          this.cardListData = res.data.items;
          this.pagination.total = res.data.totalCount;
          setTimeout(() => {
            this.handleResize()
          },0)
        }
      }).finally(()=> {
        this.cardListTableLoading = false;
      })
    },
    isRedRow(record) {
      let strGroup = []      
      if (record.cardNo && record.cardNo == this.cardNo) {
        strGroup.push('rowBackgroundColor')
      }      
      return strGroup
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.cardNo);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record           
            this.cardNo = record.cardNo
          },
        }
      }
    },    
    // handleTableChange(pagination) {
    //   this.pagination.current=pagination.current
    //   this.getOrderList()
    // }, 
    handleTableChange(pagination, ) {
      this.pagination.pageIndex=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      if(JSON.stringify(this.queryData) !='{}'){
          this.getdataList(this.queryData)
        }else{
            this.getdataList();
       }
    },  
    searchClick(){
      let params = this.formData   
      var arr1 = params.OrderNo.split('')
      if(arr1.length >30){
        arr1 = arr1.slice(0,30)
      }
      params.OrderNo = arr1.join('')
      var arr2 = params.CardNo.split('')
      if(arr2.length >50){
        arr2 = arr2.slice(0,50)
      }
      params.CardNo = arr2.join('')  
      // params.Status = Number(this.formData.Status)
      this.getOrderList(params)
      this.pagination.pageIndex = 1;
      console.log('搜索',this.formData,params)
    }, 
    // 删除过序
    delClick(){
      if(!this.cardNo){
        this.$message.warning('请选择工序')
        return
      }
      let params = {
        "cardNo": this.cardNo,
        "account": this.user.userName,
        "name": this.user.name,
      }
      if (confirm('确认删除过序？')) {
        this.spinning = true
        deleteStep(params).then(res => {
          if (res.code) {
            this.$message.success('已删除')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })
      }

    },
    // 合卡
    mergeClick(){
      if(!this.cardNo){
        this.$message.warning('请选择订单')
        return
      }
      mergeCard(this.cardNo).then(res=>{
        if(res.code){
          this.$message.success('合卡成功')
          this.getOrderList(this.formData)
        }else{
          this.$message.error(res.message)
        }
      })
    },
     // 订单详情跳转
     details(record){
      this.$router.push({path:'orderDetail1',query:{ id:record.orderNo, } ,})
    },
     // 管制卡详情跳转
     CarDetails(record){
      console.log('121',record)
      this.$router.push({path:'cardDetail',query:{id:record.id} ,})
    },                  
  }
}
</script>
<style scoped lang="less">
/deep/.ant-table .ant-table-tbody > tr > td {
    height: 34px;
}
/deep/.ant-input{
  font-weight: 500;
}
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
  .content{
    height:42px;
    padding-left:6px;
    background: #FFFFFF;
  }
 /deep/ .ant-input,.ant-select{
    margin-right:0.5%;
    margin-top:6px;
  }
.projectCard {
  padding-top:10px;
  /deep/.userStyle{
    user-select: all!important;
  }
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  .leftContent {
    background: #FFFFFF; 
    position: relative;
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
  }
  .bto{
  height:41px;
  background: #FFFFFF;
  border:2px solid #E9E9F0;
  border-top: none;
}
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    position: absolute;
    margin: 10px 0 0 10px;
  }

  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table-thead > tr > th {
    // padding:3px 0!important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table{
    .ant-table-thead > tr > th{
      padding: 3px 4px;
      // border-top:1px  solid #efefef;     
      border-right:1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 3px 4px!important;
      border-right:1px solid #efefef;
    }
    tr.ant-table-row-selected td {
     background:rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}
</style>

