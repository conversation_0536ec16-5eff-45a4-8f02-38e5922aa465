<!-- 市场管理 - 订单报价- 诚瑞销售合同 -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj" type="primary" class="printstyle" @click="printpdf">打印</a-button>
    <div id="pdfDomcr" style="font-size: 12px; font-family: 'Courier New', Courier, monospace; color: black; font-weight: 600">
      <div style="width: 100%; font-size: 30px; font-weight: bold; padding-bottom: 10px">
        <div style="display: flex; text-align: center; padding-left: 15%">
          <img src="@/assets/img/crtitle.png" style="position: relative; right: 15px; height: 80px" />
          <div>
            <div>诚瑞电路有限公司</div>
            <div style="font-size: 20px">CHEERISE CIRCUIT CO.,LTD</div>
            <div>销售合同</div>
          </div>
          <div style="font-size: 12px; position: absolute; right: 25px">合同编号:{{ CRsalesdata.value_1 }}</div>
        </div>
      </div>
      <div style="display: flex; padding: 0 30px">
        <div style="width: 50%; z-index: 99">
          <div>需方(甲方):{{ CRsalesdata.value_2 }}</div>
          <div>地址:{{ CRsalesdata.value_3 }}</div>
          <div>手机:{{ CRsalesdata.value_4 }}</div>
          <div>联系人:{{ CRsalesdata.value_5 }}</div>
          <div>签约日期:{{ CRsalesdata.value_6 }}</div>
          <div>经供需双方同意成交下列产品，订立条款如下:</div>
        </div>
        <div style="z-index: 99; width: 50%">
          <div style="float: right">
            <div>供方(乙方):{{ CRsalesdata.value_7 }}</div>
            <div>地址:{{ CRsalesdata.value_8 }}</div>
            <div>手机:{{ CRsalesdata.value_9 }}</div>
            <div>联系人:{{ CRsalesdata.value_10 }}</div>
            <div>交货日期:{{ CRsalesdata.value_6 }}</div>
            <div style="display: flex; justify-content: space-between">
              合计方式:{{ CRsalesdata.value_11 }}
              <div @click="addcontract" style="color: #4b82ac; cursor: pointer" v-if="showadd && act != 'dis'">
                点击添加 <a-icon style="font-size: 16px; top: 2px; position: relative" type="plus-circle"></a-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="position: relative">
        <table border="1" style="text-align: center; width: 100%; z-index: 99; position: relative">
          <thead>
            <tr>
              <td rowspan="2">序号</td>
              <td rowspan="2">生产编号</td>
              <td rowspan="2">客户型号</td>
              <td rowspan="2">规格(MM)</td>
              <td rowspan="2">拼版</td>
              <td rowspan="2">材质</td>
              <td rowspan="2">板厚</td>
              <td rowspan="2">层数</td>
              <td colspan="5">工艺参数</td>
              <td rowspan="2">数量(PCS)</td>
              <td rowspan="2">单价(RMB/PCS)</td>
              <td rowspan="2">工程治具费(RMB)</td>
              <td rowspan="2">样品费</td>
              <td rowspan="2">金额合计</td>
              <td rowspan="2">备注</td>
              <td rowspan="2" style="width: 40px" v-if="showadd && act != 'dis'">操作</td>
            </tr>
            <tr>
              <td>表面工艺</td>
              <td>过孔工艺</td>
              <td>阻焊</td>
              <td>文字</td>
              <td>铜厚</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in CRsalesdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.price1 }}</td>
              <td>{{ item.price2 }}</td>
              <td>{{ item.price3 }}</td>
              <td>{{ item.price4 }}</td>
              <td>{{ item.price5 }}</td>
              <td>{{ item.price6 }}</td>
              <td>{{ item.price7 }}</td>
              <td>{{ item.price8 }}</td>
              <td>{{ item.price9 }}</td>
              <td>{{ item.price10 }}</td>
              <td>{{ item.price11 }}</td>
              <td>{{ item.price12 }}</td>
              <td>{{ item.price13 }}</td>
              <td>{{ item.price14 }}</td>
              <td>{{ item.price15 }}</td>
              <td>{{ item.price16 }}</td>
              <td>{{ item.price17 }}</td>
              <td>{{ item.price18 }}</td>
              <td v-if="showadd && act != 'dis'">
                <a-tooltip placement="top" title="删除当前行数据">
                  <a-icon @click="delclick(item.id)" style="font-size: 14px; color: #4b82ac" type="close-circle"></a-icon>
                </a-tooltip>
              </td>
            </tr>
            <tr>
              <td style="text-align: right" colspan="6">总金额（大写）人民币：</td>
              <td colspan="6" style="text-align: left">{{ convertToChineseNum(amountto) }}</td>
              <td style="text-align: right" colspan="3">合计:</td>
              <td style="text-align: left">{{ amountto }}</td>
            </tr>
            <tr>
              <td colspan="20" style="text-align: left; padding-left: 10px">
                说明:<br />
                1、此合同一经签字（盖章）即日生效，本公司不承担定购前及合约外的定购要求 ，也不接受需方减少或取消合同的要求
                ，如因此造成我司损失，则由需方承担。<br />
                2、加工要求以需方提供资料为准生产，如有特殊要求需有书面说明。如需方在生产过程中需要更改资料，造成的相关损失由需方承担。<br />
                3、验收方式：按PCB行业标准(IPC-600F),如有问题：样板请在七天内,批量板请在十天以内以书面形式通知供方,如超过时限,视为需方同意接收。<br />
                4、因供方产品质量原因造成需方产品损失,在双方确认后,按照行业规则,供方的赔偿以不超过电路板自身价值为最高额度。<br />
                5、本合同一式两份， 供、需双方各执一份，自供、需双方签名（盖章）之日起生效自交货之日为止，传真件同具法律效力。
              </td>
            </tr>
            <tr>
              <td colspan="10" style="text-align: left; padding-left: 10px">需方单位名称（盖章）：{{ CRsalesdata.value_2 }}</td>
              <td colspan="10" style="text-align: left; padding-left: 10px">供方单位名称（盖章）：{{ CRsalesdata.value_7 }}</td>
            </tr>
            <tr>
              <td colspan="10" style="text-align: left; padding-left: 10px">日期：{{ CRsalesdata.value_6 }}</td>
              <td colspan="10" style="text-align: left; padding-left: 10px">日期：{{ CRsalesdata.value_6 }}</td>
            </tr>
          </tbody>
        </table>
        <img src="@/assets/img/crhtz.png" style="z-index: 0; display: block; width: 150px; position: absolute; bottom: 0px; right: 10%" />
      </div>
    </div>
    <a-modal title="合同数据添加" :visible="modalvisible" @ok="handleOk" @cancel="handleCancel" centered :width="800">
      <a-row>
        <a-col :span="10">
          <a-form-item label="订单号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入订单号进行查询" v-model="OrderNo" @keyup.enter="queryclick" :auto-focus="true" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="客户型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入客户型号进行查询" v-model="PcbFileName" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" style="margin-top: 2px" @click="queryclick">查询</a-button>
        </a-col>
      </a-row>
      <a-table
        :columns="columns"
        :row-selection="{ selectedRowKeys: selectedRowKeysind, onChange: onSelectChange, columnWidth: 25 }"
        :pagination="false"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ y: 300 }"
        :dataSource="datasource"
        :rowClassName="isRedRow"
      >
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import { verifyPageList, deletecontractno, contractNo } from "@/services/mkt/OrderReview.js";
import htmlToPdf from "@/utils/htmlToPdfa3";
export default {
  name: "ReportInfoyxd",
  props: ["CRsalesdata", "joinFactoryId", "salescustno", "ContractNoSech", "ttype", "act"],
  computed: {},
  data() {
    return {
      printObj: {
        id: "pdfDomcr", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      selectedRowKeys: [],
      showadd: true,
      selectedRowKeysind: [],
      PcbFileName: "",
      OrderNo: "",
      datasource: [],
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 35,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "订单号",
          align: "left",
          ellipsis: true,
          width: 80,
          dataIndex: "orderNo",
        },
        {
          title: "客户代码",
          dataIndex: "custNo",
          align: "left",
          ellipsis: true,
          width: 50,
        },

        {
          title: "订单类型",
          dataIndex: "reOrder",
          align: "left",
          ellipsis: true,
          width: 50,
          customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
        },
      ],
      amountto: 0,
      modalvisible: false,
      ids: [],
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.amountto = 0;
    this.ids = [];
    this.CRsalesdata.price.forEach(item => {
      if (this.ids.indexOf(item.id) == -1) {
        this.ids.push(item.id);
      }
    });
    for (let index = 0; index < this.CRsalesdata.price.length; index++) {
      if (this.CRsalesdata.price[index].price17 && this.CRsalesdata.price[index].price17 != "/") {
        this.amountto += Number(this.CRsalesdata.price[index].price17);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
    this.showadd = !this.CRsalesdata.price.some(ite => ite.status == 30);
  },
  methods: {
    convertToChineseNum,
    getsalesPdf() {
      this.showadd = false;
      setTimeout(() => {
        htmlToPdf("pdfDomcr", this.CRsalesdata.pcbFileName);
        this.showadd = !this.CRsalesdata.price.some(ite => ite.status == 30);
      }, 500);
    },
    closePrintTool() {
      document.title = this.ttype;
      this.showadd = !this.CRsalesdata.price.some(ite => ite.status == 30);
    },
    printpdf() {
      document.title = this.CRsalesdata.pcbFileName;
      this.showadd = false;
    },
    delclick(id) {
      this.ids = [];
      this.CRsalesdata.price.forEach(item => {
        if (this.ids.indexOf(item.id) == -1) {
          this.ids.push(item.id);
        }
      });
      deletecontractno(id).then(res => {
        if (res.code) {
          this.ids.forEach(item => {
            if (item == id) {
              this.ids.splice(this.ids.indexOf(item), 1);
            }
          });
          this.$message.success("删除成功");
          this.$emit("LTsalescontract", this.ids, "CR");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeys = rowKeys;
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysind = selectedRowKeys;
      this.selectedRowKeys = [];
      selectedRows.forEach(item => {
        this.selectedRowKeys.push(item.id);
      });
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    queryclick() {
      if (!this.OrderNo && !this.PcbFileName) {
        this.$message.error("请输入订单号或客户型号进行查询");
        return;
      }
      let params = {
        PageIndex: 1,
        PageSize: 20,
        CustNo: this.salescustno,
        ContractNoSech: this.ContractNoSech,
      };
      if (this.OrderNo) {
        params.OrderNo = this.OrderNo;
      }
      if (this.PcbFileName) {
        params.PcbFileName = this.PcbFileName;
      }
      verifyPageList(params).then(res => {
        if (res.code) {
          this.datasource = res.data.items;
          if (this.datasource.length == 0) {
            this.$message.error("未查询到相关订单");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    addcontract() {
      this.modalvisible = true;
      this.selectedRowKeys = [];
      this.datasource = [];
      this.OrderNo = "";
      this.PcbFileName = "";
      this.selectedRowKeysind = [];
    },
    handleOk() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择订单");
        return;
      }
      this.ids = [...this.ids, ...this.selectedRowKeys];
      contractNo(this.ids).then(res => {
        if (res.code) {
          this.$emit("LTsalescontract", this.ids, "CR");
        } else {
          this.$message.error(res.message);
        }
      });
      this.modalvisible = false;
    },
    handleCancel() {
      this.modalvisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.agreement {
  padding-bottom: 20px;
  position: relative;
  z-index: 99;
  div {
    padding-top: 10px;
  }
}
.tableclass {
  border: 1px solid black;
  margin-top: 10px;
  margin-bottom: 10px;
  td {
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding-left: 7px;
  }
  tbody {
    tr {
      height: 24px;
    }
  }
}
.rowBackgroundColor {
  background: #dcdcdc !important;
}
.ant-table-row-selected {
  background: #dcdcdc !important;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-table-tbody > tr.ant-table-row-selected td {
  background: #dcdcdc;
}
/deep/.ant-table {
  border: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-thead > tr > th {
  padding: 4px 4px;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 4px 4px !important;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
  color: #000000;
}
.pdfDom1 {
  font-size: 12px;
  padding: 25px;
  height: 650px;
  overflow: auto;
}
</style>
