<!-- 工程管理 - 生产工具 -订单列表 左边 -->
<template>
  <div ref="tableWrapper">
    <div>
      <a-table
        :columns="columns"
        :loading="orderListTableLoading"
        :dataSource="dataSource"
        :scroll="{ y: 737, x: 1016 }"
        :pagination="pagination"
        :customRow="customRow"
        :rowClassName="Clickoncolor"
        :rowKey="rowKey"
        @change="handleTableChange"
      >
        <span slot="ThickCu" slot-scope="text, record">
          <a-checkbox :checked="record.thickCu != '' ? true : false" />
        </span>
        <span slot="num" slot-scope="text, record, index">
          {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
        </span>
        <span slot="fileName" slot-scope="text, record" style="color: #0000cc" @click="FileDownload(record)" v-if="record.exportFile_">
          <span style="cursor: pointer">下载</span>
        </span>
        <span slot="inputFile_" slot-scope="text, record" style="color: #0000cc" @click="inputFileDownload(record)">
          <span style="cursor: pointer">下载</span>
        </span>
        <span slot="type_" slot-scope="text, record">
          <span v-if="record.type_ == '13'"
            >{{ record.typeStr_ }}
            <span style="color: #ff9900"><a-icon @click="drillbit(record)" type="download"></a-icon></span>
          </span>
          <span v-else>{{ record.typeStr_ }}</span>
        </span>
        <div slot="orderNo_" slot-scope="text, record">
          <a style="color: black" :title="record.orderNo_">{{ record.orderNo_ }}</a
          >&nbsp;
          <span class="tagNum" style="display: inline-block; height: 19px">
            <span>
              <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
                <span
                  class="noCopy"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    color: #ff9900;
                    padding: 0;
                    display: inline-block;
                    height: 19px;
                    width: 18px;
                    margin-left: -10px;
                    user-select: none;
                  "
                  ><a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
                <span
                  class="noCopy"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    color: #ff9900;
                    padding: 0;
                    display: inline-block;
                    height: 19px;
                    width: 18px;
                    margin-left: -10px;
                    user-select: none;
                  "
                  ><a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
              </a-tooltip>
              <a-tooltip title="加急" v-else-if="record.isJiaji">
                <span
                  class="noCopy"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    color: #ff9900;
                    padding: 0;
                    display: inline-block;
                    height: 19px;
                    width: 18px;
                    margin-left: -10px;
                    user-select: none;
                  "
                  ><a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
              </a-tooltip>
              <a-tooltip title="新客户" v-if="record.isNewCust">
                <a-tag
                  class="noCopy"
                  style="
                    font-size: 12px;
                    background: #428bca;
                    color: white;
                    padding: 0 2px;
                    margin: 0;
                    margin-right: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid #428bca;
                  "
                >
                  新
                </a-tag>
              </a-tooltip>
            </span>
          </span>
        </div>
      </a-table>
    </div>
    <!-- <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
      <a-menu-item @click="down1" v-if="showText">复制</a-menu-item>
    </a-menu>  -->
  </div>
</template>
<script>
import { checkPermission } from "@/utils/abp";
import { downloaddrillfile } from "@/services/ToolofProduction";
export default {
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    columns: {
      type: Array,
      require: true,
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            pageIndex: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
    isDrillPeiDao: {
      type: Boolean,
    },
  },
  name: "LeftTable",
  data() {
    return {
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      showText: false,
      selectedRowKeys: [],
      slectedRows: [],
      selectedRowsData: [],
      shiftKey: false,
      startIndex: -1,
      isDragging: false,
    };
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < val.length; i++) {
              let obj2 = val[i].orderNo_;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0 && longestChars.length > 12) {
              this.columns[1].width = 120 + (longestChars.length - 12) * 10 + "px";
              this.columns[7].width = 160 - (longestChars.length - 12) * 3 + "px";
              if (this.isDrillPeiDao) {
                this.columns[11].width = 120 - (longestChars.length - 12) * 3 + "px";
              } else {
                this.columns[10].width = 120 - (longestChars.length - 12) * 3 + "px";
              }
            }
            if (result >= 1 && longestChars.length > 12) {
              this.columns[1].width = 120 + (longestChars.length - 12) * 12 + result * 15 + "px";
              this.columns[7].width = 160 - result * 7 - (longestChars.length - 12) * 4 + "px";
              if (this.isDrillPeiDao) {
                this.columns[11].width = 120 - result * 6 - (longestChars.length - 12) * 4 + "px";
              } else {
                this.columns[10].width = 120 - result * 6 - (longestChars.length - 12) * 4 + "px";
              }
            }
            if (result == 0 && longestChars.length <= 12) {
              this.columns[1].width = "120px";
              this.columns[7].width = "160px";
              if (this.isDrillPeiDao) {
                this.columns[11].width = "120px";
              } else {
                this.columns[10].width = "120px";
              }
            }
            if (result >= 1 && longestChars.length <= 12) {
              this.columns[1].width = 120 + result * 15 + "px";
              this.columns[7].width = 160 - result * 6 + "px";
              if (this.isDrillPeiDao) {
                this.columns[11].width = 120 - result * 6 + "px";
              } else {
                this.columns[10].width = 120 - result * 6 + "px";
              }
            }
          });
        }
      },
    },
    pagination: {
      handler(val) {},
    },
  },
  created() {},
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
  methods: {
    checkPermission,
    FileDownload(record) {
      this.$emit("FileDownload1", record);
    },
    inputFileDownload(record) {
      this.$emit("inputFileDownload", record);
    },
    drillbit(record) {
      downloaddrillfile(record.orderNo_, record.joinFactoryId).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    down1() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    rightClick1(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.localName == "path") {
        event = e.target.parentNode.parentNode;
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split(" ")[0];
      }
      const tableWrapper = this.$refs.tableWrapper;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;
      let offsetx = event.offsetLeft + event.offsetWidth - 10;
      let offsety = event.offsetTop + 40;
      if (event.cellIndex == this.columns.length - 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle.top = offsety + "px";
        this.menuStyle.left = offsetx + "px";
      }

      document.body.addEventListener("click", this.bodyClick);
    },
    handleTableChange(pagination) {
      this.$emit("TableChange", pagination);
    },
    Clickoncolor(record) {
      let str = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        str.push("rowBackgroundColor");
      }
      return str;
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      this.slectedRows = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
        this.slectedRows.push(selectedRowsData[a]);
      }
      this.selectedRowKeys = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeys[this.selectedRowKeys.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeys[0];
        })[0];
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeys;
        let rowdata = this.slectedRows;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
          rowdata.splice(rowKeys.indexOf(record), 1);
        } else {
          rowKeys.push(record.id);
          rowdata.push(record);
        }
        this.selectedRowKeys = rowKeys;
        this.slectedRows = rowdata;
        if (this.selectedRowKeys.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowKeys = [record.id];
          this.slectedRows = [record];
        }
      }
      this.shiftKey = false;
      this.$emit("assignOrderListChange", this.slectedRows);
      this.$emit("getrightbotdata", this.selectedRowsData);
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record);
          },
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.tabRightClikBox {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    background-color: white !important;
    color: #000000;
  }
}
/deep/.ant-table {
  .rowBackgroundColor {
    background: #dfdcdc !important;
  }
}
</style>
