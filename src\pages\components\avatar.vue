<template>
  <a-card>
    <div>
      <a-avatar :size="64" icon="user" />
      <a-avatar size="large" icon="user" />
      <a-avatar icon="user" />
      <a-avatar size="small" icon="user" />
    </div>
    <br />
    <div>
      <a-avatar shape="square" :size="64" icon="user" />
      <a-avatar shape="square" size="large" icon="user" />
      <a-avatar shape="square" icon="user" />
      <a-avatar shape="square" size="small" icon="user" />
    </div>
    <div style="margin-top:24px">
      <span>
        <a-badge :count="1">
          <a-avatar shape="square" icon="user" />
        </a-badge>
      </span>
      <span>
        <a-badge dot>
          <a-avatar shape="square" icon="user" />
        </a-badge>
      </span>
    </div>
    <div>
      <a-avatar icon="user" />
      <a-avatar>
        <a-icon slot="icon" type="user" />
      </a-avatar>
      <a-avatar>U</a-avatar>
      <a-avatar>USER</a-avatar>
      <a-avatar src="https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png" />
      <a-avatar style="color: #f56a00; backgroundColor: #fde3cf">U</a-avatar>
      <a-avatar style="backgroundColor:#87d068" icon="user" />
    </div>
  </a-card>
</template>
<script>
export default {
  data() {
    return {};
  }
};
</script>
<style lang="less">
.ant-avatar {
  margin: 5px 8px;
}
</style>
