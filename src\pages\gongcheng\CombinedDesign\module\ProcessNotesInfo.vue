<template>
  <div>
    <div style="margin-bottom: 10px;margin-left:10px;display:flex;">
      <div style="margin:10px" @click="editclick">
        <a-icon type="diff" style="color: #ff9900; font-size:30px; margin-right: 10px"/>
        <p style="margin:0">编辑</p>
      </div>
      <div style="margin:10px" @click="saveclick">
        <a-icon type="file-protect" style="color: #ff9900; font-size:30px; margin-right: 10px"/>
        <p style="margin:0">保存</p>
      </div>
      <div style="margin:10px" @click="canselclick">
        <a-icon type="file-excel" style="color: #ff9900; font-size:30px; margin-right: 10px"/>
        <p style="margin:0">取消</p>
      </div>


    </div>
    <a-table
        rowKey="techNo_"
        :columns="columns1"
        :dataSource="data"
        :pagination="false"
        :bordered="true"
        :loading="tableLoading"
        :maskClosable="false"
    >
      <template slot="reMaeks_" slot-scope="text,record">
        <a-input v-model="record.reMaeks_" :disabled="!editFlg"> </a-input>
      </template>
    </a-table>
  </div>
</template>

<script>
import {flowReMarks, upFlowReMarks} from "@/services/Combinate";

const columns1 = [
  {
    dataIndex: "index",
    title: '',
    key: 'index',
    width: 40,
    align: 'center',
    // scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "主流程",
    width: 60,
    dataIndex: "techName_",
    align: 'center',
    ellipsis: true,
  },
  {
    title: "流程备注",
    width: 200,
    dataIndex: "reMaeks_",
    scopedSlots:{customRender:'reMaeks_'},
    align: 'center',
    ellipsis: true,
  },

]
export default {
    name:'ProcessNotesInfo',
    props:['ProcessNotesData','ProcessNotesId'],
  created() {
    console.log('ProcessNotesData2',this.ProcessNotesData)
    this.$nextTick(function () {
      this.data = this.ProcessNotesData
    });
  },
  data() {
    return {
      columns1,
      editFlg:false,
      data:[],
      tableLoading:false,

    };
  },

  methods: {
    editclick(){
      this.editFlg = true
    },
    canselclick(){
      if(confirm('编辑内容未保存，确认取消编辑吗？')){
        this.tableLoading = true
        flowReMarks(this.ProcessNotesId).then(res=>{
          this.data = res.data
        }).finally(()=>{
          this.tableLoading = false
          this.editFlg = false
        })

      }
    },
    saveclick(){
      let params = this.data
      upFlowReMarks(this.ProcessNotesId,params).then(res=>{
        if(res.code){
          this.$message.success('编辑保存成功')
        }else{
          flowReMarks(this.ProcessNotesId).then(res=>{
            this.data = res.data
          })
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.tableLoading = false
        this.editFlg = false
      })
    },
  },

  mounted () {
  }
}
</script>
<style scoped>
/deep/.ant-table-thead > tr > th{
  padding:6px 0!important;
}
/deep/.ant-table-tbody > tr > td{
  padding:0!important;
}


</style>
