<template>
    <div class="active" ref="active">
        <div class="box showClass">
            <a-button type="primary" @click="$emit('queryclick')" >
                查询(F)
            </a-button>
        </div> 
        <div class="box"  v-show="checkPermission('MES.MarketModule.CheckManage.OrderShip')"
        :class="checkPermission('MES.MarketModule.CheckManage.OrderShip')?'showClass':''">
            <a-button type="primary" @click="$emit('Shipment')" >
                订单出货
            </a-button>
        </div>
        <div class="box" v-show="checkPermission('MES.MarketModule.CheckManage.OrderCheck')"
        :class="checkPermission('MES.MarketModule.CheckManage.OrderCheck')?'showClass':''">
            <a-button  type="primary" @click="$emit('completeclick')" >
                对账完成
            </a-button>
        </div> 
        <div class="box" v-show="checkPermission('MES.MarketModule.CheckManage.CheckOrderExport')" 
        :class="checkPermission('MES.MarketModule.CheckManage.CheckOrderExport')?'showClass':''">
            <a-button type="primary" @click="$emit('printcontract')" >
                打印对账单
            </a-button>
        </div>  
        <div class="box" v-show="checkPermission('MES.MarketModule.CheckManage.CheckDownloadContract')"
        :class="checkPermission('MES.MarketModule.CheckManage.CheckDownloadContract')?'showClass':''">
            <a-button type="primary" @click="$emit('Downloadcontract')" >
                下载合同
            </a-button>
        </div>  
        <div v-if="buttonsmenu">
        <a-dropdown>
          <a-button type="primary" style="margin-top: 9px;margin-right: 10px;"  @click.prevent>
            按钮菜单栏
          </a-button>
          <template #overlay>
            <a-menu class="tabRightClikBox3">
              <a-menu-item @click="$emit('queryclick')">查询(F)</a-menu-item>
              <a-menu-item @click="$emit('Shipment')" v-if="checkPermission('MES.MarketModule.CheckManage.OrderShip')">订单出货</a-menu-item>
              <a-menu-item @click="$emit('Shipment')" v-if="checkPermission('MES.MarketModule.CheckManage.OrderCheck')">对账完成</a-menu-item>
              <a-menu-item @click="$emit('Shipment')" v-if="checkPermission('MES.MarketModule.CheckManage.CheckOrderExport')">打印对账单</a-menu-item>
              <a-menu-item @click="$emit('Shipment')" v-if="checkPermission('MES.MarketModule.CheckManage.CheckDownloadContract')">下载合同</a-menu-item>
            </a-menu>
          </template>
       </a-dropdown>
      </div>
    </div>
</template>
  
  <script>
  import { checkPermission } from "@/utils/abp";
  export default {
    name: "",
    components: {},
    props: ['total'],
    data() {
      return {
        nums:'',
        buttonsmenu:false,
      }
    },
    methods: {
      checkPermission,
      handleResize(){
      var paginnum = ''
      var elements = document.getElementsByClassName("showClass")    
      const num = elements.length * 104;
        if(Math.ceil(this.total/20)>10){
          paginnum = 6
        }else{
          paginnum = Math.ceil(this.total/20)
        }
        if(window.innerWidth < 1920 ||  window.innerHeight < 923 ){
          if((paginnum*25) +200+ num < window.innerWidth-150  && window.innerWidth>766){
            for (let i = 0; i < elements.length; i++){
            elements[i].style.display = "inline-block"; 
          }
        this.buttonsmenu = false
        }else{
          if(window.innerWidth>766){
            for (let i = 0; i < elements.length; i++){
                elements[i].style.display = "none"; 
              }
              this.buttonsmenu = true
          }else{
            if( window.innerWidth-20-num < 70){
              for (let i = 0; i < elements.length; i++){
                elements[i].style.display = "none"; 
                this.buttonsmenu = true
              }
            }else{
              for (let i = 0; i < elements.length; i++){
                elements[i].style.display = "inline-block"; 
              }
              this.buttonsmenu = false
            }
           }
          }
        }else{
          for (let i = 0; i < elements.length; i++){
          elements[i].style.display = "inline-block"; 
          }
          this.buttonsmenu = false
        }
    },
    },
    created() {
      this.$nextTick(()=>{
        this.nums = document.getElementsByClassName("showClass").length
        this.handleResize()
        window.addEventListener('resize', this.handleResize, true)
      })
    },
    beforeDestroy(){
    window.removeEventListener('resize', this.handleResize, true)
   },
    mounted() {},
  };
  </script>
  
  <style lang="less" scoped>
   .tabRightClikBox3{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        width:100px;
        line-height:22px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color:#666666
      }
      li:hover{
        background-color: #ff9900 !important;
        color:white;
        font-size:16px;
        height:32px;
        line-height:22px
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }
  .active {
  height: 50px;
  line-height: 40px;
  float: right;
  display: flex;

  .box {
    width: 100px;
    margin-top: 6px;
    text-align: center;

    .ant-btn {
      width: 90px;
      padding: 0;
    }
  }
}
  </style>