<!-- 工程管理 - 工程后端 - 按钮-->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendSend')"
      :class="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendSend') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="() => this.$emit('assignClick')" :loading="assignLoading"> 分派(S) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendOrder')"
      :class="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="TakeOrderClick" :disabled="isDisabled"> 取单(Q) </a-button>
    </div>
    <!-- <div class="box"  v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendWenKeUrl')">
      <a-button type="primary" disabled>
        开始
      </a-button>
    </div> -->
    <!-- <div class="box"   v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendWenKeUrl')"
    :class='checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendWenKeUrl")?"showClass":""'>
      <a-button type="primary" @click="wenkeClick">
        问客
      </a-button>
    </div> -->
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendMIBackFinish')"
      :class="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendMIBackFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="backendcompletion"> 完成 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngBackOrderModify')"
      :class="checkPermission('MES.EngineeringModule.EngineeringBackend.EngBackOrderModify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="MarketmodClick"> 市场修改 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendBackOrders')"
      :class="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendBackOrders') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="FallbackFrontClick"> 回退前端 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackSureOrderModify')"
      :class="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackSureOrderModify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Confirmmodification')"> 确认修改 </a-button>
    </div>
    <!-- <div class="box showClass" >
      <a-button type="primary" @click="PerformanceClick" >
        绩效管理
      </a-button>     
    </div> -->
    <!-- <div class="box"  v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendPassback')">
      <a-button type="primary" @click="finishClick('2')">
        回传
      </a-button>
    </div> -->
    <!--      <div class="box"  v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendModifyInformation')" >-->
    <!--        <a-button type="primary" @click="modifyInfoClick">-->
    <!--          修改信息-->
    <!--        </a-button>-->
    <!--      </div>-->

    <!-- <div class="box"   v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendImp')"
      :class='checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendImp")?"showClass":""'>
        <a-button type="primary" @click='StackImpedanceClick'>
          叠层阻抗
        </a-button>
    </div> -->
    <!--    <div v-if='advanced'  >-->
    <!--      <div class="box"  v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendFixRecord')">-->
    <!--        <a-button type="primary" @click='RepairRecordClick'>-->
    <!--          返修记录-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--    </div>-->
    <!-- <div class="box"   v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendFixFinish')"
      :class='checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendFixFinish")?"showClass":""'>
        <a-button type="primary" @click='RepairCompletedClick'>
          返修完成
        </a-button>
    </div> -->
    <!-- <div class="box"   v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendMattersNeedingAttention')"
      :class='checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendMattersNeedingAttention")?"showClass":""'>
        <a-button type="primary" @click='mattersNeedingAttentionClick'>
          注意事项
        </a-button>
    </div> -->
    <!-- <div class="box"  v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendFixRecord')"
      :class='checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendFixRecord")?"showClass":""'>
        <a-button type="primary" @click='RegisterClick' >
          返修登记
        </a-button>
    </div> -->
    <!-- <div v-if='advanced'  >
      <div class="box"  v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendRuleShowInfoPpe')">
        <a-button type="primary" @click='CustomerRulesClick'>
          客户规则
        </a-button>
      </div>
    </div> -->
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
            <a-menu-item
              @click="() => this.$emit('assignClick')"
              :loading="assignLoading"
              v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendSend')"
              >分派(S)</a-menu-item
            >
            <a-menu-item @click="TakeOrderClick" v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendOrder')"
              >取单(Q)</a-menu-item
            >
            <a-menu-item
              @click="backendcompletion"
              v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendMIBackFinish')"
              >完成</a-menu-item
            >
            <a-menu-item @click="MarketmodClick" v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngBackOrderModify')"
              >市场修改</a-menu-item
            >
            <a-menu-item @click="FallbackFrontClick" v-show="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendBackOrders')"
              >回退前端</a-menu-item
            >
            <a-menu-item
              @click="$emit('Confirmmodification')"
              v-if="checkPermission(' MES.EngineeringModule.EngineeringBackend.EngineeringBackSureOrderModify')"
              >确认修改</a-menu-item
            >
            <!-- <a-menu-item @click="PerformanceClick" >绩效管理</a-menu-item> -->
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { projectBackEndAssignBack } from "@/services/projectApi";

export default {
  name: "BackendAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      advanced: false,
      width: 762,
      nums: "",
      isDisabled: false,
      collapsed: false,
      showBtn: false,
      buttonsmenu: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      this.nums = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      const num = elements.length * 104;
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "inline-block";
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "inline-block";
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    PerformanceClick() {
      this.$emit("PerformanceClick");
    },
    // 修改信息
    // modifyInfoClick(){
    //   this.$emit('modifyInfoClick')
    // },
    // 注意事项
    mattersNeedingAttentionClick() {
      this.$emit("mattersNeedingAttentionClick");
    },
    // 返修登记
    RegisterClick() {
      this.$emit("RegisterClick");
    },
    // 回退前端
    FallbackFrontClick() {
      this.$emit("FallbackFrontClick");
    },
    // // 返修记录
    // RepairRecordClick(){
    //   this.$emit('RepairRecordClick')
    // },
    // 客户规则
    // CustomerRulesClick(){
    //   this.$emit('CustomerRulesClick')
    // },
    // 叠层阻抗
    StackImpedanceClick() {
      this.$emit("StackImpedanceClick");
    },
    // 问客
    wenkeClick() {
      this.$emit("wenkeClick");
    },
    //市场修改
    MarketmodClick() {
      this.$emit("MarketmodClick");
    },
    backendcompletion() {
      this.$emit("backendcompletion");
    },
    // 返修完成
    RepairCompletedClick() {
      this.$emit("RepairCompletedClick");
    },
    //  取单
    TakeOrderClick() {
      this.$emit("TakeOrderClick");
    },
    // 完成
    finishClick(code) {
      this.$emit("finishClick", code);
    },
    // 取单设置
    // OrderRetrievalSettingsClick(){
    //   this.$emit('OrderRetrievalSettingsClick')
    // }
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  //width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 96px;
    margin-top: 4px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
