<template>
    <div>
        <a-card :bordered="false">
            <a-alert message="基础按钮样式" type="success" class='alert'/>
            <a-button type="primary"> Primary </a-button>
            <a-button>Default</a-button>
            <a-button type="dashed">Dashed</a-button>
            <a-button type="danger">Danger</a-button>
            <a-config-provider :auto-insert-space-in-button="false">
            <a-button type="primary">按钮</a-button>
            </a-config-provider>
            <a-button type="primary">按钮</a-button>
            <a-button type="link">Link</a-button> 
        </a-card>
        <a-card :bordered="false">
            <a-alert message="按钮组合样式" type="success" class='alert'/>
            <a-button-group>
            <a-button>Cancel</a-button>
            <a-button type="primary">
                OK
            </a-button>
            </a-button-group>
            <a-button-group>
            <a-button disabled>
                L
            </a-button>
            <a-button disabled>
                M
            </a-button>
            <a-button disabled>
                R
            </a-button>
            </a-button-group>
            <a-button-group>
            <a-button type="primary">
                L
            </a-button>
            <a-button>M</a-button>
            <a-button>M</a-button>
            <a-button type="dashed">
                R
            </a-button>
            </a-button-group>

            <h4>With Icon</h4>
            <a-button-group>
            <a-button type="primary"> <a-icon type="left" />Go back </a-button>
            <a-button type="primary"> Go forward<a-icon type="right" /> </a-button>
            </a-button-group>
            <a-button-group>
            <a-button type="primary" icon="cloud" />
            <a-button type="primary" icon="cloud-download" />
            </a-button-group>
        </a-card>
        <a-card :bordered="false">
            <a-alert message="图标按钮样式" type="success" class='alert'/>
            <a-button type="primary" shape="circle" icon="search" />
            <a-button type="primary" shape="circle">
            A
            </a-button>
            <a-button type="primary" icon="search">
            Search
            </a-button>
            <a-button shape="circle" icon="search" />
            <a-button icon="search">
            Search
            </a-button>
            <a-button shape="circle" icon="search" />
            <a-button icon="search">
            Search
            </a-button>
            <a-button type="dashed" shape="circle" icon="search" />
            <a-button type="dashed" icon="search">
            Search
            </a-button>
        </a-card>
        <a-card :bordered="false">
            <a-alert message="幽灵按钮样式(幽灵按钮将其他按钮的内容反色，背景变为透明，常用在有色背景上)" type="success" class='alert'/>
            <div :style="{ background: 'rgb(190, 200, 200)', padding: '26px 16px 16px' }">
                <a-button type="primary" ghost>
                Primary
                </a-button>
                <a-button ghost>
                Default
                </a-button>
                <a-button type="dashed" ghost>
                Dashed
                </a-button>
                <a-button type="danger" ghost>
                Danger
                </a-button>
                <a-button type="link" ghost>
                Link
                </a-button>
            </div>
        </a-card>
        <a-card :bordered="false">
            <a-alert message="添加 loading 属性即可让按钮处于加载状态" type="success" class='alert'/>
            <a-button type="primary" loading>
            Loading
            </a-button>
            <a-button type="primary" size="small" loading>
            Loading
            </a-button>
            <br />
            <p style='margin-bottom:15px'></p>
            <a-button type="primary" :loading="loading" @mouseenter="enterLoading">
            mouseenter me!
            </a-button>
            <a-button type="primary" icon="poweroff" :loading="iconLoading" @click="enterIconLoading">
            延迟1s
            </a-button>
            <br />
            <p style='margin-bottom:15px'></p>
            <a-button type="primary" loading />
            <a-button type="primary" shape="circle" loading />
            <a-button type="danger" shape="round" loading />
        </a-card>
        <a-card :bordered="false">
           <a-alert message="按钮有大、中、小三种尺寸。通过设置 size 为 large small 分别把按钮设为大、小尺寸。若不设置 size，则尺寸为中" type="success" class='alert'/>
           <a-radio-group :value="size" @change="handleSizeChange">
            <a-radio-button value="large">
                Large
            </a-radio-button>
            <a-radio-button value="default">
                Default
            </a-radio-button>
            <a-radio-button value="small">
                Small
            </a-radio-button>
            </a-radio-group>
            <br><br>
            <a-button type="primary" :size="size">
            Primary
            </a-button>
            <a-button :size="size">
            Normal
            </a-button>
            <a-button type="dashed" :size="size">
            Dashed
            </a-button>
            <a-button type="danger" :size="size">
            Danger
            </a-button>
            <a-button type="link" :size="size">
            Link
            </a-button>
            <br>
            <p style='margin-bottom:15px'></p>
            <a-button type="primary" icon="download" :size="size" />
            <a-button type="primary" shape="circle" icon="download" :size="size" />
            <a-button type="primary" shape="round" icon="download" :size="size" />
            <a-button type="primary" icon="download" :size="size">
            Download
            </a-button>
            <br>
            <p style='margin-bottom:15px'></p>
            <a-button-group :size="size">
                <a-button type="primary">
                    <a-icon type="left" />Backward
                </a-button>
                <a-button type="primary">
                    Forward<a-icon type="right" />
                </a-button>
            </a-button-group>
        </a-card>
    </div>
</template>
<script>
export default {
   name: 'buttonDetail',
   data() {
    return {
      loading: false,
      iconLoading: false,
       size: 'large',
    }
   },
   methods: {
    enterLoading() {
      this.loading = true;
    },
    enterIconLoading() {
      this.iconLoading = { delay: 1000 };
    },
    handleSizeChange(e) {
        this.size = e.target.value;
    },
  },
}
</script>
<style lang="less" scoped>
.ant-alert.ant-alert-no-icon{
    margin-bottom:15px
}
.ant-btn{
    margin-right:10px
}

</style>