<template>
  <div>
    <a-card>
      <a-spin size="small" />
      <a-spin />
      <a-spin size="large" />
      <a-spin :indicator="indicator" />
      <a-spin tip="Loading..."></a-spin>
    </a-card>
  </div>
</template>
<script>
export default {
  data() {
    return {
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
    };
  },
};
</script>
<style lang="stylus">
.ant-spin 
    margin: 20px;
</style>