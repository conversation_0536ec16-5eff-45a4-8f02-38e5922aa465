import { request, METHOD } from "@/utils/request";
// 获取参数列
export async function checkList(softcode, configtype) {
  return request(`/api/app/sight/check-list?softcode=${softcode}&configtype=${configtype}`, METHOD.GET);
}
// 获取参数信息
export async function checkBillInfo(Boid, sightsite) {
  return request(`/api/app/sight/check-bill-info-v1?boid=${Boid}&sightsite=${sightsite}`, METHOD.GET);
}
// 保存参数信息
export async function setCheckBillInfo(params) {
  return request(`/api/app/sight/set-check-bill-info-v1`, METHOD.POST, params);
}
export async function setSightInfoToEMS(boid, softcode) {
  return request(`/api/app/sight/set-sight-info-to-eMS?boid=${boid}&softcode=${softcode}`, METHOD.POST);
}
