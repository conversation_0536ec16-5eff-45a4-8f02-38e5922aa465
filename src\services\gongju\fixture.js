import { request, METHOD } from "@/utils/request";
export async function profixtureordernewadd(orderno, type) {
  return request(`/api/app/e-mSPro-fixture/pro-fixture-order-new-add?OrderNo=${orderno}&ZjType=${type}`, METHOD.POST);
}
export async function profixturewaitlist(params) {
  return request(`/api/app/e-mSPro-fixture/pro-fixture-wait-list`, METHOD.GET, params);
}
export async function profixturelist(params) {
  return request(`/api/app/e-mSPro-fixture/pro-fixture-list`, METHOD.GET, params);
}
export async function profixtureorderstart(ids) {
  return request(`/api/app/e-mSPro-fixture/pro-fixture-order-start`, METHOD.POST, ids);
}
export async function profixtureorderedit(params) {
  return request(`/api/app/e-mSPro-fixture/pro-fixture-order-edit`, METHOD.POST, params);
}
export async function profixtureorderuse(id) {
  return request(`/api/app/e-mSPro-fixture/${id}/pro-fixture-order-use`, METHOD.POST);
}
export async function profixtureorderfinish(id) {
  return request(`/api/app/e-mSPro-fixture/${id}/pro-fixture-order-finish`, METHOD.POST);
}
export default {
  profixturelist,
  profixturewaitlist,
  profixtureorderstart,
  profixtureorderedit,
  profixtureorderuse,
  profixtureorderfinish,
  profixtureordernewadd,
};
