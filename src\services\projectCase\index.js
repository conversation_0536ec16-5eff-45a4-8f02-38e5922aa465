import { request, METHOD } from '@/utils/request'
//  获取订单列表信息
export async function caseOrderList(params) {
    return request(`/api/app/e-mSTWFEngineering-case/get-engineering-case-order-list?KeyWord=${params}`, METHOD.GET,)
}
//  上传图片
export async function caseAttFile(params) {
    return request(`/api/app/e-mSTWFEngineering-case/up-load-engineering-case-att-file`, METHOD.POST,params)
}
//  编辑保存
export async function caseRepait(params) {
    return request(`/api/app/e-mSTWFEngineering-case/repait`, METHOD.POST,params)
}
//  新增保存
export async function caseSaveNew(params) {
    return request(`/api/app/e-mSTWFEngineering-case/save-new`, METHOD.POST,params)
}
// 获取工程案例类型
export async function getBtoParameter(params) {
    return request(`/api/app/e-mSData-class-list/data-class-list?TypeNo=${params}`, METHOD.POST,)
}
//删除工程案例
export async function deleteengineeringcase(Id) {
    return request(`/api/app/e-mSTWFEngineering-case/delete-engineering-case/${Id}`, METHOD.POST,)
}
export default {
    caseOrderList,
    caseRepait,
    caseAttFile,
    caseSaveNew,
    deleteengineeringcase
}
