<!-- 市场管理 - 订单询价 - 按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManagement.InquiryAdd')"
      :class="checkPermission('MES.MarketModule.OrderManagement.InquiryAdd') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="addClick"> 新增(A) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManagement.WritePoNumJjJq')"
      :class="checkPermission('MES.MarketModule.OrderManagement.WritePoNumJjJq') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="editClick"> 编辑 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManagement.InquiryConfirm')"
      :class="checkPermission('MES.MarketModule.OrderManagement.InquiryConfirm') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="auditClick"> 订单确认 </a-button>
    </div>
    <!-- <div class="box"  >
      <a-button type="primary" @click="sureClick"  disabled >
        预审开始
      </a-button> 
    </div> -->
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManagement.InquiryDel')"
      :class="checkPermission('MES.MarketModule.OrderManagement.InquiryDel') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="delClick"> 型号删除 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManagement.UpdateFile')"
      :class="checkPermission('MES.MarketModule.OrderManagement.UpdateFile') ? 'showClass' : ''"
    >
      <a-button
        type="primary"
        @click="uploadPCBFileClick"
        style="background: #ff9900; color: white; border-color: #ff9900; text-shadow: 0 -1px 0 rgb(0 0 0 / 12%); box-shadow: 0 2px 0 rgb(0 0 0 / 5%)"
      >
        文件替换
      </a-button>
      <a-upload
        accept=".rar,.zip"
        name="file"
        :multiple="false"
        :customRequest="customRequest"
        :showUploadList="false"
        ref="fileRef"
        :before-upload="beforeUpload1"
        v-show="false"
      >
        <a-button style="width: 80px"><a-icon type="upload" /></a-button>
      </a-upload>
    </div>

    <!-- <div class="box"  v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendOrder')">
        <a-button type="primary" @click='TakeOrderClick'>
          取单
        </a-button>
      </div> -->
    <!-- <div v-if='advanced'  >
      <div class="box"  v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendMattersNeedingAttention')">
        <a-button type="primary" @click='mattersNeedingAttentionClick'>
          注意事项
        </a-button>
      </div>
    </div>     -->
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
            <a-menu-item @click="addClick" v-if="checkPermission('MES.MarketModule.OrderManagement.InquiryAdd')">新增(A)</a-menu-item>
            <a-menu-item @click="editClick" v-if="checkPermission('MES.MarketModule.OrderManagement.WritePoNumJjJq')">编辑(S)</a-menu-item>
            <a-menu-item @click="auditClick" v-if="checkPermission('MES.MarketModule.OrderManagement.InquiryConfirm')">订单确认</a-menu-item>
            <a-menu-item @click="delClick" v-if="checkPermission('MES.MarketModule.OrderManagement.InquiryDel')">型号删除</a-menu-item>
            <a-menu-item @click="uploadPCBFileClick" v-if="checkPermission('MES.MarketModule.OrderManagement.UpdateFile')">文件替换</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { checkPermission } from "@/utils/abp";
import { updateFile, upLoadEnquiryFile, inquirycheckorderfile } from "@/services/mkt/Inquiry.js";
export default {
  name: "BackendAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    keyArr: {
      type: Array,
    },
    CustNo: {
      type: String,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      advanced: false,
      showBtn: false,
      width: 762,
      collapsed: false,
      nums: "",
      buttonsmenu: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      this.nums = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      this.handleResize();
      window.addEventListener("resize", this.handleResize, true);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      const num = elements.length * 104;
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "inline-block";
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 20 - num < 70) {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "inline-block";
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 新增
    addClick() {
      this.$emit("addClick");
    },
    // 编辑
    editClick() {
      this.$emit("editClick");
    },
    // 订单确认
    auditClick() {
      this.$emit("auditClick");
    },
    // 订单确认
    // sureClick(){
    //   this.$emit('sureClick')
    // },
    // 型号删除
    delClick() {
      this.$emit("delClick");
    },
    uploadPCBFileClick() {
      this.$emit("uploadPCBFileClick");
    },
    handleChangeImg(info) {
      this.imgName = `${info.file.name}`.substring(0, `${info.file.name}`.indexOf("."));
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
        const filesize = Number(file.size / 1024 / 1024) < 500;
        if (!isFileType) {
          _this.$message.error("只支持.rar或.zip格式文件");
          reject();
        } else if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    // customRequest(data){
    //   const formData = new FormData()
    //   formData.append('file', data.file)
    //   let params = {
    //     'id':this.keyArr[0]
    //   }
    //   let cus = ''
    //   upLoadEnquiryFile(formData,cus).then(res =>{
    //     if (res.code == 1) {
    //       params.PcbFilePath = res.data.split(',')[0]
    //       params.pcbFileName = res.data.split(',')[1]
    //       updateFile(params).then(res =>{
    //         if (res.code){
    //           this.$message.success('上传成功')
    //           this.$emit('getOrderList')
    //         } else {
    //           this.$message.error(res.message)
    //         }
    //       })

    //     } else{
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    customRequest(data) {
      this.$emit("customRequest", data);
    },
    clickUpload(id) {
      this.orderId = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 92px;
    margin-top: 7px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
