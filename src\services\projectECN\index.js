import { request, METHOD } from "@/utils/request";
//ECN获取左边列表
export function ecnorderno(params) {
  return request("/api/app/engineering-ecn/ecn-order-no", METHOD.GET, params);
}
//ECN获取中间列表
export function ecnorderdetail(JoinFactoryId, params) {
  return request(`/api/app/engineering-ecn/ecn-order-detail/${JoinFactoryId}`, METHOD.GET, params);
}
//ECN新增
export function ecnorderadd(OrderNo, OnLineOrRecordEcn, JoinFactoryId) {
  return request(`/api/app/engineering-ecn/ecn-order-add/${JoinFactoryId}?OrderNo=${OrderNo}&OnLineOrRecordEcn=${OnLineOrRecordEcn}`, METHOD.POST);
}
//ECN型号明细信息
export function ecninfo(Id) {
  return request(`/api/app/engineering-ecn/ecn-info/${Id}`, METHOD.GET);
}
//修改保存
export function ecnordersave(params) {
  return request("/api/app/engineering-ecn/ecn-order-save", METHOD.POST, params);
}
//获取下拉值
export function classlistecn() {
  return request("/api/app/engineering-ecn/data-class-list-ecn", METHOD.POST);
}
//ECN审核
export function ecnorderverify(Id) {
  return request(`/api/app/engineering-ecn/ecn-order-verify/${Id}`, METHOD.POST);
}
//同一批未完成的订单，工程版本升级
export function upversion(params) {
  return request(`/api/app/engineering-ecn/up-version`, METHOD.POST, params);
}
//ECN反审核
export function ecnorderbackverify(Id) {
  return request(`/api/app/engineering-ecn/ecn-order-back-verify/${Id}`, METHOD.POST);
}
//ECN删除
export function ecnorderdelete(Id) {
  return request(`/api/app/engineering-ecn/ecn-order-delete/${Id}`, METHOD.POST);
}
export default {
  ecnorderno,
  ecnorderdetail,
  ecnorderadd,
  ecninfo,
  ecnordersave,
  classlistecn,
  ecnorderverify,
  ecnorderbackverify,
  ecnorderdelete,
};
