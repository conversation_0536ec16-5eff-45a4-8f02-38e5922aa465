import { request, METHOD } from '@/utils/request'

//新增接口（报价失败时写入接口）
export async function informationremind(params) {
    return request(`/api/app/information-remind`, METHOD.POST,params)
}

//列表接口
export async function getinformationremind(params) {
    return request(`/api/app/information-remind`, METHOD.GET,params)
}

//消息设为已读

export async function updateremindstatus(POST) {
    return request(`/api/app/information-remind/${POST}/update-remind-status`, METHOD.POST)
}
