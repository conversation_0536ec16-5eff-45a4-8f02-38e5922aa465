import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'

export async function upload(params) {
    return request("/api/app/e-mSOrder-management/uploadfile", METHOD.POST,params)
}

export async function getOrderList(params) {
    return request(`/api/app/e-mSOrder-management/management-info`, METHOD.GET,params)
}
export async function newOrder(params) {
    return request("/api/app/e-mSOrder-management/order-management", METHOD.POST,params)
}
export async function amderOrder(params) {
    return request("/api/app/e-mSOrder-management/up-date-order-management", METHOD.PUT,params)
}
export async function getOrderNews(params) {
    return request("/api/app/e-mSOrder-management/order-par-price-data/"+params, METHOD.POST)
}
export async function newPrice(params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/price", METHOD.POST,params)
}
export async function amderPrice(params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/price", METHOD.PUT,params)
}
export async function delePrice(params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/delete-price/"+params, METHOD.DELETE)
}

export async function deleOrder(params) {
    return request("/api/app/e-mSOrder-management/delete-order-management/"+params, METHOD.DELETE)
}


export async function queryData(params) {
    return request("/api/app/e-mSOrder-management/order-list-by-search", METHOD.GET,params)
}

export async function audit(params) {
    return request("/api/app/e-mSOrder-management/audit?Ids="+params, METHOD.POST)
}
export async function addorder(params) {
    return request("/api/app/e-mSOrder-management/order-again", METHOD.POST,params)
}

export async function getPriceList(Id) {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/get-price/${Id}`, METHOD.GET)
}

export async function getcodeList() {
    return request(`/api/app/e-mSCust-module-no/cust-combox-item`, METHOD.GET)
}

export async function getParameter() {
    return request(`/api/app/e-mSTMkt-xTDefault-par/def-list`, METHOD.GET)
}
export async function contractNumber(params) {
    return request(`/api/app/e-mSOrder-management/contract-no`, METHOD.POST,params)
}

export async function  contractInformation(params) {
    return request("/api/app/e-mSOrder-management/contract-info?"+params, METHOD.GET,)
}

export async function delContractNumber(params) {
    return request(`/api/app/e-mSOrder-management/del-contract`, METHOD.POST,params)
}


export  default {
    upload,
    getOrderList,
    newOrder,
    amderOrder,
    getOrderNews,
    newPrice,
    amderPrice,
    delePrice,
    deleOrder,
    queryData,
    audit,
    addorder,
    getPriceList,
    getcodeList,
    getParameter,
    contractNumber,
    contractInformation,
    delContractNumber
}
