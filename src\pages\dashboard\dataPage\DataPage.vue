<!--控制台- 工程数据页 -->
<template>
  <div class="dataPage" ref="SelectBox">
    <a-spin :spinning="spinning">
      <a-tabs type="card" v-model="activeKey">
        <!-- 管理总表 -->
        <!-- <a-tab-pane key="1" style='position:relative'>
          <template #tab>
            <span>
              管理总表
            </span>
          </template>
          <a-month-picker v-model='pickerDefault2' @change='change' placeholder='请选择年月' :format="monthFormat"
            style='position:absolute; top:-49px;right:0;' />
          <order-info ref="editForm" :CostAnalysisData='CostAnalysisData' :selectDate='selectDate'></order-info>
        </a-tab-pane> -->
        <!-- 外接CAM -->
        <!-- <a-tab-pane key="2" style='position:relative'>
          <template #tab>
            <span>
              外接CAM
              <a-tooltip title="导出数据">
                <a-icon type='download' @click='down2' />
              </a-tooltip>
            </span>
          </template>
          <a-range-picker type="dates" @change='change1' v-model='pickerDefault1'
            style='position:absolute; top:-49px;right:0;width:217px;' />
          <a-table 
          :columns="columns" 
          :dataSource="data1Source" 
          :pagination="false" 
          :loading='table1Loading'
          :class="data1Source.length ? 'min-table':'minClass'"
          rowKey='factoryId' 
          :scroll="{ y: 698 }" bordered>
          </a-table>
        </a-tab-pane> -->
        <!-- 人工成本 -->
        <!-- <a-tab-pane key="3" style='position:relative'>
          <template #tab>
            <span>
              人工成本
              <a-tooltip title="导出数据">
                <a-icon type='download' @click='down3' />
              </a-tooltip>
            </span>
          </template>
          <a-range-picker type="dates" @change="change2" style='position:absolute; top:-49px;right:0;width:217px;'
            v-model='pickerDefault' />
          <a-table class="bmgx"
          :columns="columns1" 
          :dataSource="data2Source" 
          :pagination="false" 
          :loading='table2Loading'
          :class="data2Source.length ? 'min-table1':'minClass1'"
          :rowKey="(record, index) => { return index }" :scroll="{ y: 728 }" bordered @change="onChange" />
        </a-tab-pane> -->
        <!-- 个人工效 -->
        <a-tab-pane key="5" style="position: relative">
          <template #tab>
            <span>
              个人工效CAM
              <a-tooltip title="导出数据">
                <a-icon type="download" @click="down5" />
              </a-tooltip>
            </span>
          </template>
          <a-month-picker
            v-model="pickerDefault3"
            @change="change4"
            placeholder="请选择年月"
            :format="monthFormat"
            style="position: absolute; top: -49px; right: 0; width: 180px"
          />
          <a-select
            v-model="FactoryId"
            style="width: 120px; position: absolute; top: -49px; right: 190px"
            showSearch
            allowClear
            optionFilterProp="lable"
            @change="Factorychange"
            placeholder="授权工厂"
          >
            <a-select-option
              style="color: blue"
              v-for="(item, index) in mapKey(factroyList)"
              :key="index"
              :value="item.value"
              :lable="item.lable"
              :title="item.lable"
            >
              {{ item.lable }}
            </a-select-option>
          </a-select>
          <a-table
            class="geren"
            :class="showData2.length ? 'mintable1 bmgx' : ''"
            :columns="columns5"
            :dataSource="showData2"
            :loading="table3Loading"
            :rowClassName="(record, index) => getRowClass(index)"
            :pagination="false"
            @change="handleTableChange"
            :rowKey="(record, index) => `${index + 1}`"
            :scroll="{ y: 720 }"
            bordered
          >
            <template slot="num1" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num1') ? 'tdred' : ''">{{ record.nums.num1 }}</div>
            </template>
            <template slot="num2" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num2') ? 'tdred' : ''">{{ record.nums.num2 }}</div>
            </template>
            <template slot="num3" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num3') ? 'tdred' : ''">{{ record.nums.num3 }}</div>
            </template>
            <template slot="num4" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num4') ? 'tdred' : ''">{{ record.nums.num4 }}</div>
            </template>
            <template slot="num5" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num5') ? 'tdred' : ''">{{ record.nums.num5 }}</div>
            </template>
            <template slot="num6" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num6') ? 'tdred' : ''">{{ record.nums.num6 }}</div>
            </template>
            <template slot="num7" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num7') ? 'tdred' : ''">{{ record.nums.num7 }}</div>
            </template>
            <template slot="num8" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num8') ? 'tdred' : ''">{{ record.nums.num8 }}</div>
            </template>
            <template slot="num9" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num9') ? 'tdred' : ''">{{ record.nums.num9 }}</div>
            </template>
            <template slot="num10" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num10') ? 'tdred' : ''">{{ record.nums.num10 }}</div>
            </template>
            <template slot="num11" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num11') ? 'tdred' : ''">{{ record.nums.num11 }}</div>
            </template>
            <template slot="num12" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num12') ? 'tdred' : ''">{{ record.nums.num12 }}</div>
            </template>
            <template slot="num13" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num13') ? 'tdred' : ''">{{ record.nums.num13 }}</div>
            </template>
            <template slot="num14" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num14') ? 'tdred' : ''">{{ record.nums.num14 }}</div>
            </template>
            <template slot="num15" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num15') ? 'tdred' : ''">{{ record.nums.num15 }}</div>
            </template>
            <template slot="num16" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num16') ? 'tdred' : ''">{{ record.nums.num16 }}</div>
            </template>
            <template slot="num17" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num17') ? 'tdred' : ''">{{ record.nums.num17 }}</div>
            </template>
            <template slot="num18" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num18') ? 'tdred' : ''">{{ record.nums.num18 }}</div>
            </template>
            <template slot="num19" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num19') ? 'tdred' : ''">{{ record.nums.num19 }}</div>
            </template>
            <template slot="num20" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num20') ? 'tdred' : ''">{{ record.nums.num20 }}</div>
            </template>
            <template slot="num21" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num21') ? 'tdred' : ''">{{ record.nums.num21 }}</div>
            </template>
            <template slot="num22" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num22') ? 'tdred' : ''">{{ record.nums.num22 }}</div>
            </template>
            <template slot="num23" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num23') ? 'tdred' : ''">{{ record.nums.num23 }}</div>
            </template>
            <template slot="num24" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num24') ? 'tdred' : ''">{{ record.nums.num24 }}</div>
            </template>
            <template slot="num25" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num25') ? 'tdred' : ''">{{ record.nums.num25 }}</div>
            </template>
            <template slot="num26" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num26') ? 'tdred' : ''">{{ record.nums.num26 }}</div>
            </template>
            <template slot="num27" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num27') ? 'tdred' : ''">{{ record.nums.num27 }}</div>
            </template>
            <template slot="num28" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num28') ? 'tdred' : ''">{{ record.nums.num28 }}</div>
            </template>
            <template slot="num29" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num29') ? 'tdred' : ''">{{ record.nums.num29 }}</div>
            </template>
            <template slot="num30" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num30') ? 'tdred' : ''">{{ record.nums.num30 }}</div>
            </template>
            <template slot="num31" slot-scope="record, text, index">
              <div :class="TDStyle(record, index, 'num31') ? 'tdred' : ''">{{ record.nums.num31 }}</div>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="8" style="position: relative">
          <template #tab>
            <span>
              个人工效QAE
              <a-tooltip title="导出数据">
                <a-icon type="download" @click="downqae" />
              </a-tooltip>
            </span>
          </template>
          <a-month-picker
            v-model="pickerDefault8"
            @change="change8"
            placeholder="请选择年月"
            :format="monthFormat"
            style="position: absolute; top: -49px; right: 0; width: 180px"
          />
          <a-select
            v-model="FactoryIdqae"
            style="width: 120px; position: absolute; top: -49px; right: 190px"
            showSearch
            allowClear
            optionFilterProp="lable"
            @change="Factorychangeqae"
            placeholder="授权工厂"
          >
            <a-select-option
              style="color: blue"
              v-for="(item, index) in mapKey(factroyList)"
              :key="index"
              :value="item.value"
              :lable="item.lable"
              :title="item.lable"
            >
              {{ item.lable }}
            </a-select-option>
          </a-select>
          <a-table
            class="geren"
            :loading="table3Loading"
            :class="showDataqae.length ? 'mintable1 bmgx' : ''"
            :columns="columnsqae"
            :dataSource="showDataqae"
            :pagination="false"
            :rowKey="(record, index) => `${index + 1}`"
            :scroll="{ y: 720 }"
            bordered
          >
          </a-table>
        </a-tab-pane>
        <!-- 部门工效(合集) -->
        <a-tab-pane key="4" style="position: relative">
          <template #tab>
            <span> 部门工效 </span>
          </template>
          <a-select
            v-model="FactoryId1"
            style="width: 120px; position: absolute; top: -49px; right: 320px"
            showSearch
            allowClear
            optionFilterProp="lable"
            @change="Factorychange1"
            placeholder="授权工厂"
          >
            <a-select-option
              style="color: blue"
              v-for="(item, index) in mapKey(factroyList)"
              :key="index"
              :value="item.value"
              :lable="item.lable"
              :title="item.lable"
            >
              {{ item.lable }}
            </a-select-option>
          </a-select>
          <a-select v-model="EffectNum" style="position: absolute; top: -49px; right: 190px; width: 120px" @change="handleChangeEffect">
            <a-select-option value="1"> 日 </a-select-option>
            <a-select-option value="2"> 周 </a-select-option>
            <a-select-option value="3"> 月 </a-select-option>
          </a-select>
          <!-- 日 -->
          <div v-if="EffectNum == 1">
            <a-month-picker
              v-model="pickerDefault4"
              @change="change3"
              placeholder="请选择年月"
              :format="monthFormat"
              style="position: absolute; top: -49px; right: 0; width: 180px"
            />
            <a-table
              class="bmgx"
              :columns="columns4"
              :dataSource="showData1"
              :pagination="false"
              :loading="table3Loading"
              :rowKey="(record, index) => `${index + 1}`"
              :scroll="{ x: 1700, y: 500 }"
              bordered
            >
              <span slot="realname" slot-scope="text, record">
                <a-tooltip title="工效=文件数÷工时">
                  <span style="color: #ff9900" v-if="record.realname == '工效'" @click="click(record)">{{ record.realname }}</span>
                  <span v-else>{{ record.realname }}</span></a-tooltip
                >
              </span>
            </a-table>
            <!--      :style="[{height: 380+'px'},{width:'100%'}]"-->
            <div id="el" :style="[{ height: 304 + 'px' }, { width: '100%' }]"></div>
          </div>
          <!-- 周 -->
          <div v-else-if="EffectNum == 2">
            <a-month-picker
              v-model="pickerDefault5"
              @change="change5"
              placeholder="请选择年月"
              :format="monthFormat"
              style="position: absolute; top: -49px; right: 0; width: 180px"
            />
            <a-table
              class="bmgx"
              :columns="columns6"
              :dataSource="showDataweek"
              :pagination="false"
              :loading="table3Loading"
              :rowKey="(record, index) => `${index + 1}`"
              bordered
            >
              <span slot="realname" slot-scope="text, record">
                <a-tooltip title="工效=文件数÷工时">
                  <span style="color: #ff9900" v-if="record.realname == '工效'" @click="clickweek(record)">{{ record.realname }}</span>
                  <span v-else>{{ record.realname }}</span></a-tooltip
                >
              </span>
            </a-table>
            <div id="el1" :style="[{ height: 304 + 'px' }, { width: '100%' }]"></div>
          </div>
          <!-- 年 -->
          <div v-else-if="EffectNum == 3">
            <a-date-picker
              v-model="pickerDefault6"
              placeholder="请选择年"
              :format="yearFormat"
              @openChange="openChange"
              @panelChange="panelChange"
              :open="open"
              mode="year"
              style="position: absolute; top: -49px; right: 0; width: 180px"
            />
            <a-table
              class="bmgx"
              :columns="columns7"
              :dataSource="showDatamonth"
              :pagination="false"
              :loading="table3Loading"
              :rowKey="(record, index) => `${index + 1}`"
              bordered
            >
              <span slot="realname" slot-scope="text, record">
                <a-tooltip title="工效=文件数÷工时">
                  <span style="color: #ff9900" v-if="record.realname == '工效'" @click="clickmonth(record)">{{ record.realname }}</span>
                  <span v-else>{{ record.realname }}</span></a-tooltip
                >
              </span>
            </a-table>
            <div id="el2" :style="[{ height: 304 + 'px' }, { width: '100%' }]"></div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="7" style="position: relative">
          <template #tab>
            <span>
              部门统计
              <a-tooltip title="导出数据">
                <a-icon type="download" @click="down6" />
              </a-tooltip>
            </span>
          </template>
          <a-month-picker
            v-model="pickerDefault7"
            @change="statisticschange"
            placeholder="请选择年月"
            :format="monthFormat"
            style="position: absolute; top: -49px; right: 0; width: 180px"
          />
          <a-select
            v-model="statisticsId"
            style="width: 120px; position: absolute; top: -49px; right: 190px"
            showSearch
            allowClear
            optionFilterProp="lable"
            @change="statisticsfactory"
            placeholder="授权工厂"
          >
            <a-select-option
              style="color: blue"
              v-for="(item, index) in mapKey(factroyList)"
              :key="index"
              :value="item.value"
              :lable="item.lable"
              :title="item.lable"
            >
              {{ item.lable }}
            </a-select-option>
          </a-select>
          <div style="height: 700px">
            <div style="height: 50%">
              <a-table
                :class="statisticsdata.length ? 'mintable2' : ''"
                :columns="columns8"
                :dataSource="statisticsdata"
                :customRow="onClickRow"
                :rowClassName="isRedRow"
                :loading="table8Loading"
                :pagination="false"
                :rowKey="(record, index) => `${index + 1}`"
                :scroll="{ y: 180 }"
                bordered
              >
              </a-table>
            </div>
            <div style="height: 50%; width: 100%; margin-top: 17px">
              <div id="Customerinquiryrate" class="bot"></div>
            </div>
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane key="6" style='position:relative'>
          <template #tab>
            <span>
              日历设置
            </span>
          </template>          
          <calendar-data ></calendar-data>
        </a-tab-pane> -->
      </a-tabs>
    </a-spin>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { TooltipComponent, LegendComponent } from "echarts/components";
import { PieChart } from "echarts/charts";
import { LabelLayout } from "echarts/features";
import { SVGRenderer } from "echarts/renderers";
echarts.use([TooltipComponent, LegendComponent, PieChart, SVGRenderer, LabelLayout]);
import {
  camAnalysis,
  laborCostAnalysis,
  costAnalysis,
  externalCamAnalysis,
  externalCamAnalysis1, //部门工效(日报表)接口
  externalCamAnalysis2, //人均工效接口
  externalCamAnalysisweek, //部门工效(周报表)接口
  externalCamAnalysismonth, //部门工效(月报表)接口
  factroyList,
  departordertotalanalys,
  externalQaeAnalysis2,
} from "@/services/dataPage";
//import OrderInfo from "@/pages/dashboard/dataPage/modules/OrderInfo";
//import calendarData from "@/pages/dashboard/dataPage/modules/calendarData";
import * as XLSX from "xlsx";
import moment from "moment";
// import { DatePicker } from 'element-ui'
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "顾客名称",
    dataIndex: "factoryName",
    align: "center",
    width: 100,
    scopedSlots: { customRender: "factoryName" },
  },
  {
    title: "汇总",
    children: [
      {
        title: "总制单量",
        dataIndex: "totalNum",
        align: "center",
        width: 100,
      },
      {
        title: "总收入",
        dataIndex: "totalIncome",
        align: "center",
        width: 100,
        customCell: record => {
          if (record.totalIncome != 0) {
            return { style: { color: "#FF9900" } };
          }
        },
      },
    ],
  },
  {
    title: "单面板",
    children: [
      {
        title: "单面板单量",
        dataIndex: "singlePanelNum",
        align: "center",
        width: 100,
      },
      {
        title: "单面板收入（元）",
        dataIndex: "singlePanelIncome",
        align: "center",
        width: 100,
      },
    ],
  },
  {
    title: "双面板",
    children: [
      {
        title: "双面板单量",
        dataIndex: "doublePanelNum",
        align: "center",
        width: 100,
      },
      {
        title: "双面板收入（元）",
        dataIndex: "doublePanelIncome",
        align: "center",
        width: 100,
      },
    ],
  },
  {
    title: "多层板",
    children: [
      {
        title: "多层板制单量",
        dataIndex: "manyPanelNum",
        align: "center",
        width: 100,
      },
      {
        title: "多层板收入（元）",
        dataIndex: "manyPanelIncome",
        align: "center",
        width: 100,
      },
    ],
  },
];
export default {
  name: "dataPage",
  data() {
    return {
      mondata: [],
      EffectNum: "1",
      selectedRowsData: {},
      statistics: [],
      columns,
      columns1: [
        {
          title: "组别",
          dataIndex: "team",
          align: "center",
          width: 100,
          key: "team",
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                // 列纵向合并
                rowSpan: row.teamRowSpan,
              },
            };
          },
        },
        {
          title: "CAM",
          dataIndex: "name",
          align: "center",
          width: 70,
        },
        {
          title: "前端/后端",
          dataIndex: "makegroup",
          align: "center",
          width: 110,
          filters: [
            { text: "前端 ", value: "前端" },
            { text: "后端", value: "后端" },
          ],
        },
        {
          title: "完成系数",
          dataIndex: "totalcost",
          align: "center",
          width: 100,
          customCell: record => {
            if (parseFloat(record.totalcost) < parseFloat(record.otherTotalcost)) {
              return { style: { color: "#ff0000" } };
            } else if (parseFloat(record.totalcost) >= parseFloat(record.otherTotalcost)) {
              return { style: { color: "#008000" } };
            }
          },
        },
        {
          title: "目标系数",
          dataIndex: "otherTotalcost",
          align: "center",
          width: 100,
        },
        {
          title: "制单目标（个/天）",
          dataIndex: "makeOrderPcsTarget",
          align: "center",
          width: 120,
        },
        {
          title: "实际制单总数量（个）",
          dataIndex: "totalNum",
          align: "center",
          width: 145,
        },
        {
          title: "单面板个数",
          dataIndex: "singlePanelNum",
          align: "center",
          width: 80,
        },
        {
          title: "单面板成本",
          dataIndex: "singlePanelIncome",
          align: "center",
          width: 80,
          // customCell: (record) => {
          //   if (record.singlePanelIncome != 0) {
          //     return { style: { 'color': '#FF9900' } }
          //   }
          // }
        },
        {
          title: "双面板个数",
          dataIndex: "doublePanelNum",
          align: "center",
          width: 80,
        },
        {
          title: "双面板成本",
          dataIndex: "doublePanelIncome",
          align: "center",
          width: 80,
          // customCell: (record) => {
          //   if (record.doublePanelIncome == 0) {
          //     return { style: { 'color': '#FF9900' } }
          //   }
          // }
        },
        {
          title: "多层板个数",
          dataIndex: "manyPanelNum",
          align: "center",
          width: 80,
        },
        {
          title: "多层成本",
          dataIndex: "manyPanelIncome",
          align: "center",
          width: 70,
          // customCell: (record) => {
          //   if (record.manyPanelIncome != 0) {
          //     return { style: { 'color': '#FF9900' } }
          //   }
          // }
        },
        {
          title: "报废量",
          dataIndex: "scrapNum",
          align: "center",
          width: 70,
        },
        {
          title: "报废扣款",
          dataIndex: "scrapDeduction",
          align: "center",
          width: 70,
        },
        {
          title: "客诉个数",
          dataIndex: "customerComplaintNum",
          align: "center",
          width: 70,
        },
        {
          title: "客诉扣款",
          dataIndex: "customerComplaintDeduction",
          align: "center",
          width: 70,
        },
        {
          title: "质量奖励",
          dataIndex: "qualityReward",
          align: "center",
        },
      ],
      columns4: [
        {
          title: "人员",
          dataIndex: "layers",
          align: "center",
          width: 60,
          key: "layers",
          fixed: "left",
          scopedSlots: { customRender: "layers" },
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                // 列纵向合并
                rowSpan: row.layersRowSpan,
              },
            };
          },
        },
        {
          title: "指标",
          //dataIndex: 'realname',
          scopedSlots: { customRender: "realname" },
          align: "center",
          fixed: "left",
          width: 65,
        },
        {
          title: "总数",
          dataIndex: "sum",
          align: "center",
          fixed: "left",
          width: 80,
        },
        {
          title: "平均",
          dataIndex: "average",
          align: "center",
          fixed: "left",
          width: 60,
        },
        {
          title: "1",
          dataIndex: "nums.1",
          align: "center",
          width: 60,
        },
        {
          title: "2",
          dataIndex: "nums.2",
          align: "center",
          width: 60,
        },
        {
          title: "3",
          dataIndex: "nums.3",
          align: "center",
          width: 60,
        },
        {
          title: "4",
          dataIndex: "nums.4",
          align: "center",
          width: 60,
        },
        {
          title: "5",
          dataIndex: "nums.5",
          align: "center",
          width: 60,
        },
        {
          title: "6",
          dataIndex: "nums.6",
          align: "center",
          width: 60,
        },
        {
          title: "7",
          dataIndex: "nums.7",
          align: "center",
          width: 60,
        },
        {
          title: "8",
          dataIndex: "nums.8",
          align: "center",
          width: 60,
        },
        {
          title: "9",
          dataIndex: "nums.9",
          align: "center",
          width: 60,
        },
        {
          title: "10",
          dataIndex: "nums.10",
          align: "center",
          width: 60,
        },
        {
          title: "11",
          dataIndex: "nums.11",
          align: "center",
          width: 60,
        },
        {
          title: "12",
          dataIndex: "nums.12",
          align: "center",
          width: 60,
        },
        {
          title: "13",
          dataIndex: "nums.13",
          align: "center",
          width: 60,
        },
        {
          title: "14",
          dataIndex: "nums.14",
          align: "center",
          width: 60,
        },
        {
          title: "15",
          dataIndex: "nums.15",
          align: "center",
          width: 60,
        },
        {
          title: "16",
          dataIndex: "nums.16",
          align: "center",
          width: 60,
        },
        {
          title: "17",
          dataIndex: "nums.17",
          align: "center",
          width: 60,
        },
        {
          title: "18",
          dataIndex: "nums.18",
          align: "center",
          width: 60,
        },
        {
          title: "19",
          dataIndex: "nums.19",
          align: "center",
          width: 60,
        },
        {
          title: "20",
          dataIndex: "nums.20",
          align: "center",
          width: 60,
        },
        {
          title: "21",
          dataIndex: "nums.21",
          align: "center",
          width: 60,
        },
        {
          title: "22",
          dataIndex: "nums.22",
          align: "center",
          width: 60,
        },
        {
          title: "23",
          dataIndex: "nums.23",
          align: "center",
          width: 60,
        },
        {
          title: "24",
          dataIndex: "nums.24",
          align: "center",
          width: 60,
        },
        {
          title: "25",
          dataIndex: "nums.25",
          align: "center",
          width: 60,
        },
        {
          title: "26",
          dataIndex: "nums.26",
          align: "center",
          width: 60,
        },
        {
          title: "27",
          dataIndex: "nums.27",
          align: "center",
          width: 60,
        },
        {
          title: "28",
          dataIndex: "nums.28",
          align: "center",
          width: 60,
        },
        {
          title: "29",
          dataIndex: "nums.29",
          align: "center",
          width: 60,
        },
        {
          title: "30",
          dataIndex: "nums.30",
          align: "center",
          width: 60,
        },
        {
          title: "31",
          dataIndex: "nums.31",
          align: "center",
          width: 60,
        },
      ],
      columns5: [
        {
          title: "工厂",
          dataIndex: "factroyName",
          ellipsis: true,
          align: "center",
          width: 80,
          key: "factroyName",
          scopedSlots: { customRender: "factroyName" },
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                rowSpan: row.factroyNameRowSpan,
              },
            };
          },
        },
        {
          title: "人员",
          dataIndex: "layers",
          ellipsis: true,
          align: "center",
          width: 70,
          key: "layers",
          scopedSlots: { customRender: "layers" },
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                rowSpan: row.layersRowSpan,
              },
            };
          },
        },
        {
          title: "指标",
          dataIndex: "realname",
          scopedSlots: { customRender: "realname" },
          align: "center",
          ellipsis: true,
          width: 60,
        },
        {
          title: "总数",
          dataIndex: "sum",
          align: "center",
          width: 50,
        },
        {
          title: "平均",
          dataIndex: "average",
          align: "center",
          width: 50,
        },
        {
          title: "1",
          scopedSlots: { customRender: "num1" },
          align: "center",
          width: 42,
        },
        {
          title: "2",
          scopedSlots: { customRender: "num2" },
          align: "center",
          width: 42,
        },
        {
          title: "3",
          scopedSlots: { customRender: "num3" },
          align: "center",
          width: 42,
        },
        {
          title: "4",
          scopedSlots: { customRender: "num4" },
          align: "center",
          width: 42,
        },
        {
          title: "5",
          scopedSlots: { customRender: "num5" },
          align: "center",
          width: 42,
        },
        {
          title: "6",
          scopedSlots: { customRender: "num6" },
          align: "center",
          width: 42,
        },
        {
          title: "7",
          scopedSlots: { customRender: "num7" },
          align: "center",
          width: 42,
        },
        {
          title: "8",
          scopedSlots: { customRender: "num8" },
          align: "center",
          width: 42,
        },
        {
          title: "9",
          scopedSlots: { customRender: "num9" },
          align: "center",
          width: 42,
        },
        {
          title: "10",
          scopedSlots: { customRender: "num10" },
          align: "center",
          width: 42,
        },
        {
          title: "11",
          scopedSlots: { customRender: "num11" },
          align: "center",
          width: 42,
        },
        {
          title: "12",
          scopedSlots: { customRender: "num12" },
          align: "center",
          width: 42,
        },
        {
          title: "13",
          scopedSlots: { customRender: "num13" },
          align: "center",
          width: 42,
        },
        {
          title: "14",
          scopedSlots: { customRender: "num14" },
          align: "center",
          width: 42,
        },
        {
          title: "15",
          scopedSlots: { customRender: "num15" },
          align: "center",
          width: 42,
        },
        {
          title: "16",
          scopedSlots: { customRender: "num16" },
          align: "center",
          width: 42,
        },
        {
          title: "17",
          scopedSlots: { customRender: "num17" },
          align: "center",
          width: 42,
        },
        {
          title: "18",
          scopedSlots: { customRender: "num18" },
          align: "center",
          width: 42,
        },
        {
          title: "19",
          scopedSlots: { customRender: "num19" },
          align: "center",
          width: 42,
        },
        {
          title: "20",
          scopedSlots: { customRender: "num20" },
          align: "center",
          width: 42,
        },
        {
          title: "21",
          scopedSlots: { customRender: "num21" },
          align: "center",
          width: 42,
        },
        {
          title: "22",
          scopedSlots: { customRender: "num22" },
          align: "center",
          width: 42,
        },
        {
          title: "23",
          scopedSlots: { customRender: "num23" },
          align: "center",
          width: 42,
        },
        {
          title: "24",
          scopedSlots: { customRender: "num24" },
          align: "center",
          width: 42,
        },
        {
          title: "25",
          scopedSlots: { customRender: "num25" },
          align: "center",
          width: 42,
        },
        {
          title: "26",
          scopedSlots: { customRender: "num26" },
          align: "center",
          width: 42,
        },
        {
          title: "27",
          scopedSlots: { customRender: "num27" },
          align: "center",
          width: 42,
        },
        {
          title: "28",
          scopedSlots: { customRender: "num28" },
          align: "center",
          width: 42,
        },
        {
          title: "29",
          scopedSlots: { customRender: "num29" },
          align: "center",
          width: 42,
        },
        {
          title: "30",
          scopedSlots: { customRender: "num30" },
          align: "center",
          width: 40,
        },
        {
          title: "31",
          scopedSlots: { customRender: "num31" },
          align: "center",
          width: 40,
        },
      ],
      columnsqae: [
        {
          title: "工厂",
          dataIndex: "factroyName",
          ellipsis: true,
          align: "center",
          width: 75,
          key: "factroyName",
          scopedSlots: { customRender: "factroyName" },
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                rowSpan: row.factroyNameRowSpan,
              },
            };
          },
        },
        {
          title: "人员",
          dataIndex: "layers",
          ellipsis: true,
          align: "center",
          width: 70,
          key: "layers",
          scopedSlots: { customRender: "layers" },
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                rowSpan: row.layersRowSpan,
              },
            };
          },
        },
        {
          title: "指标",
          dataIndex: "realname",
          scopedSlots: { customRender: "realname" },
          align: "center",
          ellipsis: true,
          width: 45,
        },
        {
          title: "总数",
          dataIndex: "sum",
          align: "center",
          width: 50,
        },
        {
          title: "平均",
          dataIndex: "average",
          align: "center",
          width: 50,
        },
        {
          title: "1",
          dataIndex: "nums.1",
          align: "center",
          width: 42,
        },
        {
          title: "2",
          dataIndex: "nums.2",
          align: "center",
          width: 42,
        },
        {
          title: "3",
          dataIndex: "nums.3",
          align: "center",
          width: 42,
        },
        {
          title: "4",
          dataIndex: "nums.4",
          align: "center",
          width: 42,
        },
        {
          title: "5",
          dataIndex: "nums.5",
          align: "center",
          width: 42,
        },
        {
          title: "6",
          dataIndex: "nums.6",
          align: "center",
          width: 42,
        },
        {
          title: "7",
          dataIndex: "nums.7",
          align: "center",
          width: 42,
        },
        {
          title: "8",
          dataIndex: "nums.8",
          align: "center",
          width: 42,
        },
        {
          title: "9",
          dataIndex: "nums.9",
          align: "center",
          width: 42,
        },
        {
          title: "10",
          dataIndex: "nums.10",
          align: "center",
          width: 42,
        },
        {
          title: "11",
          dataIndex: "nums.11",
          align: "center",
          width: 42,
        },
        {
          title: "12",
          dataIndex: "nums.12",
          align: "center",
          width: 42,
        },
        {
          title: "13",
          dataIndex: "nums.13",
          align: "center",
          width: 42,
        },
        {
          title: "14",
          dataIndex: "nums.14",
          align: "center",
          width: 42,
        },
        {
          title: "15",
          dataIndex: "nums.15",
          align: "center",
          width: 42,
        },
        {
          title: "16",
          dataIndex: "nums.16",
          align: "center",
          width: 42,
        },
        {
          title: "17",
          dataIndex: "nums.17",
          align: "center",
          width: 42,
        },
        {
          title: "18",
          dataIndex: "nums.18",
          align: "center",
          width: 42,
        },
        {
          title: "19",
          dataIndex: "nums.19",
          align: "center",
          width: 40,
        },
        {
          title: "20",
          dataIndex: "nums.20",
          align: "center",
          width: 42,
        },
        {
          title: "21",
          dataIndex: "nums.21",
          align: "center",
          width: 42,
        },
        {
          title: "22",
          dataIndex: "nums.22",
          align: "center",
          width: 42,
        },
        {
          title: "23",
          dataIndex: "nums.23",
          align: "center",
          width: 42,
        },
        {
          title: "24",
          dataIndex: "nums.24",
          align: "center",
          width: 42,
        },
        {
          title: "25",
          dataIndex: "nums.25",
          align: "center",
          width: 42,
        },
        {
          title: "26",
          dataIndex: "nums.26",
          align: "center",
          width: 42,
        },
        {
          title: "27",
          dataIndex: "nums.27",
          align: "center",
          width: 42,
        },
        {
          title: "28",
          dataIndex: "nums.28",
          align: "center",
          width: 42,
        },
        {
          title: "29",
          dataIndex: "nums.29",
          align: "center",
          width: 42,
        },
        {
          title: "30",
          dataIndex: "nums.30",
          align: "center",
          width: 40,
        },
        {
          title: "31",
          dataIndex: "nums.31",
          align: "center",
          width: 40,
        },
      ],
      columns8: [
        {
          title: "说明",
          dataIndex: "realname",
          customRender: (text, record, index) => (index == "2" || index == "4" ? `${record.realname}(%)` : record.realname),
          align: "center",
          width: 150,
        },
        {
          title: "汇总",
          dataIndex: "sum",
          align: "center",
          width: 70,
        },
        {
          title: "1",
          dataIndex: "nums.0.value",
          align: "center",
          width: 40,
        },
        {
          title: "2",
          dataIndex: "nums.1.value",
          align: "center",
          width: 40,
        },
        {
          title: "3",
          dataIndex: "nums.2.value",
          align: "center",
          width: 40,
        },
        {
          title: "4",
          dataIndex: "nums.3.value",
          align: "center",
          width: 40,
        },
        {
          title: "5",
          dataIndex: "nums.4.value",
          align: "center",
          width: 40,
        },
        {
          title: "6",
          dataIndex: "nums.5.value",
          align: "center",
          width: 40,
        },
        {
          title: "7",
          dataIndex: "nums.6.value",
          align: "center",
          width: 40,
        },
        {
          title: "8",
          dataIndex: "nums.7.value",
          align: "center",
          width: 40,
        },
        {
          title: "9",
          dataIndex: "nums.8.value",
          align: "center",
          width: 40,
        },
        {
          title: "10",
          dataIndex: "nums.9.value",
          align: "center",
          width: 40,
        },
        {
          title: "11",
          dataIndex: "nums.10.value",
          align: "center",
          width: 40,
        },
        {
          title: "12",
          dataIndex: "nums.11.value",
          align: "center",
          width: 40,
        },
        {
          title: "13",
          dataIndex: "nums.12.value",
          align: "center",
          width: 40,
        },
        {
          title: "14",
          dataIndex: "nums.13.value",
          align: "center",
          width: 40,
        },
        {
          title: "15",
          dataIndex: "nums.14.value",
          align: "center",
          width: 40,
        },
        {
          title: "16",
          dataIndex: "nums.15.value",
          align: "center",
          width: 40,
        },
        {
          title: "17",
          dataIndex: "nums.16.value",
          align: "center",
          width: 40,
        },
        {
          title: "18",
          dataIndex: "nums.17.value",
          align: "center",
          width: 40,
        },
        {
          title: "19",
          dataIndex: "nums.18.value",
          align: "center",
          width: 40,
        },
        {
          title: "20",
          dataIndex: "nums.19.value",
          align: "center",
          width: 40,
        },
        {
          title: "21",
          dataIndex: "nums.20.value",
          align: "center",
          width: 40,
        },
        {
          title: "22",
          dataIndex: "nums.21.value",
          align: "center",
          width: 40,
        },
        {
          title: "23",
          dataIndex: "nums.22.value",
          align: "center",
          width: 40,
        },
        {
          title: "24",
          dataIndex: "nums.23.value",
          align: "center",
          width: 40,
        },
        {
          title: "25",
          dataIndex: "nums.24.value",
          align: "center",
          width: 40,
        },
        {
          title: "26",
          dataIndex: "nums.25.value",
          align: "center",
          width: 40,
        },
        {
          title: "27",
          dataIndex: "nums.26.value",
          align: "center",
          width: 40,
        },
        {
          title: "28",
          dataIndex: "nums.27.value",
          align: "center",
          width: 40,
        },
        {
          title: "29",
          dataIndex: "nums.28.value",
          align: "center",
          width: 40,
        },
        {
          title: "30",
          dataIndex: "nums.29.value",
          align: "center",
          width: 40,
        },
        {
          title: "31",
          dataIndex: "nums.30.value",
          align: "center",
          width: 40,
        },
      ],
      copycolumns8: [
        {
          title: "说明",
          dataIndex: "realname",
          customRender: (text, record, index) => (index == "2" || index == "4" ? `${record.realname}(%)` : record.realname),
          align: "center",
          width: 150,
        },
        {
          title: "汇总",
          dataIndex: "sum",
          align: "center",
          width: 70,
        },
        {
          title: "1",
          dataIndex: "nums.0.value",
          align: "center",
          width: 40,
        },
        {
          title: "2",
          dataIndex: "nums.1.value",
          align: "center",
          width: 40,
        },
        {
          title: "3",
          dataIndex: "nums.2.value",
          align: "center",
          width: 40,
        },
        {
          title: "4",
          dataIndex: "nums.3.value",
          align: "center",
          width: 40,
        },
        {
          title: "5",
          dataIndex: "nums.4.value",
          align: "center",
          width: 40,
        },
        {
          title: "6",
          dataIndex: "nums.5.value",
          align: "center",
          width: 40,
        },
        {
          title: "7",
          dataIndex: "nums.6.value",
          align: "center",
          width: 40,
        },
        {
          title: "8",
          dataIndex: "nums.7.value",
          align: "center",
          width: 40,
        },
        {
          title: "9",
          dataIndex: "nums.8.value",
          align: "center",
          width: 40,
        },
        {
          title: "10",
          dataIndex: "nums.9.value",
          align: "center",
          width: 40,
        },
        {
          title: "11",
          dataIndex: "nums.10.value",
          align: "center",
          width: 40,
        },
        {
          title: "12",
          dataIndex: "nums.11.value",
          align: "center",
          width: 40,
        },
        {
          title: "13",
          dataIndex: "nums.12.value",
          align: "center",
          width: 40,
        },
        {
          title: "14",
          dataIndex: "nums.13.value",
          align: "center",
          width: 40,
        },
        {
          title: "15",
          dataIndex: "nums.14.value",
          align: "center",
          width: 40,
        },
        {
          title: "16",
          dataIndex: "nums.15.value",
          align: "center",
          width: 40,
        },
        {
          title: "17",
          dataIndex: "nums.16.value",
          align: "center",
          width: 40,
        },
        {
          title: "18",
          dataIndex: "nums.17.value",
          align: "center",
          width: 40,
        },
        {
          title: "19",
          dataIndex: "nums.18.value",
          align: "center",
          width: 40,
        },
        {
          title: "20",
          dataIndex: "nums.19.value",
          align: "center",
          width: 40,
        },
        {
          title: "21",
          dataIndex: "nums.20.value",
          align: "center",
          width: 40,
        },
        {
          title: "22",
          dataIndex: "nums.21.value",
          align: "center",
          width: 40,
        },
        {
          title: "23",
          dataIndex: "nums.22.value",
          align: "center",
          width: 40,
        },
        {
          title: "24",
          dataIndex: "nums.23.value",
          align: "center",
          width: 40,
        },
        {
          title: "25",
          dataIndex: "nums.24.value",
          align: "center",
          width: 40,
        },
        {
          title: "26",
          dataIndex: "nums.25.value",
          align: "center",
          width: 40,
        },
        {
          title: "27",
          dataIndex: "nums.26.value",
          align: "center",
          width: 40,
        },
        {
          title: "28",
          dataIndex: "nums.27.value",
          align: "center",
          width: 40,
        },
        {
          title: "29",
          dataIndex: "nums.28.value",
          align: "center",
          width: 40,
        },
        {
          title: "30",
          dataIndex: "nums.29.value",
          align: "center",
          width: 40,
        },
        {
          title: "31",
          dataIndex: "nums.30.value",
          align: "center",
          width: 40,
        },
      ],
      columns6: [
        {
          title: "人员",
          dataIndex: "layers",
          align: "center",
          width: 50,
          key: "layers",
          //fixed: 'left',
          scopedSlots: { customRender: "layers" },
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                // 列纵向合并
                rowSpan: row.layersRowSpan,
              },
            };
          },
        },
        {
          title: "指标",
          //dataIndex: 'realname',
          scopedSlots: { customRender: "realname" },
          align: "center",
          //fixed: 'left',
          width: 50,
        },
        {
          title: "总数",
          dataIndex: "sum",
          align: "center",
          width: 60,
        },
        {
          title: "平均",
          dataIndex: "average",
          align: "center",
          width: 60,
        },
        {
          title: "1周",
          dataIndex: "nums.1",
          align: "center",
          width: 50,
        },
        {
          title: "2周",
          dataIndex: "nums.2",
          align: "center",
          width: 50,
        },
        {
          title: "3周",
          dataIndex: "nums.3",
          align: "center",
          width: 50,
        },
        {
          title: "4周",
          dataIndex: "nums.4",
          align: "center",
          width: 50,
        },
        {
          title: "5周",
          dataIndex: "nums.5",
          align: "center",
          width: 50,
        },
      ],
      columns7: [
        {
          title: "人员",
          dataIndex: "layers",
          align: "center",
          width: 50,
          key: "layers",
          //fixed: 'left',
          scopedSlots: { customRender: "layers" },
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                // 列纵向合并
                rowSpan: row.layersRowSpan,
              },
            };
          },
        },
        {
          title: "指标",
          //dataIndex: 'realname',
          scopedSlots: { customRender: "realname" },
          align: "center",
          //fixed: 'left',
          width: 50,
        },
        {
          title: "总数",
          dataIndex: "sum",
          align: "center",
          //fixed: 'left',
          width: 60,
        },
        {
          title: "平均",
          dataIndex: "average",
          align: "center",
          //fixed: 'left',
          width: 60,
        },
        {
          title: "1月",
          dataIndex: "nums.1",
          align: "center",
          width: 50,
        },
        {
          title: "2月",
          dataIndex: "nums.2",
          align: "center",
          width: 50,
        },
        {
          title: "3月",
          dataIndex: "nums.3",
          align: "center",
          width: 50,
        },
        {
          title: "4月",
          dataIndex: "nums.4",
          align: "center",
          width: 50,
        },
        {
          title: "5月",
          dataIndex: "nums.5",
          align: "center",
          width: 50,
        },
        {
          title: "6月",
          dataIndex: "nums.6",
          align: "center",
          width: 50,
        },
        {
          title: "7月",
          dataIndex: "nums.7",
          align: "center",
          width: 50,
        },
        {
          title: "8月",
          dataIndex: "nums.8",
          align: "center",
          width: 50,
        },
        {
          title: "9月",
          dataIndex: "nums.9",
          align: "center",
          width: 50,
        },
        {
          title: "10月",
          dataIndex: "nums.10",
          align: "center",
          width: 50,
        },
        {
          title: "11月",
          dataIndex: "nums.11",
          align: "center",
          width: 50,
        },
        {
          title: "12月",
          dataIndex: "nums.12",
          align: "center",
          width: 50,
        },
      ],
      data1Source: [],
      data2Source: [],
      activeKey: "5",
      spinning: false,
      table1Loading: false,
      table2Loading: false,
      table3Loading: false,
      table8Loading: false,
      CostAnalysisData: {
        nbSales: {
          summoney: "",
          proportion: "",
        },
        wbSales: {
          summoney: "",
          proportion: "",
        },
        conversion: {
          summoney: "",
          proportion: "",
        },
        totalSales: {
          summoney: "",
          proportion: "",
        },
        nbFileNum: {
          summoney: "",
          proportion: "",
        },
        nbHdFileNum: {
          summoney: "",
          proportion: "",
        },
        wbFileNum: {
          summoney: "",
          proportion: "",
        },
        wbHdFileNum: {
          summoney: "",
          proportion: "",
        },
        totalFileNum: {
          summoney: "",
          proportion: "",
        },
        averageprice: {
          summoney: "",
          proportion: "",
        },
        passThroughRate: {
          summoney: "",
          proportion: "",
        },
        nblaborcost: {
          summoney: "",
          proportion: "",
        },
        wblaborcost: {
          summoney: "",
          proportion: "",
        },
        totallaborcost: {
          summoney: "",
          proportion: "",
        },
        totalpeoplenum: {
          summoney: "",
          proportion: "",
        },
        percapitaoutputPcs: {
          summoney: "",
          proportion: "",
        },
        percapitaoutputYuan: {
          summoney: "",
          proportion: "",
        },
        grossprofit: {
          summoney: "",
          proportion: "",
        },
      }, // 当月制作管理分析总表数据
      pickerDefault: [moment().subtract(1, "days"), moment().subtract(1, "days")],
      pickerDefault1: [moment().startOf("month").format("YYYY-MM-DD"), moment().endOf("month").format("YYYY-MM-DD")],
      pickerDefault2: moment(new Date(), "YYYY/MM"),
      pickerDefault3: moment(new Date(), "YYYY/MM"),
      pickerDefault8: moment(new Date(), "YYYY/MM"),
      pickerDefault4: moment(new Date(), "YYYY/MM"),
      pickerDefault5: moment(new Date(), "YYYY/MM"),
      pickerDefault6: moment(new Date(), "YYYY"),
      pickerDefault7: moment(new Date(), "YYYY/MM"),
      selectDate: moment().startOf("month").format("YYYY/MM"),
      monthFormat: "YYYY/MM",
      yearFormat: "YYYY",
      showMode: false,
      time: "",
      showData: [
        {
          date: "1",
          count: "",
          mony: "",
        },
        {
          date: "2",
          count: "",
          mony: "",
        },
        {
          date: "3",
          count: "",
          mony: "",
        },
        {
          date: "4",
          count: "",
          mony: "",
        },
        {
          date: "5",
          count: "",
          mony: "",
        },
        {
          date: "6",
          count: "",
          mony: "",
        },
        {
          date: "7",
          count: "",
          mony: "",
        },
        {
          date: "8",
          count: "",
          mony: "",
        },
        {
          date: "9",
          count: "",
          mony: "",
        },
        {
          date: "10",
          count: "",
          mony: "",
        },
        {
          date: "11",
          count: "",
          mony: "",
        },
        {
          date: "12",
          count: "",
          mony: "",
        },
        {
          date: "13",
          count: "",
          mony: "",
        },
        {
          date: "14",
          count: "",
          mony: "",
        },
        {
          date: "15",
          count: "",
          mony: "",
        },
        {
          date: "16",
          count: "",
          mony: "",
        },
        {
          date: "17",
          count: "",
          mony: "",
        },
        {
          date: "18",
          count: "",
          mony: "",
        },
        {
          date: "19",
          count: "",
          mony: "",
        },
        {
          date: "20",
          count: "",
          mony: "",
        },
        {
          date: "21",
          count: "",
          mony: "",
        },
        {
          date: "22",
          count: "",
          mony: "",
        },
        {
          date: "23",
          count: "",
          mony: "",
        },
        {
          date: "24",
          count: "",
          mony: "",
        },
        {
          date: "25",
          count: "",
          mony: "",
        },
        {
          date: "26",
          count: "",
          mony: "",
        },
        {
          date: "27",
          count: "",
          mony: "",
        },
        {
          date: "28",
          count: "",
          mony: "",
        },
        {
          date: "29",
          count: "",
          mony: "",
        },
        {
          date: "30",
          count: "",
          mony: "",
        },
        {
          date: "31",
          count: "",
          mony: "",
        },
        {
          date: "总计",
          count: "",
          mony: "",
        },
      ],
      showData1: [], //部门工效(日报表)数据展示
      showData2: [], //个人工效数据展示
      showDataqae: [], //个人工效QAE数据展示
      showDataweek: [], //部门工效(周报表)数据展示
      showDatamonth: [], //部门工效(月报表)数据展示
      statisticsdata: [], //部门统计
      Sdata: "",
      enddate: "",
      Sdata1: "",
      enddate1: "",
      data2SourceCopy: [],
      sacteam: "",
      lineData: {},
      date4: "",
      date8: "",
      statisticsdate: moment().startOf("month").format("YYYY/MM"),
      lineData1: [],
      lineDataweek: [], //周点击折线图数据
      lineDatamonth: [], //月点击折线图数据
      open: false,
      factroyList: [],
      FactoryId: undefined,
      FactoryIdqae: undefined,
      FactoryId1: undefined,
      statisticsId: undefined,
    };
  },
  filters: {
    // 折线图数据
    th_barData: function (data) {
      const value = this.showData1.find(item => {
        return item.realname == "工效";
      }).num;
    },
  },
  watch: {
    activeKey(key) {
      this.pickerDefault = null;
      this.pickerDefault1 = null;
      this.pickerDefault2 = null;
      this.pickerDefault3 = null;
      this.pickerDefault8 = null;
      this.pickerDefault4 = null;
      this.pickerDefault5 = null;
      this.pickerDefault6 = null;
      this.pickerDefault7 = null;
      if (key == 1) {
        this.pickerDefault2 = moment(new Date(), "YYYY-MM");
      }
      if (key == 2) {
        this.pickerDefault1 = [moment().startOf("month").format("YYYY-MM-DD"), moment().endOf("month").format("YYYY-MM-DD")];
        this.getCamAnalysis(moment().startOf("month").format("YYYY-MM-DD"), moment().endOf("month").format("YYYY-MM-DD"));
      }
      if (key == 3) {
        (this.pickerDefault = [moment().subtract(1, "days"), moment().subtract(1, "days")]),
          this.getLaborCostAnalysis(moment().subtract(1, "days").format("YYYY-MM-DD"), moment().subtract(1, "days").format("YYYY-MM-DD"));
      }

      if (key == 4) {
        this.pickerDefault4 = moment(new Date(), "YYYY-MM");
        this.Factorychange1();
      }

      if (key == 5) {
        this.pickerDefault3 = moment(new Date(), "YYYY/MM");
        this.FactoryId = undefined;
        this.FactoryIdqae = undefined;
        this.getLaborCostAnalysis2();
      }
      if (key == 7) {
        this.pickerDefault7 = moment(new Date(), "YYYY/MM");
        this.statisticsId = undefined;
        this.getLaborCostAnalysis3();
      }
      if (key == 8) {
        this.pickerDefault8 = moment(new Date(), "YYYY/MM");
        this.FactoryIdqae = undefined;
        this.FactoryId = undefined;
        this.getqaedata();
      }
    },
  },
  methods: {
    TDStyle(record, index, num) {
      if (
        record.realname == "个数" &&
        this.showData2[index + 2] &&
        this.showData2[index + 2].realname == "目标" &&
        Number(record.nums[num]) < Number(this.showData2[index + 2].nums[num]) &&
        record.layers != "合计" &&
        record.nums[num]
      ) {
        return 1;
      } else {
        return 0;
      }
    },
    getRowClass(index) {
      const group = Math.floor(index / 4); // 计算当前行属于哪一组
      return group % 2 === 0 ? "gray-row" : "white-row"; // 根据组的奇偶性返回不同的类名
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.realname && record.realname == this.selectedRowsData.realname) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record, index) {
      return {
        on: {
          click: () => {
            this.statistics = [];
            this.selectedRowsData = record;
            this.selectedRowsData.nums.forEach(item => {
              if (item.value == "" && item.holiday == "0") {
                this.statistics.push(0);
              } else if (item.holiday == "0") {
                this.statistics.push(item.value);
              }
            });
            this.Customerinquiryrate(index);
          },
        },
      };
    },
    Customerinquiryrate(index) {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            data: this.mondata,
          },
          yAxis: {},
          grid: {
            left: "3%", // 左边距
            right: "3%", // 右边距
            top: "25%", // 上边距
            bottom: "6%", // 下边距
          },
          color: [" green"],
          legend: {
            data: [this.statisticsdata[index].realname],
            textStyle: {
              color: "rgb(121,128,137)",
              fontSize: 12,
            },
            top: "0%", // 图例的位置
          },
          series: {
            name: this.statisticsdata[index].realname,
            data: this.statistics,
            type: "line",
            symbolSize: 8,
          },
        };
        let chartDom = document.getElementById("Customerinquiryrate");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    handleChangeEffect(value) {
      if (value == 1) {
        this.pickerDefault = null;
        this.pickerDefault1 = null;
        this.pickerDefault2 = null;
        this.pickerDefault3 = null;
        this.pickerDefault4 = moment(new Date(), "YYYY-MM");
        this.pickerDefault5 = null;
        this.pickerDefault8 = null;
        this.pickerDefault6 = null;
        this.getLaborCostAnalysis1();
      } else if (value == 2) {
        this.pickerDefault = null;
        this.pickerDefault1 = null;
        this.pickerDefault8 = null;
        this.pickerDefault2 = null;
        this.pickerDefault3 = null;
        this.pickerDefault4 = null;
        this.pickerDefault5 = moment(new Date(), "YYYY/MM");
        this.pickerDefault6 = null;
        this.getLaborCostAnalysisweek();
      } else {
        this.pickerDefault = null;
        this.pickerDefault1 = null;
        this.pickerDefault2 = null;
        this.pickerDefault8 = null;
        this.pickerDefault3 = null;
        this.pickerDefault4 = null;
        this.pickerDefault5 = null;
        this.pickerDefault6 = moment(new Date(), "YYYY");
        this.getLaborCostAnalysismonth();
      }
    },
    // 获取当月制作管理分析总表数据
    getCostAnalysis(dateString) {
      this.spinning = true;
      costAnalysis(dateString || "")
        .then(res => {
          if (res.code == 1) {
            this.CostAnalysisData = res.data;
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 获取当月外接CAM分析数据
    getCamAnalysis(a, b) {
      camAnalysis(a || moment().startOf("month").format("YYYY-MM-DD"), b || moment().endOf("month").format("YYYY-MM-DD"))
        .then(res => {
          if (res.code == 1) {
            this.data1Source = res.data;
          }
        })
        .finally(() => {
          this.table1Loading = false;
        });
    },
    // 获取当月人工成本分析数据
    getLaborCostAnalysis(a, b, c) {
      this.table2Loading = true;
      laborCostAnalysis(a || moment().subtract(1, "days").format("YYYY-MM-DD"), b || moment().subtract(1, "days").format("YYYY-MM-DD"), c || "")
        .then(res => {
          if (res.code == 1) {
            this.data2Source = res.data;
            let arr_ = [];
            this.data2Source.forEach(item => {
              item.labors.forEach((ite, index) => {
                let obj_ = ite;
                obj_["team"] = item.team;
                arr_.push(obj_);
              });
            });
            this.data2Source = arr_;
            this.data2SourceCopy = arr_;
            this.rowSpan("team");
          }
        })
        .finally(() => {
          this.table2Loading = false;
        });
    },
    //获取品类工效(日报表)
    getLaborCostAnalysis1(date) {
      this.table3Loading = true;
      let params = "";
      if (date) {
        params = date;
      }
      let FactoryId1 = "";
      if (this.FactoryId1) {
        FactoryId1 = this.FactoryId1;
      }
      externalCamAnalysis1(params, FactoryId1)
        .then(res => {
          if (res.code) {
            this.showData1 = res.data;
            let arr = [];
            this.showData1.forEach(item => {
              item["name"].forEach(subItem => {
                arr.push({
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                });
              });
            });
            arr.forEach(item => {
              let obj = {};
              item["nums"].forEach(ite => {
                obj[ite.date] = ite.value;
              });
              item["nums"] = obj;
            });
            this.showData1 = arr;
            this.lineData = arr.find(item => {
              return item.realname == "工效";
            }).nums;
            this.rowSpan1("layers");
            this.initData1();
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    click(record) {
      let arr = [];
      this.lineData = Object.values(record.nums);
      this.lineData.forEach(item => {
        arr.push(item);
      });
      this.lineData = arr;
      if (record.layers == "前端") {
        this.initData1();
      } else {
        this.initData2();
      }
    },
    //获取品类工效(周报表)
    getLaborCostAnalysisweek(date) {
      this.table3Loading = true;
      let params = "";
      if (date) {
        params = date;
      }
      let FactoryId1 = "";
      if (this.FactoryId1) {
        FactoryId1 = this.FactoryId1;
      }
      externalCamAnalysisweek(params, FactoryId1)
        .then(res => {
          if (res.code) {
            this.showDataweek = res.data;
            let arr = [];
            this.showDataweek.forEach(item => {
              item["name"].forEach(subItem => {
                arr.push({
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                });
              });
            });
            arr.forEach(item => {
              let obj = {};
              item["nums"].forEach(ite => {
                obj[ite.date] = ite.value;
              });
              item["nums"] = obj;
            });
            this.showDataweek = arr;
            this.lineDataweek = arr.find(item => {
              return item.realname == "工效";
            }).nums;
            this.rowSpanweek("layers");
            this.initDataweek();
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    clickweek(record) {
      let arr = [];
      this.lineDataweek = Object.values(record.nums);
      this.lineDataweek.forEach(item => {
        arr.push(item);
      });
      this.lineDataweek = arr;
      if (record.layers == "前端") {
        this.initDataweek();
      } else if (record.layers == "后端") {
        this.initDataweekhd();
      }
    },
    //获取品类工效(月报表)
    getLaborCostAnalysismonth(date) {
      this.table3Loading = true;
      let params = "";
      if (date) {
        params = date;
      }
      let FactoryId1 = "";
      if (this.FactoryId1) {
        FactoryId1 = this.FactoryId1;
      }
      externalCamAnalysismonth(params, FactoryId1)
        .then(res => {
          if (res.code) {
            this.showDatamonth = res.data;
            let arr = [];
            this.showDatamonth.forEach(item => {
              item["name"].forEach(subItem => {
                arr.push({
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                });
              });
            });
            arr.forEach(item => {
              let obj = {};
              item["nums"].forEach(ite => {
                obj[ite.date] = ite.value;
              });
              item["nums"] = obj;
            });
            this.showDatamonth = arr;
            this.lineDatamonth = arr.find(item => {
              return item.realname == "工效";
            }).nums;
            this.rowSpanmonth("layers");
            this.initDatamonth();
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    clickmonth(record) {
      let arr = [];
      this.lineDatamonth = Object.values(record.nums);
      this.lineDatamonth.forEach(item => {
        arr.push(item);
      });
      this.lineDatamonth = arr;
      if (record.layers == "前端") {
        this.initDatamonth();
      } else if (record.layers == "后端") {
        this.initDatamonth2();
      }
    },
    Factorychange() {
      this.getLaborCostAnalysis2();
    },
    Factorychangeqae() {
      this.getqaedata();
    },
    statisticsfactory() {
      this.getLaborCostAnalysis3();
    },
    Factorychange1() {
      if (this.EffectNum == 1) {
        this.getLaborCostAnalysis1();
      } else if (this.EffectNum == 2) {
        this.getLaborCostAnalysisweek();
      } else {
        this.getLaborCostAnalysismonth();
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    //人均工效
    getLaborCostAnalysis2(date) {
      this.table3Loading = true;
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.date4) {
        params.date = this.date4;
      }
      if (this.FactoryId) {
        params.FactoryId = this.FactoryId;
      }
      externalCamAnalysis2(params)
        .then(res => {
          if (res.code) {
            this.showData2 = res.data.items;
            let arr = [];
            this.showData2.forEach(item => {
              item["name"].forEach(subItem => {
                // if(item['layers']=='合计' || subItem['realname']!='达成率'){
                arr.push({
                  factroyName: item["factroyName"],
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                  score: subItem["score"],
                });
                // }
              });
            });
            arr.forEach(item => {
              let obj = {};
              item["nums"].forEach(ite => {
                obj["num" + ite.date] = ite.value;
              });
              item["nums"] = obj;
            });
            this.showData2 = arr;
            this.rowSpan2("factroyName", "cam");
            this.rowSpan2("layers", "cam");
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    getqaedata() {
      this.table3Loading = true;
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.date8) {
        params.date = this.date8;
      }
      if (this.FactoryIdqae) {
        params.FactoryId = this.FactoryIdqae;
      }
      externalQaeAnalysis2(params)
        .then(res => {
          if (res.code) {
            this.showDataqae = res.data.items;
            let arr = [];
            this.showDataqae.forEach(item => {
              item["name"].forEach(subItem => {
                arr.push({
                  factroyName: item["factroyName"],
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                  score: subItem["score"],
                });
              });
            });
            arr.forEach(item => {
              let obj = {};
              item["nums"].forEach(ite => {
                obj[ite.date] = ite.value;
              });
              item["nums"] = obj;
            });
            this.showDataqae = arr;
            this.rowSpan2("factroyName", "qae");
            this.rowSpan2("layers", "qae");
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    //部门统计
    getLaborCostAnalysis3(date) {
      this.table8Loading = true;
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.statisticsdate) {
        params.date = this.statisticsdate;
      }
      if (this.statisticsId) {
        params.FactoryId = this.statisticsId;
      }
      this.mondata = [];
      this.statistics = [];
      departordertotalanalys(params)
        .then(res => {
          if (res.code) {
            this.statisticsdata = res.data;
            this.selectedRowsData = this.statisticsdata[0];
            this.statisticsdata[0].nums.forEach(item => {
              if (item.value == "" && item.holiday == "0") {
                this.statistics.push(0);
              } else if (item.holiday == "0") {
                this.statistics.push(item.value);
              }
              if (item.holiday == "0") {
                this.mondata.push(item.date);
              }
            });
            localStorage.setItem("copycolumns8", JSON.stringify(this.copycolumns8));
            this.columns8 = JSON.parse(localStorage.getItem("copycolumns8"));
            for (let i = this.columns8.length - 1; i >= 0; i--) {
              if (this.columns8[i].title != "说明" && this.columns8[i].title != "汇总" && !this.mondata.includes(this.columns8[i].title)) {
                this.columns8.splice(i, 1);
              }
            }
            this.Customerinquiryrate(0);
          }
        })
        .finally(() => {
          this.table8Loading = false;
        });
    },
    // 订单表变化change
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.getLaborCostAnalysis2(this.date4);
    },
    initData() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        xAxis: {
          type: "category",
          data: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23",
            "24",
            "25",
            "26",
            "27",
            "28",
            "29",
            "30",
            "31",
          ],
        },
        yAxis: {
          type: "value",
          axisTick: {
            //y轴刻度线
            show: false,
          },
          name: "部门工效(日报表)",
          min: 0,
          max: 30,
          interval: 5,
          axisLabel: {
            formatter: "{value}",
          },
        },
        series: [
          {
            data: Object.values(this.lineData),
            type: "line",
            markLine: {},
          },
        ],
        grid: {
          left: "6%", // 左边距
          right: "4%", // 右边距
          top: "15%", // 上边距
          bottom: "15%", // 下边距
        },
      };
      myChart.setOption(option);
    },
    initData1() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        grid: {
          left: "6%", // 左边距
          right: "4%", // 右边距
          top: "15%", // 上边距
          bottom: "15%", // 下边距
        },
        xAxis: {
          type: "category",
          data: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23",
            "24",
            "25",
            "26",
            "27",
            "28",
            "29",
            "30",
            "31",
          ],
        },
        yAxis: {
          type: "value",
          axisTick: {
            //y轴刻度线
            show: false,
          },
          name: "部门工效(日报表)-前端",
          min: 0,
          max: 20,
          interval: 5,
          axisLabel: {
            formatter: "{value}",
          },
        },
        series: [
          {
            data: Object.values(this.lineData),
            type: "line",
            markLine: {
              data: [
                [
                  { name: "人效标准", value: "12.98", xAxis: 0, yAxis: 12.98, symbol: "circle" },
                  { name: "人效标准", value: "12.98", xAxis: "31", yAxis: 12.98, symbol: "circle" },
                ],
              ],
              label: {
                normal: {
                  show: true,
                  position: "end",
                  fontSize: 14,
                  formatter: "前端工效标准(日)",
                },
              },
              lineStyle: {
                normal: {
                  type: "solid",
                  color: "green",
                  width: 3,
                },
              },
            },
            itemStyle: {
              normal: {
                color: "#5470C6",
              },
            },
          },
        ],
      };
      // {
      //   data: [12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98],
      //   type: 'line'
      // }
      myChart.setOption(option);
    },
    initData2() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        xAxis: {
          type: "category",
          data: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23",
            "24",
            "25",
            "26",
            "27",
            "28",
            "29",
            "30",
            "31",
          ],
        },
        yAxis: {
          type: "value",
          axisTick: {
            //y轴刻度线
            show: false,
          },
          name: "部门工效(日报表)-后端",
          min: 0,
          max: 60,
          interval: 10,
          axisLabel: {
            formatter: "{value}",
          },
        },
        series: [
          {
            data: Object.values(this.lineData),
            type: "line",
            markLine: {
              data: [
                [
                  { name: "人效标准", value: "29.26", xAxis: 0, yAxis: 29.26, symbol: "circle" },
                  { name: "人效标准", value: "29.26", xAxis: "31", yAxis: 29.26, symbol: "circle" },
                ],
              ],
              label: {
                normal: {
                  show: true,
                  position: "end",
                  fontSize: 14,
                  formatter: "后端工效标准(日)",
                },
              },
              lineStyle: {
                normal: {
                  type: "solid",
                  color: "green",
                  width: 3,
                },
              },
            },
            itemStyle: {
              normal: {
                color: "#5470C6",
              },
            },
          },
        ],
        grid: {
          left: "6%", // 左边距
          right: "4%", // 右边距
          top: "15%", // 上边距
          bottom: "15%", // 下边距
        },
      };
      // {
      //   data: [12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98,12.98],
      //   type: 'line'
      // }
      myChart.setOption(option);
    },
    initDataweek() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el1"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        xAxis: {
          type: "category",
          data: ["1", "2", "3", "4", "5"],
        },
        yAxis: {
          type: "value",
          axisTick: {
            //y轴刻度线
            show: false,
          },
          name: "部门工效(周报表)-前端",
          min: 0,
          max: 15,
          interval: 3,
          axisLabel: {
            formatter: "{value}",
          },
        },
        series: [
          {
            data: Object.values(this.lineDataweek),
            type: "line",
            markLine: {
              data: [
                [
                  { name: "人效标准", value: "9.24", xAxis: 0, yAxis: 9.24, symbol: "circle" },
                  { name: "人效标准", value: "9.24", xAxis: "5", yAxis: 9.24, symbol: "circle" },
                ],
              ],
              label: {
                normal: {
                  show: true,
                  position: "end",
                  fontSize: 18,
                  formatter: "前端人效标准（周）",
                },
              },
              lineStyle: {
                normal: {
                  type: "solid",
                  color: "green",
                  width: 3,
                },
              },
            },
            itemStyle: {
              normal: {
                color: "#5470C6",
              },
            },
          },
        ],
        grid: {
          left: "4%",
          right: "4%",
        },
      };
      myChart.setOption(option);
    },
    initDataweekhd() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el1"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        xAxis: {
          type: "category",
          data: ["1", "2", "3", "4", "5"],
        },
        yAxis: {
          type: "value",
          axisTick: {
            //y轴刻度线
            show: false,
          },
          name: "部门工效(周报表)-后端",
          min: 0,
          max: 40,
          interval: 10,
          axisLabel: {
            formatter: "{value}",
          },
        },
        series: [
          {
            data: Object.values(this.lineDataweek),
            type: "line",
            markLine: {
              data: [
                [
                  { name: "人效标准", value: "20.79", xAxis: 0, yAxis: 20.79, symbol: "circle" },
                  { name: "人效标准", value: "20.79", xAxis: "5", yAxis: 20.79, symbol: "circle" },
                ],
              ],
              label: {
                normal: {
                  show: true,
                  position: "end",
                  fontSize: 18,
                  formatter: "后端人效标准（周）",
                },
              },
              lineStyle: {
                normal: {
                  type: "solid",
                  color: "green",
                  width: 3,
                },
              },
            },
            itemStyle: {
              normal: {
                color: "#5470C6",
              },
            },
          },
        ],
        grid: {
          left: "4%",
          right: "4%",
        },
      };
      myChart.setOption(option);
    },
    initDatamonth() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el2"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        xAxis: {
          type: "category",
          data: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
        },
        yAxis: {
          type: "value",
          axisTick: {
            //y轴刻度线
            show: false,
          },
          name: "部门工效(月报表)-前端",
          min: 0,
          max: 15,
          interval: 3,
          axisLabel: {
            formatter: "{value}",
          },
        },
        series: [
          {
            data: Object.values(this.lineDatamonth),
            type: "line",
            markLine: {
              data: [
                [
                  { name: "人效标准", value: "6.38", xAxis: 0, yAxis: 6.38, symbol: "circle" },
                  { name: "人效标准", value: "6.38", xAxis: "12", yAxis: 6.38, symbol: "circle" },
                ],
              ],
              label: {
                normal: {
                  show: true,
                  position: "end",
                  fontSize: 15,
                  formatter: "前端人效标准（月）",
                },
              },
              lineStyle: {
                normal: {
                  type: "solid",
                  color: "green",
                  width: 3,
                },
              },
            },
            itemStyle: {
              normal: {
                color: "#5470C6",
              },
            },
          },
        ],
        grid: {
          left: "4%",
          right: "4%",
        },
      };
      myChart.setOption(option);
    },
    initDatamonth2() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el2"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        xAxis: {
          type: "category",
          data: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
        },
        yAxis: {
          type: "value",
          axisTick: {
            //y轴刻度线
            show: false,
          },
          name: "部门工效(月报表)-后端",
          min: 0,
          max: 25,
          interval: 5,
          axisLabel: {
            formatter: "{value}",
          },
        },
        series: [
          {
            data: Object.values(this.lineDatamonth),
            type: "line",
            markLine: {
              data: [
                [
                  { name: "人效标准", value: "14.63", xAxis: 0, yAxis: 14.63, symbol: "circle" },
                  { name: "人效标准", value: "14.63", xAxis: "12", yAxis: 14.63, symbol: "circle" },
                ],
              ],
              label: {
                normal: {
                  show: true,
                  position: "end",
                  fontSize: 18,
                  formatter: "后端人效标准（月）",
                },
              },
              lineStyle: {
                normal: {
                  type: "solid",
                  color: "green",
                  width: 3,
                },
              },
            },
            itemStyle: {
              normal: {
                color: "#5470C6",
              },
            },
          },
        ],
        grid: {
          left: "4%",
          right: "4%",
        },
      };
      myChart.setOption(option);
    },
    // 此方法可作为公用方法，统一文件管理
    rowSpan(key) {
      let _list = this.data2Source;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        //  最后一条的 +1 时会为undefind 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); //获取相同的 下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              //其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          //  当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          //当与下一条数据不同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.data2Source = _list;
    },
    //合并列
    rowSpan1(key) {
      let _list = this.showData1;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        //  最后一条的 +1 时会为undefind 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); //获取相同的 下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              //其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          //  当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          //当与下一条数据不同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.showData1 = _list;
    },
    rowSpan2(key, type) {
      let _list = type == "cam" ? this.showData2 : this.showDataqae;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        //  最后一条的 +1 时会为undefind 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); //获取相同的 下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              //其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          //  当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          //当与下一条数据不同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      if (type == "cam") {
        this.showData2 = _list;
      } else if (type == "qae") {
        this.showDataqae = _list;
      }
    },
    rowSpanweek(key) {
      let _list = this.showDataweek;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        //  最后一条的 +1 时会为undefind 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); //获取相同的 下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              //其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          //  当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          //当与下一条数据不同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.showDataweek = _list;
    },
    rowSpanmonth(key) {
      let _list = this.showDatamonth;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        //  最后一条的 +1 时会为undefind 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); //获取相同的 下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              //其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          //  当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          //当与下一条数据不同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.showDatamonth = _list;
    },
    // 导出当月外接CAM分析数据
    down2() {
      camAnalysis(this.Sdata1 || moment().startOf("month").format("YYYY-MM-DD"), this.enddate1 || moment().endOf("month").format("YYYY-MM-DD")).then(
        res => {
          if (res.code == 1 && res.data.length > 0) {
            this.data1Source = res.data;
            this.exportExcelFile1(this.data1Source, "当月外接CAM分析", ".xlsx");
          } else {
            this.$message.error("暂无数据");
          }
        }
      );
    },
    // 导出当月人工成本分析数据
    down3() {
      //moment().subtract(1, 'days').format("YYYY-MM-DD"), moment().subtract(1, 'days').format("YYYY-MM-DD")
      //moment().startOf('month').format("YYYY-MM-DD"),  moment().endOf('month').format('YYYY-MM-DD')
      laborCostAnalysis(
        this.Sdata || moment().startOf("month").format("YYYY-MM-DD"),
        this.enddate || moment().endOf("month").format("YYYY-MM-DD"),
        this.sacteam
      ).then(res => {
        if (res.code == 1 && res.data.length > 0) {
          // this.data2Source = res.data
          let data2Source = res.data;
          let arr_ = [];
          data2Source.forEach(item => {
            item.labors.forEach((ite, index) => {
              let obj_ = ite;
              obj_["team"] = item.team;
              arr_.push(obj_);
            });
          });
          // this.data2Source = arr_
          // this.rowSpan("team");
          this.exportExcelFile(arr_, "当月人工成本分析", ".xlsx");
        } else {
          this.$message.error("暂无数据");
        }
      });
    },
    exportExcelFile1(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      jsonWorkSheet.A1.v = "工厂ID";
      jsonWorkSheet.B1.v = "顾客名称";
      jsonWorkSheet.C1.v = "单面板单量";
      jsonWorkSheet.D1.v = "单面板收入（元）";
      jsonWorkSheet.E1.v = "双面板单量";
      jsonWorkSheet.F1.v = "双面板收入（元）";
      jsonWorkSheet.G1.v = "多层板制单量";
      jsonWorkSheet.H1.v = "多层板收入（元）";
      jsonWorkSheet.I1.v = "总制单量";
      jsonWorkSheet.J1.v = "总收入";
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      return XLSX.writeFile(workBook, sheetName + fileName);
    },
    exportExcelFile(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      jsonWorkSheet.A1.v = "CAM";
      jsonWorkSheet.B1.v = "前端/后端";
      jsonWorkSheet.C1.v = "完成系数";
      jsonWorkSheet.D1.v = "目标系数";
      jsonWorkSheet.E1.v = "制单目标（个/天）";
      jsonWorkSheet.F1.v = "实际制单总数量(个)";
      jsonWorkSheet.G1.v = "单面板个数";
      jsonWorkSheet.H1.v = "单面板收入";
      jsonWorkSheet.I1.v = "双面板个数";
      jsonWorkSheet.J1.v = "双面板收入（元）";
      jsonWorkSheet.K1.v = "多层板个数";
      jsonWorkSheet.L1.v = "多层收入";
      jsonWorkSheet.M1.v = "报废量";
      jsonWorkSheet.N1.v = "报废扣款";
      jsonWorkSheet.O1.v = "客诉个数";
      jsonWorkSheet.P1.v = "客诉扣款";
      jsonWorkSheet.Q1.v = "质量奖励";
      jsonWorkSheet.R1.v = "组别";
      let hash = {};
      let data_ = array.reduce((preVal, curVal) => {
        hash[curVal.team] ? "" : (hash[curVal.team] = true && preVal.push(curVal));
        return preVal;
      }, []);
      let mergeArr_ = [];
      data_.forEach(item => {
        let start = array.map(o => o.team).indexOf(item.team);
        let end = array.map(o => o.team).lastIndexOf(item.team);
        mergeArr_.push({ s: { r: start + 1, c: 0 }, e: { r: end + 1, c: 0 } });
      });
      jsonWorkSheet["!merges"] = mergeArr_;
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      return XLSX.writeFile(workBook, sheetName + fileName);
    },
    exportExcelFile2(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      jsonWorkSheet.A1.v = "工厂";
      jsonWorkSheet.B1.v = "人员";
      jsonWorkSheet.C1.v = "指标";
      jsonWorkSheet.D1.v = "总数";
      jsonWorkSheet.E1.v = "平均";
      jsonWorkSheet.F1 ? (jsonWorkSheet.F1.v = "1") : "";
      jsonWorkSheet.G1 ? (jsonWorkSheet.G1.v = "2") : "";
      jsonWorkSheet.H1 ? (jsonWorkSheet.H1.v = "3") : "";
      jsonWorkSheet.I1 ? (jsonWorkSheet.I1.v = "4") : "";
      jsonWorkSheet.J1 ? (jsonWorkSheet.J1.v = "5") : "";
      jsonWorkSheet.K1 ? (jsonWorkSheet.K1.v = "6") : "";
      jsonWorkSheet.L1 ? (jsonWorkSheet.L1.v = "7") : "";
      jsonWorkSheet.M1 ? (jsonWorkSheet.M1.v = "8") : "";
      jsonWorkSheet.N1 ? (jsonWorkSheet.N1.v = "9") : "";
      jsonWorkSheet.O1 ? (jsonWorkSheet.O1.v = "10") : "";
      jsonWorkSheet.P1 ? (jsonWorkSheet.P1.v = "11") : "";
      jsonWorkSheet.Q1 ? (jsonWorkSheet.Q1.v = "12") : "";
      jsonWorkSheet.R1 ? (jsonWorkSheet.R1.v = "13") : "";
      jsonWorkSheet.S1 ? (jsonWorkSheet.S1.v = "14") : "";
      jsonWorkSheet.T1 ? (jsonWorkSheet.T1.v = "15") : "";
      jsonWorkSheet.U1 ? (jsonWorkSheet.U1.v = "16") : "";
      jsonWorkSheet.V1 ? (jsonWorkSheet.V1.v = "17") : "";
      jsonWorkSheet.W1 ? (jsonWorkSheet.W1.v = "18") : "";
      jsonWorkSheet.X1 ? (jsonWorkSheet.X1.v = "19") : "";
      jsonWorkSheet.Y1 ? (jsonWorkSheet.Y1.v = "20") : "";
      jsonWorkSheet.Z1 ? (jsonWorkSheet.Z1.v = "21") : "";
      jsonWorkSheet.AA1 ? (jsonWorkSheet.AA1.v = "22") : "";
      jsonWorkSheet.AB1 ? (jsonWorkSheet.AB1.v = "23") : "";
      jsonWorkSheet.AC1 ? (jsonWorkSheet.AC1.v = "24") : "";
      jsonWorkSheet.AD1 ? (jsonWorkSheet.AD1.v = "25") : "";
      jsonWorkSheet.AE1 ? (jsonWorkSheet.AE1.v = "26") : "";
      jsonWorkSheet.AF1 ? (jsonWorkSheet.AF1.v = "27") : "";
      jsonWorkSheet.AG1 ? (jsonWorkSheet.AG1.v = "28") : "";
      jsonWorkSheet.AH1 ? (jsonWorkSheet.AH1.v = "29") : "";
      jsonWorkSheet.AI1 ? (jsonWorkSheet.AI1.v = "30") : "";
      jsonWorkSheet.AJ1 ? (jsonWorkSheet.AJ1.v = "31") : "";
      let hash = {};
      let data_ = array.reduce((preVal, curVal) => {
        hash[curVal.factroyName] ? "" : (hash[curVal.factroyName] = true && preVal.push(curVal));
        hash[curVal.layers] ? "" : (hash[curVal.layers] = true && preVal.push(curVal));
        return preVal;
      }, []);
      let mergeArr_ = [];
      data_.forEach(item => {
        let start1 = array.map(o => o.factroyName).indexOf(item.factroyName);
        let end1 = array.map(o => o.factroyName).lastIndexOf(item.factroyName);
        let start2 = array.map(o => o.layers).indexOf(item.layers);
        let end2 = array.map(o => o.layers).lastIndexOf(item.layers);
        mergeArr_.push({ s: { r: start1 + 1, c: 0 }, e: { r: end1 + 1, c: 0 } }, { s: { r: start2 + 1, c: 1 }, e: { r: end2 + 1, c: 1 } });
      });
      jsonWorkSheet["!merges"] = mergeArr_;
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      return XLSX.writeFile(workBook, sheetName + fileName);
    },
    exportExcelFile3(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      jsonWorkSheet["!cols"] = [{ wch: 18 }];
      jsonWorkSheet.A1.v = "说明";
      jsonWorkSheet.B1.v = "汇总";
      jsonWorkSheet.C1 ? (jsonWorkSheet.C1.v = "1") : "";
      jsonWorkSheet.D1 ? (jsonWorkSheet.D1.v = "2") : "";
      jsonWorkSheet.E1 ? (jsonWorkSheet.E1.v = "3") : "";
      jsonWorkSheet.F1 ? (jsonWorkSheet.F1.v = "4") : "";
      jsonWorkSheet.G1 ? (jsonWorkSheet.G1.v = "5") : "";
      jsonWorkSheet.H1 ? (jsonWorkSheet.H1.v = "6") : "";
      jsonWorkSheet.I1 ? (jsonWorkSheet.I1.v = "7") : "";
      jsonWorkSheet.J1 ? (jsonWorkSheet.J1.v = "8") : "";
      jsonWorkSheet.K1 ? (jsonWorkSheet.K1.v = "9") : "";
      jsonWorkSheet.L1 ? (jsonWorkSheet.L1.v = "10") : "";
      jsonWorkSheet.M1 ? (jsonWorkSheet.M1.v = "11") : "";
      jsonWorkSheet.N1 ? (jsonWorkSheet.N1.v = "12") : "";
      jsonWorkSheet.O1 ? (jsonWorkSheet.O1.v = "13") : "";
      jsonWorkSheet.P1 ? (jsonWorkSheet.P1.v = "14") : "";
      jsonWorkSheet.Q1 ? (jsonWorkSheet.Q1.v = "15") : "";
      jsonWorkSheet.R1 ? (jsonWorkSheet.R1.v = "16") : "";
      jsonWorkSheet.S1 ? (jsonWorkSheet.S1.v = "17") : "";
      jsonWorkSheet.T1 ? (jsonWorkSheet.T1.v = "18") : "";
      jsonWorkSheet.U1 ? (jsonWorkSheet.U1.v = "19") : "";
      jsonWorkSheet.V1 ? (jsonWorkSheet.V1.v = "20") : "";
      jsonWorkSheet.W1 ? (jsonWorkSheet.W1.v = "21") : "";
      jsonWorkSheet.X1 ? (jsonWorkSheet.X1.v = "22") : "";
      jsonWorkSheet.Y1 ? (jsonWorkSheet.Y1.v = "23") : "";
      jsonWorkSheet.Z1 ? (jsonWorkSheet.Z1.v = "24") : "";
      jsonWorkSheet.AA1 ? (jsonWorkSheet.AA1.v = "25") : "";
      jsonWorkSheet.AB1 ? (jsonWorkSheet.AB1.v = "26") : "";
      jsonWorkSheet.AC1 ? (jsonWorkSheet.AC1.v = "27") : "";
      jsonWorkSheet.AD1 ? (jsonWorkSheet.AD1.v = "28") : "";
      jsonWorkSheet.AE1 ? (jsonWorkSheet.AE1.v = "29") : "";
      jsonWorkSheet.AF1 ? (jsonWorkSheet.AF1.v = "30") : "";
      jsonWorkSheet.AG1 ? (jsonWorkSheet.AG1.v = "31") : "";
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      return XLSX.writeFile(workBook, sheetName + fileName);
    },
    down6() {
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.statisticsdate) {
        params.date = this.statisticsdate;
      }
      if (this.FactoryId) {
        params.FactoryId = this.FactoryId;
      }
      departordertotalanalys(params)
        .then(res => {
          if (res.code) {
            let showData2 = res.data;
            let arr = [];
            showData2.forEach(item => {
              arr.push({
                realname: item["realname"],
                sum: item["sum"],
                nums: item["nums"],
              });
            });
            arr.forEach(item => {
              if (item["nums"].length) {
                item["nums"].forEach(ite => {
                  item["z" + ite.date] = ite.value;
                });
              } else {
                for (var a = 1; a <= 31; a++) {
                  item["z" + a] = "";
                }
              }
            });
            arr.forEach(item => {
              delete item.nums;
            });
            this.exportExcelFile3(arr, "部门统计", ".xlsx");
          } else {
            this.$message.error("暂无数据");
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    // 导出个人功效
    down5() {
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.date4) {
        params.date = this.date4;
      }
      if (this.FactoryId) {
        params.FactoryId = this.FactoryId;
      }
      externalCamAnalysis2(params)
        .then(res => {
          if (res.code) {
            let showData2 = res.data.items;
            let arr = [];
            showData2.forEach(item => {
              item["name"].forEach(subItem => {
                arr.push({
                  factroyName: item["factroyName"],
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                });
              });
            });
            arr.forEach(item => {
              let obj = {};
              if (item["nums"].length) {
                item["nums"].forEach(ite => {
                  item["z" + ite.date] = ite.value;
                });
              } else {
                for (var a = 1; a <= 31; a++) {
                  item["z" + a] = "";
                }
              }
            });
            arr.forEach(item => {
              delete item.nums;
            });
            this.exportExcelFile2(arr, "当月个人工效CAM分析", ".xlsx");
          } else {
            this.$message.error("暂无数据");
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    downqae() {
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.date8) {
        params.date = this.date8;
      }
      if (this.FactoryIdqae) {
        params.FactoryId = this.FactoryIdqae;
      }
      externalQaeAnalysis2(params).then(res => {
        if (res.code) {
          let showDataqae = res.data.items;
          let arr = [];
          showDataqae.forEach(item => {
            item["name"].forEach(subItem => {
              arr.push({
                factroyName: item["factroyName"],
                layers: item["layers"],
                realname: subItem["realname"],
                sum: subItem["sum"],
                nums: subItem["nums"],
                average: subItem["average"],
              });
            });
          });
          arr.forEach(item => {
            let obj = {};
            if (item["nums"].length) {
              item["nums"].forEach(ite => {
                item["z" + ite.date] = ite.value;
              });
            } else {
              for (var a = 1; a <= 31; a++) {
                item["z" + a] = "";
              }
            }
          });
          arr.forEach(item => {
            delete item.nums;
          });
          this.exportExcelFile2(arr, "当月个人工效QAE分析", ".xlsx");
        } else {
          this.$message.error("暂无数据");
        }
      });
    },
    moment,
    change(data, dateString) {
      this.selectDate = dateString || moment().startOf("month").format("YYYY/MM");
      this.getCostAnalysis(dateString);
    },
    change1(data, dateString) {
      this.Sdata1 = dateString[0];
      this.enddate1 = dateString[1];
      this.getCamAnalysis(dateString[0], dateString[1]);
    },
    change2(data, dateString) {
      this.Sdata = dateString[0];
      this.enddate = dateString[1];
      this.getLaborCostAnalysis(dateString[0], dateString[1], this.sacteam);
    },
    change3(data, dateString) {
      this.getLaborCostAnalysis1(dateString);
    },
    change4(data, dateString) {
      this.date4 = dateString || moment().startOf("month").format("YYYY/MM");
      this.getLaborCostAnalysis2(dateString);
    },
    change8(data, dateString) {
      this.date8 = dateString || moment().startOf("month").format("YYYY/MM");
      this.getqaedata();
    },
    statisticschange(data, dateString) {
      this.statisticsdate = dateString || moment().startOf("month").format("YYYY/MM");
      this.getLaborCostAnalysis3(dateString);
    },
    change5(data, dateString) {
      this.getLaborCostAnalysisweek(dateString);
    },
    CustomerRulesClick(record) {
      this.getExternalCamAnalysis(record.factoryId, this.time, record.factoryName);
      this.showMode = true;
    },
    getExternalCamAnalysis(a, b, c) {
      this.spinning = true;
      externalCamAnalysis(a, b)
        .then(res => {
          if (res.code) {
            this.showData = res.data;
            this.showData.factoryName = c;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    onChange(pagination, filters, sorter) {
      this.sacteam = filters.makegroup.toString();
      this.getLaborCostAnalysis(this.Sdata, this.enddate, this.sacteam);
      // if(filters) {
      //   this.data2Source = this.data2SourceCopy.filter(item => {return filters.makegroup.includes(item.makegroup) })
      //   // this.data2Source = filters
      //    this.rowSpan("team");
      // }
    },

    openChange(val) {
      if (val) {
        this.open = true;
      } else {
        this.open = false;
      }
    },
    panelChange(val) {
      this.pickerDefault6 = moment(val).format("YYYY");
      this.open = false;
      this.getLaborCostAnalysismonth(this.pickerDefault6);
      //调取接口获取数据
    },
  },
  mounted() {
    this.getCostAnalysis();
    if (this.activeKey == 5) {
      this.pickerDefault = null;
      this.pickerDefault1 = null;
      this.pickerDefault2 = null;
      this.pickerDefault3 = moment(new Date(), "YYYY/MM");
      this.pickerDefault4 = null;
      this.pickerDefault5 = null;
      this.pickerDefault6 = null;
      this.pickerDefault8 = null;
      this.getLaborCostAnalysis2();
    }
    factroyList().then(res => {
      if (res.code) {
        this.factroyList = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
  },
};
</script>

<style scoped lang="less">
.table-striped {
  background-color: #dfdcdc;
}
.tdred {
  background-color: #ffdbdb;
  color: #000000;
  font-weight: bold;
  height: 36px;
  line-height: 36px;
}
/deep/ .ant-table {
  .rowBackgroundColor {
    background: #dcdcdc !important;
  }
}
.bot {
  height: 300px;
  width: 98.9%;
  margin-left: 8px;
}
.mintable1 {
  /deep/.ant-table-body {
    min-height: 720px;
  }
}
.mintable2 {
  /deep/.ant-table-body {
    min-height: 180px;
  }
}
/deep/.ant-select-selection__placeholder,
.ant-select-search__field__placeholder {
  line-height: 15px !important;
}
/deep/.ant-calendar-picker-input {
  height: 27px;
}
/deep/.ant-select-selection--single {
  position: relative;
  height: 27px;
  cursor: pointer;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 20px;
}
/deep/.ant-select-selection-selected-value {
  line-height: 26px !important;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  padding: 0.5%;
}

/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
/deep/.ant-tabs {
  color: #000000;
}
/deep/.ant-tabs-content-no-animated {
  min-height: 758px;
}
/deep/.ant-calendar-range-picker-input {
  font-weight: 500;
}
/deep/.ant-calendar-picker-input {
  font-weight: 500;
}
/deep/.min-table1 {
  .ant-table-body {
    min-height: 728px;
  }
}
.minClass1 {
  /deep/ .ant-table-placeholder {
    min-height: 725px;
  }
}
/deep/.min-table {
  .ant-table-body {
    min-height: 698px;
  }
}
/deep/.contentInfo tr th {
  font-size: 14px;
  font-weight: 500;
}
.minClass {
  /deep/ .ant-table-placeholder {
    min-height: 695px;
  }
}
.dataPage {
  min-width: 1670px;
  background: #ffffff;
  padding: 6px 20px;
  /deep/ .ant-table {
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    margin: 0;
    min-width: 80px;
    text-align: center;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    background: #fff9e6;
  }

  /deep/.ant-table-thead > tr > th {
    padding: 6px 3px !important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 0 !important;
  }

  #tab {
    tr {
      th {
        height: 36px;
        padding: 0;
      }

      td {
        padding: 0;
      }
    }
  }
}

.ant-calendar-picker-container {
  right: 0;

  .ant-calendar-range {
    width: 400px;
  }
}

/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.gray-row td {
  background: #f8f8f8;
}
/deep/.white-row td {
  background: white;
}
.geren {
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    color: #ff9900;
  }
}
</style>
