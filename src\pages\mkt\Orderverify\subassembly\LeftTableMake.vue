<!-- 市场管理 - 订单预审 列表 -->
<template>
  <div ref="tableWrapper">
    <a-table
      :columns="columns"
      :scroll="{ y: 737, x: 1000 }"
      :dataSource="dataSource"
      :customRow="onClickRow"
      :pagination="pagination"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      @change="handleTableChange"
      :rowClassName="isRedRow"
    >
      <template slot="labelUrl" slot-scope="record">
        <span @click.stop="OperationLog(record)" style="color: #428bca; cursor: pointer">日志</span>
      </template>
      <template slot="isCustRule" slot-scope="text, record">
        <a-tooltip title="客户规则" v-if="record.isCustRule == 1">
          <a-icon type="file-text" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="CustomerRulesClick(record)" />
        </a-tooltip>
      </template>
      <template slot="analysisSpan" slot-scope="text, record" v-if="record.analysisSpan != null">
        {{ (record.analysisSpan / 60.0).toFixed(1) }}
      </template>
      <template slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </template>
      <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
        <a v-if="record.status != '待预审'" style="color: #428bca" :title="record.orderNo" @click.stop="goDetail(record)">{{ record.orderNo }}</a>
        <a v-else :title="record.orderNo" style="color: black"> {{ record.orderNo }}</a
        >&nbsp;
        <span class="tagNum" style="display: inline-block; height: 19px">
          <span v-if="record.isJiaji" style="font-size: 14px; color: #ff9900; margin: 0px 0px 0px -10px; display: inline-block; user-select: none">
            &nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
          </span>
          <a-tooltip title="精细预审" v-if="record.finePre">
            <a-tag class="noCopy TAG"> 精 </a-tag>
          </a-tooltip>
          <a-tooltip v-if="record.answerMailId" title="邮件信息查看">
            <a-tag @click="$emit('getmail', record)" class="TAG"> 邮 </a-tag>
          </a-tooltip>
          <a-tooltip v-if="record.onLineEcnState > 0" :title="record.onLineOrRecordEcn == 2 ? '更改存档升级' : 'ECN在线改版'">
            <a-tag class="TAG"> 升 </a-tag>
          </a-tooltip>
          <a-tag v-if="record.ka" class="TAG"> KA </a-tag>
          <a-tooltip :title="record.fileUploadedTime ? '重传文件时间:' + record.fileUploadedTime : '重传文件'" v-if="record.fileUploadedCount > 0">
            <span>
              <a-tag
                color="#2D221D"
                style="
                  font-size: 12px;
                  background: red;
                  color: white;
                  padding: 0 2px;
                  margin-left: 3px;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid red;
                "
              >
                重
              </a-tag>
              <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -2px">{{ record.fileUploadedCount }}</span>
            </span>
          </a-tooltip>
          <a-tag v-if="record.isOrderModify && record.orderModify == 1" @click.stop="xiudisplay(record)" class="TAG"> 修 </a-tag>
          <a-tag
            v-if="record.isOrderModify && record.orderModify == 2"
            @click.stop="xiudisplay(record)"
            style="
              font-size: 12px;
              background: #ff9900;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #ff9900;
            "
          >
            修
          </a-tag>
          <a-tooltip title="风险警告" v-if="record.riskWarning">
            <a-icon type="warning" style="color: #ff9900" class="noCopy" />
          </a-tooltip>
          <a-tag v-if="record.isOrderModify && record.orderModify == 3" @click.stop="xiudisplay(record)" class="TAG"> 退 </a-tag>
        </span>
      </div>
    </a-table>
    <right-copy ref="RightCopy" />
    <a-modal
      :title="'【' + custNo + '】-- 特殊要求'"
      :width="1000"
      :visible="visibleCustNo"
      :confirmLoading="confirmLoading"
      :mask="false"
      :maskClosable="false"
      @cancel="handleCancel"
    >
      <standard-table
        rowKey="id"
        :columns="factoryColumns"
        :dataSource="factoryData"
        :pagination="false"
        :class="factoryData.length ? 'min-table' : ''"
        :loading="lodaing2"
      >
        <span slot="companyArea_" slot-scope="{ text }">
          <template>
            {{ text }}
          </template>
        </span>
        <span slot="image_" slot-scope="{ record }">
          <template>
            <span v-if="record.image_" v-viewer>
              <img v-for="(tmp, index) in record.image_.split(',')" :key="index" :src="tmp" style="width: 26px; height: 26px; margin-right: 5px" />
            </span>
          </template>
        </span>
      </standard-table>
      <template slot="footer">
        <a-button @click="handleCancel">取消</a-button>
      </template>
    </a-modal>
    <a-modal title="查看图片" :width="640" :visible="visible" @ok="handleOkImg" @cancel="handleCancel" :mask="false" :maskClosable="false">
      <a-spin :spinning="confirmLoading">
        <a-row>
          <a-col :span="8" v-for="(tmp, index) in imgList" :key="index">
            <a :href="tmp" target="_blank">
              <img :src="tmp" alt="图片" class="imgStyle" />
            </a>
            <!-- <div v-viewer>
                          <img :src="tmp" alt="图片" class='imgStyle'>
                      </div> -->
          </a-col>
        </a-row>
      </a-spin>
    </a-modal>
    <a-modal title="修改内容" :width="1300" :visible="xiuvisible" destroyOnClose centered :mask="false" :maskClosable="false" @cancel="handleCancel">
      <div>
        <a-table
          class="xiu"
          :columns="columns4"
          :dataSource="dataSource4"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 400 }"
          :class="dataSource4.length ? 'min-table' : ''"
          :pagination="false"
        >
          <template slot="isPrintContract" slot-scope="record">
            <a-checkbox :checked="record.isPrintContract"></a-checkbox>
          </template>
          <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
            <template v-for="photo in record.filePath.split(',')">
              <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer :key="index + '-' + photo" />
            </template>
          </div>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="handleCancel">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
import { checkPermission } from "@/utils/abp";
import StandardTable from "@/components/table/StandardTable";
import { CustRequire, ordermodifylist } from "@/services/mkt/CustInfoNew";
const factoryColumns = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "6%",
    align: "width",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "要求类别",
    dataIndex: "category_",
    width: "10%",
    scopedSlots: { customRender: "category_" },
  },
  {
    title: "要求简述",
    dataIndex: "askToTell_",
    width: "20%",
    scopedSlots: { customRender: "askToTell_" },
  },
  {
    title: "要求详情",
    dataIndex: "askDetails_",
    scopedSlots: { customRender: "askDetails_" },
  },
  {
    title: "图片",
    dataIndex: "image_",
    width: "15%",
    className: "lastTd",
    scopedSlots: { customRender: "image_" },
  },
  // {
  //     title: '操作',
  //     key: 'action',
  //     width:'10%',
  //     scopedSlots: { customRender: 'action' },
  // },
];
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "30%",
    align: "left",
  },
  {
    title: "图片",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    // dataIndex: "isPrintContract",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            current: 1,
            pageSize: 25,
            showTotal: total => `总计 ${total} 条`,
            total: 0,
          },
          selectedRows: {},
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
    pageStat: {
      type: Boolean,
    },
    params1: {
      type: Object,
    },
  },
  name: "LeftTableMake",
  components: { StandardTable, RightCopy },
  data() {
    return {
      columns4,
      dataSource4: [],
      selectedRowKeysArray: [],
      menuData: {},
      xiuvisible: false,
      selectedRowsData: [],
      activeClass: "smallActive",
      proOrderId: "",
      custNo: "",
      visibleCustNo: false,
      confirmLoading: false,
      factoryColumns,
      factoryData: [],
      imgList: [],
      visible: false,
      lodaing2: false,
    };
  },
  watch: {
    pagination: {
      handler(val) {
        console.log(val);
      },
    },
    dataSource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < val.length; i++) {
              let obj2 = val[i].proOrderNo;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (longestChars.length > 12 && result == 0) {
              this.columns[7].width = 130 + (longestChars.length - 12) * 5 + "px";
              this.columns[6].width = 400 - (longestChars.length - 12) * 5 + "px";
            }
            if (longestChars.length > 12 && result >= 1) {
              this.columns[1].width = 130 + result * 30 + "px";
              this.columns[7].width = 130 + (longestChars.length - 12) * 5 + "px";
              this.columns[6].width = 400 - result * 20 - (longestChars.length - 12) * 5 + "px";
            }
            if (result == 0 && longestChars.length <= 12) {
              this.columns[1].width = "130px";
              this.columns[6].width = "400px";
              this.columns[7].width = "130px";
            }
            if (result >= 1 && longestChars.length <= 12) {
              this.columns[1].width = 130 + result * 30 + "px";
              this.columns[6].width = 400 - result * 30 + "px";
              this.columns[7].width = "130px";
            }
          });
        }
      },
    },
  },
  created() {
    // console.log('dataSource',this.dataSource)
  },
  methods: {
    checkPermission,
    xiudisplay(record) {
      ordermodifylist(record.id).then(res => {
        if (res.code) {
          this.dataSource4 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
      this.xiuvisible = true;
    },
    OperationLog(record) {
      this.$emit("OperationLog", record);
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      console.log(selectedRowKeys, selectedRows);
      this.selectedRowKeysArray = selectedRowKeys;
      this.selectedRowsData = selectedRows[0];
    },
    isRedRow(record) {
      if (record.isReOrder == 1 || record.status == 24) {
        return "fontRed";
      }
      let strGroup = [];
      if (record.id && record.id == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.$emit("getOrderDetail", record);
            this.$emit("getJobInfo", record.id);
            this.proOrderId = record.id;
            this.$nextTick(function () {
              localStorage.setItem("pageCurrent3", this.pagination.current);
              localStorage.setItem("pageSize3", this.pagination.pageSize);
              localStorage.setItem("id3", record.id);
              localStorage.setItem("record3", JSON.stringify(record));
              localStorage.setItem("stat3", true);
              localStorage.setItem("ysqueryInfo", JSON.stringify(this.params1));
            });
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    handleTableChange(pagination) {
      this.pageStat = false;
      this.$emit("tableChange", pagination);
    },
    previewClick(record) {
      this.$emit("previewClick", record);
    },
    // // 查看日志
    // viewLogClick(record){
    //   this.$emit('viewLogClick', record)
    // },
    // 修改信息
    // modifyInfoClick(record){
    //   this.$emit('modifyInfoClick', record)
    // },
    // 编辑参数
    EditParametersClick(record) {
      this.$emit("EditParametersClick", record);
    },
    goDetail(record) {
      this.$nextTick(function () {
        localStorage.setItem("pageCurrent3", this.pagination.current);
        localStorage.setItem("pageSize3", this.pagination.pageSize);
        localStorage.setItem("id3", record.id);
        localStorage.setItem("record3", JSON.stringify(record));
        localStorage.setItem("stat3", true);
        localStorage.setItem("ysqueryInfo", JSON.stringify(this.params1));
        this.$router.push({
          path: "orderDetail",
          query: {
            id: record.id,
            orderNo: record.orderNo,
            factory: record.joinFactoryId,
            boid: record.jobId,
            custNo: record.custNo,
            ttype: "2",
            reOrder: record.reOrder,
            isCustRule: record.isCustRule,
            fileUploadedCount: record.fileUploadedCount,
            orderModify: record.orderModify,
            tradeType: record.tradeType,
          },
        });
      });
    },
    // 客户规则
    CustomerRulesClick(record) {
      // this.proOrderId = record.proOrderId
      this.$emit("CustomerRulesClick", record);
    },
    goCustNo(record) {
      this.custNo = record.custNo;
      this.visibleCustNo = true;
      this.getAskList(record.id);
    },
    handleCancel() {
      this.visibleCustNo = false;
      this.xiuvisible = false;
      this.visible = false;
      this.factoryData = [];
    },
    getAskList(id) {
      this.lodaing2 = true;
      CustRequire(id)
        .then(res => {
          if (res.success) {
            this.factoryData = res.data;
          } else {
            this.$message.info(res.message);
          }
        })
        .finally(() => {
          this.lodaing2 = false;
        });
    },
    imgCheck(record) {
      this.imgList = record.image_.split(",");
      this.visible = true;
    },
    handleOkImg() {
      this.visible = false;
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.TAG {
  font-size: 10px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
  cursor: pointer;
}
.xiu {
  /deep/.ant-table-row-cell-ellipsis,
  .ant-table-row-cell-ellipsis .ant-table-column-title {
    overflow: overlay;
    white-space: normal;
    text-overflow: inherit;
  }
}
/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-content {
  border: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-row-cell-break-word {
  font-weight: 500;
}
/deep/.ant-table-header-column {
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }
  .rowBackgroundColor {
    background: #dcdcdc;
  }
  .displayFlag {
    display: none;
  }
}
/deep/.ant-modal-body {
  padding: 0 4px;
}
/deep/.ant-modal-body {
  padding: 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table {
  .ant-table-tbody > tr {
    .lastTd {
      padding: 0 4px !important;
    }
  }
}
</style>
