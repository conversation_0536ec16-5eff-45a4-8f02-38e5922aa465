import { request, METHOD } from "@/utils/request";
// 生产工具列表
export function protoollist(params) {
  return request(`/api/app/pro-tool/pro-tool-list`, METHOD.GET, params);
}
// 生产工具人员清单
export function protooluserlist() {
  return request(`/api/app/pro-tool/pro-tool-user-list`, METHOD.GET);
}
// 钻咀列表
export function protooldrilltoollist(orderno, type, factory) {
  return request(`/api/app/pro-tool/pro-tool-drill-tool-list?orderno=${orderno}&type=${type}&factory=${factory}`, METHOD.GET);
}
//钻咀下载文件
export function downloaddrillfile(orderno, factory) {
  return request(`/api/app/pro-tool/down-load-drill-file?orderno=${orderno}&factory=${factory}`, METHOD.POST);
}
// 人员对应的订单
export function userorderlist(erpid) {
  return request(`/api/app/pro-tool/pro-tool-user-order-list?erpid=${erpid}`, METHOD.GET);
}
//生产反馈模式选择
export function protoolfeedbackmode() {
  return request(`/api/app/pro-tool/feedback-mode`, METHOD.GET);
}
// 生产反馈数据获取
export function protoolpnl(OrderNo) {
  return request(`api/app/pro-tool/pro-tool-pnl?OrderNo=${OrderNo}`, METHOD.GET);
}
//生产反馈数据
export function feedbackpar2Protool(OrderNo, barCode) {
  return request(`api/app/pro-tool/feed-back-par2Protool?OrderNo=${OrderNo}&barCode=${barCode}`, METHOD.POST);
}
//输入文件下载
export function stretchdatapath(Id) {
  return request(`/api/app/pro-tool/stretch-data-path/${Id}`, METHOD.GET);
}
//取单设置
export function protoolorder() {
  return request(`/api/app/pro-tool/pro-tool-order`, METHOD.GET);
}
//分派回退
export function backprotoolsend(Id) {
  return request(`/api/app/pro-tool/back-pro-tool-send/${Id}`, METHOD.POST);
}
//分派订单
export function protoolsendorder(params) {
  return request(`/api/app/pro-tool/pro-tool-send-order`, METHOD.POST, params);
}
//订单开始
export function protoolorderstart(id) {
  return request(`/api/app/pro-tool/${id}/pro-tool-order-start`, METHOD.POST);
}
//订单完成
export function protoolorderfinished(id) {
  return request(`/api/app/pro-tool/${id}/pro-tool-order-finished`, METHOD.POST);
}
export function protoolorderfinishv3(params) {
  return request(`/api/app/pro-tool/pro-tool-order-finish-v3`, METHOD.POST, params);
}
export function finishedduoxuan(id, type) {
  return request(`/api/app/pro-tool/pro-tool-order-finished-duoxuan?type=${type}`, METHOD.POST, id);
}
//删除订单
export function protooldelorder(Id) {
  return request(`/api/app/pro-tool/del-order/${Id}`, METHOD.POST);
}
//发送钢网邮件
export function protoolsendemail(Id, params) {
  return request(`/api/app/pro-tool/${Id}/send-email`, METHOD.POST, params);
}
//生产反馈
export function protoolfeedback(params) {
  return request(`/api/app/pro-tool/pro-tool-feedback`, METHOD.POST, params);
}
//订单统计
export function protooltotalorders() {
  return request(`/api/app/pro-tool/pro-tool-total-orders`, METHOD.GET);
}
//文件上传
export function uploadfile(id, params) {
  return request(`/api/app/pro-tool/${id}/up-load-file`, METHOD.POST, params);
}
//1225新文件上传
export function uploadfilenew(params) {
  return request(`/api/app/pro-tool/up-load-file-new`, METHOD.POST, params);
}

export function uploadfilev2(params) {
  return request(`/api/app/file-manage/up-load-file-v2`, METHOD.POST, params);
}
