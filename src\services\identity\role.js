import {request, METHOD} from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
export async function getList(params) {
    // return request("/api/identity/roles", METHOD.GET, transformAbpListQuery(params)) 2023/3/1 修改
    return request("/api/app/synchronize-user/roles-list", METHOD.GET, transformAbpListQuery(params))
}
export async function getAll(params) {
    return request("/api/identity/roles/all", METHOD.GET, params)
}
export async function createUpdate(params) {
    if (params.id) {
        // return request(`/api/identity/roles/${params.id}`, METHOD.PUT, params)
        return request(`/api/app/synchronize-user/${params.id}/update-roles`, METHOD.POST, params)
    }
    return request("/api/identity/roles", METHOD.POST, params)
}
export async function del(id) {
    // return request(`/api/identity/roles/${id}`, METHOD.DELETE) 
    return request(`/api/app/synchronize-user/${id}/delete-roles`, METHOD.POST)
}
export async function get(id) {
    return request(`/api/identity/roles/${id}`, METHOD.GET)
}
export default {
    getList,
    getAll,
    createUpdate,
    del,
    get
  }
  