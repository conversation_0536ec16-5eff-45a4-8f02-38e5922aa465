<!-- 市场管理 - 业务跟单- 按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box">
      <a-button type="primary" @click="$emit('queryClick')"> 查询(F) </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="$emit('followup')"> 跟进回复 </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="$emit('endclick')"> 完结 </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="$emit('quotationcontract')"> 销售合同 </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="$emit('quotationClick')"> 报价表单 </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="$emit('quotationinformation')"> 报价信息 </a-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "MakeAction",
  props: [],
  data() {
    return {};
  },
};
</script>

<style scoped lang="less">
/deep/.ant-btn {
  padding: 0 10px !important;
}
.ppading {
  /deep/.ant-btn {
    padding: 0 0px !important;
  }
}

.active {
  height: 50px;
  line-height: 40px;
  float: right;
  // width: 1680px;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;

  .box {
    width: 90px;
    margin-top: 6px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }

    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }

        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }

        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }

        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;

        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }

        .ant-select-arrow {
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
