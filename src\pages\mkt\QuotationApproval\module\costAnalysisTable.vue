<template>
  <div style="height: 740px; overflow: auto">
    <table border="1">
      <tr>
        <td colspan="4">成本分析表</td>
      </tr>
      <tr>
        <td>客户代码</td>
        <td>{{ results.value_1 }}</td>
        <td>客户型号</td>
        <td>{{ results.value_2 }}</td>
      </tr>
      <tr>
        <td>订单类型</td>
        <td>{{ results.value_3 }}</td>
        <td>产品用途</td>
        <td>{{ results.value_4 }}</td>
      </tr>
      <tr>
        <td>层数</td>
        <td>{{ results.value_5 }}</td>
        <td>板厚(mm)</td>
        <td>{{ results.value_6 }}</td>
      </tr>
      <tr>
        <td>表面处理/厚度</td>
        <td>{{ results.value_7 }}</td>
        <td>受金面积</td>
        <td>{{ results.value_8 }}</td>
      </tr>
      <tr>
        <td>成品尺寸(mm)</td>
        <td>{{ results.value_9 }}</td>
        <td>拼板数(PCS/1SET)</td>
        <td>{{ results.value_10 }}</td>
      </tr>
      <tr>
        <td>PCS尺寸(mm)</td>
        <td>{{ results.value_11 }}</td>
        <td>最小孔</td>
        <td>{{ results.value_12 }}</td>
      </tr>
      <tr>
        <td>阻焊颜色</td>
        <td>{{ results.value_13 }}</td>
        <td>字符颜色(PCS/ISET)</td>
        <td>{{ results.value_14 }}</td>
      </tr>
      <tr>
        <td>完成铜厚</td>
        <td>{{ results.value_15 }}</td>
        <td>测试点数(W/㎡)</td>
        <td>{{ results.value_16 }}</td>
      </tr>
      <tr>
        <td>最小孔铜um</td>
        <td>{{ results.value_17 }}</td>
        <td>板厚孔径比</td>
        <td>{{ results.value_18 }}</td>
      </tr>
      <tr>
        <td>线宽/线距</td>
        <td>{{ results.value_19 }}</td>
        <td>平米孔(W/㎡)</td>
        <td>{{ results.value_20 }}</td>
      </tr>
      <tr>
        <td>材料尺寸(inch)</td>
        <td>{{ results.value_21 }}</td>
        <td>利用率</td>
        <td>{{ results.value_22 }}</td>
      </tr>
      <tr>
        <td>板材参数</td>
        <td colspan="3">{{ results.value_23 }}</td>
      </tr>
      <tr>
        <td>特殊工艺备注</td>
        <td colspan="3">{{ results.value_24 }}</td>
      </tr>
      <tr>
        <td>订单备注</td>
        <td colspan="3">{{ results.value_25 }}</td>
      </tr>
      <tr>
        <td colspan="2">
          <img :src="results.value_26" style="width: 450px" />
        </td>
        <td colspan="2"><img :src="results.value_27" style="width: 450px" /></td>
      </tr>
      <tr>
        <td colspan="4" style="text-align: center">成本明细</td>
      </tr>
      <tr>
        <td>成本清单</td>
        <td>价格</td>
        <td colspan="2">加价参数</td>
      </tr>
      <tr v-for="(item, index) in price" :key="index">
        <td>{{ item.captions2_ }}</td>
        <td>{{ item.actualPrice_ }}</td>
        <td colspan="2">{{ item.condition_ }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
export default {
  name: "CostAnalysisTable",
  props: ["costData"],
  data() {
    return {
      results: {},
      price: [],
    };
  },
  created() {
    this.price = this.costData.price;
    this.results = this.costData.results;
  },
};
</script>

<style scoped>
td {
  padding: 6px;
  text-align: center;
}
</style>
