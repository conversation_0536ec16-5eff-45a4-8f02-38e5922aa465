import { request, METHOD } from "@/utils/request";
import { transformAbpListQuery } from "@/utils/abp";

//获取详情参数列表
export async function getProject(Id) {
  return request(`/api/app/e-mSMake-module-no/m-iMake-info-par/${Id}`, METHOD.GET);
}
//修改详情
export async function amandProject(parmas) {
  return request(`/api/app/e-mSMake-module-no/update-m-iMake-info-par`, METHOD.POST, parmas);
}
//列表
export async function projectList(parmas) {
  return request(`/api/app/e-mSMake-module-no/make-info-list`, METHOD.GET, parmas);
}
//获取审核列表
export async function checkList(parmas) {
  return request(`/api/app/e-mSMake-module-no/x-tMIVerify`, METHOD.GET, parmas);
}
//打印PDF
export async function getflowmI(factoryId, mainOrderNo) {
  return request(`api/app/e-mSMake-module-no/get-flow-mI/${factoryId}?mainOrderNo=${mainOrderNo}`, METHOD.POST);
}
export async function checkBill(parmas) {
  return request(`/api/app/e-mSMake-module-no/auto-get-verify-order`, METHOD.POST, parmas);
}
//回退审核
export async function backCheck(Id) {
  return request(`/api/app/e-mSMake-module-no/update-xTBack-verify/${Id}`, METHOD.PUT);
}
//开始审核
export async function statCheck(Id) {
  return request(`/api/app/e-mSMake-module-no/update-xTVerify-start/${Id}`, METHOD.PUT);
}
//开始审核
export async function finishCheck(Id) {
  return request(`/api/app/e-mSMake-module-no/update-xTVerify-finish/${Id}`, METHOD.PUT);
}
//工艺流程 2023/3/13 更改
// export async function flowList(parmas) {
//     return request(`/api/app/e-mSMake-module-no/tech-flow`, METHOD.GET,parmas)
// }
export async function techFlowV2(parmas) {
  return request(`/api/app/tech-flow-app-sevice/tech-flow-v2`, METHOD.GET, parmas);
}
//流程详情
// export async function flowQ(Id) {
//     return request(`/api/app/e-mSMake-module-no/tech-flow-list/${Id}`, METHOD.GET)
// }
export async function flowQ(orderNo, mainOrderNo, factoryId) {
  return request(`api/app/tech-flow-app-sevice/tech-flow-list-v2/${factoryId}?orderNo=${orderNo}&mainOrderNo=${mainOrderNo}`, METHOD.GET);
}
//生产单结构
export async function flowTreeS(Id) {
  return request(`/api/app/e-mSMake-module-no/make-module-no4Ch-kBList/${Id}`, METHOD.GET);
}
export async function getTab(parmas) {
  return request(`/api/app/e-mSMake-module-no/drill-tool`, METHOD.GET, parmas);
}
//拖拽数据
export async function getDrag(OrderNo) {
  return request(`/api/app/tech-flow-app-sevice/tech-flow-auto-list?OrderNo=${OrderNo}`, METHOD.GET);
}
export async function techflowautolist(OrderNo, techNo) {
  return request(`/api/app/tech-flow-app-sevice/tech-flow-auto-list?OrderNo=${OrderNo}&TechNo=${techNo}`, METHOD.GET);
}
//修改主流程
// export async function reviseProcess(parmas) {
//     return request(`/api/app/e-mSMake-module-no/update-tech-flow`, METHOD.POST,parmas)
// }
export async function reviseProcess(parmas) {
  return request(`/api/app/tech-flow-app-sevice/update-tech-flow-v2`, METHOD.POST, parmas);
}
export async function updatetechflowchild(parmas, TechNo) {
  return request(`/api/app/tech-flow-app-sevice/update-tech-flow-child?TechNo=${TechNo}`, METHOD.POST, parmas);
}
//修改子项参数
// export async function reviseParameter(parmas) {
//     return request(`/api/app/e-mSMake-module-no/update-tech-flow-paramer`, METHOD.POST,parmas)
// }
export async function reviseParameter(parmas) {
  return request(`/api/app/tech-flow-app-sevice/update-tech-flow-paramer-v2`, METHOD.POST, parmas);
}
//上传ERP
export async function setmiflowinfostoerp(mainOrderNo, factory) {
  return request(`/api/app/tech-flow-to-erp-app-sevice/set-mi-flow-infos-to-erp/${factory}?orderNo=${mainOrderNo}`, METHOD.POST);
}
export async function setceshimiflowinfostoerp(mainOrderNo, factory) {
  return request(`/api/app/tech-flow-to-erp-app-sevice/set-ceshi-mi-flow-infos-to-erp/${factory}?orderNo=${mainOrderNo}  `, METHOD.POST);
}
export async function sethPMiflowinfostoerp(factoryId, orderNo) {
  return request(`/api/app/tech-flow-to-erp-app-sevice/set-hPMi-flow-infos-to-erp/${factoryId}?orderNo=${orderNo}`, METHOD.POST);
}
//删除工艺流程检查
export async function miflowcheck(factoryId, orderNo, TechName) {
  return request(`/api/app/tech-flow-app-sevice/mi-flow-check/${factoryId}?orderNo=${orderNo}&TechName=${TechName}`, METHOD.GTE);
}
//指示检查
export async function finalchecklist(factoryId, orderNo) {
  return request(`api/app/engineering-production/final-check-list/${factoryId}?mainOrderNo=${orderNo}`, METHOD.GTE);
}
//修改申请
export async function techflowmodifyrecord(factoryId, params) {
  return request(`api/app/tech-flow-app-sevice/tech-flow-modify-record/${factoryId}?${params}`, METHOD.POST);
}
//发送指示 上网 下网 指示审核 指示回退
export async function setstatetoerp(params) {
  return request(`/api/app/tech-flow-to-erp-app-sevice/set-state-to-erp`, METHOD.POST, params);
}
// 开料
export async function klInfo(orderNo, mainOrderNo, factoryId) {
  return request(`/api/app/e-mSMake-module-no/kl-info/${factoryId}?orderNo=${orderNo}&mainOrderNo=${mainOrderNo}`, METHOD.GET);
}
//返单导入
export async function nopecallback(scbh) {
  return request(`/api/app/tech-flow-call-back-app-sevice/nope-call-back?mainOrderNo=${scbh}`, METHOD.POST);
}
//数据导入
export async function importdata(fac, orderNo, mainOrderNo, techno) {
  return request(
    `/api/app/tech-flow-call-back-app-sevice/import-data/${fac}?orderNo=${orderNo}&mainOrderNo=${mainOrderNo}&techno=${techno}`,
    METHOD.POST
  );
}
//导入叠层信息
export async function setstackupitemv21(orderNo, factoryId) {
  return request(`/api/app/tech-flow-call-back-app-sevice/set-stack-up-item-v21/${factoryId}?orderNo=${orderNo}`, METHOD.POST);
}
// 层压
export async function stackInfo(orderNo, mainOrderNo, factoryId) {
  return request(`/api/app/e-mSMake-module-no/stack-info/${factoryId}?orderNo=${orderNo}&mainOrderNo=${mainOrderNo}`, METHOD.GET);
}
// 信息表
export async function techflowcYPMInfo(orderNo, mainOrderNo, factoryId) {
  return request(`/api/app/e-mSMake-module-no/tech-flow-cYPMInfo/${factoryId}?orderNo=${orderNo}&mainOrderNo=${mainOrderNo}`, METHOD.GET);
}
// 叠层
export async function stackimpInfo(orderNo, mainOrderNo, factoryId) {
  return request(`/api/app/e-mSMake-module-no/stack-imp/${factoryId}?orderNo=${orderNo}&mainOrderNo=${mainOrderNo}`, METHOD.GET);
}
// 钻孔
export async function drillToolV2(orderNo, mainOrderNo, factoryId, TechNo) {
  return request(`/api/app/e-mSMake-module-no/drill-tool-v2/${factoryId}?orderNo=${orderNo}&mainOrderNo=${mainOrderNo}&TechNo=${TechNo}`, METHOD.GET);
}
export async function getfrontflowmI(factoryId, mainOrderNo) {
  return request(`/api/app/e-mSMake-module-no/front-flow-mI/${factoryId}?mainOrderNo=${mainOrderNo}`, METHOD.GET);
}
export default {
  getProject,
  amandProject,
  projectList,
  checkList,
  getflowmI,
  checkBill,
  backCheck,
  statCheck,
  setstatetoerp,
  finishCheck,
  // flowList,
  // flowQ,
  flowTreeS,
  getTab,
  getDrag,
  techflowautolist,
  reviseProcess,
  updatetechflowchild,
  reviseParameter,
  setmiflowinfostoerp,
  getfrontflowmI,
};
