<!-- 物料管理-物料录入-厂商建档 -->
<template>
  <div ref="SelectBox">
    <a-form-model
        :modal="form"
    >
      <a-form-model-item label="厂商名称：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}" class="required">
        <a-input allowClear v-model="form.verdorName_ "  :autoFocus='autoFocus' v-focus-next-on-enter="'input2'" ref="input1"/>
      </a-form-model-item>
       <a-form-model-item label="厂商缩写：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.ab_"  v-focus-next-on-enter="'input9'" ref="input8" />
      </a-form-model-item>
      <a-form-model-item label="厂商地址：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.address_"  v-focus-next-on-enter="'input3'" ref="input2" />
      </a-form-model-item>
      <a-form-model-item label="厂商电话：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.phone_"  v-focus-next-on-enter="'input4'" ref="input3" />
      </a-form-model-item>
      <a-form-model-item label="厂商手机：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.mobile_" v-focus-next-on-enter="'input5'" ref="input4" />
      </a-form-model-item>
      <a-form-model-item label="厂商传真：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.fax_"  v-focus-next-on-enter="'input6'" ref="input5" />
      </a-form-model-item>
      <a-form-model-item label="电子邮箱：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.email_"  v-focus-next-on-enter="'input7'" ref="input6" />
      </a-form-model-item>
      <a-form-model-item label="客户网址：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.website_"  v-focus-next-on-enter="'input8'" ref="input7" />
      </a-form-model-item>     
      <a-form-model-item label="联系电话：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.linkman_"  v-focus-next-on-enter="'input10'" ref="input9"/>
      </a-form-model-item>
      <a-form-model-item label="发票地址：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input allowClear v-model="form.invoaddr_"  v-focus-next-on-enter="'input11'" ref="input10"/>
      </a-form-model-item>
      <a-form-model-item label="厂商分类：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-select allowClear v-model="form.kind_"  v-focus-next-on-enter="'input12'" ref="input11"  :getPopupContainer="()=>this.$refs.SelectBox">
        <a-select-option v-for="(item,index) in proOptions" :key="index" :value="item.id">{{item.caption_}}</a-select-option>
        <!-- <span>Selected: {{ selected }}</span> -->
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="厂商级别：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-select allowClear  v-model="form.class_"  v-focus-next-on-enter="'input13'" ref="input12"  :getPopupContainer="()=>this.$refs.SelectBox">
          <a-select-option v-for="(item,index) in proOptions1" :key="index" :value="item.id">{{item.caption_}}</a-select-option>
        </a-select>
      </a-form-model-item>  
      <a-form-model-item label="工厂：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-select v-model='form.JoinFactoryId' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear>
          <a-select-option  v-for="(item,index) in mapKey(factory)" :key="index" :value="item.value" :lable="item.lable" :title="item.lable">
              {{ item.lable }}
          </a-select-option>
          </a-select>
      </a-form-model-item>       
  </a-form-model>
  </div>
  
</template>

<script>
export default {
    name:'PcbSetfile',
    props:['proOptions','proOptions1','factory'],
  data() {
    return {      
      autoFocus:true,
      form:{
        verdorName_:'',  // 厂商名称
        address_: '中国',  // 厂商地址
        phone_: '10000',    // 厂商电话
        mobile_: '10000',     // 厂商手机
        fax_: '10000',     // 厂商传真
        email_:'<EMAIL>',//电子邮箱
        website_:'www.XXX.com',//客户网址
        ab_:'',//厂商缩写
        linkman_:'10000',//联系人
        invoaddr_:'中国',//发票地址
        kind_:924,//厂商分类
        class_:916,//厂商级别        
      },
      //proOptions:[],
      
    };
  },   
  methods: { 
    mapKey(data){
      if (!data) {
        return []
      } else {
        return Object.keys(data).map(item => {
          return {'value':item, 'lable': data[item]}
        })
      }
    }, 
  },
  directives: {
    'focusNextOnEnter':{
      bind: function(el, {
        value
      }, vnode) {
        el.addEventListener('keyup', (ev) => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value]
            if (nextInput && typeof nextInput.focus === 'function') {
              nextInput.focus()
              nextInput.select()
            }
          }
        })
      }
    }
  },
};
</script>
<style scoped lang="less">
.required{
  /deep/.ant-form-item-label > label{
    color: red;
  }
}
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
 color:#000000;
}
.ant-modal-body{
    .ant-form-item {
  margin-bottom: 8px;
}
}
/deep/.ant-input{
  font-weight: 500;
}
</style>