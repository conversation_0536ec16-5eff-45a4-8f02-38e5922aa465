<!-- 供应链 -协同工厂 -->
<template>
  <div class="grade">
    <a-card :loading="loading">
      <div class="firft">
        <table border="1">
          <thead>
          <tr>
            <td>协同工厂总数量</td>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td style="font-weight: 500;">{{ this.count1 }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </a-card>

    <div class="second">
      <a-card :loading="loading" title="协同品类分布">
      <div class="sc_left">
          <table border="1">
            <thead>
              <tr>
                <td>品类</td>
                <td>工厂数量</td>
              </tr>
            </thead>
            <tbody>
            <tr v-for="(item,index) in data.categoryDistributionDtos || []" :key="index">
              <td><a @click="details(item.category)">{{ item.category || '' }}</a></td>
              <td>{{ item.factoryNum || 0 }}</td>
            </tr>
            </tbody>
          </table>
      </div>
      <div class="sc_right">
          <pie-chart :el="'sc_echart'" :echartdata="data | areaData"/>
      </div>
      </a-card>
    </div>

    <div class="third">
      <a-card :loading="loading" title="各品类产能负荷">
      <div class="th_left">
        <table border="1">
          <thead>
          <tr>
            <td>品类</td>
            <td>协议产能（㎡/月）</td>
            <td>实际产出（㎡/月）</td>
            <td>产能负荷率（%)</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in data.categoryDistributionDtos || []" :key="index">
            <td>{{ item.category || 0 }}</td>
            <td>{{ item.agreedCapacity || 0}}</td>
            <td>{{ item.actualOutput || 0}}</td>
            <td>{{ (item.capacityLoadRate*100).toFixed() + '%' || '0%'}}</td>
          </tr>
          </tbody>
        </table>

      </div>
      <div class="th_right">
          <bar-chart :barData="data | th_barData" el="'th_echart'"/>
      </div>
      </a-card>
    </div>

    <div class="fourth">
      <a-card :loading="loading" title="协同工厂品级分布">
      <div class="ft_left">
        <table border="1">
          <thead>
          <tr>
            <td>品级</td>
            <td>厂家数量</td>
            <td>占比（%)</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in data.gradeDistributionDtos || []" :key="index">
            <td>{{ item.grade || '' }}</td>
            <td>{{ item.factoryNum || 0}}</td>
            <td>{{ item.proportion.toFixed() + '%' || '0%'}}</td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="ft_right">
        <bar-chart :barData="data | fr_barData" el="ft_echart"/>
      </div>
      </a-card>
    </div>
    <div class="fifth">
      <a-card :loading="loading" title="工厂评级">
        <div class="th_left">
          <!--        <a-table :columns="columns3" :data-source="data.factoryRating || []" :pagination="false" rowKey="id"   :scroll="{ y: 300 }" bordered>-->
          <!--          <template slot="factoryCode_" slot-scope="text,record"><a @click = "details(record.id)">{{text}}</a></template>-->
          <!--          <template slot="action" slot-scope="text,record"><a @click = "details(record.id)">点击前往</a></template>-->
          <!--        </a-table>-->
          <a-table
              :columns="columns3"
              :data-source="data.factoryRating || []"
              :pagination="false"
              rowKey="id"
              :scroll="{ y: 264 }"
              bordered
          >
            <template slot="factoryCode_" slot-scope="text,record"><a @click = "details1(record.id)">{{text}}</a></template>-->
          </a-table>
        </div>
      </a-card>
    </div>

    <div class="map">
      <a-card :loading="loading" title="协同工厂地区分布">
        <div class="chartMap">
          <map-chart :el="'single_panel1'" :chartData="mapData.filter(item => {return item.singlePanel})" :title="'单面板'" :flag="flag"/>
          <map-chart :el="'single_panel4'" :chartData="mapData.filter(item => {return item.aluminumPanel})" :title="'铝基板'" :flag="flag"/>
          <map-chart :el="'single_panel2'" :chartData="mapData.filter(item => {return item.doublePanel})" :title="'双面板'" :flag="flag"/>
          <map-chart :el="'single_panel3'" :chartData="mapData.filter(item => {return item.multiPanel})" :title="'多层板'" :flag="flag"/>
          <map-chart :el="'single_panel5'" :chartData="mapData.filter(item => {return item.fpcPanel})" :title="'FPC'" :flag="flag"/>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script>
import PieChart from "@/pages/collaborative/grade/component/PieChart";
import BarChart from "@/pages/collaborative/grade/component/BarChart";
import {getAllData} from "@/services/grade";
import MapChart from "@/pages/collaborative/grade/component/MapChart";
import 'whatwg-fetch'
const columns3 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 50,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,

  },
  {
    title: '品类',
    dataIndex: 'category',
    key: '',
    align:'center',
    width:50
  },
  {
    title: '名称',
    dataIndex: 'name_',
    key: 'name_',
    align:"left",
    ellipsis: true,
    width:200
  },
  {
    title: '工厂',
    dataIndex: 'factoryCode_',
    key: 'factoryCode_',
    scopedSlots: { customRender: 'factoryCode_' },
    align:'center',
    ellipsis: true,
    width:70
  },
  {
    title: '状态',
    dataIndex: 'orderStatus',
    key: 'orderStatus',
    align:'left',
    ellipsis: true,
    width:70
  },
  {
    title: '品级',
    dataIndex: 'grade',
    key: 'grade',
    align:'left',
    ellipsis: true,
    width:70
  },
  {
    title: '1月',
    dataIndex: 'm1',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '2月',
    dataIndex: 'm2',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '3月',
    dataIndex: 'm3',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '4月',
    dataIndex: 'm4',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '5月',
    dataIndex: 'm5',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '6月',
    dataIndex: 'm6',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '7月',
    dataIndex: 'm7',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '8月',
    dataIndex: 'm8',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '9月',
    dataIndex: 'm9',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '10月',
    dataIndex: 'm10',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '11月',
    dataIndex: 'm11',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '12月',
    dataIndex: 'm12',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },

]
export default {
  name: "grade",
  components: {MapChart, BarChart, PieChart},
  data(){
    return {
      data:'',
      count1: 0,
      count2:0,
      loading:false,
      mapData:[],
      flag: false,
      columns3,
      factoryRating:[

      ],   // 工厂评级
    }
  },
  filters:{
    areaData: function (data){
      let arr = []
      if (data){
        data?.categoryDistributionDtos.forEach(item => {
          let obj = {}
          obj['name'] = item.category
          obj['value'] = item.factoryNum
          arr.push(obj)
        })
      }
      return arr

    },
    th_barData: function (data) {
      // console.log('data',data)
      let name = [], value=[];
     if (data) {
       name = data?.categoryDistributionDtos.map(item => {
         return item.category
       })
       value = data?.categoryDistributionDtos.map(ite => {
         return ite.capacityLoadRate
       })
     }
      return {'name':name, 'value':[{'type': 'bar', 'data' : value,itemStyle: {
            normal: {
              label: {
                show: true,//是否显示
                position: 'top',//显示位置
                textStyle: { //文字样式
                  color: '#333',
                  fontWeight: 500,
                },
                formatter: function (params) {//核心部分 formatter 可以为字符串也可以是回调
                  if (params.value) {//如果当前值存在则拼接
                    return params.value + '㎡'
                  } else {//否则返回个空
                    return '';
                  }
                }
              }
            },
          }}]}
    },
    fr_barData: function (data) {
      let name = [], value1=[],value2=[];
      if (data) {
        name = data?.gradeDistributionDtos.map(item => {
          return item.grade
        })
        value1 = data?.gradeDistributionDtos.map(ite => {
          return ite.factoryNum
        })
        value2 = data?.gradeDistributionDtos.map(ite => {
          return ite.proportion
        })
      }

      return {'name':name, 'value':[{'type': 'bar', 'data' : value1,itemStyle: {
            normal: {
              label: {
                show: true,//是否显示
                position: 'top',//显示位置
                textStyle: { //文字样式
                  color: '#333',
                  fontSize: '16px',
                  fontWeight: 500,
                },
                formatter: function (params) {//核心部分 formatter 可以为字符串也可以是回调
                  if (params.value) {//如果当前值存在则拼接
                    return params.value
                  } else {//否则返回个空
                    return '';
                  }
                }
              }
            },
          }}, {'type': 'bar', 'data' : value2,itemStyle: {
            normal: {
              label: {
                show: true,//是否显示
                position: 'top',//显示位置
                textStyle: { //文字样式
                  color: '#333',
                  fontWeight: 500,
                },
                formatter: function (params) {//核心部分 formatter 可以为字符串也可以是回调
                  if (params.value) {//如果当前值存在则拼接
                    return params.value + '%'
                  } else {//否则返回个空
                    return '';
                  }
                }
              }
            },
          }}]}
    }
  },
  created() {
    this.getData()
  },
  methods:{
     async getData (){
       this.flag = false
       this.loading = true
       let res = await getAllData('协同')
      //  筛选休眠数据
      //  let filterData={}
      //  for (const key in res.data) {
      //   filterData[key]=res.data[key].filter(function(item) {
      //     return  item.orderStatus!="休眠"
      //   })
      // }
      // res.data=filterData
       this.count1 =  res.data.supplierOutputs.length
       this.data = res.data;
      //  console.log('this.data',this.data)
       this.mapData = this.data.supplierOutputs
       if(res){
         let data_ = res.data.supplierOutputs
         this.factoryRatingDataFilter(data_)
       }
       this.mapData.map( async (item,index) => {
         let resData = await this.fetchAPI(item)
         if (resData.status == 1) {
           item.value = resData.geocodes[0].location.split(',')
         }
         if (index == res.data.supplierOutputs.length-1) {
           this.flag = true;
         }
       })

       this.loading = false
    },
    // objectKeys(data,name){
    //   let obj_ = {'singlePanel':"单面", 'doublePanel':'双面', 'multiPanel':'多层','aluminumPanel':'铝基', 'fpcPanel':'FPC'}
    //   // for(var i in obj_){
    //   //   if(obj_[i] == name) {
    //   //     return i
    //   //   }
    //   // }
    // },
    factoryRatingDataFilter(data){
      //  console.log('11',data)
      let _arr = [];

      // let Grade ={'boutique' :'精品', 'superiorProduct':'优品', 'standardProduct' :'标品'}
      // let category={'proofing': '打样','smallbatch':'中小批量','massProduction':'大批量'}
      let nameKey = {'singlePanel':"单面", 'doublePanel':'双面', 'multiPanel':'多层','aluminumPanel':'铝基', 'fpcPanel':'FPC'}
      for(var i=0;i<data.length;i++){
          _arr.push(data[i])
        // console.log('22',_arr)
      }
      for(var j=0;j<data.length;j++){
        let category = ''
        if(_arr[j].singlePanel){
          category = "单面"
        }else if(_arr[j].doublePanel){
          category = "双面"
        }else if(_arr[j].multiPanel){
          category = "多层"
        }else if(_arr[j].aluminumPanel){
          category = "铝基"
        }else if(_arr[j].fpcPanel){
          category = "FPC"
        }
        _arr[j]['category'] = category
      }
      for (var a=0;a<_arr.length;a++){
        let Grade = [];
        if(_arr[a].boutique){
          Grade.push('精品')
        }
        if(_arr[a].superiorProduct){
          Grade.push('优品')
        }
        if(_arr[a].standardProduct){
          Grade.push('标品')
        }
        _arr[a]['grade'] = Grade.join(',')
      }
      for ( var b=0;b<_arr.length;b++){
        let orderType_ = [];
        if(_arr[b].proofing){
          orderType_ .push('打样')
        }
        if(_arr[b].smallbatch){
          orderType_ .push('中小批量')
        }
        if(_arr[b].massProduction){
          orderType_ .push('大批量')
        }
        _arr[b]['orderType_'] = orderType_.join(',')
      }
      _arr.sort((a, b) =>{return b.cooperationMode.localeCompare(a.cooperationMode, 'zh')})
      this.data.factoryRating = _arr
    },
    async fetchAPI (item) {
      const response = await fetch(`https://restapi.amap.com/v3/geocode/geo?key=597329c1be7ba7f571c3bc21f591fd6c&address=${item.address_}&city=${item.city_}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
      })
      return response.json()
    },
    details(name){
       this.$router.push({
         path: '/collaborative/gradeDetails1',
         query: {
           'name': name,
           'type':'协同',
         }
       })
    },
    details1(name){
      this.$router.push({ path:'/collaborative/detail', query:{ id:name,type:'1',factory:'协同' } })
      // this.$router.push({
      //   path: `/collaborative/detail?id=${name}&type=1`,
      // })
    }
  }
}
</script>

<style scoped lang="less">
/deep/.ant-table-thead > tr > th, .ant-table-tbody > tr > td {
    padding: 7.9px;
    overflow-wrap: break-word;
}
/deep/ .ant-table-bordered.ant-table-fixed-header .ant-table-scroll .ant-table-header.ant-table-hide-scrollbar .ant-table-thead > tr:only-child > th:last-child {
    border-right-color: #f0f0f0;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background:#F8F8F8;
}
 /deep/.ant-table-tbody > tr > td{
  padding: 7.9px;
 }
.th_right,.ft_right{
  position: relative;
  top: -50px;
}
/deep/.ant-card-head-title{
  font-weight: 500;
}
  .grade {
    background: #FFFFFF;
    min-width:1670px;
    /deep/ .ant-card-body {
      .firft {
        td {
          text-align: center;
          height: 35px;          
          a {
            text-decoration:underline!important;
          }
        }
        tr td:first-child {
          font-weight: 600;
          width: 120px;
        }
        tr td:nth-child(2) {
          width: 130px;
        }
      }

    }


    .second {
      /deep/ .ant-card-body {
        display: flex;
        .ant-card-loading-content {
          flex: 1;
        }
        .sc_left {
          height: 200px;
          flex: 1;
          width:50%;
          td {
            text-align: center;
            height: 35px;
            a {
              text-decoration:underline!important;
            }
          }
          tr td:first-child {
            font-weight: 600;
            width: 120px;
          }
          tr td:nth-child(2) {
            width: 130px;
          }
          tr td:nth-child(3) {
            width: 130px;
            font-weight: 500;
          }
        }
        .sc_right {
          width:50%;
          height: 200px;
          flex: 1;
        }
      }

    }
    .third {
      /deep/ .ant-card-body {
        display: flex;
        .ant-card-loading-content {
          flex: 1;
        }
        .th_left {
          width:50%;
          height: 200px;
          flex: 1;
        }

        .th_right {
          width:50%;
          height: 200px;
          flex: 1;
        }

        td {
          text-align: center;
          height: 35px;
        }
        tr td:first-child {
          font-weight: 600;
          width: 120px;
        }
        tr td:nth-child(2) {
          width: 130px;
        }
        tr td:nth-child(3) {
          width: 130px;
        }
      }
    }
    .fourth {
      /deep/ .ant-card-body {
        display: flex;
        .ant-card-loading-content {
          flex: 1;
        }
        .ft_left {
          height: 200px;
          flex: 1;
          width:50%;
        }
        .ft_right {
          height: 200px;
          flex: 1;
          width:50%;
        }
        td {
          text-align: center;
          height: 35px;
        }
        tr td:first-child {
          font-weight: 600;
          width: 120px;
        }
        tr td:nth-child(2) {
          width: 130px;
        }
        tr td:nth-child(3) {
          width: 130px;
        }
      }
    }
    .fifth {
      height: 400px;
      /deep/ .ant-card-body {
        .th_left {
          width: 100%;
          overflow: auto;
        }
      }
    }
    .map {
      /deep/ .ant-card-body{
        .chartMap {
          display: flex;
        }
      }

    }
  }
</style>
