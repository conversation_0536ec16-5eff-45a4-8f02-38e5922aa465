<template>
  <a-card>
      <a-alert message="信息提醒反馈" type="warning" class='alert' show-icon/>
      <p class='marginRight'></p>
      <a-button type="primary" @click="info">
        Display normal message
      </a-button>
      <br/>
      <p class='marginRight'></p>
      <a-button @click="success">
        Success
      </a-button>
      <a-button @click="error">
        Error
      </a-button>
      <a-button @click="warning">
        Warning
      </a-button>
      <br/>
      <p class='marginRight'></p>
      <a-alert message="可以通过 then 接口在关闭后运行 callback" type="warning" class='alert' show-icon/>
      <p class='marginRight'></p>
      <a-button @click="successLoad">
        Display a sequence of message
      </a-button>
      <p class='marginRight'></p>
      <a-alert message="自定义时长 10s，默认时长为 3s" type="warning" class='alert' show-icon/>
      <p class='marginRight'></p>
      <a-button @click="successTip">
        Customized display duration
      </a-button>
  </a-card>
</template>
<script>
export default {
  methods: {
    info() {
      this.$message.info('This is a normal message');
    },
    success() {
      this.$message.success('This is a success message');
    },
    successTip() {
      this.$message.success(
        'This is a prompt message for success, and it will disappear in 10 seconds',
        10,
      );
    },
    successLoad() {
      this.$message
        .loading('Action in progress..', 2.5)
        .then(() => this.$message.success('Loading finished', 2.5))
        .then(() => this.$message.info('Loading finished is finished', 2.5));
    },
    error() {
      this.$message.error('This is an error message');
    },
    warning() {
      this.$message.warning('This is a warning message');
    },
  },
};
</script>
<style lang="less" scoped>
.marginRight {
    margin-bottom:20px
}
.ant-btn{
    margin-right:10px
}
</style>