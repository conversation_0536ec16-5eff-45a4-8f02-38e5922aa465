import { request, METHOD } from '@/utils/request'
// 待收货列表(orderno)
export async function getWaitOrderList(params) {
    return request("/api/app/cutting/receiving-good-list", METHOD.GET, params)
}
// 待开料列表(orderno)
export async function getDoingOrderList(params) {
    return request("/api/app/cutting/cutting-out-list", METHOD.GET, params)
}
// 当班统计
export async function getStatisticsList(params) {
    return request("/api/app/cutting/cutting-ondutystatistics", METHOD.GET, params)
}
// 钻刀列表(orderno)
export async function getDrillHoleList(params) {
    return request("/api/app/cutting/get-drill-t0output", METHOD.GET, params)
}
// 收货
export async function Receiving(params) {
    return request('/api/app/cutting/receive-goods', METHOD.POST, params)
}
// 开料开始
export async function materialCutting(params) {
    return request(`/api/app/cutting/cutting-start`, METHOD.POST,params)
}
//  获取用户工厂
export async function UserFactory() {
    return request(`/api/app/cutting/user-fac-id`, METHOD.POST,)
}
// 获取部门过序数量
export async function getpassStepNum(params) {
    return request(`/api/app/e-mSTProc-eTMake/drill-pass-step-num`, METHOD.GET,params)
}
// 部门过序
export async function cuttingSequence(params) {
    return request(`/api/app/cutting/k-lOver-count`, METHOD.POST,params)
}
// 获取工厂Id列表
export async function getFactoryList() {
    return request(`/api/app/e-mSTPub-factory-configure/factory-id-list`, METHOD.POST,)
}
// 上传订单
export async function UploadOrder(params) {
    return request(`/api/app/e-mSTProc-dRMake/out-drill-order-up-load`, METHOD.POST, params)
}
// 设置加急
export async function SetUpExpediting(Id) {
    return request(`/api/app/cutting/is-urgent`, METHOD.POST,Id)
}
// 钻孔过序
export async function DrillingSequence(params) {
    return request(`/api/app/cutting/drilling-sequence`, METHOD.POST,params)
}
// 删除订单
export async function DeleteOrder(Id) {
    return request(`/api/app/cutting/flying-probe-del-order/${Id}`, METHOD.POST)
}
// 备注
export async function ExceptionRemarks(Id, params) {
    return request(`/api/app/cutting/set-remarks/${Id}`, METHOD.GET,params)
}

// 呼叫小车
export async function CallTrolley(params) {
    return request(`/api/app/cutting/call-trolley`, METHOD.POST,params)
}
// 人员确认
export async function Confirm() {
    return request(`/api/app/cutting/confirm`, METHOD.POST)
}
// 取消小车
export async function AgvCancel() {
    return request(`/api/app/cutting/agv-cancel`, METHOD.POST)
}
// 流程卡
export async function ProcessCard(Id) {
    return request(`/api/app/cutting/print-card/${Id}`, METHOD.POST,)
}
// 按钮上传文件
export async function UploadFile(params) {
    return request(`/api/app/e-mSTProc-dRMake/up-load-drill-file`, METHOD.POST, params)
}
// 右键上传文件
export async function UploadFile1(params) {
    return request(`/api/app/e-mSTProc-dRMake/up-file`, METHOD.POST, params)
}
// 上传文件(guid)
export async function orderFileUpload(Id) {
    return request(`/api/app/e-mSTProc-dRMake/up-load-drill-file/${Id}`, METHOD.GET,)
}


export default {
    getWaitOrderList,
    getDoingOrderList,
    getStatisticsList,
    getDrillHoleList,
    Receiving,
    materialCutting,
    getpassStepNum,   
    SetUpExpediting, 
    DeleteOrder,
    ExceptionRemarks,
    DrillingSequence,
    cuttingSequence,
    getFactoryList,
    UploadFile,
    UploadFile1,
    UploadOrder,
    CallTrolley,
    Confirm,
    AgvCancel,
    orderFileUpload,
    ProcessCard,
    UserFactory,
}