import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
import {updataSite} from "./CustInfo";

//WIP查询分页接口
export async function wipordermanegepagelist(params) {
    return request("/api/app/pcb-order-list/wip-ordermanege-page-list", METHOD.GET,params)
}
//WIP模块下拉值
export async function wipselectlist(params) {
    return request("/api/app/pcb-order-list/wip-select-list", METHOD.GET,params)
}
//WIP导出xlsx
export async function pcborderlistexport(params) {
    return request("/api/app/pcb-order-list/export", METHOD.POST,params)
}
//用户WIP导出xlsx
export async function pcborderlistexportv2(params) {
    return request("/api/app/pcb-order-list/export-v2", METHOD.POST,params,{responseType:'blob'})
}