
<!-- 市场管理 - 邮件列表 -->
<template>
    <a-spin :spinning="spinning">
      <div class="projectBackend" ref="SelectBox">
        <div class="content">
          <a-select v-model="formData.joinFactoryId"  @keyup.enter.native="searchClick" showSearch allowClear optionFilterProp="lable" 
             placeholder="工厂" style="width:120px;margin-right:0.5%;"  >
              <a-select-option style="color:blue" v-for="(item,index) in mapKey(factroyList)" :key="index" :value="item.value" :lable="item.lable" :title="item.lable">
                {{ item.lable }}
              </a-select-option>
            </a-select>
            <a-input  placeholder="客户代码" v-model.trim="formData.custNo"  :title='formData.custNo' style="width:120px;margin-right:0.5%;margin-top: 7px"
           allowClear @keyup.enter.native="searchClick"></a-input>  
           <a-select placeholder="类型"   v-model.trim="formData.effective"  :title='formData.effective' @keyup.enter.native="searchClick" 
          style="width:80px;margin-right:0.5%;" allowClear :getPopupContainer="()=>this.$refs.SelectBox">
          <a-select-option value="全部" label="全部"> 全部 </a-select-option>
            <a-select-option value="有效" label="有效"> 有效 </a-select-option>
            <a-select-option value="无效" label="无效"> 无效 </a-select-option>
          </a-select>
          <a-select placeholder="是否处理"   v-model.trim="formData.consumed"  :title='formData.consumed' @keyup.enter.native="searchClick" 
          style="width:120px;margin-right:0.5%;" allowClear :getPopupContainer="()=>this.$refs.SelectBox">
          <a-select-option value="全部" label="全部"> 全部 </a-select-option>
            <a-select-option value="是" label="是"> 是 </a-select-option>
            <a-select-option value="否" label="否"> 否 </a-select-option>
          </a-select>
         
          <a-input  placeholder="主题" v-model.trim="formData.Subject" :title='formData.Subject' style="width:150px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input>
          <!-- <a-input  placeholder="发送邮箱" v-model.trim="formData.From" :title='formData.From' style="width:150px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input>
          <a-input  placeholder="抄送邮箱" v-model.trim="formData.Cc"  :title='formData.Cc' style="width:150px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input> -->
          <!-- <a-input  placeholder="内容" v-model.trim="formData.Body"  :title='formData.Body' style="width:120px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input> -->
          <!-- <a-input  placeholder="内容" v-model.trim="formData.Body"  :title='formData.Body' style="width:183px;margin-right:0.5%;"
           allowClear @keyup.enter.native="searchClick"></a-input>
           <a-input  placeholder="附件" v-model.trim="formData.Body"  :title='formData.Body' style="width:183px;margin-right:0.5%;"
           allowClear @keyup.enter.native="searchClick"></a-input>  -->
          <a-button type="primary" @click="searchClick" style="margin-right:0.5%;">搜索</a-button>  
          <!-- <a-button type="primary" @click="EmailReply" style="margin-right:0.5%;float: right;margin-top: 8px;">邮件回复</a-button> -->
          <a-button type="primary" style="margin-right:0.5%;float: right;margin-top: 8px;" @click="invalidemail" >标记完成</a-button> 
          <a-button type="primary" style="margin-right:0.5%;float: right;margin-top: 8px;" v-if="checkPermission('MES.MarketModule.MailList.CreateOrderByMail')" @click="CreateOrder" >创建订单</a-button>
          <a-button type="primary" @click="emailstart" style="margin-right:0.5%;float: right;margin-top: 8px;">开始(S)</a-button>    
          <a-button type="primary" style="margin-right:0.5%;float: right;margin-top: 8px;" v-if="checkPermission('MES.MarketModule.MailList.ReceiveEmail')" @click="receiveemail" >邮件接收</a-button> 
        </div>
        <div style="height: 100%; display: flex;">
          <!-- <div class="rightContent" style="width: 17%; ">
           <a-table
                :columns="columns1"
                :dataSource="Statslist"
                :pagination=false
                :rowKey="'index'" 
              >  
              </a-table>
          </div>   -->
          <div class="leftContent" style="width: 100%;display: flex;flex-wrap: wrap; position:relative" ref="tableWrapper">
              <a-table
                v-if="pageshow"
                :columns="columns" 
                :dataSource="orderListData" 
                :customRow="customRow1"
                :pagination="pagination" 
                :rowKey="'id'" 
                :scroll="{y:680}"
                :loading="orderListTableLoading"
                @change="handleTableChange"    
                :rowClassName="isRedRow"         
                :class="orderListData.length ? 'min-table':''"
              >
                <template slot="num" slot-scope="text, record, index">
                  {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
                </template>
                <template slot="body" slot-scope="text, record," >
                <span>{{ record.body }}</span> 
                </template>
                <template slot="subject" slot-scope="text, record,">
                  <span>{{ record.subject }}</span> 
                </template>
                <template slot="hasAttachments" slot-scope="text, record,">
                  <span v-if="record.hasAttachments" :class="record.attachmentNums>1 ? 'fontcolor':''">是
                    <span style="font-size:8px;color: red;position: relative;top: -10px; left: -4px;" v-if="record.attachmentNums">{{ record.attachmentNums }}</span></span> 
                </template>
              </a-table> 
              <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox" style="color:red">
          <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
        </a-menu> 
              <div class="bto"></div>         
          </div>
        </div>
       </div>   
       <a-drawer
        :title="showTitle"
        :visible="dataopen"
        @close="dataCancel"
        :maskClosable="true"
        :mask="false"
        placement="right"
        style="margin-top:174px;height: 73.7%;"
        :class="dataopen ? 'drawerclass':''"
      >
        <div class="drawre" v-html="messageList"></div>
      </a-drawer>    
       <a-modal
          title="确认弹窗"
          :visible="dataVisibleMode"
          @cancel="reportHandleCancel1"
          @ok="handleOkMode"
          ok-text="确定"
          cancel-text="取消(C)"
          destroyOnClose
          :maskClosable="false"
          :width="400"
          centered
      >
      <span style="font-size:16px;">{{ messageMode }}</span>   
      </a-modal>   
      <a-modal
          title="邮件回复"
          :visible="maildataVisible"
          @cancel="reportHandleCancel1"
          @ok="mailhandleOkMode"
          ok-text="确定"
          cancel-text="取消"
          destroyOnClose
          :maskClosable="false"
          :width="600"
          centered
      > 
      <a-form >  
        <a-row >
         <a-col :span='24' class="hstyle">
            <a-form-item label="收件人"  :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }" >
            <a-input  allowClear  autoFocus>         
            </a-input>
             </a-form-item>
          </a-col>
        </a-row>
        <a-row>
         <a-col :span='24' class="hstyle">
            <a-form-item label="抄送"  :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }" >
            <a-input  allowClear  autoFocus>         
            </a-input>
             </a-form-item>
          </a-col>
        </a-row>
        <a-row>
         <a-col :span='24' class="hstyle">
            <a-form-item label="主题"  :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
            <a-input  allowClear  autoFocus>         
            </a-input>
             </a-form-item>
          </a-col>
        </a-row>
        <a-row>
         <a-col :span='24'>
            <a-form-item label="正文"  :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
            <a-textarea  allowClear :auto-size="{ minRows:6, maxRows: 15 }"  autoFocus>         
            </a-textarea >
             </a-form-item>
          </a-col>
        </a-row>
        <a-row>
         <a-col :span='24'>
          <div style="height: 130px;width: 500px;">
            <div>
            <span style="margin:0 120px;" @click="clickUpload()" v-if="fileListdata.length<4"> <a-icon type="paper-clip"></a-icon> 添加附件</span>
            <a-upload 
            name="file"
            ref="fileRef"
            :customRequest="httpRequest"
            :before-upload="beforeUpload"
            :file-list="fileListdata"
            :maxCount="1"
            @change="handleChange"
          >
          </a-upload>
            </div>
           </div>
         </a-col>
        </a-row>
        </a-form> 
      </a-modal>     
    </a-spin>
</template>
<script>
import moment from "moment";
import {
  pageList,receiveEmail,eMimeOrder,consumeorder,mailStatslist,emimestart
} from "@/services/mkt/MailList";
import {checkPermission} from "@/utils/abp";
import {factroyList} from "@/services/dataPage";
import {modulenouploadfile} from "@/services/supplier/index";
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",    
    scopedSlots: { customRender: 'num' },
    width: 50,    
  },
  {
    title: "工厂",
    width: 80,
    align: "left",
    className:'userStyle',
    dataIndex:'joinFactoryName',
    ellipsis:true,
  },
  {
    title: "客户代码",
    width: 70,
    align: "left",
    className:'userStyle',
    dataIndex:'custNo',
    ellipsis:true,
  },
  {
    title: "处理人",
    width: 60,
    align: "left",
    className:'userStyle',
    dataIndex:'processedName',
    ellipsis:true,
  },
  // {
  //   title: "来源",
  //   width: 180,
  //   dataIndex: 'from',
  //   align: "left",
  //   className:'userStyle',
  //   ellipsis:true,
  // },
  // {
  //   title: "抄送",
  //   width: 250,
  //   dataIndex: 'cc',
  //   align: "left",
  //   ellipsis:true,
  //   className:'userStyle',
  // },
  {
    title: "主题",
    dataIndex:'subject',
    align: "left",
    width:800,
    className:'userStyle',
    scopedSlots: { customRender: 'subject' },
    ellipsis:true,    
    // fixed:'left',
  },
  // {
  //   title: "内容",
  //   // width: 200,
  //   dataIndex: 'body',
  //   align: "left",  
  //   className:'userStyle',
  //   scopedSlots: { customRender: 'body' },
  //   ellipsis:true,  
  // },
  {
    title: "附件",
    width: 40,
    dataIndex: 'hasAttachments',
    align: "center",
    ellipsis:true,
    scopedSlots: { customRender: 'hasAttachments' },
    //customRender: (text,record,index) => `${record.hasAttachments? '是' : ''}`,
  },
  {
    title: "已处理",
    width: 60,
    align: "center",
    className:'userStyle',
    customRender: (text,record,index) => `${record.consumed == true ? '是' : record.consumed == false ?'否':'空'}`,
    ellipsis:true,
  },
  // {
  //   title: "mail",
  //   width: 100,
  //   dataIndex: 'mail',
  //   align: "left",
  //   className:'userStyle',
  //   ellipsis:true,
  // },
  {
    title: "创建时间",
    width: 160,
    dataIndex: 'createTime',
    className:'userStyle',
    align: "left",
    ellipsis:true,
  },
]
const columns1=[
{
    title: "工厂", 
    width: 120,
    dataIndex: 'joinFactoryName',
    className:'userStyle',
    align: "left",
    ellipsis:true,
  },
  {
    title: "数量",
    width: 120,
    dataIndex: 'mailNum',
    className:'userStyle',
    align: "left",
    ellipsis:true,
  },
]
export default{
    name:'',
    components:{},
    inject:['reload'],
    data(){
        return{
      showText:false,
      fileListdata:[],
      messageList:'',
      showTitle:'',
      dataopen:false,
      text:'',
      isDragging: false,
      menuVisible:false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex:99
      },
      menuData:{},
         startIndex: -1,  
          shiftKey:false,
          spinning:false,
          formData:{
            Subject:'',
            custNo:'',
            From:'',
            Cc:'',
            Body:'',
            consumed:'否',
            effective:'全部',
            joinFactoryId:undefined,
          },
          columns,
          columns1,
          orderListTableLoading:false,
          pagination: {
            pageSize: 20,
            pageIndex: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
          },
          Statslist:[],
          orderListData:[],
          dataVisible1:false,
          filePaths:'',
          selectedId:'',
          dataVisibleMode:false,
          maildataVisible:false,
          orderno:'',
          businessOrderNo:'',
          messageMode:'',
          isFileType:false,
          ttype:'',
          pageshow:true,
          selectedRowKeysArray:[],
          factroyList:[],
          isCtrlPressed:false
        }
    },
    mounted(){
    this.getOrderList()   
    window.addEventListener('keydown',this.keydown)
    window.addEventListener('keyup',this.keyup)
    factroyList().then(res =>{
      if(res.code){
        this.factroyList = res.data
      }else{
        this.$message.error(res.message)
      }
    })
    },
  methods: {
    checkPermission,  
    clickUpload(){
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent('click'))
    },
    beforeUpload(file){     
      this.isFileType = file.name.toLowerCase().indexOf('.zip') != -1 || file.name.toLowerCase().indexOf('.rar') != -1
      || file.name.toLowerCase().indexOf('.xls') != -1 || file.name.toLowerCase().indexOf('.pdf') != -1|| file.name.toLowerCase().indexOf('.xlsx') != -1 
        if (!this.isFileType) {
          this.$message.error('添加附件只支持.rar/.zip/.pdf/.xls/.xlsx格式文件');
          return false
        }
      },
    async httpRequest(data,type) {
      const formData = new FormData();
      formData.append("file", data.file); 
      await modulenouploadfile(formData,).then(res => {
          if (res.code == 1 ) {
            data.onSuccess(res.data);
            this.$message.success('文件上传成功')
          } else {
            this.$message.error(res.message)
          }
        })
    },
    handleChange({ fileList }) {
      if(this.isFileType){
        this.fileListdata = fileList;
      }
        
    },
    mapKey(data){
      if (!data) {
        return []
      } else {
        return Object.keys(data).map(item => {
          return {'value':item, 'lable': data[item]}
        })
      }
    },
    contentshow(record){
        this.dataopen = true
        this.showTitle='主题：' +  record.subject
        this.messageList=record.body
     },
    dataCancel(){
      this.dataopen = false
    },
    Drawerdisplay(e,text,record){
      if(record.subject.replace(/[\r\n\s]/g, "")!=text.replace(/[\r\n\s]/g, "") && record.body.replace(/[\r\n\s]/g, "")!=text.replace(/[\r\n\s]/g, "") ){
        this.dataopen=false
      }else{
        this.dataopen=true
      }
    },
    rightClick1(e,text,record){
       let event = e.target 
      if(e.target.localName != 'td'){
        event = e.target.parentNode
      }     
      this.text=event.innerText;
      if(event.className.indexOf('noCopy') != -1 || !this.text){
        this.showText = false
      }else{    
        this.showText = true
      } 
      if(event.cellIndex == 1){
        this.text=this.text.split(" ")[0]
      }
      const tableWrapper = this.$refs.tableWrapper;    
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;   
      let  offsetx= event.offsetLeft  + event.offsetWidth - 10
      let offsety = event.offsetTop + 40;     
      if(event.cellIndex == this.columns.length -1){
        this.menuStyle.top =  cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      }else{
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }       
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },    
    down(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
      }, 
    getOrderList(queryData){    
      let params = {
        ...this.pagination,
      }
      if(queryData){
        params.Subject = queryData.Subject;
        params.From = queryData.From;       
        params.Body = queryData.Body;
        params.consumed = queryData.consumed;
        params.effective = queryData.effective;
        params.Cc = queryData.Cc;
        params.joinFactoryId = queryData.joinFactoryId;
        params.custNo = queryData.custNo;
       }else{
        params.consumed='否'
        params.effective='全部'
       }
      this.orderListTableLoading = true;
      pageList(params).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
          if((params.Subject ||  params.consumed ||  params.joinFactoryId ||params.effective ||params.custNo ) &&  this.orderListData.length){
            this.selectedRowKeysArray =  [this.orderListData[0].id]
          }
          this.pagination.total = res.data.totalCount;   
          this.pagination.pageIndex = res.data.pageIndex    
        }
      }).finally(()=> {
        this.orderListTableLoading = false;
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysArray = selectedRowKeys;
    },   
    keyup(e){
      this.shiftKey = e.ctrlKey 
      if (e.key === 'Control') {
     this.isCtrlPressed = false;
    }  
    },
  
    keydown(e){
        this.shiftKey = e.ctrlKey
        if (e.key === 'Control') {
        this.isCtrlPressed = true;
        }
        if(e.keyCode == '83' && this.isCtrlPressed){
          e.preventDefault()
          this.emailstart()
          this.isCtrlPressed = false;
        }else if(e.keyCode == '67' && !this.isCtrlPressed && this.dataVisibleMode){
          this.reportHandleCancel1()
          e.preventDefault()
          this.isCtrlPressed = false;
        }else if(e.keyCode == '13' && this.dataVisibleMode ){
          this.handleOkMode()
          e.preventDefault()
        }
        // else if (e.keyCode == '13' ){
        //   e.preventDefault()
        // }
        },  
      updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.orderListData.slice(normalizedStart, normalizedEnd + 1);
      var arr=[]
      for(var a=0;a<selectedRowsData.length;a++){
        arr.push(selectedRowsData[a].id)
      }
      this.selectedRowKeysArray = arr
      if(startIndex < endIndex){
        this.selectedRowsData = this.orderListData.filter(item => {return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length-1]})[0]  
      }else{
        this.selectedRowsData = this.orderListData.filter(item => {return item.id == this.selectedRowKeysArray[0]})[0]  
      }    
      if(this.selectedRowKeysArray.length > 1){
        this.dataopen = false
      }
    },
    handleMouseMove(event, record, index) {     
      event.stopPropagation();
      if (this.isDragging &&  event.button === 0 && this.startIndex != index ) {
        this.updateSelectedItems(this.startIndex, index);
      }
    },    
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if(this.shiftKey){
        let rowKeys = this.selectedRowKeysArray;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }  
        this.selectedRowKeysArray = rowKeys;      
      }else{
        if(this.startIndex == index){   
          this.selectedRowKeysArray = [record.id]
        }         
      }
      this.shiftKey = false      
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
      
    },
      customRow1(record, index) {
      return {
        on: {
          mousedown: (event) => this.handleMouseDown(event, record, index),
          mousemove: (event) => this.handleMouseMove(event, record, index),
          mouseup: (event) => this.handleMouseUp(event, record, index),
          click: e =>{
            let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
            } 
            this.Drawerdisplay(e, text, record)
            this.contentshow(record)
          },
          contextmenu: e => {
           let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
            } 
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record)
          },
        },
      };
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeysArray;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeysArray = rowKeys;               
          },
        }
      }
    }, 
    handleTableChange(pagination, ) {
      this.pagination.pageIndex=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      let params = this.formData
      if(params.Status != undefined){
        params.Status = Number(this.formData.Status)
      } 
      this.getOrderList(params)
    }, 
    searchClick(){
      let params = this.formData
      var arr1 = params.Subject.split('')
      if(arr1.length >30){
        arr1 = arr1.slice(0,30)
      }
      params.Subject = arr1.join('')
      var arr2 = params.From.split('')
      if(arr2.length >30){
        arr2 = arr2.slice(0,30)
      }
      params.From = arr2.join('')
      var arr3 = params.Cc.split('')
      if(arr3.length >30){
        arr3 = arr3.slice(0,30)
      }
      params.Cc = arr3.join('')
      var arr4 = params.Body.split('')
      if(arr4.length >30){
        arr1 = arr4.slice(0,30)
      }
      params.Body = arr4.join('') 
      this.pageshow = false
      this.pagination.pageIndex = 1;
      this.getOrderList(params)
      this.$nextTick(() => {
          this.pageshow = true
      })
    }, 
    receiveemail(){
      receiveEmail().then(res=>{
        if(res.code){
          this.$message.success('接收成功')
          this.getOrderList()
        }else{
          this.$message.error(res.message)
        }
      })
    }, 
   
    CreateOrder(){
      if(!this.selectedRowKeysArray.length){
        this.$message.warning('请至少选择一行记录！')
        return
      }
      eMimeOrder(this.selectedRowKeysArray).then(res=>{
        if(res.code){
          this.$message.success('创建成功')
          this.getOrderList()
        }else{
          this.$message.error(res.message)
        }
      })
    }, 
    //开始弹窗
    emailstart(){
      if(!this.selectedRowKeysArray.length){
        this.$message.warning('请至少选择一行记录！')
        return
      }
      this.dataVisibleMode=true
      this.ttype=1
      this.messageMode='确定订单开始吗？'
    },  
     //标记完成弹窗
     invalidemail(){
      if(!this.selectedRowKeysArray.length){
        this.$message.warning('请至少选择一行记录！')
        return
      }
      this.dataVisibleMode=true
      this.ttype=2
      this.messageMode='确定设置为标记完成吗？'
    },  
    //邮件回复弹窗
    EmailReply(){
      // if(!this.selectedRowKeysArray.length){
      //   this.$message.warning('请至少选择一行记录！')
      //   return
      // }
      this.maildataVisible=true
    },
    mailhandleOkMode(){
      this.maildataVisible=false
    },
    handleOkMode(){
      if(this.ttype==1){
        emimestart(this.selectedRowKeysArray).then(res=>{
          if(res.code){
            this.$message.success('开始成功')
            this.getOrderList()
            this.selectedRowKeysArray=[]
          }else{
            this.$message.error(res.message)
          }
        })
        this.dataVisibleMode=false
      }
      if(this.ttype==2){
        consumeorder(this.selectedRowKeysArray).then(res=>{
        if(res.code){
          this.$message.success('设置成功')
          this.getOrderList()
        }else{
          this.$message.error(res.message)
        }
        this.dataVisibleMode = false;
      })
      }
    }, 
    isRedRow(record){
      let strGroup = []
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push('rowBackgroundColor')
      } 
      return strGroup   
    },  
    reportHandleCancel1(){
      this.dataVisibleMode = false
      this.maildataVisible=false
    }   
  }
}
</script>
<style scoped lang="less">
/deep/.ant-drawer-title{
  font-weight: bold;
}
/deep/.ant-drawer-body {
    padding: 20px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
}
.fontcolor{
  color:red
}
.hstyle{
  height: 40px;
}

/deep/.ant-upload-list{
 // width: 200px;
  margin-left: 113px;
}
/deep/.ant-drawer-content-wrapper{
  width: 652px!important;
}
.drawerclass{
  margin-right: 14px;
}
.drawre{
  max-height:570px;
  width: 612px;
  white-space: break-spaces;
  &::-webkit-scrollbar {
      //整体样式
      width: 7px !important; //y轴滚动条粗细
      height: 7px !important; //x轴滚动条粗细
    }
    &::-webkit-scrollbar-thumb {
      //滑动滑块条样式
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      background: #a5a5a5;
    }
    overflow: auto;
}
/deep/.ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
.tabRightClikBox{
  // border:2px solid rgb(238, 238, 238) !important;
  li{
    height:24px;
    line-height:24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color:#000000
  }
}
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  color:#000000;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color:#000000;
}
/deep/.ant-input{
  font-weight:500;
  color:#000000;
}
/deep/.ant-select{
  font-weight:500;
  color:#000000;
}

/deep/.rightContent{
  height: 780px;
  background-color: #FFF;
  border-top: 2px solid #efefef;
}
/deep/.userStyle{
    user-select: none!important;
  }

 /deep/.ant-tag {
    font-size: 12px;    
     color:#ff9900;
     padding: 0 2px; 
     margin: 0; 
     margin-right: 3px; 
     height: 21px;
     user-select: none;
  }
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
.projectBackend {
  min-width: 1670px;
  .content{
    height:49px;
    padding-left:6px;
    background: #FFFFFF;
    /deep/.ant-select-selection__placeholder{
      display: block;
    }
  }
  .ant-input,.ant-select{
    width:8%;
    margin-right:0.5%;
    margin-top:6px;
  }
  // height: 834px;
  width: 100%;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  /deep/.leftContent {
    
    .ant-table-selection-column{
      padding: 6px 0!important;
      width: 25px!important;
      display: inline-block;
    }
    background: #FFFFFF;
    .min-table {      
      .ant-table-body{
        // min-height:733px;
        min-height:680px;
      }
    }
    height:720px;
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
    border-left:4px solid #f0f2f5;
    // border-bottom: 4px solid rgb(233, 233, 240);
  }
  .bto{
    width: 100%;
    height:54px;
    border: 2px solid #E9E9F0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #FFFFFF;
    border-top:0;
}
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  /deep/ .ant-table-thead > tr > th {
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table{
    min-height: 720px;
    .ant-table-thead > tr > th{
      padding:7px 2px;
      // border-color: #f0f0f0;
      border-right:1px solid #efefef;
    }    
    .ant-table-tbody > tr > td {
      padding:7px 2px;
      border-right:1px solid #efefef;
      // border-color: #f0f0f0;
      position:relative;
      // .topCss{
      //   position:absolute;
      //   top:3%;        
      // }
      // .topCss1{
      //   position:absolute;
      //   top:3%;
      //   // left:30%;
        
      // }
      // .topCss2{
      //   position:absolute;
      //   top:3%;
      //   // left:25%;
        
      // }
    }
    tr.ant-table-row-selected td {
     background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: #000000;
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin:13px 0 0 10px;
    position: absolute;
  }

}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background:#F8F8F8;
  // background: #F0F2F5;
}
</style>
