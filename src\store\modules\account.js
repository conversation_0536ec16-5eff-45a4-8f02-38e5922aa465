export default {
  namespaced: true,
  state: {
    user: undefined,
    permissions: null,
    //buryingpoint: JSON.parse(localStorage.getItem(process.env.VUE_APP_DATA_KEY)) || [],
    buryingpoint: [],
    roles: null,
    routesConfig: null,
    userinfo:undefined,
  },
  getters: {
    buryingpoint: state => {
      if (!state.buryingpoint) {
        try {
          const buryingpoint = localStorage.getItem(process.env.VUE_APP_DATA_KEY)
          state.buryingpoint = JSON.parse(buryingpoint)
        } catch (e) {
          console.error(e)
        }
      }
      return state.buryingpoint
    },
    user: state => {
      if (!state.user) {
        try {
          const user = localStorage.getItem(process.env.VUE_APP_USER_KEY)
          state.user = JSON.parse(user)
        } catch (e) {
          console.error(e)
        }
      }
      return state.user
    },
    userinfo: state => {
      if (!state.userinfo) {
        try {
          const userinfo = localStorage.getItem(process.env.VUE_APP_USERINFO_KEY)
          state.userinfo = JSON.parse(userinfo)
        } catch (e) {
          console.error(e)
        }
      }
      return state.userinfo
    },
    permissions: state => {
      if (!state.permissions) {
        try {
          const permissions = localStorage.getItem(process.env.VUE_APP_PERMISSIONS_KEY)
          state.permissions = JSON.parse(permissions)
          state.permissions = state.permissions ? state.permissions : []
        } catch (e) {
          console.error(e.message)
        }
      }
      return state.permissions
    },
    roles: state => {
      if (!state.roles) {
        try {
          const roles = localStorage.getItem(process.env.VUE_APP_ROLES_KEY)
          state.roles = JSON.parse(roles)
          state.roles = state.roles ? state.roles : []
        } catch (e) {
          console.error(e.message)
        }
      }
      return state.roles
    },
    routesConfig: state => {
      if (!state.routesConfig) {
        try {
          const routesConfig = localStorage.getItem(process.env.VUE_APP_ROUTES_KEY)
          state.routesConfig = JSON.parse(routesConfig)
          state.routesConfig = state.routesConfig ? state.routesConfig : []
        } catch (e) {
          console.error(e.message)
        }
      }
      return state.routesConfig
    }
  },
  mutations: {
    setUser (state, user) {
      state.user = user
      localStorage.setItem(process.env.VUE_APP_USER_KEY, JSON.stringify(user))
    },
    setUserinfo (state, userinfo) {
      state.userinfo = userinfo
      localStorage.setItem(process.env.VUE_APP_USERINFO_KEY, JSON.stringify(userinfo))
    },
    clearBuryingPoint(state) {
      state.buryingpoint = [];
    },
    dataprocessing(state,buryingpoint){
    const result = {};
    buryingpoint.forEach((item) => {
      const key = `${item.Modulepage}-${item.name}-${item.time}-${item.usingFeatures}`;
      if (!result[key]) {
        result[key] = { ...item, Numberofuses: 1 };
      } else {
        result[key].Numberofuses++;
      }
    });
    const processedData = Object.values(result);
    const result1 = {};
    processedData.forEach((item) => {
    const key = item.Modulepage;
      if (!result1[key]) {
        result1[key] = { ...item, Usagefrequency: [{ usingFeatures: item.usingFeatures, Numberofuses: item.Numberofuses }]};
      } else {
        result1[key].Usagefrequency.push({ usingFeatures: item.usingFeatures, Numberofuses: item.Numberofuses });
      }
    });
    const processedData1 = Object.values(result1);
    processedData1.forEach((item) => {
      delete item.Numberofuses;
      delete item.usingFeatures;
    });
    state.buryingpoint =processedData1;
    },
    Buriedpointcache(state, buryingpoint){
      localStorage.setItem(process.env.VUE_APP_DATA_KEY, JSON.stringify(buryingpoint))
    },
    setPermissions(state, permissions) {
      state.permissions = permissions
      localStorage.setItem(process.env.VUE_APP_PERMISSIONS_KEY, JSON.stringify(permissions))
    },
    setRoles(state, roles) {
      state.roles = roles
      localStorage.setItem(process.env.VUE_APP_ROLES_KEY, JSON.stringify(roles))
    },
    setRoutesConfig(state, routesConfig) {
      state.routesConfig = routesConfig
      localStorage.setItem(process.env.VUE_APP_ROUTES_KEY, JSON.stringify(routesConfig))
    }
  }
}
