<!-- 市场管理-订单详情基本信息  -->
<template>
  <div class="contentInfo" ref="SelectBox">
    <a-form-model layout="inline" id="formDataElem" v-show="editFlag">
      <a-col :span="3" class="line3">
        <a-form-model-item
          label="板厚孔径比"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.apertureRatio && iseval(requiredLinkConfigList.apertureRatio.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.apertureRatio" disabled />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="锣带长度(m/㎡)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.routLength && iseval(requiredLinkConfigList.routLength.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input allowClear v-model="formData.routLength" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="孔密度(W/㎡)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.poreDensity && iseval(requiredLinkConfigList.poreDensity.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.poreDensity" @change="numChange" disabled />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="测试点数(W/㎡)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.testPointsm2 && iseval(requiredLinkConfigList.testPointsm2.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <span>{{ (formData.testPointsm2 / 1000).toFixed(2) || 0 }}</span>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="难度板"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.isDifficultyBoard && iseval(requiredLinkConfigList.isDifficultyBoard.isNullRules) && required ? 'require' : ''
          "
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isDifficultyBoard" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="最小孔铜um"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.minHoleCopper && iseval(requiredLinkConfigList.minHoleCopper.isNullRules) && required ? 'require' : ''"
          v-show="formData.boardLayers >= 2"
        >
          <div class="editWrapper" style="display: flex">
            <a-select
              style="display: inline-block"
              v-model="formData.minHoleCopper"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
              @change="setEstimate1($event, mapKey(selectOption.MinHoleCopper))"
              @search="handleSearch1($event, mapKey(selectOption.MinHoleCopper))"
              @blur="handleBlur1($event, mapKey(selectOption.MinHoleCopper))"
            >
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.MinHoleCopper)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="假层"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.totalLayer && iseval(requiredLinkConfigList.totalLayer.isNullRules) && required ? 'require' : ''"
          v-show="showMore"
        >
          <div class="editWrapper">
            <a-input v-model="formData.totalLayer" :disabled="boardLayers1" allowClear></a-input>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="合拼款数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.pinBanNum && iseval(requiredLinkConfigList.pinBanNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.pinBanNum" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="工厂合拼"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isFacHp && iseval(requiredLinkConfigList.isFacHp.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.isFacHp" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="线圈板"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isCoilPlate && iseval(requiredLinkConfigList.isCoilPlate.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isCoilPlate" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="PPAP"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.ppap && iseval(requiredLinkConfigList.ppap.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.ppap" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="导热系数w/m.k"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          class="daore"
          :class="
            requiredLinkConfigList.thermalConductivity && iseval(requiredLinkConfigList.thermalConductivity.isNullRules) && required ? 'require' : ''
          "
        >
          <div class="editWrapper">
            <a-select
              v-model="formData.thermalConductivity"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
              @change="setEstimate3($event, mapKey(selectOption.ThermalConductivity))"
              @search="handleSearch3($event, mapKey(selectOption.ThermalConductivity))"
              @blur="handleBlur3($event, mapKey(selectOption.ThermalConductivity))"
            >
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.ThermalConductivity)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="阻焊三级标准"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isSolderMaskIpc3 && iseval(requiredLinkConfigList.isSolderMaskIpc3.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isSolderMaskIpc3" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="埋阻"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.buriedResistance && iseval(requiredLinkConfigList.buriedResistance.isNullRules) && required ? 'require' : ''"
          v-show="showMore"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.buriedResistance" @change="$emit('cutchange')" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="客户转单"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isTransferOrder && iseval(requiredLinkConfigList.isTransferOrder.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isTransferOrder" />
          </div>
        </a-form-model-item>
      </a-col>
      <a-col :span="3" class="line3">
        <a-form-model-item
          label="铜浆塞孔次数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.cuPlugHoleNum && iseval(requiredLinkConfigList.cuPlugHoleNum.isNullRules) && required ? 'require' : ''"
          v-show="show2 || showMore"
        >
          <div class="editWrapper">
            <a-select v-model="formData.cuPlugHoleNum" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.CuPlugHoleNum)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="银浆塞孔次数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.silverPlugHoleNum && iseval(requiredLinkConfigList.silverPlugHoleNum.isNullRules) && required ? 'require' : ''
          "
          v-show="show2 || showMore"
        >
          <div class="editWrapper">
            <a-select
              v-model="formData.silverPlugHoleNum"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.SilverPlugHoleNum)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="树脂塞孔次数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.resinPlugHoleNum && iseval(requiredLinkConfigList.resinPlugHoleNum.isNullRules) && required ? 'require' : ''"
          v-show="show2 || showMore"
        >
          <div class="editWrapper">
            <a-select
              v-model="formData.resinPlugHoleNum"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.ResinPlugHoleNum)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="低阻测试"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.lowResistanceTest && iseval(requiredLinkConfigList.lowResistanceTest.isNullRules) && required ? 'require' : ''
          "
          v-show="formData.boardLayers >= 2"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.lowResistanceTest" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="确认工作稿"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.confirmWorkingDraft && iseval(requiredLinkConfigList.confirmWorkingDraft.isNullRules) && required ? 'require' : ''
          "
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.confirmWorkingDraft" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="确认阻抗"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.confirmImpedance && iseval(requiredLinkConfigList.confirmImpedance.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.confirmImpedance" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="确认叠层"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.confirmStacking && iseval(requiredLinkConfigList.confirmStacking.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.confirmStacking" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="附出货菲林"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.attachedShippingFilm && iseval(requiredLinkConfigList.attachedShippingFilm.isNullRules) && required
              ? 'require'
              : ''
          "
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.attachedShippingFilm" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="放湿度卡"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.humidityCard && iseval(requiredLinkConfigList.humidityCard.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-select v-model="formData.humidityCard" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.HumidityCard)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="板间隔纸"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.boardSpacingPaper && iseval(requiredLinkConfigList.boardSpacingPaper.isNullRules) && required ? 'require' : ''
          "
        >
          <div class="editWrapper">
            <a-select
              v-model="formData.boardSpacingPaper"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.BoardSpacingPaper)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="干燥剂"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.withoutDesiccant && iseval(requiredLinkConfigList.withoutDesiccant.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-select
              v-model="formData.withoutDesiccant"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.WithoutDesiccant)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="包装数量(PCS)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.packagingQuantity && iseval(requiredLinkConfigList.packagingQuantity.isNullRules) && required ? 'require' : ''
          "
        >
          <div class="editWrapper">
            <a-input v-model="formData.packagingQuantity" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="加盖ET章"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.stampET && iseval(requiredLinkConfigList.stampET.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-select v-model="formData.stampET" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.StampET)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="备品数量(PCS)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.spareQuantity && iseval(requiredLinkConfigList.spareQuantity.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.spareQuantity" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item label="相关品牌" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <div class="editWrapper">
            <a-select v-model="formData.relatedBrand" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.RelatedBrand)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
      </a-col>
      <a-col :span="3" class="line3">
        <a-form-model-item
          label="订单评审"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isOrderReview && iseval(requiredLinkConfigList.isOrderReview.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isOrderReview" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="自由拼版"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.freePanel && iseval(requiredLinkConfigList.freePanel.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.freePanel" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="LED板"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isLedPlate && iseval(requiredLinkConfigList.isLedPlate.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isLedPlate" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="揭盖板"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isUncoverPlate && iseval(requiredLinkConfigList.isUncoverPlate.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isUncoverPlate" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="HVA"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isHva && iseval(requiredLinkConfigList.isHva.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isHva" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="提供阻抗条"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isImpTestStrip && iseval(requiredLinkConfigList.isImpTestStrip.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isImpTestStrip" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="NP更改菲林数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.npFilmsNum && iseval(requiredLinkConfigList.npFilmsNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.npFilmsNum" />
          </div>
        </a-form-model-item>
        <a-form-model-item label="NP更改类型" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <div class="editWrapper" style="display: flex">
            <a-select style="display: inline-block" v-model="formData.npChangeType" showSearch allowClear optionFilterProp="lable">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.NPChangeType)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="测试架倍密"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.testTimesDense && iseval(requiredLinkConfigList.testTimesDense.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper" style="display: flex">
            <a-select style="display: inline-block" v-model="formData.testTimesDense" showSearch allowClear optionFilterProp="lable">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.TestTimesDense)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="四线测试"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.fourWireTest && iseval(requiredLinkConfigList.fourWireTest.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.fourWireTest" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="贴保护膜"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.layProtectiveFilm && iseval(requiredLinkConfigList.layProtectiveFilm.isNullRules) && required ? 'require' : ''
          "
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.layProtectiveFilm" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="二维码方式"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.qrCodeWay && iseval(requiredLinkConfigList.qrCodeWay.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-select v-model="formData.qrCodeWay" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="item in mapKey(selectOption.QrCodeWay)"
                :key="item.value"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="水印"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.waterMark_ && iseval(requiredLinkConfigList.waterMark_.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.waterMark_" />
          </div>
        </a-form-model-item>
        <a-form-model-item label="品牌标记" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <div class="editWrapper">
            <a-select v-model="formData.brandMark" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.BrandMark)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
      </a-col>
    </a-form-model>
  </div>
</template>
<script>
export default {
  name: "",
  props: [
    "editFlag",
    "spinning",
    "showData",
    "selectOption",
    "boardBrandList",
    "sheetTraderList",
    "boardtgList",
    "frontDataZSupplierf",
    "reOrder",
    "requiredLinkConfigList",
    "joinFacId",
    "required",
    "ManufacturerTG",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "formData",
  ],
  components: {},
  data() {
    return {
      dataVisible: false,
      dataVisible1: false,
      ReportList: [], // 出货报告列表
      boardLayers1: false,
      blindBuryStr1: false,
      plateTypeStr1: false,
      obj: {},
      sheetTrader: [],
      boardBrand: [],
      src: "",
      IPCLevel: [],
      tslge: false,
      frontDataZSupplier: [],
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      spinning1: false,
    };
  },
  created() {
    this.$nextTick(() => {
      // this.getEditData();
    });
  },
  mounted() {},
  watch: {
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    showData: {
      handler(val) {
        if (
          val.boardBrand == "RO4350B" ||
          val.boardBrand == "RO4003C" ||
          val.boardBrand == "SH260" ||
          val.boardBrand == "CT350" ||
          val.boardBrand == "CT338"
        ) {
          this.tslge = true;
        } else {
          this.tslge = false;
        }
      },
    },
  },

  methods: {
    mapKey(data) {
      if (!data || data.length == 0) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    numChange() {
      //测试点数(W/㎡)计算
      //测试点数 = 单板测试点*su数/交货尺寸x/交货尺寸y/10000 保留两位小数
      if (this.formData.testPointNum && this.formData.su && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        const testPointNum = this.formData.testPointNum; // 单板测试点
        const su = this.formData.su; // SU数
        const boardHeightM = this.formData.setBoardHeight / 1000; // 交货尺寸Y（转换为米）
        const boardWidthM = this.formData.setBoardWidth / 1000; // 交货尺寸X（转换为米）
        // 计算测试点数
        const testPoints = (testPointNum * su) / boardHeightM / boardWidthM;
        this.formData.testPointsm2 = Math.floor(testPoints);
      } else {
        this.formData.testPointsm2 = null;
      }
      if (this.formData.pinBanType1 && this.formData.pinBanType2) {
        this.formData.su = (Number(this.formData.pinBanType1) * this.formData.pinBanType2).toFixed();
      } else {
        this.formData.su = null;
      }
      if (this.formData.boardType == "set" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.boardArea = ((this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000).toFixed(2);
      }
      if (this.formData.boardType == "pcs" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth && this.formData.su) {
        this.formData.boardArea = (
          (this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) /
          1000000 /
          this.formData.su
        ).toFixed(2);
      }
      if (this.formData.su >= 2) {
        this.formData.boardType = "SET";
      } else if (this.formData.setBoardHeight > this.formData.pinBanType1 || this.formData.setBoardWidth > this.formData.pinBanType2) {
        this.formData.boardType = "SET";
      } else {
        this.formData.boardType = "PCS";
      }
      if (this.formData.totalHoleNum && this.formData.su && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.poreDensity = (
          ((Number(this.formData.totalHoleNum) + Number(this.formData.slotHoleNum) + (Number(this.formData.blindHoleNum) || 0)) * this.formData.su) /
          ((this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000) /
          10000
        ).toFixed(2);
      } else {
        this.formData.poreDensity = null;
      }
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate1(value, list) {
      this.formData.minHoleCopper = value;
      let a = this.getPrice(this.formData.minHoleCopper, list, value);
      this.$forceUpdate();
    },
    handleSearch1(value, list) {
      this.setEstimate1(value, list);
    },
    handleBlur1(value, list) {
      this.setEstimate1(value, list);
    },
    setEstimate3(value) {
      this.formData.thermalConductivity = value;
    },
    handleSearch3(value, list) {
      this.setEstimate3(value, list);
    },
    handleBlur3(value, list) {
      this.setEstimate3(value, list);
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("formData") != -1) {
        newIsNullRules = newIsNullRules.replace(/formData/g, "this.formData");
      }
      if (val.indexOf("reOrder") != -1) {
        newIsNullRules = newIsNullRules.replace(/reOrder/g, "this.reOrder");
      }
      if (val.indexOf("tslge") != -1) {
        newIsNullRules = newIsNullRules.replace(/tslge/g, "this.tslge");
      }
      if (val.indexOf("boardBrand") != -1) {
        newIsNullRules = newIsNullRules.replace(/boardBrand/g, "this.boardBrand");
      }

      return eval(newIsNullRules);
    },
  },
};
</script>
<style scoped lang="less">
.guokong {
  /deep/.ant-select {
    width: 36% !important;
  }
  /deep/.ant-input {
    padding: 0 !important;
  }
}
.widthclass {
  .select1 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 37px !important;
    }
  }
  .select2 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 56px !important;
    }
  }
}
.bborder {
  .div2 {
    /deep/.ant-form-item-control {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

/deep/b {
  font-weight: 500;
}
.div22 {
  /deep/.ant-form-item-control {
    padding: 0;
    max-height: 65px;
    min-height: 24.5px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
/deep/.require11 {
  color: red !important;
}
// .cu{
//   /deep/.ant-form-item-control{
//     height:26.67px;
//   }
// }
#formDataElem1 {
  .div1 {
    .ant-form-item {
      width: 30%;
    }
    /deep/ .ant-form-item-control-wrapper {
      // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
      font-family: PingFangSC-Regular, Sans-serif;
      font-weight: 500;
      color: #000000;
      font-size: 13px;
      .ant-form-item-control {
        .ant-form-item-children {
          display: block;
          min-height: 13.672px;
        }
        line-height: inherit;
        padding: 2px 4px !important;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
      }
    }
    // /deep/.pcbFileName{
    //   .ant-form-item-label{
    //     width:30%;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:70%;
    //   }
    // }
  }
}
/deep/.ant-input-affix-wrapper {
  width: 100%;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 6px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 2px;
}
/deep/.line3 {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
  // .ant-form-item-control-wrapper {
  //   .ant-form-item-control{
  //     border:0!important;
  //   }
  // }
}
.bbb {
  /deep/.ant-form-item-label {
    width: 16.65%;
    // -ms-width: 101px important; // IE
    // -webkit-width:100.5%; //谷歌
    border-left: 1px solid #ddd;
  }
  /deep/.ant-form-item-control-wrapper {
    // -ms-width: 911px!important; // IE
    // -webkit-width:942.0.5%; //谷歌
    width: 83.3%;
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
// .bbb{
//   /deep/.ant-form-item-label{
//     width:105px;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   /deep/.ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     // width:1038px;
//     width:100%;
//   }
//   /deep/textarea.ant-input {
//     min-height:24px;
//   }
//   /deep/.ant-form-item-control{
//     padding: 2px !important;
//     // width: 942px;
//     width:100%;
//   }
//   /deep/.ant-input{
//     height:24px;
//   }
// }
// /deep/.editWrapper1 {
//   .ant-form-item-label{
//     width:10%;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   .ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     width:1092px;
//   }
// }

/deep/.ant-select-selection--single {
  height: 22px !important;
}
/deep/.ant-select-item-option-content {
  color: red !important;
}
/deep/.ant-select-selection__rendered {
  line-height: 20px !important;
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.require1 {
  .ant-form-item-label > label {
    color: red !important;
    background-color: greenyellow;
  }
}
// /deep/.bac{
//     .ant-form-item-label > label {
//     color: red!important;
//     background-color: #ff9900;
// }

// }
span {
  font-size: 13px;
}

/deep/.ant-select {
  font-size: 13px !important;
}
/deep/.ant-input {
  font-size: 13px !important;
  font-weight: 500;
}
.contentInfo {
  font-size: 13px;
  width: 1676px;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    font: 12px / 1.14 arial;
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 10.5%;
        text-indent: 0.5%;
        font-size: 13px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 10.5%;
      }
    }
    .special {
      height: 268px;
      width: 456px;
      display: inline-block;
      position: absolute;
      right: 172px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 270px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 261px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 270px;
              width: 75px;
            }
          }
        }
      }
    }
    .special1 {
      height: 268px;
      width: 308px;
      display: inline-block;
      position: absolute;
      right: 320px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 267px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 250px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 267px;
            }
          }
        }
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        .ant-row {
          .line2 {
            .ant-form-item-label {
              border-bottom: 0px;
            }
          }
          .line {
            .ant-form-item-control {
              border-bottom: 0px;
            }
          }
        }
      }
      .spec {
        width: 19%;
        position: absolute;
        top: 0;
        right: 0;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .tmp {
          word-break: keep-all;
          vertical-align: top;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: inline-block;
        }
        .editWrapper1 {
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            width: 99%;
          }
        }
        .editWrapper {
          width: 100%;
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            // width:96px;
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            // width: 96px;
            width: 99%;
          }
        }
        .ant-form-item-label {
          // width: 117px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font: 12px/1.14 "微软雅黑", arial;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          // border-left: 1px solid #ddd;
          label {
            // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
            font-family: PingFangSC-Regular, Sans-serif;
            font-size: 13px;
            font-weight: 500;
            color: #000000;
          }
        }
        .ant-form-item-control-wrapper {
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font-family: PingFangSC-Regular, Sans-serif;
          font-weight: 500;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
              white-space: pre-line;
            }
            line-height: inherit;
            padding: 6px 4px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
