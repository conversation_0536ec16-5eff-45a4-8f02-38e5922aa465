<!-- 市场管理 - 价格体系 - 客户区域-->
<template>
<div > 
    <span v-if="checkPermission('MES.MarketModule.PriceSystem.AddNewPriceManage4AreaID')">
        <a-button  type="primary" @click="newlyadded" class="box">
            新增
        </a-button>
    </span>
    <span v-if="checkPermission('MES.MarketModule.PriceSystem.UpPriceManage4AreaID')">
        <a-button  type="primary" @click="editlyadded" class="box">
            编辑
        </a-button>
    </span>
        <a-button  type="primary" @click="saveclick" class="box">
            保存
        </a-button>
        <a-button  type="primary" @click="cancellation" class="box">
            取消
        </a-button>
    <template v-if="ttype=='1' || ttype=='2'" > 
        <a-row>
          <a-col :span='6'>
            <a-form-model-item label="ID"  :label-col="{ span: 6}" :wrapper-col="{ span: 18 }" style="width:100%; margin:0"  >
              <a-input  v-model="aform.id" disabled/>
            </a-form-model-item>
          </a-col>       
          <a-col :span='6'>
            <a-form-model-item label="类型" :label-col="{ span:10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-input  v-model="aform.typeNo_" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span='5'>
            <a-form-model-item label="序号" :label-col="{ span:10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-input   v-model="aform.listNo_"/>
            </a-form-model-item>
          </a-col>
          <a-col :span='6'>
            <a-form-model-item label="代码" :label-col="{ span:10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-input  v-model="aform.listName_"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span='6'>
            <a-form-model-item label="参数"  :label-col="{ span: 6}" :wrapper-col="{ span: 18 }" style="width:100%; margin:0"  >
              <a-input  v-model="aform.listParams_"/>
            </a-form-model-item>
          </a-col>       
          <a-col :span='11' class="caption">
            <a-form-model-item label="说明" :label-col="{ span:6 }" :wrapper-col="{ span: 18 }" style="width:100%; margin:0">
              <a-input   v-model="aform.caption_" style="width: 337px;"/>
            </a-form-model-item>
          </a-col>
          <a-col :span='6'>
            <a-form-model-item label="排序" :label-col="{ span:10 }" :wrapper-col="{ span: 14 }" style="width:100%; margin:0">
              <a-input  v-model="aform.display_" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </template> 
      <div class="projectackend">
        <a-table
          :columns="columns1" 
          :pagination=false
          :customRow="onClickRow"
          :rowClassName="isRedRow"
          :dataSource="dataSource9"
          :scroll="{y:450}"
          :rowKey="'id'"
          >
          <span slot="num" slot-scope="text, record, index">
              {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span> 
        </a-table>
      </div> 
    
    </div>
</template>
<script>
import {checkPermission} from "@/utils/abp";
const columns1 = [
{
    title: "",
    align: "center",
    dataIndex: 'index',
    key: 'index',
    width:50,
    scopedSlots: { customRender: 'num' },
  },
  {
    title: "ID",
    align: "left",
    width:70,
    dataIndex: "id",
  },
  {
    title: "类型",
    align: "left",
    width:80,
    dataIndex: "typeNo_",
  },
  {
    title: "序号",
    align: "left",
    width:70,
    dataIndex: "listNo_",
  },
  {
    title: "代码",
    align: "left",
    width:110,
    dataIndex: "listName_",
    ellipsis:true,
  },
  {
    title: "参数",
    align: "left",
    width:90,
    dataIndex: "listParams_",
  },
  {
    title: "说明",
    align: "left",
    width:110,
    dataIndex: "caption_",
    ellipsis:true,
  },
  {
    title: "排序",
    align: "left",
    width:54,
    dataIndex: "display_",
  },
]
export default{
    name:'',
    components:{},
    props:['dataSource9','ttype','sselectRowData',],
    data(){
        return{
            selectedData: [],
            aform:{
                "id": "",
                "typeNo_": "",
                "listNo_": "",
                "listName_": "",
                "listParams_": "",
                "caption_": "",
                "display_": "",    
            },
            id:'',
            columns1,
            pagination: {
            pageSize: 20,
            pageIndex: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
      }, 
        }
    },
  methods:{
    checkPermission,
    onClickRow(record) {
      return {
        on: {
          click: () => {  
             this.selectedData = record       
            this.$emit('sselectRow',record)         
            this.id = record.id
          }
          
        }
      }
    },
    isRedRow (record) {
      let strGroup = []
      let str =[]
      if (record.id && record.id == this.id) {
        strGroup.push('rowBackgroundColor')
      }      
      return str.concat(strGroup)
    },
    newlyadded(){
      this.$emit('newlyadded')
    },
    editlyadded(){
      this.$emit('editlyadded')
    },
    saveclick(){
      this.$emit('saveclick')
    },
    cancellation(){
      this.$emit('cancellation')
    },
  },

}
</script>
<style scoped lang="less">
.caption{
  /deep/.ant-form-item-label{
    width: 100px;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: #dfdcdc;
    }
/deep/.ant-input{
  font-weight: 500;
}
/deep/.ant-table-column-title{
  color: #000000;
  font-weight: 500;
}

.box {
    width: 70px;
    margin-bottom: 19px;
    text-align: center;
    margin-right: 16px;

    .ant-btn {
      width: 80px;
    }
  }
 .rowBackgroundColor {
      background: #aba5a5;
    }
.projectackend{
    /deep/ .ant-table{
.ant-table-header{
    border-top:1px solid #efefef;
}
.ant-table-thead > tr > th{
  padding: 4px 4px;
  border-right:1px solid #efefef;
  border-left:1px solid #efefef;
}
.ant-table-tbody > tr > td {
    padding: 4px 4px!important;
    border-right:1px solid #efefef;
    border-left:1px solid #efefef;
    max-width: 100px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    color: #000000;
}
tr.ant-table-row-selected td {
 background: #dfdcdc;
}
tr.ant-table-row-hover td {
 background: #dfdcdc;
}
.rowBackgroundColor {
  background: #dfdcdc!important;
}

.ant-table-selection-col {
  width: 20px !important;
}
}
}

</style>