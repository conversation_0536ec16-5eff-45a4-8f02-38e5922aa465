

/// StartLyr 开始层
/// EndLyr  结束层
/// OutLay  是否外层
private  DicImpInfo ImpThicknessCalc(int StartLyr,int EndLyr,bool OutLay){
    var DicImpInfo=new DicImpInfo();//返回值
    var BeginLay //开始层
    var EndLay //结束层
    if(StartLyr>EndLyr){
        BeginLay=EndLyr;
        EndLay=StartLyr;
    }else{
        BeginLay=StartLyr;
        EndLay=EndLyr;
    }
    if（BeginLay为空 || EndLay为空）{
        return DicImpInfo;
    }

    var CtlThicknessMIL=0;
    var CTLTopSide=true;
    var ThicknessStartAdd=false;
    var H1ThicknessMM=0;
    var H1ThicknessMIL=0;
    var PPCoreCounter=0;
    var Er1Thickness=0;
    foreach (var item in 叠层列表)
    {
        var LayerName=item.层号;
        var LayerMTR=item.物料; 
        var LayerMM=item.成品厚度；
        var LayerMIL=item.压合厚度;
        if(LayerName==StartLyr){
            CtlThicknessMIL=item.压合厚度;
            CTLTopSide=item.T/B=="t"?true:false;
        }
        if(LayerName==EndLay){
            ThicknessStartAdd=false;
        }
        if(ThicknessStartAdd){
            H1ThicknessMM=H1ThicknessMM+LayerMM;
            H1ThicknessMIL=H1ThicknessMIL+LayerMIL;
            if(LayerMTR=="Core" || LayerMTR=="PP"){
                PPCoreCounter=PPCoreCounter+1;
                Er1Thickness=Er1Thickness+item.DK;
            }
        }
        if(LayerName == BeginLay){
            ThicknessStartAdd=true;
        }
    }
    DicImpInfo.HMM=H1ThicknessMM;
    DicImpInfo.HMIL=H1ThicknessMIL;
    DicImpInfo.Er=Er1Thickness / PPCoreCounter;
    DicImpInfo.CtlMIL=CtlThicknessMIL;
    DicImpInfo.CtlTB=CTLTopSide;
    return DicImpInfo;
}

///改变上参时逻辑
if(上参改变){
    var row=当前行数据；
    var CTLLyr=row.控制层；
    var LayNo=总层数；
    var OutLay=CTLLyr==1 || CTLLyr==LayNo?true:false; //是否外层
    var UpLyr=row.上参；
    var ImpType=row.阻抗类型；
    var Imp_CtlT1=row.T1；

    var Imp_CtlThickness=0;
    var Imp_CtlTB=true;
    var Imp_HValue=0;
    var Imp_ErValue=0;
    var Imp_CtlThicknessInH=0;

    var drtype="http://emsapi.bninfo.com/api/app/e-mSTPub-stack-iMP/imp-type"这个接口获取的data；
    var drtype1=drtype筛选Imp_type_Code=ImpType的数据；
    if(drtype1.lenth==1){
        Imp_CtlThicknessInH=drtype1[0].Imp_CtlThicknessInH;
    }

    var DicImpThickness=ImpThicknessCalc(CTLLyr, UpLyr, OutLay)
    if(DicImpThickness==null){
        return
    }
    Imp_HValue=DicImpThickness.HMIL;
    Imp_ErValue=DicImpThickness.Er;
    Imp_CtlThickness=DicImpThickness.CtlMIL; // Imp_CtlT1  2024/12/13 取值更改为当前控制层的成品厚度mil
    Imp_CtlTB=DicImpThickness.CtlTB;
    if(OutLay)
    {
        row.H1=Imp_HValue+(Imp_CtlThicknessInH==1?Imp_CtlThickness:0);
        row.Er1=Imp_ErValue;
    }
    else
    {
        if(Imp_CtlTB) //2024/3/22 H1与H2计算方式交换
        {
            
            if(ImpType是否存在“_1”)
            {
                var DicImpThicknessNoD=ImpThicknessCalc(CTLLyr, LayNo, false)
                row.H2=DicImpThicknessNoD.HMIL;
                row.Er2=DicImpThicknessNoD.Er;
                row.H1=Imp_HValue+(Imp_CtlThicknessInH==2?Imp_CtlThickness:0);
                row.Er1=Imp_ErValue;
                row.下参=null;
            }else{
                row.H2=Imp_HValue+(Imp_CtlThicknessInH==2?Imp_CtlThickness:0);
                row.Er2=Imp_ErValue;
            }
        }
        else
        {
            row.H1=Imp_HValue+(Imp_CtlThicknessInH==1?Imp_CtlThickness:0);
            row.Er1=Imp_ErValue;
            if(ImpType是否存在“_1”)
            {
                var DicImpThicknessNoD=ImpThicknessCalc(CTLLyr, LayNo, false)
                row.H2=DicImpThicknessNoD.HMIL + Imp_CtlThickness; //2024/3/22 添加Imp_CtlThickness
                row.Er2=DicImpThicknessNoD.Er;
                row.下参=null;
            }
        }
    }
}

///改变下参时逻辑
if(下参改变){
    var row=当前行数据；
    var CTLLyr=row.控制层；
    var LayNo=总层数；
    var OutLay=CTLLyr==1 || CTLLyr==LayNo?true:false; //是否外层
    var DownLyr=row.下参;
    var ImpType=row.阻抗类型;
    var Imp_CtlT1=0;
    var Imp_CtlThickness=0;
    var Imp_CtlTB=true;
    var Imp_HValue=0;
    var Imp_ErValue=0;
    var Imp_CtlThicknessInH=0;

    var drtype="http://emsapi.bninfo.com/api/app/e-mSTPub-stack-iMP/imp-type"这个接口获取的data；
    var drtype1=drtype筛选Imp_type_Code=ImpType的数据；
    if(drtype1.lenth==1){
        Imp_CtlThicknessInH=drtype1[0].Imp_CtlThicknessInH;
    }

    var DicImpThickness=ImpThicknessCalc(CTLLyr, DownLyr, OutLay)
    if(DicImpThickness==null){
        return
    }
    Imp_HValue=DicImpThickness.HMIL;
    Imp_ErValue=DicImpThickness.Er;
    Imp_CtlThickness=DicImpThickness.CtlMIL;
    Imp_CtlTB=DicImpThickness.CtlTB;

    if(OutLay)
    {
        row.H1=Imp_HValue+(Imp_CtlThicknessInH==1?Imp_CtlThickness:0);
        row.Er1=Imp_ErValue;        
    }
    else
    {
        if(Imp_CtlTB)
        {
            row.H1=Imp_HValue+(Imp_CtlThicknessInH==1?Imp_CtlThickness:0);
            row.Er1=Imp_ErValue; 
            if(ImpType是否存在“_1”){
            var DicImpThicknessNoU=ImpThicknessCalc(CTLLyr, 1, false);
            row.H2=DicImpThicknessNoD.HMIL+Imp_CtlThickness;//2024/3/22
            row.Er2=DicImpThicknessNoD.Er;
            row.上参=null;
            }
        }
        else
        {
            if(ImpType是否存在“_1”){
                var DicImpThicknessNoU=ImpThicknessCalc(CTLLyr, 1, false);
                row.H2=DicImpThicknessNoD.HMIL;
                row.Er2=DicImpThicknessNoD.Er;
                row.H1=Imp_HValue+(Imp_CtlThicknessInH==2?Imp_CtlThickness:0);
                row.Er1=Imp_ErValue;
                row.上参=null;
            }else{
                 row.H2=Imp_HValue+(Imp_CtlThicknessInH==2?Imp_CtlThickness:0);
                 row.Er2=Imp_ErValue;
            }
            
        }
    }


}


