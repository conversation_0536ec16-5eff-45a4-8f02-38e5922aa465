<!-- 工具管理- 开料拼版 -->
<template>
  <div class="cutting">
    <a-spin :spinning="loading">
      <a-layout>
        <a-layout-header>
          <footer-action
            @autoClick="autoClick"
            @materialSettingClick="materialSettingClick"
            @handClick="handClick"
            @dataSaveClick="dataSaveClick"
            @openReport="openReport"
          >
          </footer-action>
        </a-layout-header>
        <a-layout>
          <a-layout-sider :width="310">
            <cutting-basic
              ref="basicInfo"
              :imgData="imgData"
              @autoClick="autoClick"
              @abcut="abcut"
              @getOld="getOld"
              @TaoBanChange="TaoBanChange"
              :materialSettingData="materialSettingData"
            />
          </a-layout-sider>
          <a-layout-content>
            <div class="canvasBox" style="height: 604px; overflow-y: auto">
              <a-spin :spinning="spinning" style="height: 600px">
                <a-empty v-if="!imgData.sheetCut" style="height: 100%" />
                <div class="img-box2" v-else>
                  <img v-if="mark" :src="' data:image/png;base64,' + imageUrl" alt="" style="margin-top: 0%; margin-left: 26%; height: 100%" />
                  <img v-else :src="' data:image/png;base64,' + imageUrl" alt="" style="margin-top: 1%; margin-left: 28%; height: 98%" />
                  <!-- <img v-imgscale.cover :src="' data:image/svg+xml;base64,'+ imageUrl"  alt="" /> -->
                </div>
              </a-spin>
            </div>
            <cutting-tab
              :tableLoading="tableLoading"
              :tableData="tableData"
              @getImgData2="getImgData2"
              :TaoBan="TaoBan"
              :showtag="showtag"
              :ABcutting="ABcutting"
              ref="cuttingTab"
            ></cutting-tab>
          </a-layout-content>
          <a-layout-sider :width="216">
            <right-img
              :imgData="imgData"
              :imageLoading="spinning"
              @updateImgUrl="updateImgUrl"
              ref="rightImg"
              :ABcutting="ABcutting"
              :TaoBan="TaoBan"
            ></right-img>
          </a-layout-sider>
        </a-layout>
        <!-- 材料设置弹窗 -->
        <a-modal title="材料设置" :visible="modelVisible" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="450">
          <template slot="footer">
            <a-button type="primary" @click="handleOk1">关闭</a-button>
          </template>
          <material-setting :tableData="materialSettingData" :materialLoading="materialLoading" />
        </a-modal>
        <!--数据报表弹窗 -->
        <a-modal
          title="数据报表"
          :visible="dataVisible"
          @cancel="reportHandleCancel"
          @ok="handleOk2"
          ok-text="下载"
          destroyOnClose
          :maskClosable="false"
          :width="900"
        >
          <report-info :PanelCuttingData="PanelCuttingData" :imgData="imgData" :name="titleName" ref="report" />
        </a-modal>
      </a-layout>
    </a-spin>
  </div>
</template>

<script>
import FooterAction from "@/pages/gongju/cutting/module/FooterAction";
import CuttingBasic from "@/pages/gongju/cutting/module/CuttingBasic";
import { automaticImgae, automaticSplicing, getMaterialData, manualCompositionData, dataSave, settensilecoef } from "@/services/cutting";
import CuttingTab from "@/pages/gongju/cutting/module/CuttingTab";
import RightImg from "@/pages/gongju/cutting/module/RightImg";
import MaterialSetting from "@/pages/gongju/cutting/module/MaterialSetting";
import ReportInfo from "@/pages/gongju/cutting/module/ReportInfo";
export default {
  name: "cutting",
  components: { RightImg, CuttingTab, CuttingBasic, FooterAction, MaterialSetting, ReportInfo },
  data() {
    return {
      tableLoading: false,
      showtag: false,
      tableData: [],
      ppset: "",
      imgData: [],
      spinning: false,
      imageUrl: "",
      modelVisible: false, // 材料设置弹窗,
      materialSettingData: [], //材料设置数据
      materialLoading: false,
      loading: false,
      dataVisible: false, // 数据报表弹窗,
      titleName: "",
      imgH: "",
      mark: true,
      uResult: {},
      pcsset1: [],
      TaoBan: false,
      ABcutting: false,
      // sheetLen :'',
      // sheetWidth :'',
      // panelLen :'',
      // panelWidth: '',
    };
  },
  computed: {
    PanelCuttingData() {
      if (this.tableData.length) {
        return Object.assign(this.tableData[this.$refs.cuttingTab.tabIndex], { name: "auto" });
      } else if (this.imgData.ur && this.tableData.length == 0) {
        return Object.assign(this.$refs.basicInfo.handForm, { name: "manual" });
      } else {
        return [];
      }
    },
  },
  mounted() {
    let params = {
      OrderNo: this.$route.query.job ? this.$route.query.job : "",
      FacKey_: this.$route.query.joinFactoryId ? this.$route.query.joinFactoryId : 0,
      source: this.$route.query.type ? this.$route.query.type : "",
    };
    getMaterialData(params).then(res => {
      if (res.code == 1) {
        this.materialSettingData = res.data;
      } else {
        this.$message.error("不存在材料数据，请核实");
      }
    });
    setTimeout(() => {
      this.TaoBan = this.$refs.basicInfo.form.isTaoBan;
      this.ABcutting = this.$refs.basicInfo.form.isABBoard;
    }, 500);
  },
  methods: {
    //自动拼版
    autoClick() {
      let params = JSON.parse(JSON.stringify(this.$refs.basicInfo.form));
      if (!params.pcsset || !params.cpLen || !params.cpWidth) {
        this.$message.error("请录入成品尺寸长 * 宽 / PCS / SET参数!");
        return;
      }
      this.imgData.sheetCut = "";
      this.imgData.aPnl = "";
      this.imgData.bPnl = "";
      this.imgData.tbPnl = "";
      this.tableData = [];
      // let params = Object.assign(JSON.parse(JSON.stringify(this.$refs.basicInfo.form)),JSON.parse(JSON.stringify(this.$refs.basicInfo.form3)))
      // delete params.job
      delete params.pcssetName;
      delete params.minW;
      delete params.latitudinalSize;
      delete params.sheetLen;
      delete params.sheetWidth;
      // delete params.cpSx
      // delete params.coreSum
      // delete params.ppSu
      // delete params.minLinWidth
      // delete params.bhBoard
      // delete params.xbMin
      // delete params.yhSu
      // delete params.twoJdJ
      // delete params.reservedEdgeSize
      params.sizidtos = this.materialSettingData;
      params.xnum = params.xnum ? Number(params.xnum) : 1;
      params.ynum = params.ynum ? Number(params.ynum) : 1;
      params.verSetDirec = Number(params.verSetDirec);
      params.horSetDirec = Number(params.horSetDirec);
      params.direction = Number(params.direction);
      params.unitLen = params.unitLen ? Number(params.unitLen) : null;
      params.unitWidth = params.unitWidth ? Number(params.unitWidth) : null;
      params.unitLenMax = params.unitLenMax ? Number(params.unitLenMax) : null;
      params.unitLenMin = params.unitLenMin ? Number(params.unitLenMin) : null;
      params.unitWidthMax = params.unitWidthMax ? Number(params.unitWidthMax) : null;
      params.unitWidthMin = params.unitWidthMin ? Number(params.unitWidthMin) : null;
      params.setSpaceX = params.setSpaceX ? Number(params.setSpaceX) : null;
      params.setSpaceY = params.setSpaceY ? Number(params.setSpaceY) : null;
      params.sideTop = params.sideTop ? Number(params.sideTop) : null;
      params.sideBot = params.sideBot ? Number(params.sideBot) : null;
      params.sideLeft = params.sideLeft ? Number(params.sideLeft) : null;
      params.sideRight = params.sideRight ? Number(params.sideRight) : null;
      params.reservedEdgeSize = params.reservedEdgeSize ? Number(params.reservedEdgeSize) : 0;
      params.coreSum = params.coreSum ? Number(params.coreSum) : 0;
      params.ppSum = params.ppSum ? Number(params.ppSum) : 0;
      params.facId = this.$route.query.joinFactoryId;
      params.source = this.$route.query.type;
      // delete params.verSetDirec
      // delete params.jszDirection
      // delete params.horSetDirec
      // delete params.direction
      this.tableLoading = true;
      this.spinning = true;
      automaticSplicing(params)
        .then(res => {
          if (res.code == 1) {
            if (res.data) {
              this.tableData = res.data;
              this.showtag = false;
              if (this.tableData.length == 0) {
                this.imgData = [];
              }
            }
            if (this.tableData.length > 0) {
              this.$refs.cuttingTab.selectRow = this.tableData[0];
              this.$refs.cuttingTab.dbid = this.tableData[0].indexid;
              this.$refs.cuttingTab.tabIndex = this.tableData[0].indexid;
              this.getImgData(this.tableData[0]);
            } else {
              this.spinning = false;
            }
            this.showtag = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.spinning = false;
        });
    },
    getImgData(payload) {
      let params = JSON.parse(JSON.stringify(this.$refs.basicInfo.form));
      this.$refs.basicInfo.form.pcsset = Number(this.$refs.basicInfo.form.pcsset);
      this.$refs.basicInfo.handForm.sheetLen = payload.cbcc ? payload.cbcc.split(".")[0] : "";
      this.$refs.basicInfo.handForm.sheetWidth = payload.cbck ? payload.cbck.split(".")[0] : "";
      this.$refs.basicInfo.handForm.panelLen = payload.cpanelc ? payload.cpanelc.split(".")[0] : "";
      this.$refs.basicInfo.handForm.panelWidth = payload.cpanelk ? payload.cpanelk.split(".")[0] : "";
      // let params = Object.assign(JSON.parse(JSON.stringify(this.$refs.basicInfo.form)),JSON.parse(JSON.stringify(this.$refs.basicInfo.form3)))
      // delete params.job
      delete params.pcssetName;
      delete params.minW;
      delete params.latitudinalSize;
      // delete params.cpSx
      // delete params.coreSum
      // delete params.ppSu
      // delete params.minLinWidth
      // delete params.bhBoard
      // delete params.xbMin
      // delete params.yhSu
      // delete params.twoJdJ
      // delete params.reservedEdgeSize
      params.sizidtos = this.materialSettingData;
      params.xnum = params.xnum ? Number(params.xnum) : 1;
      params.ynum = params.ynum ? Number(params.ynum) : 1;
      params.verSetDirec = Number(params.verSetDirec);
      params.horSetDirec = Number(params.horSetDirec);
      params.direction = Number(params.direction);
      params.unitLen = params.unitLen ? Number(params.unitLen) : null;
      params.unitWidth = params.unitWidth ? Number(params.unitWidth) : null;
      params.unitLenMax = params.unitLenMax ? Number(params.unitLenMax) : null;
      params.unitLenMin = params.unitLenMin ? Number(params.unitLenMin) : null;
      params.unitWidthMax = params.unitWidthMax ? Number(params.unitWidthMax) : null;
      params.unitWidthMin = params.unitWidthMin ? Number(params.unitWidthMin) : null;
      params.setSpaceX = params.setSpaceX ? Number(params.setSpaceX) : null;
      params.setSpaceY = params.setSpaceY ? Number(params.setSpaceY) : null;
      params.sideTop = params.sideTop ? Number(params.sideTop) : null;
      params.sideBot = params.sideBot ? Number(params.sideBot) : null;
      params.sideLeft = params.sideLeft ? Number(params.sideLeft) : null;
      params.sideRight = params.sideRight ? Number(params.sideRight) : null;
      params.reservedEdgeSize = params.reservedEdgeSize ? Number(params.reservedEdgeSize) : 0;
      params.coreSum = params.coreSum ? Number(params.coreSum) : 0;
      params.ppSum = params.ppSum ? Number(params.ppSum) : 0;
      params.facId = this.$route.query.joinFactoryId;
      // delete params.verSetDirec
      // delete params.jszDirection
      // delete params.horSetDirec
      // delete params.direction
      let imgParams = {
        panelMakeInput: params,
        saveOss: false,
        modeSet: payload,
      };
      if (this.TaoBan) {
        this.ppset = Number(payload.uResult.tbDataInfo.smallRecNum);
        // this.$refs.basicInfo.form.pcsset = payload.cpbs
        this.$refs.basicInfo.form.pcsset = this.ppset;
        if (this.$refs.basicInfo.form.pcsset > 1) {
          this.$refs.basicInfo.form.pcssetName = "Set";
        }
        this.$refs.basicInfo.form.cpLen = payload.setl;
        this.$refs.basicInfo.form.cpWidth = payload.setw;
      }
      this.tableLoading = true;
      automaticImgae(imgParams).then(data => {
        if (data.code == 1) {
          this.$message.success("拼板成功");
          this.imgData = data.data;
          this.uResult.uResult = this.imgData.ur;
          // this.imageUrl = data.data.sheetCut
          this.imageUrl = data.data.aPnl;
          this.$refs.rightImg.targetDom = "aPnl";
        } else {
          this.$message.error(data.message);
        }
        this.spinning = false;
        this.tableLoading = false;
      });
    },
    getOld(val, tag) {
      this.tableData = [val];
      this.$refs.cuttingTab.dbid = tag;
      this.showtag = false;
      // console.log('数据',val)
      this.getImgData(val);
    },
    //cuttingtab 取消双击后拼版成功提示
    getImgData2(payload, type) {
      this.$refs.basicInfo.handForm.sheetLen = payload.cbcc ? payload.cbcc.split(".")[0] : "";
      this.$refs.basicInfo.handForm.sheetWidth = payload.cbck ? payload.cbck.split(".")[0] : "";
      this.$refs.basicInfo.handForm.panelLen = payload.cpanelc ? payload.cpanelc.split(".")[0] : "";
      this.$refs.basicInfo.handForm.panelWidth = payload.cpanelk ? payload.cpanelk.split(".")[0] : "";
      this.$refs.basicInfo.form.pcsset = Number(this.$refs.basicInfo.form.pcsset);
      // let params = JSON.parse(JSON.stringify(this.$refs.basicInfo.form)) 传参增加套板信息
      let params = Object.assign(JSON.parse(JSON.stringify(this.$refs.basicInfo.form)), JSON.parse(JSON.stringify(this.$refs.basicInfo.form3)));
      // delete params.job
      delete params.pcssetName;
      delete params.minW;
      delete params.latitudinalSize;
      // delete params.cpSx
      // delete params.coreSum
      // delete params.ppSu
      // delete params.minLinWidth
      // delete params.bhBoard
      // delete params.xbMin
      // delete params.yhSu
      // delete params.twoJdJ
      // delete params.reservedEdgeSize
      params.xnum = params.xnum ? Number(params.xnum) : 1;
      params.ynum = params.ynum ? Number(params.ynum) : 1;
      params.verSetDirec = Number(params.verSetDirec);
      params.horSetDirec = Number(params.horSetDirec);
      params.direction = Number(params.direction);
      params.unitLen = params.unitLen ? Number(params.unitLen) : null;
      params.unitWidth = params.unitWidth ? Number(params.unitWidth) : null;
      params.unitLenMax = params.unitLenMax ? Number(params.unitLenMax) : null;
      params.unitLenMin = params.unitLenMin ? Number(params.unitLenMin) : null;
      params.unitWidthMax = params.unitWidthMax ? Number(params.unitWidthMax) : null;
      params.unitWidthMin = params.unitWidthMin ? Number(params.unitWidthMin) : null;
      params.setSpaceX = params.setSpaceX ? Number(params.setSpaceX) : null;
      params.setSpaceY = params.setSpaceY ? Number(params.setSpaceY) : null;
      params.sideTop = params.sideTop ? Number(params.sideTop) : null;
      params.sideBot = params.sideBot ? Number(params.sideBot) : null;
      params.sideLeft = params.sideLeft ? Number(params.sideLeft) : null;
      params.sideRight = params.sideRight ? Number(params.sideRight) : null;
      params.reservedEdgeSize = params.reservedEdgeSize ? Number(params.reservedEdgeSize) : 0;
      params.coreSum = params.coreSum ? Number(params.coreSum) : 0;
      params.ppSum = params.ppSum ? Number(params.ppSum) : 0;
      params.facId = this.$route.query.joinFactoryId ? Number(this.$route.query.joinFactoryId) : 0;
      //delete params.verSetDirec
      //delete params.jszDirection
      //delete params.horSetDirec
      //delete params.direction
      let imgParams = {
        panelMakeInput: params,
        saveOss: false,
        modeSet: payload,
      };
      if (this.$refs.basicInfo.form.isTaoBan) {
        this.ppset = Number(payload.uResult.tbDataInfo.smallRecNum);
        this.$refs.basicInfo.form.pcsset = this.ppset;
        if (this.$refs.basicInfo.form.pcsset > 1) {
          this.$refs.basicInfo.form.pcssetName = "Set";
        }
        //this.$refs.basicInfo.form.cpLen = payload.cpbs
        this.$refs.basicInfo.form.cpLen = payload.setl;
        this.$refs.basicInfo.form.cpWidth = payload.setw;
      }
      this.tableLoading = true;
      automaticImgae(imgParams).then(data => {
        if (data.code == 1) {
          this.imgData = data.data;
          this.uResult.uResult = this.imgData.ur;
          this.imageUrl = data.data.aPnl;
          this.$refs.rightImg.targetDom = "aPnl";
          if (type == "dbclick" && data.message) {
            this.$message.warning(data.message);
          }
        } else {
          this.$message.error(data.message);
        }
        this.spinning = false;
        this.tableLoading = false;
      });
    },
    updateImgUrl(url, val) {
      this.imageUrl = url;
      if (val == "aPnl") {
        this.mark = true;
      } else {
        this.mark = false;
      }
      // console.log('this.mark',this.mark,val)
    },
    //手动拼版
    handClick() {
      let params = JSON.parse(JSON.stringify(this.$refs.basicInfo.form));
      let handForm = JSON.parse(JSON.stringify(this.$refs.basicInfo.handForm));
      this.spinning = true;
      params["sheetLen"] = handForm.sheetLen;
      params["sheetWidth"] = handForm.sheetWidth;
      params["panelLen"] = handForm.panelLen;
      params["panelWidth"] = handForm.panelWidth;
      params["facId"] = this.$route.query.joinFactoryId;
      this.$refs.rightImg.targetDom = "aPnl";
      if (!handForm.panelLen || !handForm.panelWidth || !handForm.sheetLen || !handForm.sheetWidth) {
        this.$message.warning("请填写完整参数");
        this.spinning = false;
        return;
      }
      console.log("params", params);
      this.imgData.sheetCut = "";
      this.imgData.aPnl = "";
      this.imgData.bPnl = "";
      this.imgData.tbPnl = "";
      this.tableData = [];
      manualCompositionData(params).then(data => {
        if (data.code == 1) {
          this.$message.success("拼板成功");
          this.showtag = true;
          this.tableData = [];
          this.imgData = data.data;
          this.uResult.uResult = this.imgData.ur;
          this.imageUrl = data.data.aPnl;
        } else {
          this.$message.error(data.message);
        }
        this.spinning = false;
      });
    },
    //材料设置弹窗
    materialSettingClick() {
      this.modelVisible = true;
      // let params = {
      //     'OrderNo':this.$route.query.job?this.$route.query.job:'',
      //   }
      //   getMaterialData(params).then(res => {
      //     if (res.code == 1) {
      //       this.materialSettingData = res.data
      //     } else {
      //       this.$message.error('不存在材料数据，请核实');
      //     }
      //   })
    },
    reportHandleCancel(e) {
      this.modelVisible = false;
      this.dataVisible = false;
    },
    handleOk1(e) {
      this.modelVisible = false;
    },
    handleOk2(e) {
      console.log("getComputedStyle.e", e);
      this.$refs.report.getReportPdf();
      this.dataVisible = false;
    },
    //数据保存
    dataSaveClick() {
      this.loading = true;
      let ur;
      let form1 = JSON.parse(JSON.stringify(this.$refs.basicInfo.form));
      let form2 = JSON.parse(JSON.stringify(this.$refs.basicInfo.handForm));
      delete form1.orderName;
      delete form1.pcssetName;
      delete form1.minW;
      delete form1.latitudinalSize;
      delete form1.cpSx;
      delete form1.coreSum;
      delete form1.ppSu;
      delete form1.minLinWidth;
      delete form1.bhBoard;
      delete form1.xbMin;
      delete form1.yhSu;
      delete form1.twoJdJ;
      delete form1.reservedEdgeSize;
      form1.verSetDirec = Number(form1.verSetDirec);
      form1.horSetDirec = Number(form1.horSetDirec);
      form1.direction = Number(form1.direction);
      if (this.$route.query.typemodel) {
        form1.typemodel = 1;
      } else {
        delete form1.typemodel;
      }
      // delete form1.verSetDirec
      // delete form1.jszDirection
      // delete form1.horSetDirec
      // delete form1.direction
      if (this.tableData.length > 0) {
        ur = this.tableData[this.$refs.cuttingTab.tabIndex];
      } else {
        ur = this.uResult;
      }
      if (form2.sheetLen == "") {
        delete form2.sheetLen;
      }
      if (form2.sheetWidth == "") {
        delete form2.sheetWidth;
      }
      if (form2.panelLen == "") {
        delete form2.panelLen;
      }
      if (form2.panelWidth == "") {
        delete form2.panelWidth;
      }

      let params = {
        modeSet: ur,
        panelMakeInput: Object.assign(form1, form2),
        saveOss: false,
        panelResultOutput: this.imgData,
        joinFactoryId: this.$route.query.joinFactoryId,
        tag: this.$refs.cuttingTab.dbid ? this.$refs.cuttingTab.dbid.toString() : null,
      };
      dataSave(params).then(data => {
        if (data.code == 1) {
          this.$message.success("保存成功");
          settensilecoef(this.$route.query.joinFactoryId, this.$route.query.job).then(res => {});
        } else {
          this.$message.error(data.message);
        }
        this.loading = false;
      });
    },
    //导出报表
    openReport() {
      this.dataVisible = true;
      this.titleName = this.$refs.basicInfo.form.job;
    },
    abcut(val) {
      this.ABcutting = val;
    },
    TaoBanChange(val) {
      this.TaoBan = val;
      if (this.TaoBan && JSON.stringify(this.$refs.cuttingTab.selectRow) != "{}") {
        //this.$refs.basicInfo.form.pcsset = this.$refs.cuttingTab.selectRow.cpbs
        this.$refs.basicInfo.form.pcsset = this.ppset;
        if (this.$refs.basicInfo.form.pcsset > 1) {
          this.$refs.basicInfo.form.pcssetName = "Set";
        }
        this.$refs.basicInfo.form.cpLen = this.$refs.cuttingTab.selectRow.setl;
        this.$refs.basicInfo.form.cpWidth = this.$refs.cuttingTab.selectRow.setw;
      }
      if (this.TaoBan && this.tableData.length == 1) {
        this.$refs.basicInfo.form.pcsset = this.ppset;
        if (this.$refs.basicInfo.form.pcsset > 1) {
          this.$refs.basicInfo.form.pcssetName = "Set";
        }
        //this.$refs.basicInfo.form.pcsset = this.tableData[0].cpbs
        this.$refs.basicInfo.form.cpLen = this.tableData[0].setl;
        this.$refs.basicInfo.form.cpWidth = this.tableData[0].setw;
      }
    },
  },
  directives: {
    imgscale: {
      inserted(el, binding, vnode) {
        let isJson = obj => {
          return typeof obj === "object" && Object.prototype.toString.call(obj) == "[object Object]";
        };

        let imgScale = options => {
          let defaults = {
            imgWidth: el.width,
            imgHeight: el.height,
            panelWidth: el?.parentNode?.offsetWidth,
            panelHeight: el?.parentNode?.offsetHeight,
            scale: !binding?.modifiers?.stop,
            resizeType: binding?.modifiers?.contain ? "contain" : "cover",
            unit: "px",
          };
          console.log("defaults", defaults);
          options = isJson(options) ? options : {};
          options = Object.assign({}, defaults, options || {});
          // console.log(options)
          if (options.scale) {
            if (options.imgWidth > 0 && options.imgHeight > 0) {
              el.parentNode.style.width = options.panelWidth + options.unit;
              el.parentNode.style.height = options.panelHeight + options.unit;
              switch (options.resizeType) {
                case "cover":
                  // 默认 cover 方式，处理后的宽或高超过容器的宽高，用margin负值使之上下或左右居中
                  // 如果图片比容器扁
                  if (options.imgWidth / options.imgHeight >= options.panelWidth / options.panelHeight) {
                    console.log("1", options.imgWidth, options.imgHeight, options.panelWidth, options.panelHeight);
                    let toWidth = options.imgWidth * (options.panelHeight / options.imgHeight);
                    console.log("options", options);
                    el.style.width = toWidth * 0.6 + options.unit;
                    el.style.height = options.panelHeight * 0.6 + options.unit; //等比缩放，保持清晰度
                    el.style.marginLeft = -((toWidth - options.panelWidth) / 2) + options.unit;
                  }
                  // 如果图片比容器瘦
                  else {
                    console.log("2", options.imgWidth, options.imgHeight, options.panelWidth, options.panelHeight);
                    console.log("options", options);
                    let toHeight = options.imgHeight * (options.panelWidth / options.imgWidth);
                    el.style.width = options.panelWidth * 0.6 + options.unit;
                    el.style.height = toHeight * 0.6 + options.unit; //等比缩放，保持清晰度
                    // el.style.marginTop = -((toHeight - options.panelHeight) / 2)+options.unit
                    el.style.marginTop = -50 + options.unit;
                    el.style.marginLeft = (options.panelWidth - options.imgWidth) / 2 + options.unit;
                  }
                  break;
                case "contain":
                  // contain方式，处理后的宽或高不足容器的宽高，用margin正值使之上下或左右居中
                  if (options.imgWidth / options.imgHeight >= options.panelWidth / options.panelHeight) {
                    let toHeight = options.imgHeight * (options.panelWidth / options.imgWidth);
                    el.style.width = options.panelWidth + options.unit;
                    el.style.height = toHeight + options.unit;
                    el.style.marginTop = (options.panelHeight - toHeight) / 2 + options.unit;
                  } else {
                    let toWidth = options.imgWidth * (options.panelHeight / options.imgHeight);
                    el.style.width = toWidth + options.unit;
                    el.style.height = options.panelHeight + options.unit;
                    el.style.marginLeft = (options.panelWidth - toWidth) / 2 + options.unit; // 容器已居中
                  }
                  break;
              }
            }
          }
        };
        //已经加载过了
        if (el.complete && el.width > 0) {
          imgScale(binding.value);
          return;
        }
        //没加载过则监听load事件
        el.addEventListener(
          "load",
          () => {
            imgScale(binding.value);
          },
          false
        );
      },
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-layout-content {
  height: 770px;
}
/deep/.ant-table-thead > tr > th {
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

.cutting {
  min-width: 1670px;
}
.cutting .ant-layout-header {
  height: 50px;
  background: #ffffff;
  color: #fff;
  border-bottom: 1px solid rgba(215, 215, 215, 1);
  margin-bottom: 7px;
}
.cutting .ant-layout-sider {
  background: #ffffff;
  color: #fff;
}
.cutting .ant-layout-content {
  margin: 0 8px;
  background: #ffffff;
  .canvasBox {
    .ant-spin-nested-loading {
      height: 100%;
      /deep/ .ant-spin-container {
        height: 100%;
      }
    }
    height: 650px;
    /deep/ .ant-empty {
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-content: center;
      .ant-empty-image,
      .ant-empty-description {
        width: 100%;
      }
    }
  }
}
.img-box2 {
  width: 100%;
  height: 600px !important;
  margin: 0 auto;
  overflow: hidden;
}
/deep/ .ant-modal {
  .ant-table-row-cell-break-word {
    padding: 8px 10px;
  }
}
</style>
