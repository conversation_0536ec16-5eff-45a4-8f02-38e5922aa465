<!-- 市场管理 - 预审分派-查询 -->
<template>
  <!-- <a-modal
      title="订单查询"
      :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
      destroyOnClose
      class="searchModel"
      :width='400'      
  > -->
  <a-form >   
    <a-row>
     <a-col :span='24'>
        <a-form-item label="订单号"  :labelCol="{span: 6}" :wrapperCol="{span:16}">
        <a-input
        :autoFocus="autoFocus"
          ref="select1"
          v-model="form.OrderNo"
          v-focus-next-on-enter="'select2'" 
          allowClear
        >         
        </a-input>
         </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span='24'>
        <a-form-item label="客户代码"  :labelCol="{span: 6}" :wrapperCol="{span:16}">
          <a-input
              ref="select2"
              v-model="form.custNo"
              v-focus-next-on-enter="'select3'"
              allowClear
            >              
            </a-input>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span='24'>
        <a-form-item label="客户型号"  :labelCol="{span: 6}" :wrapperCol="{span:16}">
          <a-input
              ref="select3"
              v-model="form.PcbFileName"
              allowClear
            >              
            </a-input>
        </a-form-item>
      </a-col>
    </a-row>
    
  </a-form>
  <!-- </a-modal> -->
</template>

<script>
export default {
  name: "SearchModal",
  computed:{   
  },
  data() {
    return {
      autoFocus:true,
      // disabled: false,
      form:{
        OrderNo:'',  // 订单编号
        custNo: '',  // 订单包号
        PcbFileName: '',    // 客户型号
      }
    }
  },
  methods: {
   
   
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
  mounted() {
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
.searchModel{
  form {
    overflow: hidden;
  }
}
</style>