<!-- 工具管理 -飞针制作  旧-->
<template>
  <a-card style=" min-width: 1670px;">
    <div style="display: flex;margin-bottom: 10px">
      <div style="position: relative">
        <a-button type="primary"> 上传</a-button>
        <a-upload
            name="file"
            :multiple="false"
            :customRequest="customRequest"
            @change="handleChangeImg"
            :showUploadList="false"
            accept="application/x-compressed"
            style="position: absolute;left: 0"
        >
          <a-button style="width: 64px;"><a-icon type="upload" /></a-button>
        </a-upload>
      </div>

      <a-button type="primary"  @click="toDetail(2)" style="margin-left: 10px"> 下载</a-button>
      <a-button type="primary"  @click="toDetail(1)" style="margin-left: 10px"> 查询</a-button>
      <a-button type="primary"  @click="toDetail(3)" style="margin-left: 10px"> 设置</a-button>
      <a-button type="primary"  @click="toDetail(4)" style="margin-left: 10px"> 取消</a-button>

    </div>

    <a-table
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
        :loading='loading'
        :scroll="{ y: 600,x: 900}"
        :customRow="Rowclick"
        :row-selection="{ selectedRowKeys: selectedRowKeys_cnt, onChange: onSelectChange_content ,type: 'radio'}"

    >
      <!--<span slot="action" slot-scope="record">-->
      <!--<a href="javascript:;" @click="toDetail(record,'1')" >上传</a>-->
      <!--<a-divider type="vertical" />-->
      <!--<a href="javascript:;" @click="toDetail(record,'2')">下载</a>-->
      <!--<a-divider type="vertical" />-->
      <!--<a href="javascript:;" @click="btnSte(record,'3')" >查询</a>-->
      <!--<a-divider type="vertical" />-->
      <!--<a href="javascript:;" @click="btnSte(record,'3')" >设置</a>-->
      <!--<a-divider type="vertical" />-->
      <!--<a href="javascript:;" @click="btnSte(record,'3')" >取消</a>-->
      <!--<a-divider type="vertical" />-->
      <!--</span>-->
    </a-table>
    <a-modal
        title="提示"
        :visible="visible"
        :confirm-loading="confirmLoading"
        @ok="handleOk"
        @cancel="handleCancel"
    >
      <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="文件名称">
          <a-input v-model="form.id" />
        </a-form-model-item>
        <a-form-model-item label="制作状态">
          <a-select v-model="form.stat" placeholder="please select your zone">
            <a-select-option value="制作失败">
              制作失败
            </a-select-option>
            <a-select-option value="制作完成">
              制作完成
            </a-select-option>
            <a-select-option value="待制作">
              待制作
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="下载状态">
          <a-select v-model="form.uploadStat" placeholder="please select your zone">
            <a-select-option value="已下载">
              已下载
            </a-select-option>
            <a-select-option value="未下载">
              未下载
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <a-modal
        title="提示"
        :visible="visible1"
        :confirm-loading="confirmLoading"
        @ok="handleOk1"
        @cancel="handleCancel1"
    >
      <a-form-model :model="form1" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="大板STEP命名">
          <a-input v-model="form1.stepName" />
        </a-form-model-item>
        <a-form-model-item label="gko命名">
          <a-input v-model="form1.gkoName" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import {flyingList,flyingUpload,flyingAdd,downLoad,flyingCancel,flyingSet,getFlyingSet} from '@/services/gongju/instrument'
let columns=[
  {
    title: "文件名称",
    dataIndex: "id",
  },
  {
    title: "制作状态",
    dataIndex: "makeStatus",
  },
  {
    title: "创建时间",
    dataIndex: "createDate",
  },
  {
    title: "完成时间",
    dataIndex: "endDate",
  },
  {
    title: "下载状态",
    dataIndex: "downloadStatus",
  },
  {
    title: "上传用户",
    dataIndex: "userLoginId",
  },
  // {
  //     title: '操作',
  //     key: 'action',
  //     scopedSlots: { customRender: 'action' },
  // }
]
export default {
  name: "",
  data() {
    return {
      columns:columns,
      dataSource:[],
      pagination: {
        pageSize: 10,
        current: 1,
        total:0,
        showTotal: (total) => `总计 ${total} 条`,
      },
      loading:false,
      visible:false,
      confirmLoading: false,
      visible1:false,
      labelCol: { span: 6},
      wrapperCol: { span: 14 },
      form: {
        id:'',
        stat: '',
        uploadStat: '',
      },
      form1:{
        stepName:'',
        gkoName:''
      },
      imgName:'',
      ID:"",
      trIndex:null,
      selectedRowKeys_cnt:[],
    }
  },
  component: {},
  methods: {

    onSelectChange_content(selectedRowKeys, selectedRows) {
      this.selectedRowKeys_cnt = selectedRowKeys;
      this.ID=selectedRows[0].id
    },
    handleTableChange(pagination){
      this.pagination.current=pagination.current
      this.pagination.pageSize=pagination.pageSize
      this.getList()
    },
    toDetail(index){
      debugger
      if(index=='1'){
        this.visible=true
      }
      if(index=='2'){
        downLoad(this.ID).then(res=>{
          console.log(res)
          if(res.code=='1'){
            var link = document.createElement("a");
            link.setAttribute("download", this.ID);
            link.href = res.data;
            link.click();
            link.remove();
          }else {
            this.$message.info(res.message)
          }

        })
      }
      if(index=='3'){
        this.visible1=true
        getFlyingSet().then(res=>{
          this.form1.stepName=res.data.stepName
          this.form1.gkoName=res.data.gkoName
        })
      }
      if(index=='4'){
        flyingCancel(this.ID).then(res=>{
          this.$message.info(res.message)
          this.getList()
        })
      }
    },
    getList(){
      let parmas={
        Id:this.form.id,
        MakeStatus:this.form.stat,
        DownloadStatus:this.form.uploadStat,
        PageIndex:this.pagination.current,
        PageSize:this.pagination.pageSize
      }
      flyingList(parmas).then(res=>{
        console.log(res)
        this.dataSource=res.items
        this.pagination.total=res.totalCount
      })
    },
    //文件上传
    handleChangeImg(info) {
      this.imgName = `${info.file.name}`.substring(0,`${info.file.name}`.indexOf('.'));
      // this.$forceUpdate();
    },
    customRequest(data) {
      console.log(data)
      const formData = new FormData();
      formData.append("file", data.file);
      var reg = new RegExp("[\\u4E00-\\u9FFF]+","g");
      if(reg.test( data.file.name)   ){
        this.$message.info('文件名称不能有中文')
      }else {
        flyingUpload(formData).then((res) => {
          let parmas={
            id:this.imgName,
            upFileData_:new Date(),
            feiZhengTgzPath_:res,
            userLoginId:JSON.parse(localStorage.getItem('admin.user')).userName
          }
          flyingAdd(parmas).then(res=>{
            this.$message.info(res.message)
            this.getList()
          })
        });
      }

    },
    //查询
    handleOk(){
      this.visible=false
      this.getList()
      this.form={
        id:'',
        stat: '',
        uploadStat: '',
      }
    },
    handleCancel(){
      this.visible=false
      this.form={
        id:'',
        stat: '',
        uploadStat: '',
      }
    },
    Rowclick(record, index) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeys_cnt = keys;
            if(this.trIndex==null){
              this.trIndex=index
              document.getElementsByClassName('ant-table-row')[index].style.background='#FFF9E6'
            }else if(this.trIndex != index){
              document.getElementsByClassName('ant-table-row')[this.trIndex].style.background=''
              document.getElementsByClassName('ant-table-row')[index].style.background='#FFF9E6'
              this.trIndex=index
            }
            this.ID=record.id
          },
        },
      };
    },
    //设置
    handleOk1(){
      this.visible1=false
      flyingSet(this.form1).then(res=>{
        this.$message.info(res.message)
        this.getList()
        this.form1={
          stepName:'',
          gkoName:''
        }
      })
    },
    handleCancel1(){
      this.visible1=false
      this.form1={
        stepName:'',
        gkoName:''
      }
    },
  },
  created() {
    this.getList()
  }
}
</script>

<style scoped lang="less">
/deep/ .ant-upload.ant-upload-select{
  opacity: 0;
}
/deep/.ant-table-body{
  &::-webkit-scrollbar {//整体样式
    width: 0;//y轴滚动条粗细
    height: 0;
  }
  &::-webkit-scrollbar-thumb {//滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    // background: #00aaff;
    background: #eff1f7;
  }
  &::-webkit-scrollbar-track {//轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #f6f8ff;
  }
}
/deep/.ant-table-row-selected td{
  background: #FFFAF2!important;
  color: #FF9900!important;
}
</style>
