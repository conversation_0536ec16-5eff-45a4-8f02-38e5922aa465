<!-- 工程模块 绩效管理 -->
<template>
    <a-modal
      title="绩效管理"
      :visible="visiblePerformance"
      @cancel="reportHandleCancel"
      :destroyOnClose='true'
      :maskClosable="false"
      :width="1250"
      centered
      :confirmLoading='confirmLoading'
   >
   <template slot="footer">
    <a-button type="primary" v-if="!editFlg" @click="editClick">编辑</a-button>
    <a-button type="primary" v-if="editFlg" @click="cancelClick">取消</a-button>
    <a-button type="primary" @click="saveClick">保存</a-button>
    <a-button type="primary" v-if="type==-1" @click="deleteClick">删除</a-button>
    <a-button type="primary" v-if="type==-1" @click="examineClick">审批</a-button>
    <a-button type="primary" @click="reportHandleCancel">退出</a-button>
   </template>
    <div class="box">
        <div class="left" ref="tableWrapper">
            <a-table
            :columns="columns"
            :dataSource="coefficientsList"            
            :customRow="onClickRow"
            :pagination="false"
            :rowKey="'id'"
            :loading="TableLoading1"
            :rowClassName="isRedRow"
          
            >
            </a-table>
        </div>
        <div class="right">
            <a-table
            :columns="columns1"
            :dataSource="coefficientInfos"
            :scroll="{y: 400 }"
            :customRow="onClickRow1"
            :pagination="false"
            :rowKey="(record,index)=>{return index}"
            :loading="TableLoading2"
            :rowClassName="isRedRow1"
            :class="coefficientInfos.length ? 'min-table':''"
            >
            <template slot="score" slot-scope="text, record">                
                <a-input v-if="!record.isCalc && editFlg" v-model="record.score" :autoFocus="true"></a-input>
                <span v-else>{{record.score}}</span>
            </template>
            <template slot="remark" slot-scope="text, record,index"> 
            <a-select ref="select" v-if="record.description == '补偿系数' && editFlg && fac=='普林'"
            v-model="record.remark"  showSearch allowClear  
            @change="setEstimate($event, xslist,index)"
            @search="handleSearch($event,xslist,index)"
            @blur="handleBlur($event, xslist,index)" 
            optionFilterProp="lable" >
              <a-select-option v-for="(ite,ind) in xslist" :title="ite.text" :key="ind" :value="ite.text" :lable="ite.text">{{ite.text}}</a-select-option>
            </a-select> 
                <a-input v-else-if="!record.isCalc && editFlg " v-model="record.remark"></a-input>
                <span v-else>{{record.remark}}</span>
            </template>
            </a-table>
        </div>
        <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
        <a-menu-item @click="down1" v-if="showText">复制</a-menu-item>
        </a-menu>
    </div>
    </a-modal>
</template>
<script>
import {coefficient,setBCCoefficient,checkCoefficient,bccoefficientdel}from "@/services/projectMake";
const columns = [
    { title: '序号', 
    dataIndex: 'index', 
    key: 'index', 
    width:45, 
    ellipsis: true,
    align:'center', 
    customRender:(text,record,index)=> `${index+1}` 
    },
    { title: '生产编号', 
    dataIndex: 'orderNo', 
    key: 'orderNo', 
    width:120, 
    align:'left', 
    ellipsis: true, 
    },
    { title: '系数值', 
    dataIndex: 'score', 
    key: 'score', 
    width:60, 
    align:'left',
    ellipsis: true,  
    },
    { title: '系数人员', 
    dataIndex: 'acountName', 
    key: 'acountName', 
    width:70, 
    align:'left',
    ellipsis: true, 
    },
    { title: '有效时间', 
    dataIndex: 'createTime', 
    key: 'createTime', 
    width:135,
    align:'left', 
    ellipsis: true, 
    },
    { title: '描述', 
    dataIndex: 'description', 
    key: 'description', 
    align:'left',
    // width:45, 
    ellipsis: true, 
    },
]
const columns1 = [
    { title: '序号', 
    dataIndex: 'index', 
    key: 'index', 
    width:45, 
    ellipsis: true,
    align:'center', 
    customRender:(text,record,index)=> `${index+1}` 
    },
    { title: '系数项目', 
    dataIndex: 'description', 
    key: 'description', 
    width:80, 
    align:'left',   
    ellipsis: true, 
    },
    { title: '系数', 
    dataIndex: 'score', 
    key: 'score', 
    width:60, 
    align:'left',
    className:'editSTY',
    scopedSlots: { customRender: 'score' },
    ellipsis: true,  
    },
    { title: '状态', 
    dataIndex: 'status', 
    key: 'status', 
    width:55, 
    align:'left',
    customRender: (text,record,index) => `${record.status ? '已审核' : '未审核'}`,
    ellipsis: true, 
    },
    { title: '备注', 
    dataIndex: 'remark', 
    key: 'remark', 
    className:'editSTY',
    scopedSlots: { customRender: 'remark' },
    // width:135,
    align:'left', 
    ellipsis: true, 
    },
    
]
export default {            
    name: '',
    props:{
        selectedRowsData:{ type:Object},
        type:{type:Number},
        ids:{type:Number},
    },
    data() {
        return {
            xslist:[
            {value:1,text:'Z031/Z025满板3/3MIL走线订单按照对应层数基数金额增加壹倍'},
            {value:2,text:'重新核实阻抗加0.1分'},
            {value:3,text:'核实阻抗之后需要更改加0.6分'},
            ],
            confirmLoading:false,
            fac:"",
            columns,
            id:this.selectedRowsData.proOrderId,
            proOrderId:'',
            coefficientsList:[],
            columns1,
            proOrderId1:'',
            coefficientInfos:[],
            menuVisible:false,
            menuStyle: {
                position: "absolute",
                top: "0",
                left: "0",
                zIndex:99
            },
            TableLoading1:false,
            TableLoading2:false,
            showText:false,
            visiblePerformance:false, 
            editFlg:false,
            editRow:[],
            isCtrlPressed:false
        }
    },
    mounted (){      
    window.addEventListener('keydown',this.keydown, true)
    window.addEventListener('keyup',this.keyup, true) 
    },
    beforeDestroy(){
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
   },
    methods:{
    setEstimate(value,list,index) {
    this.coefficientInfos[index].remark = value
    },
    handleSearch(value, list,index) {
        this.setEstimate(value,list,index)
    },
    handleBlur(value,  list,index) {
        this.setEstimate(value,list,index)
    }, 
    keydown(e){
    if (e.key === 'Control') {
    this.isCtrlPressed = true;
    }
    },
    keyup(e){
    if (e.key === 'Control') {
     this.isCtrlPressed = false;
    }  
    }, 
    openModal(model){
        this.fac = model.orderChannel
        this.visiblePerformance = true       
        this.id = model.proOrderId
        this.getData()
    },
    getData(){
        this.TableLoading1 = true
        coefficient(this.id,this.type).then(res=>{
            if(res.code){
                this.coefficientsList = res.data
            }else{
                this.$message.error(res.message)
            }
        }).finally(()=>{
            this.TableLoading1 = false
        })
    },
    reportHandleCancel(){
        this.visiblePerformance = false 
        this.proOrderId='';
        this.proOrderId1='';
        this.coefficientsList=[]
        this.coefficientInfos=[]
        this.editFlg = false;
    },
    editClick(){ 
        if(!this.proOrderId){
            this.$message.warning('请选择订单')
            return
        } 
        this.editRow=[]
        var data={
            'description':'补偿系数',
            'status':0,
            'score':null,
            'remark':'',
            'isCalc':false,  
            'pId':this.proOrderId, 
            'id':'-1'         
        }
        this.coefficientInfos.forEach(item=>{
            if(item.description == '补偿系数'){
                this.editRow.push(item)
            }
        })
        if(!this.editRow.length){
            this.coefficientInfos.push(data) 
        }  
        if(this.editRow.length){
            this.proOrderId1 = this.editRow[0].id
        }else{
            this.proOrderId1 = this.coefficientInfos[this.coefficientInfos.length-1].id
        } 
        this.editFlg = true
    },
    cancelClick(){
        if(this.editFlg){  
            this.editRow=[]          
            this.editFlg = false            
            coefficient(this.id,this.type).then(res=>{
                if(res.code){
                    this.coefficientsList = res.data
                    this.coefficientInfos = this.coefficientsList.filter(item=> {return item.id ==this.proOrderId})[0].coefficientInfos
                }
            })
                      
        }          
    },
    deleteClick(){
        if(this.editFlg){
            this.$message.warning('编辑状态不能删除')
            return
        }  
        if(!this.proOrderId1){
            this.$message.warning('请选择要删除的数据')
            return
        } 
        bccoefficientdel(this.proOrderId1).then(res=>{
            if(res.code){
                this.$message.success('删除成功')
                coefficient(this.id,this.type).then(res=>{
                if(res.code){
                    this.coefficientsList = res.data
                    this.coefficientInfos = this.coefficientsList.filter(item=> {return item.id ==this.proOrderId})[0].coefficientInfos
                }
                 })
                 this.$emit('getProducerList')
                if(this.ids){
                    this.$emit('getProducerInfo',this.ids)
                } 
            }else{
                this.$message.error(res.message)
            }
        })
        
    },
    saveClick(){
        if(!this.editFlg){
            this.$message.warning('非编辑状态不能保存')
            return
        }        
        let params = {}
        if(this.editRow.length){
            params = this.editRow[0]
        }else{
            params = this.coefficientInfos[this.coefficientInfos.length-1]
        }
        if(!params.score){
            this.$message.warning('请填写补偿系数')
            return
        }
        let type = null;
        if(this.type == -1){
            type = this.selectedRowsData1.type
        }else{
            type = this.type
        }
        setBCCoefficient(type,params).then(res=>{
            if(res.code){
                this.$message.success('保存成功')     
                coefficient(this.id,this.type).then(res=>{
                if(res.code){
                    this.coefficientsList = res.data
                    this.coefficientInfos = this.coefficientsList.filter(item=> {return item.id ==this.proOrderId})[0].coefficientInfos
                }
                })
            }else{
                this.$message.error(res.message)                
                this.cancelClick()
            }
        }).finally(()=>{
            this.editFlg = false
            this.proOrderId1 = ''
        })     
    },
    examineClick(){
        if(!this.proOrderId1){
            this.$message.warning('请选择未审批系数项目')
            return
        } 
        checkCoefficient(this.proOrderId1).then(res=>{
            if(res.code){
                this.$message.success('审核成功')
                this.reportHandleCancel()
                this.$emit('getProducerList')
                if(this.ids){
                this.$emit('getProducerInfo',this.ids)
                }                
            }else{
                this.$message.error(res.message)
            }
        })
    },
    onClickRow(record) {
        return {
        on: {
            click: () => {
                this.TableLoading2 = true
                let keys = [];
                keys.push(record.id);
                this.selectedRowKeysArray = keys;
                this.selectedRowsData1 = record            
                this.proOrderId = record.id
                this.proOrderId1 = ''
                this.coefficientInfos = record.coefficientInfos
                this.TableLoading2 = false
            },
            contextmenu: e => {          
            let text = ''
                if(e.target.innerText){           
                text = e.target.innerText
                } 
                e.preventDefault();
                this.menuData = record;
                this.rightClick1(e, text, record)            
            },
        }
        }
    },
    onClickRow1(record) {
        return {
        on: {
            click: () => {
                let keys = [];
                keys.push(record.id);
                this.selectedRowKeysArray1 = keys;
                this.selectedRowsData2 = record
                this.proOrderId1 = record.id
            },
            contextmenu: e => {          
            let text = ''
                if(e.target.innerText){           
                text = e.target.innerText
                } 
                e.preventDefault();
                this.menuData = record;
                this.rightClick1(e, text, record)            
            },
        }
        }
    },
    isRedRow(record){
        let strGroup = []
        let str =[]
        if (record.id && record.id == this.proOrderId) {
            strGroup.push('rowBackgroundColor')
        }
        return str.concat(strGroup)
    },
    isRedRow1(record){
        let strGroup = []
        let str =[]
        if (record.id && record.id == this.proOrderId1) {
            strGroup.push('rowBackgroundColor')
        }
        return str.concat(strGroup)
    },
    rightClick1(e,text,record){
    let event = e.target 
    if(e.target.localName != 'td'){
        event = e.target.parentNode
    }
    if(e.target.localName == 'path'){
        event = e.target.parentNode.parentNode
    }     
    this.text=event.innerText;
    if(event.className.indexOf('noCopy') != -1 || !this.text){
        this.showText = false
    }else{    
        this.showText = true
    } 
    if(event.cellIndex == 1  || event.cellIndex == undefined){
        this.text = this.text.split(" ")[0]
      }
    const tableWrapper = this.$refs.tableWrapper;    
    const cellRect = event.getBoundingClientRect();
    const wrapperRect = tableWrapper.getBoundingClientRect();
    this.menuVisible = true;   
    let  offsetx= event.offsetLeft  + event.offsetWidth - 10
    let offsety = event.offsetTop + 40;  
    if(event.cellIndex == this.columns.length -1){
        this.menuStyle.top =  cellRect.top - wrapperRect.top + 85 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 100 + "px";
    }else{
        this.menuStyle.top = cellRect.top - wrapperRect.top + 85 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth + 20 + "px";
    } 
    // if(event.cellIndex == 0 || event.cellIndex == 1){
    //     this.menuStyle.top = offsety+ "px";
    //     this.menuStyle.left = offsetx  + "px";
    // } 
    document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },    
    down1(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
    },
    },
}                                    
</script>
<style lang="less" scoped>
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  font-size: 8px;
}
/deep/.ant-select{
    width: 100%;
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 3px;
  border-right:1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
padding: 7px 3px;
border-right:1px solid #efefef;
}   
/deep/.editSTY{
    padding:0px 4px !important;
    input{
        height:28px;
    }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: #dfdcdc;
    } 

.box{
    display: flex;
    height:400px;
    .left{
        width:45%;
        border:1px solid #eee;
    }
    .right{
        width:55%;
        border:1px solid #eee;
    }
}
.tabRightClikBox{
  li{
    height:24px;
    line-height:24px;
    margin-top: 0;
    margin-bottom: 0;
    background-color: white !important;
    color:#000000
  }

}
/deep/ .ant-table {  
    .min-table {
      .ant-table-body{
        min-height:200px;
      }
    }
  .fontRed {
    td {
      color: #DC143C;
    }
  }
  .ant-table-fixed-left{
    .cookieIdColor{
    td:first-child{
      background: #ff9900!important;
    }
  }
  
  }
  .rowBackgroundColor {
    background: #DFDCDC!important;
  }
  .displayFlag{
    display: none;
  }
}
.peopleTag{
  margin:0;
  padding:0;
  width:24px;
  border-radius: 12px;
  background: #2D221D;
  border-color: #2D221D;
  color:#FF9900;
  text-align: center;
  margin-left:2px;
}
/deep/.ant-table-row{
  height: 29px !important;
}
</style>