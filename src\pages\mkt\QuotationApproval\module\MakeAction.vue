<!-- 市场管理 - 报价审批- 按钮 -->
<template>
  <div class="active" ref="active">
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OfferRatify.OfferRatifySearch')"
      :class="checkPermission('MES.MarketModule.OfferRatify.OfferRatifySearch') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OfferRatify.OfferRatifyCheckStart')"
      :class="checkPermission('MES.MarketModule.OfferRatify.OfferRatifyCheckStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="approval"> 审批 </a-button>
    </div>
    <!-- <div class="box showClass">
      <a-button type="primary" @click="$emit('costanalysis')"> 成本分析 </a-button>
    </div> -->
    <div class="box showClass">
      <a-button type="primary" @click="$emit('costAnalysis1')"> 成本分析 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OfferRatify.OfferRatifyCheckBack')"
      :class="checkPermission('MES.MarketModule.OfferRatify.OfferRatifyCheckBack') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="returnClick"> 退回 </a-button>
    </div>
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "MakeAction",
  props: {},
  data() {
    return {
      advanced: true,
      width: 1679,
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 10;
      if (this.$refs.active.children.length > 10) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 11) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },
  methods: {
    checkPermission,
    queryClick() {
      this.$emit("queryClick");
    },
    approval() {
      this.$emit("approval");
    },
    returnClick() {
      this.$emit("returnClick");
    },
    costAnalysis() {
      this.$emit("costAnalysis");
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        let buttonsToShow = 10;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-btn {
  padding: 0 10px !important;
}
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  width: 1680px;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;

  .box {
    width: 90px;
    margin-top: 6px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
