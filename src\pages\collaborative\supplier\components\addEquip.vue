<template>
  <div ref="sb">
    <a-modal
    :title="model.id? '编辑' : '新增'"
    :width="640"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    centered
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="设备类型" ref="equipmentName_" prop="equipmentName_">
           <a-select  v-model="form.equipmentName_" :getPopupContainer="()=>this.$refs.sb" >
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="数控钻机">
                    数控钻机
                </a-select-option>
                <a-select-option value="沉铜线">
                    沉铜线
                </a-select-option>
                <a-select-option value="电镀镀铜线">
                    电镀镀铜线
                </a-select-option>
                <a-select-option value="DES线">
                    DES线
                </a-select-option>
                <a-select-option value="飞针测试机">
                    飞针测试机
                </a-select-option>
                <a-select-option value="CNC锣机">
                    CNC锣机
                </a-select-option>
                <a-select-option value="曝光机">
                    曝光机
                </a-select-option>
                <a-select-option value="磨板机">
                    磨板机
                </a-select-option>
                <a-select-option value="磨边机">
                    磨边机
                </a-select-option>
                <a-select-option value="清洗机">
                    清洗机
                </a-select-option>
                <a-select-option value="V割机">
                    V割机
                </a-select-option>
                <a-select-option value="激光切割机">
                    激光切割机
                </a-select-option>
                <a-select-option value="晒网机">
                    晒网机
                </a-select-option>
                <a-select-option value="文字喷墨机">
                    文字喷墨机
                </a-select-option>
                <a-select-option value="LDI曝光机">
                    LDI曝光机
                </a-select-option>
                <a-select-option value="LED曝光机">
                    LED曝光机
                </a-select-option>
                <a-select-option value="镭射钻孔机">
                    镭射钻孔机
                </a-select-option>
                <a-select-option value="AOI修板机">
                    AOI修板机
                </a-select-option>
                <a-select-option value="压机">
                    压机
                </a-select-option>
                <a-select-option value="开料机">
                    开料机
                </a-select-option>
                <a-select-option value="VCP线">
                    VCP线
                </a-select-option>
                <a-select-option value="蚀刻线">
                    蚀刻线
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="规格" ref="specification_" prop="specification_">
          <a-input style="font-weight: 500;"
            v-model="form.specification_"
            placeholder="规格"
          />
        </a-form-model-item>
        <a-form-model-item  label="	数量" ref="number_" prop="number_">
          <a-input style="font-weight: 500;"
            v-model="form.number_" 
            placeholder="数量"
          />
        </a-form-model-item>
        <a-form-model-item  label="制造厂家" ref="manufacturer_" prop="manufacturer_">
          <a-select  v-model="form.manufacturer_" @change="change" :getPopupContainer="()=>this.$refs.sb">
              <a-select-option :value="tmp.company" v-for="(tmp,index) in selectList" :key="index" aria-placeholder="请选择"  >
                  {{tmp.company}}
              </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item  label="产能（月/平方米）" ref="outputRate_" prop="outputRate_">
          <a-input style="font-weight: 500;"
            v-model="form.outputRate_"
            placeholder="产能（月/平方米)"
          />
        </a-form-model-item>
        <a-form-model-item  label="上传图片" v-if="form.id" ref="imgs" prop="imgs">
            <div class="clearfix">
              <a-upload
                accept=".jpg,.png,.gif,.bmp,.jpeg,"  
                name="file"
                ref="filename"
                :customRequest="httpRequest1"
                list-type="picture-card"
                :multiple="true"
                :file-list="fileList"
                :before-upload="beforeUpload"
                @preview="handlePreview"
                @change="handleChange"
              >
                <div>
                  <a-icon type="plus" />
                  <div class="ant-upload-text">
                    Upload
                  </div>
                </div>
              </a-upload>
            </div>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
  </div>
  
</template>

<script>
import { upLoadFlyingFile,} from '@/services/projectMake';
import { addEquip, uploadImg, getPhoto, getSelectFact,progressNum } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
      compileApply:{
          type: String,
          default () {
              return ''
          }
      },
      message:{
          type: String,
          default () {
              return ''
          }
      }
  },
  data() {
    return {
      baseURL: process.env.VUE_APP_API_BASE_URL,
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {
        equipmentName_: '',
        specification_: '',
        number_: '',
        manufacturer_: '',
        outputRate_: ''
      },
      rules: {
        equipmentName_: [
          { required: true, message: "名称必须填写", trigger: "blur" },
        ],
        manufacturer_: [
          { required: true, message: "请选择", trigger: "blur" },
        ],
      },
      fileList: [],
      uploading: false,
      model: '',
      selectList: [],
      arrData:[],
      isFileType:true,
      path:'',
    };
  },
  methods: {
    async httpRequest1(data,type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message)
        }
      })

    },
    getSelectFactList() {
      getSelectFact({company:''})
        .then((res) => {
              if(res.success) {
                let info = {
                  company: '请选择',
                  id: ''
                }
                this.selectList = res.data
              //  this.selectList.push(info)
              }
            })
    },
    openModal(model) {
        // if(this.compileApply=='1'){
            this.visible = true;
            this.model = model
            this.getSelectFactList()

            this.fileList = []
            if(model && model.id) {
              console.log('编辑',model.id)
                this.form = {
                    id:model.id,
                    equipmentName_: model.equipmentName_,
                    specification_: model.specification_,
                    number_: model.number_,
                    manufacturer_: model.manufacturer_,
                    outputRate_: model.outputRate_
                };
                this.getPhotos(model.id)
            }else {
              console.log('新增',model.id)
                this.form={
                    equipmentName_: '',
                    specification_: '',
                    number_: '',
                    manufacturer_: '',
                    outputRate_: ''
                }
            }
            console.log('this.form',this.form)
        // }else {
        //     this.$message.info(this.message)
        // }

    },
    beforeUpload(file) {
      this.isFileType = file.name.toLowerCase().indexOf('.jpg') != -1 || file.name.toLowerCase().indexOf('.png') != -1
          if (!this.isFileType) {
            this.$message.error('图片上传只支持.jpg/.png图片格式文件');
          }
          return this.isFileType
    },
    //查看图片
    getPhotos(id) {
        getPhoto(id)
            .then((res) => {
                res.forEach(e=>{
                  let info = {
                    uid: e.id,
                    name: 'image.png',
                    status: 'done',
                    url: e.strUrl_
                  }
                 this.fileList.push(info)
                })

            })
    },
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
        this.$nextTick(() => {
        this.$viewerApi({
          images: [file.url || file.preview],
        });
      });
    },
    handleChange({ fileList }) {
      if (this.isFileType) { 
        this.fileList = fileList;      
        for (let index = 0; index < this.fileList.length; index++) {
          const element = this.fileList[index].response;
          if (element && !this.arrData.includes(element)) {
            this.arrData.push(element);
            this.path = this.arrData.toString(',');
          }
        }
        this.fileList.forEach(ite=>{
          if(ite.url){
          this.arrData = [...new Set(this.arrData.concat(ite.url))];
          this.path = this.arrData.toString(',');
          }
         })
        if (this.fileList.length === 0) {
          this.path = '';
        }
      }
    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
      this.$refs.ruleForm.resetFields()
        this.form={
            equipmentName_: '',
            specification_: '',
            number_: '',
            manufacturer_: '',
            outputRate_: ''
        }
    },
    change(){
      console.log(this.form)
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          if(this.model.id) {
            let str = ''
              this.fileList.forEach(e=> {
                if(e.response) {
                  str += e.response + ','
                }else if(e.url) {
                  str += e.url + ','
                }

              })
              str = str.substring(0,str.length-1)
              let fileData = {
                pGuid_: this.model.id,
                strUrl_: this.path,
              }
              uploadImg(fileData).then((res) => {
                    console.log(res)
                })
          }
          var checkedData=[]
          checkedData = this.selectList.filter(item=> {return item.company == this.form.manufacturer_})
          this.form.manufacturer_  = checkedData[0].company
          let params = {
              id:this.model.id,
              pGuid_: this.suppId,
              ...this.form
          }

          addEquip(params)
            .then((res) => {
                this.visible = false;
                form.resetFields();
                this.$message.success("操作成功");
                this.fileList = []
                this.arrData=[]
                this.$emit("ok");
                progressNum(this.suppId).then(res=>{
                    if(res.code!==1){
                        //this.$message.info(res.message)
                    }
                })
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-form-item{
  margin-bottom:5px !important;
}
/deep/.ant-select-dropdown-menu-item{
   font-weight:500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
</style>
