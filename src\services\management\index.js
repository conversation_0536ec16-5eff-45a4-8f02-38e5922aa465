import { request, METHOD } from "@/utils/request";
// 待上机
export async function getWaitOrderList(params) {
  return request("/api/app/e-mSTProc-production-process/cnc-wait-distribute", METHOD.GET, params);
}
// 已上机
export async function getDoingOrderList(params) {
  return request("/api/app/e-mSTProc-production-process/cnc-finish-distribute", METHOD.GET, params);
}
// 当班统计
export async function getStatisticsList(craftKey) {
  return request(`/api/app/e-mSTProc-production-process/ondutystatistics?type=${craftKey}`, METHOD.GET);
}
// 获取机台列表
export async function getMachineList(params) {
  return request("/api/app/e-mSTProc-production-process/cnc-machine-list", METHOD.GET, params);
}
// 获取机台详细信息
export async function byid(id) {
  return request(`/api/app/e-mSTPUBEquipment/${id}/by-id`, METHOD.GET);
}
//保存机台信息
export async function updatemachineinfo(params) {
  return request(`/api/app/e-mSTProc-production-process/update-machine-info`, METHOD.POST, params);
}
// 钻孔表
export async function getDrillHoleList(id) {
  return request(`/api/app/e-mSTProc-production-process/${id}/get-drill-t0output`, METHOD.GET);
}
// 分派列表
export async function getDispatchList(id) {
  return request(`/api/app/e-mSTProc-production-process/${id}/get-probe-finish-order`, METHOD.GET);
}
// 分派机台a
export async function getDispatchMachineList(params) {
  return request("/api/app/e-mSTProc-production-process/send-machine", METHOD.POST, params);
}
// 分派回退
export async function getDispatchFallbackList(params) {
  return request(`/api/app/e-mSTProc-production-process/back-machine-order`, METHOD.POST, params);
}
//完成
export async function processfinishorder(params) {
  return request("/api/app/e-mSTProc-production-process/finish-order", METHOD.POST, params);
}
//生产完成
export async function allfinishorder(params) {
  return request("/api/app/e-mSTProc-production-process/all-finish-order", METHOD.POST, params);
}
// 下机
export async function getDoneOrderList(Id) {
  return request(`/api/app/e-mSTProc-dRMake/drill-order-finish/${Id}`, METHOD.POST);
}
// 获取部门过序数量
export async function getpassStepNum(params) {
  return request(`/api/app/e-mSTProc-dRMake/drill-pass-step-num`, METHOD.GET, params);
}
// 钻孔部门过序
export async function DrillingSequence(params) {
  return request(`/api/app/e-mSTProc-dRMake/drill-pass-step`, METHOD.POST, params);
}
// 获取工厂Id列表
export async function getFactoryList() {
  return request(`/api/app/e-mSTPub-factory-configure/factory-id-list`, METHOD.POST);
}
// 按钮上传文件
export async function UploadFile(params) {
  return request(`/api/app/e-mSTProc-dRMake/up-load-drill-file`, METHOD.POST, params);
}
// 右键上传文件
export async function UploadFile1(params) {
  return request(`/api/app/e-mSTProc-dRMake/up-file`, METHOD.POST, params);
}
// 上传订单
export async function UploadOrder(params) {
  return request(`/api/app/e-mSTProc-dRMake/out-drill-order-up-load`, METHOD.POST, params);
}
// 涨缩登记
export async function getHarmomegathusRegister(params) {
  return request(`/api/app/e-mSTProc-dRMake/zs-register`, METHOD.POST, params);
}
// 设置加急
export async function SetUpExpediting(Id) {
  return request(`/api/app/e-mSTProc-production-process/is-urgent/${Id}`, METHOD.POST, Id);
}
// 数据核对
export async function DataCheck() {
  return request(`/api/app/e-mSTProc-dRMake/data-check`, METHOD.POST);
}
// 删除订单
export async function DeleteOrder(Id) {
  return request(`/api/app/e-mSTProc-dRMake/del-order/${Id}`, METHOD.POST, Id);
}
// 异常备注
export async function ExceptionRemarks(params) {
  return request(`/api/app/e-mSTProc-production-process/set-production-remarks`, METHOD.POST, params);
}
// 获取订单已上机台Id数组
export async function getOrderMuIdList(Id) {
  return request(`/api/app/e-mSTProc-production-process/order-mu-id-list/${Id}`, METHOD.GET);
}

// 修改Pnl書
export async function updatePnl(params) {
  return request(`/api/app/e-mSTProc-production-process/update-pnl-qty`, METHOD.POST, params);
}

// 标记/取消 机台故障
export async function signMachineStatus(Id) {
  return request(`/api/app/e-mSTProc-dRMake/set-sign-bad/${Id}`, METHOD.POST);
}
// 下载
export async function downLoad(Id) {
  return request(`/api/app/e-mSTProc-dRMake/down-load-path/${Id}`, METHOD.GET);
}
// 上传(地址和ID)
export async function orderFileUpload(Id, params) {
  return request(`/api/app/e-mSTProc-dRMake/up-load-drill-file/${Id}`, METHOD.GET, params);
}
// 呼叫小车
export async function CallTrolley() {
  return request(`/api/app/e-mSTProc-rOUTMake/call-trolley`, METHOD.POST);
}
// 人员确认
export async function Confirm() {
  return request(`/api/app/e-mSTProc-rOUTMake/confirm`, METHOD.POST);
}
// 取消小车
export async function AgvCancel() {
  return request(`/api/app/e-mSTProc-rOUTMake/agv-cancel`, METHOD.POST);
}
//获取用户授权工厂
export async function getFacAccreditList() {
  return request("/api/app/e-mSTProc-production-process/user-fac-accredit", METHOD.GET);
}
//扫码派单
export async function processorder(Id, Type, OrderNo) {
  return request(`api/app/e-mSTProc-production-process/order?Id=${Id}&Type=${Type}&OrderNo=${OrderNo}&ScanCode=true`, METHOD.GET);
}

export default {
  getWaitOrderList,
  getDoingOrderList,
  getStatisticsList,
  getMachineList,
  getDrillHoleList,
  getDispatchList,
  getDispatchMachineList,
  getDispatchFallbackList,
  getDoneOrderList,
  getpassStepNum,
  DrillingSequence,
  getFactoryList,
  UploadFile,
  UploadFile1,
  UploadOrder,
  getHarmomegathusRegister,
  SetUpExpediting,
  DataCheck,
  DeleteOrder,
  ExceptionRemarks,
  getOrderMuIdList,
  signMachineStatus,
  downLoad,
  orderFileUpload,
  getFacAccreditList,
  processorder,
};
