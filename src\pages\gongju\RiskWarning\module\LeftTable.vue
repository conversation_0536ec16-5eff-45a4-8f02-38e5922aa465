<template>
  <a-table
    :columns="columns"
    bordered
    :pagination="pagination"
    :scroll="{ y: 738, x: 300 }"
    :dataSource="MainData"
    :rowClassName="isRedRow"
    :customRow="onClickRow"
    :rowKey="rowKey"
    @change="handleTableChange"
    :loading="custLoading"
  >
    <span slot="num" slot-scope="text, record, index">
      {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
    </span>
  </a-table>
</template>
<script>
export default {
  props: ["columns", "rowKey", "pagination", "custLoading", "MainData"],
  data() {
    return {
      selectedRowsData: {},
    };
  },
  methods: {
    isRedRow(record) {
      let strGroup = [];
      if (record && record === this.selectedRowsData) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    handleTableChange(pagination) {
      this.$emit("handleTableChange", pagination);
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.selectedRowsData = record;
            this.$emit("getRiskData", record);
          },
        },
      };
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-table {
  .rowBackgroundColor {
    background: #dfdcdc !important;
  }
}
</style>
