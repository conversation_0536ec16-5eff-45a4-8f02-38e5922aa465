<!-- 市场管理 - 预审分派 -订单列表 -->
<template>
  <div ref="tableWrapper" style="height: 100%; width: 100%">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ x: 880, y: 738 }"
      :customRow="customRow"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      :pagination="pagination"
      @change="handleTableChange"
      :rowClassName="isRedRow"
    >
      <!-- :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange,columnWidth: 25,}" -->
      <template slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </template>
      <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
        <a style="color: black" :title="record.orderNo">{{ text }}</a
        >&nbsp;
        <span class="tagNum" style="display: inline-block">
          <a-tooltip title="精细预审" v-if="record.finePre">
            <a-tag class="noCopy TAG"> 精 </a-tag>
          </a-tooltip>
          <span
            v-if="record.isJiaji"
            class="noCopy"
            style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; user-select: none; margin-left: -10px"
            >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
          </span>
          <a-tag v-if="record.isBigCus == '是'" class="noCopy TAG"> KA </a-tag>
          <a-tag v-if="record.quality == '优品'" class="noCopy TAG"> 优品 </a-tag>
          <a-tag v-if="record.reverseOrder_" class="noCopy TAG">
            {{ record.reverseOrder_ | splitFilter }}
          </a-tag>
        </span>
      </div>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
const timeFilter = function (value) {
  let count = 1;

  if (value.indexOf("天") > 0) {
    count = 24;
  }
  var num_ = value.match(/^[a-z|A-Z]+/gi);
  console.log(num_);
};
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    scopedSlots: { customRender: "orderNo" },
    align: "left",
    ellipsis: true,
    width: 120,
    className: "userStyle",
  },
  //   {
  //     title: "订单标记",
  //     key: "tag",
  //     scopedSlots: { customRender: 'tag' },
  //     align: "left",
  //     fixed:'left',
  //     ellipsis: true,
  //     width:140,
  //   },
  {
    title: "工厂",
    dataIndex: "contractFactoryName",
    ellipsis: true,
    width: 75,
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 70,
    className: "userStyle",
  },
  {
    title: "订单类型",
    align: "left",
    ellipsis: true,
    width: 72,
    customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
  },
  {
    title: "订单数量",
    dataIndex: "num",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: "客户型号",
    dataIndex: "customerModel",
    align: "left",
    ellipsis: true,
    width: 335,
    className: "userStyle",
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 75,
    ellipsis: true,
    align: "left",
  },
  {
    title: "预审人",
    dataIndex: "preName",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "上传时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 150,
  },
  {
    title: "建立人",
    dataIndex: "createName",
    align: "left",
    ellipsis: true,
    width: 60,
  },

  // {
  //   title: "交货单位",
  //   dataIndex: "boardType",
  //   width: '10%',
  //   ellipsis: true,
  //   align: "left",

  // },
  // {
  //   title: "客户PO",
  //   dataIndex: "custPo",
  //   width: '10%',
  //   ellipsis: true,
  //   align: "left",

  // },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    width: 100,
    // width: '5%',
    ellipsis: true,
    align: "left",
  },
];
export default {
  components: {
    RightCopy,
  },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            current: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
          selectedRows: {},
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
  },
  name: "LeftTable",
  data() {
    return {
      columns,
      selectedRowKeys: [],
      selectedRowKeys1: [],
      xScroll: 0,
      selectedRows: [],
      isDragging: false,
      startIndex: -1,
      shiftKey: false,
      menuData: {},
    };
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          var count = 0;
          this.dataSource.forEach(item => {
            var flag = 0;
            if (item.isBigCus == "是") {
              flag++;
            }
            if (item.quality == "优品") {
              flag++;
            }
            if (item.boardArea >= 2) {
              flag++;
            }
            if (item.reverseOrder_) {
              flag++;
            }
            if (flag > count) {
              count = flag;
            }
          });
          this.$nextTick(function () {
            let obj = document.getElementsByClassName("tagNum");
            console.log(obj, "obj");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0) {
              this.columns[1].width = "120px";
              this.columns[6].width = "435px";
            }
            if (result >= 1) {
              this.columns[1].width = 120 + result * 20 + "px";
              this.columns[6].width = 435 - result * 5 + "px";
            }
          });
        }
      },
    },
  },
  filters: {
    splitFilter(value) {
      return value.split("-")[1];
    },
  },
  methods: {
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      let arr = selectedRows;
      let arr2 = [];
      arr.forEach(item => {
        arr2.push(item.orderNo);
      });
      this.selectedRowKeys1 = arr2;

      // console.log('this.selectedRows选 :',this.selectedRowKeys1)
      this.$emit("assignOrderListChange", this.selectedRowKeys);
      this.$emit("assignOrderListChange1", this.selectedRowKeys1);
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },

    keydown(e) {
      // console.log('ctrlKey',e.ctrlKey,)
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      // window.addEventListener('keydown', this.keydown)
      // console.log('鼠标按下',this.shiftKey)
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
        // console.log('鼠标拖动',event, record, index)
      }
    },
    handleMouseUp(event, record, index) {
      // console.log('鼠标离开',this.shiftKey, )
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeys;
        let rowKeys1 = this.selectedRowKeys1;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        if (rowKeys1.length > 0 && rowKeys1.includes(record.orderNo)) {
          rowKeys1.splice(rowKeys1.indexOf(record.orderNo), 1);
        } else {
          rowKeys1.push(record.orderNo);
        }
        this.selectedRowKeys = rowKeys;
        this.selectedRowKeys1 = rowKeys1;
        if (this.selectedRowKeys.length == 1) {
          this.selectedRowKeys1 = [record.orderNo];
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowKeys1 = [record.orderNo];
          this.selectedRowKeys = [record.id];
        }
      }

      this.$emit("assignOrderListChange", this.selectedRowKeys);
      this.$emit("assignOrderListChange1", this.selectedRowKeys1);
      this.shiftKey = false;
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      this.selectedRowKeys = arr;
      var arr1 = [];
      for (var i = 0; i < selectedRowsData.length; i++) {
        arr1.push(selectedRowsData[i].orderNo);
      }
      this.selectedRowKeys1 = arr1;
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeys = rowKeys;

            let rowKeys1 = this.selectedRowKeys1;
            if (rowKeys1.length > 0 && rowKeys1.includes(record.orderNo)) {
              rowKeys1.splice(rowKeys1.indexOf(record.orderNo), 1);
            } else {
              rowKeys1.push(record.orderNo);
            }
            this.selectedRowKeys1 = rowKeys1;
            // console.log('this.selectedRows点 :',this.selectedRowKeys1)
            this.$emit("assignOrderListChange", this.selectedRowKeys);
            this.$emit("assignOrderListChange1", this.selectedRowKeys1);
          },
        },
      };
    },
    handleTableChange(pagination) {
      this.$emit("tableChange", pagination);
    },
  },
  mounted() {
    console.log(this.dataSource);
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
};
</script>
<style lang="less" scoped>
.TAG {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
/deep/ .rowBackgroundColor {
  background: #dcdcdc !important;
}
/deep/.userStyle {
  user-select: none !important;
}
/deep/ .ant-table {
  // table-layout: fixed;
  .ant-table-body {
    tr td {
      // max-width: 110px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
/deep/.ant-table-pagination {
  position: absolute;
}
</style>
