<!-- 市场管理 - 价格体系 - 左边列表 -->
<template>
  <div>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ y: 720, x: 300 }"
      :customRow="onClickRow"
      :pagination="pagination"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      @change="handleTableChange"
      :rowClassName="isRedRow"
    >
      <template slot="num" slot-scope="text, record, index">
        {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </template>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import RightCopy from "@/pages/RightCopy";
import { setEngineeringBack } from "@/utils/request";
import { proQuestLog } from "@/services/projectApi";
export default {
  components: {
    RightCopy,
  },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            pageIndex: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
          selectedRows: {},
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
  },
  name: "LeftTable",
  data() {
    return {
      showText: false,
      text: "",
      selectedRowKeysArray: [],
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      menuData: {},
      selectedRowsData: [],
      activeClass: "smallActive",
      proOrderId: "",
    };
  },
  watch: {
    pagination: {
      handler(val) {
        // console.log(val)
      },
    },
  },
  created() {},
  methods: {
    checkPermission,

    isRedRow(record) {
      let strGroup = [];
      if (record.id && record.id == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },

    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.proOrderId = record.id;
            this.$emit("getOrderDetail", record, "1");
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    handleTableChange(pagination) {
      this.$emit("tableChange", pagination);
    },
  },
  mounted() {
    // this.getcookie('ordernoBack')
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }

  .ant-table-fixed-left {
    .cookieIdColor {
      td:first-child {
        //border-left:2px solid #ff9900!important;
        background: #ff9900 !important;
      }
    }

    //td:nth-child(2){
    //  color:#ff9900!important;
    //}
  }

  .rowBackgroundColor {
    background: #dcdcdc;
  }

  .displayFlag {
    display: none;
  }
}

.peopleTag {
  margin: 0;
  padding: 0;
  width: 24px;
  border-radius: 12px;
  background: #2d221d;
  border-color: #2d221d;
  color: #ff9900;
  text-align: center;
  margin-left: 2px;
}
</style>
