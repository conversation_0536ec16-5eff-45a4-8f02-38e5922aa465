<!-- 工具管理- 叠层阻抗-自动叠构 -->
<template>
  <div style="display: flex" ref="SelectBox">
    <div style="width: 61.5%; border: 1px solid #a8a8a8">
      <div>
        <!-- <div>
                    <span>叠层信息</span>
                </div> -->
        <div style="display: flex; width: 100%; position: relative">
          <!-- <div style="width:86%">
                      <a-form-model :model="datainfo" :label-col="labelCol"  :wrapper-col="wrapperCol">
                          <a-row>
                              <a-col :span="6">
                              <a-form-model-item label="产品型号" >
                                  <a-input v-model="datainfo.pdctno4StackUp_"  ref="input1"/>
                              </a-form-model-item>
                              </a-col>
                              <a-col :span="6">
                              <a-form-model-item label="文件层数" >
                                  <a-input v-model="datainfo.layerNum_"  ref="input1"/>
                              </a-form-model-item>
                              </a-col>
                              <a-col :span="6">
                              <a-form-model-item label="内层基铜" >                                
                                  <a-select v-model="datainfo.innerCu4Org" @change="innerCu4Org"
                                  :getPopupContainer="()=>this.$refs.SelectBox">
                                    <a-select-option  v-for="(item,index) in mapKey(selectData.InnerCopperThickness)" :key="index" :value="item.value" :lable="item.lable"  >
                                      {{ item.lable }}
                                    </a-select-option>
                                  </a-select>
                              </a-form-model-item>
                              </a-col>
                              <a-col :span="6">
                              <a-form-model-item label="外层基铜" >
                                  <a-select v-model="datainfo.outerCu4Org"  @change="outerCu4Org"
                                  :getPopupContainer="()=>this.$refs.SelectBox">
                                    <a-select-option  v-for="(item,index) in mapKey(selectData.CopperThickness)" :key="index" :value="item.value" :lable="item.lable"  >
                                      {{ item.lable }}
                                    </a-select-option>
                                  </a-select>
                              </a-form-model-item>
                              </a-col>
                          </a-row> 
                          <a-row>
                              <a-col :span="6">
                              <a-form-model-item label="板材型号" >                              
                                  <a-select  v-model="datainfo.coreType_"                                   
                                    show-search
                                    @change='coreTypeChange($event)'
                                    option-filter-prop="children"
                                    :filter-option="filterOption">
                                    <a-select-option  v-for="item in boardTypeList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                                      {{item.text}}
                                    </a-select-option>
                                  </a-select>
                              </a-form-model-item>
                              </a-col>
                              <a-col :span="6">
                              <a-form-model-item label="要求板厚" >
                                  <a-input v-model="datainfo.thicknessOrg_"  ref="input1"/>
                              </a-form-model-item>
                              </a-col>
                              <a-col :span="6">
                              <a-form-model-item label="内层完成" >
                                  <a-select v-model="datainfo.innerCu4Finished"
                                  :getPopupContainer="()=>this.$refs.SelectBox">
                                    <a-select-option  v-for="(item,index) in mapKey(selectData.InnerCopperThickness)" :key="index" :value="item.value" :lable="item.lable"  >
                                      {{ item.lable }}
                                    </a-select-option>
                                  </a-select>
                              </a-form-model-item>
                              </a-col>
                              <a-col :span="6">
                              <a-form-model-item label="外层完成"
                              :getPopupContainer="()=>this.$refs.SelectBox" >
                                  <a-select v-model="datainfo.outerCu4Finished">
                                    <a-select-option  v-for="(item,index) in mapKey(selectData.CopperThickness)" :key="index" :value="item.value" :lable="item.lable"  >
                                      {{ item.lable }}
                                    </a-select-option>
                                  </a-select>
                              </a-form-model-item>
                              </a-col>
                          </a-row>
                      </a-form-model>
                  </div> -->
          <!-- <div style="width:15%;position: absolute;bottom:4px;right:0;" >
                      <a-button type="primary" style="margin-left:10%" @click="getInfoClick">
                        读取文件
                      </a-button>
                      <a-button type="primary" style="margin-top:5%;margin-left:10%;width:88px;" @click="renovate">
                        刷新
                      </a-button>
                  </div> -->
        </div>
      </div>
      <!-- <div >
                <div>
                    <span>文件层信息</span>
                </div>
                <div style="display:flex;width:100%;position: relative;">
                  <div  style="width:85%">
                      <a-table
                      :columns="columns"
                      :data-source="linedata"
                      bordered
                      :loading="TableLoading2"
                      :scroll="{ y:200}"
                      :rowKey="'lineID_'"
                      :pagination="false"
                      :class="linedata.length >0?'min-table':''"> 
                      <template slot="orgLayTB_" slot-scope="text,record" >                  
                        <a-select v-model="record.orgLayTB_">
                        <a-select-option  value="t"  >
                         t
                        </a-select-option>
                        <a-select-option  value="b"  >
                         b
                        </a-select-option>
                      </a-select>
                      </template>
                      <template slot="copperRatio_" slot-scope="text,record" >                  
                        <a-input v-model="record.copperRatio_"></a-input>
                      </template> 
                      <template slot="cU4Org_" slot-scope="text,record" >                  
                        <a-select v-model="record.cU4Org_">
                        <a-select-option  v-for="(item,index) in mapKey(selectData.InnerCopperThickness)" :key="index" :value="item.value" :lable="item.lable"  >
                          {{ item.lable }}
                        </a-select-option>
                      </a-select>
                      </template> 
                      <template slot="cU4Finished_" slot-scope="text,record" >                  
                        <a-select v-model="record.cU4Finished_">
                        <a-select-option  v-for="(item,index) in mapKey(selectData.InnerCopperThickness)" :key="index" :value="item.value" :lable="item.lable"  >
                          {{ item.lable }}
                        </a-select-option>
                      </a-select>
                      </template> 
                     
                      </a-table>
                  </div>
                  <div style="width:15%;position: absolute;bottom:4px;right:0;" >
                      <a-checkbox v-model="isImp" style="margin-left:10%">
                          匹配阻抗
                      </a-checkbox>
                      <a-button  type="primary"  @click="autoStackClick" style="margin-top:10%;margin-left:10%">
                          生成叠层
                      </a-button>
                  </div>
                </div>
            </div> -->
      <div>
        <!-- <div>
                    <span>自动叠构</span>
                </div> -->
        <div style="height: 572px">
          <a-table
            :columns="columns1"
            :data-source="strackdata"
            :rowKey="'id'"
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            :scroll="{ y: 540, x: 800 }"
            bordered
            :loading="TableLoading1"
            :class="strackdata.length > 0 ? 'min-table1' : ''"
            :pagination="false"
          >
            <!-- <span slot="action" slot-scope="text,record,index">
                      <a @click="ConfirmOverlay(index)">确认叠构</a>
                    </span>                   -->
          </a-table>
        </div>
      </div>
    </div>
    <div v-if="tableData.length" style="width: 38.5%">
      <h3
        style="margin: 0; border: 1px solid #a8a8a8; border-left-style: initial; border-left: 0; text-align: center; height: 30px; border-bottom: 0"
      >
        叠层信息
      </h3>
      <div class="reportTable">
        <a-spin :spinning="TableLoading3">
          <div
            style="
              width: 260px;
              padding: 23px 20px 20px 0;
              border: 1px solid;
              border-right: 0;
              border-left: 0;
              border-top: 0;
              border-bottom: 0;
              position: relative;
            "
          >
            <div v-for="(item, index) in tableData" :key="index" v-show="TableLoading3 == false">
              <div v-if="item.stackUpMTR_ == 'OZ'" class="OZclass" :class="[index == tableData.length - 1 ? 'ozlastClass' : '']">
                <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                  {{ item.child[0].stackUpLayerNo_ | layerFilter(that) }}
                </div>
                <div class="ozContent">
                  <div class="oz_bg" :class="[index == tableData.length - 1 ? 'oz_active' : '']"></div>
                </div>
              </div>
              <div v-if="item.stackUpMTR_ == 'Core'" class="coreClass">
                <div v-for="(ite, idx) in item.child" :key="idx" :class="[ite.stackUpMTR_ == 'Core' ? 'coreActive' : 'ozActive']">
                  <div v-if="ite.stackUpMTR_ == 'OZ'" class="OZclass">
                    <div v-if="ite.stackUpLayerNo_" :class="['layerName', 'L' + ite.stackUpLayerNo_]">L{{ ite.stackUpLayerNo_ }}</div>
                    <div class="ozContent2"></div>
                  </div>
                  <div v-if="ite.stackUpMTR_ == 'Core'" class="core-box">
                    <div v-if="ite.stackUpLayerNo_" :class="['layerName', 'L' + ite.stackUpLayerNo_]">L{{ ite.stackUpLayerNo_ }}</div>
                    <div class="CoreContent"></div>
                  </div>
                </div>
              </div>
              <div v-if="item.stackUpMTR_ == '金属基' || item.stackUpMTR_ == '补强'" class="jsjclass">
                <div :class="['layerName', '金属基']">{{ item.stackUpMTR_ }}</div>
                <div class="jsjContent"></div>
              </div>
              <div v-if="item.stackUpMTR_ == 'PP'" :class="item.child[0].stackUpMTRFoil_.split('+').length <= 3 ? 'PPclass' : 'PPclass'">
                <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                  L{{ item.child[0].stackUpLayerNo_ }}
                </div>
                <div class="PPContent"></div>
              </div>
              <div v-if="item.stackUpMTR_ == 'GB'" class="GBClass">
                <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                  L{{ item.child[0].stackUpLayerNo_ }}
                </div>
                <div class="gbContent"></div>
              </div>
            </div>
            <div v-if="laminationData.stackUpDrills.length > 0" class="drillClss" v-show="TableLoading3 == false">
              <div
                v-for="(list, index) in laminationData.stackUpDrills"
                :key="index"
                :ref="'dir_' + index"
                :class="Number(list.startLayer) > Number(list.endLayer) ? 'drillItem1' : 'drillItem'"
              ></div>
            </div>
          </div>
        </a-spin>
        <div class="parameterClass">
          <table>
            <tr
              v-for="(item, index) in tableData"
              :key="index"
              :class="[item.stackUpMTR_ == 'OZ' ? 'paramOZ' : item.stackUpMTR_ == 'Core' ? 'paramCore' : 'paramPP']"
            >
              <td>{{ item | paramFilter }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAutoStackInfoData, getAutoStackResultData, setautostackinfov2, setautostackinfov3 } from "@/services/impedance";
const columns = [
  {
    title: "层名",
    dataIndex: "layName_",
    align: "left",
    // fixed: 'left',
    ellipsis: true,
    width: 50,
  },
  {
    title: "T/B",
    // dataIndex: "orgLayTB_",
    align: "left",
    // fixed: 'left',
    ellipsis: true,
    scopedSlots: { customRender: "orgLayTB_" },
    width: 80,
  },
  {
    title: "残铜率",
    // dataIndex: "copperRatio_",
    align: "left",
    // fixed: 'left',
    ellipsis: true,
    scopedSlots: { customRender: "copperRatio_" },
    width: 80,
  },
  {
    title: "基铜",
    // dataIndex: "cU4Org_",
    align: "left",
    // fixed: 'left',
    ellipsis: true,
    scopedSlots: { customRender: "cU4Org_" },
    width: 80,
  },
  {
    title: "完成铜厚",
    // dataIndex: "cU4Finished_",
    align: "left",
    scopedSlots: { customRender: "cU4Finished_" },
    // fixed: 'left',
    ellipsis: true,
    width: 80,
  },
  {
    title: "原始文件名",
    dataIndex: "orgLayName_",
    align: "left",
    // fixed: 'left',
    ellipsis: true,
    // width: 80
  },
];
// const columns1 = [
//   {
//     title: "序号",
//     dataIndex: "id",
//     align: "center",
//     // fixed: 'left',
//     ellipsis: true,
//     width: 15,
//   },
//   {
//     title: "core",
//     dataIndex: "core",
//     align: "left",
//     // fixed: 'left',
//     ellipsis: true,
//     width: 30,
//   },
//   {
//     title: "内铜",
//     dataIndex: "incu",
//     align: "left",
//     // fixed: 'left',
//     ellipsis: true,
//     width: 30,
//   },
//   {
//     title: "外铜",
//     dataIndex: "outcu",
//     align: "left",
//     // fixed: 'left',
//     ellipsis: true,
//     width: 30,
//   },
//   {
//     title: "压合厚度",
//     dataIndex: "thick",
//     align: "left",
//     // fixed: 'left',
//     ellipsis: true,
//     width: 30,
//   },
//   // {
//   //   title: "操作",
//   //   align: "left",
//   //   // fixed: 'left',
//   //   ellipsis: true,
//   //   width: 60,
//   //   scopedSlots: { customRender: 'action' },
//   // }
// ]
const columns1 = [
  //  {
  //     title: "序号",
  //     dataIndex: "index",
  //     align: "left",
  //     customRender: (text,record,index) => `${index+1}`,
  //     ellipsis: true,
  //     width:40
  //   },
];
export default {
  name: "AutoStack",
  props: {
    laminationData: {
      type: Object,
      required: true,
    },
    boardTypeList: {
      type: Array,
      required: true,
    },
    selectData: {
      type: Object,
      required: true,
    },
    impedanceTab: {
      type: Array,
      required: true,
    },
    joinFactoryId: {
      required: true,
    },
  },

  data() {
    return {
      that: this,
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      datainfo: {},
      linedata: [],
      strackdata: [],
      strackdatasig: {},
      tableData: [],
      isImp: false,
      columns,
      columns1,
      proOrderId: "",
      selectedRowKeysArray: [],
      TableLoading1: false,
      TableLoading2: false,
      TableLoading3: false,
      colTypeList: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center",
          customRender: (text, record, index) => `${index + 1}`,
          ellipsis: true,
          width: 40,
        },
      ],
    };
  },
  // computed:{
  //   tableData: function(){
  //     let _self = this;
  //     let newData = []
  //     // let structure = _self.laminationData.stackUps.map(item => item.stackUpMTR_);
  //     let data = _self.laminationData.stackUps
  //     data.forEach((item,index)=> {
  //       let child = []
  //       if (item.stackUpMTR_ == 'OZ') {
  //         if (data[index+1]?.stackUpMTR_ == 'Core' && data[index+2]?.stackUpMTR_ == 'OZ') {
  //           child.push(item)
  //           child.push(data[index+1])
  //           child.push(data[index+2])
  //           newData.push({'stackUpMTR_':'Core', 'child': child})
  //         } else {
  //           if (data[index-1]?.stackUpMTR_ != 'Core' || index==0)
  //             newData.push(item)
  //         }
  //       } else if (item.stackUpMTR_ == 'PP') {
  //         newData.push(item)
  //       }else if(item.stackUpMTR_ == 'GB'){
  //         newData.push(item)
  //       }
  //     })
  //     console.log('tableData',newData)
  //     return newData
  //   },
  // },
  filters: {
    layerFilter(val, that) {
      return "L" + val;
      // if (val ==1 ){
      //   return 'GTL'
      // } else if (val == that.tableData[that.tableData.length-1].stackUpLayerNo_) {
      //   return 'GBL'
      // } else {
      //   return  'L'+val
      // }
    },
    floatFilter(val) {
      // console.log('val',val)
      return Number(val).toFixed(2);
    },
    typeFilter(val) {
      if (val == "OZ") {
        return "铜箔";
      } else if (val == "PP") {
        return "半固化片";
      } else if (val == "Core") {
        return "芯板";
      } else if (val == "GB") {
        return "光板";
      }
    },
    nameFilter(val) {
      if (val.child) {
        let str = "";
        val.child.forEach(item => {
          if (item.stackUpMTR_ == "Core") {
            str = item.stackUpMTRType_;
          }
        });
        return str;
      } else {
        return val.stackUpMTRType_;
      }
    },
    paramFilter(val) {
      if (val.child) {
        let val_ = val.child;
        let str_ = ``;
        val_.forEach(item => {
          // console.log('物料参数',item.stackUpMTR_)
          if (item.stackUpMTR_ == "OZ") {
            str_ += item.stackUpMTRFoil_ + "OZ" + "\n";
          } else {
            // let corStr_ = item.tdFlag_ ? '含铜）' : '不含铜）'
            let corStr_ = item.stackUpCoreDS_ ? "含铜）" : "不含铜）";
            str_ += item.stackUpMTRFoil_ + "(" + corStr_ + "\n";
          }
        });
        return str_;
      } else {
        if (val.stackUpMTR_ == "OZ") {
          return val.stackUpMTRFoil_ + "  OZ";
        } else {
          // 2022/08/05 屏蔽PP型号去重
          // let str = ''
          // let _data  = val.stackUpMTRFoil_.split('+').reduce(function (a, b) {
          //   if (b in a) {
          //     a[b]++
          //
          //   } else {
          //     a[b] = 1
          //   }
          //   return a
          // }, {})
          // var _dataKeys = Object.keys(_data)
          // str = _dataKeys.join('+')
          // return  str
          return val.stackUpMTRFoil_;
        }
      }
    },
    ctlFilter(val) {
      if (val.child) {
        let val_ = val.child;
        let str_ = ``;
        val_.forEach(item => {
          if (item.stackUpCTLMI_) {
            str_ += item.stackUpCTLMI_ + "\n";
          } else {
            str_ += "/" + "\n";
          }
        });
        return str_ || "/";
      } else {
        return val.stackUpCTLMI_ || "/";
      }
    },
    cphdFilter(val) {
      if (val.child) {
        let val_ = val.child;
        let str_ = ``;
        val_.forEach(item => {
          str_ += item.stackUpThichnessMM_ + "\n";
        });
        return str_;
      } else {
        return val.stackUpThichnessMM_;
      }
    },
  },
  watch: {
    strackdatasig(val) {
      // console.log('jianting ',val)
      this.$emit("ConfirmOverlay", val);
    },
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    getAutoStackInfoData() {
      getAutoStackInfoData(this.laminationData).then(res => {
        if (res.code) {
          this.datainfo = res.data.infoDto;
          this.linedata = res.data.lineDtos;
          let params = {};
          params.infoDto = this.datainfo;
          params.lineDtos = this.linedata;
          params.impOutputs = [];
          params.impMathing = this.isImp;
          getAutoStackResultData(params).then(res => {
            if (res.code) {
              this.strackdata = res.data;
            }
          });
        }
      });
    },
    setautostackinfov2() {
      let params = this.laminationData;
      params.impOutputs = this.impedanceTab;
      this.TableLoading1 = true;
      setautostackinfov2(params)
        .then(res => {
          if (res.code) {
            this.strackdata = res.data;
          }
        })
        .finally(() => {
          this.TableLoading1 = false;
        });
    },
    setautostackinfov3() {
      let params = this.laminationData;
      params.impOutputs = this.impedanceTab;
      params.joinFactoryId = this.joinFactoryId;
      this.TableLoading1 = true;
      params.isAuto = true;
      // console.log('laminationData',this.laminationData)
      params.mode = Number(this.$route.query.mode) || 0;
      setautostackinfov3(params)
        .then(res => {
          if (res.code) {
            let arr = JSON.parse(res.data);
            let aa = Object.keys(arr[0]);
            aa.splice(aa.indexOf("stackdtos"), 1);
            aa.forEach(e => {
              if (e == "id") {
                this.colTypeList.push({
                  title: e,
                  dataIndex: e,
                  align: "center",
                  ellipsis: true,
                  className: "tdclass",
                  width: 43,
                });
              } else if (e.indexOf("PP") != -1 && e.indexOf("_") == -1) {
                let str = [];
                for (var i = 0; i < arr.length; i++) {
                  str.push(arr[i][e].length);
                }
                const max = Math.max(...str);
                let wid = 50 * (max / 4);
                this.colTypeList.push({
                  title: e,
                  dataIndex: e,
                  align: "center",
                  ellipsis: true,
                  width: wid,
                  className: "tdclass",
                });
              } else if (e == "ImpMatchNo") {
                this.colTypeList.push({
                  title: e,
                  dataIndex: e,
                  align: "center",
                  ellipsis: true,
                  className: "tdclass",
                  width: 120,
                });
              } else {
                this.colTypeList.push({
                  title: e,
                  dataIndex: e,
                  align: "center",
                  ellipsis: true,
                  className: "tdclass",
                  width: 80,
                });
              }
            });
            // 这里将查询出来列添加到固定的列当中
            this.columns1 = [...this.columns1, ...this.colTypeList];
            // console.log('columns1',this.columns1)
            for (var a = 0; a < arr.length; a++) {
              arr[a].stackdtos = JSON.parse(arr[a].stackdtos);
            }
            this.strackdata = arr;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.TableLoading1 = false;
        });
    },
    // autoStackClick(){
    //     let params={};
    //     params.infoDto=this.datainfo
    //     params.lineDtos=this.linedata
    //     params.impOutputs=[]
    //     params.impMathing=this.isImp
    //     this.TableLoading1 = true
    //     getAutoStackResultData(params).then(res=>{
    //         if(res.code){
    //             this.strackdata= res.data
    //           //   let newData = []
    //           //   let data = this.strackdata[0].stackdtos
    //           //   data.forEach((item,index)=> {
    //           //   let child = []
    //           //   if (item.stackUpMTR_ == 'OZ') {
    //           //     if (data[index+1]?.stackUpMTR_ == 'Core' && data[index+2]?.stackUpMTR_ == 'OZ') {
    //           //       child.push(item)
    //           //       child.push(data[index+1])
    //           //       child.push(data[index+2])
    //           //       newData.push({'stackUpMTR_':'Core', 'child': child})
    //           //     } else {
    //           //       if (data[index-1]?.stackUpMTR_ != 'Core' || index==0)
    //           //         newData.push(item)
    //           //     }
    //           //   } else if (item.stackUpMTR_ == 'PP') {
    //           //     newData.push(item)
    //           //   }else if(item.stackUpMTR_ == 'GB'){
    //           //     newData.push(item)
    //           //   }
    //           // })
    //           //   this.tableData = newData
    //           //   this.proOrderId =this.strackdata[0].id
    //         }
    //     }).finally(()=>{
    //       this.TableLoading1 = false
    //     })
    // },
    coreTypeChange() {
      this.boardTypeList.forEach(item => {
        if (item.valueMember == this.datainfo.coreType_) {
          this.datainfo.coreTypeCode = item.text;
        }
      });
      // console.log(this.datainfo.coreType_,this.datainfo.coreTypeCode)
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            let newData = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.strackdatasig = record;
            this.proOrderId = record.id;
            this.TableLoading3 = true;
            let data = this.strackdatasig.stackdtos;
            data.forEach((item, index) => {
              let child = [];
              if (item.stackUpMTR_ == "OZ") {
                if (data[index + 1]?.stackUpMTR_ == "Core" && data[index + 2]?.stackUpMTR_ == "OZ") {
                  child.push(item);
                  child.push(data[index + 1]);
                  child.push(data[index + 2]);
                  newData.push({ stackUpMTR_: "Core", child: child });
                } else {
                  if (data[index - 1]?.stackUpMTR_ != "Core" || index == 0) {
                    child.push(item);
                    newData.push({ stackUpMTR_: "OZ", child: child });
                  }
                }
              } else if (item.stackUpMTR_ == "PP") {
                child.push(item);
                newData.push({ stackUpMTR_: "PP", child: child });
              } else if (item.stackUpMTR_ == "GB") {
                child.push(item);
                newData.push({ stackUpMTR_: "GB", child: child });
              } else if (item.stackUpMTR_ == "金属基") {
                child.push(item);
                newData.push({ stackUpMTR_: "金属基", child: child });
              } else if (item.stackUpMTR_ == "补强") {
                child.push(item);
                newData.push({ stackUpMTR_: "补强", child: child });
              }
            });
            this.tableData = newData;
            setTimeout(() => {
              this.styleHeight_();
              this.TableLoading3 = false;
            }, 500);
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && record.id == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data)
          .sort()
          .map(item => {
            return { value: item, lable: data[item] };
          });
      }
    },
    innerCu4Org() {
      this.datainfo.innerCu4Finished = this.datainfo.innerCu4Org;
    },
    outerCu4Org() {
      this.datainfo.innerCu4Finished = this.datainfo.outerCu4Finished;
    },
    getInfoClick() {
      this.$emit("getInfoClick");
    },
    renovate() {
      this.getAutoStackInfoData();
    },
    styleHeight_(val) {
      let newArr = [],
        topArr = [];
      let arr = [];
      this.laminationData.stackUpDrills.forEach(item => {
        item.count = item.endLayer - item.startLayer;
        arr.push(item);
      });
      arr.sort((a, b) => {
        // 首先比较 count，如果 count 相同，则比较 startLayer
        if (b.count !== a.count) {
          return b.count - a.count; // 降序排序 count
        } else {
          return a.startLayer - b.startLayer; // 当 count 相同时，升序排序 startLayer
        }
      });
      this.laminationData.stackUpDrills = arr;
      const forwardIntervals = arr.filter(interval => interval.startLayer <= interval.endLayer);
      const backwardIntervals = arr.filter(interval => interval.startLayer > interval.endLayer);
      backwardIntervals.sort((a, b) => {
        // 首先比较 count，如果 count 相同，则比较 startLayer
        if (b.count !== a.count) {
          return b.count - a.count; // 降序排序 count
        } else {
          return a.endLayer - b.endLayer; // 当 count 相同时，升序排序 startLayer
        }
      });
      let groups = this.filterAndGroupLayers(forwardIntervals);
      let groups1 = this.filterAndGroupLayers1(backwardIntervals);
      let all = [...groups, ...groups1];
      this.laminationData.stackUpDrills.forEach((ite, index) => {
        if (!ite.drillName.includes("ksk")) {
          let height_ = 0;
          let top_ = 0;
          let startIndex = this.tableData.findIndex(item => {
            return item.child[0].stackUpLayerNo_ == ite.startLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.startLayer);
          });
          let endIndex = this.tableData.findIndex(item => {
            return item.child[0].stackUpLayerNo_ == ite.endLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.endLayer);
          });
          if (Number(startIndex) > Number(endIndex)) {
            endIndex = this.tableData.findIndex(item => {
              return item.child[0].stackUpLayerNo_ == ite.startLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.startLayer);
            });
            startIndex = this.tableData.findIndex(item => {
              return item.child[0].stackUpLayerNo_ == ite.endLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.endLayer);
            });
          }
          newArr = this.tableData.slice(startIndex, endIndex + 1);
          topArr = this.tableData.slice(0, startIndex + 1);
          newArr.forEach(list => {
            if (list.stackUpMTR_ == "OZ" && list.child.length == "1") {
              height_ += 20;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[2].stackUpLayerNo_ == ite.startLayer &&
              Number(ite.startLayer) < Number(ite.endLayer)
            ) {
              height_ += 20;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[2].stackUpLayerNo_ == ite.endLayer &&
              Number(ite.endLayer) < Number(ite.startLayer)
            ) {
              height_ += 20;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[0].stackUpLayerNo_ == ite.endLayer &&
              Number(ite.startLayer) < Number(ite.endLayer)
            ) {
              height_ += 20;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[0].stackUpLayerNo_ == ite.startLayer &&
              Number(ite.endLayer) < Number(ite.startLayer)
            ) {
              height_ += 20;
            } else if (list.stackUpMTR_ == "Core" && list.child.length == "3") {
              height_ += 64;
            } else {
              height_ += 20;
            }
          });
          topArr.forEach(res => {
            if (res.stackUpMTR_ == "OZ" && res.child.length == "1" && ite.startLayer == "1") {
              top_ += 20;
            } else if (res.stackUpMTR_ == "OZ" && res.child.length == "1") {
              top_ += 20;
            } else if (
              res.stackUpMTR_ == "Core" &&
              res.child.length == "3" &&
              ite.startLayer == res.child[0].stackUpLayerNo_ &&
              Number(ite.startLayer) < Number(ite.endLayer)
            ) {
              top_ += 20;
            } else if (
              res.stackUpMTR_ == "Core" &&
              res.child.length == "3" &&
              ite.endLayer == res.child[0].stackUpLayerNo_ &&
              Number(ite.endLayer) < Number(ite.startLayer)
            ) {
              top_ += 20;
            } else if (res.stackUpMTR_ == "Core" && res.child.length == "3") {
              top_ += 64;
            } else {
              top_ += 20;
            }
          });
          all.forEach((data1, ind) => {
            data1.forEach(item => {
              if (item === ite) {
                this.$refs["dir_" + index][0].style.left = (ind + 1) * 18 + 60 + "px";
              }
            });
          });
          this.$refs["dir_" + index][0].style.height = height_ - 20 + "px";
          this.$refs["dir_" + index][0].style.top = (Number(ite.startLayer) < Number(ite.endLayer) ? top_ + 6 : top_ + 11) + "px";
        }
      });
    },
    filterAndGroupLayers(arr) {
      const result = [];
      const used = new Set();
      for (let i = 0; i < arr.length; i++) {
        const interval = arr[i];
        if (!used.has(i)) {
          const group = [interval];
          let currentEnd = interval.endLayer;
          for (let j = i + 1; j < arr.length; j++) {
            if (arr[j].startLayer > currentEnd) {
              group.push(arr[j]);
              currentEnd = arr[j].endLayer;
              used.add(j);
            }
          }
          result.push(group);
        }
      }
      return result;
    },
    filterAndGroupLayers1(arr) {
      const result = [];
      const used = new Set();
      for (let i = 0; i < arr.length; i++) {
        const interval = arr[i];
        if (!used.has(i)) {
          const group = [interval];
          let currentStart = interval.startLayer;
          for (let j = i + 1; j < arr.length; j++) {
            if (arr[j].endLayer > currentStart) {
              group.push(arr[j]);
              currentStart = arr[j].startLayer;
              used.add(j);
            }
          }
          result.push(group);
        }
      }
      return result;
    },
  },
  mounted() {
    //this.getAutoStackInfoData()
    // this.setautostackinfov2()
    this.setautostackinfov3();
  },
};
</script>

<style lang="less" scoped>
// .tdclass{
//   min-width:50px;
// }
/deep/ .min-table1 {
  .ant-table-body {
    min-height: 540px;
  }
}

/deep/.ant-select {
  width: 100%;
}
/deep/.ant-select-selection--single {
  height: 24px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  line-height: 22px !important;
}
/deep/.ant-input {
  width: 100%;
  height: 24px;
}

/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
/deep/.rowBackgroundColor > td {
  background: #dfdcdc !important;
}
/deep/.ant-table-thead > tr > th {
  padding: 4px 0 !important;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-table-tbody > tr > td {
  padding: 4px !important;
  font-weight: 500;
  color: #000000;
  background: #fff;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.userStyle {
  user-select: all !important;
}
.reportTable {
  border: 1px solid #a8a8a8;
  border-left: 0;
  // height:294px;
  // height:323px;
  height: 544px;
  display: flex;
  flex-wrap: wrap;
  max-height: 544px;
  overflow: auto;
  .thickness {
    width: 100%;
    text-align: right;
    padding-right: 15px;
    line-height: 20px;
    // font-weight: bold;
    font-size: 16px;
    border-top: 1px solid;
    border-bottom: 1px solid;
  }
  .layerName {
    width: 60px;
    height: 100%;
    line-height: 20px;
    // border: 2px solid;
    text-align: center;
    // font-weight: bold;
    font-size: 14px;
    color: #0000cc;
  }
  .ozlastClass {
    align-items: center;
  }
  .OZclass {
    height: 20px;
    display: flex;
    overflow: hidden;
    align-items: center;
    .ozContent {
      //border-bottom: 1px solid;
      // width: 100%;
      // margin-left: 20px;
      width: calc(100% - 68px);
      margin-left: 8px;
      display: flex;
      align-items: flex-end;
      .oz_bg {
        width: 100%;
        // border-radius: 7px;
        height: 10px;
        overflow: hidden;
        background: #f4a460;
        // background: url("../../../assets/img/pp.png") repeat-x;
        // background-position-x: -12px;
      }
      .oz_active {
        // background: url("../../../assets/img/pp2.png") repeat-x;
        // background-position-x: -12px;
        background: #f4a460;
      }
    }
  }
  .PPclass {
    overflow: hidden;
    .PPContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 10px;
      margin: 5px 0;
      background: #228b22;
      // background: #9ACD32;
      // border-radius: 5px;
    }
  }
  .coreClass {
    .ozActive:nth-of-type(1) {
      // margin-bottom: 16px;
      margin-bottom: 12px;
      height: 32px;
    }
    position: relative;
    .coreActive {
      position: absolute;
      width: 100%;
      top: 12px;
    }
    // .ozContent2 {
    //   border-bottom: 2px solid black;
    //   width: 100%;
    // }
    .ozContent2 {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 10px;
      margin: 0 0 6px 8px;
      background: #f4a460;
    }
    .core-box {
      // height: 32px;
      height: 34px;
      width: 100%;
      overflow: hidden;
      .CoreContent {
        height: 100%;
        float: right;
        // background: #FCB505;
        // background: #FCB408;
        width: calc(100% - 68px);
        background: #f0e68c;
      }
    }
  }
  .GBClass {
    overflow: hidden;
    .gbContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 22px;
      margin: 3px 0;
      background: #fcb505;
      border-radius: 5px;
    }
  }
  .drillClss {
    left: 0;
    top: 0;
    padding: 23px 20px 20px;
    width: 100%;
    position: absolute;
    height: 100%;
  }
  .drillItem {
    width: 9px;
    position: absolute;
    background: #0000cc;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    box-shadow: 0px 2px 2px 0px;
  }
  .drillItem:after {
    content: "";
    display: block;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #0000cc;
    position: absolute;
    bottom: -8px;
    left: -3.5px;
  }
  .drillItem1 {
    width: 9px;
    position: absolute;
    background: #0000cc;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    box-shadow: 0px 2px 2px 0px;
  }
  .drillItem1:after {
    content: "";
    display: block;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #0000cc;
    position: absolute;
    // bottom: -8px;
    top: -6px;
    left: -3.5px;
  }
  .parameterClass {
    padding: 23px 20px 20px;
    // .paramPP {
    //   height: 20px;
    //   line-height:20px;
    // }
    table {
      border: 0;
      font-size: 12px;
    }
    table tr td {
      text-align: center;
      color: #0000cc;
    }
    .paramCore {
      height: 64px;
      td {
        white-space: pre;
      }
    }
  }
  // .parameterClass {
  //   flex: 1;
  //   table tr th {
  //     text-align: center;
  //     width: 80px;
  //   }
  //   table tr th:nth-child(2) {
  //     width: 100px;
  //   }
  //   table tr th:nth-child(3) {
  //     width: 140px;
  //   }
  //   table tr th:nth-child(4) {
  //     width: 60px;
  //   }
  //   table tr th:nth-child(5) {
  //     width: 100px;
  //   }
  //   table {
  //     border-left: 1px solid black;
  //   }
  //   table tr:nth-child(1) {
  //     border: 1px solid black;
  //     border-left: none;

  //   }
  //   table tr th {
  //     border-right: 1px solid black;
  //   }
  //   table tr td {
  //     text-align: center;
  //     color: #0000CC;
  //     border-right:1px solid black;
  //     border-bottom:1px solid black ;
  //   }
  //   .paramOZ{
  //     height: 27px;
  //     line-height: 27px;
  //   }
  //   .paramCore {
  //     height: 60px;
  //     td {
  //       white-space: pre;
  //     }

  //     //line-height: 30px;
  //   }
  //   .paramPP {
  //     height: 19px;
  //     line-height: 19px;
  //   }
  // }
}
</style>
