<template>
  <a-modal
    title="查看图片"
    :width="640"
    :visible="visible"
    :confirmLoading="confirmLoading"
    centered
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-row>
        <a-col :span="8" v-for="(tmp,index) in fileList" :key="index">
          <a  target="_blank">
            <img :src="tmp.strUrl_" alt="图片" class='imgStyle' @click="handlePreview(tmp.strUrl_)">
          </a>
        </a-col>
      </a-row>
    </a-spin>
  </a-modal>
</template>
<script>
import { getPhoto } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }  
        }
  },
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
      this.visible = true;
      this.getPhotos(model)
    },
    handlePreview(img){
      this.$nextTick(() => {
        this.$viewerApi({
          images: [img],
        });
      });
    },

    //查看图片
    getPhotos(id) {
        getPhoto(id).then((res) => {
            this.fileList = res
          }).finally(() => {
            this.confirmLoading = false;
        });
    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
    },
    handleOk() {
      this.visible = false;
    },
  },
};
</script>

<style scoped>
.imgStyle {
    width: 100%;
    /*height: 150px;*/
    border: 5px solid #ffffff;
    border-radius: 20px;
}
</style>