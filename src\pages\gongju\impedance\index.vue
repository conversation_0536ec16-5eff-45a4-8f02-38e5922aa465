<!-- 工具管理- 叠层阻抗 -->
<template>
  <div class="impedance">
    <a-spin :spinning="spinning">
      <footer-action
        @getStructure="getStructure"
        @getoldData="getoldData"
        @dataSave="dataSave"
        @count="count"
        @openReport="openReport"
        @Exportdirectly="Exportdirectly"
        @countImpedance="countImpedance"
        @inverseCount="inverseCount"
        @Impedancereport="Impedancereport"
        @getAllData="getAllData"
        @clearData="clearData"
        @factoryChange="factoryChange"
        @exportData="exportData"
        @getTemplate="getTemplate"
        @autoStack="autoStack"
        @countCopper="countCopper"
        ref="footerAction"
      />
      <div :style="{ height: tableHeight + 'px', width: '100%', position: 'relative' }">
        <!--   v-if="boardTypeList.length > 0" -->
        <basic-info
          :boardTypeList="boardTypeList"
          :ppTypeList="ppTypeList"
          :pressingThicknessBg="pressingThicknessBg"
          @formChange="formChange"
          @changeDrillHoleTableData="changeDrillHoleTableData"
          :collapse="collapse"
          @collapseChange="collapseChange"
          :laminationData="dataSource"
          @getAllData1="getAllData1"
          @getAllData="getAllData"
          :tableHeight="tableHeight"
          ref="formInfo"
          @getLayerChange="getLayerChange"
          @getoldData="getoldData"
          :flg1="flg1"
        />
        <lamination-info
          :ppList="ppList"
          :indlist="indlist"
          @assignment="assignment"
          :loading="ppTableLoading"
          :dataSourcePP="dataSourcePP"
          :ppId="ppId"
          :ppChange="ppChange"
          @ppTableFiltet="ppTableFiltet"
          @rcChange="rcChange"
          @PPChange="PPChange"
          @submitPP="submitPP"
          @Datachange="Datachange"
          @handleCancel="handleCancel"
          :boardTypeList="boardTypeList"
          :ddta="ddta"
          :ppTypeList="ppTypeList"
          :ozListData="ozListData"
          :coreListData="coreListData"
          :GBListData="GBListData"
          :data.sync="dataSource"
          :columns="columns"
          :someList="someList"
          @insert1="insert1"
          :fac="joinFactoryId.toString()"
          :iminsert="iminsert"
          @showModel="showModel"
          @pressingThicknessBgChange="pressingThicknessBgChange"
          :collapse="collapse"
          :laminationInfoLoading="laminationInfoLoading"
          :tableHeight="tableHeight"
          ref="laminationInfo"
          @AntiTrigger="AntiTrigger"
          :okBtnFlg="okBtnFlg"
        />
      </div>
      <div id="touchmove" style="width: 100%; margin-top: 4px; margin-bottom: 4px; height: 3px; background: #ababab; cursor: s-resize"></div>
      <div style="height: 30px; border: #ccc 1px solid; border-radius: 5px 5px 0 0; border-bottom: none; line-height: 30px">
        <span style="margin-left: 15px; font-size: 12px; color: #0e2d5f">
          阻抗信息
          <a-tooltip slot="extra" placement="topLeft" title="增加一行" arrow-point-at-center>
            <a-icon type="plus" @click.stop="handleAdd" />
          </a-tooltip>
        </span>
      </div>
      <div class="footer">
        <a-collapse
          v-model="activeKey"
          expand-icon-position="right"
          :expandIcon="expandIcon1"
          @change="panelClick"
          :style="{ height: innerHeight - tableHeight - 180 + 'px', width: '100%' }"
        >
          <a-collapse-panel key="1" class="head-menu">
            <impedance-tab
              :seleDataZk="seleDataZk"
              :someList="someList"
              :data="dataSource"
              ref="impedance"
              @inverseCount="inverseCount"
              @insert1="insert1"
              :laminsert="laminsert"
              :bcdata="bcdata"
            />
          </a-collapse-panel>
        </a-collapse>
      </div>
      <div v-if="dataSaveclick" style="width: 870px">
        <report-info
          :reportType="false"
          :showMaterial="SaveMaterial"
          :showImpedancediagram="showImpedancediagram"
          :laminationData="reportData"
          :factory1="factory1"
          :joinFactoryId="joinFactoryId"
          ref="report1"
          :ttype="'down'"
        />
      </div>
      <div v-if="dataSaveclick" style="width: 870px">
        <report-info
          :reportType="false"
          :showMaterial="SaveMaterial"
          :showImpedancediagram="showImpedancediagram"
          :laminationData="reportData1"
          :factory1="factory1"
          :joinFactoryId="joinFactoryId"
          ref="report2"
          :ttype="'down'"
        />
      </div>
      <!--下载报表选择-->
      <a-modal
        title="下载报表参数选择"
        :visible="DownloadPopup"
        @cancel="reportHandleCancel"
        :width="400"
        ok-text="下载"
        @ok="DownloadPopupOk"
        cancel-text="取消"
        class="popupstyle"
        centered
        destroyOnClose
      >
        <a-row>
          <a-col :span="10">
            <a-form-model-item label="阻抗模型" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
              <a-checkbox v-model="reportdata.Impedancemodel" />
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="阻抗信息" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
              <a-checkbox v-model="Impedanceinformation" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="20">
            <a-form-model-item label="报表类型" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
              <a-select v-model="reportdata.Reporttype">
                <a-select-option v-for="(item, index) in reportlist" :value="item.value" :key="index">{{ item.text }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-modal>
      <!--pp弹窗-->
      <a-modal title="PP参数选择" :visible="PPVisible1" @cancel="handleCancel" :dialog-style="{ top: '200px' }" destroyOnClose>
        <template slot="footer">
          <a-button @click="submitPP">提交</a-button>
        </template>
        <pp-info-modal
          :ppList="ppList"
          :loading="ppTableLoading"
          :dataSourcePP="dataSourcePP"
          :ppId="ppId"
          :ppChange="ppChange"
          @ppTableFiltet="ppTableFiltet"
          @rcChange="rcChange"
          @PPChange="PPChange"
        />
      </a-modal>
      <!--报表弹窗 -->
      <a-modal
        :visible="modelVisible"
        @cancel="reportHandleCancel"
        destroyOnClose
        :maskClosable="false"
        :width="800"
        :dialog-style="{ top: '20px' }"
        :footer="null"
        centered
      >
        <!-- :class="shower ? '' : 'hide'" -->
        <template v-slot:title>
          <div class="titleBox">
            <a-button :class="!reportType ? 'btnStyle' : ''" @click="onCustomButtonClick" style="margin-left: 10px">中文</a-button>
            <a-button :class="reportType ? 'btnStyle' : ''" @click="onCustomButtonClick1" style="margin-left: 10px">英文</a-button>
            <a-checkbox v-model="showMaterial" style="margin: 0 5px 0 10px"></a-checkbox>物料名称
            <a-checkbox v-model="showImpedancediagram" style="margin: 0 5px 0 10px"></a-checkbox>阻抗模型
            <a-button @click="handleOk" class="btnStyle" style="float: right; right: 20px">下载PDF</a-button>
            <a-button @click="handleOkimg" class="btnStyle" style="float: right; right: 30px">下载图片</a-button>
          </div>
        </template>
        <report-info
          :reportType="reportType"
          :showMaterial="showMaterial"
          :showImpedancediagram="showImpedancediagram"
          :laminationData="reportData"
          :factory1="factory1"
          :joinFactoryId="joinFactoryId"
          ref="report"
        />
      </a-modal>

      <a-modal
        title="模板参数"
        :visible="templateModelVisible"
        @cancel="reportHandleCancel"
        @ok="templateHandleOk"
        ok-text="调出"
        destroyOnClose
        :maskClosable="false"
      >
        <a-form-model :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :model="templateForm" :rules="rules" ref="ruleForm">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="板材型号" prop="boardType">
                <a-select v-model="templateForm.boardType" show-search option-filter-prop="children" :filter-option="filterOption">
                  <a-select-option v-for="item in boardTypeList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="PP 型号" prop="ppType">
                <a-select v-model="templateForm.ppType">
                  <a-select-option v-for="item in ppTypeList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="内层铜厚" prop="innerCu">
                <a-select v-model="templateForm.innerCu">
                  <a-select-option value="0.5">0.5</a-select-option>
                  <a-select-option value="1">1</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="板厚" prop="finishBoardThickness">
                <a-select v-model="templateForm.finishBoardThickness">
                  <a-select-option value="0.6">0.6</a-select-option>
                  <a-select-option value="0.8">0.8</a-select-option>
                  <a-select-option value="1.0">1.0</a-select-option>
                  <a-select-option value="1.2">1.2</a-select-option>
                  <a-select-option value="1.6">1.6</a-select-option>
                  <a-select-option value="2.0">2.0</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="层数" prop="layers">
                <a-input-number v-model="templateForm.layers" :min="0" :precision="0" style="width: 100%" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-modal>
      <a-modal
        title="叠层"
        :visible="dataVisibleTemplate"
        @cancel="TemplateCancel"
        @ok="TemplatehandleOk2"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
      >
        <self-support-stack-info
          ref="SelfSupportStack"
          :stackListData="stackListData"
          :boardTypeList="boardTypeList"
          :ppTypeList="ppTypeList"
          :templateForm="templateForm"
          :cuListData="cuListData"
        />
      </a-modal>
      <!--自动叠层 -->
      <a-modal
        title="自动叠层"
        :visible="autostackVisible"
        @cancel="autostackHandleCancel"
        @ok="okBtn"
        destroyOnClose
        :maskClosable="false"
        :width="1500"
        centered
      >
        <auto-stack
          :laminationData="autostackinfo"
          :boardTypeList="boardTypeList"
          ref="autostack"
          @ConfirmOverlay="ConfirmOverlay"
          @getInfoClick="getInfoClick"
          :selectData="selectData"
          :impedanceTab="impedanceTab"
          :joinFactoryId="joinFactoryId"
        />
      </a-modal>
      <a-modal
        title="数据已保存，但有以下提示内容"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">知道了</a-button>
        </template>
        <div style="height: 200px; overflow-y: auto">
          <div v-for="(ite, index) in message" :key="index">
            <p>{{ ite }}</p>
          </div>
        </div>
      </a-modal>
    </a-spin>
  </div>
</template>
<script>
import db from "@/utils/db";
import { coreTypes, ppTypes } from "@/services/gongju/stackUp";
import {
  construction,
  coreList,
  getInpedanceData,
  getTemplateData,
  getTextOrJson,
  impedanceCount,
  impoutreportinfo,
  impedanceInverseCount,
  ozList,
  ppInfo,
  ppTable,
  saveData,
  settensilecoef,
  stackcalcv3,
  stackcalc,
  seleCU,
  seleZK,
  getTemplateData4M01,
  TemplateCu,
  TemplateList,
  calcStackUpCu,
  setautostackinfov3,
  gbList,
  stackreport,
  linebcvalue,
  stackiMP,
  uploadstackimpall,
} from "@/services/impedance";
import { mapGetters, mapState, mapMutations } from "vuex";
import BasicInfo from "@/pages/gongju/impedance/components/BasicInfo";
import LaminationInfo from "@/pages/gongju/impedance/components/LaminationInfo";
import FooterAction from "@/pages/gongju/impedance/components/FooterAction";
import PpInfoModal from "@/pages/gongju/impedance/components/PpinfoModal";
import ImpedanceTab from "@/pages/gongju/impedance/components/ImpedanceTab";
import ReportInfo from "@/pages/gongju/impedance/components/ReportInfo";
import AutoStack from "@/pages/gongju/impedance/components/AutoStack";
import SelfSupportStackInfo from "@/pages/gongju/impedance/components/SelfSupportStackInfo.vue";
import { qutoConfig } from "@/services/projectIndicate";
import Cookie from "js-cookie";
const columns = [
  {
    dataIndex: "index",
    slots: { title: "customTitle" },
    key: "index",
    width: 35,
    align: "center",
    scopedSlots: { customRender: "index" },
    // customRender: (text,record,index) => `${index+1}`,
    className: "index_",
    ellipsis: true,
  },
  {
    title: "层号",
    key: "stackUpLayerNo_",
    scopedSlots: { customRender: "stackUpLayerNo_" },
    width: 45,
    align: "center",
    className: "material_bg",
    ellipsis: true,
  },
  {
    title: "物料",
    key: "stackUpMTR_",
    scopedSlots: { customRender: "stackUpMTR_" },
    width: 60,
    align: "center",
    className: "material_bg",
    ellipsis: true,
  },
  {
    title: "型号",
    key: "stackUpMTRType_",
    scopedSlots: { customRender: "stackUpMTRType_" },
    width: 115,
    align: "center",
    className: "material_bg",
    ellipsis: true,
  },
  {
    //title: "介质",
    key: "stackUpMTRFoil_",
    scopedSlots: { customRender: "stackUpMTRFoil_" },
    slots: { title: "customTitle" },
    width: 165,
    align: "center",
    ellipsis: true,
    className: "material_bg",
  },
  {
    title: "含铜",
    key: "stackUpCoreDS_",
    scopedSlots: { customRender: "stackUpCoreDS_" },
    width: 40,
    className: "M_bg",
    align: "center",
    ellipsis: true,
  },
  {
    title: "水印",
    key: "waterMark_",
    scopedSlots: { customRender: "waterMark_" },
    width: 40,
    className: "M_bg",
    align: "center",
    ellipsis: true,
  },
  {
    title: "物料厚度(mil)",
    dataIndex: "stackUpThichnessORG_",
    width: 100,
    align: "center",
    className: "M_bg",
    ellipsis: true,
  },

  {
    title: "成品厚度(mil)",
    dataIndex: "stackUpThichnessMIL_",
    width: 100,
    align: "center",
    className: "M_bg",
    ellipsis: true,
  },
  {
    title: "成品厚度(mm)",
    // dataIndex: "stackUpThichnessMM_",
    width: 100,
    align: "center",
    className: "M_bg",
    ellipsis: true,
    scopedSlots: { customRender: "stackUpThichnessMM_" },
  },

  {
    title: "残铜率",
    key: "stackUpCTLMI_",
    scopedSlots: { customRender: "stackUpCTLMI_" },
    width: 80,
    className: "M_bg",
    align: "center",
    ellipsis: true,
  },
  {
    title: "T/B",
    key: "stackUpCUTB_",
    scopedSlots: { customRender: "stackUpCUTB_" },
    width: 55,
    align: "center",
    ellipsis: true,
  },
  {
    title: "DK",
    dataIndex: "stackUpDK_",
    width: 70,
    className: "M_bg",
    align: "center",
    ellipsis: true,
  },
  {
    title: "大料尺寸",
    // dataIndex: "stackUpPNLSize_",
    scopedSlots: { customRender: "stackUpPNLSize_" },
    width: 80,
    className: "M_bg",
    align: "center",
    ellipsis: true,
  },
  {
    title: "CuMin",
    key: "stackUpCuMin4T1_",
    scopedSlots: { customRender: "CuMin" },
    width: 50,
    align: "center",
    ellipsis: true,
  },
  {
    title: "CuMax",
    key: "stackUpCuMax4T1_",
    scopedSlots: { customRender: "CuMax" },
    width: 50,
    align: "center",
    ellipsis: true,
  },
  {
    title: "T1",
    dataIndex: "stackUpT1_",
    width: 55,
    align: "center",
    ellipsis: true,
  },
  {
    title: "NY",
    dataIndex: "ppThickness4NY_",
    width: 30,
    align: "center",
    ellipsis: true,
  },
  {
    title: "铜箔类型",
    key: "stackUpCuType_",
    scopedSlots: { customRender: "oz" },
    width: 80,
    align: "center",
    ellipsis: true,
  },
];
export default {
  name: "StackImpedance",
  components: {
    ReportInfo,
    ImpedanceTab,
    FooterAction,
    PpInfoModal,
    LaminationInfo,
    BasicInfo,
    SelfSupportStackInfo,
    AutoStack,
  },
  data() {
    return {
      reportlist: [
        { value: 0, text: "英文" },
        { value: 1, text: "中文" },
      ],
      tagData: [],
      NYdata: {}, // NY计算数据
      showerror: false,
      indlist: "",
      dataSaveclick: false,
      laminsert: false,
      shower: false,
      iminsert: false,
      timer: null,
      foldedornot: true,
      reportType: false,
      factory1: "",
      impedanceTab: [],
      boardTypeList: [],
      ppTypeList: [],
      ozListData: [],
      coreListData: [],
      GBListData: [],
      ppList: [],
      PPVisible: false,
      PPVisible1: false,
      ppTableLoading: false,
      DownloadPopup: false,
      reportdata: {
        Impedancemodel: false,
        Reporttype: 1,
      },
      Impedanceinformation: false,
      dataVisibleTemplate: false,
      ppTables: [],
      dataSourcePP: [],
      ppId: "",
      ddta: [],
      numbers: [],
      parenNumbers: [],
      upstack: false,
      valData: [],
      medium: "",
      dataSource: [], // 叠层数据
      columns,
      seleDataZk: [], // 阻抗类型列表
      ppIndex: "", // 双击PP后在列表的index
      someList: [],
      stackListData: {},
      pressingThicknessBg: "", // 板厚背景色
      collapse: true,
      innerHeight: window.innerHeight, // 获取窗口高度
      tableHeight: window.innerHeight / 2 + 100, // 设置第一个div的高度为窗口高度的一半
      modelVisible: false, // 报表弹窗,
      PPmergeData: [],
      laminationInfoLoading: false, // 调出结构的loading
      reportData: {}, // 报表数据
      reportData1: {}, //报表导出图片时物料参数与厚度单独取另外的字段
      autostackinfo: {}, //自动叠层弹窗获取入参
      spinning: false,
      isGlueFill: false, // 是否填胶
      templateModelVisible: false, // 调出模板弹窗开关
      autostackVisible: false, //自动叠层弹窗
      templateForm: {
        finishBoardThickness: "",
        layers: "",
        innerCu: "",
        ppType: "",
        boardType: "",
      },
      rules: {
        finishBoardThickness: [{ required: true, message: "请填写板厚", trigger: "change" }],
        innerCu: [{ required: true, message: "请填写内层铜厚", trigger: "change" }],
        layers: [{ required: true, message: "请填写层数", trigger: "blur" }],
        ppType: [{ required: true, message: "请选择PP型号", trigger: "change" }],
        boardType: [{ required: true, message: "请选择板材型号", trigger: "change" }],
      },
      cuListData: [],
      TemplateData: "",
      copyPpCode_: "",
      btnData: [],
      selectData: {},
      activeKey: ["1"],
      bcdata: [],
      ppChange: [],
      selectPPType_: "",
      dataVisible: false,
      message: [],
      joinFactoryId: "",
      okBtnFlg: false,
      showMaterial: false,
      SaveMaterial: false,
      showImpedancediagram: false,
      isCtrlPressed: false,
      flg1: 0,
      moveKey: false,
    };
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.upstack) {
      next(false);
      this.$message.warning("当前叠层图未上传成功 请稍等");
    } else if (this.editstatus) {
      if (confirm("当前数据未保存,请确认是否需要进行跳转页面")) {
        next();
        this.setedit(false);
      } else {
        next(false);
      }
    } else {
      next();
      this.setedit(false);
    }
  },
  computed: {
    ...mapGetters({ form: "categoryForm" }),
    ...mapState({ _drillHoleTableData: state => state.impedance._drillHoleTableData }),
    ...mapState("account", ["user"]),
    ...mapState("setting", ["editstatus"]),
    // ...mapState({'_drillHoleTableData': state => state.impedance._drillHoleTableData, 'joinFactoryId': state => state.impedance._joinFactoryId})
  }, // form data
  created() {
    // await this.getBoardTypeList();
    // await this.getPPTypeList();
    // if(this.$route.query.href && this.$route.query.href=="projectMake"){
    //   this.$store.commit('factoryChange', '216')
    // }
    this.setedit(false);
    if (this.$route.query.category == 1) {
      this.form.isPressure = true;
    }
    if (this.$route.query.joinFactoryId) {
      this.joinFactoryId = this.$route.query.joinFactoryId;
    } else {
      this.joinFactoryId = this.user.factoryId;
    }
    if (this.joinFactoryId == "37" || this.joinFactoryId == "57") {
      this.showMaterial = true;
      this.SaveMaterial = true;
      this.showImpedancediagram = false;
    } else if (this.joinFactoryId == "12" || this.joinFactoryId == "67") {
      this.showMaterial = true;
      this.SaveMaterial = true;
      this.showImpedancediagram = true;
    } else if (this.joinFactoryId == "38") {
      this.showMaterial = false;
      this.SaveMaterial = true;
      this.showImpedancediagram = true;
    } else if (this.joinFactoryId == "58" || this.joinFactoryId == "59") {
      this.showMaterial = false;
      this.SaveMaterial = true;
      this.showImpedancediagram = false;
    } else {
      this.showMaterial = false;
      this.SaveMaterial = false;
      this.showImpedancediagram = false;
    }
    this.getlinebc();
    this.getPPinfoList();
    this.getSeleDataZk();
    this.getSelectCu();
    this.factoryChange();
    this.getTemplateCu();
    this.getCore();
    //this.GetQutoConfig()
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("mouseup", this.mouseup, true);
  },
  watch: {
    _drillHoleTableData: {
      handler(newV, oldV) {
        let _flag = false;
        if (newV.length > 0) {
          newV.forEach(item => {
            if (item.startLayer && item.endLayer) {
              if (item.startLayer == 1 && item.endLayer == this.form.layers) {
                this.isGlueFill = false;
              } else {
                let left = item.drillName.slice(0, 2).toLowerCase();
                if (left == "dr" || left == "ls") {
                  _flag = true;
                  this.isGlueFill = true;
                }
              }
            }
          });
        } else {
          _flag = false;
          this.isGlueFill = false;
        }
        this.addColumn(_flag);
      },
    },
    tableHeight: {
      handler(newV, oldV) {
        this.textWidthChange1();
      },
    },
  },
  methods: {
    ...mapMutations("setting", ["setedit"]),
    Drilldelttype(data) {
      if (data.length) {
        data.forEach(target => {
          let left = target.drillName.slice(0, 2);
          let right = target.drillName.slice(-2);
          if (target.drillName == "2nd") {
            target.drillType4Stack = "2";
          } else if (left == "dr" && target.startLayer == "1" && target.endLayer == this.form.layers) {
            target.drillType4Stack = "d";
          } else if (target.drillName == "drl") {
            target.drillType4Stack = "d";
          } else if (left == "dr" && this.form.layers == 0) {
            target.drillType4Stack = "d";
          } else if (left == "dr" && right != "ks") {
            target.drillType4Stack = "m";
          } else if (left == "ld") {
            target.drillType4Stack = "l";
          } else if (left == "ls") {
            target.drillType4Stack = "l";
          } else {
            target.drillType4Stack = null;
          }
        });
        return data;
      } else {
        return [];
      }
    },
    assignment() {
      for (let i = 0; i < this.$refs.impedance.impedanceTabData.length; i++) {
        this.$refs.impedance.controlChange(this.$refs.impedance.impedanceTabData[i], i, "ozchange");
      }
    },
    getlinebc() {
      if (this.$route.query.joinFactoryId && this.$route.query.pdctno) {
        linebcvalue(this.$route.query.joinFactoryId, this.$route.query.pdctno).then(res => {
          if (res.code) {
            this.bcdata = res.data;
          }
        });
      }
    },
    insert1(ins, type) {
      if (type == "1" && ins == true) {
        this.laminsert = true;
        this.iminsert = false;
      } else if (type == "2" && ins == true) {
        this.laminsert = false;
        this.iminsert = true;
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    mouseup(e) {
      this.moveKey = false;
    },

    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "112") {
        this.count();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "113") {
        this.countImpedance();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "114") {
        this.dataSave();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    // 获取下拉选择
    GetQutoConfig() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("selectData"));
      if (data && token && data.token == token) {
        this.selectData = data.data; //本地缓存
      } else {
        qutoConfig().then(res => {
          if (res.code) {
            this.selectData = JSON.parse(res.data);
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              localStorage.setItem("selectData", JSON.stringify({ data: this.selectData, token }));
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    async factoryChange() {
      this.spinning = true;
      const token = Cookie.get("Authorization");
      const data3 = JSON.parse(localStorage.getItem("boardTypeList"));
      if (
        data3 &&
        token &&
        data3.filter(item => {
          return item.joinFactoryId == this.joinFactoryId;
        }).length
      ) {
        for (var a3 = 0; a3 < data3.length; a3++) {
          if (data3[a3].token == token && data3[a3].joinFactoryId == this.joinFactoryId) {
            this.boardTypeList = data3[a3].data; //本地缓存
          }
        }
        // this.boardTypeList =  data3.data;
      } else {
        await coreTypes(this.joinFactoryId, 0).then(res => {
          if (res.code) {
            this.boardTypeList = res.data;
            let token = Cookie.get("Authorization");
            // if(res.data.length){
            // localStorage.setItem('boardTypeList', JSON.stringify({data: this.boardTypeList, token,joinFactoryId:this.joinFactoryId}))
            // }
            if (res.data.length) {
              let arr = [];
              if (data3 == null) {
                arr.push({ data: res.data, token, joinFactoryId: this.joinFactoryId });
                localStorage.setItem("boardTypeList", JSON.stringify(arr)); //本地缓存
              } else {
                data3.push({ data: res.data, token, joinFactoryId: this.joinFactoryId });
                localStorage.setItem("boardTypeList", JSON.stringify(data3)); //本地缓存
              }
            }
          }
        });
      }
      // this.ozListData
      const data = JSON.parse(localStorage.getItem("ozListData"));
      if (
        data &&
        token &&
        data.filter(item => {
          return item.joinFactoryId == this.joinFactoryId;
        }).length
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].joinFactoryId == this.joinFactoryId) {
            this.ozListData = data[a].data; //本地缓存
          }
        }
      } else {
        await ozList(this.joinFactoryId).then(res => {
          if (res.code) {
            this.ozListData = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data == null) {
                arr.push({ data: res.data, token, joinFactoryId: this.joinFactoryId });
                localStorage.setItem("ozListData", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: res.data, token, joinFactoryId: this.joinFactoryId });
                localStorage.setItem("ozListData", JSON.stringify(data)); //本地缓存
              }
            }
          }
        });
      }
      // this.ppTypeList
      const data2 = JSON.parse(localStorage.getItem("ppTypeList"));
      if (
        data2 &&
        token &&
        data2.filter(item => {
          return item.joinFactoryId == this.joinFactoryId;
        }).length
      ) {
        for (var a2 = 0; a2 < data2.length; a2++) {
          if (data2[a2].token == token && data2[a2].joinFactoryId == this.joinFactoryId) {
            this.ppTypeList = data2[a2].data; //本地缓存
          }
        }
      } else {
        await ppTypes(this.joinFactoryId).then(res => {
          if (res.code) {
            this.ppTypeList = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data2 == null) {
                arr.push({ data: res.data, token, joinFactoryId: this.joinFactoryId });
                localStorage.setItem("ppTypeList", JSON.stringify(arr)); //本地缓存
              } else {
                data2.push({ data: res.data, token, joinFactoryId: this.joinFactoryId });
                localStorage.setItem("ppTypeList", JSON.stringify(data2)); //本地缓存
              }
            }
          }
        });
      }
      // this.GBListData
      const data4 = JSON.parse(localStorage.getItem("GBListData"));
      if (
        data4 &&
        token &&
        data4.filter(item => {
          return item.joinFactoryId == this.joinFactoryId;
        }).length
      ) {
        for (var a4 = 0; a4 < data4.length; a4++) {
          if (data4[a4].token == token && data4[a4].joinFactoryId == this.joinFactoryId) {
            this.GBListData = data4[a4].data; //本地缓存
          }
        }
      } else {
        await gbList(this.joinFactoryId).then(res => {
          if (res.code) {
            this.GBListData = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              arr.push({ data: res.data, token, joinFactoryId: this.joinFactoryId });
              if (this.getStringSizeInBytes(JSON.stringify(localStorage)) + this.getStringSizeInBytes(JSON.stringify(arr)) < 5) {
                localStorage.setItem("GBListData", JSON.stringify(arr)); //本地缓存
              }
              // if(data4 == null){
              //   arr.push({data: res.data, token,joinFactoryId:this.joinFactoryId})
              //   localStorage.setItem('GBListData', JSON.stringify(arr));//本地缓存
              // }else{
              //   data4.push({data: res.data, token,joinFactoryId:this.joinFactoryId})
              //   localStorage.setItem('GBListData', JSON.stringify(data4));//本地缓存
              // }
            }
          }
        });
      }

      // this.ozListData = await ozList(this.joinFactoryId).then(res=> res.data);
      // this.coreListData = await coreList(this.joinFactoryId).then(res=> res.data);
      // this.ppTypeList = await ppTypes(this.joinFactoryId).then(res=> res.data);
      // this.boardTypeList = await coreTypes(this.joinFactoryId,0).then(res=> res.data);
      this.spinning = false;
    },
    async getCore() {
      const factory = Number(this.joinFactoryId);
      const token = Cookie.get("Authorization");
      let mode = Number(this.$route.query.mode) || 1;
      let coreType = mode == 1 ? "coreListDataPro" : "coreListDataMkt";
      this.spinning = true;
      try {
        // 尝试从IndexedDB获取
        const cached = await db.getItem(coreType, { token, factory, mode });

        if (cached && cached.data) {
          this.coreListData = cached.data;
          this.spinning = false;
          return;
        }
        // 无缓存则请求接口
        const res = await coreList(factory, mode);
        if (res.code) {
          this.coreListData = res.data;

          // 存储到IndexedDB
          if (JSON.stringify(this.coreListData) !== "{}") {
            await db.setItem(coreType, {
              token,
              factory,
              mode,
              data: this.coreListData,
            });
          }
          this.spinning = false;
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      } catch (error) {
        console.error("IndexedDB操作失败:", error);
        this.spinning = false;
      }
    },
    getStringSizeInBytes(str) {
      // 使用UTF-8编码计算字符串的字节长度
      let totalBytes = new Blob([str]).size;

      // 将字节长度转换为兆字节（MB）
      let sizeInMB = totalBytes / (1024 * 1024);

      // 返回结果
      return sizeInMB;
    },
    async getLayerChange() {
      this.boardTypeList = await coreTypes(this.joinFactoryId, Number(this.form.layers) || 0).then(res => (res.code ? res.data : []));
    },
    pressingThicknessBgChange() {
      this.pressingThicknessBg = "";
    },
    getPPinfoList() {
      ppInfo(this.joinFactoryId).then(res => {
        if (res.code) {
          this.ppTables = res.data;
        }
      });
    },
    getStructure() {
      let _flag = true;
      let that = this;
      if (this.form.pdctno == "" || this.form.layers == "" || this.form.finishBoardThickness == "") {
        this.$error({
          title: "参数错误",
          content: "请填写完整参数...",
        });
        return;
      }
      if (this.dataSource.length > 0) {
        _flag = false;
        this.$confirm({
          title: "已有结构数据你是否要覆盖数据?",
          onOk() {
            that.getStructureHttp();
          },
          onCancel() {
            return;
          },
          class: "test",
        });
      }
      if (_flag) {
        this.getStructureHttp();
      }
    },
    getStructureHttp() {
      let routeList = this.$route.query;
      this.laminationInfoLoading = true;
      const params = JSON.parse(JSON.stringify(this.form));
      params["boardType"] = this.labelOrValue(this.boardTypeList, params["boardType"]);
      params["ppType"] = this.labelOrValue(this.ppTypeList, params["ppType"]);
      params["stackUpDrills"] = this._drillHoleTableData;
      if (routeList.InCopperThickness) {
        params["inCopperThickness"] = routeList.InCopperThickness;
      }
      if (routeList.OutCopperThickness) {
        params["outCopperThickness"] = routeList.OutCopperThickness;
      }
      if (routeList.inCu) {
        params["inCu"] = routeList.inCu;
      }
      if (routeList.outCu) {
        params["outCu"] = routeList.outCu;
      }

      if (params.pressingThickness == "") {
        delete params.pressingThickness;
      }
      // params.stackUpDrills= params.stackUpDrills.filter(item=>{return item.drillName && item.startLayer && item.endLayer})
      construction(params)
        .then(res => {
          if (res.code == 1) {
            res.data.forEach((item, index) => {
              // item.index=index
              item.stackUpPPFillGlue_ = item.stackUpPPFillGlue_ ? item.stackUpPPFillGlue_ : "";
            });
            res.data.forEach((item, index) => {
              item["tdFlag_"] = "true";
            });
            this.dataSource = res.data;
            this.laminationInfoLoading = false;
            this.$message.success("调出结构OK");
            if (this.form.layers == 2 && !this.form.pressingThickness) {
              this.count("", "countok");
            }
          } else {
            this.$message.info(res.message);
            this.laminationInfoLoading = false;
          }
        })
        .catch(error => {
          this.laminationInfoLoading = false;
        });
    },
    getSeleDataZk() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("seleDataZk"));
      if (data && token && data.token == token) {
        this.seleDataZk = data.data;
      } else {
        seleZK().then(res => {
          if (res.code) {
            this.seleDataZk = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              localStorage.setItem("seleDataZk", JSON.stringify({ data: this.seleDataZk, token }));
            }
          }
        });
      }
    },
    getSelectCu() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("someList"));
      if (data && token && data.token == token && data.joinFactoryId == this.joinFactoryId) {
        this.someList = data.data;
      } else {
        seleCU(this.joinFactoryId).then(res => {
          if (res.code) {
            this.someList = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              localStorage.setItem("someList", JSON.stringify({ data: this.someList, token, joinFactoryId: this.joinFactoryId }));
            }
          }
        });
      }
    },
    handleCancel(e) {
      this.PPVisible = false;
      this.$refs.laminationInfo.ppstyle(this.PPVisible);
    },
    reportHandleCancel(e) {
      this.modelVisible = false;
      this.templateModelVisible = false;
      this.dataVisible = false;
      this.reportType = false;
      this.DownloadPopup = false;
    },
    autostackHandleCancel(e) {
      this.autostackVisible = false;
    },
    TemplateCancel(e) {
      this.dataVisibleTemplate = false;
    },
    showModel(payload) {
      this.selectPPType_ = payload.record.stackUpMTRType_;
      let arr = this.ppTables.filter(item => {
        return item.ppType_ == payload.record.stackUpMTRType_;
      });
      for (var a = 0; a < arr.length; a++) {
        this.ppChange.push(arr[a].pP_);
      }
      this.ppChange = this.ppChange.filter((item, index, array) => {
        return array.indexOf(item) === index;
      });
      this.ppIndex = payload.index;
      if (this.ppTypeList.length && payload.record.stackUpMTRType_ != "") {
        this.ppId = this.ppTypeList.find(item => {
          return item.text == payload.record.stackUpMTRType_;
        }).valueMember;
      }
      this.medium = payload.record.stackUpMTRFoil_;
      if (this.medium) {
        this.numbers = this.medium.match(/([a-zA-Z]*\d+)(?=\()/g) || [];
        this.parenNumbers = this.medium.match(/\((\d+(\.\d+)?)\)/g).map(match => match.slice(1, -1));
        this.ppTableFiltet();
        for (var i = 0; i < this.parenNumbers.length; i++) {
          this.rcChange({ val: this.parenNumbers[i], index: i });
        }
      } else {
        this.dataSourcePP = [];
      }
      this.ppTableLoading = true;
      ppTable(payload.record.stackUpMTRType_, this.joinFactoryId).then(res => {
        this.ppList = res.data;
        this.ppTableLoading = false;
      });
      this.PPVisible = true;
      if (this.innerHeight - this.tableHeight - 221 >= 137) {
        this.$refs.laminationInfo.ppheighty = 314;
      } else {
        this.$refs.laminationInfo.ppheighty = 445 - (this.innerHeight - this.tableHeight - 221);
      }
      this.$refs.laminationInfo.ppstyle(this.PPVisible);
    },
    submitPP() {
      let val = "";
      let ORG = 0;
      let MM_ = 0;
      let rKey = "";
      let ny = 0;
      let bb = 0;
      let dk = 0;
      this.dataSourcePP = this.dataSourcePP.filter(item => {
        return item.pP_ != "";
      });
      this.dataSourcePP.forEach(res => {
        val = val + "+" + res.pP_ + "(" + res.rC_ + ")";
        bb = bb + res.ppThickness4NY_;
        ORG += res.thicknesS_;
        MM_ += res.thicknesS_ * 0.0254;
        dk += res.ppdK_;
        if (this.dataSourcePP.length > 1) {
          if (rKey == "") {
            rKey = rKey + `${res.rKey_}`;
          } else {
            rKey = rKey + `+${res.rKey_}`;
          }
        } else {
          rKey = rKey + `${res.rKey_}`;
        }
      });
      let boxArray = this.dataSource.map(item => item.stackUpMTR_);
      let ppIndexArr_ = [];
      boxArray.forEach((item, index) => {
        if (item == "PP") {
          ppIndexArr_.push(index);
        }
      });
      let symmetricIndex = ppIndexArr_[ppIndexArr_.length - 1 - ppIndexArr_.indexOf(this.ppIndex)];
      if (symmetricIndex < this.dataSource.length / 2) {
        symmetricIndex = "";
      }
      val = val.substr(1);
      this.dataSource = this.dataSource.map((res, index) => {
        if (res.stackUpMTR_ == "PP" && index == this.ppIndex) {
          res.stackUpMTRFoil_ = val;
          res.stackUpDK_ = (dk / this.dataSourcePP.length).toFixed(2) + ""; //
          res.stackUpThichnessORG_ = this.getFloat(ORG, 4);
          res.stackUpThichnessMIL_ = this.getFloat(ORG, 4); // 两个不同的字段值一样
          res.stackUpThichnessMM_ = this.getFloat(MM_, 4);
          res.ppThickness4Gllass_ = bb;
          res.ppThickness4NY_ = "";
          res.rKey_ = rKey;
        }
        if (
          res.stackUpMTR_ == "PP" &&
          index === symmetricIndex &&
          this.dataSource[this.ppIndex].stackUpMTRType_ == this.dataSource[symmetricIndex].stackUpMTRType_
        ) {
          res.stackUpMTRFoil_ = val.split("+").reverse().join("+");
          res.stackUpDK_ = (dk / this.dataSourcePP.length).toFixed(2) + ""; //
          res.stackUpThichnessORG_ = this.getFloat(ORG, 4);
          res.stackUpThichnessMIL_ = this.getFloat(ORG, 4); // 两个不同的字段值一样
          res.stackUpThichnessMM_ = this.getFloat(MM_, 4);
          res.ppThickness4Gllass_ = bb;
          res.ppThickness4NY_ = "";
          res.rKey_ = rKey.split("+").reverse().join("+");
        }
        return res;
      });
      this.$refs.laminationInfo.ppstyle(this.PPVisible);
      this.dataSourcePP.push({
        changes: [],
        dkInner_: null,
        hF_: false,
        isShowInStackUp_: false,
        pP_: "",
        ppThickness4NY_: null,
        ppTypeCodes_: null,
        ppType_: "",
        ppdK_: null,
        rC_: null,
        rCindex_: null,
        thicknesS_: null,
        rKey_: null,
      });
      //this.dataSourcePP = []
    },
    changeDrillHoleTableData(record) {
      this.indlist = record.startLayer + "+" + record.endLayer;
    },
    formChange(payload) {
      if (payload.name == "board") {
        let arr = this.boardTypeList.find(item => {
          return item.valueMember == payload.value;
        });
        let ppCode_ = "";
        if (arr) {
          ppCode_ = arr.ppCode;
        }
        this.$store.commit("changeInfo", { ppType: ppCode_ ? ppCode_.toString() : "" });
      }

      if (this.dataSource.length > 0) {
        this.dataSource.forEach(item => {
          if (payload.name == "board") {
            if (["Core", "GB", "金属基", "补强"].includes(item.stackUpMTR_)) {
              item.stackUpMTRType_ = this.labelOrValue(this.boardTypeList, payload.value);
              if (item.stackUpMTR_ == "GB") {
                item.stackUpMTRFoil_ = "";
              }
            } else {
              // let ppCode_ =  this.boardTypeList.find(item => {return item.valueMember == payload.value}).ppCode
              let arr = this.boardTypeList.find(item => {
                return item.valueMember == payload.value;
              });
              let ppCode_ = "";
              if (arr) {
                ppCode_ = arr.ppCode;
              }
              if (item.stackUpMTR_ != "OZ") {
                item.stackUpMTRType_ = this.labelOrValue(this.ppTypeList, ppCode_);
              } else {
                item.stackUpMTRType_ = "";
              }
              item.stackUpMTRFoil_ = ""; // form pp型号更改清空介质
            }
          } else {
            if (item.stackUpMTR_ == "PP") {
              item.stackUpMTRType_ = this.labelOrValue(this.ppTypeList, payload.value);
              item.stackUpMTRFoil_ = ""; // form pp型号更改清空介质
            }
          }
        });
      }
      this.$forceUpdate();
    },
    labelOrValue(data, val) {
      let newval_ = "";
      data.forEach(item => {
        if (item.valueMember == val) {
          newval_ = item.text;
        }
      });

      return newval_;
    },
    addColumn(val) {
      const flag = this.columns.some(item => item.key == "stackUpPPFillGlue_");
      if (!flag && val) {
        this.columns.splice(12, 0, {
          title: "填胶",
          key: "stackUpPPFillGlue_",
          scopedSlots: { customRender: "stackUpPPFillGlue_" },
          width: 40,
          align: "center",
        });
      }
      if (flag && !val) {
        this.columns.splice(12, 1);
      }
      this.$forceUpdate();
    },
    rcChange(payload, type) {
      let arr = this.ppTables.filter(item => {
        return item.ppType_ == this.selectPPType_ && item.rC_ == payload.val && item.pP_ == this.dataSourcePP[payload.index].pP_;
      });
      var dataA = this.dataSourcePP[payload.index];
      var dataB = arr[0];
      for (var a = 0; a < Object.keys(dataB).length; a++) {
        if (Object.keys(dataA).indexOf(Object.keys(dataB)[a]) >= 0) {
          dataA[Object.keys(dataB)[a]] = dataB[Object.keys(dataB)[a]];
        }
      }
      if (type == 1) {
        for (let index = 0; index < this.dataSourcePP.length; index++) {
          if (this.dataSourcePP[index].pP_ == payload.pP_ && payload.index <= index) {
            this.dataSourcePP[index].rC_ = this.dataSourcePP[payload.index].rC_;
            this.dataSourcePP[index].changes = this.dataSourcePP[payload.index].changes;
            this.dataSourcePP[index].dkInner_ = this.dataSourcePP[payload.index].dkInner_;
            this.dataSourcePP[index].excludeFactory = this.dataSourcePP[payload.index].excludeFactory;
            this.dataSourcePP[index].hF_ = this.dataSourcePP[payload.index].hF_;
            this.dataSourcePP[index].isShowInStackUp_ = this.dataSourcePP[payload.index].isShowInStackUp_;
            this.dataSourcePP[index].pP_ = this.dataSourcePP[payload.index].pP_;
            this.dataSourcePP[index].ppThickness4NY_ = this.dataSourcePP[payload.index].ppThickness4NY_;
            this.dataSourcePP[index].ppTypeCodes_ = this.dataSourcePP[payload.index].ppTypeCodes_;
            this.dataSourcePP[index].ppType_ = this.dataSourcePP[payload.index].ppType_;
            this.dataSourcePP[index].ppdK_ = this.dataSourcePP[payload.index].ppdK_;
            this.dataSourcePP[index].rCindex_ = this.dataSourcePP[payload.index].rCindex_;
            this.dataSourcePP[index].rKey_ = this.dataSourcePP[payload.index].rKey_;
            this.dataSourcePP[index].tG_ = this.dataSourcePP[payload.index].tG_;
            this.dataSourcePP[index].thicknesS_ = this.dataSourcePP[payload.index].thicknesS_;
            this.dataSourcePP[index].thicknesS_TOL_DN_ = this.dataSourcePP[payload.index].thicknesS_TOL_DN_;
            this.dataSourcePP[index].thicknesS_TOL_UP_ = this.dataSourcePP[payload.index].thicknesS_TOL_UP_;
            this.dataSourcePP[index].vendorCodes_ = this.dataSourcePP[payload.index].vendorCodes_;
            this.dataSourcePP[index].vendor_ = this.dataSourcePP[payload.index].vendor_;
            this.dataSourcePP[index].thicknessmM_ = this.dataSourcePP[payload.index].thicknessmM_;
          }
        }
      }
    },
    PPChange(payload) {
      for (let index = 0; index < this.dataSourcePP.length; index++) {
        const element = this.dataSourcePP[this.dataSourcePP.length - 1];
        if (element.pP_) {
          this.dataSourcePP.push({
            changes: [],
            dkInner_: null,
            hF_: false,
            isShowInStackUp_: false,
            pP_: "",
            ppThickness4NY_: null,
            ppTypeCodes_: null,
            ppType_: "",
            ppdK_: null,
            rC_: null,
            rCindex_: null,
            thicknesS_: null,
            rKey_: null,
          });
        }
      }

      let arr = this.ppTables.filter(item => {
        return item.ppType_ == this.selectPPType_ && item.pP_ == payload.val;
      });
      let Arry = [];
      for (var a = 0; a < arr.length; a++) {
        Arry.push(arr[a].rC_);
      }
      this.dataSourcePP[payload.index].changes = Arry.filter((item, index, array) => {
        return array.indexOf(item) === index;
      });
      this.dataSourcePP[payload.index].rC_ = this.dataSourcePP[payload.index].changes[0];
      this.rcChange({ val: this.dataSourcePP[payload.index].changes[0], index: payload.index });
    },
    ppTableFiltet(val) {
      this.dataSourcePP = [];
      this.PPmergeData = [];
      if (val) {
        this.valData = val.ppComb_.split("+");
      } else {
        this.valData = this.numbers;
      }
      const _obj = this.PPMerge(this.ppTables);
      this.valData.forEach(res => {
        var ppListData = _obj.find(item => {
          return item.pP_ == res;
        });
        ppListData["chind"] = ppListData.chind.filter(item => {
          return item.ppTypeCodes_ == this.ppId;
        });
        this.PPmergeData.push(ppListData);
      });
      var a = [];
      this.PPmergeData.forEach((item, idex) => {
        if (item.chind.length) {
          a.push(item.chind[0]);
          a[idex]["changes"] = item.chind.map(item => {
            return item.rC_;
          });
        }
      });

      let data_ = {
        changes: [],
        dkInner_: null,
        hF_: false,
        isShowInStackUp_: false,
        pP_: "",
        ppThickness4NY_: null,
        ppTypeCodes_: null,
        ppType_: "",
        ppdK_: null,
        rC_: null,
        rCindex_: null,
        thicknesS_: null,
        rKey_: null,
      };
      a.push(data_);
      this.dataSourcePP = JSON.parse(JSON.stringify(a));
    },
    PPMerge(arr) {
      let result = [];
      let obj = {};
      arr.forEach((item2, index2) => {
        if (!obj[item2.pP_]) {
          result.push({ pP_: item2.pP_, chind: [item2] });
          obj[item2.pP_] = true;
        } else {
          let curIndex = result.findIndex(val => val.pP_ == item2.pP_);
          result[curIndex].chind.push(item2);
        }
      });
      return result;
    },
    //当前行压合厚度
    currentLineFilter1(val, idx) {
      let data = val.split("+"),
        arr = [],
        ORG = 0;
      // if(data.length > 0){
      //   data.forEach(res=>{
      //     this.ppTables.find(item=>{
      //       if(res.indexOf(item.pP_)!= -1 &&this.dataSource[idx].stackUpMTRType_ == item.ppType_&& res.indexOf(item.rC_)!= -1){
      //         arr.push(item)
      //       }
      //     })
      //   })
      // }  2022.7.25 优化压合板厚
      let formData = JSON.parse(JSON.stringify(this.form));
      let ppCode_ = this.boardTypeList.find(item => {
        return item.valueMember == formData.boardType;
      }).ppCode;
      if (data.length > 0) {
        data.forEach(res => {
          this.ppTables.find(item => {
            //res.indexOf(item.pP_)!= -1 && ppCode_ == item.ppTypeCodes_ && res.indexOf(item.rC_)!= -1)
            if (res.indexOf(item.pP_) != -1 && ppCode_ == item.ppTypeCodes_) {
              arr.push(item);
            }
          });
        });
      }
      arr.forEach(res => {
        ORG += res.thicknesS_;
      });
      return ORG;
    },
    currentLineFilter(val, idx) {
      let data = val.split("+"),
        arr = [],
        ORG = 0;
      // if(data.length > 0){
      //   data.forEach(res=>{
      //     this.ppTables.find(item=>{
      //       if(res.indexOf(item.pP_)!= -1 &&this.dataSource[idx].stackUpMTRType_ == item.ppType_&& res.indexOf(item.rC_)!= -1){
      //         arr.push(item)
      //       }
      //     })
      //   })
      // }  2022.7.25 优化压合板厚
      // let formData=JSON.parse(JSON.stringify(this.form))
      // let ppCode_ =  this.boardTypeList.find(item => {
      // return item.valueMember == formData.boardType}).ppCode
      if (data.length > 0) {
        data.forEach(res => {
          this.ppTables.find(item => {
            //res.indexOf(item.pP_)!= -1 && ppCode_ == item.ppTypeCodes_ && res.indexOf(item.rC_)!= -1)
            if (res == item.rKey_) {
              arr.push(item);
            }
          });
        });
      }
      arr.forEach(res => {
        ORG += res.thicknesS_;
      });
      return ORG;
    },
    ppprocessing() {
      //计算板厚后对称
      const jiezhiListName = this.dataSource.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      let ppListName = jiezhiListName.filter(item => {
        return item.value == "PP";
      });
      if (ppListName.length > 1) {
        var a = [];
        for (let index = 0; index < ppListName.length; index++) {
          a.push(ppListName[index].key);
        }
        var b = a.slice(0, Math.floor(ppListName.length / 2));
        var c = a.slice(-Math.floor(ppListName.length / 2));
        var arr = [...b, ...c];
        for (let i = 0; i < arr.length / 2; i++) {
          let A = this.areAllPartsIdentical(this.dataSource[arr[i]].stackUpMTRFoil_);
          let last = arr[arr.length - 1 - i];
          if (
            this.dataSource[last].stackUpMTRFoil_ == this.dataSource[arr[i]].stackUpMTRFoil_ &&
            this.dataSource[last].stackUpMTRType_ == this.dataSource[arr[i]].stackUpMTRType_ &&
            !A
          ) {
            this.$refs.laminationInfo.ppduic1 = true;
            this.dataSource[last].stackUpMTRFoil_ = this.dataSource[arr[i]].stackUpMTRFoil_.split("+").reverse().join("+");
            this.dataSource[last].rKey_ = this.dataSource[arr[i]].rKey_.split("+").reverse().join("+");
          }
        }
      }
    },
    areAllPartsIdentical(str) {
      return str == str.split("+").reverse().join("+");
    },
    // 计算板厚
    async count(type, ok) {
      if (type == "okBtn") {
        this.okBtnFlg = true;
      } else {
        this.okBtnFlg = false;
      }
      let r = /^100$|^(\d|[0-9]\d)(\.\d{1,10})*$/;
      let _flag = false; // 介质开关
      let _flag2 = true; // 填胶开关
      let _flag3 = false; // 残铜率开关
      let _flag4 = false; // T/B开关
      let _flag5 = false; // 叠构开关
      let _index, _index2;
      if (this.dataSource.length == 0) {
        this.$message.error("请先调出叠层信息");
        return;
      }
      for (var i = 0; i < this.dataSource.length; i++) {
        if (!this.dataSource[i].stackUpMTRFoil_) {
          _flag = true;
          _index = i;
          break;
        }
        if (this.dataSource[i].stackUpMTR_ == "OZ" && !this.dataSource[i].stackUpCUTB_) {
          _flag4 = true;
          _index = i;
          break;
        }
        if (this.dataSource[i].stackUpMTR_ == "OZ" && !this.dataSource[i].stackUpCTLMI_) {
          _flag3 = true;
          _index = i;
          break;
        }
        if (this.dataSource[i].stackUpMTR_ == "PP") {
          if (!this.dataSource[i].stackUpPPFillGlue_) {
            _flag2 = false;
            _index2 = i;
            break;
          }
        }
        this.dataSource[i].ppThickness4NY_ = this.dataSource[i].ppThickness4NY_ ? Number(this.dataSource[i].ppThickness4NY_) : null;
      }
      if (_flag) {
        this.$message.error(`第${_index + 1}行介质数据异常`);
        return;
      }
      if (_flag3) {
        this.$message.error(`第${_index + 1}行残铜率数据异常`);
        return;
      }
      if (_flag4) {
        this.$message.error(`第${_index + 1}行T/B数据异常`);
        return;
      }
      if (this.isGlueFill && !_flag2) {
        this.$message.error(`第${_index2 + 1}行填胶数据异常`);
        return;
      }
      let shouldContinue = false;
      this.dataSource.some((ite, index) => {
        if (ite.stackUpMTR_ == "OZ" && !r.test(ite.stackUpCTLMI_)) {
          shouldContinue = true;
          this.$message.error(`第${index + 1}行残铜率数据异常,区间为0-100`);
          return true; // 退出 some 循环
        }
        if (Number(ite.stackUpThichnessMIL_) > Number(ite.stackUpThichnessORG_) && ite.stackUpMTR_ == "PP") {
          shouldContinue = true;
          this.$message.error(`第${index + 1}行数据异常,成品厚度(mil)不得大于物料厚度(mil)`);
          return true;
        }
        return false;
      });
      if (shouldContinue) {
        return;
      }
      // let params = {};
      // this.form.mode = Number(this.$route.query.mode) || 0;
      // this.form.pressingThickness = this.form.pressingThickness ? Number(this.form.pressingThickness) : Number(this.form.finishBoardThickness);
      // params["stackUpInfo"] = this.form;
      // params["stackUpDrills"] = this._drillHoleTableData;
      // params["stackUps"] = this.dataSource;
      await stackcalcv3(this.joinFactoryId, this.form.pdctno, Number(this.$route.query.mode) || 0).then(res => {
        if (res.code) {
          this.NYdata = res.data;
        }
      });
      this.ppprocessing();
      var sum = 0;
      var finishSum = 0;
      // =JSON.parse(localStorage.getItem('ozList'))
      //是否勾选盲埋
      if (this.columns.some(item => item.key == "stackUpPPFillGlue_")) {
        this.dataSource.forEach((item, index) => {
          let oz = "";
          let ozum = 0;
          let jzOLD = "";
          let jz = "";
          let smhd = 0;
          if (item.stackUpMTR_ == "SM") {
            jzOLD = item.stackUpMTRFoil_;
            if (index == 0 && this.dataSource[index + 1].stackUpMTR_ == "OZ") {
              oz = this.dataSource[index + 1].stackUpMTRFoil_?.split("_PT_")[1] || this.dataSource[index + 1].stackUpMTRFoil_;
            } else if (this.dataSource[index - 1].stackUpMTR_ == "OZ") {
              oz = this.dataSource[index - 1].stackUpMTRFoil_?.split("_PT_")[1] || this.dataSource[index - 1].stackUpMTRFoil_;
            }
            ozum = Number(oz * 35);
            //艾威尔
            if ([78, 80].includes(Number(this.$route.query.joinFactoryId))) {
              if (ozum <= 35) {
                if (jzOLD && jzOLD != "一次印刷") {
                  if (jzOLD == "二次印刷" || jzOLD == "1次填基材Linemask+二次印刷") {
                    smhd = 50 * 0.001;
                    jz = jzOLD;
                  } else {
                    smhd = 32.5 * 0.001;
                    jz = jzOLD;
                  }
                } else {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                }
              } else if (ozum > 35 && ozum <= 70) {
                if (jzOLD == "一次印刷") {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                } else if (jzOLD == "线路套印菲林+一次印刷" || jzOLD == "填基材Linemask+一次印刷") {
                  smhd = 32.5 * 0.001;
                  jz = "线路套印菲林+一次印刷";
                } else if (jzOLD == "1次填基材Linemask+二次印刷" || jzOLD == "二次印刷") {
                  smhd = 50 * 0.001;
                  jz = jzOLD;
                } else {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                }
              } else if (ozum > 70 && ozum <= 105) {
                if (jzOLD == "填基材Linemask+一次印刷") {
                  smhd = 32.5 * 0.001;
                  jz = jzOLD;
                } else if (jzOLD == "二次印刷" || jzOLD == "1次填基材Linemask+二次印刷") {
                  smhd = 50 * 0.001;
                  jz = jzOLD;
                } else {
                  smhd = 50 * 0.001;
                  jz = "二次印刷";
                }
              } else if (ozum > 105 && ozum <= 140) {
                if (jzOLD == "1次填基材Linemask+二次印刷") {
                  smhd = 50 * 0.001;
                  jz = jzOLD;
                } else {
                  smhd = 50 * 0.001;
                  jz = "二次印刷";
                }
              } else if (ozum > 140) {
                smhd = 50 * 0.001;
                jz = "1次填基材Linemask+二次印刷";
              }
            } else if (this.$route.query.joinFactoryId == 79) {
              if (ozum <= 70) {
                if (jzOLD && jzOLD != "一次印刷") {
                  if (jzOLD == "二次印刷") {
                    smhd = 50 * 0.001;
                    jz = "二次印刷";
                  } else {
                    smhd = 75.5 * 0.001;
                    jz = "三次印刷";
                  }
                } else {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                }
              } else if (ozum > 70 && ozum < 175) {
                if (jzOLD && jzOLD != "二次印刷") {
                  if (jzOLD == "一次印刷") {
                    smhd = 50 * 0.001;
                    jz = "二次印刷";
                  } else {
                    smhd = 75.5 * 0.001;
                    jz = "三次印刷";
                  }
                } else {
                  smhd = 50 * 0.001;
                  jz = "二次印刷";
                }
              } else {
                smhd = 75.5 * 0.001;
                jz = "三次印刷";
              }
            } else if (this.$route.query.joinFactoryId == 70) {
              if (ozum < 70) {
                smhd = 35 * 0.001;
                jz = "一次印刷";
              } else {
                smhd = 70 * 0.001;
                jz = "一次印刷";
              }
            }
            item.stackUpThichnessORG_ = Math.round(smhd / 0.0254, 3);
            item.stackUpThichnessMIL_ = Math.round(smhd / 0.0254, 3);
            item.stackUpThichnessMM_ = smhd;
            console.log(item, smhd);
            if (!jzOLD || jzOLD != jz) {
              item.stackUpMTRFoil_ = jz;
            }
          }
          if (item.stackUpMTR_ == "PP") {
            // 安全地获取前一个和后一个元素
            const prevItem = index > 0 ? this.dataSource[index - 1] : null;
            const nextItem = index < this.dataSource.length - 1 ? this.dataSource[index + 1] : null;
            // 判断是否是起始层和结束层
            const IsstartLayer = prevItem ? prevItem.stackUpLayerNo_ == 1 : false;
            const IsendLayer = nextItem ? nextItem.stackUpLayerNo_ == this.form.layers : false;
            // 优化 localStorage 读取
            var ozList1 = IsstartLayer ? JSON.parse(localStorage.getItem("ozList") || []) : JSON.parse(localStorage.getItem("ozList1") || []);
            var ozList2 = IsendLayer ? JSON.parse(localStorage.getItem("ozList") || []) : JSON.parse(localStorage.getItem("ozList1") || []);
            var upOZ = "";
            if (!this.dataSource[index + 1]) {
              return (_flag5 = true);
            }
            // let currentData = ozList.find(item => {return item.cuThicknessOrg_ == this.dataSource[index].stackUpThichnessORG_})  // 当前行参数
            let currentStackUpThichnessMIL_ = this.currentLineFilter(item.rKey_, index); // 当前行压合厚度 物料厚度(mil)
            if (this.dataSource[index - 1].stackUpMTR_ == "OZ" && this.dataSource[index + 1].stackUpMTR_ == "OZ") {
              let preData = ozList1.find(ite => {
                return ite.cuType_ == this.dataSource[index - 1].stackUpMTRFoil_;
              }); // 上一行参数
              let nextData = ozList2.find(ite => {
                return ite.cuType_ == this.dataSource[index + 1].stackUpMTRFoil_;
              }); // 下一行参数
              let preStackUpThichnessORG_ = preData.cuThickness_ * 0.0254; // 上一行成品厚度
              let nextStackUpThichnessORG_ = nextData.cuThickness_ * 0.0254; // 下一行成品厚度
              if (item.stackUpPPFillGlue_ == "B") {
                upOZ =
                  currentStackUpThichnessMIL_ * 0.0254 -
                  preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01) -
                  nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
              } else if (item.stackUpPPFillGlue_ == "U") {
                upOZ = currentStackUpThichnessMIL_ * 0.0254 - preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01);
              } else if (item.stackUpPPFillGlue_ == "D") {
                upOZ = currentStackUpThichnessMIL_ * 0.0254 - nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
              } else {
                upOZ = currentStackUpThichnessMIL_ * 0.0254;
              }
            }
            if (this.dataSource[index - 1].stackUpMTR_ == "OZ" && this.dataSource[index + 1].stackUpMTR_ != "OZ") {
              let preData = ozList1.find(ite => {
                return ite.cuType_ == this.dataSource[index - 1].stackUpMTRFoil_;
              }); // 上一行参数
              // let nextData = ozList2.find(ite => {return ite.cuType_ == this.dataSource[index+1].stackUpMTRFoil_})  // 下一行参数
              let preStackUpThichnessORG_ = preData.cuThickness_ * 0.0254; // 上一行成品厚度
              let nextStackUpThichnessORG_ = 0; // 下一行成品厚度
              if (item.stackUpPPFillGlue_ == "B") {
                upOZ =
                  currentStackUpThichnessMIL_ * 0.0254 -
                  preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01) -
                  nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
              } else if (item.stackUpPPFillGlue_ == "U") {
                upOZ = currentStackUpThichnessMIL_ * 0.0254 - preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01);
              } else if (item.stackUpPPFillGlue_ == "D") {
                upOZ = currentStackUpThichnessMIL_ * 0.0254 - nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
              } else {
                upOZ = currentStackUpThichnessMIL_ * 0.0254;
              }
            }
            if (this.dataSource[index - 1].stackUpMTR_ != "OZ" && this.dataSource[index + 1].stackUpMTR_ == "OZ") {
              // let preData = ozList1.find(ite => {return ite.cuType_ == this.dataSource[index-1].stackUpMTRFoil_})   // 上一行参数
              let nextData = ozList2.find(ite => {
                return ite.cuType_ == this.dataSource[index + 1].stackUpMTRFoil_;
              }); // 下一行参数
              let preStackUpThichnessORG_ = 0; // 上一行成品厚度
              let nextStackUpThichnessORG_ = nextData.cuThickness_ * 0.0254; // 下一行成品厚度
              if (item.stackUpPPFillGlue_ == "B") {
                upOZ =
                  currentStackUpThichnessMIL_ * 0.0254 -
                  preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01) -
                  nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
              } else if (item.stackUpPPFillGlue_ == "U") {
                upOZ = currentStackUpThichnessMIL_ * 0.0254 - preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01);
              } else if (item.stackUpPPFillGlue_ == "D") {
                upOZ = currentStackUpThichnessMIL_ * 0.0254 - nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
              } else {
                upOZ = currentStackUpThichnessMIL_ * 0.0254;
              }
            }
            if (this.dataSource[index - 1].stackUpMTR_ != "OZ" && this.dataSource[index + 1].stackUpMTR_ != "OZ") {
              upOZ = this.dataSource[index].stackUpThichnessORG_ * 0.0254;
            }
            let nyhd = 0;
            let drillThickness = 0;
            if (
              JSON.stringify(this.NYdata) != "{}" &&
              this.NYdata.drills &&
              this.NYdata.drills.length &&
              this.NYdata.pnlWidth &&
              this.NYdata.pnlLenth
            ) {
              this.NYdata.drills.forEach(item => {
                if (this.dataSource[index - 1].stackUpLayerNo_ == item.beginLyr && this.dataSource[index + 1].stackUpLayerNo_ == item.endLyr) {
                  if (this.dataSource[index - 1].stackUpMTR_ == "OZ" && this.dataSource[index + 1].stackUpMTR_ == "OZ") {
                    drillThickness =
                      this.dataSource[index - 1].stackUpThichnessMM_ +
                      this.dataSource[index].stackUpThichnessMM_ +
                      this.dataSource[index + 1].stackUpThichnessMM_;
                  } else {
                    drillThickness = 0;
                  }
                  drillThickness = this.getFloat(drillThickness, 6);
                  for (let a = 0; a < item.tools.length; a++) {
                    const element = item.tools[a];
                    nyhd += this.getFloat(
                      (3.1416 * (Number(element.dia) / 2) * (Number(element.dia) / 2) * Number(element.qty) * Number(drillThickness)) /
                        (Number(this.NYdata.pnlLenth) * Number(this.NYdata.pnlWidth)),
                      4
                    );
                  }
                }
              });
            }
            item.ppThickness4NY_ = Math.round((upOZ - nyhd - item.ppThickness4Gllass_ * 0.0254) * 1000); // 奶油厚度
            item.stackUpThichnessMM_ = this.getFloat(upOZ - nyhd, 4); // 成品厚度
            //压合厚度
            let Yh = this.getFloat((upOZ - nyhd) / 0.0254, 4);
            item.stackUpThichnessMIL_ = Yh; //压合厚度
          }
          //板厚
          const thicknessORG = Number(item.stackUpThichnessORG_);
          const thicknessMIL = Number(item.stackUpThichnessMIL_);

          if (this.dataSource[0].stackUpMTR_ == "OZ") {
            const isFirstOrLast = index == 0 || index == this.dataSource.length - 1;
            sum += isFirstOrLast ? thicknessORG : thicknessMIL;
          } else if (this.dataSource[0].stackUpMTR_ == "SM" && item.stackUpMTR_ != "SM") {
            const isSecondOrSecondLast = index == 1 || index == this.dataSource.length - 2;
            sum += isSecondOrSecondLast ? thicknessORG : thicknessMIL;
          }
          finishSum += Number(item.stackUpThichnessMIL_);
        });
        this.$forceUpdate();
      } else {
        this.dataSource.forEach((item, index) => {
          let oz = "";
          let ozum = 0;
          let jzOLD = "";
          let jz = "";
          let smhd = 0;
          if (item.stackUpMTR_ == "SM") {
            jzOLD = item.stackUpMTRFoil_;
            if (index == 0 && this.dataSource[index + 1].stackUpMTR_ == "OZ") {
              oz = this.dataSource[index + 1].stackUpMTRFoil_?.split("_PT_")[1] || this.dataSource[index + 1].stackUpMTRFoil_;
            } else if (this.dataSource[index - 1].stackUpMTR_ == "OZ") {
              oz = this.dataSource[index - 1].stackUpMTRFoil_?.split("_PT_")[1] || this.dataSource[index - 1].stackUpMTRFoil_;
            }
            ozum = Number(oz * 35);
            //艾威尔
            if ([78, 80].includes(Number(this.$route.query.joinFactoryId))) {
              if (ozum <= 35) {
                if (jzOLD && jzOLD != "一次印刷") {
                  if (jzOLD == "二次印刷" || jzOLD == "1次填基材Linemask+二次印刷") {
                    smhd = 50 * 0.001;
                    jz = jzOLD;
                  } else {
                    smhd = 32.5 * 0.001;
                    jz = jzOLD;
                  }
                } else {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                }
              } else if (ozum > 35 && ozum <= 70) {
                if (jzOLD == "一次印刷") {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                } else if (jzOLD == "线路套印菲林+一次印刷" || jzOLD == "填基材Linemask+一次印刷") {
                  smhd = 32.5 * 0.001;
                  jz = "线路套印菲林+一次印刷";
                } else if (jzOLD == "1次填基材Linemask+二次印刷" || jzOLD == "二次印刷") {
                  smhd = 50 * 0.001;
                  jz = jzOLD;
                } else {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                }
              } else if (ozum > 70 && ozum <= 105) {
                if (jzOLD == "填基材Linemask+一次印刷") {
                  smhd = 32.5 * 0.001;
                  jz = jzOLD;
                } else if (jzOLD == "二次印刷" || jzOLD == "1次填基材Linemask+二次印刷") {
                  smhd = 50 * 0.001;
                  jz = jzOLD;
                } else {
                  smhd = 50 * 0.001;
                  jz = "二次印刷";
                }
              } else if (ozum > 105 && ozum <= 140) {
                if (jzOLD == "1次填基材Linemask+二次印刷") {
                  smhd = 50 * 0.001;
                  jz = jzOLD;
                } else {
                  smhd = 50 * 0.001;
                  jz = "二次印刷";
                }
              } else if (ozum > 140) {
                smhd = 50 * 0.001;
                jz = "1次填基材Linemask+二次印刷";
              }
            } else if (this.$route.query.joinFactoryId == 79) {
              if (ozum <= 70) {
                if (jzOLD && jzOLD != "一次印刷") {
                  if (jzOLD == "二次印刷") {
                    smhd = 50 * 0.001;
                    jz = "二次印刷";
                  } else {
                    smhd = 75.5 * 0.001;
                    jz = "三次印刷";
                  }
                } else {
                  smhd = 25 * 0.001;
                  jz = "一次印刷";
                }
              } else if (ozum > 70 && ozum < 175) {
                if (jzOLD && jzOLD != "二次印刷") {
                  if (jzOLD == "一次印刷") {
                    smhd = 50 * 0.001;
                    jz = "二次印刷";
                  } else {
                    smhd = 75.5 * 0.001;
                    jz = "三次印刷";
                  }
                } else {
                  smhd = 50 * 0.001;
                  jz = "二次印刷";
                }
              } else {
                smhd = 75.5 * 0.001;
                jz = "三次印刷";
              }
            } else if (this.$route.query.joinFactoryId == 70) {
              if (ozum < 70) {
                smhd = 35 * 0.001;
                jz = "一次印刷";
              } else {
                smhd = 70 * 0.001;
                jz = "一次印刷";
              }
            }
            item.stackUpThichnessORG_ = Math.round(smhd / 0.0254, 3);
            item.stackUpThichnessMIL_ = Math.round(smhd / 0.0254, 3);
            item.stackUpThichnessMM_ = smhd;
            console.log(item, smhd);
            if (!jzOLD || jzOLD != jz) {
              item.stackUpMTRFoil_ = jz;
            }
          }
          if (item.stackUpMTR_ == "PP") {
            // 安全地获取前一个和后一个元素
            const prevItem = index > 0 ? this.dataSource[index - 1] : null;
            const nextItem = index < this.dataSource.length - 1 ? this.dataSource[index + 1] : null;
            // 判断是否是起始层和结束层
            const IsstartLayer = prevItem ? prevItem.stackUpLayerNo_ == 1 : false;
            const IsendLayer = nextItem ? nextItem.stackUpLayerNo_ == this.form.layers : false;
            // 优化 localStorage 读取
            var ozList1 = IsstartLayer ? JSON.parse(localStorage.getItem("ozList") || []) : JSON.parse(localStorage.getItem("ozList1") || []);
            var ozList2 = IsendLayer ? JSON.parse(localStorage.getItem("ozList") || []) : JSON.parse(localStorage.getItem("ozList1") || []);
            //PP成品厚度
            var upOZ = "";
            if (this.dataSource[index - 1].stackUpMTR_ == "OZ" && this.dataSource[index + 1].stackUpMTR_ == "OZ") {
              //成品厚度
              let preData = ozList1.find(ite => {
                return ite.cuType_ == this.dataSource[index - 1].stackUpMTRFoil_;
              }); // 上一行参数
              let nextData = ozList2.find(ite => {
                return ite.cuType_ == this.dataSource[index + 1].stackUpMTRFoil_;
              }); // 下一行参数
              let preStackUpThichnessORG_ = preData.cuThickness_ * 0.0254; // 上一行成品厚度mm
              let nextStackUpThichnessORG_ = nextData.cuThickness_ * 0.0254; // 下一行成品厚度mm
              let currentStackUpThichnessMIL_ = this.currentLineFilter(item.rKey_, index); // 当前物料厚度mil
              upOZ =
                currentStackUpThichnessMIL_ * 0.0254 -
                preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01) -
                nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
            }
            if (this.dataSource[index - 1].stackUpMTR_ == "OZ" && this.dataSource[index + 1].stackUpMTR_ != "OZ") {
              let preData = ozList1.find(ite => {
                return ite.cuType_ == this.dataSource[index - 1].stackUpMTRFoil_;
              }); // 上一行参数
              let preStackUpThichnessORG_ = preData.cuThickness_ * 0.0254; // 上一行成品厚度
              let currentStackUpThichnessMIL_ = this.currentLineFilter(item.rKey_, index); // 当前行压合厚度
              upOZ = currentStackUpThichnessMIL_ * 0.0254 - preStackUpThichnessORG_ * ((100 - this.dataSource[index - 1].stackUpCTLMI_) * 0.01);
            }
            if (this.dataSource[index - 1].stackUpMTR_ != "OZ" && this.dataSource[index + 1].stackUpMTR_ == "OZ") {
              let nextData = ozList2.find(ite => {
                return ite.cuType_ == this.dataSource[index + 1].stackUpMTRFoil_;
              }); // 下一行参数
              let nextStackUpThichnessORG_ = nextData.cuThickness_ * 0.0254; // 下一行成品厚度
              let currentStackUpThichnessMIL_ = this.currentLineFilter(item.rKey_, index); // 当前行压合厚度
              upOZ = currentStackUpThichnessMIL_ * 0.0254 - nextStackUpThichnessORG_ * ((100 - this.dataSource[index + 1].stackUpCTLMI_) * 0.01);
            }
            if (this.dataSource[index - 1].stackUpMTR_ != "OZ" && this.dataSource[index + 1].stackUpMTR_ != "OZ") {
              upOZ = this.dataSource[index].stackUpThichnessORG_ * 0.0254;
            }
            item.stackUpThichnessMM_ = this.getFloat(upOZ, 4); // 成品厚度
            item.ppThickness4NY_ = Math.round((upOZ - item.ppThickness4Gllass_ * 0.0254) * 1000); // 奶油厚度
            let Yh = this.getFloat(upOZ / 0.0254, 4);
            item.stackUpThichnessMIL_ = Yh; //压合厚度
          }
          //板厚
          const thicknessORG = Number(item.stackUpThichnessORG_);
          const thicknessMIL = Number(item.stackUpThichnessMIL_);

          if (this.dataSource[0].stackUpMTR_ == "OZ") {
            const isFirstOrLast = index == 0 || index == this.dataSource.length - 1;
            sum += isFirstOrLast ? thicknessORG : thicknessMIL;
          } else if (this.dataSource[0].stackUpMTR_ == "SM" && item.stackUpMTR_ != "SM") {
            const isSecondOrSecondLast = index == 1 || index == this.dataSource.length - 2;
            sum += isSecondOrSecondLast ? thicknessORG : thicknessMIL;
          }
          finishSum += Number(item.stackUpThichnessMIL_);
        });
      }
      this.$store.commit("changeInfo", { pressingThickness: this.getFloat(sum * 0.0254, 4) });
      this.$store.commit("changeInfo", { finishedThickness: this.getFloat(finishSum * 0.0254, 4) });
      this.$forceUpdate();
      let _value =
        Number(
          this.dataSource
            .find(item => {
              return item.stackUpLayerNo_ == 1;
            })
            .stackUpMTRFoil_.split("_PT_")[1]
        ) -
        Number(
          this.dataSource
            .find(item => {
              return item.stackUpLayerNo_ == 1;
            })
            .stackUpMTRFoil_.split("_PT_")[0]
        );
      let _value1 =
        Number(
          this.dataSource
            .find(item => {
              return item.stackUpLayerNo_ == this.form.layers;
            })
            .stackUpMTRFoil_.split("_PT_")[1]
        ) -
        Number(
          this.dataSource
            .find(item => {
              return item.stackUpLayerNo_ == this.form.layers;
            })
            .stackUpMTRFoil_.split("_PT_")[0]
        );
      let lhdcoz = Number(
        this.dataSource
          .find(item => {
            return item.stackUpLayerNo_ == 1;
          })
          .stackUpMTRFoil_.split("_PT_")[1]
      );
      if (this.$route.query.joinFactoryId == 22 && _value == 1 && _value1 == 1) {
        if (this.form.finishBoardThickness > 0.8) {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.05 - 0.05, 3));
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.15 - 0.05, 3));
        } else {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.09 - 0.05, 3));
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.14 - 0.05, 3));
        }
      }
      if (this.$route.query.joinFactoryId == 38 && lhdcoz > 2) {
        this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.2 + 0.08, 3));
        this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.2 - 0.08, 3));
      }
      if (_flag5) {
        this.$message.error("叠构有误，请调整"); // 在弹框前判断叠构是否有问题
      } else {
        if (this.form.pressingThickness >= this.form.engineeringLow && this.form.pressingThickness <= this.form.engineeringHiht) {
          this.pressingThicknessBg = "finish";
          if (!this.okBtnFlg) {
            if (ok != "countok") {
              this.$success({
                title: "成功",
                content: "板厚OK",
              });
            } else if (ok == "countok") {
              this.dataSave();
            }
          }
        } else if (this.form.pressingThickness > this.form.engineeringHiht) {
          this.pressingThicknessBg = "error";
          if (!this.okBtnFlg) {
            this.$error({
              title: "板厚错误",
              content: "板厚超厚，请调整",
            });
          }
        } else if (this.form.pressingThickness < this.form.engineeringLow) {
          this.pressingThicknessBg = "error";
          if (!this.okBtnFlg) {
            this.$error({
              title: "板厚错误",
              content: "板厚偏薄，请调整",
            });
          }
        }
        let data = {};
        data["stackUps"] = this.dataSource;
        data["stackUpDrills"] = this._drillHoleTableData;
        stackcalc(data).then(res => {
          if (res.code) {
            this.$store.commit("drillHoleTableChange", res.data);
          }
        });
      }
    },
    typeFilter(record) {
      if (record.imp_Type_.indexOf("_D") > 0 && record.imp_Type_.indexOf("_C") > 0) {
        return "1";
        // 含_D _C 除了了L/S 其他都可编辑
      } else if (record.imp_Type_.indexOf("_C") > 0 && record.imp_Type_.indexOf("_D") < 0) {
        //（包含_C）
        return "2";
      } else if (record.imp_Type_.indexOf("_C") < 0 && record.imp_Type_.indexOf("_D") > 0) {
        // 包含_D
        return "3";
      } else {
        // 啥都不包含
        return "4";
      }
    },
    test(text, index) {
      let impedanceData = [];
      if (text) {
        impedanceData.push(JSON.parse(JSON.stringify(this.$refs.impedance.impedanceTabData[index])));
      } else {
        impedanceData = JSON.parse(JSON.stringify(this.$refs.impedance.impedanceTabData));
      }
      for (var a = 0; a < impedanceData.length; a++) {
        let idn = 0;
        if (text) {
          idn = index + 1;
        } else {
          idn = a + 1;
        }
        if (!impedanceData[a].imp_Type_) {
          this.$error({
            title: "错误",
            content: "请选择第" + idn + "行阻抗类型",
          });
          return false;
        }
        if (!impedanceData[a].imp_ControlLay_) {
          this.$error({
            title: "错误",
            content: "请选择第" + idn + "行控制层",
          });
          return false;
        }
        if (impedanceData[a].imp_ControlLay_ && impedanceData[a].imp_ControlLay_ != "L1" && impedanceData[a].imp_UpLay_ == "") {
          this.$error({
            title: "错误",
            content: "请选择第" + idn + "行上参",
          });
          return false;
        }
        if (impedanceData[a].imp_ControlLay_ && impedanceData[a].imp_ControlLay_ != "L" + this.form.layers && impedanceData[a].imp_DownLay_ == "") {
          this.$error({
            title: "错误",
            content: "请选择第" + idn + "行下参",
          });
          return false;
        }
        if (impedanceData[a].imp_LineWidth_ == "") {
          this.$error({
            title: "错误",
            content: "请填写第" + idn + "行线宽",
          });
          return false;
        }
        if ((this.typeFilter(impedanceData[a]) == "3" || this.typeFilter(impedanceData[a]) == "1") && impedanceData[a].imp_LineSpace_ == "") {
          this.$error({
            title: "错误",
            content: "请填写第" + idn + "行线隙",
          });
          return false;
        }
        if ((this.typeFilter(impedanceData[a]) == "1" || this.typeFilter(impedanceData[a]) == "2") && impedanceData[a].imp_LineCuSpace_ == "") {
          this.$error({
            title: "错误",
            content: "请填写第" + idn + "行线铜",
          });
          return false;
        }
        if (impedanceData[a].imp_Value_Req_ == "") {
          this.$error({
            title: "错误",
            content: "请填写第" + idn + "行要求",
          });
          return false;
        }
        if (impedanceData[a].imp_Value_Tol_ == "") {
          this.$error({
            title: "错误",
            content: "请填写第" + idn + "行公差",
          });
          return false;
        }
        if (impedanceData[a].imp_OKLineWidth_ == "") {
          this.$error({
            title: "错误",
            content: "请填写第" + idn + "行L/W",
          });
          return false;
        }
        if ((this.typeFilter(impedanceData[a]) == "1" || this.typeFilter(impedanceData[a]) == "2") && impedanceData[a].imp_OKLineCuSpace_ == "") {
          this.$error({
            title: "错误",
            content: "请填写第" + idn + "行L/CU",
          });
          return false;
        }
        // if(impedanceData[a].imp_BC_ == '' && !this.showerror){
        //     this.$error({
        //     title: '错误',
        //     content: '请填写第'+ idn +'行补偿',
        //   })
        //   return false
        // }
      }
      return true;
    },
    // 阻抗计算
    countImpedance() {
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      if (!this.test()) {
        return;
      }
      if (this.form.pressingThickness == "") {
        this.$error({
          title: "错误",
          content: "板厚数据异常，请调整",
        });
        return;
      }
      if (this.$refs.impedance.impedanceTabData.length < 1) {
        this.$error({
          title: "错误",
          content: "阻抗参数错误",
        });
        return;
      }
      for (let index = 0; index < this.$refs.impedance.impedanceTabData.length; index++) {
        const element = this.$refs.impedance.impedanceTabData[index];
        let a = index + 1;
        if (element.imp_LineWidth_ && !x.test(element.imp_LineWidth_)) {
          this.$error({
            title: "错误",
            content: "第" + a + "行线宽输入值:【" + element.imp_LineWidth_ + "】异常,请修正",
          });
          return;
        }
        if (element.imp_LineSpace_ && !x.test(element.imp_LineSpace_)) {
          this.$error({
            title: "错误",
            content: "第" + a + "行线隙输入值:【" + element.imp_LineSpace_ + "】异常,请修正",
          });
          return;
        }
        if (element.imp_LineCuSpace_ && !x.test(element.imp_LineCuSpace_)) {
          this.$error({
            title: "错误",
            content: "第" + a + "行线铜输入值:【" + element.imp_LineCuSpace_ + "】异常,请修正",
          });
          return;
        }
        if (element.imp_Value_Req_ && !x.test(element.imp_Value_Req_)) {
          this.$error({
            title: "错误",
            content: "第" + a + "行要求输入值:【" + element.imp_Value_Req_ + "】异常,请修正",
          });
          return;
        }
        if (element.imp_Value_Tol_ && !x.test(element.imp_Value_Tol_)) {
          this.$error({
            title: "错误",
            content: "第" + a + "行公差输入值:【" + element.imp_Value_Tol_ + "】异常,请修正",
          });
          return;
        }
        if (element.imp_OKLineWidth_ && !x.test(element.imp_OKLineWidth_)) {
          this.$error({
            title: "错误",
            content: "第" + a + "行L/W输入值:【" + element.imp_OKLineWidth_ + "】异常,请修正",
          });
          return;
        }
        if (element.imp_OKLineSpace_ && !x.test(element.imp_OKLineSpace_)) {
          this.$error({
            title: "错误",
            content: "第" + a + "行L/S输入值:【" + element.imp_OKLineSpace_ + "】异常,请修正",
          });
          return;
        }
        // if (element.imp_BC_ && !x.test(element.imp_BC_ )) {
        //   this.$error({
        //     title: '错误',
        //     content:'第'+ a + '行补偿输入值:【'+ element.imp_BC_ +'】异常,请修正',
        //   })
        //   return
        // }
      }
      let newDataSource = [];
      this.dataSource.forEach(item => {
        if (item.stackUpLayerNo_) {
          newDataSource.push(item.stackUpLayerNo_);
        }
      });
      let prevNumber = Number(newDataSource[0]);
      for (let index = 1; index < newDataSource.length; index++) {
        let currentNumber = Number(newDataSource[index]);
        if (currentNumber <= prevNumber || currentNumber - 1 != prevNumber) {
          this.$message.error("请检查叠层信息层号顺序");
          return;
        }
        prevNumber = currentNumber;
      }
      this.spinning = true;
      const impedanceData = JSON.parse(JSON.stringify(this.$refs.impedance.impedanceTabData));
      for (var i = 0; i < impedanceData.length; i++) {
        // this.$refs.impedance.lineWidthChage(impedanceData[i],i)
        // this.$refs.impedance.reqChange(impedanceData[i],i)
        // this.$refs.impedance.lwChange(impedanceData[i],i)
        // this.$refs.impedance.controlLayChange(impedanceData[i],i)
        // this.$refs.impedance.botReferenceChange(impedanceData[i],i)
        // this.$refs.impedance.lineGapChange(impedanceData[i],i)
        // this.$refs.impedance.lineCopperChange(impedanceData[i],i)
        // this.$refs.impedance.impBCChage(impedanceData[i],i)

        let value = impedanceData[i];
        let control = value?.imp_ControlLay_.split("L")[1];
        let medium = this.dataSource.find(item => {
          return item.stackUpLayerNo_ == control;
        })?.stackUpMTRFoil_;
        let oz = medium.split("_PT")[0] || medium;
        if (
          this.dataSource.find(item => {
            return item.stackUpLayerNo_ == control;
          })
        ) {
          impedanceData[i].imp_CtlTB_ = this.dataSource.find(item => {
            return item.stackUpLayerNo_ == control;
          }).stackUpCUTB_;
        }
        if (this.form.layers != "") {
          if (this.bcdata.length != 0) {
            this.bcdata.forEach(res => {
              if (res.lineID_ == control) {
                if (impedanceData[i].imp_BC_ == res.bC_ && this.joinFactoryId == "12") {
                  if (oz <= 1 && value.imp_OKLineWidth_ >= 10.5) {
                    impedanceData[i].imp_BC_ = "0";
                    this.showerror = true;
                  } else if (oz > 1) {
                    impedanceData[i].imp_BC_ = Number(res.bC_) / 2 + "";
                    this.showerror = false;
                  }
                } else {
                  impedanceData[i].imp_BC_ = impedanceData[i].imp_BC_ ? impedanceData[i].imp_BC_ + "" : res.bC_ ? res.bC_ + "" : "0";
                  this.showerror = false;
                }
              }
            });
          } else {
            this.someList.forEach(res => {
              if (control == 1 || control == this.form.layers) {
                if (res.iO_ == "910" && res.cu_ == medium && res.holeCu_ == "20") {
                  if (impedanceData[i].imp_BC_ == res.bc && this.joinFactoryId == "12") {
                    if (oz <= 1 && value.imp_OKLineWidth_ >= 10.5) {
                      impedanceData[i].imp_BC_ = "0";
                      this.showerror = true;
                    } else if (oz > 1) {
                      impedanceData[i].imp_BC_ = res.bc / 2 + "";
                      this.showerror = false;
                    }
                  } else {
                    impedanceData[i].imp_BC_ = impedanceData[i].imp_BC_ ? impedanceData[i].imp_BC_ + "" : res.bc ? res.bc + "" : "0";
                    this.showerror = false;
                  }
                }
              } else {
                if (res.iO_ == "911" && res.cu_ == medium && res.holeCu_ == "20") {
                  if (impedanceData[i].imp_BC_ == res.bc && this.joinFactoryId == "12") {
                    if (oz <= 1 && value.imp_OKLineWidth_ >= 10.5) {
                      impedanceData[i].imp_BC_ = "0";
                      this.showerror = true;
                    } else if (oz > 1) {
                      impedanceData[i].imp_BC_ = Number(res.bc) / 2 + "";
                      this.showerror = false;
                    }
                  } else {
                    impedanceData[i].imp_BC_ = impedanceData[i].imp_BC_ ? impedanceData[i].imp_BC_ + "" : res.bc ? res.bc + "" : "0";
                    this.showerror = false;
                  }
                }
              }
            });
          }
          this.someList.forEach(res => {
            if (control == 1 || control == this.form.layers) {
              // O
              if (res.iO_ == "910" && res.cu_ == medium && res.holeCu_ == "20") {
                impedanceData[i].imp_C1_ = res.c1_ + "";
                impedanceData[i].imp_C2_ = res.c2_ + "";
                impedanceData[i].imp_C3_ = res.c3_ + "";
                impedanceData[i].imp_T1_ = res.t1_ + "";
                impedanceData[i].imp_CEr_ = res.cEr_ + "";
              }
            } else {
              if (res.iO_ == "911" && res.cu_ == medium && res.holeCu_ == "20") {
                impedanceData[i].imp_C1_ = res.c1_ + "";
                impedanceData[i].imp_C2_ = res.c2_ + "";
                impedanceData[i].imp_C3_ = res.c3_ + " ";
                impedanceData[i].imp_T1_ = res.t1_ + " ";
                impedanceData[i].imp_CEr_ = res.cEr_ + "";
              }
            }
          });
          this.$refs.impedance.impedanceTabData = impedanceData;
          this.$forceUpdate();
        } else {
          this.$message.info("请填写层数");
        }
        if (impedanceData[i].imp_ControlLay_ && impedanceData[i].imp_ControlLay_ != "L1" && impedanceData[i].imp_UpLay_) {
          this.$refs.impedance.upLayChange(impedanceData[i], i);
        }
        if (impedanceData[i].imp_ControlLay_ && impedanceData[i].imp_ControlLay_ != "L" + this.form.layers && impedanceData[i].imp_DownLay_) {
          this.$refs.impedance.downChange(impedanceData[i], i);
        }
        if (impedanceData[i].imp_SetInStackup_ == "") {
          impedanceData[i].imp_SetInStackup_ = 0;
        }
        if (impedanceData[i].imp_SetInStackup_ != null && impedanceData[i].imp_SetInStackup_ != 1) {
          this.$refs.impedance.lineWidthChage(impedanceData[i], i);
          this.$refs.impedance.lineGapChange(impedanceData[i], i);
          this.$refs.impedance.lineCopperChange(impedanceData[i], i);
        }
        if (!impedanceData[i].imp_W1_ || !impedanceData[i].imp_W2_) {
          this.$refs.impedance.lwChange(impedanceData[i], i);
        }
        delete impedanceData[i].impModelImage;
        delete impedanceData[i].impColorRed;
        delete impedanceData[i].lineCuColorRed;
        delete impedanceData[i].lineSpaceColorRed;
        delete impedanceData[i].lineWidthColorRed;
        // copyImpedance.push(JSON.parse(JSON.stringify(impedanceData[i])))
        // if(copyImpedance[i].imp_Type_.indexOf('_1')!=-1){
        //    if(!copyImpedance[i].imp_UpLay_){
        //      if(copyImpedance[i].imp_CtlTB_=='t'){
        //       copyImpedance[i].imp_Type_ = copyImpedance[i].imp_Type_ + 'D'
        //      }else{
        //       copyImpedance[i].imp_Type_ = copyImpedance[i].imp_Type_ + 'U'
        //      }
        //    }else{
        //     if(copyImpedance[i].imp_CtlTB_=='t'){
        //       copyImpedance[i].imp_Type_ = copyImpedance[i].imp_Type_ + 'U'
        //      }else{
        //       copyImpedance[i].imp_Type_ = copyImpedance[i].imp_Type_ + 'D'
        //      }
        //    }
        // }
      }
      if (impedanceData.length > 0) {
        this.spinning = true;
        impedanceCount(impedanceData).then(res => {
          this.spinning = false;
          if (res.code == 1) {
            this.$refs.impedance.impHandelClick(res);
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.$error({
          title: "计算错误",
          content: "无阻抗数据",
        });
      }
    },
    //阻抗报告
    Impedancereport() {
      let fac = "";
      if (this.$route.query.joinFactoryId) {
        fac = this.$route.query.joinFactoryId;
      } else {
        fac = "-1";
      }
      impoutreportinfo(this.form.pdctno, fac).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    exportExcelFile(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      return XLSX.writeFile(workBook, fileName);
    },
    // 阻抗反算
    inverseCount(text, index) {
      if (!this.test(text, index)) {
        return;
      }
      let params = [];
      if (text) {
        params.push(JSON.parse(JSON.stringify(this.$refs.impedance.impedanceTabData[index])));
      } else {
        params = JSON.parse(JSON.stringify(this.$refs.impedance.impedanceTabData));
      }
      for (var i = 0; i < params.length; i++) {
        delete params[i].impModelImage;
      }
      this.spinning = true;
      impedanceInverseCount(params)
        .then(res => {
          if (res.code == 1) {
            if (text) {
              this.$refs.impedance.impInverseClick(res.data, index);
            } else {
              this.$refs.impedance.impInverseClick(res.data);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 数据存盘
    async dataSave() {
      if (!this.form.pressingThickness) {
        return;
      }
      let newDataSource = [];
      this.dataSource.forEach(item => {
        if (item.stackUpLayerNo_) {
          newDataSource.push(item.stackUpLayerNo_);
        }
      });
      let prevNumber = Number(newDataSource[0]);
      for (let index = 1; index < newDataSource.length; index++) {
        let currentNumber = Number(newDataSource[index]);
        if (currentNumber <= prevNumber || currentNumber - 1 != prevNumber) {
          this.$message.error("请检查叠层信息层号顺序");
          return;
        }
        prevNumber = currentNumber;
      }
      //impedance
      this.spinning = true;
      for (var inde = 0; inde < this.dataSource.length; inde++) {
        if (
          this.dataSource[inde].stackUpMTR_ == "Core" &&
          this.dataSource[inde - 1].stackUpMTR_ == "OZ" &&
          this.dataSource[inde + 1].stackUpMTR_ == "OZ"
        ) {
          let rKey_ = this.dataSource[inde].rKey_;
          let stackUpMTRFoil_ = Number(this.dataSource[inde].stackUpMTRFoil_);
          let stackUpCoreDS_ = this.dataSource[inde].stackUpCoreDS_;
          let topOZ_ = Number(this.dataSource[inde - 1].stackUpMTRFoil_?.split("_PT_")[0]);
          let bomOZ_ = Number(this.dataSource[inde + 1].stackUpMTRFoil_?.split("_PT_")[0]);
          if (this.coreListData.length) {
            let obj = this.coreListData.filter(ite => {
              return ite.rKey_ == rKey_;
            });
            if (obj.length) {
              if (obj[0].core_ != stackUpMTRFoil_ || obj[0].topOZ_ != topOZ_ || obj[0].bomOZ_ != bomOZ_ || obj[0].tD_ != stackUpCoreDS_) {
                this.$message.error("数据保存失败，请重新选择物料再保存！");
                this.spinning = false;
                return;
              }
            }
          }
        }
      }
      let data = {},
        formData = JSON.parse(JSON.stringify(this.form)),
        LaminationData = this.dataSource,
        impedanceData = this.$refs.impedance.impedanceTabData;
      // form表单处理
      this.boardTypeList.forEach(item => {
        if (item.text == this.form.boardType) {
          formData.boardType = item.valueMember;
        }
      });
      this.ppTypeList.forEach(item => {
        if (item.text == this.form.ppType) {
          formData.ppType = item.valueMember;
        }
      });
      formData.joinFactoryId = this.joinFactoryId;
      formData.layers = Number(this.form.layers);
      delete formData.changeS;
      LaminationData.forEach(item => {
        if (item.stackUpLayerNo_) {
          item.stackUpLayerNo_ = item.stackUpLayerNo_.toString();
        }
        if (item.stackUpMTRFoil_.indexOf("PT") == -1) {
          item.stackUpCuMin4T1_ = "";
          item.stackUpCuMax4T1_ = null;
        }
        if (item.ppThickness4NY_ == "") {
          item.ppThickness4NY_ = null;
        }
        if (!item.waterMark_) {
          item.waterMark_ = false;
        }
      });
      for (let index = 0; index < impedanceData.length; index++) {
        if (impedanceData[index].imp_TrueValue_) {
          impedanceData[index].imp_SetInStackup_ = 1;
        }
      }
      let arr_tag = this.getTag(this.tagData);
      // 创建一个Map来存储guid与label的对应关系
      const tagMap = new Map(arr_tag.map(item => [item.guid, item.label]));
      // 遍历LaminationData，更新tag
      LaminationData.forEach(ite => {
        if (tagMap.has(ite.iD_)) {
          ite.tag = ite.tag ? `${ite.tag}${tagMap.get(ite.iD_)}` : tagMap.get(ite.iD_);
        }
      });
      data["stackUpInfo"] = formData;
      data["stackUps"] = LaminationData;
      data["StackIMP"] = impedanceData;
      data["stackUpDrills"] = this._drillHoleTableData;
      saveData(data).then(res => {
        if (res.code == 1) {
          if (res.data.length) {
            this.dataVisible = true;
            this.dataSaveclick = false;
            this.message = res.data;
            this.spinning = false;
            return;
          } else {
            this.$message.success("保存成功");
            this.upstack = true;
            this.setedit(false);
            this.tagData = [];
            if (this.$route.query.id) {
              settensilecoef(this.$route.query.id, this.joinFactoryId, this.form.pdctno).then(res => {});
            }
            if (this.form.pdctno) {
              getInpedanceData({ OrderNo: this.form.pdctno }, this.joinFactoryId).then(res => {
                if (res.code == 1) {
                  this.reportData = JSON.parse(JSON.stringify(res.data));
                  this.reportData1 = JSON.parse(JSON.stringify(res.data));
                  this.reportData1.stackUps.forEach(item => {
                    item.stackUpMTRFoil_ = item.stackUpMTRFoilBase;
                    item.stackUpThichnessMM_ = item.stackUpThichnessMMBase;
                  });
                  if (res.data.isEnglish == true) {
                    this.reportType = true;
                  }
                  if (this.joinFactoryId == "58" || this.joinFactoryId == "59") {
                    this.showMaterial = res.data.custAssignment;
                  }
                  if (this.joinFactoryId == "58" || this.joinFactoryId == "59") {
                    this.showImpedancediagram = this.$refs.impedance.impedanceTabData.length ? true : false;
                  }
                  this.factory1 = this.reportData.stackUpInfo.joinFactoryId;
                  this.dataSaveclick = true;
                  let formData = new FormData();
                  formData.append("OrderNo", this.form.pdctno);
                  formData.append("factoryId", this.factory1);
                  setTimeout(() => {
                    Promise.all([
                      this.$refs.report2.generateImage("dieceng", "ChineseFile"),
                      this.$refs.report2.generateImage("dieceng1", "EnglishFile"),
                      this.$refs.report1.getReporturl(),
                    ])
                      .then(() => {
                        formData.append("ChineseFile", this.$refs.report2.ChineseFile);
                        formData.append("EnglishFile", this.$refs.report2.EnglishFile);
                        formData.append("PdfFile", this.$refs.report1.PdfFile);
                        return uploadstackimpall(formData);
                      })
                      .then(res => {
                        if (res.code) {
                          this.$message.success("叠层图上传成功");
                        } else {
                          this.$message.error("叠层图上传失败", res.message);
                        }
                      })
                      .catch(error => {
                        console.error("Error:", error);
                        this.$message.error("数据存盘失败");
                      })
                      .finally(() => {
                        this.dataSaveclick = false;
                        this.spinning = false;
                        this.upstack = false;
                      });
                  }, 0);
                } else {
                  this.spinning = false;
                }
              });
            }
          }
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      });
    },
    countCopper() {
      if (!this.form.pressingThickness) {
        this.$message.error("请先计算板厚");
        return;
      }
      //impedance
      this.spinning = true;
      let data = {},
        formData = JSON.parse(JSON.stringify(this.form)),
        LaminationData = this.dataSource;
      // form表单处理
      this.boardTypeList.forEach(item => {
        if (item.text == this.form.boardType) {
          formData.boardType = item.valueMember;
        }
      });
      this.ppTypeList.forEach(item => {
        if (item.text == this.form.ppType) {
          formData.ppType = item.valueMember;
        }
      });
      formData.joinFactoryId = this.joinFactoryId;
      formData.layers = Number(this.form.layers);
      delete formData.changeS;
      LaminationData.forEach(item => {
        if (item.stackUpLayerNo_) {
          item.stackUpLayerNo_ = item.stackUpLayerNo_.toString();
        }
        if (item.stackUpMTRFoil_.indexOf("PT") == -1) {
          item.stackUpCuMin4T1_ = "";
          item.stackUpCuMax4T1_ = null;
        }
      });

      data["stackUpInfo"] = formData;
      data["stackUps"] = LaminationData;
      data["stackUpDrills"] = this._drillHoleTableData;
      calcStackUpCu(data)
        .then(res => {
          if (res.code == 1) {
            this.$message.success(res.message);
            this.dataSource = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    collapseChange() {
      this.collapse = !this.collapsereportData;
      // this.collapse = !this.collapse
    },
    // 当鼠标向上向下拖动时触发该方法
    textWidthChange(e) {
      let that = this;
      this.moveKey = true;
      let dy = e.clientY; //当你第一次单击的时候，存储Y轴的坐标。
      window.onmousemove = e => {
        if (this.moveKey == true) {
          const updateHeight = () => {
            if (e.clientY + 42 >= that.innerHeight) {
              return;
            }
            if (e.clientY < 155) {
              return;
            }
            if (e.clientY < dy) {
              that.tableHeight -= dy - e.clientY;
            } else {
              that.tableHeight += e.clientY - dy;
            }
            dy = e.clientY; // 更新鼠标按下时的Y坐标
          };
          requestAnimationFrame(updateHeight);
        }
      };

      document.onmouseup = e => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
      e.stopPropagation();
      e.preventDefault();
      return false;
    },
    textWidthChange1(e) {
      const updateHeight = () => {
        this.$refs.impedance.heighty = this.innerHeight - this.tableHeight - 220;
        if (this.innerHeight - this.tableHeight - 221 >= 137) {
          this.$refs.laminationInfo.ppheighty = 314;
        } else {
          this.$refs.laminationInfo.ppheighty = 445 - (this.innerHeight - this.tableHeight - 221);
        }
        if (this.innerHeight - this.tableHeight - 231 < 22) {
          this.activeKey = ["0"];
        } else {
          this.activeKey = ["1"];
        }
      };
      requestAnimationFrame(updateHeight);
    },
    openReport(type) {
      if (this.form.pdctno) {
        getInpedanceData({ OrderNo: this.form.pdctno }, this.joinFactoryId).then(res => {
          if (res.code == 1) {
            this.reportData = res.data;
            if (res.data.isEnglish == true) {
              this.reportType = true;
            }
            this.factory1 = this.reportData.stackUpInfo.joinFactoryId;
            this.modelVisible = true;
            if (this.joinFactoryId == "58" || this.joinFactoryId == "59") {
              this.showImpedancediagram = this.$refs.impedance.impedanceTabData.length ? true : false;
            }
          } else {
            this.$message.error("不存在叠层数据，请核实");
          }
        });
      } else {
        this.$message.error("请输入产品编号");
      }
    },
    Exportdirectly() {
      if (this.form.pdctno) {
        this.DownloadPopup = true;
        this.reportdata = {
          Impedancemodel: false,
          Reporttype: 1,
        };
      } else {
        this.$message.error("请输入产品编号");
      }
    },
    DownloadPopupOk() {
      let params = this.reportdata;
      params.mainOrderNo = this.form.pdctno;
      params.FactoryId = this.joinFactoryId;
      stackreport(params).then(res => {
        if (res.code == 1) {
          this.downloadByteArrayFromString(res.data, this.form.pdctno + "叠层阻抗信息.pdf");
        } else {
          this.$message.error("不存在叠层数据，请核实");
        }
      });
      if (this.Impedanceinformation) {
        this.downxlsx();
      }
    },
    downxlsx() {
      stackiMP(this.joinFactoryId, this.form.pdctno).then(res => {
        if (res.code == 1) {
          this.downloadByteArrayFromString(res.data, this.form.pdctno + "叠层阻抗信息.xlsx");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk(e) {
      this.$refs.report.getReportPdf();
      if (this.factory1 == 12) {
        this.downxlsx();
      }
    },
    handleOkimg() {
      this.$refs.report.getReportimg();
      if (this.factory1 == 12) {
        this.downxlsx();
      }
    },
    onCustomButtonClick() {
      this.reportType = false;
    },
    onCustomButtonClick1() {
      this.reportType = true;
    },
    clearData() {
      let that = this;
      this.$confirm({
        title: <strong>请确认是否需要清空界面上的数据?</strong>,
        content: h => <div style="color:red;">（如数据已保存则后台数据不会清除）</div>,
        onOk() {
          that.dataSource = [];
          let _obj = {
            pdctno: "", // 编号
            layers: "", // 层数
            boardType: "", // 板材型号
            // tg: '',                        // Tg值
            ppType: "", // PP型号
            finishBoardThickness: 0, // 完成板厚
            highLimit: "", // 上限公差
            engineeringHiht: 0, // 工程上限
            lowLimit: "", // 下限公差
            engineeringLow: 0, // 公差下限
            pressingThickness: 0, // 压合板厚
            finishedThickness: 0, // 成品厚度
            pressingTimes: undefined, // 压合次数
            stackUpTol: "",
            isPressure: false, // 芯板对压
            isChangeLayerPres: false, // 层压不可更改
            gBStackUp: false, // 光板
            gbcount: 0, // 光板下拉
            changeS: [], // 类型
          };
          that.$refs.impedance.impedanceTabData = [];
          that.$store.commit("changeInfo", _obj);
        },
        onCancel() {
          return;
        },
        class: "test",
      });
    },
    getAllData1() {
      this.spinning = true;
      getInpedanceData({ OrderNo: this.form.pdctno }, this.joinFactoryId)
        .then(res => {
          // res.data.stackUpInfo.joinFactoryId,this.joinFactoryId
          if (res.code == 1) {
            //stackUps
            // if (res.data.stackUpInfo.joinFactoryId != this.joinFactoryId &&  this.joinFactoryId!=216) {
            //   this.$store.commit('factoryChange', res.data.stackUpInfo.joinFactoryId)
            //   this.factoryChange()
            // }  2023/2/14
            this.flg1 = 1;
            let _obj = res.data.stackUpInfo;
            let pp = JSON.parse(JSON.stringify(this.form.ppType));
            this.ddta = res.data.stackUps;
            this.$store.commit("changeInfo", _obj);
            if (!res.data.stackUpInfo.ppType) {
              this.$store.commit("changeInfo", { ppType: pp.toString() });
            }
            // this.$refs.formInfo.bulkEvent();
            this.form.boardType = this.form.boardType.toString();
            this.dataSource = res.data.stackUps;
            if (res.data.stackUps.length == 0) {
              this.getAllData();
              this.$refs.formInfo.bulkEvent();
            }
            // if(data){
            //   this.$refs.formInfo.impedanceTabData = res.data.stackIMPs
            // }
            this.$refs.impedance.impedanceTabData = res.data.stackIMPs;
            if (this.$refs.impedance.impedanceTabData.length > 0) {
              this.tableHeight = 565;
              this.foldedornot = true;
            } else {
              this.tableHeight = 730;
              this.foldedornot = false;
            }
            if (this.innerHeight - this.tableHeight - 221 >= 137) {
              this.$refs.laminationInfo.ppheighty = 314;
            } else {
              this.$refs.laminationInfo.ppheighty = 445 - (this.innerHeight - this.tableHeight - 221);
            }
            if (this.innerHeight - this.tableHeight - 231 < 22) {
              this.activeKey = ["0"];
            } else {
              this.activeKey = ["1"];
            }
            let drldata = [];
            if (JSON.parse(localStorage.getItem("drillsData"))) {
              drldata = JSON.parse(localStorage.getItem("drillsData")).drillsData;
            }
            if (!res.data.stackUpDrills || (res.data.stackUpDrills.length == 0 && JSON.parse(localStorage.getItem("drillsData")))) {
              drldata[0].type = "get";
              this.$store.commit("drillHoleTableChange", this.Drilldelttype(drldata));
            } else if (drldata.length && res.data.stackUpDrills && res.data.stackUpDrills.length != drldata.length) {
              drldata[0].type = "get";
              this.$store.commit("drillHoleTableChange", this.Drilldelttype(drldata));
            } else {
              this.$store.commit("drillHoleTableChange", this.Drilldelttype(res.data.stackUpDrills));
              localStorage.removeItem("drillsData");
            }
            if (_obj.layers == 2 && !_obj.pressingThickness) {
              this.count("", "countok");
            }
          } else {
            this.laminationInfoLoading = true;
            let formData = JSON.parse(JSON.stringify(this.form));
            this.boardTypeList.forEach(item => {
              if (item.valueMember == this.form.boardType) {
                formData.boardType = item.text;
              }
            });

            this.autostackinfo.pdctno = formData.pdctno;
            this.autostackinfo.gBStackUp = formData.gBStackUp;
            this.autostackinfo.gbcount = formData.gbcount;
            this.autostackinfo.isPressure = formData.isPressure;
            this.autostackinfo.isChangeLayerPres = formData.isChangeLayerPres;
            this.autostackinfo.layers = formData.layers;
            this.autostackinfo.coreType = formData.boardType;
            this.autostackinfo.coreTypeCode = Number(this.form.boardType);
            this.autostackinfo.pPTypeCode = Number(formData.ppType);
            this.autostackinfo.thicknessOrg = formData.finishBoardThickness;
            this.autostackinfo.tolUp = formData.engineeringHiht.toString();
            this.autostackinfo.tolDown = formData.engineeringLow.toString();
            this.autostackinfo.stackUpDrills = this._drillHoleTableData;
            this.autostackinfo.stackUps = this.dataSource;
            let params = this.autostackinfo;
            params.impOutputs = this.impedanceTab;
            params.joinFactoryId = this.joinFactoryId;
            params.isAuto = false;
            params.mode = Number(this.$route.query.mode) || 0;
            const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject("timeout"), 60000)); // 60秒超时
            const apiRequestPromise = setautostackinfov3(params);
            Promise.race([apiRequestPromise, timeoutPromise])
              .then(res => {
                this.startTimer();
                if (res.code && res.data && JSON.parse(res.data).length) {
                  const data = JSON.parse(res.data);
                  var stackUpData = {
                    stackUpMTR_: "SM",
                    stackUpMTRType_: "通用",
                    stackUpMTRFoil_: "一次印刷",
                    stackUpThichnessORG_: "",
                    stackUpThichnessMIL_: "",
                    stackUpThichnessMM_: "",
                  };
                  if (data.length && data[0].stackdtos) {
                    this.btnData = JSON.parse(data[0].stackdtos);
                    if ([70, 78, 79, 80].includes(Number(this.$route.query.joinFactoryId))) {
                      this.btnData.unshift(stackUpData);
                      this.btnData.push(stackUpData);
                    }
                    this.dataSource = this.btnData;
                  }
                  setTimeout(() => {
                    if (params.layers == 2 && !params.pressingThickness) {
                      this.count("", "countok");
                    } else {
                      this.count("okBtn");
                    }
                  }, 0);
                  this.laminationInfoLoading = false;
                  this.$refs.laminationInfo.clickCounter1 = 0;
                } else {
                  this.getStructure();
                  this.laminationInfoLoading = false;
                  this.spinning = false;
                }
              })
              .catch(err => {
                if (err === "timeout") {
                  this.getStructure();
                  clearInterval(this.timer);
                  this.spinning = false;
                  this.laminationInfoLoading = false;
                }
              })
              .finally(() => {
                clearInterval(this.timer);
                this.spinning = false;
              });

            // this.getStructureHttp()
            // this.searchClick1()
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    getAllData() {
      this.spinning = true;
      this.laminationInfoLoading = true;
      let formData = JSON.parse(JSON.stringify(this.form));
      this.boardTypeList.forEach(item => {
        if (item.valueMember == this.form.boardType) {
          formData.boardType = item.text;
        }
      });
      this.autostackinfo.pdctno = formData.pdctno;
      this.autostackinfo.layers = formData.layers;
      this.autostackinfo.coreType = formData.boardType;
      this.autostackinfo.coreTypeCode = Number(this.form.boardType);
      this.autostackinfo.pPTypeCode = Number(formData.ppType);
      this.autostackinfo.thicknessOrg = formData.finishBoardThickness;
      this.autostackinfo.tolUp = formData.engineeringHiht.toString();
      this.autostackinfo.tolDown = formData.engineeringLow.toString();
      this.autostackinfo.stackUpDrills = this._drillHoleTableData;
      this.autostackinfo.gBStackUp = formData.gBStackUp;
      this.autostackinfo.gbcount = formData.gbcount;
      this.autostackinfo.isPressure = formData.isPressure;
      this.autostackinfo.isChangeLayerPres = formData.isChangeLayerPres;
      this.autostackinfo.stackUps = this.dataSource;

      let params = this.autostackinfo;
      params.impOutputs = this.impedanceTab;
      params.joinFactoryId = this.joinFactoryId;
      params.isAuto = false;
      params.mode = Number(this.$route.query.mode) || 0;
      const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject("timeout"), 60000)); // 60秒超时
      const apiRequestPromise = setautostackinfov3(params);
      Promise.race([apiRequestPromise, timeoutPromise])
        .then(res => {
          this.startTimer();
          if (res.code && res.data && JSON.parse(res.data).length) {
            const data = JSON.parse(res.data);
            var stackUpData = {
              stackUpMTR_: "SM",
              stackUpMTRType_: "通用",
              stackUpMTRFoil_: "一次印刷",
              stackUpThichnessORG_: "",
              stackUpThichnessMIL_: "",
              stackUpThichnessMM_: "",
            };
            if (data.length && data[0].stackdtos) {
              this.btnData = JSON.parse(data[0].stackdtos);
              if ([70, 78, 79, 80].includes(Number(this.$route.query.joinFactoryId))) {
                this.btnData.unshift(stackUpData);
                this.btnData.push(stackUpData);
              }
              this.dataSource = this.btnData;
            }
            setTimeout(() => {
              if (params.layers == 2 && !params.pressingThickness) {
                this.count("", "countok");
              } else {
                this.count("okBtn");
              }
            }, 0);
            this.laminationInfoLoading = false;
            this.$refs.laminationInfo.clickCounter1 = 0;
          } else {
            this.getStructure();
            this.laminationInfoLoading = false;
          }
        })
        .catch(err => {
          if (err === "timeout") {
            this.getStructure(); // 如果超时，执行这个函数
            clearInterval(this.timer);
            this.spinning = false;
          }
        })
        .finally(() => {
          clearInterval(this.timer);
          this.spinning = false;
        });
    },
    getoldData() {
      this.spinning = true;
      getInpedanceData({ OrderNo: this.form.pdctno }, this.joinFactoryId)
        .then(res => {
          if (res.code == 1) {
            // if (res.data.stackUpInfo.joinFactoryId != this.joinFactoryId &&  this.joinFactoryId!=216) {
            //   this.$store.commit('factoryChange', res.data.stackUpInfo.joinFactoryId)
            //   this.factoryChange()
            // }  2023/2/14
            this.flg1 = 1;
            let _obj = res.data.stackUpInfo;
            this.ddta = res.data.stackUps;
            this.$store.commit("changeInfo", _obj);
            this.$refs.formInfo.bulkEvent();
            this.form.boardType = this.form.boardType.toString();
            this.dataSource = res.data.stackUps;
            // if(data){
            //   this.$refs.formInfo.impedanceTabData = res.data.stackIMPs
            // }
            this.$refs.impedance.impedanceTabData = res.data.stackIMPs;
            if (this.$refs.impedance.impedanceTabData.length > 0) {
              this.tableHeight = 565;
              this.foldedornot = true;
            } else {
              this.tableHeight = 730;
              this.foldedornot = false;
            }
            if (this.innerHeight - this.tableHeight - 221 >= 137) {
              this.$refs.laminationInfo.ppheighty = 314;
            } else {
              this.$refs.laminationInfo.ppheighty = 445 - (this.innerHeight - this.tableHeight - 221);
            }
            if (this.$refs.impedance.heighty >= 137) {
              this.$refs.laminationInfo.ppheighty = 314;
            } else {
              this.$refs.laminationInfo.ppheighty = 445 - this.$refs.impedance.heighty;
            }
            localStorage.removeItem("drillsData");
            this.$store.commit("drillHoleTableChange", this.Drilldelttype(res.data.stackUpDrills));
            this.$message.success("调出数据OK");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    startTimer() {
      this.timer = setInterval(function () {
        this.getStructure();
      }, 5000);
    },
    // 调出模板
    searchClick1() {
      let routeList = this.$route.query;
      let params = {};
      params.InCopperThickness = routeList.InCopperThickness;
      params.OutCopperThickness = routeList.OutCopperThickness;
      params.LayerCount = this.form.layers;
      params.BoardThickness = this.form.finishBoardThickness;
      TemplateList(params).then(res => {
        if (res.code) {
          if (res.data.length) {
            this.TemplateData = res.data[0].paramInfo;
            this.getTemplateData();
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    exportData() {
      let data = {},
        formData = JSON.parse(JSON.stringify(this.form)),
        LaminationData = this.dataSource,
        impedanceData = this.$refs.impedance.impedanceTabData;
      // form表单处理
      this.boardTypeList.forEach(item => {
        if (item.text == this.form.boardType) {
          formData.boardType = item.valueMember;
        }
      });
      this.ppTypeList.forEach(item => {
        if (item.text == this.form.ppType) {
          formData.ppType = item.valueMember;
        }
      });

      delete formData.changeS;
      data["stackUpInfo"] = formData; // 表单信息
      data["stackUps"] = LaminationData; // 叠层
      data["StackIMP"] = impedanceData; // 阻抗
      data["stackUpDrills"] = this._drillHoleTableData; // 钻带
      if (!data.stackUps.length) {
        this.$message.error("暂无数据");
        return;
      }
      this.spinning = true;
      getTextOrJson(data)
        .then(res => {
          if (res.code) {
            if (res.data.imp) {
              this.linkDownload(res.data.imp, formData.pdctno, "text");
            }
            if (res.data.stack) {
              this.linkDownload(res.data.stack, formData.pdctno, "json");
            }
            this.$message.success("导出成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    linkDownload(data, title, type) {
      var data_;
      if (type == "json") {
        data_ = JSON.stringify(JSON.parse(data));
      } else {
        data_ = data;
      }
      //encodeURIComponent解决中文乱码
      let uri = "data:text/csv;charset=utf-8,\ufeff" + encodeURIComponent(data_);
      //通过创建a标签实现
      let link = document.createElement("a");
      link.href = uri;
      //对下载的文件命名
      link.download = title + "." + type;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    getTemplate() {
      let routeList = this.$route.query;
      if (this.joinFactoryId == 0 || this.joinFactoryId == 216) {
        this.stackListData.coretype = this.form.boardType;
        this.stackListData.pptype = this.form.ppType;
        this.stackListData.layers = this.form.layers;
        this.stackListData.finishBoardThickness = this.form.finishBoardThickness;
        this.stackListData.InCopperThickness = routeList.InCopperThickness;
        this.stackListData.OutCopperThickness = routeList.OutCopperThickness;
        this.dataVisibleTemplate = true;
      } else {
        this.templateModelVisible = true;
        this.templateForm = {
          finishBoardThickness: 1.6,
          layers: "",
          innerCu: 1,
          ppType: this.ppTypeList[0].valueMember,
          boardType: this.boardTypeList[0].valueMember,
        };
      }
    },
    autoStack() {
      let formData = JSON.parse(JSON.stringify(this.form));
      this.boardTypeList.forEach(item => {
        if (item.valueMember == this.form.boardType) {
          formData.boardType = item.text;
        }
      });
      // this.ppTypeList.forEach(item => {
      //   if (item.text == this.form.ppType) {
      //     formData.ppType = item.valueMember
      //   }
      // })
      // formData.joinFactoryId = this.joinFactoryId
      // formData.layers = Number(this.form.layers)
      this.autostackinfo.pdctno = formData.pdctno;
      this.autostackinfo.layers = formData.layers;
      this.autostackinfo.gBStackUp = formData.gBStackUp;
      this.autostackinfo.gbcount = formData.gbcount;
      this.autostackinfo.isPressure = formData.isPressure;
      this.autostackinfo.isChangeLayerPres = formData.isChangeLayerPres;
      this.autostackinfo.coreType = formData.boardType;
      this.autostackinfo.coreTypeCode = Number(this.form.boardType);
      this.autostackinfo.pPTypeCode = Number(formData.ppType);
      this.autostackinfo.thicknessOrg = formData.finishBoardThickness;
      this.autostackinfo.tolUp = formData.engineeringHiht.toString();
      this.autostackinfo.tolDown = formData.engineeringLow.toString();
      this.autostackinfo.stackUpDrills = this._drillHoleTableData;
      this.autostackinfo.stackUps = this.dataSource;
      this.autostackVisible = true;
    },
    templateHandleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let changeBasicForm = JSON.parse(JSON.stringify(this.templateForm));
          delete changeBasicForm.innerCu;
          this.$store.commit("changeInfo", changeBasicForm);
          let pptype_ = this.ppTypeList.find(item => {
            return item.valueMember == this.templateForm.ppType;
          }).text;
          let coreType = this.boardTypeList.find(item => {
            return item.valueMember == this.templateForm.boardType;
          }).text;
          let params = {
            layer: this.templateForm.layers,
            thick: this.templateForm.finishBoardThickness,
            innerCu: this.templateForm.innerCu.toString(),
            joinFactoryId: Number(this.joinFactoryId),
            coreType: coreType,
            ppType: pptype_,
          };
          getTemplateData(params)
            .then(res => {
              this.dataSource = res.data;
              this.$forceUpdate();
            })
            .finally(() => {
              this.templateModelVisible = false;
            });
        } else {
          return false;
        }
      });
    },
    TemplatehandleOk2() {
      this.spinning = true;
      let form = this.$refs.SelfSupportStack.form;
      let params = {
        layers: Number(form.LayerCount),
        coreType: this.boardTypeList.find(item => {
          return item.valueMember == form.coretype;
        }).text,
        ppType: this.ppTypeList.find(item => {
          return item.valueMember == form.pptype;
        }).text,
        paramInfo: this.$refs.SelfSupportStack.paramInfo,
      };
      getTemplateData4M01(params)
        .then(res => {
          if (res.code) {
            this.dataSource = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.dataVisibleTemplate = false;
        });
    },
    async getTemplateData() {
      this.spinning = true;
      this.ppTypeList = await ppTypes(this.joinFactoryId).then(res => res.data);
      this.boardTypeList = await coreTypes(this.joinFactoryId, 0).then(res => res.data);
      let form = this.form;
      let ppCode_ = this.boardTypeList.find(item => {
        return item.valueMember == form.boardType;
      }).ppCode;
      let params = {
        layers: Number(form.layers),
        coreType: this.boardTypeList.find(item => {
          return item.valueMember == form.boardType;
        }).text,
        ppType: this.ppTypeList.find(item => {
          return item.valueMember == ppCode_;
        }).text,
        paramInfo: this.TemplateData,
        outCopperThickness: Number(this.$route.query.OutCopperThickness),
      };
      getTemplateData4M01(params)
        .then(res => {
          if (res.code) {
            this.dataSource = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.dataVisibleTemplate = false;
        });
    },
    // 获取铜厚下拉选择
    getTemplateCu() {
      // TemplateCu().then(res =>{
      //   if(res.code){
      //     this.cuListData = res.data
      //   }else{
      //     this.$message.error(res.message)
      //   }
      // })
    },
    // 确认叠构
    ConfirmOverlay(val) {
      if (val) {
        this.btnData = JSON.parse(JSON.stringify(val)).stackdtos;
      }
      // this.laminationInfoLoading = true
      // this.dataSource = this.$refs.autostack.strackdatasig.stackdtos
      // this.laminationInfoLoading = false
    },
    Datachange(data) {
      if (data.length) {
        this.tagData = this.tagData.concat(data);
      }
    },
    getTag(data) {
      let groupedData = {};
      data.forEach(item => {
        if (!groupedData[item.id_]) {
          groupedData[item.id_] = { guid: 0, label: "" };
        }
        groupedData[item.id_].guid = item.id_;
        groupedData[item.id_].label += item.label + ";";
      });
      let result = Object.keys(groupedData).map(id_ => ({
        guid: groupedData[id_].guid,
        label: groupedData[id_].label,
      }));
      return result;
    },
    async okBtn() {
      if (!this.$refs.autostack.proOrderId) {
        this.$message.warning("请先选择叠层数据");
        return;
      }
      if ([70, 78, 79, 80].includes(Number(this.$route.query.joinFactoryId))) {
        const stackUpData = {
          stackUpMTR_: "SM",
          stackUpMTRType_: "通用",
          stackUpMTRFoil_: "一次印刷",
          stackUpThichnessORG_: "",
          stackUpThichnessMIL_: "",
          stackUpThichnessMM_: "",
        };
        this.btnData.unshift(stackUpData);
        this.btnData.push(stackUpData);
      }
      this.dataSource = this.btnData;
      this.autostackVisible = false;
      await this.count("okBtn");
      this.dataSave();
      this.$refs.laminationInfo.clickCounter1 = 0;
    },
    getInfoClick() {
      let str = this.$refs.formInfo.form.pdctno;
      // let url = 'H2GInterface://?TCL=EMS&JOB=' + str.toLocaleLowerCase() +'&EMS_flows=AutoTool4StackUp&AreaID=0'
      let url = "H2GInterface://?TCL=EMS&JOB=" + str.toLocaleLowerCase() + "&EMS_flows=StackUpInfo&AreaID=" + this.$route.query.factory;
      window.open(url, "_blank");
    },
    expandIcon1() {
      if (this.foldedornot) {
        return (
          <a>
            <a-icon type="down" style="margin:0 5px 31px 0 " />
          </a>
        );
      } else {
        return (
          <a>
            <a-icon type="up" style="margin:0 5px 31px 0 " />
          </a>
        );
      }
    },
    panelClick(val) {
      if (val.length) {
        this.tableHeight = 565;
        this.foldedornot = true;
      } else {
        this.tableHeight = 730;
        this.foldedornot = false;
      }
      if (this.innerHeight - this.tableHeight - 221 >= 137) {
        this.$refs.laminationInfo.ppheighty = 320;
      } else {
        this.$refs.laminationInfo.ppheighty = 457 - (this.innerHeight - this.tableHeight - 221);
      }
    },
    handleAdd() {
      if (this.form.pressingThickness == "") {
        this.$error({
          title: "错误",
          content: "压合板厚数据异常，请调整",
        });
        return;
      }
      this.$refs.impedance.addImpedance();
      if (this.$refs.impedance.impedanceTabData.length == 1) {
        this.tableHeight = 565;
        this.foldedornot = true;
      }
      if (this.innerHeight - this.tableHeight - 221 >= 137) {
        this.$refs.laminationInfo.ppheighty = 320;
      } else {
        this.$refs.laminationInfo.ppheighty = 457 - (this.innerHeight - this.tableHeight - 221);
      }
      if (this.innerHeight - this.tableHeight - 231 < 22) {
        this.activeKey = ["0"];
      } else {
        this.activeKey = ["1"];
      }
    },
    AntiTrigger(ind) {
      let impedanceData = JSON.parse(JSON.stringify(this.$refs.impedance.impedanceTabData));
      let medium = this.dataSource[ind].stackUpMTRFoil_; // oz值
      var str = "L" + this.dataSource[ind].stackUpLayerNo_; // 层数
      let index = null;
      for (var a = 0; a < impedanceData.length; a++) {
        if (impedanceData[a].imp_ControlLay_ == str) {
          index = a;
        }
      }
      let control = impedanceData[index]?.imp_ControlLay_.split("L")[1];
      this.someList.forEach(res => {
        if (control == 1 || control == this.form.layers) {
          // O
          if (res.iO_ == "910" && res.cu_ == medium && res.holeCu_ == "20") {
            impedanceTabData[index].imp_C1_ = res.c1_ + "";
            impedanceTabData[index].imp_C2_ = res.c2_ + "";
            impedanceTabData[index].imp_C3_ = res.c3_ + "";
            impedanceTabData[index].imp_T1_ = res.t1_ + "";
            impedanceTabData[index].imp_BC_ = res.t1_ + "";
            impedanceTabData[index].imp_CEr_ = res.cEr_ + "";
          }
        } else {
          if (res.iO_ == "911" && res.cu_ == medium && res.holeCu_ == "20") {
            impedanceTabData[index].imp_C1_ = res.c1_ + "";
            impedanceTabData[index].imp_C2_ = res.c2_ + "";
            impedanceTabData[index].imp_C3_ = res.c3_ + " ";
            impedanceTabData[index].imp_T1_ = res.t1_ + " ";
            impedanceTabData[index].imp_BC_ = res.t1_ + "";
            impedanceTabData[index].imp_CEr_ = res.cEr_ + "";
          }
        }
      });
      this.$forceUpdate();
      this.$refs.impedance.impedanceTabData = impedanceTabData;
      // this.$refs.impedance.controlChange(impedanceData[index],index)
      // this.$refs.impedance.lineWidthChage(impedanceData[index],index)
      // this.$refs.impedance.lwChange(impedanceData[index],index)
    },
  },
  mounted() {
    if (!this.form.pdctno) {
      localStorage.removeItem("drillsData");
    }
    document.getElementById("touchmove").onmousedown = this.textWidthChange;
    this.$refs.impedance.heighty = this.innerHeight - this.tableHeight - 220;
    document.getElementById("touchmove").onmouseup = this.textWidthChange1;
    const newData = this._drillHoleTableData.filter(item => {
      return item.drillName && item.startLayer && item.endLayer && !item.editable;
    });
    this.$store.commit("drillHoleTableChange", []);
    this.impedanceTab = this.$refs.impedance.impedanceTabData;
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("mouseup", this.mouseup, true);
  },
};
</script>
<style scoped lang="less">
.ant-form-item {
  margin-bottom: 0;
}
.popupstyle {
  .ant-select {
    width: 100%;
  }
}
.hide {
  display: none;
}
/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  /* border-color: #ff9900; */
  border: 1px solid red;
}
/deep/.ant-select-focused .ant-select-selection {
  border: 1px solid red !important;
}
/deep/.ant-input:focus {
  border: 1px solid red !important;
}

/deep/.titleBox {
  .ant-checkbox-input:focus + .ant-checkbox-inner {
    border: 1px solid #ff9900 !important;
  }
}
/deep/.btnStyle {
  background: #ff9900;
  color: #fff;
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/.ant-select-selection-selected-value {
  font-weight: 500;
}

/deep/ .ant-collapse-icon-position-right .ant-collapse-item {
  .ant-collapse-header {
    padding: 6px 18px;
    padding-right: 40px;
  }
  .ant-collapse-content-box {
    padding: 0;
  }
  .ant-table-thead > tr > th {
    padding: 5px 0;
    color: #000000;
    text-align: center;
    // font-weight: 500;
  }
  .ant-table-tbody > tr > td {
    padding: 0;
    color: #000000;
    height: 36px;
  }
}
.impedance {
  background-color: #ffffff;
  overflow: hidden;
  // height: 100%;
  // width:100%;
  //height:828px;
  min-width: 1670px;
  .title {
    width: 100%;
    padding: 0 10px;
    // background: linear-gradient(rgb(239, 245, 255) 0px, rgb(224, 236, 255) 100%) repeat-x;
    background-repeat: repeat-x;
    .head-menu {
      height: auto;
      line-height: 30px;
      color: rgb(14, 45, 95);
      // font-weight: 500;
    }
  }
  .footer {
    width: 100%;
    position: relative;
    /deep/.ant-collapse-extra {
      position: absolute;
      top: 0;
      left: 5%;
    }
    /deep/.ant-collapse-header {
      height: auto;
      line-height: 30px;
      color: rgb(14, 45, 95);
      // font-weight: 500;
      padding: 0px 18px !important;
    }
  }
}
/deep/ .ant-table-body {
  background: #d6d6d6;
}
/deep/ .ant-table-tbody .M_bg {
  background: #8fbc8b !important;
}
/deep/ .ant-table-tbody {
  // font-weight: 600!important;
  color: #000000;
  .OZ {
    .material_bg {
      background: #f4a460 !important;
    }
  }
  .SM {
    .material_bg {
      background: green !important;
    }
  }
  .Core {
    .material_bg {
      background: #f0e68c !important;
    }
  }
  .PP {
    .material_bg {
      background: #9acd32 !important;
    }
  }
  .material_bg {
    .ant-select-selection {
      background: none;
    }
  }
}
</style>
