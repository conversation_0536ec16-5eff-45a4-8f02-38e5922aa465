<!-- 市场管理 - 订单预审- 按钮 -->
<template>
  <div class="active" ref="active">
    <!-- v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreChaXun')" -->
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreGetOrder')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.PreGetOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="TakeOrderClick"> 取单(Q) </a-button>
    </div>

    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreStart')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.PreStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="MakeStartClick"> 开始(S) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreBack')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.PreBack') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ChargebackClick"> 回退 </a-button>
    </div>

    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreEnd')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.PreEnd') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="modifyInfoClick"> 完成 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreEQ')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.PreEQ') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="wenkeClick"> 问客 </a-button>
    </div>
    <!-- <div class="box" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PcborderToNew')"
      :class='checkPermission("MES.MarketModule.Prequalificationproduction.PcborderToNew")?"showClass":""'> 
        <a-button type="primary" @click="orderaccess">
          订单接入
        </a-button>
      </div>      -->
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.SendEq')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.SendEq') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Editemail')"> 编辑邮箱 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreContractReviewInfo')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.PreContractReviewInfo') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ReviewSheet"> 合同评审 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.ReleaseWarning')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.ReleaseWarning') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Releasewarning"> 解除警告 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.FilesWereAdded')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.FilesWereAdded') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="fileswereadded"> 文件追加 </a-button>
      <a-upload
        accept="*.*"
        name="file"
        ref="fileRef1"
        :customRequest="httpRequest6"
        :file-list="fileList6"
        :show-upload-list="false"
        v-show="false"
        :maxCount="1"
        @change="handleChange6"
      >
        <a-button style="width: 80px"><a-icon type="upload" /></a-button>
      </a-upload>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Prequalificationproduction.OrderSplit')"
      :class="checkPermission('MES.MarketModule.Prequalificationproduction.OrderSplit') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="OrderSplitting"> 订单拆分 </a-button>
    </div>
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px; width: 100px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="OrderSplitting" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.OrderSplit')"
              >订单拆分</a-menu-item
            >
            <a-menu-item @click="fileswereadded" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.FilesWereAdded')"
              >文件追加</a-menu-item
            >
            <a-menu-item @click="Releasewarning" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreEQ')">解除警告</a-menu-item>
            <a-menu-item @click="ReviewSheet" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreContractReviewInfo')"
              >合同评审</a-menu-item
            >
            <a-menu-item @click="$emit('Editemail')" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.SendEq')"
              >编辑邮箱</a-menu-item
            >
            <a-menu-item @click="wenkeClick" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreEQ')">问客</a-menu-item>
            <a-menu-item @click="modifyInfoClick" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreEnd')">完成</a-menu-item>
            <a-menu-item @click="ChargebackClick" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreBack')">回退</a-menu-item>
            <a-menu-item @click="MakeStartClick" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreStart')">开始(S)</a-menu-item>
            <a-menu-item @click="TakeOrderClick" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreGetOrder')"
              >取单(Q)</a-menu-item
            >
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <!-- <div class="box" > 
        <a-button type="primary" @click="checkInquiry" >
          指示检查
        </a-button>
      </div> -->

    <!-- <div class="box" >        
        <a-button type="primary" @click="uploadPCBFileClick"  style='background: #ff9900;color: white;border-color: #ff9900;text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);box-shadow: 0 2px 0 rgb(0 0 0 / 5%);'>
        <img src="@/assets/img/upload.png" alt="" style="width: 16px;margin-right: 5px;">
          上传
        </a-button>
        <a-upload 
          accept=".rar,.zip"
          name="file"
          :multiple="false"
          :customRequest="customRequest"
          @change="handleChangeImg"
          :showUploadList="false"
          ref="fileRef"
          :before-upload="beforeUpload1"
          v-show="false"
        > 
        <a-button style="width: 80px;"><a-icon type="upload" /></a-button>
        </a-upload>
      </div>   

      <div class="box">
        <a-button type="primary" @click='openApp' >
          打开APP
        </a-button>
      </div>      -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.ModifyPar')">
        <a-button type="primary" @click="EditParametersClick" >
          编辑参数
        </a-button>
      </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.FixFinish')">
        <a-button type="primary" @click='RepairCompletedClick' disabled>
          返修完成
        </a-button>
      </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.MattersNeedingAttention')">
        <a-button type="primary" @click='mattersNeedingAttentionClick' disabled>
          注意事项
        </a-button>
      </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetWenKeUrl')">
      <a-button type="primary" @click="wenkeClick" disabled>
        问客
      </a-button>
    </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetFabricatiUrl')">
      <a-button type="primary" @click="ProductionStandardClick" disabled>
        制作标准
      </a-button>
    </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetRuleShowInfoPpe')">
      <a-button type="primary" @click='CustomerRulesClick' disabled>
        客户规则
      </a-button>
    </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetAutoStack') || checkPermission('MES.EngineeringModule.EngineeringProduction.GetImp')" style="display: flex; justify-content: center">
        <div style="width: 65px">
          <a-button type="primary"  @click='GenerateStackClick' style="width: 100%; padding-left: 5px; border-bottom-right-radius: 0;border-top-right-radius: 0" disabled>
            {{selectValue}}
          </a-button>
        </div>
        <div style="width: 15px">
          <a-select @change="selectChange" v-model="selectValue" style="width: 100%;" class="selctClass" :dropdownMatchSelectWidth="false" disabled>
            <a-select-option value="生成叠层" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetAutoStack')" style='border-radius: 8px;' >生成叠层</a-select-option>
            <a-select-option value="叠层阻抗" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetImp')" style='border-radius: 8px;'>叠层阻抗</a-select-option>
          </a-select>
        </div>
    </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetAutoToolPnl')">
      <a-button type="primary" @click='CuttingClick' disabled >
        拼版开料
      </a-button>
    </div> -->

    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetFixRecord')">
      <a-button type="primary" @click='RepairRecordClick' disabled >
        返修记录
      </a-button>
    </div> -->
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { uploadPCBFile, fileswereadded } from "@/services/mkt/PrequalificationProduction.js";
import protocolCheck from "@/utils/protocolcheck";
export default {
  name: "MakeAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      selectValue: "生成叠层",
      imgName: "",
      orderId: "",
      advanced: false,
      ids1: "",
      fileList6: [],
      width: 762,
      showBtn: false,
      nums: "",
      buttonsmenu: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      this.nums = num;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      this.handleResize();
      window.addEventListener("resize", this.handleResize, true);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      let num = "";
      if (!this.advanced) {
        num = 8 * 104;
      } else {
        num = (elements.length + 1) * 104;
      }
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            if (i < 6) {
              elements[i].style.display = "inline-block";
            }
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.advanced = false;
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                if (i < 6) {
                  elements[i].style.display = "inline-block";
                }
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          if (i < 6) {
            elements[i].style.display = "inline-block";
          }
        }
        this.buttonsmenu = false;
      }
    },
    handleChange6({ fileList }) {
      this.fileList6 = fileList;
    },
    async httpRequest6(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      this.$emit("load", true);
      await fileswereadded(this.ids1, formData)
        .then(res => {
          if (res.code == 1) {
            data.onSuccess(res.data);
            this.$message.success("文件追加成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.$emit("load", false);
        });
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      this.nums = elements.length;
      if (this.advanced) {
        width_ = 1000;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
      this.handleResize();
    },
    checkPermission,

    // 取单
    TakeOrderClick() {
      this.$emit("TakeOrderClick");
    },
    // 开始
    MakeStartClick() {
      this.$emit("MakeStartClick");
    },
    // 回退
    ChargebackClick() {
      this.$emit("ChargebackClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 完成（审核通过）
    modifyInfoClick() {
      this.$emit("modifyInfoClick");
    },
    // 上传pcb文件
    uploadPCBFileClick() {
      this.$emit("uploadPCBFileClick");
    },
    checkInquiry() {
      this.$emit("checkInquiry");
    },
    // 打开APP
    openApp() {
      protocolCheck(
        "WebshellEmsCam:// 用户 密码 jbid",
        fail => {
          console.log("fail", fail);
          // 没有安装 弹窗显示 引导去下载
          // this.$message.error('未安装注册')
        },
        succ => {
          // 安装则直接打开
          console.log("succ", succ);
        }
      );
    },
    // 编辑参数
    // EditParametersClick(){
    //   this.$emit('EditParametersClick')
    // },
    // 返修完成
    RepairCompletedClick() {
      this.$emit("RepairCompletedClick");
    },
    // 问客
    wenkeClick() {
      this.$emit("wenkeClick");
    },
    //订单接入
    orderaccess() {
      this.$emit("orderaccess");
    },
    ReviewSheet() {
      this.$emit("ReviewSheet");
    },
    //文件追加
    fileswereadded() {
      this.$emit("fileswereadded");
    },
    //解除警告
    Releasewarning() {
      this.$emit("Releasewarning");
    },
    // 制作标准
    ProductionStandardClick() {
      this.$emit("ProductionStandardClick");
    },
    // 注意事项
    mattersNeedingAttentionClick() {
      this.$emit("mattersNeedingAttentionClick");
    },
    // 生成叠层
    GenerateStackClick() {
      if (this.selectValue == "生成叠层") {
        this.$emit("GenerateStackClick");
      } else {
        this.$emit("StackImpedanceClick");
      }
    },
    // 客户规则
    CustomerRulesClick() {
      this.$emit("CustomerRulesClick");
    },
    // 叠层阻抗
    // StackImpedanceClick(){
    //   this.$emit('StackImpedanceClick')
    // },
    // 拼版开料层
    CuttingClick() {
      this.$emit("CuttingClick");
    },
    // 返修记录
    RepairRecordClick() {
      this.$emit("RepairRecordClick");
    },
    handleChangeImg(info) {
      this.imgName = `${info.file.name}`.substring(0, `${info.file.name}`.indexOf("."));
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;

        // console.log("str_esc:",str_esc)
        if (!isFileType) {
          _this.$message.error("只支持.rar或.zip格式文件");
          reject();
          // return isFileType
        } else {
          resolve();
        }
      });
    },
    customRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      // console.error(formData)
      uploadPCBFile(this.orderId, formData).then(res => {
        if (res.code) {
          this.$message.success("上传成功");
          this.$emit("getOrderList");
        } else {
          this.$message.error(res.message);
        }
      });
      // console.log(this.enterOrderForm.pdctno,this.enterOrderForm.tgzOssPath)
    },
    clickUpload(id) {
      this.orderId = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    clickUpload1(id) {
      this.ids1 = id;
      this.$refs.fileRef1.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    OrderSplitting() {
      this.$emit("OrderSplitting");
    },
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-btn {
  padding: 0;
}
.active {
  // width:762px;
  // display: flex;
  // float: right;
  // flex-wrap: wrap;
  // justify-content: space-around;
  // transition: all .2s;
  // -moz-transition: all .2s;
  // -webkit-transition: all .2s;
  // -o-transition: all .2s;
  // padding: 0 30px;
  height: 100%;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    width: 90px;
    margin-top: 11px;
    // margin-right: 20px;
    text-align: center;

    .ant-btn {
      width: 90%;
    }
    .ant-btn-primary:hover {
      background-color: #ffb029 !important;
      border-color: #ffb029 !important;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        // background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
