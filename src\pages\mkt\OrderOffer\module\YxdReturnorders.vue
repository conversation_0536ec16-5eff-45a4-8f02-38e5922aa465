<template>
  <a-spin :spinning="spin">
    <div>
      <div ref="SelectBox3">
        <a-row>
          <a-col :span="2">
            <a-form-model-item label="返单更改" :label-col="{ span: 20 }" :wrapper-col="{ span: 4 }">
              <a-checkbox v-model="Returnorderdata.isReorder" @change="rechange()"></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="2">
            <a-form-model-item label="引用全套" :label-col="{ span: 20 }" :wrapper-col="{ span: 4 }">
              <a-checkbox v-model="Returnorderdata.isQuanTao" @change="qtchange()"></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="客户代码"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.custNo && iseval(requiredLinkConfigoffer.custNo.isNullRules) ? 'required' : ''"
            >
              <a-select
                v-model="Returnorderdata.custNo"
                showSearch
                optionFilterProp="lable"
                :disabled="discustNo"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
              >
                <a-select-option v-for="(item, index) in custNoList" :key="index" :value="item" :lable="item">
                  {{ item }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item label="文件上传" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <span style="display: flex">
                <a-upload
                  accept=".zip,.rar"
                  :multiple="false"
                  :file-list="fileList"
                  :show-upload-list="false"
                  :customRequest="downloadFilesCustomRequest"
                  :before-upload="beforeUpload"
                  @change="handleChange1"
                >
                  <a-button style="margin-left: -3px" :disabled="!Returnorderdata.isQuanTao && !Returnorderdata.isReorder">
                    <a-icon type="upload" /><span style="font-size: 13px">文件上传</span>
                  </a-button>
                </a-upload>
                <a-spin style="position: relative; top: 7px; left: 10px" :spinning="filespinning"></a-spin>
                <a-icon v-if="done == 'ok'" style="font-size: 20px; color: #ff9900; margin-top: 9px; margin-left: 5px" type="check-square"></a-icon>
                <a-icon v-if="done == 'no'" style="font-size: 20px; color: #ababab; margin-top: 9px; margin-left: 5px" type="close-square"></a-icon>
              </span>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item
              label="客户型号"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigoffer.customerModel && iseval(requiredLinkConfigoffer.customerModel.isNullRules) ? 'required' : ''"
            >
              <a-input
                v-model="Returnorderdata.customerModel"
                allowClear
                :autoFocus="true"
                @keyup.enter="searchReturn()"
                :disabled="requiredLinkConfigoffer.customerModel.prohibit == 1"
                :title="Returnorderdata.customerModel"
                @change="custchange()"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8" class="prostyle">
            <a-form-model-item
              label="生产型号"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigoffer.proOrderNo && iseval(requiredLinkConfigoffer.proOrderNo.isNullRules) ? 'required' : ''"
            >
              <div style="display: flex">
                <a-input
                  v-model="Returnorderdata.proOrderNo"
                  allowClear
                  :autoFocus="true"
                  @keyup.enter="searchReturn()"
                  @change="prochange()"
                  :disabled="requiredLinkConfigoffer.proOrderNo.prohibit == 1"
                />
                <a-button type="primary" style="margin-left: 2px; padding: 0 15px" @click="searchReturn">查询</a-button>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-show="Returnorderdata.isReorder">
          <a-col :span="4">
            <a-form-model-item
              label="更改后客户型号"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.newCustomerModel && iseval(requiredLinkConfigoffer.newCustomerModel.isNullRules) ? 'required' : ''"
            >
              <div class="editWrapper">
                <a-input
                  v-model="Returnorderdata.newCustomerModel"
                  allowClear
                  :disabled="requiredLinkConfigoffer.newCustomerModel.prohibit == 1"
                ></a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="更改后生产型号"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.newProOrderNo && iseval(requiredLinkConfigoffer.newProOrderNo.isNullRules) ? 'required' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="Returnorderdata.newProOrderNo" allowClear :disabled="requiredLinkConfigoffer.newProOrderNo.prohibit == 1"></a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="更改前版本"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.oldRev && iseval(requiredLinkConfigoffer.oldRev.isNullRules) ? 'required' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="Returnorderdata.oldRev" allowClear :disabled="requiredLinkConfigoffer.oldRev.prohibit == 1"></a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="更改后版本"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.newRev && iseval(requiredLinkConfigoffer.newRev.isNullRules) ? 'required' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="Returnorderdata.newRev" allowClear :disabled="requiredLinkConfigoffer.newRev.prohibit == 1"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="更改前表面处理"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.surfaceFinish && iseval(requiredLinkConfigoffer.surfaceFinish.isNullRules) ? 'required' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="Returnorderdata.surfaceFinish"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox3"
                  :disabled="requiredLinkConfigoffer.surfaceFinish.prohibit == 1"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.SurfaceFinish)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="更改后表面处理"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.surfaceFinishNew && iseval(requiredLinkConfigoffer.surfaceFinishNew.isNullRules) ? 'required' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="Returnorderdata.surfaceFinishNew"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox3"
                  :disabled="requiredLinkConfigoffer.surfaceFinishNew.prohibit == 1"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.SurfaceFinish)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-show="Returnorderdata.isReorder">
          <a-col :span="24" class="notestyle1">
            <a-form-model-item
              label="更改内容"
              :label-col="{ span: 2 }"
              :wrapper-col="{ span: 22 }"
              :class="requiredLinkConfigoffer.reMark && iseval(requiredLinkConfigoffer.reMark.isNullRules) ? 'required' : ''"
            >
              <a-textarea
                :auto-size="{ minRows: 1, maxRows: 3 }"
                v-model="Returnorderdata.reMark"
                style="width: 100%; margin-bottom: 0px"
                :disabled="requiredLinkConfigoffer.reMark.prohibit == 1"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="客户交期" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <a-date-picker
                style="margin-right: 0.5%; margin-left: 0.5%"
                valueFormat="YYYY-MM-DD"
                placeholder="客户交期"
                v-model="Returnorderdata.deliveryDate"
                @change="onChange5"
                allowClear
                :disabled="requiredLinkConfigoffer.deliveryDate.prohibit == 1"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="市场交期" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <a-date-picker
                style="margin-right: 0.5%; margin-left: 0.5%"
                valueFormat="YYYY-MM-DD"
                placeholder="市场交期"
                v-model="Returnorderdata.marketDeliveryTime"
                allowClear
                @change="onChange4"
                :disabled="requiredLinkConfigoffer.marketDeliveryTime.prohibit == 1"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="客户订单号"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.custPo && iseval(requiredLinkConfigoffer.custPo.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.custPo" allowClear :disabled="requiredLinkConfigoffer.custPo.prohibit == 1"> </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="合同号"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.contractNumber && iseval(requiredLinkConfigoffer.contractNumber.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.contractNumber" allowClear :disabled="requiredLinkConfigoffer.contractNumber.prohibit == 1">
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="测试方式"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.flyingProbe && iseval(requiredLinkConfigoffer.flyingProbe.isNullRules) ? 'required' : ''"
            >
              <a-select
                v-model="Returnorderdata.flyingProbe"
                showSearch
                allowClear
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
                :disabled="requiredLinkConfigoffer.flyingProbe.prohibit == 1"
              >
                <a-select-option v-for="(item, index) in mapKey(selectOption.FlyingProbe)" :key="index" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="装运方式"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.shipmentTerm && iseval(requiredLinkConfigoffer.shipmentTerm.isNullRules) ? 'required' : ''"
            >
              <a-select
                v-model="Returnorderdata.shipmentTerm"
                showSearch
                allowClear
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
                :disabled="requiredLinkConfigoffer.shipmentTerm.prohibit == 1"
              >
                <a-select-option v-for="(item, index) in mapKey(selectOption.ShipmentTerm)" :key="index" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item
              label="交货数量"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.num && iseval(requiredLinkConfigoffer.num.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.num" allowClear @change="numchange" :disabled="requiredLinkConfigoffer.num.prohibit == 1"> </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="备品数量"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.sparePartNum && iseval(requiredLinkConfigoffer.sparePartNum.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.sparePartNum" allowClear :disabled="requiredLinkConfigoffer.sparePartNum.prohibit == 1"> </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="面积"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.para4Area_ && iseval(requiredLinkConfigoffer.para4Area_.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.para4Area_" allowClear :disabled="requiredLinkConfigoffer.para4Area_.prohibit == 1"> </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="交货单位"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.delType && iseval(requiredLinkConfigoffer.delType.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.delType" :disabled="requiredLinkConfigoffer.delType.prohibit == 1" allowClear> </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="出货板方式"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.shipmentType && iseval(requiredLinkConfigoffer.shipmentType.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.shipmentType" :disabled="requiredLinkConfigoffer.shipmentType.prohibit == 1" allowClear> </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="拼版数量"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.orderPcs && iseval(requiredLinkConfigoffer.orderPcs.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.orderPcs" :disabled="requiredLinkConfigoffer.orderPcs.prohibit == 1" allowClear> </a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item
              label="加工工厂"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.orderDirection && iseval(requiredLinkConfigoffer.orderDirection.isNullRules) ? 'required' : ''"
            >
              <a-select
                v-model="Returnorderdata.orderDirection"
                :disabled="requiredLinkConfigoffer.orderDirection.prohibit == 1"
                showSearch
                allowClear
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
              >
                <a-select-option v-for="(item, index) in mapKey(selectOption.OrderDirection)" :key="index" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="自定分类"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="
                requiredLinkConfigoffer.customClassification && iseval(requiredLinkConfigoffer.customClassification.isNullRules) ? 'required' : ''
              "
            >
              <a-select
                v-model="Returnorderdata.customClassification"
                showSearch
                @change="customchange"
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
              >
                <a-select-option v-for="(item, index) in Rebates" :key="index" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="参考价格1"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.referencePrice1 && iseval(requiredLinkConfigoffer.referencePrice1.isNullRules) ? 'required' : ''"
            >
              <a-input
                v-model="Returnorderdata.referencePrice1"
                allowClear
                :disabled="requiredLinkConfigoffer.referencePrice1.prohibit == 1"
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="参考价格2"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.referencePrice2 && iseval(requiredLinkConfigoffer.referencePrice2.isNullRules) ? 'required' : ''"
            >
              <a-input
                v-model="Returnorderdata.referencePrice2"
                allowClear
                :disabled="requiredLinkConfigoffer.referencePrice2.prohibit == 1"
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="客户物料名称"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="
                requiredLinkConfigoffer.customerMaterialName && iseval(requiredLinkConfigoffer.customerMaterialName.isNullRules) ? 'required' : ''
              "
            >
              <a-input
                v-model="Returnorderdata.customerMaterialName"
                :disabled="requiredLinkConfigoffer.customerMaterialName.prohibit == 1"
                :title="Returnorderdata.customerMaterialName"
                allowClear
              >
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="终端客户名称"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.endCustName && iseval(requiredLinkConfigoffer.endCustName.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.endCustName" allowClear :disabled="requiredLinkConfigoffer.endCustName.prohibit == 1"> </a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="接单工厂" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <a-select
                v-model="Returnorderdata.contractFactoryId"
                showSearch
                allowClear
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
              >
                <a-select-option v-for="(item, index) in mapKey(selectOption.ContractFactoryId)" :key="index" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="客户物料号"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.customerMaterialNo && iseval(requiredLinkConfigoffer.customerMaterialNo.isNullRules) ? 'required' : ''"
            >
              <a-input
                v-model="Returnorderdata.customerMaterialNo"
                allowClear
                :disabled="requiredLinkConfigoffer.customerMaterialNo.prohibit == 1"
                @keyup.enter="searchReturn()"
              >
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item
              label="历史价格"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.unitPrice && iseval(requiredLinkConfigoffer.unitPrice.isNullRules) ? 'required' : ''"
            >
              <a-input v-model="Returnorderdata.unitPrice" allowClear :disabled="requiredLinkConfigoffer.unitPrice.prohibit == 1"> </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" v-show="Returnorderdata.isReorder">
            <a-form-model-item
              label="旧版本处理方式"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.oldBoardDoType && iseval(requiredLinkConfigoffer.oldBoardDoType.isNullRules) ? 'required' : ''"
            >
              <a-select
                v-model="Returnorderdata.oldBoardDoType"
                showSearch
                allowClear
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
              >
                <a-select-option v-for="(item, index) in mapKey(selectOption.OldBoardDoType)" :key="index" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" v-show="Returnorderdata.isReorder">
            <a-form-model-item
              label="库存板"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.inventoryBoard && iseval(requiredLinkConfigoffer.inventoryBoard.isNullRules) ? 'required' : ''"
            >
              <a-select
                v-model="Returnorderdata.inventoryBoard"
                showSearch
                allowClear
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
              >
                <a-select-option v-for="(item, index) in mapKey(selectOption.InventoryBoard)" :key="index" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" v-show="Returnorderdata.isReorder">
            <a-form-model-item
              label="在线板"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigoffer.onlineBoard && iseval(requiredLinkConfigoffer.onlineBoard.isNullRules) ? 'required' : ''"
            >
              <a-select
                v-model="Returnorderdata.onlineBoard"
                showSearch
                allowClear
                optionFilterProp="lable"
                style="width: 100%"
                :getPopupContainer="() => this.$refs.SelectBox3"
              >
                <a-select-option
                  v-for="(item, index) in mapKey(selectOption.OnlineBoard)"
                  :key="index"
                  :value="item.value"
                  :title="item.lable"
                  :lable="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12" class="notestyle">
            <a-form-model-item
              label="市场备注信息"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              :class="requiredLinkConfigoffer.mktNote && iseval(requiredLinkConfigoffer.mktNote.isNullRules) ? 'required' : ''"
            >
              <a-textarea
                :auto-size="{ minRows: 1, maxRows: 3 }"
                :disabled="requiredLinkConfigoffer.mktNote.prohibit == 1"
                v-model="Returnorderdata.mktNote"
                allowClear
                style="width: 100%; margin-bottom: 0px"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" class="notestyle">
            <a-form-model-item label="型号备注" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="Returnorderdata.note" allowClear style="width: 100%; margin-bottom: 0px" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </div>
      <div class="projectackend faclass" style="margin-left: 20px">
        <div style="display: flex">
          <div>
            <a-table
              :columns="fdcolumns"
              :dataSource="fandandataSource"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :scroll="{ y: 108 }"
              :customRow="onClickRow1"
              :pagination="false"
              :rowClassName="isRedRow6"
              :loading="spinningfand"
            >
            </a-table>
          </div>
          <div style="margin-left: 12px" v-if="inventoryData?.length">
            <a-table
              :columns="inventorycolumns"
              :dataSource="inventoryData"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :scroll="{ y: 108 }"
              :pagination="false"
              :loading="spinningfand"
            >
            </a-table>
          </div>
        </div>
        <a-divider />
        <a-table
          :columns="Accesscolumns"
          :dataSource="AccessdataSource"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 320, x: 300 }"
          :pagination="false"
          :loading="accload"
          :class="AccessdataSource.length ? 'accstyle' : ''"
        >
          <template slot="delQty" slot-scope="text, record, index">
            <a-input v-model="record.delQty" @blur="yxdchange(record, index, 1, 'delQty')" @keyup.enter.native="$event.target.blur()"></a-input>
          </template>
          <template slot="pcsPrice_" slot-scope="text, record, index">
            <a-input v-model="record.pcsPrice_" @blur="yxdchange(record, index, 2, 'pcsPrice_')" @keyup.enter.native="$event.target.blur()"></a-input>
          </template>
          <template slot="squareMetrePrice" slot-scope="text, record, index">
            <a-input
              v-model="record.squareMetrePrice"
              @blur="yxdchange(record, index, 3, 'squareMetrePrice')"
              @keyup.enter.native="$event.target.blur()"
            ></a-input>
          </template>
          <template slot="custNo" slot-scope="text, record, index">
            <a-select v-model="record.custNo" showSearch @blur="yxdchange(record, index, 4, 'custNo')" optionFilterProp="lable" style="width: 100%">
              <a-select-option v-for="(item, index) in custNoList" :key="index" :value="item" :lable="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </template>
          <template slot="custPo" slot-scope="text, record, index">
            <a-input v-model="record.custPo" @blur="yxdchange(record, index, 5, 'custPo')" @keyup.enter.native="$event.target.blur()"></a-input>
          </template>
          <template slot="payFreight" slot-scope="text, record, index">
            <a-input
              v-model="record.payFreight"
              @blur="yxdchange(record, index, 0, 'payFreight')"
              @keyup.enter.native="$event.target.blur()"
            ></a-input>
          </template>
          <template slot="moldFee_" slot-scope="text, record, index">
            <a-input v-model="record.moldFee_" @blur="yxdchange(record, index, 0, 'moldFee_')" @keyup.enter.native="$event.target.blur()"></a-input>
          </template>
          <template slot="flyPrice_" slot-scope="text, record, index">
            <a-input v-model="record.flyPrice_" @blur="yxdchange(record, index, 0, 'flyPrice_')" @keyup.enter.native="$event.target.blur()"></a-input>
          </template>
          <template slot="flimPrice_" slot-scope="text, record, index">
            <a-input
              v-model="record.flimPrice_"
              @blur="yxdchange(record, index, 0, 'flimPrice_')"
              @keyup.enter.native="$event.target.blur()"
            ></a-input>
          </template>
          <template slot="testPrice_" slot-scope="text, record, index">
            <a-input
              v-model="record.testPrice_"
              @blur="yxdchange(record, index, 0, 'testPrice_')"
              @keyup.enter.native="$event.target.blur()"
            ></a-input>
          </template>
          <template slot="expeditePrice_" slot-scope="text, record, index">
            <a-input
              v-model="record.expeditePrice_"
              @blur="yxdchange(record, index, 0, 'expeditePrice_')"
              @keyup.enter.native="$event.target.blur()"
            ></a-input>
          </template>
          <template slot="engPrice_" slot-scope="text, record, index">
            <a-input v-model="record.engPrice_" @blur="yxdchange(record, index, 0, 'engPrice_')" @keyup.enter.native="$event.target.blur()"></a-input>
          </template>
          <template slot="allOtherPrice_" slot-scope="text, record, index">
            <a-input
              v-model="record.allOtherPrice_"
              @blur="yxdchange(record, index, 0, 'allOtherPrice_')"
              @keyup.enter.native="$event.target.blur()"
            ></a-input>
          </template>
          <template slot="Action" slot-scope="text, record, index">
            <a-popconfirm title="确定删除该条订单吗?" ok-text="确定" @confirm="DeleteRow(record, index)">
              <a href="#"><a-icon type="delete"></a-icon></a>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
    </div>
  </a-spin>
</template>
<script>
import axios from "axios";
import { inquirycheckorderfile } from "@/services/mkt/Inquiry.js";
import { buttonCheck } from "@/services/mkt/OrderManagement.js";
import {
  pcbordertonewtest3,
  nopeorderdelete,
  pcbordertonewtest2,
  pcbordertonewsearchinfokc,
  orderPriceCalc,
  orderDayCalc,
  setorderpricecalcresult4Parameter,
  nopeprecheck,
  reorderpriceinfo,
  setreorderpriceinfo,
  pcbordertest,
  preaddnopenew,
  preaddnopealternew,
  indicationCheck,
  verifyFinishedOrder,
  settoolcheck,
  contractNo,
  custgrouplist,
} from "@/services/mkt/OrderReview.js";
import { upLoadEnquiryFile } from "@/services/mkt/Inquiry.js";
import { mapState } from "vuex";
const fdcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "客户代码",
    align: "left",
    ellipsis: true,
    dataIndex: "custNo",
    width: 70,
  },
  {
    title: "客户型号",
    align: "left",
    ellipsis: true,
    width: 200,
    dataIndex: "customerModel",
  },
  {
    title: "生产型号",
    align: "left",
    dataIndex: "proOrderNo",
    ellipsis: true,
    width: 160,
  },
  {
    title: "销售订单号",
    ellipsis: true,
    align: "left",
    dataIndex: "salesOrder",
    width: 150,
  },
  {
    title: "下单时间",
    ellipsis: true,
    align: "left",
    dataIndex: "checkEndTime",
    width: 150,
  },
  {
    title: "数据来源",
    ellipsis: true,
    align: "left",
    dataIndex: "sourceStr",
    width: 70,
  },
  {
    title: "订单类型",
    ellipsis: true,
    align: "left",
    dataIndex: "orderTypeStr",
    width: 70,
  },
  {
    title: "客户PO",
    align: "left",
    dataIndex: "custPo",
    ellipsis: true,
    width: 100,
  },
];
const inventorycolumns = [
  {
    title: "仓库",
    align: "left",
    ellipsis: true,
    dataIndex: "warehouse",
    width: 60,
  },
  {
    title: "数量",
    align: "left",
    ellipsis: true,
    dataIndex: "qtyOfUnit",
    width: 30,
  },
  {
    title: "版本",
    align: "left",
    ellipsis: true,
    dataIndex: "partRev",
    width: 35,
  },
  {
    title: "型号",
    align: "left",
    ellipsis: true,
    dataIndex: "partNum",
    width: 100,
  },
  {
    title: "工厂",
    align: "left",
    ellipsis: true,
    dataIndex: "factory",
    width: 140,
  },
];
const Accesscolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "订单号",
    align: "left",
    ellipsis: true,
    dataIndex: "orderNo",
    width: 120,
  },

  {
    title: "客户代码",
    align: "left",
    ellipsis: true,
    dataIndex: "custNo",
    width: 100,
    scopedSlots: { customRender: "custNo" },
  },
  {
    title: "客户PO",
    align: "left",
    ellipsis: true,
    dataIndex: "custPo",
    width: 200,
    scopedSlots: { customRender: "custPo" },
  },
  {
    title: "产品型号",
    align: "left",
    ellipsis: true,
    dataIndex: "proOrderNo",
    width: 200,
  },
  {
    title: "客户物料编码",
    align: "left",
    ellipsis: true,
    dataIndex: "customerMaterialNo",
    width: 200,
  },
  {
    title: "客户型号",
    align: "left",
    ellipsis: true,
    dataIndex: "customerModel",
    width: 200,
  },
  {
    title: "单位",
    align: "left",
    ellipsis: true,
    dataIndex: "delType",
    width: 60,
  },
  {
    title: "数量",
    align: "left",
    ellipsis: true,
    dataIndex: "delQty",
    width: 70,
    scopedSlots: { customRender: "delQty" },
  },
  {
    title: "单价",
    align: "left",
    ellipsis: true,
    dataIndex: "pcsPrice_",
    width: 70,
    scopedSlots: { customRender: "pcsPrice_" },
  },
  {
    title: "平米价",
    align: "left",
    ellipsis: true,
    dataIndex: "squareMetrePrice",
    scopedSlots: { customRender: "squareMetrePrice" },
    width: 70,
  },
  {
    title: "运费",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "payFreight" },
    width: 70,
  },
  // {
  //   title: "模具费",
  //   align: "left",
  //   ellipsis: true,
  //   scopedSlots: { customRender: "moldFee_" },
  //   width: 70,
  // },
  {
    title: "飞测费",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "flyPrice_" },
    width: 70,
  },
  {
    title: "菲林费",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "flimPrice_" },
    width: 70,
  },
  {
    title: "测试费用",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "testPrice_" },
    width: 70,
  },
  {
    title: "加急费",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "expeditePrice_" },
    width: 70,
  },
  {
    title: "工程费",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "engPrice_" },
    width: 70,
  },
  {
    title: "其他费用",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "allOtherPrice_" },
    width: 70,
  },
  {
    title: "操作",
    align: "center",
    ellipsis: true,
    scopedSlots: { customRender: "Action" },
    width: 40,
    fixed: "right",
  },
];
export default {
  name: "MergerReturnorders",
  props: ["selectOption", "requiredLinkConfigoffer", "contractNoyxd"],
  data() {
    return {
      spin: false,
      custNoList: [],
      discustNo: true,
      accload: false,
      Rebates: [],
      Returnorderdata: {
        custNo: "",
        newProOrderNo: "",
        newCustomerModel: "",
        oldRev: "",
        newRev: "",
        surfaceFinish: "",
        surfaceFinishNew: "",
        reMark: "",
        deliveryDate: "",
        marketDeliveryTime: "",
        custPo: "",
        contractNumber: "",
        flyingProbe: "",
        shipmentTerm: "",
        num: "",
        sparePartNum: "",
        shipmentType: "",
        orderPcs: "",
        orderDirection: "",
        customClassification: "返单无改",
        customerModel: "",
        endCustName: "",
        customerMaterialNo: "",
        customerMaterialName: "",
        mktNote: "",
        note: "",
        unitPrice: null,
        proOrderNo: "",
        isQuanTao: false,
      },
      userid: "",
      fileList: [],
      done: "",
      spinningfand: false,
      isFileType: false,
      FilePath: "",
      filespinning: false,
      fandandataSource: [],
      inventoryData: [],
      AccessdataSource: [],
      RowsData: {},
      fdcolumns,
      inventorycolumns,
      Accesscolumns,
      isSystem: "",
      complete: false,
    };
  },
  watch: {
    "Returnorderdata.customClassification"(newVal, oldVal) {
      if (newVal !== oldVal && newVal && oldVal) {
        if (JSON.stringify(this.RowsData) != "{}" && this.fandandataSource.length) {
          this.getreturndata(this.RowsData);
        }
      }
    },
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  created() {
    this.dereturnorderok = this.debounce(this.returnorderok, 500);
    localStorage.removeItem("Accessdata");
  },
  mounted() {
    this.userid = this.user.factoryId;
    if (JSON.stringify(this.selectOption) != "{}") {
      this.Rebates = this.mapKey(this.selectOption.CustomClassification).filter(item => item.value == "返单无改" || item.value == "新单");
    }
  },
  methods: {
    //组合合同
    Combination() {
      let ids = [];
      this.AccessdataSource.forEach(item => {
        ids.push(item.id);
      });
      if (ids.length == 0) {
        this.$message.error("当前没有可组合合同订单");
        return;
      }
      this.spinningfand = true;
      setorderpricecalcresult4Parameter(ids).then(res => {
        if (res.code) {
          contractNo(ids)
            .then(res => {
              if (res.code) {
                if (this.AccessdataSource[0].joinFactoryId == 67) {
                  this.$emit("LTsalescontract", ids, "LT", this.AccessdataSource[0].joinFactoryId, this.AccessdataSource[0].custNo);
                } else {
                  this.$emit("YXDform", ids, "YXD", this.AccessdataSource[0].joinFactoryId, this.AccessdataSource[0].custNo);
                }
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.spinningfand = false;
            });
        } else {
          this.$message.error("价格明细写入数据失败");
          this.spinningfand = false;
        }
      });
    },
    DeleteRow(record, index) {
      nopeorderdelete(record.id).then(res => {
        if (res.code) {
          this.$message.success("删除成功");
          this.AccessdataSource.splice(index, 1);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    yxdchange(record, index, changetype, pricetype, ischange) {
      let data = JSON.parse(localStorage.getItem("Accessdata"));
      let ind_ = index;
      let params = {
        delQty: record.delQty,
        pcsPrice_: record.pcsPrice_,
        squareMetrePrice: record.squareMetrePrice,
        id: record.id,
        changetype: changetype,
        payFreight: record.payFreight,
        moldFee_: record.moldFee_,
        flyPrice_: record.flyPrice_,
        flimPrice_: record.flimPrice_,
        testPrice_: record.testPrice_,
        expeditePrice_: record.expeditePrice_,
        engPrice_: record.engPrice_,
        allOtherPrice_: record.allOtherPrice_,
        custNo: record.custNo,
        custPo: record.custPo,
      };
      if (data[ind_][pricetype] != record[pricetype]) {
        if ((pricetype == "custNo" || pricetype == "custPo") && ischange != "1") {
          for (let i = 0; i < this.AccessdataSource.length; i++) {
            if (i > ind_) {
              this.AccessdataSource[i][pricetype] = record[pricetype];
              this.yxdchange(this.AccessdataSource[i], i, changetype, pricetype, "1");
            }
          }
        }
        this.accload = true;
        setreorderpriceinfo(params)
          .then(res => {
            if (res.code) {
              if (ischange != "1") {
                this.$message.success("修改成功");
              }
              this.AccessdataSource[ind_] = res.data;
              localStorage.setItem("Accessdata", JSON.stringify(this.AccessdataSource));
            } else {
              this.$message.error(res.message);
              this.AccessdataSource = JSON.parse(localStorage.getItem("Accessdata"));
            }
          })
          .finally(() => {
            this.accload = false;
          });
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("data") != -1) {
        newIsNullRules = newIsNullRules.replace(/data/g, "this.Returnorderdata");
      }
      return eval(newIsNullRules);
    },
    returnorderok(type) {
      if (this.fandandataSource.length == 0) {
        this.$message.error("无数据不能进行该操作");
        return;
      }
      if (JSON.stringify(this.RowsData) == "{}") {
        this.$message.error("请选择需要接入的订单");
        return;
      }
      if (this.Returnorderdata.isReorder && this.Returnorderdata.customClassification == "新单" && !this.FilePath) {
        this.$message.error("选择新单必须上传文件!");
        return;
      }
      let redatas = this.Returnorderdata;
      for (let key in this.requiredLinkConfigoffer) {
        let r = "";
        if (this.requiredLinkConfigoffer[key].linkRules) {
          r = eval(this.requiredLinkConfigoffer[key].linkRules);
        }
        if (
          (this.requiredLinkConfigoffer[key] && this.iseval(this.requiredLinkConfigoffer[key].isNullRules) && !redatas[key]) ||
          (this.requiredLinkConfigoffer[key].linkRules && redatas[key] && !r.test(redatas[key]))
        ) {
          this.$message.warning(this.requiredLinkConfigoffer[key].message);
          return;
        }
      }
      redatas.FilePath = this.FilePath;
      redatas.pcbFilePath = this.FilePath ? this.FilePath : redatas.pcbFilePath;
      if (!redatas.referencePrice1) {
        redatas.referencePrice1 = null;
      }
      if (!redatas.referencePrice2) {
        redatas.referencePrice2 = null;
      }
      if (!redatas.unitPrice) {
        redatas.unitPrice = null;
      }
      if (type == "wc") {
        this.complete = true;
      } else {
        this.complete = false;
      }
      let params = {};
      params.custPo = redatas.custPo;
      params.proOrderNo = redatas.proOrderNo;
      params.num = redatas.num;
      params.joinFactoryId = redatas.joinFactoryId;
      params.contractFactoryId = Number(params.contractFactoryId);
      this.$emit("loadchange", type, "true");
      nopeprecheck(params).then(res => {
        if (res.code) {
          if (!res.message) {
            this.$emit("loadchange", type, "true");
            this.fdcompleted();
          } else {
            this.$emit("loadchange", type, "false");
            this.$emit("checkmodal", res.message);
          }
        } else {
          this.$emit("loadchange", type, "false");
          this.$message.warning(res.message);
        }
      });
    },
    fdcompleted() {
      let redatas = this.Returnorderdata;
      redatas.note = redatas.note ? redatas.note.replace(/\s+/g, " ") : ""; //空白字符转换为空格
      redatas.mktNote = redatas.mktNote ? redatas.mktNote.replace(/\s+/g, " ") : ""; //空白字符转换为空格
      if (this.isSystem) {
        pcbordertest(redatas)
          .then(res => {
            if (res.code) {
              this.$message.success(res.message);
              this.accload = true;
              reorderpriceinfo(res.data.split(",")[0])
                .then(res => {
                  if (res.code) {
                    this.AccessdataSource.push(res.data);
                    localStorage.setItem("Accessdata", JSON.stringify(this.AccessdataSource));
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.accload = false;
                });
              localStorage.setItem("Returnorderdata", JSON.stringify(redatas));
              this.Returnorderdata = {};
              this.done = "";
              this.$set(this.Returnorderdata, "customClassification", "返单无改");
              let redata = JSON.parse(localStorage.getItem("Returnorderdata")) || {};
              this.$set(this.Returnorderdata, "deliveryDate", redata.deliveryDate);
              this.$set(this.Returnorderdata, "marketDeliveryTime", redata.marketDeliveryTime);
              this.$set(this.Returnorderdata, "contractNumber", redata.contractNumber);
              this.$set(this.Returnorderdata, "custPo", redata.custPo);
              this.fandandataSource = [];
              this.inventoryData = [];
              this.discustNo = true;
              this.RowsData = {};
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.$emit("loadchange", "", "false");
          });
      } else if ((!this.isSystem && redatas.isReorder) || redatas.isQuanTao) {
        preaddnopealternew(redatas)
          .then(res => {
            if (res.code) {
              this.$message.success(res.message);
              // this.$emit('getOrderList')
              // if(!redatas.deliveryDate){
              //   this.deliverycalculation(res.data.split(',')[0])
              // }else{
              //   this.valuationClick1(res.data.split(',')[0])
              // }
              this.accload = true;
              reorderpriceinfo(res.data.split(",")[0])
                .then(res => {
                  if (res.code) {
                    this.AccessdataSource.push(res.data);
                    localStorage.setItem("Accessdata", JSON.stringify(this.AccessdataSource));
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.accload = false;
                });
              localStorage.setItem("Returnorderdata", JSON.stringify(redatas));
              this.Returnorderdata = {};
              this.done = "";
              this.$set(this.Returnorderdata, "customClassification", "返单无改");
              let redata = JSON.parse(localStorage.getItem("Returnorderdata")) || {};
              this.$set(this.Returnorderdata, "deliveryDate", redata.deliveryDate);
              this.$set(this.Returnorderdata, "marketDeliveryTime", redata.marketDeliveryTime);
              this.$set(this.Returnorderdata, "contractNumber", redata.contractNumber);
              this.$set(this.Returnorderdata, "custPo", redata.custPo);
              this.fandandataSource = [];
              this.inventoryData = [];
              this.discustNo = true;
              this.RowsData = {};
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.$emit("loadchange", "", "false");
          });
      } else if (!this.isSystem && !redatas.isReorder) {
        preaddnopenew(this.Returnorderdata)
          .then(res => {
            if (res.code) {
              this.$message.success(res.message);
              // this.$emit('getOrderList')
              // if(!redatas.deliveryDate){
              //   this.deliverycalculation(res.data.split(',')[0])
              // }else{
              //   this.valuationClick1(res.data.split(',')[0])
              // }
              this.accload = true;
              reorderpriceinfo(res.data.split(",")[0])
                .then(res => {
                  if (res.code) {
                    this.AccessdataSource.push(res.data);
                    localStorage.setItem("Accessdata", JSON.stringify(this.AccessdataSource));
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.accload = false;
                });
              localStorage.setItem("Returnorderdata", JSON.stringify(redatas));
              this.Returnorderdata = {};
              this.done = "";
              this.$set(this.Returnorderdata, "customClassification", "返单无改");
              let redata = JSON.parse(localStorage.getItem("Returnorderdata")) || {};
              this.$set(this.Returnorderdata, "deliveryDate", redata.deliveryDate);
              this.$set(this.Returnorderdata, "marketDeliveryTime", redata.marketDeliveryTime);
              this.$set(this.Returnorderdata, "contractNumber", redata.contractNumber);
              this.$set(this.Returnorderdata, "custPo", redata.custPo);
              this.fandandataSource = [];
              this.inventoryData = [];
              this.discustNo = true;
              this.RowsData = {};
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.$emit("loadchange", "", "false");
          });
      }
    },
    returncomplete() {
      this.dereturnorderok("wc");
    },
    //返单计价
    valuationClick1(id) {
      buttonCheck(id, "PriceCalc").then(res => {
        if (res.code) {
          if (res.data.length == 0) {
            orderPriceCalc(id).then(res => {
              if (res.code) {
                if (this.complete) {
                  this.rehandleok(id);
                }
              }
            });
          }
        }
      });
    },
    //返单操作报价完成
    rehandleok(id) {
      indicationCheck(id, 1).then(res => {
        if (res.code) {
          if (res.data.length == 0) {
            verifyFinishedOrder(id).then(res => {
              if (res.code) {
                settoolcheck(id).then(res => {});
              }
            });
          }
        }
      });
    },
    //交期计算返单
    deliverycalculation(idss) {
      orderDayCalc(idss).then(res => {
        if (res.code) {
          this.valuationClick1(idss);
        }
      });
    },
    custchange() {
      this.fandandataSource = [];
      this.inventoryData = [];
      this.discustNo = true;
      this.Returnorderdata.custNo = "";
      this.RowsData = {};
      this.RowsData.salesOrder = "";
    },
    prochange() {
      this.fandandataSource = [];
      this.inventoryData = [];
      this.discustNo = true;
      this.Returnorderdata.custNo = "";
      this.RowsData = {};
      this.RowsData.salesOrder = "";
    },
    onClickRow1(record) {
      return {
        on: {
          click: () => {
            this.RowsData = record;
            this.done = "";
            this.$emit("risk", record);
          },
        },
      };
    },
    isRedRow6(record) {
      let strGroup = [];
      if (record.salesOrder && this.RowsData.salesOrder == record.salesOrder) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    getreturndata(record) {
      record.customClassification = this.Returnorderdata.customClassification;
      record.isReorder = this.Returnorderdata.isReorder;
      record.isQuanTao = this.Returnorderdata.isQuanTao;
      this.spin = true;
      pcbordertonewtest3(record)
        .then(res => {
          if (res.code == 0) {
            this.$message.error(res.message);
            this.discustNo = true;
          } else {
            this.Returnorderdata = res.data;
            if (res.message) {
              this.$message.info({
                content: () => res.message,
                class: "custom-class",
              });
            }
            this.Returnorderdata.contractFactoryId = this.Returnorderdata.contractFactoryId.toString();
            this.isSystem = this.Returnorderdata.isSystem;
            this.discustNo = false;
            custgrouplist(this.Returnorderdata.custNo, this.Returnorderdata.joinFactoryId).then(res => {
              if (res.code) {
                this.custNoList = res.data;
              } else {
                this.custNoList = [];
              }
            });
            let redata = JSON.parse(localStorage.getItem("Returnorderdata")) || {};
            if (this.Returnorderdata.deliveryDate == "" || !this.Returnorderdata.deliveryDate) {
              this.$set(this.Returnorderdata, "deliveryDate", redata.deliveryDate);
            }
            if (this.Returnorderdata.marketDeliveryTime == "" || !this.Returnorderdata.marketDeliveryTime) {
              this.$set(this.Returnorderdata, "marketDeliveryTime", redata.marketDeliveryTime);
            }
            if (this.Returnorderdata.contractNumber == "" || !this.Returnorderdata.contractNumber) {
              this.$set(this.Returnorderdata, "contractNumber", redata.contractNumber);
            }
            if (this.Returnorderdata.custPo == "" || !this.Returnorderdata.custPo) {
              this.$set(this.Returnorderdata, "custPo", redata.custPo);
            }
          }
        })
        .finally(() => {
          this.spin = false;
        });
    },
    rechange() {
      if (this.Returnorderdata.isReorder) {
        this.$set(this.Returnorderdata, "customClassification", "返单有改");
        this.$set(this.Returnorderdata, "isQuanTao", false);
        this.Rebates = this.mapKey(this.selectOption.CustomClassification).filter(item => item.value != "返单无改");
      } else {
        this.$set(this.Returnorderdata, "customClassification", "返单无改");
        this.Rebates = this.mapKey(this.selectOption.CustomClassification).filter(item => item.value == "返单无改" || item.value == "新单");
      }
      this.FilePath = "";
      this.done = "";
      this.$set(this.Returnorderdata, "newCustomerModel", this.Returnorderdata.customerModel);
    },
    customchange() {
      if (!this.Returnorderdata.isReorder) {
        this.FilePath = "";
        this.done = "";
        this.$set(this.Returnorderdata, "newCustomerModel", this.Returnorderdata.customerModel);
      }
    },
    qtchange() {
      if (this.Returnorderdata.isQuanTao) {
        this.$set(this.Returnorderdata, "customClassification", "新单");
        this.$set(this.Returnorderdata, "isReorder", false);
      } else if (this.Returnorderdata.isReorder) {
        this.$set(this.Returnorderdata, "customClassification", "返单有改");
      } else {
        this.$set(this.Returnorderdata, "customClassification", "返单无改");
      }
      this.FilePath = "";
      this.done = "";
      this.$set(this.Returnorderdata, "newCustomerModel", this.Returnorderdata.customerModel);
    },
    onChange4(value, dateString) {
      this.Returnorderdata.marketDeliveryTime = dateString;
    },
    onChange5(value, dateString) {
      this.Returnorderdata.deliveryDate = dateString;
    },
    searchReturn() {
      if (!this.Returnorderdata.isReorder) {
        this.Returnorderdata.isReorder = false;
      }
      if (!this.Returnorderdata.custNo) {
        this.Returnorderdata.custNo = "";
      }
      if (!this.Returnorderdata.preAccount) {
        this.Returnorderdata.preAccount = "";
      }
      if (!this.Returnorderdata.customerModel) {
        this.Returnorderdata.customerModel = "";
      }
      if (!this.Returnorderdata.customerMaterialNo) {
        this.Returnorderdata.customerMaterialNo = "";
      }
      let formData = {
        proOrderNo: this.Returnorderdata.proOrderNo,
        isReorder: this.Returnorderdata.isReorder,
        custNo: this.Returnorderdata.custNo,
        preAccount: this.Returnorderdata.preAccount,
        customerModel: this.Returnorderdata.customerModel,
        customerMaterialNo: this.Returnorderdata.customerMaterialNo,
      };
      this.RowsData = {};
      this.spinningfand = true;
      const fecth = this.userid == 58 || this.userid == 59 ? pcbordertonewsearchinfokc : pcbordertonewtest2;
      //pcbordertonewtest2
      fecth(formData)
        .then(res => {
          if (res.code) {
            if (this.userid == 58 || this.userid == 59) {
              this.fandandataSource = res.data.order;
              this.inventoryData = res.data.inventory;
            } else {
              this.fandandataSource = res.data;
              this.inventoryData = [];
            }
            if (this.fandandataSource.length == 0) {
              this.$message.error("没有找到相关数据");
              this.RowsData = {};
            }
          } else {
            this.$message.error(res.message);
            this.fandandataSource = [];
            this.inventoryData = [];
            this.discustNo = true;
            this.RowsData = {};
          }
        })
        .finally(() => {
          this.spinningfand = false;
        });
    },
    mapKey(data) {
      if (!data || data.length == 0) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    numchange() {
      let redata = this.Returnorderdata;
      if (redata.delType == "SET" && redata.setBoardHeight && redata.setBoardWidth && redata.num) {
        redata.para4Area_ = this.getFloat((redata.setBoardHeight * redata.setBoardWidth * redata.num) / 1000000.0, 4);
      } else if (redata.delType == "PCS" && redata.setBoardHeight && redata.setBoardWidth && redata.num) {
        redata.para4Area_ = this.getFloat((redata.setBoardHeight * redata.setBoardWidth * redata.num) / 1000000.0 / (redata.su || 1), 4);
        this.$set(this.Returnorderdata, "para4Area_", redata.para4Area_);
      } else {
        this.$set(this.Returnorderdata, "para4Area_", "");
      }
    },
    handleChange1({ fileList }, data) {
      if (this.isFileType) {
        this.fileList = fileList;
        if (this.fileList.length == 0) {
          this.FilePath = "";
        }
        if (this.fileList.length > 1) {
          this.fileList.splice(0, 1);
          return;
        }
      }
    },
    beforeUpload(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
      if (!this.isFileType) {
        this.$message.error("文件确认只支持.rar或.zip格式文件");
        return false;
      }
      const filesize = Number(file.size / 1024 / 1024) < 150;
      if (!filesize) {
        this.isFileType = false;
        this.$message.error("文件大小不能超过150M");
        return false;
      }
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    //分段上传MD5
    async downloadFilesCustomRequest(data) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("CustNo", this.Returnorderdata.custNo);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      let params = {
        fileName: data.file.name,
        MD5Code: GUID,
        custNo: this.Returnorderdata.custNo,
      };
      let name = data.file.name.split(".");
      this.$set(this.Returnorderdata, "newCustomerModel", data.file.name.split("." + name[name.length - 1])[0]);
      this.filespinning = true;
      this.done = "";
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              this.FilePath = res.data.split(",")[0];
              this.done = "ok";
              this.filespinning = false;
              this.$set(this.Returnorderdata, "pcbFileName", data.file.name.split("." + name[name.length - 1])[0]);
            }
          } else {
            this.$set(this.Returnorderdata, "newCustomerModel", "");
            this.$message.error(res.message);
            this.done = "no";
            i = shardCount;
            this.filespinning = false;
          }
        });
      }
    },
  },
};
</script>
<style scoped lang="less">
.custom-class {
  color: red;
}
.notestyle1 {
  /deep/.ant-form-item-label {
    width: 7%;
  }
  /deep/.ant-form-item-control-wrapper {
    width: 93%;
  }
}
.notestyle {
  /deep/.ant-form-item-label {
    width: 14%;
  }
  /deep/.ant-form-item-control-wrapper {
    width: 86%;
  }
}
.accstyle {
  /deep/.ant-input {
    height: 25px;
  }
  /deep/.ant-select-selection__rendered {
    height: 25px !important;
    line-height: 25px !important;
  }
  /deep/.ant-select-selection--single {
    position: relative;
    height: 25px;
    cursor: pointer;
  }
  /deep/.ant-table-fixed-right {
    tr.ant-table-row-hover td {
      background: #dfdcdc !important;
    }
  }
  /deep/.ant-table-body {
    min-height: 125px;
    overflow: auto;
    tr.ant-table-row-hover td {
      background: #dfdcdc !important;
    }
  }
}
/deep/.ant-input-affix-wrapper .ant-input:not(:last-child) {
  padding-right: 20px;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-divider-horizontal {
  height: 4px;
  background-color: #c9c9c9;
  margin: 4px 0;
}
/deep/.ant-table-wrapper {
  border-top: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/.ant-form-item-label > label {
  font-size: 13px;
}
.required {
  /deep/.ant-form-item-label > label {
    color: red !important;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.rowBackgroundColor {
  background: #c9c9c9 !important;
}
/deep/ .ant-table {
  .ant-table-thead > tr > th {
    padding: 5px 2px;
    border-right: 1px solid #efefef;
    height: 36px;
  }
  .customerModel {
    padding: 0 0 0 2px !important;
  }
  .ant-table-tbody > tr > td {
    padding: 5px 2px;
    border-right: 1px solid #efefef;
    height: 36px;
  }
  tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
  tr.ant-table-row-hover td {
    background: #c9c9c9;
  }
  .ant-table-selection-col {
    width: 20px !important;
  }
}
</style>
