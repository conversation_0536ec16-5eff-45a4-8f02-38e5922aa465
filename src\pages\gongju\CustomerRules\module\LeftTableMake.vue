<!-- 工具管理- 客户规则- 客户代码列表 左边 -->
<template>
  <a-table
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{y:720}"
      :customRow="onClickRow"
      :pagination="pagination"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      :rowClassName="isRedRow"
      @change="handleTableChange"
  >
    <span slot="num" slot-scope="text, record, index" class="topCss">
      {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
    </span>
<!--    <template slot="isCustRule" slot-scope="text,record">-->
<!--      <a-tooltip title="客户规则" v-if="record.isCustRule == 1">-->
<!--        <a-icon type="file-text" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop='CustomerRulesClick(record)'/>-->
<!--      </a-tooltip>-->
<!--    </template>-->
    <template slot="isCustRule" slot-scope="text,record">
      <a-checkbox :checked="record.isCustRule">
      </a-checkbox>
    </template>
  </a-table>
  
</template>

<script>
import {checkPermission} from "@/utils/abp";
import { setEngineeringMake } from "@/utils/request";
import {downFile} from "@/services/projectMake";
export default {
  props:{
    dataSource: {
      type: Array,
      require: true,
      default: () => []
    },
    orderListTableLoading: {
      type: Boolean,
      require: true
    },
    columns:{
      type: Array,
      require: true
    },
    rowKey:{
      require:true
    },
    editFlag:{
      type: Boolean,
      require: true
    },
    pagination:{
      type: Object,
      require:true,
      default: ()=>{
        return { 
          pagination: {
          pageSize: 20,
          current: 1,
          total:0,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["20", "50", "100"],//每页中显示的数据
          showTotal: (total) => `总计 ${total} 条`,
          },
          selectedRows:{},
        }
      }
    },
  },
  name: "LeftTableMake",
  data() {
    return {
      selectedRowKeysArray: [],
      selectedRowsData:[],
      activeClass:'smallActive',
      custNo:'',
      queryData:{},
    }
  },
  watch:{
    'pagination':{
      handler(val){
        console.log(val)
      }
    }
  },

  created() {

  },
  methods: {
    checkPermission,
    isRedRow(record){
      let strGroup = []
      let str =[]
      if ( record.custNo == this.custNo) {
        strGroup.push('rowBackgroundColor')
      }
      return str.concat(strGroup)
    },
    onClickRow(record) {
      return {
        on: {
        click: () => {
          if (this.editFlag) {
            this.selectedRowKeysArray=[];
            this.selectedRowsData={};          
            let keys = [];
            keys.push(record.custNo);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record
            this.$emit('getOrderDetail', record.custNo,record.tradeType)
            this.$emit('getJobInfo', record.custNo,record.tradeType)
            this.custNo = record.custNo
            this.tradeType = record.tradeType
            // console.log('this.selectedRowsData',this.selectedRowsData)
          } else {
            this.$message.warning('编辑状态不可选择其他订单')
          }
        }
        }
      }
    },
    handleTableChange(pagination){
      this.$emit('tableChange', pagination)
    },
  },
  mounted() {

  }
}
</script>


<style lang="less" scoped>

/deep/ .ant-table {
  
  .fontRed {
    td {
      color: #DC143C;
    }
  }
  .cookieIdColor{
    td:first-child{
      //border-left:2px solid #ff9900!important;
      background: #ff9900!important;
    }
    //td:nth-child(2){
    //  color:#ff9900!important;
    //}
  }
  .displayFlag{
    display: none;
  }
}
.peopleTag{
  margin:0;
  padding:0;
  width:24px;
  border-radius: 12px;
  background: #2D221D;
  border-color: #2D221D;
  color:#FF9900;
  text-align: center;
  margin-left:2px;
}

</style>
