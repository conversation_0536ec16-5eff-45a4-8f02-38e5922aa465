<template>
  <div class="impedancereport">
    <div class="topcontent">
      <a-input
        style="width: 200px; margin-right: 0.5%"
        placeholder="生产编号"
        v-model="formData.OrderNo"
        allowClear
        @keyup.enter="searchClick"
      ></a-input>
      <a-button type="primary" style="margin-right: 0.5%" @click="searchClick">调出数据</a-button>
      <a-button type="primary" style="margin-right: 0.5%" @click="exportReport">导出报告</a-button>
      <a-button type="primary" v-if="!edit" style="margin-right: 0.5%" @click="editdata">编辑</a-button>
      <a-button type="primary" v-if="edit" style="margin-right: 0.5%" @click="cancel">取消</a-button>
      <a-button type="primary" style="margin-right: 0.5%" @click="savedata">保存</a-button>
    </div>
    <div class="maincontent">
      <div class="Leftlist">
        <a-table
          :columns="maincolumns"
          :pagination="false"
          :dataSource="Datasource"
          rowKey="id"
          :scroll="{ y: 753, x: 600 }"
          class="maintable"
          :loading="loading"
        >
          <span slot="impTolerance_" slot-scope="text, record">
            <a-input v-if="edit" v-model="record.impTolerance_" allowClear />
            <span v-else>{{ record.impTolerance_ }}</span>
          </span>
          <span slot="imp_linS_" slot-scope="text, record">
            <a-input v-if="edit" v-model="record.imp_linS_" allowClear />
            <span v-else>{{ record.imp_linS_ }}</span>
          </span>
          <span slot="impTolerance_" slot-scope="text, record">
            <a-input v-if="edit" v-model="record.impTolerance_" allowClear />
            <span v-else>{{ record.impTolerance_ }}</span>
          </span>
          <span slot="resist_" slot-scope="text, record">
            <a-input v-if="edit" v-model="record.resist_" allowClear />
            <span v-else>{{ record.resist_ }}</span>
          </span>
        </a-table>
      </div>
    </div>
  </div>
</template>
<script>
import { impreportlist, setimpreportlist, reliabilityimpreport } from "@/services/scgl/QualityManagement/Impedancereport";
const maincolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "阻抗类型",
    align: "left",
    width: 80,
    ellipsis: true,
    dataIndex: "imp_Type_",
  },
  {
    title: "控制层",
    align: "left",
    width: 80,
    ellipsis: true,
    dataIndex: "imp_ControlLay_",
  },
  {
    title: "上参",
    align: "left",
    width: 80,
    ellipsis: true,
    dataIndex: "imp_UpLay_",
  },
  {
    title: "下参",
    align: "left",
    width: 80,
    ellipsis: true,
    dataIndex: "imp_DownLay_",
  },
  {
    title: "线宽",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "impTolerance_" },
  },
  {
    title: "线距",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "imp_linS_" },
  },
  {
    title: "阻值",
    align: "left",
    width: 80,
    ellipsis: true,
    dataIndex: "actual_",
  },
  {
    title: "阻抗公差",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "impTolerance_" },
  },
  {
    title: "实例",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "resist_" },
  },
];
export default {
  name: "Impedancereport",
  data() {
    return {
      formData: {},
      Datasource: [],
      edit: false,
      maincolumns,
      loading: false,
    };
  },
  methods: {
    handleResize() {
      let maintable = document.getElementsByClassName("maintable")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      let maintable1 = document.getElementsByClassName("maintable")[0];
      if (this.Datasource.length != 0) {
        maintable.style.height = window.innerHeight - 170 + "px";
        maintable1.style.height = window.innerHeight - 130 + "px";
      } else {
        maintable1.style.height = window.innerHeight - 130 + "px";
        maintable.style.height = "0px";
      }
    },
    searchClick() {
      if (!this.formData.OrderNo) {
        this.$message.error("请输入生产编号");
        return;
      }
      this.loading = true;
      impreportlist(this.formData.OrderNo)
        .then(res => {
          if (res.code) {
            this.Datasource = res.data;
            setTimeout(() => {
              this.handleResize();
            }, 0);
          } else {
            this.Datasource = [];
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    editdata() {
      if (this.Datasource.length == 0) {
        this.$message.error("暂无可编辑数据");
        return;
      }
      this.edit = true;
    },
    cancel() {
      this.edit = false;
    },
    savedata() {
      this.edit = false;
      setimpreportlist(this.Datasource)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.searchClick();
        });
    },
    exportReport() {
      if (this.Datasource.length == 0) {
        this.$message.error("暂无可导出数据");
        return;
      }
      reliabilityimpreport(this.formData.OrderNo).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
  },
  computed: {},
  created() {
    //
  },
  mounted() {
    this.handleResize();
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
};
</script>
<style lang="less" scoped>
/deep/ .editSty {
  padding: 0 2px !important;
  .ant-input-affix-wrapper .ant-input:not(:last-child) {
    padding: 2px 5px;
    height: 28px;
  }
}
.impedancereport {
  background: #ffffff;
  .maincontent {
    border-top: 2px solid #f0f0f0;
  }
  .topcontent {
    height: 44px;
    margin-left: 6px;
    padding-top: 5px;
  }
  /deep/.ant-table-thead > tr > th {
    padding: 7px 2px;
    overflow-wrap: break-word;
    border-right: 1px solid rgb(233, 233, 240);
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 7px 2px;
    overflow-wrap: break-word;
    border-right: 1px solid rgb(233, 233, 240);
  }
  /deep/.ant-table .ant-table-tbody > tr > td {
    height: 36px;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: rgb(223 220 220);
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
}
</style>
