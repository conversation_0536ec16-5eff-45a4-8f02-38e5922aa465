<template>
  <a-spin :spinning="spinning" > 
    <div class="box">
    <div class="content">
      <div style="border: 1px solid #E9E9F0;" class="left">
        <a-table
          :columns="columns1"
          :dataSource="dataSource"
          :pagination=false          
          :scroll="{ y: 718,}"
          :rowKey="(record,index)=>{return index}"
          class="Tab1"
        >
        <div slot="itemPar_" slot-scope="record">
        <span v-if="record.item_!='A板PNL SET数' && record.item_!='A板PNL 单元数' && record.item_!='开料图备注'&& record.item_!='交货利用率不达标原因' ">{{ record.itemPar_ }}</span>
        <span v-else-if="editFlg1 &&( record.item_=='开料图备注'|| record.item_=='交货利用率不达标原因' )"><a-textarea v-model="record.itemPar_" :auto-size="{ minRows: 1, maxRows: 5 }"  allowClear/></span>
        <span v-else-if="editFlg1"><a-input v-model="record.itemPar_"  allowClear/></span>
        <span v-else>{{ record.itemPar_ }}</span>
      </div> 
      </a-table>
      </div>
      <div class="right" style="border: 1px solid #E9E9F0;">       
        <div class="imgBox">
          <h3>大料图</h3>
          <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
            <a-empty v-if="!sheetOssPath" />
            <div class='img-box'  v-else v-viewer>
              <img :src="sheetOssPath" />
            </div> 
          </a-spin>
        </div>
        <div class="imgBox1">
          <h3>A板图</h3>
          <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
            <a-empty v-if="!aPnlOssPath" />           
            <div class='img-box1' v-else v-viewer>
              <img :src="aPnlOssPath" />
              <!-- <img :src="' data:image/svg+xml;base64,'+ imgData.aPnl"   /> -->
            </div> 
          </a-spin>
        </div>
        <div class="imgBox">
          <h3>B板图</h3>
          <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
            <a-empty v-if="!bPnlOssPath" />
            <div class='img-box' v-else v-viewer>
              <img :src="bPnlOssPath" />
              <!-- <img :src="' data:image/svg+xml;base64,'+ imgData.bPnl" /> -->
            </div> 
          </a-spin>
        </div>         
      </div> 
    </div>
    <div class="bto">
      <!-- <a-button @click="CuttingClick" type="primary" style="margin-top:20px;margin-left:10px" >开料拼版</a-button>  -->
      </div>
    </div>
    
  </a-spin>
</template>
<script >
  const  columns1=[
    {
      title: "序号",
      dataIndex: "index",     
      customRender: (text,record,index) => `${index+1}`,
      width:40,
      ellipsis: true,
      align: 'center',
    },
    {
      title: "参数名称",
      dataIndex: "item_",
      ellipsis: true,
      width:150,
    },
    {
      title: "参数值",
      ellipsis: true,    
      scopedSlots: { customRender: 'itemPar_' },
    },
  ];
 
export default{
    name:'cuttingInfo',
    props:['pnlParameterDto','selectData','editFlg1'], 
    data(){
        return{
          spinning:false,
          imageLoading:false,
          columns1,
          orderDetailData:[],
          activeKey: ['1','2','3'],
          sheetOssPath:'',
          aPnlOssPath:'',
          bPnlOssPath:'',
          dataSource:[]
        }
    },
    created(){
  //  this.$nextTick(()=>{
  //    this.handleResize()
  //  })
  },
    mounted(){
      this.cancle()
      window.addEventListener('resize', this.handleResize, true)    
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize, true)
    },   
    watch: {
    activeKey(key) {
      console.log(key);
    },
  },
    computed:{
    orderDetailDataFilter(){
      let arr_ = []
      if (this.orderDetailData.length != 0) {
        projectArray.forEach(item => {
          arr_.push({'projectName':item.name, 'parameter': this.orderDetailData[item.value]})
        })
        arr_ = arr_.filter(item=>item.parameter != '否' && item.parameter != '' &&  item.parameter != '不需要'  && item.parameter != null)
      }
      return arr_
    }
  },
  methods:{
    handleResize(){
        var boxstyle = document.getElementsByClassName('box')[3]
        boxstyle.style.height = window.innerHeight - 140 + 'px'
      },
    cancle(){
      if( JSON.stringify(this.pnlParameterDto) != "{}" &&  JSON.stringify(this.pnlParameterDto) != "null"){
        this.dataSource = this.pnlParameterDto.pnlEngParameterDtos 
        this.sheetOssPath = this.pnlParameterDto.sheetOssPath
        this.aPnlOssPath = this.pnlParameterDto.aPnlOssPath
        this.bPnlOssPath = this.pnlParameterDto.bPnlOssPath
      }else{
        this.dataSource = []
        this.sheetOssPath = ''
        this.aPnlOssPath = ''
        this.sheetOsssPath = ''
      }
    },
    CuttingClick(){
      this.$emit('CuttingClick')
    },
  },
  
}
</script>
<style scoped lang="less">
/deep/.ant-input{
  height: 27px;
}
/deep/.ant-table-content{
  border-top: 1px solid #f0f2f5;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f0f2f5;
}
 .box{
  overflow: auto;
  border-left:1px solid rgb(233 230 230);
  position:relative;
    .bto{
      position:absolute;
      bottom:-48px;
      right:384px;
    }
    .content{
      display: inline-flex;
      height:97%;
      .left{
        width:385px
      }
      .right {
        width: 1140px;
        display: flex; 
       .imgBox{        
        h3{
          text-align: center;
        }
        /deep/.ant-spin-container{
          position:absolute;
          top:25%;
          left:25%;
        }
        height:500px;
        width:380px;
        border:1px solid #E9E9F0;
       }  
       .imgBox1{        
        h3{
          text-align: center;
        }
        /deep/.ant-spin-container{
          position:absolute;
          top:25%;
          left:25%;
        }
        height:500px;
        width:380px;
        border:1px solid #E9E9F0;
       }     
      }
    }
    .img-box{
      width:375px;
      height:480px;
      margin-left:-92px;
      margin-top:-100px;
      img{
        width:99%;
        height:400px;
      }
    }
    .img-box1{
      width:375px;
      height:480px;
      margin-left:-92px;
      margin-top:-100px;
      img{
        width:99%;
        height:400px;
      }
    }
    
  }
 
  /deep/  .ant-table-thead > tr > th{
    padding: 3px 4px;
  }
  /deep/ .ant-table-tbody > tr > td{
    padding: 3px 4px;
  }
 
    
</style>