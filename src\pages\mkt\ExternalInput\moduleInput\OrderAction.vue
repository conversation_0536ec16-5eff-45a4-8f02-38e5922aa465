<template>
  <a-form-model layout="inline">
    <a-form-model-item>
      <a-button
        @click="$router.go(-1)"
        type="primary"
      >
        返回订单列表
      </a-button>
    </a-form-model-item>
    <a-form-model-item>
      <a-button
          :type="editFlag ? 'dashed' : 'primary'"
          @click="edit"
      >
        {{ editFlag ? '取消' : '编辑' }}
      </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="editFlag">
      <a-button
          type="primary"
          @click="save"
      >
        保存
      </a-button>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
export default {
  name: "OrderAction",
  props:['editFlag'],
  methods: {
    edit(){
      this.$emit('editInfo')
    },
    save(){
      this.$emit('dataSave')
    },
    
  }
}
</script>

<style scoped>

</style>