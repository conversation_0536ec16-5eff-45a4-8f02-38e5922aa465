<!-- 车间管理-字符管理 -->
<template>
  <div class="CharacterManage">
    <a-spin :spinning="spinning">
      <div class="content">
        <div class="left" ref="letfDom" style="width: 65%">
          <a-card :bordered="false" style="height: 390px; border: 1px solid #e9e9f0">
            <a-table
              rowKey="guid_"
              :columns="columns"
              :dataSource="data1Source"
              :pagination="false"
              :loading="table1Loading"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :customRow="eventTouch"
              :scroll="{ x: '65%', y: 355 }"
              :rowClassName="setRowClassName"
              :class="{ minClass: data1Source.length > 0 }"
            >
              <template slot="isBigCus" slot-scope="text, record">
                <a-checkbox v-model="record.isBigCus"> </a-checkbox>
              </template>
              <template slot="isStop_" slot-scope="text, record">
                <a-checkbox v-model="record.isStop_"> </a-checkbox>
              </template>
            </a-table>
          </a-card>
          <a-card :bordered="false" style="height: 390px; border: 1px solid #e9e9f0" @contextmenu.prevent="rightClick1($event)">
            <a-table
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :columns="columns2"
              :dataSource="data2Source"
              :pagination="false"
              :loading="table2Loading"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :customRow="eventTouch1"
              :scroll="{ x: '65%', y: 355 }"
              :rowClassName="setRowClassName1"
              :class="{ minClass: data2Source.length > 0 }"
            >
              <template slot="isBigCus" slot-scope="text, record">
                <a-checkbox v-model="record.isBigCus"> </a-checkbox>
              </template>
              <template slot="isStop_" slot-scope="text, record">
                <a-checkbox v-model="record.isStop_"> </a-checkbox>
              </template>
            </a-table>
            <a-menu :style="menuStyle" v-show="menuVisible1" class="tabRightClikBox">
              <a-menu-item @click="down">文件下载</a-menu-item>
            </a-menu>
          </a-card>
        </div>
        <div class="right" style="width: 35%">
          <cutting-center
            :tableData="data3Source"
            :table3Loading="table3Loading"
            :machineStatuList="data4Source"
            :machineStatuLoad="table4Loading"
            :drillLoad="table5Loading"
            :drillList="data5Source"
            :dispatchLoad="table6Loading"
            :dispatchList="data6Source"
            @getDispatchFallback="getDispatchFallback"
            @getAssignmentList="getAssignmentList"
            @getMesaList="getMesaList"
            @BatchModification="BatchModification"
            ref="cuttingCenter"
            :idList="idList"
          ></cutting-center>
        </div>
      </div>
      <div class="footer">
        <div class="actionBox">
          <cutting-action
            @handleDispatchMachine="handleDispatchMachine"
            @Scancodetodispatch="Scancodetodispatch"
            @OverOrderClick="OverOrderClick"
            @ExceptionRemarksClick="ExceptionRemarksClick"
            @queryClick="queryClick"
            @finishClick="finishClick"
            @DataCheckClick="DataCheckClick"
            :btnloading1="btnloading1"
            :btnloading2="btnloading2"
            :btnloading3="btnloading3"
            :btnloading4="btnloading4"
            :btnloading5="btnloading5"
            :btnloading6="btnloading6"
            :btnloading7="btnloading7"
          ></cutting-action>
        </div>
      </div>
      <!--扫码分派-->
      <a-modal
        title="扫码分派"
        :visible="scancodeVisible"
        @cancel="scancodeVisible = false"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :destroyOnClose="true"
        centered
      >
        <a-form-item label="扫码分派" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
          <a-input v-model="scancode" :autoFocus="true" @keyup.enter="scancodeOk" />
        </a-form-item>
        <template #footer>
          <a-button key="back" @click="scancodeVisible = false">取消</a-button>
        </template>
      </a-modal>
      <!-- 部门过序弹窗 -->
      <a-modal
        title="部门过序"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        cancelText="关闭"
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        :destroyOnClose="true"
      >
        <over-order-info
          @keyupEnter="keyupEnter"
          :quantity="quantity"
          @quantityChange="quantityChange"
          :quantityCopy="quantityCopy"
          ref="overOrder"
        />
      </a-modal>
      <!-- 异常备注弹窗 -->
      <a-modal
        title="异常备注"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <exception-remarks-info ref="exceptionRemarksInfo" />
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk5"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        centered
      >
        <query-info ref="queryInfo" @keyupEnter1="keyupEnter1" />
      </a-modal>
      <!-- 修改数量弹窗 -->
      <a-modal
        title="修改数量"
        :visible="dataVisible6"
        @cancel="reportHandleCancel"
        @ok="handleOk7"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        :destroyOnClose="true"
      >
        <batch-modification-quantity-info ref="BatchModificationQuantity" />
      </a-modal>
    </a-spin>
  </div>
</template>
<script>
import { processorder } from "@/services/management";
//按钮
import {
  getWaitOrderList,
  getDoingOrderList,
  getMachineList,
  getStatisticsList,
  getDispatchList,
  getDispatchMachineList,
  getDispatchFallbackList,
  getOrderMuIdList,
  getCharacterNum,
  DataCheck,
  characterOverCount,
  flyingProbeRemarks,
  finishCharacter,
  downLoadPath,
  getOndutystatistics,
} from "@/services/production";
import CuttingCenter from "./module/CuttingCenter";
import CuttingAction from "./module/CuttingAction";
import OverOrderInfo from "./module/OverOrderInfo";
import ExceptionRemarksInfo from "./module/ExceptionRemarksInfo";
import QueryInfo from "./module/QueryInfo";
import BatchModificationQuantityInfo from "./module/BatchModificationQuantityInfo.vue";

// 待发订单
const columns = [
  {
    dataIndex: "index",
    title: "序号",
    slots: { title: "customTitle" },
    key: "index",
    width: 40,
    align: "center",
    scopedSlots: { customRender: "index" },
    customRender: (text, record, index) => `${index + 1}`,
    customCell: (record, rowIndex) => {
      if (record.color_ == "#FF0000") {
        return { style: { background: "#FF0000" } };
      }
    },
  },
  {
    title: "生产型号",
    dataIndex: "orderNo",
    width: 120,
    ellipsis: true,
    className: "orderClass",
    align: "center",
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "PNL数",
    dataIndex: "totalNum",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "面积",
    width: 45,
    ellipsis: true,
    dataIndex: "area_",
    align: "center",
  },
  {
    title: "字符颜色",
    dataIndex: "fontColor",
    width: 70,
    ellipsis: true,
    align: "center",
  },

  {
    title: "交货日期",
    width: 95,
    ellipsis: true,
    dataIndex: "deliveryDate",
    align: "center",
  },
  {
    title: "到序时间",
    width: 100,
    dataIndex: "createTime",
    ellipsis: true,
    align: "center",
  },
  {
    title: "订单工厂",
    width: 100,
    ellipsis: true,
    dataIndex: "orderFactoryKey",
    align: "center",
  },
  {
    title: "异常备注",
    width: 130,
    ellipsis: true,
    dataIndex: "note_",
    align: "center",
  },
];
// 已发订单
const columns2 = [
  {
    dataIndex: "index",
    title: "序号",
    slots: { title: "customTitle" },
    key: "index",
    width: 40,
    align: "center",
    //fixed: 'left',
    scopedSlots: { customRender: "index" },
    customRender: (text, record, index) => `${index + 1}`,
    customCell: (record, rowIndex) => {
      if (record.color_ == "#FF0000") {
        return { style: { background: "#FF0000" } };
      }
    },
  },
  {
    title: "生产型号",
    dataIndex: "orderNo",
    width: 120,
    ellipsis: true,
    className: "orderClass",
    align: "center",
    //fixed: 'left'
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "PNL数",
    dataIndex: "totalNum",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "面积",
    width: 45,
    ellipsis: true,
    dataIndex: "area_",
    align: "center",
  },
  {
    title: "字符颜色",
    dataIndex: "fontColor",
    width: 70,
    ellipsis: true,
    align: "center",
  },
  {
    title: "交货日期",
    width: 95,
    ellipsis: true,
    dataIndex: "deliveryDate",
    align: "center",
  },
  {
    title: "到序时间",
    width: 100,
    dataIndex: "createTime",
    ellipsis: true,
    align: "center",
  },
  {
    title: "派单时间",
    width: 100,
    dataIndex: "startDate_",
    ellipsis: true,
    align: "center",
  },
  // {
  //   title: "机台数",
  //   dataIndex: "machineCount_",
  //   width: 70,
  //   ellipsis: true,
  //   align: 'center',
  // },
  {
    title: "订单工厂",
    width: 100,
    ellipsis: true,
    dataIndex: "orderFactoryKey",
    align: "center",
  },
];
export default {
  name: "CharacterManage",
  components: { CuttingCenter, CuttingAction, OverOrderInfo, ExceptionRemarksInfo, QueryInfo, BatchModificationQuantityInfo },
  inject: ["reload"],
  data() {
    return {
      spinning: false,
      columns,
      columns2,
      loading: false,
      table1Loading: false, // 待分派表格load
      table2Loading: false, // 已分派表格load
      table3Loading: false, // 机台表格load
      table4Loading: false, // 机台状态load
      table5Loading: false, // 钻刀表格load
      table6Loading: false, // 分派状态load
      data1Source: [], // 待分派集合
      data2Source: [], // 已分派集合
      data3Source: [], // 机台集合
      data3SourceCopy: [], // 暂存机台集合
      data4Source: [], // 机台状态集合
      data5Source: [], // 钻刀集合
      data6Source: [], // 分派集合
      selectedRowList: [],
      selectedRowList1: [],
      selectedRows: [],
      selectedRowsPnts: "",
      assignMachineList: [],
      rowId1: "",
      dataVisible: false, //部门过序弹窗不显示
      dataVisible1: false, //上传订单弹窗不显示
      dataVisible2: false, //涨缩登记弹窗不显示
      dataVisible3: false, //异常备注弹窗不显示
      dataVisible4: false, //查询弹窗
      dataVisible5: false, //人员登记弹窗
      dataVisible6: false, //修改数量弹窗
      quantity: "",
      quantityCopy: "",
      cardNo: "",
      confirmLoading: false,
      note: "",
      idList: [],
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      menuVisible: false,
      menuVisible1: false,
      menuData: [],
      btnloading1: false, // 分派按钮loading
      btnloading2: false, // 设置加急按钮loading
      btnloading3: false, // 数据核对按钮loading
      btnloading4: false, // 删除订单按钮loading
      btnloading5: false, // 呼叫小车按钮loading
      btnloading6: false, // 人员确认按钮loading
      btnloading7: false, // 取消小车按钮loading
      agvID: "",
      scancode: "",
      scancodeVisible: false, //扫码弹窗
    };
  },
  // watch:{
  //   'idList':{
  //     handler(val){
  //       console.log(val)
  //       if(val.length) {
  //         this.data3Source = this.data3SourceCopy.filter(item => {
  //           return val.includes(item.iD_)
  //         })
  //       } else {
  //         this.getMesaList()
  //       }
  //     }
  //   }
  // },
  methods: {
    // 待上机列表
    getorderList(orderNum) {
      let params = {};
      if (orderNum) {
        params.Pdctno = orderNum;
      }
      this.table1Loading = true;
      getWaitOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data1Source = res.data;
          }
        })
        .finally(() => {
          this.table1Loading = false;
        });
    },
    // 已上机列表
    getdoOrderList(orderNum) {
      let params = {};
      if (orderNum) {
        params.Pdctno = orderNum;
      }
      this.table2Loading = true;
      getDoingOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data2Source = res.data || [];
          }
        })
        .finally(() => {
          this.table2Loading = false;
        });
    },
    // 机台列表
    getMesaList() {
      this.table3Loading = true;
      getMachineList()
        .then(res => {
          if (res.code == 1) {
            this.data3Source = res.data;
            this.data3SourceCopy = res.data;
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    // 状态统计列表
    getMachineStatuList() {
      this.table4Loading = true;
      getOndutystatistics()
        .then(res => {
          if (res.code == 1) {
            this.data4Source = res.data;
            console.log("this.data4Source", this.data4Source);
          }
        })
        .finally(() => {
          this.table4Loading = false;
        });
    },
    // 机台上订单
    getAssignmentList(id) {
      this.table6Loading = true;
      getDispatchList(id)
        .then(res => {
          if (res.code == 1) {
            this.data6Source = res.data;
          }
        })
        .finally(() => {
          this.table6Loading = false;
        });
    },
    // 点击事件
    eventTouch(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowList = keys;
            let rowData = [];
            rowData.push(record.pnts_);
            this.selectedRowsPnts = rowData[0];
            // console.log('this.selectedRows',this.selectedRowsPnts)
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
        },
      };
    },
    eventTouch1(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            console.log("record", record);
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowList1 = keys;
            getOrderMuIdList(record.guid_).then(res => {
              if (res.code == 1) {
                this.idList = res.data;
                console.log("this.idList", this.idList);
                this.rowId1 = record.pdctno_;
              } else {
                this.$message.error(res.message);
              }
            });
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
        },
      };
    },
    // 已上机列表背景设置
    setRowClassName(record) {
      var classStr = "";
      // 单击设置背景色
      if (record.guid_ == this.selectedRowList) {
        classStr = classStr + "bacStyle" + " ";
      }
      // 双击选中背景色
      // if(record.pdctno_ == this.rowId1 ){
      //   classStr =  'clickRowSty2'
      // }
      return classStr;
    },
    setRowClassName1(record) {
      var classStr = "";
      // 单击设置背景色
      if (record.guid_ == this.selectedRowList1) {
        classStr = classStr + "bacStyle" + " ";
      }
      // 双击选中背景色
      // if(record.pdctno_ == this.rowId1 ){
      //   classStr =  'clickRowSty2'
      // }
      return classStr;
    },

    // 弹窗关闭控制
    reportHandleCancel() {
      this.dataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.dataVisible4 = false;
      this.dataVisible5 = false;
      this.dataVisible6 = false;
    },
    //扫码分派
    Scancodetodispatch() {
      if (this.$refs.cuttingCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      this.scancodeVisible = true;
    },
    scancodeOk() {
      processorder(this.$refs.cuttingCenter.selectedRowKeys, 1, this.scancode).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.scancode = "";
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 分派
    handleDispatchMachine() {
      if (this.selectedRowList.length <= 0) {
        this.$message.warning("请选择待分派订单");
        return;
      } else if (this.$refs.cuttingCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }

      // if(this.selectedRowsPnts == 0){
      //   this.$message.warning('测试点为0不允许分派')
      //   return
      // }
      // console.log(this.$refs.cuttingCenter.selectedRowKeys)  // 这个是机台ID 集合
      // console.log(this.data3Source)  // 这个是机台集合
      // 在机台集合数据里面筛选机台集合里面对应ID的机台详细信息。去做判断  如果负责人为空不允许分派，
      var id_arr = this.$refs.cuttingCenter.selectedRowKeys;
      var arr = this.data3SourceCopy;
      var str_arr = [];
      id_arr.forEach(item => {
        if (
          !arr.find(itm => {
            return itm.iD_ == item;
          }).listParams_
        ) {
          str_arr.push(
            arr.find(itm => {
              return itm.iD_ == item;
            }).caption_
          );
        }
      });
      if (str_arr.length > 0) {
        this.$message.warning(str_arr.join("、") + "负责人为空不允许分派");
        return;
      }
      this.btnloading1 = true;
      this.spinning = true;
      // 获取待分派订单id和机台id
      let assignmentData = {
        id: this.selectedRowList[0],
        equId: this.$refs.cuttingCenter.selectedRowKeys,
      };
      getDispatchMachineList(assignmentData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("分派成功");
            this.data1Source.splice(
              this.data1Source.findIndex(item => item.guid_ == this.selectedRowList[0]),
              1
            );
            this.selectedRowList = [];
            this.$refs.cuttingCenter.selectedRowKeys.forEach(ite => {
              this.data3Source.filter(item => item.iD_ == ite)[0].numMachine_ += 1;
            });
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.btnloading1 = false;
          this.spinning = false;
        });
    },
    // 分派回退
    getDispatchFallback(record) {
      let params = {
        id: record.guid_,
      };
      console.log("record", record);
      if (confirm("确定回退吗？")) {
        this.spinning = true;
        getDispatchFallbackList(params)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("分派回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.getAssignmentList(record.iD_);
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    // 部门过序
    OverOrderClick() {
      this.dataVisible = true;
    },
    // 获取部门过序数量
    async keyupEnter(cardNo) {
      this.confirmLoading = true;
      let params = cardNo;
      // let params = {
      //   'cardNo':cardNo
      // }
      await getCharacterNum(params).then(res => {
        if (res.code == 1) {
          this.cardNo = cardNo;
          this.quantity = res.data;
          this.$refs.overOrder.value = res.data;
          this.quantityCopy = JSON.parse(JSON.stringify(res.data));
          this.$message.success("获取过序数量成功");
          console.log("this.quantity;", this.quantity);
        } else {
          this.$message.error(res.message);
        }
        this.confirmLoading = false;
      });
    },
    handleOk1() {
      if (!this.$refs.overOrder.barcode) {
        this.$message.warning("流程卡号不允许为空");
        return;
      } else if (!this.$refs.overOrder.value) {
        this.$message.warning("过序数量不允许为空");
        return;
      }
      this.confirmLoading = true;
      this.spinning = true;
      let paramsData = {
        cardNo: this.$refs.overOrder.barcode,
        num: this.$refs.overOrder.value,
      };
      characterOverCount(paramsData).then(res => {
        if (res.code == 1) {
          this.$message.success("过序成功");
          this.$refs.overOrder.barcode = "";
          this.$refs.overOrder.value = "";
          this.$refs.overOrder.getFoucs();
          this.getorderList();
        } else {
          this.$message.error(res.message);
        }
        this.confirmLoading = false;
        this.spinning = false;
      });
    },
    quantityChange(payload) {
      this.quantity = payload;
    },
    // 异常备注
    ExceptionRemarksClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.dataVisible3 = true;
    },
    handleOk4() {
      this.confirmLoading = true;
      this.spinning = true;
      let note = this.$refs.exceptionRemarksInfo.note;
      flyingProbeRemarks(this.selectedRowList[0], { Note: note })
        .then(res => {
          if (res.code == 1) {
            this.$message.success("备注成功");
          } else {
            this.$message.error(res.message);
          }
          this.getorderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible3 = false;
        });
    },
    // 单击完成
    finishClick() {
      if (!this.$refs.cuttingCenter.rowId2) {
        this.$message.warning("请选择订单");
        return;
      }
      // console.log(this.$refs.cuttingCenter.rowId2)
      let params = {
        id: this.$refs.cuttingCenter.rowId2,
      };
      finishCharacter(params).then(res => {
        if (res.code) {
          this.$message.success("完成成功");
          this.getAssignmentList(this.$refs.cuttingCenter.rowId);
          this.getMesaList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //数据核对
    DataCheckClick() {
      this.btnloading3 = true;
      this.spinning = true;
      DataCheck()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("数据核对");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading3 = false;
          this.spinning = false;
        });
    },
    //查询
    queryClick() {
      this.dataVisible4 = true;
    },
    keyupEnter1() {
      this.dataVisible4 = false;
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    handleOk5() {
      this.dataVisible4 = false;
      // console.log(this.$refs.queryInfo.OrderNumber)
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuVisible1 = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    rightClick1(e) {
      this.menuVisible1 = true;
      this.menuStyle.top = e.clientY - 480 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 下载
    down() {
      downLoadPath(this.menuData.guid_).then(res => {
        if (res.code == 1) {
          window.location.href = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 批量修改数量
    BatchModification() {
      if (this.$refs.cuttingCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      this.dataVisible6 = true;
    },
    handleOk7() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = {
        ids: this.$refs.cuttingCenter.selectedRowKeys,
        qty: this.$refs.BatchModificationQuantity.quantity,
      };
      console.log(this.$refs.cuttingCenter.selectedRowKeys);
      BatchModificationQuantity(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("修改成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible6 = false;
        });
    },
  },
  mounted() {
    this.getorderList();
    this.getdoOrderList();
    this.getMesaList();
    this.getMachineStatuList();
  },
  //  updated(){
  //   // 表格斑马行显示
  //   this.renderStripe()
  // },
};
</script>
<style lang="less" scoped>
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
.CharacterManage {
  min-width: 1670px;
  user-select: none;
  .content {
    display: flex;
    height: 780px;
  }
  //.rowcolor {
  //    background: #fff9e6;
  //    -moz-user-select:none;
  //    -webkit-user-select:none;
  //    user-select:none;
  //  }

  .footer {
    .actionBox {
      overflow: hidden;
      width: 100%;
      border: 2px solid #e9e9f0;
      height: 48px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      form {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  /deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #aba5a5 !important;
  }
  /deep/ .ant-table {
    tr.ant-table-row-selected td {
      background: #aba5a5 !important;
    }
    tr.ant-table-row-hover td {
      background: #aba5a5 !important;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
      .bacStyle {
        background: #aba5a5;
      }
    }
  }
  .left {
    /deep/ .ant-table-body {
      .ant-table-tbody {
        .bacStyle.ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
        .ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
      }
    }
    /deep/ .ant-table-fixed {
      .ant-table-tbody {
        .ant-table-row {
          .orderClass {
            user-select: all;
          }
        }
      }
    }
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    /deep/ .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
  /deep/ .ant-table {
    .ant-table-selection-col {
      width: 30px;
    }
  }
  background: #ffffff;
  .minClass {
    /deep/ .ant-table-body {
      min-height: 355px;
    }
  }
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 5px 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 5px;
        }
      }
    }
  }

  /deep/ .ant-table-body {
    // &::-webkit-scrollbar {
    //   //整体样式
    //   width: 6px; //y轴滚动条粗细
    //   height: 6px;
    // }

    // &::-webkit-scrollbar-thumb {
    //   //滑动滑块条样式
    //   border-radius: 2px;
    //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    //   background: #ff9900;
    //   // #fff9e6
    // }
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 0;
        }
      }
    }
  }
  /deep/ .ant-table-scroll .mouseentered {
    overflow-x: scroll !important;
  }
}
</style>
