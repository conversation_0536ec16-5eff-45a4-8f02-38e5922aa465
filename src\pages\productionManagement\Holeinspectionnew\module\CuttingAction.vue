<!-- 车间管理-验孔管理-按钮 -->
<template>
  <div class="active" ref="active" :style="[{ width: width + 'px' }]">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillOrderStart')"
      :class="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillOrderStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="handleDispatchMachine" :loading="btnloading1"> 分派 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillScanCode')"
      :class="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillScanCode') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Scancodetodispatch')"> 扫码分派 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillEditMachine')"
      :class="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillEditMachine') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Editingmachine')"> 编辑机台 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillDefectRegistration')"
      :class="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillDefectRegistration') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('DefectRegistration')"> 缺陷登记 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillIsUrgen')"
      :class="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillIsUrgen') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="SetUpExpeditingClick"> 设置加急 </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillSetRemarks')"
      :class="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillSetRemarks') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ExceptionRemarksClick"> 生产备注 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillOrderFinish')"
      :class="checkPermission('MES.ProductionModule.CheckDrill.CheckDrillOrderFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Productioncompleted')"> 生产完成 </a-button>
    </div>
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "CuttingAction",
  props: ["btnloading1", "btnloading2", "btnloading3", "btnloading4", "btnloading5", "btnloading6", "btnloading7"],
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },

  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 762;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 分派订单
    handleDispatchMachine() {
      this.$emit("handleDispatchMachine");
    },
    // 部门过序
    OverOrderClick() {
      this.$emit("OverOrderClick");
    },
    // 上传订单
    UploadOrderClick() {
      this.$emit("UploadOrderClick");
    },
    // 涨缩登记
    harmomegathusRegisterClick() {
      this.$emit("harmomegathusRegisterClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    //设置加急
    SetUpExpeditingClick() {
      this.$emit("SetUpExpeditingClick");
    },
    // 数据核对
    DataCheckClick() {
      this.$emit("DataCheckClick");
    },
    // 删除订单
    DeleteOrderClick() {
      this.$emit("DeleteOrderClick");
    },
    //生产备注
    ExceptionRemarksClick() {
      this.$emit("ExceptionRemarksClick");
    },
    // 呼叫小车
    operationClick1() {
      this.$emit("operationClick1");
    },
    // 人员确认
    operationClick2() {
      this.$emit("operationClick2");
    },
    // 取消小车
    operationClick3() {
      this.$emit("operationClick3");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  float: right;
  height: 50px;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    width: 90px;
    margin: 6px 0;
    text-align: center;
    .ant-btn {
      width: 80px;
    }
    .ant-btn-loading {
      padding-left: 0 !important;
    }
  }
}
</style>
