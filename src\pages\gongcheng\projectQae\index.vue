<!-- 工程管理 - QAE审核  -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend">
      <span style="position: absolute; top: -4%; right: 22%; color: #ff9900; font-size: 16px">{{ showNote }}</span>
      <!-- <span>
      <a-tooltip style="z-index:999">
        <template #title>打开小助手</template>
      <a-icon type="aliwangwang" style="color: #ff9900; font-size: 26px;position:absolute;top:-26px;right:26px;" @click="webSocketLink"></a-icon>
      </a-tooltip>
    </span> -->
      <div style="width: 100%; display: flex">
        <div class="leftContent" ref="tableWrapper" style="position: relative" :style="{ height: heighty - 130 + 'px' }">
          <left-table-qae
            v-if="pageShow"
            :columns="columns1"
            :data-source="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'proOrderId'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            @getOrderDetail="getOrderDetail"
            @jigsawPuzzleClick="jigsawPuzzleClick"
            ref="orderTable"
            class="leftstyle"
            @viewLogClick="viewLogClick"
            @modifyInfoClick1="modifyInfoClick1"
            @EditParametersClick="EditParametersClick"
            @webSocketLink="webSocketLink"
            @CustomerRulesClick="CustomerRulesClick"
            @getJobInfo="getJobInfo"
            @down2="down2"
            @rightClick="rightClick"
            :cookieId="cookieId"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
          </left-table-qae>
          <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
            <a-menu-item @click="down2" v-if="showText">复制</a-menu-item>
            <a-menu-item>
              <a-upload accept=".rar,.zip" name="file" :before-upload="beforeUpload" :customRequest="httpRequest0"> 文件替换 </a-upload>
            </a-menu-item>
          </a-menu>
        </div>
        <div class="rightContent">
          <div class="Table">
            <a-collapse :activeKey="1" @change="CollapseList" :expandIcon="expandIcon1">
              <a-collapse-panel key="1">
                <div class="centerTable" style="user-select: none !important">
                  <a-tabs type="card" @change="tabpaneChange" default-active-key="1">
                    <a-tab-pane
                      key="1"
                      tab="人员订单信息"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaePersonOrderInfo')"
                    >
                      <order-detail-qae
                        :columns="columns3"
                        :data-source="peopleOrderInfoList"
                        :orderDetailTableLoading="peopleOrderInfoTableLoading"
                        :rowKey="'userNo'"
                        @getPeopleOrderList="getPeopleOrderList"
                        @saveErpKey="saveErpKey"
                        ref="peopleOrder"
                        @OrderRetrievalSettingsClick="OrderRetrievalSettingsClick"
                        class="qaestyle"
                      >
                      </order-detail-qae>
                    </a-tab-pane>
                    <a-tab-pane key="2" tab="订单详情">
                      <order-detail
                        :columns="columns2"
                        :data-source="orderDetailDataFilter"
                        :orderDetailTableLoading="orderDetailTableLoading"
                        :rowKey="'projectName'"
                        class="orderstyle"
                      >
                      </order-detail>
                    </a-tab-pane>
                  </a-tabs>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>
          <div class="rightTable">
            <!-- <div v-if="jobSwitch" class="jobNote">
            <a-table
                :columns="columns4"
                :dataSource="jobData"
                :pagination="false"
                rowKey="id"
                bordered
                :scroll="{y: 387}"
                :loading="jobTableLoading">
              <template slot="picUrl" slot-scope="text,record">
                <viewer :images="picFilter(record)">
                  <img :src="item" v-for="(item,index) in picFilter(record)" :key="index" style="width: 40px; height: 40px"/>
                </viewer>
              </template>
            </a-table>
            <div class="note">
              <div class="textNote" v-html="allNote"></div>
            </div>
          </div> -->
            <div style="user-select: none">
              <div v-if="jobSwitch" class="jobNote" style="user-select: none" ref="tableWrapper1">
                <a-table
                  :columns="columns4"
                  :dataSource="jobData"
                  :customRow="onClickRow1"
                  :pagination="false"
                  rowKey="id"
                  :scroll="{ y: 385, x: 360 }"
                  class="jobstyle"
                  :loading="jobTableLoading"
                >
                  <template slot="picUrl" slot-scope="text, record">
                    <viewer :images="picFilter(record)">
                      <img :src="item" v-for="(item, index) in picFilter(record)" :key="index" style="width: 20px; height: 20px" />
                    </viewer>
                  </template>
                  <template slot="fileUrl" slot-scope="text, record">
                    <a-tooltip title="编辑">
                      <a-icon type="edit" style="color: rgb(0, 0, 204)" @click="editClick(record)"></a-icon>
                    </a-tooltip>
                    <span style="color: rgb(0, 0, 204)">/</span>
                    <a-tooltip title="下载附件">
                      <a-icon type="download" v-if="record.fileUrl" style="color: rgb(0, 0, 204)" @click="downFile($event, record.fileUrl)"></a-icon>
                    </a-tooltip>
                    <span v-if="record.fileUrl" style="color: rgb(0, 0, 204)">/</span>
                    <a-tooltip title="删除">
                      <a-icon type="close-square" style="color: rgb(0, 0, 204)" @click="delFile(record)"></a-icon>
                    </a-tooltip>
                  </template>
                </a-table>
                <right-copy ref="RightCopy" />
              </div>
              <div v-else style="user-select: none" class="jobNote1" ref="tableWrapper2">
                <a-table
                  :columns="columns5"
                  :dataSource="peopleOrderListData"
                  :pagination="false"
                  :rowKey="
                    (record, index) => {
                      return index;
                    }
                  "
                  :scroll="{ y: 737, x: 400 }"
                  :customRow="onClickRow"
                  :rowClassName="isRedRow"
                  :loading="peopleOrderListTableLoading"
                  class="job1style"
                >
                  <div slot="score" slot-scope="text, record">
                    <a-tooltip :title="xstitle">
                      <span v-if="record.score" style="color: #428bca" @mouseover="Coefficientdetails(record.proOrderId)">{{
                        record.score.toFixed(2)
                      }}</span>
                    </a-tooltip>
                  </div>
                  <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
                    <a style="color: black" :title="record.orderNo">{{ text }}</a
                    >&nbsp;
                    <span class="tagNum1" style="display: inline-block; height: 19px">
                      <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
                        <span
                          class="noCopy"
                          style="
                            font-size: 14px;
                            font-weight: 500;
                            color: #ff9900;
                            padding: 0;
                            display: inline-block;
                            height: 19px;
                            width: 18px;
                            margin-left: -10px;
                            user-select: none;
                          "
                          >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                        </span>
                        <span
                          class="noCopy"
                          style="
                            font-size: 14px;
                            font-weight: 500;
                            color: #ff9900;
                            padding: 0;
                            display: inline-block;
                            height: 19px;
                            width: 18px;
                            margin-left: -10px;
                            user-select: none;
                          "
                          >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                        </span>
                      </a-tooltip>
                      <a-tooltip title="加急" v-else-if="record.isJiaji">
                        <span
                          class="noCopy"
                          style="
                            font-size: 14px;
                            font-weight: 500;
                            color: #ff9900;
                            padding: 0;
                            display: inline-block;
                            height: 19px;
                            width: 18px;
                            margin-left: -10px;
                            user-select: none;
                          "
                          >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                        </span>
                      </a-tooltip>
                      <a-tooltip title="新客户" v-if="record.isNewCust">
                        <a-tag
                          color="#2D221D"
                          style="
                            font-size: 12px;
                            background: #428bca;
                            color: white;
                            padding: 0 2px;
                            margin-left: 3px;
                            margin-right: 3px;
                            height: 21px;
                            user-select: none;
                            border: 1px solid #428bca;
                          "
                        >
                          新
                        </a-tag>
                      </a-tooltip>
                    </span>
                  </div>
                  <template slot="action" slot-scope="text, record">
                    <a-tooltip title="回退订单">
                      <a-icon
                        type="rollback"
                        style="color: #ff9900; font-size: 18px"
                        @click="assignBackClick(record)"
                        v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeBackSend')"
                      />
                    </a-tooltip>
                  </template>
                  <div slot="customRender" slot-scope="text, record" style="display: flex; align-items: center">
                    <template>
                      {{ text }}
                    </template>
                    <a-tooltip title="二次投产">
                      <a-tag
                        v-if="record.backUserAccount > 0"
                        color="#2D221D"
                        class="noCopy"
                        style="
                          font-size: 12px;
                          background: #428bca;
                          color: white;
                          padding: 0 2px;
                          margin: 0;
                          margin-right: 3px;
                          height: 21px;
                          user-select: none;
                          border: 1px solid #428bca;
                        "
                      >
                        二
                      </a-tag>
                    </a-tooltip>
                    <a-tag
                      v-if="record.isEQ == 1 && record.state_ != '问客已回复' && record.state_ != '问客已审核' && record.state_ != '问客'"
                      color="#2D221D"
                      class="noCopy"
                      style="
                        font-size: 12px;
                        background: #428bca;
                        color: white;
                        padding: 0 2px;
                        margin: 0;
                        margin-right: 3px;
                        height: 21px;
                        user-select: none;
                        border: 1px solid #428bca;
                      "
                    >
                      问
                    </a-tag>
                  </div>
                </a-table>
                <right-copy ref="RightCopy" />
              </div>
            </div>
            <div class="note" v-viewer v-if="jobSwitch">
              <div class="textNote" v-html="allNote"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="footerAction" style="user-select: none" ref="action">
        <qae-action
          :assignLoading="assignLoading"
          @assignClick="assignClick"
          @queryClick="queryClick"
          @RepairStartClick="RepairStartClick"
          @RepairRecordClick="RepairRecordClick"
          @wenkeClick="wenkeClick"
          @EditParametersClickbtn="EditParametersClickbtn"
          @GenerateStackClick="GenerateStackClick"
          @StackImpedanceClick="StackImpedanceClick"
          @TakeOrderClick="TakeOrderClick"
          @AuditCompletedClick="AuditCompletedClick"
          @SecondaryAuditAssignClick="SecondaryAuditAssignClick"
          @SecondaryAuditCompletedClick="SecondaryAuditCompletedClick"
          @QaeStartClick="QaeStartClick"
          @RegisterClick="RegisterClick"
          @ChargebackClick="ChargebackClick"
          @PerformanceClick="PerformanceClick"
          @ProductionOrder="ProductionOrder"
          @FallbackFrontClick="FallbackFrontClick"
          ref="actionqae"
          :total="pagination.total"
        />
      </div>
      <a-modal title="拼版图" :visible="makeupVisible" :footer="null" @cancel="handleCancel">
        <makeup-pic ref="makeup"></makeup-pic>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <query-info ref="queryInfo" />
      </a-modal>
      <!-- 编辑参数 -->
      <a-modal
        title="CAM参数设置"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        :footer="null"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <edit-parameters-make ref="EditParametersMake" :selectedRowsData="selectedRowsData" :ParameterData="ParameterData" />
      </a-modal>
      <!-- 查看叠层弹窗 -->
      <a-modal
        title="叠层"
        :visible="dataVisible5"
        @cancel="reportHandleCancel"
        :footer="null"
        @ok="handleOk5"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        class="modalSty"
        :confirmLoading="confirmLoading"
      >
        <generate-stack-info ref="GenerateStack" :stackListData="stackListData" />
      </a-modal>
      <!-- 返修开始弹窗 -->
      <a-modal
        title="返修开始"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="550"
        :confirmLoading="confirmLoading"
        centered
      >
        <repairStart-info ref="RepairStart" />
      </a-modal>
      <!-- 返修记录弹窗 -->
      <a-modal
        title="返修记录"
        :visible="dataVisible6"
        @cancel="reportHandleCancel"
        @ok="handleOk6"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        :confirmLoading="confirmLoading"
      >
        <repair-record-info ref="RepairRecord" :RepairRecordData="RepairRecordData" />
      </a-modal>
      <!-- 客户规则弹窗 -->
      <a-modal
        title="客户规则"
        :visible="dataVisible7"
        @cancel="reportHandleCancel"
        @ok="handleOk7"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        :confirmLoading="confirmLoading"
      >
        <customer-rules-info ref="CustomerRules" :CustomerData="CustomerData" />
      </a-modal>
      <!-- 日志弹窗 -->
      <a-modal
        title="日志"
        :visible="dataVisible8"
        @cancel="reportHandleCancel"
        @ok="handleOk8"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="450"
        :confirmLoading="confirmLoading"
      >
        <view-log-info ref="viewLogInfo" :viewLogData="viewLogData" />
      </a-modal>
      <!-- 取单设置 -->
      <a-modal
        title="取单设置"
        :visible="dataVisible9"
        @cancel="reportHandleCancel"
        @ok="handleOk9"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="720"
        :confirmLoading="confirmLoading"
        centered
      >
        <order-retrieval-settings-qae ref="OrderRetrievalSettings" :data="data" :peopleOrderInfoList="peopleOrderInfoList" />
      </a-modal>
      <!-- 注意事项弹窗 -->
      <a-modal
        title="信息传递"
        :visible="dataVisibleMatter"
        @cancel="reportHandleCancel"
        @ok="handleOkMatter"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="550"
        :confirmLoading="confirmLoading"
        centered
      >
        <matters-needing-attention-info ref="mattersNeedingAttention" :editData="editData" />
      </a-modal>
      <!-- 绩效管理 -->
      <performance ref="performanceModal" :selectedRowsData="selectedRowsData" :type="2"> </performance>
      <a-modal
        title=" 确认弹窗"
        :visible="dataVisibleMode"
        @cancel="reportHandleCancel"
        @ok="handleOkMode"
        ok-text="确定"
        :confirmLoading="okload"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div v-if="type != '5'">
          <span style="font-size: 14px; color: #000000">【{{ orderno }}】</span>
          <span style="font-size: 14px; color: #000000">{{ messageMode }}</span>
        </div>
        <span style="font-size: 14px; color: #000000" v-if="type == '5'">
          <h3 style="font-weight: 500; color: #000000; font-size: 14px">是否将订单号为：</h3>
          <div class="tabBox">
            <a-tag color="orange" v-for="(item, index) in assignOrderList1" :key="index + '_assign'">
              {{ item }}
            </a-tag>
          </div>
          <div style="font-weight: 500; font-size: 14px; color: #000000">分派给：{{ realName }}</div>
        </span>
      </a-modal>
      <!-- QAE完成是否二审 -->
      <a-modal
        :title="'【' + orderno + '】 完成'"
        :visible="dataVisibleqae"
        @cancel="dataVisibleqae = false"
        @ok="handleOkqae"
        ok-text="确定"
        cancel-text="取消"
        :confirmLoading="okload"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div class="class" style="font-size: 16px">是否二审: <a-checkbox v-model="IsQae" :disabled="!this.IsQAEEnble"> </a-checkbox></div>
      </a-modal>
      <a-modal :title="meslist" :visible="dataVisibleNo" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
        <template slot="footer">
          <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
          <a-button @click="reportHandleCancel">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
          </p>
          <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
      <!--红马生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="HMnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="hmnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="850"
      >
        <report-hmnoticeform :ttype="'EMS | QAE审核'" :HMnoticedata="HMnoticedata" ref="hmnotice"></report-hmnoticeform>
      </a-modal>
      <!--联合多层生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="LHDCnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="lhdcnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1400"
      >
        <report-lhdcnoticeform :ttype="'EMS | QAE审核'" :LHDCnoticedata="LHDCnoticedata" ref="lhdcnotice"></report-lhdcnoticeform>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
import ReportHmnoticeform from "@/pages/mkt/OrderOffer/report/ReportHmnoticeform";
import { proordercoefficient } from "@/services/projectDisptch";
import { ppebuttonCheck } from "@/services/projectMake/index.js";
import { noticereviewinfo, productionreport } from "@/services/mkt/OrderReview.js";
import { setEngineeringQae } from "@/utils/request";
import {
  peopleOrderList,
  projectBackEndAssign,
  projectBackEndAssignBack,
  projectBackEndOrderDetail,
  projectQaeOrderList,
  projectBackEndPeopleList,
  RepairStart,
  qaeFinish,
  isfinishqaeenble,
  qaeFinish2,
  qaeBackOrder,
  qaeBack2Cam,
  TakeOrderList,
  downFile,
  orderRetrievalSettings,
  RetrievalSettings,
  makeInfo,
  qaeStart,
  EngineeringQaeCopyNewOrder,
} from "@/services/projectQae";
import {
  MakeStart,
  getWenkeUrl,
  getGenerateStack,
  getRepairRecord,
  saveRepairRecord,
  getStackImpedance,
  getCustomerInfo,
  getViewLog,
  getParameter,
  SaveParameter,
  projectBackEndJobInfo,
  fileReplacement,
  proOrderInfo,
  delInfo,
  repierMatters,
} from "@/services/projectMake";
import { checkPermission } from "@/utils/abp";
import LeftTableQae from "@/pages/gongcheng/projectQae/subassembly/LeftTableQae";
import OrderDetailQae from "@/pages/gongcheng/projectQae/subassembly/OrderDetailQae";
import QaeAction from "@/pages/gongcheng/projectQae/subassembly/QaeAction";
import OrderRetrievalSettingsQae from "@/pages/gongcheng/projectQae/subassembly/OrderRetrievalSettingsQae";
import RepairStartInfo from "@/pages/gongcheng/projectQae/subassembly/RepairStartInfo";

import MakeupPic from "@/pages/gongcheng/projectMake/subassembly/MakeupPic";
import QueryInfo from "@/pages/gongcheng/projectMake/subassembly/QueryInfo";
import OrderDetail from "@/pages/gongcheng/projectMake/subassembly/OrderDetail";
import GenerateStackInfo from "@/pages/gongcheng/projectMake/subassembly/GenerateStackInfo.vue";
import RepairRecordInfo from "@/pages/gongcheng/projectMake/subassembly/RepairRecordInfo.vue";
import CustomerRulesInfo from "@/pages/gongcheng/projectMake/subassembly/CustomerRulesInfo.vue";
import viewLogInfo from "@/pages/gongcheng/projectMake/subassembly/viewLogInfo";
import EditParametersMake from "@/pages/gongcheng/projectMake/subassembly/EditParametersMake";
import Performance from "@/pages/gongcheng/projectMake/subassembly/Performance";
import mattersNeedingAttentionInfo from "@/pages/gongcheng/projectMake/subassembly/mattersNeedingAttentionInfo";
import ReportLhdcnoticeform from "@/pages/mkt/OrderOffer/report/ReportLhdcnoticeform";
import Cookie from "js-cookie";

// import Cookie from "_js-cookie@2.2.1@js-cookie";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    ellipsis: true,
    fixed: "left",
    scopedSlots: { customRender: "num" },
    width: 37,
  },
  {
    title: "生产编号",
    dataIndex: "orderNo",
    scopedSlots: { customRender: "orderNo" },
    align: "left",
    fixed: "left",
    ellipsis: true,
    width: 118,
    className: "userStyle",
  },

  {
    title: "订单状态",
    dataIndex: "statusType",
    align: "left",
    ellipsis: true,
    width: 63,
  },
  {
    title: "订单类型",
    dataIndex: "isReOrder",
    scopedSlots: { customRender: "isReOrder" },
    align: "left",
    ellipsis: true,
    width: 63,
  },

  {
    title: "审核人",
    dataIndex: "camCheckAdminId",
    width: 53,
    ellipsis: true,
    align: "left",
  },
  {
    title: "二审人",
    dataIndex: "miqaeAccName",
    width: 53,
    ellipsis: true,
    align: "left",
  },
  {
    title: "交期",
    dataIndex: "deliveryDate",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 110,
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "left",
    ellipsis: true,
    width: 48,
    sorter: (a, b) => {
      return a.boardLayers - b.boardLayers;
    },
  },
  {
    title: "耗时",
    dataIndex: "qaeCostTime",
    width: 60,
    ellipsis: true,
    sorter: (a, b) => {
      return a.qaeCostTime - b.qaeCostTime;
    },
    align: "left",
  },

  {
    title: "面积",
    dataIndex: "boardArea",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "前端制作",
    dataIndex: "makeUserName_",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "后端制作",
    dataIndex: "backEndRealName",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "EQ完耗时(h)",
    width: 90,
    scopedSlots: { customRender: "eqCostTime" },
    sorter: (a, b) => {
      return a.eqCostTime - b.eqCostTime;
    },
    ellipsis: true,
    align: "left",
  },
  {
    title: "剩余时间(h)",
    dataIndex: "timeOut",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "板材信息",
    dataIndex: "boardBrand",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "板厚",
    dataIndex: "boardThickness",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "文件下载",
    scopedSlots: { customRender: "down" },
    width: 100,
    ellipsis: true,
    align: "left",
    className: "noCopy",
  },
  {
    title: "客户规则",
    scopedSlots: { customRender: "mbid" },
    width: 100,
    ellipsis: true,
    align: "left",
    className: "noCopy",
  },
  {
    title: "拼版图",
    dataIndex: "jigsawPuzzle",
    align: "left",
    width: 67,
    scopedSlots: { customRender: "jigsawPuzzle" },
    // customRender: function(num) {
    //   return num.toFixed(2)
    // }
  },
  {
    title: "问客",
    dataIndex: "isEQ",
    scopedSlots: { customRender: "isEQ" },
    // customRender: (text, record, index) => `${record.isEQ ? '是' : ''}`,
    width: 60,
    className: "noCopy",
    align: "left",
  },
  {
    title: "CAM完成时间",
    dataIndex: "camEndTime",
    width: 130,
    ellipsis: true,
    align: "left",
  },
  {
    title: "下单时间",
    dataIndex: "createTime",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 130,
  },
  {
    title: "派单时间",
    dataIndex: "camSendDate",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 130,
  },
  {
    title: "系数",
    scopedSlots: { customRender: "score" },
    align: "left",
    width: 60,
  },
  {
    title: "加工工厂",
    dataIndex: "orderChannel",
    width: 75,
    ellipsis: true,
    align: "left",
  },
  {
    title: "预审人",
    dataIndex: "preName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "报价人",
    dataIndex: "checkName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "输出状态",
    dataIndex: "camPnlOutStatus",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "队列状态",
    dataIndex: "toErpState",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "队列信息",
    dataIndex: "toErpMsg",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "版本",
    dataIndex: "proRev",
    className: "userStyle",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  //  {
  //   title: "操作",
  //   align:'center',
  //   scopedSlots: { customRender: 'labelUrl' },
  //   width:50,
  //   className:'userStyle noCopy'
  //  },
  // {
  //   title: "合同编号",
  //   dataIndex: "businessOrderNo",
  //   align: "left",
  //   ellipsis: true,
  //   width: 130,
  //   className: 'userStyle'
  //  },
  // {
  //   title: "制作",
  //   scopedSlots: { customRender: 'script' },
  //   align: "center",
  //   fixed:'right',
  //   width: 40,   // 2290
  // },
  {
    title: "MI",
    scopedSlots: { customRender: "action1" },
    align: "center",
    fixed: "right",
    width: 40, // 2290
  },
  {
    title: "指示",
    scopedSlots: { customRender: "action2" },
    align: "center",
    fixed: "right",
    className: "noCopy",
    width: 40,
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 45,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "项目",
    dataIndex: "projectName",
    width: 130,
    ellipsis: true,
    align: "left",
    customCell: (record, rowIndex) => {
      if (record.projectName == "铜厚(外)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // copperThickness
      } else if (record.projectName == "铜厚(内)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // innerCopperThickness 铜厚(内)oz
      } else if (record.projectName == "字符颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; // fontColor 字符颜色(顶)
      } else if (record.projectName == "阻焊颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; //solderColor 阻焊颜色(顶)
      }
      // else if (record.projectName == '阻焊颜色(底)' && record.parameter == '无') {
      //   return { style: { 'background': '#ff0000!important', } } // solderColorBottom 阻焊颜色(底)
      // }
      else if (record.projectName == "过孔处理" && record.parameter == "过孔开窗") {
        return { style: { color: "#ff00ff!important" } }; // solderCover 过孔处理
      } else if (record.projectName == "过孔处理" && record.parameter.indexOf("塞油") >= 0) {
        return { style: { color: "#ff0000!important" } }; // solderCover 过孔处理
      } else if (record.projectName == "订单数量" && record.boardArea > 1.0) {
        return { style: { color: "#ff0000!important" } }; // num 数量
      } else if (record.projectName == "阻抗报告" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } }; // impedanceReport 阻抗报告
      } else if (record.projectName == "阻抗" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } }; // impedanceSize 阻抗
      } else if (record.projectName == "图形转移工艺" && record.parameter == "丝网印刷") {
        return { style: { color: "#ff0000!important" } }; // imageTranster 图形转移工艺
      } else if (record.projectName == "文件名" && record.count > 0) {
        return { style: { color: "#ff0000!important" } }; // fileUploadedCount 文件上传次数>0 文件名显示红色
      } else if (record.projectName == "盘中孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "半边孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "异形孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "板边包金" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "沉孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "印序列号" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "蓝胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指面积" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指金厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指镍厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "压接孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "背钻孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "通孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "阶梯孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "碳油" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "高温胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "斜边" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲槽" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋铜" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋阻" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "铜浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "银浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      }
    },
  },
  {
    title: "参数",
    dataIndex: "parameter",
    width: 121,
    align: "left",
    ellipsis: true,
    className: "userStyle heightSTY",
    scopedSlots: { customRender: "parameter" },
    customCell: (record, rowIndex) => {
      if (record.projectName == "铜厚(外)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // copperThickness
      } else if (record.projectName == "铜厚(内)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // innerCopperThickness 铜厚(内)oz
      } else if (record.projectName == "字符颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; // fontColor 字符颜色(顶)
      } else if (record.projectName == "阻焊颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; //solderColor 阻焊颜色(顶)
      } else if (record.projectName == "过孔处理" && record.parameter == "过孔开窗") {
        return { style: { color: "#ff00ff!important" } }; // solderCover 过孔处理
      } else if (record.projectName == "过孔处理" && record.parameter.indexOf("塞油") >= 0) {
        return { style: { color: "#ff0000!important" } }; // solderCover 过孔处理
      } else if (record.projectName == "订单数量" && record.boardArea > 1.0) {
        return { style: { color: "#ff0000!important" } }; // num 数量
      } else if (record.projectName == "阻抗报告" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } }; // impedanceReport 阻抗报告
      } else if (record.projectName == "阻抗" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } }; // impedanceSize 阻抗
      } else if (record.projectName == "图形转移工艺" && record.parameter == "丝网印刷") {
        return { style: { color: "#ff0000!important" } }; // imageTranster 图形转移工艺
      } else if (record.projectName == "文件名" && record.count > 0) {
        return { style: { color: "#ff0000!important" } }; // fileUploadedCount 文件上传次数>0 文件名显示红色
      } else if (record.projectName == "盘中孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "半边孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "异形孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "板边包金" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "沉孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "印序列号" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "蓝胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指面积" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指金厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指镍厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "压接孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "背钻孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "通孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "阶梯孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "碳油" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "高温胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "斜边" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲槽" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋铜" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋阻" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "铜浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "银浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      }
    },
  },
];
const columns3 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 34,
    scopedSlots: { customRender: "num" },
  },
  {
    title: "小组",
    dataIndex: "groups",
    width: 55,
    ellipsis: true,
    align: "left",
  },
  {
    title: "姓名",
    dataIndex: "realName",
    width: 85,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "customRender" },
  },
  {
    title: "目标",
    dataIndex: "targetCount_",
    width: 34,
    ellipsis: true,
    align: "left",
  },
  {
    title: "已发",
    dataIndex: "numendCounts",
    width: 34,
    ellipsis: true,
    align: "center",
  },
  {
    title: "系数",
    customRender: (text, record, index) => (record.score ? record.score.toFixed(2) : ""),
    align: "left",
    width: 50,
    ellipsis: true,
  },
  {
    title: "操作",
    width: 34,
    align: "center",
    scopedSlots: { customRender: "action" },
  },
];
const columns4 = [
  {
    title: "操作人",
    dataIndex: "inUserName",
    width: 82,
    align: "left",
    ellipsis: true,
  },
  {
    title: "创建时间",
    dataIndex: "indate",
    width: 95,
    align: "left",
    ellipsis: true,
  },
  {
    title: "备注信息",
    dataIndex: "conent",
    // width: '23%',
    align: "left",
    ellipsis: true,
  },
  {
    title: "图片",
    width: "15%",
    scopedSlots: { customRender: "picUrl" },
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: "fileUrl" },
    align: "left",
    className: "noCopy",
    width: 60,
  },
];
const columns5 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 50,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "本厂编码",
    dataIndex: "orderNo",
    scopedSlots: { customRender: "orderNo" },
    width: 120,
    ellipsis: true,
    align: "left",
    className: "userStyle",
  },
  {
    title: "订单类型",
    dataIndex: "orderType_",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: "状态",
    dataIndex: "state_",
    ellipsis: true,
    align: "left",
    width: 100,
    scopedSlots: { customRender: "customRender" },
  },
  {
    title: "派单时间",
    dataIndex: "dispatchDate",
    width: 115,
    ellipsis: true,
    align: "left",
  },
  {
    title: "系数",
    scopedSlots: { customRender: "score" },
    align: "left",
    width: 50,
  },
  {
    title: "加工工厂",
    dataIndex: "facName",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "操作",
    width: 40,
    align: "left",
    scopedSlots: { customRender: "action" },
  },
];
const projectArray = [
  { name: "工厂编号", value: "orderNo" },
  { name: "层数", value: "boardLayers" },
  { name: "板材信息", value: "fR4TypeStr" },
  { name: "板材型号", value: "boardBrandStr" },
  { name: "成品板厚", value: "boardThickness" },
  // {name:'内/外完成铜厚',value:'copperThicknessStr'},
  { name: "铜厚(外)oz", value: "copperThicknessStr" },
  { name: "铜厚(内)oz", value: "innerCopperThicknessStr" },
  { name: "阻焊颜色(顶)/(底)", value: "solderColorStr" },
  // {name:'阻焊颜色(底)',value:'solderColorBottom'},
  { name: "字符颜色(顶)/(底)", value: "fontColorStr" },
  // {name:'字符颜色(底)',value:'fontColorBottom'},
  { name: "表面处理", value: "surfaceFinishStr" },
  { name: "过孔处理", value: "solderCoverStr" },
  { name: "单元尺寸(长X宽)", value: "boardHeight" },
  { name: "成品尺寸(长X宽)", value: "boardHeightSet" },
  { name: "出货类型", value: "pinBanType" },
  { name: "测试方式", value: "flyingProbeStr" },
  { name: "文件名", value: "pcbFileName" },
  { name: "拼版数量", value: "pinBanNum" },
  { name: "电镀工艺", value: "beforePlating" },
  { name: "图形转移工艺", value: "imageTranster" },
  { name: "最小孔", value: "vias" },
  { name: "线宽线距(mil)", value: "lineWeight" },
  { name: "订单数量", value: "num" },
  // {name:'成型方式',value:'formingTypeStr'},
  // {name:'阻抗报告',value:'impedanceReport'},
  { name: "阻抗", value: "isImpedance" },
  // 特殊工艺
  { name: "盘中孔", value: "isDiscHole" },
  { name: "半边孔", value: "isHalfHole" },
  { name: "异形孔", value: "isProfileHole" },
  { name: "板边包金", value: "isPlateEdge" },
  { name: "沉孔", value: "stepHole" },
  { name: "印序列号", value: "isSerialNumber" },
  { name: "蓝胶", value: "isBlueGum" },
  { name: "金手指", value: "isGoldfinger" },
  { name: "金手指面积", value: "goldenFingerAreaRe" },
  { name: "金手指金厚", value: "goldFingerThickness" },
  { name: "金手指镍厚", value: "goldfingerNickelThickness" },
  { name: "压接孔", value: "isCrimpHole" },
  { name: "背钻孔", value: "isBackDrilling" },
  { name: "通孔控深", value: "isThroughHoleControl" },
  { name: "盲孔控深", value: "isBlindHoleControl" },
  { name: "阶梯孔", value: "steppedHole" },
  { name: "碳油", value: "isCarbonOil" },
  { name: "高温胶", value: "heatTape" },
  { name: "斜边", value: "isGoldfingerBevel" },
  { name: "盲槽", value: "isBlindSlot" },
  { name: "埋铜", value: "buriedCopper" },
  { name: "埋阻", value: "buriedResistance" },
  { name: "铜浆塞孔", value: "cuPlugHole" },
  { name: "银浆塞孔", value: "silverPlugHole" },
];
import { mapState } from "vuex";
import moment from "moment";

export default {
  name: "projectQae",
  components: {
    MakeupPic,
    QaeAction,
    OrderDetailQae,
    LeftTableQae,
    QueryInfo,
    GenerateStackInfo,
    RepairRecordInfo,
    CustomerRulesInfo,
    viewLogInfo,
    EditParametersMake,
    RepairStartInfo,
    OrderRetrievalSettingsQae,
    OrderDetail,
    Performance,
    mattersNeedingAttentionInfo,
    ReportHmnoticeform,
    RightCopy,
    ReportLhdcnoticeform,
  },
  inject: ["reload"],
  data() {
    return {
      IsQae: false,
      IsQAEEnble: false,
      HMnoticeVisible: false,
      LHDCnoticedata: {},
      LHDCnoticeVisible: false,
      HMnoticedata: {},
      dataVisibleNo: false,
      xstitle: "",
      check1: false,
      checkData: [],
      checkType: "",
      meslist: "",
      heighty: window.innerHeight,
      showText: false,
      iscolumnKey: false,
      isorder: false,
      assignOrderList1: [],
      showText1: false,
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      menuVisible1: false,
      showText2: false,
      menuStyle2: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      menuVisible2: false,
      ooeder: "",
      ooeder1: "",
      ooeder2: "",
      type: "",
      confirmLoading: false,
      okload: false,
      spinning: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      query: {
        OrderNo: "",
      },
      columns1,
      orderListData: [],
      orderListTableLoading: false,
      columns2,
      orderDetailData: [],
      orderDetailTableLoading: false,
      columns3,
      peopleOrderInfoList: [],
      peopleOrderInfoTableLoading: false,
      columns4,
      jobData: [],
      jobTableLoading: false,
      columns5,
      peopleOrderListData: [],
      peopleOrderListTableLoading: false,
      jobSwitch: false,
      note: "",
      cnNote: "",
      erpKey: "",
      proOrderId: "",
      assignLoading: false,
      makeupVisible: false, // 拼版图弹窗开关
      allNote: "",
      dataVisible: false, // 查询弹窗开关
      dataVisible3: false, // 编辑参数弹窗开关
      dataVisible4: false, // 返修开始弹窗开关
      dataVisible5: false, // 生成叠层开关
      dataVisible6: false, // 返修记录开关
      dataVisible7: false, // 客户规则开关
      dataVisible8: false, // 查看日志
      dataVisible9: false, // 取单设置
      RepairRecordData: [], // 返修记录
      stackListData: {}, // 生成叠层
      StackImpedanceData: [], // 叠层阻抗
      CustomerData: [], // 客户规则
      viewLogData: [], // 日志数据
      ParameterData: {}, // 编辑参数
      selectedRowsData: {},
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      data: {
        userNo_: 0,
        isLeave_: false,
        targetCount_: 0,
        isScript_: false,
        isHighQuality: 0,
        labels: 0,
        getNum: 0,
        stayNum: 0,
        maxNum: 0,
        length: 0,
        width: 0,
        aluminum: false,
        nonAluminum: false,
        layer1: false,
        layer2: false,
        layer4: false,
        layer6: false,
        layer8: false,
        domesticTrade: false,
        foreignTrade: false,
        ordinary: false,
        premiumProducts: false,
        boutique: false,
        area1: false,
        area2: false,
        area3: false,
        area4: false,
        area5: false,
        area6: false,
        pcs: false,
        customerSet: false,
        autoStartDate: null, // 开始时间
        autoEndDate: null, // 结束时间
        jpSet: false,
        noCoordination: false,
        coordination: false,
        bigCus: false,
        noBigCus: false,
        tradeType: 0,
        jdbOrder: false,
        dqOrder: false,
        sort: 0,
        jpOrder: false,
        isRandom: false,
        beonDuty: false, //工程值班
        jlcOrder: false, // 嘉立创
        hqOrder: false, // 华秋
        lhdcOrder: false, // 联合多层
        plOrder: false, // 普林
        tlOrder: false, // 塔联
        hzxOrder: false, // 合众鑫
        xsjOrder: false, // 兴晟捷
        jdOrder: false, // 吉达
      },
      wsUrl: "",
      pacurrent: "",
      websocket: null,
      showNote: "",
      cookieId: "",
      dataVisibleMode: false,
      dataVisibleqae: false,
      messageMode: "",
      realName: "",
      orderno: "",
      recordId: "",
      recordProOrderId: "",
      pageShow: true,
      text: "",
      visiblePerformance: false,
      dataVisibleMatter: false,
      editData: {},
      foldedornot: true,
      queryParam: {},
      pageStat: false,
      os: "",
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.getOrderList();
    this.getPeopleList();
    this.getMakeInfo();
  },
  computed: {
    orderDetailDataFilter() {
      let arr_ = [];
      if (this.orderDetailData.length != 0) {
        projectArray.forEach(item => {
          arr_.push({
            projectName: item.name,
            parameter: this.orderDetailData[item.value],
            count: this.orderDetailData.fileUploadedCount,
            boardArea: this.orderDetailData.boardArea,
          });
        });
        arr_ = arr_.filter(item => item.parameter != "否" && item.parameter != "" && item.parameter != "不需要" && item.parameter != null);
      }
      return arr_;
    },
    ...mapState("account", ["user"]),
  },

  mounted() {
    this.getcookie("ordernoQae");
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  methods: {
    checkPermission,
    Coefficientdetails(id) {
      proordercoefficient(id, 2).then(res => {
        if (res.code) {
          let tit = [];
          let data = res.data[0].coefficientInfos;
          for (let index = 0; index < data.length; index++) {
            tit.push("【" + data[index].description + " : " + data[index].score + "】");
          }
          this.xstitle = tit.join(" + ");
        }
      });
    },
    handleResize() {
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftstyle1 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1]
          .children[0];
      var leftstyle2 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[2].children[1]
          .children[0];
      if (document.getElementsByClassName("orderstyle").length != 0) {
        var orderstyle = document.getElementsByClassName("orderstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      }
      if (document.getElementsByClassName("qaestyle").length != 0) {
        var qaestyle = document.getElementsByClassName("qaestyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      }
      if (document.getElementsByClassName("jobstyle").length != 0) {
        var jobstyle = document.getElementsByClassName("jobstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      }
      if (document.getElementsByClassName("job1style").length != 0) {
        var job1style = document.getElementsByClassName("job1style")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      }
      if (document.getElementsByClassName("textNote").length != 0) {
        var textNote = document.getElementsByClassName("textNote")[0];
      }
      var leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 130 + "px";
      } else {
        leftContent.style.height = "782px";
      }

      if (leftstyle && this.orderListData.length != 0) {
        leftstyle.style.height = window.innerHeight - 174 + "px";
        leftstyle1.style.height = window.innerHeight - 174 + "px";
        leftstyle2.style.height = window.innerHeight - 174 + "px";
      } else {
        leftstyle.style.height = 0;
        leftstyle1.style.height = "10px";
        leftstyle2.style.height = "10px";
      }
      if (orderstyle && this.orderDetailDataFilter.length != 0) {
        orderstyle.style.height = window.innerHeight - 210 + "px";
      } else if (orderstyle) {
        orderstyle.style.height = 0;
      }
      if (qaestyle && this.peopleOrderInfoList.length != 0) {
        qaestyle.style.height = window.innerHeight - 210 + "px";
      } else if (qaestyle) {
        qaestyle.style.height = 0;
      }
      if (jobstyle && this.jobData.length != 0) {
        jobstyle.style.height = (window.innerHeight - 216) / 2 + 38 + "px";
      } else if (jobstyle) {
        jobstyle.style.height = 0;
      }
      if (job1style && this.peopleOrderListData.length != 0) {
        job1style.style.height = window.innerHeight - 168 + "px";
        // (window.innerHeight - 210)/2 +38+'px'
      } else if (job1style) {
        job1style.style.height = 0;
      }
      if (textNote && this.allNote && this.jobSwitch) {
        textNote.style.height = (window.innerHeight - 210) / 2 + "px";
      } else if (this.jobSwitch) {
        textNote.style.height = 0;
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.action.nums * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }

        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.reportHandleCancel();
        this.queryClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "83" && this.isCtrlPressed && checkPermission("MES.EngineeringModule.EngineeringQae.EngineeringQaeSendOrder")) {
        this.reportHandleCancel();
        this.assignClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "81" && this.isCtrlPressed && checkPermission("MES.EngineeringModule.EngineeringQae.EngineeringQaeOrder")) {
        this.TakeOrderClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "67" && !this.isCtrlPressed && this.dataVisibleMode) {
        this.reportHandleCancel();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisibleMode) {
        this.handleOkMode();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    modifyInfoClick1(record, selectedRowKeysArray) {
      this.selectedRowsData = record;
      var arr = [];
      for (var i = 0; i < selectedRowKeysArray.length; i++) {
        var str = this.orderListData.filter(ite => {
          return ite.proOrderId == selectedRowKeysArray[i];
        })[0].orderNo;
        arr.push(str);
      }
      this.assignOrderList1 = arr;
    },
    onClickRow1(record) {
      return {
        on: {
          contextmenu: e => {
            e.preventDefault();
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    // 获取当日制作数据
    getMakeInfo() {
      makeInfo().then(res => {
        if (res.code) {
          this.showNote = res.data;
        }
      });
    },
    // 获取订单
    getOrderList(queryData) {
      this.pageStat = localStorage.getItem("stat6") == "true" ? true : false;
      if (this.pageStat) {
        let pageCurrent = localStorage.getItem("pageCurrent6");
        if (pageCurrent != undefined && pageCurrent != "") {
          this.pagination.current = parseInt(pageCurrent);
        }
        let pageSize = localStorage.getItem("pageSize6");
        if (pageSize != undefined && pageSize != "") {
          this.pagination.pageSize = parseInt(pageSize);
        }
        let data = localStorage.getItem("queryParam6");
        if (data != null && data != undefined && data != "") {
          this.queryParam = JSON.parse(data);
        }
      }
      this.queryParam.pageIndex = this.pagination.current;
      this.queryParam.pageSize = this.pagination.pageSize;
      let data = {
        ...this.queryParam,
      };
      localStorage.setItem("queryParam6", JSON.stringify(data));
      let params = {
        pageIndex: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (queryData) {
        params.OrderNo = queryData.OrderNo;
        params.Status = queryData.Status;
        params.PcbFileName = queryData.PcbFileName;
      }
      let record = localStorage.getItem("record6");
      let indexId = localStorage.getItem("OrderId6");
      this.orderListTableLoading = true;
      params.PcbFileName = params.PcbFileName ? params.PcbFileName.replace(/\s+/g, " ").trim() : "";
      projectQaeOrderList(params)
        .then(res => {
          if (res.items) {
            this.orderListData = res.items;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            if (this.iscolumnKey) {
              this.orderListData.sort((a, b) => {
                let aValue = a[this.iscolumnKey];
                let bValue = b[this.iscolumnKey];
                if (typeof aValue === "string" && typeof bValue === "string") {
                  return this.isorder === "ascend" ? aValue.localeCompare(bValue) : this.isorder === "descend" ? bValue.localeCompare(aValue) : 0;
                } else {
                  if (aValue === bValue) {
                    return this.orderListData.indexOf(a) - this.orderListData.indexOf(b);
                  }
                  if (this.isorder === "ascend") {
                    return aValue > bValue ? 1 : -1;
                  } else if (this.isorder === "descend") {
                    return aValue < bValue ? 1 : -1;
                  }
                  return 0;
                }
              });
            }
            const pagination = { ...this.pagination };
            pagination.total = res.totalCount;
            this.pagination = pagination;
            if ((params.OrderNo || params.Status || params.PcbFileName) && this.orderListData.length) {
              this.$refs.orderTable.selectedRowKeysArray[0] = this.orderListData[0].proOrderId;
              this.$refs.orderTable.proOrderId = this.orderListData[0].proOrderId;
              this.$refs.orderTable.selectedRowsData.orderNo = this.orderListData[0].orderNo;
              this.$refs.orderTable.selectedRowsData = this.orderListData[0];
              this.assignOrderList1 = [this.orderListData[0].orderNo];
            }
          } else {
            this.$message.error(res.message);
          }
          if (indexId !== "" && indexId != null) {
            this.$refs.orderTable.proOrderId = indexId;
            this.$refs.orderTable.selectedRowKeysArray[0] = indexId;
          }
          if (record != null) {
            this.$refs.orderTable.selectedRowsData = JSON.parse(record);
            this.getOrderDetail(JSON.parse(record));
          }
          if (this.pageStat) {
            localStorage.removeItem("OrderId6");
            localStorage.removeItem("record6");
            localStorage.removeItem("pageCurrent6");
            localStorage.removeItem("pageSize6");
            localStorage.removeItem("stat6");
            localStorage.removeItem("queryParam6");
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    // 获取订单详情
    getOrderDetail(record) {
      this.orderDetailTableLoading = true;
      var obj = {};
      proOrderInfo(record.proOrderId)
        .then(res => {
          if (res.code) {
            if (res.data) {
              obj = res.data.proOrderInfoDto;
              Object.keys(obj).forEach(key => {
                if (obj[key] === true || obj[key] === false) {
                  obj[key] = obj[key] ? "是" : "";
                }
              });
              obj.boardHeight = res.data.proOrderInfoDto.boardHeight + "x" + res.data.proOrderInfoDto.boardWidth; // 单元尺寸
              obj.boardHeightSet = res.data.proOrderInfoDto.boardHeightSet + "x" + res.data.proOrderInfoDto.boardWidthSet; // 成品尺寸
              if (res.data.proOrderInfoDto.formingTypeStr && res.data.proOrderInfoDto.pinBanType && res.data.proOrderInfoDto.boardTypeStr) {
                obj.pinBanType =
                  res.data.proOrderInfoDto.formingTypeStr +
                  "," +
                  res.data.proOrderInfoDto.pinBanType +
                  "(" +
                  res.data.proOrderInfoDto.boardTypeStr +
                  ")";
              } else if (res.data.proOrderInfoDto.formingTypeStr && res.data.proOrderInfoDto.pinBanType && !res.data.proOrderInfoDto.boardTypeStr) {
                obj.pinBanType = res.data.proOrderInfoDto.formingTypeStr + "," + res.data.proOrderInfoDto.pinBanType;
              } else if (res.data.proOrderInfoDto.pinBanType && res.data.proOrderInfoDto.boardTypeStr && !res.data.proOrderInfoDto.formingTypeStr) {
                obj.pinBanType = res.data.proOrderInfoDto.pinBanType + "(" + res.data.proOrderInfoDto.boardTypeStr + ")";
              } else {
                obj.pinBanType = res.data.proOrderInfoDto.pinBanType;
              }
              obj.fR4TypeStr =
                res.data.proOrderInfoDto.sheetTraderStr + "" + res.data.proOrderInfoDto.fR4TypeStr + "" + res.data.proOrderInfoDto.fR4TgStr; //板材信息
              obj.solderColorStr = res.data.proOrderInfoDto.solderColorStr + "/" + res.data.proOrderInfoDto.solderColorBottomStr; //阻焊颜色顶/底
              obj.fontColorStr = res.data.proOrderInfoDto.fontColorStr + "/" + res.data.proOrderInfoDto.fontColorBottomStr; //字符颜色顶/底
              obj.num = res.data.proOrderInfoDto.num + " (" + res.data.proOrderInfoDto.boardArea + "㎡)";
              if (obj.boardLayers >= 2) {
                obj.copperThicknessStr = res.data.proOrderInfoDto.copperThickness + "/" + res.data.proOrderInfoDto.copperThickness; //外完成铜厚
              } else {
                obj.copperThicknessStr = res.data.proOrderInfoDto.copperThickness + "/0"; //外完成铜厚
              }

              if (obj.boardLayers > 2) {
                var srt = obj.boardLayers - 2;
                var a = res.data.proOrderInfoDto.innerCopperThickness;
                var arr = [];
                for (var i = 0; i < srt; i++) {
                  arr.push(a);
                }
                obj.innerCopperThicknessStr = arr.join("/");
                // obj.innerCopperThicknessStr = res.data.proOrderInfoDto.innerCopperThickness  // 内铜
              }
              this.orderDetailData = obj;
              setTimeout(() => {
                this.handleResize();
              }, 0);
              this.note = res.data.proOrderInfoDto.note;
              this.cnNote = res.data.proOrderInfoDto.cnNote;
              this.noteHtml(res.data.proOrderInfoDto);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderDetailTableLoading = false;
        });
    },
    // 获取人员
    getPeopleList() {
      this.peopleOrderInfoTableLoading = true;
      projectBackEndPeopleList()
        .then(res => {
          if (res.code) {
            this.peopleOrderInfoList = res.data;
            setTimeout(() => {
              this.handleResize();
            }, 0);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.peopleOrderInfoTableLoading = false;
        });
    },
    // 获取对应的订单人员
    // getJobInfo(id){
    //   this.jobTableLoading = true;
    //   projectBackEndJobInfo(id).then(res => {
    //     if (res.code) {
    //       this.jobData = res.data;
    //     }
    //   }).finally(()=>{
    //     this.jobTableLoading = false;
    //   })
    // },
    // 获取注意事项(作业记录)
    getJobInfo(id) {
      this.jobTableLoading = true;
      projectBackEndJobInfo(id)
        .then(res => {
          if (res.code) {
            this.jobData = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.jobTableLoading = false;
        });
    },
    // 获取人员对应的订单
    getPeopleOrderList(id) {
      this.peopleOrderListTableLoading = true;
      peopleOrderList({ ErpRkey: id })
        .then(res => {
          if (res.code) {
            this.peopleOrderListData = res.data;
            setTimeout(() => {
              this.handleResize();
              let maxLength = 0;
              let longestChars = [];
              let obj = document.getElementsByClassName("tagNum1");
              let arr = [];
              for (let i = 0; i < obj.length; i++) {
                arr.push(obj[i].children.length);
              }
              let result = -Infinity;
              arr.forEach(item => {
                if (item > result) {
                  result = item;
                }
              });
              for (let i = 0; i < this.peopleOrderListData.length; i++) {
                let obj2 = this.peopleOrderListData[i].orderNo;
                if (obj2) {
                  var [...chars] = obj2;
                  if (chars.length > maxLength) {
                    maxLength = chars.length;
                    longestChars = obj2;
                  }
                }
              }
              if (result == 0 && longestChars.length > 12) {
                this.columns5[1].width = 120 + (longestChars.length - 12) * 6 + "px";
                this.columns5[2].width = "100px";
              }
              if (result >= 1 && longestChars.length > 12) {
                this.columns5[1].width = 120 + (longestChars.length - 12) * 6 + result * 35 + "px";
                this.columns5[2].width = 100 - result * 4 - (longestChars.length - 12) * 2 + "px";
              }
              if (result == 0 && longestChars.length <= 12) {
                this.columns5[1].width = "120px";
                this.columns5[2].width = "100px";
              }
              if (result >= 1 && longestChars.length <= 12) {
                this.columns5[1].width = 120 + result * 35 + "px";
                this.columns5[2].width = 100 - result * 4 + "px";
              }
            }, 0);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.peopleOrderListTableLoading = false;
        });
    },
    // 图片字符串裁切
    picFilter(val) {
      if (val.picUrl) {
        return val.picUrl.split(",");
      } else {
        return [];
      }
    },
    // 订单表变化change
    handleTableChange(pagination, filter, sorter) {
      let { columnKey, order } = sorter;
      this.iscolumnKey = columnKey;
      this.isorder = order;
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.pacurrent = pagination.current;
      this.pageStat = false;
      let queryData = {
        OrderNo: this.ooeder,
        Status: this.ooeder1,
        PcbFileName: this.ooeder2,
      };
      if (JSON.stringify(queryData) != "{}") {
        this.getOrderList(queryData);
      } else {
        this.getOrderList();
      }
    },
    // 选项卡变化change
    tabpaneChange(activeKey) {
      if (activeKey == "2") {
        this.jobSwitch = true;
      } else {
        this.jobSwitch = false;
      }
      setTimeout(() => {
        this.handleResize();
      }, 0);
    },
    // 保存选中的人员erpkey
    saveErpKey(payload) {
      this.erpKey = payload;
    },
    // 分派事件
    assignClick() {
      this.type = "5";
      let guids = this.$refs.orderTable.selectedRowKeysArray;
      let userErpKey = this.erpKey;
      if (guids.length == 0) {
        this.$message.warning("请先选择订单");
        return;
      }
      if (!userErpKey) {
        this.$message.warning("请选择要分派的人员");
        return;
      }
      this.dataVisibleMode = true;
      this.orderno = this.$refs.orderTable.data1.orderNo;
      this.realName = this.$refs.peopleOrder.sselectedRowsData.realName;
    },
    // 分派回退
    assignBackClick(record) {
      // this.orderno = this.$refs.orderTable.selectedRowsData.orderNo
      this.orderno = record.orderNo;
      this.messageMode = "确认回退订单吗？";
      this.dataVisibleMode = true;
      this.type = "1";
      this.recordProOrderId = record.proOrderId;
    },
    ChargebackClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确定审核回退吗？";
      this.dataVisibleMode = true;
      this.type = "6";
    },
    // 回退CAM
    FallbackFrontClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "QaeBack2Cam")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "cam";
              this.dataVisibleNo = true;
              this.meslist = "回退CAM按钮检查";
            } else {
              this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
              this.messageMode = "确定回退CAM吗?";
              this.dataVisibleMode = true;
              this.type = "8";
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 编辑参数
    EditParametersClick(record) {
      this.selectedRowsData = record;
      getParameter(this.selectedRowsData.proOrderId).then(res => {
        if (res.code) {
          this.dataVisible3 = true;
          this.ParameterData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk3() {
      this.confirmLoading = true;
      this.spinning = true;
      let formData = this.$refs.EditParametersMake.ParameterForm;
      let params = {
        FactoryNo: formData.factoryNo ? "true" : "false",
        IsShuChu: formData.isShuChu ? "true" : "false",
        VCutToWX: formData.vCutToWX ? "true" : "false",
        PosNeg: formData.posNeg || "",
        OilBlockGreaterThan20_20: formData.oilBlockGreaterThan20_20 ? "true" : "false",
        BMCLToWZ: formData.bMCLToWZ ? "true" : "false",
        MinLineWS: formData.minLineWS || "",
        InnerMinLineWS: formData.innerMinLineWS || "",
        ThickCopperBaseThickness: formData.thickCopperBaseThickness || "",
        BgalcSmallAlert: formData.bgalcSmallAlert ? "true" : "false",
        VCut: formData.vCut ? "true" : "false",
        CounterboreCopper: formData.counterboreCopper ? "true" : "false",
        CounterboreNoCopper: formData.counterboreNoCopper ? "true" : "false",
        Etching2RL: formData.etching2RL ? "true" : "false",
        FakeDoubleSide: formData.fakeDoubleSide ? "true" : "false",
        Special2RL: formData.special2RL ? "true" : "false",
        IsPlankWatermark: formData.isPlankWatermark ? "true" : "false",
        IsSecant: formData.isSecant ? "true" : "false",
        AcceptBblt: formData.acceptBblt ? "true" : "false",
        // 'msg_':formData.msg_ || '',
      };
      params["Id"] = this.selectedRowsData.proOrderId;
      SaveParameter(params)
        .then(res => {
          if (res.code) {
            this.$message.success("编辑保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
        })
        .finally(() => {
          this.confirmLoading = false;
          this.spinning = false;
          this.dataVisible3 = false;
        });
    },
    // 查询
    queryClick() {
      this.dataVisible = true;
    },
    handleOk() {
      var payload = this.$refs.queryInfo.OrderNumber;
      var arr1 = payload.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      payload = arr1.join("");
      if (payload && typeof payload === "string" && payload.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产编号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      (this.ooeder1 = this.$refs.queryInfo.OrderStates), (this.ooeder = payload), (this.ooeder2 = this.$refs.queryInfo.pcbFileName);
      let queryData = {
        OrderNo: payload,
        Status: this.$refs.queryInfo.OrderStates,
        PcbFileName: this.$refs.queryInfo.pcbFileName,
      };
      this.pagination.current = 1;
      this.pageShow = false;
      this.getOrderList(queryData);
      this.$nextTick(() => {
        this.pageShow = true;
      });
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible = false; // 查询弹窗
      this.dataVisible1 = false; // 修改信息弹窗
      this.dataVisible2 = false; // 退单开关
      this.dataVisible3 = false; // 编辑参数开关
      this.dataVisible4 = false; // 返修开始
      this.dataVisible5 = false; // 生成叠层
      this.dataVisible6 = false; // 返修记录
      this.dataVisible7 = false; // 客户规则
      this.dataVisible8 = false; // 查看日志
      this.dataVisible9 = false; // 取单设置
      this.LHDCnoticeVisible = false;
      this.HMnoticeVisible = false;
      this.dataVisibleMatter = false;
      this.dataVisibleMode = false;
      this.visiblePerformance = false;
      this.dataVisibleNo = false;
    },
    handleOkqae() {
      this.type = "2";
      this.handleOkMode();
    },
    handleOkMode() {
      // 分派回退
      if (this.type == "1") {
        this.okload = true;
        this.spinning = true;
        projectBackEndAssignBack(this.recordProOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
          });
      }
      // 审核完成
      if (this.type == "2") {
        this.okload = true;
        this.spinning = true;
        qaeFinish(this.$refs.orderTable.selectedRowKeysArray[0], this.IsQae)
          .then(res => {
            if (res.code) {
              this.$message.success("审核完成");
              EngineeringQaeCopyNewOrder(this.$refs.orderTable.selectedRowKeysArray[0]).then(re => {});
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
          });
      }
      // 二审完成
      if (this.type == "3") {
        this.okload = true;
        this.spinning = true;
        qaeFinish2(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("二次审核完成");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
          });
      }
      // 审核开始
      if (this.type == "4") {
        this.okload = true;
        this.spinning = true;
        qaeStart(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("审核开始");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
          });
      }
      //订单分派
      if (this.type == "5") {
        let guids = this.$refs.orderTable.selectedRowKeysArray;
        let userErpKey = this.erpKey;
        let params = {
          guids: guids,
          MIAccID_: userErpKey.userNo,
          no: 0,
        };
        this.assignLoading = true;
        this.okload = true;
        this.spinning = true;
        projectBackEndAssign(params)
          .then(res => {
            if (res.code) {
              this.$message.success("分派成功");
              for (let i = 0; i < this.$refs.orderTable.data1.length; i++) {
                if (this.$refs.orderTable.data1[i].statusType == "待审核") {
                  this.$refs.orderTable.data1[i].statusType = "审核中";
                  this.$refs.orderTable.data1[i].camCheckAdminId = this.realName;
                }
                if (this.$refs.orderTable.data1[i].statusType == "待二审") {
                  this.$refs.orderTable.data1[i].statusType = "二审中";
                  this.$refs.orderTable.data1[i].miqaeAccName = this.realName;
                }
              }
            } else {
              this.$message.error(res.message);
            }
            //this.getOrderList()
            this.getPeopleList();
            this.getPeopleOrderList(userErpKey.userLoginID_);
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
            this.assignLoading = false;
          });
      }
      if (this.type == "6") {
        this.assignLoading = true;
        this.okload = true;
        this.spinning = true;
        qaeBackOrder(this.$refs.orderTable.selectedRowsData.proOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.getOrderList();
            this.reload();
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
            this.assignLoading = false;
          });
      }
      if (this.type == "7") {
        this.okload = true;
        this.spinning = true;
        delInfo(this.recordId)
          .then(res => {
            if (res.code) {
              this.$message.success("已删除");
              this.getJobInfo(this.$refs.orderTable.selectedRowsData.proOrderId);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
          });
      }
      if (this.type == "8") {
        this.okload = true;
        this.spinning = true;
        qaeBack2Cam(this.$refs.orderTable.selectedRowsData.proOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.okload = false;
            this.spinning = false;
          });
      }

      this.dataVisibleMode = false;
    },
    //生产单
    ProductionOrder() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 69) {
        this.HMnoticeform();
      } else if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 38) {
        this.LHDCnoticeform();
      } else {
        noticereviewinfo(this.$refs.orderTable.selectedRowKeysArray[0], 2).then(res => {
          if (res.code) {
            this.downloadByteArrayFromString(res.data, res.message);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //联合多层生产通知单预览
    LHDCnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowKeysArray[0], 2).then(res => {
        if (res.code) {
          this.LHDCnoticedata = res.data;
          this.LHDCnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    lhdcnoticedown() {
      this.$refs.lhdcnotice.getnoticePdf();
    },
    //红马生产通知单预览
    HMnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowKeysArray[0], 2).then(res => {
        if (res.code) {
          this.HMnoticedata = res.data;
          this.HMnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    hmnoticedown() {
      this.$refs.hmnotice.getnoticePdf();
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    // 取单
    TakeOrderClick() {
      this.spinning = true;
      //   let clock = window.setInterval(() => {
      //   this.$refs.actionqae.isDisabled=true
      //   this.$refs.actionqae.totalTime--
      //   if(this.totalTime==0){
      //     this.$refs.actionqae.totalTime=90
      //     this.$refs.actionqae.isDisabled=false
      //     clearInterval(clock)
      //   }
      // },1000)

      this.$refs.actionqae.isDisabled = true;
      setTimeout(() => {
        this.$refs.actionqae.isDisabled = false;
      }, 90000);
      ppebuttonCheck("dd87eabd-4d6e-dfc2-3202-3a10fbe46577", "engineeringqaeorder")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "qaeqd";
              this.dataVisibleNo = true;
              this.meslist = "QAE取单按钮检查";
            } else {
              TakeOrderList().then(res => {
                if (res.code) {
                  this.$message.success(res.message);
                  this.getOrderList();
                  this.getPeopleList();
                  this.$refs.peopleOrder.userLoginID_ = this.erpKey.userLoginID_;
                  this.getPeopleOrderList(this.erpKey.userLoginID_);
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 获取取单设置信息
    OrderRetrievalSettingsClick(record) {
      orderRetrievalSettings(record.userNo).then(res => {
        if (res.code) {
          res.data.realName = record.realName;
          res.data.groups = record.groups;
          this.data = res.data;
          this.data.facName = record.facName;
          if (!res.data.tradeTypeSrc) {
            res.data.tradeTypeSrc = [];
          }
          if (!res.data.custNo) {
            res.data.custNo = [];
          } else {
            res.data.custNo = String(res.data.custNo).split(",");
          }
          if (!res.data.orderByCustNo) {
            res.data.orderByCustNo = [];
          } else {
            res.data.orderByCustNo = String(res.data.orderByCustNo).split(",");
          }
          this.data.facid = record.facid;
          res.data.autoStartDate = moment(res.data.autoStartDate, "HH:mm");
          res.data.autoEndDate = moment(res.data.autoEndDate, "HH:mm");
          this.dataVisible9 = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 处理时间格式
    transformTimestamp(timestamp) {
      let a = new Date(timestamp).getTime();
      const date = new Date(a);
      const h = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      const m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      const dateString = h + m;
      return dateString;
    },
    // 取单设置
    handleOk9() {
      let a = /^\d+(\.\d+)?-\d+(\.\d+)?$/;
      let x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      let y = /^\+?[0-9][0-9]*$/;
      let z = /^(\d+(\.\d+)?-\d+(\.\d+)?)(,(\d+(\.\d+)?-\d+(\.\d+)?))*$/;
      if (this.data.targetCount_ && !y.test(this.data.targetCount_)) {
        this.$message.error("目标数量请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.targetCount_.focus();
        this.$refs.OrderRetrievalSettings.$refs.targetCount_.select();
        return;
      }
      if (this.data.maxNum && !y.test(this.data.maxNum)) {
        this.$message.error("每日总量请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.maxNum.focus();
        this.$refs.OrderRetrievalSettings.$refs.maxNum.select();
        return;
      }
      if (this.data.getNum && !y.test(this.data.getNum)) {
        this.$message.error("单次获取请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.getNum.focus();
        this.$refs.OrderRetrievalSettings.$refs.getNum.select();
        return;
      }
      if (this.data.stayNum && !y.test(this.data.stayNum)) {
        this.$message.error("停留数量请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.stayNum.focus();
        this.$refs.OrderRetrievalSettings.$refs.stayNum.select();
        return;
      }
      if (this.data.areaInterval && !z.test(this.data.areaInterval)) {
        this.$message.error("面积请输入正确的区间格式 如：0-5,如果多个区间的请用,分隔");
        this.$refs.OrderRetrievalSettings.$refs.areaInterval.focus();
        this.$refs.OrderRetrievalSettings.$refs.areaInterval.select();
        return;
      }
      this.confirmLoading = true;
      this.spinning = true;
      const params = {
        ...this.data,
        targetCount_: Number(this.data.targetCount_),
        getNum: Number(this.data.getNum),
        stayNum: Number(this.data.stayNum),
        maxNum: Number(this.data.maxNum),
        length: Number(this.data.length),
        width: Number(this.data.width),
        autoStartDate: this.transformTimestamp(this.data.autoStartDate),
        autoEndDate: this.transformTimestamp(this.data.autoEndDate),
      };
      params.custNo = params.custNo.join(",");
      params.orderByCustNo = params.orderByCustNo.join(",");
      RetrievalSettings(params)
        .then(res => {
          if (res.code) {
            this.$message.success("设置成功");
          } else {
            this.$message.error(res.message);
          }
          this.getPeopleList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible9 = false;
        });
    },
    // 返修开始
    RepairStartClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "FixStart")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "fxks";
              this.dataVisibleNo = true;
              this.meslist = "返修开始按钮检查";
            } else {
              this.dataVisible4 = true;
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleOk4() {
      this.dataVisible4 = false;
      this.spinning = true;
      let params = {
        id: this.$refs.orderTable.selectedRowKeysArray[0],
        conent: this.$refs.RepairStart.form.conent,
        picUrl: this.$refs.RepairStart.form.picUrl,
      };
      RepairStart(params)
        .then(res => {
          if (res.code) {
            this.$message.success("返修开始");
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    checkClick() {
      if (this.checkType == "shwc") {
        this.Isitqae(this.$refs.orderTable.selectedRowKeysArray[0], "fi");
      }
      if (this.checkType == "cam") {
        this.type = "8";
        this.handleOkMode();
      }
      if (this.checkType == "fxks") {
        this.handleOk4();
      }
      if (this.checkType == "qaeqd") {
        TakeOrderList().then(res => {
          if (res.code) {
            this.$message.success("成功");
            this.getOrderList();
            this.getPeopleList();
            this.$refs.peopleOrder.userLoginID_ = this.erpKey.userLoginID_;
            this.getPeopleOrderList(this.erpKey.userLoginID_);
          } else {
            this.$message.error(res.message);
          }
        });
      }
      this.dataVisibleNo = false;
    },
    // 审核完成
    AuditCompletedClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "Finish")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "shwc";
              this.dataVisibleNo = true;
              this.meslist = "审核完成按钮检查";
            } else {
              this.Isitqae(this.$refs.orderTable.selectedRowKeysArray[0]);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    Isitqae(id, type) {
      this.spinning = true;
      isfinishqaeenble(id)
        .then(res => {
          if (res.code) {
            this.IsQae = res.data.isQAE2;
            this.IsQAEEnble = res.data.isQAEEnble;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
          if (!this.IsQae) {
            if (type == "fi") {
              this.type = "2";
              this.handleOkMode();
            } else {
              this.messageMode = "确定审核完成吗？";
              this.dataVisibleMode = true;
              this.type = "2";
            }
          } else {
            this.dataVisibleqae = true;
          }
        });
    },
    // 二审分派
    SecondaryAuditAssignClick() {
      let guids = this.$refs.orderTable.selectedRowKeysArray;
      let userErpKey = this.erpKey;
      if (guids.length == 0) {
        this.$message.warning("请先选择订单");
        return;
      }
      if (!userErpKey) {
        this.$message.warning("请选择要分派的人员");
        return;
      }
      let params = {
        guids: guids,
        MIAccID_: userErpKey.userNo,
        no: 1,
      };
      this.assignLoading = true;
      projectBackEndAssign(params)
        .then(res => {
          if (res.code) {
            this.$message.success("二审分派成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.assignLoading = false;
        });
    },
    // 二审完成
    SecondaryAuditCompletedClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确定二次审核完成吗？";
      this.dataVisibleMode = true;
      this.type = "3";
    },
    // 审核开始
    QaeStartClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确定审核开始吗？";
      this.dataVisibleMode = true;
      this.type = "4";
    },
    // 返修登记
    RegisterClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      let OrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      let id = this.$refs.orderTable.selectedRowsData.proOrderId;
      const routeOne = this.$router.resolve({
        path: "/registerDetails",
        query: {
          OrderNo: OrderNo,
          pid: id,
          modu: "qae",
        },
      });
      window.open(routeOne.href, "_blank", routeOne.query);
    },
    // 查看叠层
    GenerateStackClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      getGenerateStack(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        this.dataVisible5 = true;
        this.stackListData = res.data;
      });
    },
    // 查看参数
    EditParametersClickbtn() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      getParameter(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          this.dataVisible3 = true;
          this.ParameterData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk5() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = {
        ProOrderId: this.$refs.orderTable.selectedRowKeysArray[0],
        id: this.$refs.GenerateStack.selectedRowKeysArray[0],
        paramInfo: this.$refs.GenerateStack.paramInfo,
        isSpecistack: this.$refs.GenerateStack.isSpecistack,
      };
      saveRepairRecord(params)
        .then(res => {
          if (res.code) {
            this.$message.success("叠层保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible5 = false;
        });
    },
    // 返修记录
    RepairRecordClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      getRepairRecord(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          this.RepairRecordData = res.data;
          this.dataVisible6 = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk6() {
      this.dataVisible6 = false;
    },
    // 叠层阻抗
    StackImpedanceClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      getStackImpedance(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          if (res.data.drills.length) {
            let drillsData = res.data.drills;
            localStorage.setItem("drillsData", JSON.stringify({ drillsData }));
          } else {
            localStorage.removeItem("drillsData");
          }
          this.StackImpedanceData = res.data;
          this.$router.push({
            path: "/gongju/impedance",
            query: {
              boardType: res.data.boardType,
              finishBoardThickness: res.data.finishBoardThickness,
              layers: res.data.layers,
              pdctno: res.data.pdctno,
              id: this.$refs.orderTable.selectedRowKeysArray[0],
            },
          });
          // const routeOne = this.$router.resolve({
          //   path: '/impedance',
          //   query: { boardType: res.data.boardType, finishBoardThickness: res.data.finishBoardThickness, layers: res.data.layers, pdctno: res.data.pdctno }
          // })
          // window.open(routeOne.href, '_blank', routeOne.query)
        } else {
          this.$message.error(res.message);
        }
      });
      // this.reload()
    },
    // 问客
    wenkeClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      let OrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      let businessOrderNo = this.$refs.orderTable.selectedRowsData.businessOrderNo;
      let joinFactoryId = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      this.$router.push({
        path: "eqDetails",
        query: {
          OrderNo: OrderNo,
          eQSource: 3,
          joinFactoryId: joinFactoryId,
          businessOrderNo: businessOrderNo,
          Jump: "QAE页",
          id: this.$refs.orderTable.selectedRowsData.proOrderId,
        },
      });
    },
    // 客户规则
    CustomerRulesClick(record) {
      this.dataVisible7 = true;
      let CustNo = record.custNo;
      let factory = record.joinFactoryId;
      getCustomerInfo(CustNo, factory, 1, record.businessOrderNo, record.orderNo).then(res => {
        if (res.code) {
          this.CustomerData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk7() {
      this.dataVisible7 = false;
    },
    // 查看日志
    viewLogClick(payload) {
      let params = payload.proOrderId;
      if (params) {
        getViewLog(params).then(res => {
          if (res.code) {
            this.MsgSort(res.data);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 排序
    MsgSort(obj) {
      obj.sort((a, b) => {
        let t1 = new Date(Date.parse(a.createTime.replace(/-/g, "/")));
        let t2 = new Date(Date.parse(b.createTime.replace(/-/g, "/")));
        return t2.getTime() - t1.getTime();
      });
      this.dataVisible8 = true;
      return (this.viewLogData = obj);
    },
    handleOk8() {
      this.dataVisible8 = false;
    },
    down2() {
      let input = document.createElement("input");
      input.value = this.text.trim();
      if (this.text != "") {
        document.body.appendChild(input);
        input.select();
        let bool = document.execCommand("copy");
        bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
        document.body.removeChild(input);
      }
    },
    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e, text, record) {
      let event = e.target;
      const mouseX = e.clientX;
      const mouseY = e.clientY;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode.parentNode;
      }
      if (e.target.localName == "path" || e.target.localName == "svg") {
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode;
        if (event.className.indexOf("tagNum") != -1) {
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
        }
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split("\n")[0];
      }
      this.menuVisible = true;
      this.menuStyle.top = mouseY - 100 + "px";
      this.menuStyle.left = mouseX - 225 + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 下载文件
    down0() {
      window.location.href = this.$refs.orderTable.menuData.pcbFilePath;
    },
    // 文件替换
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
      if (!isFileType) {
        this.$message.error("只支持.rar或.zip格式文件");
      }
      return isFileType;
    },
    async httpRequest0(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await fileReplacement(this.$refs.orderTable.menuData.proOrderId, formData).then(res => {
        if (res.code == 1) {
          this.$message.success("上传成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 行点击事件
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.proOrderId = record.proOrderId;
          },
          contextmenu: e => {
            e.preventDefault();
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    rightClick3(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode.parentNode;
      }
      if (e.target.localName == "path" || e.target.localName == "svg") {
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode;
        if (event.className.indexOf("tagNum") != -1) {
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
        }
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText2 = false;
      } else {
        this.showText2 = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == 2 || event.cellIndex == undefined) {
        this.text = this.text.split("\n")[0];
      }
      const tableWrapper = this.$refs.tableWrapper2;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible2 = true;
      let offsetx = event.offsetLeft + event.offsetWidth + 1270;
      let offsety = event.offsetTop + 20;
      if (event.cellIndex == this.columns4.length - 1) {
        this.menuStyle2.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle2.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle2.top = cellRect.top - wrapperRect.top - 10 + "px";
        this.menuStyle2.left = cellRect.left - wrapperRect.left + event.offsetWidth + 1250 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle2.top = offsety + "px";
        this.menuStyle2.left = offsetx + "px";
      }
      document.body.addEventListener("click", this.bodyClick3);
    },
    bodyClick3() {
      this.menuVisible2 = false;
      (this.menuStyle2 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick3);
    },
    down4() {
      let input = document.createElement("input");
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    isRedRow(record) {
      if (record.proOrderId == this.proOrderId) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    noteHtml(record) {
      let cnNoteRegStr = "";
      let noteRegStr = "";
      if (this.cnNote && this.cnNote.indexOf("https://admin.jiepei.com/") == -1) {
        cnNoteRegStr = this.cnNote.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${"http://admin.jiepei.com/"}$2$3`);
      } else {
        cnNoteRegStr = this.cnNote;
      }

      if (this.note && this.note.indexOf("https://admin.jiepei.com/") == -1) {
        noteRegStr = this.note.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${"http://admin.jiepei.com/"}$2$3`);
      } else {
        noteRegStr = this.note;
      }
      let str_ = `<div class="${this.note ? "divClass" : "displayFlag"}"><p>客户备注</p><div class="imgTable">${noteRegStr}</div></div>
                  <div class="${this.cnNote ? "divClass" : "displayFlag"}"><p>业务员备注</p><div class="imgTable">${cnNoteRegStr}</div></div>
                  <div class="${record.autoTaskMsg ? "divClass" : "displayFlag"}"><p>直通提示</p><div>${record.autoTaskMsg}</div></div>
                  <div class="${record.outTaskMsg ? "divClass" : "displayFlag"}"><p>输出提示</p><div>${record.outTaskMsg}</div></div>
                  <div class="${record.specialRemarks ? "divClass" : "displayFlag"}"><p>工程指示</p><div>${record.specialRemarks}</div></div>
                `;
      this.allNote = str_;
    },
    jigsawPuzzleClick(record) {
      this.makeupVisible = true;
      this.$nextTick(() => {
        this.$refs.makeup.impositionInformationExample(
          record.boardHeight,
          record.boardWidth,
          record.pinban_x || 1,
          record.pinban_y || 1,
          record.processeEdge_x || "none",
          record.processeEdge_y || 0,
          record.vCut || "none",
          record.cao_x || 0,
          record.cao_y || 0
        );
      });
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
    // updateUrl(urlPath) {
    //   let _this = this;
    //   if (urlPath.indexOf('sockjs') != -1) {
    //     _this.wsUrl = 'http://' + urlPath;
    //   } else {
    //     if (window.location.protocol == 'http:') {
    //       _this.wsUrl = 'ws://' + urlPath;
    //     } else {
    //       _this.wsUrl = 'ws://' + urlPath;
    //     }
    //   }
    // },
    // socketSendMessage(){
    //   let socketStr_ = {
    //     "proOrderId": this.$refs.orderTable.selectedRowsData.proOrderId,
    //     "type": '工程QAE'
    //   }
    //   if (this.websocket.readyState != 1) {
    //     // this.$message.warning('服务未链接')
    //   } else {
    //     this.websocket.send(JSON.stringify(socketStr_));
    //   }
    // },
    webSocketLink(val) {
      let proOrderId = val;
      let params = JSON.stringify({
        Token: Cookie.get("Authorization"),
        Type: "QAE",
        Task: "QAE",
        Uid: proOrderId,
        Data: {},
      });
      let url = "genesiscam://?" + params;
      window.open(url, "_blank");
      setEngineeringQae({
        token: proOrderId,
      });
      this.getcookie("ordernoQae");
    },
    // 获取cookie缓存订单id
    getcookie(ordernoQae) {
      //获取指定名称的cookie的值
      var arrstr = document.cookie.split("; ");
      for (var i = 0; i < arrstr.length; i++) {
        var temp = arrstr[i].split("=");
        if (temp[0] == ordernoQae) {
          this.cookieId = unescape(temp[1]);
          // return unescape(temp[1]);
        }
      }
    },
    downFile(event, src) {
      window.location.href = src;
    },
    // 删除注意事项
    delFile(record) {
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确认删除此条注意事项吗？";
      this.type = "7";
      this.recordId = record.id;
      this.dataVisibleMode = true;
    },

    // 绩效管理
    PerformanceClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.selectedRowsData = this.$refs.orderTable.selectedRowsData;
      this.$refs.performanceModal.openModal(this.selectedRowsData);
    },
    editClick(record) {
      this.dataVisibleMatter = true;
      this.editData = record;
    },
    // 注意事项
    handleOkMatter() {
      this.confirmLoading = true;
      let params = this.$refs.mattersNeedingAttention.form;
      params.id = this.editData.id;
      params.isbefor = false;
      this.spinning = true;
      repierMatters(params)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
          this.getJobInfo(this.$refs.orderTable.selectedRowKeysArray[0]);
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisibleMatter = false;
        });
    },
    expandIcon1() {
      if (this.foldedornot) {
        return (
          <a>
            <a-icon type="right" style="margin-right:5px" />
          </a>
        );
      } else {
        return (
          <a>
            <a-icon type="left" style="margin-right:5px" />
          </a>
        );
      }
    },
    CollapseList(val) {
      const elements1 = document.getElementsByClassName("centerTable");
      const elements2 = document.getElementsByClassName("rightTable");
      const elements3 = document.getElementsByClassName("Table");
      if (val.length) {
        this.foldedornot = true;
        elements3[0].style.width = "50%";
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.width = "100%";
          elements1[index].style.display = "";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.width = "50%";
        }
      } else {
        elements3[0].style.width = "16px";
        this.foldedornot = false;
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.display = "none";
          elements1[index].style.width = "";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.width = "100%";
        }
      }
    },
  },
  // beforeDestroy() {
  //   if (this.websocket) {
  //     this.websocket.close()
  //   }
  // },
};
</script>

<style scoped lang="less">
.formclass {
  /deep/.ant-modal-body {
    padding: 0 !important;
  }
  /deep/.ant-modal-footer {
    padding: 10px 100px;
  }
}
/deep/.ant-pagination.mini .ant-pagination-total-text,
.ant-pagination.mini .ant-pagination-simple-pager {
  height: 24px;
  line-height: 24px;
  font-size: 13px;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-table-column-sorter {
  display: none !important;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 0 0 0 16px;
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
  font-size: 12px;
  margin-left: -17px;
  margin-top: 27px;
}
/deep/.ant-collapse > .ant-collapse-item {
  border: none;
  border-top: 2px solid rgb(233, 233, 240);
}
/deep/.ant-collapse {
  background-color: white;
  border-width: 0 2px 4px 0;
  border-style: solid;
  border-color: #e9e9f0;
  border-radius: inherit;
  height: 100%;
  // width:50%;
  // border-right: 2px solid rgb(233, 233, 240);
  // border:none
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding: 16px 0px 18px 0px;
  padding-left: 15px;
  padding-top: 0px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  // margin-left: -15px;
  margin-top: -19px;
}
.tabRightClikBox1 {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    background-color: white !important;
    color: #000000;
  }
}
// /deep/.ant-table-row-cell-ellipsis{
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis!important;
// }
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-input {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-table .peopleTag {
  position: absolute;
  font-size: 12px;
  font-weight: 600;
  left: 24px;
  padding: 0 2px;
}
.projectBackend {
  background: #ffffff;

  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }

  /deep/.leftContent {
    user-select: none;
    .ant-table-body {
      .ant-table-fixed {
        width: 1200 !important;
      }
    }

    .ant-table-placeholder {
      .ant-empty-normal {
        margin: 0 !important;
      }
    }

    .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;

      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }

      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }

    width: 55%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }

  .rightContent {
    width: 45%;
    display: flex;
    flex-wrap: nowrap;
    align-content: flex-start;
    // /deep/.ant-table-row-cell-ellipsis.ant-table-row-cell-break-word{
    //   user-select:all;
    // }
    .Table {
      width: 50%;
      height: 100%;
    }
    .centerTable {
      width: 100%;
      border: 2px solid rgb(233, 233, 240);
      border-top: 0;
      border-bottom: 0;
      border-right: 0;
      // border-bottom: 4px solid rgb(233, 233, 240);
    }

    .rightTable {
      width: 50%;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);

      .minTable {
        .ant-table-body {
          min-height: 360px;
          border-bottom: 4px solid #e9e9f0;
        }
      }
      .jobNote {
        .note {
          height: 409px;
          overflow-y: auto;
        }
      }
      .peopleTag {
        margin: 0;
        padding: 0;
        width: 24px;
        border-radius: 12px;
        background: #2d221d;
        border-color: #2d221d;
        color: #ff9900;
        text-align: center;
        margin-left: 26px;
      }
    }
  }

  .footerAction {
    width: 100%;
    height: 41px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }

  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
      height: 36px;
    }

    .ant-tabs-nav-container {
      height: 36px;

      .ant-tabs-tab {
        margin: 0;
        height: 36px;
        line-height: 36px;
      }
    }
  }

  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }

      .eqBackground {
        background: #ffff00;
      }

      .statuBackground {
        background: #b0e0e6;
      }

      .backUserBackground {
        background: #a7a2c9;
      }
    }

    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }

    /deep/.userStyle {
      user-select: none !important;
    }
  }
  /deep/.heightSTY {
    line-height: 1.5;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }

  // /deep/ .ant-table-tbody>tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td:first-child {
  //   border-left: 2px solid #ff9900 !important;
  // }

  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
      // border-color: #f0f0f0;
    }

    .ant-table-tbody > tr > td {
      // white-space: nowrap;  /* 防止字符串换行 */
      // overflow: hidden;    /* 隐藏超出容器宽度的部分 */
      // text-overflow: ellipsis;  /* 使用省略号表示被隐藏的文本 */
      padding: 7px 2px;
      border-right: 1px solid #efefef;
      // border-color: #f0f0f0;
    }

    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }

    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }

    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;

      .ant-card-head-title {
        padding: 0;
        color: #000000;
        font-weight: 500;
      }
    }

    .ant-card-body {
      padding: 0;
    }
  }

  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }

  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 11px 0 0 10px;
  }
}

/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
<style lang="less">
.note {
  .textNote {
    // padding-top:4px;
    // border: 1px solid #D6D6D6;
    overflow-x: hidden;
    word-wrap: break-word;
    max-height: 354px;
    overflow-y: auto;
    background: #d6d6d6;
    .imgTable {
      img {
        //width: 100px;
        height: 50px;
      }
    }
    .divClass {
      margin: 0 5px;
      padding-top: 4px;
    }

    p {
      line-height: 100%;
      font-weight: 700;
      margin: 0;

      img {
        height: 80px;
      }
    }

    .displayFlag {
      display: none;
    }
  }
}
</style>
