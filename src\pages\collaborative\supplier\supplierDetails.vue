<!--供应链  - 供应商详情 -->
<template>
  <a-card class="boox">
    <div class="button">
      <a-button @click="goback" style="margin-right: 5px; font-weight: 500" type="primary"
        ><a-icon type="rollback" />{{ $t("return_button") }}</a-button
      >
      <a-button
        @click="editBasicInfo"
        v-show="type == '1' && (tabID == '0' || tabID == '19')"
        style="font-weight: 500; margin-right: 5px"
        type="primary"
        v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementEdit')"
        >{{ $t("edit_button") }}</a-button
      >
      <a-button @click="cancel" v-show="type !== '1' && (tabID == '0' || tabID == '19')" style="font-weight: 500" type="primary">{{
        $t("cancel_editing_button")
      }}</a-button>
      <a-button type="primary" @click="$refs.addLink.openModal({})" v-if="tmp == '1'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addCust.openModal({})" v-if="tmp == '2'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addEquip.openModal({})" v-if="tmp == '3'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addQualify.openModal({})" v-if="tmp == '4'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addFactory.openModal({})" v-if="tmp == '5'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addEvaluation.openModal({})" v-if="tmp == '6'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addlog.openModal({})" v-if="tmp == '8'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addDeliveryJQ.openModal({})" v-if="tmp == '13'">{{ $t("add_button") }}</a-button>
      <a-button type="primary" @click="$refs.addDeliveryZL.openModal({})" v-if="tmp == '14'">{{ $t("add_button") }}</a-button>
    </div>
    <a-tabs class="tab" :default-active-key="activeKey" @change="callback">
      <a-tab-pane :key="tmp.id" :tab="tmp.name" v-for="tmp in headList">
        <!-- 基本信息 -->
        <basic-info
          v-if="tmp.id == '0'"
          ref="basicSave"
          :basicData="basicData"
          :type="type"
          @ok="perserve"
          :checkbox1="checkbox1"
          :checkbox="checkbox"
          :cityData="cityData"
          :hitArray="hitArray"
          :checkCity.sync="checkCity"
        >
        </basic-info>
        <!-- 联系人 -->
        <div class="t2" v-else-if="tmp.id == '1'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addLink.openModal({})">新增</a-button> -->
          <standard-table rowKey="id" :columns="linkColumns" :pagination="false" :dataSource="linkData" bordered>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addLink.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleLink(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 主要客户 -->
        <div class="t2" v-else-if="tmp.id == '2'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addCust.openModal({})">新增</a-button> -->
          <standard-table rowKey="id" :columns="custColumns" :dataSource="custData" :pagination="false" bordered>
            <div slot="action" slot-scope="{ record }" style="font-weight: 500">
              <template>
                <a href="javascript:;" @click="$refs.addCust.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDel(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 生产设备 -->
        <div class="t2" v-else-if="tmp.id == '3'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addEquip.openModal({})">新增</a-button> -->
          <standard-table rowKey="id" :columns="facilityColumns" :pagination="false" :dataSource="facilityData" bordered>
            <span slot="picture4Guid_" slot-scope="{ record }">
              <template v-if="record.isphoto">
                <a href="javascript:;" @click="$refs.checkPhoto.openModal(record.id)">查看</a>
              </template>
            </span>

            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addEquip.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelEquip(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 体系认证 -->
        <div class="t3" v-else-if="tmp.id == '4'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addQualify.openModal({})">新增</a-button> -->
          <standard-table rowKey="id" :columns="certColumns" :pagination="false" :dataSource="certData" bordered>
            <span slot="qualification_" slot-scope="{ text }">
              <template>
                {{ text | filterColumn(certList) }}
              </template>
            </span>
            <span slot="isQf_" slot-scope="{ text }">
              <template>
                {{ text ? "是" : "否" }}
              </template>
            </span>
            <span slot="picture4Guid_" slot-scope="{ record }">
              <template v-if="record.isphoto">
                <a href="javascript:;" @click="$refs.checkPhoto.openModal(record.id)">查看</a>
              </template>
            </span>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addQualify.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelQualify(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 工厂图库 -->
        <div class="t2" v-else-if="tmp.id == '5'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addFactory.openModal({})">新增</a-button> -->
          <standard-table rowKey="id" :columns="factoryColumns" :pagination="false" :dataSource="factoryData" bordered>
            <span slot="companyArea_" slot-scope="{ text }">
              <template>
                {{ text | filterColumn(areaList) }}
              </template>
            </span>
            <span slot="picture4Guid_" slot-scope="{ record }">
              <template v-if="record.isphoto">
                <a href="javascript:;" @click="$refs.checkPhoto.openModal(record.id)">查看</a>
              </template>
            </span>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addFactory.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelFactory(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 评估结果 -->
        <div class="t2" v-else-if="tmp.id == '6'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addEvaluation.openModal({})">新增</a-button> -->
          <standard-table rowKey="id" :columns="assessmentColumns" :pagination="false" :dataSource="assessmentData" bordered>
            <span slot="matchLevel_" slot-scope="{ text }">
              <template>
                {{ text | filterColumn(level) }}
              </template>
            </span>
            <span slot="autoLevel_" slot-scope="{ text }">
              <template>
                {{ text | filterColumn(auto) }}
              </template>
            </span>
            <span slot="siteLevel_" slot-scope="{ text }">
              <template>
                {{ text | filterColumn(site) }}
              </template>
            </span>
            <span slot="expansion_" slot-scope="{ text }">
              <template>
                {{ text | filterColumn(expansion) }}
              </template>
            </span>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addEvaluation.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelEvaluation(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 板材物料 -->
        <div v-else-if="tmp.id == '7'">
          <a-button type="primary" class="margin10" @click="$refs.addBoard.openModal({})">{{ $t("add_button") }}</a-button>
          <standard-table rowKey="id" :columns="slabColumns" :dataSource="slabData" bordered>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addBoard.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelBoard(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 拜访日志 -->
        <div class="t5" v-else-if="tmp.id == '8'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addlog.openModal({})" >新增</a-button> -->
          <standard-table rowKey="id" :columns="logColumns" :pagination="false" :dataSource="logData" bordered>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addlog.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDellog(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
                <a-divider type="vertical" />
                <a href="javascript:;" @click="btnList(record, '1')" v-if="checkPermission('MES.MarketModule.SupplierManagement.VisitTJ')">提交</a>
                <a-divider type="vertical" />
                <a href="javascript:;" @click="btnList(record, '2')" v-if="checkPermission('MES.MarketModule.SupplierManagement.Visit')">拜访</a>
                <a-divider type="vertical" />
                <a href="javascript:;" @click="btnList(record, '3')" v-if="checkPermission('MES.MarketModule.SupplierManagement.VisitCheck')">审批</a>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 制程能力 -->
        <div v-else-if="tmp.id == '12'" class="capacity">
          <a-form-model :model="QuoteForm" :label-col="{ span: 2 }" :wrapper-col="{ span: 20 }">
            <a-form-model-item :label="item.name" v-for="(item, itemIndex) in QuoteDataList" :key="itemIndex">
              <a-select
                ref="select"
                v-model="QuoteForm[item.value]"
                v-if="item.type == '0'"
                showSearch
                allowClear
                mode="multiple"
                optionFilterProp="lable"
                @change="submitFormBtn = false"
              >
                <a-select-option v-for="(ite, index) in item.options" :key="index" :value="ite.value" :lable="ite.label">{{
                  ite.label
                }}</a-select-option>
              </a-select>
              <a-checkbox-group
                v-else-if="item.type == '1'"
                v-model="QuoteForm[item.value]"
                :options="item.options"
                @change="submitFormBtn = false"
              />
              <a-input v-else-if="item.type == '2'" v-model="QuoteForm[item.value]" @change="submitFormBtn = false"></a-input>
              <a-radio-group v-else-if="item.type == '3'" v-model="QuoteForm[item.value]" :options="item.options" @change="submitFormBtn = false" />
            </a-form-model-item>
          </a-form-model>
          <div class="fixedBtn">
            <a-button type="primary" @click="handleSubmit" :disabled="submitFormBtn"> 确认 </a-button>
          </div>
        </div>
        <!-- 操作日志 -->
        <div v-else-if="tmp.id == '9'" class="t6">
          <standard-table rowKey="id" :columns="logCaozuo" :dataSource="logList" :pagination="false" :scroll="{ y: 500 }" bordered> </standard-table>
        </div>
        <!-- 发单设置 -->
        <div v-else-if="tmp.id == '10'">
          <a-button @click="newAdd" v-if="checkPermission('MES.MarketModule.SupplierManagement.LssuingSettingAdd')">{{ $t("add_button") }}</a-button>
          <div style="display: flex; justify-content: space-around">
            <div class="Ontable">
              <table class="table1">
                <tr>
                  <th style="width: 4%">类别</th>
                  <th style="width: 12%">工艺</th>
                  <th style="width: 10%">订单面积区间</th>
                  <th style="width: 5%">目标面积</th>
                  <th style="width: 7%">目标工单个数</th>
                  <th style="width: 5%">实发面积</th>
                  <th style="width: 8%">实发工单个数</th>
                  <th style="width: 8%">Pnl总数</th>
                  <th style="width: 12%">合拼工单拼版号最小面积</th>
                  <th style="width: 11%">合拼工单子料号最小面积</th>
                  <th style="width: 8%">设置</th>
                </tr>
              </table>
              <table border="1" class="contentTable">
                <tr v-for="(color, index) in listSum" :key="index">
                  <td style="width: 5%">{{ color.boradType }}</td>
                  <td style="width: 15%">{{ color.technology }}</td>
                  <td v-if="color.supplierLssuingInfos != undefined">
                    <table class="tableTWO" v-for="(item, index) in color.supplierLssuingInfos" :key="index">
                      <td style="width: 8%">
                        {{ item.sectionArea }}
                      </td>
                      <td style="width: 5%">
                        {{ item.targetArea }}
                      </td>
                      <td style="width: 7%">
                        {{ item.targetPinBanOrderNum }}
                      </td>
                      <td style="width: 5%">
                        {{ item.sendArea }}
                      </td>
                      <td style="width: 8%">
                        {{ item.sendOrderNum }}
                      </td>
                      <td style="width: 8%">
                        {{ item.sendPnlNum }}
                      </td>
                      <td style="width: 12%">
                        {{ item.pinBanOrderMinArea }}
                      </td>
                      <td style="width: 12%">
                        {{ item.orderMinArea }}
                      </td>
                    </table>
                  </td>
                  <td style="width: 8%">
                    <a
                      href="javascript:;"
                      @click="setBill(color)"
                      v-if="checkPermission('MES.MarketModule.SupplierManagement.LssuingSettingChange')"
                      >{{ $t("edit_button") }}</a
                    >
                    <a-divider type="vertical" />
                    <a-popconfirm :title="deleteS" @confirm="delBill(color)">
                      <a href="javascript:;" v-if="checkPermission('MES.MarketModule.SupplierManagement.LssuingSettingDel')">删除</a>
                    </a-popconfirm>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <!-- 交期评级 -->
        <div class="t4" v-else-if="tmp.id == '13'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addDeliveryJQ.openModal({})">新增</a-button> -->
          <standard-table
            rowKey="guid"
            :columns="deliveryColumns2"
            :pagination="false"
            :dataSource="deliveryData"
            bordered
            :class="deliveryData.length ? 'minClass' : ''"
          >
            <div slot="gradeDate" slot-scope="{ record }">
              {{ record && record.gradeDate.split("/").splice(0, 2).join("/") }}
            </div>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addDeliveryJQ.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelDelivery(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
          <a-table
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :columns="columns1"
            :dataSource="statisticalData"
            :pagination="false"
            bordered
          >
          </a-table>
          <div class="th_right">
            <bar-chart :barData="barData2 | th_barData" el="'th_echart1'" :echartHeight="294" />
          </div>
        </div>
        <!-- 质量评级 -->
        <div class="t4" v-else-if="tmp.id == '14'">
          <!-- <a-button type='primary' class='margin10' @click="$refs.addDeliveryZL.openModal({})">新增</a-button> -->
          <standard-table
            rowKey="guid"
            :columns="deliveryColumns1"
            :pagination="false"
            :dataSource="deliveryData"
            bordered
            :class="deliveryData.length ? 'minClass' : ''"
          >
            <div slot="gradeDate" slot-scope="{ record }">
              {{ record && record.gradeDate.split("/").splice(0, 2).join("/") }}
            </div>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addDeliveryZL.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelDelivery(record.guid)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
          <a-table
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :columns="columns1"
            :dataSource="statisticalData"
            :pagination="false"
            bordered
          >
          </a-table>
          <!-- <div style="text-align: center;font-size:20px">工厂品质得分趋势图</div> -->
          <div class="th_right">
            <bar-chart :barData="barData1 | th_barData" el="'th_echart'" :echartHeight="294" />
          </div>
        </div>
        <!-- 财务评级 -->
        <div v-else-if="tmp.id == '15'">
          <a-button type="primary" class="margin10" @click="$refs.addDelivery.openModal({})">{{ $t("add_button") }}</a-button>
          <standard-table rowKey="id" :columns="deliveryColumns" :dataSource="deliveryData" bordered :class="deliveryData.length ? 'minClass' : ''">
            <div slot="gradeDate" slot-scope="{ record }">
              {{ record && record.gradeDate.split("/").splice(0, 2).join("/") }}
            </div>
            <div slot="action" slot-scope="{ record }">
              <template>
                <a href="javascript:;" @click="$refs.addDelivery.openModal(record)">{{ $t("edit_button") }}</a>
                <a-divider type="vertical" />
                <a-popconfirm :title="deleteS" @confirm="handleDelDelivery(record.id)">
                  <a href="javascript:;">{{ $t("delete_button") }}</a>
                </a-popconfirm>
              </template>
            </div>
          </standard-table>
        </div>
        <!-- 组织架构 -->
        <!-- <div v-else-if="tmp.id == '16'">
                <organizational-structure :departGroupPostDto="departGroupPostDto" @getSupData="getSupData" :factoryId="factoryId"></organizational-structure>                    
              </div> -->
        <!-- 配置 -->
        <div class="i18nwidth" v-else-if="tmp.id == '17'">
          <a-form-model :model="ConfigurationForm" :label-col="{ span: 2 }" :wrapper-col="{ span: 14 }">
            <a-row>
              <a-col :span="4">
                <div style="border: 2px solid #f0f0f0; margin: 10px" v-if="ConfigurationForm.orderConfigDto">
                  <span
                    style="
                      color: #000000;
                      display: inline-block;
                      width: 100%;
                      border-bottom: 1px solid #f0f0f0;
                      text-align: center;
                      background-color: #dfdede;
                    "
                    >{{ $t("Order_Control") }}</span
                  >
                  <a-form-model-item :label="$t('Max_Area_Per_Day')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-input v-model="ConfigurationForm.orderConfigDto.area" @blur="araeblur" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Max_Items_Per_Day')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-input v-model="ConfigurationForm.orderConfigDto.number" @blur="numberblur" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Max_Urgent_Per_Day')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-input v-model="ConfigurationForm.orderConfigDto.urgentNum" @blur="urgentNumblur" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Acceptable_Supply_Chain_Orders')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isDownOrder" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Releasable_Supply_Chain_Orders')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isGYLOffLine" @change="submitFormBtn = false" />
                  </a-form-model-item>
                </div>
              </a-col>
              <a-col :span="4">
                <div style="border: 2px solid #f0f0f0; margin: 10px">
                  <span
                    style="
                      color: #000000;
                      display: inline-block;
                      width: 100%;
                      border-bottom: 1px solid #f0f0f0;
                      text-align: center;
                      background-color: #dfdede;
                    "
                    >{{ $t("Market_Order_Management") }}</span
                  >
                  <a-form-model-item :label="$t('Releasable_Supply_Chain_Orders')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-select v-model="ConfigurationForm.isPreFenPai" style="width: 100%" @change="submitFormBtn = false">
                      <a-select-option value="0" :title="$t('Pending_Preliminary_Review')">{{ $t("Pending_Preliminary_Review") }}</a-select-option>
                      <a-select-option value="1" :title="$t('Pending_Preliminary_Assignment')">{{
                        $t("Pending_Preliminary_Assignment")
                      }}</a-select-option>
                      <a-select-option value="2" :title="$t('Pending_Quotation')">{{ $t("Pending_Quotation") }}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Preliminary_Completion')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isPreAfterEnd" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Quotation_Completion')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isPriceAfterEnd" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Order_Management')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isOrderManage" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Order_Interface_Engineering')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isPro" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Order_Interface_Production')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isToProduce" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Line_Parameter_Unit')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-select v-model="ConfigurationForm.isMetricBritishSystem" style="width: 100%" @change="submitFormBtn = false">
                      <a-select-option :value="item.value" v-for="(item, index) in metricBritishSystemList" :key="index" :title="item.text">{{
                        item.text
                      }}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Preliminary_Automatic_Assignment')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="isPcbSendOrder" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Surface_Treatment_Thickness_Unit')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-select v-model="ConfigurationForm.surfaceFinishThickUnit" style="width: 100%" @change="submitFormBtn = false">
                      <a-select-option value='U"'>U"</a-select-option>
                      <a-select-option value="um">um</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Quotation_Approval_Flow')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isOfferRatify" @change="submitFormBtn = false" />
                  </a-form-model-item>
                </div>
              </a-col>
              <a-col :span="4">
                <div style="border: 2px solid #f0f0f0; margin: 10px">
                  <span
                    style="
                      color: #000000;
                      display: inline-block;
                      width: 100%;
                      border-bottom: 1px solid #f0f0f0;
                      text-align: center;
                      background-color: #dfdede;
                    "
                    >{{ $t("Engineering_Order_Management") }}</span
                  >
                  <a-form-model-item :label="$t('Requires_Engineering_Assignment')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isPpeAssign" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Engineering_Panelling')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isPanel" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Customer_Order_Management')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isEQCheck" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Secondary_Review')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isQAE2" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Production_After_Panelling')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isHp" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Market_Modification_Confirmation')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-checkbox v-model="ConfigurationForm.isPpeSure" @change="submitFormBtn = false" />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Engineering_Automatic_Assignment')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-select
                      ref="select1"
                      v-model="ConfigurationForm.isAutoSend"
                      showSearch
                      allowClear
                      optionFilterProp="lable"
                      @change="submitFormBtn = false"
                      mode="multiple"
                    >
                      <a-select-option v-for="(item, index) in englist" :key="index" :value="item.value" :lable="item.text">{{
                        item.text
                      }}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Engineering_Modification_Mode')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-select v-model="ConfigurationForm.isParSyncMkt" style="width: 100%" @change="submitFormBtn = false">
                      <a-select-option v-for="(ite, index) in changtype" :value="ite.value" :key="index" :title="ite.text" :lable="ite.text">{{
                        ite.text
                      }}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Gold_Finger_Nickel_Thickness_Unit')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-select v-model="ConfigurationForm.goldfingerNieThicknessUnit" style="width: 100%" @change="submitFormBtn = false">
                      <a-select-option v-for="(ite, index) in GoldfingerList" :value="ite.value" :key="index" :lable="ite.text">{{
                        ite.text
                      }}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </div>
              </a-col>
              <a-col :span="4">
                <div style="border: 2px solid #f0f0f0; margin: 10px" v-if="ConfigurationForm.proToolConfigDto">
                  <span
                    style="
                      color: #000000;
                      display: inline-block;
                      width: 100%;
                      border-bottom: 1px solid #f0f0f0;
                      text-align: center;
                      background-color: #dfdede;
                    "
                    >{{ $t("Production_Tool_Configuration") }}</span
                  >
                  <a-form-model-item :label="$t('Requires_Flying_Probe_Tool')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isET" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Routing_Tool')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isRout" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Low_Resistance_Test')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isLowImpTest" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Laser_Screening')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isJGSW" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Character_Printing')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isZFPrint" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Drill_Bit_Assignment')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isDrillPeiDao" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Requires_Photoplot_Tool')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isGuangHui" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('New_Mold')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isNewmold" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('New_Test_Fixture')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isNewstand" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Customer_Provided_Test_Fixture')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                    <a-checkbox v-model="ConfigurationForm.proToolConfigDto.isCuststand" @change="submitFormBtn = false" /><br />
                  </a-form-model-item>
                  <a-form-model-item :label="$t('Production_Feedback_Mode_Selection')" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
                    <a-select
                      ref="select1"
                      v-model="ConfigurationForm.feedbackMode"
                      showSearch
                      allowClear
                      optionFilterProp="lable"
                      @change="submitFormBtn = false"
                    >
                      <a-select-option v-for="(item, index) in modelist" :key="index" :value="item.value" :title="item.text" :lable="item.text">{{
                        item.text
                      }}</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </div>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="4">
                <a-form-model-item :label="$t('User_Management_Authorization')" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                  <a-checkbox v-model="ConfigurationForm.isSQ" @change="submitFormBtn = false" />
                </a-form-model-item>
              </a-col>
              <a-col :span="16">
                <a-form-model-item :label="$t('Parsing_Configuration_Info')" :label-col="{ span: 2 }" :wrapper-col="{ span: 20 }">
                  <a-input
                    v-model="ConfigurationForm.remainCurrency"
                    :placeholder="$t('Remaining_File_Count')"
                    style="width: 200px"
                    @change="submitFormBtn = false"
                    allowClear
                  >
                  </a-input>
                  <a-date-picker
                    style="margin-right: 0.5%; margin-left: 0.5%; width: 214px"
                    valueFormat="YYYY-MM-DD"
                    :placeholder="$t('Expiry_Time')"
                    v-model="ConfigurationForm.expiredTime"
                    @change="onChange1"
                  />
                  <a-input
                    v-model="ConfigurationForm.callbackUrl"
                    :placeholder="$t('Parsing_Callback_URL')"
                    style="width: 428px"
                    @change="submitFormBtn = false"
                    allowClear
                  >
                  </a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-form-model-item :wrapper-col="{ span: 5, offset: 1 }">
              <a-button type="primary" @click="SubmitOk" :disabled="submitFormBtn"> {{ $t("confirm_button") }} </a-button>
            </a-form-model-item>
          </a-form-model>
        </div>
        <!-- 日历设置 -->
        <div v-else-if="tmp.id == '18'">
          <calendar-data :factoryId="factoryId"></calendar-data>
        </div>
        <!--系数配置 -->
        <div class="t3" v-else-if="tmp.id == '19'">
          <a-table
            :columns="columns4"
            rowKey="itemsConfigId"
            bordered
            :pagination="false"
            :data-source="ruleItemsData"
            :loading="orderListTableLoading2"
            :class="ruleItemsData.length ? 'min-table' : ''"
            :scroll="{ y: 722 }"
          >
            <template slot="value" slot-scope="text, record">
              <a-checkbox
                @change="Valuechange"
                :checked="record.value ? true : false"
                :disabled="type == '1' && (tabID == '0' || tabID == '19')"
                v-if="record.dataType == 'bit' && type == '1' && (tabID == '0' || tabID == '19')"
              ></a-checkbox>
              <a-checkbox
                @change="Valuechange"
                v-model="record.value"
                v-if="record.dataType == 'bit' && type !== '1' && (tabID == '0' || tabID == '19')"
              ></a-checkbox>
              <div>
                <span v-if="record.dataType != 'bit' && type == '1' && (tabID == '0' || tabID == '19')" @blur="Valueblur()" style="width: 100%">
                  {{ record.value }}
                </span>
                <a-input v-else @blur="Valueblur()" v-model="record.value" style="width: 100%" />
              </div>
            </template>
            <template slot="isOrderPre" slot-scope="text, record">
              <a-checkbox v-model="record.isOrderPre" :disabled="type == '1' && (tabID == '0' || tabID == '19')" />
            </template>
            <template slot="isPpeMake" slot-scope="text, record">
              <a-checkbox v-model="record.isPpeMake" :disabled="type == '1' && (tabID == '0' || tabID == '19')" />
            </template>
            <template slot="isLoseEfficacy" slot-scope="text, record">
              <a-checkbox v-model="record.isLoseEfficacy" :disabled="type == '1' && (tabID == '0' || tabID == '19')" />
            </template>
          </a-table>
          <div style="margin-top: 15px; text-align: center">
            <a-button type="primary" style="margin-right: 5px" v-if="type == '2'" @click="saveRuleItems">{{ $t("save_button") }}</a-button>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
    <add-cust ref="addCust" :compileApply="compileApply" :message="message" :suppId="suppId" @ok="getCustomer"></add-cust>
    <add-equip ref="addEquip" :compileApply="compileApply" :message="message" :suppId="suppId" @ok="getEquip"></add-equip>
    <add-qualify ref="addQualify" :compileApply="compileApply" :message="message" :suppId="suppId" @ok="getQualify"></add-qualify>
    <add-factory ref="addFactory" :compileApply="compileApply" :message="message" :suppId="suppId" @ok="getFactory"></add-factory>
    <add-evaluation ref="addEvaluation" :compileApply="compileApply" :message="message" :suppId="suppId" @ok="getAssest"></add-evaluation>
    <add-board ref="addBoard" :suppId="suppId" :compileApply="compileApply" :message="message" @ok="getBoard"></add-board>
    <add-record ref="addlog" :suppId="suppId" :compileApply="compileApply" :message="message" @ok="getlog"></add-record>
    <add-link ref="addLink" :suppId="suppId" :compileApply="compileApply" :message="message" @ok="getlink"></add-link>
    <add-delivery
      ref="addDelivery"
      :suppId="suppId"
      :dataType="someType"
      :compileApply="compileApply"
      :message="message"
      @ok="getDeliveryData"
    ></add-delivery>
    <add-delivery-z-l
      ref="addDeliveryZL"
      :suppId="suppId"
      :dataType="someType"
      :compileApply="compileApply"
      :message="message"
      @ok="getDeliveryData"
    ></add-delivery-z-l>
    <add-delivery-j-q
      ref="addDeliveryJQ"
      :suppId="suppId"
      :dataType="someType"
      :compileApply="compileApply"
      :message="message"
      @ok="getDeliveryData"
    ></add-delivery-j-q>
    <check-photo ref="checkPhoto"></check-photo>
    <a-modal title="提示" :visible="visible" @ok="handleOk" @cancel="handleCancel" centered>
      <p>{{ ModalText }}</p>
    </a-modal>
    <a-modal title="发单设置" :visible="visible1" @ok="handleOk1" @cancel="handleCancel1" centered width="1200px">
      <div>
        <span>所属品类：</span>
        <a-radio-group v-model="value1" :options="optionsWithDisabled" @change="RadioChange" :disabled="newStat" />
        <br />
        <span>制作工艺：</span>
        <a-radio-group v-model="value2" :options="options" @change="RadioChange1" :disabled="newStat" />
        <br />
        <span>制作工艺：</span>
        <a-radio-group v-model="value3" :options="optionsWith" @change="RadioChange2" :disabled="newStat" />
        <br />
        <span>制作工艺：</span>
        <a-radio-group v-model="value4" :options="optionsList" @change="RadioChange3" :disabled="newStat" />
        <br />
        <span>成型参数：</span>
        <a-checkbox-group @change="onChange" v-model="checkbox_">
          <a-checkbox value="cnc" :disabled="newStat"> CNC </a-checkbox>
          <a-checkbox value="moldForming" :disabled="newStat"> 模具成型 </a-checkbox>
        </a-checkbox-group>
      </div>
      <a-table rowKey="id" :columns="billNum" :dataSource="billData" :pagination="false" bordered :scroll="{ y: 500 }">
        <span slot="minArea" slot-scope="text, record">
          <a-input :value="text" @change="e => handleChange(e.target.value, record.key, 'minArea')">{{ text }}</a-input>
        </span>
        <span slot="maxArea" slot-scope="text, record">
          <a-input :value="text" @change="e => handleChange(e.target.value, record.key, 'maxArea')">{{ record.maxArea }}</a-input>
        </span>
        <span slot="targetArea" slot-scope="text, record">
          <a-input :value="text" @change="e => handleChange(e.target.value, record.key, 'targetArea')">{{ record.targetArea }}</a-input>
        </span>
        <span slot="targetPinBanOrderNum" slot-scope="text, record">
          <a-input :value="text" @change="e => handleChange(e.target.value, record.key, 'targetPinBanOrderNum')">{{
            record.targetPinBanOrderNum
          }}</a-input>
        </span>
        <span slot="pinBanOrderMinArea" slot-scope="text, record">
          <a-input :value="text" @change="e => handleChange(e.target.value, record.key, 'pinBanOrderMinArea')">{{
            record.pinBanOrderMinArea
          }}</a-input>
        </span>
        <span slot="orderMinArea" slot-scope="text, record">
          <a-input :value="text" @change="e => handleChange(e.target.value, record.key, 'orderMinArea')">{{ record.orderMinArea }}</a-input>
        </span>
        <span slot="action" slot-scope="text, record">
          <a-button @click="addList(record)">添加</a-button>
          <a-button @click="deleList(record.key)">删除</a-button>
        </span>
      </a-table>
    </a-modal>
  </a-card>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import BarChart from "@/pages/collaborative/grade/component/BarChart";
import axios from "axios";
import {
  getSup,
  deleteCust,
  getCust,
  deleteEquip,
  getEquip,
  deleteQualify,
  getQualify,
  getFactory,
  deleteFactory,
  deleteAssest,
  getAssest,
  getBoard,
  deleteBoard,
  getlog,
  deletelog,
  getLink,
  deleteLink,
  check,
  submitLog,
  roundLog,
  authorityChange,
  caozuoLog,
  progressNum,
  billList,
  billAdd,
  setBill,
  deleBill,
  billAmand,
  getcapacityConfig,
  getProcessCapability,
  editProcessCapability,
  getDeliveryData,
  deleteDeliveryData,
  qualityRatingList,
  qualityRatingList2,
  getsupParinfo,
  setSupplierConfig,
  ruleItemsList,
  setRuleItemsList,
} from "@/services/supplier/index";
// import {ruleItemsList,} from "@/services/CustRule";
import calendarData from "@/pages/dashboard/dataPage/modules/calendarData";
import basicInfo from "./components/basicInfo";
import addCust from "./components/addCust";
import addEquip from "./components/addEquip";
import addQualify from "./components/addQualify";
import StandardTable from "@/components/table/StandardTable";
import addFactory from "./components/addFactory.vue";
import addEvaluation from "./components/addEvaluation.vue";
import addBoard from "./components/addBoard.vue";
import addRecord from "./components/addRecord.vue";
import addLink from "./components/addLink.vue";
import checkPhoto from "./components/checkPhoto.vue";
import addDelivery from "./components/addDelivery";
import addDeliveryZL from "./components/addDeliveryZL";
// import organizationalStructure from './components/organizationalStructure' ConfigurationPage
import moment from "moment";
import { checkPermission } from "@/utils/abp";
let linkColumns = [
  {
    title: "",
    dataIndex: "contact",
    key: "Contact_Person",
  },
  {
    title: "",
    dataIndex: "post",
    key: "Position",
  },
  {
    title: "",
    dataIndex: "contactType",
    key: "Contact_Type",
  },
  {
    title: "",
    dataIndex: "phone",
    key: "Phone",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let custColumns = [
  {
    title: "",
    align: "center",
    dataIndex: "corporateName_",
    key: "Company_Name",
  },
  {
    title: "",
    dataIndex: "customerIndustry_",
    key: "Application_Field",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let facilityColumns = [
  {
    title: "",
    dataIndex: "equipmentName_",
    key: "Device_Type",
  },
  {
    title: "",
    dataIndex: "specification_",
    key: "Specification",
  },
  {
    title: "",
    dataIndex: "number_",
    key: "Quantity",
  },
  {
    title: "",
    dataIndex: "manufacturer_",
    key: "Manufacturer",
  },
  {
    title: "",
    dataIndex: "outputRate_",
    key: "Capacity",
  },
  {
    title: "",
    dataIndex: "picture4Guid_",
    scopedSlots: { customRender: "picture4Guid_" },
    key: "Image",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let certColumns = [
  {
    title: "",
    dataIndex: "qualification_",
    scopedSlots: { customRender: "qualification_" },
    key: "Project",
  },
  {
    title: "",
    dataIndex: "isQf_",
    scopedSlots: { customRender: "isQf_" },
    key: "Is",
  },
  {
    title: "",
    dataIndex: "approveTime_",
    key: "Certification_Time",
  },
  {
    title: "",
    dataIndex: "planApproveTime_",
    key: "Planned_Certification_Time",
  },
  {
    title: "",
    dataIndex: "picture4Guid_",
    scopedSlots: { customRender: "picture4Guid_" },
    key: "Image",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let factoryColumns = [
  {
    title: "",
    dataIndex: "companyArea_",
    scopedSlots: { customRender: "companyArea_" },
    key: "Area",
  },
  {
    title: "",
    dataIndex: "picture4Guid_",
    scopedSlots: { customRender: "picture4Guid_" },
    key: "Image",
  },
  {
    title: "",
    scopedSlots: { customRender: "action" },
    key: "Operation",
  },
];
let assessmentColumns = [
  {
    title: "",
    dataIndex: "matchLevel_",
    scopedSlots: { customRender: "matchLevel_" },
    key: "Matching_Level",
  },
  {
    title: "",
    dataIndex: "matchArea_",
    key: "Matching_Area",
  },
  {
    title: "",
    dataIndex: "autoLevel_",
    scopedSlots: { customRender: "autoLevel_" },
    key: "Automation_Degree",
  },
  {
    title: "",
    dataIndex: "siteLevel_",
    scopedSlots: { customRender: "siteLevel_" },
    key: "Site_Management",
  },
  {
    title: "",
    dataIndex: "outputSpace_",
    key: "Capacity_Space",
  },
  {
    title: "",
    dataIndex: "expansion_",
    scopedSlots: { customRender: "expansion_" },
    key: "Expansion_Plan",
  },
  {
    title: "",
    dataIndex: "evaResult",
    key: "Evaluation_Result",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let slabColumns = [
  {
    title: "产品类别",
    dataIndex: "productType_",
  },
  {
    title: "板材类型",
    dataIndex: "orderType_",
  },
  {
    title: "品牌",
    dataIndex: "machineType_",
  },
  {
    title: "型号",
    dataIndex: "boardType_",
  },
  {
    title: "板厚",
    dataIndex: "boardthick_",
  },
  {
    title: "铜厚",
    dataIndex: "copperThickness_",
  },
  {
    title: "TG值",
    dataIndex: "tG_",
  },
  {
    title: "操作",
    key: "action",
    scopedSlots: { customRender: "action" },
  },
];
let logColumns = [
  {
    title: "",
    dataIndex: "visitState_",
    key: "Status",
  },
  {
    title: "",
    dataIndex: "visitors_",
    key: "Visitor",
  },
  {
    title: "",
    dataIndex: "visitPosition_",
    key: "Visitor_Position",
  },
  {
    title: "",
    dataIndex: "visitTel_",
    key: "Phone",
  },
  {
    title: "",
    dataIndex: "visitMethod_",
    key: "Method",
  },
  {
    title: "",
    dataIndex: "visitContent_",
    key: "Visit_Content",
  },
  {
    title: "",
    dataIndex: "visitDate_",
    key: "Visit_Completion_Date",
  },
  {
    title: "",
    dataIndex: "planVisitDate_",
    key: "Planned_Visit_Time",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let logCaozuo = [
  {
    title: "",
    dataIndex: "inDate",
    width: "15%",
    align: "left",
    key: "Time",
  },
  {
    title: "",
    dataIndex: "inUserName",
    width: "5%",
    align: "left",
    key: "Operation",
  },
  {
    title: "",
    dataIndex: "content",
    width: "80%",
    align: "left",
    key: "Content",
  },
];
let deliveryColumns = [
  {
    title: "",
    dataIndex: "gradeDate",
    scopedSlots: { customRender: "gradeDate" },
    align: "left",
    key: "Rating_Time",
  },
  {
    title: "",
    dataIndex: "grade",
    key: "Level",
  },
  {
    title: "",
    dataIndex: "note",
    key: "Remarks",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let deliveryColumns1 = [
  {
    title: "",
    width: "20%",
    // dataIndex: "month",
    scopedSlots: { customRender: "gradeDate" },
    align: "left",
    key: "Rating_Time",
  },
  {
    title: "",
    dataIndex: "processControlScore",
    align: "left",
    key: "Process_Control_Score",
  },
  {
    title: "",
    dataIndex: "fqaControlScore",
    align: "left",
    key: "FQA_Quality_Control_Score",
  },
  {
    title: "",
    dataIndex: "customerControlScore",
    align: "left",
    key: "Complaint_Control_Score",
  },
  {
    title: "",
    dataIndex: "grade",
    key: "Level",
  },
  {
    title: "",
    dataIndex: "note",
    align: "left",
    key: "Remarks",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
let deliveryColumns2 = [
  {
    title: "",
    // dataIndex: "month",
    scopedSlots: { customRender: "gradeDate" },
    align: "left",
    key: "Rating_Time",
  },
  {
    title: "",
    dataIndex: "processControlScore",
    align: "left",
    key: "First_Time_Delivery_Score",
  },
  {
    title: "",
    dataIndex: "fqaControlScore",
    align: "left",
    key: "KA_Delivery_Score",
  },
  {
    title: "",
    dataIndex: "customerControlScore",
    align: "left",
    key: "First_Overdue_Score",
  },
  {
    title: "",
    dataIndex: "grade",
    key: "Level",
  },
  {
    title: "",
    dataIndex: "note",
    align: "left",
    key: "Remarks",
  },
  {
    title: "",
    key: "Operation",
    scopedSlots: { customRender: "action" },
  },
];
const columns1 = [
  {
    title: "",
    width: "300px",
    dataIndex: "realname",
    key: "Month",
  },
  {
    title: "",
    dataIndex: "m1",
    key: "January",
  },
  {
    title: "",
    dataIndex: "m2",
    key: "February",
  },
  {
    title: "",
    dataIndex: "m3",
    key: "March",
  },
  {
    title: "",
    dataIndex: "m4",
    key: "April",
  },
  {
    title: "",
    dataIndex: "m5",
    key: "May",
  },
  {
    title: "",
    dataIndex: "m6",
    key: "June",
  },
  {
    title: "",
    dataIndex: "m7",
    key: "July",
  },
  {
    title: "",
    dataIndex: "m8",
    key: "August",
  },
  {
    title: "",
    dataIndex: "m9",
    key: "September",
  },
  {
    title: "",
    dataIndex: "m10",
    key: "October",
  },
  {
    title: "",
    dataIndex: "m11",
    key: "November",
  },
  {
    title: "",
    dataIndex: "m12",
    key: "December",
  },
];
const columns4 = [
  {
    title: "",
    dataIndex: "groups",
    align: "center",
    width: "10%",
    key: "Category",
    ellipsis: true,
    customRender(text, row) {
      return {
        children: text,
        attrs: {
          // 列纵向合并
          rowSpan: row.groupsRowSpan,
        },
      };
    },
  },
  {
    title: "",
    dataIndex: "index",
    align: "center",
    ellipsis: true,
    width: "10%",
    customRender: (text, record, index) => `${index + 1}`,
    key: "Serial_Number",
  },
  {
    title: "",
    dataIndex: "items",
    align: "left",
    ellipsis: true,
    width: 100,
    key: "Requirement",
  },
  {
    title: "",
    dataIndex: "value",
    align: "center",
    width: 60,
    ellipsis: true,
    className: "userStyle",
    scopedSlots: { customRender: "value" },
    key: "Value",
  },
  // {
  //   title: '订单预审',
  //   dataIndex: "isOrderPre",
  //   align: "center",
  //   width: 50,
  //   ellipsis: true,
  //   scopedSlots: { customRender: 'isOrderPre' },
  // },
  // {
  //   title: '工程制作',
  //   dataIndex: 'isPpeMake',
  //   align: "center",
  //   width: 50,
  //   ellipsis: true,
  //   scopedSlots: { customRender: 'isPpeMake' },
  // },
  //  {
  //   title: '失效',
  //   dataIndex: 'isLoseEfficacy',
  //   align: "center",
  //   width: 60,
  //   scopedSlots: { customRender: 'isLoseEfficacy' },
  // },
];
var numbe = 0;
import jsonList from "../../../../public/index";
import AddDeliveryJQ from "@/pages/collaborative/supplier/components/addDeliveryJQ";
export default {
  i18n: require("@/components/language/modules/supplierS/supplierS_i18n.js"),
  name: "supplierDetails",
  inject: ["reload"],
  data() {
    return {
      isPcbSendOrder: false,
      isOfferRatify: false,
      pathEnterUrl: "",
      copylistdata: [],
      factoryId: "",
      barData1: [],
      barData2: [],
      QuoteForm: {},
      submitFormBtn: true,
      QuoteDataList: [],
      baseUrl: process.env.VUE_APP_API_JSON_URL,
      cityData: [],
      checkbox: [],
      checkbox1: [],
      englist: [
        { value: "1", text: "工程制作", key: "Engineering" },
        { value: "2", text: "工程审核", key: "Engineering_Audits" },
      ],
      changtype: [
        { value: 0, text: "市场修改", key: "Market_Modification" },
        { value: 1, text: "工程修改同步", key: "Engineering_Modification_Synchronization" },
        { value: 2, text: "工程修改不同步", key: "Engineering_Modification_Asynchronization" },
      ],
      metricBritishSystemList: [
        { value: 0, text: "英制", key: "Imperial_Unit" },
        { value: 1, text: "公制", key: "Metric_Unit" },
      ],
      modelist: [
        { value: 1, text: "拉伸值(MIL)", key: "Stretch_Value_MIL" },
        { value: 2, text: "拉伸系数", key: "Stretch_Coefficient" },
        { value: 3, text: "实测靶距", key: "Measured_Target_Distance" },
      ],
      certList: [
        { key: "404200", value: "ISO9001" },
        { key: "404201", value: "ESO14001" },
        { key: "404202", value: "UL" },
        { key: "404203", value: "CQC" },
        { key: "404204", value: "TF16949" },
        { key: "404205", value: "OHSAS18001" },
        { key: "404206", value: "JGB9001" },
      ],
      areaList: [
        { key: "404700", value: "压机" },
        { key: "404701", value: "钻孔" },
        { key: "404702", value: "电镀" },
        { key: "404703", value: "线路" },
        { key: "404704", value: "阻焊" },
        { key: "404705", value: "文字" },
        { key: "404706", value: "成型" },
        { key: "404707", value: "测试" },
      ],
      level: [
        { key: "406000", value: "A级" },
        { key: "406001", value: "B级" },
        { key: "406002", value: "C级" },
        { key: "406003", value: "D级" },
        { key: "406004", value: "E级" },
      ],
      auto: [
        { key: "406100", value: "高" },
        { key: "406100", value: "中" },
        { key: "406100", value: "低" },
      ],
      site: [
        { key: "406200", value: "优" },
        { key: "406201", value: "良" },
        { key: "406202", value: "好" },
        { key: "406203", value: "一般" },
        { key: "406204", value: "差" },
      ],
      expansion: [
        { key: "406300", value: "有" },
        { key: "406301", value: "无" },
      ],
      GoldfingerList: [
        { text: 'U"', value: 'U"' },
        { text: "um", value: "um" },
      ],
      logList: [],
      logCaozuo: logCaozuo,
      suppId: "",
      someType: 0,
      custColumns,
      facilityColumns,
      certColumns,
      factoryColumns,
      assessmentColumns,
      slabColumns,
      deliveryColumns,
      deliveryColumns1,
      logColumns,
      linkColumns,
      assessmentData: [],
      linkData: [],
      factoryData: [],
      facilityData: [],
      custData: [],
      certData: [],
      slabData: [],
      deliveryData: [],
      logData: [],
      tmp: "",
      headList: [
        {
          id: "0",
          name: "基本信息",
          key: "basic_info",
        },
        {
          id: "2",
          name: "主要客户",
          key: "main_customer",
        },
        // {
        //   id:'7',
        //   name:"板材物料"
        // },
        {
          id: "3",
          name: "生产设备",
          key: "production_equipment",
        },
        {
          id: "4",
          name: "体系认证",
          key: "certification",
        },
        {
          id: "5",
          name: "工厂图片",
          key: "factory_images",
        },
        {
          id: "6",
          name: "评估结果",
          key: "evaluation_result",
        },
        {
          id: "1",
          name: "联系人",
          key: "contact_person",
        },
        {
          id: "8",
          name: "拜访日志",
          key: "visit_log",
        },
        {
          id: "12",
          name: "制程能力",
          key: "production_capability",
        },
        {
          id: "13",
          name: "交期评级",
          key: "delivery_rating",
        },
        {
          id: "14",
          name: "质量评级",
          key: "quality_rating",
        },
        // {
        //   id:'15',
        //   name:"财务评级"
        // },
        // {
        //     id:'10',
        //     name:"发单设置"
        // },
        // {
        //   id:'16',
        //   name:"组织架构"
        // },
        {
          id: "17",
          name: "配置",
          key: "configuration",
        },
        {
          id: "18",
          name: "假期日历",
          key: "holiday_calendar",
        },
        {
          id: "19",
          name: "系数配置",
          key: "coefficient_configuration",
        },
        {
          id: "9",
          name: "操作日志",
          key: "operation_log",
        },
      ],
      basicData: {},
      type: "",
      hitArray: [],
      checkCity: [],
      jsonList,
      visible: false,
      ModalText: "",
      guid: "",
      typeBtn: "",
      tabID: "0",
      compileApply: "", //是否可以编辑
      message: "",
      listSum: [],
      visible1: false,
      value1: "",
      value2: "",
      value3: "",
      value4: "",
      optionsWithDisabled: [
        { label: "单层板", value: "singleBoard", stat: false },
        { label: "双层板", value: "doubleBoard", stat: false },
        { label: "多层板", value: "multilayerBoard", stat: false },
        { label: "铝基板", value: "aluminumBoard", stat: false },
        { label: "FPC", value: "fpcBoard", stat: false },
      ],
      options: [
        { label: "正片", value: "positive", stat: false },
        { label: "负片", value: "negative", stat: false },
      ],
      optionsWith: [
        { label: "曝光", value: "exposure", stat: false },
        { label: "丝印", value: "silkScreen", stat: false },
      ],
      optionsList: [
        { label: "沉铜", value: "copperPrecipitation", stat: false },
        { label: "导电膜", value: "conductiveFilm", stat: false },
      ],
      checkbox_: [],
      billNum: [
        {
          title: "区间小面积",
          dataIndex: "minArea",
          scopedSlots: { customRender: "minArea" },
        },
        {
          title: "区间大面积",
          dataIndex: "maxArea",
          scopedSlots: { customRender: "maxArea" },
        },
        {
          title: "目标面积",
          dataIndex: "targetArea",
          scopedSlots: { customRender: "targetArea" },
        },
        {
          title: "目标工单",
          dataIndex: "targetPinBanOrderNum",
          scopedSlots: { customRender: "targetPinBanOrderNum" },
        },
        {
          title: "合拼工单拼版号最小面积",
          dataIndex: "pinBanOrderMinArea",
          scopedSlots: { customRender: "pinBanOrderMinArea" },
          width: 200,
        },
        {
          title: "合拼工单子料号最小面积",
          dataIndex: "orderMinArea",
          scopedSlots: { customRender: "orderMinArea" },
          width: 200,
        },
        {
          title: "设置",
          dataIndex: "action",
          scopedSlots: { customRender: "action" },
          width: 200,
        },
      ],
      billData: [
        {
          key: numbe.toString(),
          minArea: "",
          maxArea: "",
          targetArea: "",
          targetPinBanOrderNum: "",
          pinBanOrderMinArea: "",
          orderMinArea: "",
        },
      ],
      cnc: false,
      moldForming: false,
      valStat: false,
      valStat1: false,
      valStat2: false,
      valStat3: false,
      newStat: true,
      billId: "",
      activeKey: "0",
      statisticalData: [],
      columns1,
      statisticalData1: [],
      deliveryColumns2,
      departGroupPostDto: {},
      ConfigurationForm: {},
      ruleItemsData: [],
      columns4,
      orderListTableLoading2: false,
    };
  },
  components: {
    AddDeliveryJQ,
    basicInfo,
    StandardTable,
    addCust,
    addEquip,
    addQualify,
    addFactory,
    addEvaluation,
    addBoard,
    addRecord,
    addLink,
    checkPhoto,
    addDelivery,
    addDeliveryZL,
    BarChart,
    calendarData,
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: "validate_other" });
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.editstatus) {
      if (confirm("当前为编辑状态数据未保存,请确认是否需要进行跳转页面?")) {
        next();
        this.setedit(false);
      } else {
        next(false);
      }
    } else {
      next();
      this.setedit(false);
    }
  },
  computed: {
    ...mapState("setting", ["editstatus"]),
  },
  watch: {
    "$i18n.locale": function (newLocale) {
      this.updateTableTitle();
    },
  },
  created() {
    let copyArray = JSON.parse(JSON.stringify(this.headList));
    copyArray.forEach((item, index) => {
      if (item.id == 13) {
        if (!checkPermission("MES.CoordinationModule.SupplierManagement.DeliveryRating")) {
          item["auth"] = true;
        }
      }
      if (item.id == 14) {
        if (!checkPermission("MES.CoordinationModule.SupplierManagement.QualityRating")) {
          item["auth"] = true;
        }
      }
      if (item.id == 15) {
        if (!checkPermission("MES.CoordinationModule.SupplierManagement.FinancialRating")) {
          item["auth"] = true;
        }
      }
      if (item.id == 17) {
        if (!checkPermission("MES.CoordinationModule.SupplierManagement.SupperConfig")) {
          item["auth"] = true;
        }
      }
    });
    this.headList = copyArray.filter(item => {
      return !item.auth;
    });
    this.suppId = this.$route.query.id;
    this.getQuoteData();
    if (this.$route.query.ids == "17") {
      this.activeKey = "17";
    }
    if (this.$route.query.ids == "18") {
      this.activeKey = "18";
    }
    this.getSupData();
    this.type = this.$route.query.type;
    if (this.type == "1") {
      this.setedit(false);
    } else if (this.type == "2") {
      this.setedit(true);
    }
    this.getlink();
    this.getCityData();
    this.getQualityRatingList2(2);
    this.getQualityRatingList2(1);
    this.getsupParinfo();
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.pathEnterUrl = from.fullPath;
    });
  },
  methods: {
    ...mapMutations("setting", ["setedit"], ""),
    updateTableTitle() {
      this.headList.forEach(headList => {
        if (headList.key) {
          headList.name = this.$t(headList.key);
        }
      });
      this.changtype.forEach(changtype => {
        if (changtype.key) {
          changtype.text = this.$t(changtype.key);
        }
      });
      this.metricBritishSystemList.forEach(metricBritishSystemList => {
        if (metricBritishSystemList.key) {
          metricBritishSystemList.text = this.$t(metricBritishSystemList.key);
        }
      });
      this.modelist.forEach(modelist => {
        if (modelist.key) {
          modelist.text = this.$t(modelist.key);
        }
      });
      this.englist.forEach(englist => {
        if (englist.key) {
          englist.text = this.$t(englist.key);
        }
      });
      let arr = [
        "columns1",
        "deliveryColumns",
        "deliveryColumns1",
        "deliveryColumns2",
        "columns4",
        "linkColumns",
        "custColumns",
        "facilityColumns",
        "certColumns",
        "factoryColumns",
        "assessmentColumns",
        "logColumns",
        "logCaozuo",
      ];
      arr.forEach(item => {
        this[item].forEach(column => {
          if (column.key) {
            column.title = this.$t(column.key);
          }
        });
      });
    },
    goback() {
      if (this.$route.query.factory == "协同") {
        this.$router.push({ path: "/collaborative/Coordination" });
      } else if (this.$route.query.factory == "零单") {
        this.$router.push({ path: "/collaborative/ZeroOrder" });
      } else if (this.$route.query.factory == "供应商") {
        this.$router.push({ path: "/collaborative/supplier" });
      } else if (this.$route.query.factory == "详情") {
        if (this.pathEnterUrl == "/collaborative/detail") {
          this.$router.go(-1); // 返回上一级路由
        } else {
          const name = JSON.parse(localStorage.getItem("gradeName")).data;
          const type = JSON.parse(localStorage.getItem("gradeType")).data;
          this.$router.push({ path: "/collaborative/gradeDetails1?name=" + name + "&type=" + type });
        }
      }
    },
    onChange1(value, dateString) {
      this.ConfigurationForm.expiredTime = dateString;
      this.submitFormBtn = false;
    },
    getsupParinfo() {
      getsupParinfo(this.suppId).then(res => {
        if (res) {
          this.ConfigurationForm = res.configDto;
          this.isPcbSendOrder = res.configDto.isPcbSendOrder === 1 ? true : false;
          if (this.ConfigurationForm.isAutoSend == null || !this.ConfigurationForm.isAutoSend) {
            this.ConfigurationForm.isAutoSend = [];
          } else {
            this.ConfigurationForm.isAutoSend = res.configDto.isAutoSend.split(",");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    deleteS() {
      return <strong>确定要删除吗？</strong>;
    },
    handleSubmit(e) {
      e.preventDefault();
      var copyObjQuoteForm = this.QuoteForm;
      var copyObjInitCheckData = this.InitCheckData;
      const resultObject = {};
      this.copylistdata.forEach(key => {
        resultObject[key] = null;
      });
      var newObj = {};
      for (const key in copyObjQuoteForm) {
        if (Object.hasOwnProperty.call(copyObjQuoteForm, key)) {
          const element = copyObjQuoteForm[key];
          if (typeof element == "object") {
            if (key) {
              newObj[key] = element.join(",");
            }
          } else {
            newObj[key] = element;
          }
        }
      }
      var setObjData = {};
      for (const copyObjInitCheckDatakey in resultObject) {
        if (Object.hasOwnProperty.call(resultObject, copyObjInitCheckDatakey)) {
          for (const newObjkey in newObj) {
            if (Object.hasOwnProperty.call(newObj, newObjkey)) {
              if (copyObjInitCheckDatakey == newObjkey) {
                setObjData[copyObjInitCheckDatakey] = newObj[newObjkey];
                break;
              } else {
                setObjData[copyObjInitCheckDatakey] = resultObject[copyObjInitCheckDatakey];
              }
            }
          }
        }
      }
      setObjData.id = this.InitCheckData.id;
      editProcessCapability(setObjData).then(res => {
        if (res.code) {
          this.$message.success("修改制程成功");
        } else {
          this.$message.error(res.message);
        }
      });
      this.submitFormBtn = true;
      this.$refs.basicSave[0].editDataInfo("制程");
    },
    addList(data) {
      numbe++;
      this.billData.push({
        key: numbe.toString(),
        minArea: "",
        maxArea: "",
        targetArea: "",
        targetPinBanOrderNum: "",
        pinBanOrderMinArea: "",
        orderMinArea: "",
      });
    },

    // deliveryColumn(text){
    //   if (this.gradeDate.length>18){
    //     this.gradeDate.substring(0,8)
    //   }
    //   else{
    //      this.gradeDate.substring(0,7)
    //   }
    // },

    deleList(text) {
      if (this.billData.length > 1) {
        this.billData.splice(text, 1);
      }
    },

    setBill(data) {
      this.newStat = true;
      this.valStat = true;
      this.valStat1 = true;
      this.valStat2 = true;
      this.valStat3 = true;
      this.billId = data.id;
      setBill(data.id).then(res => {
        if (res.code == "1") {
          this.getBillList();
          this.visible1 = true;
          let optionsWithDisabled = [
            { label: "单层板", value: "singleBoard", stat: res.data.singleBoard },
            { label: "双层板", value: "doubleBoard", stat: res.data.doubleBoard },
            { label: "多层板", value: "multilayerBoard", stat: res.data.multilayerBoard },
            { label: "铝基板", value: "aluminumBoard", stat: res.data.aluminumBoard },
            { label: "FPC", value: "fpcBoard", stat: res.data.fpcBoard },
          ];
          optionsWithDisabled.forEach(res => {
            if (res.stat) {
              this.value1 = res.value;
            }
          });

          let options = [
            { label: "正片", value: "positive", stat: res.data.positive },
            { label: "负片", value: "negative", stat: res.data.negative },
          ];
          options.forEach(res => {
            if (res.stat) {
              this.value2 = res.value;
            }
          });
          let optionsWith = [
            { label: "曝光", value: "exposure", stat: res.data.exposure },
            { label: "丝印", value: "silkScreen", stat: res.data.silkScreen },
          ];
          optionsWith.forEach(res => {
            if (res.stat) {
              this.value3 = res.value;
            }
          });
          let optionsList = [
            { label: "沉铜", value: "copperPrecipitation", stat: res.data.copperPrecipitation },
            { label: "导电膜", value: "conductiveFilm", stat: res.data.conductiveFilm },
          ];
          optionsList.forEach(res => {
            if (res.stat) {
              this.value4 = res.value;
            }
          });

          if (res.data.cnc) {
            this.checkbox_.push("cnc");
          }
          if (res.data.moldForming) {
            this.checkbox_.push("moldForming");
          }
          this.billData = res.data.lssuingContents;
        }
      });
    },
    delBill(data) {
      deleBill(data.id).then(res => {
        if (res.code == "1") {
          this.getBillList();
        }
      });
    },
    RadioChange(e, index) {
      this.valStat = true;
      this.value1 = e.target.value;
    },
    RadioChange1(e) {
      this.valStat1 = true;
      this.value2 = e.target.value;
    },
    RadioChange2(e) {
      this.valStat2 = true;
      this.value3 = e.target.value;
    },
    RadioChange3(e) {
      this.valStat3 = true;
      this.value4 = e.target.value;
    },
    onChange(checkedValues) {
      this.checkbox_ = checkedValues;
    },
    handleOk1() {
      this.optionsWithDisabled.forEach(res => {
        if (res.value == this.value1) {
          res.stat = true;
        } else {
          res.stat = false;
        }
      });
      this.options.forEach(res => {
        if (res.value == this.value2) {
          res.stat = true;
        } else {
          res.stat = false;
        }
      });
      this.optionsWith.forEach(res => {
        if (res.value == this.value3) {
          res.stat = true;
        } else {
          res.stat = false;
        }
      });
      this.optionsList.forEach(res => {
        if (res.value == this.value4) {
          res.stat = true;
        } else {
          res.stat = false;
        }
      });
      this.checkbox_.find(res => {
        if (this.checkbox_.length > 1) {
          this.moldForming = true;
          this.cnc = true;
        }
        if (this.checkbox_.length == 1) {
          if (res == "moldForming") {
            this.moldForming = true;
          } else {
            this.moldForming = false;
          }
          if (res == "cnc") {
            this.cnc = true;
          } else {
            this.cnc = false;
          }
        }
      });
      let params = {
        pid: this.suppId,
        singleBoard: this.optionsWithDisabled[0].stat,
        doubleBoard: this.optionsWithDisabled[1].stat,
        multilayerBoard: this.optionsWithDisabled[2].stat,
        aluminumBoard: this.optionsWithDisabled[3].stat,
        fpcBoard: this.optionsWithDisabled[4].stat,
        positive: this.options[0].stat,
        negative: this.options[1].stat,
        exposure: this.optionsWith[0].stat,
        silkScreen: this.optionsWith[1].stat,
        copperPrecipitation: this.optionsList[0].stat,
        conductiveFilm: this.optionsList[1].stat,
        cnc: this.cnc,
        moldForming: this.moldForming,
        lssuingContents: this.billData,
      };
      if (this.valStat && this.valStat1 && this.valStat2 && this.valStat3) {
        this.billData.forEach(res => {
          if (
            res.minArea !== "" &&
            res.maxArea != "" &&
            res.targetArea !== "" &&
            res.targetPinBanOrderNum !== "" &&
            res.pinBanOrderMinArea !== "" &&
            res.orderMinArea !== ""
          ) {
            if (Number(res.minArea) < Number(res.maxArea)) {
              if (!this.newStat) {
                billAdd(params).then(res => {
                  if (res.code == "1") {
                    this.visible1 = false;
                    this.getBillList();
                  } else {
                    this.$message.info(res.message);
                  }
                });
              } else {
                params.id = this.billId;
                billAmand(params).then(res => {
                  if (res.code == "1") {
                    this.visible1 = false;
                    this.getBillList();
                  } else {
                    this.$message.info(res.message);
                  }
                });
              }
            } else {
              this.$message.info("区间小面积不能大于区间大面积");
            }
          } else {
            this.$message.info("不能为空");
          }
        });
      } else {
        this.$message.info("请选择制作工艺或所属品类");
      }
    },
    newAdd() {
      this.newStat = false;
      this.visible1 = true;
      this.value1 = "";
      this.value2 = "";
      this.value3 = "";
      this.value4 = "";
      this.checkbox_ = [];
      this.billData = [
        {
          key: numbe.toString(),
          minArea: "",
          maxArea: "",
          targetArea: "",
          targetPinBanOrderNum: "",
          pinBanOrderMinArea: "",
          orderMinArea: "",
        },
      ];
    },
    handleCancel1() {
      this.visible1 = false;
      this.value1 = "";
      this.value2 = "";
      this.value3 = "";
      this.value4 = "";
      this.checkbox_ = [];
      this.billData = [
        {
          key: numbe.toString(),
          minArea: "",
          maxArea: "",
          targetArea: "",
          targetPinBanOrderNum: "",
          pinBanOrderMinArea: "",
          orderMinArea: "",
        },
      ];
    },
    handleChange(value, key, column) {
      const newData = [...this.billData];
      const target = newData.filter(item => key === item.key)[0];
      if (target) {
        target[column] = value;
        this.billData = newData;
      }
    },

    checkPermission,
    callback(key) {
      this.tabID = key;
      if (key == "9") {
        caozuoLog(this.suppId).then(res => {
          if (res.code == "1") {
            res.data.forEach(item => {
              item.inDate = item.inDate.replace(/T/g, " ").replace(/\.[\d]{3}Z/, "");
            });
            this.logList = res.data;
          }
        });
      }
      if (key == "10") {
        this.getBillList();
      }
      if (key == "13") {
        this.someType = 1;
        this.getDeliveryData(1);
      }
      if (key == "14") {
        this.someType = 2;
        this.getDeliveryData(2);
      }
      if (key == "15") {
        this.someType = 3;
        this.getDeliveryData(3);
      }
      this.tmp = key;
      if (key == "0") {
        this.$nextTick(function () {
          this.$refs.basicSave[0].src = this.basicData.logoAddress;
          this.$refs.basicSave[0].updateData = this.basicData;
        });
      }
      if (key == "12") {
        this.$nextTick(() => {
          let data = [];
          let capacity = document.getElementsByClassName("capacity")[0].children[0];
          for (let ind = 0; ind < this.QuoteDataList.length; ind++) {
            let maxLength = 0;
            let str = "";
            const element = this.QuoteDataList[ind];
            let option = capacity.children[ind].children[1].children[0].children[0].children[0].children;
            for (let i = 0; i < element.options.length; i++) {
              let val = element.options[i].label;
              if (val.length > maxLength) {
                maxLength = val.length;
                str = val;
              }
            }
            data.push(this.calculateWidth(str) + 36);
            if (element.type == "1" || element.type == "3") {
              for (let i = 0; i < element.options.length; i++) {
                option[i].style.width = `${data[ind]}px`;
              }
            }
          }
        });
      }
    },
    calculateWidth(str) {
      let totalWidth = 0;
      for (let i = 0; i < str.length; i++) {
        let char = str[i];
        if (/[\u4e00-\u9fa5]/.test(char)) {
          totalWidth += 15;
        } else {
          totalWidth += 9;
        }
      }
      return totalWidth;
    },
    getBillList() {
      billList(this.suppId).then(res => {
        res.data.forEach((item, index) => {
          item.key = index;
        });
        this.listSum = res.data;
        numbe = res.data.length;
      });
    },
    btnList(val, index) {
      this.typeBtn = index;
      this.guid = val.id;
      this.visible = true;
      if (index == 1) {
        this.ModalText = "是否提交";
      } else if (index == 2) {
        this.ModalText = "是否拜访";
      } else if (index == 3) {
        this.ModalText = "是否审批";
      }
    },
    handleOk() {
      if (this.typeBtn == "1") {
        submitLog(this.guid).then(res => {
          this.visible = false;
          this.$message.info(res.message);
          this.getSupData();
        });
      } else if (this.typeBtn == "2") {
        roundLog(this.guid).then(res => {
          this.visible = false;
          this.$message.info(res.message);
          this.getSupData();
        });
      } else if (this.typeBtn == "3") {
        check(this.guid).then(res => {
          this.visible = false;
          this.$message.info(res.message);
          this.getSupData();
        });
      }
    },
    handleCancel() {
      this.visible = false;
    },
    //获取省市级数据
    getCityData() {
      // axios.get(`${this.baseUrl}/index.json`).then((data)=>{
      //     console.log(data)
      this.cityData = jsonList;
      // })
    },
    //点击编辑
    editBasicInfo() {
      if (this.tabID == "19" && this.ruleItemsData.length == 0) {
        this.$message.info("没有可编辑项");
        return;
      }
      this.type = "2";
      this.setedit(true);
    },
    //点击取消
    cancel() {
      this.type = "1";
      this.setedit(false);
      this.$refs.basicSave[0].logoAddress = [];
      this.$refs.basicSave[0].fileListData = [];
      if (this.tmp == "19") {
        this.getRuleItemsList();
      }
    },
    //保存信息后进行查询
    perserve() {
      this.getSupData();
      this.type = "1";
      this.setedit(false);
    },
    //查看图片
    checkPhoto() {},
    //获取详情信息
    getSupData() {
      let id = this.suppId;
      getSup(id).then(res => {
        this.basicData = res.eMSSupplierModuleNoDto;
        this.factoryId = this.basicData.factoryId;
        if (this.$refs.basicSave) {
          this.$refs.basicSave[0].fileListData = [];
        }
        if (this.factoryId) {
          this.getRuleItemsList();
        }
        this.custData = res.eMSSupplierCustomerInfoDtos;
        this.facilityData = res.eMSSupplierProductEquipmentDtos;
        this.certData = res.eMSSupplierQualificationDtos;
        this.factoryData = res.eMSSupplierPhotoCertificationDtos;
        this.assessmentData = res.eMSSupplierEvaluationResultDtos;
        this.slabData = res.eMSSupplierBoardDtos;
        this.logData = res.eMSSupplierVisitInfoDtos;
        this.logData.forEach(res => {
          if (res.visitDate_ != "" && res.visitDate_ != null) {
            res.visitDate_ = moment(res.visitDate_).format("YYYY-MM-DD");
          }
          if (res.planVisitDate_ != "" && res.planVisitDate_ != null) {
            res.planVisitDate_ = moment(res.planVisitDate_).format("YYYY-MM-DD");
          }
        });

        //根据id转换省市区的树形结构得到name
        let searchLeaf = (items, hittext) => {
          for (let index in items) {
            if (items[index].value == hittext) {
              this.hitArray.push(items[index].label);
              return false;
            }
            searchLeaf(items[index].children, hittext);
          }
        };
        searchLeaf(this.cityData, this.basicData.province_);
        searchLeaf(this.cityData, this.basicData.city_);
        searchLeaf(this.cityData, this.basicData.area_);
        this.checkCity.push(this.basicData.province_, this.basicData.city_, this.basicData.area_);
        // console.log(this.basicData)
        if (this.basicData.glass_) {
          this.checkbox.push("glass_");
        }
        if (this.basicData.alminum_) {
          this.checkbox.push("alminum_");
        }
        if (this.basicData.cuminum_) {
          this.checkbox.push("cuminum_");
        }
        if (this.basicData.flexible_) {
          this.checkbox.push("flexible_");
        }
        if (this.basicData.hard_) {
          this.checkbox.push("hard_");
        }
        if (this.basicData.ceramic_) {
          this.checkbox.push("ceramic_");
        }
        if (this.basicData.high_) {
          this.checkbox.push("high_");
        }
        if (this.basicData.allowIssuing) {
          this.checkbox1.push("allowIssuing");
        }
        if (this.basicData.importIssuing) {
          this.checkbox1.push("importIssuing");
        }
      });
    },
    getDeliveryData(type) {
      let params = {
        pGuid: this.suppId,
        ratingType: type,
        pageIndex: 1,
        pageSize: 20,
      };
      if (type == "2") {
        // 质量评级
        // console.log('新增ok2')
        qualityRatingList(params).then(res => {
          if (res.data) {
            this.deliveryData = res.data;
            this.getQualityRatingList2(2);
            // this.$message.success('成功')
          } else {
            this.$message.error(res.message);
          }
        });
      } else if (type == "1") {
        // 质量评级
        // console.log('新增ok2')
        qualityRatingList(params).then(res => {
          if (res.data) {
            this.deliveryData = res.data;
            this.getQualityRatingList2(1);
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        getDeliveryData(params).then(res => {
          if (res.data) {
            // console.log('res.data',res.data)
            this.deliveryData = res.data.items;
          }
        });
      }
    },
    async getQualityRatingList2(type) {
      let params = {
        pGuid: this.suppId,
        ratingType: type,
        pageIndex: 1,
        pageSize: 20,
      };
      if (type == "2") {
        await qualityRatingList2(params).then(res => {
          if (res.code) {
            this.statisticalData = res.data;
            let obj = this.statisticalData.find(ite => {
              return ite.realname === "综合得分";
            });

            let arr = Object.values(obj).slice(1);
            let arr_ = [];
            arr.forEach(item => {
              arr_.push(Number(item));
            });
            this.barData1 = arr_;
            //  console.log('22',this.barData1)
          }
        });
      } else if (type == "1") {
        await qualityRatingList2(params).then(res => {
          if (res.code) {
            this.statisticalData = res.data;
            let obj = this.statisticalData.find(ite => {
              return ite.realname === "综合得分";
            });

            let arr = Object.values(obj).slice(1);
            let arr_ = [];
            arr.forEach(item => {
              arr_.push(Number(item));
            });
            this.barData2 = arr_;
            //  console.log('11',this.barData2)
          }
        });
      }
    },
    handleDel(id) {
      deleteCust(id).then(res => {
        if (res.code) {
          this.$message.success("删除成功");
        } else {
          this.$message.error(res.message);
        }

        this.getCustomer();
      });
    },
    //获取客户信息
    getCustomer() {
      getCust(this.suppId).then(res => {
        this.custData = res;
      });
    },
    //删除生产设备
    handleDelEquip(id) {
      deleteEquip(id).then(res => {
        this.$message.info("删除成功");
        this.getEquip();
      });
    },
    //获取生产信息
    getEquip() {
      getEquip(this.suppId).then(res => {
        this.facilityData = res;
      });
    },
    //删除体系认证
    handleDelQualify(id) {
      deleteQualify(id).then(res => {
        this.$message.info("删除成功");
        this.getQualify();
      });
    },
    handleDelDelivery(id) {
      // console.log('删除id',id)
      deleteDeliveryData(id).then(res => {
        if (res.code) {
          this.$message.success("删除成功");
          this.getDeliveryData(this.someType);
        }
      });
    },
    //查询体系认证
    getQualify() {
      getQualify(this.suppId).then(res => {
        this.certData = res;
      });
    },
    //查询工厂图库
    getFactory() {
      getFactory(this.suppId).then(res => {
        this.factoryData = res;
      });
    },
    //删除工厂图库
    handleDelFactory(id) {
      deleteFactory(id).then(res => {
        this.$message.info("删除成功");
        this.getFactory();
      });
    },
    //删除评估结果
    handleDelEvaluation(id) {
      deleteAssest(id).then(res => {
        this.$message.info("删除成功");
        this.getAssest();
      });
    },
    //查询评估结果
    getAssest() {
      getAssest(this.suppId).then(res => {
        this.assessmentData = res;
      });
    },
    //查询板材物料
    getBoard() {
      getBoard(this.suppId).then(res => {
        this.slabData = res;
      });
    },
    //删除评估结果
    handleDelBoard(id) {
      deleteBoard(id).then(res => {
        this.$message.info("删除成功");
        this.getBoard();
      });
    },
    //查询拜访日志
    getlog() {
      getlog(this.suppId).then(res => {
        this.logData = res;
        this.logData.forEach(res => {
          if (res.visitDate_ != "" && res.visitDate_ != null) {
            res.visitDate_ = moment(res.visitDate_).format("YYYY-MM-DD");
          }
          if (res.planVisitDate_ != "" && res.planVisitDate_ != null) {
            res.planVisitDate_ = moment(res.planVisitDate_).format("YYYY-MM-DD");
          }
        });
      });
    },
    //删除拜访日志
    handleDellog(id) {
      deletelog(id).then(res => {
        this.$message.info("删除成功");
        this.getlog();
      });
    },
    //查询联系人
    getlink() {
      getLink(this.suppId).then(res => {
        this.linkData = res;
      });
    },
    //制程能力参数配置获取
    getQuoteData() {
      getcapacityConfig().then(res => {
        var data = res.data;
        let newObj = {};
        var list = [];
        for (var i = 0; i < data.length; i++) {
          var dataArr = [];
          var dataValueArr = [];
          for (var j = 0; j < data[i].capacityConfigDetais.length; j++) {
            dataArr.push(data[i].capacityConfigDetais[j].contents);
            dataValueArr.push(data[i].capacityConfigDetais[j].value);
          }
          list.push(res.data[i].value);
          newObj[res.data[i].contents] = {
            value: res.data[i].value,
            data: dataArr,
            dataValueArr: dataValueArr,
            type: res.data[i].type,
          };
        }
        let QuoteDataListArr = [];
        for (const key in newObj) {
          if (Object.hasOwnProperty.call(newObj, key)) {
            const element = newObj[key];
            QuoteDataListArr.push({
              name: key,
              data: element,
              options: "",
              value: element.value,
              type: element.type,
            });
          }
        }

        for (let k = 0; k < QuoteDataListArr.length; k++) {
          var optionsData = [];
          for (let w = 0; w < QuoteDataListArr[k].data.dataValueArr.length; w++) {
            optionsData.push({
              label: QuoteDataListArr[k].data.data[w],
              value: QuoteDataListArr[k].data.dataValueArr[w],
            });
          }
          QuoteDataListArr[k]["options"] = optionsData;
        }
        getProcessCapability(this.suppId).then(resCheck => {
          var checkData = resCheck.data;
          this.InitCheckData = checkData;
          for (let check = 0; check < QuoteDataListArr.length; check++) {
            for (const key in checkData) {
              if (Object.hasOwnProperty.call(checkData, key)) {
                const element = checkData[key];
                if (QuoteDataListArr[check].value.toLowerCase() == key.toLowerCase()) {
                  if (element) {
                    let value = QuoteDataListArr[check].value;
                    if (QuoteDataListArr[check].type == "3" || QuoteDataListArr[check].type == "2") {
                      this.$set(this.QuoteForm, [value], element);
                    } else {
                      this.$set(this.QuoteForm, [value], element.split(","));
                    }
                  }
                }
              }
            }
          }
          this.QuoteDataList = QuoteDataListArr;
        });
        this.copylistdata = list;
      });
    },
    //删除联系人
    handleLink(id) {
      deleteLink(id).then(res => {
        this.$message.info("删除成功");
        this.getlink();
      });
    },
    araeblur() {
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/; //正数正则
      if (this.ConfigurationForm.orderConfigDto.area && !x.test(this.ConfigurationForm.orderConfigDto.area)) {
        this.$message.error("单日最大面积请输入正数");
        this.ConfigurationForm.orderConfigDto.area = "";
      }
    },
    urgentNumblur() {
      var r = /^\+?[1-9][0-9]*$/; //正整数正则
      if (this.ConfigurationForm.orderConfigDto.urgentNum && !r.test(this.ConfigurationForm.orderConfigDto.urgentNum)) {
        this.$message.error("单日最大加急请输入正数");
        this.ConfigurationForm.orderConfigDto.urgentNum = "";
      }
    },
    numberblur() {
      var r = /^\+?[1-9][0-9]*$/; //正整数正则
      if (this.ConfigurationForm.orderConfigDto.number && !r.test(this.ConfigurationForm.orderConfigDto.number)) {
        this.$message.error("单日最大款数请输入正数");
        this.ConfigurationForm.orderConfigDto.number = "";
      }
    },
    // 配置确认
    SubmitOk() {
      this.ConfigurationForm.feedbackMode = this.ConfigurationForm.feedbackMode ? Number(this.ConfigurationForm.feedbackMode) : null;
      this.ConfigurationForm.isPcbSendOrder = this.isPcbSendOrder === true ? 1 : 0;
      if (!this.ConfigurationForm.callbackUrl) {
        this.ConfigurationForm.callbackUrl = null;
      }
      if (!this.ConfigurationForm.expiredTime) {
        this.ConfigurationForm.expiredTime = null;
      }
      if (!this.ConfigurationForm.remainCurrency) {
        this.ConfigurationForm.remainCurrency = null;
      }
      this.submitFormBtn = true;
      if (this.ConfigurationForm.orderConfigDto.area) {
        this.ConfigurationForm.orderConfigDto.area = Number(this.ConfigurationForm.orderConfigDto.area);
      }
      if (this.ConfigurationForm.orderConfigDto.urgentNum) {
        this.ConfigurationForm.orderConfigDto.urgentNum = Number(this.ConfigurationForm.orderConfigDto.urgentNum);
      }
      if (this.ConfigurationForm.orderConfigDto.number) {
        this.ConfigurationForm.orderConfigDto.number = Number(this.ConfigurationForm.orderConfigDto.number);
      }
      this.ConfigurationForm.isAutoSend = this.ConfigurationForm.isAutoSend.length ? this.ConfigurationForm.isAutoSend.join(",") : null;
      let params = this.ConfigurationForm;
      params.id = this.suppId;
      setSupplierConfig(params)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.getsupParinfo();
        });
    },
    //系数配置
    Valuechange() {
      console.log("custItemList", this.custItemList);
      this.$forceUpdate();
    },
    Valueblur(value) {
      this.$forceUpdate();
    },
    // 子项列表数据
    getRuleItemsList() {
      console.log("工厂id", this.factoryId);
      ruleItemsList(this.factoryId).then(res => {
        if (res.code) {
          this.ruleItemsData = res.data;
          for (var b = 0; b < this.ruleItemsData.length; b++) {
            if (this.ruleItemsData[b].dataType == "bit") {
              if (this.ruleItemsData[b].value == "true") {
                this.ruleItemsData[b].value = true;
              } else {
                this.ruleItemsData[b].value = false;
              }
            }
          }
          // let arr = []
          // let data1 = this.ruleItemsData.filter(item => { return item.groups == '包装要求' })
          // let data2 = this.ruleItemsData.filter(item => { return item.groups == 'FQC' })
          // let data3 = this.ruleItemsData.filter(item => { return item.groups == '工程制作' })
          // arr.push(data1)
          // arr.push(data2)
          // arr.push(data3)
          // console.log('arr',arr)
          // this.ruleItemsData = arr
          this.rowSpan1("groups");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //合并列
    rowSpan1(key) {
      let _list = this.ruleItemsData;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        //  最后一条的 +1 时会为undefind 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); //获取相同的 下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              //其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          //  当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          //当与下一条数据不同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.ruleItemsData = _list;
    },
    saveRuleItems() {
      var r = /^\+?[1-9][0-9]*$/; //正整数正则
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/; //正数正则
      let arr = this.ruleItemsData;
      for (var aa = 0; aa < arr.length; aa++) {
        if (arr[aa].value && arr[aa].dataType == "int" && !r.test(arr[aa].value)) {
          this.$message.warning(arr[aa].groups + "请输入正整数");
          return;
        }
        if (arr[aa].value && arr[aa].dataType == "float" && !x.test(arr[aa].value)) {
          this.$message.warning(arr[aa].groups + "请输入正数");
          return;
        }
      }
      for (var bb = 0; bb < arr.length; bb++) {
        if (arr[bb].dataType == "bit") {
          if (arr[bb].value == true) {
            arr[bb].value = "true";
          } else {
            arr[bb].value = "false";
          }
        }
      }
      this.type = "1";
      this.setedit(false);
      setRuleItemsList(arr).then(res => {
        if (res.code) {
          this.$message.success("列表编辑成功");
          this.getRuleItemsList();
        } else {
          this.$message.error(res.message);
          this.getRuleItemsList();
        }
      });
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
  filters: {
    filterColumn(data, list) {
      let name = "";
      list.filter(item => {
        if (item.key == data) {
          name = item.value;
        }
      });
      return name;
    },
    th_barData: function (data) {
      let name = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "10月", "12月"],
        value1 = [];
      if (data) {
        value1 = data.map(ite => {
          return ite;
        });
      }
      return {
        name: name,
        value: [
          {
            type: "bar",
            data: value1,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "top",
                  textStyle: {
                    color: "#333",
                    fontWeight: 500,
                  },
                  formatter: function (params) {
                    if (params.value) {
                      return params.value;
                    } else {
                      return "";
                    }
                  },
                },
              },
            },
          },
        ],
      };
    },
  },
  mounted() {
    this.updateTableTitle();
  },
};
</script>

<style scoped lang="less">
/deep/.i18nwidth .ant-checkbox-wrapper {
  margin-left: 5px;
}
/deep/.i18nwidth .ant-checkbox-wrapper-checked {
  margin-left: 5px;
}
/deep/.i18nwidth .ant-col-4 {
  width: 320px;
}
/deep/.ant-radio-wrapper {
  color: #000;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-checkbox-wrapper {
  color: #000000;
}

/deep/.ant-tabs-nav.ant-tabs-nav-animated {
  margin: 9px;
}
/deep/.ant-form-item-children .ant-input {
  display: inline-flex;
  font-weight: 500 !important;
  margin: 5px;
}

/deep/.ant-form-item {
  margin-bottom: 11px;
}
/deep/.ant-col-14 {
  width: 64.3%;
}
/deep/.ant-calendar-picker {
  width: 100%;
}
/deep/.ant-select {
  width: 100% !important;
}
/deep/.ant-cascader-picker {
  width: 100% !important;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}

.ant-table-body {
  max-height: 314px;
  overflow-y: scroll;
}
.t4 {
  // /deep/.ant-table-wrapper{
  //   top: -2px;
  //   position: relative;
  // }
  /deep/.ant-table-placeholder {
    max-height: 352px;
  }

  /deep/.ant-table-content {
    max-height: 354px;
  }
  .margin10 {
    z-index: 999;
    position: relative;
    top: 357px;
    left: 95%;
  }
  // /deep/.ant-table-body{
  //   border-bottom: 3px solid #eff1f4;
  //   height: 323px!important;
  //   max-width: 1681px!important;
  // }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin: 10px 0;
    position: relative;
    left: 10px;
  }
  /deep/.ant-table-scroll {
    position: relative;
    top: -2px;
    max-height: 360px;
  }
}
/deep/.ant-table-header.ant-table-hide-scrollbar {
  margin-bottom: -17px;
  padding-bottom: 0px;
  min-width: 17px;
  overflow: scroll;
  padding-right: 23px;
}
.t6 {
  /deep/.ant-table-body {
    min-height: 680px;
  }
  /deep/.ant-table-scroll {
    min-height: 672px;
    position: relative;
    top: -2px;
  }
  /deep/.ant-card.ant-card-bordered {
    max-height: 820px;
  }
}
.t3 {
  /deep/.ant-input {
    width: 68% !important;
    height: 25px;
  }
  /deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    position: relative;
    top: -637px;
  }
  /deep/.ant-table-body {
    height: 638px;
    overflow-y: auto;

    border-bottom: 5px solid #e9e9f0;
    margin: 0;
  }
  /deep/.ant-table-header.ant-table-hide-scrollbar {
    padding-right: 6px;
  }
  .margin10 {
    z-index: 999;
    position: relative;
    top: 683px;
    left: 95%;
  }
  /deep/.ant-tabs.ant-tabs-top.ant-tabs-line {
    border-bottom: 2px solid #e2e2e9;
    border-right: 2px solid #e2e2e9;
    height: 794px;
  }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    position: relative;
    top: 3px;
    right: -12px;
    margin: 6px 0;
  }
  /deep/.ant-table-scroll {
    min-height: 672px;

    position: relative;
    top: -2px;
  }
  /deep/.ant-table-content {
    height: 663px;
  }
  /deep/.userStyle {
    padding: 0 4px !important;
  }
}
.t5 {
  /deep/.ant-table-placeholder {
    bottom: 636px;
    position: relative;
    border: 0;
  }
  /deep/.ant-table-body {
    height: 638px;
    overflow-y: auto;
    border-bottom: 5px solid #e9e9f0;
    margin: 0;
  }
  .margin10 {
    z-index: 999;
    position: relative;
    top: 690px;
    left: 95%;
  }
  // max-height: 733px;
  /deep/.ant-tabs.ant-tabs-top.ant-tabs-line {
    border-bottom: 2px solid #e2e2e9;
    border-right: 2px solid #e2e2e9;
  }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    position: relative;
    top: 5px;
    right: -12px;
    margin: 6px 0;
  }
  /deep/.ant-table-scroll {
    top: -2px;
    position: relative;
    min-height: 683px;
  }
  /deep/.ant-table-content {
    max-height: 675px;
  }
  /deep/.ant-table-header.ant-table-hide-scrollbar {
    padding-right: 6px;
  }
}
.t2 {
  /deep/.ant-table-bordered.ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    position: relative;
    top: -637px;
  }
  /deep/.ant-table-header.ant-table-hide-scrollbar {
    padding-right: 6px;
  }
  /deep/.ant-table-body {
    height: 638px;
    overflow-y: auto;

    margin: 0;
  }
  .margin10 {
    z-index: 999;
    position: relative;
    top: 687px;
    left: 95%;
  }
  // max-height: 733px;
  /deep/.ant-tabs.ant-tabs-top.ant-tabs-line {
    border-bottom: 2px solid #e2e2e9;
    border-right: 2px solid #e2e2e9;
  }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    position: relative;

    right: -12px;
    margin: 6px 0;
  }
  /deep/.ant-table-scroll {
    overflow: hidden;
    overflow-x: hidden;
    top: -2px;
    position: relative;
    height: 683px;
    border-bottom: 5px solid #e9e9f0;
  }
  /deep/.ant-table-content {
    height: 675px;
  }
}
/deep/.ant-tabs-tabpane.ant-tabs-tabpane-active {
  position: relative;
  top: -17px;
}
/deep/.ant-tabs-nav-wrap {
  position: relative;
  top: 3px;
}
button {
  right: -5px;
  top: 7px;
  position: relative;
}
/deep/.ant-pagination-item-active {
  font-weight: 500;
}
/deep/.ant-card-body {
  min-height: 822px;
  padding: 0;
}
.boox {
  min-width: 1670px;
}
.capacity {
  max-height: 685px;
  overflow-y: auto;
  /deep/.ant-checkbox-wrapper-checked {
    border: 1px solid #f90 !important;
    background-color: rgb(253, 246, 235);
  }
  /deep/.ant-checkbox-wrapper-checked::before {
    content: "";
    position: absolute;
    top: 28px;
    right: -28px;
    width: 0;
    height: 0;
    border: 14px solid transparent;
    border-top-color: #f90;
    -webkit-transform: translate(-50%, -50%) rotate(315deg);
    transform: translate(-50%, -50%) rotate(315deg);
  }
  /deep/.ant-checkbox-wrapper-checked::after {
    content: "\2713";
    position: absolute;
    bottom: -14px;
    right: -5px;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    font-size: 14px;
    color: #ffffff;
  }

  /deep/.ant-radio-wrapper {
    margin-left: 0;
    border: 1px solid #ddd;
    padding: 3px 5px;
    margin: 5px;
  }
  /deep/.ant-checkbox-group-item {
    margin-left: 0;
    border: 1px solid #ddd;
    padding: 3px 5px;
    margin: 5px;
  }
  /deep/.ant-radio-wrapper-checked {
    border: 1px solid #f90 !important;
  }
  /deep/.ant-radio-wrapper-checked::before {
    content: "";
    position: absolute;
    top: 28px;
    right: -28px;
    width: 0;
    height: 0;
    border: 14px solid transparent;
    border-top-color: #f90;
    -webkit-transform: translate(-50%, -50%) rotate(315deg);
    transform: translate(-50%, -50%) rotate(315deg);
  }
  /deep/.ant-radio-wrapper-checked::after {
    content: "\2713";
    position: absolute;
    bottom: -14px;
    right: -5px;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    font-size: 14px;
    color: #ffffff;
  }
  /deep/.ant-checkbox {
    display: none;
  }
  /deep/.ant-radio {
    display: none;
  }
  .fixedBtn {
    justify-content: center;
    background-color: rgb(255, 255, 255);
    height: 45px;
    bottom: 14px;
    width: 75%;
    left: 214px;
    display: flex;
    position: fixed;
  }
}

.minClass {
  /deep/ .ant-table-body {
    border-bottom: 3px solid #eff1f4;
    height: 323px !important;
    max-width: 1681px !important;
    // height:328px;
  }
}
/deep/.ant-table-thead > tr > th {
  text-align: center;
  padding: 5px;
}
/deep/.ant-table-tbody > tr > td {
  text-align: center;
  height: 33px !important;
  padding: 5px;
}

// /deep/.ant-table-thead > tr > th:nth-of-type(1) {
//   text-align: left;
//   padding-left: 35px;
// }
// /deep/.ant-table-tbody > tr > td:nth-of-type(1){
//   text-align: left;
//   padding-left: 35px;
// }
h1,
h2 {
  font-weight: normal;
}
.Ontable {
  width: 100%;
  overflow: hidden;
}

.Ontable .table1 {
  width: 100%;
}

.Ontable table tr {
  height: 50px;
}
// .Ontable table tr:nth-child(0) th{}

.Ontable table th {
  background: #fafafa;
  text-align: center;
  height: 34px;
  line-height: 34px;
  color: #6c747f;
}

.Ontable table {
  text-align: center;
}
.active {
  /*background: black;*/
  color: aqua;
}
.contentTable {
  width: 100%;
}
.tableTWO {
  width: 100%;
}
.tableTWO td {
  border-right: 1px solid;
  border-bottom: 1px solid;
  height: 50px;
  line-height: 50px;
  padding: 0;
}
.tableTWO td:last-child {
  border-right: none;
}
.Ontable .tableTWO:last-child td {
  border-bottom: none;
}

//  /deep/.ant-table-hide-scrollbar{
//   margin-right:-11px;
//  }
// /deep/.ant-table-body{
// &::-webkit-scrollbar {//整体样式
//  width:12px;//y轴滚动条粗细
//  height: 2px;
//  }}
// &::-webkit-scrollbar-thumb {//滑动滑块条样式
//  border-radius: 2px;
//      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
//  // background: #00aaff;
//      background: #ccc8c8;
//  }
// &::-webkit-scrollbar-track {//轨道的样式
//  -webkit-box-shadow:0;
//      border-radius: 0;
//      background: #f6f8ff;
//  }
// }
</style>
