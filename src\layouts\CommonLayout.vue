<template>
  <div class="common-layout">
    <div class="content"><slot></slot></div>
<!--    <page-footer :link-list="footerLinks" :copyright="copyright"></page-footer>-->
  </div>
</template>

<script>
// import PageFooter from '@/layouts/footer/PageFooter'
import {mapState} from 'vuex'

export default {
  name: 'CommonLayout',
  // components: {PageFooter},
  computed: {
    ...mapState('setting', ['footerLinks', 'copyright'])
  }
}
</script>

<style scoped lang="less">
.common-layout{
  display: flex;
  // flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: url(../assets/img/loginbg.png) no-repeat center;
  // background-image: url('https://gw.alipayobjects.com/zos/rmsportal/TVYTbAXWheQpRcWDaDMu.svg');
  // background-repeat: no-repeat;
  // background-position-x: center;
  // background-position-y: 110px;
  .content{
    @media (max-width:800px){
      //width:70%;
    }
  } 
}
</style>
