<template>
    <div ref="tableWrapper">
        <a-table 
        :loading="loading"
        :columns="columns" 
        :customRow="customRow"
        :rowClassName="isRedRow"   
        :rowKey="rowKey"
        :data-source="dataSource" 
        :pagination="pagination"
        :scroll="{ y: 739,x:1200 }"
        @change="handleTableChange"
         >
         <template slot="orderNo" slot-scope="record">   
          {{record.orderNo}}
          <a-tooltip title='已打印对账单' v-if="record.isPrintStatement"> 
              <a-icon type="check-circle" theme="twoTone" two-tone-color="#ff9900" />
          </a-tooltip>
         </template> 
        </a-table>
         <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
            <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
         </a-menu>  
    </div>
</template>
  
  <script>
  export default {
    name: "",
    components: {},
    props: ['columns','dataSource','pagination','rowKey','loading'],
    data() {
      return {
        isDragging: false,
        menuVisible:false,
        menuStyle: {
          position: "absolute",
          top: "0",
          left: "0",
          zIndex:99
        },
        startIndex: -1,  
        showText:false,
        selectedRowKeysArray: [],
        selectedRowsData:{},
        shiftKey :false ,
      }
    },
    methods: {
        handleTableChange(pagination,filter,sorter){
            this.$emit('tableChange', pagination,filter,sorter)
        },
        customRow(record, index) {
      return {
        on: {
          mousedown: (event) => this.handleMouseDown(event, record, index),
          mousemove: (event) => this.handleMouseMove(event, record, index),
          mouseup: (event) => this.handleMouseUp(event, record, index),
          contextmenu: e => {
           let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
            } 
            e.preventDefault();
            this.rightClick1(e, text, record)
          },
        },
      };
    },
    keyup(e){
      this.shiftKey = e.ctrlKey 
    },  
    keydown(e){
      this.shiftKey = e.ctrlKey        
    }, 
    rightClick1(e,text,record){ 
      let event = e.target 
      if(e.target.localName != 'td'){
        event = e.target.parentNode
      }   
      if(e.target.parentNode.localName == 'span'){
        event = e.target.parentNode.parentNode.parentNode
      }   
      this.text=event.innerText;
      if(event.className.indexOf('noCopy') != -1 || !this.text){
        this.showText = false
      }else{    
        this.showText = true
      } 
      if(event.cellIndex == 1  || event.cellIndex == undefined){
        this.text = this.text.split(" ")[0]
      }
      const tableWrapper = this.$refs.tableWrapper;    
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;   
      let offsetx= event.offsetLeft  + event.offsetWidth - 10
      let offsety = event.offsetTop + 40;     
      if(event.cellIndex == this.columns.length -1){
        this.menuStyle.top =  cellRect.top - wrapperRect.top +  "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      }else{
        this.menuStyle.top = cellRect.top - wrapperRect.top  + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      } 
      if(event.cellIndex == 0 || event.cellIndex == 1){
        this.menuStyle.top = offsety  + "px";
        this.menuStyle.left = offsetx  + "px";
      }  
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },    
    down(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
      
    },
    handleMouseMove(event, record, index) {     
      event.stopPropagation();
      if (this.isDragging &&  event.button === 0 && this.startIndex != index ){
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
      }
    },    
    handleMouseUp(event, record, index) {     
        this.isDragging = false;      
      if(this.shiftKey){
        let rowKeys = this.selectedRowKeysArray;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }  
        this.selectedRowKeysArray = rowKeys; 
        if(this.selectedRowKeysArray.length==1){
          this.selectedRowsData = record 
        }      
      }else{
        if(this.startIndex == index){
          this.selectedRowsData = record       
          this.selectedRowKeysArray = [record.id]
        }         
      }
      this.shiftKey = false      
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr=[]
      for(var a=0;a<selectedRowsData.length;a++){
        arr.push(selectedRowsData[a].id)
      }
      this.selectedRowKeysArray = arr
      if(startIndex < endIndex){
        this.selectedRowsData = this.dataSource.filter(item => {return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length-1]})[0]  
      }else{
        this.selectedRowsData = this.dataSource.filter(item => {return item.id == this.selectedRowKeysArray[0]})[0]  
      }    
     
    },
        isRedRow(record){
      let strGroup = []
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push('rowBackgroundColor')
      } 
      return strGroup   
    }, 
    },
    created() {},
    mounted() {
      window.addEventListener('keydown',this.keydown)
      window.addEventListener('keyup',this.keyup)
    },
  };
  </script>
  
  <style lang="less" scoped>
  /deep/.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected{
    background-color: rgb(255, 255, 255);
    color: black;
  }
  .tabRightClikBox{

  li{
    height:24px;
    line-height:24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
  }
  .ant-menu-item:not(:last-child){
    margin-bottom: 4px;
  }
}
  /deep/ .ant-table {
    .rowBackgroundColor {
      background: #c9c9c9 !important;
    }
  }
  </style>