<!-- 车间管理-飞针管理-部门过序 -->
<template>
  <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 12}" >
    <a-form-item label="流程卡号" style="display: block;position: relative">
      <a-input allowClear v-model="barcode"  type="password" @keyup.enter="keyupEnter" :autoFocus="autoFocus" style="color: rgba(255,255,255,1)" ref="inpt"/>
      <p style="margin: 0;position: absolute;left: 12px;top: 0;color:#000000">{{this.barcode}}</p>
    </a-form-item>

    <a-form-item label="过序数量">
      <a-input allowClear v-model='value' @change="countChange"/>
    </a-form-item>
  </a-form>
</template>
<script>
export default {
    name:'OverOrderInfo',
    props:['quantity','quantityCopy'],
  data() {
    return {
      barcode: "",
      autoFocus:true,
      value:''
    };
  },
  watch:{
    'quantity':{
      handler(val){
        this.value = val
        console.log('this.value:',this.value)
      }
      
    }
  },
  methods: {
    keyupEnter(){
        this.$emit('keyupEnter', this.barcode)
    },
    countChange(){
      this.$emit('quantityChange', this.value)
      // if(this.value<=this.quantityCopy+5){
      //   this.$emit('quantityChange', this.value)
      // }else{
      //   // this.value = this.quantityCopy
      //   this.$message.error('修改数量不能超过订单总数+5')
      // }
    },
    getFoucs(){
      this.$refs.inpt.focus();
    }
  },
};
</script>
<style scoped lang="less">
/deep/ .ant-form-item {
  .ant-form-item-control {
    span {
      display: block;
    }
  }
}
</style>