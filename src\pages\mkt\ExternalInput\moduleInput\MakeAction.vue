<template>
  <div class="active">
    <div class="box">
      <a-button type="primary" @click="TakeOrderClick" disabled> 取单 </a-button>
    </div>

    <div class="box">
      <a-button type="primary" @click="AddOrderClick"> 新增 </a-button>
    </div>

    <div class="box">
      <a-button type="primary" @click="MakeStartClick" disabled> 开始 </a-button>
    </div>

    <div class="box">
      <a-button type="primary" @click="modifyInfoClick" disabled> 完成 </a-button>
    </div>

    <div class="box">
      <a-button type="primary" @click="queryClick" d> 查询 </a-button>
    </div>

    <!-- <div class="box" >        
        <a-button type="primary" @click="uploadPCBFileClick" style='background: #ff9900;color: white;border-color: #ff9900;text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);box-shadow: 0 2px 0 rgb(0 0 0 / 5%);'  disabled>
        <img src="@/assets/img/upload.png" alt="" style="width: 16px;margin-right: 5px;">
          上传
        </a-button>
        <a-upload 
          accept=".rar,.zip"
          name="file"
          :multiple="false"
          :customRequest="customRequest"
          @change="handleChangeImg"
          :showUploadList="false"
          ref="fileRef"
          :before-upload="beforeUpload1"
          v-show="false"
        > 
        <a-button style="width: 80px;"><a-icon type="upload" /></a-button>
        </a-upload>
      </div>    -->

    <div class="box">
      <a-button type="primary" @click="openApp" disabled> 打开APP </a-button>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { uploadPCBFile } from "@/services/mkt/PrequalificationProduction.js";
import protocolCheck from "@/utils/protocolcheck";
export default {
  name: "MakeAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
  },
  data() {
    return {
      selectValue: "生成叠层",
      imgName: "",
      orderId: "",
    };
  },
  methods: {
    checkPermission,
    // 新增
    AddOrderClick() {
      this.$router.push({ path: "OrderEntry" });
    },
    // 取单
    TakeOrderClick() {
      this.$emit("TakeOrderClick");
    },
    // 开始
    MakeStartClick() {
      this.$emit("MakeStartClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 完成（审核通过）
    modifyInfoClick() {
      this.$emit("modifyInfoClick");
    },
    // 上传pcb文件
    uploadPCBFileClick() {
      this.$emit("uploadPCBFileClick");
    },
    // 打开APP
    openApp() {
      protocolCheck(
        "WebshellEmsCam:// 用户 密码 jbid",
        fail => {
          console.log("fail", fail);
          // 没有安装 弹窗显示 引导去下载
          // this.$message.error('未安装注册')
        },
        succ => {
          // 安装则直接打开
          console.log("succ", succ);
        }
      );
    },
    handleChangeImg(info) {
      this.imgName = `${info.file.name}`.substring(0, `${info.file.name}`.indexOf("."));
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;

        // console.log("str_esc:",str_esc)
        if (!isFileType) {
          _this.$message.error("只支持.rar或.zip格式文件");
          reject();
          // return isFileType
        } else {
          resolve();
        }
      });
    },
    customRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      // console.error(formData)
      uploadPCBFile(this.orderId, formData).then(res => {
        if (res.code) {
          this.$message.success("上传成功");
          this.$emit("getOrderList");
        } else {
          this.$message.error(res.message);
        }
      });
      // console.log(this.enterOrderForm.pdctno,this.enterOrderForm.tgzOssPath)
    },
    clickUpload(id) {
      this.orderId = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
  },
};
</script>

<style scoped lang="less">
.active {
  // width:762px;
  // display: flex;
  // float: right;
  // flex-wrap: wrap;
  // justify-content: space-around;
  // transition: all .2s;
  // -moz-transition: all .2s;
  // -webkit-transition: all .2s;
  // -o-transition: all .2s;
  // padding: 0 30px;
  height: 50px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    width: 14%;
    margin-top: 12px;
    // margin-right: 20px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .ant-btn-primary:hover {
      background-color: #ffb029 !important;
      border-color: #ffb029 !important;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        // background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
