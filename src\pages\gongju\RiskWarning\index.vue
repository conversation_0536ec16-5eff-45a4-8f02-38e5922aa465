<template>
  <div class="risk_warning">
    <div>
      <a-tabs :activeKey="activeKey" @tabClick="tabClick">
        <a-tab-pane key="1" tab="客户风险"> </a-tab-pane>
        <a-tab-pane key="2" tab="型号风险"> </a-tab-pane>
      </a-tabs>
      <div class="Content">
        <div class="leftContent">
          <left-table
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :pagination="pagination"
            :columns="activeKey == 1 ? columns1 : columns2"
            :Loading="Loading"
            :MainData="MainData"
            @getRiskData="getRiskData"
            ref="leftTable"
            class="leftstyle"
            @handleTableChange="handleTableChange"
          ></left-table>
        </div>
        <div class="rightContent">
          <risk-table
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :columns3="columns3"
            ref="rightTable"
            :riskLoading="riskLoading"
            :RiskData="RiskData"
            class="rightstyle"
          ></risk-table>
        </div>
      </div>
      <div class="footerAction">
        <footer-action @queryClick="queryClick" @lapse="lapse" @Modelrisk="Modelrisk"></footer-action>
      </div>
    </div>
    <!--新增型号风险-->
    <a-modal title="新增风险警告" :visible="RiskVisible" @ok="RiskHandleOk" @cancel="RiskVisible = false" :width="800" centered>
      <a-form-model :rules="addrules" :model="AddRisk" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" ref="ruleForm">
        <a-form-model-item label="工厂" prop="joinFactoryId">
          <a-select v-model="AddRisk.joinFactoryId" showSearch optionFilterProp="label" allowClear mode="multiple">
            <a-select-option v-for="(item, index) in Faclist" :key="index" :value="item.valueMember" :label="item.text">{{
              item.text
            }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="风险类型" prop="linkType">
          <a-radio-group v-model="AddRisk.linkType" @change="linkTypeChange">
            <a-radio :value="1">客户代码</a-radio>
            <a-radio :value="2">生产型号</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="客户代码" prop="custNo" v-if="AddRisk.linkType == 1">
          <a-select
            placeholder="请选择客户代码"
            v-model="AddRisk.custNo"
            :dropdownMatchSelectWidth="false"
            showSearch
            optionFilterProp="children"
            @popupScroll="handlePopupScroll"
            allowClear
            @change="supValue1"
            @search="supValue"
          >
            <a-select-option v-for="(item, index) in frontDataZSupplierf" :key="index" :value="item" :title="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="生产型号" prop="orderNo" v-if="AddRisk.linkType == 2">
          <a-input v-model="AddRisk.orderNo" placeholder="请输入生产型号" />
        </a-form-model-item>
        <a-form-model-item label="风险内容" prop="content">
          <a-textarea :auto-size="{ minRows: 4, maxRows: 6 }" v-model="AddRisk.content" placeholder="请输入风险内容" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!-- 查询弹窗 -->
    <a-modal
      title="订单查询"
      :visible="queryVisible"
      @cancel="reportHandleCancel"
      @ok="Confirmthequery"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 14 }">
        <a-form-item label="客户代码" v-show="activeKey == 1">
          <a-input v-model="queryData.custNo" placeholder="请输入客户代码" :autoFocus="true" />
        </a-form-item>
        <a-form-item label="生产型号" v-show="activeKey == 2">
          <a-input v-model="queryData.orderNo" placeholder="请输入生产型号" :autoFocus="true" />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 确认弹窗 -->
    <a-modal
      title="确认弹窗"
      :visible="ConfirmVisible"
      @cancel="reportHandleCancel"
      @ok="Confirm"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span>{{ prompt_information }}</span>
    </a-modal>
  </div>
</template>
<script>
const columns1 = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 60,
  },
  {
    title: "客户代码",
    align: "left",
    dataIndex: "custNo",
    ellipsis: true,
    width: 80,
  },
  {
    title: "客户名称",
    align: "left",
    dataIndex: "custName",
    ellipsis: true,
  },
];
const columns2 = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 60,
  },
  {
    title: "客户代码",
    align: "left",
    dataIndex: "custNo",
    ellipsis: true,
    width: 80,
  },
  {
    title: "生产型号",
    align: "left",
    dataIndex: "orderNo",
    ellipsis: true,
  },
];
const columns3 = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "风险内容",
    align: "left",
    dataIndex: "content",
    ellipsis: true,
    width: 300,
  },
  {
    title: "创建时间",
    align: "left",
    dataIndex: "createTime",
    ellipsis: true,
    width: 110,
  },
  {
    title: "创建人员",
    align: "left",
    dataIndex: "createUser",
    ellipsis: true,
    width: 70,
  },
  {
    title: "更新时间",
    align: "left",
    dataIndex: "lastModifiedTime",
    ellipsis: true,
    width: 110,
  },
  {
    title: "更新人员",
    align: "left",
    dataIndex: "lastModifiedUser",
    ellipsis: true,
    width: 70,
  },
  {
    title: "状态",
    align: "left",
    dataIndex: "status",
    customRender: (text, record, index) => (record.status == 0 ? "失效" : "有效"),
    ellipsis: true,
    width: 50,
  },
  {
    title: "工厂",
    align: "left",
    dataIndex: "joinFactoryName",
    ellipsis: true,
    width: 70,
  },
];
import { moduledropdownlist } from "@/services/mkt/orderInfo";
import Cookie from "js-cookie";
import { mktCustNo, mktcustnobyfAC } from "@/services/mkt/Inquiry.js";
import { mapState } from "vuex";
import { addRiskWarning } from "@/services/mkt/CustInfoNew";
import { riskwarningpagelist, riskwarning, updatestatus } from "@/services/gongju/RiskWarning.js";
import LeftTable from "@/pages/gongju/RiskWarning/module/LeftTable";
import RiskTable from "@/pages/gongju/RiskWarning/module/RiskTable";
import FooterAction from "@/pages/gongju/RiskWarning/module/FooterAction";
export default {
  name: "RiskWarning",
  components: {
    LeftTable,
    RiskTable,
    FooterAction,
  },
  data() {
    return {
      Faclist: [],
      prompt_information: "",
      ConfirmVisible: false,
      queryData: {},
      Loading: false,
      riskLoading: false,
      spinning: false,
      activeKey: "1",
      RiskData: [],
      MainData: [],
      columns1,
      columns2,
      columns3,
      queryVisible: false,
      isCtrlPressed: false,
      RiskVisible: false,
      addrules: {
        joinFactoryId: [{ required: true, message: "请选择工厂", trigger: "blur" }],
        linkType: [{ required: true, message: "请选择风险类型", trigger: "blur" }],
        content: [{ required: true, message: "请输入风险内容", trigger: "blur" }],
        custNo: [{ required: true, message: "请选择客户代码", trigger: "blur" }],
        orderNo: [{ required: true, message: "请输入生产型号", trigger: "blur" }],
      },
      AddRisk: {
        joinFactoryId: null,
        linkType: "",
        custNo: "",
        orderNo: "",
        content: "",
      },
      supList: [],
      frontDataZSupplierf: [], //客户代码
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        size: "",
        simple: false,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  created() {
    this.$nextTick(() => {
      this.handleResize();
      this.getOrderList();
      this.getSupplier();
    });
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    moduledropdownlist().then(res => {
      this.Faclist = res;
      this.Faclist.map(item => {
        item.valueMember = Number(item.valueMember);
      });
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  methods: {
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.toUpperCase().indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }
          this.frontDataZSupplierf = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let arr = [];
        let that = this;
        that.valueData = value.toUpperCase();
        arr = that.supList.filter(m => m.toUpperCase().indexOf(value.toUpperCase()) != -1);
        if (arr.length) {
          that.frontDataZSupplierf = arr.slice(0, 20);
        } else {
          that.frontDataZSupplierf = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplierf = this.supList.slice(0, 20);
      }
    },
    supValue1(value) {
      if (!this.formData.custNo) {
        this.supValue(undefined);
      }
    },
    getSupplier() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("mktCustNo"));
      var factory = this.user.factory;
      if (
        data &&
        token &&
        data.filter(item => {
          return item.factory == factory;
        }).length
      ) {
        for (let index = 0; index < data.length; index++) {
          if (data[index].token == token && data[index].factory == factory) {
            const element = data[index];
            this.supList = element.data;
            this.frontDataZSupplierf = element.data.slice(0, 20);
          }
        }
      } else {
        if (factory == 58 || factory == 59) {
          mktcustnobyfAC(factory).then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplierf = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          mktCustNo().then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplierf = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (JSON.stringify(that.supList) != "{}") {
                if (data == null) {
                  arr.push({ data: that.supList, token, factory });
                  localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
                } else {
                  data.push({ data: that.supList, token, factory });
                  localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
                }
              }
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
    },
    linkTypeChange() {
      this.AddRisk.orderNo = "";
      this.AddRisk.custNo = "";
      this.AddRisk.content = "";
    },
    Modelrisk() {
      this.RiskVisible = true;
      Object.keys(this.AddRisk).forEach(key => {
        this.AddRisk[key] = "";
      });
      this.AddRisk.linkType = 1;
      this.AddRisk.joinFactoryId = [Number(this.user.factoryId)];
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    lapse() {
      if (this.$refs.rightTable.selectedRowKeysArray.length == 0) {
        this.$message.error("请选择要失效的数据");
        return;
      }
      this.ConfirmVisible = true;
      this.prompt_information = "确认该警告失效吗?";
    },
    Confirm() {
      this.ConfirmVisible = false;
      let params = {
        status: 0,
        id: this.$refs.rightTable.risk_id,
      };
      updatestatus(params).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.getRiskData(this.$refs.leftTable.selectedRowsData);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Confirmthequery() {
      this.queryVisible = false;
      this.getOrderList(this.queryData);
    },
    reportHandleCancel() {
      this.queryVisible = false;
      this.ConfirmVisible = false;
    },
    queryClick() {
      this.queryData = {};
      this.queryVisible = true;
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.reportHandleCancel();
        this.queryClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.queryVisible) {
        this.Confirmthequery();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    RiskHandleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.RiskVisible = false;
          let params = {
            status: 1,
            ...this.AddRisk,
          };
          addRiskWarning(params).then(res => {
            if (res.code) {
              this.$message.success(res.message);
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          return false;
        }
      });
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    getOrderList(params) {
      let data = {
        pageIndex: this.pagination.current,
        pageSize: this.pagination.pageSize,
        ...params,
      };
      data.linkType = this.activeKey;
      this.Loading = true;
      riskwarningpagelist(data)
        .then(res => {
          if (res.code) {
            this.MainData = res.data.items;
            this.pagination.total = res.data.totalCount;
            this.RiskData = [];
            this.$refs.leftTable.selectedRowsData = {};
            this.$refs.rightTable.selectedRowsData = {};
            this.$refs.rightTable.selectedRowKeysArray = [];
            this.$refs.rightTable.risk_id = "";
            this.handleResize();
          }
        })
        .finally(() => {
          this.Loading = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.getOrderList(this.queryData);
    },
    getRiskData(val) {
      this.riskLoading = true;
      let params = {
        linkType: this.activeKey,
        custNo: val.custNo,
        orderNo: val.orderNo,
      };
      riskwarning(params)
        .then(res => {
          if (res.code) {
            this.RiskData = res.data;
            (this.$refs.rightTable.risk_id = ""), (this.$refs.rightTable.selectedRowKeysArray = []);
            this.$refs.rightTable.selectedRowsData = {};
            setTimeout(() => {
              this.handleResize();
            }, 0);
          }
        })
        .finally(() => {
          this.riskLoading = false;
        });
    },
    tabClick(key) {
      this.activeKey = key;
      this.queryData = {};
      this.pagination.pageSize = 20;
      this.pagination.current = 1;
      this.getOrderList();
    },
    handleResize() {
      var leftstyle;
      var rightstyle;
      var leftContent = document.getElementsByClassName("leftContent")[0];
      var rightContent = document.getElementsByClassName("rightContent")[0];
      leftstyle = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      rightstyle = document.getElementsByClassName("rightstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (leftstyle && this.MainData.length != 0) {
        leftstyle.style.height = window.innerHeight - 239 + "px";
      } else {
        leftstyle.style.height = 0;
      }
      leftContent.style.height = leftContent.style.height > 719 ? "719px" : window.innerHeight - 200 + "px";
      rightContent.style.height = rightContent.style.height > 719 ? "719px" : window.innerHeight - 200 + "px";
      if (rightstyle && this.RiskData.length != 0) {
        rightstyle.style.height = window.innerHeight - 239 + "px";
      } else {
        rightstyle.style.height = 0;
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = 3 * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
.risk_warning {
  background-color: white;
  /deep/.ant-table-content {
    border: 2px solid #e9e9f0;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-thead > tr > th {
    padding: 7px 3px !important;
    border-right: 1px solid #efefef;
    height: 33px;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 7px 3px !important;
    border-right: 1px solid #efefef;
    height: 33px;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin: 10px;
    position: relative;
    width: 300%;
  }
}
.Content {
  display: flex;
  width: 100%;
  .leftContent {
    width: 30%;
  }
  .rightContent {
    width: 70%;
  }
}
.footerAction {
  width: 100%;
  height: 48px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background: #ffffff;
  border-top: 0;
}
</style>
