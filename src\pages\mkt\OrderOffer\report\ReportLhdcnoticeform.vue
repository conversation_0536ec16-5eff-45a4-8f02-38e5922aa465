<!-- 联合多层生产通知单 -->
<template>
    <div class="pdfDom1" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
      <div id="lhdcnoticedom"  style="font-size: 12px;font-family:'宋体';padding: 25px;color: black;font-weight: bold;" >
        <div style="font-size: 26px;text-align: center;">深圳市联昇科技A栋13车间</div>
        <div style="font-size: 22px;text-align: center;">产品制作通知单</div>
        <div style="padding-top: 15px;">
            <table style="width: 100%;" class="onetable">
                <tr>
                    <td style="min-width: 150px;">客户:</td>                   
                    <td>{{LHDCnoticedata.custno}}</td>
                    <td>生产类型:</td>
                    <td>{{LHDCnoticedata.samepdctno}}</td>
                    <td>订单类型:</td>
                    <td>{{LHDCnoticedata.isnewpdctno}}</td>
                    <td>下单日期:</td>
                    <td style="min-width: 80px;">{{LHDCnoticedata.yqrkdate}}</td>
                </tr>
                <tr>
                    <td>生产编号:</td>                   
                    <td>{{LHDCnoticedata.pdctno}}</td>
                    <td>客户型号:</td>
                    <td colspan="3">{{LHDCnoticedata.custsty}}</td>
                    <td>生产交期:</td>
                    <td>{{LHDCnoticedata.indate}}</td>
                </tr>
                <tr>
                    <td>客户PO:</td>                   
                    <td>{{LHDCnoticedata.po}}</td>
                    <td>物料编码:</td>
                    <td>{{LHDCnoticedata.jgsj}}</td>
                    <td>物料编码2:</td>
                    <td>{{LHDCnoticedata.cno}}</td>
                    <td>返单编号:</td>
                    <td>{{LHDCnoticedata.bqya}}</td>
                </tr>
                <tr>
                    <td>订单数量:</td>                   
                    <td>{{LHDCnoticedata.quantity}}</td>
                    <td>交货面积(㎡):</td>
                    <td>{{LHDCnoticedata.orderarea}}</td>
                    <td>公共库存数:</td>
                    <td>{{LHDCnoticedata.jhfs}}</td>
                    <td>订单库存数:</td>
                    <td>{{LHDCnoticedata.zkcolor}}</td>
                </tr>
                <tr>
                    <td>表面处理/工艺参数:</td>                   
                    <td colspan="3">{{LHDCnoticedata.nospec}}</td>
                    <td>表面处理总面积(%):</td>
                    <td colspan="3">{{LHDCnoticedata.bzkColor}}</td>
                </tr>
                <tr>
                    <td>层数:</td>                   
                    <td>{{LHDCnoticedata.pbqty}}</td>
                    <td>板材:</td>
                    <td>{{LHDCnoticedata.bctp}}</td>
                    <td>板厚(mm):</td>
                    <td>{{LHDCnoticedata.cpthick}}</td>
                    <td>成品铜厚(oz):</td>
                    <td>{{LHDCnoticedata.tbthick}}</td>
                </tr>
                <tr>
                    <td>拼版:</td>                   
                    <td >{{LHDCnoticedata.bzfColor}}</td>
                    <td>Pcs尺寸(mm):</td>
                    <td>{{LHDCnoticedata.cpsize}}</td>
                    <td>set尺寸(mm):</td>
                    <td>{{LHDCnoticedata.delunit}}</td>
                    <td>尺寸公差(mm):</td>
                    <td>{{LHDCnoticedata.zkfx}}</td>
                </tr>
                <tr>
                    <td>阻焊:</td>                   
                    <td>{{LHDCnoticedata.solderColor}}</td>
                    <td>字符:</td>
                    <td>{{LHDCnoticedata.fontColor}}</td>
                    <td>外形要求:</td>
                    <td>{{LHDCnoticedata.formingTol}}</td>
                    <td>成型:</td>
                    <td>{{LHDCnoticedata.formingType}}</td>
                </tr>
                <tr>
                    <td>过孔工艺:</td>                   
                    <td>{{LHDCnoticedata.ggcl}}</td>
                    <td>特殊工艺:</td>
                    <td colspan="5">{{LHDCnoticedata.cxkind}}</td>
                </tr>
                <tr>
                    <td>标记:</td>                   
                    <td>{{LHDCnoticedata.freeboard}}</td>
                    <td>周期格式:</td>
                    <td>{{LHDCnoticedata.isopenetest}}</td>
                    <td>盲埋孔:</td>
                    <td>{{LHDCnoticedata.plate}}</td>
                    <td>内含文件款数:</td>
                    <td>{{LHDCnoticedata.kgcsj}}</td>
                </tr>
                <tr>
                    <td>其它工艺要求:</td>                   
                    <td>{{LHDCnoticedata.isopenMg}}</td>
                    <td>测试点数:</td>
                    <td>{{LHDCnoticedata.functionary}}</td>
                    <td>测试:</td>
                    <td>{{LHDCnoticedata.etest}}</td>
                    <td>共用测试架型号:</td>
                    <td>{{LHDCnoticedata.mdmktstatepepl}}</td>
                </tr>
                <tr>
                    <td>验收标准:</td>                   
                    <td>{{LHDCnoticedata.ipcLevel}}</td>
                    <td>环保要求:</td>
                    <td>{{LHDCnoticedata.hbyq}}</td>
                    <td>翘曲度:</td>
                    <td>{{LHDCnoticedata.warpage}}</td>
                    <td>标签要求:</td>
                    <td>{{LHDCnoticedata.bqya}}</td>
                </tr>
                <tr>
                    <td>最小线宽(mm):</td>                   
                    <td>{{LHDCnoticedata.lineW}}</td>
                    <td>最小线距(mm):</td>
                    <td>{{LHDCnoticedata.lineS}}</td>
                    <td>孔到线间距(mm):</td>
                    <td>{{LHDCnoticedata.holeCS}}</td>
                    <td>最小孔径(mm):</td>
                    <td>{{LHDCnoticedata.minVias}}</td>
                </tr>
                <tr>
                    <td>PCS孔数:</td>                   
                    <td>{{LHDCnoticedata.totalHole}}</td>
                    <td>平米孔数:</td>
                    <td>{{LHDCnoticedata.density}}</td>
                    <td>孔铜(um):</td>
                    <td>{{LHDCnoticedata.kongtong}}</td>
                    <td>导热系数:</td>
                    <td>{{LHDCnoticedata.thermal}}</td>
                </tr>
                <tr>
                    <td>出货报告:</td>                   
                    <td>{{LHDCnoticedata.needReport}}</td>
                    <td>出货附件:</td>
                    <td></td>
                    <td>包装要求:</td>
                    <td>{{LHDCnoticedata.packagRequire}}</td>
                    <td>难度类型:</td>
                    <td>{{LHDCnoticedata.cremark}}</td>
                </tr>
                <tr>
                    <td>工程备注:</td>                   
                    <td colspan="7" style="text-align: left;">{{LHDCnoticedata.remark}}</td>
                </tr>
                <tr>
                    <td>工程客户要求:</td>                   
                    <td colspan="7" style="text-align: left;">{{LHDCnoticedata.xhremark}}</td>
                </tr>
            </table>
            <div style="font-size: 14px;">
              <span>下单:<span style=" text-decoration: underline;padding-left: 10px;">{{LHDCnoticedata.keyin}}</span></span>
              <span style="padding-left: 15px;">流水号:<span style=" text-decoration: underline;padding-left: 10px;">{{LHDCnoticedata.mktno}}</span></span>
              <span style="padding-left: 15px;">打印时间:<span style=" text-decoration: underline;padding-left: 10px;">{{LHDCnoticedata.mdmktstatetime}}</span></span>
            </div>           
        </div>
      </div>
    </div>
  </template>
  <script>
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LHDCnoticedata','ttype'],    
    computed:{  },
    data() {
      return { 
        printObj1:{
            id: "lhdcnoticedom", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LHDCnoticedata.pdctno
        },
      getnoticePdf(){
        htmlToPdfa3('lhdcnoticedom',this.LHDCnoticedata.pdctno)  
      },
    },
  }
  </script>
  
  <style lang="less" scoped>
  .printstyle{
  position: absolute;
  top: 716px;
  right: 26px;
}
td{
  font-size: 14px;
  color: black;
}
.onetable{
  border-right: 1px solid black;
  border-bottom: 1px solid black;
  td{
       text-align: center;
       border-left: 1px solid black;
       border-top: 1px solid black;
  }
}
.m-t-10{
  /deep/.ant-col > label{
    color: red;
    font-weight: bold;
    font-size: 24px;
  }
  /deep/.ant-form-item-children{
    color: red;
    font-weight: bold;
    font-size: 24px;
  }
}
/deep/.ant-col-3{
  width: 11.1%;
}
/deep/.ant-col-21{
  width: 88.9%;
}
/deep/.ant-col-15{
  width: 65.2%;
}
/deep/.ant-col-9{
  width: 34.8%;
}
/deep/.ant-form-item{
  margin-bottom: 0;
}
/deep/.ant-form-item-label{
  border-bottom: 1px solid black;
  border-left: 1px solid black;
  padding-left: 7px;
  height: 32px;
  line-height: 32px;
  font-weight: bold;
}
/deep/.ant-form-item-control{
  border-bottom: 1px solid black;
  border-left: 1px solid black;
  padding-left: 7px;
  height: 32px;
  line-height: 32px;
  font-weight: bold;
  color: black;
}
/deep/.ant-form{
  border-top: 1px solid black;
  border-right: 1px solid black;
}
.pdfDom1{
    height: 650px;
    overflow: auto;
}
</style>

