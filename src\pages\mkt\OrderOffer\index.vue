<!-- 市场管理 - 订单报价主组件 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectMake" ref="showPanel">
      <div style="width: 100%; display: flex">
        <div class="leftContent" @contextmenu.stop="rightClick($event)" style="position: relative">
          <left-table-make
            :columns="columns1"
            :params1="params1"
            :dataSource="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'id'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            @getOrderDetail="getOrderDetail"
            @jigsawPuzzleClick="jigsawPuzzleClick"
            @previewClick="previewClick"
            @goDetail1="goDetail1"
            @getDetailInfo="getDetailInfo"
            @getmail="getmail"
            @getOrderList="getOrderList"
            @OperationLog="OperationLog"
            @Changecolumns="Changecolumns"
            @islock="islock"
            ref="orderTable"
            :class="orderListData.length ? 'min-table' : ''"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
          </left-table-make>
          <!-- <span v-if="this.pagination.total" class="totalclass">总计 {{ this.pagination.total }} 条</span> -->
          <!-- <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">        
            <a-menu-item @click="down0()">下载文件</a-menu-item> :scroll="{x:400, y:180}"          
          </a-menu> -->
        </div>
        <div class="rightContent">
          <div
            :style="{
              height: tableHeight - 378 + 'px',
              width: '100%',
              position: 'relative',
              overflow: 'hidden',
            }"
          >
            <div class="top">
              <a-table
                :columns="columns2"
                :dataSource="dataSource2"
                :loading="loading2"
                :scroll="{ y: topheighty }"
                rowKey="id"
                :pagination="false"
                :customRow="onClickRow2"
                :rowClassName="isRedRow2"
              >
                <template slot="num" slot-scope="text, record, index">
                  <div>{{ index + 1 }}</div>
                </template>
                <template slot="para4DelQty_" slot-scope="text, record, index">
                  <span v-if="index == dataSource2.length - 1">
                    <a-input
                      :title="record.para4DelQty_"
                      v-model="record.para4DelQty_"
                      ref="input"
                      @blur.stop="addInquiry(record)"
                      allowClear
                      @keyup.enter.native="$event.target.blur()"
                    ></a-input>
                  </span>
                  <span v-else>
                    <a-input
                      :title="record.para4DelQty_"
                      v-model="record.para4DelQty_"
                      v-if="row2.id == record.id"
                      @blur="numChange1(record, 'num')"
                      @keyup.enter.native="$event.target.blur()"
                      allowClear
                    >
                    </a-input>
                    <span v-else :title="record.para4DelQty_">{{ record.para4DelQty_ }}</span>
                  </span>
                </template>
                <template slot="pcsPrice_" slot-scope="text, record, index">
                  <span v-if="index != dataSource2.length - 1 && (orderfac == 58 || orderfac == 59 || orderfac == 38 || orderfac == 69)">
                    <a-input v-model="record.pcsPrice_" @blur="numChange1(record)" @keyup.enter.native="$event.target.blur()" allowClear> </a-input>
                  </span>
                  <span v-else :title="record.pcsPrice_">{{ record.pcsPrice_ }}</span>
                </template>
                <template slot="radio" slot-scope="text, record, index">
                  <span v-if="index == dataSource2.length - 1">
                    <a-radio v-model="record.isProduction" style="height: 26px" disabled> </a-radio>
                  </span>
                  <span v-else>
                    <a-radio :checked="record.isProduction" style="height: 26px" @click="CheckClick(record)"> </a-radio>
                  </span>
                </template>
                <template slot="para4UrgentDate_" slot-scope="text, record, index">
                  <span v-if="index == dataSource2.length - 1">
                    <span :title="record.para4UrgentDate_">{{ record.para4UrgentDate_ }}</span>
                  </span>
                  <span v-else>
                    <a-input
                      :title="record.para4UrgentDate_"
                      v-model="record.para4UrgentDate_"
                      @change="changePa(record)"
                      @blur="earlierdate(record)"
                      :class="record.jiaJiWarning ? 'gradient-border' : ''"
                      @keyup.enter.native="$event.target.blur()"
                      allowClear
                    >
                    </a-input>
                  </span>
                </template>
                <template slot="para4Delivery_" slot-scope="text, record, index">
                  <span v-if="index == dataSource2.length - 1">
                    <span :title="record.para4Delivery_">{{ record.para4Delivery_ }}</span>
                  </span>
                  <span v-else>
                    <a-date-picker
                      :class="record.numWarning ? 'gradient-border' : ''"
                      format="YYYY-MM-DD"
                      v-model="record.para4Delivery_"
                      :allowClear="false"
                      @blur="$event.target.blur()"
                      @change="onChange1"
                    />
                  </span>
                </template>
                <template slot="para4Area_" slot-scope="text, record">
                  <span :title="record.para4Area_" :class="record.areaWarning ? 'gradient-border' : ''">{{ record.para4Area_ }}</span>
                </template>
                <template slot="para4IntDelivery_" slot-scope="text, record">
                  <a-tooltip placement="bottom" overlayClassName="memo-tooltip">
                    <template #title v-if="Delivery">
                      <div style="white-space: pre-line; font-size: 12px; line-height: 3ch">
                        {{ Delivery }}
                      </div>
                    </template>
                    <div style="color: #428bac" @mouseover="getDelivery(record.dayCaption || [])">
                      {{ record.para4IntDelivery_ }}
                    </div>
                  </a-tooltip>
                </template>
                <template slot="action" slot-scope="text, record">
                  <!-- <a-tooltip title="修改数量">
                <a-icon  style='color:rgb(66, 139, 202);margin-left:5px;' type="edit" @click="numberClick(record)"> </a-icon>
              </a-tooltip>
              <a-tooltip title="设置下单">
                <a-icon  style='color:rgb(66, 139, 202);margin-left:5px;' type="check-circle" @click="CheckClick(record)"> </a-icon>
              </a-tooltip>  -->
                  <span style="display: flex">
                    <a-tooltip title="交期计算" v-if="!PriceFlag && !$refs.action.disfinish">
                      <a-icon style="color: rgb(66, 139, 202); margin-left: 5px" type="carry-out" @click="deliverycalculation1(record)"> </a-icon>
                    </a-tooltip>
                    <a-tooltip title="删除多套价格">
                      <a-icon style="color: rgb(66, 139, 202); margin-left: 5px" type="close-circle" @click="actionClick(record)"> </a-icon>
                    </a-tooltip>
                  </span>
                </template>
              </a-table>
            </div>
          </div>
          <div id="touchmove" style="width: 100%; height: 3px; background: #ababab; cursor: s-resize"></div>
          <div
            :style="{
              height: innerHeight - tableHeight - 42 + 'px',
              width: '100%',
            }"
            class="footer"
          >
            <div class="centerTable">
              <a-collapse :activeKey="activeKey" accordion v-if="$refs.orderTable && $refs.orderTable.selectedRowsData.showCB == 1">
                <a-collapse-panel key="0" header="销售价">
                  <a-table
                    :columns="columns3"
                    :dataSource="fildata(dataSource3, '0')"
                    :loading="loading3"
                    :rowKey="
                      (record, index) => {
                        return index;
                      }
                    "
                    :scroll="{ y: heighty }"
                    :pagination="false"
                    :customRow="onClickRow3"
                    :rowClassName="isRedRow3"
                    ref="centertable"
                  >
                    <template slot="isChooseEdit" slot-scope="text, record">
                      <a-checkbox v-model="record.isChooseEdit" disabled> </a-checkbox>
                    </template>
                    <template slot="upOrDownPrice" slot-scope="text, record">
                      <a-input
                        v-model="record.upOrDownPrice"
                        v-if="PriceFlag && record.isCollectEdit"
                        style="height: 26px; padding: 0; width: 51px"
                        @change="priceChange()"
                        @blur="PriceChange(record)"
                        @keyup.enter.native="$event.target.blur()"
                      >
                      </a-input>
                      <span :title="record.upOrDownPrice" v-else>{{ record.upOrDownPrice }}</span>
                    </template>
                    <template slot="actualPrice" slot-scope="record">
                      <a-input
                        v-model="record.actualPrice"
                        v-if="PriceFlag && record.isChooseEdit"
                        style="height: 26px; padding: 0; width: 51px"
                        @change="priceChange1(record)"
                        @blur="PriceChange1(record)"
                        @keyup.enter.native="$event.target.blur()"
                      >
                      </a-input>
                      <span :title="record.actualPrice" v-else>{{ record.actualPrice }}</span>
                    </template>
                  </a-table>
                </a-collapse-panel>
                <a-collapse-panel key="1" header="成本价">
                  <a-table
                    :columns="columns3"
                    :dataSource="fildata(dataSource3, '-1')"
                    :loading="loading3"
                    :rowKey="
                      (record, index) => {
                        return index;
                      }
                    "
                    :scroll="{ y: heighty }"
                    :pagination="false"
                    :customRow="onClickRow3"
                    :rowClassName="isRedRow3"
                    ref="centertable"
                  >
                    <template slot="isChooseEdit" slot-scope="text, record">
                      <a-checkbox v-model="record.isChooseEdit" disabled> </a-checkbox>
                    </template>
                    <template slot="upOrDownPrice" slot-scope="text, record">
                      <a-input
                        v-model="record.upOrDownPrice"
                        v-if="PriceFlag && record.isCollectEdit"
                        style="height: 26px; padding: 0; width: 51px"
                        @change="priceChange()"
                        @blur="PriceChange(record)"
                        @keyup.enter.native="$event.target.blur()"
                      >
                      </a-input>
                      <span :title="record.upOrDownPrice" v-else>{{ record.upOrDownPrice }}</span>
                    </template>
                    <template slot="actualPrice" slot-scope="record">
                      <a-input
                        v-model="record.actualPrice"
                        v-if="PriceFlag && record.isChooseEdit"
                        style="height: 26px; padding: 0; width: 51px"
                        @change="priceChange1(record)"
                        @blur="PriceChange1(record)"
                        @keyup.enter.native="$event.target.blur()"
                      >
                      </a-input>
                      <span :title="record.actualPrice" v-else>{{ record.actualPrice }}</span>
                    </template>
                  </a-table>
                </a-collapse-panel>
              </a-collapse>
              <a-table
                v-else
                :columns="columns3"
                :dataSource="dataSource3"
                :loading="loading3"
                :rowKey="
                  (record, index) => {
                    return index;
                  }
                "
                :scroll="{ y: heighty + 40 }"
                :pagination="false"
                :customRow="onClickRow3"
                :rowClassName="isRedRow3"
                ref="centertable"
              >
                <template slot="isChooseEdit" slot-scope="text, record">
                  <a-checkbox v-model="record.isChooseEdit" disabled> </a-checkbox>
                </template>
                <template slot="upOrDownPrice" slot-scope="text, record">
                  <a-input
                    v-model="record.upOrDownPrice"
                    v-if="PriceFlag && record.isCollectEdit"
                    style="height: 26px; padding: 0; width: 51px"
                    @change="priceChange()"
                    @blur="PriceChange(record)"
                    @keyup.enter.native="$event.target.blur()"
                  >
                  </a-input>
                  <span :title="record.upOrDownPrice" v-else>{{ record.upOrDownPrice }}</span>
                </template>
                <template slot="actualPrice" slot-scope="record">
                  <a-input
                    v-model="record.actualPrice"
                    v-if="PriceFlag && record.isChooseEdit"
                    style="height: 26px; padding: 0; width: 51px"
                    @change="priceChange1(record)"
                    @blur="PriceChange1(record)"
                    @keyup.enter.native="$event.target.blur()"
                  >
                  </a-input>
                  <span :title="record.actualPrice" v-else>{{ record.actualPrice }}</span>
                </template>
              </a-table>
            </div>
          </div>
          <div style="width: 100%; height: 2px; background: #efefef"></div>
          <div class="bto">
            <a-table
              :columns="columns4"
              :dataSource="dataSource4"
              :loading="loading4"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :scroll="{
                y: 740 - (innerHeight - tableHeight - 42) - (tableHeight - 378),
              }"
              :pagination="false"
              :customRow="onClickRow4"
              :rowClassName="isRedRow4"
              :class="dataSource4.length ? 'min-table4' : ''"
            >
              <template slot="upOrDownPrice" slot-scope="text, record">
                <a-input
                  v-model="record.upOrDownPrice"
                  v-if="PriceFlag && record.isDetailEdit"
                  style="height: 26px; padding: 0; width: 51px"
                  @blur="PriceChange(record, index)"
                  @keyup.enter.native="$event.target.blur()"
                >
                </a-input>
                <span v-else :title="record.upOrDownPrice">{{ record.upOrDownPrice }}</span>
              </template>
            </a-table>
          </div>
        </div>
      </div>
      <div class="footerAction">
        <make-action
          ref="action"
          :assignLoading="assignLoading"
          @onlineecn="onlineecn"
          @fileswereadded="fileswereadded"
          @OrderSplitting="OrderSplitting"
          @ContractVerification="ContractVerification"
          @Modelrisk="Modelrisk"
          @MakeStartClick="MakeStartClick"
          @uploadPCBFileClick="uploadPCBFileClick"
          @customRequest="customRequest"
          @getOrderList="getOrderList"
          @modifyInfoClick="modifyInfoClick"
          @Releasewarning="Releasewarning"
          @valuationClick="valuationClick"
          @sendcontractemail="sendcontractemail"
          @quotationClick="quotationClick"
          @quotationcontract="quotationcontract"
          :buttonload="buttonload"
          @verifyBackClick="verifyBackClick"
          @ChangePrice="ChangePrice"
          :PriceFlag="PriceFlag"
          @ChangePriceok="ChangePriceok"
          @finishClick="finishClick"
          @queryClick="queryClick"
          @marketmodification="marketmodification"
          @addorder="addorder"
          @addareturnorder="addareturnorder"
          @systemparameter="systemparameter"
          @changeorder="changeorder"
          @orderlock="orderlock"
          @ordercancellation="ordercancellation"
          @OrderDelete="OrderDelete"
          @Ordersuspension="Ordersuspension"
          @ReviewSheet="ReviewSheet"
          @OrderRecovery="OrderRecovery"
          @orderaccess="orderaccess"
          @costanalysis="costanalysis"
          @Mergerofreturnorders="Mergerofreturnorders"
          @Quantitysplitting="Quantitysplitting"
          @ModifyPO="ModifyPO"
          :params1="params1"
        />
      </div>
      <!--修改PO号-->
      <a-modal
        title="修改PO号"
        :visible="PoVisible"
        @ok="dePoHandleOk"
        @cancel="PoVisible = false"
        :width="500"
        :maskClosable="false"
        centered
        destroyOnClose
      >
        <a-form-model-item label="*PO" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }" class="required">
          <a-input v-model="cust_Po" :autoFocus="true" allowClear />
        </a-form-model-item>
      </a-modal>
      <a-modal
        title="新增风险警告"
        :visible="RiskVisible"
        @ok="RiskHandleOk"
        @cancel="RiskVisible = false"
        :width="800"
        :maskClosable="false"
        centered
        destroyOnClose
      >
        <div style="display: flex">
          <span style="width: 80px; color: red">*型号风险:</span
          ><a-textarea :auto-size="{ minRows: 4, maxRows: 6 }" :auto-focus="true" v-model="RiskContent" />
        </div>
      </a-modal>
      <!-- 合同核查 -->
      <a-modal
        title="合同核查"
        :visible="verificationVisible"
        @cancel="verificationVisible = false"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        class="verificationModal"
        :maskClosable="false"
        :width="1800"
        centered
      >
        <a-table
          class="verification"
          :columns="checkcolumns"
          :dataSource="verificationData"
          :pagination="false"
          :scroll="{ y: 541, x: 1700 }"
          :rowKey="(record, index) => `${index + 1}`"
        >
          <template slot="num" slot-scope="text, record, index">
            <div v-if="index < verificationData.length - 1">{{ (index + 1).toString() }}</div>
            <div v-else>合计</div>
          </template>
        </a-table>
        <template slot="footer">
          <a-button key="back" type="primary" @click="verificationVisible = false">关闭</a-button>
          <a-button key="back" type="primary" @click="exportExcel()">导出EXCEL</a-button>
        </template>
      </a-modal>
      <!-- 订单拆分 -->
      <a-modal
        title="订单拆分"
        :visible="orderVisible"
        @cancel="reportHandleCancel"
        @ok="handleOkorder"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form-model ref="orderForm" :model="orderForm" :rules="rules">
          <a-form-model-item label="数量" prop="splitNum" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-input v-model="orderForm.splitNum" allowClear :autoFocus="true" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal title="拼版图" :visible="makeupVisible" :footer="null" @cancel="handleCancel">
        <makeup-pic ref="makeup"></makeup-pic>
      </a-modal>
      <!--报表弹窗   :dialog-style="{ top: '20px' }"-->
      <a-modal
        title="报价表单"
        :visible="modelVisible"
        @cancel="reportHandleCancelRep"
        @ok="handleOkRep"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        :width="1050"
      >
        <report-info :reportData="reportData" ref="report" v-if="key == '1'" />
        <report-info1 :reportData="reportData" ref="report" v-if="key == '2'" />
      </a-modal>
      <!-- 红马销售合同-->
      <a-modal
        title="销售合同"
        :visible="hmVisible"
        @cancel="reportHandleCancelRep"
        @ok="handleOkhm"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-infohm
          :ttype="'EMS | 订单报价'"
          :hmsalesdata="hmsalesdata"
          :joinFactoryId="joinFactoryId"
          ref="reporthm"
          :salescustno="salescustno"
          :ContractNoSech="ContractNoSech"
          @hmform="hmform"
        />
      </a-modal>
      <!-- 明天高新销售合同-->
      <a-modal
        title="销售合同"
        :visible="mtgxVisible"
        @cancel="reportHandleCancelRep"
        @ok="handleOkmtgx"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-infomtgx
          :ttype="'EMS | 订单报价'"
          :mtgxsalesdata="mtgxsalesdata"
          :joinFactoryId="joinFactoryId"
          ref="reportmtgx"
          :salescustno="salescustno"
          :ContractNoSech="ContractNoSech"
          @mtgxform="mtgxform"
        />
      </a-modal>
      <!--雅信达 联合多层销售合同-->
      <a-modal
        title="销售合同"
        :visible="yxdVisible"
        @cancel="reportHandleCancelRep"
        @ok="handleOkyxd"
        ok-text="下载"
        destroyOnClose
        centered
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <template slot="footer">
          <a-button @click="reportHandleCancelRep">取消</a-button>
          <a-button type="primary" @click="handleOkyxd">下载</a-button>
        </template>
        <report-infoyxd
          :ttype="'EMS | 订单报价'"
          :yxddata="yxddata"
          :joinFactoryId="joinFactoryId"
          ref="reportyxd"
          :salescustno="salescustno"
          @getOrderList="getOrderList"
          :ContractNoSech="ContractNoSech"
          @YXDform="YXDform"
        />
      </a-modal>
      <!-- 精焯销售合同 -->
      <a-modal
        title="销售合同"
        :visible="JZsalesvisible"
        @cancel="reportHandleCancelRep"
        @ok="salescontractdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-jzsalescontract ref="jzsalescontract" :JZsalesdata="JZsalesdata" :ttype="'EMS | 订单报价'" />
      </a-modal>
      <!-- 奔强销售合同 -->
      <a-modal
        title="销售合同"
        :visible="BQsalesvisible"
        @cancel="reportHandleCancelRep"
        @ok="bqsalescontractdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        centered
        class="formclass"
        :width="1250"
      >
        <report-bqsalescontract
          ref="bqsalescontract"
          :ttype="'EMS | 订单报价'"
          :BQsalesdata="BQsalesdata"
          @BQsalescontract="BQsalescontract"
          :salescustno="salescustno"
        />
      </a-modal>
      <!-- 诚瑞销售合同 -->
      <a-modal
        title="销售合同"
        :visible="CRsalesvisible"
        @cancel="CRsalesvisible = false"
        @ok="crsalescontractdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        centered
        class="formclass"
        :width="1300"
      >
        <report-crsalescontract
          ref="crsalescontract"
          :ttype="'EMS | 订单报价'"
          :CRsalesdata="CRsalesdata"
          @CRsalescontract="CRsalescontract"
          :salescustno="salescustno"
          :ContractNoSech="ContractNoSech"
        />
      </a-modal>
      <!-- 本川销售合同 -->
      <a-modal
        title="销售合同"
        :visible="BCsalesvisible"
        @cancel="BCsalesvisible = false"
        @ok="bcsalescontractdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        centered
        class="formclass"
        :width="1600"
      >
        <report-bcsalescontract
          ref="bcsalescontract"
          :ttype="'EMS | 订单报价'"
          :BCsalesdata="BCsalesdata"
          @BCsalescontract="BCsalescontract"
          :salescustno="salescustno"
          :ContractNoSech="ContractNoSech"
        />
      </a-modal>
      <!-- 龙腾销售合同 -->
      <a-modal
        title="销售合同"
        :visible="LTsalesvisible"
        @cancel="LTsalesvisible = false"
        @ok="ltsalescontractdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        centered
        class="formclass"
        :width="1250"
      >
        <report-ltsalescontract
          ref="ltsalescontract"
          :ttype="'EMS | 订单报价'"
          :LTsalesdata="LTsalesdata"
          @LTsalescontract="LTsalescontract"
          :salescustno="salescustno"
          :ContractNoSech="ContractNoSech"
        />
      </a-modal>
      <a-modal
        title="评审单"
        :visible="JZreviewvisible"
        @cancel="reportHandleCancelRep"
        @ok="reviewdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="900"
      >
        <report-jzreviewform ref="jzreviewform" :JZreviewdata="JZreviewdata" />
      </a-modal>
      <a-modal
        title="报价表单"
        :visible="JZreportvisible"
        @cancel="reportHandleCancelRep"
        @ok="reportdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="widthy1"
      >
        <report-jzreportform ref="jzreportform" :selectlength="selectlength" :JZreportdata="JZreportdata" />
      </a-modal>
      <!--诚瑞报价表单-->
      <a-modal
        title="报价表单"
        :visible="CRreportvisible"
        @cancel="reportHandleCancelRep"
        @ok="crreportdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1200"
      >
        <report-crreportform ref="crreportform" :CRreportdata="CRreportdata" />
      </a-modal>
      <!--雅信达报价表单-->
      <a-modal
        title="报价表单"
        :visible="YXDreportvisible"
        @cancel="reportHandleCancelRep"
        @ok="yxdreportdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-yxdreportform1098 ref="yxdreportform1098" :YXDreportdata="YXDreportdata" :ttype="'EMS | 订单报价'" v-if="salescustno == '1098'" />
        <report-yxdreportform1151
          ref="yxdreportform1151"
          :YXDreportdata="YXDreportdata"
          :ttype="'EMS | 订单报价'"
          v-else-if="salescustno == '1151' || salescustno == '1151A'"
        />
        <report-yxdreportform1508
          ref="yxdreportform1508"
          :YXDreportdata="YXDreportdata"
          :ttype="'EMS | 订单报价'"
          v-else-if="salescustno == '1508' || salescustno == '1508A'"
        />
        <report-yxdreportform1083
          ref="yxdreportform1083"
          :YXDreportdata="YXDreportdata"
          :ttype="'EMS | 订单报价'"
          v-else-if="salescustno == '1083'"
        />
        <report-yxdreportform ref="yxdreportform" :YXDreportdata="YXDreportdata" :ttype="'EMS | 订单报价'" :salescustno="salescustno" v-else />
      </a-modal>
      <!--奔强报价表单-->
      <a-modal
        title="报价表单"
        :visible="BQreportvisible"
        @cancel="reportHandleCancelRep"
        @ok="bqreportdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-bqreportform ref="bqreportform" :ttype="'EMS | 订单报价'" :Bqreportdata="Bqreportdata" :bqreport_type="bqreport_type" />
      </a-modal>
      <!--明天高新报价表单-->
      <a-modal
        title="报价表单"
        :visible="Mtgxreportvisible"
        @cancel="reportHandleCancelRep"
        @ok="mtgxreportdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1650"
      >
        <report-mtgxreportform ref="mtgxreportform" :ttype="'EMS | 订单报价'" :Mtgxreportdata="Mtgxreportdata" />
      </a-modal>
      <!--本川报价表单-->
      <a-modal
        title="报价表单"
        :visible="Bcreportvisible"
        @cancel="reportHandleCancelRep"
        @ok="bcreportdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1650"
      >
        <report-bcreportformnx ref="bcreportformnx" :ttype="'EMS | 订单报价'" :Bcreportdata="Bcreportdata" v-if="bcformType == 'nx'" />
        <report-bcreportform0184 ref="bcreportform0184" :ttype="'EMS | 订单报价'" :Bcreportdata="Bcreportdata" v-if="bcformType == '0184'" />
        <report-bcreportformwx ref="bcreportformwx" :ttype="'EMS | 订单报价'" :Bcreportdata="Bcreportdata" v-if="bcformType == 'wx'" />
      </a-modal>
      <!--龙腾报价表单-->
      <a-modal
        title="报价表单"
        :visible="LTreport"
        @cancel="LTreport = false"
        @ok="ltreportdown(formtypes)"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="
          formtypes == 'ltreportform0972'
            ? 1800
            : formtypes == 'ltreportform0477'
            ? 1200
            : formtypes == 'ltreportform0849' || formtypes == 'ltreportform1220' || formtypes == 'ltreportform0100' || formtypes == 'ltreportform1244'
            ? 1650
            : 1250
        "
      >
        <report-ltdefaultreport ref="ltdefaultreport" :LTreportdata="LTreportdata" :ttype="'EMS | 订单报价'" v-if="formtypes == 'ltdefaultreport'" />
        <report-ltreportform0072
          ref="ltreportform0072"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform0072'"
        />
        <report-ltreportform1034
          ref="ltreportform1034"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform1034'"
        />
        <report-ltreportform1099
          ref="ltreportform1099"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform1099'"
        />
        <report-ltreportform0271
          ref="ltreportform0271"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform0271'"
        />
        <report-ltreportform0972
          ref="ltreportform0972"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform0972'"
        />
        <report-ltreportform0477
          ref="ltreportform0477"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform0477'"
        />
        <report-ltreportform1170
          ref="ltreportform1170"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform1170'"
        />
        <report-ltreportform1206
          ref="ltreportform1206"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform1206'"
        />
        <report-ltreportform0849
          ref="ltreportform0849"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform0849'"
        />
        <report-ltreportform1220
          ref="ltreportform1220"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform1220'"
        />
        <report-ltreportform1032
          ref="ltreportform1032"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform1032'"
        />
        <report-ltreportform0100
          ref="ltreportform0100"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform0100'"
        />
        <report-ltreportform0041
          ref="ltreportform0041"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform0041'"
        />
        <report-ltreportform1244
          ref="ltreportform1244"
          :LTreportdata="LTreportdata"
          :ttype="'EMS | 订单报价'"
          v-if="formtypes == 'ltreportform1244'"
        />
      </a-modal>
      <!-- 订单锁定弹窗 -->
      <a-modal title="锁定原因" :visible="lockdataVisible" destroyOnClose @cancel="reportHandleCancel" :maskClosable="false" :width="800" centered>
        <div class="projectackend">
          <a-table
            :columns="lockcolumns"
            :dataSource="lockdata"
            :pagination="false"
            :scroll="{ y: 541 }"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
          >
          </a-table>
        </div>
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">取消</a-button>
        </template>
      </a-modal>
      <a-modal
        title="核查PO"
        :visible="offlinedataVisible"
        @cancel="offlinecancel"
        @ok="orderfinish(0)"
        ok-text="继续"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="padding-left: 20px">{{ mmessage }}</span>
      </a-modal>
      <!-- 操作日志弹窗 -->
      <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose @cancel="reportHandleCancel" :maskClosable="false" :width="800" centered>
        <div class="projectackend">
          <a-table
            :columns="laborcolumns"
            :dataSource="labordata"
            :pagination="false"
            :scroll="{ y: 541 }"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
          >
            <template slot="addresscontent" slot-scope="record">
              <span v-if="record.content.indexOf('地址') == -1">{{ record.content }}</span>
              <span v-else>
                <span>{{ record.content.split("地址：")[0] }}地址：</span>
                <span @click.stop="ContractDownload1(record)" style="color: rgb(68, 146, 235)">{{ record.content.split("地址：")[1] }}</span>
              </span>
            </template>
          </a-table>
        </div>
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">取消</a-button>
        </template>
      </a-modal>
      <!-- 市场修改 -->
      <a-modal
        title="市场修改"
        :visible="marketdataVisible"
        @cancel="reportHandleCancel"
        @ok="markethandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="地址" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.address" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="价格" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.price" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="交期" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.deliverydate" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="数量" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.num" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="PO" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.PO" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="预审参数" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.parameters" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="文件替换" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.FileReplacement" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <query-info ref="queryInfo" :Quoterlist="Quoterlist" />
      </a-modal>
      <!--数量拆分-->
      <a-modal
        title="数量拆分"
        :visible="splitVisible"
        @cancel="reportHandleCancel"
        @ok="Splitconfirmation"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form-model-item label="数量拆分:" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
          <a-input :auto-focus="true" v-model="split_num" />
        </a-form-model-item>
        <span style="color: #ff9900; font-size: 12px; margin-left: 90px">*请输入要拆分的数量，剩余数量将自动调整。</span>
      </a-modal>
      <!-- 订单锁定弹窗 -->
      <a-modal
        title="订单锁定确认"
        :visible="ddataVisible"
        @cancel="reportHandleCancel"
        @ok="hhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <lock-info ref="lockinfo" :isLock="isLock" :lockCause="lockCause" />
      </a-modal>
      <a-modal title="指示检查信息" :visible="dataVisible2" @cancel="reportHandleCancel2" destroyOnClose centered :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back" @click="reportHandleCancel2">取消</a-button>
          <a-button key="back1" type="primary" v-if="check" @click="continueClick">继续</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
        </div>
      </a-modal>
      <!-- 风险警告提示 -->
      <a-modal title="风险警告提示" :visible="Riskmodal" @cancel="Riskmodal = false" destroyOnClose :maskClosable="false" :width="600" centered>
        <div style="white-space: pre-line">{{ risktext }}</div>
        <template slot="footer">
          <a-button key="back" @click="Riskmodal = false">取消</a-button>
        </template>
      </a-modal>
      <!-- 系统参数弹窗 -->
      <a-modal
        title="汇率更改"
        :visible="systemdataVisible"
        @cancel="reportHandleCancel"
        @ok="systemhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span v-for="(item, index) in exchangerate" :key="index">
          <a-form-model-item :label="item.names_" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
            <a-input v-model="item.rate_" />
          </a-form-model-item>
        </span>
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        :title="
          type == 'report' ? '确认报价单类型' : type == 'newlyadded' ? '返单新增' : type == '7' && joinFactoryId == 22 ? '确认流程' : '确认弹窗'
        "
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="DehandleOk3"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="widthy"
        centered
        :confirmLoading="fdloading"
      >
        <span v-if="type == 'report'">
          <a-radio-group v-model="value">
            <a-radio :value="1">中文</a-radio>
            <a-radio :value="2">英文</a-radio>
          </a-radio-group>
        </span>
        <div v-else-if="type == 'newlyadded'" class="newlyaddedstyle" ref="SelectBox">
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="客户代码" :label-col="{ span: 8 }" :wrapper-col="{ span: 12 }">
                <a-select placeholder="请选择客户代码" v-model="returncustno" showSearch optionFilterProp="children" allowClear>
                  <a-select-option v-for="(item, index) in frontDataZSupplier1" :key="index" :value="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <div v-else-if="type == 'ordercancel'">
          <a-form-model-item label="*取消原因" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="required">
            <a-textarea v-model="CancelCause" placeholder="请输入取消原因" :autoFocus="true" :auto-size="{ minRows: 3, maxRows: 8 }"></a-textarea>
          </a-form-model-item>
        </div>
        <div v-else-if="type == 'orderpause'">
          <a-form-model-item label="*暂停原因" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="required">
            <a-textarea v-model="PauseCause" placeholder="请输入暂停原因" :autoFocus="true" :auto-size="{ minRows: 3, maxRows: 8 }"></a-textarea>
          </a-form-model-item>
        </div>

        <div v-else-if="type == '7' && joinFactoryId == 22">
          <a-radio-group v-model="OnLineOrRecordEcn" style="display: flex; justify-content: space-around">
            <a-radio :value="1">在线订单</a-radio>
            <a-radio :value="2">建档信息</a-radio>
          </a-radio-group>
        </div>
        <div v-else-if="type == '7' && (joinFactoryId == 58 || joinFactoryId == 59)">
          <a-form-model-item label="不升级版本" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }">
            <a-checkbox v-model="Upgradeversion" />
          </a-form-model-item>
        </div>
        <span v-else>
          <span v-if="type != '5' && type != 'orderdel'" style="font-size: 16px; font-weight: 500">【{{ orderno }}】</span>
          <span style="font-size: 16px; font-weight: 500">{{ message }}</span>
        </span>
      </a-modal>
      <!-- 返单信息弹窗 -->
      <a-modal
        :title="returOrderNo + '--返单信息,【' + returCust + '】--客户型号'"
        :visible="dataVisible8"
        @cancel="reportHandleCancel"
        @ok="handleOk8"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="800"
        centered
      >
        <retur-info :val="val" ref="returInfo" :selectOption="selectOption"></retur-info>
      </a-modal>
      <!-- 返单完成核查 -->
      <a-modal
        title="返单核查 "
        :visible="checkvisible"
        @cancel="reportHandleCancel1"
        @ok="checkhandleOk"
        ok-text="继续"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="color: red"> {{ Returnverification }}</span>
      </a-modal>
      <!-- 返单更改弹窗 -->
      <a-modal
        :title="returOrderNo + '--NOPE更改返单信息,【' + returCust + '】--客户型号'"
        :visible="dataVisible9"
        @cancel="reportHandleCancel"
        @ok="handleOk9"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="900"
        :confirmLoading="fdloading"
        centered
      >
        <modify-info :redata="redata" ref="modifyinfo"></modify-info>
      </a-modal>
      <vue-draggable-resizable
        :w="width"
        :h="height"
        :x="410"
        :y="0"
        :min-width="904"
        :min-height="700"
        :max-width="904"
        :max-height="750"
        :parent="true"
        class-name="dragging1"
        @dragging="onDrag"
        @resizing="onResize"
      >
        <div id="modal-container"></div>
      </vue-draggable-resizable>
      <vue-draggable-resizable
        :w="705"
        :h="700"
        :x="410"
        :y="0"
        :min-width="705"
        :min-height="700"
        :max-width="705"
        :max-height="750"
        :parent="true"
        class-name="dragging2"
        @dragging="onDrag1"
        @resizing="onResize1"
      >
        <div id="modal-container1"></div>
      </vue-draggable-resizable>
      <a-modal
        title="预审信息"
        :visible="PredataVisible"
        :closable="false"
        destroyOnClose
        :maskClosable="false"
        :force-render="true"
        :mask="false"
        :getContainer="getModalContainer"
        ref="preform"
        :width="900"
        centered
        class="yushen"
      >
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">关闭</a-button>
        </template>
        <prequalification-info ref="editForm" v-if="JSON.stringify(showData) != '{}'" :showData="showData"></prequalification-info>
      </a-modal>
      <a-modal
        title="邮件信息"
        :visible="MaildataVisible"
        :closable="false"
        destroyOnClose
        :maskClosable="false"
        :force-render="true"
        :mask="false"
        :width="700"
        centered
        :getContainer="getModalContainer1"
        class="mailcalss"
      >
        <span style="padding-left: 7px; font-weight: bold; font-size: 20px"> {{ showTitle }}</span>
        <a-button type="primary" style="float: right" @click="reportHandleCancel">关闭</a-button>
        <a-divider />
        <div class="drawre" v-html="messageList" style="background-color: #ffffff; border: 1px solid #a3a3a3"></div>
        <a-divider />
        <div class="img-box">
          <ul>
            <li
              v-for="(item, index) in attList"
              style="position: relative; user-select: none"
              :title="item.split('attid')[0]"
              :key="index"
              class="liback"
              @click.right="rightClick1($event, item, index)"
            >
              <img v-if="item.toLowerCase().includes('pdf')" style="width: 25px; padding-left: 5px" src="@/assets/icon/pdf.png" />
              <img
                v-else-if="
                  item.toLowerCase().includes('jpg') ||
                  item.toLowerCase().includes('png') ||
                  item.toLowerCase().includes('bmp') ||
                  item.toLowerCase().includes('jpeg')
                "
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/jpg.png"
              />
              <img
                v-else-if="item.toLowerCase().includes('xlsx') || item.toLowerCase().includes('xls')"
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/12.png"
              />
              <img v-else-if="item.toLowerCase().includes('txt')" style="width: 25px; padding-left: 5px" src="@/assets/icon/txt.png" />
              <img v-else-if="item.toLowerCase().includes('tif')" style="width: 25px; padding-left: 5px" src="@/assets/icon/tiff.png" />
              <img
                v-else-if="item.toLowerCase().includes('docx') || item.toLowerCase().includes('doc')"
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/docx.png"
              />
              <img
                v-else-if="item.toLowerCase().includes('zip') || item.toLowerCase().includes('rar')"
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/zip.png"
              />
              <img v-else-if="item.toLowerCase().includes('csv')" style="width: 25px; padding-left: 5px" src="@/assets/icon/csv.png" />
              <img v-else style="width: 25px; padding-left: 5px" src="@/assets/icon/inf.png" />
              <span>
                {{ item.split("attid")[0] }}
              </span>
            </li>
          </ul>
        </div>
        <a-menu :style="menuStyle1" v-if="menuVisible1" class="tabRightClikBox">
          <a-menu-item v-if="Multiple" @click="download">下载附件</a-menu-item>
        </a-menu>
      </a-modal>
      <a-modal
        title="ECN信息"
        :visible="OrderTypeVisible"
        @cancel="handleCancelecn"
        @ok="handleOk7"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="850"
        centered
      >
        <a-form-model ref="ruleForm" :model="Ecninfo" :rules="Ecnrules">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="更改后客户型号" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" prop="customerModel">
                <a-input v-model="Ecninfo.customerModel" allowClear></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="自定分类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" prop="customClassification">
                <a-select v-model="Ecninfo.customClassification" showSearch optionFilterProp="lable" style="width: 100%" @change="customChange">
                  <a-select-option v-for="(item, index) in Rebates" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="更改前生产型号" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
                <a-input v-model="Ecninfo.proOrderNoOld" allowClear disabled></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="更改后生产型号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" prop="proOrderNo">
                <a-input v-model="Ecninfo.proOrderNo" allowClear></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="更改前表面处理" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
                <a-select v-model="Ecninfo.surfaceFinishOld" showSearch allowClear disabled optionFilterProp="lable" style="width: 100%">
                  <a-select-option v-for="(item, index) in mapKey(selectOption.SurfaceFinish)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="更改后表面处理" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" prop="surfaceFinish">
                <a-select v-model="Ecninfo.surfaceFinish" showSearch allowClear optionFilterProp="lable" style="width: 100%">
                  <a-select-option v-for="(item, index) in mapKey(selectOption.SurfaceFinish)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="旧版本处理方式" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" prop="oldBoardDoType">
                <a-select v-model="Ecninfo.oldBoardDoType" showSearch allowClear optionFilterProp="lable" style="width: 100%">
                  <a-select-option v-for="(item, index) in mapKey(selectOption.OldBoardDoType)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="库存板" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" prop="inventoryBoard">
                <a-select v-model="Ecninfo.inventoryBoard" showSearch allowClear optionFilterProp="lable" style="width: 100%">
                  <a-select-option v-for="(item, index) in mapKey(selectOption.InventoryBoard)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="更改内容" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="Ecninfo.ecnRemark" style="width: 100%; margin-bottom: 0px" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-modal>
      <!-- 销售信息弹窗 -->
      <a-modal
        :title="'【' + InfoorderNo + '】' + '-- 销售信息填写'"
        :visible="dataVisible6"
        @cancel="reportHandleCancel"
        class="xiaoshou"
        @ok="handleOk6"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="850"
        centered
      >
        <sales-info
          ref="editForm1"
          v-if="JSON.stringify(showData) != '{}'"
          :showData="showData"
          :selectOption="selectOption"
          :factoryList="factoryList"
          :saveID="saveID"
          @changeOrderType="changeOrderType"
          :rowdata="$refs.orderTable.selectedRowsData"
        ></sales-info>
      </a-modal>
      <a-modal title="检查信息" :visible="dataVisible22" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="600" centered>
        <template #footer>
          <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
          <a-button key="back" @click="reportHandleCancel">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData1" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
      <a-modal
        title="返单操作"
        @cancel="reportHandleCancel3"
        :visible="merdatavisible"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        class="return_order"
        :width="1650"
        centered
      >
        <template slot="footer" v-if="user.factoryId == 22">
          <a-button @click="reportHandleCancel3">取消</a-button>
          <a-button type="primary" :loading="loading1" @click="$refs.jzmergerinfo.dereturnorderok('qd')">确定</a-button>
          <a-button
            type="primary"
            :loading="loadingok"
            v-if="checkPermission('MES.MarketModule.Check.ReOrderCheckEnd')"
            @click="$refs.jzmergerinfo.returncomplete()"
            >完成</a-button
          >
        </template>
        <template
          slot="footer"
          v-else-if="user.factoryId == 58 || user.factoryId == 59 || user.factoryId == 67 || user.factoryId == 97 || user.factoryId == 98"
        >
          <a-button @click="reportHandleCancel3">取消</a-button>
          <a-button type="primary" :loading="loading1" @click="$refs.yxdmergerinfo.dereturnorderok('qd')">确定</a-button>
          <a-button
            type="primary"
            :loading="loadingok"
            v-if="checkPermission('MES.MarketModule.Check.ReOrderCheckEnd')"
            @click="$refs.yxdmergerinfo.returncomplete()"
            >完成</a-button
          >
          <a-button type="primary" @click="$refs.yxdmergerinfo.Combination()">组合合同</a-button>
        </template>
        <template slot="footer" v-else>
          <a-button @click="reportHandleCancel3">取消</a-button>
          <a-button type="primary" :loading="loading1" @click="$refs.othermergerinfo.dereturnorderok('qd')">确定</a-button>
          <a-button
            type="primary"
            :loading="loadingok"
            v-if="checkPermission('MES.MarketModule.Check.ReOrderCheckEnd')"
            @click="$refs.othermergerinfo.returncomplete()"
            >完成</a-button
          >
        </template>
        <div v-if="user.factoryId == 22">
          <jz-returnorders
            ref="jzmergerinfo"
            :selectOption="selectOption"
            @loadchange="loadchange"
            @risk="risk"
            @checkmodal="checkmodal"
            :requiredLinkConfigoffer="requiredLinkConfigoffer"
          ></jz-returnorders>
        </div>
        <div v-else-if="user.factoryId == 58 || user.factoryId == 59 || user.factoryId == 67 || user.factoryId == 97 || user.factoryId == 98">
          <yxd-returnorders
            ref="yxdmergerinfo"
            :selectOption="selectOption"
            @loadchange="loadchange"
            @risk="risk"
            :contractNoyxd="contractNoyxd"
            @YXDform="YXDform"
            @getOrderList="getOrderList"
            @LTsalescontract="LTsalescontract"
            @checkmodal="checkmodal"
            :requiredLinkConfigoffer="requiredLinkConfigoffer"
          ></yxd-returnorders>
        </div>
        <div v-else>
          <other-returnorders
            ref="othermergerinfo"
            :selectOption="selectOption"
            @risk="risk"
            @loadchange="loadchange"
            checkmodal="checkmodal"
            :requiredLinkConfigoffer="requiredLinkConfigoffer"
            :factoryList="factoryList"
          ></other-returnorders>
        </div>
      </a-modal>
      <a-modal
        title="订单接入"
        :visible="oderdataVisible"
        @cancel="reportHandleCancel"
        @ok="orderhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="fdloading"
        centered
      >
        <a-form-model-item label="返单更改" :labelCol="{ span: 7 }" :wrapperCol="{ span: 1 }" style="text-align: center; margin-bottom: 0">
          <a-checkbox v-model="form.isReorder"></a-checkbox>
        </a-form-model-item>
        <a-form-model-item label="生产型号：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12 }">
          <a-input v-model="form.proOrderNo" allowClear />
        </a-form-model-item>
        <a-form-model-item label="客户代码" ref="custNo" prop="custNo" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12 }">
          <a-select
            placeholder="请选择客户代码"
            :getPopupContainer="() => this.$refs.showPanel"
            v-model="form.custNo"
            :dropdownMatchSelectWidth="false"
            showSearch
            optionFilterProp="children"
            @popupScroll="handlePopupScroll"
            allowClear
            @search="supValue"
            style="width: 176px"
            :disabled="user.factoryId != 22"
          >
            <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import { factroylist } from "@/services/mkt/Inquiry.js";
import convertToChineseNum from "@/utils/convertToChineseNum";
import { riskwarning } from "@/services/gongju/RiskWarning.js";
import { addRiskWarning } from "@/services/mkt/CustInfoNew";
import axios from "axios";
import { updateFile } from "@/services/mkt/Inquiry.js";
import { Base64 } from "js-base64";
import { emimeconent, downloadbyattid } from "@/services/mkt/MailList";
import { requiredLinkConfig } from "@/services/mkt/orderInfo";
import { ordercancel, orderpause } from "@/services/mkt/OrderManagement.js";
import { checkPermission } from "@/utils/abp";
import VueDraggableResizable from "vue-draggable-resizable";
import $ from "jquery";
import LeftTableMake from "@/pages/mkt/OrderOffer/module/LeftTableMake";
import JzReturnorders from "@/pages/mkt/OrderOffer/module/JzReturnorders";
import OtherReturnorders from "@/pages/mkt/OrderOffer/module/OtherReturnorders";
import YxdReturnorders from "@/pages/mkt/OrderOffer/module/YxdReturnorders";
import {
  orderverifydelete,
  pcbnopeinfo,
  verifyPageList,
  result4ParameterList,
  resultList,
  resultDetailList,
  orderPriceCalc,
  verifyStartOrder,
  pricelistbypcbid,
  verifychangeprice,
  sendcontracteMime,
  verifyFinishedOrder,
  orderDayCalc,
  verifyBackToPreOrder,
  quotationInfo,
  quotationInfo1,
  getQuotationInfoByGrp,
  orderPriceCalc4WinForm,
  verifyFinishedOrderyXD,
  orderPriceCalcResult4Parameter,
  seturgentday,
  settoolcheck,
  verifyQuotation,
  orderrecoveryduoxuan,
  verifyfinishorders,
  timedautomaticsendorder,
  deleteOrderPriceCalcResult4Parameter,
  setNum,
  indicationCheck,
  setIsProduction,
  orderpricecalcone,
  contractReviewInfo,
  getcontractinfobygrp,
  contractNo,
  emSPcborderlog,
  pricingInspection,
  jz806EXLE,
  jz862EXLE,
  orderRecovery,
  orderpricehL,
  setorderpricehL,
  pcbordertonewtest,
  pcbordertest,
  preaddnopenew,
  preaddnopealternew,
  checkuserlist,
  calacmktorderpricedaybysig,
  parsignorderpricecalc,
  frontcontractreport,
  orderformreport,
  reviewreport,
  quotationmodel,
  yxDQuotationEXLE,
  yxDOrderpriceEXLE,
  ltQuotationEXLE,
  ltCostEXLEV2,
  paymodecheck,
  updatebatchcustpo,
} from "@/services/mkt/OrderReview.js";
import { mktCustNo, mktcustnobyfAC } from "@/services/mkt/Inquiry.js";
import { informationremind, getinformationremind } from "@/services/system/InformationRemind.js";
import { buttonCheck } from "@/services/mkt/OrderManagement.js";
import MakeAction from "@/pages/mkt/OrderOffer/module/MakeAction";
import QueryInfo from "@/pages/mkt/OrderOffer/module/QueryInfo";
import LockInfo from "@/pages/mkt/OrderOffer/module/LockInfo";
import ReturInfo from "@/pages/mkt/OrderOffer/module/ReturInfo";
import { mapMutations } from "vuex";
import ModifyInfo from "@/pages/mkt/OrderOffer/module/ModifyInfo";
import SalesInfo from "@/pages/mkt/OrderOffer/module/SalesInfo";
import MakeupPic from "@/pages/mkt/Orderverify/subassembly/MakeupPic";
import ReportInfo from "@/pages/mkt/OrderOffer/report/ReportInfo";
import ReportInfo1 from "@/pages/mkt/OrderOffer/report/ReportInfo1";
import ReportJzreportform from "@/pages/mkt/OrderOffer/report/ReportJzreportform";
import ReportCrreportform from "@/pages/mkt/OrderOffer/report/ReportCrreportform";
import ReportYxdreportform from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform";
import ReportYxdreportform1098 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1098";
import ReportYxdreportform1151 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1151";
import ReportYxdreportform1508 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1508";
import ReportYxdreportform1083 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1083";
import ReportBqreportform from "@/pages/mkt/OrderOffer/report/ReportBqreportform";
import ReportMtgxreportform from "@/pages/mkt/OrderOffer/report/ReportMtgxreportform";
import ReportBcreportformnx from "@/pages/mkt/OrderOffer/Bcreport/ReportBcreportformnx";
import ReportBcreportform0184 from "@/pages/mkt/OrderOffer/Bcreport/ReportBcreportform0184";
import ReportBcreportformwx from "@/pages/mkt/OrderOffer/Bcreport/ReportBcreportformwx";
import ReportLtreportform0849 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0849";
import ReportLtreportform1220 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1220";
import ReportLtreportform1032 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1032";
import ReportLtreportform0041 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0041";
import ReportLtreportform1244 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1244";
import ReportLtreportform0100 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0100";
import ReportLtreportform1206 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1206";
import ReportLtreportform0072 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0072";
import ReportLtreportform1034 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1034";
import ReportLtreportform1099 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1099";
import ReportLtreportform1170 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1170";
import ReportLtdefaultreport from "@/pages/mkt/OrderOffer/Ltreport/ReportLtdefaultreport";
import ReportLtreportform0271 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0271";
import ReportLtreportform0972 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0972";
import ReportLtreportform0477 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0477";
import ReportJzsalescontract from "@/pages/mkt/OrderOffer/report/ReportJzsalescontract";
import ReportBqsalescontract from "@/pages/mkt/OrderOffer/report/ReportBqsalescontract";
import ReportLtsalescontract from "@/pages/mkt/OrderOffer/report/ReportLtsalescontract";
import ReportCrsalescontract from "@/pages/mkt/OrderOffer/report/ReportCrsalescontract";
import ReportBcsalescontract from "@/pages/mkt/OrderOffer/report/ReportBcsalescontract";
import ReportJzreviewform from "@/pages/mkt/OrderOffer/report/ReportJzreviewform";
import ReportInfoyxd from "@/pages/mkt/OrderOffer/report/ReportInfoyxd";
import ReportInfohm from "@/pages/mkt/OrderOffer/report/ReportInfohm";
import ReportInfomtgx from "@/pages/mkt/OrderOffer/report/ReportInfomtgx";
import PrequalificationInfo from "@/pages/mkt/OrderOffer/module/PrequalificationInfo";
import {
  getEditOrderInfo,
  mktConfig,
  SaleOrderInfo,
  prenopeinfo,
  custgrouplist,
  preaddnope,
  addnopealter,
  nopealter,
  orderlocked,
  setordermodifylist,
  selectpars,
} from "@/services/mkt/orderInfo";
import Cookie from "js-cookie";
import moment from "moment";
import { mapState } from "vuex";
import * as XLSX from "xlsx";
import {
  graphicPreview,
  pcbordertonew,
  setreleasewarning,
  verifyonlineeCN,
  getOrderSplit,
  contractprecheck,
  ordernumsplit,
} from "@/services/mkt/PrequalificationProduction.js";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
    ellipsis: true,
    fixed: "left",
  },
  {
    title: "订单号",
    align: "left",
    ellipsis: true,
    width: 130,
    fixed: "left",
    scopedSlots: { customRender: "orderNo" },
    sorter: (a, b) => {
      return a.orderNo.localeCompare(b.orderNo);
    },
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 65,
    sorter: (a, b) => {
      return a.custNo.localeCompare(b.custNo);
    },
    // className:'userStyle',
  },
  {
    title: "客户型号",
    align: "left",
    ellipsis: true,
    width: 335,
    scopedSlots: { customRender: "customerModel" },
    // slots: { title: 'customTitle' },
    className: "customerModel",
  },
  {
    title: "订单类型",
    dataIndex: "reOrder",
    align: "left",
    ellipsis: true,
    width: 63,
    customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
    sorter: (a, b) => {
      return a.reOrder.toString().localeCompare(b.reOrder.toString());
    },
  },
  // {
  //   title: "订单数量",
  //   dataIndex: "num",
  //   width: '6%',
  //   ellipsis: true,
  //   align: "left",

  // },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    // className:'userStyle',
    width: 125,
    sorter: (a, b) => {
      return a.proOrderNo.localeCompare(b.proOrderNo);
    },
  },
  {
    title: "版本",
    dataIndex: "proRevStr",
    align: "left",
    ellipsis: true,
    width: 45,
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "left",
    ellipsis: true,
    // className:'userStyle',
    width: 40,
  },
  {
    title: "SU",
    dataIndex: "su",
    align: "left",
    width: 35,
    ellipsis: true,
  },
  // {
  //   title: "制板单价",
  //   dataIndex: "unitPrice",
  //   align: "left",
  //   width:80,
  //   ellipsis: true,
  // },
  {
    title: "状态",
    scopedSlots: { customRender: "status" },
    width: 55,
    ellipsis: true,
    align: "left",
  },
  {
    title: "解析",
    dataIndex: "analysisFinishStr",
    width: 55,
    ellipsis: true,
    align: "left",
  },
  {
    title: "交货日期",
    align: "left",
    dataIndex: "deliveryDate1",
    ellipsis: true,
    // className:'userStyle',
    width: 110,
    sorter: (a, b) => {
      if (a.deliveryDate1 === null) {
        return 1;
      }
      if (b.deliveryDate1 === null) {
        return -1;
      }
      return a.deliveryDate1.localeCompare(b.deliveryDate1);
    },
  },
  {
    title: "报价人",
    dataIndex: "checkName",
    width: 65,
    ellipsis: true,
    align: "left",
    // className:'userStyle'
  },
  {
    title: "预审人",
    dataIndex: "preName",
    width: 65,
    ellipsis: true,
    align: "left",
    // className:'userStyle'
  },
  {
    title: "业务员",
    align: "left",
    dataIndex: "ywName",
    ellipsis: true,
    // className:'userStyle',
    width: 65,
  },

  // {
  //   title: "解析状态",
  //   dataIndex: "analysisFinishStr",
  //   width:'8%',
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "图形预览",
  //   width:'8%',
  //   ellipsis: true,
  //   align: "left",
  //   scopedSlots: { customRender: 'preview' },
  // },
  {
    title: "上传时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 150,
    // className:'userStyle'
  },
  // {
  //   title: "交货时间",
  //   dataIndex: "deliveryDate",
  //   align: "left",
  //   ellipsis: true,
  //   width:'8%',
  // },

  {
    title: "订单来源",
    dataIndex: "orderSource",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  // {
  //   title: "返单编号",
  //   dataIndex: "reOrderNo",
  //   align: "left",
  //   ellipsis: true,
  //   width:120,
  //   className:'userStyle'
  // },
  {
    title: "建立人",
    dataIndex: "createName",
    align: "left",
    ellipsis: true,
    width: 65,
    // className:'userStyle'
  },
  {
    title: "合同",
    width: 220,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "contractFilePath" },
  },
  {
    title: "总金额",
    width: 100,
    align: "left",
    ellipsis: true,
    dataIndex: "totalAmountPrice",
  },
  {
    title: "合同币种",
    width: 70,
    align: "center",
    dataIndex: "currencyStr",
    ellipsis: true,
  },
  {
    title: "协议币种",
    width: 70,
    align: "center",
    dataIndex: "protocolCurrencyStr",
    ellipsis: true,
  },
  {
    title: "是否打印",
    width: 70,
    align: "center",
    scopedSlots: { customRender: "isQuotationForm" },
    ellipsis: true,
  },
  {
    title: "合同号",
    width: 140,
    align: "center",
    dataIndex: "contractNo",
    ellipsis: true,
  },
  {
    title: "客户订单号",
    width: 200,
    align: "left",
    dataIndex: "custPo",
    ellipsis: true,
  },
  {
    title: "客户物料号",
    width: 200,
    align: "left",
    dataIndex: "customerMaterialNo",
    ellipsis: true,
  },
  // {
  //   title: "邮件",
  //   width:80,
  //   align: "center",
  //   dataIndex: "mailId",
  //   scopedSlots: { customRender: 'mailId' },
  //   ellipsis: true,
  // },
  {
    title: "工厂",
    dataIndex: "contractFactoryName",
    width: 100,
    align: "center",
    ellipsis: true,
  },
  {
    title: "加工工厂",
    dataIndex: "orderDirectionStr",
    align: "center",
    width: 80,
    ellipsis: true,
  },
  {
    title: "操作",
    width: 70,
    align: "center",
    scopedSlots: { customRender: "labelUrl" },
    class: "noCopy",
  },
  {
    title: "参数信息",
    dataIndex: "sales",
    fixed: "right",
    width: 80,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "sales" },
  },
];
const columns2 = [
  {
    title: "序号",
    // dataIndex: 'index',
    key: "index",
    align: "center",
    width: 37,
    scopedSlots: { customRender: "num" },
  },
  {
    title: "下单",
    dataIndex: "radio",
    width: 37,
    ellipsis: true,
    align: "center",
    className: "inputClass",
    scopedSlots: { customRender: "radio" },
  },
  {
    title: "数量",
    dataIndex: "para4DelQty_",
    width: 50,
    ellipsis: true,
    align: "left",
    className: "inputClass",
    scopedSlots: { customRender: "para4DelQty_" },
  },
  {
    title: "提前",
    dataIndex: "para4UrgentDate_",
    width: 40,
    ellipsis: true,
    align: "left",
    className: "inputClass",
    scopedSlots: { customRender: "para4UrgentDate_" },
  },
  {
    title: "面积",
    dataIndex: "para4Area_",
    ellipsis: true,
    width: 55,
    align: "left",
    scopedSlots: { customRender: "para4Area_" },
  },
  {
    title: "交期",
    ellipsis: true,
    dataIndex: "para4IntDelivery_",
    //para4Delivery_
    width: 37,
    align: "left",
    scopedSlots: { customRender: "para4IntDelivery_" },
  },
  {
    title: "交货日期",
    dataIndex: "para4Delivery_",
    scopedSlots: { customRender: "para4Delivery_" },
    width: 110,
    className: "inputClass",
    ellipsis: true,
    align: "left",
  },
  {
    title: "单价",
    dataIndex: "pcsPrice_",
    width: 55,
    ellipsis: true,
    align: "left",
    className: "inputClass",
    scopedSlots: { customRender: "pcsPrice_" },
  },
  {
    title: "重量(KG)",
    dataIndex: "para4Weight_",
    width: 60,
    align: "left",
    // customRender: (text, record) => {
    //  return text.toFixed(2)
    // }
  },
  {
    title: "操作",
    width: 42,
    align: "center",
    scopedSlots: { customRender: "action" },
  },
];
const copycolumns3 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  // {
  //   title: "选择",
  //   dataIndex: "isChooseEdit",
  //   width: 40,
  //   ellipsis: true,
  //   align: "center",
  //   scopedSlots:{customRender:'isChooseEdit'}
  // },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
    scopedSlots: { customRender: "upOrDownPrice" },
  },
  {
    title: "实际报价",
    width: 58,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "actualPrice" },
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
  {
    title: "不含税",
    dataIndex: "noTaxPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
const copycolumns4 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
    scopedSlots: { customRender: "upOrDownPrice" },
  },
  {
    title: "实际报价",
    dataIndex: "actualPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
  {
    title: "不含税",
    dataIndex: "noTaxPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
const checkcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 40,
    scopedSlots: { customRender: "num" },
  },
  {
    title: "报价人",
    align: "left",
    dataIndex: "checkName",
    width: 65,
    ellipsis: true,
  },
  {
    title: "生产型号",
    align: "left",
    dataIndex: "proOrderNo",
    width: 180,
    ellipsis: true,
  },
  {
    title: "审批状态",
    align: "left",
    ellipsis: true,
    dataIndex: "statusStr",
    width: 80,
  },
  {
    title: "客户物料编码",
    align: "left",
    ellipsis: true,
    dataIndex: "customerMaterialNo",
    width: 120,
  },
  {
    title: "客户型号",
    align: "left",
    width: 200,
    ellipsis: true,
    dataIndex: "customerModel",
  },
  {
    title: "客户合同号",
    ellipsis: true,
    align: "left",
    dataIndex: "custPo",
    width: 130,
  },
  {
    title: "订单数量",
    align: "left",
    ellipsis: true,
    dataIndex: "para4DelQty_",
    width: 90,
  },
  {
    title: "含税价格",
    align: "left",
    width: 90,
    ellipsis: true,
    dataIndex: "pcsPriceWithTax",
  },
  {
    title: "飞测费",
    ellipsis: true,
    align: "left",
    dataIndex: "flyPrice_",
    width: 80,
  },
  {
    title: "工程费",
    align: "left",
    ellipsis: true,
    dataIndex: "engPrice_",
    width: 65,
  },
  {
    title: "测试架费",
    align: "left",
    dataIndex: "testPrice_",
    ellipsis: true,
    width: 80,
  },
  {
    title: "加急费",
    ellipsis: true,
    align: "left",
    dataIndex: "expeditePrice_",
    width: 65,
  },
  {
    title: "包干价",
    align: "left",
    ellipsis: true,
    dataIndex: "bgPrice_",
    width: 65,
  },
  {
    title: "模具费",
    align: "left",
    ellipsis: true,
    dataIndex: "moldPrice_",
    width: 65,
  },

  {
    title: "不含税价格",
    align: "left",
    ellipsis: true,
    dataIndex: "pcsPriceWithoutTax",
    width: 85,
  },
  {
    title: "反推含税价格",
    align: "left",
    ellipsis: true,
    dataIndex: "calcPCSPriceWithTax",
    width: 100,
  },
  {
    title: "面积单价",
    align: "left",
    ellipsis: true,
    dataIndex: "spcsPrice",
    width: 80,
  },
  {
    title: "订购金额",
    align: "left",
    ellipsis: true,
    dataIndex: "countPrice",
    width: 80,
  },
  {
    title: "订单金额",
    align: "left",
    ellipsis: true,
    dataIndex: "totalAmountPrice_",
    width: 80,
  },
  {
    title: "订单面积",
    ellipsis: true,
    align: "left",
    dataIndex: "para4Area_",
    width: 90,
  },
  {
    title: "下单工厂",
    align: "left",
    ellipsis: true,
    dataIndex: "contractFactoryName",
    width: 80,
  },
  {
    title: "加工工厂",
    align: "left",
    ellipsis: true,
    dataIndex: "orderDirection",
    width: 80,
  },
];
const lockcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 20,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作时间",
    align: "left",
    dataIndex: "createTime",
    width: 65,
  },
  {
    title: "操作人",
    align: "left",
    dataIndex: "userName",
    width: 30,
  },
  {
    title: "锁定原因",
    align: "left",
    dataIndex: "content",
    width: 185,
  },
];
const laborcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 20,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作时间",
    align: "left",
    dataIndex: "createTime",
    width: 65,
  },
  {
    title: "操作人",
    align: "left",
    dataIndex: "userName",
    width: 30,
  },
  {
    title: "内容",
    align: "left",
    // dataIndex: 'content',
    scopedSlots: { customRender: "addresscontent" },
    width: 185,
  },
];
export default {
  name: "",
  components: {
    ReportJzreviewform,
    ReportJzsalescontract,
    ReportCrreportform,
    ReportJzreportform,
    ReportInfo,
    ReportInfo1,
    ReportInfoyxd,
    MakeAction,
    ReportBqsalescontract,
    JzReturnorders,
    LeftTableMake,
    MakeupPic,
    QueryInfo,
    SalesInfo,
    ReturInfo,
    ModifyInfo,
    LockInfo,
    PrequalificationInfo,
    VueDraggableResizable,
    ReportYxdreportform,
    ReportYxdreportform1098,
    ReportYxdreportform1151,
    ReportYxdreportform1508,
    ReportYxdreportform1083,
    OtherReturnorders,
    ReportBqreportform,
    ReportMtgxreportform,
    ReportBcreportform0184,
    ReportBcreportformnx,
    ReportBcreportformwx,
    ReportLtreportform0271,
    YxdReturnorders,
    ReportInfohm,
    ReportInfomtgx,
    ReportLtsalescontract,
    ReportCrsalescontract,
    ReportBcsalescontract,
    ReportLtreportform0972,
    ReportLtreportform0477,
    ReportLtreportform0849,
    ReportLtdefaultreport,
    ReportLtreportform0072,
    ReportLtreportform1099,
    ReportLtreportform1170,
    ReportLtreportform1206,
    ReportLtreportform1034,
    ReportLtreportform1220,
    ReportLtreportform1032,
    ReportLtreportform0100,
    ReportLtreportform0041,
    ReportLtreportform1244,
  },
  inject: ["reload"],
  data() {
    return {
      Rebates: [],
      yxdids: [],
      Bcreportdata: {},
      Bcreportvisible: false,
      OnLineOrRecordEcn: 0,
      Upgradeversion: false,
      rules: {
        splitNum: [
          { required: true, message: "拆分数量请必填", trigger: "blur" },
          {
            pattern: /^(?:[1-9]|10)$/,
            message: "请填写1到10之间的正整数",
            trigger: "blur",
          },
        ],
      },
      Ecnrules: {
        customerModel: [{ required: true, message: "请填写更改后客户型号", trigger: "blur" }],
        customClassification: [{ required: true, message: "请选择自定分类", trigger: "blur" }],
        proOrderNo: [{ required: true, message: "请填写更改后生产型号", trigger: "blur" }],
        surfaceFinish: [{ required: true, message: "请填写更改后表面处理", trigger: "blur" }],
        oldBoardDoType: [{ required: true, message: "请选择旧版本处理方式", trigger: "blur" }],
        inventoryBoard: [{ required: true, message: "请选择库存板处理", trigger: "blur" }],
      },
      orderForm: {
        splitNum: null,
      },
      bqreport_type: "",
      offlinedataVisible: false,
      mmessage: "",
      dragy: 0,
      formtypes: "ltdefaultreport",
      bcformType: "nx",
      activeKey: "0",
      gjloading: false,
      showTitle: "",
      attList: [], //附件列表
      messageList: "",
      Quoterlist: [],
      count: 0,
      allid: [],
      orderVisible: false,
      verificationVisible: false,
      verificationData: [],
      requiredLinkConfigoffer: {},
      salescustno: "",
      userid: "",
      orderfac: "",
      ContractNoSech: "",
      Delivery: "",
      Quotationdata: {}, //报价表单数据
      fdloading: false,
      loadingok: false,
      Returnverification: "",
      checkvisible: false,
      loading1: false,
      yxddata: {},
      hmsalesdata: {},
      mtgxsalesdata: {},
      RowsData: {},
      showmodal: false,
      jijiaid: "",
      iscolumnKey: "",
      isorder: "",
      widthy: 400,
      topheighty: 146,
      heighty: 216,
      Queryinformation: {},
      innerHeight: 911,
      tableHeight: 556, // 设置第一个div的高度为窗口高度的一半
      width: 904,
      height: 750,
      x: 0,
      y: 0,
      labordataVisible: false,
      lockdataVisible: false,
      params1: {},
      labordata: [],
      risktext: "",
      Riskmodal: false,
      formdata: {
        address: false,
        price: false,
        deliverydate: false,
        num: false,
        PO: false,
        FileReplacement: false,
        parameters: false,
      },
      hasExecuted: false,
      marketdataVisible: false,
      factoryList: [],
      spinning: false,
      pagination: {
        pageSize: 20,
        //simple:true,
        current: 1,
        size: "small",
        total: 0,
        showQuickJumper: false,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      query: {
        OrderNo: "",
      },
      columns1,
      PoVisible: false,
      cust_Po: "",
      orderListData: [],
      exchangerate: [],
      orderListTableLoading: false,
      columns2,
      id: "",
      loading2: false,
      dataSource2: [],
      Paraid: "",
      Source2: [],
      columns3: [],
      copycolumns3,
      copycolumns4,
      guid4Parameter: "",
      loading3: false,
      dataSource3: [],
      columns4: [],
      laborcolumns,
      lockcolumns,
      checkcolumns,
      loading4: false,
      dataSource4: [],
      proOrderId: "",
      assignLoading: false,
      makeupVisible: false, // 拼版图弹窗开关
      dataVisible: false, // 查询弹窗开关
      PredataVisible: false,
      MaildataVisible: false,
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      attid: "",
      mailid: "",
      menuVisible1: false,
      Multiple: false,
      modelVisible: false, // 报表弹窗,
      yxdVisible: false,
      mtgxVisible: false,
      hmVisible: false,
      menuVisible: false,
      JZsalesvisible: false,
      riskcancel: false,
      BQsalesvisible: false,
      JZsalesdata: {},
      LTsalesdata: {},
      CRsalesdata: {},
      BCsalesdata: {},
      BQsalesdata: {},
      JZreportdata: {},
      CRreportdata: {},
      YXDreportdata: {},
      JZreviewdata: {},
      Bqreportdata: {},
      LTreportdata: {},
      LTreport: false,
      LTsalesvisible: false,
      CRsalesvisible: false,
      BCsalesvisible: false,
      JZreviewvisible: false,
      JZreportvisible: false,
      CRreportvisible: false,
      YXDreportvisible: false,
      BQreportvisible: false,
      RiskVisible: false,
      RiskContent: "",
      Mtgxreportvisible: false,
      Mtgxreportdata: {},
      selectlength: 0,
      widthy1: 0,
      split_num: "",
      ddataVisible: false,
      splitVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      key: "1",
      reportData: {}, //报表数据
      PriceFlag: false,
      row2: {},
      row3: {},
      dataVisible1: false, // 新增询价
      numFlag: false,
      autofocus: false,
      upOrDownPrice: "",
      oldPrice: "",
      Parameter: "",
      contractNoyxd: "",
      dataVisible2: false,
      checkData: [], // 指示检查数据
      check: false,
      checkType: "",
      num: "",
      price: "",
      addnum: "",
      addnum1: "",
      dataVisible3: false,
      CancelCause: "",
      PauseCause: "",
      message: "",
      orderno: "",
      buttonload: false,
      type: "",
      deletId: "",
      lockdata: [],
      sales: "",
      dataVisible6: false,
      Ecninfo: {},
      OrderTypeVisible: false,
      joinFactoryId: "",
      systemdataVisible: false,
      dataVisible8: false,
      dataVisible9: false,
      dataVisible22: false,
      checkData1: [], // 指示检查数据
      check1: false,
      editFlag: false,
      showData: {},
      redata: {},
      selectOption: {},
      saveID: "",
      isMovedown: false,
      startPosition: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      isMovedown1: false,
      startPosition1: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      InfoorderNo: "",
      val: [],
      popupdata: [],
      returOrderNo: "",
      returCust: "",
      pageStat: false, //分页点击不清除
      queryParam: {},
      value: 1,
      frontDataZSupplier1: [],
      returncustno: "",
      isLock: false,
      lockCause: "",
      searchbox: false,
      oderdataVisible: false,
      merdatavisible: false,
      form: {
        proOrderNo: "",
        isReorder: false,
        custNo: "",
      },
      isCtrlPressed: false,
      supList: [],
      frontDataZSupplier: [], // 供应商 100条数据的集合
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      priceChangeFlg: false,
      customerMaterialName: "",
      endCustName: "",
      customerMaterialNo: "",
      complete: false,
      recordPrice: [],
    };
  },
  created() {
    //按钮防抖 500毫秒
    this.dePoHandleOk = this.debounce(this.PoHandleOk, 500);
    this.debouncedsendcontract = this.debounce(this.sendcontract, 500);
    this.DehandleOk3 = this.debounce(this.handleOk3, 500);
    let orderno = localStorage.getItem("orderNo_servicemana");
    this.getOrderList({ OrderNo: orderno });
    this.getRequiredLink();
    checkuserlist().then(res => {
      if (res.code) {
        this.Quoterlist = res.data;
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  computed: {
    ...mapState("account", ["user", "buryingpoint"]),
  },
  mounted() {
    this.userid = this.user.factoryId;
    this.Changecolumns(this.userid);
    document.getElementById("touchmove").onmousedown = this.textWidthChange;
    this.heighty = 216;
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    this.getSupplier();
    if ((this.user.factoryId == 58 || this.user.factoryId == 59) && this.columns1[27].title != "客户名称") {
      this.columns1.splice(27, 0, {
        title: "客户名称",
        dataIndex: "custName",
        width: 220,
        ellipsis: true,
      });
    } else if (this.user.factoryId != 58 && this.user.factoryId != 59 && this.columns1[27].title == "客户名称") {
      this.columns1.splice(27, 1);
    }
    factroylist().then(res => {
      if (res.code) {
        this.factoryList = res.data;
      }
    });
  },
  beforeRouteLeave: function (to, from, next) {
    next();
  },
  methods: {
    checkPermission,
    ...mapMutations("account", ["Buriedpointcache"]),
    ...mapMutations("setting", ["setinfolength"]),
    // advanced(show){
    //   if(show){
    //     this.pagination.simple=true
    //   }else{
    //     this.pagination.simple=false
    //   }
    // },
    highlightText(text, keyword, originalColor) {
      if (!text || !keyword) return text;
      const regex = new RegExp(keyword, "gi"); // 使用正则表达式全局匹配，忽略大小写
      text = `<span style="color: ${originalColor}">${text}!</span>`;
      console.log(text, "1798");
      return text.replace(regex, `<span style="color: red;font-weight: bold;">$&</span>`); // 使用 $& 来引用匹配到的文本
    },
    customChange() {
      if (this.Ecninfo.customClassification == "返单有改不升级版本") {
        this.Ecninfo.proOrderNo = this.Ecninfo.proOrderNoOld;
      }
    },
    fildata(data, type) {
      let arr = type == -1 ? data.filter(item => item.tag_ == type) : data.filter(item => item.tag_ >= type);
      return arr;
    },
    getRequiredLink() {
      let factory = this.user.factoryId;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("requiredLinkConfigoffer"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.requiredLinkConfigoffer = data[a].data; //本地缓存
          }
        }
      } else {
        requiredLinkConfig(factory, 4).then(res => {
          if (res.code) {
            this.requiredLinkConfigoffer = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.requiredLinkConfigoffer) != "{}") {
              if (data == null) {
                arr.push({
                  data: this.requiredLinkConfigoffer,
                  token,
                  factory,
                });
                localStorage.setItem("requiredLinkConfigoffer", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({
                  data: this.requiredLinkConfigoffer,
                  token,
                  factory,
                });
                localStorage.setItem("requiredLinkConfigoffer", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    risk(val) {
      let params = {
        linkType: 2,
        JoinFactoryId: val.joinFactoryId,
        OrderNo: val.proOrderNo,
        Status: 1,
      };
      let str = "";
      if (val.riskWarningContent) {
        let arr = val.riskWarningContent.split("\\\\");
        arr.forEach((item, index) => {
          str += index + 1 + "." + item + "\n";
        });
        this.risktext = val.proOrderNo + "\n" + str;
        this.Riskmodal = true;
        if (this.user.factoryId == 22) {
          this.$refs.jzmergerinfo.Returnorderdata = {};
          this.$refs.jzmergerinfo.fandandataSource = [];
        } else if (
          this.user.factoryId == 58 ||
          this.user.factoryId == 59 ||
          this.user.factoryId == 67 ||
          this.user.factoryId == 97 ||
          this.user.factoryId == 98
        ) {
          this.$refs.yxdmergerinfo.Returnorderdata = {};
          this.$refs.yxdmergerinfo.fandandataSource = [];
          this.$refs.yxdmergerinfo.discustNo = true;
        } else {
          this.$refs.othermergerinfo.Returnorderdata = {};
          this.$refs.othermergerinfo.fandandataSource = [];
        }
        return;
      }
      riskwarning(params).then(res => {
        if (res.code && res.data.length) {
          res.data.forEach((item, index) => {
            str += index + 1 + "." + item.content + "\n";
          });
          this.riskInfo(str, val);
        } else {
          if (this.user.factoryId == 22) {
            this.$refs.jzmergerinfo.getreturndata(val);
          } else if (
            this.user.factoryId == 58 ||
            this.user.factoryId == 59 ||
            this.user.factoryId == 67 ||
            this.user.factoryId == 97 ||
            this.user.factoryId == 98
          ) {
            this.$refs.yxdmergerinfo.getreturndata(val);
          } else {
            this.$refs.othermergerinfo.getreturndata(val);
          }
        }
      });
    },
    riskInfo(str, val) {
      if (confirm("" + str)) {
        if (this.user.factoryId == 22) {
          this.$refs.jzmergerinfo.getreturndata(val);
        } else if (
          this.user.factoryId == 58 ||
          this.user.factoryId == 59 ||
          this.user.factoryId == 67 ||
          this.user.factoryId == 97 ||
          this.user.factoryId == 98
        ) {
          this.$refs.yxdmergerinfo.getreturndata(val);
        } else {
          this.$refs.othermergerinfo.getreturndata(val);
        }
      } else {
        if (this.user.factoryId == 22) {
          this.$refs.jzmergerinfo.Returnorderdata = {};
          this.$refs.jzmergerinfo.fandandataSource = [];
        } else if (
          this.user.factoryId == 58 ||
          this.user.factoryId == 59 ||
          this.user.factoryId == 67 ||
          this.user.factoryId == 97 ||
          this.user.factoryId == 98
        ) {
          this.$refs.yxdmergerinfo.Returnorderdata = {};
          this.$refs.yxdmergerinfo.fandandataSource = [];
          this.$refs.yxdmergerinfo.discustNo = true;
        } else {
          this.$refs.othermergerinfo.Returnorderdata = {};
          this.$refs.othermergerinfo.fandandataSource = [];
        }
      }
    },
    loadchange(type, load) {
      if (type == "wc" && load == "true") {
        this.loadingok = true;
      } else if (load == "true") {
        this.loading1 = true;
      } else {
        this.loading1 = false;
        this.loadingok = false;
      }
    },
    checkhandleOk() {
      this.checkvisible = false;
      if (this.user.factoryId == 22) {
        this.$refs.jzmergerinfo.fdcompleted();
      } else if (
        this.user.factoryId == 58 ||
        this.user.factoryId == 59 ||
        this.user.factoryId == 67 ||
        this.user.factoryId == 97 ||
        this.user.factoryId == 98
      ) {
        this.$refs.yxdmergerinfo.fdcompleted();
      } else {
        this.$refs.othermergerinfo.fdcompleted();
      }
    },
    checkmodal(message) {
      this.checkvisible = true;
      this.Returnverification = message;
    },
    getDelivery(list) {
      let str = [];
      for (let index = 0; index < list.length; index++) {
        str.push(list[index].captions_ + ": 交期" + list[index].priceDay_ + "天");
      }
      this.Delivery = str.join("\n");
    },
    numchange() {
      let redata = this.Returnorderdata;
      if (redata.delType == "SET" && redata.setBoardHeight && redata.setBoardWidth && redata.num) {
        redata.para4Area_ = this.getFloat((redata.setBoardHeight * redata.setBoardWidth * redata.num) / 1000000.0, 4);
      } else if (redata.delType == "PCS" && redata.setBoardHeight && redata.setBoardWidth && redata.num) {
        redata.para4Area_ = this.getFloat((redata.setBoardHeight * redata.setBoardWidth * redata.num) / 1000000.0 / (redata.su || 1), 4);
        this.$set(this.Returnorderdata, "para4Area_", redata.para4Area_);
      } else {
        this.$set(this.Returnorderdata, "para4Area_", "");
      }
    },
    handleChange1({ fileList }, data) {
      if (this.isFileType) {
        this.fileList = fileList;
        if (this.fileList.length == 0) {
          this.FilePath = "";
          this.done = "";
          this.$set(this.Returnorderdata, "newCustomerModel", this.Returnorderdata.customerModel);
        }
        if (this.fileList.length > 1) {
          this.fileList.splice(0, 1);
          return;
        }
      }
    },
    beforeUpload(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
      if (!this.isFileType) {
        this.$message.error("文件确认只支持.rar或.zip格式文件");
        return false;
      }
    },
    async downloadFilesCustomRequest(data, type) {
      this.done = "";
      const formData = new FormData();
      formData.append("file", data.file);
      let name = data.file.name.split(".");
      this.$set(this.Returnorderdata, "newCustomerModel", data.file.name.split("." + name[name.length - 1])[0]);
      this.filespinning = true;
      await upLoadEnquiryFile(formData)
        .then(res => {
          if (res.code) {
            data.onSuccess(res.data);
            this.FilePath = res.data.split(",")[0];
            this.done = "ok";
            this.$set(this.Returnorderdata, "pcbFileName", data.file.name.split("." + name[name.length - 1])[0]);
          } else {
            this.$set(this.Returnorderdata, "newCustomerModel", "");
            this.$message.error(res.message);
            this.done = "no";
          }
        })
        .finally(() => {
          this.filespinning = false;
        });
    },
    onChange2(value, dateString) {
      this.popupdata.marketDeliveryTime = dateString;
    },
    onChange3(value, dateString) {
      this.popupdata.deliveryDate = dateString;
    },
    textWidthChange(e) {
      let that = this;
      let dy = e.clientY;
      document.onmousemove = e => {
        if (e.clientY + 757 <= that.innerHeight) {
          return;
        }
        if (e.clientY >= 273) {
          return;
        }
        if (e.clientY < dy) {
          that.tableHeight -= dy - e.clientY;
        } else {
          that.tableHeight += e.clientY - dy;
        }
        dy = e.clientY;
        if (this.tableHeight >= 559.5) {
          this.heighty = 216;
        } else {
          this.heighty = this.innerHeight - this.tableHeight - 143;
          this.topheighty = 350 - this.heighty;
        }
      };
      document.onmouseup = e => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
      e.stopPropagation();
      e.preventDefault();
      return false;
    },
    getModalContainer() {
      return document.querySelector("#modal-container"); // 返回弹窗容器的选择器或 HTMLElement 对象
    },
    onResize: function (x, y, width, height) {
      this.x = x;
      this.y = y;
      this.width = width;
      this.height = height;
      if (width == 700) {
        document.getElementsByClassName("dragging1")[0].style.width = "904px";
        document.getElementsByClassName("dragging1")[0].style.height = "750px";
      }
    },
    onDrag: function (x, y) {
      document.getElementsByClassName("dragging1")[0].style.left = x;
      this.x = x;
      this.y = y;
    },
    getModalContainer1() {
      return document.querySelector("#modal-container1"); // 返回弹窗容器的选择器或 HTMLElement 对象
    },
    convertToChineseNum,
    onResize1: function (x, y, width, height) {
      if (width == 700) {
        document.getElementsByClassName("dragging2")[0].style.width = "904px";
        document.getElementsByClassName("dragging2")[0].style.height = "750px";
      }
    },
    onDrag1: function (x, y) {
      document.getElementsByClassName("dragging2")[0].style.left = x;
    },
    rightClick1(e, item, index) {
      e.preventDefault();
      this.menuVisible1 = true;
      this.attid = item.split("attid")[1];
      this.Multiple = true;
      this.menuStyle1.top = e.clientY - 110 + "px";
      this.menuStyle1.left = e.clientX - 380 + "px";
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    download() {
      downloadbyattid(this.mailid, this.attid).then(res => {
        if (res.code) {
          if (res.data) {
            var base64String = res.data;
            var binaryString = atob(base64String);
            var binaryLen = binaryString.length;
            var bytes = new Uint8Array(binaryLen);
            for (var i = 0; i < binaryLen; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }
            var blob = new Blob([bytes], { type: "application/octet-stream" });
            var url = URL.createObjectURL(blob);
            var a = document.createElement("a");
            document.body.appendChild(a);
            a.style = "display: none";
            a.href = url;
            a.download = res.message; // 这里可以自定义文件名和扩展名
            a.click();
            URL.revokeObjectURL(url);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        e.preventDefault();
        this.queryClick();
        this.reportHandleCancel();
        this.dataVisible = true;
        this.isCtrlPressed = false;
      } else if (e.keyCode == "72" && this.isCtrlPressed && checkPermission("MES.MarketModule.Check.PriceContract")) {
        this.quotationcontract();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "66" && this.isCtrlPressed && checkPermission("MES.MarketModule.Check.CheckQuotation")) {
        this.quotationClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.dataVisible3) {
        this.DehandleOk3();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    ContractDownload1(record) {
      if (record.content.indexOf("地址") != -1) {
        const xhr = new XMLHttpRequest();
        const queryString = record.content.split("地址：")[1];
        let a = queryString.split(".").slice(-1)[0];
        const splitArray = record.content.split("：【")[1].split("】")[0];
        xhr.open("GET", queryString, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = splitArray + "." + a;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      }
    },
    islock(record) {
      this.lockdataVisible = true;
      emSPcborderlog(record.id).then(res => {
        if (res.code) {
          this.lockdata = res.data.filter(item => {
            return item.content.indexOf("锁定原因") != -1;
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Changecolumns(joinFactoryId) {
      if ([67, 97, 98].indexOf(joinFactoryId) != -1) {
        this.columns3 = this.copycolumns3.filter(ite => ite.title != "港币价");
        this.columns4 = this.copycolumns4.filter(ite => ite.title != "港币价");
      } else {
        this.columns3 = this.copycolumns3.filter(ite => ite.title != "不含税");
        this.columns4 = this.copycolumns4.filter(ite => ite.title != "不含税");
      }
    },
    OperationLog(record) {
      this.labordataVisible = true;
      emSPcborderlog(record.id).then(res => {
        if (res.code) {
          this.labordata = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getData(factory) {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("PreSelectPar"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.selectOption = data[a].data; //本地缓存
          }
        }
      } else {
        selectpars(1, factory).then(res => {
          if (res.code) {
            this.selectOption = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (data == null) {
              arr.push({ data: this.selectOption, token, factory });
              localStorage.setItem("PreSelectPar", JSON.stringify(arr)); //本地缓存
            } else {
              data.push({ data: this.selectOption, token, factory });
              localStorage.setItem("PreSelectPar", JSON.stringify(data)); //本地缓存
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 获取订单
    getOrderList(queryData) {
      this.params1 = {};
      this.pageStat = localStorage.getItem("stat4") == "true" ? true : false;
      let obj = JSON.parse(localStorage.getItem("bjqueryInfo"));
      if (obj) {
        this.params1 = obj;
        queryData = obj;
      }
      if (this.pageStat) {
        let pageCurrent = localStorage.getItem("pageCurrent4");
        if (pageCurrent != undefined && pageCurrent != "") {
          this.pagination.current = parseInt(pageCurrent);
        }
        let pageSize = localStorage.getItem("pageSize4");
        if (pageSize != undefined && pageSize != "") {
          this.pagination.pageSize = parseInt(pageSize);
        }
        let data = localStorage.getItem("queryParam4");
        if (data != null && data != undefined && data != "") {
          this.queryParam = JSON.parse(data);
        }
      }
      this.queryParam.pageIndex = this.pagination.current;
      this.queryParam.pageSize = this.pagination.pageSize;
      let data = {
        ...this.queryParam,
      };

      localStorage.setItem("queryParam4", JSON.stringify(data));
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      if (queryData && queryData != "1") {
        params.OrderNo = queryData.OrderNo;
        params.PcbFileName = queryData.PcbFileName;
        params.proOrderNo = queryData.proOrderNo;
        params.custNo = queryData.custNo;
        params.custPo = queryData.custPo;
        params.Status = queryData.Status;
        params.StartTime = queryData.StartTime;
        params.EndTime = queryData.EndTime;
        params.ReOrder = queryData.ReOrder;
        params.contractNo = queryData.contractNo;
        params.Functionary = queryData.Functionary;
        params.createName = queryData.createName;
        params.CheckAccount = queryData.CheckAccount;
        params.CustomerMaterialNo = queryData.CustomerMaterialNo;
      }
      this.orderListTableLoading = true;
      let indexId = localStorage.getItem("id4");
      let record = localStorage.getItem("record4");
      params.PcbFileName = params.PcbFileName ? params.PcbFileName.replace(/\s+/g, " ").trim() : "";
      verifyPageList(params)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            if (this.iscolumnKey) {
              this.orderListData.sort((a, b) => {
                let aValue = a[this.iscolumnKey];
                let bValue = b[this.iscolumnKey];
                if (this.iscolumnKey === "deliveryDate1") {
                  if (aValue === null) {
                    return 1;
                  }
                  if (bValue === null) {
                    return -1;
                  }
                  return this.isorder === "ascend" ? aValue.localeCompare(bValue) : this.isorder === "descend" ? bValue.localeCompare(aValue) : 0;
                }
                if (typeof aValue === "string" && typeof bValue === "string") {
                  return this.isorder === "ascend" ? aValue.localeCompare(bValue) : this.isorder === "descend" ? bValue.localeCompare(aValue) : 0;
                } else {
                  if (aValue === bValue) {
                    return this.orderListData.indexOf(a) - this.orderListData.indexOf(b);
                  }
                  if (this.isorder === "ascend") {
                    return aValue > bValue ? 1 : -1;
                  } else if (this.isorder === "descend") {
                    return aValue < bValue ? 1 : -1;
                  }
                  return 0;
                }
              });
            }
            if (
              (params.custNo ||
                params.proOrderNo ||
                params.PcbFileName ||
                params.OrderNo ||
                params.contractNo ||
                params.Status ||
                params.custPo ||
                params.StartTime ||
                params.EndTime ||
                params.ReOrder ||
                params.CustomerMaterialNo ||
                params.CheckAccount ||
                params.createName ||
                params.Functionary) &&
              this.orderListData.length
            ) {
              this.$refs.orderTable.selectedRowKeysArray = [this.orderListData[0].id];
              this.$refs.orderTable.selectedRowsData.orderNo = this.orderListData[0].orderNo;
              this.$refs.orderTable.proOrderId = this.orderListData[0].id;
              this.$refs.orderTable.selectedRowsData = this.orderListData[0];
              if (this.$refs.orderTable.selectedRowsData.status != "待报价") {
                this.getOrderDetail(this.orderListData[0].id);
              }
            }
            localStorage.removeItem("bjqueryInfo");
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
            if (indexId !== "" && indexId != null) {
              this.$refs.orderTable.proOrderId = indexId;
              this.$refs.orderTable.selectedRowKeysArray[0] = indexId;
              this.getOrderDetail(indexId);
            }
            if (record !== "" && record != null && queryData != "1") {
              this.$refs.orderTable.selectedRowsData = JSON.parse(record);
            }

            if (queryData == "1") {
              // this.$refs.orderTable.selectedRowsData = this.orderListData.filter(item =>{return item.id == indexId})[0]
              this.$refs.orderTable.selectedRowsData = {};
              this.$refs.orderTable.selectedRowKeysArray = [];
              this.$refs.orderTable.proOrderId = "";
            }
            if (this.$refs.orderTable.selectedRowsData == undefined) {
              this.$refs.orderTable.proOrderId = "";
            }
            if (this.pageStat) {
              localStorage.removeItem("id4");
              localStorage.removeItem("record4");
              localStorage.removeItem("pageCurrent4");
              localStorage.removeItem("pageSize4");
              localStorage.removeItem("stat4");
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
          localStorage.removeItem("orderNo_servicemana");
        });
    },
    // 获取多套列表
    getOrderDetail(record, type) {
      if (record == undefined) {
        this.dataSource2 = [];
        this.Paraid = "";
        this.Source2 = [];
        this.dataSource3 = [];
        this.dataSource4 = [];
        return;
      }
      this.loading2 = true;
      result4ParameterList(record)
        .then(res => {
          if (res.data) {
            this.orderfac = this.$refs.orderTable.selectedRowsData.joinFactoryId;
            this.dataSource2 = res.data.filter(item => item.tag_ != -1);
            this.Paraid = res.data.filter(item => item.tag_ == -1)[0]?.id;
            if (type == "ChangePrice") {
              return;
            }
            var index = 0;
            if (this.dataSource2.findIndex(v => v.isProduction == true) < 0) {
              index = 0;
            } else {
              index = this.dataSource2.findIndex(v => v.isProduction == true);
            }
            this.Source2 = res.data;
            for (var b = 0; b < this.dataSource2.length; b++) {
              if (this.dataSource2[b].para4Weight_) {
                this.dataSource2[b].para4Weight_ = Number(this.dataSource2[b].para4Weight_).toFixed(2);
              }
              if (this.dataSource2[b].para4Delivery_) {
                this.dataSource2[b].para4Delivery_ = moment(moment(this.dataSource2[b].para4Delivery_).format("YYYY-MM-DD"));
              } else {
                this.dataSource2[b].para4Delivery_ = null;
              }
            }
            this.id = this.dataSource2[index].id;
            this.getResultList(this.dataSource2[index]);
            this.row2 = this.dataSource2[index];
            this.addnum = this.dataSource2[this.dataSource2.length - 1].para4DelQty_;
            this.addnum1 = this.dataSource2[0].para4UrgentDate_;
            this.num = this.dataSource2[0].para4DelQty_;
            this.price = this.dataSource2[0].pcsPrice_;
          }
        })
        .finally(() => {
          if (this.dataSource2.length > 0 && this.$refs.input) {
            this.$refs.input.focus();
          }
          this.loading2 = false;
        });
    },
    onClickRow2(record) {
      return {
        on: {
          click: () => {
            this.id = record.id;
            this.getResultList(record);
            this.row2 = record;
            this.addnum1 = record.para4UrgentDate_;
            this.num = record.para4DelQty_;
            this.price = record.pcsPrice_;
          },
        },
      };
    },
    // 获取价格结果列表
    getResultList(record, type) {
      this.loading3 = true;
      resultList(record.id)
        .then(res => {
          if (res.data) {
            this.dataSource3 = res.data;
            if (this.fildata(this.dataSource3, "0").length > 0) {
              this.activeKey = "0";
            } else if (this.fildata(this.dataSource3, "-1").length > 0) {
              this.activeKey = "1";
            }
            if (type != "price") {
              let index = res.data.findIndex(v => v.priceName.indexOf("平米价") !== -1 && v.tag_ > -1);
              if (index == -1) {
                // 如果没有找到tag_> -1的条目，再找包含"平米价"的条目
                index = res.data.findIndex(v => v.priceName.indexOf("平米价") !== -1);
              }
              if (this.dataSource3.length > 0 && index != -1) {
                this.guid4Parameter = this.dataSource3[index].guid4Parameter + this.dataSource3[index].guid4Order + this.dataSource3[index].guid4Calc;
                this.getResultDetailList(this.dataSource3[index]);
                this.row3 = this.dataSource3[index];
              } else {
                this.dataSource4 = [];
              }
            }
          }
        })
        .finally(() => {
          this.loading3 = false;
        });
    },
    onClickRow3(record) {
      return {
        on: {
          click: () => {
            this.guid4Parameter = record.guid4Parameter + record.guid4Order + record.guid4Calc;
            this.getResultDetailList(record);
            this.row3 = record;
            this.upOrDownPrice = record.upOrDownPrice;
          },
        },
      };
    },
    // 获取价格明细列表
    getResultDetailList(record) {
      this.loading4 = true;
      if (record.calcNameID == "1013501" || record.calcNameID == "1013510") {
        resultDetailList(record.guid4Parameter, record.calcNameID)
          .then(res => {
            if (res.data) {
              this.dataSource4 = res.data;
            }
          })
          .finally(() => {
            this.loading4 = false;
          });
      } else {
        this.dataSource4 = [];
        this.loading4 = false;
      }
    },
    onClickRow4(record) {
      return {
        on: {
          click: () => {
            this.Parameter = record.guid4Parameter + record.guid4Order + record.guid4Calc;
            this.upOrDownPrice = record.upOrDownPrice;
          },
        },
      };
    },
    reportHandleCancel1() {
      this.checkvisible = false;
    },
    handleCancelecn() {
      this.OrderTypeVisible = false;
      this.Ecninfo = {};
      this.$refs.editForm1.formData.reOrder = "1";
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.labordataVisible = false;
      this.lockdataVisible = false;
      this.marketdataVisible = false;
      this.dataVisible = false; // 查询弹窗
      this.PredataVisible = false;
      this.MaildataVisible = false;
      document.getElementsByClassName("dragging1")[0].style.display = "none";
      document.getElementsByClassName("dragging2")[0].style.display = "none";
      this.dataVisible1 = false;
      this.dataVisible3 = false;
      this.dataVisible6 = false;
      this.dataVisible8 = false;
      this.dataVisible9 = false;
      this.dataVisible22 = false;
      this.ddataVisible = false;
      this.splitVisible = false;
      this.systemdataVisible = false;
      this.oderdataVisible = false;
      this.orderVisible = false;
      this.buttonload = false;
      this.spinning = false;
    },
    reportHandleCancel3() {
      this.merdatavisible = false;
      if (
        this.user.factoryId == 58 ||
        this.user.factoryId == 59 ||
        this.user.factoryId == 67 ||
        this.user.factoryId == 97 ||
        this.user.factoryId == 98
      ) {
        if (this.contractNoyxd) {
          this.getOrderList({ contractNo: this.contractNoyxd });
          this.params1.contractNo = this.contractNoyxd;
          this.contractNoyxd = "";
        } else {
          if (this.$refs.yxdmergerinfo.AccessdataSource.length) {
            let len_ = this.$refs.yxdmergerinfo.AccessdataSource.length;
            this.getOrderList({ Status: "20", proOrderNo: this.$refs.yxdmergerinfo.AccessdataSource[len_ - 1].proOrderNo });
            this.params1.proOrderNo = this.$refs.yxdmergerinfo.AccessdataSource[len_ - 1].proOrderNo;
            this.params1.Status = "20";
          }
        }
      } else if (this.user.factoryId != 22) {
        this.getOrderList({ OrderNo: this.$refs.othermergerinfo.fdorderNo });
        this.params1.OrderNo = this.$refs.othermergerinfo.fdorderNo;
      } else {
        this.getOrderList();
      }
    },
    //指示检查弹窗
    reportHandleCancel2() {
      this.dataVisible2 = false;
      this.fdloading = false;
      this.spinning = false;
      //return this.Quotationcompleted(this.count + 1);
    },
    Quantitysplitting() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要拆分的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.splitVisible = true;
      this.split_num = "";
    },
    //订单锁定
    orderlock() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要锁定的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.isLock = this.$refs.orderTable.selectedRowsData.isLock;
      this.lockCause = this.$refs.orderTable.selectedRowsData.lockCause;
      this.ddataVisible = true;
    },
    //订单删除
    OrderDelete() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要删除的订单");
        return;
      }
      this.dataVisible3 = true;
      this.type = "orderdel";
      this.message = "确认删除选中的订单吗？";
      this.widthy = 400;
    },
    //订单暂停
    Ordersuspension() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要暂停的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.PauseCause = "";
      buttonCheck(this.$refs.orderTable.proOrderId, "OrderPause")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData1 = res.data;
              this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
              this.checkType = "ddzt";
              this.dataVisible22 = true;
            } else {
              this.dataVisible3 = true;
              this.type = "orderpause";
              this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
              this.widthy = 800;
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //订单取消
    ordercancellation() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要取消的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.CancelCause = "";
      this.spinning = true;
      buttonCheck(this.$refs.orderTable.proOrderId, "OrderCancel")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData1 = res.data;
              this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
              this.checkType = "ddqx";
              this.dataVisible22 = true;
            } else {
              this.dataVisible3 = true;
              this.type = "ordercancel";
              this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
              this.message = "确认订单取消吗？";
              this.widthy = 800;
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //文件追加
    fileswereadded() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要文件追加的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      buttonCheck(this.$refs.orderTable.proOrderId, "OrderOfferAppendFile")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData1 = res.data;
              this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
              this.checkType = "wjzj";
              this.dataVisible22 = true;
            } else {
              this.$refs.action.clickUpload1(this.$refs.orderTable.proOrderId);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //文件替换
    uploadPCBFileClick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要文件替换的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("只能选择一个订单进行文件替换");
        return;
      }
      buttonCheck(this.$refs.orderTable.selectedRowKeysArray, "OrderEnquiryUpdateFile").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "wjth";
            this.dataVisible22 = true;
          } else {
            this.$refs.action.clickUpload2();
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //分段上传MD5
    async customRequest(data) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("CustNo", this.$refs.orderTable.selectedRowsData.custNo);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      this.spinning = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              let params = {
                id: this.$refs.orderTable.selectedRowKeysArray[0],
              };
              params.PcbFilePath = res.data.split(",")[0];
              params.pcbFileName = replacedText;
              updateFile(params)
                .then(res => {
                  if (res.code) {
                    this.$message.success("上传成功");
                    this.getOrderList();
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.spinning = false;
                });
            }
          } else {
            this.spinning = false;
            data.onError(res.message);
            i = shardCount;
          }
        });
      }
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    //型号风险
    Modelrisk() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要添加型号风险的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      if (!this.$refs.orderTable.selectedRowsData.proOrderNo) {
        this.$message.warning("该订单不允许生成型号风险警告");
        return;
      }
      this.RiskVisible = true;
      this.RiskContent = "";
    },
    RiskHandleOk() {
      if (!this.RiskContent || this.RiskContent == "") {
        this.$message.error("请输入型号风险内容");
        return;
      }
      this.RiskVisible = false;
      let params = {
        linkType: 2,
        status: 1,
        content: this.RiskContent,
        custNo: this.$refs.orderTable.selectedRowsData.custNo,
        orderNo: this.$refs.orderTable.selectedRowsData.proOrderNo,
        joinFactoryId: [Number(this.$refs.orderTable.selectedRowsData.joinFactoryId)],
      };
      addRiskWarning(params).then(res => {
        if (res.code) {
          this.$message.success(res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 合同核查
    ContractVerification() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择核查订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      contractprecheck(this.$refs.orderTable.selectedRowKeysArray)
        .then(res => {
          if (res.code) {
            this.verificationVisible = true;
            this.verificationData = res.data[0].contractCheck;
            for (let index = 0; index < this.verificationData.length; index++) {
              let val = this.verificationData[index];
              Object.keys(val).forEach(key => {
                val[key] = val[key] ? val[key].toString() : "";
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 订单拆分
    OrderSplitting() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要拆分的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.orderVisible = true;
    },
    //数量拆分
    Splitconfirmation() {
      var x = /^\+?[1-9][0-9]*$/;
      if (!x.test(this.split_num)) {
        this.$message.warning("请输入正确的拆分数量");
        return;
      }
      this.splitVisible = false;
      let params = {
        id: this.$refs.orderTable.selectedRowKeysArray[0],
        splitNum: Number(this.split_num),
      };
      this.spinning = true;
      ordernumsplit(params)
        .then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.getOrderList({ OrderNo: res.data });
            // this.valuationClick();
            // //采用轮询等待spinning关闭(计价操作完成)后查询裂变订单
            // if (this.spinning) {
            //   const waitvaluation = new Promise(resolve => {
            //     const interval = setInterval(() => {
            //       if (!this.spinning) {
            //         clearInterval(interval);
            //         resolve();
            //       }
            //     }, 100); // 每100毫秒检查一次
            //   });
            //   waitvaluation.then(() => {
            //     this.getOrderList({ OrderNo: res.data });
            //   });
            // } else {
            //   this.getOrderList({ OrderNo: res.data });
            // }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleOkorder() {
      const form = this.$refs.orderForm;
      form.validate(valid => {
        if (valid) {
          let params = {
            id: this.$refs.orderTable.selectedRowKeysArray[0],
            splitNum: Number(this.orderForm.splitNum),
          };
          getOrderSplit(params)
            .then(res => {
              if (res.code) {
                this.$message.success("拆分成功");
                this.getOrderList({
                  PcbFileName: this.$refs.orderTable.selectedRowsData.pcbFileName,
                });
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.orderVisible = false;
            });
        }
      });
    },
    hhandleOk() {
      let val = this.$refs.lockinfo.val;
      val.id = this.$refs.orderTable.proOrderId;
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'订单锁定',})
      this.Buriedpointcache(this.buryingpoint);
      orderlocked(val).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          this.getOrderList("1");
        } else {
          this.$message.error(res.message);
        }
      });
      this.ddataVisible = false;
    },
    mapKey(data) {
      if (!data || data.length == 0) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    marketmodification() {
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      }
      this.marketdataVisible = true;
      this.formdata = {
        address: false,
        price: false,
        deliverydate: false,
        num: false,
        PO: false,
        parameters: false,
        FileReplacement: false,
      };
    },
    // 订单恢复
    OrderRecovery() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.dataVisible3 = true;
      this.widthy = 400;
      this.message = "确认恢复订单吗?";
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.type = "5";
    },
    markethandleOk() {
      let arr = [];
      if (!Object.values(this.formdata).some(value => value)) {
        this.$message.warning("请至少勾选一项再保存");
        return;
      }
      if (this.formdata.PO == true) {
        arr.push("PO");
      }
      if (this.formdata.address == true) {
        arr.push("地址");
      }
      if (this.formdata.deliverydate == true) {
        arr.push("交期");
      }
      if (this.formdata.num == true) {
        arr.push("数量");
      }
      if (this.formdata.price == true) {
        arr.push("价格");
      }
      if (this.formdata.parameters == true) {
        arr.push("预审参数");
      }
      if (this.formdata.FileReplacement == true) {
        arr.push("文件替换");
      }
      let content = arr.join(";");
      let parmas = {
        content: content,
        orderModifyType: 1,
        orderNo: this.$refs.orderTable.selectedRowsData.orderNo,
        proOrderNo: this.$refs.orderTable.selectedRowsData.proOrderNo,
      };
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'市场修改',})
      this.Buriedpointcache(this.buryingpoint);
      this.spinning = true;
      setordermodifylist(parmas)
        .then(res => {
          if (res.code) {
            this.$message.success("市场修改成功");
            if (this.formdata.PO == true || this.formdata.address == true) {
              this.$refs.orderTable.selectedRowsData.orderModify = 1;
              this.goDetail1(this.$refs.orderTable.selectedRowsData);
            }
            if (this.formdata.parameters == true) {
              this.$refs.orderTable.selectedRowsData.orderModify = 1;
              this.$refs.orderTable.goDetail(this.$refs.orderTable.selectedRowsData, "bianji");
            }
            if (JSON.stringify(this.params1) != "{}") {
              this.getOrderList(this.params1);
            } else {
              this.getOrderList();
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
      this.marketdataVisible = false;
    },
    queryClick() {
      this.dataVisible = true;
    },
    getmail(record) {
      document.getElementsByClassName("dragging2")[0].style.display = "block";
      this.attList = [];
      this.mailid = record.mailId;
      emimeconent(record.mailId)
        .then(res => {
          if (res.code) {
            this.showTitle = res.data.subject;
            this.messageList = Base64.decode(res.data.htmlBody);
            res.data.atts.forEach(ite => {
              this.attList.push(ite.attName + "attid" + ite.id);
            });
            this.MaildataVisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          var arr = document.getElementsByClassName("handle");
          for (var i = 0; i < arr.length; i++) {
            arr[i].style.display = "block";
          }
        });
    },
    getDetailInfo(id) {
      this.spinning = true;
      document.getElementsByClassName("dragging1")[0].style.display = "block";
      getEditOrderInfo(id)
        .then(res => {
          if (res.code) {
            this.showData = res.data;
            this.PredataVisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          var arr = document.getElementsByClassName("handle");
          for (var i = 0; i < arr.length; i++) {
            arr[i].style.display = "block";
          }
        });
    },
    handleOk() {
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'查询',})
      this.Buriedpointcache(this.buryingpoint);

      this.params1 = this.$refs.queryInfo.form;
      var arr1 = this.params1.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      this.params1.OrderNo = arr1.join("");
      var arr2 = this.params1.custNo.split("");
      if (arr2.length > 10) {
        arr2 = arr2.slice(0, 10);
      }
      this.params1.custNo = arr2.join("");
      var arr3 = this.params1.PcbFileName.split("");
      if (arr3.length > 100) {
        arr3 = arr3.slice(0, 100);
      }
      this.params1.PcbFileName = arr3.join("");
      var arr4 = this.params1.proOrderNo.split("");
      if (arr4.length > 20) {
        arr4 = arr4.slice(0, 20);
      }
      this.params1.proOrderNo = arr4.join("");
      this.pagination.current = 1;
      if (this.params1.proOrderNo && typeof this.params1.proOrderNo === "string" && this.params1.proOrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产型号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      localStorage.setItem("bjqueryInfo", JSON.stringify(this.params1));
      this.getOrderList(this.params1);
      this.$refs.orderTable.selectedRowKeysArray = [];
      this.$refs.orderTable.selectedRowsData = {};
      this.dataSource2 = [];
      this.Source2 = [];
      this.dataSource3 = [];
      this.dataSource4 = [];
    },
    systemhandleOk() {
      this.Buriedpointcache(this.buryingpoint);
      let params = this.exchangerate;
      setorderpricehL(params).then(res => {
        if (res.code) {
          this.$message.success("修改成功");
        } else {
          this.$message.warning(res.message);
        }
      });
      this.systemdataVisible = false;
    },
    systemparameter() {
      orderpricehL().then(res => {
        if (res.code) {
          this.exchangerate = res.data;
          this.systemdataVisible = true;
        } else {
          this.$message.warning(res.message);
        }
      });
    },
    addareturnorder() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      buttonCheck(this.$refs.orderTable.proOrderId, "PreVerifyOrderAddNope")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData1 = res.data;
              this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
              this.checkType = "fdxz";
              this.dataVisible22 = true;
            } else {
              this.returncustno = this.$refs.orderTable.selectedRowsData.custNo;
              custgrouplist(this.$refs.orderTable.proOrderId).then(res => {
                if (res.code) {
                  this.frontDataZSupplier1 = res.data;
                }
              });
              prenopeinfo(this.$refs.orderTable.proOrderId)
                .then(res => {
                  if (res.code) {
                    this.popupdata = res.data;
                    this.dataVisible3 = true;
                    this.widthy = 400;
                    // this.message= '确认返单新增吗?'
                    this.getData(this.$refs.orderTable.selectedRowsData.joinFactoryId);
                    this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
                    this.type = "newlyadded";
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.spinning = false;
                });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    addorder() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.returOrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      this.returCust = this.$refs.orderTable.selectedRowsData.pcbFileName;
      this.getData(this.$refs.orderTable.selectedRowsData.joinFactoryId);
      prenopeinfo(this.$refs.orderTable.proOrderId).then(res => {
        if (res.code) {
          this.val = res.data;
          this.dataVisible8 = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //返单更改弹窗
    changeorder() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.returOrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      this.returCust = this.$refs.orderTable.selectedRowsData.pcbFileName;
      buttonCheck(this.$refs.orderTable.proOrderId, "PreVerifyOrderAlter").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "fdgg";
            this.dataVisible22 = true;
          } else {
            nopealter(this.$refs.orderTable.proOrderId).then(res => {
              if (res.code) {
                this.dataVisible9 = true;
                this.redata = res.data;
              } else {
                this.$message.error(res.message);
              }
            });
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //返单更改弹窗
    changeOrderType() {
      if (this.$refs.editForm1.formData.reOrder == 0) {
        this.$message.warning("订单类型不能更改为新单");
        this.$refs.editForm1.formData.reOrder = this.$refs.orderTable.selectedRowsData.reOrder.toString();
        return;
      }
      this.Ecninfo = {};
      if (this.$refs.editForm1.formData.reOrder == 2) {
        pcbnopeinfo(this.saveID).then(res => {
          if (res.code) {
            this.Ecninfo = res.data;
            this.OrderTypeVisible = true;
            this.Ecninfo.customClassification = "";
            this.Rebates = this.mapKey(this.selectOption.CustomClassification).filter(item => item.value != "返单无改");
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.Ecninfo = {};
      }
    },
    // 销售信息弹窗
    goDetail1(record) {
      this.spinning = true;
      this.InfoorderNo = record.orderNo;
      this.$refs.orderTable.selectedRowKeysArray = [];
      this.$refs.orderTable.selectedRowKeysArray.push(record.id);
      if (record.id) {
        this.saveID = record.id;
        this.getData(record.joinFactoryId);
        getEditOrderInfo(record.id)
          .then(res => {
            if (res.code) {
              this.showData = res.data;
              this.dataVisible6 = true;
              localStorage.setItem("Testingmethod", this.showData.flyingProbe);
              setTimeout(() => {
                $(".ant-modal-header").on("mousedown", e => {
                  this.startPosition.x = e.pageX;
                  this.startPosition.y = e.pageY;
                  this.startPosition.offsetX = e.offsetX;
                  this.startPosition.offsetY = e.offsetY;
                  this.isMovedown = true;
                  let a = document.getElementsByClassName("xiaoshou")[0];
                  a.style.webkitUserSelect = "none"; // Chrome、Safari 和 Opera
                  a.style.mozUserSelect = "none"; // 火狐
                  a.style.msUserSelect = "none"; // IE 和 Edge
                  a.style.userSelect = "none"; // 标准写法
                });
              }, 200);
              document.body.addEventListener("mousemove", e => {
                if (this.isMovedown) {
                  if (
                    e.x - this.startPosition.x > 10 ||
                    e.y - this.startPosition.y > 10 ||
                    e.x - this.startPosition.x < -10 ||
                    e.y - this.startPosition.y < -10
                  ) {
                    let w = $(".ant-modal-content").width();
                    let h = $(".ant-modal-content").height();
                    $(".ant-modal-content").css({
                      left: e.pageX - this.startPosition.offsetX - (document.body.clientWidth - w) / 2 + "px",
                      // top: e.pageY -(document.body.clientHeight-3*h)/2 - 750 + 'px'
                    });
                  }
                }
              });
              document.body.addEventListener("mouseup", e => {
                this.isMovedown = false;
                let a = document.getElementsByClassName("xiaoshou")[0];
                a.style.webkitUserSelect = "text"; // Chrome、Safari 和 Opera
                a.style.mozUserSelect = "text"; // 火狐
                a.style.msUserSelect = "text"; // IE 和 Edge
                a.style.userSelect = "text"; // 标准写法
              });
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    handleOk8() {
      var r = /^\+?[1-9][0-9]*$/;
      let val = this.$refs.returInfo.val;
      let params = {
        custNo: val.custNo,
        num: Number(val.num),
        delType: val.delType,
        boardArea: Number(val.boardArea),
        orderAttribute: val.orderAttribute,
        flyingProbe: val.flyingProbe,
        testPointNum: Number(val.testPointNum),
        openedTestrack: val.openedTestrack,
        priceType: val.priceType,
        deliveryDate: val.deliveryDate,
        id: this.$refs.orderTable.proOrderId,
        reOrderNo: this.$refs.orderTable.selectedRowsData.orderNo,
      };
      if (val.num && !r.test(val.num)) {
        this.$message.warning("交货数量请输入正整数");
        return;
      }
      if (!val.delType) {
        this.$message.warning("请选择交货单位");
        return;
      }
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'返单新增',})
      this.Buriedpointcache(this.buryingpoint);
      preaddnope(params).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
        } else {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "fdxx";
            this.dataVisible22 = true;
          } else {
            this.$message.error(res.message);
          }
        }
      });
      this.dataVisible8 = false;
    },
    handleOk9() {
      this.fdloading = true;
      let redata = this.$refs.modifyinfo.redata;
      redata.ids = this.$refs.orderTable.proOrderId;
      redata.FilePath = this.$refs.modifyinfo.FilePath;
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'返单更改',})
      this.Buriedpointcache(this.buryingpoint);
      this.spinning = true;
      addnopealter(redata)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
            let val = { proOrderNo: res.data.split(",")[1] };
            this.getOrderList(val);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.fdloading = false;
          this.spinning = false;
        });
      this.dataVisible9 = false;
    },
    handleOk7() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.OrderTypeVisible = false;
        } else {
          return false;
        }
      });
    },
    handleOk6() {
      const form = this.$refs.editForm1.$refs.ruleForm1;
      let row = this.$refs.orderTable.selectedRowsData;
      var i = /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/;
      var x = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      let params = this.$refs.editForm1.formData;
      params.eqdtos = this.$refs.editForm1.tableDetails;
      params.mktNote = params.mktNote ? params.mktNote.replace(/\s+/g, " ") : "";
      params.costNote = params.costNote ? params.costNote.replace(/\s+/g, " ") : "";
      params.note = params.note ? params.note.replace(/\s+/g, " ") : "";
      params.custPo = params.custPo ? params.custPo.replace(/\s+/g, " ") : "";
      params.customerModel = params.customerModel ? params.customerModel.replace(/\s+/g, " ") : "";
      params.customerMaterialNo = params.customerMaterialNo ? params.customerMaterialNo.replace(/\s+/g, " ") : "";
      if (this.$refs.editForm1.needReportList) {
        params.needReportList = this.$refs.editForm1.needReportList.toString();
      }
      if (!params.halfProcess) {
        params.halfProcess = null;
      }
      if (!params.referencePrice1) {
        params.referencePrice1 = null;
      }
      if (!params.referencePrice2) {
        params.referencePrice2 = null;
      }
      if (params.sparePartNum) {
        params.sparePartNum = params.sparePartNum.toString();
      }
      if (!params.delType) {
        this.$message.info("请填写交货单位");
        return;
      }
      if (!params.batchType && (row.joinFactoryId == 77 || row.joinFactoryId == 100)) {
        this.$message.warning("请选择批量类型");
        return;
      }
      if (!params.transportationFactory && [78, 79, 80].includes(Number(row.joinFactoryId))) {
        this.$message.warning("请选择运输工厂");
        return;
      }
      if (!params.orderDirection && [58, 59, 78, 79, 80].includes(Number(row.joinFactoryId))) {
        this.$message.warning("请选择加工工厂");
        return;
      }
      if (params.eqEmail && !x.test(params.eqEmail)) {
        this.$message.info("请填写正确的邮箱格式");
        return;
      }
      if (params.eqPhoneNumber && !i.test(params.eqPhoneNumber)) {
        this.$message.info("请填写正确的电话格式");
        return;
      }
      params.id = this.saveID;
      params.contractFactoryId = params.contractFactoryId ? Number(params.contractFactoryId) : null;
      if (this.selectOption.ContractFactoryId && this.selectOption.ContractFactoryId.length) {
        params.contractFactoryName = this.selectOption.ContractFactoryId.filter(ite => ite.valueMember == params.contractFactoryId)[0]?.text;
      } else {
        params.contractFactoryName = this.showData.contractFactoryName;
      }
      params.reOrder = Number(params.reOrder);
      params.identificationType = Number(params.identificationType);
      params.NopeInfo = JSON.stringify(this.Ecninfo) == "{}" ? null : this.Ecninfo;
      this.spinning = true;
      SaleOrderInfo(params).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          this.dataVisible6 = false;
          let Testingmethod = localStorage.getItem("Testingmethod");
          if (Testingmethod != params.flyingProbe) {
            this.copyUpOrDownPrice();
            buttonCheck(params.id, "PriceCalc").then(res => {
              if (res.code) {
                if (res.data.length == 0) {
                  this.$refs.action.disfinish = true;
                  this.spinning = true;
                  orderPriceCalc(params.id)
                    .then(res => {
                      if (res.code) {
                        if (this.dataSource2.length > 1) {
                          for (let index = 0; index < this.dataSource2.length; index++) {
                            const element = this.dataSource2[index];
                            if (element.para4DelQty_ && !element.para4Delivery_) {
                              this.deliverycalculation1(element);
                            }
                          }
                        }
                        this.changeUpOrDownPrice();
                        this.getOrderDetail(params.id);
                      }
                    })
                    .finally(() => {
                      this.$refs.action.disfinish = false;
                      this.spinning = false;
                    });
                } else {
                  this.spinning = false;
                }
              } else {
                this.spinning = false;
              }
            });
          } else {
            this.getOrderDetail(params.id);
            this.spinning = false;
          }
          //}
          //this.getOrderList()
        } else {
          this.$message.error(res.message);
          this.spinning = false;
          this.showData.reOrder = this.showData.reOrder !== null && this.showData.reOrder !== "" ? this.showData.reOrder.toString() : null;
          this.showData.contractFactoryId = this.showData.contractFactoryId ? this.showData.contractFactoryId.toString() : "";
        }
      });
    },
    // 订单表变化change
    handleTableChange(pagination, filters, sorter) {
      let { columnKey, order } = sorter;
      this.iscolumnKey = columnKey;
      this.isorder = order;
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.pageStat = false;
      localStorage.removeItem("stat");
      localStorage.setItem("bjqueryInfo", JSON.stringify(this.params1));
      if (JSON.stringify(this.params1) != "{}") {
        this.getOrderList(this.params1);
      } else {
        this.getOrderList();
      }
    },
    // 行点击事件
    isRedRow(record) {
      if (record.proOrderId == this.proOrderId) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    isRedRow2(record) {
      if (record.id == this.id) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    isRedRow3(record) {
      if (record.guid4Parameter + record.guid4Order + record.guid4Calc == this.guid4Parameter) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    isRedRow4(record) {
      if (record.guid4Parameter + record.guid4Order + record.guid4Calc == this.Parameter) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 开始
    MakeStartClick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      buttonCheck(this.$refs.orderTable.proOrderId, "VerifyStartOrder").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "bjks";
            this.dataVisible22 = true;
          } else {
            this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
            this.dataVisible3 = true;
            this.widthy = 400;
            this.message = "确认审核开始吗？";
            this.type = "1";
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //解除警告
    Releasewarning() {
      if (this.$refs.orderTable.selectedRowKeysArray.length < 1) {
        this.$message.warning("请选择需要警告的订单");
        return;
      } else if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.dataVisible3 = true;
      this.widthy = 400;
      this.message = "确认解除警告吗？";
      this.type = "6";
    },
    //在线ECN
    onlineecn() {
      if (this.$refs.orderTable.selectedRowKeysArray.length < 1) {
        this.$message.warning("请选择需要在线ECN的订单");
        return;
      } else if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.joinFactoryId = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      this.OnLineOrRecordEcn = 0;
      this.Upgradeversion = false;
      buttonCheck(this.$refs.orderTable.proOrderId, "VerifyOnLineECN").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "ecn";
            this.dataVisible22 = true;
          } else {
            this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
            this.dataVisible3 = true;
            this.widthy = 400;
            this.message = "确认在线ECN吗?";
            this.type = "7";
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 完成
    modifyInfoClick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      if (this.dataSource2.length == 2 && !this.dataSource2[0].isProduction) {
        this.dataSource2[0].isProduction = true;
        setIsProduction(this.dataSource2[0].id).then(res => {
          if (res.code) {
            // this.$message.success('设置下单成功')
            this.getOrderDetail(this.$refs.orderTable.proOrderId);
          } else {
            this.$message.error(res.message);
            record.isProduction = false;
            return;
          }
        });
        // this.CheckClick(this.dataSource2[0])
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.dataVisible3 = true;
      this.widthy = 400;
      this.message = "确认审核完成吗？";
      this.type = "2";
    },
    // 计价
    valuationClick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要计价的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      let id = this.$refs.orderTable.proOrderId;
      this.copyUpOrDownPrice();
      buttonCheck(id, "PriceCalc").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "jijia";
            this.dataVisible22 = true;
          } else {
            this.$refs.action.disfinish = true;
            if (this.$refs.orderTable.selectedRowsData.reOrder == 1) {
              //返单点击计价按钮之前先计算交期 0425曾经理要求
              orderDayCalc(id).then(res => {
                if (res.code) {
                  this.Valuation(id);
                } else {
                  this.$message.error(res.message);
                  this.spinning = false;
                }
              });
            } else {
              this.Valuation(id);
            }
          }
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      });
    },
    Valuation(id) {
      orderPriceCalc(id)
        .then(res => {
          this.spinning = true;
          if (res.code) {
            this.$message.success("计价成功");
            if (this.dataSource2.length > 1) {
              for (let index = 0; index < this.dataSource2.length; index++) {
                const element = this.dataSource2[index];
                if (element.para4DelQty_ && !element.para4Delivery_) {
                  this.deliverycalculation1(element);
                }
              }
            }
            this.getOrderDetail(id);
            this.changeUpOrDownPrice();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.$refs.action.disfinish = false;
          this.spinning = false;
        });
    },
    // 改价
    ChangePrice() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要计价的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 12 && this.$refs.orderTable.selectedRowsData.status == "待付款") {
        this.changeprice(this.$refs.orderTable.proOrderId, true);
      }
      this.PriceFlag = true;
    },
    ChangePriceok() {
      this.PriceFlag = false;
      if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 12 && this.$refs.orderTable.selectedRowsData.status == "待付款") {
        if (this.gjloading) {
          // 如果 orderPriceCalc4WinForm 正在执行，等待其完成
          // 使用 Promise 来等待 gjloading 变为 false
          const waitForCompletion = new Promise(resolve => {
            const interval = setInterval(() => {
              if (!this.gjloading) {
                clearInterval(interval);
                resolve();
              }
            }, 100); // 每100毫秒检查一次
          });
          // 当 gjloading 变为 false 时，执行 verifychangeprice
          waitForCompletion.then(() => {
            this.changeprice(this.$refs.orderTable.proOrderId, false);
          });
        } else {
          this.changeprice(this.$refs.orderTable.proOrderId, false);
        }
      }
    },
    changeprice(id, isBegin) {
      verifychangeprice(id, isBegin).then(res => {});
    },
    priceChange() {
      this.priceChangeFlg = true;
    },
    priceChange1(record) {
      record.upOrDownPrice = (Number(record.actualPrice) - Number(record.standardPrice)).toString();
      this.priceChange();
    },
    PriceChange1(record) {
      this.PriceChange(record);
    },
    async PriceChange(record) {
      var element = [];
      for (let index = 0; index < this.dataSource4.length; index++) {
        element.push(this.dataSource4[index].upOrDownPrice);
      }
      let currencyStr = this.$refs.orderTable.selectedRowsData.currencyStr;
      let protocolCurrencyStr = this.$refs.orderTable.selectedRowsData.protocolCurrencyStr;
      if (currencyStr && currencyStr != "RMB" && protocolCurrencyStr && protocolCurrencyStr != currencyStr && this.priceChangeFlg) {
        try {
          const res = await orderpricehL();
          if (res.code && res.data.length) {
            let rate = res.data.filter(item => item.scurrency_ == currencyStr)[0].rate_;
            record.upOrDownPrice = (record.upOrDownPrice * rate).toString();
          }
        } catch (error) {
          //console.error('Error in iteration:', i, error);
        }
      }
      if (record.upOrDownPrice == this.upOrDownPrice) {
        return;
      }
      var r = /^(0|[1-9][0-9]*)(\.\d+)?$/;
      var s = /^-\d+(.\d+)?$/;
      let arr1 = this.dataSource3.filter(item => {
        return item.priceName.indexOf("制板单价") != -1 && item.upOrDownPrice;
      });
      let arr2 = this.dataSource3.filter(item => {
        return item.priceName.indexOf("平米价") != -1 && item.upOrDownPrice;
      });
      if (arr1.length && arr2.length) {
        this.$message.warning("制板单价和平米价不能同时修改!");
      }
      if (this.upOrDownPrice != record.upOrDownPrice) {
        if (record.priceName == "钻孔" && !r.test(record.upOrDownPrice)) {
          this.$message.warning("加成费用只能录入正值!");
          record.upOrDownPrice = "";
          return;
        }
        if (record.priceName == "优惠" && !s.test(record.upOrDownPrice)) {
          this.$message.warning("优惠费用只能录入负值!");
          record.upOrDownPrice = "";
          return;
        }
        let arr = [];
        await pricelistbypcbid(this.$refs.orderTable.proOrderId).then(res => {
          if (res.code) {
            res.data.forEach(record => {
              (record.key = record.guid4Calc + record.guid4Order + record.guid4Parameter), arr.push(record);
            });
          }
        });
        this.loading3 = true;
        this.loading4 = true;
        //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'改价',})
        this.Buriedpointcache(this.buryingpoint);
        this.gjloading = true;
        this.$refs.action.disfinish = true;
        this.spinning = true;
        record.tag_ = this.row3.tag_;
        orderPriceCalc4WinForm(record)
          .then(res => {
            if (res.code) {
              this.$message.success("改价成功");
              //this.PriceFlag = false
              this.oldPrice = this.upOrDownPrice;
            } else {
              this.$message.error(res.message);
              this.PriceFlag = false;
            }
          })
          .finally(() => {
            if (this.$refs.orderTable.selectedRowKeysArray.length) {
              console.log("4870");
              this.getOrderDetail(this.$refs.orderTable.selectedRowKeysArray[0], "ChangePrice");
            }
            if (this.row2) {
              this.getResultList(this.row2, "price");
            }
            if (this.row3) {
              this.getResultDetailList(this.row3);
            }
            let data1 = [];
            if (record.isShow) {
              //修改为中间列表
              arr.forEach(item => {
                if (!item.isShow && item.upOrDownPrice) {
                  data1.push(item);
                }
              });
            }
            this.datePrice(data1);
            this.loading3 = false;
            this.loading4 = false;
            this.gjloading = false;
            this.priceChangeFlg = false;
            this.spinning = false;
            this.$refs.action.disfinish = false;
          });
      }
    },
    //LT成本分析
    costanalysis() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      ltCostEXLEV2({ id: this.$refs.orderTable.selectedRowKeysArray }).then(res => {
        if (res.code) {
          // let content = "/costanalysis?id=" + this.$refs.orderTable.selectedRowKeysArray;
          // window.open(content);
          this.downcostanalysis(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downcostanalysis(url, name) {
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = name;
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      };
      xhr.send();
    },
    quotationcontract() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.buttonload = true;
      this.joinFactoryId = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      this.salescustno = this.$refs.orderTable.selectedRowsData.custNo;
      this.Buriedpointcache(this.buryingpoint);
      buttonCheck(this.$refs.orderTable.proOrderId, "VerifyContract").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "xsht";
            this.dataVisible22 = true;
            this.spinning = false;
          } else {
            contractNo(this.$refs.orderTable.selectedRowKeysArray).then(res => {
              if (res.code) {
                if (this.joinFactoryId == "58" || this.joinFactoryId == "59" || this.joinFactoryId == "65" || this.joinFactoryId == "38") {
                  this.YXDform();
                } else if (this.joinFactoryId == "22") {
                  this.JZsalescontract();
                } else if (this.joinFactoryId == "69") {
                  this.hmform();
                } else if (this.joinFactoryId == "70") {
                  this.mtgxform();
                } else if (this.joinFactoryId == "12") {
                  this.BQsalescontract();
                } else if (this.joinFactoryId == "67" || this.joinFactoryId == "97" || this.joinFactoryId == "98") {
                  this.LTsalescontract();
                } else if (this.joinFactoryId == "77" || this.joinFactoryId == "100") {
                  this.CRsalescontract();
                } else if ([78,79,80].includes(Number(this.joinFactoryId))) {
                  this.BCsalescontract();
                } else {
                  getcontractinfobygrp(this.$refs.orderTable.selectedRowKeysArray)
                    .then(res => {
                      if (res.code) {
                        this.downloadByteArrayFromString(res.data, res.message);
                      } else {
                        this.$message.error(res.message);
                      }
                    })
                    .finally(() => {
                      this.buttonload = false;
                    });
                }
              } else {
                this.$message.error(res.message);
                this.buttonload = false;
              }
            });
          }
        } else {
          this.$message.error(res.message);
          this.buttonload = false;
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    exportExcelFile(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      return XLSX.writeFile(workBook, fileName);
    },
    exportExcel() {
      // 创建一个工作簿
      const wb = XLSX.utils.book_new();
      // 根据 checkcolumns 生成表头
      const headers = this.checkcolumns.map(col => col.title);
      // 生成数据数组，包含序号
      const dataWithHeaders = [
        headers,
        ...this.verificationData.map((item, index) => {
          return [
            index + 1 == this.verificationData.length ? "合计" : (index + 1).toString(), // 序号
            ...this.checkcolumns.slice(1).map(col => {
              return item[col.dataIndex] || ""; // 如果数据不存在，返回空字符串
            }),
          ];
        }),
      ];
      // 将数据转换为工作表
      const ws = XLSX.utils.aoa_to_sheet(dataWithHeaders);
      // 计算列宽
      const range = XLSX.utils.decode_range(ws["!ref"]);
      for (let C = range.s.c; C <= range.e.c; ++C) {
        let maxLen = 0;
        for (let R = range.s.r; R <= range.e.r; ++R) {
          const cell = ws[XLSX.utils.encode_cell({ r: R, c: C })];
          if (cell && cell.t === "s") {
            maxLen = Math.max(maxLen, cell.v.length);
          }
        }
        ws["!cols"] = ws["!cols"] || [];
        ws["!cols"][C] = { wch: Math.max(maxLen + 5, 15) }; // 设置最小宽度为15
      }
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      // 导出 Excel 文件
      XLSX.writeFile(wb, this.$refs.orderTable.selectedRowsData.custNo + "合同核查数据.xlsx");
    },

    //发送合同
    sendcontractemail() {
      let arr = this.$refs.orderTable.selectedRowKeysArray;
      if (!arr[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      let data = this.orderListData.filter(item => arr.includes(item.id));
      let val = data.filter(item => !item.mailId);
      if (val.length) {
        this.$message.error("非邮件创建订单,无法使用该功能");
        return;
      }
      this.buttonload = true;
      buttonCheck(this.$refs.orderTable.proOrderId, "ContractMEil").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "fshtMEil";
            this.dataVisible22 = true;
          } else {
            this.spinning = true;
            verifyQuotation(arr).then(res => {
              if (res.code) {
                this.debouncedsendcontract(arr);
              } else {
                this.checkData1 = res.data;
                this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
                this.dataVisible22 = true;
                this.checkType = "fsht";
                this.spinning = false;
                this.buttonload = false;
              }
            });
          }
        } else {
          this.$message.error(res.message);
          this.buttonload = false;
        }
      });
    },
    sendcontract(arrs) {
      this.spinning = true;
      this.buttonload = true;
      getQuotationInfoByGrp(arrs).then(res => {
        if (res.code) {
          let params = {
            ids: arrs,
            data: res.data,
            fileName: res.message,
          };
          sendcontracteMime(params)
            .then(res => {
              if (res.code) {
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.spinning = false;
              this.buttonload = false;
            });
        } else {
          this.spinning = false;
          this.buttonload = false;
          this.$message.error(res.message);
        }
      });
    },
    //报价单
    quotationClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.checkType = "bjd";
      let data = this.$refs.orderTable.selectedRowsData;
      this.Buriedpointcache(this.buryingpoint);
      this.spinning = true;
      verifyQuotation(this.$refs.orderTable.selectedRowKeysArray)
        .then(res => {
          if (res.code) {
            if (data.custNo == "806" && data.joinFactoryId == "22") {
              jz806EXLE(this.$refs.orderTable.selectedRowKeysArray).then(res => {
                if (res.code) {
                  if (JSON.parse(res.data).length) {
                    let _self = this;
                    let str = "";
                    let cutStr = "";
                    const arr = _self.$refs.orderTable.selectedRowKeysArray;
                    for (var a = 0; a < arr.length; a++) {
                      if (
                        _self.orderListData.filter(item => {
                          return item.id == arr[a];
                        }).length
                      ) {
                        if (str == "") {
                          str = _self.orderListData.filter(item => {
                            return item.id == arr[a];
                          })[0].pcbFileName;
                        } else {
                          str =
                            str +
                            " + " +
                            _self.orderListData.filter(item => {
                              return item.id == arr[a];
                            })[0].pcbFileName;
                        }
                        cutStr =
                          "(" +
                          _self.orderListData.filter(item => {
                            return item.id == arr[a];
                          })[0].custNo +
                          ")";
                      }
                    }
                    this.exportExcelFile(JSON.parse(res.data), "表1", str + cutStr + ".xlsx");
                  } else {
                    this.$message.error("暂无数据");
                  }
                } else {
                  this.$message.error(res.message);
                }
              });
            } else if (data.custNo == "862" && data.joinFactoryId == "22") {
              let params = {
                type: "",
                id: this.$refs.orderTable.selectedRowKeysArray,
              };
              jz862EXLE(params).then(res => {
                if (res.code) {
                  if (res.data) {
                    this.downloadByteArrayFromString(res.data, res.message);
                  } else {
                    this.$message.error("暂无数据");
                  }
                } else {
                  this.$message.error(res.message);
                }
              });
            } else if (data.joinFactoryId == "22") {
              this.JZreportform();
            } else if (data.joinFactoryId == "70") {
              this.MTGXreportform();
            } else if (data.joinFactoryId == "77" || data.joinFactoryId == "100") {
              this.CRreportform();
            } else if (data.joinFactoryId == "58" || data.joinFactoryId == "59") {
              this.YXDreportform();
            } else if (data.joinFactoryId == "12") {
              if (
                data.custNo == "W115" ||
                data.custNo == "W116" ||
                data.custNo == "W072" ||
                (data.custNo.indexOf("W") == -1 && data.custNo.indexOf("Y") == -1)
              ) {
                // 内销
                this.BQreportform("nx");
              } else {
                // 外销
                this.BQreportform("wx");
              }
            } else if (data.joinFactoryId == "67" || data.joinFactoryId == "97" || data.joinFactoryId == "98") {
              this.LTreportform();
            } else if ([78,79,80].includes(Number(data.joinFactoryId))) {
              this.BCreportform();
            } else {
              this.checkClick();
            }
          } else {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.dataVisible22 = true;
            this.checkType = "bjd";
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    JZsalescontract() {
      //精焯销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      frontcontractreport(this.$refs.orderTable.selectedRowKeysArray)
        .then(res => {
          if (res.code) {
            this.JZsalesdata = res.data;
            this.JZsalesvisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    BQsalescontract(ids) {
      //奔强销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.BQsalesdata = res.data;
            this.BQsalesvisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    JZreview() {
      //精焯评审单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      reviewreport(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          this.JZreviewvisible = true;
          this.JZreviewdata = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    JZreportform() {
      //精焯报价表单 除806客户代码
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.JZreportdata = res.data;
          this.JZreportvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
      this.selectlength = this.$refs.orderTable.selectedRowKeysArray.length;
      this.widthy1 = this.selectlength == "1" ? 850 : 1250;
    },
    CRreportform() {
      //诚瑞报价表单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      this.spinning = false;
      orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.CRreportdata = res.data;
          this.CRreportvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    BQreportform(type) {
      //奔强报价表单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      if (type == "nx") {
        if (this.$refs.orderTable.selectedRowKeysArray.length > 5) {
          this.$message.warning("打印报价表单最多选择五条");
          return;
        }
      } else if (type == "wx") {
        if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
          this.$message.warning("打印报价表单最多选择一条");
          return;
        }
      }

      this.bqreport_type = type;
      this.spinning = true;
      orderformreport(this.$refs.orderTable.selectedRowKeysArray)
        .then(res => {
          if (res.code) {
            this.Bqreportdata = res.data;
            this.BQreportvisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    MTGXreportform() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.Mtgxreportdata = res.data;
          this.Mtgxreportvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //本川报价表单
    BCreportform() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      contractNo(this.$refs.orderTable.selectedRowKeysArray).then(item => {
        if (item.code) {
          quotationmodel(this.$refs.orderTable.selectedRowKeysArray[0]).then(val => {
            if (val.code) {
              if (this.$refs.orderTable.selectedRowsData.custNo == "AF0184") {
                this.bcformType = "0184";
              } else if (val.data && val.data.indexOf("外销") != -1) {
                this.bcformType = "wx";
              } else {
                this.bcformType = "nx";
              }
              orderformreport(this.$refs.orderTable.selectedRowKeysArray)
                .then(res => {
                  if (res.code) {
                    this.Bcreportdata = res.data;
                    this.Bcreportvisible = true;
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.spinning = false;
                });
            } else {
              this.spinning = false;
            }
          });
        } else {
          this.spinning = false;
          this.$message.error(item.message);
        }
      });
    },
    LTreportform() {
      //龙腾报价表单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      this.spinning = true;
      contractNo(this.$refs.orderTable.selectedRowKeysArray).then(item => {
        if (item.code) {
          quotationmodel(this.$refs.orderTable.selectedRowKeysArray[0]).then(val => {
            if (val.code) {
              if (val.data == "类型1") {
                let params = {};
                params.type = val.data;
                params.Id = this.$refs.orderTable.selectedRowKeysArray;
                ltQuotationEXLE(params)
                  .then(res => {
                    if (res.code) {
                      this.downloadByteArrayFromString(res.data, res.message);
                    } else {
                      this.$message.error(res.message);
                    }
                  })
                  .finally(() => {
                    this.spinning = false;
                  });
              } else {
                orderformreport(this.$refs.orderTable.selectedRowKeysArray)
                  .then(res => {
                    if (res.code) {
                      this.LTreportdata = res.data;
                      this.LTreport = true;
                      if (val.data == null || val.data == "") {
                        this.formtypes = "ltdefaultreport";
                      } else {
                        this.formtypes = "ltreportform" + val.data;
                      }
                    } else {
                      this.$message.error(res.message);
                    }
                  })
                  .finally(() => {
                    this.spinning = false;
                  });
              }
            } else {
              this.spinning = false;
            }
          });
        } else {
          this.spinning = false;
          this.$message.error(item.message);
        }
      });
    },
    YXDreportform() {
      //雅信达报价表单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.YXDreportdata = res.data;
          this.YXDreportvisible = true;
          this.salescustno = this.$refs.orderTable.selectedRowsData.custNo;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    YXDform(ids, type, factoryId, custno) {
      //雅信达 晶美 联合多层 销售合同
      this.joinFactoryId = factoryId ? factoryId : this.joinFactoryId;
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      this.salescustno = custno ? custno : this.$refs.orderTable.selectedRowsData.custNo;
      this.yxdids = id;
      if (id.length == 0) {
        if (type == "YXD") {
          this.yxdVisible = false;
        } else {
          this.$message.warning("请选择需要打印的订单");
        }

        return;
      }
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.yxddata = res.data;
            this.yxdVisible = true;
          } else {
            if (ids && ids.length == 0) {
              this.yxdVisible = false;
            } else {
              this.$message.error(res.message);
            }
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    //本川销售合同
    BCsalescontract(ids, type, factoryId, custno) {
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      this.joinFactoryId = factoryId ? factoryId : this.joinFactoryId;
      this.salescustno = custno ? custno : this.$refs.orderTable.selectedRowsData.custNo;
      if (id.length == 0) {
        if (type == "BC") {
          this.BCsalesvisible = false;
        } else {
          this.$message.warning("请选择需要打印的订单");
        }

        return;
      }
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.BCsalesdata = res.data;
            this.BCsalesvisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    CRsalescontract(ids, type, factoryId, custno) {
      //诚瑞销售合同
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      this.joinFactoryId = factoryId ? factoryId : this.joinFactoryId;
      this.salescustno = custno ? custno : this.$refs.orderTable.selectedRowsData.custNo;
      if (id.length == 0) {
        if (type == "CR") {
          this.CRsalesvisible = false;
        } else {
          this.$message.warning("请选择需要打印的订单");
        }

        return;
      }
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.CRsalesdata = res.data;
            this.CRsalesvisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    LTsalescontract(ids, type, factoryId, custno) {
      //龙腾销售合同
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      this.joinFactoryId = factoryId ? factoryId : this.joinFactoryId;
      this.salescustno = custno ? custno : this.$refs.orderTable.selectedRowsData.custNo;
      if (id.length == 0) {
        if (type == "LT") {
          this.LTsalesvisible = false;
        } else {
          this.$message.warning("请选择需要打印的订单");
        }

        return;
      }
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.LTsalesdata = res.data;
            this.LTsalesvisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    handleOkyxd() {
      if (this.joinFactoryId == 58 || this.joinFactoryId == 59) {
        if (this.yxdids && this.yxdids.length == 0) {
          this.yxdVisible = false;
        } else {
          quotationmodel(this.yxdids[0]).then(res => {
            if (res.code) {
              if (res.data == null || res.data == "") {
                this.$refs.reportyxd.getReportPdf();
              } else {
                let params = {};
                params.type = res.data;
                params.Id = this.yxdids;
                yxDOrderpriceEXLE(params)
                  .then(res => {
                    if (res.code) {
                      this.downloadByteArrayFromString(res.data, res.message);
                    } else {
                      this.$message.error(res.message);
                    }
                  })
                  .finally(() => {
                    this.buttonload = false;
                  });
              }
            } else {
              this.buttonload = false;
            }
          });
        }
      } else {
        this.$refs.reportyxd.getReportPdf();
      }

      //this.yxdVisible = false
    },
    handleOkhm() {
      this.$refs.reporthm.getReportPdf();
    },
    handleOkmtgx() {
      this.$refs.reportmtgx.getReportPdf();
    },
    hmform(ids) {
      //红马销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.hmsalesdata = res.data;
            this.hmVisible = true;
          } else {
            if (ids && ids.length == 0) {
              this.hmVisible = false;
            } else {
              this.$message.error(res.message);
            }
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    mtgxform(ids) {
      //明天高新销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.mtgxsalesdata = res.data;
            this.mtgxVisible = true;
          } else {
            if (ids && ids.length == 0) {
              this.mtgxVisible = false;
            } else {
              this.$message.error(res.message);
            }
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    salescontractdown() {
      this.$refs.jzsalescontract.getsalesPdf();
    },
    bqsalescontractdown() {
      getcontractinfobygrp(this.$refs.bqsalescontract.ids).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    ltsalescontractdown() {
      this.$refs.ltsalescontract.getsalesPdf();
    },
    crsalescontractdown() {
      this.$refs.crsalescontract.getsalesPdf();
    },
    bcsalescontractdown() {
      this.$refs.bcsalescontract.getsalesPdf();
    },
    reviewdown() {
      this.$refs.jzreviewform.getreviewPdf();
    },
    reportdown() {
      this.$refs.jzreportform.getreportPdf();
      this.JZreportvisible = false;
    },
    crreportdown() {
      this.$refs.crreportform.getreportPdf();
      this.CRreportvisible = false;
    },
    yxdreportdown() {
      quotationmodel(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          if (res.data == null || res.data == "") {
            if (this.salescustno == "1098") {
              this.$refs.yxdreportform1098.getreportPdf();
            } else if (this.salescustno == "1151") {
              this.$refs.yxdreportform1151.getreportPdf();
            } else if (this.salescustno == "1508") {
              this.$refs.yxdreportform1508.getreportPdf();
            } else if (this.salescustno == "1083") {
              this.$refs.yxdreportform1083.getreportPdf();
            } else {
              this.$refs.yxdreportform.getreportPdf();
            }
          } else {
            let params = {};
            params.type = res.data;
            params.Id = this.$refs.orderTable.selectedRowKeysArray;
            yxDQuotationEXLE(params).then(res => {
              if (res.code) {
                this.downloadByteArrayFromString(res.data, res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          }
        }
      });
    },
    bqreportdown() {
      this.BQreportvisible = false;
      getQuotationInfoByGrp(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
      //this.$refs.bqreportform.getreportPdf();
    },
    mtgxreportdown() {
      this.$refs.mtgxreportform.getreportPdf();
    },
    bcreportdown() {
      let dom = this.bcformType == "nx" ? "bcreportformnx" : this.bcformType == "wx" ? "bcreportformwx" : "bcreportform0184";
      this.$refs[dom].getreportPdf();
    },
    ltreportdown(reportType) {
      this.$refs[reportType].getreportPdf();
    },
    checkClick() {
      this.dataVisible22 = false;
      let data = this.$refs.orderTable.selectedRowsData;
      if (this.checkType == "bjd") {
        if (data.factoryName == "勤基") {
          quotationInfo(this.$refs.orderTable.proOrderId).then(res => {
            if (res.code == 1) {
              this.reportData = res.data;
              let val = this.reportData.priceDtos;
              for (var a = 0; a < val.length; a++) {
                if (val[a].para4Weight_) {
                  val[a].para4Weight_ = Number(val[a].para4Weight_).toFixed(2);
                }
              }
              if (this.reportData.custModuleNoDto.currency == "rmb") {
                this.key = "2";
              } else {
                this.key = "1";
              }
              this.modelVisible = true;
            } else {
              this.$message.error(res.message);
            }
          });
        } else if (data.joinFactoryId == "22") {
          this.JZreportform();
        } else if (data.joinFactoryId == "70") {
          this.MTGXreportform();
        } else if (data.joinFactoryId == "77" || data.joinFactoryId == "100") {
          this.CRreportform();
        } else if (data.joinFactoryId == "58" || data.joinFactoryId == "59") {
          this.YXDreportform();
        } else if (data.joinFactoryId == "12") {
          if (
            data.custNo == "W115" ||
            data.custNo == "W116" ||
            data.custNo == "W072" ||
            (data.custNo.indexOf("W") == -1 && data.custNo.indexOf("Y") == -1)
          ) {
            // 内销
            this.BQreportform("nx");
          } else {
            // 外销
            this.BQreportform("wx");
          }
        } else if ([78,79,80].includes(Number(data.joinFactoryId))) {
          this.BCreportform();
        } else {
          getQuotationInfoByGrp(this.$refs.orderTable.selectedRowKeysArray).then(res => {
            if (res.code) {
              this.downloadByteArrayFromString(res.data, res.message);
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
      if (this.checkType == "jijia") {
        this.spinning = true;
        this.$refs.action.disfinish = true;
        orderPriceCalc(this.$refs.orderTable.proOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success("计价成功");
              if (this.dataSource2.length > 1) {
                for (let index = 0; index < this.dataSource2.length; index++) {
                  const element = this.dataSource2[index];
                  if (element.para4DelQty_ && !element.para4Delivery_) {
                    this.deliverycalculation1(element);
                  }
                }
              }
              // if(this.$refs.orderTable.selectedRowsData.joinFactoryId==58 || this.$refs.orderTable.selectedRowsData.joinFactoryId==59
              // || this.$refs.orderTable.selectedRowsData.joinFactoryId==38 || this.$refs.orderTable.selectedRowsData.joinFactoryId==69
              // ){
              this.changeUpOrDownPrice();
              // }
              this.getOrderDetail(this.$refs.orderTable.proOrderId);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.$refs.action.disfinish = false;
          });
      }
      if (this.checkType == "fdgg") {
        nopealter(this.$refs.orderTable.proOrderId).then(res => {
          if (res.code) {
            this.dataVisible9 = true;
            this.redata = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.checkType == "fdxz") {
        this.returncustno = this.$refs.orderTable.selectedRowsData.custNo;
        custgrouplist(this.$refs.orderTable.proOrderId).then(res => {
          if (res.code) {
            this.frontDataZSupplier1 = res.data;
          }
        });
        prenopeinfo(this.$refs.orderTable.proOrderId)
          .then(res => {
            if (res.code) {
              this.popupdata = res.data;
              this.dataVisible3 = true;
              this.widthy = 400;
              // this.message= '确认返单新增吗?'
              this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
              this.type = "newlyadded";
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.checkType == "xsht") {
        contractNo(this.$refs.orderTable.selectedRowKeysArray).then(res => {
          if (res.code) {
            if (this.joinFactoryId == "58" || this.joinFactoryId == "59" || this.joinFactoryId == "65" || this.joinFactoryId == "38") {
              this.YXDform();
            } else if (this.joinFactoryId == "22") {
              this.JZsalescontract();
            } else if (this.joinFactoryId == "69") {
              this.hmform();
            } else if (this.joinFactoryId == "70") {
              this.mtgxform();
            } else if (this.joinFactoryId == "12") {
              this.BQsalescontract();
            } else if (this.joinFactoryId == "67" || this.joinFactoryId == "98" || this.joinFactoryId == "98") {
              this.LTsalescontract();
            } else if (this.joinFactoryId == "77" || this.joinFactoryId == "100") {
              this.CRsalescontract();
            } else if ([78,79,80].includes(Number(this.joinFactoryId))) {
              this.BCsalescontract();
            } else {
              getcontractinfobygrp(this.$refs.orderTable.selectedRowKeysArray).then(res => {
                if (res.code) {
                  this.downloadByteArrayFromString(res.data, res.message);
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.checkType == "ecn") {
        this.type = "7";
        this.DehandleOk3();
      }
      if (this.checkType == "bjks") {
        this.type = "1";
        this.DehandleOk3();
      }
      if (this.checkType == "htys") {
        this.type = "4";
        this.DehandleOk3();
      }
      if (this.checkType == "ddqx") {
        this.type = "ordercancel";
        this.DehandleOk3();
      }
      if (this.checkType == "ddzt") {
        this.type = "orderpause";
        this.DehandleOk3();
      }
      if (this.checktype == "wjth") {
        this.$refs.action.clickUpload2();
      }
      if (this.checkType == "wjzj") {
        this.$refs.action.clickUpload1(this.$refs.orderTable.proOrderId);
      }
      if (this.checkType == "fsht") {
        this.debouncedsendcontract(this.$refs.orderTable.selectedRowKeysArray);
      }
      if (this.checkType == "fshtMEil") {
        this.spinning = true;
        this.buttonload = true;
        verifyQuotation(arr).then(res => {
          if (res.code) {
            this.debouncedsendcontract(arr);
          } else {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.dataVisible22 = true;
            this.checkType = "fsht";
            this.spinning = false;
            this.buttonload = false;
          }
        });
      }
    },
    ReviewSheet() {
      this.joinFactoryId = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      // if(this.joinFactoryId=='22'){
      //   this.JZreview()
      // }
      else {
        contractReviewInfo(this.$refs.orderTable.selectedRowKeysArray[0], 1).then(res => {
          if (res.code) {
            this.downloadByteArrayFromString(res.data, res.message);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    reportHandleCancelRep(e) {
      this.modelVisible = false;
      this.yxdVisible = false;
      this.mtgxVisible = false;
      this.hmVisible = false;
      this.JZsalesvisible = false;
      this.BQsalesvisible = false;
      this.JZreportvisible = false;
      this.CRreportvisible = false;
      this.JZreviewvisible = false;
      this.YXDreportvisible = false;
      this.Bcreportvisible = false;
      this.BQreportvisible = false;
      this.Mtgxreportvisible = false;
    },
    handleOkRep(e) {
      this.$refs.report.getReportPdf();
      this.modelVisible = false;
    },
    // 查看拼版图
    jigsawPuzzleClick(record) {
      this.makeupVisible = true;
      this.$nextTick(() => {
        this.$refs.makeup.impositionInformationExample(
          record.boardHeight,
          record.boardWidth,
          record.pinban_x || 1,
          record.pinban_y || 1,
          record.processeEdge_x || "none",
          record.processeEdge_y || 0,
          record.vCut || "none",
          record.cao_x || 0,
          record.cao_y || 0
        );
      });
    },
    // 图形预览
    previewClick(record) {
      graphicPreview(record.id).then(res => {
        if (res.data) {
          window.open(res.data, "_blank");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //回退预审
    verifyBackClick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      buttonCheck(this.$refs.orderTable.proOrderId, "VerifyBack").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "htys";
            this.dataVisible22 = true;
          } else {
            this.type = "4";
            this.dataVisible3 = true;
            this.widthy = 400;
            this.message = "确认回退预审吗？";
            this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    isInteger(obj) {
      return obj % 1 === 0;
    },
    addInquiry(record) {
      setTimeout(() => {
        if (record.para4DelQty_ > 0 && this.isInteger(record.para4DelQty_)) {
          let params = {
            id: this.$refs.orderTable.proOrderId,
            para4DelQty_: Number(record.para4DelQty_),
          };
          this.spinning = true;
          orderPriceCalcResult4Parameter(params).then(res => {
            if (res.code) {
              let ids = res.data;
              calacmktorderpricedaybysig(ids).then(res => {
                //计算交期
                if (res.code) {
                  this.$refs.action.disfinish = true;
                  parsignorderpricecalc(ids)
                    .then(res => {
                      //计算价格
                      if (res.code) {
                        this.$message.success("成功");
                        this.getOrderDetail(this.$refs.orderTable.proOrderId);
                      } else {
                        this.$message.error(res.message);
                      }
                    })
                    .finally(() => {
                      this.spinning = false;
                      this.$refs.action.disfinish = false;
                    });
                } else {
                  this.$message.error(res.message);
                  this.spinning = false;
                }
              });
            } else {
              this.$message.error(res.message);
              this.spinning = false;
            }
          });
        } else {
          if ((record.para4DelQty_ != null && record.para4DelQty_ != "") || this.addnum != null) {
            this.$message.warning("请填写报价数量（正整数）");
          }
        }
      }, 200);
    },
    //提前天数失去焦点
    earlierdate(record) {
      var r = /^\+?[1-9][0-9]*$/;
      var f = /^-[1-9]\d*$/;
      if (record.para4UrgentDate_ != this.addnum1) {
        if (r.test(record.para4UrgentDate_) || f.test(record.para4UrgentDate_) || record.para4UrgentDate_ == "") {
          let params = {
            id: record.id,
            para4Delivery_: record.para4Delivery_,
            para4UrgentDate_: record.para4UrgentDate_ ? Number(record.para4UrgentDate_) : null,
          };
          this.spinning = true;
          seturgentday(params)
            .then(res => {
              if (res.code) {
                this.$message.success("成功");
                // this.getOrderList();
                //(this.$refs.orderTable.selectedRowsData.joinFactoryId==58 || this.$refs.orderTable.selectedRowsData.joinFactoryId==59
                // || this.$refs.orderTable.selectedRowsData.joinFactoryId==38 || this.$refs.orderTable.selectedRowsData.joinFactoryId==69)&&
                if (this.dataSource3.length) {
                  this.parsignorderprice(record);
                }
                this.getOrderDetail(this.$refs.orderTable.proOrderId);
              } else {
                this.$message.error(res.message);
                this.getOrderDetail(this.$refs.orderTable.proOrderId);
              }
            })
            .finally(() => {
              this.spinning = false;
            });
        } else {
          if (record.para4UrgentDate_ != null && (!r.test(record.para4UrgentDate_) || !f.test(record.para4UrgentDate_))) {
            this.$message.warning("提前天数请填写非零整数");
            record.para4UrgentDate_ = "";
          }
        }
      }
    },
    changePa(record) {
      if (record.para4UrgentDate_ == "") {
        this.earlierdate(record);
      }
    },
    moment,
    onChange1(value, dateString) {
      let r = dateString + " " + moment().format("HH:mm:ss");
      let record = {};
      for (var a = 0; a < this.dataSource2.length; a++) {
        if (this.dataSource2[a].id == this.id) {
          this.dataSource2[a].para4Delivery_ = dateString.trim();
          record = this.dataSource2[a];
        }
      }
      let params = {
        id: this.id,
        para4Delivery_: r.trim(),
        para4UrgentDate_: record.para4UrgentDate_ ? Number(record.para4UrgentDate_) : null,
      };
      seturgentday(params).then(res => {
        if (res.code) {
          this.$message.success("成功");
          this.getOrderDetail(this.$refs.orderTable.proOrderId);
        } else {
          this.$message.error(res.message);
          this.getOrderDetail(this.$refs.orderTable.proOrderId);
        }
      });
      // this.formData.para4Delivery_ = dateString
    },
    // 删除多套
    actionClick(record) {
      this.deletId = record.id;
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.dataVisible3 = true;
      this.widthy = 400;
      this.message = "确认删除此套价格信息？";
      this.type = "3";
    },
    //交期单条计算按钮
    deliverycalculation1(record) {
      this.spinning = true;
      this.$refs.action.disfinish = true;
      calacmktorderpricedaybysig(record.id)
        .then(res => {
          if (res.code) {
            this.$message.success("计算成功");
            this.getOrderDetail(this.$refs.orderTable.proOrderId);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.$refs.action.disfinish = false;
        });
    },
    //单独计价接口
    parsignorderprice(record, type) {
      this.spinning = true;
      this.copyUpOrDownPrice();
      this.$refs.action.disfinish = true;
      parsignorderpricecalc(record.id)
        .then(res => {
          if (res.code) {
            this.$message.success("计算成功");
            if (type && type == "num") {
              this.deliverycalculation1(record); //更改数量时计算单条交期
            }
            this.changeUpOrDownPrice();
            this.getOrderDetail(this.$refs.orderTable.proOrderId);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.$refs.action.disfinish = false;
        });
    },
    // 修改数量
    numberClick(record) {
      this.numFlag = true;
      this.num = record.para4DelQty_;
      setTimeout(() => {
        this.$refs.input.focus();
      }, 100);
    },
    numChange1(record, type) {
      record.pcsPrice_ = record.pcsPrice_ ? Number(record.pcsPrice_) : null;
      if (this.num != record.para4DelQty_ || this.price != record.pcsPrice_) {
        this.spinning = true;
        setNum(record).then(res => {
          if (res.code) {
            this.$message.success("修改成功");
            this.numFlag = false;
            if (this.dataSource3.length) {
              this.parsignorderprice(record, type); //单独计价接口
            } else {
              this.spinning = false;
            }
          } else {
            this.spinning = false;
            this.$message.error(res.message);
          }
        });
      } else {
        this.numFlag = false;
      }
    },
    // 设置为下单
    async CheckClick(record) {
      record.isProduction = true;
      this.loading2 = true;
      setIsProduction(record.id).then(res => {
        if (res.code) {
          this.$message.success("设置下单成功");
          if (this.Paraid) {
            orderpricecalcone(this.$refs.orderTable.proOrderId, this.Paraid)
              .then(res => {})
              .finally(() => {
                this.loading2 = false;
                this.getOrderDetail(this.$refs.orderTable.proOrderId);
              });
          } else {
            this.loading2 = false;
            this.getOrderDetail(this.$refs.orderTable.proOrderId);
          }
        } else {
          this.$message.error(res.message);
          record.isProduction = false;
          this.loading2 = false;
        }
      });
    },
    // 上传合同
    finishClick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.$refs.action.clickUpload(this.$refs.orderTable.selectedRowKeysArray[0]);
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'上传合同',})
      this.Buriedpointcache(this.buryingpoint);
    },

    ModifyPO() {
      if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.PoVisible = true;
    },
    PoHandleOk() {
      if (!this.cust_Po) {
        this.$message.error("必须填写PO号");
        return;
      }
      this.spinning = true;
      this.PoVisible = false;
      updatebatchcustpo(this.$refs.orderTable.selectedRowKeysArray[0], this.cust_Po)
        .then(res => {
          if (res.code) {
            this.$message.success(res.message);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spining = false;
        });
    },
    offlinecancel() {
      this.offlinedataVisible = false;
      return this.Quotationcompleted(this.count + 1);
    },
    Quotationcompleted(ind) {
      this.fdloading = true;
      if (ind < this.allid.length) {
        this.spinning = true;
        this.count = ind;
        indicationCheck(this.allid[ind].id, 1).then(res => {
          if (res.code) {
            this.checkData = res.data;
            this.check = this.checkData.findIndex(v => v.error == "1") < 0;
            if (this.checkData.length == 0) {
              paymodecheck(this.allid[ind].id).then(res => {
                // BQ 跳过订单管理调用ERP客户PO检查
                if (res.code) {
                  this.orderfinish(ind);
                } else {
                  this.offlinedataVisible = true; // 预付款弹窗
                  this.mmessage = res.message;
                  this.spinning = false;
                  this.dataVisible3 = false;
                  this.fdloading = false;
                }
              });
            } else {
              this.dataVisible2 = true;
              this.checkType = "bjwc";
            }
          } else {
            this.$message.error(res, message);
            this.Quotationcompleted(ind + 1);
            if (ind + 1 == this.allid.length) {
              this.spinning = false;
              this.dataVisible3 = false;
              this.fdloading = false;
            }
          }
        });
      } else {
        this.spinning = false;
        this.dataVisible3 = false;
        this.fdloading = false;
      }
    },
    orderfinish(ind) {
      this.offlinedataVisible = false;
      this.fdloading = true;
      this.spinning = true;
      verifyFinishedOrder(this.allid[ind].id).then(res => {
        if (res.code) {
          if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 38) {
            timedautomaticsendorder(this.$refs.orderTable.selectedRowsData.joinFactoryId).then(res => {});
          }
          settoolcheck(this.allid[ind].id).then(res => {});
          if (ind + 1 == this.allid.length) {
            this.$message.success("完成");
            this.dataVisible3 = false;
            this.fdloading = false;
          }
          if (this.orderListData.length == 1) {
            this.getOrderList();
          } else {
            const array1 = this.orderListData.findIndex(element => element.id == this.allid[ind].id);
            this.orderListData.splice(array1, 1);
          }
          this.$refs.orderTable.selectedRowKeysArray = [];
          this.$refs.orderTable.proOrderId = "";
          this.dataSource2 = [];
          this.Source2 = [];
          this.dataSource3 = [];
          this.dataSource4 = [];
          return this.Quotationcompleted(ind + 1);
        } else {
          this.spinning = false;
          let params = {
            title: "报价失败",
            content: `${this.allid[ind].orderNo}报价完成失败,原因：${res.message}`,
            userName: this.user.userName,
          };
          if (res.message != "有检查项") {
            informationremind(params).then(res => {
              if (res.code) {
                getinformationremind().then(res => {
                  if (res.code) {
                    this.setinfolength(res.data.length);
                    localStorage.setItem("infolength", res.data.length);
                  }
                });
              }
            });
          }
          if (res.data && res.data.length) {
            this.checkData = res.data;
            this.check = this.checkData.findIndex(v => v.error == "1") < 0;
            this.dataVisible2 = true;
            this.checkType = "bjwc";
          } else {
            this.$message.error(this.allid[ind].orderNo + res.message);
            this.spinning = false;
            this.dataVisible3 = false;
            this.fdloading = false;
            // if (ind + 1 == this.allid.length) {
            //   this.spinning = false;
            //   this.dataVisible3 = false;
            //   this.fdloading = false;
            // }
            // return this.Quotationcompleted(ind + 1);
          }
        }
      });
    },
    handleOk3() {
      this.fdloading = true;
      //回退
      if (this.type == "4") {
        this.spinning = true;
        let id = this.$refs.orderTable.proOrderId;
        // this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'回退预审',})
        this.Buriedpointcache(this.buryingpoint);
        verifyBackToPreOrder(id)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
              this.$refs.orderTable.selectedRowKeysArray = [];
              this.$refs.orderTable.proOrderId = "";
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.fdloading = false;
          });
      }
      // 开始
      if (this.type == "1") {
        this.spinning = true;
        // this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'开始',})
        this.Buriedpointcache(this.buryingpoint);
        verifyStartOrder(this.$refs.orderTable.proOrderId)
          .then(res => {
            localStorage.setItem("pageCurrent4", this.pagination.current);
            localStorage.setItem("pageSize4", this.pagination.pageSize);
            localStorage.setItem("id4", this.$refs.orderTable.proOrderId);
            localStorage.setItem("record4", JSON.stringify(this.$refs.orderTable.selectedRowsData));
            localStorage.setItem("stat4", true);
            localStorage.setItem("bjqueryInfo", JSON.stringify(this.params1));
            if (res.code) {
              this.$message.success("开始成功");
              if (this.$refs.orderTable.selectedRowsData.factoryName == "奔强") {
                this.getDetailInfo(this.$refs.orderTable.proOrderId);
              }
              if (
                (this.$refs.orderTable.selectedRowsData.factoryName == "普林" || this.$refs.orderTable.selectedRowsData.factoryName == "普林科技") &&
                this.$refs.orderTable.selectedRowsData.noteSure != "" &&
                this.$refs.orderTable.selectedRowsData.noteSure
              ) {
                this.$info({
                  title: "提示",
                  content: h => <div style="color:red;font-size:15px;">{this.$refs.orderTable.selectedRowsData.noteSure}</div>,
                  class: "infomessage",
                });
              }
              var a = "";
              if (this.$refs.orderTable.startIndex == -1) {
                a = 0;
              } else {
                a = this.$refs.orderTable.startIndex;
              }
              this.orderListData[a].status = "报价中";
              let indexId = this.$refs.orderTable.proOrderId;
              this.$refs.orderTable.proOrderId = indexId;
              this.getOrderDetail(indexId); //获取多套列表数据
            } else {
              this.$message.error(res.message);
            }
            // this.reload()
          })
          .finally(() => {
            this.spinning = false;
            this.fdloading = false;
          });
      }
      //返单新增
      if (this.type == "newlyadded") {
        //   let params={
        //   "custNo": this.returncustno,
        //   "proOrderNo": this.popupdata.proOrderNo,
        //   "deliveryDate": this.popupdata.deliveryDate,
        //   "marketDeliveryTime": this.popupdata.marketDeliveryTime,
        //   "custPo": this.popupdata.custPo,
        //   "contractNumber": this.popupdata.contractNumber,
        //   "flyingProbe": this.popupdata.flyingProbe,
        //   "shipmentTerm": this.popupdata.shipmentTerm,
        //   "num": this.popupdata.num,
        //   "orderDirection": this.popupdata.orderDirection,
        //   'customClassification':this.popupdata.customClassification,
        //   "customerModel": this.popupdata.customerModel,
        //   "customerMaterialNo": this.popupdata.customerMaterialNo,
        //   "customerMaterialName": this.popupdata.customerMaterialName,
        //   "mktNote": this.popupdata.mktNote,
        //   "unitPrice": this.popupdata.unitPrice,
        //   "id": this.$refs.orderTable.selectedRowsData.id,
        // }
        let params = {
          custNo: this.returncustno,
          delType: this.popupdata.delType,
          boardArea: Number(this.popupdata.boardArea),
          orderAttribute: this.popupdata.orderAttribute,
          testPointNum: Number(this.popupdata.testPointNum),
          openedTestrack: this.popupdata.openedTestrack,
          priceType: this.popupdata.priceType,
          proOrderNo: this.popupdata.proOrderNo,
          deliveryDate: this.popupdata.deliveryDate,
          reOrderNo: this.$refs.orderTable.selectedRowsData.orderNo,
          marketDeliveryTime: this.popupdata.marketDeliveryTime,
          custPo: this.popupdata.custPo,
          contractNumber: this.popupdata.contractNumber,
          flyingProbe: this.popupdata.flyingProbe,
          shipmentTerm: this.popupdata.shipmentTerm,
          num: this.popupdata.num,
          orderDirection: this.popupdata.orderDirection,
          customClassification: this.popupdata.customClassification,
          customerModel: this.popupdata.customerModel,
          customerMaterialNo: this.popupdata.customerMaterialNo,
          customerMaterialName: this.popupdata.customerMaterialName,
          mktNote: this.popupdata.mktNote,
          unitPrice: this.popupdata.unitPrice,
          id: this.$refs.orderTable.selectedRowsData.id,
        };
        this.spinning = true;
        preaddnope(params)
          .then(res => {
            if (res.code) {
              this.$message.success("保存成功");
              let val = { proOrderNo: res.data.split(",")[1] };
              this.getOrderList(val);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.fdloading = false;
            this.spinning = false;
          });
      }
      // 完成
      if (this.type == "2") {
        this.spinning = true;
        verifyfinishorders(this.$refs.orderTable.proOrderId).then(res => {
          if (res.code) {
            this.allid = res.data;
            this.Quotationcompleted(0);
          } else {
            this.spinning = false;
            this.fdloading = false;
          }
        });
      }
      // 删除多套价格
      if (this.type == "3") {
        // this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'删除',})
        this.Buriedpointcache(this.buryingpoint);
        deleteOrderPriceCalcResult4Parameter(this.deletId)
          .then(res => {
            if (res.code) {
              this.$message.success("删除成功");
              this.getOrderDetail(this.$refs.orderTable.proOrderId);
            } else {
              if (res.data && res.data.length) {
                this.checkData = res.data;
                this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                this.dataVisible2 = true;
              } else {
                this.$message.error(res.message);
              }
            }
          })
          .finally(() => {
            this.fdloading = false;
          });
      }
      // pl报价单
      if (this.type == "report") {
        if (!this.value) {
          this.$message.warning("请选择报价单类型！");
          return;
        }
        // 接口直接下载
        quotationInfo1(this.$refs.orderTable.proOrderId, this.value).then(res => {
          let blob = new Blob([res], {
            type: " application/pdf;charset=utf-8",
          }); // 为blob设置文件类型，这里.PDF
          let url = window.URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", this.$refs.orderTable.selectedRowsData.orderNo + "-报价单.pdf");
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this.fdloading = false;
        });
      }
      // 订单恢复
      if (this.type == "5") {
        //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'订单恢复',})
        this.Buriedpointcache(this.buryingpoint);
        orderrecoveryduoxuan(this.$refs.orderTable.selectedRowKeysArray)
          .then(res => {
            if (res.code) {
              this.$message.success("恢复成功");
              //this.getOrderList()
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.fdloading = false;
          });
      }
      //解除警告
      if (this.type == "6") {
        //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'解除警告',})
        this.Buriedpointcache(this.buryingpoint);
        this.spinning = true;
        setreleasewarning(this.$refs.orderTable.proOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success("解除成功");
              this.getOrderList();
              this.$refs.orderTable.selectedRowKeysArray = [];
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.fdloading = false;
          });
      }
      if (this.type == "7") {
        let ecn = 0;
        if (this.OnLineOrRecordEcn == 0 && this.joinFactoryId == 22) {
          this.$message.error("请选择ECN流程");
          this.fdloading = false;
          return;
        }
        if (this.joinFactoryId == 22) {
          ecn = this.OnLineOrRecordEcn;
        } else if (this.joinFactoryId == 58 || this.joinFactoryId == 59) {
          ecn = this.Upgradeversion ? 3 : 0;
        } else {
          ecn = 0;
        }
        this.spinning = true;
        verifyonlineeCN(this.$refs.orderTable.proOrderId, ecn)
          .then(res => {
            if (res.code) {
              this.$message.success("在线ECN成功");
              this.getOrderList({ OrderNo: res.data });
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.fdloading = false;
          });
      }
      //订单取消
      if (this.type == "ordercancel") {
        if (!this.CancelCause) {
          this.$message.error("请输入取消原因");
          this.fdloading = false;
          return;
        }
        this.spinning = true;
        ordercancel(this.$refs.orderTable.proOrderId, this.CancelCause)
          .then(res => {
            if (res.code) {
              this.$message.success("取消成功");
              this.getOrderList();
              this.$refs.orderTable.selectedRowKeysArray = [];
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.fdloading = false;
          });
      }
      //订单暂停
      if (this.type == "orderpause") {
        if (!this.PauseCause) {
          this.$message.error("请输入暂停原因");
          this.fdloading = false;
          return;
        }
        this.spinning = true;
        orderpause(this.$refs.orderTable.proOrderId, this.PauseCause)
          .then(res => {
            if (res.code) {
              this.$message.success("暂停成功");
              this.getOrderList();
              this.$refs.orderTable.selectedRowKeysArray = [];
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.fdloading = false;
          });
      }
      // 订单删除
      if (this.type == "orderdel") {
        this.spinning = true;
        orderverifydelete(this.$refs.orderTable.selectedRowKeysArray)
          .then(res => {
            if (res.code) {
              this.$message.success("删除成功");
              this.getOrderList();
              this.$refs.orderTable.selectedRowKeysArray = [];
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.fdloading = false;
          });
      }
      this.dataVisible3 = false;
    },
    continueClick() {
      this.dataVisible2 = false;
      this.spinning = true;
      if (this.checkType == "bjwc") {
        this.orderfinish(this.count);
      }
    },
    Mergerofreturnorders() {
      this.merdatavisible = true;
      localStorage.removeItem("jzreturn");
      localStorage.removeItem("otherreturn");
      localStorage.removeItem("Returnorderdata");
      this.getData(this.user.factoryId);
      this.getSupplier();
    },
    orderaccess() {
      this.oderdataVisible = true;
    },
    orderhandleOk() {
      if (!this.form.proOrderNo) {
        this.$message.warning("请输入生产型号！");
        return;
      }
      let formData = {
        proOrderNo: this.form.proOrderNo,
        isReorder: this.form.isReorder,
        custNo: this.form.custNo,
      };
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单报价',usingFeatures:'订单接入',})
      this.Buriedpointcache(this.buryingpoint);
      this.fdloading = true;
      pcbordertonew(formData)
        .then(res => {
          if (res.code) {
            this.$message.success("导入成功");
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.fdloading = false;
        });
      this.oderdataVisible = false;
    },
    getSupplier() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("mktCustNo"));
      var factory = this.user.factoryId; //订单接入时无法获取
      if (
        data &&
        token &&
        data.filter(item => {
          return item.factory == factory;
        }).length
      ) {
        for (let index = 0; index < data.length; index++) {
          if (data[index].token == token && data[index].factory == factory) {
            const element = data[index];
            this.supList = element.data;
            this.frontDataZSupplier = element.data.slice(0, 20);
          }
        }
      } else {
        if (factory == 58 || factory == 59) {
          mktcustnobyfAC(factory).then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplier = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          mktCustNo().then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplier = res.data.slice(0, 20);

              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr));
              } else {
                data.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data));
              }
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value.toUpperCase();
        let arr = that.supList.filter(m => m.toUpperCase().indexOf(value.toUpperCase()) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.supList.slice(0, 20);
      }
    },
    //
    copyUpOrDownPrice() {
      this.recordPrice = [];
      this.spinning = true;
      pricelistbypcbid(this.$refs.orderTable.proOrderId).then(res => {
        if (res.code) {
          res.data.forEach(record => {
            if (record.upOrDownPrice) {
              (record.key = record.guid4Calc + record.guid4Order + record.guid4Parameter), this.recordPrice.push(record);
            }
          });
        } else {
          this.spinning = false;
        }
      });
    },
    async changeUpOrDownPrice() {
      let arr = [];
      this.loading3 = true;
      this.loading4 = true;
      this.$refs.action.disfinish = true;
      this.spinning = true;
      await pricelistbypcbid(this.$refs.orderTable.proOrderId).then(res => {
        if (res.code) {
          res.data.forEach(record => {
            (record.key = record.guid4Calc + record.guid4Order + record.guid4Parameter), arr.push(record);
          });
        }
      });
      let arr1 = [];
      for (let index = 0; index < this.Source2.length - 1; index++) {
        var newdata = arr.filter(ite => ite.guid4Parameter == this.Source2[index].id);
        var olddata = this.recordPrice.filter(ite => ite.guid4Parameter == this.Source2[index].id);
        if (olddata.length) {
          for (let i = 0; i < olddata.length; i++) {
            for (let a = 0; a < newdata.length; a++) {
              if (olddata[i].key == newdata[a].key) {
                newdata[a].upOrDownPrice = olddata[i].upOrDownPrice;
                arr1.push(newdata[a]);
              } else {
                this.loading3 = false;
                this.loading4 = false;
                this.$refs.action.disfinish = false;
                this.spinning = false;
              }
            }
          }
        } else {
          this.loading3 = false;
          this.loading4 = false;
          this.$refs.action.disfinish = false;
          this.spinning = false;
        }
        await this.datePrice(arr1);
      }
    },
    async datePrice(arr) {
      let data1 = [];
      let data2 = [];
      arr.forEach(item => {
        if (item.isShow) {
          data1.push(item);
        } else {
          data2.push(item);
        }
      });
      // 执行data1中的每一项，确保顺序执行
      for (let item of data1) {
        await this.updatePrice(item); // 等待每次执行完成
      }
      // 执行完data1中的所有项后，再执行data2中的每一项
      for (let item of data2) {
        await this.updatePrice(item); // 同样等待每次执行完成
      }
    },
    async updatePrice(record) {
      this.$refs.action.disfinish = true;
      this.spinning = true;
      await orderPriceCalc4WinForm(record)
        .then(res => {
          if (res.code) {
            this.oldPrice = this.upOrDownPrice;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          if (this.row2) {
            this.getResultList(this.row2, "price");
          }
          if (this.row3) {
            this.getResultDetailList(this.row3);
          }
          this.loading3 = false;
          this.loading4 = false;
          this.$refs.action.disfinish = false;
          this.spinning = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.verification {
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: white;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: white !important;
  }
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-top: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-top: 1px solid #efefef;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #ffff;
    }
  }
}

/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
.verificationModal {
  table tr th {
    text-align: center;
  }
  table tr td {
    text-align: center;
  }
}
.return_order {
  /deep/.ant-modal-body {
    padding: 10px 24px !important;
  }
}
/deep/.ant-modal {
  padding-bottom: 0;
}
.required {
  /deep/.ant-form-item-label label {
    color: red !important;
  }
}
.img-box {
  height: 60px;
  background-color: #ffffff;
  padding: 6px;
  ul {
    height: 63px;
    list-style: none;
    display: flex; /* 使用flexbox布局 */
    flex-wrap: wrap; /* 允许项目多行排列 */
    gap: 6px; /* 设置项目之间的间隔 */
    overflow: auto;
    margin-bottom: 0;
    li {
      height: 40px;
      text-align: center;
      padding: 6px;
      border: 1px solid #e0e2e4;
      border-radius: 4px;
      background-color: #e0e2e4;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 500px;
    }
  }
}
.mailcalss {
  /deep/.ant-modal-footer {
    display: none;
  }
  /deep/ .ant-modal-body {
    padding: 12px 24px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
  }
  /deep/.ant-divider-horizontal {
    display: block;
    clear: both;
    width: 100%;
    min-width: 100%;
    height: 1px;
    margin: 7px 0;
    background: #a5a5a5;
  }
}
.drawre {
  /deep/ a {
    pointer-events: none !important; //禁止点击链接
  }
  color: black;
  height: 600px;
  padding: 10px;
  border-bottom: 2px solid #e0e2e4;
  white-space: break-spaces;
  &::-webkit-scrollbar {
    //整体样式
    width: 7px !important; //y轴滚动条粗细
    height: 7px !important; //x轴滚动条粗细
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #a5a5a5;
  }
  overflow: auto;
}
.memo-tooltip {
  :global(.ant-tooltip-inner) {
    max-width: 370px;
    // background-color: #ff990042;
    // border: 3px solid #ff9900;
    // color:black;
  }
}
.formclass {
  /deep/.ant-modal-body {
    padding: 0;
  }
  /deep/.ant-modal-footer {
    padding: 10px 100px;
  }
}
.formclass1 {
  /deep/.ant-modal-body {
    padding: 0;
  }
}
.prostyle {
  /deep/.ant-input-affix-wrapper {
    margin-left: -2px;
  }
}
// /deep/.ant-pagination-item{
//   border: ;
// }
/deep/.ant-pagination-prev {
  margin-left: 8px;
  margin-right: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
.faclass {
  width: 94%;
  float: right;
  /deep/.ant-table-placeholder {
    border: 1px solid #f0f0f0;
  }
  /deep/.ant-empty {
    margin: 0;
  }
  /deep/.ant-table-row-cell-break-word {
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
  }
}
.gradient-border {
  display: inline-block;
  padding: 0;
  font-size: 16px;
  color: black; /* 文字颜色 */
  width: 100%;
  height: 33px;
  line-height: 27px;
  text-align: center;
  margin-left: -2px;
  border: 2px solid transparent; /* 边框宽度 */
  border-image: linear-gradient(to left, red, red, red); /* 渐变边框 */
  border-image-slice: 1; /* 边框图像分割方式 */
  border-image-width: 2px; /* 边框图像宽度 */
  border-radius: 5px; /* 圆角 */
  margin-top: -6px;
  margin-bottom: -8px;
  background-color: red;
}
/deep/.ant-table-thead > tr > th .ant-table-column-sorter {
  display: none;
  vertical-align: middle;
}
/deep/.ant-select-allow-clear {
  width: 100%;
}
.footer {
  width: 100%;
  position: relative;
  /deep/.ant-collapse-extra {
    position: absolute;
    top: 0;
    left: 5%;
  }
  /deep/.ant-collapse-header {
    height: auto;
    line-height: 30px;
    padding: 0 34px !important;
  }
}
/deep/.dragging1 {
  display: none;
  position: absolute;
  top: -20px;
  left: -174px;
  // left:380px;
  z-index: 999 !important;
  .handle {
    width: 1px;
    height: 1px;
    border: none;
    z-index: 9999;
    display: block !important;
    background: none;
  }
  .handle-tl {
    top: 0;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-tm {
    top: 1px;
    left: 0;
    width: 100%;
    margin-left: 0;
  }
  .handle-tr {
    top: 0;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-mr {
    top: 0;
    right: -1px;
    margin-top: 1px;
    height: 100% !important;
  }
  .handle-br {
    bottom: -1px;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-bm {
    bottom: -1px;
    left: 0;
    margin-left: 0;
    width: 100%;
  }
  .handle-bl {
    bottom: -1px;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000 !important;
  }
  .handle-ml {
    left: -1px;
    margin-top: 0;
    top: 1px;
    height: 100%;
  }
}
/deep/.dragging2 {
  display: none;
  position: absolute;
  top: -33px;
  left: -197px;
  // left:380px;
  z-index: 999 !important;
  .handle {
    width: 1px;
    height: 1px;
    border: none;
    z-index: 9999;
    display: block !important;
    background: none;
  }
  .handle-tl {
    top: 0;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-tm {
    top: 1px;
    left: 0;
    width: 100%;
    margin-left: 0;
  }
  .handle-tr {
    top: 0;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-mr {
    top: 0;
    right: -1px;
    margin-top: 1px;
    height: 100% !important;
  }
  .handle-br {
    bottom: -1px;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-bm {
    bottom: -1px;
    left: 0;
    margin-left: 0;
    width: 100%;
  }
  .handle-bl {
    bottom: -1px;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000 !important;
  }
  .handle-ml {
    left: -1px;
    margin-top: 0;
    top: 1px;
    height: 100%;
  }
}
/deep/#modal-container > div:first-child {
  height: 100%;
}
#modal-container {
  position: relative;
  height: 100%;
  /deep/.ant-modal-root {
    height: 100%;
  }
  /deep/.ant-modal-wrap {
    position: relative;
    width: 100%;
    // border:2px solid #efefef;
    border: 2px solid #ffcd82;
    box-shadow: 0 6px 20px #ffcd82;
    border-left:hover {
      cursor: e-resize;
    }
  }
  /deep/.ant-modal {
    top: 0;
    position: unset;
    padding-bottom: 0;
  }
  /deep/.ant-modal-content {
    position: unset;
  }
}
/deep/#modal-container1 > div:first-child {
  height: 100%;
}
#modal-container1 {
  position: relative;
  height: 100%;
  /deep/.ant-modal-root {
    height: 100%;
  }
  /deep/.ant-modal-wrap {
    position: unset;
    width: 100%;
    // border:2px solid #efefef;
    border: 2px solid #ffcd82;
    box-shadow: 0 6px 20px #ffcd82;
    border-left:hover {
      cursor: e-resize;
    }
  }
  /deep/.ant-modal {
    top: 0;
    position: unset;
    padding-bottom: 0;
  }
  /deep/.ant-modal-content {
    position: unset;
  }
}
.tabRightClikBox {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    line-height: 30px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.yushen {
  /deep/.ant-modal-footer {
    padding: 8px;
    border-top: none;
  }
  /deep/.ant-modal-body {
    padding-top: 0px;
    padding-bottom: 0px;
    overflow: auto;
    // overflow-y: auto;
    // overflow-x: hidden;
  }
  /deep/.ant-modal-header {
    border-bottom: 1px solid #ddd;
    padding: 9px 24px;
  }
}
.blue {
  color: rgb(115, 159, 218);
}
.xiaoshou {
  /deep/.ant-modal-header {
    padding: 10px 24px;
  }
  /deep/.ant-modal-body {
    padding: 8px 20px;
    max-height: 807px;
    overflow: auto;
  }
  /deep/.ant-modal-footer {
    padding: 5px 16px;
  }
}
.projectackend {
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #c9c9c9 !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-modal-body {
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}

/deep/.ant-table-fixed-left {
  .ant-table-body-outer {
    margin-bottom: 0 !important;
  }
  .ant-table-body-inner {
    overflow-y: scroll;
    overflow-x: inherit;
    max-height: 720px !important;
  }
}

/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-calendar-picker-clear {
  right: 4px;
}
/deep/.ant-calendar-picker-icon {
  right: 4px;
}
/deep/.ant-calendar-picker-input {
  height: 30px !important;
  padding: 0 10px 0px 10px;
}
/deep/.ant-table-selection-column {
  padding: 0 !important;
}
// /deep/.ant-radio-group{
//   display: flex;
// }
/deep/.ant-radio-wrapper {
  width: 40%;
  margin-left: 10%;
}
.ant-table-tbody {
  .ant-table-row {
    .ant-table-row-cell-ellipsis {
      .ant-input-affix-wrapper {
        /deep/.ant-input-suffix {
          right: 3px !important;
        }
        /deep/.ant-input {
          height: 26px !important;
          padding: 0;
          padding-right: 6px;
          margin-top: 2px;
        }
      }
    }
  }
}
// /deep/.ant-modal-content .ant-modal-footer div{
//     margin-top:68px!important;
//   }
/deep/.inputClass {
  padding: 2px 1px !important;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectMake {
  user-select: none;
  height: 780px;
  min-width: 1670px;
  // width: 100%;
  background: #ffffff;
  /deep/.leftContent {
    border: 2px solid rgb(238, 238, 238);
    border-bottom: 4px solid #e9e9f0;
    height: 776px;
    // .ant-table-tbody>tr>td {
    //     display: flex !important;
    //     align-items: center !important;
    //   }
    .min-table {
      .ant-table-body {
        // min-height:739px;
        min-height: 734px;
      }
      .ant-table-placeholder {
        display: none;
      }
    }

    .ant-table-body {
      .ant-table-fixed {
        width: 1260px !important;
      }
    }
    width: 67%;
    // border: 2px solid rgb(233, 233, 240);
  }

  /deep/ .userStyle {
    user-select: all !important;
  }
  .rightContent {
    border-bottom: 2px solid rgb(233, 233, 240);
    width: 33%;
    height: 776px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    /deep/ .min-table2 {
      .ant-table-body {
        min-height: 215px;
      }
      .ant-table-tbody {
        //  .ant-table-row:first-child {
        //   td:last-child{
        //     i:last-child{
        //       display: none!important;top
        //     }
        //   }
        //  }
        .ant-table-row:last-child {
          td:last-child {
            i {
              display: none !important;
            }
          }
        }
      }
    }
    /deep/ .min-table3 {
      .ant-table-body {
        min-height: 215px;
      }
    }
    /deep/ .min-table4 {
      .ant-table-tbody {
        //  .ant-table-row:first-child {
        //   td:last-child{
        //     i:last-child{
        //       display: none!important;top
        //     }
        //   }
        //  }
        .ant-table-row:last-child {
          td:last-child {
            i {
              display: none !important;
            }
          }
        }
      }
    }
    /deep/ .top {
      .ant-table-bordered.ant-table-empty .ant-table-placeholder {
        border-left: 0;
        border-right: 0;
      }
      width: 100%;
      // height: 252px;
      //border: 2px solid rgb(233, 233, 240) ;
      border-bottom: 1px;
      // border-bottom: 4px solid #e9e9f0;
    }
    /deep/ .centerTable {
      .ant-table-thead > tr > th {
        height: 33px;
      }
      .ant-collapse-content > .ant-collapse-content-box {
        padding: 0;
      }
      .ant-table-bordered.ant-table-empty .ant-table-placeholder {
        border-left: 0;
        border-right: 0;
      }
      // .ant-table-body{
      // max-height: 40%!important;
      // }
      width: 100%;
      //border: 2px solid rgb(233, 233, 240) ;
      border-bottom: 1px;
      // border-bottom: 4px solid #e9e9f0;
    }
    /deep/ .bto {
      .ant-table-thead > tr > th {
        height: 33px;
      }

      .ant-table-bordered.ant-table-empty .ant-table-placeholder {
        border-left: 0;
        border-right: 0;
      }
      width: 100%;
      height: 269px;
      //border: 2px solid rgb(233, 233, 240) ;
      border-bottom: 1px;
    }
  }

  .footerAction {
    width: 100%;
    height: 48px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }

  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display: none;
    }
  }
  /deep/ .ant-table-fixed {
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    .userStyle {
      user-select: all !important;
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #c9c9c9;
  }
  /deep/ .ant-table {
    //   .ant-table-selection-column{
    //   padding:6px 0!important;
    //  }
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    .customerModel {
      padding: 0 0 0 2px !important;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #c9c9c9;
    }
    .rowBackgroundColor {
      background: #c9c9c9;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
        color: #000000;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 0;
    margin-top: 12px;
    position: relative;
    // left: 85px;
  }
  /deep/.totalclass {
    color: #000000;
    top: -23px;
    left: 15px;
    position: relative;
    display: table;
  }
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #d6d6d6;
    p {
      line-height: 35px;
      font-weight: 700;
      margin: 0;
      img {
        width: 100%;
      }
    }
    .displayFlag {
      display: none;
    }
  }
}
.modalSty {
  /deep/.ant-modal .ant-modal-content .ant-modal-body {
    padding: 0;
  }
}
</style>
