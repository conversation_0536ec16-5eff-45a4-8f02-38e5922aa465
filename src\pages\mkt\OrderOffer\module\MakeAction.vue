<!-- 市场管理 - 订单报价- 按钮 -->
<template>
  <div class="active" ref="active">
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.CheckAddReOrderNew')"
      :class="checkPermission('MES.MarketModule.Check.CheckAddReOrderNew') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Mergerofreturnorders">返单</a-button>
    </div>
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.CheckStart')"
      :class="checkPermission('MES.MarketModule.Check.CheckStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="MakeStartClick"> 开始 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.CheckValuation')"
      :class="checkPermission('MES.MarketModule.Check.CheckValuation') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="valuationClick"> 计价 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.CheckEnd')"
      :class="checkPermission('MES.MarketModule.Check.CheckEnd') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="modifyInfoClick" :disabled="disfinish"> 完成 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.CheckChangePrice')"
      :class="checkPermission('MES.MarketModule.Check.CheckChangePrice') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ChangePrice" v-if="!PriceFlag"> 改价 </a-button>
      <a-button type="primary" @click="ChangePriceok" v-if="PriceFlag"> 改价完成 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Check.CheckContractReviewInfo2')"
      :class="checkPermission('MES.MarketModule.Check.CheckContractReviewInfo2') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ReviewSheet"> 评审单 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.CheckQuotation')"
      :class="checkPermission('MES.MarketModule.Check.CheckQuotation') ? 'showClass ppading' : ''"
    >
      <a-button type="primary" @click="quotationClick"> 报价表单(B) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.PriceContract')"
      :class="checkPermission('MES.MarketModule.Check.PriceContract') ? 'showClass ppading' : ''"
    >
      <a-button type="primary" @click="quotationcontract" :loading="buttonload"> 销售合同(H) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Check.OrderCostAnalysis')"
      :class="checkPermission('MES.MarketModule.Check.OrderCostAnalysis') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('costanalysis')"> 成本分析 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.SendContractEMime')"
      :class="checkPermission('MES.MarketModule.Check.SendContractEMime') ? 'showClass ppading' : ''"
    >
      <a-button type="primary" @click="$emit('sendcontractemail')" :loading="buttonload"> 发送合同 </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.CheckBack')"
      :class="checkPermission('MES.MarketModule.Check.CheckBack') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="verifyBackClick"> 回退预审 </a-button>
    </div>

    <!-- <div class="box" > 
        <a-button type="primary" @click="calcDayClick" >
          交期计算
        </a-button>
      </div> -->
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.PreAddNope')"
      :class="checkPermission('MES.MarketModule.Check.PreAddNope') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="addorder"> 返单新增 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.PreAddNopeAlter')"
      :class="checkPermission('MES.MarketModule.Check.PreAddNopeAlter') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="changeorder"> 返单更改 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.VerifyOnLineECN')"
      :class="checkPermission('MES.MarketModule.Check.VerifyOnLineECN') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('onlineecn')"> 在线ECN </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.Check.MktOrderModify')"
      :class="checkPermission('MES.MarketModule.Check.MktOrderModify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="marketmodification"> 市场修改 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.Check.OrderCheckPcborderToNew')"
      :class="checkPermission('MES.MarketModule.Check.OrderCheckPcborderToNew') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="orderaccess"> 订单接入 </a-button>
    </div>
    <a-dropdown>
      <a-button type="primary" class="box" style="margin-top: 11px" @click.prevent>
        辅助功能<a-icon class="lockclass" type="up-circle"></a-icon>
      </a-button>
      <template #overlay>
        <a-menu class="tabRightClikBox3">
          <a-menu-item @click="finishClick" v-if="checkPermission('MES.MarketModule.Check.UpLoadContract')">上传合同</a-menu-item>
          <a-menu-item @click="systemparameter" v-if="checkPermission('MES.MarketModule.Check.HuiLSetting')">汇率设置</a-menu-item>
          <a-menu-item @click="$emit('OrderDelete')" v-if="checkPermission('MES.MarketModule.Check.OrderVerifyDelete')">订单删除</a-menu-item>
          <a-menu-item @click="$emit('Ordersuspension')" v-if="checkPermission('MES.MarketModule.Check.OrderCheckPause')">订单暂停</a-menu-item>
          <a-menu-item @click="ordercancellation" v-if="checkPermission('MES.MarketModule.Check.OrderCheckCancel')">订单取消</a-menu-item>
          <a-menu-item @click="orderlock" v-if="checkPermission('MES.MarketModule.Check.OrderLocked')">订单锁定</a-menu-item>
          <a-menu-item @click="OrderRecovery" v-if="checkPermission('MES.MarketModule.Check.OrderRecovery')">订单恢复</a-menu-item>
          <a-menu-item @click="Releasewarning" v-if="checkPermission('MES.MarketModule.Check.OrderCheckReleaseWarning')">解除警告</a-menu-item>
          <a-menu-item @click="$emit('Modelrisk')" v-if="checkPermission('MES.MarketModule.Check.RiskWarningAdd')">型号风险</a-menu-item>
          <a-menu-item @click="$emit('fileswereadded')">文件追加</a-menu-item>
          <a-menu-item @click="$emit('OrderSplitting')">订单拆分</a-menu-item>
          <a-menu-item @click="$emit('ContractVerification')">合同核查</a-menu-item>
          <a-menu-item @click="$emit('uploadPCBFileClick')" v-if="checkPermission('MES.MarketModule.Check.OrderCheckUpdateFile')"
            >文件替换</a-menu-item
          >
          <a-menu-item @click="Quantitysplitting" v-if="checkPermission('MES.MarketModule.Check.OrderNumSplit')">数量拆分</a-menu-item>
          <a-menu-item @click="ModifyPO" v-if="checkPermission('MES.MarketModule.Check.UpdateBatchCustPo')">修改PO</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-upload
      accept=".rar,.zip,pdf,.png,.jpg,.jepg"
      name="file"
      :multiple="false"
      :customRequest="httpRequest1"
      :showUploadList="false"
      ref="fileRef"
      :before-upload="beforeUpload1"
      v-show="false"
    >
      <a-button style="width: 80px"><a-icon type="upload" /></a-button>
    </a-upload>
    <a-upload
      accept=".rar,.zip"
      name="file"
      ref="fileRef1"
      :customRequest="httpRequest6"
      :file-list="fileList6"
      :show-upload-list="false"
      v-show="false"
      :maxCount="1"
      @change="handleChange6"
    >
      <a-button style="width: 80px"><a-icon type="upload" /></a-button>
    </a-upload>
    <a-upload
      accept=".rar,.zip"
      name="file1"
      :multiple="false"
      :customRequest="customRequest"
      :showUploadList="false"
      ref="fileRef2"
      :before-upload="beforeUpload2"
      v-show="false"
    >
      <a-button style="width: 80px"><a-icon type="upload" /></a-button>
    </a-upload>
    <!-- <div class="box" v-show="checkPermission('MES.MarketModule.Check.OrderLocked')"
      :class='checkPermission("MES.MarketModule.Check.OrderLocked") ? "showClass" : ""'>
      <a-button type="primary" @click="orderlock">
        订单锁定
      </a-button>
    </div> -->
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
  </div>
</template>

<script>
import { fileswereadded } from "@/services/mkt/PrequalificationProduction.js";
import { checkPermission } from "@/utils/abp";
import { contractFile } from "@/services/mkt/OrderReview.js";
import protocolCheck from "@/utils/protocolcheck";
export default {
  name: "MakeAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    PriceFlag: {
      type: Boolean,
    },
    buttonload: {
      type: Boolean,
    },
    params1: {},
  },
  data() {
    return {
      orderId: "",
      disfinish: false,
      fileList6: [],
      advanced: true,
      width: 1679,
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 10;
      if (this.$refs.active.children.length > 10) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 11) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        let buttonsToShow = 10;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
    },
    // 取单
    // TakeOrderClick(){
    //   this.$emit('TakeOrderClick')
    // },
    // 开始
    MakeStartClick() {
      this.$emit("MakeStartClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    //市场修改
    marketmodification() {
      this.$emit("marketmodification");
    },
    //订单恢复
    OrderRecovery() {
      this.$emit("OrderRecovery");
    },
    //系统参数
    systemparameter() {
      this.$emit("systemparameter");
    },
    // 完成（审核通过）
    modifyInfoClick() {
      this.$emit("modifyInfoClick");
    },
    //返单新增  2023/8/14取消弹窗内容更改为确认弹窗
    addorder() {
      this.$emit("addareturnorder");
    },
    //解除警告
    Releasewarning() {
      this.$emit("Releasewarning");
    },
    //返单更改
    changeorder() {
      this.$emit("changeorder");
    },
    // 计价
    valuationClick() {
      this.$emit("valuationClick", "计价");
    },
    // 报价单
    quotationClick() {
      this.$emit("quotationClick");
    },
    quotationcontract() {
      this.$emit("quotationcontract");
    },
    ReviewSheet() {
      this.$emit("ReviewSheet");
    },
    //订单锁定
    orderlock() {
      this.$emit("orderlock");
    },
    //订单取消
    ordercancellation() {
      this.$emit("ordercancellation");
    },
    //交期计算
    calcDayClick() {
      this.$emit("calcDayClick");
    },
    //回退预审
    verifyBackClick() {
      this.$emit("verifyBackClick");
    },
    // 改价
    ChangePrice() {
      this.$emit("ChangePrice");
    },
    ChangePriceok() {
      this.$emit("ChangePriceok");
    },
    // 上传合同
    finishClick() {
      this.$emit("finishClick");
    },
    Mergerofreturnorders() {
      this.$emit("Mergerofreturnorders");
    },
    Quantitysplitting() {
      this.$emit("Quantitysplitting");
    },
    customRequest(data) {
      this.$emit("customRequest", data);
    },

    //修改P0
    ModifyPO() {
      this.$emit("ModifyPO");
    },

    beforeUpload2(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const filesize = Number(file.size / 1024 / 1024) < 500;
        const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
        if (!isFileType) {
          _this.$message.error("只支持.rar或.zip格式文件");
          reject();
        } else if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    //订单接入
    orderaccess() {
      this.$emit("orderaccess");
    },
    beforeUpload1(file) {
      const isFileType =
        file.name.toLowerCase().indexOf(".rar") != -1 ||
        file.name.toLowerCase().indexOf(".zip") != -1 ||
        file.name.toLowerCase().indexOf(".png") != -1 ||
        file.name.toLowerCase().indexOf(".jpeg") != -1 ||
        file.name.toLowerCase().indexOf(".jpg") != -1 ||
        file.name.toLowerCase().indexOf(".pdf") != -1;
      console.log("file", file);
      if (!isFileType) {
        this.$message.error("只支持.rar/.zip/.pdf/.jpeg/.jpg/.png 格式文件");
      }
      return isFileType;
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      contractFile(this.orderId, formData).then(res => {
        if (res.code == 1) {
          this.$message.success("上传成功");
          if (JSON.stringify(this.params1) != "{}") {
            this.$emit("getOrderList", this.params1);
          } else {
            this.$emit("getOrderList");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    clickUpload(id) {
      this.orderId = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    clickUpload1(id) {
      this.orderId = id;
      this.$refs.fileRef1.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    clickUpload2(id) {
      this.orderId = id;
      this.$refs.fileRef2.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    handleChange6({ fileList }) {
      this.fileList6 = fileList;
    },
    async httpRequest6(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await fileswereadded(this.orderId, formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.$message.success("文件追加成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.lockclass {
  position: relative;
  top: 1px;
  right: 5px;
}
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-btn {
  padding: 0 10px !important;
}
.ppading {
  /deep/.ant-btn {
    padding: 0 0px !important;
  }
}

.active {
  height: 50px;
  line-height: 40px;
  float: right;
  width: 1680px;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;

  .box {
    width: 90px;
    margin-top: 6px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }

    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }

        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }

        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }

        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;

        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }

        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
