<!-- 工程管理 - 工程制作 -生产备注-->
<template>
  <a-modal
      title="生产备注"
      :visible="visible">
    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        取消
      </a-button>
      <a-button key="submit" type="primary" :loading="confirmLoading" @click="handleOk">
        保存
      </a-button>
    </template>
  
    <a-table
        :columns="columns"
        :dataSource="dataRemark"
        :pagination="false"
        rowKey="id"
        bordered
        :scroll="{y: '45vh'}">
      <template
          v-for="(col, index) in columns"
          :slot="col.scopedSlots.customRender"
          slot-scope="text, record">
        <div :key="col.title">
          <template>
            <a-select
                v-if="col.dataIndex == 'code'"
                v-model="record[col.dataIndex]"
                allowClear
                showSearch
                :filterOption="filterOption"
                placeholder="请选择"
                style="width: 120px">
              <a-select-option
                  v-for="(item, index) in flowSelect"
                  :key="index"
                  :value="item.valueMember">
                {{ item.text }}
              </a-select-option>
            </a-select>
            <a-textarea
                v-if="col.dataIndex == 'note'"
                v-model="record[col.dataIndex]"
                placeholder="请输入备注"
                :auto-size="{ minRows: 1, maxRows: 5 }"/>
          </template>
        </div>
      </template>
      <template slot="action" slot-scope="text, record, index">
        <a-button type="link" @click="deleteDataRemark(index)">删除</a-button>
      </template>
    </a-table>
    <a-button style="width: 100%;margin-top: 15px;" ghost type="primary" @click="addDataRemark">新增</a-button>
  </a-modal>
</template>

<script>
  import {getFlowSelect} from "@/services/projectApi";
const columns = [
  {
    title: "工序",
    dataIndex: "code",
    width: 200,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "code" },
  },
  {
    title: "备注",
    dataIndex: "note",
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "note" },
  },
  {
    title: "操作",
    width: 100,
    dataIndex: "action",
    align: "center",
    scopedSlots: { customRender: "action" },
  },
]
export default {
  name: "productRemark",
  props: ['visible', 'confirmLoading', 'dataRemark', 'orderNoRemark'],
  data() {
    return {
      columns,
      flowSelect: [],
    }
  },
  created() {  
   // this.getFlowSelectFunc();
  },
  methods: {
    // 将输入的内容与显示的内容进行匹配
    filterOption (value, option) {
      return option.componentOptions.children[0].text.indexOf(value) >= 0
    },
    handleCancel() {
      // this.visible = false;
      this.$emit('handleCancelRemark', false)
    },
    handleOk() {
      this.$emit('handleOkRemark', this.dataRemark)
    },
    deleteDataRemark(index){
      this.dataRemark.splice(index, 1);
    },
    addDataRemark() {
      this.dataRemark.push({
        id: "",
        orderNo: this.orderNoRemark,
        codeName: "",
        code: "",
        note: "",
      })
    },
    getFlowSelectFunc() {
      getFlowSelect().then(res => {
       // console.log(res, 5665);
        if (res.code == 1) {
          this.flowSelect = res.data;
          //console.log('this.flowSelect',this.flowSelect)
        } else {
          this.$message.error(res.message);
        }
      })
    },

  }
}
</script>

<style lang="less" scoped>
/deep/.ant-modal-close{
  display: none;
}
/deep/.ant-modal{
  width: 60% !important;
}
</style>