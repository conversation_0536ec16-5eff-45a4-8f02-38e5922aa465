<template>
    <a-table
    :columns="columns"
    :dataSource="dataSource"
    :loading="loading"
    :scroll="{ y:665,x:300}"
    :rowKey="(record, index) => {return index;} "
    :rowClassName="isRedRow"
    :customRow="onClickRow"
    :pagination="false">
    <div slot="orderNo" slot-scope="text,record" >
        <a style="color:black" :title="record.orderNo">{{record.orderNo}}</a>&nbsp;
        <span    class="noCopy" v-if="record.isJiaji"
        style="font-size: 14px;font-weight: 500; color:#ff9900;padding: 0 ;display:inline-block;height: 19px;width:18px;margin-left:-10px;user-select: none;"
        ><a-icon type="thunderbolt" theme="filled"></a-icon> 
        </span>
    </div>
    </a-table>
</template>
<script>
const columns = [
    {
        title: "序号",
        dataIndex: "index",
        key: "index",
        align: "center",
        width: 40,
        customRender: (text,record,index) => `${index+1}`,
    },
    {
        title: "本厂编码",
        align: "left",
        //dataIndex: 'orderNo',
        scopedSlots: { customRender: 'orderNo' },
        width:130,
        ellipsis: true,
    },
     {
        title: "订单类型",
        align: "left",
        dataIndex: 'reOrderType',
        width:65,
        ellipsis: true,
    },
     {
        title: "层数",
        align: "left",
        dataIndex: 'boardLayers',
        width:50,
        ellipsis: true,
    },
     {
        title: "制作人",
        align: "left",
        dataIndex: 'proAdminName',
        width:70,
        ellipsis: true,
    },
     {
        title: "审核人",
        align: "left",
        dataIndex: 'camCheckName',
        width:70,
        ellipsis: true,
    },
     {
        title: "制作完成时间",
        align: "left",
        dataIndex: 'miEndDate_',
        width:130,
        ellipsis: true,
    },
     {
        title: "审核完成时间",
        align: "left",
        dataIndex: 'camEndTime',
        width:130,
        ellipsis: true,
    },
     {
        title: "类型",
        align: "left",
        dataIndex: 'type',
        width:90,
        ellipsis: true,
    },
     {
        title: "发送人",
        align: "left",
        dataIndex: 'sendName',
        width:85,
        ellipsis: true,
    },
     {
        title: "发送时间",
        align: "left",
        dataIndex: 'sendDate',
        width:130,
        ellipsis: true,
    },
     {
        title: "接收人",
        align: "left",
        dataIndex: 'reciveName',
        width:70,
        ellipsis: true,
    },
    {
        title: "接收时间",
        align: "left",
        dataIndex: 'reciveDate',
        width:110,
        ellipsis: true,
    },
    {
        title: "交货日期",
        align: "left",
        dataIndex: 'delDate',
        width:150,
        ellipsis: true,
    },
    {
        title: "领用人",
        align: "left",
        dataIndex: 'useName',
        width:65,
        ellipsis: true,
    },
    {
        title: "领用时间",
        align: "left",
        dataIndex: 'useDate',
        width:140,
        ellipsis: true,
    },
]
export default {
    name: '',
    components: {},
    props:['dataSource','loading'],
    data() {
        return {
         columns,
         selectrowdata:{}
        }
    },
    methods: {
     isRedRow(record){
      let strGroup = []
      let str =[]
      if(record.id && record.id == this.selectrowdata.id) {
        strGroup.push('rowBackgroundColor')
      }
      return str.concat(strGroup)
    },
     onClickRow(record,index){
        return {
            on: {
                click: () => {
                  this.selectrowdata = record
                  this.$emit('getfixture',record,index,'bot')
                }
            }
        }
    },
    },
    mounted() {

    },
}
</script>
<style lang="less" scoped>
/deep/.rowBackgroundColor {
    background: #dfdcdc!important;
  }
</style>