<template>
  <a-modal
    title="编辑信息"
    :width="640"
    :visible="leaveVisible"
    @cancel="
      () => {
        $emit('leaveHandleCancel');
      }
    "
    @ok="
      () => {
        $emit('leaveHandleOk');
      }
    "
  >
    <template slot="footer">
      <a-button key="back" @click="leaveHandleCancel"> 取消</a-button>
      <a-button key="submit" type="primary" :loading="leaveLoading" @click="leaveHandleOk"> 确定 </a-button>
    </template>
    <div ref="SelectBox">
      <a-form-model :model="data" layout="inline">
        <a-row>
          <a-col :span="8">
            <a-form-item label="雇员姓名" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-input v-model="data.realName" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="data.facName != '奔强'">
            <a-form-item label="订单标签" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-select
                ref="select"
                v-model="data.labels"
                showSearch
                allowClear
                optionFilterProp="lable"
                :getPopupContainer="() => this.$refs.SelectBox"
              >
                <a-select-option v-for="(ite, index) in list" :key="index" :value="ite.value" :lable="ite.text">{{ ite.text }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="data.facName == '奔强'">
            <a-form-item label="订单标签" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-select
                ref="select"
                v-model="data.labels"
                showSearch
                allowClear
                optionFilterProp="lable"
                :getPopupContainer="() => this.$refs.SelectBox"
              >
                <a-select-option v-for="(ite, index) in bqlist" :key="index" :value="ite.value" :lable="ite.text">{{ ite.text }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="目标数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-input v-model="data.targetCount_" allowClear ref="targetCount_" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="8">
            <a-form-item label="每日总量" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-input v-model="data.maxNum" allowClear ref="maxNum" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="单次获取" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-input v-model="data.getNum" allowClear ref="getNum" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="停留数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-input v-model="data.stayNum" allowClear ref="stayNum" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <span style="font-size: 10px; color: #ff9900; margin: 20px">*出货尺寸区间填写格式为0-5,只允许填写一个区间</span>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="出货尺寸" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-input v-model="data.boardSzie" allowClear ref="boardSzie" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="面积" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }" style="width: 100%; margin: 0">
              <a-input v-model="data.areaInterval" allowClear ref="areaInterval" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="当前状态" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-tag :color="data.isLeave_ ? '#87d068' : '#f50'">
                {{ data.isLeave_ ? "休息中" : "正常" }}
              </a-tag>
              <a-button :loading="btnLoading" :type="data.isLeave_ ? 'danger' : 'primary'" size="small" @click="statusClick()">
                {{ data.isLeave_ ? "销假" : "请假" }}
              </a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-item label="单面板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.layer1" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="双面板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.layer2" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="4层板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.layer4" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="6层板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.layer6" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="8层板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.layer8" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="军工" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.warIndustry" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-item label="单片出货" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.pcs" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="客户拼版" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.customerSet" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="工厂代拼" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.jpSet" />
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="新客户" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.isNewCust" />
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="返单" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.returnOrder" />
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="新单" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.newOrder" />
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="盲埋孔" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.blindVias" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="HDI阶数" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-select
                ref="select"
                v-model="data.hdiStep"
                showSearch
                allowClear
                optionFilterProp="lable"
                :getPopupContainer="() => this.$refs.SelectBox"
              >
                <a-select-option v-for="(ite, index) in HDIStep" :key="index" :value="ite.value" :lable="ite.text">{{ ite.text }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="特殊工艺" :label-col="{ span: 21 }" :wrapper-col="{ span: 3 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.specialProcess" />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="补强&揭盖" :label-col="{ span: 18 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.uncoverB" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="超长板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.extraLongBoard" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="LED灯板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.ledBoard" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="订单渠道" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }" style="width: 100%; margin: 0">
              <a-select
                v-model="data.tradeTypeSrc"
                mode="multiple"
                placeholder="请选择"
                showSearch
                optionFilterProp="label"
                :getPopupContainer="() => this.$refs.SelectBox"
              >
                <a-select-option v-for="item in factoryList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item
              label="黑名单代码"
              ref="custNo"
              prop="custNo"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              style="width: 100%; margin: 0"
            >
              <a-select
                placeholder="该选中代码无法取单"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-model="data.custNo"
                :dropdownMatchSelectWidth="false"
                showSearch
                :autoFocus="autoFocus"
                optionFilterProp="children"
                @popupScroll="handlePopupScroll"
                allowClear
                @search="supValue"
                mode="multiple"
              >
                <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              label="优先客户代码"
              ref="orderByCustNo"
              prop="orderByCustNo"
              :label-col="{ span: 8 }"
              :wrapper-col="{ span: 16 }"
              style="width: 100%; margin: 0"
            >
              <a-select
                placeholder="请选择优先客户代码"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-model="data.orderByCustNo"
                :dropdownMatchSelectWidth="false"
                showSearch
                :autoFocus="autoFocus"
                optionFilterProp="children"
                @popupScroll="handlePopupScroll"
                allowClear
                @search="supValue"
                mode="multiple"
              >
                <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-item label="自动发单" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.isRandom" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="自动发单时间" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
              <a-time-picker format="HH:mm" style="width: 80px" v-model="data.autoStartDate" />
              -
              <a-time-picker format="HH:mm" style="width: 80px" v-model="data.autoEndDate" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="免检" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.isExemptQae" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="工程值班" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="data.beonDuty" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="显示顺序" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <a-input-number v-model="data.sort" :min="1" :max="producerListData.length" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="直通取单" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
              <a-checkbox v-model="data.isNotFullSet" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getFactoryList, getFactoryListByGroup, getDepartGroupPostList } from "@/services/identity/user";
import { mktCustNo, mktcustnobyfAC, mktcustnobyfACV3 } from "@/services/mkt/Inquiry.js";
import Cookie from "js-cookie";
export default {
  data() {
    return {
      autoFocus: false,
      isCtrlPressed: false,
      frontDataZSupplierf: [], // 供应商 100条数据的集合
      frontDataZSupplier: [], // 供应商 100条数据的集合
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      fileList: [],
      isFileType: false,
      valueData2: undefined,
      supList2: [], //从后端查询的所有数据（不会改变）
      frontDataZSupplier2: [], // 供应商 100条数据的集合
      sourceOwnerSystems2: [], // 供应商名称的集合（过滤）
      factoryList: [],
      list: [
        { value: 0, text: "样板" },
        { value: 1, text: "批量" },
        { value: 2, text: "多层" },
        { value: 7, text: "返单" },
      ],
      bqlist: [
        { value: 3, text: "沙井" },
        { value: 4, text: "松岗" },
        { value: 5, text: "外发JX" },
        { value: 6, text: "外发NT" },
        { value: 7, text: "返单" },
      ],
      HDIStep: [
        { value: 1, text: "一阶" },
        { value: 2, text: "二阶" },
        { value: 3, text: "三阶" },
        { value: 4, text: "四阶" },
        { value: 5, text: "五阶" },
        { value: 6, text: "六阶" },
        { value: 7, text: "七阶" },
        { value: 8, text: "八阶" },
      ],
      tteam: [
        { value: "xd", text: "熊达" },
        { value: "hhb", text: "胡海波" },
        { value: "fzm", text: "冯钊明" },
        { value: "lzh", text: "李忠辉" },
        { value: "pyd", text: "彭宇达" },
        { value: "gr", text: "个人" },
        { value: "nb", text: "内部" },
        { value: "JDB", text: "捷多邦" },
        { value: "zbh", text: "周邦海" },
        { value: "bngx", text: "百能" },
      ],
    };
  },
  // props: ["data", "leaveVisible", "leaveLoading", "btnLoading", "producerListData"],
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    leaveVisible: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    leaveLoading: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    btnLoading: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    producerListData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  mounted() {
    getDepartGroupPostList().then(res => {
      const allData = res?.data;
      let arr1 = allData.comBoxItems.filter(item => {
        return item.imp_CtlThicknessInH == 1;
      });
      this.factoryList = arr1;
    });
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  methods: {
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      // if(e.key == 'c' && this.isCtrlPressed && this.leaveVisible){
      //   this.leaveHandleCancel()
      //   this.isCtrlPressed = false;
      //   e.preventDefault()
      // }else if(e.key == 'o' && this.isCtrlPressed && this.leaveVisible){
      //   this.leaveHandleOk()
      //   this.isCtrlPressed = false;
      //   e.preventDefault()
      // }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    getSupplier(UserId) {
      mktcustnobyfACV3(UserId).then(res => {
        if (res.code) {
          let that = this;
          that.supList = res.data;
          that.frontDataZSupplierf = res.data.slice(0, 20);
          that.frontDataZSupplier = that.frontDataZSupplierf;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    getSupplier1(factory) {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("mktCustNo"));
      if (
        data &&
        token &&
        data.filter(item => {
          return item.factory == factory;
        }).length
      ) {
        for (let index = 0; index < data.length; index++) {
          if (data[index].token == token && data[index].factory == factory) {
            const element = data[index];
            this.supList = element.data;
            this.frontDataZSupplierf = element.data.slice(0, 20);
            this.frontDataZSupplier = this.frontDataZSupplierf;
          }
        }
      } else {
        if (factory == 58 || factory == 59) {
          mktcustnobyfAC(factory).then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplierf = res.data.slice(0, 20);
              that.frontDataZSupplier = that.frontDataZSupplierf;
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          mktCustNo().then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplierf = res.data.slice(0, 20);
              that.frontDataZSupplier = that.frontDataZSupplierf;
              let token = Cookie.get("Authorization");
              let arr = [];
              if (JSON.stringify(that.supList) != "{}") {
                if (data == null) {
                  arr.push({ data: that.supList, token, factory });
                  localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
                } else {
                  data.push({ data: that.supList, token, factory });
                  localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
                }
              }
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value;
        let arr = that.supList.filter(m => m.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
          this.supValue("");
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.supList.slice(0, 20);
      }
    },
    leaveHandleCancel() {
      this.$emit("leaveHandleCancel");
    },
    leaveHandleOk() {
      this.$emit("leaveHandleOk");
    },
    statusClick() {
      this.$emit("statusClick");
    },
    change1(data, dateString) {
      this.data.autoStartDate = dateString;
    },
    change2(data, dateString) {
      this.data.autoEndDate = dateString;
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-time-picker-input {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/ .ant-input-number-input {
  font-weight: 500;
}
.ant-modal-body {
  .ant-form {
    .ant-row {
      .ant-col {
        .ant-form-item {
          .ant-form-item-control-wrapper {
            .ant-form-item-control {
              .ant-form-item-children {
                .ant-input-affix-wrapper {
                  /deep/ .ant-input {
                    font-weight: 500;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
