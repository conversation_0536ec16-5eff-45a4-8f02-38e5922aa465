<template>
  <div style="flex: 1">
    <p style="margin: 0;text-align: center;width: 20%;margin: 0 auto;height: 35px;line-height: 35px; font-size: 20px; font-weight: 500; background: #ED7D31; border-radius: 5px">{{title}}</p>
    <div :id="el" style="height: 300px;">
      <!-- 这里以后是地图 -->
    </div>
  </div>

</template>

<script>
import china from '../china'
import * as echarts from 'echarts/core';
import { TitleComponent, TooltipComponent } from 'echarts/components';
import { ScatterChart, EffectScatterChart, CustomChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  TitleComponent,
  TooltipComponent,
  ScatterChart,
  EffectScatterChart,
  CustomChart,
  CanvasRenderer,
  UniversalTransition
]);
export default {
  name: "mapChart",
  props:['el','chartData','title','flag'],
  watch:{
    // chartData:{
    //   handler(newVal,oldVal){
    //     console.log(newVal, oldVal)
    //     if(newVal.length)this.initChart()
    //   },
    //   immediate: true
    // }
    flag: {
      handler(newVal){
        // console.log(this.chartData,this.chartData.length)  //  数据在这个组件里使用
        if (newVal) {
          this.initChart()
        }
      }
    }
  },
  methods:{
    initChart(){
      var myChartMap = echarts.init(document.getElementById(this.el));
      myChartMap.setOption({
        // 新建一个地理坐标系 geo ，打字吧
        geo: {
          map: 'china',//地图类型为中国地图,要是世界那就是world,要是省市区：例如beijing、shanghai
          itemStyle:{ // 定义样式
            normal:{       // 普通状态下的样式
              areaColor:'#6699CC',
              borderColor: '#fff',
            },
            emphasis: {         // 高亮状态下的样式
              areaColor: '#e9fbf1'
            }
          }

        },
        // hover显示目标数据
        tooltip : {
          trigger: 'item',
          formatter(params){
            // console.log(params)
              return params.data.city_ +"<br/>"+params.data.name_;

          },
          // tooltip的trigger的值可以有'item'、'axis'。
          //'item':数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。
          //'axis':坐标轴触发，主要在柱状图，折线图等会使用类目轴的图表中使用
          textStyle:{
            align:'left'
          },
        },
        // 图表背景色
        backgroundColor: '#fff',
        // 标志颜色
        color:'red',
        // 新建散点图series
        series:[{
          name:'',//series名称
          type:'scatter',//为散点类型
          coordinateSystem: 'geo',// series坐标系类型
          data:this.chartData,
          symbol:'pin',
          symbolSize:[20,20]
        }],
      })
    }
  },
  mounted() {
    // setTimeout(()=>{this.initChart();console.log(this.chartData)},1000)
  }
}
</script>

<style scoped>

</style>