<!--合同管理-->
<template>
  <a-spin :spinning="spinning">
    <div class="reconciliation">
      <div class="mainContent" style="user-select: none">
        <main-table
          :loading="loading"
          :columns="recolumn"
          :dataSource="dataSource"
          :pagination="pagination"
          :rowKey="'id'"
          class="maintable"
          ref="maintable"
          @tableChange="handleTableChange"
        >
        </main-table>
      </div>
      <div class="footerAction">
        <make-action
          ref="footerAction"
          @queryclick="queryclick"
          @completeclick="completeclick"
          :total="pagination.total"
          @Shipment="Shipment"
          @printcontract="printcontract"
          @Downloadcontract="Downloadcontract"
        >
        </make-action>
      </div>
      <a-modal
        title="订单查询"
        :visible="querydataVisible"
        @cancel="reportHandleCancel"
        @ok="queryhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form-model :modal="querydata" class="query">
          <a-form-model-item label="订单号" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-input :autoFocus="true" v-model="querydata.orderNo" allowClear />
          </a-form-model-item>
          <a-form-model-item label="本厂编号" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-input v-model="querydata.proOrderNo" allowClear />
          </a-form-model-item>
          <a-form-model-item label="客户代码" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-input v-model="querydata.custNo" allowClear />
          </a-form-model-item>
          <a-form-model-item label="合同编号" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-input v-model="querydata.custPo" allowClear />
          </a-form-model-item>
          <a-form-model-item label="合同状态" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-select v-model="querydata.status_SC" allowClear>
              <a-select-option value="0">待回传</a-select-option>
              <a-select-option value="1">已回传</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="对账状态" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-select v-model="querydata.status_CC">
              <a-select-option value="0">未出货</a-select-option>
              <a-select-option value="10">已出货</a-select-option>
              <a-select-option value="20">已对账</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-item label="交货日期" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-date-picker placeholder="开始时间" @change="StartTime" format="YYYY-MM-DD"></a-date-picker>
            <a-divider />
            <a-date-picker placeholder="结束时间" @change="EndTime" :disabled="dis" format="YYYY-MM-DD"></a-date-picker>
          </a-form-item>
        </a-form-model>
      </a-modal>
      <a-modal
        title="确认弹窗"
        :visible="confirmVisible"
        @cancel="reportHandleCancel"
        @ok="confirmOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span>{{ confirmmessage }}</span>
      </a-modal>
      <a-modal
        title="打印对账单"
        :visible="contractVisible"
        @cancel="reportHandleCancel"
        @ok="contractOk"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        :width="1500"
        centered
      >
        <reconciliation-contract :contractdata="contractdata" ref="report"></reconciliation-contract>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import { ordercheckmannegepagelist, batchcheckaudit, exportcheckstatement, batchorderaudit, downloadcontract } from "@/services/OrderReconciliation";
import MainTable from "@/pages/mkt/OrderReconciliation/module/MainTable";
import MakeAction from "@/pages/mkt/OrderReconciliation/module/MakeAction";
import ReconciliationContract from "@/pages/mkt/OrderReconciliation/module/ReconciliationContract";
const recolumn = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "订单号",
    scopedSlots: { customRender: "orderNo" },
    align: "left",
    ellipsis: true,
    width: 140,
  },
  {
    title: "本厂编号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    width: 120,
  },
  {
    title: "合同编号",
    dataIndex: "custPo",
    align: "left",
    ellipsis: true,
    width: 150,
  },
  {
    title: "客户型号",
    dataIndex: "customerModel",
    align: "left",
    ellipsis: true,
    width: 285,
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "下单日期",
    dataIndex: "offOrderTime",
    align: "left",
    ellipsis: true,
    width: 160,
  },
  // {
  //     title: "对账状态",
  //     dataIndex: "status_CCStr",
  //     align: "left",
  //     ellipsis: true,
  //     width: 75,
  // },
  {
    title: "合同状态",
    dataIndex: "status_SCStr",
    align: "left",
    ellipsis: true,
    width: 75,
  },
  {
    title: "合同金额",
    dataIndex: "contractPrice_",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "业务员",
    dataIndex: "ywName",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "跟单员",
    dataIndex: "gdName",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "数量",
    dataIndex: "deliveryNum",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "单位",
    dataIndex: "delType",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "单价",
    dataIndex: "pcsPrice_",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "工程费",
    dataIndex: "engPrice_",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "表面工艺费",
    dataIndex: "surfaceFinishPrice_",
    align: "left",
    ellipsis: true,
    width: 90,
  },
  {
    title: "加急费",
    dataIndex: "expeditePrice_",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "飞测费",
    dataIndex: "flyPrice_",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "测试架",
    dataIndex: "testPrice_",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "加成费",
    dataIndex: "addPrice_",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "厚铜费",
    dataIndex: "thickCuPrice_",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "其他费",
    dataIndex: "allOtherPrice_",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "送货日期",
    dataIndex: "deliveryDate",
    align: "left",
    ellipsis: true,
    width: 115,
  },
  {
    title: "送货单号",
    dataIndex: "deliveryNo",
    align: "left",
    ellipsis: true,
    width: 115,
  },
];
export default {
  name: "",
  components: { MainTable, MakeAction, ReconciliationContract },
  data() {
    return {
      recolumn,
      loading: false,
      isCtrlPressed: false,
      confirmVisible: false,
      contractVisible: false,
      confirmmessage: "",
      contractdata: {},
      dis: true,
      confirmtype: "",
      querydata: {
        StartTime: null,
        EndTime: null,
      },
      spinning: false,
      querydataVisible: false,
      dataSource: [],
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  methods: {
    StartTime(value, dateString) {
      this.querydata.StartTime = dateString;
      this.dis = this.querydata.StartTime ? false : true;
    },
    EndTime(value, dateString) {
      this.querydata.EndTime = dateString;
    },
    getlistdata(querydata) {
      this.loading = true;
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      params = querydata ? { ...params, ...querydata } : params;
      ordercheckmannegepagelist(params)
        .then(res => {
          if (res.code) {
            this.dataSource = res.data.items;
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
            setTimeout(() => {
              this.handleResize();
            }, 0);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    reportHandleCancel() {
      this.querydataVisible = false;
      this.confirmVisible = false;
      this.contractVisible = false;
    },
    queryhandleOk() {
      this.getlistdata(this.querydata);
      this.$refs.maintable.selectedRowsData = {};
      this.$refs.maintable.selectedRowKeysArray = [];
      this.querydataVisible = false;
    },
    confirmOk() {
      if (this.confirmtype == "Reconciliation") {
        batchcheckaudit(this.$refs.maintable.selectedRowKeysArray).then(res => {
          if (res.code) {
            this.$message.success("对账成功");
            this.getlistdata();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.confirmtype == "Shipment") {
        batchorderaudit(this.$refs.maintable.selectedRowKeysArray).then(res => {
          if (res.code) {
            this.$message.success("出货成功");
            this.getlistdata();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      this.confirmVisible = false;
      this.$refs.maintable.selectedRowsData = {};
      this.$refs.maintable.selectedRowKeysArray = [];
    },
    contractOk() {
      this.$refs.report.getReportPdf();
    },
    printcontract() {
      if (this.$refs.maintable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      exportcheckstatement(this.$refs.maintable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.contractdata = res.data;
          this.contractVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Downloadcontract() {
      if (this.$refs.maintable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要下载的订单");
        return;
      }
      downloadcontract(this.$refs.maintable.selectedRowKeysArray).then(res => {
        if (res.code) {
          let path = res.message.split("||");
          let name = res.data.split("||");
          for (let i = 0; i < path.length - 1; i++) {
            const fileNameWithoutQuery = name[i];
            const xhr = new XMLHttpRequest();
            xhr.open("GET", path[i], true);
            xhr.responseType = "blob";
            xhr.onload = function () {
              if (xhr.status === 200) {
                const blob = xhr.response;
                const link = document.createElement("a");
                link.href = window.URL.createObjectURL(blob);
                link.download = fileNameWithoutQuery + "." + path[i].split(".")[path[i].split(".").length - 1];
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }
            };
            xhr.send();
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    queryclick() {
      this.querydataVisible = true;
      this.querydata = {};
      this.querydata.StartTime = null;
      this.querydata.EndTime = null;
    },
    completeclick() {
      if (this.$refs.maintable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要对账的订单");
        return;
      }
      this.confirmmessage = "确认所选订单对账完成吗?";
      this.confirmtype = "Reconciliation";
      this.confirmVisible = true;
    },
    Shipment() {
      if (this.$refs.maintable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要出货的订单");
        return;
      }
      this.confirmmessage = "确认所选订单更改为出货状态吗?";
      this.confirmtype = "Shipment";
      this.confirmVisible = true;
    },
    handleResize() {
      var maintable =
        document.getElementsByClassName("maintable")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var maintable1 = document.getElementsByClassName("maintable")[0];
      var mainContent = document.getElementsByClassName("mainContent")[0];
      if (window.innerHeight <= 911) {
        maintable1.style.height = window.innerHeight - 90 + "px";
        mainContent.style.height = window.innerHeight - 136 + "px";
      } else {
        mainContent.style.height = "782px";
      }
      if (this.dataSource.length) {
        maintable.style.height = window.innerHeight - 172 + "px";
      } else {
        maintable.style.height = "0px";
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.footerAction.nums * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.getlistdata(this.querydata);
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.reportHandleCancel();
        this.queryclick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.querydataVisible) {
        this.queryhandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.confirmVisible) {
        this.confirmOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.getlistdata();
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("resize", this.dehandleResize, true);
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.dehandleResize, true);
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
.leftContent {
  border: 2px solid rgb(238, 238, 238);
  border-bottom: 4px solid #e9e9f0;
}
.footerAction {
  width: 100%;
  height: 48px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background: #ffffff;
  border-top: 0;
}
.query {
  /deep/.ant-divider-horizontal {
    display: inline-block;
    clear: both;
    width: 22px;
    min-width: 22px;
    height: 1px;
    background-color: rgb(209 209 209);
    margin: 0 3px;
  }
}

/deep/.ant-calendar-picker {
  width: 125px !important;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
.reconciliation {
  background-color: white;
  /deep/.ant-table-thead > tr > th {
    padding: 6px;
    border-right: 1px solid #e8e8e8;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 6px;
    border-right: 1px solid #e8e8e8;
  }
  /deep/ .ant-table-empty .ant-table-body {
    overflow: hidden !important;
  }
  /deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #c9c9c9;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin: 10px;
    position: absolute;
  }
}
</style>
