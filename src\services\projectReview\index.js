import { request, METHOD } from "@/utils/request";
//评审管理列表数据
export function engreviewlist(params) {
  return request("/api/app/pro-order-list/eng-review-list", METHOD.GET, params);
}
//评审管理开始
export function reviewstart(Id) {
  return request(`/api/app/e-mSReview-main/review-start/${Id}`, METHOD.POST);
}
//  获取待评审回复订单列表
export function reviewmain(fac, orderNo, BusinessOrderNo, ReviewNo) {
  return request(`api/app/e-mSReview-main/review-main/${fac}?OrderNo=${orderNo}&BusinessOrderNo=${BusinessOrderNo}&ReviewNo=${ReviewNo}`, METHOD.GET);
}
export function reviewinfo(fac, orderNo, BusinessOrderNo, ReviewNo) {
  return request(`api/app/e-mSReview-main/review-info/${fac}?OrderNo=${orderNo}&BusinessOrderNo=${BusinessOrderNo}&ReviewNo=${ReviewNo}`, METHOD.GET);
}
//  生产难点工序流程
export function techflowlist(fac) {
  return request(`api/app/e-mSReview-main/tech-flow-list/${fac}`, METHOD.GET);
}
//工序负责人
export function techflowuserlist(fac) {
  return request(`/api/app/e-mSReview-main/tech-flow-user-list/${fac}`, METHOD.GET);
}
//获取授权的所有人员
export function totalreviewuserlist(fac) {
  return request(`/api/app/e-mSReview-main/total-review-user-list/${fac}`, METHOD.GET);
}
//  获取评审信息下拉
export function reviewcategory(fac) {
  return request(`api/app/e-mSReview-main/review-category/${fac}`, METHOD.GET);
}

// 保存评审信息
export function setreviewinfosave(params) {
  return request(`/api/app/e-mSReview-main/set-review-info-save`, METHOD.POST, params);
}
// 提交
export function submit(params) {
  return request(`/api/app/e-mSReview-main/submit`, METHOD.POST, params);
}
//编辑问题
export function editingproblems(params) {
  return request(`/api/app/e-mSReview-main/editing-problems`, METHOD.POST, params);
}
//回复问题
export function replyproblems(params) {
  return request(`/api/app/e-mSReview-main/reply-problems`, METHOD.POST, params);
}
//删除问题
export function setdeleetproblems(id) {
  return request(`api/app/e-mSReview-main/set-deleet-problems/${id}`, METHOD.POST);
}

//提交评审
export function toSendProblems(params) {
  return request(`/api/app/e-mSReview-main/to-send`, METHOD.POST, params);
}
//撤回评审
export function backProblems(params) {
  return request(`/api/app/e-mSReview-main/back-problems/${id}`, METHOD.POST, params);
}
