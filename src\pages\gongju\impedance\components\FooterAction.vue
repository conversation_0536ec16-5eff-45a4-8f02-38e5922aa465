<!-- 工具管理- 叠层阻抗-按钮 -->
<template>
  <div  class="footerAction" style="width: 100%; display: flex; align-items: center;padding: 0 10px;height:52px;">
    <!-- <div style="width:94px; height:52px; display: flex;align-items: center; margin-left: 10px;margin-right:106px;">
      <img style="width:100%;height:100%" src="@/assets/img/bn2.png">
      <h3 style="margin: 0px 14px;font-size: 20px;color:white;">百能</h3>
    </div> -->
    <div style="display: flex;justify-content: space-between">
      <!-- <a-button type="primary" @click="handleMenuClick" icon="apartment">
        调出结构
      </a-button> -->
      <a-button type="primary" @click="getData" icon="cloud" >
        调出模板
      </a-button>     
      <a-button type="primary" @click="countThickness" icon="calculator">
        计算板厚(F1)
      </a-button>      
      <a-button type="primary" @click="countImpedance"  icon="calculator">
        阻抗计算(F2)
      </a-button>      
      <a-button type="primary"  @click="baochun" icon="save">
        数据存盘(F3)
      </a-button>

    </div>
    <div style="text-align: right;flex: 1;">
      <!-- <a-button type="primary"  @click="getTemplate" icon="export">
        调出模板
      </a-button> -->
      <a-button type="primary"  @click="getoldData" icon="cloud-sync">
        调出数据
      </a-button>
      <a-button type="primary"  @click="exportReport" icon="printer"
      v-if="checkPermission('MES.ToolModule.ImpModule.DaoChuReport')">
        导出报表
      </a-button>
      <a-button type="primary"  @click="$emit('Exportdirectly')" icon="printer"
      v-if="checkPermission('MES.ToolModule.ImpModule.XiaZaiReport')">
        下载报表
      </a-button>
      <a-button type="primary"  @click="autoStack" icon="printer">
        自动叠层
      </a-button>
      <a-button type="primary"  @click="exportData" icon="export">
        导出数据
      </a-button>
      <a-button type="primary" icon="sync" @click="clearDataClick">
        数据清空
      </a-button>
      <a-button type="primary" @click="countCopper" icon="calculator">
        计算面铜
      </a-button>
      <a-button type="primary" @click="inverseCount" icon="calculator">
        阻抗反算
      </a-button>
      <a-button type="primary" @click="Impedancereport" icon="calculator"
      v-if="checkPermission('MES.ToolModule.ImpModule.ImpModuleOutReport')">
        阻抗报告
      </a-button>
     
      <!-- <a-select default-value="0" style="width: 120px" @change="handleChange" v-model="selectVal">
        <a-select-option :key="item.valueMember" v-for="item in factoryList" :value="item.valueMember">
          {{item.text}}
        </a-select-option>
      </a-select> -->
    </div>

  </div>
</template>

<script>
import {getFactoryList} from "@/services/impedance";
import { checkPermission } from "@/utils/abp";
import {mapState} from "vuex";
export default {
  name: "FooterAction",
  props:{
  },
  computed:{
    ...mapState({'joinFactoryId': state => state.impedance._joinFactoryId}),
  },
  data() {
    return {
      factoryList:[],
      selectVal:'PCB-M01'
    }
  },
  created() {
    // console.log(this.$route.query);
    // console.log(this.factoryList);
    // this.getFactoryData() // 获取工厂列表
    // if(this.$route.query.href && this.$route.query.href=="projectMake"){
    //   this.selectVal="216"
    // }
  },
  watch:{
    'joinFactoryId': {
      handler(val){
        if (val ==0 || val){
          this.selectVal = String(val)
        }
        // console.log('内容',val,this.selectVal)
      }
    }
  },
  methods: {
    checkPermission,
    getoldData(){
      this.$emit('getoldData')
    },
    getFactoryData(){
      getFactoryList().then(res=>{
        if(res.code){
          this.factoryList = res.data
        }
      })
    },
    handleMenuClick(e){
      this.$emit('getStructure')
    },
    countThickness(){
      this.$emit('count')
    },
    countCopper(){
      this.$emit('countCopper')
    },
    baochun(){
      this.$emit('dataSave')
    },
    exportReport(){
      this.$emit('openReport')
    },
    autoStack(){
      this.$emit('autoStack')
    },
    countImpedance() {
      this.$emit('countImpedance')
    },
    inverseCount(){
      this.$emit('inverseCount')
    },
    Impedancereport(){
      this.$emit('Impedancereport')
    },
    getData(){
      this.$emit('getAllData')
    },
    clearDataClick(){
      this.$emit('clearData')
    },
    // handleChange(val){
    //   console.log('val',val)
    //   this.$store.commit('factoryChange', val)
    //   this.$emit('factoryChange')
    // },
    exportData(){
      this.$emit('exportData')
    },
    getTemplate(){
      this.$emit('getTemplate')
    }
  },
  mounted() {
  }
}
</script>

<style lang="less" scoped>
.footerAction {
  /deep/ .ant-btn {
    margin-right: 10px;
    padding: 0;
    padding: 0 4px;
    height: 28px;
    font-size: 14px;
  }
  // /deep/ .ant-btn:nth-child(3) {
  //   margin-right: 30px;
  // }
  // /deep/ .ant-btn:nth-child(7) {
  //   margin-right: 30px;
  // }
}

</style>
