<!--控制台- 市场数据页 -->
<template>
  <div class="dataPage">
    <a-spin :spinning="spinning">
      <a-tabs type="card" v-model="activeKey" @change="tabchange">
        <!-- 个人工效 -->
        <a-tab-pane key="1" style="position: relative">
          <template #tab>
            <span>
              预审个人工效
              <a-tooltip title="导出数据">
                <a-icon type="download" @click="down5" />
              </a-tooltip>
            </span>
          </template>
          <a-month-picker
            placeholder="请选择年月"
            :format="monthFormat"
            @change="change4"
            style="position: absolute; top: -49px; right: 0"
            v-model="datevalue"
          />
          <a-select
            v-model="FactoryId"
            style="width: 130px; position: absolute; top: -49px; right: 226px"
            showSearch
            allowClear
            optionFilterProp="lable"
            @change="Factorychange"
            placeholder="授权工厂"
          >
            <a-select-option
              style="color: blue"
              v-for="(item, index) in mapKey(factroyList)"
              :key="index"
              :value="item.value"
              :lable="item.lable"
              :title="item.lable"
            >
              {{ item.lable }}
            </a-select-option>
          </a-select>
        </a-tab-pane>
        <a-tab-pane key="2" style="position: relative">
          <template #tab>
            <span>
              报价个人工效
              <a-tooltip title="导出数据">
                <a-icon type="download" @click="down5" />
              </a-tooltip>
            </span>
          </template>
          <a-month-picker
            placeholder="请选择年月"
            :format="monthFormat"
            @change="change4"
            style="position: absolute; top: -49px; right: 0"
            v-model="datevalue"
          />
          <a-select
            v-model="FactoryId"
            style="width: 130px; position: absolute; top: -49px; right: 226px"
            showSearch
            allowClear
            optionFilterProp="lable"
            @change="Factorychange"
            placeholder="授权工厂"
          >
            <a-select-option
              style="color: blue"
              v-for="(item, index) in mapKey(factroyList)"
              :key="index"
              :value="item.value"
              :lable="item.lable"
              :title="item.lable"
            >
              {{ item.lable }}
            </a-select-option>
          </a-select>
        </a-tab-pane>
      </a-tabs>
      <a-table
        :columns="columns2"
        :pagination="false"
        :dataSource="showData3"
        :loading="table3Loading"
        :rowKey="(record, index) => `${index + 1}`"
        class="mainstyle"
        :scroll="{ y: 725, x: 1200 }"
        bordered
      >
      </a-table>
      <!-- <a-table class="bmgx"
            :columns="columns1"
            :dataSource="showData1"
            :pagination="false"
            :loading='table1Loading'
            :rowKey="(record, index) => `${index + 1}`"  
            :scroll="{ x: 1700}"        
            bordered>           
        </a-table>            
        <div id="el"   :style="[{ height: 285 + 'px' }, { width: '100%' },]" style="margin-top:5px;"></div>
        <a-table class="bmgx"
            :columns="columns1"
            :dataSource="showData2"
            :pagination="false"
            :loading='table1Loading'
            :rowKey="(record, index) => `${index + 1}`"  
            :scroll="{ x: 1700}"         
            bordered>           
        </a-table>       
        <div id="el2" :style="[{ height: 272 + 'px' }, { width: '100%' }]" style="margin-top:5px;"></div>
           -->
    </a-spin>
  </div>
</template>
<script>
// import * as echarts from "echarts";
import { mapState } from "vuex";
import * as XLSX from "xlsx";
import * as echarts from "echarts/core";
import { orderDataStatisticsV2, factroyList, preartificialefficiency2, preartificialefficiency3 } from "@/services/dataPage";
import { TooltipComponent, GridComponent, LegendComponent } from "echarts/components";
const columns1 = [
  {
    title: "日期",
    dataIndex: "name",
    align: "center",
    width: 80,
    fixed: "left",
    ellipsis: "true",
  },
  {
    title: "1日",
    dataIndex: "nums.1",
    align: "center",
    width: 60,
    ellipsis: "true",
  },
  {
    title: "2日",
    dataIndex: "nums.2",
    align: "center",
    width: 60,
    ellipsis: "true",
  },
  {
    title: "3日",
    dataIndex: "nums.3",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "4日",
    dataIndex: "nums.4",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "5日",
    dataIndex: "nums.5",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "6日",
    dataIndex: "nums.6",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "7日",
    dataIndex: "nums.7",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "8日",
    dataIndex: "nums.8",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "9日",
    dataIndex: "nums.9",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "10日",
    dataIndex: "nums.10",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "11日",
    dataIndex: "nums.11",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "12日",
    dataIndex: "nums.12",
    align: "center",
    ellipsis: "true",
    width: 40,
  },
  {
    title: "13日",
    dataIndex: "nums.13",
    ellipsis: "true",
    align: "center",
    width: 60,
  },
  {
    title: "14日",
    dataIndex: "nums.14",
    align: "center",
    ellipsis: "true",
    width: 40,
  },
  {
    title: "15日",
    dataIndex: "nums.15",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "16日",
    dataIndex: "nums.16",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "17日",
    dataIndex: "nums.17",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "18日",
    dataIndex: "nums.18",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "19日",
    dataIndex: "nums.19",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "20日",
    dataIndex: "nums.20",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "21日",
    dataIndex: "nums.21",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "22日",
    dataIndex: "nums.22",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "23日",
    dataIndex: "nums.23",
    ellipsis: "true",
    align: "center",
    width: 60,
  },
  {
    title: "24日",
    dataIndex: "nums.24",
    ellipsis: "true",
    align: "center",
    width: 60,
  },
  {
    title: "25日",
    dataIndex: "nums.25",
    ellipsis: "true",
    align: "center",
    width: 60,
  },
  {
    title: "26日",
    dataIndex: "nums.26",
    ellipsis: "true",
    align: "center",
    width: 60,
  },
  {
    title: "27日",
    dataIndex: "nums.27",
    ellipsis: "true",
    align: "center",
    width: 60,
  },
  {
    title: "28日",
    dataIndex: "nums.28",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "29日",
    dataIndex: "nums.29",
    ellipsis: "true",
    align: "center",
    width: 60,
  },
  {
    title: "30日",
    dataIndex: "nums.30",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "31日",
    dataIndex: "nums.31",
    align: "center",
    ellipsis: "true",
    width: 60,
  },
  {
    title: "累计",
    dataIndex: "nums.99",
    align: "center",
    ellipsis: "true",
    width: 80,
    fixed: "right",
  },
];
const columns2 = [
  {
    title: "工厂",
    dataIndex: "factroyName",
    align: "center",
    width: 85,
    key: "factroyName",
    scopedSlots: { customRender: "factroyName" },
    customRender(text, row) {
      return {
        children: text,
        attrs: {
          rowSpan: row.factroyNameRowSpan,
        },
      };
    },
  },
  {
    title: "人员",
    dataIndex: "layers",
    align: "center",
    width: 85,
    key: "layers",
    scopedSlots: { customRender: "layers" },
    customRender(text, row) {
      return {
        children: text,
        attrs: {
          rowSpan: row.layersRowSpan,
        },
      };
    },
  },
  {
    title: "指标",
    dataIndex: "realname",
    align: "center",
    width: 70,
  },
  {
    title: "总数",
    dataIndex: "sum",
    align: "center",
    width: 70,
  },
  {
    title: "平均",
    dataIndex: "average",
    align: "center",
    width: 70,
  },
  {
    title: "1",
    dataIndex: "nums.1",
    align: "center",
    width: 40,
  },
  {
    title: "2",
    dataIndex: "nums.2",
    align: "center",
    width: 40,
  },
  {
    title: "3",
    dataIndex: "nums.3",
    align: "center",
    width: 40,
  },
  {
    title: "4",
    dataIndex: "nums.4",
    align: "center",
    width: 40,
  },
  {
    title: "5",
    dataIndex: "nums.5",
    align: "center",
    width: 40,
  },
  {
    title: "6",
    dataIndex: "nums.6",
    align: "center",
    width: 40,
  },
  {
    title: "7",
    dataIndex: "nums.7",
    align: "center",
    width: 40,
  },
  {
    title: "8",
    dataIndex: "nums.8",
    align: "center",
    width: 40,
  },
  {
    title: "9",
    dataIndex: "nums.9",
    align: "center",
    width: 40,
  },
  {
    title: "10",
    dataIndex: "nums.10",
    align: "center",
    width: 40,
  },
  {
    title: "11",
    dataIndex: "nums.11",
    align: "center",
    width: 40,
  },
  {
    title: "12",
    dataIndex: "nums.12",
    align: "center",
    width: 40,
  },
  {
    title: "13",
    dataIndex: "nums.13",
    align: "center",
    width: 40,
  },
  {
    title: "14",
    dataIndex: "nums.14",
    align: "center",
    width: 40,
  },
  {
    title: "15",
    dataIndex: "nums.15",
    align: "center",
    width: 40,
  },
  {
    title: "16",
    dataIndex: "nums.16",
    align: "center",
    width: 40,
  },
  {
    title: "17",
    dataIndex: "nums.17",
    align: "center",
    width: 40,
  },
  {
    title: "18",
    dataIndex: "nums.18",
    align: "center",
    width: 40,
  },
  {
    title: "19",
    dataIndex: "nums.19",
    align: "center",
    width: 40,
  },
  {
    title: "20",
    dataIndex: "nums.20",
    align: "center",
    width: 40,
  },
  {
    title: "21",
    dataIndex: "nums.21",
    align: "center",
    width: 40,
  },
  {
    title: "22",
    dataIndex: "nums.22",
    align: "center",
    width: 40,
  },
  {
    title: "23",
    dataIndex: "nums.23",
    align: "center",
    width: 40,
  },
  {
    title: "24",
    dataIndex: "nums.24",
    align: "center",
    width: 40,
  },
  {
    title: "25",
    dataIndex: "nums.25",
    align: "center",
    width: 40,
  },
  {
    title: "26",
    dataIndex: "nums.26",
    align: "center",
    width: 40,
  },
  {
    title: "27",
    dataIndex: "nums.27",
    align: "center",
    width: 40,
  },
  {
    title: "28",
    dataIndex: "nums.28",
    align: "center",
    width: 40,
  },
  {
    title: "29",
    dataIndex: "nums.29",
    align: "center",
    width: 40,
  },
  {
    title: "30",
    dataIndex: "nums.30",
    align: "center",
    width: 40,
  },
  {
    title: "31",
    dataIndex: "nums.31",
    align: "center",
    width: 40,
  },
];
echarts.use([TooltipComponent, GridComponent, LegendComponent]);
export default {
  name: "dataPage",
  computed: {
    ...mapState("account", ["user"]),
  },
  components: {},
  //elDatePicker:DatePicker,
  data() {
    return {
      EffectNum: "1",
      columns1,
      columns2,
      spinning: false,
      table1Loading: false,
      table2Loading: false,
      table3Loading: false,
      showData1: [],
      showData2: [],
      showData3: [],
      lineData1: [],
      lineData2: [],
      barData1: [],
      barData2: [],
      data1: [],
      activeKey: "1",
      factroyList: [],
      monthFormat: "YYYY/MM",
      datevalue: undefined,
      yearFormat: "YYYY",
      FactoryId: undefined,
      date4: "",
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    if (this.user.factoryId == 38) {
      this.activeKey = "2";
    }
    this.$nextTick(() => {
      this.handleResize();
    });
  },
  watch: {},
  methods: {
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    change4(data, dateString) {
      this.date4 = dateString || moment().startOf("month").format("YYYY/MM");
      this.Personalergonomics(dateString);
    },
    Factorychange() {
      this.Personalergonomics();
    },
    tabchange(key) {
      this.activeKey = key;
      this.date4 = "";
      this.FactoryId = undefined;
      this.datevalue = undefined;
      this.Personalergonomics();
    },
    Personalergonomics(data) {
      this.table3Loading = true;
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.date4) {
        params.date = this.date4;
      }
      if (this.FactoryId) {
        params.FactoryId = this.FactoryId;
      }
      let apiToCall = this.activeKey == 1 ? preartificialefficiency2 : preartificialefficiency3;
      apiToCall(params)
        .then(res => {
          if (res.code) {
            this.showData3 = res.data.items;
            let arr = [];
            this.showData3.forEach(item => {
              item["name"].forEach(subItem => {
                arr.push({
                  factroyName: item["factroyName"],
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                });
              });
            });
            arr.forEach(item => {
              let obj = {};
              item["nums"].forEach(ite => {
                obj[ite.date] = ite.value;
              });
              item["nums"] = obj;
            });
            this.showData3 = arr;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            this.rowSpan2("factroyName");
            this.rowSpan2("layers");
          } else {
            this.$message.error(res.msg);
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    // 导出个人功效
    down5() {
      this.table3Loading = true;
      let params = {
        pageIndex: 1,
        pageSize: 120,
      };
      if (this.date4) {
        params.date = this.date4;
      }
      if (this.FactoryId) {
        params.FactoryId = this.FactoryId;
      }
      let apiToCall = this.activeKey == 1 ? preartificialefficiency2 : preartificialefficiency3;
      apiToCall(params)
        .then(res => {
          if (res.code && res.data.items.length) {
            let showData3 = res.data.items;
            let arr = [];
            showData3.forEach(item => {
              item["name"].forEach(subItem => {
                arr.push({
                  factroyName: item["factroyName"],
                  layers: item["layers"],
                  realname: subItem["realname"],
                  sum: subItem["sum"],
                  nums: subItem["nums"],
                  average: subItem["average"],
                });
              });
            });
            arr.forEach(item => {
              let obj = {};
              if (item["nums"].length) {
                item["nums"].forEach(ite => {
                  item["z" + ite.date] = ite.value;
                });
              } else {
                for (var a = 1; a <= 31; a++) {
                  item["z" + a] = "";
                }
              }
            });
            arr.forEach(item => {
              delete item.nums;
            });
            let fileName = this.activeKey == 1 ? "当月个人预审工效分析" : "当月个人报价工效分析";
            this.exportExcelFile2(arr, fileName, ".xlsx");
          } else {
            this.$message.error("暂无数据");
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    exportExcelFile2(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      jsonWorkSheet.A1.v = "工厂";
      jsonWorkSheet.B1.v = "人员";
      jsonWorkSheet.C1.v = "指标";
      jsonWorkSheet.D1.v = "总数";
      jsonWorkSheet.E1.v = "平均";
      jsonWorkSheet.F1 ? (jsonWorkSheet.F1.v = "1") : "";
      jsonWorkSheet.G1 ? (jsonWorkSheet.G1.v = "2") : "";
      jsonWorkSheet.H1 ? (jsonWorkSheet.H1.v = "3") : "";
      jsonWorkSheet.I1 ? (jsonWorkSheet.I1.v = "4") : "";
      jsonWorkSheet.J1 ? (jsonWorkSheet.J1.v = "5") : "";
      jsonWorkSheet.K1 ? (jsonWorkSheet.K1.v = "6") : "";
      jsonWorkSheet.L1 ? (jsonWorkSheet.L1.v = "7") : "";
      jsonWorkSheet.M1 ? (jsonWorkSheet.M1.v = "8") : "";
      jsonWorkSheet.N1 ? (jsonWorkSheet.N1.v = "9") : "";
      jsonWorkSheet.O1 ? (jsonWorkSheet.O1.v = "10") : "";
      jsonWorkSheet.P1 ? (jsonWorkSheet.P1.v = "11") : "";
      jsonWorkSheet.Q1 ? (jsonWorkSheet.Q1.v = "12") : "";
      jsonWorkSheet.R1 ? (jsonWorkSheet.R1.v = "13") : "";
      jsonWorkSheet.S1 ? (jsonWorkSheet.S1.v = "14") : "";
      jsonWorkSheet.T1 ? (jsonWorkSheet.T1.v = "15") : "";
      jsonWorkSheet.U1 ? (jsonWorkSheet.U1.v = "16") : "";
      jsonWorkSheet.V1 ? (jsonWorkSheet.V1.v = "17") : "";
      jsonWorkSheet.W1 ? (jsonWorkSheet.W1.v = "18") : "";
      jsonWorkSheet.X1 ? (jsonWorkSheet.X1.v = "19") : "";
      jsonWorkSheet.Y1 ? (jsonWorkSheet.Y1.v = "20") : "";
      jsonWorkSheet.Z1 ? (jsonWorkSheet.Z1.v = "21") : "";
      jsonWorkSheet.AA1 ? (jsonWorkSheet.AA1.v = "22") : "";
      jsonWorkSheet.AB1 ? (jsonWorkSheet.AB1.v = "23") : "";
      jsonWorkSheet.AC1 ? (jsonWorkSheet.AC1.v = "24") : "";
      jsonWorkSheet.AD1 ? (jsonWorkSheet.AD1.v = "25") : "";
      jsonWorkSheet.AE1 ? (jsonWorkSheet.AE1.v = "26") : "";
      jsonWorkSheet.AF1 ? (jsonWorkSheet.AF1.v = "27") : "";
      jsonWorkSheet.AG1 ? (jsonWorkSheet.AG1.v = "28") : "";
      jsonWorkSheet.AH1 ? (jsonWorkSheet.AH1.v = "29") : "";
      jsonWorkSheet.AI1 ? (jsonWorkSheet.AI1.v = "30") : "";
      jsonWorkSheet.AJ1 ? (jsonWorkSheet.AJ1.v = "31") : "";
      let hash = {};
      let data_ = array.reduce((preVal, curVal) => {
        hash[curVal.factroyName] ? "" : (hash[curVal.factroyName] = true && preVal.push(curVal));
        hash[curVal.layers] ? "" : (hash[curVal.layers] = true && preVal.push(curVal));
        return preVal;
      }, []);
      let mergeArr_ = [];
      data_.forEach(item => {
        let start1 = array.map(o => o.factroyName).indexOf(item.factroyName);
        let end1 = array.map(o => o.factroyName).lastIndexOf(item.factroyName);
        let start2 = array.map(o => o.layers).indexOf(item.layers);
        let end2 = array.map(o => o.layers).lastIndexOf(item.layers);
        mergeArr_.push({ s: { r: start1 + 1, c: 0 }, e: { r: end1 + 1, c: 0 } }, { s: { r: start2 + 1, c: 1 }, e: { r: end2 + 1, c: 1 } });
      });
      jsonWorkSheet["!merges"] = mergeArr_;
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      console.log(workBook);
      return XLSX.writeFile(workBook, sheetName + fileName);
    },
    rowSpan2(key) {
      let _list = this.showData3;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        //  最后一条的 +1 时会为undefind 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); //获取相同的 下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              //其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          //  当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          //当与下一条数据不同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.showData3 = _list;
    },
    getData() {
      orderDataStatisticsV2().then(res => {
        if (res.code) {
          this.showData = res.data;
          this.showData1 = this.showData.xpdtos;
          let arr = [];
          this.showData1.forEach(item => {
            arr.push({
              name: item["name"],
              nums: item["dtos"],
            });
          });
          arr.forEach(item => {
            let obj = {};
            item["nums"].forEach(ite => {
              obj[ite.day] = ite.value;
            });
            item["nums"] = obj;
          });
          this.showData1 = arr;
          var Ayyay = this.showData1.find(item => {
            return item.name == "品种（款）";
          }).nums;
          this.lineData1 = Object.values(Ayyay).slice(0, Object.values(Ayyay).length - 1);
          var Ayyayy = this.showData1.find(item => {
            return item.name == "面积（㎡）";
          }).nums;
          this.barData1 = Object.values(Ayyayy).slice(0, Object.values(Ayyayy).length - 1);
          this.showData2 = this.showData.offlinedtos;
          let arr1 = [];
          this.showData2.forEach(item => {
            arr1.push({
              name: item["name"],
              nums: item["dtos"],
            });
          });
          arr1.forEach(item => {
            let obj1 = {};
            item["nums"].forEach(ite => {
              obj1[ite.day] = ite.value;
            });
            item["nums"] = obj1;
          });
          this.showData2 = arr1;
          var Ayyay1 = this.showData2.find(item => {
            return item.name == "品种（款）";
          }).nums;
          this.lineData2 = Object.values(Ayyay1).slice(0, Object.values(Ayyay1).length - 1);
          var Ayyayy2 = this.showData2.find(item => {
            return item.name == "面积（㎡）";
          }).nums;
          this.barData2 = Object.values(Ayyayy2).slice(0, Object.values(Ayyayy2).length - 1);
          this.initData();
          this.initData1();
        }
      });
    },
    initData() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el"));
      let option = {
        // title: {
        //         text: '每日询盘报价',
        //         left: 10,
        //         top: 10
        //         // textStyle: {
        //         //   color: 'red'
        //         // },
        //         // //2 标题边框
        //         // borderWidth: 5,  //边框宽度
        //         // borderColor: 'blue',  //边框颜色
        //         // borderRadius: 5, //边框圆角
        //         // left: 780,
        //         // top: 260
        //     },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            // 自定义tooltip内容和样式
            return (
              '<div style="background-color: #fff;" class="tooltipSTY">' +
              '<span style="color: #333; font-size: 14px;">' +
              params[0].name +
              "</span><br/>" +
              '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#88E2E1;"></span>' +
              '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' +
              params[0].seriesName +
              ": " +
              params[0].value +
              "</span><br/>" +
              '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#34B0F0;"></span>' +
              '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' +
              params[1].seriesName +
              ": " +
              params[1].value +
              "</span>" +
              "</div>"
            );
          },
        },
        legend: {
          show: true,
          data: ["面积(㎡)", "品种(款)"],
        },
        xAxis: {
          // type: 'category',
          data: [
            "1日",
            "2日",
            "3日",
            "4日",
            "5日",
            "6日",
            "7日",
            "8日",
            "9日",
            "10日",
            "11日",
            "12日",
            "13日",
            "14日",
            "15日",
            "16日",
            "17日",
            "18日",
            "19日",
            "20日",
            "21日",
            "22日",
            "23日",
            "24日",
            "25日",
            "26日",
            "27日",
            "28日",
            "29日",
            "30日",
            "31日",
          ],
        },
        yAxis: [
          {
            type: "value",
            id: 0,
            axisTick: {
              //y轴刻度线
              show: false,
              // lineStyle: {
              //   type: 'dashed',
              //   color: 'red',
              // },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "#88E2E1",
              },
            },
            name: "面积（㎡）",
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            axisTick: {
              //y轴刻度线
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "#34B0F0",
              },
            },
            name: "品种 (款)",
            nameLocation: "end",
            id: 1,
            axisLabel: {
              formatter: "{value}",
            },
          },
        ],
        color: ["#88E2E1"],
        series: [
          {
            name: "面积 (㎡)",
            type: "bar",
            barWidth: "20%",
            yAxisIndex: 0,
            label: {
              show: true,
              position: "top",
            },
            data: this.barData1,
            itemStyle: {
              barBorderRadius: 5,
              borderWidth: 1,
              borderType: "solid",
              // borderColor: "#ccc",
              // shadowColor: "#ccc",
              shadowBlur: 3,
            },
          },
          {
            name: "品种 (款)",
            data: this.lineData1,
            type: "line",
            yAxisIndex: 1,
            itemStyle: {
              normal: {
                color: "#34B0F0",
                width: 6,
              },
            },
          },
        ],
        grid: {
          left: "2%",
          right: "2%",
        },
      };
      myChart.setOption(option);
    },
    initData1() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("el2"));
      // 绘制图表
      let option = {
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            // 自定义tooltip内容和样式
            return (
              '<div style="background-color: #fff;" class="tooltipSTY">' +
              '<span style="color: #333; font-size: 14px;">' +
              params[0].name +
              "</span><br/>" +
              '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#88E2E1;"></span>' +
              '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' +
              params[0].seriesName +
              ": " +
              params[0].value +
              "</span><br/>" +
              '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#34B0F0;"></span>' +
              '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' +
              params[1].seriesName +
              ": " +
              params[1].value +
              "</span>" +
              "</div>"
            );
          },
        },
        legend: {
          data: ["面积(㎡)", "品种(款)"],
        },
        xAxis: {
          type: "category",
          data: [
            "1日",
            "2日",
            "3日",
            "4日",
            "5日",
            "6日",
            "7日",
            "8日",
            "9日",
            "10日",
            "11日",
            "12日",
            "13日",
            "14日",
            "15日",
            "16日",
            "17日",
            "18日",
            "19日",
            "20日",
            "21日",
            "22日",
            "23日",
            "24日",
            "25日",
            "26日",
            "27日",
            "28日",
            "29日",
            "30日",
            "31日",
          ],
        },
        yAxis: [
          {
            type: "value",
            id: 0,
            axisTick: {
              //y轴刻度线
              show: false,
              // lineStyle: {
              //   type: 'dashed',
              //   color: 'red',
              // },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "#88E2E1",
              },
            },
            name: "面积（㎡）",
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            axisTick: {
              //y轴刻度线
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "#34B0F0",
              },
            },
            name: "品种 (款)",
            nameLocation: "end",
            id: 1,
            axisLabel: {
              formatter: "{value}",
            },
          },
        ],
        color: ["#88E2E1"],
        series: [
          {
            name: "面积 (㎡)",
            type: "bar",
            barWidth: "20%",
            yAxisIndex: 0,
            label: {
              show: true,
              position: "top",
            },
            data: this.barData2,
            itemStyle: {
              barBorderRadius: 5,
              borderWidth: 1,
              borderType: "solid",
              // borderColor: "#ccc",
              // shadowColor: "#ccc",
              shadowBlur: 3,
            },
          },
          {
            name: "品种 (款)",
            data: this.lineData2,
            type: "line",
            yAxisIndex: 1,
            itemStyle: {
              normal: {
                color: "#34B0F0",
              },
            },
          },
        ],
        grid: {
          left: "2%",
          right: "2%",
        },
      };
      myChart.setOption(option);
    },
    handleResize() {
      var mainstyle = document.getElementsByClassName("mainstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var dataPage = document.getElementsByClassName("dataPage")[0];
      if (window.innerHeight <= 911) {
        dataPage.style.height = window.innerHeight - 90 + "px";
      } else {
        dataPage.style.height = "820px";
      }
      if (mainstyle && this.showData3.length != 0) {
        mainstyle.style.height = window.innerHeight - 197 + "px";
      } else {
        mainstyle.style.height = 0;
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  mounted() {
    window.addEventListener("resize", this.dehandleResize, true);
    this.Personalergonomics();
    factroyList().then(res => {
      if (res.code) {
        this.factroyList = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
  },
};
</script>

<style scoped lang="less">
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table-placeholder {
  height: 170px;
}
/deep/.ant-empty-normal {
  margin-top: 30px !important;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.dataPage {
  background: #ffffff;
  padding: 10px 10px;
  /deep/ .ant-table {
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    margin: 0;
    min-width: 80px;
    text-align: center;
  }

  /deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
    background: #fff9e6;
  }

  /deep/.ant-table-thead > tr > th {
    padding: 6px 0 !important;
  }

  /deep/ .ant-table-tbody > tr > td {
    padding: 6px 0 !important;
  }

  #tab {
    tr {
      th {
        height: 36px;
        padding: 0;
      }

      td {
        padding: 0;
      }
    }
  }
}

.ant-calendar-picker-container {
  right: 0;

  .ant-calendar-range {
    width: 400px;
  }
}

/deep/ .bmgx .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
