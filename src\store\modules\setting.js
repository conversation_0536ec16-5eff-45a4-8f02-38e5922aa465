import config from "@/config";
import { ADMIN } from "@/config/default";
import { formatFullPath } from "@/utils/i18n";
import { filterMenu } from "@/utils/authority-utils";
import { getLocalSetting } from "@/utils/themeUtil";

const localSetting = getLocalSetting(true);
const customTitlesStr = sessionStorage.getItem(process.env.VUE_APP_TBAS_TITLES_KEY);
const customTitles = (customTitlesStr && JSON.parse(customTitlesStr)) || [];

export default {
  namespaced: true,
  state: {
    isMobile: false,
    loadclick: false,
    editstatus: false,
    animates: ADMIN.animates,
    palettes: ADMIN.palettes,
    pageMinHeight: 0,
    infolength: 0,
    menuData: [],
    activatedFirst: undefined,
    customTitles,
    pagination: {
      pageSize: 10,
      current: 1,
      showQuickJumper: true,
      showTotal: total => `总计 ${total} 条`,
    },
    ...config,
    ...localSetting,
  },
  getters: {
    loadclick: state => {
      if (!state.loadclick) {
        try {
          const loadclick = localStorage.getItem(process.env.VUE_APP_LOAD_KEY);
          state.loadclick = JSON.parse(loadclick);
        } catch (e) {
          console.error(e);
        }
      }
      return state.loadclick;
    },
    infolength: state => {
      if (state.infolength === 0) {
        try {
          const infolength = localStorage.getItem(process.env.VUE_APP_INFO_KEY);
          state.infolength = infolength;
        } catch (e) {
          console.error(e);
        }
      }
      return state.infolength;
    },
    editstatus: state => {
      if (!state.editstatus) {
        try {
          const editstatus = localStorage.getItem(process.env.VUE_APP_EDIT_KEY);
          state.editstatus = JSON.parse(editstatus);
        } catch (e) {
          console.error(e);
        }
      }
      return state.editstatus;
    },
    menuData(state, getters, rootState) {
      if (state.filterMenu) {
        const { permissions, roles } = rootState.account;
        // return filterMenu(state.menuData, permissions, roles)
        return filterMenu(JSON.parse(JSON.stringify(state.menuData)), permissions, roles);
      }
      return state.menuData;
    },
    firstMenu(state) {
      const { menuData } = state;
      if (menuData.length > 0 && !menuData[0].fullPath) {
        formatFullPath(menuData);
      }
      return menuData.map(item => {
        const menuItem = { ...item };
        delete menuItem.children;
        return menuItem;
      });
    },
    subMenu(state) {
      const { menuData, activatedFirst } = state;
      if (menuData.length > 0 && !menuData[0].fullPath) {
        formatFullPath(menuData);
      }
      const current = menuData.find(menu => menu.fullPath === activatedFirst);
      return (current && current.children) || [];
    },
  },
  mutations: {
    setDevice(state, isMobile) {
      state.isMobile = isMobile;
    },
    setload(state, loadclick) {
      state.loadclick = loadclick;
      localStorage.setItem(process.env.VUE_APP_LOAD_KEY, JSON.stringify(loadclick));
    },
    setedit(state, editstatus) {
      state.editstatus = editstatus;
      localStorage.setItem(process.env.VUE_APP_EDIT_KEY, JSON.stringify(editstatus));
    },
    setTheme(state, theme) {
      state.theme = theme;
    },
    setLayout(state, layout) {
      state.layout = layout;
    },
    setMultiPage(state, multiPage) {
      state.multiPage = multiPage;
    },
    setAnimate(state, animate) {
      state.animate = animate;
    },
    setWeekMode(state, weekMode) {
      state.weekMode = weekMode;
    },
    setFixedHeader(state, fixedHeader) {
      state.fixedHeader = fixedHeader;
    },
    setFixedSideBar(state, fixedSideBar) {
      state.fixedSideBar = fixedSideBar;
    },
    setLang(state, lang) {
      state.lang = lang;
    },
    setHideSetting(state, hideSetting) {
      state.hideSetting = hideSetting;
    },
    correctPageMinHeight(state, minHeight) {
      state.pageMinHeight += minHeight;
    },
    setinfolength(state, infolength) {
      state.infolength = infolength;
      localStorage.setItem(process.env.VUE_APP_INFO_KEY, infolength);
    },
    setMenuData(state, menuData) {
      state.menuData = menuData;
    },
    setAsyncRoutes(state, asyncRoutes) {
      state.asyncRoutes = asyncRoutes;
    },
    setPageWidth(state, pageWidth) {
      state.pageWidth = pageWidth;
    },
    setActivatedFirst(state, activatedFirst) {
      state.activatedFirst = activatedFirst;
    },
    setFixedTabs(state, fixedTabs) {
      state.fixedTabs = fixedTabs;
    },
    setCustomTitle(state, { path, title }) {
      if (title) {
        const obj = state.customTitles.find(item => item.path === path);
        if (obj) {
          obj.title = title;
        } else {
          state.customTitles.push({ path, title });
        }
        sessionStorage.setItem(process.env.VUE_APP_TBAS_TITLES_KEY, JSON.stringify(state.customTitles));
      }
    },
  },
};
