<!--供应链  - 供应商详情 -基本信息 -->
<template>
  <div ref="selectBox">
    <a-spin :spinning="spinning">
      <a-descriptions bordered :column="column">
        <a-descriptions-item :label="$t('company_name')">
          <span :title="basicData.name_">{{ basicData.name_ }}</span>
          <!--<a-input type="text" v-model="basicData.name_" v-else/>-->
        </a-descriptions-item>
        <a-descriptions-item :label="$t('factory_code')">
          <span v-if="type == '1'" :title="basicData.factoryCode_">{{ basicData.factoryCode_ }}</span>
          <a-input type="text" :title="basicData.factoryCode_" v-model="basicData.factoryCode_" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('legal_representative')">
          <span v-if="type == '1'" :title="basicData.legalPerson_">{{ basicData.legalPerson_ }}</span>
          <a-input type="text" :title="basicData.legalPerson_" v-model="basicData.legalPerson_" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cooperation_stage')">
          <span v-if="type == '1'" :title="basicData.cooperation_">{{ basicData.cooperation_ }}</span>
          <a-select :title="basicData.cooperation_" v-else v-model="basicData.cooperation_" :getPopupContainer="() => this.$refs.selectBox">
            <a-select-option value=""> 请选择 </a-select-option>
            <a-select-option value=" 无意向"> 无意向 </a-select-option>
            <a-select-option value=" 有意向"> 有意向 </a-select-option>
            <a-select-option value="合作中"> 合作中 </a-select-option>
            <a-select-option value="试单中"> 试单中 </a-select-option>
            <a-select-option value="终止合作"> 终止合作 </a-select-option>
          </a-select>
        </a-descriptions-item>
        <a-descriptions-item :label="$t('company_nature')">
          <span v-if="type == '1'" :title="basicData.nature_">{{ basicData.nature_ }}</span>
          <a-select
            style="width: 120px"
            :title="basicData.nature_"
            v-else
            v-model="basicData.nature_"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value=""> 请选择 </a-select-option>
            <a-select-option value="中外合资"> 中外合资 </a-select-option>
            <a-select-option value="国企"> 国企 </a-select-option>
            <a-select-option value="港资企业"> 港资企业 </a-select-option>
            <a-select-option value="台资企业"> 台资企业 </a-select-option>
            <a-select-option value="上市公司"> 上市公司 </a-select-option>
            <a-select-option value="国内独资"> 国内独资 </a-select-option>
          </a-select>
        </a-descriptions-item>

        <a-descriptions-item :label="$t('company_address')">
          <span v-if="type == '1'" :title="basicData.address_">{{ basicData.address_ }}</span>
          <a-input type="text" :title="basicData.address_" v-model="basicData.address_" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('province_city_district')">
          <span v-if="type == '1'" :title="hitArray[0] + hitArray[1] + hitArray[2]">{{ hitArray[0] }}{{ hitArray[1] }}{{ hitArray[2] }}</span>
          <a-cascader
            style="width: 150px"
            :options="cityData"
            placeholder="请选择"
            @change="onChangeData"
            v-model="checkCity_"
            defaultOpen
            v-else
            :getPopupContainer="() => this.$refs.selectBox"
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cooperation_mode')">
          <span v-if="type == '1'" :title="basicData.cooperationMode_">{{ basicData.cooperationMode_ }}</span>
          <a-select
            style="width: 120px"
            v-else
            :title="basicData.cooperationMode_"
            v-model="basicData.cooperationMode_"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value=""> 请选择 </a-select-option>
            <a-select-option value="零单"> 零单 </a-select-option>
            <a-select-option value="协同"> 协同 </a-select-option>
          </a-select>
        </a-descriptions-item>
        <!-- <a-descriptions-item label="市">
                    <span v-if="type=='1'">{{basicData.city_}}</span>
                    <a-input type="text" v-model="basicData.city_" v-else/>
                </a-descriptions-item> -->
        <a-descriptions-item :label="$t('establishment_date')">
          <span v-if="type == '1'" :title="basicData.buildDate_">{{ basicData.buildDate_ }}</span>
          <!--<a-input type="text" v-model="basicData.buildDate_" v-else/>-->
          <a-date-picker :title="basicData.buildDate_" v-model="basicData.buildDate_" @change="onChangeTime" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('registered_capital')">
          <span v-if="type == '1'" :title="basicData.funds_">{{ basicData.funds_ }}</span>
          <a-input type="number" :title="basicData.funds_" v-model="basicData.funds_" v-else @keydown="valueChange" />
        </a-descriptions-item>

        <a-descriptions-item :label="$t('land_area')">
          <span v-if="type == '1'" :title="basicData.footprint_">{{ basicData.footprint_ }}</span>
          <a-input type="number" :title="basicData.footprint_" v-model="basicData.footprint_" v-else @keydown="valueChange" />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('factory_area')">
          <span v-if="type == '1'" :title="basicData.landArea_">{{ basicData.landArea_ }}</span>
          <a-input type="number" :title="basicData.landArea_" v-model="basicData.landArea_" v-else @keydown="valueChange" />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('quality_department')">
          <span v-if="type == '1'" :title="basicData.qualitySu_">{{ basicData.qualitySu_ }}</span>
          <a-input
            type="text"
            :title="basicData.qualitySu_"
            v-model="basicData.qualitySu_"
            oninput="this.value = this.value.replace(/[^0-9]/g, '')"
            v-else
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('production_workers')">
          <span v-if="type == '1'">{{ basicData.workerSu_ }}</span>
          <a-input type="text" v-model="basicData.workerSu_" oninput="this.value = this.value.replace(/[^0-9]/g, '')" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('production_engineers')">
          <span v-if="type == '1'" :title="basicData.proEngSu_">{{ basicData.proEngSu_ }}</span>
          <a-input
            type="text"
            :title="basicData.proEngSu_"
            v-model="basicData.proEngSu_"
            oninput="this.value = this.value.replace(/[^0-9]/g, '')"
            v-else
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('total_employees')">
          <span v-if="type == '1'" :title="basicData.totalSu_">{{ basicData.totalSu_ }}</span>
          <a-input
            type="text"
            :title="basicData.totalSu_"
            v-model="basicData.totalSu_"
            oninput="this.value = this.value.replace(/[^0-9]/g, '')"
            v-else
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('shifts')">
          <span v-if="type == '1'" :title="basicData.freq_">{{ basicData.freq_ }}</span>
          <a-select style="width: 150px" :title="basicData.freq_" v-else v-model="basicData.freq_" :getPopupContainer="() => this.$refs.selectBox">
            <a-select-option value=""> 请选择 </a-select-option>
            <a-select-option value=" 白班"> 白班 </a-select-option>
            <a-select-option value=" 晚班"> 晚班 </a-select-option>
            <a-select-option value="2班"> 2班 </a-select-option>
            <a-select-option value="  3班"> 3班 </a-select-option>
          </a-select>
        </a-descriptions-item>

        <a-descriptions-item :label="$t('single_panel_lead_time')">
          <span v-if="type == '1'" :title="basicData.oneDate_">{{ basicData.oneDate_ }}</span>
          <a-input
            type="text"
            :title="basicData.oneDate_"
            v-model="basicData.oneDate_"
            oninput="this.value = this.value.replace(/[^0-9]/g, '')"
            v-else
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('double_panel_lead_time')">
          <span v-if="type == '1'" :title="basicData.twoDate_">{{ basicData.twoDate_ }}</span>
          <a-input
            type="text"
            :title="basicData.twoDate_"
            v-model="basicData.twoDate_"
            oninput="this.value = this.value.replace(/[^0-9]/g, '')"
            v-else
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('multilayer_panel_lead_time')">
          <span v-if="type == '1'" :title="basicData.manyDate_">{{ basicData.manyDate_ }}</span>
          <a-input
            type="text"
            :title="basicData.manyDate_"
            v-model="basicData.manyDate_"
            oninput="this.value = this.value.replace(/[^0-9]/g, '')"
            v-else
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('equipment_capacity')">
          <span v-if="type == '1'" :title="basicData.capacity_">{{ basicData.capacity_ }}</span>
          <a-input type="number" :title="basicData.capacity_" v-model="basicData.capacity_" @keydown="valueChange" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('actual_output')">
          <span v-if="type == '1'" :title="basicData.output_">{{ basicData.output_ }}</span>
          <a-input type="number" :title="basicData.output_" v-model="basicData.output_" v-else @keydown="valueChange" />
        </a-descriptions-item>

        <a-descriptions-item :label="$t('average_value')">
          <span v-if="type == '1'" :title="basicData.outputValue_">{{ basicData.outputValue_ }}</span>
          <a-input type="number" :title="basicData.outputValue_" v-model="basicData.outputValue_" v-else @keydown="valueChange" />
        </a-descriptions-item>

        <a-descriptions-item :label="$t('average_area_per_order')">
          <span v-if="type == '1'" :title="basicData.average_">{{ basicData.average_ }}</span>
          <a-input type="number" :title="basicData.average_" v-model="basicData.average_" v-else @keydown="valueChange" />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('number_of_part_numbers')">
          <span v-if="type == '1'" :title="basicData.itemNo_">{{ basicData.itemNo_ }}</span>
          <a-input
            type="text"
            :title="basicData.itemNo_"
            v-model="basicData.itemNo_"
            oninput="this.value = this.value.replace(/[^0-9]/g, '')"
            v-else
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('developer')">
          <span v-if="type == '1'" :title="basicData.unfold_">{{ basicData.unfold_ }}</span>
          <a-input type="text" :title="basicData.unfold_" v-model="basicData.unfold_" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('min_line_width_spacing')">
          <span v-if="type == '1'" :title="basicData.lineAbility_">{{ basicData.lineAbility_ }}</span>
          <a-input type="number" :title="basicData.lineAbility_" v-model="basicData.lineAbility_" v-else @keydown="valueChange" />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('min_finished_board_thickness')">
          <span v-if="type == '1'" :title="basicData.boardThk_">{{ basicData.boardThk_ }}</span>
          <a-input type="number" :title="basicData.boardThk_" v-model="basicData.boardThk_" v-else @keydown="valueChange" />
        </a-descriptions-item>
        <!--<a-descriptions-item label="已拜访">-->
        <!--<a-checkbox v-model="basicData.visit_">-->
        <!--</a-checkbox>-->
        <!--</a-descriptions-item>-->
        <a-descriptions-item :label="$t('max_number_of_layers')">
          <span v-if="type == '1'" :title="basicData.layers_">{{ basicData.layers_ }}</span>
          <a-input type="number" :title="basicData.layers_" v-model="basicData.layers_" v-else @keydown="valueChange" />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('max_copper_thickness')">
          <span v-if="type == '1'" :title="basicData.cuThk_">{{ basicData.cuThk_ }}</span>
          <a-input type="number" :title="basicData.cuThk_" v-model="basicData.cuThk_" v-else @keydown="valueChange" />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('main_laminate_brands')">
          <span v-if="type == '1'" :title="basicData.sheet_">{{ basicData.sheet_ }}</span>
          <a-select
            style="width: 220px"
            v-else
            :title="sheet_List"
            v-model="sheet_List"
            option-label-prop="label"
            mode="multiple"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value="" label="" disabled> 请选择 </a-select-option>
            <a-select-option value="生益" label="生益"> 生益 </a-select-option>
            <a-select-option value="建滔" label="建滔"> 建滔 </a-select-option>
            <a-select-option value="国纪" label="国纪"> 国纪 </a-select-option>
            <a-select-option value="  宏瑞兴" label="  宏瑞兴"> 宏瑞兴 </a-select-option>
          </a-select>
        </a-descriptions-item>
        <a-descriptions-item :label="$t('main_ink_brands')">
          <span v-if="type == '1'" :title="basicData.ink_">{{ basicData.ink_ }}</span>
          <a-select
            style="width: 220px"
            v-else
            v-model="ink_List"
            :title="ink_List"
            option-label-prop="label"
            mode="multiple"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value="" label="" disabled> 请选择 </a-select-option>
            <a-select-option value="太阳" label="太阳"> 太阳 </a-select-option>
            <a-select-option value="广臻" label="广臻"> 广臻 </a-select-option>
            <a-select-option value="永胜泰" label="永胜泰"> 永胜泰 </a-select-option>
            <a-select-option value="  川裕" label="  川裕"> 川裕 </a-select-option>
            <a-select-option value="联合" label="联合"> 联合 </a-select-option>
          </a-select>
        </a-descriptions-item>

        <a-descriptions-item :label="$t('collaboration_category')">
          <span v-if="type == '1'" :title="basicData.category">{{ basicData.category }}</span>
          <a-select
            style="width: 220px"
            v-else
            v-model="category_List"
            :title="category_List"
            option-label-prop="label"
            mode="multiple"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value="" label="" disabled> 请选择 </a-select-option>
            <a-select-option value="单面" label="单面"> 单面 </a-select-option>
            <a-select-option value="铝基" label="铝基"> 铝基 </a-select-option>
            <a-select-option value="双面" label="双面"> 双面 </a-select-option>
            <a-select-option value="多层" label="多层"> 多层 </a-select-option>
            <a-select-option value="FPC" label="FPC"> FPC </a-select-option>
          </a-select>
        </a-descriptions-item>

        <a-descriptions-item :label="$t('collaboration_grade')">
          <span v-if="type == '1'" :title="basicData.grade">{{ basicData.grade }}</span>
          <a-select
            style="width: 220px"
            v-else
            v-model="grade_List"
            :title="grade_List"
            option-label-prop="label"
            mode="multiple"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value="" label="" disabled> 请选择 </a-select-option>
            <a-select-option value="精品" label="精品"> 精品 </a-select-option>
            <a-select-option value="优品" label="优品"> 优品 </a-select-option>
            <a-select-option value="标品" label="标品"> 标品 </a-select-option>
          </a-select>
        </a-descriptions-item>

        <a-descriptions-item :label="$t('order_type')">
          <span v-if="type == '1'" :title="basicData.orderType">{{ basicData.orderType }}</span>
          <a-select
            style="width: 220px"
            v-else
            v-model="orderType_List"
            :title="orderType_List"
            option-label-prop="label"
            mode="multiple"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value="" label="" disabled> 请选择 </a-select-option>
            <a-select-option value="打样" label="打样"> 打样 </a-select-option>
            <a-select-option value="中小批量" label="中小批量"> 中小批量 </a-select-option>
            <a-select-option value="大批量" label="大批量"> 大批量 </a-select-option>
          </a-select>
        </a-descriptions-item>

        <a-descriptions-item :label="$t('agreed_capacity')">
          <span v-if="type == '1'" :title="basicData.agreedCapacity">{{ basicData.agreedCapacity }}</span>
          <a-input type="text" v-model="basicData.agreedCapacity" :title="basicData.agreedCapacity" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('etching_process')">
          <span v-if="type == '1'" :title="basicData.etching_">{{ basicData.etching_ }}</span>
          <a-input type="text" v-model="basicData.etching_" :title="basicData.etching_" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('circuit_process')">
          <span v-if="type == '1'" :title="basicData.lineCraft_">{{ basicData.lineCraft_ }}</span>
          <a-select
            style="width: 150px"
            v-else
            :title="basicData.lineCraft_"
            v-model="basicData.lineCraft_"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option value="" disabled> 请选择 </a-select-option>
            <a-select-option value=" 干膜"> 干膜 </a-select-option>
            <a-select-option value=" 湿膜 "> 湿膜 </a-select-option>
          </a-select>
        </a-descriptions-item>
        <a-descriptions-item :label="$t('imaging_process')">
          <span v-if="type == '1'" :title="basicData.graphCraft_">{{ basicData.graphCraft_ }}</span>
          <a-select
            style="width: 220px"
            v-else
            :title="basicData.graphCraft_"
            v-model="basicData.graphCraft_"
            :getPopupContainer="() => this.$refs.selectBox"
          >
            <a-select-option disabled value=" "> 请选择 </a-select-option>
            <a-select-option value=" 正片"> 正片 </a-select-option>
            <a-select-option value=" 负片 "> 负片 </a-select-option>
          </a-select>
        </a-descriptions-item>
        <a-descriptions-item :label="$t('through_plating_process')">
          <span v-if="type == '1'" :title="basicData.cuCraft_">{{ basicData.cuCraft_ }}</span>
          <a-input type="text" :title="basicData.cuCraft_" v-model="basicData.cuCraft_" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('none_through_plating_process')">
          <span v-if="type == '1'" :title="basicData.notCuCraft_">{{ basicData.notCuCraft_ }}</span>
          <a-input type="text" :title="basicData.notCuCraft_" v-model="basicData.notCuCraft_" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('main_product')" :span="3">
          <span v-if="type == '1'">
            <a-checkbox :checked="updateData.glass_"> {{ $t("glass_fiber_rigid_board") }} </a-checkbox>
            <a-checkbox :checked="updateData.alminum_"> {{ $t("aluminum_base_board") }} </a-checkbox>
            <a-checkbox :checked="updateData.cuminum_"> {{ $t("copper_base_board") }} </a-checkbox>
            <a-checkbox :checked="updateData.flexible_"> {{ $t("flexible_board") }} </a-checkbox>
            <a-checkbox :checked="updateData.hard_"> {{ $t("rigid_flex_board") }} </a-checkbox>
            <a-checkbox :checked="updateData.ceramic_"> {{ $t("ceramic_board") }} </a-checkbox>
            <a-checkbox :checked="updateData.high_"> {{ $t("high_frequency_high_speed") }} </a-checkbox>
          </span>
          <span v-else>
            <a-checkbox-group @change="onChange" v-model="checkbox_">
              <a-checkbox value="glass_"> {{ $t("glass_fiber_rigid_board") }} </a-checkbox>
              <a-checkbox value="alminum_"> {{ $t("aluminum_base_board") }} </a-checkbox>
              <a-checkbox value="cuminum_"> {{ $t("copper_base_board") }} </a-checkbox>
              <a-checkbox value="flexible_"> {{ $t("flexible_board") }} </a-checkbox>
              <a-checkbox value="hard_"> {{ $t("rigid_flex_board") }} </a-checkbox>
              <a-checkbox value="ceramic_"> {{ $t("ceramic_board") }} </a-checkbox>
              <a-checkbox value="high_"> {{ $t("high_frequency_high_speed") }} </a-checkbox>
            </a-checkbox-group>
          </span>
        </a-descriptions-item>
        <a-descriptions-item :label="$t('full_company_name')">
          <span v-if="type == '1'" :title="basicData.detailName">{{ basicData.detailName }}</span>
          <a-input type="text" :title="basicData.detailName" v-model="basicData.detailName" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('english_name')">
          <span v-if="type == '1'" :title="basicData.englishName">{{ basicData.englishName }}</span>
          <a-input type="text" :title="basicData.englishName" v-model="basicData.englishName" v-else />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('billing_settings')">
          <span v-if="type == '1'">
            <a-checkbox :checked="updateData.allowIssuing"> {{ $t("allow_billing") }} </a-checkbox>
            <a-checkbox :checked="updateData.importIssuing"> {{ $t("priority_billing") }} </a-checkbox>
          </span>
          <span v-else>
            <a-checkbox-group @change="onChange1" v-model="checkbox_1">
              <a-checkbox value="allowIssuing"> {{ $t("allow_billing") }} </a-checkbox>
              <a-checkbox value="importIssuing"> {{ $t("priority_billing") }} </a-checkbox>
            </a-checkbox-group>
          </span>
        </a-descriptions-item>
        <a-descriptions-item :label="$t('upload_logo')">
          <span style="display: flex">
            <span v-if="type == '1'" v-viewer style="height: 36px">
              <img v-if="basicData.logoAddress" :src="basicData.logoAddress" style="width: 63px; margin-right: 10px; height: 36px" />
            </span>
            <a-upload
              v-else
              accept=".jpg,.png,.gif,.bmp,.jpeg,"
              name="file"
              ref="fileRef"
              :before-upload="beforeUpload1"
              :customRequest="downloadFilesCustomRequest"
              :file-list="fileListData"
              @change="handleChange1"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <a-button v-if="type != '1' && fileListData.length == 0"> {{ $t("upload_logo") }} </a-button>
            </a-upload>
          </span>
        </a-descriptions-item>
      </a-descriptions>
    </a-spin>
    <div style="margin-top: 15px; text-align: center">
      <a-button type="primary" style="margin-right: 5px" @click="editDataInfo" v-if="type == '2'">{{ $t("save_button") }}</a-button>
      <!-- <a-button @click="$router.go(-1)" >返回</a-button> -->
    </div>
  </div>
</template>

<script>
import { editData, progressNum, finishChange, modulenouploadfile } from "@/services/supplier/index";

export default {
  i18n: require("@/components/language/modules/supplierS/supplierS_i18n.js"),
  props: {
    basicData: {
      type: Object,
      default() {
        return {};
      },
    },
    type: {
      type: String,
      default() {
        return "";
      },
    },
    checkbox: {
      type: Array,
      default() {
        return [];
      },
    },
    checkbox1: {
      type: Array,
      default() {
        return [];
      },
    },
    cityData: {
      type: Array,
      default() {
        return [];
      },
    },
    hitArray: {
      type: Array,
      default() {
        return [];
      },
    },
    checkCity: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      previewVisible: false,
      previewImage: "",
      isFileType: true,
      path: "", // 上传图片返回地址
      fileListData: [],
      size: "small",
      column: 4,
      sapnD: 4,
      spinning: false,
      updateData: {},
      chooseData: [],
      checkCity_: this.checkCity,
      checkbox_: this.checkbox,
      checkbox_1: this.checkbox1,
      ink_List: [],
      sheet_List: [],
      category_List: [],
      grade_List: [],
      orderType_List: [],
      titleName: this.hitArray[0] + this.hitArray[1] + this.hitArray[2],
    };
  },
  watch: {
    basicData(val) {
      if (val.ink_ != null) {
        this.ink_List = val.ink_.split(",");
      }
      if (val.sheet_ != null) {
        this.sheet_List = val.sheet_.split(",");
      }

      if (val.category != null) {
        this.category_List = val.category.split(",");
      }
      if (val.grade != null) {
        this.grade_List = val.grade.split(",");
      }
      if (val.orderType != null) {
        this.orderType_List = val.orderType.split(",");
      }
      if (!val.ink) {
        this.ink_List = [];
      }
      if (!val.sheet_) {
        this.sheet_List = [];
      }
      if (!val.category) {
        this.category_List = [];
      }
      if (!val.grade) {
        this.grade_List = [];
      }
      if (!val.orderType) {
        this.orderType_List = [];
      }
      if (val.logoAddress) {
        this.fileListData = [];
        this.fileListData.push({
          uid: 1,
          name: "image.png",
          status: "done",
          url: val.logoAddress,
          thumbUrl: val.logoAddress, //缩略图地址
        });
      }
      this.updateData = val;
    },
  },
  methods: {
    handlePreview(file) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
    },
    async downloadFilesCustomRequest(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await modulenouploadfile(formData).then(res => {
        if (res.code) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleChange1({ fileList }, data) {
      this.fileListData = fileList;
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
      if (this.fileListData.length == 0) {
        this.path = "";
      } else {
        this.path = this.fileListData[0].response || this.fileListData[0].thumbUrl;
      }
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("上传LOGO只支持.jpg/.png图片格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    valueChange(e) {
      // 通过正则过滤小数点后两位
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,2})/g)[0] || null;
    },
    //时间选择
    onChangeTime(data, dateString) {
      this.basicData.buildDate_ = dateString;
    },
    //切换省市级方法
    onChangeData(val) {
      this.chooseData = val;
      let hitArray = [];
      console.log("改变", this.chooseData);
      // if(this.chooseData.length>0){
      //     let searchLeaf = (items,hittext) => {
      //     for(let index in items){
      //     if(items[index].value == hittext){
      //     hitArray.push(items[index].label);
      //     if(hitArray.length == 1){
      //         this.titleName = items[index].label
      //     }else if(hitArray.length > 1){
      //         this.titleName = this.titleName+ '/' + items[index].label
      //     }
      //         return false
      //     }
      //     searchLeaf(items[index].children,hittext);
      //     }
      //     }
      //     searchLeaf(this.cityData,this.chooseData[0])
      //     searchLeaf(this.cityData,this.chooseData[1])
      //     searchLeaf(this.cityData,this.chooseData[2])
      // }
    },
    onChange(checkedValues) {
      if (checkedValues.indexOf("glass_") == "-1") {
        this.updateData.glass_ = false;
      } else {
        this.updateData.glass_ = true;
      }
      if (checkedValues.indexOf("alminum_") == "-1") {
        this.updateData.alminum_ = false;
      } else {
        this.updateData.alminum_ = true;
      }
      if (checkedValues.indexOf("cuminum_") == "-1") {
        this.updateData.cuminum_ = false;
      } else {
        this.updateData.cuminum_ = true;
      }
      if (checkedValues.indexOf("flexible_") == "-1") {
        this.updateData.flexible_ = false;
      } else {
        this.updateData.flexible_ = true;
      }
      if (checkedValues.indexOf("hard_") == "-1") {
        this.updateData.hard_ = false;
      } else {
        this.updateData.hard_ = true;
      }
      if (checkedValues.indexOf("ceramic_") == "-1") {
        this.updateData.ceramic_ = false;
      } else {
        this.updateData.ceramic_ = true;
      }
      if (checkedValues.indexOf("high_") == "-1") {
        this.updateData.high_ = false;
      } else {
        this.updateData.high_ = true;
      }
    },
    onChange1(checkedValues) {
      if (checkedValues.indexOf("allowIssuing") == "-1") {
        this.updateData.allowIssuing = false;
      } else {
        this.updateData.allowIssuing = true;
      }
      if (checkedValues.indexOf("importIssuing") == "-1") {
        this.updateData.importIssuing = false;
      } else {
        this.updateData.importIssuing = true;
      }
    },
    //保存基本信息
    editDataInfo(type) {
      Object.assign(this.basicData, this.updateData);
      this.basicData.ink_ = this.ink_List.toString();
      this.basicData.sheet_ = this.sheet_List.toString();
      this.basicData.category = this.category_List.toString();
      this.basicData.grade = this.grade_List.toString();
      this.basicData.orderType = this.orderType_List.toString();
      if (this.chooseData.length > 0) {
        this.basicData.province_ = this.chooseData[0];
        this.basicData.city_ = this.chooseData[1];
        this.basicData.area_ = this.chooseData[2];
      }
      if (this.basicData.name_ == "" || this.basicData.name_ == null) {
        this.$message.info("请输入公司名称");
      } else if (this.basicData.factoryCode_ == "" || this.basicData.factoryCode_ == null) {
        this.$message.info("请输入工厂编号");
      } else if (this.basicData.legalPerson_ == "" || this.basicData.legalPerson_ == null) {
        console.log(this.basicData.legalPerson_, "this.basicData.legalPerson_");
        this.$message.info("请输入法人代表");
      } else if (this.basicData.factoryCode_ == "" || this.basicData.factoryCode_ == null) {
        this.$message.info("请输入工厂编码");
      } else if (this.basicData.nature_ == "" || this.basicData.nature_ == null) {
        this.$message.info("请输入公司性质");
      } else if (this.basicData.address_ == "" || this.basicData.address_ == null) {
        this.$message.info("请输入公司地址");
      } else if (this.basicData.cooperation_ == "" || this.basicData.cooperation_ == null) {
        this.$message.info("请选择合作阶段");
      } else {
        this.spinning = true;
        var that = this;
        this.basicData.logoAddress = this.path;
        editData(this.basicData)
          .then(res => {
            if (res.code == "1") {
              that.spinning = false;
              that.$emit("ok");
              progressNum(this.$route.query.id).then(ite => {});
            }
            if (type != "制程") {
              that.$message.info(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-select-selection--multiple .ant-select-selection__choice {
  padding: 0 14px 0 0;
}
/deep/.ant-upload.ant-upload-select-picture-card > .ant-upload {
  padding: 2px !important;
}
/deep/.ant-upload-list-picture-card-container {
  height: 26px;
}
/deep/.ant-upload-picture-card-wrapper {
  position: relative;
}
/deep/.ant-upload-select-picture-card {
  position: absolute;
  top: 10%;
}
/deep/.ant-upload {
  height: 37px;
  margin-bottom: -10px;
  margin-top: -18px;
}
/deep/.ant-upload-list-item {
  height: 37px;
  margin-top: -3px;
  padding: 4px;
}
// /deep/.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled){
//     background-color: #C1C1C1!important;
// }
// /deep/ .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover{
//     background-color: #C1C1C1!important;
// }
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}
/deep/ .ant-descriptions-row:first-child th,
/deep/ .ant-descriptions-row:nth-child(2) th {
  color: red !important;
}
/deep/ .ant-descriptions-row th {
  width: 160px !important;
  font-weight: 500;
}

/deep/ .ant-descriptions-row:nth-child(2) th:last-of-type {
  color: rgba(0, 0, 0, 0.85) !important;
}
input.ant-input {
  font-weight: 500;
}
/deep/.ant-calendar-picker-input.ant-input {
  font-weight: 500;
}
/deep/.ant-descriptions-bordered .ant-descriptions-item-label {
  padding: 7px 10px;
  height: 48px;
}
/deep/.ant-descriptions-bordered .ant-descriptions-item-content {
  padding: 7px 10px;
  width: 200px;
}
</style>
