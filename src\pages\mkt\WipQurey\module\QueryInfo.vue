<!-- 市场管理 - WIP查询- 查询-->
<template>
    <a-form-model :modal="form">
      <!-- <a-form-model-item label="预审编号" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}">
          <a-input v-model="form.OrderNo"  :autoFocus='autoFocus'  allowClear/>
        </a-form-model-item> -->
        <a-form-model-item label="生产编号" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}">
          <a-input v-model="form.ProOrderNo"  :autoFocus='autoFocus' allowClear/>
        </a-form-model-item> 
        <a-form-model-item label="客户代码" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}">
          <a-input v-model="form.CustNo"   allowClear/>
        </a-form-model-item>
        <a-form-model-item label="客户型号：" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}">
          <a-input v-model="form.PcbFileName"  allowClear/>
        </a-form-model-item>  
        <a-form-model-item label="订单状态" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}">
        <a-select  v-model="form.Orderstatus" allowClear>
          <a-select-option value="1">已下线</a-select-option>
        </a-select>
      </a-form-model-item> 
       </a-form-model>
  </template>
  
  <script>
  export default {
      name:'QueryInfo',
      
    data() {
      return {      
        autoFocus:true,
        form:{
          OrderNo:'', 
          PcbFileName: '',  
          ProOrderNo:'',
          CustNo:'',
          Orderstatus:'',
        }
      };
    },
    methods: {  
    
    },
  };
  </script>
  <style lang="less" scoped>
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
  /deep/.ant-calendar-picker{
    width: 220px;
  }
  </style>