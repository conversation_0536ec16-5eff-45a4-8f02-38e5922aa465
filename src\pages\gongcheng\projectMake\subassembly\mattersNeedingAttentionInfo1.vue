<!-- 工程管理 - 工程制作-注意事项 -->
<template>
  <a-form  >
 
    <a-form-item >
        <a-upload
        accept=".image/jpeg,image/png,image/gif,image/bmp,image/jpg"
        :file-list="fileList"
        list-type="picture-card"
        class='pictureListSty'
        @change="handleChange"
        :before-upload='beforeUpload'
        :customRequest="downloadFilesCustomRequest"
      >
        <a-button  v-if="fileList.length < 4" style="font-weight:500">
        上传图片
        </a-button>
      </a-upload>
      </a-form-item>
    <a-form-item >
      <a-upload
          accept=".rar,.zip"
          :file-list="fileData"
          @change="handleChange1"
          :before-upload='beforeUpload1'
          :customRequest="downloadFilesCustomRequest1"
      >
        <a-button  v-if="fileData.length < 1"  style="font-weight:500">
          上传附件
        </a-button>
      </a-upload>
    </a-form-item>
    <a-form-item label="注意事项" :label-col="{ span:3}" :wrapper-col="{ span: 18}"><br>
      <a-textarea   v-model='form.conent' :autoFocus="autoFocus" :rows="6" />
    </a-form-item>
  </a-form>
</template>

<script>
import {UploadFile,} from  "@/services/projectMake";
export default {
   name:'mattersNeedingAttentionInfo',
  data() {
    return {
      fileList:[],
      form:{
      "conent": "",
      "picUrl": "",
      "fileUrl":'',
      "isbefor": true
      },
      autoFocus:true,
      previewVisible:false,
      previewImage:'',
      fileData:[],

    };
  },
  methods: {
    handleChange({ fileList },data) {
      this.fileList = fileList;
      console.log('this.fileList:',this.fileList)
    },
    handleChange1({ fileList },data) {
      this.fileData = fileList;
      // console.log('this.fileData:',this.fileData)
    },
    // 上传图片路径
    downloadFilesCustomRequest(data){
      const formData = new FormData()
      formData.append('file', data.file)
      UploadFile(formData).then(res =>{
        if (res.code == 1) {
          data.onSuccess(res.data);
         this.form.picUrl = this.fileList.map(item => {return item.response}).join(',')
        }
         else {
          this.$message.error(res.message)
        }
        console.log( 'this.form.picUrl:',this.form.picUrl)

      })

    },
    // 限制上传图片格式
    beforeUpload(file){
      const _this = this
      return new Promise(function(resolve, reject) {
        const isJpgOrPng = file.type.toLowerCase() === 'image/jpeg' || file.type.toLowerCase() === 'image/png' || file.type.toLowerCase() === 'image/gif' || file.type.toLowerCase() === 'image/bmp' || file.type.toLowerCase() === 'image/jpg';
        if (!isJpgOrPng) {
          _this.$message.error('图片只支持|*.jpg;*.png;*.gif;*.jpeg;*.bmp 格式');
          reject()
        } else {
          resolve()
        }
      })
    },
    // 限制上传附件格式
    beforeUpload1(file){
      const _this = this
      return new Promise(function(resolve, reject) {
        const isJpgOrPng = file.name.indexOf('.rar') != -1 || file.name.indexOf('.zip') != -1
        if (!isJpgOrPng) {
          _this.$message.error('文件只支持.rar或.zip格式');
          reject()
        } else {
          resolve()
        }
      })
    },
    // 上传附件路径
    downloadFilesCustomRequest1(data){
      const formData = new FormData()
      formData.append('file', data.file)
      UploadFile(formData).then(res =>{
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.form.fileUrl = this.fileData.map(item => {return item.response}).join(',')
        }
        else {
          this.$message.error(res.message)
        }
        console.log( 'this.form.fileUrl:',this.form.fileUrl)
      })
    },
  },
};
</script>
<style scoped lang='less'>
/deep/.ant-input{
  margin-left: -65px;
  font-weight: 500;
}
.pictureListSty{
 /deep/ .ant-upload-list-item-actions{
    a{
      display: none;
    }
  }
}
</style>
