<template>
  <div ref="SelectBox">
    <a-form >
      <a-row>
        <a-col :span='24'>
        <a-form-item label="生产编号" :labelCol="{span: 7}" :wrapperCol="{span:14}" >
          <a-input   v-model='form.Pdctno' placeholder="请输入生产编号" autoFocus ref="input1"/>
        </a-form-item>      
        </a-col>
        <a-col :span='24'>
        <a-form-item label="客户型号" :labelCol="{span: 7}" :wrapperCol="{span:14}" >
          <a-input   v-model='form.pcbFileName' placeholder="请输入客户型号" />
        </a-form-item>      
        </a-col>
        <a-col :span='24'>
          <a-form-item label="订单状态" :labelCol="{span: 7}" :wrapperCol="{span:14}">
            <a-select
                ref="select"
                v-model="form.States" 
                showSearch allowClear    
                optionFilterProp="lable"   
                :getPopupContainer="()=>this.$refs.SelectBox"
              >
              <a-select-option v-for="(ite,index) in list" :key="index" :value="ite.value" :lable="ite.text">{{ite.text}}</a-select-option>
              </a-select>
          </a-form-item>
        </a-col>
      </a-row>
        <!-- <a-row>
          <a-col :span="6">
            <a-form-model-item label="铝基板：" :labelCol="{span: 22}" :wrapperCol="{span:2}" >
              <a-checkbox  @change="onChange" name="strLjb_" :disabled="disabled" />
            </a-form-model-item>
          </a-col>

          <a-col :span="8">
            <a-form-model-item label="其他单面板" :labelCol="{span: 22}" :wrapperCol="{span: 2}">
              <a-checkbox  @change="onChange" name="strNoLjb_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>

          <a-col :span="8">
            <a-form-model-item label="≥2㎡非铝基板单面批量" :labelCol="{span: 22}" :wrapperCol="{span:2}">
              <a-checkbox  @change="onChange" name="strNoLjb2_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span='6'>
            <a-form-model-item label="四层板样板" :labelCol="{span: 22}" :wrapperCol="{span: 2}">
              <a-checkbox  @change="onChange" name="Layer_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>

          <a-col :span='8'>
            <a-form-model-item label="六层含以上样板" :labelCol="{span: 22}" :wrapperCol="{span: 2}">
              <a-checkbox  @change="onChange" name="Layer2_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>

          <a-col :span='8'>
            <a-form-model-item label="多层板批量" :labelCol="{span: 22}" :wrapperCol="{span:2}">
              <a-checkbox  @change="onChange" name="Layer3_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span='6'>
            <a-form-model-item label="≥2㎡双面板批量" :labelCol="{span: 22}" :wrapperCol="{span:2}">
              <a-checkbox  @change="onChange" name="Yp3_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>

          <a-col :span='8'>
            <a-form-model-item label="24小时双面板样品" :labelCol="{span: 22}" :wrapperCol="{span:2}">
              <a-checkbox  @change="onChange" name="Yp_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>

          <a-col :span='8'>
            <a-form-model-item label="非24小时双面板样品" :labelCol="{span: 22}" :wrapperCol="{span: 2}">
              <a-checkbox  @change="onChange" name="Yp2_" :disabled="disabled"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
            <a-col :md="9" :sm="24">
                <a-form-model-item label="订单渠道" prop="tradeTypeSrc" :label-col="{ span: 8}" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
                      <a-select v-model="form.tradeTypeSrc"  placeholder="请选择" showSearch optionFilterProp="label">
                          <a-select-option  v-for=" item in tradeTypeSrcList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                          {{ item.text }}
                        </a-select-option>
                      </a-select>
                  </a-form-model-item>
              </a-col>
        </a-row> -->
      
    </a-form>
  </div>
  <!-- <a-modal
      
      class="searchModel"
  > -->

  <!-- </a-modal> -->
</template>

<script>
import {
  getTradeTypeSrcList
} from "@/services/projectDisptch";
export default {
  name: "SearchModal",
  props: {
   
  },
  computed:{
    disabled(){
      if (this.form.Pdctno == '') {
        return false
      } else {
        return true
      }
    }
  },
  data() {
    return {
      // disabled: false,
      form:{
        Pdctno:'',  // 订单编号
        pcbFileName:'',//客户型号
        States:'',
        strLjb_: '',  // 是否铝基板
        strNoLjb_: '',    // 其他单面板
        strNoLjb2_: '',     // 大于1㎡非铝基板单面批量
        Layer_: '',     // 四层板样板
        Yp3_: '', // ≥1㎡双面板批量
        Yp2_: '', // 非24小时双面板样品
        Layer3_: '',     // 多层板批量
        Yp_: '',    // 24小时双面板样品
        Layer2_: '',    // 六层含以上样板
        tradeTypeSrc:'' //订单渠道
      },
      tradeTypeSrcList:[],
      list:[
        {value:'',text:'请选择'},
        {value:'0',text:'待分派'},
        {value:'10',text:'待制作'},
        {value:'15',text:'EQ待审'},
        {value:'20',text:'制作中'},
        {value:'21',text:'后端待派'},
        {value:'22',text:'后端制作中'},
        {value:'23',text:'返修中'},
        {value:'24',text:'已问客'},
        {value:'25',text:'返修完'},
        {value:'26',text:'待审核'},
        {value:'27',text:'审核中'},
        {value:'30',text:'已回传'},
        {value:'40',text:'已退单'},
        {value:'80',text:'暂停'},
        {value:'90',text:'取消'},
       ]
    }
  },
  created() {
    getTradeTypeSrcList().then(res => {
      this.tradeTypeSrcList = res?.data
    });
  },
  methods: {
    
    onChange(e){
      this.form[e.target.name] = e.target.checked
    }
  },
  mounted() {
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-form-item-label > label{
  color:#000000;
}
/deep/.ant-input{
  color:#000000;
  font-weight: 500;
}
.searchModel{
  form {
    overflow: hidden;
  }
}
</style>