// pipeline {
//     agent {
//         docker {
//             image 'node/emsweb:1.0.1'
//             args '--rm --name emsweb -p 4200:4200'
//         }
//     }
//     environment { 
//         CI = 'true'
//     }
//     stages {
//         stage('install') {
//             steps {
//                 sh "cp package*.json /app/"  //将package.json文件复制到node_modules目录下
//                 sh 'npm install --prefix /app'   //设置安装包所在路径
//             }
//         }
//         stage('build') {
//             steps {
//                 sh 'npm run build'
//             }
//         }
//         stage('run') { 
//             steps {
//                 sh 'nohup npm run serve &' 
//                 input message: 'Finished using the web site? (Click "Proceed" to continue)' 
//             }
//         }
//     }
// }

pipeline {
    agent any

    stages {
        stage('install') {
            agent {
                label 'master'
            }
            steps {
                sh 'npm install'
            }
        }

        stage('build') {
            agent {
                label 'master'
            }
            steps {
                sh 'npm install'
                sh 'npm run build'
            }
        }

        stage('run') {
            agent {
                docker {
                    image 'node/emsweb:1.0.1'
                    args '--rm --name emsweb -p 4200:4200'
                }
            }
            steps {
                sh 'npm install --production'
                sh 'npm start'
            }
        }
    }
}