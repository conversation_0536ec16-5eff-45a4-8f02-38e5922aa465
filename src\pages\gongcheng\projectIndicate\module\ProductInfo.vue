<template>
  <a-spin :spinning="spinning">
    <div class="box" ref="SelectBox" style="height: 771px">
      <a-form-model layout="inline" v-show="!editFlg1" style="border-top: 1px solid #ddd" id="formDataElem1">
        <div
          ref="div1"
          class="div1"
          style="
            width: 80%;
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            justify-content: start;
            max-height: 699px;
            align-content: flex-start;
          "
        >
          <a-form-model-item
            label="本厂编号"
            :title="proOrderInfoDto.orderNo"
            v-show="proOrderInfoDto.orderNo"
            :class="proOrderInfoDto.orderNo ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.orderNo">{{ proOrderInfoDto.orderNo }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户型号"
            :title="proOrderInfoDto.pcbFileName"
            v-show="proOrderInfoDto.pcbFileName"
            :class="proOrderInfoDto.pcbFileName ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.pcbFileName" class="tmp1" @click="dwon1(proOrderInfoDto.pcbFilePath)">{{
              proOrderInfoDto.pcbFileName
            }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板类型"
            :title="proOrderInfoDto.plateTypeStr"
            v-show="proOrderInfoDto.plateTypeStr"
            :class="proOrderInfoDto.plateTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.plateTypeStr">{{ proOrderInfoDto.plateTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户物料号"
            :title="proOrderInfoDto.customerMaterialNo"
            v-show="proOrderInfoDto.customerMaterialNo"
            :class="proOrderInfoDto.customerMaterialNo ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.customerMaterialNo">{{ proOrderInfoDto.customerMaterialNo }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品板厚mm"
            :title="proOrderInfoDto.boardThickness"
            v-show="proOrderInfoDto.boardThickness"
            :class="proOrderInfoDto.boardThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.boardThickness">{{ proOrderInfoDto.boardThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板厚公差"
            :title="proOrderInfoDto.boardThicknessTol"
            v-show="proOrderInfoDto.boardThicknessTol"
            :class="proOrderInfoDto.boardThicknessTol ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span style="display: inline-block; width: 100%; overflow: hidden">{{ proOrderInfoDto.boardThicknessTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="层数"
            :title="proOrderInfoDto.boardLayers"
            v-show="proOrderInfoDto.boardLayers"
            :class="proOrderInfoDto.boardLayers ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.boardLayers">{{ proOrderInfoDto.boardLayers }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品铜厚oz"
            v-if="!proOrderInfoDto.isCopperThickConversion"
            :title="
              Number(proOrderInfoDto.boardLayers) > 2
                ? '[内]' + proOrderInfoDto.innerCopperThickness + '[外]' + proOrderInfoDto.copperThickness
                : '[外]' + proOrderInfoDto.copperThickness
            "
            v-show="proOrderInfoDto.copperThickness"
            :class="proOrderInfoDto.copperThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>
              <span v-if="Number(proOrderInfoDto.boardLayers) > 2">
                <b v-if="proOrderInfoDto.innerCopperThickness" style="margin-right: 0.5%; margin-left: 0.5%">[内]</b
                >{{ proOrderInfoDto.innerCopperThickness }}
              </span>
              <b v-if="proOrderInfoDto.copperThickness" style="margin-right: 0.5%; margin-left: 0.5%">[外]</b>
              {{ proOrderInfoDto.copperThickness }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="成品铜厚um"
            v-if="proOrderInfoDto.isCopperThickConversion"
            :title="
              Number(proOrderInfoDto.boardLayers) > 2
                ? '[内]' + proOrderInfoDto.innerCopperThickness2 + '[外]' + proOrderInfoDto.copperThickness2
                : '[外]' + proOrderInfoDto.copperThickness2
            "
            v-show="proOrderInfoDto.copperThickness"
            :class="proOrderInfoDto.copperThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>
              <span v-if="Number(proOrderInfoDto.boardLayers) > 2">
                <b v-if="proOrderInfoDto.innerCopperThickness2" style="margin-right: 0.5%; margin-left: 0.5%">[内]</b
                >{{ proOrderInfoDto.innerCopperThickness2 }}
              </span>
              <b v-if="proOrderInfoDto.copperThickness2" style="margin-right: 0.5%; margin-left: 0.5%">[外]</b>
              {{ proOrderInfoDto.copperThickness2 }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊颜色"
            :title="
              proOrderInfoDto.solderColorStr && proOrderInfoDto.solderColorBottomStr
                ? '[顶]' + proOrderInfoDto.solderColorStr + '[底]' + proOrderInfoDto.solderColorBottomStr
                : proOrderInfoDto.solderColorStr
                ? '[顶]' + proOrderInfoDto.solderColorStr
                : '[底]' + proOrderInfoDto.solderColorBottomStr
            "
            v-show="proOrderInfoDto.solderColorStr"
            :class="proOrderInfoDto.solderColorStr || proOrderInfoDto.solderColorBottomStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span
              ><b style="font-family: PingFangSC-Regular, Sans-serif; font-size: 14px; color: #000000"> [顶] </b>
              {{ proOrderInfoDto.solderColorStr }} <b> [底] </b>
              {{ proOrderInfoDto.solderColorBottomStr }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊特性"
            :title="
              proOrderInfoDto.fontColorStr && proOrderInfoDto.fontColorBottomStr
                ? '[顶]' + proOrderInfoDto.fontColorStr + '[底]' + proOrderInfoDto.fontColorBottomStr
                : proOrderInfoDto.fontColorStr
                ? '[顶]' + proOrderInfoDto.fontColorStr
                : '[底]' + proOrderInfoDto.fontColorBottomStr
            "
            v-show="proOrderInfoDto.solderCharacteristicsStr"
            :class="proOrderInfoDto.solderCharacteristicsStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            {{ proOrderInfoDto.solderCharacteristicsStr }}
          </a-form-model-item>
          <a-form-model-item
            label="阻焊油墨"
            :title="proOrderInfoDto.isInkNotHalogen ? proOrderInfoDto.solderResistInkStr + '无卤油墨:是' : proOrderInfoDto.solderResistInkStr"
            v-show="proOrderInfoDto.solderResistInkStr"
            :class="proOrderInfoDto.solderResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-show="proOrderInfoDto.solderResistInkStr"
              >{{ proOrderInfoDto.solderResistInkStr }} <b style="padding-left: 50px">无卤油墨 :</b>{{ proOrderInfoDto.isInkNotHalogen ? "是" : "" }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="无卤油墨"
            :title="proOrderInfoDto.isInkNotHalogen"
            v-show="proOrderInfoDto.isInkNotHalogen && !proOrderInfoDto.solderResistInkStr"
            :class="proOrderInfoDto.isInkNotHalogen && !proOrderInfoDto.solderResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isInkNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符颜色"
            :title="
              proOrderInfoDto.fontColorStr && proOrderInfoDto.fontColorBottomStr
                ? '[顶]' + proOrderInfoDto.fontColorStr + '[底]' + proOrderInfoDto.fontColorBottomStr
                : proOrderInfoDto.fontColorStr
                ? '[顶]' + proOrderInfoDto.fontColorStr
                : '[底]' + proOrderInfoDto.fontColorBottomStr
            "
            v-show="proOrderInfoDto.fontColorStr || proOrderInfoDto.fontColorBottomStr"
            :class="proOrderInfoDto.fontColorStr || proOrderInfoDto.fontColorBottomStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span><b> [顶] </b> {{ proOrderInfoDto.fontColorStr }} <b> [底] </b> {{ proOrderInfoDto.fontColorBottomStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符特性"
            :title="
              proOrderInfoDto.fontColorStr && proOrderInfoDto.fontColorBottomStr
                ? '[顶]' + proOrderInfoDto.fontColorStr + '[底]' + proOrderInfoDto.fontColorBottomStr
                : proOrderInfoDto.fontColorStr
                ? '[顶]' + proOrderInfoDto.fontColorStr
                : '[底]' + proOrderInfoDto.fontColorBottomStr
            "
            v-show="proOrderInfoDto.characterInkCharacteristicsStr"
            :class="proOrderInfoDto.characterInkCharacteristicsStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            {{ proOrderInfoDto.characterInkCharacteristicsStr }}
          </a-form-model-item>

          <a-form-model-item
            label="字符油墨"
            :title="
              proOrderInfoDto.characterNotHalogen ? proOrderInfoDto.characterResistInkStr + '字符无卤:是' : proOrderInfoDto.characterResistInkStr
            "
            v-show="proOrderInfoDto.characterResistInkStr"
            :class="proOrderInfoDto.characterResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span
              >{{ proOrderInfoDto.characterResistInkStr }} <b style="padding-left: 27px" v-show="proOrderInfoDto.characterNotHalogen">字符无卤:</b
              >{{ proOrderInfoDto.characterNotHalogen ? "是" : "" }}</span
            >
          </a-form-model-item>
          <a-form-model-item
            label="字符无卤"
            :title="proOrderInfoDto.characterNotHalogen"
            v-show="proOrderInfoDto.characterNotHalogen && !proOrderInfoDto.characterResistInkStr"
            :class="proOrderInfoDto.characterNotHalogen && !proOrderInfoDto.characterResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.characterNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="表面处理"
            v-show="proOrderInfoDto.surfaceFinishStr"
            :class="proOrderInfoDto.surfaceFinishStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>
              <span style="display: inline-block"> {{ proOrderInfoDto.surfaceFinishStr }}; </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj') &&
                  proOrderInfoDto.surfaceFinishJsonDto.platedArea
                "
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin') &&
                  proOrderInfoDto.surfaceFinishJsonDto.platedArea
                "
                >镀金面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'hardgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold') &&
                  proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness
                "
                >镀金厚 :{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                >水金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'eletrolyticnickel' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'waterhardgold' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj') &&
                  proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness
                "
                >镍: {{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                >软金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj') &&
                  proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness
                "
                >金 :{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }} ;</span
              >
              <span
                v-if="
                  proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                >薄金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }} ;</span
              >
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea2 }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2
                "
                >金: {{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                v-if="
                  proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                >厚金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }} ;</span
              >
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                :class="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ? 'sss' : ''"
                >化金厚 :{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'haslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'spraytin' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'tinplating' ||
                    proOrderInfoDto.surfaceFinish == 'tinnedcerium' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'yqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandGoldfinger') &&
                  proOrderInfoDto.surfaceFinishJsonDto.newTinThickness
                "
                :class="
                  proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                  proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                  proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                  proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin'
                    ? 'sss'
                    : ''
                "
                >锡:{{ proOrderInfoDto.surfaceFinishJsonDto.newTinThickness }}um;
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'tinprecipitation' && proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2"
                >锡:{{ proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 }}um;
              </span>
              <span
                style="width: 29%; display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'osp' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandGoldfinger') &&
                  proOrderInfoDto.surfaceFinishJsonDto.filmThickness
                "
                :class="
                  proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                  proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                  proOrderInfoDto.surfaceFinish == 'goldplatingandosp'
                    ? 'sss'
                    : ''
                "
                >膜厚:{{ proOrderInfoDto.surfaceFinishJsonDto.filmThickness }}um;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'chemicalsilver' ||
                    proOrderInfoDto.surfaceFinish == 'sinksilverandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'silverplating' ||
                    proOrderInfoDto.surfaceFinish == 'outsourcingsilverplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness
                "
                >银厚: :{{ proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness }}um;
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' && proOrderInfoDto.surfaceFinishJsonDto.newTinThickness"
                >无铅锡厚:{{ proOrderInfoDto.surfaceFinishJsonDto.newTinThickness }}
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' && proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness"
              >
                镀镍厚:
                {{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }}
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' && proOrderInfoDto.surfaceFinishJsonDto.platedArea"
              >
                镍面积:{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' ||
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'hardgoldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2
                "
                >镍厚:{{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }}
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
              >
                金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold') &&
                  proOrderInfoDto.surfaceFinishJsonDto.platedArea2
                "
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea2 }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.platedArea"
              >
                化金面积:
                {{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
              >
                化金镍厚:{{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
              >
                化金厚:
                {{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' && proOrderInfoDto.surfaceFinishJsonDto.paThickness"
              >
                钯:{{ proOrderInfoDto.surfaceFinishJsonDto.paThickness }}{{ proOrderInfoDto.surfaceFinishThickUnit }}
              </span>
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="拼版方式"
            :title="proOrderInfoDto.pinBanType"
            v-show="proOrderInfoDto.pinBanType"
            :class="proOrderInfoDto.pinBanType ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.pinBanType }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="SU数"
            :title="proOrderInfoDto.su"
            v-show="proOrderInfoDto.su"
            :class="proOrderInfoDto.su ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.su }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="单元长mm"
            :title="proOrderInfoDto.boardHeight"
            v-show="proOrderInfoDto.boardHeight"
            :class="proOrderInfoDto.boardHeight ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardHeight }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="单元宽mm"
            :title="proOrderInfoDto.boardWidth"
            v-show="proOrderInfoDto.boardWidth"
            :class="proOrderInfoDto.boardWidth ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品长mm"
            :title="proOrderInfoDto.boardHeightSet"
            v-show="proOrderInfoDto.boardHeightSet"
            :class="proOrderInfoDto.boardHeightSet ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardHeightSet }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品宽mm"
            :title="proOrderInfoDto.boardWidthSet"
            v-show="proOrderInfoDto.boardWidthSet"
            :class="proOrderInfoDto.boardWidthSet ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardWidthSet }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成型方式"
            :title="proOrderInfoDto.formingTypeStr"
            v-show="proOrderInfoDto.formingTypeStr"
            :class="proOrderInfoDto.formingTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.formingTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成型公差"
            :title="proOrderInfoDto.formingTolStr"
            v-show="proOrderInfoDto.formingTolStr"
            :class="proOrderInfoDto.formingTolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.formingTolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="合拼款数"
            :title="proOrderInfoDto.pinBanNum"
            v-show="proOrderInfoDto.pinBanNum"
            :class="proOrderInfoDto.pinBanNum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.pinBanNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="出货单位"
            :title="proOrderInfoDto.boardTypeStr"
            v-show="proOrderInfoDto.boardTypeStr"
            :class="proOrderInfoDto.boardTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材商"
            :title="proOrderInfoDto.sheetTraderStr"
            v-show="proOrderInfoDto.sheetTraderStr"
            :class="proOrderInfoDto.sheetTraderStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.sheetTraderStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客供板材"
            :title="proOrderInfoDto.isCustomerBoard"
            v-show="proOrderInfoDto.isCustomerBoard"
            :class="proOrderInfoDto.isCustomerBoard ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isCustomerBoard ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材类型"
            :title="proOrderInfoDto.fR4TypeStr"
            v-show="proOrderInfoDto.fR4TypeStr"
            :class="proOrderInfoDto.fR4TypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.fR4TypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="耐CAF"
            :title="proOrderInfoDto.cafResistance ? '是' : ''"
            v-show="proOrderInfoDto.cafResistance"
            :class="proOrderInfoDto.cafResistance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.cafResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材型号"
            :title="proOrderInfoDto.boardBrandStr"
            v-show="proOrderInfoDto.boardBrandStr"
            :class="proOrderInfoDto.boardBrandStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardBrandStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材CTI"
            :title="proOrderInfoDto.plateCtiStr"
            v-show="proOrderInfoDto.plateCtiStr"
            :class="proOrderInfoDto.plateCtiStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plateCtiStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材TG"
            :title="proOrderInfoDto.fR4TgStr"
            v-show="proOrderInfoDto.fR4TgStr"
            :class="proOrderInfoDto.fR4TgStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.fR4TgStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="无卤板材"
            :title="proOrderInfoDto.isNotHalogen ? '是' : ''"
            v-show="proOrderInfoDto.isNotHalogen"
            :class="proOrderInfoDto.isNotHalogen ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="大料尺寸"
            :title="proOrderInfoDto.sheetSize"
            v-show="proOrderInfoDto.sheetSize"
            :class="proOrderInfoDto.sheetSize ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.sheetSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材利用率(%)"
            :title="proOrderInfoDto.sheetUtilization"
            v-show="proOrderInfoDto.sheetUtilization"
            :class="proOrderInfoDto.sheetUtilization ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.sheetUtilization }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="层压不可更改"
            :title="proOrderInfoDto.isChangeLayerPres ? '是' : '否'"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            class="divitem"
          >
            <span>{{ proOrderInfoDto.isChangeLayerPres ? "是" : "否" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不接受打叉"
            :title="proOrderInfoDto.acceptCrossed ? '是' : ''"
            v-show="proOrderInfoDto.acceptCrossed"
            :class="proOrderInfoDto.acceptCrossed ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.acceptCrossed ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许补线"
            :title="proOrderInfoDto.notAcceptPatching ? '是' : ''"
            v-show="proOrderInfoDto.notAcceptPatching"
            :class="proOrderInfoDto.notAcceptPatching ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.notAcceptPatching ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="测试方式"
            :title="proOrderInfoDto.flyingProbeStr"
            v-show="proOrderInfoDto.flyingProbeStr"
            :class="proOrderInfoDto.flyingProbeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.flyingProbeStr }}</span>
          </a-form-model-item>
          <!-- <a-form-model-item label="盖ET章" :title="proOrderInfoDto.stampEt ? '是':''" v-show="proOrderInfoDto.stampEt" :class="proOrderInfoDto.stampEt?'divitem':''" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" >
          <span >{{proOrderInfoDto.stampEt?'是':''}}</span>                      
        </a-form-model-item> -->
          <a-form-model-item
            label="盖ET章"
            :title="proOrderInfoDto.stampEtPositionStr"
            v-show="proOrderInfoDto.stampEtPositionStr"
            :class="proOrderInfoDto.stampEtPositionStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.stampEtPositionStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="飞针测试方式"
            :title="proOrderInfoDto.fpTestMethodStr"
            v-show="proOrderInfoDto.fpTestMethodStr"
            :class="proOrderInfoDto.fpTestMethodStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.fpTestMethodStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="测试治具编号"
            :title="proOrderInfoDto.testFixtureNumber"
            v-show="proOrderInfoDto.testFixtureNumber"
            :class="proOrderInfoDto.testFixtureNumber ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.testFixtureNumber }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="检验标准"
            :title="proOrderInfoDto.ipcLevelStr"
            v-show="proOrderInfoDto.ipcLevelStr"
            :class="proOrderInfoDto.ipcLevelStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.ipcLevelStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="翘曲度"
            :title="proOrderInfoDto.warpage"
            v-show="proOrderInfoDto.warpage"
            :class="proOrderInfoDto.warpage ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.warpage }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="产品用途"
            :title="proOrderInfoDto.productUsageStr"
            v-show="proOrderInfoDto.productUsageStr"
            :class="proOrderInfoDto.productUsageStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.productUsageStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="标记位置"
            :title="proOrderInfoDto.markPositionStr"
            v-show="proOrderInfoDto.markPositionStr"
            :class="proOrderInfoDto.markPositionStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.markPositionStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="UL类型"
            :title="proOrderInfoDto.ulTypeStr"
            v-show="proOrderInfoDto.ulTypeStr"
            :class="proOrderInfoDto.ulTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.ulTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="标记类型"
            :title="proOrderInfoDto.markTypeStr"
            v-show="proOrderInfoDto.markTypeStr"
            :class="proOrderInfoDto.markTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.markTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="标记面向"
            :title="proOrderInfoDto.markFaceStr"
            v-show="proOrderInfoDto.markFaceStr"
            :class="proOrderInfoDto.markFaceStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.markFaceStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="周期格式"
            :title="proOrderInfoDto.periodicFormatStr"
            v-show="proOrderInfoDto.periodicFormatStr"
            :class="proOrderInfoDto.periodicFormatStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.periodicFormatStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户指定"
            :title="proOrderInfoDto.custAssignment ? '是' : ''"
            v-show="proOrderInfoDto.custAssignment"
            :class="proOrderInfoDto.custAssignment ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.custAssignment ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="低阻测试"
            :title="proOrderInfoDto.isLowResistanceTest ? '是' : ''"
            v-show="proOrderInfoDto.isLowResistanceTest"
            :class="proOrderInfoDto.isLowResistanceTest ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isLowResistanceTest ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="确认工作稿"
            :title="proOrderInfoDto.confirmWorkingDraft ? '是' : ''"
            v-show="proOrderInfoDto.confirmWorkingDraft"
            :class="proOrderInfoDto.confirmWorkingDraft ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.confirmWorkingDraft ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="确认阻抗"
            :title="proOrderInfoDto.confirmImpedance ? '是' : ''"
            v-show="proOrderInfoDto.confirmImpedance"
            :class="proOrderInfoDto.confirmImpedance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.confirmImpedance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="确认叠层"
            :title="proOrderInfoDto.confirmStacking ? '是' : ''"
            v-show="proOrderInfoDto.confirmStacking"
            :class="proOrderInfoDto.confirmStacking ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.confirmStacking ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="V-CUT类型"
            :title="proOrderInfoDto.vCutStr"
            v-show="proOrderInfoDto.vCutStr"
            :class="proOrderInfoDto.vCutStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vCutStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="V-CUT刀数"
            :title="proOrderInfoDto.vCutKnifeNum"
            v-show="proOrderInfoDto.vCutKnifeNum"
            :class="proOrderInfoDto.vCutKnifeNum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vCutKnifeNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="角度"
            :title="proOrderInfoDto.vcutAngleStr"
            v-show="proOrderInfoDto.vcutAngleStr"
            :class="proOrderInfoDto.vcutAngleStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutAngleStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="角度公差"
            :title="proOrderInfoDto.vcutAngleTolStr"
            v-show="proOrderInfoDto.vcutAngleTolStr"
            :class="proOrderInfoDto.vcutAngleTolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutAngleTolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="余厚mm"
            :title="proOrderInfoDto.vcutSurplusThickness"
            v-show="proOrderInfoDto.vcutSurplusThickness"
            :class="proOrderInfoDto.vcutSurplusThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutSurplusThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="余厚公差mm"
            :title="proOrderInfoDto.vcutSurplusThicknessTol"
            v-show="proOrderInfoDto.vcutSurplusThicknessTol"
            :class="proOrderInfoDto.vcutSurplusThicknessTol ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutSurplusThicknessTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="余厚指定"
            :title="proOrderInfoDto.vcutSurplusThicknessZD"
            v-show="proOrderInfoDto.vcutSurplusThicknessZD"
            :class="proOrderInfoDto.vcutSurplusThicknessZD ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutSurplusThicknessZD }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="锣孔公差mm"
            :title="proOrderInfoDto.cncHoleTolStr"
            v-show="proOrderInfoDto.cncHoleTolStr"
            :class="proOrderInfoDto.cncHoleTolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.cncHoleTolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="锣带次数"
            :title="proOrderInfoDto.routNumber"
            v-show="proOrderInfoDto.routNumber"
            :class="proOrderInfoDto.routNumber ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.routNumber }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="过孔处理"
            :title="proOrderInfoDto.solderCoverStr"
            v-show="proOrderInfoDto.solderCoverStr"
            :class="proOrderInfoDto.solderCoverStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.solderCoverStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="最小阻焊桥mm"
            :title="proOrderInfoDto.minSolderBridge"
            v-show="proOrderInfoDto.minSolderBridge"
            :class="proOrderInfoDto.minSolderBridge ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.minSolderBridge }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="基材油墨厚度um"
            :title="proOrderInfoDto.solderThickness"
            v-show="proOrderInfoDto.solderThickness"
            :class="proOrderInfoDto.solderThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.solderThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="线角油墨厚度um"
            :title="proOrderInfoDto.footLineInkThickness"
            v-show="proOrderInfoDto.footLineInkThickness"
            :class="proOrderInfoDto.footLineInkThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.footLineInkThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="铜面油墨厚度um"
            :title="proOrderInfoDto.cuInkThickness"
            v-show="proOrderInfoDto.cuInkThickness"
            :class="proOrderInfoDto.cuInkThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.cuInkThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊厚度"
            :title="proOrderInfoDto.solderInkThickness"
            v-show="proOrderInfoDto.solderInkThickness"
            :class="proOrderInfoDto.solderInkThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.solderInkThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="塞孔程度"
            :title="proOrderInfoDto.plugOilExtentStr"
            v-show="proOrderInfoDto.plugOilExtentStr"
            :class="proOrderInfoDto.plugOilExtentStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plugOilExtentStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="塞孔工具"
            :title="proOrderInfoDto.plugOilToolStr"
            v-show="proOrderInfoDto.plugOilToolStr"
            :class="proOrderInfoDto.plugOilToolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plugOilToolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="塞孔面次"
            :title="proOrderInfoDto.plugOilSideStr"
            v-show="proOrderInfoDto.plugOilSideStr"
            :class="proOrderInfoDto.plugOilSideStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plugOilSideStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="是否大白油块"
            :title="proOrderInfoDto.isWhiteOilBlock ? '是' : ''"
            v-show="proOrderInfoDto.isWhiteOilBlock"
            :class="proOrderInfoDto.isWhiteOilBlock ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isWhiteOilBlock ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符网板印刷"
            :title="proOrderInfoDto.isCharacterScreenPrinting ? '是' : ''"
            v-show="proOrderInfoDto.isCharacterScreenPrinting"
            :class="proOrderInfoDto.isCharacterScreenPrinting ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isCharacterScreenPrinting ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符上表面"
            :title="proOrderInfoDto.isCharacterUpSurface ? '是' : ''"
            v-show="proOrderInfoDto.isCharacterUpSurface"
            :class="proOrderInfoDto.isCharacterUpSurface ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isCharacterUpSurface ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码方式"
            :title="proOrderInfoDto.qrCodeWay"
            v-show="proOrderInfoDto.qrCodeWay"
            :class="proOrderInfoDto.qrCodeWay ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeWay }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码类型"
            :title="proOrderInfoDto.qrCodeType"
            v-show="proOrderInfoDto.qrCodeType"
            :class="proOrderInfoDto.qrCodeType ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeType }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码要求"
            :title="proOrderInfoDto.qrCodeRequire"
            v-show="proOrderInfoDto.qrCodeRequire"
            :class="proOrderInfoDto.qrCodeRequire ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeRequire }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码阴阳"
            :title="proOrderInfoDto.qrCodeYY"
            v-show="proOrderInfoDto.qrCodeYY"
            :class="proOrderInfoDto.qrCodeYY ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeYY }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="干燥剂"
            :title="proOrderInfoDto.noAddDesiccantStr"
            v-show="proOrderInfoDto.noAddDesiccant"
            :class="proOrderInfoDto.noAddDesiccant ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.noAddDesiccantStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板间隔纸"
            :title="proOrderInfoDto.isBoardSpacerStr"
            v-show="proOrderInfoDto.isBoardSpacer"
            :class="proOrderInfoDto.isBoardSpacer ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isBoardSpacerStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="放湿度卡"
            :title="proOrderInfoDto.isHumidityCardStr"
            v-show="proOrderInfoDto.isHumidityCard"
            :class="proOrderInfoDto.isHumidityCard ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isHumidityCardStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="包装要求"
            :title="proOrderInfoDto.packagRequireStr"
            v-show="proOrderInfoDto.packagRequire"
            :class="proOrderInfoDto.packagRequire ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.packagRequireStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="交货方式"
            :title="proOrderInfoDto.deliveryMethod"
            v-show="proOrderInfoDto.deliveryMethod"
            :class="proOrderInfoDto.deliveryMethod ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.deliveryMethod }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="包装数量pcs"
            :title="proOrderInfoDto.packagingQuantity"
            v-show="proOrderInfoDto.packagingQuantity"
            :class="proOrderInfoDto.packagingQuantity ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.packagingQuantity }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="备品数量pcs"
            :title="proOrderInfoDto.spareQuantity"
            v-show="proOrderInfoDto.spareQuantity"
            :class="proOrderInfoDto.spareQuantity ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.spareQuantity }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="附出货菲林"
            :title="proOrderInfoDto.attachedShippingFilm"
            v-show="proOrderInfoDto.attachedShippingFilm"
            :class="proOrderInfoDto.attachedShippingFilm ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.attachedShippingFilm ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="盘中孔"
            :title="proOrderInfoDto.isDiscHole ? '是' : ''"
            v-show="proOrderInfoDto.isDiscHole"
            :class="proOrderInfoDto.isDiscHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isDiscHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="半孔"
            :title="proOrderInfoDto.isHalfHole ? '是' : ''"
            v-show="proOrderInfoDto.isHalfHole"
            :class="proOrderInfoDto.isHalfHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isHalfHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="异形孔"
            :title="proOrderInfoDto.isProfileHole ? '是' : ''"
            v-show="proOrderInfoDto.isProfileHole"
            :class="proOrderInfoDto.isProfileHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.isProfileHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板边包金"
            :title="proOrderInfoDto.isPlateEdge ? '是' : ''"
            v-show="proOrderInfoDto.isPlateEdge"
            :class="proOrderInfoDto.isPlateEdge ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isPlateEdge ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="沉孔"
            :title="proOrderInfoDto.stepHole ? '是' : ''"
            v-show="proOrderInfoDto.stepHole"
            :class="proOrderInfoDto.stepHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.stepHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="印序列号"
            :title="proOrderInfoDto.isSerialNumber ? '是' : ''"
            v-show="proOrderInfoDto.isSerialNumber"
            :class="proOrderInfoDto.isSerialNumber ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.isSerialNumber ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="蓝胶"
            :title="proOrderInfoDto.isBlueGum ? '是' : ''"
            v-show="proOrderInfoDto.isBlueGum"
            :class="proOrderInfoDto.isBlueGum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isBlueGum ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="埋铜"
            :title="proOrderInfoDto.buriedCopper ? '是' : ''"
            v-show="proOrderInfoDto.buriedCopper"
            :class="proOrderInfoDto.buriedCopper ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.buriedCopper ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="埋阻"
            :title="proOrderInfoDto.buriedResistance ? '是' : ''"
            v-show="proOrderInfoDto.buriedResistance"
            :class="proOrderInfoDto.buriedResistance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.buriedResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="铜浆塞孔"
            :title="proOrderInfoDto.cuPlugHole ? '是' : ''"
            v-show="proOrderInfoDto.cuPlugHole"
            :class="proOrderInfoDto.cuPlugHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.cuPlugHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻抗"
            :title="proOrderInfoDto.isImpedance ? '是' : ''"
            v-show="proOrderInfoDto.isImpedance"
            :class="proOrderInfoDto.isImpedance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore) || (showCG && show1)"
          >
            <span>{{ proOrderInfoDto.isImpedance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="压接孔"
            :title="proOrderInfoDto.isCrimpHole ? '是' : ''"
            v-show="proOrderInfoDto.isCrimpHole"
            :class="proOrderInfoDto.isCrimpHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isCrimpHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="背钻孔"
            :title="proOrderInfoDto.isBackDrilling ? '是' : ''"
            v-show="proOrderInfoDto.isBackDrilling"
            :class="proOrderInfoDto.isBackDrilling ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isBackDrilling ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="通孔控深"
            :title="proOrderInfoDto.isThroughHoleControl ? '是' : ''"
            v-show="proOrderInfoDto.isThroughHoleControl"
            :class="proOrderInfoDto.isThroughHoleControl ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isThroughHoleControl ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="盲孔控深"
            :title="proOrderInfoDto.isBlindHoleControl ? '是' : ''"
            v-show="proOrderInfoDto.isBlindHoleControl"
            :class="proOrderInfoDto.isBlindHoleControl ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isBlindHoleControl ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阶梯孔"
            :title="proOrderInfoDto.steppedHole ? '是' : ''"
            v-show="proOrderInfoDto.steppedHole"
            :class="proOrderInfoDto.steppedHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.steppedHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="碳油"
            :title="proOrderInfoDto.isCarbonOil ? '是' : ''"
            v-show="proOrderInfoDto.isCarbonOil"
            :class="proOrderInfoDto.isCarbonOil ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isCarbonOil ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="高温胶"
            :title="proOrderInfoDto.heatTape ? '是' : ''"
            v-show="proOrderInfoDto.heatTape"
            :class="proOrderInfoDto.heatTape ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.heatTape ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="斜边"
            :title="proOrderInfoDto.goldfingerBevelStr"
            v-show="proOrderInfoDto.goldfingerBevelStr"
            :class="proOrderInfoDto.goldfingerBevelStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="盲槽"
            :title="proOrderInfoDto.isBlindSlot ? '是' : ''"
            v-show="proOrderInfoDto.isBlindSlot"
            :class="proOrderInfoDto.isBlindSlot ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span
              ><span v-if="proOrderInfoDto.isBlindSlot">{{ proOrderInfoDto.isBlindSlot ? "是" : "" }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            label="银浆塞孔"
            :title="proOrderInfoDto.silverPlugHole ? '是' : ''"
            v-show="proOrderInfoDto.silverPlugHole"
            :class="proOrderInfoDto.silverPlugHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.silverPlugHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="跳V"
            :title="proOrderInfoDto.jumpCut ? '是' : ''"
            v-show="proOrderInfoDto.jumpCut"
            :class="proOrderInfoDto.jumpCut ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.jumpCut ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="局部镀厚金"
            :title="proOrderInfoDto.isPartPlatingThickGold == 1 ? '是' : ''"
            v-show="proOrderInfoDto.isPartPlatingThickGold == 1"
            :class="proOrderInfoDto.isPartPlatingThickGold == 1 ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.isPartPlatingThickGold == 1 ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金属铣槽"
            :title="proOrderInfoDto.isMetalSlot ? '是' : ''"
            v-show="proOrderInfoDto.isMetalSlot"
            :class="proOrderInfoDto.isMetalSlot ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.isMetalSlot ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指"
            :title="proOrderInfoDto.goldenfingerStr"
            v-show="proOrderInfoDto.goldenfingerStr"
            :class="proOrderInfoDto.goldenfingerStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldenfingerStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label='金手指金厚U"'
            :title="proOrderInfoDto.goldFingerThickness"
            v-show="proOrderInfoDto.goldFingerThickness"
            :class="proOrderInfoDto.goldFingerThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldFingerThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :label="'金手指镍厚' + proOrderInfoDto.goldfingerNieThicknessUnit"
            :title="proOrderInfoDto.goldfingerNickelThickness"
            v-show="proOrderInfoDto.goldfingerNickelThickness"
            :class="proOrderInfoDto.goldfingerNickelThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerNickelThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边角度"
            :title="proOrderInfoDto.goldfingerBevelAngle"
            v-show="proOrderInfoDto.goldfingerBevelAngle"
            :class="proOrderInfoDto.goldfingerBevelAngle ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelAngle }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边深度"
            :title="proOrderInfoDto.goldfingerBevelDepth"
            v-show="proOrderInfoDto.goldfingerBevelDepth"
            :class="proOrderInfoDto.goldfingerBevelDepth ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelDepth }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边余厚"
            :title="proOrderInfoDto.goldfingerBevelSurplus"
            v-show="proOrderInfoDto.goldfingerBevelSurplus"
            :class="proOrderInfoDto.goldfingerBevelSurplus ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelSurplus }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="去独立PAD"
            :title="proOrderInfoDto.deSinglePad ? '是' : ''"
            v-show="proOrderInfoDto.deSinglePad"
            :class="proOrderInfoDto.deSinglePad ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.deSinglePad ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="最小孔铜"
            :title="proOrderInfoDto.minHoleCopper"
            v-show="proOrderInfoDto.minHoleCopper"
            :class="proOrderInfoDto.minHoleCopper ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.minHoleCopper }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="平均孔铜"
            :title="proOrderInfoDto.avgHoleCopper"
            v-show="proOrderInfoDto.avgHoleCopper"
            :class="proOrderInfoDto.avgHoleCopper ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.avgHoleCopper }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="光板数"
            :title="proOrderInfoDto.gbNum"
            v-show="proOrderInfoDto.gbNum"
            :class="proOrderInfoDto.gbNum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.gbNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="对压"
            :title="proOrderInfoDto.counterPressure ? '是' : ''"
            v-show="proOrderInfoDto.counterPressure"
            :class="proOrderInfoDto.counterPressure ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.counterPressure ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="水印"
            :title="proOrderInfoDto.waterMark_ ? '是' : ''"
            v-show="proOrderInfoDto.waterMark_"
            :class="proOrderInfoDto.waterMark_ ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.waterMark_ ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="外层泪滴"
            :title="proOrderInfoDto.outerLineTeardrop ? '是' : ''"
            v-show="proOrderInfoDto.outerLineTeardrop"
            :class="proOrderInfoDto.outerLineTeardrop ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.outerLineTeardrop ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="内层泪滴"
            :title="proOrderInfoDto.innerLineTeardrop ? '是' : ''"
            v-show="proOrderInfoDto.innerLineTeardrop"
            :class="proOrderInfoDto.innerLineTeardrop ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.innerLineTeardrop ? "是" : "" }}</span>
          </a-form-model-item>
        </div>
        <div class="div2">
          <a-form-model-item
            label="出货报告"
            :title="proOrderInfoDto.needReportListStr"
            v-show="proOrderInfoDto.needReportListStr"
            :class="proOrderInfoDto.needReportListStr ? 'div22' : ''"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
            style="border-top: 1px solid #ddd"
          >
            <span class="edit" style="user-select: text">{{ proOrderInfoDto.needReportListStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="工程指示"
            :title="proOrderInfoDto.specialRemarks"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
            v-show="proOrderInfoDto.specialRemarks"
            :class="proOrderInfoDto.specialRemarks ? 'div22' : ''"
            style="border-top: 1px solid #ddd"
          >
            <span style="user-select: text">{{ proOrderInfoDto.specialRemarks }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户备注"
            :title="proOrderInfoDto.note"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
            v-show="proOrderInfoDto.note"
            :class="proOrderInfoDto.note ? 'div22' : ''"
            style="border-top: 1px solid #ddd"
          >
            <span style="user-select: text">{{ proOrderInfoDto.note }}</span>
          </a-form-model-item>
        </div>
      </a-form-model>
      <a-form-model
        layout="inline"
        style="width: 100%; border-left: 1px solid #ddd; margin-top: 10px; border-top: 1px solid #ddd"
        id="formDataElem"
        v-show="editFlg1"
      >
        <a-row>
          <a-col :span="4" class="colSTY">
            <a-form-model-item label="本厂编号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :class="[editFlg1 ? 'disable' : '']">
              <span :title="proOrderInfoDto.orderNo" class="tmp">{{ proOrderInfoDto.orderNo }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" class="colSTY">
            <a-form-model-item label="客户型号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :class="[editFlg1 ? 'disable' : '']">
              <span :title="proOrderInfoDto.pcbFileName" class="tmp" @click="dwon1(proOrderInfoDto.pcbFilePath)">{{
                proOrderInfoDto.pcbFileName
              }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="拼版方式"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.pinBanType1 && iseval(requiredLinkConfigpro.pinBanType1.isNullRules) ? 'require' : ''"
            >
              <span>
                <a-input
                  v-model="proOrderInfoDto.pinBanType1"
                  style="margin-left: 0px; width: 46.4%; border-bottom-right-radius: 0; border-top-right-radius: 0"
                  @change="changesu"
                  v-focus-next-on-tab="'42'"
                  ref="41"
                  @blur="truncationpb1()"
                />
                <span
                  style="
                    display: inline-block;
                    background: #f3f3f3;
                    height: 22px;
                    line-height: 19px;
                    width: 7%;
                    text-align: center;
                    border-top: 1px solid #d9d9d9;
                    border-bottom: 1px solid #d9d9d9;
                  "
                  >X</span
                >
                <a-input
                  v-model="proOrderInfoDto.pinBanType2"
                  style="margin-left: 0px; width: 46.3%; border-top-left-radius: 0; border-bottom-left-radius: 0"
                  @change="changesu"
                  v-focus-next-on-tab="'43'"
                  ref="42"
                  @blur="truncationpb2()"
                />
              </span>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="SU数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div>
                <a-input v-model="proOrderInfoDto.su" allowClear disabled> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="层压不可更改" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.isChangeLayerPres" v-focus-next-on-tab="'62'" ref="61"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="标记位置"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.markPosition && iseval(requiredLinkConfigpro.markPosition.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.markPosition"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="changemark"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'72'"
                  ref="71"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.MarkPosition)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="板类型"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.plateType && iseval(requiredLinkConfigpro.plateType.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.plateType"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  @change="changelayers"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'2'"
                  ref="1"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PlateType)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" class="colSTY">
            <a-form-model-item label="客户物料号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :class="[editFlg1 ? 'disable' : '']">
              <span :title="proOrderInfoDto.customerMaterialNo" style="cursor: pointer" class="tmp">{{ proOrderInfoDto.customerMaterialNo }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="单元长mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.boardHeight && iseval(requiredLinkConfigpro.boardHeight.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.boardHeight" allowClear v-focus-next-on-tab="'44'" ref="43" @blur="truncationbh()"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="单元宽mm"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.boardWidth && iseval(requiredLinkConfigpro.boardWidth.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.boardWidth" allowClear v-focus-next-on-tab="'45'" ref="44" @blur="truncationbw()"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="不接受打叉" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <a-checkbox v-model="proOrderInfoDto.acceptCrossed" v-focus-next-on-tab="'63'" ref="62"></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="UL类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.ulType && iseval(requiredLinkConfigpro.ulType.isNullRules) ? 'require' : ''"
            >
              <div class="heightSty">
                <a-select
                  v-model="proOrderInfoDto.ulType"
                  mode="multiple"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'73'"
                  ref="72"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.ULType)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="成品板厚mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.boardThickness && iseval(requiredLinkConfigpro.boardThickness.isNullRules) ? 'require' : ''"
            >
              <div style="display: flex">
                <a-select
                  v-model="proOrderInfoDto.boardThickness"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate($event, mapKey(selectData.BoardThickness))"
                  @search="handleSearch($event, mapKey(selectData.BoardThickness))"
                  @blur="handleBlur($event, mapKey(selectData.BoardThickness))"
                  v-focus-next-on-tab="'3'"
                  ref="2"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.BoardThickness)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="proOrderInfoDto.boardThicknessTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate9($event, mapKey(selectData.BoardThicknessTol))"
                  @search="handleSearch9($event, mapKey(selectData.BoardThicknessTol))"
                  @blur="handleBlur9($event, mapKey(selectData.BoardThicknessTol))"
                  v-focus-next-on-tab="'71'"
                  ref="70"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.BoardThicknessTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="层数"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.boardLayers && iseval(requiredLinkConfigpro.boardLayers.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.boardLayers"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate1($event, mapKey(selectData.BoardLayers))"
                  @search="handleSearch1($event, mapKey(selectData.BoardLayers))"
                  @blur="handleBlur1($event, mapKey(selectData.BoardLayers))"
                  v-focus-next-on-tab="'4'"
                  ref="3"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.BoardLayers)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="成品长mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.boardHeightSet && iseval(requiredLinkConfigpro.boardHeightSet.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.boardHeightSet" allowClear v-focus-next-on-tab="'46'" ref="45" @blur="truncationbhs()"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="成品宽mm"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.boardWidthSet && iseval(requiredLinkConfigpro.boardWidthSet.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.boardWidthSet" allowClear v-focus-next-on-tab="'47'" ref="46" @blur="truncationbws()"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="不允许补线" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <a-checkbox v-model="proOrderInfoDto.notAcceptPatching" v-focus-next-on-tab="'64'" ref="63"></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="标记类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.markType && iseval(requiredLinkConfigpro.markType.isNullRules) ? 'require' : ''"
            >
              <div class="heightSty">
                <a-select
                  v-model="proOrderInfoDto.markType"
                  mode="multiple"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'74'"
                  ref="73"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.MarkType)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="成品铜厚oz"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              v-if="!proOrderInfoDto.isCopperThickConversion"
              :class="requiredLinkConfigpro.copperThickness && iseval(requiredLinkConfigpro.copperThickness.isNullRules) ? 'require' : ''"
            >
              <div class="editWrapper" style="display: flex">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%" v-if="Number(proOrderInfoDto.boardLayers) > 2">内</span>
                <a-select
                  v-model="proOrderInfoDto.innerCopperThickness"
                  v-if="Number(proOrderInfoDto.boardLayers) > 2"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="setEstimate2($event, mapKey(selectData.InnerCopperThickness))"
                  @search="handleSearch2($event, mapKey(selectData.InnerCopperThickness))"
                  @blur="handleBlur2($event, mapKey(selectData.InnerCopperThickness))"
                  v-focus-next-on-tab="'5'"
                  ref="4"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.InnerCopperThickness)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%">外</span>
                <a-select
                  v-model="proOrderInfoDto.copperThickness"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate3($event, mapKey(selectData.CopperThickness))"
                  @search="handleSearch3($event, mapKey(selectData.CopperThickness))"
                  @blur="handleBlur3($event, mapKey(selectData.CopperThickness))"
                  v-focus-next-on-tab="'6'"
                  ref="5"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.CopperThickness)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="成品铜厚um"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              v-if="proOrderInfoDto.isCopperThickConversion"
              :class="requiredLinkConfigpro.copperThickness && iseval(requiredLinkConfigpro.copperThickness.isNullRules) ? 'require' : ''"
            >
              <div class="editWrapper" style="display: flex">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%" v-if="Number(proOrderInfoDto.boardLayers) > 2">内</span>
                <a-select
                  v-model="proOrderInfoDto.innerCopperThickness2"
                  v-if="Number(proOrderInfoDto.boardLayers) > 2"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  v-focus-next-on-tab="'5'"
                  ref="4"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.InnerCopperThickness2)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%">外</span>
                <a-select
                  v-model="proOrderInfoDto.copperThickness2"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'6'"
                  ref="5"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.CopperThickness2)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="成型方式"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.formingType && iseval(requiredLinkConfigpro.formingType.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.formingType"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="changevcut"
                  v-focus-next-on-tab="'48'"
                  ref="47"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FormingType)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="成型公差"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.formingTol && iseval(requiredLinkConfigpro.formingTol.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.formingTolStr }}</span>
              <span v-else>
                <a-select
                  v-model="proOrderInfoDto.formingTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'49'"
                  ref="48"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FormingTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </span>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="测试方式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.flyingProbe && iseval(requiredLinkConfigpro.flyingProbe.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.flyingProbeStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.flyingProbe"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  @change="flyChange"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'65'"
                  ref="64"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FlyingProbe)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="标记面向"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.markFace && iseval(requiredLinkConfigpro.markFace.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.markFaceStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.markFace"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'75'"
                  ref="74"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.MarkFace)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="阻焊颜色"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.solderColor && iseval(requiredLinkConfigpro.solderColor.isNullRules) ? 'require' : ''"
            >
              <div>
                <b style="color: red; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [顶] </b>
                <a-select
                  v-model="proOrderInfoDto.solderColor"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="solderColorC('change')"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'7'"
                  ref="6"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SolderColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <b style="color: green; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [底] </b>
                <a-select
                  v-model="proOrderInfoDto.solderColorBottom"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'8'"
                  ref="7"
                  @change="solderColorC('change', 'bot')"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SolderColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-left: 10px">阻焊特性:</span>
                <a-select
                  v-model="proOrderInfoDto.solderCharacteristics"
                  style="width: 83px; margin-left: 5px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'9'"
                  ref="8"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SolderCharacteristics)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="合拼款数"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.pinBanNum && iseval(requiredLinkConfigpro.pinBanNum.isNullRules) ? 'require' : ''"
            >
              <a-input v-model="proOrderInfoDto.pinBanNum" allowClear v-focus-next-on-tab="'50'" ref="49" @blur="truncationbpbn()"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="出货单位"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.boardType && iseval(requiredLinkConfigpro.boardType.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.boardTypeStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.boardType"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'51'"
                  ref="50"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.BoardType)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="盖ET章"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.stampEtPosition && iseval(requiredLinkConfigpro.stampEtPosition.isNullRules) ? 'require' : ''"
            >
              <a-select
                v-model="proOrderInfoDto.stampEtPosition"
                showSearch
                allowClear
                optionFilterProp="label"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'66'"
                ref="65"
              >
                <a-select-option
                  v-for="item in mapKey(selectData.StampEtPosition)"
                  :key="item.value"
                  :value="item.value"
                  :label="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="周期格式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.periodicFormat && iseval(requiredLinkConfigpro.periodicFormat.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.periodicFormatStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.periodicFormat"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'76'"
                  ref="75"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PeriodicFormat)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="阻焊油墨"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.solderResistInk && iseval(requiredLinkConfigpro.solderResistInk.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.solderResistInk"
                  style="width: 231px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'10'"
                  ref="9"
                >
                  <a-select-option v-for="item in SolderResistInk1" :key="item.value" :value="item.value" :lable="item.lable" :title="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="padding-left: 10px; padding-right: 10px">无卤油墨 :</span>
                <a-checkbox v-model="proOrderInfoDto.isInkNotHalogen" v-focus-next-on-tab="'11'" ref="10" @change="solderColorC('change', 'bot')" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="板材商"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.sheetTrader && iseval(requiredLinkConfigpro.sheetTrader.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.sheetTraderStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.sheetTrader"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'52'"
                  ref="51"
                  @change="changeSheet"
                >
                  <a-select-option
                    v-for="item in sheetTrader"
                    :key="item.valueMember"
                    :value="item.valueMember"
                    :label="item.text"
                    :title="item.text"
                  >
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="客供板材" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.isCustomerBoard ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isCustomerBoard" v-focus-next-on-tab="'53'" ref="52"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="飞针测试方式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.fpTestMethod && iseval(requiredLinkConfigpro.fpTestMethod.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.fpTestMethodStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.fpTestMethod"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'67'"
                  ref="66"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FpTestMethod)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="客户指定" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.custAssignment ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.custAssignment" v-focus-next-on-tab="'77'" ref="76"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="字符颜色"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.fontColor && iseval(requiredLinkConfigpro.fontColor.isNullRules) ? 'require' : ''"
            >
              <div>
                <b style="color: red; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [顶] </b>
                <a-select
                  v-model="proOrderInfoDto.fontColor"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="fontColorC('change')"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'12'"
                  ref="11"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FontColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <b style="color: green; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [底] </b>
                <a-select
                  v-model="proOrderInfoDto.fontColorBottom"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'13'"
                  ref="12"
                  @change="fontColorC('change', 'bot')"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FontColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-left: 10px">字符特性:</span>
                <a-select
                  v-model="proOrderInfoDto.characterInkCharacteristics"
                  style="width: 83px; margin-left: 5px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'14'"
                  ref="13"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.CharacterInkCharacteristics)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="板材类型"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.fR4Type && iseval(requiredLinkConfigpro.fR4Type.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.fR4TypeStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.fR4Type"
                  showSearch
                  @change="changevcut"
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'54'"
                  ref="53"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FR4Type)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="耐CAF"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.cafResistance && iseval(requiredLinkConfigpro.cafResistance.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.cafResistance ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.cafResistance" v-focus-next-on-tab="'58'" ref="57" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="测试治具编号"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.testFixtureNumber && iseval(requiredLinkConfigpro.testFixtureNumber.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.testFixtureNumber" allowClear v-focus-next-on-tab="'68'" ref="67" @blur="truncationtfn()"></a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="低阻测试"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.isLowResistanceTest && iseval(requiredLinkConfigpro.isLowResistanceTest.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isLowResistanceTest ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isLowResistanceTest" v-focus-next-on-tab="'78'" ref="77"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="字符油墨"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.characterResistInk && iseval(requiredLinkConfigpro.characterResistInk.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.characterResistInk"
                  style="width: 231px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'15'"
                  ref="14"
                >
                  <a-select-option v-for="item in CharacterResistInk1" :key="item.value" :value="item.value" :lable="item.lable" :title="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="padding-right: 10px; padding-left: 10px">字符无卤 :</span>
                <a-checkbox v-model="proOrderInfoDto.characterNotHalogen" v-focus-next-on-tab="'16'" ref="15" @change="fontColorC('change', 'bot')" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="板材型号"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.boardBrand && iseval(requiredLinkConfigpro.boardBrand.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.boardBrand"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'55'"
                  ref="54"
                  @change="changeType"
                >
                  <a-select-option v-for="item in boardBrand" :key="item.valueMember" :value="item.valueMember" :label="item.text" :title="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="板材CTI"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.plateCti && iseval(requiredLinkConfigpro.plateCti.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.plateCtiStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.plateCti"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'59'"
                  ref="58"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PlateCti)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="检验标准"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.ipcLevel && iseval(requiredLinkConfigpro.ipcLevel.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.ipcLevelStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.ipcLevel"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'69'"
                  ref="68"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.IPCLevel)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="确认工作稿" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.confirmWorkingDraft ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.confirmWorkingDraft" v-focus-next-on-tab="'79'" ref="78"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="表面处理"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              class="surSTY"
              :class="requiredLinkConfigpro.surfaceFinish && iseval(requiredLinkConfigpro.surfaceFinish.isNullRules) ? 'require' : ''"
            >
              <div style="display: flex; flex-wrap: wrap">
                <span style="width: 37%">
                  <a-select
                    v-model="proOrderInfoDto.surfaceFinish"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="changesurface"
                    v-focus-next-on-tab="'17'"
                    ref="16"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.SurfaceFinish)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </span>
                <span style="color: red; margin: 0 0.1%; width: 30%" v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold'"
                  >水金:
                  <a-select
                    style="display: inline-block; width: 49%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'18'"
                    ref="17"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 23%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'waterhardgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating'||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj'
                  "
                  >面积:
                  <a-input
                    style="display: inline-block; width: 46%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                    v-focus-next-on-tab="'19'"
                    ref="18"
                    @blur="truncationspa()"
                  />%
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 32%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin'
                  "
                  >镀金面积
                  <a-input
                    style="display: inline-block; width: 37%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                    v-focus-next-on-tab="'20'"
                    ref="19"
                    @blur="truncationspa()"
                  />
                  %
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 34%"
                  :class="proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ? 'sss' : ''"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'hardgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold'
                  "
                  >镀金厚:
                  <a-select
                    style="display: inline-block; width: 49%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'21'"
                    ref="20"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 34%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'waterhardgold' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj'
                  "
                  >镍:
                  <a-select
                    style="display: inline-block; width: 70%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate7($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch7($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur7($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'22'"
                    ref="21"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 34%" v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold'"
                  >软金:
                  <a-select
                    style="display: inline-block; width: 50%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'23'"
                    ref="22"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 30%"
                  v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold'"
                  :class="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ? 'sss' : ''"
                  >化金厚:
                  <a-select
                    style="display: inline-block; width: 42%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'26'"
                    ref="25"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 35%; height: 30px"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj'
                  "
                  >金:
                  <a-select
                    style="display: inline-block; width: 52%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'27'"
                    ref="26"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 20%; height: 30px"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                  "
                  >薄金:
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'27'"
                    ref="26"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 23%" v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold'"
                  >面积:
                  <a-input
                    style="display: inline-block; width: 46%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                    v-focus-next-on-tab="'24'"
                    ref="23"
                    @blur="truncationspa2()"
                  />%
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 29%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating'
                  "
                  >金:
                  <a-select
                    style="display: inline-block; width: 64%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'25'"
                    ref="24"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 25%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                  "
                  >厚金:
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'25'"
                    ref="24"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 62%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'osp' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandGoldfinger'
                  "
                  :class="
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger'
                      ? 'sss'
                      : ''
                  "
                  >膜厚:
                  <a-input
                    style="display: inline-block; width: 74%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.filmThickness"
                    v-focus-next-on-tab="'28'"
                    ref="27"
                    @blur="truncationsft()"
                  />um
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 62%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'chemicalsilver' ||
                    proOrderInfoDto.surfaceFinish == 'sinksilverandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'silverplating' ||
                    proOrderInfoDto.surfaceFinish == 'outsourcingsilverplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating'
                  "
                  >银厚:
                  <a-input
                    style="display: inline-block; width: 74%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness"
                    v-focus-next-on-tab="'29'"
                    ref="28"
                    @blur="truncationsnst()"
                  />um
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 100%"
                  :class="proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ? 'sss' : ''"
                  v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree'"
                  >无铅锡厚 :
                  <a-input
                    style="display: inline-block; width: 73%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newTinThickness"
                    v-focus-next-on-tab="'30'"
                    ref="29"
                  />um
                </span>
                <span style="color: red; margin: 0 0.1%; width: 29%" v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger'"
                  >镀镍厚:
                  <a-select
                    style="display: inline-block; width: 55%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate7($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch7($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur7($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'31'"
                    ref="30"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 29%" v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger'"
                  >镍面积 :
                  <a-input
                    style="display: inline-block; width: 52%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                    v-focus-next-on-tab="'32'"
                    ref="31"
                    @blur="truncationspa()"
                  />
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 62%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'haslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'spraytin' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'tinplating' ||
                    proOrderInfoDto.surfaceFinish == 'tinnedcerium' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'yqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandGoldfinger'
                  "
                  :class="
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree'
                      ? 'sss'
                      : ''
                  "
                  >锡:
                  <a-select
                    style="display: inline-block; width: 80%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newTinThickness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate10($event, mapKey(selectData.Tinthickness))"
                    @search="handleSearch10($event, mapKey(selectData.Tinthickness))"
                    @blur="handleBlur10($event, mapKey(selectData.Tinthickness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.Tinthickness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >um
                  <!-- <a-input
                    style="display: inline-block; width: 80%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newTinThickness"
                    v-focus-next-on-tab="'33'"
                    ref="32"
                    @blur="truncationntt()"
                  />um -->
                </span>
                <span style="color: red; margin: 0 0.1%; width: 62%" v-if="proOrderInfoDto.surfaceFinish == 'tinprecipitation'"
                  >锡:
                  <a-select
                    style="display: inline-block; width: 80%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate11($event, mapKey(selectData.Tinthickness2))"
                    @search="handleSearch11($event, mapKey(selectData.Tinthickness2))"
                    @blur="handleBlur11($event, mapKey(selectData.Tinthickness2))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.Tinthickness2)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >um
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 29%"
                  v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' || proOrderInfoDto.surfaceFinish == 'hardgoldplating'"
                  >镍厚 :
                  <a-select
                    style="display: inline-block; width: 55%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate8($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch8($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur8($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'34'"
                    ref="33"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 29%; height: 30px" v-if="proOrderInfoDto.surfaceFinish == 'fullgoldplating'"
                  >镍厚 :
                  <a-select
                    style="display: inline-block; width: 55%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate8($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch8($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur8($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'35'"
                    ref="34"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>

                <span style="color: red; margin: 0 0.1%; width: 44%; height: 30px" v-if="proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold'"
                  >钯 :
                  <a-input
                    style="display: inline-block; width: 62%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.paThickness"
                    v-focus-next-on-tab="'36'"
                    ref="35"
                    @blur="truncationspt()"
                  />{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 28%" v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold'"
                  >化金面积:
                  <a-input
                    style="display: inline-block; width: 39%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                    v-focus-next-on-tab="'37'"
                    ref="36"
                    @blur="truncationspa2()"
                  />%
                </span>
                <span style="color: red; margin: 0 0.1%; width: 20%" v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger'"
                  >金:
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'38'"
                    ref="37"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 24%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold'
                  "
                  >面积:
                  <a-input
                    style="display: inline-block; width: 39%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                    v-focus-next-on-tab="'39'"
                    ref="38"
                    @blur="truncationspa2()"
                  />%
                </span>

                <span style="color: red; margin: 0 0.1%; width: 39%" v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold'"
                  >化金镍厚:
                  <a-select
                    style="display: inline-block; width: 59%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate8($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch8($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur8($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'40'"
                    ref="39"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 29%; height: 30px"
                  v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold'"
                  >化金厚:
                  <a-select
                    style="display: inline-block; width: 42%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    v-focus-next-on-tab="'41'"
                    ref="40"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="板材TG"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.fR4Tg && iseval(requiredLinkConfigpro.fR4Tg.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.fR4TgStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.fR4Tg"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'56'"
                  ref="55"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FR4Tg)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="大料尺寸"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.sheetSize && iseval(requiredLinkConfigpro.sheetSize.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.sheetSize }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.sheetSize"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'57'"
                  ref="56"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SheetSize)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="无卤板材" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.isNotHalogen ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isNotHalogen" v-focus-next-on-tab="'60'" ref="59" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="板材利用率(%)"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.sheetUtilization && iseval(requiredLinkConfigpro.sheetUtilization.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.sheetUtilization }}</span>
              <a-input
                v-else
                v-model="proOrderInfoDto.sheetUtilization"
                allowClear
                v-focus-next-on-tab="'61'"
                ref="60"
                @blur="truncationsu()"
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="翘曲度"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.warpage && iseval(requiredLinkConfigpro.warpage.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.warpage"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate4($event, mapKey(selectData.Warpage))"
                  @search="handleSearch4($event, mapKey(selectData.Warpage))"
                  @blur="handleBlur4($event, mapKey(selectData.Warpage))"
                  v-focus-next-on-tab="'70'"
                  ref="69"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.Warpage)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="产品用途"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.productUsage && iseval(requiredLinkConfigpro.productUsage.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.productUsage"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'71'"
                  ref="70"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.ProductUsage)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="确认阻抗" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.confirmImpedance ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.confirmImpedance" v-focus-next-on-tab="'80'" ref="79"></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item label="确认叠层" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.confirmStacking ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.confirmStacking" v-focus-next-on-tab="'81'" ref="80"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-divider style="height: 6px; background-color: #c2c1c2; margin: 8px 0" />
        <a-row style="text-align: center">
          <a-col :span="4" style="border-top: 1px solid #ddd; border-left: 1px solid #ddd">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">钻铣参数</strong>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" style="border-top: 1px solid #ddd; border-left: 1px solid #ddd">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">阻焊&文字参数 </strong>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" style="border-top: 1px solid #ddd; border-left: 1px solid #ddd">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">出货信息</strong>
            </a-form-model-item>
          </a-col>
          <a-col :span="9" style="border-top: 1px solid #ddd; border-left: 1px solid #ddd">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">特殊工艺</strong>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="V-CUT类型"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.vCut && iseval(requiredLinkConfigpro.vCut.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.vCutStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.vCut"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-if="
                    proOrderInfoDto.formingType == 'machinemold+vcut' ||
                    proOrderInfoDto.formingType == 'vcut' ||
                    proOrderInfoDto.formingType == 'vcut+rtr' ||
                    proOrderInfoDto.formingType == 'mechanical_milling+vcut'
                  "
                  v-focus-next-on-tab="'82'"
                  ref="81"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VCut)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="proOrderInfoDto.vCut"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-else
                  disabled
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VCut)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="V-CUT刀数"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.vCutKnifeNum && iseval(requiredLinkConfigpro.vCutKnifeNum.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.vCutKnifeNum }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.vCutKnifeNum"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-if="
                    proOrderInfoDto.formingType == 'machinemold+vcut' ||
                    proOrderInfoDto.formingType == 'vcut' ||
                    proOrderInfoDto.formingType == 'vcut+rtr' ||
                    proOrderInfoDto.formingType == 'mechanical_milling+vcut'
                  "
                  v-focus-next-on-tab="'83'"
                  ref="82"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VCutKnifeNum)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="proOrderInfoDto.vCutKnifeNum"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-else
                  disabled
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VCutKnifeNum)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="角度"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.vcutAngle && iseval(requiredLinkConfigpro.vcutAngle.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.vcutAngleStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.vcutAngle"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-if="
                    proOrderInfoDto.formingType == 'machinemold+vcut' ||
                    proOrderInfoDto.formingType == 'vcut' ||
                    proOrderInfoDto.formingType == 'vcut+rtr' ||
                    proOrderInfoDto.formingType == 'mechanical_milling+vcut'
                  "
                  v-focus-next-on-tab="'84'"
                  ref="83"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutAngle)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <!-- v-focus-next-on-tab="'71'" ref="70" -->
                <a-select
                  v-model="proOrderInfoDto.vcutAngle"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-else
                  disabled
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutAngle)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="角度公差"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.vcutAngleTol && iseval(requiredLinkConfigpro.vcutAngleTol.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.vcutAngleTolStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.vcutAngleTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-if="
                    proOrderInfoDto.formingType == 'machinemold+vcut' ||
                    proOrderInfoDto.formingType == 'vcut' ||
                    proOrderInfoDto.formingType == 'vcut+rtr' ||
                    proOrderInfoDto.formingType == 'mechanical_milling+vcut'
                  "
                  v-focus-next-on-tab="'85'"
                  ref="84"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutAngleTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="proOrderInfoDto.vcutAngleTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-else
                  disabled
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutAngleTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="余厚mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.vcutSurplusThickness && iseval(requiredLinkConfigpro.vcutSurplusThickness.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.vcutSurplusThickness }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.vcutSurplusThickness"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-if="
                    proOrderInfoDto.formingType == 'machinemold+vcut' ||
                    proOrderInfoDto.formingType == 'vcut' ||
                    proOrderInfoDto.formingType == 'vcut+rtr' ||
                    proOrderInfoDto.formingType == 'mechanical_milling+vcut'
                  "
                  @change="setEstimate5($event, mapKey(selectData.VcutSurplusThickness))"
                  @search="handleSearch5($event, mapKey(selectData.VcutSurplusThickness))"
                  @blur="handleBlur5($event, mapKey(selectData.VcutSurplusThickness))"
                  v-focus-next-on-tab="'86'"
                  ref="85"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutSurplusThickness)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="proOrderInfoDto.vcutSurplusThickness"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-else
                  disabled
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutSurplusThickness)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="余厚公差mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="
                requiredLinkConfigpro.vcutSurplusThicknessTol && iseval(requiredLinkConfigpro.vcutSurplusThicknessTol.isNullRules) ? 'require' : ''
              "
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.vcutSurplusThicknessTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-if="
                    proOrderInfoDto.formingType == 'machinemold+vcut' ||
                    proOrderInfoDto.formingType == 'vcut' ||
                    proOrderInfoDto.formingType == 'vcut+rtr' ||
                    proOrderInfoDto.formingType == 'mechanical_milling+vcut'
                  "
                  v-focus-next-on-tab="'87'"
                  ref="86"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutSurplusThicknessTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="proOrderInfoDto.vcutSurplusThicknessTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-else
                  disabled
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutSurplusThicknessTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="余厚指定"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="
                requiredLinkConfigpro.vcutSurplusThicknessZD && iseval(requiredLinkConfigpro.vcutSurplusThicknessZD.isNullRules) ? 'require' : ''
              "
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.vcutSurplusThicknessZD"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.VcutSurplusThicknessZD)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="锣孔公差mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.cncHoleTol && iseval(requiredLinkConfigpro.cncHoleTol.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.cncHoleTolStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.cncHoleTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'88'"
                  ref="87"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.CncHoleTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="锣带次数"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.routNumber && iseval(requiredLinkConfigpro.routNumber.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.routNumber }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.routNumber"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'89'"
                  ref="88"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.RoutNumber)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="过孔处理"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.solderCover && iseval(requiredLinkConfigpro.solderCover.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.solderCoverStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.solderCover"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="changeplugoil"
                  v-focus-next-on-tab="'90'"
                  ref="89"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SolderCover)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="最小阻焊桥mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.minSolderBridge && iseval(requiredLinkConfigpro.minSolderBridge.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.minSolderBridge }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.minSolderBridge"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate6($event, mapKey(selectData.MinSolderBridge))"
                  @search="handleSearch6($event, mapKey(selectData.MinSolderBridge))"
                  @blur="handleBlur6($event, mapKey(selectData.MinSolderBridge))"
                  v-focus-next-on-tab="'91'"
                  ref="90"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.MinSolderBridge)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="基材油墨厚度um"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.solderThickness && iseval(requiredLinkConfigpro.solderThickness.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.solderThickness }}</span>
              <div v-else>
                <a-input v-model="proOrderInfoDto.solderThickness" allowClear v-focus-next-on-tab="'92'" ref="91" @blur="truncationst()"> </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="线角油墨厚度um"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.footLineInkThickness && iseval(requiredLinkConfigpro.footLineInkThickness.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.footLineInkThickness }}</span>
              <div v-else>
                <a-input v-model="proOrderInfoDto.footLineInkThickness" allowClear v-focus-next-on-tab="'93'" ref="92" @blur="truncationflt()">
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="铜面油墨厚度um"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.cuInkThickness && iseval(requiredLinkConfigpro.cuInkThickness.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.cuInkThickness }}</span>
              <div v-else>
                <a-input v-model="proOrderInfoDto.cuInkThickness" allowClear v-focus-next-on-tab="'94'" ref="93" @blur="truncationcit()"> </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="阻焊厚度"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.solderInkThickness && iseval(requiredLinkConfigpro.solderInkThickness.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.solderInkThickness }}</span>
              <div v-else>
                <a-input v-model="proOrderInfoDto.solderInkThickness" allowClear v-focus-next-on-tab="'95'" ref="94" @blur="truncationsit()">
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="塞孔程度"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.plugOilExtent && iseval(requiredLinkConfigpro.plugOilExtent.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.plugOilExtent"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'96'"
                  ref="95"
                  :disabled="
                    proOrderInfoDto.solderCover == 'plugoil' ||
                    proOrderInfoDto.solderCover == 'aluminiumplugoil' ||
                    proOrderInfoDto.solderCover == 'bgaplugoil' ||
                    proOrderInfoDto.solderCover == 'openwindowplusplugoil' ||
                    ((proOrderInfoDto.solderCover == 'resinplughole+cap' ||
                      proOrderInfoDto.solderCover == 'ResinPlugWithoutCap' ||
                      proOrderInfoDto.solderCover == 'resinplughole' ||
                      proOrderInfoDto.solderCover == 'plugoil+openminwindow' ||
                      proOrderInfoDto.solderCover == 'plugoil+converoil') &&
                      this.$route.query.factory == 22)
                      ? false
                      : true
                  "
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PlugOilExtent)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="塞孔工具"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.plugOilTool && iseval(requiredLinkConfigpro.plugOilTool.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.plugOilTool"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'96'"
                  ref="95"
                  :disabled="
                    proOrderInfoDto.solderCover == 'plugoil' ||
                    proOrderInfoDto.solderCover == 'aluminiumplugoil' ||
                    proOrderInfoDto.solderCover == 'bgaplugoil' ||
                    proOrderInfoDto.solderCover == 'openwindowplusplugoil' ||
                    ((proOrderInfoDto.solderCover == 'resinplughole+cap' ||
                      proOrderInfoDto.solderCover == 'ResinPlugWithoutCap' ||
                      proOrderInfoDto.solderCover == 'resinplughole' ||
                      proOrderInfoDto.solderCover == 'plugoil+openminwindow' ||
                      proOrderInfoDto.solderCover == 'plugoil+converoil') &&
                      this.$route.query.factory == 22)
                      ? false
                      : true
                  "
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PlugOilTool)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="塞孔面次"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.plugOilSide && iseval(requiredLinkConfigpro.plugOilSide.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.plugOilSide"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'97'"
                  ref="96"
                  :disabled="
                    proOrderInfoDto.solderCover == 'plugoil' ||
                    proOrderInfoDto.solderCover == 'aluminiumplugoil' ||
                    proOrderInfoDto.solderCover == 'bgaplugoil' ||
                    proOrderInfoDto.solderCover == 'openwindowplusplugoil' ||
                    ((proOrderInfoDto.solderCover == 'resinplughole+cap' ||
                      proOrderInfoDto.solderCover == 'ResinPlugWithoutCap' ||
                      proOrderInfoDto.solderCover == 'resinplughole' ||
                      proOrderInfoDto.solderCover == 'plugoil+converoil' ||
                      proOrderInfoDto.solderCover == 'plugoil+openminwindow') &&
                      this.$route.query.factory == 22)
                      ? false
                      : true
                  "
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PlugOilSide)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item label="是否大白油块" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <a-checkbox v-model="proOrderInfoDto.isWhiteOilBlock" v-focus-next-on-tab="'98'" ref="97"></a-checkbox>
            </a-form-model-item>
            <a-form-model-item label="字符网板印刷" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <a-checkbox v-model="proOrderInfoDto.isCharacterScreenPrinting" v-focus-next-on-tab="'99'" ref="98"></a-checkbox>
            </a-form-model-item>
            <a-form-model-item label="字符上表面" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <a-checkbox v-model="proOrderInfoDto.isCharacterUpSurface" v-focus-next-on-tab="'100'" ref="99"></a-checkbox>
            </a-form-model-item>
            <a-form-model-item label="二维码方式" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <div>
                <a-select
                  v-model="proOrderInfoDto.qrCodeWay"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.QrCodeWay)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item label="二维码类型" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <div>
                <a-select
                  v-model="proOrderInfoDto.qrCodeType"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.QrCodeType)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item label="二维码要求" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <div>
                <a-select
                  v-model="proOrderInfoDto.qrCodeRequire"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.QrCodeRequire)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item label="二维码阴阳" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <div>
                <a-select
                  v-model="proOrderInfoDto.qrCodeYY"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.QrCodeYY)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <!-- 88 -->
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="干燥剂"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.noAddDesiccant && iseval(requiredLinkConfigpro.noAddDesiccant.isNullRules) ? 'require' : ''"
            >
              <div v-if="editFlg1">
                <a-select
                  v-model="proOrderInfoDto.noAddDesiccant"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'101'"
                  ref="100"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.NoAddDesiccant)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="板间隔纸"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.isBoardSpacer && iseval(requiredLinkConfigpro.isBoardSpacer.isNullRules) ? 'require' : ''"
            >
              <div v-if="editFlg1">
                <a-select
                  v-model="proOrderInfoDto.isBoardSpacer"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'102'"
                  ref="101"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.IsBoardSpacer)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="放湿度卡"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.isHumidityCard && iseval(requiredLinkConfigpro.isHumidityCard.isNullRules) ? 'require' : ''"
            >
              <div v-if="editFlg1">
                <a-select
                  v-model="proOrderInfoDto.isHumidityCard"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'103'"
                  ref="102"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.IsHumidityCard)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="包装要求"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.packagRequire && iseval(requiredLinkConfigpro.packagRequire.isNullRules) ? 'require' : ''"
            >
              <div v-if="editFlg1">
                <a-select
                  v-model="proOrderInfoDto.packagRequire"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'104'"
                  ref="103"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PackagRequire)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
              <span v-else>{{ proOrderInfoDto.packagRequireStr }}</span>
            </a-form-model-item>
            <a-form-model-item
              label="出货报告"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              class="heightSty1"
              :class="requiredLinkConfigpro.needReportList && iseval(requiredLinkConfigpro.needReportList.isNullRules) ? 'require' : ''"
            >
              <div v-if="editFlg1">
                <a-select
                  v-model="proOrderInfoDto.needReportList"
                  mode="multiple"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'105'"
                  ref="104"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.NeedReportList)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
              <span v-else class="edit">{{ proOrderInfoDto.needReportListStr }}</span>
            </a-form-model-item>
            <a-form-model-item
              label="交货方式"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.deliveryMethod && iseval(requiredLinkConfigpro.deliveryMethod.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.deliveryMethod"
                  v-if="!editable"
                  @change="setEstimateb($event, mapKey(selectData.DeliveryMethod))"
                  @search="handleSearchb($event, mapKey(selectData.DeliveryMethod))"
                  @blur="handleBlurb($event, mapKey(selectData.DeliveryMethod))"
                  @dblclick.native="labelClick($event)"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'106'"
                  ref="105"
                  disabled
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.DeliveryMethod)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-input v-if="editable" v-model="proOrderInfoDto.deliveryMethod" allowClear disabled @dblclick.prevent="labelClick($event)" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="包装数量pcs"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.packagingQuantity && iseval(requiredLinkConfigpro.packagingQuantity.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.packagingQuantity" allowClear v-focus-next-on-tab="'107'" ref="106" @blur="truncationpq()">
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="备品数量pcs"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.spareQuantity && iseval(requiredLinkConfigpro.spareQuantity.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.spareQuantity" allowClear v-focus-next-on-tab="'108'" ref="107" @blur="truncationsq()"> </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item label="附出货菲林" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
              <div>
                <a-checkbox v-if="editFlg1" v-model="proOrderInfoDto.attachedShippingFilm" v-focus-next-on-tab="'109'" ref="108"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <!-- 94 -->
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="盘中孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isDiscHole ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isDiscHole" v-focus-next-on-tab="'110'" ref="109" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="半孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="
                (showCG && show2) ||
                (showCG && showMore) ||
                (showHDI && showMore) ||
                (showMM && showMore) ||
                (this.$route.query.factory == '12' && this.proOrderInfoDto.boardLayers == 1)
              "
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isHalfHole ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isHalfHole" v-focus-next-on-tab="'111'" ref="110"> </a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="异形孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="
                (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
              "
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isProfileHole ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isProfileHole" v-focus-next-on-tab="'112'" ref="111"> </a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="板边包金"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isPlateEdge ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isPlateEdge" v-focus-next-on-tab="'113'" ref="112"> </a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="沉孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="
                (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
              "
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.stepHole ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.stepHole" v-focus-next-on-tab="'114'" ref="113"> </a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="印序列号"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="
                (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
              "
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isSerialNumber ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isSerialNumber" v-focus-next-on-tab="'115'" ref="114" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="蓝胶"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isBlueGum ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isBlueGum" v-focus-next-on-tab="'116'" ref="115" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="埋铜"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.buriedCopper ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.buriedCopper" v-focus-next-on-tab="'117'" ref="116" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="埋阻"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.buriedResistance ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.buriedResistance" v-focus-next-on-tab="'118'" ref="117" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="铜浆塞孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.cuPlugHole ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.cuPlugHole" v-focus-next-on-tab="'119'" ref="118"> </a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="阻抗"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore) || (showCG && show1)"
            >
              <div>
                <a-checkbox v-model="proOrderInfoDto.isImpedance" v-focus-next-on-tab="'120'" ref="119" @change="impchange"> </a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="压接孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isCrimpHole ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isCrimpHole" v-focus-next-on-tab="'121'" ref="120" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="背钻孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isBackDrilling ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isBackDrilling" v-focus-next-on-tab="'122'" ref="121" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="通孔控深"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isThroughHoleControl ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isThroughHoleControl" v-focus-next-on-tab="'123'" ref="122" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="盲孔控深"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isBlindHoleControl ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isBlindHoleControl" v-focus-next-on-tab="'124'" ref="123" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="阶梯孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="
                (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
              "
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.steppedHole ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.steppedHole" v-focus-next-on-tab="'125'" ref="124"> </a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="碳油"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isCarbonOil ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isCarbonOil" v-focus-next-on-tab="'126'" ref="125"></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="高温胶"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.heatTape ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.heatTape" v-focus-next-on-tab="'127'" ref="126"></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="斜边"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.goldfingerBevel && iseval(requiredLinkConfigpro.goldfingerBevel.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.goldfingerBevelStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.goldfingerBevel"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'128'"
                  ref="127"
                  @change="changxb"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.GoldfingerBevel)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="盲槽"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="
                (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
              "
            >
              <span v-if="!editFlg1"
                ><span v-if="proOrderInfoDto.isBlindSlot">{{ proOrderInfoDto.isBlindSlot ? "是" : "" }}</span></span
              >
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isBlindSlot" v-focus-next-on-tab="'129'" ref="128" />
              </div>
            </a-form-model-item>

            <a-form-model-item
              label="银浆塞孔"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <div>
                <a-checkbox v-model="proOrderInfoDto.silverPlugHole" v-focus-next-on-tab="'130'" ref="129" />
              </div>
            </a-form-model-item>
            <a-form-model-item label="跳V" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div>
                <a-checkbox
                  v-model="proOrderInfoDto.jumpCut"
                  v-if="proOrderInfoDto.formingType == 'machinemold+vcut' || proOrderInfoDto.formingType == 'vcut'"
                  v-focus-next-on-tab="'131'"
                  ref="130"
                ></a-checkbox>
                <a-checkbox v-model="proOrderInfoDto.jumpCut" v-else disabled></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item label="局部镀厚金" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.isPartPlatingThickGold" v-focus-next-on-tab="'132'" ref="131"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="金属铣槽" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.isMetalSlot" v-focus-next-on-tab="'133'" ref="132" @change="MetalSlotChange"></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="金手指"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="requiredLinkConfigpro.goldenfinger && iseval(requiredLinkConfigpro.goldenfinger.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.goldenfinger"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'134'"
                  ref="133"
                  @change="goldenfingerChange"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.Goldenfinger)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label='金手指金厚U"'
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="requiredLinkConfigpro.goldFingerThickness && iseval(requiredLinkConfigpro.goldFingerThickness.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input
                  v-model="proOrderInfoDto.goldFingerThickness"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :disabled="proOrderInfoDto.goldenfinger ? false : true"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'135'"
                  ref="134"
                  @blur="truncationgft()"
                >
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              :label="'金手指镍厚' + proOrderInfoDto.goldfingerNieThicknessUnit"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="
                requiredLinkConfigpro.goldfingerNickelThickness && iseval(requiredLinkConfigpro.goldfingerNickelThickness.isNullRules)
                  ? 'require'
                  : ''
              "
            >
              <div>
                <a-input
                  v-model="proOrderInfoDto.goldfingerNickelThickness"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :disabled="proOrderInfoDto.goldenfinger ? false : true"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'136'"
                  ref="135"
                  @blur="truncationgnt()"
                >
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="金手指斜边角度"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="requiredLinkConfigpro.goldfingerBevelAngle && iseval(requiredLinkConfigpro.goldfingerBevelAngle.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input
                  v-model="proOrderInfoDto.goldfingerBevelAngle"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :disabled="proOrderInfoDto.goldfingerBevel ? false : true"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'137'"
                  ref="136"
                  @blur="truncationgfba()"
                >
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="金手指斜边深度"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="requiredLinkConfigpro.goldfingerBevelDepth && iseval(requiredLinkConfigpro.goldfingerBevelDepth.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input
                  v-model="proOrderInfoDto.goldfingerBevelDepth"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :disabled="proOrderInfoDto.goldfingerBevel ? false : true"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'138'"
                  ref="137"
                  @blur="truncationgfbd()"
                >
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="金手指斜边余厚"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="
                requiredLinkConfigpro.goldfingerBevelSurplus && iseval(requiredLinkConfigpro.goldfingerBevelSurplus.isNullRules) ? 'require' : ''
              "
            >
              <div>
                <a-input
                  v-model="proOrderInfoDto.goldfingerBevelSurplus"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :disabled="proOrderInfoDto.goldfingerBevel ? false : true"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'139'"
                  ref="138"
                  @blur="truncationgfbs()"
                >
                </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item label="去独立PAD" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.deSinglePad" v-focus-next-on-tab="'140'" ref="139" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="最小孔铜"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="requiredLinkConfigpro.minHoleCopper && iseval(requiredLinkConfigpro.minHoleCopper.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.minHoleCopper" allowClear v-focus-next-on-tab="'141'" ref="140" @blur="truncationmhc()"></a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="平均孔铜"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="requiredLinkConfigpro.avgHoleCopper && iseval(requiredLinkConfigpro.avgHoleCopper.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.avgHoleCopper" allowClear v-focus-next-on-tab="'142'" ref="141" @blur="truncationgahc()"></a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="光板数"
              :label-col="{ span: 13 }"
              :wrapper-col="{ span: 11 }"
              :class="requiredLinkConfigpro.gbNum && iseval(requiredLinkConfigpro.gbNum.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.gbNum" allowClear v-focus-next-on-tab="'143'" ref="142" @blur="truncationgahc()"></a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item label="对压" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.counterPressure"></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item label="水印" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.waterMark_"></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item label="外层泪滴" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.outerLineTeardrop" v-focus-next-on-tab="'140'" ref="139" />
              </div>
            </a-form-model-item>
            <a-form-model-item label="内层泪滴" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.innerLineTeardrop" v-focus-next-on-tab="'140'" ref="139" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div class="autoo">
          <a-row>
            <a-col :span="20">
              <a-form-model-item
                label="工程指示"
                :label-col="{ span: 2 }"
                :wrapper-col="{ span: 22 }"
                style="border-top: 1px solid #ddd"
                class="speclass"
              >
                <span>{{ proOrderInfoDto.specialRemarks }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="20">
              <a-form-model-item
                label="客户备注"
                :label-col="{ span: 2 }"
                :wrapper-col="{ span: 22 }"
                style="border-top: 1px solid #ddd"
                class="speclass"
              >
                <span>{{ proOrderInfoDto.note }}</span>
                <!-- <div  v-else >
              <a-textarea :auto-size="{ minRows: 1, maxRows: 2 }" v-model="proOrderInfoDto.note" allowClear disabled> </a-textarea>
            </div>             -->
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
      </a-form-model>
    </div>
  </a-spin>
</template>
<script>
import { productInformation, soldercolorresistinkrelation, fontcolorresistinkrelation, getvcutselect } from "@/services/projectIndicate";
import $ from "jquery";
let index = -1;
export default {
  name: "ProductInfo",
  props: [
    "proOrderInfoDto",
    "selectData",
    "factoryData",
    "editFlg1",
    "boardBrandList",
    "sheetTraderList",
    "messageList",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "requiredLinkConfigpro",
    "boardtgList",
    "ManufacturerTG",
  ],
  inject: ["reload"],
  data() {
    return {
      // show0:false,
      // show1:false,
      // show2:false,
      // showMore:false,
      // showCG:false,
      // showHDI:false,
      // showMM:false,
      Vcutdata: [],
      ymdata: [],
      SolderResistInk: [],
      SolderResistInk1: [],
      CharacterResistInk: [],
      CharacterResistInk1: [],
      spinning: false,
      prohibit: false,
      prohibit1: false,
      copyOld: "",
      copyNewVal: "",
      selectData1: {},
      selectValue: "",
      index: -1,
      sheetTrader: [],
      boardBrand: [],
      selectedValue: "",
      editValue: "",
      editable: false,
      options: [
        { value: "option1", label: "Option 1" },
        { value: "option2", label: "Option 2" },
        { value: "option3", label: "Option 3" },
      ],
      // showData: {
      //   orderNo: "",
      //   businessOrderNo: "",
      //   joinFactoryId: 0,
      //   joinFactoryIdStr: "",
      //   createTime: "",
      //   deliveryType: "",
      //   deliveryDays: 0,
      //   deliveryDate: "",
      //   isReOrder: 0,
      //   isReOrderStr: "",
      //   status: 0,
      //   statusStr: 0,
      //   proAdminAcount: "",
      //   proAdminName: "",
      //   verifyAccount: "",
      //   verifyName: "",
      //   camCheckAdminAccount: "",
      //   camCheckName: "",
      //   boardLayers: 0,
      //   boardThickness: 0,
      //   boardThicknessTol: "",
      //   pinBanNum: 0,
      //   boardWidth: 0,
      //   boardHeight: 0,
      //   boardType: "",
      //   pinBanType: "",
      //   processEdges: "",
      //   grooveHeight: 0,
      //   grooveWidth: 0,
      //   boardArea: 0,
      //   customerMaterialNo: "",
      //   ipcLevel: 0,
      //   ipcLevelStr: "",
      //   flyingProbe: "",
      //   flyingProbeStr: "",
      //   isStamping: false,
      //   isLowResistanceTest: false,
      //   isInductanceTest: false,
      //   pcbFileName: "",
      //   pcbFilePath: "",
      //   camFileName: "",
      //   camFilePath: "",
      //   isHighVoltageTest: false,
      //   changeItemNum: false,
      //   isFeed: false,
      //   changePeriod: false,
      //   isOutFactory: false
      // },
    };
  },
  mounted() {
    setTimeout(() => {
      this.getsolddata();
      this.getfontdata();
    }, 1000);
    this.sheetTrader = this.sheetTraderList;
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  watch: {
    messageList: {
      handler(val) {
        console.log("val", val);
        this.get(val);
      },
    },
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    "proOrderInfoDto.boardThickness"(newVal, oldVal) {
      setTimeout(() => {
        if (newVal != oldVal && newVal && oldVal) {
          this.copyOld = oldVal;
          this.copyNewVal = newVal;
          //this.changemm()
        }
      }, 700);
    },
  },
  methods: {
    impchange() {
      this.proOrderInfoDto.isChangeLayerPres = this.proOrderInfoDto.isImpedance;
    },
    changeType() {
      this.proOrderInfoDto.fR4Tg = null;
      let fR4Tg = [];
      if (this.boardtgList.length) {
        fR4Tg = this.proOrderInfoDto.boardBrand
          ? this.boardtgList.filter(item => {
              return item.valueMember1 == this.proOrderInfoDto.boardBrand;
            })
          : [];
      }
      if (!this.proOrderInfoDto.fR4Tg && fR4Tg.length) {
        this.proOrderInfoDto.fR4Tg = fR4Tg[0].valueMember;
      }
      if (this.ManufacturerTG.length) {
        let data = this.proOrderInfoDto.boardBrand
          ? this.ManufacturerTG.filter(item => {
              return item.coreType_ == this.proOrderInfoDto.boardBrand;
            })
          : [];
        if (data.length) {
          this.proOrderInfoDto.fR4Tg = data[0].tgValue;
          this.proOrderInfoDto.sheetTrader = data[0].verdorName_Value;
        }
      }
      if (this.boardBrandList.length && this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.proOrderInfoDto.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("val") != -1) {
        newIsNullRules = newIsNullRules.replace(/val/g, "this.proOrderInfoDto");
      }
      return eval(newIsNullRules);
    },
    handleResize() {
      var boxstyle = document.getElementsByClassName("box")[0];
      console.log("boxstyle.style.height", boxstyle, window.innerHeight);
      boxstyle.style.height = window.innerHeight - 140 + "px";
    },
    truncationpb1() {
      if (this.proOrderInfoDto.pinBanType1) {
        var pinBanType1 = this.proOrderInfoDto.pinBanType1.split("");
        if (pinBanType1.length > 3) {
          pinBanType1 = pinBanType1.slice(0, 3);
          this.$message.warning("拼版方式不能超过3个字符");
        }
        this.proOrderInfoDto.pinBanType1 = pinBanType1.join("");
        this.changesu();
      }
    },
    truncationpb2() {
      if (this.proOrderInfoDto.pinBanType2) {
        var pinBanType2 = this.proOrderInfoDto.pinBanType2.split("");
        if (pinBanType2.length > 3) {
          pinBanType2 = pinBanType2.slice(0, 3);
          this.$message.warning("拼版方式不能超过3个字符");
        }
        this.proOrderInfoDto.pinBanType2 = pinBanType2.join("");
        this.changesu();
      }
    },
    truncationbh() {
      if (this.proOrderInfoDto.boardHeight) {
        var boardHeight = this.proOrderInfoDto.boardHeight.toString().split("");
        if (boardHeight.length > 8) {
          boardHeight = boardHeight.slice(0, 8);
          this.$message.warning("单元长不能超过8个字符");
        }
        this.proOrderInfoDto.boardHeight = boardHeight.join("");
      }
    },
    truncationbw() {
      if (this.proOrderInfoDto.boardWidth) {
        var boardWidth = this.proOrderInfoDto.boardWidth.toString().split("");
        if (boardWidth.length > 8) {
          boardWidth = boardWidth.slice(0, 8);
          this.$message.warning("单元宽不能超过8个字符");
        }
        this.proOrderInfoDto.boardWidth = boardWidth.join("");
      }
    },
    truncationbhs() {
      if (this.proOrderInfoDto.boardHeightSet) {
        var boardHeightSet = this.proOrderInfoDto.boardHeightSet.toString().split("");
        if (boardHeightSet.length > 8) {
          boardHeightSet = boardHeightSet.slice(0, 8);
          this.$message.warning("成品长不能超过8个字符");
        }
        this.proOrderInfoDto.boardHeightSet = boardHeightSet.join("");
      }
    },
    truncationbws() {
      if (this.proOrderInfoDto.boardWidthSet) {
        var boardWidthSet = this.proOrderInfoDto.boardWidthSet.toString().split("");
        if (boardWidthSet.length > 8) {
          boardWidthSet = boardWidthSet.slice(0, 8);
          this.$message.warning("成品宽不能超过8个字符");
        }
        this.proOrderInfoDto.boardWidthSet = boardWidthSet.join("");
      }
    },
    truncationbpbn() {
      if (this.proOrderInfoDto.pinBanNum) {
        var pinBanNum = this.proOrderInfoDto.pinBanNum.toString().split("");
        if (pinBanNum.length > 3) {
          pinBanNum = pinBanNum.slice(0, 3);
          this.$message.warning("合拼款数不能超过3个字符");
        }
        this.proOrderInfoDto.pinBanNum = pinBanNum.join("");
      }
    },
    truncationspa() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.platedArea) {
        var platedArea = this.proOrderInfoDto.surfaceFinishJsonDto.platedArea.split("");
        if (platedArea.length > 5) {
          platedArea = platedArea.slice(0, 5);
          this.$message.warning("表面处理面积不能超过5个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.platedArea = platedArea.join("");
      }
    },
    truncationspa2() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.platedArea2) {
        var platedArea2 = this.proOrderInfoDto.surfaceFinishJsonDto.platedArea2.split("");
        if (platedArea2.length > 5) {
          platedArea2 = platedArea2.slice(0, 5);
          this.$message.warning("表面处理面积不能超过5个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.platedArea2 = platedArea2.join("");
      }
    },
    truncationsft() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.filmThickness) {
        var filmThickness = this.proOrderInfoDto.surfaceFinishJsonDto.filmThickness.split("");
        if (filmThickness.length > 10) {
          filmThickness = filmThickness.slice(0, 10);
          this.$message.warning("表面处理膜厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.filmThickness = filmThickness.join("");
      }
    },
    truncationsnst() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness) {
        var newSilverThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness.split("");
        if (newSilverThickness.length > 10) {
          newSilverThickness = newSilverThickness.slice(0, 10);
          this.$message.warning("表面处理膜厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness = newSilverThickness.join("");
      }
    },
    truncationspt() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.paThickness) {
        var paThickness = this.proOrderInfoDto.surfaceFinishJsonDto.paThickness.split("");
        if (paThickness.length > 10) {
          paThickness = paThickness.slice(0, 10);
          this.$message.warning("表面处理镍钯厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.paThickness = paThickness.join("");
      }
    },
    truncationsu() {
      if (this.proOrderInfoDto.sheetUtilization) {
        var sheetUtilization = this.proOrderInfoDto.sheetUtilization.split("");
        if (sheetUtilization.length > 5) {
          sheetUtilization = sheetUtilization.slice(0, 5);
          this.$message.warning("板材利用率不能超过5个字符");
        }
        this.proOrderInfoDto.sheetUtilization = sheetUtilization.join("");
      }
    },
    truncationst() {
      if (this.proOrderInfoDto.solderThickness) {
        var solderThickness = this.proOrderInfoDto.solderThickness.split("");
        if (solderThickness.length > 10) {
          solderThickness = solderThickness.slice(0, 10);
          this.$message.warning("基材油墨厚度不能超过10个字符");
        }
        this.proOrderInfoDto.solderThickness = solderThickness.join("");
      }
    },
    truncationflt() {
      if (this.proOrderInfoDto.footLineInkThickness) {
        var footLineInkThickness = this.proOrderInfoDto.footLineInkThickness.split("");
        if (footLineInkThickness.length > 10) {
          footLineInkThickness = footLineInkThickness.slice(0, 10);
          this.$message.warning("线角油墨厚度不能超过10个字符");
        }
        this.proOrderInfoDto.footLineInkThickness = footLineInkThickness.join("");
      }
    },
    truncationcit() {
      if (this.proOrderInfoDto.cuInkThickness) {
        var cuInkThickness = this.proOrderInfoDto.cuInkThickness.split("");
        if (cuInkThickness.length > 10) {
          cuInkThickness = cuInkThickness.slice(0, 10);
          this.$message.warning("铜面油墨厚度不能超过10个字符");
        }
        this.proOrderInfoDto.cuInkThickness = cuInkThickness.join("");
      }
    },
    truncationsit() {
      if (this.proOrderInfoDto.solderInkThickness) {
        var solderInkThickness = this.proOrderInfoDto.solderInkThickness.split("");
        if (solderInkThickness.length > 10) {
          solderInkThickness = solderInkThickness.slice(0, 10);
          this.$message.warning("阻焊厚度不能超过10个字符");
        }
        this.proOrderInfoDto.solderInkThickness = solderInkThickness.join("");
      }
    },
    truncationpq() {
      if (this.proOrderInfoDto.packagingQuantity) {
        var packagingQuantity = this.proOrderInfoDto.packagingQuantity.split("");
        if (packagingQuantity.length > 5) {
          packagingQuantity = packagingQuantity.slice(0, 5);
          this.$message.warning("包装数量不能超过5个字符");
        }
        this.proOrderInfoDto.packagingQuantity = packagingQuantity.join("");
      }
    },
    truncationsq() {
      if (this.proOrderInfoDto.spareQuantity) {
        var spareQuantity = this.proOrderInfoDto.spareQuantity.split("");
        if (spareQuantity.length > 5) {
          spareQuantity = spareQuantity.slice(0, 5);
          this.$message.warning("备品数量不能超过5个字符");
        }
        this.proOrderInfoDto.spareQuantity = spareQuantity.join("");
      }
    },
    truncationgft() {
      if (this.proOrderInfoDto.goldFingerThickness) {
        var goldFingerThickness = this.proOrderInfoDto.goldFingerThickness.split("");
        if (goldFingerThickness.length > 10) {
          goldFingerThickness = goldFingerThickness.slice(0, 10);
          this.$message.warning("金手指金厚不能超过10个字符");
        }
        this.proOrderInfoDto.goldFingerThickness = goldFingerThickness.join("");
      }
    },
    truncationgnt() {
      if (this.proOrderInfoDto.goldfingerNickelThickness) {
        var goldfingerNickelThickness = this.proOrderInfoDto.goldfingerNickelThickness.split("");
        if (goldfingerNickelThickness.length > 10) {
          goldfingerNickelThickness = goldfingerNickelThickness.slice(0, 10);
          this.$message.warning("金手指镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerNickelThickness = goldfingerNickelThickness.join("");
      }
    },
    truncationgfba() {
      if (this.proOrderInfoDto.goldfingerBevelAngle) {
        var goldfingerBevelAngle = this.proOrderInfoDto.goldfingerBevelAngle.split("");
        if (goldfingerBevelAngle.length > 10) {
          goldfingerBevelAngle = goldfingerBevelAngle.slice(0, 10);
          this.$message.warning("金手指斜边角度不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerBevelAngle = goldfingerBevelAngle.join("");
      }
    },
    truncationgfbd() {
      if (this.proOrderInfoDto.goldfingerBevelDepth) {
        var goldfingerBevelDepth = this.proOrderInfoDto.goldfingerBevelDepth.split("");
        if (goldfingerBevelDepth.length > 10) {
          goldfingerBevelDepth = goldfingerBevelDepth.slice(0, 10);
          this.$message.warning("金手指斜边深度不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerBevelDepth = goldfingerBevelDepth.join("");
      }
    },
    truncationgfbs() {
      if (this.proOrderInfoDto.goldfingerBevelSurplus) {
        var goldfingerBevelSurplus = this.proOrderInfoDto.goldfingerBevelSurplus.split("");
        if (goldfingerBevelSurplus.length > 10) {
          goldfingerBevelSurplus = goldfingerBevelSurplus.slice(0, 10);
          this.$message.warning("金手指斜边余厚不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerBevelSurplus = goldfingerBevelSurplus.join("");
      }
    },
    truncationmhc() {
      if (this.proOrderInfoDto.minHoleCopper) {
        var minHoleCopper = this.proOrderInfoDto.minHoleCopper.split("");
        if (minHoleCopper.length > 4) {
          minHoleCopper = minHoleCopper.slice(0, 4);
          this.$message.warning("最小孔铜不能超过4个字符");
        }
        this.proOrderInfoDto.minHoleCopper = minHoleCopper.join("");
      }
    },
    truncationgahc() {
      if (this.proOrderInfoDto.avgHoleCopper) {
        var avgHoleCopper = this.proOrderInfoDto.avgHoleCopper.split("");
        if (avgHoleCopper.length > 4) {
          avgHoleCopper = avgHoleCopper.slice(0, 4);
          this.$message.warning("平均孔铜不能超过4个字符");
        }
        this.proOrderInfoDto.avgHoleCopper = avgHoleCopper.join("");
      }
    },
    truncationtfn() {
      if (this.proOrderInfoDto.testFixtureNumber) {
        var testFixtureNumber = this.proOrderInfoDto.testFixtureNumber.split("");
        if (testFixtureNumber.length > 20) {
          testFixtureNumber = testFixtureNumber.slice(0, 20);
          this.$message.warning("测试治具编号不能超过20个字符");
        }
        this.proOrderInfoDto.testFixtureNumber = testFixtureNumber.join("");
      }
    },
    setStyle() {
      const elements = document.getElementsByClassName("divitem");
      const num = elements.length;
      for (var a = 0; a < elements.length; a++) {
        if (a < 24) {
          elements[a].style.width = "535px";
          elements[a].childNodes[0].style.width = "123px";
          elements[a].childNodes[1].style.width = "412px";
        } else {
          elements[a].style.width = "310px";
          elements[a].childNodes[0].style.width = "140px";
          elements[a].childNodes[1].style.width = "170px";
        }
      }
      var div2 = document.getElementsByClassName("div2")[0];
      if (div2) {
        if (num <= 24) {
          div2.style.width = "535px";
        } else if (num <= 48) {
          div2.style.width = "836px";
        } else if (num <= 72) {
          div2.style.width = "1137px";
        } else {
          div2.style.width = "94.95%";
        }
      }
      var div22 = document.getElementsByClassName("div22");
      for (var i = 0; i < div22.length; i++) {
        if (div2.style.width == "535px") {
          div22[i].childNodes[0].style.width = "123px";
          div22[i].childNodes[1].style.width = "412px";
        } else if (div2.style.width == "836px") {
          div22[i].childNodes[0].style.width = "123px";
          div22[i].childNodes[1].style.width = "713px";
        } else if (div2.style.width == "1137px") {
          div22[i].childNodes[0].style.width = "123px";
          div22[i].childNodes[1].style.width = "1014px";
        } else {
          div22[i].childNodes[0].style.width = "8.35%";
          div22[i].childNodes[1].style.width = "91.65%";
        }
      }
    },
    editClick1() {
      this.editFlg1 = !this.editFlg1;
    },
    changemm() {
      this.$emit("changemm");
    },
    changeSheet(val) {
      if (val) {
        this.proOrderInfoDto.sheetTrader = val;
      }
      if (this.boardBrandList.length && this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.proOrderInfoDto.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
      this.proOrderInfoDto.boardBrand = "";
    },
    // saveClick(){
    //   if(!this.editFlg1){
    //     this.$message.warning('非编辑状态不可保存')
    //     return
    //   }
    //   let params = this.proOrderInfoDto
    //   productInformation(params).then(res=>{
    //     if(res.code){
    //       this.$emit('GetProOrderInfo')
    //       this.$message.success('保存成功')
    //       this.editFlg1 = false
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    // 下载客户文件
    // dwon1(val){
    //   if(val){
    //     window.location.href = val
    //   }else{
    //     this.$message.error('客户文件不存在')
    //   }
    // },
    // 下载工程文件
    dwon2(val) {
      if (val) {
        window.location.href = val;
      } else {
        this.$message.error("工程文件不存在");
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    // 板厚更改 板厚公差=+/-板厚*0.1
    boardThickness() {
      this.$emit("boardThickness");
      if (this.proOrderInfoDto.boardThickness) {
        if (this.proOrderInfoDto.boardThickness <= 1.0) {
          this.proOrderInfoDto.boardThicknessTol = "+/-0.1";
        } else {
          this.proOrderInfoDto.boardThicknessTol = "+/-" + (this.proOrderInfoDto.boardThickness * 0.1).toFixed(2);
        }
      }
    },
    changxb() {
      if (!this.proOrderInfoDto.goldfingerBevel) {
        this.proOrderInfoDto.goldfingerBevelAngle = null;
        this.proOrderInfoDto.goldfingerBevelDepth = null;
        this.proOrderInfoDto.goldfingerBevelSurplus = null;
      }
    },
    goldenfingerChange() {
      if (!this.proOrderInfoDto.goldenfinger) {
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },
    MetalSlotChange() {
      if (this.$route.query.factory == 12) {
        if (this.proOrderInfoDto.isMetalSlot) {
          this.proOrderInfoDto.cncHoleTol = "+/-0.075";
        } else {
          this.proOrderInfoDto.cncHoleTol = "";
        }
      }
    },
    // 拼版方式更改
    pinBanType() {
      if (this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1) {
        if (this.proOrderInfoDto.boardType == "pcs") {
          this.$message.warning("拼版方式大于1x1,出货单位请选择set");
        }
      }
      // if(this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1 ){
      //   this.proOrderInfoDto.boardType = 'set'
      // }else{
      //   this.proOrderInfoDto.boardType = 'pcs'
      // }
    },
    //
    flyChange() {
      if (this.proOrderInfoDto.flyingProbe == "FlyingProbe") {
        this.proOrderInfoDto.fpTestMethod = "capacitance";
        this.proOrderInfoDto.testFixtureNumber = "";
      }
      if (
        this.proOrderInfoDto.flyingProbe == "custstand" ||
        this.proOrderInfoDto.flyingProbe == "newstand" ||
        this.proOrderInfoDto.flyingProbe == "sharestand" ||
        this.proOrderInfoDto.flyingProbe == "teststand"
      ) {
        this.proOrderInfoDto.testFixtureNumber = this.proOrderInfoDto.orderNo;
        this.proOrderInfoDto.fpTestMethod = "";
      }
    },
    changeplugoil() {
      if (
        this.proOrderInfoDto.solderCover == "plugoil" ||
        this.proOrderInfoDto.solderCover == "bgaplugoil" ||
        this.proOrderInfoDto.solderCover == "aluminiumplugoil" ||
        this.proOrderInfoDto.solderCover == "openwindowplusplugoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+converoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+openminwindow"
      ) {
        if (this.$route.query.factory == 38) {
          this.proOrderInfoDto.plugOilTool = "consentprinting";
        } else {
          this.proOrderInfoDto.plugOilTool = "aluminumsheet";
        }
        if (this.$route.query.factory != 58 && this.$route.query.factory != 59) {
          this.proOrderInfoDto.plugOilSide = "toplayer";
        }
      } else {
        this.proOrderInfoDto.plugOilTool = null;
        this.proOrderInfoDto.plugOilSide = null;
      }
    },
    changevcut() {
      if (this.proOrderInfoDto.formingType != "vcut" || this.proOrderInfoDto.formingType != "machinemold+vcut") {
        this.proOrderInfoDto.vCut = null;
        this.proOrderInfoDto.jumpCut = false;
        this.proOrderInfoDto.vcutSurplusThickness = null;
        this.proOrderInfoDto.vcutSurplusThicknessTol = null;
        this.proOrderInfoDto.vcutAngle = null;
        this.proOrderInfoDto.vcutAngleTol = null;
        this.proOrderInfoDto.vCutKnifeNum = null;
      }
      if (
        this.proOrderInfoDto.formingType == "vcut" ||
        this.proOrderInfoDto.formingType == "machinemold+vcut" ||
        this.proOrderInfoDto.formingType == "vcut+rtr" ||
        this.proOrderInfoDto.formingType == "mechanical_milling+vcut"
      ) {
        this.proOrderInfoDto.vCut = "ncvcut";
        this.proOrderInfoDto.vcutAngle = "30";
        this.proOrderInfoDto.vcutAngleTol = "1";
        let value = "";
        let Thickness = Number(this.proOrderInfoDto.boardThickness);
        if (this.$route.query.factory == "12") {
          if (Thickness >= 0.4 && Thickness <= 0.8) {
            value = "0.25";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          } else if (Thickness > 0.8 && Thickness <= 1.0) {
            value = "0.30";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          } else if (Thickness > 1 && Thickness <= 1.4) {
            value = "0.35";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          } else if (Thickness > 1.4 && Thickness <= 1.8) {
            value = "0.4";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          } else if (Thickness > 1.8 && Thickness <= 2.2) {
            value = "0.6";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          } else if (Thickness > 2.2) {
            value = "0.75";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          }
        } else if (this.$route.query.factory == "69") {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          if (Thickness == 2) {
            value = "0.5";
          } else if (Thickness == 1.6) {
            value = "0.4";
          } else if (Thickness == 1.2) {
            value = "0.35";
          } else if (Thickness == 1.0) {
            value = "0.3";
          } else if (Thickness == 0.6 || Thickness == 0.8) {
            value = "0.25";
          } else if (Thickness > 1) {
            value = (Thickness / 4).toFixed(2);
          } else if (Thickness < 1) {
            value = (Thickness / 3).toFixed(2);
          }
        } else if (this.$route.query.factory == "22") {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          if (this.proOrderInfoDto.custNo.indexOf("681") > -1) {
            if (Thickness > 0.6 && Thickness <= 1) {
              value = "0.35";
            } else if (Thickness > 1) {
              value = "0.4";
            }
          } else {
            if (Thickness >= 0.4 && Thickness <= 0.6) {
              value = "0.25";
            } else if (Thickness > 0.6 && Thickness <= 1.1) {
              value = "0.3";
            } else if (Thickness > 1.1 && Thickness <= 1.59) {
              value = "0.35";
            } else if (Thickness >= 1.6) {
              value = "0.4";
            }
          }
        } else if (this.$route.query.factory == "58" || this.$route.query.factory == "59") {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "";
          this.proOrderInfoDto.vcutAngle = "25";
          value = "";
          if (this.proOrderInfoDto.fR4Type == "fr4") {
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
            if (Thickness >= 0.4 && Thickness < 0.6) {
              value = "0.15";
              this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
            } else if (Thickness >= 0.6 && Thickness < 0.8) {
              value = "0.2";
              this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
            } else if (Thickness >= 0.8 && Thickness < 1) {
              value = "0.25";
            } else if (Thickness >= 1.0 && Thickness < 1.2) {
              value = "0.3";
            } else if (Thickness >= 1.2 && Thickness < 1.6) {
              value = "0.35";
            } else if (Thickness >= 1.6 && Thickness < 2) {
              value = "0.4";
            } else if (Thickness >= 2 && Thickness < 2.5) {
              value = "0.5";
            } else if (Thickness >= 2.5 && Thickness < 3) {
              value = "0.6";
            } else if (Thickness >= 3) {
              value = "0.7";
            }
          }
        } else {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          if (Thickness < 0.6) {
            value = "0.2";
          } else if (Thickness >= 0.6 && Thickness <= 0.8) {
            value = "0.25";
          } else if (Thickness > 0.8 && Thickness < 1.2) {
            value = "0.28";
          } else if (Thickness >= 1.2 && Thickness < 1.6) {
            value = "0.35";
          } else if (Thickness >= 1.6) {
            value = "0.45";
          }
        }
        this.setEstimate5(value, this.mapKey(this.selectData.VcutSurplusThickness));
      }
    },
    changemark() {
      if (!this.proOrderInfoDto.markPosition) {
        this.proOrderInfoDto.ulType = [];
        this.proOrderInfoDto.markType = [];
        this.proOrderInfoDto.markFace = null;
        this.proOrderInfoDto.periodicFormat = null;
      }
      if (!this.proOrderInfoDto.markPosition || this.proOrderInfoDto.markPosition == null || this.proOrderInfoDto.markPosition == "") {
        this.prohibit = true;
      } else {
        this.prohibit = false;
      }
    },
    changegold() {
      if (!this.proOrderInfoDto.isGoldfinger) {
        this.prohibit1 = true;
      } else {
        this.prohibit1 = false;
      }
      if (!this.proOrderInfoDto.isGoldfinger) {
        this.proOrderInfoDto.goldenFingerAreaRe = null;
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },

    changesurface() {
      if (
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandhaslwithfree" ||
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandimmersiongold" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithfreeandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithleadandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "nickelplatingandgoldplatedfinger" ||
        this.proOrderInfoDto.surfaceFinish == "ospandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "wholeimmersiongoldandimmersiongoldfinger"
      ) {
        this.proOrderInfoDto.isGoldfinger = true;
      } else {
        this.proOrderInfoDto.isGoldfinger = false;
      }
    },
    changelayers() {
      this.$emit("changelayers");
    },
    changesu() {
      if (this.proOrderInfoDto.pinBanType1 && this.proOrderInfoDto.pinBanType2) {
        this.proOrderInfoDto.su = (Number(this.proOrderInfoDto.pinBanType1) * this.proOrderInfoDto.pinBanType2).toFixed();
      } else {
        this.proOrderInfoDto.su = null;
      }
    },
    optionClick() {},
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate(value, list) {
      this.proOrderInfoDto.boardThickness = value;
      this.changevcut();
    },

    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
      if (this.proOrderInfoDto.boardThickness) {
        var boardThickness = this.proOrderInfoDto.boardThickness.split("");
        if (boardThickness.length > 6) {
          boardThickness = boardThickness.slice(0, 6);
          this.$message.warning("成品板厚不能超过6个字符");
        }
        this.proOrderInfoDto.boardThickness = boardThickness.join("");
      }
    },

    setEstimateb(value, list) {
      this.proOrderInfoDto.deliveryMethod = value;
    },
    handleBlurb(value, list) {
      this.setEstimateb(value, list);
    },
    handleSearchb(value, list) {
      this.setEstimateb(value, list);
    },
    labelClick(e) {
      console.log("dianji", e);
      this.editable = !this.editable;
    },

    setEstimate1(value, list) {
      this.proOrderInfoDto.boardLayers = value;
      let a = this.getPrice(this.proOrderInfoDto.boardLayers, list, value);
      this.changelayers();
    },
    handleSearch1(value, list) {
      this.setEstimate1(value, list);
    },
    handleBlur1(value, list) {
      this.setEstimate1(value, list);
      if (this.proOrderInfoDto.boardLayers) {
        var boardLayers = this.proOrderInfoDto.boardLayers.split("");
        if (boardLayers.length > 2) {
          boardLayers = boardLayers.slice(0, 2);
          this.$message.warning("层数不能超过2个字符");
        }
        this.proOrderInfoDto.boardLayers = boardLayers.join("");
      }
    },
    setEstimate2(value, list) {
      this.proOrderInfoDto.innerCopperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.innerCopperThickness, list, value);
    },
    handleSearch2(value, list) {
      this.setEstimate2(value, list);
    },
    handleBlur2(value, list) {
      this.setEstimate2(value, list);
      if (this.proOrderInfoDto.innerCopperThickness) {
        var innerCopperThickness = this.proOrderInfoDto.innerCopperThickness.split("");
        if (innerCopperThickness.length > 3) {
          innerCopperThickness = innerCopperThickness.slice(0, 3);
          this.$message.warning("成品铜厚内层不能超过3个字符");
        }
        this.proOrderInfoDto.innerCopperThickness = innerCopperThickness.join("");
      }
    },
    setEstimate3(value, list) {
      this.proOrderInfoDto.copperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.copperThickness, list, value);
    },
    handleSearch3(value, list) {
      this.setEstimate3(value, list);
    },
    handleBlur3(value, list) {
      this.setEstimate3(value, list);
      if (this.proOrderInfoDto.copperThickness) {
        var copperThickness = this.proOrderInfoDto.copperThickness.toString().split("");
        if (copperThickness.length > 3) {
          copperThickness = copperThickness.slice(0, 3);
          this.$message.warning("成品铜厚外层不能超过3个字符");
        }
        this.proOrderInfoDto.copperThickness = copperThickness.join("");
      }
    },
    setEstimate4(value, list) {
      this.proOrderInfoDto.warpage = value;
      let a = this.getPrice(this.proOrderInfoDto.warpage, list, value);
    },
    handleSearch4(value, list) {
      this.setEstimate4(value, list);
    },
    handleBlur4(value, list) {
      this.setEstimate4(value, list);
      if (this.proOrderInfoDto.warpage) {
        var warpage = this.proOrderInfoDto.warpage.split("");
        if (warpage.length > 6) {
          warpage = warpage.slice(0, 6);
          this.$message.warning("翘曲度不能超过6个字符");
        }
        this.proOrderInfoDto.warpage = warpage.join("");
      }
    },
    setEstimate5(value, list) {
      this.proOrderInfoDto.vcutSurplusThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.vcutSurplusThickness, list, value);
    },
    handleSearch5(value, list) {
      this.setEstimate5(value, list);
    },
    handleBlur5(value, list) {
      this.setEstimate5(value, list);
    },
    setEstimate6(value, list) {
      this.proOrderInfoDto.minSolderBridge = value;
      let a = this.getPrice(this.proOrderInfoDto.minSolderBridge, list, value);
    },
    handleSearch6(value, list) {
      this.setEstimate6(value, list);
    },
    handleBlur6(value, list) {
      this.setEstimate6(value, list);
    },
    setEstimate7(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness, list, value);
    },
    handleSearch7(value, list) {
      this.setEstimate7(value, list);
    },
    handleBlur7(value, list) {
      this.setEstimate7(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness) {
        var cjNickelThinckness = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness.split("");
        if (cjNickelThinckness.length > 10) {
          cjNickelThinckness = cjNickelThinckness.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = cjNickelThinckness.join("");
      }
    },
    setEstimate8(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2, list, value);
    },
    handleSearch8(value, list) {
      this.setEstimate8(value, list);
    },
    handleBlur8(value, list) {
      this.setEstimate8(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2) {
        var cjNickelThinckness2 = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2.split("");
        if (cjNickelThinckness2.length > 10) {
          cjNickelThinckness2 = cjNickelThinckness2.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = cjNickelThinckness2.join("");
      }
    },
    setEstimate9(value, list) {
      this.proOrderInfoDto.boardThicknessTol = value;
      let a = this.getPrice(this.proOrderInfoDto.boardThicknessTol, list, value);
    },
    handleSearch9(value, list) {
      this.setEstimate9(value, list);
    },
    handleBlur9(value, list) {
      this.setEstimate9(value, list);
    },
    setEstimate10(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = value;
    },
    handleSearch10(value, list) {
      this.setEstimate10(value, list);
    },
    handleBlur10(value, list) {
      this.setEstimate10(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness) {
        var newTinThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness.split("");
        if (newTinThickness.length > 10) {
          newTinThickness = newTinThickness.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = newTinThickness.join("");
      }
    },
    setEstimate11(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = value;
    },
    handleSearch11(value, list) {
      this.setEstimate11(value, list);
    },
    handleBlur11(value, list) {
      this.setEstimate11(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2) {
        var newTinThickness2 = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2.split("");
        if (newTinThickness2.length > 10) {
          newTinThickness2 = newTinThickness2.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = newTinThickness2.join("");
      }
    },
    get(val) {
      $("#formDataElem .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
  },
  directives: {
    focusNextOnTab: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            for (var key in vnode.context.$refs) {
              // console.log(vnode.context.$refs[key])
              if (!vnode.context.$refs[key]) {
                delete vnode.context.$refs[key];
              }
            }
            //  console.log('点击',vnode.context.$refs,index)
            //  console.log('点击ref',Object.keys(vnode.context.$refs),value)
            if (Object.keys(vnode.context.$refs).indexOf(value) == -1) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[index + 1]];
              index = index + 1;
              nextInput.focus();
            } else if (Object.keys(vnode.context.$refs).indexOf(value) >= 0 && vnode.context.$refs[value].disabled != true) {
              let nextInput = vnode.context.$refs[value];
              index = Object.keys(vnode.context.$refs).indexOf(value);
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 1]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 1]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 1]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 2]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 2]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 2]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 3]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 3]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 3]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 4]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 4]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 4]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 5]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 5]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 5]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 6]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 6]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 6]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 7]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 7]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 7]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 8]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 8]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 8]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 9]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 9]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 9]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 10]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 10]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 10]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 11]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 11]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 11]];
              nextInput.focus();
            } else if (
              Object.keys(vnode.context.$refs).indexOf(Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 12]) >= 0 &&
              vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 12]].disabled != true
            ) {
              let nextInput = vnode.context.$refs[Object.keys(vnode.context.$refs)[Object.keys(vnode.context.$refs).indexOf(value) + 12]];
              nextInput.focus();
            }
          }
        });
      },
    },
  },
  created() {
    getvcutselect(this.$route.query.factory).then(res => {
      if (res.code) {
        this.Vcutdata = res.data;
      }
    });
  },
};
</script>
<style scoped lang="less">
.box {
  /deep/.ant-select-dropdown--single {
    min-width: 90px;
  }
}
/deep/.ant-col-20 {
  width: 1291px;
}
/deep/.ant-row {
  width: 1300px;
}
/deep/.ant-col-4 {
  width: 258px;
}
/deep/.ant-col-3 {
  width: 194px;
}
/deep/.ant-col-8 {
  width: 516px;
}
/deep/.ant-col-9 {
  width: 582px;
}
.div1 {
  /deep/.ant-form-item-control {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.div2 {
  .div22 {
    /deep/.ant-form-item-control {
      padding: 0;
      min-height: 28px !important;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}

.autoo {
  /deep/.ant-form-item-control {
    height: 100% !important;
    width: 1184px;
  }
  /deep/.ant-form-item-control {
    // height: auto!important;
    background: #f5f5f5 !important;
    .ant-input {
      margin-top: 4px !important;
      margin-bottom: 0 !important;
    }
  }
}
/deep/textarea.ant-input {
  min-height: 24px;
  line-height: 1.3;
}
.speclass {
  /deep/ .ant-form-item-label {
    width: 107px;
  }
  /deep/ .ant-form-item-label > label {
    font-size: 13px !important;
  }
  /deep/ .ant-form-item-control-wrapper {
    width: 1178px;
  }
}
/deep/ .div1 {
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px !important;
    }
  }
  .ant-form-item-children {
    overflow: inherit !important;
    font-size: 13px;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  /* border-color: #ff9900; */
  border: 2px solid red;
}
/deep/.ant-select-focused .ant-select-selection {
  border: 2px solid red;
}
/deep/.ant-input:focus {
  border: 2px solid red;
}
/deep/.colSTY {
  border-left: 1px solid #ddd;
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px;
    }
  }
}
// /deep/.ant-col{
//   border-left:1px solid #ddd;
// }
/deep/.sss {
  height: 30px;
}
/deep/.ant-col-3 {
  .ant-col-14 {
    width: 58.5%;
  }
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-input {
  padding: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 11px !important;
  margin-left: 6px !important;
}

/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
// /deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
//     height: 16px!important;
//     margin-top: 3px;
//     line-height: 14px!important;
//   }
/deep/.surSTY {
  // height:56px;
  .ant-form-item-control-wrapper {
    // min-height:20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
      }
    }
  }
}
/deep/.heightSty {
  height: 28px;
  .ant-select {
    margin-top: 2px;
  }
  .ant-select-selection--multiple {
    height: 20px;
    min-height: 20px;
  }
  .ant-select-allow-clear {
    .ant-select-selection--multiple {
      height: 23px;
    }
    .ant-select-selection__rendered {
      ul {
        display: flex;
        li {
          height: 16px;
          margin-top: 2px;
          line-height: 16px;
          user-select: none;
          padding-left: 0 !important;
        }
      }
    }
    .ant-select-selection__clear {
      top: 13px;
    }
  }
}
/deep/.heightSty1 {
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
        // .edit{
        //  line-height: 28px!important;
        // text-overflow: unset!important;
        // white-space: unset!important;
        // }
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 17px !important;
            margin-top: 3px;
            line-height: 15px !important;
            width: 92%;
          }

          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}

/deep/.ant-select-dropdown-menu-item {
  font-size: 14px;
  padding: 0.5%;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-size: 14px !important;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-size: 14px !important;
  font-weight: 500;

  color: #000000;
}

.ant-row {
  .ant-col-22 {
    .ant-form-item-control {
      .ant-input-affix-wrapper {
        line-height: 29px;
      }
    }
  }
  .ant-col-17 {
    .ant-form-item {
      /deep/.ant-input {
        min-height: 23px !important;
        height: 23px !important;
        line-height: 15px !important;
      }
    }
  }
  .ant-col-24 {
    /deep/.ant-form-item-label {
      width: 106px !important;
    }
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.disable {
  background: #f5f5f5 !important;
}
.box {
  overflow: auto;
  border-left: 1px solid rgb(233 230 230);
  position: relative;
  .bto {
    position: absolute;
    bottom: -48px;
    right: 374px;
  }
}

/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;
  min-height: 28px;
  .tmp1 {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 370px;
    display: inline-block;
  }
  .tmp {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 140px;
    display: inline-block;
  }
  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 25px;
    .ant-select {
      width: 20;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #f1f1f1;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        // display: block;
        // min-height: 25px;
        line-height: 25px;
        // // vertical-align: top;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
        // width: 100%;
        // display: inline-block;

        // .ant-select-allow-clear{
        //   // .ant-select-selection--multiple{
        //   //   height: 23px;
        //   //   margin-top:2px;
        //   // }
        //   .ant-select-selection__rendered{
        //     ul{
        //       display: flex;
        //       li{
        //         margin-top:-1px;
        //       }
        //     }
        //     .ant-select-selection__choice{
        //       height: 18px;
        //       margin-top: 2px;
        //       line-height: 14px;
        //       user-select: none;
        //     }
        //   }
        //   .ant-select-selection__clear{
        //     top:11px;
        //   }
        // }
        .ant-checkbox-wrapper {
          min-height: 28px;
        }
        .ant-select-selection--single {
          height: 22px;
        }
        .ant-select-selection__rendered {
          line-height: 18px;
        }
        .ant-select {
          height: 22px;
        }
        .ant-input {
          height: 22px;
          padding-top: 2.6px;
        }
      }
      line-height: inherit;
      padding: 0px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      height: 28px;
    }
  }
}
.div2 {
  /deep/ .ant-form-item {
    margin: 0;
    width: 100%;
    display: flex;
    min-height: 28px;
    .tmp1 {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 370px;
      display: inline-block;
    }
    .tmp {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 140px;
      display: inline-block;
    }
    .editWrapper {
      display: flex;
      align-items: center;
      min-height: 25px;
      .ant-select {
        width: 20;
      }
      .ant-input {
        width: 120px;
      }
      .ant-input-number {
        width: 120px;
      }
    }
    .ant-form-item-label {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      color: #666;
      background-color: #f1f1f1;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      label {
        font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      }
    }
    .ant-form-item-control-wrapper {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      .ant-form-item-control {
        .ant-form-item-children {
          // display: block;
          // min-height: 25px;
          line-height: 25px;
          // // vertical-align: top;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          // overflow: hidden;
          // width: 100%;
          // display: inline-block;

          // .ant-select-allow-clear{
          //   // .ant-select-selection--multiple{
          //   //   height: 23px;
          //   //   margin-top:2px;
          //   // }
          //   .ant-select-selection__rendered{
          //     ul{
          //       display: flex;
          //       li{
          //         margin-top:-1px;
          //       }
          //     }
          //     .ant-select-selection__choice{
          //       height: 18px;
          //       margin-top: 2px;
          //       line-height: 14px;
          //       user-select: none;
          //     }
          //   }
          //   .ant-select-selection__clear{
          //     top:11px;
          //   }
          // }
          .ant-checkbox-wrapper {
            min-height: 28px;
          }
          .ant-select-selection--single {
            height: 22px;
          }
          .ant-select-selection__rendered {
            line-height: 18px;
          }
          .ant-select {
            height: 22px;
          }
          .ant-input {
            height: 22px;
            padding-top: 2.6px;
          }
        }
        line-height: inherit;
        padding: 0px 5px;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        height: auto !important;
      }
    }
  }
}
</style>
