<!-- 工具管理- 叠层阻抗-叠层信息 -->
<template>
  <div style="width: 80%">
    <div
      :class="['laminationInfo', collapse ? 'laminationInfoActive' : 'laminationInfoNo']"
      :style="{ height: tableHeight + 'px', overflow: 'auto' }"
      style="width: 80%; position: absolute; top: 0; right: 0"
      ref="SelectBox"
    >
      <div class="title">
        <span class="head-menu">叠层信息</span>
      </div>
      <a-table
        :columns="columns"
        :data-source="data"
        bordered
        :pagination="false"
        size="small"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :customRow="customClick"
        :row-class-name="addClass"
        :loading="laminationInfoLoading"
      >
        <!--   选中   -->
        <span
          slot="index"
          slot-scope="record, text, index"
          @contextmenu.prevent="rightClick($event, index)"
          :class="activeIndex == index && !iminsert ? 'selectIndex' : ''"
          @click="leftclick(record, text, index)"
        >
          <span>{{ index + 1 }}</span>
        </span>
        <!--层号-->
        <span slot="stackUpLayerNo_" slot-scope="record, text, index">
          <a-select
            v-if="record.stackUpMTR_ == 'OZ'"
            v-model="record.stackUpLayerNo_"
            :showArrow="false"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpLayerNo_')"
            :ref="`row-${index}-col-stackUpLayerNo_`"
          >
            <a-select-option v-for="(item, index) in Number(form.layers)" :value="item" :key="item + 'a' + index" :title="item">
              {{ item }}</a-select-option
            >
          </a-select>
          <p v-else-if="record.stackUpLayerNo_ != null && record.stackUpMTR_ != 'OZ'">{{ record.stackUpLayerNo_ }}</p>
        </span>
        <!--物料-->
        <span slot="stackUpMTR_" slot-scope="record, text, index">
          <a-select
            v-model="record.stackUpMTR_"
            @change="handleChangeMTR($event, record)"
            :showArrow="false"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpMTR_')"
            :ref="`row-${index}-col-stackUpMTR_`"
          >
            <a-select-option value="OZ" title="OZ"> OZ </a-select-option>
            <a-select-option value="PP" title="PP"> PP </a-select-option>
            <a-select-option value="Core" title="Core"> Core </a-select-option>
            <a-select-option value="GB" title="GB"> GB </a-select-option>
            <a-select-option value="补强" title="补强"> 补强 </a-select-option>
            <a-select-option value="金属基" title="金属基"> 金属基 </a-select-option>
          </a-select>
        </span>
        <!--型号-->
        <span slot="stackUpMTRType_" slot-scope="record, text, index">
          <a-select
            v-if="['Core', 'GB', '金属基', '补强'].includes(record.stackUpMTR_)"
            v-model="record.stackUpMTRType_"
            :showArrow="false"
            show-search
            option-filter-prop="children"
            :filter-option="filterOption"
            @change="stackChange(index)"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpMTRType_')"
            :ref="`row-${index}-col-stackUpMTRType_`"
          >
            <a-select-option v-for="item in stackTypeFilter(record.stackUpMTR_)" :value="item.text" :key="item.valueMember" :title="item.text">
              {{ item.text }}
            </a-select-option>
          </a-select>
          <a-select
            v-if="record.stackUpMTR_ == 'PP'"
            v-model="record.stackUpMTRType_"
            :showArrow="false"
            show-search
            option-filter-prop="children"
            :filter-option="filterOption"
            @change="borderChange(text, index)"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpMTRType_')"
            :ref="`row-${index}-col-stackUpMTRType_`"
          >
            <a-select-option v-for="item in ppTypeList" :value="item.text" :key="item.valueMember" :title="item.text">
              {{ item.text }}
            </a-select-option>
          </a-select>
          <a-select
            v-if="record.stackUpMTR_ == 'SM'"
            v-model="record.stackUpMTRType_"
            :showArrow="false"
            show-search
            option-filter-prop="children"
            :getPopupContainer="() => $refs.SelectBox"
          >
            <a-select-option value="通用" title="通用"> 通用 </a-select-option>
            <a-select-option value="太阳" title="太阳"> 太阳 </a-select-option>
          </a-select>
          <span v-if="record.stackUpMTR_ == 'OZ'"></span>
        </span>
        <!--介质-->
        <span slot="stackUpMTRFoil_" slot-scope="record, text, index">
          <a-select
            showSearch
            v-if="['OZ', 'Core', 'GB', '补强', '金属基'].includes(record.stackUpMTR_)"
            v-model="record.stackUpMTRFoil_"
            @change="handleChange($event, index, record)"
            :showArrow="false"
            :filter-option="record.stackUpMTR_ == 'GB' || record.stackUpMTR_ == 'OZ' ? gbfilterOption : filterOption"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpMTRFoil_')"
            :ref="`row-${index}-col-stackUpMTRFoil_`"
          >
            <a-select-option
              v-for="item in mediumList(record, index)"
              :value="item[record.stackUpMTR_ == 'OZ' ? 'cuType_' : record.stackUpMTR_ == 'GB' ? 'rKey_' : 'core_'] + ''"
              :key="item[record.stackUpMTR_ == 'OZ' ? 'cuType_' : record.stackUpMTR_ == 'GB' ? 'rKey_' : 'core_']"
              :title="
                record.stackUpMTR_ == 'OZ'
                  ? item['cuType_']
                  : record.stackUpMTR_ == '金属基' || record.stackUpMTR_ == '补强'
                  ? item['core_'] + '(' + item['topOZ_'] + '/' + item['bomOZ_'] + ')'
                  : record.stackUpMTR_ == 'Core'
                  ? item['core_']
                  : record.stackUpCoreDS_
                  ? item['core_'] + '(' + item['topOZ_'] + '/' + item['bomOZ_'] + ')(含铜)' + (item['waterMark_'] ? '(有水印)' : '(无水印)')
                  : item['core_'] + '(' + item['topOZ_'] + '/' + item['bomOZ_'] + ')(不含铜)' + (item['waterMark_'] ? '(有水印)' : '(无水印)')
              "
            >
              {{
                record.stackUpMTR_ == "OZ"
                  ? item["cuType_"]
                  : record.stackUpMTR_ == "金属基" || record.stackUpMTR_ == "补强"
                  ? item["core_"] + "(" + item["topOZ_"] + "/" + item["bomOZ_"] + ")"
                  : record.stackUpMTR_ == "Core"
                  ? item["core_"]
                  : item["tD_"]
                  ? item["core_"] + "(" + item["topOZ_"] + "/" + item["bomOZ_"] + ")(含铜)" + (item["waterMark_"] ? "(有水印)" : "(无水印)")
                  : item["core_"] + "(" + item["topOZ_"] + "/" + item["bomOZ_"] + ")(不含铜)" + (item["waterMark_"] ? "(有水印)" : "(无水印)")
              }}
            </a-select-option>
          </a-select>
          <div
            v-if="record.stackUpMTR_ == 'PP'"
            @dblclick="ppClick(record, index)"
            style="height: 25px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
            :title="record.stackUpMTRFoil_"
          >
            {{ record.stackUpMTRFoil_ }}
          </div>
          <a-select
            v-if="record.stackUpMTR_ == 'SM'"
            v-model="record.stackUpMTRFoil_"
            :showArrow="false"
            show-search
            option-filter-prop="children"
            :getPopupContainer="() => $refs.SelectBox"
          >
            <a-select-option value="一次印刷" title="一次印刷"> 一次印刷 </a-select-option>
            <a-select-option value="二次印刷" title="二次印刷"> 二次印刷 </a-select-option>
            <a-select-option value="三次印刷" title="三次印刷"> 三次印刷 </a-select-option>
          </a-select>
        </span>
        <template slot="customTitle" slot-scope="">
          <vue-draggable-resizable :w="widthchange" :h="30" class-name="dragging2" :draggable="false" @resizing="onResize">
            <div style="line-height: 30px; width: 100%" :width="widthchange">介质</div>
          </vue-draggable-resizable>
        </template>
        <!-- 填胶-->
        <span slot="stackUpPPFillGlue_" slot-scope="record, text, index">
          <a-select
            v-if="record.stackUpMTR_ == 'PP'"
            v-model="record.stackUpPPFillGlue_"
            :showArrow="false"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpPPFillGlue_')"
            :ref="`row-${index}-col-stackUpPPFillGlue_`"
          >
            <a-select-option v-for="item in underfillList" :value="item.value" :key="item.value" :label="item.name">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </span>
        <!-- 大料尺寸 -->
        <span slot="stackUpPNLSize_" slot-scope="record, text, index">
          <a-select
            v-if="record.stackUpMTR_ == 'Core' || record.stackUpMTR_ == 'GB'"
            showSearch
            v-model="record.stackUpPNLSize_"
            :showArrow="false"
            :filter-option="filterOption"
            :getPopupContainer="() => $refs.SelectBox"
            @change="pnlchange(record, index)"
          >
            <a-select-option v-for="item in mediumList1(record, index)" :value="item.pnlSize_" :key="item.pnlSize_">
              {{ item.pnlSize_ }}
            </a-select-option>
          </a-select>
        </span>
        <span slot="stackUpThichnessMM_" slot-scope="record">
          <div class="redbac">{{ record.stackUpThichnessMM_ }}</div>
        </span>
        <!--残铜率-->
        <span slot="stackUpCTLMI_" slot-scope="record, text, index">
          <a-input
            v-if="record.stackUpMTR_ == 'OZ'"
            v-model="record.stackUpCTLMI_"
            :disabled="(index == 0 || index == data.length - 1) && record.stackUpCTLMI_ == 100"
            style="border-radius: 0"
            :title="record.stackUpCTLMI_"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpCTLMI_')"
            :ref="`row-${index}-col-stackUpCTLMI_`"
          />
        </span>
        <!--T/B-->
        <span slot="stackUpCUTB_" slot-scope="record, text, index">
          <a-select
            v-if="record.stackUpMTR_ == 'OZ'"
            v-model="record.stackUpCUTB_"
            :showArrow="false"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpCUTB_')"
            :ref="`row-${index}-col-stackUpCUTB_`"
          >
            <a-select-option value="t" title="t"> t </a-select-option>
            <a-select-option value="b" title="b"> b </a-select-option>
          </a-select>
        </span>
        <span slot="CuMin" slot-scope="record, text, index">
          <a-input
            v-if="record.stackUpMTRFoil_ && record.stackUpMTRFoil_.indexOf('PT') != -1"
            v-model="record.stackUpCuMin4T1_"
            :title="record.stackUpCuMin4T1_"
            style="border-radius: 0"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpCuMin4T1_')"
            :ref="`row-${index}-col-stackUpCuMin4T1_`"
          />
        </span>
        <span slot="CuMax" slot-scope="record, text, index">
          <a-input
            v-if="record.stackUpMTRFoil_ && record.stackUpMTRFoil_.indexOf('PT') != -1"
            v-model="record.stackUpCuMax4T1_"
            style="border-radius: 0"
            :title="record.stackUpCuMax4T1_"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpCuMax4T1_')"
            :ref="`row-${index}-col-stackUpCuMax4T1_`"
          />
        </span>
        <span slot="oz" slot-scope="record, text, index">
          <a-select
            v-if="record.stackUpMTR_ == 'OZ'"
            v-model="record.stackUpCuType_"
            :showArrow="false"
            :getPopupContainer="() => $refs.SelectBox"
            @keydown.enter.native="handlePressEnter1(record, index, 'stackUpCuType_')"
            :ref="`row-${index}-col-stackUpCuType_`"
          >
            <a-select-option value="HTE铜箔" title="HTE铜箔"> HTE铜箔 </a-select-option>
            <a-select-option value="反转铜箔" title="反转铜箔"> 反转铜箔 </a-select-option>
            <a-select-option value="STD" title="STD"> STD </a-select-option>
          </a-select>
        </span>
        <!--含铜-->
        <span slot="stackUpCoreDS_" slot-scope="record, text, index">
          <a-checkbox
            v-if="['Core', 'GB', '补强', '金属基'].includes(record.stackUpMTR_) && record.tdFlag_ == ''"
            v-model="record.stackUpCoreDS_"
            @change="handleChange1(index, record)"
          />
          <a-checkbox
            v-else-if="['Core', 'GB', '补强', '金属基'].includes(record.stackUpMTR_) && record.tdFlag_ != ''"
            v-model="record.stackUpCoreDS_"
            disabled
          />
        </span>
        <!--水印 waterMarkFlag_-->
        <span slot="waterMark_" slot-scope="record, text, index">
          <a-checkbox
            v-if="['Core', 'GB', '补强', '金属基'].includes(record.stackUpMTR_) && record.waterMarkFlag_ == ''"
            v-model="record.waterMark_"
            @change="wmhandleChange(index, record)"
          />
          <a-checkbox
            v-else-if="['Core', 'GB', '补强', '金属基'].includes(record.stackUpMTR_) && record.waterMarkFlag_ != ''"
            v-model="record.waterMark_"
            disabled
          />
        </span>
      </a-table>
      <a-menu :style="menuStyle" v-if="menuVisible" class="tabRightClikBox">
        <a-menu-item @click="addRow">添加</a-menu-item>
        <a-menu-item @click="delectRow">删除</a-menu-item>
        <a-menu-item @click="PPsame">PP相同</a-menu-item>
        <a-menu-item @click="PPsymmetry">PP对称</a-menu-item>
        <a-menu-item @click="ozSame">基铜相同</a-menu-item>
        <a-menu-item @click="ozsymmetry">基铜对称</a-menu-item>
        <a-menu-item @click="coreSame">芯板相同</a-menu-item>
        <a-menu-item @click="coresymmetry">芯板对称</a-menu-item>
        <a-menu-item @click="Insertopticalboard">插入光板</a-menu-item>
        <a-menu-item @click="Residualcopperrate">导入残铜率</a-menu-item>
      </a-menu>
    </div>
    <div
      style="width: 0; position: absolute; right: 0; border: 2px solid #e8e8e8; background-color: white"
      :style="{ height: tableHeight + 'px', overflow: 'auto' }"
      ref="ppcanshu"
    >
      <div v-if="ppshow">
        <pp-info-modal
          :ppList="ppList"
          :loading="loading"
          :dataSourcePP="dataSourcePP"
          :ppId="ppId"
          :ppChange="ppChange"
          @ppTableFiltet="ppTableFiltet"
          @rcChange="rcChange"
          @PPChange="PPChange"
          @submitPP="submitPP"
          @handleCancel="handleCancel"
          :ppheighty="ppheighty"
        />
      </div>
    </div>
  </div>
</template>

<script>
import VueDraggableResizable from "vue-draggable-resizable";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";
import PpInfoModal from "@/pages/gongju/impedance/components/PpinfoModal";
import { coreList, ozList, setstackupcopperratio } from "@/services/impedance";
import { mapGetters, mapState, mapMutations } from "vuex";
import { type } from "jquery";
const underfillList = [
  {
    name: "U",
    value: "U",
  },
  {
    name: "D",
    value: "D",
  },
  {
    name: "B",
    value: "B",
  },
  {
    name: "N",
    value: "N",
  },
];
export default {
  name: "LaminationInfo",
  components: { PpInfoModal, VueDraggableResizable },
  props: {
    iminsert: {
      type: Boolean,
      required: true,
    },
    boardTypeList: {
      type: Array,
      required: true,
    },
    ddta: {
      type: Array,
    },
    ppTypeList: {
      type: Array,
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    someList: {
      type: Array,
      required: true,
    },
    collapse: {
      type: Boolean,
    },
    PPVisible: {
      type: Boolean,
    },
    laminationInfoLoading: {
      type: Boolean,
    },
    tableHeight: {
      type: Number,
    },
    ozListData: {
      type: Array,
    },
    coreListData: {
      type: Array,
    },
    okBtnFlg: {
      type: Boolean,
    },
    GBListData: {
      type: Array,
    },
    ppList: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      required: true,
    },
    dataSourcePP: {
      type: Array,
      required: true,
    },
    ppId: {
      type: String,
      required: true,
    },
    ppChange: {
      type: Array,
    },
    indlist: {
      type: String,
      required: true,
    },
    fac: {
      type: String,
      required: true,
    },
  },
  created() {
    if (this.ozListData.length) {
      this.ozMediumList2 = [];
      this.ozMediumList = [];
      this.ozListData.forEach(item => {
        if (item.innerOrOuter == 1) {
          this.ozMediumList2.push(item);
        } else {
          this.ozMediumList.push(item);
        }
      });
      localStorage.setItem("ozList", JSON.stringify(this.ozMediumList2));
      localStorage.setItem("ozList1", JSON.stringify(this.ozMediumList));
    }
    if (this.coreListData.length) {
      this.coreMediumList = this.coreListData;
    }
    if (this.GBListData.length) {
      this.gbMediumList = this.GBListData;
    }
  },
  computed: {
    ...mapState({ joinFactoryId: state => state.impedance._joinFactoryId }),
    ...mapGetters({ form: "categoryForm" }),
    dataNew() {
      return JSON.parse(JSON.stringify(this.data));
    },
  },

  data() {
    return {
      changdata: [],
      widthchange: 165,
      okBtnFlg1: false,
      ppheighty: null,
      ppshow: false,
      ins: false,
      del: false,
      isCtrlPressed: false,
      mincoreData: [],
      okBtnFlg2: false,
      clickCounter1: 0,
      ozMediumList: [],
      ozMediumList2: [],
      coreMediumList: [],
      gbMediumList: [],
      underfillList,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      activeIndex: NaN,
      // customClick: (record,index) => ({
      //   on: {
      //     click: (event) => {
      //     },
      //     contextmenu: e => {
      //       e.preventDefault();
      //       this.menuData = record;
      //      if(e.target.className.indexOf('index_') == -1) {
      //        return
      //      }
      //      this.activeIndex = index
      //       this.menuVisible = true;
      //       // this.menuStyle.top = e.clientY - 48 + "px";
      //       this.menuStyle.top = e.clientY - 140 + "px";
      //       let multiple = this.collapse ? 0.2 : 0.02
      //       this.menuStyle.left = e.clientX-window.innerWidth*multiple + 15 + "px";
      //       document.body.addEventListener("click", this.bodyClick);
      //     }
      //   }
      // }),
      value1Data: [],
      stackUpCoreDS_: null,
      ppduic1: false,
      inde: [],
    };
  },
  watch: {
    indlist: {
      deep: true,
      handler(val) {
        this.inde = val.split("+");
        let st = document.getElementsByClassName("redbac");
        let ind = [];
        for (let index = 0; index < st.length; index++) {
          if (
            (this.data[index].stackUpLayerNo_ == this.inde[0] || this.data[index].stackUpLayerNo_ == this.inde[1]) &&
            this.inde.length &&
            (this.inde[0] != 1 || this.inde[1] != this.form.layers)
          ) {
            ind.push(index);
          }
        }
        for (let i = 0; i < this.data.length; i++) {
          if (i >= ind[0] && i <= ind[1]) {
            st[i].style.background = "red";
          } else {
            st[i].style.background = "";
          }
        }
      },
    },
    dataNew: {
      deep: true,
      handler(newV, oldV) {
        this.changdata = [];
        const data1 = {
          stackUpLayerNo_: "层号",
          stackUpMTR_: "物料",
          stackUpMTRType_: "型号",
          stackUpMTRFoil_: "介质",
          stackUpCoreDS_: "含铜",
          stackUpCUTB_: "TB",
          stackUpPNLSize_: "大料尺寸",
        };
        newV.forEach((newItem, index) => {
          if (oldV[index] && newItem.stackUpMTRType_ != oldV[index].stackUpMTRType_) {
            this.stackChange(index, 1);
          }
          this.clickCounter1++;
          if (this.clickCounter1 <= this.ddta.length && this.okBtnFlg) {
            this.okBtnFlg1 = true;
          } else {
            this.okBtnFlg1 = false;
          }
          if (newItem && oldV.length > 1 && oldV[index] && !this.okBtnFlg1 && !this.ppduic1) {
            if (
              newItem.stackUpMTR_ != oldV[index].stackUpMTR_ ||
              newItem.stackUpMTRType_ != oldV[index].stackUpMTRType_ ||
              newItem.stackUpMTRFoil_ != oldV[index].stackUpMTRFoil_ ||
              newItem.stackUpCTLMI_ != oldV[index].stackUpCTLMI_ ||
              newItem.stackUpCUTB_ != oldV[index].stackUpCUTB_ ||
              newItem.stackUpCoreDS_ != oldV[index].stackUpCoreDS_
            ) {
              this.$store.commit("changeInfo", { pressingThickness: "" });
              this.$store.commit("changeInfo", { finishedThickness: "" });
              this.$emit("pressingThicknessBgChange");
            }
          }
          if (newItem.stackUpMTRFoil_?.indexOf("PT") == -1) {
            newItem.stackUpCuMin4T1_ = "";
            newItem.stackUpCuMax4T1_ = "";
          }
          const oldItem = oldV[index];
          for (const key in newItem) {
            if (
              oldV &&
              oldItem &&
              newItem.iD_ != 0 &&
              oldItem.iD_ != 0 &&
              newItem[key] !== oldItem[key] &&
              newItem.iD_ == oldItem.iD_ &&
              (key == "stackUpLayerNo_" ||
                key == "stackUpMTR_" ||
                key == "stackUpMTRType_" ||
                key == "stackUpMTRFoil_" ||
                key == "stackUpCoreDS_" ||
                key == "stackUpCUTB_" ||
                key == "stackUpPNLSize_")
            ) {
              this.setedit(true);
              this.changdata.push({ id_: newItem.iD_, label: ` ${data1[key]}: ${oldItem[key]} -> ${newItem[key]}` });
            }
          }
        });
        this.$emit("Datachange", this.changdata);
        this.ppduic1 = false;
      },
    },
    ozListData: {
      handler(val) {
        if (val.length) {
          this.ozMediumList2 = [];
          this.ozMediumList = [];
          val.forEach(item => {
            if (item.innerOrOuter == 1) {
              this.ozMediumList2.push(item);
            } else {
              this.ozMediumList.push(item);
            }
          });
          localStorage.setItem("ozList", JSON.stringify(this.ozMediumList2));
          localStorage.setItem("ozList1", JSON.stringify(this.ozMediumList));
        }
      },
    },
    coreListData: {
      handler(val) {
        if (val.length) {
          this.coreMediumList = val;
        }
      },
    },
    GBListData: {
      handler(val) {
        if (val.length) {
          this.gbMediumList = val;
        }
      },
    },
  },
  methods: {
    ...mapMutations("setting", ["setedit"]),
    mmbaccolor(record, index) {},
    gbfilterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().trim().startsWith(input.toLowerCase()) == true;
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    onResize: function (x, y, width, height) {
      this.columns[4].width = width;
      this.widthchange = width;
    },
    handleClickOutside(event) {
      const ppDiv = this.$refs.SelectBox; // 获取 div 的引用
      if (ppDiv && ppDiv.contains(event.target)) {
        this.ppstyle(false);
      }
    },
    ppTableFiltet(val) {
      this.$emit("ppTableFiltet", val);
    },
    rcChange(payload) {
      this.$emit("rcChange", payload, 1);
    },
    PPChange(payload) {
      this.$emit("PPChange", payload);
    },
    submitPP() {
      this.$emit("submitPP");
    },
    handleCancel() {
      this.$emit("handleCancel");
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    ppstyle(pps) {
      if (pps == true) {
        this.$refs.SelectBox.style.width = "58%";
        this.$refs.SelectBox.style.left = "20%";
        this.$refs.ppcanshu.style.right = 0;
        this.$refs.ppcanshu.style.width = "22%";
        this.ppshow = true;
      } else {
        this.$refs.SelectBox.style.width = "80%";
        this.$refs.SelectBox.style.left = "20%";
        this.$refs.ppcanshu.style.width = "0";
        this.ppshow = false;
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "45" && this.ins && !this.iminsert) {
        this.addRow();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
      if (e.keyCode == "46" && this.del && !this.iminsert) {
        e.preventDefault();
        this.delectRow();
        this.isCtrlPressed = false;
      }
    },
    addClass(record, index) {
      switch (record.stackUpMTR_) {
        case "OZ":
          return "OZ";
          // eslint-disable-next-line no-unreachable
          break;
        case "Core":
          return "Core";
          // eslint-disable-next-line no-unreachable
          break;
        case "PP":
          return "PP";
          // eslint-disable-next-line no-unreachable
          break;
        case "SM":
          return "SM";
          // eslint-disable-next-line no-unreachable
          break;
        default:
          break;
      }
    },
    handleChangeMTR(value, data) {
      data.stackUpMTRType_ = "";
      data.stackUpMTRFoil_ = "";
      data.stackUpThichnessORG_ = "";
      data.stackUpThichnessMIL_ = "";
      data.stackUpThichnessMM_ = "";
      data.stackUpCTLMI_ = "";
      data.stackUpDK_ = "";
      data.stackUpPNLSize_ = "";
      if (value == "PP") {
        data.stackUpMTRType_ = this.labelOrValue(this.ppTypeList, this.form.ppType);
      } else if (value == "Core" || value == "GB") {
        data.stackUpMTRType_ = this.labelOrValue(this.boardTypeList, this.form.boardType);
      }
    },
    labelOrValue(data, val) {
      let newval_ = "";
      data.forEach(item => {
        if (item.valueMember == val) {
          newval_ = item.text;
        }
      });
      return newval_;
    },
    borderChange(value, index) {
      let borderListArray = this.data.map(item => item.stackUpMTR_);
      let ppIndexArr_ = [];
      borderListArray.forEach((item, index) => {
        if (item == "PP") {
          ppIndexArr_.push(index);
        }
      });
      this.data[index].stackUpMTRFoil_ = "";
      if (index < this.data.length / 2 - 1) {
        this.data[ppIndexArr_[ppIndexArr_.length - 1 - ppIndexArr_.indexOf(index)]].stackUpMTRType_ = value.stackUpMTRType_;
        this.data[ppIndexArr_[ppIndexArr_.length - 1 - ppIndexArr_.indexOf(index)]].stackUpMTRFoil_ = "";
      }
      this.$forceUpdate();
    },
    // 获取介质列表
    // getMediumList(){
    //   // joinFactoryId
    //   let p1 = ozList(this.joinFactoryId)
    //   let p2 = coreList(this.joinFactoryId)
    //   Promise.all([p1, p2]).then(res => {
    //     res[0].data.forEach(item=>{
    //       if(item.innerOrOuter==1)  {
    //         this.ozMediumList2.push(item)
    //         localStorage.setItem('ozList', JSON.stringify(this.ozMediumList2))
    //       }else {
    //         this.ozMediumList.push(item)
    //         localStorage.setItem('ozList1', JSON.stringify(this.ozMediumList))
    //       }
    //     })
    //     var afterData = []
    //     res[1].data.forEach(item => {
    //       let flag = afterData.find(item1 => item1.coreType_ === item.coreType_)
    //       if (!flag) {
    //         afterData.push({
    //           coreType_: item.coreType_,
    //           origin: [item]
    //         })
    //       } else {
    //         flag.origin.push(item)
    //       }
    //     })
    //     // this.coreMediumList = afterData
    //     this.coreMediumList = res[1].data
    //   })
    // },
    mediumList(record, index) {
      if (record.stackUpMTR_ == "OZ") {
        if (record.stackUpLayerNo_ == "1" || record.stackUpLayerNo_ == this.form.layers) {
          let data1 = JSON.parse(JSON.stringify(this.ozMediumList2));
          return data1.sort(function (a, b) {
            return a["cuType_"].split("_PT_")[0] - b["cuType_"].split("_PT_")[0];
          });
        } else {
          let data2 = JSON.parse(JSON.stringify(this.ozMediumList));
          return data2.sort(function (a, b) {
            return a["cuType_"].split("_PT_")[0] - b["cuType_"].split("_PT_")[0];
          });
        }
      } else {
        let coreData = [];
        let length_ = this.data.length;
        if (record.stackUpMTR_ == "Core") {
          if (index == length_ - 1) {
            return [];
          } else {
            let topOz = this.data[index - 1].stackUpMTRFoil_?.split("_PT_")[0];
            let bomOz = this.data[index + 1].stackUpMTRFoil_?.split("_PT_")[0];
            this.coreMediumList.forEach(res => {
              if (record.stackUpMTRType_ == res.coreType_ && res.topOZ_ == topOz && res.bomOZ_ == bomOz) {
                coreData.push(res);
              }
            });
            var del = false; //core oz
            var del2 = false; // core
            var del3 = false; //core oz pnlsize
            var del4 = false; // core pnlsize
            var del5 = false; //core oz pnlsize watermark
            var del6 = false; //core  pnlsize watermark
            if (coreData.length >= 1) {
              for (let i = 0; i < coreData.length; i++) {
                if (coreData[i].coreThickness == record.stackUpMTRFoil_ && coreData[i].tD_ == record.stackUpCoreDS_) {
                  this.data[index].stackUpMTRFoil_ = coreData[i].coreThickness.toString();
                  this.data[index].stackUpCoreDS_ = coreData[i].tD_;
                  this.data[index].stackUpThichnessORG_ = coreData[i].thicknessCore_; // 物料
                  this.data[index].stackUpThichnessMIL_ = coreData[i].thicknessCore_; // 压合
                  this.data[index].stackUpThichnessMM_ = this.getFloat(coreData[i].thicknessCore_ * 0.0254, 3); // 成品
                  if (record.stackUpPNLSize_ && record.stackUpPNLSize_ == coreData[i].pnlSize_) {
                    del3 = true;
                    if (record.waterMark_ == coreData[i].waterMark_) {
                      this.data[index].stackUpPNLSize_ = coreData[i].pnlSize_;
                      this.data[index].rKey_ = coreData[i].rKey_;
                      this.data[index].waterMark_ = coreData[i].waterMark_;
                      del5 = true;
                    }
                  }
                  del = true;
                  del2 = true;
                }
              }
              if (!del) {
                for (let a = 0; a < coreData.length; a++) {
                  if (coreData[a].coreThickness == record.stackUpMTRFoil_) {
                    this.data[index].stackUpMTRFoil_ = coreData[a].coreThickness.toString();
                    this.data[index].stackUpCoreDS_ = coreData[a].tD_;
                    this.data[index].waterMark_ = coreData[a].waterMark_;
                    this.data[index].stackUpThichnessORG_ = coreData[a].thicknessCore_; // 物料
                    this.data[index].stackUpThichnessMIL_ = coreData[a].thicknessCore_; // 压合
                    this.data[index].stackUpThichnessMM_ = this.getFloat(coreData[a].thicknessCore_ * 0.0254, 3); // 成品
                    if (record.stackUpPNLSize_ && record.stackUpPNLSize_ == coreData[a].pnlSize_) {
                      if (record.waterMark_ == coreData[a].waterMark_) {
                        this.data[index].waterMark_ = coreData[a].waterMark_;
                        this.data[index].stackUpPNLSize_ = coreData[a].pnlSize_;
                        this.data[index].rKey_ = coreData[a].rKey_;
                        del6 = true;
                      }
                      del4 = true;
                    }
                    del2 = true;
                  }
                }
              }
              if (del && del2 && del3 && !del5) {
                for (let i = 0; i < coreData.length; i++) {
                  if (coreData[i].coreThickness == record.stackUpMTRFoil_ && coreData[i].tD_ == record.stackUpCoreDS_) {
                    this.data[index].stackUpMTRFoil_ = coreData[i].coreThickness.toString();
                    this.data[index].stackUpCoreDS_ = coreData[i].tD_;
                    this.data[index].stackUpThichnessORG_ = coreData[i].thicknessCore_; // 物料
                    this.data[index].stackUpThichnessMIL_ = coreData[i].thicknessCore_; // 压合
                    this.data[index].stackUpThichnessMM_ = this.getFloat(coreData[i].thicknessCore_ * 0.0254, 3); // 成品
                    if (record.stackUpPNLSize_ && record.stackUpPNLSize_ == coreData[i].pnlSize_) {
                      del3 = true;
                      this.data[index].stackUpPNLSize_ = coreData[i].pnlSize_;
                      this.data[index].rKey_ = coreData[i].rKey_;
                      this.data[index].waterMark_ = coreData[i].waterMark_;
                    }
                    del = true;
                    del2 = true;
                  }
                }
              }
              if (del2 && del4 && !del && !del6) {
                for (let a = 0; a < coreData.length; a++) {
                  if (coreData[a].coreThickness == record.stackUpMTRFoil_) {
                    this.data[index].stackUpMTRFoil_ = coreData[a].coreThickness.toString();
                    this.data[index].stackUpCoreDS_ = coreData[a].tD_;
                    this.data[index].waterMark_ = coreData[a].waterMark_;
                    this.data[index].stackUpThichnessORG_ = coreData[a].thicknessCore_; // 物料
                    this.data[index].stackUpThichnessMIL_ = coreData[a].thicknessCore_; // 压合
                    this.data[index].stackUpThichnessMM_ = this.getFloat(coreData[a].thicknessCore_ * 0.0254, 3); // 成品
                    if (record.stackUpPNLSize_ && record.stackUpPNLSize_ == coreData[a].pnlSize_) {
                      this.data[index].waterMark_ = coreData[a].waterMark_;
                      this.data[index].stackUpPNLSize_ = coreData[a].pnlSize_;
                      this.data[index].rKey_ = coreData[a].rKey_;
                      del4 = true;
                    }
                    del2 = true;
                  }
                }
              }
              if (del2 && !del4 && !del) {
                for (let x = 0; x < coreData.length; x++) {
                  if (coreData[x].coreThickness == record.stackUpMTRFoil_) {
                    this.data[index].stackUpMTRFoil_ = coreData[x].coreThickness.toString();
                    this.data[index].stackUpCoreDS_ = coreData[x].tD_;
                    this.data[index].waterMark_ = coreData[x].waterMark_;
                    this.data[index].stackUpThichnessORG_ = coreData[x].thicknessCore_; // 物料
                    this.data[index].stackUpThichnessMIL_ = coreData[x].thicknessCore_; // 压合
                    this.data[index].stackUpThichnessMM_ = this.getFloat(coreData[x].thicknessCore_ * 0.0254, 3); // 成品
                    this.data[index].stackUpPNLSize_ = coreData[x].pnlSize_;
                    this.data[index].rKey_ = coreData[x].rKey_;
                  }
                }
              }
              if (del2 && !del3 && del) {
                for (let y = 0; y < coreData.length; y++) {
                  if (coreData[y].coreThickness == record.stackUpMTRFoil_) {
                    this.data[index].stackUpMTRFoil_ = coreData[y].coreThickness.toString();
                    this.data[index].stackUpCoreDS_ = coreData[y].tD_;
                    this.data[index].waterMark_ = coreData[y].waterMark_;
                    this.data[index].stackUpThichnessORG_ = coreData[y].thicknessCore_; // 物料
                    this.data[index].stackUpThichnessMIL_ = coreData[y].thicknessCore_; // 压合
                    this.data[index].stackUpThichnessMM_ = this.getFloat(coreData[y].thicknessCore_ * 0.0254, 3); // 成品
                    this.data[index].stackUpPNLSize_ = coreData[y].pnlSize_;
                    this.data[index].rKey_ = coreData[y].rKey_;
                  }
                }
              }
            } else {
              this.data[index].stackUpPNLSize_ = "";
              this.data[index].stackUpMTRFoil_ = "";
              this.data[index].rKey_ = "";
              this.data[index].stackUpCoreDS_ = false;
              this.data[index].waterMark_ = false;
            }
            if (!del2) {
              this.data[index].stackUpPNLSize_ = "";
              this.data[index].stackUpMTRFoil_ = "";
              this.data[index].rKey_ = "";
              this.data[index].stackUpCoreDS_ = false;
              this.data[index].waterMark_ = false;
            }
          }
        } else if (record.stackUpMTR_ == "金属基" || record.stackUpMTR_ == "补强") {
          let dataj = JSON.parse(JSON.stringify(this.coreMediumList));

          dataj.forEach(res => {
            if (record.stackUpMTRType_ == res.coreType_) {
              coreData.push(res);
            }
          });
        } else if (record.stackUpMTR_ == "GB") {
          this.gbMediumList.forEach(res => {
            if (record.stackUpMTRType_ == res.coreType_) {
              coreData.push(res);
            }
          });
        } else {
          this.coreMediumList.forEach(res => {
            if (record.stackUpMTRType_ == res.coreType_) {
              coreData.push(res);
            }
          });
        }
        const res = new Map();
        if (record.stackUpMTR_ != "GB") {
          coreData = coreData.filter(a => !res.has(a.core_) && res.set(a.core_, 1));
        }
        return coreData.sort(function (a, b) {
          var value1 = a["core_"];
          var value2 = b["core_"];
          return value1 - value2;
        });
      }
    },
    parseData(str) {
      if (!str) {
        return [];
      }
      const pattern = /^([\d.]+)\(([\d.]+\/[\d.]+)\)\(([^)]+)\)\(([^)]+)\)$/;
      const match = str.match(pattern);
      return match;
    },
    mediumList1(record, index) {
      let topOz = "";
      let bomOz = "";
      let core_ = "";
      let tD_ = false;
      let wM_ = false;
      let pnllist = [];
      let length_ = this.data.length;
      if (record.stackUpMTR_ == "Core") {
        topOz = this.data[index - 1].stackUpMTRFoil_?.split("_PT_")[0];
        bomOz = this.data[index + 1].stackUpMTRFoil_?.split("_PT_")[0];
        core_ = record.stackUpMTRFoil_;
        tD_ = record.stackUpCoreDS_;
        wM_ = record.waterMark_;
      }
      if (record.stackUpMTR_ == "GB") {
        let params = this.parseData(record.stackUpMTRFoil_);
        if (params.length) {
          topOz = params[2].split("/")[0];
          bomOz = params[2].split("/")[1];
          core_ = params[1];
          tD_ = params[3] == "含铜" ? true : false;
          wM_ = params[4] == "有水印" ? true : false;
        }
      }
      if (index == length_ - 1) {
        return [];
      } else {
        this.coreMediumList.forEach(res => {
          if (
            record.stackUpMTRType_ == res.coreType_ &&
            res.topOZ_ == topOz &&
            res.bomOZ_ == bomOz &&
            res.tD_ == tD_ &&
            res.core_ == core_ &&
            res.waterMark_ == wM_
          ) {
            pnllist.push(res);
          }
        });
      }
      const res = new Map();
      pnllist = pnllist.filter(a => !res.has(a.pnlSize_) && res.set(a.pnlSize_, 1));
      return pnllist;
    },
    coreChange(value, index, data) {
      let coreData = [];
      let topOz = this.data[index - 1].stackUpMTRFoil_.split("_PT_")[0];
      let bomOz = this.data[index + 1].stackUpMTRFoil_.split("_PT_")[0];
      this.coreMediumList.forEach(res => {
        if (data.stackUpMTRType_ == res.coreType_ && res.topOZ_ == topOz && res.bomOZ_ == bomOz) {
          coreData.push(res);
        }
      });
      let _value = coreData.find(item => {
        return item.core_ == value;
      });
      let _value1 = coreData.filter(item => {
        return item.core_ == value;
      });
      let _value2 = coreData.filter(item => {
        return item.core_ == value;
      });
      let newobj = {};
      let newobj1 = {};
      _value1 = _value1.reduce((preVal, curVal) => {
        newobj[curVal.tD_] ? "" : (newobj[curVal.tD_] = preVal.push(curVal));
        return preVal;
      }, []);
      this.value1Data = _value1;
      if (_value1.length > 1) {
        _value1.some(item => {
          if (item.tD_) {
            this.data[index].tdFlag_ = "";
          }
        });
      } else {
        this.data[index].tdFlag_ = null;
      }
      _value2 = _value2.reduce((preVal, curVal) => {
        newobj1[curVal.waterMark_] ? "" : (newobj1[curVal.waterMark_] = preVal.push(curVal));
        return preVal;
      }, []);
      if (_value2.length > 1) {
        _value2.some(item => {
          if (item.waterMark_) {
            this.data[index].waterMarkFlag_ = "";
          }
        });
      } else {
        this.data[index].waterMarkFlag_ = null;
      }
      this.data[index].stackUpMTRFoil_ = value;
      this.data[index].stackUpCoreDS_ = _value1[0].tD_;
      this.data[index].waterMark_ = _value1[0].waterMark_;
      this.data[index].rKey_ = _value1[0].rKey_;
      this.data[index].stackUpThichnessORG_ = _value.thicknessCore_; // 物料
      this.data[index].stackUpThichnessMIL_ = _value.thicknessCore_; // 压合
      // debugger
      this.data[index].stackUpThichnessMM_ = this.getFloat(_value.thicknessCore_ * 0.0254, 3); // 成品
      this.data[index].stackUpPNLSize_ = _value.pnlSize_;
      let _coreDk = this.coreMediumList.filter(item => {
        return item.coreType_ == _value.coreType_ && item.coreThickness == _value.coreThickness && item.topOZ_ == topOz && item.bomOZ_ == bomOz;
      });
      this.data[index].stackUpDK_ = _coreDk[0].dkInner_ + "";
    },
    pnlchange(record, index) {
      let topOz = "";
      let bomOz = "";
      let core_ = "";
      let tD_ = false;
      let wM_ = false;
      if (record.stackUpMTR_ == "Core") {
        topOz = this.data[index - 1].stackUpMTRFoil_?.split("_PT_")[0];
        bomOz = this.data[index + 1].stackUpMTRFoil_?.split("_PT_")[0];
        core_ = record.stackUpMTRFoil_;
        tD_ = record.stackUpCoreDS_;
        wM_ = record.waterMark_;
      }
      if (record.stackUpMTR_ == "GB") {
        let params = this.parseData(record.stackUpMTRFoil_);
        if (params.length) {
          topOz = params[2].split("/")[0];
          bomOz = params[2].split("/")[1];
          core_ = params[1];
          tD_ = params[3] == "含铜" ? true : false;
          wM_ = params[4] == "有水印" ? true : false;
        }
      }
      this.coreMediumList.forEach(res => {
        if (
          res.coreType_ == record.stackUpMTRType_ &&
          res.topOZ_ == topOz &&
          res.bomOZ_ == bomOz &&
          res.tD_ == tD_ &&
          res.core_ == core_ &&
          res.pnlSize_ == record.stackUpPNLSize_ &&
          res.waterMark_ == wM_
        ) {
          this.data[index].rKey_ = res.rKey_;
        }
      });
    },
    OzChange(index, data) {
      let coreData = [];
      let topOz = this.data[index - 1].stackUpMTRFoil_.split("_PT_")[0];
      let bomOz = this.data[index + 1].stackUpMTRFoil_.split("_PT_")[0];
      this.coreMediumList.forEach(res => {
        if (data.stackUpMTRType_ == res.coreType_ && res.topOZ_ == topOz && res.bomOZ_ == bomOz && res.core_ == data.stackUpMTRFoil_) {
          coreData.push(res);
        }
      });
      let _value = coreData.find(item => {
        return item.core_ == data.stackUpMTRFoil_;
      });
      let _value1 = coreData.filter(item => {
        return item.core_ == data.stackUpMTRFoil_;
      });
      let filterData1 = coreData.filter(item => {
        if (item.tD_) {
          return item;
        }
      });
      let filterData2 = coreData.filter(item => {
        if (!item.tD_) {
          return item;
        }
      });
      let arr1, arr2;
      if (this.data[index].stackUpPNLSize_) {
        arr1 = filterData1.filter(item => {
          if (this.data[index].stackUpPNLSize_ == item.pnlSize_) {
            return item;
          }
        });
        arr2 = filterData2.filter(item => {
          if (this.data[index].stackUpPNLSize_ == item.pnlSize_) {
            return item;
          }
        });
      }
      filterData1 = arr1 && arr1.length > 0 ? arr1 : filterData1;
      filterData2 = arr2 && arr2.length > 0 ? arr2 : filterData2;
      let _value2 = [];
      let newobj1 = {};
      if (data.stackUpCoreDS_) {
        this.data[index].stackUpMTRFoil_ = filterData1[0].core_.toString(); // 介质  stackUpCoreDS_含铜
        this.data[index].stackUpThichnessORG_ = filterData1[0].thicknessCore_; // 物料 stackUpThichnessORG_
        this.data[index].stackUpThichnessMIL_ = filterData1[0].thicknessCore_; // 压合 stackUpThichnessMIL_
        this.data[index].stackUpThichnessMM_ = this.getFloat(filterData1[0].thicknessCore_ * 0.0254, 3); // 成品 stackUpThichnessMM_
        this.data[index].stackUpPNLSize_ = filterData1[0].pnlSize_; // 大料尺寸
        this.data[index].rKey_ = filterData1[0].rKey_;
        this.data[index].stackUpDK_ = filterData1[0].dK_.toString();
        this.data[index].waterMark_ = filterData1[0].waterMark_;
        _value2 = filterData1.reduce((preVal, curVal) => {
          newobj1[curVal.waterMark_] ? "" : (newobj1[curVal.waterMark_] = preVal.push(curVal));
          return preVal;
        }, []);
        if (_value2.length > 1) {
          _value2.some(item => {
            if (item.waterMark_) {
              this.data[index].waterMarkFlag_ = "";
            }
          });
        } else {
          this.data[index].waterMarkFlag_ = null;
        }
      } else {
        this.data[index].stackUpMTRFoil_ = filterData2[0].core_.toString();
        this.data[index].stackUpThichnessORG_ = filterData2[0].thicknessCore_; // 物料
        this.data[index].stackUpThichnessMIL_ = filterData2[0].thicknessCore_; // 压合
        this.data[index].stackUpThichnessMM_ = this.getFloat(filterData2[0].thicknessCore_ * 0.0254, 3); // 成品
        this.data[index].stackUpPNLSize_ = filterData2[0].pnlSize_;
        this.data[index].rKey_ = filterData2[0].rKey_;
        this.data[index].stackUpDK_ = filterData2[0].dK_.toString();
        this.data[index].waterMark_ = filterData2[0].waterMark_;
        _value2 = filterData2.reduce((preVal, curVal) => {
          newobj1[curVal.waterMark_] ? "" : (newobj1[curVal.waterMark_] = preVal.push(curVal));
          return preVal;
        }, []);
        if (_value2.length > 1) {
          _value2.some(item => {
            if (item.waterMark_) {
              this.data[index].waterMarkFlag_ = "";
            }
          });
        } else {
          this.data[index].waterMarkFlag_ = null;
        }
      }
    },
    OzChange1(index, data) {
      let coreData = [];
      let topOz = this.data[index - 1].stackUpMTRFoil_.split("_PT_")[0];
      let bomOz = this.data[index + 1].stackUpMTRFoil_.split("_PT_")[0];
      this.coreMediumList.forEach(res => {
        if (
          data.stackUpMTRType_ == res.coreType_ &&
          res.topOZ_ == topOz &&
          res.bomOZ_ == bomOz &&
          res.core_ == data.stackUpMTRFoil_ &&
          res.tD_ == data.stackUpCoreDS_
        ) {
          coreData.push(res);
        }
      });
      let filterData1 = coreData.filter(item => {
        if (item.waterMark_) {
          return item;
        }
      });
      let filterData2 = coreData.filter(item => {
        if (!item.waterMark_) {
          return item;
        }
      });
      let arr1, arr2;
      if (this.data[index].stackUpPNLSize_) {
        arr1 = filterData1.filter(item => {
          if (this.data[index].stackUpPNLSize_ == item.pnlSize_) {
            return item;
          }
        });
        arr2 = filterData2.filter(item => {
          if (this.data[index].stackUpPNLSize_ == item.pnlSize_) {
            return item;
          }
        });
      }
      filterData1 = arr1 && arr1.length > 0 ? arr1 : filterData1;
      filterData2 = arr2 && arr2.length > 0 ? arr2 : filterData2;
      if (data.waterMark_) {
        this.data[index].stackUpMTRFoil_ = filterData1[0].core_.toString(); // 介质
        this.data[index].stackUpThichnessORG_ = filterData1[0].thicknessCore_; // 物料 stackUpThichnessORG_
        this.data[index].stackUpThichnessMIL_ = filterData1[0].thicknessCore_; // 压合 stackUpThichnessMIL_
        this.data[index].stackUpThichnessMM_ = this.getFloat(filterData1[0].thicknessCore_ * 0.0254, 3); // 成品 stackUpThichnessMM_
        this.data[index].stackUpPNLSize_ = filterData1[0].pnlSize_; // 大料尺寸
        this.data[index].rKey_ = filterData1[0].rKey_;
        this.data[index].stackUpDK_ = filterData1[0].dK_.toString();
        this.data[index].stackUpCoreDS_ = filterData1[0].tD_;
      } else {
        this.data[index].stackUpMTRFoil_ = filterData2[0].core_.toString();
        this.data[index].stackUpThichnessORG_ = filterData2[0].thicknessCore_; // 物料
        this.data[index].stackUpThichnessMIL_ = filterData2[0].thicknessCore_; // 压合
        this.data[index].stackUpThichnessMM_ = this.getFloat(filterData2[0].thicknessCore_ * 0.0254, 3); // 成品
        this.data[index].stackUpPNLSize_ = filterData2[0].pnlSize_;
        this.data[index].rKey_ = filterData2[0].rKey_;
        this.data[index].stackUpDK_ = filterData2[0].dK_.toString();
        this.data[index].stackUpCoreDS_ = filterData2[0].tD_;
      }
    },
    // 介质的change事件
    handleChange(value, index, data) {
      if (data.stackUpMTR_ != "OZ") {
        if (data.stackUpMTR_ == "Core") {
          const jiezhiListName = this.data.map((item, index) => {
            return { key: index, value: item.stackUpMTR_ };
          });
          let coreListName = jiezhiListName.filter(item => {
            return item.value == "Core";
          });
          if (coreListName.length % 2 != 0) {
            coreListName.splice((coreListName.length - 1) / 2, 1);
            coreListName = coreListName.filter(item => {
              return item.key == index || item.key == this.data.length - index - 1;
            }); // 对称
          } else {
            coreListName = coreListName.filter(item => {
              return item.key == index || item.key == this.data.length - index - 1;
            }); // 对称
          }
          const symmetryCoreIndex = coreListName.map(item => {
            return item.key;
          });
          let previousline = "";
          let core = "";
          let nextline = "";
          let previousline1 = "";
          let core1 = "";
          let nextline1 = "";
          if (this.data[index - 1].stackUpMTR_ == "OZ" && this.data[index + 1].stackUpMTR_ == "OZ") {
            previousline = this.data[index - 1].stackUpMTRFoil_;
            core = this.data[index].stackUpMTRType_;
            nextline = this.data[index + 1].stackUpMTRFoil_;
          }
          if (this.data[this.data.length - index - 2].stackUpMTR_ == "OZ" && this.data[this.data.length - index].stackUpMTR_ == "OZ") {
            previousline1 = this.data[this.data.length - index - 2].stackUpMTRFoil_;
            core1 = this.data[this.data.length - index - 1].stackUpMTRType_;
            nextline1 = this.data[this.data.length - index].stackUpMTRFoil_;
          }
          if (
            symmetryCoreIndex.indexOf(index) != -1 &&
            symmetryCoreIndex.indexOf(index) <= (coreListName.length - 1) / 2 &&
            previousline == previousline1 &&
            nextline == nextline1 &&
            core == core1
          ) {
            coreListName.forEach(item => {
              this.coreChange(value, item.key, data);
            });
          } else {
            this.coreChange(value, index, data);
          }
        } else if (data.stackUpMTR_ == "金属基" || data.stackUpMTR_ == "补强") {
          this.jsjChange(value, index, data);
        } else if (data.stackUpMTR_ == "GB") {
          this.GBChange(value, index, data);
        } else {
          // 123
          this.otherChange(value, index, data);
        }
      } else {
        let _arr,
          list,
          leng_ = this.data.length;
        if (index == 0 || index == this.data.length - 1) {
          _arr = this.someList.filter(item => {
            return item.iO_ == 910;
          }); //外层
          list = this.ozMediumList2;
        } else {
          _arr = this.someList.filter(item => {
            return item.iO_ == 911;
          }); //内层
          list = this.ozMediumList;
        }
        list.filter(res => {
          if (res.cuType_ == value) {
            this.data[index].stackUpT1_ = res.t1Value_ + "";
            this.data[index].stackUpThichnessORG_ = res.cuThicknessOrg_; // 物料厚度
            this.data[index].stackUpThichnessMIL_ = res.cuThickness_; // 压合厚度
            this.data[index].stackUpThichnessMM_ = this.getFloat(res.cuThickness_ * 0.0254, 4); // 成品厚度
            this.data[index].rKey_ = res.rKey_;
            // if (index == leng_-1) {
            //   return
            // }
            // if (index == 0) {
            //   if (this.data[leng_ - 1].stackUpMTR_ == 'OZ') {
            //     this.handleChange(value, leng_ - 1,data)
            //     this.data[leng_ - 1].stackUpMTRFoil_= value
            //   }
            //   return
            // }
            var Symmetricalrow = leng_ - (index + 1);
            if (Symmetricalrow > leng_ / 2) {
              this.data[Symmetricalrow].stackUpMTRFoil_ = value;
              this.data[Symmetricalrow].stackUpT1_ = res.t1Value_ + "";
              this.data[Symmetricalrow].stackUpThichnessORG_ = res.cuThicknessOrg_; // 物料厚度
              this.data[Symmetricalrow].stackUpThichnessMIL_ = res.cuThickness_; // 压合厚度
              this.data[Symmetricalrow].stackUpThichnessMM_ = this.getFloat(res.cuThickness_ * 0.0254, 4); // 成品厚度
              this.data[Symmetricalrow].rKey_ = res.rKey_;
            }
            if (index + 2 < leng_ && this.data[index + 1].stackUpMTR_ == "Core" && this.data[index + 2].stackUpMTR_ == "OZ") {
              this.data[index + 2].stackUpMTRFoil_ = value;
              this.data[index + 2].stackUpT1_ = res.t1Value_ + "";
              this.data[index + 2].stackUpThichnessORG_ = res.cuThicknessOrg_; // 物料厚度
              this.data[index + 2].stackUpThichnessMIL_ = res.cuThickness_; // 压合厚度
              this.data[index + 2].stackUpThichnessMM_ = this.getFloat(res.cuThickness_ * 0.0254, 4); // 成品厚度
              this.data[index + 2].rKey_ = res.rKey_;
              this.handleChange(value, index + 2, data);
              return;
            }
          }
        });
        let coreData_ = [];
        if (this.data.length > 1) {
          if (this.data[index + 1]?.stackUpMTR_ == "Core") {
            // this.data[index+1].stackUpMTRFoil_ = '';
            // 下一层是core
            let changeTopOz = data.stackUpMTRFoil_.split("_PT_")[0];
            let changeBomOz = this.data[index + 2].stackUpMTRFoil_.split("_PT_")[0];
            this.coreMediumList.forEach(res => {
              if (this.data[index + 1].stackUpMTRType_ == res.coreType_ && res.topOZ_ == changeTopOz && res.bomOZ_ == changeBomOz) {
                coreData_.push(res);
              }
            });
            if (coreData_.length == 0 && index + 1 < this.data.length - 1) {
              this.data[index + 1].stackUpDK_ = "";
              this.data[index + 1].rKey_ = "";
              this.data[index + 1].stackUpMTRFoil_ = "";
              this.data[index + 1].stackUpThichnessMIL_ = "";
              this.data[index + 1].stackUpThichnessMM_ = "";
              this.data[index + 1].stackUpThichnessORG_ = "";
            } else {
              let _value1 = coreData_.filter(item => {
                return item.core_ == this.data[index + 1].stackUpMTRFoil_;
              });
              let newobj = {};
              _value1 = _value1.reduce((preVal, curVal) => {
                newobj[curVal.tD_] ? "" : (newobj[curVal.tD_] = preVal.push(curVal));
                return preVal;
              }, []);
              if (_value1.length > 1) {
                _value1.some(item => {
                  if (item.tD_) {
                    this.data[index + 1].tdFlag_ = "";
                  }
                });
                this.data[index + 1].stackUpCoreDS_ = _value1[0].tD_;
                this.data[index + 1].waterMark_ = _value1[0].waterMark_;
              } else {
                this.data[index + 1].tdFlag_ = null;
              }
              let _value2 = coreData_.filter(item => {
                return item.core_ == value;
              });
              let newobj1 = {};
              _value2 = _value2.reduce((preVal, curVal) => {
                newobj1[curVal.waterMark_] ? "" : (newobj1[curVal.waterMark_] = preVal.push(curVal));
                return preVal;
              }, []);
              if (_value2.length > 1) {
                _value2.some(item => {
                  if (item.waterMark_) {
                    this.data[index + 1].waterMarkFlag_ = "";
                  }
                });
                this.data[index + 1].waterMark_ = _value2[0].waterMark_;
              } else {
                this.data[index + 1].waterMarkFlag_ = null;
              }
            }
          }
          if (this.data[index - 1]?.stackUpMTR_ == "Core" && this.data[index + 1]?.stackUpMTR_ != "PP") {
            // 上一层是core
            this.coreMediumList.forEach(res => {
              if (
                this.data[index - 1].stackUpMTRType_ == res.coreType_ &&
                res.topOZ_ == this.data[index - 2].stackUpMTRFoil_.split("_PT_")[0] &&
                res.bomOZ_ == data.stackUpMTRFoil_.split("_PT_")[0]
              ) {
                coreData_.push(res);
              }
            });
            if (coreData_.length == 0 && index + 1 < this.data.length - 1) {
              this.data[index + 1].stackUpDK_ = "";
              this.data[index + 1].rKey_ = "";
              this.data[index + 1].stackUpMTRFoil_ = "";
              this.data[index + 1].stackUpThichnessMIL_ = "";
              this.data[index + 1].stackUpThichnessMM_ = "";
              this.data[index + 1].stackUpThichnessORG_ = "";
            }
          }
        }
        this.$emit("assignment");
      }
      this.$forceUpdate();
    },
    //
    wmhandleChange(index, data) {
      this.$store.commit("changeInfo", { pressingThickness: "" });
      this.$store.commit("changeInfo", { finishedThickness: "" });
      this.$emit("pressingThicknessBgChange");
      this.OzChange1(index, data);
      this.pnlchange(data, index);
    },
    handleChange1(index, data) {
      this.$store.commit("changeInfo", { pressingThickness: "" });
      this.$store.commit("changeInfo", { finishedThickness: "" });
      this.$emit("pressingThicknessBgChange");
      this.OzChange(index, data);
      this.pnlchange(data, index);
    },
    ppClick(record, index) {
      this.$emit("showModel", { record: record, index: index });
    },
    GBChange(value, index, data) {
      const arr = this.coreMediumList.filter(it => {
        return it.rKey_ == value;
      });
      let vastr = arr[0].tD_
        ? arr[0].core_ + "(" + arr[0].topOZ_ + "/" + arr[0].bomOZ_ + ")(含铜)" + (arr[0].waterMark_ ? "(有水印)" : "(无水印)")
        : arr[0].core_ + "(" + arr[0].topOZ_ + "/" + arr[0].bomOZ_ + ")(不含铜)" + (arr[0].waterMark_ ? "(有水印)" : "(无水印)");
      this.data[index].stackUpMTRFoil_ = vastr;
      this.data[index].stackUpCoreDS_ = arr[0].tD_;
      this.data[index].waterMark_ = arr[0].waterMark_;
      this.data[index].stackUpThichnessORG_ = arr[0].thicknessCore_;
      this.data[index].stackUpThichnessMIL_ = arr[0].thicknessCore_;
      // this.data[index].stackUpThichnessMM_ =  arr[0].coreThickness;  //成品厚度
      this.data[index].stackUpThichnessMM_ = this.getFloat(arr[0].thicknessCore_ * 0.0254, 3);
      this.data[index].stackUpPNLSize_ = arr[0].pnlSize_;
      this.data[index].rKey_ = arr[0].rKey_;
      let _coreDk = this.coreMediumList.filter(item => {
        return item.coreType_ == arr[0].coreType_ && item.coreThickness == arr[0].coreThickness;
      });
      this.data[index].stackUpDK_ = _coreDk[0].dkInner_ + "";
      if (data.stackUpMTRFoil_) {
        if (data.stackUpMTRFoil_.indexOf("PT") == -1) {
          this.data[index].stackUpCuMin4T1_ = "";
          this.data[index].stackUpCuMax4T1_ = "";
        }
      }
    },
    otherChange(value, index, data) {
      let coreData = [];
      this.coreMediumList.forEach(res => {
        if (data.stackUpMTRType_ == res.coreType_) {
          coreData.push(res);
        }
      });
      let _value = coreData.find(item => {
        return item.core_ == value;
      });
      let _value1 = coreData.filter(item => {
        return item.core_ == value;
      });
      let _value2 = coreData.filter(item => {
        return item.core_ == value;
      });
      let newobj = {};
      let newobj1 = {};
      _value1 = _value1.reduce((preVal, curVal) => {
        newobj[curVal.tD_] ? "" : (newobj[curVal.tD_] = preVal.push(curVal));
        return preVal;
      }, []);
      _value2 = _value2.reduce.reduce((preVal, curVal) => {
        newobj1[curVal.waterMark_] ? "" : (newobj1[curVal.waterMark_] = preVal.push(curVal));
        return preVal;
      }, []);
      if (_value1.length > 1) {
        _value1.some(item => {
          if (item.tD_) {
            this.data[index].tdFlag_ = "";
          }
        });
      } else {
        this.data[index].tdFlag_ = null;
      }
      if (_value2.length > 1) {
        _value2.some(item => {
          if (item.waterMark_) {
            this.data[index].waterMarkFlag_ = "";
          }
        });
      } else {
        this.data[index].waterMarkFlag_ = null;
      }
      // debugger
      this.data[index].stackUpMTRFoil_ = value;
      this.data[index].stackUpCoreDS_ = _value1[0].tD_;
      this.data[index].waterMark_ = _value1[0].waterMark_;
      this.data[index].stackUpThichnessORG_ = _value.thicknessCore_;
      this.data[index].stackUpThichnessMIL_ = _value.thicknessCore_;
      this.data[index].stackUpThichnessMM_ = _value.coreThickness;
      this.data[index].stackUpPNLSize_ = _value.pnlSize_;
      this.data[index].rKey_ = _value1[0].rKey_;
      let _coreDk = this.coreMediumList.filter(item => {
        return item.coreType_ == _value.coreType_ && item.coreThickness == _value.coreThickness;
      });
      this.data[index].stackUpDK_ = _coreDk[0].dkInner_ + "";
      if (data.stackUpMTRFoil_) {
        if (data.stackUpMTRFoil_.indexOf("PT") == -1) {
          this.data[index].stackUpCuMin4T1_ = "";
          this.data[index].stackUpCuMax4T1_ = "";
        }
      }
    },
    jsjChange(value, index, data) {
      let coreData = [];
      this.coreMediumList.forEach(res => {
        if (data.stackUpMTRType_ == res.coreType_) {
          coreData.push(res);
        }
      });
      let _value = coreData.find(item => {
        return item.core_ == value;
      });
      let _value1 = coreData.filter(item => {
        return item.core_ == value;
      });
      let _value2 = coreData.filter(item => {
        return item.core_ == value;
      });
      let newobj = {};
      let newobj1 = {};
      _value1 = _value1.reduce((preVal, curVal) => {
        newobj[curVal.tD_] ? "" : (newobj[curVal.tD_] = preVal.push(curVal));
        return preVal;
      }, []);
      _value2 = _value2.reduce((preVal, curVal) => {
        newobj1[curVal.waterMark_] ? "" : (newobj[curVal.waterMark_] = preVal.push(curVal));
        return preVal;
      }, []);
      if (_value1.length > 1) {
        _value1.some(item => {
          if (item.tD_) {
            this.data[index].tdFlag_ = "";
          }
        });
      } else {
        this.data[index].tdFlag_ = null;
      }

      if (_value2.length > 1) {
        _value1.some(item => {
          if (item.waterMark_) {
            this.data[index].waterMarkFlag_ = "";
          }
        });
      } else {
        this.data[index].waterMarkFlag_ = null;
      }
      // debugger
      this.data[index].stackUpMTRFoil_ = value + "(" + _value.topOZ_ + "/" + _value.bomOZ_ + ")";
      this.data[index].stackUpCoreDS_ = _value1[0].tD_;
      this.data[index].waterMark_ = _value1[0].waterMark_;
      this.data[index].stackUpThichnessORG_ = _value.thicknessCore_;
      this.data[index].stackUpThichnessMIL_ = _value.thicknessCore_;
      this.data[index].stackUpThichnessMM_ = _value.coreThickness;
      this.data[index].stackUpPNLSize_ = _value.pnlSize_;
      this.data[index].rKey_ = _value.rKey_;
      let _coreDk = this.coreMediumList.filter(item => {
        return item.coreType_ == _value.coreType_ && item.coreThickness == _value.coreThickness;
      });
      this.data[index].stackUpDK_ = _coreDk[0].dkInner_ + "";
    },
    stackChange(index, ty) {
      if (!ty) {
        this.data[index].stackUpMTRFoil_ = "";
        this.data[index].stackUpDK_ = "";
        this.data[index].stackUpPNLSize_ = "";
        this.data[index].stackUpThichnessMIL_ = "";
        this.data[index].stackUpThichnessMM_ = "";
        this.data[index].stackUpThichnessORG_ = "";
      }
      // this.data[index].stackUpMTRFoil_ = ''

      this.$forceUpdate();
    },
    customClick(record, index) {
      return {
        on: {
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            // if(e.target.className.indexOf('index_') == -1) {
            //   return
            // }
            this.activeIndex = index;
            //this.menuVisible = true;
            this.rightClick(e, index);
          },
        },
      };
    },
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    leftclick(record, text, index) {
      this.activeIndex = index;
      if (JSON.stringify(text) != "{}") {
        this.ins = true;
        this.del = true;
      } else {
        this.ins = false;
        this.del = true;
      }
      this.$emit("insert1", this.ins, 1);
    },
    rightClick(e, index) {
      let event = e.target;
      if (
        event.className.indexOf("ant-input") != -1 ||
        event.className.indexOf("ant-select-selection-selected-value") != -1 ||
        event.className.indexOf("ant-checkbox-input") != -1
      ) {
        this.menuVisible = false;
      } else {
        this.menuVisible = true;
      }
      this.activeIndex = index;
      this.menuStyle.top = e.clientY - 140 + "px";
      let multiple = this.collapse ? 0.2 : 0.02;
      this.menuStyle.left = e.clientX - window.innerWidth * multiple - 150 + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    addRow() {
      this.data.splice(this.activeIndex + 1, 0, {
        no: 0,
        iD_: this.activeIndex + 1,
        rKey_: null,
        remark_: null,
        stackUpCTLMI_: null,
        stackUpCUTB_: null,
        stackUpCoreDS_: null,
        waterMark_: null,
        stackUpDK_: null,
        stackUpKey4MTR_: null,
        stackUpLayerNo_: null,
        stackUpMTRFoil_: null,
        stackUpMTRType_: null,
        stackUpMTR_: null,
        stackUpOZLayer_: null,
        stackUpPNLSize_: null,
        stackUpPPFillGlue_: null,
        stackUpT1_: null,
        stackUpThichnessMIL_: null,
        stackUpThichnessMM_: null,
        stackUpThichnessORG_: null,
        tdFlag_: null,
        waterMarkFlag_: null,
        stackUpCuMin4T1_: null,
        stackUpCuMax4T1_: null,
        stackUpCuType_: null,
      });
    },
    delectRow() {
      this.data.splice(this.activeIndex, 1);
      this.setedit(true);
    },
    stackTypeFilter(val) {
      if (val == "金属基" || val == "补强") {
        return this.boardTypeList.filter(item => {
          return item.category == val;
        });
      } else {
        return this.boardTypeList;
      }
    },
    ozSame() {
      const jiezhiListName = this.data.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      let ozListName = jiezhiListName.filter(item => {
        return item.value == "OZ";
      });
      this.data[ozListName[ozListName.length - 1].key].stackUpMTRType_ = this.data[ozListName[0].key].stackUpMTRType_;
      this.data[ozListName[ozListName.length - 1].key].stackUpMTRFoil_ = this.data[ozListName[0].key].stackUpMTRFoil_;
      this.data[ozListName[ozListName.length - 1].key].stackUpCoreDS_ = this.data[ozListName[0].key].stackUpCoreDS_;
      this.data[ozListName[ozListName.length - 1].key].waterMark_ = this.data[ozListName[0].key].waterMark_;
      this.data[ozListName[ozListName.length - 1].key].stackUpThichnessORG_ = this.data[ozListName[0].key].stackUpThichnessORG_;
      this.data[ozListName[ozListName.length - 1].key].stackUpThichnessMIL_ = this.data[ozListName[0].key].stackUpThichnessMIL_;
      this.data[ozListName[ozListName.length - 1].key].stackUpThichnessMM_ = this.data[ozListName[0].key].stackUpThichnessMM_;
      //this.data[ozListName[ozListName.length-1].key].stackUpCTLMI_ = this.data[ozListName[0].key].stackUpCTLMI_
      // this.data[ozListName[ozListName.length-1].key].stackUpCUTB_ = this.data[ozListName[0].key].stackUpCUTB_
      this.data[ozListName[ozListName.length - 1].key].stackUpDK_ = this.data[ozListName[0].key].stackUpDK_;
      this.data[ozListName[ozListName.length - 1].key].stackUpPNLSize_ = this.data[ozListName[0].key].stackUpPNLSize_;
      this.data[ozListName[ozListName.length - 1].key].CuMin = this.data[ozListName[0].key].CuMin;
      this.data[ozListName[ozListName.length - 1].key].CuMax = this.data[ozListName[0].key].CuMax;
      this.data[ozListName[ozListName.length - 1].key].stackUpT1_ = this.data[ozListName[0].key].stackUpT1_;
      this.data[ozListName[ozListName.length - 1].key].stackUpCuType_ = this.data[ozListName[0].key].stackUpCuType_;
      this.data[ozListName[ozListName.length - 1].key].ppThickness4NY_ = this.data[ozListName[0].key].ppThickness4NY_;
      delete ozListName[0];
      delete ozListName[ozListName.length - 1];
      let firstIndex = ozListName[1].key;
      const firstozpre = this.data[firstIndex];
      if (ozListName.length) {
        for (let index = 1; index < ozListName.length - 1; index++) {
          this.data[ozListName[index].key].stackUpMTRType_ = firstozpre.stackUpMTRType_;
          this.data[ozListName[index].key].stackUpMTRFoil_ = firstozpre.stackUpMTRFoil_;
          this.data[ozListName[index].key].stackUpCoreDS_ = firstozpre.stackUpCoreDS_;
          this.data[ozListName[index].key].waterMark_ = firstozpre.waterMark_;
          this.data[ozListName[index].key].stackUpThichnessORG_ = firstozpre.stackUpThichnessORG_;
          this.data[ozListName[index].key].stackUpThichnessMIL_ = firstozpre.stackUpThichnessMIL_;
          this.data[ozListName[index].key].stackUpThichnessMM_ = firstozpre.stackUpThichnessMM_;
          //this.data[ozListName[index].key].stackUpCTLMI_ = firstozpre.stackUpCTLMI_
          // this.data[ozListName[index].key].stackUpCUTB_ = firstozpre.stackUpCUTB_
          this.data[ozListName[index].key].stackUpDK_ = firstozpre.stackUpDK_;
          this.data[ozListName[index].key].stackUpPNLSize_ = firstozpre.stackUpPNLSize_;
          this.data[ozListName[index].key].CuMin = firstozpre.CuMin;
          this.data[ozListName[index].key].CuMax = firstozpre.CuMax;
          this.data[ozListName[index].key].stackUpT1_ = firstozpre.stackUpT1_;
          this.data[ozListName[index].key].stackUpCuType_ = firstozpre.stackUpCuType_;
          this.data[ozListName[index].key].ppThickness4NY_ = firstozpre.ppThickness4NY_;
        }
      }
    },
    ozsymmetry() {
      const jiezhiListName = this.data.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      let ozListName = jiezhiListName.filter(item => {
        return item.value == "OZ";
      });
      var a = [];
      for (let index = 0; index < ozListName.length; index++) {
        a.push(ozListName[index].key);
      }
      var b = a.slice(0, Math.floor(ozListName.length / 2));
      var c = a.slice(-Math.floor(ozListName.length / 2));
      var arr = [...b, ...c];
      for (let i = 0; i < arr.length / 2; i++) {
        this.data[arr[arr.length - 1 - i]].stackUpMTRType_ = this.data[arr[i]].stackUpMTRType_;
        this.data[arr[arr.length - 1 - i]].stackUpMTRFoil_ = this.data[arr[i]].stackUpMTRFoil_;
        this.data[arr[arr.length - 1 - i]].stackUpCoreDS_ = this.data[arr[i]].stackUpCoreDS_;
        this.data[arr[arr.length - 1 - i]].waterMark_ = this.data[arr[i]].waterMark_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessORG_ = this.data[arr[i]].stackUpThichnessORG_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessMM_ = this.data[arr[i]].stackUpThichnessMM_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessMIL_ = this.data[arr[i]].stackUpThichnessMIL_;
        this.data[arr[arr.length - 1 - i]].stackUpCUTB_ = this.data[arr[i]].stackUpCUTB_;
        this.data[arr[arr.length - 1 - i]].stackUpDK_ = this.data[arr[i]].stackUpDK_;
        this.data[arr[arr.length - 1 - i]].CuMin = this.data[arr[i]].CuMin;
        this.data[arr[arr.length - 1 - i]].CuMax = this.data[arr[i]].CuMax;
        this.data[arr[arr.length - 1 - i]].stackUpT1_ = this.data[arr[i]].stackUpT1_;
        this.data[arr[arr.length - 1 - i]].stackUpCuType_ = this.data[arr[i]].stackUpCuType_;
        this.data[arr[arr.length - 1 - i]].ppThickness4NY_ = this.data[arr[i]].ppThickness4NY_;
      }
    },
    PPsame() {
      const jiezhiListName = this.data.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      let ppListName = jiezhiListName.filter(item => {
        return item.value == "PP";
      });
      var a = Math.floor(ppListName.length / 2);
      let firstIndex = ppListName[0].key;
      const firstpppre = this.data[firstIndex];
      if (ppListName.length) {
        for (let index = 0; index < ppListName.length; index++) {
          if (index > ppListName.length - a - 1) {
            this.data[ppListName[index].key].stackUpMTRFoil_ = firstpppre.stackUpMTRFoil_.split("+").reverse().join("+");
            this.data[ppListName[index].key].rKey_ = firstpppre.rKey_.split("+").reverse().join("+");
          } else {
            this.data[ppListName[index].key].stackUpMTRFoil_ = firstpppre.stackUpMTRFoil_;
            this.data[ppListName[index].key].rKey_ = firstpppre.rKey_;
          }
          this.data[ppListName[index].key].stackUpMTRType_ = firstpppre.stackUpMTRType_;
          this.data[ppListName[index].key].stackUpCoreDS_ = firstpppre.stackUpCoreDS_;
          this.data[ppListName[index].key].waterMark_ = firstpppre.waterMark_;
          this.data[ppListName[index].key].stackUpThichnessORG_ = firstpppre.stackUpThichnessORG_;
          this.data[ppListName[index].key].stackUpThichnessMIL_ = firstpppre.stackUpThichnessMIL_;
          this.data[ppListName[index].key].stackUpThichnessMM_ = firstpppre.stackUpThichnessMM_;
          this.data[ppListName[index].key].stackUpCTLMI_ = firstpppre.stackUpCTLMI_;
          this.data[ppListName[index].key].stackUpCUTB_ = firstpppre.stackUpCUTB_;
          this.data[ppListName[index].key].stackUpDK_ = firstpppre.stackUpDK_;
          this.data[ppListName[index].key].stackUpPNLSize_ = firstpppre.stackUpPNLSize_;
          this.data[ppListName[index].key].CuMin = firstpppre.CuMin;
          this.data[ppListName[index].key].CuMax = firstpppre.CuMax;
          this.data[ppListName[index].key].stackUpT1_ = firstpppre.stackUpT1_;
          this.data[ppListName[index].key].stackUpCuType_ = firstpppre.stackUpCuType_;
          this.data[ppListName[index].key].ppThickness4NY_ = firstpppre.ppThickness4NY_;
          this.data[ppListName[index].key].ppThickness4Gllass_ = firstpppre.ppThickness4Gllass_;
        }
      }
    },
    PPsymmetry() {
      const jiezhiListName = this.data.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      let ppListName = jiezhiListName.filter(item => {
        return item.value == "PP";
      });
      var a = [];
      for (let index = 0; index < ppListName.length; index++) {
        a.push(ppListName[index].key);
      }
      var b = a.slice(0, Math.floor(ppListName.length / 2));
      var c = a.slice(-Math.floor(ppListName.length / 2));
      var arr = [...b, ...c];
      for (let i = 0; i < arr.length / 2; i++) {
        this.data[arr[arr.length - 1 - i]].stackUpMTRType_ = this.data[arr[i]].stackUpMTRType_;
        this.data[arr[arr.length - 1 - i]].stackUpMTRFoil_ = this.data[arr[i]].stackUpMTRFoil_.split("+").reverse().join("+");
        this.data[arr[arr.length - 1 - i]].rKey_ = this.data[arr[i]].rKey_.split("+").reverse().join("+");
        this.data[arr[arr.length - 1 - i]].stackUpCoreDS_ = this.data[arr[i]].stackUpCoreDS_;
        this.data[arr[arr.length - 1 - i]].waterMark_ = this.data[arr[i]].waterMark_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessORG_ = this.data[arr[i]].stackUpThichnessORG_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessMM_ = this.data[arr[i]].stackUpThichnessMM_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessMIL_ = this.data[arr[i]].stackUpThichnessMIL_;
        this.data[arr[arr.length - 1 - i]].stackUpCUTB_ = this.data[arr[i]].stackUpCUTB_;
        this.data[arr[arr.length - 1 - i]].stackUpDK_ = this.data[arr[i]].stackUpDK_;
        this.data[arr[arr.length - 1 - i]].CuMin = this.data[arr[i]].CuMin;
        this.data[arr[arr.length - 1 - i]].CuMax = this.data[arr[i]].CuMax;
        this.data[arr[arr.length - 1 - i]].stackUpT1_ = this.data[arr[i]].stackUpT1_;
        this.data[arr[arr.length - 1 - i]].stackUpCuType_ = this.data[arr[i]].stackUpCuType_;
        this.data[arr[arr.length - 1 - i]].ppThickness4NY_ = this.data[arr[i]].ppThickness4NY_;
      }
    },
    coreSame() {
      const jiezhiListName = this.data.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      let coreListName = jiezhiListName.filter(item => {
        return item.value == "Core";
      });
      let firstIndex = coreListName[0].key;
      const firstozpre = this.data[firstIndex - 1];
      const firstoznext = this.data[firstIndex + 1];
      const firststackUpMTRType_ = this.data[firstIndex];
      if (coreListName.length) {
        for (var a = 0; a < coreListName.length; a++) {
          if (this.data[coreListName[a].key - 1].stackUpMTR_ == "OZ") {
            this.data[coreListName[a].key - 1].stackUpMTRFoil_ = firstozpre.stackUpMTRFoil_;
            this.handleChange(firstozpre.stackUpMTRFoil_, coreListName[a].key - 1, firstozpre);
          }
          if (this.data[coreListName[a].key + 1].stackUpMTR_ == "OZ") {
            this.data[coreListName[a].key + 1].stackUpMTRFoil_ = firstoznext.stackUpMTRFoil_;
            this.handleChange(firstoznext.stackUpMTRFoil_, coreListName[a].key + 1, firstoznext);
          }
          if (this.data[coreListName[a].key - 1].stackUpMTR_ == "OZ" && this.data[coreListName[a].key + 1].stackUpMTR_ == "OZ") {
            this.data[coreListName[a].key].stackUpMTRType_ = firststackUpMTRType_.stackUpMTRType_;
            this.data[coreListName[a].key].stackUpMTRFoil_ = firststackUpMTRType_.stackUpMTRFoil_;
            this.handleChange(firststackUpMTRType_.stackUpMTRFoil_, coreListName[a].key, firststackUpMTRType_);
          }
        }
      }
      this.$forceUpdate();
    },
    coresymmetry() {
      this.ozsymmetry();
      const jiezhiListName = this.data.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      let coreListName = jiezhiListName.filter(item => {
        return item.value == "Core";
      });
      var a = [];
      for (let index = 0; index < coreListName.length; index++) {
        a.push(coreListName[index].key);
      }
      var b = a.slice(0, Math.floor(coreListName.length / 2));
      var c = a.slice(-Math.floor(coreListName.length / 2));
      var arr = [...b, ...c];
      for (let i = 0; i < arr.length / 2; i++) {
        this.data[arr[arr.length - 1 - i]].stackUpMTRType_ = this.data[arr[i]].stackUpMTRType_;
        this.data[arr[arr.length - 1 - i]].stackUpMTRFoil_ = this.data[arr[i]].stackUpMTRFoil_;
        this.data[arr[arr.length - 1 - i]].stackUpCoreDS_ = this.data[arr[i]].stackUpCoreDS_;
        this.data[arr[arr.length - 1 - i]].waterMark_ = this.data[arr[i]].waterMark_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessORG_ = this.data[arr[i]].stackUpThichnessORG_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessMM_ = this.data[arr[i]].stackUpThichnessMM_;
        this.data[arr[arr.length - 1 - i]].stackUpThichnessMIL_ = this.data[arr[i]].stackUpThichnessMIL_;
        this.data[arr[arr.length - 1 - i]].stackUpCUTB_ = this.data[arr[i]].stackUpCUTB_;
        this.data[arr[arr.length - 1 - i]].stackUpDK_ = this.data[arr[i]].stackUpDK_;
        this.data[arr[arr.length - 1 - i]].CuMin = this.data[arr[i]].CuMin;
        this.data[arr[arr.length - 1 - i]].CuMax = this.data[arr[i]].CuMax;
        this.data[arr[arr.length - 1 - i]].stackUpT1_ = this.data[arr[i]].stackUpT1_;
        this.data[arr[arr.length - 1 - i]].stackUpCuType_ = this.data[arr[i]].stackUpCuType_;
        this.data[arr[arr.length - 1 - i]].ppThickness4NY_ = this.data[arr[i]].ppThickness4NY_;
      }
    },
    Residualcopperrate() {
      let params = {
        joinFactoryId: this.fac,
        pdctno: this.form.pdctno,
        stackUpOutputs: this.data,
      };
      setstackupcopperratio(params).then(res => {
        if (res.code) {
          this.$message.success("导入成功");
          res.data.forEach((item, index) => {
            this.data[index].stackUpCTLMI_ = item.stackUpCTLMI_;
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Insertopticalboard() {
      const jiezhiListName = this.data.map((item, index) => {
        return { key: index, value: item.stackUpMTR_ };
      });
      for (let index = 0; index < jiezhiListName.length; index++) {
        if (jiezhiListName[index].key == this.activeIndex && jiezhiListName[index].value != "PP") {
          this.$message.error("请选择需要插入光板的PP所在行");
          return;
        }
      }
      this.data.splice(this.activeIndex + 1, 0, {
        no: 0,
        iD_: this.activeIndex + 1,
        ppThickness4Gllass_: null,
        ppThickness4NY_: "",
        rKey_: null,
        remark_: null,
        stackUpCTLMI_: null,
        stackUpCUTB_: null,
        stackUpCoreDS_: null,
        stackUpDK_: null,
        stackUpKey4MTR_: null,
        stackUpLayerNo_: null,
        stackUpMTRFoil_: null,
        stackUpOZLayer_: null,
        stackUpPNLSize_: null,
        stackUpPPFillGlue_: null,
        stackUpT1_: null,
        stackUpThichnessMIL_: null,
        stackUpThichnessMM_: null,
        stackUpThichnessORG_: null,
        tdFlag_: null,
        waterMarkFlag_: null,
        stackUpCuMin4T1_: null,
        stackUpCuMax4T1_: null,
        stackUpCuType_: null,
        stackUpMTRType_: this.labelOrValue(this.boardTypeList, this.form.boardType),
        stackUpMTR_: "GB",
      });
      this.data.splice(this.activeIndex + 2, 0, {
        no: 0,
        iD_: this.activeIndex + 2,
        ppThickness4Gllass_: null,
        ppThickness4NY_: "",
        rKey_: null,
        remark_: null,
        stackUpCTLMI_: null,
        stackUpCUTB_: null,
        stackUpCoreDS_: null,
        stackUpDK_: null,
        stackUpKey4MTR_: null,
        stackUpLayerNo_: null,
        stackUpMTRFoil_: null,
        stackUpOZLayer_: null,
        stackUpPNLSize_: null,
        stackUpPPFillGlue_: null,
        stackUpT1_: null,
        stackUpThichnessMIL_: null,
        stackUpThichnessMM_: null,
        stackUpThichnessORG_: null,
        tdFlag_: null,
        waterMarkFlag_: null,
        stackUpCuMin4T1_: null,
        stackUpCuMax4T1_: null,
        stackUpCuType_: null,
        stackUpMTRType_: this.data[this.activeIndex].stackUpMTRType_,
        stackUpMTR_: this.data[this.activeIndex].stackUpMTR_,
      });
    },
    handlePressEnter1(record, index, dataIndex) {
      const currentRef = this.$refs[`row-${index}-col-${dataIndex}`];
      const columnRefIndex = this.columns.findIndex(column => column.key === dataIndex);
      if (index === this.data.length - 1 || (dataIndex == "stackUpMTRType_" && index == this.data.length - 2)) {
        const nextColumnIndex = columnRefIndex + 1;
        let nextColumnDataIndex = "";
        if (this.columns[nextColumnIndex]) {
          nextColumnDataIndex = this.columns[nextColumnIndex].key;
        }

        if (dataIndex == "stackUpMTRFoil_") {
          this.setFocus("stackUpCTLMI_", 0);
        } else if (dataIndex == "stackUpCUTB_") {
          this.setFocus("stackUpCuMin4T1_", 0);
        } else if (dataIndex == "stackUpCuMax4T1_") {
          this.setFocus("stackUpCuType_", 0);
        } else {
          this.setFocus(nextColumnDataIndex, 0);
        }

        // if(columnRefIndex == 21 && this.data.length){
        //   this.setFocus('minLineW_', 1)
        // }
      } else {
        const nextColumnIndex1 = columnRefIndex;
        const nextColumnDataIndex1 = this.columns[nextColumnIndex1].key;
        let nextColumnDatadrillName_ = "";
        if (
          dataIndex == "stackUpLayerNo_" ||
          dataIndex == "stackUpMTRType_" ||
          dataIndex == "stackUpCTLMI_" ||
          dataIndex == "stackUpCUTB_" ||
          dataIndex == "stackUpCuType_"
        ) {
          nextColumnDatadrillName_ = index + 2;
        } else if (dataIndex == "stackUpCuMin4T1_" || dataIndex == "stackUpCuMax4T1_") {
          nextColumnDatadrillName_ = this.data.length - 1;
        } else {
          nextColumnDatadrillName_ = index + 1;
        }
        this.setFocus(nextColumnDataIndex1, nextColumnDatadrillName_);
      }
      currentRef.blur();
    },

    setFocus(dataIndex, rowId) {
      if (dataIndex) {
        if (dataIndex == "stackUpMTRType_" && rowId == 0) {
          rowId = 1;
        }
        const refName = `row-${rowId}-col-${dataIndex}`;
        const ref = this.$refs[refName];
        if (ref) {
          ref.focus();
          this.flag = true;
        } else {
          if (this.flag == true) {
            this.setFocus(dataIndex, rowId + 1);
          }
          this.flag = false;
        }
      }
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    document.addEventListener("click", this.handleClickOutside);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    document.removeEventListener("click", this.handleClickOutside);
  },
};
</script>

<style lang="less" scoped>
/deep/.dragging2 {
  display: block !important;
  top: 0 !important;
  .handle {
    width: 1px;
    height: 1px;
    border: 1px solid #8fbc8b;
    z-index: 9999;
    display: block !important;
  }
  .handle-tl {
    display: none !important;
  }
  .handle-tm {
    display: none !important;
  }
  .handle-tr {
    display: none !important;
  }
  .handle-mr {
    top: 0;
    right: 1px;
    margin-top: 0;
    height: 100% !important;
  }
  .handle-br {
    display: none !important;
  }
  .handle-bm {
    display: none !important;
  }
  .handle-bl {
    display: none !important;
  }
  .handle-ml {
    display: none !important;
  }
}
/deep/.ant-select-dropdown-menu-item {
  padding: 5px 4px;
}
/deep/.ant-select-selection__rendered {
  line-height: 23px;
}
/deep/.selectIndex {
  background-color: #fff9e6;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

// .laminationInfoActive {
//  // width: 80%;
// }
.laminationInfoNo {
  width: 98%;
}
.laminationInfo {
  // position: absolute;
  // top: 0;
  // right: 0;
  height: 100%;
  border: 1px solid #d6d6d6;
  border-left: 0;
  transition: width 0.6s linear;
  .title {
    width: 100%;
    padding: 0 10px;
    // background: linear-gradient(rgb(239, 245, 255) 0px, rgb(224, 236, 255) 100%) repeat-x;
    background-repeat: repeat-x;
    .head-menu {
      height: auto;
      line-height: 30px;
      color: rgb(14, 45, 95);
      // font-weight: 500;
    }
  }
  span {
    display: block;
    height: 22px;
  }
  /deep/ .ant-table-small > .ant-table-content > .ant-table-body {
    margin: 2px;
  }

  /deep/ .ant-table-thead > tr > th {
    padding: 0 !important;
    background: linear-gradient(#f7f7f7, #f0f0f0);
    border-style: solid;
    border-color: #ccc;
    border-width: 0 1px 1px 0;
    height: 30px;
    font-size: 14px;
  }
  /deep/ .ant-table-tbody > tr > td {
    font-size: 12px !important;
    padding: 0 !important;
    height: 22px;
    font-weight: 500;
    line-height: 22px;
    .ant-input {
      height: 22px;
    }
    .ant-select {
      font-size: 12px;
      width: 100%;
      .ant-select-selection--single {
        height: 22px;
      }
      .ant-select-selection__rendered {
        margin: 0;
        width: 100%;
        height: 100%;
        .ant-select-selection-selected-value {
          width: 100%;
          height: 100%;
          text-align: center;
          line-height: 22px;
        }
      }
    }
    .ant-select-selection {
      border: none;
    }
    // .ant-input {
    //   border: none;
    // }
  }
  .tabRightClikBox {
    li {
      height: 25px;
      line-height: 25px;
      margin-top: 0;
      margin-bottom: 0;
      font-size: 12px;
      border-bottom: 1px solid rgb(225, 223, 223);
      background-color: rgb(255, 255, 255) !important;
      color: #000000;
    }
    .ant-menu-item:not(:last-child) {
      margin-bottom: 4px;
    }
  }
}
</style>
