<!-- 车间管理-AOI-上传订单 -->
<template>
  <div ref="SelectBox">
    <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 12 }" :form="enterOrderForm">
      <a-form-item label="大料张数">
        <a-input type="number" v-model="enterOrderForm.materialNum" :autoFocus="autoFocus" v-focus-next-on-enter="'input2'" ref="input1" />
      </a-form-item>
      <a-form-item label="PNL数量">
        <a-input type="number" v-model="enterOrderForm.pnlQty" v-focus-next-on-enter="'input3'" ref="input2" />
      </a-form-item>
      <a-form-item label="总孔数">
        <a-input type="number" v-model="enterOrderForm.allHoleNum" v-focus-next-on-enter="'input4'" ref="input3" />
      </a-form-item>
      <a-form-item label="长边mm">
        <a-input type="number" v-model="enterOrderForm.lSize" v-focus-next-on-enter="'input5'" ref="input4" />
      </a-form-item>
      <a-form-item label="宽边mm">
        <a-input type="number" v-model="enterOrderForm.wSize" v-focus-next-on-enter="'input6'" ref="input5" />
      </a-form-item>
      <a-form-item label="板厚mm">
        <a-input type="number" v-model="enterOrderForm.boardthick" v-focus-next-on-enter="'input7'" ref="input6" />
      </a-form-item>
      <a-form-item label="最小孔径">
        <a-input type="number" v-model="enterOrderForm.minHole" v-focus-next-on-enter="'input8'" ref="input7" />
      </a-form-item>
      <a-form-item label="交货日期">
        <a-date-picker allowClear v-model="enterOrderForm.delDate" class="picker" />
      </a-form-item>
      <a-form-item label="铜厚">
        <a-select allowClear v-model="enterOrderForm.copperThickness" :getPopupContainer="() => this.$refs.SelectBox">
          <a-select-option value="1"> 1 </a-select-option>
          <a-select-option value="2"> 2 </a-select-option>
          <a-select-option value="3"> 3 </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="层数">
        <a-select allowClear v-model="enterOrderForm.boardLayers" :getPopupContainer="() => this.$refs.SelectBox">
          <a-select-option value="1"> 1 </a-select-option>
          <a-select-option value="2"> 2 </a-select-option>
          <a-select-option value="4"> 4 </a-select-option>
          <a-select-option value="6"> 6 </a-select-option>
          <a-select-option value="8"> 8 </a-select-option>
        </a-select>
      </a-form-item>
      <a-row>
        <!-- <a-col :span="3"></a-col> -->
        <a-col :span="12">
          <a-form-item label="特殊板材:" :labelCol="{ span: 14 }" :wrapperCol="{ span: 5, offset: 1 }">
            <a-checkbox @change="SpecialMaterial" v-model="enterOrderForm.isSpecialMaterial"></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="多槽板:" :labelCol="{ span: 10 }" :wrapperCol="{ span: 5, offset: 1 }">
            <a-checkbox @change="SpecialMaterial" v-model="enterOrderForm.MuchSlottedBoard"></a-checkbox>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="文件路径">
        <a-input style="width: 180px" v-model="enterOrderForm.tgzOssPath"> </a-input>
        <a-upload
          accept=".drl,.2nd"
          :show-upload-list="false"
          class="avatar-uploader"
          :customRequest="downloadFilesCustomRequest"
          :multiple="false"
          :before-upload="beforeUpload"
        >
          <a-button> <a-icon type="upload" /></a-button>
        </a-upload>
      </a-form-item>
      <a-form-item label="订单工厂">
        <a-select
          allowClear
          show-search
          option-filter-prop="children"
          :filter-option="filterOption"
          v-model="enterOrderForm.fac"
          :getPopupContainer="() => this.$refs.SelectBox"
        >
          <a-select-option v-for="(item, index) in factoryList" :key="index" :value="item.text">
            {{ item.text }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { getFactoryList, UploadFile } from "@/services/management";
export default {
  name: "EnterOrderInfo",
  props: [],
  data() {
    return {
      factoryList: [],
      autoFocus: true,
      enterOrderForm: {
        materialNum: "", // 大料张数
        pnlQty: "", // NL数量
        allHoleNum: "", // 总孔数
        lSize: "", // 长
        wSize: "", // 宽
        boardthick: "", // 板厚
        minHole: "", // 最小孔径
        delDate: "", // 交期
        copperThickness: "", // 铜厚
        boardLayers: "", // 层数
        isSpecialMaterial: false, // 特殊板材
        MuchSlottedBoard: false, //多槽板
        tgzOssPath: "", // 文件路径
        fac: "", // 订单工厂
        pdctno: "", // 订单编号
      },
    };
  },
  methods: {
    selectClick() {
      getFactoryList().then(res => {
        if (res.code == 1) {
          this.factoryList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadFilesCustomRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      UploadFile(formData).then(res => {
        if (res.code == 1) {
          this.enterOrderForm.tgzOssPath = res.data;
          this.$message.success(res.message);
        } else {
          this.$message.error(res.message);
        }
        var str = this.enterOrderForm.tgzOssPath;
        var arr = str.split("/");
        var newstr_ = arr[arr.length - 1];
        this.enterOrderForm.pdctno = newstr_.split("_")[0];
        // console.log( this.enterOrderForm.pdctno)
        // console.log(this.enterOrderForm.pdctno,this.enterOrderForm.tgzOssPath)
      });
    },
    beforeUpload(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isFileType = file.name.toLowerCase().indexOf(".drl") != -1 || file.name.toLowerCase().indexOf(".2nd") != -1;
        const isFileName = escape(file.name).indexOf("%u") != -1;
        if (!isFileType) {
          _this.$message.error("只支持.drl或.2nd格式文件");
          reject();
        } else if (isFileName) {
          _this.$message.error("上传的文件名不能包含中文");
          reject();
        } else {
          resolve();
        }
      });
    },
    SpecialMaterial() {
      this.isSpecialMaterial = !this.isSpecialMaterial;
    },

    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
  },
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
  mounted() {
    this.selectClick();
  },
};
</script>
<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
  text-align: left;
}

.picker {
  width: 226px;
}
.ant-form-item {
  margin-bottom: 0;
}
.ant-form-item-children {
  width: 180px;
  border-right: 0;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.ant-upload {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.avatar-uploader > .ant-upload {
  position: absolute;
  height: 32px;
}
.ant-upload-select-picture-card i {
  font-size: 12px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  color: #666;
}
</style>
