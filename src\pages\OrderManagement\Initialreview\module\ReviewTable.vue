<!--客诉初审列表-->
<template>
    <div class="main">
        <a-table
            :columns="columns"
            :dataSource="auditdata"
            :pagination="pagination"
            @change="handleTableChange" 
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            :scroll="{ x: 1650,y:640 }"
            :rowKey="(record,index) => index"
            bordered
            :class="auditdata.length?'mintable':''"
        >
        <template slot="operation" slot-scope="text, record, index">           
            <div v-show="activeKey=='30'">               
                <a-button  type="link" size="small" style="padding: 0px;font-size: 12px" @click="Detail(record,index)">初审</a-button>
                <a-divider type="vertical" style="height: 15px; background-color: #ff9900" />
                <a-button  type="link" size="small" style="padding: 0px;font-size: 12px" @click="sendclick(record)">发送</a-button>
            </div> 
        <div v-show="activeKey=='40'">
            <a-button  type="link" size="small" style="padding: 0px;font-size: 12px" @click="Detail(record,index)">详情</a-button>
        </div>
         </template>
         <template slot="filedown" slot-scope="text, record">
          <a-icon @click="filedown(record.verdictFilePath)" type="download" style="color: #428bca;"></a-icon>
        </template>
    </a-table>    
    </div>
</template>
<script>
import {factoryfirstreviewsend} from "@/services/complaint/QualitativeComplaint.js";
export default {
    props:['auditdata','activeKey','pagination','columns'],
    name:'ReviewTable',
    data(){
        return{
            complaintdata:{},
            selectdata:{},
            proOrderId:'',
        }
    },
    created(){
    },
    mounted(){

    },
    methods:{
        onClickRow(record){
                return {
                    on: {
                        click: () => {
                            this.selectdata = record;
                            this.proOrderId=record.id
                        }
                    }
                }
            },
            isRedRow(record){
                let strGroup = []
                let str =[]
                if(record.id && record.id == this.proOrderId) {
                    strGroup.push('rowBackgroundColor')
                }
                return str.concat(strGroup)
            },
        sendclick(record){
            this.$emit('sendclick',record)
        },
        
        filedown(path){
            if(path){
                window.location.href = path
            }else{
                this.$message.error('暂无附件,不允许下载文件')
            }        
        },
        Detail(record){
            this.$router.push({path:'Initialreviewdetails',query:{id:record.id,statusbar:record.status} })
        },
        handleTableChange(pagination,filter,sorter){
            this.$emit('tableChange', pagination,filter,sorter)
    },
    }
}

</script>
<style lang="less" scoped>
.mintable{
    /deep/.ant-table-body{
        min-height: 640px;
    }
}
/deep/.ant-pagination-prev {
    margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input{
  padding: 0;
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager{
  margin: 0;
}
/deep/.ant-pagination-slash {
    margin: 0;
}
/deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 8px 0 0 10px;
    position: fixed;
  }
.main{
    background: white;
    height: 679px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
        background: #F8F8F8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
background: #dfdcdc;
}
/deep/ .ant-table{
        .ant-table-tbody{
            tr.ant-table-row-hover td {
             background: #dfdcdc;
            }
        }
        .rowBackgroundColor {
                background: #c9c9c9!important;
            }       
        .ant-table-thead > tr > th{
            padding: 4px 4px;
        }
        .ant-table-tbody > tr > td {
            padding: 4px 4px!important;
            max-width: 100px;
            color: #000000;
        }
        tr.ant-table-row-selected td {
        background: #dfdcdc;
        }
        tr.ant-table-row-hover td {
        background: #dfdcdc;
        }
}
</style>