<!-- 工程管理 - 工程制作-注意事项 -->
<template>
  <div @click="bodyClick">
    <div v-if="!id">
      <a-form  > 
      <a-form-item >
        <a-upload
          accept=".image/jpeg,image/png,image/gif,image/bmp,image/jpg"
          :file-list="fileList"
          list-type="picture-card"
          @preview="handlePreview" 
          @change="handleChange"
          :before-upload='beforeUpload'
          :customRequest="downloadFilesCustomRequest"
        >      
          <a-button  v-if="fileList.length  < 4" style="font-weight:500;height:40px;">
          上传图片
          </a-button>
          <a-button v-if="fileList.length  < 4" 
          style="font-weight:500;margin-top:6px;height:40px;" @click.stop="showCopy(1)" title="点击 ctrl+V粘贴上传" > 
            粘贴图片
          </a-button>
        </a-upload>
        </a-form-item>
      <a-form-item >
        <a-upload
            accept=".rar,.zip"
            :file-list="fileData"
            @change="handleChange1"
            :before-upload='beforeUpload1'
            :customRequest="downloadFilesCustomRequest1"
        >
          <a-button  v-if="fileData.length < 1"  style="font-weight:500;margin-left: 6px;">
            上传附件
          </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item label="注意事项" :label-col="{ span:4}" :wrapper-col="{ span: 18}">
        <a-textarea   v-model='form.conent' :autoFocus="autoFocus" :rows="6" />
      </a-form-item>
    </a-form>

    </div>
    <div v-if="id">
      <div style="line-height: 34px; color: rgb(255, 153, 0);margin-left: 45%;margin-top: -28px;" v-if="increase" @click="additems">+添加注意事项</div>
      <div class="top">
        <a-table
          v-if="jobData.length"
          :columns="columns4"
          :dataSource="jobData"
          :pagination="false"
          rowKey="id"
          :loading="jobTableLoading"
          :scroll="{y:152}">
          <template slot="picUrl" slot-scope="text,record">
            <viewer :images="picFilter(record)">
              <img :src="item" v-for="(item,index) in picFilter(record)" :key="index" style="width: 18px; height: 18px"/>
            </viewer>
          </template>
          <template slot="fileUrl" slot-scope="text,record">
          <a-tooltip title="下载附件" >
          <a-icon type="download" v-if="record.fileUrl" style="color:rgb(0, 0, 204); " @click="downFile(record.fileUrl)"></a-icon>
          </a-tooltip>
          <span v-if="record.fileUrl" style="color:rgb(0, 0, 204)">/</span>
          <a-tooltip title="删除" >
          <a-icon type="close-square" style="color:rgb(0, 0, 204)" @click="delFile(record)"></a-icon>
          </a-tooltip>
        </template>
        </a-table>
      </div>
      <div style="display: flex;width: 100%;border: 1px solid #efefef;" v-if="payattentionto">
    <div style="width: 80%;">
      <a-form>
      <a-form-item >
        <div style="display: flex;">
          <span style="margin-left: 22px;width: 86px;">上传图片：</span>
        <a-upload
          accept=".image/jpeg,image/png,image/gif,image/bmp,image/jpg"
          :file-list="fileList"
          list-type="picture-card"
          @preview="handlePreview" 
          @change="handleChange"
          :before-upload='beforeUpload'
          :customRequest="downloadFilesCustomRequest"
        >      
          <a-button  v-if="fileList.length  < 4" style="font-weight:500;height:40px;">
          上传图片
          </a-button>
          <a-button v-if="fileList.length  < 4" 
          style="font-weight:500;margin-top:6px;height:40px;" @click.stop="showCopy(1)" title="点击 ctrl+V粘贴上传" > 
            粘贴图片
          </a-button>
        </a-upload>
        </div>
      </a-form-item>
      <a-form-item >
        <span style="margin-left: 22px;">上传附件：</span>
        <a-upload
            accept=".rar,.zip"
            :file-list="fileData"
            @change="handleChange1"
            :before-upload='beforeUpload1'
            :customRequest="downloadFilesCustomRequest1"
        >
          <a-button  v-if="fileData.length < 1"  style="font-weight:500;margin-left: 20px;">
            上传附件
          </a-button> 
        </a-upload>
      </a-form-item>
      <a-form-item >
        <div style="display: flex;">
          <span style="margin-left: 22px;">注意事项：</span>
        <a-textarea  :auto-size="{ minRows:4 , maxRows:4}" v-model='form.conent' :autoFocus="autoFocus" />
        </div>
       </a-form-item>
    </a-form>
    </div>
    <div style="width: 20%; text-align: center; line-height:270px;border-left: 1px solid #efefef;">
      <div style="height: 25px;" @click="savedata">+保存</div>
    </div>
    </div>
    <a-modal
        title=" 确认弹窗"
        :visible="dataVisibleMode"
        @cancel="reportHandleCancel"
        @ok="handleOkMode"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
  
     >
     <span style="font-size:14px;color: #000000;">{{messageMode}}</span>   
     </a-modal> 
    </div>
  </div>   
  </template>
  
  <script>
  import {UploadFile,projectBackEndJobInfo,delInfo} from  "@/services/projectMake";
  import axios from 'axios'
  const columns4 = [
    {
        title: "序号",
        dataIndex: "index",
        key: "index",
        width:'8%',
        align: "center",
        customRender: (text, record, index) => `${index + 1}`,
    },
    {
      title: "操作人",
      dataIndex: "inUserName",
      width: '15%',
      align: "left",
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "indate",
      width: '15%',
      align: "left",
      ellipsis: true,
    },
    {
      title: "备注信息",
      dataIndex: "conent",
      width: '30%',
      align: "left",
      ellipsis: true,
    },
    {
      title: "图片",
      width: '15%',
      scopedSlots: { customRender: 'picUrl' },
      align: "left",
    },
    {
      title: "操作",
      scopedSlots: { customRender: 'fileUrl' },
      className: "noCopy",
      align: "left",
    }
  
  ]
  export default {   
     name:'mattersNeedingAttentionInfo',
     props:{
      id: {
        type: String,
        require: true,
        default: () => ''
      },
      editData:{
        type:Object,
      }
    },
    data() {
      return {
        columns4,
        messageMode:"",
        fileList:[],
        jobData:[],
        increase:false,//控制增加按钮
        form:{
        "conent": "",
        "picUrl": "",
        "fileUrl":'',
        "isbefor": true
        },
        jobTableLoading:false,
        autoFocus:true,
        previewVisible:false,
        dataVisibleMode:false,
        previewImage:'',
        fileData:[],
        payattentionto:false,
        recordId:'',
        ids:'',
      };
    },
    created(){
      //console.log('this.id',this.id)
      if(this.id){
        this.ids = this.id
        this.getJobInfo(this.ids)
      }
    
    },
    mounted(){
      //console.log('editData',this.editData)
      if(this.editData){
        let dataA =  this.form
        let dataB = this.editData
        for(let a=0;a<Object.keys(dataB).length;a++){       
          if(Object.keys(dataA).indexOf(Object.keys(dataB)[a])>=0){            
            dataA[Object.keys(dataB)[a]] =dataB[Object.keys(dataB)[a]] 
          }
          this.form =  dataA  
          if(this.form.picUrl){    
            this.fileList=[]      
            let list=  this.form.picUrl.split(',')
                list.forEach((e,index)=>{
                    let info = {
                        uid:index,
                        name: 'image.png',
                        status: 'done',
                        url:e,
                        thumbUrl:e,//缩略图地址
                    }
                    this.fileList.push(info)
                })         
        } 
        if(this.form.fileUrl){    
          this.fileData=[]      
          let list=  this.form.fileUrl.split(',')
              list.forEach((e,index)=>{
                let info = {
                    uid:index,
                    name: e.split('/')[e.split('/').length-1],
                    status: 'done',
                    url:e,
                    thumbUrl:e,//缩略图地址
                }
                this.fileData.push(info)
              })
        }
        } 
      }
    },
    beforeDestroy(){
      window.removeEventListener('paste', this.getClipboardFiles);
    },
    methods: {
      picFilter(val){
        if (val.picUrl) {
          return val.picUrl.split(',')
        } else {
          return []
        }
      },  
      delFile(record){
        this.messageMode = '确认删除此条注意事项吗？'
        this.dataVisibleMode = true
        this.recordId = record.id
       },  
       reportHandleCancel(){
        this.dataVisibleMode = false
       },
       handleOkMode(){       
          delInfo(this.recordId).then(res=>{
            if(res.code){
              this.$message.success('已删除')
              this.getJobInfo(this.ids)
            }else{
              this.$message.error(res.message)
            }
          })
        this.dataVisibleMode = false; 
      },
        // 下载附件文件
        downFile(record){
        window.location.href = record
      },
          // 获取对应的订单人员
        getJobInfo(ids){
        this.jobTableLoading = true;
        projectBackEndJobInfo(ids).then(res => {
          if (res.code) {
            this.jobData = res.data;
            if(this.jobData.length){
              this.payattentionto = false
              this.increase=true
            }else{
              this.payattentionto = true
              this.increase=false
            }
          }
        }).finally(()=>{
          this.jobTableLoading = false;
        })
      },
      additems(){
        this.payattentionto = true
        this.increase=false
        this.form = {}
        this.fileData = []
        this.fileList = []
      },
      savedata(){
        if(!this.form.conent && !this.form.picUrl && !this.form.fileUrl){
          this.$message.error('请至少传递一项信息')
          return
        }
       this.$emit('handleOk4')
       this.payattentionto = false
       this.increase =true
      },
      handleChange({ fileList },data) {
        if(!fileList){        
          fileList = data.concat(this.fileList)
        } 
        this.fileList = fileList;
        let arrb =  this.fileList.map(item => {return item.response || item.url})   
        console.log('arrb:',arrb)
        let obj = arrb.join(',')
        this.form.picUrl = obj
        if(this.fileList.length  >= 4){          
          this.bodyClick()
        }     
      },
      handleChange1({ fileList },data) {
        this.fileData = fileList; 
        if(this.fileData.length){
           this.form.fileUrl = this.fileData.map(item => {return item.response || item.url}).join(',')
        } else{
           this.form.fileUrl  = ''
        }          
      },
      // 上传图片路径
      downloadFilesCustomRequest(data){
        const formData = new FormData()
        formData.append('file', data.file)
        UploadFile(formData).then(res =>{
          if (res.code == 1) {
            data.onSuccess(res.data);
          }
           else {
            this.$message.error(res.message)
          }   
          if(this.fileList.length  >= 4){          
            this.bodyClick()
          }       
        })
  
      },
      // 限制上传图片格式
      beforeUpload(file){
        const _this = this
        return new Promise(function(resolve, reject) {
          const isJpgOrPng = file.type.toLowerCase() === 'image/jpeg' || file.type.toLowerCase() === 'image/png' || file.type.toLowerCase() === 'image/gif' || file.type.toLowerCase() === 'image/bmp' || file.type.toLowerCase() === 'image/jpg';
          if (!isJpgOrPng) {
            _this.$message.error('图片只支持|*.jpg;*.png;*.gif;*.jpeg;*.bmp 格式');
            reject()
          } else {
            resolve()
          }
        })
      },
      // 限制上传附件格式
      beforeUpload1(file){
        const _this = this
        return new Promise(function(resolve, reject) {
          const isJpgOrPng = file.name.indexOf('.rar') != -1 || file.name.indexOf('.zip') != -1
          if (!isJpgOrPng) {
            _this.$message.error('文件只支持.rar或.zip格式');
            reject()
          } else {
            resolve()
          }
        })
      },
      handlePreview (file) {
        this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
    },
      // 上传附件路径
      downloadFilesCustomRequest1(data){
        const formData = new FormData()
        formData.append('file', data.file)
        UploadFile(formData).then(res =>{
          if (res.code == 1) {
            data.onSuccess(res.data);
            // this.form.fileUrl = this.fileData.map(item => {return item.response}).join(',')
          }
          else {
            this.$message.error(res.message)
          }
          // //console.log( 'this.form.fileUrl:',this.form.fileUrl)
        })
      },
      showCopy(type) {     
      window.addEventListener('paste', this.getClipboardFiles);
      try{    
      navigator.clipboard.read().then( res=>{
          const clipboardItems = res 
           if (clipboardItems[0].types[0].indexOf('image') > -1) {
                clipboardItems[0].getType(clipboardItems[0].types[0]).then( b=>{
                const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {type: clipboardItems[0].types[0]}); 
                this.file = files
                this.getClipboardFiles()                
              });
            }else{
              this.$message.error('粘贴内容不是图片');
              return;
            }
          })
        }catch (e) {
            ////console.log('出错了')        
        }
    },    
    bodyClick(){
      window.removeEventListener('paste', this.getClipboardFiles);
    },
      getClipboardFiles(event) {
      let file = null;
      if(event){
      const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
        // 检索剪切板items,类数组，不能使用forEach
        for (let i = 0; i < items.length; i += 1) {         
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf('image') !== -1) {
              file = items[i].getAsFile();
            }
        }
      } 
      }else{
        file = this.file
      }
      if (!file) {
        this.$message.error('粘贴内容不是图片');
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error('请上传小于10M,并且格式正确的图片');
        return;
      }
      this.beforeUpload(file);   // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload(file)) {  // **** return true 之后进行上传
        this.startloading = true;
        const formData = new FormData();
       // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append('file', file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + '/api/app/engineering-production/up-load-back-file',   // 接口地址
          method: 'post',
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => { 
            if (res.code) {
              file.status = 'done';
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });             
                this.handleChange(file, arr);               
            } else {
              this.$message.error(data.message || '网络异常,请重试或检查网络连接状态');
              this.startloading = false;
              this.show = false;
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
    },
  };
  </script>
  <style scoped lang='less'>
  .top{
    border: 1px solid #efefef;
    /deep/.ant-table-placeholder{
      height: 117px;
    }
    /deep/.ant-empty-normal{
      margin: 0;
    }
   /deep/ .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
      padding: 4px 7px;
      overflow-wrap: break-word;
      border-right: 1px solid #efefef;
  }
  /deep/.ant-table-tbody > tr > td {
      padding: 4px 7px;
      overflow-wrap: break-word;
      border-right: 1px solid #efefef;
  }
  }
  /deep/.ant-upload-list-item-card-actions {
    position: absolute;
    right: -20px;
    opacity: 0;
}
  /deep/.ant-upload.ant-upload-select-picture-card{
    margin-left: 5px;
  }
  /deep/.ant-upload-list-text{
      position: relative;
      left: 14px;
      top: 3px;
      display: inline-block;
  }
  
  /deep/.ant-upload-list-picture-card .ant-upload-list-item {
      float: left;
      width: 104px;
      height: 104px;
  }
  .ant-row {
    margin-bottom: 0;
    margin-top: 10px;
  }
  /deep/.ant-input{
    font-weight: 500;
    width: 400px;
    margin-left: 20px;
  } 
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
        background: #dfdcdc;
      }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
  }
  </style>
  