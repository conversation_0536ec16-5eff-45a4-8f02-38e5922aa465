<!-- 市场管理 - 订单报价- 订单列表 -->

<template>
  <a-spin :spinning="spinning">
    <div ref="tableWrapper">
      <a-table
        :columns="columns"
        :scroll="{ y: 737, x: 600 }"
        :dataSource="dataSource"
        :pagination="pagination"
        :rowKey="rowKey"
        :customRow="customRow"
        :loading="orderListTableLoading"
        @change="handleTableChange"
        :rowClassName="isRedRow"
        class="resizable-table"
      >
        <template slot="contractFilePath" slot-scope="record">
          <span
            v-if="record.contractFilePath"
            @click.stop="ContractDownload(record)"
            style="
              font-size: 14px;
              background: none;
              color: #428bca;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: none;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 128px;
            "
          >
            {{ record.contractFile }}
          </span>
        </template>
        <template slot="labelUrl" slot-scope="record">
          <span @click.stop="OperationLog(record)" style="color: #428bca"> 日志</span>
        </template>
        <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">{{ record.orderNo }}&nbsp;</div>
      </a-table>
      <right-copy ref="RightCopy" />
    </div>
  </a-spin>
</template>

<script>
import RightCopy from "@/pages/RightCopy"; // 右键复制
import { checkPermission } from "@/utils/abp";
export default {
  props: ["dataSource", "orderListTableLoading", "columns", "pagination", "rowKey"],
  name: "LeftTable",
  components: { RightCopy },
  data() {
    return {
      spinning: false,
      proOrderId: "",
      shiftKey: false,
      selectedRowsData: [],
      selectedRowList: [],
      isDragging: false,
      startIndex: -1,
    };
  },
  watch: {},
  mounted() {},
  created() {},
  methods: {
    checkPermission,
    OperationLog(record) {
      this.$emit("OperationLog", record);
    },
    ContractDownload(record) {
      if (record.contractFilePath) {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", record.contractFilePath, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            if (record.contractFilePath.indexOf("pdf") != -1) {
              link.download = record.contractFile + ".pdf";
            } else {
              link.download = record.contractFile + ".zip";
            }
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };

        xhr.send();
      }
    },
    handleTableChange(pagination, filter, sorter) {
      this.$emit("tableChange", pagination, filter, sorter);
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowList.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      var data = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
        data.push(selectedRowsData[a]);
      }
      this.selectedRowList = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowList[this.selectedRowList.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowList[0];
        })[0];
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowList;
        let record1 = this.alldata;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
          record1.splice(record1.indexOf(record), 1);
        } else {
          rowKeys.push(record.id);
          record1.push(record);
        }
        this.selectedRowList = rowKeys;
        this.alldata = record1;
        if (this.selectedRowList.length == 1) {
          this.selectedRowsData = record;
          this.alldata = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowList = [record.id];
          this.alldata = [record];
        }
      }
      this.proOrderId = this.selectedRowsData.id;
      this.shiftKey = false;
      this.$emit("gettopdata", this.proOrderId);
      this.$emit("Changecolumns", this.selectedRowsData.joinFactoryId);
    },
  },
};
</script>

<style lang="less" scoped></style>
