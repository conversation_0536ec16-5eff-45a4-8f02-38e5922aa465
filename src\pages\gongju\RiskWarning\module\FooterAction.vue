<!-- 工具 - 风险警告- 按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="advanced && checkPermission('MES.ToolModule.RiskWarning.RiskWarningAdd')"
      :class="checkPermission('MES.ToolModule.RiskWarning.RiskWarningAdd') ? 'showClass' : ''"
    >
      <div>
        <a-button type="primary" @click="Modelrisk"> 新增风险 </a-button>
      </div>
    </div>
    <div
      class="box"
      v-show="advanced && checkPermission('MES.ToolModule.RiskWarning.RiskWarningUpdateStatus')"
      :class="checkPermission('MES.ToolModule.RiskWarning.RiskWarningUpdateStatus') ? 'showClass' : ''"
    >
      <div>
        <a-button type="primary" @click="lapse"> 失效 </a-button>
      </div>
    </div>
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
            <a-menu-item @click="lapse" v-show="advanced && checkPermission('MES.ToolModule.RiskWarning.RiskWarningUpdateStatus')">失效</a-menu-item>
            <a-menu-item @click="Modelrisk" v-show="advanced && checkPermission('MES.ToolModule.RiskWarning.RiskWarningAdd')">新增风险</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";

export default {
  name: "QaeAction",
  props: {},
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
      nums: "",
      buttonsmenu: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.active.children.length > 7) {
        this.collapsed = true;
      } else {
        this.collapsed = false;
      }
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length + 1;
      this.nums = elements.length + 1;
      if (num <= 7) {
        this.showBtn = false;
        this.advanced = true;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      for (var a = 0; a < 6; a++) {
        elements[a].style.cssText = "";
      }
      this.handleResize();
      window.addEventListener("resize", this.handleResize, true);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      let num = "";
      if (!this.advanced) {
        num = 8 * 104;
      } else {
        num = (elements.length + 1) * 104;
      }
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          //elements1[0].style.display = "inline-block";
          for (let i = 0; i < elements.length; i++) {
            if (i < 6) {
              elements[i].style.display = "inline-block";
            }
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            //elements1[0].style.display = "none";
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.advanced = false;
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              // elements1[0].style.display = "none";
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              //elements1[0].style.display = "inline-block";
              for (let i = 0; i < elements.length; i++) {
                if (i < 6) {
                  elements[i].style.display = "inline-block";
                }
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        //elements1[0].style.display = "inline-block";
        for (let i = 0; i < elements.length; i++) {
          if (i < 6) {
            elements[i].style.display = "inline-block";
          }
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      if (this.advanced) {
        width_ = 1500;
      } else {
        width_ = 762;
        this.$nextTick(() => {
          const elements = document.getElementsByClassName("showClass");
          for (var a = 0; a < 6; a++) {
            elements[a].style.cssText = "";
          }
        });
      }
      this.$refs.active.style.width = width_ + "px";
      this.handleResize();
    },
    //失效
    lapse() {
      this.$emit("lapse");
    },
    //型号风险
    Modelrisk() {
      this.$emit("Modelrisk");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-btn {
  padding: 0 10px !important;
}
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  //width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 92px;
    margin-top: 1px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
