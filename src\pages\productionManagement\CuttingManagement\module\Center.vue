<!-- 车间管理-开料管理 -右边列表数据 -->
<template>
  <a-card :bordered="false">
    <div class="machine" style="border: 1px solid #E9E9F0;">
      <div class="left" style="border: 1px solid #E9E9F0;">
        <div class="top" style="border-bottom: 3px solid #E9E9F0;">
          <a-table
              :rowKey="(record,index)=>{return index}"
              :columns="columns2"
              :dataSource="machineStatuList"
              :pagination="false"
              :loading="machineStatuLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
          >
          </a-table>
        </div>
        <div class="bot">
          <!-- 钻刀列表 -->
          <a-table              
              :rowKey="(record,index)=>{return index}"
              :columns="columns3"
              :dataSource="drillList"
              :pagination="false"
              :loading="drillLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :scroll="{y:594 }"
          >
          </a-table>       
        </div>
      </div>
      <div class="right" style="border: 1px solid #E9E9F0;"> 
          <a-collapse v-model="activeKey" :bordered="false" >
            <big-img v-if="showImg" @clickit="viewImg" :imgSrc="imgSrc"></big-img>  
            <a-collapse-panel key="1" header="开料图"  >
              <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
                <a-empty v-if="!sheetOssPath"/>
                <div  v-else style="height:300px;width:245px;" >
                  <img :src='sheetOssPath' style="height:100%;width:100%;" @click="clickImg"/>
                </div>
              </a-spin>
            </a-collapse-panel>
            <a-collapse-panel key="2" header="PNL图">
              <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
                <a-empty v-if="!aPnlOssPath"/>
                <div v-else style="height:300px;width:245px;">
                  <img :src='aPnlOssPath' style="height: 100%;width: 100%" @click="clickImg"/>
                </div>
              </a-spin>
            </a-collapse-panel>
    
            <template #expandIcon="props">
              <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0"/>
            </template>
          </a-collapse>
        </div> 
    </div>
  </a-card>
</template>

<script>
import BigImg from './BigImg.vue'
const columns2 = [
  {
    title: "状态",
    dataIndex: "state_",
    width: 50,
    align: 'center',
  },
  {
    title: "面积(㎡)",
    dataIndex: "area_",
    width: 65,
    align: 'center',
    customRender: (text, record) => {
      return text.toFixed(2)
    }
  },
  {
    title: "PNL数",
    dataIndex: "pnL_",
    width: 65,
    align: 'center',
  },
  {
    title: "款数",
    dataIndex: "count_",
    width: 50,
    align: 'center',
  },
]
const columns3 = [
  {
    title: "钻刀号",
    dataIndex: "toolno",
    width: 50,
    align: 'center',
  },
  {
    title: "孔径",
    dataIndex: "tooldia",
    width: 50,
    align: 'center',
  },
  {
    title: "数量",
    dataIndex: "qty",
    width: 50,
    align: 'center',
  },
  {
    title: "描述",
    dataIndex: "captions",
    width: 50,
    align: 'center',
  },
]

export default {
  name:'Center',
  components:{BigImg},
  props:{
    tableData:{
      type: Array
    },    
    machineStatuList:{
      type: Array
    },
    machineStatuLoad:{
      type: Boolean
    },
    drillList:{
      type: Array
    },
    drillLoad:{
      type: Boolean
    },
    dispatchList:{
      type: Array
    },
    dispatchLoad:{
      type: Boolean
    },
    imageLoading:{
      type: Boolean
    },
    aPnlOssPath:{
      type:String
    },
    bPnlOssPath:{
      type:String
    },
    sheetOssPath:{
      type:String
    },
  },
  data () {
    return {
      columns2,
      columns3,
      activeKey: ['1','2'],
      showDrill:false,
      selectedRowKeys: [],
      rowId:'',
      rowId1:'',
      showImg:false,
      imgSrc: ''
    }
  },
  methods:{ 
    clickImg(e) {
    this.showImg = true;
    // 获取当前图片地址
    this.imgSrc = e.currentTarget.src;
    },
    viewImg(){
    this.showImg = false;
    },
  },
  watch: {
    activeKey(key) {
      console.log(key);
    },
  },
}
</script>

<style lang="less" scoped>
.machine {
  height: 780px;
  display: flex;
  .left {
    width: 50%;
    .top{
      height:181px;
    }
  }
/deep/  .right {
    width: 50%;    
       .ant-collapse {
         background: #ffff;         
        .ant-collapse-item{
          height:389px;
        }
        .img-view{
          .img{
            position:fixed;
            top:11%;
            left:30%;
            width: 40%;
            z-index: 999;
          }
        }
      }
  }
  /deep/ .ant-table-tbody {
    // .ant-table-row{
    //   td:first-child{
    //     user-select: all;
    //   }
    // }
    .clickRowStyl {
      background: #fff9e6;
    }
    
  }
  
}


</style>