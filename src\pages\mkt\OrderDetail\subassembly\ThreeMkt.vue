<!-- 市场管理-订单详情分片3 -->
<template>
  <div class="contentInfo" ref="SelectBox">
    <a-card :bordered="false">
      <a-form-model layout="inline" id="formDataElem" v-show="editFlag">
        <a-row>
          <a-col :span="3" class="line3">
            <a-form-model-item
              label="外层最小线宽mil"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.lineWidth && iseval(requiredLinkConfigList.lineWidth.isNullRules) && required ? 'require' : ''"
              v-show="!show0"
            >
              <div class="editWrapper">
                <a-input v-model="formData.lineWidth" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="外层最小线距mil"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.lineSpacing && iseval(requiredLinkConfigList.lineSpacing.isNullRules) && required ? 'require' : ''"
              v-show="!show0"
            >
              <div class="editWrapper">
                <a-input v-model="formData.lineSpacing" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="最小IC尺寸mil"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.icSize && iseval(requiredLinkConfigList.icSize.isNullRules) && required ? 'require' : ''"
              v-show="!show0"
            >
              <div class="editWrapper">
                <a-input v-model="formData.icSize" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="最小BGA(mil)"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.bgaSize && iseval(requiredLinkConfigList.bgaSize.isNullRules) && required ? 'require' : ''"
              v-show="!show0"
            >
              <div class="editWrapper">
                <a-input v-model="formData.bgaSize" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="通孔最小孔mm"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.vias && iseval(requiredLinkConfigList.vias.isNullRules) && required ? 'require' : ''"
              v-show="!show0"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.vias"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="$emit('ApertureRatio')"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.Vias)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="单元通孔孔数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.totalHoleNum && iseval(requiredLinkConfigList.totalHoleNum.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="formData.totalHoleNum" @change="numChange" allowClear />
              </div>
            </a-form-model-item>

            <a-form-model-item
              label="单元槽(散孔)数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.slotHoleNum && iseval(requiredLinkConfigList.slotHoleNum.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="formData.slotHoleNum" allowClear @change="numChange" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="最小测试点mil"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.testPointSize && iseval(requiredLinkConfigList.testPointSize.isNullRules) && required ? 'require' : ''"
              v-show="!show0"
            >
              <div class="editWrapper">
                <a-input v-model="formData.testPointSize" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="单元测试点数量"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.testPointNum && iseval(requiredLinkConfigList.testPointNum.isNullRules) && required ? 'require' : ''"
              v-show="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
            >
              <div class="editWrapper">
                <a-input v-model="formData.testPointNum" allowClear @change="numChange" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="内层孔到线mil"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.holetoline && iseval(requiredLinkConfigList.holetoline.isNullRules) && required ? 'require' : ''"
              v-show="((showCG && showMore) || (showMM && showMore) || (showHDI && showMore)) && ((showData.holetoline && !editFlag) || editFlag)"
            >
              <div class="editWrapper">
                <a-input v-model="formData.holetoline" :disabled="boardLayers1" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="V-CUT刀数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.vCutKnifeNum && iseval(requiredLinkConfigList.vCutKnifeNum.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input
                  v-model="formData.vCutKnifeNum"
                  :min="0"
                  allowClear
                  :disabled="
                    formData.formingType == 'vcut' ||
                    formData.formingType == 'vcut+rtr' ||
                    formData.formingType == 'vcut+rtp' ||
                    formData.formingType == 'mechanical_milling+vcut'
                      ? false
                      : true
                  "
                />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="跳V刀数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.jumpCutXt && iseval(requiredLinkConfigList.jumpCutXt.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input
                  v-model="formData.jumpCutXt"
                  allowClear
                  :disabled="
                    formData.formingType == 'vcut' ||
                    formData.formingType == 'vcut+rtr' ||
                    formData.formingType == 'vcut+rtp' ||
                    formData.formingType == 'mechanical_milling+vcut'
                      ? false
                      : true
                  "
                />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="阻抗组数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.impGroupNum && iseval(requiredLinkConfigList.impGroupNum.isNullRules) && required ? 'require' : ''"
              v-show="formData.boardLayers >= 2"
            >
              <div class="editWrapper">
                <a-input v-model="formData.impGroupNum" ref="impGroupNum" allowClear> </a-input>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="阻抗非常规公差"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.isImpUnTol && iseval(requiredLinkConfigList.isImpUnTol.isNullRules) && required ? 'require' : ''"
              v-show="!show0"
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.isImpUnTol" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="line3">
            <a-form-model-item
              label="内层最小线宽mil"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.inLineWidth && iseval(requiredLinkConfigList.inLineWidth.isNullRules) && required ? 'require' : ''"
              v-show="showMore"
            >
              <div class="editWrapper">
                <a-input v-model="formData.inLineWidth" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="内层最小线距mil"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.inLineSpacing && iseval(requiredLinkConfigList.inLineSpacing.isNullRules) && required ? 'require' : ''"
              v-show="showMore"
            >
              <div class="editWrapper">
                <a-input v-model="formData.inLineSpacing" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="层压不可更改"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="
                requiredLinkConfigList.isChangeLayerPres && iseval(requiredLinkConfigList.isChangeLayerPres.isNullRules) && required ? 'require' : ''
              "
              v-show="showMore"
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.isChangeLayerPres" :disabled="boardLayers1" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="光板数1"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.gbNum && iseval(requiredLinkConfigList.gbNum.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select v-model="formData.gbNum" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.GbNum)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="PP张数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.ppNum && iseval(requiredLinkConfigList.ppNum.isNullRules) && required ? 'require' : ''"
              v-show="formData.boardLayers >= 2"
            >
              <div class="editWrapper">
                <a-select v-model="formData.ppNum" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.PpNum)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>

            <a-form-model-item
              label="特殊PP张数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.specialPpNum && iseval(requiredLinkConfigList.specialPpNum.isNullRules) && required ? 'require' : ''"
              v-show="formData.boardLayers >= 2"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.specialPpNum"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.PpNum)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>

            <a-form-model-item
              label="压合/钻孔次数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.pressTimes && iseval(requiredLinkConfigList.pressTimes.isNullRules) && required ? 'require' : ''"
              v-show="showHDI || showMM"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.pressTimes"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="boardLayers1 || plateTypeStr1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.PressTimes)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable + item.value"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  style="margin-left: 5px"
                  v-model="formData.drillTimes"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="boardLayers1 || plateTypeStr1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.PressTimes)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable + item.value"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="盲埋阶数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.blindBuryOrder && iseval(requiredLinkConfigList.blindBuryOrder.isNullRules) && required ? 'require' : ''"
              v-show="showHDI || showMM"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.blindBuryOrder"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="boardLayers1 || plateTypeStr1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.BlindBuryOrder)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable + item.value"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="机械盲孔数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.blindHoleNum && iseval(requiredLinkConfigList.blindHoleNum.isNullRules) && required ? 'require' : ''"
              v-show="showHDI || showMM"
            >
              <div class="editWrapper">
                <a-input v-model="formData.blindHoleNum" @change="numChange" :disabled="boardLayers1 || plateTypeStr1" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="最小盲埋孔mm"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.blindHoleMin && iseval(requiredLinkConfigList.blindHoleMin.isNullRules) && required ? 'require' : ''"
              v-show="showHDI || showMM"
            >
              <div class="editWrapper">
                <a-input v-model="formData.blindHoleMin" :disabled="boardLayers1 || plateTypeStr1" allowClear />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="盲孔厚介质"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="
                requiredLinkConfigList.blindHoleThickMedium && iseval(requiredLinkConfigList.blindHoleThickMedium.isNullRules) && required
                  ? 'require'
                  : ''
              "
              v-show="showHDI || showMM"
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.blindHoleThickMedium" />
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="激光阶数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.laserOrder && iseval(requiredLinkConfigList.laserOrder.isNullRules) && required ? 'require' : ''"
              v-show="showHDI"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.laserOrder"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="boardLayers1 || plateTypeStr1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="cutchange"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.LaserOrder)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable + item.value"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="激光次数/类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.laserNum && iseval(requiredLinkConfigList.laserNum.isNullRules) && required ? 'require' : ''"
              v-show="showHDI"
            >
              <div class="editWrapper widthclass">
                <a-select
                  v-model="formData.laserNum"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="boardLayers1 || plateTypeStr1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  class="select1"
                  @change="laserNumchange"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.LaserNum)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable + item.value"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="formData.laserType"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="boardLayers1 || plateTypeStr1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  class="select2"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.LaserType)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="激光最小孔mm"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.laserMinHole && iseval(requiredLinkConfigList.laserMinHole.isNullRules) && required ? 'require' : ''"
              v-show="showHDI"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.laserMinHole"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="boardLayers1 || plateTypeStr1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.LaserMinHole)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="激光孔数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.laserHoleNum && iseval(requiredLinkConfigList.laserHoleNum.isNullRules) && required ? 'require' : ''"
              v-show="showHDI"
            >
              <div class="editWrapper">
                <a-input v-model="formData.laserHoleNum" :disabled="boardLayers1 || plateTypeStr1" allowClear></a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <four-mkt
            @cutchange="cutchange"
            :spinning="spinning"
            :editFlag="editFlag"
            :showData="showData"
            :selectOption="selectOption"
            :boardBrandList="boardBrandList"
            :sheetTraderList="sheetTraderList"
            :boardtgList="boardtgList"
            :supList="supList"
            :joinFacId="joinFacId"
            :ManufacturerTG="ManufacturerTG"
            :show0="show0"
            :show1="show1"
            :show2="show2"
            :showMore="showMore"
            :showCG="showCG"
            :showHDI="showHDI"
            :showMM="showMM"
            :reOrder="reOrder"
            :required="required"
            :frontDataZSupplierf="frontDataZSupplierf"
            :requiredLinkConfigList="requiredLinkConfigList"
            :formData="formData"
          ></four-mkt>
        </a-row>
        <six-mkt
          :spinning="spinning"
          :editFlag="editFlag"
          :joinFacId="joinFacId"
          :allnote="{ note: formData.note, noteSure: formData.noteSure }"
          ref="sixMkt"
          :formData="formData"
        ></six-mkt>
      </a-form-model>
    </a-card>
  </div>
</template>
<script>
import FourMkt from "@/pages/mkt/OrderDetail/subassembly/FourMkt";
import SixMkt from "@/pages/mkt/OrderDetail/subassembly/SixMkt";
export default {
  name: "",
  props: [
    "editFlag",
    "spinning",
    "showData",
    "selectOption",
    "boardBrandList",
    "sheetTraderList",
    "boardtgList",
    "supList",
    "frontDataZSupplierf",
    "reOrder",
    "requiredLinkConfigList",
    "joinFacId",
    "required",
    "ManufacturerTG",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "formData",
  ],
  components: { SixMkt, FourMkt },
  data() {
    return {
      dataVisible: false,
      dataVisible1: false,
      ReportList: [], // 出货报告列表
      boardLayers1: false,
      blindBuryStr1: false,
      plateTypeStr1: false,
      obj: {},
      sheetTrader: [],
      boardBrand: [],
      src: "",
      IPCLevel: [],
      tslge: false,
      frontDataZSupplier: [],
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      spinning1: false,
    };
  },
  created() {
    this.$nextTick(() => {});
  },
  mounted() {},
  watch: {
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    showData: {
      handler(val) {
        if (
          val.boardBrand == "RO4350B" ||
          val.boardBrand == "RO4003C" ||
          val.boardBrand == "SH260" ||
          val.boardBrand == "CT350" ||
          val.boardBrand == "CT338"
        ) {
          this.tslge = true;
        } else {
          this.tslge = false;
        }
      },
    },
    supList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.frontDataZSupplier = newval.slice(0, 20);
        });
      },
    },
  },

  methods: {
    cutchange() {
      this.$emit("cutchange");
    },
    laserNumchange(value) {
      this.formData.laserOrder = value;
      this.formData.blindBuryOrder = value;
      if (this.formData.laserNum == 1) {
        this.formData.laserType = "普通";
      } else if (this.formData.laserNum >= 2) {
        this.formData.laserType = "叠加";
      }
    },
    mapKey(data) {
      if (!data || !data.length) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    numChange() {
      //测试点数(W/㎡)计算
      //测试点数 = 单板测试点*su数/交货尺寸x米/交货尺寸y米/10000 保留两位小数
      if (this.formData.testPointNum && this.formData.su && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        const testPointNum = this.formData.testPointNum; // 单板测试点
        const su = this.formData.su; // SU数
        const boardHeightM = this.formData.setBoardHeight / 1000; // 交货尺寸Y（转换为米）
        const boardWidthM = this.formData.setBoardWidth / 1000; // 交货尺寸X（转换为米）
        // 计算测试点数
        const testPoints = (testPointNum * su) / boardHeightM / boardWidthM;
        this.formData.testPointsm2 = Math.floor(testPoints);
      } else {
        this.formData.testPointsm2 = null;
      }
      if (this.formData.pinBanType1 && this.formData.pinBanType2) {
        this.formData.su = (Number(this.formData.pinBanType1) * this.formData.pinBanType2).toFixed();
      } else {
        this.formData.su = null;
      }
      if (this.formData.boardType == "set" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.boardArea = ((this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000).toFixed(2);
      }
      if (this.formData.boardType == "pcs" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth && this.formData.su) {
        this.formData.boardArea = (
          (this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) /
          1000000 /
          this.formData.su
        ).toFixed(2);
      }
      if (this.formData.su >= 2) {
        this.formData.boardType = "SET";
      } else if (this.formData.setBoardHeight > this.formData.pinBanType1 || this.formData.setBoardWidth > this.formData.pinBanType2) {
        this.formData.boardType = "SET";
      } else {
        this.formData.boardType = "PCS";
      }
      if (this.formData.totalHoleNum && this.formData.su && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.poreDensity = (
          ((Number(this.formData.totalHoleNum) + Number(this.formData.slotHoleNum) + (Number(this.formData.blindHoleNum) || 0)) * this.formData.su) /
          ((this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000) /
          10000
        ).toFixed(2);
      } else {
        this.formData.poreDensity = null;
      }
    },
    setEstimate2(value) {
      this.formData.surfaceFinishJsonDto.cjNickelThinckness = value.toString();
    },
    handleSearch2(value) {
      this.setEstimate2(value);
    },
    handleBlur2(value) {
      this.setEstimate2(value);
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("formData") != -1) {
        newIsNullRules = newIsNullRules.replace(/formData/g, "this.formData");
      }
      if (val.indexOf("reOrder") != -1) {
        newIsNullRules = newIsNullRules.replace(/reOrder/g, "this.reOrder");
      }
      if (val.indexOf("tslge") != -1) {
        newIsNullRules = newIsNullRules.replace(/tslge/g, "this.tslge");
      }
      if (val.indexOf("boardBrand") != -1) {
        newIsNullRules = newIsNullRules.replace(/boardBrand/g, "this.boardBrand");
      }

      return eval(newIsNullRules);
    },
  },
};
</script>
<style scoped lang="less">
.guokong {
  /deep/.ant-select {
    width: 36% !important;
  }
  /deep/.ant-input {
    padding: 0 !important;
  }
}
.widthclass {
  .select1 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 37px !important;
    }
  }
  .select2 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 56px !important;
    }
  }
}
.bborder {
  .div2 {
    /deep/.ant-form-item-control {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

/deep/b {
  font-weight: 500;
}
.div22 {
  /deep/.ant-form-item-control {
    padding: 0;
    max-height: 65px;
    min-height: 24.5px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
/deep/.require11 {
  color: red !important;
}
// .cu{
//   /deep/.ant-form-item-control{
//     height:26.67px;
//   }
// }
#formDataElem1 {
  .div1 {
    .ant-form-item {
      width: 30%;
    }
    /deep/ .ant-form-item-control-wrapper {
      // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
      font-family: PingFangSC-Regular, Sans-serif;
      font-weight: 500;
      color: #000000;
      font-size: 13px;
      .ant-form-item-control {
        .ant-form-item-children {
          display: block;
          min-height: 13.672px;
        }
        line-height: inherit;
        padding: 2px 4px !important;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
      }
    }
    // /deep/.pcbFileName{
    //   .ant-form-item-label{
    //     width:30%;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:70%;
    //   }
    // }
  }
}
/deep/.ant-input-affix-wrapper {
  width: 100%;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 6px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 2px;
}
/deep/.line3 {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
  // .ant-form-item-control-wrapper {
  //   .ant-form-item-control{
  //     border:0!important;
  //   }
  // }
}
.bbb {
  /deep/.ant-form-item-label {
    width: 16.65%;
    // -ms-width: 101px important; // IE
    // -webkit-width:100.5%; //谷歌
    border-left: 1px solid #ddd;
  }
  /deep/.ant-form-item-control-wrapper {
    // -ms-width: 911px!important; // IE
    // -webkit-width:942.0.5%; //谷歌
    width: 83.3%;
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
// .bbb{
//   /deep/.ant-form-item-label{
//     width:105px;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   /deep/.ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     // width:1038px;
//     width:100%;
//   }
//   /deep/textarea.ant-input {
//     min-height:24px;
//   }
//   /deep/.ant-form-item-control{
//     padding: 2px !important;
//     // width: 942px;
//     width:100%;
//   }
//   /deep/.ant-input{
//     height:24px;
//   }
// }
// /deep/.editWrapper1 {
//   .ant-form-item-label{
//     width:10%;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   .ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     width:1092px;
//   }
// }

/deep/.ant-select-selection--single {
  height: 22px !important;
}
/deep/.ant-select-item-option-content {
  color: red !important;
}
/deep/.ant-select-selection__rendered {
  line-height: 20px !important;
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.require1 {
  .ant-form-item-label > label {
    color: red !important;
    background-color: greenyellow;
  }
}
// /deep/.bac{
//     .ant-form-item-label > label {
//     color: red!important;
//     background-color: #ff9900;
// }

// }
span {
  font-size: 13px;
}

/deep/.ant-select {
  font-size: 13px !important;
}
/deep/.ant-input {
  font-size: 13px !important;
  font-weight: 500;
}
.contentInfo {
  font-size: 13px;
  width: 1676px;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    font: 12px / 1.14 arial;
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 10.5%;
        text-indent: 0.5%;
        font-size: 13px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 10.5%;
      }
    }
    .special {
      height: 268px;
      width: 456px;
      display: inline-block;
      position: absolute;
      right: 172px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 270px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 261px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 270px;
              width: 75px;
            }
          }
        }
      }
    }
    .special1 {
      height: 268px;
      width: 308px;
      display: inline-block;
      position: absolute;
      right: 320px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 267px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 250px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 267px;
            }
          }
        }
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        .ant-row {
          .line2 {
            .ant-form-item-label {
              border-bottom: 0px;
            }
          }
          .line {
            .ant-form-item-control {
              border-bottom: 0px;
            }
          }
        }
      }
      .spec {
        width: 19%;
        position: absolute;
        top: 0;
        right: 0;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .tmp {
          word-break: keep-all;
          vertical-align: top;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: inline-block;
        }
        .editWrapper1 {
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            width: 99%;
          }
        }
        .editWrapper {
          width: 100%;
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            // width:96px;
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            // width: 96px;
            width: 99%;
          }
        }
        .ant-form-item-label {
          // width: 117px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font: 12px/1.14 "微软雅黑", arial;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          // border-left: 1px solid #ddd;
          label {
            // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
            font-family: PingFangSC-Regular, Sans-serif;
            font-size: 13px;
            font-weight: 500;
            color: #000000;
          }
        }
        .ant-form-item-control-wrapper {
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font-family: PingFangSC-Regular, Sans-serif;
          font-weight: 500;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
              white-space: pre-line;
            }
            line-height: inherit;
            padding: 6px 4px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
