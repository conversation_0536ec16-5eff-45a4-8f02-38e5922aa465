<!-- 工程管理 - 工程后端 -查询-->
<template>
  <div ref="SelectBox">
    <a-form >
    <a-row>
      <a-col :span='24'>
      <a-form-item label="生产编号" :labelCol="{span: 7}" :wrapperCol="{span:14}" >
        <a-input   v-model='form.OrderNo' placeholder="请输入生产编号" :autoFocus="autoFocus" v-focus-next-on-enter="'select1'" ref="input1"/>
      </a-form-item>
      </a-col>
    </a-row>
     <a-row>

     <a-col :span='24'>
        <a-form-item label="订单状态"  :labelCol="{span: 7}" :wrapperCol="{span:14}">
        <a-select
          ref="select1"
          v-model="form.States"
          showSearch allowClear optionFilterProp="lable"
          :getPopupContainer="()=>this.$refs.SelectBox"
        >
        <a-select-option v-for="(item,index) in list" :key="index" :value="item.value" :lable="item.text">{{ item.text }}</a-select-option>
          
        </a-select>
         </a-form-item>
      </a-col>
     </a-row>


    
<!-- <a-row>
    <a-col :md="9" :sm="24">
         <a-form-model-item label="订单渠道" prop="tradeTypeSrc" :label-col="{ span: 8}" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
              <a-select v-model="form.tradeTypeSrc"  placeholder="请选择" showSearch optionFilterProp="label">
                   <a-select-option  v-for=" item in tradeTypeSrcList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
          </a-form-model-item>
      </a-col>
 </a-row> -->
  </a-form>
  </div>
  
</template>

<script>
import {
  getTradeTypeSrcList
} from "@/services/projectDisptch";
export default {
    name:'QueryInfoBackend',
  data() {
    return {
      form:{
      OrderNo:'',
      States:'',
      // Dispatch:'',
      // strLjb_: false,  // 是否铝基板
      // strNoLjb_: false,    // 其他单面板
      // strNoLjb2_: false,     // 大于1㎡非铝基板单面批量
      // Layer_: false,     // 四层板样板
      // Yp3_: false, // ≥1㎡双面板批量
      // Yp2_: false, // 非24小时双面板样品
      // Layer3_: false,     // 多层板批量
      // Yp_: false,    // 24小时双面板样品
      // Layer2_: false,    // 六层含以上样板
      // tradeTypeSrc:'',
      },
      autoFocus:true,
      tradeTypeSrcList:[],
      list:[
        {value:'',text:'请选择'},
        {value:'21',text:'后端待派'},
        {value:'22',text:'后端制作'},
        {value:'24',text:'已问客'},
        {value:'15',text:'EQ待审'},
        {value:'30',text:'已回传'},
    ]
    };
  },
  created() {
    getTradeTypeSrcList().then(res => {
      this.tradeTypeSrcList = res?.data
    });
  },
  methods: {
  //  keyupEnter1(){
  //     this.$emit('keyupEnter1')
  // }
   onChange(e){
      this.form[e.target.name] = e.target.checked
    }
  },
    mounted() {
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
 computed:{
    disabled(){
      if (this.form.OrderNo == '') {
        return false
      } else {
        return true
      }
    }
  },

};
</script>
<style lang="less">
/deep/.ant-select-dropdown-menu-item{
  color: #000000;
  font-weight: 500;
}

.ant-form-item{
  margin-bottom: 0;
}
</style>
