<!-- 市场管理 - 订单报价- 查询-->
<template>
  <a-form-model :modal="form">
    <a-form-model-item label="订单号：" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.OrderNo" :autoFocus="autoFocus" v-focus-next-on-enter="'input2'" ref="input1" allowClear />
    </a-form-model-item>
    <a-form-model-item label="客户代码" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.custNo" v-focus-next-on-enter="'input3'" ref="input2" allowClear />
    </a-form-model-item>
    <a-form-model-item label="客户型号：" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.PcbFileName" v-focus-next-on-enter="'input4'" ref="input3" allowClear />
    </a-form-model-item>
    <a-form-model-item label="生产型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.proOrderNo" v-focus-next-on-enter="'input5'" ref="input4" allowClear />
    </a-form-model-item>
    <a-form-model-item label="客户订单号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.custPo" v-focus-next-on-enter="'input6'" ref="input5" allowClear />
    </a-form-model-item>
    <a-form-item label="订单状态" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-select ref="select" v-model="form.Status">
        <a-select-option value="">请选择 </a-select-option>
        <a-select-option value="15">待报价 </a-select-option>
        <a-select-option value="20">报价中 </a-select-option>
        <a-select-option value="22">待付款 </a-select-option>
        <a-select-option value="30">已完成 </a-select-option>
        <a-select-option value="35">已暂停 </a-select-option>
        <a-select-option value="40">已取消 </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="订单类型" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-select ref="select" v-model="form.ReOrder">
        <a-select-option value="0">新单 </a-select-option>
        <a-select-option value="1">返单 </a-select-option>
        <a-select-option value="2">返单更改 </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="开始时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-date-picker format="YYYY-MM-DD" @change="StartTime"></a-date-picker>
    </a-form-item>
    <a-form-item label="结束时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-date-picker format="YYYY-MM-DD" @change="EndTime" :disabled="form.StartTime ? false : true"></a-date-picker>
    </a-form-item>
    <a-form-model-item label="合同号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.contractNo" v-focus-next-on-enter="'input5'" ref="input4" allowClear />
    </a-form-model-item>
    <a-form-model-item label="业务员" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-select ref="select" v-model="form.Functionary" allowClear showSearch optionFilterProp="lable">
        <a-select-option v-for="(item, index) in Quoterlist" :key="index" :lable="item.text" :value="item.valueMember">{{
          item.text
        }}</a-select-option>
      </a-select>
    </a-form-model-item>
    <a-form-model-item label="报价人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-select ref="select" v-model="form.CheckAccount" allowClear showSearch optionFilterProp="lable">
        <a-select-option v-for="(item, index) in Quoterlist" :key="index" :lable="item.text" :value="item.valueMember">{{
          item.text
        }}</a-select-option>
      </a-select>
    </a-form-model-item>
    <a-form-model-item label="物料号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.CustomerMaterialNo" v-focus-next-on-enter="'input5'" ref="input4" allowClear />
    </a-form-model-item>
    <a-form-model-item label="建立人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
      <a-input v-model="form.createName" allowClear />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
export default {
  name: "QueryInfo",
  props: ["Quoterlist"],
  data() {
    return {
      autoFocus: true,
      form: {
        StartTime: null,
        EndTime: null,
        OrderNo: "", // 订单编号
        PcbFileName: "", // 文件名
        contractNo: "", //合同号
        proOrderNo: "",
        custNo: "",
        custPo: "", //客户订单号
        ReOrder: "",
        Status: "",
        Functionary: "",
        CheckAccount: "",
        CustomerMaterialNo: "",
        createName: "",
      },
    };
  },
  methods: {
    StartTime(value, dateString) {
      this.form.StartTime = dateString;
    },
    EndTime(value, dateString) {
      this.form.EndTime = dateString;
    },
    //  keyupEnter1(){
    //     this.$emit('keyupEnter1')
    // }
  },
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
// /deep/ .ant-calendar-picker-input{
//   height: 34px ;
//   padding: 0 10px 0px 10px;
// }
/deep/.ant-calendar-picker {
  width: 220px;
}
</style>
