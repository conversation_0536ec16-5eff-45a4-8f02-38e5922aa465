import { request, METHOD } from '@/utils/request';
import qs from 'querystring'
// 待发订单
export async function getWaitOrderList(params) {
    return request("/api/app/e-mSTProc-character-make/character-wait-distribute", METHOD.GET, params)
}
// 已发订单
export async function getDoingOrderList(params) {
    return request("/api/app/e-mSTProc-character-make/character-finish-distribute", METHOD.GET, params)
}
// 获取机台列表
export async function getMachineList(params) {
    return request("/api/app/e-mSTProc-character-make/character-machine-list", METHOD.GET, params)
}
// 当班统计
export async function getStatisticsList(params) {
    return request("/api/app/e-mSTProc-eTMake/get-ondutystatistics", METHOD.GET, params)
}
// 字符当班统计
export async function getOndutystatistics() {
    return request("/api/app/e-mSTProc-character-make/get-ondutystatistics", METHOD.GET, )
}
// 机台上订单
export async function getDispatchList(id,params) {
    return request(`/api/app/e-mSTProc-character-make/${id}/get-flying-probe-finish-order`, METHOD.GET,params)
}
// 分派机台
export async function getDispatchMachineList(params) {
    return request('/api/app/e-mSTProc-character-make/drill-send-machine', METHOD.POST, params)
}
// 分派回退
export async function getDispatchFallbackList (params) {
    return request('/api/app/e-mSTProc-character-make/drill-back-machine', METHOD.POST, params)
}
// 获取已上机机台数组
export async function getOrderMuIdList (Id) {
    return request(`/api/app/e-mSTProc-character-make/order-mu-id-list/${Id}`, METHOD.GET,)
}
// 获取部门过序数量
export async function getCharacterNum (Id) {
    return request(`/api/app/e-mSTProc-character-make/character-num?cardNo=${Id}`, METHOD.GET,)
}
// 字符部门过序
export async function characterOverCount (params) {
    return request(`/api/app/e-mSTProc-character-make/character-over-count`, METHOD.POST,params)
}
// 异常备注
export async function flyingProbeRemarks (Id,params) {
    return request(`/api/app/e-mSTProc-character-make/flying-probe-remarks/${Id}`, METHOD.GET,params)
}
// 修改pnl数
export async function up4CharacterPnlQty (params) {
    return request(`/api/app/e-mSTProc-character-make/up4Character-pnl-qty`, METHOD.POST,params)
}
// 单击完成
export async function finishCharacter (Id) {
    return request(`/api/app/e-mSTProc-character-make/finish-character`, METHOD.POST,Id)
}
// 测试文件
export async function downLoadPath (Id) {
    return request(`/api/app/e-mSTProc-character-make/y-down-load-path/${Id}`, METHOD.GET,)
}
// 数据核对
export async function DataCheck() {
    return request(`/api/app/e-mSTProc-character-make/data-check`, METHOD.POST,)
}

export default {
    getWaitOrderList,
    getDoingOrderList,
    getMachineList,
    getStatisticsList,
    getDispatchList,
    getDispatchMachineList,
    getDispatchFallbackList,
    getOrderMuIdList,
    getCharacterNum,
    characterOverCount,
    flyingProbeRemarks,
    up4CharacterPnlQty,
    finishCharacter,
    downLoadPath,
    DataCheck,
    getOndutystatistics,
}
