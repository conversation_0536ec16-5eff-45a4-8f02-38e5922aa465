<template>
    <div :id="el" style="width:100%; height:142px; position: absolute; left: 0; bottom: 10px;"></div>
</template>
<script>
import * as echarts from 'echarts/core';
import { TooltipComponent, LegendComponent } from 'echarts/components';
import { PieChart } from 'echarts/charts';
import { LabelLayout } from 'echarts/features';
import { SVGRenderer } from 'echarts/renderers';

echarts.use([
  TooltipComponent,
  LegendComponent,
  Pie<PERSON>hart,
  SVGRenderer,
  LabelLayout
]);
export default {
  props:["el","echartdata", "minAngle"],
  data() {
    return {
      data:[]
    };
  },
  watch: {
    // echartdata (val) {
    //   console.log(111111111111111)
    //   this.data = val
    //   this.drawLine()
    // }
  },
  mounted(){
    this.drawLine()
  },
  methods:{
    drawLine(){
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: function(params){
                var tip = "";
                if(params != null && params.data.name == "自动化订单") {
                    tip = params.name + ":" + params.value + "</br>" + "直通订单数:" + params.data.autoThroughValue 
                } else {
                    tip = params.name + ":" + params.value
                }
                // console.log(tip)
                return tip;
                
            }
          },
          legend: {
            top: '5%',
            left: 'center',
            data: []
          },
          color: ["#FFDC60", "#91CC75", "#ff9900", "#DDDFE3"],
          series: [
            {
              type:'pie',
                radius: ['0%', '95%'],
                center: ['80%', '50%'],
                avoidLabelOverlap: false,
                hoverAnimation: false, 
                label: {
                    normal: {
                      position: 'inner',
                      show: true,
                      textStyle : {
                        fontWeight : 600 ,
                        fontSize : 10
                      },
                      formatter:'{d}%'
                    },
                },
                minAngle: this.minAngle || 0,
              data: this.echartdata || []
            }
          ]
        }
        let chartDom = document.getElementById(this.el);
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      })
    }
  },
};
</script>
<style scoped>
</style>
