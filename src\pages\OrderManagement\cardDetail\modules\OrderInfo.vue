<template>
  <div class="contentInfo">
    <ul :model="formInfo">
      <li>
        <div style="width: 10%; display: inline-block">管制卡编号 :</div>
        <a-input v-cloak v-model="formInfo.cardNo" disabled style="width: 20%" />
      </li>
      <li>
        <div style="width: 10%; display: inline-block">状态 :</div>
        <a-radio-group v-model="formInfo.status" name="radioGroup">
          <a-radio value="10">生产中</a-radio>
          <a-radio value="20">已完成</a-radio>
          <a-radio value="30">已作废</a-radio>
          <a-radio value="40">已暂停</a-radio>
        </a-radio-group>
      </li>
      <li>
        <div style="width: 10%; display: inline-block">生产优先级 :</div>
        <a-radio-group v-model="formInfo.priority">
          <a-radio value="1">紧急</a-radio>
          <a-radio value="2">优先</a-radio>
          <a-radio value="3">正常</a-radio>
        </a-radio-group>
      </li>
      <li>
        <div style="width: 10%; display: inline-block">投料PNL数 :</div>
        <a-input v-model="formInfo.num" style="width: 20%" />
      </li>
      <li>
        <div style="width: 10%; display: inline-block">生产交期 :</div>
        <a-date-picker
          v-model="formInfo.deliveryDate"
          valueFormat="YYYY-MM-DD HH:mm:ss "
          :showTime="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
          @change="onChange1"
        />
      </li>
      <li>
        <div style="width: 10%; display: inline-block">备注 :</div>
        <a-input v-model="formInfo.remark" style="width: 20%" />
      </li>
      <li>
        <div style="display: inline-block; margin-left: 13%">到达时间</div>
        <div style="display: inline-block; margin-left: 7%">清理时间</div>
      </li>
      <li v-for="(item, index) in formInfo.proProcesstimes" :key="index">
        <div style="width: 10%; display: inline-block">{{ item.stepName }} :</div>
        <a-date-picker
          valueFormat="YYYY-MM-DD HH:mm:ss "
          v-model="item.arrivalTime"
          style="width: 10%"
          @change="(data, dateString) => onChange2(data, dateString, index)"
        />
        <a-date-picker
          valueFormat="YYYY-MM-DD HH:mm:ss "
          v-model="item.deadLineTime"
          style="width: 10%; margin-left: 0.6%"
          @change="(data, dateString) => onChange3(data, dateString, index)"
        />
      </li>
    </ul>
  </div>
</template>

<script>
import moment from "moment";
import { getEditOrderInfo, getSelectOption } from "@/services/mkt/orderInfo";
import $ from "jquery";
export default {
  name: "OrderInfo",
  props: ["formInfo"],
  data() {
    return {};
  },

  created() {},
  watch: {
    formData(val) {
      if (val.needReportList != null) {
        this.ReportList = val.needReportList.split(",");
      }
      // console.log(this.ReportList)
    },
  },
  methods: {
    moment,
    onChange1(value, dateString) {
      this.formInfo.deliveryDate = dateString;
    },
    onChange2(value, dateString, index) {
      console.log(index, value, dateString, "22");
      this.formInfo.proProcesstimes[index].arrivalTime = dateString;
    },
    onChange3(value, dateString, index) {
      console.log(index, value, dateString, "33");
      this.formInfo.proProcesstimes[index].deadLineTime = dateString;
    },
  },
};
</script>

<style scoped lang="less">
.contentInfo {
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 32px;
          .ant-select {
            width: 120px;
          }
          .ant-input {
            width: 120px;
          }
          .ant-input-number {
            width: 120px;
          }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          label {
            font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          }
        }
        .ant-form-item-control-wrapper {
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 8px 10px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
ul li {
  list-style-type: none;
  height: 40px;
  line-height: 40px;
}
</style>
