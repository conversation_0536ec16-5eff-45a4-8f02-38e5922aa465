<!-- 物料管理-物料录入-->
<template>
  <a-spin :spinning="spinning">
    <div class="projectMake1">
      <div style="width: 100%">
        <div class="leftContent">
          <a-table
            :columns="columns1"
            :dataSource="data1Source"
            :loading="TableLoading1"
            :rowKey="'id'"
            @change="handleTableChange"
            :pagination="pagination"
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            bordered
            class="maintable"
            ref="orderTable"
            :scroll="{ x: 1700, y: 726 }"
            v-if="key == 1"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
          </a-table>
          <a-table
            :columns="columns4"
            :dataSource="data4Source"
            :loading="TableLoading1"
            :rowKey="'id'"
            @change="handleTableChange"
            :pagination="pagination"
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            class="maintable"
            ref="orderTable"
            :scroll="{ y: 726, x: 1200 }"
            bordered
            v-if="key == 4"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <span slot="operate" slot-scope="record">
              <span style="color: rgb(66, 139, 202)" @click="Operationlog(record)">日志</span>
            </span>
          </a-table>
          <a-table
            :columns="columns5"
            :dataSource="data5Source"
            :loading="TableLoading1"
            :rowKey="'id'"
            class="maintable"
            :customRow="onClickRow"
            @change="handleTableChange"
            :pagination="pagination"
            :scroll="{ x: 1600, y: 726 }"
            :rowClassName="isRedRow"
            ref="orderTable"
            bordered
            v-if="key == 5"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
          </a-table>
          <a-table
            :columns="columns6"
            :dataSource="data6Source"
            :loading="TableLoading1"
            :rowKey="'id'"
            class="maintable"
            @change="handleTableChange"
            :pagination="pagination"
            :scroll="{ x: 1600, y: 726 }"
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            ref="orderTable"
            bordered
            v-if="key == 6"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <template slot="hF_" slot-scope="text, record">
              <a-checkbox v-model="record.hF_"> </a-checkbox>
            </template>
          </a-table>
        </div>
      </div>

      <div class="footerAction">
        <make-action
          ref="action"
          @pcbSetfile="pcbSetfile"
          @queryClick="queryClick"
          :total="pagination.total"
          @btnAdd="btnAdd"
          @edit="edit"
          @addCategory="addCategory"
          @deleteorder="deleteorder"
        />
      </div>
      <!-- 厂商建档 -->
      <a-modal
        title="厂商建档"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="新增"
        destroyOnClose
        centered
        :maskClosable="false"
        :width="500"
      >
        <pcbSetfile ref="pcbSetfile" :proOptions="proOptions" :proOptions1="proOptions1" />
      </a-modal>
      <!-- 新增物料 -->
      <a-modal
        :title="titleNew"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        centered
        @ok="handleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        class="wuliao"
        :width="800"
      >
        <btn-add
          ref="btnAdd"
          :materialCategory="materialCategory"
          :materialCategory1="materialCategory1"
          :materialCategory2="materialCategory2"
          :selectOption="selectOption"
          :platetypenew="platetypenew"
          :selectedRowsData="selectedRowsData"
          :ide="ide"
          :editnew="editnew"
          :materialtype="materialtype"
          :pptype="pptype"
          :tgtype="tgtype"
          :factoryname="factoryname"
          :ppcategory="ppcategory"
          :pptgvalue="pptgvalue"
          :factory="factory"
          :topoz="topoz"
          :bomoz="bomoz"
          :user="user"
        />
      </a-modal>
      <!-- 操作日志弹窗 -->
      <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose :maskClosable="false" @cancel="reportHandleCancel" :width="800" centered>
        <div class="projectackend">
          <a-table
            :columns="laborcolumns"
            :dataSource="labordata"
            :pagination="false"
            :scroll="{ y: 541 }"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
          >
          </a-table>
        </div>
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">取消</a-button>
        </template>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="物料查询"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        centered
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
      >
        <query-info
          ref="queryInfo"
          :topoz="topoz"
          :bomoz="bomoz"
          :materialCategory="materialCategory"
          :pptype="pptype"
          :pptgvalue="pptgvalue"
          :materialtype="materialtype"
          :platetypenew="platetypenew"
          :selectOption="selectOption"
          :VendorData="VendorData"
        />
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        title="确认弹窗"
        :visible="dataVisible6"
        @cancel="reportHandleCancel"
        @ok="handleOk6"
        centered
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
      >
        <span style="font-size: 16px">{{ ConfirmationInformation }}</span>
      </a-modal>
      <!-- 添加类别 -->
      <a-modal
        title="添加类别"
        :visible="dataVisible5"
        @cancel="reportHandleCancel"
        @ok="handleOk5"
        ok-text="确定"
        centered
        destroyOnClose
        :maskClosable="false"
        :width="400"
      >
        <add-category
          ref="addCategory"
          :pptype="pptype"
          :tgtype="tgtype"
          :materialCategory2="materialCategory2"
          :producttype="producttype"
          :factory="factory"
        />
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import { mapState } from "vuex";
import {
  getkind,
  matervendornolist,
  getsupplier,
  addPcbMessage,
  addMaterialMessage,
  putMaterialMessage,
  pageMaterList,
  getPlatetype,
  getcoreozlist,
  getPPtype,
  getPPcategory,
  factroylist,
  addfl,
  getTgtype,
  getmaterialtype,
  getproducttype,
  getfactory,
  getPPtgvalue,
  softdelmaterno,
} from "@/services/mkt/materialmodule.js";
import Cookie from "js-cookie";
import MakeAction from "@/pages/materialmodule/MaterialEntry/module/MakeAction";
import PcbSetfile from "@/pages/materialmodule/MaterialEntry/module/PcbSetfile";
import BtnAdd from "@/pages/materialmodule/MaterialEntry/module/BtnAdd.vue";
import QueryInfo from "@/pages/materialmodule/MaterialEntry/module/QueryInfo.vue";
import AddCategory from "@/pages/materialmodule/MaterialEntry/module/AddCategory.vue";

// 钻刀物料数据展示
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 40,
  },
  {
    title: "物料编码",
    align: "left",
    ellipsis: true,
    width: 65,
    dataIndex: "materNo_",
    className: "userStyle",
  },
  {
    title: "物料类别",
    dataIndex: "materTypeName_",
    align: "left",
    ellipsis: true,
    width: 75,
    className: "userStyle",
  },
  {
    title: "物料名称",
    dataIndex: "materName_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 75,
  },
  {
    title: "物料规格",
    dataIndex: "materSpec_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 200,
  },
  {
    title: "供应商",
    dataIndex: "verdorName_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "单位",
    dataIndex: "unitsName_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 40,
  },
  {
    title: "板材类型",
    className: "userStyle",
    dataIndex: "coreTypeCodes_",
    align: "left",
    ellipsis: true,
    width: 40,
  },
  {
    title: "板材厚度",
    dataIndex: "core_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 40,
  },
  {
    title: "顶铜",
    dataIndex: "topOZ_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "底铜",
    align: "left",
    className: "userStyle",
    ellipsis: true,
    width: 80,
    dataIndex: "bomOZ_",
  },
  {
    title: "内DK",
    className: "userStyle",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "外DK",
    dataIndex: "",
    className: "userStyle",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "是否含铜",
    className: "userStyle",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "无卤素",
    className: "userStyle",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "TG值",
    dataIndex: "",
    className: "userStyle",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "大料长",
    className: "userStyle",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "大料宽",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "CAF",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "CTI",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "水印",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "PTFE",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "是否阻燃",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "基板颜",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "适用行",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "适用产",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "燃烧性",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "剥离强",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "系列等",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "抗弯强",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "适宜工",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "不适宜",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "热应力",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "表面电",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "耐电弧",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "吸水率",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
];
// 板材物料数据展示
const columns4 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 40,
    className: "userStyle",
  },
  {
    title: "类别",
    dataIndex: "materTypeName_",
    align: "left",
    ellipsis: true,
    className: "userStyle",
    width: 45,
  },
  {
    title: "规格",
    dataIndex: "materSpec_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 350,
  },
  {
    title: "型号",
    dataIndex: "coreTypeCodesStr",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 150,
  },
  {
    title: "TG值",
    dataIndex: "tgType_",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "板厚",
    dataIndex: "core_",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "含铜",
    dataIndex: "tD_",
    customRender: (text, record, index) => `${record.tD_ == true ? "是" : ""}`,
    align: "center",
    width: 50,
    ellipsis: true,
  },
  {
    title: "顶铜",
    dataIndex: "topOZ_",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "底铜",
    align: "left",
    ellipsis: true,
    width: 50,
    dataIndex: "bomOZ_",
  },
  {
    title: "DK",
    dataIndex: "dkOuter_",
    align: "left",
    width: 50,
    ellipsis: true,
  },
  {
    title: "长",
    dataIndex: "pnlSizeX_",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "宽",
    dataIndex: "pnlSizeY_",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "无卤",
    customRender: (text, record, index) => `${record.halogen_ == true ? "是" : ""}`,
    align: "center",
    width: 50,
    ellipsis: true,
  },
  {
    title: "水印",
    dataIndex: "waterMark_",
    customRender: (text, record, index) => `${record.waterMark_ == true ? "是" : ""}`,
    align: "center",
    width: 50,
    ellipsis: true,
  },
  {
    title: "PTFE",
    dataIndex: "ptfE_",
    customRender: (text, record, index) => `${record.ptfE_ == true ? "是" : ""}`,
    align: "center",
    width: 50,
    ellipsis: true,
  },
  {
    title: "CAF",
    dataIndex: "caF_",
    customRender: (text, record, index) => `${record.caF_ == true ? "是" : ""}`,
    align: "center",
    width: 50,
    ellipsis: true,
  },
  {
    title: "CTI",
    dataIndex: "ctI_",
    customRender: (text, record, index) => `${record.ctI_ == true ? "是" : ""}`,
    align: "center",
    width: 50,
    ellipsis: true,
  },
  // {
  //   title: "第三方编码",
  //   align: "left",
  //   ellipsis: true,
  //   width: 250,
  //   dataIndex:'erpKey_',
  // },
  // {
  //   title: "采购价",
  //   align: "left",
  //   ellipsis: true,
  //   width: 350,
  //   dataIndex:'purchasePrice',
  // },
];
// 铜箔物料数据展示
const columns5 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 40,
  },
  {
    title: "类别",
    dataIndex: "materTypeName_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "规格",
    dataIndex: "materSpec_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 120,
  },
  {
    title: "内外",
    dataIndex: "innerOrOuterStr",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "型号",
    align: "left",
    ellipsis: true,
    width: 70,
    dataIndex: "cU_",
  },
  {
    title: "厚度",
    dataIndex: "cuThickness_",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "工厂",
    dataIndex: "",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "第三方编码",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 200,
    dataIndex: "erpKey_",
    className: "userStyle",
  },
  {
    title: "采购价",
    align: "left",
    ellipsis: true,
    width: 350,
    dataIndex: "purchasePrice",
  },
];
// 半固化片物料数据展示
const columns6 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 40,
  },
  {
    title: "类别",
    dataIndex: "materTypeName_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "规格",
    dataIndex: "materSpec_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "类别",
    dataIndex: "ppTypeCodesStr_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "TG值",
    dataIndex: "tG_",
    align: "left",
    width: 30,
    ellipsis: true,
  },
  {
    title: "类型",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "pP_",
  },
  {
    title: "厚度",
    dataIndex: "thicknesS_",
    align: "left",
    width: 30,
    ellipsis: true,
  },
  {
    title: "含胶量",
    dataIndex: "rC_",
    align: "left",
    width: 30,
    ellipsis: true,
  },
  {
    title: "内DK",
    dataIndex: "ppDKInner_",
    align: "left",
    width: 30,
    ellipsis: true,
  },
  {
    title: "外DK",
    dataIndex: "ppDKOuter_",
    align: "left",
    width: 30,
    ellipsis: true,
  },
  {
    title: "无卤素",
    customRender: (text, record, index) => `${record.hF_ == true ? "是" : ""}`,
    align: "center",
    width: 30,
    ellipsis: true,
  },
  {
    title: "正公差",
    dataIndex: "thicknesS_TOL_UP_",
    align: "left",
    width: 30,
    ellipsis: true,
  },
  {
    title: "负公差",
    dataIndex: "thicknesS_TOL_DN_",
    align: "left",
    width: 30,
    ellipsis: true,
  },
  {
    title: "第三方编码",
    align: "left",
    ellipsis: true,
    width: 150,
    dataIndex: "erpKey_",
  },
  {
    title: "采购价",
    align: "left",
    ellipsis: true,
    width: 150,
    dataIndex: "purchasePrice",
  },
];
//操作日志
const laborcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 20,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作时间",
    align: "left",
    dataIndex: "createTime",
    width: 65,
  },
  {
    title: "操作人",
    align: "left",
    dataIndex: "userName",
    width: 30,
  },
  {
    title: "内容",
    align: "left",
    dataIndex: "content",
    width: 185,
  },
];
export default {
  name: "materialmodule",
  components: { MakeAction, PcbSetfile, QueryInfo, AddCategory, BtnAdd },
  inject: ["reload"],
  data() {
    return {
      factoryList: [],
      labordataVisible: false,
      ConfirmationInformation: "",
      laborcolumns,
      labordata: [],
      spinning: false,
      pagination: {
        current: 1,
        pageSize: 20,
        showTotal: total => `总计 ${total} 条`,
        total: 0,
        MaterType_: "",
        pageIndex: 1,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
      },
      columns1,
      columns4,
      columns5,
      columns6,
      data1Source: [],
      selectOption: {},
      VendorData: [], //获取厂商
      data4Source: [],
      data5Source: [],
      data6Source: [],
      key: 4,
      TableLoading1: false,
      dataVisible: false, // 查询弹窗开关
      dataVisible1: false, // 新增物料开关
      dataVisible4: false, // 查询
      dataVisible5: false, //添加类别
      dataVisible6: false, // 确认弹窗
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      stackListData: {}, // 生成叠层
      StackImpedanceData: [], // 叠层阻抗
      CuttingData: [], // 开料拼版
      proOptions: [],
      proOptions1: [],
      materialCategory: [], //物料类别
      materialCategory1: [], //进货单位
      materialCategory2: [], //供应商
      platetypenew: [], //板材类型
      pptype: [], //对应pp类型
      tgtype: [], //对应tg类型
      topoz: [],
      bomoz: [],
      selectedRowsData: {},
      ide: "",
      selectedRowKeysArray: [],
      id: "",
      editnew: false,
      titleNew: "新增物料",
      materialtype: [], //物料类别
      producttype: [], //产品类别
      factory: [], //工厂
      factoryname: [], //新增erp工厂
      ppcategory: [], //pp类型
      pptgvalue: [], //pp tg值
      queryInfo: {},
    };
  },
  created() {
    this.$nextTick(() => {
      this.getcoreozlist();
      this.getproKey();
      this.getproKey1();
      this.getMaterial();
      this.getMaterial1();
      this.getsupplier1();
      this.getPlatetype1(this.user.factoryId);
      this.getPPtype1();
      this.getTgtype1();
      this.getmaterialtype1();
      this.getproducttype1();
      this.getPPcategory1();
      this.getPPtgvalue1();
      this.getfactory1();
      this.factroylist();
      this.getOrderList({ MaterSpec_: "", MaterType_: 908 });
      this.getVendor();
      this.handleResize();
    });
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  methods: {
    handleResize(data) {
      var mainstyle = document.getElementsByClassName("maintable")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 145 + "px";
      } else {
        leftContent.style.height = "771px";
      }
      if (mainstyle && data && data.length != 0) {
        mainstyle.style.height = window.innerHeight - 185 + "px";
      } else {
        mainstyle.style.height = 0;
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.action.nums * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
          if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
            if (footerwidth < paginnum * 25 + 200) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            if (window.innerWidth > 766) {
              if (footerwidth < paginnum * 45) {
                this.pagination.simple = true;
              } else {
                this.pagination.simple = false;
              }
            } else {
              this.pagination.simple = true;
            }
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    Operationlog(record) {
      this.labordataVisible = true;
    },
    // 获取列表
    getOrderList(queryData) {
      let params = {};
      if (queryData) {
        params = queryData;
      }
      params.PageIndex = this.pagination.current;
      params.PageSize = this.pagination.pageSize;
      if (queryData) {
        if (queryData.MaterType_) {
          params.MaterType_ = queryData.MaterType_;
        } else {
          params.MaterType_ = 908;
        }
      } else {
        params.MaterType_ = 908;
      }
      this.TableLoading1 = true;
      pageMaterList(params)
        .then(res => {
          if (res.code) {
            if (params.MaterType_ == 831) {
              this.key = 1;
              this.data1Source = res.data.items;
              localStorage.setItem("maindata", JSON.stringify(this.data1Source));
              setTimeout(() => {
                this.handleResize(this.data1Source);
              }, 0);
              this.pagination.total = res.data.totalCount;
            }
            if (params.MaterType_ == 908) {
              this.key = 4;
              this.data4Source = res.data.items;
              localStorage.setItem("maindata", JSON.stringify(this.data4Source));
              setTimeout(() => {
                this.handleResize(this.data4Source);
              }, 0);
              this.pagination.total = res.data.totalCount;
            }
            if (params.MaterType_ == 909) {
              this.key = 5;
              this.data5Source = res.data.items;
              localStorage.setItem("maindata", JSON.stringify(this.data5Source));
              setTimeout(() => {
                this.handleResize(this.data5Source);
              }, 0);
              this.pagination.total = res.data.totalCount;
            }
            if (params.MaterType_ == 914) {
              this.key = 6;
              this.data6Source = res.data.items;
              localStorage.setItem("maindata", JSON.stringify(this.data6Source));
              setTimeout(() => {
                this.handleResize(this.data6Source);
              }, 0);
              this.pagination.total = res.data.totalCount;
            }
          } else {
            this.$message.error(res.data);
          }
        })
        .finally(() => {
          this.TableLoading1 = false;
        });
    },
    //  选中传参编辑
    onClickRow(record, index) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.ide = index;
            this.selectedRowsData.coreTypeCodes_ = Number(this.selectedRowsData.coreTypeCodes_);
            this.id = record.id;
          },
          dblclick: event => {
            this.$emit("webSocketLink");
            setEngineeringMake({
              token: record.id,
            });
            this.getcookie("orderno");
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
        },
      };
    },
    // 厂商建档
    pcbSetfile() {
      this.dataVisible = true;
    },
    handleOk() {
      this.spinning = true;
      let queryData = this.$refs.pcbSetfile.form;
      addPcbMessage(queryData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.dataVisible = false;
          this.getsupplier1();
        });
    },
    // 新增物料
    btnAdd() {
      this.dataVisible1 = true;
      this.editnew = false;
      this.titleNew = "新增物料";
    },
    handleOk1() {
      let queryData = this.$refs.btnAdd.form1;
      let erpKey_ = [];
      var r = /^0{1}(\.\d*)|(^[1-9][0-9]*)+(\.\d*)?$/;
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      var y = /^(?:\d+(?:\.\d+)?|(?:(?:\d+(?:\.\d+)?|0)_PT_(?:\d+(?:\.\d+)?|0)))$/;
      if (!queryData.materType_) {
        this.$message.warning("请选择物料类别");
        return;
      }
      if (!queryData.materVendor_) {
        this.$message.warning("请选择供应商");
        return;
      }
      if (!queryData.units_) {
        this.$message.warning("请选择进货单位");
        return;
      }
      if (queryData.materType_ == 908) {
        if (!queryData.coreTypeCodes_) {
          this.$message.warning("请选择板材类型");
          return;
        }
        if (!queryData.core_) {
          this.$message.warning("请填写板材厚度");
          return;
        }
        if (!queryData.topOZ_) {
          this.$message.warning("请选择顶铜厚度");
          return;
        }
        if (!queryData.bomOZ_) {
          this.$message.warning("请选择底铜厚度");
          return;
        }
        if (!r.test(queryData.dkInner_)) {
          this.$message.warning("请填写内层DK为正数");
          return;
        }
        if (!r.test(queryData.dkOuter_)) {
          this.$message.warning("请填写外层DK为正数");
          return;
        }
        if (!r.test(queryData.pnlSizeX_)) {
          this.$message.warning("请填写大板长为正数");
          return;
        }
        if (!r.test(queryData.pnlSizeY_)) {
          this.$message.warning("请填写大板宽为正数");
          return;
        }
      }
      if (queryData.materType_ == 909) {
        if (!queryData.innerOrOuter) {
          this.$message.warning("请选择内外铜箔");
          return;
        }
        if (!queryData.cU_) {
          this.$message.warning("请填写铜箔名称");
          return;
        }
        if (!y.test(queryData.cU_)) {
          this.$message.warning("铜箔名称请输入正确格式 如:0.5 或 0.33_PT_1");
          return;
        }
        if (!x.test(queryData.cuThicknessOrg_)) {
          this.$message.warning("请填写基础铜箔厚度为正数");
          return;
        }
        if (!x.test(queryData.cuThickness_)) {
          this.$message.warning("请填写完成铜箔厚度为正数");
          return;
        }
        if (!x.test(queryData.t1Value_)) {
          this.$message.warning("请填写T值为正数");
          return;
        }
        if (!queryData.joinFactoryId) {
          this.$message.warning("请选择工厂");
          return;
        }
      }
      if (queryData.materType_ == 914) {
        if (!queryData.ppTypeCodes_) {
          this.$message.warning("请选择PP类别");
          return;
        }
        if (!queryData.pP_) {
          this.$message.warning("请选择PP类型");
          return;
        }
        if (!r.test(queryData.rC_)) {
          this.$message.warning("请填写含胶量为正数");
          return;
        }
        if (!queryData.tG_) {
          this.$message.warning("请选择TG值");
          return;
        }
        if (!r.test(queryData.thicknesS_)) {
          this.$message.warning("请填写PP厚度为正数");
          return;
        }
        if (!r.test(queryData.dkInner_)) {
          this.$message.warning("请输入内DK为正数");
          return;
        }
        if (!r.test(queryData.dkOuter_)) {
          this.$message.warning("请输入外DK为正数");
          return;
        }
        if (queryData.ppThickness4NY_ && !x.test(queryData.ppThickness4NY_) && this.user.factoryId != 12) {
          this.$message.warning("玻布厚度请输入非负数");
          return;
        }
        if (!x.test(queryData.ppThickness4NY_) && this.user.factoryId == 12) {
          this.$message.warning("玻布厚度请输入非负数");
          return;
        }
        queryData.ppThickness4NY_ = queryData.ppThickness4NY_ == "" || !queryData.ppThickness4NY_ ? null : Number(queryData.ppThickness4NY_);
      }
      if (this.$refs.btnAdd.wuliaodata.length == 1 && queryData.materType_ == 908) {
        if (this.$refs.btnAdd.wuliaodata[0].faid == "") {
          this.$message.warning("请选择编码工厂");
          return;
        }
        if (this.$refs.btnAdd.wuliaodata[0].erpkey == "") {
          this.$message.warning("请输入编码");
          return;
        }
      }
      for (let index = 0; index < this.$refs.btnAdd.wuliaodata.length - 1; index++) {
        const element = this.$refs.btnAdd.wuliaodata[index];
        if (element.faid == "") {
          this.$message.warning("请选择编码工厂");
          return;
        }
        if (element.erpkey == "") {
          this.$message.warning("请输入编码");
          return;
        }
      }
      for (let i = 0; i < this.$refs.btnAdd.wuliaodata.length - 1; i++) {
        const element = this.$refs.btnAdd.wuliaodata[i];
        erpKey_.push("F" + element.faid + ":" + element.erpkey);
      }
      let purchasePrice = {}; // 初始化为一个空对象，而不是数组

      for (let a = 0; a < this.$refs.btnAdd.purchasedata.length - 1; a++) {
        const element = this.$refs.btnAdd.purchasedata[a];
        // 动态生成键名并设置对应的值
        purchasePrice[`F${element.faid}`] = {
          price: element.price.toString(), // 转换为字符串
          minpur: element.minpur.toString(), // 转换为字符串
        };
      }
      // 将对象转换为 JSON 字符串
      const jsonString = JSON.stringify(purchasePrice);
      console.log(jsonString);
      this.spinning = true;
      let params = {
        moduleNo: {},
        cu: {},
        core: {},
        pp: {},
      };
      if (queryData) {
        params.no = queryData.materType_; // 908板材、909铜箔、914半固化片
        //主要内容
        params.moduleNo.materType_ = queryData.materType_; //物料类别 int(908,909,910,914)
        params.moduleNo.materName_ = queryData.materName_; //物料名称
        params.moduleNo.materSpec_ = queryData.materSpec_; //物料规格
        params.moduleNo.materVendor_ = queryData.materVendor_; //供应商int
        params.moduleNo.units_ = queryData.units_; //单位 int
        params.moduleNo.erpKey_ = erpKey_.join(","); // erp编码
        params.moduleNo.purchasePrice = jsonString; // 采购价格
        params.id = this.selectedRowsData.id;
        //铜箔
        if (params.no == 909) {
          params.cu.innerOrOuter = queryData.innerOrOuter; //内外铜箔
          params.cu.cU_ = queryData.cU_; //铜厚
          params.cu.cuThickness_ = queryData.cuThickness_; //实际铜箔厚度
          params.cu.cuThicknessOrg_ = queryData.cuThicknessOrg_;
          params.cu.joinFactoryId = queryData.joinFactoryId; //工厂
          params.cu.cuDisplay_ = queryData.cuDisplay_; //排序
          params.cu.t1Value_ = queryData.t1Value_; //T值
          params.cu.c1_ = queryData.c1_;
          params.cu.c2_ = queryData.c2_;
          params.cu.c3_ = queryData.c3_;
          params.cu.cEr_ = queryData.cEr_;
          params.cu.lineWidthDiff_ = queryData.lineWidthDiff_;
          //板材
        } else if (params.no == 908) {
          params.core.coreTypeCodes_ = queryData.coreTypeCodes_; //板材类型
          params.core.core_ = queryData.core_; //板材厚度
          params.core.topOZ_ = queryData.topOZ_; //顶铜厚度
          params.core.bomOZ_ = queryData.bomOZ_; //底铜厚度
          params.core.dkInner_ = queryData.dkInner_; //内层DK
          params.core.dkOuter_ = queryData.dkOuter_; //外层DK
          params.core.tD_ = queryData.tD_; //是否含铜
          params.core.tgType_ = queryData.tgType_; //TG类型
          params.core.halogen_ = queryData.halogen_; //无卤素
          params.core.pnlSizeX_ = queryData.pnlSizeX_; //大板长
          params.core.pnlSizeY_ = queryData.pnlSizeY_; //大板宽
          params.core.caF_ = queryData.caF_; //是否CAF
          params.core.ctI_ = queryData.ctI_; //是否CTI
          params.core.waterMark_ = queryData.waterMark_; //是否水印
          params.core.meterBoard_ = queryData.meterBoard_; //电表板
          params.core.ptfE_ = queryData.ptfE_; //PTFE_
        } else if (params.no == 914) {
          params.pp.ppTypeCodes_ = queryData.ppTypeCodes_; //PP类别
          params.pp.pP_ = queryData.pP_; //PP类型
          params.pp.rC_ = queryData.rC_; //含胶量
          params.pp.tG_ = queryData.tG_; //TG_
          params.pp.thicknesS_ = queryData.thicknesS_; //PP厚度
          params.pp.thicknesS_TOL_UP_ = queryData.thicknesS_TOL_UP_; //正公差
          params.pp.thicknesS_TOL_DN_ = queryData.thicknesS_TOL_DN_; //负公差
          params.pp.dkInner_ = queryData.dkInner_; //内DK
          params.pp.dkOuter_ = queryData.dkOuter_; //外DK
          params.pp.ppThickness4NY_ = queryData.ppThickness4NY_; //玻布厚度
          params.pp.hF_ = queryData.hF_; //无卤素
          params.pp.rcIndex_ = queryData.rcIndex_; //排序
        }
      }
      this.dataVisible1 = false;
      if (this.editnew) {
        putMaterialMessage(params)
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              this.id = "";
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else {
        addMaterialMessage(params)
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              this.id = "";
              this.queryInfo.MaterType_ = params.no;
              this.getOrderList(this.queryInfo);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    // 编辑物料
    edit() {
      if (this.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要编辑的订单");
        return;
      }
      this.dataVisible1 = true;
      this.editnew = true;
      this.titleNew = "编辑物料";
    },
    //查询按钮
    queryClick() {
      this.dataVisible4 = true;
    },
    deleteorder() {
      if (this.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要删除的物料");
        return;
      }
      this.dataVisible6 = true;
      this.ConfirmationInformation = "确定要删除选中的物料吗?";
    },
    handleOk6() {
      softdelmaterno(this.id).then(res => {
        if (res.code) {
          this.$message.success("删除成功");
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
      this.dataVisible6 = false;
    },
    handleOk4() {
      this.dataVisible4 = false;
      this.pagination.current = 1;
      this.queryInfo = this.$refs.queryInfo.queryForm;
      this.getOrderList(this.queryInfo);
    },
    //添加类别
    addCategory() {
      this.dataVisible5 = true;
    },
    handleOk5() {
      this.spinning = true;
      let queryData = this.$refs.addCategory.queryForm;
      addfl(queryData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.dataVisible5 = false;
          //添加类别后立马刷新一下
          this.getPlatetype1();
          this.getPPtype1();
        });
    },
    //确认
    async httpRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });
      if (resData.code == 1) {
        orderFileUpload({ Pdctno: this.menuData.pdctno_, path: resData.data }).then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible = false; // 厂商建档
      this.dataVisible1 = false; // 物料新增
      this.dataVisible3 = false; // 编辑参数开关
      this.dataVisible4 = false; // 查询
      this.dataVisible5 = false; // 添加类别
      this.labordataVisible = false; //操作日志
      this.dataVisible6 = false; // 物料新增
    },
    // 获取厂商分类下拉
    getproKey() {
      this.loading = true;
      let proOptionsid = [122];
      getkind(proOptionsid).then(res => {
        if (res.code) {
          this.proOptions = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //获取厂商建档-[厂商级别]下拉列
    getproKey1() {
      this.loading = true;
      let proOptionsid = [121];
      getkind(proOptionsid).then(res => {
        if (res.code) {
          this.proOptions1 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //获取厂商类别
    getVendor() {
      console.log("111");
      matervendornolist().then(res => {
        if (res.code) {
          this.VendorData = res.data;
          console.log(this.VendorData, "VendorData");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //获取添加类别-[物料类别]下拉列
    getMaterial() {
      this.loading = true;
      let materialCategoryid = [118];
      getkind(materialCategoryid).then(res => {
        if (res.code) {
          this.materialCategory = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //获取新增-[进货单位]下拉列
    getMaterial1() {
      this.loading = true;
      let materialCategoryid = [119];
      getkind(materialCategoryid).then(res => {
        if (res.code) {
          this.materialCategory1 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //获取新增-[供应商]下拉列
    getsupplier1() {
      let params = {
        current: 1,
        PageSize: 9999,
      };
      getsupplier(params).then(res => {
        if (res.code) {
          this.materialCategory2 = res.data.items;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getcoreozlist() {
      getcoreozlist().then(res => {
        if (res.code) {
          this.topoz = res.data.topOZ_;
          this.bomoz = res.data.bomOZ;
        }
      });
    },
    //获取新增-板材-[板材类型]下拉列
    getPlatetype1(factory) {
      this.loading = true;
      getPlatetype().then(res => {
        if (res.code) {
          this.platetypenew = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //获取添加类别-[对应工厂]下拉列
    getfactory1() {
      this.loading = true;
      getfactory().then(res => {
        if (res.code) {
          this.factory = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    factroylist() {
      factroylist().then(res => {
        if (res.code) {
          this.factoryname = res.data;
        }
      });
    },
    // 获取新增-半固化片-[PP类别]下拉
    getPPtype1() {
      this.loading = true;
      getPPtype().then(res => {
        if (res.code) {
          this.pptype = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 获取新增-半固化片-[TG值]下拉
    getPPtgvalue1() {
      this.loading = true;
      getPPtgvalue().then(res => {
        if (res.code) {
          this.pptgvalue = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 获取新增-半固化片-PP类型下拉
    getPPcategory1() {
      this.loading = true;
      getPPcategory().then(res => {
        if (res.code) {
          this.ppcategory = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 获取添加分类对应Tg类别下拉
    getTgtype1() {
      this.loading = true;
      getTgtype().then(res => {
        if (res.code) {
          this.tgtype = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取物料类型下拉
    getmaterialtype1() {
      this.loading = true;
      let materialCategoryid = [118];
      getmaterialtype(materialCategoryid).then(res => {
        if (res.code) {
          this.materialtype = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 订单表变化change
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      //this.pagination.MaterType_ = pagination.MaterType_
      this.pagination.pageSize = pagination.pageSize;
      this.getOrderList(this.queryInfo);
    },
    // 获取添加分类对应Tg类别下拉
    getproducttype1() {
      this.loading = true;
      getproducttype().then(res => {
        if (res.code) {
          this.producttype = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 行点击事件
    isRedRow(record) {
      if (record.id == this.id) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
  },
  //刷新页面时默认展示板材数据
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
};
</script>

<style scoped lang="less">
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
.projectackend {
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
/deep/.ant-modal-title {
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-select {
  color: #000000;
}
/deep/.ant-input {
  color: #000000;
}
/deep/.ant-cascader-picker {
  color: #000000;
}
/deep/.ant-select-selection--multiple .ant-select-selection__choice {
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectMake1 {
  background: #ffffff;
  /deep/.leftContent {
    border: 2px solid rgb(238, 238, 238);
    border-bottom: 4px solid #e9e9f0;
    .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
  /deep/ .userStyle {
    user-select: all !important;
  }
  .footerAction {
    width: 100%;
    height: 48px;
    overflow: hidden;
    border: 2px solid #e9e9f0;
    border-top: 0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    background: #ffffff;
  }

  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display: none;
    }
  }
  /deep/ .ant-table-fixed {
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
      .eqBackground {
        background: #ffff00;
      }
      .statuBackground {
        background: #b0e0e6;
      }
      .backUserBackground {
        background: #a7a2c9;
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    .userStyle {
      user-select: all !important;
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 10px 4px;
      border-color: #f0f0f0;
    }
    .ant-table-tbody > tr {
      height: 35px;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      border-color: #f0f0f0;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }
    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    position: absolute;
    margin: 0;
    margin-left: 16px;
    margin-top: 8px;
  }
}
</style>
