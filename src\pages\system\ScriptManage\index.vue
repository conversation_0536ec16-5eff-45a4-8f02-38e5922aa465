<!-- 系统管理-脚本管理 -->
<template>
  <a-spin :spinning="spinning">
    <div class="ScriptManage">
      <div style="width: 100%">
        <div class="leftContent">
          <a-table
            :columns="columns"
            :dataSource="dataSource"
            :loading="TableLoading"
            :rowKey="'pcName'"
            :customRow="onClickRow"
            :rowClassName="isRedRow"            
            :pagination="pagination"
            @change="handleTableChange"
            :scroll="{ y:738,x:1500}"                    
            ref="orderTable"
            class="mainstyle"
          >
          <span slot="num" slot-scope="text, record, index">
           {{(pagination.current-1)*pagination.pageSize+parseInt(index)+1}}
          </span>
          <template slot="permission" slot-scope="text,record">
             <a-checkbox  :checked="record.permission">
             </a-checkbox>
          </template>

          </a-table>
        </div>
      </div>
      <div class="footerAction">
        <make-action
            ref="action"
            @queryClick="queryClick"
            @editAdd="editAdd"
            @deleteBtn="deleteBtn"
        />
      </div>
      <!-- 查询弹窗 -->
   <a-modal
          title="订单查询"
          :visible="dataVisible"
          @cancel="reportHandleCancel"
          @ok="handleOk1"
          ok-text="确定"
          centered
          destroyOnClose
          :maskClosable="false"
          :width="400"
   >
   <query-info ref='queryInfo'/>
   </a-modal>
   <!-- 编辑 -->
   <a-modal
           title="编辑"
          :visible="dataVisible1"
          @cancel="reportHandleCancel"
          centered
          @ok="handleOk2"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
   >
     <edit-add ref='editAdd'
     :selectedRowsData="selectedRowsData"/>
   </a-modal>

    </div>
  </a-spin>
</template>
<script>
import {
  getList,
  UpdateList,
  delList,
} from "@/services/identity/script.js";
import EditAdd from '@/pages/system/ScriptManage/module/EditAdd.vue';
import MakeAction from "@/pages/system/ScriptManage/module/MakeAction";
import QueryInfo from "@/pages/system/ScriptManage/module/QueryInfo";
// 数据展示
const columns = [
   {
        title: '序号',
        width: '3%',
        align: "center",
        ellipsis: true,
        scopedSlots: { customRender: "num" },
  },
  {
    title: "主机名",
    align: "left",
    ellipsis: true,
    width: '55%',
    dataIndex: "pcName",
    className: "userStyle",
  },
  {
    title: "版本",
    dataIndex: "version",
    align: "left",
    //fixed:'left',
    ellipsis: true,
    width: '10%',
  },
  {
    title: "操作时间",
    dataIndex: "inDate",
    align: "left",
    //fixed:'left',
    ellipsis: true,
    width: '16%',
  },
  {
    title: "职位",
    dataIndex: "useR_",
    align: "left",
    ellipsis: true,
    width: '10%',
  },
  {
    title: "姓名",
    dataIndex: "realName",
    align: "left",
    ellipsis: true,
    width: '10%',
  },
  {
    title: "授权",
    //dataIndex: "permission",
    scopedSlots: { customRender: "permission" },
    align: "center",
    ellipsis: true,
    width: '5%',
  },
];
export default {
    name: "ScriptManage",
    components: { MakeAction,QueryInfo,EditAdd},
    inject:['reload'],
  data() {
    return {
      spinning:false,
      columns,
      dataSource: [],
      TableLoading: false,
      dataVisible:false,
      dataVisible1:false,
      selectedRowsData:{},//选中数据
      id:'',
      pagination: {
          pageSize: 20,
          current: 1,
          total:0,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["20", "50", "100"],//每页中显示的数据
          showTotal: (total) => `总计 ${total} 条`,
      },
    };
  },
  created(){
    this.$nextTick(()=>{
      this.handleResize()
      this.getList1();
    })   
  },
  mounted() {
    window.addEventListener('resize', this.handleResize, true)
  },
  beforeDestroy(){
    window.removeEventListener('resize', this.handleResize, true)
   },
  computed:{},
  methods: {
    handleResize(){
      var mainstyle = document.getElementsByClassName('mainstyle')[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var leftContent = document.getElementsByClassName('leftContent')[0]
      if(window.innerHeight<=911){
        leftContent.style.height = window.innerHeight - 138 +'px'
      }else{
        leftContent.style.height = '761px'
      }  
      if(mainstyle && this.dataSource.length!=0){
        mainstyle.style.height =  window.innerHeight - 173 +'px'
      }else{
        mainstyle.style.height = 0
      }
      var paginnum = ''
        var footerwidth =  window.innerWidth-224
        if(Math.ceil(this.pagination.total/20)>10){
          paginnum = 7
        }else{
          paginnum = Math.ceil(this.pagination.total/20)
        }
        if(((paginnum*50)+310)<footerwidth){
        this.pagination.simple=false
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
        }else{
        this.pagination.simple=false
        this.pagination.size = 'small'
        this.pagination.showSizeChanger = false
        this.pagination.showQuickJumper = false
       }
    },
     // 获取列表
    getList1(queryForm) {
        this.TableLoading = true; 
        let params={
          ...this.pagination,
        };
        if(queryForm) {
        params.pcName = queryForm.pcName
      }         
      getList(params).then((res) => {
        if(res.code){
          this.dataSource = res.data.items;          
          this.pagination.total = res.data.items.totalCount;
          setTimeout(() => this.handleResize(), 0);
        }else{
          this.$message.error(res.message)
        }
        }).finally(()=> {
        this.TableLoading = false;
      });
     },
     handleTableChange(pagination, filters, sorter) {
      this.pagination.current=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      this.getList1();
    },
     //查询按钮
    queryClick(){
     this.dataVisible = true;     
    },
    //查询确认
    handleOk1(){
      this.dataVisible = false; 
      this.pagination.current = 1;     
      this.getList1(this.$refs.queryInfo.queryForm)
    },
    // 编辑
    editAdd(){
     this.dataVisible1 = true;
    },
    // 编辑确认
     handleOk2(){
      this.spinning = true;
      let queryData = this.$refs.editAdd.form1
      let params={};
      if(queryData){
          params=queryData
          params.id=queryData.pcName
          params.inDate=this.selectedRowsData.inDate
          params.version=this.selectedRowsData.version          
      }
       UpdateList(params).then(res=>{
        if (res.code == 1) {
           this.$message.success('编辑成功')
        }else {
            this.$message.error(res.message)
          }
      }).finally(()=> {      
         this.spinning = false;
         this.dataVisible1 = false;
         this.getList1();
      })
    },
    //删除
    deleteBtn(){        
        let id=this.selectedRowsData.pcName
        console.log('删除传值',id)
        if(confirm('确认删除该用户吗')){
            this.spinning = true;
            delList(id).then(res=>{            
            if(res.code==1){
                this.$message.success('删除成功')
            }else{                
                this.$message.error(res.message)
            }
            }).finally(()=> {
                this.spinning = false;
                this.getList1();
            });
        }
    },   
     // 弹窗关闭
    reportHandleCancel(){     
      this.dataVisible = false;  // 查询   
      this.dataVisible1 = false;  // 编辑  
      this.spinning = false; 
    },
    //  选中传参编辑
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.pcName);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.pcName = record.pcName;
            //console.log("选中当前行", this.selectedRowsData);
          },         
        },
      };
    },
    // 行点击事件
    isRedRow(record) {
      if (record.pcName == this.pcName) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.ant-table-pagination.ant-pagination {   
    margin: 11px;
    position: absolute;
    }
.ScriptManage {
  user-select: none;
  background: #FFFFFF;
  /deep/.leftContent {
    border:2px solid rgb(238, 238, 238);
    border-bottom: 4px solid #e9e9f0;
  }
    .footerAction {
      width: 100%;
      height: 48px;
      overflow: hidden;
      background: #FFFFFF;
     // border: 1px solid orange;
    }    
   /deep/ .ant-table{    

    .ant-table-thead > tr > th{
      padding: 7px 4px !important;
      border-right: 1px solid #efefef!important;
      border-color: #f0f0f0;
      background: #fafafa !important;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 4px!important;
      border-right: 1px solid #efefef!important;
      border-color: #f0f0f0;
    }
    .ant-table-tbody .ant-table-row:nth-child(2n) {
      background: #F8F8F8;
    }
    .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: #dfdcdc;
    }
    .ant-table-tbody > tr.ant-table-row-selected td {    
      background: #dfdcdc;
    }
   
    .ant-table-body{
      .ant-table-tbody{
        .rowBackgroundColor {
          background: #dfdcdc!important;
        }
      }
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  } 
  
}
</style>