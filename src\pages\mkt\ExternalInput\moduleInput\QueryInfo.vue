<template>
  <a-form-model
        :modal="form"
    >
      <a-form-model-item label="订单编号：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input v-model="form.OrderNo"  :autoFocus='autoFocus' v-focus-next-on-enter="'input2'" ref="input1"/>
      </a-form-model-item>
      <a-form-model-item label="客户料号" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input v-model="form.PcbFileName"  v-focus-next-on-enter="'input3'" ref="input2"/>
      </a-form-model-item>
     
      
    </a-form-model>
</template>

<script>
export default {
    name:'QueryInfo',
    
  data() {
    return {      
      autoFocus:true,
      form:{
        OrderNo:'',  // 订单编号
        PcbFileName: '',    // 客户料号
        Status: '',     // 状态
        CheckAdminId: '',     // 审单业务员
      }
    };
  },
  methods: {  
  //  keyupEnter1(){
  //     this.$emit('keyupEnter1')
  // }
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
};
</script>