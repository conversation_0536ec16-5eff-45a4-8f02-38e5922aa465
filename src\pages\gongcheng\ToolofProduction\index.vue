<!-- 工程管理 - 生产工具-->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend" style="position: relative">
      <div style="display: flex; width: 100%">
        <div class="leftContent">
          <left-tablemake
            ref="LeftTablemake"
            :columns="columns1"
            :isDrillPeiDao="this.user.isDrillPeiDao"
            :orderListTableLoading="orderListTableLoading"
            :dataSource="orderListData"
            :pagination="pagination"
            @TableChange="handleTableChange"
            @inputFileDownload="inputFileDownload"
            @FileDownload1="FileDownload1"
            @assignOrderListChange="assignOrderListChange"
            @getrightbotdata="getrightbotdata"
            :rowKey="'id'"
            :class="orderListData.length ? 'mintable' : ''"
          />
          <span slot="num" slot-scope="text, record, index">
            {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
          </span>
        </div>
        <div class="centerContent">
          <a-collapse :activeKey="0" @change="CollapseList" :expandIcon="expandIcon1">
            <a-collapse-panel key="1">
              <div class="centercollapse">
                <div class="topContent">
                  <a-table
                    :columns="columns3"
                    :dataSource="topData"
                    :loading="topListTableLoading"
                    :pagination="false"
                    :scroll="{ y: 325 }"
                    :rowKey="(text, record, index) => `${index + 1}`"
                  >
                  </a-table>
                </div>
                <center-tablemake
                  ref="CenterTablemake"
                  :columns="columns2"
                  :centerListTableLoading="centerListTableLoading"
                  :dataSource="centerData"
                  :pagination="false"
                  :rowKey="'userLoginID_'"
                  @getbotdata="getbotdata"
                  :class="centerData.length ? 'mintable1' : ''"
                />
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
        <div class="rightContent">
          <div class="rtContent">
            <a-table
              :dataSource="botData"
              :pagination="false"
              :loading="botloading"
              :columns="columns4"
              :scroll="{ y: 343, x: 336 }"
              rowKey="guid_"
              :class="botData.length ? 'mintable2' : ''"
            >
              <div slot="action" slot-scope="text, record" v-if="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolSendBack')">
                <a-tooltip title="回退订单">
                  <a-icon type="rollback" style="color: #ff9900; font-size: 18px" @click="backClick(record)" />
                </a-tooltip>
              </div>
              <div slot="orderNo_" slot-scope="text, record">
                <a style="color: black" :title="record.orderNo_">{{ record.orderNo_ }}</a
                >&nbsp;
                <a-tooltip title="加急" v-if="record.isJiaji">
                  <span style="color: #ff9900; padding: 0; margin-left: -10px; user-select: none">
                    <a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </a-tooltip>
                <a-tag v-if="record.isJunG" class="tagstyle"> {{ record.joinFactoryId == 70 ? "J" : "军" }} </a-tag>
                <a-tooltip title="" v-if="record.downloadFileDate">
                  <span style="color: #ff9900; padding: 0; user-select: none">
                    <a-icon type="file-text"></a-icon>
                  </span>
                </a-tooltip>
              </div>
            </a-table>
          </div>
          <div class="rbContent">
            <a-table
              :dataSource="rightbotData"
              :pagination="false"
              :loading="rbloading"
              :columns="columns5"
              :scroll="{ y: heighty }"
              rowKey="guid_"
              :class="rightbotData.length ? 'mintable3' : ''"
            >
            </a-table>
          </div>
        </div>
      </div>
      <div class="footerAction">
        <backend-action
          ref="action"
          @getOrderList="getOrderList"
          @Fileupload="Fileupload"
          :selectedRowsData="rowdata"
          @finishv3="finishv3"
          @pageload="pageload"
          @queryclick="queryclick"
          @Pickinguporders="Pickinguporders"
          @Productionfeedback="Productionfeedback"
          @Orderstart="Orderstart"
          @Ordercompletion="Ordercompletion"
          @delorderno="delorderno"
          @Dispatchorders="Dispatchorders"
          @Sendsteelmesh="Sendsteelmesh"
          @FileDownload="FileDownload"
        />
      </div>
      <div v-if="showreport">
        <report-info ref="reportinfo" :CardId="rowdata.joinFactoryId" :businessOrderNo="rowdata.orderNo_" :lay="1" :ttype="'scgj'" />
      </div>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="querydataVisible"
        @cancel="handleCancel"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        centered
        :width="400"
        class="queryclass"
      >
        <a-row>
          <a-col :span="24">
            <a-form-item label="生产编号" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="form.OrderNo" placeholder="请输入生产编号" :autoFocus="autoFocus" ref="input1" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-modal>
      <!-- 订单确认 -->
      <a-modal
        title="确认弹窗"
        :visible="ConfirmdataVisible"
        @cancel="handleCancel"
        @ok="ConfirmhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        centered
        :width="400"
      >
        <div v-if="ttype == '6'" style="color: red">标准靶距和实测靶距差值 > 0.1,请确认！</div>
        <div v-if="ttype == 1">
          <h3 style="font-weight: 500; color: #000000; font-size: 14px">确认将订单号为：</h3>
          <div class="tabBox">
            <a-tag color="orange" v-for="(item, index) in assignOrderList1" :key="index + '_assign'">
              {{ item }}
            </a-tag>
          </div>
          <div style="font-weight: 500; color: #000000">分派给：{{ personnel }}</div>
        </div>
        <div v-if="ttype == 2 || ttype == 4 || ttype == 5 || ttype == 7">【{{ ordermodel }}】{{ messagelist }}</div>
        <div v-if="ttype == 3 && $refs.LeftTablemake.selectedRowKeys.length == 1">【{{ ordermodel }}】{{ messagelist }}</div>
        <div v-else-if="ttype == 3 && $refs.LeftTablemake.selectedRowKeys.length != 1">确认所选订单批量完成吗?</div>
      </a-modal>
      <!-- 生产反馈 -->
      <a-modal
        title="反馈信息"
        :visible="ProductiondataVisible"
        @cancel="handleCancel"
        @ok="ProductionhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        centered
        :width="600"
        class="fank"
      >
        <a-row>
          <a-col :span="12">
            <a-form-item label="条形码" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="formdata.barCode" allowClear :autoFocus="true" @keyup.enter="getprodata('barCode')" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="生产编号" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input v-model="formdata.orderNo_" allowClear @keyup.enter="getprodata('orderNo_')" @blur="getprodata('orderNo_')" />
            </a-form-item>
          </a-col>
          <a-col :span="1">
            <a-upload
              name="file"
              ref="fileRef"
              :customRequest="httpRequest6"
              :file-list="fileList6"
              :show-upload-list="false"
              :maxCount="1"
              @change="handleChange6"
              :before-upload="beforeUpload"
            >
              <a-button style="padding: 0 6px"><a-icon type="upload" /></a-button>
            </a-upload>
          </a-col>
          <a-col :span="10">
            <a-form-item label="生产批号" :labelCol="{ span: 7 }" :wrapperCol="{ span: 17 }">
              <a-input v-model="formdata.taskNo" allowClear disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="反馈类型" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }" class="required">
              <a-select ref="select1" mode="multiple" v-model="formdata.type_" showSearch allowClear optionFilterProp="lable">
                <a-select-option v-for="(item, index) in list" :key="index" :value="item.value" :lable="item.text">{{ item.text }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="this.feedbackmode == 1 || !this.feedbackmode">
          <!-- @change="lashenchange()" -->
          <a-col :span="12">
            <a-form-item label="X拉伸值" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input placeholder="mil" v-model="formdata.xStretch" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Y拉伸值" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input placeholder="mil" v-model="formdata.yStretch" allowClear />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="this.feedbackmode == 2 || !this.feedbackmode">
          <!-- @change="xishuchange()" -->
          <a-col :span="12">
            <a-form-item label="X拉伸系数" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input placeholder="请输入万分之几" v-model="formdata.xStretchVaule" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Y拉伸系数" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input placeholder="请输入万分之几" v-model="formdata.yStretchVaule" allowClear />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="this.feedbackmode == 3 || !this.feedbackmode">
          <!-- @change="scchange()" -->
          <a-col :span="12">
            <a-form-item label="X设计靶距" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input v-model="formdata.xStandardRange" /><!--:disabled="disx"-->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Y设计靶距" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input v-model="formdata.yStandardRange" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="this.feedbackmode == 3 || !this.feedbackmode">
          <!-- @change="scchange()" -->
          <a-col :span="12">
            <a-form-item label="X实测靶距" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input v-model="formdata.xActualRange" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Y实测靶距" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }" class="required">
              <a-input v-model="formdata.yActualRange" allowClear />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <!-- @change="pnlchangex()" -->
          <a-col :span="12" v-if="!this.feedbackmode">
            <a-form-item label="PNL_X" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="formdata.pnl_X" :disabled="dis" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="!this.feedbackmode">
            <a-form-item label="PNL_Y" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-input v-model="formdata.pnl_Y" :disabled="dis" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="加急处理" :labelCol="{ span: 8 }" :wrapperCol="{ span: 14 }">
              <a-checkbox v-model="formdata.isJiaji" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="反馈备注" :labelCol="{ span: 4 }" :wrapperCol="{ span: 19 }">
              <a-textarea v-model="formdata.feedbackRemarks" allowClear :auto-size="{ minRows: 2, maxRows: 5 }" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import axios from "axios";
import ReportInfo from "@/pages/gongcheng/process/modules/ReportInfo";
import { mapState } from "vuex";
import { checkPermission } from "@/utils/abp";
import { uploadfile, uploadfilenew } from "@/services/ToolofProduction";
import {
  protoollist,
  protooluserlist,
  userorderlist,
  protoolorder,
  backprotoolsend,
  protooldrilltoollist,
  protoolfeedbackmode,
  protoolorderfinishv3,
  protoolsendemail,
  protoolsendorder,
  protoolorderstart,
  protoolorderfinished,
  protoolfeedback,
  protooltotalorders,
  protooldelorder,
  finishedduoxuan,
  protoolpnl,
  feedbackpar2Protool,
  stretchdatapath,
} from "@/services/ToolofProduction";
import BackendAction from "@/pages/gongcheng/ToolofProduction/subassembly/BackendAction";
import LeftTablemake from "@/pages/gongcheng/ToolofProduction/subassembly/LeftTablemake";
import CenterTablemake from "@/pages/gongcheng/ToolofProduction/subassembly/CenterTablemake";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    fixed: "left",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "生产编号",
    scopedSlots: { customRender: "orderNo_" },
    align: "left",
    fixed: "left",
    ellipsis: true,
    width: 120,
    className: "userStyle",
  },
  {
    title: "类型",
    scopedSlots: { customRender: "type_" },
    align: "left",
    ellipsis: true,
    width: 90,
  },
  {
    title: "工厂",
    dataIndex: "factoryName",
    customRender: (text, record, index) => `${record.factoryName.includes("雅信达") ? record.factoryName.split("雅信达")[0] : record.factoryName}`,
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "厚铜",
    scopedSlots: { customRender: "ThickCu" },
    align: "center",
    ellipsis: true,
    width: 45,
  },
  {
    title: "订单类型",
    dataIndex: "reOrderType",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "状态",
    dataIndex: "status_",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  // {
  //   title: "面积",
  //   dataIndex: "boardArea",
  //   align: "left",
  //   ellipsis: true,
  //   width: 70,
  // },
  {
    title: "制作人",
    dataIndex: "makeName",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "开始时间",
    dataIndex: "startDate_",
    width: 160,
    ellipsis: true,
    align: "left",
  },
  {
    title: "输入",
    dataIndex: "inputFile_",
    width: 60,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "inputFile_" },
  },
  {
    title: "完成",
    dataIndex: "fileName",
    width: 60,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "fileName" },
  },
  // {
  //   title: "PNL数",
  //   dataIndex: "panels",
  //   width: 60,
  //   ellipsis: true,
  //   align: "center",
  // },
  {
    title: "交期",
    dataIndex: "deliveryDate",
    align: "center",
    ellipsis: true,
    width: 120,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 140,
  },
  {
    title: "合拼编号",
    dataIndex: "combinationModel",
    width: 140,
    ellipsis: true,
    align: "left",
  },
  {
    title: "完成时间",
    dataIndex: "finishDate_",
    align: "left",
    ellipsis: true,
    width: 140,
  },

  // {
  //   title: "叠板数",
  //   dataIndex: "dieBanNum",
  //   align: "left",
  //   ellipsis: true,
  //   width: 94,
  // },
  // {
  //   title: "几头机",
  //   dataIndex: "drillingMachineNum",
  //   align: "left",
  //   ellipsis: true,
  //   width: 80,
  // },
  {
    title: "错误信息",
    dataIndex: "errMsg_",
    align: "left",
    ellipsis: true,
    width: 180,
  },

  // {
  //   title: "反馈备注",
  //   dataIndex: "feedbackRemarks",
  //   align: "left",
  //   ellipsis: true,
  //   width: 140,
  // },

  // {
  //   title: "X标准靶距",
  //   dataIndex: "xStandardRange",
  //   width: 90,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Y标准靶距",
  //   dataIndex: "yStandardRange",
  //   width: 90,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "X实测靶距",
  //   dataIndex: "xActualRange",
  //   width: 90,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Y实测靶距",
  //   dataIndex: "yActualRange",
  //   width: 90,
  //   ellipsis: true,
  //   align: "left",
  // },
  {
    title: "面积",
    dataIndex: "boardArea",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "版本",
    dataIndex: "proRev",
    className: "userStyle",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  // {
  //   title: "操作",
  //   scopedSlots: { customRender: "action" },
  //   align: "center",
  //   ellipsis: true,
  //   width: 50,
  // },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "姓名",
    dataIndex: "realName",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "停留",
    dataIndex: "count_TL",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "完成",
    dataIndex: "count_OK",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "工厂",
    dataIndex: "facName",
    align: "left",
    ellipsis: true,
    width: 70,
    className: "userStyle",
  },
];
const columns3 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "待分派",
    dataIndex: "waitSendCount",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "待制作",
    dataIndex: "waitMakeCount",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "制作中",
    dataIndex: "makeCount",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
  {
    title: "制作完",
    dataIndex: "nowadayCount",
    align: "left",
    ellipsis: true,
    width: 60,
    className: "userStyle",
  },
];
const columns4 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "生产编号",
    dataIndex: "orderNo_",
    align: "left",
    ellipsis: true,
    width: 110,
    className: "userStyle",
    scopedSlots: { customRender: "orderNo_" },
  },
  {
    title: "类型",
    dataIndex: "type",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "状态",
    dataIndex: "state_",
    align: "left",
    ellipsis: true,
    width: 70,
    className: "userStyle",
  },
  {
    title: "工厂",
    dataIndex: "facName",
    align: "left",
    ellipsis: true,
    width: 80,
    className: "userStyle",
  },
  {
    title: "派单时间",
    dataIndex: "dispatchDate",
    align: "left",
    ellipsis: true,
    width: 120,
    className: "userStyle",
  },
  {
    title: "完成时间",
    dataIndex: "endDate",
    align: "left",
    ellipsis: true,
    width: 120,
  },
  {
    title: "操作",
    align: "center",
    ellipsis: true,
    width: 50,
    className: "userStyle",
    scopedSlots: { customRender: "action" },
  },
];
const columns5 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "刀号",
    dataIndex: "toolno",
    align: "left",
    ellipsis: true,
    width: 40,
    className: "userStyle",
  },
  {
    title: "孔径",
    dataIndex: "tooldia",
    align: "left",
    ellipsis: true,
    width: 45,
    className: "userStyle",
  },
  {
    title: "数量",
    dataIndex: "qty",
    align: "left",
    ellipsis: true,
    width: 50,
    className: "userStyle",
  },
  {
    title: "备注",
    dataIndex: "captions",
    align: "left",
    ellipsis: true,
    width: 65,
    className: "userStyle",
  },
  {
    title: "钻咀数",
    dataIndex: "drlnum",
    align: "left",
    ellipsis: true,
    width: 50,
    className: "userStyle",
  },
];
export default {
  components: { BackendAction, LeftTablemake, CenterTablemake, ReportInfo },
  data() {
    return {
      rowdata: {},
      showreport: false,
      filedata: null,
      ordertype: "",
      dis: false,
      disx: false,
      spinning: false,
      oldval: "",
      heighty: 737,
      formdata: {
        orderNo_: "",
        type_: "",
        xStandardRange: "",
        yStandardRange: "",
        xActualRange: "",
        yActualRange: "",
        isJiaji: false,
        barCode: "",
        taskNo: "",
        feedbackRemarks: "",
        FileName: "",
        UploadFile: "",
        pnl_X: "",
        pnl_Y: "",
        xStretchVaule: "",
        yStretchVaule: "",
      },
      list: [
        { value: "3", text: "钻孔拉伸" },
        { value: "6", text: "锣带拉伸" },
        { value: "20", text: "线路拉伸" },
        { value: "21", text: "阻焊拉伸" },
        { value: "22", text: "文字拉伸" },
        { value: "23", text: "全套拉伸" }, //全套拉伸不做锣带拉伸
      ],
      columns1,
      columns2,
      columns3,
      columns4,
      columns5,
      rightbotData: [],
      orderListData: [],
      centerData: [],
      botData: [],
      feedbackmode: 0,
      topData: [],
      ConfirmdataVisible: false,
      pagination: {
        pageSize: 20,
        pageIndex: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      querydataVisible: false,
      form: {
        OrderNo: "",
      },
      autoFocus: true,
      fileList6: [],
      params1: {},
      ttype: "",
      ordermodel: "",
      messagelist: "",
      personnel: "",
      ids: "",
      assignOrderList1: [],
      assignOrderList: [],
      userLoginID_: "",
      UserNo_: "",
      orderListTableLoading: false,
      centerListTableLoading: false,
      topListTableLoading: false,
      rbloading: false,
      botloading: false,
      ProductiondataVisible: false,
      foldedornot: true,
      isCtrlPressed: false,
      params: {},
    };
  },
  created() {
    this.getOrderList();
    this.getcenter();
    this.gettop();
    document.addEventListener("copy", this.handleCopy);
    console.log(this.user.isZhangSuo, this.user.isDrillPeiDao, "840");
    if (this.user.isZhangSuo && this.user.isDrillPeiDao && columns1[5].dataIndex != "typeStr") {
      //用户信息勾选临时拉伸与钻咀配刀
      columns1.splice(
        5,
        0,
        { title: "类型明细", dataIndex: "typeStr", width: 90, ellipsis: true, align: "center" },
        { title: "X标准靶距", dataIndex: "xStandardRange", width: 90, ellipsis: true, align: "left" },
        { title: "Y标准靶距", dataIndex: "yStandardRange", width: 90, ellipsis: true, align: "left" },
        { title: "X实测靶距", dataIndex: "xActualRange", width: 90, ellipsis: true, align: "left" },
        { title: "Y实测靶距", dataIndex: "yActualRange", width: 90, ellipsis: true, align: "left" },
        { title: "X", dataIndex: "xStretchVaule", width: 80, ellipsis: true, align: "left" },
        { title: "Y", dataIndex: "yStretchVaule", width: 80, ellipsis: true, align: "left" }
      );
      columns1.splice(17, 0, { title: "PNL数", dataIndex: "panels", width: 60, ellipsis: true, align: "center" });
      columns1.splice(
        22,
        0,
        { title: "叠板数", dataIndex: "dieBanNum", align: "left", ellipsis: true, width: 94 },
        { title: "几头机", dataIndex: "drillingMachineNum", align: "left", ellipsis: true, width: 80 }
      );
      columns1.splice(
        25,
        0,
        { title: "反馈备注", dataIndex: "feedbackRemarks", align: "left", ellipsis: true, width: 140 },
        { title: "标记位置", dataIndex: "markPositionStr", align: "left", ellipsis: true, width: 100 },
        { title: "周期格式", dataIndex: "periodicFormatStr", align: "left", ellipsis: true, width: 100 }
      );
    } else if (this.user.isZhangSuo && !this.user.isDrillPeiDao && columns1[5].dataIndex != "typeStr") {
      //临时拉伸
      columns1.splice(
        5,
        0,
        { title: "类型明细", dataIndex: "typeStr", width: 90, ellipsis: true, align: "center" },
        { title: "X标准靶距", dataIndex: "xStandardRange", width: 90, ellipsis: true, align: "left" },
        { title: "Y标准靶距", dataIndex: "yStandardRange", width: 90, ellipsis: true, align: "left" },
        { title: "X实测靶距", dataIndex: "xActualRange", width: 90, ellipsis: true, align: "left" },
        { title: "Y实测靶距", dataIndex: "yActualRange", width: 90, ellipsis: true, align: "left" },
        { title: "X", dataIndex: "xStretchVaule", width: 80, ellipsis: true, align: "left" },
        { title: "Y", dataIndex: "yStretchVaule", width: 80, ellipsis: true, align: "left" },
        {
          title: "完成",
          dataIndex: "fileName",
          width: 60,
          ellipsis: true,
          align: "center",
          scopedSlots: { customRender: "fileName" },
        },
        { title: "反馈备注", dataIndex: "feedbackRemarks", align: "left", ellipsis: true, width: 140 }
      );
      columns1.splice(19, 1);
      columns1.splice(
        22,
        0,
        { title: "标记位置", dataIndex: "markPositionStr", align: "left", ellipsis: true, width: 100 },
        { title: "周期格式", dataIndex: "periodicFormatStr", align: "left", ellipsis: true, width: 100 }
      );
    } else if (!this.user.isZhangSuo && this.user.isDrillPeiDao && columns1[11].dataIndex != "panels") {
      //钻咀配刀
      columns1.splice(11, 0, { title: "PNL数", dataIndex: "panels", width: 60, ellipsis: true, align: "center" });
      columns1.splice(
        16,
        0,
        { title: "叠板数", dataIndex: "dieBanNum", align: "left", ellipsis: true, width: 94 },
        { title: "几头机", dataIndex: "drillingMachineNum", align: "left", ellipsis: true, width: 80 }
      );
    }
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  mounted() {
    const elements1 = document.getElementsByClassName("centercollapse");
    const elements2 = document.getElementsByClassName("leftContent");
    const elements3 = document.getElementsByClassName("rtContent");
    this.foldedornot = false;
    for (let index = 0; index < elements1.length; index++) {
      elements1[index].style.display = "none";
      elements1[index].style.width = "";
    }
    for (let index = 0; index < elements2.length; index++) {
      elements2[index].style.width = "80%";
      elements2[index].style.display = "";
    }
    for (let index = 0; index < elements3.length; index++) {
      elements3[index].style.width = " ";
      elements3[index].style.display = "none";
    }
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  methods: {
    checkPermission,
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.handleCancel();
        this.queryclick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.querydataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.ConfirmdataVisible) {
        this.ConfirmhandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    getprodata(type) {
      if (!this.formdata[type]) {
        this.$message.error("无法获取相关数据");
        return;
      }
      let orderNo = this.formdata.orderNo_ || "";
      let barCode = this.formdata.barCode || "";
      feedbackpar2Protool(orderNo, barCode).then(res => {
        if (res.code) {
          this.$set(this.formdata, "pnl_X", res.data.pnL_LEN);
          this.$set(this.formdata, "pnl_Y", res.data.pnL_WTH);
          this.$set(this.formdata, "xStandardRange", res.data.targetRangeX);
          this.$set(this.formdata, "yStandardRange", res.data.targetRangeY);
          this.$set(this.formdata, "taskNo", res.data.taskno);
          if (type == "barCode") {
            this.$set(this.formdata, "orderNo_", res.data.orderNo);
          }
          if (this.formdata.pnl_X && this.formdata.pnl_Y) {
            this.dis = true;
          } else {
            this.dis = false;
          }
          if (this.formdata.xStandardRange && this.formdata.yStandardRange) {
            this.disx = true;
          } else {
            this.disx = false;
          }
        } else {
          this.$message.error("未获取到相关数据");
          this.dis = false;
          this.disx = false;
        }
      });
    },
    lashenchange() {
      if (this.formdata.pnl_X && this.formdata.xStretch) {
        let xStretchVaule = (
          ((Number(this.formdata.xStretch) + (Number(this.formdata.pnl_X) / 25.4) * 1000) / ((this.formdata.pnl_X / 25.4) * 1000) - 1) *
          10000
        ).toFixed(2);
        this.$set(this.formdata, "xStretchVaule", xStretchVaule);
      } else {
        this.$set(this.formdata, "xStretchVaule", "");
      }
      if (this.formdata.pnl_Y && this.formdata.yStretch) {
        let yStretchVaule = (
          ((Number(this.formdata.yStretch) + (Number(this.formdata.pnl_Y) / 25.4) * 1000) / ((this.formdata.pnl_Y / 25.4) * 1000) - 1) *
          10000
        ).toFixed(2);
        this.$set(this.formdata, "yStretchVaule", yStretchVaule);
      } else {
        this.$set(this.formdata, "yStretchVaule", "");
      }
    },
    xishuchange() {
      if (this.formdata.pnl_X && this.formdata.xStretchVaule) {
        let xStretch = ((Number(this.formdata.pnl_X) / 25.4) * 1000 * (Number(this.formdata.xStretchVaule) / 10000)).toFixed(2);
        this.$set(this.formdata, "xStretch", xStretch);
      } else {
        this.$set(this.formdata, "xStretch", "");
      }
      if (this.formdata.pnl_Y && this.formdata.yStretchVaule) {
        let yStretch = ((Number(this.formdata.pnl_Y) / 25.4) * 1000 * (Number(this.formdata.yStretchVaule) / 10000)).toFixed(2);
        this.$set(this.formdata, "yStretch", yStretch);
      } else {
        this.$set(this.formdata, "yStretch", "");
      }
    },
    pnlchangex() {
      if (this.user.factoryId == 12 || this.user.factoryId == 22 || this.user.factoryId == 58 || this.user.factoryId == 59) {
        if (this.formdata.pnl_X && this.formdata.xStretchVaule) {
          let xStretch = ((Number(this.formdata.pnl_X) / 25.4) * 1000 * (Number(this.formdata.xStretchVaule) / 10000)).toFixed(2);
          this.$set(this.formdata, "xStretch", xStretch);
        }
      } else {
        if (this.formdata.pnl_X && this.formdata.xStretch) {
          let xStretchVaule = (
            ((Number(this.formdata.xStretch) + (Number(this.formdata.pnl_X) / 25.4) * 1000) / ((this.formdata.pnl_X / 25.4) * 1000) - 1) *
            10000
          ).toFixed(2);
          this.$set(this.formdata, "xStretchVaule", xStretchVaule);
        }
      }
    },
    pnlchangey() {
      if (this.user.factoryId == 12 || this.user.factoryId == 22 || this.user.factoryId == 58 || this.user.factoryId == 59) {
        if (this.formdata.pnl_Y && this.formdata.yStretchVaule) {
          let yStretch = ((Number(this.formdata.pnl_Y) / 25.4) * 1000 * (Number(this.formdata.yStretchVaule) / 10000)).toFixed(2);
          this.$set(this.formdata, "yStretch", yStretch);
        }
      } else {
        if (this.formdata.pnl_Y && this.formdata.yStretch) {
          let yStretchVaule = (
            ((Number(this.formdata.yStretch) + (Number(this.formdata.pnl_Y) / 25.4) * 1000) / ((this.formdata.pnl_Y / 25.4) * 1000) - 1) *
            10000
          ).toFixed(2);
          this.$set(this.formdata, "yStretchVaule", yStretchVaule);
        }
      }
    },
    scchange() {
      if (this.formdata.xActualRange && this.formdata.xStandardRange) {
        let xStretchVaule = ((Number(this.formdata.xActualRange - this.formdata.xStandardRange) / this.formdata.xStandardRange) * 10000).toFixed(2);
        let xStretch = (Number(this.formdata.xActualRange - this.formdata.xStandardRange) / 0.0254).toFixed(2);
        this.$set(this.formdata, "xStretch", xStretch);
        this.$set(this.formdata, "xStretchVaule", xStretchVaule);
      } else {
        this.$set(this.formdata, "xStretchVaule", "");
        this.$set(this.formdata, "xStretch", "");
      }
      if (this.formdata.yActualRange && this.formdata.yStandardRange) {
        let yStretchVaule = ((Number(this.formdata.yActualRange - this.formdata.yStandardRange) / this.formdata.yStandardRange) * 10000).toFixed(2);
        let yStretch = (Number(this.formdata.yActualRange - this.formdata.yStandardRange) / 0.0254).toFixed(2);
        this.$set(this.formdata, "yStretch", yStretch);
        this.$set(this.formdata, "yStretchVaule", yStretchVaule);
      } else {
        this.$set(this.formdata, "yStretchVaule", "");
        this.$set(this.formdata, "yStretch", "");
      }
    },
    handleChange6({ fileList }) {
      this.fileList6 = fileList;
      if (fileList.length > 1) {
        this.$message.warning("该操作只能上传一个文件 默认按照最新文件上传");
        this.fileList6.splice(0, 1);
      }
    },
    beforeUpload(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const filesize = Number(file.size / 1024 / 1024) < 500;
        if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    beforeUpload3(file) {
      this.isFileType =
        file.name.toLowerCase().indexOf(".zip") != -1 ||
        file.name.toLowerCase().indexOf(".rar") != -1 ||
        file.name.toLowerCase().indexOf(".tgz") != -1;
      if (!this.isFileType) {
        this.$message.error("该功能只支持.zip.rar.tgz");
      }
      return this.isFileType;
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    async httpRequest6(data, type) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/pro-tool/up-load-file-new-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              const [uploadFile, fileName] = res.data.split(",");
              this.$set(this.formdata, "orderNo_", fileName);
              this.$set(this.formdata, "UploadFile", uploadFile);
              this.getprodata("orderNo_");
            }
          } else {
            this.$message.error(res.message);
            i = shardCount;
          }
        });
      }
    },
    handleCopy(event) {
      const selectedText = window.getSelection().toString().trim();
      const processedText = selectedText.replace(/[\s⚡]/g, ""); // 去掉所有空格和标识
      event.clipboardData.setData("text/plain", processedText);
      event.preventDefault();
    },
    expandIcon1() {
      if (this.foldedornot) {
        return (
          <a>
            <a-icon type="right" style="margin-right:5px" />
          </a>
        );
      } else {
        return (
          <a>
            <a-icon type="left" style="margin-right:5px" />
          </a>
        );
      }
    },
    CollapseList(val) {
      const elements1 = document.getElementsByClassName("centercollapse");
      const elements2 = document.getElementsByClassName("leftContent");
      const elements3 = document.getElementsByClassName("rtContent");
      if (val.length) {
        this.foldedornot = true;
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.width = "100%";
          elements1[index].style.display = "";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.width = "60%";
        }
        for (let index = 0; index < elements3.length; index++) {
          elements3[index].style.width = "100%";
          elements3[index].style.display = "";
          this.heighty = 326;
        }
      } else {
        this.foldedornot = false;
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.display = "none";
          elements1[index].style.width = "";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.width = "80%";
          elements2[index].style.display = "";
        }
        for (let index = 0; index < elements3.length; index++) {
          elements3[index].style.width = " ";
          elements3[index].style.display = "none";
          this.heighty = 737;
        }
      }
    },
    handleTableChange(pagination) {
      this.pagination.pageIndex = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      if (JSON.stringify(this.form) != "{}") {
        this.getOrderList(this.form);
      } else {
        this.getOrderList();
      }
    },
    getrightbotdata(selectedRowsData) {
      this.rbloading = true;
      if (selectedRowsData.type_ == "13") {
        protooldrilltoollist(selectedRowsData.orderNo_, 13, selectedRowsData.joinFactoryId)
          .then(res => {
            if (res.code) {
              this.rightbotData = res.data;
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.rbloading = false;
          });
      } else {
        this.rightbotData = [];
        this.rbloading = false;
      }
    },
    assignOrderListChange(payload) {
      var arr = [];
      var arr1 = [];
      for (var i = 0; i < payload.length; i++) {
        arr.push(payload[i].orderNo_);
        arr1.push(payload[i].id);
      }
      this.assignOrderList1 = arr;
      this.assignOrderList = arr1;
    },
    getOrderList(querydata) {
      this.orderListTableLoading = true;
      let params = {
        PageIndex: this.pagination.pageIndex,
        PageSize: this.pagination.pageSize,
      };
      var obj = Object.assign(params, querydata);
      protoollist(obj)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            this.pagination.total = res.data.totalCount;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    getcenter() {
      this.centerListTableLoading = true;
      protooluserlist()
        .then(res => {
          if (res.code) {
            this.centerData = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.centerListTableLoading = false;
        });
    },
    gettop() {
      this.topListTableLoading = true;
      protooltotalorders()
        .then(res => {
          if (res) {
            this.topData = res;
          }
        })
        .finally(() => {
          this.topListTableLoading = false;
        });
    },
    getbotdata(erpid) {
      this.botloading = true;
      userorderlist(erpid)
        .then(res => {
          if (res.code) {
            this.botData = res.data;
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < this.botData.length; i++) {
              let obj2 = this.botData[i].orderNo_;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            if (longestChars.length > 12) {
              this.columns4[1].width = 115 + (longestChars.length - 12) * 8 + "px";
            } else {
              this.columns4[1].width = "115px";
              this.columns4[2].width = "80px";
              this.columns4[3].width = "70px";
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.botloading = false;
        });
    },
    //生产反馈
    Productionfeedback() {
      this.formdata = {};
      this.fileList6 = [];
      protoolfeedbackmode().then(res => {
        if (res.code) {
          this.feedbackmode = res.data;
          this.ProductiondataVisible = true;
        }
      });
    },
    //取单
    Pickinguporders() {
      protoolorder().then(res => {
        if (res.code) {
          this.$message.success("取单成功");
          this.getOrderList();
          this.getcenter();
          this.gettop();
          if (this.$refs.CenterTablemake.erpid) {
            this.getbotdata(this.$refs.CenterTablemake.erpid);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //订单分派
    Dispatchorders() {
      if (this.$refs.LeftTablemake.selectedRowsData.length == 0 && !this.$refs.CenterTablemake.personnel) {
        this.$message.warning("请选择分派订单及人员");
        return;
      }
      if (this.$refs.LeftTablemake.selectedRowsData.length == 0) {
        this.$message.warning("请选择分派订单");
        return;
      }
      if (!this.$refs.CenterTablemake.personnel) {
        this.$message.warning("请选择分派人员");
        return;
      }
      this.ttype = "1";
      this.personnel = this.$refs.CenterTablemake.personnel;
      this.ConfirmdataVisible = true;
    },
    //列表文件下载
    FileDownload1(record) {
      if (record.exportFile_) {
        const urlObj = new URL(record.exportFile_);
        const path = urlObj.pathname;
        const fileName = path.substring(path.lastIndexOf("/") + 1);
        const fileNameWithoutQuery = decodeURIComponent(fileName.split("?")[0]);
        const xhr = new XMLHttpRequest();
        xhr.open("GET", record.exportFile_, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = fileNameWithoutQuery;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
        if ((record.type_ == "4" || record.type_ == "5") && this.user.factoryId == 12) {
          this.$refs.action.files = [];
          this.$refs.action.clickUpload1("1");
        } else {
          this.finish([record.id], 1);
        }
      }
    },
    //文件下载
    inputFileDownload(record) {
      const ordermodel = record.orderNo_.toLowerCase().replace(/([a-z])([A-Z])/g, "$1-$2");
      this.spinning = true;
      let fileType = ".tgz";
      stretchdatapath(record.id)
        .then(res => {
          if (res.code) {
            if (res.data) {
              if (record.factoryName.includes("雅信达") && res.data.includes("百能文件中转")) {
                window.location.href = res.data;
              } else {
                const xhr = new XMLHttpRequest();
                xhr.open("GET", res.data, true);
                xhr.responseType = "blob";
                xhr.onload = function () {
                  if (xhr.status === 200) {
                    const blob = xhr.response;
                    const link = document.createElement("a");
                    link.href = window.URL.createObjectURL(blob);
                    link.download = ordermodel + fileType;
                    link.style.display = "none";
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                };
                xhr.send();
              }
            } else {
              this.$message.error("当前暂无输入文件下载");
            }
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    FileDownload() {
      var ordermodel1 = this.$refs.LeftTablemake.selectedRowsData.orderNo_;
      if (this.$refs.LeftTablemake.selectedRowsData.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.LeftTablemake.selectedRowsData.length > 1) {
        this.$message.warning("该操作只能选择一条订单");
        return;
      }
      if (!this.$refs.LeftTablemake.selectedRowsData.inputFile_) {
        this.$message.error("当前订单没有制作完成文件");
        return;
      }
      let inputFile_ = this.$refs.LeftTablemake.selectedRowsData.inputFile_;
      let a = inputFile_.split(".").slice(-1)[0];
      if (inputFile_) {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", inputFile_, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = ordermodel1 + "." + a;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      }
    },
    //文件上传
    Fileupload() {
      if (!this.$refs.LeftTablemake.selectedRowKeys.length) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.LeftTablemake.selectedRowKeys.length > 1) {
        this.$message.warning("该操作只能选择一条订单");
        return;
      }
      this.$refs.action.clickUpload(this.$refs.LeftTablemake.selectedRowsData.id);
    },
    //订单开始
    Orderstart() {
      if (this.$refs.LeftTablemake.selectedRowKeys.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.LeftTablemake.selectedRowKeys.length > 1) {
        this.$message.warning("该操作只能选择一条订单");
        return;
      }
      this.ttype = "2";
      this.messagelist = "确认订单开始吗？";
      this.ConfirmdataVisible = true;
      this.ordermodel = this.$refs.LeftTablemake.selectedRowsData.orderNo_;
    },
    //订单完成
    Ordercompletion() {
      if (this.$refs.LeftTablemake.selectedRowKeys.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      this.$refs.action.files = [];
      let val = this.$refs.LeftTablemake.slectedRows.filter(item => item.type_ == "4" || item.type_ == "5");
      if (val.length > 0 && this.$refs.LeftTablemake.selectedRowKeys.length > 1 && this.user.factoryId == 12) {
        this.$message.warning("订单类型为锣带自动或飞针自动只能选择一个订单进行完成");
        return;
      }
      if (val.length && this.user.factoryId == 12) {
        this.ordertype = "et&route";
      } else {
        this.ordertype = "";
      }
      this.ttype = "3";
      this.messagelist = "确认订单完成吗？";
      this.ConfirmdataVisible = true;
      this.ordermodel = this.$refs.LeftTablemake.selectedRowsData.orderNo_;
    },
    //分派回退
    backClick(record) {
      this.ttype = "4";
      this.messagelist = "确认订单回退吗？";
      this.ConfirmdataVisible = true;
      this.ordermodel = record.orderNo_;
      this.ids = record.guid_;
    },
    Sendsteelmesh() {
      if (this.$refs.LeftTablemake.selectedRowKeys.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.LeftTablemake.selectedRowKeys.length > 1) {
        this.$message.warning("该操作只能选择一条订单");
        return;
      }
      this.rowdata = this.$refs.LeftTablemake.selectedRowsData;
      if (this.rowdata.productDataUp && this.rowdata.productDataUp.indexOf("1") != -1) {
        this.showreport = true;
      } else {
        this.showreport = false;
      }
      this.ttype = "7";
      this.messagelist = "确认该订单发送钢网邮件吗?";
      this.ConfirmdataVisible = true;
      this.ordermodel = this.$refs.LeftTablemake.selectedRowsData.orderNo_;
    },
    delorderno() {
      if (this.$refs.LeftTablemake.selectedRowKeys.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.LeftTablemake.selectedRowKeys.length > 1) {
        this.$message.warning("该操作只能选择一条订单");
        return;
      }
      this.ttype = "5";
      this.messagelist = "确认删除该订单吗？";
      this.ConfirmdataVisible = true;
      this.ordermodel = this.$refs.LeftTablemake.selectedRowsData.orderNo_;
    },
    handleCancel() {
      this.querydataVisible = false;
      this.ConfirmdataVisible = false;
      this.showreport = false;
      this.orderListTableLoading = false;
      if (this.ttype != "6") {
        this.ProductiondataVisible = false;
      }
      this.ttype = "";
    },
    handleOk() {
      let params = this.form;
      var arr1 = params.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      this.pagination.pageIndex = 1;
      params.OrderNo = arr1.join("");
      if (params.OrderNo && typeof params.OrderNo === "string" && params.OrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产编号查询不能小于5位");
        return;
      }
      this.querydataVisible = false;
      this.getOrderList(params);
    },
    ProductionhandleOk() {
      let params = this.formdata;
      let r = /^-?\d+(\.\d+)?$/;
      if (!params.orderNo_) {
        this.$message.warning("请输入生产编号");
        return;
      }
      if (params.type_.length == 0) {
        this.$message.warning("请选择反馈类型");
        return;
      }
      if (params.xStretchVaule < -15 || params.xStretchVaule > 15 || (!params.xStretchVaule && this.feedbackmode == 2)) {
        this.$message.warning("请输入x拉伸系数,输入区间为±15");
        return;
      }
      if (params.yStretchVaule < -15 || params.yStretchVaule > 15 || (!params.yStretchVaule && this.feedbackmode == 2)) {
        this.$message.warning("请输入y拉伸系数,输入区间为±15");
        return;
      }
      if (!params.xStretch && this.feedbackmode == 1) {
        this.$message.warning("请输入X拉伸值");
        return;
      }
      if (!params.yStretch && this.feedbackmode == 1) {
        this.$message.warning("请输入Y拉伸值");
        return;
      }
      if (!params.xStandardRange && this.feedbackmode == 3) {
        this.$message.warning("请输入X设计靶距");
        return;
      }
      if (!params.yStandardRange && this.feedbackmode == 3) {
        this.$message.warning("请输入Y设计靶距");
        return;
      }
      if (!params.xActualRange && this.feedbackmode == 3) {
        this.$message.warning("请输入X实测靶距");
        return;
      }
      if (!params.yActualRange && this.feedbackmode == 3) {
        this.$message.warning("请输入Y实测靶距");
        return;
      }
      params.FeedbackMode = this.feedbackmode ? this.feedbackmode : null;
      if (!params.pnl_X) {
        params.pnl_X = null;
      }
      if (!params.pnl_Y) {
        params.pnl_Y = null;
      }
      if (!params.xStretch) {
        params.xStretch = null;
      }
      if (!params.xStretchVaule) {
        params.xStretchVaule = null;
      }
      if (!params.yStretch) {
        params.yStretch = null;
      }
      if (!params.yStretchVaule) {
        params.yStretchVaule = null;
      }
      this.params = params;

      this.orderListTableLoading = true;
      let differenceX = Math.abs(Number(params.xStandardRange) - Number(params.xActualRange)).toFixed(2);
      let differenceY = Math.abs(Number(params.yStandardRange) - Number(params.yActualRange)).toFixed(2);
      if (differenceX > 0.1 || differenceY > 0.1) {
        this.ttype = "6";
        this.ConfirmdataVisible = true;
      } else {
        this.ProductiondataVisible = false;
        this.protoolfeedback();
      }
    },
    ConfirmhandleOk() {
      this.ConfirmdataVisible = false;
      if (this.ttype == "1") {
        let params = {
          guids: this.assignOrderList,
          userLoginID_: this.$refs.CenterTablemake.selectedRowsData.userNo,
        };
        protoolsendorder(params).then(res => {
          if (res.code) {
            this.$message.success("分派成功");
            this.getOrderList();
            this.gettop();
            this.$refs.LeftTablemake.selectedRowsData = [];
            this.$refs.LeftTablemake.selectedRowKeys = [];
            this.$refs.LeftTablemake.slectedRows = [];
            this.getcenter();
            if (this.$refs.CenterTablemake.erpid) {
              this.getbotdata(this.$refs.CenterTablemake.erpid);
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "2") {
        protoolorderstart(this.$refs.LeftTablemake.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success("开始成功");
            if (this.$refs.LeftTablemake.selectedRowsData.type_ == "13") {
              this.$refs.LeftTablemake.drillbit(this.$refs.LeftTablemake.selectedRowsData);
            }
            this.getOrderList();
            this.gettop();
            this.getcenter();
            if (this.$refs.CenterTablemake.erpid) {
              this.getbotdata(this.$refs.CenterTablemake.erpid);
            }
            this.$refs.LeftTablemake.selectedRowsData = [];
            this.$refs.LeftTablemake.selectedRowKeys = [];
            this.$refs.LeftTablemake.slectedRows = [];
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "3") {
        this.$refs.action.filecount = 0;
        this.rowdata = this.$refs.LeftTablemake.selectedRowsData;
        if (this.ordertype == "et&route" && this.user.factoryId == 12) {
          this.$refs.action.files = [];
          this.$refs.action.clickUpload1("0");
        } else {
          this.finish(this.assignOrderList, 0);
        }
      }
      if (this.ttype == "4") {
        backprotoolsend(this.ids).then(res => {
          if (res.code) {
            this.$message.success("回退成功");
            this.gettop();
            this.getOrderList();
            this.$refs.LeftTablemake.selectedRowsData = [];
            this.$refs.LeftTablemake.selectedRowKeys = [];
            this.$refs.LeftTablemake.slectedRows = [];
            this.getcenter();
            if (this.$refs.CenterTablemake.erpid) {
              this.getbotdata(this.$refs.CenterTablemake.erpid);
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "5") {
        protooldelorder(this.$refs.LeftTablemake.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success("删除订单成功");
            this.getOrderList();
            this.getcenter();
            this.gettop();
            if (this.$refs.CenterTablemake.erpid) {
              this.getbotdata(this.$refs.CenterTablemake.erpid);
            }
            this.getrightbotdata();
            this.$refs.LeftTablemake.selectedRowsData = [];
            this.$refs.LeftTablemake.selectedRowKeys = [];
            this.$refs.LeftTablemake.slectedRows = [];
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "6") {
        this.ProductiondataVisible = false;
        this.protoolfeedback();
      }
      if (this.ttype == "7") {
        this.spinning = true;
        if (this.showreport) {
          setTimeout(() => {
            protoolsendemail(this.$refs.LeftTablemake.selectedRowsData.id, this.$refs.reportinfo.formData)
              .then(res => {
                if (res.code) {
                  this.$message.success(res.message);
                  this.datarolad();
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.$refs.reportinfo.formData = null;
                this.showreport = false;
                this.spinning = false;
              });
          }, 1000);
        } else {
          protoolsendemail(this.$refs.LeftTablemake.selectedRowsData.id)
            .then(res => {
              if (res.code) {
                this.$message.success(res.message);
                this.datarolad();
              } else {
                this.$message.error(res.message);
                this.spinning = false;
              }
            })
            .finally(() => {
              this.spinning = false;
            });
        }
      }
    },
    datarolad() {
      this.getOrderList();
      this.getcenter();
      this.gettop();
      this.getrightbotdata();
      this.$refs.LeftTablemake.selectedRowsData = [];
      this.$refs.LeftTablemake.selectedRowKeys = [];
      this.$refs.LeftTablemake.slectedRows = [];
      if (this.$refs.CenterTablemake && this.$refs.CenterTablemake.erpid) {
        this.getbotdata(this.$refs.CenterTablemake.erpid);
      }
    },
    finish(ids, type) {
      finishedduoxuan(ids, type).then(res => {
        if (res.code) {
          this.$message.success("订单完成");
          this.getOrderList();
          this.getcenter();
          this.gettop();
          if (this.$refs.CenterTablemake.erpid) {
            this.getbotdata(this.$refs.CenterTablemake.erpid);
          }
          this.$refs.LeftTablemake.selectedRowsData = [];
          this.$refs.LeftTablemake.selectedRowKeys = [];
          this.$refs.LeftTablemake.slectedRows = [];
        } else {
          this.$message.error(res.message);
        }
      });
    },
    pageload(load) {
      this.spinning = load;
    },
    finishv3() {
      this.$message.success("订单完成");
      this.spinning = false;
      this.getOrderList();
      this.getcenter();
      this.gettop();
      if (this.$refs.CenterTablemake.erpid) {
        this.getbotdata(this.$refs.CenterTablemake.erpid);
      }
      this.$refs.LeftTablemake.selectedRowsData = [];
      this.$refs.LeftTablemake.selectedRowKeys = [];
      this.$refs.LeftTablemake.slectedRows = [];
    },
    queryclick() {
      this.querydataVisible = true;
      this.form.OrderNo = "";
    },
    protoolfeedback() {
      let type_ = "";
      if (this.params.type_.indexOf("23") != -1 && this.params.type_.indexOf("6") != -1) {
        type_ = "3,6,20,21,22";
      } else if (this.params.type_.indexOf("23") != -1 && this.params.type_.indexOf("6") == -1) {
        type_ = "3,20,21,22";
      } else {
        type_ = this.params.type_
          .slice()
          .sort((a, b) => a - b)
          .join(",");
      }
      this.params.type_ = type_;
      protoolfeedback(this.params)
        .then(res => {
          if (res.code) {
            this.$message.success("操作成功");
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
          this.params = {};
        });
    },
  },
};
</script>
<style lang="less" scoped>
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
/deep/.ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-upload-select-text {
  margin-left: -18px;
  margin-top: 2px;
}
.required {
  /deep/.ant-form-item-label label {
    color: red;
  }
}
.fank {
  /deep/.ant-select {
    width: 436px;
  }
}
/deep/.userStyle {
  user-select: all;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 0 0 0 16px;
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
  font-size: 12px;
  margin-left: -17px;
  margin-top: 27px;
}
/deep/.ant-collapse > .ant-collapse-item {
  border: none;
  border-top: 2px solid rgb(233, 233, 240);
}
/deep/.ant-collapse {
  background-color: white;
  border-width: 0 2px 2px 0;
  border-style: solid;
  border-color: #e9e9f0;
  border-radius: inherit;
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding: 16px 0px 18px 0px;
  padding-left: 15px;
  padding-top: 0px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  // margin-left: -15px;
  margin-top: -19px;
}
.custom-input {
  background-color: white;
  color: black;
}
.queryclass {
  /deep/.ant-modal-body {
    padding: 27px 0 27px 0;
  }
}
.projectBackend {
  height: 814px;
  min-width: 1670px;
  background: #ffffff;
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
  }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin: 10px 0 0 16px;
  }
  .leftContent {
    width: 60%;
    height: 776px;
    border: 2px solid rgb(233, 233, 240);
    //user-select: none;
    .mintable {
      /deep/.ant-table-body {
        min-height: 737px;
      }
    }
  }
  .centerContent {
    .centercollapse {
      border-left: 2px solid rgb(233, 233, 240);
    }
    .topContent {
      width: 100%;
      height: 9.4%;
      /deep/.ant-empty-normal {
        margin-top: -13px;
      }
      /deep/.ant-empty-description {
        margin-top: 4px;
      }
      /deep/.ant-empty-image {
        display: none;
      }
      /deep/.ant-table-placeholder {
        height: 38px;
      }
    }
    .mintable1 {
      border-top: 2px solid rgb(233, 233, 240);
      /deep/.ant-table .ant-table-thead > tr > th {
        height: 34px;
      }
      /deep/.ant-table-body {
        min-height: 648px;
      }
    }
  }
  .rightContent {
    width: 19%;
    height: 760px;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
    border-top: 0;
    .mintable2 {
      /deep/.ant-table-body {
        min-height: 343px;
        border-bottom: 2px solid rgb(233, 233, 240);
      }
    }
    .mintable3 {
      /deep/.ant-table-body {
        min-height: 326px;
        border-bottom: 2px solid rgb(233, 233, 240);
      }
    }
    .rtContent {
      height: 396px;
      width: 100%;
      border-top: 2px solid rgb(233, 233, 240);
    }
    .rbContent {
      height: 361;
      width: 100%;
      border-top: 2px solid rgb(233, 233, 240);
      border-bottom: 1px solid rgb(233, 233, 240);
    }
  }
  .footerAction {
    width: 100%;
    height: 48px;
    border-bottom: 2px solid #e9e9f0;
    border-left: 2px solid #e9e9f0;
    border-right: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
  }
}
</style>
