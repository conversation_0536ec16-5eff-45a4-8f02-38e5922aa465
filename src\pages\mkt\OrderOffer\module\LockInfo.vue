<!-- 市场管理 - 订单报价- 订单锁定 -->
<template>
    <a-form-model >
      <a-form-model-item label="是否锁定" :labelCol="{span: 6}" :wrapperCol="{span: 12,}">
          <a-checkbox  v-model="val.isLock"  @change="changeis" />
        </a-form-model-item>
        <a-form-model-item label="锁定原因" :labelCol="{span: 6}" :wrapperCol="{span: 17}">
          <a-textarea  v-model="val.lockCause" :disabled="val.isLock==true ? false:true "/>
        </a-form-model-item>
      </a-form-model>
  </template>
  
  <script>
  export default {
    props:['isLock','lockCause'],      
    data() {
      return {      
        val:{
          'isLock':false,
          'lockCause':'',
        }
      };
    },
    methods: { 
      changeis(){
        if(this.val.isLock==false){
          this.val.lockCause =null
        }
      },
    },
    mounted(){
      this.val.isLock = this.isLock
      this.val.lockCause = this.lockCause
    }
  };
  </script>