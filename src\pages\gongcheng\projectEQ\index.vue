<!-- 工程管理 - 问客管理  -->
<template>
  <a-spin :spinning="spinning">
    <div class="projecteq">
      <div style="width: 100%; display: flex">
        <div
          class="leftContent"
          style="border: 1px solid rgb(233, 233, 240); border-right: 2px solid rgb(233, 233, 240); position: relative; user-select: none"
          ref="tableWrapper"
        >
          <span class="iconstyle">
            <a-tooltip @click="caretup" v-if="fold" title="展开"><a-icon type="unordered-list"></a-icon></a-tooltip>
            <a-tooltip @click="caretdown" v-if="!fold" title="折叠"><a-icon type="unordered-list"></a-icon></a-tooltip>
          </span>
          <a-table
            :columns="columns1"
            :customRow="onClickRow"
            :pagination="pagination"
            :scroll="{ y: 738, x: 1200 }"
            :rowClassName="isRedRow"
            rowKey="proOrderId"
            :dataSource="eqdatasource"
            @change="handleTableChange"
            class="leftstyle"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <template slot="labelUrl" slot-scope="record">
              <a-tooltip title="工程指示">
                <a-icon type="container" class="noCopy" style="color: #ff9900; font-size: 18px" @click.stop="orderClick(record, record.proOrderId)" />
              </a-tooltip>
              <!-- <span  @click="OperationLog(record)" style="color: #428bca;" > 日志</span> -->
            </template>
            <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
              <span>{{ record.orderNo }}</span
              >&nbsp;
              <span class="tagNum" style="display: inline-block; height: 19px">
                <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
                  <span
                    class="noCopy"
                    style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                  <span
                    class="noCopy"
                    style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </a-tooltip>
                <a-tooltip title="加急" v-else-if="record.isJiaji">
                  <span
                    class="noCopy"
                    style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </a-tooltip>
                <a-tooltip title="新客户" v-if="record.isNewCust">
                  <a-tag class="noCopy tagstyle"> 新 </a-tag>
                </a-tooltip>
                <a-tag v-if="record.ka" class="noCopy tagstyle"> KA </a-tag>
                <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.isReOrder == 1">
                  <a-tag class="noCopy tagstyle"> 改 </a-tag>
                </a-tooltip>
                <a-tag v-if="record.pauseCancelState == 2" class="noCopy tagstyle"> 取消 </a-tag>
                <a-tag v-if="record.pauseCancelState == 3" class="noCopy tagstyle"> 暂停 </a-tag>
                <a-tag v-if="record.pauseCancelState == 4" class="noCopy tagstyle"> 暂停取消 </a-tag>
                <a-tag v-if="record.isJunG" class="noCopy tagstyle"> {{ record.joinFactoryId == 70 ? "J" : "军" }} </a-tag>
                <a-tooltip v-if="record.identificationType == 1" title="按新单制作">
                  <span
                    style="
                      background: #ff9900;
                      border-radius: 50%;
                      color: white;
                      padding: 0 1px;
                      text-align: center;
                      height: 22px;
                      width: 22px;
                      line-height: 20px;
                      display: inline-block;
                      font-size: 12px;
                    "
                  >
                    新 </span
                  >&nbsp;
                </a-tooltip>
                <a-tooltip v-if="record.identificationType == 2" title="按返单有改制作">
                  <span
                    style="
                      background: #ff9900;
                      border-radius: 50%;
                      color: white;
                      padding: 0 1px;
                      text-align: center;
                      height: 22px;
                      width: 22px;
                      line-height: 20px;
                      display: inline-block;
                      font-size: 12px;
                    "
                  >
                    改 </span
                  >&nbsp;
                </a-tooltip>
              </span>
            </div>
          </a-table>
          <right-copy ref="RightCopy" />
        </div>
        <div class="rightContent" style="border: 1px solid rgb(233, 233, 240)">
          <a-table
            :columns="columns2"
            :pagination="false"
            rowKey="realName"
            :scroll="{ y: 738, x: 200 }"
            :dataSource="datasource1"
            class="rightstyle"
          >
            <span slot="num" slot-scope="text, record, index" class="contentTabAction">
              {{ index + 1 }}
            </span>
          </a-table>
        </div>
      </div>
      <div class="footerAction" style="user-select: none; margin-left: -2px">
        <div>
          <a-button
            type="primary"
            class="box"
            @click="Uploadattachments"
            v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementUpEQAttachment')"
            >上传附件</a-button
          >
          <a-upload
            name="file"
            ref="fileRef7"
            :customRequest="httpRequest7"
            :file-list="fileList7"
            :show-upload-list="false"
            :maxCount="1"
            @change="handleChange7"
          >
            <!-- <a-button type="primary"    style="display: none;"> </a-button> -->
          </a-upload>
          <!-- <a-button type="primary" class="box" @click="Askcustomerstoretreat()"
         v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementEQBack')">
          问客回退
      </a-button>  -->
          <a-button type="primary" class="box" @click="withdraweq" v-if="checkPermission('MES.EngineeringModule.EqManagement.YjBackEQ')">
            撤回问客
          </a-button>
          <div>
            <a-button
              type="primary"
              class="box"
              @click="FileReplacement"
              v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementUpdateFilePro')"
            >
              文件替换
            </a-button>
            <a-upload
              accept=".rar,.zip"
              name="file"
              :multiple="false"
              :customRequest="customRequest"
              :showUploadList="false"
              ref="fileRef"
              :before-upload="beforeUpload1"
              v-show="false"
            >
              <a-button><a-icon type="upload" /> </a-button>
            </a-upload>
          </div>
          <!-- <a-button type="primary" class="box" @click="Emailsending"
            v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementEQSendMail')">
                邮件发送(E)
            </a-button> 
            <a-button type="primary" class="box" @click="Emailinformation"
            v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementEQSave')">
                邮件信息
            </a-button> -->
          <!-- <a-button type="primary" class="box" @click="eqsending"  2023/9/19
            v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementEQSend')">
                发送
            </a-button> -->
          <a-button
            type="primary"
            class="box"
            @click="wenkeClick"
            v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementGetWenKeUrl')"
          >
            问客
          </a-button>
          <a-button type="primary" class="box" @click="startbutton" v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementEQStart')">
            开始(S)
          </a-button>
          <a-button type="primary" class="box" @click="queryClick"> 查询(F) </a-button>
        </div>
        <div v-if="buttonsmenu">
          <a-dropdown>
            <a-button type="primary" class="box1" @click.prevent> 按钮菜单栏 </a-button>
            <template #overlay>
              <a-menu class="tabRightClikBox1">
                <a-menu-item @click="queryClick">查询(F)</a-menu-item>
                <a-menu-item @click="startbutton" v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementEQStart')"
                  >开始(S)</a-menu-item
                >
                <a-menu-item @click="wenkeClick" v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementGetWenKeUrl')"
                  >问客</a-menu-item
                >
                <a-menu-item @click="FileReplacement" v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementUpdateFilePro')"
                  >文件替换</a-menu-item
                >
                <a-menu-item @click="withdraweq" v-if="checkPermission('MES.EngineeringModule.EqManagement.YjBackEQ')">撤回问客</a-menu-item>
                <a-menu-item @click="Uploadattachments" v-if="checkPermission('MES.EngineeringModule.EqManagement.EqManagementUpEQAttachment')"
                  >上传附件</a-menu-item
                >
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel1"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form>
          <a-row>
            <a-col :span="24">
              <a-form-item label="生产编号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
                <a-input v-model="formdata.OrderNo" placeholder="请输入生产编号" allowClear autoFocus> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="客户型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
                <a-input v-model="formdata.PcbFileName" placeholder="请输入客户型号" allowClear> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="姓名" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
                <a-select v-model="formdata.RealName" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="(ite, index) in list" :key="index" :value="ite.account" :lable="ite.name">{{ ite.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <!-- 操作日志弹窗 -->
      <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose @cancel="reportHandleCancel1" :maskClosable="false" :width="800" centered>
        <div class="projectackend">
          <a-table
            :columns="laborcolumns"
            :dataSource="labordata"
            :pagination="false"
            :scroll="{ y: 541 }"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
          >
            <template slot="addresscontent" slot-scope="record">
              <span v-if="record.content.indexOf('地址') == -1">{{ record.content }}</span>
              <span v-else>
                <span>{{ record.content.split("地址：")[0] }}地址：</span>
                <span @click.stop="ContractDownload1(record)" style="color: rgb(68, 146, 235)">{{ record.content.split("地址：")[1] }}</span>
              </span>
            </template>
          </a-table>
        </div>
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel1">取消</a-button>
        </template>
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        title="确认弹窗"
        :visible="confirmdataVisible"
        @cancel="reportHandleCancel1"
        @ok="confirmhandleOk"
        ok-text="确定"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span v-if="ttype == 3 && datasource.eqMailSendCount >= 1" style="color: red; font-weight: bold">{{ proorderNo }}{{ promptmessage }}</span>
        <span v-else>{{ proorderNo }}{{ promptmessage }}</span>
      </a-modal>
      <!-- 邮件信息弹窗 -->
      <a-modal
        :title="proorderNo + ' 邮件信息 '"
        :visible="emaildataVisible"
        @cancel="reportHandleCancel1"
        @ok="emailhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="600"
        centered
      >
        <a-form>
          <a-row>
            <a-col :span="24">
              <a-form-item label="收件人" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.eqEmail" autoFocus> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="抄送" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.cc" autoFocus> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="主题" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.subject" autoFocus> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="正文" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-textarea allowClear :auto-size="{ minRows: 10, maxRows: 15 }" v-model="emaildata.body" autoFocus> </a-textarea>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <a-modal
        title="上传附件"
        :visible="dataVisibleileFile"
        @cancel="reportHandleCancel1"
        @ok="handleOkFile"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        centered
        :maskClosable="false"
        :width="400"
      >
        <template slot="footer">
          <a-button key="back" @click="reportHandleCancel1">取消</a-button>
          <a-button key="submit" type="primary" :disabled="!uppath || uppath == ''" @click="handleOkFile">确定</a-button>
        </template>
        <a-form-model :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
          <a-form-model-item label="交期">
            <a-date-picker format="YYYY-MM-DD" placeholder="请选择交期" @change="TimeChange" v-model="delDate" style="width: 235px"> </a-date-picker>
          </a-form-model-item>
          <a-upload :multiple="false" :file-list="fileList7" :customRequest="httpRequest7" :show-upload-list="true" @change="handleChange7">
            <a-button> <a-icon type="upload" />上传文件 </a-button>
          </a-upload>
        </a-form-model>
      </a-modal>
      <a-modal :title="meslist" :visible="dataVisibleNo" @cancel="reportHandleCancel1" destroyOnClose :maskClosable="false" :width="400" centered>
        <template slot="footer">
          <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
          <a-button @click="reportHandleCancel1">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
          </p>
          <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import RightCopy from "@/pages/RightCopy";
import { inquirycheckorderfile } from "@/services/mkt/Inquiry.js";
import { ppebuttonCheck } from "@/services/projectMake/index.js";
import {
  eqmanagementlist,
  managementuserinfo,
  emSEQMaineqStart,
  emSEQMaineqSend,
  emSEQMaineqRepair,
  emSEQMaineqSave,
  eqSendmail,
  updateFilepro,
  getViewLog,
  eqBack,
} from "@/services/projectApi";
import { toSendProblems, yjbackproblems, upeQAttachment, upLoadFlyingFile } from "@/services/projectMake";
import { checkPermission } from "@/utils/abp";
import moment from "moment";
import axios from "axios";
import { upLoadEnquiryFile } from "@/services/mkt/Inquiry.js";
const columns1 = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "生产编号",
    align: "left",
    dataIndex: "orderNo",
    ellipsis: true,
    scopedSlots: { customRender: "orderNo" },
    width: 110,
  },
  {
    title: "下单时间",
    align: "left",
    dataIndex: "createTime",
    ellipsis: true,
    width: 135,
  },
  {
    title: "订单交期",
    align: "left",
    ellipsis: true,
    dataIndex: "deliveryDate",
    width: 95,
  },
  {
    title: "类型",
    dataIndex: "isReOrder",
    customRender: (text, record, index) =>
      `${record.isReOrder == 0 ? "新单" : record.isReOrder == 1 ? "返单" : record.isReOrder == 2 ? "返单更改" : ""}`,
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "状态",
    align: "left",
    dataIndex: "statusType",
    ellipsis: true,
    width: 85,
  },
  {
    title: "次数",
    align: "left",
    dataIndex: "eqNumber",
    ellipsis: true,
    width: 45,
  },
  {
    title: "提交时间",
    align: "left",
    dataIndex: "eqInputDate",
    ellipsis: true,
    width: 135,
    sorter: (a, b) => {
      return a.eqInputDate.localeCompare(b.eqInputDate);
    },
  },
  {
    title: "发出时间",
    align: "left",
    dataIndex: "eqSendDate",
    ellipsis: true,
    width: 135,
    sorter: (a, b) => {
      return a.eqSendDate.localeCompare(b.eqSendDate);
    },
  },
  {
    title: "联系人",
    width: 70,
    ellipsis: true,
    dataIndex: "eqContactPerson",
    align: "left",
  },

  {
    title: "联系人电话",
    width: 160,
    ellipsis: true,
    dataIndex: "eqContactPhone",
    align: "left",
  },
  {
    title: "业务员",
    width: 100,
    ellipsis: true,
    dataIndex: "businessPerson",
    align: "left",
  },
  {
    title: "业务员电话",
    width: 120,
    ellipsis: true,
    dataIndex: "businessPhone",
    align: "left",
  },
  {
    title: "来源",
    width: 100,
    ellipsis: true,
    dataIndex: "eqSourceStr",
    align: "left",
  },
  {
    title: "审核人",
    width: 90,
    ellipsis: true,
    dataIndex: "eqStartName",
    align: "left",
  },
  {
    title: "面积",
    align: "left",
    dataIndex: "boardArea",
    ellipsis: true,
    width: 70,
  },
  {
    title: "EQ发送后耗时",
    align: "left",
    dataIndex: "eqSendCostTime",
    ellipsis: true,
    width: 110,
  },
  {
    title: "EQ完成时间",
    align: "left",
    dataIndex: "eqLastSendDate",
    ellipsis: true,
    width: 110,
  },
  {
    title: "版本",
    dataIndex: "proRev",
    className: "userStyle",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "指示",
    width: 45,
    align: "center",
    scopedSlots: { customRender: "labelUrl" },
    fixed: "right",
    class: "noCopy",
  },
];
const columns2 = [
  {
    title: "序号",
    key: "index",
    align: "center",
    // fixed:'left',
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "姓名",
    align: "left",
    // fixed:'left',
    dataIndex: "realName",
    width: 90,
  },

  {
    title: "个数",
    align: "center",
    width: 70,
    dataIndex: "orderCount",
  },
];
const laborcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 20,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作时间",
    align: "left",
    dataIndex: "createTime",
    width: 65,
  },
  // {
  //   title: "操作人",
  //   align: "left",
  //   dataIndex: 'userName',
  //   width:30,
  // },
  {
    title: "内容",
    align: "left",
    scopedSlots: { customRender: "addresscontent" },
    width: 185,
  },
];
export default {
  name: "projectEQ",
  inject: ["reload"],
  components: {
    RightCopy,
  },
  data() {
    return {
      fold: false,
      dataVisibleNo: false,
      check1: false,
      checkData: [],
      checkType: "",
      meslist: "",
      labordata: [],
      buttonsmenu: false,
      EQFileName: "",
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      fileList7: [],
      laborcolumns,
      promptmessage: "",
      emaildataVisible: false,
      labordataVisible: false,
      datasource: [],
      spinning: false,
      ttype: "",
      eQSource: "",
      id: "",
      proorderNo: "",
      formdata: {
        OrderNo: "",
        PcbFileName: "",
        RealName: "",
      },
      columns1,
      list: [],
      uppath: "",
      columns2,
      eqdatasource: [],
      datasource1: [],
      emaildata: [],
      dataVisible: false,
      confirmdataVisible: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        isCtrlPressed: false,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      dataVisibleileFile: false,
      delDate: null,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      let leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerWidth < 1912) {
        leftContent.style.height = window.innerHeight - 133 + "px";
      } else {
        leftContent.style.height = "780px";
      }
      this.geteqdata();
      this.getstatistics();
      this.caretdown();
    });
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.handleResize, true);
  },
  watch: {
    eqdatasource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let pauseCancelState = val.some(val => val.pauseCancelState == 4) ? 30 : 0;
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < val.length; i++) {
              let obj2 = val[i].orderNo;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0 && longestChars.length > 12) {
              this.columns1[1].width = 110 + (longestChars.length - 12) * 10 + "px";
              this.columns1[11].width = "100px";
              this.columns1[10].width = "160px";
            }
            if (result >= 1 && longestChars.length > 12) {
              this.columns1[1].width = 110 + (longestChars.length - 12) * 10 + result * 25 + pauseCancelState + "px";
              this.columns1[11].width = 100 - result * 13 - (longestChars.length - 12) * 3 + "px";
              this.columns1[10].width = 160 - result * 15 - (longestChars.length - 12) * 3 + "px";
            }
            if (result == 0 && longestChars.length <= 12) {
              this.columns1[1].width = "110px";
              this.columns1[11].width = "100px";
              this.columns1[10].width = "160px";
            }
            if (result >= 1 && longestChars.length <= 12) {
              this.columns1[1].width = 110 + result * 25 + pauseCancelState + "px";
              this.columns1[11].width = 100 - result * 13 + "px";
              this.columns1[10].width = 160 - result * 15 + "px";
            }
          });
        }
      },
    },
  },
  methods: {
    orderClick(record, id) {
      this.$nextTick(function () {
        this.$router.push({
          path: "/gongcheng/engineering",
          query: {
            OrderNo: record.orderNo,
            id: id,
            factory: record.joinFactoryId,
            typee: "4",
            eqSource: record.eqSource,
            isCustRule: record.isCustRule,
            custNo: record.custNo,
            fileUploadedCount: record.fileUploadedCount,
            businessOrderNo: record.businessOrderNo,
          },
        });
      });
    },
    handleResize() {
      this.screenWidth = window.innerWidth;
      this.screenHeight = window.innerHeight;
      var leftstyle = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftstyle1 = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[1].children[1];
      var rightstyle = document.getElementsByClassName("rightstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      let leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerWidth < 1912) {
        leftContent.style.height = window.innerHeight - 133 + "px";
      } else {
        leftContent.style.height = "780px";
      }
      this.$refs.tableWrapper.style.height = window.innerHeight - 143 > 777 ? "777px" : window.innerHeight - 143 + "px";
      if (leftstyle && this.eqdatasource.length != 0) {
        leftstyle.style.height = window.innerHeight - 173 + "px";
        leftstyle1.style.height = window.innerHeight - 173 + "px";
      } else {
        leftstyle.style.height = 0;
        leftstyle1.style.height = 0;
      }
      if (rightstyle && this.datasource1.length != 0) {
        rightstyle.style.height = window.innerHeight - 173 + "px";
      } else {
        rightstyle.style.height = 0;
      }
      var elements = document.getElementsByClassName("box");
      const num = elements.length * 110;
      var footerwidth = this.screenWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (this.screenWidth < 1920 || this.screenHeight < 923) {
        if (paginnum * 50 + 310 < footerwidth / 2 && elements.length * 110 + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          if (this.screenWidth > 766) {
            this.pagination.simple = false;
            this.pagination.size = "small";
            this.pagination.showSizeChanger = false;
            this.pagination.showQuickJumper = false;
          }
        }
        if (paginnum * 25 + 200 + num < this.screenWidth - 150 && this.screenWidth > 766) {
          if (footerwidth / 2 < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
          this.buttonsmenu = false;
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "inline-block";
          }
        } else {
          if (this.screenWidth > 766) {
            if (footerwidth / 2 < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
            this.buttonsmenu = true;
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
          } else {
            this.pagination.simple = true;
            if (this.screenWidth - 4 - num < 100) {
              this.buttonsmenu = true;
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
              }
            } else {
              this.buttonsmenu = false;
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "inline-block";
              }
            }
          }
        }
      } else {
        this.buttonsmenu = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
        this.pagination.simple = false;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
    },
    ContractDownload1(record) {
      if (record.content.indexOf("地址") != -1) {
        const xhr = new XMLHttpRequest();
        const queryString = record.content.split("地址：")[1];
        let a = queryString.split(".").slice(-1)[0];
        const splitArray = record.content.split("5C")[2].split(".")[0];
        xhr.open("GET", queryString, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = splitArray + "." + a;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      }
    },
    handleChange7({ fileList }) {
      this.fileList7 = [fileList[fileList.length - 1]];
      //this.fileList7 = fileList;
      let a = [];
      let b = [];
      for (let index = 0; index < this.fileList7.length; index++) {
        this.EQFileName = this.fileList7[index].name;
        // a.push(this.fileList7[index].name)
        // b.push(this.fileList7[index].response)
      }
      // this.EQFileName = a.join(',')
      // this.uppath = b.join(',')
    },
    OperationLog(record) {
      this.labordataVisible = true;
      getViewLog(record.proOrderId).then(res => {
        if (res.code) {
          this.labordata = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        this.reportHandleCancel1();
        this.dataVisible = true;
        e.preventDefault();
      } else if (e.keyCode == "67" && !this.isCtrlPressed && this.confirmdataVisible) {
        this.isCtrlPressed = false;
        this.reportHandleCancel1();
        e.preventDefault();
      } else if (e.keyCode == "83" && this.isCtrlPressed && checkPermission("MES.EngineeringModule.EqManagement.EqManagementEQStart")) {
        this.isCtrlPressed = false;
        //this.reportHandleCancel1()
        this.startbutton();
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        e.preventDefault();
      } else if (e.keyCode == "13" && this.confirmdataVisible) {
        this.confirmhandleOk();
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    checkPermission,
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.indexOf(".rar") != -1 || file.name.indexOf(".zip") != -1;
        const filesize = Number(file.size / 1024 / 1024) < 500;
        if (!isJpgOrPng) {
          _this.$message.error("文件只支持.rar或.zip格式");
          reject();
        } else if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    //分段上传MD5  文件替换
    async customRequest(data) {
      let GUID;
      let shardSize;
      let shardCount;
      let next;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("CustNo", this.datasource.custNo);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      let params = {
        fileName: data.file.name,
        MD5Code: GUID,
        custNo: this.datasource.custNo,
      };
      let params1 = {
        id: this.id,
      };
      this.spinning = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              params1.PcbFilePath = res.data.split(",")[0];
              params1.pcbFileName = replacedText;
              updateFilepro(params1)
                .then(res => {
                  if (res.code) {
                    this.$message.success("上传成功");
                    this.geteqdata();
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.spinning = false;
                });
            }
          } else {
            this.$message.error(res.message);
            i = shardCount;
            this.spinning = false;
          }
        });
      }
    },
    //分段上传附件
    async httpRequest7(data, type) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      shardSize = 1024 * 1024; //以1kB为一个分片
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      this.spinning = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              this.uppath = res.message;
              if (!this.dataVisibleileFile) {
                this.upeQAttachment();
              } else {
                this.spinning = false;
              }
            }
          } else {
            this.$message.error(res.message);
            i = shardCount;
            this.spinning = false;
          }
        });
      }
    },
    // async httpRequest7(data, type) {
    //     const formData = new FormData();
    //     formData.append("file", data.file);
    //     let headers = {}
    //     headers['Content-Disposition'] = 'form-data; name="FileName"; filename="' + encodeURIComponent(data.file.name) + '"';
    //     headers['Content-Type']= 'text/html;charset=UTF-8';
    //     await upLoadFlyingFile(formData).then(res => {
    //       if (res.code == 1) {
    //         data.onSuccess(res.data);
    //         this.uppath = res.data
    //         if(!this.dataVisibleileFile){
    //           this.upeQAttachment()
    //         }
    //       } else {
    //         this.$message.error(res.message)
    //       }
    //     })
    //   },
    //上传附件成功
    upeQAttachment() {
      let params = {
        path: this.uppath,
        eqSource: Number(this.datasource.eqSource),
        orderNo: this.datasource.orderNo,
        businessOrderNo: this.datasource.businessOrderNo,
        joinFactoryId: this.datasource.joinFactoryId,
        fileName: this.EQFileName,
      };
      if (this.delDate) {
        params.delDate = this.delDate;
      }
      this.spinning = true;
      upeQAttachment(params)
        .then(res => {
          if (res.code) {
            this.$message.success("上传成功");
            this.geteqdata();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.delDate = null;
          this.uppath = "";
          this.spinning = false;
        });
    },
    wenkeClick() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.$router.push({
        path: "eqDetails",
        query: {
          OrderNo: this.datasource.orderNo,
          eQSource: this.datasource.eqSource,
          id: this.id,
          businessOrderNo: this.datasource.businessOrderNo,
          joinFactoryId: this.datasource.joinFactoryId,
          Jump: "问客管理",
          para4IntDelivery: this.datasource.para4IntDelivery,
          eqNumber: this.datasource.eqNumber,
          deliveryDate: this.datasource.deliveryDate,
        },
      });
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.proOrderId && record.proOrderId == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.proorderNo = record.orderNo;
            this.id = record.proOrderId;
            this.eQSource = record.eqSource;
            this.datasource = record;
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.pacurrent = pagination.current;
      this.pageStat = false;
      let params = this.formdata;
      if (JSON.stringify(params) != "{}") {
        this.geteqdata(params);
      } else {
        this.geteqdata();
      }
    },
    handleOk() {
      let data = this.formdata;
      if (data.OrderNo) {
        var arr1 = data.OrderNo.split("");
        if (arr1.length > 20) {
          arr1 = arr1.slice(0, 20);
        }
        data.OrderNo = arr1.join("");
      }
      if (data.OrderNo && typeof data.OrderNo === "string" && data.OrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产编号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      this.pagination.current = 1;
      this.geteqdata(data);
    },
    confirmhandleOk() {
      this.confirmdataVisible = false;
      if (this.ttype == "1") {
        this.spinning = true;
        emSEQMaineqStart(this.id)
          .then(res => {
            if (res.code) {
              this.$router.push({
                path: "eqDetails",
                query: {
                  OrderNo: this.datasource.orderNo,
                  eQSource: this.datasource.eqSource,
                  id: this.id,
                  businessOrderNo: this.datasource.businessOrderNo,
                  joinFactoryId: this.datasource.joinFactoryId,
                  Jump: "问客管理",
                  para4IntDelivery: this.datasource.para4IntDelivery,
                  eqNumber: this.datasource.eqNumber,
                  deliveryDate: this.datasource.deliveryDate,
                },
              });
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.ttype == "2") {
        this.spinning = true;
        let params = {
          orderNo: this.proorderNo,
          eqSource: Number(this.eQSource),
          businessOrderNo: this.datasource.businessOrderNo,
          joinFactoryId: this.datasource.joinFactoryId,
        };
        toSendProblems(params)
          .then(res => {
            if (res.code) {
              this.$message.success("发送成功");
              this.geteqdata();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
        // emSEQMaineqSend(this.id).then(res=>{
        //   if(res.code){
        //     this.$message.success("EQ发送成功")
        //   }else{
        //     this.$message.error(res.message)
        //   }
        // }).finally(()=>{
        //   this.spinning = false
        // })
      }
      if (this.ttype == "3") {
        this.spinning = true;
        eqSendmail(this.id)
          .then(res => {
            if (res.code) {
              this.$message.success("邮件发送成功");
              this.geteqdata();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.ttype == "4") {
        this.spinning = true;
        let params = {
          orderNo: this.proorderNo,
          eqSource: Number(this.eQSource),
          businessOrderNo: this.datasource.businessOrderNo,
          joinFactoryId: this.datasource.joinFactoryId,
        };
        yjbackproblems(params)
          .then(res => {
            if (res.code) {
              this.$message.success("撤回成功");
              this.geteqdata();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.ttype == "5") {
        this.spinning = true;
        eqBack(this.id)
          .then(res => {
            if (res.code) {
              this.$message.success("问客回退成功");
              this.geteqdata();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    emailhandleOk() {
      this.emaildata.id = this.id;
      emSEQMaineqSave(this.emaildata).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          this.getemaildata();
        } else {
          this.$message.error(res.message);
        }
      });
      this.emaildataVisible = false;
    },
    reportHandleCancel1() {
      this.dataVisible = false;
      this.confirmdataVisible = false;
      this.emaildataVisible = false;
      this.labordataVisible = false;
      this.dataVisibleileFile = false;
      this.delDate = null;
      this.uppath = "";
      this.dataVisibleNo = false;
    },
    FileReplacement() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    Emailinformation() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.emaildataVisible = true;
      this.getemaildata();
    },
    Uploadattachments() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      // this.dataVisibleileFile = true
      // this.delDate = this.datasource.deliveryDate;
      this.fileList7 = [];
      if (this.datasource.eqNumber > 1 || this.datasource.para4IntDelivery <= 3) {
        this.dataVisibleileFile = true;
        this.delDate = this.datasource.deliveryDate;
      } else {
        this.$refs.fileRef7.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
      }
    },
    checkClick() {
      if (this.checkType == "wkks") {
        this.spinning = true;
        emSEQMaineqStart(this.id)
          .then(res => {
            if (res.code) {
              this.$router.push({
                path: "eqDetails",
                query: {
                  OrderNo: this.datasource.orderNo,
                  eQSource: this.datasource.eqSource,
                  id: this.id,
                  businessOrderNo: this.datasource.businessOrderNo,
                  joinFactoryId: this.datasource.joinFactoryId,
                  Jump: "问客管理",
                  para4IntDelivery: this.datasource.para4IntDelivery,
                  eqNumber: this.datasource.eqNumber,
                  deliveryDate: this.datasource.deliveryDate,
                },
              });
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.checkType == "chwk") {
        this.type = "4";
        this.handleOk();
      }
      this.dataVisibleNo = false;
    },
    startbutton() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.id, "EQStartAsync").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData = res.data;
            this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
            this.checkType = "wkks";
            this.dataVisibleNo = true;
            this.meslist = "问客开始按钮检查";
            this.spinning = false;
          } else {
            emSEQMaineqStart(this.id)
              .then(res => {
                if (res.code) {
                  this.$router.push({
                    path: "eqDetails",
                    query: {
                      OrderNo: this.datasource.orderNo,
                      eQSource: this.datasource.eqSource,
                      id: this.id,
                      businessOrderNo: this.datasource.businessOrderNo,
                      joinFactoryId: this.datasource.joinFactoryId,
                      Jump: "问客管理",
                      para4IntDelivery: this.datasource.para4IntDelivery,
                      eqNumber: this.datasource.eqNumber,
                      deliveryDate: this.datasource.deliveryDate,
                    },
                  });
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.spinning = false;
              });
          }
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      });
    },
    eqsending() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.confirmdataVisible = true;
      this.promptmessage = "确认EQ发送吗?";
      this.ttype = "2";
    },
    Emailsending() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.confirmdataVisible = true;
      this.promptmessage = "确认邮件发送吗?";
      this.ttype = "3";
    },
    withdraweq() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.id, "YjBackProblems")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "chwk";
              this.dataVisibleNo = true;
              this.meslist = "问客开始按钮检查";
            } else {
              this.confirmdataVisible = true;
              this.promptmessage = "确认撤回问客吗?";
              this.ttype = "4";
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    Askcustomerstoretreat() {
      if (!this.id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.confirmdataVisible = true;
      this.promptmessage = "确认问客回退吗?";
      this.ttype = "5";
    },
    queryClick() {
      this.dataVisible = true;
      this.formdata = {};
    },
    getstatistics() {
      managementuserinfo().then(res => {
        this.datasource1 = res;
        setTimeout(() => {
          this.handleResize();
        }, 0);
        for (let index = 0; index < this.datasource1.length; index++) {
          let name = this.datasource1[index].realName;
          let account = this.datasource1[index].account;
          this.list.push({ account: account, name: name });
          this.list = this.list.filter(item => {
            return item.name != "全部";
          });
        }
      });
    },
    geteqdata(data) {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      var obj = Object.assign(params, data);
      this.spinning = true;
      eqmanagementlist(obj)
        .then(res => {
          this.eqdatasource = res.items;
          setTimeout(() => {
            this.handleResize();
          }, 0);
          this.pagination.total = res.totalCount;
          if ((obj.OrderNo || obj.RealName || obj.PcbFileName) && this.eqdatasource.length > 0) {
            this.id = this.eqdatasource[0].proOrderId;
            this.proorderNo = this.eqdatasource[0].proorderNo;
            this.eQSource = this.eqdatasource[0].eqSource;
            this.datasource = this.eqdatasource[0];
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    getemaildata() {
      emSEQMaineqRepair(this.id).then(res => {
        if (res.code) {
          this.emaildata = res.data;
        }
      });
    },
    TimeChange(value, dateString) {
      this.delDate = dateString;
    },
    handleOkFile() {
      this.dataVisibleileFile = false;
      this.upeQAttachment();
    },
    caretup() {
      this.fold = false;
      let left = document.getElementsByClassName("leftContent")[0];
      let right = document.getElementsByClassName("rightContent")[0];
      right.style.display = "none";
      left.style.width = "100%";
      this.handleResize();
    },
    caretdown() {
      this.fold = true;
      let left = document.getElementsByClassName("leftContent")[0];
      let right = document.getElementsByClassName("rightContent")[0];
      right.style.display = "";
      left.style.width = "86%";
      right.style.width = "14%";
    },
  },
};
</script>
<style scoped lang="less">
.iconstyle {
  font-size: 17px;
  color: #ff9900;
  position: absolute;
  margin: 0px;
  top: 3px;
  z-index: 99;
  right: 1px;
  height: 33px;
  background: #f8f8f9;
}
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
.tabRightClikBox1 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-table-fixed-header .ant-table-body-inner {
  overflow: hidden;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  margin-left: 10px;
  position: fixed;
  margin: 11px 0;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
.rightstyle {
  overflow-x: auto !important;
  /deep/ .ant-table-body {
    .ant-table-tbody {
      .ant-table-row:last-child {
        .contentTabAction {
          display: none;
        }
      }
    }
    /deep/.rowBackgroundColor {
      background: rgb(223, 220, 220) !important;
    }
  }
}
.mintable1 {
  /deep/.ant-table-body {
    min-height: 738px !important;
  }
}

.projectackend {
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #c9c9c9 !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
/deep/.ant-upload-list {
  margin-left: 22px;
  padding-top: 35px;
}
/deep/.ant-upload {
  float: left;
  margin-left: 15px;
}
/deep/ .ant-table-scroll table .ant-table-fixed-columns-in-body:not([colspan]) {
  color: black;
}
/deep/.ant-table-scroll table .ant-table-fixed-columns-in-body:not([colspan]) > * {
  visibility: inherit;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.box {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.box1 {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.footerAction {
  width: 100%;
  height: 50px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  form {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #fff;
  }
}
.projecteq {
  background-color: #ffffff;
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-thead > tr > th {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
  /deep/.ant-table-row-cell-break-word {
    border-right: 1px solid #efefef;
  }
  /deep/.leftContent {
    //height: 780px;
    .mintable {
      .ant-table-body-inner {
        max-height: 737px !important;
      }
      .ant-table-pagination {
        margin: 6px 0;
        z-index: 99;
        position: absolute;
        bottom: -6.5%;
        margin-left: 1%;
      }
      .ant-table-body {
        min-height: 738px;
      }
    }
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }
}
</style>
