<template>
  <div class="reportList">
    <a-spin :spinning="tableLoading">
      <div class="box_left">
        <div class="form1">
          <a-form-model layout="inline" :model="form">
            <a-form-model-item>
              <a-input v-model="form.reportName" @keyup.enter.native="searchReport" placeholder="报表名称" />
            </a-form-model-item>
            <a-form-model-item>
              <a-select showSearch allowClear optionFilterProp="lable" placeholder="工厂" v-model="Factory">
                <a-select-option
                  style="color: blue"
                  v-for="(item, index) in mapKey(factroyList)"
                  :key="index"
                  :value="item.lable"
                  :lable="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item>
              <a-button type="primary" @click="searchReport" :disabled="form.reportName === '' && !Factory"> 查询 </a-button>
            </a-form-model-item>
            <a-form-model-item>
              <a-button type="primary" @click="onReset"> 重置 </a-button>
            </a-form-model-item>
            <a-form-model-item v-if="checkPermission('MES.ReportModule.ReportManager.ReportManagerAdd')">
              <a-button type="primary" @click="addReport"> 新增 </a-button>
            </a-form-model-item>
            <a-form-model-item style="float: right" v-if="xlsxshow">
              <a-button type="primary" @click="exportReport" :loading="downloading">导出</a-button>
            </a-form-model-item>
          </a-form-model>
        </div>
        <a-spin :spinning="loading">
          <div class="content">
            <div style="width: 28%">
              <a-table
                :columns="columns"
                :data-source="tableData"
                rowKey="key"
                bordered
                :expandIcon="expandIcon"
                childrenColumnName="child"
                :expandIconColumnIndex="1"
                :defaultExpandAllRows="true"
                :loading="tableLoading"
                :pagination="false"
                :rowClassName="isRedRow1"
                :customRow="onClickRow"
                @change="handleTableChange"
                :scroll="{ y: 712 }"
                :class="tableData.length ? 'mintable' : ''"
              >
                <span slot="number" slot-scope="text, record">
                  {{ record.level1 }}
                </span>
                <template slot="action" slot-scope="text, record">
                  <a-tooltip title="报表下载与预览" style="margin-right: 10px" v-if="record.showAction">
                    <a-icon type="download" style="color: #ff9900; font-size: 18px" @click="download(record)" />
                  </a-tooltip>
                  <a-tooltip title="编辑报表" v-if="checkPermission('MES.ReportModule.ReportManager.ReportManagermodify') && record.showAction">
                    <a-icon type="edit" theme="filled" style="color: #ff9900; font-size: 18px" @click="editReport(record)" />
                  </a-tooltip>
                </template>
              </a-table>
            </div>
            <div style="width: 70%; overflow-x: auto" class="preview">
              <a-empty v-if="!xlsxshow" />
              <div v-show="xlsxshow" style="width: 100%; height: 100%">
                <vue-office-excel class="excel" style="height: 100%; width: 100%" :src="xlsxshow" ref="xlsxViewer" :options="options" />
              </div>
              <!-- <a-empty v-if="dataList.length==0"/>
      <table v-else style="border-top: 2px solid black;border-left: 2px solid black;color: black;">
          <thead>
          <tr style="background-color: rgb(0, 176, 80);" >
            <td v-for="(item,index) in dataList" :key="index" style="font-size: 16px;font-weight: bold;text-align: center;" >{{item}}</td>
          </tr>
          </thead>
          <tbody>
           <tr v-for="(row, rowIndex) in previewdata" :key="rowIndex">
          <td v-for="(value, key, columnIndex) in row" :key="columnIndex" style="text-align: center;color: black;">
            {{ value }}
          </td>
          </tr>
          </tbody>
       </table> -->
            </div>
          </div>
        </a-spin>
      </div>
    </a-spin>
    <a-modal v-model="downloadModal" title="报表下载与预览" @ok="handleOk" :destroyOnClose="true" :confirmLoading="confirmLoading" centered>
      <form-create v-model="fApi" :rule="rule" :value.sync="value" :option="option"></form-create>
      <template slot="footer">
        <a-button @click="reportHandleCancel">取消</a-button>
      </template>
      <template slot="footer">
        <a-button type="primary" @click="handleOk" :loading="downloading">下载</a-button>
      </template>
      <template slot="footer">
        <a-button type="primary" @click="Preview" :loading="viewloading">预览</a-button>
      </template>
    </a-modal>
    <a-modal
      v-model="addreportModal"
      :title="modalType == 'edit' ? '编辑报表' : '新增报表'"
      @ok="addHandleOk"
      :width="1200"
      :confirmLoading="confirmLoading"
      destroyOnClose
    >
      <div class="addReportBox">
        <div class="addReportleft">
          <a-form-model :model="addReportForm">
            <a-form-model-item label="名称" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <a-input v-model="addReportForm.reportName" placeholder="报表名称" />
            </a-form-model-item>
            <a-form-model-item>
              <a-row>
                <a-col :span="4">
                  <a-select v-model="addReportForm.sqlType" class="sqlSelect">
                    <a-select-option value="1"> SQL脚本 </a-select-option>
                    <a-select-option value="2"> 存储过程 </a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="20">
                  <SqlEditor
                    v-if="sqlVisible"
                    ref="sqleditor"
                    :value="addReportForm.sqlcontent"
                    :tables="sqlTable"
                    @changeTextarea="changeTextarea($event)"
                  />
                </a-col>
              </a-row>
            </a-form-model-item>
          </a-form-model>
        </div>
        <div class="addReportRight">
          <div class="btn" @click="getVariable">
            获取变量
            <a-icon type="right-circle" theme="filled" />
          </div>
          <div class="addContent">
            <a-table
              :columns="addColumns"
              :data-source="addTableData"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              bordered
              :pagination="false"
              :class="{ minClass: addTableData.length > 0 }"
            >
              <template slot="action" slot-scope="text, record">
                <a-tooltip title="下载报表">
                  <a-icon type="download" style="color: #ff9900; font-size: 18px" @click="download(record)" />
                </a-tooltip>
              </template>
              <template slot="type" slot-scope="text, record">
                <a-select v-model="record.type">
                  <a-select-option value="StrDtp"> 时间 </a-select-option>
                  <a-select-option value="StrTxt"> 文本 </a-select-option>
                  <a-select-option value="StrCmb"> 选择 </a-select-option>
                </a-select>
              </template>
              <template slot="captions" slot-scope="text, record">
                <a-input v-model="record.captions"></a-input>
              </template>
              <template slot="parmsDef" slot-scope="text, record">
                <a-input v-model="record.parmsDef"></a-input>
              </template>
              <template slot="cmbItems" slot-scope="text, record">
                <a-input v-model="record.cmbItems"></a-input>
              </template>
              <template slot="customParmsDef">
                默认
                <a-tooltip placement="topLeft" title="下载表单时参数默认值。">
                  <a-icon type="question-circle" style="margin-left: 5px; color: #ff9900" />
                </a-tooltip>
              </template>
              <template slot="customCaptions">
                描述
                <a-tooltip placement="topLeft" title="字段名lable定义,用于下载报表时填参时字段的lable展示。">
                  <a-icon type="question-circle" style="margin-left: 5px; color: #ff9900" />
                </a-tooltip>
              </template>
              <template slot="customCmbItems">
                选择项
                <a-tooltip placement="topLeft" title="当类型为选择时，该值有多个请用|隔开,例:0|1|2。">
                  <a-icon type="question-circle" style="margin-left: 5px; color: #ff9900" />
                </a-tooltip>
              </template>
            </a-table>
            <div>
              <p style="margin: 0; background: #ff9900; font-weight: 500; text-align: center; color: #fff">用户权限勾选</p>
              <a-checkbox-group style="padding-left: 30px" v-model="selectRoles">
                <a-row>
                  <a-col :span="6" v-for="(item, index) in roleList" :key="index">
                    <a-checkbox :value="item.name">
                      {{ item.name }}
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
              <div style="background-color: rgb(187, 187, 187); height: 2px"></div>
              <a-form-item label="所属模块" :labelCol="{ span: 3 }" :wrapperCol="{ span: 5 }">
                <a-select v-model="addReportForm.reportType" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="(item, index) in list" :key="index" :value="item.value" :lable="item.text">{{ item.text }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-model-item label="授权工厂" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <a-select showSearch allowClear optionFilterProp="lable" placeholder="授权工厂" mode="multiple" v-model="empowerFactory">
                  <a-select-option
                    style="color: blue"
                    v-for="(item, index) in mapKey(factroyList)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/excel/lib/index.css";
import { factroyList } from "@/services/analysis";
import {
  addReport,
  addReportGetVariable,
  editReport,
  editReportParams,
  getExcelData,
  selectreportv2,
  selectreportv3,
  selectreportv4,
  getParList,
  getReportList,
} from "@/services/reportManagement";
import moment from "moment";
import * as XLSX from "xlsx";
import * as XLSX_STYLE from "xlsx-style";
import { checkPermission } from "@/utils/abp";
import sqlFormatter from "sql-formatter";
import SqlEditor from "./components/SqlEditor";
import { getAssignableRoles } from "@/services/identity/user";
const columns = [
  {
    scopedSlots: { customRender: "number" },
    title: "序号",
    width: 70,
    align: "center",
  },
  {
    dataIndex: "captions",
    key: "captions",
    title: "名称",
  },
  {
    title: "Action",
    key: "action",
    scopedSlots: { customRender: "action" },
    width: 65,
  },
];
const addColumns = [
  {
    dataIndex: "name",
    title: "变量名",
    align: "center",
    width: "15%",
  },
  {
    dataIndex: "type",
    title: "类型",
    scopedSlots: { customRender: "type" },
    align: "center",
    width: "15%",
  },
  {
    dataIndex: "captions",
    scopedSlots: { customRender: "captions" },
    align: "center",
    slots: { title: "customCaptions" },
  },
  {
    dataIndex: "parmsDef",
    scopedSlots: { customRender: "parmsDef" },
    align: "center",
    slots: { title: "customParmsDef" },
  },
  {
    dataIndex: "cmbItems",
    scopedSlots: { customRender: "cmbItems" },
    align: "center",
    slots: { title: "customCmbItems" },
  },
];
export default {
  name: "ReportList",
  created() {
    this.reportList();
    this.getRolesList();
    factroyList().then(res => {
      if (res.code) {
        this.factroyList = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
  },
  components: { SqlEditor, VueOfficeExcel },
  data() {
    return {
      options: {
        minColLength: 0, // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
        minRowLength: 0, // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
        widthOffset: 30, //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
        heightOffset: 0, //在默认渲染的列表高度上再加 Npx高
        beforeTransformData: workbookData => {
          return workbookData;
        }, //底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。
        transformData: workbookData => {
          workbookData[0].styles.forEach(ite => {
            if (ite.align == "center") {
              ite.font.size = 12;
            } else {
              ite.font.size = 10;
            }
          });
          return workbookData;
        }, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容
      },
      downloading: false,
      viewloading: false,
      xlsxshow: "",
      xlsxspin: false,
      factroyList: [],
      guid_: "",
      dataList: [],
      targetList: [],
      columns,
      tableData: [],
      copyTableData: [],
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      downloadModal: false,
      form: {
        reportName: "",
      },
      formBoxList: [],
      id_: "",
      // 实例对象
      fApi: {},
      //表单数据
      value: {},
      // 表单其他配置
      option: {
        submitBtn: false,
      },
      //表单生成规则
      rule: [],
      captions: "",
      addreportModal: false,
      addReportForm: {
        reportName: "",
        sqlcontent: "",
        sqlType: "1",
        reportType: "",
      },
      empowerFactory: [],
      Factory: undefined,
      sqlVisible: true,
      sqlTable: {},
      addTableData: [],
      addColumns,
      roleList: [],
      selectRoles: [],
      confirmLoading: false,
      tableLoading: false,
      modalType: "edit",
      reportId: "",
      previewdata: [],
      loading: false,
      list: [
        { value: 2, text: "工程" },
        { value: 1, text: "市场" },
        { value: 3, text: "生产" },
      ],
    };
  },
  methods: {
    checkPermission,
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    onClickRow(record) {
      return {
        on: {
          dblclick: () => {
            if (record.showAction) {
              this.download(record, "预览");
            }
          },
          click: () => {
            if (record.key != this.guid_) {
              this.dataList = [];
              this.xlsxshow = "";
            }
            this.guid_ = record.key;
          },
        },
      };
    },
    isRedRow1(record) {
      let strGroup = [];
      let str = [];
      if (record.key == this.guid_) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    addPropertyToTree1(tree, prop, parentLevel) {
      tree.forEach((node, index) => {
        node[prop] = parentLevel ? `${parentLevel}-${index + 1}` : `${index + 1}`;
        node.showAction = false;
        if (node.child) {
          this.addPropertyToTree1(node.child, prop, node[prop]);
        } else {
          node.showAction = true;
        }
      });
    },
    reportList() {
      this.tableLoading = true;
      getReportList()
        .then(res => {
          if (res.code) {
            this.tableData = JSON.parse(JSON.stringify(res.data));
            this.copyTableData = JSON.parse(JSON.stringify(res.data));
            this.addPropertyToTree1(this.tableData, "level1");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 自定义树形选择图标
    expandIcon(props) {
      if (props.record.child != "null" && props.record.child) {
        if (props.expanded) {
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder-open" style="margin-right:5px" />
            </a>
          );
        } else {
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder" style="margin-right:5px" />
            </a>
          );
        }
      } else {
        return (
          <span style="margin-right:0px">
            <a-icon type=" " />
          </span>
        );
      }
    },
    download(record, type) {
      if (type != "预览") {
        this.downloadModal = true;
      }
      this.id_ = record.id;
      this.guid_ = record.key;
      this.captions = record.captions;
      this.loading = true;
      getParList(record.id).then(res => {
        if (res.code) {
          this.formBoxList = res.data;
          let _data = [];
          let reg = new RegExp("@", "g");
          res.data.forEach(item => {
            if (item.type_ == "StrDtp") {
              // StrTxt 文本 StrCmb 选择
              _data.push({
                type: "DatePicker",
                field: item.names_.replace(reg, ""),
                title: item.captions_,
                value: item.parmsDef ? item.parmsDef : moment().format("YYYY-MM-DD"),
                wrap: {
                  labelCol: { span: 6 },
                  wrapperCol: { span: 18 },
                },
              });
            } else if (item.type_ == "StrTxt") {
              let parmsDef;
              if (item.names_.indexOf("PnYear") > -1) {
                parmsDef = moment().format("YYYY");
              }
              if (item.names_.indexOf("PnMonth") > -1) {
                parmsDef =
                  this.captions.indexOf("03_01_") > -1 || this.captions.indexOf("04_12_") > -1
                    ? moment().format("MM")
                    : moment().subtract(1, "months").format("MM");
              }
              _data.push({
                type: "input",
                field: item.names_.replace(reg, ""),
                title: item.captions_,
                value: parmsDef,
                wrap: {
                  labelCol: { span: 6 },
                  wrapperCol: { span: 18 },
                },
              });
            } else {
              let option_ = [];
              item.cmbItems_.split("|").forEach(item => {
                option_.push({ value: item, label: item });
              });
              _data.push({
                type: "select",
                field: item.names_.replace(reg, ""),
                title: item.captions_,
                //value: item.parmsDef,
                value: moment().format("YYYY-MM-DD"),
                options: option_,
                wrap: {
                  labelCol: { span: 6 },
                  wrapperCol: { span: 18 },
                },
              });
            }
          });
          this.rule = _data;
          if (type == "预览") {
            if (this.rule.length != 0) {
              if (this.rule[0].type == "DatePicker") {
                this.value.PnSDate = moment().format("YYYY-MM-DD");
                this.value.PnEDate = moment().add(1, "day").format("YYYY-MM-DD");
                delete this.value.PnYear;
                delete this.value.PnMonth;
              } else if (this.rule[0].type == "input") {
                this.value.PnYear = moment().format("YYYY");
                this.value.PnMonth = moment().format("MM");
                delete this.value.PnEDate;
                delete this.value.PnSDate;
              } else {
                this.value = [];
              }
            } else {
              this.value = [];
            }
            this.getdata("view");
          }
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    reportHandleCancel() {
      this.downloadModal = false;
      this.loading = false;
    },
    getdata(type) {
      let params = {};
      if (type == "export") {
        window.location.href = this.xlsxshow;
        return;
      }
      this.confirmLoading = true;
      this.xlsxspin = true;
      for (let key in this.value) {
        params["@" + key] = this.value[key] || "";
      }
      if (type == "ok") {
        this.downloading = true;
      } else {
        this.viewloading = true;
      }
      selectreportv4(this.id_, params)
        .then(res => {
          if (res.code) {
            if (type == "ok") {
              window.location.href = res.data;
            }
            this.xlsxshow = res.data;
          } else {
            this.$message.error(res.message);
            this.xlsxshow = "";
          }
        })
        .finally(() => {
          this.confirmLoading = false;
          this.xlsxspin = false;
          this.downloading = false;
          this.viewloading = false;
          this.downloadModal = false;
          this.loading = false;
        });
    },
    convertBase64ToUrl(base64String) {
      const byteCharacters = atob(base64String);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      return URL.createObjectURL(blob);
    },
    downxlsx(url) {
      if (url) {
        const urlObj = new URL(url);
        const path = urlObj.pathname;
        const fileName = path.substring(path.lastIndexOf("/") + 1);
        const fileNameWithoutQuery = decodeURI(fileName.split("?")[0]);
        const xhr = new XMLHttpRequest();
        xhr.open("GET", url, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = fileNameWithoutQuery;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      }
    },
    downloadByteArrayFromString(byteArrayString, filename) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      link.click();
      URL.revokeObjectURL(url);
    },
    exportReport() {
      this.getdata("export");
    },
    Preview() {
      this.getdata("view");
    },
    handleOk() {
      this.getdata("ok");
    },
    exportExcelFile(array, sheetName, fileName) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      const columnWidths = {};
      var a = "";
      array.forEach(row => {
        Object.keys(row).forEach(key => {
          const cellValue = row[key] ? row[key].toString() : "";
          const cellValue1 = key ? key.toString() : "";
          if (cellValue.length > cellValue1.length) {
            a = cellValue.length;
          } else {
            a = cellValue1.length;
          }
          if (!columnWidths[key] || a > columnWidths[key]) {
            columnWidths[key] = a;
          }
        });
      });
      console.log("jsonWorkSheet", jsonWorkSheet);
      jsonWorkSheet["!cols"] = Object.keys(columnWidths).map(key => ({ wch: columnWidths[key] + 6 }));
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        },
      };
      return XLSX.writeFile(workBook, fileName);
    },
    exportExcel(array, sheetName, fileName1) {
      var list = this.getExportDataList(array);
      const workBook = XLSX.utils.book_new();
      const workSheet = XLSX.utils.json_to_sheet(list, { skipHeader: true });
      const columnWidths = {};
      var a = "";
      list.forEach(row => {
        Object.keys(row).forEach(key => {
          const cellValue = row[key] ? row[key].toString() : "";
          const cellValue1 = key ? key.toString() : "";
          if (cellValue.length > cellValue1.length) {
            a = cellValue.length;
          } else {
            a = cellValue1.length;
          }
          if (!columnWidths[key] || a > columnWidths[key]) {
            columnWidths[key] = a;
          }
        });
      });
      workSheet["!cols"] = Object.keys(columnWidths).map(key => ({ wch: columnWidths[key] + 8 }));
      let wsRows = [];
      for (let i in list) {
        if (i == 0) {
          wsRows.push({ hpx: 30 }); // 首行高度为 100px
        } else {
          wsRows.push({ hpx: 30 }); // 其他行高度为 30px
        }
      }
      workSheet["!rows"] = wsRows;
      for (let key in workSheet) {
        if (key == "!ref" || key == "!merges" || key == "!cols" || key == "!rows") {
          continue;
        } else {
          if (key.match(/\d+/g).join("") == "1") {
            workSheet[key].s = {
              border: {
                top: {
                  style: "thin",
                },
                bottom: {
                  style: "thin",
                },
                left: {
                  style: "thin",
                },
                right: {
                  style: "thin",
                },
              },
              fill: {
                fgColor: { rgb: "00B050" },
              },
              font: {
                bold: true,
                sz: 12,
              },
              alignment: {
                horizontal: "center", // 水平（向左、向右、居中）
                vertical: "center", // 上下（向上、向下、居中）
                wrapText: false, // 设置单元格自动换行，目前仅对非合并单元格生效
                indent: 0, // 设置单元格缩进
              },
            };
          } else {
            workSheet[key].s = {
              border: {
                top: {
                  style: "thin",
                },
                bottom: {
                  style: "thin",
                },
                left: {
                  style: "thin",
                },
                right: {
                  style: "thin",
                },
              },
              fill: {
                fgColor: { rgb: "ffffff" },
              },
              font: {
                sz: 12,
              },
              alignment: {
                horizontal: "center", // 水平（向左、向右、居中）
                vertical: "center", // 上下（向上、向下、居中）
                wrapText: false, // 设置单元格自动换行，目前仅对非合并单元格生效
                indent: 0, // 设置单元格缩进
              },
            };
          }
        }
      }
      XLSX.utils.book_append_sheet(workBook, workSheet, sheetName);
      const tmpDown = new Blob([
        this.s2ab(
          XLSX_STYLE.write(workBook, {
            bookType: "xlsx",
            bookSST: true,
            type: "binary",
            cellStyles: true,
          })
        ),
      ]);
      this.downloadExcelFile(tmpDown, fileName1);
    },
    getExportDataList(arr) {
      arr.forEach(item => {
        Object.keys(item).forEach(key => {
          if (item[key] === null || item[key] === undefined) {
            item[key] = "";
          }
        });
      });
      const thList1 = Object.keys(arr[0]);
      const keyList1 = Object.keys(arr[0]);
      const targetList1 = arr;
      const tdList = this.formatJson(keyList1, targetList1); // 过滤字段以及转换数据格式，即：表格数据
      tdList.unshift(thList1); // 将 thList 数组添加到 tdList 数组开头，即：表格头部
      const list = tdList;
      return list;
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(item => {
          return v[item];
        })
      );
    },
    s2ab(s) {
      if (typeof ArrayBuffer !== "undefined") {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i != s.length; ++i) {
          view[i] = s.charCodeAt(i) & 0xff;
        }
        return buf;
      } else {
        const buf = new Array(s.length);
        for (let i = 0; i != s.length; ++i) {
          buf[i] = s.charCodeAt(i) & 0xff;
        }
        return buf;
      }
    },
    downloadExcelFile(obj, fileName) {
      const a_node = document.createElement("a");
      a_node.download = fileName;
      if ("msSaveOrOpenBlob" in navigator) {
        window.navigator.msSaveOrOpenBlob(obj, fileName);
      } else {
        a_node.href = URL.createObjectURL(obj);
      }
      a_node.click();
      setTimeout(() => {
        URL.revokeObjectURL(obj);
      }, 2000);
    },
    searchReport() {
      let data = [];
      if (this.Factory && !this.form.reportName) {
        this.copyTableData.forEach(ite => {
          if (ite.captions == this.Factory) {
            data.push(ite);
          }
        });
      } else if (!this.Factory && this.form.reportName) {
        this.copyTableData.forEach(ite => {
          ite.child.forEach(item => {
            item.child.forEach(val => {
              data.push(val);
            });
          });
        });
      } else if (this.Factory && this.form.reportName) {
        let data1 = [];
        this.copyTableData.forEach(ite => {
          if (ite.captions == this.Factory) {
            data1.push(ite);
          }
        });
        data1.forEach(ite => {
          ite.child.forEach(item => {
            item.child.forEach(val => {
              data.push(val);
            });
          });
        });
      }
      this.tableData = data.filter(item => {
        return item.captions.indexOf(this.form.reportName) != -1;
      });
      this.addPropertyToTree1(this.tableData, "level1");
    },
    onReset() {
      this.form.reportName = "";
      this.reportList();
    },
    addReport() {
      this.addreportModal = true;
      this.modalType = "add";
      this.addReportForm = {
        reportName: "",
        sqlcontent: "",
        sqlType: "1",
        reportType: "",
      };
      (this.empowerFactory = []), (this.addTableData = []);
      this.selectRoles = [];
    },
    getVariable() {
      let params = {
        sql: this.addReportForm.sqlcontent,
        sqltype: Number(this.addReportForm.sqlType),
      };
      this.addTableData = [];
      if (!this.addReportForm.sqlcontent || !this.addReportForm.sqlType) {
        this.$message.warning("请先填写参数");
        return;
      }
      addReportGetVariable(params).then(res => {
        if (res.code) {
          res.data.forEach(item => {
            this.addTableData.push({
              name: item,
              type: "StrDtp",
              captions: "",
              parmsDef: "",
              cmbItems: "",
            });
          });
        }
      });
    },
    changeTextarea(val) {
      this.addReportForm.sqlcontent = val;
    },
    getRolesList() {
      getAssignableRoles().then(res => {
        this.roleList = res.items;
      });
    },
    addHandleOk() {
      this.confirmLoading = true;
      let params = {
        rePortName: this.addReportForm.reportName,
        sqlType: Number(this.addReportForm.sqlType),
        sqlStr_: this.addReportForm.sqlcontent,
        reportSql4ParDtos: this.addTableData,
        roles: this.selectRoles,
        empowerFactory: this.empowerFactory.join(","),
        reportType: this.addReportForm.reportType ? this.addReportForm.reportType : null,
      };
      if (this.modalType == "add") {
        addReport(params)
          .then(res => {
            if (!res.code) {
              this.$message.error(res.message);
            } else {
              this.$message.success("添加成功");
              this.reportList();
            }
          })
          .finally(() => {
            this.confirmLoading = false;
            this.addreportModal = false;
          });
      } else {
        params["id"] = this.reportId;
        editReport(params)
          .then(res => {
            if (res.code) {
              this.$message.success("修改成功");
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.confirmLoading = false;
            this.addreportModal = false;
          });
      }
    },
    editReport(record) {
      this.modalType = "edit";
      this.reportId = record.id;
      editReportParams(record.id).then(res => {
        this.addreportModal = true;
        if (res.code) {
          this.addReportForm.reportName = res.data.rePortName;
          this.addReportForm.sqlType = res.data.sqlType + "";
          this.addReportForm.sqlcontent = res.data.sqlStr_;
          this.addTableData = res.data.reportSql4ParDtos || [];
          this.selectRoles = res.data.roles || [];
          this.empowerFactory = res.data.empowerFactory ? res.data.empowerFactory.split(",") : [];
          this.addReportForm.reportType = res.data.reportType;
        }
      });
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.x-spreadsheet-overlayer {
  width: 100% !important;
  height: 100% !important;
}
/deep/.x-spreadsheet-sheet {
  width: 100% !important;
  height: 100% !important;
}
/deep/.x-spreadsheet-overlayer-content {
  width: 100% !important;
  height: 100% !important;
}
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
.preview {
  margin-left: 20px;
  height: 748px;
  &::-webkit-scrollbar {
    width: 8px; //y轴滚动条粗细
    height: 10px; //x轴滚动条粗细
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #8b8b8a;
  }
  td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 5px 8px;
    max-width: 300px; /* 设置一个最大宽度，根据需要调整 */
    border-right: 2px solid black;
    border-bottom: 2px solid black;
  }
  // tr:nth-child(2n) {
  //    background: #f0efef;
  // }
}

.form1 {
  margin-left: 10px;
  height: 49px;
  /deep/.ant-select {
    width: 120px;
  }
}
.ant-select-dropdown-menu-item {
  padding: 5px 0 !important;
  text-align: center;
}
</style>
<style scoped lang="less">
/deep/.ant-form-inline .ant-form-item {
  display: inline-block;
  margin-right: 16px;
  margin-bottom: 0;
  margin-top: 4px;
}
/deep/.ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-select-selection-selected-value {
  color: #000000;
}
/deep/.ant-checkbox-wrapper {
  color: #000000;
}
/deep/.ant-table-column-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}

/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.bto {
  height: 47px;
  border: 2px solid #e9e9f0;
  border-top: none;
}

.box_left {
  width: 100%;
  z-index: 10;
}
/deep/.mintable {
  .ant-table-body {
    min-height: 712px;
    border-right: 1px solid #e9e9f0;
    border-bottom: 4px solid #e9e9f0;
  }
  .ant-table-pagination.ant-pagination {
    margin: 4px 0;
    z-index: 99;
    position: absolute;
    bottom: -45px;
    margin-left: 1%;
  }
}
.reportList {
  min-width: 1670px;
  // height:808px;
  background: #ffffff;
  .content {
    min-width: 1670px;
    border-left: 1px solid #e9e9f0;
    height: 764px;
    background: #ffffff;
    display: flex;
    /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: #dfdcdc;
    }
    /deep/.ant-table-fixed-header .ant-table-scroll .ant-table-header {
      border-right: 1px solid #e9e9f0;
    }
    /deep/.ant-table-bordered.ant-table-fixed-header
      .ant-table-scroll
      .ant-table-header.ant-table-hide-scrollbar
      .ant-table-thead
      > tr:only-child
      > th:last-child {
      border-right: 1px solid #e9e9f0;
    }
    /deep/.ant-table-bordered .ant-table-thead > tr > th {
      border-right: 1px solid #e9e9f0;
    }
  }
}
/deep/.ant-table-tbody > tr > td {
  padding: 7px 8px;
  overflow-wrap: break-word;
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 8px;
}
.sqlSelect {
  /deep/ .ant-select-selection {
    border: 0 !important;
    border-color: #cccccc;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
    &:hover {
      border-color: #cccccc;
    }
    &:focus {
      border-color: #cccccc;
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
    }
    &:active {
      border-color: #cccccc;
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
    }
    .ant-select-selection__rendered {
      margin: 0;
      .ant-select-selection-selected-value {
        width: 100%;
        text-align: right;
        &::after {
          content: ":";
          position: relative;
          top: -0.5px;
          margin: 0 8px 0 2px;
        }
      }
    }

    .ant-select-arrow {
      display: none;
    }
  }
}
.addReportBox {
  display: flex;
  /deep/ .ant-form-item {
    margin: 0;
  }
  .addReportleft {
    width: 45%;
  }
  .addReportRight {
    flex: 1;
    display: flex;
    .btn {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 30px;
      font-size: 16px;
      font-weight: 500;
      white-space: normal;
      text-align: center;
      background: #f4f7fb;
      color: #ff9900;
      box-shadow: 0 6px 20px rgb(0 0 0 / 20%);
      border: 2px solid #e9e9f0;
      margin: 0 10px;
      cursor: pointer;
    }
    .addContent {
      flex: 1;
      .minClass {
        /deep/ .ant-table {
          .ant-table-body {
            min-height: 168px;
          }
        }
      }

      /deep/ .ant-table {
        tr th {
          padding: 5px 0;
        }
        tr td {
          padding: 0;
          color: #000000;
        }
      }
      .ant-select {
        width: 100%;
      }
    }
  }
}
</style>
