<!-- 物料管理-型号录入-按钮 -->
<template>
  <div class="active">
    <div class="box" v-if="checkPermission('MES.MaterialModule.Type.TypeAdd')">
      <a-button type="primary" @click="addCategory"> 型号录入 </a-button>
    </div>

    <div class="box" v-if="checkPermission('MES.MaterialModule.Type.VendorNoAdd')">
      <a-button type="primary" @click="pcbSetfile"> 厂商录入 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.MaterialModule.Type.VendorNoSearch')">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";

export default {
  name: "Action",
  data() {
    return {};
  },
  methods: {
    checkPermission,
    //查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 厂商建档
    pcbSetfile() {
      this.$emit("pcbSetfile");
    },
    // 添加类别
    addCategory() {
      this.$emit("addCategory");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  float: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    width: 80px;
    margin-top: 7px;
    margin-right: 15px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .ant-btn-primary:hover {
      background-color: #ffb029 !important;
      border-color: #ffb029 !important;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        // background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
