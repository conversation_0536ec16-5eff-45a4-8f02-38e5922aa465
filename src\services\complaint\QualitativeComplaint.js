import { request, METHOD } from '@/utils/request';
// 客诉定性订单列表
export function custcomplaintspagelist (params) {
    return request("/api/app/cust-complaints/get-page-list", METHOD.POST,params)
}
//取单
export function getwaitverdictorder (id) {
    return request(`/api/app/cust-complaints/${id}/get-wait-verdict-order`, METHOD.POST)
}
//个人定性
export function complaintsqualitative (params) {
    return request(`/api/app/cust-complaints/qualitative`, METHOD.POST,params)
}
//客诉定性发送
export function qualitativeasyncv2 (id) {
    return request(`/api/app/cust-complaints/${id}/qualitative-async-v2`, METHOD.POST)
}
// 工厂订单列表
export function factorycomplaintspagelist (params) {
    return request("/api/app/factory-complaints/page-list", METHOD.GET,params)
}
//上传客诉相关文件
export function uploadcomplaintsfile (params) {
    return request("/api/app/cust-complaints/up-load-complaints-file", METHOD.POST,params)
}

//客户查询接口
export function custbyid (id) {
    return request(`/api/app/cust-complaints/${id}/by-id`, METHOD.GET)
}
//工厂查询接口
export function factorybyid (id) {
    return request(`/api/app/factory-complaints/${id}/by-id`, METHOD.GET)
}
//新增投诉工厂
export function complainfactory (params) {
    return request("/api/app/cust-complaints/complain-factory", METHOD.POST,params)
}
//客诉初审-厂商初审
export function factoryfirstreview (params) {
    return request("/api/app/cust-complaints/factory-first-review", METHOD.POST,params)
}
//厂商复核
export function factoryresultreview (params) {
    return request("/api/app/cust-complaints/factory-result-review", METHOD.POST,params)
}
//平台投诉工厂已回复评价
export function platformcheck (params) {
    return request("/api/app/cust-complaints/platform-check", METHOD.POST,params)
}
//初审发送
export function factoryfirstreviewsend (id) {
    return request(`/api/app/cust-complaints/${id}/factory-first-review-send`, METHOD.POST)
}
//复核发送
export function factoryresultreviewsend (id) {
    return request(`/api/app/cust-complaints/${id}/factory-result-review-send`, METHOD.POST)
}
//投诉工厂待回访沟通记录 
export function complaintsplatformfinish (params) {
    return request(`/api/app/cust-complaints/platform-finish`, METHOD.POST,params)
}
//客户待回访沟通记录
export function complaintscustfinish (params) {
    return request(`/api/app/cust-complaints/cust-finish`, METHOD.POST,params)
}
//设为默认 
export function factorycomplaintsaddress (params) {
    return request(`/api/app/factory-complaints-address`, METHOD.POST,params)
}
//获取默认地址下拉 
export function complaintsaddress (TradeType,JoinFactoryId) {
    return request(`/api/app/factory-complaints-address?TradeType=${TradeType}&JoinFactoryId=${JoinFactoryId}`, METHOD.GET)
}
//删除收货地址
export function deletebyid(id) {
    return request(`/api/app/factory-complaints-address/${id}/delete-by-id`, METHOD.POST)
}