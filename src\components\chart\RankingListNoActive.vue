<template>
  <div class="rank">
    <h4 class="title">{{title}}</h4>
    <!-- <a href="#" class="showMoreClick" v-if="this.list.length > 12" @click="showMoreClick">
      <a-icon :type="showFlag ? 'down' : 'up'" />
      {{showFlag ? "收起全部" : "显示全部"}}
    </a> -->
    <ul class="list">
      <li :key="index" v-for="(item, index) in list">
        <span >{{index + 1}}</span>
        <span style="display:inline-block;width:80px">{{item.stage}}</span>
        <span >{{item.orderNo}}</span>
        <!-- <span >{{item.producer}}</span> -->
        <span >{{item.dispatchTime}}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'RankingListNoActive',
  props: ['title', 'list'],
  data () {
    return {
      filterList: this.list,
      showFlag: false,
    }
  },
  computed: {
    listSplice () {
      if (this.showFlag) {
        return this.filterList;
      } else {
        if(this.filterList.length > 12) {
          return this.filterList.slice(0, 13);
        } else {
          return this.filterList;
        }
      }
    }
  },
  methods:{
    showMoreClick () {
      this.showFlag = !this.showFlag
    }
  }
}
</script>

<style lang="less" scoped>
  .rank{
    padding: 0 32px 32px 32px;
    position: relative;
    .title{
      color: #000000;
      font-size: 14px;
      line-height: 22px;
      font-weight: 600;
    }
    .showMoreClick {
      position: absolute;
      right: 5px;
      top: 5px;
      text-decoration: revert;
    }
    .list{
      margin: 25px 0 0;
      padding: 0;
      list-style: none;
      li {
        margin-top: 23px;
        span {
          // color: @text-color-second;
          color:#000000;
          font-size: 14px;
          line-height: 22px;
        }
        span:first-child {
          background-color: @layout-bg-color;
          border-radius: 20px;
          display: inline-block;
          font-size: 12px;
          font-weight: 600;
          margin-right: 24px;
          height: 20px;
          line-height: 20px;
          width: 20px;
          text-align: center;
        }
        span.active {
          // background-color: #314659 !important;
          color: @text-color-inverse !important;
          position: relative;
        }
        .active0::after{
              width: 32px;
              height: 36px;
              content: '';
              left: -10px;
              top: -14px;
              display: inline-block;
              vertical-align: top;
              position: absolute;
              background:url("../../assets/icon/1.png") no-repeat center center
        }
        .active1::after{
              width: 32px;
              height: 36px;
              content: '';
              left: -10px;
              top: -14px;
              display: inline-block;
              vertical-align: top;
              position: absolute;
              background:url("../../assets/icon/2.png") no-repeat center center
        }
        .active2::after{
              width: 32px;
              height: 36px;
              content: '';
              left: -10px;
              top: -14px;
              display: inline-block;
              vertical-align: top;
              position: absolute;
              background:url("../../assets/icon/3.png") no-repeat center center
        }
        span:last-child {
          float: right;
        }
      }
    }
  }
</style>
