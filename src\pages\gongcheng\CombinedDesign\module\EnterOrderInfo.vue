<!-- 工程管理 - 合拼设计 - 编写参数 -->
<template>
  <a-form  :form="enterOrderForm">
    <a-row>
      <a-col :span="12" >
        <a-form-item label="外层线宽线距" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.minLineWS' />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="内层线宽线距" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.innerMinLineWS' />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="最小阻焊桥" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.minSolderBridge'  />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="PNL数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.pnlQty_' />
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span="12">
        <a-form-item label="总孔数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.viasCount'  />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="槽孔数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.slotHoleCount' />
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span="12">
        <a-form-item label="最小孔" :label-col="{ span: 12}" :wrapper-col="{ span: 12 }">
          <a-input  v-model='enterOrderForm.vias'/>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="阻焊层" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.flCountSolder' />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="沉金面积" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input  v-model='enterOrderForm.goldPlateAreaScale'>
          </a-input>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="靶距" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input  v-model='enterOrderForm.range'>
          </a-input>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12" >
        <a-form-item label="电镀面积GTL" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.platingAreaGTL' />
        </a-form-item>
      </a-col>
      <a-col :span="12" >
        <a-form-item label="电镀面积GBL" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.platingAreaGBL' />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12" >
        <a-form-item label="字符层" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-input   v-model='enterOrderForm.flCountFont' />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="测试" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-select
              show-search
              v-model='enterOrderForm.testType'
          >
            <a-select-option v-for="(item,index) in ClassList" :key="index" :value="item.caption_">
              {{item.caption_}}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="顶层阻焊" :label-col="{ span: 12 }" :wrapper-col="{ span: 12}">
          <a-checkbox  v-model='enterOrderForm.solderGTS'>
          </a-checkbox>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="底层阻焊" :label-col="{ span: 12 }" :wrapper-col="{ span: 12}">
          <a-checkbox  v-model='enterOrderForm.solderGBS'>
          </a-checkbox>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="顶层字符" :label-col="{ span: 12 }" :wrapper-col="{ span:12 }">
          <a-checkbox  v-model='enterOrderForm.fontGTS'>
          </a-checkbox>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="底层字符" :label-col="{ span: 12 }" :wrapper-col="{ span:12 }">
          <a-checkbox  v-model='enterOrderForm.fontGBS'>
          </a-checkbox>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="锣电镀孔" :label-col="{ span: 12 }" :wrapper-col="{ span: 12}">
          <a-checkbox  v-model='enterOrderForm.gongElectroplatedHole'>
          </a-checkbox>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="有铜台阶" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-checkbox  v-model='enterOrderForm.drillFloor'>
          </a-checkbox>
        </a-form-item>
      </a-col>
    </a-row>


    <a-row>
      <a-col :span="12">
        <a-form-item label="先V割后成型" :label-col="{ span: 12 }" :wrapper-col="{ span: 12}">
          <a-checkbox  v-model='enterOrderForm.vCutToWX'>
          </a-checkbox>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="先喷锡后文字" :label-col="{ span: 12 }" :wrapper-col="{ span: 12}">
          <a-checkbox  v-model='enterOrderForm.bmclToWZ'>
          </a-checkbox>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="油块大于20*20mm" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
          <a-checkbox  v-model='enterOrderForm.oilBlockGreaterThan20_20'>
          </a-checkbox>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="只能正片" :label-col="{ span: 12}" :wrapper-col="{ span: 12}">
          <a-checkbox  v-model='enterOrderForm.positiveCraft'>
          </a-checkbox>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>

</template>

<script>

import {paraGet} from "@/services/Combinate";

export default {
    name:'EnterOrderInfo',
    props:['ClassList','selectRow1','editData'],
  created() {
    console.log('editData',this.editData)
    // == '1'? true:false
    this.$nextTick(function () {
      this.enterOrderForm.fontGBS = this.editData.fontGBS == '1'? true:false;
      this.enterOrderForm.fontGTS = this.editData.fontGTS == '1'? true:false;
      this.enterOrderForm.solderGBS = this.editData.solderGBS == '1'? true:false;
      this.enterOrderForm.solderGTS = this.editData.solderGTS == '1'? true:false;
      this.enterOrderForm.gongElectroplatedHole = this.editData.gongElectroplatedHole == '1'? true:false;
      this.enterOrderForm.vCutToWX = this.editData.vCutToWX == '1'? true:false;
      this.enterOrderForm.bmclToWZ = this.editData.bmclToWZ == '1'? true:false;
      this.enterOrderForm.oilBlockGreaterThan20_20 = this.editData.oilBlockGreaterThan20_20 == '1'? true:false;
      this.enterOrderForm.positiveCraft = this.editData.positiveCraft == '1'? true:false;
      this.enterOrderForm.drillFloor = this.editData.drillFloor == '1'? true:false;
      this.enterOrderForm.pnlQty_ = this.selectRow1.pnlQty_
      this.enterOrderForm.innerMinLineWS = this.editData.innerMinLineWS ;
      this.enterOrderForm.minLineWS = this.editData.minLineWS ;
      this.enterOrderForm.flCountSolder = this.editData.flCountSolder;
      this.enterOrderForm.slotHoleCount = this.editData.slotHoleCount ;
      this.enterOrderForm.flCountFont = this.editData.flCountFont ;
      this.enterOrderForm.range = this.editData.range;
      this.enterOrderForm.testType = this.editData.testType;
      this.enterOrderForm.vias = this.editData.vias;
      this.enterOrderForm.viasCount = this.editData.viasCount;
      this.enterOrderForm.minSolderBridge = this.editData.minSolderBridge;
      this.enterOrderForm.goldPlateAreaScale = this.editData.goldPlateAreaScale;
      this.enterOrderForm.platingAreaGBL = this.editData.platingAreaGBL;
      this.enterOrderForm.platingAreaGTL = this.editData.platingAreaGTL;
      console.log('this.enterOrderForm',this.enterOrderForm)
    });
  },
  data() {
    return {
      factoryList: [],
      autoFocus:true,
      enterOrderForm:{
        "innerMinLineWS": "", // 内层线宽线距
        "minLineWS": "", // 外层线宽线距
        "fontGBS": false,  // 底层字符
        "fontGTS":false,  // 顶层字符
        "solderGBS": false,  // 底层阻焊
        "solderGTS": false,  // 顶层阻焊
        "flCountSolder": "",  // 阻焊层
        "viasCount": "",  // 总孔数
        "flCountFont": "",  // 字符层
        "platingAreaGBL": "",  // 电镀面积GBL
        "platingAreaGTL": "",  // 电镀面积GTL
        "gongElectroplatedHole": false,  // 锣电镀孔
        "range": "",     // 靶距
        "minSolderBridge": "",  // 最小阻焊桥
        "vCutToWX": false,  // 先V割后成型
        "vias": "",  // 最小孔
        "testType": "",  // 测试
        "bmclToWZ": false,  // 先喷锡后文字
        "oilBlockGreaterThan20_20": false,  // 油块
        "positiveCraft": false,  // 只能正片
        "drillFloor": false,  // 有铜台阶孔
        "pnlQty_": "",  // Pnl数
        "goldPlateAreaScale": "",  // 沉金面积
        "slotHoleCount": "",  // 槽孔数
      },
    };
  },

  methods: {

  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
  mounted () {
  }
}
</script>
<style scoped>
.picker{
  width:226px;
}
.ant-form-item{
  margin-bottom: 0;
}
.ant-form-item-children{
    width:180px;
    border-right: 0;
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
.ant-upload{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.avatar-uploader > .ant-upload {
  position:absolute;
  height: 32px;
}
.ant-upload-select-picture-card i {
  font-size: 12px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {

  color: #666;
}


</style>
