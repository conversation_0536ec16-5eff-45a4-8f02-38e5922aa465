<!-- 工程管理 - ECN管理 -按钮-->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnAdd')"
      :class="checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnAdd') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="newlyadded"> 新增 </a-button>
    </div>
    <div
      class="box"
      v-if="editflag && checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnRepait')"
      :class="editflag && checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnRepait') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="compile"> 编辑 </a-button>
    </div>
    <div
      class="box"
      v-if="!editflag && checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnCancel')"
      :class="!editflag && checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnCancel') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="cancelcompile"> 取消编辑 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnSave')"
      :class="checkPermission('MES.EngineeringModule.EngineeringEcn.EngineeringEcnSave') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="preserve"> 保存 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.EngineeringEcn.EcnOrderDelete')"
      :class="checkPermission('MES.EngineeringModule.EngineeringEcn.EcnOrderDelete') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="omission"> ECN删除 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.EngineeringEcn.EcnOrderVerify')"
      :class="checkPermission('MES.EngineeringModule.EngineeringEcn.EcnOrderVerify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="toexamine"> ECN审核 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.EngineeringEcn.EcnOrderBackVerify')"
      :class="checkPermission('MES.EngineeringModule.EngineeringEcn.EcnOrderBackVerify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="deapproval"> ECN反审核 </a-button>
    </div>
    <!-- <div class="box showClass" >
        <a-button type="primary" @click="onlineecn">
          在线ECN
          </a-button>
      </div> -->
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";

export default {
  name: "",
  props: {
    editflag: {
      type: Boolean,
    },
    ttype: {
      type: String,
    },
  },
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    //反审核
    deapproval() {
      this.$emit("deapproval");
    },
    //ECN审核
    toexamine() {
      this.$emit("toexamine");
    },
    //删除
    omission() {
      this.$emit("omission");
    },
    //在线ECN
    onlineecn() {
      this.$emit("onlineecn");
    },
    //取消编辑
    cancelcompile() {
      this.$emit("cancelcompile");
    },
    //保存
    preserve() {
      this.$emit("preserve");
    },
    //编辑
    compile() {
      this.$emit("compile");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    //新增
    newlyadded() {
      this.$emit("newlyadded");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 100px;
    margin-top: 4px;
    text-align: center;

    .ant-btn {
      width: 87px;
      padding: 0;
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
