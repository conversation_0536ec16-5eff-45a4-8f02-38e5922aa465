<!-- 市场管理 - 价格体系 - 价格体系明细 右边列表-->
<template>
  <div style="overflow-y: auto">
    <a-table
      style="word-break: break-all"
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ y: 684, x: 700 }"
      :pagination="false"
      :rowKey="rowKey"
      :customRow="onClickRow"
      :rowClassName="isRedRow"
      :loading="TableLoading2"
    >
      <template slot="isReview_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isReview_ == true ? true : false" />
        </span>
      </template>
      <template slot="isShow_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isShow_ == true ? true : false" />
        </span>
      </template>
      <template slot="isSamplePrice_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isSamplePrice_ == true ? true : false" />
        </span>
      </template>
      <template slot="isSquareMeterPrice_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isSquareMeterPrice_ == true ? true : false" />
        </span>
      </template>
      <template slot="isDetailEdit_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isDetailEdit_ == true ? true : false" />
        </span>
      </template>
      <template slot="isCollectEdit_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isCollectEdit_ == true ? true : false" />
        </span>
      </template>
      <template slot="isActualEdit_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isActualEdit_ == true ? true : false" />
        </span>
      </template>
      <template slot="isChooseEdit_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isChooseEdit_ == true ? true : false" />
        </span>
      </template>
      <template slot="isCustNo_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isCustNo_ == true ? true : false" />
        </span>
      </template>
      <template slot="isOtherPrice_" slot-scope="text, record">
        <span>
          <a-checkbox :checked="record.isOtherPrice_ == true ? true : false" />
        </span>
      </template>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import RightCopy from "@/pages/RightCopy";
export default {
  components: {
    RightCopy,
  },
  name: "OrderDetail",
  props: {
    columns: {
      type: Array,
      require: true,
    },
    dataSource: {
      type: Array,
      require: true,
    },
    TableLoading2: {
      type: Boolean,
      require: true,
    },
    rowKey: {
      type: String,
      require: true,
    },
  },
  data() {
    return {
      selectedRowsData: [],
      id: "",
    };
  },

  methods: {
    checkPermission,
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.selectedRowsData = record;
            this.$emit("selectRow", record);
            this.id = record.id;
          },
          contextmenu: e => {
            e.preventDefault();
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.id && record.id == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .ant-table {
  .rowSty {
    td {
      color: #dc143c;
    }
  }
  .rowSty1 {
    td {
      color: #2828ff;
    }
  }
  .rowBackgroundColor {
    background: #aba5a5;
  }
}
</style>
