import { request, METHOD } from "@/utils/request";
// 待收货列表(orderno)
export async function getPageList(params) {
  return request("/api/app/e-mSTPub-combinate-erp-info", METHOD.GET, params);
}
// ERP导入
export async function Receiving(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/erp-combine-data?tradetype=${params}`, METHOD.POST);
}
// 获取订单池订单
export async function hpPoolOrderInfo(params) {
  return request("/api/app/e-mSTPub-combinate-erp-info/h-pPool-order-info", METHOD.GET, params);
}
// 获取已数据合拼的订单
export async function hpINGOrderInfo(params) {
  return request("/api/app/e-mSTPub-combinate-erp-info/h-pINGOrder-info", METHOD.GET, params);
}
// 获取合拼订单子板明细
export async function hpINGDetailInfo(id,params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/h-pINGDetail-info?number=${params}&PGuid_=${id}`, METHOD.GET);
}
//激活
export function activation(Id, Active) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/set-active/${Id}?Active=${Active}`, METHOD.POST);
}
//改数
export function setChangeNum(Id, Num) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/set-change-num/${Id}?Num=${Num}`, METHOD.POST);
}
//更改pnl数
export function setchangepnlnum(Id, Num) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/set-change-pnl-num/${Id}?Num=${Num}`, METHOD.POST);
}
// 获取合拼工厂、总pnl数、总面积
export async function hpFac(params) {
  return request("/api/app/e-mSTPub-combinate-erp-info/h-pFac", METHOD.GET, params);
}
// 获取合拼信息 菲林工厂
export async function hpFLFac(params) {
  return request("/api/app/e-mSTPub-combinate-erp-info/h-pFLFac", METHOD.GET, params);
}
// 获取合拼配置
// export async function hpAppConfig(params) {
//     return request("/api/app/e-mSTPub-combinate-erp-info/h-pApp-config", METHOD.GET, params)
// }
export async function hpAppConfig(params) {
  return request("/api/app/e-mSTPub-combinate-erp-info/combin-par-config-list", METHOD.GET, params);
}
// 获取编写参数测试下拉选择
export async function getClassList(params) {
  return request("/api/app/e-mSData-class-list/get-class-list", METHOD.POST, params);
}
// 获取编写参数信息
export async function paraGet(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/para-get?guid=${params}`, METHOD.POST);
}
// 保存编写参数
export async function paraSet(Id, params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/para-set/${Id}`, METHOD.POST, params);
}
// 单板下线
export async function sigFinish(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/sig-finish`, METHOD.POST, params);
}
// 单板回退
export async function sigBack(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/sig-back/${Id}`, METHOD.POST);
}
// 合拼订单
export async function hpPoolHand(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/wait-hPTo-hp-pool-hand`, METHOD.POST, params);
}
// 回退待合拼
export async function back2WaitHP(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/back2Wait-hP`, METHOD.POST, params);
}
// 更新菲林工厂参数
export async function upHPFLFac(Id, params) {
  return request(`api/app/e-mSTPub-combinate-erp-info/up-hPFLFac/${Id}?IsSent=${params}`, METHOD.POST);
}
// 跳转叠层阻抗
export async function getImp(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/imp/${Id}`, METHOD.GET);
}
// 跳转拼板开料
export async function autoToolPnl(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/auto-tool-pnl/${Id}`, METHOD.GET);
}
// 回退合拼
export async function combineBack(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/combine-back`, METHOD.POST, params);
}
//合拼完成
export async function pinbanfinished(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/pin-ban-finished/${Id}`, METHOD.POST);
}
// 作废合拼
export async function deleteHP(Id, params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/delete-hP/${Id}?Pdctno=${params}`, METHOD.POST);
}
// 自动流程
export async function autoProduce(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/auto-produce-flow/${Id}`, METHOD.POST);
}
// 获取工厂
export async function getFactory() {
  return request(`/api/app/e-mSTPub-combinate-erp-info/get-factory`, METHOD.GET);
}
// 流程备注
export async function flowReMarks(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/flow-re-marks?guid=${Id}`, METHOD.POST);
}
// 更新流程备注
export async function upFlowReMarks(Id, params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/up-flow-re-marks?guid=${Id}`, METHOD.POST, params);
}
// 删除数据
export async function deleteData(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/delete-data`, METHOD.POST, params);
}
// 手动导入
export async function importErpDataM(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/import-erp-data-m`, METHOD.POST, params);
}
// 订单清理
export async function setwaithPstate(Guild) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/set-wait-hPstate?Guild=${Guild}`, METHOD.POST);
}
// 清除数据
export async function clearSize(params) {
  return request(`api/app/e-mSTPub-combinate-erp-info/clear-size`, METHOD.POST, params);
}
// 测试数据
export async function TestGroupList() {
  return request(`/api/app/e-mSTPub-combinate-erp-info/group-list`, METHOD.GET);
}
// 订单合拼
export async function hpPoolHand1(params) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/execution-order`, METHOD.POST, params);
}
// 生成结构
export async function generatingStructure(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/set-mMKBAll-data/${Id}`, METHOD.POST);
}
//修改工厂下拉
export async function getfactory(id, type) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/get-factory/${id}?type=${type}`, METHOD.GET);
}
//修改工厂
export async function combinateerpinfoupfactory(id, type, fac) {
  return request(`api/app/e-mSTPub-combinate-erp-info/up-factory/${id}?type=${type}&Factory=${fac}`, METHOD.POST);
}
//通过合拼订单ID获取订单信息
export async function waithPOrderid(id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/${id}/wait-hPOrder-id`, METHOD.POST);
}
// 自动流程
export async function autoMations(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/auto-produce-flow/${Id}`, METHOD.POST);
}
// 工程指示
export async function engineeringInstructions(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/set-info-to-pro-order/${Id}`, METHOD.POST);
}

// 【普林】在图形/数据合拼时把型号写到ERP
export async function setOrderNoToErp(Id) {
  return request(`/api/app/e-mSTPub-combinate-erp-info/set-order-no-to-erp/${Id}`, METHOD.POST);
}
//合拼设计备注信息
export async function byorderno(joinFactoryId, params) {
  return request(`/api/app/e-mSTPpe-information-transfer/by-order-no/${joinFactoryId}`, METHOD.GET, params);
}
//合拼设计叠层信息
export async function stackupimpinfo(params) {
  return request(`/api/app/engineering-make-info/stack-up-imp-info`, METHOD.GET, params);
}
export default {
  getPageList,
  Receiving,
  hpPoolOrderInfo,
  hpINGOrderInfo,
  hpINGDetailInfo,
  hpFac,
  hpFLFac,
  hpAppConfig,
  getClassList,
  paraGet,
  paraSet,
  sigFinish,
  sigBack,
  hpPoolHand,
  hpPoolHand1,
  back2WaitHP,
  upHPFLFac,
  getImp,
  autoToolPnl,
  combineBack,
  pinbanfinished,
  deleteHP,
  autoProduce,
  getFactory,
  flowReMarks,
  upFlowReMarks,
  deleteData,
  importErpDataM,
  clearSize,
  TestGroupList,
  setChangeNum,
  setOrderNoToErp,
  byorderno,
};
