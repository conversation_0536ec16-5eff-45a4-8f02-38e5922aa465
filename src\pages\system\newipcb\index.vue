<!-- 系统管理-新iPCB任务监控 -->
<template>
  <a-spin :spinning="spinning">
    <div class="main" style="position: relative" ref="SelectBox">
      <div class="Content" style="position: relative; bottom: 3px">
        <!-- <a-input  placeholder="GerberID" v-model="formData.GerberID" style="width:183px;margin-right:0.5%;margin-bottom: 0.4%;" allowClear @keyup.enter.native="searchClick"></a-input> -->
        <a-input
          placeholder="文件名"
          v-model="formData.SpecPath"
          style="width: 183px; margin-right: 0.5%"
          allowClear
          @keyup.enter.native="searchClick"
        ></a-input>
        <a-input
          placeholder="ID查询"
          v-model="formData.BizObjectID"
          style="width: 183px; margin-right: 0.5%"
          allowClear
          @keyup.enter.native="searchClick"
        ></a-input>
        <a-date-picker style="margin-right: 0.5%" format="YYYY-MM-DD " placeholder="提交时间(开始)" @change="onChange1" />
        <a-date-picker style="margin-right: 0.5%" format="YYYY-MM-DD " placeholder="提交时间(结束)" @change="onChange2" />
        <a-button type="primary" @click="searchClick" style="margin-right: 0.5%">搜索</a-button>
      </div>
      <div style="width: 100%; display: flex">
        <div class="leftContent">
          <a-table
            :columns="columns1"
            :pagination="false"
            :scroll="{ y: 740 }"
            rowKey="factoryCode"
            :dataSource="dataSource1"
            :class="dataSource1.length ? 'min-table' : ''"
          >
            <div slot="factoryCode" slot-scope="text, record" @contextmenu.prevent.stop="rightClick1($event, text)">
              <a :title="record.factoryCode" style="color: #000000">{{ record.factoryCode }}</a>
            </div>
            <div slot="counts" slot-scope="text, record" @contextmenu.prevent.stop="rightClick1($event, text)">
              <a :title="record.counts" style="color: #000000">{{ record.counts }}</a>
            </div>
          </a-table>
        </div>
        <div class="rightContent">
          <a-table
            v-if="pageshow"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="pagination"
            :loading="orderListTableLoading"
            :rowKey="'bizObjectID'"
            :scroll="{ x: 1346, y: 714 }"
            @change="handleTableChange"
            :class="dataSource.length ? 'min-table' : ''"
          >
            <template slot="labelUrl" slot-scope="record">
              <span style="color: #428bca; cursor: pointer" @click.stop="analyseFile(record)"> 文件</span>
              <a style="color: #428bca; opacity: 0.2"> | </a>
              <span style="color: #428bca; cursor: pointer" @click.stop="previewClick(record)"> 操作</span>
              <a style="color: #428bca; opacity: 0.2"> | </a>
              <a style="color: rgb(66, 139, 202); cursor: pointer" @click="click2(record)">详情</a>
              <a style="color: #428bca; opacity: 0.2"> | </a>
              <a style="color: rgb(66, 139, 202); cursor: pointer" @click="resetting(record)">重置</a>
            </template>
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <div slot="specPath" slot-scope="text, record" @contextmenu.prevent.stop="rightClick1($event, text)">
              <a
                :title="record.specPath"
                style="color: rgb(66, 139, 202); overflow: hidden; text-overflow: ellipsis; width: 223px; display: block"
                @click="click1(record)"
                >{{ record.specPath }}</a
              >
            </div>
            <span slot="bizObjectID" slot-scope="text, record" @contextmenu.prevent.stop="rightClick1($event, text)">
              <a :title="record.bizObjectID" style="color: #000000">{{ record.bizObjectID }}</a>
            </span>
            <div slot="name" slot-scope="text, record" @contextmenu.prevent.stop="rightClick1($event, text)">
              <a :title="record.name" style="color: #000000">{{ record.name }}</a>
            </div>
            <div slot="zndrWaitTime" slot-scope="text, record" @contextmenu.prevent.stop="rightClick1($event, text)">
              <a :title="record.zndrWaitTime" style="color: #000000">{{ record.zndrWaitTime }}</a>
            </div>
            <span slot="title_slot">
              智能导入等待时间<a-tooltip title="点击排序"><a-icon type="question-circle-o" /></a-tooltip>
            </span>
            <span slot="content_slot" slot-scope="text, record" style="cursor: pointer">
              {{ record.zndrWaitTime }}
            </span>
          </a-table>
          <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
            <a-menu-item @click="down">复制</a-menu-item>
          </a-menu>
        </div>
      </div>

      <!-- <div class="bto"></div> -->
      <a-modal
        :title="RowData.specPath + '日志信息'"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        centered
        destroyOnClose
        :maskClosable="false"
        :width="650"
        :confirmLoading="confirmLoading"
        class="viewLogInfo"
      >
        <template slot="footer">
          <a-button @click="reportHandleCancel">关闭</a-button>
        </template>
        <view-log-info ref="viewLogInfo" :viewLogData="viewLogData" />
      </a-modal>
    </div>
    <a-modal title="确认弹窗" :visible="confirmdatavisible" @ok="handleOk" @cancel="reportHandleCancel" centered :width="400">
      <span>{{ messagelist }}</span>
    </a-modal>
    <a-modal
      :title="RowData.specPath + ' --格式分析'"
      :visible="FileVisible"
      @cancel="FileVisible = false"
      centered
      :width="1000"
      destroyOnClose
      :maskClosable="false"
    >
      <a-table
        :columns="columnsfile"
        :dataSource="FileData"
        :expandIcon="expandIcon"
        rowKey="level"
        :pagination="false"
        :expandIconColumnIndex="0"
        :defaultExpandAllRows="false"
        :scroll="{ y: 600, x: 800 }"
      >
        <span slot="num" slot-scope="text, record">
          {{ record.level }}
        </span>
      </a-table>
      <template slot="footer">
        <a-button @click="FileVisible = false">关闭</a-button>
      </template>
    </a-modal>
  </a-spin>
</template>
<script>
import { graphicPreview2, graphicPreview3, ipCBTaskoutput } from "@/services/mkt/PrequalificationProduction.js";
import { taskMonitoringnewPageList, ipCBTask, monitoringListneweCount, settaskcznew } from "@/services/identity/ipcb";
import viewLogInfo from "@/pages/system/newipcb/module/viewLogInfo";
import moment from "moment";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    fixed: "left",
    scopedSlots: { customRender: "num" },
    width: 40,
  },
  {
    title: "名称",
    dataIndex: "name",
    align: "left",
    fixed: "left",
    width: 85,
    ellipsis: true,
    className: "userStyle",
    scopedSlots: { customRender: "name" },
  },
  {
    title: "文件名",
    dataIndex: "specPath",
    scopedSlots: { customRender: "specPath" },
    width: 225,
    ellipsis: true,
    align: "left",
    className: "userStyle",
  },
  {
    title: "格式分析等待",
    dataIndex: "formatAnayWaitTime",
    ellipsis: true,
    align: "left",
    width: 120,
    sorter: (a, b) => {
      return a.formatAnayWaitTime - b.formatAnayWaitTime;
    },
  },
  {
    title: "格式分析用时",
    dataIndex: "formatAnayTime",
    align: "left",
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.formatAnayTime - b.formatAnayTime;
    },
  },
  {
    title: "P2G等待",
    dataIndex: "gerberWaitTime",
    align: "left",
    //  // fixed:'left',
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.gerberWaitTime - b.gerberWaitTime;
    },
  },
  {
    title: "P2G用时",
    dataIndex: "gerberTime",
    align: "left",
    //  // fixed:'left',
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.gerberTime - b.gerberTime;
    },
  },
  {
    title: "智能导入等待",
    dataIndex: "zndrWaitTime",
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "zndrWaitTime" },
    width: 120,
    sorter: (a, b) => {
      return a.zndrWaitTime - b.zndrWaitTime;
    },
  },
  {
    title: "智能导入用时",
    dataIndex: "zndrTime",
    align: "left",
    //  // fixed:'left',
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.zndrTime - b.zndrTime;
    },
  },

  {
    title: "Gerber分析等待",
    align: "left",
    dataIndex: "gerberAnalysisWaitTime",
    // fixed:'left',
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.gerberAnalysisWaitTime - b.gerberAnalysisWaitTime;
    },
  },
  {
    title: "Gerber分析用时",
    dataIndex: "gerberAnalysisTime",
    align: "left",
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.gerberAnalysisTime - b.gerberAnalysisTime;
    },
  },
  {
    title: "OCR等待时间",
    align: "left",
    dataIndex: "ocrWaitTime",
    // fixed:'left',
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.ocrWaitTime - b.ocrWaitTime;
    },
  },
  {
    title: "OCR转换用时",
    dataIndex: "ocrTime",
    align: "left",
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.ocrTime - b.ocrTime;
    },
  },
  {
    title: "SPEC分析等待",
    dataIndex: "specWaitTime",
    align: "left",
    ellipsis: true,
    width: 120,
  },
  {
    title: "SPEC分析用时",
    dataIndex: "specTime",
    align: "left",
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.specTime - b.specTime;
    },
  },
  {
    title: "审核总用时",
    dataIndex: "auditTime",
    width: 120,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      return a.auditTime - b.auditTime;
    },
  },
  {
    title: "状态",
    width: 170,
    dataIndex: "satus",
    ellipsis: true,
    align: "center",
  },
  {
    title: "提交时间",
    dataIndex: "createtime",
    width: 150,
    ellipsis: true,
    align: "left",
  },
  {
    title: "ID",
    dataIndex: "bizObjectID",
    scopedSlots: { customRender: "bizObjectID" },
    width: 280,
    ellipsis: true,
    align: "left",
  },
  // {
  //   title: "详情",
  //   width: 45,
  //   ellipsis: true,
  //   fixed:'right',
  //   align: "left",
  //   scopedSlots:{customRender:'info'},
  // },
  {
    title: "操作",
    align: "center",
    fixed: "right",
    width: 180,
    scopedSlots: { customRender: "labelUrl" },
  },
  // {
  //   title: "pcb上传时间",
  //   dataIndex: "uploadTime",
  //   width: 120,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "转换开始时间",
  //   dataIndex: "conversionStartTime",
  //   width: 120,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "转换结束时间",
  //   dataIndex: "conversionEndTime",
  //   width:120,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "智能导入开始时间",
  //   dataIndex: "auditDateB",
  //   width:140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "智能导入结束时间",
  //   dataIndex: "gerberDateE",
  //   width: 140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "OCR建立时间",
  //   dataIndex: "ocrCreatedDate",
  //   width: 100,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "OCR开始时间",
  //   dataIndex: "ocrStartTime",
  //   width: 100,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "OCR结束时间",
  //   dataIndex: "ocrEndTime",
  //   width: 100,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Gerber分析任务创建时间",
  //   dataIndex: "gerberAnalysisCreateTime",
  //   width: 150,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Gerber分析开始时间",
  //   dataIndex: "gerberAnalysisStartTime",
  //   width: 140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Gerber分析结束时间",
  //   dataIndex: "gerberAnalysisEndTime",
  //   width: 140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "SPEC审核开始时间",
  //   dataIndex: "specDateB",
  //   width: 130,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "SPEC审核结束时间",
  //   dataIndex: "auditDateE",
  //   width: 130,
  //   ellipsis: true,
  //   align: "left",
  // },
];
const columns1 = [
  {
    title: "工厂",
    align: "left",
    width: 100,
    ellipsis: true,
    className: "userStyle",
    dataIndex: "factoryCode",
    scopedSlots: { customRender: "factoryCode" },
  },
  {
    title: "数量",
    align: "left",
    ellipsis: true,
    width: 80,
    className: "userStyle",
    dataIndex: "counts",
    scopedSlots: { customRender: "counts" },
  },
];
const columnsfile = [
  // {
  //   title: "序号",
  //   align: "left",
  //   width: 80,
  //   scopedSlots: { customRender: "num" },
  // },
  {
    title: "文件",
    dataIndex: "originalPath",
    ellipsis: true,
    align: "left",
    width: 800,
  },
  {
    title: "文件格式",
    dataIndex: "fileTypeStr",
    ellipsis: true,
    align: "left",
    width: 100,
  },
];
export default {
  name: "",
  components: { viewLogInfo },
  data() {
    return {
      menuVisible: false,
      confirmdatavisible: false,
      messagelist: "",
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      RowData: {},
      menuData: {},
      spinning: false,
      columns,
      columns1,
      columnsfile,
      dataSource: [],
      dataSource1: [],
      orderListTableLoading: false,
      pagination: {
        pageSize: 20,
        pageIndex: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      formData: {
        GerberID: "",
        SpecPath: "",
        BizObjectID: "",
        State: undefined,
        StartTime: "",
        EndTime: "",
      },
      FileData: {},
      FileVisible: false,
      pageshow: true,
      dataVisible: false,
      confirmLoading: false,
      viewLogData: [],
      queryData: {},
      jump: false,
      selectrowData: {},
    };
  },
  mounted() {
    this.getList1();
    this.getList();
  },
  watch: {},
  methods: {
    previewClick(record) {
      graphicPreview3(record.bizObjectID).then(res => {
        if (res.data) {
          window.open(res.data, "_blank");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    analyseFile(record) {
      this.RowData = record;
      let params = {
        BizObjectID: record.bizObjectID,
        TaskType: "file_format",
      };
      this.orderListTableLoading = true;
      ipCBTaskoutput(params)
        .then(res => {
          if (res.code && res.data) {
            this.FileVisible = true;
            this.FileData = this.organizeData(res.data);
            console.log(this.FileData, "this.FileData");
            this.addPropertyToTree(this.FileData, "level");
          } else {
            this.$message.error(res.message || "暂无格式分析数据可查看");
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    addPropertyToTree(tree, prop, parentLevel) {
      tree.forEach((node, index) => {
        node[prop] = parentLevel ? `${parentLevel}-${index + 1}` : `${index + 1}`;
        if (node.children) {
          this.addPropertyToTree(node.children, prop, node[prop]);
        }
      });
    },
    organizeData(rawData) {
      const result = [];
      const pathMap = {};
      // 首先整理数据，并根据fullPath建立映射
      rawData.forEach(item => {
        const { dirInfo, files } = item;
        const { fullPath } = dirInfo;
        // 创建当前目录的条目
        const dirEntry = {
          ...dirInfo,
          children: files.map(file => ({
            ...file.fileInfo,
            level: result.length + 1, // 计算级别
            fileType: file.fileType,
            fileTypeStr: file.fileTypeStr,
          })),
        };
        // 存储目录路径到pathMap中
        pathMap[fullPath] = dirEntry;
        // 将目录条目添加到结果集
        result.push(dirEntry);
      });

      // 通过fullPath匹配并构建父子层级关系
      result.forEach(dir => {
        const parentPath = dir.fullPath.replace(/\\[^\\]+$/, ""); // 去掉路径的最后一部分，得到父级路径
        if (parentPath && pathMap[parentPath]) {
          pathMap[parentPath].children = pathMap[parentPath].children || [];
          pathMap[parentPath].children.push(dir);
        }
      });
      // 最终的结果是每个目录的子项都应该嵌套在它的父项的children字段下
      return result.filter(dir => !pathMap[dir.fullPath.replace(/\\[^\\]+$/, "")]);
    },
    expandIcon(props) {
      if (props.record.children && props.record.children.length > 0 && props.record.children != "null") {
        if (props.expanded) {
          //有数据-展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder-open" style="margin-right:5px" />
            </a>
          );
        } else {
          //有数据-未展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder" style="margin-right:5px" />
            </a>
          );
        }
      } else {
        //无数据-图标
        return (
          <span style="margin-right:0px">
            <a-icon type=" " />
          </span>
        );
      }
    },
    handleTableChange(pagination, filters, sorter) {
      if (this.pagination.pageSize != pagination.pageSize) {
        this.jump = true;
        pagination.current = 1;
      } else {
        this.jump = false;
      }
      this.pagination.pageIndex = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      if (pagination.current != pagination.pageIndex && !this.jump) {
        if (JSON.stringify(this.queryData) != "{}") {
          this.getList(this.queryData);
        } else {
          this.getList();
        }
      }
      if (this.jump) {
        this.pageshow = false;
        if (JSON.stringify(this.queryData) != "{}") {
          this.getList(this.queryData);
        } else {
          this.getList();
        }
        let that = this;
        this.$nextTick(() => {
          that.pageshow = true;
        });
      }
    },
    rightClick1(e, text) {
      if (text || text == 0) {
        this.text = text;
        this.menuVisible = true;
        this.menuStyle.top = e.clientY - 110 + "px";
        this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
        document.body.addEventListener("click", this.bodyClick);
      }
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    down() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    getList1(params) {
      monitoringListneweCount(params).then(res => {
        if (res.code) {
          this.dataSource1 = res.data;
        } else {
          this.dataSource1 = [];
        }
      });
    },
    getList(queryData) {
      let params = {
        PageIndex: this.pagination.pageIndex,
        PageSize: this.pagination.pageSize,
      };
      let obj = {};
      if (queryData) {
        obj = Object.assign(params, queryData);
      } else {
        obj = params;
      }
      this.orderListTableLoading = true;
      taskMonitoringnewPageList(obj)
        .then(res => {
          if (res.code) {
            this.dataSource = res.data.items;
            this.pagination.total = res.data.totalCount;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    moment,
    onChange1(value, dateString) {
      this.formData.StartTime = dateString;
    },
    onChange2(value, dateString) {
      this.formData.EndTime = dateString;
    },
    searchClick() {
      let params = this.formData;
      var arr1 = params.GerberID.split("");
      if (arr1.length > 150) {
        arr1 = arr1.slice(0, 150);
      }
      params.GerberID = arr1.join("");

      var arr2 = params.SpecPath.split("");
      if (arr2.length > 150) {
        arr2 = arr2.slice(0, 150);
      }
      params.SpecPath = arr2.join("");

      var arr3 = params.BizObjectID.split("");
      if (arr3.length > 150) {
        arr3 = arr3.slice(0, 150);
      }
      params.BizObjectID = arr3.join("");
      this.queryData = params;
      this.pageshow = false;
      this.pagination.pageIndex = 1;
      this.getList(params);
      this.getList1(params);
      this.$nextTick(() => {
        this.pageshow = true;
      });
    },
    click1(record) {
      if (record.gerberPath) {
        let path = "http://sight.bninfo.com/Downloadsave.aspx?fileNameAbs=" + encodeURIComponent(record.gerberPath);
        // let a = record.gerberPath.split('.');
        // this.downloadByteArrayFromURL(path, record.bizObjectID + '.' + a[a.length - 1]);
        window.open(path, "_blank", "noreferrer");
      }
    },
    downloadByteArrayFromURL(url, fileName) {
      fetch(url)
        .then(response => response.arrayBuffer())
        .then(arrayBuffer => {
          const byteArray = new Uint8Array(arrayBuffer);
          const blob = new Blob([byteArray], { type: "application/octet-stream" });
          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = fileName;
          link.click();
          URL.revokeObjectURL(url);
        })
        .catch(error => {
          console.error("Error downloading file:", error);
        });
    },
    click2(record) {
      ipCBTask(record.bizObjectID).then(res => {
        if (res.code) {
          this.viewLogData = res.data;
          this.RowData = record;
          this.dataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    resetting(record) {
      this.selectrowData = record;
      this.messagelist = "确认重置吗?";
      this.confirmdatavisible = true;
    },
    reportHandleCancel() {
      this.dataVisible = false;
      this.confirmdatavisible = false;
    },
    handleOk() {
      settaskcznew(this.selectrowData.bizObjectID).then(res => {
        if (res.code) {
          this.$message.success("重置成功");
        } else {
          this.$message.error(res.message);
        }
      });
      this.confirmdatavisible = false;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .ant-table {
  border: 1px solid #efefef;
  .ant-table-thead > tr > th {
    padding: 6px 4px;
    border-right: 1px solid #efefef;
    height: 34.6px;
  }
  .ant-table-tbody > tr > td {
    padding: 6px 4px !important;
    border-right: 1px solid #efefef;
    position: relative;
    height: 34.6px;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: rgb(223 220 220);
}
.viewLogInfo {
  /deep/.ant-modal-body {
    max-height: 600px;
    overflow: auto;
  }
}
.tabRightClikBox {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-tag {
  font-size: 12px;
  font-weight: 500;
  color: #ff9900;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
}
.ant-table-row-cell-break-word {
  position: relative;
}
.span {
  position: absolute;
  top: 5%;
}
p {
  margin: 0;
}
.main {
  background: #ffffff;
  padding-top: 7px;
  min-width: 1670px;
  .content {
    height: 44px;
    padding-left: 6px;
    background: #ffffff;
    /deep/.ant-select-selection__placeholder {
      display: block;
    }
  }
  .ant-input,
  .ant-select {
    width: 8%;
    margin-right: 0.5%;
    margin-top: 6px;
  }
  /deep/.userStyle {
    user-select: none !important;
  }
  // height: 834px;
  width: 100%;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  /deep/.leftContent {
    .ant-table-selection-column {
      padding: 0 !important;
    }
    background: #ffffff;
    .min-table {
      .ant-table-body {
        min-height: 740px;
      }
    }
    height: 793px;
    width: 17%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }
  /deep/.rightContent {
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
    width: 83%;
    // max-width: 1346px;
    height: 793px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    //  overflow: auto;
    overflow: hidden;
    .min-table {
      width: 100%;
      .ant-table-body {
        min-height: 714px;
      }
    }
  }
  /deep/.ant-table-wrapper {
    width: 100%;
  }
  .bto {
    height: 60px;
    background: #ffffff;
    border: 2px solid #e9e9f0;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container {
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: rgb(223 220 220);
  }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  // /deep/ .ant-table-thead > tr > th {
  //   // padding:3px 0!important;
  //   .ant-table-column-sorter {
  //     display: none;
  //   }
  // }
  /deep/ .ant-table {
    .ant-table-tbody > tr > td {
      position: relative;
      .topCss {
        position: absolute;
        top: 3%;
      }
      .topCss1 {
        position: absolute;
        top: 3%;
        // left:30%;
      }
      .topCss2 {
        position: absolute;
        top: 3%;
        // left:25%;
      }
    }
    tr.ant-table-row-selected td {
      background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
      background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220) !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 2px 0 0 10px;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
  // background: #F0F2F5;
}
</style>
