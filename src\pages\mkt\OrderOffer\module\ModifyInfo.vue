<!-- 市场管理 - 订单报价- 返单更改 -->
<template>
  <div>
    <div>
      <a-card>
        <div
          style="
            text-align: center;
            background-color: #fafafa;
            width: 100%;
            border-top: 1px solid #ddd;
            border-right: 1px solid #ddd;
            border-left: 1px solid #ddd;
          "
        >
          类别信息
        </div>
        <a-form-model>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="本厂编号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="redata.proOrderNo" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="7">
              <a-form-model-item label="改后本厂编号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="redata.newProOrderNo" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="11">
              <a-form-model-item label="客户型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
                <div class="editWrapper">
                  <a-input v-model="redata.customerModel" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="更改前版本" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="redata.oldRev" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="7">
              <a-form-model-item label="更改后版本" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="redata.newRev" allowClear> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="11">
              <a-form-model-item label="文件上传" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
                <span style="display: flex">
                  <a-upload
                    accept=".zip,.rar"
                    :multiple="false"
                    :file-list="fileList"
                    :show-upload-list="false"
                    :customRequest="downloadFilesCustomRequest"
                    :before-upload="beforeUpload"
                    @change="handleChange1"
                  >
                    <a-button> <a-icon type="upload" />文件上传 </a-button>
                  </a-upload>
                  <!-- <span v-else style="font-size: 12px;line-height: 2;color: #000;"> {{ FileName }}</span>  -->
                </span>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="更改内容" :label-col="{ span: 2 }" :wrapper-col="{ span: 21 }" class="editWrapper1">
                <div>
                  <a-textarea v-model="redata.reMark" style="width: 100%; min-height: 24px; line-height: 14px; margin-bottom: 0px" />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-card>
    </div>
  </div>
</template>
<script>
import axios from "axios";
import { upLoadEnquiryFile, inquirycheckorderfile } from "@/services/mkt/Inquiry.js";
export default {
  props: {
    redata: {
      type: Object,
      required: false,
      default: () => {},
    },
  },

  data() {
    return {
      fileList: [],
      isFileType: false,
      FilePath: "",
      FileName: "",
    };
  },
  methods: {
    handleChange1({ fileList }, data) {
      if (this.isFileType) {
        this.fileList = fileList;
        if (this.fileList.length == 0) {
          this.FilePath = "";
        }
        if (this.fileList.length > 1) {
          // this.$message.error('文件上传只支持单个文件')
          this.fileList.splice(0, 1);
          return;
        }
      }
    },
    beforeUpload(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
      if (!this.isFileType) {
        this.$message.error("文件确认只支持.rar或.zip格式文件");
        return false;
      }
      const filesize = Number(file.size / 1024 / 1024) < 150;
      if (!filesize) {
        this.isFileType = false;
        this.$message.error("文件大小不能超过150M");
        return false;
      }
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    //分段上传MD5
    async downloadFilesCustomRequest(data) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("CustNo", this.redata.custNo);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      this.orderListTableLoading = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              this.FilePath = res.data.split(",")[0];
              this.redata.customerModel = replacedText;
            }
          } else {
            this.orderListTableLoading = false;
            data.onError(res.message);
            i = shardCount;
          }
        });
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-btn > .anticon + span,
.ant-btn > span + .anticon {
  font-size: 10px;
}
.ant-btn {
  height: 24px;
  padding: 0px 13.5px;
}
/deep/.ant-input {
  font-size: 12px;
}
.editWrapper1 {
  display: flex;
  /deep/.ant-form-item-label {
    width: 88px;
  }
  /deep/.ant-form-item-control-wrapper {
    width: 761px;
  }
  /deep/.ant-form-item-label {
    display: inline-block !important;
    height: auto !important;
    line-height: 24px !important;
  }
  /deep/.ant-form-item-control-wrapper {
    .ant-form-item-control {
      min-height: 24px;
      height: auto !important;
    }
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }

  /deep/.ant-input {
    height: 24px;
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.ant-table {
  /deep/.ant-table-thead tr {
    height: 0 !important;
  }
  .ant-table-thead tr th {
    padding: 6px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 6px 4px;
    border-right: 1px solid #efefef;
  }
  tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
  tr.ant-table-row-hover td {
    background: #dfdcdc;
  }
  .rowBackgroundColor {
    background: #dfdcdc;
  }

  .ant-table-selection-col {
    width: 20px !important;
  }
}

.ant-modal-content {
  .ant-modal-body {
    .ant-card {
      /deep/ .ant-card-body {
        padding: 0 !important;
      }
    }
  }
}
.ant-modal-content {
  .ant-modal-body {
    .ant-card {
      .ant-card-body {
        .ant-form {
          border-left: 1px solid #ddd;
          border-top: 1px solid #ddd;
        }
        .ant-row {
          margin-bottom: 0;
          .ant-col {
            .ant-form-item {
              /deep/.ant-form-item-control-wrapper {
                .ant-form-item-control {
                  border-top: 1px solid #ddd;
                }
              }
              /deep/.ant-form-item-label {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
                font-weight: 500;
                color: #666;
                background-color: #fafafa;
                border-right: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                height: 30px;
                label {
                  font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
                  font-weight: 500;
                }
              }
              /deep/.ant-form-item-control-wrapper {
                font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
                font-weight: 500;
                .ant-form-item-control {
                  line-height: inherit;
                  padding: 2px 4px;
                  border-right: 1px solid #ddd;
                  border-bottom: 1px solid #ddd;
                  // border-top: 1px solid #ddd;
                  height: 30px;
                  .ant-form-item-children {
                    .editWrapper {
                      .ant-input {
                        height: 24px;
                        min-height: 20px;
                        line-height: 16px;
                      }
                      .ant-select {
                        .ant-select-selection {
                          height: 24px;
                          .ant-select-selection__rendered {
                            line-height: 24px;
                          }
                        }
                      }
                      .ant-checkbox-wrapper {
                        .ant-checkbox {
                          margin-top: 5px;
                        }
                      }
                      .ant-input-affix-wrapper {
                        .ant-input {
                          height: 24px;
                          padding: 0 5px;
                        }
                      }
                      .ant-calendar-picker {
                        .ant-calendar-picker-input {
                          height: 24px;
                          width: 272px;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
