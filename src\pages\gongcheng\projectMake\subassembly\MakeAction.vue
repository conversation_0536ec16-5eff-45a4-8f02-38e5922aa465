<!-- 工程管理 - 工程制作-按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.GetEngineeringProductionOrder')"
      :class="checkPermission('MES.EngineeringModule.EngineeringProduction.GetEngineeringProductionOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="TakeOrderClick" :disabled="isDisabled"> 取单(Q) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.MakeStart')"
      :class="checkPermission('MES.EngineeringModule.EngineeringProduction.MakeStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="MakeStartClick"> 开始(S) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.EngineeringProductionPpeFinish')"
      :class="checkPermission('MES.EngineeringModule.EngineeringProduction.EngineeringProductionPpeFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="finishClick('2')"> 完成 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.EngOrderModify')"
      :class="checkPermission('MES.EngineeringModule.EngineeringProduction.EngOrderModify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="MarketmodClick"> 市场修改 </a-button>
    </div>
    <div class="box showClass">
      <a-button type="primary" @click="PerformanceClick"> 绩效管理 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.EngSureOrderModify')"
      :class="checkPermission('MES.EngineeringModule.EngineeringProduction.EngSureOrderModify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Confirmmodification"> 确认修改 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.EngPreContractNotice')"
      :class="checkPermission('MES.EngineeringModule.EngineeringProduction.EngPreContractNotice') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ProductionOrder"> 生产单 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.GetWenKeUrl')"
      :class="checkPermission('MES.EngineeringModule.EngineeringProduction.GetWenKeUrl') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="wenkeclick"> 问客 </a-button>
    </div>
    <div class="box showClass">
      <a-button type="primary" @click="$emit('Releasewarning')"> 解除警告 </a-button>
    </div>
    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.EngineeringProductionPpeFinish')">
      <a-button type="primary" @click="finishClick('2')" style='background: #ff9900;color: white;border-color: #ff9900;text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);box-shadow: 0 2px 0 rgb(0 0 0 / 5%);'>
        回传
      </a-button>
     <a-upload
         accept=".rar,.zip"
         name="file"
         :multiple="false"
         :customRequest="httpRequest1"
         :showUploadList="false"
         ref="fileRef"
         :before-upload="beforeUpload1"
         v-show="false"
     >
       <a-button style="width: 80px;"><a-icon type="upload" /></a-button>
     </a-upload>
    </div> -->
    <!--    <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetAutoStack') || checkPermission('MES.EngineeringModule.EngineeringProduction.GetImp')" style="display: flex; justify-content: center">-->
    <!--      <div style="width: 65px">-->
    <!--        <a-button type="primary"  @click='GenerateStackClick' style="width: 100%; padding-left: 5px; border-bottom-right-radius: 0;border-top-right-radius: 0">-->
    <!--          {{selectValue}}-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--      <div style="width: 15px">-->
    <!--        <a-select @change="selectChange" v-model="selectValue" style="width: 100%;" class="selctClass" :dropdownMatchSelectWidth="false">-->
    <!--          <a-select-option value="生成叠层" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetAutoStack')" style='border-radius: 8px;' >生成叠层</a-select-option>-->
    <!--          <a-select-option value="叠层阻抗" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetImp')" style='border-radius: 8px;'>叠层阻抗</a-select-option>-->
    <!--        </a-select>-->
    <!--      </div>-->
    <!--    </div>-->
    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetImp')">
      <a-button type="primary" @click ="GenerateStackClick">
        叠层阻抗
      </a-button>
    </div>
    <div   >
      <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetAutoToolPnl')">
        <a-button type="primary" @click='CuttingClick' >
          拼版开料
        </a-button>
      </div>
    </div> -->
    <!--    <div v-if='advanced'  >-->
    <!--      <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetFixRecord')">-->
    <!--        <a-button type="primary" @click='RepairRecordClick' >-->
    <!--          返修记录-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--    </div>-->
    <!-- <div class="box" v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.FixFinish')"
      :class='checkPermission("MES.EngineeringModule.EngineeringProduction.FixFinish")?"showClass":""'>
        <a-button type="primary" @click='RepairCompletedClick'>
          返修完成
        </a-button>
      </div> -->
    <!--  <div class="box" v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.MattersNeedingAttention')"
      :class='checkPermission("MES.EngineeringModule.EngineeringProduction.MattersNeedingAttention")?"showClass":""'>
        <a-button type="primary" @click='mattersNeedingAttentionClick'>
          注意事项
        </a-button>
    </div> -->
    <!-- <div class="box"  v-show="checkPermission('MES.EngineeringModule.EngineeringProduction.GetFixRecord')"
      :class='checkPermission("MES.EngineeringModule.EngineeringProduction.GetFixRecord")?"showClass":""'>
        <a-button type="primary" @click='RegisterClick' >
          返修登记
        </a-button>   
    </div> -->
    <!--    <div v-if='advanced'  >-->
    <!--      <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetFabricatiUrl')">-->
    <!--        <a-button type="primary" @click="ProductionStandardClick">-->
    <!--          制作标准-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--  </div>-->
    <!--  <div v-if='advanced'  >-->
    <!--      <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.MattersNeedingAttention')">-->
    <!--        <a-button type="primary" @click="OverlayCopyClick">-->
    <!--          叠层复制-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--  </div>-->
    <!-- <div v-if='advanced'  >
    <div class="box" >
      <a-button type="primary" @click ="excelClick">
       订单导入
      </a-button>
      <a-upload
          accept=".xlsx,.xls"
          name="file"
          ref="fileRef"
          :before-upload="beforeUpload1"
          :customRequest="httpRequest1" >
        <a-button style="width: 80px;display: none;"><a-icon type="upload" /></a-button>
      </a-upload>
    </div>
  </div> -->
    <!--    <div v-if='advanced'  >-->
    <!--      <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetFixRecord')">-->
    <!--        <a-button type="primary" @click='openApp' >-->
    <!--          打开APP-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--  </div>-->
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
            <a-menu-item @click="TakeOrderClick" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetEngineeringProductionOrder')"
              >取单(Q)</a-menu-item
            >
            <a-menu-item @click="MakeStartClick" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.MakeStart')">开始(S)</a-menu-item>
            <a-menu-item
              @click="finishClick('2')"
              v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.EngineeringProductionPpeFinish')"
              >完成</a-menu-item
            >
            <a-menu-item @click="MarketmodClick" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.EngOrderModify')"
              >市场修改</a-menu-item
            >
            <!-- <a-menu-item @click="PerformanceClick">绩效管理</a-menu-item> -->
            <a-menu-item @click="Confirmmodification" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.EngSureOrderModify')"
              >确认修改</a-menu-item
            >
            <a-menu-item @click="ProductionOrder" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.EngPreContractNotice')"
              >生产单</a-menu-item
            >
            <a-menu-item @click="wenkeclick" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetWenKeUrl')">问客</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import protocolCheck from "@/utils/protocolcheck";
import TagSelectOption from "@/components/tool/TagSelectOption";
import { ImportOrder } from "@/services/projectMake";

export default {
  name: "MakeAction",
  props: {
    total: {
      type: Number,
    },
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    selectId: {
      type: String,
    },
  },
  data() {
    return {
      selectValue: "生成叠层",
      advanced: false,
      width: 762,
      collapsed: false,
      orderId: "",
      showBtn: false,
      nums: "",
      isDisabled: false,
      buttonsmenu: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      this.nums = elements.length;
      let buttonsToShow = 8;
      if (this.$refs.active.children.length > 8) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 8) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      this.handleResize();
    });
  },
  mounted() {
    // var box = document.getElementsByClassName('box')
    // console.log('box',box)
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      const num = elements.length * 104;
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            if (i < 8) {
              elements[i].style.display = "inline-block";
            } else {
              elements[i].style.display = "none";
            }
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                if (i < 8) {
                  elements[i].style.display = "inline-block";
                } else {
                  elements[i].style.display = "none";
                }
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        let buttonsToShow = 8;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
    },
    //生产单
    ProductionOrder() {
      this.$emit("ProductionOrder");
    },
    //问客
    wenkeclick() {
      this.$emit("wenkeclick");
    },
    // 取单
    TakeOrderClick() {
      this.$emit("TakeOrderClick");
    },
    // 开始
    MakeStartClick() {
      this.$emit("MakeStartClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 返修完成
    RepairCompletedClick() {
      this.$emit("RepairCompletedClick");
    },
    // 问客
    wenkeClick() {
      this.$emit("wenkeClick");
    },
    //市场修改
    MarketmodClick() {
      this.$emit("MarketmodClick");
    },
    // 制作标准
    ProductionStandardClick() {
      this.$emit("ProductionStandardClick");
    },
    //叠层复制
    OverlayCopyClick() {
      this.$emit("OverlayCopyClick");
    },
    //订单导入
    excelClick() {
      // this.$emit('excelClick')
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    // 注意事项
    mattersNeedingAttentionClick() {
      this.$emit("mattersNeedingAttentionClick");
    },
    // 返修登记
    RegisterClick() {
      this.$emit("RegisterClick");
    },
    // 生成叠层
    GenerateStackClick() {
      // if (this.selectValue =='生成叠层') {
      //   this.$emit('GenerateStackClick')
      // }else {
      //   this.$emit('StackImpedanceClick')
      // }
      this.$emit("StackImpedanceClick");
    },
    // 拼版开料层
    CuttingClick() {
      this.$emit("CuttingClick");
    },
    // 返修记录
    RepairRecordClick() {
      this.$emit("RepairRecordClick");
    },
    // 完成
    finishClick(code) {
      this.$emit("finishClick", code);
    },
    PerformanceClick() {
      this.$emit("PerformanceClick");
    },
    Confirmmodification() {
      this.$emit("Confirmmodification");
    },
    openApp() {
      protocolCheck(
        "WebshellEmsCam://",
        fail => {
          console.log("fail", fail);
          // 没有安装 弹窗显示 引导去下载
          this.$message.error("未安装注册");
        },
        succ => {
          // 安装则直接打开
          console.log("succ", succ);
        }
      );
    },
    beforeUpload1(file) {
      const isFileType = file.name.toLowerCase().indexOf(".xlsx") != -1 || file.name.toLowerCase().indexOf(".xls") != -1;
      console.log("file", file);
      if (!isFileType) {
        this.$message.error("只支持.xlsx或.xls格式文件");
      }
      return isFileType;
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      console.log(formData);
      ImportOrder(formData).then(res => {
        if (res.code == 1) {
          this.$message.success("导入完成");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    clickUpload(id) {
      this.orderId = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.active {
  float: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 90px;
    margin-top: 8px;
    text-align: center;
    .ant-btn {
      width: 80px;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
    ///deep/  .ant-select{
    // .ant-select-arrow{
    //   display: none;
    // }
    // .ant-select-selection__rendered{
    //   margin-left:10px;
    //   margin-right:0;
    // }
    // .ant-select-selection{
    //   background: #ff9900;
    // }
    // .ant-select-selection:hover {
    //   border-color: #ffb029;
    //   border-right-width: 1px !important;
    //   background-color: #ffb029;
    // }
    // width: 80px;
    // color: #fff;
    // background: #ff9900;
    // border-radius: 4px;
    // color: #fff;
    // background-color: #ff9900;
    // border-color: #ff9900;
    // text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);
    //}
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
