<!-- 车间管理-开料管理 -->
<template>
  <div class="CuttingManagement">
    <a-spin :spinning="spinning">
      <div class="content">
        <div class="left" ref="letfDom" style="width: 65%">
          <a-card :bordered="false" style="height: 390px; border: 1px solid #e9e9f0">
            <a-table
              rowKey="guid_"
              :columns="columns"
              :dataSource="data1Source"
              :pagination="false"
              :loading="table1Loading"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :customRow="eventTouch"
              :rowClassName="setRowClassName"
              :scroll="{ x: '65%', y: 355 }"
              :row-selection="{ selectedRowKeys: selectedRowList, onChange: onSelectChange }"
              :class="{ minClass: data1Source.length > 0 }"
            >
            </a-table>
          </a-card>
          <a-card :bordered="false" style="height: 390px; border: 1px solid #e9e9f0" @contextmenu.prevent="rightClick($event)">
            <a-table
              rowKey="guid_"
              :columns="columns2"
              :dataSource="data2Source"
              :pagination="false"
              :loading="table2Loading"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :customRow="eventTouch1"
              :scroll="{ x: '65%', y: 355 }"
              :rowClassName="setRowClassName1"
              :row-selection="{ selectedRowKeys: selectedRowList1, onChange: onSelectChange1 }"
              :class="{ minClass: data2Source.length > 0 }"
            >
              <template slot="isPrint_" slot-scope="text, record">
                <a-checkbox v-model="record.isPrint_"> </a-checkbox>
              </template>
              <template slot="ispush" slot-scope="text, record">
                <a-checkbox v-model="record.ispush"> </a-checkbox>
              </template>
              <template slot="isStop_" slot-scope="text, record">
                <a-checkbox v-model="record.isStop_"> </a-checkbox>
              </template>
            </a-table>
            <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
              <a-menu-item>
                <a-upload accept=".drl" name="file" :before-upload="beforeUpload" :customRequest="httpRequest0"> 文件上传 </a-upload>
              </a-menu-item>
              <a-menu-item @click="down"> 流程卡 </a-menu-item>
            </a-menu>
          </a-card>
        </div>
        <div class="right" style="width: 35%">
          <center
            :tableData="data3Source"
            :machineStatuList="data4Source"
            :machineStatuLoad="table4Loading"
            :imageLoading="imageLoading"
            :drillLoad="table5Loading"
            :drillList="data5Source"
            ref="cuttingCenter"
            :aPnlOssPath="aPnlOssPath"
            :bPnlOssPath="bPnlOssPath"
            :sheetOssPath="sheetOssPath"
          ></center>
        </div>
      </div>
      <div class="footer">
        <div class="actionBox">
          <action
            @ReceivingClick="ReceivingClick"
            @StartOfMaterialCuttingClick="StartOfMaterialCuttingClick"
            @OverOrderClick="OverOrderClick"
            @UploadOrderClick="UploadOrderClick"
            @SetUpExpeditingClick="SetUpExpeditingClick"
            @DeleteOrderClick="DeleteOrderClick"
            @ExceptionRemarksClick="ExceptionRemarksClick"
            @queryClick="queryClick"
            @DrillingSequenceClick="DrillingSequenceClick"
            @operationClick1="operationClick1"
            @operationClick2="operationClick2"
            @operationClick3="operationClick3"
            :btnloading1="btnloading1"
            :btnloading2="btnloading2"
            :btnloading3="btnloading3"
            :btnloading4="btnloading4"
            :btnloading5="btnloading5"
            :btnloading6="btnloading6"
            :btnloading7="btnloading7"
            :btnloading8="btnloading8"
          ></action>
        </div>
      </div>
      <!-- 部门过序弹窗 -->
      <a-modal
        title="部门过序"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        :destroyOnClose="true"
      >
        <over-order-info
          @keyupEnter="keyupEnter"
          :quantity="quantity"
          @quantityChange="quantityChange"
          :quantityCopy="quantityCopy"
          ref="overOrder"
        />
      </a-modal>
      <!-- 上传订单弹窗 -->
      <a-modal
        title="录入订单"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <enter-order-info ref="enterOderForm" />
      </a-modal>
      <!-- 开料信息弹窗 -->
      <a-modal
        title="开料信息"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
      >
        <material-opening-info ref="materialOpeningInfo" :selectedRows1="selectedRows1" />
      </a-modal>
      <!-- 异常备注弹窗 -->
      <a-modal
        title="异常备注"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <exception-remarks-info ref="exceptionRemarksInfo" />
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk5"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <query-info ref="queryInfo" @keyupEnter1="keyupEnter1" />
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import {
  getWaitOrderList,
  getDoingOrderList,
  getDrillHoleList,
  getStatisticsList,
  Receiving,
  UserFactory,
  materialCutting,
  getpassStepNum,
  cuttingSequence,
  UploadOrder,
  SetUpExpediting,
  DeleteOrder,
  ExceptionRemarks,
  DrillingSequence,
  CallTrolley,
  Confirm,
  AgvCancel,
  ProcessCard,
  orderFileUpload,
  UploadFile1,
} from "@/services/cutting1";
import Center from "./module/Center";
import Action from "./module/Action";
import OverOrderInfo from "./module/OverOrderInfo";
import EnterOrderInfo from "./module/EnterOrderInfo";
import ExceptionRemarksInfo from "./module/ExceptionRemarksInfo";
import QueryInfo from "./module/QueryInfo";
import { download } from "@/utils/util";
import MaterialOpeningInfo from "./module/MaterialOpeningInfo";
const columns = [
  {
    dataIndex: "index",
    title: "序号",
    slots: { title: "customTitle" },
    key: "index",
    width: 40,
    align: "center",
    fixed: "left",
    scopedSlots: { customRender: "index" },
    customRender: (text, record, index) => `${index + 1}`,
    customCell: record => {
      if (record.color_ == "#FF0000") {
        return { style: { background: "#FF0000" } };
      }
    },
  },
  {
    title: "拼板编号",
    dataIndex: "pdctno_",
    width: 130,
    ellipsis: true,
    className: "orderClass",
    align: "center",
    fixed: "left",
  },
  {
    title: "板材类别",
    width: 100,
    ellipsis: true,
    dataIndex: "fR4Type",
    align: "center",
  },
  {
    title: "流程卡号",
    dataIndex: "cardNo",
    width: 110,
    ellipsis: true,
    align: "center",
  },
  {
    title: "板厚",
    width: 45,
    dataIndex: "boardThickness",
    ellipsis: true,
    align: "center",
  },
  {
    title: "铜厚",
    dataIndex: "copperThickness",
    width: 50,
    ellipsis: true,
    align: "center",
  },
  {
    title: "大料尺寸",
    dataIndex: "sheetSize",
    width: 70,
    ellipsis: true,
    align: "center",
  },
  {
    title: "大料数量",
    dataIndex: "materialNum_",
    width: 70,
    ellipsis: true,
    align: "center",
  },
  {
    title: "PNL数",
    dataIndex: "num",
    width: 55,
    ellipsis: true,
    align: "center",
  },
  {
    title: "推送时间",
    width: 100,
    dataIndex: "inDate_",
    align: "center",
    ellipsis: true,
  },
  {
    title: "收货时间",
    width: 100,
    dataIndex: "startDate_",
    align: "center",
    ellipsis: true,
  },
  {
    title: "订单工厂",
    width: 90,
    ellipsis: true,
    dataIndex: "factoryName",
    align: "center",
  },
  {
    title: "交货日期",
    width: 90,
    dataIndex: "deliveryDate",
    ellipsis: true,
    align: "center",
  },
];
const columns2 = [
  {
    dataIndex: "index",
    title: "序号",
    slots: { title: "customTitle" },
    key: "index",
    width: 40,
    align: "center",
    fixed: "left",
    scopedSlots: { customRender: "index" },
    customRender: (text, record, index) => `${index + 1}`,
    customCell: record => {
      if (record.color_ == "#FF0000") {
        return { style: { background: "#FF0000" } };
      }
    },
  },
  {
    title: "拼板编号",
    dataIndex: "pdctno_",
    width: 130,
    ellipsis: true,
    className: "orderClass",
    align: "center",
    fixed: "left",
  },
  {
    title: "板材类别",
    width: 105,
    ellipsis: true,
    dataIndex: "fR4Type",
    align: "center",
  },
  {
    title: "流程卡号",
    dataIndex: "cardNo",
    width: 125,
    ellipsis: true,
    align: "center",
  },
  {
    title: "子单数",
    width: 60,
    dataIndex: "count4Hp_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "PNL数",
    dataIndex: "count4Pnl_",
    width: 55,
    ellipsis: true,
    align: "center",
  },
  {
    title: "面积",
    width: 45,
    dataIndex: "area_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "铜厚",
    dataIndex: "copperThickness",
    width: 70,
    ellipsis: true,
    align: "center",
  },
  {
    title: "大料尺寸",
    dataIndex: "sheetSize",
    width: 75,
    ellipsis: true,
    align: "center",
  },
  {
    title: "大料数量",
    dataIndex: "materialNum_",
    width: 75,
    ellipsis: true,
    align: "center",
  },
  {
    title: "板厚",
    width: 45,
    dataIndex: "boardthick_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "最小孔径",
    dataIndex: "minHole_",
    width: 75,
    ellipsis: true,
    align: "center",
  },
  {
    title: "打印",
    dataIndex: "isPrint_",
    width: 50,
    align: "center",
    scopedSlots: { customRender: "isPrint_" },
  },
  {
    title: "订单工厂",
    width: 90,
    ellipsis: true,
    dataIndex: "fac_",
    align: "center",
  },
  {
    title: "交货日期",
    width: 100,
    ellipsis: true,
    dataIndex: "delDate_",
    align: "center",
  },
  {
    title: "推送",
    width: 45,
    dataIndex: "ispush",
    scopedSlots: { customRender: "ispush" },
    align: "center",
  },
  {
    title: "总孔数",
    width: 45,
    dataIndex: "allHoleNum_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "收货时间",
    width: 100,
    dataIndex: "endDate_",
    align: "center",
    ellipsis: true,
  },
  {
    title: "开料开始",
    width: 100,
    dataIndex: "cutStartDate_",
    align: "center",
    ellipsis: true,
  },
  {
    title: "备注",
    width: 120,
    dataIndex: "note_",
    align: "center",
    ellipsis: true,
  },
  {
    title: "暂停",
    width: 45,
    dataIndex: "isStop_",
    scopedSlots: { customRender: "isStop_" },
    align: "center",
  },
];
import { mapState } from "vuex";
export default {
  name: "CuttingManagement",
  components: { Action, Center, OverOrderInfo, EnterOrderInfo, ExceptionRemarksInfo, QueryInfo, MaterialOpeningInfo },
  inject: ["reload"],
  data() {
    return {
      spinning: false,
      columns,
      columns2,
      loading: false,
      table1Loading: false, // 待收货表格load
      table2Loading: false, // 待开料表格load
      table4Loading: false, // 机台状态load
      table5Loading: false, // 钻刀表格load
      imageLoading: false,
      data1Source: [], // 待分派集合
      data2Source: [], // 已分派集合
      data3Source: [], // 机台集合
      data4Source: [], // 机台状态集合
      data5Source: [], // 钻刀集合
      selectedRowList: [], // 选择待收货订单id数组
      selectedRowList1: [], // 选择待开料订单id数组
      selectedRowList2: [], // 选择待开料订单流程卡号数组
      selectedRows: [],
      selectedRows1: [],
      assignMachineList: [],
      data: [],
      rowId1: "",
      dataVisible: false, //部门过序弹窗不显示
      dataVisible1: false, //上传订单弹窗不显示
      dataVisible2: false, //开料信息弹窗不显示
      dataVisible3: false, //异常备注弹窗不显示
      dataVisible4: false, //查询弹窗
      quantity: "",
      quantityCopy: "",
      cardNo: "",
      confirmLoading: false,
      note: "",
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      menuVisible: false,
      menuData: [],
      OrderNumber: "",
      aPnlOssPath: "", // a板图
      bPnlOssPath: "", // b板图
      sheetOssPath: "", // 大料图
      btnloading1: false, // 收货按钮loading
      btnloading2: false, // 开料按钮loading
      btnloading3: false, // 设置加急按钮loading
      btnloading4: false, // 删除订单按钮loading
      btnloading5: false, // 呼叫小车按钮loading
      btnloading6: false, // 人员确认按钮loading
      btnloading7: false, // 取消小车按钮loading
      btnloading8: false, // 钻孔过序按钮loading
      agvID: "",
      scancode: "",
      scancodeVisible: false, //扫码弹窗
    };
  },
  // 获取当前登陆账号信息
  computed: {
    ...mapState("account", ["user"]),
  },
  methods: {
    // 待收货列表
    getorderList(orderNum) {
      let params = {};
      if (orderNum) {
        params.orderno = orderNum;
      }
      this.table1Loading = true;
      getWaitOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data1Source = res.data || [];
          }
        })
        .finally(() => {
          this.table1Loading = false;
        });
    },
    // 已开料列表
    getdoOrderList(orderNum) {
      let params = {};
      if (orderNum) {
        params.orderno = orderNum;
      }
      this.table2Loading = true;
      getDoingOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data2Source = res.data || [];
          }
        })
        .finally(() => {
          this.table2Loading = false;
        });
    },
    // 状态统计列表
    getMachineStatuList() {
      this.table4Loading = true;
      getStatisticsList()
        .then(res => {
          if (res.code == 1) {
            this.data4Source = res.data;
          }
        })
        .finally(() => {
          this.table4Loading = false;
        });
    },
    // 钻刀列表
    getDrillList(id) {
      this.table5Loading = true;
      getDrillHoleList({ orderno: id })
        .then(res => {
          if (res.code == 1) {
            this.data5Source = res.data;
          }
        })
        .finally(() => {
          this.table5Loading = false;
        });
    },
    // 点击事件配置
    eventTouch(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            let rowKeys = this.selectedRowList;
            if (rowKeys.length > 0 && rowKeys.includes(record.guid_)) {
              rowKeys.splice(rowKeys.indexOf(record.guid_), 1);
            } else {
              rowKeys.push(record.guid_);
            }
            this.selectedRowList = rowKeys;
            console.log("this.selectedRowList", this.selectedRowList);
          },
          // dblclick: () => { //双击
          //   this.getDrillList(record.pdctno_)
          //   this.rowId1 = record.pdctno_;
          //   // console.log('this.rowId1',this.rowId1)
          // },
        },
      };
    },
    eventTouch1(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            // 单击
            let rowKeys1 = this.selectedRowList1;
            if (rowKeys1.length > 0 && rowKeys1.includes(record.guid_)) {
              rowKeys1.splice(rowKeys1.indexOf(record.guid_), 1);
            } else {
              rowKeys1.push(record.guid_);
            }
            this.selectedRowList1 = rowKeys1;
            // console.log('this.selectedRowList1',this.selectedRowList1)
            let rowData = this.selectedRows1;
            if (rowData.length > 0 && rowData.includes(record.materialNum_)) {
              rowData.splice(rowData.indexOf(record.materialNum_), 1);
            } else {
              rowData.push(record.materialNum_);
            }
            this.selectedRows1 = rowData;
            // console.log('this.selectedRows1',this.selectedRows1)
            this.getDrillList(record.pdctno_);
            this.rowId1 = record.pdctno_;
            // console.log('this.rowId1',this.rowId1)
            this.aPnlOssPath = record.aPnlOssPath;
            this.bPnlOssPath = record.bPnlOssPath;
            this.sheetOssPath = record.sheetOssPath;
            let cardNoArr = [];
            cardNoArr.push(record.cardNo);
            this.selectedRowList2 = cardNoArr;
            console.log("this.selectedRowList2", this.selectedRowList2);
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
          // dblclick: () => { //双击
          //   this.getDrillList(record.pdctno_)
          //   this.rowId1 = record.pdctno_;
          //   console.log('this.rowId1',this.rowId1)
          //   this.aPnlOssPath = record.aPnlOssPath;
          //   this.bPnlOssPath = record.bPnlOssPath;
          //   this.sheetOssPath = record.sheetOssPath;
          //   // this.imageLoading =true
          // },
        },
      };
    },

    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      console.log();
      this.menuStyle.top = e.clientY - 450 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 选择待收货订单列表
    onSelectChange(selectedRowKeys, selectedRows) {
      const newSelectedRowKeys = [],
        newSelectedRows = [];
      selectedRows.forEach(item => {
        newSelectedRowKeys.push(item.guid_);
        newSelectedRows.push(item);
      });
      this.selectedRowList = newSelectedRowKeys;
      this.selectedRows = newSelectedRows;
      console.log("待收货订单", this.selectedRowList);
    },
    // 选择待开料订单列表
    onSelectChange1(selectedRowKeys, selectedRows) {
      console.log(selectedRows);
      const newSelectedRowKeys = [],
        newSelectedRows = [],
        newSelectedRowsMaterialNum = [],
        newCardNoArr = [];
      selectedRows.forEach(item => {
        newSelectedRowKeys.push(item.guid_);
        newSelectedRowsMaterialNum.push(item.materialNum_);
        newSelectedRows.push(item);
        newCardNoArr.push(item.cardNo);
      });
      this.selectedRowList1 = newSelectedRowKeys;
      this.selectedRows1 = newSelectedRowsMaterialNum;
      this.data = newSelectedRows;
      this.selectedRowList2 = newCardNoArr;
      console.log("待开料订单", this.selectedRowList1);
    },
    // 收货
    ReceivingClick() {
      let params = this.selectedRowList;
      if (this.selectedRowList.length <= 0) {
        this.$message.warning("请至少选择1个待收货订单");
        return;
      }
      this.spinning = true;
      this.btnloading1 = true;
      this.selectedRowList.forEach(item => {
        Receiving([item]).then(res => {
          // if(res.code == 1){
          //   this.$message.success('收货成功')
          // }else{
          //   this.$message.error(res.message)
          // }
          if (!res.code) {
            this.$message.error(res.message);
          }
          this.reload();
        });
      });
      this.btnloading1 = false;
      this.spinning = false;
      this.reload();
    },
    // 开料开始
    StartOfMaterialCuttingClick() {
      // console.log('name',this.user)
      if (this.selectedRowList1.length <= 0) {
        return this.$message.warning("请选择待开料订单");
      }
      console.log(this.selectedRows1, this.selectedRowList1);
      this.btnloading2 = true;
      UserFactory()
        .then(res => {
          if (res.code == 1) {
            if (res.data == 175) {
              let params = {
                ids: this.selectedRowList1,
                materialNum_: 0,
              };
              materialCutting(params).then(res => {
                if (res.code == 1) {
                  this.$message.success("设置成功");
                } else {
                  this.$message.error(res.message);
                }
                // this.reload()
              });
            } else {
              if (this.selectedRowList1.length != 1) {
                return this.$message.warning("请选择单个待开料订单");
              }
              this.dataVisible2 = true;
            }
          } else {
            this.$message.error(res.message);
          }
          // this.getdoOrderList()
          // this.reload()
        })
        .finally(() => {
          this.btnloading2 = false;
          this.spinning = false;
        });
    },
    // 开料信息弹窗
    handleOk3() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = {
        ids: this.selectedRowList1,
        materialNum_: this.selectedRows1[0],
      };
      materialCutting(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible2 = false;
        });
    },
    // 待开料背景设置
    setRowClassName1(record) {
      var classStr = "";
      // 单击设置背景色
      if (record.guid_ == this.selectedRowList1) {
        classStr = classStr + "bacStyle" + " ";
      }
      // 双击选中背景色
      // if(record.pdctno_ == this.rowId1 ){
      //   classStr =  'clickRowSty2'
      // }
      return classStr;
    },
    setRowClassName(record) {
      var classStr = "";
      // 单击设置背景色
      if (record.guid_ == this.selectedRowList) {
        classStr = classStr + "bacStyle" + " ";
      }
      // 双击选中背景色
      // if(record.pdctno_ == this.rowId1 ){
      //   classStr =  'clickRowSty2'
      // }
      return classStr;
    },
    // 弹窗关闭控制
    reportHandleCancel() {
      this.dataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.dataVisible4 = false;
    },
    quantityChange(payload) {
      this.quantity = payload;
    },
    // 部门过序
    OverOrderClick() {
      this.dataVisible = true;
    },
    // 获取部门过序数量
    async keyupEnter(cardNo) {
      this.confirmLoading = true;
      let params = {
        cardNo: cardNo,
      };
      await getpassStepNum(params).then(res => {
        if (res.code == 1) {
          this.cardNo = cardNo;
          this.quantity = res.data;
          this.quantityCopy = JSON.parse(JSON.stringify(res.data));
          this.$message.success("获取过序数量成功");
        } else {
          this.$message.error(res.message);
        }
        this.reload();
        this.confirmLoading = false;
      });
    },
    handleOk1() {
      this.confirmLoading = true;
      this.spinning = true;
      let paramsData = {
        cardNo: this.cardNo,
        num: this.quantity,
      };
      cuttingSequence(paramsData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("过序成功");
          } else {
            this.$message.error(res.message);
          }
          // this.getorderList();
          // this.getdoOrderList()
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible = false;
        });
    },
    // 上传钻孔
    UploadOrderClick() {
      this.dataVisible1 = true;
    },
    handleOk2() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = this.$refs.enterOderForm.enterOrderForm;
      console.log(params);
      UploadOrder(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
          // this.getorderList()
          // this.getdoOrderList()
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible1 = false;
        });
    },

    // 设置加急
    SetUpExpeditingClick() {
      if (this.selectedRowList1.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.spinning = true;
      this.btnloading3 = true;
      SetUpExpediting(this.selectedRowList1)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("设置成功");
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.btnloading3 = false;
          this.spinning = false;
        });
    },
    // 删除订单
    DeleteOrderClick() {
      if (this.selectedRowList1.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.spinning = true;
      this.btnloading4 = true;
      DeleteOrder(this.selectedRowList1[0])
        .then(res => {
          if (res.code == 1) {
            this.$message.success("设置成功");
          } else {
            this.$message.error(res.message);
          }
          this.getdoOrderList();
          this.reload();
        })
        .finally(() => {
          this.btnloading4 = false;
          this.spinning = false;
        });
    },
    // 备注
    ExceptionRemarksClick() {
      if (this.selectedRowList1.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.dataVisible3 = true;
    },
    handleOk4() {
      this.confirmLoading = true;
      this.spinning = true;
      let note = this.$refs.exceptionRemarksInfo.note;
      console.log(this.selectedRowList1);
      ExceptionRemarks(this.selectedRowList1, { note: note })
        .then(res => {
          if (res.code == 1) {
            this.$message.success("备注成功");
          } else {
            this.$message.error(res.message);
          }
          this.getdoOrderList();
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible3 = false;
        });
    },
    //查询
    queryClick() {
      this.dataVisible4 = true;
    },
    keyupEnter1() {
      this.dataVisible4 = false;
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    handleOk5() {
      this.dataVisible4 = false;
      console.log(this.$refs.queryInfo.OrderNumber);
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    // 呼叫小车
    operationClick1() {
      if (!this.selectedRowList2.length > 0) {
        this.$message.error("请选择待开料订单");
        return;
      }
      this.spinning = true;
      this.btnloading5 = true;
      // this.selectedRowList2 所选流程卡号集合
      CallTrolley(this.selectedRowList2)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("呼叫成功");
            this.agvID = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading5 = false;
          this.spinning = false;
        });
    },
    // 人员确认
    operationClick2() {
      this.spinning = true;
      this.btnloading6 = true;
      Confirm()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("人员已确认");
          } else {
            this.$message.error(res.message);
          }
          this.getdoOrderList();
          this.getMachineStatuList();
        })
        .finally(() => {
          this.btnloading6 = false;
          this.spinning = false;
        });
    },
    // 取消小车
    operationClick3() {
      this.spinning = true;
      this.btnloading7 = true;
      AgvCancel()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("取消成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading7 = false;
          this.spinning = false;
        });
    },
    //  钻孔过序
    DrillingSequenceClick() {
      if (this.selectedRowList1.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.spinning = true;
      this.btnloading8 = true;
      this.selectedRowList1.forEach(item => {
        DrillingSequence([item]).then(res => {
          // if(res.code == 1){
          //   this.$message.success('钻孔过序成功')
          // }else{
          //   this.$message.error(res.message)
          // }
          // this.getdoOrderList()
          // this.reload()
          if (!res.code) {
            this.$message.error(res.message);
          }
        });
      });
      this.btnloading8 = false;
      this.spinning = false;
      this.reload();
    },

    // 上传文件
    async httpRequest0(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });
      if (resData.code == 1) {
        orderFileUpload(this.menuData.guid_, { path: resData.data }).then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".drl") != -1;
      if (!isFileType) {
        this.$message.error("只支持.drl格式文件");
      }
      return isFileType;
    },
    // 流程卡
    down() {
      ProcessCard(this.menuData.guid_)
        .then(res => {
          if (res.code == 1) {
            //  this.$message.success('成功')
            window.open(res.data);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.reload();
        });
    },
  },

  mounted() {
    this.getorderList();
    this.getdoOrderList();
    this.getMachineStatuList();
    this.getDrillList();
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: rgb(223 220 220);
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}

.CuttingManagement {
  min-width: 1670px;
  user-select: none;
  .content {
    display: flex;
    height: 780px;
  }
  .rowcolor {
    background: #fff9e6;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
  }
  .footer {
    .actionBox {
      overflow: hidden;
      height: 48px;
      width: 100%;
      border: 2px solid #e9e9f0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      form {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
      .bacStyle {
        background: #aba5a5 !important;
      }
    }
  }
  .left {
    /deep/ .ant-table-fixed {
      .ant-table-tbody {
        .ant-table-row {
          .orderClass {
            user-select: all;
          }
        }
      }
    }
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    /deep/ .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        text-align: center;
        border-bottom: 1px solid rgb(225, 223, 223);
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
  /deep/ .ant-table {
    .ant-table-selection-col {
      width: 30px;
    }
    tr.ant-table-row-selected td {
      background: #aba5a5;
    }
    tr.ant-table-row-hover td {
      background: #aba5a5;
    }
  }
  background: #ffffff;
  .minClass {
    /deep/ .ant-table-body {
      min-height: 355px;
    }
  }
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 5px 0;
        }
      }
    }
    .ant-table-tbody {
      .ant-input {
        padding: 0;
        border: 0;
        border-radius: 0;
        margin: -5px 0;
        text-align: center;
      }
      tr {
        td {
          padding: 5px 5px;
        }
      }
    }
  }

  /deep/ .ant-table-body {
    // &::-webkit-scrollbar {
    //   //整体样式
    //   width: 6px; //y轴滚动条粗细
    //   height: 6px;
    // }

    // &::-webkit-scrollbar-thumb {
    //   //滑动滑块条样式
    //   border-radius: 2px;
    //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    //   background: #ff9900;
    //   // #fff9e6
    // }
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 0;
        }
      }
    }
  }
  /deep/ .ant-table-scroll .mouseentered {
    overflow-x: scroll !important;
  }
}
</style>
