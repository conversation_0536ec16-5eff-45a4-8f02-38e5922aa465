<!-- 市场管理 - 价格体系 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend" style="position: relative">
      <div style="width: 100%; display: flex">
        <div class="leftContent" style="user-select: none">
          <left-table
            :columns="columns1"
            :data-source="dataSource1"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'id'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            @getOrderDetail="getOrderDetail"
            ref="orderTable"
            class="leftstyle"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
          </left-table>
        </div>
        <div class="center" style="user-select: none">
          <center-table
            :columns="columns2"
            :data-source="dataSource2"
            :orderListTableLoading="TableLoading2"
            :rowKey="'custNo'"
            ref="centerTable"
            class="centerstyle"
          >
          </center-table>
        </div>
        <div class="rightContent">
          <div class="centerTable">
            <a-tabs type="card" @change="tabpaneChange">
              <a-tab-pane key="1" tab="制板费">
                <order-detail
                  :columns="columns3"
                  :data-source="dataSource3"
                  :TableLoading2="TableLoading3"
                  :rowKey="'id'"
                  ref="orderDetail"
                  @selectRow="selectRow"
                  class="rightstyle1"
                >
                </order-detail>
              </a-tab-pane>
              <a-tab-pane key="2" tab="工程费">
                <order-detail
                  :columns="columns4"
                  :data-source="dataSource4"
                  :TableLoading2="TableLoading4"
                  :rowKey="'id'"
                  @selectRow="selectRow"
                  ref="orderDetail"
                  class="rightstyle2"
                >
                </order-detail>
              </a-tab-pane>
              <a-tab-pane key="7" tab="金手指价">
                <order-detail
                  :columns="columns9"
                  :data-source="dataSource8"
                  :TableLoading2="TableLoading9"
                  :rowKey="'id'"
                  @selectRow="selectRow"
                  class="rightstyle3"
                  ref="orderDetail"
                >
                </order-detail>
              </a-tab-pane>
              <a-tab-pane key="6" tab="区域对接客户">
                <order-detail
                  :columns="columns8"
                  :data-source="dataSource2"
                  :TableLoading2="TableLoading8"
                  @selectRow="selectRow"
                  :rowKey="'id'"
                  class="rightstyle4"
                  ref="orderDetail"
                >
                </order-detail>
              </a-tab-pane>
              <!-- <a-tab-pane key="3" tab="其他费用">
              <order-detail
                  :columns="columns5"
                  :data-source="dataSource5"
                  :TableLoading2="TableLoading5"
                  :rowKey="'id'"        
                  @selectRow="selectRow"        
                  ref="orderDetail"
              >
              </order-detail>
            </a-tab-pane> -->
              <!-- <a-tab-pane key="4" tab="区域公式">
              <order-detail
                  :columns="columns6"
                  :data-source="dataSource6"
                  :TableLoading2="TableLoading6"
                  :rowKey="'id'"                  
                  ref="orderDetail"
                  @selectRow="selectRow"   
                  :class="dataSource6.length ? 'min-table':''"  
              >
              </order-detail>
            </a-tab-pane> -->
              <!-- <a-tab-pane key="5" tab="分配公式">
              <order-detail
                  :columns="columns7"
                  :data-source="dataSource7"
                  :TableLoading2="TableLoading7"
                  :rowKey="'id'" 
                  @selectRow="selectRow"                 
                  ref="orderDetail"
              >
              </order-detail>
            </a-tab-pane> -->
            </a-tabs>
          </div>
        </div>
      </div>
      <div class="footerAction" style="user-select: none">
        <backend-action
          :assignLoading="assignLoading"
          ref="action"
          @queryClick="queryClick"
          @addClick="addClick"
          @editClick="editClick"
          @delClick="delClick"
          @ExportPrice="ExportPrice"
          @customerArea="customerArea"
          :total="pagination.total"
          :disbackend="disbackend"
          :activeKey="activeKey"
        />
      </div>
      <!-- 查询弹窗 -->
      <a-modal
        title="客户代码查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        centered
        :maskClosable="false"
        :width="400"
      >
        <query-info-backend ref="queryInfo" />
      </a-modal>
      <a-modal
        title="确认弹窗"
        :visible="dataVisibleMode"
        @cancel="reportHandleCancel"
        @ok="handleOkMode"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span>{{ messageMode }}</span>
      </a-modal>
      <a-modal
        title="客户区域"
        footer
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        destroyOnClose
        :maskClosable="false"
        :width="1000"
        centered
      >
        <customer-info
          ref="customerInfo"
          :dataSource9="dataSource9"
          :ttype="ttype"
          :sselectRowData="sselectRowData"
          @sselectRow="sselectRow"
          @newlyadded="newlyadded"
          @editlyadded="editlyadded"
          @saveclick="saveclick"
          @cancellation="cancellation"
        />
      </a-modal>
      <a-modal
        :title="titleList"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="widthy"
        centered
      >
        <cost-info
          ref="costInfo"
          :editFlg="editFlg"
          :activeKey="activeKey"
          :selectRowData="selectRowData"
          :selectOption="selectOption"
          :dropdownselection="dropdownselection"
        />
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import { checkPermission } from "@/utils/abp";
import {
  mktPriceManage4AreaID,
  mkTPriceRuleData4CalcCustArea,
  mktcustno,
  basicPrice,
  upbBasicPrice,
  delBasicPrice,
  delformula,
  delgoldfingerprice,
  delareacust,
  newpricemanage4AreaiD,
  uppricemanage4AreaiD,
  engPrice,
  upbEngPrice,
  delEngPrice,
  formula,
  upformula,
  getselect,
  areaCust,
  mktgoldfingerprice,
  goldfingerprice,
  upgoldfingerprice,
  exportPrice,
  pricemanage4AreaiD,
} from "@/services/mkt/PriceSystem";
import LeftTable from "@/pages/mkt/PriceSystem/module/LeftTable";
import centerTable from "@/pages/mkt/PriceSystem/module/centerTable";
import OrderDetail from "@/pages/mkt/PriceSystem/module/OrderDetail";
import BackendAction from "@/pages/mkt/PriceSystem/module/BackendAction";
import QueryInfoBackend from "@/pages/mkt/PriceSystem/module/QueryInfoBackend";
import costInfo from "@/pages/mkt/PriceSystem/module/costInfo";
import customerInfo from "@/pages/mkt/PriceSystem/module/customerInfo";
import * as XLSX from "xlsx";
import Cookie from "js-cookie";
import moment from "moment";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 40,
  },
  {
    title: "区域ID",
    dataIndex: "id",
    align: "left",
    ellipsis: true,
    width: 80,
    // sorter: (a, b) => {
    //   return a.orderNo.localeCompare(b.orderNo)
    // }
  },
  {
    title: "区域名称",
    dataIndex: "caption_",
    align: "left",
    ellipsis: true,
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    width: 80,
    align: "left",
    ellipsis: true,
  },
  {
    title: "客户名称",
    dataIndex: "custName",
    ellipsis: true,
    // width: 120,
    align: "left",
  },
];
const columns3 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "层数",
    dataIndex: "layer_",
    width: 40,
    ellipsis: true,
    align: "left",
  },
  {
    title: "1㎡",
    dataIndex: "priceA_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "2㎡",
    dataIndex: "priceB_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "3㎡",
    dataIndex: "priceC_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "4㎡",
    dataIndex: "priceD_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "5㎡",
    dataIndex: "priceE_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "6㎡",
    dataIndex: "priceF_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "7㎡",
    dataIndex: "priceG_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "8㎡",
    dataIndex: "priceH_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "9㎡",
    dataIndex: "rX_PriceA_",
    width: 52,
    ellipsis: true,
    align: "left",
  },
  {
    title: "10㎡",
    dataIndex: "rX_PriceB_",
    width: 60,
    ellipsis: true,
    align: "left",
  },
  {
    title: "10-20㎡",
    dataIndex: "sB_PriceA_",
    width: 60,
    ellipsis: true,
    align: "left",
  },
  {
    title: "20-30㎡",
    dataIndex: "priceL_",
    width: 60,
    ellipsis: true,
    align: "left",
  },
  {
    title: "30-40㎡",
    dataIndex: "priceZ_",
    width: 60,
    ellipsis: true,
    align: "left",
  },
  {
    title: "40-50㎡",
    dataIndex: "priceX_",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "50-100㎡",
    dataIndex: "priceV_",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: ">100㎡",
    dataIndex: "priceN_",
    width: 60,
    ellipsis: true,
    align: "left",
  },
];
const columns4 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "层数",
    dataIndex: "layer_",
    width: 460,
    ellipsis: true,
    align: "left",
  },
  {
    title: "工程费（元/款）",
    dataIndex: "priceA_",
    width: 120,
    ellipsis: true,
    align: "left",
  },
];
const columns5 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "left",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "分类",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "分类序号",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "条件1",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "条件2",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "条件3",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "条件4",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "样板费",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "批量费",
    dataIndex: "",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "工程费",
    dataIndex: "",
    width: 60,
    ellipsis: true,
    align: "left",
  },
];
const columns6 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "left",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "判断类别",
    dataIndex: "calcNameIDStr",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "判断类型",
    dataIndex: "conditionTypeStr",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "判断区域",
    dataIndex: "conditionAreaIDStr",
    width: 80,
    align: "left",
    ellipsis: true,
  },
  {
    title: "判断公式",
    dataIndex: "condition_",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "结果类型",
    dataIndex: "expressionTypeStr",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "结果区域",
    dataIndex: "expressionAreaIDStr",
    width: 80,
    align: "left",
    ellipsis: true,
  },
  {
    title: "结果公式",
    dataIndex: "expression_",
    width: 80,
    align: "left",
    ellipsis: true,
  },
  {
    title: "说明1",
    dataIndex: "captions_",
    width: 80,
    align: "left",
    ellipsis: true,
  },
  {
    title: "说明2",
    dataIndex: "captions2_",
    width: 80,
    align: "left",
    ellipsis: true,
  },
  {
    title: "基价类型",
    dataIndex: "basicPriceTypeStr",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "基价类型区域",
    dataIndex: "basicPriceAreaIDStr",
    width: 100,
    ellipsis: true,
    align: "left",
  },
  {
    title: "基价计算条件",
    dataIndex: "basicPrice_",
    width: 100,
    align: "left",
    ellipsis: true,
  },
  {
    title: "基价因子",
    dataIndex: "factor_",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "基价min类型",
    dataIndex: "basicPrice4MinTypeStr",
    width: 100,
    ellipsis: true,
    align: "left",
  },
  {
    title: "基价min区域",
    dataIndex: "basicPrice4MinAreaIDStr",
    width: 100,
    align: "left",
    ellipsis: true,
  },
  {
    title: "基价min计算",
    dataIndex: "basicPrice4Min_",
    width: 100,
    align: "left",
    ellipsis: true,
  },
  {
    title: "基价max类型",
    dataIndex: "basicPrice4MaxTypeStr",
    width: 100,
    ellipsis: true,
    align: "left",
  },
  {
    title: "基价max区域",
    dataIndex: "basicPrice4MaxAreaIDStr	",
    width: 100,
    align: "left",
    ellipsis: true,
  },
  {
    title: "基价max计算",
    dataIndex: "basicPrice4Max_",
    width: 100,
    align: "left",
    ellipsis: true,
  },
  {
    title: "条件有效",
    dataIndex: "isChkStr",
    width: 80,
    customRender: (text, record, index) => `${record.isChk_ == "1" ? "是" : "否"}`,
    align: "left",
    ellipsis: true,
  },
  {
    title: "系统计算模",
    dataIndex: "calcFormulaType_",
    width: 100,
    ellipsis: true,
    align: "left",
  },
  {
    title: "是否评审",
    dataIndex: "isReview_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isReview_" },
  },
  {
    title: "显示排序",
    dataIndex: "calcDisplays_",
    width: 80,
    align: "left",
    ellipsis: true,
  },
  {
    title: "汇总显示",
    dataIndex: "isShow_",
    width: 80,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "isShow_" },
  },
  {
    title: "样板费",
    dataIndex: "isSamplePrice_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isSamplePrice_" },
  },
  {
    title: "平米价",
    dataIndex: "isSquareMeterPrice_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isSquareMeterPrice_" },
  },
  {
    title: "其他费",
    dataIndex: "isOtherPrice_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isOtherPrice_" },
  },
  {
    title: "编辑计算",
    dataIndex: "isDetailEdit_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isDetailEdit_" },
  },
  {
    title: "编辑加减",
    dataIndex: "isCollectEdit_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isCollectEdit_" },
  },
  {
    title: "编辑实际报价",
    dataIndex: "isActualEdit_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isActualEdit_" },
  },
  {
    title: "选择",
    dataIndex: "isChooseEdit_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isChooseEdit_" },
  },
  {
    title: "客规条件",
    dataIndex: "isCustNo_",
    width: 80,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isCustNo_" },
  },
];
const columns7 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "left",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "计算条件",
    dataIndex: "calcCondition_",
    width: 400,
    ellipsis: true,
    align: "left",
  },
  {
    title: "计算条件说明",
    dataIndex: "captions_",
    width: 150,
    ellipsis: true,
    align: "left",
  },
];
const columns8 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "客户详情",
    dataIndex: "custNameStr",
    width: 200,
    ellipsis: true,
    align: "left",
  },
];
const columns9 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 60,
    customRender: (text, record, index) => `${index + 1}`,
  },
  // {
  //   title: "条数始",
  //   dataIndex: "",
  //   ellipsis: true,
  //   align: "left",
  // },
  {
    title: "条数末",
    dataIndex: "count4To_",
    ellipsis: true,
    align: "left",
  },
  {
    title: "<=10u",
    dataIndex: "price_A",
    ellipsis: true,
    align: "left",
  },
  {
    title: "15u",
    dataIndex: "price_B",
    ellipsis: true,
    align: "left",
  },
  {
    title: "20u",
    dataIndex: "price_C",
    ellipsis: true,
    align: "left",
  },
  {
    title: "30u",
    dataIndex: "price_D",
    ellipsis: true,
    align: "left",
  },
  {
    title: "40u",
    dataIndex: "price_E",
    ellipsis: true,
    align: "left",
  },
  {
    title: "50u",
    dataIndex: "price_F",
    ellipsis: true,
    align: "left",
  },
];

export default {
  name: "PriceSystem",
  components: { BackendAction, LeftTable, centerTable, OrderDetail, QueryInfoBackend, costInfo, customerInfo },
  inject: ["reload"],
  data() {
    return {
      ttype: "",
      confirmLoading: false,
      spinning: false,
      pagination: {
        pageSize: 20,
        pageIndex: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      columns1,
      columns2,
      columns3,
      columns4,
      columns5,
      columns6,
      columns7,
      columns8,
      columns9,
      dataSource1: [],
      dataSource2: [],
      dataSource3: [],
      dataSource4: [],
      dataSource5: [],
      dataSource6: [],
      dataSource7: [],
      dataSource8: [],
      dataSource9: [],
      dataSource10: [],
      selectOption: {},
      dropdownselection: [],
      orderListTableLoading: false,
      TableLoading2: false,
      params1: {},
      TableLoading3: false,
      TableLoading4: false,
      TableLoading5: false,
      TableLoading6: false,
      TableLoading7: false,
      TableLoading8: false,
      TableLoading9: false,
      assignLoading: false,
      dataVisible: false, // 查询弹窗开关
      dataVisibleMode: false,
      messageMode: "",
      orderno: "",
      dataVisible1: false,
      dataVisible2: false,
      titleList: "新增",
      widthy: 600,
      activeKey: "1",
      editFlg: false,
      LeftSelectRowData: {},
      selectRowData: {},
      sselectRowData: {},
      disbackend: false,
      isCtrlPressed: false,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.getOrderList();
      this.getData();
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("resize", this.dehandleResize, true);
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.dehandleResize, true);
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  methods: {
    checkPermission,
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        e.preventDefault();
        this.reportHandleCancel();
        this.queryClick();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.dataVisibleMode) {
        this.handleOkMode();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    handleResize() {
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var centerstyle =
        document.getElementsByClassName("centerstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var pag = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[1];
      if (this.activeKey == 1) {
        var rightstyle1 =
          document.getElementsByClassName("rightstyle1")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
        if (rightstyle1 && this.dataSource3.length != 0) {
          rightstyle1.style.height = window.innerHeight - 228 + "px";
        } else {
          rightstyle1.style.height = 0;
        }
      } else if (this.activeKey == 2) {
        var rightstyle2 =
          document.getElementsByClassName("rightstyle2")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
        if (rightstyle2 && this.dataSource4.length != 0) {
          rightstyle2.style.height = window.innerHeight - 228 + "px";
        } else {
          rightstyle2.style.height = 0;
        }
      } else if (this.activeKey == 7) {
        var rightstyle3 =
          document.getElementsByClassName("rightstyle3")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
        if (rightstyle3 && this.dataSource8.length != 0) {
          rightstyle3.style.height = window.innerHeight - 228 + "px";
        } else {
          rightstyle3.style.height = 0;
        }
      } else if (this.activeKey == 6) {
        var rightstyle4 =
          document.getElementsByClassName("rightstyle4")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
        if (rightstyle4 && this.dataSource2.length != 0) {
          rightstyle4.style.height = window.innerHeight - 228 + "px";
        } else {
          rightstyle4.style.height = 0;
        }
      }
      var leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 150 + "px";
      } else {
        leftContent.style.height = "771px";
      }
      if (leftstyle && this.dataSource1.length != 0) {
        leftstyle.style.height = window.innerHeight - 191 + "px";
      } else {
        leftstyle.style.height = 0;
      }
      if (centerstyle && this.dataSource2.length != 0) {
        centerstyle.style.height = window.innerHeight - 191 + "px";
      } else {
        centerstyle.style.height = 0;
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (pag) {
        pag.style.width = paginnum * 50 + 360 + "px";
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.action.nums * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }

        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    getData() {
      getselect().then(res => {
        if (res.code) {
          this.selectOption = res.data;
          console.log("this.selectOption", this.selectOption);
        } else {
          this.$message.error(res.message);
        }
      });
      mktcustno().then(res => {
        if (res.code) {
          this.dropdownselection = this.convertToArray(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    convertToArray(objct) {
      return Object.keys(objct).map(key => ({ id: key, value: objct[key] }));
    },
    handleOkMode() {
      if (this.activeKey == "1") {
        delBasicPrice(this.selectRowData.id).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getOrderDetail(this.LeftSelectRowData, "1");
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.activeKey == "2") {
        delEngPrice(this.selectRowData.id).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getOrderDetail(this.LeftSelectRowData, "2");
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.activeKey == "4") {
        delformula(this.selectRowData.id).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getOrderDetail(this.LeftSelectRowData, "4");
          } else {
            this.$message.error(res.message);
          }
        });
      }

      if (this.activeKey == "6") {
        let val = {
          calcAreaID_: this.LeftSelectRowData.id,
          id: this.selectRowData.id,
        };
        delareacust(val).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getOrderDetail(this.LeftSelectRowData, "7");
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.activeKey == "7") {
        delgoldfingerprice(this.selectRowData.id).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getOrderDetail(this.LeftSelectRowData, "7");
          } else {
            this.$message.error(res.message);
          }
        });
      }
      this.dataVisibleMode = false;
    },
    // 获取订单
    getOrderList(queryData) {
      let params = {
        pageSize: this.pagination.pageSize,
        pageIndex: this.pagination.pageIndex,
      };
      if (queryData) {
        params.CustNo = queryData.CustNo;
      }
      this.orderListTableLoading = true;
      pricemanage4AreaiD().then(res => {
        if (res.code) {
          this.dataSource9 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
      mktPriceManage4AreaID(params)
        .then(res => {
          if (res.code) {
            this.dataSource1 = res.data.items;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            this.pagination.total = res.data.totalCount;
            if (queryData && this.dataSource1.length) {
              this.$refs.orderTable.selectedRowsData = this.dataSource1[0];
              this.$refs.orderTable.proOrderId = this.dataSource1[0].id;
              this.$refs.orderTable.selectedRowKeysArray = [this.dataSource1[0].id];
              this.getOrderDetail(this.dataSource1[0], "1");
            } else {
              this.dataSource2 = [];
              this.dataSource3 = [];
              this.dataSource4 = [];
              this.dataSource8 = [];
            }
            this.dataSource1.forEach(item => {
              if (item.isReOrder == 0) {
                item.isReOrder = false;
              } else {
                item.isReOrder = true;
              }
            });
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    getOrderDetail(record, queryData) {
      this.$refs.orderDetail.id = "";
      this.selectRowData = {};
      this.LeftSelectRowData = record;
      this.TableLoading2 = true;
      this.TableLoading3 = true;
      let CustNo = "";
      let id = "";
      if (queryData == "3") {
        CustNo = record.CustNo;
      } else {
        id = record.id;
      }
      mktgoldfingerprice().then(res => {
        if (res.code) {
          this.dataSource8 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
      mkTPriceRuleData4CalcCustArea(id, CustNo)
        .then(res => {
          if (res.code) {
            this.dataSource2 = res.data.calcCustAreaList;
            this.dataSource3 = res.data.calcBasicPriceList; // 制版
            this.dataSource4 = res.data.calcENGPriceList; // 工程
            this.dataSource4Copy = res.data.calcENGPriceList;
            this.dataSource5 = res.data.calcOTherPriceList; // 其他
            this.dataSource6 = res.data.calcPriceFormulaList; // 区域公式
            this.dataSource7 = res.data.calcPriceFormulaAreaList; // 分配公式
            setTimeout(() => {
              this.handleResize();
            }, 0);
            if (queryData == "3") {
              this.dataSource1 = this.dataSource1.filter(item => {
                return item.id == this.dataSource2[0].calcAreaID;
              });
              this.$refs.orderTable.proOrderId = this.dataSource1[0].id;
            }
            // if(this.selectRowData.id){
            //   this.selectRowData = this.dataSource3.filter(item =>{return item.id == this.selectRowData.id})[0]
            // }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.TableLoading2 = false;
          this.TableLoading3 = false;
        });
    },
    // 订单表变化change
    handleTableChange(pagination) {
      this.pagination.pageIndex = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      this.pageStat = false;
      localStorage.removeItem("stat");
      // this.getOrderList()
      if (JSON.stringify(this.params1) != "{}") {
        this.getOrderList(this.params1);
      } else {
        this.getOrderList();
      }
    },
    // 选项卡变化change
    tabpaneChange(activeKey) {
      this.activeKey = activeKey;
      setTimeout(() => {
        this.handleResize();
        this.$refs.action.handleResize();
      }, 0);
      this.selectRowData = {};
      this.selectRowData.id = "";
    },
    //客户区域点击新增
    newlyadded() {
      this.ttype = "1";
      this.$refs.customerInfo.aform = {};
    },
    editlyadded() {
      if (!this.$refs.customerInfo.id) {
        this.$message.warning("请选择需要编辑的行");
        return;
      }
      this.ttype = "2";
      this.$refs.customerInfo.aform = this.sselectRowData;
    },
    //客户区域点击取消
    cancellation() {
      this.ttype = "3";
      this.getOrderList();
      this.$refs.customerInfo.id = null;
    },
    //点击保存
    saveclick() {
      let info = this.$refs.customerInfo.aform;
      let params = info;
      if (params.id) {
        params.id = Number(params.id);
      } else {
        params.id = null;
      }
      if (params.typeNo_) {
        params.typeNo_ = Number(params.typeNo_);
      } else {
        params.typeNo_ = null;
      }
      if (params.listNo_) {
        params.listNo_ = Number(params.listNo_);
      }
      if (params.display_) {
        params.display_ = Number(params.display_);
      }
      if (this.ttype == "1") {
        newpricemanage4AreaiD(params).then(res => {
          if (res.code) {
            this.$message.success("新增成功");
            this.getOrderList();
            this.ttype = "3";
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "2") {
        uppricemanage4AreaiD(params).then(res => {
          if (res.code) {
            this.$message.success("编辑成功");
            this.ttype = "3";
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible = false; // 查询
      this.dataVisible1 = false;
      this.dataVisibleMode = false;
      this.dataVisible2 = false;
      if (this.$refs.customerInfo) {
        this.$refs.customerInfo.aform = {};
        this.$refs.customerInfo.id = "";
      }
    },
    // 查询
    queryClick() {
      this.dataVisible = true;
      this.pagination.pageIndex = 1;
    },
    handleOk() {
      this.dataVisible = false;
      let params = this.$refs.queryInfo.form;
      this.params1 = this.$refs.queryInfo.form;
      if (params.CustNo) {
        var arr1 = params.CustNo.split("");
        if (arr1.length > 30) {
          arr1 = arr1.slice(0, 30);
        }
        params.CustNo = arr1.join("");
        this.getOrderList(params);
        // this.getOrderDetail(params,'3')
      }
    },
    handleOk1() {
      var r = /^\+?[1-9][0-9]*$/;
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      let info = this.$refs.costInfo.form;
      const form = this.$refs.costInfo.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          if (this.activeKey == "1") {
            let params = info.calcBasicPriceList;
            this.dataVisible1 = false;
            if (this.editFlg) {
              params.id = this.selectRowData.id;
              params.areaID4Price_ = this.selectRowData.areaID4Price_;
              params.layer_ = params.layer_.toString();
              upbBasicPrice(params).then(res => {
                if (res.code) {
                  this.$message.success("编辑成功");
                  this.getOrderDetail(this.LeftSelectRowData, "1");
                } else {
                  this.$message.error(res.message);
                }
              });
            } else {
              params.areaID4Price_ = this.LeftSelectRowData.id;
              basicPrice(params).then(res => {
                if (res.code) {
                  this.$message.success("新增成功");
                  this.getOrderDetail(this.LeftSelectRowData, "1");
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          }
          if (this.activeKey == "2") {
            let params = info.calcENGPriceList;
            // if(!params.layer_  || ! r.test(params.layer_)){
            //   this.$message.warning('请填写层数为正整数')
            // return}
            this.dataVisible1 = false;
            if (this.editFlg) {
              params.id = this.selectRowData.id;
              params.areaID4Price_ = this.selectRowData.areaID4Price_;
              params.layer_ = params.layer_.toString();
              upbEngPrice(params).then(res => {
                if (res.code) {
                  this.$message.success("编辑成功");
                  this.getOrderDetail(this.LeftSelectRowData, "2");
                } else {
                  this.$message.error(res.message);
                }
              });
            } else {
              params.areaID4Price_ = this.LeftSelectRowData.id;
              engPrice(params).then(res => {
                if (res.code) {
                  this.$message.success("新增成功");
                  this.getOrderDetail(this.LeftSelectRowData, "2");
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          }
          if (this.activeKey == "4") {
            let params = info.calcPriceFormulaList;
            if (!params.calcNameID_) {
              this.$message.warning("请选择判断类别");
              return;
            }
            if (!params.conditionType_) {
              this.$message.warning("请选择判断类型");
              return;
            }

            if (!params.conditionAreaID_) {
              this.$message.warning("请选择判断区域");
              return;
            }
            if (!params.condition_) {
              this.$message.warning("请填写判断公式");
              return;
            }
            if (!params.expressionType_) {
              this.$message.warning("请选择结果类型");
              return;
            }
            if (!params.expressionAreaID_) {
              this.$message.warning("请选择结果区域");
              return;
            }
            if (!params.expression_) {
              this.$message.warning("请填写结果公式");
              return;
            }
            if (!params.captions_) {
              this.$message.warning("请填写说明1");
              return;
            }
            if (!params.basicPriceAreaID_) {
              this.$message.warning("请选择基价类型区域");
              return;
            }
            if (!params.isChk_) {
              this.$message.warning("请选择条件是否有效");
              return;
            }
            if (!params.calcDisplays_) {
              this.$message.warning("请填写显示排序");
              return;
            }
            if (params.calcNameID_) {
              params.calcNameID_ = Number(params.calcNameID_);
            } else {
              params.calcNameID_ = null;
            }
            if (params.conditionType_) {
              params.conditionType_ = Number(params.conditionType_);
            } else {
              params.conditionType_ = null;
            }
            if (params.conditionAreaID_) {
              params.conditionAreaID_ = Number(params.conditionAreaID_);
            } else {
              params.conditionAreaID_ = null;
            }
            if (params.expressionType_) {
              params.expressionType_ = Number(params.expressionType_);
            } else {
              params.expressionType_ = null;
            }
            if (params.expressionAreaID_) {
              params.expressionAreaID_ = Number(params.expressionAreaID_);
            } else {
              params.expressionAreaID_ = null;
            }
            if (params.inUserNo_) {
              params.inUserNo_ = Number(params.inUserNo_);
            } else {
              params.inUserNo_ = null;
            }
            if (params.basicPriceType_) {
              params.basicPriceType_ = Number(params.basicPriceType_);
            } else {
              params.basicPriceType_ = null;
            }
            if (params.basicPriceAreaID_) {
              params.basicPriceAreaID_ = Number(params.basicPriceAreaID_);
            } else {
              params.basicPriceAreaID_ = null;
            }
            if (params.basicPrice4MinType_) {
              params.basicPrice4MinType_ = Number(params.basicPrice4MinType_);
            } else {
              params.basicPrice4MinType_ = null;
            }
            if (params.basicPrice4MinAreaID_) {
              params.basicPrice4MinAreaID_ = Number(params.basicPrice4MinAreaID_);
            } else {
              params.basicPrice4MinAreaID_ = null;
            }
            if (params.basicPrice4MaxType_) {
              params.basicPrice4MaxType_ = Number(params.basicPrice4MaxType_);
            } else {
              params.basicPrice4MaxType_ = null;
            }
            if (params.basicPrice4MaxAreaID_) {
              params.basicPrice4MaxAreaID_ = Number(params.basicPrice4MaxAreaID_);
            } else {
              params.basicPrice4MaxAreaID_ = null;
            }
            if (params.isChk_) {
              params.isChk_ = Number(params.isChk_);
            } else {
              params.isChk_ = null;
            }
            if (params.calcFormulaType_) {
              params.calcFormulaType_ = Number(params.calcFormulaType_);
            } else {
              params.calcFormulaType_ = null;
            }
            if (params.calcDisplays_) {
              params.calcDisplays_ = Number(params.calcDisplays_);
            } else {
              params.calcDisplays_ = null;
            }
            if (params.calcArea_) {
              params.calcArea_ = Number(params.calcArea_);
            } else {
              params.calcArea_ = null;
            }
            this.dataVisible1 = false;
            if (this.editFlg) {
              params.id = this.selectRowData.id;
              params.areaID4Price_ = this.selectRowData.areaID4Price_;
              upformula(params).then(res => {
                if (res.code) {
                  this.$message.success("编辑成功");
                  this.getOrderDetail(this.LeftSelectRowData, "4");
                } else {
                  this.$message.error(res.message);
                }
              });
            } else {
              params.calcArea_ = this.LeftSelectRowData.id;
              formula(params).then(res => {
                if (res.code) {
                  this.$message.success("新增成功");
                  this.getOrderDetail(this.LeftSelectRowData, "4");
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          }
          if (this.activeKey == "3") {
            this.dataVisible1 = false;
          }
          if (this.activeKey == "5") {
            this.dataVisible1 = false;
          }
          if (this.activeKey == "6") {
            let params = info.calcCustAreaList;
            let val = {
              calcAreaID_: this.LeftSelectRowData.id,
              id: params.custNameStr,
            };
            this.dataVisible1 = false;
            areaCust(val).then(res => {
              if (res.code) {
                this.$message.success("新增成功");
                this.getOrderDetail(this.LeftSelectRowData, "6");
              } else {
                this.$message.error(res.message);
              }
            });
          }
          if (this.activeKey == "7") {
            let params = info.goldfinger;
            if (params.count4To_) {
              params.count4To_ = Number(params.count4To_);
            }
            if (params.price_A) {
              params.price_A = Number(params.price_A);
            }
            if (params.price_B) {
              params.price_B = Number(params.price_B);
            }
            if (params.price_C) {
              params.price_C = Number(params.price_C);
            }
            if (params.price_D) {
              params.price_D = Number(params.price_D);
            }
            if (params.price_E) {
              params.price_E = Number(params.price_E);
            }
            if (params.price_F) {
              params.price_F = Number(params.price_F);
            }
            this.dataVisible1 = false;
            if (this.editFlg) {
              params.id = this.selectRowData.id;
              params.areaID4Price_ = this.selectRowData.areaID4Price_;
              upgoldfingerprice(params).then(res => {
                if (res.code) {
                  this.$message.success("编辑成功");
                  this.getOrderDetail(this.LeftSelectRowData, "7");
                } else {
                  this.$message.error(res.message);
                }
              });
            } else {
              params.count4From_ = this.$refs.orderTable.proOrderId;
              goldfingerprice(params).then(res => {
                if (res.code) {
                  this.$message.success("新增成功");
                  this.getOrderDetail(this.LeftSelectRowData, "7");
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          }
        }
      });
    },
    sselectRow(record) {
      this.sselectRowData = record;
    },
    selectRow(record) {
      this.selectRowData = record;
      if (this.selectRowData.calcNameID_) {
        this.selectRowData.calcNameID_ = this.selectRowData.calcNameID_.toString();
      }
      if (this.selectRowData.conditionAreaID_) {
        this.selectRowData.conditionAreaID_ = this.selectRowData.conditionAreaID_.toString();
      }
      if (this.selectRowData.conditionType_) {
        this.selectRowData.conditionType_ = this.selectRowData.conditionType_.toString();
      }
      if (this.selectRowData.isChk_) {
        this.selectRowData.isChk_ = this.selectRowData.isChk_.toString();
      }
      if (this.selectRowData.expressionType_) {
        this.selectRowData.expressionType_ = this.selectRowData.expressionType_.toString();
      }
      if (this.selectRowData.expressionAreaID_) {
        this.selectRowData.expressionAreaID_ = this.selectRowData.expressionAreaID_.toString();
      }
      if (this.selectRowData.basicPriceType_) {
        this.selectRowData.basicPriceType_ = this.selectRowData.basicPriceType_.toString();
      }
      if (this.selectRowData.basicPriceAreaID_) {
        this.selectRowData.basicPriceAreaID_ = this.selectRowData.basicPriceAreaID_.toString();
      }
      if (this.selectRowData.basicPrice4MinType_) {
        this.selectRowData.basicPrice4MinType_ = this.selectRowData.basicPrice4MinType_.toString();
      }
      if (this.selectRowData.basicPrice4MinAreaID_) {
        this.selectRowData.basicPrice4MinAreaID_ = this.selectRowData.basicPrice4MinAreaID_.toString();
      }
      if (this.selectRowData.basicPrice4MaxType_) {
        this.selectRowData.basicPrice4MaxType_ = this.selectRowData.basicPrice4MaxType_.toString();
      }
      if (this.selectRowData.basicPrice4MaxAreaID_) {
        this.selectRowData.basicPrice4MaxAreaID_ = this.selectRowData.basicPrice4MaxAreaID_.toString();
      }
    },
    //客户区域
    customerArea() {
      this.dataVisible2 = true;
      this.ttype = "3";
    },
    addClick() {
      if (!this.LeftSelectRowData.id) {
        this.$message.warning("请选择价格区域行");
        return;
      }
      this.dataVisible1 = true;
      this.editFlg = false;
      if (this.activeKey == "1") {
        this.titleList = "新增制板费";
        this.widthy = 600;
      }
      if (this.activeKey == "2") {
        this.titleList = "新增工程费";
        this.widthy = 500;
      }
      if (this.activeKey == "4") {
        this.titleList = "新增区域公式";
      }
      if (this.activeKey == "5") {
        this.titleList = "新增分配公式";
      }
      if (this.activeKey == "3") {
        this.titleList = "新增其他费用";
      }
      if (this.activeKey == "6") {
        this.titleList = "新增区域对接客户";
        this.widthy = 400;
      }
      if (this.activeKey == "7") {
        this.titleList = "新增金手指价";
        this.widthy = 600;
      }
    },
    editClick() {
      if (!this.LeftSelectRowData.id) {
        this.$message.warning("请选择价格区域行");
        return;
      }
      if (!this.selectRowData.id) {
        this.$message.warning("请选择需要编辑的行");
        return;
      }
      this.editFlg = true;
      this.dataVisible1 = true;
      if (this.activeKey == "1") {
        this.titleList = "编辑制板费";
      }
      if (this.activeKey == "2") {
        this.titleList = "编辑工程费";
      }
      if (this.activeKey == "4") {
        this.titleList = "编辑区域公式";
      }
      if (this.activeKey == "5") {
        this.titleList = "编辑分配公式";
      }
      if (this.activeKey == "3") {
        this.titleList = "编辑其他费用";
      }
      if (this.activeKey == "6") {
        this.titleList = "编辑区域对接客户";
      }
      if (this.activeKey == "7") {
        this.titleList = "编辑金手指价";
      }
    },
    delClick() {
      if (!this.LeftSelectRowData.id) {
        this.$message.warning("请选择价格区域行");
        return;
      }
      if (!this.selectRowData.id) {
        this.$message.warning("请选择需要删除的行");
        return;
      }
      this.dataVisibleMode = true;
      this.messageMode = "确认删除吗？";
    },
    ExportPrice() {
      if (!this.LeftSelectRowData.id) {
        this.$message.warning("请选择价格区域行");
        return;
      }
      exportPrice(this.LeftSelectRowData.id).then(res => {
        if (res.code) {
          let basicPriceDto = JSON.parse(res.data.basicPriceDto);
          let engPriceDto = JSON.parse(res.data.engPriceDto);
          if (basicPriceDto.length || engPriceDto.length) {
            let data = [];
            let sheetName = [];
            if (basicPriceDto.length) {
              data = basicPriceDto;
              sheetName = ["制版费"];
            }
            if (engPriceDto.length) {
              data = engPriceDto;
              sheetName = ["工程费"];
            }
            if (basicPriceDto.length && engPriceDto.length) {
              data = [basicPriceDto, engPriceDto];
              sheetName = ["制版费", "工程费"];
            }
            this.exportExcelFile(data, sheetName, this.LeftSelectRowData.id + "-价格表.xlsx");
          } else {
            this.$message.error("暂无数据");
          }
        }
      });
    },
    exportExcelFile(array, sheetName, fileName) {
      const workBook = {
        SheetNames: sheetName,
        Sheets: {},
      };
      sheetName.forEach((iten, j) => {
        console.log(array[j], "array[j]");
        workBook.Sheets[iten] = XLSX.utils.json_to_sheet(array[j]);
      });
      return XLSX.writeFile(workBook, fileName);
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-form-item label {
  font-family: PingFangSC-Regular, Sans-serif !important;
  font-size: 14px !important;
  color: #000000;
}
/deep/a,
area,
button,
[role="button"],
input:not([type="range"]),
label,
select,
summary,
textarea {
  color: #000000 !important;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
.projectBackend {
  /deep/.userStyle {
    user-select: none !important;
  }
  background: #ffffff;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  /deep/.leftContent {
    width: 20%;
    border: 2px solid rgb(233, 233, 240);
  }
  /deep/.center {
    width: 20%;
    border: 2px solid rgb(233, 233, 240);
  }

  /deep/ .rightContent {
    border: 2px solid #e9e9f0;
    width: 60%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .centerTable {
      width: 100%;
    }
  }
  .footerAction {
    width: 100%;
    height: 51px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container {
      height: 36px;
      .ant-tabs-tab {
        margin: 0;
        height: 36px;
        line-height: 36px;
      }
    }
  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
      .eqBackground {
        background: #ffff00;
      }
      .statuBackground {
        background: #b0e0e6;
      }
      .backUserBackground {
        background: #a7a2c9;
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    /deep/.userStyle {
      user-select: none !important;
    }
    //td[class~='userStyle'] {
    //  user-select: all !important;
    //}
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
      // border-color: #f0f0f0;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px !important;
      border-right: 1px solid #efefef;
      // border-color: #f0f0f0;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
        color: #000000;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 18px 0 0 10px;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
<style lang="less">
.note {
  .textNote {
    padding-top: 4px;
    background: #d6d6d6;
    .imgTable {
      img {
        width: 100px;
        height: 50px;
      }
    }
    p {
      height: 100%;
      line-height: 35px;
      font-weight: 700;
      margin: 0;
    }
    .divClass {
      margin: 0 5px;
    }
    .displayFlag {
      display: none;
    }
  }
}
</style>
