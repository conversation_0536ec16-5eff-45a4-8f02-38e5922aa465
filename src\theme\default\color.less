@import '~ant-design-vue/lib/style/themes/default';

@gray-1: #ffffff;
@gray-2: #fafafa;
@gray-3: #f5f5f5;
@gray-4: #f0f0f0;
@gray-5: #d9d9d9;
@gray-6: #bfbfbf;
@gray-7: #8c8c8c;
@gray-8: #595959;
@gray-9: #434343;
@gray-10: #262626;
@gray-11: #1f1f1f;
@gray-12: #141414;
@gray-13: #000000;

@primary-color: @primary-color;
@success-color: @success-color;
@warning-color: @warning-color;
@error-color: @warning-color;

@title-color: @heading-color;
@text-color: @text-color;
@text-color-second: @text-color-secondary;

@layout-bg-color: @layout-body-background;
@base-bg-color: @body-background;
@hover-bg-color: rgba(0, 0, 0, 0.025);
@border-color: @border-color-split;
@shadow-color: @shadow-color;

@text-color-inverse: @text-color-inverse;
@hover-bg-color-light: @hover-bg-color;
@hover-bg-color-dark: @primary-7;
@hover-bg-color-night: rgba(255, 255, 255, 0.025);
@header-bg-color-dark: @layout-header-background;

@shadow-down: @shadow-1-down;
@shadow-up: @shadow-1-up;
@shadow-left: @shadow-1-left;
@shadow-right: @shadow-1-right;

@theme-list: light, dark, night;
