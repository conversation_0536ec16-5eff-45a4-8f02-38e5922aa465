<!--生产管理- 订单详情-拼版图查看 -->
<template>
  <div class="imposition-information imposition-informationExample rel">
    <div class="imposition-information-item rel">
      <p class="f14 black bold text-center mb10">拼版示意图：↓ (仅供参考)<span class="f12 cl-b16a00 text-center normal ml10">缩放比例：<em class="proportion bold"></em>/1</span></p>
      <div class="panel-x mb10"><p class="number f12"><span><em class="panel-width">0.00</em> mm</span></p></div>
      <div class="panel-box">
        <div class="edgerailwidth edgerailwidth-top"></div>
        <div class="panel-item clearfix">
          <div class="edgerailwidth edgerailwidth-left pull-left"></div>
          <div class="example-createpanel pull-left"></div>
          <div class="edgerailwidth edgerailwidth-right pull-left"></div>
        </div>
        <div class="edgerailwidth edgerailwidth-bottom"></div>
      </div>
      <div class="panel-y"><p class="number f12"><span><em class="panel-height">0.00</em><br /> mm</span></p></div>
    </div>

    <p class="cl-b16a00 mt10">高：<span class="board-height f14 bold">0.00</span>mm<br />宽：<span class="board-width f14 bold">0.00</span>mm 方式拼版<br />工艺边：<span class="edgerail-direction f14 bold">无</span>，工艺边宽度：<span class="edgerail-width f14 bold">0</span>mm</p>

    <div class="cao_info cl-b16a00 mt10" style="display:none;">水平槽间距：<span class="cao_x f14 bold">2</span>mm，垂直槽间距：<span class="cao_y f14 bold">2</span>mm</div>
  </div>
</template>

<script>
import $ from 'jquery';
export default {
  name: "MakeupPic",
  methods:{
    impositionInformationExample(lHeight, wWidth, pxV, pyH, edgerailWay, processeedgeywidth, vcut, cao_x, cao_y) {
      console.log(lHeight, wWidth, pxV, pyH, edgerailWay, processeedgeywidth, vcut, cao_x, cao_y)
      //name=BoardHeight 高度
      var l = lHeight;
      // name=BoardWidth 宽度
      var w = wWidth;
      // name=pinban_x 垂直方向
      var px = pxV;
      //name=pinban_y 水平方向
      var py = pyH;
      //工艺边 
      var EdgeRail = edgerailWay || "none";
      //工艺边宽度
      var processeEdge_yWidth = processeedgeywidth || 0;
      ////锣槽
      var VCutValue = vcut;
      var edgerailWidth;
      if (EdgeRail == "none") {
        edgerailWidth = "无";
      }
      if (EdgeRail == "leftright") {
        edgerailWidth = "左右方向";
      }
      if (EdgeRail == "updown") {
        edgerailWidth = "上下方向";
      }
      if (EdgeRail == "both") {
        edgerailWidth = "四边方向";
      }
      var c = l * px ; var k = w * py ;

      $(".cao_info").hide();
      if (l > 0 && w > 0 && px > 0 && py > 0) {
      var panelWidth = w * py;
      var panelHeight = l * px;
      var panellistLength = $(".panel-list").length;
      if (panellistLength > 0) {
        $(".panel-list").remove();
      }
      var exampleCreatePanel = ("<ul class='panel-list' style='padding: 0;margin:0'>");
      for (var i = 0; i < px; i++) {
        var examplePanelLi = "<li>";
        for (var j = 0; j < py; j++) {
          var examplePanelSpan = ("<span class='item pull-left'></span>");
          examplePanelLi += examplePanelSpan;
        }
        examplePanelLi += "</li>";
        exampleCreatePanel += examplePanelLi;
      }
      exampleCreatePanel += "</ul>";
      $(".example-createpanel").html(exampleCreatePanel);
        var Proportion,itemWidth,itemHeight;
      if (panelWidth > panelHeight) {
         Proportion= parseFloat(280 / panelWidth).toFixed(2);
         itemWidth = 280 / py;
         itemHeight = panelHeight * Proportion / px;
      } else {
         Proportion = parseFloat(280 / panelHeight).toFixed(2);
         itemHeight = 280 / px;
         itemWidth = panelWidth * Proportion / py;
      }

      $(".imposition-informationExample .proportion").text(Proportion);
      $(".panel-list .item").css({ "width": itemWidth, "height": itemHeight });

      var panelyWidth = itemWidth * py;
      var panelyHeight = itemHeight * px;
      var edgerailHeight = panelyHeight;

      if (EdgeRail != "none" && parseFloat(processeEdge_yWidth) < 3) {
        $("[name=processeEdge_y]").val(3);
      }
      var processeEdge_y = processeEdge_yWidth;
      if (EdgeRail == "none") {
        c = l * px ;
        k = w * py ;
        panelyHeight = itemHeight * px;
        $(".imposition-informationExample .edgerailwidth-left").css({ "position": "relative", "z-index": "-10" });
        $(".imposition-informationExample .edgerailwidth-right").css({ "position": "relative", "z-index": "-10" });
        $(".imposition-informationExample .edgerailwidth-top").css({ "position": "relative", "z-index": "-10" });
        $(".imposition-informationExample .edgerailwidth-bottom").css({ "position": "relative", "z-index": "-10" });
      }
      if (EdgeRail == "updown") {
        c += processeEdge_y * 2;
        processeEdge_y = 5;
        panelyHeight = itemHeight * px + (processeEdge_y * 2);
        edgerailHeight = panelyHeight - (processeEdge_y * 2);
        $(".imposition-informationExample .edgerailwidth-left").css({ "position": "relative", "z-index": "-10" });
        $(".imposition-informationExample .edgerailwidth-right").css({ "position": "relative", "z-index": "-10" });
        $(".imposition-informationExample .edgerailwidth-top").css({ "position": "relative", "z-index": "10" });
        $(".imposition-informationExample .edgerailwidth-bottom").css({ "position": "relative", "z-index": "10" });
        $(".example-createpanel").css("margin-left", -processeEdge_y);
      }
      if (EdgeRail == "leftright") {
        k += processeEdge_y * 2;
        processeEdge_y = 5;
        panelyWidth = itemWidth * py + (processeEdge_y * 2);
        panelyHeight = itemHeight * px;
        edgerailHeight = panelyHeight;
        $(".imposition-informationExample .edgerailwidth-left").css({ "position": "relative", "z-index": "10" });
        $(".imposition-informationExample .edgerailwidth-right").css({ "position": "relative", "z-index": "10" });
        $(".imposition-informationExample .edgerailwidth-top").css({ "position": "relative", "z-index": "-10" });
        $(".imposition-informationExample .edgerailwidth-bottom").css({ "position": "relative", "z-index": "-10" });
        $(".imposition-information .panel-y").css("top", 74 + processeEdge_y);
      }
      if (EdgeRail == "both") {
        k += processeEdge_y * 2;
        c += processeEdge_y * 2;
        processeEdge_y = 5;
        panelyWidth = itemWidth * py + (processeEdge_y * 2);
        panelyHeight = itemHeight * px + (processeEdge_y * 2);
        edgerailHeight = panelyHeight - (processeEdge_y * 2);
        $(".imposition-informationExample .edgerailwidth-left").css({ "position": "relative", "z-index": "10" });
        $(".imposition-informationExample .edgerailwidth-right").css({ "position": "relative", "z-index": "10" });
        $(".imposition-informationExample .edgerailwidth-top").css({ "position": "relative", "z-index": "10" });
        $(".imposition-informationExample .edgerailwidth-bottom").css({ "position": "relative", "z-index": "10" });
      }
      // var luocaoY = (py - 1) * 0.8;
      // var luocaoX = (px - 1) * 0.8;
      // if (VCutValue == "luocao" || VCutValue == "vcutluocao") {
      //    k += luocaoY;
      //    c += luocaoX;
      //    $(".luocaoY").text(parseFloat(luocaoX).toFixed(2));
      //    $(".luocaoX").text(parseFloat(luocaoY).toFixed(2));
      // }
      // $(".panelWidth").text(parseFloat(k).toFixed(2));
      // $(".panelHeight").text(parseFloat(c).toFixed(2));

      $(".imposition-informationExample .edgerailwidth-left").css({ "width": processeEdge_y, "height": edgerailHeight });
      $(".imposition-informationExample .edgerailwidth-right").css({ "width": processeEdge_y, "height": edgerailHeight });
      $(".imposition-informationExample .edgerailwidth-top").css({ "width": panelyWidth, "height": processeEdge_y });
      $(".imposition-informationExample .edgerailwidth-bottom").css({ "width": panelyWidth, "height": processeEdge_y });
      $(".imposition-informationExample .panel-x").css("width", panelyWidth);
      $(".imposition-informationExample .panel-y .number").css("height", panelyHeight);

      if (VCutValue.indexOf("luocao") >= 0) {
        k += (py - 1) * cao_y;
        c += (px - 1) * cao_x;

        var ppy = parseInt(py);
        var ppx = parseInt(px);

        if (EdgeRail == "leftright") {
          k += 2 * cao_y;
          ppy += 2;
        }

        if (EdgeRail == "updown") {
          c += 2 * cao_x;
          ppx += 2;
        }

        if (EdgeRail == "both") {
          k += 2 * cao_y;
          c += 2 * cao_x;
          ppy += 2;
          ppx += 2;
        }

        $(".imposition-informationExample .panel-width").text(parseFloat(k).toFixed(2));
        $(".imposition-informationExample .panel-height").text(parseFloat(c).toFixed(2));

        $(".board-height").text(parseFloat(l  * px).toFixed(2) + " + " + (ppx - 1) + "x" + cao_x + "(槽宽)");
        $(".board-width").text(parseFloat(w  * py).toFixed(2) + " + " + (ppy - 1) + "x" + cao_y + "(槽宽)");

        $(".cao_info").show();
        $(".cao_x").text(cao_x);
        $(".cao_y").text(cao_y);
      }else {
        $(".imposition-informationExample .panel-width").text(parseFloat(k).toFixed(2));
        $(".imposition-informationExample .panel-height").text(parseFloat(c).toFixed(2));

        $(".board-height").text(parseFloat(l  * px).toFixed(2));
        $(".board-width").text(parseFloat(w * py).toFixed(2));
      }
      $(".edgerail-direction").text(edgerailWidth);
      $(".edgerail-width").text(processeEdge_yWidth);
    }
  }
  },
  mounted() {

  }
}
</script>
<style>
.f12{font-size: 12px;}
.f14{font-size: 14px;}
.bold{font-weight: 700;}
.normal{font-weight: 400;}
em{font-style: normal;}
ul,li{list-style: none;}
.black{color: #000;}
.cl-b16a00{color: #b16a00;}
.text-center{text-align: center;}
.mt10{margin-top: 10px;}
.mb10{margin-bottom: 10px;}
.ml10{margin-left: 10px;}
.undis{display: none;}
.pull-left{float: left;}
.clearfix{zoom:1;}
.clearfix:after{content:'.';display:block;clear:both;height:0;visibility:hidden;line-height:0;overflow:hidden;}
.imposition-information{width:385px; padding: 15px 20px 20px 15px; background-color: #fff;position: relative}
.imposition-information .panel-x{width: 280px;}
.imposition-information .panel-x .number {position:relative;text-align:center;border-left:1px solid #d7d7d7;border-right:1px solid #d7d7d7;line-height:18px;height:18px;}
.imposition-information .panel-x .number:after {position:absolute;left:0;top:9px;width:100%;height:1px;background-color:#d7d7d7;content:"";}
.imposition-information .panel-x .number span {position:relative;z-index:1;padding:0 9px;line-height:18px;background:#fff;text-align:center;color:#999;}
.imposition-information .panel-y {position:absolute;right:30px;top:74px;width:28px;}
.imposition-information .panel-y .number {position:relative; height:auto; text-align:center;border-top:1px solid #d7d7d7;border-bottom:1px solid #d7d7d7; z-index: 999;}
.imposition-information .panel-y .number:after {position:absolute;left:14px;top:0;width:1px;height:100%;background-color:#d7d7d7;content:"";}
.imposition-information .panel-y .number span {display:block;position:relative;top:50%;margin-top:-19px;z-index:1;background:#fff;text-align:center;color:#999;}
.imposition-information .panel-item{width: 321px;}

.imposition-information li{overflow: hidden; font-size: 0;}
.imposition-information .item{display: block;background-color:#05b302; border-right:1px dashed #ccc; border-bottom:1px dashed #ccc;}

.imposition-information .edgerailwidth{background-color: #0c3a0c;}
</style>