<!--生产管理- 投料列表-修改工厂 -->
<template>
  <div ref="SelectBox">
    <a-form >   
    <a-row>
     <a-col :span='24'>
        <a-form-item label="工厂"  :labelCol="{span: 6}" :wrapperCol="{span:16}">
          <a-select  v-model="form.joinFactoryId" showSearch allowClear optionFilterProp="lable" :getPopupContainer="()=>this.$refs.SelectBox">
            <a-select-option  v-for="(item,index) in factoryData" :key="index" :value="item.valueMember" :lable="item.text"  >
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
  </div>
  </template>

<script>
export default {
    name:'QueryInfoBackend',
    props:['factoryData','selectedId'],
  data() {
    return {
      form:{
        joinFactoryId:'',
      },
           
    };
  },
  mounted(){
    this.form.joinFactoryId = this.selectedId
  },
 
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
};
</script>
<style lang="less" scoped>
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
}

.ant-form-item{
  margin-bottom: 0;
}
</style>
