import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'

export async function upload(params) {
    return request("/api/app/e-mSOrder-management/uploadfile", METHOD.POST,params)
}

export async function getOrderList(params) {
    return request("/api/app/e-mSOrder-management/management-info", METHOD.GET)
}
export async function newOrder(params) {
    return request("/api/app/e-mSOrder-management/order-management", METHOD.POST,params)
}
export async function amderOrder(params) {
    return request("/api/app/e-mSOrder-management/up-date-order-management", METHOD.PUT,params)
}
export async function getOrderNews(params) {
    return request("/api/app/e-mSOrder-management/order-par-price-data/"+params, METHOD.POST)
}
export async function newPrice(params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/price", METHOD.POST,params)
}
export async function amderPrice(params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/price", METHOD.PUT,params)
}
export async function delePrice(params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/delete-price/"+params, METHOD.DELETE)
}

export async function deleOrder(params) {
    return request("/api/app/e-mSOrder-management/delete-order-management/"+params, METHOD.DELETE)
}


export async function queryData(params) {
    return request("/api/app/e-mSOrder-management/order-list-by-search", METHOD.GET,params)
}

export async function audit(params) {
    return request("/api/app/e-mSOrder-management/audit/"+params, METHOD.POST)
}
export async function addorder(params) {
    return request("/api/app/e-mSOrder-management/order-again", METHOD.POST,params)
}





export  default {
    upload,
    getOrderList,
    newOrder,
    amderOrder,
    getOrderNews,
    newPrice,
    amderPrice,
    delePrice,
    deleOrder,
    queryData,
    audit,
    addorder
}