<template>
  <div class="contentInfo">
    <a-card title="参数信息" :bordered="false">
      <a-form-model layout="inline">
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="板材类别" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.pcbProType }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.pcbProType">
                  <a-select-option v-for="item in mapKey(selectOption.PcbProType)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="样/批" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.orderType }}</span>
              <div class="editWrapper" style="flex-wrap: wrap" v-else>
                <a-select v-model="formData.orderType">
                  <a-select-option v-for="item in mapKey(selectOption.OrderType)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="客户料号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag"></span>
              <div class="editWrapper" v-else>
                <a-input />
                <button>文件上传</button>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="订单编号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag"></span>
              <div class="editWrapper" v-else>
                <a-input />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="层数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.boardLayers }}</span>
              <div class="editWrapper" style="flex-wrap: wrap" v-else>
                <a-select v-model="formData.boardLayers">
                  <a-select-option v-for="item in mapKey(selectOption.BoardLayers)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="订单面积" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.boardArea }}</span>
              <div class="editWrapper" style="flex-wrap: wrap" v-else><a-input v-model="formData.boardArea" /> m2</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="出货方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.boardType }}</span>
              <div class="editWrapper" style="flex-wrap: wrap" v-else>
                <a-select v-model="formData.boardType">
                  <a-select-option v-for="item in mapKey(selectOption.BoardType)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="分割方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.vCut }}</span>
              <div class="editWrapper" style="flex-wrap: wrap" v-else>
                <a-select v-model="formData.vCut">
                  <a-select-option v-for="item in mapKey(selectOption.VCut)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="是否接受打叉板" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlag">{{ showData.acceptCrossed ? "是" : "否" }}</span>
              <div class="editWrapper" v-else>
                <a-checkbox v-model="formData.acceptCrossed" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="是否阻抗" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlag">{{ showData.isImpedance ? "是" : "否" }}</span>
              <div class="editWrapper" v-else>
                <a-checkbox v-model="formData.isImpedance" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="成品板厚" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.boardThickness }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.boardThickness">
                  <a-select-option v-for="item in mapKey(selectOption.BoardThickness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="工艺边" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.processEdges }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.processEdges">
                  <a-select-option v-for="item in mapKey(selectOption.ProcessEdges)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="2">
            <a-form-model-item label="半孔" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlag">{{ showData.isHalfHole ? "是" : "否" }}</span>
              <div class="editWrapper" v-else>
                <a-checkbox v-model="formData.isHalfHole" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="2">
            <a-form-model-item label="盲埋孔" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlag">{{ showData.isBlindVias ? "是" : "否" }}</span>
              <div class="editWrapper" v-else>
                <a-checkbox v-model="formData.isBlindVias" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="是否加急" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlag">{{ showData.isJiaji ? "是" : "否" }}</span>
              <div class="editWrapper" v-else>
                <a-checkbox v-model="formData.isJiaji" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="板材品牌" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.fR4Type }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.fR4Type">
                  <a-select-option v-for="item in mapKey(selectOption.FR4Type)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="TG" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.fR4Tg }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.fR4Tg">
                  <a-select-option v-for="item in mapKey(selectOption.FR4Tg)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="验收标准" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.ipcLevel }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.ipcLevel">
                  <a-select-option v-for="item in mapKey(selectOption.ipcLevel)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="交货日期" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.deliveryDate }}</span>
              <div class="editWrapper" v-else>
                <a-input v-model="formData.deliveryDate"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="外层铜厚" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.copperThickness }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.copperThickness">
                  <a-select-option v-for="item in mapKey(selectOption.CopperThickness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="内层铜厚" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.innerCopperThickness }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.innerCopperThickness">
                  <a-select-option v-for="item in mapKey(selectOption.InnerCopperThickness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="测试方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.flyingProbe }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.flyingProbe">
                  <a-select-option v-for="item in mapKey(selectOption.FlyingProbe)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="导热系数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.invoice }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.invoice">
                  <a-select-option v-for="item in mapKey(selectOption.Invoice)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="线宽线距" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.lineWeight }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.lineWeight">
                  <a-select-option v-for="item in mapKey(selectOption.LineWeight)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="拼版款数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.pinBanNum }}</span>
              <div class="editWrapper" v-else>
                <a-input v-model="formData.pinBanNum" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="交货数量" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.num }}</span>
              <div class="editWrapper" v-else>
                <a-input v-model="formData.num" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="拼版方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.pinBanType }}</span>
              <div class="editWrapper" v-else>
                <a-input v-model="formData.pinBanType" />
                X
                <a-input v-model="formData.pinBanType" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="最小孔径" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.vias }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.vias">
                  <a-select-option v-for="item in mapKey(selectOption.Vias)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="孔铜25um" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.holeThickness_ ? "是" : "否" }}</span>
              <div class="editWrapper" v-else>
                <a-checkbox v-model="formData.holeThickness_"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="成长" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.boardHeight }}</span>
              <div class="editWrapper" v-else>
                <a-input v-model="formData.boardHeight" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="成宽" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.boardWidth }}</span>
              <div class="editWrapper" v-else>
                <a-input v-model="formData.boardWidth" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="表面处理" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.surfaceFinish }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.surfaceFinish">
                  <a-select-option v-for="item in mapKey(selectOption.SurfaceFinish)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="厚度" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.imGoldThinckness }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.imGoldThinckness">
                  <a-select-option v-for="item in mapKey(selectOption.ImGoldThinckness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="过孔处理" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.solderCover }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.solderCover">
                  <a-select-option v-for="item in mapKey(selectOption.SolderCover)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="阻焊(顶)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.solderColor }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.solderColor">
                  <a-select-option v-for="item in mapKey(selectOption.SolderColor)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="阻焊(底)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.solderColorBottom }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.solderColorBottom">
                  <a-select-option v-for="item in mapKey(selectOption.SolderColorBottom)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="字符(顶)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.fontColor }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.fontColor">
                  <a-select-option v-for="item in mapKey(selectOption.FontColor)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="字符(底)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.fontColorBottom }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.fontColorBottom">
                  <a-select-option v-for="item in mapKey(selectOption.FontColorBottom)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="协同工厂" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="!editFlag">{{ showData.joinFactoryId }}</span>
              <div class="editWrapper" v-else>
                <a-select v-model="formData.joinFactoryId">
                  <a-select-option v-for="item in mapKey(selectOption.JoinFactoryId)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="业务员备注" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <span v-if="!editFlag">{{ showData.cnNote }}</span>
              <div class="editWrapper" style="height: 96px" v-else>
                <a-input v-model="formData.cnNote" style="width: 80%" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="审单备注" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <span v-if="!editFlag">{{ showData.checkNote }}</span>
              <div class="editWrapper" style="height: 96px" v-else>
                <a-input v-model="formData.checkNote" style="width: 80%" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>

    <a-modal title="拼版图" :visible="makeupVisible" :footer="null" @cancel="handleCancel">
      <makeup-pic ref="makeup"></makeup-pic>
    </a-modal>
  </div>
</template>

<script>
import { getEditOrderInfo, getSelectOption } from "@/services/mkt/orderInfo";
import MakeupPic from "@/pages/mkt/moduleInput/MakeupPic";
import $ from "jquery";
export default {
  name: "OrderInfo",
  props: ["editFlag", "showData"],
  components: { MakeupPic },
  data() {
    return {
      selectOption: [],
      formData: {},
      dataVisible: false,
      dataVisible1: false,
      ProcessEdgeWidth: 0,
      makeupVisible: false, // 拼版图弹窗开关
      ReportList: [], // 出货报告列表
    };
  },
  computed: {
    ozValue() {
      let arr_ = [];
      for (var i = 0; i < this.formData.boardLayers; i++) {
        if (i == 0 || i == this.formData.boardLayers - 1) {
          arr_.push(this.formData.copperThickness);
        } else {
          arr_.push(this.formData.innerCopperThickness);
        }
      }
      return arr_.join("/");
    },
  },
  filters: {
    invoiceFilter(val) {
      let val_ = "";
      switch (val) {
        case 0:
          val_ = "默认";
          break;
        case 1:
          val_ = "1.0w";
          break;
        case 2:
          val_ = "1.5w";
          break;
        case 3:
          val_ = "2.0w";
          break;
        case 4:
          val_ = "0.5w";
          break;
        case 5:
          val_ = "0.7w";
          break;
        case 6:
          val_ = "5w";
          break;
        case 8:
          val_ = "8w";
          break;
        case 9:
          val_ = "3w";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
    camFilter(val) {
      let val_ = "";
      switch (val) {
        case 1:
          val_ = "中级";
          break;
        case 2:
          val_ = "高级";
          break;
        case 3:
          val_ = "资深";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
  },
  created() {
    this.getData();
    this.getEditData();
  },
  watch: {
    formData(val) {
      if (val.needReportList != null) {
        this.ReportList = val.needReportList.split(",");
      }
      // console.log(this.ReportList)
    },
  },
  methods: {
    getData() {
      getSelectOption().then(res => {
        if (res.code) {
          this.selectOption = res.data;
          console.log("this.selectOption", this.selectOption);
        }
      });
    },
    getEditData() {
      let id = this.$route.query.id;
      if (id) {
        getEditOrderInfo(id).then(res => {
          if (res.code) {
            // 铜厚（内、外）：innerCopperThickness  copperThickness
            // 孔径：vias
            // 金手指倒斜边：goldfinger
            // 阻抗：impedanceSize
            // 阻抗报告：impedanceReport
            // 阻抗测试条：zkTestStrip
            // 工程文件：hasCamFile
            // 导热系数：invoice
            // 工程师等级：camEngineer
            // 出货报告材质：reportMaterial
            // 平均孔铜 ：holeCuRequest
            // 最小单点: minPoint
            let data_ = res.data;
            data_.boardLayers = data_.boardLayers ? data_.boardLayers.toString() : "";
            data_.innerCopperThickness = data_.innerCopperThickness ? data_.innerCopperThickness.toString() : "";
            data_.copperThickness = data_.copperThickness ? data_.copperThickness.toString() : "";
            data_.vias = data_.vias ? data_.vias.toString() : "";
            data_.goldfinger = data_.goldfinger ? data_.goldfinger.toString() : "不需要";
            data_.impedanceSize = data_.impedanceSize ? data_.impedanceSize.toString() : "不需要";
            data_.impedanceReport = data_.impedanceReport ? data_.impedanceReport.toString() : "不需要";
            data_.zkTestStrip = data_.zkTestStrip ? data_.zkTestStrip.toString() : "不需要";
            data_.invoice = data_.invoice ? data_.invoice.toString() : "";
            data_.camEngineer = data_.camEngineer ? data_.camEngineer.toString() : "";
            data_.reportMaterial = data_.reportMaterial ? data_.reportMaterial.toString() : "";
            data_.holeCuRequest = data_.holeCuRequest ? data_.holeCuRequest.toString() : "0";
            data_.minPoint = data_.minPoint ? data_.minPoint.toString() : "0";
            this.formData = data_;
            let arr = this.formData.processEdges.split(":");
            this.$set(this.formData, "processEdges1", arr[0]);
            this.$set(this.formData, "ProcessEdgeWidth", arr[1]);
            // let arr1 =this.formData.pinBanType.toLowerCase().split('x')
            // this.$set(this.formData,'showNum',arr1[0]*arr1[1]*this.formData.num)
            console.log("this.formData", this.formData);
          }
        });
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    // 查看拼版图
    jigsawPuzzleClick() {
      console.log(this.formData);
      var arr = this.formData.pinBanType.toLowerCase().split("x");
      this.makeupVisible = true;
      this.$nextTick(() => {
        this.$refs.makeup.impositionInformationExample(
          this.formData.boardHeight,
          this.formData.boardWidth,
          arr[0] || 1,
          arr[1] || 1,
          this.formData.processEdges1 || "无",
          this.formData.ProcessEdgeWidth || 0,
          this.formData.vCut || "none",
          this.formData.grooveWidth || 0,
          this.formData.grooveHeight || 0
        );
      });
    },
    // 下载工程文件
    down1() {
      if (this.showData.pcbFileName) {
        window.location.href = this.showData.pcbFileName;
      }
    },
    // 下载工程文件
    down() {
      if (this.formData.factoryProductFile) {
        window.location.href = this.formData.factoryProductFile;
      }
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
  },
};
</script>

<style scoped lang="less">
.contentInfo {
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 32px;
          .ant-select {
            width: 120px;
          }
          .ant-input {
            width: 120px;
          }
          .ant-input-number {
            width: 120px;
          }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          label {
            font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          }
        }
        .ant-form-item-control-wrapper {
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 8px 10px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
