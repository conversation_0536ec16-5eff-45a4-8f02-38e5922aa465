<!-- 系统管理-脚本管理-编辑 -->
<template>
  <a-form-model
        :modal="form1"
    >
    <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="主机名称">
      <a-input   v-model='form1.pcName'/>
    </a-form-item>
    <a-form-item label="职位">
      <a-input   v-model='form1.useR_'/>
    </a-form-item>
    <a-form-item label="姓名">
      <a-input   v-model='form1.realName'/>
    </a-form-item>
    <a-form-item label="权限">
      <a-checkbox   v-model='form1.permission'/>
    </a-form-item>
  </a-form>
  
    </a-form-model>
</template>

<script>
export default {
  name:'EditAdd',    
   props:['selectedRowsData'],
  data() {
    return {      
      autoFocus:true,
      form1:{
        pcName:'',
        useR_:'',
        realName:'',
        permission:'',
      }
    };
  },
  methods: {  
  },
  created(){
     //console.log('测试',this.selectedRowsData)
     this.$nextTick(function () {
      this.form1.pcName = this.selectedRowsData.pcName;//主机名
      this.form1.useR_ = this.selectedRowsData.useR_;//职位
      this.form1.realName = this.selectedRowsData.realName;//姓名
      this.form1.permission = this.selectedRowsData.permission;//权限
      })
  },
};
</script>
<style scoped lang="less">
/deep/.ant-input{
  font-weight: 500;
}
.ant-modal-body{
    .ant-form-item {
  margin-bottom: 8px;
}
}

</style>