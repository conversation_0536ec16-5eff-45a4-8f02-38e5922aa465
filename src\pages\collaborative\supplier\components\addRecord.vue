<template>
  <div ref="SelectBox">
    <a-modal
    :title="form.id? '编辑' : '新增'"
    :width="550"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="拜访人" ref="visitors_" prop="visitors_">
          <a-input style="font-weight: 500;"
            v-model="form.visitors_"
            placeholder="拜访人"
          />
        </a-form-model-item>
        <a-form-model-item  label="职位" ref="visitPosition_" prop="visitPosition_">
          <a-select   v-model="form.visitPosition_" :getPopupContainer="()=>this.$refs.SelectBox" >
            <a-select-option value="">
              请选择
            </a-select-option>
            <a-select-option value="董事长">
              董事长
            </a-select-option>
            <a-select-option value="总经理">
              总经理
            </a-select-option>
            <a-select-option value="生产副总">
              生产副总
            </a-select-option>
            <a-select-option value="销售副总">
              销售副总
            </a-select-option>
            <a-select-option value="采购副总">
              采购副总
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item  label="方式" ref="visitMethod_" prop="visitMethod_">
          <a-select   v-model="form.visitMethod_" :getPopupContainer="()=>this.$refs.SelectBox" >
            <a-select-option value="">
              请选择
            </a-select-option>
            <a-select-option value="电话">
              电话
            </a-select-option>
            <a-select-option value="邮件">
              邮件
            </a-select-option>
            <a-select-option value="线下">
              线下
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item  label="电话" ref="visitTel_" prop="visitTel_">
          <a-input style="font-weight: 500;"
            v-model="form.visitTel_"
            placeholder="电话"
          />
        </a-form-model-item>
        <a-form-model-item  label="计划拜访时间" ref="planVisitDate_" prop="planVisitDate_">
          <a-date-picker v-model="form.planVisitDate_" style="width: 100%;"/>
        </a-form-model-item>
        <a-form-model-item  label="拜访完成日期" ref="visitDate_" prop="visitDate_">
          <a-date-picker v-model="form.visitDate_"  style="width: 100%;"/>
        </a-form-model-item>
        <a-form-model-item  label="拜访内容" ref="visitContent_" prop="visitContent_">
          <a-textarea style="font-weight: 500;"
            v-model="form.visitContent_"
            placeholder="拜访内容"
            :auto-size="{ minRows: 5, maxRows: 8 }"
          />
        </a-form-model-item>

      </a-form-model>
    </a-spin>
  </a-modal>
  </div>
  
</template>

<script>
import moment from 'moment';
import { addlog, updatelog,progressNum } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
    compileApply:{
      type: String,
      default () {
        return ''
      }
    },
    message:{
      type: String,
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      visible: false,
      confirmLoading: false,
      form: {

      },
      rules: {
        visitors_: [
          { required: true, message: "名称必须填写", trigger: "blur" },
        ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
      // if(this.compileApply=='1'){
        this.visible = true;
        this.model = model
        if(model && model.id){
          this.form = {
            id:model.id,
            visitors_:model.visitors_,
            visitPosition_: model.visitPosition_,
            visitTel_:model.visitTel_,
            visitMethod_: model.visitMethod_,
            visitContent_: model.visitContent_,
            visitDate_: model.visitDate_,
            planVisitDate_:model.planVisitDate_
          }
        }else {
          this.form={
            id:'',
            visitors_:'',
            visitPosition_: '',
            visitTel_:'',
            visitMethod_: '',
            visitContent_: '',
            visitDate_: '',
            planVisitDate_:''
          }
        }
      // }else {
      //   this.$message.info(this.message)
      // }


    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          this.form.visitDate_ = moment(this.form.visitDate_).format('YYYY-MM-DD')
          this.form.planVisitDate_=moment(this.form.planVisitDate_).format('YYYY-MM-DD')
          let params = {
              id: this.model.id,
              pGuid_: this.suppId,
              ...this.form
          }
         if(this.model.id) {
            updatelog(this.model.id,params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info("操作成功");
                    this.$emit("ok");
                  progressNum(this.suppId).then(res=>{
                    if(res.code!==1){
                      this.$message.info(res.message)
                    }
                  })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }else{
              addlog(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info("操作成功");
                    this.$emit("ok");
                  progressNum(this.suppId).then(res=>{
                    if(res.code!==1){
                      this.$message.info(res.message)
                    }
                  })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
}
/deep/.ant-calendar-picker-input.ant-input{
font-weight: 500
}
/deep/.ant-form-item{
      margin-bottom: 5px;
  }
</style>
