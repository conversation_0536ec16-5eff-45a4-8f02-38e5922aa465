<!--YXD报价单1151  -->
<template>
  <div class="pdfDom1" style="font-size: 14px">
    <a-button v-print="printObj" @click="printpdf" class="printstyle" type="primary">打印</a-button>
    <div id="yxdreport1" style="padding: 25px; font-family: '宋体'">
      <div style="display: flex; justify-content: space-between">
        <img src="@/assets/img/1151logo.png" style="width: 134px; height: 30px" />
        <div style="font-size: 20px; font-weight: bold; text-align: center">
          <div>{{ YXDreportdata.factory_ }}</div>
          <div>PCB采购合同</div>
        </div>
        <div>
          <div>打印时间:{{ YXDreportdata.factorydete }}</div>
        </div>
      </div>
      <div style="display: flex; justify-content: space-between">
        <div>
          <div>甲方： {{ YXDreportdata.factory_ }}</div>
          <div>乙方： {{ YXDreportdata.value_1 }}</div>
        </div>
        <div>
          <div>页码:1/1</div>
          <div>客户合同号：{{ YXDreportdata.value_2 }}</div>
        </div>
      </div>
      <div>
        兹因甲方向乙方订购下列产品 ，经双方协商一致，订立本合同：
        <table border="1" style="text-align: center; margin-top: 5px; width: 100%">
          <thead>
            <tr>
              <td rowspan="2" style="width: 50px">序号</td>
              <td rowspan="2">产品名称</td>
              <td rowspan="2">料号</td>
              <td rowspan="2" colspan="2">拼版尺寸(长*宽)</td>
              <td rowspan="2">拼板数</td>
              <td rowspan="2">层数</td>
              <td rowspan="2">材质</td>
              <td colspan="3">成品</td>
              <td rowspan="2">表面处理</td>
              <td rowspan="2">阻焊</td>
              <td rowspan="2">字符</td>
              <td rowspan="2">数量</td>
              <td rowspan="2">含税单价</td>
              <td rowspan="2">工程费</td>
              <td rowspan="2">测试费</td>
              <td rowspan="2">飞测费</td>
              <td rowspan="2">总价</td>
              <td rowspan="2">交期</td>
            </tr>
            <tr>
              <td>板厚(mm)</td>
              <td>内层铜厚OZ</td>
              <td>外层铜厚OZ</td>
            </tr>
          </thead>
          <tr v-for="(item, index) in YXDreportdata.price" :key="index">
            <td>{{ index + 1 }}</td>
            <!--产品名称-->
            <td>{{ item.price1 }}</td>
            <!--料号-->
            <td>{{ item.price2 }}</td>
            <!--拼版尺寸(长)-->
            <td>{{ item.price3 }}</td>
            <!--拼版尺寸(宽)-->
            <td>{{ item.price4 }}</td>
            <!--拼板数-->
            <td>{{ item.price5 }}</td>
            <!--层数-->
            <td>{{ item.price6 }}</td>
            <!--材质-->
            <td>{{ item.price7 }}</td>
            <!--板厚(mm)-->
            <td>{{ item.price8 }}</td>
            <!--内层铜厚OZ-->
            <td>{{ item.price9 }}</td>
            <!--外层铜厚OZ-->
            <td>{{ item.price10 }}</td>
            <!--表面处理-->
            <td>{{ item.price11 }}</td>
            <!--阻焊-->
            <td>{{ item.price12 }}</td>
            <!--字符-->
            <td>{{ item.price13 }}</td>
            <!--数量-->
            <td>{{ item.price14 }}</td>
            <!--含税单价-->
            <td>{{ item.price15 }}</td>
            <!--工程费-->
            <td>{{ item.price16 }}</td>
            <!--测试费-->
            <td>{{ item.price17 }}</td>
            <!--飞测费-->
            <td>{{ item.price18 }}</td>
            <!--总价-->
            <td>{{ item.price19 }}</td>
            <!--交期-->
            <td>{{ item.price20 }}</td>
          </tr>
          <tr>
            <td colspan="12">合计金额大写:{{ convertToChineseNum(amountto) }}</td>
            <td colspan="4">合计金额小写：</td>
            <td colspan="5">{{ amountto }}</td>
          </tr>
        </table>
      </div>
      <div>
        <div>1、币别: {{ YXDreportdata.value_3 }} / 税率： {{ YXDreportdata.value_4 }}</div>
        <div>2、付款方式： {{ YXDreportdata.value_5 }}</div>
        <div>3、包装方式： {{ YXDreportdata.value_6 }}</div>
        <div>4、交货地点： {{ YXDreportdata.value_7 }}</div>
        <div>5、其他说明：</div>
        <div>①验收标准：符合IPC-6012和IPC-A-600标准：</div>
        <div>
          ②每批交付的PCB，打叉的SET比例不得超过10%，单个SET上叉板的数量不得超过2PCS，打叉板要求单独包装并于外包装上做标识
          ，同包装袋内的打叉板方向和位置必须一致 ，且打叉 板双面均需做标识以便识别。
        </div>
        <div>
          ③如因PCB板质量问题，导致已加工的PCBA不能使用，需承担PCBA上所有元器件的采购成本，其中IC、MCU等贵重元器件可拆除再利用（只能用于维修）的50%扣款，其他全额扣款。
        </div>
        <div>④甲方收到产品后，如发现质量问题，数量等问题，应在10天内书面提出，双方协商处理；</div>
        <div>
          ⑤乙方送货时需随货附上产品出厂合格检测报告和切片报告 、送货单（一式四联），并需在送货单上注明本合同的单号
          ，送货单上货品名称和型号规格需与本合同一致 ；来料外箱和 内包装都需贴上标签，整箱包装；散装出货时均需在箱内和外箱贴上相应的出货清单
          ，否则甲方仓库有权拒收；
        </div>
        <div>⑥所有与产品相关的质量要求和执行标准的争议处理 ，以双方签订的《采购框架合同》和《质量保证协议》为准；</div>
        <div>⑦本合同任何变更，补充，取消或终止等事项，均需以书面方式作出，并由双方签章后生效；</div>
        <div>⑧争议解决：因本合同产生的一切争议，双方友好协商解决；协商不成时，提交甲方所在地有管辖权的人民法院裁决 ；</div>
        <div>⑨本合同壹式贰份，双方各执壹份，具有同等法律效力，自双方签字盖章之日起生效。</div>
      </div>
      <div style="position: relative; margin-top: 30px; height: 130px">
        <div style="display: flex; justify-content: space-between; z-index: 99; position: relative">
          <div style="padding-left: 60px">
            <div>甲方（盖章）：{{ YXDreportdata.factory_ }}</div>
            <div>地址：</div>
            <div>授权代表（签字）：</div>
            <div>联系电话： 15871104990</div>
          </div>
          <div style="padding-right: 60px">
            <div>乙方盖章： {{ YXDreportdata.value_1 }}</div>
            <div>地址： 惠州市惠城区水口镇中心村雅信达工业园</div>
            <div>授权代表（签字）：</div>
            <div>联系电话： 0755-27771099 0755-27778499</div>
          </div>
        </div>
        <img
          v-if="YXDreportdata.value_1.indexOf('惠州') != -1"
          src="@/assets/img/hzyxd.png"
          style="position: absolute; top: -27px; z-index: 0; display: block; left: 900px; width: 150px"
        />
        <img
          v-if="YXDreportdata.value_1.indexOf('江西') != -1"
          src="@/assets/img/jxyxd.png"
          style="position: absolute; bottom: -27px; z-index: 0; display: block; left: 900px; width: 150px"
        />
      </div>
    </div>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["YXDreportdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj: {
        id: "yxdreport1", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.YXDreportdata.price.length; index++) {
      if (this.YXDreportdata.price[index].price19 && this.YXDreportdata.price[index].price19 != "/") {
        this.amountto += Number(this.YXDreportdata.price[index].price19);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.YXDreportdata.pcbFileName;
    },
    getreportPdf() {
      htmlToPdfa3("yxdreport1", this.YXDreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
