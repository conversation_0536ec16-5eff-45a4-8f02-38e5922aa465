<template>
  <a-spin :spinning="spinning">
    <h3
      v-if="errPag"
      style="margin-top: 0; margin-bottom: 0.5em; color: #cf1b26; font-weight: 700; text-align: start; margin-left: 1%; font-size: 24px"
    >
      {{ errPagInfo }}
    </h3>
    <div id="app" class="box" ref="SelectBox" @click="bodyClick" v-else>
      <span
        style="margin-top: 0; margin-bottom: 0.5em; color: #cf1b26; font-weight: 700; text-align: left; margin-left: 1%; float: left; font-size: 24px"
        >{{ OrderNo }}&nbsp; <span v-if="pcbdata.pcbFileName">({{ pcbdata.pcbFileName }})</span></span
      >
      <div style="display: flex; float: right">
        <a-button type="primary" style="margin-right: 10px" @click="copyClick">复制链接</a-button>
        <a-button type="primary" style="margin-right: 10px" @click="exportBtn('1')">EQ导出</a-button>
        <!-- <a-button type="primary"   style="margin-right:10px"  @click="replyclick" >回复</a-button> -->
      </div>
      <div style="width: 100%; overflow: auto; border-bottom: 1px solid #e8e8e8" class="maincontent">
        <table border style="border-color: #e1e1e2">
          <thead>
            <tr>
              <th style="width: 10%">问客次数</th>
              <th style="width: 7%">序号</th>
              <th style="width: 10%">操作时间</th>
              <th style="width: 43%">问题详情</th>
              <th style="width: 9%">附件</th>
              <th style="width: 10%">操作</th>
            </tr>
          </thead>
        </table>
        <div style="width: 100%; overflow: auto; border-bottom: 1px solid #e1e1e2" class="scorllclass">
          <a-collapse :activeKey="copyorderData.length">
            <a-collapse-panel v-for="(val, inde) in copyorderData" :key="(inde + 1).toString()">
              <template #header>
                <div style="text-align: left">
                  第 <span style="color: rgb(207, 27, 38); font-weight: 700; font-size: 16px">{{ inde + 1 }}</span> 次问客
                </div>
              </template>
              <table border style="border-color: #e1e1e2; color: #000000; border-top-color: #ffffff00">
                <template v-for="(item, index) in val">
                  <tr :key="'1' + index">
                    <td style="width: 10%" v-if="item.num" :rowspan="item.num">{{ item.eqNumber }}</td>
                    <td style="width: 7%" :rowspan="item.reply ? 2 : 1">
                      Q<i>{{ qsort(item.id) }}</i>
                    </td>
                    <td style="width: 10%">{{ item.createTime }}</td>
                    <td style="width: 43%" class="left">
                      <div>
                        <p>问题描述：{{ item.contentS }}</p>
                        <p>建议方案1：{{ item.contentA }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentB)">建议方案2：{{ item.contentB }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentC)">建议方案3：{{ item.contentC }}</p>
                      </div>
                    </td>
                    <td style="width: 9%">
                      <div v-if="item.image != ''" v-viewer>
                        <span v-for="(ite, ind) in item.image" :key="ind" style="color: #0068ff; cursor: pointer; text-decoration: underline">
                          <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                          ><br />
                        </span>
                      </div>
                      <div v-if="item.filePath != '' && item.filePath != ','">
                        <span style="color: red; cursor: pointer" @click="down(item.filePath)"
                          >工程上传文件（可点击下载{{ item.filePath.split(",").length }}个文件）</span
                        >
                      </div>
                    </td>
                    <td style="width: 10%" :rowspan="item.reply ? 2 : 1">
                      <div>
                        <p @click="replyClick(item)">+回复提问</p>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="item.reply" :key="'2' + index">
                    <td style="width: 7%">{{ item.reply.solutionTime }}</td>
                    <td style="width: 43%" class="left">
                      <div>
                        <p style="color: #ff9900">回复内容：{{ item.reply.content }}</p>
                      </div>
                    </td>
                    <td style="width: 9%">
                      <div v-if="item.reply.image" v-viewer>
                        <span
                          v-for="(ite, ind) in item.reply.image.split(',')"
                          :key="ind"
                          style="color: #0068ff; cursor: pointer; text-decoration: underline"
                        >
                          <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                          ><br />
                        </span>
                      </div>
                      <div v-if="item.reply.filePath">
                        <span style="color: red; cursor: pointer" @click="down(item.reply.filePath)"
                          >文件（可点击下载{{ item.reply.filePath.split(",").length }}个文件）</span
                        >
                      </div>
                    </td>
                  </tr>
                </template>
              </table>
            </a-collapse-panel>
          </a-collapse>
          <table>
            <tr>
              <td style="width: 10%"></td>
              <td style="width: 10%"></td>
              <td style="width: 43%"></td>
              <td style="text-align: center; width: 17%">
                <a-button type="primary" style="margin-top: 10px" @click="replyclick">确认回复</a-button><br />
                <span style="color: red; font-size: 12px">(以上EQ问题全部回复完成后,点击“确认回复”,工厂才能收到问客)</span>
              </td>
            </tr>
          </table>
        </div>
      </div>
      <!--    回复问题弹窗-->
      <a-modal
        title="请输入回复内容"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="600"
        :wrap-style="{ overflow: 'hidden' }"
      >
        <div @click="bodyClick">
          <a-form>
            <template>
              <p>问题描述：{{ replyForm.contentS }}</p>
              <div v-if="replyForm.image1 != '' || replyForm.image1 != []" v-viewer>
                <img style="height: 50px; width: 50px; margin: 10px" v-for="(ite, index) in replyForm.image1" :key="index" :src="ite" />
              </div>
              <a-radio-group v-model="replyForm.value" @change="radioChange" style="display: flex; flex-direction: column">
                <a-radio style="height: 30px" :value="1" v-if="!isEmptyOrWhitespace(replyForm.contentA)">方案一： {{ replyForm.contentA }}</a-radio>
                <a-radio style="height: 30px" :value="2" v-if="!isEmptyOrWhitespace(replyForm.contentB)">方案二：{{ replyForm.contentB }}</a-radio>
                <a-radio style="height: 30px" :value="4" v-if="!isEmptyOrWhitespace(replyForm.contentC)">方案三：{{ replyForm.contentC }}</a-radio>
                <a-radio :value="3"
                  ><span style="display: inline-block; line-height: 60px">其他方案:</span>
                  <a-textarea
                    v-model="replyForm.proposalC"
                    style="width: 80%"
                    :auto-size="{ minRows: 2, maxRows: 5 }"
                    :disabled="replyForm.value == '3' ? false : true"
                  ></a-textarea>
                </a-radio>
              </a-radio-group>
              <div>
                <a-upload
                  name="file"
                  ref="fileRef"
                  :before-upload="beforeUpload1"
                  :customRequest="httpRequest5"
                  :file-list="fileListData2"
                  @change="handleChange5"
                  list-type="picture-card"
                  @preview="handlePreview"
                >
                  <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                  <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(5)" title="ctrl+V 粘贴上传">
                    粘贴图片
                  </a-button>
                </a-upload>
                <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview" :width="900">
                  <img style="width: 100%; height: 100%" :src="previewImage" />
                </a-modal>

                <a-upload
                  name="file"
                  ref="fileRef"
                  :before-upload="beforeUpload11"
                  :customRequest="httpRequest2"
                  :file-list="fileList2"
                  @change="handleChange22"
                >
                  <a-button v-if="fileList2.length < 1"> 上传文件 </a-button>
                </a-upload>
              </div>
            </template>
          </a-form>
        </div>
      </a-modal>
      <!--确认弹窗-->
      <a-modal
        title="确认回复"
        :visible="modalvisible"
        @cancel="reportHandleCancel"
        @ok="replyclick"
        ok-text="确认"
        destroyOnClose
        :maskClosable="false"
        :width="600"
        centered
      >
        <p>您的EQ已全部回复完成,确认后不能修改回复内容</p>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import {
  upLoadFlyingFile,
  timestampVerifyCode,
  exportEQReport,
  proorderinformation,
  exportEQReportv2,
  exportEQReportv3,
} from "@/services/projectMake";
import axios from "axios";
import $ from "jquery";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    // scopedSlots: { customRender: 'num' },
    width: "15%",
  },
  {
    title: "操作时间",
    dataIndex: "solutionTime",
    align: "left",
    ellipsis: true,
    width: "15%",
    className: "userStyle",
  },
  {
    title: "操作人员",
    dataIndex: "userName",
    align: "left",
    ellipsis: true,
    width: "15%",
  },
  {
    title: "问题详情",
    // customRender: (text,record,index) => `${record.isReOrder ? '是' : ''}`,
    scopedSlots: { customRender: "content" },
    align: "left",
    ellipsis: true,
    width: "40%",
  },

  {
    title: "操作",
    scopedSlots: { customRender: "action" },
    align: "center",
    // width: 120,   // 2290
  },
];
import { getEqList, eqMainByCust, replyProblems, replyproblembycust, replyproblemsbycust } from "@/services/projectMake";
import moment from "moment";
export default {
  name: "eqDetails1",
  components: {},
  data() {
    return {
      modalvisible: false,
      restatus: null,
      pcbdata: {},
      copyorderData: [],
      spinning: false,
      fileList2: [],
      fileListData2: [],
      columns1,
      orderData: [],
      show: false,
      startloading: false,
      showCopyType: "",
      file: null,
      addTr: false,
      replyTr: false,
      replyData: [],
      selectData1: [],
      selectData2: [],
      selectData3: [],

      form: {
        orderNo: "", //订单号
        eqType: "", //问题类型
        problemDescription: "", // 问题描述
        proposalA: "", // 建议A
        proposalB: "", //建议B
        proposalC: "", //建议C
      },
      askType: 0, // 问客类型（0：问题确认，1：文件确认）
      path: "", // 上传文件返回地址
      path3: "", // 编辑上传图片图片返回地址
      dataVisible1: false,
      solution: "", //解决方案
      fileList: [],
      fileListData: [],
      fileListData3: [],
      fileList4: [],
      isFileType: true,
      previewVisible: false,
      arrData: [],
      arrData1: [],
      dataVisible2: false,
      dataVisible3: false,
      arrData2: [],
      editForm: {
        id: "",
        problemDescription: "", // 问题描述
        proposalA: "", // 建议A
        proposalB: "", //建议B
        proposalC: "", //建议C
        path: "", // 图片地址
        filePath: "", // 文件地址
        askType: 0,
      }, // 编辑问题弹窗数据
      replyForm: {
        Quiz: "",
        id: "",
        value: undefined,
        proposalC: "",
        image1: [],
        image: "",
      },
      previewImage: "",
      eqData: [],
      user: "",
      imglist: [],
      errPagInfo: "",
      errPag: false,
      OrderNo: "",
      isMovedown: false,
      startPosition: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      currentEqNum: 1,
    };
  },
  watch: {
    askType(newNal, oldval) {
      if (newNal == 1) {
        this.form.problemDescription = "文件确认";
        this.form.proposalA = "文件无误";
        this.form.proposalB = "文件有误";
        this.form.proposalC = "";
      } else {
        this.form.problemDescription = "";
        this.form.proposalA = "";
        this.form.proposalB = "";
        this.form.proposalC = "";
      }
    },
  },
  methods: {
    qsort(id) {
      return this.orderData.findIndex(item => item.id == id) + 1;
    },
    sortby(img) {
      return this.imglist.findIndex(item => item == img) + 1;
    },
    downimg(path) {
      this.currentImageUrl = path; // 设置当前图片URL
      this.$nextTick(() => {
        this.$viewerApi({
          images: [this.currentImageUrl],
        });
      });
      //window.location.href=path
    },
    getpcbfile() {
      let params = {
        orderNo: this.$route.query.OrderNo,
        businessOrderNo: this.$route.query.BusinessOrderNo,
      };
      proorderinformation(this.$route.query.JoinFactoryId, params).then(res => {
        if (res.code) {
          this.pcbdata = res.data;
        }
      });
    },
    isEmptyOrWhitespace(content) {
      return /^\s*$/.test(content);
    },
    handleResize() {
      let main = document.getElementsByClassName("maincontent")[0];
      main.style.height = window.innerHeight - 100 + "px";
    },
    //回复按钮
    replyclick() {
      let params = {
        orderNo: this.$route.query.OrderNo,
        businessOrderNo: this.$route.query.BusinessOrderNo,
        joinFactoryId: this.$route.query.JoinFactoryId,
        eqSource: this.$route.query.eQSource,
      };
      replyproblemsbycust(params)
        .then(res => {
          if (res.code) {
            this.$message.success("操作成功");
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.modalvisible = false;
        });
    },
    // 复制链接
    copyClick() {
      let URL = process.env.VUE_APP_API_JSON_URL;
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.BusinessOrderNo;
      let JoinFactoryId = this.$route.query.JoinFactoryId;
      let eQSource = this.$route.query.eQSource;
      timestampVerifyCode().then(res => {
        if (res.code) {
          let Timestamp = res.data.timestamp;
          let VerifyCode = res.data.verifyCode;
          let content =
            URL +
            "/eqDetails1?JoinFactoryId=" +
            JoinFactoryId +
            "&OrderNo=" +
            OrderNo +
            "&Timestamp=" +
            Timestamp +
            "&VerifyCode=" +
            VerifyCode +
            "&BusinessOrderNo=" +
            BusinessOrderNo +
            "&eQSource=" +
            eQSource;
          if (window.clipboardData) {
            window.clipboardData.setData("text", content);
          } else {
            (function () {
              document.oncopy = function (e) {
                e.clipboardData.setData("text", content);
                e.preventDefault();
                document.oncopy = null;
              };
            })(content);
            document.execCommand("Copy");
          }
          this.$message.success("复制成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //EQ导出
    exportBtn(type) {
      let params = Number(type);
      let OrderNo = this.$route.query.OrderNo;
      let eQSource = this.$route.query.eQSource;
      let BusinessOrderNo = this.$route.query.BusinessOrderNo;
      let JoinFactoryId = this.$route.query.JoinFactoryId;
      // exportEQReportv2(JoinFactoryId,OrderNo,params,eQSource,BusinessOrderNo).then( res=>{
      //    if(res.code){
      //     this.downfile(res.data,res.message)
      //    }else{
      //     this.$message.error(res.message)
      //    }
      // })
      exportEQReportv3(JoinFactoryId, OrderNo, type, eQSource, BusinessOrderNo).then(res => {
        if (res.code) {
          this.downfile(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downfile(val, name) {
      const xhr = new XMLHttpRequest();
      xhr.open("GET", val, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = name;
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      };
      xhr.send();
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    beforeUpload1(file) {
      if (this.askType == 1) {
        // const hasChinese = /[\u4e00-\u9fa5]/.test(file.name);
        // this.isFileType =  !hasChinese
        // if (!this.isFileType) {
        //   this.$message.error('文件确认上传文件不得含有中文字符');
        // }
        // return this.isFileType
      } else {
        this.isFileType = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!this.isFileType) {
          this.$message.error("问题确认只支持.jpg/.png图片格式文件");
        }
        return this.isFileType;
      }
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      //console.log('file',file)
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload1(file); // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload1(file)) {
        // **** return true 之后进行上传
        this.startloading = true;
        const formData = new FormData();
        // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append("file", file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file", // 接口地址
          method: "post",
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => {
            if (res.code) {
              //console.log('res',res)
              file.status = "done";
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              if (this.showCopyType == "5") {
                this.handleChange5(file, arr);
              }
              this.startloading = false;
              this.show = false;
            } else {
              this.$message.error(data.message || "网络异常,请重试或检查网络连接状态");
              this.startloading = false;
              this.show = false;
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
    handlePreview(file) {
      this.previewVisible = true;
      this.previewImage = file.response || file.thumbUrl;
    },
    bodyClick() {
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    // 获取订单问客记录
    getOrderList() {
      this.spinning = true;
      let OrderNo = this.$route.query.OrderNo;
      let Timestamp = this.$route.query.Timestamp;
      let VerifyCode = this.$route.query.VerifyCode;
      let BusinessOrderNo = this.$route.query.BusinessOrderNo;
      let JoinFactoryId = this.$route.query.JoinFactoryId;
      let pathstring = "";
      eqMainByCust(JoinFactoryId, OrderNo, Timestamp, VerifyCode, BusinessOrderNo)
        .then(res => {
          if (res.code) {
            let arrData = [];
            this.imglist = [];
            this.orderData = res.data;
            res.data.forEach(item => {
              if (item.image) {
                pathstring += item.image + ",";
              }
              if (item.reply && item.reply.image) {
                pathstring += item.reply.image + ",";
              }
              if (item.parentId <= 0) {
                let content = item.content;
                var arr = content.split("|||");
                var arr_ = arr[1].split(";");
                if (item.image) {
                  var a = item.image.split(",");
                  item.image = a;
                }
                item.contentS = arr[0];
                item.contentA = arr_[0];
                item.contentB = arr_[1];
                item.contentC = arr_[2];
              }
            });
            this.imglist = pathstring.split(",");
            this.imglist.pop();
            if (this.orderData.filter(item => item.status == 1).length > 0) {
              this.currentEqNum = this.orderData.filter(item => item.status == 1)[0].eqNumber;
            } else if (this.orderData.filter(item => item.status == 2).length > 0) {
              const maxNUm = Math.max(...this.orderData.filter(item => item.status == 2).map(item => item.eqNumber));
              this.currentEqNum = maxNUm + 1;
            }
            this.orderData = this.addNumProperty(this.orderData);
          } else {
            this.errPag = true;
            this.errPagInfo = res.message;
            // this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    addNumProperty(arr) {
      const grouped = arr.reduce((acc, item) => {
        let key = item.eqNumber;
        if (!key) {
          key = 0;
        }
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        this.copyorderData = Object.values(acc);
        return acc;
      }, {});
      let newArr = [];
      let fornum = 0;
      for (const [_, items] of Object.entries(grouped)) {
        fornum += items.length;
        let replyNum = 0;
        for (let i = 0; i < items.length; i++) {
          if (i == 0) {
            items[i].num = items.length;
          } else {
            items[i].num = 0;
          }
          if (items[i].reply) {
            replyNum++;
          }
          newArr.push(items[i]);
        }
        newArr[newArr.length - items.length].num = newArr[newArr.length - items.length].num + replyNum;
      }
      return newArr;
    },
    showCopy(type) {
      window.addEventListener("paste", this.getClipboardFiles);
      this.showCopyType = type;
      try {
        navigator.clipboard.read().then(res => {
          const clipboardItems = res;
          if (clipboardItems[0].types[0].indexOf("image") > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then(b => {
              const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, { type: clipboardItems[0].types[0] });
              this.file = files;
              this.getClipboardFiles();
            });
          } else {
            this.$message.error("粘贴内容不是图片");
            return;
          }
        });
      } catch (e) {
        ////console.log('出错了')
      }
    },
    beforeUpload11(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
      if (!this.isFileType) {
        this.$message.error("文件确认只支持.rar或.zip格式文件");
      }
      return this.isFileType;
    },
    async httpRequest2(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          // this.path = res.data
          this.editForm.path = res.data;
          this.replyForm.path = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleChange22({ fileList }, data) {
      if (this.isFileType) {
        this.fileList2 = fileList;
        if (this.fileList2.length == 0) {
          this.replyForm.path = "";
        }
      }
    },
    handleChange5({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData2);
      }
      let namesArray = fileList.map(item => item.response);
      this.arrData2 = namesArray;
      this.replyForm.image = this.arrData2.toString(",");
      if (this.isFileType) {
        this.fileListData2 = fileList;
        if (this.fileListData2.length == 0) {
          this.replyForm.image = "";
        }
      }
    },
    reportHandleCancel() {
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.replyForm.Quiz = "";
      this.replyForm.image = "";
      this.replyForm.path = "";
      this.replyForm.value = undefined;
      this.replyForm.proposalC = "";
      this.fileListData3 = [];
      this.arrData1 = [];
      this.arrData2 = [];
      this.fileList4 = [];
      this.modalvisible = false;
    },
    // 点击查看上传图片
    handleCancelPreview() {
      this.previewVisible = false;
    },
    async httpRequest5(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.arrData2.push(res.data);
          this.replyForm.image = this.arrData2.toString(",");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 回复问题
    replyClick(item) {
      this.restatus = item.status;
      this.dataVisible3 = true;
      this.replyForm.id = item.id;
      this.replyForm.contentS = item.contentS;
      this.replyForm.contentA = item.contentA;
      this.replyForm.contentB = item.contentB;
      this.replyForm.contentC = item.contentC;
      this.replyForm.proposalC = "";
      this.replyForm.image1 = item.image;
      setTimeout(() => {
        $(".ant-modal-header").on("mousedown", e => {
          this.startPosition.x = e.pageX;
          this.startPosition.y = e.pageY;
          this.startPosition.offsetX = e.offsetX;
          this.startPosition.offsetY = e.offsetY;
          this.isMovedown = true;
        });
      }, 200);
      document.body.addEventListener("mousemove", e => {
        if (this.isMovedown) {
          if (
            e.x - this.startPosition.x > 10 ||
            e.y - this.startPosition.y > 10 ||
            e.x - this.startPosition.x < -10 ||
            e.y - this.startPosition.y < -10
          ) {
            let w = $(".ant-modal-content").width();
            let h = $(".ant-modal-content").height();
            $(".ant-modal-content").css({
              left: e.pageX - this.startPosition.offsetX - (document.body.clientWidth - w) / 2 + "px",
              top: e.pageY - (document.body.clientHeight - 3 * h) / 2 + 50 + "px",
            });
          }
        }
      });
      document.body.addEventListener("mouseup", e => {
        this.isMovedown = false;
      });
    },
    handleOk3() {
      if (this.replyForm.value == undefined) {
        this.$message.warning("请选择方案！");
        return;
      }
      if (this.replyForm.value == "3") {
        this.replyForm.Quiz = this.replyForm.proposalC;
      }
      if (this.replyForm.Quiz == "") {
        this.$message.warning("请填写其他方案！");
        return;
      }

      this.dataVisible3 = false;
      let params = {
        id: this.replyForm.id,
        Quiz: this.replyForm.Quiz,
        image: this.replyForm.image,
        path: this.replyForm.path,
      };
      params.eqSource = this.$route.query.eQSource ? Number(this.$route.query.eQSource) : 0;
      const count = this.orderData.filter(ite => ite.status != 2 && !ite.reply).length;
      if (count == 1 && this.restatus != 2) {
        this.modalvisible = true;
      } else if (count == 0 && this.restatus == 2 && this.pcbdata.isEQ != true) {
        this.modalvisible = true;
      }
      replyproblembycust(params).then(res => {
        if (res.code) {
          this.$message.success("回复成功");
          this.replyForm.Quiz = "";
          this.replyForm.image = "";
          this.replyForm.path = "";
          this.replyForm.value = undefined;
          this.replyForm.proposalC = "";
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    radioChange() {
      if (this.replyForm.value == "1") {
        this.replyForm.Quiz = this.replyForm.contentA;
      }
      if (this.replyForm.value == "2") {
        this.replyForm.Quiz = this.replyForm.contentB;
      }
      if (this.replyForm.value == "3") {
        this.replyForm.Quiz = this.replyForm.proposalC;
      }
      if (this.replyForm.value == "4") {
        this.replyForm.Quiz = this.replyForm.contentC;
      }
    },
    // 下载工程文件
    down(item) {
      let pcbFileName = this.pcbdata.pcbFileName;
      if (item) {
        var data = item.split(",");
        for (var i = 0; i < data.length; i++) {
          if (data[i].indexOf("EQ%5C") != -1 || data[i].indexOf("myhuaweicloud") != -1) {
            (function (i) {
              var name = "";
              if (data[i].indexOf("EQ%5C") != -1) {
                name = decodeURIComponent(data[i].split("EQ%5C")[1]);
              } else {
                name = decodeURIComponent(data[i].split("/").slice(-1)[0]);
              }
              const xhr = new XMLHttpRequest();
              xhr.open("GET", data[i], true);
              xhr.responseType = "blob";
              xhr.onload = function () {
                if (xhr.status === 200) {
                  const blob = xhr.response;
                  const link = document.createElement("a");
                  link.href = window.URL.createObjectURL(blob);
                  link.download = pcbFileName ? pcbFileName + "+" + name : name;
                  link.style.display = "none";
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }
              };
              xhr.send();
            })(i);
          } else {
            window.location.href = data[i];
          }
        }
      }
    },
  },
  mounted() {
    this.getOrderList();
    this.OrderNo = this.$route.query.OrderNo;
    this.getpcbfile();
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
    window.removeEventListener("paste", this.getClipboardFiles);
  },
};
</script>

<style scoped lang="less">
.maincontent {
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: #b6b5b4;
    // #fff9e6
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
.box {
  // height: 900px;
  // min-width:1670px;
  overflow: auto;
  background: white;
  padding: 10px;
  padding-bottom: 0;
}
p {
  margin-bottom: 4px;
}
/deep/.tab {
  width: 100% !important;
  text-align: center;
}
.left {
  text-align: left !important;
}
#app {
  // font-family: Avenir, Helvetica, Arial, sans-serif;
  //font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}
table {
  width: 100%;
  /*height: 100%;*/
}
/deep/.ant-collapse {
  border: none;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 0;
}
/deep/.ant-collapse-header {
  border-left: 1px solid;
  border-right: 1px solid;
  border-color: #e1e1e2;
}
.scorllclass {
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: #b6b5b4;
    // #fff9e6
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
</style>
