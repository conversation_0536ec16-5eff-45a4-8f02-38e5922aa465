<template>
    <div class="content">
        <a-tabs  :activeKey="activeKey"  @tabClick="tabClick">
        <a-tab-pane key="0" tab="待入库">
            <pending-storage></pending-storage>
        </a-tab-pane>
        <a-tab-pane key="1" tab="已入库">
            <already-storage></already-storage>
        </a-tab-pane>
      </a-tabs>
    </div>
</template>

<script>
import PendingStorage from "@/pages/OrderManagement/PackagingStorage/modules/PendingStorage"; 
import AlreadyStorage from "@/pages/OrderManagement/PackagingStorage/modules/AlreadyStorage"; 
export default {
    components:{PendingStorage,AlreadyStorage},
    name: 'PackagingStorage',
    data() {
        return {
            activeKey: '0',
        }
    },
    mounted() {
    },
    methods: {
        tabClick(key){
            this.activeKey=key
      },
    }
}
</script>
<style lang="less" scoped>
.content{
    background-color: white;
}
</style>