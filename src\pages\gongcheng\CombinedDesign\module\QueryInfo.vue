<!-- 工程管理 - 合拼设计 -查询 -->
<template>
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="生产编号">
      <a-input ref="inputRef" :autoFocus="true" v-model='queryForm.Pdctno_' placeholder="请输入生产编号"  allowClear/>
    </a-form-item>
    <!-- <a-form-item label="协同工厂">
      <a-select   v-model='queryForm.JoinFactoryId' placeholder="请选择工厂" showSearch allowClear optionFilterProp="label" >
        <a-select-option  v-for="(item,index) in facList" :key="index" :value="item.id" :label="item.key_">{{item.key_}} </a-select-option>
      </a-select>
    </a-form-item> -->

  </a-form>
</template>

<script>
export default {
    name:'QueryInfo',
  props:['facList'],
  data() {
    return {
      queryForm:{
        Pdctno_:'',
        JoinFactoryId:''
      },

    };
  },
  methods: {

  },
};
</script>
<style lang="less" scoped>
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
</style>

