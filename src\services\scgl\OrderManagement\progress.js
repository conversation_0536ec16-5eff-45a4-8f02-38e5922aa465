import { request, METHOD } from '@/utils/request'

// 获取过序数量 
export async function getPassFlowNum(params) {
    return request(`/api/app/pro-card-info/is-allow-pass-flow-num?CardNo=${params}`, METHOD.GET, )
}
// 部门过序 
export async function setStepFlow(params) {
    return request(`/api/app/pro-card-info/set-step-flow`, METHOD.POST,params )
}
// 左侧统计列表
export async function progressLeftList(params) {
    return request(`/api/app/pro-card-info/production-progress-left-list`, METHOD.GET, params)
}
// 生产进度列表 
export async function progressList(params) {
    return request(`/api/app/pro-card-info/production-progress-list`, METHOD.GET,params )
}
// 过数列表 
export async function cardFlowList(params) {
    return request(`/api/app/pro-card-info/card-flow-list?CardNo=${params}`, METHOD.GET,)
}
export default {
    getPassFlowNum,
    setStepFlow, 
    progressLeftList,
    progressList,
    cardFlowList,

}