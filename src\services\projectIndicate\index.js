/*
 * @Author: C<PERSON>
 * @Date: 2022-06-16 15:08:05
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-27 10:00:31
 * @FilePath: \vue-antd-admin\src\services\projectDisptch\index.js
 * @Description:
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved.
 */
import { request, METHOD } from "@/utils/request";
// 获取订单列表
export async function proOrderInfo(params) {
  return request(`/api/app/engineering-make-info/pro-order-info?OrderNo=${params}`, METHOD.GET);
}
// 获取选择项接口
export async function qutoConfig(factory) {
  return request(`/api/app/engineering-make-info-up/pro-order-quto-config-v2?fatory=${factory}`, METHOD.GET);
}
export async function selectpars(type, fatory) {
  return request(`/api/app/selection-parameters/select-pars?type=${type}&fatory=${fatory}`, METHOD.GET);
}
// 获取工厂选择项接口
export async function factoryList() {
  return request(`/api/app/e-mSSupplier-module-no/factory-id-list`, METHOD.POST);
}
// 产品信息修改保存
export async function productInformation(params) {
  return request(`/api/app/engineering-make-info-up/set-product-information`, METHOD.POST, params);
}
// 钻铣参数修改保存
export async function drillingInformation(params) {
  return request(`/api/app/engineering-production/set-drilling-information`, METHOD.POST, params);
}
// 基铜改变补偿参数联动
export async function baseCu_(joinFactoryId) {
  return request(`/api/app/engineering-make-info-up/base-copper-bc/${joinFactoryId}`, METHOD.POST, joinFactoryId);
}
// 线路信息修改保存
export async function lineInformation(params) {
  return request(`/api/app/engineering-production/set-line-information`, METHOD.POST, params);
}
// 阻焊油墨修改保存
export async function solderInformation(params) {
  return request(`/api/app/engineering-production/set-solder-information`, METHOD.POST, params);
}
// 发货信息修改保存
export async function shippingInformation(params) {
  return request(`/api/app/engineering-production/set-shipping-information`, METHOD.POST, params);
}
// 指示检查
export async function mIIndicationCheck(Id, type) {
  return request(`/api/app/engineering-make-info-up/m-iIndication-check/${Id}?type=${type}`, METHOD.POST);
}
// 生成流程前写入合拼设计
export async function setCombinInfo(orderNo, businessOrderNo) {
  // return request(`/api/app/engineering-production/set-combin-info/${Id}`, METHOD.POST,)
  return request(`/api/app/engineering-production/set-combin-info?orderNo=${orderNo}&businessOrderNo=${businessOrderNo}`, METHOD.POST);
}
// 生成结构
export async function setMMKBAllData(Id) {
  return request(`/api/app/engineering-production/set-mMKBAll-data/${Id}`, METHOD.POST);
}
// 生成流程
export async function camAutoFlow(Id) {
  return request(`/api/app/engineering-production/cam-auto-flow/${Id}`, METHOD.POST);
}
//新MI生成
export async function ceshicamAutoFlow(Id) {
  return request(`/api/app/engineering-production/ceshi-cam-auto-flow/${Id}`, METHOD.POST);
}
// 叠层保存
export async function setStackImpInformation(params) {
  return request(`/api/app/engineering-production/set-stack-imp-information`, METHOD.POST, params);
}
// 板材信息获取项
export async function boardBrandItems(params) {
  return request(`/api/app/engineering-make-info-up/board-brand-items?fatory=${params}`, METHOD.GET);
}
//板材厂商
export async function sheetTraderItems(params) {
  return request(`/api/app/engineering-make-info-up/sheet-trader-items?fatory=${params}`, METHOD.GET);
}
// 选择文件
export async function setProFile(Id, params) {
  return request(`/api/app/engineering-production/set-pro-file/${Id}`, METHOD.POST, params);
}
// 特殊参数保存
export async function tableInformation(params) {
  return request(`/api/app/engineering-make-info-up/set-par-table-information`, METHOD.POST, params);
}
// 拼版设计保存
export async function setpnlengparinformation(params) {
  return request(`/api/app/engineering-make-info-up/set-pnl-eng-par-information`, METHOD.POST, params);
}
export async function gsFileList(OrderNo, factory) {
  //return request(`/api/app/gs/gs-file-list?OrderNo=${OrderNo}&factory=${factory}`, METHOD.GET,)
  return request(`/api/app/engineering-make-info/order-no-file-list?OrderNo=${OrderNo}&factory=${factory}`, METHOD.GET);
}
export async function setGsFileDelete(id) {
  return request(`/api/app/gs/${id}/set-gs-file-delete`, METHOD.POST);
}
export async function graphicPreview(Id) {
  return request(`/api/app/engineering-make-info/pro-order-graphic-preview/${Id}`, METHOD.POST);
}
//获取产品属性信息
export async function productfeatures(Id) {
  return request(`/api/app/engineering-make-info-up/product-features/${Id}`, METHOD.GET);
}
//保存产品属性信息
export async function setproductfeatures(params) {
  return request(`/api/app/engineering-make-info-up/set-product-features`, METHOD.POST, params);
}
//市场同步
export async function proorderinfosyncmkt(id) {
  return request(`api/app/pro-order-parameters-sync/${id}/pro-order-info-sync-mkt`, METHOD.POST);
}
export async function syncmkt2Erp(id) {
  return request(`api/app/pro-order-parameters-sync/${id}/sync-mkt2Erp `, METHOD.POST);
}
// 下载文件
export async function campnlfile(path, Factory, IsFolder) {
  return request(`/api/app/engineering-production/cam-pnl-file?path=${path}&Factory=${Factory}&IsFolder=${IsFolder}`, METHOD.GET);
}
// 删除文件
export async function deletecampnlfile(path, Factory, IsFolder, OrderNo) {
  return request(
    `/api/app/engineering-production/delete-cam-pnl-file?path=${path}&Factory=${Factory}&IsFolder=${IsFolder}&OrderNo=${OrderNo}`,
    METHOD.GET
  );
}
//阻焊油墨下拉
export async function soldercolorresistinkrelation(Factory) {
  return request(`/api/app/engineering-make-info-up/solder-color-resist-ink-relation?Factory=${Factory}`, METHOD.GET);
}
//字符油墨下拉
export async function fontcolorresistinkrelation(Factory) {
  return request(`/api/app/engineering-make-info-up/font-color-resist-ink-relation?Factory=${Factory}`, METHOD.GET);
}
//上传钢网文件
export async function uploadworkfile(params, id) {
  return request(`/api/app/engineering-make-info-up/up-load-work-file/${id}`, METHOD.POST, params);
}
//余厚、余厚公差数据联动
export async function getvcutselect(JoinFactoryId) {
  return request(`/api/app/engineering-make-info-up/get-vcutselect/${JoinFactoryId}`, METHOD.GET);
}
