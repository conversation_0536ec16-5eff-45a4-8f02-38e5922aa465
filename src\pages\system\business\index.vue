<!-- 系统管理-企业管理 -->
<template>
  <a-card>
    <div :class="advanced ? 'search' : null">
      <a-form layout="horizontal">
        <div :class="advanced ? null : 'fold'">
          <a-row>
            <a-col :md="7" :sm="24">
              <a-form-item
                  label="中文名"
                  :labelCol="{ span: 3 }"
                  :wrapperCol="{ span: 18, offset: 1 }"
              >
                <a-input
                    v-model="queryParam.EnterpriseName"
                    placeholder="搜索"
                />
              </a-form-item>
            </a-col>
            <a-col :md="7" :sm="24">
              <a-form-item
                  :labelCol="{ span:1 }"
                  :wrapperCol="{ span: 18, offset: 1 }"
              >
                <!--                <a-checkbox-->
                <!--                    v-model="queryParam.loack"-->
                <!--                >锁定用户</a-checkbox>-->
                <a-button type="primary" @click="refresh" >查询</a-button>
                <a-button
                    style="margin-left: 8px"
                    @click="() => {this.queryParam = {};this.loadData()}"
                >重置</a-button>
              </a-form-item>
            </a-col>
            <div class="operator">
              <a-button
                  v-if="checkPermission('AbpIdentity.ClaimTypes.Create')"
                  @click="addClick"
                  type="primary"
              >新建</a-button>
            </div>
          </a-row>
        </div>
      </a-form>
    </div>

    <div>

      <standard-table
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          @change="handleTableChange"
          :pagination="pagination"
          :loading="loading"
          :keyboard="false"
          :maskClosable="false"
      >
        <img slot="cardimageslot" slot-scope="text" style="60px; height:62px;" :src="text.text" />
        <div slot="action" slot-scope="{ record }">
          <template>
            <a-dropdown>
              <a class="ant-dropdown-link" href="#">
                操作
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay">
                <a-menu-item v-if="checkPermission('AbpIdentity.ClaimTypes.Delete')">
                  <a-popconfirm
                      title="确定要删除吗？"
                      @confirm="handleDel(record.id)"
                  >
                    <a href="javascript:;">删除</a>
                  </a-popconfirm>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="edit(record)">编辑</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </template>
        </div>
      </standard-table>
      <a-modal
          title="新建"
          :visible="visible"
          :confirm-loading="confirmLoading"
          @ok="handleOk"
          @cancel="handleCancel"
      >
        <a-form-model ref="ruleForm" :model="ruleForm" :rules="rules" v-bind="layout">
          <a-form-model-item has-feedback label="中文名" prop="pass">
            <a-input v-model="ruleForm.enterpriseName" autocomplete="off" />
          </a-form-model-item>
          <a-form-model-item has-feedback label="英文名" prop="checkPass">
            <a-input v-model="ruleForm.enterpriseEngName" autocomplete="off" />
          </a-form-model-item>
          <a-form-model-item has-feedback label="Logo" prop="age">
            <a-upload
                accept=".jpg,.jpeg,.png"
                list-type="picture-card"
                :show-upload-list="false"
                class="avatar-uploader"
                :customRequest="downloadFilesCustomRequest"
                :multiple="false"
            >
              <img v-if="imageUrl" :src="imageUrl" alt="avatar" style="max-width: 102px; max-height: 102px"/>
              <div v-else>
                <a-icon :type="uploadLoading ? 'loading' : 'plus'" />
                <div class="ant-upload-text">
                  Upload
                </div>
              </div>
            </a-upload>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </div>
  </a-card>
</template>

<script>
import StandardTable from "@/components/table/StandardTable";
import {getList, find, actionList, imgUpload} from "@/services/business";
import { checkPermission } from "@/utils/abp";
const columns = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 60,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "中文名",
    dataIndex: "enterpriseName",
    align: 'left',
  },
  {
    title: "英文名",
    dataIndex: "enterpriseEngName",
    align: 'left',
  },
  {
    title: "logo",
    dataIndex: "enterpriseLogo",
    scopedSlots: { customRender: 'cardimageslot' },
    align: 'left',
  },
  {
    title: "操作",
    scopedSlots: { customRender: "action" },
    align: 'center',
  },
];
let that;
function getBase64(img, callback) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}
export default {
  components: { StandardTable },
  data() {
    let checkPending;
    let checkAge = (rule, value, callback) => {
      clearTimeout(checkPending);
      if (value) {
        return callback(new Error('请输入企业中文名'));
      }
      checkPending = setTimeout(() => {
        callback();
      }, 1000);
    };
    let checkAge2 = (rule, value, callback) => {
      clearTimeout(checkPending);
      if (value) {
        return callback(new Error('请输入企业英文名'));
      }
      checkPending = setTimeout(() => {
        callback();
      }, 1000);
    };
    return {
      advanced: true,
      columns: columns,
      dataSource: [],
      selectedRows: [],
      pagination: {
        pageSize: 10,
        current: 1,
        showQuickJumper: true,
        showTotal: (total) => `总计 ${total} 条`,
      },
      sorter: {
        field: "id",
        order: "desc",
      },
      loading: false,
      queryParam: {},
      roles: [],
      organizations: [],
      replaceFields: {
        children: "children",
        title: "displayName",
        key: "id",
        value: "id",
      },
      visible:false,
      confirmLoading: false,
      ruleForm:{
        enterpriseName:'',
        enterpriseEngName:''

      },
      layout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 14 },
      },
      rules: {
        pass: [{ validator: checkAge, trigger: 'change' }],
        checkPass: [{ validator: checkAge2, trigger: 'change' }],
      },
      uploadLoading: false,
      imageUrl: '',
      isEdit: false
    };
  },
  mounted() {
    that = this;
    this.loadData();
  },
  filters: {
    sexFilter(key) {
      if (!key) {
        return "";
      }
      return sexMap[key].text;
    },
  },
  methods: {
    checkPermission,
    handleDel(id) {
      find({'id': id, type: 'DELETE'}).then((res) => {
        this.loadData();
        this.$message.info("删除成功");
      });
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    onSelectChange() {
      this.$message.info("选中行改变了");
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = { ...this.pagination };
      pager.current = pagination.current;
      this.pagination = pager;
      if (sorter.field) this.sorter = sorter;
      this.loadData();
    },
    loadData() {
      this.loading = true;
      let params = {
        ...this.pagination,
        ...this.queryParam,
        sorter: this.sorter,
      };
      console.log(params)
      getList(params)
          .then((res) => {
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
            this.dataSource = res.data.items;
          })
          .finally(() => {
            this.loading = false;
          });
    },
    refresh() {
      this.pagination.current = 1;
      this.loadData();
    },
    addClick(){
      this.isEdit = false
      this.visible = true
    },
    handleOk(e) {
      this.confirmLoading = true;
      let params = {}
      if (this.isEdit) {
        params.type = 'PUT'
      } else {
        params.type = 'POST'
      }

      params.obj = {
        ...this.ruleForm,
        'enterpriseLogo': this.imageUrl
      }
     var _self = this
      actionList(params).then(res => {
        console.log(res)
        if (res.code == 1){
          setTimeout(() => {
            _self.$message.success(res.message)
            _self.visible = false;
            _self.confirmLoading = false;
            _self.loadData()
          }, 2000);
        } else {
          _self.$message.error(res.message)
          _self.visible = false;
          _self.confirmLoading = false;
          _self.loadData()
        }


      })

    },
    handleCancel(e) {
      this.visible = false;
    },
    downloadFilesCustomRequest(data){
      const formData = new FormData()
      formData.append('file', data.file)
      imgUpload(formData).then(res =>{
        this.imageUrl = res
      })
    },
    edit(record){
      console.log(record)
      this.isEdit = true
      this.ruleForm.enterpriseEngName =  record.enterpriseEngName
      this.ruleForm.enterpriseName =  record.enterpriseName
      this.imageUrl = record.enterpriseLogo
      this.ruleForm['id'] = record.id
      this.visible = true
    }
  },
};
</script>

<style lang="less" scoped>
.search {
  margin-bottom: 54px;
}
.fold {
  width: calc(100% - 216px);
  display: inline-block;
}
.operator {
  margin-bottom: 18px;
}
@media screen and (max-width: 900px) {
  .fold {
    width: 100%;
  }
}
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>