<!--龙腾报价表单  -->
<template>
  <div class="pdfDom1" style="font-size: 13px">
    <a-button
      v-print="printObj1"
      @click="printpdf"
      type="primary"
      class="printstyle"
      >打印</a-button
    >
    <div
      id="ltreport0041"
      style="
        padding: 25px;
        font-family: 等线;
        color: black;
        font-weight: 600;
        position: relative;
      "
    >
      <div style="width: 100%; display: flex">
        <div style="text-align: center; width: 100%">
          <img src="@/assets/img/lt.png" style="height: 70px" />
          <div>
            <span
              style="font-size: 32px; font-weight: bold; letter-spacing: 5px"
              >报价单</span
            >
          </div>
        </div>
      </div>
      <div style="display: flex; line-height: 3ch">
        <div style="width: 50%; z-index: 99; font-size: 15px">
          <div>客户名称(Customer name):{{ LTreportdata.value_1 }}</div>
          <div>联系人(connect person):{{ LTreportdata.value_2 }}</div>
          <div>电话(tel No.):{{ LTreportdata.value_3 }}</div>
          <div>传真(fax No.):{{ LTreportdata.value_4 }}</div>
        </div>
        <div style="z-index: 99; width: 50%">
          <div style="float: right; font-size: 15px">
            <div>名称(marketing name):{{ LTreportdata.value_5 }}</div>
            <div>联系人(connect person):{{ LTreportdata.value_6 }}</div>
            <div>电话(tel No.):{{ LTreportdata.value_7 }}</div>
            <div>传真(fax No.):{{ LTreportdata.value_8 }}</div>
            <div>报价日期:{{ LTreportdata.value_9 }}</div>
          </div>
        </div>
      </div>
      <div>
        <table
          border="1"
          style="
            text-align: center;
            margin-top: 5px;
            width: 100%;
            border-top: 1px solid black;
            border-left: 1px solid black;
          "
        >
          <thead>
            <tr style="font-size: 15px">
              <td>No</td>
              <td style="width: 110px">产品名称<br />Part Number</td>
              <td>规格要求<br />Description</td>
              <td colspan="2">尺寸(SIZE)MM</td>
              <td>拼板<br />panel</td>
              <td>层数</td>
              <td>表面处理</td>
              <td>数量quantity</td>
              <td>单价(price)(RMB:元)</td>
              <td>工程费</td>
              <td>测试架</td>
              <td>合计</td>
              <td>备注</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in LTreportdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.price1 }}</td>
              <td>{{ item.price2 }}</td>
              <td>{{ item.price3 }}</td>
              <td>{{ item.price4 }}</td>
              <td>{{ item.price5 }}</td>
              <td>{{ item.price6 }}</td>
              <td>{{ item.price7 }}</td>
              <td>{{ item.price8 }}</td>
              <td>{{ item.price9 }}</td>
              <td>{{ item.price10 }}</td>
              <td>{{ item.price11 }}</td>
              <td>{{ item.price12 }}</td>
              <td>{{ item.price13 }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="z-index: 99; position: relative">
        <div
          style="
            display: flex;
            line-height: 2.8ch;
            margin-top: 8px;
            font-size: 15px;
          "
        >
          <div>
            <div style="font-size: 16px">备注(Remark):</div>
            <div>
              &nbsp;&nbsp;1&nbsp;&nbsp;样品交货期(Sample time):5-7天(2L);
              10-15天(4L)
            </div>
            <div style="color: red; font-weight: bold">
              &nbsp;&nbsp;2&nbsp;&nbsp;每次返单下单达到最小订单量:3㎡，请尽力下足
            </div>
            <div>
              &nbsp;&nbsp;3&nbsp;&nbsp;批量交货期(Lead
              time):首批:10-12天(2L);12-15天(4L)
            </div>
            <div>
              &nbsp;&nbsp;4&nbsp;&nbsp;交货方式(Delivery):{{
                LTreportdata.value_10
              }}
            </div>
            <div style="color: red; font-weight: bold">
              &nbsp;&nbsp;5&nbsp;&nbsp;付款方式(Payment method):{{
                LTreportdata.value_11
              }}
            </div>
            <div>
              &nbsp;&nbsp;6&nbsp;&nbsp;价格含税状况(Tax):{{
                LTreportdata.value_12
              }}
            </div>
            <div>
              &nbsp;&nbsp;7&nbsp;&nbsp;此报价有效期(Period of time):60天。
            </div>
          </div>
        </div>
      </div>
      <div
        style="
          display: flex;
          width: 100%;
          padding-top: 15px;
          font-size: 15px;
          z-index: 99;
          position: relative;
        "
      >
        <div style="width: 40%">
          <div>
            客&nbsp; 户&nbsp;确&nbsp;认:<span class="Underline"></span>&nbsp;
            &nbsp;
          </div>
          <div>Cust Confirm</div>
          <div>日期(DATE):<span class="Underline"></span>&nbsp; &nbsp;</div>
        </div>
        <div style="width: 35%">
          <div>
            核&emsp;&emsp;&emsp;准:<span class="Underline"></span>&nbsp; &nbsp;董艳丽
          </div>
          <div>QUOTE</div>
          <div>日期(DATE):<span class="Underline"></span>&nbsp; &nbsp;</div>
        </div>
        <div style="width: 25%">
          <div>
            制单:<span class="Underline"></span>&nbsp; &nbsp;{{
              LTreportdata.value_13
            }}
          </div>
          <div style="height: 22.5px"></div>
          <div>
            日期:<span class="Underline"></span>&nbsp; &nbsp;{{
              LTreportdata.value_14
            }}
          </div>
        </div>
      </div>
      <img
        src="@/assets/img/lthtz.png"
        style="
          position: absolute;
          bottom: 10px;
          z-index: 0;
          display: block;
          left: 554px;
          width: 150px;
          transform: rotate(353deg);
        "
      />
    </div>
  </div>
</template>
<script>
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["LTreportdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj1: {
        id: "ltreport0041", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.LTreportdata.price.length; index++) {
      if (
        this.LTreportdata.price[index].total &&
        this.LTreportdata.price[index].total != "/"
      ) {
        this.amountto += Number(this.LTreportdata.price[index].total);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.LTreportdata.pcbFileName;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getreportPdf() {
      htmlToPdfa3("ltreport0041", this.LTreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.Underline {
  position: relative;
  display: inline-block;
}
.Underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 200px; /* 下划线宽度 */
  height: 2px; /* 下划线高度 */
  background-color: rgb(107, 106, 106); /* 下划线颜色 */
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
  table > thead > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
  table > tbody > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
}
</style>
