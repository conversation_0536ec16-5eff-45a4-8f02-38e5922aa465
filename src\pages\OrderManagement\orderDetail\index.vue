<!--生产管理- 订单详情 -->
<template>
  <a-spin :spinning="spinning">
    <div class="orderDetail">
      <order-action
        @Feeding="Feeding"
        @examined="examined"
        @deleted="deleted"
        @modify="modify"
        @AllPrintClick="AllPrintClick"
        :ttype="ttype"
      ></order-action>
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="PCB订单详情">
          <order-info ref="editForm" :showData="showData"></order-info>
          <a-card title="SOP参数列表" :bordered="false">
            <template>
              <a-tree
                :replace-fields="{ children: 'children', title: 'name', key: 'name' }"
                style="width: 50%"
                :tree-data="treeData"
                v-if="treeData.length"
                :defaultExpandParent="true"
                :defaultExpandAll="true"
                @select="changefirstpass($event)"
              >
              </a-tree>
            </template>
            <a-table
              :columns="columns2"
              :dataSource="sopData"
              :pagination="false"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :loading="tableLoading1"
              :customRow="onClickRow"
              ref="FlowList"
              :rowClassName="isRedRow1"
            >
              <span slot="num" slot-scope="text, record, index">
                {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
              </span>
              <template slot="craft" slot-scope="record">
                <span v-for="(item, index) in record.paraList || []" :key="index">
                  <p style="margin: 0">{{ item.name }}:{{ item.values }};</p>
                </span>
              </template>
            </a-table>
          </a-card>
          <a-card title="参数信息" :bordered="false">
            <a-table
              :columns="columns3"
              :dataSource="parameterData"
              :pagination="false"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :loading="tableLoading2"
              :scroll="{ x: 1200 }"
            >
              <template slot="orderNo" slot-scope="record">
                <span>{{ record.orderNo }}</span>
                <!-- <span style="color:red;">(加急)</span>    fontColorStr        -->
              </template>
              <template slot="copperThickness" slot-scope="record">
                <span>{{ record.innerCopperThickness }}/{{ record.copperThickness }}oz</span>
              </template>
              <template slot="boardWidth" slot-scope="record">
                <span>{{ record.boardHeight }}*{{ record.boardWidth }}</span>
              </template>
              <template slot="vCut" slot-scope="record">
                <span v-if="record.vCut == 'none'">无</span>
                <span v-else>{{ record.vCut }}</span>
              </template>
              <template slot="solderColorStr" slot-scope="record">
                <span v-if="record.solderColorStr">
                  <div
                    v-if="record.solderColorStr.indexOf('绿') != -1"
                    style="width: 14px; height: 13px; background: green; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorStr.indexOf('红') != -1"
                    style="width: 14px; height: 13px; background: red; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorStr.indexOf('白') != -1"
                    style="width: 14px; height: 13px; background: white; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorStr.indexOf('黑') != -1"
                    style="width: 14px; height: 13px; background: black; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorStr.indexOf('黄') != -1"
                    style="width: 13px; height: 13px; background: yellow; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorStr.indexOf('蓝') != -1"
                    style="width: 14px; height: 13px; background: blue; display: inline-block; border: 1px solid black"
                  ></div>
                  <a-icon v-if="record.solderColorStr == 'none'" type="close-square" style="color: #d1cbcb"></a-icon>
                  {{ record.solderColorStr }}
                </span>
                <span v-if="record.solderColorBottomStr">
                  /
                  <div
                    v-if="record.solderColorBottomStr.indexOf('绿') != -1"
                    style="width: 14px; height: 13px; background: green; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorBottomStr.indexOf('红') != -1"
                    style="width: 14px; height: 13px; background: red; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorBottomStr.indexOf('白') != -1"
                    style="width: 14px; height: 13px; background: white; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorBottomStr.indexOf('黑') != -1"
                    style="width: 14px; height: 13px; background: black; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorBottomStr.indexOf('黄') != -1"
                    style="width: 13px; height: 13px; background: yellow; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.solderColorBottomStr.indexOf('蓝') != -1"
                    style="width: 14px; height: 13px; background: blue; display: inline-block; border: 1px solid black"
                  ></div>
                  <a-icon v-if="record.v == 'none'" type="close-square" style="color: #d1cbcb"></a-icon>
                  {{ record.solderColorBottomStr }}
                </span>
              </template>
              <template slot="fontColorStr" slot-scope="record">
                <span v-if="record.fontColorStr">
                  <div
                    v-if="record.fontColorStr.indexOf('绿') != -1"
                    style="width: 14px; height: 13px; background: green; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorStr.indexOf('红') != -1"
                    style="width: 14px; height: 13px; background: red; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorStr.indexOf('白') != -1"
                    style="width: 14px; height: 13px; background: white; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorStr.indexOf('黑') != -1"
                    style="width: 14px; height: 13px; background: black; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorStr.indexOf('黄') != -1"
                    style="width: 14px; height: 13px; background: yellow; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorStr.indexOf('蓝') != -1"
                    style="width: 14px; height: 13px; background: blue; display: inline-block; border: 1px solid black"
                  ></div>
                  <a-icon v-if="record.fontColorStr == 'none'" type="close-square" style="color: #d1cbcb"></a-icon>
                  {{ record.fontColorStr }}
                </span>
                <span v-if="record.fontColorBottomStr">
                  /
                  <div
                    v-if="record.fontColorBottomStr.indexOf('绿') != -1"
                    style="width: 14px; height: 13px; background: green; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorBottomStr.indexOf('红') != -1"
                    style="width: 14px; height: 13px; background: red; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorBottomStr.indexOf('白') != -1"
                    style="width: 14px; height: 13px; background: white; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorBottomStr.indexOf('黑') != -1"
                    style="width: 14px; height: 13px; background: black; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorBottomStr.indexOf('黄') != -1"
                    style="width: 14px; height: 13px; background: yellow; display: inline-block; border: 1px solid black"
                  ></div>
                  <div
                    v-if="record.fontColorBottomStr.indexOf('蓝') != -1"
                    style="width: 14px; height: 13px; background: blue; display: inline-block; border: 1px solid black"
                  ></div>
                  <a-icon v-if="record.fontColorBottomStr == 'none'" type="close-square" style="color: #d1cbcb"></a-icon>
                  {{ record.fontColorBottomStr }}
                </span>
              </template>
            </a-table>
          </a-card>
          <a-card title="管制卡列表" :bordered="false">
            <a-table
              :columns="columns4"
              :dataSource="ControlData"
              :pagination="false"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :loading="tableLoading3"
            >
              <template slot="currentStepName" slot-scope="record">
                <span :title="record.currentStepName" style="color: #428bca" @click.stop="cardNoClick(record)">{{ record.currentStepName }} </span>
              </template>
              <template slot="action" slot-scope="record">
                <span @click="PrintPreview(record)" style="margin-right: 5px; color: #409eff">打印</span>
                <span @click="editPreview(record)" style="margin-right: 5px; color: #409eff">修改</span>
                <span @click="feed(record)" style="margin-right: 5px; color: #409eff" v-if="checkPermission('MES.ProManagement.Feeding.BuFeeding')"
                  >补料</span
                >
                <span
                  @click="Scorecard(record)"
                  style="margin-right: 5px; color: #409eff"
                  v-if="checkPermission('MES.ProManagement.Feeding.Scorecard')"
                  >分卡</span
                >
              </template>
            </a-table>
          </a-card>
        </a-tab-pane>
        <a-tab-pane key="2" tab="操作日志">
          <a-table
            :rowKey="'id'"
            :columns="columns1"
            :pagination="false"
            :dataSource="viewLogData"
            :scroll="{ y: 672 }"
            :class="viewLogData.length ? 'mintable' : ''"
          >
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
    <a-modal title="投料单填写" :visible="modelVisible1" destroyOnClose @cancel="handleCancel" @ok="feedingOk" centered>
      <feeding-modal ref="feeding" :pinBanNum="pinBanNum" />
    </a-modal>
    <a-modal title="修改" :visible="modelVisible2" @cancel="handleCancel" @ok="modifyOk">
      <modify-modal ref="modify" :formInfo="formInfo" />
    </a-modal>
    <!--打印流程卡弹窗 -->
    <a-modal title="流程卡" :visible="dataVisible1" @cancel="handleCancel" destroyOnClose :maskClosable="false" :width="1000" :footer="null">
      <report-info ref="report" :CardId="CardId" :orderNo="orderNo" :businessOrderNo="businessOrderNo" />
    </a-modal>
    <!-- 确认弹窗 -->
    <a-modal
      title=" 确认弹窗"
      :visible="dataVisible6"
      @cancel="handleCancel"
      @ok="handleOkMode"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span style="font-size: 16px">确认拼版作废吗？</span>
    </a-modal>
    <!-- 分卡 -->
    <a-modal
      :title="'分卡' + '-' + headerSTR"
      :visible="dataVisible2"
      @cancel="handleCancel"
      @ok="handleOk2"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="300"
      centered
    >
      <div>
        <a-form-model-item label="数量" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-input v-model="Num" :autoFocus="autoFocus" allowClear></a-input>
        </a-form-model-item>
      </div>
    </a-modal>
    <a-modal title="过数详情" :visible="modelVisible" @cancel="handleCancel" :footer="null" :width="750">
      <excess-details ref="ExcessDetails" :ExcessData="ExcessData" />
    </a-modal>
  </a-spin>
</template>

<script>
import OrderAction from "@/pages/OrderManagement/orderDetail/modules/OrderAction";
import OrderInfo from "@/pages/OrderManagement/orderDetail/modules/OrderInfo";
import FeedingModal from "@/pages/OrderManagement/orderDetail/modules/FeedingModal";
import modifyModal from "@/pages/OrderManagement/orderDetail/modules/modifyModal";
import ReportInfo from "@/pages/OrderManagement/orderDetail/modules/ReportInfo";
import ExcessDetails from "@/pages/OrderManagement/ProgressManagement/modules/ExcessDetails";
import { mapState } from "vuex";
import moment from "moment";
import { checkPermission } from "@/utils/abp";
import JsBarcode from "jsbarcode";
import { cardFlowList } from "@/services/scgl/OrderManagement/progress";
import {
  proPinBanOrderList,
  sopParaList,
  proCardInfo,
  proCardInfoFeed,
  proCardInfoList,
  proPinBanOrder,
  setChecked,
  cancelPinBanOrder,
  pinBanOrderLogList,
  pinBanDetilList,
  getForEdit,
  setPinBanInfo,
  scorecard,
} from "@/services/scgl/OrderManagement/Composition";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
    className: "left-bor",
  },
  {
    title: "操作时间",
    dataIndex: "createTime",
    align: "left",
    width: 100,
  },
  {
    title: "操作详情",
    dataIndex: "content",
    align: "left",
    width: 700,
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
    className: "left-bor",
  },
  {
    title: "工序名称",
    dataIndex: "stepName",
    align: "left",
    width: 160,
  },
  {
    title: "标准时长",
    dataIndex: "timeSpan",
    align: "left",
    width: 115,
  },
  {
    title: "工艺参数",
    align: "left",
    scopedSlots: { customRender: "craft" },
  },
];
const columns3 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
    className: "left-bor",
  },
  {
    title: "订单编号",
    align: "center",
    width: 160,
    scopedSlots: { customRender: "orderNo" },
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "center",
    width: 45,
  },
  {
    title: "TG值",
    dataIndex: "fR4Tg",
    align: "center",
    width: 70,
  },
  {
    title: "板厚",
    dataIndex: "boardThickness",
    align: "center",
    width: 45,
  },
  {
    title: "材料",
    dataIndex: "fR4TypeStr",
    align: "center",
    width: 70,
  },
  {
    title: "铜厚(内)/(外)",
    align: "center",
    width: 90,
    scopedSlots: { customRender: "copperThickness" },
  },
  {
    title: " 阻焊 (顶)/(底）",
    align: "center",
    width: 120,
    scopedSlots: { customRender: "solderColorStr" },
  },
  {
    title: "文字(顶)/(底）",
    align: "center",
    width: 130,
    scopedSlots: { customRender: "fontColorStr" },
  },
  {
    title: "工艺",
    dataIndex: "surfaceFinishStr",
    align: "center",
    width: 90,
  },
  {
    title: "过孔处理",
    dataIndex: "solderCoverStr",
    align: "center",
    width: 80,
  },
  {
    title: "尺寸信息",
    align: "center",
    width: 100,
    scopedSlots: { customRender: "boardWidth" },
  },
  {
    title: "set样式",
    dataIndex: "pinBanType",
    align: "center",
    width: 80,
  },
  {
    title: "需求数量",
    dataIndex: "actualNum",
    align: "center",
    width: 100,
  },
  {
    title: "预投数",
    dataIndex: "ytPinBanNum",
    align: "center",
    width: 60,
  },
  {
    title: "每拼数",
    dataIndex: "eachPinBanNum",
    align: "center",
    width: 50,
  },
  {
    title: "需求面积",
    dataIndex: "boardArea",
    align: "center",
    width: 100,
  },
  {
    title: "V割方式",
    align: "center",
    width: 100,
    scopedSlots: { customRender: "vCut" },
  },
  {
    title: "阻抗报告",
    // dataIndex: 'impedanceReport',
    align: "center",
    width: 80,
    customRender: (text, record, index) => `${record.impedanceReport ? "需要" : "不需要"}`,
  },
  {
    title: "金手指倒斜边",
    dataIndex: "goldfinger",
    align: "center",
    width: 100,
  },
  {
    title: "业务员备注",
    dataIndex: "cnNote",
    align: "center",
    width: 100,
  },
  {
    title: "客户备注",
    dataIndex: "note",
    align: "center",
    width: 100,
  },
  {
    title: "业务员",
    dataIndex: "followAdminName",
    align: "center",
    width: 100,
  },
  {
    title: "工程人员",
    dataIndex: "proAdminName",
    align: "center",
    width: 100,
  },
  {
    title: "工程审核",
    dataIndex: "camCheckName",
    align: "center",
    width: 100,
  },
  {
    title: "操作",
    dataIndex: "",
    align: "center",
    width: 50,
  },
];
const columns4 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
    className: "left-bor",
  },
  {
    title: "管制卡编号",
    dataIndex: "cardNo",
    align: "left",
    width: 160,
  },
  {
    title: "投料时间",
    dataIndex: "createTime",
    align: "left",
    width: 160,
  },
  {
    title: "投料人员",
    dataIndex: "adminName",
    align: "left",
    width: 70,
  },
  {
    title: "状态",
    dataIndex: "statusStr",
    align: "left",
    width: 90,
  },
  {
    title: "当前工序",
    className: "btosty",

    children: [
      {
        title: "工序名",
        // dataIndex: 'currentStepName',
        align: "left",
        width: 120,
        scopedSlots: { customRender: "currentStepName" },
      },
      {
        title: "过数",
        dataIndex: "currentNum",
        align: "left",
        width: 130,
      },
      {
        title: "过数时间",
        dataIndex: "currentTime",
        align: "left",
        width: 170,
      },
      {
        title: "完成比例",
        dataIndex: "currentStepCount",
        align: "left",
        width: 100,
      },
    ],
  },

  {
    title: "特殊需求",
    dataIndex: "remark",
    align: "left",
    width: 180,
  },
  {
    title: "板材单价",
    dataIndex: "boardPrice",
    align: "left",
    width: 110,
  },
  {
    title: "操作",
    //width: 100,
    align: "left",
    scopedSlots: { customRender: "action" },
  },
];
export default {
  name: "OrderDetail",
  components: { OrderInfo, OrderAction, FeedingModal, modifyModal, ReportInfo, ExcessDetails },
  data() {
    return {
      showData: {},
      spinning: false,
      columns1,
      viewLogData: [],
      treeData: [],
      Data: [],
      value2: "",
      selectVal: "",
      sopData: [],
      columns2,
      tableLoading1: false,
      columns3,
      parameterData: [],
      tableLoading2: false,
      modelVisible1: false,
      modelVisible2: false,
      columns4,
      ControlData: [],
      tableLoading3: false,
      dataVisible1: false,
      dataVisible6: false,
      CardId: "",
      formInfo: {},
      orderNo: "",
      pinBanNum: "",
      businessOrderNo: "",
      feedFlg: false,
      cardNo: "",
      autoFocus: true,
      dataVisible2: false,
      headerSTR: "",
      recordId: "",
      Num: null,
      modelVisible: false,
      ExcessData: [],
      ttype: "",
    };
  },
  async created() {
    await this.getDetailInfo();
    this.ttype = this.$route.query.ttype;
    this.getControlData();
    this.getViewLog();
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  watch: {
    pagination: {
      handler(val) {
        console.log(val);
      },
    },
  },
  methods: {
    checkPermission,
    moment,
    // 参数信息
    async getDetailInfo() {
      this.spinning = true;
      let id = this.$route.query.id;
      let businessOrderNo = this.$route.query.businessOrderNo;
      await proPinBanOrder(id, businessOrderNo).then(res => {
        if (res.code) {
          this.showData = res.data;
        }
      });
      pinBanDetilList(id, businessOrderNo).then(res => {
        if (res.code) {
          this.parameterData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
      sopParaList(id, businessOrderNo).then(res => {
        this.tableLoadin1 = true;
        if (res.code) {
          this.treeData = [res.data];
          this.sopData = res.data.processParams;
          this.flatten([res.data]);
        }
      });
      this.spinning = false;
      this.tableLoading1 = false;
    },
    getControlData() {
      let params = {
        PageIndex: 1,
        PageSize: 999,
        OrderNo: this.$route.query.id,
        BusinessOrderNo: this.$route.query.businessOrderNo,
      };
      this.tableLoading3 = true;
      proCardInfoList(params)
        .then(res => {
          if (res.code) {
            this.ControlData = res.data.items;
          }
        })
        .finally(() => {
          this.tableLoading3 = false;
        });
    },
    isRedRow1(record) {
      let strGroup = [];
      let str = [];
      if (record.orderNo && record.orderNo == this.orderNo) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    async changefirstpass($event) {
      this.tableLoading1 = true;
      let name = $event[0];
      var obj = this.Data.filter(item => {
        return item.name == name;
      });
      if (obj.length) {
        this.sopData = obj[0].processParams;
      }
      this.tableLoading1 = false;
      console.log("this.sopData ", obj, this.sopData);
    },
    flatten(arr) {
      return [].concat(
        ...arr.map(item => {
          this.Data.push(item);
          if (item.children) {
            let arr = [].push(item, ...this.flatten(item.children));
            return arr[item];
          }
          return [].concat[item];
        })
      );
    },
    // 投料单填写
    Feeding() {
      this.modelVisible1 = true;
      this.pinBanNum = this.showData.pinBanNum;
    },
    feedingOk() {
      if (this.feedFlg) {
        let params = this.$refs.feeding.form;
        var arr1 = params.num.split("");
        if (arr1.length > 30) {
          arr1 = arr1.slice(0, 30);
        }
        params.num = arr1.join("");
        params.num = Number(params.num);
        params.name = this.user.name;
        params.account = this.user.userName;
        params.cardNo = this.cardNo;
        params.businessOrderNo = this.$route.query.businessOrderNo;
        console.log("保存数据", params);
        proCardInfoFeed(params)
          .then(res => {
            if (res.code) {
              this.$message.success("成功");
              this.getControlData();
            } else {
              this.$message.error(res.message);
              this.$refs.feeding.form.num = this.showData.pinBanNum;
              this.$refs.feeding.form.remarks = "";
            }
          })
          .finally(() => {
            this.modelVisible1 = false;
          });
      } else {
        let params = this.$refs.feeding.form;
        params.num = Number(params.num);
        params.name = this.user.name;
        params.orderNo = this.$route.query.id;
        (params.cardNum = 1),
          // params.isFeed = false,
          (params.account = this.user.userName);
        params.businessOrderNo = this.$route.query.businessOrderNo;
        proCardInfo(params)
          .then(res => {
            if (res.code) {
              this.$message.success("成功");
              this.getControlData();
            } else {
              this.$message.error(res.message);
              this.$refs.feeding.form.num = this.showData.pinBanNum;
              this.$refs.feeding.form.remarks = "";
            }
          })
          .finally(() => {
            this.modelVisible1 = false;
          });
      }
    },
    handleCancel() {
      this.modelVisible1 = false;
      this.dataVisible1 = false;
      this.modelVisible2 = false;
      this.dataVisible6 = false;
      this.dataVisible2 = false;
      this.modelVisible = false;
    },
    PrintPreview(record) {
      this.CardId = record.id;
      this.businessOrderNo = record.businessOrderNo;
      this.orderNo = this.$route.query.id;
      this.dataVisible1 = true;
    },
    editPreview(record) {
      this.$router.push({ path: "cardDetail", query: { id: record.id } });
    },
    // 补料
    feed(record) {
      console.log("record", record);
      this.feedFlg = true;
      this.modelVisible1 = true;
      this.pinBanNum = record.num;
      this.cardNo = record.cardNo;
    },
    // 订单列表点击事件
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.guid_ = record.guid_;
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowKeysArray = keys;
            this.pdctNo_ = record.pdctNo_;
          },
        },
      };
    },
    // 查看日志
    getViewLog() {
      this.spinning = true;
      let Id = this.showData.id;
      pinBanOrderLogList(Id)
        .then(res => {
          if (res.code) {
            this.viewLogData = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 拼版审核
    examined() {
      let params = {
        orderNo: this.$route.query.id,
        checkAccount: this.user.userName,
        checkName: this.user.name,
        businessOrderNo: this.$route.query.businessOrderNo,
      };
      setChecked(params).then(res => {
        if (res.code) {
          this.$message.success("审核成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 拼版作废
    deleted() {
      this.dataVisible6 = true;
    },
    handleOkMode() {
      let params = {
        orderNo: this.$route.query.id,
        account: this.user.userName,
        name: this.user.name,
        businessOrderNo: this.$route.query.businessOrderNo,
      };
      this.dataVisible6 = false;
      cancelPinBanOrder(params).then(res => {
        if (res.code) {
          this.$message.success("作废成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 修改编辑
    modify() {
      let Id = this.showData.id;
      getForEdit(Id).then(res => {
        if (res.code) {
          this.formInfo = res.data;
          this.formInfo.deliveryDate = moment(this.formInfo.deliveryDate);
        }
      });
      this.modelVisible2 = true;
    },
    modifyOk() {
      let params = this.$refs.modify.formInfo;
      params.boardThickness = Number(params.boardThickness);
      params.copperThickness = Number(params.copperThickness);
      params.innerCopperThickness = Number(params.innerCopperThickness);
      (params.account = this.user.userName), (params.name = this.user.name);
      setPinBanInfo(params).then(res => {
        if (res.code) {
          this.$message.success("修改成功");
          this.getViewLog();
        } else {
          this.$message.error(res.error);
        }
      });
      this.modelVisible2 = false;
    },
    // 多张流程卡打印
    AllPrintClick() {
      var arr = this.ControlData;
      for (var i = 0; i < arr.length; i++) {
        console.log(arr[i].id);
        var routeOne = this.$router.resolve({
          path: "/ProcessCard",
          query: { id: arr[i].id, businessOrderNo: arr[i].businessOrderNo },
        });
        window.open(routeOne.href, "_blank", routeOne.query);
      }
    },
    // 分卡
    Scorecard(record) {
      console.log("record", record);
      this.headerSTR = record.cardNo;
      this.recordId = record.id;
      this.dataVisible2 = true;
    },
    handleOk2() {
      var arr = this.Num.split("");
      if (arr.length > 10) {
        arr = arr.slice(0, 10);
      }
      let num = Number(arr.join(""));
      scorecard(this.recordId, num).then(res => {
        if (res.code) {
          this.$message.success("分卡成功");
          this.getControlData();
        } else {
          this.$message.error(res.message);
        }
      });
      this.dataVisible2 = false;
    },
    // 工序弹窗
    cardNoClick(record) {
      cardFlowList(record.cardNo).then(res => {
        if (res.code) {
          this.ExcessData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
      this.modelVisible = true;
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table-tbody > tr > td {
  font-size: 14px;
}
/deep/.ant-tabs-tab-active ant-tabs-tab {
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.left-bor {
  border-left: 1px solid #efefef;
}
.orderDetail {
  padding: 10px;
  min-width: 1670px;
  background: #ffffff;
  /deep/.rowBackgroundColor {
    background: #fff9e6 !important;
  }
  /deep/.ant-table {
    .btosty {
      border-bottom: 1px solid #efefef;
    }
  }
  /deep/.ant-table-thead > tr > th {
    padding: 5px 0 5px 5px !important;
    border-top: 1px solid #efefef;
    border-right: 1px solid #efefef;
    // border-color:#f0f0f0!important;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 5px 0 5px 5px !important;
    border-right: 1px solid #efefef;
    // border-color:#f0f0f0!important;
  }
  .min-table {
    .ant-table-body {
      min-height: 702px;
    }
  }
  /deep/.mintable {
    .ant-table-body {
      height: 702px;
      // overflow: auto;
      min-height: 702px;
    }
  }
  /deep/ .ant-tabs {
    // .viewInfo{
    //   .ant-table-thead{
    //     .ant-table-align-left{
    //       text-align: center!important;;
    //     }
    //   }
    // }
    margin-top: 10px;
    .ant-tabs-bar {
      margin: 0;
      border-bottom: 1px solid #ccc;
      .ant-tabs-nav-wrap {
        .ant-tabs-ink-bar {
          display: none !important;
        }
      }
      .ant-tabs-tab {
        margin: 0;
        padding: 0 10px;
        border: 1px solid #ccc;
        font-size: 14px;
        height: 34px;
        line-height: 34px;
        border-left: 0;
        font-weight: 500;
        &:nth-child(1) {
          border-left: 1px solid #ccc;
        }
      }
      .ant-tabs-tab-active {
        border-top: 2px solid #f90 !important;
        border-bottom-color: #ffffff;
        background: #ffffff;
      }
    }
  }
}
/deep/ .ant-card {
  .ant-card-head {
    padding: 0;
    min-height: auto;
    border: 0;
    .ant-card-head-title {
      padding: 0;
      border-bottom: 2px solid #ddd;
      height: 29px;
      line-height: 20px;
      margin-bottom: 15px;
      text-indent: 5px;
      font-size: 16px;
      font-weight: 500;
      color: #000;
      padding-bottom: 8px;
      margin-top: 15px;
    }
  }
  .ant-card-body {
    padding: 24px;
    .ant-form {
      border-left: 1px solid #ddd;
      border-top: 1px solid #ddd;
    }
    .ant-form-item {
      margin: 0;
      width: 100%;
      display: flex;

      .editWrapper {
        display: flex;
        align-items: center;
        min-height: 32px;
        .ant-select {
          width: 120px;
        }
        .ant-input {
          width: 120px;
        }
        .ant-input-number {
          width: 120px;
        }
      }
      .ant-form-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-family: PingFangSC-Regular, Sans-serif;
        font-size: 14px;
        color: #666;
        background-color: #fafafa;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        label {
          font-family: PingFangSC-Regular, Sans-serif;
          font-size: 14px;
        }
      }
      .ant-form-item-control-wrapper {
        font-family: PingFangSC-Regular, Sans-serif;
        font-size: 14px;
        .ant-form-item-control {
          .ant-form-item-children {
            display: block;
            min-height: 13.672px;
          }
          line-height: inherit;
          padding: 8px 10px;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
        }
      }
    }
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-table {
  font-size: 14px !important;
}
</style>
