<!-- 工程管理 - QAE审核- 按钮 -->
<template>
  <div class="active" ref="active">
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeSearch')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeSearch') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeSendOrder')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeSendOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="assignClick" :loading="assignLoading"> 分派(S) </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeOrder')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="TakeOrderClick">
        <!-- <span v-if="isDisabled">{{ totalTime }}</span> :disabled="isDisabled"-->取单(Q)
      </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeWenKeUrl')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeWenKeUrl') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="wenkeClick"> 问客 </a-button>
    </div>
    <!-- 
    <div class="box" v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeModifyPar')"
     :class='checkPermission("MES.EngineeringModule.EngineeringQae.EngineeringQaeModifyPar")?"showClass":""'>
      <a-button type="primary" @click="EditParametersClickbtn">
        查看参数
      </a-button>
    </div>      -->

    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFinish')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="AuditCompletedClick"> 完成 </a-button>
    </div>
    <!-- <div class="box"  v-show="advanced&&(checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeAutoStack') || checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeImp'))"
    :class='checkPermission("MES.EngineeringModule.EngineeringQae.EngineeringQaeAutoStack")|| checkPermission("MES.EngineeringModule.EngineeringQae.EngineeringQaeImp")?"showClass":""'>    
    <a-button type="primary" @click ="GenerateStackClick">叠层阻抗</a-button>       
    </div> -->

    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeBackOrder')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeBackOrder') ? 'showClass' : ''"
    >
      <div>
        <a-button type="primary" @click="ChargebackClick">审核回退</a-button>
      </div>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeBack2Cam')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeBack2Cam') ? 'showClass' : ''"
    >
      <div>
        <a-button type="primary" @click="FallbackFrontClick">回退CAM</a-button>
      </div>
    </div>

    <!-- <div
      class="box"
      v-show="advanced && checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFixStart')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFixStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="RepairStartClick"> 返修开始 </a-button>
    </div> -->

    <div
      class="box"
      v-show="advanced && checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFixRecord')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFixRecord') ? 'showClass' : ''"
    >
      <div>
        <a-button type="primary" @click="RegisterClick"> 返修登记 </a-button>
      </div>
    </div>
    <!-- <div class="box showClass" v-show="advanced">
      <a-button type="primary" @click="PerformanceClick" >
        绩效管理
      </a-button>     
    </div> -->
    <div
      class="box"
      v-show="advanced && checkPermission('MES.EngineeringModule.EngineeringQae.EngPreContractNoticeQAE')"
      :class="checkPermission('MES.EngineeringModule.EngineeringQae.EngPreContractNoticeQAE') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ProductionOrder"> 生产单 </a-button>
    </div>
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
            <a-menu-item @click="assignClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeSendOrder')"
              >分派(S)</a-menu-item
            >
            <a-menu-item @click="TakeOrderClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeOrder')"
              >取单(Q)</a-menu-item
            >
            <a-menu-item @click="wenkeClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeWenKeUrl')">问客</a-menu-item>
            <a-menu-item @click="AuditCompletedClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFinish')"
              >完成</a-menu-item
            >
            <a-menu-item @click="ChargebackClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeBackOrder')"
              >审核回退</a-menu-item
            >
            <a-menu-item @click="FallbackFrontClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeBack2Cam')"
              >回退CAM</a-menu-item
            >
            <!-- <a-menu-item @click="RepairStartClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFixStart')"
              >返修开始</a-menu-item
            > -->
            <a-menu-item @click="RegisterClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFixRecord')"
              >返修登记</a-menu-item
            >
            <!-- <a-menu-item @click="PerformanceClick">绩效管理</a-menu-item> -->
            <a-menu-item @click="ProductionOrder" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngPreContractNoticeQAE')"
              >生产单</a-menu-item
            >
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeStart')">
        <a-button type="primary" @click='QaeStartClick' >
          开始
        </a-button>
      </div> -->
    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeImp')">
      <a-button type="primary" @click ="GenerateStackClick">
        叠层阻抗
      </a-button>
    </div> -->
    <!-- <div v-if='advanced'  >
     <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFixRecord')">
       <a-button type="primary" @click='RepairRecordClick'>
         返修记录
       </a-button>
     </div>
   </div>     -->
    <!-- <div v-if='advanced'  >
      <div class="box">
        <a-button type="primary" @click='CustomerRulesClick' v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeRuleShowInfoPpe')">
          客户规则
        </a-button>
      </div>
    </div> -->
    <!-- <div v-if='advanced'  >
      <div class="box" >
          <a-button type="primary" @click="SecondaryAuditAssignClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeSendOrder2')">
            二审分派
          </a-button>
        </div>
    </div> -->
    <!-- <div v-if='advanced'  >
        <div class="box" >
          <a-button type="primary" @click="SecondaryAuditCompletedClick" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeFinish2')">
            二审完成
          </a-button>
        </div>
    </div> -->
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { projectBackEndAssignBack } from "@/services/projectApi";

export default {
  name: "QaeAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      selectValue: "生成叠层",
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
      nums: "",
      buttonsmenu: false,
      isDisabled: false,
      totalTime: 90,
    };
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.active.children.length > 7) {
        // let domLength = this.$refs.active.children.length
        // let sty_ = ''
        // for (var i=0; i< domLength; i++) {
        //   if (i == this.$refs.active.children.length-1) {
        //     sty_ = "order:11";
        //   } else {
        //     sty_ = "order:"+i*2;
        //   }
        //   this.$refs.active.children[i].style.cssText = sty_
        // }
        // this.width = 1500
        this.collapsed = true;
      } else {
        this.collapsed = false;
        // this.width = 762
      }
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length + 1;
      this.nums = elements.length + 1;
      if (num <= 7) {
        this.showBtn = false;
        this.advanced = true;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      for (var a = 0; a < 6; a++) {
        elements[a].style.cssText = "";
      }
      this.handleResize();
      window.addEventListener("resize", this.handleResize, true);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      //var elements1 = document.getElementsByClassName("showClass1")
      let num = "";
      if (!this.advanced) {
        num = 8 * 104;
      } else {
        num = (elements.length + 1) * 104;
      }
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          //elements1[0].style.display = "inline-block";
          for (let i = 0; i < elements.length; i++) {
            if (i < 6) {
              elements[i].style.display = "inline-block";
            }
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            //elements1[0].style.display = "none";
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.advanced = false;
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              // elements1[0].style.display = "none";
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              //elements1[0].style.display = "inline-block";
              for (let i = 0; i < elements.length; i++) {
                if (i < 6) {
                  elements[i].style.display = "inline-block";
                }
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        //elements1[0].style.display = "inline-block";
        for (let i = 0; i < elements.length; i++) {
          if (i < 6) {
            elements[i].style.display = "inline-block";
          }
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      if (this.advanced) {
        width_ = 1500;
      } else {
        width_ = 762;
        this.$nextTick(() => {
          const elements = document.getElementsByClassName("showClass");
          for (var a = 0; a < 6; a++) {
            elements[a].style.cssText = "";
          }
        });
      }
      this.$refs.active.style.width = width_ + "px";
      this.handleResize();
    },
    //分派
    assignClick() {
      this.$emit("assignClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    PerformanceClick() {
      this.$emit("PerformanceClick");
    },
    ProductionOrder() {
      this.$emit("ProductionOrder");
    },
    // 修改信息
    modifyInfoClick() {
      this.$emit("modifyInfoClick");
    },
    // 注意事项
    // mattersNeedingAttentionClick(){
    //   this.$emit('mattersNeedingAttentionClick')
    // },
    // 返修开始
    RepairStartClick() {
      this.$emit("RepairStartClick");
    },
    // 返修登记
    RegisterClick() {
      this.$emit("RegisterClick");
    },
    // 返修记录
    RepairRecordClick() {
      this.$emit("RepairRecordClick");
    },
    // 客户规则
    // CustomerRulesClick(){
    //   this.$emit('CustomerRulesClick')
    // },
    // 问客
    wenkeClick() {
      this.$emit("wenkeClick");
    },
    EditParametersClickbtn() {
      this.$emit("EditParametersClickbtn");
    },
    // 审核开始
    QaeStartClick() {
      this.$emit("QaeStartClick");
    },
    // 审核完成
    AuditCompletedClick() {
      this.$emit("AuditCompletedClick");
    },
    //  取单
    TakeOrderClick() {
      this.$emit("TakeOrderClick");
    },
    ChargebackClick() {
      this.$emit("ChargebackClick");
    },
    FallbackFrontClick() {
      this.$emit("FallbackFrontClick");
    },
    // // 生成叠层
    // GenerateStackClick(){
    //   this.$emit('GenerateStackClick')
    // },
    // 叠层阻抗
    // StackImpedanceClick(){
    //   this.$emit('StackImpedanceClick')
    // },
    // 生成叠层
    GenerateStackClick() {
      // if (this.selectValue =='生成叠层') {
      //   this.$emit('GenerateStackClick')
      // }else {
      this.$emit("StackImpedanceClick");
      //}
    },
    // 开始
    // MakeStartClick(){
    //   this.$emit('MakeStartClick')
    // },
    // 二审分派
    SecondaryAuditAssignClick() {
      this.$emit("SecondaryAuditAssignClick");
    },
    // 二审完成
    SecondaryAuditCompletedClick() {
      this.$emit("SecondaryAuditCompletedClick");
    },
    selectChange() {},
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-btn {
  padding: 0 10px !important;
}
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  //width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 92px;
    margin-top: 1px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
