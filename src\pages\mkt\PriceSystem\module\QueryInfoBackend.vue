<!-- 市场管理 - 价格体系 - 查询 -->
<template>
  <div ref="SelectBox">
    <a-form >
    <a-row>
      <a-col :span='24'>
      <a-form-item label="客户代码" :labelCol="{span: 7}" :wrapperCol="{span:14}" >
        <!-- <a-input   v-model='form.CustNo' placeholder="请输入客户代码" :autoFocus="autoFocus" /> -->
        <a-select v-model="form.CustNo" showSearch allowClear optionFilterProp="lable"    @popupScroll="handlePopupScroll"
          @search="supValue"  :getPopupContainer="()=>this.$refs.SelectBox" :autoFocus="true">
          <a-select-option  v-for="(item,index) in frontDataZSupplier" :key="index" :value="item.lable" :lable="item.lable" >
            {{ item.lable }}
          </a-select-option>
        </a-select>
      </a-form-item>
      </a-col>
    </a-row>
  </a-form>
  </div>
  
</template>

<script>
import {
  mktCustNo
}from "@/services/mkt/PriceSystem";
export default {
    name:'QueryInfoBackend',
  data() {
    return {
      form:{
        CustNo:'',     
      },
      autoFocus:true,
      supList:[],
      frontDataZSupplier:[],
      treePageSize: 20,
      scrollPage: 1,
      valueData:undefined,
    };
  },
 
  methods: { 
    mapKey(data){
      if (!data) {
          return []
      } else {
          return Object.keys(data).map(item => {
          return {'value':item, 'lable': data[item]}
          })
      }
    },
    getData(){
      mktCustNo().then(res=>{
      if(res.code){
        this.supList = this.mapKey(res.data)
        this.frontDataZSupplier =  this.supList.slice(0, 20) 
      }else{
        this.$message.error(res.message)
      }
    })
    },
     //下拉框下滑事件
     handlePopupScroll (e) {
      const { target } = e
      const scrollHeight = target.scrollHeight - target.scrollTop
      const clientHeight = target.clientHeight
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1       
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1
          const scrollPage = this.scrollPage// 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1)// 新增数据量
          const newData = [] // 存储新增数据
          let max = '' // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize
          } else {
            // 否则
            max = this.supList.length
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.lable.indexOf(this.valueData) != -1) { // 当data数组的下标小于max时
                newData.push(item)
              }
            })
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) { // 当data数组的下标小于max时
                newData.push(item)
              }
            })
          }
    
          this.frontDataZSupplier= newData // 将新增的数据赋值到要显示的数组中
        }
      }
    },   
     supValue(value) {
      if (value) {        
        let that = this;
        that.valueData = value      
        let arr = that.supList.filter(m=> m.lable.indexOf(value)!= -1)
        if(arr.length){
          that.frontDataZSupplier = arr.slice(0, 20)
        }else{
          that.frontDataZSupplier = []
        }
       
        console.log('搜索',this.frontDataZSupplier)
      }else{          
          this.valueData = undefined
          this.frontDataZSupplier = this.supList.slice(0, 20)
        
        }
    },
 
  },
  mounted() {   
     this.getData()
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
 computed:{    
  },

};
</script>
<style lang="less" scoped>
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  color:#000000;
}
/deep/.ant-input{
  font-weight: 500;
  color:#000000;
}
.ant-form-item{
  margin-bottom: 0;
}
</style>
