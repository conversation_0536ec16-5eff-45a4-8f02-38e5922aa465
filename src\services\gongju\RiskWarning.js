import { request, METHOD } from "@/utils/request";

//风险警告主列表
export async function riskwarningpagelist(params) {
  return request(`api/app/risk-warning/page-list-v2`, METHOD.GET, params);
}
//风险警告详情
export async function riskwarning(params) {
  return request(`/api/app/risk-warning`, METHOD.GET, params);
}
//风险警告失效
export async function updatestatus(params) {
  return request(`/api/app/risk-warning/update-status`, METHOD.POST, params);
}
