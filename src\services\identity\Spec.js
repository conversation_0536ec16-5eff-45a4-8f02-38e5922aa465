import {request, METHOD} from '@/utils/request'
export async function getList(params) {
    return request("/api/app/spec-update-config/page-list", METHOD.GET, params)
}
export async function createUpdate(params) {   
    return request("/api/app/spec-update-config", METHOD.POST, params)
}
export async function GetEditInfo(id) {   
    return request(`/api/app/spec-update-config?Id=${id}`, METHOD.GET, )
}
export async function updateEditInfo(params) {   
    return request(`/api/app/spec-update-config/update`, METHOD.POST, params)
}
export async function del(id) {
    return request(`/api/app/spec-update-config/${id}/delete-by-id`, METHOD.POST)
}
export async function updateFactoerSpec(id) {
    return request(`/api/app/spec-update-config/${id}/update-factoer-spec`, METHOD.POST)
}
export async function get(id) {
    return request(`/api/identity/roles/${id}`, METHOD.GET)
}
export default {
    getList,
    createUpdate,
    del,
    get
  }
  