<!-- 市场管理 - 订单报价- 订单列表 -->

<template>
  <a-spin :spinning="spinning">
    <div ref="tableWrapper">
      <!-- :row-selection="{ selectedRowKeys: selectedRowKeysArray, onChange: onSelectChange,columnWidth:25}" -->
      <a-table
        :columns="columns"
        :scroll="{ y: 737, x: 600 }"
        :dataSource="dataSource"
        :customRow="customRow"
        :pagination="pagination"
        :rowKey="rowKey"
        :loading="orderListTableLoading"
        @change="handleTableChange"
        :rowClassName="isRedRow"
        class="resizable-table"
      >
        <template slot="customTitle" slot-scope="">
          <vue-draggable-resizable :w="widthchange" :h="36" class-name="dragging2" :draggable="false" @resizing="onResize">
            <div style="line-height: 36px; width: 100%" :width="widthchange">客户型号</div>
          </vue-draggable-resizable>
        </template>
        <template slot="customerModel" slot-scope="text, record">
          <span :title="record.customerModel">{{ record.customerModel }}</span>
          <!-- <a style="color: #428bca"  type="primary" @click="$emit('getDetailInfo',record.id)">{{ record.customerModel }}</a> -->
        </template>
        <template slot="labelUrl" slot-scope="record">
          <span @click.stop="OperationLog(record)" style="color: #428bca"> 日志</span>
        </template>
        <template slot="isQuotationForm" slot-scope="record">
          <span v-if="record.isQuotationForm"> 是</span>
          <span v-else>否</span>
        </template>
        <template slot="contractFilePath" slot-scope="record">
          <span
            v-if="record.contractFilePath"
            @click.stop="ContractDownload(record)"
            style="
              font-size: 14px;
              background: none;
              color: #428bca;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: none;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 128px;
            "
          >
            {{ record.contractFile }}
          </span>
        </template>
        <template slot="mark" slot-scope="record">
          <a-tag
            v-if="record.isLock"
            color="#2D221D"
            style="font-size: 12px; color: #ff9900; padding: 0 2px; margin: 0; margin-right: 3px; height: 21px"
          >
            急
          </a-tag>
        </template>
        <template slot="sales" slot-scope="text, record">
          <a style="color: #428bca" type="primary" @click="goDetail1(record)">销售</a>
          <a-divider type="vertical" />
          <a style="color: #428bca" type="primary" @click="$emit('getDetailInfo', record.id)">预审</a>
        </template>
        <template slot="mailId" slot-scope="text, record">
          <a style="color: #428bca" type="primary" @click="$emit('getmail', record)">{{ record.mailId }}</a>
        </template>
        <template slot="num" slot-scope="text, record, index">
          {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
        </template>
        <template slot="status" slot-scope="text, record">
          <div style="display: flex; align-items: center">
            <span>{{ record.status }}&nbsp; </span>
            <a-tooltip title="该订单已发送合同邮件" v-if="record.isSendMarketContract >= 2">
              <svg
                t="1728634262911"
                class="icon"
                viewBox="0 0 1044 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="11469"
                width="20"
                height="20"
              >
                <path
                  d="M16.384 157.696q-11.264-8.192-14.336-13.312t-3.072-14.336l0-9.216q0-26.624 13.312-41.984t46.08-15.36l843.776 1.024q33.792 0 47.104 14.336t13.312 39.936l1.024 11.264q0 9.216-2.048 12.8t-16.384 14.848l-418.816 251.904q-10.24 4.096-25.088 11.264t-19.968 8.192q-6.144 0-18.432-5.632t-27.648-14.848zM646.144 572.416q-21.504 0-34.816 9.216t-20.992 23.552-10.24 32.256-2.56 34.304l0 71.68 0 12.288q0 6.144 1.024 11.264l-141.312 0q-72.704 0-136.192-0.512t-111.616-0.512l-70.656 0q-36.864 0-60.416-9.728t-36.864-25.088-18.432-35.328-5.12-41.472l0-378.88q0-21.504 10.24-27.136t25.6 5.632q5.12 3.072 18.432 11.776t31.744 19.968 38.4 24.064 37.888 24.064 30.72 19.456 16.896 10.24q14.336 9.216 16.384 23.04t-3.072 24.064q-4.096 10.24-12.288 26.624t-17.408 33.28-17.92 32.256-11.776 23.552q-5.12 14.336 2.048 19.456t22.528-4.096q3.072-2.048 16.896-15.872t29.184-30.72 29.184-31.744 18.944-17.92q9.216-8.192 24.064-11.776t26.112 2.56q7.168 4.096 19.456 12.288t27.136 17.92 30.208 19.968l27.648 18.432q12.288 8.192 26.112 10.24t26.624 1.024 23.04-4.608 15.36-6.656 19.456-11.776 31.232-18.944 31.744-19.456 22.016-13.312l129.024-79.872q2.048-1.024 12.8-8.192t26.624-17.408 34.816-22.528 35.84-23.04 31.232-19.968 20.48-13.312q19.456-12.288 30.208-9.728t10.752 16.896l0 266.24q-28.672-23.552-55.808-44.032t-49.664-34.816q-22.528-15.36-39.424-10.752t-27.648 19.968-16.384 35.84-5.632 36.864q0 11.264-0.512 18.432t-0.512 12.288q-1.024 5.12-1.024 8.192l-15.36 0-104.448 0zM1028.096 679.936q13.312 10.24 13.824 28.672t-10.752 26.624q-15.36 12.288-35.84 29.184t-42.496 34.304-43.008 34.816-38.4 30.72q-21.504 17.408-30.208 18.432t-8.704-26.624l0-46.08q0-17.408-8.704-28.672t-23.04-11.264l-118.784 0q-14.336 0-28.16-10.24t-13.824-26.624l0-52.224q0-28.672 9.216-34.816t32.768-6.144l20.48 0q9.216 0 20.992 0.512t28.16 0.512l43.008 0q20.48 0 29.184-7.168t8.704-25.6l0-45.056q0-18.432 6.144-23.552t22.528 8.192q16.384 12.288 37.888 29.696t44.544 35.328 45.056 35.84 39.424 31.232z"
                  p-id="11470"
                  fill="#ff9900"
                ></path>
              </svg>
              <span style="font-size: 10px; color: red; position: relative; top: -10px; left: -2px" v-if="record.isSendMarketContract > 2">{{
                record.isSendMarketContract - 2
              }}</span>
            </a-tooltip>
          </div>
        </template>
        <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
          <a style="color: #428bca" :title="record.orderNo" @click.stop="goDetail(record)">{{ record.orderNo }}</a
          >&nbsp;
          <span class="tagNum" style="display: inline-block; height: 19px">
            <span v-if="record.isExtremeJiaji">
              <span
                style="
                  font-size: 14px;
                  font-weight: 500;
                  color: #ff9900;
                  padding: 0 2px;
                  margin: 0;
                  display: inline-block;
                  height: 19px;
                  width: 14px;
                  margin-right: 4px;
                  margin-left: -10px;
                  user-select: none;
                "
                >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
              </span>
              <span
                style="
                  font-size: 14px;
                  font-weight: 500;
                  color: #ff9900;
                  padding: 0 2px;
                  margin: 0;
                  display: inline-block;
                  height: 19px;
                  width: 14px;
                  margin-right: 4px;
                  margin-left: -10px;
                  user-select: none;
                "
                >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
              </span>
            </span>
            <span
              v-else-if="record.isJiaji"
              style="
                font-size: 14px;
                font-weight: 500;
                color: #ff9900;
                padding: 0 2px;
                margin: 0;
                display: inline-block;
                height: 19px;
                width: 14px;
                margin-right: 4px;
                margin-left: -10px;
                user-select: none;
              "
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
            <a-tooltip v-if="record.identificationType == 1" title="按新单制作">
              <span
                style="
                  background: #ff9900;
                  border-radius: 50%;
                  color: white;
                  padding: 0 1px;
                  text-align: center;
                  height: 22px;
                  width: 22px;
                  line-height: 20px;
                  display: inline-block;
                  font-size: 12px;
                "
              >
                新 </span
              >&nbsp;
            </a-tooltip>
            <a-tooltip v-if="record.identificationType == 2" title="按返单有改制作">
              <span
                style="
                  background: #ff9900;
                  border-radius: 50%;
                  color: white;
                  padding: 0 1px;
                  text-align: center;
                  height: 22px;
                  width: 22px;
                  line-height: 20px;
                  display: inline-block;
                  font-size: 12px;
                "
              >
                改 </span
              >&nbsp;
            </a-tooltip>
            <a-tooltip v-if="record.mailId" title="邮件信息查看">
              <a-tag
                @click="$emit('getmail', record)"
                style="
                  font-size: 10px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                "
              >
                邮
              </a-tag>
            </a-tooltip>
            <a-tooltip v-if="record.onLineEcnState > 0" :title="record.onLineOrRecordEcn == 2 ? '更改存档升级' : 'ECN在线改版'">
              <a-tag
                style="
                  font-size: 10px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                  cursor: pointer;
                "
              >
                升
              </a-tag>
            </a-tooltip>
            <a-tag
              v-if="record.ka"
              style="
                font-size: 10px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              KA
            </a-tag>
            <a-tooltip :title="record.fileUploadedTime ? '重传文件时间:' + record.fileUploadedTime : '重传文件'" v-if="record.fileUploadedCount > 0">
              <span>
                <a-tag
                  color="#2D221D"
                  style="
                    font-size: 12px;
                    background: red;
                    color: white;
                    padding: 0 2px;
                    margin-left: 3px;
                    margin-right: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid red;
                  "
                >
                  重
                </a-tag>
                <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -2px">{{ record.fileUploadedCount }}</span>
              </span>
            </a-tooltip>
            <a-tag
              v-if="record.isLock"
              @click.stop="islock(record)"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              锁
            </a-tag>
            <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.reOrder == 1">
              <a-tag
                style="
                  font-size: 12px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                "
              >
                改
              </a-tag>
            </a-tooltip>
            <a-tag
              v-if="record.isOrderModify && record.orderModify == 1"
              @click.stop="xiudisplay(record)"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              修
            </a-tag>
            <a-tag
              v-if="record.isOrderModify && record.orderModify == 2"
              @click.stop="xiudisplay(record)"
              style="
                font-size: 12px;
                background: #ff9900;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #ff9900;
              "
            >
              修
            </a-tag>
            <a-tag
              v-if="record.isNeedOrderOff"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              下
            </a-tag>
            <a-tag
              v-if="record.isOrderModify && record.orderModify == 3"
              @click.stop="xiudisplay(record)"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              退
            </a-tag>
            <!-- <span v-if="record.reminderTime && compareTimes(record.reminderTime)">
      <a-tag   @click.stop=FollowUpContent(record)
      style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca" >
          跟
      </a-tag>
      <span style="font-size:8px;color: red;position: relative;top: -10px; left: -4px;" v-if="record.followTimes>0">{{record.followTimes }}</span>
    </span> -->
            <a-tooltip title="风险警告" v-if="record.riskWarning">
              <a-icon type="warning" style="color: #ff9900" class="noCopy" />
            </a-tooltip>
          </span>
        </div>
      </a-table>
      <right-copy ref="RightCopy" />
      <a-modal
        title="修改内容"
        :width="1300"
        :visible="xiuvisible"
        destroyOnClose
        centered
        :mask="false"
        :maskClosable="false"
        @cancel="handleCancel"
      >
        <div>
          <a-table
            class="xiu"
            :columns="columns4"
            :dataSource="dataSource4"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :scroll="{ y: 400 }"
            :class="dataSource4.length ? 'min-table' : ''"
            :pagination="false"
            :loading="loading2"
          >
            <template slot="isPrintContract" slot-scope="record">
              <a-checkbox :checked="record.isPrintContract"></a-checkbox>
            </template>
            <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
              <template v-for="photo in record.filePath.split(',')">
                <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer :key="index + '-' + photo" />
              </template>
            </div>
          </a-table>
        </div>
        <template slot="footer">
          <a-button @click="handleCancel">取消</a-button>
        </template>
      </a-modal>
      <a-modal
        :title="'【' + custNo + '】-- 特殊要求'"
        :width="1000"
        :visible="visibleCustNo"
        :confirmLoading="confirmLoading"
        :mask="false"
        :maskClosable="false"
        @cancel="handleCancel"
      >
        <standard-table
          rowKey="id"
          :columns="factoryColumns"
          :dataSource="factoryData"
          :pagination="false"
          :class="factoryData.length ? 'min-table' : ''"
          :loading="loading1"
        >
          <span slot="companyArea_" slot-scope="{ text }">
            <template>
              {{ text }}
            </template>
          </span>
          <span slot="image_" slot-scope="{ record }">
            <template>
              <span v-if="record.image_" v-viewer>
                <img v-for="(tmp, index) in record.image_.split(',')" :key="index" :src="tmp" style="width: 26px; height: 26px; margin-right: 5px" />
              </span>
            </template>
          </span>
        </standard-table>
        <template slot="footer">
          <a-button @click="handleCancel">取消</a-button>
        </template>
      </a-modal>
      <a-modal
        title="跟进内容"
        :width="600"
        :visible="Contentvisible"
        :confirmLoading="confirmLoading"
        @ok="contenOk"
        :mask="false"
        :maskClosable="false"
        @cancel="handleCancel"
        centered
      >
        <a-form-model-item label="跟进内容" :labelCol="{ span: 5 }" :wrapperCol="{ span: 15, offset: 1 }">
          <a-textarea allowClear :auto-size="{ minRows: 1, maxRows: 15 }" autoFocus v-model="Content"> </a-textarea>
        </a-form-model-item>
      </a-modal>
      <a-modal title="查看图片" :width="640" :visible="visible" @ok="handleOkImg" @cancel="handleCancel" :mask="false" :maskClosable="false">
        <a-spin :spinning="confirmLoading">
          <a-row>
            <a-col :span="8" v-for="(tmp, index) in imgList" :key="index">
              <a :href="tmp" target="_blank">
                <img :src="tmp" alt="图片" class="imgStyle" />
              </a>
              <!-- <div v-viewer>
                        <img :src="tmp" alt="图片" class='imgStyle'>
                    </div> -->
            </a-col>
          </a-row>
        </a-spin>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
import VueDraggableResizable from "vue-draggable-resizable";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";
import { checkPermission } from "@/utils/abp";
import StandardTable from "@/components/table/StandardTable";
import { CustRequire, ordermodifylist, followupcontent } from "@/services/mkt/CustInfoNew";
const factoryColumns = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "6%",
    align: "center",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "要求类别",
    dataIndex: "category_",
    width: "10%",
    scopedSlots: { customRender: "category_" },
  },
  {
    title: "要求简述",
    dataIndex: "askToTell_",
    width: "20%",
    scopedSlots: { customRender: "askToTell_" },
  },
  {
    title: "要求详情",
    dataIndex: "askDetails_",
    scopedSlots: { customRender: "askDetails_" },
  },
  {
    title: "图片",
    dataIndex: "image_",
    width: "15%",
    scopedSlots: { customRender: "image_" },
    className: "lastTd",
  },
  // {
  //     title: '操作',
  //     key: 'action',
  //     width:'10%',
  //     scopedSlots: { customRender: 'action' },
  // },
];
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "30%",
    align: "left",
  },
  // {
  //     title: "附件地址",
  //     dataIndex: "filePath",
  //     ellipsis: true,
  //     align: "left",
  // },
  {
    title: "图片",
    //dataIndex: "filePath",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    // dataIndex: "isPrintContract",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            current: 1,
            pageSize: 25,
            showTotal: total => `总计 ${total} 条`,
            total: 0,
          },
          selectedRows: {},
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
    params1: {
      type: Object,
    },
  },
  name: "LeftTableMake",
  components: { StandardTable, VueDraggableResizable, RightCopy },
  data() {
    return {
      spinning: false,
      Routingjump: false,
      loading2: false,
      guid: "",
      showText: false,
      aaa: {},
      Contentvisible: false,
      Content: "",
      text: "",
      fanhui: "",
      xiuvisible: false,
      selectedRowKeysArray: [],
      selectedRowsData: {},
      activeClass: "smallActive",
      proOrderId: "",
      visibleCustNo: false,
      factoryColumns,
      columns4,
      dataSource4: [],
      factoryData: [],
      imgList: [],
      visible: false,
      confirmLoading: false,
      custNo: "",
      loading1: false,
      isDragging: false,
      startIndex: -1,
      shiftKey: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      menuData: {},
      widthchange: 325,
    };
  },
  watch: {
    pagination: {
      handler(val) {},
    },
    dataSource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let maxLength = 0;
            let longestChars = [];
            var isSendMarketContract = false;
            for (let i = 0; i < val.length; i++) {
              if (val[i].isSendMarketContract == 2) {
                isSendMarketContract = true;
              }
              let obj2 = val[i].proOrderNo;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (isSendMarketContract) {
              this.columns[9].width = "80px";
            } else {
              this.columns[9].width = "55px";
            }
            if (longestChars.length > 12 && result == 0) {
              this.columns[1].width = "130px";
              this.columns[5].width = 125 + (longestChars.length - 12) * 7 + "px";
              this.columns[3].width = 335 - (longestChars.length - 12) * 7 + "px";
              this.widthchange = 335 - (longestChars.length - 12) * 7;
            }
            if (longestChars.length > 12 && result >= 1) {
              this.columns[1].width = 130 + result * 20 + "px";
              this.columns[5].width = 125 + (longestChars.length - 12) * 7 + "px";
              this.columns[3].width = 335 - result * 20 - (longestChars.length - 12) * 7 + "px";
              this.widthchange = 335 - result * 20 - (longestChars.length - 12) * 7;
            }
            if (result == 0 && longestChars.length <= 12) {
              this.columns[1].width = "130px";
              this.columns[5].width = "125px";
              this.columns[3].width = "335px";
              this.widthchange = 335;
            }
            if (result >= 1 && longestChars.length <= 12) {
              this.columns[1].width = 130 + result * 20 + "px";
              this.columns[5].width = "125px";
              this.columns[3].width = 335 - result * 20 + "px";
              this.widthchange = 335 - result * 20;
            }
          });
        }
      },
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
  created() {},
  methods: {
    checkPermission,
    compareTimes(preSendTime) {
      const currentTime = new Date();
      const preSendTimeDate = new Date(preSendTime);
      return currentTime >= preSendTimeDate;
    },
    ContractDownload(record) {
      if (record.contractFilePath) {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", record.contractFilePath, true);
        xhr.responseType = "blob";

        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);

            // 根据Content - Type设置下载文件名
            let fileName = record.contractFile;
            const contentType = xhr.getResponseHeader("Content-Type");
            if (contentType.includes("pdf")) {
              fileName += ".pdf";
            } else if (contentType.includes("zip")) {
              fileName += ".zip";
            } else if (contentType.includes("jpg") || contentType.includes("jpeg")) {
              fileName += ".jpg";
            } else if (contentType.includes("png")) {
              fileName += ".png";
            } else if (contentType.includes("gif")) {
              fileName += ".gif";
            } else {
              const lastDotIndex = record.contractFilePath.lastIndexOf(".");
              const fileExtension = lastDotIndex !== -1 ? record.contractFilePath.substring(lastDotIndex) : "";
              fileName += fileExtension;
            }
            link.download = fileName;

            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 释放ObjectURL
            URL.revokeObjectURL(link.href);
          } else {
            console.error("文件下载失败，状态码:", xhr.status);
          }
        };

        xhr.onerror = function () {
          console.error("文件下载网络错误");
        };

        xhr.send();
      }
    },
    OperationLog(record) {
      this.$emit("OperationLog", record);
    },
    islock(record) {
      this.$emit("islock", record);
    },
    xiudisplay(record) {
      this.loading2 = true;
      ordermodifylist(record.id)
        .then(res => {
          if (res.code) {
            this.dataSource4 = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
      this.xiuvisible = true;
    },
    FollowUpContent(record) {
      this.guid = record.id;
      this.Contentvisible = true;
      this.Content = "";
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysArray = selectedRowKeys;
      this.proOrderId = this.selectedRowKeysArray[this.selectedRowKeysArray.length - 1];
      this.selectedRowsData = this.dataSource.filter(item => {
        return item.id == this.proOrderId;
      })[0];
      this.$emit("getOrderDetail", this.proOrderId);
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.orderExceptionState) {
        strGroup.push("rowRed");
      }
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    contenOk() {
      this.spinning = true;
      let params = {
        id: this.guid,
        content: this.Content,
      };
      this.Contentvisible = false;
      followupcontent(params)
        .then(res => {
          if (res.code) {
            this.$message.success("跟进成功");
            this.$emit("getOrderList");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleTableChange(pagination, filter, sorter) {
      this.pageStat = false;
      this.$emit("tableChange", pagination, filter, sorter);
    },
    previewClick() {
      this.$emit("previewClick", record);
    },
    goDetail(record, type) {
      this.Routingjump = true;
      //console.log(this.pagination.current,this.pagination.pageSize,record.id,record,this.params1,'624')
      this.$router.push({
        path: "orderDetail",
        query: {
          id: record.id,
          orderNo: record.orderNo,
          factory: record.joinFactoryId,
          boid: record.jobId,
          ttype: "1",
          orderModify: record.orderModify,
          reOrder: record.reOrder,
          preMode: record.preMode,
          isCustRule: record.isCustRule,
          custNo: record.custNo,
          bianji: type == "bianji" ? true : false,
          tradeType: record.tradeType,
        },
      });
      localStorage.setItem("pageCurrent4", this.pagination.current);
      localStorage.setItem("pageSize4", this.pagination.pageSize);
      localStorage.setItem("id4", record.id);
      localStorage.setItem("record4", JSON.stringify(record));
      localStorage.setItem("stat4", true);
      localStorage.setItem("bjqueryInfo", JSON.stringify(this.params1));
      //console.log(this.pagination.current,this.pagination.pageSize,record.id,record,this.params1,'633')
    },
    goDetail1(record) {
      if (this.selectedRowKeysArray.indexOf(record.id) != -1) {
        this.selectedRowKeysArray.push(record.id);
      }
      this.$emit("goDetail1", record);
    },
    handleRowSelectionChange(selectedRowKeys) {
      // 处理行选择变化事件
      const selectedRows = this.getSelectedRows(selectedRowKeys);
      if (selectedRows.length === 1) {
        this.lastSelected = selectedRows[0].id;
      } else {
        this.lastSelected = null;
      }
    },
    goCustNo(record) {
      this.custNo = record.custNo;
      this.visibleCustNo = true;
      this.getAskList(record.id);
    },
    handleCancel() {
      this.visibleCustNo = false;
      this.Contentvisible = false;
      this.xiuvisible = false;
      this.visible = false;
      this.factoryData = [];
      this.dataSource4 = [];
    },
    getAskList(id) {
      this.loading1 = true;
      CustRequire(id)
        .then(res => {
          if (res.success) {
            this.factoryData = res.data;
          } else {
            this.$message.info(res.message);
          }
        })
        .finally(() => {
          this.loading1 = false;
        });
    },
    imgCheck(record) {
      this.imgList = record.image_.split(",");
      this.visible = true;
    },
    handleOkImg() {
      this.visible = false;
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeysArray;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowKeysArray = rowKeys;
        if (this.selectedRowKeysArray.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowKeysArray = [record.id];
        }
      }
      this.proOrderId = this.selectedRowsData.id;
      this.$emit("Changecolumns", this.selectedRowsData.joinFactoryId);
      setTimeout(() => {
        if (!this.Routingjump) {
          this.$emit("getOrderDetail", this.proOrderId);
        }
      }, 500);
      this.shiftKey = false;
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      this.selectedRowKeysArray = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[0];
        })[0];
      }
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    cancel() {
      this.bodyClick();
    },
    onResize: function (x, y, width, height) {
      this.columns[3].width = width;
      this.widthchange = width;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-divider-vertical {
  margin: 0 4px;
  background: #999999;
}
.xiu {
  /deep/.ant-table-row-cell-ellipsis,
  .ant-table-row-cell-ellipsis .ant-table-column-title {
    overflow: overlay;
    white-space: normal;
    text-overflow: inherit;
  }
}
.resizable-table .vue-draggable-resizable {
  cursor: col-resize;
}
/deep/.dragging2 {
  display: block !important;
  top: 0 !important;
  .handle {
    width: 1px;
    height: 1px;
    border: 1px solid #efefef;
    z-index: 9999;
    display: block !important;
  }
  .handle-tl {
    display: none !important;
  }
  .handle-tm {
    display: none !important;
  }
  .handle-tr {
    display: none !important;
  }
  .handle-mr {
    top: 0;
    right: 3px;
    margin-top: 0;
    height: 100% !important;
  }
  .handle-br {
    display: none !important;
  }
  .handle-bm {
    display: none !important;
  }
  .handle-bl {
    display: none !important;
  }
  .handle-ml {
    display: none !important;
  }
}
/deep/.ant-modal-body {
  margin-top: 30px;
}
/deep/.ant-table-row-cell-break-word {
  font-weight: 500;
}
/deep/.ant-table-header-column {
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
.ant-table .ant-table-row-selected {
  background-color: #dcdcdc;
}
/deep/ .ant-table {
  .fontRed {
    td {
      color: red;
    }
  }
  .rowRed {
    background: red !important;
  }
  .rowBackgroundColor {
    background: #dcdcdc !important;
  }
  .displayFlag {
    display: none;
  }
  /deep/.ant-modal-body {
    padding: 0 4px;
  }
}
/deep/.ant-modal-body {
  padding: 0;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table {
  .ant-table-thead > tr > th {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr {
    .lastTd {
      padding: 0 4px !important;
    }
  }
}
</style>
