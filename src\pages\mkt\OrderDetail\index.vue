<!-- 市场管理 - 订单预审 -订单详情 -->
<template>
  <a-spin :spinning="spinning">
    <div class="orderDetail">
      <order-action
        :editFlag="editFlag"
        :joinFacId="joinFacId"
        :orderModify="orderModify"
        :ttype="ttype"
        :preMode="preMode"
        :showData="showData"
        :isCustRule="isCustRule"
        :activeKey="activeKey"
        @editInfo="editInfo"
        @dataSave="dataSave"
        @previewClick="previewClick"
        @laminationClick="laminationClick"
        @CuttingClick="CuttingClick"
        @CustomerRulesClick="CustomerRulesClick"
        @modifyInfoClick="modifyInfoClick"
        @backGroudKLClick="backGroudKLClick"
        @ordermodifyInfo="ordermodifyInfo"
        @diffPreview="diffPreview"
      ></order-action>
      <div style="position: relative">
        <!-- <span style="display: inline-block; position: absolute; left: 8%;font-size: 20px;color: red;font-weight: 600;">{{orderno}}</span>  -->
        <a-tabs :activeKey="activeKey" @tabClick="tabClick">
          <a-tab-pane key="1" :tab="'【' + orderno + '】--PCB订单详情'">
            <one-mkt
              ref="editForm"
              :spinning="spinning"
              :editFlag="editFlag"
              :showData="showData"
              :selectOption="selectOption"
              :boardBrandList="boardBrandList"
              :sheetTraderList="sheetTraderList"
              :messageList="messageList"
              :boardtgList="boardtgList"
              :supList="supList"
              :joinFacId="joinFacId"
              :ManufacturerTG="ManufacturerTG"
              :reOrder="reOrder"
              :required="required"
              :frontDataZSupplierf="frontDataZSupplierf"
              :requiredLinkConfigList="requiredLinkConfigList"
              :copperdata="copperdata"
            ></one-mkt>
          </a-tab-pane>
          <a-tab-pane key="2" tab="销售信息">
            <sales-info
              v-if="activeKey == '2'"
              ref="editForm1"
              :editFlag="editFlag"
              :showData="showData"
              :selectOption="selectOption"
              :saveID="saveID"
            ></sales-info>
          </a-tab-pane>
          <a-tab-pane key="3" tab=" SPEC参数 ">
            <spec-info v-if="activeKey == '3'"></spec-info>
          </a-tab-pane>
          <a-tab-pane key="4" tab="钻带页">
            <drill-info
              v-if="activeKey == '4'"
              :drilldata="drilldata"
              :drillDiameters="drillDiameters"
              :lineInfoData="lineInfoData"
              :editFlag="editFlag"
              :selectOption="selectOption"
            ></drill-info>
          </a-tab-pane>
          <a-tab-pane key="5" tab="文件预览" v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreFilePreview')">
            <gerber-viewer v-if="activeKey == '5'"></gerber-viewer>
          </a-tab-pane>
          <a-tab-pane key="6" tab="阻抗信息">
            <lamination-info
              v-if="activeKey == '6'"
              :proOrderStackUpImpDto="proOrderStackUpImpDto"
              :editFlg1="editFlag"
              ref="dom4"
              :proOrderInfoDto="proOrderInfoDto"
              :ttype="'mkt'"
              @GetProOrderInfo="GetProOrderInfo"
            ></lamination-info>
          </a-tab-pane>
          <a-tab-pane key="7" tab="一站式" v-if="showData.isPcba">
            <one-stop v-if="activeKey == '7'" ref="dom7" :showData="showData" :editFlag="editFlag"></one-stop>
          </a-tab-pane>
          <!-- <a-tab-pane key="3" tab="工程问客" disabled>
        Content of Tab Pane 3
      </a-tab-pane> -->
        </a-tabs>
        <template>
          <div class="spec" v-if="activeKey == '3' || activeKey == '5'">
            <iframe :src="src" width="100%" height="723px" frameborder="0"></iframe>
          </div>
        </template>
        <vue-draggable-resizable
          :w="width"
          :h="height"
          :x="1000"
          :y="70"
          :min-width="605"
          :min-height="205"
          :max-width="964"
          :max-height="570"
          :parent="true"
          class-name="dragging1"
          @dragging="onDrag"
          @resizing="onResize"
        >
          <div id="modal-container"></div>
        </vue-draggable-resizable>
      </div>
      <!-- 弹窗制作1 -->
      <a-modal
        title="确认弹窗"
        :visible="dataVisible7"
        @cancel="reportHandleCancel"
        @ok="handleOk7"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div style="overflow-y: auto">
          <p>{{ message1 }}</p>
          <p style="color: red; font-size: 12px">点击取消重新编辑尺寸,点击确认保存当前尺寸</p>
        </div>
      </a-modal>
      <a-modal
        title="市场备注"
        :visible="mktnotevisible"
        :closable="false"
        destroyOnClose
        :maskClosable="false"
        :force-render="true"
        :mask="false"
        :getContainer="getModalContainer"
        ref="preform"
        :width="600"
        centered
        class="mkt"
      >
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel2">关闭</a-button>
        </template>
        <div style="height: 110px; overflow-y: auto">{{ showData.mktNote }}</div>
      </a-modal>
      <a-modal title=" 订单详情确认" :visible="dataVisible" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">取消</a-button>
        </template>
        <div style="height: 200px; overflow-y: auto">
          <div v-for="(ite, index) in message" :key="index">
            <p>{{ ite }}</p>
          </div>
        </div>
      </a-modal>
      <a-modal title="检查信息" :visible="dataVisible22" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="600" centered>
        <template #footer>
          <a-button key="back" type="primary" @click="reportHandleCancel">取消</a-button>
          <a-button key="back1" type="primary" v-if="check" @click="continueClick">继续</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
        </div>
      </a-modal>
      <!-- 客户规则弹窗 -->
      <a-drawer
        :title="this.$route.query.custNo + ' -- 客户规则 '"
        :visible="dataopen"
        @close="dataCancel"
        :maskClosable="false"
        :mask="false"
        placement="right"
        style="margin-top: 188px; height: 77%"
        :class="dataopen ? 'drawerclass' : ''"
      >
        <div style="height: 632px; overflow-y: auto">
          <div v-for="(ite, index) in ruleDescribe_" :key="index" style="display: flex">
            <p style="color: red; white-space: pre">{{ convertToLetter(index + 1) }}、</p>
            <p style="white-space: pre-wrap">{{ ite }}</p>
          </div>
        </div>
      </a-drawer>
      <a-modal
        title="确认弹窗"
        :visible="confirmVisible"
        @cancel="reportHandleCancel"
        @ok="confirmOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div>{{ confirmmessage }}</div>
      </a-modal>
      <!--差异预览弹窗-->
      <a-modal
        title="差异预览"
        :width="1000"
        :visible="diffVisible"
        destroyOnClose
        centered
        :mask="false"
        :maskClosable="false"
        @cancel="diffVisible = false"
      >
        <div>
          <a-table
            class="diff"
            :columns="diffcolumns"
            :dataSource="diffData"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :scroll="{ y: 400 }"
            :class="diffData.length ? 'min-table' : ''"
            :pagination="false"
            :loading="diffLoad"
          >
            <template slot="num" slot-scope="text, record, index">
              <span :style="{ color: !record.isSame ? 'red' : '' }">{{ index + 1 }}</span>
            </template>
          </a-table>
        </div>
        <template slot="footer">
          <a-button type="primary" @click="showList('all')" v-if="diffType == 'difference'">展示全部</a-button>
          <a-button type="primary" @click="showList('difference')" v-if="diffType == 'all'">展示差异</a-button>
          <a-button @click="diffVisible = false">关闭</a-button>
        </template>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import db from "@/utils/db";
const LaminationInfo = () => import("@/pages/gongcheng/projectIndicate/module/LaminationInfo");
import VueDraggableResizable from "vue-draggable-resizable";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";
// const CustomerRulesInfo = () => import('@/pages/mkt/Orderverify/subassembly/CustomerRulesInfo.vue')
const OrderAction = () => import("@/pages/mkt/OrderDetail/subassembly/OrderAction");
//const OneMkt = () => import('@/pages/mkt/OrderDetail/subassembly/OneMkt')
const SpecInfo = () => import("@/pages/mkt/OrderDetail/subassembly/SpecInfo");
const SalesInfo = () => import("@/pages/mkt/OrderDetail/subassembly/SalesInfo");
const DrillInfo = () => import("@/pages/mkt/OrderDetail/subassembly/DrillInfo");
const OneStop = () => import("@/pages/mkt/OrderDetail/subassembly/OneStop");
const GerberViewer = () => import("@/pages/mkt/OrderDetail/subassembly/GerberViewer");
import OneMkt from "@/pages/mkt/OrderDetail/subassembly/OneMkt";
import { graphicPreview, getModifyInformation, prebackgroudkL } from "@/services/mkt/PrequalificationProduction.js";
import { coreList, ozList, gbList } from "@/services/impedance";
import { coreTypes, ppTypes } from "@/services/gongju/stackUp";
import { mapState, mapMutations } from "vuex";
import {
  checkbilldif,
  getEditOrderInfo,
  updateOrderInfo,
  mktConfig,
  autoToolPnl,
  pcbOrderImp,
  sheetTraderItems,
  boardBrandItems,
  specFileInfo,
  stackupimpinfo,
  selectpars,
  boardtgitems,
  changeCheck,
  drillInfo,
  lineInfo,
  requiredLinkConfig,
  SaleOrderInfo,
  setpcborderpartableinfo,
  coretypeverdors,
  orderpreimp,
  copperthickconversion,
} from "@/services/mkt/orderInfo";
import { getCustomerInfo } from "@/services/mkt/PrequalificationProduction.js";
import { mktCustNo, mktcustnobyfAC } from "@/services/mkt/Inquiry.js";
import { indicationCheck, verifyFinishedOrder, settoolcheck } from "@/services/mkt/OrderReview.js";
import Cookie from "js-cookie";
import { checkPermission } from "@/utils/abp";
import moment from "moment";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "操作时间",
    dataIndex: "createTime",
    align: "center",
    width: 100,
  },
  {
    title: "操作详情",
    dataIndex: "content",
    align: "left",
    width: 700,
  },
];
const diffcolumns = [
  {
    title: "序号",
    width: 45,
    align: "center",
    scopedSlots: { customRender: "num" },
  },
  {
    title: "描述",
    dataIndex: "description",
    ellipsis: true,
    width: 120,
    align: "left",
  },
  {
    title: "值1",
    dataIndex: "value1",
    ellipsis: true,
    width: 200,
    align: "left",
  },
  {
    title: "值2",
    dataIndex: "value2",
    ellipsis: true,
    width: 200,
    align: "left",
  },
];
export default {
  name: "OrderDetail",
  //OrderInfo LaminationInfo VueDraggableResizable
  components: { OrderAction, SpecInfo, DrillInfo, SalesInfo, GerberViewer, OneMkt, LaminationInfo, OneStop, VueDraggableResizable },
  data() {
    return {
      diffVisible: false,
      diffData: [],
      alldiffData: [],
      diffType: "difference",
      diffLoad: false,
      diffcolumns,
      required: false,
      mktnotevisible: false,
      confirmVisible: false,
      confirmmessage: "",
      checktype: "",
      copperdata: [],
      proOrderStackUpImpDto: {},
      proOrderInfoDto: {},
      reOrder: "",
      ruleDescribe_: [],
      editFlag: false,
      showData: {},
      spinning: false,
      columns1,
      viewLogData: [],
      add: false,
      selectOption: {},
      id: "",
      orderno: "",
      boardBrandList: [],
      boardtgList: [],
      ManufacturerTG: [],
      sheetTraderList: [],
      dataVisible: false,
      dataVisible7: false,
      message1: "",
      message: [],
      type: "",
      deletId: "",
      messageList: [],
      src: "",
      specFileData: {},
      ttype: "",
      preMode: "",
      orderModify: "",
      drilldata: [],
      checkData: [],
      check: false,
      dataVisible22: false,
      check1: false,
      drillDiameters: [],
      supList: [],
      frontDataZSupplierf: [],
      lineInfoData: [],
      CustomerData: [], // 客户规则
      dataopen: false,
      isMovedown: false,
      width: 605,
      height: 250,
      x: 0,
      y: 0,
      requiredLinkConfigList: {},
      isCustRule: false,
      saveID: "",
      activeKey: "1",
      editKey: "",
      joinFacId: "",
    };
  },
  created() {
    this.orderModify = this.$route.query.orderModify;
    this.joinFacId = this.$route.query.factory;
    this.reOrder = this.$route.query.reOrder;
    this.ttype = this.$route.query.ttype;
    this.preMode = this.$route.query.preMode;
    this.isCustRule = this.$route.query.isCustRule;
    if (this.$route.query.id) {
      this.id = this.$route.query.id;
    }
    if (this.$route.query.orderNo) {
      this.orderno = this.$route.query.orderNo;
    }
    this.getData(this.$route.query.factory);
  },
  updated() {
    if (this.activeKey == 1) {
      if (this.$refs.editForm) {
        this.$refs.editForm.setStyle();
      }
    }
  },
  async mounted() {
    // this.getSpecInfo()
    // let baseURL= process.env.VUE_APP_API_BASE_URL
    let baseURL = process.env.VUE_APP_API_JSON_URL;
    // this.src = baseURL + '/AuditParametersMkt'
    if (this.$route.query.boid) {
      // this.src = baseURL + '/AuditParametersMkt?boid='+this.$route.query.boid+'&id='+this.$route.query.id
      this.src = "http://localhost:4200/AuditParametersMkt?boid=" + this.$route.query.boid + "&id=" + this.$route.query.id;
    } else {
      this.src = baseURL + "/AuditParametersMkt";
      //this.src = 'http://localhost:4200/AuditParametersMkt'
    }
    this.saveID = this.$route.query.id;
    this.setedit(this.editFlag);
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    await this.getDetailInfo();
    if (this.$route.query.bianji == true) {
      this.editInfo();
      if (this.showData.mktNote && this.$route.query.factory == 12) {
        document.getElementsByClassName("dragging1")[0].style.display = "block";
        this.mktnotevisible = true;
      }
    }
    this.getCore();
    this.getData1();
    this.getTG();
    this.ManufacturerandTG();
    this.getsheet();
    this.getboardBrand();
    this.getSupplier();
    this.getRequiredLink();
    this.getcopper();
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.editstatus) {
      if (confirm("当前为编辑状态数据未保存,请确认是否需要进行跳转页面?")) {
        next();
        this.setedit(false);
      } else {
        next(false);
      }
    } else {
      next();
      this.setedit(false);
    }
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  // 获取当前登陆账号信息
  computed: {
    ...mapState("account", ["user"]),
    ...mapState("setting", ["editstatus"]),
  },
  methods: {
    checkPermission,
    ...mapMutations("setting", ["setedit"]),
    // 获取参数
    GetProOrderInfo() {
      let parmas = this.$route.query.id;
      if (parmas) {
        stackupimpinfo(parmas).then(res => {
          if (res.code) {
            this.proOrderStackUpImpDto = res.data;
            if (this.proOrderStackUpImpDto.stackIMPOutputs == null) {
              this.proOrderStackUpImpDto.stackIMPOutputs = [];
            }
            if (this.proOrderStackUpImpDto.stackUpOutputs == null) {
              this.proOrderStackUpImpDto.stackUpOutputs = [];
            }
            this.$nextTick(() => {
              if (this.$refs.dom4) {
                this.$refs.dom4.handleResize();
              }
            });
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.key == "e" && this.isCtrlPressed) {
        this.editInfo();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.key == "s" && this.isCtrlPressed) {
        this.dataSave();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    tabClick(key) {
      if (this.editFlag && this.editKey != key) {
        this.$message.warning("编辑状态不可执行此操作！");
        return;
      }
      this.activeKey = key;
      let baseURL = process.env.VUE_APP_API_JSON_URL;
      if (this.$route.query.boid) {
        this.src = baseURL + "/AuditParametersMkt?boid=" + this.$route.query.boid + "&id=" + this.$route.query.id;
        //this.src = 'http://localhost:4200/AuditParametersMkt?boid='+this.$route.query.boid+'&id='+this.$route.query.id
      } else {
        this.src = baseURL + "/AuditParametersMkt";
        //this.src = 'http://localhost:4200/AuditParametersMkt'
      }
      if (key == 3) {
        this.src = this.src + "&configtype=1"; // spec
      }
      if (key == 5) {
        this.src = this.src + "&configtype=0"; // gerber
      }
      if (key == 1 && localStorage.getItem("savesuccess") == "true") {
        this.getDetailInfo();
        localStorage.removeItem("savesuccess");
      }
      if (key == 4) {
        this.getData11();
      }
      if (key == 6) {
        this.GetProOrderInfo();
      }
    },
    getSupplier() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("mktCustNo"));
      var factory = this.$route.query.tradeType;
      if (
        data &&
        token &&
        data.filter(item => {
          return item.factory == factory;
        }).length
      ) {
        for (let index = 0; index < data.length; index++) {
          if (data[index].token == token && data[index].factory == factory) {
            const element = data[index];
            this.supList = element.data;
            this.frontDataZSupplierf = element.data.slice(0, 20);
          }
        }
      } else {
        if (factory == 58 || factory == 59) {
          mktcustnobyfAC(factory).then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplierf = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.supList, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          mktCustNo().then(res => {
            if (res.code) {
              let that = this;
              that.supList = res.data;
              that.frontDataZSupplierf = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (JSON.stringify(that.supList) != "{}") {
                if (data == null) {
                  arr.push({ data: that.supList, token, factory });
                  localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
                } else {
                  data.push({ data: that.supList, token, factory });
                  localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
                }
              }
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
    },
    getcopper() {
      let joinFactoryId = 0;
      if (this.$route.query.factory) {
        joinFactoryId = this.$route.query.factory;
      }
      const token = Cookie.get("Authorization");
      const data3 = JSON.parse(localStorage.getItem("copperdata"));
      if (
        data3 &&
        token &&
        data3.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a3 = 0; a3 < data3.length; a3++) {
          if (data3[a3].token == token && data3[a3].joinFactoryId == joinFactoryId) {
            this.copperdata = data3[a3].data; //本地缓存
          }
        }
      } else {
        copperthickconversion(joinFactoryId).then(res => {
          if (res.code) {
            this.copperdata = JSON.parse(res.data);
            let token = Cookie.get("Authorization");
            if (JSON.parse(res.data).length) {
              let arr = [];
              if (data3 == null) {
                arr.push({ data: JSON.parse(res.data), token, joinFactoryId: joinFactoryId });
                localStorage.setItem("copperdata", JSON.stringify(arr)); //本地缓存
              } else {
                data3.push({ data: JSON.parse(res.data), token, joinFactoryId: joinFactoryId });
                localStorage.setItem("copperdata", JSON.stringify(data3)); //本地缓存
              }
            }
          }
        });
      }
    },
    getData11() {
      drillInfo(this.id).then(res => {
        if (res.code) {
          this.drilldata = res.data.drillParameters;
          this.drillDiameters = res.data.drillDiameters;
        } else {
          this.$message.error(res.message);
        }
      });
      lineInfo(this.id).then(res => {
        if (res.code) {
          this.lineInfoData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getData1() {
      const maxCacheSize = 1000000;
      let joinFactoryId = 0;
      if (this.$route.query.factory) {
        joinFactoryId = this.$route.query.factory;
      }
      const token = Cookie.get("Authorization");
      const data3 = JSON.parse(localStorage.getItem("boardTypeList"));
      if (
        data3 &&
        token &&
        data3.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a3 = 0; a3 < data3.length; a3++) {
          if (data3[a3].token == token && data3[a3].joinFactoryId == joinFactoryId) {
            this.boardTypeList = data3[a3].data; //本地缓存
          }
        }
      } else {
        coreTypes(joinFactoryId, 0).then(res => {
          if (res.code) {
            this.boardTypeList = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data3 == null) {
                arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("boardTypeList", JSON.stringify(arr)); //本地缓存
              } else {
                data3.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("boardTypeList", JSON.stringify(data3)); //本地缓存
              }
            }
          }
        });
      }
      const data = JSON.parse(localStorage.getItem("ozListData"));
      if (
        data &&
        token &&
        data.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].joinFactoryId == joinFactoryId) {
            this.ozListData = data[a].data; //本地缓存
          }
        }
      } else {
        ozList(joinFactoryId).then(res => {
          if (res.code) {
            this.ozListData = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data == null) {
                arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ozListData", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ozListData", JSON.stringify(data)); //本地缓存
              }
            }
          }
        });
      }
      const data2 = JSON.parse(localStorage.getItem("ppTypeList"));
      if (
        data2 &&
        token &&
        data2.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a2 = 0; a2 < data2.length; a2++) {
          if (data2[a2].token == token && data2[a2].joinFactoryId == joinFactoryId) {
            this.ppTypeList = data2[a2].data; //本地缓存
          }
        }
      } else {
        ppTypes(joinFactoryId).then(res => {
          if (res.code) {
            this.ppTypeList = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data2 == null) {
                arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ppTypeList", JSON.stringify(arr)); //本地缓存
              } else {
                data2.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ppTypeList", JSON.stringify(data2)); //本地缓存
              }
            }
          }
        });
      }
      // this.GBListData
      const data4 = JSON.parse(localStorage.getItem("GBListData"));
      if (
        data4 &&
        token &&
        data4.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a4 = 0; a4 < data4.length; a4++) {
          if (data4[a4].token == token && data4[a4].joinFactoryId == joinFactoryId) {
            this.GBListData = data4[a4].data; //本地缓存
          }
        }
      } else {
        gbList(joinFactoryId).then(res => {
          if (res.code) {
            this.GBListData = res.data;
            let token = Cookie.get("Authorization");
            localStorage.removeItem("coreListData");
            localStorage.removeItem("GBListData");
            if (res.data.length) {
              let arr = [];
              arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
              if (this.getStringSizeInBytes(JSON.stringify(localStorage)) + this.getStringSizeInBytes(JSON.stringify(arr)) < 5) {
                localStorage.setItem("GBListData", JSON.stringify(arr)); //本地缓存
              }
              // if(data4 == null){
              //   arr.push({data: res.data, token,joinFactoryId:joinFactoryId})
              //   localStorage.setItem('GBListData', JSON.stringify(arr));//本地缓存
              // }else{
              //   data4.push({data: res.data, token,joinFactoryId:joinFactoryId})
              //   localStorage.setItem('GBListData', JSON.stringify(data4));//本地缓存
              // }
            }
          }
        });
      }
    },
    async getCore() {
      let factory = 0;
      if (this.$route.query.factory) {
        factory = Number(this.$route.query.factory);
      }
      this.spinning = true;
      const token = Cookie.get("Authorization");
      let mode = 0;
      let coreType = "coreListDataMkt";
      try {
        // 尝试从IndexedDB获取
        const cached = await db.getItem(coreType, { token, factory, mode });

        if (cached && cached.data) {
          this.coreListData = cached.data;
          this.spinning = false;
          return;
        }
        // 无缓存则请求接口
        const res = await coreList(factory, mode);
        if (res.code) {
          this.coreListData = res.data;
          this.spinning = false;
          // 存储到IndexedDB
          if (JSON.stringify(this.coreListData) !== "{}") {
            await db.setItem(coreType, {
              token,
              factory,
              mode,
              data: this.coreListData,
            });
          }
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      } catch (error) {
        console.error("IndexedDB操作失败:", error);
        this.spinning = false;
      }
    },
    getStringSizeInBytes(str) {
      // 使用UTF-8编码计算字符串的字节长度
      let totalBytes = new Blob([str]).size;

      // 将字节长度转换为兆字节（MB）
      let sizeInMB = totalBytes / (1024 * 1024);

      // 返回结果
      return sizeInMB;
    },
    getData(factory) {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("PreSelectPar"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.selectOption = data[a].data; //本地缓存
          }
        }
      } else {
        selectpars(1, factory).then(res => {
          if (res.code) {
            this.selectOption = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.selectOption) != "{}") {
              if (data == null) {
                arr.push({ data: this.selectOption, token, factory });
                localStorage.setItem("PreSelectPar", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.selectOption, token, factory });
                localStorage.setItem("PreSelectPar", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    getTG() {
      let params = {};
      let factory = this.$route.query.factory;
      params.factory = factory;
      params.type = "1";
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("boardtgitems"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.boardtgList = data[a].data; //本地缓存
          }
        }
      } else {
        boardtgitems(params).then(res => {
          if (res.code) {
            this.boardtgList = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.boardtgList) != "{}") {
              if (data == null) {
                arr.push({ data: this.boardtgList, token, factory });
                localStorage.setItem("boardtgitems", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.boardtgList, token, factory });
                localStorage.setItem("boardtgitems", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    ManufacturerandTG() {
      let factory = this.$route.query.factory;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("coretypeverdors"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.ManufacturerTG = data[a].data; //本地缓存
          }
        }
      } else {
        coretypeverdors(factory).then(res => {
          if (res.code) {
            this.ManufacturerTG = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.ManufacturerTG) != "{}") {
              if (data == null) {
                arr.push({ data: this.ManufacturerTG, token, factory });
                localStorage.setItem("coretypeverdors", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.ManufacturerTG, token, factory });
                localStorage.setItem("coretypeverdors", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    getboardBrand() {
      let factory = this.$route.query.factory;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("boardBrandItems"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.boardBrandList = data[a].data; //本地缓存
          }
        }
      } else {
        boardBrandItems(factory).then(res => {
          if (res.code) {
            this.boardBrandList = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.boardBrandList) != "{}") {
              if (data == null) {
                arr.push({ data: this.boardBrandList, token, factory });
                localStorage.setItem("boardBrandItems", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.boardBrandList, token, factory });
                localStorage.setItem("boardBrandItems", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    getsheet() {
      let factory = this.$route.query.factory;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("sheetTraderItems"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.sheetTraderList = data[a].data; //本地缓存
          }
        }
      } else {
        sheetTraderItems(factory).then(res => {
          if (res.code) {
            this.sheetTraderList = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.sheetTraderList) != "{}") {
              if (data == null) {
                arr.push({ data: this.sheetTraderList, token, factory });
                localStorage.setItem("sheetTraderItems", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.sheetTraderList, token, factory });
                localStorage.setItem("sheetTraderItems", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    getRequiredLink() {
      let factory = this.$route.query.factory;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("requiredLinkConfigList"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.requiredLinkConfigList = data[a].data; //本地缓存
          }
        }
      } else {
        requiredLinkConfig(factory, 2).then(res => {
          if (res.code) {
            this.requiredLinkConfigList = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.requiredLinkConfigList) != "{}") {
              if (data == null) {
                arr.push({ data: this.requiredLinkConfigList, token, factory });
                localStorage.setItem("requiredLinkConfigList", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.requiredLinkConfigList, token, factory });
                localStorage.setItem("requiredLinkConfigList", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 参数信息
    async getDetailInfo() {
      let id = this.$route.query.id;
      if (id) {
        this.spinning = true;
        await getEditOrderInfo(id)
          .then(res => {
            if (res.code) {
              res.data.holeDensity = res.data.holeDensity ? res.data.holeDensity : "0";
              this.showData = res.data;
              this.proOrderInfoDto.boardLayers = res.data.boardLayers;
              if (
                (this.$route.query.factory != "22" && this.$route.query.factory != "12") ||
                (this.$route.query.factory == "22" &&
                  this.showData.reOrder != "1" &&
                  this.showData.reOrder != "2" &&
                  this.showData.customClassification != "ECN改版不升级" &&
                  this.showData.isyyqtorder == false) ||
                (this.$route.query.factory == "12" && this.showData.reOrder != "1")
              ) {
                this.required = true;
              } else {
                this.required = false;
              }
              if (this.$refs.editForm1) {
                this.$refs.editForm1.getEditData();
                this.$refs.editForm1.formData = this.showData;
              }
              // if (this.showData.boardLayers <= 2) {
              //   this.showData.plateType = "cg";
              //   this.showData.plateTypeStr = "常规硬板";
              // }
              if (this.showData.pinBanType) {
                let arr = this.showData.pinBanType.toLowerCase().split("x");
                this.$set(this.showData, "pinBanType1", arr[0]);
                this.$set(this.showData, "pinBanType2", arr[1]);
              } else {
                this.$set(this.showData, "pinBanType1", 1);
                this.$set(this.showData, "pinBanType2", 1);
              }
              if (this.showData.processEdges) {
                let arr1 = this.showData.processEdges.split(":");
                if (arr1[0] == "none") {
                  arr1[0] = "无";
                } else if (arr1[0] == "updown") {
                  arr1[0] = "上下方向";
                } else if (arr1[0] == "leftright") {
                  arr1[0] = "左右方向";
                } else if (arr1[0] == "both") {
                  arr1[0] = "四边方向";
                } else if (arr1[0] == "") {
                  arr1[0] = "";
                }
                this.showData.processEdges = arr1[0] + ":" + arr1[1];
              } else {
                this.showData.processEdgesStrL = "none";
                this.showData.processEdgesStrR = 0;
              }
              if (!this.showData.vCut) {
                this.showData.vCut = "vcut";
              }
              if (this.showData.surfaceFinishJsonDto.imGoldThinckness) {
                this.showData.surfaceFinishJsonDto.imGoldThinckness = this.showData.surfaceFinishJsonDto.imGoldThinckness.toString();
              }
              if (this.showData.surfaceFinishJsonDto.newTinThickness) {
                this.showData.surfaceFinishJsonDto.newTinThickness = this.showData.surfaceFinishJsonDto.newTinThickness.toString();
              }
              if (this.showData.surfaceFinishJsonDto.nickelThickness) {
                this.showData.surfaceFinishJsonDto.nickelThickness = this.showData.surfaceFinishJsonDto.nickelThickness.toString();
              }
              if (this.showData.surfaceFinishJsonDto.cjNickelThinckness) {
                this.showData.surfaceFinishJsonDto.cjNickelThinckness = this.showData.surfaceFinishJsonDto.cjNickelThinckness.toString();
              }
              if (this.showData.plateType == "cg") {
                this.$refs.editForm.showCG = true;
              } else {
                this.$refs.editForm.showCG = false;
              }
              if (this.showData.plateType == "hdi") {
                this.$refs.editForm.showHDI = true;
              } else {
                this.$refs.editForm.showHDI = false;
              }
              if (this.showData.plateType == "mm") {
                this.$refs.editForm.showMM = true;
              } else {
                this.$refs.editForm.showMM = false;
              }
              if (this.showData.boardLayers == 0) {
                this.$refs.editForm.show0 = true;
              } else {
                this.$refs.editForm.show0 = false;
              }
              if (this.showData.boardLayers == 1) {
                this.$refs.editForm.show1 = true;
              } else {
                this.$refs.editForm.show1 = false;
              }
              if (this.showData.boardLayers == 2) {
                this.$refs.editForm.show2 = true;
              } else {
                this.$refs.editForm.show2 = false;
              }
              if (this.showData.boardLayers > 2) {
                this.$refs.editForm.showMore = true;
              } else {
                this.$refs.editForm.showMore = false;
              }
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    getSpecInfo() {
      let boid = this.$route.query.boid;
      specFileInfo(boid).then(res => {
        if (res.code) {
          this.specFileData = res.data;
        }
      });
    },
    editInfo() {
      if (JSON.stringify(this.selectOption) == "{}") {
        this.$message.warning("下拉资源加载中，请稍后再试");
        return;
      }
      // //返单不允许编辑预审信息 25/1/2 不区分工厂
      // if (this.reOrder == 1) {
      //   this.$message.error("返单不允许编辑预审信息！");
      //   return;
      // }
      let id = this.$route.query.id;
      if (this.activeKey == 1) {
        this.spinning = true;
        changeCheck(id)
          .then(res => {
            if (res.code) {
              this.editFlag = true;
              this.setedit(this.editFlag);
              this.editKey = this.activeKey;
              this.$refs.editForm.getEditData();
              this.$refs.editForm.$refs.twoMkt.solderColorC();
              this.$refs.editForm.$refs.twoMkt.fontColorC();
              if (this.$route.query.isCustRule == 1 || this.$route.query.isCustRule == true) {
                this.CustomerRulesClick();
              }
            } else {
              if (res.data && res.data.length) {
                this.checkData = res.data;
                this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
                this.dataVisible22 = true;
              } else {
                this.$message.error(res.message);
              }
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.activeKey == 2) {
        this.editFlag = true;
        this.$refs.editForm1.getEditData();
        this.setedit(this.editFlag);
        this.editKey = this.activeKey;
      } else if (this.activeKey == 4) {
        this.editFlag = true;
        this.setedit(this.editFlag);
        this.editKey = this.activeKey;
      } else if (this.activeKey == 6) {
        this.editFlag = true;
        if (this.proOrderStackUpImpDto.stackIMPOutputs == null) {
          this.proOrderStackUpImpDto.stackIMPOutputs = [];
        }
        this.proOrderStackUpImpDto.stackIMPOutputs.splice(this.proOrderStackUpImpDto.stackIMPOutputs.length, 0, {
          imp_LineWidth_: "",
          imp_LineSpace_: "",
          imp_LineCuSpace_: "",
          imp_Value_Req_: "",
          imp_OKLineWidth_: "",
          imp_Value_Tol_: "",
          imp_Type_: "",
          imp_ControlLay_: "",
          imp_UpLay_: "",
          imp_DownLay_: "",
          imp_type_PRC: "",
        });
        this.setedit(this.editFlag);
        this.editKey = this.activeKey;
        this.$refs.dom4.addaction();
        this.$refs.dom4.handleResize();
      } else if (this.activeKey == 7) {
        this.editFlag = true;
        this.setedit(this.editFlag);
        this.editKey = this.activeKey;
        this.$refs.dom7.geteditdata();
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("formData") != -1) {
        newIsNullRules = newIsNullRules.replace(/formData/g, "this.$refs.editForm.formData");
      }
      if (val.indexOf("reOrder") != -1) {
        newIsNullRules = newIsNullRules.replace(/reOrder/g, "this.reOrder");
      }
      if (val.indexOf("tslge") != -1) {
        newIsNullRules = newIsNullRules.replace(/tslge/g, "this.$refs.editForm.$refs.twoMkt.tslge");
      }
      if (val.indexOf("boardBrand") != -1) {
        newIsNullRules = newIsNullRules.replace(/boardBrand/g, "this.$refs.editForm.boardBrand");
      }
      return eval(newIsNullRules);
    },
    dataSave() {
      if (!this.editFlag) {
        this.$message.warning("非编辑状态不可执行保存操作！");
        return;
      }
      var r = /^\+?[1-9][0-9]*$/; //正整数正则
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/; //正数正则
      var a = /^0{1}(\.\d*)|(^[1-9][0-9]*)+(\.\d*)?$/;
      var i = /^99.99999999999999999999999$|^(\d|[0-9]\d)(\.\d{1,10})*$/; //小于100的正数
      var ii = /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/; // 电话格式
      var xx = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // 邮箱格式
      var x1 = /^([0-9]\d*)(\.\d+)?$/;
      if (this.activeKey == 1) {
        this.messageList = [];
        let params = this.$refs.editForm.formData;
        let text = this.$refs.editForm.$refs.twoMkt.$refs.threeMkt.$refs.sixMkt.copynote.note;
        let text1 = this.$refs.editForm.$refs.twoMkt.$refs.threeMkt.$refs.sixMkt.copynote.noteSure;
        params.note = text ? text.replace(/\s+/g, " ") : ""; //空白字符转换为空格
        params.noteSure = text1 ? text1.replace(/\s+/g, " ") : ""; //空白字符转换为空格
        if (
          !(
            params.surfaceFinish == "goldplatedfingerandimmersiongold" ||
            params.surfaceFinish == "immersiongold" ||
            params.surfaceFinish == "immersiongoldandosp" ||
            params.surfaceFinish == "immersiongoldHaslwithfree" ||
            params.surfaceFinish == "wqpxandjbdj" ||
            params.surfaceFinish == "cjandjbdj" ||
            params.surfaceFinish == "zbdjandosp" ||
            params.surfaceFinish == "tinprecipitation" ||
            params.surfaceFinish == "nickelpalladiumgold" ||
            params.surfaceFinish == "chemicalsilver" ||
            params.surfaceFinish == "fullgoldplating" ||
            params.surfaceFinish == "hardgoldplating" ||
            params.surfaceFinish == "waterplatedgold" ||
            params.surfaceFinish == "tinplating" ||
            params.surfaceFinish == "goldplatingandhaslwithfree" ||
            params.surfaceFinish == "waterhardgold" ||
            params.surfaceFinish == "goldplating" ||
            params.surfaceFinish == "immersiongoldandgoldplating" ||
            params.surfaceFinish == "goldnickelplatingandchemical" ||
            params.surfaceFinish == "goldnickelplatingandosp" ||
            params.surfaceFinish == "goldnickelplatingandhaslwithfree" ||
            params.surfaceFinish == "wholegoldplatingandosp" ||
            params.surfaceFinish == "goldnickelplatingandhaslwithlead" ||
            params.surfaceFinish == "fullgilding" ||
            params.surfaceFinish == "nickelpalladiumgoldandcjsz" ||
            params.surfaceFinish == "djszandyqpx" ||
            params.surfaceFinish == "djszandcj" ||
            params.surfaceFinish == "zbcjandcjsz" ||
            params.surfaceFinish == "hjandty" ||
            params.surfaceFinish == "cyandty" ||
            params.surfaceFinish == "djszandwqpx" ||
            params.surfaceFinish == "dnanddjsz" ||
            params.surfaceFinish == "immersiongoldandGoldfinger" ||
            params.surfaceFinish == "electrogold" ||
            params.surfaceFinish == "immersiongoldandjbelectrogold" ||
            params.surfaceFinish == "goldplatingandimmersiongold" ||
            params.surfaceFinish == "goldplatingandosp" ||
            params.surfaceFinish == "wholegoldplating" ||
            params.surfaceFinish == "immersiongoldanddj" ||
            params.surfaceFinish == "goldplatingandosp" ||
            params.surfaceFinish == "goldplatingandimmersiontin" ||
            params.surfaceFinish == "immersionnickelgold" ||
            params.surfaceFinish == "thickgoldplating" ||
            params.surfaceFinish == "goldplatingwithoutnickelplating" ||
            params.surfaceFinish == "goldplatinglocalthickgold" ||
            params.surfaceFinish == "electroplatingsilverelectroplatinggold" ||
            params.surfaceFinish == "chemicalsilverandgoldplating" ||
            params.surfaceFinish == "nickelgoldplating" ||
            params.surfaceFinish == "Goldplatingwithoutnickelplatinglocalthickgold" ||
            params.surfaceFinish == "Immersiongoldlocalthickgold" ||
            params.surfaceFinish == "Immersionnickelgoldplatinghardgold" ||
            params.surfaceFinish == "sunkengoldgold-platedfingers" ||
            params.surfaceFinish == "SelectiveThickGoldPlating" ||
            params.surfaceFinish == "Antioxidant+golddeposition" ||
            params.surfaceFinish == "Tinspraying+goldplating" ||
            params.surfaceFinish == "Leadfreetinspraying+goldplating"
          )
        ) {
          params.surfaceFinishJsonDto.platedArea = null;
        }
        if (
          !(
            params.surfaceFinish == "goldplatingandimmersiongold" ||
            params.surfaceFinish == "goldplatingandimmersiontin" ||
            params.surfaceFinish == "waterhardgold" ||
            params.surfaceFinish == "hardgoldplating" ||
            params.surfaceFinish == "immersiongoldandgoldplating" ||
            params.surfaceFinish == "goldplatinglocalthickgold" ||
            params.surfaceFinish == "Goldplatingwithoutnickelplatinglocalthickgold" ||
            params.surfaceFinish == "Immersiongoldlocalthickgold" ||
            params.surfaceFinish == "Immersionnickelgoldplatinghardgold"
          )
        ) {
          params.surfaceFinishJsonDto.platedArea2 = null;
        }
        if (!params.boardType) {
          params.boardType = "";
        }
        if (params.surfaceFinish == "tinprecipitation") {
          params.surfaceFinishJsonDto.newTinThickness = null;
        } else {
          params.surfaceFinishJsonDto.newTinThickness2 = null;
        }
        if (params.fR4Tg != "" && params.fR4Tg != null) {
          params.fR4Tg = params.fR4Tg.toLowerCase();
        }
        params.setBoardHeight = Number(params.setBoardHeight);
        params.setBoardWidth = Number(params.setBoardWidth);
        if (params.boardLayers < 2) {
          params.inLineWidth = null;
          params.inLineSpacing = null;
        }
        if (params.serialNumber == "") {
          params.serialNumber = null;
        }
        if (params.totalLayer == "") {
          params.totalLayer = null;
        }
        if (params.jumpCutXt == "") {
          params.jumpCutXt = null;
        }
        if (params.goldfingerHpNum == "") {
          params.goldfingerHpNum = null;
        }
        if (params.innerBevelNum == "") {
          params.innerBevelNum = null;
        }
        if (params.blueGumPercentage == "") {
          params.blueGumPercentage = null;
        }
        if (params.carbonOilPercentage == "") {
          params.carbonOilPercentage = null;
        }
        if (params.conventionBevelNum == "") {
          params.conventionBevelNum = null;
        }
        if (params.totalHoleNum == "") {
          params.totalHoleNum = null;
        }
        if (params.pasteRedTape == "") {
          params.pasteRedTape = null;
        }
        if (!params.packagingQuantity) {
          params.packagingQuantity = null;
        }
        if (!params.spareQuantity) {
          params.spareQuantity = null;
        }
        if (!params.depthControl) {
          params.depthControl = null;
        }
        if (!params.countersinkAngle) {
          params.countersinkAngle = null;
        }
        if (!params.depthControlArea) {
          params.depthControlArea = null;
        }
        if (params.surfaceFinishJsonDto.platedArea) {
          params.surfaceFinishJsonDto.platedArea = Math.round(params.surfaceFinishJsonDto.platedArea).toString();
        }
        if (!params.blindHoleMin) {
          params.blindHoleMin = null;
        }
        if (!params.pinBanNum) {
          params.pinBanNum = null;
        }
        if (!params.ipcLevel) {
          params.ipcLevel = null;
        }
        if (!params.backDrillNum) {
          params.backDrillNum = null;
        }
        if (!params.vCutKnifeNum) {
          params.vCutKnifeNum = null;
        }
        if (!params.slotHoleNum) {
          params.slotHoleNum = null;
        }
        if (!params.testPointNum) {
          params.testPointNum = null;
        }
        if (!params.laserHoleNum) {
          params.laserHoleNum = null;
        }
        if (!params.blindHoleNum) {
          params.blindHoleNum = null;
        }
        if (!params.profileHoleNum) {
          params.profileHoleNum = null;
        }
        if (!params.stepHoleNum) {
          params.stepHoleNum = null;
        }
        if (!params.steppedHoleNum) {
          params.steppedHoleNum = null;
        }
        if (params.boardLayers < 2) {
          params.ppNum = null;
        }
        if (params.cuThickness) {
          const cuThicknessParts = params.cuThickness.split("/");
          const isInvalidFormat = cuThicknessParts.some(part => !x.test(part));
          if ((params.cuThickness.indexOf("/") === -1 && !x.test(params.cuThickness)) || isInvalidFormat) {
            this.message.push("铜厚格式不正确,请检查后重新输入");
            this.messageList.push("成品铜厚");
          }
        }
        if (params.boardLayers <= 2) {
          if (params.plateType == "mm") {
            this.$message.warning("订单类型为盲埋,层数必须大于2层");
            return;
          }
          if (params.plateType == "hdi") {
            this.$message.warning("订单类型为HDI,层数必须大于2层");
            return;
          }
          params.blindBury = null;
          params.blindBuryOrder = null;
          params.pressTimes = null;
          params.laserOrder = null;
          params.laserType = null;
          params.laserNum = null;
          params.laserMinHole = null;
          params.laserHoleNum = null;
          params.blindHoleNum = null;
          params.blindHoleMin = null;
          params.holetoline = null;
          params.gbNum2 = null;
          params.isChangeLayerPres = false;
          params.isPseudolayer = false;
          params.totalLayer = null;
        }
        if (params.plateType == "cg") {
          params.blindBury = null;
          params.blindBuryOrder = null;
          params.pressTimes = null;
          params.blindHoleNum = null;
          params.blindHoleMin = null;
        }
        if (params.plateType != "hdi") {
          params.laserNum = null;
          params.laserOrder = null;
          params.laserMinHole = null;
          params.laserHoleNum = null;
          params.laserType = null;
        }
        if (params.boardHeight) {
          params.boardHeight = Number(params.boardHeight);
        } else {
          params.boardHeight = null;
        }
        if (params.boardWidth) {
          params.boardWidth = Number(params.boardWidth);
        } else {
          params.boardWidth = null;
        }
        if (!params.innerCopperThickness2) {
          params.innerCopperThickness2 = null;
        } else {
          params.innerCopperThickness2 = Number(params.innerCopperThickness2);
        }
        if (!params.copperThickness2) {
          params.copperThickness2 = null;
        } else {
          params.copperThickness2 = Number(params.copperThickness2);
        }
        if (!params.innerCopperThickness) {
          params.innerCopperThickness = null;
        } else {
          params.innerCopperThickness = Number(params.innerCopperThickness);
        }
        if (!params.copperThickness) {
          params.copperThickness = null;
        } else {
          params.copperThickness = Number(params.copperThickness);
        }
        if (params.pinBanType1 && params.pinBanType2) {
          params.pinBanType = params.pinBanType1 + "x" + params.pinBanType2;
          params.pinBanTypeStrX = params.pinBanType1;
          params.pinBanTypeStrY = params.pinBanType2;
        }
        if (
          Number((params.setBoardHeight * params.setBoardWidth).toFixed(4)) < Number((params.boardHeight * params.boardWidth * params.su).toFixed(4))
        ) {
          this.dataVisible7 = true;
          this.message1 = "成品尺寸面积小于单元面积，请确认";
          return;
        }
        // if (params.isCopperThickConversion) {
        //   params.copperThickness = this.copperdata.filter(item => item.CopperThickness2 == params.copperThickness2)[0]?.CopperThickness;
        //   params.innerCopperThickness = this.copperdata.filter(item => item.CopperThickness2 == params.innerCopperThickness2)[0]?.CopperThickness;
        // }
        this.$refs.editForm.numChange("save");
        if (this.required) {
          this.islink();
        } else {
          for (let key in this.requiredLinkConfigList) {
            let r = "";
            if (this.requiredLinkConfigList[key].linkRules) {
              r = eval(this.requiredLinkConfigList[key].linkRules);
            }
            if (params.boardLayers == 0) {
              params.boardLayers = params.boardLayers.toString();
            }
            if (this.requiredLinkConfigList[key].linkRules && params[key] && !r.test(params[key])) {
              this.message.push(this.requiredLinkConfigList[key].message);
              this.messageList.push(this.requiredLinkConfigList[key].lable);
            }
          }
        }
        if (this.message.length) {
          this.dataVisible = true;
          return;
        }
        this.$delete(params, "status");
        let id = this.$route.query.id;
        // 确保id是一个字符串
        params.id = Array.isArray(id) ? id[0] : id;
        this.spinning = true;
        updateOrderInfo(params)
          .then(res => {
            if (res.code) {
              this.editFlag = false;
              this.setedit(this.editFlag);
              this.$message.success(res.message);
              this.getDetailInfo();
              this.getViewLog();
              this.getData11();
              this.$refs.editForm.formData = {};
              this.$refs.editForm.setStyle();
            } else {
              this.$message.error(res.message);
              this.editFlag = false;
              this.setedit(this.editFlag);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.activeKey == 2) {
        let params = this.$refs.editForm1.formData;
        params.eqdtos = this.$refs.editForm1.tableDetails;
        params.mktNote = params.mktNote ? params.mktNote.replace(/\s+/g, " ") : "";
        params.costNote = params.costNote ? params.costNote.replace(/\s+/g, " ") : "";
        if (this.$refs.editForm1.needReportList) {
          params.needReportList = this.$refs.editForm1.needReportList.toString();
        }
        if (!params.referencePrice1) {
          params.referencePrice1 = null;
        }
        if (!params.referencePrice2) {
          params.referencePrice2 = null;
        }
        if (params.sparePartNum) {
          params.sparePartNum = params.sparePartNum.toString();
        }
        if (!params.delType) {
          this.$message.info("请填写交货单位");
          return;
        }
        if (params.eqEmail && !xx.test(params.eqEmail)) {
          this.$message.info("请填写正确的邮箱格式");
          return;
        }
        if (params.delArea && !x.test(params.delArea)) {
          this.$message.info("交货面积请填写正数");
          return;
        }
        if (params.eqPhoneNumber && !ii.test(params.eqPhoneNumber)) {
          this.$message.info("请填写正确的电话格式");
          return;
        }
        if (params.num && !r.test(params.num)) {
          this.$message.info("参考数量请填写正整数");
          return;
        }
        params.id = this.saveID;
        SaleOrderInfo(params)
          .then(res => {
            if (res.code) {
              this.$message.success("保存成功");
              this.getDetailInfo();
              this.getViewLog();
              this.editFlag = false;
              this.setedit(this.editFlag);
            } else {
              this.$message.error(res.message);
              this.getDetailInfo();
              this.editFlag = false;
              this.setedit(this.editFlag);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.activeKey == 4) {
        let data1 = JSON.parse(JSON.stringify(this.drilldata));
        const now = new Date();
        const isoString = now.toISOString();
        for (let index = 0; index < this.drilldata.length; index++) {
          const val = this.drilldata[index];
          if (val.beginLayer && !r.test(val.beginLayer)) {
            this.$message.warning("开始层数请填写正整数");
            return;
          }
          if (val.endLayer && !r.test(val.endLayer)) {
            this.$message.warning("结束层数请填写正整数");
            return;
          }
          if (val.layerNo && !r.test(val.layerNo)) {
            this.$message.warning("总层数请填写正整数");
            return;
          }
          // if(val.minBorehole && !x.test(val.minBorehole)){
          //   this.$message.warning('最小孔径请输入正数')
          //   return
          // }
          if (Number(val.beginLayer) && Number(val.endLayer) && Number(val.beginLayer) >= Number(val.endLayer)) {
            this.$message.warning("开始层数不能大于等于结束层数");
            return;
          }
          if (Number(val.endLayer) > Number(val.layerNo)) {
            this.$message.warning("结束层不得大于总层数");
            return;
          }
          val.beginLayer = val.beginLayer ? Number(val.beginLayer) : null;
          val.endLayer = val.endLayer ? Number(val.endLayer) : null;
          val.layerNo = val.layerNo ? Number(val.layerNo) : null;
          // val.minBorehole= val.minBorehole?Number(val.minBorehole):null
          if (JSON.stringify(val) != JSON.stringify(data1[index])) {
            val.createTime = isoString;
          }
        }
        let params = {
          id: this.$route.query.id,
          lineInfoInputs: this.lineInfoData,
          tdrillParameterInputs: this.drilldata,
        };
        setpcborderpartableinfo(params)
          .then(res => {
            if (res.code) {
              this.$message.success("保存成功");
              this.editFlag = false;
              this.setedit(this.editFlag);
              this.getData11();
            } else {
              this.$message.warning(res.message);
              this.editFlag = false;
              this.setedit(this.editFlag);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.activeKey == 6) {
        // 叠层阻抗信息保存
        this.$refs.dom4.imphandleOk();
        this.$refs.dom4.delaction();
        this.editFlag = false;
        this.setedit(this.editFlag);
      } else if (this.activeKey == 7) {
        let data = this.$refs.dom7.formData;
        data.id = this.$route.query.id;
        if (data.custComponents != "全部" && data.custComponents == "部分" && this.$refs.dom7.custComponents) {
          data.custComponents = this.$refs.dom7.custComponents;
        }
        updateOrderInfo(data)
          .then(res => {
            if (res.code) {
              this.editFlag = false;
              this.setedit(this.editFlag);
              this.$message.success(res.message);
              this.getDetailInfo();
            } else {
              this.$message.error(res.message);
              this.editFlag = false;
              this.setedit(this.editFlag);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    islink() {
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/; //正数正则
      var x1 = /^([0-9]\d*)(\.\d+)?$/;
      var r = /^\+?[1-9][0-9]*$/; //正整数正则
      let params = this.$refs.editForm.formData;
      params.copperThickness = params.copperThickness === 0 ? "0" : params.copperThickness;
      params.innerCopperThickness = params.innerCopperThickness === 0 ? "0" : params.innerCopperThickness;
      for (var key in this.requiredLinkConfigList) {
        let r = "";
        if (this.requiredLinkConfigList[key].linkRules) {
          r = eval(this.requiredLinkConfigList[key].linkRules);
        }
        if (params.boardLayers == 0) {
          params.boardLayers = params.boardLayers.toString();
        }
        if (
          (this.requiredLinkConfigList[key] && this.iseval(this.requiredLinkConfigList[key].isNullRules) && !params[key]) ||
          (this.requiredLinkConfigList[key].linkRules && params[key] && !r.test(params[key]))
        ) {
          this.message.push(this.requiredLinkConfigList[key].message);
          this.messageList.push(this.requiredLinkConfigList[key].lable);
        }
      }
      if (params.pinBanType1 > 1 || params.pinBanType2 > 1) {
        if (params.boardType == "PCS") {
          this.message.push("拼版方式大于1x1,拼版单位请选择SET");
          this.messageList.push("拼版单位");
        }
      }
      if (params.boardType == "PCS" && this.reOrder != 1) {
        if (params.setBoardHeight != params.boardHeight || params.setBoardWidth != params.boardWidth) {
          this.message.push("拼版单位为pcs,请核实成品尺寸");
          this.messageList.push("成品长度");
          this.messageList.push("成品宽度");
        }
      }
      if (params.su > 1 && this.reOrder != 1) {
        if (params.setBoardHeight == params.boardHeight && params.setBoardWidth == params.boardWidth) {
          this.message.push("拼版单位为set,请核实成品长度宽尺寸");
          this.messageList.push("成品长度");
          this.messageList.push("成品宽度");
        }
      }
      if (params.ipcLevel == 2 || params.ipcLevel == 3 || params.ipcLevel == 5 || params.ipcLevel == 6) {
        if (params.minHoleCopper && params.minHoleCopper < 20) {
          this.message.push("IPC三级标准,军工标准最小孔铜必须≥20UM");
          this.messageList.push("最小孔铜um");
        }
      }
      if (
        ((!params.innerCopperThickness && params.boardLayers > 2) ||
          (!params.copperThickness && params.boardLayers > 0) ||
          (params.cuThickness && params.cuThickness.split("/").indexOf("") > -1 && params.boardLayers > 0)) &&
        this.iseval(this.requiredLinkConfigList.copperThickness.isNullRules) != 0
      ) {
        if (!params.innerCopperThickness && params.boardLayers > 2) {
          this.message.push("请选择成品铜厚内铜");
          this.messageList.push("成品铜厚");
        } else if (!params.copperThickness && params.boardLayers > 0) {
          this.message.push("请选择成品铜厚外铜");
          this.messageList.push("成品铜厚");
        } else if (params.cuThickness.split("/").indexOf("") > -1 && params.boardLayers > 0) {
          if (params.boardLayers % 2 == 0) {
            this.message.push("铜厚不能为空");
            this.messageList.push("成品铜厚");
          }
        } else {
          this.message.push("请选择成品铜厚");
          this.messageList.push("成品铜厚");
        }
      }
      if (
        (params.surfaceFinish == "immersiongoldandosp" || //沉金+OS
          params.surfaceFinish == "immersiongold" || //沉金
          params.surfaceFinish == "immersiongoldandGoldfinger" || //沉金+电金手指
          params.surfaceFinish == "nickelpalladiumgold" || //镍钯金
          params.surfaceFinish == "cyandty" || //沉银+碳油
          params.surfaceFinish == "djszandwqpx" || //镀金手指&无铅喷锡
          params.surfaceFinish == "djszandyqpx" || //镀金手指&有铅喷锡
          params.surfaceFinish == "dnanddjsz" || //镀镍&镀金手指
          params.surfaceFinish == "hjandty" || //化金&碳墨
          params.surfaceFinish == "nickelpalladiumgoldandcjsz" || //镍钯金&金手指
          params.surfaceFinish == "zbcjandcjsz" || //整板沉金&沉金手指
          params.surfaceFinish == "electrogold") && //电金
        !x.test(params.surfaceFinishJsonDto.platedArea)
      ) {
        this.message.push("请填写面积为正数");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "immersiongoldandosp" || //沉金+OS
          params.surfaceFinish == "nbjandcj" || //镍钯金+沉金
          params.surfaceFinish == "nbjandgoldplating" || //镍钯金+镀金
          params.surfaceFinish == "immersiongoldanddj" || //沉金+电金
          (params.surfaceFinish == "haslwithleadandGoldfinger" && this.$route.query.factory != "38") || //有铅喷锡+电金手指
          params.surfaceFinish == "immersiongoldandty" || //沉金+碳油
          params.surfaceFinish == "chemicalsilverandgoldplating" || //沉银+镀金
          params.surfaceFinish == "ospandfinger" || //osp+金手指
          params.surfaceFinish == "immersiongold" || //沉金
          params.surfaceFinish == "immersiongoldandGoldfinger" || //沉金+电金手指
          params.surfaceFinish == "nickelpalladiumgold" || //镍钯金
          params.surfaceFinish == "hjandty" || //化金&碳墨
          params.surfaceFinish == "nickelpalladiumgoldandcjsz" || //镍钯金&金手指
          params.surfaceFinish == "ospandcjsz" || //OSP&沉金手指
          params.surfaceFinish == "wqpxandcjsz" || //无铅喷锡&沉金手指
          params.surfaceFinish == "yqpxandcjsz" || //有铅喷锡&沉金手指
          params.surfaceFinish == "zbcjandcjsz" || //整板沉金&沉金手指
          params.surfaceFinish == "electrogold") && //电金
        !params.surfaceFinishJsonDto.imGoldThinckness
      ) {
        this.message.push("请选择金厚");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "nbjandgoldplating" || //镍钯金+镀金
          params.surfaceFinish == "immersiongoldanddj") && //沉金+电金
        !params.surfaceFinishJsonDto.imGoldThinckness2
      ) {
        this.message.push("请选择金厚");
        this.messageList.push("表面处理");
      }
      if (
        params.surfaceFinishJsonDto.imGoldThinckness &&
        !r.test(params.surfaceFinishJsonDto.imGoldThinckness) &&
        (this.$route.query.factory == "37" || this.$route.query.factory == "57")
      ) {
        this.message.push("金厚请输入或选择正整数");
        this.messageList.push("表面处理");
      } else if (params.surfaceFinishJsonDto.imGoldThinckness && !x1.test(params.surfaceFinishJsonDto.imGoldThinckness)) {
        this.message.push("金厚请输入或选择正数");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "immersiongold" || //沉金
          params.surfaceFinish == "immersiongoldandGoldfinger" || //沉金+电金手指
          params.surfaceFinish == "nickelpalladiumgold") && //镍钯金
        !params.surfaceFinishJsonDto.cjNickelThinckness &&
        this.$route.query.factory != 58 &&
        this.$route.query.factory != 59 &&
        this.$route.query.factory != 38
      ) {
        this.message.push("请填写镍厚");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "hardgoldplating" || //镀硬金
          params.surfaceFinish == "tinprecipitation" || //沉锡
          params.surfaceFinish == "chemicalsilver" || //沉银
          params.surfaceFinish == "goldplatingandhaslwithfree" || //镀金+无铅喷锡
          params.surfaceFinish == "fullgoldplating" || //镀软金
          params.surfaceFinish == "goldplating" || //镀金
          params.surfaceFinish == "waterplatedgold" || //镀水金
          params.surfaceFinish == "tinplating" || //电镀锡
          params.surfaceFinish == "waterhardgold" || //镀水金+镀软金
          params.surfaceFinish == "immersiongoldandgoldplating" || //沉金+选择性镀金
          params.surfaceFinish == "djszandcj" || //镀金手指&化金
          params.surfaceFinish == "goldnickelplatingandosp" || //局部镀镍/金+OSP
          params.surfaceFinish == "goldnickelplatingandchemical" || //局部镀镍/金+化学镍金
          params.surfaceFinish == "goldnickelplatingandhaslwithfree" || //局部镍金+无铅喷锡
          params.surfaceFinish == "wholegoldplatingandosp" || //全板镀镍/金+OSP
          params.surfaceFinish == "goldnickelplatingandhaslwithlead" || //有铅喷锡+选择性镀金
          params.surfaceFinish == "fullgilding") && //全面镀金
        params.surfaceFinishJsonDto.platedArea != null &&
        params.surfaceFinishJsonDto.platedArea != "" &&
        !x1.test(params.surfaceFinishJsonDto.platedArea)
      ) {
        this.message.push("请填写面积为正数");
        this.messageList.push("表面处理");
      }
      if (
        params.surfaceFinish == "wholegoldplating" && //整板镀金
        params.surfaceFinishJsonDto.platedArea != null &&
        params.surfaceFinishJsonDto.platedArea != "" &&
        !x1.test(params.surfaceFinishJsonDto.platedArea)
      ) {
        this.message.push("请填写镀面积为正数");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "goldplatingandimmersiongold" || //镀金+沉金
          params.surfaceFinish == "goldplatingandimmersiontin" || //镀金+沉锡
          params.surfaceFinish == "waterhardgold" || //镀水金+镀软金
          params.surfaceFinish == "hardgoldplating") && //镀硬金
        params.surfaceFinishJsonDto.platedArea2 != null &&
        params.surfaceFinishJsonDto.platedArea2 != "" &&
        !x1.test(params.surfaceFinishJsonDto.platedArea2)
      ) {
        this.message.push("请填写面积为正数");
        this.messageList.push("表面处理");
      }
      if (params.surfaceFinish == "cyandty" && !params.surfaceFinishJsonDto.newSilverThickness) {
        this.message.push("请填写银厚");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "goldplatingandimmersiongold" ||
          params.surfaceFinish == "dnanddjsz" ||
          params.surfaceFinish == "goldplatingandimmersiontin" ||
          params.surfaceFinish == "goldplatingandosp" ||
          params.surfaceFinish == "djszandwqpx" ||
          params.surfaceFinish == "djszandyqpx") &&
        !params.surfaceFinishJsonDto.imGoldThinckness
      ) {
        this.message.push("请选择镀金厚");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "goldplatingandimmersiongold" ||
          params.surfaceFinish == "goldplatingandimmersiontin" ||
          params.surfaceFinish == "goldplatingandosp") &&
        !params.surfaceFinishJsonDto.platedArea
      ) {
        this.message.push("请填写镀面积为正数");
        this.messageList.push("表面处理");
      }
      if (
        (params.surfaceFinish == "nickelpalladiumgold" || params.surfaceFinish == "nickelpalladiumgoldandcjsz") &&
        !x.test(params.surfaceFinishJsonDto.paThickness)
      ) {
        this.message.push("请填写钯厚为正数");
        this.messageList.push("表面处理");
      }
    },
    //拼版点击确定
    handleOk7() {
      this.dataVisible7 = false;
      this.$refs.editForm.numChange("save");
      if (this.required) {
        this.islink();
      } else {
        for (let key in this.requiredLinkConfigList) {
          let r = "";
          if (this.requiredLinkConfigList[key].linkRules) {
            r = eval(this.requiredLinkConfigList[key].linkRules);
          }
          if (params.boardLayers == 0) {
            params.boardLayers = params.boardLayers.toString();
          }
          if (this.requiredLinkConfigList[key].linkRules && params[key] && !r.test(params[key])) {
            this.message.push(this.requiredLinkConfigList[key].message);
            this.messageList.push(this.requiredLinkConfigList[key].lable);
          }
        }
      }
      if (this.message.length) {
        this.dataVisible = true;
        return;
      }
      this.message1 = "";
      let params = this.$refs.editForm.formData;
      this.$delete(params, "status");
      params.id = this.$route.query.id;
      updateOrderInfo(params).then(res => {
        if (res.code) {
          this.editFlag = false;
          this.setedit(this.editFlag);
          this.$message.success(res.message);
          this.getDetailInfo();
          this.getViewLog();
          this.$refs.editForm.formData = {};
        } else {
          this.$message.error(res.message);
          this.editFlag = false;
          this.setedit(this.editFlag);
        }
      });
    },
    // 弹窗制作
    reportHandleCancel() {
      this.dataVisible = false;
      this.message = [];
      this.dataVisible7 = false;
      this.message1 = "";
      this.dataVisible22 = false;
      this.check = false;
      this.confirmVisible = false;
    },

    // 图形预览
    previewClick() {
      graphicPreview(this.$route.query.id).then(res => {
        if (res.data) {
          window.open(res.data, "_blank");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 开料拼版
    CuttingClick() {
      let params = this.showData;
      if (!params.setBoardHeight) {
        this.$message.info("请填写成长");
        return;
      }
      if (!params.setBoardWidth) {
        this.$message.info("请填写成宽");
        return;
      }
      if (this.id) {
        autoToolPnl(this.id).then(res => {
          if (res.code) {
            this.CuttingData = res.data;
            this.$router.push({
              path: "/gongju/cutting",
              query: {
                boardThink: res.data.boardThink,
                joinFactoryId: res.data.joinFactoryId,
                cpLen: res.data.cpLen,
                cpWidth: res.data.cpWidth,
                job: res.data.job,
                layCount: res.data.layCount,
                pcsset: res.data.pcsset,
                pcssetName: res.data.pcssetName,
                unitLen: res.data.unitLen,
                unitWidth: res.data.unitWidth,
                setX: res.data.setX,
                setY: res.data.setY,
                typemodel: true,
                numUDSix: params.impGroupNum / 2,
                type: "SC",
              },
            });
            // window.open(routeOne.href, '_blank',routeOne.query)
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 叠层阻抗
    laminationClick() {
      let params = this.showData;
      if (!params.boardBrand) {
        this.$message.info("请选择板材型号");
        return;
      }
      let org = [];
      let fin = [];
      if (this.lineInfoData.length) {
        for (let index = 0; index < this.lineInfoData.length; index++) {
          const element = this.lineInfoData[index];
          if (!element.cU4Org_) {
            org.push(index + 1);
          }
          if (!element.cU4Finished_) {
            fin.push(index + 1);
          }
        }
      }
      if (org.length) {
        this.$message.info(`钻带页第${org.join(",")}行基铜数据未填写,不允许跳转叠层阻抗`);
        return;
      }
      if (fin.length) {
        this.$message.info(`钻带页第${fin.join(",")}行完成铜厚数据未填写,不允许跳转叠层阻抗`);
        return;
      }
      if (this.id) {
        orderpreimp(this.id).then(res => {
          if (res.code) {
            this.$router.push({
              path: "/gongju/impedance",
              query: {
                boardType: res.data.boardType,
                finishBoardThickness: res.data.finishBoardThickness,
                layers: res.data.layers,
                pdctno: res.data.pdctno,
                InCopperThickness: res.data.innerCopperThickness,
                OutCopperThickness: res.data.copperThickness,
                joinFactoryId: this.$route.query.factory,
                inCu: res.data.inCu,
                outCu: res.data.outCu,
                mode: 0,
                id: this.id,
              },
            });
            if (res.data.drills.length) {
              let drillsData = res.data.drills;
              localStorage.setItem("drillsData", JSON.stringify({ drillsData }));
            } else {
              localStorage.removeItem("drillsData");
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 查看日志
    getViewLog() {
      let OrderId = this.$route.query.id;
      // if(OrderId){
      //   this.spinning = true
      //   orderLog(OrderId).then(res => {
      //   if (res.code){
      //       this.viewLogData = res.data
      //     } else {
      //       this.$message.error(res.message)
      //     }
      // }).finally(()=>{
      //   this.spinning = false
      // })
      // }
    },
    CustomerRulesClick() {
      this.dataopen = true;
      this.ruleDescribe_ = [];
      let CustNo = this.$route.query.custNo;
      let factory = this.$route.query.factory;
      getCustomerInfo(CustNo, factory, 0, "", this.$route.query.orderNo).then(res => {
        if (res.code) {
          this.CustomerData = res.data;
          for (let index = 0; index < this.CustomerData.length; index++) {
            const element = this.CustomerData[index].ruleDescribe_;
            this.ruleDescribe_.push(element);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    convertToLetter(amount) {
      const capitalNumbers = ["", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
      const unit = ["", "十", "百", "千", "万"]; // 加入万单位
      const numStr = amount.toString();
      let chineseStr = "";
      for (let i = 0; i < numStr.length; i++) {
        const digit = parseInt(numStr[i]); // 获取每一位的数字
        const unitIndex = (numStr.length - 1 - i) % 5; // 获取单位索引
        if (digit !== 0) {
          if (capitalNumbers[digit] == "一" && unit[unitIndex]) {
            chineseStr += unit[unitIndex];
          } else {
            chineseStr += capitalNumbers[digit] + unit[unitIndex];
          }
        }
      }
      return chineseStr;
    },
    dataCancel() {
      this.dataopen = false;
    },
    reportHandleCancel2() {
      this.mktnotevisible = false;
      document.getElementsByClassName("dragging1")[0].style.display = "none";
    },
    getModalContainer() {
      return document.querySelector("#modal-container"); // 返回弹窗容器的选择器或 HTMLElement 对象
    },
    onResize: function (x, y, width, height) {
      this.x = x;
      this.y = y;
      this.width = width;
      this.height = height;
    },
    onDrag: function (x, y) {
      document.getElementsByClassName("dragging1")[0].style.left = x;
      this.x = x;
      this.y = y;
    },
    // 预审完成
    modifyInfoClick() {
      this.spinning = true;
      indicationCheck(this.saveID, 0)
        .then(res => {
          if (res.code) {
            this.checkData = res.data;
            this.checktype = "yswc";
            this.check = this.checkData.findIndex(v => v.error == "1") < 0;
            if (this.checkData.length == 0) {
              getModifyInformation(this.saveID).then(res => {
                if (res.code) {
                  this.$message.success("预审完成");
                  this.$nextTick(function () {
                    this.$router.push({ path: "/shichang/Orderverify" });
                    this.$forceUpdate();
                  });
                } else {
                  if (res.data.length) {
                    this.checkData = res.data;
                    this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                    this.dataVisible22 = true;
                  } else {
                    this.$message.error(res.message);
                  }
                }
              });
            } else {
              this.dataVisible22 = true;
            }
          } else {
            this.$message.error(res, message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 后台开料
    backGroudKLClick() {
      prebackgroudkL(this.saveID).then(res => {
        if (res.code) {
          this.$message.success("提交后台开料成功,请稍后刷新获取结果");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //报价完成
    confirmOk() {
      this.confirmVisible = false;
      this.spinning = true;
      indicationCheck(this.saveID, 1).then(res => {
        if (res.code) {
          this.checkData = res.data;
          this.checktype = "bjwc";
          this.check = this.checkData.findIndex(v => v.error == "1") < 0;
          if (this.checkData.length == 0) {
            verifyFinishedOrder(this.saveID)
              .then(res => {
                if (res.code) {
                  settoolcheck(this.saveID).then(res => {});
                  this.$message.success("完成");
                } else {
                  if (res.data && res.data.length) {
                    this.checkData = res.data;
                    this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                    this.dataVisible22 = true;
                  } else {
                    this.$message.error(res.message);
                  }
                }
              })
              .finally(() => {
                this.spinning = false;
              });
          } else {
            this.dataVisible22 = true;
          }
        } else {
          this.$message.error(res, message);
          this.spinning = false;
        }
      });
    },
    //差异预览
    diffPreview() {
      if (!this.showData.jobId1 || !this.showData.jobId) {
        this.$message.error("该订单暂不支持使用差异预览功能");
        return;
      }
      this.diffData = [];
      this.diffVisible = true;
      this.diffLoad = true;
      checkbilldif(this.showData.jobId1, this.showData.jobId)
        .then(res => {
          if (res.code) {
            this.alldiffData = res.data;
            this.alldiffData.sort((a, b) => {
              return a.disPlay - b.disPlay;
            });
            this.diffData = this.alldiffData.filter(v => v.isSame == false);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.diffLoad = false;
        });
    },
    showList(type) {
      this.diffType = type;
      if (type == "all") {
        this.diffData = this.alldiffData;
      } else {
        this.diffData = this.alldiffData.filter(v => v.isSame == false);
      }
    },
    ordermodifyInfo() {
      this.confirmVisible = true;
      this.confirmmessage = "确认该订单报价完成吗?";
    },
    continueClick() {
      this.dataVisible22 = false;
      this.spinning = true;
      if (this.checktype == "yswc") {
        getModifyInformation(this.saveID)
          .then(res => {
            if (res.code) {
              this.$message.success("预审完成");
              this.$nextTick(function () {
                this.$router.push({ path: "/shichang/Orderverify" });
                this.$forceUpdate();
              });
              this.check = false;
            } else {
              if (res.data.length) {
                this.checkData = res.data;
                this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                this.dataVisible22 = true;
              } else {
                this.$message.error(res.message);
                this.check = false;
              }
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.checktype == "bjwc") {
        verifyFinishedOrder(this.saveID)
          .then(res => {
            if (res.code) {
              settoolcheck(this.saveID).then(res => {});
              this.$message.success("报价完成");
              this.check = false;
            } else {
              if (res.data && res.data.length) {
                this.checkData = res.data;
                this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                this.dataVisible22 = true;
              } else {
                this.$message.error(res.message);
                this.check = false;
              }
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.diff {
  border: 1px solid #efefef;
  /deep/.ant-table-row-cell-ellipsis,
  .ant-table-row-cell-ellipsis .ant-table-column-title {
    overflow: overlay;
    white-space: normal;
    text-overflow: inherit;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 4px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 4px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr {
      .lastTd {
        padding: 0 4px !important;
      }
    }
  }
}
/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
.mkt {
  /deep/.ant-modal-body {
    padding: 12px;
  }
}
/deep/.ant-drawer-body {
  padding: 7px;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
// /deep/.ant-tabs .ant-tabs-top-content{
//   overflow: auto !important;
// }
.drawerclass {
  margin-right: 10px;
}
/deep/.dragging1 {
  display: none;
  position: absolute;
  top: -20px;
  left: 0;
  // left:380px;
  z-index: 999 !important;
  .handle {
    width: 1px;
    height: 1px;
    border: 1px solid #ff9900;
    z-index: 9999;
    display: block !important;
  }
  .handle-tl {
    top: 0;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-tm {
    top: 1px;
    left: 0;
    width: 100%;
    margin-left: 0;
  }
  .handle-tr {
    top: 0;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-mr {
    top: 0;
    right: -1px;
    margin-top: 1px;
    height: 100% !important;
  }
  .handle-br {
    bottom: -1px;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-bm {
    bottom: -1px;
    left: 0;
    margin-left: 0;
    width: 100%;
  }
  .handle-bl {
    bottom: -1px;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000 !important;
  }
  .handle-ml {
    left: -1px;
    margin-top: 0;
    top: 1px;
    height: 100%;
  }
}
/deep/#modal-container > div:first-child {
  height: 100%;
}
#modal-container {
  position: relative;
  height: 100%;
  /deep/.ant-modal-root {
    height: 100%;
  }
  /deep/.ant-modal-wrap {
    position: unset;
    width: 100%;
    // border:2px solid #efefef;
    border: 2px solid #ffcd82;
    box-shadow: 0 6px 20px #ffcd82;
    border-left:hover {
      cursor: e-resize;
    }
  }
  /deep/.ant-modal {
    top: 0;
    position: unset;
    padding-bottom: 0;
  }
  /deep/.ant-modal-content {
    position: unset;
  }
}
/deep/.ant-form-item-label > label::after {
  margin: 0 2px 0 2px;
}
.spec {
  background: #d5e4f2;
  width: 16.5%;
  position: absolute;
  top: 36px;
  right: 0;
}
/deep/.ant-tabs .ant-tabs-top-content.ant-tabs-content-animated {
  min-height: 715px;
  height: auto;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 12px / 1.14 arial;
}
.orderDetail {
  padding: 10px;
  padding-bottom: 0;
  background: #ffffff;
  min-width: 1368px;
  overflow: auto;
  /deep/ .ant-tabs {
    .viewInfo {
      .ant-table-thead {
        .ant-table-align-left {
          text-align: center !important;
        }
      }
    }
    margin-top: 7px;
    .ant-tabs-bar {
      margin: 0;
      border-bottom: 1px solid #ccc;
      .ant-tabs-nav-wrap {
        .ant-tabs-ink-bar {
          display: none !important;
        }
      }
      .ant-tabs-tab {
        margin: 0;
        padding: 0 10px;
        border: 1px solid #ccc;
        font-size: 14px;
        height: 34px;
        line-height: 34px;
        border-left: 0;
        font-weight: 500;
        &:nth-child(1) {
          border-left: 1px solid #ccc;
        }
      }
      .ant-tabs-tab-active {
        border-top: 2px solid #f90 !important;
        border-bottom-color: #ccc;
        background: #ffffff;
      }
    }
  }
}
@media screen and (max-width: 575px) {
  /deep/.ant-form-item-label {
    display: block;
    margin: 0;
    padding: 0 !important;
    line-height: 1.5 !important;
    white-space: initial;
    text-align: left !important;
    color: red;
  }
  @media (max-width: 575px) {
    /deep/.ant-form-item-label label::after {
      display: revert !important;
    }
  }
}
</style>
