<!-- 市场管理 - 订单询价主组件 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend" style="position: relative">
      <div style="width: 100%; display: flex">
        <div class="leftContent" style="user-select: none; position: relative" @dragover.prevent @drop.prevent="onDrop">
          <a-upload
            v-if="
              !checkPermission('MES.MarketModule.OrderManagement.WritePoNumJjJq') &&
              this.frontDataZSupplierf.length == 1 &&
              !this.frontDataZSupplierf[0].isBlacklist
            "
            :multiple="true"
            :file-list="fileListtz"
            ref="fileRef"
            :directory="true"
            :customRequest="downloadFilesCustomRequest"
            :before-upload="beforeUpload"
            :showUploadList="false"
            @change="handleChange"
            :openFileDialogOnClick="false"
          >
            <left-table
              :columns="columns1"
              :data-source="orderListData"
              :orderListTableLoading="orderListTableLoading"
              :rowKey="'id'"
              @tableChange="handleTableChange"
              @getSelectedRowList="getSelectedRowList"
              :pagination="pagination"
              ref="orderTable"
              class="maintablestyle"
            >
            </left-table>
          </a-upload>
          <left-table
            v-else
            :columns="columns1"
            :data-source="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'id'"
            @tableChange="handleTableChange"
            @getSelectedRowList="getSelectedRowList"
            :pagination="pagination"
            ref="orderTable"
            class="maintablestyle"
          >
          </left-table>
        </div>
      </div>
      <div class="footerAction" style="user-select: none">
        <backend-action
          ref="backendAction"
          :keyArr="keyArr"
          :total="pagination.total"
          :CustNo="CustNo"
          :assignLoading="assignLoading"
          @getOrderList="getOrderList"
          @queryClick="queryClick"
          @addClick="addClick"
          @customRequest="customRequest"
          @editClick="editClick"
          @auditClick="auditClick"
          @delClick="delClick"
          @uploadPCBFileClick="uploadPCBFileClick"
        />
      </div>

      <!-- 查询弹窗 -->
      <a-modal
        title="询价查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel1"
        @ok="handleOk"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        centered
        :maskClosable="false"
        :width="400"
      >
        <query-info ref="queryInfo" />
      </a-modal>
      <!-- 新增编辑弹窗 -->
      <a-modal
        :title="type == '1' ? '新增' : '编辑'"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        centered
        destroyOnClose
        :maskClosable="false"
        :width="600"
        :footer="null"
      >
        <enter-order-info
          ref="EnterOrderInfo"
          @handleOk1="handleOk1"
          @reportHandleCancel="reportHandleCancel"
          :selectedData="selectedData"
          :type="type"
          :supList="supList"
          :frontDataZSupplierf="frontDataZSupplierf"
          :factoryList="factoryList"
          @getSupplier="getSupplier"
        />
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        title=" 确认弹窗"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="font-size: 16px" v-if="orderno">【{{ orderno }}】</span>
        <span style="font-size: 16px; margin-left: 15px">{{ message }}</span>
      </a-modal>
      <a-modal title="检查信息" :visible="dataVisible2" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back" @click="reportHandleCancel">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
        </div>
      </a-modal>
      <a-modal title="检查信息" :visible="checkvisible" @cancel="reportHandleCancel" destroyOnClose centered :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back" @click="reportHandleCancel">取消</a-button>
          <a-button key="back1" type="primary" v-if="Instructioncheck" @click="continueClick">继续</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in Instructiondata" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="Instructioncheck"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
        </div>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import axios from "axios";
import { buttonCheck } from "@/services/mkt/OrderManagement.js";
import { upLoadEnquiryFile } from "@/services/mkt/Inquiry.js";
import { checkPermission } from "@/utils/abp";
import { mktCustNov2, checkorderfile, inquirymktCustNo, inquirycheckorderfile, updateFile, factroylist } from "@/services/mkt/Inquiry.js";
import LeftTable from "@/pages/mkt/Inquiry/subassembly/LeftTable";
import BackendAction from "@/pages/mkt/Inquiry/subassembly/BackendAction";
import QueryInfo from "@/pages/mkt/Inquiry/subassembly/QueryInfo.vue";
import EnterOrderInfo from "@/pages/mkt/Inquiry/subassembly/EnterOrderInfo";
import Cookie from "js-cookie";
import { mapState } from "vuex";
import moment from "moment";
import {
  orderEnquiryPageList,
  orderAdd,
  inquiryorderAdd,
  orderUpdate,
  orderAudit,
  inquiryorderAudit,
  orderDelete,
  inquiryorderDelete,
} from "@/services/mkt/Inquiry.js";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 50,
  },
  {
    title: "订单号",
    align: "left",
    dataIndex: "orderNo",
    scopedSlots: { customRender: "orderNo" },
    ellipsis: true,
    width: 130,
    className: "userStyle",
    // sorter: (a, b) => {
    //   return a.orderNo.localeCompare(b.orderNo)
    // }
  },
  //   {
  //     title: "订单标记",
  //     key: "tag",
  //     scopedSlots: { customRender: 'tag' },
  //     align: "left",
  //     fixed:'left',
  //     ellipsis: true,
  //     width:140,
  //   },
  {
    title: "工厂",
    dataIndex: "contractFactoryName",
    ellipsis: true,
    width: 75,
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 80,
    className: "userStyle",
  },
  {
    title: "客户型号",
    dataIndex: "customerModel",
    align: "left",
    ellipsis: true,
    width: 400,
    className: "userStyle",
  },
  {
    title: "建立人",
    dataIndex: "createName",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "录入时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 160,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 70,
    ellipsis: true,
    align: 70,
  },
  {
    title: "交货数",
    dataIndex: "num",
    width: 70,
    ellipsis: true,
    align: 80,
  },
  {
    title: "交货单位",
    dataIndex: "delType",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: "客户PO",
    dataIndex: "custPo",
    width: 130,
    ellipsis: true,
    align: "left",
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    width: 60,
    ellipsis: true,
    align: "left",
  },
];
export default {
  name: "orderInquiry",
  components: { LeftTable, BackendAction, QueryInfo, EnterOrderInfo },
  inject: ["reload"],
  data() {
    return {
      fileListtz: [],
      ids: [],
      tzlength: 0,
      ind: 0,
      isFileType: false,
      confirmLoading: false,
      CustNo: "",
      spinning: false,
      pagination: {
        pageSize: 20,
        pageIndex: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      columns1,
      orderListData: [],
      dataVisible: false,
      dataVisible1: false,
      assignLoading: false,
      orderListTableLoading: false,
      type: "1",
      selectedData: {},
      orderNo: "",
      dataVisible3: false,
      message: "",
      orderno: "",
      type1: "",
      deletId: "",
      dataVisible2: false,
      checkvisible: false,
      Instructiondata: [], //指示检查
      checktype: "",
      Instructioncheck: false,
      checkData: [], // 检查数据
      check: false,
      tzpcbFileName: "",
      PcbFileDatatz: [],
      MD5Code2tz: [],
      pcbNametz: [],
      Data: {},
      keyArr: [],
      supList: [], //从后端查询的所有数据（不会改变）
      frontDataZSupplierf: [], // 供应商 100条数据的集合
      factoryList: [],
      isCtrlPressed: false,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.handleResize();
      this.getOrderList();
    });
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  mounted() {
    this.getSupplier();
    this.getfac();
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  methods: {
    checkPermission,
    onDrop(e) {
      this.tzlength = 0;
      let items = e.dataTransfer.items;
      for (let i = 0; i < items.length; i++) {
        let item = items[i];
        if (item.kind == "file") {
          let entry = item.webkitGetAsEntry();
          this.getFileFromEntryRecursively(entry);
        }
      }
    },
    getFileFromEntryRecursively(entry) {
      if (entry.isFile) {
        if (entry.fullPath.toLowerCase().indexOf(".zip") != -1 || entry.fullPath.toLowerCase().indexOf(".rar") != -1) {
          this.tzlength++;
        }
      } else {
        let reader = entry.createReader();
        reader.readEntries(
          entries => {
            entries.forEach(entry => this.getFileFromEntryRecursively(entry));
          },
          e => {
            console.log(e);
          }
        );
      }
    },
    handleChange({ fileList }, data, xhr) {
      if (this.isFileType) {
        this.fileListtz = fileList;
        this.PcbFileDatatz = [];
        this.MD5Code2tz = [];
        this.pcbNametz = [];
        var arr = [];
        const fileName = this.fileListtz[0].name;
        const lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex !== -1) {
          this.tzpcbFileName = fileName.substring(0, lastDotIndex);
        }
        arr = this.fileListtz.filter(item => {
          return item.status == "done";
        });
        for (var a = 0; a < arr.length; a++) {
          this.PcbFileDatatz.push(arr[a].response);
          this.MD5Code2tz.push(this.guid(arr[a].name, arr[a].lastModified, arr[a].size, arr[a].type));
          this.pcbNametz.push(arr[a].name);
        }
      }
    },
    beforeUpload(file) {
      this.isFileType =
        file.name.toLowerCase().indexOf(".zip") != -1 ||
        file.name.toLowerCase().indexOf(".rar") != -1 ||
        file.name.toLowerCase().indexOf(".7z") != -1;
      if (!this.isFileType) {
        this.$message.error("检测到" + file.name + "非.rar或.zip或.7z格式文件,请重新选择文件上传");
        return false;
      }
      const filesize = Number(file.size / 1024 / 1024) < 150;
      if (!filesize) {
        this.isFileType = false;
        this.$message.error("文件大小不能超过150M");
        return false;
      }
      const uploadedFileNames = this.fileListtz.map(file => file.name);
      if (uploadedFileNames.includes(file.name)) {
        this.$message.error("已存在该文件名，请不要重复上传！");
        const index = this.fileListtz.findIndex(item => item.name === file.name);
        if (index !== -1) {
          this.fileListtz.splice(index, 1); // 手动删除重复文件
        }
      }
      return true;
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    calculateFileMD5(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
          const fileContent = new Uint8Array(e.target.result); // 将 ArrayBuffer 转换为 Uint8Array
          const hash = this.$md5(fileContent); // 假设 $md5 支持二进制数据
          resolve(hash);
        };
        reader.onerror = err => {
          reject(err);
        };
        reader.readAsArrayBuffer(file); // 使用 readAsArrayBuffer 读取文件
      });
    },
    async downloadFilesCustomRequestAA(data) {
      if (this.isFileType) {
        const formData = new FormData();
        let GUID = this.guid(data.file.name, data.file.lastModified, data.file.size, data.file.type);
        formData.append("file", data.file);
        this.orderListTableLoading = true;
        await upLoadEnquiryFile(formData, this.frontDataZSupplierf[0], GUID)
          .then(res => {
            if (res.code == 1) {
              if (res.message) {
                if (confirm("" + data.file.name + ":" + res.message)) {
                  data.onSuccess(res.data);
                } else {
                  data.onError(res.message);
                }
              } else {
                data.onSuccess(res.data);
              }
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.orderListTableLoading = false;
            this.ind++;
            if (this.ind == this.tzlength) {
              this.$nextTick(() => {
                this.handleOktz();
                this.ind = 0;
                this.tzlength = 0;
              });
            }
          });
      }
    },
    async downloadFilesCustomRequest(data) {
      if (this.isFileType) {
        this.orderListTableLoading = true;
        let GUID;
        let shardSize;
        let shardCount;
        var partFile;
        var pecent;
        let next;
        const str = data.file.name;
        const parser = new DOMParser();
        const doc = parser.parseFromString(str, "text/html");
        const parsedText = doc.body.textContent;
        const replacedText = parsedText.replace(/\s+/g, " ");
        let size = data.file.size;
        GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
        let FileMD5 = await this.calculateFileMD5(data.file);
        if (size / 1024 / 1024 > 50) {
          shardSize = 1024 * 1024 * 2;
        } else {
          shardSize = 1024 * 1024;
        }
        shardCount = Math.ceil(size / shardSize); //总片数
        const formData = new FormData();
        formData.append("CustNo", this.frontDataZSupplierf[0]);
        formData.append("FileSeg", ""); //文件
        formData.append("FileMD5", FileMD5); // md5
        formData.append("MD5Code", GUID); // md5
        formData.append("FileName", replacedText); //文件名
        formData.append("startpos", 0); //当前开始长度
        formData.append("FileSize", size); //文件尺寸
        let params = {
          custNo: this.frontDataZSupplierf[0],
          fileName: data.file.name,
          MD5Code: GUID,
        };
        await inquirycheckorderfile(params).then(res => {
          if (res.code) {
            if (res.message) {
              if (confirm("" + data.file.name + ":" + res.message)) {
                next = true;
              }
            } else {
              next = true;
            }
          } else {
            next = false;
            data.onError(res.message);
            this.$message.error(replacedText + res.message);
            this.tzlength = 0;
            this.fileListtz = [];
            this.orderListTableLoading = false;
          }
        });
        if (next == true) {
          for (var i = 0; i < shardCount; i++) {
            const start = i * shardSize;
            const end = Math.min(size, start + shardSize);
            formData.set("FileSeg", data.file.slice(start, end));
            formData.set("startpos", i * shardSize);
            let headers = {};
            headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
            headers["Content-Type"] = "text/html;charset=UTF-8";
            await axios({
              url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
              method: "post",
              data: formData,
              headers: headers, // 请求头
            }).then(res => {
              if (res.code == 1) {
                if (i == shardCount - 1) {
                  data.onSuccess(res.data);
                  this.ind++;
                  if (this.ind == this.tzlength) {
                    this.$nextTick(() => {
                      this.handleOktz();
                      this.ind = 0;
                      this.tzlength = 0;
                    });
                  }
                }
              } else {
                data.onError(res.message);
                this.$message.error(res.message);
                this.orderListTableLoading = false;
                i = shardCount;
              }
            });
          }
        }
      }
    },
    handleOktz() {
      this.orderListTableLoading = true;
      let arr = [];
      for (var i = 0; i < this.PcbFileDatatz.length; i++) {
        let name = this.pcbNametz[i].split(".");
        let rname = this.pcbNametz[i];
        arr.push({
          custNo: this.frontDataZSupplierf[0], // 客户代码
          pcbFileName: rname.split("." + name[name.length - 1])[0], // 文件名
          PcbFilePath: this.PcbFileDatatz[i].split(",")[0], // 文件地址
          fIleMD5Code: this.PcbFileDatatz[i].split(",")[2], // 文件MD5
          FIleMD5Code: this.MD5Code2tz[i],
        });
      }
      inquiryorderAdd(arr)
        .then(res => {
          if (res.code) {
            //this.$message.success('新增成功')
            this.fileListtz = [];
            this.getOrderList();
          } else {
            //this.$message.error(res.$message)
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    handleResize() {
      var maintablestyle =
        document.getElementsByClassName("maintablestyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftContent = document.getElementsByClassName("leftContent")[0];
      var maintablestyle2 = document.getElementsByClassName("maintablestyle")[0];
      if (window.innerWidth < 1921) {
        leftContent.style.height = window.innerHeight - 148 + "px";
      } else {
        leftContent.style.height = "762px";
      }
      if (maintablestyle && this.orderListData.length != 0) {
        maintablestyle.style.height = window.innerHeight - 189 + "px";
      } else {
        maintablestyle.style.height = 0;
      }
      if (maintablestyle2 && this.orderListData.length == 0) {
        maintablestyle2.style.height = window.innerHeight - 155 + "px";
      } else {
        maintablestyle2.style.height = 0;
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.backendAction.nums * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "65" && this.isCtrlPressed && checkPermission("MES.MarketModule.OrderManagement.InquiryAdd")) {
        e.preventDefault();
        this.addClick();
        this.reportHandleCancel();
        this.dataVisible1 = true;
        this.isCtrlPressed = false;
      } else if (e.keyCode == "67" && this.dataVisible3) {
        e.preventDefault();
        this.reportHandleCancel();
      } else if (e.keyCode == "70" && this.isCtrlPressed) {
        e.preventDefault();
        this.reportHandleCancel();
        this.dataVisible = true;
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        e.preventDefault();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && this.dataVisible3) {
        this.handleOk3();
        e.preventDefault();
        this.isCtrlPressed = false;
      }
      //  else if(e.keyCode == '13'){
      //   e.preventDefault()
      //   this.isCtrlPressed = false;
      //  }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    getfac() {
      factroylist().then(res => {
        if (res.code) {
          this.factoryList = res.data;
        }
      });
    },
    getSupplier(fac) {
      let factory = "";
      if (fac) {
        factory = fac;
        this.$refs.EnterOrderInfo.enterOrderForm.custNo = "";
      } else if (!fac && (this.user.factoryId == 58 || this.user.factoryId == 59 || this.user.factoryId == 67)) {
        factory = this.user.factoryId;
      }
      mktCustNov2(factory).then(res => {
        if (res.code) {
          let that = this;
          that.supList = res.data;
          that.frontDataZSupplierf = res.data.slice(0, 20);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取订单
    getOrderList(queryData) {
      let params = {
        // ...this.pagination
        PageIndex: this.pagination.pageIndex,
        PageSize: this.pagination.pageSize,
      };
      var obj = Object.assign(params, queryData);
      this.orderListTableLoading = true;
      obj.PcbFileName = obj.PcbFileName ? obj.PcbFileName.replace(/\s+/g, " ").trim() : "";
      orderEnquiryPageList(obj)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            if (this.orderListData.length && (obj.OrderNo || obj.custNo || obj.PcbFileName)) {
              this.$refs.orderTable.proOrderId = this.orderListData[0].id;
              this.$refs.orderTable.selectedRowList = [this.orderListData[0].id];
              this.keyArr = [this.orderListData[0].id];
              this.$refs.orderTable.selectedRows.orderNo = this.orderListData[0].orderNo;
            }
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
            this.orderListData.forEach(item => {
              if (item.isReOrder == 0) {
                item.isReOrder = false;
              } else {
                item.isReOrder = true;
              }
            });
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },

    // 订单表变化change
    handleTableChange(pagination, filters, sorter) {
      this.pagination.pageIndex = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      // localStorage.setItem('pageCurrent',this.pagination.pageIndex)
      // localStorage.setItem('pageSize',pagination.pageSize)
      // this.pageStat=false
      // localStorage.removeItem('stat')
      if (JSON.stringify(this.Data) != "{}") {
        this.getOrderList(this.Data);
      } else {
        this.getOrderList();
      }
    },
    // handleTableChange(pagination) {
    //   this.pagination.current=pagination.current
    //   this.getOrderList()
    // },
    reportHandleCancel1() {
      this.dataVisible = false; // 查询
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible3 = false;
      this.dataVisible1 = false; // 新增/编辑
      this.dataVisible2 = false;
      this.checkvisible = false;
      if (this.$refs.EnterOrderInfo) {
        if (this.type == "1") {
          this.$refs.EnterOrderInfo.enterOrderForm.custNo = "";
          this.$refs.EnterOrderInfo.enterOrderForm.num = "";
          this.$refs.EnterOrderInfo.enterOrderForm.delType = "";
          this.$refs.EnterOrderInfo.enterOrderForm.custPo = "";
          this.$refs.EnterOrderInfo.enterOrderForm.orderSource = "";
          this.$refs.EnterOrderInfo.enterOrderForm.pcbFileName = "";
          this.$refs.EnterOrderInfo.enterOrderForm.isJiaji = false;
          this.$refs.EnterOrderInfo.enterOrderForm.PcbFilePath = "";
          this.$refs.EnterOrderInfo.enterOrderForm.isNeedOrderOff = false;
          this.$refs.EnterOrderInfo.enterOrderForm.isCreateProOrderNo = false;
        } else {
          this.$refs.EnterOrderInfo.enterOrderForm.custNo = this.$refs.orderTable.selectedRowsData.custNo;
          this.$refs.EnterOrderInfo.enterOrderForm.num = this.$refs.orderTable.selectedRowsData.num;
          this.$refs.EnterOrderInfo.enterOrderForm.delType = this.$refs.orderTable.selectedRowsData.delType;
          this.$refs.EnterOrderInfo.enterOrderForm.custPo = this.$refs.orderTable.selectedRowsData.custPo;
          this.$refs.EnterOrderInfo.enterOrderForm.orderSource = this.$refs.orderTable.selectedRowsData.orderSource;
          this.$refs.EnterOrderInfo.enterOrderForm.pcbFileName = this.$refs.orderTable.selectedRowsData.pcbFileName;
          this.$refs.EnterOrderInfo.enterOrderForm.isJiaji = this.$refs.orderTable.selectedRowsData.isJiaji;
          this.$refs.EnterOrderInfo.enterOrderForm.isNeedOrderOff = this.$refs.orderTable.selectedRowsData.isNeedOrderOff;
          this.$refs.EnterOrderInfo.enterOrderForm.isCreateProOrderNo = this.$refs.orderTable.selectedRowsData.isCreateProOrderNo;
          this.$refs.EnterOrderInfo.enterOrderForm.PcbFilePath = this.$refs.orderTable.selectedRowsData.PcbFilePath;
        }
      }
    },
    // 查询
    queryClick() {
      this.dataVisible = true;
    },
    handleOk() {
      let params = this.$refs.queryInfo.form;
      this.Data = this.$refs.queryInfo.form;
      var arr1 = params.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      params.OrderNo = arr1.join("");
      var arr2 = params.custNo.split("");
      if (arr2.length > 10) {
        arr2 = arr2.slice(0, 10);
      }
      params.custNo = arr2.join("");
      var arr3 = params.PcbFileName.split("");
      if (arr3.length > 100) {
        arr3 = arr3.slice(0, 100);
      }
      params.PcbFileName = arr3.join("");
      this.pagination.pageIndex = 1;
      this.dataVisible = false;
      this.getOrderList(params);
    },
    // 新增
    addClick() {
      this.type = "1";
      this.dataVisible1 = true;
    },
    handleOk1(type) {
      const form = this.$refs.EnterOrderInfo.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          let params = this.$refs.EnterOrderInfo.enterOrderForm;
          if (params.num == "") {
            params.num = null;
          } else {
            params.num = Number(params.num);
          }
          let arr = [];
          if (!checkPermission("MES.MarketModule.OrderManagement.WritePoNumJjJq")) {
            for (var i = 0; i < params.PcbFileData.length; i++) {
              let name = this.$refs.EnterOrderInfo.pcbName[i].split(".");
              let rname = this.$refs.EnterOrderInfo.pcbName[i];
              arr.push({
                custNo: params.custNo, // 客户代码
                num: params.num, // 交货数
                delType: params.delType, // 交货单位
                custPo: params.custPo, // 客户PO
                orderSource: params.orderSource, // 订单来源
                pcbFileName: rname.split("." + name[name.length - 1])[0], // 文件名
                isJiaji: params.isJiaji, // 加急
                isNeedOrderOff: params.isNeedOrderOff,
                isCreateProOrderNo: params.isCreateProOrderNo,
                PcbFilePath: params.PcbFileData[i].split(",")[0], // 文件地址
                fileExtension: params.PcbFileData[i].split(",")[params.PcbFileData[i].split(",").length - 1], //文件名后缀
                proOrderNo: params.proOrderNo,
                terminalCust: params.terminalCust,
                FIleMD5Code: this.$refs.EnterOrderInfo.MD5Code2[i],
                factoryId: this.$refs.EnterOrderInfo.TradeType,
                // 'DeliveryDateStr':params.DeliveryDateStr,
              });
            }
          } else {
            for (var a = 0; a < params.PcbFileData.length; a++) {
              let name = this.$refs.EnterOrderInfo.pcbName[a].split(".");
              let rname = this.$refs.EnterOrderInfo.pcbName[a];
              arr.push({
                custNo: params.custNo, // 客户代码
                num: params.num, // 交货数
                delType: params.delType, // 交货单位
                custPo: params.custPo, // 客户PO
                orderSource: params.orderSource, // 订单来源
                pcbFileName: rname.split("." + name[name.length - 1])[0], // 文件名
                isJiaji: params.isJiaji, // 加急
                isNeedOrderOff: params.isNeedOrderOff,
                isCreateProOrderNo: params.isCreateProOrderNo,
                PcbFilePath: params.PcbFileData[a].split(",")[0], // 文件地址
                fileExtension: params.PcbFileData[a].split(",")[params.PcbFileData[a].split(",").length - 1], //文件名后缀
                proOrderNo: params.proOrderNo,
                terminalCust: params.terminalCust,
                customerModel: params.pcbFileName,
                FIleMD5Code: this.$refs.EnterOrderInfo.MD5Code2[a],
                factoryId: this.$refs.EnterOrderInfo.TradeType,
                // 'DeliveryDateStr':params.DeliveryDateStr,
              });
            }
          }
          if (this.type == "1") {
            this.spinning = true;
            inquiryorderAdd(arr)
              .then(res => {
                if (res.code) {
                  let arr = res.data;
                  if (type == "ok") {
                    inquiryorderAudit(arr).then(res => {
                      if (res.code) {
                        this.$message.success("确认成功");
                        this.pagination.pageIndex = 1;
                        this.getOrderList();
                      } else {
                        if (res.data && res.data.length) {
                          this.checkData = res.data;
                          this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                          this.dataVisible2 = true;
                        } else {
                          this.$message.error(res.message);
                        }
                      }
                    });
                  } else {
                    this.$message.success("成功");
                    this.getOrderList();
                  }
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.spinning = false;
              });
          } else {
            params.id = this.$refs.orderTable.proOrderId;
            params.customerModel = params.pcbFileName;
            if (!params.PcbFileData.length) {
              params.PcbFilePath = this.selectedData.pcbFilePath;
              params.pcbFileName = this.selectedData.pcbFileName;
            } else {
              params.PcbFilePath = params.PcbFileData[0].split(",")[0];
              params.pcbFileName = params.PcbFileData[0].split(",")[1];
            }
            this.spinning = true;
            orderUpdate(params)
              .then(res => {
                if (res.code) {
                  let arr = [res.data];
                  if (type == "ok") {
                    inquiryorderAudit(arr).then(res => {
                      if (res.code) {
                        this.$message.success("确认成功");
                        this.pagination.pageIndex = 1;
                        this.getOrderList();
                      } else {
                        if (res.data && res.data.length) {
                          this.checkData = res.data;
                          this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                          this.dataVisible2 = true;
                        } else {
                          this.$message.error(res.message);
                        }
                      }
                    });
                  } else {
                    this.$message.success("成功");
                    this.getOrderList();
                    this.reload();
                  }
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.spinning = false;
              });
          }
          this.dataVisible1 = false;
        }
      });
    },
    // 编辑
    editClick() {
      let id = this.$refs.orderTable.proOrderId;
      if (!id) {
        this.$message.warning("请选择订单");
        return;
      }
      this.selectedData = this.$refs.orderTable.selectedRowsData;
      this.orderNo = this.selectedData.orderNo;
      this.type = "2";
      this.dataVisible1 = true;
    },
    // 订单确认
    // auditClick(){
    //   let id = this.$refs.orderTable.proOrderId
    //   if(!id){
    //     this.$message.warning('请选择订单')
    //     return
    //   }
    //   orderAudit(id).then(res=>{
    //     if(res.code){
    //       this.$message.success('确认成功')
    //       this.getOrderList()
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    // 订单确认添加弹窗
    auditClick() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      var arr = this.$refs.orderTable.selectedRowList;
      if (arr.length > 1) {
        this.message = "请确认批量订单确认吗？";
        this.orderno = "";
      } else {
        this.orderno = this.$refs.orderTable.selectedRows.orderNo;
        this.message = "订单确认吗？";
      }
      this.dataVisible3 = true;
      this.type1 = "3";
    },

    // 删除型号
    delClick() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      var list = [];
      var arr = this.$refs.orderTable.selectedRows;
      for (var i = 0; i < arr.length; i++) {
        list.push(arr[i].orderNo);
      }
      if (list.length > 1) {
        this.message = "请确认批量删除型号吗？";
        this.orderno = "";
      } else {
        this.orderno = list[0];
        this.message = "确认删除型号吗？";
      }
      this.dataVisible3 = true;
      this.type1 = "1";
    },
    handleOk3() {
      var arr = this.$refs.orderTable.selectedRowList;
      // 删除
      if (this.type1 == "1") {
        inquiryorderDelete(arr).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.pagination.pageIndex = 1;
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      // 确认
      if (this.type1 == "3") {
        inquiryorderAudit(arr).then(res => {
          if (res.code) {
            this.$message.success("确认成功");
            this.pagination.pageIndex = 1;
            this.getOrderList();
          } else {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              this.dataVisible2 = true;
            } else {
              this.$message.error(res.message);
            }
          }
        });
      }
      this.dataVisible3 = false;
      this.$refs.orderTable.selectedRows = [];
      this.$refs.orderTable.selectedRowList = [];
    },
    getSelectedRowList(key) {
      this.keyArr = key;
    },
    //分段上传MD5
    async customRequest(data) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("CustNo", this.$refs.orderTable.selectedRows.custNo);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      this.orderListTableLoading = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              let params = {
                id: this.$refs.orderTable.selectedRowList[0],
              };
              params.PcbFilePath = res.data.split(",")[0];
              params.pcbFileName = replacedText;
              updateFile(params)
                .then(res => {
                  if (res.code) {
                    this.$message.success("上传成功");
                    this.getOrderList();
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.orderListTableLoading = false;
                });
            }
          } else {
            this.orderListTableLoading = false;
            data.onError(res.message);
            i = shardCount;
          }
        });
      }
    },
    uploadPCBFileClick() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择需要文件替换的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowList.length > 1) {
        this.$message.warning("只能选择一个订单进行文件替换");
        return;
      }
      buttonCheck(this.$refs.orderTable.selectedRowList, "OrderEnquiryUpdateFile").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.Instructiondata = res.data;
            this.Instructioncheck = this.Instructiondata.findIndex(v => v.error == "1") < 0;
            this.checkType = "wjth";
            this.checkvisible = true;
          } else {
            this.$refs.backendAction.clickUpload();
            this.CustNo = this.$refs.orderTable.selectedRows.custNo;
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    continueClick() {
      if (this.checktype == "wjth") {
        this.$refs.backendAction.clickUpload();
        this.CustNo = this.$refs.orderTable.selectedRows.custNo;
      }
      this.checkvisible = false;
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-upload {
  width: 100%;
}
/deep/.ant-pagination.mini .ant-pagination-total-text,
.ant-pagination.mini .ant-pagination-simple-pager {
  height: 24px;
  line-height: 24px;
  font-size: 13px;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/ .ant-table-pagination.ant-pagination {
  float: left;
  margin: 14px 0 0 10px;
}

/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-table-selection-column {
  padding: 7px 0 !important;
  display: inline-block;
  width: 25px !important;
}
/deep/.ant-modal-body {
  padding: 24px 0 !important;
  color: #000000;
}
.projectBackend {
  /deep/.userStyle {
    user-select: none !important;
  }
  background: #ffffff;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  /deep/.leftContent {
    .ant-table-body {
      .ant-table-fixed {
        width: 1650px !important;
      }
    }
    .tabRightClikBox {
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }

  /deep/ .rightContent {
    width: 41%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .centerTable {
      .peopleTag {
        position: absolute;
        font-size: 12px;
        font-weight: 600;
        left: 0;
        padding: 0 2px;
      }
      .ant-table-body {
        .ant-table-tbody {
          .ant-table-row:first-child {
            .anticon {
              display: none;
            }
          }
        }
        max-height: 702px !important;
      }
      width: 50%;
      height: 762px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
    }
    .rightTable {
      .ant-table-body {
        max-height: 715px !important;
      }
      width: 50%;
      height: 762px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
      .jobNote {
        /deep/ .ant-table-wrapper {
          user-select: none;
          height: 444px;
        }
        .minTable {
          min-height: 200px;
        }
        .note {
          height: 300px;
          overflow-y: auto;
        }
      }
      .peopleTag {
        margin: 0;
        padding: 0;
        width: 24px;
        border-radius: 12px;
        background: #2d221d;
        border-color: #2d221d;
        color: #ff9900;
        text-align: center;
        margin-left: 2px;
      }
    }
  }
  .footerAction {
    width: 100%;
    height: 54px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container {
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }
  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
      .eqBackground {
        background: #ffff00;
      }
      .statuBackground {
        background: #b0e0e6;
      }
      .backUserBackground {
        background: #a7a2c9;
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    /deep/.userStyle {
      user-select: none !important;
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #d6d6d6;
    .imgTable {
      img {
        width: 100px;
        height: 50px;
      }
    }
    p {
      height: 100%;
      line-height: 35px;
      font-weight: 700;
      margin: 0;
    }
    .displayFlag {
      display: none;
    }
  }
}
</style>
