<template>
  <div ref="sb">
     <a-modal
    :title="form.id? '编辑' : '新增'"
    :width="640"
    centered
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="企业名称" ref="corporateName_" prop="corporateName_">
          <a-input style="font-weight: 500;"
            v-model="form.corporateName_"
            placeholder="企业名称"
          />
        </a-form-model-item>
        <a-form-model-item  label="应用领域" ref="customerIndustry_" prop="customerIndustry_">
          <a-select v-model="form.customerIndustry_"    placeholder="应用领域" :getPopupContainer="()=>this.$refs.sb" >
            <a-select-option value="">
              请选择
            </a-select-option>
            <a-select-option value="医疗器械">
              医疗器械
            </a-select-option>
            <a-select-option value="通讯">
              通讯
            </a-select-option>
            <a-select-option value="汽车">
              汽车
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
  </div>
 
</template>

<script>
import { addCust,progressNum } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
    compileApply:{
      type: String,
      default () {
        return ''
      }
    },
    message:{
      type: String,
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {},
      rules: {
        corporateName_: [
          { required: true, message: "名称必须填写", trigger: "blur" },
        ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
      // if(this.compileApply=='1'){
        this.visible = true;
        this.model = model
        this.form = {
          id:model.id,
          corporateName_: model.corporateName_,
          customerIndustry_: model.customerIndustry_,
        };
      // }else {
      //   this.$message.info(this.message)
      // }

    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          let params = {
              id: this.model.id,
              pGuid_: this.suppId,
              corporateName_: this.form.corporateName_,
              customerIndustry_: this.form.customerIndustry_,
          }
          addCust(params)
            .then((res) => {
                this.visible = false;
                form.resetFields();
                this.$message.info("操作成功");
                this.$emit("ok");
              progressNum(this.suppId).then(res=>{
                console.log(res)
                if(res.code!==1){
                  this.$message.info(res.message)
                }
              })
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
  /deep/.ant-modal-title{
    font-weight: 500;
  }
  /deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  }
/deep/.ant-form-item{
      margin-bottom: 5px;
  }
</style>
