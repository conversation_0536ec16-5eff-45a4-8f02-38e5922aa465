<!-- 用户管理 - 问客管理  -->
<template>
  <a-spin :spinning="spinning">
    <div class="projecteq">
      <a-tabs :activeKey="activeKey" @tabClick="tabClick">
        <a-tab-pane key="0" tab="待回复"> </a-tab-pane>
        <a-tab-pane key="1" tab="已回复"></a-tab-pane>
      </a-tabs>
      <div
        class="leftContent"
        style="width: 100%; border: 1px solid rgb(233, 233, 240); border-right: 2px solid rgb(233, 233, 240); position: relative; user-select: normal"
        ref="tableWrapper"
      >
        <a-table
          :columns="columns1"
          :pagination="pagination"
          :scroll="{ y: 738, x: 1200 }"
          :customRow="onClickRow"
          :rowClassName="isclickcolor"
          :dataSource="eqdatasource"
          @change="handleTableChange"
          rowKey="proOrderId"
          class="leftstyle"
        >
          <span slot="num" slot-scope="text, record, index">
            {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
          </span>
          <template slot="orderNo" slot-scope="text, record">
            <span>{{ record.orderNo }}</span
            >&nbsp;
            <span class="tagNum" style="display: inline-block; height: 19px">
              <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
                <span
                  class="noCopy"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    color: #ff9900;
                    padding: 0;
                    margin-left: -10px;
                    display: inline-block;
                    height: 19px;
                    width: 18px;
                    user-select: none;
                  "
                  ><a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
                <span
                  class="noCopy"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    color: #ff9900;
                    padding: 0;
                    margin-left: -10px;
                    display: inline-block;
                    height: 19px;
                    width: 18px;
                    user-select: none;
                  "
                  ><a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
              </a-tooltip>
              <a-tooltip title="加急" v-else-if="record.isJiaji">
                <span
                  class="noCopy"
                  style="
                    font-size: 14px;
                    font-weight: 500;
                    color: #ff9900;
                    padding: 0;
                    margin-left: -10px;
                    display: inline-block;
                    height: 19px;
                    width: 18px;
                    user-select: none;
                  "
                  ><a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
              </a-tooltip>
              <a-tooltip title="新客户" v-if="record.isNewCust">
                <a-tag
                  class="noCopy"
                  style="
                    font-size: 12px;
                    background: #428bca;
                    color: white;
                    padding: 0 2px;
                    margin: 0;
                    margin-right: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid #428bca;
                  "
                >
                  新
                </a-tag>
              </a-tooltip>
              <a-tag
                v-if="record.ka"
                style="
                  font-size: 10px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                "
              >
                KA
              </a-tag>
              <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.isReOrder == 1">
                <a-tag
                  style="
                    font-size: 12px;
                    background: #428bca;
                    color: white;
                    padding: 0 2px;
                    margin: 0;
                    margin-right: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid #428bca;
                  "
                >
                  改
                </a-tag>
              </a-tooltip>
              <a-tag
                class="noCopy"
                v-if="record.pauseCancelState == 2"
                color="#2D221D"
                style="
                  font-size: 12px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                "
              >
                取消
              </a-tag>
              <a-tag
                class="noCopy"
                v-if="record.pauseCancelState == 3"
                color="#2D221D"
                style="
                  font-size: 12px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                "
              >
                暂停
              </a-tag>
              <a-tag
                v-if="record.pauseCancelState == 4"
                color="#2D221D"
                class="noCopy"
                style="
                  font-size: 12px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                "
              >
                暂停取消
              </a-tag>
              <a-tag
                class="noCopy"
                v-if="record.isJunG"
                color="#2D221D"
                style="
                  font-size: 12px;
                  background: #428bca;
                  color: white;
                  padding: 0 2px;
                  margin: 0;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid #428bca;
                "
              >
                {{ record.joinFactoryId == 70 ? "J" : "军" }}
              </a-tag>
            </span>
          </template>
          <template slot="action" slot-scope="text, record">
            <a @click="deexportBtn(record)" style="color: #428bca">EQ下载</a>
            <a-divider type="vertical" />
            <a @click="deUploadattachments(record)" style="color: #428bca">EQ回复</a>/<a @click="deLeaveareserve(record)" style="color: #428bca"
              >留底下载</a
            >
          </template>
        </a-table>
        <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
        </a-menu>
      </div>
      <div class="footerAction" style="user-select: none; margin-left: -2px">
        <div v-show="!buttonsmenu">
          <a-button type="primary" class="box" @click="queryClick"> 查询(F) </a-button>
          <a-button type="primary" class="box" @click="EqClick"> 回复 </a-button>
        </div>
        <div v-show="buttonsmenu">
          <a-dropdown>
            <a-button type="primary" class="box1" @click.prevent> 按钮菜单栏 </a-button>
            <template #overlay>
              <a-menu class="tabRightClikBox1">
                <a-menu-item @click="queryClick">查询(F)</a-menu-item>
                <a-menu-item @click="EqClick">回复</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-upload
            name="file"
            ref="fileRef7"
            :customRequest="httpRequest7"
            :file-list="fileList7"
            :show-upload-list="false"
            :maxCount="1"
            @change="handleChange7"
            :before-upload="beforeUpload"
          >
          </a-upload>
        </div>
      </div>
    </div>
    <!-- 查询弹窗 -->
    <a-modal
      title="订单查询"
      :visible="dataVisible"
      @cancel="reportHandleCancel1"
      @ok="handleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form>
        <a-form-item label="生产编号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input v-model="formdata.OrderNo" placeholder="请输入生产编号" allowClear autoFocus> </a-input>
        </a-form-item>
        <a-form-model-item label="客户型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input v-model="formdata.customerModel" placeholder="请输入客户型号" allowClear />
        </a-form-model-item>
        <a-form-item label="下单时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-range-picker v-model="timeValue" @change="onChange" />
        </a-form-item>
      </a-form>
    </a-modal>
    <!--上传附件是否弹窗框-->
    <a-modal
      title="上传附件"
      :visible="dataVisibleileFile"
      @cancel="reportHandleCancel1"
      @ok="handleOkFile"
      ok-text="确定"
      cancel-text="取消"
      destroyOnClose
      centered
      :maskClosable="false"
      :width="400"
    >
      <a-form-model :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="交期">
          <a-date-picker format="YYYY-MM-DD" placeholder="请选择交期" @change="TimeChange" v-model="delDate" style="width: 235px"> </a-date-picker>
        </a-form-model-item>
        <a-upload
          :multiple="false"
          :file-list="fileList7"
          :customRequest="httpRequest7"
          :show-upload-list="true"
          @change="handleChange7"
          :before-upload="beforeUpload"
        >
          <a-button> <a-icon type="upload" />上传文件 </a-button>
        </a-upload>
      </a-form-model>
    </a-modal>
  </a-spin>
</template>
<script>
import axios from "axios";
import qs from "querystring";
import { mapState } from "vuex";
import {
  eqmanagementlistbycustno,
  exporteQReportbycustppe,
  downeQAttachmentbycust,
  iseQRead,
  exporteQReportcust,
  downeQAttachmentcust,
} from "@/services/usermanagement/index.js";
import { timestampVerifyCode, upeQAttachment, exportEQReport, downeQAttachment, exportEQReportv2, exportEQReportv3 } from "@/services/projectMake";
const columns1 = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "生产编号",
    align: "left",
    dataIndex: "orderNo",
    ellipsis: true,
    scopedSlots: { customRender: "orderNo" },
    width: 160,
  },
  {
    title: "客户型号",
    align: "left",
    dataIndex: "customerModel",
    ellipsis: true,
    width: 150,
  },
  {
    title: "下单时间",
    align: "left",
    dataIndex: "createTime",
    ellipsis: true,
    width: 140,
    sorter: (a, b) => {
      return a.createTime.localeCompare(b.createTime);
    },
  },
  {
    title: "订单交期",
    align: "left",
    ellipsis: true,
    dataIndex: "deliveryDate",
    width: 85,
  },
  {
    title: "类型",
    dataIndex: "isReOrder",
    customRender: (text, record, index) =>
      `${record.isReOrder == 0 ? "新单" : record.isReOrder == 1 ? "返单" : record.isReOrder == 2 ? "返单更改" : ""}`,
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "状态",
    align: "left",
    ellipsis: true,
    dataIndex: "statusType",
    width: 85,
  },
  {
    title: "查看",
    align: "center",
    ellipsis: true,
    customRender: (text, record, index) => `${record.isEQRead == 1 ? "已阅" : ""}`,
    width: 40,
  },
  {
    title: "次数",
    align: "left",
    dataIndex: "eqNumber",
    ellipsis: true,
    width: 45,
  },
  {
    title: "提交时间",
    align: "left",
    dataIndex: "eqInputDate",
    ellipsis: true,
    width: 130,
    sorter: (a, b) => {
      return a.eqInputDate.localeCompare(b.eqInputDate);
    },
  },
  {
    title: "发出时间",
    align: "left",
    dataIndex: "eqSendDate",
    ellipsis: true,
    width: 130,
    sorter: (a, b) => {
      return a.eqSendDate.localeCompare(b.eqSendDate);
    },
  },
  {
    title: "来源",
    width: 100,
    ellipsis: true,
    dataIndex: "eqSourceStr",
    align: "left",
  },
  // {
  //   title: "审核人",
  //   width: 50,
  //   ellipsis: true,
  //   dataIndex: "eqStartName",
  //   align: "left",
  // },

  // {
  //   title: "联系人",
  //   width: 80,
  //   ellipsis: true,
  //   dataIndex: "eqContactPerson",
  //   align: "left",
  // },

  {
    title: "工程师电话",
    width: 110,
    ellipsis: true,
    dataIndex: "eqContactPhone",
    align: "left",
  },
  // {
  //   title: "业务员",
  //   width: 65,
  //   ellipsis: true,
  //   dataIndex: "businessPerson",
  //   align: "left",
  // },
  // {
  //   title: "业务员电话",
  //   width: 110,
  //   ellipsis: true,
  //   dataIndex: "businessPhone",
  //   align: "left",
  // },
];
export default {
  name: "projectEQ",
  data() {
    return {
      fileList7: [],
      timeValue: undefined,
      activeKey: "0",
      selectdata: {},
      formdata: {},
      dataVisible: false,
      eqdatasource: [],
      spinning: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      showText: false,
      id: "",
      columns1,
      user: {},
      buttonsmenu: false,
      isCtrlPressed: false,
      dataVisibleileFile: false,
      delDate: null,
      EQFileName: "",
      uppath: "",
      eqbutton: 0,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        isCtrlPressed: false,
        size: "",
        simple: false,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  computed: {
    ...mapState("account", ["userinfo"]),
  },
  created() {
    this.deexportBtn = this.debounce(this.exportBtn, 500); //防抖
    this.deUploadattachments = this.debounce(this.Uploadattachments, 500);
    this.deLeaveareserve = this.debounce(this.Leaveareserve, 500);
    if (this.userinfo) {
      this.user = this.userinfo;
    } else {
      this.user = JSON.parse(localStorage.getItem("UserInformation"));
    }
    this.eqbutton = this.user.custEqManagement;
    if (this.user.custEqManagement == 1) {
      if (this.columns1.length < 14) {
        this.columns1.splice(13, 0, {
          title: "操作",
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: "action" },
          align: "center",
        });
      }
    } else {
      this.columns1.splice(13, 1);
    }
    this.$nextTick(() => {
      this.handleResize();
      this.geteqdata();
    });
  },
  mounted() {
    document.title = "EMS | 问客管理";
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    onChange(date, dateString) {
      this.formdata.StartTime = dateString[0];
      this.formdata.EndTime = dateString[1];
    },
    tabClick(key) {
      this.activeKey = key;
      this.geteqdata();
    },
    TimeChange(value, dateString) {
      this.delDate = dateString;
    },
    handleOkFile() {
      this.dataVisibleileFile = false;
      this.upeQAttachment();
    },
    Leaveareserve(record) {
      let params = {
        orderNo: record.orderNo,
        joinFactoryId: record.joinFactoryId,
        businessOrderNo: record.businessOrderNo,
        eqSource: Number(record.eqSource),
      };
      downeQAttachmentcust(params).then(res => {
        if (res.code) {
          if (res.data != "") {
            let path = res.data.split(",");
            path.forEach(item => {
              this.downfile(item);
            });
          } else {
            this.$message.error("暂无文件");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    exportBtn(record) {
      let JoinFactoryId = record.joinFactoryId;
      let eQSource = record.eqSource;
      let type = 1;
      exportEQReportv3(JoinFactoryId, record.orderNo, type, eQSource, record.businessOrderNo).then(res => {
        if (res.code) {
          this.downfile(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
      // if(JoinFactoryId == 12){
      //   exportEQReportv3(JoinFactoryId,record.orderNo,type,eQSource,record.businessOrderNo).then(res=>{
      //     if(res.code){
      //       this.downfile(res.data,res.message)
      //     }else{
      //       this.$message.error(res.message)
      //     }
      //   })
      // }else{
      //   exportEQReportv2(JoinFactoryId,record.orderNo,type,eQSource,record.businessOrderNo).then(res=>{
      //   if(res.code){
      //     this.downfile(res.data,res.message)
      //     downeQAttachmentbycust(JoinFactoryId,record.orderNo,record.businessOrderNo).then(res=>{
      //       if(res.code){
      //         if(res.data){
      //           let path = res.data.split(',')
      //           path.forEach(item => {
      //             this.downfile(item)
      //           });
      //         }
      //       }else{
      //          this.$message.error(res.message)
      //       }
      //     })
      //   }else{
      //     this.$message.error(res.message)
      //   }
      // })
      // }
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    downfile(val, name) {
      const urlObj = new URL(val);
      const path = urlObj.pathname;
      const fileName = path.substring(path.lastIndexOf("/") + 1);
      let fileNameWithoutQuery = "";
      if (name) {
        fileNameWithoutQuery = name;
      } else {
        fileNameWithoutQuery = decodeURI(fileName.split("?")[0]);
      }
      const xhr = new XMLHttpRequest();
      xhr.open("GET", val, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = fileNameWithoutQuery;
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      };
      xhr.send();
    },
    Uploadattachments() {
      this.fileList7 = [];
      this.$refs.fileRef7.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
      // if (this.selectdata.eqNumber > 1 || this.selectdata.para4IntDelivery<=3) {
      //  this.dataVisibleileFile = true
      //  this.delDate = this.selectdata.deliveryDate;
      // } else {
      //   this.$refs.fileRef7.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent('click'))
      // }
    },
    //上传附件成功
    upeQAttachment() {
      let params = {
        path: this.uppath,
        eqSource: Number(this.selectdata.eqSource),
        orderNo: this.selectdata.orderNo,
        businessOrderNo: this.selectdata.businessOrderNo,
        joinFactoryId: this.selectdata.joinFactoryId,
        fileName: this.EQFileName,
      };
      if (this.delDate) {
        params.delDate = this.delDate;
      }
      this.spinning = true;
      upeQAttachment(params)
        .then(res => {
          if (res.code) {
            this.$message.success("上传成功");
            this.geteqdata();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.delDate = null;
          this.uppath = "";
          this.spinning = false;
        });
    },
    async beforeUpload(file, filelist) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const filesize = Number(file.size / 1024 / 1024) < 500;
        if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    //分段上传附件
    async httpRequest7(data, type) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      this.spinning = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              this.uppath = res.message;
              if (!this.dataVisibleileFile) {
                this.upeQAttachment();
              } else {
                this.spinning = false;
              }
            }
          } else {
            this.$message.error(res.message);
            i = shardCount;
            this.spinning = false;
          }
        });
      }
    },
    handleChange7({ fileList }) {
      this.fileList7 = [fileList[fileList.length - 1]];
      for (let index = 0; index < this.fileList7.length; index++) {
        this.EQFileName = this.fileList7[index].name;
      }
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    EqClick() {
      if (JSON.stringify(this.selectdata) == "{}") {
        this.$message.warning("请选择需要进行回复的订单");
      } else {
        let URL = process.env.VUE_APP_API_JSON_URL;
        let OrderNo = this.selectdata.orderNo;
        let BusinessOrderNo = this.selectdata.businessOrderNo;
        let JoinFactoryId = this.selectdata.joinFactoryId;
        let eQSource = this.selectdata.eqSource;
        timestampVerifyCode().then(res => {
          if (res.code) {
            iseQRead(this.selectdata.proOrderId).then(res => {});
            let Timestamp = res.data.timestamp;
            let VerifyCode = res.data.verifyCode;
            let content =
              URL +
              "/eqDetails1?JoinFactoryId=" +
              JoinFactoryId +
              "&OrderNo=" +
              OrderNo +
              "&Timestamp=" +
              Timestamp +
              "&VerifyCode=" +
              VerifyCode +
              "&BusinessOrderNo=" +
              BusinessOrderNo +
              "&eQSource=" +
              eQSource;
            window.open(content);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    isclickcolor(record) {
      let strGroup = [];
      if (record.proOrderId && record.proOrderId == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.id = record.proOrderId;
            this.selectdata = record;
          },
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.rightClick1(e, text, record);
          },
        },
      };
    },
    rightClick1(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode;
      }
      if (e.target.localName == "path" || e.target.localName == "svg") {
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode;
        if (event.className.indexOf("tagNum") != -1) {
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
        }
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split(" ")[0].trim();
      }
      const tableWrapper = this.$refs.tableWrapper;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;
      if (event.cellIndex == this.columns1.length - 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    down() {
      let input = document.createElement("input");
      input.value = this.text;
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        this.reportHandleCancel1();
        this.formdata = {};
        this.timeValue = undefined;
        this.dataVisible = true;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    queryClick() {
      this.dataVisible = true;
      this.formdata = {};
      this.timeValue = undefined;
    },
    reportHandleCancel1() {
      this.delDate = null;
      this.uppath = "";
      this.dataVisibleileFile = false;
      this.dataVisible = false;
    },
    handleOk() {
      this.dataVisible = false;
      this.geteqdata(this.formdata);
    },
    geteqdata(querydata) {
      this.spinning = true;
      let params = {
        ClientLoginKey: this.user.clientLoginKey,
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
        EqStatus: this.activeKey,
      };
      if (querydata) {
        params = { ...params, ...querydata };
      }
      eqmanagementlistbycustno(params)
        .then(res => {
          this.eqdatasource = res.items;
          setTimeout(() => {
            this.handleResize();
          }, 0);
          this.pagination.total = res.totalCount;
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      if (JSON.stringify(this.formdata) != "{}") {
        this.geteqdata(this.formdata);
      } else {
        this.geteqdata();
      }
    },
    handleResize() {
      let leftContent = document.getElementsByClassName("leftContent")[0];
      let leftstyle = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (leftstyle && this.eqdatasource.length != 0) {
        leftstyle.style.height = window.innerHeight - 233 + "px";
      } else {
        leftstyle.style.height = 0;
      }
      leftContent.style.height = window.innerHeight - 140 > 719 ? "719px" : window.innerHeight - 190 + "px";
      let elements = document.getElementsByClassName("box");
      let buttonwidth = elements.length * 110;
      let footerwidth = window.innerWidth - 170;
      let paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      let paginationwidth = paginnum * 40 + 380;
      let paginationwidth1 = paginnum * 22 + 336;
      let paginationwidth2 = 140;
      if (buttonwidth + paginationwidth < footerwidth) {
        this.pagination.size = "default";
        this.pagination.simple = false;
        this.buttonsmenu = false;
      } else if (buttonwidth + paginationwidth > footerwidth && buttonwidth + paginationwidth1 < footerwidth) {
        this.pagination.size = "small";
        this.pagination.simple = false;
        this.buttonsmenu = false;
      } else {
        this.pagination.simple = true;
        this.buttonsmenu = false;
        if (buttonwidth + paginationwidth2 > footerwidth) {
          this.buttonsmenu = true;
        } else {
          this.buttonsmenu = false;
        }
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-calendar-range-picker-input {
  text-align: left;
  width: 29%;
}
/deep/.ant-menu-item:hover {
  color: rgb(22, 22, 22);
}
/deep/.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #ffffff;
  color: black;
}
.tabRightClikBox {
  li {
    height: 24px;
    line-height: 20px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  position: fixed;
  font-size: small;
  margin: 7px;
}
.tabRightClikBox1 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.footerAction {
  width: 100%;
  height: 46px;
  background: white;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  .box {
    float: right;
    margin-top: 6px;
    margin-right: 10px;
    width: 90px;
  }
  .box1 {
    float: right;
    margin-top: 9px;
    margin-right: 12px;
    width: 90px;
  }
}
.projecteq {
  background-color: white;
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 3px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-thead > tr > th {
      padding: 4px 2px;
      height: 30px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 2px;
      height: 30px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
</style>
