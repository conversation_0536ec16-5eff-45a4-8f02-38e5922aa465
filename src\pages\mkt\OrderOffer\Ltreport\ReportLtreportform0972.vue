<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ltreport0927" style=" padding: 25px;color: black;font-weight: 600;font-family: '宋体';font-size: 10px;" >
            <div style="text-align: center;font-weight: bold;font-size: 30px;">深圳拓邦股份有限公司</div>
            <div style="text-align: center;font-weight: bold;font-size: 24px;">PCB报价单</div>
            <div style="width: 100%;display:flex">
                <div style="width: 60%;margin-left:70px">
                    <div>报价日期：{{ LTreportdata.date_ }}</div>
                    <div>报价编号：{{ LTreportdata.orderNo_ }}</div>
                    <div>供应商名称:{{ LTreportdata.factory_ }}</div>
                    <div>年用量:{{ LTreportdata.nO_ }}</div>
                    <div>交货地址：{{ LTreportdata.address_ }}</div>
                </div>
                <div style="width: 30%;">
                    <br/>
                    <div>版本:{{ LTreportdata.nO1_ }}</div>
                    <div>产品应用:{{ LTreportdata.nO2_ }}</div>
                    <div>币别: {{ LTreportdata.currency_ }}</div>
                    <div>月结方式:{{ LTreportdata.clearingForm }}</div>
                </div>
            </div>
            <div>
                <table border="1" style="text-align: center;margin-top: 5px;width: 100%;border-top: 1px solid black;border-left: 1px solid black;">
                    <thead>
                        <tr>
                            <td>报价日期</td>
                            <td colspan="2">TB/PN</td>
                            <td> </td>
                            <td> </td>
                            <td> </td>
                            <td colspan="15">规格型号</td>
                            <td>基础工艺平米价（<span style="color: red;">人民币含税/平</span>）</td>
                            <td colspan="15">其他加价项（<span style="color: red;">人民币含税/平</span>）</td>
                            <td rowspan="2">平方米价格（<span style="color: red;">含税</span>）</td>
                            <td rowspan="2">单价（<span style="color: red;">未税</span>）</td>
                            <td rowspan="2">未税总金额</td>
                            <td rowspan="2">MOQ</td>
                            <td rowspan="2">L/T(天)</td>
                            <td rowspan="2">其他加价说明</td>
                        </tr>
                        <td></td>
                        <td>客户料号</td>
                        <td>物料描述</td>
                        <td>数量</td>
                        <td>工程费未税</td>
                        <td>类型</td>
                        <td>层数</td>
                        <td>表面处理</td>
                        <td>TG</td>
                        <td>CTI</td>
                        <td>板材类型</td>
                        <td>板材品牌及型号</td>
                        <td>PCB厚度(mm)</td>
                        <td>完成铜厚(um)</td>
                        <td  colspan="2">拼版尺寸(mm)</td>
                        <td>拼板数</td>
                        <td>利用率</td>
                        <td>沉金面积</td>
                        <td>孔数(万/平)</td>
                        <td>基础工艺平米价</td>
                        <td>表面处理加价</td>
                        <td>TG加价</td>
                        <td>CTI加价</td>
                        <td>低利用率加价</td>
                        <td>POFV加价</td>
                        <td>半孔加价</td>
                        <td>超孔加价</td>
                        <td>双PP加价</td>
                        <td>阻抗加价</td>
                        <td>高温胶纸加价</td>
                        <td>最小孔径0.2加价</td>
                        <td>白油块加价</td>
                        <td>单面板-CNC加价</td>
                        <td>单面板-曝光加价</td>
                        <td>无卤加价</td>
                        <td>其他加价</td>
                    </thead> 
                    <tbody>
                        <tr v-for="(item,index) in LTreportdata.price" :key="index">
                            <td style="width:70px;">{{ item.bType }}</td>
                            <td>{{ item.custName }} </td>
                            <td>{{ item.pinBanType }} </td>
                            <td>{{ item.qty }} </td>
                            <td>{{ item.eng }} </td>
                            <td>{{ item.boardBrand }} </td>
                            <td>{{ item.lay }} </td>
                            <td>{{ item.surf }} </td>
                            <td>{{ item.setQty }} </td>
                            <td>{{ item.diff }} </td>
                            <td>{{ item.solder }} </td>
                            <td>{{ item.film }} </td>
                            <td>{{ item.boardThickness }} </td>
                            <td>{{ item.oucu }} </td>
                            <td>{{ item.nxbPrice }} </td>
                            <td>{{ item.size }} </td>
                            <td>{{ item.su }} </td>
                            <td>{{ item.mat }} </td>
                            <td>{{ item.area }} </td>
                            <td>{{ item.poreDensity }} </td>
                            <td>{{ item.plate }} </td>
                            <td>{{ item.surface }} </td>
                            <td>{{ item.hpPrice }} </td>
                            <td>{{ item.zhPrice }} </td>
                            <td>{{ item.zfPrice }} </td>
                            <td>{{ item.yjkPrice }} </td>
                            <td>{{ item.remark }} </td>
                            <td>{{ item.zkPrice }} </td>
                            <td>{{ item.wlPrice }} </td>
                            <td>{{ item.wxbPrice }} </td>
                            <td>{{ item.tyPrice }} </td>
                            <td>{{ item.bcPrice }} </td>
                            <td> </td>
                            <td>{{ item.ljPrice }} </td>
                            <td>{{ item.ktPrice }} </td>
                            <td>{{ item.bbPrice }} </td>
                            <td>{{ item.bgPrice }} </td>
                            <td>{{ item.sampl }} </td>
                            <td>{{ item.pcs }} </td>
                            <td>{{ item.total }} </td>
                            <td>{{ item.other }} </td>
                            <td>{{ item.incu }} </td>
                            <td>{{ item.notes }} </td>
                        </tr>
                    </tbody>   
                </table>
            </div>
            <div style="margin-left: 70px;padding-top: 5px;">特殊说明：{{ LTreportdata.specialrequirements }}</div>
            <div style="margin-left: 70px;display: flex;padding-top: 5px;">
                <div style="width: 30%;">提交：<span class="Underline"></span>{{ LTreportdata.noteSure }}</div> 
                <div style="width: 30%;">审核：<span class="Underline"></span>{{ LTreportdata.consignee }}</div> 
                <div style="width: 30%;">批准：<span class="Underline"></span>{{ LTreportdata.quoter }}</div> 
            </div>
        </div>
    </div>
  </template>
  <script>
  import html2pdf from 'html2pdf.js'
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        printObj1:{
            id: "ltreport0927", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
    },
    methods: { 
        downloadToPDF(dom, name) {
        const element = document.getElementById(dom);
        const opt = {
            margin: 0,
            filename: name + ".pdf",
            image: { type: "jpeg", quality: 1},
            html2canvas: { scale: 1},
            jsPDF: {
                unit: "in",
                format: "a3",
                orientation: "landscape",
            },
            pagebreak: { mode: ['css', 'legacy',]},
        };
        html2pdf().set(opt).from(element).save();
      },
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            this.downloadToPDF('ltreport0927',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
     .Underline {
        position: relative;
        display: inline-block;
    }
    .Underline::after {
        content: "";
        position: absolute;
        left: 0;
        bottom:-8px; /* 下划线距离文字底部的距离 */
        width: 200px; /* 下划线宽度 */
        height: 2px; /* 下划线高度 */
        background-color: rgb(107, 106, 106); /* 下划线颜色 */
    }
    .Underline1 {
        position: relative;
        display: inline-block;
    }
    .Underline1::after {
        content: "";
        position: absolute;
        left: 0;
        bottom:-3px; /* 下划线距离文字底部的距离 */
        width: 200px; /* 下划线宽度 */
        height: 2px; /* 下划线高度 */
        background-color: rgb(107, 106, 106); /* 下划线颜色 */
    }
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
  .pdfDom1{
      height: 650px;
      overflow: auto;
      table >thead> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
    table >tbody> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
  }
  </style>
