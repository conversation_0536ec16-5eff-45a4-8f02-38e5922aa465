<!-- 工程管理 - 工程制作-系数倍数修改 -->
<template>
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="系数倍数修改">
      <a-input   v-model='scoreMultiples' placeholder="请输入系数倍数" :autoFocus="autoFocus" @keyup.enter="keyupEnter1"/>
    </a-form-item>
  </a-form>
</template>

<script>
export default {
    name:'xishuModify',
    props:{
    selectedRowsData:{
      type:Object
    },
  },
  data() {
    return {
      scoreMultiples:'',
      autoFocus:true,
      proOrderId:this.selectedRowsData.proOrderId,
    };
  },
  methods: {  
   keyupEnter1(){
      this.$emit('keyupEnter1')
  }
  },
};
</script>