<!-- 物料管理-物料录入-按钮 -->
<template>
  <div class="active">
    <div
      class="box"
      v-if="checkPermission('MES.MaterialModule.Material.MaterialAdd')"
      :class="checkPermission('MES.MaterialModule.Material.MaterialAdd') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="btnAdd"> 新增 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MaterialModule.Material.MaterialEdit')"
      :class="checkPermission('MES.MaterialModule.Material.MaterialEdit') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="edit"> 编辑 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MaterialModule.Material.MaterialSoftDel')"
      :class="checkPermission('MES.MaterialModule.Material.MaterialSoftDel') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="deleteorder"> 删除 </a-button>
    </div>
    <!-- <div class="box showClass" >
        <a-button type="primary" @click="addCategory">
          添加类别
        </a-button>
      </div>
      <div class="box showClass">
        <a-button type="primary" @click ="pcbSetfile" >
          厂商建档
        </a-button>
      </div> -->
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-right: 15px; width: 100px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="edit" v-if="checkPermission('MES.MaterialModule.Material.MaterialAdd')">新增</a-menu-item>
            <a-menu-item @click="edit" v-if="checkPermission('MES.MaterialModule.Material.MaterialEdit')">编辑</a-menu-item>
            <a-menu-item @click="deleteorder" v-if="checkPermission('MES.MaterialModule.Material.MaterialSoftDel')">删除</a-menu-item>
            <!-- <a-menu-item @click="addCategory">添加类别</a-menu-item>      
       <a-menu-item @click="pcbSetfile" >厂商建档</a-menu-item> -->
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { uploadPCBFile } from "@/services/mkt/PrequalificationProduction.js";
import protocolCheck from "@/utils/protocolcheck";
export default {
  name: "MakeAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      nums: "",
      buttonsmenu: false,
    };
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      let num = "";
      num = (elements.length + 1) * 104;
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            if (i < 6) {
              elements[i].style.display = "inline-block";
            }
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                if (i < 6) {
                  elements[i].style.display = "inline-block";
                }
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          if (i < 6) {
            elements[i].style.display = "inline-block";
          }
        }
        this.buttonsmenu = false;
      }
    },
    // 新增物料
    btnAdd() {
      this.$emit("btnAdd");
    },
    // 编辑
    edit() {
      this.$emit("edit");
    },
    //查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 厂商建档
    pcbSetfile() {
      this.$emit("pcbSetfile");
    },
    //删除
    deleteorder() {
      this.$emit("deleteorder");
    },
    // 添加类别
    addCategory() {
      this.$emit("addCategory");
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.nums = document.getElementsByClassName("showClass").length;
      window.addEventListener("resize", this.handleResize, true);
      this.handleResize();
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.active {
  position: relative;
  top: 8px;
  height: 50px;
  float: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    margin-right: 15px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .ant-btn-primary:hover {
      background-color: #ffb029 !important;
      border-color: #ffb029 !important;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        // background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
