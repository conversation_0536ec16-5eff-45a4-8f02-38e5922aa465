import { request, METHOD } from "@/utils/request";
import { lt } from "date-fns/locale";
export async function coreTypes(id, layer) {
  // console.log(id,layer)
  return request(`/api/app/e-mSTPub-stack-up-basic/core-type-item/${id}?layer=${layer}`, METHOD.POST);
}
export async function ppTypes(id) {
  return request(`/api/app/e-mSTPub-stack-up-basic/p-pType-item/${id}`, METHOD.POST);
}
export async function pdctnos(joinFactoryId) {
  return request(`/api/app/e-mSTPub-stack-up-basic/pdct-nos/${joinFactoryId}`, METHOD.GET);
}
//YXD基础信息联动数据
export async function stackuptol(joinFactoryId, params) {
  return request(`/api/app/e-mSTPub-stack-up-basic/stack-up-tol/${joinFactoryId}`, METHOD.GET, params);
}
//LT上下限公差联动压合公差
export async function stackuptolv2(joinFactoryId, params) {
  return request(`/api/app/e-mSTPub-stack-up-basic/stack-up-tol-v2/${joinFactoryId}`, METHOD.GET, params);
}
export default {
  coreTypes,
  ppTypes,
  pdctnos,
  stackuptol,
  stackuptolv2,
};
