<!--生产管理- 订单详情-投料单填写 -->
<template>
  <a-form>
    <a-form-item label="生产开始时间" :label-col="{ span:6}" :wrapper-col="{ span: 18}">
      <a-date-picker 
        v-model="form.deliveryDate"              
        valueFormat="YYYY-MM-DD "               
        placeholder="开始交期"
        @change="onChange1"
      />
    </a-form-item>
    <!-- <a-form-item label="是否补料" :label-col="{ span:6}" :wrapper-col="{ span: 10}">
      <a-checkbox v-model="form.isFeed"    />
    </a-form-item> -->
    <a-form-item label="投料数量" :label-col="{ span:6}" :wrapper-col="{ span: 10}">
      <a-input v-model="form.num"   allowClear />
    </a-form-item>
    <a-form-item label="备注" :label-col="{ span:6}" :wrapper-col="{ span: 10}">
      <a-textarea v-model="form.remarks"  allow-clear  />
    </a-form-item>
  </a-form>
</template>

<script>
import moment from "moment";
 
export default {
  name: "FeedingModal",
  props:['pinBanNum'],
  data(){
    return{
      form:{
        deliveryDate:moment(),
        // isFeed:false,
        num:'',
        remarks:'',
      }
    }
  },   
  mounted(){
    this.form.num = this.pinBanNum

  },
  methods: {
    moment,
    onChange1 (value, dateString) {
      this.form.deliveryDate = dateString
      console.log('dateString',dateString)
    },
    
  }
  
}
</script>

<style scoped lang="less">
/deep/.ant-input{
  font-weight:500
}
.ant-calendar-picker{
  width:197px;
}
</style>