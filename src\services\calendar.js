import { request, METHOD } from '@/utils/request';
// 获取参数列
 export async function calendarList(date,factory) {
    return request(`/api/app/calendar/calendar-list?date=${date}&factory=${factory}`, METHOD.GET)
}
// 获取参数信息
export async function setHoliday(startDate,endDate,isHoliday,factory) {
    return request(`/api/app/calendar/set-holiday?startDate=${startDate}&endDate=${endDate}&isHoliday=${isHoliday}&factory=${factory}`, METHOD.POST,)
}
//日志
export async function calendarlogfactoryid(factory) {
    return request(`/api/app/e-mSCalendar-log/calendar-log-factory-id?JoinFactoryId=${factory}`, METHOD.GET,)
}
