import { request, METHOD } from '@/utils/request'
export async function flyingList(params) {
    return request("/api/app/e-mSTProc-eT/e-tList", METHOD.GET, params)
}
export async function flyingUpload(params) {
    return request("/api/file-management/files/flyuploadpfile", METHOD.POST, params)
}
export async function flyingAdd(params) {
    return request("/api/app/e-mSTProc-eT", METHOD.POST, params)
}
export async function downLoad(Id) {
    return request(`/api/app/e-mSTProc-eT/down-load-path/${Id}`, METHOD.GET,)
}

export async function flyingCancel(Id) {
    return request(`/api/app/e-mSTProc-eT/cancel-order/${Id}`, METHOD.POST,)
}
export async function flyingSet(parmas) {
    return request(`/api/app/e-mSTProc-eT/set-naming`, METHOD.POST,parmas)
}
export async function getFlyingSet(parmas) {
    return request(`/api/app/e-mSTProc-eT/naming`, METHOD.GET)
}


export  default {
    flyingList,
    flyingUpload,
    flyingAdd,
    downLoad,
    flyingCancel,
    flyingSet,
    getFlyingSet
}
