<template>
    <div>
        <a-descriptions  bordered>
            <a-descriptions-item label="主要板材品牌">
            <span v-if="type=='1'">{{basicData.sheet_}}</span>
              <a-input type="text" v-model="basicData.sheet_" readonly v-else/>
            </a-descriptions-item>
            <a-descriptions-item label="主要油墨品牌">
              <span v-if="type=='1'">{{basicData.ink_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="最高层数">
              <span v-if="type=='1'">{{basicData.layers_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="成品最小板厚">
              <span v-if="type=='1'">{{basicData.boardThk_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="最高铜厚">
              <span v-if="type=='1'">{{basicData.cuThk_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="最小线宽/线距">
              <span v-if="type=='1'">{{basicData.lineAbility_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="蚀刻工艺">
               <span v-if="type=='1'">{{basicData.etching_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="线路工艺">
               <span v-if="type=='1'">{{basicData.lineCraft_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="沉铜工艺">
                <span v-if="type=='1'">{{basicData.cuCraft_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="图形工艺">
               <span v-if="type=='1'">{{basicData.graphCraft_}}</span>
            </a-descriptions-item>
            <a-descriptions-item label="非沉铜工艺">
                <span v-if="type=='1'">{{basicData.notCuCraft_}}</span>
            </a-descriptions-item>
        </a-descriptions>
    </div>
</template>

<script>
export default {
    props: {
        basicData: {
            type: Object,
            default () {
                return {}
            }
        },
        type: {
           type: String,
            default () {
                return ''
            }  
        }
    },
    data () {
        return {
            
        }
    },
    methods: {
        
    }
}
</script>