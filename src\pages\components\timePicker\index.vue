<template>
  <a-card>
      <div>
        <a-alert message="12 小时制的时间选择器，默认的 format 为 h:mm:ss a。" type="warning" class='alert' show-icon/>
        <a-time-picker use12-hours @change="onChange" />
        <span class='marginRight'></span>
        <a-time-picker use12-hours format="h:mm:ss A" @change="onChange" />
        <span class='marginRight'></span>
        <a-time-picker use12-hours format="h:mm a" @change="onChange" />
        <span class='marginRight'></span>
      </div>
      <div>
          <a-alert message="在 TimePicker 选择框底部显示自定义的内容。" type="warning" class='alert' show-icon/>
          <a-time-picker :open="open" @openChange="handleOpenChange">
            <a-button slot="addon" slot-scope="panel" size="small" type="primary" @click="handleClose">
                Ok {{ panel.prefixCls }}
            </a-button>
            </a-time-picker>
            <a-time-picker :open.sync="open2">
            <a-button slot="addon" size="small" type="primary" @click="handleClose">
                Ok
            </a-button>
          </a-time-picker>
      </div>
      <div>
          <a-alert message="禁用时间选择" type="warning" class='alert' show-icon/>
          <a-time-picker :default-value="moment('12:08:23', 'HH:mm:ss')" disabled />
      </div>
      <div>
          <a-alert message="三种大小的输入框，大的用在表单中，中的为默认" type="warning" class='alert' show-icon/>
          <a-time-picker :default-value="moment('12:08:23', 'HH:mm:ss')" size="large" />
          <a-time-picker :default-value="moment('12:08:23', 'HH:mm:ss')" />
          <a-time-picker :default-value="moment('12:08:23', 'HH:mm:ss')" size="small" />
      </div>
  </a-card>
</template>
<script>
import moment from 'moment';
export default {
  data() {
      return {
          open: false,
          open2: false,
      }
  },
  methods: {
    moment,
    onChange(time, timeString) {
      console.log(time, timeString);
    },
    handleOpenChange(open) {
      console.log('open', open);
      this.open = open;
    },
    handleClose() {
      this.open = false;
      this.open2 = false;
    },
  },
};
</script>
<style lang="less" scoped>
.marginRight {
    margin-right:10px
}
.alert {
    margin-bottom:10px;
    margin-top: 10px
}
</style>