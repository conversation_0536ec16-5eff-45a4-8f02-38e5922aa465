<template>
  <div ref="SelectBox">
    <a-modal
    :title="model.id? '编辑' : '新增'"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    centered
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="项目名称" ref="qualification_" prop="qualification_">
            <a-select v-model="form.qualification_" :getPopupContainer="()=>this.$refs.SelectBox" >
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="404200">
                    ISO9001
                </a-select-option>
                <a-select-option value="404201">
                    ESO14001
                </a-select-option>
                <a-select-option value="404202">
                    UL
                </a-select-option>
                <a-select-option value="404203">
                    CQC
                </a-select-option>
                <a-select-option value="404204">
                    TF16949
                </a-select-option>
                <a-select-option value="404205">
                    OHSAS18001
                </a-select-option>
                <a-select-option value="404206">
                    JGB9001
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="是否" ref="isQf_" prop="isQf_">
           <a-switch v-model="form.isQf_"/>
        </a-form-model-item>
        <a-form-model-item   label="认证时间" ref="approveTime_" prop="approveTime_">
           <a-date-picker  v-model="form.approveTime_" format="YYYY-MM-DD" style="width: 100%;"/>
        </a-form-model-item>
        <a-form-model-item   label="计划认证时间" ref="planApproveTime_" prop="planApproveTime_">
           <a-date-picker  v-model="form.planApproveTime_" format="YYYY-MM-DD" style="width: 100%;"/>
        </a-form-model-item>
        <a-form-model-item  label="上传图片" v-if="form.id" ref="imgs" prop="imgs">
            <div class="clearfix">
              <a-upload
                accept=".jpg,.png,.gif,.bmp,.jpeg,"  
                name="file"
                ref="filename"
                :customRequest="httpRequest1"
                list-type="picture-card"
                :multiple="true"
                :file-list="fileList"
                :before-upload="beforeUpload"
                @preview="handlePreview"
                @change="handleChange"
              >
                <div>
                  <a-icon type="plus" />
                  <div class="ant-upload-text">
                    Upload
                  </div>
                </div>
              </a-upload>
            </div>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
  </div>
  
</template>

<script>
import moment from 'moment';
import { upLoadFlyingFile,} from '@/services/projectMake';
import { addQualify, updateQualify, uploadImg, getPhoto,progressNum } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
      compileApply:{
          type: String,
          default () {
              return ''
          }
      },
      message:{
          type: String,
          default () {
              return ''
          }
      }
  },
  data() {
    return {
      baseURL: process.env.VUE_APP_API_BASE_URL,
      labelCol: { span: 5},
      wrapperCol: { span: 18 },
      visible: false,
      confirmLoading: false,
      isFileType:true,
      arrData:[],
      path:'',
      form: {
        qualification_: '',
        isQf_: false,
        approveTime_: '',
        planApproveTime_: '',
      },
      rules: {
        qualification_: [
          { required: true, message: "名称必须填写", trigger: "blur" },
        ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    async httpRequest1(data,type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message)
        }
      })

    },
    beforeUpload(file) {
      this.isFileType = file.name.toLowerCase().indexOf('.jpg') != -1 || file.name.toLowerCase().indexOf('.png') != -1
          if (!this.isFileType) {
            this.$message.error('图片上传只支持.jpg/.png图片格式文件');
          }
          return this.isFileType
    },
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
        this.$nextTick(() => {
        this.$viewerApi({
          images: [file.url || file.preview],
        });
      });
    },
    handleChange({ fileList }) {
     if (this.isFileType) { 
        this.fileList = fileList;      
        for (let index = 0; index < this.fileList.length; index++) {
          const element = this.fileList[index].response;
          if (element && !this.arrData.includes(element)) {
            this.arrData.push(element);
            this.path = this.arrData.toString(',');
          }
        }
        this.fileList.forEach(ite=>{
          if(ite.url){
          this.arrData = [...new Set(this.arrData.concat(ite.url))];
          this.path = this.arrData.toString(',');
          }
         })
         console.log(this.arrData ,'this.arrData ');
        if (this.fileList.length === 0) {
          this.path = '';
        }
      }
    },
    openModal(model) {
        // if(this.compileApply=='1'){
            this.visible = true;
            this.model = model

            this.fileList = []
            if(model && model.id) {
                this.form = {
                    id:model.id,
                    qualification_: model.qualification_,
                    isQf_: model.isQf_,
                    approveTime_: model.approveTime_,
                    planApproveTime_: model.planApproveTime_,
                };
                this.getPhotos(model.id)
            }else {
                this.form= {
                    qualification_: '',
                    isQf_: false,
                    approveTime_: '',
                    planApproveTime_: '',
                }
            }
        // }else {
        //     this.$message.info(this.message)
        // }

    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
      this.$refs.ruleForm.resetFields()
        this.form= {
            qualification_: '',
            isQf_: false,
            approveTime_: '',
            planApproveTime_: '',
        }
    },
    //查看图片
    getPhotos(id) {
        getPhoto(id)
            .then((res) => {
                res.forEach(e=>{
                  let info = {
                    uid: e.id,
                    name: 'image.png',
                    status: 'done',
                    url: e.strUrl_
                  }
                 this.fileList.push(info)
                })

            })
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          let params = {
              id: this.model.id,
              pGuid_: this.suppId,
              approveTime_: moment(this.form.approveTime_).format('YYYY-MM-DD'),
              planApproveTime_: moment(this.form.planApproveTime_).format('YYYY-MM-DD'),
              isQf_ : this.form.isQf_,
              qualification_: this.form.qualification_
          }
          if(this.model.id) {

            let str = ''
              this.fileList.forEach(e=> {
                if(e.response) {
                  str += e.response + ','
                }else if(e.url) {
                  str += e.url + ','
                }

              })
              str = str.substring(0,str.length-1)
              let fileData = {
                pGuid_: this.model.id,
                strUrl_: this.path
              }
              uploadImg(fileData).then((res) => {
                    console.log(res)
                })
            updateQualify(this.model.id,params) .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.success('保存成功')
                    this.arrData=[]
                    this.fileList = []
                    this.$emit("ok");
                    progressNum(this.suppId).then(res=>{
                        if(res.code!=='1'){
                            //this.$message.info(res.message)
                        }
                    })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }else{
              addQualify(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info("操作成功");
                    this.fileList = []
                    this.$emit("ok");
                    progressNum(this.suppId).then(res=>{
                        if(res.code!=='1'){
                            this.$message.info(res.message)
                        }
                    })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }

        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
}
/deep/.ant-calendar-picker-input.ant-input{
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-form-item{
    margin-bottom: 5px;
}
</style>
