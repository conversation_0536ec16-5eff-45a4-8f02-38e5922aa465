<!-- 工具管理- 开料拼版-按钮 -->
<template>
  <div class="btnActive">
      <div  class="boxx" @click="autoClick">
        <a-icon type="appstore" theme="filled" class="icon"/>
        <div class="text">自动拼板</div>
      </div>
      <div class="boxx" @click="manualClick">
        <a-icon type="wallet" theme="filled" class="icon"/>
        <div class="text">手动拼板</div>
      </div>
      <div  class="boxx" @click="materialSettingClick">
        <a-icon type="setting" theme="filled" class="icon"/>
        <div class="text">材料设置</div>

      </div>
      <!-- <div class=" Manual" @click="manualDrawingClick" >
        <a-icon type="sliders" theme="filled" class="icon"/>
        <div class="text">手动画图</div>

      </div> -->
      <div class="boxx" @click="dataSaveClick">
        <a-icon type="save" theme="filled" class="icon"/>
        <div class="text">数据保存</div>
      </div>
      
      <div class="boxx" @click="reportClick">
        <a-icon type="printer" theme="filled" class="icon"/>
        <div class="text">数据报表</div>
      </div>
      
  </div>
</template>

<script>
export default {
  name: "FooterAction",
  methods: {
    autoClick(){
      this.$emit('autoClick')
    },
    manualClick(){
      // alert('手动拼板')
      this.$emit('handClick')
    },
    materialSettingClick(){
      // alert('材料设置')
      this.$emit('materialSettingClick')
    },
    // manualDrawingClick(){
    //   // alert('手动画图')
    // },
    dataSaveClick(){
       // alert('保存数据')
      this.$emit('dataSaveClick')
    },
    reportClick(){
      // alert('导出报表')
      this.$emit('openReport')
    },
  }
}
</script>

<style scoped lang="less">
// /deep/.ant-layout {
//   color: rgba(51, 51, 51, 1);  
// }

.btnActive {
  display: flex;
  height: 50px;
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  padding: 4px 0 2px 14px;
  // .Manual{
  //   margin-right: 14px ;
  //   display: flex;
  //   flex-wrap: wrap;
  //   justify-content: center;     
  //   color: #bbbb;
  //   border-color: rgba(215, 215, 215, 1);
  //   .icon {
  //     color: #bbbb;
  //   }    
  //   .icon {
  //     width: 24px;
  //     height: 24px;
  //     flex: 1;
  //     font-size: 24px;
  //     color: rgba(187, 187, 187, 1);
  //   }
  //   .text {
  //     height: 18px;
  //     line-height: 18px;
  //     width: 100%;
  //     text-align: center;
  //   }
  // }
  .boxx {
    margin-right: 14px ;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    cursor: pointer;
    border: 1px solid #FFFFFF;
    color: rgba(51, 51, 51, 1)!important; 
    &:hover {
      color: #ff9900!important;
      background:#FFF9E6;
      border-color: rgba(215, 215, 215, 1);
      .icon {
        color: #ff9900!important;
      }
    }
    .icon {
      width: 24px;
      height: 24px;
      flex: 1;
      font-size: 24px;
      color: rgba(187, 187, 187, 1);
    }
    .text {
      height: 18px;
      line-height: 18px;
      width: 100%;
      text-align: center;
    }
  }
 

}

</style>