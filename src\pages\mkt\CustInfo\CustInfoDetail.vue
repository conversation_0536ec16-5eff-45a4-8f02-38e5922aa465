<!-- 市场管理 - 客户管理详情 -->
<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <div class="custinfo" ref="SelectBox" @click="bodyClick">
    <a-card>
      <a-modal v-model="rightVisible" :mask="false" :maskClosable="false" @cancel="handleCancel1" centered title="联系人添加" @ok="handleRight">
        <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="默认联系人">
            <a-checkbox v-model.trim="form.default_" @change="handleChangetype_"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="联系人类型" ref="type_" prop="type_">
            <a-select
              v-model="form.type_"
              placeholder="请选择"
              @change="handleChangetype_"
              showSearch
              allowClear
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option value="办公"> 办公 </a-select-option>
              <a-select-option value="商务"> 商务 </a-select-option>
              <a-select-option value="采购"> 采购 </a-select-option>
              <a-select-option value="报价"> 报价 </a-select-option>
              <a-select-option value="技术"> 技术 </a-select-option>
              <a-select-option value="发货"> 发货 </a-select-option>
              <a-select-option value="询价"> 询价 </a-select-option>
              <a-select-option value="EQ联系人"> EQ联系人 </a-select-option>
              <a-select-option value="发票"> 发票 </a-select-option>
              <a-select-option value="跟单员"> 跟单员 </a-select-option>
              <a-select-option value="工作稿"> 工作稿 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="联系人" ref="addName" prop="addName">
            <a-input v-model.trim="form.addName" allowClear />
          </a-form-model-item>
          <a-form-model-item label="联系电话" ref="addPhone" prop="addPhone">
            <a-input v-model.trim="form.addPhone" allowClear />
          </a-form-model-item>
          <a-form-model-item label="邮箱" ref="mailbox" prop="mailbox">
            <a-input v-model.trim="form.mailbox" allowClear @change="mailboxChange" />
          </a-form-model-item>
          <a-form-model-item label="传真">
            <a-input v-model.trim="form.fax" allowClear />
          </a-form-model-item>
          <a-form-model-item label="收货省份" ref="provinceData" prop="provinceData">
            <a-cascader
              v-model="form.provinceData"
              change-on-select
              :options="jsonList"
              placeholder="请选择"
              @change="onChangeData"
              allowClear
              :show-search="{ filter }"
            />
          </a-form-model-item>
          <a-form-model-item label="收货地址" ref="address" prop="address">
            <a-input v-model.trim="form.address" allowClear @blur="parseAddress" />
          </a-form-model-item>
          <a-form-model-item label="收货公司">
            <a-input v-model.trim="form.receiverCompany_" allowClear />
          </a-form-model-item>

          <a-form-model-item label="终端客户">
            <a-select
              v-model="form.terminalCust"
              showSearch
              allowClear
              optionFilterProp="lable"
              @popupScroll="handlePopupScroll"
              @search="supValue"
              @change="terminalChange"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="item in frontDataZSupplier" :key="item.valueMember" :value="item.valueMember" :lable="item.valueMember">
                {{ item.valueMember }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <!-- <a-button type="primary" @click="$router.go(-1)" style="margin-right:5px;float: right;margin-top:6px"><a-icon type="rollback" />返回</a-button> -->
      <a-tabs default-active-key="1" @change="callback">
        <a-tab-pane key="1" tab="详情">
          <div style="position: relative; height: 768px">
            <a-spin :spinning="spinning">
              <a-form-model
                id="formDataElem"
                layout="inline"
                style="width: 100%; border-left: 1px solid #ddd; margin-top: 10px; border-top: 1px solid #ddd"
                :model="dataList"
              >
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="办事机构"
                      :class="requiredCustomConfig.institution && iseval(requiredCustomConfig.institution.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.institutionStr }}</span>
                      <div v-else>
                        <div>
                          <a-select v-model="dataList.institution" showSearch allowClear optionFilterProp="lable" :disabled="type == '0'">
                            <a-select-option v-for="item in mapKey(selectData.Institution)" :key="item.value" :value="item.value" :lable="item.lable">
                              {{ item.lable }}
                            </a-select-option>
                          </a-select>
                        </div>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户代码"
                      :class="requiredCustomConfig.custNo && iseval(requiredCustomConfig.custNo.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.custNo }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.custNo" allowClear :disabled="guid_ != '' ? true : false || disCustNo"> </a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户名称"
                      :class="requiredCustomConfig.custName && iseval(requiredCustomConfig.custName.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span :title="dataList.custName" v-if="type == '1'">{{ dataList.custName }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.custName" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户缩写"
                      :class="requiredCustomConfig.abbreviation && iseval(requiredCustomConfig.abbreviation.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.abbreviation }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.abbreviation" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户洲别"
                      :class="requiredCustomConfig.continent && iseval(requiredCustomConfig.continent.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.continentStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.continent"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.Continent)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户国别"
                      :class="requiredCustomConfig.country && iseval(requiredCustomConfig.country.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.countryStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.country"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.Country)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!-- <a-col :span="6">
                            <a-form-model-item label="客户类别" :class="requiredCustomConfig.category && iseval(requiredCustomConfig.category.isNullRules)?'redsty':''" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">               
                                <span v-if="type=='1'">{{dataList.categoryStr}}</span>
                                <div  v-else>
                                <a-select v-model="dataList.category" showSearch allowClear optionFilterProp="lable" :getPopupContainer="()=>this.$refs.SelectBox">
                                    <a-select-option  v-for="(item) in mapKey(selectData.Category)" :key="item.value" :value="item.value" :lable="item.lable">
                                        {{ item.lable }}
                                    </a-select-option>
                                </a-select>
                                </div>
                            </a-form-model-item>
                            </a-col> -->
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户类型"
                      :class="requiredCustomConfig.custLevel && iseval(requiredCustomConfig.custLevel.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.custLevelStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.custLevel"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.CustLevel)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="订单渠道"
                      :class="requiredCustomConfig.orderChannel && iseval(requiredCustomConfig.orderChannel.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.orderChannel }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.orderChannel"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.OrderChannel)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="行业类别"
                      :class="requiredCustomConfig.hylb && iseval(requiredCustomConfig.hylb.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.hylbStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.hylb"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.HYLB)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="业务人员"
                      :class="requiredCustomConfig.businessPerson && iseval(requiredCustomConfig.businessPerson.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.businessPerson }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.businessPerson" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="业务电话"
                      :class="requiredCustomConfig.businessPhone && iseval(requiredCustomConfig.businessPhone.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.businessPhone }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.businessPhone" allowClear> </a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="电子邮箱"
                      :title="dataList.email"
                      :class="requiredCustomConfig.email && iseval(requiredCustomConfig.email.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'" class="tmp">{{ dataList.email }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.email" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="新旧客户"
                      :class="requiredCustomConfig.newOrOld && iseval(requiredCustomConfig.newOrOld.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.newOrOldStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.newOrOld"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.NewOrOld)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户属性"
                      :class="requiredCustomConfig.custProperty && iseval(requiredCustomConfig.custProperty.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.custPropertyStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.custProperty"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.CustProperty)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="所属集团"
                      :class="requiredCustomConfig.ownedGroup && iseval(requiredCustomConfig.ownedGroup.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.ownedGroup }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.ownedGroup" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="业务组"
                      :class="requiredCustomConfig.ywz && iseval(requiredCustomConfig.ywz.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.ywzStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.ywz"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.YWZ)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="合同币种"
                      :class="requiredCustomConfig.currency && iseval(requiredCustomConfig.currency.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.currencyStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.currency"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.Currency)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="协议币种"
                      :class="requiredCustomConfig.protocolCurrency && iseval(requiredCustomConfig.protocolCurrency.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.protocolCurrencyStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.protocolCurrency"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option
                            v-for="item in mapKey(selectData.ProtocolCurrency)"
                            :key="item.value"
                            :value="item.value"
                            :lable="item.lable"
                          >
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="付款方式"
                      :class="requiredCustomConfig.payMethod && iseval(requiredCustomConfig.payMethod.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.payMethodStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.payMethod"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.PayMethod)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="付款条件"
                      :class="requiredCustomConfig.payCondition && iseval(requiredCustomConfig.payCondition.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.payConditionStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.payCondition"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.PayCondition)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="发票类型"
                      :class="requiredCustomConfig.invoiceType && iseval(requiredCustomConfig.invoiceType.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.invoiceTypeStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.invoiceType"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.InvoiceType)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="税率类型"
                      :class="requiredCustomConfig.taxType && iseval(requiredCustomConfig.taxType.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.taxTypeStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.taxType"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.TaxType)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="税率"
                      :class="requiredCustomConfig.taxPercentage && iseval(requiredCustomConfig.taxPercentage.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.taxPercentage }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.taxPercentage" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="纳税号"
                      :class="requiredCustomConfig.taxNo && iseval(requiredCustomConfig.taxNo.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.taxNo }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.taxNo" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!-- 
                            <a-col :span="6">
                            <a-form-model-item label="客户网址" :title="dataList.webSite" :class="requiredCustomConfig.webSite && iseval(requiredCustomConfig.webSite.isNullRules)?'redsty':''" :label-col="{ span:8}" :wrapper-col="{ span: 16 }">               
                                <span v-if="type=='1'">{{dataList.webSite}}</span>
                                <div  v-else>
                                <a-input v-model.trim="dataList.webSite"  allowClear >                                
                                </a-input>
                                </div>
                            </a-form-model-item>
                            </a-col>  
                                                     -->
                </a-row>
                <a-row>
                  <a-col :span="12">
                    <a-form-model-item
                      label="客户地址"
                      :title="dataList.address"
                      :class="requiredCustomConfig.address && iseval(requiredCustomConfig.address.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <span v-if="type == '1'" class="tmp">{{ dataList.address }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.address" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="邮政编码"
                      :class="requiredCustomConfig.postcode && iseval(requiredCustomConfig.postcode.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.postcode }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.postcode" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户传真"
                      :class="requiredCustomConfig.fax && iseval(requiredCustomConfig.fax.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.fax }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.fax" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!-- <a-col :span="6">
                            <a-form-model-item label="发票地址" :class="requiredCustomConfig.invoiceAddress && iseval(requiredCustomConfig.invoiceAddress.isNullRules)?'redsty':''" :label-col="{ span: 8}" :wrapper-col="{ span: 16}" >
                                <span v-if="type=='1'" class="tmp" :title="dataList.invoiceAddress">{{dataList.invoiceAddress}}</span>
                                <div  v-else>
                                    <a-input v-model.trim="dataList.invoiceAddress"  allowClear ></a-input>
                                </div>
                            </a-form-model-item>
                            </a-col>
                        </a-row>
                        <a-row> -->
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户电话"
                      :class="requiredCustomConfig.tel && iseval(requiredCustomConfig.tel.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.tel }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.tel" allowClear> </a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="企业法人"
                      :class="requiredCustomConfig.corporation && iseval(requiredCustomConfig.corporation.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.corporation }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.corporation" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="联系人"
                      :class="requiredCustomConfig.contact && iseval(requiredCustomConfig.contact.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.contact }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.contact" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="可接手客户"
                      :class="requiredCustomConfig.kjskh && iseval(requiredCustomConfig.kjskh.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.kjskh ? "是" : " " }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.kjskh"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!-- <a-col :span="6">
                            <a-form-model-item label="联系人" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">               
                                <span v-if="type=='1'">{{dataList.institution}}</span>
                                <div  v-else>
                                <a-input  >
                                </a-input>
                                </div>
                            </a-form-model-item>
                            </a-col> -->

                  <!-- <a-col :span="6">
                            <a-form-model-item label="统一社会信用代码" :class="requiredCustomConfig.socialCreditCode && iseval(requiredCustomConfig.socialCreditCode.isNullRules)?'redsty':''" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                            <span v-if="type=='1'">{{dataList.socialCreditCode}}</span>
                            <div  v-else>
                                <a-input v-model.trim="dataList.socialCreditCode"  allowClear ></a-input>
                            </div>
                            </a-form-model-item>
                            </a-col>
                            <a-col :span="6">
                            <a-form-model-item label="汉语缩写" :class="requiredCustomConfig.chineseSX && iseval(requiredCustomConfig.chineseSX.isNullRules)?'redsty':''" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                            <span v-if="type=='1'">{{dataList.chineseSX}}</span>
                            <div  v-else>
                                <a-input v-model.trim="dataList.chineseSX"  allowClear  ></a-input>
                            </div>
                            </a-form-model-item>
                            </a-col>
                           -->
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="开户银行"
                      :class="requiredCustomConfig.openBank && iseval(requiredCustomConfig.openBank.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.openBank }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.openBank" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="开户名称"
                      :class="requiredCustomConfig.bankUser && iseval(requiredCustomConfig.bankUser.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.bankUser }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.bankUser" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="银行账号"
                      :class="requiredCustomConfig.bankAccount && iseval(requiredCustomConfig.bankAccount.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.bankAccount }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.bankAccount" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="VIP客户"
                      :class="requiredCustomConfig.vip && iseval(requiredCustomConfig.vip.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.vip ? "是" : " " }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.vip"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="价格类型"
                      :class="requiredCustomConfig.priceType && iseval(requiredCustomConfig.priceType.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.priceTypeStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.priceType"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.PriceType)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="3">
                    <a-form-model-item
                      label="黑客户名单"
                      :class="requiredCustomConfig.ishmd && iseval(requiredCustomConfig.ishmd.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 16 }"
                      :wrapper-col="{ span: 8 }"
                    >
                      <span v-if="type == '1'">{{ dataList.ishmd ? "是" : " " }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.ishmd" @click="ishmdClick"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="3">
                    <a-form-model-item
                      label="生效日期"
                      :class="requiredCustomConfig.hmdDate && iseval(requiredCustomConfig.hmdDate.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.hmdDate }}</span>
                      <div v-else>
                        <a-date-picker v-model="dataList.hmdDate" format="YYYY-MM-DD" placeholder="请选择日期" @change="TimeChange"> </a-date-picker>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="黑名单原因"
                      :class="requiredCustomConfig.hmdCause && iseval(requiredCustomConfig.hmdCause.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.hmdCause }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.hmdCause" allowClear :disabled="dataList.ishmd ? false : true"></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="新报价合同"
                      :class="requiredCustomConfig.newOfferContract && iseval(requiredCustomConfig.newOfferContract.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.newOfferContract ? "是" : " " }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.newOfferContract"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!-- <a-col :span="6">
                            <a-form-model-item label="销售工程师" :class="requiredCustomConfig.functionary && iseval(requiredCustomConfig.functionary.isNullRules)?'redsty':''" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">               
                                <span v-if="type=='1'">{{dataList.functionaryStr}}</span>
                                <div  v-else>
                                <a-select v-model="dataList.functionary" showSearch allowClear optionFilterProp="lable" :getPopupContainer="()=>this.$refs.SelectBox"> 
                                    <a-select-option  v-for="(item) in userListData" :key="item.valueMember" :value="item.valueMember" :lable="item.text">
                                        {{ item.text }}
                                    </a-select-option>                                    
                                 </a-select>
                                </div>
                            </a-form-model-item>
                            </a-col>                                
                                              -->
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="销售工程师"
                      :class="requiredCustomConfig.functionary && iseval(requiredCustomConfig.functionary.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.functionaryStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.functionary"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in userListData" :key="item.valueMember" :value="item.valueMember" :lable="item.text">
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="跟单员(业助)"
                      :class="requiredCustomConfig.ddgDy && iseval(requiredCustomConfig.ddgDy.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.ddgDyStr }}</span>
                      <div v-else-if="[67,97,98].includes(Number(tradeType))">
                        <a-select
                          v-model="dataList.ddgDy"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          mode="multiple"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in userListData" :key="item.valueMember" :value="item.valueMember" :lable="item.text">
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </div>
                      <div v-else>
                        <a-select
                          v-model="dataList.ddgDy"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in userListData" :key="item.valueMember" :value="item.valueMember" :lable="item.text">
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="预审工程师"
                      :class="requiredCustomConfig.preEngineer && iseval(requiredCustomConfig.preEngineer.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.preEngineerStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.preEngineer"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in userListData" :key="item.valueMember" :value="item.valueMember" :lable="item.text">
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="从属代码 "
                      :title="dataList.custNoGroup"
                      :class="requiredCustomConfig.custNoGroup && iseval(requiredCustomConfig.custNoGroup.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.custNoGroup }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.custNoGroup"
                          :getPopupContainer="() => this.$refs.SelectBox"
                          :dropdownMatchSelectWidth="false"
                          showSearch
                          optionFilterProp="children"
                          @popupScroll="handlePopupScroll1"
                          allowClear
                          @search="supValue1"
                        >
                          <a-select-option v-for="(item, index) in frontDataZSupplier1" :key="index" :value="item">
                            {{ item }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>

                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="信用账期"
                      :class="requiredCustomConfig.xyzq && iseval(requiredCustomConfig.xyzq.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.xyzqStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.xyzq"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in mapKey(selectData.XYZQ)" :key="item.value" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="授信额度(RMB)"
                      :class="requiredCustomConfig.sxed && iseval(requiredCustomConfig.sxed.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.sxed }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.sxed" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="对账时间"
                      :class="requiredCustomConfig.payDay && iseval(requiredCustomConfig.payDay.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.payDay }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.payDay" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="业务调节系数"
                      :class="requiredCustomConfig.adjustmentFactor && iseval(requiredCustomConfig.adjustmentFactor.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.adjustmentFactor }}</span>
                      <div v-else>
                        <a-input v-model.trim="dataList.adjustmentFactor" allowClear></a-input>
                      </div>
                    </a-form-model-item>
                  </a-col>

                  <!-- <a-col :span="6">
                            <a-form-model-item label="是否需要确认生产稿" :class="requiredCustomConfig.confirmFile && iseval(requiredCustomConfig.confirmFile.isNullRules)?'redsty':''" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                            <span v-if="type=='1'">{{dataList.confirmFile ? '是':''}}</span>
                            <div  v-else>
                                <a-checkbox v-model="dataList.confirmFile" > </a-checkbox>
                                </div>
                            </a-form-model-item>
                            </a-col> -->
                </a-row>
                <!-- <a-row>
                            <a-col :span="6">
                            <a-form-model-item label="采购类型" :class="requiredCustomConfig.cglx && iseval(requiredCustomConfig.cglx.isNullRules)?'redsty':''" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                            <span v-if="type=='1'">{{dataList.cglx}}</span>
                            <div  v-else>
                                <a-select v-model="dataList.cglx"  showSearch allowClear >
                                    <a-select-option style="color:#000000"   value="PCB"> PCB </a-select-option>
                                    <a-select-option  style="color:#000000"  value="板材"> 板材 </a-select-option>
                                </a-select>
                            </div>
                            </a-form-model-item>
                            </a-col>
                          
                            <a-col :span="6">
                            <a-form-model-item label="具体分类" :class="requiredCustomConfig.jtfl && iseval(requiredCustomConfig.jtfl.isNullRules)?'redsty':''" :label-col="{ span: 8}"  :wrapper-col="{ span: 16}" >
                            <span v-if="type=='1'">{{dataList.jtfl}}</span>
                            <div  v-else>
                                <a-input v-model.trim="dataList.jtfl"  allowClear ></a-input>
                            </div>
                            </a-form-model-item>
                            </a-col>
                            
                            <a-col :span="6">
                            <a-form-model-item label="出货单是否需要回签" :class="requiredCustomConfig.chdhq && iseval(requiredCustomConfig.chdhq.isNullRules)?'redsty':''"  :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                            <span v-if="type=='1'">{{dataList.chdhq ? '是':''}}</span>
                            <div  v-else>
                                <a-checkbox v-model="dataList.chdhq" ></a-checkbox>
                            </div>
                            </a-form-model-item>
                            </a-col>
                        </a-row> -->
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="意向客户"
                      :class="requiredCustomConfig.isIntention && iseval(requiredCustomConfig.isIntention.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.isIntention ? "是" : "" }}</span>
                      <div v-else>
                        <div v-if="type == '0'">
                          <a-checkbox v-model="dataList.isIntention" disabled></a-checkbox>
                        </div>
                        <div v-else>
                          <a-checkbox v-model="dataList.isIntention" @change="isIntention"></a-checkbox>
                        </div>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="不需要LOGO"
                      :class="requiredCustomConfig.isNotLogo && iseval(requiredCustomConfig.isNotLogo.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.isNotLogo ? "是" : " " }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.isNotLogo"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="上传LOGO"
                      :class="requiredCustomConfig.path1 && iseval(requiredCustomConfig.path1.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span style="display: flex">
                        <span v-viewer style="height: 30px">
                          <img v-if="type == '1' && src" :src="src" style="height: 34px; margin-right: 10px" />
                        </span>
                        <a-upload
                          accept=".jpg,.png,.gif,.bmp,.jpeg,"
                          name="file"
                          ref="fileRef"
                          :before-upload="beforeUpload1"
                          :customRequest="downloadFilesCustomRequest"
                          :file-list="fileListData"
                          @change="handleChange1"
                          list-type="picture-card"
                          @preview="handlePreview"
                        >
                          <a-button v-if="!src && type != '1' && fileListData.length == 0"> 上传LOGO </a-button>
                          <a-button v-else-if="src && type != '1' && fileListData.length == 0"> 替换LOGO </a-button>
                        </a-upload>
                        <a-button
                          style="font-weight: 500; padding: 4px; height: 28px; margin-top: 2px"
                          v-if="type != '1' && fileListData.length == 0"
                          @click.stop="showCopy(1)"
                          title="ctrl+V 粘贴上传"
                        >
                          粘贴图片
                        </a-button>
                      </span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="报价单类型"
                      :class="requiredCustomConfig.quotationModel && iseval(requiredCustomConfig.quotationModel.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.quotationModel }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.quotationModel"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option
                            v-for="item in mapKey(selectData.QuotationModel)"
                            :key="item.value"
                            :value="item.value"
                            :lable="item.lable"
                          >
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!--  -->
                </a-row>
                <!-- <a-row>
                            <a-col :span="12">
                            <a-form-model-item label="报告" :class="requiredCustomConfig.needReportList && iseval(requiredCustomConfig.needReportList.isNullRules)?'redsty':''" prop="needReportList" :label-col="{ span:4}" :wrapper-col="{ span: 20}" style="width:100%; margin:0" class='heightSty1'>
                                <span v-if="type=='1'">{{dataList.needReportListStr}}</span>
                                <div v-else class="editWrapper">
                                <a-select v-model="dataList.needReportList" mode="multiple" placeholder="请选择" showSearch  allowClear optionFilterProp="lable" style="width:100%;" :getPopupContainer="()=>this.$refs.SelectBox">
                                    <a-select-option v-for="(item,index) in mapKey(selectData.NeedReportList)" :key="index" :value="item.value"  :lable="item.lable" > 
                                    {{ item.lable }}
                                    </a-select-option>
                                </a-select>
                                </div>
                            </a-form-model-item>
                            </a-col>
                        </a-row>                           -->
                <a-row>
                  <a-col :span="12">
                    <a-form-model-item
                      label="上传公司证明文件"
                      :class="requiredCustomConfig.path1 && iseval(requiredCustomConfig.path1.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <span style="display: flex">
                        <span v-viewer>
                          <template v-if="type == '1' && picFilter">
                            <img :src="item" v-for="(item, index) in picFilter" :key="index" style="width: 68px; height: 34px; margin-right: 10px" />
                          </template>
                        </span>
                        <a-upload
                          name="file"
                          ref="fileRef1"
                          :before-upload="beforeUpload2"
                          :customRequest="downloadFilesCustomRequest2"
                          :file-list="fileListData2"
                          @change="handleChange2"
                          list-type="picture-card"
                          @preview="handlePreview"
                        >
                          <a-button v-if="type != '1' && fileListData2.length < 2"> 上传证明文件 </a-button>
                          <a-button v-if="type != '1' && fileListData2.length >= 2"> 替换证明文件 </a-button>
                        </a-upload>
                        <a-button
                          style="font-weight: 500; padding: 4px; height: 28px; margin-top: 2px"
                          v-if="type != '1'"
                          @click.stop="showCopy(2)"
                          title="ctrl+V 粘贴上传"
                        >
                          粘贴图片
                        </a-button>
                      </span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="KA"
                      :class="requiredCustomConfig.ka && iseval(requiredCustomConfig.ka.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.ka ? "是" : " " }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.ka"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="3">
                    <a-form-model-item
                      label="EQ模板"
                      :class="requiredCustomConfig.eqTemplate && iseval(requiredCustomConfig.eqTemplate.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 10 }"
                      :wrapper-col="{ span: 14 }"
                    >
                      <span v-if="type == '1'">{{ dataList.eqTemplate === 0 ? "中文" : dataList.eqTemplate === 1 ? "英文" : "中英文" }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.eqTemplate"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="(item, index) in EQTemplatelist" :key="index" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="3">
                    <a-form-model-item
                      label="叠层模板"
                      :class="requiredCustomConfig.impTate_ && iseval(requiredCustomConfig.impTate_.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 10 }"
                      :wrapper-col="{ span: 14 }"
                    >
                      <span v-if="type == '1'">{{ dataList.impTate_ === 0 ? "中文" : dataList.impTate_ === 1 ? "英文" : "" }}</span>
                      <div v-else>
                        <a-select v-model="dataList.impTate_" showSearch optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                          <a-select-option v-for="(item, index) in impTate_ist" :key="index" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="EQ发送邮件类型"
                      :class="requiredCustomConfig.eqEmailType && iseval(requiredCustomConfig.eqEmailType.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.eqEmailType === 0 ? "带附件" : dataList.eqEmailType === 1 ? "无附件" : "" }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.eqEmailType"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="(item, index) in maillist" :key="index" :value="item.value" :lable="item.lable">
                            {{ item.lable }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="客户唯一key值"
                      :class="requiredCustomConfig.clientLoginKey && iseval(requiredCustomConfig.clientLoginKey.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span>{{ dataList.clientLoginKey }}</span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="EQ上传/下载"
                      :class="requiredCustomConfig.custEqManagement && iseval(requiredCustomConfig.custEqManagement.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.custEqManagement == 1 ? "是" : "否" }}</span>
                      <div v-else>
                        <a-checkbox v-model="custEqManagementbool"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="是否过二审"
                      :class="requiredCustomConfig.isCustQaeTwo && iseval(requiredCustomConfig.isCustQaeTwo.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.isCustQaeTwo == 1 ? "是" : "否" }}</span>
                      <div v-else>
                        <a-checkbox v-model="isCustQaeTwobool"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="钢网回传"
                      :class="requiredCustomConfig.needWorkFile && iseval(requiredCustomConfig.needWorkFile.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.needWorkFile == 1 ? "是" : "否" }}</span>
                      <div v-else>
                        <a-checkbox v-model="needWorkFilebool"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="生产数据回传"
                      :class="requiredCustomConfig.ProductDataUp && iseval(requiredCustomConfig.ProductDataUp.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ productDataUpStr }}</span>
                      <div v-else>
                        <a-select mode="multiple" v-model="productDataUp">
                          <a-select-option v-for="(ite, index) in productDataUpList" :key="index" :value="ite.value">{{ ite.label }}</a-select-option>
                          <!-- <a-select-option value="0">叠层</a-select-option>
                          <a-select-option value="1">MI</a-select-option> -->
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="业务经理"
                      :class="requiredCustomConfig.businessManager && iseval(requiredCustomConfig.businessManager.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.businessManagerStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.businessManager"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in userListData" :key="item.valueMember" :value="item.valueMember" :lable="item.text">
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="业务总监"
                      :class="requiredCustomConfig.businessDirector && iseval(requiredCustomConfig.businessDirector.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.businessDirectorStr }}</span>
                      <div v-else>
                        <a-select
                          v-model="dataList.businessDirector"
                          showSearch
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                        >
                          <a-select-option v-for="item in userListData" :key="item.valueMember" :value="item.valueMember" :lable="item.text">
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="6">
                    <a-form-model-item
                      label="WIP 钢网下载 "
                      :class="requiredCustomConfig.steelDownload && iseval(requiredCustomConfig.steelDownload.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.steelDownload ? "是" : "否" }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.steelDownload"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-model-item
                      label="WIP 叠层下载"
                      :class="requiredCustomConfig.upDownload && iseval(requiredCustomConfig.upDownload.isNullRules) ? 'redsty' : ''"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                    >
                      <span v-if="type == '1'">{{ dataList.upDownload ? "是" : "否" }}</span>
                      <div v-else>
                        <a-checkbox v-model="dataList.upDownload"> </a-checkbox>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-form-model>
            </a-spin>
            <div style="position: absolute; bottom: 4px; right: 0">
              <a-button @click="compile" type="primary" v-show="type == '1' && tabID == '1'" style="margin-right: 20px">编辑(E)</a-button>
              <a-button @click="cancel" type="primary" v-show="type === '0'" style="margin-right: 20px">取消编辑</a-button>
              <a-button type="primary" @click="signNew" style="margin-right: 20px"> 保存(S)</a-button>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="联系人" force-render v-if="guid_ != ''">
          <div class="conta">
            <div style="margin-top: 30px">
              <span style="margin-left: 50px; font-weight: bold">收货地址</span>
              <a-button type="primary" style="width: 130px; float: right; margin-right: 50px" @click="addData">添加新地址</a-button>
              <a-button type="primary" style="width: 70px; float: right; margin-right: 50px" @click="querycilik">查询</a-button>
              <a-input
                placeholder="请输入需查询的终端客户"
                v-model="timcust"
                style="width: 230px; float: right; margin-right: 10px"
                @keyup.enter="querycilik"
              />
            </div>
            <a-list
              itemKey="id"
              :loading="listloading"
              :itemLayout="'vertical'"
              :dataSource="tableDetails"
              :pagination="tableDetails.length ? listpagination : false"
              @change="listhandleTableChange"
              :style="{ height: '600px' }"
              class="container"
            >
              <template>
                <a-list-item v-for="item in tableDetails" :key="item.id">
                  <div class="list-item-content">
                    <a-list-item-meta :title="getTitle(item)" :description="getTitle1(item)" />
                    <a-divider type="vertical" style="height: 40px; border-color: rgb(0, 0, 0)" />
                    <a-list-item-meta :description="getTitle3(item)" />
                    <a-divider type="vertical" style="height: 40px; border-color: rgb(0, 0, 0)" />
                    <a-list-item-meta :title="getTitle4(item)" :description="getTitle5(item)" />
                    <span style="margin-top: -20px">
                      <a @click="compileS(item)">编辑</a>
                      <a-divider type="vertical" style="height: 15px; border-color: rgb(0, 0, 0)" />
                      <a @click="cancelS(item)">删除</a>
                    </span>
                  </div>
                  <span v-if="item.default_" style="font-size: 14px; float: right; margin-top: -20px">默认联系人</span>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <!-- <a-table
                    rowKey="id"
                    :columns="columnsDetails"
                    :dataSource="tableDetails"
                    :pagination="pagination"
                    @change="handleTableChange"
                    :loading="loading"
                    :scroll="{ y:660 }"
                    :class="tableDetails.length ? 'min-table':''"
                >   
                <span slot="num" slot-scope="text, record, index">
                    {{(pagination.current-1)*pagination.pageSize+parseInt(index)+1}}
                </span>          
                <div slot="actionS" slot-scope="text,record">
                    <template  >
                        <a href="javascript:;" @click="compileS(record)">编辑</a>
                        <a-divider type="vertical" />
                        <a-popconfirm
                            title="确定要删除吗？"
                            @confirm="cancelS(record)"
                        >
                            <a href="javascript:;" >删除</a>
                        </a-popconfirm>
                    </template>
                </div>
                </a-table> -->
        </a-tab-pane>
        <a-tab-pane key="3" tab="特殊要求" force-render v-if="guid_ != ''">
          <div>
            <!--<a-button icon="plus"  type='primary'  @click="$refs.addDemand.openModal({})" >新增</a-button>   -->
            <a-button type="dashed" style="width: 100%; color: #000000" icon="plus" @click="$refs.addDemand.openModal({})">新增</a-button>
            <standard-table
              rowKey="id"
              :columns="factoryColumns"
              :dataSource="factoryData"
              :pagination="false"
              :class="factoryData.length ? 'min-table' : ''"
            >
              <span slot="companyArea_" slot-scope="{ text }">
                <template>
                  {{ text }}
                </template>
              </span>
              <span slot="image_" slot-scope="{ record }">
                <template>
                  <a href="javascript:;" v-if="record.image_ != ''" @click="imgCheck(record)">查看</a>
                </template>
              </span>
              <div slot="action" slot-scope="{ record }">
                <template>
                  <a href="javascript:;" @click="$refs.addDemand.openModal(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要删除吗？" @confirm="handleDelFactory(record.id)">
                    <a href="javascript:;">删除</a>
                  </a-popconfirm>
                </template>
              </div>
            </standard-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" tab="终端客户" force-render v-if="guid_ != ''">
          <div style="position: relative; height: 774px">
            <a-button type="dashed" style="width: 100%; color: #000000" icon="plus" @click="addClick()">新增</a-button>
            <a-table
              rowKey="custItem_"
              :columns="columns4"
              :dataSource="custData"
              :pagination="pagination1"
              @change="handleTableChange1"
              :loading="loading"
              :scroll="{ y: 660 }"
              :class="custData.length ? 'min-table' : ''"
            >
              <span slot="num" slot-scope="text, record, index">
                {{ (pagination1.current - 1) * pagination1.pageSize + parseInt(index) + 1 }}
              </span>
              <div slot="actionS" slot-scope="text, record">
                <template>
                  <a href="javascript:;" @click="editClick(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定要删除吗？" @confirm="DelCust(record)">
                    <a href="javascript:;">删除</a>
                  </a-popconfirm>
                </template>
              </div>
            </a-table>
            <div style="position: absolute; bottom: 0; right: 0">
              <a-button type="primary" @click="$refs.queryInfo.openModal({})" style="margin-right: 20px"> 查询 </a-button>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="5" tab="风险警告" force-render v-if="guid_ != ''">
          <div style="position: relative; height: 774px">
            <a-button type="dashed" style="width: 100%; color: #000000" icon="plus" @click="addRisk()">新增</a-button>
            <a-table
              :columns="columns3"
              bordered
              :pagination="false"
              :scroll="{ y: 600, x: 700 }"
              :dataSource="RiskData"
              :rowKey="'id'"
              :loading="riskLoading"
              :class="RiskData.length ? 'min-table' : ''"
            >
              <div slot="action" slot-scope="text, record">
                <template>
                  <a href="javascript:;" @click="updateRisk(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm title="确定该警告失效吗？" @confirm="lapse(record)">
                    <a href="javascript:;">失效</a>
                  </a-popconfirm>
                </template>
              </div>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
      <add-demand ref="addDemand" :demandId="demandId" @ok="getdemand"></add-demand>
      <query-info ref="queryInfo" @queryok="queryok"></query-info>
      <a-modal
        title="查看图片"
        :width="650"
        :visible="visible"
        :confirmLoading="confirmLoading"
        centered
        cancel-text="关闭"
        @cancel="visible = false"
        :mask="true"
        :maskClosable="true"
      >
        <a-spin :spinning="confirmLoading">
          <a-row>
            <a-col :span="8" v-for="(tmp, index) in imgList" :key="index">
              <img :src="tmp" alt="图片" class="imgStyle" @click="viewpicture(tmp)" />
            </a-col>
          </a-row>
        </a-spin>
        <template slot="footer">
          <a-button @click="visible = false">关闭(Esc)</a-button>
        </template>
      </a-modal>
      <a-modal
        :title="risk_type == 'add' ? '新增风险警告' : '编辑风险警告'"
        :visible="RiskVisible"
        @ok="RiskHandleOk"
        @cancel="RiskVisible = false"
        :width="800"
        centered
      >
        <div style="display: flex">
          <span style="width: 80px; color: red">*客户风险:</span
          ><a-textarea :auto-size="{ minRows: 4, maxRows: 6 }" :auto-focus="true" v-model="RiskContent" />
        </div>
      </a-modal>
      <a-modal title="确认弹窗" :visible="dataVisible11" @ok="handleOkOk" @cancel="handleCancelPreview" :width="400" centered>
        <span style="font-size: 16px">确认删除该条数据吗?</span>
      </a-modal>
      <a-modal :title="addtitle" :visible="custVisible" @ok="custOk" @cancel="handleCancelPreview" :width="500" centered class="zdcustomers">
        <a-form-model ref="zdruleForm" :model="custList" :rules="zdrules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item label="终端客户" ref="custItem_" prop="custItem_">
            <a-input v-model="custList.custItem_" allowClear :disabled="editCust" />
          </a-form-model-item>
          <a-form-model-item label="联系人" ref="assistant" prop="assistant">
            <a-input v-model="custList.assistant" allowClear />
          </a-form-model-item>
          <a-form-model-item label="联系人邮箱" ref="assistantEmail" prop="assistantEmail">
            <a-input v-model="custList.assistantEmail" allowClear />
          </a-form-model-item>
          <a-form-model-item label="联系人电话" ref="assistantPhone" prop="assistantPhone">
            <a-input v-model="custList.assistantPhone" allowClear />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal title=" 详情确认" :visible="dataVisible" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">取消</a-button>
        </template>
        <div style="height: 200px; overflow-y: auto">
          <div v-for="(ite, index) in message" :key="index">
            <p>{{ ite }}</p>
          </div>
        </div>
      </a-modal>
    </a-card>
    <div class="bto"></div>
  </div>
</template>
<script>
import AddressParse, { AREA, Utils } from "address-parse";
import axios from "axios";
const requiredCustomConfig = {};
import {
  comSite,
  getList,
  custinfoDetails,
  custinfoSave,
  custManageConfig,
  updateCust,
  custDetails,
  getHeadList,
  getCustConfig,
  changeConfig,
  addConfig,
  deleData,
  newConfig,
  delSite,
  dataSite,
  getAsk,
  deleAsk,
  userList,
  terminalCusts,
  terminalCust,
  terminalCustList,
  deleteTerminalCust,
  updateterminalcust,
  addRiskWarning,
  updateRiskWarning,
  getRiskWarning,
  selectpars,
  updatestatus,
} from "@/services/mkt/CustInfoNew";
import { mktCustNo, mktcustnobyfAC } from "@/services/mkt/Inquiry.js";
import addDemand from "./addDemand";
import queryInfo from "./queryInfo";
import { email } from "@/plugins/validate.js";
import StandardTable from "@/components/table/StandardTable";
import jsonList from "../../../../public/index";
import { getUserInfo } from "@/services/identity/user";
import { mapState, mapMutations } from "vuex";
import Cookie from "js-cookie";
import { modulenouploadfile } from "@/services/supplier/index";
import { requiredLinkConfig } from "@/services/mkt/orderInfo";
import $ from "jquery";
// import e from "express";
const columns3 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: 50,
  },
  {
    title: "风险内容",
    align: "left",
    dataIndex: "content",
    ellipsis: true,
  },
  {
    title: "创建时间",
    align: "left",
    dataIndex: "createTime",
    ellipsis: true,
    width: 200,
  },
  {
    title: "创建人员",
    align: "left",
    dataIndex: "createUser",
    ellipsis: true,
    width: 100,
  },
  {
    title: "更新时间",
    align: "left",
    dataIndex: "lastModifiedTime",
    ellipsis: true,
    width: 200,
  },
  {
    title: "更新人员",
    align: "left",
    dataIndex: "lastModifiedUser",
    ellipsis: true,
    width: 100,
  },
  {
    title: "状态",
    align: "left",
    dataIndex: "status",
    customRender: (text, record, index) => (record.status == 0 ? "失效" : "有效"),
    ellipsis: true,
    width: 80,
  },
  {
    title: "操作",
    key: "action",
    width: 100,
    scopedSlots: { customRender: "action" },
  },
];
let factoryColumns = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "3%",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "要求类别",
    dataIndex: "category_",
    width: "5%",
    scopedSlots: { customRender: "category_" },
  },
  {
    title: "要求简述",
    dataIndex: "askToTell_",
    width: "20%",
    scopedSlots: { customRender: "askToTell_" },
  },
  {
    title: "要求详情",
    dataIndex: "askDetails_",
    scopedSlots: { customRender: "askDetails_" },
  },
  {
    title: "图片",
    dataIndex: "image_",
    width: "15%",
    scopedSlots: { customRender: "image_" },
  },
  {
    title: "操作",
    key: "action",
    width: "10%",
    scopedSlots: { customRender: "action" },
  },
];
const columnsDetails = [
  {
    title: "序号",
    // customRender: (text,record,index) => `${index+1}`,
    width: "3%",
    ellipsis: true,
    scopedSlots: { customRender: "num" },
  },
  {
    title: "默认",
    dataIndex: "default_",
    width: "3%",
    ellipsis: true,
    customRender: (text, record, index) => `${record.default_ ? "是" : "否"}`,
  },
  {
    title: "类型",
    dataIndex: "type_",
    width: "6%",
    ellipsis: true,
  },
  {
    title: "联系人",
    dataIndex: "connactUseName_",
    width: "5%",
    ellipsis: true,
  },
  {
    title: "联系电话",
    dataIndex: "connactPhone_",
    width: "7%",
    ellipsis: true,
  },
  {
    title: "邮箱",
    dataIndex: "mailbox",
    width: "14%",
    ellipsis: true,
  },
  {
    title: "传真",
    dataIndex: "fax",
    width: "8%",
    ellipsis: true,
  },
  {
    title: "收货省份",
    dataIndex: "receiverState_",
    width: "7%",
    ellipsis: true,
  },
  {
    title: "收货城市",
    dataIndex: "receiverCity_",
    width: "5%",
    ellipsis: true,
  },
  {
    title: "收货公司",
    dataIndex: "receiverCompany_",
    width: "12%",
    ellipsis: true,
  },
  {
    title: "收货地址",
    dataIndex: "address_",
    width: "15%",
    ellipsis: true,
  },
  {
    title: "终端客户",
    dataIndex: "terminalCust",
    ellipsis: true,
  },
  {
    title: "操作",
    width: "7%",
    ellipsis: true,
    scopedSlots: { customRender: "actionS" },
  },
];
const columns4 = [
  {
    title: "序号",
    width: "3%",
    ellipsis: true,
    scopedSlots: { customRender: "num" },
  },
  {
    title: "终端客户",
    dataIndex: "custItem_",
    width: "10%",
    ellipsis: true,
  },
  {
    title: "联系人",
    dataIndex: "assistant",
    width: "10%",
    ellipsis: true,
  },
  {
    title: "联系人邮箱",
    dataIndex: "assistantEmail",
    width: "10%",
    ellipsis: true,
  },
  {
    title: "联系人电话",
    dataIndex: "assistantPhone",
    width: "10%",
    ellipsis: true,
  },
  {
    title: "操作",
    width: "7%",
    ellipsis: true,
    scopedSlots: { customRender: "actionS" },
  },
];
export default {
  name: "ClientOperating",
  inject: ["reload"],
  data() {
    return {
      risk_type: "",
      riskinfo: {},
      riskLoading: false,
      RiskData: [],
      RiskVisible: false,
      RiskContent: "",
      userListData: [],
      productDataUp: [],
      productDataUpList: [
        { value: "0", label: "叠层" },
        { value: "1", label: "MI" },
        { value: "2", label: "分孔图" },
      ],
      productDataUpStr: "",
      labelCol: { span: 7 },
      wrapperCol: { span: 17 },
      rules: {
        type_: [{ required: true, message: "联系人类型必须选择", trigger: "blur" }],
        mailbox: [
          {
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
        addPhone: [
          {
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
            message: "请输入正确的号码格式",
            trigger: "blur",
          },
        ],
        // addName: [{ required: true, message: "联系人必须填写", trigger: "blur" }],
      },
      zdrules: {
        assistantEmail: [
          {
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
        assistantPhone: [
          {
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
            message: "请输入正确的号码格式",
            trigger: "blur",
          },
        ],
      },
      regulate: false,
      jsonList,
      path1: "",
      fileListData: [],
      addtitle: "",
      fileListData2: [],
      timcust: "",
      src: "",
      visible: false,
      confirmLoading: false,
      fileList: [],
      uploading: false,
      model: "",
      size: "small",
      column: 5,
      spinning: false,
      dataVisible11: false,
      dataList: {
        kjskh: false,
        contact: "",
        custNo: "",
        institution: "",
        vip: false,
        institutionStr: "",
        custName: "",
        abbreviation: "",
        createTime: "",
        createAccount: "",
        createName: "",
        continent: "",
        continentStr: "",
        category: "",
        categoryStr: "",
        country: "",
        countryStr: "",
        custLevel: "",
        custLevelStr: "",
        ownedGroup: "",
        newOrOld: "",
        newOrOldStr: "",
        businessPerson: "",
        currency: "",
        currencyStr: "",
        email: "",
        tel: "",
        fax: "",
        postcode: "",
        address: "",
        webSite: "",
        invoiceAddress: "",
        invoiceType: "",
        invoiceTypeStr: "",
        taxType: "",
        taxTypeStr: "",
        payCondition: "",
        payConditionStr: "",
        payDay: "",
        payMethod: "",
        payMethodStr: "",
        taxPercentage: "",
        corporation: "",
        socialCreditCode: "",
        taxNo: "",
        chineseSX: "",
        openBank: "",
        bankAccount: "",
        bankUser: "",
        functionary: "",
        ishmd: false,
        ddgDy: "",
        ywz: "",
        xyzq: "",
        sxed: "",
        hylb: "",
        jtfl: "",
        cglx: "",
        confirmFile: false,
        chdhq: false,
        mrCity: "",
        adjustmentFactor: "1",
        custProperty: "",
        custPropertyStr: "",
        businessPhone: "",
        priceType: "",
        hmdCause: "",
        needReportList: [],
        needReportListStr: [],
        hmdDate: undefined,
        eqTemplate: null,
        impTate_: null,
        eqEmailType: null,
        custEqManagement: 0,
        isCustQaeTwo: 0,
        needWorkFile: 0,
      },
      steelDownloadbool: false,
      UpDownloadbool: false,
      needWorkFilebool: false,
      isCustQaeTwobool: false,
      custEqManagementbool: false,
      EQTemplatelist: [
        { value: 0, lable: "中文" },
        { value: 1, lable: "英文" },
        { value: 2, lable: "中英文" },
      ],
      impTate_ist: [
        { value: 0, lable: "中文" },
        { value: 1, lable: "英文" },
      ],
      maillist: [
        { value: 0, lable: "带附件" },
        { value: 1, lable: "无附件" },
      ],
      selectData: {},
      logoUrl: "",
      AuthenPicture: "",
      loading: false,
      listloading: false,
      columnsDetails,
      rightVisible: false,
      selectedRows: [],
      dataSource: [],
      delData: [],
      formInquire: {},
      formNew: {
        custcode: "", //查询信息
        custname: "",
      },
      stat: false, //编辑状态开关
      record: {}, //点击的顾客信息
      guid_: this.$route.query.id, //顾客id
      Qinji: this.$route.query.factoryName, //工厂名称
      tableDetails: [],
      form: {
        default_: false, // 默认联系人
        type_: "", // 联系人类型
        addName: "", // 联系人
        addPhone: "", // 联系电话
        mailbox: "", //邮箱
        provinceData: [], // 收获省份
        receiverCompany_: "", // 收获公司
        address: "", // 收货地址
      },
      receiverState_: "",
      receiverCity_: "",
      formInline: [],
      layout: {
        labelCol: { span: 9 },
        wrapperCol: { span: 12 },
      },
      siteStat: false, //地址编辑状态
      siteId: "", //地址id
      trIndex: null,
      addStat: false, //新增状态
      tabID: "1",
      type: "1",
      factoryColumns: factoryColumns,
      demandId: this.$route.query.id,
      factoryData: [],
      imgList: [],
      ids: "",
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      pagination1: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      listpagination: {
        current: 1,
        pageSize: 8,
        showQuickJumper: true,
        showSizeChanger: true,
        onChange: this.listhandleTableChange,
        onShowSizeChange: this.listhandleTableChange,
        pageSizeOptions: ["8"], // 每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      disCustNo: false,
      UserInfo: {},
      terminalCustList: [],
      mktCustNo: [],
      frontDataZSupplier: [], // 供应商 100条数据的集合
      frontDataZSupplier1: [],
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      valueData1: undefined,
      receiverState_zh: "",
      receiverCity_zh: "",
      columns4,
      columns3,
      custData: [],
      custVisible: false,
      custList: {},
      editCust: false,
      requiredCustomConfig,
      tradeType: null,
      dataVisible: false,
      message: [],
      messageList: [],
      picFilter: [],
      isCtrlPressed: false,
      showCopyType: "",
    };
  },
  components: { addDemand, StandardTable, queryInfo },
  watch: {
    $route(newRoute) {
      this.getData(newRoute.query.id);
    },
    messageList: {
      handler(val) {
        this.get(val);
      },
    },
  },
  computed: {
    ...mapState("account", ["user"]),
    ...mapState("setting", ["editstatus"]),
  },
  async created() {
    let rightHead = "DGV_MKTCustShipLoctionInfo";
    // this.getConfig(rightHead)  获取联系人表头
    this.guid_ = this.$route.query.id;
    this.Qinji = this.$route.query.factoryName;
    this.tradeType = this.$route.query.tradeType;
    this.getData(this.guid_);
    this.getSupplier();
    this.GetCustManageConfig();
    if (this.guid_ != "") {
      this.getAskList(this.guid_);
      this.getCustDetails(this.guid_);
      this.getCustData();
    }
    if (!this.guid_) {
      // this.dataList.functionary = this.user.userName  取消销售工程师默认
      //    var arr = this.userListData.filter(v =>{ return v.valueMember == this.user.userName })
      //    if(arr.length == 0){
      //     this.dataList.functionary = this.user.userName
      //    }
      if (this.$route.query.tradeType == 12) {
        this.dataList.currency = "RMB";
        this.dataList.taxType = "taxincluded";
        this.dataList.invoiceType = "specialinvoice";
        this.dataList.payMethod = "transfer";
        this.dataList.custLevel = "B";
        this.dataList.taxPercentage = "0.13";
      }
      this.getUserInfo();
    }
    this.getCustomConfig();
    this.getRiskData();
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.editstatus) {
      if (confirm("当前为编辑状态数据未保存,请确认是否需要进行跳转页面?")) {
        next();
        this.setedit(false);
      } else {
        next(false);
      }
    } else {
      next();
      this.setedit(false);
    }
  },
  mounted() {
    for (var aa = 0; aa < this.jsonList.length; aa++) {
      for (var b = 0; b < this.jsonList[aa].children.length; b++) {
        delete this.jsonList[aa].children[b].children;
      }
    }
    this.handleChangetype_();
    if (!this.guid_) {
      this.excutive();
    }
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("paste", this.getClipboardFiles);
  },
  methods: {
    ...mapMutations("setting", ["setedit"]),
    getRiskData() {
      let params = {
        linkType: 1,
        custNo: this.$route.query.custNo,
      };
      getRiskWarning(params).then(res => {
        if (res.code) {
          this.RiskData = res.data;
        }
      });
    },
    parseAddress() {
      if (this.form.address != "" && this.$route.query.factoryName != "普林" && this.$route.query.factoryName != "普林科技") {
        const result = AddressParse.parse(this.form.address);
        if (result.length == 1) {
          this.receiverState_zh = result[0].province;
          this.receiverCity_zh = result[0].city;
          const [province, city] = Utils.getTargetAreaListByCode("province", result[0].code, true);
          this.form.provinceData = [province.code, city.code];
          this.receiverState_ = province.code;
          this.receiverCity_ = city.code;
        }
      }
    },
    viewpicture(path) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [path],
        });
      });
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "69" && this.isCtrlPressed) {
        e.preventDefault();
        this.compile();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "83" && this.isCtrlPressed) {
        e.preventDefault();
        this.signNew();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && this.rightVisible && !this.isCtrlPressed) {
        this.handleRight();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible11 && !this.isCtrlPressed) {
        this.handleOkOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.custVisible && !this.isCtrlPressed) {
        this.custOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    getTitle(item) {
      return (
        <span>
          {item.connactUseName_ ? (
            <span style="margin-right: 5px;">
              <a-icon type="user" style="font-size:20px;font-weight:bold;color:black"></a-icon>
              {item.connactUseName_}
            </span>
          ) : null}
          {item.receiverCompany_ ? <span style="font-size:12px"> ({item.receiverCompany_})</span> : null}
        </span>
      );
    },
    getTitle1(item) {
      return (
        <span style="font-size:14px;width:486px;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;display: block; cursor: pointer;">
          {item.connactPhone_ ? (
            <span style="margin-right: 5px;">
              <a-icon type="phone" style="font-size:16px;font-weight:bold;color:gray"></a-icon>
              {item.connactPhone_}
            </span>
          ) : null}
          {item.mailbox ? (
            <span style="margin-right: 5px;" title={item.mailbox}>
              <a-icon type="mail" style="font-size:16px;font-weight:bold;color:gray"></a-icon>
              {item.mailbox}
            </span>
          ) : null}
          {item.fax ? (
            <span style="margin-right: 5px;">
              <a-icon type="printer" style="font-size:16px;font-weight:bold;color:gray"></a-icon> {item.fax}
            </span>
          ) : null}
        </span>
      );
    },
    getTitle3(item) {
      return (
        <span style="font-size:14px">
          <span style="color:black;">
            {item.receiverState_zh && item.receiverCity_zh ? (
              <span style="margin-right: 5px;">
                <a-icon type="environment" style="font-size:16px;font-weight:bold;"></a-icon>
                {item.receiverState_zh}
                {item.receiverCity_zh}
              </span>
            ) : null}
          </span>
          {item.address_ ? <span> ({item.address_})</span> : null}
        </span>
      );
    },
    getTitle4(item) {
      return <span style="font-size:14px">联系人类型： {item.type_}</span>;
    },
    getTitle5(item) {
      return <span style="font-size:14px">终端客户： {item.terminalCust}</span>;
    },
    handleCancelPreview() {
      this.custVisible = false;
      this.dataVisible11 = false;
      this.getCustData();
      const form = this.$refs.zdruleForm;
      if (form) {
        form.clearValidate();
      }
    },
    handlePreview(file) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
    },
    filter(inputValue, path) {
      return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
    },
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.terminalCustList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.terminalCustList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.terminalCustList.forEach((item, index) => {
              if (item.valueMember.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.terminalCustList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    handlePopupScroll1(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.mktCustNo.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.mktCustNo.length;
          }
          // 判断是否有搜索
          if (this.valueData1) {
            this.mktCustNo.forEach((item, index) => {
              if (item.valueMember.indexOf(this.valueData1) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.mktCustNo.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier1 = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    terminalChange() {
      this.$forceUpdate();
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("上传LOGO只支持.jpg/.png图片格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    beforeUpload2(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("图片只支持.jpg/.png图片格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    async downloadFilesCustomRequest(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await modulenouploadfile(formData).then(res => {
        if (res.code) {
          data.onSuccess(res.data);
          this.logoUrl = res.data;
          this.path1 = this.logoUrl.toString(",");
          this.src = this.path1;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async downloadFilesCustomRequest2(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await modulenouploadfile(formData).then(res => {
        if (res.code) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData);
      }
      this.fileListData = fileList;
      if (this.fileListData.length && this.fileListData[0].response) {
        this.logoUrl = this.fileListData[0].response;
        this.path1 = this.logoUrl.toString(",");
        this.src = this.path1;
      }
      if (this.fileListData.length == 0) {
        this.path1 = "";
        this.src = this.path1;
      }
    },
    handleChange2({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData2);
      }
      this.fileListData2 = fileList;
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value;
        let arr = that.terminalCustList.filter(m => m.valueMember.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.terminalCustList.slice(0, 20);
      }
    },
    supValue1(value) {
      if (value) {
        let that = this;
        that.valueData1 = value;
        let arr = that.mktCustNo.filter(m => m.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier1 = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier1 = [];
        }
      } else {
        this.valueData1 = undefined;
        this.frontDataZSupplier1 = this.mktCustNo.slice(0, 20);
      }
    },
    handleChangetype_() {
      if (this.form.default_ == true) {
        this.namerules();
        this.mailerules();
      } else {
        this.remail();
        this.rename();
      }
      if (this.form.type_ == "采购" || this.form.type_ == "发货") {
        this.provincerules();
        this.addressrules();
        this.remail();
      } else if (this.form.type_ == "询价") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else if (this.form.type_ == "跟单员") {
        this.phonerules();
        this.remail();
        this.readdress();
        this.reprovince();
      } else if (this.form.type_ == "工作稿") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else if (this.$route.query.tradeType == 37 || this.$route.query.tradeType == 57) {
        if (this.form.default_ == true && this.form.type_ == "EQ联系人") {
          this.mailerules();
          this.readdress();
          this.reprovince();
        }
      } else if (this.form.type_ == "EQ联系人") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else {
        this.rephone();
        this.readdress();
        this.reprovince();
      }
    },
    namerules() {
      this.rules = { ...this.rules, addName: [{ required: true, message: "联系人必须填写", trigger: "blur" }] };
    },
    phonerules() {
      this.rules = {
        ...this.rules,
        addPhone: [
          {
            required: true,
            message: "联系电话必须填写,请输入正确格式",
            trigger: "blur",
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
          },
        ],
      };
    },
    mailerules() {
      this.rules = {
        ...this.rules,
        mailbox: [
          {
            required: true,
            message: "邮箱必须填写,请输入正确格式",
            trigger: "blur",
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
          },
        ],
      };
    },
    provincerules() {
      this.rules = { ...this.rules, provinceData: [{ required: true, message: "收货省份必须选择", trigger: "blur" }] };
    },
    addressrules() {
      this.rules = { ...this.rules, address: [{ required: true, message: "收货地址必须填写", trigger: "blur" }] };
    },
    rephone() {
      this.rules = {
        ...this.rules,
        addPhone: [
          {
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
            message: "请输入正确的号码格式",
            trigger: "blur",
          },
        ],
      };
    },
    remail() {
      this.rules = {
        ...this.rules,
        mailbox: [
          {
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
      };
    },
    rename() {
      this.rules = { ...this.rules, addName: [] };
    },
    reprovince() {
      this.rules = { ...this.rules, provinceData: [] };
    },
    readdress() {
      this.rules = { ...this.rules, address: [] };
    },
    //省份
    onChangeData(val) {
      if (val.length) {
        this.receiverState_zh = this.jsonList.filter(item => {
          return item.value == val[0];
        })[0].label;
      }
      if (val.length > 1) {
        var arr = [];
        arr = this.jsonList.filter(item => {
          return item.value == val[0];
        })[0].children;
        this.receiverCity_zh = arr.filter(item => {
          return item.value == val[1];
        })[0].label;
      }
      if (val.length) {
        if (val[0] == "110000" || val[0] == "310000" || val[0] == "120000" || val[0] == "500000") {
          this.receiverCity_zh = this.receiverState_zh;
          this.receiverState_zh = this.receiverState_zh.split("市")[0];
        }
      } else {
        this.receiverCity_zh = "";
        this.receiverState_zh = "";
      }
      this.receiverCity_ = val[1];
      this.receiverState_ = val[0];
    },
    mailboxChange() {
      this.$forceUpdate();
    },
    imgCheck(record) {
      this.imgList = record.image_.split(",");
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
    },
    handleCancel1() {
      this.rightVisible = false;
      this.siteStat = false;
      this.receiverState_zh = "";
      this.receiverCity_zh = "";
      this.receiverState_ = "";
      this.receiverCity_ = "";
      this.$refs.ruleForm.resetFields();
    },
    //查询工厂图库
    getdemand() {
      this.getAskList(this.guid_);
    },
    //删除工厂图库
    handleDelFactory(id) {
      deleAsk(id).then(res => {
        this.$message.info("删除成功");
        this.getAskList(this.guid_);
      });
    },
    //获取特殊要求列表
    getAskList(id) {
      getAsk(id).then(res => {
        if (res.success) {
          this.factoryData = res.data;
        } else {
          this.$message.info(res.message);
        }
      });
    },
    seleChange(value, key) {
      this.$forceUpdate();
    },
    //取消编辑
    cancel() {
      this.type = "1";
      this.setedit(false);
      this.messageList = [];
      if (this.guid_ != "") {
        this.getData(this.guid_);
      }
      this.fileListData = [];
      this.fileListData2 = [];
    },
    //tab切换
    callback(key) {
      this.tabID = key;
      this.listpagination.current = 1;
      this.listpagination.pageSize = 8;
      this.timcust = "";
    },
    //编辑
    compile() {
      this.fileListData = [];
      if (this.dataList.logoUrl) {
        let info = {
          uid: 0,
          name: "image.png",
          status: "done",
          url: this.dataList.logoUrl,
          thumbUrl: this.dataList.logoUrl, //缩略图地址
        };
        this.fileListData.push(info);
      }
      if (this.dataList.authenPicture) {
        this.fileListData2 = [];
        let list = this.dataList.authenPicture.split(",");
        list.forEach((e, index) => {
          let info = {
            uid: index,
            name: "image.png",
            status: "done",
            url: e,
            thumbUrl: e, //缩略图地址
          };
          this.fileListData2.push(info);
        });
      }
      if (this.dataList.ddgDy && [67,97,98].includes(Number(this.tradeType))) {
        this.dataList.ddgDy = this.dataList.ddgDy.split(",");
      }
      this.stat = true;
      this.type = "0";
      this.setedit(true);
    },
    // 获取下拉选择项
    GetCustManageConfig() {
      let factory = this.tradeType;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("CustSelectPar"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.selectData = data[a].data; //本地缓存
          }
        }
      } else {
        selectpars(3, factory).then(res => {
          if (res.code) {
            this.selectData = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.selectData) != "{}") {
              if (data == null) {
                arr.push({ data: this.selectData, token, factory });
                localStorage.setItem("CustSelectPar", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.selectData, token, factory });
                localStorage.setItem("CustSelectPar", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    mapKey(data) {
      if (!data || JSON.stringify(data) == "{}") {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    getSupplier() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("mktCustNo"));
      var factory = "";
      if (this.tradeType) {
        factory = this.tradeType;
      } else {
        factory = this.user.factoryId;
      }
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (let index = 0; index < data.length; index++) {
          if (data[index].token == token && data[index].factory == factory) {
            this.mktCustNo = data[index].data; //本地缓存
            this.frontDataZSupplier1 = data[index].data.slice(0, 20);
          }
        }
      } else {
        if (factory == 58 || factory == 59) {
          mktcustnobyfAC(factory).then(res => {
            if (res.code) {
              let that = this;
              that.mktCustNo = res.data;
              that.frontDataZSupplier1 = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.mktCustNo, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.mktCustNo, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          mktCustNo().then(res => {
            if (res.code) {
              let that = this;
              that.mktCustNo = res.data;
              that.frontDataZSupplier1 = res.data.slice(0, 20);
              let token = Cookie.get("Authorization");
              let arr = [];
              if (data == null) {
                arr.push({ data: that.mktCustNo, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.mktCustNo, token, factory });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
    },
    //获取详情
    getData(guid) {
      if (guid != "") {
        custinfoDetails(guid).then(res => {
          if (res.code) {
            this.dataList = res.data;
            this.custEqManagementbool = this.dataList.custEqManagement == 1 ? true : false;
            this.needWorkFilebool = this.dataList.needWorkFile == 1 ? true : false;
            this.productDataUp = !this.dataList.productDataUp ? [] : this.dataList.productDataUp.split(",");
            this.productDataUpStr = "";
            for (const num of this.productDataUp) {
              const matchedItem = this.productDataUpList.find(item => item.value === num.toString());
              if (matchedItem) {
                if (this.productDataUpStr == "") {
                  this.productDataUpStr = matchedItem.label;
                } else {
                  this.productDataUpStr += "，" + matchedItem.label;
                }
              }
            }
            this.isCustQaeTwobool = this.dataList.isCustQaeTwo == 1 ? true : false;
            this.src = this.dataList.logoUrl;
            if (this.dataList.authenPicture) {
              this.picFilter = this.dataList.authenPicture.split(",");
            } else {
              this.picFilter = [];
            }
            if (this.dataList.institution) {
              this.dataList.institution = this.dataList.institution.toString();
            }
            if (this.dataList.needReportList) {
              this.dataList.needReportList = this.dataList.needReportList.split(",");
            } else {
              this.dataList.needReportList = [];
            }
            const token = Cookie.get("Authorization");
            const data = JSON.parse(localStorage.getItem("terminalCustList"));
            if (
              data &&
              token &&
              data.filter(item => {
                return item.token == token;
              }).length &&
              data.filter(item => {
                return item.tradeType == this.dataList.tradeType;
              }).length
            ) {
              for (var aa = 0; aa < data.length; aa++) {
                if (data[aa].token == token && data[aa].tradeType == this.dataList.tradeType) {
                  this.terminalCustList = data[aa].data; //本地缓存
                  this.frontDataZSupplier = data[aa].data.slice(0, 20);
                }
              }
            } else {
              terminalCusts(this.dataList.tradeType).then(res => {
                if (res.data) {
                  this.terminalCustList = res.data;
                  this.frontDataZSupplier = res.data.slice(0, 20);
                  let token = Cookie.get("Authorization");
                  let arr = [];
                  if (res.data.length) {
                    if (data == null) {
                      arr.push({ data: this.terminalCustList, token, tradeType: this.dataList.tradeType });
                      localStorage.setItem("terminalCustList", JSON.stringify(arr)); //本地缓存
                    } else {
                      data.push({ data: this.terminalCustList, token, tradeType: this.dataList.tradeType });
                      localStorage.setItem("terminalCustList", JSON.stringify(data)); //本地缓存
                    }
                  }
                } else {
                  this.$message.error(res.message);
                }
              });
            }
            userList(this.dataList.tradeType).then(res => {
              if (res.code) {
                this.userListData = res.data;
              }
            });
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.type = "2";
      }
    },
    excutive() {
      let par = -1;
      userList(par).then(res => {
        if (res.code) {
          this.userListData = res.data;
        }
      });
    },
    isIntention() {
      if (this.dataList.isIntention) {
        this.dataList.custNo = "";
        this.disCustNo = true;
      } else {
        this.disCustNo = false;
      }
    },
    reportHandleCancel() {
      this.dataVisible = false;
      this.message = [];
    },
    get(val) {
      $("#formDataElem .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("guid_") != -1) {
        newIsNullRules = newIsNullRules.replace(/guid_/g, "this.guid_");
      }
      if (val.indexOf("UserInfo.factoryName") != -1) {
        newIsNullRules = newIsNullRules.replace(/UserInfo.factoryName/g, "this.UserInfo.factoryName");
      }
      if (val.indexOf("Qinji") != -1) {
        newIsNullRules = newIsNullRules.replace(/Qinji/g, "this.Qinji");
      }
      return eval(newIsNullRules);
    },
    //保存客户管理详情
    signNew() {
      if (this.type == "1") {
        this.$message.warning("非编辑状态不允许保存");
        return;
      }
      let params = this.dataList;
      params.guid_ = this.guid_;
      var a = /^[1-9]\d*$/;
      var r = /^(\d|[1-9]\d+)(\.\d+)?$/;
      for (var key in this.requiredCustomConfig) {
        let rr = "";
        if (this.requiredCustomConfig[key].linkRules) {
          rr = eval(this.requiredCustomConfig[key].linkRules);
        }
        if (
          (this.requiredCustomConfig[key] && this.iseval(this.requiredCustomConfig[key].isNullRules) && !params[key]) ||
          (this.requiredCustomConfig[key].linkRules && params[key] && !rr.test(params[key]))
        ) {
          this.message.push(this.requiredCustomConfig[key].message);
          this.messageList.push(this.requiredCustomConfig[key].lable);
        }
      }

      if (params.taxPercentage && !r.test(params.taxPercentage)) {
        this.message.push("税率请输入大于0的正数");
        this.messageList.push("税率");
      }
      if (this.message.length) {
        this.dataVisible = true;
        return;
      }
      params.institution = Number(params.institution);
      params.logoUrl = this.fileListData[0]?.response || this.fileListData[0]?.url;
      let str = "";
      this.fileListData2.forEach(e => {
        if (e.response) {
          str += e.response + ",";
        } else if (e.url) {
          str += e.url + ",";
        }
      });
      str = str.substring(0, str.length - 1);
      this.AuthenPicture = str;
      if (this.AuthenPicture.split(",").length > 2) {
        this.$message.warning("公司证明文件最多只能上传两张");
        return;
      }
      params.authenPicture = this.AuthenPicture;
      if (params.needReportList.length) {
        params.needReportList = params.needReportList.toString();
      } else {
        params.needReportList = "";
      }
      params.eqTemplate = params.eqTemplate === 0 || params.eqTemplate === 1 || params.eqTemplate === 2 ? params.eqTemplate : null;
      params.impTate_ = params.impTate_ === 0 || params.impTate_ === 1 ? params.impTate_ : null;
      params.eqEmailType = params.eqEmailType === 0 || params.eqEmailType === 1 ? params.eqEmailType : null;
      params.custEqManagement = Number(this.custEqManagementbool);
      params.isCustQaeTwo = Number(this.isCustQaeTwobool);
      params.needWorkFile = Number(this.needWorkFilebool);
      params.productDataUp = this.productDataUp.join(",");
      params.ddgDy = typeof params.ddgDy == "object" ? params.ddgDy.join(",") : params.ddgDy;
      if (this.guid_) {
        // 编辑保存
        if (this.stat) {
          updateCust(params).then(res => {
            if (res.code) {
              this.$message.success(res.message);
              this.getData(this.guid_);
              this.fileListData = [];
              this.fileListData2 = [];
              this.type = "1";
              this.setedit(false);
              this.stat = false;
              this.messageList = [];
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          this.$message.warning("非编辑状态不可点保存");
        }
      } else {
        //新增保存
        custinfoSave(params).then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.guid_ = res.data;
            this.getAskList(res.data);
            this.getCustDetails(res.data);
            this.getCustData();
            this.tradeType = this.$route.query.tradeType;
            this.getData(res.data);
            this.getSupplier();
            this.GetCustManageConfig();
            this.getCustomConfig();
            //this.reload()
            this.type = "1";
            this.setedit(false);
            //this.$router.push('CRMNew')
            this.fileListData = [];
            this.fileListData2 = [];
            this.messageList = [];
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //默认联系人
    onChange(e) {
      if (e.target.checked) {
        this.default_ = true;
      } else {
        this.default_ = false;
      }
    },
    //新增联系人
    addData() {
      this.rightVisible = true;
      this.form.addName = "";
      this.form.addPhone = "";
      this.form.default_ = false;
      this.form.address = "";
      this.receiverState_ = "";
      this.form.provinceData = [];
      this.receiverCity_ = "";
      this.form.receiverCompany_ = "";
      this.form.type_ = "";
      this.form.mailbox = "";
      this.form.fax = "";
      this.form.terminalCust = "";
      this.receiverState_zh = "";
      this.receiverCity_zh = "";
      this.remail();
      this.readdress();
      this.reprovince();
      this.rephone();
    },
    querycilik() {
      this.getCustDetails(this.$route.query.id);
    },
    //新增联系人
    handleRight() {
      const form = this.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          let params = {
            id: this.guid_,
            pGuid_: this.guid_,
            address_: this.form.address,
            connactUseName_: this.form.addName,
            connactPhone_: this.form.addPhone,
            default_: this.form.default_,
            // receiverState_:this.receiverState_.length ? this.receiverState_:'',
            receiverState_: this.receiverState_,
            receiverCity_: this.receiverCity_,
            receiverCompany_: this.form.receiverCompany_,
            type_: this.form.type_,
            mailbox: this.form.mailbox,
            fax: this.form.fax,
            terminalCust: this.form.terminalCust,
            receiverState_zh: this.receiverState_zh,
            receiverCity_zh: this.receiverCity_zh,
          };
          this.rightVisible = false;
          if (this.siteStat) {
            params.id = this.siteId;
            comSite(params)
              .then(res => {
                if (res.code) {
                  this.getCustDetails(this.guid_);
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.siteStat = false;
                this.form.default_ == false;
              });
          } else {
            newConfig(params)
              .then(res => {
                if (res.code) {
                  this.getCustDetails(this.guid_);
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.siteStat = false;
                this.form.default_ == false;
              });
          }
        }
      });

      // if(this.default_){
      //     params.default_='1'
      // }else {
      //     params.default_='0'
      // }
    },
    //获取控件配置
    getConfig(name) {
      getHeadList(name).then(res => {
        if (name == "DGV_MKTCustShipLoctionInfo") {
          res.columns.forEach(item => {
            let num = {};
            if (item.isVisible) {
              num.title = item.headerTxt_PRC;
              num.dataIndex = item.names.replace(item.names[0], item.names[0].toLowerCase());
              num.align = "left";
              this.columnsDetails.push(num);
            }
          });
          this.columnsDetails.push({
            title: "操作列表",
            scopedSlots: { customRender: "actionS" },
            // fixed: "right"
          });
          this.columnsDetails.unshift({
            title: "序号",
            scopedSlots: { customRender: "index" },
            // fixed: "right"
          });
        }
      });
    },

    //地址删除
    cancelS(value) {
      this.dataVisible11 = true;
      this.ids = value.id;
    },
    //地址删除
    handleOkOk() {
      delSite(this.ids).then(res => {
        this.getCustDetails(this.guid_);
      });
      this.dataVisible11 = false;
    },
    //地址编辑
    compileS(value) {
      let receiverState_ = "";
      let receiverCity_ = "";
      //根据id转换省市区的树形结构得到name
      let searchLeaf = (items, hittext) => {
        for (let index in items) {
          if (items[index].label == hittext) {
            receiverState_ = items[index].value;
          }
        }
      };
      searchLeaf(this.jsonList, value.receiverState_);
      //根据name转换市区的树形结构得到id
      let searchLeaf1 = (items, hittext) => {
        for (let index in items) {
          if (items[index].label == hittext) {
            receiverCity_ = items[index].value;
          }
          searchLeaf1(items[index].children, hittext);
        }
      };
      searchLeaf1(this.jsonList, value.receiverCity_);

      this.form.provinceData = [];
      this.siteId = value.id;
      this.rightVisible = true;
      this.siteStat = true;
      this.form.addName = value.connactUseName_;
      this.form.addPhone = value.connactPhone_;
      this.form.address = value.address_;
      this.form.receiverCompany_ = value.receiverCompany_;
      if (receiverState_) {
        this.form.provinceData.push(receiverState_, receiverCity_);
      }
      this.form.type_ = value.type_;
      this.form.mailbox = value.mailbox;
      this.form.fax = value.fax;
      this.form.terminalCust = value.terminalCust;
      this.receiverState_zh = value.receiverState_zh;
      this.receiverCity_zh = value.receiverCity_zh;
      this.receiverState_ = receiverState_;
      this.receiverCity_ = receiverCity_;
      if (value.default_ == "是") {
        this.form.default_ = true;
      } else {
        this.form.default_ = false;
      }
      this.handleChangetype_();
    },
    handleOk(e) {
      this.visible = false;
      getList(this.formNew).then(res => {
        this.dataSource = res;
        this.formNew.custname = "";
        this.formNew.custcode = "";
      });
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.getCustDetails(this.guid_);
    },
    handleTableChange1(pagination) {
      this.pagination1.current = pagination.current;
      this.pagination1.pageSize = pagination.pageSize;
      if (this.queryData) {
        this.getCustData(this.queryData);
      } else {
        this.getCustData();
      }
    },
    listhandleTableChange(page, pageSize) {
      if (typeof page == "object") {
        this.listpagination.current = Number(page.target._value);
      } else {
        this.listpagination.current = page;
      }
      if (pageSize) {
        this.listpagination.pageSize = pageSize;
      }

      this.getCustDetails(this.guid_);
    },
    // 获取表格对应的行id
    Rowclick(record, index) {
      return {
        on: {
          click: () => {
            if (!this.stat) {
              if (this.trIndex == null) {
                this.trIndex = index;
                document.getElementsByClassName("ant-table-row")[index].style.background = "#FFF9E6";
              } else if (this.trIndex != index) {
                document.getElementsByClassName("ant-table-row")[this.trIndex].style.background = "";
                document.getElementsByClassName("ant-table-row")[index].style.background = "#FFF9E6";
                this.trIndex = index;
              }
            }
            this.record = record;
            let strGuid = record.guid_;
            this.guid_ = record.guid_;
            if (!this.stat && !this.addStat) {
              this.getCustDetails(strGuid);
            }
          },
        },
      };
    },
    //刷新联系人列表
    getCustDetails(id) {
      let parmas = {};
      parmas.PageIndex = this.listpagination.current;
      parmas.PageSize = this.listpagination.pageSize;
      if (this.timcust) {
        parmas.timcust = this.timcust;
      }
      parmas.id = id;
      this.listloading = true;
      dataSite(parmas)
        .then(res => {
          res.data.items.forEach(item => {
            if (item.default_) {
              item.default_ = "是";
            }
            if (item.receiverState_) {
              //根据id转换省市区的树形结构得到name
              let searchLeaf = (items, hittext) => {
                for (let index in items) {
                  if (items[index].value == hittext) {
                    item.receiverState_ = items[index].label;
                    return false;
                  }
                }
              };
              searchLeaf(this.jsonList, item.receiverState_);
            }

            if (item.receiverCity_) {
              //根据id转换省市区的树形结构得到name
              let searchLeaf = (items, hittext) => {
                for (let index in items) {
                  if (items[index].value == hittext) {
                    item.receiverCity_ = items[index].label;
                    return false;
                  }
                  searchLeaf(items[index].children, hittext);
                }
              };
              searchLeaf(this.jsonList, item.receiverCity_);
            }
          });
          this.tableDetails = res.data.items;
          this.listpagination.total = res.data.totalCount;
        })
        .finally(res => {
          this.listloading = false;
        });
    },
    getUserInfo() {
      let id = this.user.id;
      const data = JSON.parse(localStorage.getItem("UserInfo"));
      if (data && data.data.id == id && data.expires > Date.now()) {
        this.UserInfo = data.data; //本地缓存
      } else {
        getUserInfo(id).then(res => {
          if (res) {
            this.UserInfo = res;
            let expires = Date.now() + 8000 * 60 * 60; // 8小时有效期
            localStorage.setItem("UserInfo", JSON.stringify({ data: this.UserInfo, expires })); //本地缓存
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    ishmdClick() {
      if (this.dataList.ishmd) {
        this.dataList.hmdCause = "";
      }
    },
    getCustData(queryData) {
      let parmas = {};
      parmas.PageIndex = this.pagination1.current;
      parmas.PageSize = this.pagination1.pageSize;
      if (queryData) {
        parmas.CustItem = queryData;
      }
      parmas.id = this.guid_;
      terminalCust(parmas).then(res => {
        if (res.code) {
          this.custData = res.data.items;
          this.pagination1.total = res.data.totalCount;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    custOk() {
      const form = this.$refs.zdruleForm;
      form.validate(valid => {
        if (valid) {
          this.custVisible = false;
          let params = this.custList;
          params.custNo_ = this.dataList.custNo;
          params.tradeType_ = this.dataList.tradeType;
          if (this.editCust) {
            updateterminalcust(params).then(res => {
              if (res.code) {
                this.$message.success("编辑成功");
                this.getCustData();
              } else {
                this.$message.error(res.message);
              }
            });
          } else {
            terminalCustList(params).then(res => {
              if (res.code) {
                this.$message.success("新增成功");
                this.getCustData();
              } else {
                this.$message.error(res.message);
              }
            });
          }
        }
      });
    },
    queryok() {
      this.queryData = this.$refs.queryInfo.form.custItem_;
      this.pagination1.current = 1;
      this.getCustData(this.queryData);
      this.$refs.queryInfo.form.custItem_ = "";
    },
    editClick(record) {
      this.addtitle = "编辑终端客户";
      this.custList = record;
      this.custVisible = true;
      this.editCust = true;
    },
    addClick() {
      this.addtitle = "新增终端客户";
      this.custVisible = true;
      this.custList = {};
      this.editCust = false;
    },
    addRisk() {
      this.RiskVisible = true;
      this.RiskContent = "";
      this.risk_type = "add";
    },
    updateRisk(record) {
      this.RiskVisible = true;
      this.RiskContent = record.content;
      this.risk_type = "update";
      this.riskinfo = record;
    },
    RiskHandleOk() {
      if (!this.RiskContent || this.RiskContent == "") {
        this.$message.error("请输入客户风险内容");
        return;
      }
      this.RiskVisible = false;
      if (this.risk_type == "add") {
        let params = {
          linkType: 1,
          status: 1,
          content: this.RiskContent,
          custNo: this.dataList.custNo,
          orderNo: "",
          joinFactoryId: [Number(this.dataList.tradeType)],
        };
        addRiskWarning(params).then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.getRiskData();
          } else {
            this.$message.error(res.message);
          }
        });
      } else if (this.risk_type == "update") {
        this.riskinfo.content = this.RiskContent;
        updateRiskWarning(this.riskinfo).then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.getRiskData();
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    lapse(record) {
      let params = {
        status: 0,
        id: record.id,
      };
      updatestatus(params).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.getRiskData();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    DelCust(record) {
      deleteTerminalCust(record.custItem_).then(res => {
        if (res.code) {
          this.$message.success("删除成功");
          this.getCustData();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getCustomConfig() {
      var factory = "";
      if (this.tradeType) {
        factory = this.tradeType;
      } else {
        factory = this.user.factoryId;
      }
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("requiredCustomConfig"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.requiredCustomConfig = data[a].data; //本地缓存
          }
        }
      } else {
        requiredLinkConfig(factory, 1).then(res => {
          if (res.code) {
            this.requiredCustomConfig = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.requiredCustomConfig) != "{}") {
              if (data == null) {
                arr.push({ data: this.requiredCustomConfig, token, factory });
                localStorage.setItem("requiredCustomConfig", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.requiredCustomConfig, token, factory });
                localStorage.setItem("requiredCustomConfig", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    showCopy(type) {
      window.addEventListener("paste", this.getClipboardFiles);
      this.showCopyType = type;
      try {
        navigator.clipboard.read().then(res => {
          const clipboardItems = res;
          if (clipboardItems[0].types[0].indexOf("image") > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then(b => {
              const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {
                type: clipboardItems[0].types[0],
              });
              this.file = files;
              this.getClipboardFiles();
            });
          } else {
            this.$message.error("粘贴内容不是图片");
            return;
          }
        });
      } catch (e) {
        //console.log('出错了')
      }
    },
    bodyClick() {
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload1(file);
      this.beforeUpload2(file); // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload1(file) || this.beforeUpload2(file)) {
        // **** return true 之后进行上传
        this.startloading = true;
        const formData = new FormData();
        formData.append("file", file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSSupplier-module-no/up-load-file", // 接口地址/api/app/e-mSSupplier-module-no/up-load-file
          method: "post",
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => {
            if (res.code) {
              file.status = "done";
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              if (this.showCopyType == "1") {
                this.handleChange1(file, arr);
              } else {
                this.handleChange2(file, arr);
              }
            } else {
              this.$message.error(data.message || "网络异常,请重试或检查网络连接状态");
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
    TimeChange(value, dateString) {
      this.dataList.hmdDate = dateString;
      console.log("dateString", this.dataList.hmdDate);
    },
  },
};
</script>

<style lang="less" scoped>
.picstyle {
  /deep/.ant-form-item-label {
    width: 140.5px;
  }
  /deep/.ant-form-item-control-wrapper {
    width: 707px;
  }
}
/deep/.ant-upload-picture-card-wrapper {
  width: auto;
  display: flex;
}
/deep/.ant-col-20 {
  width: 83.33%;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item-uploading-text {
  margin-top: 7px;
  margin-left: 3px;
}
/deep/.ant-table-pagination.ant-pagination {
  margin: 13px 0 0 10px;
}
.conta {
  /deep/.ant-spin-container {
    height: 600px;
  }
  /deep/.ant-pagination {
    float: left;
    margin-top: 42px;
    margin-left: 30px;
  }
}

.container {
  //overflow-y:scroll;
  height: 600px;
}

// .container::-webkit-scrollbar{
//     display: none;
// }
/deep/.ant-list-item-meta-content {
  margin-left: 20px;
}
/deep/.ant-list-item {
  padding: 6px 25px 6px 5px;
}
/deep/.ant-list-vertical .ant-list-item-meta {
  margin-bottom: 0px;
}
/deep/.ant-list {
  margin-top: 30px;
}
/deep/.ant-list-item-no-flex {
  margin: 15px 50px 0 50px;
  height: 60px;
  border: 2px solid #e8e8e8;
}
/deep/.ant-list-split .ant-list-item:last-child {
  border-bottom: 2px solid #e8e8e8;
}
/deep/.ant-list-vertical .ant-list-item-meta-title {
  margin-bottom: 0;
}
/deep/.list-item-content {
  display: flex;
  align-items: center;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.searchPosElem1 {
  background-color: #fafafa;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
.zdcustomers {
  /deep/.ant-form-item .ant-form-item-label {
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
  }

  /deep/.ant-form-item .ant-form-item-control-wrapper .ant-form-item-control {
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
  }
  /deep/.ant-form {
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;
  }
}

/deep/.ant-upload-list-picture-card {
  .ant-upload-list-item {
    padding: 1px;
    border-radius: 0px;
  }
}
/deep/.ant-upload-list-item-list-type-picture-card {
  width: 82px;
  height: 38px;
  margin: 0;
  margin-top: -3px;
}
/deep/.ant-upload-list-picture-card-container {
  width: 90px;
  height: 20px;
  margin: 0;
}
/deep/.ant-upload-select-picture-card > .ant-upload {
  padding: 3px;
  .ant-btn {
    height: 24px;
  }
}
/deep/.ant-upload-select-picture-card {
  height: 24px;
  /* margin-right: 8px; */
  margin-bottom: -12px;
}
/deep/.ant-modal-title {
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-cascader-picker {
  color: #000000;
}

/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
}
/deep/.ant-tabs-nav .ant-tabs-tab {
  padding: 10px 16px !important;
}
.custom-dropdown {
  position: absolute;
  z-index: 1000;
  width: 100%;
  top: 35px;
  left: 0;
}
/deep/.heightSty1 {
  .ant-form-item-label {
    width: 141px;
  }
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      // width:708px;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
        // .edit{
        //  line-height: 28px!important;
        // text-overflow: unset!important;
        // white-space: unset!important;
        // }
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        margin-top: 1px !important;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice__remove {
            right: 2px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 25px !important;
            margin-top: 3px;
            line-height: 25px !important;
            width: 14%;
            padding: 0 10px 0 5px !important;
          }

          .ant-select-selection__clear {
            top: 18px;
          }
        }
      }
    }
  }
}
.custinfo {
  min-width: 1681px;
  overflow: auto;
  font-weight: 500;
}
/deep/.redsty {
  .ant-col-16 {
    .ant-form-item-control {
      .ant-form-item-children {
        vertical-align: top;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        width: 100%;
        display: inline-block;
      }
    }
  }
  label {
    color: red !important;
  }
}
/deep/.standard-table {
  .ant-table-body {
    max-height: 660px !important;
    // min-height:658px;
  }
}
/deep/.min-table {
  .ant-table-body {
    min-height: 660px;
  }
}
/deep/.ant-tabs-bar {
  margin: 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table {
  .ant-table-thead > tr > th {
    padding: 6px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 5.5px 4px;
    border-right: 1px solid #efefef;
  }
}

/deep/.ant-card-body {
  padding: 0;
  height: 769px;
}
.bto {
  height: 54px;
  border: 2px solid #f5f5f5;
  background: #fff;
}
/deep/ .ant-descriptions-row th {
  width: 150px !important;
}
.box /deep/.ant-card-body {
  padding: 10px !important;
}
.leftTab /deep/ .ant-table {
  height: 600px !important;
}
.ant-table-wrapper {
  max-height: calc(100vh - 230px);
  // overflow-y: auto;
}
.form {
  width: 50%;
  padding-top: 20px;
  max-height: calc(100vh - 230px);
  overflow-y: auto;
}
.forms {
  width: 100%;
  padding-top: 20px;
  // margin-bottom: 300px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}
.inputStyle {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  align-content: center;
  margin-bottom: 10px;
}
// .inputStyle input{
//   /*width: 40%;*/
//   /*min-width: 100px;*/
//   /*max-width: 50%;*/
// }
.inputStyle span {
  display: block;
  width: 105px;
  text-align: center;
}
.inputs {
  display: flex;
}
.nameTitle {
  display: block;
  width: 30%;
  text-align: right;
  margin-right: 10px;
}
.ant-form-item {
  margin-bottom: 5px;
  .tmp {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    display: inline-block;
  }
}
.box_right {
  width: 100%;
  float: right;
  position: relative;
  border: 1px solid rgb(221, 219, 219);
  z-index: 10;
  max-height: calc(100vh - 230px);
  overflow-y: auto;
  border-radius: 5px;
}
.claim {
  width: 500%;
  border: 1px solid rgb(221, 219, 219);
  border-radius: 5px;
}
.claim:hover {
  outline: none !important;
  border-color: #ffb029;
  box-shadow: 0 0 1px #ffb029;
}
.claim:focus {
  outline: none !important;
  border-color: #ffb029;
  box-shadow: 0 0 5px #ffb029;
}
.table_bottom {
  width: 98%;
  height: 250px;
  border: 1px solid rgb(221, 219, 219);

  margin: auto;
}
.SearchFor {
  float: left;
  border: 1px solid rgb(221, 219, 219);
  text-align: center;
  padding: 10px 0 10px 10px;
}

.addClass {
  color: red;
}
.classCol {
  color: red !important;
}
.imgStyle {
  width: 100%;
  /*height: 150px;*/
  border: 5px solid #ffffff;
  border-radius: 20px;
}
/deep/.ant-form {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
}
/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;

  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 33px;
    .ant-select {
      width: 120px;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-family: PingFangSC-Regular, Sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    background-color: #fafafa;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font-family: PingFangSC-Regular, Sans-serif;
      color: #000000;
      font-weight: 500;
    }
  }
  .ant-form-item-control-wrapper {
    color: #000000;
    font-family: PingFangSC-Regular, Sans-serif;
    font-weight: 500;
    .ant-form-item-control {
      .ant-form-item-children {
        display: block;
        min-height: 34px;
        line-height: 32px;
        .ant-checkbox-wrapper {
          height: 34px;
          // line-height: 28px;
        }
        .ant-select-selection--single {
          height: 33px;
        }
        .ant-select-selection__rendered {
          line-height: 33px;
        }
        .ant-select {
          height: 34px;
        }
        .ant-input {
          height: 34px;
        }
      }
      line-height: inherit;
      padding: 3px 10px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
    }
  }
}
</style>
