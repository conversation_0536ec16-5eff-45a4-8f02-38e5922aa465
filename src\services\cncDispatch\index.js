import { request, METHOD } from '@/utils/request';
// 分派明细列表
export function cncDispatchList (OrderNo,fac) {
    return request(`/api/app/e-mSTPub-cNCDispatch/c-nCDispatch-list?OrderNo=${OrderNo}&fac=${fac}`, METHOD.GET,)
}
// 生产工具列表
export function protoollist (params) {
    return request(`/api/app/pro-tool/pro-tool-list`, METHOD.GET,params)
}
// 显示工厂订单
export function cncMucount () {
    return request(`/api/app/e-mSTPub-cNCDispatch/c-nCMucount`, METHOD.GET,)
}
// 显示人员订单数
export function projectBackEndPeopleList () {
    return request('/api/app/e-mSTPub-cNCDispatch/c-nCUser-info', METHOD.GET)
}
// 人员对应订单(显示用户详细订单)
export function peopleOrderList (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/user-data-info`, METHOD.GET,params)
}
// 后端订单分派
export function projectBackEndAssign (params) {
    return request('/api/app/engineering-backend/send-order', METHOD.POST,params)
}
// 获取工厂id （查询弹窗）
export function getFactoryList () {
    return request('/api/app/e-mSTPub-factory-configure/factory-id-and-code-list-rout', METHOD.POST,)
}
// 订单分派
export function cncSend (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/c-nCSend`, METHOD.GET,params)
}
// 分派回退
export function cncSendBack (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/c-nCSend-back`, METHOD.GET,params)
}
// 订单完结
export function cncOrderFinish (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/c-nCOrder-finish`, METHOD.GET,params)
}
// 获取取单设置
export function orderRetrievalSettings (params) {
    return request(`/api/app/e-mSTSys-user-assignment-cNC/order-retrieval-settings?userid=${params}`, METHOD.GET,)
}
// 取单设置
export function RetrievalSettings (params) {
    return request(`/api/app/e-mSTSys-user-assignment-cNC/order-retrieval-settings`, METHOD.POST,params)
}
// 自动取单
export function cnCToolInfo (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/c-nCTool-order`, METHOD.GET,params)
}
// 完成
export function finish (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/finish?Pdctno=${params}`, METHOD.POST,)
}
// 节点回退
export function backmake (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/backmake?Pdctno=${params}`, METHOD.POST,params)
}
// 文件替换
export function ossUp (Pdctno,params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/oss-up?Pdctno=${Pdctno}&alypath_rou=${params}`, METHOD.POST)
}
// 上传文件
export function upLoadFlyingFile (params) {
    return request(`/api/app/e-mSTPub-cNCDispatch/up-load-flying-file`, METHOD.POST,params)
}
// 上传参考文件
export function upLoadRout2OSS (params,Pdctno,) {
    return request(`/api/app/e-mSTPub-cNCDispatch/up-load-rout2OSS?alypath_rou=${params}&Pdctno=${Pdctno}`, METHOD.POST,)
}
// 获取完成文件路径
export function finishFileDownload (OrderNo) {
    return request(`/api/app/e-mSTPub-cNCDispatch/finish-file-download?Pdctno=${OrderNo}`, METHOD.POST,)
}

export default {
    cncDispatchList,
    protoollist,
    cncMucount,
    projectBackEndPeopleList,
    peopleOrderList,
    projectBackEndAssign,
    getFactoryList,
    cncSend,
    cncSendBack,
    cncOrderFinish,
    orderRetrievalSettings,
    RetrievalSettings,
    cnCToolInfo,
    finish,
    backmake,
    ossUp,
    upLoadFlyingFile,
    finishFileDownload,
}
