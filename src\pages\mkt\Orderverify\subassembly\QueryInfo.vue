<!-- 市场管理 - 订单预审- 查询 -->
<template>
  <a-form-model :modal="form">
    <a-form-model-item label="订单号：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
      <a-input v-model="form.OrderNo" :autoFocus="autoFocus" v-focus-next-on-enter="'input2'" ref="input1" allowClear />
    </a-form-model-item>
    <a-form-model-item label="客户代码" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
      <a-input v-model="form.custNo" v-focus-next-on-enter="'input3'" ref="input2" allowClear />
    </a-form-model-item>
    <a-form-model-item label="客户型号：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
      <a-input v-model="form.PcbFileName" v-focus-next-on-enter="'input4'" ref="input3" allowClear />
    </a-form-model-item>
    <a-form-model-item label="生产型号" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
      <a-input v-model="form.proOrderNo" v-focus-next-on-enter="'input7'" ref="input6" allowClear />
    </a-form-model-item>
    <a-form-model-item label="预审人" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
      <a-select ref="select" v-model="form.CheckAccount" allowClear showSearch optionFilterProp="lable">
        <a-select-option v-for="(item, index) in Quoterlist" :key="index" :lable="item.text" :value="item.valueMember">{{
          item.text
        }}</a-select-option>
      </a-select>
    </a-form-model-item>
    <a-form-item label="订单类型" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
      <a-select ref="select" v-model="form.ReOrder" allowClear showSearch>
        <a-select-option value="0">新单 </a-select-option>
        <a-select-option value="1">返单 </a-select-option>
        <a-select-option value="2">返单更改 </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="建立人" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
      <a-input v-model="form.createName" allowClear />
    </a-form-item>
  </a-form-model>
</template>

<script>
export default {
  name: "QueryInfo",
  props: ["Quoterlist"],
  data() {
    return {
      autoFocus: true,
      form: {
        OrderNo: "", // 订单编号
        custNo: "", // 订单包号
        PcbFileName: "", // 文件名
        proOrderNo: "", //本厂编码
        CheckAccount: "",
      },
    };
  },
  methods: {},
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
</style>
