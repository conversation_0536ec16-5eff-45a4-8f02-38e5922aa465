<!-- 工具管理- 叠层阻抗-叠层报表 中文 -->
<template>
  <div class="pdfDom" :style="ttype == 'down' ? { height: '0.5px' } : { height: '735px' }" :class="ttype == 'down' ? 'pdfmargin' : ''">
    <!-- <a-button type="primary" style="float: right;margin-top: 55px;margin-right: 29px;" @click="generateImage1">img导出</a-button> -->
    <!-- 2024/4/2 打印按钮暂时关闭
       <span style="float: right;margin-top: 55px;margin-right: 29px;">
      <button v-show="!reportType" v-print='printObj' >打印</button>
      <button v-show="reportType" v-print='printObj1' >打印</button>
    </span> -->
    <div id="pdfDom" v-show="!reportType || ttype == 'down'" ref="imgdom">
      <!-- <div> -->
      <div class="headerTitle item">
        <div style="position: relative">
          <img v-if="baseimg" :src="baseimg" />
          <div style="text-align: left; font-weight: 500; font-size: 16px; margin-top: 5px; color: #000000">
            产品编号：{{ this.laminationData.stackUpInfo.pdctno }}
          </div>
        </div>
        <div style="text-align: center; margin-left: 100px">
          <h2>{{ laminationData.stackUpInfo.enterpriseName }}</h2>
          <!-- <span>叠层阻抗信息</span> -->
        </div>
      </div>
      <div style="text-align: left; font-weight: 500; font-size: 16px; height: 40px; color: #000000; background-color: white">
        客户型号：{{ this.laminationData.stackUpInfo.customerNumber }}
      </div>
      <h3 style="margin: 0; border: 2px solid black; border-bottom: 0; color: #ff9900; width: 100%; padding-left: 45%" class="item">叠层信息</h3>
      <div class="reportTable item bbb" ref="dieceng" style="background-color: white" id="dieceng">
        <div style="padding: 46px 10px 10px; border: 1px solid #000000; position: relative" class="Throughout">
          <!--            这是叠构图 -->
          <div v-for="(item, index) in tableData" :key="index">
            <div v-if="item.stackUpMTR_ == 'OZ'" class="OZclass" :class="[index == tableData.length - 1 ? 'ozlastClass' : '']">
              <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                {{ item.child[0].stackUpLayerNo_ | layerFilter(that) }}
              </div>
              <div class="ozContent">
                <div class="oz_bg" :class="[index == tableData.length - 1 ? 'oz_active' : '']"></div>
              </div>
            </div>
            <div v-if="item.stackUpMTR_ == 'Core'" class="coreClass">
              <div v-for="(ite, idx) in item.child" :key="idx" :class="[ite.stackUpMTR_ == 'Core' ? 'coreActive' : 'ozActive']">
                <div v-if="ite.stackUpMTR_ == 'OZ'" class="OZclass">
                  <div v-if="ite.stackUpLayerNo_" :class="['layerName', 'L' + ite.stackUpLayerNo_]">L{{ ite.stackUpLayerNo_ }}</div>
                  <div class="ozContent2"></div>
                </div>
                <div v-if="ite.stackUpMTR_ == 'Core'" class="core-box">
                  <div v-if="ite.stackUpLayerNo_" :class="['layerName', 'L' + ite.stackUpLayerNo_]">L{{ ite.stackUpLayerNo_ }}</div>
                  <div class="CoreContent"></div>
                </div>
              </div>
            </div>
            <div v-if="item.stackUpMTR_ == '金属基' || item.stackUpMTR_ == '补强'" class="jsjclass">
              <div :class="['layerName', '金属基']">{{ item.stackUpMTR_ }}</div>
              <div class="jsjContent"></div>
            </div>
            <div v-if="item.stackUpMTR_ == 'PP'" class="PPclass">
              <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                L{{ item.child[0].stackUpLayerNo_ }}
              </div>
              <div class="PPContent"></div>
            </div>
            <div v-if="item.stackUpMTR_ == 'GB'" class="GBClass">
              <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                L{{ item.child[0].stackUpLayerNo_ }}
              </div>
              <div class="gbContent"></div>
            </div>
          </div>
          <div v-if="laminationData.stackUpDrills.length > 0" class="drillClss">
            <div
              v-for="(list, index) in laminationData.stackUpDrills"
              :key="index"
              :ref="'dir_' + index"
              :class="
                Number(list.startLayer) > Number(list.endLayer)
                  ? /^ks|^bz/i.test(list.drillName)
                    ? 'drillItemred1'
                    : 'drillItem1'
                  : /^ks|^bz/i.test(list.drillName)
                  ? 'drillItemred'
                  : 'drillItem'
              "
            ></div>
          </div>
        </div>
        <div class="parameterClass">
          <table style="width: 100%; max-width: 560px">
            <tr style="height: 46px">
              <th style="width: 65px">类型</th>
              <th v-if="showMaterial" style="width: 90px">物料名称</th>
              <th style="width: 231px">物料参数</th>
              <th style="width: 60px">残铜率</th>
              <th style="width: 70px">厚度(mm)</th>
            </tr>
            <tr
              v-for="(item, index) in tableData"
              :key="index"
              :class="[
                item.stackUpMTR_ == 'OZ' ? 'paramOZ' : item.stackUpMTR_ == 'Core' ? 'paramCore' : item.stackUpMTR_ == 'GB' ? 'paramGB' : 'paramPP',
              ]"
            >
              <td>{{ item.stackUpMTR_ | typeFilter }}</td>
              <td v-if="showMaterial">{{ item | nameFilter }}</td>
              <td
                class="param-cell"
                :class="[item.stackUpMTR_ == 'PP' ? (item.child[0].stackUpMTRFoil_.split('+').length <= 3 ? 'tdclass' : 'tdclass1') : '']"
              >
                {{ item | paramFilter }}
              </td>
              <td>{{ item | ctlFilter }}</td>
              <td>{{ item | cphdFilter }}</td>
            </tr>
          </table>
        </div>
        <div class="thickness">
          <span>
            <span v-if="laminationData.stackUpInfo.layers > 2">
              压合完成理论厚度:{{ laminationData.stackUpInfo.pressingThickness | floatFilter }}mm
            </span>
            <span v-if="joinFactoryId == 67 || joinFactoryId == 58 || joinFactoryId == 59"
              >,要求压合厚度 :{{ laminationData.stackUpInfo.yqThicknessOK2_ }}</span
            >
            <br />
            <span v-if="laminationData.stackUpInfo.highLimit && laminationData.stackUpInfo.lowLimit">
              完成板厚:{{ laminationData.stackUpInfo.finishBoardThickness | floatFilter }}(+{{ laminationData.stackUpInfo.highLimit }}/{{
                laminationData.stackUpInfo.lowLimit
              }})mm
            </span>
            <span v-else> 完成板厚:{{ laminationData.stackUpInfo.finishBoardThickness | floatFilter }}mm </span>
          </span>
          <span style="margin-left: 5px" v-if="laminationData.stackUpInfo.isChangeLayerPres"> 压合不可更改 </span>
        </div>
      </div>
      <!-- </div> -->
      <div style="border: 2px solid; border-top: 0" ref="000">
        <div class="item">
          <h3 style="margin: 0; border-bottom: 2px solid black; color: #ff9900; width: 100%; padding-left: 45%">阻抗信息</h3>
          <a-table
            :columns="columns"
            :data-source="impedanceTable"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            bordered
            :pagination="false"
            :rowClassName="rowClassName"
            class="zuk"
          >
            <template slot="adjustLineweight" slot-scope="text, record">
              <a v-if="record.lineWidthColorRed" style="color: red" type="primary">{{ record.adjustLineweight }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.adjustLineweight }}</a>
            </template>
            <template slot="adjustLinedistance" slot-scope="text, record">
              <a v-if="record.lineSpaceColorRed" style="color: red" type="primary">{{ record.adjustLinedistance }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.adjustLinedistance }}</a>
            </template>
            <template slot="adjustingWirecopper" slot-scope="text, record">
              <a v-if="record.lineCuColorRed" style="color: red" type="primary">{{ record.adjustingWirecopper }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.adjustingWirecopper }}</a>
            </template>
            <template slot="resistanceTolerance" slot-scope="text, record">
              <a v-if="record.impColorRed" style="color: red" type="primary">{{ record.resistanceTolerance }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.resistanceTolerance }}</a>
            </template>
          </a-table>
        </div>
        <div v-if="showImpedancediagram">
          <h3
            style="margin: 0; border-top: 1px solid black; color: #ff9900; width: 100%; padding-left: 45%; border-bottom: 1px solid black"
            class="item"
          >
            阻抗模型
          </h3>
          <div v-for="(item, index) in impedanceData" :key="index" class="impedance item">
            <h4>
              {{ impTypeFilter(item.imp_Type_) }}<br />
              {{ item.imp_PoLarName }}
            </h4>
            <div class="imp_left">
              <div class="line_flex">
                <p>原稿线宽</p>
                <p>{{ item.imp_LineWidth_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p>调整线宽</p>
                <p>{{ item.imp_OKLineWidth_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p>原稿线距</p>
                <p>{{ item.imp_LineSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p>调整线距</p>
                <p>{{ item.imp_OKLineSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p>原稿线铜</p>
                <p>{{ item.imp_LineCuSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p>调整线铜</p>
                <p>{{ item.imp_OKLineCuSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p>要求阻值</p>
                <p>{{ item.imp_Value_Req_ }}</p>
                <p>Ω</p>
              </div>
            </div>
            <div class="imp_center">
              <img :src="'data:image/png;base64,' + item.impModelImage" />
            </div>
            <div class="imp_right">
              <div class="line_flex" v-if="item.imp_H1_ && item.imp_H1_ != 0">
                <p>H1</p>
                <p>{{ item.imp_H1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_Er1_ && item.imp_Er1_ != 0">
                <p>Er1</p>
                <p>{{ item.imp_Er1_ }}</p>
                <p></p>
              </div>
              <div class="line_flex" v-if="item.imp_H2_ && item.imp_H2_ != 0">
                <p>H2</p>
                <p>{{ item.imp_H2_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_Er2_ && item.imp_Er2_ != 0">
                <p>Er2</p>
                <p>{{ item.imp_Er2_ }}</p>
                <p></p>
              </div>
              <div class="line_flex" v-if="item.imp_W1_ && item.imp_W1_ != 0">
                <p>W1</p>
                <p>{{ item.imp_W1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_W2_ && item.imp_W2_ != 0">
                <p>W2</p>
                <p>{{ item.imp_W2_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_D1_ && item.imp_D1_ != 0">
                <p>D1</p>
                <p>{{ item.imp_D1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_S1_ && item.imp_S1_ != 0">
                <p>S1</p>
                <p>{{ item.imp_S1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_T1_ && item.imp_T1_ != 0">
                <p>T1</p>
                <p>{{ item.imp_T1_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_C1_ &&
                  item.imp_C1_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>C1</p>
                <p>{{ item.imp_C1_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_C2_ &&
                  item.imp_C2_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>C2</p>
                <p>{{ item.imp_C2_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_C3_ &&
                  item.imp_C3_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>C3</p>
                <p>{{ item.imp_C3_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_CEr_ &&
                  item.imp_CEr_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>CEr</p>
                <p>{{ item.imp_CEr_ }}</p>
                <p></p>
              </div>
              <div class="line_flex" v-if="item.imp_TrueValue_">
                <p>Imp</p>
                <p>{{ item.imp_TrueValue_ }}</p>
                <p>Ω</p>
              </div>
            </div>
            <h3>
              <p style="margin: 0">控制层:{{ item.imp_ControlLay_ }};</p>
              <p style="margin: 0">上参考:{{ item.imp_UpLay_ || "/" }};</p>
              <p style="margin: 0">下参考:{{ item.imp_DownLay_ || "/" }};</p>
            </h3>
          </div>
        </div>
      </div>
    </div>
    <div id="pdfDom1" v-show="reportType || ttype == 'down'" ref="imgdom1">
      <div>
        <div class="headerTitle item">
          <div style="position: relative">
            <img v-if="baseimg" :src="baseimg" />
            <div style="text-align: left; font-weight: 500; font-size: 16px; margin-top: 5px; color: #000000">
              Product Number:{{ this.laminationData.stackUpInfo.pdctno }}
            </div>
          </div>
          <div style="text-align: center; margin-left: 50px">
            <h2>{{ laminationData.stackUpInfo.enterpriseEngName }}</h2>
            <!-- <span>叠层阻抗信息</span> -->
            <!-- <span>Engineering Stackup & Impedance</span> -->
          </div>
        </div>
        <div style="text-align: left; font-weight: 500; font-size: 16px; color: #000000; height: 40px; background-color: white">
          Customer Number:{{ this.laminationData.stackUpInfo.customerNumber }}
        </div>
        <h3 style="margin: 0; border: 2px solid black; border-bottom: 0; width: 100%; padding-left: 45%; color: #ff9900" class="item">
          Stackup Information
        </h3>
        <div class="reportTable item bbb" ref="dieceng1" id="dieceng1" style="background-color: white">
          <!-- <div style="background: #1AA65F;width: 65px;align-items: center;display: flex; color: #FFFFFF; font-weight: 500; text-align: center">
            Finish<br/>Board<br/>Thickness<br/>{{laminationData.stackUpInfo.finishBoardThickness | floatFilter}}<br/>(mm)</div> -->
          <div style="padding: 45px 10px 2px 10px; border: 1px solid #000000; position: relative" class="Throughout">
            <!--            这是叠构图 -->
            <div v-for="(item, index) in tableData" :key="index">
              <div v-if="item.stackUpMTR_ == 'OZ'" class="OZclass" :class="[index == tableData.length - 1 ? 'ozlastClass' : '']">
                <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                  {{ item.child[0].stackUpLayerNo_ | layerFilter(that) }}
                </div>
                <div class="ozContent">
                  <div class="oz_bg" :class="[index == tableData.length - 1 ? 'oz_active' : '']"></div>
                </div>
              </div>
              <div v-if="item.stackUpMTR_ == 'Core'" class="coreClass">
                <div v-for="(ite, idx) in item.child" :key="idx" :class="[ite.stackUpMTR_ == 'Core' ? 'coreActive' : 'ozActive']">
                  <div v-if="ite.stackUpMTR_ == 'OZ'" class="OZclass">
                    <div v-if="ite.stackUpLayerNo_" :class="['layerName', 'L' + ite.stackUpLayerNo_]">L{{ ite.stackUpLayerNo_ }}</div>
                    <div class="ozContent2"></div>
                  </div>
                  <div v-if="ite.stackUpMTR_ == 'Core'" class="core-box">
                    <div v-if="ite.stackUpLayerNo_" :class="['layerName', 'L' + ite.stackUpLayerNo_]">L{{ ite.stackUpLayerNo_ }}</div>
                    <div class="CoreContent"></div>
                  </div>
                </div>
              </div>
              <div v-if="item.stackUpMTR_ == '金属基' || item.stackUpMTR_ == '补强'" class="jsjclass">
                <div :class="['layerName', '金属基']">{{ item.stackUpMTR_ }}</div>
                <div class="jsjContent"></div>
              </div>
              <div v-if="item.stackUpMTR_ == 'PP'" class="PPclass">
                <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                  L{{ item.child[0].stackUpLayerNo_ }}
                </div>
                <div class="PPContent"></div>
              </div>
              <div v-if="item.stackUpMTR_ == 'GB'" class="GBClass">
                <div v-if="item.child[0].stackUpLayerNo_" :class="['layerName', 'L' + item.child[0].stackUpLayerNo_]">
                  L{{ item.child[0].stackUpLayerNo_ }}
                </div>
                <div class="gbContent"></div>
              </div>
            </div>
            <div v-if="laminationData.stackUpDrills.length > 0" class="drillClss">
              <div
                v-for="(list, index) in laminationData.stackUpDrills"
                :key="index"
                :ref="'dir1_' + index"
                :class="
                  Number(list.startLayer) > Number(list.endLayer)
                    ? /^ks|^bz/i.test(list.drillName)
                      ? 'drillItemred1'
                      : 'drillItem1'
                    : /^ks|^bz/i.test(list.drillName)
                    ? 'drillItemred'
                    : 'drillItem'
                "
              ></div>
            </div>
          </div>
          <div class="parameterClass">
            <table style="width: 100%; max-width: 560px">
              <tr>
                <th style="width: 40px">Type</th>
                <th v-if="showMaterial" style="width: 90px">Material</th>
                <th style="width: 264px">Parameter</th>
                <th>Residual Copper</th>
                <th>Thickness (mm)</th>
              </tr>
              <tr
                v-for="(item, index) in tableData"
                :key="index"
                :class="[
                  item.stackUpMTR_ == 'OZ' ? 'paramOZ' : item.stackUpMTR_ == 'Core' ? 'paramCore' : item.stackUpMTR_ == 'GB' ? 'paramGB' : 'paramPP',
                ]"
              >
                <td style="width: 40px">{{ item.stackUpMTR_ | typeFilter1 }}</td>
                <td v-if="showMaterial">{{ item | nameEnFilter }}</td>
                <td
                  class="param-cell"
                  :class="[item.stackUpMTR_ == 'PP' ? (item.child[0].stackUpMTRFoil_.split('+').length <= 3 ? 'tdclass' : 'tdclass2') : '']"
                >
                  {{ item | paramFilter1 }}
                </td>
                <td>{{ item | ctlFilter }}</td>
                <td>{{ item | cphdFilter }}</td>
              </tr>
            </table>
          </div>
          <div class="thickness">
            <span v-if="laminationData.stackUpInfo.layers > 2">
              PressBoardThickness:{{ laminationData.stackUpInfo.pressingThickness | floatFilter }}mm
            </span>
            <span v-if="joinFactoryId == 67 || joinFactoryId == 58 || joinFactoryId == 59"
              >,Pressing thickness is required :{{ yqThicknessOK2_ }}</span
            ><br />
            <span>
              <span v-if="laminationData.stackUpInfo.highLimit && laminationData.stackUpInfo.lowLimit">
                finishBoardThickness:{{ laminationData.stackUpInfo.finishBoardThickness | floatFilter }}(+{{
                  laminationData.stackUpInfo.highLimit
                }}/{{ laminationData.stackUpInfo.lowLimit }})mm
              </span>
              <span v-else> finishBoardThickness:{{ laminationData.stackUpInfo.finishBoardThickness | floatFilter }}mm </span>
            </span>
            <br />
            <span style="margin-left: 3px" v-if="laminationData.stackUpInfo.isChangeLayerPres"> Compression cannot be changed </span>
          </div>
        </div>
      </div>

      <div style="border: 2px solid; border-top: 0" ref="111">
        <h3 style="margin: 0; border-bottom: 2px solid black; width: 100%; padding-left: 45%; color: #ff9900; background-color: white" item>
          Impedance Information
        </h3>
        <div class="item">
          <a-table
            :columns="columns1"
            :data-source="impedanceTable"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            bordered
            :pagination="false"
            class="zuk"
            :rowClassName="rowClassName"
          >
            <template slot="adjustLineweight" slot-scope="text, record">
              <a v-if="record.lineWidthColorRed" style="color: red" type="primary">{{ record.adjustLineweight }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.adjustLineweight }}</a>
            </template>
            <template slot="adjustLinedistance" slot-scope="text, record">
              <a v-if="record.lineSpaceColorRed" style="color: red" type="primary">{{ record.adjustLinedistance }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.adjustLinedistance }}</a>
            </template>
            <template slot="adjustingWirecopper" slot-scope="text, record">
              <a v-if="record.lineCuColorRed" style="color: red" type="primary">{{ record.adjustingWirecopper }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.adjustingWirecopper }}</a>
            </template>
            <template slot="resistanceTolerance" slot-scope="text, record">
              <a v-if="record.impColorRed" style="color: red" type="primary">{{ record.resistanceTolerance }}</a>
              <a v-else style="color: #000000" type="primary">{{ record.resistanceTolerance }}</a>
            </template>
          </a-table>
        </div>
        <div v-if="showImpedancediagram">
          <h3
            style="margin: 0; border-top: 1px solid black; color: #ff9900; width: 100%; padding-left: 45%; border-bottom: 1px solid black"
            class="item"
          >
            Impedance Model
          </h3>
          <div v-for="(item, index) in impedanceData" :key="index" class="impedance item">
            <h4>
              {{ item.imp_Type_ }}<br />
              {{ item.imp_PoLarName }}
            </h4>
            <div class="imp_left">
              <div class="line_flex">
                <p style="width: 155px">Original LineWidth</p>
                <p>{{ item.imp_LineWidth_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p style="width: 155px">Adjust LineWidth</p>
                <p>{{ item.imp_OKLineWidth_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p style="width: 155px">Original LineSpacing</p>
                <p>{{ item.imp_LineSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p style="width: 155px">Adjust LineSpacing</p>
                <p>{{ item.imp_OKLineSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p style="width: 155px">Original Line To Copper</p>
                <p>{{ item.imp_LineCuSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p style="width: 155px">Adjust Line To Copper</p>
                <p>{{ item.imp_OKLineCuSpace_ || "/" }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex">
                <p style="width: 155px">Required Resistance</p>
                <p>{{ item.imp_Value_Req_ }}</p>
                <p>Ω</p>
              </div>
            </div>
            <div class="imp_center" style="margin: 10px">
              <img :src="'data:image/png;base64,' + item.impModelImage" style="width: 297px" />
            </div>
            <div class="imp_right">
              <div class="line_flex" v-if="item.imp_H1_ && item.imp_H1_ != 0">
                <p>H1</p>
                <p>{{ item.imp_H1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_Er1_ && item.imp_Er1_ != 0">
                <p>Er1</p>
                <p>{{ item.imp_Er1_ }}</p>
                <p></p>
              </div>
              <div class="line_flex" v-if="item.imp_H2_ && item.imp_H2_ != 0">
                <p>H2</p>
                <p>{{ item.imp_H2_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_Er2_ && item.imp_Er2_ != 0">
                <p>Er2</p>
                <p>{{ item.imp_Er2_ }}</p>
                <p></p>
              </div>
              <div class="line_flex" v-if="item.imp_W1_ && item.imp_W1_ != 0">
                <p>W1</p>
                <p>{{ item.imp_W1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_W2_ && item.imp_W2_ != 0">
                <p>W2</p>
                <p>{{ item.imp_W2_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_D1_ && item.imp_D1_ != 0">
                <p>D1</p>
                <p>{{ item.imp_D1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_S1_ && item.imp_S1_ != 0">
                <p>S1</p>
                <p>{{ item.imp_S1_ }}</p>
                <p>Mil</p>
              </div>
              <div class="line_flex" v-if="item.imp_T1_ && item.imp_T1_ != 0">
                <p>T1</p>
                <p>{{ item.imp_T1_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_C1_ &&
                  item.imp_C1_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>C1</p>
                <p>{{ item.imp_C1_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_C2_ &&
                  item.imp_C2_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>C2</p>
                <p>{{ item.imp_C2_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_C3_ &&
                  item.imp_C3_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>C3</p>
                <p>{{ item.imp_C3_ }}</p>
                <p>Mil</p>
              </div>
              <div
                class="line_flex"
                v-if="
                  item.imp_CEr_ &&
                  item.imp_CEr_ != 0 &&
                  impTypeFilter(item.imp_Type_).includes('外层') &&
                  !impTypeFilter(item.imp_Type_).includes('开窗')
                "
              >
                <p>CEr</p>
                <p>{{ item.imp_CEr_ }}</p>
                <p></p>
              </div>
              <div class="line_flex" v-if="item.imp_TrueValue_">
                <p>Imp</p>
                <p>{{ item.imp_TrueValue_ }}</p>
                <p>Ω</p>
              </div>
            </div>
            <h3 style="width: 500px">
              <p style="margin: 0">Control Layer:{{ item.imp_ControlLay_ }};</p>
              <p style="margin: 0">Upper reference:{{ item.imp_UpLay_ || "/" }};</p>
              <p style="margin: 0">Lower reference:{{ item.imp_DownLay_ || "/" }};</p>
            </h3>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import html2pdf from "html2pdf.js";
import domtoimage from "dom-to-image";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 40,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "类型",
    dataIndex: "type",
    // width:100,
    align: "center",
  },
  {
    title: "控制层",
    dataIndex: "controlLayer",
    width: 50,
    align: "center",
  },
  {
    title: "参考层",
    dataIndex: "referenceLayer",
    width: 50,
    align: "center",
  },
  {
    title: "原线宽(Mil)",
    dataIndex: "primarylLinewidth",
    width: 50,
    align: "center",
  },
  {
    title: "原线距(Mil)",
    dataIndex: "primaryLinedistance",
    width: 50,
    align: "center",
  },
  {
    title: "原线铜(Mil)",
    dataIndex: "primaryCopper",
    width: 50,
    align: "center",
  },
  {
    title: "调整线宽(Mil)",
    //dataIndex: 'adjustLineweight',
    width: 60,
    align: "center",
    scopedSlots: { customRender: "adjustLineweight" },
  },
  {
    title: "调整线距(Mil)",
    // dataIndex: 'adjustLinedistance',
    width: 60,
    align: "center",
    scopedSlots: { customRender: "adjustLinedistance" },
  },
  {
    title: "调整线铜(Mil)",
    //dataIndex: 'adjustingWirecopper',
    scopedSlots: { customRender: "adjustingWirecopper" },
    width: 60,
    align: "center",
  },
  {
    title: "要求阻值(Ω)",
    dataIndex: "filmLinewidth",
    width: 60,
    align: "center",
  },
  {
    title: "实际阻值及公差(Ω)",
    align: "center",
    width: 100,
    //dataIndex: 'resistanceTolerance',
    scopedSlots: { customRender: "resistanceTolerance" },
  },
];
const columns1 = [
  {
    title: "NO",
    dataIndex: "index",
    width: 38,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "Imp Type",
    dataIndex: "imp_Type_",
    width: 75,
    align: "center",
  },
  {
    title: "Control Layer",
    dataIndex: "controlLayer",
    width: 50,
    align: "center",
  },
  {
    title: "ReferenceLayer",
    dataIndex: "referenceLayer",
    width: 65,
    align: "center",
  },
  {
    title: "Original Line Width(Mil)",
    dataIndex: "primarylLinewidth",
    width: 55,
    align: "center",
  },
  {
    title: "Original Line Spacing(Mil)",
    dataIndex: "primaryLinedistance",
    width: 55,
    align: "center",
  },
  {
    title: "Original Line ToCopper(Mil)",
    dataIndex: "primaryCopper",
    width: 67,
    align: "center",
  },
  {
    title: "Adjust Line Width(Mil)",
    // dataIndex: 'adjustLineweight',
    width: 55,
    align: "center",
    scopedSlots: { customRender: "adjustLineweight" },
  },
  {
    title: "Adjust Line Spacing(Mil)",
    //dataIndex: 'adjustLinedistance',
    width: 55,
    align: "center",
    scopedSlots: { customRender: "adjustLinedistance" },
  },
  {
    title: "Adjust Line ToCopper(Mil)",
    //dataIndex: 'adjustingWirecopper',
    scopedSlots: { customRender: "adjustingWirecopper" },
    width: 67,
    align: "center",
  },
  {
    title: "Required Resistance(Ω)",
    dataIndex: "filmLinewidth",
    width: 68,
    align: "center",
  },
  {
    title: "Actual Resistance& Tolerance (Ω)",
    align: "center",
    //width:82,
    //dataIndex: 'resistanceTolerance',
    scopedSlots: { customRender: "resistanceTolerance" },
  },
];
import { uploadstackimptable, uploadstackimages, uploadstackimagesv2 } from "@/services/impedance";
import htmlToPdfurl from "@/utils/htmlToPdfurl";
import htmlToPdf from "@/utils/htmlToPdf";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import $ from "jquery";
export default {
  name: "ReportInfo",
  props: {
    laminationData: {
      type: Object,
      required: true,
    },
    reportType: {
      type: Boolean,
    },
    factory1: {
      type: Number,
    },
    showMaterial: {},
    ttype: {},
    showImpedancediagram: {
      type: Boolean,
    },
    joinFactoryId: {},
  },
  watch: {
    reportType(val) {
      this.styleHeight_();
    },
  },
  filters: {
    layerFilter(val, that) {
      return "L" + val;
      // if (val ==1 ){
      //   return 'GTL'
      // } else if (val == that.laminationData.stackUps[that.laminationData.stackUps.length-1].stackUpLayerNo_) {
      //   return 'GBL'
      // } else {
      //   return  'L'+val
      // }
    },
    floatFilter(val) {
      return val.toFixed(2);
    },
    typeFilter(val) {
      if (val == "OZ") {
        return "铜箔";
      } else if (val == "PP") {
        return "半固化片";
      } else if (val == "Core") {
        return "芯板";
      } else if (val == "GB") {
        return "光板";
      } else {
        return val;
      }
    },
    typeFilter1(val) {
      if (val == "OZ") {
        return "OZ";
      } else if (val == "PP") {
        return "PP";
      } else if (val == "Core") {
        return "Core";
      } else if (val == "GB") {
        return "GB";
      } else {
        return val;
      }
    },
    nameFilter(val) {
      if (val.child) {
        let str = "";
        val.child.forEach(item => {
          if (item.stackUpMTR_ == "Core" || item.stackUpMTR_ == "PP" || item.stackUpMTR_ == "GB") {
            str = item.vendorNoStr + "\n" + item.stackUpMTRType_;
          }
        });
        return str;
      } else {
        return val.stackUpMTRType_;
      }
    },
    nameEnFilter(val) {
      if (val.child) {
        let str = "";
        val.child.forEach(item => {
          if (item.stackUpMTR_ == "Core" || item.stackUpMTR_ == "PP" || item.stackUpMTR_ == "GB") {
            str = item.vendorNoEngStr + "\n" + item.stackUpMTRType_;
          }
        });
        return str;
      } else {
        return val.stackUpMTRType_;
      }
    },
    paramFilter(val) {
      if (val.child) {
        let val_ = val.child;
        let str_ = ``;
        val_.forEach(item => {
          if (item.stackUpMTR_ == "OZ") {
            str_ += item.stackUpMTRFoil_ + "OZ" + "\n";
          } else if (item.stackUpMTR_ == "Core") {
            let corStr_ = (item.stackUpCoreDS_ ? "含铜）" : "不含铜）") + item.thicknesSstr;
            str_ += Number(item.stackUpMTRFoil_).toFixed(3) + "(" + corStr_ + "\n";
          } else if (item.stackUpMTR_ == "PP") {
            let thicknesS = item.thicknesSstr.split("+");
            let str__ = "";
            if (thicknesS.length <= 3) {
              thicknesS.forEach((item, index) => {
                str__ += item.split(",")[0] + " " + item.split(",")[1] + "\n";
              });
              str_ += str__;
            } else {
              str_ += item.stackUpMTRFoil_;
            }
          } else {
            str_ = item.stackUpMTRFoil_;
          }
        });
        return str_;
      } else {
        if (val.stackUpMTR_ == "OZ") {
          return val.stackUpMTRFoil_ + "  OZ";
        } else {
          return val.stackUpMTRFoil_;
        }
      }
    },
    paramFilter1(val) {
      // console.log('val',val,val.stackUpMTRFoil_)
      if (val.child) {
        let val_ = val.child;
        let str_ = ``;
        val_.forEach(item => {
          if (item.stackUpMTR_ == "OZ") {
            str_ += item.stackUpMTRFoil_ + "OZ" + "\n";
          } else if (item.stackUpMTR_ == "Core") {
            let corStr_ = (item.stackUpCoreDS_ ? "include copper)" : "exclude copper)") + item.thicknesSstr;
            str_ += item.stackUpMTRFoil_ + "(" + corStr_ + "\n";
          } else if (item.stackUpMTR_ == "GB") {
            str_ = item.stackUpMTRFoil_;
            if (item.stackUpMTRFoil_.indexOf("不含铜") != -1) {
              str_ = str_.replace(/不含铜/g, "exclude copper");
            } else if (item.stackUpMTRFoil_.indexOf("含铜") != -1) {
              str_ = str_.replace(/含铜/g, "include copper");
            }
            if (item.stackUpMTRFoil_.indexOf("有水印") != -1) {
              str_ = str_.replace(/有水印/g, "Watermark");
            } else if (item.stackUpMTRFoil_.indexOf("无水印") != -1) {
              str_ = str_.replace(/无水印/g, "Non-watermarked");
            }
          } else if (item.stackUpMTR_ == "PP") {
            let thicknesS = item.thicknesSstr.split("+");
            let str__ = "";
            if (thicknesS.length <= 3) {
              thicknesS.forEach((item, index) => {
                str__ += item.split(",")[0] + " " + item.split(",")[1] + "\n";
              });
              str_ += str__;
            } else {
              str_ += item.stackUpMTRFoil_;
            }
          } else {
            str_ = item.stackUpMTRFoil_;
          }
        });
        //console.log('str',str_)
        return str_;
      } else {
        if (val.stackUpMTR_ == "OZ") {
          return val.stackUpMTRFoil_ + "  OZ";
        } else if (val.stackUpMTR_ == "GB") {
          let newIsNullRules = val.stackUpMTRFoil_;
          if (val.stackUpMTRFoil_.indexOf("不含铜") != -1) {
            newIsNullRules = newIsNullRules.replace(/不含铜/g, "exclude copper");
          } else if (val.stackUpMTRFoil_.indexOf("含铜") != -1) {
            newIsNullRules = newIsNullRules.replace(/含铜/g, "include copper");
          }
          return newIsNullRules;
        } else {
          // 2022/08/05 屏蔽PP型号去重
          // let str = ''
          // let _data  = val.stackUpMTRFoil_.split('+').reduce(function (a, b) {
          //   if (b in a) {
          //     a[b]++
          //
          //   } else {
          //     a[b] = 1
          //   }
          //   return a
          // }, {})
          // var _dataKeys = Object.keys(_data)
          // str = _dataKeys.join('+')
          // return  str
          return val.stackUpMTRFoil_;
        }
      }
    },
    ctlFilter(val) {
      if (val.child) {
        let val_ = val.child;
        let str_ = ``;
        val_.forEach(item => {
          if (item.stackUpCTLMI_) {
            str_ += item.stackUpCTLMI_ + "\n";
          } else {
            str_ += "/" + "\n";
          }
        });
        return str_ || "/";
      } else {
        return val.stackUpCTLMI_ || "/";
      }
    },
    cphdFilter(val) {
      if (val.child) {
        let val_ = val.child;
        let str_ = ``;
        val_.forEach(item => {
          str_ += item.stackUpThichnessMM_ + "\n";
        });
        return str_;
      } else {
        return val.stackUpThichnessMM_;
      }
    },
  },
  computed: {
    tableData: function () {
      let _self = this;
      let newData = [];
      let structure = _self.laminationData.stackUps.map(item => item.stackUpMTR_);
      let data = _self.laminationData.stackUps;
      data.forEach((item, index) => {
        let child = [];
        if (item.stackUpMTR_ == "OZ") {
          if (data[index + 1]?.stackUpMTR_ == "Core" && data[index + 2]?.stackUpMTR_ == "OZ") {
            child.push(item);
            child.push(data[index + 1]);
            child.push(data[index + 2]);
            newData.push({ stackUpMTR_: "Core", child: child });
          } else {
            if (data[index - 1]?.stackUpMTR_ != "Core" || index == 0) {
              child.push(item);
              newData.push({ stackUpMTR_: "OZ", child: child });
            }
          }
        } else if (item.stackUpMTR_ == "PP") {
          child.push(item);
          newData.push({ stackUpMTR_: "PP", child: child });
        } else if (item.stackUpMTR_ == "GB") {
          child.push(item);
          newData.push({ stackUpMTR_: "GB", child: child });
        } else if (item.stackUpMTR_ == "金属基") {
          child.push(item);
          newData.push({ stackUpMTR_: "金属基", child: child });
        } else if (item.stackUpMTR_ == "补强") {
          child.push(item);
          newData.push({ stackUpMTR_: "补强", child: child });
        }
      });
      return newData;
    },
    impedanceData: function () {
      let _self = this;
      let im_data = _self.laminationData.stackIMPs;
      // im_data.forEach(item => {
      //
      // })
      return im_data;
    },
    impedanceTable() {
      const arr = [];
      this.laminationData.stackIMPs.forEach(item => {
        // debugger
        let _obj = {
          type: this.impTypeFilter(item.imp_Type_),
          imp_Type_: item.imp_type_ENG,
          controlLayer: item.imp_ControlLay_,
          referenceLayer: item.imp_UpLay_ ? (item.imp_DownLay_ ? item.imp_UpLay_ + "/" + item.imp_DownLay_ : item.imp_UpLay_) : item.imp_DownLay_,
          primarylLinewidth: item.imp_LineWidth_,
          primaryLinedistance: item.imp_LineSpace_,
          primaryCopper: item.imp_LineCuSpace_,
          adjustLineweight: item.imp_OKLineWidth_,
          adjustLinedistance: item.imp_OKLineSpace_,
          adjustingWirecopper: item.imp_OKLineCuSpace_,
          filmLinewidth: item.imp_Value_Req_,
          impColorRed: item.impColorRed,
          lineCuColorRed: item.lineCuColorRed,
          lineSpaceColorRed: item.lineSpaceColorRed,
          lineWidthColorRed: item.lineWidthColorRed,
          resistanceTolerance: item.imp_TrueValue_ + "±" + item.imp_Value_Tol_,
        };
        arr.push(_obj);
      });
      return arr;
    },
  },
  data() {
    return {
      PdfFile: null,
      EnglishFile: null,
      ChineseFile: null,
      baseimg: "",
      printObj: {
        id: "pdfDom", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "叠层报表打印", // 预览页面的标题
        popTitle: "", // 打印页面的页眉
      },
      printObj1: {
        id: "pdfDom1", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "叠层报表打印", // 预览页面的标题
        popTitle: "", // 打印页面的页眉
      },
      columns,
      columns1,
      data: [],
      that: this,
      height: window.document.documentElement.clientHeight - 158,
      style_: 0,
      pageSum: 0,
      contentPaiing: "0",
    };
  },
  methods: {
    toBase64(imgUrl) {
      // 一定要设置为let，不然图片不显示
      const image = new Image();
      // 解决跨域问题
      image.setAttribute("crossOrigin", "anonymous");
      const imageUrl = imgUrl;
      image.src = imageUrl;
      // image.onload为异步加载
      image.onload = () => {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        var quality = 0.8;
        // 这里的baseimg就是base64类型
        this.baseimg = canvas.toDataURL("png", quality);
      };
    },
    // 通用方法：生成图片并转换为 FormData
    async generateImage(elementRef, fileProperty) {
      try {
        const element = this.$refs[elementRef];
        const options = {
          quality: 3.0,
          height: element.clientHeight,
        };
        const blob = await domtoimage.toBlob(element, options);
        const arrayBuffer = await this.blobToArrayBuffer(blob);
        this[fileProperty] = this.arrayBufferToFormData(arrayBuffer, "png");
      } catch (error) {
        throw new Error(`Failed to generate image: ${error.message}`);
      }
    },

    // 通用方法：将 Blob 转换为 ArrayBuffer
    blobToArrayBuffer(blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = () => reject(new Error("FileReader error: " + reader.error.message));
        reader.readAsArrayBuffer(blob);
      });
    },

    // 通用方法：将 ArrayBuffer 转换为 FormData
    arrayBufferToFormData(arrayBuffer, type) {
      const fileName = type === "pdf" ? "filename.pdf" : "Stacking.png";
      const blob = new Blob([arrayBuffer], { type: type === "pdf" ? "application/pdf" : "image/png" });
      return new File([blob], fileName, { type: type === "pdf" ? "application/pdf" : "image/png" });
    },

    // 生成 PDF 文件
    async getReporturl() {
      const pdfBlob = await htmlToPdfurl("pdfDom", this.laminationData.stackUpInfo.pdctno);
      this.PdfFile = this.arrayBufferToFormData(pdfBlob, "pdf");
    },
    getReportPdf() {
      //outPutPdfFn  2版本分页截断问题
      //printPdf 3版本canvas画布大小限制
      this.$nextTick(() => {
        if (this.factory1 == 37 || this.factory1 == 22) {
          if (this.reportType) {
            this.downloadToPDF("pdfDom1", this.laminationData.stackUpInfo.pdctno + " Stacked impedance information");
          } else {
            this.downloadToPDF("pdfDom", this.laminationData.stackUpInfo.pdctno + " 叠层阻抗信息");
          }
        } else if (this.factory1 == 12 || this.factory1 == 38) {
          if (this.reportType) {
            this.downloadToPDF("pdfDom1", this.laminationData.stackUpInfo.pdctno + "_Stacked_impedance_information");
          } else {
            this.downloadToPDF("pdfDom", this.laminationData.stackUpInfo.pdctno + "_叠层阻抗信息");
          }
        } else if (this.factory1 == 58 || this.factory1 == 59) {
          if (this.reportType) {
            this.downloadToPDF("pdfDom1", this.laminationData.stackUpInfo.pdctno + "Stacked_impedance_information confirmation");
          } else {
            this.downloadToPDF("pdfDom", this.laminationData.stackUpInfo.pdctno + "阻抗信息确认");
          }
        } else {
          if (this.reportType) {
            this.downloadToPDF("pdfDom1", this.laminationData.stackUpInfo.pdctno);
          } else {
            this.downloadToPDF("pdfDom", this.laminationData.stackUpInfo.pdctno);
          }
        }
      });
    },
    getReportimg() {
      let element, imgname;
      if (this.reportType) {
        element = this.$refs.imgdom1;
        imgname = this.laminationData.stackUpInfo.pdctno + "_Stacked_impedance_information.jpg";
      } else {
        element = this.$refs.imgdom;
        imgname = this.laminationData.stackUpInfo.pdctno + "_叠层阻抗信息.jpg";
      }
      const options = {
        quality: 3.0, // 设置高质量
        width: 820, // 可选，指定输出图片的宽度
        height: element.clientHeight, // 可选，指定输出图片的高度
      };
      domtoimage
        .toBlob(element, options)
        .then(blob => {
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download = imgname;
          document.body.appendChild(a);
          a.click();
          URL.revokeObjectURL(url);
          document.body.removeChild(a);
        })
        .catch(error => {
          console.error("Failed to convert element to blob:", error);
        });
    },
    printPdf(pdfdom, name) {
      let dom = document.getElementById(pdfdom);
      if (!dom) return;
      html2canvas(dom, { useCORS: true, allowTaint: true }).then(canvas => {
        const contentWidth = canvas.width;
        const contentHeight = canvas.height;
        const pageHeight = (contentWidth / 592.28) * 841.89;
        let leftHeight = contentHeight;
        let position = 0;
        // a4纸的尺寸 [595.28, 841.89]，html 页面生成的 canvas 在 pdf 中图片的宽高
        const imgWidth = 595.28;
        const imgHeight = (592.28 / contentWidth) * contentHeight;
        const img = canvas.toDataURL("image/jpeg", 1.0);
        const pdf = new jsPDF("", "pt", "a4");
        // 有两个高度需要区分，一个是html页面的实际高度，和生成 pdf 的页面高度(841.89)
        // 当内容未超过 pdf 一页显示的范围，无需分页
        if (leftHeight < pageHeight) {
          pdf.addImage(img, "JPEG", 0, 0, imgWidth, imgHeight);
        } else {
          while (leftHeight > 0) {
            pdf.addImage(img, "JPEG", 0, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;
            // 避免添加空白页
            if (leftHeight > 0) {
              pdf.addPage();
            }
          }
        }
        // 挂载至页面
        let dataurl = pdf.output("datauristring");
        let blob1 = this.dataURLtoBlob(dataurl);
        const url = window.URL.createObjectURL(blob1); //获得一个pdf的url对象
        this.downloadPDF(url, name);
      });
    },
    // 当 base64 过大时会导致页面无法加载，需要转化成 blob 格式
    dataURLtoBlob(dataurl) {
      const arr = dataurl.split(",");
      // 注意base64的最后面中括号和引号是不转译的
      const _arr = arr[1].substring(0, arr[1].length - 2);
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(_arr);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], {
        type: mime,
      });
    },
    downloadPDF(url, fileName) {
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
    },
    downloadToPDF(dom, name) {
      const element = document.getElementById(dom);
      const opt = {
        margin: 0,
        filename: name + ".pdf",
        image: { type: "jpeg", quality: 1 },
        html2canvas: { scale: 1 },
        jsPDF: {
          unit: "in",
          format: "a4",
          orientation: "portrait",
        },
        pagebreak: { mode: ["css", "legacy"], avoid: [".impedance", ".zk"] },
      };
      html2pdf().set(opt).from(element).save();
    },
    rowClassName(record, index) {
      return "zk";
    },
    // arrayBufferToFormData(arrayBuffer, type) {
    //   const formData = new FormData();
    //   const blob = new Blob([arrayBuffer]);
    //   if (type == "pdf") {
    //     formData.append("file", blob, "filename.pdf");
    //   } else if (type == "png") {
    //     formData.append("file", blob, "Stacking.png");
    //   }
    //   return formData;
    // },
    //避免分页被截断
    outPutPdfFn() {
      let vm = this;
      const A4_WIDTH = 592.28;
      const A4_HEIGHT = 841.89;
      // $myLoading 自定义等待动画组件，实现导出事件的异步等待交互
      vm.$nextTick(() => {
        // dom的id。
        let target;
        if (!this.reportType) {
          target = document.getElementById("pdfDom");
        } else {
          target = document.getElementById("pdfDom1");
        }
        let pageHeight = (target.scrollWidth / A4_WIDTH) * A4_HEIGHT;
        // 获取分割dom，此处为class类名为item的dom
        let lableListID = document.getElementsByClassName("item");
        // 进行分割操作，当dom内容已超出a4的高度，则将该dom前插入一个空dom，把他挤下去，分割
        var datanum = this.impedanceTable.length;
        for (let i = 0; i < lableListID.length; i++) {
          var multiple = "";
          multiple = Math.ceil((lableListID[i].offsetTop + lableListID[i].offsetHeight) / pageHeight);
          let isSplit = this.isSplit(lableListID, i, multiple * pageHeight);
          if (isSplit) {
            let divParent = lableListID[i].parentNode; // 获取该div的父节点
            let newNode = document.createElement("div");
            newNode.className = "emptyDiv";
            newNode.style.background = "#fff";
            let _H = multiple * pageHeight - (lableListID[i].offsetTop + lableListID[i].offsetHeight);
            let pre = lableListID[i].previousElementSibling;
            let next = lableListID[i].nextSibling; // 获取div的下一个兄弟节点
            newNode.style.height = _H + 70 + "px";
            if (_H + 70 > 382) {
              newNode.style.height = _H + 70 - 382 + "px";
            }
            newNode.style.width = "100%";
            // 判断兄弟节点是否存在
            if (next && isSplit) {
              // 存在则将新节点插入到div的下一个兄弟节点之前，即div之后
              divParent.insertBefore(newNode, next);
            } else {
              // 不存在则直接添加到最后,appendChild默认添加到divParent的最后
              divParent.appendChild(newNode);
            }
          }
        }
        if (this.factory1 == 37 || this.factory1 == 22) {
          if (this.reportType) {
            htmlToPdf("pdfDom1", this.laminationData.stackUpInfo.pdctno + " Stacked impedance information");
          } else {
            htmlToPdf("pdfDom", this.laminationData.stackUpInfo.pdctno + " 叠层阻抗信息");
          }
        } else if (this.factory1 == 12) {
          if (this.reportType) {
            htmlToPdf("pdfDom1", this.laminationData.stackUpInfo.pdctno + "_Stacked_impedance_information");
          } else {
            htmlToPdf("pdfDom", this.laminationData.stackUpInfo.pdctno + "_叠层阻抗信息");
          }
        } else if (this.factory1 == 58 || this.factory1 == 59) {
          if (this.reportType) {
            htmlToPdf("pdfDom1", this.laminationData.stackUpInfo.pdctno + "Stacked_impedance_information confirmation");
          } else {
            htmlToPdf("pdfDom", this.laminationData.stackUpInfo.pdctno + "阻抗信息确认");
          }
        } else {
          if (this.reportType) {
            htmlToPdf("pdfDom1", this.laminationData.stackUpInfo.pdctno);
          } else {
            htmlToPdf("pdfDom", this.laminationData.stackUpInfo.pdctno);
          }
        }
      });
    },
    // 判断是否需要添加空白div
    isSplit(nodes, index, pageHeight) {
      // 计算当前这块dom是否跨越了a4大小，以此分割
      if (
        nodes[index].offsetTop + nodes[index].offsetHeight < pageHeight &&
        nodes[index + 1] &&
        nodes[index + 1].offsetTop + nodes[index + 1].offsetHeight > pageHeight
      ) {
        return true;
      }
      return false;
    },

    impTypeFilter(val) {
      var arr = [
        { text: "外层单端(盖油)", valueMember: "O_S(SM)", imp_CtlThicknessInH: 0 },
        { text: "外层差分(盖油)", valueMember: "O_D(SM)", imp_CtlThicknessInH: 0 },
        { text: "内层单端", valueMember: "I_S_2", imp_CtlThicknessInH: 2 },
        { text: "内层差分", valueMember: "I_D_2", imp_CtlThicknessInH: 2 },
        { text: "外层单端共面地(盖油)", valueMember: "O_S_C(SM)", imp_CtlThicknessInH: 0 },
        { text: "外层差分共面地(盖油)", valueMember: "O_D_C(SM)", imp_CtlThicknessInH: 0 },
        { text: "内层单端(1层屏蔽)", valueMember: "I_S_1", imp_CtlThicknessInH: 2 },
        { text: "内层单端(1层屏蔽A)", valueMember: "I_S_1A", imp_CtlThicknessInH: 2 },
        { text: "内层单端(1层屏蔽B)", valueMember: "I_S_1B", imp_CtlThicknessInH: 2 },
        { text: "内层差分(1层屏蔽)", valueMember: "I_D_1", imp_CtlThicknessInH: 2 },
        { text: "内层差分(1层屏蔽A)", valueMember: "I_D_1A", imp_CtlThicknessInH: 2 },
        { text: "内层差分(1层屏蔽B)", valueMember: "I_D_1B", imp_CtlThicknessInH: 2 },
        { text: "内层单端共面地", valueMember: "I_S_C_2", imp_CtlThicknessInH: 2 },
        { text: "内层单端共面地(1层屏蔽)", valueMember: "I_S_C_1", imp_CtlThicknessInH: 2 },
        { text: "内层差分共面地", valueMember: "I_D_C_2", imp_CtlThicknessInH: 2 },
        { text: "内层差分共面地(1层屏蔽)", valueMember: "I_D_C_1", imp_CtlThicknessInH: 2 },
        { text: "内层层间差分", valueMember: "I_L_D_2", imp_CtlThicknessInH: 2 },
        { text: "外层单端(开窗)", valueMember: "O_S", imp_CtlThicknessInH: 0 },
        { text: "外层差分(开窗)", valueMember: "O_D", imp_CtlThicknessInH: 0 },
        { text: "外层单端共面地(开窗)", valueMember: "O_S_C", imp_CtlThicknessInH: 0 },
        { text: "外层差分共面地(开窗)", valueMember: "O_D_C", imp_CtlThicknessInH: 0 },
        { text: "外层单端共面地(无屏蔽)(开窗)", valueMember: "O_S_C_0", imp_CtlThicknessInH: 0 },
        { text: "外层单端共面地(无屏蔽)(盖油)", valueMember: "O_S_C_0(SM)", imp_CtlThicknessInH: 0 },
        { text: "外层差分共面地(无屏蔽)(开窗)", valueMember: "O_D_C_0", imp_CtlThicknessInH: 0 },
        { text: "外层差分共面地(无屏蔽)(盖油)", valueMember: "O_D_C_0(SM)", imp_CtlThicknessInH: 0 },
        { text: "内层单端共面地(无屏蔽)", valueMember: "I_S_C_0", imp_CtlThicknessInH: 2 },
        { text: "内层差分共面地(无屏蔽)", valueMember: "I_D_C_0", imp_CtlThicknessInH: 2 },
      ];
      let str = arr.find(item => item.valueMember == val).text;
      return str;
    },
    styleHeight_(val) {
      let newArr = [],
        topArr = [];
      let arr = [];
      this.laminationData.stackUpDrills.forEach(item => {
        item.count = item.endLayer - item.startLayer;
        arr.push(item);
      });
      this.laminationData.stackUpDrills = arr;
      const forwardIntervals = arr.filter(interval => interval.startLayer <= interval.endLayer);
      const backwardIntervals = arr.filter(interval => interval.startLayer > interval.endLayer);
      forwardIntervals.sort((a, b) => {
        // 首先比较 count，如果 count 相同，则比较 startLayer
        if (b.count !== a.count) {
          return b.count - a.count; // 降序排序 count
        } else {
          return a.startLayer - b.startLayer; // 当 count 相同时，升序排序 startLayer
        }
      });
      backwardIntervals.sort((a, b) => {
        // 首先比较 count，如果 count 相同，则比较 endLayer
        if (b.count !== a.count) {
          return b.count - a.count; // 降序排序 count
        } else {
          return a.endLayer - b.endLayer; // 当 count 相同时，升序排序 endLayer
        }
      });
      // let all = [...forward, ...backward];
      let allIntervals = [...forwardIntervals, ...backwardIntervals];
      let all = this.groupIntervals(allIntervals);
      // 获取所有 class 为 Throughout 的元素
      let ThroughoutElements = document.querySelectorAll(".Throughout");

      // 遍历所有匹配的元素 自适应叠层结构宽度
      ThroughoutElements.forEach(element => {
        if (all.length <= 9) {
          element.style.width = "235px";
        } else {
          element.style.width = Number(all.length - 9) * 20 + 235 > 420 ? "420px" : Number(all.length - 9) * 20 + 235 + "px";
        }
      });
      this.laminationData.stackUpDrills.forEach((ite, index) => {
        //if (!ite.drillName.includes("ksk")) {
        // 特殊处理：开始层和结束层相同的情况
        const isSingleLayer = ite.startLayer == ite.endLayer;
        let height_ = 0;
        let top_ = 0;
        let startIndex = this.tableData.findIndex(item => {
          return item.child[0].stackUpLayerNo_ == ite.startLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.startLayer);
        });
        let endIndex = this.tableData.findIndex(item => {
          return item.child[0].stackUpLayerNo_ == ite.endLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.endLayer);
        });
        if (Number(startIndex) > Number(endIndex)) {
          endIndex = this.tableData.findIndex(item => {
            return item.child[0].stackUpLayerNo_ == ite.startLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.startLayer);
          });
          startIndex = this.tableData.findIndex(item => {
            return item.child[0].stackUpLayerNo_ == ite.endLayer || (item.child.length == "3" && item.child[2].stackUpLayerNo_ == ite.endLayer);
          });
        }
        newArr = this.tableData.slice(startIndex, endIndex + 1);
        topArr = this.tableData.slice(0, startIndex + 1);
        if (isSingleLayer) {
          height_ += 35;
        } else {
          newArr.forEach(list => {
            if (list.stackUpMTR_ == "OZ" && list.child.length == "1") {
              height_ += 30;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[2].stackUpLayerNo_ == ite.startLayer &&
              Number(ite.startLayer) < Number(ite.endLayer)
            ) {
              height_ += 30;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[2].stackUpLayerNo_ == ite.endLayer &&
              Number(ite.endLayer) < Number(ite.startLayer)
            ) {
              height_ += 30;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[0].stackUpLayerNo_ == ite.endLayer &&
              Number(ite.startLayer) < Number(ite.endLayer)
            ) {
              height_ += 30;
            } else if (
              list.stackUpMTR_ == "Core" &&
              list.child.length == "3" &&
              list.child[0].stackUpLayerNo_ == ite.startLayer &&
              Number(ite.endLayer) < Number(ite.startLayer)
            ) {
              height_ += 30;
            } else if (list.stackUpMTR_ == "Core" && list.child.length == "3") {
              height_ += 74;
            } else {
              height_ += 74;
            }
          });
        }
        topArr.forEach(res => {
          if (res.stackUpMTR_ == "OZ" && res.child.length == "1" && ite.startLayer == "1") {
            top_ += 30;
          } else if (res.stackUpMTR_ == "OZ" && res.child.length == "1") {
            top_ += 30;
          } else if (
            res.stackUpMTR_ == "Core" &&
            res.child.length == "3" &&
            ite.startLayer == res.child[0].stackUpLayerNo_ &&
            Number(ite.startLayer) <= Number(ite.endLayer)
          ) {
            top_ += 30;
          } else if (
            res.stackUpMTR_ == "Core" &&
            res.child.length == "3" &&
            ite.endLayer == res.child[0].stackUpLayerNo_ &&
            Number(ite.endLayer) <= Number(ite.startLayer)
          ) {
            top_ += 30;
          } else if (res.stackUpMTR_ == "Core" && res.child.length == "3") {
            top_ += 74;
          } else {
            top_ += 74;
          }
        });
        all.forEach((data1, ind) => {
          data1.forEach(item => {
            if (item === ite) {
              this.$refs["dir_" + index][0].style.left = (ind + 1) * 18 + 45 + "px";
              this.$refs["dir1_" + index][0].style.left = (ind + 1) * 18 + 45 + "px";
            }
          });
        });
        this.$refs["dir_" + index][0].style.height = height_ - 30 + "px";
        this.$refs["dir_" + index][0].style.top = (Number(ite.startLayer) <= Number(ite.endLayer) ? top_ + 22 : top_ + 28) + "px";
        this.$refs["dir1_" + index][0].style.height = height_ - 30 + "px";
        this.$refs["dir1_" + index][0].style.top = (Number(ite.startLayer) <= Number(ite.endLayer) ? top_ + 22 : top_ + 28) + "px";
        // }
      });
    },
    // 分组逻辑：正向和反向intervals合并
    groupIntervals(intervals) {
      const groups = [];

      // 按起始层升序排序（正向）或按结束层升序排序（反向）
      intervals.sort((a, b) => {
        if (a.startLayer <= a.endLayer) {
          return a.startLayer - b.startLayer; // 正向
        } else {
          return a.endLayer - b.endLayer; // 反向
        }
      });

      // 找到最小的起始层（正向）或最小的结束层（反向）
      let minLayer = Math.min(...intervals.map(item => (item.startLayer <= item.endLayer ? item.startLayer : item.endLayer)));

      // 找到所有起始层为minLayer的intervals（正向）或结束层为minLayer的intervals（反向）
      let initialIntervals = intervals.filter(item => (item.startLayer <= item.endLayer ? item.startLayer : item.endLayer) === minLayer);

      // 将这些intervals分别作为初始组
      initialIntervals.forEach(interval => {
        groups.push([interval]);
      });

      // 遍历剩余的intervals
      intervals.forEach(interval => {
        if ((interval.startLayer <= interval.endLayer ? interval.startLayer : interval.endLayer) !== minLayer) {
          let addedToGroup = false;

          // 优先尝试将interval加入到结束层相等的组中
          for (let group of groups) {
            const lastIntervalInGroup = group[group.length - 1];
            // 新增条件：如果结束层相等，优先合并
            if (
              (interval.startLayer <= interval.endLayer &&
                lastIntervalInGroup.startLayer > lastIntervalInGroup.endLayer &&
                interval.endLayer == lastIntervalInGroup.endLayer) ||
              (interval.startLayer > interval.endLayer &&
                lastIntervalInGroup.startLayer <= lastIntervalInGroup.endLayer &&
                interval.endLayer == lastIntervalInGroup.endLayer)
            ) {
              // 检查是否与组内所有区间兼容 防止冲突
              if (this.isCompatibleWithGroup(interval, group)) {
                group.push(interval);
                addedToGroup = true;
                console.log("正向反向结束层相等，合并");
                break;
              }
            }
          }

          // 如果结束层不相等，尝试其他合并条件
          if (!addedToGroup) {
            for (let group of groups) {
              const lastIntervalInGroup = group[group.length - 1];
              if (this.canMerge(interval, lastIntervalInGroup) && this.isCompatibleWithGroup(interval, group)) {
                group.push(interval);
                console.log("常规合并");
                addedToGroup = true;
                break;
              }
            }
          }

          // 如果没有找到可以加入的组，则创建新组
          if (!addedToGroup) {
            groups.push([interval]);
          }
        }
      });

      return groups;
    },

    // 判断一个区间是否与组内所有区间兼容
    isCompatibleWithGroup(interval, group) {
      for (let existingInterval of group) {
        if (!this.canMerge(interval, existingInterval)) {
          return false; // 只要有一个区间不能合并，就返回false
        }
      }
      return true; // 如果可以与组中的所有区间合并，返回true
    },

    // 判断两个区间是否可以合并到一组
    canMerge(interval1, interval2) {
      // 正向区间和反向区间可以合并的条件：正向的结束层小于等于反向的结束层
      if (interval1.startLayer <= interval1.endLayer && interval2.startLayer > interval2.endLayer) {
        return interval1.endLayer <= interval2.endLayer;
      } else if (interval1.startLayer > interval1.endLayer && interval2.startLayer <= interval2.endLayer) {
        return interval1.endLayer >= interval2.endLayer;
      }

      // 正向区间和正向区间：当前区间的起始层大于组的最后一个区间的结束层
      if (interval1.startLayer <= interval1.endLayer && interval2.startLayer <= interval2.endLayer) {
        return interval1.startLayer > interval2.endLayer;
      }

      // 反向区间和反向区间：当前区间的结束层大于组的最后一个区间的起始层
      if (interval1.startLayer > interval1.endLayer && interval2.startLayer > interval2.endLayer) {
        return interval1.endLayer > interval2.startLayer;
      }

      return false;
    },
  },
  mounted() {
    this.styleHeight_();
    //将图片网络地址转base64格式  图片网络地址使用html2pdf导出不展示
    this.toBase64(this.laminationData.stackUpInfo.enterpriseLogo);
  },
};
</script>

<style lang="less" scoped>
.item {
  background-color: white;
}
.zuk {
  /deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-row {
    background-color: white;
  }
}
@media print {
  .printContent {
    font-size: 12px; /* 在打印时使用的字体大小 */
  }
}
.drillClss {
  left: 0;
  top: 0;
  padding: 23px 12px 20px;
  width: 100%;
  position: absolute;
  height: 100%;
}
.drillItem {
  width: 9px;
  position: absolute;
  background: #0000cc;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItem:after {
  content: "";
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #0000cc;
  position: absolute;
  bottom: -8px;
  left: -3.5px;
}
.drillItem1 {
  width: 9px;
  position: absolute;
  background: #0000cc;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItem1:after {
  content: "";
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #0000cc;
  position: absolute;
  // bottom: -8px;
  top: -6px;
  left: -3.5px;
}
.drillItemred {
  width: 9px;
  position: absolute;
  background: rgb(177, 7, 7);
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItemred:after {
  content: "";
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgb(177, 7, 7);
  position: absolute;
  bottom: -8px;
  left: -3.5px;
}
.drillItemred1 {
  width: 9px;
  position: absolute;
  background: rgb(177, 7, 7);
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItemred1:after {
  content: "";
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid rgb(177, 7, 7);
  position: absolute;
  // bottom: -8px;
  top: -6px;
  left: -3.5px;
}
.pdfDom {
  overflow: auto;
  //height: 735px;
  background-color: white;
  margin: -25px;
  &::-webkit-scrollbar {
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #ff9900;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
.pdfmargin {
  margin-top: 26px;
}
#pdfDom {
  padding: 25px;
}
#pdfDom1 {
  padding: 25px;
}
.headerTitle {
  background-color: white;
  display: flex;
  // margin-bottom: 20px;
  img {
    margin-left: 50px;
    height: 70px;
  }
  h2 {
    font-size: 22px;
    color: #000000;
    // font-weight: 600;
  }
  p {
    font-size: 12px;
    color: #000000;
    // font-weight: 600;
  }
  span {
    font-size: 16px;
    color: #990099;
  }
}
.reportTable {
  border: 1px solid;
  display: flex;
  flex-wrap: wrap;
  .thickness {
    width: 100%;
    text-align: left;
    padding-left: 15px;
    line-height: 30px;
    font-weight: bold;
    font-size: 14px;
    border: 1px solid;
    color: #000000;
  }
  .layerName {
    width: 45px;
    height: 100%;
    // border: 2px solid;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    color: #0000cc;
  }
  .ozlastClass {
    align-items: center;
  }
  .OZclass {
    height: 30px;
    display: flex;
    overflow: hidden;
    align-items: center;
    .ozContent {
      //border-bottom: 1px solid;
      width: calc(100% - 43px);
      // margin-left: 8px;
      display: flex;
      align-items: end;
      .oz_bg {
        width: 100%;
        // border-radius: 7px;
        height: 10px;
        overflow: hidden;
        background: #f4a460;
        // background: url("../../../assets/img/pp.png") repeat-x;
        // background-position-x: -12px;
      }
      .oz_active {
        // background: url("../../../assets/img/pp2.png") repeat-x;
        // background-position-x: -12px;
        background: #f4a460;
        width: 100%;
      }
    }
  }
  .PPclass {
    overflow: hidden;
    .PPContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 43px);
      height: 34px;
      margin: 20px 0;
      background: #228b22;
    }
  }
  .jsjclass {
    display: flex;
    overflow: hidden;
    .jsjContent {
      float: right;
      margin-left: 15px;
      margin-top: 10px;
      margin-bottom: 10px;
      width: 100%;
      height: 12px;
      //margin: 10px 0;
      background: #fcb505;
      border-radius: 5px;
    }
  }
  .coreClass {
    .ozActive:nth-of-type(1) {
      // margin-bottom: 16px;
      margin-bottom: 12px;
      height: 32px;
    }
    position: relative;
    .coreActive {
      position: absolute;
      width: 100%;
      top: 17px;
    }
    // .ozContent2 {
    //   border-bottom: 2px solid black;
    //   width: 100%;
    // }
    .ozContent2 {
      float: right;
      margin-left: 10px;
      width: calc(100% - 43px);
      height: 10px;
      margin: 0 0 6px -2px;
      background: #f4a460;
    }
    .core-box {
      // height: 32px;
      height: 34px;
      width: 100%;
      overflow: hidden;
      .CoreContent {
        height: 100%;
        float: right;
        // background: #FCB505;
        // background: #FCB408;
        width: calc(100% - 43px);
        background: #f0e68c;
      }
    }
  }
  // .coreClass {
  //   .ozActive:nth-of-type(1) {
  //     margin-bottom: 20px;
  //   }
  //   position: relative;
  //   .coreActive {
  //     position: absolute;
  //     width: 100%;
  //     top: 16px;
  //   }
  //   .ozContent2 {
  //     border-bottom: 2px solid black;
  //     width: 100%;
  //   }
  //   .core-box {
  //     height: 48px;
  //     width: 100%;
  //     overflow: hidden;
  //     .CoreContent {
  //       height: 100%;
  //       float: right;
  //       background: #FCB505;
  //       width: calc(100% - 68px);
  //       background: #FCB408;
  //     }
  //   }

  // }
  .GBClass {
    overflow: hidden;
    .gbContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 43px);
      height: 35px;
      margin: 20px 0;
      background: #fcb505;
      border-radius: 5px;
    }
  }
  .parameterClass {
    flex: 1;
    width: 70%;
    table tr th {
      text-align: center;
      color: #000000;
      width: 79px;
    }
    table {
      border-left: 2px solid black;
    }
    table tr:nth-child(1) {
      border: 2px solid black;
      border-left: none;
    }
    table tr th {
      border-right: 2px solid black;
      color: #000000;
      font-weight: bold;
    }
    table tr td {
      text-align: center;
      font-weight: bold !important;
      color: #0000cc;
      border-right: 2px solid black;
      border-bottom: 2px solid black;
    }
    .paramOZ {
      height: 27px;
      line-height: 27px;
    }
    .paramCore {
      height: 74px;
      td {
        white-space: pre-line;
      }
    }

    .paramPP {
      height: 74px;
      td {
        white-space: pre-line;
      }
    }
    .paramPP .param-cell {
      position: relative;
      margin-bottom: -0.005em;
    }
    .paramGB {
      height: 74px;
      td {
        white-space: pre-line;
      }
    }
    .paramGB .param-cell {
      position: relative;
      margin-bottom: -0.005em;
    }
  }
}
.tdclass1 {
  width: 270px;
  overflow-wrap: anywhere;
}
.tdclass2 {
  width: 266px;
  overflow-wrap: anywhere;
}
.impedance {
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid black;
  border-bottom: 1px solid black;
  padding-left: 5px;
  h4 {
    width: 100%;
    color: #000000;
  }
  h3 {
    display: flex;
    width: 260px;
    justify-content: space-between;
    p {
      margin: 0;
      margin-right: 10px;
    }
    p:nth-child(1) {
      margin: 0;
    }
  }
  // .imp_left{
  // }
  .line_flex {
    display: flex;
    p {
      margin: 0;
      margin-right: 15px;
      font-size: 14px;
      line-height: 25px;
      font-weight: 500;
      color: #000000;
    }
    // p:nth-child(1) {
    //   width: 200px;
    // }
    p:nth-child(2) {
      width: 40px;
    }
  }
  .imp_center {
    margin: 0 25px;
    img {
      padding: 15px;
      background: #008181;
    }
  }
  .imp_right {
    .line_flex {
      p:nth-child(1) {
        width: 40px;
      }
    }
  }
}
/deep/.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 16px 16px;
  overflow-wrap: break-word;
  max-width: 74px;
}
/deep/ .ant-table-body {
  .ant-table-thead {
    tr > th {
      padding: 5px 0;
      border-color: black;
      color: #000000;
      &:last-child {
        border-right: 0;
      }
    }
  }
  .ant-table-tbody {
    tr > td {
      padding: 7px 0;
      border-color: black;
      &:last-child {
        border-right: 0;
      }
    }
  }
}
</style>
