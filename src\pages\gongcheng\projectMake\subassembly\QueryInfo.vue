<!-- 工程管理 - 工程制作/QAE审核 - 查询 -->
<template>
  <div ref="SelectBox">
    <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 14 }">
      <a-form-item label="生产编号">
        <a-input v-model="OrderNumber" placeholder="请输入生产编号" :autoFocus="autoFocus" v-focus-next-on-enter="''" ref="input1" />
      </a-form-item>
      <a-form-item label="客户型号" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
        <a-input v-model="pcbFileName" placeholder="请输入客户型号" />
      </a-form-item>
      <a-form-item label="订单状态">
        <a-select ref="select" v-model="OrderStates" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
          <a-select-option v-for="(ite, index) in list" :key="index" :value="ite.value" :lable="ite.text">{{ ite.text }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="制作人" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
        <a-input v-model="proAdminName" />
      </a-form-item>
      <!-- <a-form-model-item label="订单渠道" prop="tradeTypeSrc" style="width:100%; margin:0">
              <a-select v-model="TradeTypeSrc"  placeholder="请选择" showSearch optionFilterProp="label">
                   <a-select-option  v-for=" item in tradeTypeSrcList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
          </a-form-model-item> -->
    </a-form>
  </div>
</template>

<script>
import { getTradeTypeSrcList } from "@/services/projectDisptch";
export default {
  name: "QueryInfo",

  data() {
    return {
      OrderNumber: "",
      pcbFileName: "",
      OrderStates: "",
      TradeTypeSrc: "",
      proAdminName: "",
      autoFocus: true,
      tradeTypeSrcList: [],
      list: [
        { value: "", text: "请选择" },
        { value: "10", text: "待制作" },
        { value: "20", text: "制作中" },
        { value: "15", text: "EQ待审" },
        { value: "24", text: "已问客" },
      ],
    };
  },
  created() {
    getTradeTypeSrcList().then(res => {
      this.tradeTypeSrcList = res?.data;
    });
  },
  methods: {
    //  keyupEnter1(){
    //     this.$emit('keyupEnter1')
    // }
  },
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-time-picker-input {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/ .ant-input-number-input {
  font-weight: 500;
}
/deep/.ant-input {
  font-weight: 500;
}
</style>
