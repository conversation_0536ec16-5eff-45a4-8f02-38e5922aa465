<template>
    <common-layout>
      <div class="login_box" ref="login_box">
        <div class="login_left">
        </div>
        <div class="login_right">
          <div class="top">
          </div>
          <div style="display: flex;">
            <div style="width:100%">
              <a-tabs default-active-key="1"  @change="callback" v-if="login">
            <a-tab-pane key="1" tab="短信登录"  force-render>
              <phone-login :typpe="'pe'"/>
            </a-tab-pane>
          </a-tabs>
           <register-form v-else-if="activekey != 3" @register="register"></register-form>
            </div>
          </div>
     </div>
      </div>
      <a-modal
          title="修改密码"
          :visible="dataVisible"
          @cancel="reportHandleCancel"
          @ok="handleOk"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
      >
        <change-password ref='ChangePassword' />
      </a-modal>
    </common-layout>
  </template>
  
  <script>
  import CommonLayout from "@/layouts/CommonLayout";
  import {
    login,
  } from "@/services/user";
  import { getUserInfo,
  } from "@/services/identity/user";
  import PhoneLogin from "@/pages/login/modules/PhoneLogin";
  import { setAuthorization } from "@/utils/request";
  import { loadRoutes } from "@/utils/routerUtil";
  import { mapMutations } from "vuex";
  import RegisterForm from "@/pages/login/modules/RegisterForm";
  import ChangePassword from '@/layouts/header/ChangePassword';
  import {newPassword} from "@/services/projectDisptch";
  import {mapState,} from 'vuex'
  import { setuserName  } from "@/utils/request";
  import Cookie from 'js-cookie';
  import wxlogin from "vue-wxlogin";
  import axios from 'axios'
  import qs from 'querystring'
  export default {
    name: "Pelogin",
    components: {RegisterForm, CommonLayout,PhoneLogin,ChangePassword,},
    data() {
      return {
        logging: false,
        error: "",
        form: this.$form.createForm(this),
        visible: false,
        confirmLoading: false,
        tenantName: "",
        content: '发送验证码',  // 按钮里显示的内容
        totalTime: 60,      //记录具体倒计时时间
        disabledBtn:false,
        inputStat:false,//验证码输入框
        login:true,
        dataVisible:false,
        isShowForm:false,
        activekey:'1',
        loggingwx:false,
        loginWXFlg:false,
      };
    },
    computed: {
      systemName() {
        return this.$store.state.setting.systemName;
      },
      ...mapState('account', ['user','buryingpoint']), 
    },
    watch:{
      totalTime(val){
        if(val==0){
          this.disabledBtn=false
          this.content='发送验证码'
        }
      }
    },
    mounted(){
    window.addEventListener('resize', this.handleResize, true)
    if (window.innerWidth <= 576) {
          this.$refs.login_box.style.width = '360px';
        }else if(window.innerWidth <= 801 && window.innerWidth >= 576){
          this.$refs.login_box.style.width = '554px';
        }else{
          this.$refs.login_box.style.width = '';
          this.$refs.login_box.style.position = 'relative';
          this.$refs.login_box.style.left = '0';
        }
    },
    beforeDestroy(){
      window.removeEventListener('resize', this.handleResize, true)
    },
    methods: {
      ...mapMutations("account", ["setUser", "setPermissions", "setRoles",'clearBuryingPoint','dataprocessing']),
        //账号验证
        handleResize() {
        this.$nextTick(() => { 
        if (window.innerWidth <= 576 && window.innerWidth <= 801) {
          this.$refs.login_box.style.width = '360px';
        }else if(window.innerWidth <= 801 && window.innerWidth >= 576){
          this.$refs.login_box.style.width = '554px';
        }else{
          this.$refs.login_box.style.width = '';
          this.$refs.login_box.style.position = 'relative';
          this.$refs.login_box.style.left = '0';
        }
         })
      },
      callback(key) {
        this.activekey = key        
      },
      countDown() {
        this.disabledBtn=true
        let clock = window.setInterval(() => {
          this.totalTime--
          this.content = this.totalTime + 's后重新发送'
          if(this.totalTime==0){
            this.disabledBtn=false
            this.content ='发送验证码'
            this.totalTime=60
            clearInterval(clock)
          }
        },1000)
        let parmas={}
        parmas.userName=this.form.getFieldValue("name")
        parmas.smsFrom='MES后台'
        parmas.sendType='登录验证'
      },   
      reportHandleCancel(){
        this.dataVisible = false
        this.logging = false
        this.form.setFieldsValue({
            name: '',
            password:'',
          })
      },
      handleOk(){     
        let params = this.$refs.ChangePassword.newNumber
        newPassword(params).then(res=> {
          if (res) {
            this.$message.success('密码修改成功')
          }
        }).finally(()=>{
          this.dataVisible = false
          this.logging = false
          this.form.setFieldsValue({
            name: '',
            password:'',
          })
        })
  
      },
      handlePermissions(obj) {
        let permissions = [];
        if (!obj) {
          return permissions;
        }
        permissions = Object.keys(obj).map((x) => {
          return {
            id: x,
            operation: [],
          };
        });
        return permissions;
        // let list = Object.keys(obj).map((x) => {
        //   let n = x.split(".").length - 1;
        //   return {
        //     val: x,
        //     num: n,
        //   };
        // });
        // let idList = list.filter((x) => x.num == 1);
        // permissions = idList.map((x) => {
        //   let operation = list
        //     .filter((y) => y.num == 2 && y.val.indexOf(x.val) > -1)
        //     .map((y) => {
        //       return y.val.split(".")[2];
        //     });
        //   return {
        //     id: x.val,
        //     operation: operation,
        //   };
        // });
        // return permissions;
      },
      register(){
        this.login = !this.login
      },
     
    },
  };
  </script>
  
  <style lang="less" scoped>
  /deep/.ant-form-item {
      margin-bottom: 24px;
  }
  .qrcode iframe{
    padding-left:0;
    position: absolute;
    left:50%;
    top:50%;
    transform: translate(-50%, -50%);
  }
    /deep/.ant-btn-lg{
      padding: 0 3px!important;
      font-size: 14px;
    }
  .common-layout {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    .login_box{
      background-color: transparent;
      height: 460px;
      display: flex;
      top: 50%;
      transform: translate(0,2px);
      .login_left{
        width: 50%;
        height: 100%;
        float: left;
        background: #f90;
        display: none;
      }
      .login_right{     
        width: 100%;
        height: 100%;
        background: #fff;
        padding: 60px 30px 0;  
      }
    }
    .top {
      text-align: center;
      margin-bottom: 30px;
      .header {
        height: 44px;
        line-height: 44px;
        a {
          text-decoration: none;
        }
        .logo {
          height: 44px;
          vertical-align: top;
          margin-right: 16px;
        }
        .title {
          font-size: 24px;
          color: @title-color;
          font-weight: 600;
          position: relative;
          top: 2px;
  
        }
      }
      .desc {
        font-size: 14px;
        color: @text-color-second;
        margin-top: 12px;
        margin-bottom: 40px;
      }
    }
    .login {
      width: 320px;
      margin: 0 auto;
      .icon {
        font-size: 24px;
        color: @text-color-second;
        margin-left: 16px;
        vertical-align: middle;
        cursor: pointer;
        transition: color 0.3s;
  
        &:hover {
          color: @primary-color;
        }
      }
    }
  }
  </style>
  