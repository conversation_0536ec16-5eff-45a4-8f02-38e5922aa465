<!-- 工具管理- 叠层阻抗-订单信息表单 -->
<template>
  <div
    :class="['basicInfo', collapse ? 'collapseNoActive' : 'collapseActive']"
    :style="{ height: tableHeight + 'px', overflow: 'auto' }"
    ref="SelectBox"
  >
    <div class="basicInfoTitle">
      <span class="head-menu">基础信息</span>
      <!-- <a-icon :type="collapse ? 'double-left': 'double-right'"  @click="collapseC"/> -->
    </div>
    <div class="basicInfoContent">
      <a-form-model :model="form">
        <a-row>
          <a-col :span="18">
            <a-form-model-item label="生产编号" :label-col="{ span: 6 }" :wrapper-col="{ span: 17 }" class="pctno">
              <a-auto-complete
                v-model="form.pdctno"
                :dataSource="Storedata"
                @select="onSelect"
                @search="handleSearch"
                :defaultActiveFirstOption="false"
                @keydown.native.enter.prevent="getoldData"
                ref="input1"
                @change="updatamessage"
                :filter-option="filterOption"
                :getPopupContainer="() => this.$refs.SelectBox"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="层数" :label-col="{ span: 11 }" :wrapper-col="{ span: 12 }">
              <a-input :title="form.layers" v-model="form.layers" v-focus-next-on-enter="'input3'" ref="input2" @blur="layerChange" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="14">
            <a-form-model-item label="板材型号" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
              <a-select
                v-model="form.boardType"
                :title="form.boardType"
                @change="boardChange"
                allowClear
                show-search
                option-filter-prop="children"
                :filter-option="filterOption"
                :getPopupContainer="() => this.$refs.SelectBox"
              >
                <a-select-option v-for="item in boardTypeList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="要求板厚" :label-col="{ span: 11 }" :wrapper-col="{ span: 12 }">
              <a-input :title="form.finishBoardThickness" v-model="form.finishBoardThickness" ref="input3" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="14">
            <a-form-model-item label="PP 型号" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
              <a-select
                :title="form.ppType"
                v-model="form.ppType"
                @change="ppChange"
                allowClear
                :getPopupContainer="() => this.$refs.SelectBox"
                show-search
                :filter-option="filterOption"
                option-filter-prop="children"
              >
                <a-select-option v-for="item in ppTypeList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="压合板厚" :label-col="{ span: 11 }" :wrapper-col="{ span: 12 }">
              <a-input
                :title="form.pressingThickness"
                v-model="form.pressingThickness"
                disabled
                :class="{ redBg: pressingThicknessBg == 'error', greenBg: pressingThicknessBg == 'finish' }"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="14">
            <a-form-model-item label="压合公差" :labelCol="{ span: 8 }" :wrapperCol="{ span: 15 }">
              <a-input :title="form.stackUpTol" v-model="form.stackUpTol" />
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="成品板厚" :label-col="{ span: 11 }" :wrapper-col="{ span: 12 }">
              <a-input :title="form.finishedThickness" v-model="form.finishedThickness" disabled />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="14">
            <a-form-model-item label="上限公差" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
              <a-input :title="form.highLimit" v-model="form.highLimit" @change="LimitChange(form.highLimit)" />
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="工程上限" :label-col="{ span: 11 }" :wrapper-col="{ span: 12 }">
              <a-input :title="form.engineeringHiht" v-model="form.engineeringHiht" disabled />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="14">
            <a-form-model-item label="下限公差" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
              <a-input :title="form.lowLimit" v-model="form.lowLimit" @change="LimitChange(form.lowLimit)" />
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="工程下限" :label-col="{ span: 11 }" :wrapper-col="{ span: 12 }">
              <a-input :title="form.engineeringLow" v-model="form.engineeringLow" disabled />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="14">
            <a-form-model-item label="层压不可更改" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <a-checkbox v-model="form.isChangeLayerPres" disabled></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="对压" :label-col="{ span: 16 }" :wrapper-col="{ span: 8 }">
              <a-checkbox v-model="form.isPressure"></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="光板" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" class="gbstackup">
              <!-- <a-checkbox v-model="form.gBStackUp"></a-checkbox> -->
              <a-select v-model="form.gbcount" showSearch optionFilterProp="lable">
                <a-select-option v-for="(ite, indx) in gblist" :key="indx" :value="ite.value">{{ ite.label }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <drill-hole-info :laminationObj="laminationData" @changeDrillHoleTableData="changeDrillHoleTableData" />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState, mapMutations } from "vuex";
import { ref, watch } from "vue";
import { pdctnos, stackuptol, stackuptolv2 } from "@/services/gongju/stackUp";
import DrillHoleInfo from "@/pages/gongju/impedance/components/DrillHoleInfo";
export default {
  components: { DrillHoleInfo },
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
  name: "StackImpedance",
  props: {
    boardTypeList: {
      type: Array,
      required: true,
    },
    ppTypeList: {
      type: Array,
      required: true,
    },
    pressingThicknessBg: {
      type: String,
    },
    collapse: {
      type: Boolean,
    },
    laminationData: {
      type: Array,
    },
    tableHeight: {
      type: Number,
    },
    flg1: {},
  },
  data() {
    return {
      gblist: [
        { value: 0, label: " " },
        { value: 1, label: "1" },
        { value: 2, label: "2" },
        { value: 3, label: "3" },
        { value: 4, label: "4" },
      ],
      stackUpTolList1: [],
      pdctno11: [],
      Storedata: [],
      switch1: false,
      flg11: 0,
    };
  },
  mounted() {
    pdctnos(0).then(res => {
      if (res.code) {
        this.pdctno11 = res.data;
      }
    });
    // console.log('this.$route.query',this.$route.query);
    let routeList = this.$route.query;
    if (JSON.stringify(routeList) !== "{}") {
      this.$store.commit("changeInfo", routeList);
      this.form.boardType = this.form.boardType.toString();
      this.pressGetAllData1();
    } else {
      // console.log('不包含',this.form);
      let _obj = {
        pdctno: "", // 编号
        layers: "", // 层数
        boardType: "", // 板材型号
        // tg: '',                        // Tg值
        ppType: "", // PP型号
        finishBoardThickness: 0, // 完成板厚
        highLimit: "", // 上限公差
        engineeringHiht: 0, // 工程上限
        lowLimit: "", // 下限公差
        engineeringLow: 0, // 公差下限
        pressingThickness: 0, // 压合板厚
        finishedThickness: 0, // 成品板厚
        pressingTimes: undefined, // 压合次数
        stackUpTol: "",
        isPressure: false, // 芯板对压
        isChangeLayerPres: false, // 层压不可更改
        gBStackUp: false, // 光板
        gbcount: 0, //光板下拉
        changeS: [], // 类型
      };
      this.$store.commit("changeInfo", _obj);
    }
    this.flg11 = this.flg1;
  },
  watch: {
    boardTypeList: {
      handler(val) {
        let routeList = this.$route.query;
        if (JSON.stringify(routeList) !== "{}" && val && routeList.type != "HP") {
          routeList.boardType && this.$emit("formChange", { name: "board", value: routeList.boardType });
        }
      },
      deep: true,
    },
    "form.boardType": {
      handler(val) {
        if (val) {
          this.flg11 = this.flg1;
          if (this.flg11 == 0) {
            this.boardChange(val);
          }
          this.flg11 = 0;
        }
      },
    },
    "form.finishBoardThickness": {
      handler(val) {
        if (val) {
          this.bulkEvent();
        }
      },
    },
    ppTypeList: {
      handler(val) {
        console.log("ppTypeList 监听", val);
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters({ form: "categoryForm" }),
    ...mapState("account", ["user"]),
  },
  methods: {
    ...mapMutations("setting", ["setedit"]),
    changeDrillHoleTableData(record) {
      this.$emit("changeDrillHoleTableData", record);
    },
    handleSearch(value) {
      this.Storedata = !value ? [] : this.Storedata;
    },
    onSelect(value) {
      //  console.log('222', value);
    },
    updatamessage() {
      this.Storedata = [];
      for (let index = 0; index < this.pdctno11.length; index++) {
        const currentItem = this.pdctno11[index].toLowerCase(); // 将当前下拉转换为小写形式
        const userInput = this.form.pdctno.toLowerCase(); // 将用户输入转换为小写形式
        if (currentItem.indexOf(userInput) !== -1 && userInput !== "") {
          this.Storedata.push(this.pdctno11[index]);
        }
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    boardChange(payload) {
      this.$emit("formChange", { name: "board", value: payload });
      // setTimeout(()=>{ this.$emit('formChange', {name:"board",value:payload})},500)
    },
    ppChange(payload) {
      // console.log('触发了2',payload)
      this.$emit("formChange", { name: "pp", value: payload });
    },
    //上下限公差获取压合公差
    LimitChange(limit) {
      let joinid = Number(this.$route.query.joinFactoryId || this.user.factoryId);
      let params = {
        Limit: Number(Math.abs(limit)),
        Pdtctno: this.form.pdctno,
        ThicknessOrg: Number(this.form.finishBoardThickness),
        highLimit: Number(this.form.highLimit),
        lowLimit: Number(this.form.lowLimit),
      };
      stackuptolv2(joinid, params).then(res => {
        if (res.code && res.data.stackUpTol) {
          this.form.stackUpTol = res.data.stackUpTol;
          this.form.engineeringHiht = res.data.engineeringHiht;
          this.form.engineeringLow = res.data.engineeringLow;
        }
      });
    },
    // 板厚计算公差
    bulkEvent() {
      let params = {
        Pdtctno: this.form.pdctno,
        ThicknessOrg: Number(this.form.finishBoardThickness),
      };
      let joinid = Number(this.$route.query.joinFactoryId || this.user.factoryId);
      if (this.form.finishBoardThickness < 1) {
        this.form.highLimit = "0.1";
        this.form.lowLimit = "-0.1";
      } else {
        this.form.highLimit = String(this.getFloat(this.form.finishBoardThickness * 0.1, 3)); //上限公差
        this.form.lowLimit = String(-this.getFloat(this.form.finishBoardThickness * 0.1, 3)); //下限公差
      }
      //压合公差
      this.form.stackUpTol =
        this.form.highLimit > 0.1
          ? "+" + (this.form.highLimit - 0.025).toFixed(3) + "/-" + (this.form.highLimit - 0.025).toFixed(3)
          : "+" + (0.1 - 0.025).toFixed(3) + "/-" + (0.1 - 0.025).toFixed(3);
      if (joinid == 22) {
        if (this.form.finishBoardThickness < 1.2) {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.07, 3)); //工程上限
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.13, 3)); //工程下限
        } else if (this.form.finishBoardThickness < 1.5 && this.form.finishBoardThickness >= 1.2) {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.06, 3)); //工程上限
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.14, 3)); //工程下限
        } else {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.05, 3)); //工程上限
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.15, 3)); //工程下限
        }
      } else if (((joinid == 58 || joinid == 59) && this.form.finishBoardThickness <= 3.5) || joinid == 12 || joinid == 67) {
        stackuptol(joinid, params).then(res => {
          if (res.code) {
            this.form.engineeringHiht = res.data.engineeringHiht.toFixed(3);
            this.form.engineeringLow = res.data.engineeringLow.toFixed(3);
            this.form.highLimit = res.data.highLimit;
            this.form.lowLimit = res.data.lowLimit;
            this.form.stackUpTol = res.data.stackUpTol;
          }
        });
      } else if (joinid == 38) {
        this.form.stackUpTol = "+0.08/-0.08";
        if (this.form.finishBoardThickness > 0.8) {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.05, 3)); //工程上限
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.15, 3)); //工程下限
        } else {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.09, 3));
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.14, 3));
        }
      } else if (joinid == 70) {
        this.form.stackUpTol = "+0.05/-0.05";
      } else {
        if (this.form.finishBoardThickness > 0.8) {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.05, 3)); //工程上限
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.15, 3)); //工程下限
        } else {
          this.form.engineeringHiht = String(this.getFloat(this.form.finishBoardThickness - 0.09, 3));
          this.form.engineeringLow = String(this.getFloat(this.form.finishBoardThickness - 0.14, 3));
        }
      }
    },
    collapseC() {
      this.$emit("collapseChange");
    },
    getoldData() {
      this.$emit("getoldData");
      // setTimeout(()=>{ this.$emit('getAllData')},1000)
    },
    pressGetAllData() {
      this.$emit("getAllData");
      // setTimeout(()=>{ this.$emit('getAllData')},1000)
    },
    pressGetAllData1() {
      this.$emit("getAllData1");
      // setTimeout(()=>{ this.$emit('getAllData')},1000)
    },
    layerChange() {
      this.$emit("getLayerChange");
    },
  },
};
</script>

<style scoped lang="less">
.ant-input {
  padding: 4px 5px;
}
/deep/.ant-select-selection__rendered {
  position: relative;
  display: block;
  margin-right: 11px;
  margin-left: 4px;
  line-height: 30px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
.gbstackup {
  /deep/.ant-form-item-control {
    width: 43px;
  }
  /deep/.ant-select-selection--single .ant-select-selection__rendered {
    margin-right: 10px;
  }
  /deep/.ant-select-arrow {
    right: 5px;
  }
}
.pctno {
  /deep/.ant-select-arrow-icon > svg {
    display: none;
  }
}

/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  font-size: 14px;
}

/deep/.ant-table-bordered .ant-table-tbody > tr > td {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
  padding: 5px 2px;
  font-size: 13px;
}
.collapseActive {
  left: -18%;
  // background: #E0ECFF;
  .basicInfoContent {
    opacity: 0;
  }
}
.collapseNoActive {
  left: 0;
  .basicInfoContent {
    opacity: 1;
  }
}
.basicInfo {
  height: 100%;
  transition: left 0.6s linear;
  width: 20%;
  border: 1px solid #d6d6d6;
  position: absolute;
  top: 0;
  /deep/.ant-form .ant-form-item {
    margin: 5px 0 0 0 !important;
  }
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #a8a8a8;
    // #fff9e6
  }
  .basicInfoContent {
    transition: opacity 0.5s linear;
    // margin: 5px;
    // border: 1px solid #95B8E7;
    // max-height: 420px;
    border: 1px solid #f3f3f3;
    //  max-height: 420px;
    //  min-height: 435px;

    overflow-y: auto;
    &::-webkit-scrollbar {
      //整体样式
      width: 6px; //y轴滚动条粗细
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      //滑动滑块条样式
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      background: #ff9900;
      // #fff9e6
    }
  }
  .basicInfoTitle {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    // background: linear-gradient(rgb(239, 245, 255) 0px, rgb(224, 236, 255) 100%) repeat-x;
    background-repeat: repeat-x;
    .head-menu {
      height: auto;
      line-height: 30px;
      color: rgb(14, 45, 95);
      // font-weight: 500;
      i {
        cursor: pointer;
        color: #1280d2;
        &:hover {
          color: #1281d3;
        }
      }
    }
  }
  /deep/ .ant-form {
    background-color: #f6f6f6;
    .ant-form-item {
      margin: 0;
      .ant-form-item-control {
        line-height: 32px;
        height: 32px;
        .ant-input {
          height: 32px;
          color: #ff9900;
          //  font-weight: 600;
        }
        .redBg {
          background: #dc143c;
          color: #fff;
        }
        .greenBg {
          background: green;
          color: #fff;
        }
        .ant-select-selection--single {
          height: 32px;
          line-height: 32px;
          .ant-select-selection__rendered {
            height: 32px;
            line-height: 32px;
            //color: #ff9900;
            //  font-weight: 600;
          }
        }
      }
      .ant-form-item-label {
        line-height: 32px;
        height: 32px;
        //  label {
        //    color: #ffd285;
        //    font-weight: 600;
        //  }
        //label::after {
        //  content:''
        //}
      }
    }
  }
}
</style>
