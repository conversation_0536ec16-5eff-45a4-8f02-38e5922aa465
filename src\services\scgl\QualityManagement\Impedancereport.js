import { request, METHOD } from "@/utils/request";

//获取数据
export async function impreportlist(OrderNo) {
  return request(`api/app/t-pub-inspection-report-imp/imp-report-list?OrderNo=${OrderNo}`, METHOD.GET);
}
//保存数据
export async function setimpreportlist(params) {
  return request(`/api/app/t-pub-inspection-report-imp/set-imp-report-list`, METHOD.POST, params);
}
//导出报告
export async function reliabilityimpreport(OrderNo) {
  return request(`api/app/t-pub-inspection-report-imp/reliability-imp-report?orderno=${OrderNo}`, METHOD.GET);
}
