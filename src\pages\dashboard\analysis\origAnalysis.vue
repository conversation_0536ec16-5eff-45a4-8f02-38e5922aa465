<template>
  <div class="analysis" style="overflow: auto" id="dataContainer" @scroll="thbouncedHandleScroll">
    <div style="height: 100%">
      <div style="height: 75%; display: flex">
        <div class="leftdata">
          <div>
            <a-form-model-item label="工厂筛选" :label-col="{ span: 3 }" :wrapper-col="{ span: 19 }">
              <div>
                <a-select showSearch allowClear optionFilterProp="lable" placeholder="授权工厂" v-model="FactoryId" @change="Factorychange">
                  <a-select-option
                    style="color: blue"
                    v-for="(item, index) in mapKey(factroyList)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </div>
          <div class="bac">
            <a-spin :spinning="dayLoading" style="height: 100%">
              <div style="display: flex">
                <a-button type="primary" shape="round" :class="classification1 ? 'dayweek' : 'weekmonth'" @click="dayclick()"> 当日 </a-button>
                <a-button type="primary" shape="round" :class="classification2 ? 'dayweek' : 'weekmonth'" @click="weekclick()"> 周度 </a-button>
                <a-button type="primary" shape="round" :class="classification3 ? 'dayweek' : 'weekmonth'" @click="monthclick()"> 月度 </a-button>
                <a-range-picker
                  style="width: 260px; margin: 15px 0 15px 50px; border-radius: 5px"
                  @change="rangechange"
                  v-model="dateString"
                  allowClear
                />
              </div>
              <div>
                <div class="Orderingsituation" style="margin-top: 40px" v-if="!dayLoading">
                  <div style="display: flex">
                    <div class="data1">总下单品种(款)</div>
                    <div>
                      <div class="data2">{{ Orderingdata.allofflinenum }}款</div>
                    </div>
                  </div>
                  <div style="display: flex">
                    <div class="data1">总下单面积(㎡)</div>
                    <div class="data2">{{ Orderingdata.allofflinearea }}㎡</div>
                  </div>
                  <div style="display: flex">
                    <div class="data1">结存品种(款)</div>
                    <div>
                      <div class="data2">{{ Orderingdata.allbalancenum }}款</div>
                    </div>
                  </div>
                  <div style="display: flex">
                    <div class="data1">结存面积(㎡)</div>
                    <div class="data2">{{ Orderingdata.allbalancearea }}㎡</div>
                  </div>
                </div>
                <div style="height: 0; display: flex">
                  <div id="Layercount" class="left1"></div>
                  <div id="Makeorder" class="left1"></div>
                </div>
              </div>
            </a-spin>
          </div>
        </div>
        <div class="rightdata">
          <div class="top2">
            <a-spin :spinning="rateLoading">
              <div id="Totalorderplacement" class="chievementrate"></div>
              <table
                border="1"
                style="width: 13%; color: rgba(121, 128, 137, 0); font-size: 8px; text-align: center; margin-top: -37px; margin-left: 7px"
              >
                <thead style="height: 21px">
                  总下单金额（万元）
                </thead>
                <tbody>
                  <tr>
                    <td style="height: 21px">总下单金额（万元）</td>
                  </tr>
                </tbody>
              </table>
              <table
                border="1"
                style="width: 82%; color: rgb(121, 128, 137); margin-top: -64px; margin-left: 135px; font-size: 8px; text-align: center"
              >
                <thead>
                  <tr>
                    <td v-for="(item, index) in ratemonth" :key="index" style="width: 48px; height: 21px">{{ item }}月</td>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in TableData" :key="index">
                    <td style="height: 21px">{{ item.month1 }}</td>
                    <td style="height: 21px">{{ item.month2 }}</td>
                    <td style="height: 21px">{{ item.month3 }}</td>
                    <td style="height: 21px">{{ item.month4 }}</td>
                    <td style="height: 21px">{{ item.month5 }}</td>
                    <td style="height: 21px">{{ item.month6 }}</td>
                    <td style="height: 21px">{{ item.month7 }}</td>
                    <td style="height: 21px">{{ item.month8 }}</td>
                    <td style="height: 21px">{{ item.month9 }}</td>
                    <td style="height: 21px">{{ item.month10 }}</td>
                    <td style="height: 21px">{{ item.month11 }}</td>
                    <td style="height: 21px">{{ item.month12 }}</td>
                  </tr>
                </tbody>
              </table>
            </a-spin>
          </div>
          <div class="bot2">
            <a-spin :spinning="rateLoading1">
              <div id="CAMPersonalErgonomics" class="chievementrate"></div>
            </a-spin>
          </div>
        </div>
      </div>
      <div style="height: 25%">
        <a-spin :spinning="eqloading">
          <div id="Customerinquiryrate" class="bot"></div>
        </a-spin>
      </div>
    </div>
    <div style="width: 100%; height: 550px; background-color: white" class="analysishp">
      <div style="border: 1px solid green; width: 98.9%; margin-left: 8px; height: 540px">
        <a-select v-model="Dailyandmonthly" style="width: 200px; height: 50px" @change="dychange">
          <a-select-option value="1"> 日 </a-select-option>
          <a-select-option value="2"> 月 </a-select-option>
        </a-select>
        <a-select v-model="YearDate2" style="width: 200px; height: 50px; margin-left: 15px" @change="onChange3" showSearch optionFilterProp="label">
          <a-select-option v-for="item in years" :key="item.label" :value="item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <div v-if="Dailyandmonthly == '1'">
          <div style="padding: 15px">
            <a-table
              :columns="columns4"
              :loading="comloading"
              :dataSource="comtabledata"
              :pagination="false"
              :rowKey="(record, index) => `${index + 1}`"
              :scroll="{ x: 600 }"
              bordered
            >
            </a-table>
          </div>
        </div>
        <div v-if="Dailyandmonthly == '2'">
          <div style="padding: 15px">
            <a-table
              :columns="columns5"
              :dataSource="comtabledata1"
              :loading="comloading"
              :pagination="false"
              :rowKey="(record, index) => `${index + 1}`"
              :scroll="{ x: 600 }"
              bordered
            >
            </a-table>
          </div>
        </div>
        <a-spin :spinning="comloading">
          <div style="padding: 15px; height: 262px">
            <div id="Combinationrate" style="height: 243px; width: 100%"></div>
          </div>
        </a-spin>
      </div>
    </div>
    <div style="width: 100%; height: 1595px; background-color: white" class="analysishp">
      <div style="border: 1px solid green; width: 98.9%; margin-left: 8px; height: 1580px">
        <a-select v-model="orderdayormoth" @change="ordchange">
          <a-select-option value="1"> 日 </a-select-option>
          <a-select-option value="2"> 月 </a-select-option>
        </a-select>
        <a-select v-model="YearDate" style="margin-left: 15px" @change="onChange2" showSearch optionFilterProp="label">
          <a-select-option v-for="item in years" :key="item.label" :value="item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <span style="font-size: 30px; margin-left: 13%; font-weight: bold; color: #ff9900">市场下单分析表</span>
        <div v-if="orderdayormoth == '1'">
          <div style="padding: 15px">
            <a-table
              :loading="comloading1"
              :columns="columns6"
              :customRow="onClickRow"
              :rowClassName="isRedRow"
              :dataSource="ordertabledata"
              :pagination="false"
              :rowKey="(record, index) => `${index + 1}`"
              :scroll="{ x: 600 }"
              bordered
            >
            </a-table>
          </div>
        </div>
        <div v-if="orderdayormoth == '2'">
          <div style="padding: 15px">
            <a-table
              :loading="comloading1"
              :customRow="onClickRow1"
              :columns="columns7"
              :rowClassName="isRedRow"
              :dataSource="ordertabledata1"
              :pagination="false"
              :rowKey="(record, index) => `${index + 1}`"
              :scroll="{ x: 600 }"
              bordered
            >
            </a-table>
          </div>
        </div>
        <a-spin :spinning="comloading1">
          <div style="padding: 15px; height: 262px">
            <div id="Orderdata" style="height: 243px; width: 100%"></div>
          </div>
        </a-spin>
      </div>
    </div>
    <div style="width: 100%; height: 1245px; background-color: white" class="analysishp">
      <div style="border: 1px solid green; width: 98.9%; margin-left: 8px; height: 1235px">
        <a-select v-model="prodayormoth" @change="prochange">
          <a-select-option value="1"> 日 </a-select-option>
          <a-select-option value="2"> 月 </a-select-option>
        </a-select>
        <a-select v-model="YearDate1" style="margin-left: 15px" @change="prochange" showSearch optionFilterProp="label">
          <a-select-option v-for="item in years" :key="item.label" :value="item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <span style="font-size: 30px; margin-left: 13%; font-weight: bold; color: #ff9900">工程下线订单分析表</span>
        <div v-if="prodayormoth == '1'">
          <div style="padding: 15px">
            <a-table
              :loading="comloading2"
              :columns="columns8"
              :dataSource="Protabledata"
              :pagination="false"
              :rowKey="(record, index) => `${index + 1}`"
              :scroll="{ x: 600 }"
              bordered
            >
            </a-table>
          </div>
        </div>
        <div v-if="prodayormoth == '2'">
          <div style="padding: 15px">
            <a-table
              :loading="comloading2"
              :columns="columns9"
              :dataSource="Protabledata1"
              :pagination="false"
              :rowKey="(record, index) => `${index + 1}`"
              :scroll="{ x: 600 }"
              bordered
            >
            </a-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts/core";
import { TooltipComponent, LegendComponent } from "echarts/components";
import { PieChart } from "echarts/charts";
import { LabelLayout } from "echarts/features";
import { SVGRenderer } from "echarts/renderers";
import moment from "moment";
echarts.use([TooltipComponent, LegendComponent, PieChart, SVGRenderer, LabelLayout]);
import {
  factroyList,
  ppEOrderdatastatisticsv2,
  ppEOrderdatastatisticsv1,
  ppEOrderdatastatistics,
  ppEOrderdatastatisticsv3,
  ppEOrderyeardatastatistics,
  ppEOrdereQDatastatistics,
  camdatastatistics,
  datastatisticsv1,
  datastatisticsv2,
  mkt2Ppedatav1,
  mkt2Ppedatav2,
  ppeofflinedatav1,
  ppeofflinedatav2,
} from "@/services/analysis";

export default {
  props: [],
  data() {
    return {
      factroyList: [],
      selectedRowsData: {},
      eqloading: false,
      rateLoading: false,
      rateLoading1: false,
      FactoryId: undefined,
      dayLoading: false,
      classification1: true,
      classification2: false,
      classification3: false,
      classification4: false,
      dateString: [],
      Orderingdata: {},
      date: moment().format("YYYY-MM-DD"),
      Orderingarea: [],
      Ordervariety: [],
      Dailyandmonthly: "1",
      orderdayormoth: "1",
      prodayormoth: "1",
      comloading: false,
      comloading1: false,
      comloading2: false,
      averagebar: [],
      averageline: [],
      ratemonth: [],
      TableData: [],
      YearDate: moment().format("YYYY"),
      YearDate2: moment().format("YYYY"),
      YearDate1: moment().format("YYYY"),
      years: [], // 用于存储年份的数组
      comtabledata: [],
      comtabledata1: [],
      ordertabledata: [],
      ordertabledata1: [],
      Protabledata: [],
      Protabledata1: [],
      orderdata: [],
      orderlegend: "",
      CAMdata: [],
      EQdata: [],
      MultipleEQs: [],
      Combiningdata: [],
      Combiningdata1: [],
      columns4: [
        {
          title: "日期",
          dataIndex: "name",
          align: "center",
          width: 150,
          fixed: "left",
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 60,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 60,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 60,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 60,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 60,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 60,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 60,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 60,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 60,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 60,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 60,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 60,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 60,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 60,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 60,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 60,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 60,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 60,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 60,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 60,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 60,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 60,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 60,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 60,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 60,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 60,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 60,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 60,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 60,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 60,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 60,
        },
      ],
      columns5: [
        {
          title: "日期",
          dataIndex: "name",
          align: "center",
          width: 150,
        },
        {
          title: "1",
          dataIndex: "month1",
          align: "center",
          width: 60,
        },
        {
          title: "2",
          dataIndex: "month2",
          align: "center",
          width: 60,
        },
        {
          title: "3",
          dataIndex: "month3",
          align: "center",
          width: 60,
        },
        {
          title: "4",
          dataIndex: "month4",
          align: "center",
          width: 60,
        },
        {
          title: "5",
          dataIndex: "month5",
          align: "center",
          width: 60,
        },
        {
          title: "6",
          dataIndex: "month6",
          align: "center",
          width: 60,
        },
        {
          title: "7",
          dataIndex: "month7",
          align: "center",
          width: 60,
        },
        {
          title: "8",
          dataIndex: "month8",
          align: "center",
          width: 60,
        },
        {
          title: "9",
          dataIndex: "month9",
          align: "center",
          width: 60,
        },
        {
          title: "10",
          dataIndex: "month10",
          align: "center",
          width: 60,
        },
        {
          title: "11",
          dataIndex: "month11",
          align: "center",
          width: 60,
        },
        {
          title: "12",
          dataIndex: "month12",
          align: "center",
          width: 60,
        },
      ],
      columns6: [
        {
          title: "类别",
          dataIndex: "name",
          align: "center",
          width: 200,
          fixed: "left",
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 60,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 60,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 60,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 60,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 60,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 60,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 60,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 60,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 60,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 60,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 60,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 60,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 60,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 60,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 60,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 60,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 60,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 60,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 60,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 60,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 60,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 60,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 60,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 60,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 60,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 60,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 60,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 60,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 60,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 60,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 60,
        },
      ],
      columns7: [
        {
          title: "类别",
          dataIndex: "name",
          align: "center",
          width: 150,
        },
        {
          title: "1",
          dataIndex: "month1",
          align: "center",
          width: 60,
        },
        {
          title: "2",
          dataIndex: "month2",
          align: "center",
          width: 60,
        },
        {
          title: "3",
          dataIndex: "month3",
          align: "center",
          width: 60,
        },
        {
          title: "4",
          dataIndex: "month4",
          align: "center",
          width: 60,
        },
        {
          title: "5",
          dataIndex: "month5",
          align: "center",
          width: 60,
        },
        {
          title: "6",
          dataIndex: "month6",
          align: "center",
          width: 60,
        },
        {
          title: "7",
          dataIndex: "month7",
          align: "center",
          width: 60,
        },
        {
          title: "8",
          dataIndex: "month8",
          align: "center",
          width: 60,
        },
        {
          title: "9",
          dataIndex: "month9",
          align: "center",
          width: 60,
        },
        {
          title: "10",
          dataIndex: "month10",
          align: "center",
          width: 60,
        },
        {
          title: "11",
          dataIndex: "month11",
          align: "center",
          width: 60,
        },
        {
          title: "12",
          dataIndex: "month12",
          align: "center",
          width: 60,
        },
      ],
      columns8: [
        {
          title: "类别",
          dataIndex: "name",
          align: "center",
          width: 200,
          fixed: "left",
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 60,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 60,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 60,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 60,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 60,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 60,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 60,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 60,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 60,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 60,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 60,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 60,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 60,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 60,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 60,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 60,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 60,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 60,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 60,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 60,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 60,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 60,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 60,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 60,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 60,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 60,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 60,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 60,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 60,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 60,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 60,
        },
      ],
      columns9: [
        {
          title: "类别",
          dataIndex: "name",
          align: "center",
          width: 150,
        },
        {
          title: "1",
          dataIndex: "month1",
          align: "center",
          width: 60,
        },
        {
          title: "2",
          dataIndex: "month2",
          align: "center",
          width: 60,
        },
        {
          title: "3",
          dataIndex: "month3",
          align: "center",
          width: 60,
        },
        {
          title: "4",
          dataIndex: "month4",
          align: "center",
          width: 60,
        },
        {
          title: "5",
          dataIndex: "month5",
          align: "center",
          width: 60,
        },
        {
          title: "6",
          dataIndex: "month6",
          align: "center",
          width: 60,
        },
        {
          title: "7",
          dataIndex: "month7",
          align: "center",
          width: 60,
        },
        {
          title: "8",
          dataIndex: "month8",
          align: "center",
          width: 60,
        },
        {
          title: "9",
          dataIndex: "month9",
          align: "center",
          width: 60,
        },
        {
          title: "10",
          dataIndex: "month10",
          align: "center",
          width: 60,
        },
        {
          title: "11",
          dataIndex: "month11",
          align: "center",
          width: 60,
        },
        {
          title: "12",
          dataIndex: "month12",
          align: "center",
          width: 60,
        },
      ],
    };
  },
  created() {
    this.thbouncedHandleScroll = this.throttle(this.handleScroll, 300);
    this.dayclick();
    this.Annualorderplacement();
    this.Camefficiency();
    this.EQrate();
    const currentYear = new Date().getFullYear(); // 获取当前年份
    for (let year = currentYear; year >= 2024; year--) {
      this.years.push({ value: year.toString(), label: year.toString() }); // 从当前年份递减到2024年，并转换为字符串
    }
  },
  async mounted() {
    factroyList().then(res => {
      if (res.code) {
        this.factroyList = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
  },
  beforeDestroy() {
    const container = document.getElementById("dataContainer");
    if (container) {
      container.removeEventListener("scroll", this.thbouncedHandleScroll);
    }
  },
  methods: {
    // 处理滚动事件 使用懒加载
    handleScroll(event) {
      const container = event.target;
      const { scrollTop, scrollHeight, clientHeight } = container;
      const threshold = 3500; // 距离底部3500像素时并且下单分析数据未加载才进行数据加载
      if (
        scrollTop + clientHeight >= scrollHeight - threshold &&
        this.comtabledata.length == 0 &&
        this.ordertabledata.length == 0 &&
        this.Protabledata.length == 0
      ) {
        this.Combiningrate();
        this.getorderdata();
        this.getprodata();
      }
    },
    onChange3() {
      this.dychange();
    },
    dychange() {
      this.comtabledata = [];
      this.comtabledata1 = [];
      if (this.Dailyandmonthly == "1") {
        this.Combiningrate();
      } else {
        this.Combiningrate1();
      }
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.selectedRowsData = record;
            let data1 = [];
            if (record.name.includes("下单面积") || record.name.includes("下单款数")) {
              Object.keys(record).forEach(key => {
                if (!record[key].includes("下单面积") && !record[key].includes("下单款数")) {
                  data1.push(record[key]);
                }
              });
              data1.pop();
              let xdata = Array.from({ length: 31 }, (_, i) => i + 1 + "日");
              this.Orderdata(data1, record.name, xdata);
            } else {
              this.Orderdata([], "", []);
            }
          },
        },
      };
    },
    onClickRow1(record) {
      return {
        on: {
          click: () => {
            this.selectedRowsData = record;
            let data1 = [];
            if (record.name.includes("下单面积") || record.name.includes("下单款数")) {
              Object.keys(record).forEach(key => {
                if (!record[key].includes("下单面积") && !record[key].includes("下单款数")) {
                  data1.push(record[key]);
                }
              });
              data1.pop();
              let xdata = Array.from({ length: 12 }, (_, i) => i + 1 + "月");
              this.Orderdata(data1, record.name, xdata);
            } else {
              this.Orderdata([], "", []);
            }
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.name && record.name == this.selectedRowsData.name && (record.name.includes("下单面积") || record.name.includes("下单款数"))) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onChange2() {
      this.ordchange();
    },
    ordchange() {
      this.ordertabledata = [];
      this.ordertabledata1 = [];
      this.selectedRowsData = {};
      this.Orderdata([], "", []);
      if (this.orderdayormoth == "1") {
        this.getorderdata();
      } else {
        this.getorderdata1();
      }
    },
    prochange() {
      this.Protabledata = [];
      this.Protabledata1 = [];
      if (this.prodayormoth == "1") {
        this.getprodata();
      } else {
        this.getprodata1();
      }
    },
    Factorychange() {
      if (this.classification1) {
        this.dayclick();
      } else if (this.classification2) {
        this.weekclick();
      } else if (this.classification3) {
        this.monthclick();
      } else if (this.classification4) {
        this.Intervalselection();
      }
      this.Annualorderplacement();
      this.Camefficiency();
      this.EQrate();
      if (this.Dailyandmonthly == "1") {
        this.Combiningrate();
      } else {
        this.Combiningrate1();
      }
      if (this.orderdayormoth == "1") {
        this.getorderdata();
      } else {
        this.getorderdata1();
      }
      if (this.prodayormoth == "1") {
        this.getprodata();
      } else {
        this.getprodata1();
      }
    },
    dayclick() {
      this.dayLoading = true;
      this.classification1 = true;
      this.classification2 = false;
      this.classification3 = false;
      this.dateString = [];
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      ppEOrderdatastatisticsv2(this.date, faid)
        .then(res => {
          if (res.code) {
            this.Orderingdata = res.data;
            this.Layercount();
            this.Makeorder();
          }
        })
        .finally(() => {
          this.dayLoading = false;
        });
    },
    weekclick() {
      this.dayLoading = true;
      this.classification2 = true;
      this.classification1 = false;
      this.classification3 = false;
      this.dateString = [];
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      ppEOrderdatastatisticsv1(this.date, faid)
        .then(res => {
          if (res.code) {
            this.Orderingdata = res.data;
            this.Layercount();
            this.Makeorder();
          }
        })
        .finally(() => {
          this.dayLoading = false;
        });
    },
    monthclick() {
      this.dayLoading = true;
      this.classification3 = true;
      this.classification1 = false;
      this.classification2 = false;
      this.dateString = [];
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      ppEOrderdatastatistics(this.date, faid)
        .then(res => {
          if (res.code) {
            this.Orderingdata = res.data;
            this.Layercount();
            this.Makeorder();
          }
        })
        .finally(() => {
          this.dayLoading = false;
        });
    },
    Intervalselection() {
      this.dayLoading = true;
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      var Startdate = localStorage.getItem("dateString").split(",")[0];
      var Enddate = localStorage.getItem("dateString").split(",")[1];
      ppEOrderdatastatisticsv3(Startdate, Enddate, faid)
        .then(res => {
          if (res.code) {
            this.Orderingdata = res.data;
            this.Layercount();
            this.Makeorder();
          }
        })
        .finally(() => {
          this.dayLoading = false;
        });
    },
    Annualorderplacement() {
      this.ratemonth = [];
      this.Orderingarea = [];
      this.Ordervariety = [];
      this.rateLoading = true;
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      ppEOrderyeardatastatistics(faid)
        .then(res => {
          if (res.code) {
            for (let index = 0; index < res.data.offlineAreadtos.length; index++) {
              res.data.offlineAreadtos[index].dtos.forEach(ite => {
                this.Orderingarea.push(ite.value);
                this.ratemonth.push(ite.month);
              });
            }
            for (let index = 0; index < res.data.offlineNumdtos.length; index++) {
              res.data.offlineNumdtos[index].dtos.forEach(ite => {
                this.Ordervariety.push(ite.value);
              });
              this.ratemonth.pop();
              this.Ordervariety.pop();
              this.Orderingarea.pop();
              this.Totalorderplacement();
              this.TableData = [
                this.generateDayData(res.data.offlineAreadtos[0], "month", "dtos"),
                this.generateDayData(res.data.offlineNumdtos[0], "month", "dtos"),
              ];
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.rateLoading = false;
        });
    },
    Camefficiency() {
      this.CAMdata = [];
      this.rateLoading1 = true;
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      camdatastatistics(faid)
        .then(res => {
          if (res.code) {
            for (let index = 0; index < res.data.offlineAverageNumdtos.length; index++) {
              res.data.offlineAverageNumdtos[index].dtos.forEach(ite => {
                this.CAMdata.push(ite.value);
              });
            }
          }
          this.CAMdata.pop();
          this.CAMPersonalErgonomics();
        })
        .finally(() => {
          this.rateLoading1 = false;
        });
    },
    EQrate() {
      this.EQdata = [];
      this.MultipleEQs = [];
      this.eqloading = true;
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      ppEOrdereQDatastatistics(faid)
        .then(res => {
          if (res.code) {
            for (let index = 0; index < res.data.offlineEQNumdtos.length; index++) {
              res.data.offlineEQNumdtos[index].dtos.forEach(ite => {
                this.EQdata.push(ite.value);
              });
            }
            for (let index = 0; index < res.data.offlineEQ2Numdtos.length; index++) {
              res.data.offlineEQ2Numdtos[index].dtos.forEach(ite => {
                this.MultipleEQs.push(ite.value);
              });
            }
          }
          this.EQdata.pop();
          this.MultipleEQs.pop();
          this.Customerinquiryrate();
        })
        .finally(() => {
          this.eqloading = false;
        });
    },
    getorderdata() {
      this.ordertabledata = [];
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.comloading1 = true;
      let date;
      if (this.YearDate && this.YearDate !== moment().format("YYYY")) {
        date = moment(`${this.YearDate}-${moment().format("MM")}-01`)
          .endOf("month")
          .format("YYYY-MM-DD");
      } else {
        // 否则设置为当前日期
        date = moment().format("YYYY-MM-DD");
      }
      mkt2Ppedatav1(date, faid)
        .then(res => {
          if (res.code) {
            this.ordertabledata = [
              this.generateDayData(res.data.offlineAreadtos[0], "day", "dDtos"), //下单面积
              this.generateDayData(res.data.offlineNumdtos[0], "day", "dDtos"), //下单款数
              this.generateDayData(res.data.offlinePCTANdtos[0], "day", "dDtos"), //面积品种比
              this.generateDayData(res.data.offlineNewNumdtos[0], "day", "dDtos"), //新单数(面积 品种)
              this.generateDayData(res.data.offlineNewPCTANdtos[0], "day", "dDtos"), //新单面积品种比
              this.generateDayData(res.data.offlineNewAreadtos[0], "day", "dDtos"), //新单面积
              this.generateDayData(res.data.offlineNewPCTAreadtos[0], "day", "dDtos"), //新单面积比
              this.generateDayData(res.data.offlineReNumdtos[0], "day", "dDtos"), //返单数
              this.generateDayData(res.data.offlineRePCTANdtos[0], "day", "dDtos"), //返单品种比
              this.generateDayData(res.data.offlineReAreadtos[0], "day", "dDtos"), //返单面积
              this.generateDayData(res.data.offlineRePCTAreadtos[0], "day", "dDtos"), //返单面积比
              this.generateDayData(res.data.offline2LayersNumdtos[0], "day", "dDtos"), //双面板数量
              this.generateDayData(res.data.offline2LPCTANdtos[0], "day", "dDtos"), //双面板面积品种比
              this.generateDayData(res.data.offline2LayersAreadtos[0], "day", "dDtos"), //双面板面积
              this.generateDayData(res.data.offline2LPCTAreadtos[0], "day", "dDtos"), //双面板面积比
              this.generateDayData(res.data.offline4LayersNumdtos[0], "day", "dDtos"), //4层板数量
              this.generateDayData(res.data.offline4LPCTANdtos[0], "day", "dDtos"), //4层板面积品种比
              this.generateDayData(res.data.offline4LayersAreadtos[0], "day", "dDtos"), //4层板面积
              this.generateDayData(res.data.offline4LPCTAreadtos[0], "day", "dDtos"), //4层板面积比
              this.generateDayData(res.data.offline6LayersNumdtos[0], "day", "dDtos"), //4-8
              this.generateDayData(res.data.offline6LPCTANdtos[0], "day", "dDtos"), //4-8品种比
              this.generateDayData(res.data.offline6LayersAreadtos[0], "day", "dDtos"), //4-8层板面积
              this.generateDayData(res.data.offline6LPCTAreadtos[0], "day", "dDtos"), //4-8层板面积比
              this.generateDayData(res.data.offline10LayersNumdtos[0], "day", "dDtos"), //>8
              this.generateDayData(res.data.offline10LPCTANdtos[0], "day", "dDtos"), //>8面积品种比
              this.generateDayData(res.data.offline10LayersAreadtos[0], "day", "dDtos"), //>8层板面积
              this.generateDayData(res.data.offline10LPCTAreadtos[0], "day", "dDtos"), //>8层板面积比
              this.generateDayData(res.data.offlineHDINumdtos[0], "day", "dDtos"), //HDI数量
              this.generateDayData(res.data.offlineHDIPCTANdtos[0], "day", "dDtos"), //HDI品种比
              this.generateDayData(res.data.offlineHDIAreadtos[0], "day", "dDtos"), //HDI面积
              this.generateDayData(res.data.offlineHDIPCTAreadtos[0], "day", "dDtos"), //HDI面积比
              this.generateDayData(res.data.offlineHVANumdtos[0], "day", "dDtos"), //HVA
              this.generateDayData(res.data.offlineHVAPCTANdtos[0], "day", "dDtos"), //HVA面积品种比
              this.generateDayData(res.data.offlineHVAAreadtos[0], "day", "dDtos"), //HVA面积
              this.generateDayData(res.data.offlineHVAPCTAreadtos[0], "day", "dDtos"), //HVA面积比
              this.generateDayData(res.data.offlineJiajiNumdtos[0], "day", "dDtos"), //加急板数量（面积、品种）
              this.generateDayData(res.data.offlineJiajiPCTANdtos[0], "day", "dDtos"), //加急板面积品种比
              this.generateDayData(res.data.offlineJiajiAreadtos[0], "day", "dDtos"), //加急板面积
              this.generateDayData(res.data.offlineJiajiPCTAreadtos[0], "day", "dDtos"), //加急板面积比
              this.generateDayData(res.data.offlineAverageLayerdtos[0], "day", "dDtos"), //平均订单层数
            ];
            console.log(this.ordertabledata, "转化数据", res.data, "原始数据");
            this.selectedRowsData = this.ordertabledata[0];
            let data1 = [];
            res.data.offlineAreadtos[0].dDtos.forEach(ite => {
              data1.push(ite.value);
            });
            data1.pop();
            let xdata = Array.from({ length: 31 }, (_, i) => i + 1 + "日");
            this.Orderdata(data1, "下单面积", xdata);
          }
        })
        .finally(() => {
          this.comloading1 = false;
        });
    },
    getorderdata1() {
      this.ordertabledata = [];
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.comloading1 = true;
      let date;
      if (this.YearDate && this.YearDate !== moment().format("YYYY")) {
        date = `${this.YearDate}-12-31`;
      } else {
        date = moment().format("YYYY-MM-DD");
      }
      mkt2Ppedatav2(date, faid)
        .then(res => {
          if (res.code) {
            this.ordertabledata1 = [
              this.generateDayData(res.data.offlineAreadtos[0], "month", "dtos"), //下单面积
              this.generateDayData(res.data.offlineNumdtos[0], "month", "dtos"), //下单款数
              this.generateDayData(res.data.offlinePCTANdtos[0], "month", "dtos"), //面积品种比
              this.generateDayData(res.data.offlineNewNumdtos[0], "month", "dtos"), //新单数(面积 品种)
              this.generateDayData(res.data.offlineNewPCTANdtos[0], "month", "dtos"), //新单面积品种比
              this.generateDayData(res.data.offlineNewAreadtos[0], "month", "dtos"), //新单面积
              this.generateDayData(res.data.offlineNewPCTAreadtos[0], "month", "dtos"), //新单面积比
              this.generateDayData(res.data.offlineReNumdtos[0], "month", "dtos"), //返单数
              this.generateDayData(res.data.offlineRePCTANdtos[0], "month", "dtos"), //返单品种比
              this.generateDayData(res.data.offlineReAreadtos[0], "month", "dtos"), //返单面积
              this.generateDayData(res.data.offlineRePCTAreadtos[0], "month", "dtos"), //返单面积比
              this.generateDayData(res.data.offline2LayersNumdtos[0], "month", "dtos"), //双面板数量
              this.generateDayData(res.data.offline2LPCTANdtos[0], "month", "dtos"), //双面板面积品种比
              this.generateDayData(res.data.offline2LayersAreadtos[0], "month", "dtos"), //双面板面积
              this.generateDayData(res.data.offline2LPCTAreadtos[0], "month", "dtos"), //双面板面积比
              this.generateDayData(res.data.offline4LayersNumdtos[0], "month", "dtos"), //4层板数量
              this.generateDayData(res.data.offline4LPCTANdtos[0], "month", "dtos"), //4层板面积品种比
              this.generateDayData(res.data.offline4LayersAreadtos[0], "month", "dtos"), //4层板面积
              this.generateDayData(res.data.offline4LPCTAreadtos[0], "month", "dtos"), //4层板面积比
              this.generateDayData(res.data.offline6LayersNumdtos[0], "month", "dtos"), //4-8
              this.generateDayData(res.data.offline6LPCTANdtos[0], "month", "dtos"), //4-8品种比
              this.generateDayData(res.data.offline6LayersAreadtos[0], "month", "dtos"), //4-8层板面积
              this.generateDayData(res.data.offline6LPCTAreadtos[0], "month", "dtos"), //4-8层板面积比
              this.generateDayData(res.data.offline10LayersNumdtos[0], "month", "dtos"), //>8
              this.generateDayData(res.data.offline10LPCTANdtos[0], "month", "dtos"), //>8面积品种比
              this.generateDayData(res.data.offline10LayersAreadtos[0], "month", "dtos"), //>8层板面积
              this.generateDayData(res.data.offline10LPCTAreadtos[0], "month", "dtos"), //>8层板面积比
              this.generateDayData(res.data.offlineHDINumdtos[0], "month", "dtos"), //HDI数量
              this.generateDayData(res.data.offlineHDIPCTANdtos[0], "month", "dtos"), //HDI品种比
              this.generateDayData(res.data.offlineHDIAreadtos[0], "month", "dtos"), //HDI面积
              this.generateDayData(res.data.offlineHDIPCTAreadtos[0], "month", "dtos"), //HDI面积比
              this.generateDayData(res.data.offlineHVANumdtos[0], "month", "dtos"), //HVA数量
              this.generateDayData(res.data.offlineHVAPCTANdtos[0], "month", "dtos"), //HVA品种比
              this.generateDayData(res.data.offlineHVAAreadtos[0], "month", "dtos"), //HVA面积
              this.generateDayData(res.data.offlineHVAPCTAreadtos[0], "month", "dtos"), //HVA面积比
              this.generateDayData(res.data.offlineJiajiNumdtos[0], "month", "dtos"), //加急板数量（面积、品种）
              this.generateDayData(res.data.offlineJiajiPCTANdtos[0], "month", "dtos"), //加急板面积品种比
              this.generateDayData(res.data.offlineJiajiAreadtos[0], "month", "dtos"), //加急板面积
              this.generateDayData(res.data.offlineJiajiPCTAreadtos[0], "month", "dtos"), //加急板面积比
              this.generateDayData(res.data.offlineAverageLayerdtos[0], "month", "dtos"), //平均订单层数
            ];
            this.selectedRowsData = this.ordertabledata1[0];
            let data1 = [];
            res.data.offlineAreadtos[0].dtos.forEach(ite => {
              data1.push(ite.value);
            });
            data1.pop();
            let xdata = Array.from({ length: 12 }, (_, i) => i + 1 + "月");
            this.Orderdata(data1, "下单面积", xdata);
          }
        })
        .finally(() => {
          this.comloading1 = false;
        });
    },
    getprodata() {
      this.Protabledata = [];
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.comloading2 = true;
      let date;
      if (this.YearDate1 && this.YearDate1 !== moment().format("YYYY")) {
        date = moment(`${this.YearDate1}-${moment().format("MM")}-01`)
          .endOf("month")
          .format("YYYY-MM-DD");
      } else {
        // 否则设置为当前日期
        date = moment().format("YYYY-MM-DD");
      }
      ppeofflinedatav1(date, faid)
        .then(res => {
          if (res.code) {
            this.Protabledata = [
              this.generateDayData(res.data.offlineAreadtos[0], "day", "dDtos"), //下单面积
              this.generateDayData(res.data.offlineNumdtos[0], "day", "dDtos"), //下单款数
              this.generateDayData(res.data.offlinePCTANdtos[0], "day", "dDtos"), //面积品种比
              this.generateDayData(res.data.offlineNewNumdtos[0], "day", "dDtos"), //新单数(面积 品种)
              this.generateDayData(res.data.offlineNewPCTANdtos[0], "day", "dDtos"), //新单面积品种比
              this.generateDayData(res.data.offlineNewAreadtos[0], "day", "dDtos"), //新单面积
              this.generateDayData(res.data.offlineNewPCTAreadtos[0], "day", "dDtos"), //新单面积比
              this.generateDayData(res.data.offlineReNumdtos[0], "day", "dDtos"), //返单数
              this.generateDayData(res.data.offlineRePCTANdtos[0], "day", "dDtos"), //返单品种比
              this.generateDayData(res.data.offlineReAreadtos[0], "day", "dDtos"), //返单面积
              this.generateDayData(res.data.offlineRePCTAreadtos[0], "day", "dDtos"), //返单面积比
              this.generateDayData(res.data.offline2LayersNumdtos[0], "day", "dDtos"), //双面板数量
              this.generateDayData(res.data.offline2LPCTANdtos[0], "day", "dDtos"), //双面板面积品种比
              this.generateDayData(res.data.offline2LayersAreadtos[0], "day", "dDtos"), //双面板面积
              this.generateDayData(res.data.offline2LPCTAreadtos[0], "day", "dDtos"), //双面板面积比
              this.generateDayData(res.data.offline4LayersNumdtos[0], "day", "dDtos"), //4层板数量
              this.generateDayData(res.data.offline4LPCTANdtos[0], "day", "dDtos"), //4层板面积品种比
              this.generateDayData(res.data.offline4LayersAreadtos[0], "day", "dDtos"), //4层板面积
              this.generateDayData(res.data.offline4LPCTAreadtos[0], "day", "dDtos"), //4层板面积比
              this.generateDayData(res.data.offline6LayersNumdtos[0], "day", "dDtos"), //4-8
              this.generateDayData(res.data.offline6LPCTANdtos[0], "day", "dDtos"), //4-8品种比
              this.generateDayData(res.data.offline6LayersAreadtos[0], "day", "dDtos"), //4-8层板面积
              this.generateDayData(res.data.offline6LPCTAreadtos[0], "day", "dDtos"), //4-8层板面积比
              this.generateDayData(res.data.offline10LayersNumdtos[0], "day", "dDtos"), //>8
              this.generateDayData(res.data.offline10LPCTANdtos[0], "day", "dDtos"), //>8面积品种比
              this.generateDayData(res.data.offline10LayersAreadtos[0], "day", "dDtos"), //>8层板面积
              this.generateDayData(res.data.offline10LPCTAreadtos[0], "day", "dDtos"), //>8层板面积比
              this.generateDayData(res.data.offlineHDINumdtos[0], "day", "dDtos"), //HDI数量
              this.generateDayData(res.data.offlineHDIPCTANdtos[0], "day", "dDtos"), //HDI品种比
              this.generateDayData(res.data.offlineHDIAreadtos[0], "day", "dDtos"), //HDI面积
              this.generateDayData(res.data.offlineHDIPCTAreadtos[0], "day", "dDtos"), //HDI面积比
              this.generateDayData(res.data.offlineHVANumdtos[0], "day", "dDtos"), //HVA
              this.generateDayData(res.data.offlineHVAPCTANdtos[0], "day", "dDtos"), //HVA面积品种比
              this.generateDayData(res.data.offlineHVAAreadtos[0], "day", "dDtos"), //HVA面积
              this.generateDayData(res.data.offlineHVAPCTAreadtos[0], "day", "dDtos"), //HVA面积比
              this.generateDayData(res.data.offlineJiajiNumdtos[0], "day", "dDtos"), //加急板数量（面积、品种）
              this.generateDayData(res.data.offlineJiajiPCTANdtos[0], "day", "dDtos"), //加急板面积品种比
              this.generateDayData(res.data.offlineJiajiAreadtos[0], "day", "dDtos"), //加急板面积
              this.generateDayData(res.data.offlineJiajiPCTAreadtos[0], "day", "dDtos"), //加急板面积比
              this.generateDayData(res.data.offlineAverageLayerdtos[0], "day", "dDtos"), //平均订单层数
            ];
            let data1 = [];
            res.data.offlineAreadtos[0].dDtos.forEach(ite => {
              data1.push(ite.value);
            });
            data1.pop();
          }
        })
        .finally(() => {
          this.comloading2 = false;
        });
    },
    getprodata1() {
      this.Protabledata1 = [];
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.comloading2 = true;
      let date;
      if (this.YearDate1 && this.YearDate1 !== moment().format("YYYY")) {
        date = `${this.YearDate1}-12-31`;
      } else {
        date = moment().format("YYYY-MM-DD");
      }
      ppeofflinedatav2(date, faid)
        .then(res => {
          if (res.code) {
            this.Protabledata1 = [
              this.generateDayData(res.data.offlineAreadtos[0], "month", "dtos"), //下单面积
              this.generateDayData(res.data.offlineNumdtos[0], "month", "dtos"), //下单款数
              this.generateDayData(res.data.offlinePCTANdtos[0], "month", "dtos"), //面积品种比
              this.generateDayData(res.data.offlineNewNumdtos[0], "month", "dtos"), //新单数(面积 品种)
              this.generateDayData(res.data.offlineNewPCTANdtos[0], "month", "dtos"), //新单面积品种比
              this.generateDayData(res.data.offlineNewAreadtos[0], "month", "dtos"), //新单面积
              this.generateDayData(res.data.offlineNewPCTAreadtos[0], "month", "dtos"), //新单面积比
              this.generateDayData(res.data.offlineReNumdtos[0], "month", "dtos"), //返单数
              this.generateDayData(res.data.offlineRePCTANdtos[0], "month", "dtos"), //返单品种比
              this.generateDayData(res.data.offlineReAreadtos[0], "month", "dtos"), //返单面积
              this.generateDayData(res.data.offlineRePCTAreadtos[0], "month", "dtos"), //返单面积比
              this.generateDayData(res.data.offline2LayersNumdtos[0], "month", "dtos"), //双面板数量
              this.generateDayData(res.data.offline2LPCTANdtos[0], "month", "dtos"), //双面板面积品种比
              this.generateDayData(res.data.offline2LayersAreadtos[0], "month", "dtos"), //双面板面积
              this.generateDayData(res.data.offline2LPCTAreadtos[0], "month", "dtos"), //双面板面积比
              this.generateDayData(res.data.offline4LayersNumdtos[0], "month", "dtos"), //4层板数量
              this.generateDayData(res.data.offline4LPCTANdtos[0], "month", "dtos"), //4层板面积品种比
              this.generateDayData(res.data.offline4LayersAreadtos[0], "month", "dtos"), //4层板面积
              this.generateDayData(res.data.offline4LPCTAreadtos[0], "month", "dtos"), //4层板面积比
              this.generateDayData(res.data.offline6LayersNumdtos[0], "month", "dtos"), //4-8
              this.generateDayData(res.data.offline6LPCTANdtos[0], "month", "dtos"), //4-8品种比
              this.generateDayData(res.data.offline6LayersAreadtos[0], "month", "dtos"), //4-8层板面积
              this.generateDayData(res.data.offline6LPCTAreadtos[0], "month", "dtos"), //4-8层板面积比
              this.generateDayData(res.data.offline10LayersNumdtos[0], "month", "dtos"), //>8
              this.generateDayData(res.data.offline10LPCTANdtos[0], "month", "dtos"), //>8面积品种比
              this.generateDayData(res.data.offline10LayersAreadtos[0], "month", "dtos"), //>8层板面积
              this.generateDayData(res.data.offline10LPCTAreadtos[0], "month", "dtos"), //>8层板面积比
              this.generateDayData(res.data.offlineHDINumdtos[0], "month", "dtos"), //HDI数量
              this.generateDayData(res.data.offlineHDIPCTANdtos[0], "month", "dtos"), //HDI品种比
              this.generateDayData(res.data.offlineHDIAreadtos[0], "month", "dtos"), //HDI面积
              this.generateDayData(res.data.offlineHDIPCTAreadtos[0], "month", "dtos"), //HDI面积比
              this.generateDayData(res.data.offlineHVANumdtos[0], "month", "dtos"), //HVA数量
              this.generateDayData(res.data.offlineHVAPCTANdtos[0], "month", "dtos"), //HVA品种比
              this.generateDayData(res.data.offlineHVAAreadtos[0], "month", "dtos"), //HVA面积
              this.generateDayData(res.data.offlineHVAPCTAreadtos[0], "month", "dtos"), //HVA面积比
              this.generateDayData(res.data.offlineJiajiNumdtos[0], "month", "dtos"), //加急板数量（面积、品种）
              this.generateDayData(res.data.offlineJiajiPCTANdtos[0], "month", "dtos"), //加急板面积品种比
              this.generateDayData(res.data.offlineJiajiAreadtos[0], "month", "dtos"), //加急板面积
              this.generateDayData(res.data.offlineJiajiPCTAreadtos[0], "month", "dtos"), //加急板面积比
              this.generateDayData(res.data.offlineAverageLayerdtos[0], "month", "dtos"), //平均订单层数
            ];
            let data1 = [];
            res.data.offlineAreadtos[0].dtos.forEach(ite => {
              data1.push(ite.value);
            });
            data1.pop();
          }
        })
        .finally(() => {
          this.comloading2 = false;
        });
    },
    Combiningrate() {
      this.Combiningdata = [];
      this.averagebar = [];
      this.averageline = [];
      this.Combinationrate(this.averageline, this.averagebar, "", "", []);
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.comloading = true;
      let date;
      if (this.YearDate2 && this.YearDate2 !== moment().format("YYYY")) {
        date = moment(`${this.YearDate2}-${moment().format("MM")}-01`)
          .endOf("month")
          .format("YYYY-MM-DD");
      } else {
        // 否则设置为当前日期
        date = moment().format("YYYY-MM-DD");
      }
      datastatisticsv1(date, faid)
        .then(res => {
          if (res.code) {
            this.comtabledata = [
              this.generateDayData(res.data.waitHPNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.zhHPNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.offlineNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.hpNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.hpRatedtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.avgHPzbNumdtos[0], "day", "monthDtos"),
            ];
            res.data.avgHPzbNumdtos[0].monthDtos.forEach(ite => {
              this.averagebar.push(Number(ite.value));
            });
            res.data.hpRatedtos[0].monthDtos.forEach(ite => {
              this.averageline.push(Number(ite.value));
            });
            this.averageline.pop();
            this.averagebar.pop();
            let xdata = Array.from({ length: 31 }, (_, i) => i + 1 + "日");
            this.Combinationrate(this.averageline, this.averagebar, "当日合拼平均子板数", "合拼率", xdata);
          }
        })
        .finally(() => {
          this.comloading = false;
        });
    },
    //月
    Combiningrate1() {
      this.Combiningdata = [];
      this.averagebar = [];
      this.averageline = [];
      this.Combinationrate(this.averageline, this.averagebar, "", "", []);
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.comloading = true;
      let date;
      if (this.YearDate2 && this.YearDate2 !== moment().format("YYYY")) {
        date = `${this.YearDate2}-12-31`;
      } else {
        date = moment().format("YYYY-MM-DD");
      }
      datastatisticsv2(date, faid)
        .then(res => {
          if (res.code) {
            res.data.avgHPzbNumdtos[0].yearDtos.forEach(ite => {
              this.averagebar.push(Number(ite.value));
            });
            res.data.hpRatedtos[0].yearDtos.forEach(ite => {
              this.averageline.push(Number(ite.value));
            });
            this.averageline.pop();
            this.averagebar.pop();
            this.comtabledata1 = [
              this.generateDayData(res.data.waitHPNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.zhHPNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.offlineNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.hpNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.hpRatedtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.avgHPzbNumdtos[0], "month", "yearDtos"),
            ];
            let xdata = Array.from({ length: 12 }, (_, i) => i + 1 + "月");
            this.Combinationrate(this.averageline, this.averagebar, "当月合拼平均子板数", "合拼率", xdata);
          }
        })
        .finally(() => {
          this.comloading = false;
        });
    },
    generateDayData(data, time, dtos) {
      return {
        name: data.name,
        ...data[dtos].reduce((acc, dayData, index) => {
          acc[time + (index + 1)] = dayData.value;
          acc.name = data.name;
          return acc;
        }, {}),
      };
    },
    rangechange(value, dateString) {
      localStorage.setItem("dateString", dateString);
      this.classification1 = false;
      this.classification2 = false;
      this.classification3 = false;
      this.classification4 = true;
      this.Intervalselection();
    },
    Layercount() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          title: {
            left: "center",
            top: "center",
            textStyle: {
              fontSize: 12,
            },
          },
          legend: {
            orient: "vertical",
            icon: "rect",
            itemHeight: 8, // 修改icon图形大小
            itemWidth: 8, // 修改icon图形大小
            x: "right",
            y: "center",
            align: "left",
            data: ["单双面", "四层", "六层", "六层以上"],
          },
          color: [" rgb(72,116,203)", " rgb(242,186,2)", " rgb(238,130,47)", " rgb(117,189,66)"],
          series: [
            {
              name: "",
              type: "pie",
              radius: ["40%", "70%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              label: {
                normal: {
                  position: "inner",
                  show: true,
                  textStyle: {
                    fontWeight: 600,
                    fontSize: 8,
                  },
                  formatter: "{d}" + "%",
                },
              },

              labelLine: {
                show: false,
              },
              minAngle: 30 || 0,
              data: [
                { value: this.Orderingdata.lay1offlinenum || 0, name: "单双面" },
                { value: this.Orderingdata.lay4offlinenum || 0, name: "四层" },
                { value: this.Orderingdata.lay6offlinenum || 0, name: "六层" },
                { value: this.Orderingdata.lay6Upofflinenum || 0, name: "六层以上" },
              ],
            },
          ],
        };
        let chartDom = document.getElementById("Layercount");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    Makeorder() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          title: {
            left: "center",
            top: "center",
            textStyle: {
              fontSize: 12,
            },
          },
          legend: {
            orient: "vertical",
            icon: "rect",
            itemHeight: 8, // 修改icon图形大小
            itemWidth: 8, // 修改icon图形大小
            x: "right",
            y: "center",
            align: "left",
            data: ["直通", "全套"],
          },
          color: [" rgb(72,116,203)", " rgb(242,186,2)"],
          series: [
            {
              name: "",
              type: "pie",
              radius: ["40%", "70%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              label: {
                normal: {
                  position: "inner",
                  show: true,
                  textStyle: {
                    fontWeight: 600,
                    fontSize: 8,
                  },
                  formatter: "{d}" + "%",
                },
              },

              labelLine: {
                show: false,
              },
              minAngle: 30 || 0,
              data: [
                { value: this.Orderingdata.noFullsetofflinenum || 0, name: "直通" },
                { value: this.Orderingdata.fullsetofflinenum || 0, name: "全套" },
              ],
            },
          ],
        };
        let chartDom = document.getElementById("Makeorder");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    Totalorderplacement() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              var seriesType = params.seriesType; // 获取数据点所属的系列类型
              if (seriesType === "line") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value;
              } else if (seriesType === "bar") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value;
              }
            },
          },
          xAxis: [
            {
              type: "category",
              data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                textStyle: {
                  color: "rgba(121, 128, 137, 0)",
                  fontSize: 10,
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              data: [],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "rgb(121,128,137)",
                  fontSize: 10,
                },
                axisPointer: { snap: true },
              },
            },
          ],
          grid: {
            left: "12%",
            right: "5%",
            top: "10%",
            bottom: "25%",
          },
          color: ["rgb(254,146,123)", "rgb(68,189,220)"],
          legend: [
            {
              data: ["总下单面积(㎡)", "总下单品种(款)"],
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
              orient: "vertical",
              x: "5",
              y: "235",
              itemWidth: 20,
              itemHeight: 10,
            },
            {
              data: ["总下单面积(㎡)", "总下单品种(款)"],
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 12,
              },
              orient: "horizontal",
              x: "400",
              y: "0",
              itemWidth: 20,
              itemHeight: 10,
            },
          ],
          series: [
            {
              name: "总下单面积(㎡)",
              type: "bar",
              barGap: "-8%",
              barWidth: "30%",
              data: this.Orderingarea,
              itemStyle: {
                borderColor: "rgb(253,94,58)",
                borderWidth: 2,
                borderType: "solid",
              },
            },
            {
              data: this.Ordervariety,
              type: "line",
              name: "总下单品种(款)",
              label: {
                show: false,
                position: "top",
                textStyle: {
                  fontSize: 14,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
          ],
        };
        let chartDom = document.getElementById("Totalorderplacement");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    Combinationrate(linedata, bardata, leg1, leg2, xdata) {
      const option = {
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            var seriesType = params.seriesType; // 获取数据点所属的系列类型
            if (seriesType === "line") {
              return params.seriesName + "<br/>" + params.name + "日 : " + params.value + "%";
            } else if (seriesType === "bar") {
              return params.seriesName + "<br/>" + params.name + "日: " + params.value;
            }
          },
        },
        xAxis: [{ type: "category", data: xdata }],
        yAxis: [
          {
            type: "value",

            data: [],
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
            },
          },
          {
            type: "value",
            data: [],
            axisLine: { show: false },
            position: "right", // 将右边的 y 轴显示在右侧
            axisTick: { show: false },
            axisLabel: {
              formatter: "{value} %",
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
              axisPointer: { snap: true },
            },
          },
        ],
        grid: {
          left: "3%", // 左边距
          right: "3%", // 右边距
          top: "25%", // 上边距
          bottom: "10%", // 下边距
        },
        legend: [
          {
            data: [leg1, leg2],
            textStyle: {
              color: "rgb(121,128,137)",
              fontSize: 12,
            },
            orient: "horizontal",
            x: "600",
            y: "0",
            itemWidth: 20,
            itemHeight: 10,
          },
        ],
        color: ["rgb(254,146,123)", "rgb(123,126,223)"],
        series: [
          {
            name: leg1,
            type: "bar",
            data: bardata,
            barWidth: "50%",
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 10,
              },
            },
            smooth: false,
            symbol: "circle",
            symbolSize: 8,
          },
          {
            name: leg2,
            yAxisIndex: 1, // 使用右侧的 y 轴
            type: "line",
            data: linedata,
            label: {
              formatter: function (params) {
                return params.value + "%";
              },
              show: true,
              position: "top",
              textStyle: {
                fontSize: 10,
              },
            },
            smooth: false,
            symbol: "circle",
            symbolSize: 8,
          },
        ],
      };
      let chartDom = document.getElementById("Combinationrate");
      let myChart = echarts.init(chartDom);
      myChart.setOption(option);
    },
    Orderdata(data, name, xdata) {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            data: xdata,
            axisLabel: {
              textStyle: {
                fontSize: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "gray", // 设置 x 轴轴线的颜色为灰色
              },
            },
            axisTick: {
              lineStyle: {
                color: "gray", // 设置 x 轴坐标刻度线的颜色为灰色
              },
            },
          },
          yAxis: {
            axisLabel: {
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
              axisPointer: { snap: true },
            },
          },
          grid: {
            left: "3%", // 左边距
            right: "3%", // 右边距
            top: "25%", // 上边距
            bottom: "10%", // 下边距
          },
          color: [" rgb(254,146,123)"],
          legend: {
            data: [name],
            textStyle: {
              color: "rgb(121,128,137)",
              fontSize: 12,
            },
            top: "5%", // 图例的位置
          },
          series: [
            {
              name: name,
              data: data,
              type: "line",
              label: {
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 10,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
          ],
        };
        let chartDom = document.getElementById("Orderdata");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    CAMPersonalErgonomics() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
            axisLabel: {
              textStyle: {
                fontSize: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "gray",
              },
            },
            axisTick: {
              lineStyle: {
                color: "gray",
              },
            },
          },
          yAxis: {
            axisLabel: {
              textStyle: {
                fontSize: 10,
              },
            },
          },
          grid: {
            left: "3%",
            right: "3%",
            top: "10%",
            bottom: "10%",
          },
          color: [" rgb(68,189,220)"],
          legend: {
            data: ["CAM制作人均效率(个/日)"], // 图例项的名称
            textStyle: {
              // 图例项的文字样式
              color: "rgb(121,128,137)",
              fontSize: 12,
            },
            top: "0%", // 图例的位置
          },
          series: [
            {
              name: "CAM制作人均效率(个/日)",
              data: this.CAMdata,
              type: "line",
              label: {
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 10,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
          ],
        };
        let chartDom = document.getElementById("CAMPersonalErgonomics");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    Customerinquiryrate() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
            axisLabel: {
              textStyle: {
                fontSize: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "gray", // 设置 x 轴轴线的颜色为灰色
              },
            },
            axisTick: {
              lineStyle: {
                color: "gray", // 设置 x 轴坐标刻度线的颜色为灰色
              },
            },
          },
          yAxis: {
            axisLabel: {
              formatter: "{value} %",
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
              axisPointer: { snap: true },
            },
          },
          grid: {
            left: "3%", // 左边距
            right: "3%", // 右边距
            top: "25%", // 上边距
            bottom: "10%", // 下边距
          },
          color: ["rgb(254,146,123)", "rgb(68,189,220)"],
          legend: [
            {
              data: ["工程一次问客率(%)", "工程二次问客率(%)"],
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 12,
              },
              top: "5%", // 图例的位置
            },
          ],
          series: [
            {
              name: "工程一次问客率(%)",
              data: this.EQdata,
              type: "line",
              label: {
                formatter: function (params) {
                  return params.value + "%";
                },
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 10,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
            {
              name: "工程二次问客率(%)",
              data: this.MultipleEQs,
              type: "line",
              label: {
                formatter: function (params) {
                  return params.value + "%";
                },
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 10,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
          ],
        };
        let chartDom = document.getElementById("Customerinquiryrate");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
  .rowBackgroundColor {
    background: #dcdcdc !important;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
/deep/.ant-table-body {
  overflow-x: scroll;
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    // background: #ff9900;
    background: #e7e7e7;
    // #fff9e6
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
/deep/.ant-table-wrapper {
  border-top: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
}
/deep/ .ant-table-thead > tr > th {
  height: 27px !important;
}
/deep/ .ant-table .ant-table-tbody > tr > td {
  height: 27px !important;
}
/deep/.analysishp {
  .ant-calendar-picker {
    position: relative;
    height: 32px;
    cursor: pointer;
    width: 200px;
    top: 18px;
    left: 20px;
    .anticon svg {
      display: inline-block;
      position: relative;
      top: -10px;
    }
  }
  .ant-select-selection--single {
    width: 200px;
    border: #ff9900 1px solid;
    margin: 15px 15px 0 15px;
  }
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 4px;
    overflow-wrap: break-word;
  }
  .ant-table-align-center {
    background-color: white;
  }
  .ant-table-bordered .ant-table-thead > tr > th,
  .ant-table-bordered .ant-table-tbody > tr > td {
    border-left: 1px solid #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: white;
}
/deep/.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: #ff9900 !important;
}
/deep/.ant-input {
  border: #ff9900 1px solid;
}
.bot {
  height: 200px;
  width: 98.9%;
  margin-left: 8px;
  border: 1px solid green;
}
.analysis {
  height: 816px;
  width: 1687px;
  background: #ffffff;
  position: absolute;
  top: 6px;
  .leftdata {
    width: 40%;
    height: 100%;
    /deep/.ant-select-selection--single {
      position: relative;
      height: 32px;
      cursor: pointer;
      width: 150px;
      border: #ff9900 1px solid;
    }
    .Orderingsituation {
      .data1 {
        background-color: rgb(72, 116, 203);
        width: 200px;
        height: 40px;
        border-radius: 10px;
        margin-left: 25px;
        color: #ffffff;
        text-align: center;
        font-size: 16px;
        line-height: 40px;
        margin-top: 7px;
      }
      .data2 {
        background-color: rgb(72, 116, 203);
        width: 360px;
        height: 40px;
        border-radius: 10px;
        margin-left: 20px;
        color: #ffffff;
        text-align: center;
        line-height: 40px;
        font-size: 20px;
        margin-top: 7px;
      }
    }
    .bac {
      width: 98%;
      // margin: -21px 7px;
      margin: 8px 8px;
      border: 1px solid green;
      height: 88.2%;
      .left1 {
        width: 300px;
        height: 200px;
        margin-top: 30px;
      }
    }
    .dayweek {
      background-color: #ff9900;
      border: #ff9900 1px solid;
      margin: 15px 0 15px 30px;
      color: #ffffff;
    }
    .weekmonth {
      background-color: rgb(250, 250, 250);
      border: #ff9900 1px solid;
      margin: 15px 0 15px 30px;
      color: #ff9900;
    }
  }
  .rightdata {
    width: 60%;
    height: 100%;
    .top2 {
      width: 98%;
      margin: 8px 10px;
      border: 1px solid green;
      height: 48%;
      .chievementrate {
        width: 98%;
        height: 282px;
        position: relative;
        top: 10px;
        left: 20px;
        // background-color: #ff9900;
      }
    }
    .bot2 {
      width: 98%;
      margin: 8px 10px;
      border: 1px solid green;
      height: 48%;
      .chievementrate {
        width: 98%;
        height: 280px;
        position: relative;
        top: 10px;
        left: 10px;
      }
    }
  }
}
</style>
