import { request, METHOD } from "@/utils/request";
//验孔待处理订单
export async function checkdrillwaithandle(params) {
  return request(`/api/app/e-mSTProc-production-process/check-drill-wait-handle`, METHOD.GET, params);
}
//验孔已处理订单
export async function checkdrillfinishhandle(params) {
  return request(`/api/app/e-mSTProc-production-process/check-drill-finish-handle`, METHOD.GET, params);
}
//验孔开始
export async function setcheckdrillstart(Id) {
  return request(`/api/app/e-mSTProc-production-process/set-check-drill-start/${Id}`, METHOD.POST);
}
//验孔结束
export async function setcheckdrillfinish(Id) {
  return request(`/api/app/e-mSTProc-production-process/set-check-drill-finish/${Id}`, METHOD.POST);
}
//缺陷登记
export async function checkdrillorder(params) {
  return request(`/api/app/e-mSTProc-production-process/check-drill-order`, METHOD.POST, params);
}
//获取钻孔数据
export async function drljson(id) {
  return request(`api/app/e-mSTProc-production-process/${id}/drl-json`, METHOD.GET);
}
//修改钻孔表信息
export async function updatedrltool(params) {
  return request(`/api/app/e-mSTProc-production-process/update-drl-tool`, METHOD.POST, params);
}
