import Vue from "vue";
import App from "./App.vue";
import { initRouter } from "./router";
import "./theme/index.less";
import Antd from "ant-design-vue";
import Viewer from "v-viewer";
import "viewerjs/dist/viewer.css";
import formCreate from "@form-create/ant-design-vue";
import "xe-utils";
import VXETable from "vxe-table";
import "vxe-table/lib/style.css";
import * as echarts from "echarts";
import barcode from "@xkeshi/vue-barcode";
// import Print from '@/plugins/print-Print'
// Vue.use(Print)
// import  JSZip from "jszip"
// Vue.use(JSZip)
import VueTouch from "vue-touch";
Vue.use(VueTouch, { name: "v-touch" });

import Print from "vue-print-nb";
Vue.use(Print);
import axios from "axios";
Vue.prototype.$axios = axios;

import VueDraggableResizable from "vue-draggable-resizable";
Vue.use(VueDraggableResizable);
import "vue-draggable-resizable/dist/VueDraggableResizable.css";

Vue.use(barcode);
Vue.use(VXETable);
Vue.use(Viewer);
Vue.use(echarts);

// import '@/mock'
import store from "./store";
import "animate.css/source/animate.css";
import Plugins from "@/plugins";
import { initI18n } from "@/utils/i18n";
import bootstrap from "@/bootstrap";
import "moment/locale/zh-cn";
import "./utils/filter";
// import Print from 'vue-print-nb'
// import VueDND from 'awe-dnd'
import md5 from "js-md5";
// import infiniteScroll from 'vue-infinite-scroll';  // 滚动加载

// Vue.use(htmlToPdf);
Vue.prototype.$md5 = md5;

Vue.config.devtools = true;
const router = initRouter(store.state.setting.asyncRoutes);
const i18n = initI18n("CN", "US", "TH");
Vue.prototype.getFloat = function (number, n) {
  n = n ? parseInt(n) : 0;
  if (n <= 0) return Math.round(number);
  number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n);
  return number;
};
Vue.prototype.debounce = function (func, wait) {
  wait = wait || 500;
  var _this = this;
  let timeout;
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(_this, args); // 这里确保 args 是展开的
    }, wait);
  };
};
Vue.prototype.throttle = function (func, wait) {
  var _this = this;
  wait = wait || 1000; // 如果没有提供wait参数，则默认为1000毫秒
  let lastCall = 0; // 上次执行函数的时间戳

  return function (...args) {
    const now = Date.now(); // 当前时间戳
    // 如果当前时间与上次执行时间的差值大于等待时间，则执行函数
    if (now - lastCall >= wait) {
      lastCall = now; // 更新上次执行时间
      func.apply(_this, args); // 执行函数
    }
  };
};
// Vue.use(VueDND)
// Vue.use(infiniteScroll)
Vue.use(formCreate);
Vue.use(Antd);
// Vue.use(Viser)
Vue.use(Plugins);
// Vue.use(Print)
Vue.directive("dragging", function (el, binding) {
  // console.log(el);
  el.style = "color:" + binding.value;
});

import Vue2OrgTree from "vue2-org-tree";
import "vue2-org-tree/dist/style.css";

Vue.use(Vue2OrgTree);
Vue.config.productionTip = false;

bootstrap({ router, store, i18n, message: Vue.prototype.$message });

new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
}).$mount("#app");
