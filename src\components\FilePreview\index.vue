<template> 
    <div title="文件预览" :visible="showDoc || showPdf || showImg" :maskClosable="false" @cancel="cancel"
      width="750px">
      <div class="modal-body form">
        <div v-if="showImg">
          <img :src="images" preview="1" preview-text="" style="width:100%"/>
        </div>
        <div v-show="showDoc" ref="word">
          <iframe v-if="fileUrl" frameborder="0"
                  :src="fileUrl"
                  width='100%'
                  height="100%">
          </iframe>
        </div>      
          <div v-show="showPdf" class="pdf-layout" id="top">
            <div id="imageContainer2" style="position:absolute;">
          <pdf-view
            ref="pdf"
            :src="pdfPath"
            :page="currentPage"
            @num-pages="pageCount=$event"
            @page-loaded="currentPage=$event"
            @loaded="loadPdfHandler"/>
            </div>
        </div> 
        <div id="grabber2" style="position: absolute; top: 0; left: 0; cursor: grab;width: 100%; height: 100%; "></div>      
      </div>  
      <template >
        <div v-if="showPdf" :class="type=='GC'?'pdf-layout-page1':'pdf-layout-page'">
          <a-button @click="changePdfPage(0)" :disabled="currentPage===1">上一页</a-button>
          {{currentPage}} / {{pageCount}}
          <a-button @click="changePdfPage(1)" :disabled="currentPage===pageCount" >下一页</a-button>
          <a-button  @click="cancel">取消</a-button>
        </div>
      </template> 
  </div>
</template>
<script>
  import pdfView from 'vue-pdf'
  import axios from 'axios'
  const docxPre = require('docx-preview')
  window.JSZip = require('jszip')
  export default {
    name: 'FilePreview',
    components: { pdfView },
    props:['type'],
    data() {
      return {
        showDoc: false,//判断如果是否为word文件显示
        showPdf: false,//判断如果是否为pdf文件显示
        showImg: false,//判断如果是否为图片显示
        fileUrl: '',//pdf链接
        images: '',//图片链接
        pdfPath:'',
        currentPage: 0, // pdf文件页码
        pageCount: 0, // pdf文件总页数
      }
    },
    mounted(){
      const imageContainer2 = document.getElementById('imageContainer2');
      const grabber2 = document.getElementById('grabber2');    
      let isDragging2 = false;
      let startX2, startY2, offsetX2, offsetY2;    
      grabber2.addEventListener('mousedown', (event) => {
          isDragging2 = true;
          startX2= event.clientX - imageContainer2.offsetLeft;
          startY2 = event.clientY - imageContainer2.offsetTop;
          offsetX2 = imageContainer2.offsetLeft;
          offsetY2 = imageContainer2.offsetTop;
          grabber2.style.cursor = 'grabbing';
      });
      document.addEventListener('mousemove', (event) => {
          if (isDragging2) {
              const newX = event.clientX - startX2;
              const newY = event.clientY - startY2;
              imageContainer2.style.left = `${newX}px`;
              imageContainer2.style.top = `${newY}px`;
          }
      });
          document.addEventListener('mouseup', () => {
          isDragging2 = false;
          grabber2.style.cursor = 'grab';
      });
    },
    methods: {
      showView(filePath) {
        let that = this
         let type = filePath.split('.')[filePath.split('.').length - 1].toLowerCase()
        if (type === 'jpg' || type === 'png' || type === 'jpeg') {
          that.images = filePath
          that.showImg = true
        } else if (type === 'pdf') {
          that.loadPdfHandler()//重置pdf第一页展示
          that.pdfPath = filePath
          that.showPdf = true
        } else if (type === 'doc') {//word预览 
          that.fileUrl = 'https://view.officeapps.live.com/op/view.aspx?src=' + filePath
          that.showDoc = true
        } else if (type === 'docx') {//word预览
          that.showDoc = true
          that.previewWord(filePath)
        }
      },
      // 后端返回二进制流
      previewWord(filePath) {
        console.log('docx')
        let that = this
        axios({
          method: 'get',
          responseType: 'blob', 
          url: filePath  
        }).then(({ data }) => {
          docxPre.renderAsync(data, this.$refs.word)
        })
      },
      //pdf上一页下一页操作
      changePdfPage(val) { 
        if (val === 0 && this.currentPage > 1) {
          this.currentPage-- 
        }
        if (val === 1 && this.currentPage < this.pageCount) {
          this.currentPage++
          this.top()
        }
      },
      top() {
        document.querySelector('#top').scrollIntoView(true)
      },
      // pdf加载时
      loadPdfHandler(e) {
        this.currentPage = 1 // 加载的时候先加载第一页
      },
      cancel() {
        this.showDoc = false//判断如果是否为word文件显示
        this.showPdf = false//判断如果是否为pdf文件显示
        this.showImg = false//判断如果是否为图片显示
      }
    }
  }
</script>
<style lang="less" scoped>

  .form {
    &::-webkit-scrollbar {
      //整体样式
      width: 10px !important; //y轴滚动条粗细
      height: 10px
    }
    &::-webkit-scrollbar-thumb {
      //滑动滑块条样式
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      background: #a5a5a5;
    }
    //height: 620px;
    overflow: auto;
    //border: 2px solid #efefef;
  } 
  .ant-btn{
    margin-right: 10px;
  }
 .pdf-layout-page {
    display: inline-block;
    text-align: center;
    font-size: 14px;
    position: fixed;
    bottom: 8px;
    left: 969px;
  }
  .pdf-layout-page1 {
    font-size: 14px;
    position: fixed;
    left: 50%;
    bottom: 65px;
  }

</style>