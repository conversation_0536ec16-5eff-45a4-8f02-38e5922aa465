<template>
  <div class="leftTab" style="position: relative" ref="tableWrapper">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :customRow="customRow"
      rowKey="proOrderId"
      :scroll="{ y: 737, x: 880 }"
      :loading="orderListTableLoading"
      :pagination="pagination"
      :rowClassName="isRedRow"
      @change="handleTableChange"
      :class="dataSource.length ? 'mintable' : ''"
    >
      <template slot="labelUrl" slot-scope="record">
        <span @click.stop="OperationLog(record.proOrderId)" style="color: #428bca">日志</span>
      </template>
      <template slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </template>
      <div slot="pdct_NAME_" slot-scope="text, record" style="display: flex; align-items: center">
        <a style="color: black" :title="record.pdct_NAME_">{{ text }}</a
        >&nbsp;
        <span class="tagNum" style="display: inline-block; height: 19px">
          <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
            <span class="noCopy" style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
            <span class="noCopy" style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tooltip title="加急" v-else-if="record.isJiaji">
            <span class="noCopy" style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tag v-if="record.ka" class="noCopy tagstyle"> KA </a-tag>
          <a-tooltip title="新客户" v-if="record.isNewCust">
            <a-tag class="noCopy tagstyle"> 新 </a-tag>
          </a-tooltip>
          <a-tooltip v-if="record.onLineEcnState > 0" :title="record.onLineOrRecordEcn == 2 ? '更改存档升级' : 'ECN在线改版'">
            <a-tag class="noCopy tagstyle"> 升 </a-tag>
          </a-tooltip>
          <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.isReOrder == 1">
            <a-tag class="noCopy tagstyle"> 改 </a-tag>
          </a-tooltip>
          <a-tooltip title="二次投产" v-if="record.backUserAccount > 0">
            <a-tag class="noCopy tagstyle"> 二 </a-tag>
          </a-tooltip>
          <span>
            <a-tag
              class="noCopy tagstyle"
              v-if="record.isEQ == 1 && record.orderState_ != '问客已回复' && record.orderState_ != '问客已审核' && record.orderState_ != '问客'"
            >
              问
            </a-tag>
            <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -4px" v-if="record.eqNumber">{{ record.eqNumber }}</span>
          </span>
          <a-tooltip v-if="record.isFix" title="返修中">
            <a-tag class="noCopy tagstyle"> 修 </a-tag>
          </a-tooltip>
          <a-tag
            @click.stop="xiudisplay(record)"
            v-if="record.isOrderModify"
            class="noCopy tagstyle"
            style="background: #ff9900; border-color: #ff9900"
          >
            修
          </a-tag>
          <a-tooltip v-if="record.identificationType == 1" title="按新单制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              新 </span
            >&nbsp;
          </a-tooltip>
          <a-tooltip v-if="record.identificationType == 2" title="按返单有改制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              改 </span
            >&nbsp;
          </a-tooltip>
          <a-tag class="noCopy tagstyle" v-if="record.isBigCus == '是'"> KA </a-tag>
          <a-tag class="noCopy tagstyle" v-if="record.isReverse == '1' || record.isReverse == '2'" color="#ff9900"> 反 </a-tag>
          <a-tag v-if="!record.isFullSet" class="noCopy tagstyle"> 直 </a-tag>
          <a-tag v-if="record.reverseOrder_" class="noCopy tagstyle">{{ record.reverseOrder_ | splitFilter }} </a-tag>
          <a-tag class="noCopy tagstyle" v-if="record.isJunG"> {{ record.joinFactoryId == 70 ? "J" : "军" }} </a-tag>
          <a-tooltip :title="record.fileUploadedTime ? '重传文件时间:' + record.fileUploadedTime : ''" v-if="record.fileUploadedCount > 0">
            <span>
              <a-tag class="noCopy tagstyle" style="background: red; border-color: red"> 重 </a-tag>
              <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -2px">{{ record.fileUploadedCount }}</span>
            </span>
          </a-tooltip>
          <a-tag v-if="record.pauseCancelState == 2" class="noCopy tagstyle"> 取消 </a-tag>
          <a-tag v-if="record.pauseCancelState == 3" class="noCopy tagstyle"> 暂停 </a-tag>
          <a-tag v-if="record.pauseCancelState == 4" class="noCopy tagstyle"> 暂停取消 </a-tag>
          <a-tooltip title="确认工作稿" v-if="record.confirmWorkingDraft">
            <a-tag class="noCopy tagstyle"> 确 </a-tag>
          </a-tooltip>
        </span>
      </div>
      <template slot="pcbFileName" slot-scope="record">
        <a-tooltip :title="record.pcbFileName">
          <span v-if="record.pcbFilePath" @click="down(record)" style="color: rgb(66, 139, 202)">{{ record.pcbFileName }}</span>
        </a-tooltip>
      </template>
    </a-table>
    <right-copy ref="RightCopy" />
    <a-modal title="修改内容" :width="1300" :visible="xiuvisible" destroyOnClose centered :maskClosable="false" @cancel="reportHandleCancel">
      <div>
        <a-table
          class="xiu"
          :columns="columns4"
          :dataSource="dataSource4"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 400 }"
          :class="dataSource4.length ? 'min-table' : ''"
          :pagination="false"
          :loading="loading2"
        >
          <template slot="isPrintContract" slot-scope="record">
            <a-checkbox :checked="record.isPrintContract"></a-checkbox>
          </template>
          <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
            <template v-for="photo in record.filePath.split(',')">
              <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer :key="index + '-' + photo" />
            </template>
          </div>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
    <!-- 操作日志弹窗 -->
    <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose :maskClosable="false" @cancel="reportHandleCancel" :width="800" centered>
      <div class="projectackend">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
        </a-table>
      </div>
      <template slot="footer">
        <a-button type="primary" @click="Timenodes" style="float: left">{{ buttonname }}</a-button>
        <a-button type="primary" @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
import { mapState } from "vuex";
import { ordermodifylist } from "@/services/mkt/CustInfoNew";
import { proorderlog } from "@/services/projectDisptch";
const timeFilter = function (value) {
  let count = 1;

  if (value.indexOf("天") > 0) {
    count = 24;
  }
  var num_ = value.match(/^[a-z|A-Z]+/gi);
  console.log(num_);
};
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    className: "userStyle",
    width: 45,
    fixed: "left",
    scopedSlots: { customRender: "num" },
    // customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "生产编号",
    dataIndex: "pdct_NAME_",
    className: "userStyle",
    width: 125,
    ellipsis: true,
    fixed: "left",
    align: "left",
    scopedSlots: { customRender: "pdct_NAME_" },
  },
  {
    title: "订单类型",
    customRender: (text, record, index) =>
      `${record.isReOrder == 0 ? "新单" : record.isReOrder == 1 ? "返单" : record.isReOrder == 2 ? "返单更改" : ""}`,
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 95,
  },
  // {
  //   title: "交期",
  //   dataIndex: "urgentType_",
  //   width: 50,
  //   ellipsis: true,
  //   align: "left",
  //   sorter: (a, b) => {
  //     let an = a.urgentType_.indexOf("天") > 0 ? parseInt(a.urgentType_) * 24 : parseInt(a.urgentType_);
  //     let bn = b.urgentType_.indexOf("天") > 0 ? parseInt(b.urgentType_) * 24 : parseInt(b.urgentType_);
  //     return an - bn;
  //   },
  // },
  {
    title: "交期",
    dataIndex: "delDate_",
    className: "userStyle",
    ellipsis: true,
    width: 130,
    align: "left",
    // fixed:'left',
  },
  {
    title: "层数",
    dataIndex: "layer_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 40,
    sorter: (a, b) => {
      return a.layer_ - b.layer_;
    },
  },
  {
    title: "板厚",
    dataIndex: "boardThickness",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 45,
  },
  {
    title: "面积",
    dataIndex: "boardArea",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 45,
    sorter: (a, b) => {
      return a.boardArea - b.boardArea;
    },
    customRender: function (num) {
      return num.toFixed(2);
    },
  },

  {
    title: "板材信息",
    dataIndex: "board_",
    className: "userStyle",
    align: "left",
    width: 110,
    ellipsis: true,
    sorter: (a, b) => {
      return a.board_.localeCompare(b.board_);
    },
  },
  {
    title: "表面工艺",
    dataIndex: "surfaceFinishStr",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 160,
  },
  {
    title: "客户型号",
    dataIndex: "pcbFileName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 250,
  },
  {
    title: "尺寸",
    dataIndex: "boardSize",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 90,
    sorter: (a, b) => {
      return eval(a.boardSize.replace(/x/g, "*")) - eval(b.boardSize.replace(/x/g, "*"));
    },
  },

  // {
  //   title: "客编",
  //   dataIndex: "mbId",
  //   width: 60,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "状态",
  //   dataIndex: "orderState_",
  //   align: "left",
  //   ellipsis: true,
  //   width: 80,
  //   scopedSlots: { customRender: "customRender" },
  //   sorter: (a, b) => {
  //     return a.orderState_.localeCompare(b.orderState_);
  //   },
  // },
  {
    title: "订单状态",
    dataIndex: "orderState_",
    className: "userStyle",
    width: 75,
    ellipsis: true,
    align: "left",
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    className: "userStyle",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "接单时间",
    dataIndex: "inDate_",
    className: "userStyle",
    ellipsis: true,
    width: 130,
    align: "left",
    sorter: (a, b) => {
      return a.inDate_.localeCompare(b.inDate_);
    },
  },
  {
    title: "剩余时间(h)",
    dataIndex: "timeOut",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "预审人",
    dataIndex: "preName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "报价人",
    dataIndex: "checkName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "拼板",
    dataIndex: "pinBanType_",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 50,
    sorter: (a, b) => {
      return eval(a.pinBanType_.replace(/x/g, "*")) - eval(b.pinBanType_.replace(/x/g, "*"));
    },
  },
  {
    title: "加工工厂",
    dataIndex: "orderChannel",
    className: "userStyle",
    width: 100,
    ellipsis: true,
    align: "left",
  },
  {
    title: "版本",
    dataIndex: "proRev",
    className: "userStyle",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  //  {
  //   title: "操作",
  //   align:'center',
  //   scopedSlots: { customRender: 'labelUrl' },
  //   width:50,
  //   className:'userStyle noCopy'
  //  },
  // {
  //   title: "合同编号",
  //   dataIndex: "businessOrderNo",
  //   className:'userStyle',
  //   align: "left",
  //   ellipsis: true,
  //   width: 120,
  //  },

  // {
  //   title: "文件名",
  //   ellipsis: true,
  //   width: 150,
  //   align: "left",
  //   scopedSlots: { customRender: "pcbFileName" },
  // },
];
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "25%",
    align: "left",
  },
  {
    title: "图片",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  components: {
    RightCopy,
  },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    yScroll: {
      type: Number,
      require: true,
      default: 0,
    },
    xScroll: {
      type: Number,
      require: true,
      default: 0,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            current: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
          selectedRows: {},
        };
      },
    },
  },
  name: "LeftTable",
  data() {
    return {
      buttonname: "显示全部节点",
      copylabordata: [],
      laborcolumns: [
        {
          title: "序号",
          align: "center",
          dataIndex: "index",
          key: "index",
          width: 20,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "操作时间",
          align: "left",
          dataIndex: "createTime",
          width: 65,
        },
        {
          title: "操作人",
          align: "left",
          dataIndex: "userName",
          width: 30,
        },
        {
          title: "内容",
          align: "left",
          dataIndex: "content",
          width: 185,
        },
      ],
      dataSource4: [],
      xiuvisible: false,
      columns4,
      loading2: false,
      text: "",
      labordata: [],
      labordataVisible: false,
      showText: false,
      isDragging: false,
      startIndex: -1,
      shiftKey: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      menuData: {},
      selectedRowsData: [],
      paginationstyle: "",
      columns,
      selectedRowKeys: [],
      selectedRowID: [],
      activeClass: "smallActive",
      // let height= this.$refs.element.offsetHeight;
    };
  },
  watch: {
    dataSource: function () {
      var count = 0;
      this.dataSource.forEach(item => {
        var flag = 0;
        if (item.isBigCus == "是") {
          flag++;
        }
        if (item.quality == "优品") {
          flag++;
        }
        if (item.boardArea >= 2) {
          flag++;
        }
        if (item.reverseOrder_) {
          flag++;
        }
        if (item.isReverse == "1") {
          flag++;
        }
        if (item.isReverse == "2") {
          flag++;
        }
        if (flag > count) {
          count = flag;
        }
      });
      if (this.dataSource) {
        this.$nextTick(function () {
          let pauseCancelState = this.dataSource.some(val => val.pauseCancelState == 4) ? 30 : 0;
          let maxLength = 0;
          var longestChars = [];
          for (let i = 0; i < this.dataSource.length; i++) {
            let obj2 = this.dataSource[i].pdct_NAME_;
            if (obj2) {
              var [...chars] = obj2;
              if (chars.length > maxLength) {
                maxLength = chars.length;
                longestChars = obj2;
              }
            }
          }
          let obj = document.getElementsByClassName("tagNum");
          let arr = [];
          for (let i = 0; i < obj.length; i++) {
            arr.push(obj[i].children.length);
          }
          let result = -Infinity;
          arr.forEach(item => {
            if (item > result) {
              result = item;
            }
          });
          if (result == 0 && longestChars.length > 12) {
            this.columns[1].width = 125 + (longestChars.length - 12) * 6 + "px";
            this.columns[2].width = "95px";
            this.columns[3].width = "130px";
            this.columns[7].width = "110px";
            this.columns[8].width = "160px";
          }
          if (result >= 1 && longestChars.length > 12) {
            this.columns[1].width = 125 + (longestChars.length - 12) * 6 + result * 20 + pauseCancelState + "px";
            this.columns[2].width = 95 - result * 5 - (longestChars.length - 12) * 2 + "px";
            this.columns[3].width = 130 - result * 5 - (longestChars.length - 12) * 2 + "px";
            this.columns[7].width = 110 - result * 5 - (longestChars.length - 12) * 1 + "px";
            this.columns[8].width = 160 - result * 5 - (longestChars.length - 12) * 1 + "px";
          }
          if (result == 0 && longestChars.length <= 12) {
            this.columns[1].width = "125px";
            this.columns[2].width = "95px";
            this.columns[3].width = "130px";
            this.columns[7].width = "110px";
            this.columns[8].width = "160px";
          }
          if (result >= 1 && longestChars.length <= 12) {
            this.columns[1].width = 125 + result * 20 + pauseCancelState + "px";
            this.columns[2].width = 95 - result * 5 + "px";
            this.columns[3].width = 130 - result * 5 + "px";
            this.columns[7].width = 110 - result * 5 + "px";
            this.columns[8].width = 160 - result * 5 + "px";
          }
        });
      }
      if (count > 2) {
        this.activeClass = "Active";
      }
    },

    pagination: {
      handler(val) {
        console.log(val);
      },
    },
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  filters: {
    splitFilter(value) {
      return value.split("-")[1];
    },
  },
  methods: {
    Timenodes() {
      if (this.buttonname == "显示全部节点") {
        this.buttonname = "显示关键节点";
        this.labordata = this.copylabordata;
      } else if (this.buttonname == "显示关键节点") {
        this.buttonname = "显示全部节点";
        this.labordata = this.copylabordata.length ? this.copylabordata.filter(item => item.keyNo == 1) : [];
      }
    },
    xiudisplay(record) {
      this.loading2 = true;
      ordermodifylist(record.proOrderId)
        .then(res => {
          if (res.code) {
            this.dataSource4 = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
      this.xiuvisible = true;
    },
    reportHandleCancel() {
      this.labordataVisible = false;
      this.buttonname = "显示全部节点";
      this.xiuvisible = false;
    },
    OperationLog(proOrderId) {
      proorderlog(proOrderId).then(res => {
        if (res.code) {
          this.labordata = res.data.length ? res.data.filter(item => item.keyNo == 1) : [];
          this.copylabordata = JSON.parse(JSON.stringify(res.data));
          this.labordataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.proOrderId && this.selectedRowKeys.includes(record.proOrderId)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.$emit("assignOrderListChange", this.selectedRowKeys);
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].proOrderId);
      }
      this.selectedRowKeys = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeys[this.selectedRowKeys.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeys[0];
        })[0];
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeys;
        if (rowKeys.length > 0 && rowKeys.includes(record.proOrderId)) {
          rowKeys.splice(rowKeys.indexOf(record.proOrderId), 1);
        } else {
          rowKeys.push(record.proOrderId);
        }
        this.selectedRowKeys = rowKeys;
        if (this.selectedRowKeys.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowKeys = [record.proOrderId];
        }
      }

      this.$emit("assignOrderListChange", this.selectedRowKeys);
      this.shiftKey = false;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.proOrderId)) {
              rowKeys.splice(rowKeys.indexOf(record.proOrderId), 1);
            } else {
              rowKeys.push(record.proOrderId);
            }
            this.selectedRowKeys = rowKeys;
            this.$emit("assignOrderListChange", this.selectedRowKeys);
          },
          contextmenu: e => {
            e.preventDefault();
            console.log("11", record);
            this.menuData = record;
          },
        },
      };
    },
    handleTableChange(pagination, filter, sorter) {
      this.$emit("tableChange", pagination, filter, sorter);
    },
    down(val) {
      if (val.pcbFilePath) {
        window.location.href = val.pcbFilePath;
      } else {
        this.$message.error("文件不存在");
      }
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
    // 检查是否存在操作列
    const hasActionColumn = this.columns.some(column => column.title === "操作");

    // 如果没有操作列，并且用户不是wf15，则添加操作列
    if (!hasActionColumn && this.user.userName != "wf15") {
      this.columns.push({
        title: "操作",
        align: "center",
        scopedSlots: { customRender: "labelUrl" },
        width: 50,
        className: "userStyle noCopy",
      });
    }

    // 如果用户是wf15且工厂ID为38，则移除操作列
    if (this.user.userName == "wf15" && this.user.factoryId == 38) {
      this.columns = this.columns.filter(column => column.title !== "操作");
    }
    //this.paginationstyle = document.getElementsByClassName('mintable')[0].children[0].children[0].childNodes[1]
  },
};
</script>

<style lang="less" scoped>
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-content {
  border: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
.xiu {
  /deep/.ant-table-row-cell-ellipsis,
  .ant-table-row-cell-ellipsis .ant-table-column-title {
    overflow: overlay;
    white-space: normal;
    text-overflow: inherit;
  }
}
.projectackend {
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table {
    tr.ant-table-row-hover td {
      background: #dfdcdc !important;
    }
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
/deep/.userStyle {
  user-select: none !important;
}
.leftTab {
  height: 100%;
  position: relative;
  //height:764px;
  // max-width: 1253px;
  // /deep/.mintable{
  //   .ant-table-body{
  //       min-height:724px;
  //       // .ant-table-fixed-left table{
  //       //   width:500px!important;
  //       // }
  //     }
  // }
  /deep/.ant-table-selection-column {
    padding: 0 !important;
  }

  /deep/ .ant-spin-nested-loading {
    position: initial !important;
  }
  /deep/.ant-spin-container {
    position: initial !important;
  }
  /deep/ .ant-pagination {
    position: absolute;
    float: left;
    margin: 10px 0 0 10px;
  }
  .peopleTag {
    margin: 0;
    padding: 0;
    width: 24px;
    border-radius: 12px;
    background: #2d221d;
    border-color: #2d221d;
    color: #ff9900;
    text-align: center;
    margin-left: 2px;
  }

  /deep/.rowBackgroundColor {
    background: #dcdcdc !important;
  }
}
</style>
