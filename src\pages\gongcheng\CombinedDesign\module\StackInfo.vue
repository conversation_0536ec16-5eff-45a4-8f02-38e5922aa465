<template>
    <div class="box">
        <div style="border-bottom: 1px solid #ddd;" >
        <a-table
          :columns="columns1"
          :dataSource="stackUpOutputs"
          :pagination=false
          :scroll="{ y: 500,x:1520}"
          :rowKey="(record,index)=>{return index}"
          :row-class-name="addClass"
          bordered
        >
        <span slot="stackUpCoreDS_" slot-scope="record">
          <a-checkbox  v-if="['Core', 'GB', '补强', '金属基'].includes(record.stackUpMTR_)" v-model="record.stackUpCoreDS_" disabled/>
        </span>
      </a-table>
      </div>
    </div>
</template>
<script>
const  columns1=[
    {
      dataIndex: "index",        
      key: 'index',
      width: 40,
      align: 'center',       
      customRender: (text,record,index) => `${index+1}`,
      className: 'index_'
    },
    {
      title: "层号",
      dataIndex: 'stackUpLayerNo_',
      width: 55,
      align: "center",
      className: "material_bg"
    },
    {
      title: "物料",
      dataIndex: 'stackUpMTR_',
      width: 65,
      align: "center",
      className: "material_bg"
    },
    {
      title: "型号",
      dataIndex: 'stackUpMTRType_',
      width: 115,
      align: "center",
      className: "material_bg"
    },
    {
      title: "介质",
      dataIndex: 'stackUpMTRFoil_',
      width: 200,
      align: "center",
      ellipsis: true,
      className: "material_bg"
    },
    {
      title: "含铜",
      width: 55,
      className: 'M_bg',
      scopedSlots: { customRender: 'stackUpCoreDS_' },
      align: "center"
    },
    {
      title: "物料厚度(mil)",
      dataIndex: "stackUpThichnessORG_",
      width: 100,
      align: "center",
      className: "M_bg"

    },
    {
      title: "成品厚度(mil)",
      dataIndex: "stackUpThichnessMIL_",
      width: 100,
      align: "center",
      className: "M_bg"

    },
    {
      title: "成品厚度(mm)",
      dataIndex: "stackUpThichnessMM_",
      width: 100,
      align: "center",
      className: 'M_bg'

    },
    {
      title: "残铜率",
      dataIndex: 'stackUpCTLMI_',
      width: 70,
      className: 'M_bg',
      align: "center"
    },
    {
      title: "T/B",
      dataIndex: 'stackUpCUTB_',
      width: 55,
      className: 'M_bg',
      align: "center"
    },
    {
      title: "DK",
      dataIndex: "stackUpDK_",
      width: 90,
      className: 'M_bg',
      align: "center"
    },
    {
      title: "大料尺寸",
      dataIndex: "stackUpPNLSize_",
      width: 90,
      className: 'M_bg',
      align: "center"
    },
    {
      title: "T1",
      dataIndex: "stackUpT1_",
      width: 55,
      className: 'M_bg',
      align: "center"
    },
  ]
export default {
  name: 'StackInfo',
  props:['stackUpOutputs'],
  data () {
    return {
        columns1,
    }
  },
  methods: {
    addClass(record, index) {
      switch (record.stackUpMTR_) {
        case 'OZ':
          return 'OZ';
        case 'Core':
          return "Core";
        case 'PP':
          return 'PP';
        default:
            break;
      }
    }, 
  },
  mounted () {
    
  }
}
</script>
<style lang="less" scoped>

.box{
    /deep/.ant-table-thead > tr > th, .ant-table-tbody > tr > td {
            padding: 4px 6px;
            overflow-wrap: break-word;
            background-color:#e5e5e5  !important;
        }
    /deep/ .ant-table-tbody .M_bg{
    background: #8FBC8B!important;
  }
    /deep/ .ant-table-tbody > tr > td {
            padding: 4px 6px;
            font-size: 12px;
            font-weight: 500;
            overflow-wrap: break-word;
        }
    /deep/ .ant-table-tbody {
    .OZ {
        .material_bg {
            background: #F4A460 !important;
        }
    }
    .Core {
        .material_bg {
             background: #F0E68C !important;
            }
    }
    .PP {
        .material_bg {
            background: #9ACD32 !important;
        }
    }
    .material_bg {
        .ant-select-selection {
            background:none ;
        }
    }
    .index_{
      background-color: rgb(165, 165, 165) !important;
    }
    }
}

</style>