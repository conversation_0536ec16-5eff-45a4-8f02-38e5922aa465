const convertToChineseNum = amount => {
  const capitalNumbers = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  const unit = ["", "拾", "佰", "仟"];
  const bigUnit = ["", "万", "亿", "兆"]; // 支持更大单位
  const smallUnit = ["角", "分"];

  // 将数字转换为字符串，并保留两位小数
  const numStr = amount.toFixed(2);
  const numArr = numStr.split("."); // 分割整数部分和小数部分
  let chineseStr = "";

  // 处理整数部分
  const processSegment = (segment, unitIndex) => {
    let result = "";
    for (let i = 0; i < segment.length; i++) {
      const digit = parseInt(segment[i]);
      const unitIndexInner = segment.length - 1 - i;
      if (digit !== 0) {
        result += capitalNumbers[digit] + unit[unitIndexInner];
      } else if (result.length > 0 && result[result.length - 1] !== "零") {
        result += "零";
      }
    }
    if (result.endsWith("零")) {
      result = result.slice(0, -1);
    }
    if (result) {
      result += bigUnit[unitIndex];
    }
    return result;
  };

  // 按“万”和“亿”分割整数部分
  const integerPart = numArr[0].padStart((((numArr[0].length - 1) / 4) | 0) * 4 + 4, "0");
  const segments = [];
  for (let i = 0; i < integerPart.length; i += 4) {
    segments.push(integerPart.slice(i, i + 4));
  }

  // 处理每个段
  for (let i = segments.length - 1; i >= 0; i--) {
    const segmentResult = processSegment(segments[i], segments.length - 1 - i);
    if (segmentResult) {
      chineseStr = segmentResult + chineseStr;
    }
  }

  // 如果整数部分为空，则添加“零”
  if (!chineseStr) {
    chineseStr = "零";
  }
  chineseStr += "元";

  // 处理小数部分
  for (let j = 0; j < numArr[1].length; j++) {
    const digit = parseInt(numArr[1][j]);
    if (digit !== 0) {
      chineseStr += capitalNumbers[digit] + smallUnit[j];
    }
  }

  // 清理多余的“零”
  return amount ? chineseStr.replace(/零+/g, "零").replace(/零(元|角)/g, "$1") : "零元";
};

export default convertToChineseNum;
