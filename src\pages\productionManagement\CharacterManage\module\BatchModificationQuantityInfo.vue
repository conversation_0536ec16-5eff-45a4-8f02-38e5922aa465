<!-- 车间管理-字符管理-修改数量 -->
<template>
  <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 12}" >
    <a-form-item label="PNL数">
      <a-input   v-model='quantity'  :autoFocus="autoFocus"/>
    </a-form-item>
    
  </a-form>
</template>

<script>
export default {
    name:'BatchModificationQuantityInfo',
    props:[''],
  data() {
    return {
        quantity:'',
        autoFocus:true
    };
  },
  methods: {  
 
  },
};
</script>