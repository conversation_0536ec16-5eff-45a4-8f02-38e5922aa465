<!-- BQ销售合同 -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="pdfDombq" style="font-size: 12px; color: black">
      <div style="width: 100%; display: flex">
        <div style="width: 20%">
          <img src="@/assets/img/bq.jpg" v-if="BQsalesdata.value_1.indexOf('深圳') != -1" />
          <img src="@/assets/img/jdlogo.png" v-else-if="BQsalesdata.value_1.indexOf('重庆') != -1" style="height: 70px" />
          <img src="@/assets/img/bqlogo.png" v-else style="height: 70px" />
        </div>
        <div style="text-align: center; width: 60%">
          <div style="font-size: 30px; font-weight: bold">{{ BQsalesdata.value_3 }}</div>
          <div style="font-size: 26px; font-weight: bold">订货合同书</div>
          <br />
        </div>
        <div style="margin-top: 15px; width: 20%">
          <table style="border-top: 1px solid black; border-left: 1px solid black; float: right">
            <tr>
              <td style="padding-left: 1ch; min-width: 75px">合同编号:</td>
              <td style="min-width: 100px; padding-left: 1ch">{{ BQsalesdata.orderNo_ }}</td>
            </tr>
            <tr>
              <td style="padding-left: 1ch">签订日期:</td>
              <td style="padding-left: 1ch">{{ BQsalesdata.date_ }}</td>
            </tr>
            <tr>
              <td style="padding-left: 1ch">签订地点:</td>
              <td style="padding-left: 1ch">{{ BQsalesdata.custL_ }}</td>
            </tr>
          </table>
        </div>
      </div>
      <a-divider />
      <div style="margin-top: 20px; line-height: 3ch; display: flex">
        <div style="width: 59%">
          <div>
            承揽人:&nbsp; <span class="Underline"> &nbsp;{{ BQsalesdata.value_1 }} </span>
          </div>
          <div>
            地&nbsp; &nbsp;址:&nbsp; <span class="Underline"> &nbsp;{{ BQsalesdata.value_2 }} </span>
          </div>
          <div style="display: flex; width: 100%">
            <div style="width: 35%">电&nbsp; &nbsp;话:&nbsp; &nbsp; <span class="Underline1">0755-29606089</span></div>
            <div>传&nbsp; &nbsp;真:&nbsp; &nbsp; <span class="Underline1">0755-28235429</span></div>
          </div>
        </div>
        <div style="width: 41%">
          <div>
            定作人:&nbsp; <span :class="BQsalesdata.address_ ? 'Underline' : 'Underline3'">{{ BQsalesdata.address_ }}</span>
          </div>
          <div>
            地&nbsp; &nbsp;址:&nbsp; <span :class="BQsalesdata.factoryAddR_ ? 'Underline' : 'Underline3'">{{ BQsalesdata.factoryAddR_ }}</span>
          </div>
          <div style="display: flex">
            <div style="width: 50%">
              电&nbsp; &nbsp;话:&nbsp; &nbsp; <span :class="BQsalesdata.tel_ ? 'Underline1' : 'Underline2'">{{ BQsalesdata.tel_ }}</span>
            </div>
            <div>
              传&nbsp; &nbsp;真:&nbsp; &nbsp; <span :class="BQsalesdata.fax_ ? 'Underline1' : 'Underline2'">{{ BQsalesdata.fax_ }}</span>
            </div>
          </div>
        </div>
      </div>
      <div style="line-height: 3.5ch; display: flex">
        <div style="width: 29%">
          <div>(1).加工名称、规格、数量以及交货期(本条款手写无效):</div>
          <div>
            开户名称：<span style="font-weight: bold">{{ BQsalesdata.value_3 }}</span>
          </div>
        </div>
        <div style="width: 30%">
          <div>
            订单编号:&nbsp; &nbsp;<span :class="BQsalesdata.contractNO ? 'Underline1' : 'Underline2'">{{ BQsalesdata.contractNO }}</span>
          </div>
          <div>
            开户账号:&nbsp; &nbsp;<span style="font-weight: bold">{{ BQsalesdata.bankAccount_ }}</span>
          </div>
        </div>
        <div style="width: 35%">
          <div>
            联系人:&nbsp; <span :class="BQsalesdata.link_ ? 'Underline' : 'Underline3'">{{ BQsalesdata.link_ }} </span>
          </div>
          <div>
            开户银行：<span style="font-weight: bold">{{ BQsalesdata.openBank_ }}</span>
          </div>
        </div>
        <div>
          <div></div>
          <br />
          <div @click="addcontract" style="color: #4b82ac; float: right" v-if="showadd">
            点击添加 <a-icon style="font-size: 16px; top: 2px; position: relative" type="plus-circle"></a-icon>
          </div>
        </div>
      </div>
      <div>
        <table style="text-align: center; width: 100%; border-top: 1px solid black; border-left: 1px solid black">
          <thead>
            <tr>
              <td>序号</td>
              <td>生产编号</td>
              <td>文件名</td>
              <td>单位</td>
              <td>数量</td>
              <td>单价</td>
              <td>表面费</td>
              <td>菲林费</td>
              <td>工程费</td>
              <td>模具费</td>
              <td>飞针费</td>
              <td>测试架</td>
              <td>加急费</td>
              <td>其他费</td>
              <td>合计</td>
              <td>交期</td>
              <td>面积(㎡)</td>
              <td>发货地址</td>
              <td style="width: 40px" v-if="showadd">操作</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in BQsalesdata.price" :key="index">
              <td>{{ item.no }}</td>
              <td>{{ item.orderNo_ }}</td>
              <td>{{ item.name }}</td>
              <td>{{ item.bType }}</td>
              <td>{{ item.qty }}</td>
              <td>{{ item.pcs }}</td>
              <td>{{ item.surface }}</td>
              <td>{{ item.film }}</td>
              <td>{{ item.eng }}</td>
              <td>{{ item.mould }}</td>
              <td>{{ item.fly }}</td>
              <td>{{ item.test }}</td>
              <td>{{ item.urgent }}</td>
              <td>{{ item.other }}</td>
              <td>{{ item.total }}</td>
              <td>{{ item.custdate }}</td>
              <td>{{ item.area }}</td>
              <td style="max-width: 200px">{{ item.delAddr_ }}</td>
              <td style="width: 40px" v-if="showadd">
                <a-tooltip placement="top" title="删除当前行数据">
                  <a-icon @click="delclick(item.id)" style="font-size: 14px; color: #4b82ac; padding-left: 5px" type="close-circle"></a-icon>
                </a-tooltip>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr>
              <td colspan="8">{{ BQsalesdata.specialrequirements }}</td>
              <td colspan="8" style="font-weight: bold">合计金额(大写) : 人民币{{ convertToChineseNum(amountto) }}</td>
              <td colspan="3" style="font-weight: bold">合计:{{ amountto }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div>(2)加工描述</div>
      <div>
        <table style="text-align: center; width: 100%; border-top: 1px solid black; border-left: 1px solid black">
          <thead>
            <tr>
              <td>序号</td>
              <td>生产编号</td>
              <td>层数</td>
              <td>是否新单</td>
              <td>板厚</td>
              <td>规格(mm*mm)</td>
              <td>表面处理</td>
              <td>备注</td>
              <td>业务员</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in BQsalesdata.orderPar" :key="index">
              <td>{{ item.no }}</td>
              <td>{{ item.proOrderNo }}</td>
              <td>{{ item.boardLayers }}</td>
              <td>{{ item.reOrder }}</td>
              <td>{{ item.boardThickness }}</td>
              <td>{{ item.size }}</td>
              <td>{{ item.surfaceFinish }}</td>
              <td style="max-width: 200px">{{ item.remark_ }}</td>
              <td>{{ item.functionary_ }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="line-height: 3.5ch; margin-top: 15px; display: flex">
        <div style="display: sticky; z-index: 99; position: relative">
          (1) 运费负担：承揽人
          <span style="width: 230px; display: inline-block"
            >(2) 发票类型:
            <span :class="BQsalesdata.invoiceType_ ? 'Underline1' : 'Underline2'" style="padding-left: 8px; font-size: 14px">{{
              BQsalesdata.invoiceType_
            }}</span></span
          >
          <span style="width: 140px; display: inline-block"
            >税率类型：<span :class="BQsalesdata.taxPercentage_ ? 'Underline4' : 'Underline5'" style="padding-left: 8px; font-size: 14px"
              >{{ BQsalesdata.taxPercentage_ }}
            </span></span
          >
          (3) 交货方式: 送货/快递 (4) 执行标准:{{ BQsalesdata.ipcLevel }} (5) 专用和附加识别标志：无<br />
          <span style="width: 230px; display: inline-block"
            >(6) 结算方式：
            <span :class="BQsalesdata.clearingForm ? 'Underline1' : 'Underline2'" style="padding-left: 40px; font-size: 14px">{{
              BQsalesdata.clearingForm
            }}</span></span
          >
          付款凭证仅为银行结算凭证或加盖有承揽人财务章的收据。逾期付款按月息2%计算。<br />
          (7) 如果定作人没有在本合同规定的发/到货日期后的10日内收到本合同项下的货物,应即书面通知承揽人,否则视为已收到本合同项下货物。<br />
          (8) 定作人委托承揽人加工的线路板版权应当是定作人合法持有的,如有侵犯第三方产权的行为,全部责任由定作人承担,与承揽方无关。<br />
          (9) 双方业务中所发传真等同原件具同等法律效力。<br />
          <span style="font-weight: bold">
            (10)
            定做人在收到本合同项下货物后,若有质量问题,应在收到货物后十天内以书面形式提出,期满未提出的视为合格,在上述期限内提出的质量异议双方应友好协商处理,
            若因产品质量问题导致定做人无法使用,承揽人应重新供货,定做人不要求补货时承揽人只承担有问题产品的PCB同额成本赔偿。<br />
            (11) 如产生争议,由双方协商,协商不成交由深圳市宝安区人民法院管辖.<br />
            (12) 特殊说明:因定作人未按约定支付货款而产生实际债权的费用(仲裁费、律师费、交通费等)由定作人承担。<br />
            (13) PCB板在真空包装情况下保质期为6个月(超过3个月贴片前须烤板:120℃*8H,超过6个月建议报废,不保证贴片质量)<br />
            (14) 订单交期从确认好工程EQ后开始计算 &nbsp;&nbsp;&nbsp; (15) 所有与产品相关的要求,标准,若与合同条款冲突,以合同为准<br />
          </span>
        </div>
        <div style="z-index: 0; position: relative; right: 85%; top: 218px; height: 141px">
          <img src="@/assets/img/bqht.png" v-if="BQsalesdata.value_1.indexOf('深圳') != -1" style="height: 150px" />
          <img src="@/assets/img/bqhtcd.png" v-if="BQsalesdata.value_1.indexOf('成都') != -1" style="height: 150px" />
          <img src="@/assets/img/bqhtbj.png" v-if="BQsalesdata.value_1.indexOf('北京') != -1" style="height: 150px" />
          <img src="@/assets/img/bqhtjd.png" v-if="BQsalesdata.value_1.indexOf('重庆') != -1" style="height: 150px" />
          <img src="@/assets/img/bqhthz.png" v-if="BQsalesdata.value_1.indexOf('杭州') != -1" style="height: 150px" />
          <img src="@/assets/img/bqhtwh.png" v-if="BQsalesdata.value_1.indexOf('武汉') != -1" style="height: 150px" />
        </div>
      </div>
      <div style="margin-top: 20px; display: flex">
        <div style="z-index: 99; display: flex; position: relative">
          <div style="width: 720px; height: 80px">
            <div>承揽人:{{ BQsalesdata.value_1 }}(盖章)</div>
            <div>
              代表签字:&nbsp;&nbsp;<span :class="BQsalesdata.factorydelegate_ ? 'Underline4' : 'Underline5'">{{ BQsalesdata.factorydelegate_ }}</span>
            </div>
          </div>
          <div>
            <div>定作人:(盖章)</div>
            <div>代表签字:</div>
          </div>
        </div>
      </div>
    </div>
    <a-modal title="合同数据添加" :visible="modalvisible" @ok="handleOk" @cancel="handleCancel" centered :width="800">
      <a-row>
        <a-col :span="10">
          <a-form-item label="订单号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入订单号进行查询" v-model="OrderNo" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="客户型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入客户型号进行查询" v-model="PcbFileName" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" style="margin-top: 2px" @click="queryclick">查询</a-button>
        </a-col>
      </a-row>
      <a-table
        :columns="columns"
        :row-selection="{ selectedRowKeys: selectedRowKeysind, onChange: onSelectChange, columnWidth: 25 }"
        :pagination="false"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ y: 300 }"
        :dataSource="datasource"
        :rowClassName="isRedRow"
      >
        <!-- :customRow="onClickRow" -->
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import html2pdf from "html2pdf.js";
import { verifyPageList, deletecontractno, contractNo } from "@/services/mkt/OrderReview.js";
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["BQsalesdata", "salescustno", "ContractNoSech", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      selectedRowKeys: [],
      showadd: true,
      selectedRowKeysind: [],
      PcbFileName: "",
      datasource: [],
      OrderNo: "",
      modalvisible: false,
      ids: [],
      printObj1: {
        id: "pdfDombq", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 35,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "订单号",
          align: "left",
          ellipsis: true,
          width: 80,
          dataIndex: "orderNo",
        },
        {
          title: "客户代码",
          dataIndex: "custNo",
          align: "left",
          ellipsis: true,
          width: 50,
        },

        {
          title: "订单类型",
          dataIndex: "reOrder",
          align: "left",
          ellipsis: true,
          width: 50,
          customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
        },
      ],
    };
  },
  mounted() {
    this.amountto = 0;
    this.ids = [];
    this.printObj1.closeCallback = this.closePrintTool;
    this.BQsalesdata.price.forEach(item => {
      if (this.ids.indexOf(item.id) == -1) {
        this.ids.push(item.id);
      }
    });
    this.showadd = !this.BQsalesdata.price.some(ite => ite.status == 30);
    this.$nextTick(() => {
      for (let index = 0; index < this.BQsalesdata.price.length; index++) {
        if (this.BQsalesdata.price[index].total && this.BQsalesdata.price[index].total != "/") {
          this.amountto += Number(this.BQsalesdata.price[index].total);
        }
      }
      this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
    });
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
      this.showadd = !this.BQsalesdata.price.some(ite => ite.status == 30);
    },
    printpdf() {
      document.title = this.BQsalesdata.pcbFileName;
      this.showadd = false;
    },
    delclick(id) {
      this.ids = [];
      this.BQsalesdata.price.forEach(item => {
        if (this.ids.indexOf(item.id) == -1) {
          this.ids.push(item.id);
        }
      });
      deletecontractno(id).then(res => {
        if (res.code) {
          this.ids.forEach(item => {
            if (item == id) {
              this.ids.splice(this.ids.indexOf(item), 1);
            }
          });
          this.$message.success("删除成功");
          this.$emit("BQsalescontract", this.ids);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeys = rowKeys;
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysind = selectedRowKeys;
      this.selectedRowKeys = [];
      selectedRows.forEach(item => {
        this.selectedRowKeys.push(item.id);
      });
    },
    queryclick() {
      if (!this.OrderNo && !this.PcbFileName) {
        this.$message.error("请输入订单号或客户型号进行查询");
        return;
      }
      let params = {
        PageIndex: 1,
        PageSize: 20,
        CustNo: this.salescustno,
        ContractNoSech: this.ContractNoSech,
      };
      if (this.OrderNo) {
        params.OrderNo = this.OrderNo;
      }
      if (this.PcbFileName) {
        params.PcbFileName = this.PcbFileName;
      }
      verifyPageList(params).then(res => {
        if (res.code) {
          this.datasource = res.data.items;
          if (this.datasource.length == 0) {
            this.$message.error("未查询到相关订单");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    addcontract() {
      this.modalvisible = true;
      this.selectedRowKeys = [];
      this.datasource = [];
      this.OrderNo = "";
      this.PcbFileName = "";
      this.selectedRowKeysind = [];
    },
    handleOk() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择订单");
        return;
      }
      this.ids = [...this.ids, ...this.selectedRowKeys];
      contractNo(this.ids).then(res => {
        if (res.code) {
          this.$emit("BQsalescontract", this.ids);
        } else {
          this.$message.error(res.message);
        }
      });
      this.modalvisible = false;
    },
    handleCancel() {
      this.modalvisible = false;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getsalesPdf() {
      this.showadd = false;
      setTimeout(() => {
        htmlToPdfa3("pdfDombq", this.BQsalesdata.pcbFileName);
        this.showadd = !this.BQsalesdata.price.some(ite => ite.status == 30);
      }, 500);
    },
    getpdf(dom, name) {
      const element = document.getElementById(dom);
      const opt = {
        margin: 0.5,
        filename: name + ".pdf",
        image: { type: "jpeg", quality: 0.9 },
        html2canvas: { scale: 1 },
        jsPDF: {
          unit: "in",
          format: "a4",
          orientation: "landscape",
        },
        pagebreak: { mode: ["avoid-all", "css", "legacy"], after: ".sectionbreak" },
      };
      html2pdf().set(opt).from(element).save();
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 866px;
  right: 26px;
}
/deep/.rowBackgroundColor {
  background: #dcdcdc !important;
}
/deep/.ant-table-row-selected {
  background: #dcdcdc !important;
}
/deep/.ant-table-tbody > tr.ant-table-row-selected td {
  background: #dcdcdc;
}
/deep/.ant-table {
  border: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-thead > tr > th {
  padding: 4px 4px;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 4px 4px !important;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
  color: #000000;
}
.Underline {
  position: relative;
  display: inline-block;
}

.Underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0; /* 下划线距离文字底部的距离 */
  width: 400px; /* 下划线宽度 */
  height: 1px; /* 下划线高度 */
  background-color: black; /* 下划线颜色 */
}
.Underline3 {
  position: relative;
  display: inline-block;
}

.Underline3::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 400px; /* 下划线宽度 */
  height: 1px; /* 下划线高度 */
  background-color: black; /* 下划线颜色 */
}
.Underline1 {
  position: relative;
  display: inline-block;
}
.Underline1::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0; /* 下划线距离文字底部的距离 */
  width: 150px; /* 下划线宽度 */
  height: 1px; /* 下划线高度 */
  background-color: black; /* 下划线颜色 */
}
.Underline2 {
  position: relative;
  display: inline-block;
}
.Underline2::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 150px; /* 下划线宽度 */
  height: 1px; /* 下划线高度 */
  background-color: black; /* 下划线颜色 */
}
.Underline4 {
  position: relative;
  display: inline-block;
}
.Underline4::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0; /* 下划线距离文字底部的距离 */
  width: 75px; /* 下划线宽度 */
  height: 1px; /* 下划线高度 */
  background-color: black; /* 下划线颜色 */
}
.Underline5 {
  position: relative;
  display: inline-block;
}
.Underline5::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 75px; /* 下划线宽度 */
  height: 1px; /* 下划线高度 */
  background-color: black; /* 下划线颜色 */
}
/deep/.ant-divider-horizontal {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: -11px 0;
  background: black;
}
.agreement {
  padding-bottom: 20px;
  position: relative;
  z-index: 99;
  div {
    padding-top: 10px;
  }
}
.tableclass {
  border: 1px solid black;
  margin-top: 10px;
  margin-bottom: 10px;
  td {
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding-left: 7px;
  }
  tbody {
    tr {
      height: 24px;
    }
  }
}
table > tr > td {
  border-bottom: 1px solid black;
  border-right: 1px solid black;
}
table > thead > tr > td {
  border-bottom: 1px solid black;
  border-right: 1px solid black;
}
table > tbody > tr > td {
  border-bottom: 1px solid black;
  border-right: 1px solid black;
}
.pdfDom1 {
  padding: 25px;
  height: 800px;
  overflow: auto;
}
</style>
