<!-- 车间管理-成型管理-查询 -->
<template>
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="订单编号">
      <a-input allowClear  v-model='OrderNumber' placeholder="请输入订单编号" :autoFocus="autoFocus" @keyup.enter="keyupEnter1"/>
    </a-form-item>
  </a-form>
</template>

<script>
export default {
    name:'QueryInfo',
    
  data() {
    return {
      OrderNumber:'',
      autoFocus:true
    };
  },
  methods: {  
   keyupEnter1(){
      this.$emit('keyupEnter1')
  }
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-form-item{
  margin-bottom: 0;
}
</style>