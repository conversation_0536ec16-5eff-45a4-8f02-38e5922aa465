<template>
    <div>
        <a-button @click="baochun" >保存</a-button>
        <a-button @click="editBasicInfo"  v-show="type=='1'&&tabID==1" style="margin-left:5px" v-if="checkPermission('MES.MarketModule.ContractManage.ContractManageChange')">编辑</a-button>
        <a-button @click="cancel"  v-show="type=='2'&&tabID==1" style="margin-left:5px" v-if="checkPermission('MES.MarketModule.ContractManage.ContractManageChange')">取消编辑</a-button>
        <a-spin :spinning="spinning">
            <a-descriptions  bordered :column="column">
                <a-descriptions-item label="公司名称" :span="2">
                    <span >{{contractData.name_}}</span>
                </a-descriptions-item>
                <a-descriptions-item label="供应商编号" >
                    <span >{{contractData.factoryCode_}}</span>
                </a-descriptions-item>
                <a-descriptions-item label="合作阶段" >
                    <span >{{contractData.cooperation_}}</span>
                </a-descriptions-item>
                <a-descriptions-item label="签约主体" >
                    <span  v-if="type=='1'">{{contractData.signingSubject}}</span>
                    <a-input type="text" style="width: 180px;" v-model="contractData.signingSubject" v-else/>
                </a-descriptions-item>
                <a-descriptions-item label="合作开始时间" >
                    <span   v-if="type=='1'">{{contractData.contractStartDate}}</span>
                    <a-date-picker   v-model="contractData.contractStartDate"  @change="onChangeTime" v-else />
                </a-descriptions-item>
                <a-descriptions-item label="合作到期时间" >
                    <span   v-if="type=='1'">{{contractData.contractExpireDate}}</span>
                    <a-date-picker   v-model="contractData.contractExpireDate"  @change="onChangeTime1" v-else />
                </a-descriptions-item>
                <a-descriptions-item label="付款时间" >
                    <span   v-if="type=='1'">{{contractData.paymentDate}}</span>
                    <a-date-picker   v-model="contractData.paymentDate"  @change="onChangeTime2" v-else />
                </a-descriptions-item>
            </a-descriptions>
        </a-spin>


        <a-button @click="visibleMS"  style="margin: 10px 0" v-if="checkPermission('MES.MarketModule.ContractManage.ContractFinanceAdd')">增加财务信息</a-button>
        <standard-table
                rowKey="id"
                :columns="slabColumns"
                :dataSource="slabData"
                :pagination="false"
        >
            <div slot="action" slot-scope="{ record }">
                <template>
                    <a href="javascript:;" @click="compile(record)" v-if="checkPermission('MES.MarketModule.ContractManage.ContractFinanceChange')">编辑</a>
                    <a-divider type="vertical" />
                    <a-popconfirm
                            title="确定要删除吗？"
                            @confirm="handleDelBoard(record.id)"
                    >
                        <a href="javascript:;" v-if="checkPermission('MES.MarketModule.ContractManage.ContractFinanceDel')">删除</a>
                    </a-popconfirm>
                </template>
            </div>
        </standard-table>

        <a-modal
                title="提示"
                :visible="visibleM"
                :confirm-loading="confirmLoading"
                @ok="handleOk"
                @cancel="handleCancel"
        >
            <a-form layout="horizontal">

                <a-form-item
                        label="银行账号"
                        :labelCol="{ span: 6 }"
                        :wrapperCol="{ span: 12, offset: 1 }"
                >
                    <a-input
                            v-model="form.bankAccount"
                            placeholder="银行账号"
                    />
                </a-form-item>
                <a-form-item
                        label="开户行"
                        :labelCol="{ span: 6 }"
                        :wrapperCol="{ span: 12, offset: 1 }"
                >
                    <a-input
                            v-model="form.bank"
                            placeholder="开户行"
                    />
                </a-form-item>
                <a-form-item
                        label="结算周期"
                        :labelCol="{ span: 6 }"
                        :wrapperCol="{ span: 12, offset: 1 }"
                >
                    <a-input
                            v-model="form.settlementPeriod"
                            placeholder="结算周期"
                    />
                </a-form-item>
                <a-form-item
                        label="财务对账人"
                        :labelCol="{ span: 6 }"
                        :wrapperCol="{ span: 12, offset: 1 }"
                >
                    <a-input
                            v-model="form.financiaReconcilor"
                            placeholder="财务对账人"
                    />
                </a-form-item>
            </a-form>
        </a-modal>

    </div>
</template>

<script>
    import StandardTable from "@/components/table/StandardTable";
    import {contractSave,bankAdd,Amend,bankget,bankChaxun,bankDel,allowAmend} from "@/services/contract/index";
    import {checkPermission} from "../../../utils/abp";
    const   slabColumns=[
        {
            title:'银行账号',
            dataIndex:'bankAccount'
        },
        {
            title:'开户行',
            dataIndex:'bank'
        },
        {
            title:'结算周期',
            dataIndex:'settlementPeriod'
        },
        {
            title:'财务对账',
            dataIndex:'financiaReconcilor'
        },
        {
            title: '操作',
            key: 'action',
            scopedSlots: { customRender: 'action' },
        }
    ]
    export default {
        name: "contractQ",
        data(){
            return{
                column: 2,
                spinning:false,
                slabColumns:slabColumns,
                slabData:[],
                form:{},
                visibleM:false,
                confirmLoading:false,
                typeId:'1'
            }
        },
        props: {
            contractData: {
                type: Object,
                default () {
                    return {}
                }
            },
            type: {
                type: String,
                default () {
                    return ''
                }
            },
            tabID: {
                type: String,
                default () {
                    return ''
                }
            },
            guid: {
                type: String,
                default () {
                    return ''
                }
            },
        },
        components:{StandardTable},
        created(){
        this.getBankList()
        },
        methods:{
            checkPermission,
            getBankList(){
                bankget(this.guid).then(res=>{
                    console.log(res)
                    if(res.code=='1'){
                        this.slabData=res.data
                    }
                })
            },
            //时间选择
            onChangeTime(data,dateString){
                this.contractData.contractStartDate=dateString
            },
            onChangeTime1(data,dateString){
                this.contractData.contractExpireDate=dateString
            },
            onChangeTime2(data,dateString){
                this.contractData.paymentDate=dateString
            },
            baochun(){
                if(this.type=='2'){
                    contractSave(this.contractData).then(res=>{
                        if(res.code=='1'){
                            this.$message.info(res.message)
                            this.$emit('typeStat','1')
                        }
                    })
                }
            },
            cancel(){
                this.$emit('typeStat','1')
            },
            editBasicInfo(){
                allowAmend(this.guid).then(res=>{
                    if(res.code=='1'){
                        this.$emit('typeStat','2')
                    }else {
                        this.$message.info(res.message)
                    }
                })

            },
            handleOk(){
                if(this.typeId=='1'){
                    this.form.pid=this.guid
                    bankAdd(this.form).then(res=>{
                        this.$message.info(res.message)
                        this.visibleM=false
                        this.form={}
                        if(res.code=='1'){
                            this.getBankList()
                        }

                    })
                }else {
                    this.form.pid=this.guid
                    Amend(this.form).then(res=>{
                        this.$message.info(res.message)
                        this.visibleM=false
                        this.form={}
                        if(res.code=='1'){
                            this.getBankList()
                        }
                    })
                }

            },
            handleCancel(){
                this.visibleM=false
                this.form={}
            },
            visibleMS(){
                allowAmend(this.guid).then(res=>{
                    if(res.code=='1'){
                        this.typeId='1'
                        this.visibleM=true
                    }else {
                        this.$message.info(res.message)
                    }
                })

            },
            compile(data){
                allowAmend(this.guid).then(res=>{
                    if(res.code=='1'){
                        this.typeId='2'
                        this.visibleM=true
                        let id= data.id
                        bankChaxun(id).then(res=>{
                            console.log(res)
                            if(res.code=='1'){
                                this.form =res.data
                            }
                        })
                    }else {
                        this.$message.info(res.message)
                    }
                })

            },
            handleDelBoard(id){
                allowAmend(this.guid).then(res=>{
                    if(res.code=='1'){
                        bankDel(id).then((res) => {
                            this.$message.info(res.message);
                            this.getBankList()
                        });
                    }else {
                        this.$message.info(res.message)
                    }
                })
            },
        },
    }
</script>

<style scoped>

</style>
