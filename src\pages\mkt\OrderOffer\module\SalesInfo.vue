<!-- 市场管理 - 订单报价- 销售信息 -->
<template>
  <div class="contentInfo">
    <a-card :bordered="false">
      <div ref="SelectBox">
        <a-form-model layout="inline" ref="ruleForm1" :rules="rules1" :model="formData">
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="客户型号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input v-model="formData.customerModel" :disabled="type == 'order_management'"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="客户订单号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input v-model="formData.custPo" allowClear :disabled="type == 'order_management'"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="合同号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="formData.contractNumber" allowClear :disabled="type == 'order_management'"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="客户物料号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input v-model="formData.customerMaterialNo" allowClear :disabled="type == 'order_management'"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="客户物料名称" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.customerMaterialName"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="终端客户型号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="formData.endCustNo" allowClear :disabled="type == 'order_management'"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="终端物料号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.endCustMaterialNo"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="终端物料名称" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.endCustMaterialName"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="终端客户订单号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.endCustPo"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="终端客户名称" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.endCustName"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="测试方式" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.flyingProbe"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    @change="changeFlyingProbe"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.FlyingProbe)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="总测试点数" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="formData.delQtyAll" allowClear disabled> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row v-if="showflyingProbe">
            <a-col :span="24">
              <a-form-model-item label="测试方式内容" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-input v-model="formData.flyingProbeContent" allowClear :disabled="type == 'order_management'"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="交货单位" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }" class="require">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.delType"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.BoardType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="市场交期" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-date-picker
                    :disabled="
                      (rowdata.joinFactoryId != 22 && rowdata.joinFactoryId != 37 && rowdata.joinFactoryId != 57) || type == 'order_management'
                    "
                    style="margin-right: 0.5%; margin-left: 0.5%; width: 200px"
                    valueFormat="YYYY-MM-DD"
                    placeholder="市场交期"
                    v-model="formData.marketDeliveryTime"
                    @change="onChange1"
                  />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="备品数(PCS)" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.sparePartNum"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="订单类型" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.jzOrderType"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.JzOrderType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="库存板处理" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.inventoryBoardProcess"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.InventoryBoardProcess)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="产品属性" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input v-model="formData.orderAttribute" allowClear :disabled="type == 'order_management'"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="价格类型" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.priceType"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.PriceType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="参考价格1" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.referencePrice1"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="参考价格2" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.referencePrice2"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="品名行号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.productRownum"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="工具费行号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.toolFeeRownum"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="加急费行号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.jiajiFeeRownum"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="模具费行号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.mouldFeeRownum"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="OEM PN" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.oemPn"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="订单类型" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.reOrder"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="$emit('changeOrderType')"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    :disabled="
                      (rowdata.joinFactoryId == 58 || rowdata.joinFactoryId == 59) &&
                      rowdata.reOrder != 0 &&
                      type != 'order_management' &&
                      !rowdata.isOrderModify &&
                      !formData.isOnLineEcn
                        ? false
                        : true
                    "
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ReOrder, 'int')"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="装运方式" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.shipmentTerm"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.ShipmentTerm)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="地点" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.shipLocation"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.ShipLocation)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col
              :span="8"
              v-if="![67, 97, 98].includes(Number(rowdata.joinFactoryId))"
              :class="[58, 59, 78, 79, 80].includes(Number(rowdata.joinFactoryId)) ? 'require' : ''"
            >
              <a-form-model-item label="加工工厂" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <a-select
                  v-model="formData.orderDirection"
                  showSearch
                  allowClear
                  :disabled="type == 'order_management'"
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in Processingplants()" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8" v-else-if="[67, 97, 98].includes(Number(rowdata.joinFactoryId))">
              <a-form-model-item label="研发项目" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <a-select
                  v-model="formData.rdProject"
                  showSearch
                  allowClear
                  :disabled="type == 'order_management'"
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in rdProjectList" :key="index" :value="item.rrecId" :lable="item.code_">
                    {{ item.code_ }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8" v-if="[58, 59].includes(Number(rowdata.joinFactoryId))">
              <a-form-model-item label="研发项目" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <a-select
                  v-model="formData.rdProject"
                  showSearch
                  allowClear
                  :disabled="type == 'order_management'"
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in rdProjectList" :key="index" :value="item.rrecId" :lable="item.code_">
                    {{ item.code_ }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="标识类型" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.identificationType"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.IdentificationType)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8" v-if="[78, 79, 80].includes(Number(rowdata.joinFactoryId))">
              <a-form-model-item label="运输工厂" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }" class="require">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.transportationFactory"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.TransportationFactory)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8" v-if="[77, 100].includes(Number(rowdata.joinFactoryId))">
              <a-form-model-item label="半制程工序" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.halfProcess"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.HalfProcess, 'int')"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <!-- <a-col :span="8" v-if="[77, 100].includes(Number(rowdata.joinFactoryId))">
              <a-form-model-item label="加工形式" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.processingForm"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ProcessingForm)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8" v-if="[77, 100].includes(Number(rowdata.joinFactoryId))">
              <a-form-model-item label="外发工厂" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.outsourcedFactory"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.OutsourcedFactory)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col> -->
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="上锡板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.isUpperTin" :disabled="type == 'order_management'" />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="已开测试架" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.openedTestrack" :disabled="type == 'order_management'" />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="高附加值" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.isHighAddValue" :disabled="type == 'order_management'"> </a-checkbox>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="接单工厂" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <a-select
                  v-model="formData.contractFactoryId"
                  showSearch
                  allowClear
                  :disabled="type == 'order_management' || [80, 78, 79].includes(Number(rowdata.joinFactoryId))"
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey1(factoryList)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <!-- <a-col :span="8">
          <a-form-model-item label="相关品牌" :label-col="{ span:10}" :wrapper-col="{ span:14 }" >
            <a-select v-model="formData.relatedBrand" showSearch allowClear :disabled="type == 'order_management'" optionFilterProp="lable" :getPopupContainer="()=>this.$refs.SelectBox">
                  <a-select-option  v-for="(item,index) in mapKey(selectOption.RelatedBrand)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
            </a-form-model-item>
          </a-col>  -->
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="后补合同" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.behindContract" :disabled="type == 'order_management'" />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="付款状态" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.payStatus"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.PayStatus)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                  <!-- <a-checkbox v-model="formData.payinadvance"/> -->
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="同等型号" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input allowClear :disabled="type == 'order_management'" v-model="formData.sameModel"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8" :class="rowdata.joinFactoryId == 77 ? 'require' : ''">
              <a-form-model-item label="批量类型" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <a-select
                  v-model="formData.batchType"
                  showSearch
                  allowClear
                  :disabled="type == 'order_management'"
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.BatchType)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="收货地址" class="zzz" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div style="position: relative">
                  <a-select
                    v-model="formData.shippingAddress"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    style="width: 86%"
                    class="userStyle"
                    @change="addressChange"
                    @popupScroll="handlePopupScroll2"
                    @search="supValue1"
                  >
                    <a-select-option v-for="(item, index) in AddressList" :key="index" :title="item.valueMember1" :value="item.text" :lable="item.valueMember1">
                      {{ item.valueMember1 }}
                    </a-select-option>
                  </a-select>
                  <a-button
                    type="primary"
                    style="margin-left: 1%; position: absolute; width: 80px"
                    @click="addClick"
                    v-if="type != 'order_management'"
                    >临时地址</a-button
                  >
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <!-- <a-col :span="6">
            <a-form-model-item label=" EQ联系人" :label-col="{ span: 12}" :wrapper-col="{ span: 12 }">
            <div class="editWrapper" >
                <a-input v-model="formData.eqContactPerson" allowClear :disabled="type == 'order_management'"></a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="EQ联系人电话" :label-col="{ span: 10}" :wrapper-col="{ span: 14 }">
               <div class="editWrapper" >
                <a-input v-model="formData.eqPhoneNumber"  allowClear :disabled="type == 'order_management'"></a-input>
              </div>              
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="EQ联系人邮箱" :label-col="{ span: 8}" :wrapper-col="{ span: 16 }">
               <div class="editWrapper" >
                <a-input v-model="formData.eqEmail"  allowClear :disabled="type == 'order_management'"></a-input>
              </div>              
            </a-form-model-item>
          </a-col> -->
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="出货报告"
                prop="needReportList"
                :label-col="{ span: 3 }"
                :wrapper-col="{ span: 21 }"
                style="width: 100%; margin: 0"
                class="heightSty1"
              >
                <div class="editWrapper">
                  <a-select
                    v-model="needReportList"
                    mode="multiple"
                    placeholder="请选择"
                    showSearch
                    allowClear
                    :disabled="type == 'order_management'"
                    optionFilterProp="lable"
                    style="width: 100%"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.NeedReportList)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="市场备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-textarea
                    v-model="formData.mktNote"
                    style="width: 100%"
                    :auto-size="{ minRows: 1, maxRows: 5 }"
                    :disabled="type == 'order_management'"
                  />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="费用备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-textarea
                    v-model="formData.costNote"
                    style="width: 100%"
                    :auto-size="{ minRows: 1, maxRows: 5 }"
                    :disabled="type == 'order_management'"
                  />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="型号备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-textarea
                    v-model="formData.note"
                    style="width: 100%"
                    :auto-size="{ minRows: 1, maxRows: 5 }"
                    :disabled="type == 'order_management'"
                  />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <span title="添加EQ联系人">
          <a-icon
            type="plus"
            style="margin-left: 10px; color: #ff9900; font-size: 20px"
            @click="addEQClick"
            v-if="type != 'order_management'"
          ></a-icon
        ></span>
        <span title="临时EQ联系人">
          <a-icon
            type="usergroup-add"
            style="margin-left: 10px; color: #ff9900; font-size: 20px"
            @click="temporaryeq"
            v-if="type != 'order_management'"
          ></a-icon
        ></span>
        <span title="获取EQ联系人">
          <a-icon
            type="swap"
            style="margin-left: 10px; color: #ff9900; font-size: 20px"
            @click="Geteqcontacts"
            v-if="type != 'order_management'"
          ></a-icon
        ></span>
        <div style="max-height: 175px; overflow-y: auto">
          <a-list itemKey="id" bordered :grid="{ gutter: 16, column: 2 }" :dataSource="tableDetails" :pagination="false" class="container">
            <template>
              <a-list-item v-for="item in tableDetails" :key="item.id" :span="10">
                <div class="list-item-content">
                  <a-list-item-meta :title="getTitle(item)" :description="getTitle1(item)" />
                  <a-divider type="vertical" style="height: 40px; margin-top: 2%; border-color: rgb(0, 0, 0)" />
                  <span style="margin-top: 0px; margin-right: 6px">
                    <a @click="compileS(item)">编辑</a>
                    <a-divider type="vertical" style="height: 15px; border-color: rgb(0, 0, 0)" />
                    <a-popconfirm title="确定要删除吗？" @confirm="cancelS(item)">
                      <a href="javascript:;">删除</a>
                    </a-popconfirm>
                  </span>
                </div>
                <span v-if="item.default_" style="font-size: 12px; float: right; margin-top: -25px; margin-right: 20px; color: red">主邮箱</span>
                <span v-else style="font-size: 12px; float: right; margin-top: -25px; margin-right: 15px; color: #000000">抄送邮箱</span>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-card>
    <a-card>
      <a-modal
        v-model="rightVisible"
        :confirmLoading="loading"
        :mask="true"
        :maskClosable="false"
        centered
        @cancel="handleCancel1"
        title="客户添加"
        @ok="handleRight"
      >
        <div ref="SelectBox1">
          <a-form-model ref="ruleForm" :model="form" :rules="rules">
            <a-form-model-item label="默认联系人" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-checkbox v-model="form.default_" @change="handleChangetype_"></a-checkbox>
            </a-form-model-item>
            <a-form-model-item label="联系人类型" ref="type_" prop="type_" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-select
                v-model="form.type_"
                placeholder="请选择"
                showSearch
                allowClear
                @change="handleChangetype_"
                :getPopupContainer="() => this.$refs.SelectBox1"
              >
                <a-select-option value="办公"> 办公 </a-select-option>
                <a-select-option value="商务"> 商务 </a-select-option>
                <a-select-option value="采购"> 采购 </a-select-option>
                <a-select-option value="报价"> 报价 </a-select-option>
                <a-select-option value="技术"> 技术 </a-select-option>
                <a-select-option value="发货"> 发货 </a-select-option>
                <a-select-option value="询价"> 询价 </a-select-option>
                <a-select-option value="EQ联系人"> EQ联系人 </a-select-option>
                <a-select-option value="发票"> 发票 </a-select-option>
                <a-select-option value="跟单员"> 跟单员 </a-select-option>
                <a-select-option value="工作稿"> 工作稿 </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="联系人" ref="addName" prop="addName" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.addName" allowClear />
            </a-form-model-item>
            <a-form-model-item label="联系电话" ref="addPhone" prop="addPhone" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.addPhone" allowClear />
            </a-form-model-item>
            <a-form-model-item label="邮箱" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" ref="mailbox" prop="mailbox">
              <a-input v-model="form.mailbox" allowClear @change="mailboxChange" />
            </a-form-model-item>
            <a-form-model-item label="传真" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.fax" allowClear />
            </a-form-model-item>
            <a-form-model-item label="收货省份" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" ref="provinceData" prop="provinceData">
              <a-cascader
                v-model="form.provinceData"
                change-on-select
                :options="jsonList"
                placeholder="请选择"
                @change="onChangeData"
                allowClear
                :show-search="{ filter }"
              />
            </a-form-model-item>
            <a-form-model-item label="收货地址" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" ref="address" prop="address">
              <a-input v-model="form.address" allowClear @blur="parseAddress" /><!---->
            </a-form-model-item>
            <a-form-model-item label="收货公司" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.receiverCompany_" allowClear />
            </a-form-model-item>
            <a-form-model-item label="终端客户" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-select
                v-model="form.terminalCust"
                showSearch
                allowClear
                optionFilterProp="lable"
                @popupScroll="handlePopupScroll"
                :getPopupContainer="() => this.$refs.SelectBox1"
                @search="supValue"
                @change="terminalChange"
              >
                <a-select-option v-for="item in frontDataZSupplier" :key="item.valueMember" :value="item.valueMember" :lable="item.valueMember">
                  {{ item.valueMember }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-form-model>
        </div>
      </a-modal>
    </a-card>
    <a-modal
      :title="EQtype == 'add' ? '添加EQ联系人' : '修改EQ联系人'"
      v-model="eqVisible"
      :mask="true"
      :maskClosable="false"
      @cancel="handleCancelEQ"
      centered
      @ok="handleokEQ"
    >
      <div ref="SelectBox2">
        <a-form-model ref="ruleFormEQ" :model="formEQ" :rules="rules2">
          <a-form-model-item label="默认联系人" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-checkbox v-model.trim="formEQ.default_" @change="ruleChange"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="联系人" ref="connactUseName_" prop="connactUseName_" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-input v-model.trim="formEQ.connactUseName_" allowClear />
          </a-form-model-item>
          <a-form-model-item label="联系电话" ref="connactPhone_" prop="connactPhone_" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-input v-model.trim="formEQ.connactPhone_" allowClear />
          </a-form-model-item>
          <a-form-model-item label="邮箱" ref="mailbox" prop="mailbox" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-input v-model.trim="formEQ.mailbox" allowClear @change="mailboxChange" />
          </a-form-model-item>
        </a-form-model>
      </div>
    </a-modal>
    <a-modal
      title="临时联系人"
      v-model="temdatavisible"
      :mask="true"
      :maskClosable="false"
      @cancel="handleCancel"
      :width="850"
      centered
      @ok="handleok"
    >
      <a-table
        :columns="columns4"
        :dataSource="dataSource4"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ y: 300 }"
        :pagination="false"
        :customRow="onClickRow"
        :rowClassName="isRedRow"
      >
        <template slot="operation" slot-scope="text, record">
          <a-icon type="delete" @click="delclick(record)" style="color: cornflowerblue; cursor: pointer"></a-icon>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import AddressParse, { AREA, Utils } from "address-parse";
import { deliveryAddress, adressByPcbId, rdProject, moduledropdownlist } from "@/services/mkt/orderInfo";
import { terminalCusts, setcustlinks, custlinks, eqcontactperson, deletecustlinks } from "@/services/mkt/CustInfoNew";
import jsonList from "../../../../../public/index";
import Cookie from "js-cookie";
import $ from "jquery";
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
    align: "center",
  },
  {
    title: "默认",
    dataIndex: "default_",
    customRender: (text, record, index) => (record.default_ == true ? "是" : "否"),
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "联系人",
    dataIndex: "connactUseName_",
    width: 60,
    ellipsis: true,
    align: "left",
  },
  {
    title: "联系电话",
    dataIndex: "connactPhone_",
    ellipsis: true,
    width: 85,
    align: "left",
  },
  {
    title: "邮箱",
    dataIndex: "mailbox",
    ellipsis: true,
    align: "left",
    width: 180,
  },
  {
    title: "操作",
    scopedSlots: { customRender: "operation" },
    width: 30,
    ellipsis: true,
    align: "center",
  },
];
export default {
  name: "SalesInfo",
  props: ["editFlag", "showData", "selectOption", "saveID", "rowdata", "type", "factoryList"],
  data() {
    return {
      Faclist: [],
      rdProjectList: [],
      formData: {},
      showflyingProbe: false,
      selectedRowsData: {},
      columns4,
      dataSource4: [],
      AddressList: [],
      dataVisible: false,
      dataVisible1: false,
      processEdgesStrR: "",
      makeupVisible: false, // 拼版图弹窗开关
      needReportList: [], // 出货报告列表
      rules: {
        type_: [{ required: true, message: "联系人类型必须选择", trigger: "blur" }],
        mailbox: [
          {
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
        addName: [{ required: true, message: "联系人必须填写", trigger: "blur" }],
        addPhone: [{ pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/, message: "请输入正确的号码格式", trigger: "blur" }],
      },
      rules1: {
        delType: [{ required: true, message: "交货单位请必须选择", trigger: "blur" }],
      },
      rules2: {
        connactUseName_: [{ required: true, message: "联系人必须填写", trigger: "blur" }],
        connactPhone_: [
          {
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
            message: "请输入正确的号码格式",
            trigger: "blur",
          },
        ],
        mailbox: [
          { required: true, message: "邮箱必须填写", trigger: "blur" },
          {
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
      },
      rightVisible: false,
      form: {
        default_: false, // 默认联系人
        type_: "发货", // 联系人类型
        mailbox: "", //邮箱
        addName: "", // 联系人
        addPhone: "", // 联系电话
        fax: "",
        provinceData: [], // 收货省份
        receiverCompany_: "", // 收货公司
        address: "", // 收货地址
        terminalCust: "",
      },
      formEQ: {
        default_: true, // 默认联系人
        type_: "EQ联系人", // 联系人类型
        mailbox: "", //邮箱
        connactUseName_: "", // 联系人
        connactPhone_: "", // 联系电话
      },
      receiverState_: "",
      jsonList,
      terminalCustList: [],
      frontDataZSupplier: [], // 供应商 100条数据的集合
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      isFileType: false,
      receiverState_zh: "",
      receiverCity_zh: "",
      AddressListALL: [],
      valueData1: undefined,
      loading: false,
      tableDetails: [],
      eqVisible: false, //eq联系人新增
      temdatavisible: false,
      EQtype: "", //eq联系人新增/编辑类型
      editData: {},
    };
  },
  filters: {
    invoiceFilter(val) {
      let val_ = "";
      switch (val) {
        case 0:
          val_ = "默认";
          break;
        case 1:
          val_ = "1.0w";
          break;
        case 2:
          val_ = "1.5w";
          break;
        case 3:
          val_ = "2.0w";
          break;
        case 4:
          val_ = "0.5w";
          break;
        case 5:
          val_ = "0.7w";
          break;
        case 6:
          val_ = "5w";
          break;
        case 8:
          val_ = "8w";
          break;
        case 9:
          val_ = "3w";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
    camFilter(val) {
      let val_ = "";
      switch (val) {
        case 1:
          val_ = "中级";
          break;
        case 2:
          val_ = "高级";
          break;
        case 3:
          val_ = "资深";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
  },
  created() {
    this.getList();
  },
  mounted() {
    for (var a = 0; a < this.jsonList.length; a++) {
      for (var b = 0; b < this.jsonList[a].children.length; b++) {
        delete this.jsonList[a].children[b].children;
      }
    }
    this.handleChangetype_();
    this.getEditData();
    this.getDeliveryAddress();
    const token = Cookie.get("Authorization");
    const data = JSON.parse(localStorage.getItem("terminalCustList"));
    if (
      data &&
      token &&
      data.token == token &&
      data.filter(item => {
        return item.tradeType == this.showData.joinFactoryId;
      }).length
    ) {
      for (var aa = 0; aa < data.length; aa++) {
        if (data[aa].token == token && data[aa].tradeType == this.showData.joinFactoryId) {
          this.terminalCustList = data.data; //本地缓存
          this.frontDataZSupplier = data.data.slice(0, 20);
        }
      }
    } else {
      terminalCusts(this.showData.joinFactoryId).then(res => {
        if (res.data) {
          this.terminalCustList = res.data;
          this.frontDataZSupplier = res.data.slice(0, 20);
          let token = Cookie.get("Authorization");
          let arr = [];
          if (res.data.length) {
            if (data == null) {
              arr.push({ data: this.terminalCustList, token, tradeType: this.showData.joinFactoryId });
              localStorage.setItem("terminalCustList", JSON.stringify(arr)); //本地缓存
            } else {
              data.push({ data: this.terminalCustList, token, tradeType: this.showData.joinFactoryId });
              localStorage.setItem("terminalCustList", JSON.stringify(data)); //本地缓存
            }
          }
        } else {
          this.$message.error(res.message);
        }
      });
    }
    const data1 = JSON.parse(localStorage.getItem("rdProjectList"));
    if (
      data1 &&
      token &&
      data1.token == token &&
      data1.filter(item => {
        return item.tradeType == this.showData.joinFactoryId;
      }).length
    ) {
      for (var i = 0; i < data1.length; i++) {
        if (data1[i].token == token && data1[i].tradeType == this.showData.joinFactoryId) {
          this.rdProjectList = data1.data; //本地缓存
        }
      }
    } else {
      rdProject(this.showData.joinFactoryId).then(res => {
        if (res.data) {
          this.rdProjectList = res.data;
          let token = Cookie.get("Authorization");
          let arr = [];
          if (res.data.length) {
            if (data1 == null) {
              arr.push({ data: this.rdProjectList, token, tradeType: this.showData.joinFactoryId });
              localStorage.setItem("rdProjectList", JSON.stringify(arr)); //本地缓存
            } else {
              data1.push({ data: this.rdProjectList, token, tradeType: this.showData.joinFactoryId });
              localStorage.setItem("rdProjectList", JSON.stringify(data)); //本地缓存
            }
          }
        } else {
          this.$message.error(res.message);
        }
      });
    }
  },
  methods: {
    delclick(record) {
      deletecustlinks({ id: [record.id] }).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.temporaryeq();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    parseAddress() {
      if (this.form.address != "" && this.showData.joinFactoryId != 37 && this.showData.joinFactoryId != 57) {
        const result = AddressParse.parse(this.form.address);
        if (result.length == 1) {
          this.receiverState_zh = result[0].province;
          this.receiverCity_zh = result[0].city;
          const [province, city] = Utils.getTargetAreaListByCode("province", result[0].code, true);
          this.form.provinceData = [province.code, city.code];
          this.receiverState_ = province.code;
          this.receiverCity_ = city.code;
        }
      }
    },
    ruleChange() {
      if (!this.formEQ.default_) {
        this.rules2 = { ...this.rules2, connactUseName_: [] };
      } else {
        this.rules2 = { ...this.rules2, connactUseName_: [{ required: true, message: "联系人类型必须填写", trigger: "blur" }] };
      }
    },
    onChange1(value, dateString) {
      this.formData.marketDeliveryTime = dateString;
    },
    handleChangetype_() {
      if (this.form.default_ == true) {
        this.namerules();
        this.mailerules();
      } else {
        this.remail();
        this.rename();
      }
      if (this.form.type_ == "采购" || this.form.type_ == "发货") {
        this.provincerules();
        this.addressrules();
        this.remail();
      } else if (this.form.type_ == "询价") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else if (this.form.type_ == "跟单员") {
        this.phonerules();
        this.remail();
        this.readdress();
        this.reprovince();
      } else if (this.form.type_ == "工作稿") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else if (this.$route.query.tradeType == 37 || this.$route.query.tradeType == 57) {
        if (this.form.default_ == true && this.form.type_ == "EQ联系人") {
          this.mailerules();
          this.readdress();
          this.reprovince();
        }
      } else if (this.form.type_ == "EQ联系人") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else {
        this.rephone();
        this.readdress();
        this.reprovince();
      }
    },
    namerules() {
      this.rules = { ...this.rules, addName: [{ required: true, message: "联系人必须填写", trigger: "blur" }] };
    },
    phonerules() {
      this.rules = {
        ...this.rules,
        addPhone: [
          {
            required: true,
            message: "联系电话必须填写,请输入正确格式",
            trigger: "blur",
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
          },
        ],
      };
    },
    mailerules() {
      this.rules = {
        ...this.rules,
        mailbox: [
          {
            required: true,
            message: "邮箱必须填写,请输入正确格式",
            trigger: "blur",
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
          },
        ],
      };
    },
    provincerules() {
      this.rules = { ...this.rules, provinceData: [{ required: true, message: "收货省份必须选择", trigger: "blur" }] };
    },
    addressrules() {
      this.rules = { ...this.rules, address: [{ required: true, message: "收货地址必须填写", trigger: "blur" }] };
    },
    rephone() {
      this.rules = {
        ...this.rules,
        addPhone: [
          {
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
            message: "请输入正确的号码格式",
            trigger: "blur",
          },
        ],
      };
    },
    remail() {
      this.rules = {
        ...this.rules,
        mailbox: [
          {
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "请输入正确的邮箱格式",
            trigger: "blur",
          },
        ],
      };
    },
    rename() {
      this.rules = { ...this.rules, addName: [] };
    },
    reprovince() {
      this.rules = { ...this.rules, provinceData: [] };
    },
    readdress() {
      this.rules = { ...this.rules, address: [] };
    },
    filter(inputValue, path) {
      return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
    },
    getDeliveryAddress() {
      deliveryAddress(this.saveID).then(res => {
        if (res.code) {
          this.AddressListALL = res.data;
          this.AddressList = res.data.slice(0, 20);
        }
      });
    },
    getEditData() {
      if (this.showData.needReportList) {
        this.needReportList = this.showData.needReportList.split(",");
      } else {
        this.needReportList = [];
      }
      this.formData = this.showData;
      this.changeFlyingProbe();
      this.formData.contractFactoryId = this.showData.contractFactoryId ? this.showData.contractFactoryId.toString() : "";
      this.form.terminalCust = this.showData.terminalCust;
      this.formData.delQtyAll = this.formData.delQty * this.formData.testPointNum;
      this.formData.reOrder = this.formData.reOrder !== null && this.formData.reOrder !== "" ? this.formData.reOrder.toString() : null;
      this.formData.identificationType =
        this.formData.identificationType !== null && this.formData.identificationType !== "" ? this.formData.identificationType.toString() : null;
      let arr_ = [];
      for (var i = 0; i < this.formData.boardLayers; i++) {
        if (i == 0 || i == this.formData.boardLayers - 1) {
          arr_.push(this.formData.copperThickness);
        } else {
          arr_.push(this.formData.innerCopperThickness);
        }
      }
      this.formData.cuThickness = arr_.join("/");
      if (this.formData.processEdgesStrL == "updown" || this.formData.processEdgesStrL == "leftright" || this.formData.processEdgesStrL == "both") {
        this.formData.processEdgesStrR = 4;
      }

      if (this.formData.ipcLevel > 0) {
        this.formData.iPCLevel = this.formData.ipcLevel.toString();
      }
      this.tableDetails = this.formData.eqdtos;
    },
    getList() {
      moduledropdownlist().then(res => {
        this.Faclist = res;
        if (this.Faclist.length) {
          this.Faclist.map(item => {
            item.value = item.text;
            item.lable = item.text;
          });
        }
      });
    },
    Processingplants() {
      let selectData = [];
      if ([77, 100].includes(Number(this.rowdata.joinFactoryId))) {
        selectData = this.Faclist;
      } else {
        selectData = this.mapKey(this.selectOption.OrderDirection);
      }
      return selectData;
    },
    mapKey(data, type) {
      if (!data || data.length == 0) {
        return [];
      } else {
        return data.map(item => {
          return { value: type == "int" ? Number(item.valueMember) : item.valueMember, lable: item.text };
        });
      }
    },
    mapKey1(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(key => {
          return { value: key, lable: data[key] };
        });
      }
    },
    //时间选择
    onChangeTime(data, dateString) {
      this.formData.deliveryDate = dateString;
    },
    numChange() {
      if (this.formData.delType == "set" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.boardArea = ((this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000).toFixed(2);
      }
      if (this.formData.delType == "pcs" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth && this.formData.su) {
        this.formData.boardArea = (
          (this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) /
          1000000 /
          this.formData.su
        ).toFixed(2);
      }
    },
    address() {
      this.$forceUpdate();
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate(value, list) {
      this.formData.shippingAddress = value;
      let a = this.getPrice(this.formData.shippingAddress, list, value);
      this.$forceUpdate();
    },
    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
    },
    addressChange() {
      this.$forceUpdate();
      this.formData.shippingAddressId = this.AddressList.filter(ite => ite.text == this.formData.shippingAddress)[0]?.valueMember;
      //AddressList
    },
    handleCancel1() {
      this.rightVisible = false;
      this.receiverState_zh = "";
      this.receiverCity_zh = "";
      this.$refs.ruleForm.resetFields();
    },
    mailboxChange() {
      this.$forceUpdate();
    },
    addClick() {
      this.rightVisible = true;
      this.form = {
        default_: false, // 默认联系人
        type_: "发货", // 联系人类型
        mailbox: "", //邮箱
        addName: "", // 联系人
        addPhone: "", // 联系电话
        fax: "",
        provinceData: [], // 收货省份
        receiverCompany_: "", // 收货公司
        address: "", // 收货地址
        terminalCust: "",
      };
      if (this.showData.terminalCust) {
        this.form.terminalCust = this.showData.terminalCust;
      }
      this.handleChangetype_();
      this.$forceUpdate();
    },
    //新增联系人
    handleRight() {
      const form = this.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          let params = {
            id: this.saveID,
            address_: this.form.address,
            connactUseName_: this.form.addName,
            connactPhone_: this.form.addPhone,
            default_: this.form.default_,
            // receiverState_:this.receiverState_.length ? this.receiverState_:'',
            receiverState_: this.receiverState_,
            receiverCity_: this.receiverCity_,
            receiverCompany_: this.form.receiverCompany_,
            type_: this.form.type_,
            mailbox: this.form.mailbox,
            fax: this.form.fax,
            terminalCust: this.form.terminalCust,
            receiverState_zh: this.receiverState_zh,
            receiverCity_zh: this.receiverCity_zh,
          };
          this.loading = true;
          adressByPcbId(params).then(res => {
            this.formData.shippingAddress = res.data.text;
            this.formData.shippingAddressId = res.data.valueMember;
            this.rightVisible = false;
            this.loading = false;
            this.getDeliveryAddress();
          });
        }
      });
    },
    onChangeData(val) {
      if (val.length) {
        this.receiverState_zh = this.jsonList.filter(item => {
          return item.value == val[0];
        })[0].label;
      }
      if (val.length > 1) {
        var arr = [];
        arr = this.jsonList.filter(item => {
          return item.value == val[0];
        })[0].children;
        this.receiverCity_zh = arr.filter(item => {
          return item.value == val[1];
        })[0].label;
      }
      if (val.length) {
        if (val[0] == "110000" || val[0] == "310000" || val[0] == "120000" || val[0] == "500000") {
          this.receiverCity_zh = this.receiverState_zh;
          this.receiverState_zh = this.receiverState_zh.split("市")[0];
        }
      } else {
        this.receiverCity_zh = "";
        this.receiverState_zh = "";
      }
      this.receiverCity_ = val[1];
      this.receiverState_ = val[0];
    },
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.terminalCustList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.terminalCustList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.terminalCustList.forEach((item, index) => {
              if (item.valueMember.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.terminalCustList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    handlePopupScroll2(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.AddressListALL.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.AddressListALL.length;
          }
          // 判断是否有搜索
          if (this.valueData1) {
            this.AddressListALL.forEach((item, index) => {
              if (item.text.indexOf(this.valueData1) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.AddressListALL.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.AddressList = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue1(value) {
      if (value) {
        let that = this;
        that.valueData1 = value;
        let arr = that.AddressListALL.filter(m => m.text.indexOf(value) != -1);
        if (arr.length) {
          that.AddressList = arr.slice(0, 20);
        } else {
          that.AddressList = [];
        }
      } else {
        this.valueData1 = undefined;
        this.AddressList = this.AddressListALL.slice(0, 20);
      }
    },
    terminalChange() {
      this.$forceUpdate();
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value;
        let arr = that.terminalCustList.filter(m => m.valueMember.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.terminalCustList.slice(0, 20);
      }
    },
    getTitle(item) {
      return (
        <span>
          <span style="margin-right: 5px;margin-left:5px;color:#000000">
            <a-icon type="user" style="margin-right: 5px;"></a-icon>
            {item.connactUseName_} <span style="float:right;color:#000000">EQ联系人</span>
          </span>
        </span>
      );
    },
    getTitle1(item) {
      return (
        <span style="font-size:13px;">
          {item.connactPhone_ ? (
            <span style="margin-right: 5px;margin-left:5px;color:#000000; " class="tmp">
              <a-icon type="phone" style="margin-right: 5px;color:#000000;"></a-icon>
              <span title={item.connactPhone_}>{item.connactPhone_}</span>
            </span>
          ) : null}
          {item.mailbox ? (
            <span style="margin-right: 5px;display:inline-block;color:#000000;" class="tmp1">
              <a-icon type="mail" style="margin-right: 5px;margin-left:5px;color:#000000;"></a-icon>
              <span title={item.mailbox}>{item.mailbox}</span>
            </span>
          ) : null}
        </span>
      );
    },
    addEQClick() {
      this.eqVisible = true;
      this.EQtype = "add";
      this.formEQ = {
        default_: true, // 默认联系人
        type_: "EQ联系人", // 联系人类型
        mailbox: "", //邮箱
        connactUseName_: "", // 联系人
        connactPhone_: "", // 联系电话
      };
      this.ruleChange();
    },
    Geteqcontacts() {
      eqcontactperson(this.rowdata.id).then(res => {
        if (res.code) {
          this.tableDetails = res.data;
          if (res.data.length == 0) {
            this.$message.warning("该客户暂无联系人信息！");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    changeFlyingProbe() {
      if (this.formData.flyingProbe == "sharestand" && (this.formData.joinFactoryId == "58" || this.formData.joinFactoryId == "59")) {
        this.showflyingProbe = true;
      } else {
        this.showflyingProbe = false;
      }
    },
    temporaryeq() {
      custlinks(this.rowdata.id).then(res => {
        if (res.code) {
          for (let index = 0; index < res.data.length; index++) {
            res.data[index].ide = index + 1;
          }
          this.selectedRowsData = {};
          this.dataSource4 = res.data;
          this.temdatavisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleCancel() {
      this.temdatavisible = false;
    },
    handleCancelEQ() {
      this.eqVisible = false;
      if (this.EQtype == "edit") {
        this.formEQ.type_ = "EQ联系人";
        this.formEQ.default_ = this.editData.default_;
        this.formEQ.connactUseName_ = this.editData.connactUseName_;
        this.formEQ.connactPhone_ = this.editData.connactPhone_;
        this.formEQ.mailbox = this.editData.mailbox;
      }
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.selectedRowsData = record;
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.ide && record.ide == this.selectedRowsData.ide) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    handleokEQ() {
      const form = this.$refs.ruleFormEQ;
      form.validate(valid => {
        if (valid) {
          let mianmail = this.tableDetails.filter(ite => ite.default_ == true);
          if ((mianmail.length && this.formEQ.default_ == true && !this.editData.default_) || (mianmail.length > 1 && this.formEQ.default_ == true)) {
            this.$message.error("该客户已有默认联系人，不允许重复添加！");
            return;
          }
          this.eqVisible = false;
          if (this.EQtype == "add") {
            this.tableDetails.push(this.formEQ);
            let params = this.formEQ;
            params.id = this.rowdata.id;
            setcustlinks(params).then(res => {
              //if(res.code)
            });
          } else {
            const index = this.tableDetails.findIndex(obj => obj === this.editData);
            if (index !== -1) {
              this.tableDetails[index] = this.formEQ;
            }
          }
        }
      });
    },
    handleok() {
      let mianmail = this.tableDetails.filter(ite => ite.default_ == true);
      if (mianmail.length && this.selectedRowsData.default_ == true) {
        this.$message.error("该客户已有默认联系人，不允许重复添加！");
        return;
      }
      this.temdatavisible = false;
      if (JSON.stringify(this.selectedRowsData) != "{}") {
        this.tableDetails.push(this.selectedRowsData);
      }
    },
    compileS(record) {
      this.editData = record;
      this.formEQ = {
        default_: record.default_, // 默认联系人
        type_: record.type_, // 联系人类型
        mailbox: record.mailbox, //邮箱
        connactUseName_: record.connactUseName_, // 联系人
        connactPhone_: record.connactPhone_, // 联系电话
      };
      this.EQtype = "edit";
      this.eqVisible = true;
      this.ruleChange();
    },
    cancelS(record) {
      const index = this.tableDetails.findIndex(obj => obj === record);
      if (index !== -1) {
        this.tableDetails.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-list-item-meta-description {
  max-width: 310px;
}
.address {
  /deep/.ant-form-item-label {
    width: 101px;
  }
  /deep/.ant-form-item-control-wrapper {
    width: 438px;
  }
}
/deep/ .ant-table {
  .rowBackgroundColor {
    background: #dcdcdc !important;
  }
}
/deep/.ant-table-scroll {
  border: 1px solid #e8e8e8;
  border-bottom: none;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table {
  .ant-table-thead > tr > th {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr {
    .lastTd {
      padding: 0 4px !important;
    }
  }
}
/deep/ .tmp {
  word-break: keep-all;
  vertical-align: top;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 37%;
  display: inline-block;
}
/deep/ .tmp1 {
  word-break: keep-all;
  vertical-align: top;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 58%;
  display: inline-block;
}
/deep/.ant-list {
  .ant-divider,
  .ant-divider-vertical {
    margin: 0 4px;
  }
}
/deep/.list-item-content {
  display: flex;
  border: 1px solid #dfdfdf;
}
/deep/.ant-list-vertical .ant-list-item-meta {
  margin-bottom: 0;
}
/deep/.ant-list-bordered .ant-list-item {
  padding: 4px;
  margin-bottom: 0;
}
/deep/.ant-list-vertical .ant-list-item-meta-title {
  margin-bottom: 4px;
}
/deep/.ant-table-thead > tr > th {
  padding: 6px 2px;
}
/deep/.ant-table-tbody > tr > td {
  padding: 6px 2px;
}
/deep/.editWrapper1 {
  .ant-form-item-control {
    width: 269.66px !important;
    height: 41px !important;
  }
}

/deep/.ant-input:not(:last-child) {
  padding-right: 18px;
}
/deep/.ant-input-suffix {
  right: 5px;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select-selection--multiple .ant-select-selection__choice {
  color: #000000;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
  padding: 1.5%;
  padding-left: 10px;
}
/deep/.userStyle {
  .ant-select-selection {
    user-select: all;
  }
}
/deep/.zzz {
  // .ant-form-item-label{
  //   width:101px ;
  // }
  // .ant-form-item-control-wrapper{
  //   width: 438px;
  // }

  .ant-select {
    min-height: 32px !important;
    height: auto !important;
    .ant-select-selection {
      display: inline-block;
      height: auto !important;
      width: 100%;
    }
    .ant-select-selection-selected-value {
      overflow: inherit;
      white-space: inherit;
    }
  }
}
/deep/.heightSty1 {
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
        // .edit{
        //  line-height: 28px!important;
        // text-overflow: unset!important;
        // white-space: unset!important;
        // }
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 21px !important;
            margin-top: 3px;
            line-height: 20px !important;
            width: 19%;
          }

          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}
/deep/.ant-select-selection-selected-value {
  span:nth-child(1) {
    display: none !important;
  }
  span:nth-child(2) {
    display: none !important;
  }
  span:nth-child(3) {
    width: 100% !important;
  }
}
/deep/form .ant-select {
  height: 32px;
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
.contentInfo {
  // height:190px;
  height: 100%;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: 500;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 32px;
          // .ant-select {
          //   width: 120px;

          // }
          // .ant-input {
          //   width: 122px;

          // }
          // .ant-input-number {
          //   width: 120px;
          // }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          label {
            font-family: PingFangSC-Regular, Sans-serif;
            color: #000000;
            font-size: 14px;
            font-weight: 500;
          }
        }
        .ant-form-item-control-wrapper {
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 4px 4px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
/deep/.ant-modal-body {
  .ant-form {
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;

    .ant-form-item {
      margin: 0;
      width: 100%;
      display: flex;
      min-height: 40px;
      // height:40px!important;

      .editWrapper {
        display: flex;
        align-items: center;
        min-height: 40px;

        /deep/.ant-select {
          width: 120px;
          height: 24px;
        }

        /deep/ .ant-input {
          width: 120px;
          height: 24px;
        }

        .ant-input-number {
          width: 120px;
        }
      }

      .ant-form-item-label {
        // height:100%;
        // display: flex;
        // align-items: center;
        // justify-content: flex-end;
        font-family: PingFangSC-Regular, Sans-serif;
        color: #000000;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        background-color: #fafafa;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;

        label {
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .ant-form-item-control-wrapper {
        height: 100%;
        font-family: PingFangSC-Regular, Sans-serif;
        color: #000000;
        font-size: 14px;
        font-weight: 500;

        .ant-form-item-control {
          height: 100%;

          .ant-form-item-children {
            display: block;
            min-height: 34px;
            line-height: 34px;

            .ant-checkbox-wrapper {
              height: 35px;
              line-height: 34px;
            }

            .ant-select-selection--single {
              height: 34px;
              line-height: 34px;
            }

            .ant-select-selection__rendered {
              line-height: 34px;
            }

            .ant-select {
              height: 34px;
            }

            .ant-input {
              height: 34px;
              width: 100%;
            }
          }

          line-height: inherit;
          padding: 2px 10px;
          border-right: 1px solid #ddd;
          border-bottom: 2px solid #ddd;
        }
      }
    }
  }
}
</style>
