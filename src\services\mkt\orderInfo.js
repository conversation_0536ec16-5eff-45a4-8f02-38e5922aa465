import { request, METHOD } from "@/utils/request";

export async function getOrderInfo(id) {
  return request(`/api/app/pcb-order/${id}/by-id`, METHOD.GET);
}
// 新增
export async function getOrderWF() {
  return request(`/api/app/e-mSPcb-order-wf`, METHOD.POST);
}

export async function getSelectOption() {
  return request("/api/app/pcb-order/getselect", METHOD.GET);
}
//获取返单新增数据
export async function prenopeinfo(id) {
  return request(`/api/app/verify-nope-add/${id}/pre-nope-info`, METHOD.GET);
}
//列表接口
export async function emSPcborderlog(id) {
  return request(`/api/app/e-mSPcb-order-log?OrderId=${id}`, METHOD.GET);
}
//订单报价市场修改按钮
export async function setordermodifylist(parmas) {
  return request(`/api/app/engineering-production/set-order-modify-list`, METHOD.POST, parmas);
}
//返单新增保存
export async function preaddnope(val) {
  return request("/api/app/nope-button/pre-add-nope", METHOD.POST, val);
}
//获取从属代码
export async function custgrouplist(id) {
  return request(`/api/app/order-pre-button/${id}/cust-group-list`, METHOD.GET);
}
//获取返单更改数据
export async function nopealter(id) {
  return request(`/api/app/nope-button/${id}/pre-nope-alter-info`, METHOD.GET);
}
//返单更改新增
export async function addnopealter(redata) {
  return request("/api/app/nope-button/pre-add-nope-alter", METHOD.POST, redata);
}
export async function mktConfig(fatory) {
  return request(`/api/app/order-pre2/mkt-config?fatory=${fatory}`, METHOD.GET);
}
export async function selectpars(type, fatory) {
  return request(`/api/app/selection-parameters/select-pars?type=${type}&fatory=${fatory} `, METHOD.GET);
}
export async function getSelectOption1() {
  return request("/api/app/e-mSPcb-order-wf/def-par-info", METHOD.GET);
}
// 获取编辑订单信息
// export async function getEditOrderInfo (id) {
//     return request(`/api/app/pcb-order/${id}/by-change-id`, METHOD.GET)
// }
export async function getEditOrderInfo(id) {
  return request(`/api/app/order-pre/${id}/by-change-id`, METHOD.GET);
}
//销售信息修改接口
export async function SaleOrderInfo(params) {
  return request(`/api/app/verify-nope-add/update-sale`, METHOD.POST, params);
}
//钻带页编辑保存
export async function setpcborderpartableinfo(params) {
  return request(`/api/app/order-pre/set-pcb-order-par-table-info`, METHOD.POST, params);
}
// 编辑保存
// export async function updateOrderInfo(params){
//     return request('/api/app/pcb-order', METHOD.PUT,params)
// }
// export async function updateOrderInfo(params){
//     return request('/api/app/pcb-order/update', METHOD.POST,params)
// }
export async function updateOrderInfo(params) {
  return request("/api/app/order-pre/update", METHOD.POST, params);
}
// 日志参数
export async function orderLog(params) {
  return request(`/api/app/e-mSPcb-order-log?OrderId=${params}`, METHOD.GET);
}
// 跳转拼版开料
export async function autoToolPnl(Id) {
  return request(`/api/app/pcb-order/auto-tool-pnl/${Id}`, METHOD.GET);
}
// 跳转叠层
export async function pcbOrderImp(Id) {
  return request(`/api/app/pcb-order/imp/${Id}`, METHOD.GET);
}
export async function orderpreimp(Id) {
  return request(`/api/app/order-pre/imp/${Id}`, METHOD.GET);
}
export async function deliveryAddress(Id) {
  return request(`/api/app/pcb-order/${Id}/delivery-address`, METHOD.GET);
}
//Lt获取研发项目
export async function rdProject(JoinFactoryId) {
  return request(`/api/app/nope-button/r-dProject/${JoinFactoryId}`, METHOD.GET);
}
//加工工厂获取供应商配置
export async function moduledropdownlist() {
  return request(`api/app/e-mSSupplier-module-no/supplier-module-drop-down-list`, METHOD.GET);
}
//订单锁定
export async function orderlocked(val) {
  return request(`/api/app/nope-button/order-locked`, METHOD.POST, val);
}
export async function adressByPcbId(params) {
  return request(`/api/app/e-mSCust-ship-loction-list/adress-by-pcb-id`, METHOD.POST, params);
}
export async function sheetTraderItems(params) {
  return request(`/api/app/order-pre2/sheet-trader-items?fatory=${params}`, METHOD.GET);
}
export async function boardBrandItems(params) {
  return request(`/api/app/order-pre2/board-brand-items?fatory=${params}`, METHOD.GET);
}
export async function boardtgitems(params) {
  return request(`/api/app/order-pre2/board-tg-items?fatory=${params.factory}&type=${params.type}`, METHOD.GET);
}
export async function coretypeverdors(params) {
  return request(`/api/app/order-pre2/core-type-verdors?fatory=${params}`, METHOD.GET);
}
export async function specFileInfo(BOID) {
  return request(`/api/app/sight/spec-file-info?BOID=${BOID}`, METHOD.GET);
}
// export  async function changeCheck(Id) {
//     return request(`/api/app/pcb-order/change-check/${Id}`, METHOD.POST,)
// }
export async function changeCheck(Id) {
  return request(`/api/app/order-pre/change-check/${Id}`, METHOD.POST);
}
// export  async function drillInfo(Id) {
//     return request(`/api/app/pcb-order/drill-info/${Id}`, METHOD.GET,)
// }
export async function drillInfo(Id) {
  return request(`/api/app/order-pre/drill-info/${Id}`, METHOD.GET);
}
export async function copperthickconversion(factory) {
  return request(`api/app/order-pre2/copper-thick-conversion/${factory}`, METHOD.GET);
}
// export  async function lineInfo(Id) {
//     return request(`/api/app/pcb-order/line-info/${Id}`, METHOD.GET,)
// }
export async function lineInfo(Id) {
  return request(`/api/app/order-pre/line-info/${Id}`, METHOD.GET);
}
export async function requiredLinkConfig(Id, type) {
  return request(`/api/app/pcb-order-list/required-link-config/${Id}?type=${type}`, METHOD.GET);
}
//订单预审获取叠层信息
export async function stackupimpinfo(Id) {
  return request(`/api/app/order-pre2/stack-up-imp-info/${Id}`, METHOD.GET);
}
//上传开料拼版图
export async function preuploadpBT(id, file) {
  return request(`/api/app/order-pre-button/${id}/upload-pBT`, METHOD.POST, file);
}
//差异预览list
export async function checkbilldif(BOID1, BOID2) {
  return request(`api/app/sight/check-bill-dif?BOID1=${BOID1}&BOID2=${BOID2}`, METHOD.GET);
}
export default {
  checkbilldif,
  getOrderInfo,
  boardtgitems,
  coretypeverdors,
  getSelectOption,
  getSelectOption1,
  getEditOrderInfo,
  updateOrderInfo,
  orderLog,
  getOrderWF,
  mktConfig,
  deliveryAddress,
  prenopeinfo,
  emSPcborderlog,
  setordermodifylist,
  preaddnope,
  nopealter,
  addnopealter,
  orderlocked,
  stackupimpinfo,
};
