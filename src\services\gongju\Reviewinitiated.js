import { request, METHOD } from "@/utils/request";
//评审发起列表
export async function engreviewlist(params) {
  return request("/api/app/e-mSReview-main/eng-review-list", METHOD.GET, params);
}
//评审策划列表
export async function reviewplanlist(params) {
  return request("/api/app/e-mSReview-main/review-plan-list", METHOD.GET, params);
}
//评审策划--退回
export async function reviewplanback(OrderId, Reason) {
  return request(`/api/app/e-mSReview-main/review-plan-back/${OrderId}?Reason=${Reason}`, METHOD.POST);
}
//评审总结--退回
export async function reviewsumupback(OrderId, Reason) {
  return request(`/api/app/e-mSReview-main/review-sum-up-back/${OrderId}?Reason=${Reason}`, METHOD.POST);
}
//评审策划--评审总结  完结
export async function allreviewfinish(OrderId, Reason) {
  return request(`/api/app/e-mSReview-main/all-review-finish/${OrderId}?Reason=${Reason}`, METHOD.POST);
}
//评审发起--退回
export async function engreviewback(OrderId, Reason) {
  return request(`/api/app/e-mSReview-main/review-back/${OrderId}?Reason=${Reason}`, METHOD.POST);
}
//评审发起--删除
export async function reviewdelete(OrderId) {
  return request(`/api/app/e-mSReview-main/review-delete/${OrderId}`, METHOD.POST);
}
//评审内容 -- 提交评审
export async function allreviewsubmit(OrderId, ButtonName) {
  return request(`/api/app/e-mSReview-main/all-review-submit/${OrderId}?ButtonName=${ButtonName}`, METHOD.POST);
}
//评审内容 签名接口
export async function setreviewsignname(params) {
  return request(`/api/app/e-mSReview-main/set-review-sign-name`, METHOD.POST, params);
}
//评审总结列表
export async function reviewsumuplist(params) {
  return request("/api/app/e-mSReview-main/review-sum-up-list", METHOD.GET, params);
}
//评审查询列表
export async function reviewsearchlist(params) {
  return request("/api/app/e-mSReview-main/review-search-list", METHOD.GET, params);
}
//根据一级评审类别获取二级评审类别下拉
export async function reviewcategorylist(JoinFactoryId, params) {
  return request(`/api/app/e-mSReview-main/review-category-list/${JoinFactoryId}`, METHOD.GET, params);
}
//评审按钮检查
export async function reviewbuttoncheck(Id, buttonname) {
  return request(`/api/app/e-mSReview-main/review-button-check/${Id}?buttonname=${buttonname}`, METHOD.POST);
}
//评审内容获取评审人员下拉
export async function emSReviewmainuserlist(FactoryId) {
  return request(`/api/app/e-mSReview-main/user-list/${FactoryId}`, METHOD.GET);
}
//评审内容页面统一退回接口
export async function allreviewback(OrderId, ButtonName, Reason) {
  return request(`/api/app/e-mSReview-main/all-review-back/${OrderId}?ButtonName=${ButtonName}&Reason=${Reason}`, METHOD.POST);
}
//评审查询导出EXCEL
export async function reviewexportexcel(params) {
  return request("/api/app/e-mSReview-main/review-export-excel", METHOD.POST, params);
}
