<template>
  <a-spin :spinning="spinning">
    <div class="box">
      <a-form-model layout="inline" style="width: 100%; border-left: 1px solid #ddd; margin-top: 10px; border-top: 1px solid #ddd">
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="阻焊颜色" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1"
                ><b style="color: red"> [顶] </b> {{ proOrderSolderDto.solderColorStr }} <b style="color: green"> [底] </b>
                {{ proOrderSolderDto.solderColorBottomStr }}
              </span>
              <div v-else>
                <b style="color: red"> [顶] </b>
                <a-select
                  v-model="proOrderSolderDto.solderColor"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="solderColor"
                >
                  <a-select-option v-for="item in mapKey(selectData.SolderColor)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <b style="color: green"> [底] </b>
                <a-select v-model="proOrderSolderDto.solderColorBottom" style="width: 90px" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.SolderColor)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="阻焊油墨" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.solderResistInkStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderSolderDto.solderResistInk"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="setEstimate($event, mapKey(selectData.SolderResistInk))"
                  @search="handleSearch($event, mapKey(selectData.SolderResistInk))"
                  @blur="handleBlur($event, mapKey(selectData.SolderResistInk))"
                >
                  <a-select-option v-for="item in mapKey(selectData.SolderResistInk)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="阻焊油墨特性" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.solderCharacteristicsStr }}</span>
              <div v-else>
                <a-select v-model="proOrderSolderDto.solderCharacteristics" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.SolderCharacteristics)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="6">
            <a-form-model-item label="基材油墨厚度" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" >
              <span v-if="!editFlg1">{{proOrderSolderDto.solderThickness}}</span>
              <div  v-else>
                <a-input v-model="proOrderSolderDto.solderThickness" allowClear> </a-input>
              </div>
            </a-form-model-item>
          </a-col> -->
        </a-row>

        <a-row>
          <!-- <a-col :span="6">
            <a-form-model-item label="过孔处理" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{proOrderSolderDto.solderCoverStr}}</span>
              <div  v-else>
                <a-select v-model="proOrderSolderDto.solderCover" showSearch allowClear optionFilterProp="lable" >
                <a-select-option  v-for="(item) in mapKey(selectData.SolderCover)" :key="item.value" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
                </a-select>
              </div>
           
            </a-form-model-item>
          </a-col> -->
          <!-- <a-col :span="6">
            <a-form-model-item label="塞孔工具" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{proOrderSolderDto.plugOilToolStr}}</span>
              <div  v-else>
                <a-select v-model="proOrderSolderDto.plugOilTool" showSearch allowClear optionFilterProp="lable" v-if="proOrderSolderDto.solderCover == 'plugoil'" >
                <a-select-option  v-for="(item) in mapKey(selectData.PlugOilTool)" :key="item.value" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
                </a-select>
                <a-select v-model="proOrderSolderDto.plugOilTool" showSearch allowClear optionFilterProp="lable" v-else  disabled>
                <a-select-option  v-for="(item) in mapKey(selectData.PlugOilTool)" :key="item.value" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col> -->
          <!-- <a-col :span="6">
            <a-form-model-item label="塞孔面次" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{proOrderSolderDto.plugOilSideStr}}</span>
              <div  v-else>
                <a-select v-model="proOrderSolderDto.plugOilSide" showSearch allowClear optionFilterProp="lable" v-if="proOrderSolderDto.solderCover == 'plugoil'">
                <a-select-option  v-for="(item) in mapKey(selectData.PlugOilSide)" :key="item.value" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
                </a-select>
                <a-select v-model="proOrderSolderDto.plugOilSide" showSearch allowClear optionFilterProp="lable" v-else disabled>
                <a-select-option  v-for="(item) in mapKey(selectData.PlugOilSide)" :key="item.value" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col> -->
          <!-- <a-col :span="6">
            <a-form-model-item label="线角油墨厚度" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" >
              <span v-if="!editFlg1">{{proOrderSolderDto.footLineInkThickness}}</span>
              <div  v-else>
                <a-input v-model="proOrderSolderDto.footLineInkThickness" allowClear > </a-input>
              </div>
            </a-form-model-item>
          </a-col> -->

          <!-- <a-col :span="6">
            <a-form-model-item label="字符油墨" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{proOrderSolderDto.minSolderBridgeStr}}</span>
              <div  v-else>
                <a-select v-model="proOrderSolderDto.minSolderBridge" >
                <a-select-option  v-for="(item) in mapKey(selectData.MinSolderBridge)" :key="item.value" :value="item.value">
                  {{ item.lable }}
                </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col> -->
        </a-row>
        <a-row>
          <!-- <a-col :span="6">
            <a-form-model-item label="最小阻焊桥mm" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{proOrderSolderDto.minSolderBridgeStr}}</span>
              <div  v-else>
                <a-select v-model="proOrderSolderDto.minSolderBridge" showSearch allowClear optionFilterProp="lable"
                @change="setEstimate2($event,  mapKey(selectData.MinSolderBridge))"
                @search="handleSearch2($event, mapKey(selectData.MinSolderBridge))"
                @blur="handleBlur2($event,  mapKey(selectData.MinSolderBridge))"
                >
                <a-select-option  v-for="(item) in mapKey(selectData.MinSolderBridge)" :key="item.value" :value="item.value" :lable="item.lable">
                  {{ item.lable }}
                </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-model-item :wrapper-col="{ span: 24 }"> </a-form-model-item>
          </a-col>
          <!-- <a-col :span="6">
            <a-form-model-item label="铜面油墨厚度" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" >
              <span v-if="!editFlg1">{{proOrderSolderDto.cuInkThickness}}</span>
              <div  v-else>
                <a-input v-model="proOrderSolderDto.cuInkThickness" allowClear> </a-input>
              </div>
            </a-form-model-item>
          </a-col> -->
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="字符颜色" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1"
                ><b style="color: red"> [顶] </b> {{ proOrderSolderDto.fontColorStr }} <b style="color: green"> [底] </b>
                {{ proOrderSolderDto.fontColorBottomStr }}</span
              >
              <div v-else>
                <b style="color: red"> [顶] </b>
                <a-select
                  v-model="proOrderSolderDto.fontColor"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="fontColor"
                >
                  <a-select-option v-for="item in mapKey(selectData.FontColor)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <b style="color: green"> [底] </b>
                <a-select v-model="proOrderSolderDto.fontColorBottom" style="width: 90px" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.FontColor)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="字符油墨" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.characterResistInkStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderSolderDto.characterResistInk"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="setEstimate1($event, mapKey(selectData.CharacterResistInk))"
                  @search="handleSearch1($event, mapKey(selectData.CharacterResistInk))"
                  @blur="handleBlur1($event, mapKey(selectData.CharacterResistInk))"
                >
                  <a-select-option v-for="item in mapKey(selectData.CharacterResistInk)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="字符油墨特性" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.characterInkCharacteristicsStr }}</span>
              <div v-else>
                <a-select v-model="proOrderSolderDto.characterInkCharacteristics" showSearch allowClear optionFilterProp="lable">
                  <a-select-option
                    v-for="item in mapKey(selectData.CharacterInkCharacteristics)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="字符上表面处理" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.isCharacterUpSurface ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderSolderDto.isCharacterUpSurface"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="是否有白油块" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.isWhiteOilBlock ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderSolderDto.isWhiteOilBlock"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="字符网板印刷" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.isCharacterScreenPrinting ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderSolderDto.isCharacterScreenPrinting"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="印碳油" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.isCarbonOil ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderSolderDto.isCarbonOil"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="UL类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="sty">
              <span v-if="!editFlg1">{{ proOrderSolderDto.ulTypeStr }}</span>
              <div v-else>
                <a-select v-model="proOrderSolderDto.ulType" mode="multiple" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.ULType)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="标记位置" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="sty">
              <span v-if="!editFlg1">{{ proOrderSolderDto.markPositionStr }}</span>
              <div v-else>
                <a-select v-model="proOrderSolderDto.markPosition" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.MarkPosition)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="标记面向" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.markFaceStr }}</span>
              <div v-else>
                <a-select v-model="proOrderSolderDto.markFace" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.MarkFace)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="标记类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="sty">
              <span v-if="!editFlg1">{{ proOrderSolderDto.markTypeStr }}</span>
              <div v-else>
                <a-select v-model="proOrderSolderDto.markType" mode="multiple" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.MarkType)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="周期格式" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <span v-if="!editFlg1">{{ proOrderSolderDto.periodicFormatStr }}</span>
              <div v-else>
                <a-select v-model="proOrderSolderDto.periodicFormat" showSearch allowClear optionFilterProp="lable">
                  <a-select-option v-for="item in mapKey(selectData.PeriodicFormat)" :key="item.value" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <div class="bto">
        <!-- <a-button @click="editClick" type="primary" style="margin-top:20px;margin-right:10px;" v-if="!editFlg1" >编辑</a-button>
        <a-button @click="editClick" type="primary" style="margin-top:20px;margin-right:10px;" v-else >取消</a-button>
        <a-button @click="saveClick" type="primary" style="margin-top:20px;margin-right:10px;"  >保存</a-button> -->
      </div>
    </div>
  </a-spin>
</template>
<script>
import { solderInformation } from "@/services/projectIndicate";
export default {
  name: "SolderScreen",
  props: ["proOrderSolderDto", "selectData", "editFlg1"],
  data() {
    return {
      spinning: false,
    };
  },
  methods: {
    editClick() {
      this.editFlg1 = !this.editFlg1;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    saveClick() {
      if (!this.editFlg1) {
        this.$message.warning("非编辑状态不可保存");
        return;
      }
      let params = this.proOrderSolderDto;
      solderInformation(params).then(res => {
        if (res.code) {
          this.$emit("GetProOrderInfo");
          this.$message.success("保存成功");
          this.editFlg1 = false;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    solderColor() {
      this.proOrderSolderDto.solderColorBottom = this.proOrderSolderDto.solderColor;
    },
    fontColor() {
      this.proOrderSolderDto.fontColorBottom = this.proOrderSolderDto.fontColor;
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate(value, list) {
      this.proOrderSolderDto.solderResistInk = value;
      let a = this.getPrice(this.proOrderSolderDto.solderResistInk, list, value);
      console.log("阻焊油墨", a, this.proOrderSolderDto.solderResistInk, value);
    },
    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
    },
    setEstimate1(value, list) {
      this.proOrderSolderDto.characterResistInk = value;
      let a = this.getPrice(this.proOrderSolderDto.characterResistInk, list, value);
      console.log("字符油墨", a, this.proOrderSolderDto.characterResistInk, value);
    },
    handleSearch1(value, list) {
      this.setEstimate1(value, list);
    },
    handleBlur1(value, list) {
      this.setEstimate1(value, list);
    },
    setEstimate2(value, list) {
      this.proOrderSolderDto.minSolderBridge = value;
      let a = this.getPrice(this.proOrderSolderDto.minSolderBridge, list, value);
      console.log("最小阻焊桥", a, this.proOrderSolderDto.minSolderBridge, value);
    },
    handleSearch2(value, list) {
      this.setEstimate2(value, list);
    },
    handleBlur2(value, list) {
      this.setEstimate2(value, list);
    },
  },
};
</script>
<style scoped lang="less">
.sty {
  /deep/.ant-form-item-children {
    height: 28px;
    .ant-select-selection__rendered {
      height: 32px;
    }
  }

  /deep/.ant-select ul {
    display: flex;
  }
}
.box {
  // height:760px;
  height: 590px;
  position: relative;
  .bto {
    position: absolute;
    bottom: -48px;
    right: 374px;
  }
}
/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;

  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 28px;
    .ant-select {
      width: 120px;
    }

    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 12px/1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #fafafa;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 12px/1.17 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 12px/1.17 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        display: block;
        min-height: 28px;
        line-height: 26px;
        .ant-checkbox-wrapper {
          height: 28px;
        }
        .ant-select-selection--single {
          height: 28px;
        }
        .ant-select-selection__rendered {
          line-height: 28px;
        }
        .ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
          height: 21px;
          margin-top: 2px;
          line-height: 20px;
        }
        .ant-select-selection--multiple {
          height: 28px;
          min-height: 28px;
        }
        .ant-select {
          height: 28px;
        }
        .ant-input {
          height: 28px;
        }
      }
      line-height: inherit;
      padding: 2px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
    }
  }
}
</style>
