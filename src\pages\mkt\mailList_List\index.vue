<!-- 市场管理 - 邮件列表 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend" ref="SelectBox">
      <div class="content">
        <a-select
          v-model="formData.joinFactoryId"
          @keyup.enter.native="searchClick"
          showSearch
          allowClear
          optionFilterProp="lable"
          placeholder="工厂"
          style="width: 120px; margin-right: 0.5%"
        >
          <a-select-option
            style="color: blue"
            v-for="(item, index) in mapKey(factroyList)"
            :key="index"
            :value="item.value"
            :lable="item.lable"
            :title="item.lable"
          >
            {{ item.lable }}
          </a-select-option>
        </a-select>
        <a-select
          v-model="formData.mailTo"
          @keyup.enter.native="searchClick"
          showSearch
          allowClear
          optionFilterProp="lable"
          placeholder="邮箱"
          style="width: 200px; margin-right: 0.5%"
        >
          <a-select-option
            style="color: blue"
            v-for="(item, index) in Emaillist"
            :key="index"
            :value="item.userName"
            :lable="item.userName"
            :title="item.userName"
          >
            {{ item.userName }}
          </a-select-option>
        </a-select>
        <a-input
          placeholder="主题"
          v-model.trim="formData.Subject"
          :title="formData.Subject"
          style="width: 250px; margin-right: 0.5%"
          allowClear
          @keyup.enter.native="searchClick"
        ></a-input>
        <a-button type="primary" @click="searchClick" style="margin-right: 0.5%">搜索</a-button>
        <!-- <a-button type="primary" @click="EmailReply" style="margin-right:0.5%;float: right;margin-top: 8px;">邮件回复</a-button> -->
        <a-dropdown>
          <a-button type="primary" class="box" style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px" @click.prevent
            >更多功能<a-icon class="lockclass" type="down-circle"></a-icon>
          </a-button>
          <template #overlay>
            <a-menu class="tabRightClikBox3">
              <a-menu-item @click="Replyemail('single')">回复</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button type="primary" @click="Replyemail('all')" style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          >回复全部</a-button
        >
        <a-button type="primary" style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px" @click="invalidemail">标记完成</a-button>
        <a-button
          type="primary"
          style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          v-if="checkPermission('MES.MarketModule.MailList.CreateOrderByMail')"
          @click="CreateOrder"
          >创建订单</a-button
        >
        <a-button type="primary" @click="emailstart" style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px">开始(S)</a-button>
        <a-button
          type="primary"
          style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          v-if="checkPermission('MES.MarketModule.MailList.ReceiveEmail')"
          @click="receiveemail"
          >邮件接收</a-button
        >
        <a-button
          type="primary"
          @click="Replacecode"
          style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          v-if="checkPermission('MES.MarketModule.MailList.ReplaceCustno')"
          >代码替换</a-button
        >
        <a-button
          type="primary"
          @click="Uploadattachments"
          style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          v-if="checkPermission('MES.MarketModule.MailList.UploadAttachment')"
          >上传附件</a-button
        >
        <a-button
          type="primary"
          @click="downfile"
          style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          v-if="checkPermission('MES.MarketModule.MailList.UploadAttachment')"
          >下载附件</a-button
        >
        <a-button
          type="primary"
          @click="addareturnorder('add')"
          style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          v-if="checkPermission('MES.MarketModule.Check.PreAddNope')"
          >返单新增</a-button
        >
        <a-button
          type="primary"
          @click="addareturnorder('finish')"
          style="margin-right: 0.5%; float: right; margin-top: 8px; padding: 0 10px"
          v-if="checkPermission('MES.MarketModule.Check.CheckEnd')"
          >报价完成</a-button
        >
      </div>
      <div style="height: 100%; display: flex">
        <div class="leftContent" style="width: 25%; display: flex; flex-wrap: wrap; position: relative" ref="tableWrapper">
          <a-list
            :grid="{ gutter: 16, column: 1 }"
            itemKey="id"
            :loading="orderListTableLoading"
            :pagination="showListData.length ? listpagination : false"
            @change="listhandleTableChange"
            :itemLayout="'vertical'"
            :dataSource="showListData"
            :style="{ height: '760px' }"
            class="container"
            :getPopupContainer="() => this.$refs.SelectBox"
            @scroll="handleMouseWheel"
          >
            <template>
              <a-list-item
                v-for="(item, index) in showListData"
                :key="item.id"
                @click="handleClick($event, item)"
                @mousedown="handleMouseDown($event, item, index)"
                @mousemove="handleMouseMove($event, item, index)"
                @mouseup="handleMouseUp($event, item, index)"
                :class="[selectedRowKeysArray.indexOf(item.id) !== -1 ? 'selectedcolor' : '']"
              >
                <div class="list-item-content">
                  <a-list-item-meta :title="getTitle(item)" :description="getTitle1(item)" />
                </div>
              </a-list-item>
            </template>
          </a-list>
          <!-- <div style="margin-left:10px;font-size: 16px;" v-if="orderListData.length">总计 {{orderListData.length}} 条</div> -->
        </div>
        <div style="width: 75%; margin: 10px; margin-bottom: 0; margin-right: 0; display: flex; flex-direction: column; background-color: #ffffff">
          <div v-if="replyVisible">
            <h3 style="padding-left: 7px">主&nbsp;&nbsp;&nbsp;题: {{ retitle }}</h3>
            <a-divider />
            <div class="drawre1" style="background-color: white">
              <a-row>
                <a-col :span="24">
                  <a-textarea allowClear :auto-size="{ minRows: 8, maxRows: 15 }" v-model="maildata.text" autoFocus placeholder="请输入回复正文">
                  </a-textarea>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <div style="height: 64px; width: 500px">
                    <div class="add">
                      <span style="margin: 0 10px; cursor: pointer" @click="clickUpload()"> <a-icon type="paper-clip"></a-icon> 添加附件</span>
                      <a-upload
                        name="file"
                        ref="fileRef"
                        :maxCount="1"
                        :customRequest="httpRequest"
                        :before-upload="beforeUpload"
                        :file-list="fileListdata"
                        @change="handleChange"
                      >
                      </a-upload>
                    </div>
                  </div>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-button type="primary" @click="mailhandleOkMode">发送</a-button>
                  <a-button style="margin-left: 10px" @click="reportHandleCancel1">取消</a-button>
                </a-col>
              </a-row>
            </div>
          </div>
          <div>
            <h3 style="padding-left: 7px">{{ showTitle }}</h3>
            <a-spin :spinning="loading">
              <div class="drawre" v-html="messageList" style="background-color: #ffffff"></div>
            </a-spin>
            <div class="img-box">
              <ul>
                <li
                  v-for="(item, index) in attList"
                  style="position: relative; user-select: none"
                  :title="item.split('attid')[0]"
                  :key="index"
                  @mouseenter="mouseenter(index)"
                  @mouseleave="mouseleave(index)"
                  @click.right="rightClick1($event, item, index)"
                  @click="leftclick($event, item, index)"
                  class="liback"
                >
                  <img v-if="item.toLowerCase().includes('pdf')" style="width: 25px; padding-left: 5px" src="@/assets/icon/pdf.png" />
                  <img
                    v-else-if="
                      item.toLowerCase().includes('jpg') ||
                      item.toLowerCase().includes('png') ||
                      item.toLowerCase().includes('bmp') ||
                      item.toLowerCase().includes('jpeg')
                    "
                    style="width: 25px; padding-left: 5px"
                    src="@/assets/icon/jpg.png"
                  />
                  <img
                    v-else-if="item.toLowerCase().includes('xlsx') || item.toLowerCase().includes('xls')"
                    style="width: 25px; padding-left: 5px"
                    src="@/assets/icon/12.png"
                  />
                  <img v-else-if="item.toLowerCase().includes('txt')" style="width: 25px; padding-left: 5px" src="@/assets/icon/txt.png" />
                  <img v-else-if="item.toLowerCase().includes('tif')" style="width: 25px; padding-left: 5px" src="@/assets/icon/tiff.png" />
                  <img
                    v-else-if="item.toLowerCase().includes('docx') || item.toLowerCase().includes('doc')"
                    style="width: 25px; padding-left: 5px"
                    src="@/assets/icon/docx.png"
                  />
                  <img
                    v-else-if="item.toLowerCase().includes('zip') || item.toLowerCase().includes('rar')"
                    style="width: 25px; padding-left: 5px"
                    src="@/assets/icon/zip.png"
                  />
                  <img v-else-if="item.toLowerCase().includes('csv')" style="width: 25px; padding-left: 5px" src="@/assets/icon/csv.png" />
                  <img v-else style="width: 25px; padding-left: 5px" src="@/assets/icon/inf.png" />
                  <span :style="isHoveringIndex == index ? '' : 'color:#000000;'">
                    {{ item.split("attid")[0] }}
                  </span>
                </li>
              </ul>
            </div>
            <a-menu :style="menuStyle1" v-if="menuVisible1" class="tabRightClikBox">
              <a-menu-item v-if="dbdata.length == 1 && Multiple" @click="download">下载附件</a-menu-item>
              <a-menu-item v-if="dbdata.length == 1 && Multiple" @click="Filereplacement">文件替换</a-menu-item>
            </a-menu>
          </div>
        </div>
      </div>
    </div>
    <a-modal title="按钮检查信息" :visible="dataVisible22" @cancel="dataVisible22 = false" destroyOnClose :maskClosable="false" :width="600" centered>
      <template #footer>
        <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
        <a-button key="back" @click="dataVisible22 = false">取消</a-button>
      </template>
      <div class="class" style="font-size: 16px; font-weight: 500">
        <p v-for="(item, index) in checkData1" :key="index">
          <span v-if="item.error == '1'" style="color: red">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-else style="color: black">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
        </p>
        <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
      </div>
    </a-modal>
    <a-modal title="指示检查信息" :visible="dataVisible2" @cancel="reportHandleCancel2" destroyOnClose centered :maskClosable="false" :width="600">
      <template #footer>
        <a-button key="back" @click="reportHandleCancel2">取消</a-button>
        <a-button key="back1" type="primary" v-if="check" @click="continueClick">继续</a-button>
      </template>
      <div class="class" style="font-size: 16px; font-weight: 500">
        <p v-for="(item, index) in checkData" :key="index">
          <span v-if="item.error == '1'" style="color: red">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-else style="color: black">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
        </p>
        <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
      </div>
    </a-modal>
    <a-modal
      :title="mktbutton == 'add' ? '返单新增' : '报价完成'"
      :visible="returnvisible"
      @cancel="returnvisible = false"
      @ok="returnok"
      ok-text="确定"
      class="xiaoshou"
      :confirmLoading="returnloading"
      cancel-text="取消"
      destroyOnClose
      :maskClosable="false"
      :width="1100"
      centered
    >
      <template #footer>
        <a-button key="back" @click="returnvisible = false">取消</a-button>
        <a-button key="back" @click="returnok('application')" type="primary" v-if="mktbutton == 'add'">应用</a-button>
        <a-button key="back" @click="returnok()" type="primary">确定</a-button>
      </template>
      <a-row>
        <a-col :span="8">
          <a-form-model-item label="客户型号" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
            <a-input v-model="adddata.PcbFileName" placeholder="请输入客户型号" allowClear @keyup.enter="search" :autoFocus="true" />
          </a-form-model-item>
        </a-col>
        <a-col :span="8" v-if="mktbutton == 'add'">
          <a-form-model-item label="生产型号" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
            <a-input v-model="adddata.proOrderNo" placeholder="请输入生产型号" allowClear @keyup.enter="search" />
          </a-form-model-item>
        </a-col>
        <a-col :span="2">
          <a-button type="primary" @click="search" style="margin: 3px 0 0 3px">查询</a-button>
        </a-col>
        <a-col :span="6" v-if="mktbutton == 'add'">
          <a-form-model-item label="数量" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
            <a-input v-model="adddata.num" placeholder="请输入数量" allowClear />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
        style="border-top: 1px solid #efefef; margin-top: 15px; border-right: 1px solid #efefef"
        :columns="returncolumns"
        :scroll="{ y: 700 }"
        :dataSource="tabledata"
        :pagination="false"
        rowKey="id"
        :loading="orderLoading"
        :customRow="onClickRow"
        :rowClassName="tableRowClass"
      ></a-table>
    </a-modal>
    <a-modal
      :title="ttype == '3' ? '替换客户代码' : ttype == '4' ? '上传附件' : '确认弹窗'"
      :visible="dataVisibleMode"
      @cancel="reportHandleCancel1"
      @ok="handleOkMode"
      ok-text="确定"
      cancel-text="取消(C)"
      destroyOnClose
      class="thdm"
      :maskClosable="false"
      :confirmLoading="loadingMode"
      :width="400"
      centered
    >
      <span style="font-size: 16px" v-if="ttype != 3">{{ messageMode }}</span>
      <a-form-model ref="ruleForm1" :model="formState" :label-col="{ span: 6 }" :rules="rules1" :wrapper-col="{ span: 16 }" v-if="ttype == 3">
        <a-form-model-item label="替换代码" ref="custNo" prop="custNo">
          <a-input v-model="formState.custNo" placeholder="请输入替换代码" allowClear @keyup.enter="handleOkMode" :autoFocus="true" />
          <!-- <a-select v-model="formState.custNo">
            <a-select-option v-for="(item,index) in custNolist" :key="index" :value="item.custNo">{{item.custNo}}</a-select-option>
          </a-select> -->
        </a-form-model-item>
      </a-form-model>
      <a-form-model ref="ruleForm2" :model="form" :rules="rules2" v-if="ttype == 4 && showmodal">
        <a-form-model-item label="交期" ref="para4IntDelivery" prop="para4IntDelivery">
          <a-date-picker format="YYYY-MM-DD" placeholder="请选择交期" @change="TimeChange" v-model="form.para4IntDelivery" style="width: 235px">
          </a-date-picker>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="备注填写"
      :visible="listdataVisible"
      @cancel="reportHandleCancel1"
      @ok="listhandleOk"
      destroyOnClose
      :maskClosable="false"
      :width="600"
      centered
      :confirmLoading="confirmLoading"
    >
      <a-form-model ref="ruleForm" :model="formdata" :rules="rules" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
        <a-form-model-item label="市场加急">
          <a-checkbox v-model="formdata.isJiaji" ref="input4" />
        </a-form-model-item>
        <a-form-model-item label="文件名称" ref="custkind" prop="custkind">
          <a-input v-model="formdata.custkind" @focus="inputfocus" />
        </a-form-model-item>
        <a-form-model-item label="交货数量" ref="num" prop="num">
          <a-input v-model="formdata.num" @focus="inputfocus" />
        </a-form-model-item>
        <a-form-model-item label="备注信息" ref="memo" prop="memo">
          <a-textarea v-model="formdata.memo" :auto-size="{ minRows: 4, maxRows: 10 }" @focus="inputfocus" />
        </a-form-model-item>
        <a-form-model-item class="create">
          <div>
            <span style="margin: 0 10px" @click="clickUpload1()"> <a-icon type="paper-clip"></a-icon> 添加附件</span>
            <span style="margin: 0 10px; margin-left: 40px; cursor: pointer" @click.stop="showCopy()">
              <a-icon type="scissor"></a-icon> 点击后可ctrl+V粘贴上传</span
            >
            <a-upload name="file" ref="fileRef1" :multiple="true" :customRequest="httpRequest1" :file-list="FileList" @change="handleChange1">
            </a-upload>
          </div>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      title="邮件回复"
      :visible="maildataVisible"
      @cancel="reportHandleCancel1"
      @ok="mailhandleOkMode"
      ok-text="确定"
      cancel-text="取消"
      destroyOnClose
      :maskClosable="false"
      :width="600"
      centered
    >
      <a-form>
        <a-row>
          <a-col :span="24" class="hstyle">
            <a-form-item label="主题" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
              <a-input allowClear v-model="maildata.theme"> </a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="正文" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
              <a-textarea allowClear :auto-size="{ minRows: 6, maxRows: 15 }" v-model="maildata.text" autoFocus> </a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <div style="height: 108px; width: 500px">
              <div>
                <span style="margin: 0 120px" @click="clickUpload()" v-if="fileListdata.length < 4">
                  <a-icon type="paper-clip"></a-icon> 添加附件</span
                >
                <a-upload
                  name="file"
                  ref="fileRef"
                  :maxCount="1"
                  :customRequest="httpRequest"
                  :before-upload="beforeUpload"
                  :file-list="fileListdata"
                  @change="handleChange"
                >
                </a-upload>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </a-spin>
</template>
<script>
import $ from "jquery";
import { prenopeinfo, preaddnope } from "@/services/mkt/orderInfo";
import { buttonCheck } from "@/services/mkt/OrderManagement.js";
import {
  verifyPageList,
  result4ParameterList,
  setIsProduction,
  verifyfinishorders,
  verifyFinishedOrder,
  timedautomaticsendorder,
  indicationCheck,
  settoolcheck,
} from "@/services/mkt/OrderReview.js";
import axios from "axios";
import { Base64 } from "js-base64";
import moment from "moment";
import {
  pageList,
  receiveEmail,
  eMimeOrder,
  consumeorder,
  mailStatslist,
  emimestart,
  attList,
  setmailcustno,
  uploadattachment,
  updatefilepro,
  downloadbyattid,
  createorderbyatt,
  replymessage,
  emaillist,
  emimeconent,
  emimeorderv2,
  emailcustno,
  downloademail,
  byupdatedeliverydate,
} from "@/services/mkt/MailList";
import { checkPermission } from "@/utils/abp";
import { factroyList } from "@/services/dataPage";
import { modulenouploadfile } from "@/services/supplier/index";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 50,
  },
  {
    title: "工厂",
    width: 80,
    align: "left",
    className: "userStyle",
    dataIndex: "joinFactoryName",
    ellipsis: true,
  },
  {
    title: "客户代码",
    width: 70,
    align: "left",
    className: "userStyle",
    dataIndex: "custNo",
    ellipsis: true,
  },
  {
    title: "处理人",
    width: 60,
    align: "left",
    className: "userStyle",
    dataIndex: "processedName",
    ellipsis: true,
  },
  {
    title: "主题",
    dataIndex: "subject",
    align: "left",
    width: 800,
    className: "userStyle",
    scopedSlots: { customRender: "subject" },
    ellipsis: true,
  },
  {
    title: "附件",
    width: 40,
    dataIndex: "hasAttachments",
    align: "center",
    ellipsis: true,
    scopedSlots: { customRender: "hasAttachments" },
  },
  {
    title: "已处理",
    width: 60,
    align: "center",
    className: "userStyle",
    customRender: (text, record, index) => `${record.consumed == true ? "是" : record.consumed == false ? "否" : "空"}`,
    ellipsis: true,
  },
  {
    title: "创建时间",
    width: 160,
    dataIndex: "createTime",
    className: "userStyle",
    align: "left",
    ellipsis: true,
  },
];
const columns1 = [
  {
    title: "工厂",
    width: 120,
    dataIndex: "joinFactoryName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
  },
  {
    title: "数量",
    width: 120,
    dataIndex: "mailNum",
    className: "userStyle",
    align: "left",
    ellipsis: true,
  },
];
const returncolumns = [
  {
    title: "序号",
    width: 45,
    dataIndex: "index",
    align: "center",
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    width: 100,
    sorter: (a, b) => {
      return a.proOrderNo.localeCompare(b.proOrderNo);
    },
  },
  {
    title: "订单号",
    align: "left",
    ellipsis: true,
    width: 100,
    dataIndex: "orderNo",
    sorter: (a, b) => {
      return a.orderNo.localeCompare(b.orderNo);
    },
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 60,
    sorter: (a, b) => {
      return a.custNo.localeCompare(b.custNo);
    },
  },
  {
    title: "客户型号",
    align: "left",
    ellipsis: true,
    width: 260,
    dataIndex: "customerModel",
  },
  {
    title: "上传时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 130,
  },
  {
    title: "订单类型",
    dataIndex: "reOrder",
    align: "left",
    ellipsis: true,
    width: 55,
    customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
    sorter: (a, b) => {
      return a.reOrder.toString().localeCompare(b.reOrder.toString());
    },
  },
  {
    title: "状态",
    align: "left",
    ellipsis: true,
    width: 45,
    dataIndex: "status",
  },
];
export default {
  name: "",
  components: {},
  inject: ["reload"],
  data() {
    return {
      guid: "",
      isMovedown: false,
      startPosition: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      mktbutton: "",
      mailid: "",
      formState: {
        custNo: null,
      },
      loadingMode: false,
      returncolumns,
      tabledata: [],
      orderLoading: false,
      form: { para4IntDelivery: null },
      mailbox: "",
      confirmLoading: false,
      custNolist: [],
      formdata: {
        custkind: "",
        memo: "",
        num: "",
        isJiaji: false,
      },
      rules: {
        custkind: [{ required: true, message: "请输入文件名称", trigger: "blur" }],
        num: [{ pattern: /^(\d|[1-9]\d+)(\.\d+)?$/, message: "交货数量请输入正数", trigger: "blur" }],
      },
      rules1: {
        custNo: [{ required: true, message: "请输入需要替换的客户代码", trigger: "blur" }],
      },
      rules2: {
        para4IntDelivery: [{ required: true, message: "请选择交期", trigger: "blur" }],
      },
      dbdata: [],
      Multiple: false,
      isfullReply: false,
      attid: "",
      attfile: [],
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      menuVisible1: false,
      maildata: {
        theme: "",
        CCrecipient: "",
        text: "",
      },
      showText: false,
      fileListdata: [],
      FileList: [],
      path: [],
      messageList: "",
      showTitle: "主题",
      retitle: "",
      dataopen: false,
      text: "",
      isDragging: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      menuData: {},
      selectdata: {},
      startIndex: -1,
      shiftKey: false,
      spinning: false,
      formData: {
        Subject: "",
        joinFactoryId: undefined,
        mailTo: undefined,
        effective: "全部",
      },
      columns,
      columns1,
      orderListTableLoading: false,
      listpagination: {
        current: 1,
        size: "small",
        pageSize: 10,
        showQuickJumper: false,
        showSizeChanger: false,
        onChange: this.listhandleTableChange,
        onShowSizeChange: this.listhandleTableChange,
        pageSizeOptions: ["10"], // 每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      Statslist: [],
      orderListData: [], //总数据
      showListData: [], //显示的数据
      treePageSize: 10,
      scrollPage: 1,
      dataVisible1: false,
      filePaths: "",
      selectedId: "",
      dataVisibleMode: false,
      listdataVisible: false,
      replyVisible: false,
      maildataVisible: false,
      orderno: "",
      businessOrderNo: "",
      messageMode: "",
      isFileType: false,
      ttype: "",
      pageshow: true,
      selectedRowKeysArray: [],
      returnvisible: false,
      returnloading: false,
      frontDataZSupplier1: [],
      count: 0,
      adddata: {},
      dataVisible22: false,
      rowdata: {},
      allid: [],
      dataVisible2: false,
      checkData: [], // 指示检查数据
      check: false,
      checkType: "",
      dataSource2: [],
      popupdata: {},
      checkData1: [], // 指示检查数据
      check1: false,
      factroyList: [],
      Emaillist: [],
      isCtrlPressed: false,
      attList: [], //附件列表
      showmodal: false,
      isHoveringIndex: -1,
      loading: false,
    };
  },
  async mounted() {
    await emaillist().then(res => {
      if (res.code) {
        this.Emaillist = res.data;
        if (this.Emaillist.length) {
          this.formData.mailTo = this.Emaillist.filter(ite => ite.def == true)[0]?.userName;
        }
      } else {
        this.$message.error(res.message);
      }
    });
    this.getOrderList(this.formData);
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
    factroyList().then(res => {
      if (res.code) {
        this.factroyList = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("paste", this.getClipboardFiles);
  },
  methods: {
    checkPermission,
    continueClick() {
      this.dataVisible2 = false;
      this.orderLoading = true;
      this.returnloading = true;
      if (this.checkType == "bjwc") {
        verifyFinishedOrder(this.allid[this.count])
          .then(res => {
            if (res.code) {
              settoolcheck(this.allid[this.count]).then(res => {});
              if (this.rowdata.joinFactoryId == 38) {
                timedautomaticsendorder(this.rowdata.joinFactoryId).then(res => {});
              }
              if (this.count + 1 == this.allid.length) {
                this.$message.success("完成");
                consumeorder(this.selectedRowKeysArray).then(res => {
                  if (res.code) {
                    this.$message.success(res.message);
                    this.formData.Subject = "";
                    (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                    this.messageList = "";
                    this.showTitle = "主题";
                    this.attList = [];
                    this.dbdata = [];
                  } else {
                    this.$message.error(res.message);
                  }
                });
                this.returnvisible = false;
              }
              this.allid[this.count] = "";
              this.dataSource2 = [];
              return this.Quotationcompleted(this.count + 1);
            } else {
              if (res.data && res.data.length) {
                this.checkData = res.data;
                this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                this.dataVisible2 = true;
              } else {
                this.$message.error(res.message);
              }
              this.orderLoading = false;
              this.returnloading = false;
            }
          })
          .finally(() => {
            if (this.count + 1 == this.allid.length) {
              this.orderLoading = false;
              this.returnloading = false;
            }
          });
      }
    },
    //指示检查弹窗
    reportHandleCancel2() {
      this.dataVisible2 = false;
      return this.Quotationcompleted(this.count + 1);
    },
    checkClick() {
      if (this.checkType == "fdxz") {
        let params = {
          custNo: this.rowdata.custNo,
          delType: this.popupdata.delType,
          boardArea: Number(this.popupdata.boardArea),
          orderAttribute: this.popupdata.orderAttribute,
          testPointNum: Number(this.popupdata.testPointNum),
          openedTestrack: this.popupdata.openedTestrack,
          priceType: this.popupdata.priceType,
          proOrderNo: this.popupdata.proOrderNo,
          deliveryDate: this.popupdata.deliveryDate,
          reOrderNo: this.rowdata.orderNo,
          marketDeliveryTime: this.popupdata.marketDeliveryTime,
          custPo: this.popupdata.custPo,
          contractNumber: this.popupdata.contractNumber,
          flyingProbe: this.popupdata.flyingProbe,
          shipmentTerm: this.popupdata.shipmentTerm,
          num: this.adddata.num,
          orderDirection: this.popupdata.orderDirection,
          customClassification: this.popupdata.customClassification,
          customerModel: this.popupdata.customerModel,
          customerMaterialNo: this.popupdata.customerMaterialNo,
          customerMaterialName: this.popupdata.customerMaterialName,
          mktNote: this.popupdata.mktNote,
          unitPrice: this.popupdata.unitPrice,
          id: this.guid,
          mailId: this.mailid,
        };
        preaddnope(params).then(res => {
          if (res.code) {
            this.$message.success("返单新增成功");
            if (this.addtype == "application") {
              consumeorder(this.selectedRowKeysArray).then(res => {
                if (res.code) {
                  this.$message.success(res.message);
                  this.formData.Subject = "";
                  (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                  this.messageList = "";
                  this.showTitle = "主题";
                  this.attList = [];
                  this.dbdata = [];
                } else {
                  this.$message.error(res.message);
                }
              });
            }
            this.returnvisible = false;
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.guid = record.id;
            this.rowdata = record;
            if (this.mktbutton == "add") {
              this.$set(this.adddata, "num", record.num);
              prenopeinfo(record.id).then(res => {
                if (res.code) {
                  this.popupdata = res.data;
                } else {
                  this.$message.error(res.message);
                }
              });
            } else if (this.mktbutton == "finish") {
              result4ParameterList(this.guid).then(res => {
                if (res.data) {
                  this.dataSource2 = res.data.filter(item => item.tag_ != -1);
                  var index = 0;
                  if (this.dataSource2.findIndex(v => v.isProduction == true) < 0) {
                    index = 0;
                  } else {
                    index = this.dataSource2.findIndex(v => v.isProduction == true);
                  }
                  for (var b = 0; b < this.dataSource2.length; b++) {
                    if (this.dataSource2[b].para4Weight_) {
                      this.dataSource2[b].para4Weight_ = Number(this.dataSource2[b].para4Weight_).toFixed(2);
                    }
                    if (this.dataSource2[b].para4Delivery_) {
                      this.dataSource2[b].para4Delivery_ = moment(moment(this.dataSource2[b].para4Delivery_).format("YYYY-MM-DD"));
                    } else {
                      this.dataSource2[b].para4Delivery_ = null;
                    }
                  }
                }
              });
            }
          },
        },
      };
    },
    tableRowClass(record) {
      let strGroup = [];
      if (record.id && record.id == this.guid) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    search() {
      if (!this.adddata.proOrderNo && !this.adddata.PcbFileName) {
        this.$message.warning("请输入需要查询的参数");
        return;
      }
      this.orderLoading = true;
      let params = {
        PageIndex: 1,
        PageSize: 10,
        proOrderNo: this.adddata.proOrderNo,
        PcbFileName: this.adddata.PcbFileName,
      };
      verifyPageList(params)
        .then(res => {
          if (res.code) {
            this.tabledata = res.data.items;
          } else {
            this.tabledata = [];
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderLoading = false;
        });
    },
    returnok(type) {
      if (this.mktbutton == "add") {
        var r = /^\+?[1-9][0-9]*$/;
        if (this.adddata.num && !r.test(this.adddata.num)) {
          this.$message.warning("返单数量必须为正整数");
          this.returnloading = false;
          return;
        }
        this.addtype = type;
        this.returnloading = true;
        buttonCheck(this.guid, "PreVerifyOrderAddNope").then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData1 = res.data;
              this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
              this.checkType = "fdxz";
              this.dataVisible22 = true;
              this.returnloading = false;
            } else {
              let params = {
                custNo: this.rowdata.custNo,
                delType: this.popupdata.delType,
                boardArea: Number(this.popupdata.boardArea),
                orderAttribute: this.popupdata.orderAttribute,
                testPointNum: Number(this.popupdata.testPointNum),
                openedTestrack: this.popupdata.openedTestrack,
                priceType: this.popupdata.priceType,
                proOrderNo: this.popupdata.proOrderNo,
                deliveryDate: this.popupdata.deliveryDate,
                reOrderNo: this.rowdata.orderNo,
                marketDeliveryTime: this.popupdata.marketDeliveryTime,
                custPo: this.popupdata.custPo,
                contractNumber: this.popupdata.contractNumber,
                flyingProbe: this.popupdata.flyingProbe,
                shipmentTerm: this.popupdata.shipmentTerm,
                num: this.adddata.num,
                orderDirection: this.popupdata.orderDirection,
                customClassification: this.popupdata.customClassification,
                customerModel: this.popupdata.customerModel,
                customerMaterialNo: this.popupdata.customerMaterialNo,
                customerMaterialName: this.popupdata.customerMaterialName,
                mktNote: this.popupdata.mktNote,
                unitPrice: this.popupdata.unitPrice,
                id: this.guid,
                mailId: this.mailid,
              };
              this.returnloading = true;
              preaddnope(params)
                .then(res => {
                  if (res.code) {
                    this.$message.success(res.message);
                    if (this.addtype == "application") {
                      consumeorder(this.selectedRowKeysArray).then(res => {
                        if (res.code) {
                          this.$message.success(res.message);
                          this.formData.Subject = "";
                          (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                          this.messageList = "";
                          this.showTitle = "主题";
                          this.attList = [];
                          this.dbdata = [];
                        } else {
                          this.$message.error(res.message);
                        }
                      });
                    }
                    this.returnvisible = false;
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.returnloading = false;
                });
            }
          } else {
            this.$message.error(res.message);
            this.returnloading = false;
          }
        });
      } else if (this.mktbutton == "finish") {
        if (this.dataSource2.length == 2 && !this.dataSource2[0].isProduction) {
          this.dataSource2[0].isProduction = true;
          setIsProduction(this.dataSource2[0].id).then(res => {
            if (res.code) {
              //
            } else {
              this.$message.error(res.message);
              record.isProduction = false;
              return;
            }
          });
        }
        this.orderLoading = true;
        this.returnloading = true;
        verifyfinishorders(this.guid).then(res => {
          if (res.code) {
            this.allid = res.data;
            this.Quotationcompleted(0);
          } else {
            this.orderLoading = false;
            this.returnloading = false;
          }
        });
      }
    },
    Quotationcompleted(ind) {
      if (ind < this.allid.length) {
        this.orderLoading = true;
        this.returnloading = true;
        this.count = ind;
        indicationCheck(this.allid[ind].id, 1).then(res => {
          if (res.code) {
            this.checkData = res.data;
            this.check = this.checkData.findIndex(v => v.error == "1") < 0;
            if (this.checkData.length == 0) {
              verifyFinishedOrder(this.allid[ind].id)
                .then(res => {
                  if (res.code) {
                    if (this.rowdata.joinFactoryId == 38) {
                      timedautomaticsendorder(this.rowdata.joinFactoryId).then(res => {});
                    }
                    settoolcheck(this.allid[ind].id).then(res => {});
                    if (ind + 1 == this.allid.length) {
                      this.$message.success("完成");
                      consumeorder(this.selectedRowKeysArray).then(res => {
                        if (res.code) {
                          this.$message.success(res.message);
                          this.formData.Subject = "";
                          (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                          this.messageList = "";
                          this.showTitle = "主题";
                          this.attList = [];
                          this.dbdata = [];
                        } else {
                          this.$message.error(res.message);
                        }
                      });
                    }
                    if (this.orderListData.length == 1) {
                      this.getOrderList();
                    } else {
                      const array1 = this.orderListData.findIndex(element => element.id == this.allid[ind].id);
                      this.orderListData.splice(array1, 1);
                    }
                    this.guid = "";
                    this.dataSource2 = [];
                    return this.Quotationcompleted(ind + 1);
                  } else {
                    this.orderLoading = false;
                    this.returnloading = false;
                    if (res.data && res.data.length) {
                      this.checkData = res.data;
                      this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                      this.dataVisible2 = true;
                      this.checkType = "bjwc";
                    } else {
                      this.$message.error(res.message);
                      return this.Quotationcompleted(ind + 1);
                    }
                  }
                })
                .finally(() => {
                  if (ind + 1 == this.allid.length) {
                    this.orderLoading = false;
                    this.returnloading = false;
                  }
                });
            } else {
              this.dataVisible2 = true;
              this.checkType = "bjwc";
            }
          } else {
            this.$message.error(res, message);
            if (ind + 1 == this.allid.length) {
              this.orderLoading = false;
              this.returnloading = false;
            }
          }
        });
      } else {
        this.orderLoading = false;
      }
    },
    addareturnorder(type) {
      if (this.selectedRowKeysArray.length == 0) {
        this.$message.error("请选择订单");
        return;
      }
      if (this.selectedRowKeysArray.length > 1) {
        this.$message.error("该操作只能选择一个订单");
        return;
      }
      this.returnvisible = true;
      this.tabledata = [];
      this.adddata = {};
      this.mktbutton = type;
      setTimeout(() => {
        $(".ant-modal-header").on("mousedown", e => {
          this.startPosition.x = e.pageX;
          this.startPosition.y = e.pageY;
          this.startPosition.offsetX = e.offsetX;
          this.startPosition.offsetY = e.offsetY;
          this.isMovedown = true;
          let a = document.getElementsByClassName("xiaoshou")[0];
          a.style.webkitUserSelect = "none"; // Chrome、Safari 和 Opera
          a.style.mozUserSelect = "none"; // 火狐
          a.style.msUserSelect = "none"; // IE 和 Edge
          a.style.userSelect = "none"; // 标准写法
        });
      }, 200);
      document.body.addEventListener("mousemove", e => {
        if (this.isMovedown) {
          if (
            e.x - this.startPosition.x > 10 ||
            e.y - this.startPosition.y > 10 ||
            e.x - this.startPosition.x < -10 ||
            e.y - this.startPosition.y < -10
          ) {
            let w = $(".ant-modal-content").width();
            let h = $(".ant-modal-content").height();
            $(".ant-modal-content").css({
              left: e.pageX - this.startPosition.offsetX - (document.body.clientWidth - w) / 2 + "px",
              top: e.pageY - (document.body.clientHeight - 3 * h) / 2 - 750 + "px",
            });
          }
        }
      });
      document.body.addEventListener("mouseup", e => {
        this.isMovedown = false;
        let a = document.getElementsByClassName("xiaoshou")[0];
        a.style.webkitUserSelect = "text"; // Chrome、Safari 和 Opera
        a.style.mozUserSelect = "text"; // 火狐
        a.style.msUserSelect = "text"; // IE 和 Edge
        a.style.userSelect = "text"; // 标准写法
      });
    },
    TimeChange(value, dateString) {
      this.form.para4IntDelivery = dateString;
    },
    inputfocus() {
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    bodyClick() {
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    showCopy(event) {
      window.addEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          for (let i = 0; i < items.length; i += 1) {
            file = items[i].getAsFile();
          }
          // if (items[i].type.indexOf('application/pdf') !== -1 || items[i].type.indexOf('application/x-zip-compressed') !== -1
          //     || items[i].type.indexOf('application/x-compressed') !== -1
          //     ||items[i].type.indexOf('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') !== -1
          //     || items[i].type.indexOf('application/vnd.ms-excel') !== -1 || items[i].type.indexOf('application/pdf') !== -1)
          //   if (!file) {
          //   this.$message.error('粘贴内容不是.rar/.zip/.pdf/.xls/.xlsx格式文件');
          //   return;
          // }
          const formData = new FormData();
          formData.append("file", file);
          axios({
            url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSSupplier-module-no/up-load-file", // 接口地址
            method: "post",
            data: formData,
            //headers: this.headers,  // 请求头
          })
            .then(res => {
              if (res.code) {
                file.status = "done";
                file.response = res.data;
                file.thumbUrl = res.data;
                file.url = res.data;
                file.uid = new Date().valueOf();
                const arr = [];
                arr.push({
                  name: file.name,
                  uid: file.uid,
                  response: file.response,
                  size: file.size,
                  status: file.status,
                  type: file.type,
                  thumbUrl: file.thumbUrl,
                  url: file.url,
                });
                this.$message.success("文件粘贴成功");
                this.handleChange1(file, arr);
              } else {
                //
              }
            })
            .catch(() => {
              // this.$message.error('网络异常,请重试或检查网络连接状态');
            });
        }
      }
    },
    clickUpload() {
      if (this.fileListdata.length == 4) {
        this.$message.error("回复邮件最多上传4个附件");
        return;
      } else {
        this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
      }
    },
    clickUpload1() {
      this.$refs.fileRef1.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await modulenouploadfile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.$message.success("文件上传成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    beforeUpload(file) {
      this.isFileType =
        file.name.toLowerCase().indexOf(".zip") != -1 ||
        file.name.toLowerCase().indexOf(".rar") != -1 ||
        file.name.toLowerCase().indexOf(".xls") != -1 ||
        file.name.toLowerCase().indexOf(".pdf") != -1 ||
        file.name.toLowerCase().indexOf(".xlsx") != -1;
      if (!this.isFileType) {
        this.$message.error("添加附件只支持.rar/.zip/.pdf/.xls/.xlsx格式文件");
      }
      return this.isFileType;
    },
    async httpRequest(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await modulenouploadfile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.$message.success("文件上传成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleChange({ fileList }) {
      if (this.isFileType) {
        this.attfile = [];
        this.fileListdata = fileList;
        if (fileList.length) {
          fileList.forEach(item => {
            if (item.response) {
              this.attfile.push(item.response);
            }
          });
        } else {
          this.attfile = [];
        }
      }
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.FileList);
      }
      const removedFiles = this.FileList.filter(file => !fileList.includes(file));
      removedFiles.forEach(file => {
        const removedElement = file.response;
        if (removedElement && this.path.includes(removedElement)) {
          this.path = this.path.filter(element => !element.includes(removedElement));
        }
      });
      this.FileList = fileList;
      for (let index = 0; index < this.FileList.length; index++) {
        const element = this.FileList[index].response;
        if (element && !this.path.includes(element)) {
          this.path.push(element);
        }
      }
      if (this.FileList.length === 0) {
        this.path = [];
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    Drawerdisplay(e, text, record) {
      if (
        record.subject.replace(/[\r\n\s]/g, "") != text.replace(/[\r\n\s]/g, "") &&
        record.body.replace(/[\r\n\s]/g, "") != text.replace(/[\r\n\s]/g, "")
      ) {
        this.dataopen = false;
      } else {
        this.dataopen = true;
      }
    },
    down() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    listhandleTableChange(page, pageSize) {
      if (typeof page == "object") {
        this.listpagination.current = Number(page.target._value);
      } else {
        this.listpagination.current = page;
      }
      if (pageSize) {
        this.listpagination.pageSize = pageSize;
      }

      this.getOrderList(this.formData);
    },
    getOrderList(queryData) {
      let params = {
        PageIndex: this.listpagination.current,
        PageSize: this.listpagination.pageSize,
      };
      if (queryData) {
        params.Subject = queryData.Subject;
        params.joinFactoryId = queryData.joinFactoryId;
        params.mailTo = queryData.mailTo;
      }
      params.effective = "全部";
      this.orderListTableLoading = true;
      pageList(params)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            this.showListData = this.orderListData;
            if (this.orderListData.length) {
              this.selectedRowKeysArray = [this.orderListData[0].id];
              this.getbody(this.orderListData[0]);
              this.selectdata = this.orderListData[0];
              this.attList = [];
            } else {
              this.selectedRowKeysArray = [];
              this.showTitle = "";
              this.messageList = "";
              this.attList = [];
              this.mailid = "";
            }
            this.dbdata = [];
            this.listpagination.total = res.data.totalCount;
            this.listpagination.current = res.data.pageIndex;
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "83" && this.isCtrlPressed) {
        e.preventDefault();
        this.emailstart();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "67" && !this.isCtrlPressed && this.dataVisibleMode) {
        this.reportHandleCancel1();
        e.preventDefault();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && this.dataVisibleMode) {
        this.handleOkMode();
        e.preventDefault();
      }
      // else if (e.keyCode == '13' ){
      //   e.preventDefault()
      // }
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.orderListData.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      this.selectedRowKeysArray = arr;
      this.attfile = [];
      if (startIndex < endIndex) {
        this.selectedRowsData = this.orderListData.filter(item => {
          return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.orderListData.filter(item => {
          return item.id == this.selectedRowKeysArray[0];
        })[0];
      }
      if (this.selectedRowKeysArray.length > 1) {
        this.dataopen = false;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index && this.replyVisible == false) {
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    handleMouseUp(event, record, index) {
      if (this.replyVisible == true) {
        this.$message.error("当前邮件为回复状态，请先回复或取消回复");
        return;
      }
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeysArray;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowKeysArray = rowKeys;
        this.attfile = [];
      } else {
        if (this.startIndex == index) {
          this.selectedRowKeysArray = [record.id];
        }
      }
      this.shiftKey = false;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0 && this.replyVisible == false) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    searchClick() {
      let params = this.formData;
      var arr1 = params.Subject.split("");
      if (arr1.length > 30) {
        arr1 = arr1.slice(0, 30);
      }
      params.Subject = arr1.join("");
      this.pageshow = false;
      this.listpagination.current = 1;
      this.getOrderList(params);
      this.$nextTick(() => {
        this.pageshow = true;
      });
    },
    receiveemail() {
      receiveEmail().then(res => {
        if (res.code) {
          this.$message.success("接收成功");
          this.formData.Subject = "";
          (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //替换客户代码
    Replacecode() {
      if (!this.selectedRowKeysArray.length) {
        this.$message.warning("请选择需要进行替换客户代码的邮件!");
        return;
      }
      if (this.selectedRowKeysArray.length > 1) {
        this.$message.warning("只能选择一条邮件进行替换!");
        return;
      }
      this.dataVisibleMode = true;
      //2024/10/9 下拉 改成 输入
      // emailcustno(this.mailbox).then(res=>{
      //   if(res.code){
      //     this.custNolist=res.data
      //   }
      // })
      this.ttype = 3;
    },
    downfile() {
      if (!this.selectedRowKeysArray.length) {
        this.$message.warning("请选择需要下载的邮件!");
        return;
      }
      if (this.selectedRowKeysArray.length > 1) {
        this.$message.warning("只能选择一条邮件进行下载!");
        return;
      }
      downloademail(this.mailid).then(res => {
        this.downloadExcel(res);
      });
    },
    downloadExcel(res) {
      const blob = new Blob([res], { type: "message/rfc822" });
      const url = window.URL.createObjectURL(blob);
      // 创建<a>标签
      const a = document.createElement("a");
      a.href = url;
      // 从Content-Disposition中获取文件名
      const contentDisposition = res["content-disposition"];
      let fileName = this.selectdata.id + this.selectdata.uid;
      if (contentDisposition) {
        const fileNameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const matches = fileNameRegex.exec(contentDisposition);
        if (matches != null && matches[1]) {
          fileName = matches[1].replace(/['"]/g, "");
        }
      }
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    },

    //上传附件弹窗
    Uploadattachments() {
      if (!this.selectedRowKeysArray.length) {
        this.$message.warning("请选择需要上传附件的邮件!");
        return;
      }
      if (this.selectedRowKeysArray.length > 1) {
        this.$message.warning("只能选择一条邮件进行上传附件!");
        return;
      }
      if (!this.selectdata.custNo) {
        this.$message.warning("该订单无客户代码，不允许上传附件!");
        return;
      }
      byupdatedeliverydate(this.mailid).then(res => {
        if (res.code && res.data) {
          this.showmodal = true;
          this.messageMode = "";
          this.form.para4IntDelivery = res.message;
        } else {
          this.messageMode = "确认上传附件吗?";
          this.showmodal = false;
        }
      });
      this.dataVisibleMode = true;
      this.ttype = 4;
      this.form.para4IntDelivery = null;
    },
    //开始弹窗
    emailstart() {
      if (!this.selectedRowKeysArray.length) {
        this.$message.warning("请至少选择一行记录！");
        return;
      }
      this.dataVisibleMode = true;
      this.ttype = 1;
      this.messageMode = "确定订单开始吗？";
    },
    //文件替换
    Filereplacement() {
      this.dataVisibleMode = true;
      this.ttype = 5;
      this.messageMode = "确定替换文件吗？";
    },
    //邮件回复
    Replyemail(type) {
      if (!this.selectedRowKeysArray.length) {
        this.$message.warning("请选择需要进行回复的邮件!");
        return;
      }
      if (this.selectedRowKeysArray.length > 1) {
        this.$message.warning("只能选择一条邮件进行回复!");
        return;
      }
      let drawre = document.getElementsByClassName("drawre");
      drawre[0].style.height = "290px";
      this.replyVisible = true;
      // this.maildataVisible=true
      if (type == "single") {
        this.isfullReply = false;
      } else {
        this.isfullReply = true;
      }
      this.maildata = {};
      this.attfile = [];
      // this.maildata.theme
      this.retitle = "Re：" + this.showTitle;
      this.fileListdata = [];
    },
    //标记完成弹窗
    invalidemail() {
      if (!this.selectedRowKeysArray.length) {
        this.$message.warning("请至少选择一行记录！");
        return;
      }
      this.dataVisibleMode = true;
      this.ttype = 2;
      this.messageMode = "确定设置为标记完成吗？";
    },
    //邮件回复弹窗
    EmailReply() {
      // if(!this.selectedRowKeysArray.length){
      //   this.$message.warning('请至少选择一行记录！')
      //   return
      // }
      this.maildataVisible = true;
    },
    mailhandleOkMode() {
      let params = {
        strSubject: this.retitle,
        strbody: this.maildata.text,
        atts: this.attfile,
        isfullReply: this.isfullReply,
        id: this.mailid,
        // cc:this.maildata.CCrecipient
      };
      replymessage(params).then(res => {
        if (res.code) {
          this.$message.success("回复成功");
          this.formData.Subject = "";
          (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
          this.attfile = [];
          this.messageList = "";
          this.showTitle = "主题";
          this.attList = [];
          this.dbdata = [];
        } else {
          this.$message.error(res.message);
        }
      });
      //this.maildataVisible=false
      this.replyVisible = false;
      this.attfile = [];
      let drawre = document.getElementsByClassName("drawre");
      drawre[0].style.height = "661px";
    },
    handleOkMode() {
      if (this.ttype == 1) {
        this.loadingMode = true;
        emimestart(this.selectedRowKeysArray)
          .then(res => {
            if (res.code) {
              this.$message.success("开始成功");
              let a = this.showListData.filter(item => item.id == this.selectedRowKeysArray[0])[0];
              a.processedName = true;
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.dataVisibleMode = false;
            this.loadingMode = false;
          });
      }
      if (this.ttype == 2) {
        this.loadingMode = true;
        consumeorder(this.selectedRowKeysArray)
          .then(res => {
            if (res.code) {
              this.$message.success("设置成功");
              this.formData.Subject = "";
              (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
              this.messageList = "";
              this.showTitle = "主题";
              this.attList = [];
              this.dbdata = [];
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.dataVisibleMode = false;
            this.loadingMode = false;
          });
      }
      if (this.ttype == 3) {
        const form = this.$refs.ruleForm1;
        var arr1 = this.formState.custNo.split("");
        if (arr1.length > 30) {
          arr1 = arr1.slice(0, 30);
        }
        this.formState.custNo = arr1.join("");
        form.validate(valid => {
          if (valid) {
            this.loadingMode = true;
            setmailcustno(this.mailid, this.formState.custNo)
              .then(res => {
                if (res.code) {
                  this.formData.Subject = "";
                  (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                  this.messageList = "";
                  this.showTitle = "主题";
                  this.attList = [];
                  this.dbdata = [];
                  this.$message.success("设置成功");
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.dataVisibleMode = false;
                this.loadingMode = false;
              });
          }
        });
      }
      if (this.ttype == 4) {
        if (this.showmodal) {
          const form = this.$refs.ruleForm2;
          form.validate(valid => {
            if (valid) {
              this.loadingMode = true;
              uploadattachment(this.mailid, this.form.para4IntDelivery)
                .then(res => {
                  if (res.code) {
                    this.$message.success("上传附件成功");
                    this.formData.Subject = "";
                    (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                    this.messageList = "";
                    this.showTitle = "主题";
                    this.attList = [];
                    this.dbdata = [];
                  } else {
                    this.$message.error(res.message);
                  }
                })
                .finally(() => {
                  this.dataVisibleMode = false;
                  this.loadingMode = false;
                });
            }
          });
        } else {
          uploadattachment(this.mailid, "")
            .then(res => {
              if (res.code) {
                this.$message.success("上传附件成功");
                this.formData.Subject = "";
                (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                this.messageList = "";
                this.showTitle = "主题";
                this.attList = [];
                this.dbdata = [];
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.dataVisibleMode = false;
            });
        }
      }
      if (this.ttype == 5) {
        updatefilepro(this.mailid, this.attid).then(res => {
          if (res.code) {
            this.formData.Subject = "";
            (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
            this.messageList = "";
            this.showTitle = "主题";
            this.attList = [];
            this.dbdata = [];
            this.$message.success("设置成功");
          } else {
            this.$message.error(res.message);
          }
        });
        this.dataVisibleMode = false;
      }
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    reportHandleCancel1() {
      this.dataVisibleMode = false;
      this.maildataVisible = false;
      this.replyVisible = false;
      this.attfile = [];
      this.listdataVisible = false;
      this.formState.custNo = "";
      this.loadingMode = false;
      window.removeEventListener("paste", this.getClipboardFiles);
      let drawre = document.getElementsByClassName("drawre");
      drawre[0].style.height = "661px";
    },
    getTitle(item) {
      return (
        <div style="display: flex;justify-content: space-between;color:black">
          <div>
            {item.processedName ? (
              <a-icon type="user" style="font-size: 28px;color:#FF9900;"></a-icon>
            ) : (
              <a-icon type="user" style="font-size: 28px;color:#000000;"></a-icon>
            )}
            {item.custNo ? (
              <span style="margin-right: 5px;width:70px;display: inline-block;"> {item.custNo}</span>
            ) : (
              <span style="margin-right: 5px;width:70px;display: inline-block;"></span>
            )}
            {item.isNewCust ? <a-tag class="iconstyle">新</a-tag> : null}
            {item.isJiaji ? <a-tag class="iconstyle">急</a-tag> : null}
            {item.isBigCus ? <a-tag class="iconstyle">KA</a-tag> : null}
            {item.isMunitions ? <a-tag class="iconstyle">军</a-tag> : null}
          </div>
          <div>
            {item.attachmentNums > 0 ? (
              <span style="font-size:20px">
                {" "}
                <a-icon type="paper-clip"></a-icon>{" "}
                <span style="font-size:14px;color: red;position: relative;top: -6px; left: -4px;">{item.attachmentNums}</span>
              </span>
            ) : null}
            {item.createTime ? <span style="font-size:12px;margin-right:6px;"> {item.createTime}</span> : null}
          </div>
        </div>
      );
    },
    getTitle1(item) {
      return (
        <span style="font-size:14px;color:black">
          {item.subject ? (
            <div title={item.subject} style="margin-right: 5px;" class="overflow-ellipsis">
              {item.subject}
            </div>
          ) : null}
        </span>
      );
    },
    handleClick(e, item) {
      if (this.replyVisible == false) {
        this.selectdata = item;
        this.dbdata = [];
        this.getbody(item);
      }
    },
    getbody(item) {
      this.attList = [];
      this.messageList = "";
      this.showTitle = "";
      this.mailid = item.id;
      this.loading = true;
      emimeconent(item.id)
        .then(res => {
          if (res.code) {
            if (res.data.atts.length) {
              res.data.atts.forEach(ite => {
                this.attList.push(ite.attName + "attid" + ite.id);
              });
            }
            this.mailbox = res.data.from;
            this.showTitle = res.data.subject;
            this.retitle = "Re：" + res.data.subject;
            this.messageList = Base64.decode(res.data.htmlBody);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleMouseWheel(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage;
          const treePageSize = this.treePageSize * (scrollPage || 1);
          const newData = [];
          let max = "";
          if (this.orderListData.length > treePageSize) {
            max = treePageSize;
          } else {
            max = this.orderListData.length;
          }
          this.orderListData.forEach((item, index) => {
            if (index < max) {
              newData.push(item);
            }
          });

          this.showListData = newData;
        }
      }
    },
    mouseenter(index) {
      this.isHoveringIndex = index;
    },
    mouseleave(index) {
      this.isHoveringIndex = -1;
    },
    leftclick(e, item, index) {
      if (this.dbdata.indexOf(item.split("attid")[1]) != -1) {
        this.dbdata.splice(this.dbdata.indexOf(item.split("attid")[1]), 1);
      } else {
        this.dbdata.push(item.split("attid")[1]);
      }
      let st = document.getElementsByClassName("liback");
      if (st[index].style.backgroundColor == "rgb(171, 171, 171)") {
        st[index].style.backgroundColor = "#E0E2E4";
        st[index].style.border = "none";
      } else {
        st[index].style.backgroundColor = "rgb(171, 171, 171)";
        st[index].style.border = "2px solid #ff9900";
      }
    },
    rightClick1(e, item, index) {
      e.preventDefault();
      this.menuVisible1 = true;
      this.attid = item.split("attid")[1];
      if (this.dbdata.indexOf(this.attid) != -1) {
        this.Multiple = true;
      } else {
        this.Multiple = false;
      }
      this.menuStyle1.top = e.clientY - 110 + "px";
      this.menuStyle1.left = e.clientX - 180 + "px";
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    download(e, val, index) {
      // let id =''
      // if(val){
      //   id = val.split('attid')[1]
      // }else{
      //   id = this.attid
      // }
      downloadbyattid(this.mailid, this.attid).then(res => {
        if (res.code) {
          if (res.data) {
            var base64String = res.data;
            var binaryString = atob(base64String);
            var binaryLen = binaryString.length;
            var bytes = new Uint8Array(binaryLen);
            for (var i = 0; i < binaryLen; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }
            var blob = new Blob([bytes], { type: "application/octet-stream" });
            var url = URL.createObjectURL(blob);
            var a = document.createElement("a");
            document.body.appendChild(a);
            a.style = "display: none";
            a.href = url;
            a.download = res.message; // 这里可以自定义文件名和扩展名
            a.click();
            URL.revokeObjectURL(url);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    establish() {
      this.listdataVisible = true;
      this.FileList = [];
      this.path = [];
      if (this.dbdata.length == 1) {
        let fileName = this.attList.filter(ite => ite.split("attid")[1] == this.dbdata[0])[0].split("attid")[0];
        this.$set(this.formdata, "custkind", fileName.slice(0, fileName.lastIndexOf(".")));
      } else {
        let arr = [];
        let arr1 = [];
        if (this.dbdata.length > 1) {
          this.dbdata.forEach(ite => {
            this.attList.forEach(item => {
              if (ite == item.split("attid")[1]) {
                if (item.toLowerCase().includes("zip") || item.toLowerCase().includes("rar") || item.toLowerCase().includes("tgz")) {
                  arr.push(item.split("attid")[0]);
                } else {
                  arr1.push(item.split("attid")[0]);
                }
              }
            });
          });
        } else {
          this.attList.forEach(item => {
            if (item.toLowerCase().includes("zip") || item.toLowerCase().includes("rar") || item.toLowerCase().includes("tgz")) {
              arr.push(item.split("attid")[0]);
            } else {
              arr1.push(item.split("attid")[0]);
            }
          });
        }
        if (arr.length) {
          this.$set(this.formdata, "custkind", arr[0].slice(0, arr[0].lastIndexOf(".")));
        } else {
          let fileName = arr1[0].substring(0, arr1[0].lastIndexOf("."));
          let extension = arr1[0].substring(arr1[0].lastIndexOf(".") + 1);
          let b = extension !== "zip" && extension !== "rar" ? `${fileName}.zip` : arr1[0];
          this.$set(this.formdata, "custkind", b.slice(0, b.lastIndexOf(".")));
        }
      }
    },
    CreateOrder() {
      if (!this.attList.length) {
        this.$message.warning("没有附件,无法创建订单");
        return;
      }
      if (!this.selectedRowKeysArray.length) {
        this.$message.warning("请选择一条数据进行创建订单");
        return;
      }
      if (this.selectedRowKeysArray.length > 1) {
        this.$message.warning("创建订单只能选择一条数据");
        return;
      }
      this.formdata = {};
      this.establish();
    },
    listhandleOk() {
      const form = this.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          this.formdata.memo = this.formdata.memo ? this.formdata.memo : "";
          let params = {
            mailid: this.mailid,
            attids: this.dbdata,
            num: Number(this.formdata.num) || 0,
            custkind: this.formdata.custkind,
            adnexaPaths: this.path,
            memo: this.formdata.memo,
            IsJiaji: this.formdata.isJiaji || false,
          };
          emimeorderv2(params)
            .then(res => {
              if (res.code) {
                this.$message.success("创建成功");
                if (this.dbdata.length == 0) {
                  this.formData.Subject = "";
                  (this.formData.joinFactoryId = undefined), this.getOrderList(this.formData);
                  this.messageList = "";
                  this.showTitle = "主题";
                  this.attList = [];
                } else {
                  let st = document.getElementsByClassName("liback");
                  if (st.length && this.attList.length) {
                    this.attList.forEach((ite, index) => {
                      st[index].style.backgroundColor = "#E0E2E4";
                      st[index].style.border = "none";
                    });
                  }
                  this.dbdata = [];
                }
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.listdataVisible = false;
              this.confirmLoading = false;
            });
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-pagination-jump-prev {
  min-width: 20px;
  max-width: 20px;
}
/deep/.ant-pagination-item a {
  padding: 0 4px;
}
/deep/.ant-list-pagination {
  height: 24px;
  position: absolute;
  margin-left: 8px;
  bottom: 1px;
}
/deep/.ant-pagination-total-text {
  margin-right: 0;
}
/deep/ .rowBackgroundColor {
  background: rgb(223 220 220) !important;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table-thead > tr > th .ant-table-column-sorter {
  display: none;
  vertical-align: middle;
}
/deep/.ant-table-thead > tr > th {
  padding: 4px 4px;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 4px 4px !important;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
  color: #000000;
}
.iconstyle {
  font-size: 12px;
  background: #428bca;
  color: white !important;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
.thdm {
  /deep/.ant-row {
    display: flex;
  }
}
.line1 {
  margin-left: 5px;
  border: none;
  border-bottom: 1px solid #a6a6a6;
  width: 400px;
  outline: none;
}
/deep/.ant-divider-horizontal {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 7px 0;
  background: #c8c8c8;
}
/deep/.ant-form-item {
  margin: 0;
}
.lockclass {
  position: relative;
  top: 1px;
  right: 5px;
}
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-drawer-title {
  font-weight: bold;
}
/deep/.ant-drawer-body {
  padding: 20px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}
/deep/.list-item-content {
  display: flex;
  align-items: center;
}
/deep/.ant-list-item-no-flex {
  margin: 7px 15px 0 8px;
  height: 66px;
  border: 2px solid #e8e8e8 !important;
  border-radius: 4px;
}
/deep/.ant-list-item-meta-description {
  margin-left: 30px;
}
/deep/.ant-list-grid .ant-col > .ant-list-item {
  margin-bottom: 0;
}
/deep/.ant-list-vertical .ant-list-item-meta-title {
  margin-bottom: 4px;
  line-height: 18px;
}
/deep/.ant-list-vertical .ant-list-item-meta {
  margin-bottom: 10px;
}
.list-item-content:hover {
  background-color: #e0e2e4;
}
.selectedcolor {
  .list-item-content {
    background-color: #e0e2e4;
  }
}
.overflow-ellipsis {
  white-space: nowrap; /* 确保内容不换行 */
  overflow: hidden; /* 隐藏超出容器的内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  width: 300px; /* 定义容器的宽度，根据需要调整 */
}
.img-box {
  height: 60px;
  background-color: #ffffff;
  padding: 6px;
  ul {
    height: 83px;
    list-style: none;
    display: flex; /* 使用flexbox布局 */
    flex-wrap: wrap; /* 允许项目多行排列 */
    gap: 6px; /* 设置项目之间的间隔 */
    overflow: auto;
    margin-bottom: 0;
    li {
      height: 40px;
      text-align: center;
      padding: 6px;
      border: 1px solid #e0e2e4;
      border-radius: 4px;
      background-color: #e0e2e4;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 500px;
    }
  }
}
.container {
  overflow-y: scroll;
  height: 760px;
  width: 100%;
  user-select: none;
  // &::-webkit-scrollbar {
  //     //整体样式
  //     width: 7px !important; //y轴滚动条粗细
  //     height: 7px !important; //x轴滚动条粗细
  // }
  // &::-webkit-scrollbar-thumb {
  //   //滑动滑块条样式
  //   border-radius: 2px;
  //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
  //   background: #a5a5a5;
  // }
}
.fontcolor {
  color: red;
}
.hstyle {
  height: 40px;
}
.add {
  /deep/.ant-upload-list {
    // width: 200px;
    margin-left: 6px;
    display: flex;
  }
  /deep/.ant-upload-list-item {
    position: relative;
    height: 22px;
    margin-top: 8px;
    font-size: 14px;
    line-height: 22px;
  }
}
.create {
  width: 100%;
  /deep/.ant-upload-list-item-info a {
    color: #606060 !important;
  }
  /deep/.ant-upload {
    border: 1px dashed #ccc; /* 设置虚线边框 */
    height: 20px;
    display: block;
  }
  /deep/ .ant-col {
    width: 100% !important;
  }
  /deep/.ant-upload-list {
    padding-left: 5px;
  }
}
/deep/.ant-upload-list-item-card-actions {
  position: absolute;
  right: -10px;
  opacity: 0;
}
/deep/.ant-drawer-content-wrapper {
  width: 652px !important;
}
.drawerclass {
  margin-right: 14px;
}
.drawre {
  /deep/ a {
    pointer-events: none !important; //禁止点击链接
  }
  color: black;
  height: 641px;
  padding: 10px;
  border-bottom: 2px solid #e0e2e4;
  white-space: break-spaces;
  &::-webkit-scrollbar {
    //整体样式
    width: 7px !important; //y轴滚动条粗细
    height: 7px !important; //x轴滚动条粗细
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #a5a5a5;
  }
  overflow: auto;
}
.drawre1 {
  color: black;
  height: 300px;
  padding: 10px;
  border-bottom: 2px solid #e0e2e4;
  white-space: break-spaces;
  &::-webkit-scrollbar {
    //整体样式
    width: 7px !important; //y轴滚动条粗细
    height: 7px !important; //x轴滚动条粗细
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #a5a5a5;
  }
  overflow: auto;
}
/deep/.ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
.tabRightClikBox {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}

/deep/.rightContent {
  height: 780px;
  background-color: #fff;
  border-top: 2px solid #efefef;
}
/deep/.userStyle {
  user-select: none !important;
}

/deep/.ant-tag {
  font-size: 12px;
  color: #ff9900;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
}
.ant-table-row-cell-break-word {
  position: relative;
}
.span {
  position: absolute;
  top: 5%;
}
p {
  margin: 0;
}
.projectBackend {
  min-width: 1670px;
  .content {
    height: 49px;
    padding-left: 6px;
    background: #ffffff;
    /deep/.ant-select-selection__placeholder {
      display: block;
    }
  }
  .ant-input,
  .ant-select {
    width: 8%;
    margin-right: 0.5%;
    margin-top: 6px;
  }
  // height: 834px;
  width: 100%;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  /deep/.leftContent {
    .ant-spin-nested-loading > div > .ant-spin .ant-spin-dot {
      position: relative;
      top: 300px;
      margin: -10px;
      left: 0;
    }
    .ant-table-selection-column {
      padding: 6px 0 !important;
      width: 25px !important;
      display: inline-block;
    }
    background: #ffffff;
    .min-table {
      .ant-table-body {
        // min-height:733px;
        min-height: 760px;
      }
    }
    height: 770px;
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
    border-left: 0 solid #f0f2f5;
    // border-bottom: 4px solid rgb(233, 233, 240);
  }
  .bto {
    width: 100%;
    height: 54px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container {
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: rgb(223 220 220);
  }
  /deep/ .ant-table-thead > tr > th {
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    min-height: 720px;
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      // border-color: #f0f0f0;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
      // border-color: #f0f0f0;
      position: relative;
      // .topCss{
      //   position:absolute;
      //   top:3%;
      // }
      // .topCss1{
      //   position:absolute;
      //   top:3%;
      //   // left:30%;

      // }
      // .topCss2{
      //   position:absolute;
      //   top:3%;
      //   // left:25%;

      // }
    }
    tr.ant-table-row-selected td {
      background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
      background: rgb(223 220 220);
    }
    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: #000000;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
  // background: #F0F2F5;
}
</style>
