<template>
  <a-table
    :columns="columns3"
    bordered
    :pagination="false"
    :scroll="{ y: 738, x: 700 }"
    :dataSource="RiskData"
    :rowKey="rowKey"
    :loading="riskLoading"
    :rowClassName="isRedRow"
    :customRow="onClickRow"
  >
  </a-table>
</template>
<script>
export default {
  props: ["columns3", "rowKey", "riskLoading", "RiskData"],
  data() {
    return {
      risk_id: "",
      selectedRowsData: {},
      selectedRowKeysArray: [],
    };
  },
  methods: {
    isRedRow(record) {
      let strGroup = [];
      if (record.id && record.id == this.risk_id) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.risk_id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.risk_id = record.id;
          },
        },
      };
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-table {
  .rowBackgroundColor {
    background: #dfdcdc !important;
  }
}
</style>
