<!-- 工程管理 - QAE审核- 人员订单信息 -->
<template>
<a-table
    :columns="columns"
    :dataSource="dataSource"
    :scroll="{y: 702,x:340}"
    :pagination="false"
    :rowKey="rowKey"
    :customRow="onClickRow"
    :rowClassName="isRedRow"
    :loading="orderDetailTableLoading">
    <template slot="customRender" slot-scope="text,record," >
      <template >
        {{ text }}
      </template>
      <a-tag color="orange" v-if="record.isLeave_" style="font-size: 12px;font-weight: 600;padding: 0 2px;height: 21px;">
        休
      </a-tag>
    </template>
    <template slot="action" slot-scope="text,record" >
      <a-tooltip title="取单设置" v-if="checkPermission('MES.EngineeringModule.EngineeringQae.EngineeringQaeMIIsLeave')" 
      :style="record.realName=='合计' ?{display:'none'}:''">
        <a-icon type="edit" style="color: #ff9900; font-size: 18px;" @click.stop="OrderRetrievalSettingsClick(record)"/>
      </a-tooltip>
    </template>
    <span slot="num" slot-scope="text,record,index" class="contentTabAction">
      {{ index+1 }}
    </span>
</a-table>
</template>

<script>
import {checkPermission} from "@/utils/abp";
export default {
  name: "OrderDetailQae",
  props: {
    columns:{
      type: Array,
      require: true
    },
    dataSource:{
      type: Array,
      require: true
    },
    orderDetailTableLoading:{
      type: Boolean,
      require: true
    },
    rowKey:{
      type: String,
      require: true
    }
  },
  data(){
    return {
      userLoginID_:'',
      sselectedRowsData:[],
    }
  },
  methods:{
    checkPermission,
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.sselectedRowsData = record
            if (record.userLoginID_ && this.userLoginID_ != record.userLoginID_){
              this.$emit('getPeopleOrderList', record.userLoginID_)
            }
            this.$emit('saveErpKey', record)
            this.userLoginID_ = record.userLoginID_            
          }
        }
      }
    },
    isRedRow (record) {   
      // console.log(record)   
      let strGroup = []
      let str =[]
      if (record.userLoginID_ && record.userLoginID_ == this.userLoginID_) {
        // return 'rowBackgroundColor'
        strGroup.push('rowBackgroundColor')
      } 
      if(record.isLeave_ == true){ // 是否请假
        str.push('rowSty')
        // return 'rowSty' 
      }  
      // if(record.isBigCus == true){ // 是否大客
      //   str.push('rowSty1')
      // }
      if(str.length > 1){
        // console.error('str',[str[0]])
        str = [str[0]]
      }
      // console.log('str.concat(strGroup):',str.concat(strGroup))    
      return str.concat(strGroup)     
      
    },
    OrderRetrievalSettingsClick(record){
      this.$emit('OrderRetrievalSettingsClick',record)
    }
  },
}
</script>

<style scoped lang="less">
///deep/ .ant-table-body {
//  overflow-y: auto!important;
//}
@media screen and (min-width: 1700px) {
  .centerTable1 {
    overflow-x: auto !important;
    /deep/ .ant-table-body {
      .ant-table-tbody {
        .ant-table-row:last-child {
          .contentTabAction {
            display: none;
          }
        }
      }
      //   min-height: 743px;
    }
    
    /deep/.rowBackgroundColor {
      background: rgb(223, 220, 220) !important;
    }
  }
}
/deep/ .ant-table {
  
  .rowSty {
    td {
      color: #DC143C;
    }
  }
  .rowSty1{
    td{
      color: #2828FF;
    }
  }
  .peopleTag {
    position: absolute;
    font-size: 12px;
    font-weight: 600;
    left: 2px;
    padding: 0 2px;
  }
  .rowBackgroundColor {
      background: #aba5a5;
    }
}


</style>