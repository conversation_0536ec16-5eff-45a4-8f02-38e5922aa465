import { request, METHOD } from "@/utils/request";
// 订单列表
export function orderManegePageList(params) {
  return request("/api/app/pcb-order-list/ordermanege-page-list", METHOD.GET, params);
}
// 订单管理统计列表
export function pcbordermanagetotal() {
  return request("/api/app/pcb-order-list/pcb-order-manage-total", METHOD.POST);
}
// 上传文件
export function upLoadEnquiryFile(params) {
  return request("/api/app/pcb-order/up-load-enquiry-file", METHOD.POST, params);
}
// 新增
export function orderAdd(params) {
  return request("/api/app/pcb-order/order-add", METHOD.POST, params);
}
// 编辑
export function orderUpdate(params) {
  return request("/api/app/pcb-order/order-update", METHOD.POST, params);
}
// 订单确认
export function offlineOrder(val) {
  return request(`/api/app/pcb-order/offline-order`, METHOD.POST, val);
}
//订单取消
export function ordercancel(Id, CancelCause) {
  return request(`/api/app/verify-finished/${Id}/order-cancel?cancelCause=${CancelCause}`, METHOD.POST, Id);
}
//订单暂停
export function orderpause(Id, PauseCause) {
  return request(`/api/app/verify-finished/${Id}/order-pause?cancelCause=${PauseCause}`, METHOD.POST, Id);
}
export function paymodecheck(Id) {
  return request(`/api/app/nope-button/pay-mode-check/${Id}`, METHOD.POST, Id);
}
//YXD获取多个id
export function offlineorders(Id) {
  return request(`/api/app/pcb-order/${Id}/offline-orders`, METHOD.POST, Id);
}
export function offlineorderyXD(Id) {
  return request(`api/app/pcb-order/${Id}/offline-order-yXD`, METHOD.POST, Id);
}
//YXD下线完成到工程
export function offlineordertoppeyXD(Id) {
  return request(`/api/app/pcb-order/${Id}/offline-order-to-ppe-yXD`, METHOD.POST, Id);
}
export function purchasepricelist(Id) {
  return request(`/api/app/nope-button/purchase-price-list/${Id}`, METHOD.GET);
}
//订单回退
export function backtoverify(Id) {
  return request(`/api/app/nope-button/${Id}/order-back-to-verify `, METHOD.POST, Id);
}
export function back2Waitoffline(Id) {
  return request(`/api/app/pcb-order/back2Wait-offline/${Id}`, METHOD.POST, Id);
}
//审批
export function orderverifyyXD(params) {
  return request(`api/app/pcb-order/order-verify-yXD`, METHOD.POST, params);
}
// 删除型号
export function orderDelete(Id) {
  return request(`/api/app/order-inquiry/order-delete`, METHOD.POST, Id);
}
// 获取市场信息中的选择项
export function mktConfig() {
  return request(`/api/app/order-pre2/mkt-config `, METHOD.GET);
}
// 获取订单信息
export function pcbOrder(id) {
  return request(`/api/app/pcb-order/${id}/by-id`, METHOD.GET);
}
// 客户代码选择项 2024/5/9
export function mktCustNo() {
  return request(`/api/app/order-inquiry/mkt-cust-no`, METHOD.GET);
}
// 市场按钮检查
export function buttonCheck(Id, OfflineOrder) {
  return request(`/api/app/order-pre-button/button-check/${Id}?buttonname=${OfflineOrder}`, METHOD.POST);
}
export default {};
