<template>
  <a-spin :spinning="spinning">
    <div class="projectReview">
      <div
        class="mainContent"
        style="width: 100%; border: 1px solid rgb(233, 233, 240); border-right: 2px solid rgb(233, 233, 240); user-select: none; height: 100%"
        ref="tableWrapper"
      >
        <main-table
          :reviewcolumns="reviewcolumns"
          :pagination="pagination"
          @handleTableChange="handleTableChange"
          :rowKey="(record, index) => `${index + 1}`"
          :Reviewsource="Reviewsource"
          class="leftstyle"
          ref="mainTable"
        />
      </div>
      <div class="footerAction" style="user-select: none; margin-left: -2px">
        <footer-action
          @returnClick="returnClick"
          @queryClick="queryClick"
          :total="pagination.total"
          @addClick="addClick"
          @editClick="editClick"
          @deleteClick="deleteClick"
          @checkClick="checkClick"
          @finishClick="finishClick"
        />
      </div>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="dataVisible = false"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form>
          <a-row>
            <a-col :span="24">
              <a-form-item label="订单号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input v-model="formdata.businessOrderNo" allowClear autoFocus> </a-input>
              </a-form-item>
              <a-form-item label="生产型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input v-model="formdata.orderNo" allowClear> </a-input>
              </a-form-item>
              <a-form-item label="流水编号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input v-model="formdata.serialNo" allowClear> </a-input>
              </a-form-item>
              <a-form-item label="提出人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input v-model="formdata.name" allowClear> </a-input>
              </a-form-item>
              <a-form-item label="开始时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-date-picker format="YYYY-MM-DD" @change="StartTime"></a-date-picker>
              </a-form-item>
              <a-form-item label="结束时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-date-picker format="YYYY-MM-DD" @change="EndTime" :disabled="formdata.StartTime ? false : true"></a-date-picker>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <!--按钮检查-->
      <a-modal title="检查信息" :visible="checkVisible" @cancel="checkVisible = false" destroyOnClose centered :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back1" type="primary" v-if="check" @click="continueclick">继续</a-button>
          <a-button key="back" @click="checkVisible = false">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.info == '1'" style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        title="确认弹窗"
        :visible="confirmvisible"
        @cancel="confirmvisible = false"
        @ok="dehandleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        centered
      >
        <div v-if="confirmtype == 'return' || confirmtype == 'finish'">
          <a-form>
            <a-row>
              <a-col :span="24">
                <a-form-item
                  :label="confirmtype == 'return' ? '*退回原因' : '*完结原因'"
                  :labelCol="{ span: 4 }"
                  :wrapperCol="{ span: 20 }"
                  class="required"
                >
                  <a-textarea placeholder="请输入原因" v-model="Reason" allowClear autoFocus :auto-size="{ minRows: 4, maxRows: 8 }"> </a-textarea>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div v-else>{{ confirmmessage }}</div>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import { engreviewlist, engreviewback, reviewdelete, allreviewfinish, reviewbuttoncheck } from "@/services/gongju/Reviewinitiated";
import MainTable from "@/pages/gongju/Reviewinitiated/modules/MainTable";
import FooterAction from "@/pages/gongju/Reviewinitiated/modules/FooterAction";
import { checkPermission } from "@/utils/abp";
import { mapState } from "vuex";
const reviewcolumns = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "订单号",
    dataIndex: "businessOrderNo",
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "流水编号",
    align: "left",
    dataIndex: "serialNo",
    ellipsis: true,
    width: 120,
  },
  {
    title: "生产型号",
    align: "left",
    dataIndex: "orderNo",
    ellipsis: true,
    width: 130,
  },
  {
    title: "评审人",
    align: "left",
    ellipsis: true,
    dataIndex: "reviewUser",
    width: 65,
  },
  {
    title: "评审类别",
    align: "left",
    ellipsis: true,
    dataIndex: "reviewSource",
    width: 65,
  },
  {
    title: "客户型号",
    align: "left",
    dataIndex: "customerModel",
    ellipsis: true,
    width: 200,
  },
  {
    title: "评审类别",
    align: "left",
    dataIndex: "reviewType",
    ellipsis: true,
    width: 120,
  },
  {
    title: "订单类型",
    align: "left",
    dataIndex: "isReOrderStr",
    ellipsis: true,
    width: 60,
  },
  {
    title: "评审状态",
    align: "left",
    dataIndex: "statusStr",
    ellipsis: true,
    width: 70,
  },
  {
    title: "提出人",
    align: "left",
    dataIndex: "createName",
    ellipsis: true,
    width: 70,
  },
  {
    title: "提出时间",
    width: 100,
    ellipsis: true,
    dataIndex: "createTime",
    align: "left",
  },
  {
    title: "分配退回原因",
    align: "left",
    dataIndex: "sendBackReason",
    ellipsis: true,
    width: 200,
  },
  {
    title: "发起审核退回原因",
    align: "left",
    dataIndex: "backReason",
    ellipsis: true,
    width: 200,
  },
];
export default {
  data() {
    return {
      checkVisible: false,
      check: false,
      checkType: "",
      checkData: [],
      Reason: "",
      confirmmessage: "",
      spinning: false,
      confirmtype: "",
      confirmvisible: false,
      formdata: {},
      dataVisible: false,
      reviewcolumns,
      Reviewsource: [],
      buttonsmenu: false,
      text: "",
      isCtrlPressed: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  components: {
    MainTable,
    FooterAction,
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  created() {
    this.dehandleOk1 = this.debounce(this.handleOk1, 500);
    this.$nextTick(() => {
      this.getreviewdata();
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        this.dataVisible = true;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        e.preventDefault();
      } else if (e.keyCode == "13" && this.confirmvisible) {
        this.dehandleOk1();
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    StartTime(value, dateString) {
      this.$set(this.formdata, "StartTime", dateString);
    },
    EndTime(value, dateString) {
      this.$set(this.formdata, "EndTime", dateString);
    },
    queryClick() {
      this.dataVisible = true;
      this.formdata = {};
    },
    continueclick() {
      if (this.checkType == "check") {
        this.reviewJump("check");
      } else if (this.checkType == "return") {
        this.confirmtype = "return";
        this.dehandleOk1();
      } else if (this.checkType == "delete") {
        this.confirmtype = "delete";
        this.dehandleOk1();
      }
    },
    buttonCheck(key, name) {
      reviewbuttoncheck(this.$refs.mainTable.proOrderId, key).then(res => {
        if (res.code) {
          if (res.data.length) {
            this.checkData = res.data;
            this.checkVisible = true;
            this.checkType = name;
            this.check = !this.checkData.some(item => item.error == 1);
          } else {
            this.Jumpreview(name);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Jumpreview(review_Type) {
      this.$router.push({
        path: "Judgingcontent",
        query: {
          OrderNo: this.$refs.mainTable.selecdata.orderNo,
          businessOrderNo: this.$refs.mainTable.selecdata.businessOrderNo,
          joinFactoryId: this.$refs.mainTable.selecdata.joinFactoryId,
          id: this.$refs.mainTable.proOrderId,
          reviewSource: this.$refs.mainTable.selecdata.reviewsource,
          review_Type: review_Type,
          page: "0",
          reviewNo: this.$refs.mainTable.selecdata.reviewNo,
        },
      });
    },
    addClick() {
      this.$router.push({
        path: "Judgingcontent",
        query: {
          joinFactoryId: this.user.factoryId,
          review_Type: "add",
          page: "0",
          reviewNo: this.$refs.mainTable.selecdata.reviewNo,
        },
      });
    },
    editClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要编辑的订单");
        return;
      }
      this.buttonCheck("ReviewStartEdit", "edit");
    },
    checkClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择订单进入评审页面");
        return;
      }
      this.buttonCheck("ReviewStartCheck", "check");
    },
    deleteClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要删除的订单");
        return;
      }
      reviewbuttoncheck(this.$refs.mainTable.proOrderId, "ReviewStartDelete").then(res => {
        if (res.code) {
          if (res.data.length) {
            this.checkData = res.data;
            this.checkVisible = true;
            this.checkType = "delete";
            this.check = !this.checkData.some(item => item.error == 1);
          } else {
            this.confirmmessage = this.$refs.mainTable.selecdata.orderNo + "确定删除吗？";
            this.confirmvisible = true;
            this.confirmtype = "delete";
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    finishClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要完成的订单");
        return;
      }
      this.confirmvisible = true;
      this.confirmtype = "finish";
      this.Reason = "";
    },
    returnClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要退回的订单");
        return;
      }
      reviewbuttoncheck(this.$refs.mainTable.proOrderId, "ReviewStartBack").then(res => {
        if (res.code) {
          if (res.data.length) {
            this.checkData = res.data;
            this.checkVisible = true;
            this.checkType = "return";
            this.check = !this.checkData.some(item => item.error == 1);
          } else {
            this.confirmvisible = true;
            this.confirmtype = "return";
            this.Reason = "";
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk1() {
      if (!this.Reason && (this.confirmtype == "return" || this.confirmtype == "finish")) {
        this.$message.warn("请输入原因");
        return;
      }
      this.spinning = true;
      this.confirmvisible = false;
      if (this.confirmtype == "return") {
        engreviewback(this.$refs.mainTable.proOrderId, this.Reason)
          .then(res => {
            if (res.code) {
              this.$message.success(res.message);
              this.getreviewdata();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.confirmtype == "delete") {
        reviewdelete(this.$refs.mainTable.proOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success(res.message);
              this.getreviewdata();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.confirmtype == "finish") {
        allreviewfinish(this.$refs.mainTable.proOrderId, this.Reason)
          .then(res => {
            if (res.code) {
              this.$message.success(res.message);
              this.getreviewdata();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    handleOk() {
      if (JSON.stringify(this.formdata) != "{}") {
        this.getreviewdata(this.formdata);
      }
      this.dataVisible = false;
    },
    getreviewdata(data) {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      this.spinning = true;
      var obj = Object.assign(params, data);
      engreviewlist(obj)
        .then(res => {
          this.Reviewsource = res.items;
          setTimeout(() => {
            this.handleResize();
          }, 0);
          this.pagination.total = res.totalCount;
          this.proOrderId = "";
          this.selecdata = {};
          if (params.orderNo) {
            this.proOrderId = this.Reviewsource[0].id;
            this.selecdata = this.Reviewsource[0];
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      let params = this.formdata;
      if (JSON.stringify(params) != "{}") {
        this.getreviewdata(params);
      } else {
        this.getreviewdata();
      }
    },
    handleResize() {
      let screenHeight = window.innerHeight;
      let mainContent = document.getElementsByClassName("mainContent")[0];
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (this.Reviewsource.length == 0) {
        this.$refs.tableWrapper.style.height = screenHeight - 143 + "px";
      } else {
        this.$refs.tableWrapper.style.height = 0;
      }
      mainContent.style.height = screenHeight < 920 ? screenHeight - 135 + "px" : "775px";
      if (leftstyle && this.Reviewsource.length != 0) {
        leftstyle.style.height = screenHeight - 173 + "px";
      } else {
        leftstyle.style.height = 0;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.required /deep/ .ant-form-item-label > label {
  color: red !important;
}
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
.box {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.box1 {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.footerAction {
  width: 100%;
  height: 50px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  form {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #fff;
  }
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
.tabRightClikBox1 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-table-fixed-header .ant-table-body-inner {
  overflow: hidden;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  margin-left: 10px;
  position: fixed;
  margin: 11px 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectReview {
  background-color: #ffffff;
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/.ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 7px 2px !important ;
    overflow-wrap: break-word;
  }
  /deep/ .ant-table {
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-thead > tr > th {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
  /deep/.ant-table-row-cell-break-word {
    border-right: 1px solid #efefef;
  }
}
/deep/.mainContent {
  .mintable {
    .ant-table-body-inner {
      max-height: 737px !important;
    }
    .ant-table-pagination {
      margin: 6px 0;
      z-index: 99;
      position: absolute;
      bottom: -6.5%;
      margin-left: 1%;
    }
    .ant-table-body {
      min-height: 738px;
    }
  }
  border: 2px solid rgb(233, 233, 240);
  border-bottom: 4px solid rgb(233, 233, 240);
}
</style>
