<!--生产管理- 管制卡详情 -->
<template>
    <a-spin :spinning="spinning">
    <div class="orderDetail">  
       <order-action
       @saveClick="saveClick"
       ></order-action>
      <a-tabs default-active-key="1" >       
        <a-tab-pane key="1" tab="基本信息">        
         <order-info :formInfo="formInfo" ref="formInfo"></order-info>
        </a-tab-pane>
        <a-tab-pane key="2" tab="操作日志"  > 
          <a-table 
          :rowKey="'id'"
          :columns="columns1" 
          :pagination= false        
          :dataSource='viewLogData' 
          :scroll='{y:672}'
          :class="'viewInfo'"
          >          
          </a-table>         
        </a-tab-pane>              
      </a-tabs>      
    </div>
      
    </a-spin>
  </template>
  <script>
  import moment from "moment";
  import OrderInfo from "@/pages/OrderManagement/cardDetail/modules/OrderInfo";
  import OrderAction from "@/pages/OrderManagement/cardDetail/modules/OrderAction";
  import {mapState,} from 'vuex'
  import {
      cardInfoEdit,
      saveCardInfoEdit,
      cardInfoLogList,
  } from "@/services/scgl/OrderManagement/ControlCard"
const columns1 = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: "center",
      customRender: (text,record,index) => `${index+1}`,
      width: 40,    
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      align: "left",
      width: 100,    
    },
    {
      title: '操作详情',
      dataIndex: 'content',
      align: "left",
      width:700,    
    },
    
  ]
  export default {
    name:"cardDetail",
    components:{OrderInfo,OrderAction},
    data(){
        return{
            spinning:false,
            formInfo:{},
            columns1,
            viewLogData:[],
        }
    },
    async created() {     
      await this.getData() 
      this.getViewLog()
    }, 
    computed: {
      ...mapState('account', ['user',]),  
    },
    methods: {
      moment,
      async getData(){   
        this. spinning = true
        let Id = this.$route.query.id
        console.log(Id)
        await cardInfoEdit(Id).then(res=>{
          if(res.code){
            this.formInfo = res.data
            this.formInfo.status = this.formInfo.status.toString()
            this.formInfo.priority = this.formInfo.priority.toString()
            console.log('数据',this.formInfo)
          }else{
            this.$message.error(res.message)
          }
            
        }).finally(()=>{
          this.spinning = false
        })
        
      },
      saveClick(){
        let params = this.$refs.formInfo.formInfo
        console.log('params',params)
        params.status = Number( params.status)
        params.priority = Number( params.priority)
        params.num = Number( params.num)
        params.name = this.user.name
        params.account = this.user.userName
        saveCardInfoEdit(params).then(res=>{
          if(res.code){
            this.$message.success('保存成功')
            this.getViewLog()
          }else{
            this.$message.error(res.message)
          }
        }).finally(()=>{
          this.$refs.formInfo.formInfo.status = this.$refs.formInfo.formInfo.status.toString()
          this.$refs.formInfo.formInfo.priority = this.$refs.formInfo.formInfo.priority.toString()
        })
      },
      // 查看日志
      getViewLog(){
        this.spinning = true
        let Id = this.$route.query.id
        cardInfoLogList(Id).then(res => {
          if (res.code){  
              this.viewLogData = res.data
            } else {
              this.$message.error(res.message)
            }
        }).finally(()=>{
          this.spinning = false
        })
      },
    }
    
  }
</script>
<style scoped lang="less">
  .orderDetail {
    padding: 10px;
    background: #ffffff;
    min-width: 1670px;
    /deep/.rowBackgroundColor {
    background: #FFF9E6!important;
  }
    /deep/ .ant-tabs {
      // .viewInfo{
      //   .ant-table-thead{
      //     .ant-table-align-left{
      //       text-align: center!important;;
      //     }
      //   }
      // }
      margin-top: 10px;
      .ant-tabs-bar{
        margin: 0;
        border-bottom: 1px solid #ccc;
        .ant-tabs-nav-wrap {
          .ant-tabs-ink-bar {
            display: none!important;
          }
        }        
        .ant-tabs-tab {
          margin: 0;
          padding: 0 10px;
          border: 1px solid #ccc;
          font-size: 14px;
          height: 34px;
          line-height: 34px;
          border-left: 0;
          font-weight: 500;
          &:nth-child(1){
            border-left: 1px solid #ccc;;
          }
        }
        .ant-tabs-tab-active {
          border-top: 2px solid #f90 !important;
          border-bottom-color:#ffffff ;
          background: #ffffff;
  
        }
  
      }
    }
  }
 
  /deep/.ant-table{
          font-size: 12px!important;
        }
  </style>