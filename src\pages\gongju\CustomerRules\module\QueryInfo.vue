<!-- 工具管理- 客户规则- 查询 -->
<template >
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="客户代码">
      <a-input   v-model='OrderNumber' placeholder="请输入客户代码"  :autoFocus="autoFocus" />
    </a-form-item>
  </a-form>
</template>

<script>
export default {
    name:'QueryInfo',
  data() {
    return {
      OrderNumber:'',
      autoFocus:true
    };
  },
  methods: {
  //  keyupEnter1(){
  //     this.$emit('keyupEnter1')
  // }
  },

};
</script>
<style lang="less" scoped>
/deep/.ant-form-item{
  margin-bottom: 0;
}
</style>
