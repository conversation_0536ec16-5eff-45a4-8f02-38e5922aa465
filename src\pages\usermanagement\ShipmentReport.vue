<!-- 用户管理 - 出货报告  -->

<template>
  <a-spin :spinning="spinning">
    <div class="Shipment">
      <div class="mainContent" style="width:100%;border: 1px solid rgb(233, 233, 240);
          border-right:2px solid rgb(233, 233, 240);position: relative;user-select:normal;" ref="tableWrapper">              
              <a-table
              :columns="columns1"
              :pagination="pagination"
              :scroll="{y:738,x:1200}"
              :dataSource="reportdatasource"
              @change="handleTableChange"
              :rowKey="(record,index)=>{return index}"
              class="Tablestyle">
              <span slot="num" slot-scope="text, record, index">
                {{(pagination.current-1)*pagination.pageSize+parseInt(index)+1}}
              </span>  
              <span slot='action' slot-scope="text, record">
                <a-tooltip placement="top" title="下载出货报告">
                  <a-icon type="download" @click="downfile(record)" style="color: #428bca;"></a-icon>
                </a-tooltip>                
              </span>   
            </a-table>          
      </div>  
      <div class="footerAction" style='user-select:none;margin-left: -2px;'>
          <div v-show="!buttonsmenu">
            <a-button type="primary" class="box" @click="queryClick">
                查询(F)
            </a-button> 
          </div> 
          <div v-show="buttonsmenu">
        <a-dropdown>
          <a-button type="primary" class="box1" @click.prevent>
            按钮菜单栏
          </a-button>
          <template #overlay>
            <a-menu class="tabRightClikBox1">
              <a-menu-item >查询(F)</a-menu-item>
            </a-menu>
          </template>
       </a-dropdown>
        </div>             
    </div>
    </div>
  
     <!-- 查询弹窗 -->
     <a-modal
              title="订单查询"
              :visible="dataVisible"
              @cancel="dataVisible=false"
              @ok="handleOk"
              ok-text="确定"
              destroyOnClose
              :maskClosable="false"
              :width="400"
              centered
              class="modalclass"
       >
       <a-form >   
            <a-form-item label="生产编号"  :labelCol="{span: 6}" :wrapperCol="{span: 16}">
               <a-input  v-model="querydata.OrderNo" placeholder="" allowClear  autoFocus> </a-input>
            </a-form-item>
            <a-form-model-item label="客户型号" :labelCol="{span: 6}" :wrapperCol="{span: 16}">
              <a-input v-model="querydata.customerModel" placeholder="" allowClear/>
            </a-form-model-item> 
    </a-form>  
    </a-modal>
  </a-spin>
</template>
<script>
import {importinspectionreportv2} from "@/services/scgl/QualityManagement/ShippingReport"
import {mapState,} from 'vuex'
import {shipreport} from '@/services/usermanagement/index.js'
  const columns1 = [
      {
        title: '序号',
        key: 'index',
        align: "center",
        scopedSlots: { customRender: 'num' },
        width: 45,
      },
      {
        title: "生产编号",
        align: "left",
        dataIndex: "orderNo",
        ellipsis: true,
        scopedSlots: { customRender: 'orderNo' },
        width: 160,
      },
      {
        title: "客户型号",
        align: "left",
        dataIndex: "customerModel",
        ellipsis: true,
        width: 150,
      },
      {
        title: "下单时间",
        align: "left",
        dataIndex: "createTime",
        ellipsis: true,
        width: 140,
        sorter: (a, b) => {
          return a.createTime.localeCompare(b.createTime)
        }
      },
      {
        title: "订单交期",
        align: "left",
        ellipsis: true,
        dataIndex: "deliveryDate",
        width: 85,
      },
      {
        title: "类型",
        dataIndex: "isReOrder",
        customRender: (text, record, index) => `${record.isReOrder == 0 ? '新单' : record.isReOrder == 1 ? '返单' : record.isReOrder == 2 ? '返单更改' : ''}`,
        align: "left",
        ellipsis: true,
        width: 70,
      },
      {
        title: "当前工序",
        align: "left",
        ellipsis: true,
        dataIndex: "",
        width: 85,
      },
      {
        title: "是否下载",
        align: "center",
        customRender: (text, record, index) => `${record.isMark===true?'是':'否'}`,
        ellipsis: true,
        width: 70,
      },
      {
        title: "操作",
        align: "center",
        ellipsis: true,
        scopedSlots: { customRender: 'action' },
        width: 50,
      },
    ]
  export default {
    name: 'ShipmentReport',
    data () {
      return {
        columns1,
        user:{},
        querydata:{},
        dataVisible: false,
        buttonsmenu: false,
        spinning: false,
        reportdatasource:[],
        isCtrlPressed:false,
        pagination: {
          pageSize: 20,
          current: 1,
          total:0,
          showQuickJumper: true,
          isCtrlPressed:false,
          size:'',
          simple:false,
          showSizeChanger: true,
          pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
          showTotal: (total) => `总计 ${total} 条`,
        },   
      }
    },
    computed: {
    ...mapState('account', ['userinfo',]), 
  },
    methods: {
      queryClick(){
        this.dataVisible=true
        this.querydata={}
      },
      handleTableChange(pagination) {
        this.pagination.current = pagination.current
        this.pagination.pageSize = pagination.pageSize
        if(JSON.stringify(this.querydata) != '{}'){
          this.getdata(this.querydata)
        }else{
          this.getdata();
        }
      },
      handleResize(){
        let mainContent = document.getElementsByClassName('mainContent')[0]
        let Tablestyle = document.getElementsByClassName('Tablestyle')[0].children[0].children[0].children[0].children[0].children[0].children[1]
        if(Tablestyle && this.reportdatasource.length!=0){
          Tablestyle.style.height = window.innerHeight-173+'px'         
        }else{
          Tablestyle.style.height =0
        }       
        mainContent.style.height=window.innerHeight-140 > 777 ?'777px':window.innerHeight-140+'px'
        let elements = document.getElementsByClassName("box")    
        let buttonwidth = elements.length * 110;
        let footerwidth = window.innerWidth-170
        let paginnum = ''
          if(Math.ceil(this.pagination.total/20)>10){
            paginnum = 7
          }else{
            paginnum = Math.ceil(this.pagination.total/20)
          }
        let paginationwidth = (paginnum*40)+380
        let paginationwidth1 = (paginnum*22)+336
        let paginationwidth2 = 140
        if(buttonwidth+paginationwidth<footerwidth){
          this.pagination.size = 'default'
          this.pagination.simple = false
          this.buttonsmenu=false
        }else if(buttonwidth+paginationwidth>footerwidth && buttonwidth+paginationwidth1<footerwidth){
          this.pagination.size = 'small'
          this.pagination.simple = false
          this.buttonsmenu=false
        }else{
          this.pagination.simple = true
          this.buttonsmenu=false
          if(buttonwidth+paginationwidth2>footerwidth){
            this.buttonsmenu=true
          }else{
            this.buttonsmenu=false
          }
        }        
      },
      downfile(val){
        this.spinning = true
        let params = {
        'orderno':val.orderNo,
        'Reports':[1,2,3,4,5,6,7,8],
        'joinFactoryId':val.joinFactoryId,
      }
        importinspectionreportv2(params).then(res=>{
          if(res.code){
            if(res.data!=null){
              this.downloadByteArrayFromString(res.data,res.message)  
              this.getdata()
            }else{
              this.$message.warning('暂无数据 不能导出报告')
            }                   
          }else{
            this.$message.error(res.message)
          }            
      }).finally(()=>{
        this.spinning = false
      }
      )
      },
      downloadByteArrayFromString(byteArrayString, fileName) {
          const byteCharacters = atob(byteArrayString); 
          const byteNumbers = new Array(byteCharacters.length); 
          for (let i = 0; i < byteCharacters.length; i++) { byteNumbers[i] = byteCharacters.charCodeAt(i); } 
          const byteArray = new Uint8Array(byteNumbers); 
          const blob = new Blob([byteArray], { type: 'application/octet-stream' }); 
          const url = URL.createObjectURL(blob); 
          const link = document.createElement('a'); 
          link.href = url; link.download = fileName; link.click(); 
          URL.revokeObjectURL(url); 
        },
      getdata(querydata){
        this.spinning = true
        let params={
          'ClientLoginKey':this.user.clientLoginKey,
          'PageIndex':this.pagination.current,
          'PageSize':this.pagination.pageSize,    
        }
        if(querydata){
          params = {...params,...querydata}
        }
        shipreport(params).then(res=>{
            this.reportdatasource = res.items
            setTimeout(() => {
              this.handleResize();
            },0); 
            this.pagination.total = res.totalCount
        }).finally(()=>{
          this.spinning = false
        })
      },
      handleOk(){
        this.dataVisible = false
        this.getdata(this.querydata)
      },  
      keydown(e) {
        if (e.key === 'Control') {
          this.isCtrlPressed = true;
        }
        if (e.keyCode == '70' && this.isCtrlPressed) {
          this.isCtrlPressed = false;
          this.queryClick()
          e.preventDefault()
        } else if (e.keyCode == '13' && this.dataVisible) {
          this.handleOk()
          e.preventDefault()
        }
      },
      keyup(e) {
        if (e.key === 'Control') {
          this.isCtrlPressed = false;
        }
      },
    },
    created() {
      if(this.userinfo){
        this.user = this.userinfo
      }else{
        this.user = JSON.parse(localStorage.getItem('UserInformation'))
      }
      this.$nextTick(()=>{
        this.handleResize()
        this.getdata()
      })    
    },
    mounted() {
      document.title='EMS | 出货报告'
      window.addEventListener('resize', this.handleResize, true)
      window.addEventListener('keydown', this.keydown, true)
      window.addEventListener('keyup', this.keyup, true)
    },
    beforeDestroy() {
      window.removeEventListener("keydown", this.keydown, true);
      window.removeEventListener("keyup", this.keyup, true);
      window.removeEventListener('resize', this.handleResize, true)
    },
  }
</script>
<style lang="css" scoped>
  .tabRightClikBox1{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        width:100px;
        line-height:22px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color:#666666
      }
      li:hover{
        background-color: #ff9900 !important;
        color:white;
        font-size:16px;
        height:32px;
        line-height:22px
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
  }
.footerAction {
    width: 100%;
    height:52px;
    background: white;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    .box{
          float: right;
          margin-top: 9px;
          margin-right:10px;
          width: 90px;
      }
    .box1{
      float: right;
      margin-top: 9px;
      margin-right:12px;
      width: 90px;
    }
    }
    .modalclass{
      /deep/.ant-form-item{
          margin-bottom: 0 !important;
        }
    }
   
 .Shipment {
        background-color: white;
        /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
          background: #dfdcdc;
        }
        /deep/.ant-pagination-prev {
          margin-left: 8px;
        }
        /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
          background: #F8F8F8;
        }
        /deep/.ant-pagination-simple .ant-pagination-simple-pager input{
          padding: 0;
          margin: 0;
        }
        /deep/.ant-pagination-simple .ant-pagination-simple-pager{
          margin: 0;
        }
        /deep/.ant-pagination-slash {
            margin: 0;
        }
        /deep/.ant-table-pagination.ant-pagination{
          float: left;
          position: fixed;
          font-size: small;
          margin: 8px;
        }
        /deep/ .ant-table-thead > tr > th {
            padding: 4px 2px;
            height:36px;        
            border-right:1px solid #efefef !important;      
          }
      /deep/.ant-table-tbody > tr > td {
          padding:4px 2px;
          height:36px;
          border-right:1px solid #efefef;
        }
      /deep/ .ant-table{
        tr.ant-table-row-hover td {
         background: #dfdcdc;
        }    
        .ant-table-selection-col {
          width: 20px !important;
        }
      }
}
</style>