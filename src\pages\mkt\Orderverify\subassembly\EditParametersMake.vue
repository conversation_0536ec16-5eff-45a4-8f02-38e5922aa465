<template>
  <a-form  layout="inline">
    <a-row>
      <a-col :span='12'>
        <a-form-item label="不加厂编"  :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-checkbox v-model="ParameterForm.factoryNo"/>  
        </a-form-item>
      </a-col>
      <a-col :span='12'>
        <a-form-item label="托管输出" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-checkbox v-model="ParameterForm.isShuChu"/>  
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span='12'>
      <a-form-item label="先V割后成型" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.vCutToWX"/>  
      </a-form-item>
    </a-col>
      <a-col :span='12'>
      <a-form-item label="正负片" :label-col="{ span: 14}" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-select
          ref="select"  
          show-search
          v-model="ParameterForm.posNeg"
        >
          <a-select-option value="不限">不限</a-select-option>
          <a-select-option value="只能负片">只能负片</a-select-option>
          <a-select-option value="只能正片">只能正片</a-select-option>
        </a-select>  
      </a-form-item>
     </a-col>
    </a-row>

    <a-row>
      <a-col :span='12'>
        <a-form-item label="白油块大于20*20mm" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-checkbox v-model="ParameterForm.oilBlockGreaterThan20_20" />  
        </a-form-item>
      </a-col>
      <a-col :span='12'>
        <a-form-item label="先喷锡后文字" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-checkbox v-model="ParameterForm.bMCLToWZ" />  
        </a-form-item>
       </a-col>
      </a-row>  

  <a-row>
    <a-col :span='12'>
      <a-form-item label="外层线宽线距" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-input  v-model="ParameterForm.minLineWS"/>  
      </a-form-item>
    </a-col>
    <a-col :span='12'>
      <a-form-item label="内层线宽线距" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-input  v-model="ParameterForm.innerMinLineWS" />  
      </a-form-item>
     </a-col>
    </a-row> 

  <a-row>
    <a-col :span='12'>
      <a-form-item label="铜厚超制程" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">        
        <a-select 
        show-search
        option-filter-prop="children"
        :filter-option="filterOption"
        v-model='ParameterForm.thickCopperBaseThickness'>
        <a-select-option v-for="(item,index) in dataClassList" :key="index" :value="item.valueMember" :lable="item.text">
          {{item.text}}
        </a-select-option>
      </a-select>
      </a-form-item>
    </a-col>
    <a-col :span='12'>
      <a-form-item label="BGA/IC开窗" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.bgalcSmallAlert"/> 
      </a-form-item>
    </a-col>
  </a-row>

  <a-row>
    <a-col :span='12'>  
      <a-form-item label="V-CUT" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.vCut"/>  
      </a-form-item>
    </a-col>
    <a-col :span='12'>
      <a-form-item label="有铜沉头孔/控深槽" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.counterboreCopper"/>  
      </a-form-item>
    </a-col>
  </a-row>

  <a-row>
    <a-col :span='12'>
      <a-form-item label="无铜沉头孔/控深槽" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.counterboreNoCopper"/>  
      </a-form-item>
    </a-col>
    <a-col :span='12'>
      <a-form-item label="蚀刻前二钻" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.etching2RL"/>  
      </a-form-item>
    </a-col>
  </a-row>

  <a-row>
    <a-col :span='12'>
      <a-form-item label="假双面板" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.fakeDoubleSide"/>  
      </a-form-item>
    </a-col>
    <a-col :span='12'>
      <a-form-item label="单独走二钻" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
        <a-checkbox v-model="ParameterForm.special2RL"/>  
      </a-form-item>
    </a-col>
  </a-row>

  <a-row>
    <a-col :span='12'>
    <a-form-item label="板材需带水印" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
      <a-checkbox v-model="ParameterForm.isPlankWatermark"/>  
    </a-form-item>
    </a-col>
    <a-col :span='12'>
      <a-form-item :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          
      </a-form-item>
    </a-col>
  </a-row>

  <!-- <a-row>
    <a-form-item label="提示"  >
      <a-input style=" width:380px" v-model="ParameterForm.msg_"/>  
    </a-form-item>
  </a-row> -->
  
  </a-form>
  
</template>

<script>
import { getBtoParameter } from '@/services/projectMake'
export default {
  name:'EditParametersMake',   
  props:{
    selectedRowsData:{
      type:Object
    },
    ParameterData:{
      type:Object
    }
  },

  created(){    
    console.log('this.ParameterData:',this.ParameterData)
    this.$nextTick(function () {
      this.ParameterForm.factoryNo = this.ParameterData.factoryNo=='true' ? true : false ;
      this.ParameterForm.isShuChu = this.ParameterData.isShuChu=='true' ? true : false  ;
      this.ParameterForm.vCutToWX = this.ParameterData.vCutToWX=='true' ? true : false ;
      this.ParameterForm.posNeg = this.ParameterData.posNeg || '';
      this.ParameterForm.oilBlockGreaterThan20_20 = this.ParameterData.oilBlockGreaterThan20_20=='true' ? true : false  ;
      this.ParameterForm.bMCLToWZ = this.ParameterData.bMCLToWZ=='true' ? true : false ;
      this.ParameterForm.minLineWS = this.ParameterData.minLineWS || '';
      this.ParameterForm.innerMinLineWS = this.ParameterData.innerMinLineWS || '';
      this.ParameterForm.thickCopperBaseThickness = this.ParameterData.thickCopperBaseThickness || '';
      this.ParameterForm.bgalcSmallAlert = this.ParameterData.bgalcSmallAlert=='true' ? true : false  ;
      this.ParameterForm.vCut = this.ParameterData.vCut=='true' ? true : false  ;
      this.ParameterForm.counterboreCopper = this.ParameterData.counterboreCopper=='true' ? true : false  ;
      this.ParameterForm.counterboreNoCopper = this.ParameterData.counterboreNoCopper=='true' ? true : false  ;
      this.ParameterForm.etching2RL = this.ParameterData.etching2RL=='true' ? true : false  ;
      this.ParameterForm.fakeDoubleSide = this.ParameterData.fakeDoubleSide=='true' ? true : false  ;
      this.ParameterForm.special2RL = this.ParameterData.special2RL=='true' ? true : false  ;
      this.ParameterForm.isPlankWatermark = this.ParameterData.isPlankWatermark=='true' ? true : false  ;
      // this.ParameterForm.msg_ = this.ParameterData.msg_ || '';
    });
    
  },
  data() {
    return {
      OrderNumber:'',
      dataClassList:[],
      ParameterForm:{
        "factoryNo": false,                        // 不加厂编
        "isShuChu": false,                         // 托管输出: 
        "vCutToWX": false,                         // 先V割后成型
        "posNeg": '',                           // 正负片
        "oilBlockGreaterThan20_20": false,         // 白油块大于20*20mm
        "bMCLToWZ": false,                         // 先喷锡后文字
        "minLineWS": "",                        // 外层线宽线距
        "innerMinLineWS": '',                   // 内层线宽线距 
        "thickCopperBaseThickness": "",         // 厚铜超制程
        "bgalcSmallAlert": false,                  // BGA/IC开窗小
        "vCut": false,                             // 是否v
        "counterboreCopper": false,                // 有铜沉头孔/控深槽
        "counterboreNoCopper": false,              // 无铜沉头孔/控深槽
        "etching2RL": false,                       // 蚀刻前二钻 
        "fakeDoubleSide":false,                   // 假双面板
        "special2RL": false,                       // 单独走二钻
        "isPlankWatermark": false,                 // 板材需带水印
        // "msg_": '',                             // 提示:
              
        
      }
    };
  },
  methods: { 
    selectClick(){      
      getBtoParameter(945).then(res =>{
        if(res.code){   
          this.dataClassList = res.data 
          console.log('this.dataClassList',this.dataClassList)   
         
        }else {
          this.$message.error(res.message)
        } 
      })       
    },
    filterOption(input, option) {
      return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
   
    
  },
  mounted () {
    this.selectClick()
  }
};
</script>