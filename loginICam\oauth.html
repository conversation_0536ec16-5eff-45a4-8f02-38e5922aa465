<!DOCTYPE html>
<html lang="en" class="beauty-scroll">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">  
    <link rel="icon" href="./img/bn2.png">   
    <title>授权登录</title> 
  </head>
  <style >
    * {
      padding: 0;
      margin: 0
    }
    
    html,
    body {
      /* width: 100%;
      height: 100%; */
      overflow-y:auto;
      overflow-x: hidden;
      /* font-family: "微软雅黑", arial */
      /*font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;*/
    }    
    body {
      background-color: #f3f3f3
    }
    .error {     
      color: #ff0000;
      padding: 6px;  
      /* border: 1px solid #ff0000;
      background-color: #ffdddd;   */
      display: none;
    }
    .error1 {     
      color: #ff0000;
      padding: 4px;     
      display: none;
      margin-bottom:20px;
      margin-top:2px;
    }
    .error2 {     
      color: #ff0000;
      padding: 4px;     
      display: none;
      margin-bottom:20px;
      margin-top:2px;
    }
    
    .main {
      display: flex;
      margin: 0px auto;
      width: 580px;
      height: 410px;
      background: #ffffff;
      padding: 20px;
      position: relative;   
    }
    
    .main .img {
      width: 280px;
      border-radius: 2px;
      height:90%;
    }
    
    .main .close {
      width: 12px;
      height: 12px;
      position: absolute;
      top: 22px;
      right: 22px;
      background: url("/web/images/login/login_close.png") no-repeat center center;
      background-size: 100%;
      cursor: pointer;
      display: none
    }
    .inputSTY{
      width:256px;
      height: 40px;
      padding: 0  11px;
      font-size: 16px;
      margin-bottom:24px;
      border:1px solid #d9d9d9;
      border-radius: 4px;
    }
    .inputSTY:hover{
      border-color:#ff9900!important;
    }
    input:focus{
      outline: none; 
      border:1px solid #ff9900!important;
    }
    
    .main .close:hover {
      background: url("/web/images/login/login_close_hover.png") no-repeat center center;
      background-size: 100%
    }
    
    .main .login-wrap {
      width: 280px;
      float: right;
      margin-left:20px;
    }
    
    .main .login-wrap a:hover {
      text-decoration: underline;
    }
    
    .main .login-wrap .title {
      font-size: 20px;
      text-align: center;
      color: #000000;
      padding: 25px 0 0;
      margin-bottom:60px;
      position: relative;
    }
    .main .login-wrap .title span {      
      position: absolute;
      top:30px;
      left:80px;
    }
    
    .main .login-wrap .tabs {
      width: 234px;
      height: 32px;
      background-color: #f0f0f0;
      border-radius: 16px;
      display: flex;
      margin: 20px auto 30px auto
    }
    
    .main .login-wrap .tabs p {
      line-height: 32px;
      font-size: 16px;
      font-weight: 500;
      color: #000000;
      width: 50%;
      text-align: center;
      cursor: pointer
    }
    
    .main .login-wrap .tabs p.current {
      width: 115px;
      height: 30px;
      background: #ffffff;
      border-radius: 15px;
      margin-top: 1px
    }
    
    .l {
      display: flex;
      justify-content: space-around;
    }
    
    .main .login-wrap .tabs p.r {
      margin-right: 1px
    }
    
    .login-wrap .tabs-item .user-wrap {
      width: 300px;
      height: 40px;
      background: #ffffff;
      position: relative;
      margin: 35px 0 0;
    }
    
    .login-wrap .tabs-item .user-wrap .icon {
      width: 16px;
      height: 16px;
      position: absolute;
      top: 12px;
      left: 12px
    }
    
    .login-wrap .tabs-item .user-wrap .icons {
      width: 16px;
      height: 16px;
      position: absolute;
      right: 12px;
      top: 12px;
      cursor: pointer
    }
    
    .login-wrap .tabs-item .user-wrap .icons.by {
      background: url("/web/images/login/icon_by.png") no-repeat center;
      background-size: 100%
    }
    
    .login-wrap .tabs-item .user-wrap .icons.zy {
      background: url("/web/images/login/icon_zy.png") no-repeat center;
      background-size: 100%
    }
    
    .login-wrap .tabs-item .user-wrap .icon.username {
      background: url("/web/images/login/icon_user.png") no-repeat center;
      background-size: 100%
    }
    
    .login-wrap .tabs-item .user-wrap .icon.password {
      width: 14px;
      height: 16px;
      background: url("/web/images/login/icon_password.png") no-repeat center;
      background-size: 100%
    }
    
    .login-wrap .tabs-item .user-wrap input {
      width: 100%;
      height: 40px;
      line-height: 40px;
      border: 1px solid #e9e9e9;
      border-radius: 4px;
      text-indent: 40px;
      font-size: 14px;
      outline: none
    }
    
    .login-wrap .tabs-item .user-error {
      padding: 6px 0 12px;
      font-size: 12px;
      color: #999999
    }
    
    .login-wrap .tabs-item .user-error.error {
      color: #FF2121
    }
    
    .login-wrap .tabs-item .user-error.error.hidden {
      visibility: hidden
    }
    
    .login-wrap .tabs-item .user-error.error.show {
      visibility: inherit
    }
    
    .login-wrap .tabs-item .user-error.error .icon-tips {
      background: url("/web/images/login/icon_error.png") no-repeat center;
      background-size: 100%
    }
    
    .login-wrap .tabs-item .user-error .icon-tips {
      width: 16px;
      height: 16px;
      display: inline-block;
      vertical-align: middle;
      background: url("/web/images/login/login_tips.png") no-repeat center;
      background-size: 100%;
      margin-top: -2px;
      margin-right: 4px
    }
    
    .login-wrap .tabs-item .user-checked {
      display: flex;
      justify-content: space-between;
      margin: 30px 0 0;
    }
    
    .login-wrap .tabs-item .user-checked label {
      font-size: 12px;
      color: #666;
      margin: 0 26px 0 2px;
      cursor: pointer;
      user-select: none;
    }
    
    .login-wrap .tabs-item .user-checked label input[type='checkbox'] {
      width: 16px;
      height: 16px;
      display: inline-block;
      vertical-align: middle;
      margin: -2px 4px 0 0;
    }
    
    .login-wrap .tabs-item .user-checked .link {
      font-size: 12px;
      color: #999;
      cursor: pointer;
    }
    
    .login-wrap .tabs-item .user-checked .link:hover {
      opacity: 0.9;
    }
    
     #loginBtn {
      width: 300px;
      height: 40px;
      background-color: #01a279;
      border-radius: 3px;
      margin-top: 30px;
      text-align: center;
      cursor: pointer;
      line-height: 40px;
      color: #ffffff;
      letter-spacing: 4px;
      border: none;
      outline: none;
    }
    
     #loginBtn:hover {
      opacity: 0.9;
    }
    
    .login-wrap .tabs-item .user-register {
      margin-top: 20px;
      text-align: right;
      font-size: 13px;
      color: #999999
    }
    
    .login-wrap .tabs-item .user-register .link {
      color: #01a279;
      cursor: pointer;
    }
    
    .login-wrap .tabs-item .user-register .link:hover {
      text-decoration: underline;
    }
    
    .ui-item {
      width: 300px
    }
    
    .login-wrap .tabs-item .ui-phone-item {
      background: #ffffff;
      border: 1px solid #e9e9e9;
      border-radius: 4px;
      height: 39px;
      position: relative
    }
    
    .login-wrap .tabs-item .ui-phone-item select {
      height: 36px;
      border: none;
      font-size: 12px;
      position: absolute;
      top: 2px;
      left: 0;
      z-index: 1;
      outline: none
    }
    
    .login-wrap .tabs-item .ui-phone-item input[type='text'] {
      width: 100%;
      border: none;
      height: 39px;
      font-size: 14px;
      position: absolute;
      left: 0;
      top: 0;
      text-indent: 120px;
      outline: none
    }
    
    .login-wrap .tabs-item .drag_verify {
      position: relative;
      height: 38px;
      margin-bottom: 20px;
      margin-top: 20px
    }
    
    .login-wrap .tabs-item .sms-item {
      background: #ffffff;
      border: 1px solid #e9e9e9;
      border-radius: 4px;
      height: 40px;
      position: relative
    }
    
    .login-wrap .tabs-item .sms-item input[type='text'] {
      width: 180px;
      height: 38px;
      font-size: 14px;
      text-indent: 20px;
      border: none;
      outline: none
    }
    
    .login-wrap .tabs-item .sms-item .line {
      height: 25px;
      border-right: 1px solid #e9e9e9;
      display: inline-block;
      vertical-align: middle
    }
    
    .login-wrap .tabs-item .sms-item .J_get_smscode {
      line-height: 40px;
      font-size: 14px;
      margin-left: 15px;
      cursor: pointer;
      color: #01a279
    }
    
    .login-wrap .tabs-item .sms-item .J_get_smscode:hover {
      text-decoration: underline;
    }
    
    .login-wrap .tabs-item .ui-item #loginSignBtn {
      width: 300px;
      height: 40px;
      background: #01a279;
      border-radius: 3px;
      text-align: center;
      cursor: pointer;
      line-height: 40px;
      color: #ffffff;
      letter-spacing: 4px;
      border: none;
      font-size: 16px;
      cursor: pointer;
      margin-top: 20px
    }
    
    .login-wrap .tabs-item .ui-item #loginSignBtn:hover {
      opacity: 0.9;
    }
    
    .login-wrap .tabs-item .ui-protocal {
      margin-top: 24px;
      text-align: center;
      position: relative
    }
    
    .login-wrap .tabs-item .ui-protocal label {
      font-size: 12px;
      color: #999;
    }
    
    .login-wrap .tabs-item .ui-protocal label input[type='checkbox'] {
      width: 15px;
      height: 15px;
      display: inline-block;
      vertical-align: middle;
      margin: -2px 4px 0 0;
    }
    
    .login-wrap .tabs-item .ui-protocal a {
      color: #666666;
      text-decoration: underline;
      cursor: pointer;
    }
    
    .login-wrap .tabs-item .ui-protocal a:hover {
      text-decoration: underline;
    }
    
    .ui-item .validator-error {
      box-shadow: 0 0 1px 1px #e86868;
      border: 1px solid #e86868;
      background-color: snow;
      border-radius: 4px
    }
    
    .valid_warn {
      position: absolute;
      top: 40px;
      left: 0;
      color: #e86868;
      background-color: #fff;
      font-size: 12px
    }
    form-item{
      height:40px;
      line-height: 40px;
    }
    </style>
  <body class="body1">
    <div class="main">
      <button
      :loading="logging"
      style="width: 100%; margin-top: 24px;font-size: 16px;"
      size="large"                                         
      id="loginBtn"
      onclick="validateLogin()"                        
      >确认绑定</button>      
    </div>
  </body>
 
  <script src="./js/jquery-1.8.3.js"></script>
  <script language="javascript"> 
   function validateLogin(){
    console.log('当前页面',window.location.href)
    event.preventDefault()
    // var xhr = new XMLHttpRequest();
    // // var url = 'http://*************:1002/api/app/e-mSPermission/permissions-for-iCAM?username='+ sUserName + '&password=' + sPassword; 
    // var url = 'http://emsapi.bninfo.com/api/app/e-mSPermission/permissions-for-iCAM?username='+ sUserName + '&password=' + sPassword; 
    //     xhr.open('GET', url);
    //     xhr.responseType = 'json';
    //     xhr.onload = function() {       
    //       if (xhr.status === 200) { 
    //         let res = xhr.response         
    //         if(res.code){           
    //         }else{
            
    //         }
    //       } else {
          
    //       }
    //     };
    //     xhr.onerror = function() {
    //       console.log('Request failed');
    //     };
    //     xhr.send();     
   }   
  
  </script>
</html>