<!-- 工程管理 - 工艺流程 - 按钮  -->
<template>
  <a-form-model layout="inline" :model="form" style="margin-bottom: 4px">
    <a-form-model-item label="生产型号">
      <a-input
        v-model="form.orderName"
        placeholder="请输入生产型号"
        allowClear
        @input="change"
        @keyup.enter.native="searchReport"
        style="height: 28px"
        disabled
      /><!--:disabled="!bianjiad" -->
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.FlowChaXun')">
      <a-button type="primary" @click="searchReport" :disabled="form.orderName === ''" style="width: 60px; padding: 0"> 查找(F) </a-button>
    </a-form-model-item>

    <a-form-model-item v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.FlowRepier')">
      <a-button type="primary" @click="bianjiad1" style="width: 80px; padding: 0" :disabled="selectedRowKeysArray.length <= 0">
        编辑流程(E)
      </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.ParRepier') && bianjiad">
      <a-button type="primary" @click="bianji" style="width: 80px; padding: 0" :disabled="selectedRowKeysArray1.length <= 0"> 编辑参数(R) </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="!bianjiad">
      <a-button type="primary" @click="cancel" style="width: 80px; padding: 0"> 取消编辑 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="!bianjiad">
      <a-button type="primary" @click="signBtn" style="width: 80px; padding: 0" :loading="saveloading"> 保存编辑(S) </a-button>
    </a-form-model-item>
    <a-form-model-item>
      <a-button type="primary" style="width: 80px; padding: 0" @click="editsteps" :disabled="selectedRowKeysArray1.length <= 0"> 编辑工步 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.MiToErp')" style="float: right">
      <a-button type="primary" style="width: 80px; padding: 0" @click="uploaderp('formal')" :disabled="selectedRowKeysArray.length <= 0">
        上传ERP
      </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.MiToErpTest')" style="float: right">
      <a-button type="primary" style="width: 80px; padding: 0" @click="uploaderp('test')" :disabled="selectedRowKeysArray.length <= 0">
        新ERP上传
      </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.BackVerify')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="IndicationFallback" :disabled="selectedRowKeysArray.length <= 0">
        指示回退
      </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.SendVerify')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="InstructionReview" :disabled="selectedRowKeysArray.length <= 0">
        指示审核
      </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.BackOnline')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="Offline" :disabled="selectedRowKeysArray.length <= 0"> 下网 </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.SendingOnline')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="surftheInternet" :disabled="selectedRowKeysArray.length <= 0"> 上网 </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.SendingInstructions')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="Sendinginstructions" :disabled="selectedRowKeysArray.length <= 0">
        发送指示
      </a-button>
    </a-form-model-item>
    <!-- <a-form-model-item  style="float:right;" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.FlowMI')">
        <a-button type="primary" style="width: 80px; padding: 0" @click="printpdf" :disabled='selectedRowKeysArray.length <= 0' >
        打印PDF
        </a-button>
      </a-form-model-item>   -->
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.FlowMI')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="Predownloadpdf" :disabled="selectedRowKeysArray.length <= 0">
        预览MI
      </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.ImportData')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="$emit('Dataexport')" :disabled="selectedRowKeysArray1.length <= 0">
        数据导入
      </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.NopeCallBack')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="$emit('Importofreturnorders')"> 返单回导 </a-button>
    </a-form-model-item>
    <a-form-model-item style="float: right" v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.FinalCheckList')">
      <a-button type="primary" style="width: 80px; padding: 0" @click="$emit('Auditinspection')" :disabled="selectedRowKeysArray.length <= 0">
        审核检查
      </a-button>
    </a-form-model-item>
    <!--v-if="checkPermission('MES.EngineeringModule.TechnologicalProcess.TechFlowModifyRecord')" -->
    <a-form-model-item style="float: right">
      <a-button type="primary" style="width: 80px; padding: 0" @click="$emit('Modifyapplication')" :disabled="selectedRowKeysArray.length <= 0">
        修改申请
      </a-button>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "FormBasic",
  inject: ["reload"],
  props: ["selectedRowKeysArray", "selectedRowKeysArray1", "bianjiad", "fold"],
  data() {
    return {
      form: {
        orderName: "",
      },
      saveloading: false,
    };
  },
  created() {
    if (this.$route.query.pdctno) {
      this.form.orderName = this.$route.query.pdctno;
      this.searchReport();
    }
  },
  methods: {
    checkPermission,
    IndicationFallback() {
      this.$emit("IndicationFallback");
    },
    InstructionReview() {
      this.$emit("InstructionReview");
    },
    Offline() {
      this.$emit("Offline");
    },
    printpdf() {
      this.$emit("printpdf");
    },
    Predownloadpdf() {
      this.$emit("Predownloadpdf");
    },
    surftheInternet() {
      this.$emit("surftheInternet");
    },
    Sendinginstructions() {
      this.$emit("Sendinginstructions");
    },
    searchReport() {
      var payload = this.form.orderName;
      var arr1 = payload.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      payload = arr1.join("");
      this.form.orderName = payload;
      this.$emit("getFlowList", payload);
    },
    bianjiad1() {
      this.$emit("bianjiad1");
    },
    editsteps() {
      this.$emit("editsteps");
    },
    bianji() {
      this.$emit("bianji");
    },
    change() {
      this.$emit("change");
    },

    cancel() {
      this.$emit("cancel");
    },
    signBtn() {
      this.$emit("signBtn");
    },
    uploaderp(type) {
      this.$emit("uploaderp", type);
    },
    foldClick() {
      this.$emit("foldClick");
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-input {
  height: 28px;
}
/deep/.ant-form-item {
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 0;
}
</style>
