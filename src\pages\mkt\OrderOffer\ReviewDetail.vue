<!-- 市场管理 - 订单报价- 订单详情-->
<template>
  <a-spin :spinning="spinning">
  <div class="orderDetail">
    <order-action 
    ></order-action>
    <div style="position:relative">
      <!-- <span style="display: inline-block; position: absolute; left: 8%;font-size: 20px;color: red;font-weight: 600;">{{orderno}}</span>  -->
    <a-tabs default-active-key="1" @change="callback" >           
      <!-- <a-tab-pane key="1" tab="销售信息"  >
        <sales-info  :editFlag="editFlag" ref="editForm1" :showData="showData" :selectOption="selectOption" ></sales-info>        
      </a-tab-pane> -->
      <a-tab-pane key="1" :tab="'【'+ orderno + '】--预审信息'" >
        <order-info ref="editForm"  :showData="showData" :selectOption="selectOption" :boardBrandList="boardBrandList" :requiredLinkConfigList="requiredLinkConfigList"></order-info>
      </a-tab-pane> 
      <!-- <a-tab-pane key="3" tab="销售绩效" disabled>
        Content of Tab Pane 3
      </a-tab-pane> -->
    </a-tabs>
    </div>
     <!-- 弹窗制作1 -->
     <a-modal    
      title=" 订单详情确认"
      :visible="dataVisible"
      @cancel="reportHandleCancel"
      @ok="handleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered

   >
   <div style="height:200px;overflow-y: auto;">
   <div v-for="(ite,index) in message" :key="index" >
    <p>{{ite}}</p>
   </div>
  </div>
   </a-modal>  
   <!-- 弹窗制作1 -->
  

  </div>
  </a-spin>
</template>

<script>
import OrderAction from "@/pages/mkt/OrderOffer/module/OrderAction";
import OrderInfo from "@/pages/mkt/OrderDetail/subassembly/OrderInfo";
// import SalesInfo from "@/pages/mkt/OrderReview/module/SalesInfo";
import {getEditOrderInfo, updateOrderInfo,orderLog,getOrderWF,requiredLinkConfig,} from "@/services/mkt/orderInfo";
import {verifySalesInfoUpdate, } from "@/services/mkt/OrderReview";
import { boardBrandItems,} from "@/services/projectIndicate";
import {mapState,} from 'vuex';
import Cookie from 'js-cookie';
const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 40,    
  },
  {
    title: '操作时间',
    dataIndex: 'createTime',
    align: "center",
    width: 100,    
  },
  {
    title: '操作详情',
    dataIndex: 'content',
    align: "left",
    width:700,    
  },
  
]
export default {
  name: "ReviewDetail",
  components:{OrderInfo, OrderAction},
  data(){
    return {
      editFlag:false,
      showData:{},
      spinning:false,
      viewLogData:[],
      add:false,
      key:'1',
      selectOption: [],
      boardBrandList:[],
        // 弹窗制作2

     dataVisible:false,
     message:[],    
       type:'',
      deletId:'',
      orderno:'',
      requiredLinkConfigList:{}
      // 弹窗制作2
      
    }
  },
  async created() {
    await this.getDetailInfo()
    this.getViewLog() 
    if(this.$route.query.orderNo){
        this.orderno = this.$route.query.orderNo
    }
    await this.getRequiredLink()
  },
  updated(){
    this.$refs.editForm.setStyle()
  },
  watch:{
    watch:{
    'pagination':{
      handler(val){
        console.log(val)
      }
    }
  },
  },
    // 获取当前登陆账号信息
    computed: {
  ...mapState('account', ['user',]),
  },
  methods: {     
    // 参数信息
    async  getDetailInfo(){      
      let id = this.$route.query.id
      if(id){
        
        await getEditOrderInfo(id).then(res => {
        this.spinning = true
        if (res.code) {
          res.data.holeDensity = res.data.holeDensity ? res.data.holeDensity:'0'
          this.showData = res.data 
          if(this.showData.pinBanType){
            let arr =this.showData.pinBanType.toLowerCase().split('x')
            this.$set(this.showData,'pinBanType1',arr[0]) 
            this.$set(this.showData,'pinBanType2',arr[1])
          }else{
            this.$set(this.showData,'pinBanType1',1) 
            this.$set(this.showData,'pinBanType2',1)            
          }
          if(this.showData.processEdges){
            let arr1 =  this.showData.processEdges.split(':')
            if(arr1[0] == 'none'){
              arr1[0] =  '无'
            }else if(arr1[0] == 'updown'){
              arr1[0] =  '上下方向'
            }else if(arr1[0] == 'leftright'){
              arr1[0] =  '左右方向'
            }else if(arr1[0] == 'both'){
              arr1[0] =  '四边方向'
            }else if(arr1[0] == ''){
              arr1[0] =  ''
            }
            this.showData.processEdges = arr1[0]+ ':' + arr1[1]
          }else{
            this.showData.processEdgesStrL = 'none'
            this.showData.processEdgesStrR = 0

          }
          if(this.showData.delType == 'set' && this.showData.num && this.showData.setBoardHeight && this.showData.setBoardWidth){
            this.showData.boardArea = (this.showData.num * this.showData.setBoardHeight *this.showData.setBoardWidth)/1000000 
          }
          if(this.showData.delType == 'pcs' && this.showData.num && this.showData.setBoardHeight && this.showData.setBoardWidth && this.showData.su){
            this.showData.boardArea = ((this.showData.num * this.showData.setBoardHeight *this.showData.setBoardWidth))/1000000/this.showData.su
          }
          if(!this.showData.vCut){
            this.showData.vCut = 'vcut'
          }
           if(this.showData.surfaceFinishJsonDto.goldThickness){
            this.showData.surfaceFinishJsonDto.goldThickness  = (this.showData.surfaceFinishJsonDto.goldThickness).toString()
          }
          if(this.showData.surfaceFinishJsonDto.tinThickness){
            this.showData.surfaceFinishJsonDto.tinThickness  = (this.showData.surfaceFinishJsonDto.tinThickness).toString()
          }
          if(this.showData.surfaceFinishJsonDto.nickelThickness){
            this.showData.surfaceFinishJsonDto.nickelThickness  = (this.showData.surfaceFinishJsonDto.nickelThickness).toString()
          }
          if(this.showData.plateType=='cg'){
              this.$refs.editForm.showCG = true
            }else{
              this.$refs.editForm.showCG = false
            }
            if(this.showData.plateType=='hdi'){
              this.$refs.editForm.showHDI = true        
            }else{
              this.$refs.editForm.showHDI=false       
            }
            if(this.showData.plateType=='mm'){
              this.$refs.editForm.showMM = true       
            }else{       
              this.$refs.editForm.showMM = false
            }
            if(this.showData.boardLayers == 0){
              this.$refs.editForm.show0 = true       
            }else{       
              this.$refs.editForm.show0 = false
            }
            if(this.showData.boardLayers == 1){
              this.$refs.editForm.show1 = true       
            }else{       
              this.$refs.editForm.show1 = false
            }
            if(this.showData.boardLayers == 2){
              this.$refs.editForm.show2 = true       
            }else{       
              this.$refs.editForm.show2 = false
            }
            if(this.showData.boardLayers >= 4){
              this.$refs.editForm.showMore = true       
            }else{       
              this.$refs.editForm.showMore = false
            }

          console.log('this.surfaceFinishJsonDto',this.showData.surfaceFinishJsonDto)

         
        }
      }).finally(()=>{
        this.spinning = false
      })
    }
   
      
    },
    editInfo(){
      if(this.showData.status!="审核中"){
        this.$message.error("当前订单状态不能编辑！")
        return
      }
      if(this.showData.checkAccount){
        this.showData.checkAccount = this.showData.checkAccount.toLowerCase()
      }   
      if(this.user.userName){
        this.user.userName = this.user.userName.toLowerCase()
      }   
      
      if(this.showData.checkAccount!=this.user.userName){
        this.$message.error("该订单审核人是【"+this.showData.checkName+"】,不能编辑！")
        return
      }
      // this.editFlag = !this.editFlag
      this.editFlag = true
      // if(this.showData.preName!=""){
      //   this.$message.error("预审人不是你，不能编辑！")
      // }else{
      //   this.$refs.editForm.getEditData()
      // }     
      this.$refs.editForm.getEditData()
     
      
    },  
    callback(key) {
      this.$nextTick(()=>{
        if(this.$refs.editForm){
          if(this.showData.plateType=='cg'){
              this.$refs.editForm.showCG = true
            }else{
              this.$refs.editForm.showCG = false
            }
            if(this.showData.plateType=='hdi'){
              this.$refs.editForm.showHDI = true        
            }else{
              this.$refs.editForm.showHDI=false       
            }
            if(this.showData.plateType=='mm'){
              this.$refs.editForm.showMM = true       
            }else{       
              this.$refs.editForm.showMM = false
            }
            if(this.showData.boardLayers == 0){
              this.$refs.editForm.show0 = true       
            }else{       
              this.$refs.editForm.show0 = false
            }
            if(this.showData.boardLayers == 1){
              this.$refs.editForm.show1 = true       
            }else{       
              this.$refs.editForm.show1 = false
            }
            if(this.showData.boardLayers == 2){
              this.$refs.editForm.show2 = true       
            }else{       
              this.$refs.editForm.show2 = false
            }
            if(this.showData.boardLayers >= 4){
              this.$refs.editForm.showMore = true       
            }else{       
              this.$refs.editForm.showMore = false
            }
        }
      })
      this.key = key
    },   
    // dataSave(){
    //   console.log('1',this.$refs.editForm,)
    //   console.log(this.$refs.editForm.formData,)  
    //   var r = /^\+?[1-9][0-9]*$/      
    //   let params = this.$refs.editForm.formData
    //   if(this.$refs.editForm1){
    //     params.needReportList = this.$refs.editForm1.needReportList              
    //   }      
    //   if(params.orderType){
    //     params.orderType = Number(params.orderType)
    //   }  
    //   if(params.boardLayers){
    //     params.boardLayers = Number(params.boardLayers)
    //   } 
    //   if(params.boardThickness){
    //     params.boardThickness = Number(params.boardThickness)
    //   }
    //   if(params.copperThickness){
    //     params.copperThickness = Number(params.copperThickness)
    //   }
    //   if(params.innerCopperThickness){
    //     params.innerCopperThickness = Number(params.innerCopperThickness)
    //   }
    //   if(params.su){
    //     params.su = Number(params.su)
    //   }  
    //   if(params.vCutKnifeNum){
    //     params.vCutKnifeNum = Number(params.vCutKnifeNum)
    //   } 
    //   if(params.grooveHeight){
    //     params.grooveHeight = Number(params.grooveHeight)
    //   }
    //   if(params.grooveWidth){
    //     params.grooveWidth = Number(params.grooveWidth)
    //   }
    //   if(params.boardWidth){
    //     params.boardWidth = Number(params.boardWidth)
    //   }
    //   if(params.boardHeight){
    //     params.boardHeight = Number(params.boardHeight)
    //   }  
    //   if(params.setBoardWidth){
    //     params.setBoardWidth = Number(params.setBoardWidth)
    //   } 
    //   if(params.setBoardHeight){
    //     params.setBoardHeight = Number(params.setBoardHeight) 
    //   }
    //   if(params.testPointNum){
    //     params.testPointNum = Number(params.testPointNum)
    //   }
    //   if(params.vias){
    //     params.vias = Number(params.vias)
    //   }
    //   if(params.totalHoleNum){
    //     params.totalHoleNum = Number(params.totalHoleNum)
    //   }  
    //   if(params.slotHoleNum){
    //     params.slotHoleNum = Number(params.slotHoleNum)
    //   } 
    //   if(params.blindHoleMin){
    //     params.blindHoleMin = Number(params.blindHoleMin)
    //   }
    //   if(params.blindHoleNum){
    //     params.blindHoleNum = Number(params.blindHoleNum)
    //   }
    //   if(params.poreDensity){
    //     params.poreDensity = Number(params.poreDensity)
    //   }else{
    //     params.poreDensity = null
    //   }
    //   if(params.testPointNum){
    //     params.testPointNum = Number(params.testPointNum)
    //   }
    //   if(params.profileHoleNum){
    //     params.profileHoleNum = Number(params.profileHoleNum)
    //   }
    //   if(params.backDrillNum){
    //     params.backDrillNum = Number(params.backDrillNum)
    //   }  
    //   if(params.totalLayer){
    //     params.totalLayer = Number(params.totalLayer)
    //   } 
    //   if(params.ipcLevel){
    //     params.ipcLevel = Number(params.ipcLevel)
    //   }
    //   if(params.gbNum){
    //     params.gbNum = Number(params.gbNum)
    //   }
    //   if(params.ppNum){
    //     params.ppNum = Number(params.ppNum)
    //   }
    //   if(params.edges){
    //     params.edges = Number(params.edges)
    //   }
    //   if(params.throughHoleNum){
    //     params.throughHoleNum = Number(params.throughHoleNum)
    //   }
    //   if(params.ctrlBlindHole){
    //     params.ctrlBlindHole = Number(params.ctrlBlindHole)
    //   }  
    //   if(params.halfSthNum){
    //     params.halfSthNum = Number(params.halfSthNum)
    //   } 
    //   if(params.pinBanNum){
    //     params.pinBanNum = Number(params.pinBanNum)
    //   }
    //   if(params.num){
    //     params.num = Number(params.num)
    //   }
    //   if(params.boardArea){
    //     params.boardArea = Number(params.boardArea)
    //   }
    //   if(params.status){
    //     params.status = Number(params.status)
    //   } 
    //   if(params.surfaceFinishJsonDto.goldThickness){
    //     params.surfaceFinishJsonDto.goldThickness  = Number(params.surfaceFinishJsonDto.goldThickness)
    //   }
    //   if(params.surfaceFinishJsonDto.tinThickness){
    //     params.surfaceFinishJsonDto.tinThickness  = Number(params.surfaceFinishJsonDto.tinThickness)
    //   }
    //   if(params.surfaceFinishJsonDto.nickelThickness){
    //     params.surfaceFinishJsonDto.nickelThickness  = Number(params.surfaceFinishJsonDto.nickelThickness)
    //   }
      
     
    //   if(params.processEdgesStrL == '无'){
    //     params.processEdgesStrL =  'none'
    //   }else if(params.processEdgesStrL == '上下方向'){
    //     params.processEdgesStrL =  'updown'
    //   }else if(params.processEdgesStrL == '左右方向'){
    //     params.processEdgesStrL =  'leftright'
    //   }else if(params.processEdgesStrL == '四边方向'){
    //     params.processEdgesStrL =  'both'
    //   }else if(params.processEdgesStrL == ''){
    //     params.processEdgesStrL =  ''
    //   }
    //   params.processEdges = params.processEdgesStrL + ":" + params.processEdgesStrR 
    //   if( params.pinBanType1 && params.pinBanType2){
    //     params.pinBanType =  params.pinBanType1 + 'x' +  params.pinBanType2
    //   } 
    //   if(params.needReportList){
    //     params.needReportList=params.needReportList.toString() 
    //   }     
    //   // 验证必填项
    //   // 内容一起提示
    //   if(!params.lineWidth || !params.lineSpacing || !params.plateType || (params.plateType=='hdi' && params.boardLayers<=2) || !params.boardThickness || 
    //   params.boardLayers== null  || !params.vias || !params.totalHoleNum || (params.boardLayers > 0 && !params.cuThickness.split('/').every(value => value !='')) ||(
    //   params.boardLayers > 0 && params.cuThickness.split('/').indexOf('0') != -1 )|| !params.holeCopper || !params.holeCopperSpacing || !params.surfaceFinish || !params.routLength
    //   ||!params.pinBanType1 || !params.pinBanType2 || !params.testPointSize ||!params.boardHeight || (!params.boardWidth || (!params.holetoline && params.boardLayers>=4)) ||
    //   !params.setBoardHeight || !params.setBoardWidth || (!params.ppNum && params.boardLayers>=4) || !params.sheetSize || !params.sheetUtilization || !params.iPCLevel || !params.solderColorBottom
    //   || !params.solderColorBottom || !params.solderCover || !params.solderColorBottom ||  !r.test(params.goldfingerThickness) || 
    //   ! r.test(params.goldenFingerArea) ||! r.test(params.goldfingerNieThickness) || !params.fontColor ||(!r.test(params.pinBanType1) || ! r.test(params.pinBanType2)) || 
    //   !params.fontColorBottom || !params.boardBrand || !params.fR4Type || !params.fR4Tg || !params.sheetTrader ||!params.deliveryDate ||!params.delType ||!params.flyingProbeStr){

    //     if(!params.lineWidth){
    //       this.message.push('请填写最小线宽')
    //     }
       
    
    //   if(!params.lineSpacing){
    //     this.message.push('请填写最小线距')
    //   }
      
  
    //   if(!params.plateType){
    //     this.message.push('请选择板类型')
    //   }
      
    
    //   if(params.plateType=='hdi' && params.boardLayers<=2){
    //     this.message.push('板类型与层数不匹配')
    //   }
     
     
    //   if(!params.boardThickness){
    //     this.message.push('请选择板厚')

    //   }   
     
    //   if(params.boardLayers== null ){
    //     this.message.push('请选择层数')
        
    //   } 
    //   if(!params.vias){
    //     this.message.push('请选择通孔最小孔尺寸')        
    //   }
       
    //   if(!params.totalHoleNum){
    //     this.message.push('请填写单元通孔孔数')        
    //   }      
    
    //   if(params.boardLayers > 0 && !params.cuThickness.split('/').every(value => value !='')){
    //     this.message.push('请填写铜厚')
    //   }
     
    //   if(params.boardLayers > 0 && params.cuThickness.split('/').indexOf('0') != -1){
    //     this.message.push('铜厚不能为0')

    //   }
       
    //   if(!params.holeCopper){
    //     this.message.push('请填写最小孔铜')
    //   }
     
    //   if(!params.holeCopperSpacing){
    //     this.message.push('请填写孔铜间距')        
    //   }
       
    //   if(!params.surfaceFinish){
    //     this.message.push('请选择表面处理')         
    //   }
      
    //   if(!params.routLength){
    //     this.message.push('请填写锣带长度')         
    //   }
      
    //   if(!params.pinBanType1 || !params.pinBanType2){
    //     this.message.push('请填写拼版方式')  
    //   }
    //   if(!r.test(params.pinBanType1) || ! r.test(params.pinBanType2)){
    //     this.message.push('拼版方式数为正整数')  
    //   }
      
    //   if(!params.testPointSize){
    //     this.message.push('请填写最小测试点尺寸')        
    //   }
     
    //   if(!params.boardHeight){
    //     this.message.push('请填写单元长')          
    //   }
      
    //   if(!params.boardWidth){
    //     this.message.push('请填写单元宽')         
    //   }
       
    //   if(!params.holetoline && params.boardLayers>=4){
    //     this.message.push('请填写内层孔到线间距')         
    //   }
      
    //   if(!params.setBoardHeight){
    //     this.message.push('请填写成品长')        
    //   }
      
    //   if(!params.setBoardWidth){
    //     this.message.push('请填写成品宽')         
    //   }
      
    //   if(!params.ppNum && params.boardLayers>=4){
    //     this.message.push('请填写pp张数')         
    //   }
      
    //   if(!params.sheetSize){
    //     this.message.push('请选择大料尺寸')        
    //   }
       
    //   if(!params.sheetUtilization){
    //     this.message.push('请填写板材利用率')         
    //   }
       
    //   if(!params.iPCLevel){
    //     this.message.push('请选择验收标准')        
    //   }
     
      
    //   if(!params.solderColor){
    //     this.message.push('请选择顶层阻焊颜色')         
    //   }
       
    //   if(!params.solderColorBottom){
    //     this.message.push('请选择底层阻焊颜色')        
    //   }
         
    //   if(!params.solderCover){    
    //     this.message.push('请选择过孔处理')    
    //   }
     
    //   if(params.isGoldfinger && !r.test(params.goldfingerThickness)){
    //     this.message.push('金手指金厚请输入正整数')        
    //   }    
      
    //   if(params.isGoldfinger && !r.test(params.goldenFingerArea)){
    //     this.message.push('条数请输入正整数')        
    //   }       
      
    //   if(params.isGoldfinger && !r.test(params.goldfingerNieThickness)){
    //     this.message.push('金手指镍厚请输入正整数')         
    //   }    
       
    //   if(!params.fontColor){
    //     this.message.push('请选择顶层字符颜色')
    //   }
      
    //   if(!params.fontColorBottom){
    //     this.message.push('请选择底层字符颜色')        
    //   }
       
    //   if(!params.boardBrand){
    //     this.message.push('请选择板材型号')
    //   }
      
    //   if(!params.fR4Type){
    //     this.message.push('请选择板材类型')        
    //   }
      
    //   if(!params.fR4Tg){
    //     this.message.push('请选择板材TG')         
    //   }     
    //   if(!params.sheetTrader){
    //     this.message.push('请选择板材商') 
    //   }
    //   if(!params.deliveryDate){
    //     this.message.push('请填写客户交期') 
    //   }
    //   if(!params.delType){
    //     this.message.push('请选择交货单位') 
    //   }
    //   if(!params.flyingProbeStr){
    //     this.message.push('请选择测试方式') 
    //   }
    //   // this.message = this.message.toString()
    //   if(this.message.length){
    //     this.dataVisible = true;
    //     return 
    //   }      
    //   this.type = '1'             
    //   }

    // this.$delete(params,'status')    
    //   params.id = this.$route.query.id
    //   console.log('传参',params)
    //   updateOrderInfo(params).then(res=> {
    //     if (res.code){
    //       this.editFlag = false;
    //       this.$message.success('保存成功')
    //       this.getDetailInfo()
    //     }else{
    //        this.$message.error(res.message)
    //        this.editFlag = false;
    //        this.$refs.editForm.getEditData()
    //     }
    //   })
    
      
    // },
     // 弹窗制作3
     handleOk(){
      this.dataVisible=false;
      this.message =[]
    },
    reportHandleCancel(){
      this.dataVisible = false; 
      this.message =[]
    },
    getRequiredLink(){
      let factory = this.$route.query.factory
      const token =  Cookie.get('Authorization')    
      const data = JSON.parse(localStorage.getItem('requiredLinkConfigList',))
      if(data && data.filter(item =>{return item.factory == factory}).length && token){ 
        for(var a=0;a<data.length;a++){
          if(data[a].token == token && data[a].factory == factory){
            this.requiredLinkConfigList =  data[a].data;//本地缓存  
          }
        }   
      }else{          
        requiredLinkConfig(factory,2).then(res=>{
          if(res.code){
            this.requiredLinkConfigList  = res.data 
            let token = Cookie.get('Authorization') 
            let arr  = []
            if(JSON.stringify(this.requiredLinkConfigList) != "{}"){
              if(data == null){
                arr.push({data: this.requiredLinkConfigList, token,factory})
                localStorage.setItem('requiredLinkConfigList', JSON.stringify(arr));//本地缓存
              }else{
                data.push({data: this.requiredLinkConfigList, token,factory})
                localStorage.setItem('requiredLinkConfigList', JSON.stringify(data));//本地缓存 
              }
            }
             
          }else{
            this.$message.error(res.message)
          }
        })
      }
    },
 
    // 查看日志
    getViewLog(){      
      // let OrderId = this.$route.query.id
      // if(OrderId){
      //   this.spinning = true
      //   orderLog(OrderId).then(res => {
      //   if (res.code){  
      //       this.viewLogData = res.data
      //     } else {
      //       this.$message.error(res.message)
      //     }
      // }).finally(()=>{
      //   this.spinning = false
      // })
      // }
      
    },
    

  }
}
</script>

<style scoped lang="less">
/deep/.ant-form-item-label > label::after{
  margin:0 2px 0 2px;
}
.orderDetail {
  padding: 10px;
  min-width: 1670px;
  height: 822px;
  background: #ffffff;
  /deep/ .ant-tabs {
    .viewInfo{
      .ant-table-thead{
        .ant-table-align-left{
          text-align: center!important;;
        }
      }
    }
    margin-top: 10px;
    .ant-tabs-bar{
      margin: 0;
      border-bottom: 1px solid #ccc;
      .ant-tabs-nav-wrap {
        .ant-tabs-ink-bar {
          display: none!important;
        }
      }
      .ant-tabs-tab {
        margin: 0;
        padding: 0 10px;
        border: 1px solid #ccc;
        font-size: 14px;
        height: 34px;
        line-height: 34px;
        border-left: 0;
        font-weight: 500;
        &:nth-child(1){
          border-left: 1px solid #ccc;;
        }
      }
      .ant-tabs-tab-active {
        border-top: 2px solid #f90 !important;
        border-bottom-color:#ffffff ;
        background: #ffffff;

      }

    }
  }
  @media screen and (max-width: 575px) {
  /deep/.ant-form-item-label{
    display: block;
    margin: 0;
    padding: 0 !important;
    line-height: 1.5!important;
    white-space: initial;
    text-align: left!important;
    color:red;
}
@media (max-width: 575px){
  /deep/.ant-form-item-label label::after {
    display: revert!important;
  }

}

} 
}
</style>