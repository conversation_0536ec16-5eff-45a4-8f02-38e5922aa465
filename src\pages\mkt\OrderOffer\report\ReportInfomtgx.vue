<!-- 明天高新销售合同 -->
<template>
  <div class="pdfDom1" style="font-size: 12px">
    <a-button v-print="printObj1" type="primary" class="printstyle" @click="printpdf">打印</a-button>
    <div id="pdfDommtgx" style="font-family: monospace; color: black">
      <div style="text-align: center; width: 100%; font-size: 22px; font-weight: bold; padding-bottom: 10px">
        <div style="display: flex; justify-content: center">
          <img src="@/assets/img/mtgxlogo.png" style="height: 40px; position: relative; right: 20px" />
          成都明天高新产业有限责任公司
        </div>
        <div>产品销售合同</div>
      </div>
      <div style="display: flex; justify-content: space-between; line-height: 3.5ch">
        <div>
          <div>买方:{{ mtgxsalesdata.value_1 }}</div>
          <div></div>
          <br />
          <div>卖方:{{ mtgxsalesdata.value_3 }}</div>
        </div>
        <div style="padding-right: 100px">
          <div>合同编号:{{ mtgxsalesdata.value_3 }}</div>
          <div>签订日期:{{ mtgxsalesdata.value_4 }}</div>
          <div>签订地点:{{ mtgxsalesdata.value_5 }}</div>
        </div>
      </div>
      <div style="font-size: 14px; padding: 10px 0">
        买卖双方在平等自愿、诚实守信基础上、经双方友好协商、依据中国现行法律法规及商业惯例、就买方购卖方所加工产品达成本合同以资共同遵守。
      </div>
      <div>一、产品名称、规格、数量、金额及交货期</div>
      <table border="1" style="width: 100%" class="table1">
        <tr>
          <th>序号</th>
          <th>物料号</th>
          <th>批次号</th>
          <th>品名</th>
          <th colspan="2">长宽(mm)</th>
          <th>工艺要求</th>
          <th>数量(件)</th>
          <th>单价(元/件)</th>
          <th>工程费(元)</th>
          <th>压合费(元)</th>
          <th>树脂塞孔费(元)</th>
          <th>镀金费(元)</th>
          <th>合计金额(元)</th>
        </tr>
        <tr v-for="(item, index) in mtgxsalesdata.price" :key="index">
          <td>{{ item.price1 }}</td>
          <td>{{ item.price2 }}</td>
          <td>{{ item.price3 }}</td>
          <td>{{ item.price4 }}</td>
          <td>{{ item.price5 }}</td>
          <td>{{ item.price6 }}</td>
          <td style="max-width: 200px">{{ item.price7 }}</td>
          <td>{{ item.price8 }}</td>
          <td>{{ item.price9 }}</td>
          <td>{{ item.price10 }}</td>
          <td>{{ item.price11 }}</td>
          <td>{{ item.price12 }}</td>
          <td>{{ item.price13 }}</td>
          <td>{{ item.price14 }}</td>
        </tr>
        <tr>
          <td colspan="5">合计金额人民币(大写):</td>
          <td colspan="7">{{ convertToChineseNum(amountto) }}</td>
          <td colspan="2">￥{{ amountto }}</td>
        </tr>
      </table>
      <div style="padding: 10px 0; line-height: 3.5ch">
        &emsp;&emsp;以上合同价格为卖方专供买方的协议价、为卖方商业机密、根据中国现有相关商业保密法律法规、买方不得将此价格泄露给任何除买方以外的第三方,由此给卖方带来的损失由买方承担;买卖双方都必须保护双方的知识产权不被外泄,由此给对方造成损失将对另一方追责。<br />
        &emsp;&emsp;买方承诺拥有因履行本合同向卖方提供的图纸、技术资料、商标图样、外包装设计等资料的知识产权全部权利(包括但不限于技术规格、设计、发现、发明、产品、技术信息、程序、工艺、改进、开发成果、图纸、笔记、文件、资料和所有其他知识产权权利)或获得了权
        利人的许可,保证向卖方提供的相关资料等不侵犯任何第三方的知识产权,不存在知识产权争议。当卖方根据买方提供的图纸、技术资料等进行加工的产品侵权遭受他人索赔或行政处罚等维权措施的,买方应全力组织协调应对,并赔偿卖方的损失。
      </div>
      <div style="padding: 10px 0; line-height: 3.5ch">
        二、产品的验收标准(包括质量要求):产品按照
        {{
          mtgxsalesdata.value_6
        }}标准执行,无明确标准则按照行业标准执行;产品验收按图纸加工要求验收,买方收货后如对产品有异议,应在一工作周内以书面形式通知卖方,否则视为接受。
      </div>
      <div style="padding: 10px 0; line-height: 3.5ch">
        三、产品的交(提)货期限、交货方式、到货地点(在满足本合同第四条前提下执行条款):<br />
        1、产品的交(提)货日期: {{ mtgxsalesdata.value_7 }} ;<br />
        2、产品的交货方式:R卖方送货¨买方自提自运¨快递代运; <br />
        3、送/代运接收地址: {{ mtgxsalesdata.value_8 }} ,接收人:{{ mtgxsalesdata.value_9 }} ,接收人联系方式 : {{ mtgxsalesdata.value_10 }} ;<br />
        4、买方如要求变更到货地点或接收人,应在本合同约定交货期的2天前以书面通知卖方,否则卖方仍按照本合同约定的地点交货;买方变更之后的地址仍在成都市内的,由卖方送货并承担费用;买方变更之后的地址在成都市外的,由买方承担运费等相关费用<br />
        5、如由卖方送货产品应当面清点并验收,代运货物收货后24小时内对收到产品的品种、数量等提出异议,未提出异议即视为认可并验收。
      </div>
      <div style="padding: 10px 0; line-height: 3.5ch">
        四、结算方式及期限：<br />
        1、本合同签定后 {{ mtgxsalesdata.value_11 }} 月内付清全款；<br />
        2、本合同签定后{{ mtgxsalesdata.value_12 }} 日内买方支付卖方本合同价款的 {{ mtgxsalesdata.value_13 }} %做为预付款,即人民币{{
          mtgxsalesdata.value_14
        }}
        元,余款 {{ mtgxsalesdata.value_15 }} 元在发货前支付； <br />
        3、送货后 {{ mtgxsalesdata.value_16 }} 月内付清全款；
      </div>
      <div style="padding: 10px 0; line-height: 3.5ch">
        五、不可抗拒及纠纷解决：<br />
        1、产品购销的任何一方由于不可抗拒力的原因不能履行合同时,应及时向对方通报不能履行或不能完全履行的理由,在取得有关证明以后,允许延期履行、部分履行或者不履行合同,并根据情况可部分或全部免予承担违约责任；<br />
        2、买方应按照本协议约定按期足额付款,买方逾期付款的,每逾期一日,买方应承担应付而未付金额万分之五的违约金。<br />
        3、本合同一式两份,买卖双方各执一份,经双方签字盖章后生效；<br />
        4、本合同生效时限,以双方中的一方最终签定日期为准,在买卖都履行完合同后,本合同自动失效；<br />
        5、双方合同履行过程中未尽事宜或发生争执的,双方应协商解决或者请求调解,如协商或调解未能解决向供货所在地人民法院提起诉讼；<br />
      </div>
      <table border="1" style="width: 100%" class="table2">
        <tr>
          <td>卖方:{{ mtgxsalesdata.value_2 }}</td>
          <td>买方: (盖章)</td>
        </tr>
        <tr>
          <td>代表:{{ mtgxsalesdata.value_17 }}</td>
          <td>买方:</td>
        </tr>
        <tr>
          <td>地址:{{ mtgxsalesdata.value_18 }}</td>
          <td>地址:</td>
        </tr>
        <tr>
          <td>电话:{{ mtgxsalesdata.value_19 }}</td>
          <td>电话:</td>
        </tr>
        <tr>
          <td>开户银行:{{ mtgxsalesdata.value_20 }}</td>
          <td>开户银行:</td>
        </tr>
        <tr>
          <td>帐号:1001 3000 0006 1165</td>
          <td>帐号:</td>
        </tr>
        <tr>
          <td>增值税纳税人:91510132728077609M</td>
          <td>增值税纳税人:</td>
        </tr>
      </table>
    </div>
  </div>
</template>
<script>
import html2pdf from "html2pdf.js";
import convertToChineseNum from "@/utils/convertToChineseNum";
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["mtgxsalesdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj1: {
        id: "pdfDommtgx", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.mtgxsalesdata.price.length; index++) {
      if (this.mtgxsalesdata.price[index].price14 && this.mtgxsalesdata.price[index].price14 != "/") {
        this.amountto += Number(this.mtgxsalesdata.price[index].price14);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.mtgxsalesdata.pcbFileName;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getReportPdf() {
      const element = document.getElementById("pdfDommtgx");
      const opt = {
        margin: 0,
        filename: "明天高新销售合同.pdf",
        image: { type: "jpeg", quality: 2 },
        html2canvas: { scale: 2 },
        jsPDF: {
          unit: "in",
          format: "a4",
          orientation: "landscape",
        },
        pagebreak: { mode: ["avoid-all", "css", "legacy"] },
      };
      html2pdf().set(opt).from(element).save();
    },
  },
};
</script>

<style lang="less" scoped>
.table2 td {
  padding: 5px;
}
.table1 td {
  text-align: center;
}

.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
/deep/.ant-divider-horizontal {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 8px 0;
  background: black;
}
.agreement {
  padding-bottom: 20px;
  position: relative;
  z-index: 99;
  div {
    padding-top: 10px;
  }
}
.tableclass {
  border: 1px solid black;
  margin-top: 10px;
  margin-bottom: 10px;
  td {
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding-left: 7px;
  }
  tbody {
    tr {
      height: 24px;
    }
  }
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
#pdfDommtgx {
  padding: 25px;
}
</style>
