<template>
  <a-spin :spinning="spinning">
    <div id="app" class="box" ref="SelectBox" @click="bodyClick">
      <span
        style="margin-top: 0; margin-bottom: 0.5em; color: #cf1b26; font-weight: 700; text-align: left; margin-left: 1%; float: left; font-size: 24px"
        >{{ OrderNo }}</span
      >
      <div style="display: flex; float: right">
        <a-tooltip :title="'下载已完结的问客EXCEL文件以及对应的附件信息'">
          <a-button type="primary" @click="deReplytodownload" style="margin-right: 10px">已回复下载<a-icon type="question-circle" /></a-button>
        </a-tooltip>
        <a-button
          type="primary"
          @click="Emailinformation"
          style="margin-right: 10px"
          v-if="checkPermission('MES.EngineeringModule.EngineeringEqPage.EngineeringEqPageEQSave')"
          >编辑邮箱</a-button
        >
        <a-button
          type="primary"
          @click="copyClick"
          style="margin-right: 10px"
          v-if="checkPermission('MES.EngineeringModule.EngineeringEqPage.EngineeringEqPageCopyLink') && $route.query.Jump != '预审'"
          >复制链接</a-button
        >
        <a-button type="primary" @click="allSenClick" style="margin-right: 10px" v-if="sendshow">提交问客</a-button>
        <a-upload
          name="file"
          ref="fileRef1"
          :before-upload="beforeUpload3"
          :customRequest="httpRequest6"
          :file-list="fileList6"
          :show-upload-list="false"
          :maxCount="1"
          @change="handleChange6"
        >
          <a-button type="primary" v-if="checkPermission('MES.EngineeringModule.EngineeringEqPage.EngineeringEqExcel')" style="margin-right: 10px">
            EQ回导
          </a-button>
        </a-upload>
        <a-tooltip :title="'未完结的问客EXCEL文件以及对应的附件信息'">
          <a-button type="primary" style="margin-right: 10px" @click="exportBtn('1')">EQ导出<a-icon type="question-circle" /></a-button>
        </a-tooltip>
        <a-button
          type="primary"
          @click="Uploadattachments"
          style="margin-right: 10px"
          v-if="checkPermission('MES.EngineeringModule.EngineeringEqPage.EngineeringEqPageUpEQAttachment')"
          >EQ回复</a-button
        >
        <a-upload
          name="file"
          ref="fileRef7"
          :customRequest="httpRequest7"
          :file-list="fileList7"
          :show-upload-list="false"
          :maxCount="1"
          @change="handleChange7"
          :before-upload="beforeUpload"
        >
        </a-upload>
        <a-button type="primary" @click="Downloadattachments" style="margin-right: 10px"> 下载附件 </a-button>
        <!-- v-if="checkPermission('MES.EngineeringModule.EngineeringEqPage.EngineeringEqPageDownEQAttachment')" 下载附件权限 -->
        <a-button
          type="primary"
          @click="sendmail"
          style="margin-right: 10px"
          :disabled="sendFlag || dissend"
          v-if="checkPermission('MES.EngineeringModule.EngineeringEqPage.EngineeringEqPageMail')"
        >
          发送邮件
        </a-button>
      </div>
      <table border style="border-color: #e1e1e2; color: #000000; width: 100%">
        <thead>
          <tr>
            <th style="width: 5%">问客次数</th>
            <th style="width: 5%">序号</th>
            <th style="width: 7%">操作时间</th>
            <th style="width: 7%">操作人员</th>
            <th :style="EQTemplate == 2 ? { width: '60%' } : { width: '30%' }">问题详情</th>
            <th style="width: 10%">附件</th>
            <th style="width: 10%">操作</th>
          </tr>
        </thead>
        <tr colspan="6" style="position: relative; height: 34px">
          <span style="line-height: 34px; position: absolute; right: 50%; color: #ff9900" @click="addClick">+添加问题</span>
        </tr>
      </table>
      <div style="height: 690px; width: 100%; overflow: auto; border-bottom: 1px solid #e1e1e2" class="scorllclass">
        <a-collapse :activeKey="copyorderData.length">
          <a-collapse-panel v-for="(val, inde) in copyorderData" :key="(inde + 1).toString()">
            <template #header>
              <div style="text-align: left">
                第 <span style="color: rgb(207, 27, 38); font-weight: 700; font-size: 16px">{{ inde + 1 }}</span> 次问客
              </div>
            </template>
            <table border style="border-color: #e1e1e2; color: #000000; border-top-color: #ffffff00">
              <tbody>
                <template v-for="(item, index) in val">
                  <tr :key="'1' + index">
                    <td style="width: 5%" v-if="item.num" :rowspan="item.num">{{ item.eqNumber }}</td>
                    <td style="width: 5%" :rowspan="item.reply ? 2 : 1">
                      Q<i>{{ qsort(item.id) }}</i>
                    </td>
                    <td style="width: 7%">{{ item.createTime }}</td>
                    <td style="width: 7%">{{ item.userName }}</td>
                    <td style="width: 30%" class="left" v-if="EQTemplate == 0 || EQTemplate == 2">
                      <div>
                        <p>问题描述：{{ item.contentS }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentA)">建议方案一：{{ item.contentA }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentB)">建议方案二：{{ item.contentB }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentC)">建议方案三：{{ item.contentC }}</p>
                      </div>
                    </td>
                    <td style="width: 30%" class="left" v-if="EQTemplate == 1 || EQTemplate == 2">
                      <div>
                        <p>E Q：{{ item.contentS_en }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentA_en)">Seg1：{{ item.contentA_en }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentB_en)">Seg2：{{ item.contentB_en }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentC_en)">Seg3：{{ item.contentC_en }}</p>
                      </div>
                    </td>
                    <th style="width: 10%">
                      <div v-if="item.image != []" v-viewer>
                        <span v-for="(ite, ind) in item.image" :key="ind" style="color: #0068ff; cursor: pointer; text-decoration: underline">
                          <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                          ><br />
                        </span>
                        <div v-viewer v-if="currentImageUrl" style="display: none">
                          <img :src="currentImageUrl" alt="附件图片" />
                        </div>
                        <!-- <img style="height:50px;width:50px;margin-left:10px;" v-for="(ite,ind) in item.image" :key="ind" :src="ite" /> -->
                      </div>
                      <div v-if="item.filePath != '' && item.filePath != ',' && item.filePath">
                        <span style="color: red; cursor: pointer" @click="down(item.filePath)"
                          >工程上传文件<br />（可点击下载{{ item.filePath.split(",").length }}个文件）</span
                        >
                      </div>
                    </th>
                    <td style="width: 10%" :rowspan="item.reply ? 2 : 1">
                      <div>
                        <p @click="editClick(item)" v-if="item.status == 1">+编辑问题</p>
                        <p @click="deleteClick(item)" v-if="item.status == 1">+删除问题</p>
                        <p @click="replyClick(item)" v-if="item.status == 3">+回复提问</p>
                        <!-- <p @click="sendClick(item)" v-if="item.status == 1 " >+发送问题</p> -->
                        <p @click="backClick(item)" v-if="item.status == 3">+删除问客</p>
                        <p @click="copy1Click(item)" v-if="item.status == 2">+复制提问</p>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="item.reply" :key="'2' + index">
                    <td style="width: 7%">{{ item.reply.solutionTime }}</td>
                    <td style="width: 7%">{{ item.reply.userName }}</td>
                    <td style="width: 30%" :colspan="EQTemplate == 0 ? 1 : 2" class="left" v-if="EQTemplate == 0 || EQTemplate == 2">
                      <div>
                        <p style="color: #ff9900">回复内容：{{ item.reply.content }}</p>
                      </div>
                    </td>
                    <td style="width: 30%" :colspan="1" class="left" v-if="EQTemplate == 1">
                      <div>
                        <p style="color: #ff9900">EQ：{{ item.reply.content }}</p>
                      </div>
                    </td>
                    <th style="width: 10%">
                      <div v-if="item.reply.image" v-viewer>
                        <!-- <img style="height:50px;width:50px;margin-left:10px;" v-for="(ite,ind) in item.reply.image.split(',')" :key="ind" :src="ite" /> -->
                        <span
                          v-for="(ite, ind) in item.reply.image.split(',')"
                          :key="ind"
                          style="color: #0068ff; cursor: pointer; text-decoration: underline"
                        >
                          <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                          ><br />
                        </span>
                      </div>
                      <div v-if="item.reply.filePath">
                        <span style="color: red" @click="down(item.reply.filePath)"
                          >文件（可点击下载{{ item.reply.filePath.split(",").length }}个文件）</span
                        >
                      </div>
                    </th>
                  </tr>
                </template>
              </tbody>
            </table>
          </a-collapse-panel>
        </a-collapse>
        <table border style="border-color: #e1e1e2; color: #000000; border-top-color: #ffffff00">
          <tbody>
            <tr v-if="addTr">
              <td style="width: 5%">
                <p>{{ currentEqNum }}</p>
              </td>
              <td style="width: 5%">
                <p>
                  Q<i>{{ orderData.length + 1 }}</i>
                </p>
              </td>
              <td style="width: 7%">
                <p>{{ addTime }}</p>
              </td>
              <td style="width: 7%">
                <p>{{ user }}</p>
              </td>
              <td class="left" style="width: 30%" v-if="EQTemplate == 0 || EQTemplate == 2">
                <template>
                  <a-form layout="inline" :model="form">
                    <a-row>
                      <a-col :span="12">
                        <a-form-item label="问客类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-select v-model="askType" showSearch allowClear optionFilterProp="label" :getPopupContainer="() => this.$refs.SelectBox">
                            <a-select-option v-for="(item, index) in selectData3" :key="index" :value="item.display_" :label="item.caption_">{{
                              item.caption_
                            }}</a-select-option>
                          </a-select>
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item style="width: 100%; margin: 0">
                          <span style="color: red; margin-left: 10px; font-size: 13px">*如为问题确认，不需要上传工程文件；反之则不需上传图片</span>
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="12">
                        <a-form-item label="问题类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-select
                            v-model="form.keyType"
                            showSearch
                            allowClear
                            optionFilterProp="lable"
                            :getPopupContainer="() => this.$refs.SelectBox"
                          >
                            <a-select-option v-for="(item, index) in selectData1" :key="index" :value="item.text" :lable="item.text"
                              >{{ item.text }}
                            </a-select-option>
                          </a-select>
                        </a-form-item>
                      </a-col>
                      <a-col :span="8">
                        <a-form-item style="margin-left: 50px">
                          <a-button @click="click2" style="font-weight: 500">关键字</a-button>
                        </a-form-item>
                      </a-col>
                    </a-row>

                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="问题描述" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <!-- <a-input v-model="form.problemDescription"  @blur="changeDescription()" allowClear/> -->
                          <a-textarea
                            v-model="form.problemDescription"
                            :auto-size="{ minRows: 2, maxRows: 5 }"
                            @blur="changeDescription()"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="建议方案一" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-textarea
                            :auto-size="{ minRows: 1, maxRows: 3 }"
                            type="text"
                            v-model="form.proposalA"
                            @blur="changeA()"
                            placeholder="请撰写方案一"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="建议方案二" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-textarea
                            :auto-size="{ minRows: 1, maxRows: 3 }"
                            type="text"
                            v-model="form.proposalB"
                            @blur="changeB()"
                            placeholder="请撰写方案二"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item
                          label="建议方案三"
                          prop="proposalC"
                          :label-col="{ span: 4 }"
                          :wrapper-col="{ span: 16 }"
                          style="width: 100%; margin: 0"
                        >
                          <a-textarea
                            :auto-size="{ minRows: 1, maxRows: 3 }"
                            type="text"
                            v-model="form.proposalC"
                            @blur="changeC()"
                            placeholder="请撰写方案三"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <span style="color: #ff9900; margin-left: 80px; font-size: 13px">如多方案请按相应输入框输入建议方案</span>
                    <a-row>
                      <a-col :span="24" v-if="addTr">
                        <a-form-item label="解决方案" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
                          <p style="margin: 0">{{ solution }}</p>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </template>
              </td>
              <td class="left" style="width: 30%" v-if="EQTemplate == 1 || EQTemplate == 2">
                <template>
                  <a-form layout="inline" :model="form">
                    <a-row>
                      <a-col :span="12">
                        <a-form-item label="问客类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-select v-model="askType" showSearch allowClear optionFilterProp="label" :getPopupContainer="() => this.$refs.SelectBox">
                            <a-select-option v-for="(item, index) in selectData3" :key="index" :value="item.display_" :label="item.caption_">{{
                              item.caption_
                            }}</a-select-option>
                          </a-select>
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item style="width: 100%; margin: 0">
                          <span style="color: red; margin-left: 10px; font-size: 13px">*如为问题确认，不需要上传工程文件；反之则不需上传图片</span>
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="12">
                        <a-form-item label="问题类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-select
                            v-model="form.enKeyType"
                            showSearch
                            allowClear
                            optionFilterProp="lable"
                            :getPopupContainer="() => this.$refs.SelectBox"
                          >
                            <a-select-option v-for="(item, index) in selectData1" :key="index" :value="item.text" :lable="item.text"
                              >{{ item.text }}
                            </a-select-option>
                          </a-select>
                        </a-form-item>
                      </a-col>
                      <a-col :span="8">
                        <a-form-item style="margin-left: 50px">
                          <a-button @click="click2" style="font-weight: 500">关键字</a-button>
                        </a-form-item>
                      </a-col>
                    </a-row>

                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="问题描述" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <!-- <a-input v-model="form.problemDescription"  @blur="changeDescription()" allowClear/> -->
                          <a-textarea
                            v-model="form.problemDescription_en"
                            :auto-size="{ minRows: 2, maxRows: 5 }"
                            @blur="changeDescription()"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="建议方案一" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-textarea
                            :auto-size="{ minRows: 1, maxRows: 3 }"
                            type="text"
                            v-model="form.proposalA_en"
                            @blur="changeA()"
                            placeholder="请撰写方案一"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="建议方案二" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                          <a-textarea
                            :auto-size="{ minRows: 1, maxRows: 3 }"
                            type="text"
                            v-model="form.proposalB_en"
                            @blur="changeB()"
                            placeholder="请撰写方案二"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24">
                        <a-form-item
                          label="建议方案三"
                          prop="proposalC"
                          :label-col="{ span: 4 }"
                          :wrapper-col="{ span: 16 }"
                          style="width: 100%; margin: 0"
                        >
                          <a-textarea
                            :auto-size="{ minRows: 1, maxRows: 3 }"
                            type="text"
                            v-model="form.proposalC_en"
                            @blur="changeC()"
                            placeholder="请撰写方案三"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                    <span style="color: #ff9900; margin-left: 80px; font-size: 13px">如多方案请按相应输入框输入建议方案</span>
                    <a-row>
                      <a-col :span="24" v-if="addTr">
                        <a-form-item label="解决方案" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
                          <p style="margin: 0">{{ solution }}</p>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </template>
              </td>
              <th style="width: 10%">
                <a-row style="padding: 10px">
                  <template v-if="askType == 0">
                    <a-upload
                      name="file"
                      ref="fileRef"
                      :before-upload="beforeUpload1"
                      :customRequest="httpRequest1"
                      :file-list="fileListData"
                      @change="handleChange1"
                      @preview="handlePreview"
                    >
                      <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                      <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(1)" title="点击 ctrl+V粘贴上传">
                        粘贴图片
                      </a-button>
                    </a-upload>
                    <!-- <div >
                      <div
                        id="Msg"
                        contentEditable="true" 
                      >
                        <span contentEditable="false" style="display:block;position:relative;top:35px">点击，粘贴图片上传</span
                        >
                      </div>
                      <div v-show="startloading" style="margin-top:10px">
                        <a-spin tip="图片上传中...">
                          <div style="border: 1px solid #91d5ff;background-color: #e6f7ff;padding: 30px;"></div>
                        </a-spin>
                      </div>
                    </div> -->
                    <!-- <span @click="showCopy">点击，粘贴图片上传</span> -->
                    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview" :width="900">
                      <img style="width: 100%; height: 100%" :src="previewImage" />
                    </a-modal>
                  </template>
                  <template v-else>
                    <a-upload
                      name="file"
                      ref="fileRef"
                      :before-upload="beforeUpload1"
                      :customRequest="httpRequest2"
                      :file-list="fileList"
                      @change="handleChange2"
                    >
                      <a-button> 上传文件 </a-button>
                    </a-upload>
                  </template>
                </a-row>
              </th>
              <td style="width: 10%">
                <p><a-button type="primary" @click="uploadclick()" :loading="spinning1"> 提交</a-button></p>
                <p><a-button type="primary" @click="cancelClick"> 取消</a-button></p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <a-modal
        title="EQ回复"
        :visible="dataVisibleileFile"
        @cancel="reportHandleCancel1"
        @ok="handleOkFile"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        centered
        :maskClosable="false"
        :width="400"
      >
        <a-form-model :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
          <a-form-model-item label="交期">
            <a-date-picker format="YYYY-MM-DD" placeholder="请选择交期" @change="TimeChange" v-model="delDate" style="width: 235px"> </a-date-picker>
          </a-form-model-item>
          <a-upload
            :multiple="false"
            :file-list="fileList7"
            :customRequest="httpRequest7"
            :show-upload-list="true"
            @change="handleChange7"
            :before-upload="beforeUpload"
          >
            <a-button> <a-icon type="upload" />上传文件 </a-button>
          </a-upload>
        </a-form-model>
      </a-modal>
      <a-modal
        title="关键字索引"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="1100"
      >
        <key-word :selectData1="selectData1" ref="keyWord" :eqQuestion="eqQuestion" :StepName1="StepName1"></key-word>
      </a-modal>
      <!--    编辑问题弹窗-->
      <a-modal
        title="请输入问题描述"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="EQTemplate == 2 ? 1000 : 600"
      >
        <div @click="bodyClick">
          <div style="display: flex">
            <a-form
              :model="editForm"
              style="width: 48%"
              v-if="EQTemplate == 0 || EQTemplate == 2"
              :style="EQTemplate == 2 ? { width: '48%' } : { width: '100%' }"
            >
              <a-form-item>
                <a-textarea
                  :auto-size="{ minRows: 1, maxRows: 3 }"
                  v-model="editForm.problemDescription"
                  placeholder="问题描述"
                  allowClear
                ></a-textarea>
              </a-form-item>
              <a-form-item>
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalA" placeholder="输入建议方案一" allowClear></a-textarea>
              </a-form-item>
              <a-form-item>
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalB" placeholder="输入建议方案二" allowClear></a-textarea>
              </a-form-item>
              <a-form-item>
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalC" placeholder="输入建议方案三" allowClear></a-textarea>
              </a-form-item>
            </a-form>
            <a-form
              :model="editForm"
              style="width: 48%; margin-left: 2%"
              v-if="EQTemplate == 1 || EQTemplate == 2"
              :style="EQTemplate == 2 ? { width: '48%' } : { width: '100%' }"
            >
              <a-form-item>
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.problemDescription_en" placeholder="EQ" allowClear></a-textarea>
              </a-form-item>
              <a-form-item>
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalA_en" placeholder="Seg1" allowClear></a-textarea>
              </a-form-item>
              <a-form-item>
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalB_en" placeholder="Seg2" allowClear></a-textarea>
              </a-form-item>
              <a-form-item>
                <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalC_en" placeholder="Seg3" allowClear></a-textarea>
              </a-form-item>
            </a-form>
          </div>
          <a-form-item style="padding: 10px">
            <template v-if="editForm.askType == '0'">
              <a-upload
                name="file"
                ref="fileRef"
                :before-upload="beforeUploadnew"
                :customRequest="httpRequest3"
                :file-list="fileListData3"
                @change="handleChange3"
                list-type="picture-card"
                @preview="handlePreview"
              >
                <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(3)" title="ctrl+V 粘贴上传">
                  粘贴图片
                </a-button>
              </a-upload>
              <a-modal :visible="previewVisible" :footer="null" centered @cancel="handleCancelPreview">
                <img style="width: 100%; height: 100%" :src="previewImage" />
              </a-modal>
            </template>
            <template v-else>
              <a-upload
                name="file"
                ref="fileRef"
                :before-upload="beforeUpload2"
                :customRequest="httpRequest4"
                :file-list="fileList4"
                @change="handleChange4"
              >
                <a-button> 上传文件 </a-button>
              </a-upload>
            </template>
          </a-form-item>
        </div>
      </a-modal>
      <!--    回复问题弹窗-->
      <a-modal
        title="请输入回复内容"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="1000"
      >
        <div @click="bodyClick">
          <a-form>
            <!-- <template>
          <a-textarea v-model="replyForm.Quiz" :auto-size="{ minRows: 2, maxRows:5 }" ></a-textarea>  
        </template> -->
            <div style="display: flex">
              <div v-if="EQTemplate == 0" style="width: 48%">
                <div>问题描述：{{ replyForm.contentS }}</div>
                <div v-if="replyForm.image1 != '' || replyForm.image1 != []" v-viewer>
                  <img style="height: 50px; width: 50px; margin: 10px" v-for="(ite, index) in replyForm.image1" :key="index" :src="ite" />
                </div>
                <a-radio-group v-model="replyForm.value" @change="radioChange" style="display: flex; flex-direction: column">
                  <a-radio style="height: 30px" :value="1" v-if="!isEmptyOrWhitespace(replyForm.contentA)">方案一： {{ replyForm.contentA }}</a-radio>
                  <a-radio style="height: 30px" :value="2" v-if="!isEmptyOrWhitespace(replyForm.contentB)">方案二：{{ replyForm.contentB }}</a-radio>
                  <a-radio style="height: 30px" :value="4" v-if="!isEmptyOrWhitespace(replyForm.contentC)">方案三：{{ replyForm.contentC }}</a-radio>
                  <a-radio :value="3"
                    ><span style="display: inline-block; line-height: 60px">其他方案:</span>
                    <a-textarea
                      v-model="replyForm.proposalC"
                      style="width: 80%"
                      :auto-size="{ minRows: 2, maxRows: 5 }"
                      :disabled="replyForm.value == '3' ? false : true"
                    ></a-textarea>
                  </a-radio>
                </a-radio-group>
              </div>
              <div v-if="EQTemplate == 1" style="width: 48%; margin-left: 2%">
                <div>问题描述：{{ replyForm.contentS_en }}</div>
                <div v-if="replyForm.image1 != '' || replyForm.image1 != []" v-viewer>
                  <img style="height: 50px; width: 50px; margin: 10px" v-for="(ite, index) in replyForm.image1" :key="index" :src="ite" />
                </div>
                <a-radio-group v-model="replyForm.value" @change="radioChange" style="display: flex; flex-direction: column">
                  <a-radio style="height: 30px" :value="1" v-if="!isEmptyOrWhitespace(replyForm.contentA_en)"
                    >方案一： {{ replyForm.contentA_en }}</a-radio
                  >
                  <a-radio style="height: 30px" :value="2" v-if="!isEmptyOrWhitespace(replyForm.contentB_en)"
                    >方案二：{{ replyForm.contentB_en }}</a-radio
                  >
                  <a-radio style="height: 30px" :value="4" v-if="!isEmptyOrWhitespace(replyForm.contentC_en)"
                    >方案三：{{ replyForm.contentC_en }}</a-radio
                  >
                  <a-radio :value="3"
                    ><span style="display: inline-block; line-height: 60px">其他方案:</span>
                    <a-textarea
                      v-model="replyForm.proposalC"
                      style="width: 80%"
                      :auto-size="{ minRows: 2, maxRows: 5 }"
                      :disabled="replyForm.value == '3' ? false : true"
                    ></a-textarea>
                  </a-radio>
                </a-radio-group>
              </div>
              <div v-if="EQTemplate == 2" style="width: 100%">
                <div>问题描述：{{ replyForm.contentS }}({{ replyForm.contentS_en }})</div>
                <div v-if="replyForm.image1 != '' || replyForm.image1 != []" v-viewer>
                  <img style="height: 50px; width: 50px; margin: 10px" v-for="(ite, index) in replyForm.image1" :key="index" :src="ite" />
                </div>
                <a-radio-group v-model="replyForm.value" @change="radioChange" style="display: flex; flex-direction: column">
                  <a-radio style="height: 30px" :value="1" v-if="!isEmptyOrWhitespace(replyForm.contentA)"
                    >方案一： {{ replyForm.contentA }}({{ replyForm.contentA_en }})</a-radio
                  >
                  <a-radio style="height: 30px" :value="2" v-if="!isEmptyOrWhitespace(replyForm.contentB)"
                    >方案二：{{ replyForm.contentB }}({{ replyForm.contentB_en }})</a-radio
                  >
                  <a-radio style="height: 30px" :value="4" v-if="!isEmptyOrWhitespace(replyForm.contentC)"
                    >方案三：{{ replyForm.contentC }}({{ replyForm.contentC_en }})</a-radio
                  >
                  <a-radio :value="3"
                    ><span style="display: inline-block; line-height: 60px">其他方案:</span>
                    <a-textarea
                      v-model="replyForm.proposalC"
                      style="width: 80%"
                      :auto-size="{ minRows: 2, maxRows: 5 }"
                      :disabled="replyForm.value == '3' ? false : true"
                    ></a-textarea>
                  </a-radio>
                </a-radio-group>
              </div>
            </div>

            <div>
              <a-upload
                name="file"
                ref="fileRef"
                :before-upload="beforeUpload1"
                :customRequest="httpRequest5"
                :file-list="fileListData2"
                @change="handleChange5"
                list-type="picture-card"
                @preview="handlePreview"
              >
                <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(5)" title="ctrl+V 粘贴上传">
                  粘贴图片
                </a-button>
              </a-upload>
              <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview" :width="900">
                <img style="width: 100%; height: 100%" :src="previewImage" />
              </a-modal>
              <a-upload
                name="file"
                ref="fileRef"
                :before-upload="beforeUpload11"
                :customRequest="httpRequest22"
                :file-list="fileList2"
                @change="handleChange22"
              >
                <a-button v-if="fileList2.length < 1"> 上传文件 </a-button>
              </a-upload>
            </div>
          </a-form>
        </div>
      </a-modal>
      <!-- 邮件信息弹窗 -->
      <a-modal
        title="编辑邮箱"
        :visible="emaildataVisible"
        @cancel="reportHandleCancel1"
        @ok="emailhandleOk"
        destroyOnClose
        :maskClosable="false"
        :width="600"
        centered
      >
        <template #footer>
          <a-button key="cancel" type="primary" @click="reportHandleCancel1">取消</a-button>
          <a-button key="save" :type="sendFlag ? 'primary' : ''" :disabled="!sendFlag" @click="emailhandleOk">保存</a-button>
          <a-button key="submit" type="primary" :loading="loading" @click="emailhandleSend" :disabled="sendFlag">发送(E)</a-button>
        </template>
        <a-form :model="emaildata">
          <a-row>
            <a-col :span="24">
              <a-form-item label="收件人" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.eqEmail" autoFocus> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="emaildata.eqContactPerson">
            <a-col :span="24">
              <a-form-item label="联系人" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.eqContactPerson" autoFocus disabled> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="emaildata.eqPhoneNumber">
            <a-col :span="24">
              <a-form-item label="联系人方式" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.eqPhoneNumber" autoFocus disabled> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="抄送" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.cc" autoFocus :disabled="$route.query.joinFactoryId == 38"> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="主题" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-input allowClear v-model="emaildata.subject" autoFocus> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="正文" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
                <a-textarea allowClear :auto-size="{ minRows: 10, maxRows: 15 }" v-model="emaildata.body" autoFocus> </a-textarea>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <!-- 粘贴图片上传 -->
      <a-modal title="复制上传图片" :visible="show" @cancel="Msgcancel" :footer="null" centered :destroyOnClose="true">
        <div style="width: 100%">
          <div
            id="Msg"
            contentEditable="true"
            style="overflow: hidden; width: 200px; height: 200px; border: 1px solid #999; text-align: center; margin: 0 auto; border-radius: 5px"
          >
            <span contentEditable="false" style="display: block; position: relative; top: 90px" :autoFocus="true">点击，粘贴图片上传</span>
          </div>
          <div v-show="startloading" style="margin-top: 10px">
            <a-spin tip="图片上传中...">
              <div style="border: 1px solid #91d5ff; background-color: #e6f7ff; padding: 30px"></div>
            </a-spin>
          </div>
        </div>
      </a-modal>
      <!-- 按钮检查 -->
      <a-modal :title="meslist" :visible="dataVisible4" @cancel="reportHandleCancel" destroyOnClose centered :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back1" type="primary" v-if="check" @click="continueclick">继续</a-button>
          <a-button key="back" @click="reportHandleCancel">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.info == '1'" style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import { exporteQReportcust, exporteQReportcustv2 } from "@/services/usermanagement/index.js";
import { ppebuttonCheck, eqTemplate, proorderinformation, teQMail } from "@/services/projectMake/index.js";
import Clipboard from "clipboard";
import { checkPermission } from "@/utils/abp";
import { ImportOrder } from "@/services/projectMake";
import axios from "axios";
import $ from "jquery";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    // scopedSlots: { customRender: 'num' },
    width: "15%",
  },
  {
    title: "操作时间",
    dataIndex: "solutionTime",
    align: "left",
    ellipsis: true,
    width: "15%",
    className: "userStyle",
  },
  {
    title: "操作人员",
    dataIndex: "userName",
    align: "left",
    ellipsis: true,
    width: "15%",
  },
  {
    title: "问题详情",
    // customRender: (text,record,index) => `${record.isReOrder ? '是' : ''}`,
    scopedSlots: { customRender: "content" },
    align: "left",
    ellipsis: true,
    width: "40%",
  },

  {
    title: "操作",
    scopedSlots: { customRender: "action" },
    align: "center",
    // width: 120,   // 2290
  },
];
import {
  getEqList,
  getClassList,
  emSEQMaineqExcel,
  eqExcelmkt,
  BigClassList,
  upLoadFlyingFile,
  upeQAttachment,
  downeQAttachment,
  submit,
  editingProblems,
  replyProblems,
  sendProblems,
  backProblems,
  setdeleetproblems,
  toSendProblems,
  user,
  timestampVerifyCode,
  exportEQReport,
  eqQuestion,
  exportEQReportv2,
} from "@/services/projectMake";
import { downeQAttachmentbycust } from "@/services/usermanagement/index.js";
import { emSEQMaineqRepair, eqSendmail, emSEQMaineqSave, eqMktSendMail, eqSendmailmkt } from "@/services/projectApi";
import moment from "moment";
import keyWord from "@/pages/gongcheng/projectPage/eqModule/keyWord";
export default {
  name: "eqDetails",
  components: { keyWord },
  data() {
    return {
      activeKey: ["1"],
      pcbFileName: "",
      sendshow: false,
      currentImageUrl: "",
      imglist: [],
      spinning: false,
      dissend: false,
      copyform: {},
      checkData: [],
      check: false,
      spinning1: false,
      dataVisibleileFile: false,
      meslist: "",
      delDate: null,
      EQFileName: "",
      dataVisible4: false,
      file1data: "",
      file11data: "",
      eqdataVisible: false,
      columns1,
      orderData: [],
      copyorderData: [],
      uppath: "",
      addTr: false,
      replyTr: false,
      replyData: [],
      selectData1: [],
      selectData2: [],
      selectData3: [],
      addTime: moment(new Date()).format("YYYY-MM-DD "),
      form: {
        orderNo: "", //订单号
        keyType: "", //问题类型
        problemDescription: "", // 问题描述
        proposalA: "", // 建议A
        proposalB: "", //建议B
        proposalC: "", //建议C
        enKeyType: "",
        problemDescription_en: "",
        proposalA_en: "",
        proposalB_en: "",
        proposalC_en: "",
      },
      StepName1: "",
      askType: 0, // 问客类型（0：问题确认，1：文件确认）
      path: "", // 上传文件返回地址
      path3: "", // 编辑上传图片图片返回地址
      dataVisible1: false,
      solution: "", //解决方案
      fileList: [],
      fileList2: [],
      fileList6: [],
      fileList7: [],
      fileListData: [],
      fileListData2: [],
      fileListData3: [],
      fileList4: [],
      isFileType: true,
      previewVisible: false,
      arrData: [],
      arrData1: [],
      arrData2: [],
      dataVisible2: false,
      dataVisible3: false,
      editForm: {
        id: "",
        problemDescription: "", // 问题描述
        proposalA: "", // 建议A
        proposalB: "", //建议B
        proposalC: "", //建议C
        path: "", // 图片地址
        filePath: "", // 文件地址
        askType: 0,
        problemDescription_en: "", // 问题描述
        proposalA_en: "", // 建议A
        proposalB_en: "", //建议B
        proposalC_en: "", //建议C
      }, // 编辑问题弹窗数据
      replyForm: {
        Quiz: "",
        id: "",
        value: undefined,
        proposalC: "",
        proposalC_en: "",
        image1: [],
        image: "",
      },
      previewImage: "",
      eqData: [],
      user: "",
      isMovedown: false,
      startPosition: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      OrderNo: "",
      eqQuestion: [],
      Jump: "",
      emaildataVisible: false,
      emaildata: {},
      emaildataCopyOriginal: {},
      id: "",
      loading: false,
      isCtrlPressed: false,
      show: false,
      startloading: false,
      showCopyType: "",
      file: null,
      sendFlag: false,
      currentEqNum: 1,
      allowAddTr: true,
      EQTemplate: 0,
    };
  },
  watch: {
    askType(newNal, oldval) {
      if (newNal == 1) {
        this.form.problemDescription = "文件确认";
        this.form.proposalA = "文件无误";
        this.form.proposalB = "文件有误";
        this.form.proposalC = "";
        this.form.problemDescription_en = "file confirm";
        this.form.proposalA_en = "file No error";
        this.form.proposalB_en = "file Wrong";
        this.form.proposalC_en = "";
      } else {
        this.form.problemDescription = "";
        this.form.proposalA = "";
        this.form.proposalB = "";
        this.form.proposalC = "";
        this.form.problemDescription_en = "";
        this.form.proposalA_en = "";
        this.form.proposalB_en = "";
        this.form.proposalC_en = "";
      }
    },
    "emaildata.eqEmail"(newVal, oldVal) {
      if (newVal != oldVal && newVal != this.emaildataCopyOriginal.eqEmail) {
        this.sendFlag = true;
      } else {
        this.sendFlag = false;
      }
    },
    "emaildata.cc"(newVal, oldVal) {
      if (newVal != oldVal && newVal != this.emaildataCopyOriginal.cc) {
        this.sendFlag = true;
      } else {
        this.sendFlag = false;
      }
    },
    "emaildata.subject"(newVal, oldVal) {
      if (newVal != oldVal && newVal != this.emaildataCopyOriginal.subject) {
        this.sendFlag = true;
      } else {
        this.sendFlag = false;
      }
    },
    "emaildata.body"(newVal, oldVal) {
      if (newVal != oldVal && newVal != this.emaildataCopyOriginal.body) {
        this.sendFlag = true;
      } else {
        this.sendFlag = false;
      }
    },
  },

  methods: {
    checkPermission,
    sortby(img) {
      return this.imglist.findIndex(item => item == img) + 1;
    },
    qsort(id) {
      return this.orderData.findIndex(item => item.id == id) + 1;
    },
    getpcbfile() {
      let params = {
        orderNo: this.$route.query.OrderNo,
        businessOrderNo: this.$route.query.businessOrderNo,
      };
      eqTemplate(this.$route.query.joinFactoryId, params).then(res => {
        if (res.code) {
          this.EQTemplate = res.data;
        }
      });
    },
    // 获取订单问客记录
    getOrderList() {
      this.spinning = true;
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      getEqList(JoinFactoryId, OrderNo, BusinessOrderNo)
        .then(res => {
          if (res.code) {
            let arrData = [];
            this.imglist = [];
            this.orderData = res.data;
            let pathstring = "";
            res.data.forEach(item => {
              if (item.image) {
                pathstring += item.image + ",";
              }
              if (item.reply && item.reply.image) {
                pathstring += item.reply.image + ",";
              }
              if (item.parentId <= 0) {
                let content = item.content;
                var arr = content.split("|||");
                var arr_ = arr.length > 1 ? arr[1].split(";") : "";
                item.contentS = arr[0];
                item.contentA = arr_[0];
                item.contentB = arr_[1];
                item.contentC = arr_[2];
                let enContent = item.enContent;
                if (enContent) {
                  var arr_en = enContent.split("|||");
                  var arr_en_ = arr_en.length > 1 ? arr_en[1].split(";") : "";
                  item.contentS_en = arr_en[0];
                  item.contentA_en = arr_en_[0];
                  item.contentB_en = arr_en_[1];
                  item.contentC_en = arr_en_[2];
                }
                if (item.image) {
                  var a = item.image.split(",");
                  item.image = a;
                }
              }
            });
            this.imglist = pathstring.split(",");
            this.imglist.pop();
            //判断orderData所有问客状态：2024/7/9
            // 有status=1时，this.currentEqNum等于 status=1的数据的  eqNumber
            // 无status=1 有status=2时，this.currentEqStatus等于 status=2的数据中eqNumber的最大值+1
            // 无status=1 有status=3时，不允许添加新问题
            this.allowAddTr = true;
            if (this.orderData.filter(item => item.status == 1).length > 0) {
              this.currentEqNum = this.orderData.filter(item => item.status == 1)[0].eqNumber;
            } else if (this.orderData.filter(item => item.status == 2).length > 0) {
              const maxNUm = Math.max(...this.orderData.filter(item => item.status == 2).map(item => item.eqNumber));
              this.currentEqNum = maxNUm + 1;
              if (this.orderData.filter(item => item.status == 3).length > 0) {
                this.allowAddTr = false;
              }
            }
            this.orderData = this.addNumProperty(this.orderData);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    addNumProperty(arr) {
      if (arr.length == 0) {
        this.copyorderData = [];
      }
      const grouped = arr.reduce((acc, item) => {
        let key = item.eqNumber;
        if (!key) {
          key = 0;
        }
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        this.copyorderData = Object.values(acc);
        console.log(this.copyorderData, "copydata");
        return acc;
      }, {});
      let newArr = [];
      let fornum = 0;
      for (const [_, items] of Object.entries(grouped)) {
        fornum += items.length;
        let replyNum = 0;
        for (let i = 0; i < items.length; i++) {
          if (i == 0) {
            items[i].num = items.length;
          } else {
            items[i].num = 0;
          }
          if (items[i].reply) {
            replyNum++;
          }
          newArr.push(items[i]);
        }
        newArr[newArr.length - items.length].num = newArr[newArr.length - items.length].num + replyNum;
      }
      return newArr;
    },
    // 添加问题
    async addClick() {
      if (!this.allowAddTr) {
        this.$message.error("该订单有未回复的问客,暂不可以新增加问题！");
        return;
      }
      this.addTr = true;
      // this.$nextTick(() => { // 监听粘贴事件
      //   document.getElementById('Msg').addEventListener('paste', this.getClipboardFiles);
      // });

      user().then(res => {
        if (res.code) {
          this.user = res.data.realName_;
        }
      });
    },
    // 获取问客下拉选择
    getSelect1() {
      let params = [1118];
      getClassList(params).then(res => {
        if (res.code) {
          this.selectData3 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取问题下拉选择
    getSelect2() {
      let params = [1119];
      getClassList(params).then(res => {
        if (res.code) {
          this.selectData2 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getSelect() {
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      BigClassList(JoinFactoryId, OrderNo, BusinessOrderNo).then(res => {
        if (res.code) {
          this.selectData1 = res.data.sort((a, b) => a.iType - b.iType);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel() {
      this.dataVisible4 = false;
      this.eqdataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.replyForm.Quiz = "";
      this.replyForm.image = "";
      this.replyForm.path = "";
      this.replyForm.value = undefined;
      this.replyForm.proposalC = "";
      this.fileListData3 = [];
      this.arrData1 = [];
      this.arrData2 = [];
      this.fileList4 = [];
    },
    changeDescription() {
      this.$forceUpdate();
    },
    changeA() {
      this.$forceUpdate();
    },
    changeB() {
      this.$forceUpdate();
    },
    changeC() {
      this.$forceUpdate();
    },
    // 关键字
    click2() {
      this.StepName1 = this.form.keyType;
      this.dataVisible1 = true;
    },
    //
    handleOk1() {
      this.dataVisible1 = false;
      let formData = this.$refs.keyWord.selectData;
      this.copyform = JSON.parse(JSON.stringify(this.$refs.keyWord.selectData));
      this.form.keyType = formData.stepName;
      this.form.problemDescription = formData.problemDescription;
      this.form.proposalA = formData.proposalA;
      this.form.proposalB = formData.proposalB;
      this.form.proposalC = formData.proposalC;
      this.form.enKeyType = formData.stepName_en;
      this.form.problemDescription_en = formData.problemDescription_en;
      this.form.proposalA_en = formData.proposalA_en;
      this.form.proposalB_en = formData.proposalB_en;
      this.form.proposalC_en = formData.proposalC_en;
      this.solution = formData.solution;
    },
    click1() {
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData);
      }
      if (this.isFileType) {
        const removedFiles = this.fileListData.filter(file => !fileList.includes(file));
        removedFiles.forEach(file => {
          const removedElement = file.response;
          if (removedElement && this.arrData.includes(removedElement)) {
            this.arrData = this.arrData.filter(element => !element.includes(removedElement));
            this.path = this.arrData.toString(",");
          }
        });
        this.fileListData = fileList;
        for (let index = 0; index < this.fileListData.length; index++) {
          const element = this.fileListData[index].response;
          if (element && !this.arrData.includes(element)) {
            this.arrData.push(element);
            this.path = this.arrData.toString(",");
          }
        }

        if (this.fileListData.length === 0) {
          this.path = "";
        }
        //console.log('this.fileListData:',this.fileListData)
      }
    },
    handleChange2({ fileList }, data) {
      if (this.isFileType) {
        this.fileList = fileList;
        var a = [];
        if (this.fileList.length == 0) {
          this.path = "";
        } else {
          for (let index = 0; index < fileList.length; index++) {
            a.push(fileList[index].response);
          }
          this.path = a.join(",");
        }
      }
    },
    handleChange22({ fileList }, data) {
      if (this.isFileType) {
        this.fileList2 = fileList;
        if (this.fileList2.length == 0) {
          this.replyForm.path = "";
        }
      }
    },
    handleChange3({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData3);
      }
      let namesArray = fileList.map(item => item.response);
      this.arrData1 = namesArray;
      this.fileListData3 = fileList;
      if (this.fileListData3.length == 0) {
        this.editForm.path = "";
      }
    },
    handleChange4({ fileList }, data) {
      this.fileList4 = fileList;
      var a = [];
      if (this.fileList4.length == 0) {
        this.editForm.filePath = "";
      } else {
        for (let index = 0; index < fileList.length; index++) {
          a.push(fileList[index].response);
        }
        this.editForm.filePath = a.join(",");
      }
    },
    handleChange6({ fileList }) {
      if (this.isFileType) {
        this.fileList6 = fileList;
      }
    },
    handleChange7({ fileList }) {
      if (this.isFileType) {
        this.fileList7 = [fileList[fileList.length - 1]];
        for (let index = 0; index < this.fileList7.length; index++) {
          this.EQFileName = this.fileList7[index].name;
        }
      }
    },
    handleChange5({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData2);
      }
      let namesArray = fileList.map(item => item.response);
      this.arrData2 = namesArray;
      this.replyForm.image = this.arrData2.toString(",");
      if (this.isFileType) {
        this.fileListData2 = fileList;
        if (this.fileListData2.length == 0) {
          this.replyForm.image = "";
        }
      }
    },
    beforeUpload1(file) {
      if (this.askType == 1) {
        // const hasChinese = /[\u4e00-\u9fa5]/.test(file.name);
        // this.isFileType =  !hasChinese
        // if (!this.isFileType) {
        //   this.$message.error('文件确认上传文件不得含有中文字符');
        // }
        // return this.isFileType
      } else {
        this.isFileType = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!this.isFileType) {
          this.$message.error("问题确认只支持.jpg/.png图片格式文件");
        }
        return this.isFileType;
      }
    },
    beforeUpload11(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
      if (!this.isFileType) {
        this.$message.error("文件确认只支持.rar或.zip格式文件");
      }
      return this.isFileType;
    },
    beforeUpload3(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".xlsx") != -1 || file.name.toLowerCase().indexOf(".xls") != -1;
      if (!this.isFileType) {
        this.$message.error("该功能只支持.xlsx或.xls格式文件");
      }
      return this.isFileType;
    },
    beforeUpload2(file) {
      //  const hasChinese = /[\u4e00-\u9fa5]/.test(file.name);
      //   this.isFileType = !hasChinese
      //  if (!this.isFileType) {
      //     this.$message.error('文件确认上传文件不得含有中文字符');
      //   }
      //   return this.isFileType
    },
    beforeUpload(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const filesize = Number(file.size / 1024 / 1024) < 500;
        if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    //分段EQ回复
    async httpRequest7(data, type) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      this.spinning = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              this.uppath = res.message;
              if (!this.dataVisibleileFile) {
                this.upeQAttachment();
              } else {
                this.spinning = false;
              }
            }
          } else {
            this.$message.error(res.message);
            i = shardCount;
            this.spinning = false;
          }
        });
      }
    },
    //EQ回复成功
    upeQAttachment() {
      let params = {
        path: this.uppath,
        eqSource: Number(this.$route.query.eQSource),
        orderNo: this.$route.query.OrderNo,
        businessOrderNo: this.$route.query.businessOrderNo,
        joinFactoryId: this.$route.query.joinFactoryId,
        fileName: this.EQFileName,
      };
      if (this.delDate) {
        params.delDate = this.delDate;
      }
      this.spinning = true;
      upeQAttachment(params)
        .then(res => {
          if (res.code) {
            this.$message.success("上传成功");
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.delDate = null;
          this.uppath = "";
          this.spinning = false;
        });
    },
    //下载附件
    Downloadattachments() {
      let params = {
        orderNo: this.$route.query.OrderNo,
        joinFactoryId: this.$route.query.joinFactoryId,
        businessOrderNo: this.$route.query.businessOrderNo,
        eqSource: Number(this.$route.query.eQSource),
      };
      downeQAttachment(params).then(res => {
        if (res.code) {
          if (res.data) {
            const nameadata = res.data.split(";");
            const data = nameadata[1];
            const name = nameadata[0];
            const xhr = new XMLHttpRequest();
            xhr.open("GET", data, true);
            xhr.responseType = "blob";
            xhr.onload = function () {
              if (xhr.status === 200) {
                const blob = xhr.response;
                const link = document.createElement("a");
                link.href = window.URL.createObjectURL(blob);
                link.download = name;
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }
            };
            xhr.send();
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest2(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest22(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.replyForm.path = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    beforeUploadnew(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("图片只支持.jpg/.png图片格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    async httpRequest3(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest4(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest5(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.arrData2.push(res.data);
          this.replyForm.image = this.arrData2.toString(",");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest6(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      console.log(this.id, "1588");
      if (this.$route.query.Jump == "预审") {
        await eqExcelmkt(JoinFactoryId, this.OrderNo, BusinessOrderNo, formData, this.id).then(res => {
          if (res.code == 1) {
            data.onSuccess(res.data);
            this.getOrderList();
            this.$message.success("EQ回导成功");
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        await emSEQMaineqExcel(JoinFactoryId, this.OrderNo, BusinessOrderNo, formData).then(res => {
          if (res.code == 1) {
            data.onSuccess(res.data);
            this.getOrderList();
            this.$message.success("EQ回导成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 点击查看上传图片
    handleCancelPreview() {
      this.previewVisible = false;
    },
    handlePreview(file) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
      // this.previewVisible = true;
      // this.previewImage = file.response || file.thumbUrl;
    },
    onPaste(event) {
      //console.log('e',event)
      event.preventDefault();
      event.stopPropagation();
      const items = (event.clipboardData || window.clipboardData).items;
      //console.log('items',items)
      let file = null;
      if (!items || items.length === 0) {
        this.$message.error("当前浏览器不支持本地或请打开图片再复制");
        return;
      }
      // 搜索剪切板items
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf("image") !== -1) {
          file = items[i].getAsFile();
          break;
        }
      }
      if (!file) {
        this.$message.error("粘贴内容非图片");
        return;
      }
      // const fileList = Array.from(pastedImages).map((image) => new File([image], image.name));
      // fileListData.value = [...fileList];
      // if (file) {
      //   this.$refs.fileRef.httpRequest1(file);
      // }

      // var formRrcover = new FormData()
      // formRrcover.append('file', file)
      // // formRrcover.append('processData', false)
      // upLoadFlyingFile(formRrcover).then(res => {
      //   if (res.code == 1) {
      //     //console.log('res',res.data)
      //     // data.onSuccess(res.data);
      //     // this.arrData.push(res.data)
      //     // this.path = this.arrData.toString(',')
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
    },
    isEmptyOrWhitespace(content) {
      return /^\s*$/.test(content);
    },
    // 提交
    uploadclick(item) {
      var val = {};
      if (item) {
        val.askType = item.askType;
        val.businessOrderNo = item.businessOrderNo;
        val.eqSource = item.eqSource;
        val.joinFactoryId = item.joinFactoryId;
        val.keyType = item.keyType;
        val.orderNo = item.orderNo;
        val.problemDescription = item.contentS;
        val.proposalA = item.contentA;
        val.proposalB = item.contentB;
        val.proposalC = item.contentC;
        val.problemDescription_en = item.contentS_en;
        val.proposalA_en = item.contentA_en;
        val.proposalB_en = item.contentB_en;
        val.proposalC_en = item.contentC_en;
        val.enKeyType = item.enKeyType;
        val.eqNumber = item.eqNumber;
        if (item.image) {
          this.path = item.image.join(",");
        }
      } else {
        val = this.form;
      }
      if (!this.path && this.askType == 1) {
        this.$message.warning("请上传文件");
        return;
      }
      if (!val.keyType) {
        val.keyType = " ";
      }
      if (!val.enKeyType) {
        val.enKeyType = " ";
      }
      if (this.askType == 0 && !item) {
        if (this.isEmptyOrWhitespace(val.problemDescription)) {
          this.$message.warning("请填写问题描述");
          return;
        }
        if (this.isEmptyOrWhitespace(val.proposalA) && this.isEmptyOrWhitespace(val.proposalB) && this.isEmptyOrWhitespace(val.proposalC)) {
          this.$message.warning("请填写建议方案");
          return;
        }
      }
      let params = val;
      if (params.problemDescription) {
        var arr1 = params.problemDescription.split("");
        if (arr1.length > 500) {
          arr1 = arr1.slice(0, 500);
        }
        params.problemDescription = arr1.join("");
      }

      if (params.proposalA) {
        var arr2 = params.proposalA.split("");
        if (arr2.length > 200) {
          arr2 = arr2.slice(0, 200);
        }
        params.proposalA = arr2.join("");
      }

      if (params.proposalB) {
        var arr3 = params.proposalB.split("");
        if (arr3.length > 200) {
          arr3 = arr3.slice(0, 200);
        }
        params.proposalB = arr3.join("");
      }

      if (params.proposalC) {
        var arr4 = params.proposalC.split("");
        if (arr4.length > 200) {
          arr4 = arr4.slice(0, 200);
        }
        params.proposalC = arr4.join("");
      }
      params.orderNo = this.$route.query.OrderNo;
      if (this.$route.query.eQSource) {
        params.eqSource = Number(this.$route.query.eQSource);
      }
      params.joinFactoryId = this.$route.query.joinFactoryId;
      params.businessOrderNo = this.$route.query.businessOrderNo;
      params.askType = this.askType;
      this.spinning1 = true;
      params.eqNumber = this.currentEqNum;
      if (this.path) {
        const pathArray = this.path.split(",").filter(element => element !== "" && element);
        this.path = pathArray.join(",");
      }
      params.path = this.path;
      submit(params)
        .then(res => {
          if (res.code) {
            if (!item) {
              this.$message.success("提交成功");
            }
            this.fileListData = [];
            this.fileList = [];
            this.arrData = [];
            this.path = "";
            this.solution = "";
            this.askType = 0;
            this.addTr = false;
            this.form = {};
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning1 = false;
        });
    },
    // 取消提交
    cancelClick() {
      this.addTr = false;
      this.askType = 0;
      this.arrData = [];
      this.fileListData = [];
      this.fileList = [];
      this.form = {};
      this.path = "";
      this.solution = "";
    },
    //删除问题
    deleteClick(item) {
      if (confirm("确认删除该问题吗？")) {
        let params = {
          id: item.id,
          eqSource: Number(this.$route.query.eQSource),
        };
        setdeleetproblems(params).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 编辑问题
    editClick(item) {
      this.dataVisible2 = true;
      this.editForm.id = item.id;
      this.editForm.path = item.image;
      this.editForm.filePath = item.filePath;
      this.editForm.problemDescription = item.contentS;
      this.editForm.proposalA = item.contentA;
      this.editForm.proposalB = item.contentB;
      this.editForm.proposalC = item.contentC;
      this.editForm.problemDescription_en = item.contentS_en;
      this.editForm.proposalA_en = item.contentA_en;
      this.editForm.proposalB_en = item.contentB_en;
      this.editForm.proposalC_en = item.contentC_en;
      this.editForm.askType = item.askType;
      this.fileListData3 = [];
      this.fileList4 = [];
      if (item.image) {
        item.image.forEach((e, index) => {
          const fileName = e.split("/").pop(); // 提取文件名
          this.fileListData3.push({
            uid: index,
            name: fileName,
            status: "done",
            url: e,
            thumbUrl: e, //缩略图地址
          });
        });
      }
      if (item.filePath) {
        item.filePath.split(",").forEach((e, index) => {
          const fileName = e.split("/").pop(); // 提取文件名
          this.fileList4.push({
            uid: index,
            name: fileName,
            status: "done",
            url: e,
          });
        });
      }
    },
    handleOk2() {
      let params = this.editForm;
      if (this.isEmptyOrWhitespace(params.problemDescription)) {
        this.$message.warning("请输入问题描述");
        return;
      }
      if (this.isEmptyOrWhitespace(params.proposalA) && this.isEmptyOrWhitespace(params.proposalB) && this.isEmptyOrWhitespace(params.proposalC)) {
        this.$message.warning("请填写建议方案");
        return;
      }
      let str = "";
      this.fileListData3.forEach(e => {
        if (e.response) {
          str += e.response + ",";
        } else if (e.url) {
          str += e.url + ",";
        }
      });
      str = str.substring(0, str.length - 1);
      params.path = str;
      let str1 = "";
      this.fileList4.forEach(e => {
        if (e.response) {
          str1 += e.response + ",";
        } else if (e.url) {
          str1 += e.url + ",";
        }
      });
      str1 = str1.substring(0, str1.length - 1);
      params.filePath = str1;
      if (this.editForm.askType == "1" && !params.filePath) {
        this.$message.warning("文件确认请上传附件");
        return;
      }
      this.dataVisible2 = false;
      editingProblems(params).then(res => {
        if (res.code) {
          this.$message.success("编辑成功");
          this.fileListData3 = [];
          this.arrData1 = [];
          this.fileList4 = [];
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 编辑删除图片
    delClick(item) {
      if (confirm("确认删除该图片？")) {
        this.editForm.path.splice(this.editForm.path.indexOf(item), 1);
      }
    },
    radioChange() {
      if (this.EQTemplate == 1) {
        if (this.replyForm.value == "1") {
          this.replyForm.Quiz = this.replyForm.contentA_en;
        }
        if (this.replyForm.value == "2") {
          this.replyForm.Quiz = this.replyForm.contentB_en;
        }
        if (this.replyForm.value == "3") {
          this.replyForm.Quiz = this.replyForm.proposalC_en;
        }
        if (this.replyForm.value == "4") {
          this.replyForm.Quiz = this.replyForm.contentC_en;
        }
      } else {
        if (this.replyForm.value == "1") {
          this.replyForm.Quiz = this.replyForm.contentA;
        }
        if (this.replyForm.value == "2") {
          this.replyForm.Quiz = this.replyForm.contentB;
        }
        if (this.replyForm.value == "3") {
          this.replyForm.Quiz = this.replyForm.proposalC;
        }
        if (this.replyForm.value == "4") {
          this.replyForm.Quiz = this.replyForm.contentC;
        }
      }
    },
    // 回复问题
    replyClick(item) {
      this.dataVisible3 = true;
      this.replyForm.id = item.id;
      this.replyForm.contentS = item.contentS;
      this.replyForm.contentA = item.contentA;
      this.replyForm.contentB = item.contentB;
      this.replyForm.contentC = item.contentC;
      this.replyForm.contentS_en = item.contentS_en;
      this.replyForm.contentA_en = item.contentA_en;
      this.replyForm.contentB_en = item.contentB_en;
      this.replyForm.contentC_en = item.contentC_en;
      this.replyForm.image1 = item.image;
      this.replyForm.path = "";
      this.replyForm.image = "";
      setTimeout(() => {
        $(".ant-modal-header").on("mousedown", e => {
          this.startPosition.x = e.pageX;
          this.startPosition.y = e.pageY;
          this.startPosition.offsetX = e.offsetX;
          this.startPosition.offsetY = e.offsetY;
          this.isMovedown = true;
        });
      }, 200);
      document.body.addEventListener("mousemove", e => {
        if (this.isMovedown) {
          if (
            e.x - this.startPosition.x > 10 ||
            e.y - this.startPosition.y > 10 ||
            e.x - this.startPosition.x < -10 ||
            e.y - this.startPosition.y < -10
          ) {
            let w = $(".ant-modal-content").width();
            let h = $(".ant-modal-content").height();
            $(".ant-modal-content").css({
              left: e.pageX - this.startPosition.offsetX - (document.body.clientWidth - w) / 2 + "px",
              top: e.pageY - (document.body.clientHeight - 3 * h) / 2 + 50 + "px",
            });
          }
        }
      });
      document.body.addEventListener("mouseup", e => {
        this.isMovedown = false;
      });
    },
    handleOk3() {
      if (this.replyForm.value == undefined) {
        this.$message.warning("请选择方案！");
        return;
      }
      if (this.replyForm.value == "3") {
        this.replyForm.Quiz = this.EQTemplate == 1 ? this.replyForm.proposalC_en : this.replyForm.proposalC;
      }
      if (this.replyForm.Quiz == "") {
        this.$message.warning("请填写其他方案！");
        return;
      }

      this.dataVisible3 = false;
      let params = {
        id: this.replyForm.id,
        Quiz: this.replyForm.Quiz,
        image: this.replyForm.image,
        path: this.replyForm.path,
      };
      params.eqSource = this.$route.query.eQSource ? Number(this.$route.query.eQSource) : 0;
      replyProblems(params).then(res => {
        if (res.code) {
          this.$message.success("回复成功");
          this.replyForm.Quiz = "";
          this.replyForm.image = "";
          this.replyForm.path = "";
          this.replyForm.value = undefined;
          this.replyForm.proposalC = "";
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 发送问题
    sendClick(item) {
      if (confirm("确认发送该问题吗？")) {
        let params = {
          id: item.id,
        };
        params.eqSource = Number(this.$route.query.eQSource);
        sendProblems(params).then(res => {
          if (res.code) {
            this.getOrderList();
            this.$message.success("发送成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 一键发送
    allSenClick() {
      let params = {
        orderNo: this.$route.query.OrderNo,
        eqSource: Number(this.$route.query.eQSource),
        joinFactoryId: this.$route.query.joinFactoryId,
        businessOrderNo: this.$route.query.businessOrderNo,
      };
      if (this.$route.query.eQSource == "5" || this.$route.query.Jump == "预审") {
        if (confirm("您确认要提交全部问题吗？")) {
          toSendProblems(params).then(res => {
            if (res.code) {
              this.getOrderList();
              this.$message.success("发送成功");
            } else {
              this.$message.error(res.message);
            }
          });
        }
      } else {
        ppebuttonCheck(this.$route.query.id, "ToSendEq").then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "sendeq";
              this.dataVisible4 = true;
              this.meslist = "提交问客检查";
            } else {
              if (confirm("您确认要提交全部问题吗？")) {
                toSendProblems(params).then(res => {
                  if (res.code) {
                    this.getOrderList();
                    this.$message.success("发送成功");
                  } else {
                    this.$message.error(res.message);
                  }
                });
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    continueclick() {
      this.dataVisible4 = false;
      let params = {
        orderNo: this.$route.query.OrderNo,
        eqSource: Number(this.$route.query.eQSource),
        joinFactoryId: this.$route.query.joinFactoryId,
        businessOrderNo: this.$route.query.businessOrderNo,
      };
      setTimeout(() => {
        if (this.checkType == "sendeq") {
          if (confirm("您确认要提交全部问题吗？")) {
            toSendProblems(params).then(res => {
              if (res.code) {
                this.getOrderList();
                this.$message.success("发送成功");
              } else {
                this.$message.error(res.message);
              }
            });
          }
        }
      }, 500);
    },
    // 复制链接
    copyClick() {
      let URL = process.env.VUE_APP_API_JSON_URL;
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      let eQSource = this.$route.query.eQSource;
      timestampVerifyCode().then(res => {
        if (res.code) {
          let Timestamp = res.data.timestamp;
          let VerifyCode = res.data.verifyCode;
          let content =
            URL +
            "/eqDetails1?JoinFactoryId=" +
            JoinFactoryId +
            "&OrderNo=" +
            OrderNo +
            "&Timestamp=" +
            Timestamp +
            "&VerifyCode=" +
            VerifyCode +
            "&BusinessOrderNo=" +
            BusinessOrderNo +
            "&eQSource=" +
            eQSource;
          if (window.clipboardData) {
            window.clipboardData.setData("text", content);
          } else {
            (function () {
              document.oncopy = function (e) {
                e.clipboardData.setData("text", content);
                e.preventDefault();
                document.oncopy = null;
              };
            })(content);
            document.execCommand("Copy");
          }
          this.$message.success("复制成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //复制问题
    copy1Click(item) {
      if (!this.allowAddTr) {
        this.$message.error("该订单有未回复的问客,暂不可以新增加问题！");
        return;
      }
      if (confirm("确认复制该问题吗？")) {
        const newItem = { ...item };
        delete newItem.reply;
        newItem.status = 1;
        newItem.createTime = moment(new Date()).format("MM-DD HH:mm");
        this.orderData.push(newItem);
        this.uploadclick(newItem);
      }
    },
    // 撤回问题
    backClick(item) {
      if (confirm("确认撤回该问题吗？")) {
        let params = {
          id: item.id,
          eqSource: Number(this.$route.query.eQSource),
        };
        backProblems(params).then(res => {
          if (res.code) {
            this.getOrderList();
            this.$message.success("撤回成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //下载图片
    downimg(path) {
      this.currentImageUrl = path; // 设置当前图片URL
      this.$nextTick(() => {
        this.$viewerApi({
          images: [this.currentImageUrl],
        });
      });
      //window.location.href=path
    },
    // 下载工程文件
    down(item) {
      let pcbFileName = this.pcbFileName;
      if (item) {
        var data = item.split(",");
        for (var i = 0; i < data.length; i++) {
          if (data[i].indexOf("EQ%5C") != -1 || data[i].indexOf("myhuaweicloud") != -1) {
            (function (i) {
              var name = "";
              if (data[i].indexOf("EQ%5C") != -1) {
                name = decodeURIComponent(data[i].split("EQ%5C")[1]);
              } else {
                name = decodeURIComponent(data[i].split("/").slice(-1)[0]);
              }
              const xhr = new XMLHttpRequest();
              xhr.open("GET", data[i], true);
              xhr.responseType = "blob";
              xhr.onload = function () {
                if (xhr.status === 200) {
                  const blob = xhr.response;
                  const link = document.createElement("a");
                  link.href = window.URL.createObjectURL(blob);
                  link.download = pcbFileName ? pcbFileName + "+" + name : name;
                  link.style.display = "none";
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }
              };
              xhr.send();
            })(i);
          } else {
            window.location.href = data[i];
          }
        }
      }
    },
    getBlob(url, cb) {
      var xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          cb(xhr.response);
        }
      };
      xhr.send();
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename);
      } else {
        var link = document.createElement("a");
        var body = document.querySelector("body");

        link.href = window.URL.createObjectURL(blob);
        link.download = filename;

        link.style.display = "none";
        body.appendChild(link);

        link.click();
        body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
      }
    },
    exportBtn(type) {
      let params = Number(type);
      let OrderNo = this.$route.query.OrderNo;
      let eQSource = this.$route.query.eQSource;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      exportEQReportv2(JoinFactoryId, OrderNo, params, eQSource, BusinessOrderNo).then(res => {
        if (res.code) {
          this.downfile(res.data, res.message);
          downeQAttachmentbycust(JoinFactoryId, OrderNo, BusinessOrderNo).then(res => {
            if (res.code) {
              if (res.data) {
                let path = res.data.split(",");
                path.forEach(item => {
                  this.downfile(item);
                });
              }
            } else {
              this.$message.error(res.message);
            }
          });
          this.file1data = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downfile(val, name) {
      const urlObj = new URL(val);
      const path = urlObj.pathname;
      const fileName = path.substring(path.lastIndexOf("/") + 1);
      const fileNameWithoutQuery = name
        ? name
        : this.pcbFileName
        ? this.pcbFileName + "+" + decodeURI(fileName.split("?")[0])
        : decodeURI(fileName.split("?")[0]);
      const xhr = new XMLHttpRequest();
      xhr.open("GET", val, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = fileNameWithoutQuery;
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      };
      xhr.send();
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    Replytodownload() {
      let val = this.$route.query;
      exporteQReportcustv2(val.joinFactoryId, val.OrderNo, Number(val.eQSource), val.businessOrderNo).then(res => {
        if (res.code) {
          this.downfile(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel1() {
      this.emaildataVisible = false;
      this.loading = false;
      this.delDate = null;
      this.uppath = "";
      this.dataVisibleileFile = false;
    },
    handleOkFile() {
      this.dataVisibleileFile = false;
      this.upeQAttachment();
    },
    TimeChange(value, dateString) {
      this.delDate = dateString;
    },
    Uploadattachments() {
      this.fileList7 = [];
      if (this.$route.query.eqNumber > 1 || this.$route.query.para4IntDelivery <= 3) {
        this.dataVisibleileFile = true;
        this.delDate = this.$route.query.deliveryDate;
      } else {
        this.$refs.fileRef7.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
      }
    },
    Emailinformation() {
      teQMail(this.$route.query.id)
        .then(res => {})
        .finally(() => {
          this.emaildataVisible = true;
          this.getemaildata();
        });
    },
    getemaildata() {
      emSEQMaineqRepair(this.id).then(res => {
        if (res.code) {
          this.emaildataCopyOriginal.body = res.data.body;
          this.emaildataCopyOriginal.cc = res.data.cc;
          this.emaildataCopyOriginal.eqEmail = res.data.eqEmail;
          this.emaildataCopyOriginal.id = res.data.id;
          this.emaildataCopyOriginal.subject = res.data.subject;
          this.emaildata = res.data;
        }
      });
    },
    // 邮件信息保存
    emailhandleOk() {
      this.emaildata.id = this.id;
      emSEQMaineqSave(this.emaildata).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          this.sendFlag = false;
          this.getemaildata();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 邮件发送
    emailhandleSend() {
      this.sendmail();
    },
    // 发送邮件
    sendmail() {
      this.loading = true;
      this.dissend = true;
      this.spinning = true;
      let jiek = this.$route.query.Jump == "预审" ? eqSendmailmkt : eqSendmail;
      jiek(this.id)
        .then(res => {
          if (res.code) {
            this.$message.success("发送邮件成功");
            if (this.$route.query.Jump == "预审") {
              this.$router.push({ path: "/shichang/Orderverify" });
            } else if (this.$route.query.Jump == "制作") {
              this.$router.push({
                path: "/gongcheng/engineering",
                query: {
                  OrderNo: this.$route.query.OrderNo,
                  id: this.$route.query.id,
                  factory: this.$route.query.joinFactoryId,
                  typee: "2",
                },
              });
            } else if (this.$route.query.Jump == "后端") {
              this.$router.push({
                path: "/gongcheng/engineering",
                query: {
                  OrderNo: this.$route.query.OrderNo,
                  id: this.$route.query.id,
                  factory: this.$route.query.joinFactoryId,
                  typee: "1",
                },
              });
            } else if (this.$route.query.Jump == "QAE") {
              this.$router.push({ path: "/gongcheng/projectQae" });
            } else if (this.$route.query.Jump == "问客管理") {
              this.$router.push({ path: "/gongcheng/projectEQ" });
            } else if (this.$route.query.Jump == "市场问客管理") {
              this.$router.push({ path: "/mkt/marketEQ" });
            } else if (this.$route.query.Jump == "投料") {
              this.$router.push({ path: "/OrderManagement/CompositionManagement" });
            } else if (this.$route.query.Jump == "EQ列表") {
              this.$router.push({ path: "/OrderManagement/EQManagement" });
            } else if (this.$route.query.Jump == "QAE") {
              this.$router.push({
                path: "/gongcheng/engineering",
                query: {
                  OrderNo: this.$route.query.OrderNo,
                  id: this.$route.query.id,
                  factory: this.$route.query.joinFactoryId,
                  typee: "3",
                },
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.dissend = false;
          }, 2000);
          this.emaildataVisible = false;
          this.loading = false;
          this.spinning = false;
        });
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.key == "e" && this.isCtrlPressed && this.emaildataVisible) {
        this.isCtrlPressed = false;
        this.emailhandleSend();
        e.preventDefault();
      }
    },
    showCopy(type) {
      window.addEventListener("paste", this.getClipboardFiles);
      this.showCopyType = type;
      try {
        navigator.clipboard.read().then(res => {
          const clipboardItems = res;
          if (clipboardItems[0].types[0].indexOf("image") > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then(b => {
              const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {
                type: clipboardItems[0].types[0],
              });
              this.file = files;
              this.getClipboardFiles();
            });
          } else {
            this.$message.error("粘贴内容不是图片");
            return;
          }
        });
      } catch (e) {
        ////console.log('出错了')
      }

      // this.show = true;
      // this.$nextTick(() => { // 监听粘贴事件
      //   document.getElementById('Msg').addEventListener('paste', this.getClipboardFiles);
      // });
    },
    bodyClick() {
      //console.log('bodyClick')
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    Msgcancel() {
      this.show = false;
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      //console.log('file',file)
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload1(file); // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload1(file)) {
        // **** return true 之后进行上传
        this.startloading = true;
        const formData = new FormData();
        // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append("file", file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file", // 接口地址
          method: "post",
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => {
            if (res.code) {
              //console.log('res',res)
              file.status = "done";
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              //console.log('this.showCopyType',this.showCopyType)
              if (this.showCopyType == "1") {
                this.handleChange1(file, arr);
              } else if (this.showCopyType == "5") {
                this.handleChange5(file, arr);
              } else if (this.showCopyType == "3") {
                this.handleChange3(file, arr);
              }
              this.startloading = false;
              this.show = false;
            } else {
              this.$message.error(data.message || "网络异常,请重试或检查网络连接状态");
              this.startloading = false;
              this.show = false;
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
  },
  created() {
    this.deReplytodownload = this.debounce(this.Replytodownload, 500);
    let params = {
      orderNo: this.$route.query.OrderNo,
      businessOrderNo: this.$route.query.businessOrderNo,
    };
    proorderinformation(this.$route.query.joinFactoryId, params).then(res => {
      if (res.code) {
        this.pcbFileName = res.data.pcbFileName;
      }
    });
  },
  mounted() {
    this.getOrderList();
    this.getSelect();
    this.getSelect1();
    this.getpcbfile();
    //this.getSelect2()
    let uid = this.$route.query.uid || "";
    this.Jump = this.$route.query.Jump;
    if (
      this.Jump == "预审" &&
      uid &&
      uid != "" &&
      uid.slice(0, 2) == "PO" &&
      this.$route.query.joinFactoryId == 12 &&
      this.checkPermission("MES.EngineeringModule.EngineeringEqPage.EngineeringEqPageLickSending")
    ) {
      this.sendshow = true;
    } else if (this.Jump != "预审" && this.checkPermission("MES.EngineeringModule.EngineeringEqPage.EngineeringEqPageLickSending")) {
      this.sendshow = true;
    } else {
      this.sendshow = false;
    }
    if (this.$route.query.eQSource == 4) {
      this.OrderNo = this.$route.query.OrderNo2;
      this.id = this.$route.query.OrderNo;
    } else {
      this.OrderNo = this.$route.query.OrderNo;
    }
    if (this.$route.query.id) {
      this.id = this.$route.query.id;
    }
    let params = {
      StepName: "",
      Problem: "",
      KeyWord: "",
      Factoryid: this.$route.query.joinFactoryId,
    };
    eqQuestion(params).then(res => {
      if (res.code) {
        this.eqQuestion = res.data;
      } else {
        this.$message.error(res.message);
      }
    });

    // this.$nextTick(() => { // 监听粘贴事件
    //   document.getElementById('Msg').addEventListener('paste', this.getClipboardFiles);
    // });
    // eqTemplteSelectList(this.OrderNo,this.$route.query.eQSource).then(res=>{
    //   if(res.code){
    //     this.selectList = res.data || []
    //   }else{
    //     this.$message.error(res.message)
    //   }
    // })
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("paste", this.getClipboardFiles);
  },
};
</script>

<style scoped lang="less">
/deep/.ant-btn > .anticon + span,
.ant-btn > span + .anticon {
  margin-left: 2px;
}
/deep/.ant-collapse {
  border: none;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 0;
}
/deep/.ant-collapse-header {
  border-left: 1px solid;
  border-right: 1px solid;
  border-color: #e1e1e2;
}
#Msg {
  overflow: hidden;
  width: 104px;
  height: 104px;
  border: 1px dashed #d9d9d9;
  text-align: center;
  margin: 0 auto;
  border-radius: 5px;
}
#Msg:hover {
  border-color: #ff9900;
}
/deep/ th {
  font-weight: 500;
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  text-align: left;
}

.box {
  // height: 900px;
  height: 817px;
  min-width: 1670px;
  // overflow-y: auto;
  background: white;
  padding: 10px;
}
p {
  margin-bottom: 4px;
}
/deep/ .tab {
  width: 100% !important;
  text-align: center;
}
.left {
  text-align: left !important;
}
#app {
  // font-family: Avenir, Helvetica, Arial, sans-serif;
  //font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}
table {
  width: 100%;
  /*height: 100%;*/
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
.scorllclass {
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: #b6b5b4;
    // #fff9e6
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
</style>
