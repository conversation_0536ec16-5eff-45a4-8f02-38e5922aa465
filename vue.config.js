let path = require('path')
const webpack = require('webpack')
const ThemeColorReplacer = require('webpack-theme-color-replacer')
const {getThemeColors, modifyVars} = require('./src/utils/themeUtil')
const {resolveCss} = require('./src/utils/theme-color-replacer-extend')
const CompressionWebpackPlugin = require('compression-webpack-plugin')

const productionGzipExtensions = ['js', 'css']
const isProd = process.env.NODE_ENV === 'production'
const Version = new Date().getTime()
const assetsCDN = {
  // webpack build externals
  externals: {
    // vue: 'Vue',
    // 'vue-router': 'VueRouter',
    // vuex: 'Vuex',
    // axios: 'axios',
    // nprogress: 'NProgress',
    // clipboard: 'ClipboardJS',
    // '@antv/data-set': 'DataSet',
    // 'js-cookie': 'Cookies'
  },
  css: [
  ],
  js: [
    // '//cdn.jsdelivr.net/npm/vue@2.6.11/dist/vue.min.js',
    // '//cdn.jsdelivr.net/npm/vue-router@3.3.4/dist/vue-router.min.js',
    // '//cdn.jsdelivr.net/npm/vuex@3.4.0/dist/vuex.min.js',
    // '//cdn.jsdelivr.net/npm/axios@0.19.2/dist/axios.min.js',
    // '//cdn.jsdelivr.net/npm/nprogress@0.2.0/nprogress.min.js',
    // '//cdn.jsdelivr.net/npm/clipboard@2.0.6/dist/clipboard.min.js',
    // '//cdn.jsdelivr.net/npm/@antv/data-set@0.11.4/build/data-set.min.js',
    // '//cdn.jsdelivr.net/npm/js-cookie@2.2.1/src/js.cookie.min.js'
  ]
}

module.exports = {
  devServer: {
    // proxy: {
    //   '/api': { //此处要与 /services/api.js 中的 API_PROXY_PREFIX 值保持一致
    //     target: process.env.VUE_APP_API_BASE_URL,
    //     changeOrigin: true,
    //     pathRewrite: {
    //       '^/api': ''
    //     }
    //   }
    // }
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [path.resolve(__dirname, "./src/theme/theme.less")],
    }
  },
 
  configureWebpack: config => {    
    config.entry.app = ["babel-polyfill", "whatwg-fetch", "./src/main.js"];
    config.performance = {
      hints: false
    }  
    const Timestamp = new Date().getTime() 
    config.output.filename = `static/js/[name].${Version}.${Timestamp}.js`
    config.output.chunkFilename = `static/js/[name].${Version}.${Timestamp}.js`
    config.plugins.push(
        new ThemeColorReplacer({
          fileName: `css/theme-colors-[contenthash:8].css`,
          matchColors: getThemeColors(),
          injectCss: true,
          resolveCss
        })
    )
    // Ignore all locale files of moment.js
    config.plugins.push(new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/))
    // 生产环境下将资源压缩成gzip格式
    if (isProd) {
      // add `CompressionWebpack` plugin to webpack plugins
      config.plugins.push(new CompressionWebpackPlugin({
        filename: '[path].gz[query]',
        algorithm: 'gzip',
        test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
        threshold: 10240,
        minRatio: 0.8
      }))
    }
    // if prod, add externals
    if (isProd) {
      config.externals = assetsCDN.externals
    }
    // 为了提高网页加载性能 启用代码压缩和优化以减小文件大小，并提高加载速度。
    // if (isProd) {
    //   config.optimization.minimizer.push(new TerserPlugin());
    // }
    // //  configureWebpack 中配置代码分割。通过将应用程序打包成多个文件，并在需要时按需加载，可以减少初始加载时间。 2023/7/5    
    // config.optimization = {
    //   splitChunks: {
    //     chunks: 'all',
    //   },
    // }
  },
  chainWebpack: config => {
    config.plugins.delete("prefetch");
    // 生产环境下关闭css压缩的 colormin 项，因为此项优化与主题色替换功能冲突
    config.module
        .rule('images')
        .use('url-loader')
        .tap(options => {
          options.name = `static/img/[name].${Version}.[ext]`
          options.fallback = {
            loader: 'file-loader',
            options: {
              name: `static/img/[name].${Version}.[ext]`
            }
          }
          return options
        })
    if (isProd) {
      config.plugin('optimize-css')
          .tap(args => {
            args[0].cssnanoOptions.preset[1].colormin = false
            return args
          })
    }
    // 生产环境下使用CDN
    if (isProd) {
      config.plugin('html')
          .tap(args => {
            args[0].cdn = assetsCDN
            return args
          })
    }
  },
  css: {
    extract: {
      // 修改打包后css文件名
      filename: `static/css/[name].${Version}.css`,
      chunkFilename: `static/css/[name].${Version}.css`
    },
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: modifyVars(),
          javascriptEnabled: true
        }
      },
    }
  },
  publicPath: process.env.VUE_APP_PUBLIC_PATH,
  outputDir: 'dist',
  assetsDir: 'static',
}