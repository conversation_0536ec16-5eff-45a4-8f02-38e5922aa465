import {request, METHOD} from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
//查
export async function getList(params) {
    return request("/api/app/e-mSTGSLogin-record-list", METHOD.GET,params)
}
//改
export async function UpdateList(params) {
    return request('/api/app/e-mSTGSLogin-record-list/update', METHOD.POST, params)
}
//删
export async function delList(id) {
    return request(`/api/app/e-mSTGSLogin-record-list/${id}/by-id`, METHOD.DELETE)
}
//增
export async function createList(params) {
    return request('/api/app/e-mSTGSLogin-record-list', METHOD.POST,params)
}
export default {
    getList,
    UpdateList,
    delList,
    createList
  }
  