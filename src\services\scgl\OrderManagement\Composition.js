import { request, METHOD } from '@/utils/request'
// 获取订单列表
export async function proPinBanOrderList(params) {
    return request("/api/app/pro-pin-ban-order/pro-pin-ban-order-list", METHOD.GET, params)
}
// 获取sop参数
export async function sopParaList(id,params) {
    return request(`api/app/pro-pin-ban-order/sop-para-list?OrderNo=${id}&BusinessOrderNo=${params}`, METHOD.GET, )
}
// 获取参数信息
export async function pinBanDetilList(id,params) {
    return request(`/api/app/pro-pin-ban-order/pin-ban-detil-list?OrderNo=${id}&BusinessOrderNo=${params}`, METHOD.GET, )
}
// 创建管制卡
export async function proCardInfo(params) {
    return request(`api/app/pro-card-info`, METHOD.POST,params )
}
// 管制卡补料
export async function proCardInfoFeed(params) {
    return request(`/api/app/pro-card-info/feed`, METHOD.POST,params )
}
// 管制卡列表
export async function proCardInfoList(params) {
    return request(`api/app/pro-card-info/pro-card-info-list`, METHOD.GET,params )
}
// 打印管制卡信息
export async function printCard(params,BusinessOrderNo) {
    return request(`/api/app/pro-card-info/print-card/${params}?BusinessOrderNo=${BusinessOrderNo}`, METHOD.POST, )
}
// 跳转订单详情 
export async function proPinBanOrder(id,params) {
    return request(`api/app/pro-pin-ban-order/pro-pin-ban-order?OrderNo=${id}&BusinessOrderNo=${params}`, METHOD.GET, )
}
// 流程卡审核 
export async function setChecked(params) {
    return request(`/api/app/pro-card-info/set-checked`, METHOD.POST, params)
}
// 拼版作废 
export async function cancelPinBanOrder(params) {
    return request(`/api/app/pro-card-info/cancel-pin-ban-order`, METHOD.POST, params)
}
// 拼版订单操作日志 
export async function pinBanOrderLogList(Id) {
    return request(`/api/app/pro-pin-ban-order/pro-pin-ban-order-log-list/${Id}`, METHOD.GET, )
}
// 获取修改参数 
export async function getForEdit(Id) {
    return request(`/api/app/pro-pin-ban-order/pin-ban-info-for-edit/${Id}`, METHOD.GET, )
}
//保存获取修改参数 
export async function setPinBanInfo(params) {
    return request(`/api/app/pro-pin-ban-order/set-pin-ban-info`, METHOD.POST,params )
}
// 打印状态设置 
export async function printStatus(params) {
    return request(`/api/app/pro-card-info/print-status`, METHOD.POST,params )
}
// 列表获取下载文件路径
export async function pinbanFilePath(OrderNo,TypeKeys,BusinessOrderNo) {
    return request(`/api/app/pro-pin-ban-order/pinban-file-path?OrderNo=${OrderNo}&TypeKeys=${TypeKeys}&BusinessOrderNo=${BusinessOrderNo}`, METHOD.GET, )
}
// 分卡
export async function scorecard(CardId,Num){
    return request(`/api/app/pro-card-info/scorecard/${CardId}?Num=${Num}`,METHOD.POST,)
}
export async function factoryList(){
    return request(`/api/app/pro-pin-ban-order/factory-list`,METHOD.GET,)
}
export async function setFactory(params){
    return request(`/api/app/pro-pin-ban-order/set-factory`,METHOD.POST,params)
}
export async function upLoadFile(params){
    return request(`/api/app/pro-pin-ban-order/up-load-file`,METHOD.POST,params)
}
export async function setPinbanFilePath(params){
    return request(`/api/app/pro-pin-ban-order/set-pinban-file-path`,METHOD.POST,params)
}
export async function setXtSure(params){
    return request(`/api/app/pro-pin-ban-order/set-xt-sure`,METHOD.POST,params)
}
export async function delivergoods(joinFactoryId,params){
    return request(`/api/app/pcb-factory-sender-info/delivergoods/${joinFactoryId}`,METHOD.POST,params)
}


export default {
    proPinBanOrderList,
    sopParaList,
    proCardInfo,
    proCardInfoList,
    printCard,
    proPinBanOrder,
    setChecked,
    cancelPinBanOrder,
    pinBanOrderLogList,
    pinBanDetilList,
    getForEdit,
    setPinBanInfo,
    printStatus,
    factoryList,
    setFactory,
    setXtSure,
}