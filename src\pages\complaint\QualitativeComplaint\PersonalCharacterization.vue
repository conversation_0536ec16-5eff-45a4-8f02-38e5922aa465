<!--客诉定性投诉详情-->
<template>
    <div class="PersonalCharacterization">
        <a-collapse :activeKey="'1'" @change="CollapseList">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>投诉详情</div>
                        <div>{{ text }}</div>
                    </div>
                </template>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'客户型号'">
                            <a-input disabled v-model="complaintdata.customerModel"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'客户联系人'">
                            <a-input disabled v-model="complaintdata.contactName"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'联系人电话'">
                            <a-input disabled v-model="complaintdata.contactNumber"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'投诉日期'">
                            <a-input disabled v-model="complaintdata.complaintDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'生产型号'">
                            <a-input disabled v-model="complaintdata.proOrderNo"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'交货日期'">
                            <a-input disabled v-model="complaintdata.deliveryDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'交货总数'" >
                            <a-input disabled v-model="complaintdata.deliveryNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'不良总数'">
                            <a-input disabled v-model="complaintdata.badNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'周期'" >
                            <a-input disabled v-model="complaintdata.dateCode"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'问题描述'" >
                            <div class="questiontext">{{ complaintdata. problemDescription}}</div>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'上传附件'" >
                            <span style="color: #428bca;cursor: pointer;" @click="filedown(complaintdata.complaintFilePath)"> 
                                <a-icon type="link" style="padding: 0 5px;" ></a-icon>{{complaintdata.complaintFileName}}
                            </span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 3 }" :wrapper-col="{ span: 18 }" :label="'客户要求措施'" >
                            <a-radio-group   name="radioGroup" disabled v-model="complaintdata.takeSteps">
                                <a-radio :value="1">退货返工</a-radio>
                                <a-radio :value="2">报废补货</a-radio>
                                <a-radio :value="3">报废扣款</a-radio>
                                <a-radio :value="4">特采</a-radio>
                                <a-radio :value="5">其他</a-radio>
                            </a-radio-group>
                            <a-input style="width: 300px;" disabled v-model="complaintdata.takeStepsName"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'是否需要费用补偿'" >
                            <a-radio-group disabled  name="radioGroup" v-model="complaintdata.costCompensation">
                                <a-radio :value="false">否</a-radio>
                                <a-radio :value="true">是</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="4">
                        <a-form-model-item :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :label="'是否需要8D报告'" >
                            <a-radio-group  disabled name="radioGroup" v-model="complaintdata.need8DReport">
                                <a-radio :value="false">否</a-radio>
                                <a-radio :value="true">是</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span:6}" :wrapper-col="{ span: 16 }" :label="'要求改善报告回复客户时间'" >
                            <a-radio-group  disabled name="radioGroup" v-model="complaintdata.needReplytime">
                                <a-radio :value="3">3天</a-radio>
                                <a-radio :value="2">2天</a-radio>
                                <a-radio :value="1">1天</a-radio>                              
                                <a-radio :value="0">其他</a-radio>
                            </a-radio-group>
                            <a-input style="width: 150px;" disabled v-model="complaintdata.needReplytimeName"></a-input>
                        </a-form-model-item>
                    </a-col>
                   
                </a-row>
            </a-collapse-panel>
        </a-collapse>
        <div style="margin-top: 15px;"></div>
        <a-collapse :activeKey="'1'" @change="CollapseList1">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>客诉定性</div>
                        <div>{{ text1 }}</div>
                    </div>
                </template>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'*评审结果'" class="require">
                            <a-radio-group   name="radioGroup" v-model="qualitative.verdict" :disabled="statusbar!=10">
                                <a-radio :value="1">不成立,问题可忽略</a-radio>
                                <a-radio :value="2">客方责任</a-radio>
                                <a-radio :value="3">供方责任</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'原由详述'" >
                            <a-textarea v-model="qualitative.verdictRemark" :auto-size="{ minRows: 4, maxRows:4}" :disabled="statusbar!=10"></a-textarea >
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-if="!complaintdata.verdictFilePath">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <a-upload  
                            ref="fileRef7"
                            :file-list="fileList7"
                            :before-upload="beforeUpload7"
                            :customRequest="httpRequest7"
                            accept=".jpg,.bmp,.png,.zip,.rar,.7z"
                            @change="handleChange7">
                            <a-button v-if="fileList7.length==0" > <a-icon type="upload"/> 选择文件</a-button>
                            <span style="padding:0 15px;" v-if="fileList7.length==0" >未上传任何附件</span>
                            </a-upload>                           
                        <div style="color: #ff9900;font-size: 12px;">温馨提示：附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式，文件大小不超过10M。</div>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-else-if="fileList7.length==0">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <span style="color: #428bca;cursor: pointer;" @click="filedown(complaintdata.verdictFilePath)"> 
                                <a-icon type="link" style="padding: 0 5px;" ></a-icon>{{complaintdata.verdictFileName}}
                            </span>  
                            <span v-if="complaintdata.status=='10' && complaintdata.verdict" style="padding-left: 20px;" @click="deletefile" >
                                <a-tooltip title="该操作不可撤销，删除后可重新上传附件"><a-icon type="delete"></a-icon></a-tooltip>                               
                            </span>               
                        <div style="color: #ff9900;font-size: 12px;">温馨提示：附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式，文件大小不超过10M。</div>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                            <a-button type="primary" style="margin-left: 185px;" @click="handleok" :disabled="statusbar!=10">确定</a-button>
                            <a-button type="normal" style="margin-left: 30px;" @click="back">返回</a-button>
                    </a-col>
                </a-row>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>
<script>
import {complaintsqualitative,uploadcomplaintsfile,custbyid} from "@/services/complaint/QualitativeComplaint.js";
export default {
    name:'PersonalCharacterization',
    props:[],
    data() {
        return {
            text:'收起',
            text1:'收起',
            isFileType:false,
            statusbar:'',
            fileList7:[],
            complaintdata:{},
            qualitative:{
                verdict:'0',
                verdictRemark:'',
                verdictFileName:'',
                verdictFilePath:'',
            },
        }
    },
    mounted(){
        custbyid(this.$route.query.id).then(res=>{
            if(res.code){
                this.complaintdata=JSON.parse(JSON.stringify(res.data)) 
                this.qualitative = res.data
            }
        })
    },
    methods:{
        deletefile(){
            this.complaintdata.verdictFilePath=null
            this.qualitative.verdictFilePath=null
        },
        filedown(path){         
            if(path){
                window.location.href = path
            }
        },
        handleok(){
            if(!this.qualitative.verdict){
                this.$message.error('请选择评审结果')
                return
            }
            let params = this.qualitative
            params.id = this.$route.query.id
            params.verdict=Number(this.qualitative.verdict)
            complaintsqualitative(params).then(res=>{
                if(res.code){
                    this.$message.success('定性成功')
                    localStorage.setItem('tabkey',this.$route.query.tabkey)
                    this.$router.push({path:'QualitativeComplaint',query:{} })
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        back(){
            localStorage.setItem('tabkey',this.$route.query.tabkey)
            this.$router.push({path:'QualitativeComplaint',query:{} })
        },
        CollapseList(val){
            if(val.length){
                this.text = '收起'
            }else{
                this.text = '展开'
            }
        },
        CollapseList1(val){
            if(val.length){
                this.text1 = '收起'
            }else{
                this.text1 = '展开'
            }
        },
        beforeUpload7(file){
        this.isFileType = file.name.toLowerCase().indexOf('.zip') != -1 || file.name.toLowerCase().indexOf('.rar') != -1||
        file.name.toLowerCase().indexOf('.jpg') != -1 || file.name.toLowerCase().indexOf('.bmp') != -1||
        file.name.toLowerCase().indexOf('.png') != -1 || file.name.toLowerCase().indexOf('.7z') != -1
            if (!this.isFileType) {
            this.$message.error('附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式，文件大小不超过10M');
            return false
            }
        },
        file7up(){
            this.$refs.fileRef7.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent('click'))
        },
        handleChange7({ fileList },data) { 
         if( this.isFileType){
            this.fileList7 = fileList;
        }
        if(fileList.length == 0){
            this.qualitative.verdictFileName = ''
            this.qualitative.verdictFilePath = ''
          }
        },
        async httpRequest7(data,type) {
            const formData = new FormData();
            formData.append("file", data.file);
            await uploadcomplaintsfile(formData).then(res => {
                if (res.code == 1) {
                    data.onSuccess(res.data);
                    this.qualitative.verdictFileName = data.file.name
                    this.qualitative.verdictFilePath = res.data
                } else {
                    this.$message.error(res.message)
                }
            })
            },
    },
    created(){
        this.statusbar=this.$route.query.statusbar
    }
}
</script>
<style lang="less" scoped>
/deep/.require{ 
    .ant-form-item-label label{
        color:red !important;
    }
}
.questiontext{
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    height: 150px;
    overflow: auto;
    background: whitesmoke;
}
.PersonalCharacterization{
    padding: 10px;
    overflow: auto;
    height: 821px;
    background-color: white;
    border: 1px solid #e8e8e8;
    &::-webkit-scrollbar {
        width: 6px; 
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        background: #ff9900;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #ffffff;
      }
    /deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
        right: 16px !important;
        left: auto;
    }
    /deep/.ant-radio-disabled .ant-radio-inner::after {
        background-color: #ff9900;
    }
    /deep/.ant-radio-disabled .ant-radio-inner {
        background-color: #ffffff;
        border-color: #d9d9d9  !important;
        cursor: not-allowed;
    }
    /deep/.ant-radio-disabled + span {
        color: black;
        cursor: not-allowed;
    }
    /deep/.ant-radio-checked .ant-radio-inner{
        border-color: #ff9900  !important;
    }
    /deep/.ant-divider-horizontal {
        display: block;
        clear: both;
        width: 100%;
        min-width: 100%;
        height: 1px;
        margin: 13px 0;
    }
    /deep/.ant-form-item{
        margin-bottom: 0;
    }
}

</style>