import { request, METHOD } from "@/utils/request";

//报价审批列表数据
export async function offerratifypagelist(params) {
  return request("/api/app/offer-ratify/offer-ratify-page-list", METHOD.GET, params);
}
//报价审批
export async function orderofferratify(params) {
  return request("/api/app/offer-ratify/order-offer-ratify", METHOD.POST, params);
}
//报价审批退回
export async function orderofferratifyback(params) {
  return request("/api/app/offer-ratify/order-offer-ratify-back", METHOD.POST, params);
}
