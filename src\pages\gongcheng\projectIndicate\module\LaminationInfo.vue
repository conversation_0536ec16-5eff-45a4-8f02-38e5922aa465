<template>
  <a-spin :spinning="spinning">
    <div class="box">
      <div style="border-bottom: 1px solid #ddd" v-if="ttype == 'mkt'" class="laminationstyle">
        <a-table
          :columns="columns1"
          :dataSource="proOrderStackUpImpDto.stackUpOutputs"
          :pagination="false"
          :scroll="{ y: 437, x: 1520 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :row-class-name="addClass"
          class="Tab1"
          bordered
        >
          <span slot="stackUpCoreDS_" slot-scope="record">
            <a-checkbox v-if="['Core', 'GB', '补强', '金属基'].includes(record.stackUpMTR_)" v-model="record.stackUpCoreDS_" disabled />
          </span>
        </a-table>
      </div>
      <!-- <div style="border-bottom: 1px solid #ddd;border-top: 1px solid #ddd;height: 30px;">
        <span style="margin-left: 15px;font-size: 12px;color: #0e2d5f;line-height: 30px;">
          阻抗信息 
          <a-tooltip slot="extra" placement="topLeft" title="点击增加阻抗信息" arrow-point-at-center>
            <a-icon  type="plus" @click="addcilck"/>
          </a-tooltip>
        </span>
      </div> -->
      <div style="border-bottom: 1px solid #ddd; width: 99.8%" ref="SelectBox">
        <a-table
          :columns="columns"
          :data-source="proOrderStackUpImpDto.stackIMPOutputs"
          bordered
          :scroll="{ y: heighty, x: 1520 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          class="Tab2"
          ref="impedanceTab"
          @keydown.native="handleTableKeyDown"
          :pagination="false"
          :rowClassName="isRedRow"
          :customRow="onClickRow"
        >
          <span slot="imp_TrueValue_" slot-scope="record" style="display: block; width: 100%; height: 100%">
            <div style="width: 100%; height: 100%" :style="record | styleFilter" :title="record.imp_TrueValue_">
              {{ record.imp_TrueValue_ }}
            </div>
          </span>
          <!-- 阻抗类型 -->
          <span slot="imp_type_PRC" slot-scope="record, text, index">
            <a-select
              v-if="editFlg1 && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_Type_"
              :filter-option="filterOption"
              @change="impTypeChange(record, index)"
            >
              <a-select-option v-for="item in seleDataZk" :title="item.text" :value="item.valueMember" :key="item.valueMember">
                {{ item.text }}
              </a-select-option>
            </a-select>
            <span v-else>{{ record.imp_type_PRC }}</span>
          </span>
          <!-- 控制层    -->
          <span slot="imp_ControlLay_" slot-scope="record, text, index">
            <a-select
              v-if="editFlg1 && record.imp_Type_ != '' && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_ControlLay_"
              :filter-option="filterOption"
              @change="impControlLayChange(record, index)"
            >
              <a-select-option v-for="item in controlLayers(record)" :value="item.value" :key="item.value">
                {{ item.name }}
              </a-select-option>
            </a-select>
            <span v-else>{{ record.imp_ControlLay_ }}</span>
          </span>
          <!-- 上参 -->
          <span slot="imp_UpLay_" slot-scope="record, text, index">
            <a-select
              v-if="editFlg1 && record.imp_Type_ != '' && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_UpLay_"
              :filter-option="filterOption"
            >
              <a-select-option v-for="item in upLayFilter(record, index)" :value="item.value" :key="item.value">
                {{ item.name }}
              </a-select-option>
            </a-select>
            <span v-else>{{ record.imp_UpLay_ }}</span>
          </span>
          <!-- 下参 -->
          <span slot="imp_DownLay_" slot-scope="record, text, index">
            <a-select
              v-if="editFlg1 && record.imp_Type_ != '' && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_DownLay_"
              :filter-option="filterOption"
            >
              <a-select-option v-for="item in downLayFilter(record, index)" :value="item.value" :key="item.value">
                {{ item.name }}
              </a-select-option>
            </a-select>
            <span v-else>{{ record.imp_DownLay_ }}</span>
          </span>
          <!-- 线宽 -->
          <span slot="imp_LineWidth_" slot-scope="record, text, index">
            <a-input
              v-if="editFlg1 && record.imp_Type_ != '' && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_LineWidth_"
              @change="lwchange(record, index)"
              @click="selectall($event.target)"
            >
            </a-input>
            <span v-else>{{ record.imp_LineWidth_ }}</span>
          </span>
          <!-- 线隙 -->
          <span slot="imp_LineSpace_" slot-scope="record, text, index">
            <a-input
              v-if="editFlg1 && record.imp_Type_ != '' && (typeFilter(record) == '3' || typeFilter(record) == '1') && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_LineSpace_"
              @change="lschange(record, index)"
              @click="selectall($event.target)"
            >
            </a-input>
            <span v-else>{{ record.imp_LineSpace_ }}</span>
          </span>
          <!-- 线铜 -->
          <span slot="imp_LineCuSpace_" slot-scope="record, text, index">
            <a-input
              v-if="editFlg1 && record.imp_Type_ != '' && (typeFilter(record) == '1' || typeFilter(record) == '2') && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_LineCuSpace_"
              @change="lcchange(record, index)"
              @click="selectall($event.target)"
            >
            </a-input>
            <span v-else>{{ record.imp_LineCuSpace_ }}</span>
          </span>
          <!-- 要求 -->
          <span slot="imp_Value_Req_" slot-scope="record">
            <a-input
              v-if="editFlg1 && record.imp_Type_ != '' && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_Value_Req_"
              @change="reqChange(record)"
              @click="selectall($event.target)"
            >
            </a-input>
            <span v-else>{{ record.imp_Value_Req_ }}</span>
          </span>
          <!-- 公差 -->
          <span slot="imp_Value_Tol_" slot-scope="record">
            <a-input
              v-if="editFlg1 && record.imp_Type_ != '' && !record.imp_TrueValue_"
              showSearch
              allowClear
              v-model="record.imp_Value_Tol_"
              @click="selectall($event.target)"
            >
            </a-input>
            <span v-else>{{ record.imp_Value_Tol_ }}</span>
          </span>
          <!-- 删除 -->
          <span slot="action" slot-scope="record, text, index">
            <a-tooltip title="删除" v-if="!record.imp_TrueValue_ && index != proOrderStackUpImpDto.stackIMPOutputs.length - 1">
              <a-icon style="margin-right: 5%; color: #0e2d5f" type="close-circle" @click.stop="removeImpedance($event, record, index)" />
            </a-tooltip>
          </span>
        </a-table>
        <a-menu :style="menuStyle1" v-if="menuVisible1 && editFlg1" class="tabRightClikBox">
          <a-menu-item @click="manycilck">多层复制</a-menu-item>
          <a-menu-item @click="singlecilck">单层复制</a-menu-item>
        </a-menu>
      </div>
      <div class="bto"></div>
      <a-modal
        title="阻抗信息复制"
        :visible="copydataVisible"
        @cancel="reportHandleCancel"
        @ok="copyhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div v-if="copytype == '多层'">
          <a-select
            v-model="CopyLayers"
            mode="multiple"
            showSearch
            allowClear
            optionFilterProp="label"
            style="width: 75%"
            placeholder="请选择需要复制的层"
          >
            <a-select-option v-for="item in allLayers()" :key="item.value" :value="item.value" :label="item.lable">
              {{ item.lable }}
            </a-select-option>
          </a-select>
          <a-button type="primary" style="position: relative; left: 15px" @click="allclik">全选</a-button>
        </div>
        <div v-else>
          <a-input v-model="CopyLayers1" placeholder="请输入当前选择行需要进行复制的行数"></a-input>
        </div>
      </a-modal>
      <a-modal
        title="阻抗信息添加"
        :visible="impdataVisible"
        @cancel="reportHandleCancel"
        @ok="imphandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form>
          <a-form-item label="阻抗类型" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-select showSearch allowClear v-model="impdata.imp_Type_" :filter-option="filterOption" @change="impTypeChange">
              <a-select-option v-for="item in seleDataZk" :title="item.text" :value="item.valueMember" :key="item.valueMember">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="控制层" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-select showSearch allowClear v-model="impdata.imp_ControlLay_" :filter-option="filterOption" @change="impControlLayChange">
              <a-select-option v-for="item in controlLayers()" :value="item.value" :key="item.value">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="上参" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-select v-model="impdata.imp_UpLay_" showSearch allowClear :filter-option="filterOption">
              <a-select-option v-for="item in upLayFilter()" :value="item.value" :key="item.value">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="下参" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-select v-model="impdata.imp_DownLay_" showSearch allowClear :filter-option="filterOption">
              <a-select-option v-for="item in downLayFilter()" :value="item.value" :key="item.value">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="线宽" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-input v-model="impdata.imp_LineWidth_" />
          </a-form-item>
          <a-form-item label="线隙" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-input v-model="impdata.imp_LineSpace_" />
          </a-form-item>
          <a-form-item label="线铜" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-input v-model="impdata.imp_LineCuSpace_" />
          </a-form-item>
          <a-form-item label="要求" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-input v-model="impdata.imp_Value_Req_" @change="reqChange(impdata.imp_Value_Req_)" />
          </a-form-item>
          <a-form-item label="公差" :labelCol="{ span: 7 }" :wrapperCol="{ span: 14 }">
            <a-input v-model="impdata.imp_Value_Tol_" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
const columns = [
  {
    title: "",
    dataIndex: "index",
    width: 20,
    ellipsis: true,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "阻抗类型",
    scopedSlots: { customRender: "imp_type_PRC" },
    width: 120,
    ellipsis: true,
    align: "left",
  },
  {
    title: "控制层",
    key: "imp_ControlLay_",
    width: 70,
    ellipsis: true,
    scopedSlots: { customRender: "imp_ControlLay_" },
    align: "center",
    onFilter: (value, record) => record.imp_ControlLay_.indexOf(value) === 0,
  },
  {
    title: "上参",
    width: 40,
    align: "center",
    ellipsis: true,
    key: "imp_UpLay_",
    scopedSlots: { customRender: "imp_UpLay_" },
  },
  {
    title: "下参",
    width: 40,
    key: "imp_DownLay_",
    ellipsis: true,
    scopedSlots: { customRender: "imp_DownLay_" },
    align: "center",
  },

  {
    title: "线宽",
    key: "imp_LineWidth_",
    scopedSlots: { customRender: "imp_LineWidth_" },
    align: "center",
    ellipsis: true,
    width: 60,
    onFilter: (value, record) => record.imp_LineWidth_.indexOf(value) === 0,
  },
  {
    title: "线隙",
    key: "imp_LineSpace_",
    scopedSlots: { customRender: "imp_LineSpace_" },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "线铜",
    key: "imp_LineCuSpace_",
    scopedSlots: { customRender: "imp_LineCuSpace_" },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "要求",
    key: "imp_Value_Req_",
    scopedSlots: { customRender: "imp_Value_Req_" },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "公差",
    width: 35,
    key: "imp_Value_Tol_",
    scopedSlots: { customRender: "imp_Value_Tol_" },
    align: "center",
    ellipsis: true,
  },
  {
    title: "L/W",
    dataIndex: "imp_OKLineWidth_",
    key: "imp_OKLineWidth_",
    // scopedSlots: { customRender: 'imp_OKLineWidth_' },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "L/S",
    dataIndex: "imp_OKLineSpace_",
    // key: 'imp_OKLineSpace_',
    // scopedSlots: { customRender: 'imp_OKLineSpace_' },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "L/Cu",
    dataIndex: "imp_OKLineCuSpace_",
    width: 35,
    key: "imp_OKLineCuSpace_",
    align: "center",
    ellipsis: true,
    // scopedSlots: { customRender: 'imp_OKLineCuSpace_' },
  },
  {
    title: "Imp(Eth)",
    dataIndex: "imp_TrueValueWithOutSM_",
    align: "center",
    ellipsis: true,
    width: 60,
  },
  {
    title: "Imp",
    key: "imp_TrueValue_",
    scopedSlots: { customRender: "imp_TrueValue_" },
    align: "center",
    ellipsis: true,
    width: 40,
  },
  // {
  //   title: "H1",
  //   dataIndex: "imp_H1_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 50
  // },
  // {
  //   title: "Er1",
  //   dataIndex: "imp_Er1_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 40
  // },
  // {
  //   title: "H2",
  //   dataIndex: "imp_H2_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 35
  // },

  // {
  //   title: "Er2",
  //   dataIndex: "imp_Er2_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 40
  // },
  // {
  //   title: "W1",
  //   dataIndex: "imp_W1_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 40
  // },
  // {
  //   title: "W2",
  //   dataIndex: "imp_W2_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 35
  // },
  // {
  //   title: "S1",
  //   dataIndex: "imp_S1_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 35
  // },
  // {
  //   title: "D1",
  //   dataIndex: "imp_D1_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 35
  // },
  // {
  //   title: "T1",
  //   dataIndex: "imp_T1_",
  //   align:'center',
  //   ellipsis: true,
  //   width:35
  // },
  // {
  //   title: "C1",
  //   dataIndex: "imp_C1_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 35
  // },
  // {
  //   title: "C2",
  //   dataIndex: "imp_C2_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 35
  // },
  // {
  //   title: "C3",
  //   dataIndex: "imp_C3_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 35
  // },
  // {
  //   title: "CEr_",
  //   dataIndex: "imp_CEr_",
  //   align:'center',
  //   ellipsis: true,
  //   width:35
  // },
  // {
  //   title: "Imp-H1",
  //   dataIndex: "imp_H1IncludeLyrNoList_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 50,
  // },
  // {
  //   title: "Imp-H2",
  //   dataIndex: "imp_H2IncludeLyrNoList_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 50,
  // },
  // {
  //   title: "补偿",
  //   key: "imp_BC_",
  //   dataIndex: "imp_BC_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 50,
  // },
];
const columns1 = [
  {
    dataIndex: "index",
    key: "index",
    width: 40,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    className: "index_",
  },
  {
    title: "层号",
    dataIndex: "stackUpLayerNo_",
    width: 55,
    align: "center",
    className: "material_bg",
  },
  {
    title: "物料",
    dataIndex: "stackUpMTR_",
    width: 65,
    align: "center",
    className: "material_bg",
  },
  {
    title: "型号",
    dataIndex: "stackUpMTRType_",
    width: 115,
    align: "center",
    className: "material_bg",
  },
  {
    title: "介质",
    dataIndex: "stackUpMTRFoil_",
    width: 200,
    align: "center",
    ellipsis: true,
    className: "material_bg",
  },
  {
    title: "含铜",
    width: 55,
    className: "M_bg",
    scopedSlots: { customRender: "stackUpCoreDS_" },
    align: "center",
  },
  {
    title: "物料厚度(mil)",
    dataIndex: "stackUpThichnessORG_",
    width: 100,
    align: "center",
    className: "M_bg",
  },
  {
    title: "成品厚度(mil)",
    dataIndex: "stackUpThichnessMIL_",
    width: 100,
    align: "center",
    className: "M_bg",
  },
  {
    title: "成品厚度(mm)",
    dataIndex: "stackUpThichnessMM_",
    width: 100,
    align: "center",
    className: "M_bg",
  },
  {
    title: "残铜率",
    dataIndex: "stackUpCTLMI_",
    width: 70,
    className: "M_bg",
    align: "center",
  },
  {
    title: "T/B",
    dataIndex: "stackUpCUTB_",
    width: 55,
    align: "center",
  },
  {
    title: "DK",
    dataIndex: "stackUpDK_",
    width: 90,
    className: "M_bg",
    align: "center",
  },
  {
    title: "大料尺寸",
    dataIndex: "stackUpPNLSize_",
    width: 90,
    className: "M_bg",
    align: "center",
  },
  {
    title: "T1",
    dataIndex: "stackUpT1_",
    width: 55,
    align: "center",
  },
];
import { seleZK, creatstackimp } from "@/services/impedance";
import Cookie from "js-cookie";
import $ from "jquery";
import { param } from "jquery";
export default {
  name: "LaminationInfo",
  props: ["proOrderStackUpImpDto", "selectData", "editFlg1", "boardBrandList", "proOrderInfoDto", "ttype"],
  data() {
    return {
      spinning: false,
      dianji: "",
      heighty: 732,
      CopyLayers: [],
      CopyLayers1: "",
      copytype: "",
      seleDataZk: [], // 阻抗类型列表
      impdataVisible: false,
      copydataVisible: false,
      rowdata: {},
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      currentCell: null,
      menuVisible1: false,
      impdata: {
        imp_LineWidth_: "",
        imp_LineSpace_: "",
        imp_LineCuSpace_: "",
        imp_Value_Req_: "",
        imp_Value_Tol_: "",
        imp_Type_: "",
        imp_ControlLay_: "",
        imp_UpLay_: "",
        imp_DownLay_: "",
      },
      ide: "",
      columns,
      columns1,
      dataSource1: [],
      dataSource2: [],
      dataSource3: [],
      impedanceTabData: [],
    };
  },
  filters: {
    styleFilter(val) {
      if (val.imp_ValueColor_ == "2") {
        return {
          background: "red",
          "line-height": "35px",
        };
      }
      if (val.imp_ValueColor_ == "1") {
        return {
          background: "green",
          "line-height": "35px",
        };
      }
    },
  },
  mounted() {
    window.addEventListener("click", this.keyclick);
    window.addEventListener("resize", this.handleResize, true);
    this.heighty = this.ttype == "pro" ? 732 : 210;
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  created() {
    this.$nextTick(() => {
      if (this.columns.length == 16) {
        this.columns.splice(15, 1);
      }
      console.log("created", this.proOrderStackUpImpDto.stackIMPOutputs);
      if (this.proOrderStackUpImpDto.stackIMPOutputs == null) {
        this.proOrderStackUpImpDto.stackIMPOutputs = [];
      }
      if (this.proOrderStackUpImpDto.stackIMPOutputs.length) {
        this.getfilters();
      }
      //this.handleResize()
    });
    this.getSeleDataZk();
  },
  methods: {
    allclik() {
      this.CopyLayers = [];
      let all = this.allLayers();
      all.forEach(ite => {
        this.CopyLayers.push(ite.value);
      });
    },
    selectall(target) {
      target.select();
    },
    keyclick() {
      this.dianji = true;
    },
    getCurrentCellIndex() {
      const table = this.$refs.impedanceTab.$el;
      const cells = table.querySelectorAll("td");
      if (!this.currentCell || this.dianji) {
        this.currentCell = this.$refs.impedanceTab.$el.querySelector(":focus").closest("td");
      }
      return this.currentCell ? Array.from(cells).indexOf(this.currentCell) : 0;
    },
    handleTableKeyDown(event) {
      if (event.key == "ArrowDown" || event.key == "ArrowUp" || event.key == "ArrowRight" || event.key == "ArrowLeft") {
        const table = this.$refs.impedanceTab.$el;
        const cells = table.querySelectorAll("td"); //当前表格所有单元格
        setTimeout(() => {
          this.dianji = false;
        }, 500);
        const key = event.key;
        const cellIndex = this.getCurrentCellIndex(); //当前行坐标位置
        const numCols = this.columns.length; //列数
        const numRows = Math.ceil(cells.length / numCols); // 计算总行数
        if (key === "ArrowDown" && cellIndex < cells.length - numCols) {
          event.preventDefault();
          let nextRowIndex = Math.floor((cellIndex + numCols) / numCols);
          while (nextRowIndex < numRows) {
            const nextIndex = nextRowIndex * numCols + (cellIndex % numCols); //下一行×列数为起始位置index加偏移位置index获取下一行具体位置index
            const nextCell = cells[nextIndex]; //获取下一行具体位置
            if (nextCell.querySelector("input") || nextCell.querySelector("div")) {
              this.currentCell = nextCell;
              break;
            } else {
              nextRowIndex++;
            }
          }
        } else if (key === "ArrowUp" && cellIndex >= numCols) {
          event.preventDefault();
          let prevRowIndex = Math.floor((cellIndex - numCols) / numCols);
          while (prevRowIndex >= 0) {
            const prevIndex = prevRowIndex * numCols + (cellIndex % numCols);
            const prevCell = cells[prevIndex];

            if (prevCell.querySelector("input") || prevCell.querySelector("div")) {
              this.currentCell = prevCell;
              break;
            } else {
              prevRowIndex--;
            }
          }
        } else if (key === "ArrowRight") {
          event.preventDefault();
          if (this.currentCell) {
            let nextIndex = cellIndex + 1;
            while (nextIndex <= cells.length) {
              if (nextIndex % numCols === 0) {
                nextIndex = cellIndex - (cellIndex % numCols) + 1;
              }
              this.currentCell = cells[nextIndex];
              if (this.currentCell.querySelector("input") || this.currentCell.querySelector("div")) {
                // 找到具有 input 或 div 的单元格，跳出循环
                break;
              } else {
                nextIndex++;
              }
            }
          }
        } else if (key === "ArrowLeft") {
          event.preventDefault();
          if (this.currentCell) {
            let prevIndex = cellIndex - 1;
            while (prevIndex < cells.length) {
              if (prevIndex % numCols === numCols - 1) {
                prevIndex = cellIndex - (cellIndex % numCols) + 30;
              }
              this.currentCell = cells[prevIndex];
              if (this.currentCell.querySelector("input") || this.currentCell.querySelector("div")) {
                break;
              } else {
                prevIndex--;
              }
            }
          }
        }
        if (this.currentCell) {
          const input = this.currentCell.querySelector("input");
          const div = this.currentCell.querySelector("div");
          if (input) {
            input.focus();
            input.select();
          }
          if (div) {
            div.focus();
          }
        }
      }
    },
    addaction() {
      if (this.columns.length < 16) {
        this.columns.splice(15, 0, {
          title: "操作",
          key: "action",
          width: 30,
          align: "center",
          scopedSlots: { customRender: "action" },
        });
      }
    },
    delaction() {
      this.columns.splice(15, 1);
    },
    typeFilter(record) {
      if (record.imp_Type_.indexOf("_D") > 0 && record.imp_Type_.indexOf("_C") > 0) {
        return "1";
      } else if (record.imp_Type_.indexOf("_C") > 0 && record.imp_Type_.indexOf("_D") < 0) {
        return "2";
      } else if (record.imp_Type_.indexOf("_C") < 0 && record.imp_Type_.indexOf("_D") > 0) {
        return "3";
      } else {
        return "4";
      }
    },
    lwchange(record, index) {
      if (record.imp_LineWidth_) {
        this.proOrderStackUpImpDto.stackIMPOutputs[index].imp_OKLineWidth_ = record.imp_LineWidth_;
      }
      this.getfilters();
      this.$forceUpdate();
    },
    lschange(record, index) {
      if (record.imp_LineSpace_) {
        this.proOrderStackUpImpDto.stackIMPOutputs[index].imp_OKLineSpace_ = record.imp_LineSpace_;
      }
    },
    lcchange(record, index) {
      if (record.imp_LineCuSpace_) {
        this.proOrderStackUpImpDto.stackIMPOutputs[index].imp_OKLineCuSpace_ = record.imp_LineCuSpace_;
      }
    },
    getfilters() {
      const controlLayerParams = [];
      const linewidthParams = [];
      this.proOrderStackUpImpDto.stackIMPOutputs.forEach(item => {
        if (item.imp_ControlLay_) {
          controlLayerParams.push(item.imp_ControlLay_);
        }
        if (item.imp_LineWidth_) {
          linewidthParams.push(item.imp_LineWidth_);
        }
      });
      const uniqueParams = [...new Set(controlLayerParams)];
      const filters = uniqueParams.map(param => ({
        text: param,
        value: param,
      }));
      const lineParams = [...new Set(linewidthParams)];
      const filters1 = lineParams.map(param => ({
        text: param,
        value: param,
      }));
      this.columns[2].filters = filters;
      this.columns[5].filters = filters1;
    },
    onClickRow(record, index) {
      return {
        on: {
          click: event => {
            this.ide = index + 1;
            this.rowdata = record;
          },
          contextmenu: e => {
            e.preventDefault();
            if (this.ide) {
              this.rightClick1(e, record, this.ide);
            }
          },
        },
      };
    },
    isRedRow(record, index) {
      let strGroup = [];
      if (this.ide && this.ide == index + 1) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    rightClick1(e, record, ide) {
      let event = e.target;
      this.rowIndex = event.parentNode.rowIndex;
      this.menuVisible1 = true;
      if (this.ttype == "pro") {
        this.menuStyle1.top = e.clientY - 100 + "px";
        this.menuStyle1.left = e.clientX - 350 + "px";
      } else if (this.ttype == "mkt") {
        this.menuStyle1.top = e.clientY - 210 + "px";
        this.menuStyle1.left = e.clientX - 220 + "px";
      }

      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    impTypeChange(record, index) {
      for (var i in this.proOrderStackUpImpDto.stackIMPOutputs[index]) {
        let _obj = this.proOrderStackUpImpDto.stackIMPOutputs[index];
        if (i != "imp_Type_" && i != "imp_type_PRC") {
          _obj[i] = "";
          this.proOrderStackUpImpDto.stackIMPOutputs[index] = _obj;
        }
      }
      if (record.imp_Type_ && this.proOrderStackUpImpDto.stackIMPOutputs[this.proOrderStackUpImpDto.stackIMPOutputs.length - 1].imp_Type_) {
        this.proOrderStackUpImpDto.stackIMPOutputs.splice(this.proOrderStackUpImpDto.stackIMPOutputs.length, 0, {
          imp_LineWidth_: "",
          imp_LineSpace_: "",
          imp_LineCuSpace_: "",
          imp_Value_Req_: "",
          imp_Value_Tol_: "",
          imp_Type_: "",
          imp_ControlLay_: "",
          imp_UpLay_: "",
          imp_DownLay_: "",
          imp_OKLineWidth_: "",
          imp_type_PRC: "",
          imp_OKLineSpace_: "",
          imp_OKLineCuSpace_: "",
        });
      } else {
        if (index != this.proOrderStackUpImpDto.stackIMPOutputs.length - 1 && !this.proOrderStackUpImpDto.stackIMPOutputs[index].imp_Type_) {
          this.proOrderStackUpImpDto.stackIMPOutputs.splice(index, 1);
        }
      }
    },
    impControlLayChange(record, index) {
      let uplist = this.upLayFilter(record, index);
      let downlist = this.downLayFilter(record, index);
      if (record.imp_ControlLay_ && uplist.length) {
        record.imp_UpLay_ = uplist[uplist.length - 1].name;
      } else {
        record.imp_UpLay_ = "";
      }
      if (record.imp_ControlLay_ && downlist.length) {
        record.imp_DownLay_ = downlist[0].name;
      } else {
        record.imp_DownLay_ = "";
      }
      record.imp_LineWidth_ = "";
      record.imp_LineSpace_ = "";
      record.imp_LineCuSpace_ = "";
      record.imp_Value_Req_ = "";
      record.imp_Value_Tol_ = "";
      record.imp_OKLineWidth_ = "";
      record.imp_OKLineSpace_ = "";
      record.imp_OKLineCuSpace_ = "";
      record.imp_TrueValue_ = "";
      record.imp_TrueValueWithOutSM_ = "";
      this.getfilters();
      this.$forceUpdate();
    },
    controlLayers(record) {
      let count = this.proOrderInfoDto.boardLayers;
      let arr = [];
      if (record && record.imp_Type_) {
        if (record.imp_Type_.split("_")[0] == "I") {
          for (var i = 2; i < count; i++) {
            arr.push({
              name: "L" + i,
              value: "L" + i,
            });
          }
          return arr;
        } else if (record.imp_Type_.split("_")[0] == "O") {
          return [
            { name: "L1", value: "L1" },
            { name: "L" + count, value: "L" + count },
          ];
        }
      }
    },
    upLayFilter(record) {
      if (record && record.imp_ControlLay_) {
        const count = record.imp_ControlLay_.match(/\d+$/gi)[0],
          arr = [];
        for (var i = 1; i < count; i++) {
          arr.push({
            name: "L" + i,
            value: "L" + i,
          });
        }
        return arr;
      }
    },
    downLayFilter(record) {
      if (record && record.imp_ControlLay_) {
        const count = record.imp_ControlLay_.match(/\d+$/gi)[0],
          arr = [];
        let edg = Number(this.proOrderInfoDto.boardLayers) + 1;
        for (var i = Number(count) + 1; i < edg; i++) {
          arr.push({
            name: "L" + i,
            value: "L" + i,
          });
        }
        return arr;
      }
    },
    allLayers() {
      let count = this.proOrderInfoDto.boardLayers;
      let arr = [];
      for (var i = 1; i <= count; i++) {
        if (this.rowdata.imp_ControlLay_ && i != Number(this.rowdata.imp_ControlLay_.split("L")[1])) {
          arr.push({
            lable: "L" + i,
            value: "L" + i,
          });
        }
      }
      return arr;
    },
    removeImpedance(e, record, index) {
      e.preventDefault();
      if (index != this.proOrderStackUpImpDto.stackIMPOutputs.length - 1) {
        const index_ = this.proOrderStackUpImpDto.stackIMPOutputs.indexOf(record);
        this.proOrderStackUpImpDto.stackIMPOutputs.splice(index_, 1);
      } else {
        this.$message.error("该条数据不能删除");
      }
    },
    reqChange(record) {
      if (Number(record.imp_Value_Req_) < 50) {
        record.imp_Value_Tol_ = 5 + "";
      } else {
        record.imp_Value_Tol_ = record.imp_Value_Req_ * 0.1 + "";
      }
      this.$forceUpdate();
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    getSeleDataZk() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("seleDataZk"));
      if (data && token && data.token == token) {
        this.seleDataZk = data.data;
      } else {
        seleZK().then(res => {
          if (res.code) {
            this.seleDataZk = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              localStorage.setItem("seleDataZk", JSON.stringify({ data: this.seleDataZk, token }));
            }
          }
        });
      }
    },
    addcilck() {
      this.impdataVisible = true;
      this.impdata = {
        imp_ControlLay_: "",
        imp_UpLay_: "",
        imp_DownLay_: "",
        imp_LineWidth_: "",
        imp_LineSpace_: "",
        imp_LineCuSpace_: "",
        imp_Value_Req_: "",
        imp_Value_Tol_: "",
        imp_Type_: "",
        imp_OKLineWidth_: "",
        imp_OKLineSpace_: "",
        imp_OKLineCuSpace_: "",
      };
    },
    manycilck() {
      this.CopyLayers = [];
      if (!this.editFlg1) {
        this.$message.warning("非编辑状态不允许进行复制");
        return;
      }
      this.copytype = "多层";
      this.copydataVisible = true;
    },
    singlecilck() {
      this.CopyLayers1 = "";
      if (!this.editFlg1) {
        this.$message.warning("非编辑状态不允许进行复制");
        return;
      }
      this.copytype = "单层";
      this.copydataVisible = true;
    },
    reportHandleCancel() {
      this.impdataVisible = false;
      this.copydataVisible = false;
    },
    imphandleOk(type) {
      let param = {
        id: this.$route.query.id,
      };
      if (this.ttype == "mkt") {
        param.orderNo = this.$route.query.orderNo;
      } else if (this.ttype == "pro") {
        param.orderNo = this.$route.query.OrderNo;
      }
      param["stackIMP"] = [...this.proOrderStackUpImpDto.stackIMPOutputs];
      if (type != "copy") {
        param["stackIMP"].splice(param["stackIMP"].length - 1, 1);
      }
      for (let index = 0; index < param["stackIMP"].length; index++) {
        if (!param["stackIMP"][index].imp_TrueValue_) {
          param["stackIMP"][index].imp_SetInStackup_ = 0;
        }
      }
      creatstackimp(param).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          this.impdataVisible = false;
          this.$nextTick(() => {
            this.handleResize();
          });
        } else {
          this.$message.error(res.message);
        }
        this.$emit("GetProOrderInfo");
      });
    },
    copyhandleOk() {
      let half = this.proOrderInfoDto.boardLayers / 2;
      let upinterval, downinterval, imptype;
      if (this.rowdata.imp_UpLay_) {
        upinterval = Number(this.rowdata.imp_ControlLay_.split("L")[1] - this.rowdata.imp_UpLay_.split("L")[1]);
      }
      if (this.rowdata.imp_DownLay_) {
        downinterval = Number(this.rowdata.imp_DownLay_.split("L")[1] - this.rowdata.imp_ControlLay_.split("L")[1]);
      }
      if (this.rowdata.imp_Type_.includes("O_")) {
        imptype = "outside";
      } else if (this.rowdata.imp_Type_.includes("I_")) {
        imptype = "inner";
      }
      let data = this.proOrderStackUpImpDto.stackIMPOutputs;
      let zkvalue = this.seleDataZk.filter(ite => ite.valueMember == this.rowdata.imp_Type_)[0].valueMember1;
      let zk, zktype;
      if (zkvalue) {
        zk = this.seleDataZk.filter(ite => ite.valueMember == zkvalue)[0].text;
        zktype = this.seleDataZk.filter(ite => ite.valueMember == zkvalue)[0].valueMember;
      } else {
        zk = this.rowdata.imp_type_PRC;
        zktype = this.rowdata.imp_Type_;
      }
      if (this.copytype == "多层") {
        this.CopyLayers.sort((a, b) => {
          const numA = parseInt(a.substring(1));
          const numB = parseInt(b.substring(1));
          return numA - numB;
        });
        for (let index = 0; index < this.CopyLayers.length; index++) {
          if (imptype == "outside") {
            if (this.CopyLayers[index] == "L" + this.proOrderInfoDto.boardLayers) {
              data.splice(data.length - 1, 0, {
                imp_ControlLay_: this.CopyLayers[index], //控制层
                imp_type_PRC: this.rowdata.imp_type_PRC, //阻抗类型中文
                imp_Type_: this.rowdata.imp_Type_, //阻抗类型英文
                imp_UpLay_: "L" + (this.proOrderInfoDto.boardLayers - (upinterval ? upinterval : downinterval)), //上参
                imp_LineWidth_: this.rowdata.imp_LineWidth_, //线宽
                imp_LineSpace_: this.rowdata.imp_LineSpace_, //线隙
                imp_LineCuSpace_: this.rowdata.imp_LineCuSpace_, //线铜
                imp_Value_Req_: this.rowdata.imp_Value_Req_, //要求
                imp_Value_Tol_: this.rowdata.imp_Value_Tol_, //公差
                imp_OKLineWidth_: this.rowdata.imp_OKLineWidth_, //L/W
                imp_OKLineSpace_: this.rowdata.imp_OKLineSpace_, //L/S
                imp_OKLineCuSpace_: this.rowdata.imp_OKLineCuSpace_, //L/Cu
              });
            } else if (this.CopyLayers[index] == "L1") {
              data.splice(data.length - 1, 0, {
                imp_ControlLay_: this.CopyLayers[index],
                imp_type_PRC: this.rowdata.imp_type_PRC,
                imp_Type_: this.rowdata.imp_Type_,
                imp_DownLay_: "L" + (1 + (upinterval ? upinterval : downinterval)),
                imp_LineWidth_: this.rowdata.imp_LineWidth_,
                imp_LineSpace_: this.rowdata.imp_LineSpace_,
                imp_LineCuSpace_: this.rowdata.imp_LineCuSpace_,
                imp_Value_Req_: this.rowdata.imp_Value_Req_,
                imp_Value_Tol_: this.rowdata.imp_Value_Tol_,
                imp_OKLineWidth_: this.rowdata.imp_OKLineWidth_,
                imp_OKLineSpace_: this.rowdata.imp_OKLineSpace_,
                imp_OKLineCuSpace_: this.rowdata.imp_OKLineCuSpace_,
              });
            } else {
              let d, u;
              if (this.rowdata.imp_DownLay_) {
                if (this.CopyLayers[index].split("L")[1] < half) {
                  u = "L" + (Number(this.CopyLayers[index].split("L")[1]) - 1);
                  d = "L" + (Number(this.CopyLayers[index].split("L")[1]) + downinterval);
                } else {
                  u = "L" + (Number(this.CopyLayers[index].split("L")[1]) - downinterval);
                  d = "L" + (Number(this.CopyLayers[index].split("L")[1]) + 1);
                }
              } else if (this.rowdata.imp_UpLay_) {
                if (this.CopyLayers[index].split("L")[1] < half) {
                  u = "L" + (Number(this.CopyLayers[index].split("L")[1]) - 1);
                  d = "L" + (Number(this.CopyLayers[index].split("L")[1]) + upinterval);
                } else {
                  u = "L" + (Number(this.CopyLayers[index].split("L")[1]) - upinterval);
                  d = "L" + (Number(this.CopyLayers[index].split("L")[1]) + 1);
                }
              }
              if (Number(d.split("L")[1]) > Number(this.proOrderInfoDto.boardLayers)) {
                d = "";
              }
              if (Number(u.split("L")[1]) < 1) {
                u = "";
              }
              data.splice(data.length - 1, 0, {
                imp_ControlLay_: this.CopyLayers[index],
                imp_type_PRC: zk,
                imp_Type_: zktype,
                imp_DownLay_: d,
                imp_UpLay_: u,
                imp_LineWidth_: this.rowdata.imp_LineWidth_,
                imp_LineSpace_: this.rowdata.imp_LineSpace_,
                imp_LineCuSpace_: this.rowdata.imp_LineCuSpace_,
                imp_Value_Req_: this.rowdata.imp_Value_Req_,
                imp_Value_Tol_: this.rowdata.imp_Value_Tol_,
                imp_OKLineWidth_: this.rowdata.imp_OKLineWidth_,
                imp_OKLineSpace_: this.rowdata.imp_OKLineSpace_,
                imp_OKLineCuSpace_: this.rowdata.imp_OKLineCuSpace_,
              });
            }
          } else if (imptype == "inner") {
            if (this.CopyLayers[index] != "L" + this.proOrderInfoDto.boardLayers && this.CopyLayers[index] != "L1") {
              let up, down;
              if (
                (this.CopyLayers[index].split("L")[1] <= half && this.rowdata.imp_ControlLay_.split("L")[1] <= half) ||
                (this.CopyLayers[index].split("L")[1] > half && this.rowdata.imp_ControlLay_.split("L")[1] > half)
              ) {
                up = upinterval ? "L" + (Number(this.CopyLayers[index].split("L")[1]) - upinterval) : "";
                down = downinterval ? "L" + (Number(this.CopyLayers[index].split("L")[1]) + downinterval) : "";
              } else {
                up = downinterval ? "L" + (Number(this.CopyLayers[index].split("L")[1]) - downinterval) : "";
                down = upinterval ? "L" + (Number(this.CopyLayers[index].split("L")[1]) + upinterval) : "";
              }
              if (Number(down.split("L")[1]) > Number(this.proOrderInfoDto.boardLayers)) {
                down = "";
              }
              if (Number(up.split("L")[1]) < 1) {
                up = "";
              }
              data.splice(data.length - 1, 0, {
                imp_ControlLay_: this.CopyLayers[index],
                imp_type_PRC: this.rowdata.imp_type_PRC,
                imp_Type_: this.rowdata.imp_Type_,
                imp_DownLay_: down,
                imp_UpLay_: up,
                imp_LineWidth_: this.rowdata.imp_LineWidth_,
                imp_LineSpace_: this.rowdata.imp_LineSpace_,
                imp_LineCuSpace_: this.rowdata.imp_LineCuSpace_,
                imp_Value_Req_: this.rowdata.imp_Value_Req_,
                imp_Value_Tol_: this.rowdata.imp_Value_Tol_,
                imp_OKLineWidth_: this.rowdata.imp_OKLineWidth_,
                imp_OKLineSpace_: this.rowdata.imp_OKLineSpace_,
                imp_OKLineCuSpace_: this.rowdata.imp_OKLineCuSpace_,
              });
            } else if (this.CopyLayers[index] == "L1") {
              let down1 = Number(this.CopyLayers[index].split("L")[1]) + (downinterval || upinterval);
              data.splice(data.length - 1, 0, {
                imp_ControlLay_: this.CopyLayers[index],
                imp_type_PRC: zk,
                imp_Type_: zktype,
                imp_DownLay_: down1 ? "L" + down1 : "",
                imp_UpLay_: "",
                imp_LineWidth_: this.rowdata.imp_LineWidth_,
                imp_LineSpace_: this.rowdata.imp_LineSpace_,
                imp_LineCuSpace_: this.rowdata.imp_LineCuSpace_,
                imp_Value_Req_: this.rowdata.imp_Value_Req_,
                imp_Value_Tol_: this.rowdata.imp_Value_Tol_,
                imp_OKLineWidth_: this.rowdata.imp_OKLineWidth_,
                imp_OKLineSpace_: this.rowdata.imp_OKLineSpace_,
                imp_OKLineCuSpace_: this.rowdata.imp_OKLineCuSpace_,
              });
            } else if (this.CopyLayers[index] == "L" + this.proOrderInfoDto.boardLayers) {
              let up1 = Number(this.CopyLayers[index].split("L")[1]) - (upinterval || downinterval);
              data.splice(data.length - 1, 0, {
                imp_ControlLay_: this.CopyLayers[index],
                imp_type_PRC: zk,
                imp_Type_: zktype,
                imp_DownLay_: "",
                imp_UpLay_: up1 ? "L" + up1 : "",
                imp_LineWidth_: this.rowdata.imp_LineWidth_,
                imp_LineSpace_: this.rowdata.imp_LineSpace_,
                imp_LineCuSpace_: this.rowdata.imp_LineCuSpace_,
                imp_Value_Req_: this.rowdata.imp_Value_Req_,
                imp_Value_Tol_: this.rowdata.imp_Value_Tol_,
                imp_OKLineWidth_: this.rowdata.imp_OKLineWidth_,
                imp_OKLineSpace_: this.rowdata.imp_OKLineSpace_,
                imp_OKLineCuSpace_: this.rowdata.imp_OKLineCuSpace_,
              });
            }
          }
        }
      } else {
        for (let index = 0; index < this.CopyLayers1; index++) {
          data.splice(data.length - 1, 0, {
            imp_ControlLay_: this.rowdata.imp_ControlLay_, //控制层
            imp_type_PRC: this.rowdata.imp_type_PRC, //阻抗类型中文
            imp_Type_: this.rowdata.imp_Type_, //阻抗类型英文
            imp_UpLay_: this.rowdata.imp_UpLay_, //上参
            imp_DownLay_: this.rowdata.imp_DownLay_, //下参
          });
        }
      }
      this.getfilters();
      this.copydataVisible = false;
    },
    handleResize() {
      console.log(this.ttype);
      var boxstyle = document.getElementsByClassName("box")[2];
      let tab2 = document.getElementsByClassName("Tab2")[1].children[0].children[0].children[0].children[0].children[0].children[1];
      if (this.ttype == "pro") {
        boxstyle.style.height = window.innerHeight - 140 + "px";
        if (tab2 && this.proOrderStackUpImpDto.stackIMPOutputs.length != 0) {
          tab2.style.height = window.innerHeight - 180 + "px";
        } else {
          tab2.style.height = "0px";
        }
      } else if (this.ttype == "mkt") {
        let tab1 = document.getElementsByClassName("Tab1")[1].children[0].children[0].children[0].children[0].children[0].children[1];
        // boxstyle.style.height = window.innerHeight - 185 + 'px'
        // if(tab2 && this.proOrderStackUpImpDto.stackIMPOutputs &&  this.proOrderStackUpImpDto.stackIMPOutputs.length!=0){
        //   tab1.style.height =  window.innerHeight/2 - 210+'px'
        //   tab2.style.height =  window.innerHeight/2+'px'
        // }else{
        tab2.style.height = this.proOrderStackUpImpDto.stackIMPOutputs.length ? "200px" : "0px";
        tab1.style.height = this.proOrderStackUpImpDto.stackUpOutputs.length ? "437px" : "0px";
        // }
      }
    },
    addClass(record, index) {
      switch (record.stackUpMTR_) {
        case "OZ":
          return "OZ";
          // eslint-disable-next-line no-unreachable
          break;
        case "Core":
          return "Core";
          // eslint-disable-next-line no-unreachable
          break;
        case "PP":
          return "PP";
          // eslint-disable-next-line no-unreachable
          break;
        default:
          break;
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
};
</script>
<style scoped lang="less">
.laminationstyle {
  /deep/.ant-table-thead > tr > th {
    padding: 0 9px;
    height: 27px;
    line-height: 27px;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 0 9px;
    height: 27px;
    line-height: 27px;
  }
}
/deep/.ant-input-affix-wrapper .ant-input:not(:last-child) {
  padding-right: 10px;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select {
  width: 100%;
}
/deep/.ant-select-selection--single {
  position: relative;
  height: 26px;
  cursor: pointer;
}
.Tab2 {
  /deep/.ant-select-selection__rendered {
    line-height: 24px !important;
  }
}
/deep/.ant-select-selection__rendered {
  position: relative;
  display: block;
  margin-right: 11px;
  margin-left: 11px;
  line-height: 32px;
}
.tabRightClikBox {
  li {
    height: 25px;
    line-height: 25px;
    margin-top: 0;
    margin-bottom: 0;
    font-size: 12px;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: rgb(255, 255, 255) !important;
    color: #000000;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.box {
  border-left: 1px solid rgb(233 230 230);
  position: relative;
  .bto {
    position: absolute;
    bottom: -48px;
    right: 374px;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/ .ant-table-thead > tr > th {
  padding: 0 9px;
  height: 36px;
  line-height: 36px;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 0 9px;
  height: 36px;
  line-height: 36px;
}
/deep/ .ant-table-tbody .M_bg {
  background: #8fbc8b !important;
}
/deep/ .ant-table-tbody {
  // font-weight: 600!important;
  .OZ {
    .material_bg {
      background: #f4a460 !important;
    }
  }
  .Core {
    .material_bg {
      background: #f0e68c !important;
    }
  }
  .PP {
    .material_bg {
      background: #9acd32 !important;
    }
  }
  .material_bg {
    .ant-select-selection {
      background: none;
    }
  }
}
/deep/.ant-empty-normal {
  padding: 0 !important;
}
/deep/ .ant-form-item {
  margin: 0;
  padding-top: 10px;
  width: 100%;
  display: flex;

  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 28px;
    .ant-select {
      width: 120px;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 14px/1.14 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    //background-color: #fafafa;
    // border-right: 1px solid #ddd;
    // border-bottom: 1px solid #ddd;
    label {
      font: 14px/1.14 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 14px/1.14 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        display: block;
        min-height: 28px;
        line-height: 26px;
        .ant-checkbox-wrapper {
          height: 28px;
        }
        .ant-select-selection--single {
          height: 28px;
        }
        .ant-select-selection__rendered {
          line-height: 28px;
        }
        .ant-select {
          height: 28px;
        }
        .ant-input {
          height: 28px;
        }
      }
      line-height: inherit;
      padding: 2px 5px;
      // border-right: 1px solid #ddd;
      // border-bottom: 1px solid #ddd;
    }
  }
}
</style>
