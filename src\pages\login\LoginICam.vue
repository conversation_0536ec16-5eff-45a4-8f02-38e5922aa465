<template>
  <div class="main">
    <span class="close"></span>
    <img src="../../assets/web/login_bg_2.png" class="img" id="loginImg">
    <div class="login-wrap">
      <div class="title">
        <img  style='width:40px;margin:0 15px 10px;' src="@/assets/img/bn2.png">
        <span >iPCB-CAM 登录</span> 
      </div>      
      <a-form @submit="onSubmit" :form="form" >
                <a-alert
                    type="error"
                    :closable="true"
                    v-show="error"
                    :message="error"
                    showIcon
                    style="margin-bottom: 24px"
                />
                <a-form-item>
                  <a-input
                      autocomplete="autocomplete"
                      size="large"
                      placeholder="请输入账号"
                      v-decorator="['name',{rules: [{ required: true, message: '请输入账号', whitespace: true },],},]"                      
                  >                 
                    <a-icon slot="prefix" type="user" />
                  </a-input>
                </a-form-item>
                <a-form-item>
                  <a-input
                      size="large"
                      placeholder="请输入密码"
                      autocomplete="autocomplete"
                      type="password"
                      v-decorator="['password',{rules: [{ required: true, message: '请输入密码', whitespace: true },],},]"
                  >
                    <a-icon slot="prefix" type="lock" />
                  </a-input>
                </a-form-item>
                <a-form-item style="margin:0;">
                  <div class="user-checked">
                    <div class="l">                     
                      <label><input type="checkbox" name="remPassword" value="1"  v-model="isRemember" />记住密码</label>
                      <label><input type="checkbox" name="autologin" value="1" v-model="isAuto" />自动登录</label>
                      <!-- <a @click="Password" style="cursor: pointer;">忘记密码</a> -->
                    </div>                    
                  </div>                  
                </a-form-item>                
                <a-form-item>
                  <a-button
                      :loading="logging"
                      style="width: 100%; margin-top: 24px;"
                      size="large"
                      htmlType="submit"                     
                      id="loginBtn"
                  >登录</a-button
                  >
                </a-form-item>
        </a-form>                 
      <!-- <div style="margin: 25px 0 0;">
        <a disabled="true" onclick="window.open('http://icam.pcbpp.com:8080/signUp.html')" style="color: #01a279;cursor: pointer;font-size:14px;">注册新账号</a>
      </div>      -->
    </div>
  </div>
  <!-- <common-layout>
    <div class="login_box">
      <div class="login_left">
        <img src="../../assets/img/loginlogo.png" alt="">
      </div>
      <div class="login_right">
        <div class="top">
          <div class="header">
            <span class="title">{{ systemName }}</span>
          </div>
        </div>
        <a-tabs default-active-key="1" @change="callback" v-if="login">
          <a-tab-pane key="1" tab="账号登录">
            <div class="login">
              <a-form @submit="onSubmit" :form="form">
                <a-alert
                    type="error"
                    :closable="true"
                    v-show="error"
                    :message="error"
                    showIcon
                    style="margin-bottom: 24px"
                />
                <a-form-item>
                  <a-input
                      autocomplete="autocomplete"
                      size="large"
                      placeholder=""
                      v-decorator="[
                  'name',
                  {
                    rules: [
                      { required: true, message: '请输入账户名', whitespace: true },
                    ],
                  },
                ]"
                      
                  >
                 
                    <a-icon slot="prefix" type="user" />
                  </a-input>
                </a-form-item>
                <a-form-item>
                  <a-input
                      size="large"
                      placeholder="*"
                      autocomplete="autocomplete"
                      type="password"
                      v-decorator="[
                  'password',
                  {
                    rules: [
                      { required: true, message: '请输入密码', whitespace: true },
                    ],
                  },
                ]"
                  >
                    <a-icon slot="prefix" type="lock" />
                  </a-input>
                </a-form-item>
                <a-form-item v-if="inputStat">
                  <a-row :gutter="8" style="margin: 0 -4px">
                    <a-col :span="16">
                      <a-input size="large" placeholder="captcha"

                               v-decorator="[
                  'smsCode',
                  {
                    rules: [
                      { required: true, message: '请输入验证码', whitespace: true },
                    ],
                  },
                ]"
                      >
                        <a-icon slot="prefix" type="mail" />
                      </a-input>
                    </a-col>
                    <a-col :span="8" style="padding-left: 4px">
                      <a-button style="width: 100%" class="captcha-button" size="large"  :disabled="disabledBtn"  @click="countDown">{{content}}</a-button>
                    </a-col>
                  </a-row>
                </a-form-item>
                <a-form-item>
                  <a-button
                      :loading="logging"
                      style="width: 100%; margin-top: 24px;"
                      size="large"
                      htmlType="submit"
                      type="primary"
                      class="login_btn"
                  >登录</a-button
                  >
                </a-form-item>
              </a-form>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="手机快捷登录" force-render disabled>
            <phone-login/>
          </a-tab-pane>
        </a-tabs>
        <register-form v-else @register="register"></register-form>
        <div style="width: 100%; text-align: center">{{login? '还没有账号？':'已有账号？'}}<a @click="register">{{login? '立即注册':'马上登录'}}</a></div>
      </div>
    </div>
  </common-layout> -->
</template>
<script>
import {
  login,
  applicationConfiguration,
  verificationName1,
  verificationName,
  getVerification, getHomePage
} from "@/services/user";
import PhoneLogin from "@/pages/login/modules/PhoneLogin";
import { setAuthorization } from "@/utils/request";
import { loadRoutes } from "@/utils/routerUtil";
import { mapMutations } from "vuex";


export default {
  name: "LoginICam",
  data() {
    return {
      logging: false,
      error: "",
      form: this.$form.createForm(this),
      visible: false,
      confirmLoading: false,
      tenantName: "",
      content: '发送验证码',  // 按钮里显示的内容
      totalTime: 60,      //记录具体倒计时时间
      disabledBtn: false,
      inputStat: false,//验证码输入框
      login: false,
      isRemember:false,
      isAuto:false,
      ipcbversion:'',
      macaddress:'',
    };
  },
  computed: {
    systemName() {
      return this.$store.state.setting.systemName;
    },
  },
  mounted() {
    window.external.SetWindowPos(JSON.stringify({
      x: (screen.width / 2) - 340 ,
      y: (screen.height / 2) -225,
      width: 680,
      height: 450,
    }));
    const info = window.external.GetLoginInfo("")
    let infoJSON = JSON.parse(info);
    if(infoJSON.RememberPassword){       
      this.form.setFieldsValue({
        name: infoJSON.UserName,
        password:infoJSON.PassWord,       
      }) 
      this.isRemember = infoJSON.RememberPassword;
      this.ipcbversion = infoJSON.ipcbversion ? infoJSON.ipcbversion:''
      this.macaddress = infoJSON.macaddress ? infoJSON.macaddress:''
    }
    this.isAuto = infoJSON.AutoLogin;
    if(this.isAuto){
      this.logging = true
      verificationName1(infoJSON.UserName, infoJSON.PassWord,this.ipcbversion,this.macaddress).then((res) => {
        if (res.code) {
          window.external.CallLogin(JSON.stringify({
            code: res.code,
            success: res.success,
            message: res.message,
            data:res.data,
            UserName:infoJSON.UserName,
            PassWord:infoJSON.PassWord,
            ipcbversion:this.ipcbversion,
            macaddress:this.macaddress,
            RememberAccount: this.isRemember,
            RememberPassword:this.isRemember,
            AutoLogin: this.isAuto,
            CompanyName:"百能信息"
          }));
        } else {
          this.$message.error(res.message);
        }
        this.logging = false;

      });
    }
  },
  watch: {
    totalTime(val) {
      if (val == 0) {
        this.disabledBtn = false
        this.content = '发送验证码'
      }
    }
  },
  methods: {
    ...mapMutations("account", ["setUser", "setPermissions", "setRoles"]),
    Password(){
      console.log('忘记密码')
    },
    callback(key) {
      console.log(key);
    },
    countDown() {
      this.disabledBtn = true
      let clock = window.setInterval(() => {
        this.totalTime--
        this.content = this.totalTime + 's后重新发送'
        if (this.totalTime == 0) {
          this.disabledBtn = false
          this.content = '发送验证码'
          this.totalTime = 60
          clearInterval(clock)
        }
      }, 1000)
      let parmas = {}
      parmas.userName = this.form.getFieldValue("name")
      parmas.smsFrom = 'MES后台'
      parmas.sendType = '登录验证'
      getVerification(parmas).then(res => {
      })
    },
    onSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err) => {
        if (!err) {
          this.logging = true;        
          this.afterLogin()         
        }
      });
    },   
    afterLogin(res) {
      // console.log('11')
      const loginRes = res;
      // if (loginRes) {
      //   const { user, permissions, roles } = loginRes.data;
      //   this.setUser(user);
      //   this.setPermissions(permissions);
      //   this.setRoles(roles);
      //   setAuthorization({
      //     token: loginRes.access_token,
      //     expireAt: new Date(new Date().getTime() + loginRes.expires_in),
      //   });
      // }  
      const name = this.form.getFieldValue("name");
      const password = this.form.getFieldValue("password");
      verificationName1(name, password,this.ipcbversion,this.macaddress).then((res) => {
        if (res.code) {
          window.external.CallLogin(JSON.stringify({
            code: res.code,
            success: res.success,
            message: res.message,
            data:res.data,
            UserName:name,
            PassWord:password,
            ipcbversion:this.ipcbversion,
            macaddress:this.macaddress,
            RememberAccount: this.isRemember,
            RememberPassword:this.isRemember,
            AutoLogin: this.isAuto,
            CompanyName:"百能信息"
          }));
        } else {
          this.$message.error(res.message);
        }
        this.logging = false;

        // 10-25 登录成功跳转home页
        // getHomePage().then(res => {
        //   if (res.data) {
        //     if (res.data == '/') {
        //       this.$router.push( res.data)
        //     } else {
        //       this.$router.push('/'+ res.data)
        //     }
        //   } else {
        //     this.$router.push('/dashboard/analysis')
        //   }
        // })

        // if(res.currentUser.roles.indexOf('AGV') != -1) {
        //   this.$router.push('/productionManagement/agvDispatch')
        // } else{
        //   this.$router.push('/dashboard/analysis')
        // }

      });
    },
    handlePermissions(obj) {
      let permissions = [];
      if (!obj) {
        return permissions;
      }
      permissions = Object.keys(obj).map((x) => {
        return {
          id: x,
          operation: [],
        };
      });
      return permissions;
      // let list = Object.keys(obj).map((x) => {
      //   let n = x.split(".").length - 1;
      //   return {
      //     val: x,
      //     num: n,
      //   };
      // });
      // let idList = list.filter((x) => x.num == 1);
      // permissions = idList.map((x) => {
      //   let operation = list
      //     .filter((y) => y.num == 2 && y.val.indexOf(x.val) > -1)
      //     .map((y) => {
      //       return y.val.split(".")[2];
      //     });
      //   return {
      //     id: x.val,
      //     operation: operation,
      //   };
      // });
      // return permissions;
    },
    register() {    
      window.location.href = 'http://icam.pcbpp.com:8080/signUp.html'
    }
  },
};
</script>
<style >
* {
  padding: 0;
  margin: 0
}

html,
body {
  width: 100%;
  height: 100%;
  /* font-family: "微软雅黑", arial */
   /*font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;*/
}

body {
  background-color: #f3f3f3
}

.main {
  margin: 0px auto;
  /* width: 640px; */
  width: 620px;
  height: 410px;
  background: #ffffff;
  padding: 20px;
  position: relative
}

.main .img {
  width: 280px;
  border-radius: 2px
}

.main .close {
  width: 12px;
  height: 12px;
  position: absolute;
  top: 22px;
  right: 22px;
  background: url("/web/images/login/login_close.png") no-repeat center center;
  background-size: 100%;
  cursor: pointer;
  display: none
}

.main .close:hover {
  background: url("/web/images/login/login_close_hover.png") no-repeat center center;
  background-size: 100%
}

.main .login-wrap {
  /* width: 300px; */
  width: 280px;
  float: right;
  margin-top:30px;
}

.main .login-wrap a:hover {
  text-decoration: underline;
}

.main .login-wrap .title {
  font-size: 20px;
  text-align: left;
  color: #000000;
  padding: 0;
  margin-bottom:15px;
}

.main .login-wrap .tabs {
  width: 234px;
  height: 32px;
  background-color: #f0f0f0;
  border-radius: 16px;
  display: flex;
  margin: 20px auto 30px auto
}

.main .login-wrap .tabs p {
  line-height: 32px;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  width: 50%;
  text-align: center;
  cursor: pointer
}

.main .login-wrap .tabs p.current {
  width: 115px;
  height: 30px;
  background: #ffffff;
  border-radius: 15px;
  margin-top: 1px
}

.l {
  display: flex;
  justify-content: space-around;
}

.main .login-wrap .tabs p.r {
  margin-right: 1px
}

.login-wrap .tabs-item .user-wrap {
  width: 300px;
  height: 40px;
  background: #ffffff;
  position: relative;
  margin: 35px 0 0;
}

.login-wrap .tabs-item .user-wrap .icon {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 12px;
  left: 12px
}

.login-wrap .tabs-item .user-wrap .icons {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 12px;
  top: 12px;
  cursor: pointer
}

.login-wrap .tabs-item .user-wrap .icons.by {
  background: url("/web/images/login/icon_by.png") no-repeat center;
  background-size: 100%
}

.login-wrap .tabs-item .user-wrap .icons.zy {
  background: url("/web/images/login/icon_zy.png") no-repeat center;
  background-size: 100%
}

.login-wrap .tabs-item .user-wrap .icon.username {
  background: url("/web/images/login/icon_user.png") no-repeat center;
  background-size: 100%
}

.login-wrap .tabs-item .user-wrap .icon.password {
  width: 14px;
  height: 16px;
  background: url("/web/images/login/icon_password.png") no-repeat center;
  background-size: 100%
}

.login-wrap .tabs-item .user-wrap input {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  text-indent: 40px;
  font-size: 14px;
  outline: none
}

.login-wrap .tabs-item .user-error {
  padding: 6px 0 12px;
  font-size: 12px;
  color: #999999
}

.login-wrap .tabs-item .user-error.error {
  color: #FF2121
}

.login-wrap .tabs-item .user-error.error.hidden {
  visibility: hidden
}

.login-wrap .tabs-item .user-error.error.show {
  visibility: inherit
}

.login-wrap .tabs-item .user-error.error .icon-tips {
  background: url("/web/images/login/icon_error.png") no-repeat center;
  background-size: 100%
}

.login-wrap .tabs-item .user-error .icon-tips {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  background: url("/web/images/login/login_tips.png") no-repeat center;
  background-size: 100%;
  margin-top: -2px;
  margin-right: 4px
}

.login-wrap .tabs-item .user-checked {
  display: flex;
  justify-content: space-between;
  margin: 30px 0 0;
}

.login-wrap .tabs-item .user-checked label {
  font-size: 12px;
  color: #666;
  margin: 0 26px 0 2px;
  cursor: pointer;
  user-select: none;
}

.login-wrap .tabs-item .user-checked label input[type='checkbox'] {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  margin: -2px 4px 0 0;
}

.login-wrap .tabs-item .user-checked .link {
  font-size: 12px;
  color: #999;
  cursor: pointer;
}

.login-wrap .tabs-item .user-checked .link:hover {
  opacity: 0.9;
}

 #loginBtn {
  width: 300px;
  height: 40px;
  background-color: #01a279;
  border-radius: 3px;
  margin-top: 30px;
  text-align: center;
  cursor: pointer;
  line-height: 40px;
  color: #ffffff;
  letter-spacing: 4px;
  border: none;
  outline: none;
}

 #loginBtn:hover {
  opacity: 0.9;
}

.login-wrap .tabs-item .user-register {
  margin-top: 20px;
  text-align: right;
  font-size: 13px;
  color: #999999
}

.login-wrap .tabs-item .user-register .link {
  color: #01a279;
  cursor: pointer;
}

.login-wrap .tabs-item .user-register .link:hover {
  text-decoration: underline;
}

.ui-item {
  width: 300px
}

.login-wrap .tabs-item .ui-phone-item {
  background: #ffffff;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  height: 39px;
  position: relative
}

.login-wrap .tabs-item .ui-phone-item select {
  height: 36px;
  border: none;
  font-size: 12px;
  position: absolute;
  top: 2px;
  left: 0;
  z-index: 1;
  outline: none
}

.login-wrap .tabs-item .ui-phone-item input[type='text'] {
  width: 100%;
  border: none;
  height: 39px;
  font-size: 14px;
  position: absolute;
  left: 0;
  top: 0;
  text-indent: 120px;
  outline: none
}

.login-wrap .tabs-item .drag_verify {
  position: relative;
  height: 38px;
  margin-bottom: 20px;
  margin-top: 20px
}

.login-wrap .tabs-item .sms-item {
  background: #ffffff;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  height: 40px;
  position: relative
}

.login-wrap .tabs-item .sms-item input[type='text'] {
  width: 180px;
  height: 38px;
  font-size: 14px;
  text-indent: 20px;
  border: none;
  outline: none
}

.login-wrap .tabs-item .sms-item .line {
  height: 25px;
  border-right: 1px solid #e9e9e9;
  display: inline-block;
  vertical-align: middle
}

.login-wrap .tabs-item .sms-item .J_get_smscode {
  line-height: 40px;
  font-size: 14px;
  margin-left: 15px;
  cursor: pointer;
  color: #01a279
}

.login-wrap .tabs-item .sms-item .J_get_smscode:hover {
  text-decoration: underline;
}

.login-wrap .tabs-item .ui-item #loginSignBtn {
  width: 300px;
  height: 40px;
  background: #01a279;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  line-height: 40px;
  color: #ffffff;
  letter-spacing: 4px;
  border: none;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px
}

.login-wrap .tabs-item .ui-item #loginSignBtn:hover {
  opacity: 0.9;
}

.login-wrap .tabs-item .ui-protocal {
  margin-top: 24px;
  text-align: center;
  position: relative
}

.login-wrap .tabs-item .ui-protocal label {
  font-size: 12px;
  color: #999;
}

.login-wrap .tabs-item .ui-protocal label input[type='checkbox'] {
  width: 15px;
  height: 15px;
  display: inline-block;
  vertical-align: middle;
  margin: -2px 4px 0 0;
}

.login-wrap .tabs-item .ui-protocal a {
  color: #666666;
  text-decoration: underline;
  cursor: pointer;
}

.login-wrap .tabs-item .ui-protocal a:hover {
  text-decoration: underline;
}

.ui-item .validator-error {
  box-shadow: 0 0 1px 1px #e86868;
  border: 1px solid #e86868;
  background-color: snow;
  border-radius: 4px
}

.valid_warn {
  position: absolute;
  top: 40px;
  left: 0;
  color: #e86868;
  background-color: #fff;
  font-size: 12px
}

.toast {
  z-index: 999999;
  position: fixed;
  top: 200px;
  left: 50%;
  right: 0;
  border-radius: 3px;
  font-size: 12px;
  color: #fff
}

.toast .text {
  display: inline-block;
  padding: 0 20px;
  line-height: 32px;
  background-color: rgba(0, 0, 0, .8);
  border-radius: 3px
}
</style>

<!-- <style lang="less" scoped>
  /deep/.ant-btn-lg{
    padding: 0 3px!important;
  }
.common-layout {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  .login_box{
    background-color: transparent;
    width: 1080px;
    height: 800px;
    top: 50%;
    transform: translate(0,2px);
    .login_left{
      width: 50%;
      height: 100%;
      float: left;
      background: #f90;
    }
    .login_right{
      float: left;
      width: 50%;
      height: 100%;
      background: #fff;
      padding: 160px 75px 0;
    }
  }
  .top {
    text-align: center;
    margin-bottom: 30px;
    .header {
      height: 44px;
      line-height: 44px;
      a {
        text-decoration: none;
      }
      .logo {
        height: 44px;
        vertical-align: top;
        margin-right: 16px;
      }
      .title {
        font-size: 30px;
        color: @title-color;
        font-family: "Myriad Pro", "Helvetica Neue", Arial, Helvetica,
          sans-serif;
        font-weight: 600;
        position: relative;
        top: 2px;

      }
    }
    .desc {
      font-size: 14px;
      color: @text-color-second;
      margin-top: 12px;
      margin-bottom: 40px;
    }
  }
  .login {
    width: 368px;
    margin: 0 auto;
    @media screen and (max-width: 576px) {
      width: 95%;
    }
    @media screen and (max-width: 320px) {
      .captcha-button {
        font-size: 14px;
      }
    }
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
@media (max-width:1200px){
    .common-layout{
      .login_box{
        width:100%;
        height:600px;
        .login_left{display: none;}
        .login_right{
          width:100%;float: none;
          padding: 60px 20px 0;
          .title{font-size:20px;}
        }
      }

    }

}
</style> -->
