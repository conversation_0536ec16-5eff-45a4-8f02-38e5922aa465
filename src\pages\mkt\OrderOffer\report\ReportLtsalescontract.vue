<!-- 市场管理 - 订单报价- 龙腾销售合同 -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj" type="primary" class="printstyle" @click="printpdf">打印</a-button>
    <div id="pdfDomlt" style="font-size: 12px; font-family: 'Courier New', Courier, monospace; color: black; font-weight: 600">
      <div style="width: 100%; text-align: center; display: flex; justify-content: center">
        <div><img src="@/assets/img/LTlogo.png" style="width: 120px" /></div>
        <div style="margin-left: 20px">
          <span style="font-size: 28px; font-weight: bold">{{ LTsalesdata.factory_ }}</span
          ><br />
          <span style="font-size: 20px">{{ LTsalesdata.value_1 }}</span
          ><br />
          <span style="font-size: 20px">PCB销售合同</span>
        </div>
      </div>
      <div style="display: flex">
        <div style="width: 900px">
          <div>甲方:{{ LTsalesdata.party_ }}</div>
          <div>乙方:{{ LTsalesdata.factory_ }}</div>
          <div>甲、乙双方经充分协商，就甲方委托乙方按文件加工生产产品事宜，达到如下条件供双方遵守，并适用于双方此前和将来的交易。</div>
          <div>请盖章回签确认，以便及时下单，传真件有效。如有冲突，以此为准。</div>
          <div>备 注:{{ LTsalesdata.specialrequirements }}</div>
        </div>
        <div style="width: 200px">
          <div>客户订单号:{{ LTsalesdata.contractNO }}</div>
          <div>销售合同号:{{ LTsalesdata.orderNo_ }}</div>
        </div>
        <span @click="addcontract" style="color: #4b82ac; margin-top: 67px; cursor: pointer" v-if="showadd && act != 'dis'"
          >点击添加 <a-icon style="font-size: 16px; top: 2px; position: relative" type="plus-circle"></a-icon
        ></span>
      </div>
      <div>
        <table border="1" style="text-align: center; width: 100%">
          <thead>
            <tr>
              <td>序号</td>
              <td>生产型号(工程型号)</td>
              <td>层数</td>
              <td>物料编码</td>
              <td>客户型号</td>
              <td>规格Set</td>
              <td>板材/板厚</td>
              <td>铜厚外/内(oz)</td>
              <td>表面处理</td>
              <td>阻焊颜色</td>
              <td>字符颜色</td>
              <td>数量</td>
              <td>单位</td>
              <td>单价</td>
              <td v-if="engprice">工程费</td>
              <td v-if="mouldprice">模具费</td>
              <td v-if="testprice">测试架费</td>
              <td v-if="otherprice">其他费</td>
              <td>合计金额</td>
              <td>交货日期</td>
              <td style="width: 40px" v-if="showadd && act != 'dis'">操作</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in LTsalesdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.name }}</td>
              <td>{{ item.lay }}</td>
              <td>{{ item.mat }}</td>
              <td>{{ item.custName }}</td>
              <td>{{ item.size }}</td>
              <td>{{ item.boardBrand }}</td>
              <td>{{ item.oucu }}</td>
              <td>{{ item.surface }}</td>
              <td>{{ item.mask }}</td>
              <td>{{ item.char_ }}</td>
              <td>{{ item.qty }}</td>
              <td>{{ item.bType }}</td>
              <td>{{ item.pcs }}</td>
              <td v-if="engprice">{{ item.eng }}</td>
              <td v-if="mouldprice">{{ item.mould }}</td>
              <td v-if="testprice">{{ item.test }}</td>
              <td v-if="otherprice">{{ item.other }}</td>
              <td>{{ item.total }}</td>
              <td>{{ item.custdate }}</td>
              <td v-if="showadd && act != 'dis'">
                <a-tooltip placement="top" title="删除当前行数据">
                  <a-icon @click="delclick(item.id)" style="font-size: 14px; color: #4b82ac" type="close-circle"></a-icon>
                </a-tooltip>
              </td>
            </tr>
            <tr>
              <td style="text-align: right; padding-right: 15px" colspan="2">总金额(大写):</td>
              <td colspan="6">{{ convertToChineseNum(amountto) }}</td>
              <td style="text-align: right; padding-left: 6px" colspan="2">小写(RMB):</td>
              <td colspan="10">{{ amountto }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="position: relative">
        <div style="display: flex; width: 100%; line-height: 3ch">
          <div style="width: 33%">付款周期:{{ LTsalesdata.clearingForm }}<br />发货方式：{{ LTsalesdata.deliveryType }}</div>
          <div style="width: 33%">税率(%):{{ LTsalesdata.taxPercentage_ }}<br />验收标准:{{ LTsalesdata.ipcLevel }}</div>
          <div style="width: 33%">包装方式:{{ LTsalesdata.defend }}<br />交货地址:{{ LTsalesdata.deliveryAdd }}</div>
        </div>
        <div style="z-index: 99; position: relative">
          <div>
            一、本次加工要求由甲方提供的纸质书面文件或电子文件为准，特殊要求以文字说明，原有条款以双方文字约定为准。<br />
            二、由于定制加工过程的特殊性，实际交货数量可以与约定数量有差异，双方同意以批量产品（数量≥50）允许正负5%的误差，对50个以下的样品允许正负2个误差。<br />
            &nbsp;&nbsp;&nbsp;交货数量在误差范围内视为正常交货，双方依据实际交货数量结算货款。<br />
            三、PCB成品板真空包装，在恒温恒湿的仓中储存，温度23±2℃，湿度50±10% ；<br />
            四、PCB成品板在上述(三)的恒温恒湿条下，喷锡板最长储存放时间1年；沉金板最长储存放时间6个月；抗氧化板（osp）、沉锡板、沉银板最长储存放时间3个月。<br />
            五、在产品加工过程中，甲方变更加工要求的，乙方收取相应的费用并顺延交货期限，甲方要求暂停生产或取消订货的，甲方须按乙方已发生成本支付费用并进行适度赔偿。<br />
            六、乙方加工生产的PCB产品质量符合国际IPC-600G，CLASS II 标准，其它必须遵循的特殊要示已在本合同中列出。<br />
            七、甲方不同意支付测试架费用，即视为甲方要求目测，甲方七.因乙方加工生产产品的品质问题给甲方造成损失的，甲方可以要求更换不合格的产品或要求乙方在本批供货额的二倍限额内据实赔偿。<br />
            八.、甲方对乙方加工生产的产品的质量异议期为十天，甲方对质量有异议的应在十天内书面提出，否则视为甲方认可乙方交货的质量。<br />
            九、甲方按照约定日期履行付款义务，每拖欠一日应支付所拖欠款额的千分之一的违约金。<br />
            十、本合同履行中的争议由甲、乙双立协商解决，协商不成的可向签约地孝感法院起诉。<br />
            十一、本合同经双方盖章或经办人签字或经办人签字后生效。一式两份，甲、乙双方各执壹份，传真件与合同原件具有相同法律效力。<br />
          </div>
          <div style="margin-top: 10px; display: flex; justify-content: space-between">
            <div>
              <div>甲方:{{ LTsalesdata.custL_ }}</div>
              <div>地址:{{ LTsalesdata.custAddL_ }}</div>
              <div>
                <span style="width: 200px; display: inline-block">电话:{{ LTsalesdata.facPhone_ }}</span> <span>传真:{{ LTsalesdata.facFax_ }}</span>
              </div>
              <div>经办人:{{ LTsalesdata.custdelegate_ }}</div>
              <div>日期:{{ LTsalesdata.custdate_ }}</div>
            </div>
            <div>
              <div>乙方:{{ LTsalesdata.factory_ }}</div>
              <div>地址:{{ LTsalesdata.factoryAddR_ }}</div>
              <div>
                <span style="width: 200px; display: inline-block">电话:{{ LTsalesdata.tel_ }}</span> <span>传真:{{ LTsalesdata.fax_ }}</span>
              </div>
              <div>经办人:{{ LTsalesdata.factorydelegate_ }}</div>
              <div>日期:{{ LTsalesdata.factorydete_ }}</div>
            </div>
          </div>
        </div>
        <img
          src="@/assets/img/lthtz.png"
          style="z-index: 0; display: block; width: 150px; transform: rotate(353deg); position: absolute; bottom: 0px; right: 10%"
        />
        <div style="height: 40px"></div>
      </div>
    </div>
    <a-modal title="合同数据添加" :visible="modalvisible" @ok="handleOk" @cancel="handleCancel" centered :width="800">
      <a-row>
        <a-col :span="10">
          <a-form-item label="订单号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入订单号进行查询" v-model="OrderNo" @keyup.enter="queryclick" :auto-focus="true" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="客户型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入客户型号进行查询" v-model="PcbFileName" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" style="margin-top: 2px" @click="queryclick">查询</a-button>
        </a-col>
      </a-row>
      <a-table
        :columns="columns"
        :row-selection="{ selectedRowKeys: selectedRowKeysind, onChange: onSelectChange, columnWidth: 25 }"
        :pagination="false"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ y: 300 }"
        :dataSource="datasource"
        :rowClassName="isRedRow"
      >
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import { verifyPageList, deletecontractno, contractNo } from "@/services/mkt/OrderReview.js";
import htmlToPdf from "@/utils/htmlToPdfa3";
export default {
  name: "ReportInfoyxd",
  props: ["LTsalesdata", "joinFactoryId", "salescustno", "ContractNoSech", "ttype", "act"],
  computed: {},
  data() {
    return {
      printObj: {
        id: "pdfDomlt", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      selectedRowKeys: [],
      showadd: true,
      selectedRowKeysind: [],
      PcbFileName: "",
      OrderNo: "",
      datasource: [],
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 35,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "订单号",
          align: "left",
          ellipsis: true,
          width: 80,
          dataIndex: "orderNo",
        },
        {
          title: "客户代码",
          dataIndex: "custNo",
          align: "left",
          ellipsis: true,
          width: 50,
        },

        {
          title: "订单类型",
          dataIndex: "reOrder",
          align: "left",
          ellipsis: true,
          width: 50,
          customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
        },
      ],
      amountto: 0,
      modalvisible: false,
      ids: [],
      otherprice: false,
      engprice: false,
      mouldprice: false,
      testprice: false,
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.amountto = 0;
    this.ids = [];
    this.LTsalesdata.price.forEach(item => {
      if (this.ids.indexOf(item.id) == -1) {
        this.ids.push(item.id);
      }
    });
    for (let index = 0; index < this.LTsalesdata.price.length; index++) {
      if (this.LTsalesdata.price[index].total && this.LTsalesdata.price[index].total != "/") {
        this.amountto += Number(this.LTsalesdata.price[index].total);
      }
      if (this.LTsalesdata.price[index].eng != "/" && this.LTsalesdata.price[index].eng) {
        this.engprice = true;
      }
      if (this.LTsalesdata.price[index].mould != "/" && this.LTsalesdata.price[index].mould) {
        this.mouldprice = true;
      }
      if (this.LTsalesdata.price[index].test != "/" && this.LTsalesdata.price[index].test) {
        this.testprice = true;
      }
      if (this.LTsalesdata.price[index].other != "/" && this.LTsalesdata.price[index].other) {
        this.otherprice = true;
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
    this.showadd = !this.LTsalesdata.price.some(ite => ite.status == 30);
  },
  methods: {
    convertToChineseNum,
    getsalesPdf() {
      this.showadd = false;
      setTimeout(() => {
        htmlToPdf("pdfDomlt", this.LTsalesdata.pcbFileName);
        this.showadd = !this.LTsalesdata.price.some(ite => ite.status == 30);
      }, 500);
    },
    closePrintTool() {
      document.title = this.ttype;
      this.showadd = !this.LTsalesdata.price.some(ite => ite.status == 30);
    },
    printpdf() {
      document.title = this.LTsalesdata.pcbFileName;
      this.showadd = false;
    },
    delclick(id) {
      this.ids = [];
      this.LTsalesdata.price.forEach(item => {
        if (this.ids.indexOf(item.id) == -1) {
          this.ids.push(item.id);
        }
      });
      deletecontractno(id).then(res => {
        if (res.code) {
          this.ids.forEach(item => {
            if (item == id) {
              this.ids.splice(this.ids.indexOf(item), 1);
            }
          });
          this.$message.success("删除成功");
          this.$emit("LTsalescontract", this.ids, "LT");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeys = rowKeys;
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysind = selectedRowKeys;
      this.selectedRowKeys = [];
      selectedRows.forEach(item => {
        this.selectedRowKeys.push(item.id);
      });
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    queryclick() {
      if (!this.OrderNo && !this.PcbFileName) {
        this.$message.error("请输入订单号或客户型号进行查询");
        return;
      }
      let params = {
        PageIndex: 1,
        PageSize: 20,
        CustNo: this.salescustno,
        ContractNoSech: this.ContractNoSech,
      };
      if (this.OrderNo) {
        params.OrderNo = this.OrderNo;
      }
      if (this.PcbFileName) {
        params.PcbFileName = this.PcbFileName;
      }
      verifyPageList(params).then(res => {
        if (res.code) {
          this.datasource = res.data.items;
          if (this.datasource.length == 0) {
            this.$message.error("未查询到相关订单");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    addcontract() {
      this.modalvisible = true;
      this.selectedRowKeys = [];
      this.datasource = [];
      this.OrderNo = "";
      this.PcbFileName = "";
      this.selectedRowKeysind = [];
    },
    handleOk() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择订单");
        return;
      }
      this.ids = [...this.ids, ...this.selectedRowKeys];
      contractNo(this.ids).then(res => {
        if (res.code) {
          this.$emit("LTsalescontract", this.ids, "LT");
        } else {
          this.$message.error(res.message);
        }
      });
      this.modalvisible = false;
    },
    handleCancel() {
      this.modalvisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.agreement {
  padding-bottom: 20px;
  position: relative;
  z-index: 99;
  div {
    padding-top: 10px;
  }
}
.tableclass {
  border: 1px solid black;
  margin-top: 10px;
  margin-bottom: 10px;
  td {
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding-left: 7px;
  }
  tbody {
    tr {
      height: 24px;
    }
  }
}
.rowBackgroundColor {
  background: #dcdcdc !important;
}
.ant-table-row-selected {
  background: #dcdcdc !important;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-table-tbody > tr.ant-table-row-selected td {
  background: #dcdcdc;
}
/deep/.ant-table {
  border: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-thead > tr > th {
  padding: 4px 4px;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 4px 4px !important;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
  color: #000000;
}
.pdfDom1 {
  font-size: 12px;
  padding: 25px;
  height: 650px;
  overflow: auto;
}
</style>
