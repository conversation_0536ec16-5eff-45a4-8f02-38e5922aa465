<!-- 工程管理 - 工程后端-市场修改 -->
<template>
    <div class='MarketBack'>
      <a-form :label-col="{ span:2 }" :wrapper-col="{ span: 18}" >
        <a-form-item >
          <a-upload
              accept=".png, .jpg, .jpeg,bmp"               
              name="file"
              ref="fileRef"
              :before-upload="beforeUpload1"
              :multiple="true"
              :customRequest="httpRequest1"
              :file-list="fileListData"
              @change="handleChange1"
              list-type="picture-card"
              @preview="handlePreview"
          >
            <a-button style="font-weight:500" v-if="showUploadButton">
              上传图片
            </a-button>
          </a-upload>
        <a-modal
            :visible="previewVisible"
            :footer="null"
            @cancel="handleCancelPreview"
            :width="900"
        >
          <img  style="width: 100%;height:100%;" :src="previewImage" />
        </a-modal>
        </a-form-item>  
        <!-- <a-form-item >
          <a-checkbox  v-model="MarketForm.isCancel"> 取消订单</a-checkbox>
        </a-form-item>    -->
        <a-form-item label="备注" >
          <br>
          <a-textarea  style="width: 420px; margin-left: -40px;" :rows="4"  v-model="MarketForm.content" allowClear/>
        </a-form-item>
      </a-form>
    </div>
  </template>
  
  
  <script>
  import axios from 'axios'
  import {UploadFile,} from  "@/services/projectMake";
  export default {
      name:'MarketBack',
    data() {
      return {      
        fileListData:[],
        MarketForm:{
          "filePaths":'',
          "content": "",
          "isCancel":false,
        },
        isFileType:true,
        previewVisible:false,
        previewTitle:'',
        arrData:[],
        previewImage:"",
        isFirstTimeUpload :true,
        showUploadButton:true
      };
    },
    created(){
    },
    beforeDestroy(){
      window.removeEventListener('paste', this.getClipboardFiles);
    },
    methods: { 
      handleChange1({ fileList},data) {
        if(!fileList){        
          fileList = data.concat(this.fileListData)
        }
        if(this.isFileType){
          this.fileListData = fileList
        }
        if (this.fileListData.length >= 5){
          this.showUploadButton=false
        }
        else{
          this.showUploadButton=true
          this.isFirstTimeUpload=true
        }
        if (this.fileListData.length > 5) {
          // 只有第一次上传时提示，以后都不再提示
          if (this.isFirstTimeUpload || typeof this.isFirstTimeUpload === 'undefined') {
            this.$message.error('选择照片不可超过五张');
            this.isFirstTimeUpload = false;
          }
          this.fileListData.splice(5);
        }
        
        if (this.isFileType) {
          const removedFiles = this.fileListData.filter(file => !fileList.includes(file));
          removedFiles.forEach(file => {
            const removedElement = file.response;
            if (removedElement && this.arrData.includes(removedElement)) {
              this.arrData = this.arrData.filter(element =>  !element.includes(removedElement));
              this.MarketForm.filePaths = this.arrData.toString(',');
            }
          });
          this.fileListData = fileList;
          for (let index = 0; index < this.fileListData.length; index++) {
            const element = this.fileListData[index].response;
            if (element && !this.arrData.includes(element)) {
              this.arrData.push(element);
              this.MarketForm.filePaths= this.arrData.toString(',');
            }
          }
        }
      },
      // 上传图片路径
      async httpRequest1(data,type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await UploadFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message)
        }
      })

    },
      // // 限制上传格式
      beforeUpload1(file){
        this.isFileType = file.type.toLowerCase() === 'image/jpeg' || file.type.toLowerCase() === 'image/png' || file.type.toLowerCase() === 'image/bmp' || file.type.toLowerCase() === 'image/jpg';
        if (!this .isFileType) {
        this.$message.error("上传图片只支持.jpg/.jpeg/.png/.bmp图片格式文件");
      }
      return this.isFileType;
      },
   
      handlePreview (file) {
        this.previewVisible = true;
        this.previewImage = file.response || file.thumbUrl;
      },
      // 点击查看上传图片
      handleCancelPreview () {
        this.previewVisible = false;
      },
      
      showCopy(type) {     
      window.addEventListener('paste', this.getClipboardFiles);    
      this.showCopyType = type;
      try{    
      navigator.clipboard.read().then( res=>{
          const clipboardItems = res 
           if (clipboardItems[0].types[0].indexOf('image') > -1) {
                clipboardItems[0].getType(clipboardItems[0].types[0]).then( b=>{
                const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {type: clipboardItems[0].types[0]}); 
                this.file = files
                this.getClipboardFiles()                
              });
            }else{
              this.$message.error('粘贴内容不是图片');
              return;
            }
          })
        }catch (e) {
            ////console.log('出错了')        
        }
   
      // this.show = true;
      // this.$nextTick(() => { // 监听粘贴事件
      //   document.getElementById('Msg').addEventListener('paste', this.getClipboardFiles);
      // });
    },    
    bodyClick(){
      //console.log('bodyClick')
      window.removeEventListener('paste', this.getClipboardFiles);
    },    
    getClipboardFiles(event) {
      let file = null;
      if(event){
       const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
        // 检索剪切板items,类数组，不能使用forEach
        for (let i = 0; i < items.length; i += 1) {         
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf('image') !== -1) {
              file = items[i].getAsFile();
            }
        }
      } 
      }else{
        file = this.file
      }
      //console.log('file',file)
      if (!file) {
        this.$message.error('粘贴内容不是图片');
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error('请上传小于10M,并且格式正确的图片');
        return;
      }
      this.beforeUpload1(file);   // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload1(file)) {  // **** return true 之后进行上传
        const formData = new FormData();
       // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append('file', file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + '/api/app/engineering-production/up-load-back-file',   // 接口地址
          method: 'post',
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => { 
            if (res.code) {
              //console.log('res',res)
              file.status = 'done';
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              //console.log('this.showCopyType',this.showCopyType)
              if(this.showCopyType == '1'){
                this.handleChange1(file, arr); 
              }          
             
            } else {
              this.$message.error(data.message || '网络异常,请重试或检查网络连接状态');             
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
    },
   
  };
  </script>
  <style scoped lang="less">

  .ChargebackMake{
    /deep/.ant-input{
      margin-top: -40px;
      font-weight: 500;
    }
   /deep/ .ant-form{
      .ant-form-item{
        margin: 0!important;
      }
    }
   .pictureListSty{
     width: 400px !important;
     margin-left: 50px;
      /deep/ .ant-upload-list-item-actions{
      a{
        display: none;
      }
    }
   }
    /deep/ .ant-checkbox-wrapper{
      margin-left: 0!important;
    }
  }
  
  </style>