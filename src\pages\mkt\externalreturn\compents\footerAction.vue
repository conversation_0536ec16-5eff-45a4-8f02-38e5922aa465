<!--
 * @Author: CJP
 * @Date: 2022-06-16 18:33:32
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-20 18:50:29
 * @FilePath: \vue-antd-admin\src\pages\mkt\externalreturn\compents\footerAction.vue
 * @Description: 
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved. 
-->
<template>
<div class='bto'>
    <a-button type="primary" @click="onClick">
      刷新表格
    </a-button>
    <a-button type="primary" @click="modelClick">
     取单设置
    </a-button>
    
</div>
</template>
<script>
 

export default {
name:'footerAction',
data(){
    return{
    
    }
},

    
methods:{
    onClick(){
        this.$emit('onClick')
    },
    modelClick(){
        this.$emit('modelClick')
    }
}
   
}
</script>
<style scoped lang='less'>
.bto{
    display: flex;
    width:20%;
    float: right;
    justify-content: space-evenly
}
.top{
    width:100%;
    height:500px;
    border: 2px solid black;
    display:flex;
    .left{
        width:49%;
        height: 100%;
        border: 2px solid red;
        display:inline-block;
    }
    .center{
        width:28%;
        height: 100%;
        border: 2px solid green;
        display:inline-block;
    }
    .right{
        width:19%;
        height: 100%;
        border: 2px solid blue;
        display:inline-block;
    }
}
.footer{
    width:100%;
    height:50px;
    border: 2px solid black;

}
</style>
