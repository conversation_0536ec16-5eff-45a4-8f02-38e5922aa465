import { request, METHOD } from '@/utils/request';
// 价格区域列表
export function mktPriceManage4AreaID (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/mkt-price-manage4Area-iD", METHOD.GET,params)
}
// 区域客户及各费用
export function mkTPriceRuleData4CalcCustArea (params,CustNo) {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/m-kTPrice-rule-data4Calc-cust-area?AreaID=${params}&CustNo=${CustNo}`, METHOD.GET,)
}
//获取客户区域参数展示
export async function pricemanage4AreaiD () {
    return request('/api/app/e-mSOrder-price-calc-result4Parameter/price-manage4Area-iD ', METHOD.GET)
}
//新增区域客户保存
export function newpricemanage4AreaiD (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/new-price-manage4Area-iD", METHOD.POST,params)
}
//修改客户区域
export function uppricemanage4AreaiD (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/up-price-manage4Area-iD", METHOD.POST,params)
}
// 新增制板费数据
export function basicPrice (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/basic-price", METHOD.POST,params)
}
// 修改制板费数据
export function upbBasicPrice (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/up-basic-price", METHOD.POST,params)
}
// 新增区域公式
export function formula (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/formula ", METHOD.POST,params)
}
// 修改区域公式
export function upformula (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/up-formula", METHOD.POST,params)
}
//获取选择项
export async function getselect () {
    return request('/api/app/e-mSOrder-price-calc-result4Parameter/getselect ', METHOD.GET)
}
export function delBasicPrice (Id) {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/del-basic-price/${Id}`, METHOD.POST,)
}
export function engPrice (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/eng-price", METHOD.POST,params)
}
export function upbEngPrice (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/up-eng-price", METHOD.POST,params)
}
export function delEngPrice (Id) {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/del-eng-price/${Id}`, METHOD.POST,)
}
export function delformula (Id) {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/del-formula/${Id}`, METHOD.POST,)
}
export async function mktcustno () {
    return request('/api/app/e-mSOrder-price-calc-result4Parameter/mkt-cust-no', METHOD.GET)
}
//新增区域对应客户
export function areaCust (val) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/area-cust", METHOD.POST,val)
}
//金手指价展示
export async function mktgoldfingerprice () {
    return request('/api/app/e-mSOrder-price-calc-result4Parameter/mkt-gold-finger-price', METHOD.GET)
}
//新增金手指价保存
export function goldfingerprice (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/gold-finger-price", METHOD.POST,params)
}
//编辑金手指价保存
export function upgoldfingerprice (params) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/up-gold-finger-price", METHOD.POST,params)
}
//删除金手指价
export function delgoldfingerprice (Id) {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/del-gold-finger-price/${Id}`, METHOD.POST,)
}
//删除区域对应客户
export function delareacust (val) {
    return request("/api/app/e-mSOrder-price-calc-result4Parameter/del-area-cust", METHOD.POST,val)
}
export function exportPrice (Id) {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/export-price/${Id}`, METHOD.POST,)
}
export function mktCustNo () {
    return request(`/api/app/e-mSOrder-price-calc-result4Parameter/mkt-cust-no`, METHOD.GET,)
}

