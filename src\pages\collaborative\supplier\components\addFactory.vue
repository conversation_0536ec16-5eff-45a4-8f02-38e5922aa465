<template>
  <div ref="sb">
    <a-modal
    :title="model.id? '编辑' : '新增'"
    :width="450"
    :visible="visible"
    centered
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="区域" ref="companyArea_" prop="companyArea_">
            <a-select   v-model="form.companyArea_" :getPopupContainer="()=>this.$refs.sb" >
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="404700">
                    压机
                </a-select-option>
                <a-select-option value="404701">
                    钻孔
                </a-select-option>
                <a-select-option value="404702">
                    电镀
                </a-select-option>
                <a-select-option value="404703">
                    线路
                </a-select-option>
                <a-select-option value="404704">
                    阻焊
                </a-select-option>
                <a-select-option value="404705">
                    文字
                </a-select-option>
                <a-select-option value="404706">
                    成型
                </a-select-option>
                <a-select-option value="404707">
                    测试
                </a-select-option>
            </a-select>
        </a-form-model-item>
         <a-form-model-item  label="上传图片" v-if="form.id" ref="imgs" prop="imgs">
            <div class="clearfix">
              <a-upload
              accept=".jpg,.png,.gif,.bmp,.jpeg,"  
              name="file"
              ref="fileRef"
              :multiple="true"
              :before-upload="beforeUpload1"
              :customRequest="downloadFilesCustomRequest"
              @preview="handlePreview"
              :file-list="fileListData"
              @change="handleChange1"
              list-type="picture-card"    
              >
                <div>
                  <a-icon type="plus" />
                  <div class="ant-upload-text">
                    Upload
                  </div>
                </div>
              </a-upload>
            </div>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
  </div>
  
</template>

<script>
import { addFactory, uploadImg, getPhoto ,progressNum} from "@/services/supplier/index";
import { modulenouploadfile} from "@/services/supplier/index";
function getBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
      compileApply:{
          type: String,
          default () {
              return ''
          }
      },
      message:{
          type: String,
          default () {
              return ''
          }
      }
  },
  data() {
    return {
      isFileType:true,
      path:'', // 上传图片返回地址
      logoAddress:[],
      fileListData:[],
      baseURL: process.env.VUE_APP_API_BASE_URL,
      labelCol: { span: 5},
      wrapperCol: { span: 19 },
      visible: false,
      confirmLoading: false,
      arrData:[],
      form: {
        companyArea_: ""
      },
      rules: {
        companyArea_: [
          { required: true, message: "名称必须填写", trigger: "blur" },
        ],
      },
      uploading: false,
      model: '',
      fileList: [],
    };
  },
  methods: {
    handleChange1({ fileList },data) { 
      if (this.isFileType) { 
        this.fileListData = fileList;      
        for (let index = 0; index < this.fileListData.length; index++) {
          const element = this.fileListData[index].response;
          if (element && !this.arrData.includes(element)) {
            this.arrData.push(element);
            this.path = this.arrData.toString(',');
          }
        }
        this.fileListData.forEach(ite=>{
          if(ite.url){
          this.arrData = [...new Set(this.arrData.concat(ite.url))];
          this.path = this.arrData.toString(',');
          }
         })
        if (this.fileListData.length === 0) {
          this.path = '';
        }
      }
    },
        beforeUpload1(file){
            this.isFileType = file.name.toLowerCase().indexOf('.jpg') != -1 || file.name.toLowerCase().indexOf('.png') != -1

            if (!this.isFileType) {
            this.$message.error('上传图片只支持.jpg/.png图片格式文件');
            }
            return this.isFileType

    },
    async downloadFilesCustomRequest(data,type){ 
      const formData = new FormData()
      formData.append('file', data.file)
      await modulenouploadfile(formData).then(res =>{
        if (res.code) {
            data.onSuccess(res.data); 
            this.logoAddress.push(res.data)
            this.path = this.logoAddress.toString(',')
        } else {
            this.$message.error(res.message)
         }
        
      })
      
    },  
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
        this.$nextTick(() => {
        this.$viewerApi({
          images: [file.url || file.preview],
        });
      });
    },
    handleChange({ fileList }) {
      this.fileList = fileList;
    },
    openModal(model) {
            this.visible = true;
            this.model = model
            this.fileList = []
            if(model && model.id) {
                this.form = {
                    id:model.id,
                    companyArea_: model.companyArea_,
                };
                this.getPhotos(model.id)
            }else {
                this.form ={
                    companyArea_: '',
                };
            }
        // }else {
        //     this.$message.info(this.message)
        // }

    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
      this.$refs.ruleForm.resetFields()
    },
    //查看图片
    getPhotos(id) {
        getPhoto(id)
            .then((res) => {
                res.forEach(e=>{
                  let info = {
                    uid: e.id,
                    name: 'image.png',
                    status: 'done',
                    url: e.strUrl_
                  }
                 this.fileList.push(info)
                })

            })
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {

          if(this.model.id) {
              let str = ''
              this.fileList.forEach(e=> {
                if(e.response) {
                  str += e.response + ','
                }else if(e.url) {
                  str += e.url + ','
                }

              })
              str = str.substring(0,str.length-1)
              let fileData = {
                pGuid_: this.model.id,
                strUrl_: this.path
              }
              uploadImg(fileData)
                .then((res) => {
                    console.log(res)
                })

          }

          let params = {
              id: this.model.id,
              pGuid_: this.suppId,
              companyArea_: this.form.companyArea_
          }

          addFactory(params)
              .then((res) => {
                  this.visible = false;
                  form.resetFields();
                  this.$message.info("操作成功");
                  this.fileList = []
                  this.$emit("ok");
                  progressNum(this.suppId).then(res=>{
                      if(res.code!==1){
                          this.$message.info(res.message)
                      }
                  })
              })
              .finally(() => {
              this.confirmLoading = false;
          });


        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-form-item{
    margin-bottom: 5px;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item{

 font-weight: 500;

}
</style>
