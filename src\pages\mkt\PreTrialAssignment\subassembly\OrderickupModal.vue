<template>
  <div ref="SelectBox">
    <a-form-model :model="data" layout="inline">
      <a-row>
        <a-col :span="8">
          <a-form-item label="雇员姓名" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
            <a-input v-model="data.realName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="14">
          <a-form-item label="目标数量" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
            <a-input v-model="data.targetNum" allowClear />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-item label="单次获取" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
            <a-input v-model="data.singleAcquisition" allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="14">
          <a-form-item label="停留数量" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
            <a-input v-model="data.stayNum" allowClear />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-model-item label="当前状态" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
            <a-tag :color="data.isAskForLeave ? '#87d068' : '#f50'">
              {{ data.isAskForLeave ? "休息中" : "正常" }}
            </a-tag>
            <a-button :type="data.isAskForLeave ? 'danger' : 'primary'" size="small" @click="statusClick()">
              {{ data.isAskForLeave ? "销假" : "请假" }}
            </a-button>
          </a-form-model-item>
        </a-col>
        <a-col :span="14">
          <a-form-model-item
            label="黑名单代码"
            ref="custNo"
            prop="custNo"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 14 }"
            style="width: 100%; margin: 0"
          >
            <a-select
              placeholder="该选中代码无法取单"
              :getPopupContainer="() => this.$refs.SelectBox"
              v-model="data.custNo"
              :dropdownMatchSelectWidth="false"
              showSearch
              :autoFocus="autoFocus"
              optionFilterProp="children"
              @popupScroll="handlePopupScroll"
              allowClear
              @search="supValue"
              mode="multiple"
            >
              <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="4">
          <a-form-item label="自动发单" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
            <a-checkbox v-model="data.isAutoSendOrder" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="预审值班" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }" style="width: 100%; margin: 0">
            <a-checkbox v-model="data.beonDuty" />
          </a-form-item>
        </a-col>
        <a-col :span="14">
          <a-form-item label="自动发单时间" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
            <a-time-picker format="HH:mm" style="width: 120px" v-model="data.autoSendStartDate" />
            -
            <a-time-picker format="HH:mm" style="width: 120px" v-model="data.autoSendEndDate" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-item label="显示顺序" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
            <a-input-number v-model="data.sort" :min="1" />
          </a-form-item>
        </a-col>
        <a-col :span="14">
          <a-form-model-item
            label="优先客户代码"
            ref="orderByCustNo"
            prop="orderByCustNo"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 14 }"
            style="width: 100%; margin: 0"
          >
            <a-select
              placeholder="请选择优先客户代码"
              :getPopupContainer="() => this.$refs.SelectBox"
              v-model="data.orderByCustNo"
              :dropdownMatchSelectWidth="false"
              showSearch
              :autoFocus="autoFocus"
              optionFilterProp="children"
              @popupScroll="handlePopupScroll"
              allowClear
              @search="supValue"
              mode="multiple"
            >
              <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-item label="精细预审" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
            <a-checkbox v-model="data.finePre"></a-checkbox>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import { mktCustNo } from "@/services/mkt/Inquiry.js";
import Cookie from "js-cookie";
import moment from "moment";
export default {
  data() {
    return {
      autoFocus: false,
      frontDataZSupplierf: [], // 供应商 100条数据的集合
      frontDataZSupplier: [], // 供应商 100条数据的集合
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
    };
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  mounted() {
    this.getSupplier();
  },
  methods: {
    getSupplier() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("mktCustNo"));
      if (data && token) {
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          this.supList = element.data;
          this.frontDataZSupplierf = element.data.slice(0, 20);
          this.frontDataZSupplier = this.frontDataZSupplierf;
        }
      } else {
        mktCustNo().then(res => {
          if (res.code) {
            let that = this;
            that.supList = res.data;
            that.frontDataZSupplierf = res.data.slice(0, 20);
            that.frontDataZSupplier = that.frontDataZSupplierf;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(that.supList) != "{}") {
              if (data == null) {
                arr.push({ data: that.supList, token });
                localStorage.setItem("mktCustNo", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: that.supList, token });
                localStorage.setItem("mktCustNo", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value;
        let arr = that.supList.filter(m => m.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
          this.supValue("");
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.supList.slice(0, 20);
      }
    },
    statusClick() {
      this.$emit("statusClick");
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-input-number {
  width: 105px;
}
</style>
