<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1" style="font-size: 13px;" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ReportLtreportform1099" style=" padding: 25px;font-family: 等线;color: black;font-weight: 600;" >
            <div style="width: 100%;display: flex;">
                    <div style="text-align: center;width: 100%;">
                        <img   src="@/assets/img/lt.png" style="height: 70px;">
                        <div>
                            <span style="font-size: 32px;font-weight: bold;letter-spacing: 5px;">报价单</span>
                        </div>    
                    </div>                            
            </div>
            <div style="display: flex;line-height: 3ch;">
                <div style="width:50%;z-index: 99;font-size: 15px;">
                    <div>客户名称(Customer name):{{ LTreportdata.name_ }}</div>
                    <div>联系人(connect person):{{ LTreportdata.party_ }}</div>
                    <div>电话(tel No.):{{ LTreportdata.facPhone_ }}</div>
                    <div>传真(fax No.):{{ LTreportdata.facFax_ }}</div>
                </div>
                <div style="z-index: 99;width:50%">
                    <div style="float: right;font-size: 15px;">
                        <div>名称(marketing name):{{ LTreportdata.factory_ }}</div>
                        <div>联系人(connect person):{{ LTreportdata.link_ }}</div>
                        <div>电话(tel No.):{{ LTreportdata.tel_ }}</div>
                        <div>传真(fax No.):{{ LTreportdata.fax_ }}</div>
                        <div>报价日期:{{ LTreportdata.date_ }}</div>
                    </div>                   
                </div>               
            </div>
            <div>
                <table border="1" style="text-align: center;margin-top: 5px;width: 100%;border-top: 1px solid black;border-left: 1px solid black;">
                    <thead>
                        <tr style="font-size: 15px;">
                            <td>No</td>
                            <td style="width: 110px;">产品名称<br/>Part Number</td>
                            <td>物料编码</td>
                            <td>规格要求<br/>Description</td>
                            <td colspan="2">尺寸(SIZE)MM</td>
                            <td>拼板<br/>panel</td>
                            <td>层数</td>
                            <td>表面处理<br/>(Finish) </td>
                            <td>数量<br/>quantity</td>
                            <td>工程费</td>
                            <td>单 价price(RMB:元/PCS)</td>
                            <td>测具费 </td>
                            <td>合计 </td>
                            <td>备注</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in LTreportdata.price' :key="index">
                            <td>{{ item.no }}</td>
                            <td>{{ item.custName }}</td>
                            <td>{{ item.mat }}</td>
                            <td>{{ item.pinBanType }}</td>
                            <td>{{ item.setBoardWidth }}</td>
                            <td>{{ item.setBoardHeight }}</td>
                            <td>{{ item.lay }}</td>
                            <td>{{ item.su }}</td>
                            <td>{{ item.surface }}</td>
                            <td>{{ item.setQty }}</td>
                            <td>{{ item.eng }}</td>
                            <td>{{ item.poreDensity }}</td>
                            <td>{{ item.test }}</td>
                            <td>{{ item.pcs }}</td>
                            <td>{{ item.notes }}</td>
                        </tr>
                        <tr  style="font-size: 15px;">
                            <td></td>
                            <td colspan="11"></td>
                            <td>总金额</td>
                            <td>{{ amountto }}</td>
                            <td></td>
                        </tr>
                    </tbody>                
                </table>
            </div>
            <div style="z-index:99;position: relative;">
                <div style="display: flex;line-height:2.8ch;margin-top: 8px;font-size: 15px;">
                    <div>
                        <div>备注(Remark):</div>
                       <span style="width: 50px;display: inline-block;text-align: center;"> 1 </span>交货方式（Delivery）：{{ LTreportdata.deliveryType }}<br/>
                       <span style="width: 50px;display: inline-block;text-align: center;"> 2 </span> 交货地点（Delivery add）：{{ LTreportdata.currency_ }}<br/>
                       <span style="width: 50px;display: inline-block;text-align: center;color: red;"> 3 </span> <span style="color: red;"> 付款方式(Payment method)：{{ LTreportdata.clearingForm }}</span><br/>
                       <span style="width: 50px;display: inline-block;text-align: center;"> 4 </span> 价格含税状况（Tax）：{{ LTreportdata.taxPercentage_ }}<br/>
                       <span style="width: 50px;display: inline-block;text-align: center;"> 5 </span> 此报价有效期（Period of time）：{{ LTreportdata.pact_ }}<br/>
                       <span style="width: 50px;display: inline-block;text-align: center;"> 6 </span> 验收标准：{{ LTreportdata.ipcLevel }}<br/>
                    </div>
                </div>
            </div>
            <div style="display: flex;width: 100%;padding-top: 15px;font-size: 15px;z-index: 99;position: relative;">
                <div style="width: 40%;">
                    <div>客&nbsp; 户&nbsp;确&nbsp;认:<span class="Underline"></span>&nbsp; &nbsp;</div>
                    <div>Cust Confirm</div>
                    <div>日期(DATE):<span class="Underline"></span>&nbsp; &nbsp;</div>
                </div>
                <div style="width: 35%;">
                    <div>核准:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="Underline"></span>&nbsp; &nbsp;{{ LTreportdata.discer }}</div>
                    <div>QUOTE</div>
                    <div>日期(DATE):<span class="Underline"></span>&nbsp; &nbsp;</div>
                </div>
                <div style="width: 25%;">
                    <div>经办人:&nbsp; &nbsp; &nbsp; &nbsp; <span class="Underline"></span>&nbsp; &nbsp;{{ LTreportdata.quoter }}</div>
                    <div>Confirmed by</div>
                    <div>日期(DATE):<span class="Underline"></span>&nbsp; &nbsp;{{ LTreportdata.date_ }}</div>
                </div>
            </div>
            <!-- <img  src="@/assets/img/lthtz.png" 
             style="position: relative;
                    top: -130px;
                    z-index: 0;
                    display: block;
                    left: 954px;
                    width: 150px;
                    transform: rotate(353deg);"  >     -->
        </div>
    </div>
  </template>
  <script>
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        amountto:0,
        printObj1:{
            id: "ReportLtreportform1099", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
        this.amountto =0 
        for (let index = 0; index < this.LTreportdata.price.length; index++) {
            if(this.LTreportdata.price[index].pcs && this.LTreportdata.price[index].pcs!='/'){
                this.amountto +=Number(this.LTreportdata.price[index].pcs)
            } 
        }
        this.amountto = this.amountto ? this.getFloat(this.amountto,4) : 0
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            htmlToPdfa3('ReportLtreportform1099',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
   .Underline {
    position: relative;
    display: inline-block;
    }
    .Underline::after {
    content: "";
    position: absolute;
    left: 0;
    bottom:-8px; /* 下划线距离文字底部的距离 */
    width: 200px; /* 下划线宽度 */
    height: 2px; /* 下划线高度 */
    background-color: rgb(107, 106, 106); /* 下划线颜色 */
    }
  .pdfDom1{
      height: 650px;
      overflow: auto;
      table >thead> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
    table >tbody> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
  }
  </style>
