<!-- 市场管理 - 订单报价- 返回订单报价 -->
<template>
  <a-form-model layout="inline">
    <a-form-model-item v-if="checkPermission('MES.MarketModule.Check.CheckFanHui')">
      <!--  @click="$router.go(-1)" -->
      <a-button       
        @click="returnClick"
        type="primary"
      >
        返回订单报价
      </a-button>
    </a-form-model-item>
    <!-- <a-form-model-item v-if="checkPermission('MES.MarketModule.Check.CheckEdit')"> -->
      <!-- <a-button
          :type="editFlag ? 'dashed' : 'primary'"
          @click="edit"
      >
        {{ editFlag ? '取消' : '编辑' }}
      </a-button> -->
<!--      
      <a-button
          @click="edit"
          type="primary"
          v-if="!editFlag"
      >
       编辑
      </a-button>
    </a-form-model-item> -->
    <!-- <a-form-model-item v-if="editFlag">
      <a-button
          type="primary"
          @click="save"
      >
        保存
      </a-button>
    </a-form-model-item> -->
    
  </a-form-model>
</template>

<script>
import {graphicPreview,
} from "@/services/mkt/PrequalificationProduction.js";
import {checkPermission} from "@/utils/abp";
export default {
  name: "OrderAction",
  props:['editFlag'],
  methods: {
    checkPermission,
    edit(){
      this.$emit('editInfo')
    },
    save(){
      this.$emit('dataSave')
    },
    returnClick(){
      this.$nextTick(function () {  
        this.$router.push({path: '/shichang/OrderOffer', })
      }) 
    }
    
    
  }
}
</script>

<style scoped>

</style>