<!--控制台- 市场分析页 -->
<template>
  <div class="analysis">
    <div style="height: 100%">
      <div style="display: flex; height: 652px">
        <div class="leftdata">
          <div>
            <a-row>
              <a-col :span="12">
                <a-form-model-item label="工厂筛选" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
                  <a-select showSearch allowClear optionFilterProp="lable" placeholder="授权工厂" v-model="FactoryId" @change="Factorychange">
                    <a-select-option
                      style="color: blue"
                      v-for="(item, index) in mapKey(factroyList)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-if="showsource">
                <a-form-model-item label="订单来源" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
                  <a-select showSearch allowClear optionFilterProp="lable" v-model="OrderSource" @change="Factorychange">
                    <a-select-option value="线下">线下</a-select-option>
                    <a-select-option value="电商">电商</a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
          <div class="bac">
            <a-spin :spinning="dayLoading" style="height: 100%">
              <div style="display: flex">
                <a-button type="primary" shape="round" :class="classification1 ? 'dayweek' : 'weekmonth'" @click="Inquiryclick('1')"> 当日 </a-button>
                <a-button type="primary" shape="round" :class="classification2 ? 'dayweek' : 'weekmonth'" @click="Inquiryclick('2')"> 周度 </a-button>
                <a-button type="primary" shape="round" :class="classification3 ? 'dayweek' : 'weekmonth'" @click="Inquiryclick('3')"> 月度 </a-button>
                <a-range-picker
                  style="width: 260px; margin: 15px 0 15px 50px; border-radius: 5px"
                  @change="rangechange"
                  v-model="dateString"
                  allowClear
                />
              </div>
              <div class="Orderingsituation">
                <div style="display: flex" v-if="!dayLoading">
                  <div class="data1" style="height: 76px; line-height: 76px">总询单量</div>
                  <div>
                    <div class="data2" style="height: 35px; line-height: 35px">
                      {{ Orderingdata.allXdnum }}款 <span style="font-size: 12px; margin-left: 10px">同比-13%</span>
                    </div>
                    <div class="data2" style="height: 35px; line-height: 35px">
                      {{ Orderingdata.allXdarea }}㎡<span style="font-size: 12px; margin-left: 10px">同比-13%</span>
                    </div>
                  </div>
                </div>
                <div style="display: flex" v-if="!dayLoading">
                  <div class="data1">总询单金额</div>
                  <div class="data2">{{ Orderingdata.allXdamount }}万元 <span style="font-size: 12px; margin-left: 10px">同比-3%</span></div>
                </div>
                <div style="display: flex" v-if="!dayLoading">
                  <div class="data1" style="height: 76px; line-height: 76px">总下单量</div>
                  <div>
                    <div class="data2" style="height: 35px; line-height: 35px">
                      {{ Orderingdata.allOfflinenum }}款 <span style="font-size: 12px; margin-left: 10px">同比-8%</span>
                    </div>
                    <div class="data2" style="height: 35px; line-height: 35px">
                      {{ Orderingdata.allOfflinearea }}㎡<span style="font-size: 12px; margin-left: 10px">同比-13%</span>
                    </div>
                  </div>
                </div>
                <div style="display: flex" v-if="!dayLoading">
                  <div class="data1">总下单金额</div>
                  <div class="data2">{{ Orderingdata.allOfflineamount }}万元 <span style="font-size: 12px; margin-left: 10px">同比-2%</span></div>
                </div>
                <div style="display: flex" v-if="!dayLoading">
                  <div class="data1">加急费</div>
                  <div class="data2">{{ Orderingdata.allExpeditePrice }}万元 <span style="font-size: 12px; margin-left: 10px">同比-9%</span></div>
                </div>
                <div style="display: flex" v-if="!dayLoading">
                  <div class="data1">平米价</div>
                  <div class="data2">{{ Pricepersquaremeter.toFixed(2) }}元/㎡ <span style="font-size: 12px; margin-left: 10px">同比-7%</span></div>
                </div>
              </div>
              <div style="display: flex; height: 0">
                <div id="main" class="left1"></div>
                <div id="main1" class="left1"></div>
              </div>
            </a-spin>
          </div>
        </div>
        <div class="rightdata">
          <div class="top2">
            <a-spin :spinning="rateLoading">
              <div id="columnar" class="chievementrate"></div>
              <table
                border="1"
                style="width: 13%; color: rgba(255, 0, 0, 0); font-size: 8px; text-align: center; margin-top: -24px; margin-left: 7px"
              >
                <thead style="height: 21px">
                  总下单金额（万元）
                </thead>
                <tbody>
                  <tr>
                    <td style="height: 21px">总下单金额（万元）</td>
                  </tr>
                  <tr>
                    <td style="height: 21px">总下单金额（万元）</td>
                  </tr>
                </tbody>
              </table>
              <table
                border="1"
                style="width: 82%; color: rgb(121, 128, 137); margin-top: -85px; margin-left: 136px; font-size: 8px; text-align: center"
              >
                <thead>
                  <tr>
                    <td v-for="(item, index) in ratemonth" :key="index" style="width: 48px; height: 21px">{{ item }}月</td>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in TableData" :key="index">
                    <td style="height: 21px">{{ item.month1 }}</td>
                    <td style="height: 21px">{{ item.month2 }}</td>
                    <td style="height: 21px">{{ item.month3 }}</td>
                    <td style="height: 21px">{{ item.month4 }}</td>
                    <td style="height: 21px">{{ item.month5 }}</td>
                    <td style="height: 21px">{{ item.month6 }}</td>
                    <td style="height: 21px">{{ item.month7 }}</td>
                    <td style="height: 21px">{{ item.month8 }}</td>
                    <td style="height: 21px">{{ item.month9 }}</td>
                    <td style="height: 21px">{{ item.month10 }}</td>
                    <td style="height: 21px">{{ item.month11 }}</td>
                    <td style="height: 21px">{{ item.month12 }}</td>
                  </tr>
                </tbody>
              </table>
              <div v-if="!rateLoading" style="font-size: 16px; color: rgb(121, 128, 137); text-align: center; margin-top: -300px">商机达成率</div>
              <img v-if="!rateLoading" style="width: 100%; margin-top: -33px; height: 100%" src="@/assets/img/kb.png" />
            </a-spin>
          </div>
          <div class="bot2">
            <a-spin :spinning="rateLoading1">
              <div id="columnar1" class="chievementrate"></div>
              <table
                border="1"
                style="width: 13%; color: rgba(121, 128, 137, 0); font-size: 8px; text-align: center; margin-top: -24px; margin-left: 7px"
              >
                <thead style="height: 21px">
                  总下单金额（万元）
                </thead>
                <tbody>
                  <tr>
                    <td style="height: 21px">总下单金额（万元）</td>
                  </tr>
                  <tr>
                    <td style="height: 21px">总下单金额（万元）</td>
                  </tr>
                </tbody>
              </table>
              <table
                border="1"
                style="width: 82%; color: rgb(121, 128, 137); margin-top: -85px; margin-left: 136px; font-size: 8px; text-align: center"
              >
                <thead>
                  <tr>
                    <td v-for="(item, index) in ratemonth" :key="index" style="width: 48px; height: 21px">{{ item }}月</td>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in mountData" :key="index">
                    <td style="height: 21px">{{ item.month1 }}</td>
                    <td style="height: 21px">{{ item.month2 }}</td>
                    <td style="height: 21px">{{ item.month3 }}</td>
                    <td style="height: 21px">{{ item.month4 }}</td>
                    <td style="height: 21px">{{ item.month5 }}</td>
                    <td style="height: 21px">{{ item.month6 }}</td>
                    <td style="height: 21px">{{ item.month7 }}</td>
                    <td style="height: 21px">{{ item.month8 }}</td>
                    <td style="height: 21px">{{ item.month9 }}</td>
                    <td style="height: 21px">{{ item.month10 }}</td>
                    <td style="height: 21px">{{ item.month11 }}</td>
                    <td style="height: 21px">{{ item.month12 }}</td>
                  </tr>
                </tbody>
              </table>
              <div v-if="!rateLoading1" style="font-size: 16px; color: rgb(121, 128, 137); text-align: center; margin-top: -300px">淘金率</div>
              <img v-if="!rateLoading1" style="width: 100%; margin-top: -33px; height: 100%" src="@/assets/img/kb.png" />
            </a-spin>
          </div>
        </div>
      </div>
      <div class="botdata">
        <a-spin :spinning="botloading">
          <div id="botmain" class="bot"></div>
        </a-spin>
      </div>
      <div class="Category_distribution">
        <a-spin :spinning="Categoryload" style="height: 100%">
          <div style="display: flex">
            <a-button type="primary" shape="round" :class="Category_distribution1 ? 'dayweek' : 'weekmonth'" @click="Categoryclick('1')">
              当日
            </a-button>
            <a-button type="primary" shape="round" :class="Category_distribution2 ? 'dayweek' : 'weekmonth'" @click="Categoryclick('2')">
              周度
            </a-button>
            <a-button type="primary" shape="round" :class="Category_distribution3 ? 'dayweek' : 'weekmonth'" @click="Categoryclick('3')">
              月度
            </a-button>
            <a-range-picker
              style="width: 260px; margin: 15px 0 15px 50px; border-radius: 5px"
              @change="Categorychange"
              v-model="dateCategory"
              allowClear
            />
          </div>
          <div style="display: flex; height: 0">
            <div v-for="(item, index) in arr1" :key="index" class="left1" :id="item"></div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts/core";
import { TooltipComponent, LegendComponent } from "echarts/components";
import { PieChart } from "echarts/charts";
import { LabelLayout } from "echarts/features";
import { SVGRenderer } from "echarts/renderers";
import moment from "moment";
echarts.use([TooltipComponent, LegendComponent, PieChart, SVGRenderer, LabelLayout]);
import {
  mkTOrderdatastatistics,
  factroyList,
  mkTOrderdatastatisticsv1,
  mkTOrderdatastatisticsv2,
  mkTOrderdatastatisticsv3,
  bodatastatistics,
  modatastatistics,
  timestatistics,
  mkTOrder4Xdstatistics,
  mkTOrder4Xdstatisticsv1,
  mkTOrder4Xdstatisticsv2,
  mkTOrder4Xdstatisticsv3,
} from "@/services/analysis";

export default {
  props: [],
  data() {
    return {
      Pricepersquaremeter: 0,
      Categoryload: false,
      CategoryData: {},
      arr1: [],
      classification1: true,
      classification2: false,
      classification3: false,
      Category_distribution1: true,
      Category_distribution2: false,
      Category_distribution3: false,
      Category_distribution4: false,
      dateString: [],
      dateString1: [],
      dateCategory: [],
      factroyList: [],
      TableData: [],
      mountData: [],
      Orderingdata: {},
      FactoryId: undefined,
      OrderSource: "",
      date: moment().format("YYYY-MM-DD"),
      dayLoading: false,
      rateLoading: false,
      rateLoading1: false,
      showsource: false,
      botloading: false,
      classification4: false,
      ratevalue: [],
      ratemonth: [],
      ordervalue: [],
      inquiryvalue: [],
      Golddata: [],
      Orderamount: [],
      Inquiryamount: [],
      averagetime: [],
    };
  },
  mounted() {
    factroyList().then(res => {
      if (res.code) {
        this.factroyList = res.data;
        this.showsource = this.mapKey(this.factroyList).some(val => val.value == "12");
      } else {
        this.$message.error(res.message);
      }
    });
  },
  created() {
    this.Inquiryclick("1");
    this.Categoryclick("1");
    this.Achievementrate();
    this.Goldrushrate();
    this.timestatistics();
  },
  methods: {
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    Factorychange() {
      if (this.classification1) {
        this.Inquiryclick("1");
      } else if (this.classification2) {
        this.Inquiryclick("2");
      } else if (this.classification3) {
        this.Inquiryclick("3");
      } else if (this.classification4) {
        this.Intervalselection();
      }
      if (this.Category_distribution1) {
        this.Categoryclick("1");
      } else if (this.Category_distribution1) {
        this.Categoryclick("2");
      } else if (this.Category_distribution1) {
        this.Categoryclick("3");
      } else if (this.Category_distribution1) {
        this.Categorychange();
      }
      this.Goldrushrate();
      this.Achievementrate();
      this.timestatistics();
    },
    Categoryclick(type) {
      let fetch1;
      this.Category_distribution1 = false;
      this.Category_distribution2 = false;
      this.Category_distribution3 = false;
      this.Category_distribution4 = false;
      if (type == "1") {
        this.Category_distribution1 = true;
        fetch1 = mkTOrder4Xdstatisticsv2;
      } else if (type == "2") {
        this.Category_distribution2 = true;
        fetch1 = mkTOrder4Xdstatisticsv1;
      } else if (type == "3") {
        this.Category_distribution3 = true;
        fetch1 = mkTOrder4Xdstatistics;
      }
      this.dateCategory = [];
      const faid = this.FactoryId || "";
      this.arr1 = [];
      this.Categoryload = true;
      fetch1(this.date, faid, this.OrderSource)
        .then(res => {
          if (res.code) {
            this.CategoryData = res.data;
            let colorlist = ["#f37e50", "#cd2e9c", "#3ec8f0", "#f37e50", "#cd2e9c"];
            let textlist = ["双面板", "4-6层", "8-10层", ">10层", "HDI板"];
            Object.keys(this.CategoryData).forEach((item, index) => {
              this.arr1.push(item);
              this.Proportion(item, colorlist[index], textlist[index]);
            });
          }
        })
        .finally(() => {
          this.Categoryload = false;
        });
    },
    Inquiryclick(type) {
      let fetch1;
      this.dayLoading = true;
      this.classification1 = false;
      this.classification2 = false;
      this.classification3 = false;
      this.dateString = [];
      if (type == 1) {
        this.classification1 = true;
        fetch1 = mkTOrderdatastatisticsv2;
      } else if (type == 2) {
        this.classification2 = true;
        fetch1 = mkTOrderdatastatisticsv1;
      } else if (type == 3) {
        this.classification3 = true;
        fetch1 = mkTOrderdatastatistics;
      }
      var faid = this.FactoryId || "";
      fetch1(this.date, faid, this.OrderSource)
        .then(res => {
          if (res.code) {
            this.Orderingdata = res.data;
            if (Number(this.Orderingdata.allOfflineamount) && Number(this.Orderingdata.allOfflinearea)) {
              this.Pricepersquaremeter = (Number(this.Orderingdata.allOfflineamount) / Number(this.Orderingdata.allOfflinearea)) * 10000;
            }
            this.drawLine();
            this.drawLine1();
          }
        })
        .finally(() => {
          this.dayLoading = false;
        });
    },
    Intervalselection() {
      this.dayLoading = true;
      var faid = this.FactoryId || "";
      var Startdate = localStorage.getItem("dateString").split(",")[0];
      var Enddate = localStorage.getItem("dateString").split(",")[1];
      mkTOrderdatastatisticsv3(Startdate, Enddate, faid, this.OrderSource)
        .then(res => {
          if (res.code) {
            this.Orderingdata = res.data;
            if (Number(this.Orderingdata.allOfflineamount) && Number(this.Orderingdata.allOfflinearea)) {
              this.Pricepersquaremeter = (Number(this.Orderingdata.allOfflineamount) / Number(this.Orderingdata.allOfflinearea)) * 10000;
            }
            this.drawLine();
            this.drawLine1();
          }
        })
        .finally(() => {
          this.dayLoading = false;
        });
    },
    Categorychange(value, dateString) {
      this.dateCategory = dateString;
      this.Category_distribution1 = false;
      this.Category_distribution2 = false;
      this.Category_distribution3 = false;
      this.Category_distribution4 = true;
      const faid = this.FactoryId || "";
      this.arr1 = [];
      var Startdate = dateString[0];
      var Enddate = dateString[1];
      this.Categoryload = true;
      mkTOrder4Xdstatisticsv3(Startdate, Enddate, faid, this.OrderSource)
        .then(res => {
          if (res.code) {
            this.CategoryData = res.data;
            let colorlist = ["#f37e50", "#cd2e9c", "#3ec8f0", "#f37e50", "#cd2e9c"];
            let textlist = ["双面板", "4-6层", "8-10层", ">10层", "HDI板"];
            Object.keys(this.CategoryData).forEach((item, index) => {
              this.arr1.push(item);
              this.Proportion(item, colorlist[index], textlist[index]);
            });
          }
        })
        .finally(() => {
          this.Categoryload = false;
        });
    },
    rangechange(value, dateString) {
      localStorage.setItem("dateString", dateString);
      this.dateString1 = dateString;
      this.classification1 = false;
      this.classification2 = false;
      this.classification3 = false;
      this.classification4 = true;
      this.Intervalselection();
    },
    timestatistics() {
      this.botloading = true;
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.averagetime = [];
      timestatistics(faid, this.OrderSource)
        .then(res => {
          if (res.code) {
            for (let index = 0; index < res.data.length; index++) {
              const element = res.data[index].timeSpan;
              this.averagetime.push(element);
            }
          }
          this.drawLine2();
        })
        .finally(() => {
          this.botloading = false;
        });
    },
    Goldrushrate() {
      this.rateLoading1 = true;
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.Golddata = [];
      this.Orderamount = [];
      this.Inquiryamount = [];
      modatastatistics(faid, this.OrderSource)
        .then(res => {
          if (res.code) {
            for (let index = 0; index < res.data.bodtos.length; index++) {
              const Golddata = res.data.bodtos[index];
              for (let i = 0; i < Golddata.dtos.length; i++) {
                this.Golddata.push(Golddata.dtos[i].value);
              }
            }
            for (let index = 0; index < res.data.offlinedtos.length; index++) {
              const Orderamount = res.data.offlinedtos[index];
              for (let i = 0; i < Orderamount.dtos.length; i++) {
                this.Orderamount.push(Orderamount.dtos[i].value);
              }
            }
            for (let index = 0; index < res.data.xpdtos.length; index++) {
              const Inquiryamount = res.data.xpdtos[index];
              for (let i = 0; i < Inquiryamount.dtos.length; i++) {
                this.Inquiryamount.push(Inquiryamount.dtos[i].value);
              }
            }
            this.Golddata.pop();
            this.Orderamount.pop();
            this.Inquiryamount.pop();
            this.mountData = [
              {
                name: "总询价金额",
                month1: this.Inquiryamount[0],
                month2: this.Inquiryamount[1],
                month3: this.Inquiryamount[2],
                month4: this.Inquiryamount[3],
                month5: this.Inquiryamount[4],
                month6: this.Inquiryamount[5],
                month7: this.Inquiryamount[6],
                month8: this.Inquiryamount[7],
                month9: this.Inquiryamount[8],
                month10: this.Inquiryamount[9],
                month11: this.Inquiryamount[10],
                month12: this.Inquiryamount[11],
              },
              {
                name: "总下单金额",
                month1: this.Orderamount[0],
                month2: this.Orderamount[1],
                month3: this.Orderamount[2],
                month4: this.Orderamount[3],
                month5: this.Orderamount[4],
                month6: this.Orderamount[5],
                month7: this.Orderamount[6],
                month8: this.Orderamount[7],
                month9: this.Orderamount[8],
                month10: this.Orderamount[9],
                month11: this.Orderamount[10],
                month12: this.Orderamount[11],
              },
              {
                name: "淘金率",
                month1: this.Golddata[0],
                month2: this.Golddata[1],
                month3: this.Golddata[2],
                month4: this.Golddata[3],
                month5: this.Golddata[4],
                month6: this.Golddata[5],
                month7: this.Golddata[6],
                month8: this.Golddata[7],
                month9: this.Golddata[8],
                month10: this.Golddata[9],
                month11: this.Golddata[10],
                month12: this.Golddata[11],
              },
            ];
            this.drawLine4();
          }
        })
        .finally(() => {
          this.rateLoading1 = false;
        });
    },
    Achievementrate() {
      this.rateLoading = true;
      var faid = "";
      if (this.FactoryId) {
        faid = this.FactoryId;
      } else {
        faid = "";
      }
      this.inquiryvalue = [];
      this.ordervalue = [];
      this.ratevalue = [];
      this.ratemonth = [];
      bodatastatistics(faid, this.OrderSource)
        .then(res => {
          if (res.code) {
            for (let index = 0; index < res.data.bodtos.length; index++) {
              const ratedata = res.data.bodtos[index];
              for (let i = 0; i < ratedata.dtos.length; i++) {
                this.ratevalue.push(ratedata.dtos[i].value);
                this.ratemonth.push(ratedata.dtos[i].month);
              }
            }
            for (let index = 0; index < res.data.offlinedtos.length; index++) {
              const placeanorderdata = res.data.offlinedtos[index];
              for (let i = 0; i < placeanorderdata.dtos.length; i++) {
                this.ordervalue.push(placeanorderdata.dtos[i].value);
              }
            }
            for (let index = 0; index < res.data.xpdtos.length; index++) {
              const inquirydata = res.data.xpdtos[index];
              for (let i = 0; i < inquirydata.dtos.length; i++) {
                this.inquiryvalue.push(inquirydata.dtos[i].value);
              }
            }
            this.ratemonth.pop();
            this.ordervalue.pop();
            this.inquiryvalue.pop();
            this.ratevalue.pop();
            this.TableData = [
              {
                name: "总询盘量",
                month1: this.inquiryvalue[0],
                month2: this.inquiryvalue[1],
                month3: this.inquiryvalue[2],
                month4: this.inquiryvalue[3],
                month5: this.inquiryvalue[4],
                month6: this.inquiryvalue[5],
                month7: this.inquiryvalue[6],
                month8: this.inquiryvalue[7],
                month9: this.inquiryvalue[8],
                month10: this.inquiryvalue[9],
                month11: this.inquiryvalue[10],
                month12: this.inquiryvalue[11],
              },
              {
                name: "总下单量",
                month1: this.ordervalue[0],
                month2: this.ordervalue[1],
                month3: this.ordervalue[2],
                month4: this.ordervalue[3],
                month5: this.ordervalue[4],
                month6: this.ordervalue[5],
                month7: this.ordervalue[6],
                month8: this.ordervalue[7],
                month9: this.ordervalue[8],
                month10: this.ordervalue[9],
                month11: this.ordervalue[10],
                month12: this.ordervalue[11],
              },
              {
                name: "商机达成率",
                month1: this.ratevalue[0],
                month2: this.ratevalue[1],
                month3: this.ratevalue[2],
                month4: this.ratevalue[3],
                month5: this.ratevalue[4],
                month6: this.ratevalue[5],
                month7: this.ratevalue[6],
                month8: this.ratevalue[7],
                month9: this.ratevalue[8],
                month10: this.ratevalue[9],
                month11: this.ratevalue[10],
                month12: this.ratevalue[11],
              },
            ];
            this.drawLine3();
          }
        })
        .finally(() => {
          this.rateLoading = false;
        });
    },
    drawLine() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          title: {
            text: "订单询价",
            left: "center",
            top: "center",
            textStyle: {
              fontSize: 12,
            },
          },
          legend: {
            orient: "vertical",
            icon: "rect",
            itemHeight: 8, // 修改icon图形大小
            itemWidth: 8, // 修改icon图形大小
            x: "right",
            y: "center",
            align: "left",
            data: ["新单", "返单"],
          },
          color: [" rgb(72,116,203)", " rgb(242,186,2)"],
          series: [
            {
              name: "订单询价",
              type: "pie",
              radius: ["40%", "70%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              label: {
                normal: {
                  position: "inner",
                  show: true,
                  textStyle: {
                    fontWeight: 600,
                    fontSize: 10,
                  },
                  formatter: "{d}",
                },
              },

              labelLine: {
                show: false,
              },
              minAngle: 30 || 0,
              data: [
                { value: this.Orderingdata.allNewXdnum || 0, name: "新单" },
                { value: this.Orderingdata.allOldXdnum || 0, name: "返单" },
              ],
            },
          ],
        };
        let chartDom = document.getElementById("main");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    drawLine1() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          title: {
            text: "订单询价",
            left: "center",
            top: "center",
            textStyle: {
              fontSize: 12,
            },
          },
          legend: {
            orient: "vertical",
            icon: "rect",
            itemHeight: 8, // 修改icon图形大小
            itemWidth: 8, // 修改icon图形大小
            x: "right",
            y: "center",
            align: "left",
            data: ["国内", "国外"],
          },
          color: [" rgb(72,116,203)", " rgb(242,186,2)"],
          series: [
            {
              name: "订单询价",
              type: "pie",
              radius: ["40%", "70%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              label: {
                normal: {
                  position: "inner",
                  show: true,
                  textStyle: {
                    fontWeight: 600,
                    fontSize: 10,
                  },
                  formatter: "{d}",
                },
              },
              labelLine: {
                show: false,
              },
              minAngle: 30 || 0,
              data: [
                { value: this.Orderingdata.domesticXdnum || 0, name: "国内" },
                { value: this.Orderingdata.interXdnum || 0, name: "国外" },
              ],
            },
          ],
        };
        let chartDom = document.getElementById("main1");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    Proportion(idtype, colors, text) {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          title: {
            text: "品类分布",
            left: "center",
            top: "center",
            textStyle: {
              fontSize: 12,
            },
          },
          legend: {
            orient: "vertical",
            icon: "rect",
            itemHeight: 8, // 修改icon图形大小
            itemWidth: 8, // 修改icon图形大小
            x: "right",
            y: "center",
            align: "left",
            data: [text, "其他"],
          },
          color: [" rgb(72,116,203)", " rgb(242,186,2)"],
          series: [
            {
              name: "品类分布",
              type: "pie",
              radius: ["40%", "70%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              label: {
                normal: {
                  position: "inner",
                  show: true,
                  textStyle: {
                    fontWeight: 600,
                    fontSize: 10,
                  },
                  formatter: "{d}%",
                },
              },
              labelLine: {
                show: false,
              },
              minAngle: 30 || 0,
              data: [
                { value: this.CategoryData[idtype] || 0, name: text },
                { value: 100 - this.CategoryData[idtype] || 0, name: "其他" },
              ],
            },
          ],
        };
        let chartDom = document.getElementById(idtype);
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    drawLine2() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          xAxis: {
            type: "category",
            data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
            axisLabel: {
              textStyle: {
                fontSize: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "gray", // 设置 x 轴轴线的颜色为灰色
              },
            },
            axisTick: {
              lineStyle: {
                color: "gray", // 设置 x 轴坐标刻度线的颜色为灰色
              },
            },
          },
          yAxis: {
            axisLabel: {
              textStyle: {
                fontSize: 10,
              },
            },
          },
          grid: {
            left: "3%", // 左边距
            right: "3%", // 右边距
            top: "25%", // 上边距
            bottom: "5%", // 下边距
            containLabel: true, // 自动计算标签的大小以避免图表溢出
          },
          // backgroundColor: 'rgb(51,63,80)',
          // color: ["rgb(113,150,125)"],
          color: [" rgb(34,213,140)"],
          legend: {
            // 图例配置
            data: ["平均报价完成时间(min/单)"], // 图例项的名称
            textStyle: {
              // 图例项的文字样式
              fontSize: 10,
              // color: '#fff'
            },
            top: "5%", // 图例的位置
          },
          series: [
            {
              name: "平均报价完成时间(min/单)",
              data: this.averagetime || 0,
              type: "line",
              label: {
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 10,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
          ],
        };
        let chartDom = document.getElementById("botmain");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    drawLine3() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              var seriesType = params.seriesType; // 获取数据点所属的系列类型
              if (seriesType === "line") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value + " %";
              } else if (seriesType === "bar") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value;
              }
            },
          },
          xAxis: [
            {
              type: "category",
              data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                textStyle: {
                  color: "rgba(121, 128, 137, 0)",
                  fontSize: 10,
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              data: [],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                formatter: "{value} %",
                textStyle: {
                  color: "rgb(121,128,137)",
                  fontSize: 10,
                },
                axisPointer: { snap: true },
              },
            },
            {
              type: "value",
              position: "right", // 将右边的 y 轴显示在右侧
              data: [],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                textStyle: {
                  color: "rgb(121,128,137)",
                  fontSize: 10,
                },
              },
            },
          ],
          grid: {
            left: "12%", // 左边距
            right: "5%", // 右边距
            top: "10%", // 上边距
            bottom: "35%", // 下边距
            //containLabel: true // 自动计算标签的大小以避免图表溢出
          },
          color: ["rgb(91,155,213)", "rgb(237,125,49)", "rgb(34,213,140)"],
          legend: {
            // 图例配置
            data: ["总询盘量", "总下单量", "总商机达成率(%)"], // 图例项的名称
            textStyle: {
              // 图例项的文字样式
              color: "rgb(121,128,137)",
              fontSize: 10,
            },
            orient: "vertical",
            x: "5",
            y: "206",
            itemWidth: 20, // 设置图例项的宽度
            itemHeight: 10, // 设置图例项的高度
          },
          series: [
            {
              name: "总询盘量",
              type: "bar",
              data: this.inquiryvalue,
              yAxisIndex: 1, // 使用右侧的 y 轴
              barGap: "-8%",
              itemStyle: {
                borderColor: "rgb(62,207,240)",
                borderWidth: 2,
                borderType: "solid",
              },
            },
            {
              name: "总下单量",
              type: "bar",
              barGap: "-8%",
              data: this.ordervalue,
              yAxisIndex: 1, // 使用右侧的 y 轴
              itemStyle: {
                borderColor: "rgb(253,94,58)",
                borderWidth: 2,
                borderType: "solid",
              },
            },
            {
              data: this.ratevalue || 0,
              type: "line",
              name: "总商机达成率(%)",
              label: {
                show: false,
                position: "top",
                textStyle: {
                  fontSize: 14,
                },
              },
              smooth: false,
              symbolSize: 12,
            },
          ],
        };
        let chartDom = document.getElementById("columnar");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    drawLine4() {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              var seriesType = params.seriesType; // 获取数据点所属的系列类型
              if (seriesType === "line") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value + " %";
              } else if (seriesType === "bar") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value;
              }
            },
          },
          xAxis: [
            {
              type: "category",
              data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                textStyle: {
                  color: "rgba(121, 128, 137, 0)",
                  fontSize: 10,
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              data: [],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                formatter: "{value} %",
                textStyle: {
                  color: "rgb(121,128,137)",
                  fontSize: 10,
                },
                axisPointer: { snap: true },
              },
            },
            {
              type: "value",
              position: "right", // 将右边的 y 轴显示在右侧
              data: [],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                textStyle: {
                  color: "rgb(121,128,137)",
                  fontSize: 10,
                },
              },
            },
          ],
          grid: {
            left: "12%", // 左边距
            right: "5%", // 右边距
            top: "10%", // 上边距
            bottom: "35%", // 下边距
            // containLabel: true // 自动计算标签的大小以避免图表溢出
          },
          color: ["rgb(91,155,213)", "rgb(237,125,49)", "rgb(34,213,140)"],
          legend: {
            // 图例配置
            data: ["总询价金额(万元)", "总下单金额(万元)", "淘金率(%)"], // 图例项的名称
            textStyle: {
              // 图例项的文字样式
              color: "rgb(121,128,137)",
              fontSize: 10,
            },
            orient: "vertical",
            x: "5",
            y: "206",
            itemWidth: 20, // 设置图例项的宽度
            itemHeight: 10, // 设置图例项的高度
          },
          series: [
            {
              name: "总询价金额(万元)",
              type: "bar",
              data: this.Inquiryamount || 0,
              yAxisIndex: 1, // 使用右侧的 y 轴
              barGap: "-8%",
              itemStyle: {
                borderColor: "rgb(62,207,240)",
                borderWidth: 2,
                borderType: "solid",
              },
            },
            {
              name: "总下单金额(万元)",
              type: "bar",
              barGap: "-8%",
              data: this.Orderamount || 0,
              yAxisIndex: 1, // 使用右侧的 y 轴
              itemStyle: {
                borderColor: "rgb(253,94,58)",
                borderWidth: 2,
                borderType: "solid",
              },
            },
            {
              data: this.Golddata || 0,
              type: "line",
              name: "淘金率(%)",
              label: {
                show: false,
                position: "top",
                textStyle: {
                  fontSize: 14,
                },
              },
              smooth: false,
              symbolSize: 12,
            },
          ],
        };
        let chartDom = document.getElementById("columnar1");
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
  },
  watch: {},
};
</script>

<style lang="less" scoped>
/deep/.ant-select-selection--single {
  position: relative;
  height: 32px;
  cursor: pointer;
  width: 150px;
  border: #ff9900 1px solid;
}
/deep/.ant-form-item {
  margin-bottom: 0;
  margin-top: 7px;
}
/deep/.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: #ff9900 !important;
}
/deep/.ant-input {
  border: #ff9900 1px solid;
}
.analysis {
  min-width: 1697px;
  background: #ffffff;
  position: absolute;
  top: 6px;
  .dayweek {
    background-color: #ff9900;
    border: #ff9900 1px solid;
    margin: 15px 0 15px 30px;
    color: #ffffff;
  }
  .weekmonth {
    background-color: rgb(250, 250, 250);
    border: #ff9900 1px solid;
    margin: 15px 0 15px 30px;
    color: #ff9900;
  }
  .leftdata {
    height: 100%;
    width: 40%;
    .Orderingsituation {
      .data1 {
        background-color: rgb(72, 116, 203);
        width: 150px;
        height: 35px;
        border-radius: 10px;
        margin-left: 25px;
        color: #ffffff;
        text-align: center;
        font-size: 16px;
        line-height: 35px;
        margin-top: 7px;
      }
      .data2 {
        background-color: rgb(72, 116, 203);
        width: 410px;
        height: 35px;
        border-radius: 10px;
        margin-left: 20px;
        color: #ffffff;
        text-align: center;
        line-height: 35px;
        font-size: 20px;
        margin-top: 7px;
      }
    }
  }
  .rightdata {
    height: 100%;
    width: 60%;
    .top2 {
      width: 98%;
      margin: 8px 10px;
      border: 1px solid green;
      height: 48%;
      .chievementrate {
        width: 98%;
        height: 270px;
        position: relative;
        top: 40px;
        left: 20px;
      }
    }
    .bot2 {
      width: 98%;
      margin: 8px 10px;
      border: 1px solid green;
      height: 48%;
      .chievementrate {
        width: 98%;
        height: 270px;
        position: relative;
        top: 40px;
        left: 20px;
      }
    }
  }
  .botdata {
    height: 163px;
    width: 100%;
    .bot {
      height: 165px;
      width: 98.9%;
      margin-left: 8px;
      border: 1px solid green;
    }
  }
  .bac {
    width: 98%;
    margin: 8px 8px;
    border: 1px solid green;
    height: 90.2%;
    .left1 {
      width: 300px;
      height: 200px;
    }
  }
  .Category_distribution {
    width: 98.8%;
    margin: 8px 8px;
    border: 1px solid green;
    height: 277px;
    .left1 {
      width: 300px;
      height: 200px;
    }
  }
  .radius1 {
    // background-color: rgb(17,30,49);
    width: 95%;
    margin: 8px 20px;
    height: 93%;
    border-radius: 20px;
    // background-image: linear-gradient(rgba(29,52,80) 1px, transparent 1px),
    //               linear-gradient(90deg, rgba(29,52,80) 1px, transparent 1px);
    // background-size: 50px 40px;
  }
}
</style>
