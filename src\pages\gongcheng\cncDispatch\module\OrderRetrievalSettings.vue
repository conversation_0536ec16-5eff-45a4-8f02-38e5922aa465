<!-- 工程管理 - 锣带分派 - 人员设置-->
<template>

    <a-form-model
      :model="data"
      layout="inline"
    >
    <a-row>
      <a-col :span='8'>
        <a-form-item label="雇员名称"  :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.userName_" disabled  />
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="订单标签" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
           <a-select
            ref="select"
            v-model="data.labels"
          >
            <a-select-option value="0">样板</a-select-option>
            <a-select-option value="1">批量</a-select-option>
            <a-select-option value="2">多层</a-select-option>
            <a-select-option value="3">捷多邦</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="目标数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.targetCount_" />
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span='8'>
        <a-form-item label="每日总量" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.maxNum"/>
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="单次获取"  :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.getNum" />
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="停留数量"  :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.stayNum"/>
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span='8'>
        <a-form-item label="长" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.length"/>
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="宽" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.width" />
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-model-item label="当前状态" :label-col="{ span: 10 }" :wrapper-col="{ span: 14}" style="width:100%; margin:0">
          <a-tag :color="data.isLeave_ ? '#87d068' : '#f50'">
            {{ data.isLeave_ ? "休息中" : "正常" }}
          </a-tag>
          <a-button
            :type="data.isLeave_ ? 'danger' : 'primary'"
            size="small"
            @click="statusClick()"
          >
            {{ data.isLeave_ ? "销假" : "请假" }}
          </a-button>
        </a-form-model-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span='4'>
        <a-form-item label="铝基"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.aluminum"/>
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="非铝基" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.noAluminum"/>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span='4'>
        <a-form-item label="SMT"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.smt" />
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="非SMT"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.noSmt" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span='4'>
        <a-form-item label="KA优品" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.isKaYoupin" />
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="非KA优品" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.noKaYoupin" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span='8'>
        <a-form-item label="显示顺序">
          <a-input-number v-model="data.sort" :min="1" :max="peopleOrderInfoList.length"/>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span='24'>
        <a-form-item label="订单工厂" style='width:100%;' :label-col="{ span:3 }" :wrapper-col="{ span:20}">
          <a-select
              show-search
              mode="multiple"
              v-model="factory_List"
              ref="select1"
          >
            <a-select-option v-for="(item,index) in factoryData" :key="index" :value="item.valueMember"  >
              {{item.text}}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>

  </a-form-model>
</template>

<script>
export default {
  //  "jPOrder":false,
  name:'OrderRetrievalSettings',
  props:["data","peopleOrderInfoList",'factoryData' ],

  created(){
  },
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      factory_List:[],
    };
  },
  watch: {
    data(val){
      console.log(val)
      if(val.factroy!=null && val.factroy!=0 && val.factroy!= ''){
        this.factory_List = val.factroy.split(',')
      }

    }
  },
  methods: {
    statusClick(){
      this.data.isLeave_ = !this.data.isLeave_
      console.log('this.data.isLeave_',this.data.isLeave_)

    }
  },
  mounted () {

  }
};
</script>
