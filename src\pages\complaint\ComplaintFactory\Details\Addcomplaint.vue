<!--投诉工厂-->
<template>
    <div class="Addcomplaint">
        <a-collapse :activeKey="'1'" @change="CollapseList">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>投诉详情</div>
                        <div>{{ text }}</div>
                    </div>
                </template>
            <div>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'订单号'">
                            <a-input v-model="showdata.orderNo" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'客户型号'">
                            <a-input disabled v-model="showdata.customerModel"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'生产编号'">
                            <a-input disabled v-model="showdata.proOrderNo"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'联系人'" class="require">
                            <a-input v-model="showdata.factoryContactName" :disabled="statusbar!=20"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'联系电话'"  class="require">
                            <a-input v-model="showdata.factoryContactNumber" :disabled="statusbar!=20"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'交货日期'">
                            <a-input v-model="showdata.deliveryDate" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货总数'" >
                            <a-input v-model="showdata.deliveryNum" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'不良总数'">
                            <a-input v-model="showdata.badNum" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :label="'周期'" >
                            <a-input  v-model="showdata.dateCode" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'不良率'" >
                            <a-input disabled v-model="Defective"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'问题描述'" >
                            <a-textarea v-model="showdata.problemDescription"  :auto-size="{ minRows: 4, maxRows:6 }" disabled></a-textarea >
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-if="!filepath">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <a-upload  
                            ref="fileRef"
                            :file-list="fileList"
                            :before-upload="beforeUpload"
                            :customRequest="httpRequest"
                            accept=".jpg,.bmp,.png,.zip,.rar,.7z"
                            @change="handleChange">
                            <a-button v-if="fileList.length==0" > <a-icon type="upload"/> 选择文件</a-button>
                            <span style="padding:0 15px;" v-if="fileList.length==0" >未上传任何附件</span>
                            </a-upload>                           
                        <div style="color: #ff9900;font-size: 12px;">温馨提示：附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式，文件大小不超过10M。</div>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-else-if="fileList.length==0">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <span v-if="filepath">
                                <span style="color: #428bca;cursor: pointer;" @click="filedown(filepath)"> 
                                <a-icon type="link" style="padding: 0 5px;" ></a-icon>{{filename}}
                                </span>  
                                <span  style="padding-left: 20px;" @click="deletefile" >
                                    <a-tooltip title="该操作不可撤销，删除后可重新上传附件"><a-icon type="delete"></a-icon></a-tooltip>                               
                                </span> 
                            </span>                                         
                        <div style="color: #ff9900;font-size: 12px;">温馨提示：附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式，文件大小不超过10M。</div>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">                     
                            <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :label="'不良批次要求采取的措施'" class="require">
                                <a-radio-group v-model="showdata.takeSteps" name="radioGroup" :disabled="statusbar!=20">
                                    <a-radio :value="1">退货返工</a-radio>
                                    <a-radio :value="2">报废补货</a-radio>
                                    <a-radio :value="3">报废扣款</a-radio>
                                    <a-radio :value="4">特采</a-radio>
                                    <a-radio :value="5">其他</a-radio>
                                    <a-input v-model="showdata.takeStepsName" :disabled="statusbar!=20"  placeholder="请输入" v-show="showdata.takeSteps=='5'" style="width: 398px;"></a-input>
                                </a-radio-group> 
                            <div v-show="showdata.takeSteps=='2' || showdata.takeSteps=='1'">
                                <div v-show="showdata.takeSteps=='2'">补货板收货地址及收货人信息：（地址，姓名隔开）</div>
                                <div v-show="showdata.takeSteps=='1'">返工 OK 板收货地址及收货人信息：（地址，姓名隔开）</div>
                                <div style="display: flex;justify-content: space-around;">
                                    <a-input placeholder="姓名" v-model="showdata.receiveContactName" style="width: 130px;" :disabled="statusbar!=20">
                                        <template #prefix>
                                            <a-icon type="user" style="color:red" />
                                        </template>
                                    </a-input>
                                    <a-input placeholder="电话" v-model="showdata.receiveContactNumber" style="width: 180px;" :disabled="statusbar!=20">
                                        <template #prefix>
                                            <a-icon type="phone" style="color:red" />
                                        </template>
                                    </a-input>
                                    <a-input placeholder="地址" v-model="showdata.receiveAddress" style="width: 300px;" :disabled="statusbar!=20">
                                        <template #prefix>
                                            <a-icon type="home" style="color:red" />
                                        </template>
                                    </a-input>
                                    <a-button type="primary" style="margin-left: 15px;" @click="setasdefault">设为默认</a-button>
                                    <a-button type="primary" style="margin-left: 15px;" @click="selectaddress">地址</a-button>
                                </div>    
                            </div>                                      
                            </a-form-model-item>
                    </a-col>
                </a-row>    
                <a-row v-show="showdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废品形态'" >
                            <a-radio-group  name="radioGroup" v-model="showdata.scrapType" :disabled="statusbar!=20">
                                <a-radio value="PCB">PCB</a-radio>
                                <a-radio value="PCBA">PCBA</a-radio>
                                <a-radio value="整机">整机</a-radio>
                                <a-radio value="其它">其它</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废品数量描述'" >
                            <a-input v-model="showdata.scrapNumDetail" :disabled="statusbar!=20"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>   
                <a-row v-show="showdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'不良板成本(元)'" >
                            <a-input v-model="showdata.badBoardCost" :disabled="statusbar!=20"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'扣款总金额(元)'" >
                            <a-input v-model="showdata.deductMoney" :disabled="statusbar!=20"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>  
                <a-row v-show="showdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废总成本(元)'" >
                            <a-input v-model="showdata.scrapCost" :disabled="statusbar!=20"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>  
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'是否需要8D报告'" class="require">
                            <a-radio-group   name="radioGroup"  v-model="showdata.need8DReport" :disabled="statusbar!=20">
                                <a-radio :value="false">否</a-radio>
                                <a-radio :value="true">是</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'要求解决时效'" :class="showdata.need8DReport?'require':''">
                            <a-radio-group  @change="replychange" name="radioGroup" v-model="showdata.needReplytime"  :disabled="statusbar!=20">
                                <a-radio :value="3">3天</a-radio>
                                <a-radio :value="2">2天</a-radio>
                                <a-radio :value="1">1天</a-radio>                               
                                <a-radio :value="0">其他</a-radio>
                            </a-radio-group>
                            <a-input v-model="showdata.needReplytimeName" :disabled="showdata.needReplytime!==0 || statusbar!=20" style="width: 100px;" ></a-input>
                        </a-form-model-item>
                    </a-col>                  
                </a-row>  
                <a-row style="margin-top: 15px;">
                    <a-col :span="16">
                            <a-button type="primary" style="margin-left: 185px;" @click="handleok" :disabled="statusbar!=20">确定</a-button>
                            <a-button type="normal" style="margin-left: 30px;" @click="back">返回</a-button>
                    </a-col>
                </a-row>
            </div>
            </a-collapse-panel>
        </a-collapse>
        <a-modal
                title="收货地址"
                :visible="addressvisible"
                @ok="handleOk"
                @cancel="addressvisible=false"
                centered
                :width="900"
        >
        <a-table
            :columns="columns"
            :dataSource="tabledata"
            :customRow="onClickRow"
            :pagination="false"
            :scroll="{y:400 }"
            :rowClassName="isRedRow"
            :rowKey="(record,index) => index"
            bordered
        >
        <template slot="action" slot-scope="text, record">
            <a-tooltip title="点击删除该条地址">
                <a-icon type="delete" @click="deleteclick(record)" style="color: #428bca;"></a-icon>
            </a-tooltip>           
        </template> 
        </a-table>
     </a-modal>
    </div>
</template>
<script>
const columns = [
{
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 65,
    ellipsis:true,
    fixed: 'left',
},   
{
    title: "联系人",
    dataIndex: "receiveContactName",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "联系电话",
    dataIndex: "receiveContactNumber",
    align: "left",
    ellipsis: true,
    width:140,       
},
{
    title: "地址",
    dataIndex: "receiveAddress",
    align: "left",
    ellipsis: true,   
},
{
    title: "创建时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,   
    width:200,    
},
{
    title: "是否默认",
    customRender: (text,record,index) => record.isDefault==true?'是':'否',
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "操作",
    align: "center",
    scopedSlots:{customRender:'action'},
    ellipsis: true,   
    width:50,    
},
];
import {uploadcomplaintsfile,custbyid,complainfactory,deletebyid,
    factorycomplaintsaddress,complaintsaddress,factorybyid} from "@/services/complaint/QualitativeComplaint.js";
export default {
    name:'Addcomplaint',
    props:[],
    data() {
        return {
            Defective:'',
            text:'收起',
            isFileType:false,
            columns,
            addressvisible:false,
            fileList:[],
            filepath:null,
            filename:null,
            showdata:{},
            addressdata:{},
            tabledata:[],
            proOrderId:'',
        }
    },
    mounted(){
        if(this.$route.query.type=='dxts'){
            this.getDataDetail()
        }         
    },
    methods:{
        deleteclick(record){
            deletebyid(record.id).then(res=>{
                if(res.code){
                    this.$message.success('已删除')
                    this.getaddress('mr')
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        replychange(){
            if(this.showdata.needReplytime!==0){
                this.showdata.needReplytimeName=''
            }
        },
        onClickRow(record){
                return {
                    on: {
                        click: () => {
                            this.selectdata = record;
                            this.proOrderId=record.id
                        }
                    }
                }
            },
            isRedRow(record){
                let strGroup = []
                let str =[]
                if(record.id && record.id == this.proOrderId) {
                    strGroup.push('rowBackgroundColor')
                }
                return str.concat(strGroup)
            },
        getaddress(type){
            complaintsaddress(this.showdata.tradeType,this.showdata.joinFactoryId,).then(res=>{
                if(res.code){
                    if(res.data.length){
                        this.tabledata = res.data
                        this.addressdata=res.data.filter(item=>item.isDefault==true)[0]
                        if(type!='mr'){
                            this.$set(this.showdata,'receiveAddress',this.addressdata.receiveAddress)
                            this.$set(this.showdata,'receiveContactName',this.addressdata.receiveContactName)
                            this.$set(this.showdata,'receiveContactNumber',this.addressdata.receiveContactNumber)
                        }                       
                    }                    
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        selectaddress(){
            if(this.tabledata.length){
                this.addressvisible = true
                this.selectdata = {};
                this.proOrderId=''
            }else{
                this.$message.error('暂无收件地址信息')
            }
        },
        handleOk(){
            this.addressvisible =false
            this.$set(this.showdata,'receiveAddress',this.selectdata.receiveAddress)
            this.$set(this.showdata,'receiveContactName',this.selectdata.receiveContactName)
            this.$set(this.showdata,'receiveContactNumber',this.selectdata.receiveContactNumber)
        },
        setasdefault(){
            if(!this.showdata.receiveContactName){
                this.$message.error('请填写或选择收件人')
                return
            }
            if(!this.showdata.receiveContactNumber){
                this.$message.error('请填写或选择收件人联系方式')
                return
            }
            if(!this.showdata.receiveAddress){
                this.$message.error('请填写或选择收件人地址')
                return
            }
            let params ={
                    "tradeType": this.showdata.tradeType,
                    "joinFactoryId": this.showdata.joinFactoryId,
                    "receiveContactName": this.showdata.receiveContactName,
                    "receiveContactNumber": this.showdata.receiveContactNumber,
                    "receiveAddress":this.showdata.receiveAddress,
                }
            factorycomplaintsaddress(params).then(res=>{
                if(res.code){
                    this.$message.success(res.message)
                    this.getaddress('mr')
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        filedown(path){         
            if(path){
                window.location.href = path
            }
        },
        deletefile(){
            this.showdata.verdictFilePath=null
            this.filepath=null
            this.filename=null
        },
        handleok(){
            let params = this.showdata
            params.badBoardCost=params.badBoardCost?Number(params.badBoardCost):null
            params.deductMoney=params.deductMoney?Number(params.deductMoney):null
            params.scrapCost=params.scrapCost?Number(params.scrapCost):null
            if(!params.factoryContactName){
                this.$message.error('请填写联系人')
                return
            }
            if(!params.factoryContactNumber){
                this.$message.error('请填写联系人电话')
                return
            }
            if(!params.takeSteps){
                this.$message.error('请选择不良批次要求采取的措施')
                return
            }
            if(params.need8DReport && !params.needReplytime && params.needReplytime!==0){
                this.$message.error('请选择要求解决时效')
                return
            }
            if(!params.receiveContactName && (params.takeSteps==1 || params.takeSteps==2) ){
                this.$message.error('请填写或选择收件人')
                return
            }
            if(!params.receiveContactNumber && (params.takeSteps==1 || params.takeSteps==2) ){
                this.$message.error('请填写或选择收件人联系方式')
                return
            }
            if(!params.receiveAddress && (params.takeSteps==1 || params.takeSteps==2) ){
                this.$message.error('请填写或选择收件人地址')
                return
            }
            if(params.takeSteps==1 || params.takeSteps==2){
                params.takeStepsName=''
                params.scrapType=''
                params.scrapNumDetail=''
                params.badBoardCost=null
                params.scrapCost=null
                params.deductMoney=null
            }
            if(params.takeSteps==3){
                params.receiveContactName=''
                params.receiveContactNumber=''
                params.receiveAddress=''
            }
            if(params.takeSteps==4){
                params.takeStepsName=''
                params.scrapType=''
                params.scrapNumDetail=''
                params.badBoardCost=null
                params.scrapCost=null
                params.deductMoney=null
                params.receiveContactName=''
                params.receiveContactNumber=''
                params.receiveAddress=''
            }
            if(params.takeSteps==5){ 
                params.scrapType=''
                params.scrapNumDetail=''
                params.badBoardCost=null
                params.scrapCost=null
                params.deductMoney=null
                params.receiveContactName=''
                params.receiveContactNumber=''
                params.receiveAddress=''
            }
            complainfactory(params).then(res=>{
                if(res.code){
                    this.$message.success(res.message)
                    localStorage.setItem('tabkey',20)
                    this.$router.push({path:'QualitativeComplaint',query:{}})
                }else{
                    this.$message.error(res.message)                
                }
            })
        },
        CollapseList(val){
            if(val.length){
                this.text = '收起'
            }else{
                this.text = '展开'
            }
        },
        getDataDetail(){       
            if(this.statusbar==20){
                custbyid(this.$route.query.id).then(res=>{
                if(res.code){
                    this.showdata=res.data
                    this.Defective = this.getFloat(this.showdata.badNum/this.showdata.deliveryNum*100,3)+'%'
                    this.getaddress()  
                    this.filepath=JSON.parse(JSON.stringify(res.data)).verdictFilePath ||JSON.parse(JSON.stringify(res.data)).complaintFilePath
                    this.filename = JSON.parse(JSON.stringify(res.data)).verdictFileName ||JSON.parse(JSON.stringify(res.data)).complaintFileName
                }else{
                    this.$message.error(res.message)
                    this.back()
                }
            })
            } else{
                factorybyid(this.$route.query.id).then(res=>{
                if(res.code){
                    this.showdata=res.data
                    this.Defective = this.getFloat(this.showdata.badNum/this.showdata.deliveryNum*100,3)+'%'
                    this.getaddress()  
                    this.filepath=JSON.parse(JSON.stringify(res.data)).verdictFilePath ||JSON.parse(JSON.stringify(res.data)).complaintFilePath
                    this.filename = JSON.parse(JSON.stringify(res.data)).verdictFileName ||JSON.parse(JSON.stringify(res.data)).complaintFileName
                }else{
                    this.$message.error(res.message)
                    this.back()
                }
            })
            }   
          
        },

        back(){
            if(this.$route.query.type=='dxts'){
                localStorage.setItem('tabkey',20)
                this.$router.push({path:'QualitativeComplaint',query:{}})
            }else{
                this.$router.push({path:'ComplaintFactory',query:{} })
            }            
        },
        async httpRequest(data,type) {
            const formData = new FormData();
            formData.append("file", data.file);
            await uploadcomplaintsfile(formData).then(res => {
                if (res.code == 1) {
                    data.onSuccess(res.data);
                    this.showdata.complaintFilePath=res.data
                    this.showdata.complaintFileName=data.file.name
                } else {
                    this.$message.error(res.message)
                }
            })
            },
        beforeUpload(file){
            this.isFileType = file.name.toLowerCase().indexOf('.zip') != -1 || file.name.toLowerCase().indexOf('.rar') != -1||
            file.name.toLowerCase().indexOf('.jpg') != -1 || file.name.toLowerCase().indexOf('.bmp') != -1||
            file.name.toLowerCase().indexOf('.png') != -1 || file.name.toLowerCase().indexOf('.7z') != -1
                if (!this.isFileType) {
                    this.$message.error('附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式,文件大小不超过10M');
                    return false
                }
        },
        handleChange({ fileList },data) { 
            if(fileList.length==0){
                this.showdata.complaintFilePath=''
                this.showdata.complaintFileName=''
            }
         if( this.isFileType){
            this.fileList = fileList;
            if(this.fileList.length > 1){
            this.fileList.splice(0,1)
            return
            }
        }
        },
    },
    created(){
        this.statusbar=this.$route.query.statusbar    
    }
}
</script>
<style lang="less" scoped>
/deep/.ant-radio-disabled .ant-radio-inner::after {
        background-color: #ff9900;
    }
    /deep/.ant-radio-disabled .ant-radio-inner {
        background-color: #ffffff;
        border-color: #d9d9d9  !important;
        cursor: not-allowed;
    }
    /deep/.ant-radio-disabled + span {
        color: black;
        cursor: not-allowed;
    }
    /deep/.ant-radio-checked .ant-radio-inner{
        border-color: #ff9900  !important;
    }
/deep/.rowBackgroundColor {
    background: #dfdcdc!important;
  }
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
        background: #F8F8F8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
background: #dfdcdc;
}
/deep/ .ant-table{
        .ant-table-tbody{
            tr.ant-table-row-hover td {
             background: #dfdcdc;
            }
        }
        .rowBackgroundColor {
                background: #c9c9c9!important;
            }       
        .ant-table-thead > tr > th{
            padding: 4px 4px;
        }
        .ant-table-tbody > tr > td {
            padding: 4px 4px!important;
            max-width: 100px;
            color: #000000;
        }
        tr.ant-table-row-selected td {
        background: #dfdcdc;
        }
        tr.ant-table-row-hover td {
        background: #dfdcdc;
        }
}
/deep/.require{ 
    .ant-form-item-label label{
        color:red !important;
    }
}
.Addcomplaint{
    padding: 10px;
    overflow: auto;
    height: 821px;
    background-color: white;
    border: 1px solid #e8e8e8;
    &::-webkit-scrollbar {
        width: 6px; 
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        background: #ff9900;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #ffffff;
      }
    /deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
    right: 16px !important;
    left: auto;
    }
    /deep/.ant-divider-horizontal {
        display: block;
        clear: both;
        width: 100%;
        min-width: 100%;
        height: 1px;
        margin: 13px 0;
    }
    /deep/.ant-form-item{
        margin-bottom: 0;
    }
}

</style>