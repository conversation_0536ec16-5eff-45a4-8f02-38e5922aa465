<!-- 工具管理- 开料拼版-数据报表 -->
<template>
<div id="pdfDom" style="padding:10px;">
  <div class="header"></div>
    <h3 style="text-align:center;color:#ff9900;line-height:34px;font-weight: 500;">拼版参数信息</h3>
    <a-table
        :rowKey="(record, index) => {
            return index;
            }"
        :columns="columns"
        :data-source="tableData"
        :pagination="false"   
    > 
    </a-table> 
    <div style="margin-top:20px;">
    <h3 style="text-align:center;color:#ff9900;margin-top:10px;font-weight: 500;">拼版开料图</h3>
    <div style="width:910px;height:900px;  ">
        <div style="width:910px;height:520px;">            
            <div style="width:450px;height:600px;margin:0 auto;">
              <h4 style="margin:10px 0;font-weight: 500;">大料图</h4>
            <img :src="' data:image/png;base64,'+ imgData.sheetCut" data-name="sheetCut" style="width:100%;height:100%;margin-top:-80px; transform:rotate(-90deg);" />
            </div>
        </div> 
        <div style="width:910px;height:380px;display:flex;margin-top:20px;justify-content: center;">
            <div  v-if="imgData.aPnl">
                <h4 style="font-weight: 500;">A板图</h4>
                <div style="width:356px;height:366px;">
                <img :src="' data:image/png;base64,'+ imgData.aPnl" data-name="aPnl" style="width:100%;height:100%;" />
                </div>
            </div>
            <div v-if="imgData.bPnl">
                <h4 style="font-weight: 500;">B板图</h4>
                 <div style="width:356px;height:366px;">
                <img :src="' data:image/png;base64,'+ imgData.bPnl" data-name="bPnl" style="width:100%;height:100%;" />
                </div>
            </div>           
        </div>      
    </div> 
    </div>   
</div>
</template>

<script>
const columns = [
  
  {
    title: 'Panel长',
    dataIndex: 'cpanelc',
    width: '5.5%',
    align:'center'
  },
  {
    title: 'Panel宽',
    dataIndex: 'cpanelk',
    width: '5.5%',
    align:'center'
  },
  {
    title: '上下边距',
    dataIndex: 'csbj',
    width: '6%',
    align:'center'
  },  
  {
    title: '左右边距',
    dataIndex: 'czbj',
    width: '6%',
    align:'center'
  }, 
  {
    title: '板材长',
    dataIndex: 'cbcc',
    width: '5.5%',
    align:'center'
  },
  {
    title: '板材宽',
    dataIndex: 'cbck',
    width: '5.5%',
    align:'center'
  }, 
  {
    title: '拼版数',
    dataIndex: 'cpbs',
    width: '5%',
    align:'center'
  },
  {
    title: '开料数',
    dataIndex: 'ckls',
    width: '5%',
    align:'center'
  },
  {
    title: '拼版利用率',
    dataIndex: 'cpblyl',
    width: '7%',
    align:'center'
  },
  {
    title: '板材利用率',
    dataIndex: 'cbclyl',
    width: '7%',
    align:'center'
  },
   

]
import htmlToPdf from '@/utils/htmlToPdf';
export default{
    name:"reportInfo",
    props:{
        PanelCuttingData:{
            type:Object,
            required: true,
        },
        imgData:{
        },
        reportData:{
          type:Object
        },
        name: {
          type: String
        },
        
    },
    computed:{
      tableData(){
         let obj = {}
        if(this.PanelCuttingData.name == "manual"){
          obj['cpanelc'] =  this.PanelCuttingData.panelLen    //手動panel長
          obj['cpanelk'] =  this.PanelCuttingData.panelWidth   //手動panel寬
          obj['csbj'] =  this.PanelCuttingData.top_bottomD    //手動上下边距
          obj['czbj'] =  this.PanelCuttingData.left_rightD   //手動左右边距
          obj['cbcc'] =  this.PanelCuttingData.sheetLen    //手動板材长
          obj['cbck'] =  this.PanelCuttingData.sheetWidth   //手動板材宽
          obj['cpbs'] =  this.PanelCuttingData.pbSmallRecNum   //手動拼版数
          obj['ckls'] =  this.PanelCuttingData.smallRecNum    //手動开料数
          obj['cpblyl'] =  this.PanelCuttingData.useRate1   //手動拼版利用率
          obj['cbclyl'] =  this.PanelCuttingData.useRate2   //手動板材利用率
        } else {
          obj = this.PanelCuttingData
        }
        return [obj]
      }
    },
    data(){
        return{
            columns,
        }
    },
    methods: {
        getReportPdf(){
        htmlToPdf('pdfDom',this.name)
        }
    },
    mounted(){
      console.error(this.PanelCuttingData)
    }
}

</script>
