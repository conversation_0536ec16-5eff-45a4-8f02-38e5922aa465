<template>
  <a-form-model ref="ruleForm" :model="ruleForm" :rules="rules">
    <a-form-model-item prop="phone">
      <a-input v-model="ruleForm.phone" autocomplete="off" size="large">
        <a-icon slot="prefix" type="phone" style="color:rgba(0,0,0,.25)" />
      </a-input>
    </a-form-model-item>
    <a-form-model-item has-feedback prop="code">
      <a-row :gutter="8" style="margin: 0 -4px">
        <a-col :span="16">
          <a-input v-model="ruleForm.code" autocomplete="off" size="large" >
            <a-icon slot="prefix" type="mail" style="color:rgba(0,0,0,.25)"/>
          </a-input>
        </a-col>
        <a-col :span="8" style="padding-left: 4px">
          <a-button style="width: 100%" class="captcha-button" size="large" :disabled="disabledBtn || (typpe=='pe' && !ruleForm.phone)" @click="countDown">{{content}}</a-button>
        </a-col>
      </a-row>
    </a-form-model-item>
    <a-form-model-item>
      <a-button type="primary" @click="submitForm('ruleForm')" style="width: 100%;margin-top: 24px;" class="ant-btn-lg" :loading="logging">
        登录
      </a-button>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import {applicationConfiguration, getRegisterCode, wechatcode,login, phoneLogin,customerbindingopenid} from "@/services/user";
import {setAuthorization} from "@/utils/request";
import {loadRoutes} from "@/utils/routerUtil";
import {mapMutations} from "vuex";

export default {
  props:['typpe'],
  data() {
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('手机号不能为空'));
      } else {
        callback();
      }
    };
    let validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('验证码不能为空!'));
      }  else {
        callback();
      }
    };
    return {
      ruleForm: {
        phone: '',
        code: '',
      },
      rules: {
        phone: [{ validator: validatePass, trigger: 'change' }],
        code: [{ validator: validatePass2, trigger: 'change' }],
      },
      disabledBtn:false,
      logging:false,
      content: '发送验证码',  // 按钮里显示的内容
      totalTime: 60,      //记录具体倒计时时间
    };
  },
  methods: {
    ...mapMutations("account", ["setUser", "setPermissions", "setRoles"]),
    submitForm(formName) {
      if(this.typpe=='web'){
        this.$refs[formName].validate(valid => {
        if (valid) {
          this.logging = true;
          let params = {}
          params.phoneNumber = this.ruleForm.phone;
          params.smsCode = this.ruleForm.code;
          phoneLogin(params)
              .then(this.afterLogin)
              .finally(() => {
                this.logging = false;
              });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
      }else if (this.typpe=='pe'){     
          this.logging = true;
          let params = {}
          params.phoneNumber = this.ruleForm.phone;
          params.smsCode = this.ruleForm.code;
          params.wxcode = this.$route.query.code;
          params.factoryId =37
          customerbindingopenid(params).then(res => {
            if(res.code){
              this.$message.success('登录成功');
              let url ='https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx2261a2befc1cd869&redirect_uri=http%3A%2F%2Fems.bninfo.com%2Fwippe&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect'
              window.open(url, '_self',)
            }else{
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.logging = false;
          })
      }
    
    },
    afterLogin(res) {
      const loginRes = res;
      if (loginRes) {
        // const { user, permissions, roles } = loginRes.data;
        // this.setUser(user);
        // this.setPermissions(permissions);
        // this.setRoles(roles);
        setAuthorization({
          token: loginRes.access_token,
          expireAt: new Date(new Date().getTime() + loginRes.expires_in),
        });
        // 获取路由配置
        // getRoutesConfig().then((result) => {
        //   const routesConfig = result.data.data;
        //   loadRoutes(
        //     { router: this.$router, store: this.$store, i18n: this.$i18n },
        //     routesConfig
        //   );
        //   this.$router.push("/demo");
        //   this.$message.success(loginRes.message, 3);
        // });
        applicationConfiguration().then((res) => {
          res.currentUser.tenantName=res.currentTenant.name;
          this.setUser(res.currentUser);
          let permissions = this.handlePermissions(res.auth.grantedPolicies);
          this.setPermissions(permissions);
          this.setRoles(res.currentUser.roles);
          loadRoutes();
          this.logging = false;
          this.$message.success("登录成功", 3);
          this.$router.push('/dashboard/analysis')
        });
      } else {
        this.error = loginRes.message;
      }
    },
    handlePermissions(obj) {
      let permissions = [];
      if (!obj) {
        return permissions;
      }
      permissions = Object.keys(obj).map((x) => {
        return {
          id: x,
          operation: [],
        };
      });
      return permissions;
    },
    countDown() {
        if (this.ruleForm.phone){
          this.disabledBtn=true
          let clock = window.setInterval(() => {
            this.totalTime--
            this.content = this.totalTime + 's后重新发送'
            if(this.totalTime==0){
              this.disabledBtn=false
              this.content ='发送验证码'
              this.totalTime=60
              clearInterval(clock)
            }
          },1000)
          let parmas={}
          parmas.phone=this.ruleForm.phone
          parmas.smsFrom='MES后台'
          if(this.typpe=='web'){
            parmas.sendType='登录验证'
            getRegisterCode(parmas).then(res=>{
            if (res.code!=1) {
              this.$message.error(res.message)
            }
          })
          }else if (this.typpe=='pe'){
            parmas.sendType='公众号绑定_PL'
            parmas.factoryId=37
            wechatcode(parmas).then(res=>{
            if (res.code!=1) {
              this.$message.error(res.message)
            }
          })
          }
         
        } else {
          this.$message.error('手机号不能为空')
        }

    },
  },
};
</script>
<style lang="less" scoped>
   @media (max-width: 420px) {
        .captcha-button {
          font-size: 14px;
        }
      } 
</style>
