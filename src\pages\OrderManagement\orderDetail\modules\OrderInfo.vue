<!--生产管理- 订单详情- 基础信息 -->
<template>
  <div class="contentInfo">
    <a-card title="基础信息" :bordered="false">
      <a-form-model layout="inline" style="width: 100%">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="协同工厂" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <span>{{ showData.joinFactoryIdStr }}</span>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="8">
            <a-form-model-item label=" " :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span></span>
            </a-form-model-item>
          </a-col> -->
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="拼版编号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.orderNo }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="加急类型" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.deliveryDays }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="层数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.boardLayers }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="8">
            <a-form-model-item label="PNL尺寸(mm)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.boardHeight }} * {{ showData.boardWidth }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="PNL数量" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>下单:{{ showData.pinBanNum }}{{ showData.allCardNum > 0 ? "/投料:" + showData.allCardNum : "" }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="板材" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span
                >{{ showData.fR4TypeStr }} <span>{{ showData.fR4Tg }}</span></span
              >
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="8">
            <a-form-model-item label="板厚" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.boardThickness }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="表面处理" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.surfaceFinishStr }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="阻焊颜色" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.solderColorStr }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="8">
            <a-form-model-item label="文字颜色" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.fontColorStr }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="测试方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.flyingProbeStr }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="投产面积(m²)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span v-if="showData.boardWidth && showData.boardHeight && showData.pinBanNum"
                >下单:{{ ((showData.boardWidth * showData.boardHeight * showData.pinBanNum) / 1000000.0).toFixed(2) || "" + " m2" }}
                {{ showData.allCardArea > 0 ? "/投料:" + showData.allCardArea : "" }}</span
              >
              <span v-else> </span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="出货面积(m²)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.shipArea }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="生产交期" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.deliveryDate }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="最小孔径" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.vias }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="孔数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.viasCount }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="槽孔数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.cViasCount }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="导热系数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.invoiceStr }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="二次孔数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.viasCount2 }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="二次槽孔数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.cViasCount2 }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="子订单数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.orderCount }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="锣程(m)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.luoLength }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="测试点" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.testCount }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="板材单价(￥)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>未匹配</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="状态" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.statusStr }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="拼版人员/审核人" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.adminName }} / </span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="订单类型" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.orderTypeStr }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="线路菲林数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.flCountLine }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="阻焊菲林数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.flCountSolder }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="文字菲林数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.flCountFont }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="交期天数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.deliveryDays }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="铜厚(内/外)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span
                >{{ showData.innerCopperThickness }} / <span style="color: red">{{ showData.copperThickness }}</span></span
              >
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="特殊备注" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.copyRemark }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="拼板开料图" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div v-viewer v-if="showData.klFileUrl" style="height: 60px">
                <img :src="showData.klFileUrl" style="height: 60px" />
              </div>
              <div v-else style="height: 60px"></div>
              <!-- <img :src="' data:image/png;base64,'+ showData.klFileUrl" /> -->
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="拼板压合图" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div v-viewer v-if="showData.yhFileUrl">
                <img :src="showData.yhFileUrl" style="height: 60px" />
              </div>
              <div v-else style="height: 60px"></div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="拼板示意图" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div v-viewer v-if="showData.pnlleUrlA">
                <img :src="showData.pnlleUrlA" style="height: 60px" />
                <img :src="showData.pnlleUrlB" style="height: 60px" />
              </div>

              <div v-else style="height: 60px"></div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="大料张数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.bigBoardNum }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="PNL数/大料" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.outSheetToPNLNum }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="大板尺寸" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <span>{{ showData.sheetWidth }} x {{ showData.sheetHeight }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="拼板生成流程" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <span>未匹配</span>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>

    <a-modal title="拼版图" :visible="makeupVisible" :footer="null" @cancel="handleCancel">
      <makeup-pic ref="makeup"></makeup-pic>
    </a-modal>
  </div>
</template>

<script>
import { getEditOrderInfo, getSelectOption } from "@/services/mkt/orderInfo";
import MakeupPic from "@/pages/OrderManagement/orderDetail/modules/MakeupPic";
import $ from "jquery";
export default {
  name: "OrderInfo",
  props: ["showData"],
  components: { MakeupPic },
  data() {
    return {
      selectOption: [],
      formData: {},
      dataVisible: false,
      dataVisible1: false,
      ProcessEdgeWidth: 0,
      makeupVisible: false, // 拼版图弹窗开关
      ReportList: [], // 出货报告列表
    };
  },
  computed: {
    ozValue() {
      let arr_ = [];
      for (var i = 0; i < this.formData.boardLayers; i++) {
        if (i == 0 || i == this.formData.boardLayers - 1) {
          arr_.push(this.formData.copperThickness);
        } else {
          arr_.push(this.formData.innerCopperThickness);
        }
      }
      return arr_.join("/");
    },
  },
  filters: {
    invoiceFilter(val) {
      let val_ = "";
      switch (val) {
        case 0:
          val_ = "默认";
          break;
        case 1:
          val_ = "1.0w";
          break;
        case 2:
          val_ = "1.5w";
          break;
        case 3:
          val_ = "2.0w";
          break;
        case 4:
          val_ = "0.5w";
          break;
        case 5:
          val_ = "0.7w";
          break;
        case 6:
          val_ = "5w";
          break;
        case 8:
          val_ = "8w";
          break;
        case 9:
          val_ = "3w";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
    camFilter(val) {
      let val_ = "";
      switch (val) {
        case 1:
          val_ = "中级";
          break;
        case 2:
          val_ = "高级";
          break;
        case 3:
          val_ = "资深";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
  },
  created() {
    console.log("11", this.showData);
  },
  watch: {
    formData(val) {
      if (val.needReportList != null) {
        this.ReportList = val.needReportList.split(",");
      }
      // console.log(this.ReportList)
    },
  },
  methods: {
    getData() {
      getSelectOption().then(res => {
        if (res.code) {
          this.selectOption = res.data;
          console.log("this.selectOption", this.selectOption);
        }
      });
    },
    getEditData() {
      let id = this.$route.query.id;
      getEditOrderInfo(id).then(res => {
        if (res.code) {
          // 铜厚（内、外）：innerCopperThickness  copperThickness
          // 孔径：vias
          // 金手指倒斜边：goldfinger
          // 阻抗：impedanceSize
          // 阻抗报告：impedanceReport
          // 阻抗测试条：zkTestStrip
          // 工程文件：hasCamFile
          // 导热系数：invoice
          // 工程师等级：camEngineer
          // 出货报告材质：reportMaterial
          // 平均孔铜 ：holeCuRequest
          // 最小单点: minPoint
          let data_ = res.data;
          data_.boardLayers = data_.boardLayers ? data_.boardLayers.toString() : "";
          data_.innerCopperThickness = data_.innerCopperThickness ? data_.innerCopperThickness.toString() : "";
          data_.copperThickness = data_.copperThickness ? data_.copperThickness.toString() : "";
          data_.vias = data_.vias ? data_.vias.toString() : "";
          data_.goldfinger = data_.goldfinger ? data_.goldfinger.toString() : "不需要";
          data_.impedanceSize = data_.impedanceSize ? data_.impedanceSize.toString() : "不需要";
          data_.impedanceReport = data_.impedanceReport ? data_.impedanceReport.toString() : "不需要";
          data_.zkTestStrip = data_.zkTestStrip ? data_.zkTestStrip.toString() : "不需要";
          data_.invoice = data_.invoice ? data_.invoice.toString() : "";
          data_.camEngineer = data_.camEngineer ? data_.camEngineer.toString() : "";
          data_.reportMaterial = data_.reportMaterial ? data_.reportMaterial.toString() : "";
          data_.holeCuRequest = data_.holeCuRequest ? data_.holeCuRequest.toString() : "0";
          data_.minPoint = data_.minPoint ? data_.minPoint.toString() : "0";
          this.formData = data_;
          let arr = this.formData.processEdges.split(":");
          this.$set(this.formData, "processEdges1", arr[0]);
          this.$set(this.formData, "ProcessEdgeWidth", arr[1]);
          // let arr1 =this.formData.pinBanType.toLowerCase().split('x')
          // this.$set(this.formData,'showNum',arr1[0]*arr1[1]*this.formData.num)
          console.log("this.formData", this.formData);
        }
      });
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    sendImg(event, url) {
      this.$emit("updateImgUrl", url);
    },
    // 查看拼版图
    jigsawPuzzleClick() {
      console.log(this.formData);
      var arr = this.formData.pinBanType.toLowerCase().split("x");
      this.makeupVisible = true;
      this.$nextTick(() => {
        this.$refs.makeup.impositionInformationExample(
          this.formData.boardHeight,
          this.formData.boardWidth,
          arr[0] || 1,
          arr[1] || 1,
          this.formData.processEdges1 || "无",
          this.formData.ProcessEdgeWidth || 0,
          this.formData.vCut || "none",
          this.formData.grooveWidth || 0,
          this.formData.grooveHeight || 0
        );
      });
    },
    // 下载工程文件
    down1() {
      if (this.showData.pcbFileName) {
        window.location.href = this.showData.pcbFileName;
      }
    },
    // 下载工程文件
    down() {
      if (this.formData.factoryProductFile) {
        window.location.href = this.formData.factoryProductFile;
      }
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
  },
};
</script>

<style scoped lang="less">
.contentInfo {
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 2px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 16px;
        font-weight: 500;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }
    .ant-card-body {
      padding: 24px;
      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 32px;
          .ant-select {
            width: 120px;
          }
          .ant-input {
            width: 120px;
          }
          .ant-input-number {
            width: 120px;
          }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          label {
            font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          }
        }
        .ant-form-item-control-wrapper {
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 8px 10px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
