<!-- 市场管理 - 合同管理 - pl -->
<template>
    <div class="pdfDom1" >
        <div id="pdfDompl"  style="color: black;font-family:NSimSun;">
            <div style="display: flex;width: 100%;">
            <!-- <img   src="@/assets/img/pllogo.png" style="height: 70px;padding-left: 100px;">         -->
            <img :src="contractdata.logo_" crossorigin="anonymous" style="height: 70px;padding-left: 100px;">
            <div style="width: 867px;text-align: center;">
                <span style="font-size: 24px;font-weight: bold;letter-spacing:3px">{{ contractdata.factory_ }}</span><br/>
                <span style="font-size: 22px;font-weight: bold;">{{ contractdata.contract_ }}</span><br/>
            </div>
            <div style="margin-top: 15px;font-size: 10px;">
                <span style="font-size: 20px;font-weight: bold;">对账期: 2024-02-01 至 2024-02-29</span>
            </div>            
            </div>
            <div style="display: flex;padding-top: 15px;padding-bottom: 15px;">
                <div style="width:900px;">
                    <div>{{ contractdata.fromFactory_ }}</div>
                    <div>{{ contractdata.factoryadd_ }}</div>
                    <div>{{ contractdata.attn_ }}</div>
                    <div>{{ contractdata.tel_ }} {{ contractdata.eml_ }}</div>
                </div>
                <div>
                    <div>{{ contractdata.name_ }}</div>
                    <div>{{ contractdata.address_ }}</div>
                    <div>{{ contractdata.link_ }} </div>
                    <div>{{ contractdata.linkTel_ }}  </div>
                </div>
                
            </div>
            <div>
                <table border="1" style="font-size: 12px;text-align: center;">
                    <thead>
                        <tr>
                            <td style="width: 80px;">下单日期</td>
                            <td style="width: 120px;">合同编号</td>
                            <td style="width: 120px;">客户型号</td>
                            <td style="width: 120px;">本厂编码</td>
                            <td style="width: 40px;">数量</td>
                            <td style="width: 40px;">单位</td>
                            <td style="width: 40px;">单价</td>
                            <td style="width: 60px;">工程费</td>
                            <td style="width: 80px;">表面工艺费</td>
                            <td style="width: 60px;">加急费</td>
                            <td style="width: 60px;">飞测费</td>
                            <td style="width: 60px;">测试架</td>
                            <td style="width: 60px;">加成费</td>
                            <td style="width: 60px;">厚铜费</td>
                            <td style="width: 80px;">其他费用</td>
                            <td style="width: 80px;">合同金额</td>
                            <td style="width: 80px;">送货日期</td>
                            <td style="width: 160px;">送货单号</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for="(item,index) in contractdata.priceList "  :key="index">
                            <td>  {{item.offlineDate}}  </td>
                            <td>  {{ item.contratNo }}  </td>         
                            <td>  {{ item.custName }}  </td>                                     
                            <td>  {{ item.name_ }}  </td>     
                            <td>  {{ item.qty }}  </td>     
                            <td>  {{ item.bType }}  </td>     
                            <td>  {{ item.pcs }}  </td>     
                            <td>  {{ item.eng }}  </td>     
                            <td>  {{ item.surf }}  </td>     
                            <td>  {{ item.urgent }}  </td>     
                            <td>  {{ item.fly }}  </td>     
                            <td>  {{ item.test }}  </td>     
                            <td>  {{ item.addF }}  </td>     
                            <td>  {{ item.cu }}  </td>     
                            <td>  {{ item.other }}  </td>     
                            <td>  {{ item.total }}  </td>
                            <td>  {{ item.custdate }}  </td>     
                            <td>  {{ item.delno }}  </td>   
                        </tr>
                        <tr>
                            <td ></td> 
                            <td ></td> 
                            <td ></td> 
                            <td style="text-align: right;padding-right: 10px;" colspan="6">本期发货金额合计 : {{ amountto }}</td>
                            <td style="text-align: left;padding-left: 15px;" colspan="9">大写金额: {{convertToChineseNum(amountto)}}  </td>
                            
                        </tr>
                    </tbody>                  
                </table>
            </div>
            <div style="z-index:99;position: relative;padding-top: 25px;font-size: 16px;">
               <div style="padding-left: 30px;">{{ contractdata.sura }}</div>
               <div style="display: flex;">
                  <div style="width: 410px;text-align: center;margin-top: 75px;">
                   {{ contractdata.factory_}}
                    <a-divider style="background-color: black;height: 1px;"/>
                </div>
                <div style="margin-top: 75px;margin-left: 600px;width: 420px;">
                    <span>{{ contractdata.custname_ }}</span><span style="float: right;">(签字/盖章)</span>
                    <a-divider style="background-color: black;height: 1px;"/>
                </div>
               </div>
            </div>
            <!-- <img src="@/assets/img/lhdcz.png" style="height: 120px;width: 120px;position: relative;top: -96px;left: 769px;z-index: 0;display: block;"  >     -->
        </div>
    </div>
  </template>
  <script>
  import convertToChineseNum from '@/utils/convertToChineseNum';
  import htmlToPdf from '@/utils/htmlToPdfa3';
  export default {
    name: "",
    props:['contractdata'],    
    computed:{  },
    data() {
      return { 
        amountto:0,
      }
    },
    mounted() {  
        this.amountto =0
        for (let index = 0; index < this.contractdata.priceList.length; index++) {
            if(this.contractdata.priceList[index].total && this.contractdata.priceList[index].total!='/'){
                this.amountto += Number(this.contractdata.priceList[index].total)
            }  
        } 
        this.amountto = this.amountto ? this.getFloat(this.amountto,4) : 0
    },
    methods: { 
        convertToChineseNum,
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getReportPdf(){
            htmlToPdf('pdfDompl',this.contractdata.filename_)     
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
  .agreement{
    padding-bottom: 20px;
    position: relative;
    z-index: 99;
    div{
        padding-top: 10px;
    }
  }
  .tableclass {
    border:1px solid black;
    margin-top: 10px;
    margin-bottom: 10px;
    td{
        border-right:1px solid black;
        border-bottom:1px solid black;
        padding-left: 7px;
    }
    tbody{
        tr{
            height: 24px;
        }
    }

  }
  .pdfDom1{
      padding: 25px;
      height: 650px;
      overflow: auto;
  }
  </style>
