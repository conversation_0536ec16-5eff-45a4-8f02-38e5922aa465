<template>
  <!-- :rowSelection="selectedRows ? {selectedRowKeys: selectedRowKeys, onChange: updateSelect} : undefined" -->
  <div class="standard-table">
    <a-table
      :bordered="bordered"
      :loading="loading"
      :columns="columns"
      :dataSource="dataSource"
      :rowKey="rowKey"
      :pagination="pagination"
      :customRow="onCustomRow"
      @change="onChange"
      :rowClassName="isRedRow"
      :scroll="{ y: 722, x: 1000 }"
    >
      <span slot="connectionString" slot-scope="text, record" @contextmenu.prevent.stop="rightClick1($event, text)">
        <a :title="record.connectionString" style="color: rgba(0, 0, 0, 0.65)">{{ record.connectionString }}</a>
      </span>
      <template slot-scope="text, record, index" :slot="slot" v-for="slot in Object.keys($scopedSlots).filter(key => key !== 'expandedRowRender')">
        <slot :name="slot" v-bind="{ text, record, index }"></slot>
      </template>
      <template :slot="slot" v-for="slot in Object.keys($slots)">
        <slot :name="slot"></slot>
      </template>
      <template slot-scope="record, index, indent, expanded" :slot="$scopedSlots.expandedRowRender ? 'expandedRowRender' : ''">
        <slot v-bind="{ record, index, indent, expanded }" :name="$scopedSlots.expandedRowRender ? 'expandedRowRender' : ''"></slot>
      </template>
    </a-table>
    <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
      <a-menu-item @click="down">复制</a-menu-item>
    </a-menu>
  </div>
</template>

<script>
export default {
  name: "StandardTable",
  props: {
    bordered: Boolean,
    loading: [Boolean, Object],
    columns: Array,
    dataSource: Array,
    rowKey: {
      type: [String, Function],
      default: "key",
    },
    pagination: {
      type: [Object, Boolean],
      default: true,
    },
    isCheck: {
      type: Boolean,
      default: false,
    },
    scroll: Object,
    selectedRows: Array,
    expandedRowKeys: Array,
    expandedRowRender: Function,
  },
  data() {
    return {
      needTotalList: [],
      rowKeys: [],
      row: [],
      proOrderId: "",
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      menuData: {},
    };
  },
  methods: {
    rightClick1(e, text) {
      if (text || text == 0) {
        this.text = text;
        this.menuVisible = true;
        this.menuStyle.top = e.clientY - 110 + "px";
        this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
        document.body.addEventListener("click", this.bodyClick);
      }
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    down() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },

    onCustomRow(record) {
      return {
        on: {
          // 事件
          click: event => {
            this.$emit("selctedData", record);
            this.proOrderId = record.id;
            if (this.isCheck) {
              this.$emit("checkColumn", record);
            }
          }, // 点击行
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.id && record.id == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      // if (record.isReOrder == 1 || record.status == 24) {
      //  str.push('fontRed')
      // }

      // if(str.length > 1){
      //   str = [str[0]]
      // }
      // console.log('str.concat(strGroup):',str.concat(strGroup))
      return str.concat(strGroup);
    },
    updateSelect(selectedRowKeys, selectedRows) {
      console.log(selectedRowKeys, selectedRows);
      this.$emit("update:selectedRows", selectedRows);
      this.$emit("selectedRowChange", selectedRowKeys, selectedRows);
    },
    initTotalList(columns) {
      const totalList = columns
        .filter(item => item.needTotal)
        .map(item => {
          return {
            ...item,
            total: 0,
          };
        });
      return totalList;
    },
    onClear() {
      this.updateSelect([], []);
      this.$emit("clear");
    },
    onChange(pagination, filters, sorter, { currentDataSource }) {
      this.$emit("change", pagination, filters, sorter, { currentDataSource });
    },
  },
  created() {},
  watch: {
    selectedRows(selectedRows, selectedRowKeys) {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, "selectedRows: ", selectedRows);
    },
  },
  computed: {
    selectedRowKeys() {
      return this.selectedRows.map(record => {
        return typeof this.rowKey === "function" ? this.rowKey(record) : record[this.rowKey];
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
/deep/.ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
}

.tabRightClikBox {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
}
// /deep/.min-table{
//     .ant-table-body{
//         min-height:658px;
//     }
// }
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
.standard-table {
  .alert {
    margin-bottom: 16px;
    .message {
      a {
        font-weight: 600;
      }
    }
    .clear {
      float: right;
    }
  }
}
</style>
