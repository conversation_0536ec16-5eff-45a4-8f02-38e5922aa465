<!-- 市场管理 - 订单预审主组件 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectMake">
      <span style="position: absolute; top: -4%; right: 0%; color: #ff9900; font-size: 16px">{{ showNote }}</span>
      <div style="width: 100%; display: flex; position: relative">
        <div class="leftContent" @contextmenu.prevent="rightClick($event)">
          <left-table-make
            :columns="columns1"
            :dataSource="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'id'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            @getOrderDetail="getOrderDetail"
            @getJobInfo="getJobInfo"
            @previewClick="previewClick"
            @OperationLog="OperationLog"
            @getmail="getmail"
            @CustomerRulesClick="CustomerRulesClick"
            :params1="params1"
            ref="orderTable"
            class="leftstyle"
          >
          </left-table-make>
        </div>
      </div>
      <div class="footerAction">
        <make-action
          ref="action"
          :assignLoading="assignLoading"
          @queryClick="queryClick"
          @MakeStartClick="MakeStartClick"
          @TakeOrderClick="TakeOrderClick"
          @ChargebackClick="ChargebackClick"
          @modifyInfoClick="modifyInfoClick"
          @Editemail="Editemail"
          @RepairCompletedClick="RepairCompletedClick"
          @uploadPCBFileClick="uploadPCBFileClick"
          @wenkeClick="wenkeClick"
          @orderaccess="orderaccess"
          @ProductionStandardClick="ProductionStandardClick"
          @GenerateStackClick="GenerateStackClick"
          @RepairRecordClick="RepairRecordClick"
          @load="load"
          @StackImpedanceClick="StackImpedanceClick"
          @CustomerRulesClick="CustomerRulesClick"
          @CuttingClick="CuttingClick"
          @getOrderList="getOrderList"
          @ReviewSheet="ReviewSheet"
          @Releasewarning="Releasewarning"
          @fileswereadded="fileswereadded"
          @OrderSplitting="OrderSplitting"
          :total="pagination.total"
        />
      </div>
      <div id="modal-container"></div>
      <a-modal
        title="订单接入"
        :visible="oderdataVisible"
        @cancel="reportHandleCancel"
        @ok="orderhandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form-model-item label="返单更改" :labelCol="{ span: 7 }" :wrapperCol="{ span: 1 }" style="text-align: center; margin-bottom: 0">
          <a-checkbox v-model="form.isReorder"></a-checkbox>
        </a-form-model-item>
        <a-form-model-item label="生产型号：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12 }">
          <a-input v-model="form.proOrderNo" allowClear />
        </a-form-model-item>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <query-info ref="queryInfo" :Quoterlist="Quoterlist" />
      </a-modal>
      <!-- 客户规则弹窗 -->
      <a-modal
        title="客户规则"
        :visible="dataVisible7"
        @cancel="reportHandleCancel2"
        :getContainer="getModalContainer"
        destroyOnClose
        :maskClosable="false"
        :mask="false"
        :width="960"
        :force-render="true"
        :dialogClass="'dialogClass'"
      >
        <template #footer>
          <a-button @click="dataVisible7 = false">关闭(Esc)</a-button>
        </template>
        <customer-rules-info ref="CustomerRules" :CustomerData="CustomerData" />
      </a-modal>
      <a-modal title="指示检查信息" :visible="dataVisible2" centered @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back" @click="reportHandleCancel">取消</a-button>
          <a-button key="back1" type="primary" v-if="check" @click="continueClick">继续</a-button>
        </template>
        <div class="class" style="font-size: 16px">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
        </div>
      </a-modal>
      <!-- 操作日志弹窗 -->
      <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose :maskClosable="false" @cancel="reportHandleCancel" :width="800" centered>
        <div class="projectackend">
          <a-table
            :columns="laborcolumns"
            :dataSource="labordata"
            :pagination="false"
            :scroll="{ y: 541 }"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
          >
          </a-table>
        </div>
        <template slot="footer">
          <a-button @click="reportHandleCancel" type="primary">取消</a-button>
        </template>
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        title=" 确认弹窗"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        cancel-text="取消(Esc)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="font-size: 16px" v-if="orderno">【{{ orderno }}】</span>
        <span style="font-size: 16px">{{ message }}</span>
      </a-modal>
      <a-modal title="检查信息" :visible="dataVisible22" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" centered :width="600">
        <template #footer>
          <a-button key="back" type="primary" @click="reportHandleCancel">取消</a-button>
          <a-button key="back1" type="primary" v-if="check1" @click="continueClick">继续</a-button>
        </template>
        <div class="class" style="font-size: 16px">
          <p v-for="(item, index) in checkData1" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
        </div>
      </a-modal>
      <!-- 订单拆分 -->
      <a-modal
        title="订单拆分"
        :visible="orderVisible"
        @cancel="reportHandleCancel"
        @ok="handleOkorder"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form-model ref="orderForm" :model="orderForm" :rules="rules">
          <a-form-model-item label="数量" prop="splitNum" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-input v-model="orderForm.splitNum" allowClear :autoFocus="true" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal
        title="编辑邮箱"
        :visible="emailVisible"
        @cancel="emailcancel"
        @ok="debouncedEmailhandleOk"
        :loading="emailloading"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="800"
        centered
      >
        <a-form-model-item label="*确认内容：" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }" class="required">
          <a-textarea allowClear :auto-size="{ minRows: 4, maxRows: 10 }" :auto-focus="true" v-model="email" @focus="focusNextOnEnter" />
        </a-form-model-item>
        <a-form-model-item label="附件上传" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
          <a-upload
            name="file"
            accept=".jpg,.jpeg,.png,.gif,.bmp,"
            ref="fileRef"
            :before-upload="beforeUpload1"
            :customRequest="httpRequest1"
            :file-list="fileListData"
            @change="handleChange1"
          >
            <a-button style="font-weight: 500; height: 30px"> 上传图片 </a-button>
            <a-button style="font-weight: 500; height: 30px; margin-left: 10px" @click.stop="showCopy()" title="点击 ctrl+V粘贴上传">
              粘贴图片
            </a-button>
          </a-upload>
        </a-form-model-item>
      </a-modal>
      <a-modal
        title="邮件信息"
        :visible="MaildataVisible"
        :closable="false"
        destroyOnClose
        :maskClosable="false"
        :force-render="true"
        :mask="false"
        :width="700"
        centered
        class="mailcalss"
      >
        <!--    :getContainer="getModalContainer1" -->
        <span style="padding-left: 7px; font-weight: bold; font-size: 20px"> {{ showTitle }}</span>
        <a-button type="primary" style="float: right" @click="reportHandleCancel">关闭</a-button>
        <a-divider />
        <div class="drawre" v-html="messageList" style="background-color: #ffffff; border: 1px solid #a3a3a3"></div>
        <a-divider />
        <div class="img-box">
          <ul>
            <li
              v-for="(item, index) in attList"
              style="position: relative; user-select: none"
              :title="item.split('attid')[0]"
              :key="index"
              class="liback"
              @click.right="rightClick1($event, item, index)"
            >
              <img v-if="item.toLowerCase().includes('pdf')" style="width: 25px; padding-left: 5px" src="@/assets/icon/pdf.png" />
              <img
                v-else-if="
                  item.toLowerCase().includes('jpg') ||
                  item.toLowerCase().includes('png') ||
                  item.toLowerCase().includes('bmp') ||
                  item.toLowerCase().includes('jpeg')
                "
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/jpg.png"
              />
              <img
                v-else-if="item.toLowerCase().includes('xlsx') || item.toLowerCase().includes('xls')"
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/12.png"
              />
              <img v-else-if="item.toLowerCase().includes('txt')" style="width: 25px; padding-left: 5px" src="@/assets/icon/txt.png" />
              <img v-else-if="item.toLowerCase().includes('tif')" style="width: 25px; padding-left: 5px" src="@/assets/icon/tiff.png" />
              <img
                v-else-if="item.toLowerCase().includes('docx') || item.toLowerCase().includes('doc')"
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/docx.png"
              />
              <img
                v-else-if="item.toLowerCase().includes('zip') || item.toLowerCase().includes('rar')"
                style="width: 25px; padding-left: 5px"
                src="@/assets/icon/zip.png"
              />
              <img v-else-if="item.toLowerCase().includes('csv')" style="width: 25px; padding-left: 5px" src="@/assets/icon/csv.png" />
              <img v-else style="width: 25px; padding-left: 5px" src="@/assets/icon/inf.png" />
              <span>
                {{ item.split("attid")[0] }}
              </span>
            </li>
          </ul>
        </div>
        <a-menu :style="menuStyle1" v-if="menuVisible1" class="tabRightClikBox1">
          <a-menu-item v-if="Multiple" @click="download">下载附件</a-menu-item>
        </a-menu>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { emimeconent, downloadbyattid } from "@/services/mkt/MailList";
import axios from "axios";
import { upLoadFlyingFile } from "@/services/projectMake";
import { buttonCheck } from "@/services/mkt/OrderManagement.js";
import $ from "jquery";
import {
  TakeOrderList,
  projectMakeOrderList,
  MakeStart,
  preBackOrder,
  setreleasewarning,
  getModifyInformation,
  RepairCompleted,
  getProductionStandard,
  getGenerateStack,
  getRepairRecord,
  saveRepairRecord,
  getStackImpedance,
  getCutting,
  getCustomerInfo,
  downFile,
  sendeMimeeq,
  getFactoryList,
  graphicPreview,
  ordercheckmakeinfo,
  pcbordertonew,
  getOrderSplit,
} from "@/services/mkt/PrequalificationProduction.js";
import { Base64 } from "js-base64";
import { indicationCheck, verifyFinishedOrder, contractReviewInfo, checkuserlist } from "@/services/mkt/OrderReview.js";
import LeftTableMake from "@/pages/mkt/Orderverify/subassembly/LeftTableMake";
import MakeAction from "@/pages/mkt/Orderverify/subassembly/MakeAction";
import QueryInfo from "@/pages/mkt/Orderverify/subassembly/QueryInfo";
import CustomerRulesInfo from "@/pages/mkt/Orderverify/subassembly/CustomerRulesInfo.vue";
import { prenopeinfo, preaddnope, addnopealter, nopealter, mktConfig, emSPcborderlog } from "@/services/mkt/orderInfo";
import Cookie from "js-cookie";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "订单号",
    align: "left",
    ellipsis: true,
    dataIndex: "orderNo",
    width: 120,
    scopedSlots: { customRender: "orderNo" },
  },
  {
    title: "工厂",
    dataIndex: "contractFactoryName",
    ellipsis: true,
    width: 82,
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "订单类型",
    dataIndex: "reOrder",
    align: "left",
    ellipsis: true,
    width: 70,
    customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
  },
  {
    title: "订单数量",
    dataIndex: "num",
    ellipsis: true,
    align: "left",
    width: 70,
  },
  {
    title: "客户型号",
    align: "left",
    ellipsis: true,
    width: 400,
    dataIndex: "customerModel",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    width: 124,
  },
  {
    title: "版本",
    dataIndex: "proRevStr",
    align: "left",
    ellipsis: true,
    width: 45,
  },
  {
    title: "客规",
    dataIndex: "isCustRule",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "isCustRule" },
    width: 40,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 60,
    ellipsis: true,
    align: "left",
  },
  {
    title: "预审人",
    dataIndex: "preName",
    width: 60,
    ellipsis: true,
    align: "left",
  },
  {
    title: "解析状态",
    dataIndex: "analysisFinishStr",
    width: 68,
    ellipsis: true,
    align: "left",
  },
  {
    title: "解析时长",
    scopedSlots: { customRender: "analysisSpan" },
    width: 68,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      return a.analysisSpan - b.analysisSpan;
    },
  },
  // {
  //   title: "图形预览",
  //   width:'8%',
  //   ellipsis: true,
  //   align: "left",
  //   scopedSlots: { customRender: 'preview' },
  // },
  {
    title: "上传时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 150,
  },
  {
    title: "交货时间",
    dataIndex: "deliveryDate",
    align: "left",
    ellipsis: true,
    width: 95,
  },
  {
    title: "建立人",
    dataIndex: "createName",
    align: "left",
    ellipsis: true,
    width: 68,
  },
  // {
  //   title: "订单来源",
  //   dataIndex: "orderSource",
  //   width: '6%',
  //   ellipsis: true,
  //   align: "left",
  // },
  {
    title: "操作",
    width: 45,
    align: "center",
    scopedSlots: { customRender: "labelUrl" },
    class: "noCopy",
  },
];
const columns4 = [
  {
    title: "操作人",
    dataIndex: "inUserName",
    width: 35,
    ellipsis: true,
    align: "center",
  },
  {
    title: "创建时间",
    dataIndex: "indate",
    width: 55,
    ellipsis: true,
    align: "center",
  },
  {
    title: "备注信息",
    dataIndex: "conent",
    width: 35,
    ellipsis: true,
    align: "left",
  },
  {
    title: "图片",
    width: 35,
    scopedSlots: { customRender: "picUrl" },
    align: "center",
  },
];
const laborcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 20,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作时间",
    align: "left",
    dataIndex: "createTime",
    width: 65,
  },
  {
    title: "操作人",
    align: "left",
    dataIndex: "userName",
    width: 30,
  },
  {
    title: "内容",
    align: "left",
    dataIndex: "content",
    width: 185,
  },
];
export default {
  name: "PrequalificationProduction",
  components: { MakeAction, QueryInfo, LeftTableMake, CustomerRulesInfo },
  inject: ["reload"],
  data() {
    return {
      emailVisible: false,
      emailloading: false,
      Quoterlist: [],
      showNote: "",
      showTitle: "",
      attid: "",
      mailid: "",
      attList: [], //附件列表
      MaildataVisible: false,
      menuVisible1: false,
      messageList: "",
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      Multiple: false,
      email: "",
      fileListData: [],
      filepath: [],
      file: "",
      modalWidth: 960,
      isFullscreen: false,
      isDraggableResizableActive: true,
      isDraggableResizableEnabled: true,
      modalTop: 100,
      modalLeft: 100, // 客户规则弹窗
      returOrderNo: "",
      labordata: [],
      oderdataVisible: false,
      form: {
        proOrderNo: "",
        isReorder: false,
      },
      returCust: "",
      val: [],
      redata: {},
      factoryList: [],
      spinning: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      query: {
        OrderNo: "",
      },
      columns1,
      params1: {},
      orderListData: [],
      orderListTableLoading: false,
      orderDetailData: [],
      orderDetailTableLoading: false,
      columns4,
      laborcolumns,
      jobData: [],
      jobTableLoading: false,
      note: "",
      cnNote: "",
      erpKey: "",
      proOrderId: "",
      assignLoading: false,
      selectedRowsData: {},
      allNote: "",
      dataVisible: false, // 查询弹窗开关
      dataVisible7: false, // 客户规则开关
      dataVisible8: false,
      dataVisible10: false,
      dataVisible9: false,
      ParameterData: {},
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      stackListData: {}, // 生成叠层
      RepairRecordData: [], // 返修记录
      StackImpedanceData: [], // 叠层阻抗
      CuttingData: [], // 开料拼版
      CustomerData: [], // 客户规则
      check: false,
      checkData: [],
      dataVisible2: false,
      dataVisible3: false,
      message: "",
      orderno: "",
      type: "",
      deletId: "",
      pageStat: false, //分页点击不清除
      queryParam: {},
      dataVisible22: false,
      labordataVisible: false,
      checkData1: [], // 指示检查数据
      checkType: "",
      check1: false,
      isMovedown: false,
      startPosition: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      isCtrlPressed: false,
      orderVisible: false,
      rules: {
        splitNum: [
          { required: true, message: "拆分数量请必填", trigger: "blur" },
          {
            pattern: /^(?:[1-9]|10)$/,
            message: "请填写1到10之间的正整数",
            trigger: "blur",
          },
        ],
      },
      orderForm: {
        splitNum: null,
      },
    };
  },
  created() {
    this.debouncedEmailhandleOk = this.debounce(this.emailhandleOk, 500);
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.getOrderList();
      this.handleResize();
      checkuserlist().then(res => {
        if (res.code) {
          this.Quoterlist = res.data;
        }
      });
    });

    // this.selectClick();  // 获取协同工厂列表
    // if(this.$route.query.id){
    //   let params={}
    //   params.OrderNo = queryData.OrderNo;
    //   params.custNo = queryData.custNo;
    //   params.PcbFileName = queryData.PcbFileName

    // }
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
    ordercheckmakeinfo().then(res => {
      if (res.code) {
        this.showNote = res.data;
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
    window.removeEventListener("paste", this.getClipboardFiles);
  },
  methods: {
    checkPermission,
    handleResize() {
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 134 + "px";
      } else {
        leftContent.style.height = "778px";
      }
      if (leftstyle && this.orderListData.length != 0) {
        leftstyle.style.height = window.innerHeight - 174 + "px";
      } else {
        leftstyle.style.height = 0;
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.action.nums * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }

        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    getmail(record) {
      this.attList = [];
      this.mailid = record.answerMailId;
      emimeconent(this.mailid)
        .then(res => {
          if (res.code) {
            this.showTitle = res.data.subject;
            this.messageList = Base64.decode(res.data.htmlBody);
            res.data.atts.forEach(ite => {
              this.attList.push(ite.attName + "attid" + ite.id);
            });
            this.MaildataVisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          var arr = document.getElementsByClassName("handle");
          for (var i = 0; i < arr.length; i++) {
            arr[i].style.display = "block";
          }
        });
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.reportHandleCancel();
        this.queryClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.dataVisible3) {
        this.handleOk3();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "81" && this.isCtrlPressed && checkPermission("MES.MarketModule.Prequalificationproduction.PreGetOrder")) {
        this.reportHandleCancel();
        this.TakeOrderClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "83" && this.isCtrlPressed && checkPermission("MES.MarketModule.Prequalificationproduction.PreStart")) {
        this.reportHandleCancel();
        this.MakeStartClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.orderVisible) {
        this.handleOkorder();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    OperationLog(record) {
      this.labordataVisible = true;
      emSPcborderlog(record.id).then(res => {
        if (res.code) {
          this.labordata = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取订单
    getOrderList(queryData) {
      this.params1 = {};
      this.pageStat = localStorage.getItem("stat3") == "true" ? true : false;
      let obj = JSON.parse(localStorage.getItem("ysqueryInfo"));
      if (obj) {
        this.params1 = obj;
        queryData = obj;
      }
      if (this.pageStat) {
        let pageCurrent = localStorage.getItem("pageCurrent3");
        if (pageCurrent != undefined && pageCurrent != "") {
          this.pagination.current = parseInt(pageCurrent);
        }
        let pageSize = localStorage.getItem("pageSize3");
        if (pageSize != undefined && pageSize != "") {
          this.pagination.pageSize = parseInt(pageSize);
        }
        let data = localStorage.getItem("queryParam3");
        if (data != null && data != undefined && data != "") {
          this.queryParam = JSON.parse(data);
        }
      }
      this.queryParam.pageIndex = this.pagination.current;
      this.queryParam.pageSize = this.pagination.pageSize;
      let data = {
        ...this.queryParam,
      };
      localStorage.setItem("queryParam3", JSON.stringify(data));
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      if (queryData) {
        params.OrderNo = queryData.OrderNo;
        params.custNo = queryData.custNo;
        params.PcbFileName = queryData.PcbFileName;
        params.proOrderNo = queryData.proOrderNo;
        params.CheckAccount = queryData.CheckAccount;
        params.ReOrder = queryData.ReOrder;
        params.createName = queryData.createName;
      }
      this.orderListTableLoading = true;
      let indexId = "";
      indexId = localStorage.getItem("id3");
      let record = "";
      record = localStorage.getItem("record3");
      this.$nextTick(function () {
        params.PcbFileName = params.PcbFileName ? params.PcbFileName.replace(/\s+/g, " ").trim() : "";
        projectMakeOrderList(params)
          .then(res => {
            if (res.code) {
              this.orderListData = res.data.items;
              setTimeout(() => {
                this.handleResize();
              }, 0);
              if (
                (params.OrderNo ||
                  params.PcbFileName ||
                  params.proOrderNo ||
                  params.CheckAccount ||
                  params.custNo ||
                  params.ReOrder ||
                  params.createName) &&
                this.orderListData.length
              ) {
                this.$refs.orderTable.selectedRowKeysArray[0] = [this.orderListData[0].id];
                this.$refs.orderTable.selectedRowsData.orderNo = this.orderListData[0].orderNo;
                this.$refs.orderTable.proOrderId = this.orderListData[0].id;
                this.$refs.orderTable.selectedRowsData = this.orderListData[0];
              }
              const pagination = { ...this.pagination };
              pagination.total = res.data.totalCount;
              localStorage.removeItem("ysqueryInfo");
              this.pagination = pagination;
              if (indexId !== "" && indexId != null) {
                this.$refs.orderTable.proOrderId = indexId;
                this.$refs.orderTable.selectedRowKeysArray[0] = indexId;
              }
              if (record !== "" && record != null) {
                this.$refs.orderTable.selectedRowsData = JSON.parse(record);
              }
              if (this.pageStat) {
                localStorage.removeItem("id3");
                localStorage.removeItem("record3");
                localStorage.removeItem("pageCurrent3");
                localStorage.removeItem("pageSize3");
                localStorage.removeItem("stat3");
              }
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.orderListTableLoading = false;
          });
      });
    },
    ReviewSheet() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      }
      this.spinning = true;
      contractReviewInfo(this.$refs.orderTable.selectedRowKeysArray[0], 1)
        .then(res => {
          if (res.code === 0) {
            this.$message.error(res.message);
            return;
          }
          let data = res;
          let _self = this;
          let fileReader = new FileReader();
          fileReader.onload = function (event) {
            try {
              let jsonData = JSON.parse(event.target.result); // 说明是普通对象数据，后台转换失败
              if (jsonData.code == 0) {
                // 接口返回的错误信息
                _self.$message.error(jsonData.message); // 弹出的提示信息
              }
            } catch (err) {
              const blob = new Blob([res], { type: " application/pdf;charset=utf-8" });
              const url = window.URL.createObjectURL(blob);
              const link = document.createElement("a");
              link.style.display = "none";
              link.href = url;
              let str = "";
              let cutStr = "";
              const arr = _self.$refs.orderTable.selectedRowKeysArray;
              for (var a = 0; a < arr.length; a++) {
                if (
                  _self.orderListData.filter(item => {
                    return item.id == arr[a];
                  }).length
                ) {
                  str = _self.orderListData.filter(item => {
                    return item.id == arr[a];
                  })[0].pcbFileName;
                  cutStr =
                    "(" +
                    _self.orderListData.filter(item => {
                      return item.id == arr[a];
                    })[0].custNo +
                    ")";
                }
              }
              link.setAttribute("download", str + cutStr + "-合同评审单.pdf");
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link); // 点击后移除，防止生成很多个隐藏a标签
            }
          };
          fileReader.readAsText(data); // 注意别落掉此代码，可以将 Blob 或者 File 对象转根据特殊的编码格式转化为内容(字符串形式)
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 获取订单详情
    getOrderDetail(record) {
      // this.orderDetailTableLoading = true;
      // projectBackEndOrderDetail(record.id).then(res => {
      //     if (res.data) {
      //       console.log('res.data:',res.data)
      //       console.log('record:',record)
      //       this.note = res.data.note;
      //       this.cnNote = res.data.cnNote;
      //       this.noteHtml(record)
      //       this.orderDetailData = res.data;
      //       // console.log('this.orderDetailData:',this.orderDetailData)
      //     }
      // }).finally(() => {
      //   this.orderDetailTableLoading = false;
      // })
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng =
          file.type.toLowerCase() === "image/jpeg" ||
          file.type.toLowerCase() === "image/png" ||
          file.type.toLowerCase() === "image/gif" ||
          file.type.toLowerCase() === "image/bmp" ||
          file.type.toLowerCase() === "image/jpg";
        if (!isJpgOrPng) {
          _this.$message.error("图片只支持*.jpg;*.png;*.gif;*.jpeg;*.bmp 格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData);
      }
      this.fileListData = fileList;
      this.filepath = [];
      fileList.forEach(item => {
        this.filepath.push(item.response);
      });
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    showCopy() {
      window.removeEventListener("paste", this.getClipboardFiles);
      window.addEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload1(file); // 上传组件自带的函数，这里有些图片校验规则
      const formData = new FormData();
      formData.append("file", file);
      axios({
        url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file", // 接口地址
        method: "post",
        data: formData,
      }).then(res => {
        if (res.code) {
          file.status = "done";
          file.response = res.data;
          file.thumbUrl = res.data;
          file.url = res.data;
          file.uid = new Date().valueOf();
          const arr = [];
          arr.push({
            name: file.name,
            uid: file.uid,
            response: file.response,
            size: file.size,
            status: file.status,
            type: file.type,
            thumbUrl: file.thumbUrl,
            url: file.url,
          });
          this.handleChange1(file, arr);
        } else {
          this.$message.error(data.message || "网络异常,请重试或检查网络连接状态");
        }
      });
    },
    //编辑邮箱
    Editemail() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择需要进行编辑的订单");
        return;
      }
      if (!this.$refs.orderTable.selectedRowsData.mailId) {
        this.$message.error("该订单不是邮件创建,无法使用该功能");
        return;
      }
      this.emailVisible = true;
      this.email = "您好!\n此订单有问题需要和贵司确认,请帮忙及时回复下,谢谢!";
      this.filepath = [];
      this.fileListData = [];
    },
    emailhandleOk() {
      if (/^\s*$/.test(this.email)) {
        this.$message.warning("请输入确认内容");
        return;
      }
      this.emailloading = true;
      window.removeEventListener("paste", this.getClipboardFiles);
      this.emailVisible = false;
      let params = {
        id: this.$refs.orderTable.selectedRowsData.id,
        remark: this.email,
        images: this.filepath,
      };
      sendeMimeeq(params)
        .then(res => {
          if (res.code) {
            this.$message.success(res.message);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.emailloading = false;
        });
    },
    emailcancel() {
      window.removeEventListener("paste", this.getClipboardFiles);
      this.emailVisible = false;
    },
    focusNextOnEnter() {
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    // 取单
    TakeOrderClick() {
      this.dataVisible3 = true;
      this.message = "确定取单吗？";
      this.type = "1";
    },
    // 开始
    MakeStartClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.dataVisible3 = true;
      this.message = "确定预审开始吗？";
      this.type = "2";
    },
    // 退单
    ChargebackClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.dataVisible3 = true;
      this.message = "确定预审回退吗？";
      this.type = "3";
    },
    // 完成
    modifyInfoClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.dataVisible3 = true;
      this.message = "确定预审完成吗？";
      this.type = "4";
    },
    //解除警告
    Releasewarning() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.dataVisible3 = true;
      this.message = "确定解除警告吗？";
      this.type = "5";
    },
    // 问客
    wenkeClick() {
      if (!this.$refs.orderTable.selectedRowsData.id) {
        this.$message.warning("请选择订单");
        return;
      }
      let OrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      //订单预审跳转问客OrderNo与businessOrderNo传参一致
      const routeOne = this.$router.resolve({
        path: "/gongcheng/eqDetails",
        query: {
          OrderNo: OrderNo,
          eQSource: 5,
          businessOrderNo: OrderNo,
          joinFactoryId: this.$refs.orderTable.selectedRowsData.joinFactoryId,
          id: this.$refs.orderTable.selectedRowsData.id,
          Jump: "预审",
          uid: this.$refs.orderTable.selectedRowsData.uid,
        },
      });
      window.open(routeOne.href, "_self", routeOne.query);
      // this.$router.resolve({path:'/gongcheng/eqDetails',query:{ OrderNo:OrderNo,eQSource:5,} })
      // getWenkeUrl(this.$refs.orderTable.selectedRowKeysArray[0]).then(res =>{
      //   if (res.code){
      //     console.log(res)
      //     window.open(res.data,"_blank").location
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
    },
    //指示检查
    // checkInquiry(){
    //   if(!this.$refs.orderTable.selectedRowKeysArray[0]){
    //     this.$message.warning("请选择订单")
    //     return
    //   }
    //   indicationCheck(this.$refs.orderTable.selectedRowKeysArray[0]).then(res=>{
    //     if(res.code){
    //       this.checkData = res.data
    //         this.check = this.checkData.findIndex(v => v.error == '1') < 0
    //       console.log('this.checkData',this.checkData,this.check)
    //       this.dataVisible2 = true
    //     }else{
    //       this.$message.error(res,message)
    //     }
    //   })
    // },

    // 查询
    queryClick() {
      this.dataVisible = true;
    },
    orderaccess() {
      this.oderdataVisible = true;
    },
    orderhandleOk() {
      let formData = { proOrderNo: this.form.proOrderNo, isReorder: this.form.isReorder };
      pcbordertonew(formData).then(res => {
        if (res.code) {
          this.$message.success("导入成功");
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
      this.oderdataVisible = false;
    },
    handleOk() {
      this.params1 = this.$refs.queryInfo.form;
      var arr1 = this.params1.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      this.params1.OrderNo = arr1.join("");
      var arr2 = this.params1.custNo.split("");
      if (arr2.length > 10) {
        arr2 = arr2.slice(0, 10);
      }
      this.params1.custNo = arr2.join("");
      var arr3 = this.params1.PcbFileName.split("");
      if (arr3.length > 100) {
        arr3 = arr3.slice(0, 100);
      }
      this.params1.PcbFileName = arr3.join("");
      var arr4 = this.params1.proOrderNo.split("");
      if (arr4.length > 20) {
        arr4 = arr4.slice(0, 20);
      }
      this.params1.proOrderNo = arr4.join("");
      this.pagination.current = 1;
      localStorage.setItem("ysqueryInfo", JSON.stringify(this.params1));
      if (this.params1.proOrderNo && typeof this.params1.proOrderNo === "string" && this.params1.proOrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产型号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      this.getOrderList(this.params1);
    },
    // 图形预览
    previewClick(record) {
      graphicPreview(record.id).then(res => {
        if (res.data) {
          window.open(res.data, "_blank");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible = false; // 查询弹窗
      this.dataVisible1 = false; // 修改信息弹窗
      this.dataVisible8 = false; // 查看日志
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.dataVisible66 = false;
      this.dataVisible9 = false;
      this.dataVisible10 = false;
      this.dataVisible22 = false;
      this.labordataVisible = false;
      this.oderdataVisible = false; //订单接入弹窗
      this.orderVisible = false;
      this.orderForm.splitNum = "";
      this.MaildataVisible = false;
    },
    rightClick1(e, item, index) {
      e.preventDefault();
      this.menuVisible1 = true;
      this.attid = item.split("attid")[1];
      this.Multiple = true;
      this.menuStyle1.top = e.clientY - 40 + "px";
      this.menuStyle1.left = e.clientX - 600 + "px";
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    download() {
      downloadbyattid(this.mailid, this.attid).then(res => {
        if (res.code) {
          if (res.data) {
            var base64String = res.data;
            var binaryString = atob(base64String);
            var binaryLen = binaryString.length;
            var bytes = new Uint8Array(binaryLen);
            for (var i = 0; i < binaryLen; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }
            var blob = new Blob([bytes], { type: "application/octet-stream" });
            var url = URL.createObjectURL(blob);
            var a = document.createElement("a");
            document.body.appendChild(a);
            a.style = "display: none";
            a.href = url;
            a.download = res.message; // 这里可以自定义文件名和扩展名
            a.click();
            URL.revokeObjectURL(url);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel2() {
      this.dataVisible7 = false; // 客户规则
    },
    // 确认弹窗
    handleOk3() {
      // 取单
      if (this.type == "1") {
        this.spinning = true;
        TakeOrderList()
          .then(res => {
            if (res.code) {
              this.$message.success("成功");
              this.reload();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      // 开始
      if (this.type == "2") {
        this.spinning = true;
        MakeStart(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("成功");
              this.$router.push({
                path: "orderDetail",
                query: {
                  id: this.$refs.orderTable.selectedRowKeysArray[0],
                  orderNo: this.orderno,
                  factory: this.$refs.orderTable.selectedRowsData.joinFactoryId,
                  boid: this.$refs.orderTable.selectedRowsData.jobId,
                  ttype: "2",
                  custNo: this.$refs.orderTable.selectedRowsData.custNo,
                  reOrder: this.$refs.orderTable.selectedRowsData.reOrder,
                  isCustRule: this.$refs.orderTable.selectedRowsData.isCustRule,
                  bianji: true,
                  orderModify: this.$refs.orderTable.selectedRowsData.orderModify,
                  tradeType: this.$refs.orderTable.selectedRowsData.tradeType,
                },
              });
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      // 回退
      if (this.type == "3") {
        this.spinning = true;
        preBackOrder(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("成功");
              this.reload();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      // 完成
      if (this.type == "4") {
        this.spinning = true;
        indicationCheck(this.$refs.orderTable.selectedRowKeysArray[0], 0)
          .then(res => {
            if (res.code) {
              this.checkData = res.data;
              this.checktype = "yswc";
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              if (this.checkData.length == 0) {
                getModifyInformation(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
                  if (res.code) {
                    this.$message.success("预审完成");
                    this.getOrderList();
                  } else {
                    if (res.data.length) {
                      this.checkData = res.data;
                      this.checktype = "yswc";
                      this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                      this.dataVisible2 = true;
                    } else {
                      this.$message.error(res.message);
                    }
                  }
                });
              } else {
                this.dataVisible2 = true;
              }
            } else {
              this.$message.error(res, message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      //解除警告
      if (this.type == "5") {
        this.spinning = true;
        setreleasewarning(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("解除成功");
              this.reload();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      this.dataVisible3 = false;
    },
    continueClick() {
      if (this.checktype == "wjth") {
        this.$refs.backendAction.clickUpload();
        this.CustNo = this.$refs.orderTable.selectedRows.custNo;
        this.checkvisible = false;
      } else if (this.checktype == "yswc") {
        this.dataVisible2 = false;
        getModifyInformation(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
          if (res.code) {
            this.$message.success("预审完成");
            this.getOrderList();
          } else {
            if (res.data.length) {
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              this.dataVisible2 = true;
            } else {
              this.$message.error(res.message);
            }
          }
        });
      }
    },

    // 获取协同工厂列表
    selectClick() {
      getFactoryList().then(res => {
        if (res.code == 1) {
          this.factoryList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    load(loadData) {
      this.spinning = loadData;
    },
    //文件追加
    fileswereadded() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      buttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "OrderOfferAppendFile").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.checkType = "wjzj";
            this.dataVisible22 = true;
          } else {
            this.$refs.action.clickUpload1(this.$refs.orderTable.selectedRowKeysArray[0]);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 上传pcb文件
    uploadPCBFileClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.$refs.action.clickUpload(this.$refs.orderTable.selectedRowKeysArray[0]);
    },

    // 返修完成
    RepairCompletedClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowsData.status == 18 && this.$refs.orderTable.selectedRowsData.qaeStatus == 20) {
        this.spinning = true;
        RepairCompleted(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("返修完成");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
          });
      } else {
        this.$message.error("当前状态，不允许返修完成！");
      }
    },
    // 制作标准
    ProductionStandardClick() {
      getProductionStandard().then(res => {
        if (res.code) {
          window.open(res.data, "_blank").location;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 生成叠层
    GenerateStackClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      getGenerateStack(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        this.dataVisible5 = true;
        this.stackListData = res.data;
      });
    },
    handleOk5() {
      this.spinning = true;
      this.dataVisible5 = false;
      console.log("this.$refs.GenerateStack.selectedRowKeysArray:", this.$refs.GenerateStack.paramInfo);
      let params = {
        ProOrderId: this.$refs.orderTable.selectedRowKeysArray[0],
        id: this.$refs.GenerateStack.selectedRowKeysArray[0],
        paramInfo: this.$refs.GenerateStack.paramInfo,
        isSpecistack: this.$refs.GenerateStack.isSpecistack,
      };
      saveRepairRecord(params)
        .then(res => {
          if (res.code) {
            this.$message.success("叠层保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 返修记录
    RepairRecordClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      getRepairRecord(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          this.RepairRecordData = res.data;
          this.dataVisible6 = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk6() {
      this.dataVisible6 = false;
    },
    // 叠层阻抗
    StackImpedanceClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      getStackImpedance(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          if (res.data.drills.length) {
            let drillsData = res.data.drills;
            localStorage.setItem("drillsData", JSON.stringify({ drillsData }));
          } else {
            localStorage.removeItem("drillsData");
          }
          this.StackImpedanceData = res.data;
          // console.log('this.StackImpedanceData:',this.StackImpedanceData)
          const routeOne = this.$router.resolve({
            path: "/impedance",
            query: {
              boardType: res.data.boardType,
              finishBoardThickness: res.data.finishBoardThickness,
              layers: res.data.layers,
              pdctno: res.data.pdctno,
            },
          });
          window.open(routeOne.href, "_blank", routeOne.query);
          // this.$router.push({path: '/impedance', query:{boardType:res.data.boardType,finishBoardThickness:res.data.finishBoardThickness,layers:res.data.layers,pdctno:res.data.pdctno}})
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 开料拼版
    CuttingClick() {
      getCutting(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          this.CuttingData = res.data;
          this.$router.push({
            path: "/gongju/cutting",
            query: {
              boardThink: res.data.boardThink,
              cpLen: res.data.cpLen,
              cpWidth: res.data.cpWidth,
              job: res.data.job,
              layCount: res.data.layCount,
              pcsset: res.data.pcsset,
              pcssetName: res.data.pcssetName,
              joinFactoryId: res.data.joinFactoryId,
              numUDSix: res.data.numUDSix,
              txtCoupDL: res.data.txtCoupDL,
              txtCoupDW: res.data.txtCoupDW,
            },
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 客户规则
    CustomerRulesClick(record) {
      this.dataVisible7 = true;
      setTimeout(() => {
        $(".ant-modal-header").on("mousedown", e => {
          this.startPosition.x = e.pageX;
          this.startPosition.y = e.pageY;
          this.startPosition.offsetX = e.offsetX;
          this.startPosition.offsetY = e.offsetY;
          this.isMovedown = true;
        });
      }, 200);
      document.body.addEventListener("mousemove", e => {
        if (this.isMovedown) {
          if (
            e.x - this.startPosition.x > 10 ||
            e.y - this.startPosition.y > 10 ||
            e.x - this.startPosition.x < -10 ||
            e.y - this.startPosition.y < -10
          ) {
            let w = $(".ant-modal-wrap").width();
            let h = $(".ant-modal-wrap").height();
            // $('.ant-modal-content').css({
            //   // left: e.pageX - this.startPosition.offsetX-(document.body.clientWidth-w)/2 + 'px',
            //   left: 0,
            //   top: e.pageY -(document.body.clientHeight-3*h)/2 - 750 + 'px'
            // })
            $(".ant-modal-wrap").css({
              left: e.pageX - this.startPosition.offsetX - (document.body.clientWidth - w) / 2 + 300 + "px",
              top: e.pageY - (document.body.clientHeight - 3 * h) / 2 - 600 + "px",
            });
          }
        }
      });
      document.body.addEventListener("mouseup", e => {
        this.isMovedown = false;
      });
      let CustNo = record.custNo;
      let factory = record.joinFactoryId;
      getCustomerInfo(CustNo, factory, 0, "", record.orderNo).then(res => {
        if (res.code) {
          this.CustomerData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getModalContainer() {
      return document.querySelector("#modal-container"); // 返回弹窗容器的选择器或 HTMLElement 对象
    },
    // 获取对应的订单人员
    getJobInfo(id) {
      this.jobTableLoading = true;
      // projectBackEndJobInfo(id).then(res => {
      //   if (res.code) {
      //     this.jobData = res.data;
      //   }
      // }).finally(()=>{
      //   this.jobTableLoading = false;
      // })
    },
    // 图片字符串裁切
    picFilter(val) {
      if (val.picUrl) {
        return val.picUrl.split(",");
      } else {
        return [];
      }
    },
    // 订单表变化change
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      localStorage.removeItem("stat");
      localStorage.setItem("ysqueryInfo", JSON.stringify(this.params1));
      if (JSON.stringify(this.params1) != "{}") {
        this.getOrderList(this.params1);
      } else {
        this.getOrderList();
      }
    },
    // 行点击事件
    isRedRow(record) {
      if (record.proOrderId == this.proOrderId) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    noteHtml(record) {
      // console.log(this.note, this.cnNote, record.errChk_, record.errMsg_)
      // const noteRegStr = this.note.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${'http://admin.jiepei.com/'}$2$3`)
      // const cnNoteRegStr = this.cnNote.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${'http://admin.jiepei.com/'}$2$3`)
      // let str_ = `<div class="${this.note ? '' : 'displayFlag'}"><p>客户备注</p><div>${noteRegStr}</div></div>
      //             <div class="${this.cnNote ? '' : 'displayFlag'}"><p>业务员备注</p><div>${cnNoteRegStr}</div></div>
      //             <div class="${record.errChk_ ? '' : 'displayFlag'}"><p>分析项</p><div>${record.errChk_}</div></div>
      //             <div class="${record.errMsg_ ? '' : 'displayFlag'}"><p>输出提示</p><div>${record.errMsg_}</div></div>
      //           `
      // this.allNote = str_
    },

    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      console.log();
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 下载文件
    down0() {
      window.location.href = this.$refs.orderTable.menuData.pcbFilePath;
      if (window.location.href) {
        downFile(this.$refs.orderTable.menuData.proOrderId).then(res => {
          if (!res.code) {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 订单拆分
    OrderSplitting() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderVisible = true;
    },
    handleOkorder() {
      const form = this.$refs.orderForm;
      form.validate(valid => {
        if (valid) {
          this.orderVisible = false;
          let params = {
            id: this.$refs.orderTable.selectedRowKeysArray[0],
            splitNum: Number(this.orderForm.splitNum),
          };
          getOrderSplit(params)
            .then(res => {
              if (res.code) {
                this.$message.success("拆分成功");
                this.reload();
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.orderForm.splitNum = "";
            });
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.img-box {
  height: 60px;
  background-color: #ffffff;
  padding: 6px;
  ul {
    height: 63px;
    list-style: none;
    display: flex; /* 使用flexbox布局 */
    flex-wrap: wrap; /* 允许项目多行排列 */
    gap: 6px; /* 设置项目之间的间隔 */
    overflow: auto;
    margin-bottom: 0;
    li {
      height: 40px;
      text-align: center;
      padding: 6px;
      border: 1px solid #e0e2e4;
      border-radius: 4px;
      background-color: #e0e2e4;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 500px;
    }
  }
}
.mailcalss {
  /deep/.ant-modal-footer {
    display: none;
  }
  /deep/ .ant-modal-body {
    padding: 12px 24px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
  }
  /deep/.ant-divider-horizontal {
    display: block;
    clear: both;
    width: 100%;
    min-width: 100%;
    height: 1px;
    margin: 7px 0;
    background: #a5a5a5;
  }
}
.drawre {
  /deep/ a {
    pointer-events: none !important; //禁止点击链接
  }
  color: black;
  height: 600px;
  padding: 10px;
  border-bottom: 2px solid #e0e2e4;
  white-space: break-spaces;
  &::-webkit-scrollbar {
    //整体样式
    width: 7px !important; //y轴滚动条粗细
    height: 7px !important; //x轴滚动条粗细
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #a5a5a5;
  }
  overflow: auto;
}
.memo-tooltip {
  :global(.ant-tooltip-inner) {
    max-width: 370px;
    // background-color: #ff990042;
    // border: 3px solid #ff9900;
    // color:black;
  }
}
.tabRightClikBox1 {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    line-height: 30px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.required {
  /deep/.ant-form-item-label > label {
    color: red;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-table-column-sorter {
  display: none !important;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
#modal-container {
  /deep/.ant-modal-wrap {
    position: absolute;
    width: 960px;
    height: fit-content;
    top: 72px;
    left: 72px;
  }
  /deep/.ant-modal {
    top: 0;
  }
}

.projectackend {
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}

/deep/.ant-modal-body {
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}

/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectMake {
  background: #ffffff;
  /deep/.leftContent {
    border: 2px solid rgb(238, 238, 238);
    border-bottom: 4px solid #e9e9f0;
    // height:762px;
    .min-table {
      .ant-table-body {
        min-height: 720px;
      }
      .ant-table-placeholder {
        display: none;
      }
    }

    .ant-table-body {
      .ant-table-fixed {
        width: 1260px !important;
      }
    }
    width: 100%;
    // border: 2px solid rgb(233, 233, 240);
    .tabRightClikBox {
      // border:2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
  /deep/ .userStyle {
    user-select: all !important;
  }
  .rightContent {
    border-bottom: 2px solid rgb(233, 233, 240);
    width: 25%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    /deep/ .centerTable {
      .ant-table-bordered.ant-table-empty .ant-table-placeholder {
        border-left: 0;
        border-right: 0;
      }
      .ant-table-body {
        max-height: 741px !important;
      }
      width: 100%;
      height: 774px;
      border: 2px solid rgb(233, 233, 240);
    }
  }
  .footerAction {
    width: 100%;
    height: 48px;
    border: 2px solid #e9e9f0;
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display: none;
    }
  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
      .eqBackground {
        background: #ffff00;
      }
      .statuBackground {
        background: #b0e0e6;
      }
      .backUserBackground {
        background: #a7a2c9;
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    .userStyle {
      user-select: all !important;
    }
  }
  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 12px 0 0 10px;
  }
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #d6d6d6;
    p {
      line-height: 35px;
      font-weight: 700;
      margin: 0;
      img {
        width: 100%;
      }
    }
    .displayFlag {
      display: none;
    }
  }
}
.modalSty {
  /deep/.ant-modal .ant-modal-content .ant-modal-body {
    padding: 0;
  }
}
</style>
