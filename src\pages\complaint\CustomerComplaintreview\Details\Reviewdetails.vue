<!--投诉工厂-品质复核详情-->
<template>
    <div class="Reviewdetails">
        <a-collapse :activeKey="'1'" @change="CollapseList">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>投诉详情</div>
                        <div>{{ text }}</div>
                    </div>
                </template>
            <div>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'客户型号'">
                            <a-input disabled v-model="complaintdata.custNo"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'客户联系人'">
                            <a-input disabled v-model="complaintdata.factoryContactName"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'联系电话'">
                            <a-input disabled v-model="complaintdata.factoryContactNumber"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'投诉日期'">
                            <a-input disabled v-model="complaintdata.complaintDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'生产型号'">
                            <a-input disabled v-model="complaintdata.proOrderNo"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货日期'">
                            <a-input disabled v-model="complaintdata.deliveryDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货总数'" >
                            <a-input disabled v-model="complaintdata.deliveryNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'不良总数'">
                            <a-input disabled v-model="complaintdata.badNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'周期'" >
                            <a-input disabled v-model="complaintdata.dateCode"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'问题描述'" >
                            <div class="questiontext">{{ complaintdata. problemDescription}}</div>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <span style="color: #428bca;cursor: pointer;"  @click="filedown(complaintdata.complaintFilePath)"> 
                                <a-icon type="link" style="padding: 0 5px;"></a-icon>{{complaintdata.complaintFileName}}
                            </span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'不良批次要求采取的措施'" >
                            <a-radio-group   disabled name="radioGroup" v-model="complaintdata.takeSteps" >
                                <a-radio :value="1">退货返工</a-radio>
                                <a-radio :value="2">报废补货</a-radio>
                                <a-radio :value="3">报废扣款</a-radio>
                                <a-radio :value="4">特采</a-radio>
                                <a-radio :value="5">其他</a-radio>
                            </a-radio-group>
                            <a-input style="width: 486px;" v-model="complaintdata.takeStepsName"  disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废品形态'" >
                            <a-radio-group  disabled name="radioGroup" >
                                <a-radio value="1">PCB</a-radio>
                                <a-radio value="2">PCBA</a-radio>
                                <a-radio value="3">整机</a-radio>
                                <a-radio value="4">其它</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'报废品数量描述'" >
                            <a-input disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>   
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'不良板成本(元)'" >
                            <a-input disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'扣款总金额(元)'" >
                            <a-input disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row> 
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'是否需要8D报告'" >
                            <a-radio-group  disabled name="radioGroup" v-model="complaintdata.need8DReport">
                                <a-radio :value="false">否</a-radio>
                                <a-radio :value="true">是</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>    
                    <a-col :span="16" >
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'要求改善报告回复客户时间'" >
                            <a-radio-group  disabled name="radioGroup" v-model="complaintdata.needReplytime">
                                <a-radio :value="3">3天</a-radio>
                                <a-radio :value="2">2天</a-radio>
                                <a-radio :value="1">1天</a-radio>                              
                                <a-radio :value="0">其他</a-radio>
                            </a-radio-group>
                            <a-input v-model="complaintdata.needReplytimeName" disabled style="width: 100px;"></a-input>
                        </a-form-model-item>
                    </a-col>             
                </a-row>
                <a-row>
                   
                </a-row>
            </div>
            </a-collapse-panel>
        </a-collapse>
        <a-collapse :activeKey="'1'" @change="CollapseList1" style="margin-top: 15px;">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>厂商回复</div>
                        <div>{{ text1 }}</div>
                    </div>
                </template>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'评审结果'" >
                            <a-radio-group    name="radioGroup"  >
                                <a-radio value="1">不属实</a-radio>
                                <a-radio value="2">属实,我司责任</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'备注说明'" >
                            <a-textarea   :auto-size="{ minRows: 4, maxRows:6 }"></a-textarea >
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'解决方案'" >
                            <a-radio-group    name="radioGroup" v-model="complaintdata.reviewTakeSteps" >
                                <a-radio :value="1">退货返工</a-radio>
                                <a-radio :value="2">补货</a-radio>
                                <a-radio :value="3">报废</a-radio>
                                <a-radio :value="4">特采</a-radio>
                                <a-radio :value="5">其他</a-radio>
                            </a-radio-group>
                            <a-input style="width: 300px;"  v-model="complaintdata.reviewTakeStepsName"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'备注情由'" >
                            <a-textarea   :auto-size="{ minRows: 4, maxRows:6 }"></a-textarea >
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'8D报告'" >
                            <a-upload  
                            ref="fileRef"
                            :file-list="fileList"
                            :before-upload="beforeUpload"
                            accept=".jpg,.bmp,.png,.zip,.rar,.7z"
                            @change="handleChange">
                            <a-button> <a-icon type="upload" /> 选择文件</a-button>
                            <span style="padding:0 15px;" v-if="fileList.length==0" >未上传任何附件</span>
                            </a-upload>                           
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row style="margin-top: 15px;">
                    <a-col :span="16">
                            <a-button type="primary" style="margin-left: 185px;">确定</a-button>
                            <a-button type="normal" style="margin-left: 30px;" @click="back">返回</a-button>
                    </a-col>
                </a-row>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>
<script>
import {custbyid} from "@/services/complaint/QualitativeComplaint.js";
export default {
    name:'Reviewdetails',
    props:[],
    data() {
        return {
            text:'收起',
            text1:'收起',
            isFileType:false,
            fileList:[],
            complaintdata:{},
        }
    },
    mounted(){
        custbyid(this.$route.query.id).then(res=>{
            if(res.code){
                this.complaintdata=res.data
            }
        })
    },
    methods:{
        filedown(path){         
            if(path){
                window.location.href = path
            }
        },
        handleChange({ fileList },data) { 
         if( this.isFileType){
            this.fileList = fileList;
            if(this.fileList.length > 1){
            this.fileList.splice(0,1)
            return
            }
        }
        },
        beforeUpload(file){
            this.isFileType = file.name.toLowerCase().indexOf('.zip') != -1 || file.name.toLowerCase().indexOf('.rar') != -1||
            file.name.toLowerCase().indexOf('.jpg') != -1 || file.name.toLowerCase().indexOf('.bmp') != -1||
            file.name.toLowerCase().indexOf('.png') != -1 || file.name.toLowerCase().indexOf('.7z') != -1
                if (!this.isFileType) {
                    this.$message.error('附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式,文件大小不超过10M');
                    return false
                }
        },
        back(){
            this.$router.push({path:'CustomerComplaintreview',query:{} })
        },
        CollapseList(val){
            if(val.length){
                this.text = '收起'
            }else{
                this.text = '展开'
            }
        },
        CollapseList1(val){
            if(val.length){
                this.text1 = '收起'
            }else{
                this.text1 = '展开'
            }
        },
    },
    created(){

    }
}
</script>
<style lang="less" scoped>
.Reviewdetails{
    padding: 10px;
    overflow: auto;
    height: 821px;
    background-color: white;
    border: 1px solid #e8e8e8;
    &::-webkit-scrollbar {
        width: 6px; 
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        background: #ff9900;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #ffffff;
      }
    /deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
    right: 16px !important;
    left: auto;
    }
    /deep/.ant-radio-disabled .ant-radio-inner::after {
        background-color: #ff9900;
    }
    /deep/.ant-radio-disabled .ant-radio-inner {
        background-color: #ffffff;
        border-color: #d9d9d9  !important;
        cursor: not-allowed;
    }
    /deep/.ant-radio-disabled + span {
        color: black;
        cursor: not-allowed;
    }
    /deep/.ant-radio-checked .ant-radio-inner{
        border-color: #ff9900  !important;
    }
    /deep/.ant-divider-horizontal {
        display: block;
        clear: both;
        width: 100%;
        min-width: 100%;
        height: 1px;
        margin: 13px 0;
    }
    /deep/.ant-form-item{
        margin-bottom: 0;
    }
}

</style>