<!-- 工具管理- 工程案例-按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box" v-if="type == '1' && checkPermission('MES.ToolModule.EngineeringCase.EngineeringCaseRepait')">
      <a-button type="primary" @click="editClick"> 编辑 </a-button>
    </div>
    <div class="box" v-if="type == '0' && checkPermission('MES.ToolModule.EngineeringCase.EngineeringCaseCancel')">
      <a-button type="primary" @click="cancelClick"> 取消 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ToolModule.EngineeringCase.EngineeringCaseSave')">
      <a-button type="primary" @click="saveClick"> 保存 </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ToolModule.EngineeringCase.EngineeringCaseSaveNew')">
      <a-button type="primary" @click="addClick"> 新增 </a-button>
    </div>

    <div class="box" v-if="checkPermission('MES.ToolModule.EngineeringCase.EngineeringDelete')">
      <a-button type="primary" @click="$emit('deleteclick')"> 删除 </a-button>
    </div>
    <!--    <div v-if='advanced'  >-->
    <!--      <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetFixRecord')">-->
    <!--        <a-button type="primary"  >-->
    <!--          打开APP-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--  </div>-->
    <!--    <span class="box">-->
    <!--      <a-button type="dashed" @click="toggleAdvanced">-->
    <!--        {{advanced ? '收起' : '展开'}}-->
    <!--        <a-icon :type="advanced ? 'right' : 'left'" />-->
    <!--      </a-button>-->
    <!--    </span>-->
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import protocolCheck from "@/utils/protocolcheck";
import TagSelectOption from "@/components/tool/TagSelectOption";
import { finish, upLoadCamFile } from "@/services/projectMake";

export default {
  name: "MakeAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    selectId: {
      type: String,
    },
    type: {
      type: String,
    },
  },
  data() {
    return {
      selectValue: "生成叠层",
      advanced: false,
      width: 762,
      collapsed: false,
      orderId: "",
    };
  },
  created() {
    this.$nextTick(() => {
      if (this.$refs.active.children.length > 7) {
        let domLength = this.$refs.active.children.length;
        let sty_ = "";
        for (var i = 0; i < domLength; i++) {
          if (i == this.$refs.active.children.length - 1) {
            sty_ = "order:11";
          } else {
            sty_ = "order:" + i * 2;
          }
          this.$refs.active.children[i].style.cssText = sty_;
        }
        this.width = 1500;
        this.collapsed = true;
      } else {
        this.collapsed = false;
        this.width = 762;
      }
    });
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      if (this.advanced) {
        width_ = 1500;
      } else {
        width_ = 762;
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 编辑
    editClick() {
      this.$emit("editClick");
    },
    // 取消
    cancelClick() {
      this.$emit("cancelClick");
    },
    // 保存
    saveClick() {
      this.$emit("saveClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 新增
    addClick() {
      this.$emit("addClick");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 106px;
    margin-top: 12px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
