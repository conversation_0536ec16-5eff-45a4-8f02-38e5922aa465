<!-- 市场管理 - 价格体系 - 按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.PriceSystem.PriceManage4AreaID')"
      :class="checkPermission('MES.MarketModule.PriceSystem.PriceManage4AreaID') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="customerArea"> 客户区域 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.PriceSystem.PriceSystemAdd')"
      :class="checkPermission('MES.MarketModule.PriceSystem.PriceSystemAdd') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="addClick"> 新增 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.PriceSystem.PriceSystemUp') && activeKey != '6'"
      :class="checkPermission('MES.MarketModule.PriceSystem.PriceSystemUp') && activeKey != '6' ? 'showClass' : ''"
    >
      <a-button type="primary" @click="editClick"> 编辑 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.PriceSystem.PriceSystemDel')"
      :class="checkPermission('MES.MarketModule.PriceSystem.PriceSystemDel') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="delClick"> 删除 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.MarketModule.PriceSystem.ExportPrice')"
      :class="checkPermission('MES.MarketModule.PriceSystem.ExportPrice') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ExportPrice"> 导出价格 </a-button>
    </div>
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px; width: 100px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="ExportPrice" v-if="checkPermission('MES.MarketModule.PriceSystem.ExportPrice')">导出价格</a-menu-item>
            <a-menu-item @click="queryClick">查询</a-menu-item>
            <a-menu-item @click="delClick" v-if="checkPermission('MES.MarketModule.PriceSystem.PriceSystemDel')">删除</a-menu-item>
            <a-menu-item @click="editClick" v-if="checkPermission('MES.MarketModule.PriceSystem.PriceSystemUp') && activeKey != '6'"
              >编辑</a-menu-item
            >
            <a-menu-item @click="addClick" v-if="checkPermission('MES.MarketModule.PriceSystem.PriceSystemAdd')">新增</a-menu-item>
            <a-menu-item @click="customerArea" v-if="checkPermission('MES.MarketModule.PriceSystem.PriceManage4AreaID')">客户区域</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { stringify } from "querystring";

export default {
  name: "BackendAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    disbackend: {
      type: Boolean,
    },
    activeKey: {
      type: String,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
      nums: "",
      buttonsmenu: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      this.nums = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      const num = elements.length * 104;
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "inline-block";
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "inline-block";
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    customerArea() {
      this.$emit("customerArea");
    },
    queryClick() {
      this.$emit("queryClick");
    },
    addClick() {
      this.$emit("addClick");
    },
    editClick() {
      this.$emit("editClick");
    },
    delClick() {
      this.$emit("delClick");
    },
    ExportPrice() {
      this.$emit("ExportPrice");
    },
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 92px;
    margin-top: 8px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
