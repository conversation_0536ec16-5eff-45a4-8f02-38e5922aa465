<template>
  <div ref="SelectBox">
    <div>
      <a-form-model layout="inline" style="margin-top: 10px; border-top: 1px solid #ddd; width: 390px" id="formDataElemFour">
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="层压不可更改" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.isChangeLayerPres" ref="firstInput" v-focus-next-on-tab="'2'"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="标记位置"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.markPosition && iseval(requiredLinkConfigpro.markPosition.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.markPosition"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="changemark"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="11"
                  v-focus-next-on-tab="'12'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.MarkPosition)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="不接受打叉" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <a-checkbox v-model="proOrderInfoDto.acceptCrossed" ref="2" v-focus-next-on-tab="'3'"></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="UL类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.ulType && iseval(requiredLinkConfigpro.ulType.isNullRules) ? 'require' : ''"
            >
              <div class="heightSty">
                <a-select
                  v-model="proOrderInfoDto.ulType"
                  mode="multiple"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="12"
                  v-focus-next-on-tab="'13'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.ULType)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="不允许补线" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <a-checkbox v-model="proOrderInfoDto.notAcceptPatching" ref="3" v-focus-next-on-tab="'4'"></a-checkbox>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="标记类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.markType && iseval(requiredLinkConfigpro.markType.isNullRules) ? 'require' : ''"
            >
              <div class="heightSty">
                <a-select
                  v-model="proOrderInfoDto.markType"
                  mode="multiple"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="13"
                  v-focus-next-on-tab="'14'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.MarkType)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="测试方式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.flyingProbe && iseval(requiredLinkConfigpro.flyingProbe.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.flyingProbe"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  @change="flyChange"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="4"
                  v-focus-next-on-tab="'5'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FlyingProbe)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="标记面向"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.markFace && iseval(requiredLinkConfigpro.markFace.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.markFaceStr }}</span>
              <div v-else>
                <a-select
                  v-model="proOrderInfoDto.markFace"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="14"
                  v-focus-next-on-tab="'15'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.MarkFace)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3">
            <a-form-model-item
              label="盖ET章"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.stampEtPosition && iseval(requiredLinkConfigpro.stampEtPosition.isNullRules) ? 'require' : ''"
            >
              <a-select
                v-model="proOrderInfoDto.stampEtPosition"
                showSearch
                allowClear
                optionFilterProp="label"
                :getPopupContainer="() => this.$refs.SelectBox"
                ref="5"
                v-focus-next-on-tab="'6'"
              >
                <a-select-option
                  v-for="item in mapKey(selectData.StampEtPosition)"
                  :key="item.value"
                  :value="item.value"
                  :label="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="周期格式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.periodicFormat && iseval(requiredLinkConfigpro.periodicFormat.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.periodicFormat"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :disabled="prohibit"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="15"
                  v-focus-next-on-tab="'16'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PeriodicFormat)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="飞针测试方式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.fpTestMethod && iseval(requiredLinkConfigpro.fpTestMethod.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.fpTestMethod"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="6"
                  v-focus-next-on-tab="'7'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FpTestMethod)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="客户指定" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.custAssignment ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.custAssignment" ref="16" v-focus-next-on-tab="'17'"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="测试治具编号"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.testFixtureNumber && iseval(requiredLinkConfigpro.testFixtureNumber.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-input v-model="proOrderInfoDto.testFixtureNumber" allowClear ref="7" v-focus-next-on-tab="'8'" @blur="truncationtfn()"></a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="低阻测试"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.isLowResistanceTest && iseval(requiredLinkConfigpro.isLowResistanceTest.isNullRules) ? 'require' : ''"
            >
              <span v-if="!editFlg1">{{ proOrderInfoDto.isLowResistanceTest ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.isLowResistanceTest" ref="17" v-focus-next-on-tab="'18'"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="检验标准"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.ipcLevel && iseval(requiredLinkConfigpro.ipcLevel.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.ipcLevel"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="8"
                  v-focus-next-on-tab="'9'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.IPCLevel)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="确认工作稿" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <span v-if="!editFlg1">{{ proOrderInfoDto.confirmWorkingDraft ? "是" : "" }}</span>
              <div v-else>
                <a-checkbox v-model="proOrderInfoDto.confirmWorkingDraft" ref="18" v-focus-next-on-tab="'19'"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3" class="colSTY">
            <a-form-model-item
              label="翘曲度"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.warpage && iseval(requiredLinkConfigpro.warpage.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.warpage"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate4($event, mapKey(selectData.Warpage))"
                  @search="handleSearch4($event, mapKey(selectData.Warpage))"
                  @blur="handleBlur4($event, mapKey(selectData.Warpage))"
                  ref="9"
                  v-focus-next-on-tab="'10'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.Warpage)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="产品用途"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigpro.productUsage && iseval(requiredLinkConfigpro.productUsage.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.productUsage"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="10"
                  v-focus-next-on-tab="'11'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.ProductUsage)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" class="colSTY">
            <a-form-model-item label="确认阻抗" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div>
                <a-checkbox v-model="proOrderInfoDto.confirmImpedance" ref="19" v-focus-next-on-tab="'20'"></a-checkbox>
              </div>
            </a-form-model-item>
            <a-form-model-item label="确认叠层" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div>
                <a-checkbox
                  v-model="proOrderInfoDto.confirmStacking"
                  ref="20"
                  v-focus-next-on-tab="'21'"
                  @keydown.native.enter="handleLastInputEnter"
                ></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>
<script>
let index = -1;
let focusInput = false;
import { productInformation } from "@/services/projectIndicate";
import $ from "jquery";
export default {
  name: "ProThree",
  props: [
    "shouldjump",
    "proOrderInfoDto",
    "selectData",
    "editFlg1",
    "boardBrandList",
    "sheetTraderList",
    "messageList",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "requiredLinkConfigpro",
    "boardtgList",
    "ManufacturerTG",
  ],
  inject: ["reload"],
  components: {},
  data() {
    return {
      ymdata: [],
      SolderResistInk: [],
      SolderResistInk1: [],
      CharacterResistInk: [],
      CharacterResistInk1: [],
      spinning: false,
      prohibit: false,
      prohibit1: false,
      copyOld: "",
      copyNewVal: "",
      selectData1: {},
      selectValue: "",
      index: -1,
      sheetTrader: [],
      boardBrand: [],
      selectedValue: "",
      editValue: "",
      editable: false,
      options: [
        { value: "option1", label: "Option 1" },
        { value: "option2", label: "Option 2" },
        { value: "option3", label: "Option 3" },
      ],
    };
  },
  mounted() {
    this.changemark();
    this.boardBrand = this.boardBrandList;
    this.sheetTrader = this.sheetTraderList;
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  directives: {
    focusNextOnTab: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode !== 13 || focusInput) return; // 非回车键直接返回
          // 清理无效的 refs 引用
          const refs = vnode.context.$refs;
          for (const key in refs) {
            if (!refs[key]) delete refs[key];
          }

          const refKeys = Object.keys(refs);
          const currentIndex = refKeys.indexOf(value); // 当前元素索引

          // 情况1：目标元素不存在于 refs 列表
          if (currentIndex === -1) {
            const nextIndex = typeof index !== "undefined" ? index + 1 : 0;
            if (nextIndex < refKeys.length) {
              const nextInput = refs[refKeys[nextIndex]];
              if (nextInput && !nextInput.disabled) {
                index = nextIndex;
                nextInput.focus();
              }
            }
          }
          // 情况2：目标元素存在且可用
          else if (!refs[value].disabled) {
            index = currentIndex;
            refs[value].focus();
          }
          // 情况3：向后查找最多12个可用元素
          else {
            for (let offset = 1; offset <= 12; offset++) {
              const targetIndex = currentIndex + offset;
              if (targetIndex >= refKeys.length) break;

              const targetKey = refKeys[targetIndex];
              const targetEl = refs[targetKey];

              if (targetEl && !targetEl.disabled) {
                targetEl.focus();
                break; // 找到第一个可用元素后停止
              }
            }
          }
        });
      },
    },
  },
  watch: {
    shouldjump: {
      handler(newVal) {
        if (newVal) {
          // 聚焦第一个输入框
          focusInput = true;
          this.$refs.firstInput.focus();
          index = -1; // 重置索引
          // 通知父组件重置状态
          this.$emit("resetjump");
        }
        setTimeout(() => {
          focusInput = false;
        }, 500);
      },
    },
    messageList: {
      handler(val) {
        this.get(val);
      },
    },
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
          console.log(this.boardBrand, "2151");
        });
      },
    },
    "proOrderInfoDto.boardThickness"(newVal, oldVal) {
      setTimeout(() => {
        if (newVal != oldVal && newVal && oldVal) {
          this.copyOld = oldVal;
          this.copyNewVal = newVal;
        }
      }, 700);
    },
  },
  methods: {
    handleLastInputEnter(e) {
      event.preventDefault(); // 阻止默认行为（如打开下拉框）
      this.$emit("jumptofive1"); // 触发父组件的jumptofive1事件
    },
    changeType() {
      this.proOrderInfoDto.fR4Tg = null;
      let fR4Tg = [];
      if (this.boardtgList.length) {
        fR4Tg = this.proOrderInfoDto.boardBrand
          ? this.boardtgList.filter(item => {
              return item.valueMember1 == this.proOrderInfoDto.boardBrand;
            })
          : [];
      }
      if (!this.proOrderInfoDto.fR4Tg && fR4Tg.length) {
        this.proOrderInfoDto.fR4Tg = fR4Tg[0].valueMember;
      }
      if (this.ManufacturerTG.length) {
        let data = this.proOrderInfoDto.boardBrand
          ? this.ManufacturerTG.filter(item => {
              return item.coreType_ == this.proOrderInfoDto.boardBrand;
            })
          : [];
        if (data.length) {
          this.proOrderInfoDto.fR4Tg = data[0].tgValue;
          this.proOrderInfoDto.sheetTrader = data[0].verdorName_Value;
        }
      }
      if (this.boardBrandList.length && this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.proOrderInfoDto.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("val") != -1) {
        newIsNullRules = newIsNullRules.replace(/val/g, "this.proOrderInfoDto");
      }
      return eval(newIsNullRules);
    },
    truncationtfn() {
      if (this.proOrderInfoDto.testFixtureNumber) {
        var testFixtureNumber = this.proOrderInfoDto.testFixtureNumber.split("");
        if (testFixtureNumber.length > 20) {
          testFixtureNumber = testFixtureNumber.slice(0, 20);
          this.$message.warning("测试治具编号不能超过20个字符");
        }
        this.proOrderInfoDto.testFixtureNumber = testFixtureNumber.join("");
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    // 板厚更改 板厚公差=+/-板厚*0.1
    boardThickness() {
      this.$emit("boardThickness");
      if (this.proOrderInfoDto.boardThickness) {
        if (this.proOrderInfoDto.boardThickness <= 1.0) {
          this.proOrderInfoDto.boardThicknessTol = "+/-0.1";
        } else {
          this.proOrderInfoDto.boardThicknessTol = "+/-" + (this.proOrderInfoDto.boardThickness * 0.1).toFixed(2);
        }
      }
    },
    changxb() {
      if (!this.proOrderInfoDto.goldfingerBevel) {
        this.proOrderInfoDto.goldfingerBevelAngle = null;
        this.proOrderInfoDto.goldfingerBevelDepth = null;
        this.proOrderInfoDto.goldfingerBevelSurplus = null;
      }
    },
    goldenfingerChange() {
      if (!this.proOrderInfoDto.goldenfinger) {
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },
    MetalSlotChange() {
      if (this.$route.query.factory == 12) {
        if (this.proOrderInfoDto.isMetalSlot) {
          this.proOrderInfoDto.cncHoleTol = "+/-0.075";
        } else {
          this.proOrderInfoDto.cncHoleTol = "";
        }
      }
    },
    // 拼版方式更改
    pinBanType() {
      if (this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1) {
        if (this.proOrderInfoDto.boardType == "pcs") {
          this.$message.warning("拼版方式大于1x1,出货单位请选择set");
        }
      }
      // if(this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1 ){
      //   this.proOrderInfoDto.boardType = 'set'
      // }else{
      //   this.proOrderInfoDto.boardType = 'pcs'
      // }
    },
    //
    flyChange() {
      if (this.proOrderInfoDto.flyingProbe == "FlyingProbe") {
        this.proOrderInfoDto.fpTestMethod = "capacitance";
        this.proOrderInfoDto.testFixtureNumber = "";
      }
      if (
        this.proOrderInfoDto.flyingProbe == "custstand" ||
        this.proOrderInfoDto.flyingProbe == "newstand" ||
        this.proOrderInfoDto.flyingProbe == "sharestand" ||
        this.proOrderInfoDto.flyingProbe == "teststand"
      ) {
        this.proOrderInfoDto.testFixtureNumber = this.proOrderInfoDto.orderNo;
        this.proOrderInfoDto.fpTestMethod = "";
      }
    },
    changeplugoil() {
      if (
        this.proOrderInfoDto.solderCover == "plugoil" ||
        this.proOrderInfoDto.solderCover == "bgaplugoil" ||
        this.proOrderInfoDto.solderCover == "aluminiumplugoil" ||
        this.proOrderInfoDto.solderCover == "openwindowplusplugoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+converoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+openminwindow"
      ) {
        if (this.$route.query.factory == 38) {
          this.proOrderInfoDto.plugOilTool = "consentprinting";
        } else {
          this.proOrderInfoDto.plugOilTool = "aluminumsheet";
        }
        if (this.$route.query.factory != 58 && this.$route.query.factory != 59) {
          this.proOrderInfoDto.plugOilSide = "toplayer";
        }
      } else {
        this.proOrderInfoDto.plugOilTool = null;
        this.proOrderInfoDto.plugOilSide = null;
      }
    },
    changemark() {
      if (!this.proOrderInfoDto.markPosition) {
        this.proOrderInfoDto.ulType = [];
        this.proOrderInfoDto.markType = [];
        this.proOrderInfoDto.markFace = null;
        this.proOrderInfoDto.periodicFormat = null;
      }
      if (!this.proOrderInfoDto.markPosition || this.proOrderInfoDto.markPosition == null || this.proOrderInfoDto.markPosition == "") {
        this.prohibit = true;
      } else {
        this.prohibit = false;
      }
    },
    changegold() {
      if (!this.proOrderInfoDto.isGoldfinger) {
        this.prohibit1 = true;
      } else {
        this.prohibit1 = false;
      }
      if (!this.proOrderInfoDto.isGoldfinger) {
        this.proOrderInfoDto.goldenFingerAreaRe = null;
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },

    changesurface() {
      if (
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandhaslwithfree" ||
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandimmersiongold" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithfreeandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithleadandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "nickelplatingandgoldplatedfinger" ||
        this.proOrderInfoDto.surfaceFinish == "ospandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "wholeimmersiongoldandimmersiongoldfinger"
      ) {
        this.proOrderInfoDto.isGoldfinger = true;
      } else {
        this.proOrderInfoDto.isGoldfinger = false;
      }
    },
    changelayers() {
      this.$emit("changelayers");
    },
    solderColorC(type, val) {
      if (this.proOrderInfoDto.solderColor && type == "change" && val != "bot") {
        this.proOrderInfoDto.solderColorBottom = this.proOrderInfoDto.solderColor;
      }
      let solder_color = "";
      if (this.proOrderInfoDto.solderColor == "none" || this.proOrderInfoDto.solderColor == "noResistance") {
        solder_color = this.proOrderInfoDto.solderColorBottom;
      } else {
        solder_color = this.proOrderInfoDto.solderColor;
      }
      this.SolderResistInk1 = [];
      let data = [];
      if (this.proOrderInfoDto.isInkNotHalogen) {
        data = this.SolderResistInk.filter(ite => ite.solderColor == solder_color && ite.hf == true);
      } else {
        data = this.SolderResistInk.filter(ite => ite.solderColor == solder_color);
      }
      if (data.length != 0) {
        for (let index = 0; index < data.length; index++) {
          this.selectData.SolderResistInk.forEach(item => {
            if (item.valueMember == data[index].solderResistInk) {
              this.SolderResistInk1.push({
                lable: item.text,
                value: data[index].solderResistInk,
              });
            }
          });
        }
        if (this.$route.query.factory == 12) {
          if (type == "change") {
            this.proOrderInfoDto.solderResistInk = this.SolderResistInk1[0].value;
          } else {
            this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk
              ? this.proOrderInfoDto.solderResistInk
              : this.SolderResistInk1[0].value;
          }
        } else if (this.$route.query.factory == 67) {
          if (type == "change") {
            this.proOrderInfoDto.solderResistInk = this.SolderResistInk1[0].value;
          } else {
            this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk ? this.proOrderInfoDto.solderResistInk : "";
          }
          this.SolderResistInk1 = this.mapKey(this.selectData.SolderResistInk);
        } else {
          if (type == "change") {
            this.proOrderInfoDto.solderResistInk = "";
          } else {
            this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk ? this.proOrderInfoDto.solderResistInk : "";
          }
        }
      } else {
        this.SolderResistInk1 = this.mapKey(this.selectData.SolderResistInk);
        if (type == "change") {
          this.proOrderInfoDto.solderResistInk = "";
        } else {
          this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk ? this.proOrderInfoDto.solderResistInk : "";
        }
      }
    },
    changesu() {
      if (this.proOrderInfoDto.pinBanType1 && this.proOrderInfoDto.pinBanType2) {
        this.proOrderInfoDto.su = (Number(this.proOrderInfoDto.pinBanType1) * this.proOrderInfoDto.pinBanType2).toFixed();
      } else {
        this.proOrderInfoDto.su = null;
      }
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },

    setEstimateb(value, list) {
      this.proOrderInfoDto.deliveryMethod = value;
    },
    handleBlurb(value, list) {
      this.setEstimateb(value, list);
    },
    handleSearchb(value, list) {
      this.setEstimateb(value, list);
    },
    labelClick(e) {
      console.log("dianji", e);
      this.editable = !this.editable;
    },

    setEstimate1(value, list) {
      this.proOrderInfoDto.boardLayers = value;
      let a = this.getPrice(this.proOrderInfoDto.boardLayers, list, value);
      this.changelayers();
    },
    handleSearch1(value, list) {
      this.setEstimate1(value, list);
    },
    changeSheet(val) {
      if (val) {
        this.proOrderInfoDto.sheetTrader = val;
      }
      if (this.boardBrandList.length && this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.proOrderInfoDto.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
      this.proOrderInfoDto.boardBrand = "";
    },
    handleBlur1(value, list) {
      this.setEstimate1(value, list);
      if (this.proOrderInfoDto.boardLayers) {
        var boardLayers = this.proOrderInfoDto.boardLayers.split("");
        if (boardLayers.length > 2) {
          boardLayers = boardLayers.slice(0, 2);
          this.$message.warning("层数不能超过2个字符");
        }
        this.proOrderInfoDto.boardLayers = boardLayers.join("");
      }
    },
    setEstimate2(value, list) {
      this.proOrderInfoDto.innerCopperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.innerCopperThickness, list, value);
    },
    handleSearch2(value, list) {
      this.setEstimate2(value, list);
    },
    handleBlur2(value, list) {
      this.setEstimate2(value, list);
      if (this.proOrderInfoDto.innerCopperThickness) {
        var innerCopperThickness = this.proOrderInfoDto.innerCopperThickness.split("");
        if (innerCopperThickness.length > 3) {
          innerCopperThickness = innerCopperThickness.slice(0, 3);
          this.$message.warning("成品铜厚内层不能超过3个字符");
        }
        this.proOrderInfoDto.innerCopperThickness = innerCopperThickness.join("");
      }
    },
    setEstimate3(value, list) {
      this.proOrderInfoDto.copperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.copperThickness, list, value);
    },
    handleSearch3(value, list) {
      this.setEstimate3(value, list);
    },
    handleBlur3(value, list) {
      this.setEstimate3(value, list);
      if (this.proOrderInfoDto.copperThickness) {
        var copperThickness = this.proOrderInfoDto.copperThickness.toString().split("");
        if (copperThickness.length > 3) {
          copperThickness = copperThickness.slice(0, 3);
          this.$message.warning("成品铜厚外层不能超过3个字符");
        }
        this.proOrderInfoDto.copperThickness = copperThickness.join("");
      }
    },
    setEstimate4(value, list) {
      this.proOrderInfoDto.warpage = value;
      let a = this.getPrice(this.proOrderInfoDto.warpage, list, value);
    },
    handleSearch4(value, list) {
      this.setEstimate4(value, list);
    },
    handleBlur4(value, list) {
      this.setEstimate4(value, list);
      if (this.proOrderInfoDto.warpage) {
        var warpage = this.proOrderInfoDto.warpage.split("");
        if (warpage.length > 6) {
          warpage = warpage.slice(0, 6);
          this.$message.warning("翘曲度不能超过6个字符");
        }
        this.proOrderInfoDto.warpage = warpage.join("");
      }
    },
    setEstimate5(value, list) {
      this.proOrderInfoDto.vcutSurplusThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.vcutSurplusThickness, list, value);
    },
    handleSearch5(value, list) {
      this.setEstimate5(value, list);
    },
    handleBlur5(value, list) {
      this.setEstimate5(value, list);
    },
    setEstimate6(value, list) {
      this.proOrderInfoDto.minSolderBridge = value;
      let a = this.getPrice(this.proOrderInfoDto.minSolderBridge, list, value);
    },
    handleSearch6(value, list) {
      this.setEstimate6(value, list);
    },
    handleBlur6(value, list) {
      this.setEstimate6(value, list);
    },
    setEstimate7(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness, list, value);
    },
    handleSearch7(value, list) {
      this.setEstimate7(value, list);
    },
    handleBlur7(value, list) {
      this.setEstimate7(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness) {
        var cjNickelThinckness = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness.split("");
        if (cjNickelThinckness.length > 10) {
          cjNickelThinckness = cjNickelThinckness.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = cjNickelThinckness.join("");
      }
    },
    setEstimate8(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2, list, value);
    },
    handleSearch8(value, list) {
      this.setEstimate8(value, list);
    },
    handleBlur8(value, list) {
      this.setEstimate8(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2) {
        var cjNickelThinckness2 = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2.split("");
        if (cjNickelThinckness2.length > 10) {
          cjNickelThinckness2 = cjNickelThinckness2.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = cjNickelThinckness2.join("");
      }
    },
    setEstimate9(value, list) {
      this.proOrderInfoDto.boardThicknessTol = value;
      let a = this.getPrice(this.proOrderInfoDto.boardThicknessTol, list, value);
    },
    handleSearch9(value, list) {
      this.setEstimate9(value, list);
    },
    handleBlur9(value, list) {
      this.setEstimate9(value, list);
    },
    setEstimate10(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = value;
    },
    handleSearch10(value, list) {
      this.setEstimate10(value, list);
    },
    handleBlur10(value, list) {
      this.setEstimate10(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness) {
        var newTinThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness.split("");
        if (newTinThickness.length > 10) {
          newTinThickness = newTinThickness.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = newTinThickness.join("");
      }
    },
    setEstimate11(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = value;
    },
    handleSearch11(value, list) {
      this.setEstimate11(value, list);
    },
    handleBlur11(value, list) {
      this.setEstimate11(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2) {
        var newTinThickness2 = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2.split("");
        if (newTinThickness2.length > 10) {
          newTinThickness2 = newTinThickness2.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = newTinThickness2.join("");
      }
    },
    get(val) {
      $("#formDataElemFour .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
.box {
  /deep/.ant-select-dropdown--single {
    min-width: 90px;
  }
}
/deep/.ant-col-20 {
  width: 1291px;
}
/deep/.ant-col-4 {
  width: 258px;
}
/deep/.ant-col-3 {
  width: 194px;
}
/deep/.ant-col-8 {
  width: 516px;
}
/deep/.ant-col-9 {
  width: 582px;
}
.div1 {
  /deep/.ant-form-item-control {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.div2 {
  .div22 {
    /deep/.ant-form-item-control {
      padding: 0;
      min-height: 28px !important;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}

.autoo {
  /deep/.ant-form-item-control {
    height: 100% !important;
    width: 1184px;
  }
  /deep/.ant-form-item-control {
    // height: auto!important;
    background: #f5f5f5 !important;
    .ant-input {
      margin-top: 4px !important;
      margin-bottom: 0 !important;
    }
  }
}
/deep/textarea.ant-input {
  min-height: 24px;
  line-height: 1.3;
}
.speclass {
  /deep/ .ant-form-item-label {
    width: 107px;
  }
  /deep/ .ant-form-item-label > label {
    font-size: 13px !important;
  }
  /deep/ .ant-form-item-control-wrapper {
    width: 1178px;
  }
}
/deep/ .div1 {
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px !important;
    }
  }
  .ant-form-item-children {
    overflow: inherit !important;
    font-size: 13px;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  /* border-color: #ff9900; */
  border: 2px solid red;
}
/deep/.ant-select-focused .ant-select-selection {
  border: 2px solid red;
}
/deep/.ant-input:focus {
  border: 2px solid red;
}
/deep/.colSTY {
  border-left: 1px solid #ddd;
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px;
    }
  }
}
// /deep/.ant-col{
//   border-left:1px solid #ddd;
// }
/deep/.sss {
  height: 30px;
}
/deep/.ant-col-3 {
  .ant-col-14 {
    width: 58.5%;
  }
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-input {
  padding: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 11px !important;
  margin-left: 6px !important;
}

/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
// /deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
//     height: 16px!important;
//     margin-top: 3px;
//     line-height: 14px!important;
//   }
/deep/.surSTY {
  // height:56px;
  .ant-form-item-control-wrapper {
    // min-height:20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
      }
    }
  }
}
/deep/.heightSty {
  height: 28px;
  .ant-select {
    margin-top: 2px;
  }
  .ant-select-selection--multiple {
    height: 20px;
    min-height: 20px;
  }
  .ant-select-allow-clear {
    .ant-select-selection--multiple {
      height: 23px;
    }
    .ant-select-selection__rendered {
      ul {
        display: flex;
        li {
          height: 16px;
          margin-top: 2px;
          line-height: 16px;
          user-select: none;
          padding-left: 0 !important;
        }
      }
    }
    .ant-select-selection__clear {
      top: 13px;
    }
  }
}
/deep/.heightSty1 {
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 17px !important;
            margin-top: 3px;
            line-height: 15px !important;
            width: 92%;
          }

          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}

/deep/.ant-select-dropdown-menu-item {
  font-size: 14px;
  padding: 0.5%;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-size: 14px !important;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-size: 14px !important;
  font-weight: 500;

  color: #000000;
}

.ant-row {
  .ant-col-22 {
    .ant-form-item-control {
      .ant-input-affix-wrapper {
        line-height: 29px;
      }
    }
  }
  .ant-col-17 {
    .ant-form-item {
      /deep/.ant-input {
        min-height: 23px !important;
        height: 23px !important;
        line-height: 15px !important;
      }
    }
  }
  .ant-col-24 {
    /deep/.ant-form-item-label {
      width: 106px !important;
    }
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.disable {
  background: #f5f5f5 !important;
}
.box {
  overflow: auto;
  border-left: 1px solid rgb(233 230 230);
  position: relative;
  .bto {
    position: absolute;
    bottom: -48px;
    right: 374px;
  }
}

/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;
  min-height: 28px;
  .tmp1 {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 370px;
    display: inline-block;
  }
  .tmp {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 140px;
    display: inline-block;
  }
  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 25px;
    .ant-select {
      width: 20;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #f1f1f1;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        line-height: 25px;
        .ant-checkbox-wrapper {
          min-height: 28px;
        }
        .ant-select-selection--single {
          height: 22px;
        }
        .ant-select-selection__rendered {
          line-height: 18px;
        }
        .ant-select {
          height: 22px;
        }
        .ant-input {
          height: 22px;
          padding-top: 2.6px;
        }
      }
      line-height: inherit;
      padding: 0px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      height: 28px;
    }
  }
}
.div2 {
  /deep/ .ant-form-item {
    margin: 0;
    width: 100%;
    display: flex;
    min-height: 28px;
    .tmp1 {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 370px;
      display: inline-block;
    }
    .tmp {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 140px;
      display: inline-block;
    }
    .editWrapper {
      display: flex;
      align-items: center;
      min-height: 25px;
      .ant-select {
        width: 20;
      }
      .ant-input {
        width: 120px;
      }
      .ant-input-number {
        width: 120px;
      }
    }
    .ant-form-item-label {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      color: #666;
      background-color: #f1f1f1;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      label {
        font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      }
    }
    .ant-form-item-control-wrapper {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      .ant-form-item-control {
        .ant-form-item-children {
          // display: block;
          // min-height: 25px;
          line-height: 25px;
          // // vertical-align: top;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          // overflow: hidden;
          // width: 100%;
          // display: inline-block;

          // .ant-select-allow-clear{
          //   // .ant-select-selection--multiple{
          //   //   height: 23px;
          //   //   margin-top:2px;
          //   // }
          //   .ant-select-selection__rendered{
          //     ul{
          //       display: flex;
          //       li{
          //         margin-top:-1px;
          //       }
          //     }
          //     .ant-select-selection__choice{
          //       height: 18px;
          //       margin-top: 2px;
          //       line-height: 14px;
          //       user-select: none;
          //     }
          //   }
          //   .ant-select-selection__clear{
          //     top:11px;
          //   }
          // }
          .ant-checkbox-wrapper {
            min-height: 28px;
          }
          .ant-select-selection--single {
            height: 22px;
          }
          .ant-select-selection__rendered {
            line-height: 18px;
          }
          .ant-select {
            height: 22px;
          }
          .ant-input {
            height: 22px;
            padding-top: 2.6px;
          }
        }
        line-height: inherit;
        padding: 0px 5px;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        height: auto !important;
      }
    }
  }
}
</style>
