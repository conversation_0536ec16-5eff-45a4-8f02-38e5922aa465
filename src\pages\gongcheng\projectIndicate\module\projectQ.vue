<template>
    <a-card style=" min-width: 1670px;">
        <div style="display: flex">
            <div v-if="type!=1">
                <a-button v-if="type_fw=='1'" @click="project_fw(1)" style="margin-bottom:5px">编辑</a-button>
                <a-button v-else @click="project_fw(2)" style="margin-bottom:5px">取消编辑</a-button>
                <a-button  @click="btn_save" style="margin-bottom:5px;margin-left: 10px">保存</a-button>
            </div>
            <a-button  @click="$router.go(-1)" style="margin-bottom:5px;margin-left: 10px">返回</a-button>
        </div>

        <a-tabs v-model="key" @change="callback">
            <a-tab-pane key="1" tab="产品信息">
                <a-descriptions bordered  :column="column"  >
                    <a-descriptions-item  label="层数" >
                        <span v-if="type_fw=='1'">{{form.boardLayers}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.boardLayers" />
                    </a-descriptions-item >
                    <a-descriptions-item label="板mm"  >
                        <span v-if="type_fw=='1'">{{form.boardThickness}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.boardThickness" />
                    </a-descriptions-item>
                    <a-descriptions-item label="检验标准"  >
                        <select v-if="type_fw=='1'" v-model="form.ipc"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in IPC" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else v-model="form.ipc" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in IPC" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item>
                    <a-descriptions-item  label="板厚公差mm"  >
                        <span  v-if="type_fw=='1'">{{form.boardThkTolerate}}</span>
                        <a-select v-else v-model="form.boardThkTolerate" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in BoardThkTolerate" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item label="成品宽mm"  >
                        <span  v-if="type_fw=='1'">{{form.boardWidth}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.boardWidth" />
                    </a-descriptions-item>
                    <a-descriptions-item label="X/Set数"  >
                        <span v-if="type_fw=='1'">{{form.xSet}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.xSet" />
                    </a-descriptions-item>
                    <a-descriptions-item label="Unit/Set"  >
                        <span v-if="type_fw=='1'">{{form.pinBanNum}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.pinBanNum" />
                    </a-descriptions-item>
                    <a-descriptions-item label="单元长mm" >
                        <span  v-if="type_fw=='1'">{{form.pcsLendth}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.pcsLendth" />
                    </a-descriptions-item>
                    <a-descriptions-item  label="成品长mm"  >
                        <span  v-if="type_fw=='1'"> {{form.boardHeight}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.boardHeight" />
                    </a-descriptions-item >
                    <a-descriptions-item label="Y/Set数"  >
                        <span  v-if="type_fw=='1'">{{form.ySet}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.ySet" />
                    </a-descriptions-item>
                    <a-descriptions-item label="拼板单位"   >
                        <span v-if="type_fw=='1'">{{form.boardType}}</span>
                        <a-select v-else  v-model="form.boardType" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in BoardType" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item>
                    <a-descriptions-item label="单元宽mm"  >
                        <span  v-if="type_fw=='1'">{{form.pcsWidth}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.pcsWidth" />
                    </a-descriptions-item>

                    <a-descriptions-item label="测试方式"  >
                        <select v-if="type_fw=='1'" v-model="form.flyingProbe"   style="width: 200px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in FlyingProbe" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else v-model="form.flyingProbe" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in FlyingProbe" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item>
                    <a-descriptions-item label="测试架编号"  >
                        <span v-if="type_fw=='1'">{{form.testShareModel}}</span>
                        <a-input v-else v-model="form.testShareModel" />
                    </a-descriptions-item>
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="2" tab="拼版设计" >
                <a-descriptions bordered  :column="column" >
                    <a-descriptions-item  label="A PNL长(mm)" >
                        <span v-if="type_fw=='1'">{{form.apnlLength}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.apnlLength"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="A PNL宽(mm)" >
                        <span v-if="type_fw=='1'">{{form.apnlWidth}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.apnlWidth"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="A PNL SET数" >
                        <span v-if="type_fw=='1'">{{form.apnlsetNum}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.apnlsetNum"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="A PNL 单元数" >
                        <span v-if="type_fw=='1'">{{form.apnlpcsNum}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.apnlpcsNum"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="B PNL长(mm)" >
                        <span v-if="type_fw=='1'">{{form.bpnlLength}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.bpnlLength"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="B PNL宽(mm)" >
                        <span v-if="type_fw=='1'">{{form.bpnlWidth}}</span>
                        <a-input type="number" @keydown="valueChange" v-else v-model="form.bpnlWidth"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="B PNL 单元数" >
                        <span v-if="type_fw=='1'">{{form.bpnlpcsNum}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.bpnlpcsNum"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="B PNL SET数" >
                        <span v-if="type_fw=='1'">{{form.bpnlsetNum}}</span>
                        <a-input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '')"  v-else v-model="form.bpnlsetNum"></a-input>
                    </a-descriptions-item >
                </a-descriptions>
                <img :src="form.pnlAPath" alt="">
                <img :src="form.pnlBPath" alt="">
            </a-tab-pane>
            <a-tab-pane key="3" tab="钻孔参数" >
                <a-descriptions bordered :column="column" >
                    <a-descriptions-item  label="成型方式" >
                        <select v-if="type_fw=='1'" v-model="form.formingType"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in FormingType" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.formingType" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in FormingType" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="外形公差" >
                        <select v-if="type_fw=='1'" v-model="form.shapeTol"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in ShapeTol" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.shapeTol" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in ShapeTol" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="内槽公差" >
                        <select v-if="type_fw=='1'" v-model="form.slotTol"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in SlotTol" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.slotTol" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in SlotTol" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="V割角度" >
                        <select v-if="type_fw=='1'" v-model="form.vcutAngle"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in VcutAngle" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.vcutAngle" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in VcutAngle" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="V割余厚" >
                        <select v-if="type_fw=='1'" v-model="form.vcutRemainThk_"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in VcutRemainThk_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.vcutRemainThk_" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in VcutRemainThk_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="余厚公差" >
                        <select v-if="type_fw=='1'" v-model="form.vcutRemainThkPlusTol_"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in VcutRemainThkPlusTol_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.vcutRemainThkPlusTol_" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in VcutRemainThkPlusTol_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="大板V割" >
                        <span v-if="type_fw=='1'&&form.vCutToWX_">是</span>
                        <span v-if="type_fw=='1'&&!form.vCutToWX_">否</span>
                        <a-checkbox v-if="type_fw!=1" v-model="form.vCutToWX_"></a-checkbox>
                    </a-descriptions-item >
                    <a-descriptions-item  label="孔壁粗糙度" >
                        <select v-if="type_fw=='1'" v-model="form.holeRough"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in HoleRough" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.holeRough" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in HoleRough" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="最小钻孔" >
                        <span v-if="type_fw=='1'">{{form.vias}}</span>
                        <a-input v-else v-model="form.vias" />
                    </a-descriptions-item >
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="4" tab="线路参数" >
                <a-descriptions bordered :column="column" >
                    <a-descriptions-item  label="外层线路" >
                        <select v-if="type_fw=='1'" v-model="form.lineTechnology"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in LineTechnology" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.lineTechnology" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in LineTechnology" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >

                    <a-descriptions-item  label="内层铜厚oz" >
                        <span v-if="type_fw=='1'">{{form.innerCopperThickness}}</span>
                        <a-select v-else v-model="form.innerCopperThickness" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in InnerCopperThickness" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >

                    <a-descriptions-item  label="外层铜厚oz" >
                        <span v-if="type_fw=='1'">{{form.copperThickness}}</span>
                        <a-select  v-else v-model="form.copperThickness" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in CopperThickness" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >

                    <a-descriptions-item  label="孔铜" >
                        <span v-if="type_fw=='1'">{{form.holeCU}}</span>
                        <a-select  v-else v-model="form.holeCU" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in HoleCU" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >

                    <a-descriptions-item  label="线宽线距" >
                        <span v-if="type_fw=='1'">{{form.lineWeight}}</span>
                        <a-input v-else v-model="form.lineWeight" />
                    </a-descriptions-item >

                    <a-descriptions-item  label="补偿系数" >
                        <span v-if="type_fw=='1'">{{form.lineCompensate}}</span>
                        <a-input v-else v-model="form.lineCompensate" />
                    </a-descriptions-item >

                    <a-descriptions-item  label="线路公差" >
                        <span v-if="type_fw=='1'">{{form.lineTol}}</span>
                        <a-select v-else  v-model="form.lineTol" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in LineTol" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >

                    <a-descriptions-item  label="干膜尺寸" >
                        <span v-if="type_fw=='1'">{{form.dryFilmSize}}</span>
                        <a-select v-else  v-model="form.dryFilmSize" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in DryFilmSize" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="表面处理" >
                        <select v-if="type_fw=='1'" v-model="form.surfaceFinish"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in SurfaceFinish" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.surfaceFinish" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in SurfaceFinish" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="5" tab="阻焊字符">
                <a-descriptions bordered :column="column" >
                    <a-descriptions-item  label="阻焊颜色(顶)" >
                        <select v-if="type_fw=='1'" v-model="form.solderColor"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in SolderColor" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.solderColor" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in SolderColor" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="阻焊颜色(底)" >
                        <select v-if="type_fw=='1'" v-model="form.solderColorBottom"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in SolderColorBottom" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.solderColorBottom" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in SolderColorBottom" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="防焊厚度" >
                        <select v-if="type_fw=='1'" v-model="form.maskOilThk_"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in MaskOilThk_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.maskOilThk_" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in MaskOilThk_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="阻焊油墨型号" >
                        <span v-if="type_fw=='1'">{{form.solderFirm}}</span>
                        <a-input v-else v-model="form.solderFirm" />
                    </a-descriptions-item >
                    <a-descriptions-item  label="最小阻焊桥mm" >
                        <span v-if="type_fw=='1'">{{form.minSMBridge}}</span>
                        <a-input v-else v-model="form.minSMBridge" />
                    </a-descriptions-item >
                    <a-descriptions-item  label="过孔处理方式" >
                        <select v-if="type_fw=='1'" v-model="form.solderCover"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in SolderCover" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.solderCover" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in SolderCover" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="塞孔工艺" >
                        <select v-if="type_fw=='1'" v-model="form.plugOilTool_"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in PlugOilTool_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.plugOilTool_" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in PlugOilTool_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="字符颜色(顶)" >
                        <select v-if="type_fw=='1'" v-model="form.fontColor"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in FontColor" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.fontColor" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in FontColor" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="字符颜色(底)" >
                        <select v-if="type_fw=='1'" v-model="form.fontColorBottom"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in FontColorBottom" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.fontColorBottom" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in FontColorBottom" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="字符油墨型号" >
                        <span v-if="type_fw=='1'">{{form.fontFirm}}</span>
                        <a-input v-else v-model="form.fontFirm"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="周期层" >
                        <span v-if="type_fw=='1'">{{form.cycleLayer}}</span>
                        <a-select v-else  v-model="form.cycleLayer" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in CycleLayer" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="周期格式" >
                        <select v-if="type_fw=='1'" v-model="form.cycleMarker"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in CycleMarker" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else v-model="form.cycleMarker" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in CycleMarker" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >

                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="6" tab="检验出货" >
                <a-descriptions bordered :column="column" >
                    <a-descriptions-item  label="包装要求" >
                        <span v-if="type_fw=='1'">{{form.packaging}}</span>
                        <a-input v-else v-model="form.packaging"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="接受打X数量" >
                        <span v-if="type_fw=='1'">{{form.badBoardRequire}}</span>
                        <a-input v-else v-model="form.badBoardRequire"></a-input>
                    </a-descriptions-item >
                    <a-descriptions-item  label="包装数量" >
                        <select v-if="type_fw=='1'" v-model="form.packingSu_"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in PackingSu_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select  v-else v-model="form.packingSu_" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in PackingSu_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="翘曲度" >
                        <select v-if="type_fw=='1'" v-model="form.warpage_"   style="width: 150px; -webkit-appearance: none; border: none"      disabled >
                            <option  v-for="(item,index) in Warpage_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </option>
                        </select>
                        <a-select v-else  v-model="form.warpage_" style="width: 100%;">
                            <a-select-option  v-for="(item,index) in Warpage_" :key="index"  :value="item.valueMember">
                                {{item.text}}
                            </a-select-option>
                        </a-select>
                    </a-descriptions-item >
                    <a-descriptions-item  label="压板翘" >
                        <span v-if="type_fw=='1'&&form.isPressureplate_">是</span>
                        <span v-if="type_fw=='1'&&!form.isPressureplate_">否</span>
                        <a-checkbox  v-if="type_fw!=1"   v-model="form.isPressureplate_"></a-checkbox>
                    </a-descriptions-item >
                    <a-descriptions-item  label="离子污染度测试" >
                        <span v-if="type_fw=='1'&&form.isTest4Plasma_">是</span>
                        <span v-if="type_fw=='1'&&!form.isTest4Plasma_">否</span>
                        <a-checkbox  v-if="type_fw!=1"  v-model="form.isTest4Plasma_"></a-checkbox>
                    </a-descriptions-item >
                    <a-descriptions-item  label="出货报告" >
                        <span v-if="type_fw=='1'&&form.isReport4Shipment_">是</span>
                        <span v-if="type_fw=='1'&&!form.isReport4Shipment_">否</span>
                        <a-checkbox  v-if="type_fw!=1"  v-model="form.isReport4Shipment_"></a-checkbox>
                    </a-descriptions-item >
                    <a-descriptions-item  label="添加湿度卡" >
                        <span v-if="type_fw=='1'&&form.isHumidityCard_">是</span>
                        <span v-if="type_fw=='1'&&!form.isHumidityCard_">否</span>
                        <a-checkbox  v-if="type_fw!=1"   v-model="form.isHumidityCard_"></a-checkbox>
                    </a-descriptions-item >
                </a-descriptions>
            </a-tab-pane>
        </a-tabs>
    </a-card>
</template>

<script>
    import {getProject,amandProject} from '@/services/project'
    import {seleKey}from "@/services/synergy/sendOrders"
    export default {
        name: "",
        inject:['reload'],
        data() {
            return {
                column:4,
                type:this.$route.query.type,
                labelCol: { span: 4 },
                wrapperCol: { span: 14 },
                key:'1',
                form: {
                    boardLayers:'',
                    boardThickness:'',
                    boardHeight:'',
                    boardThkTolerate:'',
                    boardWidth:'',
                    xSet:'',
                    pinBanNum:'',
                    pcsLendth:'',
                    ySet:'',
                    boardType:'',
                    pcsWidth:'',
                    ipc:'',
                    flyingProbe:'',
                    testShareModel:'',
                },
                id:this.$route.query.id,
                BoardThkTolerate:[],//板厚公差mm:
                BoardType:[],//拼板单位:
                IPC:[],//检验标准:
                FlyingProbe:[],//测试方式
                FormingType:[],//成型方式
                ShapeTol:[],//外形公差
                SlotTol:[],//内槽公差
                VcutAngle:[],//V割角度
                VcutRemainThk_:[],//V割余厚
                VcutRemainThkPlusTol_:[],//余厚公差
                HoleRough:[],//孔壁粗糙度
                LineTechnology:[],//外层线路
                InnerCopperThickness:[],//内层铜厚oz
                CopperThickness:[],//外层铜厚oz
                HoleCU:[],//孔铜
                LineTol:[],//线路公差
                DryFilmSize:[],//干膜尺寸
                SurfaceFinish:[],//表面处理
                SolderColor:[],//阻焊颜色(顶)
                SolderColorBottom:[],//阻焊颜色(底)
                MaskOilThk_:[],//防焊厚度
                SolderCover:[],//过孔处理方式
                PlugOilTool_:[],//塞孔工艺
                FontColor:[],//字符颜色(顶)
                FontColorBottom:[],//字符颜色(底)
                CycleLayer:[],//周期层
                CycleMarker:[],//周期格式
                PackingSu_:[],//包装数量
                Warpage_:[],//翘曲度
                type_fw:1,
            }
        },
        component: {},
        created(){
          this.getform()
            seleKey('b8197bc4-9ebe-4662-8563-3fae738e967e,97fcb3f6-1943-4a64-9ba4-1fbc0ed392dd,9b6cf8a6-1816-e511-9063-78acc03d06a8,acf59dea-0f1a-e511-9063-78acc03d06a8,0652a3e8-8918-e511-9063-78acc03d06a8,c80e27ce-6f2c-4ede-b3c6-0f3cae7e1840').then(res=>{
              console.log(res)
                res.forEach(item=>{
                    if(item.uda=='BoardType'){
                        this.BoardType=item.items
                    }
                    if(item.uda=='BoardThkTolerate'){
                        this.BoardThkTolerate=item.items
                    }
                    if(item.uda=='IPC'){
                        this.IPC=item.items
                    }
                    if(item.uda=='FlyingProbe'){
                        this.FlyingProbe=item.items
                    }
                    if(item.uda=='FormingType'){
                        this.FormingType=item.items
                    }
                    if(item.uda=='ShapeTol'){
                        this.ShapeTol=item.items
                    }
                    if(item.uda=='SlotTol'){
                        this.SlotTol=item.items
                    }
                    if(item.uda=='VcutAngle'){
                        this.VcutAngle=item.items
                    }
                    if(item.uda=='VcutRemainThk_'){
                        this.VcutRemainThk_=item.items
                    }
                    if(item.uda=='VcutRemainThkPlusTol_'){
                        this.VcutRemainThkPlusTol_=item.items
                    }
                    if(item.uda=='HoleRough'){
                        this.HoleRough=item.items
                    }
                    if(item.uda=='LineTechnology'){
                        this.LineTechnology=item.items
                    }
                    if(item.uda=='InnerCopperThickness'){
                        this.InnerCopperThickness=item.items
                    }
                    if(item.uda=='CopperThickness'){
                        this.CopperThickness=item.items
                    }
                    if(item.uda=='HoleCU'){
                        this.HoleCU=item.items
                    }
                    if(item.uda=='LineTol'){
                        this.LineTol=item.items
                    }
                    if(item.uda=='DryFilmSize'){
                        this.DryFilmSize=item.items
                    }
                    if(item.uda=='SurfaceFinish'){
                        this.SurfaceFinish=item.items
                    }
                    if(item.uda=='SolderColor'){
                        this.SolderColor=item.items
                    }
                    if(item.uda=='SolderColorBottom'){
                        this.SolderColorBottom=item.items
                    }
                    if(item.uda=='MaskOilThk_'){
                        this.MaskOilThk_=item.items
                    }
                    if(item.uda=='SolderCover'){
                        this.SolderCover=item.items
                    }
                    if(item.uda=='PlugOilTool_'){
                        this.PlugOilTool_=item.items
                    }
                    if(item.uda=='FontColor'){
                        this.FontColor=item.items
                    }
                    if(item.uda=='FontColorBottom'){
                        this.FontColorBottom=item.items
                    }
                    if(item.uda=='CycleLayer'){
                        this.CycleLayer=item.items
                    }
                    if(item.uda=='CycleMarker'){
                        this.CycleMarker=item.items
                    }
                    if(item.uda=='PackingSu_'){
                        this.PackingSu_=item.items
                    }
                    if(item.uda=='Warpage_'){
                        this.Warpage_=item.items
                    }
                    if(item.uda=='CycleLayer'){
                        this.CycleLayer=item.items
                    }
                    if(item.uda=='CycleLayer'){
                        this.CycleLayer=item.items
                    }
                    if(item.uda=='CycleLayer'){
                        this.CycleLayer=item.items
                    }
                })
            })
        },
        methods: {
            valueChange(e){
                // 通过正则过滤小数点后两位
                e.target.value = (e.target.value.match(/^\d*(\.?\d{0,2})/g)[0]) || null
            },
            getform(){
                getProject(this.id).then(res=>{
                    console.log(res)
                    this.form=res
                })
            },
            callback(key){
                this.key=key
            },
            project_fw(val){
                if(val==1){
                    this.type_fw=2
                }else {
                    this.type_fw=1
                }
            },
            btn_save(){
                amandProject(this.form).then(res=>{
                    this.$message.info(res.message)
                    this.getform()
                    this.type_fw=1
                })
            },
        },
    }
</script>

<style scoped lang="less">
    /deep/.ant-menu-submenu-title{
        padding: 0!important;
    }
    /deep/.ant-menu-submenu-arrow{
        right: 0!important;
    }
    /deep/.ant-menu-item{
        padding-left: 10px!important;
    }
    /deep/.ant-card-body{
        padding: 10px;
    }
    .formDiv{
        display: flex;
        align-items: center;
        width: 16%;
    }
    /deep/.ant-col{
        width: 100px;
    }
    /deep/ .ant-descriptions-row  .ant-descriptions-item-content{
        width: 200px!important;
    }
</style>
