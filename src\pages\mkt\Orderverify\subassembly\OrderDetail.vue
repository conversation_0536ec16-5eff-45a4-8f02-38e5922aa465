<template>
<a-table
    :columns="columns"
    :dataSource="dataSource"
    :scroll="{y: 687}"
    :pagination="false"
    :rowKey="rowKey"
    bordered
    :customRow="onClickRow"
    :rowClassName="isRedRow"
    :loading="orderDetailTableLoading">
    <template slot="action" slot-scope="text,record" >
      <a-tooltip title="取单设置" v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendMIIsLeave')">
        <a-icon type="edit" style="color: #ff9900; font-size: 18px;" @click.stop="OrderRetrievalSettingsClick(record)"/>
      </a-tooltip>
    </template>
</a-table>
</template>

<script>
import {checkPermission} from "@/utils/abp";
export default {
  name: "OrderDetail",
  props: {
    columns:{
      type: Array,
      require: true
    },
    dataSource:{
      type: Array,
      require: true
    },
    orderDetailTableLoading:{
      type: <PERSON><PERSON><PERSON>,
      require: true
    },
    rowKey:{
      type: String,
      require: true
    }
  },
  data(){
    return {
      erpRKEY_:'',
    }
  },
  methods:{
    checkPermission,
    onClickRow(record) {
      return {
        on: {
          click: () => {
            if (record.erpRKEY_ && this.erpRKEY_ != record.erpRKEY_){
              this.$emit('getPeopleOrderList', record.erpRKEY_)
            }
            this.$emit('saveErpKey', record)
            this.erpRKEY_ = record.erpRKEY_
            
          }
        }
      }
    },
    isRedRow (record) {      
      let strGroup = []
      let str =[]
      if (record.erpRKEY_ && record.erpRKEY_ == this.erpRKEY_) {
        // return 'rowBackgroundColor'
        strGroup.push('rowBackgroundColor')
      } 
      if(record.isLeave_ == true){ // 是否请假
        str.push('rowSty')
        // return 'rowSty' 
      }  
      if(record.isBigCus == true){ // 是否大客
        str.push('rowSty1')
      }
      if(str.length > 1){
        str = [str[0]]
      }   
      return str.concat(strGroup)     
      
    },
    OrderRetrievalSettingsClick(record){
      this.$emit('OrderRetrievalSettingsClick',record)
    }
  },
}
</script>

<style scoped lang="less">
///deep/ .ant-table-body {
//  overflow-y: auto!important;
//}

/deep/ .ant-table {
  .rowSty {
    td {
      color: #DC143C;
    }
  }
  .rowSty1{
    td{
      color: #2828FF;
    }
  }
  .rowBackgroundColor {
      background: #aba5a5;
    }
}


</style>