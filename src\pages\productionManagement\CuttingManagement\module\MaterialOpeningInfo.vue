<!-- 车间管理-开料管理 -开料信息 -->
<template>
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="大料张数">
      <a-input   v-model='selectedRows1[0]' :autoFocus="autoFocus" @keyup.enter="keyupEnter1"/>
    </a-form-item>
  </a-form>
</template>

<script>
export default {
    name:'MaterialOpeningInfo',
    props:['selectedRows1'],
  data() {
    return {    
      OrderNumber:'',  
      autoFocus:true
    };
  },
  methods: {  
    
   keyupEnter1(){
      this.$emit('keyupEnter1')
  }
  },
};
</script>