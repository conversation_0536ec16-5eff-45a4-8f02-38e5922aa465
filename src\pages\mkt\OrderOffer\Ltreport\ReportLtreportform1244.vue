<!--龙腾报价表单  -->
<template>
  <div class="pdfDom1" style="font-size: 13px">
    <a-button
      v-print="printObj1"
      @click="printpdf"
      type="primary"
      class="printstyle"
      >打印</a-button
    >
    <div
      id="ltreport1244"
      style="
        padding: 25px;
        font-family: 等线;
        color: black;
        font-weight: 600;
        position: relative;
      "
    >
      <div style="width: 100%; display: flex">
        <div style="text-align: center; width: 100%">
          <img src="@/assets/img/lt.png" style="height: 70px" />
          <div>
            <span
              style="font-size: 32px; font-weight: bold; letter-spacing: 5px"
              >报价单</span
            >
          </div>
        </div>
      </div>
      <div style="display: flex; line-height: 3ch">
        <div style="width: 50%; z-index: 99; font-size: 15px">
          <div>TO:{{ LTreportdata.value_1 }}</div>
          <div>联络人/ATTN:{{ LTreportdata.value_2 }}</div>
          <div>电话/TEL:{{ LTreportdata.value_3 }}</div>
          <div>传真/FAX:{{ LTreportdata.value_4 }}</div>
        </div>
        <div style="z-index: 99; width: 50%">
          <div style="float: right; font-size: 15px">
            <div>FROM:{{ LTreportdata.value_5 }}</div>
            <div>联系人:{{ LTreportdata.value_6 }}</div>
            <div>交易币别:{{ LTreportdata.value_10 }}</div>
            <div>付款方式:{{ LTreportdata.value_7 }}</div>
          </div>
        </div>
      </div>
      <div>
        <table
          border="1"
          style="
            text-align: center;
            margin-top: 5px;
            width: 100%;
            border-top: 1px solid black;
            border-left: 1px solid black;
          "
        >
          <thead>
            <tr style="font-size: 15px">
              <td>No</td>
              <td>物料编号<br />Customer code</td>
              <td>PCB型号<br />Models</td>
              <td>板材</td>
              <td>X(mm)</td>
              <td>Y(mm)</td>
              <td>出数</td>
              <td>单只尺寸</td>
              <td>层数</td>
              <td>板厚</td>
              <td>铜厚</td>
              <td>表面处理</td>
              <td>阻焊字符颜色</td>
              <td>数量/单位Pcs</td>
              <!-- <td>模冲 未税单价(元)</td> -->
              <td>CNC 未税单价(元)</td>
              <td colspan="2" style="background-color: #c6e0b4">
                每平方米未税报价价格(材料+加工)(元)
              </td>
              <td>拼板利用率</td>
              <td>交货期 Lead time</td>
              <td>模具费(元)</td>
              <td>增值税</td>
              <td>备注(未税)</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in LTreportdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.price1 }}</td>
              <td>{{ item.price2 }}</td>
              <td>{{ item.price3 }}</td>
              <td>{{ item.price4 }}</td>
              <td>{{ item.price5 }}</td>
              <td>{{ item.price6 }}</td>
              <td>{{ item.price7 }}</td>
              <td>{{ item.price8 }}</td>
              <td>{{ item.price9 }}</td>
              <td>{{ item.price10 }}</td>
              <td>{{ item.price11 }}</td>
              <td>{{ item.price12 }}</td>
              <td>{{ item.price13 }}</td>
              <!-- <td>{{ item.price14 }}</td> -->
              <td>{{ item.price15 }}</td>
              <td>{{ item.price16 }}</td>
              <td>{{ item.price17 }}</td>
              <td>{{ item.price18 }}</td>
              <td>{{ item.price19 }}</td>
              <td>{{ item.price20 }}</td>
              <td>{{ item.tax }}</td>
              <td>{{ item.notes }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="z-index: 99; position: relative">
        <div
          style="
            display: flex;
            line-height: 2.8ch;
            margin-top: 8px;
            font-size: 15px;
          "
        >
          <div>
            <div>备注: 1.以上报价从即日开始生效</div>
            <div>
              &emsp;&emsp; &nbsp;2.订单下达后当天可取消订单/Cancellation
              windows(/)
            </div>
            <div>
              &emsp;&emsp; &nbsp;3.订单下达后次日可调整交期/Reschedule window(/)
            </div>
            <div style="color: red">
              &emsp;&emsp;
              &nbsp;4.PCB板单价计价公式:=长x宽/拼板数x每平方米未税/1000000
            </div>
          </div>
        </div>
      </div>
      <div
        style="
          display: flex;
          width: 100%;
          padding-top: 15px;
          font-size: 15px;
          z-index: 99;
          position: relative;
          justify-content: space-between;
        "
      >
        <div>
          <div>客户确认(盖章):</div>
          <div>日期:</div>
        </div>
        <div>
          <div>核准:董艳丽</div>
          <div>日期:{{ date }}</div>
        </div>
        <div style="min-width: 150px">
          <div>报价人:{{ LTreportdata.value_8 }}</div>
          <div>日期:{{ LTreportdata.value_9 }}</div>
        </div>
      </div>
      <div style="height: 40px"></div>
      <img
        src="@/assets/img/lthtz.png"
        style="
          position: absolute;
          bottom: 5px;
          z-index: 0;
          display: block;
          left: 49%;
          width: 150px;
          transform: rotate(353deg);
        "
      />
    </div>
  </div>
</template>
<script>
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
import moment from "moment";
export default {
  name: "",
  props: ["LTreportdata", "ttype"],
  computed: {},
  data() {
    return {
      date:moment().format('YYYY-MM-DD'),
      amountto: 0,
      printObj1: {
        id: "ltreport1244", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.LTreportdata.price.length; index++) {
      if (
        this.LTreportdata.price[index].total &&
        this.LTreportdata.price[index].total != "/"
      ) {
        this.amountto += Number(this.LTreportdata.price[index].total);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.LTreportdata.pcbFileName;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getreportPdf() {
      htmlToPdfa3("ltreport1244", this.LTreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.Underline {
  position: relative;
  display: inline-block;
}
.Underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 200px; /* 下划线宽度 */
  height: 2px; /* 下划线高度 */
  background-color: rgb(107, 106, 106); /* 下划线颜色 */
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
  table > thead > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
  table > tbody > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
}
</style>
