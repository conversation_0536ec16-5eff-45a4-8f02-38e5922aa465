import { request, METHOD } from '@/utils/request'

//获取数据
export async function slicereportlist(OrderNo) {
    return request(`/api/app/slice-report/slice-report-list?OrderNo=${OrderNo}`, METHOD.GET,)
}
//保存数据
export async function setslicereportlist(params) {
    return request(`/api/app/slice-report/set-slice-report-list`, METHOD.POST,params)
}
//切片报告
export async function reliabilitytestreport(orderno) {
    return request(`/api/app/slice-report/reliability-test-report?orderno=${orderno}`, METHOD.GET,)
}
