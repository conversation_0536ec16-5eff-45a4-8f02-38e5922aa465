<!-- 市场管理 - 价格体系 - 中间列表 -->
<template>
  <div>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ y: 720, x: 300 }"
      :customRow="onClickRow"
      :pagination="false"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      :rowClassName="isRedRow"
    >
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { setEngineeringBack } from "@/utils/request";
import RightCopy from "@/pages/RightCopy";
import { proQuestLog } from "@/services/projectApi";
export default {
  components: {
    RightCopy,
  },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    rowKey: {
      type: String,
      require: true,
    },
  },
  name: "LeftTable",
  data() {
    return {
      selectedRowKeysArray: [],
      selectedRowsData: [],
      activeClass: "smallActive",
      proOrderId: "",

      menuData: {},
    };
  },
  watch: {
    pagination: {
      handler(val) {
        // console.log(val)
      },
    },
  },
  created() {},
  methods: {
    checkPermission,
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.proOrderId == this.cookieId) {
        strGroup.push("cookieIdColor");
      }
      if (record.proOrderId && record.proOrderId == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },

    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.proOrderId);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.proOrderId = record.proOrderId;
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
  },
  mounted() {
    // this.getcookie('ordernoBack')
  },
};
</script>

<style lang="less" scoped>
.tabRightClikBox {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
}

/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }

  .ant-table-fixed-left {
    .cookieIdColor {
      td:first-child {
        //border-left:2px solid #ff9900!important;
        background: #ff9900 !important;
      }
    }

    //td:nth-child(2){
    //  color:#ff9900!important;
    //}
  }

  .rowBackgroundColor {
    background: #dcdcdc;
  }

  .displayFlag {
    display: none;
  }
}

.peopleTag {
  margin: 0;
  padding: 0;
  width: 24px;
  border-radius: 12px;
  background: #2d221d;
  border-color: #2d221d;
  color: #ff9900;
  text-align: center;
  margin-left: 2px;
}
</style>
