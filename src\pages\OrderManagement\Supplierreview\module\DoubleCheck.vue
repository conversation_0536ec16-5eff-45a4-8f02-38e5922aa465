<!--生产管理客诉复核-->
<template>
    <div class="main">
        <a-table
            :columns="columns"
            :dataSource="checkdata"
            :pagination="pagination"
            @change="handleTableChange" 
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            :scroll="{ x: 1650,y:640 }"
            :rowKey="(record,index) => index"
            bordered
            :class="checkdata.length?'mintable':''"
        >
        <template slot="operation" slot-scope="text, record, index">
            <div v-show="activeKey=='40' || activeKey=='60'">
                <a-button @click="Detail(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">复核</a-button>
                <a-divider type="vertical" style="height: 15px; background-color: #ff9900" />
                <a-button  type="link" size="small" style="padding: 0px;font-size: 12px" @click="sendclick(record)">发送</a-button>
            </div>   
            <div v-show="activeKey=='100'">
                <a-button @click="allDetail(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">详情</a-button>
            </div>        
        </template>
    </a-table>    
    </div>
</template>
<script>

export default {
    props:['checkdata','activeKey','pagination','columns'],
    name:'FactoryTable',
    data(){
        return{
            selectdata:{},
            proOrderId:'',
        }
    },
    created(){
    },
    mounted(){

    },
    methods:{
        allDetail(record){
            this.$router.push({path:'/QualityManagement/Platformevaluation',query:{id:record.id,type:'scfh',dis:1,statusbar:record.status,activeKey:this.activeKey} })
        },
        onClickRow(record){
                return {
                    on: {
                        click: () => {
                            this.selectdata = record;
                            this.proOrderId=record.id
                        }
                    }
                }
            },
            isRedRow(record){
                let strGroup = []
                let str =[]
                if(record.id && record.id == this.proOrderId) {
                    strGroup.push('rowBackgroundColor')
                }
                return str.concat(strGroup)
            },
        sendclick(record){
            this.$emit('sendclick',record)
        },
        Detail(record){
            this.$router.push({path:'Reviewdetails',query:{id:record.id,type:'csfh'} })
        },
        handleTableChange(pagination,filter,sorter){
            this.$emit('tableChange', pagination,filter,sorter)
    },
    }
}

</script>
<style lang="less" scoped>
.mintable{
    /deep/.ant-table-body{
        min-height: 640px;
    }
}
/deep/.ant-pagination-prev {
    margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input{
  padding: 0;
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager{
  margin: 0;
}
/deep/.ant-pagination-slash {
    margin: 0;
}
/deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 8px 0 0 10px;
    position: fixed;
  }
.main{
    background: white;
    height: 679px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
        background: #F8F8F8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
background: #dfdcdc;
}
/deep/ .ant-table{
        .ant-table-tbody{
            tr.ant-table-row-hover td {
             background: #dfdcdc;
            }
        }
        .rowBackgroundColor {
                background: #c9c9c9!important;
            }       
        .ant-table-thead > tr > th{
            padding: 4px 4px;
        }
        .ant-table-tbody > tr > td {
            padding: 4px 4px!important;
            max-width: 100px;
            color: #000000;
        }
        tr.ant-table-row-selected td {
        background: #dfdcdc;
        }
        tr.ant-table-row-hover td {
        background: #dfdcdc;
        }
}
</style>