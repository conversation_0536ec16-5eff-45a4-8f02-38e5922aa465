import { request, METHOD } from "@/utils/request";

export async function getCamData() {
  return request("/api/app/analysis/cam-day-data-statistics", METHOD.GET);
}
export async function vueversion() {
  return request("/api/app/e-mSTSys-app-config/vue-version", METHOD.GET);
}
export async function getPanelData() {
  return request("/api/app/analysis/pin-ban-day-data-statistics", METHOD.GET);
}

export async function getGropOrderList() {
  return request("/api/app/analysis/cam-croup-day-data-statistics", METHOD.GET);
}

export async function newGetCamData() {
  return request("/api/app/analysis/cam-day-data-statistics-v2", METHOD.GET);
}
// mkt分析页
export async function orderDataStatistics() {
  return request("/api/app/analysis/order-data-statistics-v1", METHOD.GET);
}
//市场分析页下单数据 月
export async function mkTOrderdatastatistics(date, FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder-data-statistics?date=${date}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页下单数据 周
export async function mkTOrderdatastatisticsv1(date, FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder-data-statistics-v1?date=${date}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页下单数据 日
export async function mkTOrderdatastatisticsv2(date, FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder-data-statistics-v2?date=${date}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页下单数据 时间区间选择
export async function mkTOrderdatastatisticsv3(Startdate, Enddate, FactoryId, OrderSource) {
  return request(
    `/api/app/analysis/m-kTOrder-data-statistics-v3?Startdate=${Startdate}&Enddate=${Enddate}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`,
    METHOD.GET
  );
}
//市场分析页品类分布数据 月
export async function mkTOrder4Xdstatistics(date, FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder4Xd-statistics?date=${date}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页品类分布数据 周
export async function mkTOrder4Xdstatisticsv1(date, FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder4Xd-statistics-v1?date=${date}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页品类分布数据 日
export async function mkTOrder4Xdstatisticsv2(date, FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder4Xd-statistics-v2?date=${date}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页品类分布数据 时间区间选择
export async function mkTOrder4Xdstatisticsv3(Startdate, Enddate, FactoryId, OrderSource) {
  return request(
    `/api/app/analysis/m-kTOrder4Xd-statistics-v3?Startdate=${Startdate}&Enddate=${Enddate}&FactoryId=${FactoryId}&OrderSource=${OrderSource}`,
    METHOD.GET
  );
}
//工程分析页数据 日
export async function ppEOrderdatastatisticsv2(date, FactoryId) {
  return request(`/api/app/analysis/p-pEOrder-data-statistics-v2?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//工程分析页数据 周
export async function ppEOrderdatastatisticsv1(date, FactoryId) {
  return request(`/api/app/analysis/p-pEOrder-data-statistics-v1?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//工程分析页数据 月
export async function ppEOrderdatastatistics(date, FactoryId, type, Enddate) {
  return request(`/api/app/analysis/p-pEOrder-data-statistics?date=${date}&FactoryId=${FactoryId}&type=${type}&Enddate=${Enddate}`, METHOD.GET);
}
//工程分析页数据 时间区间选择
export async function ppEOrderdatastatisticsv3(Startdate, Enddate, FactoryId) {
  return request(`/api/app/analysis/p-pEOrder-data-statistics-v3?Startdate=${Startdate}&Enddate=${Enddate}&FactoryId=${FactoryId}`, METHOD.GET);
}
//年度下单数据
export async function ppEOrderyeardatastatistics(FactoryId) {
  return request(`/api/app/analysis/p-pEOrder-year-data-statistics?FactoryId=${FactoryId}`, METHOD.GET);
}
//工程一次问客率
export async function ppEOrdereQDatastatistics(FactoryId) {
  return request(`/api/app/analysis/p-pEOrder-eQData-statistics?FactoryId=${FactoryId}`, METHOD.GET);
}
//合拼数据分析 日 数据
export async function datastatisticsv1(date, FactoryId) {
  return request(`/api/app/analysis/combinate-order-data-statistics-v1?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//合拼数据分析 月 数据
export async function datastatisticsv2(date, FactoryId) {
  return request(`/api/app/analysis/combinate-order-data-statistics-v2?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//下单数据分析 日 数据
export async function mkt2Ppedatav1(date, FactoryId) {
  return request(`api/app/analysis/mkt2Ppe-order-data-statistics-v1?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//下单数据分析 月 数据
export async function mkt2Ppedatav2(date, FactoryId) {
  return request(`api/app/analysis/mkt2Ppe-order-data-statistics-v2?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//下线数据分析 日 数据
export async function ppeofflinedatav1(date, FactoryId) {
  return request(`/api/app/analysis/ppe-offline-data-statistics-v1?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//下线数据分析 月 数据
export async function ppeofflinedatav2(date, FactoryId) {
  return request(`/api/app/analysis/ppe-offline-data-statistics-v2?date=${date}&FactoryId=${FactoryId}`, METHOD.GET);
}
//CAM人均工效
export async function camdatastatistics(FactoryId) {
  return request(`/api/app/analysis/p-pEOrder-cam-data-statistics?FactoryId=${FactoryId}`, METHOD.GET);
}
// 授权工厂下拉
export async function factroyList() {
  return request(`/api/app/cost-analysis/factroy-list`, METHOD.GET);
}
//市场分析页 商机达成率
export async function bodatastatistics(FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder-bo-data-statistics?FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页 淘金率
export async function modatastatistics(FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder-mo-data-statistics?FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//市场分析页 平均报价时长
export async function timestatistics(FactoryId, OrderSource) {
  return request(`/api/app/analysis/m-kTOrder-average-time-statistics?FactoryId=${FactoryId}&OrderSource=${OrderSource}`, METHOD.GET);
}
//工程分析页获取工程下线率与每日产出  月度表
export async function ppEOrdermonthdatastatistics(FactoryId) {
  return request(`/api/app/analysis/p-pEOrder-month-data-statistics?FactoryId=${FactoryId}`, METHOD.GET);
}
