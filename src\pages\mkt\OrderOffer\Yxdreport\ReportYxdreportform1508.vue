<!--YXD报价单1508  -->
<template>
  <div class="pdfDom1" style="font-size: 14px">
    <a-button v-print="printObj" @click="printpdf" class="printstyle" type="primary">打印</a-button>
    <div id="yxdreport1" style="padding: 25px; font-family: '宋体'">
      <div style="width: 100%; display: flex; text-align: center">
        <img src="@/assets/img/yxdlogo.png" style="height: 80px" />
        <div style="width: 100%; position: relative; left: -64px">
          <span style="font-size: 30px; font-weight: bold">{{ YXDreportdata.factory_ }}</span
          ><br />
          <span style="font-size: 30px; font-weight: bold; letter-spacing: 5px">报价单</span>
        </div>
      </div>
      <div style="display: flex; justify-content: space-around">
        <div>单据日期:{{ YXDreportdata.factorydete_ }}</div>
        <div>报价:{{ YXDreportdata.orderNo_ }}</div>
      </div>
      <a-divider style="height: 1px; background-color: #717171; margin: 5px 0" />
      <div>
        【1】加工品名称、规格、数量及交期：
        <table border="1" style="text-align: center; margin-top: 5px; width: 100%">
          <thead>
            <td>序号</td>
            <td>文件名</td>
            <td>物料编码</td>
            <td>层数</td>
            <td>拼版尺寸(MM)</td>
            <td>板厚</td>
            <td>板材品牌</td>
            <td>TG值</td>
            <td>铜厚</td>
            <td>表面处理</td>
            <td>阻焊颜色</td>
            <td>字符颜色</td>
            <td>数量</td>
            <td>平米价</td>
            <td>单价(PCS)</td>
            <td>测试架(RMB)</td>
            <td>工程费(RMB)</td>
            <td>加急费(RMB)</td>
            <td>镭雕单价</td>
            <td>包含所有额外费用单价</td>
            <td>合计</td>
            <td>备注</td>
          </thead>
          <tbody v-for="(item, index) in YXDreportdata.price" :key="index">
            <td>{{ index + 1 }}</td>
            <!--文件名-->
            <td>{{ item.price1 }}</td>
            <!--物料编码-->
            <td>{{ item.price2 }}</td>
            <!--层数-->
            <td>{{ item.price3 }}</td>
            <!--拼版尺寸-->
            <td>{{ item.price4 }}</td>
            <!--板厚-->
            <td>{{ item.price5 }}</td>
            <!--板材品牌-->
            <td>{{ item.price6 }}</td>
            <!--TG值-->
            <td>{{ item.price7 }}</td>
            <!--铜厚-->
            <td>{{ item.price8 }}</td>
            <!--表面处理-->
            <td>{{ item.price9 }}</td>
            <!--阻焊颜色-->
            <td>{{ item.price10 }}</td>
            <!--字符颜色-->
            <td>{{ item.price11 }}</td>
            <!--数量-->
            <td>{{ item.price12 }}</td>
            <!--平米价-->
            <td>{{ item.price13 }}</td>
            <!--单价-->
            <td>{{ item.price14 }}</td>
            <!--测试架-->
            <td>{{ item.price15 }}</td>
            <!--工程费-->
            <td>{{ item.price16 }}</td>
            <!--加急费-->
            <td>{{ item.price17 }}</td>
            <!--镭雕单价-->
            <td>{{ item.price18 }}</td>
            <!--包含所有额外费用单价-->
            <td>{{ item.price19 }}</td>
            <!--合计-->
            <td>{{ item.price20 }}</td>
            <!--备注-->
            <td>{{ item.price21 }}</td>
          </tbody>
        </table>
      </div>
      <div style="position: relative; height: 210px">
        <div style="position: relative; z-index: 99">
          <div style="font-size: 26px; font-weight: bold; letter-spacing: 5px">备注：</div>
          <div>一.以上报价含税.</div>
          <div>三.如原材料价格浮动太大，根据原材料价格上调下跌，双方协商再议</div>
          <div style="display: flex">
            <div style="width: 50%">供货方： {{ YXDreportdata.factory_ }}</div>
            <div>订货方： {{ YXDreportdata.value_3 }}</div>
          </div>
          <div style="display: flex">
            <div style="width: 50%">电话: {{ YXDreportdata.value_1 }}</div>
            <div>电话：{{ YXDreportdata.value_4 }}</div>
          </div>
          <div style="display: flex">
            <div style="width: 50%">工厂地址：{{ YXDreportdata.value_2 }}</div>
            <div>地址： {{ YXDreportdata.value_5 }}</div>
          </div>
        </div>
        <img
          v-if="YXDreportdata.factory_.indexOf('惠州') != -1"
          src="@/assets/img/hzyxd.png"
          style="position: absolute; top: 50px; z-index: 0; display: block; left: 70px; width: 150px"
        />
        <img
          v-if="YXDreportdata.factory_.indexOf('江西') != -1"
          src="@/assets/img/jxyxd.png"
          style="position: absolute; top: 50px; z-index: 0; display: block; left: 70px; width: 150px"
        />
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["YXDreportdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj: {
        id: "yxdreport1", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.amountto = 0;
    // for (let index = 0; index < this.YXDreportdata.price.length; index++) {
    //     if(this.YXDreportdata.price[index].total && this.YXDreportdata.price[index].total!='/'){
    //         this.amountto +=Number(this.YXDreportdata.price[index].total)
    //     }
    // }
  },
  methods: {
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.YXDreportdata.pcbFileName;
    },
    getreportPdf() {
      htmlToPdfa3("yxdreport1", this.YXDreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
