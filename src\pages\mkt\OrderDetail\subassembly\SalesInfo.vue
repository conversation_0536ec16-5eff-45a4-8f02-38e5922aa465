<!-- 市场管理 - 订单报价- 销售信息 -->
<template>
  <div class="contentInfo">
    <a-card :bordered="false">
      <div ref="SelectBox">
        <a-form-model layout="inline" :model="formData" v-if="!editFlag">
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="客户型号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span class="tmp">{{ formData.customerModel }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="客户订单号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.custPo }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="合同号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.contractNumber }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="客户物料号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.customerMaterialNo }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="客户物料名称" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.customerMaterialName }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端客户型号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.endCustNo }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端物料号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.endCustMaterialNo }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端物料名称" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.endCustMaterialName }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="终端客户订单号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.endCustPo }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端客户名称" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.endCustName }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="测试方式" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.flyingProbeStr }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6" v-if="this.$route.query.factory != 59 && this.$route.query.factory != 58 && this.$route.query.factory != 67">
              <a-form-model-item label="加工工厂" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.orderDirection }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6" v-if="formData.flyingProbe == 'sharestand' && (formData.joinFactoryId == '58' || formData.joinFactoryId == '59')">
              <a-form-model-item label="测试方式内容" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.flyingProbeContent }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="交货单位" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" class="require">
                <div class="editWrapper">
                  <span>{{ formData.delType }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="市场交期" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.marketDeliveryTime }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="备品数(PCS)" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.sparePartNum }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="总测试点数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.delQtyAll }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="订单类型" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.jzOrderType }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="库存板处理" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.inventoryBoardProcess }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="产品属性" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.orderAttribute }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="价格类型" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.priceType }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="参考价格1" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.referencePrice1 }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="参考价格2" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.referencePrice2 }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="装运方式" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.shipmentTerm }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="地点" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.shipLocation }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="高附加值" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.isHighAddValue ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="上锡板" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.isUpperTin ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="已开测试架" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.openedTestrack ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="接单工厂" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.contractFactoryName }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="后补合同" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <span>{{ formData.behindContract ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="收货地址" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <span>{{ formData.shippingAddress }}</span>
                  <!-- <a-button type="primary" style="margin-left:4%;position:absolute;" @click="addClick" disabled>临时地址</a-button>   -->
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- <a-row>
            <a-col :span="6">
              <a-form-model-item label=" EQ联系人" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.eqContactPerson }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="EQ联系人电话" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.eqPhoneNumber }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="EQ联系人邮箱" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.eqEmail }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="加急" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <span>{{ formData.toJiaji ? '是' : '' }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row> -->
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="出货报告"
                prop="needReportList"
                :label-col="{ span: 3 }"
                :wrapper-col="{ span: 21 }"
                style="width: 100%; margin: 0"
              >
                <div class="editWrapper">
                  <span>{{ formData.needReportListStr }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="市场备注"
                :label-col="{ span: 3 }"
                :wrapper-col="{ span: 21 }"
                :class="formData.mktNote ? 'div22' : ''"
                class="div22"
              >
                <span style="width: 100%">{{ formData.mktNote }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="费用备注"
                :label-col="{ span: 3 }"
                :wrapper-col="{ span: 21 }"
                :class="formData.costNote ? 'div22' : ''"
                class="div22"
              >
                <span style="overflow-y: scroll; height: 53px; width: 100%; overflow-x: hidden">{{ formData.costNote }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="型号备注"
                :label-col="{ span: 3 }"
                :wrapper-col="{ span: 21 }"
                :class="formData.note ? 'div22' : ''"
                class="div22"
              >
                <span style="overflow-y: scroll; height: 53px; width: 100%; overflow-x: hidden">{{ formData.note }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <a-form-model layout="inline" ref="ruleForm1" :rules="rules1" :model="formData" v-else>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="客户型号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.customerModel"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="客户订单号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.custPo" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="合同号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.contractNumber" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="客户物料号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.customerMaterialNo" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="客户物料名称" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.customerMaterialName"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端客户型号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.endCustNo" allowClear> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端物料号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.endCustMaterialNo"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端物料名称" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.endCustMaterialName"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="终端客户订单号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.endCustPo"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="终端客户名称" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.endCustName"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="测试方式" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.flyingProbe"
                    showSearch
                    @change="changeFlyingProbe"
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.FlyingProbe)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6" v-if="this.$route.query.factory != 59 && this.$route.query.factory != 58 && this.$route.query.factory != 67">
              <a-form-model-item label="加工工厂" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <a-select
                  v-model="formData.orderDirection"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.OrderDirection)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6" v-if="showflyingProbe">
              <a-form-model-item label="测试方式内容" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.flyingProbeContent" allowClear> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="交货单位" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" class="require">
                <div class="editWrapper">
                  <a-select v-model="formData.delType" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                    <a-select-option v-for="(item, index) in mapKey(selectOption.BoardType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="市场交期" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-date-picker
                    style="margin-right: 0.5%; margin-left: 0.5%; width: 200px"
                    valueFormat="YYYY-MM-DD"
                    placeholder="市场交期"
                    v-model="formData.marketDeliveryTime"
                    @change="onChange1"
                  />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="备品数(PCS)" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.sparePartNum"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="总测试点数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.delQtyAll" allowClear disabled> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="订单类型" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.jzOrderType"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.JzOrderType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="库存板处理" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.inventoryBoardProcess"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.InventoryBoardProcess)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="产品属性" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.orderAttribute" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="价格类型" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.priceType"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.PriceType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="参考价格1" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.referencePrice1"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="参考价格2" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input allowClear v-model="formData.referencePrice2"></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="装运方式" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.shipmentTerm"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.ShipmentTerm)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="地点" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.shipLocation"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.ShipLocation)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="高附加值" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.isHighAddValue"> </a-checkbox>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="上锡板" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.isUpperTin" />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="已开测试架" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.openedTestrack" />
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="接单工厂" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-select
                    v-model="formData.contractFactoryId"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ContractFactoryId)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="后补合同" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.behindContract" />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="收货地址" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div style="position: relative" editWrapper>
                  <a-select
                    v-model="formData.shippingAddress"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    style="width: 78.5%"
                    @change="addressChange"
                    @popupScroll="handlePopupScroll2"
                    @search="supValue1"
                  >
                    <a-select-option v-for="(item, index) in AddressList" :key="index" :title="item.text" :value="item.text" :lable="item.text">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                  <a-button type="primary" style="margin-left: 4%; position: absolute; height: 25px" @click="addClick">临时地址</a-button>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- <a-row>
            <a-col :span="6">
              <a-form-model-item label=" EQ联系人" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.eqContactPerson" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="EQ联系人电话" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.eqPhoneNumber" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="EQ联系人邮箱" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="formData.eqEmail" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="加急" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-checkbox v-model="formData.toJiaji" allowClear></a-checkbox>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row> -->
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="出货报告"
                prop="needReportList"
                :label-col="{ span: 3 }"
                :wrapper-col="{ span: 21 }"
                style="width: 100%; margin: 0"
              >
                <div class="editWrapper">
                  <a-select
                    v-model="needReportList"
                    mode="multiple"
                    placeholder="请选择"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    style="width: 100%"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.NeedReportList)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24">
              <a-form-model-item label="市场备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-textarea v-model="formData.mktNote" style="width: 100%" :auto-size="{ minRows: 2, maxRows: 2 }" />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="费用备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-textarea v-model="formData.costNote" style="width: 100%" :auto-size="{ minRows: 2, maxRows: 2 }" />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="型号备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-textarea v-model="formData.note" style="width: 100%" :auto-size="{ minRows: 2, maxRows: 2 }" />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <span title="添加EQ联系人" v-if="editFlag">
          <a-icon type="plus" style="margin-left: 10px; color: #ff9900; font-size: 20px" @click="addEQClick"></a-icon
        ></span>
        <div style="max-height: 184px; overflow-y: auto; width: 100%">
          <a-list itemKey="id" bordered :grid="{ gutter: 16, column: 2 }" :dataSource="tableDetails" :pagination="false" class="container">
            <template>
              <a-list-item v-for="item in tableDetails" :key="item.id">
                <div class="list-item-content">
                  <a-list-item-meta :title="getTitle(item)" :description="getTitle1(item)" />
                  <a-divider type="vertical" style="height: 40px; margin-top: 2%; border-color: rgb(0, 0, 0)" />
                  <span style="margin-top: 0px; width: 73px; margin-right: 10px" v-if="editFlag">
                    <a @click="compileS(item)">编辑</a>
                    <a-divider type="vertical" style="height: 15px; border-color: rgb(0, 0, 0)" />
                    <a-popconfirm title="确定要删除吗？" @confirm="cancelS(item)">
                      <a href="javascript:;">删除</a>
                    </a-popconfirm>
                  </span>
                  <span v-else>
                    <span v-if="item.default_" style="font-size: 14px; float: right; margin-top: 15px; margin-right: 16px; color: red">主邮箱</span>
                    <span v-else style="font-size: 14px; float: right; margin-top: 15px; margin-right: 12px; color: #000000">抄送邮箱</span>
                  </span>
                </div>
                <span v-show="editFlag">
                  <span v-if="item.default_" style="font-size: 14px; float: right; margin-top: -25px; margin-right: 30px; color: red">主邮箱</span>
                  <span v-else style="font-size: 14px; float: right; margin-top: -25px; margin-right: 22px; color: #000000">抄送邮箱</span>
                </span>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-card>
    <a-card>
      <a-modal
        v-model="rightVisible"
        :confirmLoading="loading"
        :mask="true"
        :maskClosable="false"
        centered
        @cancel="handleCancel1"
        title="客户添加"
        @ok="handleRight"
      >
        <div ref="SelectBox1">
          <a-form-model ref="ruleForm" :model="form" :rules="rules">
            <a-form-model-item label="默认联系人" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-checkbox v-model="form.default_" @change="handleChangetype_"></a-checkbox>
            </a-form-model-item>
            <a-form-model-item label="联系人类型" ref="type_" prop="type_" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-select
                v-model="form.type_"
                placeholder="请选择"
                showSearch
                allowClear
                @change="handleChangetype_"
                :getPopupContainer="() => this.$refs.SelectBox1"
              >
                <a-select-option value="办公"> 办公 </a-select-option>
                <a-select-option value="商务"> 商务 </a-select-option>
                <a-select-option value="采购"> 采购 </a-select-option>
                <a-select-option value="报价"> 报价 </a-select-option>
                <a-select-option value="技术"> 技术 </a-select-option>
                <a-select-option value="发货"> 发货 </a-select-option>
                <a-select-option value="询价"> 询价 </a-select-option>
                <a-select-option value="EQ联系人"> EQ联系人 </a-select-option>
                <a-select-option value="发票"> 发票 </a-select-option>
                <a-select-option value="跟单员"> 跟单员 </a-select-option>
                <a-select-option value="工作稿"> 工作稿 </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="联系人" ref="addName" prop="addName" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.addName" allowClear />
            </a-form-model-item>
            <a-form-model-item label="联系电话" ref="addPhone" prop="addPhone" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.addPhone" allowClear />
            </a-form-model-item>
            <a-form-model-item label="邮箱" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" ref="mailbox" prop="mailbox">
              <a-input v-model="form.mailbox" allowClear @change="mailboxChange" />
            </a-form-model-item>
            <a-form-model-item label="传真" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.fax" allowClear />
            </a-form-model-item>
            <a-form-model-item label="收货省份" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" ref="provinceData" prop="provinceData">
              <a-cascader
                v-model="form.provinceData"
                change-on-select
                :options="jsonList"
                placeholder="请选择"
                @change="onChangeData"
                allowClear
                :show-search="{ filter }"
              />
            </a-form-model-item>
            <a-form-model-item label="收货地址" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" ref="address" prop="address">
              <a-input v-model="form.address" allowClear @blur="parseAddress" />
            </a-form-model-item>
            <a-form-model-item label="收货公司" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-input v-model="form.receiverCompany_" allowClear />
            </a-form-model-item>
            <a-form-model-item label="终端客户" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-select
                v-model="form.terminalCust"
                showSearch
                allowClear
                optionFilterProp="lable"
                @popupScroll="handlePopupScroll"
                :getPopupContainer="() => this.$refs.SelectBox1"
                @search="supValue"
                @change="terminalChange"
              >
                <a-select-option v-for="item in frontDataZSupplier" :key="item.valueMember" :value="item.valueMember" :lable="item.valueMember">
                  {{ item.valueMember }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-form-model>
        </div>
      </a-modal>
    </a-card>
    <a-modal
      :title="EQtype == 'add' ? '添加EQ联系人' : '修改EQ联系人'"
      v-model="eqVisible"
      :mask="true"
      :maskClosable="false"
      @cancel="handleCancelEQ"
      centered
      @ok="handleokEQ"
    >
      <div ref="SelectBox2">
        <a-form-model ref="ruleFormEQ" :model="formEQ" :rules="rules2">
          <a-form-model-item label="默认联系人" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-checkbox v-model.trim="formEQ.default_" @change="ruleChange"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="联系人" ref="connactUseName_" prop="connactUseName_" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-input v-model.trim="formEQ.connactUseName_" allowClear />
          </a-form-model-item>
          <a-form-model-item label="联系电话" ref="connactPhone_" prop="connactPhone_" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-input v-model.trim="formEQ.connactPhone_" allowClear />
          </a-form-model-item>
          <a-form-model-item label="邮箱" ref="mailbox" prop="mailbox" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-input v-model.trim="formEQ.mailbox" allowClear @change="mailboxChange" />
          </a-form-model-item>
        </a-form-model>
      </div>
    </a-modal>
  </div>
</template>
<script>
import AddressParse, { AREA, Utils } from "address-parse";
import { deliveryAddress, adressByPcbId } from "@/services/mkt/orderInfo";
import { terminalCusts } from "@/services/mkt/CustInfoNew";
import jsonList from "../../../../../public/index";
import Cookie from "js-cookie";
import $ from "jquery";
export default {
  name: "SalesInfo",
  props: ["editFlag", "showData", "selectOption", "saveID"],
  data() {
    return {
      formData: {},
      showflyingProbe: false,
      AddressList: [],
      dataVisible: false,
      dataVisible1: false,
      processEdgesStrR: "",
      makeupVisible: false, // 拼版图弹窗开关
      needReportList: [], // 出货报告列表
      rules: {
        type_: [{ required: true, message: "联系人类型必须选择", trigger: "blur" }],
        addName: [{ required: true, message: "联系人必须填写", trigger: "blur" }],
        mailbox: [
          { pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: "请输入正确的邮箱格式", trigger: "blur" },
        ],
        addPhone: [{ pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/, message: "请输入正确的号码格式", trigger: "blur" }],
      },
      rules1: {
        delType: [{ required: true, message: "交货单位请必须选择", trigger: "blur" }],
      },
      rules2: {
        connactUseName_: [{ required: true, message: "联系人类型必须填写", trigger: "blur" }],
        connactPhone_: [
          { required: true, message: "联系电话必须填写", trigger: "blur" },
          { pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/, message: "请输入正确的号码格式", trigger: "blur" },
        ],
        mailbox: [
          { required: true, message: "邮箱必须填写", trigger: "blur" },
          { pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: "请输入正确的邮箱格式", trigger: "blur" },
        ],
      },
      rightVisible: false,
      form: {
        default_: false, // 默认联系人
        type_: "发货", // 联系人类型
        mailbox: "", //邮箱
        addName: "", // 联系人
        addPhone: "", // 联系电话
        fax: "",
        provinceData: [], // 收货省份
        receiverCompany_: "", // 收货公司
        address: "", // 收货地址
        terminalCust: "",
      },
      formEQ: {
        default_: true, // 默认联系人
        type_: "EQ联系人", // 联系人类型
        mailbox: "", //邮箱
        connactUseName_: "", // 联系人
        connactPhone_: "", // 联系电话
      },
      receiverState_: "",
      jsonList,
      terminalCustList: [],
      frontDataZSupplier: [], // 供应商 100条数据的集合
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      isFileType: false,
      receiverState_zh: "",
      receiverCity_zh: "",
      AddressListALL: [],
      valueData1: undefined,
      loading: false,
      tableDetails: [],
      eqVisible: false, //eq联系人新增
      EQtype: "", //eq联系人新增/编辑类型
      editData: {},
    };
  },
  filters: {
    invoiceFilter(val) {
      let val_ = "";
      switch (val) {
        case 0:
          val_ = "默认";
          break;
        case 1:
          val_ = "1.0w";
          break;
        case 2:
          val_ = "1.5w";
          break;
        case 3:
          val_ = "2.0w";
          break;
        case 4:
          val_ = "0.5w";
          break;
        case 5:
          val_ = "0.7w";
          break;
        case 6:
          val_ = "5w";
          break;
        case 8:
          val_ = "8w";
          break;
        case 9:
          val_ = "3w";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
    camFilter(val) {
      let val_ = "";
      switch (val) {
        case 1:
          val_ = "中级";
          break;
        case 2:
          val_ = "高级";
          break;
        case 3:
          val_ = "资深";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
  },
  mounted() {
    for (var a = 0; a < this.jsonList.length; a++) {
      for (var b = 0; b < this.jsonList[a].children.length; b++) {
        delete this.jsonList[a].children[b].children;
      }
    }
    this.formData = this.showData;
    this.handleChangetype_();
    this.getEditData();
    this.getDeliveryAddress();
    const token = Cookie.get("Authorization");
    const data = JSON.parse(localStorage.getItem("terminalCustList"));
    if (
      data &&
      token &&
      data.token == token &&
      data.filter(item => {
        return item.tradeType == this.formData.joinFactoryId;
      }).length
    ) {
      for (var aa = 0; aa < data.length; aa++) {
        if (data[aa].token == token && data[aa].tradeType == this.formData.joinFactoryId) {
          this.terminalCustList = data.data; //本地缓存
          this.frontDataZSupplier = data.data.slice(0, 20);
        }
      }
    } else {
      terminalCusts(this.formData.joinFactoryId).then(res => {
        if (res.data) {
          this.terminalCustList = res.data;
          this.frontDataZSupplier = res.data.slice(0, 20);
          let token = Cookie.get("Authorization");
          let arr = [];
          if (res.data.length) {
            if (data == null) {
              arr.push({ data: this.terminalCustList, token, tradeType: this.formData.joinFactoryId });
              localStorage.setItem("terminalCustList", JSON.stringify(arr)); //本地缓存
            } else {
              data.push({ data: this.terminalCustList, token, tradeType: this.formData.joinFactoryId });
              localStorage.setItem("terminalCustList", JSON.stringify(data)); //本地缓存
            }
          }
        } else {
          this.$message.error(res.message);
        }
      });
    }
  },
  methods: {
    changeFlyingProbe() {
      if (this.formData.flyingProbe == "sharestand" && (this.formData.joinFactoryId == "58" || this.formData.joinFactoryId == "59")) {
        this.showflyingProbe = true;
      } else {
        this.showflyingProbe = false;
      }
    },
    onChange1(value, dateString) {
      this.formData.marketDeliveryTime = dateString;
    },
    parseAddress() {
      if (this.form.address != "" && this.$route.query.factory != 37 && this.$route.query.factory != 57) {
        const result = AddressParse.parse(this.form.address);
        if (result.length == 1) {
          this.receiverState_zh = result[0].province;
          this.receiverCity_zh = result[0].city;
          const [province, city] = Utils.getTargetAreaListByCode("province", result[0].code, true);
          this.form.provinceData = [province.code, city.code];
          this.receiverState_ = province.code;
          this.receiverCity_ = city.code;
        }
      }
    },
    ruleChange() {
      if (!this.formEQ.default_) {
        const newRules = [{ required: false, message: "联系人类型必须填写", trigger: "blur" }];
        const newRules1 = [
          { required: false, message: "联系电话必须填写", trigger: "blur" },
          { pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/, message: "请输入正确的号码格式", trigger: "blur" },
        ];
        this.rules2 = { ...this.rules2, connactUseName_: newRules, connactPhone_: newRules1 };
      } else {
        const newRules = [{ required: true, message: "联系人类型必须填写", trigger: "blur" }];
        const newRules1 = [
          { required: true, message: "联系电话必须填写", trigger: "blur" },
          { pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/, message: "请输入正确的号码格式", trigger: "blur" },
        ];
        this.rules2 = { ...this.rules2, connactUseName_: newRules, connactPhone_: newRules1 };
      }
    },
    handleChangetype_() {
      if (this.form.default_ == true) {
        this.namerules();
        this.mailerules();
      } else {
        this.remail();
        this.rename();
      }
      if (this.form.type_ == "采购" || this.form.type_ == "发货") {
        this.provincerules();
        this.addressrules();
        this.remail();
      } else if (this.form.type_ == "询价") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else if (this.form.type_ == "跟单员") {
        this.phonerules();
        this.remail();
        this.readdress();
        this.reprovince();
      } else if (this.form.type_ == "工作稿") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else if (this.$route.query.tradeType == 37 || this.$route.query.tradeType == 57) {
        if (this.form.default_ == true && this.form.type_ == "EQ联系人") {
          this.mailerules();
          this.readdress();
          this.reprovince();
        }
      } else if (this.form.type_ == "EQ联系人") {
        this.mailerules();
        this.readdress();
        this.reprovince();
      } else {
        this.rephone();
        this.readdress();
        this.reprovince();
      }
    },
    phonerules() {
      this.rules = {
        ...this.rules,
        addPhone: [
          {
            required: true,
            message: "联系电话必须填写,请输入正确格式",
            trigger: "blur",
            pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/,
          },
        ],
      };
    },
    namerules() {
      this.rules = { ...this.rules, addName: [{ required: true, message: "联系人必须填写", trigger: "blur" }] };
    },
    mailerules() {
      this.rules = {
        ...this.rules,
        mailbox: [
          {
            required: true,
            message: "邮箱必须填写,请输入正确格式",
            trigger: "blur",
            pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
          },
        ],
      };
    },
    provincerules() {
      this.rules = { ...this.rules, provinceData: [{ required: true, message: "收货省份必须选择", trigger: "blur" }] };
    },
    addressrules() {
      this.rules = { ...this.rules, address: [{ required: true, message: "收货地址必须填写", trigger: "blur" }] };
    },
    rephone() {
      this.rules = {
        ...this.rules,
        addPhone: [{ pattern: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/, message: "请输入正确的号码格式", trigger: "blur" }],
      };
    },
    remail() {
      this.rules = {
        ...this.rules,
        mailbox: [
          { pattern: /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: "请输入正确的邮箱格式", trigger: "blur" },
        ],
      };
    },
    rename() {
      this.rules = { ...this.rules, addName: [] };
    },
    reprovince() {
      this.rules = { ...this.rules, provinceData: [] };
    },
    readdress() {
      this.rules = { ...this.rules, address: [] };
    },
    filter(inputValue, path) {
      return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
    },
    getDeliveryAddress() {
      deliveryAddress(this.saveID).then(res => {
        if (res.code) {
          //   let arr = []
          //   let str = ''
          //   for(var a=0;a<res.data.length;a++){
          //     if(res.data[a].erpKey_){
          //       str = '【' + res.data[a].address_  +'】【' + res.data[a].connactUseName_ +'】【' + res.data[a].connactPhone_ +'】|'+ res.data[a].erpKey_
          //     }else{
          //       str = '【' + res.data[a].address_  +'】【' + res.data[a].connactUseName_ +'】【' + res.data[a].connactPhone_ +'】'
          //     }
          //     arr.push(str)
          //   }
          //   for (let i = 0; i < arr.length; i++) {
          //    for (let j = i + 1; j < arr.length; j++) {
          //     if (arr[i] === arr[j]) {
          //       arr.splice(j, 1);
          //       j--;
          //     }
          //   }
          // }
          //   this.AddressListALL = arr
          //   this.AddressList = arr.slice(0, 20)
          this.AddressListALL = res.data;
          this.AddressList = res.data.slice(0, 20);
        }
      });
    },
    getEditData() {
      if (this.formData.needReportList) {
        this.needReportList = this.formData.needReportList.split(",");
      } else {
        this.needReportList = [];
      }
      this.changeFlyingProbe();
      this.formData.contractFactoryId = this.formData.contractFactoryId ? this.formData.contractFactoryId.toString() : "";
      this.formData.delQtyAll = this.formData.delQty * this.formData.testPointNum;
      let arr_ = [];
      for (var i = 0; i < this.formData.boardLayers; i++) {
        if (i == 0 || i == this.formData.boardLayers - 1) {
          arr_.push(this.formData.copperThickness);
        } else {
          arr_.push(this.formData.innerCopperThickness);
        }
      }
      this.formData.cuThickness = arr_.join("/");
      if (this.formData.processEdgesStrL == "updown" || this.formData.processEdgesStrL == "leftright" || this.formData.processEdgesStrL == "both") {
        this.formData.processEdgesStrR = 4;
      }

      if (this.formData.ipcLevel > 0) {
        this.formData.iPCLevel = this.formData.ipcLevel.toString();
      }
      this.tableDetails = this.formData.eqdtos;
    },
    mapKey(data) {
      if (!data || !data.length) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    //时间选择
    onChangeTime(data, dateString) {
      this.formData.deliveryDate = dateString;
    },
    numChange() {
      if (this.formData.delType == "set" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.boardArea = ((this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000).toFixed(2);
      }
      if (this.formData.delType == "pcs" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth && this.formData.su) {
        this.formData.boardArea = (
          (this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) /
          1000000 /
          this.formData.su
        ).toFixed(2);
      }
    },
    address() {
      this.$forceUpdate();
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate(value, list) {
      this.formData.shippingAddress = value;
      let a = this.getPrice(this.formData.shippingAddress, list, value);
      this.$forceUpdate();
    },
    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
    },
    addressChange() {
      this.$forceUpdate();
    },
    handleCancel1() {
      this.rightVisible = false;
      this.receiverState_zh = "";
      this.receiverCity_zh = "";
      this.$refs.ruleForm.resetFields();
    },
    mailboxChange() {
      this.$forceUpdate();
    },
    addClick() {
      this.rightVisible = true;
      this.form = {
        default_: false, // 默认联系人
        type_: "发货", // 联系人类型
        mailbox: "", //邮箱
        addName: "", // 联系人
        addPhone: "", // 联系电话
        fax: "",
        provinceData: [], // 收货省份
        receiverCompany_: "", // 收货公司
        address: "", // 收货地址
        terminalCust: "",
      };
      this.form.terminalCust = this.formData.terminalCust;
      this.handleChangetype_();
    },
    //新增联系人
    handleRight() {
      const form = this.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          let params = {
            id: this.saveID,
            address_: this.form.address,
            connactUseName_: this.form.addName,
            connactPhone_: this.form.addPhone,
            default_: this.form.default_,
            // receiverState_:this.receiverState_.length ? this.receiverState_:'',
            receiverState_: this.receiverState_,
            receiverCity_: this.receiverCity_,
            receiverCompany_: this.form.receiverCompany_,
            type_: this.form.type_,
            mailbox: this.form.mailbox,
            fax: this.form.fax,
            terminalCust: this.form.terminalCust,
            receiverState_zh: this.receiverState_zh,
            receiverCity_zh: this.receiverCity_zh,
          };
          this.loading = true;
          adressByPcbId(params).then(res => {
            this.formData.shippingAddress = res.message;
            this.rightVisible = false;
            this.loading = false;
            this.getDeliveryAddress();
          });
        }
      });
    },
    onChangeData(val) {
      if (val.length) {
        this.receiverState_zh = this.jsonList.filter(item => {
          return item.value == val[0];
        })[0].label;
      }
      if (val.length > 1) {
        var arr = [];
        arr = this.jsonList.filter(item => {
          return item.value == val[0];
        })[0].children;
        this.receiverCity_zh = arr.filter(item => {
          return item.value == val[1];
        })[0].label;
      }
      if (val.length) {
        if (val[0] == "110000" || val[0] == "310000" || val[0] == "120000" || val[0] == "500000") {
          this.receiverCity_zh = this.receiverState_zh;
          this.receiverState_zh = this.receiverState_zh.split("市")[0];
        }
      } else {
        this.receiverCity_zh = "";
        this.receiverState_zh = "";
      }
      this.receiverCity_ = val[1];
      this.receiverState_ = val[0];
    },
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.terminalCustList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.terminalCustList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.terminalCustList.forEach((item, index) => {
              if (item.valueMember.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.terminalCustList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    handlePopupScroll2(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.AddressListALL.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.AddressListALL.length;
          }
          // 判断是否有搜索
          if (this.valueData1) {
            this.AddressListALL.forEach((item, index) => {
              if (item.text.indexOf(this.valueData1) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.AddressListALL.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.AddressList = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue1(value) {
      if (value) {
        let that = this;
        that.valueData1 = value;
        let arr = that.AddressListALL.filter(m => m.text.indexOf(value) != -1);
        if (arr.length) {
          that.AddressList = arr.slice(0, 20);
        } else {
          that.AddressList = [];
        }
      } else {
        this.valueData1 = undefined;
        this.AddressList = this.AddressListALL.slice(0, 20);
      }
    },
    terminalChange() {
      this.$forceUpdate();
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value;
        let arr = that.terminalCustList.filter(m => m.valueMember.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.terminalCustList.slice(0, 20);
      }
    },
    getTitle(item) {
      return (
        <span>
          {item.connactUseName_ ? (
            <span style="margin-right: 5px;margin-left:5px;color:#000000;">
              <a-icon type="user" style="margin-right: 5px;"></a-icon>
              {item.connactUseName_} <span style="float:right;margin-right:10px;color:#000000;">EQ联系人</span>
            </span>
          ) : null}
        </span>
      );
    },
    getTitle1(item) {
      return (
        <span style="font-size:13px;">
          {item.connactPhone_ ? (
            <span style="margin-right: 5px;margin-left:5px;color:#000000; " class="tmp">
              <a-icon type="phone" style="margin-right: 5px;color:#000000;"></a-icon>
              <span title={item.connactPhone_}>{item.connactPhone_}</span>
            </span>
          ) : null}
          {item.mailbox ? (
            <span style="margin-right: 5px;display:inline-block;color:#000000;" class="tmp1">
              <a-icon type="mail" style="margin-right: 5px;color:#000000;"></a-icon>
              <span title={item.mailbox}>{item.mailbox}</span>
            </span>
          ) : null}
        </span>
      );
    },
    addEQClick() {
      this.eqVisible = true;
      this.EQtype = "add";
      this.formEQ = {
        default_: true, // 默认联系人
        type_: "EQ联系人", // 联系人类型
        mailbox: "", //邮箱
        connactUseName_: "", // 联系人
        connactPhone_: "", // 联系电话
      };
      this.ruleChange();
    },
    handleCancelEQ() {
      this.eqVisible = false;
      if (this.EQtype == "edit") {
        this.formEQ.type_ = "EQ联系人";
        this.formEQ.default_ = this.editData.default_;
        this.formEQ.connactUseName_ = this.editData.connactUseName_;
        this.formEQ.connactPhone_ = this.editData.connactPhone_;
        this.formEQ.mailbox = this.editData.mailbox;
      }
    },
    handleokEQ() {
      const form = this.$refs.ruleFormEQ;
      form.validate(valid => {
        if (valid) {
          this.eqVisible = false;
          if (this.EQtype == "add") {
            this.tableDetails.push(this.formEQ);
          } else {
            const index = this.tableDetails.findIndex(obj => obj === this.editData);
            if (index !== -1) {
              this.tableDetails[index] = this.formEQ;
            }
          }
        }
      });
    },
    compileS(record) {
      this.editData = record;
      this.formEQ.type_ = record.type_;
      this.formEQ.default_ = record.default_;
      this.formEQ.connactUseName_ = record.connactUseName_;
      this.formEQ.connactPhone_ = record.connactPhone_;
      this.formEQ.mailbox = record.mailbox;
      this.EQtype = "edit";
      this.eqVisible = true;
      this.ruleChange();
    },
    cancelS(record) {
      const index = this.tableDetails.findIndex(obj => obj === record);
      if (index !== -1) {
        this.tableDetails.splice(index, 1);
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .tmp {
  word-break: keep-all;
  vertical-align: top;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 28%;
  display: inline-block;
}
/deep/ .tmp1 {
  word-break: keep-all;
  vertical-align: top;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 67%;
  display: inline-block;
}
/deep/.list-item-content {
  display: flex;
  border: 1px solid #dfdfdf;
}
/deep/.ant-list-vertical .ant-list-item-meta {
  margin-bottom: 0;
}
/deep/.ant-list-bordered .ant-list-item {
  padding: 4px;
  margin-bottom: 0;
  //width:60%;
}
/deep/.ant-list-vertical .ant-list-item-meta-title {
  margin-bottom: 4px;
}

/deep/.ant-form-item-label {
  line-height: 27px;
}
/deep/.ant-select-selection-selected-value {
  float: left;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 21px;
}
/deep/ .ant-select-selection__rendered {
  height: 24px;
}
/deep/.ant-select-selection {
  height: 24px;
}
/deep/ .ant-input {
  height: 24px;
}

/deep/.ant-input:not(:last-child) {
  padding-right: 18px;
}

/deep/.ant-input-suffix {
  right: 5px;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-select-selection--multiple .ant-select-selection__choice {
  color: #000000;
}

/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
  padding-left: 10px;
}

/deep/.userStyle {
  .ant-select-selection {
    user-select: all;
  }
}
.div22 {
  /deep/.ant-form-item-control {
    padding: 0;
    max-height: 45px !important;
    min-height: 29px !important;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

/deep/.ant-select-selection-selected-value {
  span:nth-child(1) {
    display: none !important;
  }

  span:nth-child(2) {
    display: none !important;
  }

  span:nth-child(3) {
    width: 100% !important;
  }
}

// /deep/form .ant-select {
//   height: 22px;
// }

/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}

.contentInfo {
  // height:190px;
  height: 100%;
  width: 70%;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;

      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        // height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: 500;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }

    .ant-card-body {
      padding: 0;

      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }

      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .tmp {
          word-break: keep-all;
          vertical-align: top;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: inline-block;
        }

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 22px;
        }

        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;

          label {
            font-family: PingFangSC-Regular, Sans-serif;
            color: #000000;
            font-size: 14px;
            font-weight: 500;
          }
        }

        .ant-form-item-control-wrapper {
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;

          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }

            line-height: inherit;
            padding: 2px 4px;
            // padding-top: 4px;
            min-height: 29px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}

/deep/.ant-modal-body {
  .ant-form {
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;

    .ant-form-item {
      margin: 0;
      width: 100%;
      display: flex;

      .editWrapper {
        display: flex;
        align-items: center;
        min-height: 40px;

        /deep/.ant-select {
          width: 120px;
          height: 34px;
        }

        /deep/ .ant-input {
          width: 120px;
          height: 34px;
        }
        .ant-input-number {
          width: 120px;
        }
      }

      .ant-form-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-family: PingFangSC-Regular, Sans-serif;
        color: #000000;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        background-color: #fafafa;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;

        label {
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .ant-form-item-control-wrapper {
        font-family: PingFangSC-Regular, Sans-serif;
        color: #000000;
        font-size: 14px;
        font-weight: 500;

        .ant-form-item-control {
          .ant-form-item-children {
            display: block;
            min-height: 34px;
            line-height: 34px;

            .ant-checkbox-wrapper {
              height: 34px;
              line-height: 34px;
            }

            .ant-select-selection--single {
              height: 34px;
              line-height: 34px;
            }

            .ant-select-selection__rendered {
              line-height: 34px;
              height: 34px;
            }

            .ant-select {
              height: 34px !important;
              line-height: 34px;
            }
            .ant-select-selection-selected-value {
              line-height: 34px !important;
            }

            .ant-input {
              height: 34px;
              width: 100%;
            }
          }

          line-height: inherit;
          padding: 2px 10px;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
        }
      }
    }
  }
}
</style>
