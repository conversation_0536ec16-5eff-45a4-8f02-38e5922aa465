<!--生产管理- 对账列表 -->
<template>
    <a-spin :spinning="spinning">
      <div class="projectBackend" @click="bodyClick">
        <div class="content" ref="SelectBox">
          <a-input  placeholder="拼版编号" v-model="formData.OrderNo" style="width:183px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input>
          <a-input  placeholder="工厂" v-model="formData.FactoryCode" style="width:83px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input>
              <a-select placeholder="请选择状态" allowClear v-model="formData.Status" @keyup.enter.native="searchClick" 
              showSearch optionFilterProp="label" :getPopupContainer="()=>this.$refs.SelectBox">
                  <a-select-option value="0"  label="待确认"> 待确认</a-select-option>
                  <a-select-option value="5"  label="议价中"> 议价中</a-select-option>
                  <a-select-option value="10" label="已确认"> 已确认</a-select-option>     
                  <a-select-option value="20" label="待支付"> 待支付</a-select-option>   
                  <a-select-option value="30" label="已支付"> 已支付</a-select-option>             
              </a-select> 
              <a-date-picker 
                  format="YYYY-MM-DD "               
                  placeholder="开始时间"
                  @change="onChange1" />
              -
              <a-date-picker  
                  style="margin-right:0.5%;"              
                  format="YYYY-MM-DD "               
                  placeholder="结束时间"
                  @change="onChange2"
                 
              />
              <a-modal
                title="确认弹窗"
                :visible="dataM"
                @cancel="reportHandleCancel1"
                @ok="OkMode"
                ok-text="确定"
                destroyOnClose
                :maskClosable="false"
                :width="400"
                centered
                >
              <span style="font-size:16px;" v-if="this.selectedRowKeysArray.length == 1">【{{this.num2}}】{{ messageMode }}</span>   
              <span style="font-size:16px;" v-else>
                {{ messageMode }}
              </span>
              </a-modal> 
   
              <a-button type="primary" @click="searchClick" style="margin-right:0.5%;">搜索</a-button>  
              <span v-if="checkPermission('MES.ProManagement.ReconcileList.ExportBalance')" @click="billexport">
              <a-button type="primary" style="margin-right:0.5%;" >账单导出</a-button> 
              </span>
              <span v-if="checkPermission('MES.ProManagement.ReconcileList.BatchSyn')" @click="coordination">
                <a-button type="primary" style="margin-right:0.5%;">批量协同确认</a-button> 
              </span>
              <span v-if="checkPermission('MES.ProManagement.ReconcileList.BackXtSure')" @click="returncoordination">
                <a-button type="primary" style="margin-right:0.5%;">退回协同</a-button> 
              </span>
             
        </div>
        <div style='width:100%;display:flex;'>
           <div class="leftContent" ref="tableWrapper" style="position:relative" >
            <a-table 
              v-if="pageshow"
              :columns="columns" 
              :dataSource="orderListData" 
              :customRow="customRow"
              :pagination="pagination" 
              :rowKey="'id'" 
              :scroll="{x:1600,y: 696 }"
              :loading="orderListTableLoading"
              @change="handleTableChange"           
              :class="orderListData.length ? 'min-table':''"
              :rowClassName="isRedRow" 
              style="position: relative;"   
            >
            <template slot="solderColor" slot-scope="record">
                <span v-if="record.solderColorStr || record.solderColorBottomStr">{{record.solderColorStr}}/{{ record.solderColorBottomStr }}</span>
              </template>
              <div slot="businessOrderNo" slot-scope="text,record" @contextmenu.prevent.stop="rightClick1($event,text)">  
              <a :title="record.businessOrderNo" style="color:#000000;">{{record.businessOrderNo}}</a>
              </div>
              <span slot="num" slot-scope="text, record, index" class="topCss">
                {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
              </span>   
              <template slot="poUrl" slot-scope="record">
                <a-icon v-if="record.poUrl!= null && record.poUrl != ''" type="menu-fold" style="color: #428bca;font-size: 16px;font-weight: 500;"
                @click.stop="details1(record.poUrl)" class="topCss" ></a-icon>         
              </template>  
              <template slot="labelUrl" slot-scope="record">
                <a-icon v-if="record.labelUrl!= null && record.labelUrl != ''" type="menu-fold" style="color: #428bca;font-size: 16px;font-weight: 500;"
                @click.stop="details2(record.labelUrl)" class="topCss" ></a-icon>         
              </template>       
              <div slot="orderNo" slot-scope="text,record" @contextmenu.prevent.stop="rightClick1($event,text)"> 
                  <a  style="color: #428bca" @click.stop="details(record)" class="topCss" :title="record.orderNo">{{record.orderNo}} </a>&nbsp;
                <span class="tagNum" style="display:inline-block;">
                  <span v-if="record.IsUrgent" style="font-size: 14px;font-weight: 500; color:#ff9900;padding: 0 2px; margin: 0; display:inline-block;height: 19px;width:14px;
                  margin-right:4px;margin-left:-10px;user-select: none;"><a-icon type="thunderbolt" theme="filled"></a-icon> </span>
                  <a-tag v-if="record.isHighQuality && record.isHighQuality != 0" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px;
                   height: 21px;user-select: none;border: 1px solid #428bca">  {{ record.isHighQuality == 1 ? '优' : '精' }}</a-tag>
                  <a-tag v-if="record.isLock" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;
                  border: 1px solid #428bca" > 锁  </a-tag>
                  <a-tag v-if="record.smtFactoryId"  style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;
                  border: 1px solid #428bca" > 贴 </a-tag>
                  <a-tag v-if="record.confirmWorkingDraft " style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;
                  border: 1px solid #428bca" >确</a-tag>
                  <a-tag v-if="record.isBigCus" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;
                  border: 1px solid #428bca" > KA</a-tag>                 
                  <a-tag v-if="record.isReverse == '1' || record.isReverse == '2'" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px;
                   height: 21px;user-select: none;border: 1px solid #428bca"> 反 </a-tag>              
                </span>
              </div>
              <template slot="Process" slot-scope="record" >
                <span  >                          
                  <span>阻焊颜色:
                    <div v-if="record.solderColorStr.indexOf('绿') != -1 " style="width: 13px;height:13px;background: green;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.solderColorStr.indexOf('红') != -1 " style="width: 13px;height:13px;background: red;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.solderColorStr.indexOf('白') != -1 " style="width: 13px;height:13px;background: white;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.solderColorStr.indexOf('黑') != -1 " style="width: 13px;height:13px;background: black;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.solderColorStr.indexOf('黄') != -1 " style="width: 13px;height:13px;background: yellow;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.solderColorStr.indexOf('蓝') != -1 " style="width: 13px;height:13px;background: blue;display: inline-block;border:1px solid black"></div>
                    <a-icon v-if="record.solderColorStr == 'none' "  type="close-square" style="color:#d1cbcb;"></a-icon>
                    {{record.solderColorStr}}                                 
                  </span>
                  <br/>
                  <span>文字颜色:
                    <div v-if="record.fontColorStr.indexOf('绿') != -1 " style="width: 13px;height:13px;background: green;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.fontColorStr.indexOf('红') != -1 " style="width: 13px;height:13px;background: red;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.fontColorStr.indexOf('白') != -1 " style="width: 13px;height:13px;background: white;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.fontColorStr.indexOf('黑') != -1 " style="width: 13px;height:13px;background: black;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.fontColorStr.indexOf('黄') != -1 " style="width: 13px;height:13px;background: yellow;display: inline-block;border:1px solid black"></div>
                    <div v-if="record.fontColorStr.indexOf('蓝') != -1 " style="width: 13px;height:13px;background: blue;display: inline-block;border:1px solid black"></div>
                    <a-icon v-if="record.fontColorStr == 'none' "  type="close-square" style="color:#d1cbcb;"></a-icon>
                    {{record.fontColorStr}}                  
                  </span>
                </span>
              </template>                            
              <template slot="action" slot-scope="record" >               
                  <span style="color:#428bca;"  @click="down9(record)" >协同确认</span>  
                  <div data-v-50499b08 role="separator" class="ant-divider ant-divider-vertical"></div>            
                  <span style="color:#428bca;"  @click="eqClick(record)" >问客</span>             
              </template>      
            </a-table>
            <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox" >
                <a-menu-item @click="down"  v-if="showText">复制</a-menu-item>
              </a-menu>              
        </div>
        <div class="rightContent">
          <div class="top">
          <a-table
          :columns="columns1" 
          :rowClassName="isRedRow1"  
          :loading ="loadingt"
          :customRow="onClickRow1"
          :pagination=false
          :rowKey="(record, index) => { return index }" 
          :dataSource="topdataSource"
          :scroll="{y:340}"
          :class="topdataSource.length ? 'min-table':''"
          >
          <template slot="isChooseEdit" slot-scope="text, record,">
            <a-checkbox v-model="record.isChooseEdit" disabled> </a-checkbox>
          </template>
          <span slot="num" slot-scope="text, record, index" class="topCss">
              {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span> 
          <!-- <template slot="upOrDownPrice" slot-scope="text, record,">
            <a-input v-model="record.upOrDownPrice" v-if="PriceFlag && record.isCollectEdit" style="height:26px"  @blur="PriceChange(record)"> </a-input>
            <span v-else >{{record.upOrDownPrice}}</span>
          </template> -->
          </a-table>
        </div>
          <div class="bot">
          <a-table
          :columns="columns2" 
          :rowClassName="isRedRow2" 
          :customRow="onClickRow2"
          :loading ="loadingb"
          :rowKey="(record, index) => { return index }" 
          :pagination=false
          :dataSource="botdataSource"
          :scroll="{y:310}"
          :class="botdataSource.length?'you1':''"
          >
          <span slot="num" slot-scope="text, record, index" class="topCss">
              {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span> 
          <!-- <template slot="upOrDownPrice" slot-scope="text, record,">
            <a-input v-model="record.upOrDownPrice" v-if="PriceFlag && record.isCollectEdit" style="height:26px"  @blur="PriceChange(record)"> </a-input>
            <span v-else >{{record.upOrDownPrice}}</span>
          </template> -->
        </a-table>
        </div>
        </div>
      </div>
      <div class="bto">
        </div>  
      </div>  
    </a-spin>
</template>
<script>
import moment from "moment";
import XLSX from "xlsx";
import {
  proPinBanOrderList,exportbalance,batchsynaudit,backpropinban,pricedetaillist,orderpricedetaillist,
} from "@/services/scgl/OrderManagement/Reconciliation";
import {checkPermission} from "@/utils/abp";
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",    
    scopedSlots: { customRender: 'num' },
    className:'userStyle',
    width: 50,
    ellipsis: true,
    fixed:'left',
  },
  {
    title: "拼版编号",
    align: "left",
    dataIndex: 'orderNo',
    width:120,
    className:'userStyle',
    fixed:'left',
    ellipsis: true,
    scopedSlots: { customRender: 'orderNo' },
  },
  
  {
    title: "工厂",
    key: "tag",
    ellipsis: true,
    dataIndex:'factoryIdStr',
    align: "left",
    width: 90,
  },
  {
    title: "状态",
    className:'userStyle',
    align: "left",
    width: 70, 
    ellipsis: true,
    dataIndex:'status', 
  },
  {
    title: "采购单列",
    width: 80,
    // dataIndex: 'poUrl',
    align: "center",
    ellipsis: true,
    scopedSlots: { customRender: 'poUrl' },
  },
  {
    title: "出货标签",
    width: 80,
    align: "center",
    ellipsis: true,
    scopedSlots: { customRender: 'labelUrl' },
  },
  {
    title: "板材类型",
    className:'userStyle',
    width: 110,
    ellipsis: true,
    dataIndex: 'fR4TypeStr',
    align: "left",
  },
  {
    title: "阻焊(顶/底)",
    align: "left",
    className:'userStyle',
    width: 100,  
    ellipsis: true,
    scopedSlots: { customRender: 'solderColor' },         
  }, 
  {
    title: "板材品牌",
    width: 80,
    dataIndex: 'boardBrandStr',
    className:'userStyle',
    align: "left",
    ellipsis: true,
  },
  {
    title: "业务订单号",
    width: 129,
    dataIndex: 'businessOrderNo',
    className:'userStyle',
    align: "left",
    ellipsis: true,
    scopedSlots:{customRender:'businessOrderNo'}
  },
  {
    title: "板厚",
    width: 70,
    ellipsis: true,
    dataIndex: 'boardThickness',
    align: "left",
    scopedSlots:{customRender:''}
  },
  {
    title: "订单毛面积",
    width: 95,
    ellipsis: true,
    dataIndex: 'orderArea',
    align: "left",
  },
  {
    title: "出货净面积",
    width: 95,
    ellipsis: true,
    dataIndex: 'shipArea',
    align: "left",
  },
  {
    title: "客户需求PCS数量",
    width: 120,
    ellipsis: true,
    dataIndex: 'needPcsNum',
    align: "left",
  },
  {
    title: "外层铜厚",
    width: 80,
    ellipsis: true,
    dataIndex: 'copperThickness',
    align: "left",
    scopedSlots:{customRender:''}
  },
  {
    title: "内层铜厚",
    width: 80,
    ellipsis: true,
    dataIndex: 'innerCopperThickness',
    align: "left",
    scopedSlots:{customRender:''}
  },
  {
    title: "创建时间",
    align: "left",
    dataIndex:'createdTime',
    width:200,
    ellipsis: true,
    className:'createdTime',
  },
  {
    title: "表面处理",
    width: 80,
    ellipsis: true,
    dataIndex: 'surfaceFinishStr',
    align: "left",
    scopedSlots:{customRender:''}
  },
  {
    title: "平米单价",
    width: 80,
    ellipsis: true,
    dataIndex: 'purchaseMoney',
    align: "left",
    scopedSlots:{customRender:''}
  },
  {
    title: "业务端平米单价",
    width: 120,
    ellipsis: true,
    dataIndex: 'businessPurchaseMoney',
    align: "left",
    scopedSlots:{customRender:''}
  },
  {
    title: "板费",
    width: 60,
    dataIndex: 'boardPrice',
    align: "left",
    ellipsis: true,
    scopedSlots:{customRender:''}
  },
  {
    title: "业务端板费",
    width: 120,
    dataIndex: 'businessBoardPrice',
    align: "left",
    ellipsis: true,
    scopedSlots:{customRender:''}
  },
  {
    title: "溢价备注",
    width: 80,
    ellipsis: true,
    dataIndex: 'floatRemarkStr',
    align: "left",
  },
  {
    title: "对账金额",
    width: 80,
    ellipsis: true,
    dataIndex: 'reconciliationMoney',
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: 'action' },
    align: "left",
    width:130, 
    ellipsis: true, 
    class:"noCopy"
  },

]
const columns1 = [
{
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",    
    scopedSlots: { customRender: 'num' },
    width: 55,
  },
  {
    title: "选择",
    align: "left",
    width:40,
    dataIndex:'isChooseEdit', 
    scopedSlots:{customRender:'isChooseEdit'}
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    align: "left",
    width:95,
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    align: "left",
    width:65,
  },
  {
    title: "加减价",
    align: "left",
    width:55,
    scopedSlots:{customRender:'upOrDownPrice'}
  },
  {
    title: "实际报价",
    dataIndex: "actualPrice",
    align: "left",
    width:65,
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    align: "left",
    width:58,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    align: "left",
    width:55,
  },
]
const columns2 = [
{
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",    
    scopedSlots: { customRender: 'num' },
    width: 50,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    align: "left",
    width:80,
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    align: "left",
    width:80,
  },
  {
    title: "加减价",
    align: "left",
    width:60,
    scopedSlots:{customRender:'upOrDownPrice'}
  },
  {
    title: "实际报价",
    dataIndex: "actualPrice",
    align: "left",
    width:84,
  },
  {
    title: "美元价",
    align: "left",
    dataIndex: "usdPrice",
    width:70,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    align: "left",
    width:65,
  },
]
export default{
    name:'',
    components:{},
    inject:['reload'],
    data(){
        return{
          Parameter:'',
          params1:'',
          guid:'',
          row3:{},  
          spinning:false,
          formData:{
            OrderNo:'',
            FactoryCode:"",
            Status:undefined,
            StartDeliveryDate:'',
            EndDeliveryDate:'',
          },
          columns,
          columns1,
          columns2,
          orderListTableLoading:false,
          loadingt:false,
          loadingb:false,
          pagination: {
            pageSize: 20,
            pageIndex: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
          },
          orderListData:[],
          topdataSource:[],
          botdataSource:[],
          dataVisible1:false,
          filePaths:'',
          queryParam:{},
          selectedId:'',
          dataM:false,
          businessOrderNo:'',
          messageMode:'',
          pageshow:true,
          selectedRowKeysArray:[],
          proOrderId:'',
          num2:'',
          isDragging: false,
      startIndex: -1,  
      shiftKey :false ,
      menuVisible:false,
      showText:false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex:99
      },
      menuData:{}
        }
    },
    mounted(){
      this.getOrderList()   
      window.addEventListener('keydown',this.keydown)
      window.addEventListener('keyup',this.keyup)
      // window.addEventListener("mousedown",this.bodyClick)
    },
    watch:{
      'orderListData':{
      handler(val){
        if(val){
          this.$nextTick(function(){ 
            let obj =  document.getElementsByClassName('tagNum')
            let arr=[]
            for(let i=0;i<obj.length;i++){
              arr.push(obj[i].children.length)
            }
            let result = -Infinity;
            arr.forEach((item) => {
                if (item > result) {
                    result = item;
                } 
            });
            if(result == 0){
              this.columns[1].width = '120px'
              this.columns[2].width = '90px'
              this.columns[3].width = '70px'
              this.columns[4].width = '80px'
              this.columns[5].width = '80px'
              this.columns[6].width = '110px'
              this.columns[7].width = '100px'
              this.columns[8].width = '80px'
              this.columns[9].width = '129px'
              this.columns[10].width = '70px'
              this.columns[11].width = '95px'
               this.columns[12].width = '95px'
            }
            if(result >= 1){
              this.columns[1].width = 120 + result*20 + 'px'
              this.columns[2].width = 90 - result*2 + 'px'
              this.columns[3].width = 70 - result*2 + 'px'
              this.columns[4].width = 80 - result*2 + 'px'
              this.columns[5].width = 80 - result*2 + 'px'
              this.columns[6].width = 110 - result*2 + 'px'
              this.columns[7].width = 100 - result*2 + 'px'
              this.columns[8].width = 80 - result*2 + 'px'
              this.columns[9].width = 129 - result*2 + 'px'
              this.columns[10].width = 70 - result*2 + 'px'
              this.columns[11].width = 95 - result*1 + 'px' 
              this.columns[12].width = 95 - result*1 + 'px'
            }           
          })
        }
      }
    },
    },
  methods: {
    checkPermission,
    moment,
    isRedRow(record){
      let strGroup = []
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push('rowBackgroundColor')
      } 
      return strGroup   
    }, 
    onChange1 (value, dateString) {
      this.formData.StartDeliveryDate = dateString      
    },   
    onChange2 (value, dateString) {
      this.formData.EndDeliveryDate = dateString
    }, 
    //获取价格列表
    getResultList(record){ 
      this.loadingt = true; 
      pricedetaillist(record.id).then(res => {           
          if (res.code) {          
            this.topdataSource = res.data;  
            let index = res.data.findIndex(v => v.priceName.indexOf('平米价')!= -1)        
            if(this.topdataSource.length>0){
              this.guid4Parameter = this.topdataSource[index].guid4Parameter + this.topdataSource[index].guid4Order + this.topdataSource[index].guid4Calc                            
              this.getResultDetailList(this.topdataSource[index])   
              this.row3 = this.topdataSource[index]
            } else{
              this.botdataSource = []              
            }
            }
          }).finally(() => {
        this.loadingt = false;
      })
    },     
    //获取价格明细
    getResultDetailList(record){
      this.loadingb = true;  
      this.guid = this.proOrderId;
      orderpricedetaillist(record.guid4Parameter,record.calcNameID,this.guid).then(res => {    
          if (res.code) {          
            this.botdataSource = res.data;  
            }
          }).finally(() => {
            this.loadingb = false;
      })
    },     
    //行点击事件
    isRedRow1 (record) {
      if (record.guid4Parameter + record.guid4Order + record.guid4Calc == this.guid4Parameter) {
        return 'rowBackgroundColor'
      } else {
        return ''
      }
    },
    isRedRow2 (record) {
      if (record.guid4Parameter + record.guid4Order + record.guid4Calc == this.Parameter) {
        return 'rowBackgroundColor'
      } else {
        return ''
      }
    },   
    // 获取订单
    getOrderList(queryData){    
      this.params1 = {}
      this.pageStat=localStorage.getItem('Restat')== 'true'? true:false;  
      let obj = JSON.parse( localStorage.getItem('Reconciliation')) 
      if(obj){
        this.params1 = obj
        queryData = obj
      }
      if(this.pageStat){
          let pageCurrent=localStorage.getItem('RepageCurrent')
          if(pageCurrent!=undefined&&pageCurrent!=''){
              this.pagination.pageIndex= parseInt(pageCurrent)
          }
          let pageSize=localStorage.getItem('RepageSize')
          if(pageSize!=undefined&&pageSize!=''){
              this.pagination.pageSize= parseInt(pageSize)
          }
          let data=localStorage.getItem('queryParam4')
          if(data!=null&&data!=undefined&&data!=''){
              this.queryParam=JSON.parse(data)
          }
          }  
      this.queryParam.pageIndex=this.pagination.pageIndex
      this.queryParam.pageSize=this.pagination.pageSize
      let data = {
          ...this.queryParam
      }
      localStorage.setItem('queryParam4',JSON.stringify(data))    
      let params = {
        ...this.pagination,
      }
      if(queryData){
        params.OrderNo = queryData.OrderNo;
        params.Status = queryData.Status? Number(queryData.Status):null;       
        params.EndDeliveryDate = queryData.EndDeliveryDate;
        params.StartDeliveryDate = queryData.StartDeliveryDate;
        params.FactoryCode = queryData.FactoryCode;
       }
      this.orderListTableLoading = true;
      let indexId=localStorage.getItem('Reid') 
      let record= localStorage.getItem('Rerecord') 
      proPinBanOrderList (params).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
          this.pagination.total = res.data.totalCount;   
          localStorage.removeItem('Reconciliation')
          this.pagination.pageIndex = res.data.pageIndex    
          if(indexId!==''&&indexId!=null){
              this.selectedRowKeysArray[0] = Number(indexId) 
              this.proOrderId = Number(indexId) 
          }
          if(record!==''&&record!=null) {
            this.selectedRowsData = JSON.parse(record)
            this.num2= this.selectedRowsData.orderNo
            this.getResultList(this.selectedRowsData)
            }
            if(this.pageStat){  
            localStorage.removeItem('Reid')
            localStorage.removeItem('Rerecord')
            localStorage.removeItem('RepageCurrent')
            localStorage.removeItem('RepageSize')
            localStorage.removeItem('Restat')
            }
        }
      }).finally(()=> {
        this.orderListTableLoading = false;
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysArray = selectedRowKeys;

    },
    onClickRow1(record) {
      return {
        on: {
          click: () => {            
            this.guid4Parameter = record.guid4Parameter + record.guid4Order + record.guid4Calc
            this.proOrderId = this.selectedRowKeysArray[this.selectedRowKeysArray.length-1] 
            this.getResultDetailList(record)   
            this.upOrDownPrice = record.upOrDownPrice   
            this.row3 = record         
          },
        }
      }
    },
    keyup(e){
      this.shiftKey = e.ctrlKey 
    },  
    keydown(e){
          this.shiftKey = e.ctrlKey
    }, 
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
      
    },
    handleMouseMove(event, record, index) {     
      event.stopPropagation();
      if (this.isDragging &&  event.button === 0 && this.startIndex != index) {
        this.updateSelectedItems(this.startIndex, index);
      }
    },    
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if(this.shiftKey){
        let rowKeys = this.selectedRowKeysArray;
        this.num2=record.orderNo
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }  
        this.selectedRowKeysArray = rowKeys; 
        if(this.selectedRowKeysArray.length==1){
          this.selectedRowsData = record 
        }      
      }else{
        this.num2=record.orderNo
        if(this.startIndex == index){
          this.selectedRowsData = record       
          this.selectedRowKeysArray = [record.id]
        }         
      }
      this.proOrderId = this.selectedRowKeysArray[this.selectedRowKeysArray.length-1] 
        let obj = {}
        obj = this.orderListData.filter(item =>{return item.id == this.proOrderId})[0]
      if(this.selectedRowKeysArray.length!=0){
          this.getResultList(obj)
        }else{
          this.topdataSource = [];  
          this.botdataSource = [];  
        }
      this.shiftKey = false      
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.orderListData.slice(normalizedStart, normalizedEnd + 1);
      var arr=[]
      for(var a=0;a<selectedRowsData.length;a++){
        arr.push(selectedRowsData[a].id)
      }
      this.selectedRowKeysArray = arr
      if(startIndex < endIndex){
        this.selectedRowsData = this.orderListData.filter(item => {return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length-1]})[0]  
      }else{
        this.selectedRowsData = this.orderListData.filter(item => {return item.id == this.selectedRowKeysArray[0]})[0]  
      }    
     
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: (event) => this.handleMouseDown(event, record, index),
          mousemove: (event) => this.handleMouseMove(event, record, index),
          mouseup: (event) => this.handleMouseUp(event, record, index),

          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
            } 
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record)
          }
        },
      };
    },
    rightClick1(e,text,record){ 
      let event = e.target 
      if(e.target.localName != 'td'){
        event = e.target.parentNode
      }   
      if(e.target.parentNode.localName == 'span'){
        event = e.target.parentNode.parentNode.parentNode
      }  
      if(e.target.localName == 'path' || e.target.localName == 'svg'){
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode
        if(event.className.indexOf('tagNum') != -1){
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode
        }
      } 
      if(e.target.parentNode.localName == 'div'){
        event = e.target.parentNode.parentNode
      } 
      this.text=event.innerText;
      if(event.className.indexOf('noCopy') != -1 || !this.text){
        this.showText = false
      }else{    
        this.showText = true
      } 
      if(event.cellIndex == 1  || event.cellIndex == undefined){
        this.text = this.text.split(" ")[0]
      }      
      const tableWrapper = this.$refs.tableWrapper;    
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;   
      let  offsetx= event.offsetLeft  + event.offsetWidth - 10
      let offsety = event.offsetTop + 40; 
      if(event.cellIndex == this.columns.length -1){
        this.menuStyle.top =  cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      }else if(cellRect.left >= 1236){
        this.menuStyle.top = cellRect.top - wrapperRect.top  + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left  + "px";
      }else{
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      } 
      if(event.cellIndex == 0 || event.cellIndex == 1){
        this.menuStyle.top = offsety - 36 + "px";
        this.menuStyle.left = offsetx  + "px";
      }      
      document.body.addEventListener("click", this.bodyClick);
    },    
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },    
    down(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
      },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.num2=record.orderNo
            let rowKeys = this.selectedRowKeysArray;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            
            this.selectedRowKeysArray = rowKeys; 
            this.proOrderId = this.selectedRowKeysArray[this.selectedRowKeysArray.length-1] 
            let obj = {}
            obj = this.orderListData.filter(item =>{return item.id == this.proOrderId})[0]
           if(this.selectedRowKeysArray.length!=0){
              this.getResultList(obj)
            }else{
              this.topdataSource = [];  
              this.botdataSource = [];  
            }
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
        }
      }
    }, 
    onClickRow2(record) {
      return {
        on: {
          click: () => {            
            this.Parameter = record.guid4Parameter + record.guid4Order + record.guid4Calc
            this.upOrDownPrice = record.upOrDownPrice        
          },
        }
      }
    }, 
    handleTableChange(pagination, ) {
      this.pagination.pageIndex=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      let params = this.formData
      if(params.Status != undefined){
        params.Status = Number(this.formData.Status)
      } 
      localStorage.setItem('Reconciliation',JSON.stringify(this.params1))
      if(JSON.stringify(this.params1) != '{}'){
            this.getOrderList(this.params1)
          }else{
            this.getOrderList();
          }
    }, 
    searchClick(){
      this.selectedRowKeysArray.length=0
      this.topdataSource=[]
      this.botdataSource=[]
      let params = this.formData
      this.params1 = this.formData 
      localStorage.setItem('Reconciliation',JSON.stringify(this.params1))
      var arr1 = params.OrderNo.split('')
      if(arr1.length >30){
        arr1 = arr1.slice(0,30)
      }
      params.OrderNo = arr1.join('')     
        this.pageshow = false
        this.pagination.pageIndex = 1;
        this.getOrderList(params)
        this.$nextTick(() => {
           this.pageshow = true
        })
       },
    // 打印流程卡
    PrintClick(){      
      if(!this.selectedRowKeysArray.length){
        this.$message.warning('请选择流程卡')
        return
      }
      this.dataVisible1 = true
    },
    handleCancel2(){
      this.dataVisible1 = false
      this.getOrderList()
     
    },
    // 详情跳转
    details(record){
        localStorage.setItem('RepageCurrent',this.pagination.pageIndex)
        localStorage.setItem('RepageSize',this.pagination.pageSize)
        localStorage.setItem('Reid',record.id)
        localStorage.setItem('Rerecord',JSON.stringify(record))
        localStorage.setItem('Restat',true) 
        localStorage.setItem('Reconciliation',JSON.stringify(this.params1))
      this.$router.push({path:'orderDetail1',query:{ id:record.orderNo,businessOrderNo:record.businessOrderNo,ttype:1,ids:record.id} ,})
    },
    details1(record){    
      window.open(record,'_blank')
    },
    details2(record){    
      window.open(record,'_blank')
    },
    eqClick(record){
      let OrderNo = record.id
      let OrderNo2= record.orderNo
      const routeOne = this.$router.resolve({
              path: '/gongcheng/eqDetails',
              query:{
                OrderNo:OrderNo,
                eQSource:4,
                OrderNo2:OrderNo2,
              }
            })
        window.open(routeOne.href,  "_self",routeOne.query)
     
    },
    reportHandleCancel1(){
      this.dataM=false;
    },  
    //退回协同
    returncoordination(){
      let rowKeys = this.selectedRowKeysArray;
      if(rowKeys.length == 0){
        this.$message.warning('请选择订单')
        return
      }else{
        this.messageMode = '确定退回协同吗？'
        this.type = '2'
        this.dataM=true
      }
     },
    //协同确认
    coordination(){
      let rowKeys = this.selectedRowKeysArray;
      if(rowKeys.length == 0){
        this.$message.warning('请选择订单')
        return
      } else{
        if(this.selectedRowKeysArray.length==1){
          this.messageMode = '确定协同确认吗？'
        }else{
          this.messageMode = '确定批量协同确认吗？'
        }
        
        this.type = '1'
        this.dataM=true
      }
      
      
    },

    OkMode(){
      let rowKeys = this.selectedRowKeysArray;
      let ids ={
        'ids':rowKeys
      }
      if(this.type == '2'){
        backpropinban(ids).then(res=>{
          if(res.code){
            this.$message.success('退回协同成功')
            this.selectedRowKeysArray =[]
            this.proOrderId = ''
            this.getOrderList()
          }else{
            this.$message.error(res.message)
          }
          this.dataM = false
        })                       
      }
      if(this.type == '1'){      
        batchsynaudit(ids).then(res=>{
        if(res.code){
          this.$message.success('协同确认成功')
          this.getOrderList()
          this.selectedRowKeysArray =[]
          this.proOrderId = ''
        }else{
          this.$message.error(res.message)
          }
          this.dataM = false
        }) 
      }                   
    },
    //订单导出
    billexport(){
      if(!this.formData.StartDeliveryDate){
        this.$message.warning('请选择开始时间')
        return
      } 
      let params = {
        'StartDeliveryDate':this.formData.StartDeliveryDate,
        'EndDeliveryDate':this.formData.EndDeliveryDate,
      }
      exportbalance(params).then(res=>{
        if(res.code){  
          let bexport = JSON.parse(res.data)
        if(bexport.length){
          let data =[]
          let sheetName = []
          data = bexport
          sheetName=['账单导出'] 
          this.exportExcelFile(data,sheetName,'-账单表.xlsx')
            }
            else{
            this.$message.error('暂无数据')            
          } 
        }
      }) 
    },
    exportExcelFile(array,sheetName, fileName) {
      const workBook = {
        SheetNames: sheetName,
        Sheets: {},
      };
      sheetName.forEach((iten)=>{
          workBook.Sheets[iten] = XLSX.utils.json_to_sheet(array)
        })
      return XLSX.writeFile(workBook, fileName);
    },
  }
}
</script>
<style scoped lang="less">
.you1{
  /deep/.ant-table-body{
    height: 313px;
  }
}
/deep/.tabRightClikBox{
  li{
    height:24px;
    line-height:22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    color:#000000
  }
  .ant-menu-item:not(:last-child){
    margin-bottom: 4px;
  }
}
 /deep/.userStyle{
    user-select: none!important;
  }
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.rowBackgroundColor {
      background: #dcdcdc;
    }
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
}

/deep/.ant-input{
  font-weight:500;
}
 .rightContent {
    border-bottom: 2px solid rgb(233, 233, 240);
    width:30%;
    height:30%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
   }   
   /deep/ .top {
    overflow-x: hidden;
    overflow-y: auto; 
    height: 340px;
    .min-table {      
      .ant-table-body{
        min-height:342px;
      }
    }
    .ant-table-wrapper{
      .ant-spin-nested-loading{
        .ant-spin-container{
          .ant-table{
            .ant-table-content{
              .ant-table-scroll{
                .ant-table-placeholder{
                  height: 340px;
                }
              }
            }
          }
        }
      }
    }
     .ant-table-bordered.ant-table-empty .ant-table-placeholder{
        border-left:0;
        border-right:0;
      }      
      width: 100%;
      height: 380px;
      border: 2px solid rgb(233, 233, 240) ;
      border-bottom:1px;    
      } 
      /deep/ .bot {
                .ant-table-wrapper{
      .ant-spin-nested-loading{
        .ant-spin-container{
          .ant-table{
            .ant-table-content{
              .ant-table-scroll{
                .ant-table-placeholder{
                  height:313px;
                }
              }
            }
          }
        }
      }
    }
     .ant-table-bordered.ant-table-empty .ant-table-placeholder{
        border-left:0;
        border-right:0;
      }
      width: 100%;
      height:350px;
      border: 2px solid rgb(233, 233, 240) ;
      border-bottom:1px;    
      } 
 /deep/.ant-tag {
    font-size: 12px;    
     color:#ff9900;
     padding: 0 2px; 
     margin: 0; 
     margin-right: 3px; 
     height: 21px;
     user-select: none;
  }
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
.projectBackend {
  padding-top:10px;
  min-width: 1670px;
  .content{
    height:44px;
    width:100%;
    padding-left:6px;
    background: #FFFFFF;
    /deep/.ant-select-selection__placeholder{
      display: block;
    }
  }
  .ant-input,.ant-select{
    width:8%;
    margin-right:0.5%;
    margin-top:6px;
  }ant-table-body
  /deep/.userStyle{
    user-select: none!important;
  }
  width: 100%;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  /deep/.leftContent {
    position: relative;
    .ant-table-selection-column{
      padding:0!important;
    }
    background: #FFFFFF;
    .min-table {      
      .ant-table-body{
        min-height:696px;      
      }
    }
    // height:728px;
    height:732px;
    width: 70%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }
  .bto{
  height:50px;
  background: #FFFFFF;
  border:2px solid #E9E9F0;
}
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  /deep/ .ant-table-thead > tr > th {
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table{
    // min-height: 728px;
    .ant-table-thead > tr > th{
      padding: 7px 4px;
      // border-color: #f0f0f0;
      border-right:1px solid #efefef;
    }    
    .ant-table-tbody > tr > td {
      padding:6px 4px;
      border-right:1px solid #efefef;
      position:relative;
    }
    tr.ant-table-row-selected td {
     background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 6px 0 0 10px;
  }

}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background:#F8F8F8;
  // background: #F0F2F5;
}
</style>
