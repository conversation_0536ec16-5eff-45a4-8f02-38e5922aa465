{"name": "vue-antd-admin", "version": "0.7.2", "homepage": "https://iczer.github.io/vue-antd-admin", "private": true, "scripts": {"serve": "npm run change-modules && vue-cli-service serve --port=4200", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode development", "lint": "vue-cli-service lint", "predeploy": "yarn build", "deploy": "gh-pages -d dist -b pages -r https://gitee.com/iczer/vue-antd-admin.git", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "docs:deploy": "vuepress build docs && gh-pages -d docs/.vuepress/dist -b master -r https://gitee.com/iczer/vue-antd-admin-docs.git", "change-modules": "node ./scripts/change-modules.js"}, "dependencies": {"@antv/data-set": "^0.11.4", "@form-create/ant-design-vue": "^2.5.19", "@microsoft/signalr": "^6.0.6", "@vue-office/docx": "^1.1.3", "@vue-office/excel": "^1.7.8", "@vue-office/pdf": "^1.1.3", "@xkeshi/vue-barcode": "^1.0.0", "address-parse": "^1.2.19", "animate.css": "^4.1.0", "ant-design-vue": "1.7.8", "axios": "^0.21.4", "better-scroll": "^2.5.1", "canvg": "^4.0.1", "clipboard": "^2.0.11", "core-js": "^3.6.5", "date-fns": "^2.14.0", "docx-preview": "0.1.16", "dom-to-image": "^2.6.0", "echarts": "^5.2.1", "element-ui": "^2.15.8", "enquire.js": "^2.1.6", "file-saver": "^2.0.5", "highlight.js": "^10.2.1", "html-to-pdfmake": "^2.5.7", "html2canvas": "^1.4.0", "html2pdf.js": "^0.10.1", "idb": "^8.0.2", "jquery": "^3.6.0", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "jsbarcode": "^3.11.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "jszip": "^2.6.1", "mockjs": "^1.1.0", "moment": "^2.29.3", "nprogress": "^0.2.0", "papaparse": "^5.4.1", "pdfmake": "^0.2.10", "qrcodejs2": "^0.0.2", "rimraf": "^3.0.2", "serve": "^14.2.0", "sql-formatter": "^4.0.2", "tiff.js": "^1.0.0", "uuid": "^8.3.2", "v-viewer": "^1.6.4", "vconsole": "^3.14.6", "viser-vue": "^2.4.8", "vue": "^2.6.11", "vue-codemirror": "^4.0.6", "vue-demi": "^0.14.7", "vue-draggable-resizable": "^2.3.0", "vue-i18n": "^8.18.2", "vue-i18n-extract": "^2.0.7", "vue-pdf": "^4.3.0", "vue-photo-preview": "^1.1.3", "vue-print-nb": "^1.7.5", "vue-quill-editor": "^3.0.6", "vue-router": "3.0", "vue-touch": "^2.0.0-beta.4", "vue2-org-tree": "^1.3.6", "vuedraggable": "^2.23.2", "vuex": "^3.4.0", "vxe-table": "^3.5.7", "whatwg-fetch": "^3.6.2", "xe-utils": "^3.5.4", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@ant-design/colors": "^4.0.1", "@vue/cli-plugin-babel": "^4.4.0", "@vue/cli-plugin-eslint": "^4.4.0", "@vue/cli-service": "^4.4.0", "@vuepress/plugin-back-to-top": "^1.5.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.5", "babel-plugin-transform-remove-console": "^6.9.4", "babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^5.0.1", "deepmerge": "^4.2.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "fast-deep-equal": "^3.1.3", "gh-pages": "^3.1.0", "less": "^4.1.3", "less-loader": "^6.2.0", "patch-package": "^8.0.0", "script-loader": "^0.7.2", "style-resources-loader": "^1.3.2", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-infinite-scroll": "^2.0.2", "vue-printjs": "^1.0.0", "vue-template-compiler": "^2.6.11", "vue-wxlogin": "^1.0.4", "vuepress": "^1.5.2", "webpack": "^4.46.0", "webpack-theme-color-replacer": "^1.3.12", "whatwg-fetch": "^3.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-vars": "off", "no-undef": "off", "no-irregular-whitespace": "off", "no-console": "off", "no-debugger": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}