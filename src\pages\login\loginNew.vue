<template>
  <div>
      <a-spin tip="Loading...">
      </a-spin>
  </div>
</template>

<script>
    import {skipLogin,applicationConfiguration} from "@/services/user"
    import { setAuthorization } from "@/utils/request";
    import { loadRoutes } from "@/utils/routerUtil";
    import { mapMutations } from "vuex";
    export default {
        name: "",
        data() {
            return {
              spinning:true,
            }
        },
        created(){
            let parmas={
                username:'',
                timestamp:new Date().getTime(),
                verifyCode:this.$md5('kw3j5k32ur38rnerkKJHk83'+new Date().getTime()),
            }
            if(this.$route.query.uid){
                parmas.username=this.$route.query.uid
            }
            if (this.$route.query.phoneNumber && this.$route.query.isEnterpriseUser && this.$route.query.enterpriseId) {
              parmas['phoneNumber'] = this.$route.query.phoneNumber
              parmas['isEnterpriseUser'] = this.$route.query.isEnterpriseUser
              parmas['enterpriseId'] = this.$route.query.enterpriseId
            }
            // this.$message.info(parmas.username+parmas.timestamp+parmas.verifyCode)
            if(parmas.username!=''&&parmas.timestamp!=""&&parmas.verifyCode!=''){
                if(localStorage.getItem('adminToken')!=null&&JSON.parse(localStorage.getItem('adminToken')) .userName==parmas.username){
                    applicationConfiguration().then((res) => {
                        res.currentUser.tenantName=res.currentTenant.name;
                        this.setUser(res.currentUser);
                        let permissions = this.handlePermissions(res.auth.grantedPolicies);
                        this.setPermissions(permissions);
                        this.setRoles(res.currentUser.roles);
                        loadRoutes();
                        this.spinning = false
                        if (this.$route.query.type == 'impedance') {
                          this.$router.push('/impedance')
                        }
                        else {
                          this.$router.push('/flyingProbe')
                        }

                    });
                }else {
                    let url;
                    if (this.$route.query.type == 'impedance') {
                      url = '/api/app/single-sign-on/imp-single-sign-on'
                    } else {
                      url = '/api/app/single-sign-on/single-sign-on'
                    }
                    skipLogin(parmas,url).then(res=>{
                        if(res.code=='1'){
                          if (this.para)
                            setAuthorization({
                                token: res.data.accessToken,
                                expireAt: new Date(new Date().getTime() + res.data.expiresIn),
                            });
                            let user={
                                token: res.data.accessToken,
                                expireAt: new Date(new Date().getTime() + res.data.expiresIn),
                                expiresIn:res.data.expiresIn,
                                userName:  parmas.username
                            }
                            localStorage.setItem('adminToken',JSON.stringify(user))
                            if(localStorage.getItem('adminToken')!=null){
                                applicationConfiguration().then((res) => {
                                    res.currentUser.tenantName=res.currentTenant.name;
                                    this.setUser(res.currentUser);
                                    let permissions = this.handlePermissions(res.auth.grantedPolicies);
                                    this.setPermissions(permissions);
                                    this.setRoles(res.currentUser.roles);
                                    loadRoutes();
                                  this.spinning = false
                                  if (this.$route.query.type == 'impedance') {
                                    this.$router.push('/impedance')
                                  } else {
                                    this.$router.push('/flyingProbe')
                                  }
                                });
                            }
                        }else {
                            this.spinning = false
                            this.$message.info(res.message)
                        }
                    })
                }


            }
        },
        component: {},
        methods: {
            ...mapMutations("account", ["setUser", "setPermissions", "setRoles"]),
            handlePermissions(obj) {
                let permissions = [];
                if (!obj) {
                    return permissions;
                }
                permissions = Object.keys(obj).map((x) => {
                    return {
                        id: x,
                        operation: [],
                    };
                });
                return permissions;

            },
        },
    }
</script>

<style scoped>

</style>
