<template>
  <a-spin :spinning="spinning">
    <div class="box" ref="SelectBox" style="height: 771px">
      <a-form-model layout="inline" v-if="!editFlg1" style="border-top: 1px solid #ddd">
        <div
          ref="div1"
          class="div1"
          style="
            width: 80%;
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            justify-content: start;
            max-height: 699px;
            align-content: flex-start;
          "
        >
          <a-form-model-item
            label="本厂编号"
            :title="proOrderInfoDto.orderNo"
            v-show="proOrderInfoDto.orderNo"
            :class="proOrderInfoDto.orderNo ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.orderNo">{{ proOrderInfoDto.orderNo }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户型号"
            :title="proOrderInfoDto.pcbFileName"
            v-show="proOrderInfoDto.pcbFileName"
            :class="proOrderInfoDto.pcbFileName ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.pcbFileName" class="tmp1" @click="dwon1(proOrderInfoDto.pcbFilePath)">{{
              proOrderInfoDto.pcbFileName
            }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板类型"
            :title="proOrderInfoDto.plateTypeStr"
            v-show="proOrderInfoDto.plateTypeStr"
            :class="proOrderInfoDto.plateTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.plateTypeStr">{{ proOrderInfoDto.plateTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户物料号"
            :title="proOrderInfoDto.customerMaterialNo"
            v-show="proOrderInfoDto.customerMaterialNo"
            :class="proOrderInfoDto.customerMaterialNo ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.customerMaterialNo">{{ proOrderInfoDto.customerMaterialNo }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品板厚mm"
            :title="proOrderInfoDto.boardThickness"
            v-show="proOrderInfoDto.boardThickness"
            :class="proOrderInfoDto.boardThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.boardThickness">{{ proOrderInfoDto.boardThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板厚公差"
            :title="proOrderInfoDto.boardThicknessTol"
            v-show="proOrderInfoDto.boardThicknessTol"
            :class="proOrderInfoDto.boardThicknessTol ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span style="display: inline-block; width: 100%; overflow: hidden">{{ proOrderInfoDto.boardThicknessTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="层数"
            :title="proOrderInfoDto.boardLayers"
            v-show="proOrderInfoDto.boardLayers"
            :class="proOrderInfoDto.boardLayers ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span :title="proOrderInfoDto.boardLayers">{{ proOrderInfoDto.boardLayers }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品铜厚oz"
            :title="
              Number(proOrderInfoDto.boardLayers) > 2
                ? '[内]' + proOrderInfoDto.innerCopperThickness + '[外]' + proOrderInfoDto.copperThickness
                : '[外]' + proOrderInfoDto.copperThickness
            "
            v-show="proOrderInfoDto.copperThickness"
            :class="proOrderInfoDto.copperThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>
              <span v-if="Number(proOrderInfoDto.boardLayers) > 2">
                <b v-if="proOrderInfoDto.innerCopperThickness" style="margin-right: 0.5%; margin-left: 0.5%">[内]</b
                >{{ proOrderInfoDto.innerCopperThickness }}
              </span>
              <b v-if="proOrderInfoDto.copperThickness" style="margin-right: 0.5%; margin-left: 0.5%">[外]</b>
              {{ proOrderInfoDto.copperThickness }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="成品铜厚um"
            v-if="proOrderInfoDto.isCopperThickConversion"
            :title="
              Number(proOrderInfoDto.boardLayers) > 2
                ? '[内]' + proOrderInfoDto.innerCopperThickness2 + '[外]' + proOrderInfoDto.copperThickness2
                : '[外]' + proOrderInfoDto.copperThickness2
            "
            v-show="proOrderInfoDto.copperThickness"
            :class="proOrderInfoDto.copperThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>
              <span v-if="Number(proOrderInfoDto.boardLayers) > 2">
                <b v-if="proOrderInfoDto.innerCopperThickness2" style="margin-right: 0.5%; margin-left: 0.5%">[内]</b
                >{{ proOrderInfoDto.innerCopperThickness2 }}
              </span>
              <b v-if="proOrderInfoDto.copperThickness2" style="margin-right: 0.5%; margin-left: 0.5%">[外]</b>
              {{ proOrderInfoDto.copperThickness2 }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊颜色"
            :title="
              proOrderInfoDto.solderColorStr && proOrderInfoDto.solderColorBottomStr
                ? '[顶]' + proOrderInfoDto.solderColorStr + '[底]' + proOrderInfoDto.solderColorBottomStr
                : proOrderInfoDto.solderColorStr
                ? '[顶]' + proOrderInfoDto.solderColorStr
                : '[底]' + proOrderInfoDto.solderColorBottomStr
            "
            v-show="proOrderInfoDto.solderColorStr"
            :class="proOrderInfoDto.solderColorStr || proOrderInfoDto.solderColorBottomStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span
              ><b style="font-family: PingFangSC-Regular, Sans-serif; font-size: 14px; color: #000000"> [顶] </b>
              {{ proOrderInfoDto.solderColorStr }} <b> [底] </b>
              {{ proOrderInfoDto.solderColorBottomStr }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊特性"
            :title="
              proOrderInfoDto.fontColorStr && proOrderInfoDto.fontColorBottomStr
                ? '[顶]' + proOrderInfoDto.fontColorStr + '[底]' + proOrderInfoDto.fontColorBottomStr
                : proOrderInfoDto.fontColorStr
                ? '[顶]' + proOrderInfoDto.fontColorStr
                : '[底]' + proOrderInfoDto.fontColorBottomStr
            "
            v-show="proOrderInfoDto.solderCharacteristicsStr"
            :class="proOrderInfoDto.solderCharacteristicsStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            {{ proOrderInfoDto.solderCharacteristicsStr }}
          </a-form-model-item>
          <a-form-model-item
            label="阻焊油墨"
            :title="proOrderInfoDto.isInkNotHalogen ? proOrderInfoDto.solderResistInkStr + '无卤油墨:是' : proOrderInfoDto.solderResistInkStr"
            v-show="proOrderInfoDto.solderResistInkStr"
            :class="proOrderInfoDto.solderResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span v-show="proOrderInfoDto.solderResistInkStr"
              >{{ proOrderInfoDto.solderResistInkStr }} <b style="padding-left: 50px">无卤油墨 :</b>{{ proOrderInfoDto.isInkNotHalogen ? "是" : "" }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="无卤油墨"
            :title="proOrderInfoDto.isInkNotHalogen"
            v-show="proOrderInfoDto.isInkNotHalogen && !proOrderInfoDto.solderResistInkStr"
            :class="proOrderInfoDto.isInkNotHalogen && !proOrderInfoDto.solderResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isInkNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符颜色"
            :title="
              proOrderInfoDto.fontColorStr && proOrderInfoDto.fontColorBottomStr
                ? '[顶]' + proOrderInfoDto.fontColorStr + '[底]' + proOrderInfoDto.fontColorBottomStr
                : proOrderInfoDto.fontColorStr
                ? '[顶]' + proOrderInfoDto.fontColorStr
                : '[底]' + proOrderInfoDto.fontColorBottomStr
            "
            v-show="proOrderInfoDto.fontColorStr || proOrderInfoDto.fontColorBottomStr"
            :class="proOrderInfoDto.fontColorStr || proOrderInfoDto.fontColorBottomStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span><b> [顶] </b> {{ proOrderInfoDto.fontColorStr }} <b> [底] </b> {{ proOrderInfoDto.fontColorBottomStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符特性"
            :title="
              proOrderInfoDto.fontColorStr && proOrderInfoDto.fontColorBottomStr
                ? '[顶]' + proOrderInfoDto.fontColorStr + '[底]' + proOrderInfoDto.fontColorBottomStr
                : proOrderInfoDto.fontColorStr
                ? '[顶]' + proOrderInfoDto.fontColorStr
                : '[底]' + proOrderInfoDto.fontColorBottomStr
            "
            v-show="proOrderInfoDto.characterInkCharacteristicsStr"
            :class="proOrderInfoDto.characterInkCharacteristicsStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            {{ proOrderInfoDto.characterInkCharacteristicsStr }}
          </a-form-model-item>

          <a-form-model-item
            label="字符油墨"
            :title="
              proOrderInfoDto.characterNotHalogen ? proOrderInfoDto.characterResistInkStr + '字符无卤:是' : proOrderInfoDto.characterResistInkStr
            "
            v-show="proOrderInfoDto.characterResistInkStr"
            :class="proOrderInfoDto.characterResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span
              >{{ proOrderInfoDto.characterResistInkStr }} <b style="padding-left: 27px" v-show="proOrderInfoDto.characterNotHalogen">字符无卤:</b
              >{{ proOrderInfoDto.characterNotHalogen ? "是" : "" }}</span
            >
          </a-form-model-item>
          <a-form-model-item
            label="字符无卤"
            :title="proOrderInfoDto.characterNotHalogen"
            v-show="proOrderInfoDto.characterNotHalogen && !proOrderInfoDto.characterResistInkStr"
            :class="proOrderInfoDto.characterNotHalogen && !proOrderInfoDto.characterResistInkStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.characterNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="表面处理"
            v-show="proOrderInfoDto.surfaceFinishStr"
            :class="proOrderInfoDto.surfaceFinishStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>
              <span style="display: inline-block"> {{ proOrderInfoDto.surfaceFinishStr }}; </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj' ||
                    proOrderInfoDto.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+golddeposition' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+goldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.platedArea
                "
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin') &&
                  proOrderInfoDto.surfaceFinishJsonDto.platedArea
                "
                >镀金面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'hardgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold') &&
                  proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness
                "
                >镀金厚 :{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                >水金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'eletrolyticnickel' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'waterhardgold' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj' ||
                    proOrderInfoDto.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+golddeposition' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadspraytin+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+GoldPlatedFingers') &&
                  proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness
                "
                >镍: {{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                >软金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj' ||
                    proOrderInfoDto.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+golddeposition' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadspraytin+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+GoldPlatedFingers') &&
                  proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness
                "
                >金 :{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }} ;</span
              >
              <span
                v-if="
                  proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                >薄金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }} ;</span
              >
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold' && proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea2 }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2
                "
                >金: {{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                v-if="
                  proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                >厚金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }} ;</span
              >
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                :class="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ? 'sss' : ''"
                >化金厚 :{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'haslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'spraytin' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'tinplating' ||
                    proOrderInfoDto.surfaceFinish == 'tinnedcerium' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'yqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'Leadspraytin+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.newTinThickness
                "
                :class="
                  proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                  proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                  proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                  proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin'
                    ? 'sss'
                    : ''
                "
                >锡:{{ proOrderInfoDto.surfaceFinishJsonDto.newTinThickness }}um;
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'tinprecipitation' && proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2"
                >锡:{{ proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 }}um;
              </span>
              <span style="display: inline-block" v-if="proOrderInfoDto.surfaceFinish == 'tinprecipitation' && proOrderInfoDto.isSmt"
                >是否含有SMT贴片:{{ proOrderInfoDto.isSmt ? "是" : "否" }} ;
              </span>
              <span style="display: inline-block" v-if="proOrderInfoDto.surfaceFinish == 'immersiongold' && proOrderInfoDto.holeSealing"
                >封孔:{{ proOrderInfoDto.holeSealing ? "是" : "否" }};
              </span>
              <span style="display: inline-block" v-if="proOrderInfoDto.surfaceFinish == 'immersiongold' && proOrderInfoDto.holeSealingTime"
                >封孔盐雾时间(H):{{ proOrderInfoDto.holeSealingTime }};
              </span>
              <span
                style="width: 29%; display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'osp' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandGoldfinger') &&
                  proOrderInfoDto.surfaceFinishJsonDto.filmThickness
                "
                :class="
                  proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                  proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                  proOrderInfoDto.surfaceFinish == 'goldplatingandosp'
                    ? 'sss'
                    : ''
                "
                >膜厚:{{ proOrderInfoDto.surfaceFinishJsonDto.filmThickness }}um;
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'chemicalsilver' ||
                    proOrderInfoDto.surfaceFinish == 'sinksilverandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'silverplating' ||
                    proOrderInfoDto.surfaceFinish == 'outsourcingsilverplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness
                "
                >银厚: :{{ proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness }}um;
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' && proOrderInfoDto.surfaceFinishJsonDto.newTinThickness"
                >无铅锡厚:{{ proOrderInfoDto.surfaceFinishJsonDto.newTinThickness }}
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' && proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness"
              >
                镀镍厚:
                {{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }}
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' && proOrderInfoDto.surfaceFinishJsonDto.platedArea"
              >
                镍面积:{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' ||
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'hardgoldplating') &&
                  proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2
                "
                >镍厚:{{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }}
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
              >
                金:{{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block"
                v-if="
                  (proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold') &&
                  proOrderInfoDto.surfaceFinishJsonDto.platedArea2
                "
                >面积 :{{ proOrderInfoDto.surfaceFinishJsonDto.platedArea2 }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.platedArea"
              >
                化金面积:
                {{ proOrderInfoDto.surfaceFinishJsonDto.platedArea }}%;
              </span>
              <span
                style="display: inline-block"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
              >
                化金镍厚:{{ proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' && proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
              >
                化金厚:
                {{ proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2 }}{{ proOrderInfoDto.surfaceFinishThickUnit }};
              </span>
              <span
                style="display: inline-block; height: 30px"
                v-if="proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' && proOrderInfoDto.surfaceFinishJsonDto.paThickness"
              >
                钯:{{ proOrderInfoDto.surfaceFinishJsonDto.paThickness }}{{ proOrderInfoDto.surfaceFinishThickUnit }}
              </span>
            </span>
          </a-form-model-item>
          <a-form-model-item
            label="拼版方式"
            :title="proOrderInfoDto.pinBanType"
            v-show="proOrderInfoDto.pinBanType"
            :class="proOrderInfoDto.pinBanType ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.pinBanType }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="SU数"
            :title="proOrderInfoDto.su"
            v-show="proOrderInfoDto.su"
            :class="proOrderInfoDto.su ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.su }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="单元长mm"
            :title="proOrderInfoDto.boardHeight"
            v-show="proOrderInfoDto.boardHeight"
            :class="proOrderInfoDto.boardHeight ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardHeight }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="单元宽mm"
            :title="proOrderInfoDto.boardWidth"
            v-show="proOrderInfoDto.boardWidth"
            :class="proOrderInfoDto.boardWidth ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品长mm"
            :title="proOrderInfoDto.boardHeightSet"
            v-show="proOrderInfoDto.boardHeightSet"
            :class="proOrderInfoDto.boardHeightSet ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardHeightSet }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成品宽mm"
            :title="proOrderInfoDto.boardWidthSet"
            v-show="proOrderInfoDto.boardWidthSet"
            :class="proOrderInfoDto.boardWidthSet ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardWidthSet }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成型方式"
            :title="proOrderInfoDto.formingTypeStr"
            v-show="proOrderInfoDto.formingTypeStr"
            :class="proOrderInfoDto.formingTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.formingTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="成型公差"
            :title="proOrderInfoDto.formingTolStr"
            v-show="proOrderInfoDto.formingTolStr"
            :class="proOrderInfoDto.formingTolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.formingTolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="合拼款数"
            :title="proOrderInfoDto.pinBanNum"
            v-show="proOrderInfoDto.pinBanNum"
            :class="proOrderInfoDto.pinBanNum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.pinBanNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="出货单位"
            :title="proOrderInfoDto.boardTypeStr"
            v-show="proOrderInfoDto.boardTypeStr"
            :class="proOrderInfoDto.boardTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材商"
            :title="proOrderInfoDto.sheetTraderStr"
            v-show="proOrderInfoDto.sheetTraderStr"
            :class="proOrderInfoDto.sheetTraderStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.sheetTraderStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客供板材"
            :title="proOrderInfoDto.isCustomerBoard"
            v-show="proOrderInfoDto.isCustomerBoard"
            :class="proOrderInfoDto.isCustomerBoard ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isCustomerBoard ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材类型"
            :title="proOrderInfoDto.fR4TypeStr"
            v-show="proOrderInfoDto.fR4TypeStr"
            :class="proOrderInfoDto.fR4TypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.fR4TypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="耐CAF"
            :title="proOrderInfoDto.cafResistance ? '是' : ''"
            v-show="proOrderInfoDto.cafResistance"
            :class="proOrderInfoDto.cafResistance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.cafResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材型号"
            :title="proOrderInfoDto.boardBrandStr"
            v-show="proOrderInfoDto.boardBrandStr"
            :class="proOrderInfoDto.boardBrandStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.boardBrandStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材CTI"
            :title="proOrderInfoDto.plateCtiStr"
            v-show="proOrderInfoDto.plateCtiStr"
            :class="proOrderInfoDto.plateCtiStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plateCtiStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材TG"
            :title="proOrderInfoDto.fR4TgStr"
            v-show="proOrderInfoDto.fR4TgStr"
            :class="proOrderInfoDto.fR4TgStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.fR4TgStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="无卤板材"
            :title="proOrderInfoDto.isNotHalogen ? '是' : ''"
            v-show="proOrderInfoDto.isNotHalogen"
            :class="proOrderInfoDto.isNotHalogen ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="大料尺寸"
            :title="proOrderInfoDto.sheetSize"
            v-show="proOrderInfoDto.sheetSize"
            :class="proOrderInfoDto.sheetSize ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.sheetSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板材利用率(%)"
            :title="proOrderInfoDto.sheetUtilization"
            v-show="proOrderInfoDto.sheetUtilization"
            :class="proOrderInfoDto.sheetUtilization ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.sheetUtilization }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="层压不可更改"
            :title="proOrderInfoDto.isChangeLayerPres ? '是' : '否'"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            class="divitem"
          >
            <span>{{ proOrderInfoDto.isChangeLayerPres ? "是" : "否" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不接受打叉"
            :title="proOrderInfoDto.acceptCrossed ? '是' : ''"
            v-show="proOrderInfoDto.acceptCrossed"
            :class="proOrderInfoDto.acceptCrossed ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.acceptCrossed ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许补线"
            :title="proOrderInfoDto.notAcceptPatching ? '是' : ''"
            v-show="proOrderInfoDto.notAcceptPatching"
            :class="proOrderInfoDto.notAcceptPatching ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.notAcceptPatching ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="测试方式"
            :title="proOrderInfoDto.flyingProbeStr"
            v-show="proOrderInfoDto.flyingProbeStr"
            :class="proOrderInfoDto.flyingProbeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.flyingProbeStr }}</span>
          </a-form-model-item>
          <!-- <a-form-model-item label="盖ET章" :title="proOrderInfoDto.stampEt ? '是':''" v-show="proOrderInfoDto.stampEt" :class="proOrderInfoDto.stampEt?'divitem':''" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" >
            <span >{{proOrderInfoDto.stampEt?'是':''}}</span>                      
          </a-form-model-item> -->
          <a-form-model-item
            label="盖ET章"
            :title="proOrderInfoDto.stampEtPositionStr"
            v-show="proOrderInfoDto.stampEtPositionStr"
            :class="proOrderInfoDto.stampEtPositionStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.stampEtPositionStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="飞针测试方式"
            :title="proOrderInfoDto.fpTestMethodStr"
            v-show="proOrderInfoDto.fpTestMethodStr"
            :class="proOrderInfoDto.fpTestMethodStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.fpTestMethodStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="测试治具编号"
            :title="proOrderInfoDto.testFixtureNumber"
            v-show="proOrderInfoDto.testFixtureNumber"
            :class="proOrderInfoDto.testFixtureNumber ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.testFixtureNumber }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="检验标准"
            :title="proOrderInfoDto.ipcLevelStr"
            v-show="proOrderInfoDto.ipcLevelStr"
            :class="proOrderInfoDto.ipcLevelStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.ipcLevelStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="翘曲度"
            :title="proOrderInfoDto.warpage"
            v-show="proOrderInfoDto.warpage"
            :class="proOrderInfoDto.warpage ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.warpage }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="产品用途"
            :title="proOrderInfoDto.productUsageStr"
            v-show="proOrderInfoDto.productUsageStr"
            :class="proOrderInfoDto.productUsageStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.productUsageStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="标记位置"
            :title="proOrderInfoDto.markPositionStr"
            v-show="proOrderInfoDto.markPositionStr"
            :class="proOrderInfoDto.markPositionStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.markPositionStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="UL类型"
            :title="proOrderInfoDto.ulTypeStr"
            v-show="proOrderInfoDto.ulTypeStr"
            :class="proOrderInfoDto.ulTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.ulTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="标记类型"
            :title="proOrderInfoDto.markTypeStr"
            v-show="proOrderInfoDto.markTypeStr"
            :class="proOrderInfoDto.markTypeStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.markTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="标记面向"
            :title="proOrderInfoDto.markFaceStr"
            v-show="proOrderInfoDto.markFaceStr"
            :class="proOrderInfoDto.markFaceStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.markFaceStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="周期格式"
            :title="proOrderInfoDto.periodicFormatStr"
            v-show="proOrderInfoDto.periodicFormatStr"
            :class="proOrderInfoDto.periodicFormatStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.periodicFormatStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户指定"
            :title="proOrderInfoDto.custAssignment ? '是' : ''"
            v-show="proOrderInfoDto.custAssignment"
            :class="proOrderInfoDto.custAssignment ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.custAssignment ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="低阻测试"
            :title="proOrderInfoDto.isLowResistanceTest ? '是' : ''"
            v-show="proOrderInfoDto.isLowResistanceTest"
            :class="proOrderInfoDto.isLowResistanceTest ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isLowResistanceTest ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="确认工作稿"
            :title="proOrderInfoDto.confirmWorkingDraft ? '是' : ''"
            v-show="proOrderInfoDto.confirmWorkingDraft"
            :class="proOrderInfoDto.confirmWorkingDraft ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.confirmWorkingDraft ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="确认阻抗"
            :title="proOrderInfoDto.confirmImpedance ? '是' : ''"
            v-show="proOrderInfoDto.confirmImpedance"
            :class="proOrderInfoDto.confirmImpedance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.confirmImpedance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="确认叠层"
            :title="proOrderInfoDto.confirmStacking ? '是' : ''"
            v-show="proOrderInfoDto.confirmStacking"
            :class="proOrderInfoDto.confirmStacking ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.confirmStacking ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="V-CUT类型"
            :title="proOrderInfoDto.vCutStr"
            v-show="proOrderInfoDto.vCutStr"
            :class="proOrderInfoDto.vCutStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vCutStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="V-CUT刀数"
            :title="proOrderInfoDto.vCutKnifeNum"
            v-show="proOrderInfoDto.vCutKnifeNum"
            :class="proOrderInfoDto.vCutKnifeNum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vCutKnifeNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="V-CUT削铜"
            :title="proOrderInfoDto.vCutCoppershavedStr"
            v-show="proOrderInfoDto.vCutCoppershavedStr"
            :class="proOrderInfoDto.vCutCoppershavedStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vCutCoppershavedStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="角度"
            :title="proOrderInfoDto.vcutAngleStr"
            v-show="proOrderInfoDto.vcutAngleStr"
            :class="proOrderInfoDto.vcutAngleStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutAngleStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="角度公差"
            :title="proOrderInfoDto.vcutAngleTolStr"
            v-show="proOrderInfoDto.vcutAngleTolStr"
            :class="proOrderInfoDto.vcutAngleTolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutAngleTolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="余厚mm"
            :title="proOrderInfoDto.vcutSurplusThickness"
            v-show="proOrderInfoDto.vcutSurplusThickness"
            :class="proOrderInfoDto.vcutSurplusThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutSurplusThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="余厚公差mm"
            :title="proOrderInfoDto.vcutSurplusThicknessTol"
            v-show="proOrderInfoDto.vcutSurplusThicknessTol"
            :class="proOrderInfoDto.vcutSurplusThicknessTol ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutSurplusThicknessTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="余厚指定"
            :title="proOrderInfoDto.vcutSurplusThicknessZD"
            v-show="proOrderInfoDto.vcutSurplusThicknessZD"
            :class="proOrderInfoDto.vcutSurplusThicknessZD ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.vcutSurplusThicknessZD }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="锣孔公差mm"
            :title="proOrderInfoDto.cncHoleTolStr"
            v-show="proOrderInfoDto.cncHoleTolStr"
            :class="proOrderInfoDto.cncHoleTolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.cncHoleTolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="锣带次数"
            :title="proOrderInfoDto.routNumber"
            v-show="proOrderInfoDto.routNumber"
            :class="proOrderInfoDto.routNumber ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.routNumber }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="最大孔粗(um)"
            :title="proOrderInfoDto.maxHoleWide"
            v-show="proOrderInfoDto.maxHoleWide"
            :class="proOrderInfoDto.maxHoleWide ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.maxHoleWide }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="孔位精度"
            :title="proOrderInfoDto.holeAccuracy"
            v-show="proOrderInfoDto.holeAccuracy"
            :class="proOrderInfoDto.holeAccuracy ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.holeAccuracy }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="孔到边精度"
            :title="proOrderInfoDto.holeToEdge"
            v-show="proOrderInfoDto.holeToEdge"
            :class="proOrderInfoDto.holeToEdge ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.holeToEdge }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="焊盘到边精度"
            :title="proOrderInfoDto.padToEdge"
            v-show="proOrderInfoDto.padToEdge"
            :class="proOrderInfoDto.padToEdge ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.padToEdge }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="特别备注"
            :title="proOrderInfoDto.speRemarks"
            v-show="proOrderInfoDto.speRemarks"
            :class="proOrderInfoDto.speRemarks ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.speRemarks }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="沉头孔类型"
            :title="proOrderInfoDto.counterboreType"
            v-show="proOrderInfoDto.counterboreType"
            :class="proOrderInfoDto.counterboreType ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.counterboreType }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="过孔处理"
            :title="proOrderInfoDto.solderCoverStr"
            v-show="proOrderInfoDto.solderCoverStr"
            :class="proOrderInfoDto.solderCoverStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.solderCoverStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="最小阻焊桥mm"
            :title="proOrderInfoDto.minSolderBridge"
            v-show="proOrderInfoDto.minSolderBridge"
            :class="proOrderInfoDto.minSolderBridge ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.minSolderBridge }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="最小开窗孔mm"
            :title="proOrderInfoDto.minimumwindowopening"
            v-show="proOrderInfoDto.minimumwindowopening"
            :class="proOrderInfoDto.minimumwindowopening ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.minimumwindowopening }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="基材油墨厚度um"
            :title="proOrderInfoDto.solderThickness"
            v-show="proOrderInfoDto.solderThickness"
            :class="proOrderInfoDto.solderThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.solderThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="线角油墨厚度um"
            :title="proOrderInfoDto.footLineInkThickness"
            v-show="proOrderInfoDto.footLineInkThickness"
            :class="proOrderInfoDto.footLineInkThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.footLineInkThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="铜面油墨厚度um"
            :title="proOrderInfoDto.cuInkThickness"
            v-show="proOrderInfoDto.cuInkThickness"
            :class="proOrderInfoDto.cuInkThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.cuInkThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊厚度"
            :title="proOrderInfoDto.solderInkThickness"
            v-show="proOrderInfoDto.solderInkThickness"
            :class="proOrderInfoDto.solderInkThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.solderInkThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="塞孔程度"
            :title="proOrderInfoDto.plugOilExtentStr"
            v-show="proOrderInfoDto.plugOilExtentStr"
            :class="proOrderInfoDto.plugOilExtentStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plugOilExtentStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="塞孔工具"
            :title="proOrderInfoDto.plugOilToolStr"
            v-show="proOrderInfoDto.plugOilToolStr"
            :class="proOrderInfoDto.plugOilToolStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plugOilToolStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="塞孔面次"
            :title="proOrderInfoDto.plugOilSideStr"
            v-show="proOrderInfoDto.plugOilSideStr"
            :class="proOrderInfoDto.plugOilSideStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.plugOilSideStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="是否大白油块"
            :title="proOrderInfoDto.isWhiteOilBlock ? '是' : ''"
            v-show="proOrderInfoDto.isWhiteOilBlock"
            :class="proOrderInfoDto.isWhiteOilBlock ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isWhiteOilBlock ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符网板印刷"
            :title="proOrderInfoDto.isCharacterScreenPrinting ? '是' : ''"
            v-show="proOrderInfoDto.isCharacterScreenPrinting"
            :class="proOrderInfoDto.isCharacterScreenPrinting ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isCharacterScreenPrinting ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符上表面"
            :title="proOrderInfoDto.isCharacterUpSurface ? '是' : ''"
            v-show="proOrderInfoDto.isCharacterUpSurface"
            :class="proOrderInfoDto.isCharacterUpSurface ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isCharacterUpSurface ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码方式"
            :title="proOrderInfoDto.qrCodeWay"
            v-show="proOrderInfoDto.qrCodeWay"
            :class="proOrderInfoDto.qrCodeWay ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeWay }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码类型"
            :title="proOrderInfoDto.qrCodeType"
            v-show="proOrderInfoDto.qrCodeType"
            :class="proOrderInfoDto.qrCodeType ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeType }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码要求"
            :title="proOrderInfoDto.qrCodeRequire"
            v-show="proOrderInfoDto.qrCodeRequire"
            :class="proOrderInfoDto.qrCodeRequire ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeRequire }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码阴阳"
            :title="proOrderInfoDto.qrCodeYY"
            v-show="proOrderInfoDto.qrCodeYY"
            :class="proOrderInfoDto.qrCodeYY ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.qrCodeYY }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码尺寸"
            :title="proOrderInfoDto.codeSize"
            v-show="proOrderInfoDto.codeSize"
            :class="proOrderInfoDto.codeSize ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.codeSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码面次"
            :title="proOrderInfoDto.codeFace"
            v-show="proOrderInfoDto.codeFace"
            :class="proOrderInfoDto.codeFace ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.codeFace }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="IC最小开窗MIL"
            :title="proOrderInfoDto.isIc"
            v-show="proOrderInfoDto.isIc"
            :class="proOrderInfoDto.isIc ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isIc }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="塞孔要求来源"
            :title="proOrderInfoDto.viaRequire"
            v-show="proOrderInfoDto.viaRequire"
            :class="proOrderInfoDto.viaRequire ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.viaRequire }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="生产方式"
            :title="proOrderInfoDto.produceWay"
            v-show="proOrderInfoDto.produceWay"
            :class="proOrderInfoDto.produceWay ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.produceWay }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许过孔发黄"
            :title="proOrderInfoDto.viaNotYellow ? '是' : ''"
            v-show="proOrderInfoDto.viaNotYellow"
            :class="proOrderInfoDto.viaNotYellow ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.viaNotYellow ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许线角漏铜"
            :title="proOrderInfoDto.leakCu ? '是' : ''"
            v-show="proOrderInfoDto.leakCu"
            :class="proOrderInfoDto.leakCu ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.leakCu ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许菲林印"
            :title="proOrderInfoDto.filmSeal ? '是' : ''"
            v-show="proOrderInfoDto.filmSeal"
            :class="proOrderInfoDto.filmSeal ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.filmSeal ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许补油"
            :title="proOrderInfoDto.notOil ? '是' : ''"
            v-show="proOrderInfoDto.notOil"
            :class="proOrderInfoDto.notOil ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.notOil ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="必须保证阻焊桥"
            :title="proOrderInfoDto.solderBridge ? '是' : ''"
            v-show="proOrderInfoDto.solderBridge"
            :class="proOrderInfoDto.solderBridge ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.solderBridge ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许油墨入孔"
            :title="proOrderInfoDto.notLnkHole ? '是' : ''"
            v-show="proOrderInfoDto.notLnkHole"
            :class="proOrderInfoDto.notLnkHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.notLnkHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许假性露铜"
            :title="proOrderInfoDto.exposedCu ? '是' : ''"
            v-show="proOrderInfoDto.exposedCu"
            :class="proOrderInfoDto.exposedCu ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.exposedCu ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="不允许磨板"
            :title="proOrderInfoDto.notRubBoard ? '是' : ''"
            v-show="proOrderInfoDto.notRubBoard"
            :class="proOrderInfoDto.notRubBoard ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.notRubBoard ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊前沉金"
            :title="proOrderInfoDto.goldSolder ? '是' : ''"
            v-show="proOrderInfoDto.goldSolder"
            :class="proOrderInfoDto.goldSolder ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldSolder ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻值要求"
            :title="proOrderInfoDto.impRequire"
            v-show="proOrderInfoDto.impRequire"
            :class="proOrderInfoDto.impRequire ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.impRequire }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="碳油"
            :title="proOrderInfoDto.isCarbonOil ? '是' : ''"
            v-show="proOrderInfoDto.isCarbonOil"
            :class="proOrderInfoDto.isCarbonOil ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isCarbonOil ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="碳油型号"
            :title="proOrderInfoDto.oilModel"
            v-show="proOrderInfoDto.oilModel"
            :class="proOrderInfoDto.oilModel ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.oilModel }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="Capton型号"
            :title="proOrderInfoDto.captonType"
            v-show="proOrderInfoDto.captonType"
            :class="proOrderInfoDto.captonType ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.captonType }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="高温胶"
            :title="proOrderInfoDto.heatTape ? '是' : ''"
            v-show="proOrderInfoDto.heatTape"
            :class="proOrderInfoDto.heatTape ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.heatTape ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="连续不断号"
            :title="proOrderInfoDto.isBrokenNumber"
            v-show="proOrderInfoDto.isBrokenNumber"
            :class="proOrderInfoDto.isBrokenNumber ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isBrokenNumber ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="接受断码"
            :title="proOrderInfoDto.iscodes"
            v-show="proOrderInfoDto.iscodes"
            :class="proOrderInfoDto.iscodes ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.iscodes }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="边框"
            :title="proOrderInfoDto.frame ? '是' : ''"
            v-show="proOrderInfoDto.frame"
            :class="proOrderInfoDto.frame ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.frame ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="干燥剂"
            :title="proOrderInfoDto.noAddDesiccantStr"
            v-show="proOrderInfoDto.noAddDesiccant"
            :class="proOrderInfoDto.noAddDesiccant ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.noAddDesiccantStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板间隔纸"
            :title="proOrderInfoDto.isBoardSpacerStr"
            v-show="proOrderInfoDto.isBoardSpacer"
            :class="proOrderInfoDto.isBoardSpacer ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isBoardSpacerStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="放湿度卡"
            :title="proOrderInfoDto.isHumidityCardStr"
            v-show="proOrderInfoDto.isHumidityCard"
            :class="proOrderInfoDto.isHumidityCard ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isHumidityCardStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="包装要求"
            :title="proOrderInfoDto.packagRequireStr"
            v-show="proOrderInfoDto.packagRequire"
            :class="proOrderInfoDto.packagRequire ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.packagRequireStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="交货方式"
            :title="proOrderInfoDto.deliveryMethod"
            v-show="proOrderInfoDto.deliveryMethod"
            :class="proOrderInfoDto.deliveryMethod ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.deliveryMethod }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="包装数量pcs"
            :title="proOrderInfoDto.packagingQuantity"
            v-show="proOrderInfoDto.packagingQuantity"
            :class="proOrderInfoDto.packagingQuantity ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.packagingQuantity }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="备品数量pcs"
            :title="proOrderInfoDto.spareQuantity"
            v-show="proOrderInfoDto.spareQuantity"
            :class="proOrderInfoDto.spareQuantity ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.spareQuantity }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="附出货菲林"
            :title="proOrderInfoDto.attachedShippingFilm"
            v-show="proOrderInfoDto.attachedShippingFilm"
            :class="proOrderInfoDto.attachedShippingFilm ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.attachedShippingFilm ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="盘中孔"
            :title="proOrderInfoDto.isDiscHole ? '是' : ''"
            v-show="proOrderInfoDto.isDiscHole"
            :class="proOrderInfoDto.isDiscHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isDiscHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="半孔"
            :title="proOrderInfoDto.isHalfHole ? '是' : ''"
            v-show="proOrderInfoDto.isHalfHole"
            :class="proOrderInfoDto.isHalfHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isHalfHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="异形孔"
            :title="proOrderInfoDto.isProfileHole ? '是' : ''"
            v-show="proOrderInfoDto.isProfileHole"
            :class="proOrderInfoDto.isProfileHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.isProfileHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板边包金"
            :title="proOrderInfoDto.isPlateEdge ? '是' : ''"
            v-show="proOrderInfoDto.isPlateEdge"
            :class="proOrderInfoDto.isPlateEdge ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isPlateEdge ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="沉孔"
            :title="proOrderInfoDto.stepHole ? '是' : ''"
            v-show="proOrderInfoDto.stepHole"
            :class="proOrderInfoDto.stepHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.stepHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="印序列号"
            :title="proOrderInfoDto.isSerialNumber ? '是' : ''"
            v-show="proOrderInfoDto.isSerialNumber"
            :class="proOrderInfoDto.isSerialNumber ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.isSerialNumber ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="蓝胶"
            :title="proOrderInfoDto.isBlueGum ? '是' : ''"
            v-show="proOrderInfoDto.isBlueGum"
            :class="proOrderInfoDto.isBlueGum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isBlueGum ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="埋铜"
            :title="proOrderInfoDto.buriedCopper ? '是' : ''"
            v-show="proOrderInfoDto.buriedCopper"
            :class="proOrderInfoDto.buriedCopper ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.buriedCopper ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="埋阻"
            :title="proOrderInfoDto.buriedResistance ? '是' : ''"
            v-show="proOrderInfoDto.buriedResistance"
            :class="proOrderInfoDto.buriedResistance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.buriedResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="铜浆塞孔"
            :title="proOrderInfoDto.cuPlugHole ? '是' : ''"
            v-show="proOrderInfoDto.cuPlugHole"
            :class="proOrderInfoDto.cuPlugHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.cuPlugHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻抗"
            :title="proOrderInfoDto.isImpedance ? '是' : ''"
            v-show="proOrderInfoDto.isImpedance"
            :class="proOrderInfoDto.isImpedance ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore) || (showCG && show1)"
          >
            <span>{{ proOrderInfoDto.isImpedance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="压接孔"
            :title="proOrderInfoDto.isCrimpHole ? '是' : ''"
            v-show="proOrderInfoDto.isCrimpHole"
            :class="proOrderInfoDto.isCrimpHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isCrimpHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="背钻孔"
            :title="proOrderInfoDto.isBackDrilling ? '是' : ''"
            v-show="proOrderInfoDto.isBackDrilling"
            :class="proOrderInfoDto.isBackDrilling ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isBackDrilling ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="通孔控深"
            :title="proOrderInfoDto.isThroughHoleControl ? '是' : ''"
            v-show="proOrderInfoDto.isThroughHoleControl"
            :class="proOrderInfoDto.isThroughHoleControl ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isThroughHoleControl ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="盲孔控深"
            :title="proOrderInfoDto.isBlindHoleControl ? '是' : ''"
            v-show="proOrderInfoDto.isBlindHoleControl"
            :class="proOrderInfoDto.isBlindHoleControl ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.isBlindHoleControl ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阶梯孔"
            :title="proOrderInfoDto.steppedHole ? '是' : ''"
            v-show="proOrderInfoDto.steppedHole"
            :class="proOrderInfoDto.steppedHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span>{{ proOrderInfoDto.steppedHole ? "是" : "" }}</span>
          </a-form-model-item>

          <a-form-model-item
            label="斜边"
            :title="proOrderInfoDto.goldfingerBevelStr"
            v-show="proOrderInfoDto.goldfingerBevelStr"
            :class="proOrderInfoDto.goldfingerBevelStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="盲槽"
            :title="proOrderInfoDto.isBlindSlot ? '是' : ''"
            v-show="proOrderInfoDto.isBlindSlot"
            :class="proOrderInfoDto.isBlindSlot ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span
              ><span v-if="proOrderInfoDto.isBlindSlot">{{ proOrderInfoDto.isBlindSlot ? "是" : "" }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            label="银浆塞孔"
            :title="proOrderInfoDto.silverPlugHole ? '是' : ''"
            v-show="proOrderInfoDto.silverPlugHole"
            :class="proOrderInfoDto.silverPlugHole ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span>{{ proOrderInfoDto.silverPlugHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="跳V"
            :title="proOrderInfoDto.jumpCut ? '是' : ''"
            v-show="proOrderInfoDto.jumpCut"
            :class="proOrderInfoDto.jumpCut ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.jumpCut ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="局部镀厚金"
            :title="proOrderInfoDto.isPartPlatingThickGold == 1 ? '是' : ''"
            v-show="proOrderInfoDto.isPartPlatingThickGold == 1"
            :class="proOrderInfoDto.isPartPlatingThickGold == 1 ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isPartPlatingThickGold == 1 ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="特殊工艺"
            :title="proOrderInfoDto.specialType"
            v-show="proOrderInfoDto.specialType"
            :class="proOrderInfoDto.specialType ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.specialType }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="PIM值要求"
            :title="proOrderInfoDto.pimAsk"
            v-show="proOrderInfoDto.pimAsk"
            :class="proOrderInfoDto.pimAsk ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.pimAsk }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="微带线露铜"
            :title="proOrderInfoDto.copperWire ? '是' : ''"
            v-show="proOrderInfoDto.copperWire"
            :class="proOrderInfoDto.copperWire ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.copperWire ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="板边露铜"
            :title="proOrderInfoDto.expCopper ? '是' : ''"
            v-show="proOrderInfoDto.expCopper"
            :class="proOrderInfoDto.expCopper ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.expCopper ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金属铣槽"
            :title="proOrderInfoDto.isMetalSlot ? '是' : ''"
            v-show="proOrderInfoDto.isMetalSlot"
            :class="proOrderInfoDto.isMetalSlot ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.isMetalSlot ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指"
            :title="proOrderInfoDto.goldenfingerStr"
            v-show="proOrderInfoDto.goldenfingerStr"
            :class="proOrderInfoDto.goldenfingerStr ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldenfingerStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label='金手指金厚U"'
            :title="proOrderInfoDto.goldFingerThickness"
            v-show="proOrderInfoDto.goldFingerThickness"
            :class="proOrderInfoDto.goldFingerThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldFingerThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :label="'金手指镍厚' + proOrderInfoDto.goldfingerNieThicknessUnit"
            :title="proOrderInfoDto.goldfingerNickelThickness"
            v-show="proOrderInfoDto.goldfingerNickelThickness"
            :class="proOrderInfoDto.goldfingerNickelThickness ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerNickelThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边角度"
            :title="proOrderInfoDto.goldfingerBevelAngle"
            v-show="proOrderInfoDto.goldfingerBevelAngle"
            :class="proOrderInfoDto.goldfingerBevelAngle ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelAngle }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边深度"
            :title="proOrderInfoDto.goldfingerBevelDepth"
            v-show="proOrderInfoDto.goldfingerBevelDepth"
            :class="proOrderInfoDto.goldfingerBevelDepth ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelDepth }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边余厚"
            :title="proOrderInfoDto.goldfingerBevelSurplus"
            v-show="proOrderInfoDto.goldfingerBevelSurplus"
            :class="proOrderInfoDto.goldfingerBevelSurplus ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.goldfingerBevelSurplus }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="去独立PAD"
            :title="proOrderInfoDto.deSinglePad ? '是' : ''"
            v-show="proOrderInfoDto.deSinglePad"
            :class="proOrderInfoDto.deSinglePad ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.deSinglePad ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="最小孔铜"
            :title="proOrderInfoDto.minHoleCopper"
            v-show="proOrderInfoDto.minHoleCopper"
            :class="proOrderInfoDto.minHoleCopper ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.minHoleCopper }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="平均孔铜"
            :title="proOrderInfoDto.avgHoleCopper"
            v-show="proOrderInfoDto.avgHoleCopper"
            :class="proOrderInfoDto.avgHoleCopper ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.avgHoleCopper }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="光板数"
            :title="proOrderInfoDto.gbNum"
            v-show="proOrderInfoDto.gbNum"
            :class="proOrderInfoDto.gbNum ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.gbNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="对压"
            :title="proOrderInfoDto.counterPressure ? '是' : ''"
            v-show="proOrderInfoDto.counterPressure"
            :class="proOrderInfoDto.counterPressure ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.counterPressure ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="水印"
            :title="proOrderInfoDto.waterMark_ ? '是' : ''"
            v-show="proOrderInfoDto.waterMark_"
            :class="proOrderInfoDto.waterMark_ ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.waterMark_ ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="外层泪滴"
            :title="proOrderInfoDto.outerLineTeardrop ? '是' : ''"
            v-show="proOrderInfoDto.outerLineTeardrop"
            :class="proOrderInfoDto.outerLineTeardrop ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.outerLineTeardrop ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="内层泪滴"
            :title="proOrderInfoDto.innerLineTeardrop ? '是' : ''"
            v-show="proOrderInfoDto.innerLineTeardrop"
            :class="proOrderInfoDto.innerLineTeardrop ? 'divitem' : ''"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 14 }"
          >
            <span>{{ proOrderInfoDto.innerLineTeardrop ? "是" : "" }}</span>
          </a-form-model-item>
        </div>
        <div class="div2">
          <a-form-model-item
            label="出货报告"
            :title="proOrderInfoDto.needReportListStr"
            v-show="proOrderInfoDto.needReportListStr"
            :class="proOrderInfoDto.needReportListStr ? 'div22' : ''"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
            style="border-top: 1px solid #ddd"
          >
            <span class="edit" style="user-select: text">{{ proOrderInfoDto.needReportListStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="工程指示"
            :title="proOrderInfoDto.specialRemarks"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
            v-show="proOrderInfoDto.specialRemarks"
            :class="proOrderInfoDto.specialRemarks ? 'div22' : ''"
            style="border-top: 1px solid #ddd"
          >
            <span style="user-select: text">{{ proOrderInfoDto.specialRemarks }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="客户备注"
            :title="proOrderInfoDto.note"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
            v-show="proOrderInfoDto.note"
            :class="proOrderInfoDto.note ? 'div22' : ''"
            style="border-top: 1px solid #ddd"
          >
            <span style="user-select: text">{{ proOrderInfoDto.note }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊参数"
            :title="solderRequireStr"
            v-show="solderRequireStr"
            :class="solderRequireStr ? 'div22' : ''"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
            style="border-top: 1px solid #ddd"
          >
            <span class="edit" style="user-select: text">{{ solderRequireStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="阻焊备注"
            :title="proOrderInfoDto.solderRemarks"
            v-show="proOrderInfoDto.solderRemarks"
            :class="proOrderInfoDto.solderRemarks ? 'div22' : ''"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
          >
            <span>{{ proOrderInfoDto.solderRemarks }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="字符备注"
            :title="proOrderInfoDto.sikeRemarks"
            v-show="proOrderInfoDto.sikeRemarks"
            :class="proOrderInfoDto.sikeRemarks ? 'div22' : ''"
            :label-col="{ span: 2 }"
            :wrapper-col="{ span: 22 }"
          >
            <span>{{ proOrderInfoDto.sikeRemarks }}</span>
          </a-form-model-item>
        </div>
      </a-form-model>
      <div style="display: flex" v-if="editFlg1">
        <div>
          <pro-two1
            ref="protwo1"
            @jumptotwo2="handleJumpToTwo2"
            :editFlg1="editFlg1"
            :messageList="messageList"
            :proOrderInfoDto="proOrderInfoDto"
            :selectData="selectData"
            @boardThickness="boardThickness"
            @changelayers="changelayers"
            :show0="show0"
            :show1="show1"
            :show2="show2"
            :showMore="showMore"
            :showCG="showCG"
            :showHDI="showHDI"
            :showMM="showMM"
            :requiredLinkConfigpro="requiredLinkConfigpro"
            :ManufacturerTG="ManufacturerTG"
            :boardtgList="boardtgList"
            @solderColorC="solderColorC"
            :SolderResistInk1="SolderResistInk1"
          ></pro-two1>
          <pro-two2
            ref="protwo2"
            @fontColorC="fontColorC"
            :CharacterResistInk1="CharacterResistInk1"
            @jumptothree="handleJumpToThree"
            @resetjump="resetjump"
            :shouldjump="shouldJumpToTWo2"
            :editFlg1="editFlg1"
            :messageList="messageList"
            :proOrderInfoDto="proOrderInfoDto"
            :selectData="selectData"
            @boardThickness="boardThickness"
            @changelayers="changelayers"
            :requiredLinkConfigpro="requiredLinkConfigpro"
          ></pro-two2>
        </div>

        <pro-three
          ref="prothree"
          :shouldjump="shouldJumpToThree"
          @resetjump="resetjump"
          @jumptofour="handleJumpToFour"
          :editFlg1="editFlg1"
          :messageList="messageList"
          :proOrderInfoDto="proOrderInfoDto"
          :selectData="selectData"
          :factoryData="factoryData"
          :boardBrandList="boardBrandList"
          :sheetTraderList="sheetTraderList"
          @boardThickness="boardThickness"
          @changelayers="changelayers"
          :show0="show0"
          :show1="show1"
          :show2="show2"
          :showMore="showMore"
          :showCG="showCG"
          :showHDI="showHDI"
          :showMM="showMM"
          :requiredLinkConfigpro="requiredLinkConfigpro"
          :ManufacturerTG="ManufacturerTG"
          :boardtgList="boardtgList"
        ></pro-three>
        <pro-four
          ref="profour"
          :shouldjump="shouldJumpToFour"
          @resetjump="resetjump"
          @jumptofive1="handleJumpToFive1"
          :editFlg1="editFlg1"
          :messageList="messageList"
          :proOrderInfoDto="proOrderInfoDto"
          :selectData="selectData"
          :factoryData="factoryData"
          :boardBrandList="boardBrandList"
          :sheetTraderList="sheetTraderList"
          @boardThickness="boardThickness"
          @changelayers="changelayers"
          :show0="show0"
          :show1="show1"
          :show2="show2"
          :showMore="showMore"
          :showCG="showCG"
          :showHDI="showHDI"
          :showMM="showMM"
          :requiredLinkConfigpro="requiredLinkConfigpro"
          :ManufacturerTG="ManufacturerTG"
          :boardtgList="boardtgList"
        ></pro-four>
      </div>
      <div v-if="editFlg1" style="width: 1485px">
        <a-divider style="height: 6px; background-color: #c2c1c2; margin: 8px 0" />
        <a-row style="text-align: center">
          <a-col :span="4" style="border-top: 1px solid #ddd; border-left: 2px solid #ddd">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">钻铣参数</strong>
            </a-form-model-item>
          </a-col>
          <a-col :span="7" style="border-top: 1px solid #ddd; border-left: 2px solid #ddd; width: 451px">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">阻焊&文字参数 </strong>
            </a-form-model-item>
          </a-col>
          <a-col :span="3" style="border-top: 1px solid #ddd; border-left: 2px solid #ddd">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">出货信息</strong>
            </a-form-model-item>
          </a-col>
          <a-col :span="9" style="border-top: 1px solid #ddd; border-left: 2px solid #ddd">
            <a-form-model-item :wrapper-col="{ span: 24 }">
              <strong style="font-size: 14px; font-weight: bold">特殊工艺</strong>
            </a-form-model-item>
          </a-col>
        </a-row>
      </div>
      <div v-if="editFlg1" style="display: flex">
        <pro-five1
          ref="profive1"
          :shouldjump="shouldJumpToFive1"
          @resetjump="resetjump"
          @jumptofive2="handleJumpToFive2"
          :editFlg1="editFlg1"
          :messageList="messageList"
          :proOrderInfoDto="proOrderInfoDto"
          :selectData="selectData"
          :factoryData="factoryData"
          :boardBrandList="boardBrandList"
          :sheetTraderList="sheetTraderList"
          @boardThickness="boardThickness"
          @changelayers="changelayers"
          :show0="show0"
          :show1="show1"
          :show2="show2"
          :showMore="showMore"
          :showCG="showCG"
          :showHDI="showHDI"
          :showMM="showMM"
          :requiredLinkConfigpro="requiredLinkConfigpro"
          :ManufacturerTG="ManufacturerTG"
          :boardtgList="boardtgList"
        ></pro-five1>
        <pro-five2
          ref="profive2"
          :shouldjump="shouldJumpToFive2"
          @resetjump="resetjump"
          @jumptosix="handleJumpToSix"
          :editFlg1="editFlg1"
          :messageList="messageList"
          :proOrderInfoDto="proOrderInfoDto"
          :selectData="selectData"
          :factoryData="factoryData"
          :boardBrandList="boardBrandList"
          :sheetTraderList="sheetTraderList"
          @boardThickness="boardThickness"
          @changelayers="changelayers"
          :show0="show0"
          :show1="show1"
          :show2="show2"
          :showMore="showMore"
          :showCG="showCG"
          :showHDI="showHDI"
          :showMM="showMM"
          :requiredLinkConfigpro="requiredLinkConfigpro"
          :ManufacturerTG="ManufacturerTG"
          :boardtgList="boardtgList"
        ></pro-five2>
        <pro-six
          ref="prosix"
          :shouldjump="shouldJumpToSix"
          @resetjump="resetjump"
          :editFlg1="editFlg1"
          :messageList="messageList"
          :proOrderInfoDto="proOrderInfoDto"
          :selectData="selectData"
          :factoryData="factoryData"
          :boardBrandList="boardBrandList"
          :sheetTraderList="sheetTraderList"
          @boardThickness="boardThickness"
          @changelayers="changelayers"
          :show0="show0"
          :show1="show1"
          :show2="show2"
          :showMore="showMore"
          :showCG="showCG"
          :showHDI="showHDI"
          :showMM="showMM"
          :requiredLinkConfigpro="requiredLinkConfigpro"
          :ManufacturerTG="ManufacturerTG"
          :boardtgList="boardtgList"
        ></pro-six>
      </div>
      <div class="autoo" v-if="editFlg1">
        <a-row>
          <a-col :span="20">
            <a-form-model-item
              label="工程指示"
              :label-col="{ span: 2 }"
              :wrapper-col="{ span: 22 }"
              style="border-top: 1px solid #ddd"
              class="speclass"
            >
              <span>{{ proOrderInfoDto.specialRemarks }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="20">
            <a-form-model-item
              label="客户备注"
              :label-col="{ span: 2 }"
              :wrapper-col="{ span: 22 }"
              style="border-top: 1px solid #ddd"
              class="speclass"
            >
              <span>{{ proOrderInfoDto.note }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="20">
            <a-form-model-item
              label="阻焊参数"
              :label-col="{ span: 2 }"
              :wrapper-col="{ span: 22 }"
              style="border-top: 1px solid #ddd"
              class="speclass SolderSty"
            >
              <div>
                <a-select
                  v-model="solderRequire"
                  mode="multiple"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="item in Soldermaskparameters" :key="item.value" :value="item.value" :label="item.lable" :title="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="20">
            <a-form-model-item
              label="阻焊备注"
              :label-col="{ span: 2 }"
              :wrapper-col="{ span: 22 }"
              style="border-top: 1px solid #ddd"
              :class="requiredLinkConfigpro.solderRemarks && iseval(requiredLinkConfigpro.solderRemarks.isNullRules) ? 'require' : ''"
              class="speclass"
            >
              <div>
                <a-input v-model="proOrderInfoDto.solderRemarks" allowClear> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="20">
            <a-form-model-item
              label="字符备注"
              :class="requiredLinkConfigpro.sikeRemarks && iseval(requiredLinkConfigpro.sikeRemarks.isNullRules) ? 'require' : ''"
              :label-col="{ span: 2 }"
              :wrapper-col="{ span: 22 }"
              style="border-top: 1px solid #ddd"
              class="speclass"
            >
              <div>
                <a-input v-model="proOrderInfoDto.sikeRemarks" allowClear> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </div>
    </div>
  </a-spin>
</template>
<script>
import ProThree from "@/pages/gongcheng/projectIndicate/Product/ProThree";
import ProTwo1 from "@/pages/gongcheng/projectIndicate/Product/ProTwo1";
import ProTwo2 from "@/pages/gongcheng/projectIndicate/Product/ProTwo2";
import ProFour from "@/pages/gongcheng/projectIndicate/Product/ProFour";
import ProFive1 from "@/pages/gongcheng/projectIndicate/Product/ProFive1";
import ProFive2 from "@/pages/gongcheng/projectIndicate/Product/ProFive2";
import ProSix from "@/pages/gongcheng/projectIndicate/Product/ProSix";
import { fontcolorresistinkrelation, soldercolorresistinkrelation } from "@/services/projectIndicate";
import $ from "jquery";
let index = -1;
export default {
  name: "ProOne",
  props: [
    "proOrderInfoDto",
    "selectData",
    "factoryData",
    "editFlg1",
    "boardBrandList",
    "sheetTraderList",
    "messageList",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "requiredLinkConfigpro",
    "boardtgList",
    "ManufacturerTG",
    "solderRequire1",
    "Soldermaskparameters",
    "solderRequireStr",
  ],
  inject: ["reload"],
  components: { ProTwo1, ProTwo2, ProThree, ProFour, ProFive1, ProFive2, ProSix },
  data() {
    return {
      solderRequire: [],
      shouldJumpToTWo2: false,
      shouldJumpToThree: false,
      shouldJumpToFour: false,
      shouldJumpToFive1: false,
      shouldJumpToFive2: false,
      shouldJumpToSix: false,
      ymdata: [],
      SolderResistInk: [],
      SolderResistInk1: [],
      CharacterResistInk: [],
      CharacterResistInk1: [],
      spinning: false,
      prohibit: false,
      prohibit1: false,
      copyOld: "",
      copyNewVal: "",
      selectData1: {},
      selectValue: "",
      index: -1,
      sheetTrader: [],
      boardBrand: [],
      selectedValue: "",
      editValue: "",
      editable: false,
      options: [
        { value: "option1", label: "Option 1" },
        { value: "option2", label: "Option 2" },
        { value: "option3", label: "Option 3" },
      ],
    };
  },
  mounted() {
    this.solderRequire = this.solderRequire1;
    this.getfontdata();
    this.getsolddata();
    this.sheetTrader = this.sheetTraderList;
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  watch: {
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    "proOrderInfoDto.boardThickness"(newVal, oldVal) {
      setTimeout(() => {
        if (newVal != oldVal && newVal && oldVal) {
          this.copyOld = oldVal;
          this.copyNewVal = newVal;
        }
      }, 700);
    },
  },
  methods: {
    handleJumpToTwo2() {
      this.shouldJumpToTWo2 = true;
    },
    handleJumpToThree() {
      this.shouldJumpToThree = true;
    },
    handleJumpToFour() {
      this.shouldJumpToFour = true;
    },
    handleJumpToFive1() {
      this.shouldJumpToFive1 = true;
    },
    handleJumpToFive2() {
      this.shouldJumpToFive2 = true;
    },
    handleJumpToSix() {
      this.shouldJumpToSix = true;
    },
    resetjump() {
      this.shouldJumpToTWo2 = false;
      this.shouldJumpToThree = false;
      this.shouldJumpToFour = false;
      this.shouldJumpToFive1 = false;
      this.shouldJumpToFive2 = false;
      this.shouldJumpToSix = false;
    },
    changeSheet(val) {
      if (val) {
        this.proOrderInfoDto.sheetTrader = val;
      }
      if (this.boardBrandList.length && this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.proOrderInfoDto.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
      this.proOrderInfoDto.boardBrand = "";
    },
    handleResize() {
      var boxstyle = document.getElementsByClassName("box")[0];
      boxstyle.style.height = window.innerHeight - 140 + "px";
    },

    setStyle() {
      const elements = document.getElementsByClassName("divitem");
      const num = elements.length;
      for (var a = 0; a < elements.length; a++) {
        if (a < 24) {
          elements[a].style.width = "535px";
          elements[a].childNodes[0].style.width = "123px";
          elements[a].childNodes[1].style.width = "412px";
        } else {
          elements[a].style.width = "310px";
          elements[a].childNodes[0].style.width = "140px";
          elements[a].childNodes[1].style.width = "170px";
        }
      }
      var div2 = document.getElementsByClassName("div2")[0];
      if (div2) {
        if (num <= 24) {
          div2.style.width = "535px";
        } else if (num <= 48) {
          div2.style.width = "836px";
        } else if (num <= 72) {
          div2.style.width = "1137px";
        } else {
          div2.style.width = "94.95%";
        }
      }
      var div22 = document.getElementsByClassName("div22");
      for (var i = 0; i < div22.length; i++) {
        if (div2.style.width == "535px") {
          div22[i].childNodes[0].style.width = "123px";
          div22[i].childNodes[1].style.width = "412px";
        } else if (div2.style.width == "836px") {
          div22[i].childNodes[0].style.width = "123px";
          div22[i].childNodes[1].style.width = "713px";
        } else if (div2.style.width == "1137px") {
          div22[i].childNodes[0].style.width = "123px";
          div22[i].childNodes[1].style.width = "1014px";
        } else {
          div22[i].childNodes[0].style.width = "8.35%";
          div22[i].childNodes[1].style.width = "91.65%";
        }
      }
    },
    // 下载工程文件
    dwon2(val) {
      if (val) {
        window.location.href = val;
      } else {
        this.$message.error("工程文件不存在");
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    // 板厚更改 板厚公差=+/-板厚*0.1
    boardThickness() {
      this.$emit("boardThickness");
      if (this.proOrderInfoDto.boardThickness) {
        if (this.proOrderInfoDto.boardThickness <= 1.0) {
          this.proOrderInfoDto.boardThicknessTol = "+/-0.1";
        } else {
          this.proOrderInfoDto.boardThicknessTol = "+/-" + (this.proOrderInfoDto.boardThickness * 0.1).toFixed(2);
        }
      }
    },
    changxb() {
      if (!this.proOrderInfoDto.goldfingerBevel) {
        this.proOrderInfoDto.goldfingerBevelAngle = null;
        this.proOrderInfoDto.goldfingerBevelDepth = null;
        this.proOrderInfoDto.goldfingerBevelSurplus = null;
      }
    },
    goldenfingerChange() {
      if (!this.proOrderInfoDto.goldenfinger) {
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },
    MetalSlotChange() {
      if (this.$route.query.factory == 12) {
        if (this.proOrderInfoDto.isMetalSlot) {
          this.proOrderInfoDto.cncHoleTol = "+/-0.075";
        } else {
          this.proOrderInfoDto.cncHoleTol = "";
        }
      }
    },
    // 拼版方式更改
    pinBanType() {
      if (this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1) {
        if (this.proOrderInfoDto.boardType == "pcs") {
          this.$message.warning("拼版方式大于1x1,出货单位请选择set");
        }
      }
      // if(this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1 ){
      //   this.proOrderInfoDto.boardType = 'set'
      // }else{
      //   this.proOrderInfoDto.boardType = 'pcs'
      // }
    },
    //
    flyChange() {
      if (this.proOrderInfoDto.flyingProbe == "FlyingProbe") {
        this.proOrderInfoDto.fpTestMethod = "capacitance";
        this.proOrderInfoDto.testFixtureNumber = "";
      }
      if (
        this.proOrderInfoDto.flyingProbe == "custstand" ||
        this.proOrderInfoDto.flyingProbe == "newstand" ||
        this.proOrderInfoDto.flyingProbe == "sharestand" ||
        this.proOrderInfoDto.flyingProbe == "teststand"
      ) {
        this.proOrderInfoDto.testFixtureNumber = this.proOrderInfoDto.orderNo;
        this.proOrderInfoDto.fpTestMethod = "";
      }
    },

    changesurface() {
      if (
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandhaslwithfree" ||
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandimmersiongold" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithfreeandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithleadandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "nickelplatingandgoldplatedfinger" ||
        this.proOrderInfoDto.surfaceFinish == "ospandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "wholeimmersiongoldandimmersiongoldfinger"
      ) {
        this.proOrderInfoDto.isGoldfinger = true;
      } else {
        this.proOrderInfoDto.isGoldfinger = false;
      }
    },
    changelayers() {
      this.$emit("changelayers");
    },
    getsolddata() {
      soldercolorresistinkrelation(this.$route.query.factory).then(res => {
        if (res.code) {
          this.SolderResistInk = res.data;
          this.solderColorC();
        } else {
          this.SolderResistInk1 = this.mapKey(this.selectData.SolderResistInk);
        }
      });
    },
    solderColorC(type, val) {
      if (this.proOrderInfoDto.solderColor && type == "change" && val != "bot") {
        this.proOrderInfoDto.solderColorBottom = this.proOrderInfoDto.solderColor;
      }
      let solder_color = "";
      if (this.proOrderInfoDto.solderColor == "none" || this.proOrderInfoDto.solderColor == "noResistance") {
        solder_color = this.proOrderInfoDto.solderColorBottom;
      } else {
        solder_color = this.proOrderInfoDto.solderColor;
      }
      this.SolderResistInk1 = [];
      let data = [];
      if (this.proOrderInfoDto.isInkNotHalogen) {
        data = this.SolderResistInk.filter(ite => ite.solderColor == solder_color && ite.hf == true);
      } else {
        data = this.SolderResistInk.filter(ite => ite.solderColor == solder_color);
      }
      if (data.length != 0) {
        for (let index = 0; index < data.length; index++) {
          this.selectData.SolderResistInk.forEach(item => {
            if (item.valueMember == data[index].solderResistInk) {
              this.SolderResistInk1.push({
                lable: item.text,
                value: data[index].solderResistInk,
              });
            }
          });
        }
        if (this.$route.query.factory == 12) {
          if (type == "change") {
            this.proOrderInfoDto.solderResistInk = this.SolderResistInk1[0].value;
          } else {
            this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk
              ? this.proOrderInfoDto.solderResistInk
              : this.SolderResistInk1[0].value;
          }
        } else if (this.$route.query.factory == 67) {
          if (type == "change") {
            this.proOrderInfoDto.solderResistInk = this.SolderResistInk1[0].value;
          } else {
            this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk ? this.proOrderInfoDto.solderResistInk : "";
          }
          this.SolderResistInk1 = this.mapKey(this.selectData.SolderResistInk);
        } else {
          if (type == "change") {
            this.proOrderInfoDto.solderResistInk = "";
          } else {
            this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk ? this.proOrderInfoDto.solderResistInk : "";
          }
        }
      } else {
        this.SolderResistInk1 = this.mapKey(this.selectData.SolderResistInk);
        if (type == "change") {
          this.proOrderInfoDto.solderResistInk = "";
        } else {
          this.proOrderInfoDto.solderResistInk = this.proOrderInfoDto.solderResistInk ? this.proOrderInfoDto.solderResistInk : "";
        }
      }
    },
    getfontdata() {
      fontcolorresistinkrelation(this.$route.query.factory).then(res => {
        if (res.code) {
          this.CharacterResistInk = res.data;
          this.fontColorC();
        } else {
          this.CharacterResistInk1 = this.mapKey(this.selectData.CharacterResistInk);
        }
      });
    },
    fontColorC(type, val) {
      if (this.proOrderInfoDto.fontColor && type == "change" && val != "bot") {
        this.proOrderInfoDto.fontColorBottom = this.proOrderInfoDto.fontColor;
      }
      let font_color = "";
      if (this.proOrderInfoDto.fontColor == "none" || this.proOrderInfoDto.fontColor == "noResistance") {
        font_color = this.proOrderInfoDto.fontColorBottom;
      } else {
        font_color = this.proOrderInfoDto.fontColor;
      }
      this.CharacterResistInk1 = [];
      let data = [];
      //未规范接口内字段 solderColor为对应的字符颜色value  solderResistInk为对应的字符油墨value
      if (this.$route.query.factory == 22) {
        if (this.proOrderInfoDto.characterNotHalogen) {
          data = this.CharacterResistInk.filter(ite => ite.solderColor == font_color && ite.hf == this.proOrderInfoDto.characterNotHalogen);
        } else {
          data = this.CharacterResistInk.filter(ite => ite.solderColor == font_color);
        }
      } else {
        data = this.CharacterResistInk.filter(ite => ite.solderColor == font_color && ite.hf == this.proOrderInfoDto.characterNotHalogen);
      }
      if (data.length != 0) {
        for (let index = 0; index < data.length; index++) {
          this.selectData.CharacterResistInk.forEach(item => {
            if (item.valueMember == data[index].solderResistInk) {
              this.CharacterResistInk1.push({
                lable: item.text,
                value: data[index].solderResistInk,
              });
            }
          });
        }
        if (this.$route.query.factory == 12) {
          if (type == "change") {
            this.proOrderInfoDto.characterResistInk = this.CharacterResistInk1[0].value;
          } else {
            this.proOrderInfoDto.characterResistInk = this.proOrderInfoDto.characterResistInk
              ? this.proOrderInfoDto.characterResistInk
              : this.CharacterResistInk1[0].value;
          }
        } else if (this.$route.query.factory == 67) {
          if (type == "change") {
            this.proOrderInfoDto.characterResistInk = this.CharacterResistInk1[0].value;
          } else {
            this.proOrderInfoDto.characterResistInk = this.proOrderInfoDto.characterResistInk ? this.proOrderInfoDto.characterResistInk : "";
          }
          this.CharacterResistInk1 = this.mapKey(this.selectData.CharacterResistInk);
        } else {
          if (type == "change") {
            this.proOrderInfoDto.characterResistInk = "";
          } else {
            this.proOrderInfoDto.characterResistInk = this.proOrderInfoDto.characterResistInk ? this.proOrderInfoDto.characterResistInk : "";
          }
        }
      } else {
        this.CharacterResistInk1 = this.mapKey(this.selectData.CharacterResistInk);
        if (type == "change") {
          this.proOrderInfoDto.characterResistInk = "";
        } else {
          this.proOrderInfoDto.characterResistInk = this.proOrderInfoDto.characterResistInk ? this.proOrderInfoDto.characterResistInk : "";
        }
      }
    },
    changesu() {
      if (this.proOrderInfoDto.pinBanType1 && this.proOrderInfoDto.pinBanType2) {
        this.proOrderInfoDto.su = (Number(this.proOrderInfoDto.pinBanType1) * this.proOrderInfoDto.pinBanType2).toFixed();
      } else {
        this.proOrderInfoDto.su = null;
      }
    },
    optionClick() {},
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate2(value, list) {
      this.proOrderInfoDto.innerCopperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.innerCopperThickness, list, value);
    },
    handleSearch2(value, list) {
      this.setEstimate2(value, list);
    },
    handleBlur2(value, list) {
      this.setEstimate2(value, list);
      if (this.proOrderInfoDto.innerCopperThickness) {
        var innerCopperThickness = this.proOrderInfoDto.innerCopperThickness.split("");
        if (innerCopperThickness.length > 3) {
          innerCopperThickness = innerCopperThickness.slice(0, 3);
          this.$message.warning("成品铜厚内层不能超过3个字符");
        }
        this.proOrderInfoDto.innerCopperThickness = innerCopperThickness.join("");
      }
    },
    setEstimate3(value, list) {
      this.proOrderInfoDto.copperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.copperThickness, list, value);
    },
    handleSearch3(value, list) {
      this.setEstimate3(value, list);
    },
    handleBlur3(value, list) {
      this.setEstimate3(value, list);
      if (this.proOrderInfoDto.copperThickness) {
        var copperThickness = this.proOrderInfoDto.copperThickness.toString().split("");
        if (copperThickness.length > 3) {
          copperThickness = copperThickness.slice(0, 3);
          this.$message.warning("成品铜厚外层不能超过3个字符");
        }
        this.proOrderInfoDto.copperThickness = copperThickness.join("");
      }
    },
    setEstimate4(value, list) {
      this.proOrderInfoDto.warpage = value;
      let a = this.getPrice(this.proOrderInfoDto.warpage, list, value);
    },
    handleSearch4(value, list) {
      this.setEstimate4(value, list);
    },
    handleBlur4(value, list) {
      this.setEstimate4(value, list);
      if (this.proOrderInfoDto.warpage) {
        var warpage = this.proOrderInfoDto.warpage.split("");
        if (warpage.length > 6) {
          warpage = warpage.slice(0, 6);
          this.$message.warning("翘曲度不能超过6个字符");
        }
        this.proOrderInfoDto.warpage = warpage.join("");
      }
    },
    setEstimate5(value, list) {
      this.proOrderInfoDto.vcutSurplusThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.vcutSurplusThickness, list, value);
    },
    handleSearch5(value, list) {
      this.setEstimate5(value, list);
    },
    handleBlur5(value, list) {
      this.setEstimate5(value, list);
    },
    setEstimate6(value, list) {
      this.proOrderInfoDto.minSolderBridge = value;
      let a = this.getPrice(this.proOrderInfoDto.minSolderBridge, list, value);
    },
    handleSearch6(value, list) {
      this.setEstimate6(value, list);
    },
    handleBlur6(value, list) {
      this.setEstimate6(value, list);
    },
    setEstimate7(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness, list, value);
    },
    handleSearch7(value, list) {
      this.setEstimate7(value, list);
    },
    handleBlur7(value, list) {
      this.setEstimate7(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness) {
        var cjNickelThinckness = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness.split("");
        if (cjNickelThinckness.length > 10) {
          cjNickelThinckness = cjNickelThinckness.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = cjNickelThinckness.join("");
      }
    },
    setEstimate8(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2, list, value);
    },
    handleSearch8(value, list) {
      this.setEstimate8(value, list);
    },
    handleBlur8(value, list) {
      this.setEstimate8(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2) {
        var cjNickelThinckness2 = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2.split("");
        if (cjNickelThinckness2.length > 10) {
          cjNickelThinckness2 = cjNickelThinckness2.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = cjNickelThinckness2.join("");
      }
    },
    setEstimate9(value, list) {
      this.proOrderInfoDto.boardThicknessTol = value;
      let a = this.getPrice(this.proOrderInfoDto.boardThicknessTol, list, value);
    },
    handleSearch9(value, list) {
      this.setEstimate9(value, list);
    },
    handleBlur9(value, list) {
      this.setEstimate9(value, list);
    },
    setEstimate10(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = value;
    },
    handleSearch10(value, list) {
      this.setEstimate10(value, list);
    },
    handleBlur10(value, list) {
      this.setEstimate10(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness) {
        var newTinThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness.split("");
        if (newTinThickness.length > 10) {
          newTinThickness = newTinThickness.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = newTinThickness.join("");
      }
    },
    setEstimate11(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = value;
    },
    handleSearch11(value, list) {
      this.setEstimate11(value, list);
    },
    handleBlur11(value, list) {
      this.setEstimate11(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2) {
        var newTinThickness2 = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2.split("");
        if (newTinThickness2.length > 10) {
          newTinThickness2 = newTinThickness2.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = newTinThickness2.join("");
      }
    },
  },
};
</script>
<style scoped lang="less">
.box {
  /deep/.ant-select-dropdown--single {
    min-width: 90px;
  }
}
/deep/.ant-col-20 {
  width: 1291px;
}
// /deep/.ant-row {
//   width: 1300px;
// }
/deep/.ant-col-4 {
  width: 258px;
}
/deep/.ant-col-3 {
  width: 194px;
}
/deep/.ant-col-8 {
  width: 516px;
}
/deep/.ant-col-9 {
  width: 582px;
}
.div1 {
  /deep/.ant-form-item-control {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.div2 {
  .div22 {
    /deep/.ant-form-item-control {
      padding: 0;
      min-height: 28px !important;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}

.autoo {
  /deep/.ant-form-item-control {
    height: 100% !important;
    width: 1184px;
  }
  /deep/.ant-form-item-control {
    // height: auto!important;
    background: #f5f5f5 !important;
    .ant-input {
      margin-top: 4px !important;
      margin-bottom: 0 !important;
    }
  }
}
/deep/textarea.ant-input {
  min-height: 24px;
  line-height: 1.3;
}
.speclass {
  /deep/ .ant-form-item-label {
    width: 107px;
  }
  /deep/ .ant-form-item-label > label {
    font-size: 13px !important;
  }
  /deep/ .ant-form-item-control-wrapper {
    width: 1178px;
  }
}
/deep/ .div1 {
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px !important;
    }
  }
  .ant-form-item-children {
    overflow: inherit !important;
    font-size: 13px;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  /* border-color: #ff9900; */
  border: 2px solid red;
}
/deep/.ant-select-focused .ant-select-selection {
  border: 2px solid red;
}
/deep/.ant-input:focus {
  border: 2px solid red;
}
/deep/.colSTY {
  border-left: 1px solid #ddd;
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px;
    }
  }
}
// /deep/.ant-col{
//   border-left:1px solid #ddd;
// }
/deep/.sss {
  height: 30px;
}
/deep/.ant-col-3 {
  .ant-col-14 {
    width: 58.5%;
  }
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-input {
  padding: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 11px !important;
  margin-left: 6px !important;
}

/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
// /deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
//     height: 16px!important;
//     margin-top: 3px;
//     line-height: 14px!important;
//   }
/deep/.surSTY {
  // height:56px;
  .ant-form-item-control-wrapper {
    // min-height:20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
      }
    }
  }
}
/deep/.heightSty {
  height: 28px;
  .ant-select {
    margin-top: 2px;
  }
  .ant-select-selection--multiple {
    height: 20px;
    min-height: 20px;
  }
  .ant-select-allow-clear {
    .ant-select-selection--multiple {
      height: 23px;
    }
    .ant-select-selection__rendered {
      ul {
        display: flex;
        li {
          height: 16px;
          margin-top: 2px;
          line-height: 16px;
          user-select: none;
          padding-left: 0 !important;
        }
      }
    }
    .ant-select-selection__clear {
      top: 13px;
    }
  }
}
/deep/.SolderSty {
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
        // .edit{
        //  line-height: 28px!important;
        // text-overflow: unset!important;
        // white-space: unset!important;
        // }
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        width: 100%;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 17px !important;
            margin-top: 3px;
            line-height: 15px !important;
          }
          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}

/deep/.ant-select-dropdown-menu-item {
  font-size: 14px;
  padding: 0.5%;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-size: 14px !important;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-size: 14px !important;
  font-weight: 500;

  color: #000000;
}

.ant-row {
  .ant-col-22 {
    .ant-form-item-control {
      .ant-input-affix-wrapper {
        line-height: 29px;
      }
    }
  }
  .ant-col-17 {
    .ant-form-item {
      /deep/.ant-input {
        min-height: 23px !important;
        height: 23px !important;
        line-height: 15px !important;
      }
    }
  }
  .ant-col-24 {
    /deep/.ant-form-item-label {
      width: 106px !important;
    }
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.disable {
  background: #f5f5f5 !important;
}
.box {
  overflow: auto;
  border-left: 1px solid rgb(233 230 230);
  position: relative;
  .bto {
    position: absolute;
    bottom: -48px;
    right: 374px;
  }
}

/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;
  min-height: 28px;
  .tmp1 {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 370px;
    display: inline-block;
  }
  .tmp {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 140px;
    display: inline-block;
  }
  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 25px;
    .ant-select {
      width: 20;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #f1f1f1;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        // display: block;
        // min-height: 25px;
        line-height: 25px;
        // // vertical-align: top;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
        // width: 100%;
        // display: inline-block;

        // .ant-select-allow-clear{
        //   // .ant-select-selection--multiple{
        //   //   height: 23px;
        //   //   margin-top:2px;
        //   // }
        //   .ant-select-selection__rendered{
        //     ul{
        //       display: flex;
        //       li{
        //         margin-top:-1px;
        //       }
        //     }
        //     .ant-select-selection__choice{
        //       height: 18px;
        //       margin-top: 2px;
        //       line-height: 14px;
        //       user-select: none;
        //     }
        //   }
        //   .ant-select-selection__clear{
        //     top:11px;
        //   }
        // }
        .ant-checkbox-wrapper {
          min-height: 28px;
        }
        .ant-select-selection--single {
          height: 22px;
        }
        .ant-select-selection__rendered {
          line-height: 18px;
        }
        .ant-select {
          height: 22px;
        }
        .ant-input {
          height: 22px;
          padding-top: 2.6px;
        }
      }
      line-height: inherit;
      padding: 0px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      height: 28px;
    }
  }
}
.div2 {
  /deep/ .ant-form-item {
    margin: 0;
    width: 100%;
    display: flex;
    min-height: 28px;
    .tmp1 {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 370px;
      display: inline-block;
    }
    .tmp {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 140px;
      display: inline-block;
    }
    .editWrapper {
      display: flex;
      align-items: center;
      min-height: 25px;
      .ant-select {
        width: 20;
      }
      .ant-input {
        width: 120px;
      }
      .ant-input-number {
        width: 120px;
      }
    }
    .ant-form-item-label {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      color: #666;
      background-color: #f1f1f1;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      label {
        font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      }
    }
    .ant-form-item-control-wrapper {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      .ant-form-item-control {
        .ant-form-item-children {
          // display: block;
          // min-height: 25px;
          line-height: 25px;
          // // vertical-align: top;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          // overflow: hidden;
          // width: 100%;
          // display: inline-block;

          // .ant-select-allow-clear{
          //   // .ant-select-selection--multiple{
          //   //   height: 23px;
          //   //   margin-top:2px;
          //   // }
          //   .ant-select-selection__rendered{
          //     ul{
          //       display: flex;
          //       li{
          //         margin-top:-1px;
          //       }
          //     }
          //     .ant-select-selection__choice{
          //       height: 18px;
          //       margin-top: 2px;
          //       line-height: 14px;
          //       user-select: none;
          //     }
          //   }
          //   .ant-select-selection__clear{
          //     top:11px;
          //   }
          // }
          .ant-checkbox-wrapper {
            min-height: 28px;
          }
          .ant-select-selection--single {
            height: 22px;
          }
          .ant-select-selection__rendered {
            line-height: 18px;
          }
          .ant-select {
            height: 22px;
          }
          .ant-input {
            height: 22px;
            padding-top: 2.6px;
          }
        }
        line-height: inherit;
        padding: 0px 5px;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        height: auto !important;
      }
    }
  }
}
</style>
