<!-- 车间管理-AOI管理-涨缩登记 -->
<template>
  <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 12 }" :form="harmomeForm">
    <a-form-item label="X">
      <a-input type="number" v-model="harmomeForm.valuex" />
    </a-form-item>

    <a-form-item label="Y">
      <a-input type="number" v-model="harmomeForm.valuey" />
    </a-form-item>
  </a-form>
</template>

<script>
export default {
  name: "HarmomegathusRegister",
  data() {
    return {
      harmomeForm: {
        valuex: "",
        valuey: "",
      },
    };
  },
  methods: {},
};
</script>
