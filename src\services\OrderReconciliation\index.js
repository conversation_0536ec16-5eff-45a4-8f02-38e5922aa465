import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
import { param } from 'jquery'
export async function ordercheckmannegepagelist(params) {
    return request( `/api/app/e-mSPcb-check-management/order-check-mannege-page-list`, METHOD.GET,params)
}
export async function batchcheckaudit(ids) {
    return request( `/api/app/e-mSPcb-check-management/batch-check-audit`, METHOD.POST,ids)
}
export async function batchorderaudit(ids) {
    return request( `/api/app/e-mSPcb-check-management/batch-order-audit`, METHOD.POST,ids)
}
export async function exportcheckstatement(ids) {
    return request( `/api/app/e-mSPcb-check-management/export-check-report`, METHOD.POST,ids)
}
export async function downloadcontract(ids) {
    return request( `/api/app/e-mSPcb-check-management/download-contract`, METHOD.POST,ids)
}
export default {
    ordercheckmannegepagelist,
    batchcheckaudit,
    exportcheckstatement,
    batchorderaudit,
    downloadcontract
}
