<template>
  <a-card style="height: 100%; background-color: white">
    <div style="padding: 12px">
      <span>工厂名称:</span>
      <a-input v-model="queryParam.company" placeholder="工厂名称" @keyup.enter="refresh" style="width: 250px; margin: 0 12px" />
      <a-button type="primary" @click="refresh">{{ $t("search_button") }} </a-button>
      <a-button style="margin-left: 12px" @click="returnBtn">{{ $t("reset_button") }}</a-button>
      <a-button type="primary" style="float: right" @click="$refs.addModel.openModal({})">新增</a-button>
    </div>
    <a-table
      :class="dataSource.length ? 'main-table' : ''"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      @change="handleTableChange"
      :loading="loading"
      :scroll="{ y: 700, x: 1650 }"
      :rowClassName="isRedRow"
    >
      <span slot="action" slot-scope="record">
        <a href="javascript:;" @click="$refs.addModel.openModal(record)">{{ $t("edit_button") }}</a>
        <a-divider type="vertical" />
        <a-popconfirm title="确定要删除吗？" @confirm="deleteFactPage(record.id)">
          <a href="javascript:;">{{ $t("delete_button") }}</a>
        </a-popconfirm>
      </span>
    </a-table>
    <add-model ref="addModel" @ok="getMarketData"></add-model>
  </a-card>
</template>

<script>
import addModel from "./components/addModel.vue";
import { getSelectFactPage, deleteFactPage } from "@/services/supplier/index";
let columns = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "工厂名称",
    dataIndex: "company",
    width: 220,
  },
  {
    title: "创建人",
    dataIndex: "createUserName",
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
  },
  {
    title: "更新人",
    dataIndex: "updateUserName",
  },
  {
    title: "操作",
    key: "action",
    scopedSlots: { customRender: "action" },
  },
];
export default {
  name: "",
  i18n: require("@/components/language/common/buttoni18n.js"),
  data() {
    return {
      queryParam: {
        company: "",
      },
      loading: false,
      disabled: true,
      colNumber: 3,
      columns: columns,
      dataSource: [],
      selectedRowKeys: [],
      rootSubmenuKeys: ["sub1", "sub2", "sub4"],
      openKeys: ["0"],
      pagination: {
        pageSize: 20,
        pageIndex: 1,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"],
        total: 0,
        showTotal: total => `总计 ${total} 条`,
      },
      menuData: [
        {
          name: "分类",
          id: "0",
          childer: [
            {
              name: "制程能力",
              id: "01",
            },
            {
              name: "主要客户",
              id: "02",
              childer: [],
            },
            {
              name: "生产设备",
              id: "03",
              childer: [],
            },
            {
              name: "体系认证",
              id: "04",
              childer: [],
            },
            {
              name: "工厂图库",
              id: "05",
              childer: [],
            },
            {
              name: "评估结果",
              id: "06",
              childer: [],
            },
            {
              name: "板料物料",
              id: "07",
              childer: [],
            },
            {
              name: "摆放日志",
              id: "08",
              childer: [],
            },
          ],
        },
      ],
      groupsData: [],
      trIndex: null,
    };
  },
  created() {
    this.getMarketData();
  },
  components: { addModel },
  methods: {
    returnBtn() {
      this.queryParam = {};
      this.pagination.current = 1;
      this.getMarketData();
    },
    //获取数据
    isRedRow(record, index) {
      return index % 2 === 0 ? "table-striped" : "";
    },
    getMarketData() {
      let data = {
        company: this.queryParam.company,
        ...this.pagination,
      };
      this.loading = true;
      getSelectFactPage(data)
        .then(res => {
          if (res.success) {
            res.data.items.forEach(e => {
              if (e.updateTime) {
                e.updateTime = e.updateTime.substring(0, 10);
              }
              if (e.createTime) {
                e.createTime = e.createTime.substring(0, 10);
              }
            });
            this.pagination.total = res.data.totalCount;
            this.dataSource = res.data.items;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //查询详情
    toDetail(data, type) {
      this.$router.push({ path: "/shichang/detail", query: { id: data.guid_, type: type } });
    },
    handleTableChange(pagination, filters, sorter) {
      console.log(pagination);
      this.pagination.pageIndex = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.getMarketData();
    },
    onOpenChange(openKeys) {
      const latestOpenKey = openKeys.find(key => this.openKeys.indexOf(key) === -1);
      if (this.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
        this.openKeys = openKeys;
      } else {
        this.openKeys = latestOpenKey ? [latestOpenKey] : [];
      }
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    //删除
    deleteFactPage(id) {
      let params = {
        key: id,
      };
      deleteFactPage(params).then(res => {
        if (res.success) {
          this.$message.info("删除成功");
        }
        this.getMarketData();
      });
    },
    refresh() {
      if (!this.queryParam.company.trim()) {
        this.$message.error("请输入查询条件");
        return;
      }
      this.pagination.current = 1;
      this.getMarketData();
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .table-striped {
  background-color: #f8f8f8; /* 斑马纹的颜色 */
}
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/ .ant-form-horizontal {
  height: 42px;
}
/deep/ .ant-table-pagination {
  float: left;
  margin: 6px;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-tbody > tr td {
  height: 19px;
  padding: 7px;
}
/deep/ .ant-table-ant-table-fixed-header {
  height: 21px;
  padding: 7px;
}
/deep/ .ant-card-body {
  padding: 0px;
}
.rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 7px;
  overflow-wrap: break-word;
}
/deep/ .ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
.main-table {
  /deep/ .ant-table-body {
    min-height: 700px;
  }
}
</style>
