import { request, METHOD } from '@/utils/request';
// 客户代码列表
export function custRuleOrderList (params) {
    return request(`/api/app/e-mSTMkt-cust-rule/get-cust-rule-order-list`, METHOD.GET,params)
}
// 获取客户规则信息信息
export function ruleShowInfo (CustNo,factory,type) {
    return request(`/api/app/order-pre-button/rule-show-info?CustNo=${CustNo}&factory=${factory}&type=${type}`, METHOD.GET)
}
// 客户规则录入
export function attInfo (Id) {
    return request(`/api/app/e-mSTMkt-cust-rule/att-info/${Id}`, METHOD.GET,)
}
// 新增
export function saveNew (params,tradeType) {
    return request(`/api/app/e-mSTMkt-cust-rule/save-new`, METHOD.POST,params,tradeType)
}
// 编辑
export function repait (params) {
    return request(`/api/app/e-mSTMkt-cust-rule/repait`, METHOD.POST,params)
}
// 获取下拉选择数据
export function ClassList (params) {
    return request(`/api/app/e-mSData-class-list/get-class-list`, METHOD.POST,params)
}
export function custClassList () {
    return request(`/api/app/e-mSTMkt-cust-rule/cust-class-list`, METHOD.GET,)
}
export function custClassListv2 () {
    return request(`/api/app/e-mSTMkt-cust-rule/cust-class-list-v2`, METHOD.GET,)
}
// 下拉子项
export function custClassItemList (Pid) {
    return request(`/api/app/e-mSTMkt-cust-rule/cust-class-item-list?Pid=${Pid}`, METHOD.GET,)
}
// 删除
export function custRule () {
    return request(`/api/app/e-mSTMkt-cust-rule`, METHOD.GET,)
}
// 添加附件
export function attFile (Id,params) {
    return request(`/api/app/e-mSTMkt-cust-rule/up-load-cust-rule-att-file/${Id}`, METHOD.POST,params)
}
// 删除附件
export function delFile (Id) {
    return request(`/api/app/e-mSTMkt-cust-rule/delete-att/${Id}`, METHOD.POST,)
}
// 子项参数列表
export function ruleItemsList (CustNo,TradeType) {
    return request(`/api/app/e-mSTMkt-cust-rule/rule-items-list?CustNo=${CustNo}&TradeType=${TradeType}`, METHOD.GET,)
}
// 子项参数修改保存
export function setRuleItemsList (params) {
    return request(`/api/app/e-mSTMkt-cust-rule/set-rule-items-list`, METHOD.POST,params)
}
//删除失效客户规则
export function deletecustrule (params) {
    return request(`/api/app/e-mSTMkt-cust-rule/delete-cust-rule`, METHOD.POST,params)
}

export default {
    custRuleOrderList,
    ruleShowInfo,
    attInfo,
    saveNew,
    repait,
    custRule,
    attFile,
    delFile,
    ClassList,
    custClassList,
}
