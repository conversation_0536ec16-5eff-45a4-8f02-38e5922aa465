<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1">
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ltreport0849" style=" padding: 25px;color: black;font-family: 等线;font-size: 12px;" >
            <div style="width: 100%;text-align: center;">{{ date }}</div> 
            <div style="padding-top: 15px;">
                <table style="width: 100%;" border="1">
                    <tr>
                        <td colspan="24" style="text-align: center;font-size: 20px;font-weight: bolder;"> 湖北龙腾电子科技股份有限公司报价单</td>
                        <td></td>
                    </tr>
                    <tr>
                       <td>需方:</td> 
                       <td colspan="11">{{ LTreportdata.date_ }}</td>
                       <td colspan="12">供方：{{ LTreportdata.nO1_ }}</td>
                       <td></td>
                    </tr>
                    <tr>
                        <td>联系人</td>
                        <td colspan="2">{{ LTreportdata.orderNo_}}</td>
                        <td colspan="9">联系人：{{ LTreportdata.factory_ }}</td>
                        <td colspan="3">联系人:{{ LTreportdata.nO2_  }}</td>
                        <td colspan="9">联系电话：{{ LTreportdata.currency_}}</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>报价类型：</td>
                        <td colspan="23">
                            <a-checkbox-group  v-model="LTreportdata.address_">
                                <a-checkbox  disabled >
                                    <span style="color:black">研发</span>
                                </a-checkbox>
                                <a-checkbox  disabled>
                                    <span style="color:black">量产</span>
                                </a-checkbox>
                            </a-checkbox-group>
                        </td>
                        <td></td>
                    </tr>
                    <tr>
                        <td rowspan="3">No.</td>
                        <td rowspan="3">料号</td>
                        <td rowspan="3">规格型号</td>
                        <td colspan="9" style="text-align: center;background: #bdd7ee;">加工工艺</td>
                        <td rowspan="3"  style="background: #bdd7ee;">订单数量</td>
                        <td rowspan="3"  style="background: #ffc000;">基材单价/㎡</td>
                        <td rowspan="3"  style="background: #ffc000;">加工费用/㎡</td>
                        <td rowspan="3"  style="background: #ffc000;">辅料费用/㎡</td>
                        <td rowspan="3"  style="background: #ffc000;">损耗/㎡</td>
                        <td rowspan="3"  style="background: #ffc000;">管销/㎡</td>
                        <td rowspan="3"  style="background: #ffc000;">利润/㎡</td>
                        <td rowspan="3"  style="color: red;">含税单价</td>
                        <td rowspan="3">未税单价</td>
                        <td rowspan="3"  style="background: #ffc000;">工程费用</td>
                        <td rowspan="3">未税总价</td>
                        <td rowspan="3">最小起订量(MOQ)</td>
                        <!-- <td rowspan="3">总面积数(㎡)</td> -->
                        <td rowspan="3">备注</td>
                    </tr>
                    <tr>
                        <td colspan="2"  rowspan="2" style="text-align: center;background: #bdd7ee;">交货尺寸<br/>(长mm*宽mm)</td>
                        <td rowspan="2" style="background: #bdd7ee;">拼板数量</td>
                        <td rowspan="2" style="background: #bdd7ee;">单只尺寸</td>
                        <td rowspan="2" style="background: #bdd7ee;">层数</td>
                        <td rowspan="2" style="background: #bdd7ee;">完成板厚</td>
                        <td colspan="2" style="background: #bdd7ee;">基铜厚</td>
                        <td rowspan="2" style="background: #bdd7ee;">表面处理</td>
                    </tr>
                    <tr>
                        <td style="background: #bdd7ee;">内</td>
                        <td style="background: #bdd7ee;">外</td>
                    </tr>
                    <tr v-for="(item,index) in LTreportdata.price" :key="index">
                        <td>{{ index+1 }}</td>
                        <td>{{ item.bType }}</td>
                        <td>{{ item.custName }}</td>
                        <td>{{ item.pinBanType }}</td>
                        <td>{{ item.qty }}</td>
                        <td>{{ item.eng }}</td>
                        <td>{{ item.boardBrand }}</td>
                        <td>{{ item.lay }}</td>
                        <td>{{ item.surf }}</td>
                        <td>{{ item.setQty }}</td>
                        <td>{{ item.diff }}</td>
                        <td>{{ item.solder }}</td>
                        <td>{{ item.film}}</td>
                        <td>{{ item.boardThickness }}</td>
                        <td>{{ item.oucu }}</td>
                        <td>{{ item.size }}</td>
                        <td>{{ item.su }}</td>
                        <td>{{ item.mat }}</td>
                        <td>{{ item.area }}</td>
                        <td>{{ item.poreDensity }}</td>
                        <td>{{ item.plate }}</td>
                        <td>{{ item.surface }}</td>
                        <td>{{ item.hpPrice }}</td>
                        <td>{{ item.zHPrice }}</td>
                        <td>{{ item.notes }}</td>
                        <!-- <td>{{ item.notes }}</td>
                        <td>{{ item.notes }}</td> -->
                    </tr>
                    <tr>
                        <td>合计</td>
                        <td colspan="24"></td>
                    </tr>
                    <tr>
                        <td>币种未税</td>
                        <td colspan="24">
                            <a-checkbox-group v-model="LTreportdata.noteSure">
                                <a-checkbox  disabled>
                                    <span style="color:black">RMB</span>
                                </a-checkbox>
                                <a-checkbox  disabled>
                                    <span style="color:black">HKD</span>
                                </a-checkbox>
                                <a-checkbox  disabled>
                                    <span style="color:black">USD</span>
                                </a-checkbox>
                            </a-checkbox-group>
                        </td>
                    </tr>
                    <tr>
                        <td>结算方式</td>
                        <td colspan="24">
                            <a-checkbox-group v-model="LTreportdata.defend">
                                <a-checkbox  disabled>
                                    <span style="color:black">月结30</span>
                                </a-checkbox>
                                <a-checkbox  disabled>
                                    <span style="color:black">月结60天</span>
                                </a-checkbox>
                                <a-checkbox  disabled>
                                    <span style="color:black">月结90天</span>
                                </a-checkbox>
                            </a-checkbox-group>
                        </td>
                    </tr>
                    <tr>
                        <td>备注</td>
                        <td colspan="24">以上报价有效期30天。</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
  </template>
  <script>
  import moment from 'moment';
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        date: moment().format("YYYY-MM-DD"),
        printObj1:{
            id: "ltreport0849", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
        textdata:[{},{},],
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            htmlToPdfa3('ltreport0849',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
  span{
    font-size: 12px;
  }
    table > tr > td {
       padding:3px 5px;
       border: 1px solid black;
    }
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
  .pdfDom1{
      height: 650px;
      overflow: auto;
  }
  </style>
