<!-- 市场管理-订单详情基本信息  -->
<template>
  <div class="contentInfo" ref="SelectBox">
    <a-form-model layout="inline" id="formDataElem" v-show="editFlag">
      <a-col :span="3" class="line3">
        <a-form-model-item
          label="盘中孔"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isDiscHole && iseval(requiredLinkConfigList.isDiscHole.isNullRules) && required ? 'require' : ''"
          v-show="show2 || showMore"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isDiscHole" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="半边孔边数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.halfSthNum && iseval(requiredLinkConfigList.halfSthNum.isNullRules) && required ? 'require' : ''"
          v-show="show2 || showMore"
        >
          <div class="editWrapper">
            <a-select v-model="formData.halfSthNum" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.HalfSthNum)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="异形孔孔数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.profileHoleNum && iseval(requiredLinkConfigList.profileHoleNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.profileHoleNum" :min="1" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="板边包金边数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.intPlateEdge && iseval(requiredLinkConfigList.intPlateEdge.isNullRules) && required ? 'require' : ''"
          v-show="formData.boardLayers > 0"
        >
          <div class="editWrapper">
            <a-select v-model="formData.intPlateEdge" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.IntPlateEdge)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="金属铣槽"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isMetalSlot && iseval(requiredLinkConfigList.isMetalSlot.isNullRules) && required ? 'require' : ''"
          v-show="show2 || showMore"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isMetalSlot" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="沉孔数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.stepHoleNum && iseval(requiredLinkConfigList.stepHoleNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.stepHoleNum" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="序列号个数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.serialNumber && iseval(requiredLinkConfigList.serialNumber.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.serialNumber" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="蓝胶"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isBlueGum && iseval(requiredLinkConfigList.isBlueGum.isNullRules) && required ? 'require' : ''"
          v-show="!show0"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isBlueGum" @change="formData.blueGumPercentage = null" />
            <a-input allowClear style="margin-left: 6px" v-model="formData.blueGumPercentage" :disabled="formData.isBlueGum == false" />%
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="金手指面积(%)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.goldenFingerAreaRe && iseval(requiredLinkConfigList.goldenFingerAreaRe.isNullRules) && required ? 'require' : ''
          "
          v-show="!show0"
        >
          <div class="editWrapper">
            <span> <a-input v-model="formData.goldenFingerAreaRe" allowClear /></span>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="金手指条数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.goldenFingerArea && iseval(requiredLinkConfigList.goldenFingerArea.isNullRules) && required ? 'require' : ''"
          v-show="!show0"
        >
          <div class="editWrapper">
            <span> <a-input v-model="formData.goldenFingerArea" allowClear /></span>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label='金手指金厚U"'
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.goldfingerThickness && iseval(requiredLinkConfigList.goldfingerThickness.isNullRules) && required ? 'require' : ''
          "
          v-show="!show0"
        >
          <div class="editWrapper">
            <span> <a-input v-model="formData.goldfingerThickness" allowClear /></span>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label='金手指镍厚U"'
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.goldfingerNieThickness && iseval(requiredLinkConfigList.goldfingerNieThickness.isNullRules) && required
              ? 'require'
              : ''
          "
          v-show="!show0"
        >
          <div class="editWrapper">
            <span> <a-input v-model="formData.goldfingerNieThickness" allowClear /></span>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="金手指尺寸"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.goldfingerSize && iseval(requiredLinkConfigList.goldfingerSize.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <span> <a-input v-model="formData.goldfingerSize" allowClear /></span>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="金手指合拼款数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.goldfingerHpNum && iseval(requiredLinkConfigList.goldfingerHpNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.goldfingerHpNum" allowClear />
          </div>
        </a-form-model-item>
      </a-col>
      <a-col :span="3" class="line3">
        <a-form-model-item
          label="压接孔"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isCrimpHole && iseval(requiredLinkConfigList.isCrimpHole.isNullRules) && required ? 'require' : ''"
          v-show="!show0"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isCrimpHole" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="背钻孔次数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.backDrillNum && iseval(requiredLinkConfigList.backDrillNum.isNullRules) && required ? 'require' : ''"
          v-show="showMore"
        >
          <div class="editWrapper">
            <a-input v-model="formData.backDrillNum" :min="1" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="通孔控深次数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.throughHoleNum && iseval(requiredLinkConfigList.throughHoleNum.isNullRules) && required ? 'require' : ''"
          v-show="!show0"
        >
          <div class="editWrapper">
            <a-select
              v-model="formData.throughHoleNum"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.ThroughHoleNum)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="控深深度(mm)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.depthControl && iseval(requiredLinkConfigList.depthControl.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.depthControl" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="盲孔控深次数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.ctrlBlindHole && iseval(requiredLinkConfigList.ctrlBlindHole.isNullRules) && required ? 'require' : ''"
          v-show="showHDI || showMM"
        >
          <div class="editWrapper">
            <a-select v-model="formData.ctrlBlindHole" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option
                v-for="(item, index) in mapKey(selectOption.CtrlBlindHole)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="沉孔角度"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.countersinkAngle && iseval(requiredLinkConfigList.countersinkAngle.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.countersinkAngle" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="阶梯孔孔数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.steppedHoleNum && iseval(requiredLinkConfigList.steppedHoleNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input v-model="formData.steppedHoleNum" allowClear />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="碳油"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.isCarbonOil && iseval(requiredLinkConfigList.isCarbonOil.isNullRules) && required ? 'require' : ''"
          v-show="!show0"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.isCarbonOil" @change="formData.carbonOilPercentage == ''" />
            <a-input allowClear style="margin-left: 6px" v-model="formData.carbonOilPercentage" :disabled="formData.isCarbonOil == false" />%
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="高温胶(处)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.pasteRedTape && iseval(requiredLinkConfigList.pasteRedTape.isNullRules) && required ? 'require' : ''"
          v-show="!show0"
        >
          <div class="editWrapper">
            <a-input allowClear v-model="formData.pasteRedTape" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="内斜边刀数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.innerBevelNum && iseval(requiredLinkConfigList.innerBevelNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input allowClear v-model="formData.innerBevelNum" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="常规斜边刀数"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="
            requiredLinkConfigList.conventionBevelNum && iseval(requiredLinkConfigList.conventionBevelNum.isNullRules) && required ? 'require' : ''
          "
        >
          <div class="editWrapper">
            <a-input allowClear v-model="formData.conventionBevelNum" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="盲槽(个数)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.blindSlotNum && iseval(requiredLinkConfigList.blindSlotNum.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input allowClear v-model="formData.blindSlotNum" @change="$emit('cutchange')" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="控深面积(m/㎡)"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.depthControlArea && iseval(requiredLinkConfigList.depthControlArea.isNullRules) && required ? 'require' : ''"
        >
          <div class="editWrapper">
            <a-input allowClear v-model="formData.depthControlArea" />
          </div>
        </a-form-model-item>
        <a-form-model-item
          label="埋铜"
          :label-col="{ span: 12 }"
          :wrapper-col="{ span: 12 }"
          :class="requiredLinkConfigList.buriedCopper && iseval(requiredLinkConfigList.buriedCopper.isNullRules) && required ? 'require' : ''"
          v-show="showMore"
        >
          <div class="editWrapper">
            <a-checkbox v-model="formData.buriedCopper" @change="$emit('cutchange')" />
          </div>
        </a-form-model-item>
      </a-col>
      <five-mkt
        @cutchange="$emit('cutchange')"
        :spinning="spinning"
        :editFlag="editFlag"
        :showData="showData"
        :selectOption="selectOption"
        :boardBrandList="boardBrandList"
        :sheetTraderList="sheetTraderList"
        :boardtgList="boardtgList"
        :joinFacId="joinFacId"
        :ManufacturerTG="ManufacturerTG"
        :show0="show0"
        :show1="show1"
        :show2="show2"
        :showMore="showMore"
        :showCG="showCG"
        :showHDI="showHDI"
        :showMM="showMM"
        :reOrder="reOrder"
        :required="required"
        :frontDataZSupplierf="frontDataZSupplierf"
        :requiredLinkConfigList="requiredLinkConfigList"
        :formData="formData"
      ></five-mkt>
    </a-form-model>
  </div>
</template>
<script>
import FiveMkt from "@/pages/mkt/OrderDetail/subassembly/FiveMkt";
export default {
  name: "",
  props: [
    "editFlag",
    "spinning",
    "showData",
    "selectOption",
    "boardBrandList",
    "sheetTraderList",
    "boardtgList",
    "frontDataZSupplierf",
    "reOrder",
    "requiredLinkConfigList",
    "joinFacId",
    "required",
    "ManufacturerTG",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "formData",
  ],
  components: { FiveMkt },
  data() {
    return {
      sheetTrader: [],
      boardBrand: [],
      tslge: false,
      frontDataZSupplier: [],
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      spinning1: false,
    };
  },
  created() {
    this.$nextTick(() => {
      // this.getEditData();
    });
  },
  mounted() {},
  watch: {
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    showData: {
      handler(val) {
        if (
          val.boardBrand == "RO4350B" ||
          val.boardBrand == "RO4003C" ||
          val.boardBrand == "SH260" ||
          val.boardBrand == "CT350" ||
          val.boardBrand == "CT338"
        ) {
          this.tslge = true;
        } else {
          this.tslge = false;
        }
      },
    },
  },

  methods: {
    mapKey(data) {
      if (!data || !data.length) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("formData") != -1) {
        newIsNullRules = newIsNullRules.replace(/formData/g, "this.formData");
      }
      if (val.indexOf("reOrder") != -1) {
        newIsNullRules = newIsNullRules.replace(/reOrder/g, "this.reOrder");
      }
      if (val.indexOf("tslge") != -1) {
        newIsNullRules = newIsNullRules.replace(/tslge/g, "this.tslge");
      }
      if (val.indexOf("boardBrand") != -1) {
        newIsNullRules = newIsNullRules.replace(/boardBrand/g, "this.boardBrand");
      }

      return eval(newIsNullRules);
    },
  },
};
</script>
<style scoped lang="less">
.guokong {
  /deep/.ant-select {
    width: 36% !important;
  }
  /deep/.ant-input {
    padding: 0 !important;
  }
}
.widthclass {
  .select1 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 37px !important;
    }
  }
  .select2 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 56px !important;
    }
  }
}
.bborder {
  .div2 {
    /deep/.ant-form-item-control {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

/deep/b {
  font-weight: 500;
}
.div22 {
  /deep/.ant-form-item-control {
    padding: 0;
    max-height: 65px;
    min-height: 24.5px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
/deep/.require11 {
  color: red !important;
}
// .cu{
//   /deep/.ant-form-item-control{
//     height:26.67px;
//   }
// }
#formDataElem1 {
  .div1 {
    .ant-form-item {
      width: 30%;
    }
    /deep/ .ant-form-item-control-wrapper {
      // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
      font-family: PingFangSC-Regular, Sans-serif;
      font-weight: 500;
      color: #000000;
      font-size: 13px;
      .ant-form-item-control {
        .ant-form-item-children {
          display: block;
          min-height: 13.672px;
        }
        line-height: inherit;
        padding: 2px 4px !important;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
      }
    }
    // /deep/.pcbFileName{
    //   .ant-form-item-label{
    //     width:30%;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:70%;
    //   }
    // }
  }
}
/deep/.ant-input-affix-wrapper {
  width: 100%;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 6px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 2px;
}
/deep/.line3 {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
  // .ant-form-item-control-wrapper {
  //   .ant-form-item-control{
  //     border:0!important;
  //   }
  // }
}
.bbb {
  /deep/.ant-form-item-label {
    width: 16.65%;
    // -ms-width: 101px important; // IE
    // -webkit-width:100.5%; //谷歌
    border-left: 1px solid #ddd;
  }
  /deep/.ant-form-item-control-wrapper {
    // -ms-width: 911px!important; // IE
    // -webkit-width:942.0.5%; //谷歌
    width: 83.3%;
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
// .bbb{
//   /deep/.ant-form-item-label{
//     width:105px;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   /deep/.ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     // width:1038px;
//     width:100%;
//   }
//   /deep/textarea.ant-input {
//     min-height:24px;
//   }
//   /deep/.ant-form-item-control{
//     padding: 2px !important;
//     // width: 942px;
//     width:100%;
//   }
//   /deep/.ant-input{
//     height:24px;
//   }
// }
// /deep/.editWrapper1 {
//   .ant-form-item-label{
//     width:10%;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   .ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     width:1092px;
//   }
// }

/deep/.ant-select-selection--single {
  height: 22px !important;
}
/deep/.ant-select-item-option-content {
  color: red !important;
}
/deep/.ant-select-selection__rendered {
  line-height: 20px !important;
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.require1 {
  .ant-form-item-label > label {
    color: red !important;
    background-color: greenyellow;
  }
}
// /deep/.bac{
//     .ant-form-item-label > label {
//     color: red!important;
//     background-color: #ff9900;
// }

// }
span {
  font-size: 13px;
}

/deep/.ant-select {
  font-size: 13px !important;
}
/deep/.ant-input {
  font-size: 13px !important;
  font-weight: 500;
}
.contentInfo {
  font-size: 13px;
  width: 1676px;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    font: 12px / 1.14 arial;
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 10.5%;
        text-indent: 0.5%;
        font-size: 13px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 10.5%;
      }
    }
    .special {
      height: 268px;
      width: 456px;
      display: inline-block;
      position: absolute;
      right: 172px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 270px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 261px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 270px;
              width: 75px;
            }
          }
        }
      }
    }
    .special1 {
      height: 268px;
      width: 308px;
      display: inline-block;
      position: absolute;
      right: 320px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 267px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 250px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 267px;
            }
          }
        }
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        .ant-row {
          .line2 {
            .ant-form-item-label {
              border-bottom: 0px;
            }
          }
          .line {
            .ant-form-item-control {
              border-bottom: 0px;
            }
          }
        }
      }
      .spec {
        width: 19%;
        position: absolute;
        top: 0;
        right: 0;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .tmp {
          word-break: keep-all;
          vertical-align: top;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: inline-block;
        }
        .editWrapper1 {
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            width: 99%;
          }
        }
        .editWrapper {
          width: 100%;
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            // width:96px;
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            // width: 96px;
            width: 99%;
          }
        }
        .ant-form-item-label {
          // width: 117px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font: 12px/1.14 "微软雅黑", arial;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          // border-left: 1px solid #ddd;
          label {
            // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
            font-family: PingFangSC-Regular, Sans-serif;
            font-size: 13px;
            font-weight: 500;
            color: #000000;
          }
        }
        .ant-form-item-control-wrapper {
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font-family: PingFangSC-Regular, Sans-serif;
          font-weight: 500;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
              white-space: pre-line;
            }
            line-height: inherit;
            padding: 6px 4px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
