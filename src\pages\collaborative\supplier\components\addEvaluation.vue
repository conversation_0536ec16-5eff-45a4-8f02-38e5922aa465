<template>
    <div ref="sb">
    <a-modal
    :title="model.id? '编辑' : '新增'"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    centered
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="匹配等级" ref="matchLevel_" prop="matchLevel_">
           <a-select  v-model="form.matchLevel_"  :getPopupContainer="()=>this.$refs.sb" >
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="406000">
                    A级
                </a-select-option>
                <a-select-option value="406001">
                    B级
                </a-select-option>
                <a-select-option value="406002">
                    C级
                </a-select-option>
                <a-select-option value="406003">
                    D级
                </a-select-option>
                <a-select-option value="406004">
                    E级
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="匹配面积" ref="matchArea_" prop="matchArea_">
          <a-input style="font-weight: 500;"
            v-model="form.matchArea_"
            placeholder="匹配面积"
          />
        </a-form-model-item>
        <a-form-model-item  label="	自动化程度" ref="autoLevel_" prop="autoLevel_">
          <a-select  v-model="form.autoLevel_" :getPopupContainer="()=>this.$refs.sb">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="406100">
                    高
                </a-select-option>
                <a-select-option value="406101">
                    中
                </a-select-option>
                <a-select-option value="406102">
                    低
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="现场管理" ref="siteLevel_" prop="siteLevel_">
            <a-select  v-model="form.siteLevel_" :getPopupContainer="()=>this.$refs.sb">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="406200">
                    优
                </a-select-option>
                <a-select-option value="406201">
                    良
                </a-select-option>
                <a-select-option value="406202">
                    好
                </a-select-option>
                <a-select-option value="406203">
                    一般
                </a-select-option>
                <a-select-option value="406204">
                    差
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="产能空间（平方/米）" ref="outputSpace_" prop="outputSpace_">
          <a-input style="font-weight: 500;"
            v-model="form.outputSpace_"
            placeholder="产能空间（平方/米）"
          />
        </a-form-model-item>
        <a-form-model-item  label="	扩产计划" ref="expansion_" prop="expansion_">
          <a-select v-model="form.expansion_" :getPopupContainer="()=>this.$refs.sb">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="406300">
                    有
                </a-select-option>
                <a-select-option value="406301">
                    无
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="评估结果" ref="evaResult" prop="evaResult">
          <a-input style="font-weight: 500;"
            v-model="form.evaResult"
            placeholder="评估结果"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
    </div>
 
</template>

<script>
import { addAssest, updateAssest,progressNum } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
      compileApply:{
          type: String,
          default () {
              return ''
          }
      },
      message:{
          type: String,
          default () {
              return ''
          }
      }
  },
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {
          matchLevel_: '',
          matchArea_: '',
          autoLevel_: '',
          siteLevel_: '',
          outputSpace_: '',
          expansion_: '',
          evaResult: '',
      },
      rules: {
          matchLevel_: [
          { required: true, message: "请选择匹配等级", trigger: "blur" },
        ],
          matchArea_: [
              { required: true, message: "请填写匹配面积", trigger: "blur" },
          ],
          autoLevel_: [
              { required: true, message: "请选择自动化程度", trigger: "blur" },
          ],
          siteLevel_: [
              { required: true, message: "请选择现场管理", trigger: "blur" },
          ],
          outputSpace_: [
              { required: true, message: "请填写产能空间", trigger: "blur" },
          ],
          expansion_: [
              { required: true, message: "请选择扩产计划", trigger: "blur" },
          ],
          evaResult: [
              { required: true, message: "请填写评估结果", trigger: "blur" },
          ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
        // if(this.compileApply=='1'){
            this.visible = true;
            this.model = model
            if(model && model.id) {
                this.form = {
                    id:model.id,
                    matchLevel_: model.matchLevel_,
                    matchArea_: model.matchArea_,
                    autoLevel_: model.autoLevel_,
                    siteLevel_: model.siteLevel_,
                    outputSpace_: model.outputSpace_,
                    expansion_: model.expansion_,
                    evaResult: model.evaResult,
                };
            }else {
                this.form={
                    matchLevel_: '',
                    matchArea_: '',
                    autoLevel_: '',
                    siteLevel_: '',
                    outputSpace_: '',
                    expansion_: '',
                    evaResult: '',
                }
            }
        // }else {
        //     this.$message.info(this.message)
        // }

    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
      this.$refs.ruleForm.resetFields()
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          let params = {
              id:this.model.id,
              pGuid_: this.suppId,
              ...this.form
          }
          if(this.model.id) {
            updateAssest(this.model.id,params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info("操作成功");
                    this.$emit("ok");
                    progressNum(this.suppId).then(res=>{
                        if(res.code!==1){
                            this.$message.info(res.message)
                        }
                    })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }else{
              addAssest(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info("操作成功");
                    this.$emit("ok");
                    progressNum(this.suppId).then(res=>{
                        if(res.code!==1){
                            this.$message.info(res.message)
                        }
                    })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
    /deep/.ant-select-dropdown-menu-item{ 
       font-weight: 500;
    }
    /deep/.ant-modal-header .ant-modal-title {
        font-weight: 500;
    }
    /deep/.ant-form-item{
        margin-bottom: 5px;
    }
</style>
