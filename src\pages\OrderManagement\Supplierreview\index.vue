<!-- 生产管理 客诉复核-->
<template>
    <a-spin :spinning="spinning">  
        <div class="CustomerComplaintreview">
            <a-tabs  :activeKey="activeKey"  @tabClick="tabClick">
            <a-tab-pane key="40" tab="待复核"> 
            </a-tab-pane>
            <!-- <a-tab-pane key="60" tab="已复核">
            </a-tab-pane> -->
            <a-tab-pane key="60" tab="待回访">                
            </a-tab-pane>
            <a-tab-pane key="100" tab="已完结">                
            </a-tab-pane>
        </a-tabs>
        <div style="display: flex;padding-bottom: 7px;">
            <a-input placeholder="请输入订单编号/客户编号" style="width: 250px;margin-left: 15px;"
            @keydown.enter.native="search()"  v-model="searchdata.CommonOrderNo" ></a-input>
            <a-range-picker  :format="'YYYY-MM-DD'" @change="onChange" v-model="timeValue"
            style="margin-left: 15px;" :placeholder="['客诉开始时间', '客诉结束时间']">
            <template #suffixIcon>
                <a-icon type="calendar" />
            </template>
            </a-range-picker>
            <a-button type="primary" style="margin-left: 15px;" @click="search()"><a-icon type="search"></a-icon>查询</a-button>
        </div>  
        <double-check 
        :columns="columns" 
        :checkdata="checkdata" 
        :activeKey="activeKey" 
        :pagination="pagination" 
        @sendclick="sendclick"
        @tableChange="handleTableChange" />  
        <div class="footerAction" style='user-select:none;margin-left: -2px;'>
          <div>
            <!-- <a-button type="primary" class="box">
                取单
            </a-button>  -->
          </div>             
        </div>      
        </div>
    </a-spin>
</template>
<script>
import {factorycomplaintspagelist,factoryresultreviewsend} from "@/services/complaint/QualitativeComplaint.js";
import DoubleCheck from '@/pages/OrderManagement/Supplierreview/module/DoubleCheck'
const columns = [
{
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 65,
    ellipsis:true,
    fixed: 'left',
},   
{
    title: "客诉编号",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "订单编号",
    dataIndex: "orderNo",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "生产编号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,   
},
{
    title: "联系人",
    dataIndex: "factoryContactName",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "联系电话",
    dataIndex: "factoryContactNumber",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "投诉日期",
    dataIndex: "complaintDate",
    align: "left",
    ellipsis: true,
    width:180,       
},
{
    title: "客户要求",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:200,       
},
{
    title: "8D报告",
    customRender: (text,record,index) => `${record.need8DReport?'是':'否'}`,
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "文件下载",
    align: "left",
    ellipsis: true,
    scopedSlots:{customRender:'filedown'},
    width:80,       
},
{
    title: "状态",
    dataIndex: "statusStr",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "初审人",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "初审时间",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "操作",
    align: "center",
    ellipsis: true,
    width:100,       
    scopedSlots:{customRender:'operation'}
},
];
export default {
    name:'CustomerComplaintreview',
    components:{DoubleCheck},
    data(){
        return{
            spinning:false,
            columns,
            activeKey:'40',
            searchdata:{},
            timeValue:undefined,
            pagination: {
                pageSize: 20,
                current: 1,
                total:0,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: [ "20", "50", "100"],//每页中显示的数据
                showTotal: (total) => `总计 ${total} 条`,
            },
            checkdata:[],
        }
    },
    created(){
    },
    mounted(){
        this.getdata({status:this.activeKey})
    },
    methods:{
        onChange(date, dateString) {
                this.searchdata.StartDate=dateString[0]
                this.searchdata.EndDate=dateString[1]
            },
        search(){
            if(JSON.stringify(this.searchdata)=='{}'){
                this.$message.error('请输入查询条件')
                return
            }
            let params = {...this.searchdata,...{status:this.activeKey}}
            this.getdata(params)
        },
        sendclick(record){
            factoryresultreviewsend(record.id).then(res=>{
                if(res.code){
                    this.$message.success(res.message)
                    this.getdata({status:this.activeKey})
                }else{
                   this.$message.error(res.message)
                }
            })
        },    
        getdata(val){
            this.spinning=true
            let tabkey = localStorage.getItem('tabkey')
            if(tabkey){
                this.activeKey = tabkey
                val.status=tabkey
            }
            let params = {
            'PageIndex': this.pagination.current,
            'PageSize' : this.pagination.pageSize,        
            }  
        params = {...params,...val}
        factorycomplaintspagelist(params).then(res=>{
            if(res.code){
                this.checkdata =res.data.items
            }else{
                this.$message.error(res.message)
            }
        }).finally(() => {
            this.spinning = false;
            localStorage.removeItem('tabkey')
        });
        },
        addnewcomplaint(){
            this.$router.push({path:'Addcomplaint',query:{} })
        },
        tabClick(key){
            this.activeKey = key
            this.searchdata={}
            this.timeValue=undefined
            this.getdata({status:this.activeKey})
        },
        // 订单表变化change
        handleTableChange(pagination, filters, sorter) {
            this.pagination.current=pagination.current
            this.pagination.pageSize=pagination.pageSize
            if(JSON.stringify(this.searchdata) == "{}"){
                this.getdata({status:this.activeKey})
            }else{
                let params = {
                    status:this.activeKey,
                    ...this.searchdata
                }
                this.getdata(params)
            }
        },  
    }
}

</script>
<style lang="less" scoped>
.CustomerComplaintreview{
    background-color: white;
    width: 100%;
}
.footerAction {
    width: 100%;
    height:46px;
    background: white;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    .box{
        float: right;
        margin-top: 6px;
        margin-right:10px;
        width: 90px;
    }
}
</style>