<!--品质管理- 作废录入-基本信息 -->
<template>
  <div class="contentInfo">
    <ul :model="formInfo" class="left">
      <li>
        <div class="lableName" style="color: red">工单编号 :</div>
        <a-input v-cloak v-model="formInfo.cardNo" @keyup.enter="keyupEnter" :disabled="flg" />
      </li>
      <li>
        <div class="lableName">拼版编号 :</div>
        <a-input v-cloak v-model="formInfo.pinBanNo" disabled />
      </li>
      <li>
        <div class="lableName" style="color: red">报废订单 :</div>
        <a-select v-model="formInfo.orders" show-search allowClear :disabled="flg" :getPopupContainer="() => this.$refs.contentInfo">
          <a-select-option v-for="(item, index) in orderNos" :key="index" :value="item">{{ item }}</a-select-option>
        </a-select>
      </li>
      <!-- <li  >
              <div class='lableName' style="color:red"> 报废数量 :</div> 
              <a-input  v-model="formInfo.num" :disabled='flg'  />        
            </li> -->
      <li>
        <div class="lableName" style="color: red">责任工序 :</div>
        <a-select
          v-model="formInfo.stepKey"
          @change="change"
          option-filter-prop="children"
          :filter-option="filterOption"
          show-search
          allowClear
          :getPopupContainer="() => this.$refs.contentInfo"
        >
          <a-select-option v-for="(item, index) in StepKeyList" :key="index" :value="item.text">
            {{ item.valueMember }}
          </a-select-option>
        </a-select>
      </li>
      <li>
        <div class="lableName" style="color: red">报废单位 :</div>
        <a-select v-model="formInfo.unit" show-search allowClear :disabled="flg" :getPopupContainer="() => this.$refs.contentInfo">
          <a-select-option value="PNL">PNL</a-select-option>
          <a-select-option value="SET">SET</a-select-option>
          <a-select-option value="PCS">PCS</a-select-option>
        </a-select>
      </li>

      <!-- <li >
              <div class='lableName' style="color:red"> 报废时间 :</div> 
              <a-date-picker 
                v-model="formInfo.scrapTime"              
                valueFormat="YYYY-MM-DD HH:mm:ss "
                :showTime="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"  
                @change="onChange1"
              />
            </li>   -->

      <li>
        <div class="lableName">责任人 :</div>
        <a-input v-model="formInfo.personLiable" />
      </li>
      <li>
        <div class="lableName">处理措施 :</div>
        <a-select v-model="formInfo.handling" show-search allowClear :getPopupContainer="() => this.$refs.contentInfo">
          <a-select-option value="无">无</a-select-option>
          <a-select-option value="补投">补投</a-select-option>
        </a-select>
      </li>
      <li>
        <div class="lableName" style="color: red">录入工序 :</div>
        <a-select v-model="formInfo.createStep" show-search allowClear :getPopupContainer="() => this.$refs.contentInfo">
          <a-select-option value="MRB">MRB</a-select-option>
          <a-select-option value="AOI">AOI</a-select-option>
          <a-select-option value="FQC">FQC</a-select-option>
        </a-select>
      </li>
      <!-- <li >
              <div class='lableName' style="color:red"> 报废缺陷 :</div> 
              <a-textarea :auto-size="{ minRows: 2, maxRows: 5 }"    v-model="formInfo.reason"/>        
            </li>  -->
    </ul>
    <ul class="right">
      <!-- <li v-for="(ite,index) in ConfigreList" :key="index" :value="ite.valueMember">
        <a-checkbox @change="click($event,ite.valueMember)"></a-checkbox>       
        {{ite.valueMember}}
        <a-input style="width:40px;padding:2px;"></a-input>
      </li> -->
      <a-table
        :dataSource="ConfigreList"
        :pagination="false"
        :columns="columns"
        :rowKey="'valueMember'"
        bordered
        :scroll="{ y: 692 }"
        :class="ConfigreList.length ? 'minTable' : ''"
      >
        <span slot="num" slot-scope="record">
          <a-input @blur="blur(record)" v-model="record.num" allowClear>{{ record.num }} </a-input>
        </span>
      </a-table>
    </ul>
  </div>
</template>

<script>
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 50,
  },
  {
    title: "报废缺陷",
    dataIndex: "valueMember",
    key: "valueMember",
    width: 100,
  },
  {
    title: "报废数量",
    scopedSlots: { customRender: "num" },
    width: 80,
  },
];
import moment from "moment";
import { byCardNo, defectConfigre } from "@/services/scgl/QualityManagement/quality";
import $ from "jquery";
export default {
  name: "OrderInfo",
  props: ["updateForm", "StepKeyList", "flg", "ConfigreList"],
  data() {
    return {
      formInfo: {
        pinBanNo: "", //作废料号
        cardNo: "", //流程卡号
        unit: "", //报废单位（pcs.set,pnl)
        orders: "", //子订单号 （报废订单）
        stepKey: undefined, //报废工序
        personLiable: "", //责任人
        reason: "", //报废原因
        handling: "", //处理措施
        createStep: "", //录入工序
        createAccount: "", //录入人
        createName: "", //录入时间
        num: "", // 报废数量
      },
      orderNos: [],
      // StepKeyList:[],
      disabled: true,
      columns,
    };
  },

  mounted() {
    // this.getStepConfigre()
  },
  watch: {
    updateForm(val) {
      this.upData(val);
    },
  },
  methods: {
    moment,
    onChange1(value, dateString) {
      this.formInfo.scrapTime = dateString;
    },
    upData(val) {
      console.log("val", val);
      this.formInfo.pinBanNo = val.pinBanNo;
      this.formInfo.cardNo = val.cardNo;
      this.formInfo.unit = val.unit;
      this.formInfo.stepKey = val.stepKey;
      this.formInfo.personLiable = val.personLiable;
      this.formInfo.reason = val.reason;
      this.formInfo.scrapTime = val.scrapTime;
      this.formInfo.createStep = val.createStep;
      this.formInfo.handling = val.handling;
      this.formInfo.num = val.num;
      this.formInfo.orders = val.orders;
      this.keyupEnter();
      if (this.formInfo.stepKey) {
        this.$emit("defectConfigre", this.formInfo.stepKey);
      }
    },
    change() {
      this.$emit("defectConfigre", this.formInfo.stepKey);
    },
    // click(e,valueMember){
    //   console.log(e,valueMember,)
    //   if(e.target.checked){
    //     if(!this.formInfo.reason){
    //     this.formInfo.reason =  valueMember
    //     }else{
    //       var arr = this.formInfo.reason.split(';')
    //       console.log(arr,arr.includes(valueMember))
    //       if(!arr.includes(valueMember)) {
    //         this.formInfo.reason = this.formInfo.reason +';' + valueMember
    //       }
    //     }
    //   }else{
    //     if(this.formInfo.reason.split(';').includes(valueMember)) {
    //         this.formInfo.reason = this.formInfo.reason +';' + valueMember
    //       }
    //   }

    // },
    keyupEnter() {
      byCardNo(this.formInfo.cardNo).then(res => {
        if (res.code) {
          this.formInfo.pinBanNo = res.data.pinBanNo;
          this.orderNos = res.data.orderNos;
          if (this.orderNos.length == 1) {
            this.formInfo.orders = this.orderNos[0];
          }

          this.$emit("getStepConfigre", this.formInfo.pinBanNo, this.formInfo.cardNo);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    blur(val) {
      console.log(val);
      this.$forceUpdate();
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}
/deep/.ant-table {
  width: 300px;
  .ant-input {
    height: 26px;
    padding: 2px 11px;
  }
}
/deep/.ant-table-thead > tr > th {
  padding: 3px;
}
/deep/.ant-table-tbody > tr > td {
  padding: 3px;
}
/deep/.ant-table-bordered .ant-table-body > table {
  border-left: 0;
}

.contentInfo {
  width: 100%;
  height: 720px;
  display: flex;
  .left {
    width: 25%;
  }
  .right {
    display: flex;
    flex-wrap: wrap;
    width: 74%;
    /deep/.ant-table-body {
      border-bottom: 1px solid #ddd;
      border-left: 1px solid #ddd;
    }
    .minTable {
      /deep/.ant-table-body {
        min-height: 288px;
      }
    }
    li {
      display: inline-block;
      width: 100px;
      margin: 0 5px;
      label {
        margin-right: 5px;
      }
    }
  }
  .lableName {
    width: 20%;
    display: inline-block;
  }
  ul li {
    .ant-input,
    .ant-select {
      width: 79%;
    }
    list-style-type: none;
    height: 40px;
    line-height: 40px;
  }

  // /deep/ .ant-card {
  //   .ant-card-head {
  //     padding: 0;
  //     min-height: auto;
  //     border: 0;
  //     .ant-card-head-title {
  //       padding: 0;
  //       border-bottom: 1px solid #ddd;
  //       height: 29px;
  //       line-height: 20px;
  //       margin-bottom: 15px;
  //       text-indent: 5px;
  //       font-size: 14px;
  //       font-weight: normal;
  //       color: #000;
  //       padding-bottom: 8px;
  //       margin-top: 15px;

  //     }
  //   }
  //   .ant-card-body {
  //     padding: 0;
  //     .ant-form {
  //       border-left:1px solid #ddd;
  //       border-top:1px solid #ddd;
  //     }
  //     .ant-form-item {
  //       margin: 0;
  //       width: 100%;
  //       display: flex;

  //       .editWrapper {
  //         display: flex;
  //         align-items: center;
  //         min-height: 32px;
  //         .ant-select {
  //           width: 120px;
  //         }
  //         .ant-input {
  //           width: 120px;
  //         }
  //         .ant-input-number {
  //           width: 120px;
  //         }
  //       }
  //       .ant-form-item-label {
  //         display: flex;
  //         align-items: center;
  //         justify-content: flex-end;
  //         font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
  //         color: #666;
  //         background-color: #fafafa;
  //         border-right: 1px solid #ddd;
  //         border-bottom: 1px solid #ddd;
  //         label {
  //           font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
  //         }
  //       }
  //       .ant-form-item-control-wrapper {
  //         font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
  //         .ant-form-item-control {

  //           .ant-form-item-children {
  //             display: block;
  //             min-height: 13.672px;
  //           }
  //           line-height: inherit;
  //           padding: 8px 10px;
  //           border-right: 1px solid #ddd;
  //           border-bottom: 1px solid #ddd;
  //         }
  //       }
  //     }
  //   }
  // }
}
</style>
