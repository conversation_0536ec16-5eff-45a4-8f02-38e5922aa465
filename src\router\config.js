import TabsView from "@/layouts/tabs/TabsView";
import BlankView from "@/layouts/BlankView";
import PageView from "@/layouts/PageView";

// 路由配置
const options = {
  mode: "history",
  routes: [
    {
      path: "/login",
      name: "登录页",
      EnglishName: "Login",
      ThaiName: "เข้าสู่ระบบ",
      component: () => import("@/pages/login"),
    },
    {
      path: "/loginpe",
      name: "登录页",
      EnglishName: "LoginPE",
      ThaiName: "เข้าสู่ระบบ",
      component: () => import("@/pages/login/Loginpe"),
    },
    {
      path: "/wippe",
      name: "WIP查询",
      EnglishName: "WIPQuery",
      ThaiName: "แบบสอบถาม WIP",
      component: () => import("@/pages/login/WipPe"),
    },
    {
      path: "/loginICam",
      name: "loginICam",
      EnglishName: "LoginIcam",
      ThaiName: "เข้าสู่ระบบ Icam",
      component: () => import("@/pages/login/LoginICam"),
    },
    {
      path: "/userlogin",
      name: "用户登录",
      EnglishName: "UserLogin",
      ThaiName: "เข้าสู่ระบบผู้ใช้",
      component: () => import("@/pages/usermanagement/userlogin"),
    },
    {
      path: "/usermanagement",
      name: "用户订单管理",
      EnglishName: "UserOrder",
      ThaiName: "การจัดการคำสั่งซื้อของผู้ใช้",
      component: () => import("@/pages/usermanagement/index"),
    },
    {
      path: "/loginNew",
      name: "loginNew",
      EnglishName: "LoginNew",
      ThaiName: "เข้าสู่ระบบใหม่",
      component: () => import("@/pages/login/loginNew"),
    },
    {
      path: "/loginOA",
      name: "loginOA",
      EnglishName: "LoginOA",
      ThaiName: "เข้าสู่ระบบ OA",
      component: () => import("@/pages/login/loginOA"),
    },
    {
      path: "/flyingProbe",
      name: "flyingProbe",
      EnglishName: "FlyingProbe",
      ThaiName: "เข็มบิน",
      component: () => import("@/pages/login/flyingProbe"),
    },
    {
      path: "*",
      name: "404",
      EnglishName: "NotFound",
      ThaiName: "404",
      component: () => import("@/pages/exception/404"),
    },
    // {
    //   path: '/impedance',
    //   name: 'impedance',
    //   component: () => import('@/pages/impedance'),
    // },
    {
      path: "/403",
      name: "403",
      EnglishName: "Forbidden",
      ThaiName: "403",
      component: () => import("@/pages/exception/403"),
    },
    {
      path: "/eqDetails1",
      name: "问客详情",
      EnglishName: "EQDetails",
      ThaiName: "รายละเอียดคำถาม",
      component: () => import("@/pages/gongcheng/projectPage/eqDetails1"),
    },
    {
      path: "costanalysis",
      name: "成本分析预览",
      component: () => import("@/pages/mkt/QuotationApproval/module/costanalysis"),
    },
    {
      path: "/registerDetails",
      name: "返修登记",
      EnglishName: "RepairReg",
      ThaiName: "ลงทะเบียนซ่อม",
      component: () => import("@/pages/gongcheng/projectPage/registerDetails"),
    },
    {
      path: "/ProcessCard",
      name: "流程卡打印",
      EnglishName: "ProcessCardPrint",
      ThaiName: "พิมพ์การ์ดกระบวนการ",
      component: () => import("@/pages/OrderManagement/ProcessCard"),
    },
    {
      path: "/AuditParameters",
      name: "审核参数",
      EnglishName: "AuditParams",
      ThaiName: "พารามิเตอร์ตรวจสอบ",
      component: () => import("@/pages/sight/AuditParameters"),
    },
    {
      path: "/drillStructure",
      name: "钻孔结构",
      EnglishName: "DrillStructure",
      ThaiName: "โครงสร้างเจาะ",
      component: () => import("@/pages/sight/drillStructure"),
    },
    {
      path: "/Goldfinger",
      name: "金手指计算",
      EnglishName: "GoldfingerCalc",
      ThaiName: "คำนวณ Goldfinger",
      component: () => import("@/pages/sight/Goldfinger"),
    },
    {
      path: "/AuditParametersMkt",
      name: "审核参数",
      EnglishName: "AuditParamsMkt",
      ThaiName: "พารามิเตอร์ตรวจสอบตลาด",
      component: () => import("@/pages/sight/AuditParametersMkt"),
    },
    {
      path: "/callback",
      name: "回调页",
      EnglishName: "Callback",
      ThaiName: "หน้าส่งกลับ",
      component: () => import("@/pages/callback"),
    },
    {
      path: "/",
      name: "首页",
      EnglishName: "Home",
      ThaiName: "หน้าหลัก",
      component: TabsView,
      children: [
        {
          path: "dashboard",
          name: "控制台",
          EnglishName: "Dashboard",
          ThaiName: "แดชบอร์ด",
          meta: {
            icon: "dashboard",
            authority: "MES.ControlPanel",
          },
          redirect: "/dashboard/analysis",
          component: () => import("@/layouts/BlankView"),
          children: [
            // {
            //   path: 'analysisMkt',
            //   name: '市场分析页',
            //   meta:{
            //     authority: 'MES.ControlPanel.Analysis'
            //   },
            //   component: () => import('@/pages/dashboard/analysisMkt'),
            // },
            {
              path: "MarketanAlysis",
              name: "市场分析页",
              EnglishName: "MarketAnalysis",
              ThaiName: "วิเคราะห์ตลาด",
              meta: {
                authority: "MES.ControlPanel.MarketAnalysis",
              },
              component: () => import("@/pages/dashboard/analysisMkt/MarketanAlysis"),
            },
            {
              path: "dataPageMkt",
              name: "市场数据页",
              EnglishName: "MarketDataPage",
              ThaiName: "ข้อมูลตลาด",
              meta: {
                authority: "MES.ControlPanel.MarketDataPage",
              },
              component: () => import("@/pages/dashboard/dataPageMkt/DataPage"),
            },
            {
              path: "analysis",
              name: "工程分析页",
              EnglishName: "EngineeringAnalysis",
              ThaiName: "วิเคราะห์วิศวกรรม",
              meta: {
                authority: "MES.ControlPanel.EngineeringAnalysis",
              },
              component: () => import("@/pages/dashboard/analysis"),
            },
            {
              path: "dataPage",
              name: "工程数据页",
              EnglishName: "EngineeringDataPage",
              ThaiName: "ข้อมูลวิศวกรรม",
              meta: {
                authority: "MES.ControlPanel.EngineeringDataPage",
              },
              component: () => import("@/pages/dashboard/dataPage/DataPage"),
            },
          ],
        },
        {
          path: "collaborative",
          name: "供应链",
          EnglishName: "SupplyChain",
          ThaiName: "ห่วงโซ่อุปทาน",
          meta: {
            icon: "appstore-o",
            authority: "MES.CoordinationModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "supplier",
              name: "供应商",
              EnglishName: "Supplier",
              ThaiName: "ซัพพลายเออร์",
              meta: {
                authority: "MES.CoordinationModule.SupplierManagement",
              },
              component: () => import("@/pages/collaborative/supplier/supplierS"),
            },
            {
              path: "detail",
              name: "供应商详情",
              EnglishName: "SupplierDetail",
              ThaiName: "รายละเอียดซัพพลายเออร์",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/collaborative/supplier/supplierDetails"),
            },
            {
              path: "Coordination",
              name: "协同工厂",
              EnglishName: "CoordinationFac",
              ThaiName: "โรงงานประสานงาน",
              meta: {
                authority: "MES.CoordinationModule.CoordinationFactory",
              },
              component: () => import("@/pages/collaborative/grade/Coordination.vue"),
            },
            {
              path: "ZeroOrder",
              name: "零单工厂",
              EnglishName: "OddOrderFactory",
              ThaiName: "โรงงานออเดอร์เล็ก",
              meta: {
                authority: "MES.CoordinationModule.OddOrderFactory",
              },
              component: () => import("@/pages/collaborative/grade/ZeroOrder.vue"),
            },
            {
              path: "gradeDetails1",
              name: "详情",
              EnglishName: "GradeDetail",
              ThaiName: "รายละเอียด",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/collaborative/grade/gradeDetail1"),
            },
            {
              path: "contract",
              name: "合同管理",
              EnglishName: "ContractManage",
              ThaiName: "จัดการสัญญา",
              meta: {
                authority: "MES.CoordinationModule.ContractManage",
              },
              component: () => import("@/pages/collaborative/supplier/contractNew"),
            },
            {
              path: "manufacturer",
              name: "制造工厂",
              EnglishName: "Manufacturer",
              ThaiName: "โรงงานผลิต",
              meta: {
                authority: "MES.CoordinationModule.ZZGC",
              },
              component: () => import("@/pages/collaborative/manufacturer/index"),
            },
          ],
        },
        {
          path: "shichang",
          name: "市场管理",
          EnglishName: "Market",
          ThaiName: "จัดการตลาด",
          meta: {
            icon: "form",
            authority: "MES.MarketModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "CRMNew",
              name: "客户管理",
              EnglishName: "Customer",
              ThaiName: "จัดการลูกค้า",
              meta: {
                authority: "MES.MarketModule.CustomerManagement",
              },
              component: () => import("@/pages/mkt/CustInfo"),
            },
            {
              path: "CRMNewDetail",
              name: "客户管理详情",
              EnglishName: "CustomerDetail",
              ThaiName: "รายละเอียดลูกค้า",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/mkt/CustInfo/CustInfoDetail"),
            },
            {
              path: "MailList_List",
              name: "邮件列表",
              EnglishName: "MailList",
              ThaiName: "รายการอีเมล",
              meta: {
                authority: "MES.MarketModule.MailList",
              },
              component: () => import("@/pages/mkt/mailList_List"),
            },
            {
              path: "orderInquiry",
              name: "订单询价",
              EnglishName: "OrderInquiry",
              ThaiName: "สอบถามออเดอร์",
              meta: {
                authority: "MES.MarketModule.OrderManagement",
              },
              component: () => import("@/pages/mkt/Inquiry"),
            },
            {
              path: "PreTrialAssignment",
              name: "预审分派",
              EnglishName: "PretrialAssignment",
              ThaiName: "มอบหมายล่วงหน้า",
              meta: {
                authority: "MES.MarketModule.Pretrialassignment",
              },
              component: () => import("@/pages/mkt/PreTrialAssignment"),
            },
            {
              path: "Orderverify",
              name: "订单预审",
              EnglishName: "OrderVerify",
              ThaiName: "ตรวจสอบออเดอร์",
              meta: {
                authority: "MES.MarketModule.Prequalificationproduction",
              },
              component: () => import("@/pages/mkt/Orderverify"),
            },
            {
              path: "orderDetail",
              name: "订单详情",
              EnglishName: "OrderDetail",
              ThaiName: "รายละเอียดออเดอร์",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/mkt/OrderDetail"),
            },
            {
              path: "OrderOffer",
              name: "订单报价",
              EnglishName: "OrderOffer",
              ThaiName: "เสนอราคาออเดอร์",
              meta: {
                authority: "MES.MarketModule.Check",
              },
              component: () => import("@/pages/mkt/OrderOffer"),
            },
            {
              path: "QuotationApproval",
              name: "报价审批",
              EnglishName: "QuotationApproval",
              ThaiName: "อนุมัติราคา",
              meta: {
                authority: "MES.MarketModule.OfferRatify",
              },
              component: () => import("@/pages/mkt/QuotationApproval"),
            },
            {
              path: "BusinessTracking",
              name: "业务跟单",
              EnglishName: "BusinessTracking",
              ThaiName: "ติดตามธุรกิจ",
              meta: {
                authority: "MES.MarketModule.BusinessDocumentary",
              },
              component: () => import("@/pages/mkt/BusinessTracking"),
            },
            {
              path: "ReviewDetail",
              name: "订单详情",
              EnglishName: "ReviewDetail",
              ThaiName: "รายละเอียดออเดอร์",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/mkt/OrderOffer/ReviewDetail"),
            },
            {
              path: "OrderManagement",
              name: "订单管理",
              EnglishName: "Order",
              ThaiName: "จัดการออเดอร์",
              meta: {
                authority: "MES.MarketModule.OrderManage",
              },
              component: () => import("@/pages/mkt/OrderManagement"),
            },
            {
              path: "OrderReconciliation",
              name: "合同管理",
              EnglishName: "Contract",
              ThaiName: "จัดการสัญญา",
              meta: {
                authority: "MES.MarketModule.CheckManage",
              },
              component: () => import("@/pages/mkt/OrderReconciliation"),
            },
            {
              path: "WipQurey",
              name: "WIP查询",
              EnglishName: "WIPQuery",
              ThaiName: "สอบถาม WIP",
              meta: {
                authority: "MES.MarketModule.WipOrder",
              },
              component: () => import("@/pages/mkt/WipQurey"),
            },
            {
              path: "PriceSystem",
              name: "价格体系",
              EnglishName: "PriceSystem",
              ThaiName: "ระบบราคา",
              meta: {
                authority: "MES.MarketModule.PriceSystem",
              },
              component: () => import("@/pages/mkt/PriceSystem"),
            },
            {
              path: "ServiceManaging",
              name: "客服管理",
              EnglishName: "CustomerService",
              ThaiName: "บริการลูกค้า",
              meta: {
                authority: "MES.MarketModule.CustomerServiceManag",
              },
              component: () => import("@/pages/mkt/ServiceManaging"),
            },
            {
              path: "marketEQ",
              name: "问客管理",
              EnglishName: "MarketEQ",
              ThaiName: "จัดการคำถาม",
              meta: {
                authority: "MES.MarketModule.MktEqManagement",
              },
              component: () => import("@/pages/mkt/marketEQ"),
            },
          ],
        },
        {
          path: "gongcheng",
          name: "工程管理",
          EnglishName: "Engineering",
          ThaiName: "จัดการวิศวกรรม",
          meta: {
            icon: "control",
            authority: "MES.EngineeringModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "projectdispatch",
              name: "工程派单",
              EnglishName: "ProjectDispatch",
              ThaiName: "ส่งงานวิศวกรรม",
              meta: {
                authority: "MES.EngineeringModule.EngineeringDispatch",
              },
              component: () => import("@/pages/gongcheng/projectDisptchTest"),
            },
            {
              path: "projectMake",
              name: "工程制作",
              EnglishName: "ProjectMake",
              ThaiName: "ผลิตงานวิศวกรรม",
              meta: {
                authority: "MES.EngineeringModule.EngineeringProduction",
              },
              component: () => import("@/pages/gongcheng/projectMake"),
            },
            {
              path: "projectBackend",
              name: "工程后端",
              EnglishName: "ProjectBackend",
              ThaiName: "แบ็กเอนด์วิศวกรรม",
              meta: {
                authority: "MES.EngineeringModule.EngineeringBackend",
              },
              component: () => import("@/pages/gongcheng/projectBackend"),
            },
            {
              path: "projectEQ",
              name: "问客管理",
              EnglishName: "ProjectEQ",
              ThaiName: "จัดการคำถาม",
              meta: {
                authority: "MES.EngineeringModule.EqManagement",
              },
              component: () => import("@/pages/gongcheng/projectEQ"),
            },
            // {
            //   path: "projectReview",
            //   name: "评审管理",
            //   meta: {
            //     authority: "MES.EngineeringModule.ReviewManagement",
            //   },
            //   component: () => import("@/pages/gongcheng/projectReview"),
            // },
            {
              path: "projectQae",
              name: "QAE审核",
              EnglishName: "QAEAudit",
              ThaiName: "ตรวจสอบ QAE",
              meta: {
                authority: "MES.EngineeringModule.EngineeringQae",
              },
              component: () => import("@/pages/gongcheng/projectQae"),
            },
            {
              path: "eqDetails",
              name: "问客详情",
              EnglishName: "EQDetails",
              ThaiName: "รายละเอียดคำถาม",
              meta: {
                authority: "MES.EngineeringModule.EngineeringEqPage",
                invisible: true,
              },
              component: () => import("@/pages/gongcheng/projectPage/eqDetails"),
            },
            {
              path: "nbook",
              name: "NBOOK",
              EnglishName: "NBOOK",
              ThaiName: "NBOOK",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/gongcheng/projectPage/nbook"),
            },
            {
              path: "ReviewDetails",
              name: "评审详情",
              EnglishName: "ReviewDetails",
              ThaiName: "รายละเอียดการทบทวน",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/gongcheng/ReviewDetails"),
            },
            {
              path: "engineeringQ",
              name: "工程指示详情",
              EnglishName: "EngineeringInstruction",
              ThaiName: "รายละเอียดคำสั่ง",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/gongcheng/projectIndicate/module/projectQ"),
            },
            {
              path: "JointManagement",
              name: "合拼管理",
              EnglishName: "JointManagement",
              ThaiName: "จัดการรวม",
              meta: {
                authority: "MES.EngineeringModule.CombineManage",
              },
              component: () => import("@/pages/gongcheng/JointManagement"),
            },
            {
              path: "CombinedDesign",
              name: "合拼设计",
              EnglishName: "CombinedDesign",
              ThaiName: "ออกแบบรวม",
              meta: {
                authority: "MES.EngineeringModule.Combinate",
              },
              component: () => import("@/pages/gongcheng/CombinedDesign"),
            },
            // {
            //   path: 'cncDispatch',
            //   name: '锣带分派',
            //   meta: {
            //      authority: 'MES.EngineeringModule.FormingDispatch',
            //   },
            //   component: () => import('@/pages/gongcheng/cncDispatch/cnc.vue'),
            // },
            {
              path: "ToolofProduction",
              name: "生产工具",
              EnglishName: "ProductionTool",
              ThaiName: "เครื่องมือผลิต",
              meta: {
                authority: "MES.EngineeringModule.ProductionTool",
              },
              component: () => import("@/pages/gongcheng/ToolofProduction"),
            },
            {
              path: "engineering",
              name: "工程指示",
              EnglishName: "EngineeringIndicate",
              ThaiName: "คำสั่งวิศวกรรม",
              meta: {
                authority: "MES.EngineeringModule.EngineeringInstruction",
                invisible: true,
              },
              component: () => import("@/pages/gongcheng/projectIndicate"),
            },
            {
              path: "technologicalProcess",
              name: "工艺流程",
              EnglishName: "TechProcess",
              ThaiName: "กระบวนการผลิต",
              meta: {
                authority: "MES.EngineeringModule.TechnologicalProcess",
              },
              component: () => import("@/pages/gongcheng/process"),
            },
            {
              path: "projectECN",
              name: "ECN管理",
              EnglishName: "ECNManagement",
              ThaiName: "จัดการ ECN",
              meta: {
                authority: "MES.EngineeringModule.EngineeringEcn",
              },
              component: () => import("@/pages/gongcheng/projectECN"),
            },
          ],
        },
        {
          path: "gongju",
          name: "工具管理",
          EnglishName: "Tool",
          ThaiName: "จัดการเครื่องมือ",
          meta: {
            icon: "profile",
            authority: "MES.ToolModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "feizhen",
              name: "飞针制作",
              EnglishName: "FlyNeedle",
              ThaiName: "ผลิตเข็มบิน",
              meta: {
                authority: "MES.ToolModule.FlyToolModule",
              },
              component: () => import("@/pages/gongju/feizhen"),
            },
            {
              path: "rout",
              name: "锣带制作",
              EnglishName: "RoutTool",
              ThaiName: "ผลิตแผ่นรูท",
              meta: {
                authority: "MES.ToolModule.RoutToolModule",
              },
              component: () => import("@/pages/gongju/rout"),
            },
            {
              path: "impedance",
              name: "叠层阻抗",
              EnglishName: "Impedance",
              ThaiName: "อิมพีแดนซ์",
              meta: {
                authority: "MES.ToolModule.ImpModule",
              },
              component: () => import("@/pages/gongju/impedance"),
            },
            {
              path: "fixture",
              name: "治具模块",
              EnglishName: "FixtureModule",
              ThaiName: "โมดูลฟิกซ์เจอร์",
              meta: {
                authority: "MES.ToolModule.JigManagementModule",
              },
              component: () => import("@/pages/gongju/fixture"),
            },
            {
              path: "cutting",
              name: "开料拼板",
              EnglishName: "Cutting",
              ThaiName: "ตัดและประกอบ",
              meta: {
                authority: "MES.ToolModule.Cutandmakeup",
              },
              component: () => import("@/pages/gongju/cutting"),
            },
            {
              path: "projectCase",
              name: "工程案例",
              EnglishName: "ProjectCase",
              ThaiName: "เคสวิศวกรรม",
              meta: {
                authority: "MES.ToolModule.EngineeringCase",
              },
              component: () => import("@/pages/gongju/projectCase/index.vue"),
            },
            {
              path: "CustomerRules",
              name: "客户规则",
              EnglishName: "CustomerRules",
              ThaiName: "กฎลูกค้า",
              meta: {
                authority: "MES.ToolModule.CustRule",
              },
              component: () => import("@/pages/gongju/CustomerRules/index.vue"),
            },
            {
              path: "RiskWarning",
              name: "风险警告",
              EnglishName: "RiskWarning",
              ThaiName: "เตือนความเสี่ยง",
              meta: {
                authority: "MES.ToolModule.RiskWarning",
              },
              component: () => import("@/pages/gongju/RiskWarning"),
            },
            {
              path: "Reviewinitiated",
              name: "评审审核",
              EnglishName: "ReviewAudit",
              ThaiName: "ตรวจสอบการทบทวน",
              meta: {
                authority: "MES.ToolModule.ReviewStart",
              },
              component: () => import("@/pages/gongju/Reviewinitiated"),
            },
            {
              path: "Reviewplanning",
              name: "评审策划",
              EnglishName: "ReviewPlanning",
              ThaiName: "วางแผนทบทวน",
              meta: {
                authority: "MES.ToolModule.ReviewPlan",
              },
              component: () => import("@/pages/gongju/Reviewplanning"),
            },
            {
              path: "Reviewsummary",
              name: "评审总结",
              EnglishName: "ReviewSummary",
              ThaiName: "สรุปการทบทวน",
              meta: {
                authority: "MES.ToolModule.ReviewSumUp",
              },
              component: () => import("@/pages/gongju/Reviewsummary"),
            },
            {
              path: "Reviewinquire",
              name: "评审查询",
              EnglishName: "ReviewInquire",
              ThaiName: "สอบถามการทบทวน",
              meta: {
                authority: "MES.ToolModule.ReviewSearch",
              },
              component: () => import("@/pages/gongju/Reviewinquire"),
            },
            {
              path: "Judgingcontent",
              name: "评审内容",
              EnglishName: "JudgingContent",
              ThaiName: "เนื้อหาการทบทวน",
              meta: {
                invisible: true,
                authority: "MES.ToolModule.ReviewInfo",
              },
              component: () => import("@/pages/gongju/Judgingcontent"),
            },
          ],
        },
        {
          path: "sight",
          name: "sight",
          EnglishName: "Sight",
          ThaiName: "Sight",
          meta: {
            icon: "snippets",
            invisible: true,
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "AuditParameters",
              name: "审核参数",
              EnglishName: "AuditParams",
              ThaiName: "พารามิเตอร์ตรวจสอบ",
              meta: {
                link: "/AuditParameters",
                invisible: true,
              },
            },
            {
              path: "AuditParametersMkt",
              name: "审核参数",
              EnglishName: "AuditParamsMkt",
              ThaiName: "พารามิเตอร์ตรวจสอบตลาด",
              meta: {
                link: "/AuditParametersMkt",
                invisible: true,
              },
            },
            {
              path: "drillStructure",
              name: "钻孔结构",
              EnglishName: "DrillStructure",
              ThaiName: "โครงสร้างเจาะ",
              meta: {
                link: "/drillStructure",
                invisible: true,
              },
            },
            {
              path: "Goldfinger",
              name: "金手指计算",
              EnglishName: "GoldfingerCalc",
              ThaiName: "คำนวณ Goldfinger",
              meta: {
                link: "/Goldfinger",
                invisible: true,
              },
            },
          ],
        },
        {
          path: "OrderManagement",
          name: "生产管理",
          EnglishName: "Production",
          ThaiName: "จัดการการผลิต",
          meta: {
            icon: "cluster",
            authority: "MES.ProManagement",
          },
          component: () => import("@/layouts/BlankView"),
          children: [
            {
              path: "CompositionManagement",
              name: "投料列表",
              EnglishName: "FeedingList",
              ThaiName: "รายการวัตถุดิบ",
              meta: {
                authority: "MES.ProManagement.Feeding",
              },
              component: () => import("@/pages/OrderManagement/CompositionManagement"),
            },
            {
              path: "EQManagement",
              name: "EQ列表",
              EnglishName: "EQList",
              ThaiName: "รายการ EQ",
              meta: {
                authority: "MES.ProManagement.EQList",
              },
              component: () => import("@/pages/OrderManagement/EQManagement"),
            },
            {
              path: "PackagingStorage",
              name: "包装入库",
              EnglishName: "PackagingStorage",
              ThaiName: "บรรจุและจัดเก็บ",
              meta: {
                authority: "MES.ProManagement.WarehousingList",
              },
              component: () => import("@/pages/OrderManagement/PackagingStorage"),
            },
            {
              path: "ReconciliationList",
              name: "采购对账",
              EnglishName: "ReconciliationList",
              ThaiName: "ตรวจสอบการซื้อ",
              meta: {
                authority: "MES.ProManagement.ReconcileList",
              },
              component: () => import("@/pages/OrderManagement/ReconciliationList"),
            },
            {
              path: "orderDetail1",
              name: "订单详情",
              EnglishName: "OrderDetail",
              ThaiName: "รายละเอียดออเดอร์",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/OrderManagement/orderDetail"),
            },
            // {
            //   path: 'ControlCard',
            //   name: '管制卡',
            //   meta:{
            //     authority:'MES.ProManagement.ControlCard'
            //   },
            //   component: () => import('@/pages/scgl/OrderManagement/ControlCard')
            // },
            {
              path: "ProgressManagement",
              name: "生产进度",
              EnglishName: "ProductionProgress",
              ThaiName: "ความคืบหน้าผลิต",
              meta: {
                authority: "MES.ProManagement.ProductionProgress",
              },
              component: () => import("@/pages/OrderManagement/ProgressManagement"),
            },
            {
              path: "cardDetail",
              name: "管制卡详情",
              EnglishName: "ControlCardDetail",
              ThaiName: "รายละเอียดการ์ดควบคุม",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/OrderManagement/cardDetail"),
            },
            {
              path: "Supersequence",
              name: "部门过序",
              EnglishName: "DeptSequence",
              ThaiName: "ลำดับแผนก",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/OrderManagement/Supersequence"),
            },
            {
              path: "mergeCard",
              name: "工单合卡",
              EnglishName: "WorkorderCombination",
              ThaiName: "รวมใบงาน",
              meta: {
                authority: "MES.ProManagement.Workordercombination",
              },
              component: () => import("@/pages/OrderManagement/mergeCard"),
            },
            {
              path: "Initialreview",
              name: "客诉初审",
              EnglishName: "FirstReview",
              ThaiName: "ทบทวนครั้งแรก",
              meta: {
                authority: "MES.ProManagement.CustComplaintFirstReviewList",
              },
              component: () => import("@/pages/OrderManagement/Initialreview"),
            },
            {
              path: "Supplierreview",
              name: "客诉复核",
              EnglishName: "ResultReview",
              ThaiName: "ทบทวนผล",
              meta: {
                authority: "MES.ProManagement.CustComplaintResultReviewList",
              },
              component: () => import("@/pages/OrderManagement/Supplierreview"),
            },
            {
              path: "Reviewdetails",
              name: "供应商复核详情",
              EnglishName: "SupplierReviewDetail",
              ThaiName: "รายละเอียดทบทวนซัพพลายเออร์",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/OrderManagement/Supplierreview/Details/Reviewdetails"),
            },
            {
              path: "Initialreviewdetails",
              name: "初审详情",
              EnglishName: "FirstReviewDetail",
              ThaiName: "รายละเอียดทบทวนครั้งแรก",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/OrderManagement/Initialreview/Details/Initialreviewdetails"),
            },
          ],
        },
        {
          path: "QualityManagement",
          name: "品质管理",
          EnglishName: "Quality",
          ThaiName: "จัดการคุณภาพ",
          meta: {
            icon: "container",
            authority: "MES.QualityManagement",
          },
          component: () => import("@/layouts/BlankView"),
          children: [
            {
              path: "Scrap",
              name: "报废管理",
              EnglishName: "Scrap",
              ThaiName: "จัดการของเสีย",
              meta: {
                authority: "MES.QualityManagement.Scrapmanagement",
              },
              component: () => import("@/pages/QualityManagement/ScrapManagement"),
            },
            {
              path: "VoidEntry",
              name: "作废录入",
              EnglishName: "VoidEntry",
              ThaiName: "บันทึกการยกเลิก",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/QualityManagement/VoidEntry"),
            },
            {
              path: "ShippingReport",
              name: "出货报告",
              EnglishName: "ShippingReport",
              ThaiName: "รายงานจัดส่ง",
              meta: {
                authority: "MES.QualityManagement.ShipmentReport",
              },
              component: () => import("@/pages/QualityManagement/ShippingReport"),
            },
            {
              path: "SliceReport",
              name: "切片报告",
              EnglishName: "SliceReport",
              ThaiName: "รายงานสไลซ์",
              meta: {
                authority: "MES.QualityManagement.SliceReport",
              },
              component: () => import("@/pages/QualityManagement/SliceReport"),
            },
            {
              path: "Impedancereport",
              name: "阻抗报告",
              EnglishName: "ImpedanceReport",
              ThaiName: "รายงานอิมพีแดนซ์",
              meta: {
                authority: "MES.QualityManagement.ImpedanceReport",
              },
              component: () => import("@/pages/QualityManagement/Impedancereport"),
            },
            {
              path: "QualitativeComplaint",
              name: "客诉定性",
              EnglishName: "CustComplaintQualitative",
              ThaiName: "กำหนดลักษณะการร้องเรียน",
              meta: {
                authority: "MES.QualityManagement.CustComplaintQualitativeList",
              },
              component: () => import("@/pages/complaint/QualitativeComplaint"),
            },
            {
              path: "ComplaintFactory",
              name: "投诉工厂",
              EnglishName: "ComplaintFactory",
              ThaiName: "ร้องเรียนโรงงาน",
              meta: {
                authority: "MES.QualityManagement.FactoryComplaintList",
              },
              component: () => import("@/pages/complaint/ComplaintFactory"),
            },
            {
              path: "CustomerComplaintreview",
              name: "客诉复核",
              EnglishName: "CustComplaintReview",
              ThaiName: "ทบทวนการร้องเรียน",
              meta: {
                authority: "MES.QualityManagement.QualityCustComplaintResultReviewList",
              },
              component: () => import("@/pages/complaint/CustomerComplaintreview"),
            },
            {
              path: "Reviewdetails",
              name: "品质复核详情",
              EnglishName: "QualityReviewDetail",
              ThaiName: "รายละเอียดทบทวนคุณภาพ",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/complaint/CustomerComplaintreview/Details/Reviewdetails"),
            },
            {
              path: "PersonalCharacterization",
              name: "个人定性",
              EnglishName: "PersonalCharacterization",
              ThaiName: "กำหนดลักษณะส่วนบุคคล",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/complaint/QualitativeComplaint/PersonalCharacterization"),
            },
            {
              path: "Addcomplaint",
              name: "新增投诉",
              EnglishName: "AddComplaint",
              ThaiName: "เพิ่มการร้องเรียน",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/complaint/ComplaintFactory/Details/Addcomplaint"),
            },
            {
              path: "Waitingforreply",
              name: "投诉详情",
              EnglishName: "ComplaintDetail",
              ThaiName: "รายละเอียดการร้องเรียน",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/complaint/ComplaintFactory/Details/Waitingforreply"),
            },
            {
              path: "Platformevaluation",
              name: "评价详情",
              EnglishName: "PlatformEvaluation",
              ThaiName: "รายละเอียดการประเมิน",
              meta: {
                invisible: true,
              },
              component: () => import("@/pages/complaint/ComplaintFactory/Details/Platformevaluation"),
            },
          ],
        },

        {
          path: "productionManagement",
          name: "车间管理",
          EnglishName: "Production",
          ThaiName: "การจัดการการผลิต",
          meta: {
            icon: "gold",
            authority: "MES.ProductionModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            // {
            //   path: "cuttingManagement",
            //   name: "开料管理",
            //   meta: {
            //     authority: "MES.ProductionModule.Cutting",
            //   },
            //   component: () => import("@/pages/productionManagement/CuttingManagement"),
            // },
            {
              path: "LaminatedEdges",
              name: "层压锣边",
              EnglishName: "LaminatedEdges",
              ThaiName: "การตัดขอบแผ่น",
              meta: {
                authority: "MES.ProductionModule.LaminatedEdges",
              },
              component: () => import("@/pages/productionManagement/LaminatedEdges"),
            },
            {
              path: "drillbittoolholder",
              name: "钻咀配刀",
              EnglishName: "DrillbitToolholder",
              ThaiName: "เครื่องมือเจาะ",
              meta: {
                authority: "MES.ProductionModule.Drillbittoolholder",
              },
              component: () => import("@/pages/productionManagement/drillbittoolholder"),
            },
            {
              path: "drillHoleManagement",
              name: "钻孔管理",
              EnglishName: "DrillHole",
              ThaiName: "การจัดการการเจาะรู",
              meta: {
                authority: "MES.ProductionModule.DrillHole",
              },
              component: () => import("@/pages/productionManagement/DrillHoleManagement"),
            },
            {
              path: "Holeinspectionnew",
              name: "验孔管理",
              EnglishName: "HoleInspection",
              ThaiName: "การตรวจสอบรู",
              meta: {
                authority: "MES.ProductionModule.CheckDrill",
              },
              component: () => import("@/pages/productionManagement/Holeinspectionnew"),
            },
            {
              path: "LineManagement",
              name: "线路管理",
              EnglishName: "Line",
              ThaiName: "การจัดการสาย",
              meta: {
                authority: "MES.ProductionModule.Line",
              },
              component: () => import("@/pages/productionManagement/LineManagement"),
            },
            {
              path: "AOIManagement",
              name: "AOI管理",
              EnglishName: "AOIManagement",
              ThaiName: "การจัดการAOI",
              meta: {
                authority: "MES.ProductionModule.AOI",
              },
              component: () => import("@/pages/productionManagement/AOIManagement"),
            },
            {
              path: "PlugholeManagement",
              name: "塞孔管理",
              EnglishName: "Plughole",
              ThaiName: "รูปลั๊ก",
              meta: {
                authority: "MES.ProductionModule.Plugholes",
              },
              component: () => import("@/pages/productionManagement/PlugholeManagement"),
            },
            {
              path: "SolderManagement",
              name: "阻焊管理",
              EnglishName: "Solder",
              ThaiName: "การจัดการการ땜",
              meta: {
                authority: "MES.ProductionModule.Solder",
              },
              component: () => import("@/pages/productionManagement/SolderManagement"),
            },
            // {
            //   path: "grindingPlate",
            //   name: "磨板管理",
            //   meta: {
            //     authority: "MES.ProductionModule.GrindingPlate",
            //   },
            //   component: () => import("@/pages/productionManagement/grindingPlate"),
            // },
            {
              path: "Charactermanagement",
              name: "字符管理",
              EnglishName: "Character",
              ThaiName: "การจัดการตัวอักษร",
              meta: {
                authority: "MES.ProductionModule.Character",
              },
              component: () => import("@/pages/productionManagement/Charactermanagement"),
            },
            // {
            //   path: 'CharacterManage',
            //   name: '字符管理',
            //   meta: {
            //     authority: 'MES.ProductionModule.Character',
            //   },
            //   component: () => import('@/pages/productionManagement/CharacterManage'),
            // },
            {
              path: "flyingNeedle",
              name: "飞针管理",
              EnglishName: "FlyingNeedle",
              ThaiName: "การจัดการเข็มบิน",
              meta: {
                authority: "MES.ProductionModule.FlyingNeedle",
              },
              component: () => import("@/pages/productionManagement/flyingNeedle"),
            },
            {
              path: "Formingmanagementnew",
              name: "成型管理",
              EnglishName: "Forming",
              ThaiName: "การจัดการการขึ้นรูป",
              meta: {
                authority: "MES.ProductionModule.Forming",
              },
              component: () => import("@/pages/productionManagement/Formingmanagementnew"),
            },
            {
              path: "Vcutmanagement",
              name: "VCUT管理",
              EnglishName: "Vcut",
              ThaiName: "การจัดการการตัดV",
              meta: {
                authority: "MES.ProductionModule.Vcut",
              },
              component: () => import("@/pages/productionManagement/Vcutmanagement"),
            },
            // {
            //   path: 'formingManagement',
            //   name: '成型管理',
            //   meta: {
            //     authority: 'MES.ProductionModule.Forming',
            //   },
            //   component: () => import('@/pages/productionManagement/formingManagement'),
            // },
          ],
        },
        {
          path: "materialmodule",
          name: "物料管理",
          EnglishName: "Material",
          ThaiName: "การจัดการวัสดุ",
          meta: {
            icon: "robot",
            authority: "MES.MaterialModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "categoryluru",
              name: "型号录入",
              EnglishName: "TypeEntry",
              ThaiName: "การบันทึกประเภท",
              meta: {
                authority: "MES.MaterialModule.Type",
              },
              component: () => import("@/pages/materialmodule/category"),
            },
            {
              path: "MaterialEntry",
              name: "物料录入",
              EnglishName: "MaterialEntry",
              ThaiName: "การบันทึกวัสดุ",
              meta: {
                authority: "MES.MaterialModule.Material",
              },
              component: () => import("@/pages/materialmodule/MaterialEntry"),
            },
          ],
        },
        {
          path: "reportManagement",
          name: "报表管理",
          EnglishName: "Report",
          ThaiName: "การจัดการรายงาน",
          meta: {
            icon: "file-excel",
            authority: "MES.ReportModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "reportList",
              name: "报表列表",
              EnglishName: "ReportList",
              ThaiName: "รายการรายงาน",
              meta: {
                authority: "MES.ReportModule.ReportManager",
              },
              component: () => import("@/pages/reportPage/ReportList"),
            },
          ],
        },
        {
          path: "system",
          name: "系统管理",
          EnglishName: "System",
          ThaiName: "การจัดการระบบ",
          meta: {
            icon: "setting",
            authority: "MES.SysModule",
          },
          component: () => import("@/layouts/PageView"),
          children: [
            {
              path: "user",
              name: "用户管理",
              EnglishName: "User",
              ThaiName: "การจัดการผู้ใช้",
              component: () => import("@/pages/system/UserList"),
            },
            {
              path: "role",
              name: "角色管理",
              EnglishName: "Role",
              ThaiName: "การจัดการบทบาท",
              meta: {
                authority: "MES.SysModule.RoleManagement",
              },
              component: () => import("@/pages/system/RoleList"),
            },
            {
              path: "Spec",
              name: "Spec更新",
              EnglishName: "SpecUpdate",
              ThaiName: "การอัปเดตข้อมูลจำเพาะ",
              meta: {
                authority: "MES.SysModule.Specmanagement",
              },
              component: () => import("@/pages/system/SpecList"),
            },
            // {
            //   path: "ipcb",
            //   name: "iPCB任务监控",
            //   meta: {
            //     authority: "MES.SysModule.IpcbTask",
            //   },
            //   component: () => import("@/pages/system/ipcbList"),
            // },
            {
              path: "newipcb",
              name: "iPCB任务监控",
              EnglishName: "IPCBTaskMonitor",
              ThaiName: "การติดตามงานiPCB",
              meta: {
                authority: "MES.SysModule.IpcbTask",
              },
              component: () => import("@/pages/system/newipcb"),
            },
            {
              path: "organizationUnits",
              name: "身份标识管理",
              EnglishName: "Identity",
              ThaiName: "การจัดการตัวตน",
              meta: {
                authority: "AbpIdentity.OrganizationUnits",
                invisible: true,
              },
              component: () => import("@/layouts/BlankView"),
              children: [
                {
                  path: "organization",
                  name: "组织机构",
                  EnglishName: "Organization",
                  ThaiName: "หน่วยงาน",
                  meta: {
                    authority: "AbpIdentity.OrganizationUnits",
                    invisible: true,
                  },
                  component: () => import("@/pages/system/organizationUnits/organization"),
                },
                {
                  path: "claimTypes",
                  name: "声明类型",
                  EnglishName: "ClaimTypes",
                  ThaiName: "ประเภทการอ้างสิทธิ์",
                  meta: {
                    authority: "AbpIdentity.ClaimTypes",
                    invisible: true,
                  },
                  component: () => import("@/pages/system/organizationUnits/claimType/claimTypes"),
                },
                {
                  path: "securityLogs",
                  name: "安全日志",
                  EnglishName: "SecurityLogs",
                  ThaiName: "บันทึกความปลอดภัย",
                  meta: {
                    authority: "AbpIdentity.ClaimTypes",
                    invisible: true,
                  },
                  component: () => import("@/pages/system/organizationUnits/claimType/securityLogs"),
                },
              ],
            },
            {
              path: "tenant",
              name: "租户管理",
              EnglishName: "Tenant",
              ThaiName: "การจัดการผู้เช่า",
              meta: {
                authority: "Saas.Tenants",
                invisible: true,
              },
              component: () => import("@/pages/system/TenantList"),
            },
            {
              path: "script",
              name: "脚本管理",
              EnglishName: "Script",
              ThaiName: "การจัดการสคริปต์",
              meta: {
                authority: "MES.SysModule.ScriptManagement",
              },
              component: () => import("@/pages/system/ScriptManage"),
            },
            {
              path: "edition",
              name: "版本管理",
              EnglishName: "Edition",
              ThaiName: "การจัดการเวอร์ชัน",
              meta: {
                authority: "Saas.Editions",
                invisible: true,
              },
              component: () => import("@/pages/system/editionManage"),
            },
            // {
            //   path: 'business',
            //   name: '企业管理',
            //   meta: {
            //     //authority: 'Saas.Tenants',
            //   },
            //   component: () => import('@/pages/system/business'),
            // },
          ],
        },
      ],
    },
  ],
};

export default options;
