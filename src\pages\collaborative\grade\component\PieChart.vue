<template>
  <div>
    <div className="pie">
      <div id="pie1">
        <!-- 为 ECharts 准备一个具备大小（宽高）的 DOM -->
        <div :id="el" style="float:left;width:100%;height: 300px"></div>
      </div>
    </div>
  </div>
</template>

<script>// 引入基本模板
let echarts = require('echarts/lib/echarts')
// 引入饼状图组件
require('echarts/lib/chart/pie')
require('echarts/lib/component/legend');
// 引入提示框和title组件
require('echarts/lib/component/tooltip')
require('echarts/lib/component/title')


export default {
  created() {
  },
  mounted() {
    this.initData();
  },
  props:['el','echartdata'],
  methods: {
    //初始化数据
    initData() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById(this.el));
      // console.log(this.echartdata)
      // 绘制图表
      myChart.setOption({
        // title: {
        //   text: '协同品类分布',//主标题
        //   x: 'center',//x轴方向对齐方式
        // },
        tooltip: {
          trigger: 'item',
          formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
        legend: {
          orient: 'vertical',
          right: '10%',
          data: this.echartdata.filter(item => {return item.name})
        },
        series: [
          {
            name: '品类分布',
            type: 'pie',
            radius: '55%',
            center: ['50%', '30%'],
            label:{            //饼图图形上的文本标签
              normal:{
                show:true,
                position:'inner', //标签的位置
                textStyle : {
                  fontWeight : 300 ,
                },
                formatter:'{b}:{d}%'
              }
            },
            data: this.echartdata,
            itemStyle: {
              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });
    },
  }
}
</script>