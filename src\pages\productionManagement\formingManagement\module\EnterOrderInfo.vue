<!-- 车间管理-成型管理-录入订单 -->
<template>
  <div ref="SelectBox">
    <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 12 }" :form="enterOrderForm">
    <a-form-item label="大料数量">
      <a-input  type='number' v-model='enterOrderForm.count4Pnl_'  :autoFocus='autoFocus' v-focus-next-on-enter="'input2'" ref="input1"/>
    </a-form-item>
    <a-form-item label="合拼款数">
      <a-input type='number'  v-model='enterOrderForm.count4Hp_' v-focus-next-on-enter="'input3'" ref="input2"/>
    </a-form-item> 
    <a-form-item label="长边mm">
      <a-input type='number'  v-model='enterOrderForm.lSize' v-focus-next-on-enter="'input4'" ref="input3"/>
    </a-form-item>
    <a-form-item label="宽边mm">
      <a-input type='number'  v-model='enterOrderForm.wSize' v-focus-next-on-enter="'input5'" ref="input4"/>
    </a-form-item>         
    <a-form-item label="锣程">
      <a-input allowClear type='string'  v-model='enterOrderForm.routC_' v-focus-next-on-enter="'input6'" ref="input5" />
    </a-form-item>
    <a-form-item label="板厚">
      <a-input type='number'  v-model='enterOrderForm.boardthick' v-focus-next-on-enter="'input7'" ref="input6"/>
    </a-form-item>   
    <a-form-item label="交货日期" >
       <a-date-picker allowClear v-model='enterOrderForm.delDate' class='picker' />
    </a-form-item> 
    <a-form-item label="文件路径"  > 
      <a-input style='width:180px;'  v-model='enterOrderForm.tgzOssPath'>
      </a-input>   
      <a-upload 
        accept=".rou"
        :show-upload-list="false"
        class="avatar-uploader"
        :customRequest="downloadFilesCustomRequest"
        :multiple="false"
        :before-upload="beforeUpload1"

      >              
        <a-button > <a-icon type="upload" /></a-button>
      </a-upload>
       
    </a-form-item>
    <a-form-item label="Tgz路径"  > 
      <a-input style='width:180px;'  v-model='enterOrderForm.ossRouPathTGZ_'>
      </a-input>   
      <a-upload 
        accept=".tgz"
        :show-upload-list="false"
        class="avatar-uploader"
        :customRequest="downloadFilesCustomRequest1"
        :multiple="false"
        :before-upload="beforeUpload2"

      >              
        <a-button > <a-icon type="upload" /></a-button>
      </a-upload>
       
    </a-form-item>
    <a-form-item label="订单工厂">
      <a-select 
        allowClear
        show-search
        option-filter-prop="children"
        :filter-option="filterOption"
        v-model='enterOrderForm.fac'
        :getPopupContainer="()=>this.$refs.SelectBox">
        <a-select-option v-for="(item,index) in factoryList" :key="index" :value="item.text">
          {{item.text}}
        </a-select-option>
      </a-select>
    </a-form-item>
   
  </a-form>
  </div>
  
</template>

<script>
import { getFactoryList,UploadFile,UploadFileTgz,} from '@/services/forming-management'
export default {
    name:'EnterOrderInfo',
    props:[],
  data() {
    return {
      factoryList: [],
      autoFocus:true,
      enterOrderForm:{
        count4Pnl_: '',              // 大料数量
        count4Hp_: '',               // 合拼款数
        lSize: '',                  // 长边(mm)
        wSize: '',                  // 宽边(mm)
        delDate: '',               // 交货日期
        routC_: '',                // 锣程
        boardthick: '',            // 板厚
        tgzOssPath:'',             // 文件路径
        fac: '',                   // 订单工厂
        ossRouPathTGZ_:'',         // TGZ路径
        pdctno: '',                // 订单编号 
        pdctnoTgz:'',              // 订单编号Tgz 
      },
    };
  },
  methods: { 
     selectClick(){
      getFactoryList().then(res =>{
       if (res.code == 1) {
          this.factoryList = res.data   
        }else{
          this.$message.error(res.message)
        }
      })      
    },
    // rou路径
    downloadFilesCustomRequest(data){      
      const formData = new FormData()
      formData.append('file', data.file)
      UploadFile(formData).then(res =>{
        if (res.code == 1) {
          this.enterOrderForm.tgzOssPath = res.data          
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
        var str = this.enterOrderForm.tgzOssPath
        var arr = str.split('/')
        var newstr_ = arr[arr.length-1]
        this.enterOrderForm.pdctno = newstr_.split('_')[0]
        console.log( this.enterOrderForm.pdctno)
        console.log(this.enterOrderForm.pdctno,this.enterOrderForm.tgzOssPath)
      })      
    },
    beforeUpload1(file){
      const _this = this
      return new Promise(function(resolve, reject){
        const isFileType = file.name.toLowerCase().indexOf('.rou') != -1        
        const isFileName = escape(file.name).indexOf("%u") != -1 
        // console.log("str_esc:",str_esc)         
      if (!isFileType) {
        _this.$message.error('只支持.rou格式文件');
        reject()
        // return isFileType
      }else if (isFileName){        
        _this.$message.error('上传的文件名不能包含中文'); 
        reject()                   
      } else{
        resolve() 
      }   
      })
    },
    // tgz路径
    downloadFilesCustomRequest1(data){      
      const formData = new FormData()
      formData.append('file', data.file)
      UploadFileTgz(formData).then(res =>{
        if (res.code == 1) {
          this.enterOrderForm.ossRouPathTGZ_ = res.data          
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
        var str = this.enterOrderForm.ossRouPathTGZ_
        var arr = str.split('/')
        var newstr_ = arr[arr.length-1]
        this.enterOrderForm.pdctnoTgz = newstr_.split('_')[0]
        console.log( this.enterOrderForm.pdctnoTgz)
        console.log(this.enterOrderForm.pdctnoTgz,this.enterOrderForm.ossRouPathTGZ_)
      })      
    },
    beforeUpload2(file){
      const _this = this
      return new Promise(function(resolve, reject){
        const isFileType = file.name.toLowerCase().indexOf('.tgz') != -1        
        const isFileName = escape(file.name).indexOf("%u") != -1 
        // console.log("str_esc:",str_esc)         
      if (!isFileType) {
        _this.$message.error('只支持.tgz格式文件');
        reject()
        // return isFileType
      }else if (isFileName){        
        _this.$message.error('上传的文件名不能包含中文'); 
        reject()                   
      } else{
        resolve() 
      }   
      })
    },
    SpecialMaterial(){
      this.isSpecialMaterial = !this.isSpecialMaterial
    },
    
    filterOption(input, option) {
      return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
  mounted () {
    this.selectClick()
  }
}
</script>
<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
 color:#000000;
}

.picker{
  width:226px;
}
.ant-form-item{
  margin-bottom: 0;  
}
.ant-form-item-children{
    width:180px;
    border-right: 0;
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  } 
.ant-upload{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.avatar-uploader > .ant-upload {  
  position:absolute;
  height: 32px;
}
.ant-upload-select-picture-card i {
  font-size: 12px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
 
  color: #666;
}


</style>