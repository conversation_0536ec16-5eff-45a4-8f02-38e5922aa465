<!-- 工程管理 - 锣带分派  -查询 -->
<template>
  <div ref="SelectBox">
    <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="生产编号">
      <a-input   v-model='OrderNumber' placeholder="请输入生产编号" allowClear :autoFocus="autoFocus" v-focus-next-on-enter="'select1'" ref="input1"/>
    </a-form-item>
    <!-- <a-form-item label="订单工厂">
      <a-select
          show-search
          allowClear
          ref="select1"
          v-model="OrderFac"
          :getPopupContainer="()=>this.$refs.SelectBox"
        >
        <a-select-option v-for="(item,index) in factoryData" :key="index" :value="item.text">
          {{item.text}}
        </a-select-option>
        </a-select>
    </a-form-item> -->
  </a-form>
  </div>
 
</template>

<script>

export default {
    name:'QueryInfoBackend',
  props:['factoryData'],
  data() {
    return {
      OrderNumber:'',
      OrderFac:'',
      factoryList: [],
      autoFocus:true
    };
  },
  methods: {

  },
  mounted() {

  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
};
</script>
<style lang="less"  scoped>
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
}

/deep/.ant-input{
  font-weight: 500;
}
</style>
