<template>
  <div>
    <div class="text-center">
      <vue2-org-tree name="test"
        :data="departGroupPostDto"
        :horizontal="true"
        :collapsable="false"
        :label-class-name="labelClassName"
        :render-content="renderContent"
        @on-expand="onExpand"
        @on-node-click="onNodeClick"
      />
    </div> 
     <!-- 新增弹窗 -->
   <a-modal
          :title="key==1? '新增':'编辑'"
          :visible="dataVisible"
          @cancel="reportHandleCancel"
          @ok="handleOk"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
   >
   <a-form-model :label-col="{ span:7 }" :wrapper-col="{ span: 14}" :rules="rules"  ref="ruleForm"  :model="form">
    <a-form-model-item label="部门名称" v-if="level == '0'" ref="departmentName" prop="departmentName">
      <a-input  v-model='form.departmentName' placeholder="请输入部门名称"  :autoFocus="autoFocus"   />     
    </a-form-model-item> 
    <a-form-model-item label="小组名称" v-if="level == 'D'&& key == 1"  prop="groupName">
      <a-input   v-model='form.groupName' placeholder="请输入小组名称" :autoFocus="autoFocus"  />    
    </a-form-model-item> 
    <a-form-model-item label="部门名称" v-if="level == 'D'&& key == 2"  prop="departmentName">
      <a-input  v-model='form.departmentName' placeholder="请输入部门名称" :autoFocus="autoFocus"  />
    </a-form-model-item> 
    <a-form-model-item label="岗位名称" v-if="level == 'G'&& key == 1" prop="postName">
      <a-input   v-model='form.postName' placeholder="请输入岗位名称" :autoFocus="autoFocus" />
    </a-form-model-item> 
    <a-form-model-item label="小组名称" v-if="level == 'G'&& key == 2" prop="groupName">
      <a-input  v-model='form.groupName' placeholder="请输入小组名称" :autoFocus="autoFocus"  />
    </a-form-model-item> 
    <a-form-model-item label="岗位名称" v-if="level == 'P'" prop="postName">
      <a-input  v-model='form.postName' placeholder="请输入岗位名称" :autoFocus="autoFocus" />
    </a-form-model-item>     
  </a-form-model>   
   </a-modal>   
  </div>
</template>
<script> 
import {AddDepart,AddGroup,AddPost,updateDepart,updateGroup,updatePost,} from '@/services/supplier/index' // 页面转换图片下载插件 写在需要下载的页面
export default {
  props: ['departGroupPostDto','factoryId'],
  data () {
    return {      
      // 背景色
      labelClassName: "bg-none",
      title:'',
      dataVisible:false,
      autoFocus:true,
      level:'', 
      form:{
        departmentName:'', // 部门
        groupName:'', // 小组
        postName:'', // 岗位
      },      
      clickData:{},
      rules: {
        departmentName: [
          { required: true, message: "部门名称必须填写", trigger: "blur" },
        ],
        groupName: [
          { required: true, message: "小组名称必须填写", trigger: "blur" },
        ],
        postName: [
          { required: true, message: "岗位名称必须填写", trigger: "blur" },
        ],
      },
      key:1,
        
    };
  },
  computed: {},
  async created () { },
  async mounted () {
    console.log('departGroupPostDto',this.departGroupPostDto)
  },
  methods: {
    // <a-menu-item v-show={data.level !== '0'} key={'add'}>添加同级部门</a-menu-item>
    // <a-menu-item key={'edit'}>修改部门</a-menu-item>
    //<a-menu-item key={'del'} >删除部门</a-menu-item >
    //<a-menu slot="overlay" onclick={(key) => this.onClick(key, data)}>          
    //    <a-menu-item v-show={data.level !== 'P'} key={'addChild'}>添加下级部门</a-menu-item>          
    //      </a-menu >
    renderContent(h, data) {
      return (<span><a-dropdown trigger={['click']}>
        <span style='color:#595f71'>
          {data.label} 
          <a style='color:#FE9900'>
            <a-tooltip title="添加下级" v-show={data.level !== 'P'}>
              <a-icon type='plus'  onclick={() => this.onClick(1, data)} style='margin-left:5px;'></a-icon>
            </a-tooltip>
            <a-tooltip title="编辑" v-show={data.level !== '0'} >
              <a-icon type='edit'  onclick={() => this.onClick(2, data)} style='margin-left:5px;'></a-icon>
            </a-tooltip>
          </a>
        </span>
        </a-dropdown ></span >)      
    },
    onExpand(data) {
      if ("expand" in data) {
        data.expand = !data.expand;
        if (!data.expand && data.children) {
          this.collapse(data.children);
        }
      } else {
        this.$set(data, "expand", true);
      }
    },
    onClick(key,data){       
      this.key = key  
      this.clickData = data           
      this.level = data.level
      this.dataVisible = true      
      if(key == 2){
        console.log(data)
        if(data.level == 'D'){
          this.form.departmentName = data.label
        }
        if(data.level == 'G'){
          this.form.groupName = data.label          
        }
        if(data.level == 'P'){
          this.form.postName = data.label          
        }
      }
    },
    handleOk(){
      if(this.key == 1){
        if(this.level == '0' && this.form.departmentName){
          let params={
              "factoryId": this.factoryId,
              "departmentName": this.form.departmentName
            }
          AddDepart(params).then(res=>{          
            if(res.code){
              this.$message.success('添加成功')
              this.$emit('getSupData')
              this.form.departmentName = ''
            }else{
              this.$message.error(res.message)
            }
          }).finally(()=>{
            this.dataVisible = false
          })
        }
        if(this.level == 'D' && this.form.groupName){
          let params={
              "factoryId": this.factoryId,
              "departId": this.clickData.id,
              "groupName": this.form.groupName
            }
            AddGroup(params).then(res=>{          
            if(res.code){
              this.$message.success('添加成功')
              this.$emit('getSupData')
              this.form.groupName = ''
            }else{
              this.$message.error(res.message)
            }
          }).finally(()=>{
            this.dataVisible = false
          })
        }
        if(this.level == 'G' && this.form.postName){
          let params={
              "factoryId": this.factoryId,
              "groupId": this.clickData.id,
              "postName": this.form.postName
            }
            AddPost(params).then(res=>{          
            if(res.code){
              this.$message.success('添加成功')
              this.$emit('getSupData')
              this.form.postName = ''
            }else{
              this.$message.error(res.message)
            }
          }).finally(()=>{
            this.dataVisible = false
          })
        }
      }else{
        if(this.level == 'D' && this.form.departmentName){
          let params={
              "id": this.clickData.id,
              "departmentName": this.form.departmentName
            }
            console.log('部门',params)
            updateDepart(params).then(res=>{          
            if(res.code){
              this.$message.success('编辑成功')
              this.$emit('getSupData')
              this.form.departmentName = ''
            }else{
              this.$message.error(res.message)
            }
          }).finally(()=>{
            this.dataVisible = false
          })
        }
        if(this.level == 'G' && this.form.groupName){
          let params={
              "id": this.clickData.id,
              "groupName": this.form.groupName
            }
            console.log('小组',params)
            updateGroup(params).then(res=>{          
            if(res.code){
              this.$message.success('编辑成功')
              this.$emit('getSupData')
              this.form.groupName = ''
            }else{
              this.$message.error(res.message)
            }
          }).finally(()=>{
            this.dataVisible = false
          })
        }
        if(this.level == 'P' && this.form.postName){
          let params={           
              "id": this.clickData.id,
              "postName": this.form.postName
            }
            console.log('岗位',params)
            updatePost(params).then(res=>{          
            if(res.code){
              this.$message.success('编辑成功')
              this.$emit('getSupData')
              this.form.postName = ''
            }else{
              this.$message.error(res.message)
            }
          }).finally(()=>{
            this.dataVisible = false
          })
        }
      }
      
      
    },
    reportHandleCancel(){
      this.dataVisible = false
      this.form.departmentName = ''
      this.form.groupName = ''
      this.form.postName = ''      
    },
    onNodeClick(e, data) {
      // alert(data.label);
    },
    collapse(list) {
      var _this = this;
      list.forEach(function(child) {
        if (child.expand) {
          child.expand = false;
        }
        child.children && _this.collapse(child.children);
      });
    },
    expandChange() {
      this.toggleExpand(this.data, this.expandAll);
    },
    toggleExpand(data, val) {
      var _this = this;
      if (Array.isArray(data)) {
        data.forEach(function(item) {
          _this.$set(item, "expand", val);
          if (item.children) {
            _this.toggleExpand(item.children, val);
          }
        });
      } else {
        this.$set(data, "expand", val);
        if (data.children) {
          _this.toggleExpand(data.children, val);
        }
      }
    }
  }
}
</script>
<style lang='less'>

.org-tree-container {
  background: none !important;
  float:left;
}
// .org-tree-node-label-inner:hover{
//   background-color: #e6c670;
// }
.org-tree-node-label {
  white-space: nowrap;
}
.bg-none {
  background-color: #FFE7A3;
  color: #fff;
}
.bg-white {
  background-color: #ecf5ff;
}
.org-tree-node-label .org-tree-node-label-inner {
  width: 13vw;
  min-width: 249px;
  padding:2px 0px;
  text-align: center;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.15);
  /* border: 1px solid @colors;*/
  overflow: hidden;
  box-sizing: border-box;
}
.bg-tomato {
  background-color: #595f71;
}
.bg-gold {
  background-color: #fd6969;
}
.bg-gray {
  background-color: #deceaa;
}
.bg-lightpink {
  background-color: lightpink;
}
.bg-blue {
  background-color: #057d9f;
}
.bg-green {
  background-color: #409eff;
}
</style>
<style scoped>
::v-deep .horizontal .org-tree-node:before{
  top: 0 !important;
  border-color: black!important;
}
::v-deep .horizontal .org-tree-node-children:before{
  top: 49.8% !important;
  border-color: black!important;
}
::v-deep .horizontal .org-tree-node:after {
  width: 18px;
  height: 49.8% !important;
  border-color: black!important;
}
</style>
  
