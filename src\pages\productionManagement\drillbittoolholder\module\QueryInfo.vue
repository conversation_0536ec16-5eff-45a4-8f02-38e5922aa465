<!-- 车间管理-钻咀配刀-查询 -->
<template>
  <div ref="SelectBox">
    <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 14 }">
      <a-form-item label="订单编号">
        <a-input allowClear v-model="OrderNumber" placeholder="请输入订单编号" :autoFocus="autoFocus" />
      </a-form-item>
      <!-- <a-form-model-item label="授权工厂" prop="facAccredit" style="width:100%; margin:0">
              <a-select allowClear v-model="FacAccredit"  placeholder="请选择" showSearch optionFilterProp="label" :getPopupContainer="()=>this.$refs.SelectBox">
                   <a-select-option  v-for=" item in facAccreditList" :key="item.text" :value="item.valueMember" :label="item.text">
                  {{ item.text }}
                </a-select-option>
              </a-select>
          </a-form-model-item> -->
    </a-form>
  </div>
</template>

<script>
import { getFacAccreditList } from "@/services/management";
export default {
  name: "QueryInfo",

  data() {
    return {
      OrderNumber: "",
      autoFocus: true,
      FacAccredit: "",
      facAccreditList: [],
    };
  },
  created() {
    // getFacAccreditList().then(res => {
    //   this.facAccreditList = res?.data
    // });
  },
  methods: {},
};
</script>
<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
</style>
