<!-- 工程管理 - 合拼管理 -按钮 -->
<template>
  <div class="active" ref="active">
    <!-- <div class="box" v-if="checkPermission('MES.ProductionModule.Cutting.CuttingHoleAffirm')" >
           <a-button type="primary" @click='operationClick2' :loading="btnloading6" >
             人员确认
           </a-button>
         </div>  -->
    <div class="box" v-if="checkPermission('MES.ProductionModule.Cutting.CuttingSearch')">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <!-- <div v-if='advanced' >           
          <div class="box " v-if="checkPermission('MES.ProductionModule.Cutting.CuttingOrderUpLoad')">
            <a-button type="primary" @click='UploadOrderClick'>
              上传钻孔
            </a-button>
          </div>
        </div> -->
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "Action",
  props: ["btnloading1", "btnloading2", "btnloading3", "btnloading4", "btnloading5", "btnloading6", "btnloading7", "btnloading8"],
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 106px;
    margin: 6px 0;
    text-align: center;
    .ant-btn {
      width: 80px;
    }
    .ant-btn-loading {
      padding-left: 0 !important;
    }
  }
}
</style>
