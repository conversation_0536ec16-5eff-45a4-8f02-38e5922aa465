<template>
  <div class="fixturestyle">
    <div style="display: flex">
      <div class="leftContent">
        <div class="topcontent">
          <top-table class="topstyle" ref="toptable" :loading="loading" @getfixture="getfixture" :dataSource="topdata" />
        </div>
        <div class="botcontent">
          <bot-table :dataSource="botdata" @getfixture="getfixture" ref="bottable" :loading="loading" class="botstyle" />
        </div>
      </div>
      <div class="rightContent">
        <div style="text-align: center; background-color: #fafafa; height: 37px; line-height: 36px; border: 1px solid #efefef; color: black">
          参数信息
        </div>
        <a-form-item label="供应商" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input v-model="fixturedata.supplier" allowClear :disabled="!edit" />
        </a-form-item>
        <a-form-item label="价格" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input v-model="fixturedata.price" :disabled="!edit" allowClear />
        </a-form-item>
        <!-- <a-form-item label="拼板款数"  :labelCol="{span: 6}" :wrapperCol="{span:16}">
          <a-input  v-model="fixturedata.pinBanNum" :disabled="!edit" allowClear />  
          </a-form-item>  
          <a-form-item label="总测试点"  :labelCol="{span: 6}" :wrapperCol="{span:16}">
          <a-input  v-model="fixturedata.delQtyAll" :disabled="!edit" allowClear />  
          </a-form-item>   -->
        <a-form-item label="内部编号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input v-model="fixturedata.internalNum" :disabled="!edit" allowClear />
        </a-form-item>
        <a-form-item label="报废" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-checkbox v-model="fixturedata.tovoid" :disabled="!edit"></a-checkbox>
        </a-form-item>
        <a-form-item label="领用人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input v-model="fixturedata.useName" disabled allowClear />
        </a-form-item>
        <a-form-item label="领用时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input v-model="fixturedata.useDate" allowClear disabled />
        </a-form-item>
        <a-form-item label="备注" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-textarea v-model="fixturedata.remark" :disabled="!edit" allowClear :auto-size="{ minRows: 5, maxRows: 10 }" />
        </a-form-item>
      </div>
    </div>
    <div class="footeraction">
      <make-action
        @queryclick="queryclick"
        @startclick="startclick"
        @editclick="editclick"
        @cancleclick="cancleclick"
        @saveclick="saveclick"
        @addclick="addclick"
        @useclick="useclick"
        @finishclick="finishclick"
        :edit="edit"
      />
    </div>
    <!-- 查询弹窗 -->
    <a-modal
      title="订单查询"
      :visible="queryVisible"
      @cancel="handleCancel"
      @ok="queryhandleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form-item label="本厂编码" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
        <a-input v-model="orderno" :autoFocus="true" allowClear />
      </a-form-item>
    </a-modal>
    <a-modal
      title="新增"
      :visible="addVisible"
      @cancel="handleCancel"
      @ok="addhandleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form-item label="本厂编码" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }" class="required">
        <a-input v-model="Ourfactorycode" allowClear />
      </a-form-item>
      <a-form-item label="治具类型" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }" class="required">
        <a-select v-model="zjtype">
          <a-select-option value="20">新开测试架</a-select-option>
          <a-select-option value="21">新开模具</a-select-option>
          <a-select-option value="22">客供测试架</a-select-option>
        </a-select>
      </a-form-item>
    </a-modal>

    <!-- 确认弹窗 -->
    <a-modal
      title="确认弹窗"
      :visible="datavisible"
      @cancel="handleCancel"
      @ok="handleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <div>{{ OrderNo }}{{ messagelist }}</div>
    </a-modal>
  </div>
</template>
<script>
import { mapState } from "vuex";
import {
  profixturewaitlist,
  profixturelist,
  profixtureorderstart,
  profixtureorderedit,
  profixtureorderuse,
  profixtureorderfinish,
  profixtureordernewadd,
} from "@/services/gongju/fixture";
import TopTable from "@/pages/gongju/fixture/components/TopTable";
import BotTable from "@/pages/gongju/fixture/components/BotTable";
import MakeAction from "@/pages/gongju/fixture/components/MakeAction";
export default {
  name: "",
  computed: {
    ...mapState("account", ["user"]),
  },
  components: { TopTable, BotTable, MakeAction },
  data() {
    return {
      topdata: [],
      botdata: [],
      edit: false,
      queryVisible: false,
      addVisible: false,
      datavisible: false,
      fixturedata: {},
      orderno: "",
      OrderNo: "",
      Ourfactorycode: "",
      zjtype: null,
      loading: false,
      messagelist: "",
      type: "",
      index: "",
    };
  },
  methods: {
    //取消编辑
    cancleclick() {
      this.fixturedata = JSON.parse(localStorage.getItem("copy"))[this.index];
      this.edit = false;
    },
    //编辑
    editclick() {
      if (!this.$refs.bottable.selectrowdata.id) {
        this.$message.warning("请选择要编辑的订单");
        return;
      }
      this.edit = true;
    },
    //保存
    saveclick() {
      if (!this.edit) {
        this.$message.warning("当前为非编辑状态 不允许保存");
        return;
      }
      profixtureorderedit(this.fixturedata).then(ite => {
        if (ite.code) {
          this.$message.success("编辑成功");
          this.getbotdata();
        } else {
          this.$message.error(ite.message);
        }
      });
      this.edit = false;
    },
    //完成
    finishclick() {
      if (!this.$refs.bottable.selectrowdata.id) {
        this.$message.warning("请选择要完成的订单");
        return;
      }
      this.OrderNo = this.$refs.bottable.selectrowdata.orderNo;
      this.messagelist = "确认订单完成吗?";
      this.datavisible = true;
      this.type = "finish";
    },
    //领用
    useclick() {
      if (!this.$refs.bottable.selectrowdata.id) {
        this.$message.warning("请选择要领用的订单");
        return;
      }
      this.OrderNo = this.$refs.bottable.selectrowdata.orderNo;
      this.messagelist = "确认订单领用吗?";
      this.datavisible = true;
      this.type = "use";
    },
    getfixture(record, index, tab) {
      if (tab == "top") {
        this.$refs.bottable.selectrowdata = {};
      } else if (tab == "bot") {
        this.$refs.toptable.selectrowdata = {};
        this.$refs.toptable.selectedRowKeysArray = [];
      }
      this.fixturedata = record;
      this.index = index;
    },
    handleCancel() {
      this.queryVisible = false;
      this.datavisible = false;
      this.addVisible = false;
    },
    addhandleOk() {
      if (!this.Ourfactorycode) {
        this.$message.warning("请填写本厂编码");
        return;
      }
      if (!this.zjtype) {
        this.$message.warning("请填写治具类型");
        return;
      }
      this.addVisible = false;
      this.zjtype = Number(this.zjtype);
      profixtureordernewadd(this.Ourfactorycode, this.zjtype).then(res => {
        if (res.code) {
          this.$message.success("新增成功");
          this.gettopdata();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    queryhandleOk() {
      this.gettopdata(this.orderno);
      this.getbotdata(this.orderno);
      this.queryVisible = false;
    },
    handleOk() {
      if (this.type == "start") {
        profixtureorderstart(this.$refs.toptable.selectedRowKeysArray).then(res => {
          if (res.code) {
            this.$message.success("订单开始成功");
            this.gettopdata();
            this.getbotdata();
          } else {
            this.$message.warning(res.message);
          }
        });
      }
      if (this.type == "use") {
        profixtureorderuse(this.$refs.bottable.selectrowdata.id).then(res => {
          if (res.code) {
            this.$message.success("订单领用成功");
            this.getbotdata();
            this.fixturedata = {};
            this.$refs.bottable.selectrowdata = {};
          } else {
            this.$message.warning(res.message);
          }
        });
      }
      if (this.type == "finish") {
        profixtureorderfinish(this.$refs.bottable.selectrowdata.id).then(res => {
          if (res.code) {
            this.$message.success("订单已完成");
            this.getbotdata();
          } else {
            this.$message.warning(res.message);
          }
        });
      }
      this.datavisible = false;
    },
    //订单查询
    queryclick() {
      this.orderno = "";
      this.queryVisible = true;
    },
    addclick() {
      this.Ourfactorycode = "";
      this.zjtype = null;
      this.addVisible = true;
    },
    //开始按钮
    startclick() {
      if (this.$refs.toptable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择要开始的订单");
        return;
      }
      this.OrderNo = this.$refs.toptable.selectedRowKeysArray.length == 1 ? this.$refs.toptable.selectrowdata.orderNo : "";
      this.messagelist = this.$refs.toptable.selectedRowKeysArray.length == 1 ? "确认订单开始吗?" : "确认订单批量开始吗?";
      this.datavisible = true;
      this.type = "start";
    },

    handleResize() {
      var topstyle =
        document.getElementsByClassName("topstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var topcontent = document.getElementsByClassName("topcontent")[0];
      var botstyle = document.getElementsByClassName("botstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var botcontent = document.getElementsByClassName("botcontent")[0];
      if (this.topdata.length == 0) {
        topcontent.style.height = (window.innerHeight - 155) / 2 + "px";
        topstyle.style.height = "0px";
      } else {
        if (window.innerHeight <= 911) {
          topstyle.style.height = (window.innerHeight - 230) / 2 + "px";
        } else {
          topstyle.style.height = "340.5px";
        }
      }
      if (this.botdata.length == 0) {
        botcontent.style.height = (window.innerHeight - 155) / 2 + "px";
        botstyle.style.height = "0px";
      } else {
        if (window.innerHeight <= 911) {
          botstyle.style.height = (window.innerHeight - 230) / 2 + "px";
        } else {
          botstyle.style.height = "340.5px";
        }
      }
    },
    gettopdata(orderno) {
      let params = {};
      params.orderNo = orderno;
      this.loading = true;
      profixturewaitlist(params)
        .then(res => {
          if (res.code) {
            this.topdata = res.data;
            setTimeout(() => {
              this.handleResize();
            }, 0);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getbotdata(orderno) {
      let params = {};
      this.loading = true;
      params.orderNo = orderno;
      profixturelist(params)
        .then(res => {
          if (res.code) {
            localStorage.setItem("copy", JSON.stringify(res.data));
            this.botdata = res.data;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            this.$refs.bottable.selectrowdata = {};
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  async created() {
    this.$nextTick(() => {
      this.getbotdata();
      this.gettopdata();
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
};
</script>
<style lang="less" scoped>
.required {
  /deep/.ant-col > label {
    color: red;
  }
}
/deep/.ant-select {
  width: 235px;
}
/deep/.ant-form-item {
  margin: 10px 0 0 0;
}
/deep/.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 6px 4px;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 6px 4px;
  border-right: 1px solid #efefef;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
.fixturestyle {
  width: 100%;
  background-color: white;
  .leftContent {
    width: 80%;
    .topcontent {
      border: 1px solid #efefef;
    }
    .botcontent {
      border: 1px solid #efefef;
    }
  }
  .rightContent {
    width: 20%;
  }
  .footeraction {
    width: 100%;
    height: 60px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
}
</style>
