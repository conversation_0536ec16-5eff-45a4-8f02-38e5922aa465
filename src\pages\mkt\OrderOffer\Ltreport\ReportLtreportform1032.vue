<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1" style="font-size: 13px;" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ltreport1032" style=" padding: 25px;font-family: 等线;color: black;font-weight: 600;position: relative;" >
            <div style="width: 100%;display: flex;">
                    <div style="text-align: center;width: 100%;">
                        <img   src="@/assets/img/lt1032.png" style="width: 100%;"> 
                    </div>                            
            </div>
            <div style="display: flex;line-height: 3ch;padding-top: 20px;">
                <div style="width:50%;z-index: 99;font-size: 15px;">
                    <div>收件人 TO:{{ LTreportdata.value_1 }}</div>
                    <div>供应商  Supplier:{{ LTreportdata.value_2 }}</div>
                    <div>收件人 Addressee: {{ LTreportdata.value_3 }}</div>
                    <div>电    话 Tel :{{ LTreportdata.value_4 }}</div>
                </div>
                <div style="z-index: 99;width:50%">
                    <div style="float: right;font-size: 15px;">
                        <div>付款方式:{{ LTreportdata.value_5 }}</div>
                        <div>交货方式 Delivery:{{ LTreportdata.value_6 }}</div>
                        <div>币别及税:{{ LTreportdata.value_7 }}</div>
                        <div>交期:{{ LTreportdata.value_8 }}</div>
                    </div>                   
                </div>               
            </div>
            <div>
                <table border="1" style="text-align: center;margin-top: 5px;width: 100%;border-top: 1px solid black;border-left: 1px solid black;">
                    <thead>
                        <tr style="font-size: 15px;">
                            <td rowspan="2">No</td>
                            <td rowspan="2">汇川编码</td>
                            <td rowspan="2">版本</td>
                            <td rowspan="2">层数</td>
                            <td rowspan="2">表面处理</td>
                            <td rowspan="2">板料</td>
                            <td rowspan="2">TG</td>
                            <td rowspan="2">板厚(mm)</td>
                            <td rowspan="2">基铜</td>
                            <td colspan="3">拼板尺寸</td>
                            <td rowspan="2">Qty(pcs)</td>
                            <td rowspan="2">面积(㎡)</td>
                            <td rowspan="2">基价(RMB/㎡)</td>
                            <td rowspan="2">加价</td>
                            <td rowspan="2">板材利用率</td>
                            <td rowspan="2">平米价</td>
                            <td rowspan="2">单价(RMB/pcs未税)</td>
                            <td rowspan="2">工程费 RMB</td>
                            <td rowspan="2">单pcs工程费 RMB</td>
                            <td rowspan="2">含工程费的未税单价(Rmb/pcs)</td>
                        </tr>
                        <tr>
                            <td>长</td>
                            <td>宽</td>
                            <td>拼板数</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in LTreportdata.price' :key="index">
                            <td>{{ index+1 }}</td>
                            <td>{{ item.price1 }}</td>
                            <td>{{ item.price2 }}</td>
                            <td>{{ item.price3 }}</td>
                            <td>{{ item.price4 }}</td>
                            <td>{{ item.price5 }}</td>
                            <td>{{ item.price6 }}</td>
                            <td>{{ item.price7 }}</td>
                            <td>{{ item.price8 }}</td>
                            <td>{{ item.price9 }}</td>
                            <td>{{ item.price10 }}</td>
                            <td>{{ item.price11 }}</td>
                            <td>{{ item.price12 }}</td>
                            <td>{{ item.price13 }}</td>
                            <td>{{ item.price14 }}</td>
                            <td>{{ item.price15 }}</td>
                            <td>{{ item.price16 }}</td>
                            <td>{{ item.price17 }}</td>
                            <td>{{ item.price18 }}</td>
                            <td>{{ item.price19 }}</td>
                            <td>{{ item.price20 }}</td>
                            <td>{{ item.notes }}</td>
                        </tr>                      
                    </tbody>                
                </table>
            </div>
            <div style="z-index:99;position: relative;">
                <div style="display: flex;line-height:2.8ch;margin-top: 8px;font-size: 15px;">
                    <div>
                        <div style="font-size: 16px;">通用要求： </div>
                        <div>1、如果基于此询价而发生采购行为，则供应商须遵从汇川技术通用采购条款；( The Company's General Terms & Conditions of Purchase will apply if or  dersresult from this request.)</div>
                        <div>2、我们将认真考虑您提的涉及降低成本的其他提议；( Alternate proposals involving cost reduction will be considered.) </div>    
                        <div>3、请提交产品各组件价格，包括成本组成明细；请提供样品、成品、工具的交货时间；( Please submit separate prices, including cost breakdowns, and  lead time for the following : sample parts, production  parts, tooling.)ss</div>    
                        <div>4、请在报价单中注明我司的 RFQ 编号，零件编码，EC版本；( State our RFQ/I No, part numbers, and the EC level(s) on your quotation.)</div>      
                        <div>5、供应商提供的报价应包括空运或海运的出口包装费用；( Seller agrees that the quotation price includes the cost of export packing via air or ocean.)</div>      
                        <div>6、供应商同意基于此询价产生的交易价格中应包括图纸和数据的分发费用；汇川公司可以对这些图纸和数据进行再加工；可以向任何与汇川公司业务有关的第三方提供这些图纸和数据；<br/>
                            ( Seller agrees that the quotation price includes the cost of all drawings and data delivered or required under a    resultant   purchase order.  The Company shall have the right to reproduce and distribute such drawings and data to 3rd parties for any purpose   related to thebusiness of the Company)</div>     
                      </div>
                </div>
            </div>
            <div style="display: flex;width: 100%;padding-top: 15px;font-size: 15px;z-index: 99;position: relative;">
                <div style="width: 50%;">
                    <div>供应商（盖章）:{{ LTreportdata.value_9 }}</div>
                    <div>Supplier</div>
                </div>
                <div style="width: 50%;">
                    <div>填写日期 :{{ LTreportdata.value_10 }}</div>
                    <div>Date</div>
                </div>
            </div>
            <img  src="@/assets/img/lthtz.png" 
             style="position: absolute;
                    bottom: 10px;
                    z-index: 0;
                    display: block;
                    left: 54px;
                    width: 150px;
                    transform: rotate(353deg);"  >    
        </div>
    </div>
  </template>
  <script>
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        amountto:0,
        printObj1:{
            id: "ltreport1032", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
        this.amountto =0 
        for (let index = 0; index < this.LTreportdata.price.length; index++) {
            if(this.LTreportdata.price[index].total && this.LTreportdata.price[index].total!='/'){
                this.amountto +=Number(this.LTreportdata.price[index].total)
            } 
        }
        this.amountto = this.amountto ? this.getFloat(this.amountto,4) : 0
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            htmlToPdfa3('ltreport1032',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
   .Underline {
    position: relative;
    display: inline-block;
    }
    .Underline::after {
    content: "";
    position: absolute;
    left: 0;
    bottom:-8px; /* 下划线距离文字底部的距离 */
    width: 200px; /* 下划线宽度 */
    height: 2px; /* 下划线高度 */
    background-color: rgb(107, 106, 106); /* 下划线颜色 */
    }
  .pdfDom1{
      height: 650px;
      overflow: auto;
      table >thead> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
    table >tbody> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
  }
  </style>
