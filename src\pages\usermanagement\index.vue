<template>
  <div v-if="JSON.stringify(user) != '{}'">
    <div class="header">
      <img style="width: 24px; margin: 0 10px 7px 10px" src="@/assets/img/bn2.png" />
      <span style="color: white; font-weight: 500; font-size: 20px">{{ systemName }}</span>
      <a-dropdown>
        <span style="float: right">
          <!-- <a-avatar size="small" shape="circle" src="https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png"/> -->
          <a-avatar class="avatar" size="small" shape="circle" style="background: #a6dbff" :src="require('@/assets/icon/user.png')" />
          <span style="margin: 0 20px 0 5px">{{ user.custNo }}</span>
        </span>
        <a-menu :class="['avatar-menu']" slot="overlay">
          <a-menu-item @click="ChangePasswordClick">
            <a-icon style="margin-right: 8px" type="user" />
            <span>修改密码</span>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item @click="logout">
            <a-icon style="margin-right: 8px" type="poweroff" />
            <span>退出登录</span>
          </a-menu-item>
        </a-menu>
      </a-dropdown>
      <a
        href="./zhiy.xls"
        v-if="user.tradeType == 38"
        download="客户端EQ及钢网操作指引.xls"
        style="margin: 0 20px 0 5px; float: right; color: aliceblue"
        >客户端操作指引</a
      >
    </div>
    <div class="body">
      <div class="leftmenu">
        <a-menu :selectedKeys="selectedKeys" @click="handleClick">
          <a-menu-item v-for="(ite, index) in tablist" :key="ite.key" :value="index">
            <span v-if="ite.type">{{ ite.name }}</span>
            <!--v-if="JSON.parse(this.user.PlatformPermission)[ite.type]"-->
          </a-menu-item>
        </a-menu>
      </div>
      <div class="rightcontent">
        <div class="top">
          <a-tabs :activeKey="tabkey" hide-add type="editable-card" @change="tabclick">
            <a-tab-pane v-for="(ite, index) in tablist1" :key="ite.key" :value="index">
              <div slot="tab" style="font-size: 13px">
                <a-icon @click="onRefresh(ite)" v-if="tabkey == ite.key" :type="ite.load ? 'loading' : 'sync'" />
                <span>{{ ite.name }}</span>
                <a-icon class="icon-close" type="close" @click="delTab(ite, index)" />
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
        <div class="bot" style="padding-top: 7px">
          <inquire-aboutguests v-if="tabkey == '1'" ref="eq"></inquire-aboutguests>
          <wip-query v-if="tabkey == '2'" ref="wip"></wip-query>
          <shipment-report v-if="tabkey == '3'" ref="report"></shipment-report>
          <placean-order v-if="tabkey == '4'" :user="user" ref="order"></placean-order>
        </div>
      </div>
    </div>
    <a-modal
      title="修改密码"
      :visible="dataVisible"
      @cancel="dataVisible = false"
      @ok="handleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="500"
      centered
    >
      <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 14 }">
        <a-form-item label="原密码" prop="password">
          <a-input-password id="el" v-model="OldClientLoginKey" placeholder="请输入原密码" :autoFocus="autoFocus" @blur="checkNum" />
        </a-form-item>
        <div v-if="errFlag" style="position: absolute; top: 120px; left: 130px">
          <span style="color: red">*输入的原密码错误,请重新输入*</span>
        </div>
        <a-form-item label="新密码">
          <a-input-password
            id="newEL"
            v-model="clientLoginKey"
            placeholder="请输入新密码"
            :disabled="!editFlag"
            @blur="animateWidth()"
            @change="animateWidth1()"
            v-focus-next-on-enter="'input3'"
            ref="input2"
          />
          <span v-if="Dom3" style="font-size: 14px; line-height: 24px; margin-bottom: -20px; color: red; display: block">
            密码需8-16位,包含大小写字母、数字及特殊符号(不得包含空格),以确保账户安全。</span
          >
        </a-form-item>
        <a-form-item label="确认密码" style="margin-bottom: 0">
          <a-input-password id="id" v-model="checkNewNumber" placeholder="请确认新密码" @blur="checkNum1" :disabled="!editFlag" ref="input3" />
        </a-form-item>
        <div v-if="errFlag1" style="position: absolute; left: 130px">
          <span style="color: red">*与新密码不一致,请重新输入*</span>
        </div>
        <div v-if="errFlag2" style="position: absolute; left: 130px">
          <span style="color: #ff9900">*确认新密码成功，请点击确定提交*</span>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { wipdatalHDC } from "@/services/usermanagement/index.js";
import { mapState } from "vuex";
import InquireAboutguests from "@/pages/usermanagement/InquireAboutguests";
import WipQuery from "@/pages/usermanagement/WipQuery";
import ShipmentReport from "@/pages/usermanagement/ShipmentReport";
import PlaceanOrder from "@/pages/usermanagement/PlaceanOrder";
import Cookie from "js-cookie";
import { setAuthorizationuser } from "@/utils/request";
import { updateclientloginkey } from "@/services/usermanagement/index.js";
export default {
  components: { InquireAboutguests, WipQuery, ShipmentReport, PlaceanOrder },
  computed: {
    ...mapState("account", ["userinfo"]),
    systemName() {
      return this.$store.state.setting.systemName;
    },
  },
  data() {
    return {
      user: {},
      OldClientLoginKey: "",
      clientLoginKey: "",
      checkNewNumber: "",
      editFlag: false,
      autoFocus: true,
      errFlag: false,
      errFlag1: false,
      errFlag2: false,
      Dom1: false,
      Dom2: false,
      Dom3: false,
      dataVisible: false,
      selectedKeys: ["1"],
      loading: false,
      tabkey: "1",
      tablist: [
        { key: "1", name: "问客管理", load: false, type: "EqManage" },
        { key: "2", name: "WIP查询", load: false, type: "Wip" },
        { key: "3", name: "出货报告", load: false, type: "CHReport" },
        // {key:'4',name:'立即下单',load:false},
      ],
      tablist1: [{ key: "1", name: "问客管理", load: false, type: "EqManage" }],
    };
  },
  created() {
    if (this.userinfo) {
      this.user = this.userinfo;
    } else {
      this.user = JSON.parse(localStorage.getItem("UserInformation"));
    }
    this.tablist.forEach(item => {
      item.type = JSON.parse(this.user.platformPermission)[item.type];
    });
    if (JSON.stringify(this.user) == "{}" || !this.user) {
      this.$router.push("/userlogin");
    }
    this.$nextTick(() => {
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("click", this.keyclick);
    let key = localStorage.getItem("pageinfo");
    let tab = JSON.parse(localStorage.getItem("tablist1"));
    if (tab) {
      this.tablist1 = tab;
    }
    if (key) {
      this.tabkey = key;
      this.selectedKeys = [key];
    }
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    delTab(ite, index) {
      if (this.tablist1.length == 1) {
        this.$message.warning("这是最后一页，不能再关闭了");
        return;
      }
      let ind = this.tablist1.findIndex(item => item.key === ite.key);
      this.tablist1.splice(ind, 1);
      this.selectedKeys = [this.tablist1[this.tablist1.length - 1].key];
      this.tabkey = this.tablist1[this.tablist1.length - 1].key;
      localStorage.setItem("pageinfo", this.tabkey);
      localStorage.setItem("tablist1", JSON.stringify(this.tablist1));
    },
    checkNum() {
      if (this.OldClientLoginKey) {
        this.editFlag = true;
        this.errFlag = false;
        document.getElementById("newEL").focus();
      }
    },
    // 修改密码
    ChangePasswordClick() {
      this.dataVisible = true;
    },
    // 失去
    animateWidth() {
      let name = this.clientLoginKey;
      var regex = /^(?!.*\s)(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^a-zA-Z\d]).{8,16}$/;
      if (!regex.test(name)) {
        this.Dom3 = true;
      }
    },
    animateWidth1() {
      let name = this.clientLoginKey;
      var regex = /^(?!.*\s)(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^a-zA-Z\d]).{8,16}$/;
      if (regex.test(name)) {
        this.Dom3 = false;
      }
    },
    checkNum1() {
      if (this.checkNewNumber != this.clientLoginKey) {
        this.errFlag1 = true;
        this.errFlag2 = false;
        this.checkNewNumber = "";
      } else {
        this.errFlag1 = false;
        this.errFlag2 = true;
      }
    },
    handleOk() {
      let params = {
        custNo: this.user.custNo,
        clientLoginKey: this.clientLoginKey,
        factoryid: this.user.tradeType,
        oldClientLoginKey: this.OldClientLoginKey,
      };
      updateclientloginkey(params).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.$router.push("/userlogin");
          this.dataVisible = false;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    keyclick() {
      let data = JSON.parse(localStorage.getItem("UserInformation"));
      if (!data) {
        this.$router.push("/userlogin");
      }
      const token = Cookie.get("token1");
      if (!token) {
        this.$router.push("/userlogin");
        return;
      }
      setAuthorizationuser({
        token: token,
        expireAt: new Date(new Date().getTime() + 3600000),
      });
    },
    downloadFile() {
      const filePath = "@/pages/usermanagement/zhiy.xls";
      const link = document.createElement("a");
      link.href = filePath;
      link.download = "客户端操作指引.xls"; // 设置下载的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    handleResize() {
      let screenWidth = window.innerWidth;
      let screenHeight = window.innerHeight;
      let leftmenu = document.getElementsByClassName("leftmenu")[0];
      leftmenu.style.height = screenHeight - 50 + "px";
      let rightcontent = document.getElementsByClassName("rightcontent")[0];
      rightcontent.style.width = screenWidth - 150 + "px";
      let bot = document.getElementsByClassName("bot")[0];
      bot.style.height = screenHeight - 90 + "px";
    },
    handleClick(e) {
      this.selectedKeys = e.keyPath;
      this.tabkey = e.keyPath[0];
      localStorage.setItem("pageinfo", this.tabkey);
      let arr = this.tablist.filter(item => item.key == e.keyPath[0])[0];
      let index = this.tablist1.findIndex(item => item.key === arr.key);
      if (index === -1) {
        this.tablist1.push({
          key: this.tabkey,
          name: this.tablist.filter(item => item.key == this.tabkey)[0].name,
          load: false,
        });
      }
      localStorage.setItem("tablist1", JSON.stringify(this.tablist1));
    },
    tabclick(e) {
      this.tabkey = e;
      this.selectedKeys = [e];
    },
    logout() {
      this.$router.push("/userlogin");
    },
    onRefresh(page) {
      page.load = true;
      localStorage.setItem("pageinfo", page.key);
      this.$router.go();
      page.load = false;
      localStorage.setItem("tablist1", JSON.stringify(this.tablist1));
    },
  },
};
</script>

<style scoped lang="less">
.header {
  height: 45px;
  width: 100%;
  background-color: rgb(124, 90, 35);
  color: white;
  line-height: 45px;
  .avatar-menu {
    width: 110px;
  }
}
.body {
  display: flex;
  width: 100%;
  background-color: rgb(240, 242, 245);
  .leftmenu {
    width: 150px;
    background-color: white;
    box-shadow: rgba(0, 21, 41, 0.35) 2px 0px 6px;
    border-left: 1px solid #ccc;
  }
  .rightcontent {
    padding: 0 10px;
  }
}
.ant-tabs {
  height: 30px;
}
/deep/ .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
  line-height: 30px;
}
</style>
