import { request, METHOD } from '@/utils/request'
// 获取已入库订单列表
export async function pcbstorage(params) {
    return request("/api/app/pcb-storage/page-list", METHOD.GET, params)
}
// 获取待入库订单信息
export async function orderlistbycardinfo(joinFactoryId,orderNo,type) {
    return request(`/api/app/pcb-storage/order-list-by-card-info/${joinFactoryId}?orderNo=${orderNo}&type=${type}`, METHOD.GET,)
}
// 订单入库
export async function pcbstoragewarehousing(params) {
    return request("/api/app/pcb-storage/warehousing", METHOD.POST, params)
}
export default {
    pcbstorage,  
    orderlistbycardinfo,
    pcbstoragewarehousing,
}