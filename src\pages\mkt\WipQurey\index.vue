<!-- 市场管理 - WIP查询主组件 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectmanagement" ref="tableWrapper">
      <div class="box" style="display: flex">
        <div class="leftContent" style="border: 2px solid rgb(233 230 230); border-bottom: none; overflow: auto" ref="leftstyle">
          <ul
            @click="liClick(item)"
            :class="[item.value == step ? 'licolor' : '']"
            v-for="(item, index) in mapKey(leftList)"
            :key="index"
            :value="item.value"
            :lable="item.lable"
          >
            <li>
              <span v-if="item.lable.indexOf('(') != -1">
                {{ item.lable.split("(")[0] }} (<span style="color: red">{{ item.lable.split("(")[1].split(")")[0] }}</span
                >)
              </span>
              <span v-else>{{ item.lable }}</span>
            </li>
          </ul>
        </div>
        <div class="rightContent" style="border-top: 2px solid rgb(233 230 230)" ref="mainstyle1">
          <a-table
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :columns="columns1"
            @change="handleTableChange"
            :pagination="pagination"
            :data-source="orderListData"
            :scroll="{ y: 737, x: 1000 }"
            class="mainstyle"
            :customRow="onClickRow"
            :rowClassName="isclickcolor"
          >
            <template slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </template>
            <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
              <span :title="record.orderNo">{{ record.orderNo }}</span
              >&nbsp;
              <span class="tagNum" style="display: inline-block; height: 19px">
                <span v-if="record.isExtremeJiaji">
                  <span style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; margin-left: -10px; user-select: none"
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                  <span style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; margin-left: -10px; user-select: none"
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </span>
                <a-tooltip title="加急" v-if="record.isJiaji">
                  <span style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; margin-left: -10px; user-select: none"
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </a-tooltip>
                <a-tooltip v-if="step == 9" title="点击跳转问客管理详情">
                  <a-icon type="link" style="color: #428bca; cursor: pointer" @click="wenkeClick(record)" />
                </a-tooltip>
                <a-tooltip title="新客户" v-if="record.isNewCust">
                  <a-tag
                    color="#2D221D"
                    style="
                      font-size: 12px;
                      background: #428bca;
                      color: white;
                      padding: 0 2px;
                      margin-left: 3px;
                      margin-right: 3px;
                      height: 21px;
                      user-select: none;
                      border: 1px solid #428bca;
                    "
                  >
                    新
                  </a-tag>
                </a-tooltip>
                <a-tag
                  v-if="record.isOrderModify && record.orderModify == 1"
                  @click.stop="xiudisplay(record)"
                  style="
                    font-size: 12px;
                    background: #428bca;
                    color: white;
                    padding: 0 2px;
                    margin: 0;
                    margin-right: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid #428bca;
                  "
                >
                  修
                </a-tag>
                <a-tag
                  v-if="record.isOrderModify && record.orderModify == 2"
                  @click.stop="xiudisplay(record)"
                  style="
                    font-size: 12px;
                    background: #ff9900;
                    color: white;
                    padding: 0 2px;
                    margin: 0;
                    margin-right: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid #ff9900;
                  "
                >
                  修
                </a-tag>
                <a-tag
                  v-if="record.isOrderModify && record.orderModify == 3"
                  @click.stop="xiudisplay(record)"
                  style="
                    font-size: 12px;
                    background: #428bca;
                    color: white;
                    padding: 0 2px;
                    margin: 0;
                    margin-right: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid #428bca;
                  "
                >
                  退
                </a-tag>
                <a-tooltip title="生产通知单已下载">
                  <a-icon v-if="record.printProductionOrder" type="file-done" style="color: #ff9900; margin-left: 4px"></a-icon>
                </a-tooltip>
                <a-tooltip title="订单锁定" v-if="record.isLock">
                  <a-tag
                    color="#2D221D"
                    style="
                      font-size: 12px;
                      background: #428bca;
                      color: white;
                      padding: 0 2px;
                      margin-left: 3px;
                      margin-right: 3px;
                      height: 21px;
                      user-select: none;
                      border: 1px solid #428bca;
                    "
                  >
                    锁
                  </a-tag>
                </a-tooltip>
              </span>
            </div>
          </a-table>
          <right-copy ref="RightCopy" />
        </div>
      </div>
      <div class="bto">
        <div class="bobu">
          <a-button type="primary" @click="queryclick">查询(F)</a-button>
        </div>
        <div class="bobu">
          <a-button type="primary" @click="downxlsx">导出</a-button>
        </div>
      </div>
    </div>
    <!-- 查询弹窗 -->
    <a-modal
      title="订单查询"
      :visible="querydataVisible"
      @cancel="reportHandleCancel"
      @ok="queryhandleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <query-info ref="queryInfo" />
    </a-modal>
    <a-modal
      title="修改内容"
      :width="1300"
      :visible="xiuvisible"
      destroyOnClose
      centered
      :mask="false"
      :maskClosable="false"
      @cancel="reportHandleCancel"
    >
      <div>
        <a-table
          :columns="columns4"
          :loading="loading"
          :dataSource="dataSource4"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 400 }"
          :class="dataSource4.length ? 'min-table' : ''"
          :pagination="false"
        >
          <template slot="isPrintContract" slot-scope="record">
            <a-checkbox :checked="record.isPrintContract"></a-checkbox>
          </template>
          <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
            <div v-for="photo in record.filePath.split(',')" :key="index + '-' + photo">
              <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer />
            </div>
          </div>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
  </a-spin>
</template>
<script>
import moment from "moment";
import RightCopy from "@/pages/RightCopy";
import QueryInfo from "@/pages/mkt/WipQurey/module/QueryInfo";
import { ordermodifylist } from "@/services/mkt/CustInfoNew";
import { wipordermanegepagelist, wipselectlist, pcborderlistexport } from "@/services/mkt/WipQurey";
let columns1 = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 50,
  },
  {
    title: "预审编号",
    align: "left",
    ellipsis: true,
    width: 110,
    scopedSlots: { customRender: "orderNo" },
    sorter: (a, b) => {
      return a.orderNo.localeCompare(b.orderNo);
    },
  },
  {
    title: "生产编号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    width: 170,
    sorter: (a, b) => {
      return a.proOrderNo.localeCompare(b.proOrderNo);
    },
  },
  {
    title: "状态",
    dataIndex: "statusStr",
    align: "left",
    ellipsis: true,
    width: 90,
  },
  {
    title: "客编",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "终端客户",
    dataIndex: "terminalCust",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "客户型号",
    dataIndex: "pcbFileName",
    align: "left",
    ellipsis: true,
    width: 320,
    sorter: (a, b) => {
      return a.pcbFileName.localeCompare(b.pcbFileName);
    },
  },
  {
    title: "数量",
    dataIndex: "para4DelQty",
    width: 55,
    ellipsis: true,
    align: "left",
  },
  {
    title: "面积",
    dataIndex: "para4Area",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  // {
  // title: "总金额",
  // dataIndex: "totalAmountPrice",
  // width:95,
  // ellipsis: true,
  // align: "left",
  // },
  {
    title: "交货日期",
    dataIndex: "deliveryDate",
    align: "left",
    ellipsis: true,
    width: 95,
    sorter: (a, b) => {
      return a.deliveryDate.localeCompare(b.deliveryDate);
    },
  },
  {
    title: "工厂",
    dataIndex: "factoryName",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "工序",
    dataIndex: "selectValue",
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "EQ开始时间",
    dataIndex: "eqBeginDate",
    align: "left",
    ellipsis: true,
    width: 160,
  },
  {
    title: "发送后耗时",
    dataIndex: "eqCostTime",
    align: "left",
    ellipsis: true,
    width: 90,
  },
  {
    title: "EQ完成时间",
    dataIndex: "eqEndDate",
    align: "left",
    ellipsis: true,
    width: 160,
  },
  {
    title: "备注",
    dataIndex: "memo",
    align: "left",
    ellipsis: true,
    width: 220,
  },
  {
    title: "作业员",
    dataIndex: "userName",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "制作人",
    dataIndex: "proAdminName",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "业务员",
    dataIndex: "ywName",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "电话",
    dataIndex: "tel",
    align: "left",
    ellipsis: true,
    width: 110,
  },
  {
    title: "过序时间",
    dataIndex: "inDate",
    align: "left",
    ellipsis: true,
    width: 160,
    sorter: (a, b) => {
      return a.inDate.localeCompare(b.inDate);
    },
  },
];
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "30%",
    align: "left",
  },
  // {
  //     title: "附件地址",
  //     dataIndex: "filePath",
  //     ellipsis: true,
  //     align: "left",
  // },
  {
    title: "图片",
    //dataIndex: "filePath",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    // dataIndex: "isPrintContract",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  name: "",
  components: { QueryInfo, RightCopy },
  data() {
    return {
      loading: false,
      isCtrlPressed: false,
      querydataVisible: false,
      columns4,
      dataSource4: [],
      xiuvisible: false,
      step: "0",
      id: "",
      leftList: [],
      aaa: [],
      spinning: false,
      orderListData: [],
      queryData: {},
      columns1,
      showText: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  computed: {},
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.handleResize();
      this.getOrderListData();
      this.getleftlisit();
    });
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  watch: {
    orderListData: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0) {
              this.columns1[1].width = "110px";
              this.columns1[6].width = "300px";
            }
            if (result >= 1) {
              this.columns1[1].width = 110 + result * 35 + "px";
              this.columns1[6].width = 300 - result * 30 + "px";
            }
          });
        }
      },
    },
  },
  methods: {
    moment,
    wenkeClick(record) {
      this.$router.push({
        path: "/gongcheng/eqDetails",
        query: {
          OrderNo: record.proOrderNo,
          eQSource: record.eqSource,
          id: record.id,
          businessOrderNo: record.orderNo,
          joinFactoryId: record.joinFactoryId,
          Jump: "WIP",
          para4IntDelivery: record.para4IntDelivery,
          eqNumber: record.eqNumber,
          deliveryDate: record.deliveryDate,
        },
      });
    },
    downxlsx() {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
        No: this.step,
      };
      pcborderlistexport(params).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    handleResize() {
      var mainstyle = document.getElementsByClassName("mainstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (mainstyle && this.orderListData.length != 0) {
        mainstyle.style.height = window.innerHeight - 174 + "px";
      } else {
        mainstyle.style.height = 0;
      }
      var rightContent = document.getElementsByClassName("rightContent")[0];
      if (this.orderListData.length != 0) {
        if (window.innerHeight - 153 < 756) {
          rightContent.style.height = window.innerHeight - 153 + "px";
        } else {
          rightContent.style.height = "776px";
        }
      } else {
        rightContent.style.height = 0;
      }
      if (window.innerHeight - 153 < 756) {
        this.$refs.leftstyle.style.height = window.innerHeight - 137 + "px";
      } else {
        this.$refs.leftstyle.style.height = "776px";
      }
      this.$refs.leftstyle.style.width = "200px";
      if (window.innerWidth < 769) {
        this.$refs.mainstyle1.style.width = window.innerWidth - 200 + "px";
      } else {
        this.$refs.mainstyle1.style.width = window.innerWidth - 420 + "px";
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      if (window.innerWidth < 1920) {
        if (110 + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 + 200 + 110 < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    xiudisplay(record) {
      this.loading = true;
      this.xiuvisible = true;
      ordermodifylist(record.id)
        .then(res => {
          if (res.code) {
            this.dataSource4 = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getleftlisit(data) {
      if (data) {
        data.PcbFileName = data.PcbFileName ? data.PcbFileName.replace(/\s+/g, " ").trim() : "";
      }
      wipselectlist(data).then(res => {
        if (res.code) {
          this.leftList = res.data;
        }
      });
    },
    getOrderListData(queryData) {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
        No: this.step,
      };
      var obj = Object.assign(params, queryData);
      obj.PcbFileName = obj.PcbFileName ? obj.PcbFileName.replace(/\s+/g, " ").trim() : "";
      this.spinning = true;
      wipordermanegepagelist(obj)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            setTimeout(() => {
              this.handleResize();
            });
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    isclickcolor(record) {
      let strGroup = [];
      if (record.id && record.id == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.id = record.id;
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    queryclick() {
      this.querydataVisible = true;
    },
    queryhandleOk() {
      this.queryData = this.$refs.queryInfo.form;
      this.step = 0;
      if ([...this.queryData.ProOrderNo].length > 20) {
        this.queryData.ProOrderNo = [...this.queryData.ProOrderNo].slice(0, 20).join("");
      }
      if ([...this.queryData.CustNo].length > 10) {
        this.queryData.CustNo = [...this.queryData.CustNo].slice(0, 10).join("");
      }
      if ([...this.queryData.PcbFileName].length > 100) {
        this.queryData.PcbFileName = [...this.queryData.PcbFileName].slice(0, 100).join("");
      }
      if (this.queryData.ProOrderNo && typeof this.queryData.ProOrderNo === "string" && this.queryData.ProOrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产型号查询不能小于5位");
        return;
      }
      this.querydataVisible = false;
      this.getOrderListData(this.queryData);
      this.getleftlisit(this.queryData);
    },
    reportHandleCancel() {
      this.querydataVisible = false;
      this.xiuvisible = false;
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      if (JSON.stringify(this.queryData) != "{}") {
        this.getOrderListData(this.queryData);
        this.getleftlisit(this.queryData);
      } else {
        this.getOrderListData();
        this.getleftlisit();
      }
    },
    liClick(record) {
      if (record.value) {
        this.step = record.value;
      } else {
        this.step = 0;
      }
      this.pagination.current = 1;
      this.getOrderListData(this.queryData);
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryclick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.querydataVisible) {
        this.queryhandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-pagination.mini .ant-pagination-total-text,
.ant-pagination.mini .ant-pagination-simple-pager {
  height: 24px;
  line-height: 24px;
  font-size: 13px;
}
/deep/.ant-pagination-prev {
  margin-left: 11px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/ .ant-table-pagination.ant-pagination {
  float: left;
  margin: 21px 0 0 10px;
}
/deep/.ant-table-thead > tr > th .ant-table-column-sorter {
  display: none !important;
  vertical-align: middle;
}
/deep/.min-table {
  border: 1px solid #efefef;
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/.ant-menu-item:hover {
  color: rgb(22, 22, 22);
}
/deep/.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #ffffff;
  color: black;
}
/deep/ .ant-table {
  .rowBackgroundColor {
    background: #dfdcdc !important;
  }
}
/deep/.ant-table-thead > tr > th {
  padding: 5px 4px;
  overflow-wrap: break-word;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 5px 4px;
  overflow-wrap: break-word;
  border-right: 1px solid #efefef;
}
.projectmanagement {
  user-select: none !important;
  .box {
    border-top: none !important;
    border: 2px solid rgb(233, 233, 240);
    background: #ffffff;
    .leftContent {
      ul {
        margin-bottom: 0;
        padding-left: 0;
      }
      ul li {
        list-style: none;
        margin: 0;
        border-bottom: 1px solid rgb(233 230 230);
        padding-left: 10px;
        font-size: 14px;
        height: 36px;
        line-height: 36px;
      }
      li:hover {
        color: white;
        font-weight: 600;
        font-size: 16px;
        background-color: #ffc664;
      }
      /deep/ .licolor li {
        color: white;
        font-weight: 600;
        font-size: 16px;
        background-color: #ff9900;
      }
      &::-webkit-scrollbar {
        //整体样式
        width: 4px; //y轴滚动条粗细
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        //滑动滑块条样式
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        background: #ff9900;
        // #fff9e6
      }
      overflow-y: scroll;
      /deep/ .ant-list-header {
        padding: 5px 24px;
        text-align: center;
        background-color: #f0f0f0;
      }
      .ant-list-item {
        padding: 5px 12px;
      }
    }
  }
  .bto {
    height: 48px;
    background: #ffffff;
    border: 2px solid #e9e9f0;
    .bobu {
      margin: 7px;
      float: right;
    }
  }

  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: rgb(223 220 220);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 8px 0 0 -185px;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: rgb(223 220 220);
}
</style>
