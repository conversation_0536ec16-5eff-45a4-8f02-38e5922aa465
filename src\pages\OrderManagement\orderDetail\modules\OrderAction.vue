<!--生产管理- 订单详情-按钮 -->
<template>
  <a-form-model layout="inline">
    <a-form-model-item  v-if="checkPermission('MES.ProManagement.Feeding.FeedBack')">
      <span v-if="ttype==1">
        <a-button
        @click="returnClick1"
        type="primary"
      >
        返回对账列表
      </a-button>
      </span>
      <span v-if="ttype==2">
        <a-button
        @click="returnClick2"
        type="primary"
      >
        返回生产进度列表
      </a-button>
      </span>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.ProManagement.Feeding.FeedAdd')">
      <a-button
        @click="Feeding"
        type="primary"
      >
        投料单填写
      </a-button>
    </a-form-model-item>
    <a-form-model-item  v-if="checkPermission('MES.ProManagement.Feeding.CheckFlowCard')">
      <a-button
        @click="examined"
        type="primary"
      >
      审核流程卡
      </a-button>
    </a-form-model-item>
    <a-form-model-item  v-if="checkPermission('MES.ProManagement.Feeding.ZuoFei')">
      <a-button
        @click="deleted"
        type="primary"
      >
        拼版作废
      </a-button>
    </a-form-model-item>
    <a-form-model-item >
      <a-button 
      type="primary" 
      @click="AllPrintClick"
      >
      打印流程卡
    </a-button>
    </a-form-model-item>
   
    <!-- <a-form-model-item>
      <a-button
        @click="modify"
        type="primary"
      >
        修改
      </a-button>
    </a-form-model-item> -->
    
  </a-form-model>
</template>

<script>
import {checkPermission} from "@/utils/abp";
export default {
  name: "OrderAction",
  props:['ttype'],
  methods: {
    checkPermission,
    returnClick2(){  
        this.$router.push({path: '/OrderManagement/ProgressManagement', })
      },
      returnClick1(){  
        this.$router.push({path: '/OrderManagement/ReconciliationList', })
      },
    Feeding(){
      this.$emit('Feeding')
    },
    examined(){
      this.$emit('examined')
    },
    deleted(){
      this.$emit('deleted')
    },
    modify(){
      this.$emit('modify')
    },
    AllPrintClick(){
      this.$emit('AllPrintClick')
    }
    
  }
}
</script>

<style scoped lang="less">
/deep/.ant-form-item-children{
  .ant-btn{
    margin:0 10px;
  }
  
}
</style>