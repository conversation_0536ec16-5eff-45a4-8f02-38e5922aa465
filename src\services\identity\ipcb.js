import { request, METHOD } from "@/utils/request";
export async function taskMonitoringPageList(params) {
  return request("/api/app/sight/task-monitoring-page-list", METHOD.GET, params);
}
export async function taskMonitoringnewPageList(params) {
  return request("api/app/sight/task-monitoring-new-page-list", METHOD.GET, params);
}
export async function monitoringListCount(params) {
  return request("/api/app/sight/task-monitoring-list-count", METHOD.GET, params);
}
export async function sightState() {
  return request("/api/app/sight/state", METHOD.GET);
}
export async function taskMonitoring(params) {
  return request(`/api/app/sight/task-monitoring?BizObjectID=${params}`, METHOD.GET);
}
export async function ipCBTask(params) {
  return request(`api/app/sight/i-pCBTask?BOID=${params}`, METHOD.GET);
}
//重置
export async function settaskcz(params) {
  return request(`api/app/sight/set-task-cz?BizObjectID=${params}`, METHOD.POST);
}
//新ipcb统计接口
export async function monitoringListneweCount(params) {
  return request("api/app/sight/task-monitoring-list-newe-count", METHOD.GET, params);
}
//新ipcb 重置接口
export async function settaskcznew(params) {
  return request(`api/app/sight/set-task-cz-new?BizObjectID=${params}`, METHOD.POST);
}
export default {
  taskMonitoringPageList,
  monitoringListCount,
  sightState,
  taskMonitoring,
  monitoringListneweCount,
  settaskcz,
  settaskcznew,
};
