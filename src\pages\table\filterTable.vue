<template>
  <page-layout>
    <a-card>
      <standard-table
      rowKey="key"
      :columns="columns"
      :dataSource="data"
      :pagination="pagination"
      @change="onChange"
      >
      </standard-table>
    </a-card>
  </page-layout>
</template>
<script>
import PageLayout from "@/layouts/PageLayout";
import StandardTable from "@/components/table/StandardTable";
const columns = [
  {
    title: "Name",
    dataIndex: "name",
    filters: [
      {
        text: "<PERSON>",
        value: "<PERSON>"
      },
      {
        text: "<PERSON>",
        value: "<PERSON>"
      },
      {
        text: "Submenu",
        value: "Submenu",
        children: [
          {
            text: "Green",
            value: "Green"
          },
          {
            text: "Black",
            value: "Black"
          }
        ]
      }
    ],
    // specify the condition of filtering result
    // here is that finding the name started with `value`
    onFilter: (value, record) => record.name.indexOf(value) === 0,
    sorter: (a, b) => a.name.length - b.name.length,
    sortDirections: ["descend"]
  },
  {
    title: "Age",
    dataIndex: "age",
    defaultSortOrder: "descend",
    sorter: (a, b) => a.age - b.age
  },
  {
    title: "Address",
    dataIndex: "address",
    filters: [
      {
        text: "London",
        value: "London"
      },
      {
        text: "New York",
        value: "New York"
      }
    ],
    filterMultiple: false,
    onFilter: (value, record) => record.address.indexOf(value) === 0,
    sorter: (a, b) => a.address.length - b.address.length,
    sortDirections: ["descend", "ascend"]
  }
];

const data = [
  {
    key: "1",
    name: "John Brown",
    age: 32,
    address: "New York No. 1 Lake Park"
  },
  {
    key: "2",
    name: "Jim Green",
    age: 42,
    address: "London No. 1 Lake Park"
  },
  {
    key: "3",
    name: "Joe Black",
    age: 32,
    address: "Sidney No. 1 Lake Park"
  },
  {
    key: "4",
    name: "Jim Red",
    age: 32,
    address: "London No. 2 Lake Park"
  }
];

function onChange(pagination, filters, sorter) {
  console.log("params", pagination, filters, sorter);
}

export default {
  components: { PageLayout, StandardTable },
  data() {
    return {
      data,
      columns,
      pagination: this.$store.state.setting.pagination,
    };
  },
  methods: {
    onChange
  }
};
</script>
