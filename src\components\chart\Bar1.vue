<template>
  <div>
    <!-- <h4 style="font-weight:600;font-size:14px;color: rgba(0, 0, 0, 0.45);text-indent:20px">{{title}} <span class="dayComplatePanel" 
      v-if="!(dayComplatePanel < 0)">（拼板：{{dayComplatePanel}}件，子料号：{{pinBanDetaileOrderNum}}件）</span></h4> -->
    <div id="myechart" style="height: 500px;"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts/core';
import {
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components';
import { BarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  CanvasRenderer
]);
  export default {
    props:["title", "barData", "dayComplatePanel","pinBanDetaileOrderNum"],
    data() {
      return {
        data:[],
      };
    },
    mounted() {
      this.echartInit()
      console.log('this.barData',this.barData)
    },
    methods: {
      echartInit () {
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // type: 'cross',
              // crossStyle: {
              //   color: '#999'
              // }
            }
          },         
          // tooltip: {
          //   // trigger: 'axis',
          //   formatter(params){
          //     console.log('params',params)
          //     var result = ''
          //       var dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#ff9900"></span>'
          //       var dotHtml2 = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#ccc"></span>'
          //       result += params.name + "</br>" + dotHtml +'询盘' + params.data+ "</br>" + dotHtml2 +'下单量' + params.data + "</br>"
          //       return result
          //   },
          //   axisPointer: {
          //     type: 'shadow'
          //   }
          // },
          legend: {
            data: ['询盘', '下单量']
          },
          xAxis: {
            data: this.barData.xData,
            axisLabel: {  
              interval:0,  
              // rotate:40  
            }  
          },
          color: ['#ff9900','#ccc',],
          yAxis: {
            axisLabel: {
              formatter: '{value}'
              
            }
          },          
            grid: {
              x: 30,
              y: 50,
              x2: 30,
              y2: 30,
              containLabel: true,
            },
          series: [
            {
              name: '询盘',
              type: 'bar',
              label: {
                show: true,
                position: "top"
              },
              // z: '-1',
              // barGap: '-100%',
              data: this.barData.y1Data,
              barWidth: "20%",
              itemStyle: {
                barBorderRadius: 5,
                borderWidth: 1,
                borderType: "solid",
                borderColor: "#ff9900",
                shadowColor: "#ff9900",
                shadowBlur: 3
              }
            },
            {
              name: '下单量',
              type: 'bar',
              barWidth: "20%",
              label: {
                show: true,
                position: "top"
              },
              data: this.barData.y2Data,
              itemStyle: {
                barBorderRadius: 5,
                borderWidth: 1,
                borderType: "solid",
                borderColor: "#ccc",
                shadowColor: "#ccc",
                shadowBlur: 3
              }
            }
          ]
        };
        var chartDom = document.getElementById('myechart');
        var myChart = echarts.init(chartDom);
        myChart.setOption(option)
      }
      
    }
  };
</script>
<style scoped>
.dayComplatePanel {
  color: #ff9900;
  font-weight: 600;
}
</style>