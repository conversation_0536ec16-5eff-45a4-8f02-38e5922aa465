<template>
  <div class="calendar-box">
    <div class="calendar-wrapper">
      <div class="calendar-toolbar">
        <div class="prev" @click="prevMonth">上个月</div>
        <div class="current">{{ currentDateStr }}</div>
        <div class="next" @click="nextMonth">下个月</div>
      </div>

      <div class="calendar-week">
        <div class="week-item calendarBorder" v-for="item of weekList" :key="item">
          {{ item }}
        </div>
      </div>
      <div class="calendar-inner">
        <div
          class="calendar-item calendarBorder"
          v-for="(item, index) of calendarList"
          :key="index"
          :class="{
            'calendar-item': true,
            calendarBorder: true,
            'calendar-item-hover': !item.disable,
            'calendar-item-disabled': item.disable,
            'calendar-item-checked': dayChecked && dayChecked.value == item.value,
            'item-checked': item.isHoliday,
          }"
        >
          <!-- @click="handleClickDay(item)" -->
          <div style="position: relative">
            <span style="margin-bottom: 0; position: absolute; z-index: 99; top: -12px; right: -12px; color: #ffc36c" v-show="item.isHoliday"
              >休</span
            >
            <p style="margin-bottom: 0; text-align: center; display: block; width: 30px; height: 30px; border-radius: 30px; line-height: 30px">
              {{ item.date }}
            </p>
            <!-- <p style="margin-bottom:0;text-align: center;" v-show="item.isHoliday">休</p> -->
          </div>
        </div>
      </div>
    </div>
    <a-button type="primary" style="position: absolute; top: 50px; left: 700px" @click="setClick">{{ $t("set_date_button") }}</a-button>
    <a-button type="primary" style="position: absolute; top: 95px; left: 700px" @click="OperationLog">{{ $t("view_log_button") }}</a-button>
    <!-- 操作日志弹窗 -->
    <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose :maskClosable="false" @cancel="reportHandleCancel" :width="800" centered>
      <div class="projectackend">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
        </a-table>
      </div>
      <template slot="footer">
        <a-button type="primary" @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
    <a-modal title="日历设置" :visible="makeupVisible" @ok="handleOkMode" :width="400" centered destroyOnClose @cancel="handleCancel">
      <a-form-model :rules="rules" ref="ruleForm" :model="Form">
        <a-form-model-item label="开始时间" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }" ref="startDate" prop="startDate">
          <a-date-picker style="margin-right: 0.5%" format="YYYY-MM-DD " @change="onChange1" />
        </a-form-model-item>
        <a-form-model-item label=" 结束时间" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }" ref="endDate" prop="endDate">
          <a-date-picker style="margin-right: 0.5%" format="YYYY-MM-DD " @change="onChange2" />
        </a-form-model-item>
        <a-form-model-item label="是否节假日" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
          <a-checkbox v-model="Form.isHoliday"></a-checkbox>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import moment from "moment";
import { calendarList, setHoliday, calendarlogfactoryid } from "@/services/calendar";
export default {
  i18n: require("@/components/language/modules/supplierS/supplierS_i18n.js"),
  props: ["factoryId"],
  data() {
    return {
      labordataVisible: false,
      labordata: [],
      laborcolumns: [
        {
          title: "序号",
          align: "center",
          dataIndex: "index",
          key: "index",
          width: 20,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "操作时间",
          align: "left",
          dataIndex: "createTime",
          width: 65,
        },
        {
          title: "操作人",
          align: "left",
          dataIndex: "userName",
          width: 30,
        },
        {
          title: "内容",
          align: "left",
          dataIndex: "content",
          width: 185,
        },
      ],
      showYearMonth: {}, // 显示的年月
      calendarList: [], // 用于遍历显示
      shareDate: new Date(), // 享元模式，用来做 日期数据转换 优化
      dayChecked: {}, // 当前选择的天
      weekList: ["一", "二", "三", "四", "五", "六", "日"], // 周
      makeupVisible: false,
      Form: {
        startDate: undefined,
        endDate: undefined,
        isHoliday: false,
      },
      rules: {
        startDate: [{ required: true, message: "请选择开始时间", trigger: ["blur", "change"] }],
        endDate: [{ required: true, message: "请选择结束时间", trigger: ["blur", "change"] }],
      },
    };
  },
  created() {
    setTimeout(() => {
      this.initDataFun(); // 初始化数据
    }, 500);
  },
  computed: {
    // 显示当前时间
    currentDateStr() {
      let { year, month } = this.showYearMonth;
      return `${year}年${this.pad(month + 1)}月`;
    },
  },
  methods: {
    //#region 计算日历数据
    // 初始化数据
    initDataFun() {
      // 初始化当前时间
      this.setCurrentYearMonth(); // 设置日历显示的日期（年-月）
      this.createCalendar(); // 创建当前月对应日历的日期数据
      this.getCurrentDay(); // 获取今天
    },
    // 设置日历显示的日期（年-月）
    setCurrentYearMonth(d = new Date()) {
      let year = d.getFullYear();
      let month = d.getMonth();
      let date = d.getDate();
      this.showYearMonth = {
        year,
        month,
        date,
      };
    },
    getCurrentDay(d = new Date()) {
      let year = d.getFullYear();
      let month = d.getMonth();
      let date = d.getDate();
      this.dayChecked = {
        year,
        month,
        date,
        value: this.stringify(year, month, date),
        disable: false,
      };
    },
    // 创建当前月对应日历的日期数据
    createCalendar(startDate) {
      // 一天有多少毫秒
      const oneDayMS = 24 * 60 * 60 * 1000;
      let list = [];
      let list1 = [];
      let str = "";
      if (startDate) {
        str = startDate;
        this.showYearMonth = {
          date: startDate.split("-")[2],
          month: startDate.split("-")[1] - 1,
          year: startDate.split("-")[0],
        };
      } else {
        str = this.currentDateStr;
      }
      calendarList(str, this.factoryId).then(res => {
        if (res.code) {
          list = res.data;
          let { year, month } = this.showYearMonth;
          let firstDay = this.getFirstDayByMonths(year, month);
          let prefixDaysLen = firstDay === 0 ? 6 : firstDay - 1;
          let begin = new Date(year, month, 1).getTime() - oneDayMS * prefixDaysLen;
          let lastDay = this.getLastDayByMonth(year, month);
          let suffixDaysLen = lastDay === 0 ? 0 : 7 - lastDay;
          let end = new Date(year, month + 1, 0).getTime() + oneDayMS * suffixDaysLen;
          // 填充天
          while (begin <= end) {
            this.shareDate.setTime(begin);
            let year = this.shareDate.getFullYear();
            let curMonth = this.shareDate.getMonth();
            let date = this.shareDate.getDate();
            let arr = list.filter(item => {
              return item.day == date;
            });
            let isHoliday = false;
            let dayOfWeek = null;
            let isWeekend = false;
            if (arr.length && curMonth == month) {
              isHoliday = arr[0].isHoliday;
              dayOfWeek = arr[0].dayOfWeek;
              isWeekend = arr[0].isWeekend;
            }
            list1.push({
              year: year,
              month: curMonth + 1, // 月是从0开始的
              date: date,
              value: this.stringify(year, curMonth, date),
              disable: curMonth !== month,
              note: "",
              checked: false,
              dayOfWeek: dayOfWeek,
              isWeekend: isWeekend,
              isHoliday: isHoliday,
            });
            begin += oneDayMS;
          }
          this.calendarList = list1;
          console.log("this.calendarList", this.calendarList);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 格式化时间
    stringify(year, month, date) {
      let str = [year, this.pad(month + 1), this.pad(date)].join("-");
      return str;
    },
    // 对小于 10 的数字，前面补 0
    pad(str) {
      return str < 10 ? `0${str}` : str;
    },
    // 点击上一月
    prevMonth() {
      this.showYearMonth.month--;
      this.recalculateYearMonth(); // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.createCalendar(); // 创建当前月对应日历的日期数据
    },
    // 点击下一月
    nextMonth() {
      this.showYearMonth.month++;
      this.recalculateYearMonth(); // 因为 month的变化 会超出 0-11 的范围， 所以需要重新计算
      this.createCalendar(); // 创建当前月对应日历的日期数据
    },
    // 重算：显示的某年某月
    recalculateYearMonth() {
      let { year, month, date } = this.showYearMonth;

      let maxDate = this.getDaysByMonth(year, month);
      // 预防其他月跳转到2月，2月最多只有29天，没有30-31
      date = Math.min(maxDate, date);

      let instance = new Date(year, month, date);
      this.setCurrentYearMonth(instance);
    },
    // 判断当前月有多少天
    getDaysByMonth(year, month) {
      return new Date(year, month + 1, 0).getDate();
    },
    // 当前月的第一天是星期几
    getFirstDayByMonths(year, month) {
      return new Date(year, month, 1).getDay();
    },
    // 当前月的最后一天是星期几
    getLastDayByMonth(year, month) {
      return new Date(year, month + 1, 0).getDay();
    },
    // #endregion 计算日历数据

    // 操作：点击了某天
    handleClickDay(item) {
      if (!item || item.disable) return;
      console.log(item);
      console.log("item", item);
      // if(item.note){
      //   item.note = ''
      // }else{
      //   item.note = '选中'
      // }
      if (item.checked) {
        item.checked = false;
      } else {
        item.checked = true;
        // this.dayChecked = item;
      }
    },
    handleCancel() {
      this.makeupVisible = false;
      this.Form.isHoliday = false;
    },
    moment,
    onChange1(value, dateString) {
      this.Form.startDate = dateString;
    },
    onChange2(value, dateString) {
      this.Form.endDate = dateString;
    },
    setClick() {
      this.makeupVisible = true;
    },
    OperationLog() {
      calendarlogfactoryid(this.factoryId).then(res => {
        if (res.code) {
          this.labordata = res.data;
          this.labordataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel() {
      this.labordataVisible = false;
    },
    handleOkMode() {
      const form = this.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          setHoliday(this.Form.startDate, this.Form.endDate, this.Form.isHoliday, this.factoryId)
            .then(res => {
              if (res.code) {
                this.createCalendar(this.Form.startDate);
                this.Form.isHoliday = false;
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.makeupVisible = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.projectackend {
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
@calendarWidth: 427px; // 60 * 7 + 7 * 1
.calendar-box {
  // width: 50vw;
  width: 660px;
  margin-top: 30px;
  // height:500px;
  display: flex;
  justify-content: center;
  align-items: center;
  .calendar-wrapper {
    .calendar-toolbar {
      width: @calendarWidth;
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .prev,
      .next,
      .current {
        cursor: pointer;
        &:hover {
          color: #ff9900;
        }
      }
    }
    .calendar-week {
      width: @calendarWidth;
      border-left: 1px solid #eee;
      display: flex;
      flex-wrap: wrap;
      .week-item {
        width: 60px;
        height: 50px;
        border-top: 1px solid #eee;
      }
    }
    .calendar-inner {
      width: @calendarWidth;
      border-left: 1px solid #eee;
      display: flex;
      flex-wrap: wrap;
      .calendar-item {
        width: 60px;
        height: 60px;
      }
      .calendar-item-hover {
        cursor: pointer;
        p {
          &:hover {
            color: #fff;
            border-radius: 30px;
            background-color: #ffc36c;
          }
        }
        // p:first-child{
        //   &:hover {
        //     color: #fff;
        //     border-radius: 30px;
        //     background-color: #9ec1f1;
        //   }
        // }
        // p:last-child{
        //   &:hover {
        //     color: #ffc36c;
        //   }
        // }
      }
      .calendar-item-disabled {
        color: #acacac;
        cursor: not-allowed;
      }
      .calendar-item-checked {
        p {
          color: #fff;
          background-color: #ff9900;
        }
        // p:first-child{
        //   color: #fff;
        //   background-color: #ff9900;
        // }
      }
      .calendar-item-checked:hover {
        p {
          color: #fff;
          background-color: #ff9900 !important;
        }
        // p:first-child{
        //   color: #fff;
        //   background-color: #ff9900!important;
        // }
        // p:last-child{
        //   color: #fff;

        // }
      }
      .item-checked {
        p {
          color: #fff;
          background-color: #ffc36c;
        }
        // p:first-child{
        //   color: #fff;
        //   background-color: #ffc36c;
        // }
        // p:last-child{
        //   color: #ffc36c;
        // }
      }
      .item-checked:hover {
        p {
          color: #fff;
          background-color: #ffc36c !important;
        }

        // p:first-child{
        //   color: #fff;
        //   background-color: #ffc36c!important;
        // }
        // p:last-child{
        //   color:#ffc36c;
        // }
      }
    }
    .calendarBorder {
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom: 1px solid #eee;
      border-right: 1px solid #eee;
    }
  }
}
</style>
