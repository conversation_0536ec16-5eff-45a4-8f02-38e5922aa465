<template>
  <div>
    <h4 style="font-weight:600;font-size:14px;color:#000000;text-indent:20px">{{title}} <span class="dayComplatePanel" v-if="!(dayComplatePanel < 0)">（拼板：{{dayComplatePanel}}件，子料号：{{pinBanDetaileOrderNum}}件）</span></h4>
    <div id="myechart" style="height: 500px;color:#000000;"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts/core';
import {
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components';
import { BarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  CanvasRenderer
]);
  export default {
    props:["title", "barData", "dayComplatePanel","pinBanDetaileOrderNum"],
    data() {
      return {
        data:[],
      };
    },
    mounted() {
      this.echartInit()
      console.log(this.barData)
    },
    methods: {
      echartInit () {
        const option = {
          tooltip: {
            // trigger: 'axis',
            formatter(params){
              var result = ''
                var dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#ccc"></span>'
                var dotHtml2 = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#ff9900"></span>'
                var dotHtml3 = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#fff9e6"></span>'
                result += params.name + "</br>" + dotHtml +'目标：' + params.data.target+ "</br>" + dotHtml2 +'实际：' + params.data.area + "</br>" + dotHtml3 +'拼板：' + params.data.num
                return result
            },
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['目标', '实际'],
          },
          xAxis: {
            data: this.barData.xData,
            axisLabel: {  
              interval:0,  
              // rotate:40  
            }  
          },
          color: ['#ccc', '#ff9900'],
          yAxis: {
            axisLabel: {
              formatter: '{value}'
            }
          },
          
            grid: {
                x: 30,
                y: 50,
                x2: 30,
                y2: 30,
                containLabel: true,

            },
          series: [
            {
              name: '目标',
              type: 'bar',
              z: '-1',
              barGap: '-100%',
              data: this.barData.y2Data
            },
            {
              name: '实际',
              type: 'bar',
              data: this.barData.y1Data
            }
          ]
        };
        var chartDom = document.getElementById('myechart');
        var myChart = echarts.init(chartDom);
        myChart.setOption(option)
      }
      
    }
  };
</script>
<style scoped>
.dayComplatePanel {
  color: #ff9900;
  font-weight: 600;
}
</style>