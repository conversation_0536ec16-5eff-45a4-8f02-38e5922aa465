<template>
  <div ref="SelectBox">
    <div>
      <a-form-model layout="inline" style="margin-top: 10px; border-top: 1px solid #ddd" id="formDataElemTwo">
        <a-row>
          <a-col :span="4" class="colSTY">
            <a-form-model-item label="本厂编号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" class="disable">
              <span :title="proOrderInfoDto.orderNo" class="tmp">{{ proOrderInfoDto.orderNo }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" class="colSTY">
            <a-form-model-item label="客户型号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" class="disable">
              <span :title="proOrderInfoDto.pcbFileName" class="tmp" @click="dwon1(proOrderInfoDto.pcbFilePath)">{{
                proOrderInfoDto.pcbFileName
              }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="板类型"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.plateType && iseval(requiredLinkConfigpro.plateType.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.plateType"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  @change="changelayers"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-focus-next-on-tab="'2'"
                  ref="1"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.PlateType)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" class="colSTY">
            <a-form-model-item label="客户物料号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :class="[editFlg1 ? 'disable' : '']">
              <span :title="proOrderInfoDto.customerMaterialNo" style="cursor: pointer" class="tmp">{{ proOrderInfoDto.customerMaterialNo }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="成品板厚mm"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.boardThickness && iseval(requiredLinkConfigpro.boardThickness.isNullRules) ? 'require' : ''"
            >
              <div style="display: flex">
                <a-select
                  v-model="proOrderInfoDto.boardThickness"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate($event, mapKey(selectData.BoardThickness))"
                  @search="handleSearch($event, mapKey(selectData.BoardThickness))"
                  @blur="handleBlur($event, mapKey(selectData.BoardThickness))"
                  v-focus-next-on-tab="'3'"
                  ref="2"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.BoardThickness)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  v-model="proOrderInfoDto.boardThicknessTol"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate9($event, mapKey(selectData.BoardThicknessTol))"
                  @search="handleSearch9($event, mapKey(selectData.BoardThicknessTol))"
                  @blur="handleBlur9($event, mapKey(selectData.BoardThicknessTol))"
                  v-focus-next-on-tab="'4'"
                  ref="3"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.BoardThicknessTol)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4" class="colSTY">
            <a-form-model-item
              label="层数"
              :label-col="{ span: 10 }"
              :wrapper-col="{ span: 14 }"
              :class="requiredLinkConfigpro.boardLayers && iseval(requiredLinkConfigpro.boardLayers.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.boardLayers"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate1($event, mapKey(selectData.BoardLayers))"
                  @search="handleSearch1($event, mapKey(selectData.BoardLayers))"
                  @blur="handleBlur1($event, mapKey(selectData.BoardLayers))"
                  ref="4"
                  v-focus-next-on-tab="'5'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.BoardLayers)"
                    :key="item.value"
                    :value="item.value"
                    :label="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="成品铜厚oz"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.copperThickness && iseval(requiredLinkConfigpro.copperThickness.isNullRules) ? 'require' : ''"
            >
              <div class="editWrapper" style="display: flex">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%" v-if="Number(proOrderInfoDto.boardLayers) > 2">内</span>
                <a-select
                  v-model="proOrderInfoDto.innerCopperThickness"
                  v-if="Number(proOrderInfoDto.boardLayers) > 2"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  disabled
                  ref="5"
                  v-focus-next-on-tab="'6'"
                  @change="setEstimate2($event, mapKey(selectData.InnerCopperThickness))"
                  @search="handleSearch2($event, mapKey(selectData.InnerCopperThickness))"
                  @blur="handleBlur2($event, mapKey(selectData.InnerCopperThickness))"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.InnerCopperThickness)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%">外</span>
                <a-select
                  v-model="proOrderInfoDto.copperThickness"
                  ref="6"
                  v-focus-next-on-tab="'7'"
                  showSearch
                  disabled
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate3($event, mapKey(selectData.CopperThickness))"
                  @search="handleSearch3($event, mapKey(selectData.CopperThickness))"
                  @blur="handleBlur3($event, mapKey(selectData.CopperThickness))"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.CopperThickness)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
            <a-form-model-item
              label="成品铜厚um"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              v-if="proOrderInfoDto.isCopperThickConversion"
              :class="requiredLinkConfigpro.copperThickness && iseval(requiredLinkConfigpro.copperThickness.isNullRules) ? 'require' : ''"
            >
              <div class="editWrapper" style="display: flex">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%" v-if="Number(proOrderInfoDto.boardLayers) > 2">内</span>
                <a-select
                  v-model="proOrderInfoDto.innerCopperThickness2"
                  ref="7"
                  v-focus-next-on-tab="'8'"
                  v-if="Number(proOrderInfoDto.boardLayers) > 2"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  disabled
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.InnerCopperThickness2)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                    :disabled="true"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%">外</span>
                <a-select
                  v-model="proOrderInfoDto.copperThickness2"
                  showSearch
                  allowClear
                  ref="8"
                  v-focus-next-on-tab="'9'"
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  disabled
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectData.CopperThickness2)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                    :disabled="true"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="阻焊颜色"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.solderColor && iseval(requiredLinkConfigpro.solderColor.isNullRules) ? 'require' : ''"
            >
              <div>
                <b style="color: red; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [顶] </b>
                <a-select
                  v-model="proOrderInfoDto.solderColor"
                  style="width: 90px"
                  showSearch
                  ref="9"
                  v-focus-next-on-tab="'10'"
                  allowClear
                  optionFilterProp="lable"
                  @change="solderColorC('change')"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SolderColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <b style="color: green; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [底] </b>
                <a-select
                  v-model="proOrderInfoDto.solderColorBottom"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="10"
                  v-focus-next-on-tab="'11'"
                  @change="solderColorC('change', 'bot')"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SolderColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-left: 10px">阻焊特性:</span>
                <a-select
                  v-model="proOrderInfoDto.solderCharacteristics"
                  style="width: 83px; margin-left: 5px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="11"
                  v-focus-next-on-tab="'12'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.SolderCharacteristics)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="阻焊油墨"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.solderResistInk && iseval(requiredLinkConfigpro.solderResistInk.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.solderResistInk"
                  style="width: 231px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="12"
                  v-focus-next-on-tab="'13'"
                >
                  <a-select-option v-for="item in SolderResistInk1" :key="item.value" :value="item.value" :lable="item.lable" :title="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="padding-left: 10px; padding-right: 10px">无卤油墨 :</span>
                <a-checkbox
                  v-model="proOrderInfoDto.isInkNotHalogen"
                  @keydown.native.enter="handleLastInputEnter"
                  v-focus-next-on-tab="'14'"
                  ref="13"
                  @change="solderColorC('change', 'bot')"
                />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>
<script>
let index = -1;
import { productInformation, soldercolorresistinkrelation, fontcolorresistinkrelation } from "@/services/projectIndicate";
import $ from "jquery";
export default {
  name: "ProThree",
  props: ["proOrderInfoDto", "selectData", "editFlg1", "messageList", "requiredLinkConfigpro", "SolderResistInk1"],
  inject: ["reload"],
  components: {},
  data() {
    return {};
  },

  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  directives: {
    focusNextOnTab: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode !== 13) return; // 非回车键直接返回

          // 清理无效的 refs 引用
          console.log(index, "447");
          const refs = vnode.context.$refs;
          for (const key in refs) {
            if (!refs[key]) delete refs[key];
          }

          const refKeys = Object.keys(refs);
          const currentIndex = refKeys.indexOf(value); // 当前元素索引

          // 情况1：目标元素不存在于 refs 列表
          if (currentIndex === -1) {
            const nextIndex = typeof index !== "undefined" ? index + 1 : 0;
            if (nextIndex < refKeys.length) {
              const nextInput = refs[refKeys[nextIndex]];
              if (nextInput && !nextInput.disabled) {
                index = nextIndex;
                nextInput.focus();
              }
            }
          }
          // 情况2：目标元素存在且可用
          else if (!refs[value].disabled) {
            index = currentIndex;
            refs[value].focus();
          }
          // 情况3：向后查找最多12个可用元素
          else {
            for (let offset = 1; offset <= 12; offset++) {
              const targetIndex = currentIndex + offset;
              if (targetIndex >= refKeys.length) break;

              const targetKey = refKeys[targetIndex];
              const targetEl = refs[targetKey];

              if (targetEl && !targetEl.disabled) {
                targetEl.focus();
                break; // 找到第一个可用元素后停止
              }
            }
          }
        });
      },
    },
  },
  watch: {
    messageList: {
      handler(val) {
        this.get(val);
      },
    },
  },
  methods: {
    handleLastInputEnter(e) {
      event.preventDefault(); // 阻止默认行为（如打开下拉框）
      this.$emit("jumptotwo2");
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("val") != -1) {
        newIsNullRules = newIsNullRules.replace(/val/g, "this.proOrderInfoDto");
      }
      return eval(newIsNullRules);
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    // 板厚更改 板厚公差=+/-板厚*0.1
    boardThickness() {
      this.$emit("boardThickness");
      if (this.proOrderInfoDto.boardThickness) {
        if (this.proOrderInfoDto.boardThickness <= 1.0) {
          this.proOrderInfoDto.boardThicknessTol = "+/-0.1";
        } else {
          this.proOrderInfoDto.boardThicknessTol = "+/-" + (this.proOrderInfoDto.boardThickness * 0.1).toFixed(2);
        }
      }
    },
    changxb() {
      if (!this.proOrderInfoDto.goldfingerBevel) {
        this.proOrderInfoDto.goldfingerBevelAngle = null;
        this.proOrderInfoDto.goldfingerBevelDepth = null;
        this.proOrderInfoDto.goldfingerBevelSurplus = null;
      }
    },
    goldenfingerChange() {
      if (!this.proOrderInfoDto.goldenfinger) {
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },
    MetalSlotChange() {
      if (this.$route.query.factory == 12) {
        if (this.proOrderInfoDto.isMetalSlot) {
          this.proOrderInfoDto.cncHoleTol = "+/-0.075";
        } else {
          this.proOrderInfoDto.cncHoleTol = "";
        }
      }
    },
    // 拼版方式更改
    pinBanType() {
      if (this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1) {
        if (this.proOrderInfoDto.boardType == "pcs") {
          this.$message.warning("拼版方式大于1x1,出货单位请选择set");
        }
      }
      // if(this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1 ){
      //   this.proOrderInfoDto.boardType = 'set'
      // }else{
      //   this.proOrderInfoDto.boardType = 'pcs'
      // }
    },
    //
    flyChange() {
      if (this.proOrderInfoDto.flyingProbe == "FlyingProbe") {
        this.proOrderInfoDto.fpTestMethod = "capacitance";
        this.proOrderInfoDto.testFixtureNumber = "";
      }
      if (
        this.proOrderInfoDto.flyingProbe == "custstand" ||
        this.proOrderInfoDto.flyingProbe == "newstand" ||
        this.proOrderInfoDto.flyingProbe == "sharestand" ||
        this.proOrderInfoDto.flyingProbe == "teststand"
      ) {
        this.proOrderInfoDto.testFixtureNumber = this.proOrderInfoDto.orderNo;
        this.proOrderInfoDto.fpTestMethod = "";
      }
    },
    changeplugoil() {
      if (
        this.proOrderInfoDto.solderCover == "plugoil" ||
        this.proOrderInfoDto.solderCover == "bgaplugoil" ||
        this.proOrderInfoDto.solderCover == "aluminiumplugoil" ||
        this.proOrderInfoDto.solderCover == "openwindowplusplugoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+converoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+openminwindow"
      ) {
        if (this.$route.query.factory == 38) {
          this.proOrderInfoDto.plugOilTool = "consentprinting";
        } else {
          this.proOrderInfoDto.plugOilTool = "aluminumsheet";
        }
        if (this.$route.query.factory != 58 && this.$route.query.factory != 59) {
          this.proOrderInfoDto.plugOilSide = "toplayer";
        }
      } else {
        this.proOrderInfoDto.plugOilTool = null;
        this.proOrderInfoDto.plugOilSide = null;
      }
    },
    changevcut() {
      if (this.proOrderInfoDto.formingType != "vcut" || this.proOrderInfoDto.formingType != "machinemold+vcut") {
        this.proOrderInfoDto.vCut = null;
        this.proOrderInfoDto.jumpCut = false;
        this.proOrderInfoDto.vcutSurplusThickness = null;
        this.proOrderInfoDto.vcutSurplusThicknessTol = null;
        this.proOrderInfoDto.vcutAngle = null;
        this.proOrderInfoDto.vcutAngleTol = null;
        this.proOrderInfoDto.vCutKnifeNum = null;
        this.proOrderInfoDto.vCutCoppershaved = null;
      }
      if (
        this.proOrderInfoDto.formingType == "vcut" ||
        this.proOrderInfoDto.formingType == "machinemold+vcut" ||
        this.proOrderInfoDto.formingType == "vcut+rtr" ||
        this.proOrderInfoDto.formingType == "mechanical_milling+vcut"
      ) {
        this.proOrderInfoDto.vCut = "ncvcut";
        this.proOrderInfoDto.vcutAngle = "30";
        this.proOrderInfoDto.vcutAngleTol = "1";
        let value = "";
        let Thickness = Number(this.proOrderInfoDto.boardThickness);
        if (this.$route.query.factory == "12") {
          if (Thickness >= 0.4 && Thickness <= 0.8) {
            value = "0.25";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          } else if (Thickness > 0.8 && Thickness <= 1.0) {
            value = "0.30";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          } else if (Thickness > 1 && Thickness <= 1.4) {
            value = "0.35";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          } else if (Thickness > 1.4 && Thickness <= 1.8) {
            value = "0.4";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          } else if (Thickness > 1.8 && Thickness <= 2.2) {
            value = "0.6";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          } else if (Thickness > 2.2) {
            value = "0.75";
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          }
        } else if (this.$route.query.factory == "69") {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
          if (Thickness == 2) {
            value = "0.5";
          } else if (Thickness == 1.6) {
            value = "0.4";
          } else if (Thickness == 1.2) {
            value = "0.35";
          } else if (Thickness == 1.0) {
            value = "0.3";
          } else if (Thickness == 0.6 || Thickness == 0.8) {
            value = "0.25";
          } else if (Thickness > 1) {
            value = (Thickness / 4).toFixed(2);
          } else if (Thickness < 1) {
            value = (Thickness / 3).toFixed(2);
          }
        } else if (this.$route.query.factory == "22") {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          if (this.proOrderInfoDto.custNo.indexOf("681") > -1) {
            if (Thickness > 0.6 && Thickness <= 1) {
              value = "0.35";
            } else if (Thickness > 1) {
              value = "0.4";
            }
          } else {
            if (Thickness >= 0.4 && Thickness <= 0.6) {
              value = "0.25";
            } else if (Thickness > 0.6 && Thickness <= 1.1) {
              value = "0.3";
            } else if (Thickness > 1.1 && Thickness <= 1.59) {
              value = "0.35";
            } else if (Thickness >= 1.6) {
              value = "0.4";
            }
          }
        } else if (this.$route.query.factory == "58" || this.$route.query.factory == "59") {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "";
          this.proOrderInfoDto.vcutAngle = "25";
          value = "";
          if (this.proOrderInfoDto.fR4Type == "fr4") {
            this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
            if (Thickness >= 0.4 && Thickness < 0.6) {
              value = "0.15";
              this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
            } else if (Thickness >= 0.6 && Thickness < 0.8) {
              value = "0.2";
              this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.05";
            } else if (Thickness >= 0.8 && Thickness < 1) {
              value = "0.25";
            } else if (Thickness >= 1.0 && Thickness < 1.2) {
              value = "0.3";
            } else if (Thickness >= 1.2 && Thickness < 1.6) {
              value = "0.35";
            } else if (Thickness >= 1.6 && Thickness < 2) {
              value = "0.4";
            } else if (Thickness >= 2 && Thickness < 2.5) {
              value = "0.5";
            } else if (Thickness >= 2.5 && Thickness < 3) {
              value = "0.6";
            } else if (Thickness >= 3) {
              value = "0.7";
            }
          }
        } else {
          this.proOrderInfoDto.vcutSurplusThicknessTol = "+/-0.10";
          if (Thickness < 0.6) {
            value = "0.2";
          } else if (Thickness >= 0.6 && Thickness <= 0.8) {
            value = "0.25";
          } else if (Thickness > 0.8 && Thickness < 1.2) {
            value = "0.28";
          } else if (Thickness >= 1.2 && Thickness < 1.6) {
            value = "0.35";
          } else if (Thickness >= 1.6) {
            value = "0.45";
          }
        }
        this.setEstimate5(value, this.mapKey(this.selectData.VcutSurplusThickness));
      }
    },
    changesurface() {
      if (
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandhaslwithfree" ||
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandimmersiongold" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithfreeandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithleadandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "nickelplatingandgoldplatedfinger" ||
        this.proOrderInfoDto.surfaceFinish == "ospandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "wholeimmersiongoldandimmersiongoldfinger"
      ) {
        this.proOrderInfoDto.isGoldfinger = true;
      } else {
        this.proOrderInfoDto.isGoldfinger = false;
      }
    },
    changelayers() {
      this.$emit("changelayers");
    },
    solderColorC(type, val) {
      this.$emit("solderColorC", type, val);
    },
    changesu() {
      if (this.proOrderInfoDto.pinBanType1 && this.proOrderInfoDto.pinBanType2) {
        this.proOrderInfoDto.su = (Number(this.proOrderInfoDto.pinBanType1) * this.proOrderInfoDto.pinBanType2).toFixed();
      } else {
        this.proOrderInfoDto.su = null;
      }
    },
    optionClick() {},
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate(value, list) {
      console.log("1028", value);
      this.proOrderInfoDto.boardThickness = value;
      this.changevcut();
    },

    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
      if (this.proOrderInfoDto.boardThickness) {
        var boardThickness = this.proOrderInfoDto.boardThickness.toString().split("");
        if (boardThickness.length > 6) {
          boardThickness = boardThickness.slice(0, 6);
          this.$message.warning("成品板厚不能超过6个字符");
        }
        this.proOrderInfoDto.boardThickness = boardThickness.join("");
      }
    },

    setEstimateb(value, list) {
      this.proOrderInfoDto.deliveryMethod = value;
    },
    handleBlurb(value, list) {
      this.setEstimateb(value, list);
    },
    handleSearchb(value, list) {
      this.setEstimateb(value, list);
    },
    setEstimate1(value, list) {
      this.proOrderInfoDto.boardLayers = value;
      let a = this.getPrice(this.proOrderInfoDto.boardLayers, list, value);
      this.changelayers();
    },
    handleSearch1(value, list) {
      this.setEstimate1(value, list);
    },
    handleBlur1(value, list) {
      this.setEstimate1(value, list);
      if (this.proOrderInfoDto.boardLayers) {
        var boardLayers = this.proOrderInfoDto.boardLayers.split("");
        if (boardLayers.length > 2) {
          boardLayers = boardLayers.slice(0, 2);
          this.$message.warning("层数不能超过2个字符");
        }
        this.proOrderInfoDto.boardLayers = boardLayers.join("");
      }
    },
    setEstimate2(value, list) {
      this.proOrderInfoDto.innerCopperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.innerCopperThickness, list, value);
    },
    handleSearch2(value, list) {
      this.setEstimate2(value, list);
    },
    handleBlur2(value, list) {
      this.setEstimate2(value, list);
      if (this.proOrderInfoDto.innerCopperThickness) {
        var innerCopperThickness = this.proOrderInfoDto.innerCopperThickness.toString().split("");
        if (innerCopperThickness.length > 3) {
          innerCopperThickness = innerCopperThickness.slice(0, 3);
          this.$message.warning("成品铜厚内层不能超过3个字符");
        }
        this.proOrderInfoDto.innerCopperThickness = innerCopperThickness.join("");
      }
    },
    setEstimate3(value, list) {
      this.proOrderInfoDto.copperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.copperThickness, list, value);
    },
    handleSearch3(value, list) {
      this.setEstimate3(value, list);
    },
    handleBlur3(value, list) {
      this.setEstimate3(value, list);
      if (this.proOrderInfoDto.copperThickness) {
        var copperThickness = this.proOrderInfoDto.copperThickness.toString().split("");
        if (copperThickness.length > 3) {
          copperThickness = copperThickness.slice(0, 3);
          this.$message.warning("成品铜厚外层不能超过3个字符");
        }
        this.proOrderInfoDto.copperThickness = copperThickness.join("");
      }
    },
    setEstimate4(value, list) {
      this.proOrderInfoDto.warpage = value;
      let a = this.getPrice(this.proOrderInfoDto.warpage, list, value);
    },
    handleSearch4(value, list) {
      this.setEstimate4(value, list);
    },
    handleBlur4(value, list) {
      this.setEstimate4(value, list);
      if (this.proOrderInfoDto.warpage) {
        var warpage = this.proOrderInfoDto.warpage.split("");
        if (warpage.length > 6) {
          warpage = warpage.slice(0, 6);
          this.$message.warning("翘曲度不能超过6个字符");
        }
        this.proOrderInfoDto.warpage = warpage.join("");
      }
    },
    setEstimate5(value, list) {
      this.proOrderInfoDto.vcutSurplusThickness = value;
      console.log(this.proOrderInfoDto.vcutSurplusThickness, "this.proOrderInfoDto.vcutSurplusThickness");
    },
    handleSearch5(value, list) {
      this.setEstimate5(value, list);
    },
    handleBlur5(value, list) {
      this.setEstimate5(value, list);
    },
    setEstimate6(value, list) {
      this.proOrderInfoDto.minSolderBridge = value;
      let a = this.getPrice(this.proOrderInfoDto.minSolderBridge, list, value);
    },
    handleSearch6(value, list) {
      this.setEstimate6(value, list);
    },
    handleBlur6(value, list) {
      this.setEstimate6(value, list);
    },
    setEstimate7(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness, list, value);
    },
    handleSearch7(value, list) {
      this.setEstimate7(value, list);
    },
    handleBlur7(value, list) {
      this.setEstimate7(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness) {
        var cjNickelThinckness = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness.split("");
        if (cjNickelThinckness.length > 10) {
          cjNickelThinckness = cjNickelThinckness.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = cjNickelThinckness.join("");
      }
    },
    setEstimate8(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2, list, value);
    },
    handleSearch8(value, list) {
      this.setEstimate8(value, list);
    },
    handleBlur8(value, list) {
      this.setEstimate8(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2) {
        var cjNickelThinckness2 = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2.split("");
        if (cjNickelThinckness2.length > 10) {
          cjNickelThinckness2 = cjNickelThinckness2.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = cjNickelThinckness2.join("");
      }
    },
    setEstimate9(value, list) {
      this.proOrderInfoDto.boardThicknessTol = value;
      let a = this.getPrice(this.proOrderInfoDto.boardThicknessTol, list, value);
    },
    handleSearch9(value, list) {
      this.setEstimate9(value, list);
    },
    handleBlur9(value, list) {
      this.setEstimate9(value, list);
    },
    setEstimate10(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = value;
    },
    handleSearch10(value, list) {
      this.setEstimate10(value, list);
    },
    handleBlur10(value, list) {
      this.setEstimate10(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness) {
        var newTinThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness.split("");
        if (newTinThickness.length > 10) {
          newTinThickness = newTinThickness.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = newTinThickness.join("");
      }
    },
    setEstimate11(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = value;
    },
    handleSearch11(value, list) {
      this.setEstimate11(value, list);
    },
    handleBlur11(value, list) {
      this.setEstimate11(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2) {
        var newTinThickness2 = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2.split("");
        if (newTinThickness2.length > 10) {
          newTinThickness2 = newTinThickness2.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = newTinThickness2.join("");
      }
    },
    get(val) {
      $("#formDataElemTwo .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
  },
  created() {},
};
</script>
<style scoped lang="less">
/deep/.ant-col-20 {
  width: 1291px;
}
/deep/.ant-col-4 {
  width: 258px;
}
/deep/.ant-col-3 {
  width: 194px;
}
/deep/.ant-col-8 {
  width: 516px;
}
/deep/.ant-col-9 {
  width: 582px;
}
.div1 {
  /deep/.ant-form-item-control {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.div2 {
  .div22 {
    /deep/.ant-form-item-control {
      padding: 0;
      min-height: 28px !important;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}

.autoo {
  /deep/.ant-form-item-control {
    height: 100% !important;
    width: 1184px;
  }
  /deep/.ant-form-item-control {
    // height: auto!important;
    background: #f5f5f5 !important;
    .ant-input {
      margin-top: 4px !important;
      margin-bottom: 0 !important;
    }
  }
}
/deep/textarea.ant-input {
  min-height: 24px;
  line-height: 1.3;
}
.speclass {
  /deep/ .ant-form-item-label {
    width: 107px;
  }
  /deep/ .ant-form-item-label > label {
    font-size: 13px !important;
  }
  /deep/ .ant-form-item-control-wrapper {
    width: 1178px;
  }
}
/deep/ .div1 {
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px !important;
    }
  }
  .ant-form-item-children {
    overflow: inherit !important;
    font-size: 13px;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  /* border-color: #ff9900; */
  border: 2px solid red;
}
/deep/.ant-select-focused .ant-select-selection {
  border: 2px solid red;
}
/deep/.ant-input:focus {
  border: 2px solid red;
}
/deep/.colSTY {
  border-left: 1px solid #ddd;
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px;
    }
  }
}
// /deep/.ant-col{
//   border-left:1px solid #ddd;
// }
/deep/.sss {
  height: 30px;
}
/deep/.ant-col-3 {
  .ant-col-14 {
    width: 58.5%;
  }
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-input {
  padding: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 11px !important;
  margin-left: 6px !important;
}

/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
// /deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
//     height: 16px!important;
//     margin-top: 3px;
//     line-height: 14px!important;
//   }
/deep/.surSTY {
  // height:56px;
  .ant-form-item-control-wrapper {
    // min-height:20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
      }
    }
  }
}
/deep/.heightSty {
  height: 28px;
  .ant-select {
    margin-top: 2px;
  }
  .ant-select-selection--multiple {
    height: 20px;
    min-height: 20px;
  }
  .ant-select-allow-clear {
    .ant-select-selection--multiple {
      height: 23px;
    }
    .ant-select-selection__rendered {
      ul {
        display: flex;
        li {
          height: 16px;
          margin-top: 2px;
          line-height: 16px;
          user-select: none;
          padding-left: 0 !important;
        }
      }
    }
    .ant-select-selection__clear {
      top: 13px;
    }
  }
}
/deep/.heightSty1 {
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
        // .edit{
        //  line-height: 28px!important;
        // text-overflow: unset!important;
        // white-space: unset!important;
        // }
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 17px !important;
            margin-top: 3px;
            line-height: 15px !important;
            width: 92%;
          }

          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}

/deep/.ant-select-dropdown-menu-item {
  font-size: 14px;
  padding: 0.5%;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-size: 14px !important;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-size: 14px !important;
  font-weight: 500;

  color: #000000;
}

.ant-row {
  .ant-col-22 {
    .ant-form-item-control {
      .ant-input-affix-wrapper {
        line-height: 29px;
      }
    }
  }
  .ant-col-17 {
    .ant-form-item {
      /deep/.ant-input {
        min-height: 23px !important;
        height: 23px !important;
        line-height: 15px !important;
      }
    }
  }
  .ant-col-24 {
    /deep/.ant-form-item-label {
      width: 106px !important;
    }
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.disable {
  background: #f5f5f5 !important;
}

/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;
  min-height: 28px;
  .tmp1 {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 370px;
    display: inline-block;
  }
  .tmp {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 140px;
    display: inline-block;
  }
  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 25px;
    .ant-select {
      width: 20;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #f1f1f1;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        // display: block;
        // min-height: 25px;
        line-height: 25px;
        // // vertical-align: top;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
        // width: 100%;
        // display: inline-block;

        // .ant-select-allow-clear{
        //   // .ant-select-selection--multiple{
        //   //   height: 23px;
        //   //   margin-top:2px;
        //   // }
        //   .ant-select-selection__rendered{
        //     ul{
        //       display: flex;
        //       li{
        //         margin-top:-1px;
        //       }
        //     }
        //     .ant-select-selection__choice{
        //       height: 18px;
        //       margin-top: 2px;
        //       line-height: 14px;
        //       user-select: none;
        //     }
        //   }
        //   .ant-select-selection__clear{
        //     top:11px;
        //   }
        // }
        .ant-checkbox-wrapper {
          min-height: 28px;
        }
        .ant-select-selection--single {
          height: 22px;
        }
        .ant-select-selection__rendered {
          line-height: 18px;
        }
        .ant-select {
          height: 22px;
        }
        .ant-input {
          height: 22px;
          padding-top: 2.6px;
        }
      }
      line-height: inherit;
      padding: 0px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      height: 28px;
    }
  }
}
.div2 {
  /deep/ .ant-form-item {
    margin: 0;
    width: 100%;
    display: flex;
    min-height: 28px;
    .tmp1 {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 370px;
      display: inline-block;
    }
    .tmp {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 140px;
      display: inline-block;
    }
    .editWrapper {
      display: flex;
      align-items: center;
      min-height: 25px;
      .ant-select {
        width: 20;
      }
      .ant-input {
        width: 120px;
      }
      .ant-input-number {
        width: 120px;
      }
    }
    .ant-form-item-label {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      color: #666;
      background-color: #f1f1f1;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      label {
        font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      }
    }
    .ant-form-item-control-wrapper {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      .ant-form-item-control {
        .ant-form-item-children {
          // display: block;
          // min-height: 25px;
          line-height: 25px;
          // // vertical-align: top;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          // overflow: hidden;
          // width: 100%;
          // display: inline-block;

          // .ant-select-allow-clear{
          //   // .ant-select-selection--multiple{
          //   //   height: 23px;
          //   //   margin-top:2px;
          //   // }
          //   .ant-select-selection__rendered{
          //     ul{
          //       display: flex;
          //       li{
          //         margin-top:-1px;
          //       }
          //     }
          //     .ant-select-selection__choice{
          //       height: 18px;
          //       margin-top: 2px;
          //       line-height: 14px;
          //       user-select: none;
          //     }
          //   }
          //   .ant-select-selection__clear{
          //     top:11px;
          //   }
          // }
          .ant-checkbox-wrapper {
            min-height: 28px;
          }
          .ant-select-selection--single {
            height: 22px;
          }
          .ant-select-selection__rendered {
            line-height: 18px;
          }
          .ant-select {
            height: 22px;
          }
          .ant-input {
            height: 22px;
            padding-top: 2.6px;
          }
        }
        line-height: inherit;
        padding: 0px 5px;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        height: auto !important;
      }
    }
  }
}
</style>
