<!-- 市场管理 - 订单管理 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend" style="position: relative" ref="SelectBox">
      <div style="width: 100%; display: flex">
        <div style="display: flex" :style="[58, 59, 77].includes(Number(this.user.factoryId)) ? 'width: 67%' : 'width: 100%'">
          <div class="leftContent" @contextmenu.prevent="rightClick($event)" style="user-select: none; float: right; width: 100%; position: relative">
            <div>
              <left-table
                :columns="columns1"
                @gettopdata="gettopdata"
                @getDetailInfo="getDetailInfo"
                @goDetail1="goDetail1"
                :data-source="orderListData"
                :orderListTableLoading="orderListTableLoading"
                :rowKey="'id'"
                @tableChange="handleTableChange"
                :pagination="pagination"
                ref="orderTable"
                class="leftstyle"
              >
              </left-table>
            </div>
            <!-- <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
                <a-menu-item >合同评审单</a-menu-item>
                <a-menu-item >返单更改单</a-menu-item>
              </a-menu>         -->
          </div>
          <!-- <a-collapse 
            @change="CollapseList"
            :expandIcon="expandIcon1"
            :default-active-key="[]"
            >
              <a-collapse-panel >
                <div class="secontent" style="width:100%;height: 775px;border-left: 2px solid rgb(233, 233, 240);border-bottom: 0;border-top: 0;margin-top: -16px;border-right:0" >
                <a-table
                  :columns="columns2"
                  :rowKey= "(text,record,index) => `${index+1}`"
                  :pagination="false" 
                  :data-source="dataSource1">
                </a-table>
                </div> 
              </a-collapse-panel>
            </a-collapse> -->
        </div>
        <div class="righttable" :style="[58, 59, 77].includes(Number(this.user.factoryId)) ? '' : 'display: none'">
          <div class="toptable">
            <a-table
              :columns="topcolumns"
              :dataSource="topdata"
              :scroll="{ y: 150 }"
              :loading="loading2"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
              :customRow="onClickRow2"
              :rowClassName="isRedRow2"
            >
              <template slot="radio" slot-scope="text, record">
                <span>
                  <a-radio :checked="record.isProduction" style="height: 26px"> </a-radio>
                </span>
              </template>
            </a-table>
          </div>
          <div class="centertable">
            <a-table
              :columns="centercolumns"
              :customRow="onClickRow3"
              :rowClassName="isRedRow3"
              :dataSource="centerdata"
              :loading="loading3"
              :scroll="{ y: 288 }"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
            ></a-table>
          </div>
          <div class="bottable">
            <a-table
              :columns="botcolumns"
              :dataSource="botdata"
              :scroll="{ y: 216 }"
              :loading="loading4"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
            ></a-table>
          </div>
        </div>
        <div class="footerAction" style="user-select: none; width: 100%; position: absolute; bottom: -46px" ref="footerAction">
          <backend-action
            :assignLoading="assignLoading"
            @quotationcontract="quotationcontract"
            @Contractarchiving="Contractarchiving"
            @queryClick="queryClick"
            @marketmodification="marketmodification"
            @auditClick="auditClick"
            @Orderoffline="Orderoffline"
            @auditClick1="auditClick1"
            @delClick="delClick"
            @orderRollback="orderRollback"
            @ordercancellation="ordercancellation"
            @Orderapproval="Orderapproval"
            @BackToWaitOffline="BackToWaitOffline"
            @ReviewSheet="ReviewSheet"
            @changeOrder="changeOrder"
            @Productionnotification="Productionnotification"
            :total="pagination.total"
          />
        </div>
      </div>
      <!-- 销售信息弹窗 -->
      <a-modal
        :title="'【' + InfoorderNo + '】' + '-- 销售信息'"
        :visible="dataVisible6"
        @cancel="reportHandleCancel"
        class="xiaoshou"
        @ok="dataVisible6 = false"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="850"
        centered
      >
        <sales-info
          ref="editForm1"
          v-if="JSON.stringify(showData) != '{}'"
          :showData="showData"
          :selectOption="selectOption"
          :type="'order_management'"
          :saveID="$refs.orderTable.selectedRowsData.id"
          :rowdata="$refs.orderTable.selectedRowsData"
        ></sales-info>
      </a-modal>
      <!--预审信息-->
      <a-modal
        title="预审信息"
        :visible="PredataVisible"
        :closable="false"
        destroyOnClose
        :maskClosable="false"
        :force-render="true"
        :mask="false"
        :getContainer="getModalContainer"
        ref="preform"
        :width="900"
        centered
        class="yushen"
      >
        <template slot="footer">
          <a-button type="primary" @click="reportHandleCancel">关闭</a-button>
        </template>
        <prequalification-info ref="editForm" v-if="JSON.stringify(showData) != '{}'" :showData="showData" :tabtype="'1'"></prequalification-info>
      </a-modal>
      <vue-draggable-resizable
        :w="width_order"
        :h="height_order"
        :x="410"
        :y="0"
        :min-width="904"
        :min-height="700"
        :max-width="904"
        :max-height="750"
        :parent="true"
        class-name="dragging1"
        @dragging="onDrag"
        @resizing="onResize"
      >
        <div id="modal-ordermanagement"></div>
      </vue-draggable-resizable>
      <!-- 龙腾销售合同 -->
      <a-modal
        title="销售合同"
        :visible="LTsalesvisible"
        @cancel="LTsalesvisible = false"
        @ok="ltsalescontractdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        centered
        class="formclass"
        :width="1250"
      >
        <report-ltsalescontract
          ref="ltsalescontract"
          :ttype="'EMS | 订单管理'"
          :LTsalesdata="LTsalesdata"
          @LTsalescontract="LTsalescontract"
          :salescustno="salescustno"
        />
      </a-modal>
      <a-modal
        title="销售合同"
        :visible="yxdVisible"
        @cancel="reportHandleCancel"
        @ok="handleOkyxd"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-infoyxd
          :ttype="'EMS | 订单管理'"
          :yxddata="yxddata"
          :joinFactoryId="joinFactoryId"
          ref="reportyxd"
          :salescustno="salescustno"
          :ContractNoSech="ContractNoSech"
          @YXDform="YXDform"
        />
      </a-modal>
      <!-- 红马销售合同-->
      <a-modal
        title="销售合同"
        :visible="hmVisible"
        @cancel="reportHandleCancel"
        @ok="handleOkhm"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-infohm
          :ttype="'EMS | 订单管理'"
          :hmsalesdata="hmsalesdata"
          :joinFactoryId="joinFactoryId"
          ref="reporthm"
          :salescustno="salescustno"
          :ContractNoSech="ContractNoSech"
          @hmform="hmform"
        />
      </a-modal>
      <a-modal
        title="销售合同"
        :visible="JZsalesvisible"
        @cancel="reportHandleCancel"
        @ok="salescontractdown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-jzsalescontract ref="jzsalescontract" :JZsalesdata="JZsalesdata" :ttype="'EMS | 订单管理'" />
      </a-modal>
      <!-- 奔强销售合同 -->
      <a-modal
        title="销售合同"
        :visible="BQsalesvisible"
        @cancel="reportHandleCancel"
        @ok="bqsalescontractdown"
        ok-text="下载"
        centered
        destroyOnClose
        :maskClosable="false"
        class="formclass1"
        :width="1250"
      >
        <report-bqsalescontract
          ref="bqsalescontract"
          :ttype="'EMS | 订单管理'"
          :BQsalesdata="BQsalesdata"
          @BQsalescontract="BQsalescontract"
          :salescustno="salescustno"
        />
      </a-modal>
      <!--奔强生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="BQnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="bqnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1250"
      >
        <report-bqnoticeform :ttype="'EMS | 订单管理'" :BQnoticedata="BQnoticedata" ref="bqnotice"></report-bqnoticeform>
      </a-modal>
      <!--联合多层生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="LHDCnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="lhdcnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1400"
      >
        <report-lhdcnoticeform :ttype="'EMS | 订单管理'" :LHDCnoticedata="LHDCnoticedata" ref="lhdcnotice"></report-lhdcnoticeform>
      </a-modal>
      <!--红马生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="HMnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="hmnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="850"
      >
        <report-hmnoticeform :ttype="'EMS | 订单管理'" :HMnoticedata="HMnoticedata" ref="hmnotice"></report-hmnoticeform>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel1"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <query-info ref="queryInfo" />
      </a-modal>
      <!-- 订单下线新调取接口失败弹窗 -->
      <a-modal
        title="预付款"
        :visible="offlinedataVisible"
        @cancel="reportHandleCancel"
        @ok="offhandleOk"
        ok-text="是"
        cancel-text="否"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="padding-left: 20px">{{ mmessage }}</span>
      </a-modal>
      <a-modal
        title="错误信息"
        :visible="offlinedataVisible1"
        @cancel="reportHandleCancel"
        @ok="offhandleOk1"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <template slot="footer">
          <a-button type="primary" @click="offhandleOk1"><span style="color: white">取消</span></a-button>
        </template>
        <div v-for="(ite, index) in cunchume" :key="index" style="white-space: pre-line">
          <span style="color: red">{{ ite.no }}</span
          >{{ ite.message }}
        </div>
        <!-- <p style="white-space: pre-line;">{{mmessage}}</p> -->
      </a-modal>
      <a-modal
        :title="popup"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="widthy"
        centered
      >
        <template slot="footer">
          <a-button @click="reportHandleCancel"><span style="color: #5f5f5f">取消</span></a-button>
          <a-button :loading="auditload" type="primary" @click="handleOk3"><span style="color: #fff">确定</span></a-button>
        </template>
        <div v-if="type1 == '5'">
          <a-form-model-item label="*取消原因" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="required">
            <a-textarea v-model="CancelCause" placeholder="请输入取消原因" :auto-focus="true" :auto-size="{ minRows: 3, maxRows: 8 }"></a-textarea>
          </a-form-model-item>
        </div>
        <span v-else-if="type1 == '3'">
          <a-form-model :model="form" ref="ruleForm">
            <!-- 33工厂下单方向单独取值 -->
            <div v-if="[58, 59].includes(Number($refs.orderTable.alldata[0].joinFactoryId))">确认订单下线吗？</div>
            <a-row v-if="![33, 58, 59].includes(Number($refs.orderTable.alldata[0].joinFactoryId))">
              <a-col :span="24">
                <a-form-model-item
                  label="下单方向："
                  :label-col="{ span: 6 }"
                  :wrapper-col="{ span: 15 }"
                  style="width: 100%"
                  ref="orderDirection"
                  prop="orderDirection"
                  class="require"
                >
                  <a-select v-model="form.orderDirection" showSearch allowClear optionFilterProp="lable" @change="changeorderd()">
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.OrderDirection)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row v-if="$refs.orderTable.alldata[0].joinFactoryId == 33">
              <a-col :span="24">
                <a-form-model-item
                  label="下单方向："
                  :label-col="{ span: 6 }"
                  :wrapper-col="{ span: 15 }"
                  style="width: 100%"
                  ref="orderDirection"
                  prop="orderDirection"
                  class="require"
                >
                  <a-select v-model="form.orderDirection" showSearch allowClear optionFilterProp="lable">
                    <a-select-option v-for="(item, index) in factoryData" :key="index" :value="item.valueMember" :lable="item.text">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row v-if="$refs.orderTable.alldata[0].joinFactoryId == 57">
              <a-col :span="24">
                <a-form-model-item
                  label="采购价格"
                  :label-col="{ span: 6 }"
                  :wrapper-col="{ span: 15 }"
                  style="width: 100%"
                  ref="purchasePrice"
                  prop="purchasePrice"
                  :class="showcg ? 'require' : ''"
                >
                  <a-input v-model="form.purchasePrice"></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </span>
        <span v-else-if="type1 == 'xd'" class="xd">
          <a-table
            :columns="xdcolumns"
            :dataSource="xddataSource"
            :scroll="{ y: 360 }"
            :pagination="false"
            :rowKey="(record, index) => `${index + 1}`"
            :customRow="onClickRow"
            :rowClassName="isRedRow"
          >
            <template slot="purchasePrice" slot-scope="text, record">
              <span style="color: #428bca">{{ record.purchasePrice }}</span>
            </template>
            <template slot="factoryCode" slot-scope="text, record">
              <span
                >{{ record.factoryCode }}
                <a-tooltip v-if="record.ruleDetail != '' && record.ruleDetail" :title="record.ruleDetail">
                  <a-icon style="color: #ff9900" type="copy"></a-icon>
                </a-tooltip>
              </span>
            </template>
          </a-table>
        </span>
        <span v-else-if="type1 == 'approval'" class="Select">
          <a-form-model-item label="下单方向：" :label-col="{ span: 6 }" :wrapper-col="{ span: 15 }" style="width: 100%" class="require">
            <a-select v-model="Approvetheorder" showSearch allowClear optionFilterProp="lable">
              <a-select-option v-for="(item, index) in mapKey(selectOption.OrderDirection)" :key="index" :value="item.value" :lable="item.lable">
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </span>
        <div v-else>
          <span style="font-size: 16px" v-if="orderno">【{{ orderno }}】</span>
          <span style="font-size: 16px; margin-left: 15px">{{ message }}</span>
        </div>
      </a-modal>
      <a-modal
        title="市场修改"
        :visible="marketdataVisible"
        @cancel="reportHandleCancel"
        @ok="markethandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="地址" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.address" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="价格" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.price" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="交期" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.deliverydate" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.num" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="PO" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
              <a-checkbox v-model="formdata.PO" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-modal>
      <a-modal title="检查信息" :visible="dataVisible2" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="600" centered>
        <template #footer>
          <a-button key="back" @click="reportHandleCancel">取消</a-button>
          <a-button v-if="check" type="primary" style="color: #fff !important" @click="ok11">确定</a-button>
        </template>
        <div class="class" style="font-size: 16px">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
      <a-modal
        title="提示信息"
        :visible="dataVisibleOrder"
        @cancel="reportHandleCancel"
        @ok="handleOrder"
        ok-text="继续"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div v-for="(ite, index) in cunchume" :key="index" style="font-size: 16px; color: CornflowerBlue">
          {{ ite }}
        </div>
        <!-- <span style="font-size:16px;margin-left:15px;color:CornflowerBlue;">{{mmessage1}}</span> -->
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import SalesInfo from "@/pages/mkt/OrderOffer/module/SalesInfo";
import VueDraggableResizable from "vue-draggable-resizable";
import ReportLtsalescontract from "@/pages/mkt/OrderOffer/report/ReportLtsalescontract";
import ReportJzsalescontract from "@/pages/mkt/OrderOffer/report/ReportJzsalescontract";
import ReportBqsalescontract from "@/pages/mkt/OrderOffer/report/ReportBqsalescontract";
import ReportBqnoticeform from "@/pages/mkt/OrderOffer/report/ReportBqnoticeform";
import ReportLhdcnoticeform from "@/pages/mkt/OrderOffer/report/ReportLhdcnoticeform";
import ReportHmnoticeform from "@/pages/mkt/OrderOffer/report/ReportHmnoticeform";
import ReportInfohm from "@/pages/mkt/OrderOffer/report/ReportInfohm";
import ReportInfoyxd from "@/pages/mkt/OrderOffer/report/ReportInfoyxd";
import {
  frontcontractreport,
  productionreport,
  quotationmodel,
  yxDOrderpriceEXLE,
  timedautomaticsendorder,
  result4ParameterList,
  resultList,
  resultDetailList,
  getcontractinfobygrp,
  contractNo,
} from "@/services/mkt/OrderReview.js";
import $ from "jquery";
import PrequalificationInfo from "@/pages/mkt/OrderOffer/module/PrequalificationInfo";
import { factoryList } from "@/services/scgl/OrderManagement/Composition";
import { mktConfig, selectpars, getEditOrderInfo, setordermodifylist } from "@/services/mkt/orderInfo";
import { mapMutations } from "vuex";
import { checkPermission } from "@/utils/abp";
import Cookie from "js-cookie";
import LeftTable from "@/pages/mkt/OrderManagement/LeftTable";
import BackendAction from "@/pages/mkt/OrderManagement/BackendAction";
import QueryInfo from "@/pages/mkt/OrderManagement/QueryInfo.vue";
import { indicationCheck } from "@/services/mkt/OrderReview.js";
import { mapState } from "vuex";
import moment from "moment";
import {
  orderManegePageList,
  offlineOrder,
  paymodecheck,
  orderDelete,
  back2Waitoffline,
  orderverifyyXD,
  backtoverify,
  ordercancel,
  pcbordermanagetotal,
  buttonCheck,
  offlineorders,
  purchasepricelist,
  offlineorderyXD,
  offlineordertoppeyXD,
} from "@/services/mkt/OrderManagement.js";
import { contractReviewInfo, getnOPEAlterreport, noticereviewinfo } from "@/services/mkt/OrderReview.js";
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "总计",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "totalCount",
  },
  {
    title: "停留",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "stayCount",
  },
  {
    title: "询价",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "enquiryCount",
  },
  {
    title: "待派",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "waitPrecheckCount",
  },
  {
    title: "预审",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "preCheckCount",
  },
  {
    title: "报价",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "verifyCount",
  },
  {
    title: "待下线",
    align: "left",
    ellipsis: true,
    width: 50,
    dataIndex: "waitToProCount",
  },
  {
    title: "下单",
    align: "left",
    ellipsis: true,
    width: 40,
    dataIndex: "toProCount",
  },
];
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 40,
  },
  {
    title: "订单号",
    align: "left",
    ellipsis: true,
    width: 120,
    scopedSlots: { customRender: "orderNo" },
    dataIndex: "orderNo",
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 80,
    scopedSlots: { customRender: "custNo" },
  },
  {
    title: "订单类型",
    dataIndex: "reOrder",
    align: "left",
    ellipsis: true,
    width: 75,
    customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
  },
  {
    title: "状态",
    dataIndex: "status",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "预付款",
    dataIndex: "advancePayment",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "生产型号",
    align: "left",
    scopedSlots: { customRender: "proOrderNo" },
    ellipsis: true,
    width: 150,
  },
  {
    title: "版本",
    dataIndex: "proRevStr",
    align: "left",
    ellipsis: true,
    width: 45,
  },
  {
    title: "客户型号",
    dataIndex: "customerModel",
    align: "left",
    ellipsis: true,
    width: 280,
  },
  {
    title: "客户物料号",
    dataIndex: "customerMaterialNo",
    align: "left",
    ellipsis: true,
    width: 150,
  },
  {
    title: "客户订单号",
    width: 200,
    align: "left",
    dataIndex: "custPo",
    ellipsis: true,
  },
  {
    title: "数量",
    dataIndex: "para4DelQty",
    width: 50,
    ellipsis: true,
    align: "left",
  },
  {
    title: "面积",
    dataIndex: "para4Area",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "合同总金额",
    dataIndex: "totalAmountPrice",
    width: 85,
    ellipsis: true,
    align: "left",
  },
  {
    title: "交货日期",
    dataIndex: "deliveryDate",
    align: "left",
    ellipsis: true,
    width: 95,
  },
  {
    title: "交期天数",
    dataIndex: "para4IntDelivery",
    align: "left",
    ellipsis: true,
    width: 72,
  },
  {
    title: "下单日期",
    dataIndex: "checkEndTime",
    align: "left",
    ellipsis: true,
    width: 148,
  },

  {
    title: "报价人",
    dataIndex: "checkName",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  // {
  //   title: "当前工序",
  //   dataIndex: "",
  //   align: "left",
  //   ellipsis: true,
  //   width: 85,
  // },
  // {
  //   title: "过序时间",
  //   dataIndex: "",
  //   align: "left",
  //   ellipsis: true,
  //   width: 160,
  // },
  // {
  //   title: "操作人",
  //   dataIndex: "",
  //   align: "left",
  //   ellipsis: true,
  //   width: 65,
  // },
  {
    title: "层数",
    dataIndex: "boardLayers",
    width: 45,
    ellipsis: true,
    align: "left",
  },
  {
    title: "工厂/外发",
    dataIndex: "factoryName2",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加工工厂",
    dataIndex: "orderDirectionStr",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "新客户",
    dataIndex: "isNewCust",
    width: 50,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "isNewCust" },
  },
  {
    title: "加急",
    customRender: (text, record, index) => `${record.isJiaji ? "是" : "否"}`,
    width: 40,
    ellipsis: true,
    align: "center",
  },
  {
    title: "军工",
    dataIndex: "isWarProc",
    width: 40,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "isWarProc" },
  },
  {
    title: "备注",
    dataIndex: "customerRemarks",
    width: 270,
    ellipsis: true,
    align: "left",
  },
  // {
  //   title: "预审确认",
  //   dataIndex: "noteSure",
  //   width: 300,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "客户回复",
  //   dataIndex: "customerResponse",
  //   width: 300,
  //   ellipsis: true,
  //   align: "left",
  // },
  {
    title: "确认/回复",
    width: 80,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "Action" },
  },
];
const xdcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "工厂",
    dataIndex: "factoryCode",
    width: 50,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "factoryCode" },
  },
  {
    title: "下单",
    customRender: (text, record, index) => (record.rulePass ? "是" : ""),
    width: 40,
    ellipsis: true,
    align: "center",
  },
  {
    title: "采购价格",
    dataIndex: "purchasePrice",
    width: 55,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "purchasePrice" },
  },
  {
    title: "已下单面积",
    dataIndex: "offarea",
    width: 55,
    ellipsis: true,
    align: "left",
  },
  {
    title: "指标(饱和度)",
    children: [
      {
        title: "交期",
        dataIndex: "number",
        width: 85,
        ellipsis: true,
        align: "left",
      },
      {
        title: "面积",
        dataIndex: "area",
        align: "left",
        ellipsis: true,
        width: 95,
      },
      {
        title: "加急",
        dataIndex: "urgentNum",
        align: "left",
        ellipsis: true,
        width: 95,
      },
    ],
  },
];
const topcolumns = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    key: "index",
    align: "center",
    width: 37,
  },
  {
    title: "下单",
    scopedSlots: { customRender: "radio" },
    width: 37,
    ellipsis: true,
    align: "center",
    className: "inputClass",
  },
  {
    title: "数量",
    dataIndex: "para4DelQty_",
    width: 50,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "提前",
    dataIndex: "para4UrgentDate_",
    width: 40,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "面积",
    dataIndex: "para4Area_",
    ellipsis: true,
    width: 55,
    align: "left",
  },
  {
    title: "交期",
    ellipsis: true,
    dataIndex: "para4IntDelivery_",
    width: 37,
    align: "left",
  },
  {
    title: "交货日期",
    dataIndex: "para4Delivery_",
    width: 110,
    className: "inputClass",
    ellipsis: true,
    align: "left",
  },
  {
    title: "单价",
    dataIndex: "pcsPrice_",
    width: 55,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "重量(KG)",
    dataIndex: "para4Weight_",
    width: 80,
    align: "left",
  },
];
const centercolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    dataIndex: "upOrDownPrice",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
  },
  {
    title: "实际报价",
    width: 58,
    ellipsis: true,
    align: "left",
    dataIndex: "actualPrice",
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
const botcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    dataIndex: "upOrDownPrice",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
  },
  {
    title: "实际报价",
    width: 58,
    ellipsis: true,
    align: "left",
    dataIndex: "actualPrice",
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
export default {
  name: "orderInquiry",
  components: {
    VueDraggableResizable,
    ReportJzsalescontract,
    LeftTable,
    PrequalificationInfo,
    BackendAction,
    QueryInfo,
    ReportInfoyxd,
    ReportBqsalescontract,
    ReportBqnoticeform,
    ReportInfohm,
    ReportHmnoticeform,
    ReportLhdcnoticeform,
    ReportLtsalescontract,
    SalesInfo,
  },
  inject: ["reload"],
  data() {
    return {
      guid4Parameter: "",
      id: "",
      width_order: 904,
      height_order: 750,
      x_order: 0,
      y_order: 0,
      Approvetheorder: "",
      yxddata: {},
      allids: [],
      count: 0,
      modeltype: "",
      CancelCause: "",
      hmVisible: false,
      hmsalesdata: {},
      salescustno: "",
      ContractNoSech: "",
      joinFactoryId: "",
      yxdVisible: false,
      LTsalesvisible: false,
      yxdids: [],
      LTsalesdata: {},
      datasource: [],
      modelvisi: "",
      foldedornot: false,
      xddataSource: [],
      xdcolumns,
      factoryData: [],
      cunchume: [],
      formdata: {
        address: false,
        price: false,
        deliverydate: false,
        num: false,
        PO: false,
      },
      confirmLoading: false,
      spinning: false,
      popup: "",
      JZsalesvisible: false,
      BQsalesdata: {},
      BQsalesvisible: false,
      BQnoticeVisible: false,
      LHDCnoticeVisible: false,
      HMnoticeVisible: false,
      HMnoticedata: {},
      BQnoticedata: {},
      LHDCnoticedata: {},
      JZsalesdata: {},
      auditload: false,
      form: {
        orderDirection: "",
        purchasePrice: "",
      },
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      columns1,
      columns2,
      PredataVisible: false,
      dataVisible6: false,
      isMovedown: false,
      InfoorderNo: "",
      startPosition: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      showData: {},
      topcolumns,
      centercolumns,
      botcolumns,
      topdata: [],
      centerdata: [],
      botdata: [],
      loading2: false,
      loading3: false,
      loading4: false,
      orderListData: [],
      dataSource1: [],
      mmessage: "",
      dataVisible: false,
      offlinedataVisible: false,
      offlinedataVisible1: false,
      assignLoading: false,
      orderListTableLoading: false,
      type: "1",
      selectedData: {},
      orderNo: "",
      dataVisible3: false,
      message: "",
      orderno: "",
      type1: "",
      widthy: "",
      deletId: "",
      menuVisible: false,
      showcg: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      dataVisible2: false,
      marketdataVisible: false,
      checkData: [],
      check: false,
      queryData: {},
      selectOption: {},
      mmessage1: "",
      orderDirection: "",
      dataVisibleOrder: false,
      isCtrlPressed: false,
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.getOrderList();
      this.handleResize();
    });
  },
  computed: {
    ...mapState("account", ["user", "buryingpoint"]),
  },
  beforeRouteLeave: function (to, from, next) {
    next();
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize);
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
    this.handleResize();
    factoryList().then(res => {
      if (res.code) {
        this.factoryData = res.data;
      }
    });
    if ([58, 59, 77].includes(Number(this.user.factoryId)) && this.columns1[this.columns1.length - 1].title != "参数信息") {
      this.columns1.push({
        title: "参数信息",
        fixed: "right",
        width: 100,
        ellipsis: true,
        align: "center",
        scopedSlots: { customRender: "sales" },
      });
    } else if (this.user.factoryId != 58 && this.user.factoryId != 59 && this.columns1[this.columns1.length - 1].title == "参数信息") {
      this.columns1.splice(this.columns1.length - 1, 1);
    }
  },
  methods: {
    checkPermission,
    getModalContainer() {
      return document.querySelector("#modal-ordermanagement"); // 返回弹窗容器的选择器或 HTMLElement 对象
    },
    onResize: function (x, y, width, height) {
      this.width_order = width;
      this.height_order = height;
      if (width == 700) {
        document.getElementsByClassName("dragging1")[0].style.width = "904px";
        document.getElementsByClassName("dragging1")[0].style.height = "750px";
      }
    },
    onDrag: function (x, y) {
      document.getElementsByClassName("dragging1")[0].style.left = x;
      this.x = x;
      this.y = y;
    },
    handleOkyxd() {
      if (this.joinFactoryId == 58 || this.joinFactoryId == 59) {
        if (this.yxdids && this.yxdids.length == 0) {
          this.yxdVisible = false;
        } else {
          quotationmodel(this.yxdids[0]).then(res => {
            if (res.code) {
              if (res.data == null || res.data == "") {
                this.$refs.reportyxd.getReportPdf();
              } else {
                let params = {};
                params.type = res.data;
                params.Id = this.yxdids;
                yxDOrderpriceEXLE(params).then(res => {
                  if (res.code) {
                    this.downloadByteArrayFromString(res.data, res.message);
                  } else {
                    this.$message.error(res.message);
                  }
                });
              }
            }
          });
        }
      } else {
        this.$refs.reportyxd.getReportPdf();
      }
    },
    ...mapMutations("account", ["Buriedpointcache"]),
    isRedRow(record) {
      let strGroup = [];
      if (record.id && record.id == this.datasource.id) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.datasource = record;
          },
        },
      };
    },
    changeorderd() {
      if (this.form.orderDirection == "外发") {
        this.showcg = true;
      } else {
        this.showcg = false;
      }
    },
    handleResize() {
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 134 + "px";
      } else {
        leftContent.style.height = "778px";
      }
      if (leftstyle && this.orderListData.length != 0) {
        leftstyle.style.height = window.innerHeight - 174 + "px";
      } else {
        leftstyle.style.height = 0;
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.footerAction ? this.$refs.footerAction.nums * 104 : 0;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }

        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.reportHandleCancel();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "88" && this.isCtrlPressed) {
        this.reportHandleCancel();
        this.reportHandleCancel1();
        this.auditClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "72" && this.isCtrlPressed && checkPermission("MES.MarketModule.OrderManage.SalesContract")) {
        this.quotationcontract();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible3) {
        this.handleOk3();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
      // else if(e.keyCode == '13'){
      //   e.preventDefault()
      // }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    expandIcon1() {
      if (this.foldedornot) {
        return (
          <a>
            <a-icon type="right" style="margin-right:5px" />
          </a>
        );
      } else {
        return (
          <a>
            <a-icon type="left" style="margin-right:5px" />
          </a>
        );
      }
    },
    CollapseList(val) {
      const elements1 = document.getElementsByClassName("leftContent");
      const elements2 = document.getElementsByClassName("secontent");
      if (val.length) {
        this.foldedornot = true;
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.width = "71.9%";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.display = "";
        }
      } else {
        this.foldedornot = false;
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.width = "99%";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.display = "none";
        }
      }
    },
    //市场修改点击确定
    markethandleOk() {
      let arr = [];
      if (!this.formdata.PO && !this.formdata.address && !this.formdata.deliverydate && !this.formdata.num && !this.formdata.price) {
        this.$message.warning("请至少勾选一项再点击确定");
        return;
      }
      if (this.formdata.PO == true) {
        arr.push("PO");
      }
      if (this.formdata.address == true) {
        arr.push("地址");
      }
      if (this.formdata.deliverydate == true) {
        arr.push("交期");
      }
      if (this.formdata.num == true) {
        arr.push("数量");
      }
      if (this.formdata.price == true) {
        arr.push("价格");
      }
      let content = arr.join(";");
      let parmas = {
        content: content,
        orderModifyType: 1,
        orderNo: this.$refs.orderTable.selectedRows.orderNo,
        proOrderNo: this.$refs.orderTable.selectedRows.proOrderNo,
      };
      setordermodifylist(parmas).then(res => {
        if (res.code) {
          this.$message.success("市场修改成功");
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
      this.marketdataVisible = false;
      this.formdata = {
        address: false,
        price: false,
        deliverydate: false,
        num: false,
        PO: false,
      };
    },
    // 获取订单
    getOrderList(queryData) {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      var obj = Object.assign(params, queryData);
      obj.PcbFileName = obj.PcbFileName ? obj.PcbFileName.replace(/\s+/g, " ").trim() : "";
      this.orderListTableLoading = true;
      orderManegePageList(obj)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            if (
              (obj.custNo ||
                obj.OrderNo ||
                obj.PcbFileName ||
                obj.proOrderNo ||
                obj.Status ||
                obj.contractNo ||
                obj.StartTime ||
                obj.EndTime ||
                obj.custPo ||
                obj.ReOrder ||
                obj.createName ||
                obj.customerMaterialNo) &&
              this.orderListData.length
            ) {
              this.$refs.orderTable.selectedRowList = [this.orderListData[0].id];
              this.$refs.orderTable.selectedRows.orderNo = this.orderListData[0].orderNo;
              this.$refs.orderTable.selectedRowsData.joinFactoryId = this.orderListData[0].joinFactoryId;
              this.$refs.orderTable.alldata = [this.orderListData[0]];
              this.$refs.orderTable.selectedRowsData = this.orderListData[0];
              this.gettopdata(this.orderListData[0].id);
            }
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
            this.orderListData.forEach(item => {
              if (item.isReOrder == 0) {
                item.isReOrder = false;
              } else {
                item.isReOrder = true;
              }
            });
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },

    // 销售信息弹窗
    goDetail1(record) {
      this.spinning = true;
      this.InfoorderNo = record.orderNo;
      this.$refs.orderTable.selectedRowKeysArray = [];
      this.$refs.orderTable.selectedRowKeysArray.push(record.id);
      if (record.id) {
        this.getData(record.joinFactoryId);
        getEditOrderInfo(record.id)
          .then(res => {
            if (res.code) {
              this.showData = res.data;
              this.dataVisible6 = true;
              setTimeout(() => {
                $(".ant-modal-header").on("mousedown", e => {
                  console.log("mousedown");
                  this.startPosition.x = e.pageX;
                  this.startPosition.y = e.pageY;
                  this.startPosition.offsetX = e.offsetX;
                  this.startPosition.offsetY = e.offsetY;
                  this.isMovedown = true;
                  let a = document.getElementsByClassName("xiaoshou")[0];
                  a.style.webkitUserSelect = "none"; // Chrome、Safari 和 Opera
                  a.style.mozUserSelect = "none"; // 火狐
                  a.style.msUserSelect = "none"; // IE 和 Edge
                  a.style.userSelect = "none"; // 标准写法
                });
              }, 200);
              document.body.addEventListener("mousemove", e => {
                if (this.isMovedown) {
                  if (
                    e.x - this.startPosition.x > 10 ||
                    e.y - this.startPosition.y > 10 ||
                    e.x - this.startPosition.x < -10 ||
                    e.y - this.startPosition.y < -10
                  ) {
                    let w = $(".ant-modal-content").width();
                    let h = $(".ant-modal-content").height();
                    $(".ant-modal-content").css({
                      left: e.pageX - this.startPosition.offsetX - (document.body.clientWidth - w) / 2 + "px",
                      // top: e.pageY -(document.body.clientHeight-3*h)/2 - 750 + 'px'
                    });
                  }
                }
              });
              document.body.addEventListener("mouseup", e => {
                this.isMovedown = false;
                let a = document.getElementsByClassName("xiaoshou")[0];
                a.style.webkitUserSelect = "text"; // Chrome、Safari 和 Opera
                a.style.mozUserSelect = "text"; // 火狐
                a.style.msUserSelect = "text"; // IE 和 Edge
                a.style.userSelect = "text"; // 标准写法
              });
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    // 获取预审订单详情
    getDetailInfo(id) {
      this.spinning = true;
      document.getElementsByClassName("dragging1")[0].style.display = "block";
      getEditOrderInfo(id)
        .then(res => {
          if (res.code) {
            this.showData = res.data;
            this.PredataVisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          var arr = document.getElementsByClassName("handle");
          for (var i = 0; i < arr.length; i++) {
            arr[i].style.display = "block";
          }
        });
    },
    //获取多套价格
    gettopdata(id) {
      if (![58, 59, 77].includes(Number(this.user.factoryId))) {
        return;
      }
      if (id == undefined) {
        this.topdata = [];
        this.centerdata = [];
        this.botdata = [];
        return;
      }
      this.loading2 = true;
      result4ParameterList(id)
        .then(res => {
          if (res.data.length > 1) {
            this.topdata = res.data.filter(item => item.isProduction == true);
            var index = this.topdata.findIndex(v => v.isProduction == true);
            for (var b = 0; b < this.topdata.length; b++) {
              if (this.topdata[b].para4Weight_) {
                this.topdata[b].para4Weight_ = Number(this.topdata[b].para4Weight_).toFixed(2);
              }
              if (this.topdata[b].para4Delivery_) {
                this.topdata[b].para4Delivery_ = moment(moment(this.topdata[b].para4Delivery_).format("YYYY-MM-DD"));
              } else {
                this.topdata[b].para4Delivery_ = null;
              }
            }
            this.getcenterdata(this.topdata[index]);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
    },
    // 获取价格结果列表
    getcenterdata(record) {
      this.loading3 = true;
      resultList(record.id)
        .then(res => {
          if (res.data) {
            this.centerdata = res.data;
            let index = res.data.findIndex(v => v.priceName.indexOf("平米价") != -1);
            if (this.centerdata.length > 0 && index != -1) {
              this.guid4Parameter = this.centerdata[index].guid4Parameter + this.centerdata[index].guid4Order + this.centerdata[index].guid4Calc;
              this.getbotdata(this.centerdata[index]);
            } else {
              this.botdata = [];
            }
          }
        })
        .finally(() => {
          this.loading3 = false;
        });
    },
    // 获取价格明细列表
    getbotdata(record) {
      this.loading4 = true;
      if (record.calcNameID == "1013501" || record.calcNameID == "1013510") {
        resultDetailList(record.guid4Parameter, record.calcNameID)
          .then(res => {
            if (res.data) {
              this.botdata = res.data;
            }
          })
          .finally(() => {
            this.loading4 = false;
          });
      } else {
        this.botdata = [];
        this.loading4 = false;
      }
    },
    onClickRow2(record) {
      return {
        on: {
          click: () => {
            this.id = record.id;
            this.getcenterdata(record);
          },
        },
      };
    },
    onClickRow3(record) {
      return {
        on: {
          click: () => {
            this.guid4Parameter = record.guid4Parameter + record.guid4Order + record.guid4Calc;
            this.getbotdata(record);
          },
        },
      };
    },
    // 行点击事件
    isRedRow2(record) {
      if (record.id == this.id) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    isRedRow3(record) {
      if (record.guid4Parameter + record.guid4Order + record.guid4Calc == this.guid4Parameter) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    // 订单表变化change
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      this.pageStat = false;
      localStorage.removeItem("stat");
      if (JSON.stringify(this.queryData) != "{}") {
        this.getOrderList(this.queryData);
      } else {
        this.getOrderList();
      }
    },
    reportHandleCancel1() {
      this.dataVisible = false; // 查询
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.spinning = false;
      this.dataVisible6 = false;
      this.PredataVisible = false;
      this.hmVisible = false;
      this.JZsalesvisible = false;
      this.BQsalesvisible = false;
      this.BQnoticeVisible = false;
      this.LHDCnoticeVisible = false;
      this.HMnoticeVisible = false;
      this.offlinedataVisible = false;
      this.yxdVisible = false;
      this.offlinedataVisible1 = false;
      this.dataVisible3 = false;
      this.dataVisible2 = false;
      this.marketdataVisible = false;
      this.dataVisibleOrder = false;
      this.cunchume = [];
      document.getElementsByClassName("dragging1")[0].style.display = "none";
      // if(this.modeltype == 'yxd'){
      //   this.Offlineinspection(this.count+1)
      //   this.$refs.orderTable.selectedRowList=[]
      // }
      if (this.$refs.EnterOrderInfo) {
        if (this.type == "1") {
          this.$refs.EnterOrderInfo.enterOrderForm.custNo = "";
          this.$refs.EnterOrderInfo.enterOrderForm.num = "";
          this.$refs.EnterOrderInfo.enterOrderForm.delType = "";
          this.$refs.EnterOrderInfo.enterOrderForm.custPo = "";
          this.$refs.EnterOrderInfo.enterOrderForm.orderSource = "";
          this.$refs.EnterOrderInfo.enterOrderForm.pcbFileName = "";
          this.$refs.EnterOrderInfo.enterOrderForm.proOrderNo = "";
          this.$refs.EnterOrderInfo.enterOrderForm.isJiaji = false;
          this.$refs.EnterOrderInfo.enterOrderForm.PcbFilePath = "";
        } else {
          this.$refs.EnterOrderInfo.enterOrderForm.custNo = this.$refs.orderTable.selectedRowsData.custNo;
          this.$refs.EnterOrderInfo.enterOrderForm.num = this.$refs.orderTable.selectedRowsData.num;
          this.$refs.EnterOrderInfo.enterOrderForm.delType = this.$refs.orderTable.selectedRowsData.delType;
          this.$refs.EnterOrderInfo.enterOrderForm.custPo = this.$refs.orderTable.selectedRowsData.custPo;
          this.$refs.EnterOrderInfo.enterOrderForm.orderSource = this.$refs.orderTable.selectedRowsData.orderSource;
          this.$refs.EnterOrderInfo.enterOrderForm.pcbFileName = this.$refs.orderTable.selectedRowsData.pcbFileName;
          this.$refs.EnterOrderInfo.enterOrderForm.proOrderNo = this.$refs.orderTable.selectedRowsData.proOrderNo;
          this.$refs.EnterOrderInfo.enterOrderForm.isJiaji = this.$refs.orderTable.selectedRowsData.isJiaji;
          this.$refs.EnterOrderInfo.enterOrderForm.PcbFilePath = this.$refs.orderTable.selectedRowsData.PcbFilePath;
        }
      }
    },
    // 查询
    queryClick() {
      this.dataVisible = true;
    },
    handleOk() {
      this.$refs.orderTable.selectedRowList = [];
      let params = this.$refs.queryInfo.form;
      var arr1 = params.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      params.OrderNo = arr1.join("");
      var arr2 = params.custNo.split("");
      if (arr2.length > 10) {
        arr2 = arr2.slice(0, 10);
      }
      params.custNo = arr2.join("");
      var arr3 = params.PcbFileName.split("");
      if (arr3.length > 100) {
        arr3 = arr3.slice(0, 100);
      }
      params.PcbFileName = arr3.join("");
      var arr4 = params.proOrderNo.split("");
      if (arr4.length > 20) {
        arr4 = arr4.slice(0, 20);
      }
      params.proOrderNo = arr4.join("");
      var arr5 = params.custPo.split("");
      if (arr5.length > 30) {
        arr5 = arr5.slice(0, 30);
      }
      params.custPo = arr5.join("");
      (params.Status = params.Status ? Number(params.Status) : null), (this.pagination.current = 1);
      this.queryData = params;
      if (this.queryData.proOrderNo && typeof this.queryData.proOrderNo === "string" && this.queryData.proOrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产型号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单管理',usingFeatures:'订单查询',})
      this.Buriedpointcache(this.buryingpoint);
      this.getOrderList(params);
    },
    getData() {
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("PreSelectPar"));
      const factory = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.selectOption = data[a].data; //本地缓存
            let arr = this.mapKey(this.selectOption.OrderDirection);
            if (arr.length && this.$refs.orderTable.alldata[0].joinFactoryId != 33) {
              if (this.$refs.orderTable.alldata[0].joinFactoryId == 37 || this.$refs.orderTable.alldata[0].joinFactoryId == 57) {
                this.form.orderDirection = arr[0].value;
                this.Approvetheorder = arr[0].value;
              } else if (this.$refs.orderTable.alldata[0].joinFactoryId == 58 || this.$refs.orderTable.alldata[0].joinFactoryId == 59) {
                this.Approvetheorder = this.$refs.orderTable.selectedRowsData.orderDirection;
              } else {
                if (this.$refs.orderTable.selectedRowsData.orderDirection) {
                  this.form.orderDirection = this.$refs.orderTable.selectedRowsData.orderDirection;
                  this.Approvetheorder = this.$refs.orderTable.selectedRowsData.orderDirection;
                } else {
                  this.form.orderDirection = arr[0].value;
                  this.Approvetheorder = arr[0].value;
                }
              }
            }
            if (this.form.orderDirection == "外发") {
              this.showcg = true;
            } else {
              this.showcg = false;
            }
          }
        }
      } else {
        selectpars(1, factory).then(res => {
          if (res.code) {
            this.selectOption = res.data;
            let arr1 = this.mapKey(this.selectOption.OrderDirection);
            if (arr1.length && this.$refs.orderTable.alldata[0].joinFactoryId != 33) {
              if (this.$refs.orderTable.alldata[0].joinFactoryId == 37 || this.$refs.orderTable.alldata[0].joinFactoryId == 57) {
                this.form.orderDirection = arr1[0].value;
                this.Approvetheorder = arr1[0].value;
              } else if (this.$refs.orderTable.alldata[0].joinFactoryId == 58 || this.$refs.orderTable.alldata[0].joinFactoryId == 59) {
                this.Approvetheorder = this.$refs.orderTable.selectedRowsData.orderDirection;
              } else {
                if (this.$refs.orderTable.selectedRowsData.orderDirection) {
                  this.form.orderDirection = this.$refs.orderTable.selectedRowsData.orderDirection;
                  this.Approvetheorder = this.$refs.orderTable.selectedRowsData.orderDirection;
                } else {
                  this.form.orderDirection = arr1[0].value;
                  this.Approvetheorder = arr1[0].value;
                }
              }
            }
            if (this.form.orderDirection == "外发") {
              this.showcg = true;
            } else {
              this.showcg = false;
            }
            let token = Cookie.get("Authorization");
            let arr = [];
            if (data == null) {
              arr.push({ data: this.selectOption, token, factory });
              localStorage.setItem("PreSelectPar", JSON.stringify(arr)); //本地缓存
            } else {
              data.push({ data: this.selectOption, token, factory });
              localStorage.setItem("PreSelectPar", JSON.stringify(data)); //本地缓存
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
      console.log(this.Approvetheorder, "1111");
    },
    salescontractdown() {
      this.$refs.jzsalescontract.getsalesPdf();
    },
    ltsalescontractdown() {
      this.$refs.ltsalescontract.getsalesPdf();
    },
    bqsalescontractdown() {
      getcontractinfobygrp(this.$refs.bqsalescontract.ids).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
      //this.$refs.bqsalescontract.getsalesPdf()
    },
    bqnoticedown() {
      this.$refs.bqnotice.getnoticePdf();
    },
    hmnoticedown() {
      this.$refs.hmnotice.getnoticePdf();
    },
    lhdcnoticedown() {
      this.$refs.lhdcnotice.getnoticePdf();
    },
    quotationcontract() {
      if (this.$refs.orderTable.selectedRowList == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      this.joinFactoryId = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      this.Buriedpointcache(this.buryingpoint);
      contractNo(this.$refs.orderTable.selectedRowList).then(res => {
        if (res.code) {
          if (this.joinFactoryId == "58" || this.joinFactoryId == "59" || this.joinFactoryId == "65" || this.joinFactoryId == "38") {
            this.YXDform();
          } else if (this.joinFactoryId == "22") {
            this.JZsalescontract();
          } else if (this.joinFactoryId == "37") {
            this.hmform();
          } else if (this.joinFactoryId == "12") {
            this.BQsalescontract();
          } else if (this.joinFactoryId == "67") {
            this.LTsalescontract();
          } else {
            getcontractinfobygrp(this.$refs.orderTable.selectedRowList).then(res => {
              if (res.code) {
                this.downloadByteArrayFromString(res.data, res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Contractarchiving() {
      if (this.$refs.orderTable.selectedRowList == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      this.joinFactoryId = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      contractNo(this.$refs.orderTable.selectedRowList).then(res => {
        if (res.code) {
          getcontractinfobygrp(this.$refs.orderTable.selectedRowList).then(res => {
            if (res.code) {
              this.downloadByteArrayFromString(res.data, res.message);
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    JZsalescontract() {
      //精焯销售合同
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      frontcontractreport(this.$refs.orderTable.selectedRowList).then(res => {
        if (res.code) {
          this.JZsalesdata = res.data;
          this.JZsalesvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    BQsalescontract(ids) {
      //奔强销售合同
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowList;
      frontcontractreport(id).then(res => {
        if (res.code) {
          this.salescustno = this.orderListData.filter(ite => ite.id == this.$refs.orderTable.selectedRowList[0])[0].custNo;
          this.ContractNoSech = 2;
          this.BQsalesdata = res.data;
          this.BQsalesvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //BQ生产通知单预览
    BQnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowList[0], 1).then(res => {
        if (res.code) {
          this.BQnoticedata = res.data;
          this.BQnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //联合多层生产通知单预览
    LHDCnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowList[0], 1).then(res => {
        if (res.code) {
          this.LHDCnoticedata = res.data;
          this.LHDCnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //红马生产通知单预览
    HMnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowList[0], 1).then(res => {
        if (res.code) {
          this.HMnoticedata = res.data;
          this.HMnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    LTsalescontract(ids, type, factoryId, custno) {
      //龙腾销售合同
      let id = ids ? ids : this.$refs.orderTable.selectedRowList;
      this.joinFactoryId = factoryId ? factoryId : this.joinFactoryId;
      this.salescustno = custno ? custno : this.$refs.orderTable.selectedRowsData.custNo;
      if (id.length == 0) {
        if (type == "LT") {
          this.LTsalesvisible = false;
        } else {
          this.$message.warning("请选择需要打印的订单");
        }

        return;
      }
      frontcontractreport(id)
        .then(res => {
          if (res.code) {
            this.ContractNoSech = 1;
            this.LTsalesdata = res.data;
            this.LTsalesvisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.buttonload = false;
        });
    },
    YXDform(ids, type, factoryId, custno) {
      //雅信达 晶美 联合多层 销售合同
      if (this.$refs.orderTable.selectedRowList.length == 0 && type != "YXD") {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowList;
      this.yxdids = id;
      if (id.length) {
        if (this.orderListData.filter(ite => ite.id == id[0].toLowerCase())[0]) {
          this.joinFactoryId = this.orderListData.filter(ite => ite.id == id[0].toLowerCase())[0].joinFactoryId;
          this.salescustno = this.orderListData.filter(ite => ite.id == id[0].toLowerCase())[0].custNo;
        } else {
          this.joinFactoryId = factoryId;
          this.salescustno = custno;
        }
      }
      frontcontractreport(id).then(res => {
        if (res.code) {
          this.ContractNoSech = 1;
          this.yxddata = res.data;
          this.yxdVisible = true;
        } else {
          if (ids && ids.length == 0) {
            this.yxdVisible = false;
          } else {
            this.$message.error(res.message);
          }
        }
      });
    },
    handleOkhm() {
      this.$refs.reporthm.getReportPdf();
    },
    hmform(ids) {
      //红马销售合同
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      this.hmVisible = true;
      let id = ids ? ids : this.$refs.orderTable.selectedRowList;
      frontcontractreport(id).then(res => {
        if (res.code) {
          this.salescustno = this.orderListData.filter(ite => ite.id == this.$refs.orderTable.selectedRowList[0])[0].custNo;
          this.ContractNoSech = 1;
          this.hmsalesdata = res.data;
          this.hmVisible = true;
        } else {
          if (ids && ids.length == 0) {
            this.hmVisible = false;
          } else {
            this.$message.error(res.message);
          }
        }
      });
    },
    //市场修改
    marketmodification() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      }
      this.marketdataVisible = true;
    },
    Orderoffline() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择需要下线的订单");
        return;
      }
      if (this.user.factoryName == "精焯" && this.$refs.orderTable.selectedRowList.length > 1) {
        this.auditClick(); //精焯订单下线多选直接走订单下线
      } else if (this.$refs.orderTable.selectedRowsData.isGYLOffLine) {
        this.auditClick1(); //供应链订单下线
      } else {
        this.auditClick();
      }
    },
    //供应链下单
    auditClick1() {
      var arr = this.$refs.orderTable.selectedRowList;
      this.datasource = [];
      if (arr.length == 0) {
        this.$message.warning("请选择需要下线的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowList.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      buttonCheck(arr[0], "OfflineOrder")
        .then(res => {
          if (res.code) {
            this.checkData = res.data;
            this.check = this.checkData.findIndex(v => v.error == "1") < 0;
            if (this.checkData.length == 0) {
              paymodecheck(arr[0]).then(res => {
                // 判断是否有预付款
                if (res.code) {
                  this.popup = "订单下线";
                  this.dataVisible3 = true; // 订单下线确认弹窗
                  this.widthy = 800;
                  this.form.purchasePrice = null;
                  this.type1 = "xd";
                  purchasepricelist(this.$refs.orderTable.selectedRowList[0]).then(res => {
                    if (res.code) {
                      this.xddataSource = res.data;
                    }
                  });
                } else {
                  this.offlinedataVisible = true; // 预付款弹窗
                  this.mmessage = res.message;
                  this.modelvisi = "ddxx2";
                }
              });
            } else {
              this.dataVisible2 = true;
              this.checkType = "ddxxcs";
            }
          } else {
            this.$message.error(res, message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 订单下线添加弹窗
    async auditClick() {
      var arr = this.$refs.orderTable.selectedRowList;
      if (arr.length == 0) {
        this.$message.warning("请选择需要下线的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowList.length != 1 && this.user.factoryName != "精焯") {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      this.allids = [];
      if (this.$refs.orderTable.alldata[0].joinFactoryId == 58 || this.$refs.orderTable.alldata[0].joinFactoryId == 59) {
        this.spinning = true;
        await offlineorders(this.$refs.orderTable.alldata[0].id)
          .then(res => {
            if (res.code) {
              this.allids = res.data;
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else {
        this.allids = this.$refs.orderTable.alldata;
      }
      if (this.$refs.orderTable.alldata[0].joinFactoryId != 33) {
        this.orderDirection = this.$refs.orderTable.selectedRowsData.orderDirection;
      }
      //按钮检查 2023/9/26
      if (this.user.factoryName != "精焯") {
        this.Success_numbers = 0;
        this.Offlineinspection(0);
      } else {
        this.popup = "订单下线";
        this.dataVisible3 = true; // 订单下线确认弹窗
        this.widthy = 400;
        this.form.purchasePrice = null;
        this.type1 = "3";
        this.getData();
      }
    },
    Offlineinspection(ind) {
      if (ind < this.allids.length) {
        this.spinning = true;
        buttonCheck(this.allids[ind].id, "OfflineOrder")
          .then(res => {
            if (res.code) {
              this.count = ind;
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              if (this.checkData.length == 0) {
                this.modeltype = " ";
                paymodecheck(this.allids[ind].id).then(res => {
                  // 判断是否有预付款
                  if (res.code) {
                    //this.dataVisible3 = true;
                    this.popup = "订单下线";
                    this.widthy = 400;
                    this.form.purchasePrice = null;
                    this.type1 = "3";
                    this.modeltype = " ";
                    this.form.orderDirection = this.allids[ind].orderDirection;
                    this.getData();
                    if (ind != 0) {
                      this.handleOk3();
                    } else {
                      this.dataVisible3 = true; // 订单下线确认弹窗
                    }
                  } else {
                    this.offlinedataVisible = true; // 预付款弹窗
                    this.mmessage = res.message;
                    this.modelvisi = "ddxx1";
                    this.modeltype = "yxd";
                  }
                });
              } else {
                this.modeltype = "yxd";
                this.dataVisible2 = true;
                this.checkType = "ddxx";
              }
            } else {
              this.$message.error(res, message);
              this.Offlineinspection(ind + 1);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else {
        this.$refs.orderTable.selectedRowList = [];
        if (
          this.allids.length &&
          this.Success_numbers == this.allids.length &&
          (this.allids[0].joinFactoryId == 58 || this.allids[0].joinFactoryId == 59)
        ) {
          this.spinning = true;
          offlineorderyXD(this.allids[0].id).then(res => {
            if (res.code) {
              let ids = res.data.split(",");
              if (ids.length > 0) {
                ids.forEach((item, index) => {
                  offlineordertoppeyXD(item).then(re => {
                    if (index == ids.length - 1) {
                      this.$message.success("完成");
                      this.getOrderList();
                      this.spinning = false;
                    }
                  });
                });
              } else {
                this.spinning = false;
              }
            } else {
              this.spinning = false;
              this.$message.error(res.message);
            }
          });
        } else {
          this.getOrderList();
          this.spinning = false;
        }
      }
    },
    ok11() {
      this.dataVisible2 = false;
      var arr = this.$refs.orderTable.selectedRowList;
      if (this.checkType == "ddxx") {
        paymodecheck(this.allids[this.count].id).then(res => {
          if (res.code) {
            this.popup = "订单下线";
            //this.dataVisible3 = true;
            this.widthy = 400;
            this.form.purchasePrice = null;
            this.type1 = "3";
            this.form.orderDirection = this.allids[this.count].orderDirection;
            this.getData();
            if (this.count != 0) {
              this.handleOk3();
            } else {
              this.dataVisible3 = true; // 订单下线确认弹窗
            }
          } else {
            this.offlinedataVisible = true;
            this.mmessage = res.message;
            this.modelvisi = "ddxx1";
          }
        });
      }
      if (this.checkType == "ddxxcs") {
        paymodecheck(arr[0]).then(res => {
          if (res.code) {
            this.popup = "订单下线";
            this.dataVisible3 = true;
            this.widthy = 800;
            this.form.purchasePrice = null;
            this.type1 = "xd";
            purchasepricelist(this.$refs.orderTable.selectedRowList[0]).then(res => {
              if (res.code) {
                this.xddataSource = res.data;
              }
            });
          } else {
            this.offlinedataVisible = true;
            this.mmessage = res.message;
            this.modelvisi = "ddxx2";
          }
        });
      }
      if (this.checkType == "ddqx") {
        this.spinning = true;
        ordercancel(arr[0], this.CancelCause)
          .then(res => {
            if (res.code) {
              this.$message.success("取消成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.checkType == "ddsp") {
        this.type1 = "approval";
        this.getData();
        this.Approvetheorder = this.$refs.orderTable.selectedRows.orderDirection;
        this.handleOk3();
      }
      if (this.checkType == "ddht") {
        this.type1 = "6";
        this.handleOk3();
      }
    },
    offhandleOk1() {
      this.offlinedataVisible1 = false;
      this.cunchume = [];
      // this.Offlineinspection(this.count)
      this.$refs.orderTable.selectedRowList = [];
    },
    offhandleOk() {
      this.offlinedataVisible = false;
      this.popup = "订单下线";
      this.form.purchasePrice = null;
      if (this.modelvisi == "ddxx1") {
        this.widthy = 400;
        //this.dataVisible3 = true;
        this.type1 = "3";
        this.form.orderDirection = this.allids[this.count].orderDirection;
        this.getData();
        if (this.count != 0) {
          this.handleOk3();
        } else {
          this.dataVisible3 = true; // 订单下线确认弹窗
        }
      } else if (this.modelvisi == "ddxx2") {
        this.widthy = 800;
        this.type1 = "xd";
        this.dataVisible3 = true;
        purchasepricelist(this.$refs.orderTable.selectedRowList[0]).then(res => {
          if (res.code) {
            this.xddataSource = res.data;
          }
        });
      }
    },
    //订单回退
    orderRollback() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowList.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      var list = [];
      var arr = this.$refs.orderTable.selectedRows;
      for (var i = 0; i < arr.length; i++) {
        list.push(arr[i].orderNo);
      }
      buttonCheck(this.$refs.orderTable.selectedRowList[0], "OrderBackToVerify")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "ddht";
              this.dataVisible2 = true;
            } else {
              this.orderno = this.$refs.orderTable.selectedRows.orderNo;
              this.message = "确认订单回退吗？";
              this.dataVisible3 = true;
              this.widthy = 400;
              this.type1 = "6";
              this.popup = "确认弹窗";
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    Orderapproval() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowList.length > 1) {
        this.type1 = "approval";
        this.getData();
        this.handleOk3();
      } else {
        this.dataVisible3 = true;
        this.widthy = 500;
        this.type1 = "approval";
        this.getData();
        this.Approvetheorder = this.$refs.orderTable.selectedRowsData.orderDirection;
        this.popup = this.orderno + "订单审批";
      }

      // buttonCheck(this.$refs.orderTable.selectedRowList[0], "OrderVerify")
      //   .then(res => {
      //     if (res.code) {
      //       if (res.data && res.data.length) {
      //         this.checkData = res.data;
      //         this.check = this.checkData.findIndex(v => v.error == "1") < 0;
      //         this.checkType = "ddsp";
      //         this.dataVisible2 = true;
      //       } else {
      //         this.dataVisible3 = true;
      //         this.widthy = 500;
      //         this.type1 = "approval";
      //         this.getData();
      //         this.Approvetheorder = this.$refs.orderTable.selectedRowsData.orderDirection;
      //         this.popup = this.orderno + "订单审批";
      //       }
      //     } else {
      //       this.$message.error(res.message);
      //     }
      //   })
      //   .finally(() => {
      //     this.spinning = false;
      //   });
    },
    BackToWaitOffline() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowList.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRows.orderNo;
      this.message = "确认该订单回退到待下线吗？";
      this.dataVisible3 = true;
      this.widthy = 500;
      this.type1 = "back";
      this.popup = "确认弹窗";
    },
    //订单取消
    ordercancellation() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowList.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      this.CancelCause = "";
      var list = [];
      var arr = this.$refs.orderTable.selectedRows;
      for (var i = 0; i < arr.length; i++) {
        list.push(arr[i].orderNo);
      }

      buttonCheck(this.$refs.orderTable.selectedRowList[0], "OrderCancel")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "ddqx";
              this.dataVisible2 = true;
            } else {
              this.orderno = this.$refs.orderTable.selectedRows.orderNo;
              this.message = "确认订单取消吗？";
              this.dataVisible3 = true;
              this.widthy = 800;
              this.type1 = "5";
              this.popup = "确认弹窗";
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //生产通知单
    async Productionnotification() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowList.length != 1 && this.$refs.orderTable.selectedRowsData.joinFactoryId == 12) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 12) {
        this.BQnoticeform();
      } else if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 38) {
        this.LHDCnoticeform();
      }
      // else if(this.$refs.orderTable.selectedRowsData.joinFactoryId==69){
      //   this.HMnoticeform()
      // }
      else {
        let list = this.$refs.orderTable.selectedRowList;
        for (let i = 0; i < list.length; i++) {
          try {
            const res = await noticereviewinfo(list[i], 1);
            if (res.code) {
              this.downloadByteArrayFromString(res.data, res.message);
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          } catch (error) {
            //console.error('Error in iteration:', i, error);
          }
        }
      }
    },
    changeOrder() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowList.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      this.Buriedpointcache(this.buryingpoint);
      getnOPEAlterreport(this.$refs.orderTable.selectedRowList[0]).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    ReviewSheet() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowList.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      contractReviewInfo(this.$refs.orderTable.selectedRowList[0], 1)
        .then(res => {
          let data = res;
          let _self = this;
          let fileReader = new FileReader();
          fileReader.onload = function (event) {
            /* eslint-disable */
            try {
              let jsonData = JSON.parse(event.target.result); // 说明是普通对象数据，后台转换失败
              if (jsonData.code == 0) {
                // 接口返回的错误信息
                _self.$message.error(jsonData.message); // 弹出的提示信息
              }
            } catch (err) {
              const blob = new Blob([res], { type: " application/pdf;charset=utf-8" });
              const url = window.URL.createObjectURL(blob);
              const link = document.createElement("a");
              link.style.display = "none";
              link.href = url;
              let str = "";
              let cutStr = "";
              const arr = _self.$refs.orderTable.selectedRowList;
              for (var a = 0; a < arr.length; a++) {
                if (
                  _self.orderListData.filter(item => {
                    return item.id == arr[a];
                  }).length
                ) {
                  str = _self.orderListData.filter(item => {
                    return item.id == arr[a];
                  })[0].pcbFileName;
                  cutStr =
                    "(" +
                    _self.orderListData.filter(item => {
                      return item.id == arr[a];
                    })[0].custNo +
                    ")";
                }
              }
              link.setAttribute("download", str + cutStr + "-合同评审单.pdf");
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link); // 点击后移除，防止生成很多个隐藏a标签
            }
          };
          fileReader.readAsText(data); // 注意别落掉此代码，可以将 Blob 或者 File 对象转根据特殊的编码格式转化为内容(字符串形式)
        })
        .catch(err => {});
    },
    // 删除型号
    delClick() {
      if (this.$refs.orderTable.selectedRowList.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      var list = [];
      var arr = this.$refs.orderTable.selectedRows;
      for (var i = 0; i < arr.length; i++) {
        list.push(arr[i].orderNo);
      }
      if (list.length > 1) {
        this.message = "请确认批量删除型号吗？";
        this.orderno = "";
      } else {
        this.orderno = this.$refs.orderTable.selectedRows.orderNo;
        this.message = "确认删除型号吗？";
      }
      this.dataVisible3 = true;
      this.widthy = 400;
      this.type1 = "1";
      this.popup = "确认弹窗";
    },
    handleOk3() {
      var arr = this.$refs.orderTable.selectedRowList;
      this.dataVisible3 = false;
      // 删除
      if (this.type1 == "1") {
        this.spinning = true;
        orderDelete(arr)
          .then(res => {
            if (res.code) {
              this.$message.success("删除成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      // 订单下线1确认
      if (this.type1 == "3") {
        this.spinning = true;
        var r = /^0{1}(\.\d*)|(^[1-9][0-9]*)+(\.\d*)?$/;
        if (!this.form.orderDirection) {
          this.$message.warning("请选择下单方向");
          this.spinning = false;
          return;
        }
        if ((!this.form.purchasePrice || !r.test(this.form.purchasePrice)) && this.$refs.orderTable.alldata[0].joinFactoryId == 57 && this.showcg) {
          this.$message.warning("采购价格请输入正数");
          this.spinning = false;
          return;
        }
        if (this.form.purchasePrice && !r.test(this.form.purchasePrice) && this.$refs.orderTable.alldata[0].joinFactoryId == 57 && !this.showcg) {
          this.$message.warning("采购价格请输入正数");
          this.spinning = false;
          return;
        }
        this.cunchume = [];
        if (this.$refs.orderTable.alldata[0].joinFactoryId != 58 && this.$refs.orderTable.alldata[0].joinFactoryId != 59) {
          this.$refs.orderTable.alldata.forEach(item => {
            if (item.orderDirection && item.orderDirection != this.form.orderDirection) {
              this.dataVisibleOrder = true;
              this.spinning = false;
              this.mmessage1 =
                item.orderNo + "此订单上一批次是" + item.orderDirection + "制作，本次选择的是" + this.form.orderDirection + "制作,请确认";
              this.cunchume.push(this.mmessage1);
              this.modeltype = "yxd";
            }
          });
        }
        if (this.cunchume.length == 0) {
          this.modeltype = " ";
          this.offlineOrder(0);
        }
      }
      //订单下线2确认
      if (this.type1 == "xd") {
        var r = /^\+?[1-9][0-9]*$/;
        if (this.datasource.length == 0) {
          this.$message.warning("请选择下单订单");
          return;
        }
        if (!this.datasource.rulePass) {
          this.$message.error("该工厂不符合下单规则");
          return;
        }
        this.cunchume = [];
        if (this.$refs.orderTable.alldata.orderDirection && this.$refs.orderTable.alldata.orderDirection != this.datasource.factoryCode) {
          // if (this.$refs.orderTable.alldata.orderDirection == 58) {
          //   this.$refs.orderTable.alldata.orderDirection = "惠州";
          // } else if (this.$refs.orderTable.alldata.orderDirection == 59) {
          //   this.$refs.orderTable.alldata.orderDirection = "江西";
          // }
          // if (this.datasource.factoryCode == 58) {
          //   this.datasource.factoryCode = "惠州";
          // } else if (this.datasource.factoryCode == 59) {
          //   this.datasource.factoryCode = "江西";
          // }
          this.dataVisibleOrder = true;
          this.mmessage1 =
            this.$refs.orderTable.alldata.orderNo +
            "此订单上一批次是" +
            this.$refs.orderTable.alldata.orderDirection +
            "制作，本次选择的是" +
            this.datasource.factoryCode +
            "制作,请确认";
          this.cunchume.push(this.mmessage1);
        }
        if (this.cunchume.length == 0) {
          this.offlineOrder(0);
        }
      }
      //取消
      if (this.type1 == "5") {
        if (!this.CancelCause) {
          this.$message.error("请输入取消原因");
          return;
        }
        this.spinning = true;
        // this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单管理',usingFeatures:'订单取消',})
        this.Buriedpointcache(this.buryingpoint);
        ordercancel(arr[0], this.CancelCause)
          .then(res => {
            if (res.code) {
              this.$message.success("取消成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      //回退1 待下线回退到报价中
      if (this.type1 == "6") {
        this.spinning = true;
        //this.buryingpoint.push({name:this.user.name,time:moment().format("YYYY-MM-DD"),Modulepage:'市场管理订单管理',usingFeatures:'订单回退',})
        this.Buriedpointcache(this.buryingpoint);
        backtoverify(arr[0])
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      //回退2 已完成回退到待下线
      if (this.type1 == "back") {
        this.spinning = true;
        back2Waitoffline(arr)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      //订单审批
      if (this.type1 == "approval") {
        if (this.$refs.orderTable.selectedRowList.length > 1) {
          this.Approvetheorder = null;
        } else {
          if (!this.Approvetheorder) {
            this.$message.error("请选择下单方向");
            this.dataVisible3 = true;
            return;
          }
        }
        this.spinning = true;
        let params = {
          ids: arr,
          orderDirection: this.Approvetheorder,
        };
        orderverifyyXD(params)
          .then(res => {
            if (res.code) {
              this.$message.success("审批成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      this.popup = "确认弹窗";
      if (this.type1 != "3") {
        this.$refs.orderTable.selectedRows = [];
        this.$refs.orderTable.selectedRowList = [];
      }
    },
    handleOrder() {
      this.dataVisibleOrder = false;
      this.cunchume = [];
      this.offlineOrder(0);
    },
    // 下线接口
    offlineOrder(index) {
      //this.cunchume=[]
      this.Buriedpointcache(this.buryingpoint);
      this.spinning = true;
      this.auditload = true;
      if (this.type1 == "3") {
        let all = this.$refs.orderTable.alldata;
        if (all[0].joinFactoryId == 59 || all[0].joinFactoryId == 58) {
          all[0] = this.allids[this.count];
        }
        if (index < all.length) {
          const item = all[index];
          const val = {
            id: item.id,
            orderDirection: this.form.orderDirection,
            purchasePrice: this.form.purchasePrice ? Number(this.form.purchasePrice) : null,
          };
          offlineOrder(val)
            .then(res => {
              if (res.code) {
                this.$message.success(item.orderNo + "下线成功");
                this.Success_numbers += 1;
                if (all[0].joinFactoryId == 37) {
                  timedautomaticsendorder(all[0].joinFactoryId).then(res => {});
                }
                this.$refs.orderTable.selectedRowList = [];
              } else {
                this.cunchume.push({ no: item.orderNo, message: res.message });
              }
              // 继续递归调用下一个订单处理
              return this.offlineOrder(index + 1);
            })
            .catch(error => {
              console.error(error);
              return this.offlineOrder(index + 1);
            });
        } else {
          // 所有订单处理完成后执行
          this.spinning = false;
          this.auditload = false;
          if (this.cunchume.length > 0) {
            this.offlinedataVisible1 = true;
            this.modeltype = "yxd";
          } else {
            this.Offlineinspection(this.count + 1);
          }
        }
      }
      if (this.type1 == "xd") {
        let val = {
          id: this.$refs.orderTable.alldata[0].id,
          orderDirection: this.datasource.factoryId.toString(),
          purchasePrice: this.datasource.purchasePrice ? Number(this.datasource.purchasePrice) : null,
        };
        const promises = [];
        this.auditload = true;
        const promise = offlineOrder(val).then(res => {
          if (res.code) {
            this.$message.success(this.$refs.orderTable.alldata[0].orderNo + "下线成功");
            if (this.$refs.orderTable.alldata[0].joinFactoryId == 37) {
              timedautomaticsendorder(this.$refs.orderTable.alldata[0].joinFactoryId).then(res => {});
            }
            this.$refs.orderTable.selectedRowList = [];
          } else {
            this.cunchume.push({ no: this.$refs.orderTable.alldata[0].orderNo, message: res.message });
          }
        });
        promises.push(promise);
        Promise.all(promises)
          .then(() => {
            this.spinning = false;
            this.auditload = false;
            this.getOrderList();
            if (this.cunchume.length > 0) {
              this.offlinedataVisible1 = true;
            }
          })
          .catch(error => {
            console.error(error);
            this.spinning = false;
            this.auditload = false;
          });
      }
    },
    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    mapKey(data) {
      if (!data || data.length == 0) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
  },
};
</script>

<style scoped lang="less">
.xiaoshou {
  /deep/.ant-modal-header {
    padding: 10px 24px;
  }
  /deep/.ant-modal-body {
    padding: 8px 20px;
    max-height: 807px;
    overflow: auto;
  }
  /deep/.ant-modal-footer {
    padding: 5px 16px;
  }
}
/deep/#modal-ordermanagement > div:first-child {
  height: 100%;
}
#modal-ordermanagement {
  position: relative;
  height: 100%;
  /deep/.ant-modal-root {
    height: 100%;
  }
  /deep/.ant-modal-wrap {
    position: relative;
    width: 100%;
    // border:2px solid #efefef;
    border: 2px solid #ffcd82;
    box-shadow: 0 6px 20px #ffcd82;
    border-left:hover {
      cursor: e-resize;
    }
  }
  /deep/.ant-modal {
    top: 0;
    position: unset;
    padding-bottom: 0;
  }
  /deep/.ant-modal-content {
    position: unset;
  }
}
/deep/.dragging1 {
  display: none;
  position: absolute;
  top: -20px;
  left: -174px;
  // left:380px;
  z-index: 999 !important;
  .handle {
    width: 1px;
    height: 1px;
    border: none;
    z-index: 9999;
    display: block !important;
    background: none;
  }
  .handle-tl {
    top: 0;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-tm {
    top: 1px;
    left: 0;
    width: 100%;
    margin-left: 0;
  }
  .handle-tr {
    top: 0;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-mr {
    top: 0;
    right: -1px;
    margin-top: 1px;
    height: 100% !important;
  }
  .handle-br {
    bottom: -1px;
    right: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000;
  }
  .handle-bm {
    bottom: -1px;
    left: 0;
    margin-left: 0;
    width: 100%;
  }
  .handle-bl {
    bottom: -1px;
    left: -1px;
    width: 4px !important;
    height: 4px !important ;
    z-index: 10000 !important;
  }
  .handle-ml {
    left: -1px;
    margin-top: 0;
    top: 1px;
    height: 100%;
  }
}
.yushen {
  /deep/.ant-modal-footer {
    padding: 8px;
    border-top: none;
  }
  /deep/.ant-modal-body {
    padding-top: 0px;
    padding-bottom: 0px;
    overflow: auto;
  }
  /deep/.ant-modal-header {
    border-bottom: 1px solid #ddd;
    padding: 9px 24px;
  }
}
.righttable {
  /deep/.ant-empty-normal {
    margin: 5px 0;
  }
  border-bottom: 2px solid rgb(233, 233, 240);
  width: 33%;
  height: 776px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  .toptable {
    height: 20%;
    width: 100%;
  }
  .centertable {
    height: 45%;
    width: 100%;
  }
  .bottable {
    height: 35%;
    width: 100%;
  }
}
.Select {
  /deep/.ant-select {
    width: 100%;
  }
}
/deep/.ant-modal {
  padding-bottom: 0;
}
.formclass1 {
  /deep/.ant-modal-body {
    padding: 0 !important;
  }
}
.required {
  /deep/.ant-form-item-label label {
    color: red !important;
  }
}
.formclass {
  /deep/.ant-modal-body {
    padding: 0 !important;
  }
  /deep/.ant-modal-footer {
    padding: 10px 100px;
  }
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.rowBackgroundColor {
  background: rgb(223, 220, 220) !important;
}
.xd {
  /deep/.ant-table-thead > tr:not(:last-child) > th[colspan] {
    border-bottom: 1px solid #efefef;
  }
  /deep/.ant-table-scroll {
    border: 1px solid #efefef;
  }
  /deep/.ant-modal-body {
    padding: 0 4px;
  }
  /deep/.ant-modal-body {
    padding: 0;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 4px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 4px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr {
      .lastTd {
        padding: 0 4px !important;
      }
    }
  }
}
.require {
  /deep/.ant-form-item-label > label {
    color: red;
  }
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 16px 0 0 16px;
}
.icon-left:before {
  content: "\2039"; // 左箭头图标的 Unicode
}
.icon-right:before {
  content: "\203A"; // 右箭头图标的 Unicode
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
  font-size: 12px;
  margin-left: -17px;
  margin-top: 27px;
}
/deep/.ant-collapse > .ant-collapse-item {
  border: none;
  border-top: 2px solid rgb(233, 233, 240);
}
/deep/.ant-collapse {
  background-color: white;
  border-width: 0 3px 3px 0;
  border-style: solid;
  border-color: #e9e9f0;
  border-radius: inherit;
  // border-right: 2px solid rgb(233, 233, 240);
  // border:none
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding: 16px 0px 18px 0px;
  padding-left: 15px;
  padding-top: 0px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  // margin-left: -15px;
  margin-top: -19px;
}
/deep/.ant-modal-body {
  color: #000000;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
// /deep/.ant-form-item-label > label{
//   color: #000000
// }
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
// .ant-row{
//   .ant-col{
//     .ant-form-item{
//       .ant-form-item-control-wrapper{
//         .ant-form-item-control{
//           .ant-form-item-children{
//             .ant-select{
//               width: 270px;
//             }
//           }
//         }
//       }
//     }
//   }
// }
/deep/.ant-table-selection-column {
  padding: 6px 0 !important;
  display: inline-block;
  width: 25px !important;
}
/deep/.ant-modal-body {
  padding: 24px 19px !important;
}
.projectBackend {
  /deep/.userStyle {
    user-select: none !important;
  }
  background: #ffffff;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  /deep/.leftContent {
    .ant-table-body {
      .ant-table-fixed {
        width: 1650px !important;
      }
    }
    .tabRightClikBox {
      // border:2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }

  /deep/ .rightContent {
    width: 41%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .centerTable {
      .peopleTag {
        position: absolute;
        font-size: 12px;
        font-weight: 600;
        left: 0;
        padding: 0 2px;
      }
      .ant-table-body {
        .ant-table-tbody {
          .ant-table-row:first-child {
            .anticon {
              display: none;
            }
          }
        }
        max-height: 702px !important;
      }
      width: 50%;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
    }
    .rightTable {
      .ant-table-body {
        max-height: 715px !important;
      }
      width: 50%;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
      .jobNote {
        /deep/ .ant-table-wrapper {
          user-select: none;
          height: 444px;
        }
        .minTable {
          min-height: 200px;
        }
        .note {
          height: 300px;
          overflow-y: auto;
        }
      }
      .peopleTag {
        margin: 0;
        padding: 0;
        width: 24px;
        border-radius: 12px;
        background: #2d221d;
        border-color: #2d221d;
        color: #ff9900;
        text-align: center;
        margin-left: 2px;
      }
    }
  }
  .footerAction {
    width: 100%;
    height: 48px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container {
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }
  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
      .eqBackground {
        background: #ffff00;
      }
      .statuBackground {
        background: #b0e0e6;
      }
      .backUserBackground {
        background: #a7a2c9;
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    /deep/.userStyle {
      user-select: all !important;
    }
    //td[class~='userStyle'] {
    //  user-select: all !important;
    //}
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/.ant-table-pagination.ant-pagination {
    margin: 8px 0;
    z-index: 99;
    position: absolute;
    margin-left: 1%;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #d6d6d6;
    .imgTable {
      img {
        width: 100px;
        height: 50px;
      }
    }
    p {
      height: 100%;
      line-height: 35px;
      font-weight: 700;
      margin: 0;
    }
    .displayFlag {
      display: none;
    }
  }
}
</style>
