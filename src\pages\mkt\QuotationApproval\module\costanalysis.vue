<template>
  <div v-show="xlsurl" style="width: 100%; height: 100%">
    <vue-office-excel class="excel" style="height: 100%; width: 100%" :src="xlsurl" ref="xlsxViewer" :options="options" />
  </div>
</template>
<script>
import { ltCostEXLEV2 } from "@/services/mkt/OrderReview.js";
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/excel/lib/index.css";
export default {
  components: {
    VueOfficeExcel,
  },
  data() {
    return {
      xlsurl: "",
      options: {
        minColLength: 0, // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
        minRowLength: 0, // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
        widthOffset: 30, //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
        heightOffset: 0, //在默认渲染的列表高度上再加 Npx高
        beforeTransformData: workbookData => {
          return workbookData;
        }, //底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。
        transformData: workbookData => {
          workbookData[0].styles.forEach(ite => {
            if (ite.align == "center") {
              ite.font.size = 12;
            } else {
              ite.font.size = 10;
            }
          });
          return workbookData;
        }, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容
      },
    };
  },
  mounted() {
    this.getxlsurl();
  },
  methods: {
    getxlsurl() {
      ltCostEXLEV2({ id: this.$route.query.id.split(",") }).then(res => {
        if (res.code) {
          this.xlsurl = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.x-spreadsheet-overlayer {
  width: 100% !important;
  height: 100% !important;
}
/deep/.x-spreadsheet-sheet {
  width: 100% !important;
  height: 100% !important;
}
/deep/.x-spreadsheet-overlayer-content {
  width: 100% !important;
  height: 100% !important;
}
</style>
