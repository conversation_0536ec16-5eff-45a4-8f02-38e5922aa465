<!-- 市场管理 - 订单报价- hm -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj" type="primary" class="printstyle" @click="printpdf">打印</a-button>
    <div id="pdfDomhm" style="font-size: 12px; font-family: 'Courier New', Courier, monospace; text-shadow: 0 0 1px black; color: black">
      <div style="display: flex; width: 100%">
        <div style="width: 1040px; text-align: center">
          <span style="font-size: 28px; letter-spacing: 15px">珠海市晟辉电子有限公司 </span><br />
          <span style="font-size: 20px; letter-spacing: 15px">销售合同 </span><br />
        </div>
        <div style="margin-top: 15px; font-size: 12px; width: 181px">
          <table border="1" style="float: right">
            <tr>
              <td style="padding-left: 1ch">合同编号:</td>
              <td style="width: 100px; padding-left: 1ch">{{ hmsalesdata.orderNo_ }}</td>
            </tr>
            <tr>
              <td style="padding-left: 1ch">日&nbsp;&nbsp;期:</td>
              <td style="padding-left: 1ch">{{ hmsalesdata.date_ }}</td>
            </tr>
            <tr>
              <td style="padding-left: 1ch">币&nbsp;&nbsp;种:</td>
              <td style="padding-left: 1ch">{{ hmsalesdata.currency_ }}</td>
            </tr>
          </table>
        </div>
      </div>
      <div style="display: flex">
        <div style="width: 900px">
          <div>客户:{{ hmsalesdata.name_ }}</div>
          <div>地址:{{ hmsalesdata.address_ }}</div>
          <div>联系人: {{ hmsalesdata.party_ }}</div>
          <div>电话: {{ hmsalesdata.facPhone_ }}</div>
          <div>经双方友好协议达成一致,订立本合同 :</div>
        </div>
        <div style="width: 200px">
          <div style="margin-top: 20px">联系人:{{ hmsalesdata.link_ }}</div>
          <div>电话:{{ hmsalesdata.tel_ }}</div>
        </div>
        <span @click="addcontract" style="color: #4b82ac; margin-top: 33px" v-if="showadd && act != 'dis'"
          >点击添加 <a-icon style="font-size: 16px; top: 2px; position: relative" type="plus-circle"></a-icon
        ></span>
      </div>
      <div>
        <table border="1" style="text-align: center; width: 100%">
          <thead>
            <tr>
              <td style="width: 30px" rowspan="2">序号</td>
              <td style="width: 100px" rowspan="2">客户型号</td>
              <td style="width: 100px" rowspan="2">物料编码</td>
              <td colspan="13">成品工艺要求</td>
              <td rowspan="2">数量</td>
              <td rowspan="2" v-if="salescustno != 'M396' && salescustno != 'M396B'">平米价</td>
              <td rowspan="2" v-if="salescustno != 'M396' && salescustno != 'M396B'">单价</td>
              <td rowspan="2" v-if="salescustno == 'M396' || salescustno == 'M396B'">含税平米价</td>
              <td rowspan="2" v-if="salescustno == 'M396' || salescustno == 'M396B'">未税单价</td>
              <td rowspan="2" v-if="salescustno == 'M396' || salescustno == 'M396B'">含税单价</td>
              <td rowspan="2">工程费</td>
              <td rowspan="2">加急费</td>
              <td rowspan="2">飞针费</td>
              <td rowspan="2">其他费用</td>
              <td rowspan="2">合计金额</td>
              <td rowspan="2">交期</td>
              <td rowspan="2">备注</td>
              <td style="width: 40px" rowspan="2" v-if="showadd && act != 'dis'">操作</td>
            </tr>
            <tr>
              <td>品牌</td>
              <td>板材</td>
              <td>层</td>
              <td>板厚(mm)</td>
              <td>外铜oz</td>
              <td>内铜oz</td>
              <td>交货尺寸(mm)</td>
              <td>拼版</td>
              <td>表面处理</td>
              <td>阻焊</td>
              <td>字符</td>
              <td>过孔工艺</td>
              <td>测试架</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in hmsalesdata.price" :key="index">
              <td>{{ item.no }}</td>
              <td>{{ item.custName }}</td>
              <td>{{ item.mat }}</td>
              <td>{{ item.bType }}</td>
              <td>{{ item.boardBrand }}</td>
              <td>{{ item.lay }}</td>
              <td>{{ item.boardT }}</td>
              <td>{{ item.oucu }}</td>
              <td>{{ item.incu }}</td>
              <td>{{ item.setBoardWidth }}*{{ item.setBoardHeight }}</td>
              <td>{{ item.su }}</td>
              <td>{{ item.surface }}</td>
              <td>{{ item.mask }}</td>
              <td>{{ item.char_ }}</td>
              <td>{{ item.solder }}</td>
              <td v-if="salescustno != 'M396' && salescustno != 'M396B'">{{ item.test }}</td>
              <td v-if="salescustno != 'M396' && salescustno != 'M396B'">{{ item.qty }}</td>
              <td v-if="salescustno == 'M396' || salescustno == 'M396B'">{{ item.cktPrice }}</td>
              <td v-if="salescustno == 'M396' || salescustno == 'M396B'">{{ item.bgPrice }}</td>
              <td v-if="salescustno == 'M396' || salescustno == 'M396B'">{{ item.bbPrice }}</td>
              <td>{{ item.plate }}</td>
              <td>{{ item.pcs }}</td>
              <td>{{ item.eng }}</td>
              <td>{{ item.urgent }}</td>
              <td>{{ item.fly }}</td>
              <td>{{ item.other }}</td>
              <td>{{ item.total }}</td>
              <td>{{ item.custdate }}</td>
              <td>{{ item.notes }}</td>
              <td v-if="showadd && act != 'dis'">
                <a-tooltip placement="top" title="删除当前行数据">
                  <a-icon @click="delclick(item.id)" style="font-size: 14px; color: #4b82ac" type="close-circle"></a-icon>
                </a-tooltip>
              </td>
            </tr>
            <tr>
              <td style="text-align: right; padding-right: 15px" colspan="14">总金额(人名币/大写): {{ convertToChineseNum(amountto) }}</td>
              <td style="text-align: left; padding-left: 6px" colspan="14">￥ {{ amountto }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="z-index: 99; position: relative">
        <span>备注 : </span><br />
        <div>
          <div>
            一、质量技术及验收标准:需方按■【{{ hmsalesdata.ipcLevel }}】；客供资料:■ 【{{ hmsalesdata.eml_ }}】;包装要求:■【{{
              hmsalesdata.defend
            }}】；交货要求:■【{{ hmsalesdata.deliveryType }}】；<br />
            二、付款方式:■【{{ hmsalesdata.clearingForm }}】；票据形式:■【{{ hmsalesdata.invoiceType_ }}】;签约地点:珠海；
          </div>
          <div>
            三、验收:需方须于收到货物后5日内进行验收,如在约定时间内需方未提出书面异议的,视为货物的质量、数量均合格。品质不良乙方仅承担有质量问题的线路板本身单价不超过三倍的赔偿责任,不承担其他连带责任。
            <br />四、条款说明:
          </div>
          <div>1、本合同双方单位签字盖章或委托代理人签字后即时生效,具有同等法律效力。</div>
          <div>
            2、付款约定:需方应按约定时间付款,如逾期未付则自逾期之日起每日按应支付总货款的3%支付违约金,违约方还应承担由此造成守约方
            实现债权产生的诉讼费、律师费、差旅费等必要费用。
          </div>
          <div>3、需方委托供方加工的线路板版权应当是需方合法持有的,如有侵犯第三方产权的行为,全部责任由需方承担,与供方无关。</div>
          <div>4、本合同所涉及的所有费用均以人民币计</div>
          <div>5、因本合同发生纠纷的,双方先协商解决,协商不成,向供方所在地法院起诉,适用中华人民共和国法律。</div>
          <div>6、需方须于收到本合同需在24小时内盖章回传,需方未在约定时间盖章回传,本合同无效。</div>
          <div style="">7、在线板,通知暂停时间最长为60天,超时报废处理。暂停,取消需核算成本费用。</div>
          <div>
            8、此订单半年内未返单,测试架与模具甲方可以取回,否则我司自行报废处理,逾期不退,我司新开测试架保质期为一年,若一年后测试架
            没法修理,需客户重新开测试架。
          </div>
        </div>
        <div style="margin-top: 10px; display: flex">
          <div style="width: 720px">
            <div>供方(签章): 珠海市晟辉电子有限公司</div>
            <div>代 表:</div>
          </div>
          <div style="width: 465px">
            <div>需方(签章): {{ hmsalesdata.name_ }}</div>
            <div>代 表:</div>
          </div>
        </div>
      </div>
      <img
        src="@/assets/img/hmhtz.png"
        style="position: relative; top: -107px; z-index: 0; display: block; left: 54px; width: 200px; transform: rotate(353deg)"
      />
    </div>
    <a-modal title="合同数据添加" :visible="modalvisible" @ok="handleOk" @cancel="handleCancel" centered :width="800">
      <a-row>
        <a-col :span="10">
          <a-form-item label="订单号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入订单号进行查询" v-model="OrderNo" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="客户型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入客户型号进行查询" v-model="PcbFileName" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" style="margin-top: 2px" @click="queryclick">查询</a-button>
        </a-col>
      </a-row>
      <a-table
        :columns="columns"
        :row-selection="{ selectedRowKeys: selectedRowKeysind, onChange: onSelectChange, columnWidth: 25 }"
        :pagination="false"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ y: 300 }"
        :dataSource="datasource"
        :rowClassName="isRedRow"
      >
        <!-- :customRow="onClickRow" -->
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import { verifyPageList, deletecontractno, contractNo } from "@/services/mkt/OrderReview.js";
import htmlToPdf from "@/utils/htmlToPdfa3";
//htmlToPdfa3  htmlToPdf
export default {
  name: "ReportInfoyxd",
  props: ["hmsalesdata", "joinFactoryId", "salescustno", "ContractNoSech", "ttype", "act"],
  computed: {},
  data() {
    return {
      printObj: {
        id: "pdfDomhm", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      selectedRowKeys: [],
      showadd: true,
      selectedRowKeysind: [],
      PcbFileName: "",
      OrderNo: "",
      datasource: [],
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 35,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "订单号",
          align: "left",
          ellipsis: true,
          width: 80,
          dataIndex: "orderNo",
        },
        {
          title: "客户代码",
          dataIndex: "custNo",
          align: "left",
          ellipsis: true,
          width: 50,
        },

        {
          title: "订单类型",
          dataIndex: "reOrder",
          align: "left",
          ellipsis: true,
          width: 50,
          customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
        },
      ],
      amountto: 0,
      modalvisible: false,
      ids: [],
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.amountto = 0;
    this.ids = [];
    this.showadd = !this.hmsalesdata.price.some(ite => ite.status == 30);
    this.hmsalesdata.price.forEach(item => {
      if (this.ids.indexOf(item.id) == -1) {
        this.ids.push(item.id);
      }
    });
    for (let index = 0; index < this.hmsalesdata.price.length; index++) {
      if (this.hmsalesdata.price[index].total && this.hmsalesdata.price[index].total != "/") {
        this.amountto += Number(this.hmsalesdata.price[index].total);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
      this.showadd = !this.hmsalesdata.price.some(ite => ite.status == 30);
    },
    printpdf() {
      document.title = this.hmsalesdata.pcbFileName;
      this.showadd = false;
    },
    delclick(id) {
      this.ids = [];
      this.hmsalesdata.price.forEach(item => {
        if (this.ids.indexOf(item.id) == -1) {
          this.ids.push(item.id);
        }
      });
      deletecontractno(id).then(res => {
        if (res.code) {
          this.ids.forEach(item => {
            if (item == id) {
              this.ids.splice(this.ids.indexOf(item), 1);
            }
          });
          this.$message.success("删除成功");
          this.$emit("hmform", this.ids);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeys = rowKeys;
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysind = selectedRowKeys;
      this.selectedRowKeys = [];
      selectedRows.forEach(item => {
        this.selectedRowKeys.push(item.id);
      });
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    queryclick() {
      if (!this.OrderNo && !this.PcbFileName) {
        this.$message.error("请输入订单号或客户型号进行查询");
        return;
      }
      let params = {
        PageIndex: 1,
        PageSize: 20,
        CustNo: this.salescustno,
        ContractNoSech: this.ContractNoSech,
      };
      if (this.OrderNo) {
        params.OrderNo = this.OrderNo;
      }
      if (this.PcbFileName) {
        params.PcbFileName = this.PcbFileName;
      }
      verifyPageList(params).then(res => {
        if (res.code) {
          this.datasource = res.data.items;
          if (this.datasource.length == 0) {
            this.$message.error("未查询到相关订单");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    addcontract() {
      this.modalvisible = true;
      this.selectedRowKeys = [];
      this.datasource = [];
      this.OrderNo = "";
      this.PcbFileName = "";
      this.selectedRowKeysind = [];
    },
    handleOk() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择订单");
        return;
      }
      this.ids = [...this.ids, ...this.selectedRowKeys];
      contractNo(this.ids).then(res => {
        if (res.code) {
          this.$emit("hmform", this.ids);
        } else {
          this.$message.error(res.message);
        }
      });
      this.modalvisible = false;
    },
    handleCancel() {
      this.modalvisible = false;
    },
    getReportPdf() {
      this.showadd = false;
      setTimeout(() => {
        htmlToPdf("pdfDomhm", this.hmsalesdata.pcbFileName);
        this.showadd = !this.hmsalesdata.price.some(ite => ite.status == 30);
      }, 500);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
/deep/.rowBackgroundColor {
  background: #dcdcdc !important;
}
/deep/.ant-table-row-selected {
  background: #dcdcdc !important;
}
/deep/.ant-table-tbody > tr.ant-table-row-selected td {
  background: #dcdcdc;
}
/deep/.ant-table {
  border: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-thead > tr > th {
  padding: 4px 4px;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 4px 4px !important;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
  color: #000000;
}
//   .transparent-image {
//   filter: invert(1) hue-rotate(180deg);
// }
.agreement {
  padding-bottom: 20px;
  position: relative;
  z-index: 99;
  div {
    padding-top: 10px;
  }
}
.tableclass {
  border: 1px solid black;
  margin-top: 10px;
  margin-bottom: 10px;
  td {
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding-left: 7px;
  }
  tbody {
    tr {
      height: 24px;
    }
  }
}
.pdfDom1 {
  //  font-family: 'Courier New', Courier, monospace;
  font-size: 12px;
  padding: 25px;
  height: 650px;
  overflow: auto;
}
</style>
