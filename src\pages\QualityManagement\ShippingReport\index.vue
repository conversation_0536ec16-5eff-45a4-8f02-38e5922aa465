<!--品质管理- 出货报告 -->
<template>
  <div ref="SelectBox">
    <a-spin :spinning="spinning">
      <div class="projectBackend">
        <div class="content">
          <a-input
            style="width: 10%; margin-right: 0.5%"
            placeholder="生产型号"
            @change="inputchange"
            v-model="formData.PinBanNo"
            allowClear
            @keyup.enter="searchClick"
            id="pb"
            autofocus
          ></a-input>
          <a-button type="primary" @click="searchClick" style="margin-right: 0.5%">查询</a-button>
          <a-button type="primary" @click="Shippingreport" style="margin-right: 0.5%">导出报告</a-button>
          <a-button type="primary" @click="editClick" v-if="!editFlg" style="margin-right: 0.5%">编辑</a-button>
          <a-button type="primary" @click="cancelClick" v-if="editFlg" style="margin-right: 0.5%">取消</a-button>
          <a-button type="primary" @click="saveClick" style="margin-right: 0.5%">保存</a-button>
        </div>
        <div class="table">
          <div class="leftContent" style="display: flex">
            <div style="width: 26%; border-right: 2px solid #e9e9f0">
              <vxe-table
                border
                stripe
                show-overflow
                class="toptable"
                :scroll-y="{ enabled: true, gt: 20 }"
                ref="xTable1"
                :height="770"
                :row-config="{ isCurrent: true, isHover: true }"
                @cell-click="cellClickEvent"
                :data="dataSource1"
              >
                <vxe-column type="seq" title="序号" width="60" style="text-align: center" align="center"></vxe-column>
                <vxe-column field="facNumber" title="生产型号" show-overflow></vxe-column>
                <vxe-column field="isMark" title="标识" width="50" show-overflow>
                  <template #default="{ row }">
                    <div>{{ row.isMark ? "是" : "否" }}</div>
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
            <div style="width: 74%">
              <a-table
                :columns="columns"
                :dataSource="orderListData"
                :pagination="false"
                :rowKey="'id'"
                :loading="orderListTableLoading"
                @change="handleTableChange"
                :scroll="{ y: 577 }"
                class="centerTable"
                :class="orderListData.length ? 'minTable' : ''"
              >
                <span slot="value_" slot-scope="text, record">
                  <a-input v-if="editFlg" v-model="record.value_" allowClear />
                  <span v-else>{{ record.value_ }}</span>
                </span>
                <template slot="result_" slot-scope="text, record">
                  <a-select v-if="editFlg" v-model="record.result_" allowClear>
                    <a-select-option value="ACC">ACC</a-select-option>
                    <a-select-option value="REJ">REJ</a-select-option>
                  </a-select>
                  <span v-else>{{ record.result_ }}</span>
                </template>
              </a-table>
              <div>
                <div v-if="editFlg" class="centerContentBottom">
                  <a-upload
                    accept=".jpg,.png,.jpeg,"
                    name="file"
                    @preview="handlePreview"
                    list-type="picture-card"
                    ref="fileRef"
                    :before-upload="beforeUpload1"
                    :customRequest="httpRequest1"
                    :file-list="SizefileData"
                    @change="handleChangesize"
                  >
                    <a-button style="font-weight: 500; height: 40px"> 上传尺寸图 </a-button>
                  </a-upload>
                  <a-upload
                    accept=".jpg,.png,.jpeg,"
                    name="file"
                    @preview="handlePreview"
                    list-type="picture-card"
                    ref="fileRef"
                    :before-upload="beforeUpload1"
                    :customRequest="httpRequest1"
                    :file-list="DrlfileData"
                    @change="handleChangedrl"
                  >
                    <a-button style="font-weight: 500; height: 40px"> 上传分孔图 </a-button>
                  </a-upload>
                </div>
                <div v-else class="centerContentBottom">
                  <div style="width: 50%">
                    <div style="padding-left: 15px; font-weight: bold; font-size: 16px">尺寸图:</div>
                    <div v-if="this.sliceSizeImg_">
                      <span v-for="(item, index) in this.sliceSizeImg_.split('|')" :key="index">
                        <img :src="item" style="max-height: 60px; padding-left: 15px" v-viewer />
                      </span>
                    </div>
                  </div>
                  <div style="width: 50%">
                    <div style="padding-left: 15px; font-weight: bold; font-size: 16px">分孔图:</div>
                    <div v-if="this.sliceDrlImg_">
                      <span v-for="(item, index) in this.sliceDrlImg_.split('|')" :key="index">
                        <img :src="item" style="max-height: 60px; padding-left: 15px" v-viewer />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="rightContent">
            <div class="rightContentTop">
              <a-table
                :columns="columns2"
                :dataSource="dataSource2"
                :pagination="false"
                :rowKey="'id'"
                :loading="TableLoading2"
                @change="handleTableChange"
                :scroll="{ y: 360, x: 300 }"
                :class="dataSource2.length ? 'minTable1' : ''"
              >
                <template slot="actual1_" slot-scope="text, record">
                  <a-input v-if="editFlg" v-model="record.actual1_" allowClear />
                  <span v-else>{{ record.actual1_ }}</span>
                </template>
                <template slot="actual2_" slot-scope="text, record">
                  <a-input v-if="editFlg" v-model="record.actual2_" allowClear />
                  <span v-else>{{ record.actual2_ }}</span>
                </template>
                <template slot="actual3_" slot-scope="text, record">
                  <a-input v-if="editFlg" v-model="record.actual3_" allowClear />
                  <span v-else>{{ record.actual3_ }}</span>
                </template>
                <template slot="actual4_" slot-scope="text, record">
                  <a-input v-if="editFlg" v-model="record.actual4_" allowClear />
                  <span v-else>{{ record.actual4_ }}</span>
                </template>
                <template slot="result_" slot-scope="text, record">
                  <a-select v-if="editFlg" v-model="record.result_" allowClear>
                    <a-select-option value="ACC">ACC</a-select-option>
                    <a-select-option value="REJ">REJ</a-select-option>
                  </a-select>
                  <span v-else>{{ record.result_ }}</span>
                </template>
              </a-table>
            </div>
            <div class="rightContentCnter">
              <a-table
                :columns="columns3"
                :dataSource="dataSource3"
                :pagination="false"
                :rowKey="'id'"
                :loading="TableLoading3"
                @change="handleTableChange"
                :scroll="{ y: 181, x: 300 }"
                :class="dataSource3.length ? 'minTable2' : ''"
              >
                <template slot="tolerance" slot-scope="text, record, index">
                  <a-input v-if="editFlg" v-model="record.tolerance" @blur="handleBlur(record, index, 'tolerance')" />
                  <span v-else>{{ record.tolerance }}</span>
                </template>
                <template slot="size" slot-scope="text, record, index">
                  <a-input v-if="editFlg" v-model="record.size" @blur="handleBlur(record, index, 'size')" />
                  <span v-else>{{ record.size }}</span>
                </template>
                <template slot="actual1_" slot-scope="text, record, index">
                  <a-input v-if="editFlg" v-model="record.actual1_" @blur="handleBlur(record, index, 'actual1_')" />
                  <span v-else>{{ record.actual1_ }}</span>
                </template>
                <template slot="actual2_" slot-scope="text, record, index">
                  <a-input v-if="editFlg" v-model="record.actual2_" @blur="handleBlur(record, index, 'actual2_')" />
                  <span v-else>{{ record.actual2_ }}</span>
                </template>
                <template slot="actual3_" slot-scope="text, record, index">
                  <a-input v-if="editFlg" v-model="record.actual3_" @blur="handleBlur(record, index, 'actual3_')" />
                  <span v-else>{{ record.actual3_ }}</span>
                </template>
                <template slot="actual4_" slot-scope="text, record, index">
                  <a-input v-if="editFlg" v-model="record.actual4_" @blur="handleBlur(record, index, 'actual4_')" />
                  <span v-else>{{ record.actual4_ }}</span>
                </template>
                <template slot="result_" slot-scope="text, record">
                  <a-select v-if="editFlg" v-model="record.result_" allowClear>
                    <a-select-option value="ACC">ACC</a-select-option>
                    <a-select-option value="REJ">REJ</a-select-option>
                  </a-select>
                  <span v-else>{{ record.result_ }}</span>
                </template>
                <template slot="action_" slot-scope="text, record, index">
                  <a-tooltip placement="topLeft" title="删除" v-if="editFlg && index != dataSource3.length - 1">
                    <a-icon style="color: #ff9900" type="delete" @click="DeleteRow(record, index)"></a-icon>
                  </a-tooltip>
                </template>
              </a-table>
            </div>
            <div class="rightContentBottom">
              <a-form-model :model="form" layout="inline">
                <a-row class="lastRow">
                  <a-col :span="8">
                    <a-form-model-item label="孔与分孔图" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                      <a-checkbox v-if="editFlg" v-model="form.holeDrillDrawing_" />
                      <a-checkbox v-else :checked="form.holeDrillDrawing_" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item label="孔路与菲林" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                      <a-checkbox v-if="editFlg" v-model="form.conductorAndFilm_" />
                      <a-checkbox v-else :checked="form.conductorAndFilm_" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item label="阻焊与菲林" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                      <a-checkbox v-if="editFlg" v-model="form.solderMaskAndFilm_" />
                      <a-checkbox v-else :checked="form.solderMaskAndFilm_" />
                    </a-form-model-item>
                  </a-col>

                  <a-col :span="8">
                    <a-form-model-item label="字符与菲林" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                      <a-checkbox v-if="editFlg" v-model="form.liginAndFilm_" />
                      <a-checkbox v-else :checked="form.liginAndFilm_" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item label="工程变更" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                      <a-checkbox v-if="editFlg" v-model="form.engineeringChanging_" />
                      <a-checkbox v-else :checked="form.engineeringChanging_" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-form-model>
            </div>
          </div>
        </div>
      </div>
      <a-modal
        title="报告类型选择"
        :visible="dataVisibleMode"
        @cancel="reportHandleCancel"
        @ok="handleOkMode"
        ok-text="导出"
        destroyOnClose
        :maskClosable="false"
        :width="550"
        centered
      >
        <a-form-model-item label="订单号" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
          <a-input v-model="reportdata.orderNo_" allowClear></a-input>
        </a-form-model-item>
        <a-form-model-item label="出货数量" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
          <a-input v-model="reportdata.para4DelQty_" allowClear></a-input>
        </a-form-model-item>
        <a-form-model-item label="周期" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
          <a-input v-model="reportdata.cycle_" allowClear></a-input>
        </a-form-model-item>
        <a-form-model-item label="批次号" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
          <a-input v-model="reportdata.batchNumber_" allowClear></a-input>
        </a-form-model-item>
        <a-form-model-item label="日期" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
          <a-date-picker v-model="reportdata.date_" allowClear format="YYYY-MM-DD " @change="onChange1" />
        </a-form-model-item>
        <a-form-model-item label="SOW 编号" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
          <a-input v-model="reportdata.jz_sow" allowClear></a-input>
        </a-form-model-item>
        <a-form-model-item label="报告类型" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" style="width: 100%; margin: 0">
          <a-select
            v-model="reportdata.reportid"
            placeholder="请选择需要导出的报告"
            style="width: 100%"
            mode="multiple"
            showSearch
            allowClear
            optionFilterProp="lable"
          >
            <a-select-option v-for="(item, index) in reportlist" :key="index" :value="item.value" :lable="item.text">{{ item.text }}</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-modal>
    </a-spin>
  </div>
</template>
<script>
import { upLoadFlyingFile } from "@/services/projectMake";
import moment from "moment";
import { mapState } from "vuex";
import {
  procardwip,
  shipReportList,
  setReportList,
  inspectionreport,
  importinspectionreportv2,
  reportvalue,
  setreportselectvalue,
} from "@/services/scgl/QualityManagement/ShippingReport";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "类别",
    align: "left",
    width: 100,
    ellipsis: true,
    dataIndex: "typeStr",
  },
  {
    title: "描述",
    dataIndex: "description",
    align: "left",
    width: 160,
    ellipsis: true,
  },
  {
    title: "要求",
    dataIndex: "reValue_",
    align: "left",
    width: 160,
    ellipsis: true,
  },
  {
    title: "实际",
    dataIndex: "value_",
    align: "left",
    width: 150,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "value_" },
  },
  {
    title: "结果",
    dataIndex: "result_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "result_" },
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "刀号",
    align: "left",
    width: 60,
    ellipsis: true,
    dataIndex: "toolNumber",
  },
  {
    title: "成品孔径",
    dataIndex: "finishedAperture",
    align: "left",
    width: 70,
    ellipsis: true,
  },
  {
    title: "公差",
    dataIndex: "tolerance",
    align: "left",
    width: 110,
    ellipsis: true,
  },
  {
    title: "属性",
    dataIndex: "attribute",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "实际尺寸1",
    dataIndex: "actual1_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual1_" },
  },
  {
    title: "实际尺寸2",
    dataIndex: "actual2_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual2_" },
  },
  {
    title: "实际尺寸3",
    dataIndex: "actual3_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual3_" },
  },
  {
    title: "实际尺寸4",
    dataIndex: "actual4_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual4_" },
  },
  {
    title: "结果",
    dataIndex: "result_",
    align: "left",
    width: 70,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "result_" },
  },
];
const columns3 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "尺寸",
    align: "left",
    width: 80,
    ellipsis: true,
    dataIndex: "size",
    scopedSlots: { customRender: "size" },
    className: "editSty",
  },
  {
    title: "公差",
    dataIndex: "tolerance",
    align: "left",
    className: "editSty",
    width: 110,
    ellipsis: true,
    scopedSlots: { customRender: "tolerance" },
  },
  {
    title: "实际尺寸1",
    dataIndex: "actual1_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual1_" },
  },
  {
    title: "实际尺寸2",
    dataIndex: "actual2_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual2_" },
  },
  {
    title: "实际尺寸3",
    dataIndex: "actual3_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual3_" },
  },
  {
    title: "实际尺寸4",
    dataIndex: "actual4_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actual4_" },
  },
  {
    title: "结果",
    dataIndex: "result_",
    align: "left",
    width: 70,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "result_" },
  },
  {
    title: "操作",
    fixed: "right",
    align: "center",
    width: 40,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "action_" },
  },
];
export default {
  name: "",
  data() {
    return {
      reportdata: {
        reportid: [],
        orderNo_: "", //订单号
        para4DelQty_: "", //出货数量
        cycle_: "", //周期
        batchNumber_: "", //批次号
        date_: "", //日期
        jz_sow: "",
      },
      reportlist: [],
      spinning: false,
      formData: {
        PinBanNo: "",
      },
      columns,
      columns2,
      columns3,
      orderListTableLoading: false,
      TableLoading2: false,
      TableLoading3: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      StepKeyList: [],
      orderListData: [],
      dataSource1: [],
      dataSource2: [],
      dataSource3: [],
      proOrderId: "",
      selectedRowKeysArray: [],
      selectedRowsData: {},
      dataVisibleMode: false,
      orderno: "",
      messageMode: "",
      id: "",
      modeType: "",
      form: {
        areaID_: "",
        conductorAndFilm_: false,
        engineeringChanging_: false,
        holeDrillDrawing_: false,
        liginAndFilm_: false,
        id: "",
        indate: "",
        keyin: "",
        pdctno_: "",
      },
      editFlg: false,
      sliceSizeImg_: "",
      sliceDrlImg_: "",
      SizefileData: [],
      DrlfileData: [],
    };
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  mounted() {
    if (this.user.factoryId == 22) {
      this.reportlist = [
        { value: 1, text: "COC" },
        { value: 2, text: "出貨檢查報告" },
        { value: 3, text: "孔徑量度記錄" },
        { value: 4, text: "分孔图" },
        { value: 5, text: "尺寸測量記錄" },
        { value: 6, text: "尺寸图" },
        //{ value: 7, text: "尺寸图-2" },
        { value: 8, text: "板厚&V-cut量度記錄" },
        { value: 9, text: "微切片测量记录" },
        { value: 10, text: "绝缘层量度记录" },
        { value: 11, text: "可焊性测试报告" },
        { value: 12, text: "热应力测试报告" },
        { value: 13, text: "電测试报告" },
        { value: 14, text: "离子污染测试" },
      ];
    } else {
      this.reportlist = [
        { value: 1, text: "出货报告" },
        { value: 2, text: "COC报告" },
        { value: 3, text: "检查总表" },
        { value: 4, text: "尺寸测试报告" },
        { value: 5, text: "测试报告" },
        { value: 6, text: "金相切片报告" },
        { value: 7, text: "可焊性报告" },
        { value: 8, text: "阻抗报告" },
      ];
    }
    this.getdata(); //"839B-SFB094751B1"
  },
  methods: {
    moment,
    handlePreview(file) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.indexOf(".jpg") != -1 || file.name.indexOf(".png") != -1 || file.name.indexOf(".jpeg") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("只能上传jpg/png/jpeg文件");
          reject();
        } else {
          resolve();
        }
      });
    },
    handleChangesize({ fileList }, data) {
      this.SizefileData = fileList;
      if (this.SizefileData.length > 2) {
        this.$message.warning("该操作最多上传两张图片！默认按照最新上传图片为准");
        this.SizefileData.splice(0, 1);
      }
      this.sliceSizeImg_ = "";
      let arr = [];
      if (this.SizefileData.length) {
        this.SizefileData.forEach(item => {
          arr.push(item.response);
        });
        this.sliceSizeImg_ = arr.join("|");
      } else {
        this.sliceSizeImg_ = "";
      }
    },
    handleChangedrl({ fileList }, data) {
      this.DrlfileData = fileList;
      if (this.DrlfileData.length > 2) {
        this.$message.warning("该操作最多上传一张图片！默认按照最新上传图片为准");
        this.DrlfileData.splice(0, 1);
      }
      this.sliceDrlImg_ = "";
      if (this.DrlfileData.length) {
        this.sliceDrlImg_ = this.DrlfileData[0].response;
      } else {
        this.sliceDrlImg_ = "";
      }
      console.log(this.sliceDrlImg_, "sliceDrlImg_");
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    inputchange() {
      if (!this.formData.PinBanNo) {
        document.getElementById("pb").focus();
      }
    },
    //获取列表数据
    getdata(orderno) {
      let params = {
        orderno: orderno,
      };
      procardwip(params).then(res => {
        if (res.code) {
          this.dataSource1 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 调出数据
    searchClick() {
      if (!this.formData.PinBanNo) {
        this.$message.warning("请输入编号！");
        return;
      }
      var arr1 = this.formData.PinBanNo.split("");
      if (arr1.length > 30) {
        arr1 = arr1.slice(0, 30);
      }
      this.formData.PinBanNo = arr1.join("");
      this.getdata(this.formData.PinBanNo);
    },
    DeleteRow(row, index) {
      this.dataSource3.splice(index, 1);
    },
    handleBlur(record, index) {
      // 定义需要检查的字段
      const keys = ["size", "tolerance", "actual1_", "actual2_", "actual3_", "actual4_"];
      // 检查是否为最后一行且有任意字段有值
      if (index === this.dataSource3.length - 1) {
        const hasValue = keys.some(key => record[key]);
        if (hasValue) {
          this.dataSource3.push({ no_: Number(index + 1) });
        }
      }
      // 如果不是最后一行且所有字段都为空，则删除该行
      if (index !== this.dataSource3.length - 1) {
        const allEmpty = keys.every(key => !record[key]);
        if (allEmpty) {
          this.dataSource3.splice(index, 1);
        }
      }
    },
    // 获取订单
    getOrderList(queryData) {
      this.spinning = true;
      shipReportList(queryData)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.reportADtos ? res.data.reportADtos : [];
            this.dataSource2 = res.data.reportBDtos ? res.data.reportBDtos : [];
            this.dataSource3 = res.data.reportCDtos ? res.data.reportCDtos : [];
            this.form = res.data.mainDto ? res.data.mainDto : {};
            this.sliceDrlImg_ = this.form.drlImg_;
            this.sliceSizeImg_ = this.form.sizeImg_;
            if (this.form.sizeImg2_) {
              this.sliceSizeImg_ += "|" + this.form.sizeImg2_;
            }
            console.log(this.sliceSizeImg_, "sliceSizeImg_");
            if (this.form.drlImg_) {
              this.DrlfileData = [];
              this.DrlfileData.push({
                uid: 1,
                name: "image.png",
                status: "done",
                url: this.form.drlImg_,
                thumbUrl: this.form.drlImg_, //缩略图地址
                response: this.form.drlImg_,
              });
            }
            if (this.sliceSizeImg_) {
              this.SizefileData = [];
              let imgList = this.sliceSizeImg_.split("|");
              imgList.forEach((item, index) => {
                this.SizefileData.push({
                  uid: index + 1,
                  name: "image.png",
                  status: "done",
                  url: item,
                  thumbUrl: item, //缩略图地址
                  response: item,
                });
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //出货报告
    Shippingreport() {
      if (this.orderListData.length == 0 && this.dataSource2.length == 0 && this.dataSource3.length == 0) {
        this.$message.warning("请先获取数据！");
        return;
      }
      if (this.user.factoryId == 12 || this.user.factoryId == 38 || this.user.factoryId == 22) {
        this.dataVisibleMode = true;
        if (this.user.factoryId == 22) {
          this.$set(this.reportdata, "reportid", [1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14]);
        } else {
          this.$set(this.reportdata, "reportid", [1, 2, 3, 4, 5, 6, 7, 8]);
        }

        setreportselectvalue(this.form.pdctno_).then(res => {
          if (res.code) {
            this.reportdata = res.data;
            if (this.user.factoryId == 22) {
              this.$set(this.reportdata, "reportid", [1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14]);
            } else {
              this.$set(this.reportdata, "reportid", [1, 2, 3, 4, 5, 6, 7, 8]);
            }
            if (!this.reportdata.date_) {
              this.reportdata.date_ = moment(new Date()).format("YYYY-MM-DD");
            }
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        inspectionreport(this.form.pdctno_).then(res => {
          if (res.code) {
            if (res.data != null) {
              this.downloadByteArrayFromString(res.data, res.message);
              this.formData.PinBanNo = "";
              this.proOrderId = "";
              this.getdata();
              this.orderListData = [];
              this.dataSource2 = [];
              this.dataSource3 = [];
              this.form = {};
            } else {
              this.$message.warning("暂无数据 不能导出报告");
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    editClick() {
      if (!this.orderListData.length) {
        this.$message.warning("无可编辑数据！");
        return;
      }
      this.editFlg = true;
      this.dataSource3.push({ no_: Number(this.dataSource3.length + 1) });
    },
    cancelClick() {
      this.editFlg = false;
      this.getOrderList(this.formData.PinBanNo);
    },
    saveClick() {
      if (!this.editFlg) {
        this.$message.warning("非编辑状态不可执行此操作！");
        return;
      }
      this.form.drlImg_ = this.sliceDrlImg_;
      this.form.sizeImg_ = this.sliceSizeImg_?.split("|")[0] || "";
      this.form.sizeImg2_ = this.sliceSizeImg_?.split("|")[1] || "";
      this.dataSource3.pop();
      let params = {
        mainDto: this.form,
        reportADtos: this.orderListData,
        reportBDtos: this.dataSource2,
        reportCDtos: this.dataSource3,
      };
      setReportList(params).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          this.editFlg = false;
          this.getOrderList(this.formData.PinBanNo);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.facNumber && record.facNumber == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.facNumber);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.proOrderId = record.facNumber;
            this.formData.PinBanNo = record.facNumber;
            this.getOrderList(this.proOrderId);
          },
        },
      };
    },
    cellClickEvent({ row, column }) {
      let keys = [];
      keys.push(row.facNumber);
      this.selectedRowKeysArray = keys;
      this.selectedRowsData = row;
      this.proOrderId = row.facNumber;
      this.formData.PinBanNo = row.facNumber;
      this.getOrderList(this.proOrderId);
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      this.pageStat = false;
      localStorage.removeItem("stat");
      this.getOrderList();
    },
    onChange1(value, dateString) {
      this.reportdata.date_ = dateString;
    },
    reportHandleCancel() {
      this.dataVisibleMode = false;
    },
    handleOkMode() {
      if (this.reportdata.reportid.length == 0) {
        this.$message.warning("请选择需要导出的报告类型");
        return;
      }
      let params = {
        orderno: this.formData.PinBanNo,
        Reports: this.reportdata.reportid,
        joinFactoryId: this.user.factoryId,
      };
      let val = { ...this.form, ...this.reportdata };
      delete val.reportid;
      reportvalue(val).then(res => {
        if (res.code) {
          importinspectionreportv2(params).then(res => {
            if (res.code) {
              if (res.data != null) {
                this.downloadByteArrayFromString(res.data, res.message);
                this.formData.PinBanNo = "";
                this.proOrderId = "";
                this.getdata();
                this.orderListData = [];
                this.dataSource2 = [];
                this.dataSource3 = [];
                this.form = {};
              } else {
                this.$message.warning("暂无数据 不能导出报告");
              }
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          this.$message.error(res.message);
        }
      });
      this.dataVisibleMode = false;
    },
  },
};
</script>

<style scoped lang="less">
/deep/.vxe-header--column {
  font-weight: 500;
}
/deep/.vxe-table--render-default {
  position: relative;
  font-size: 14px;
  color: #000000;
  font-weight: 500;
  font-family: sans-serif;
  direction: ltr;
}
/deep/.vxe-table .vxe-table--header-wrapper {
  color: #000000;
  font-family: sans-serif;
}
/deep/.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 0;
}
/deep/.vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default.vxe-editable .vxe-body--column,
.vxe-table--render-default .vxe-footer--column.col--ellipsis,
.vxe-table--render-default .vxe-header--column.col--ellipsis {
  height: 37px;
}
/deep/.vxe-table--render-default .vxe-body--row.row--current {
  background: rgb(223 220 220);
}
/deep/.vxe-table--render-default .vxe-body--row.row--hover {
  background: #dfdcdc !important;
}
/deep/.editSty {
  padding: 0 2px !important;
  .ant-input {
    width: 99% !important;
    margin-top: 0 !important;
    padding: 2px 14px 2px 2px;
  }
  .ant-input-suffix {
    right: 4px !important;
  }
  .ant-select {
    width: 99% !important;
    margin-top: 0 !important;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-input {
  font-weight: 500;
}
.ant-table-row-cell-break-word {
  position: relative;
}
.span {
  position: absolute;
  top: 5%;
}
p {
  margin: 0;
}
.projectBackend {
  /deep/.userStyle {
    user-select: all !important;
  }
  height: 825px;
  min-width: 1670px;
  // width: 100%;
  background: #ffffff;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  .ant-input,
  .ant-select {
    width: 8%;
    margin-right: 0.5%;
    margin-top: 6px;
  }
  .content {
    height: 44px;
    margin-left: 6px;
    padding-top: 5px;
  }
  .table {
    width: 100%;
    // height:738px;
    height: 785px;
    display: flex;
    flex-wrap: nowrap;
    .centerContentBottom {
      height: 172px;
      width: 100%;
      padding: 15px;
      border: 2px solid rgb(233, 233, 240);
      border-top: 4px solid #e9e9f0;
      display: flex;
      align-items: center;
    }
    .centerTable {
      height: 613px;
    }
    .leftContent {
      .minTable {
        /deep/.ant-table-body {
          min-height: 577px;
        }
      }
      height: 100%;
      width: 65%;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
    }
    .rightContent {
      display: flex;
      flex-wrap: wrap;
      width: 35%;
      height: 100%;
      // border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid #e9e9f0;
      .minTable1 {
        /deep/.ant-table-body {
          min-height: 360px;
        }
      }
      .minTable2 {
        /deep/.ant-table-body {
          min-height: 181px;
        }
      }
      .rightContentTop {
        height: 398px;
        width: 100%;
        border: 2px solid rgb(233, 233, 240);
        border-bottom: 0;
      }
      .rightContentCnter {
        height: 218px;
        width: 100%;
        border: 2px solid rgb(233, 233, 240);
        border-top: 4px solid #e9e9f0;
        border-bottom: 0;
        /deep/ .ant-table {
          .ant-table-thead > tr > th {
            padding: 7px 2px;
            border-right: 1px solid #efefef;
            height: 33px;
          }
        }
      }
      .rightContentBottom {
        height: 172px;
        width: 100%;
        border: 2px solid rgb(233, 233, 240);
        border-top: 4px solid #e9e9f0;
      }
    }
  }
  /deep/ .ant-table {
    // min-height: 740px;
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 0;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-selected td {
      background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
      background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220) !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container {
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: rgb(223 220 220);
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 12px 0 0 10px;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-calendar-picker {
  width: 100%;
}
</style>
