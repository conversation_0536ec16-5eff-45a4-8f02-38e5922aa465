<template>
  <a-spin :spinning="spinning">
    <div id="app" class="box" ref="SelectBox" @click="bodyClick">
      <div style="display: flex; justify-content: space-between">
        <div
          style="
            margin-top: 0;
            margin-bottom: 0.5em;
            color: #cf1b26;
            font-weight: 700;
            text-align: left;
            margin-left: 1%;
            float: left;
            font-size: 24px;
          "
        >
          {{ OrderNo }}
        </div>
        <a-button type="primary" @click="reworkbegins" v-if="modu == 'qae'">返修开始</a-button>
      </div>

      <table border style="border-color: #e1e1e2">
        <thead>
          <tr>
            <th>序号</th>
            <th>操作时间</th>
            <th>操作人员</th>
            <th>问题详情</th>
            <th v-if="modu == 'qae'">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr colspan="5" style="position: relative; height: 34px" v-if="modu == 'qae'">
            <span style="line-height: 34px; position: absolute; right: 50%; color: #ff9900; cursor: pointer" @click="addClick">+添加问题</span>
          </tr>
          <template v-for="(item, index) in orderData">
            <tr :key="'1' + index">
              <td style="width: 10%" :rowspan="item.reply ? 2 : 1">
                Q<i>{{ index + 1 }}</i>
              </td>
              <td style="width: 15%">{{ item.indate }}</td>
              <td style="width: 15%">{{ item.inUserName }}</td>
              <td style="width: 45%" class="left">
                <div>
                  <p>问题类型：{{ item.questionType }}</p>
                  <p>问题描述：{{ item.conent }}</p>
                  <div v-if="item.picUrl" v-viewer>
                    <img style="height: 50px; width: 50px; margin-left: 10px" v-for="(ite, index) in item.picUrl" :key="index" :src="ite" />
                  </div>
                  <div v-if="item.fileUrl">
                    <span style="color: red" @click="down(item.fileUrl)">附件（可点击下载）</span>
                  </div>
                </div>
              </td>
              <td style="width: 15%" :rowspan="item.reply ? 2 : 1" v-if="modu == 'qae'">
                <div>
                  <p @click="editClick(item)" style="cursor: default">+编辑</p>
                  <p @click="backClick(item)" style="cursor: default">+删除</p>
                </div>
              </td>
            </tr>
            <tr v-if="item.reply" :key="'2' + index">
              <td style="width: 15%">{{ item.reply.solutionTime }}</td>
              <td style="width: 15%">{{ item.reply.inUserName }}</td>
              <td style="width: 45%" class="left">
                <div>
                  <p style="color: #ff9900">回复内容：{{ item.reply.content }}</p>
                </div>
              </td>
            </tr>
          </template>
          <tr v-if="addTr">
            <td style="width: 10%">
              <p>
                Q<i>{{ orderData.length + 1 }}</i>
              </p>
            </td>
            <td style="width: 15%">
              <p>{{ addTime }}</p>
            </td>
            <td style="width: 15%">
              <p>{{ user }}</p>
            </td>
            <td class="left" style="width: 45%">
              <template>
                <a-form layout="inline" :model="form">
                  <a-row>
                    <a-col :span="12">
                      <a-form-item label="问题类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-select v-model="form.questionType" allow-clear :getPopupContainer="() => this.$refs.SelectBox">
                          <a-select-option v-for="(item, index) in selectData2" :key="index" :value="item.caption_">{{
                            item.caption_
                          }}</a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                    <a-col :span="4">
                      <a-form-item style="margin-left: 20px">
                        <a-button type="primary" @click="keyWords">关键字</a-button>
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="问题描述" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-textarea v-model="form.conent" :rows="5" @blur="changeDescription()" allow-clear />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row style="padding: 10px">
                    <template>
                      <a-upload
                        name="file"
                        accept="image/*"
                        ref="fileRef"
                        :before-upload="beforeUpload1"
                        :customRequest="httpRequest1"
                        :file-list="fileListData"
                        @change="handleChange1"
                        list-type="picture-card"
                        @preview="handlePreview"
                      >
                        <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                        <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(1)" title="点击 ctrl+V粘贴上传">
                          粘贴图片
                        </a-button>
                      </a-upload>
                      <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview" :width="900">
                        <img style="width: 100%; height: 100%" :src="previewpicUrl" />
                      </a-modal>
                    </template>
                    <template>
                      <a-upload
                        name="file"
                        ref="fileRef"
                        :before-upload="beforeUpload2"
                        :customRequest="httpRequest2"
                        :file-list="fileList"
                        @change="handleChange2"
                      >
                        <a-button v-if="fileList.length < 1" style="font-weight: 500"> 上传附件 </a-button>
                      </a-upload>
                    </template>
                  </a-row>
                </a-form>
              </template>
            </td>
            <td style="width: 15%">
              <p><a-button type="primary" @click="uploadclick"> 提交</a-button></p>
              <p><a-button type="primary" @click="cancelClick"> 取消</a-button></p>
            </td>
          </tr>
        </tbody>
      </table>
      <a-modal
        title="关键字索引"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="1100"
      >
        <repair-word ref="RepairWord" :selectData1="selectData2" :StepName1="StepName1"></repair-word>
      </a-modal>
      <!--    编辑问题弹窗-->
      <a-modal
        title="请输入问题描述"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="520"
      >
        <div @click="bodyClick">
          <a-form :model="editForm">
            <a-form-item>
              <a-textarea :rows="5" v-model="editForm.conent" allow-clear></a-textarea>
            </a-form-item>
            <a-form-item style="padding: 10px">
              <template>
                <div v-viewer style="display: flex; width: 100%; flex-wrap: wrap">
                  <img
                    style="width: 100px; height: 100px; margin-bottom: 10px; margin-right: 10px"
                    v-for="(item, index) in editForm.picUrl"
                    :key="index"
                    :src="item"
                    @contextmenu.prevent="delClick(item)"
                  />
                </div>
                <a-upload
                  name="file"
                  accept="image/*"
                  ref="fileRef"
                  :before-upload="beforeUpload1"
                  :customRequest="httpRequest3"
                  :file-list="fileListData3"
                  @change="handleChange3"
                  list-type="picture-card"
                  @preview="handlePreview"
                >
                  <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                  <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(3)" title="点击 ctrl+V粘贴上传">
                    粘贴图片
                  </a-button>
                </a-upload>
                <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview">
                  <img style="width: 100%; height: 100%" :src="previewpicUrl" />
                </a-modal>
              </template>
              <template>
                <a-upload
                  name="file"
                  ref="fileRef"
                  :before-upload="beforeUpload2"
                  :customRequest="httpRequest4"
                  :file-list="fileList4"
                  @change="handleChange4"
                >
                  <a-button v-if="fileList4.length < 1" style="font-weight: 500"> 上传附件 </a-button>
                </a-upload>
              </template>
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </div>
    <a-modal :title="meslist" :visible="dataVisibleNo" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
      <template slot="footer">
        <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
        <a-button @click="reportHandleCancel">取消</a-button>
      </template>
      <div class="class" style="font-size: 16px; font-weight: 500">
        <p v-for="(item, index) in checkData" :key="index">
          <span v-if="item.error == '1'" style="color: red">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}</span>
          </span>
          <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}</span>
          </span>
          <span v-else style="color: black">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}</span>
          </span>
        </p>
        <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
      </div>
    </a-modal>
  </a-spin>
</template>

<script>
import axios from "axios";
import $ from "jquery";
import { mapState } from "vuex";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    // scopedSlots: { customRender: 'num' },
    width: "15%",
  },
  {
    title: "操作时间",
    dataIndex: "solutionTime",
    align: "left",
    ellipsis: true,
    width: "15%",
    className: "userStyle",
  },
  {
    title: "操作人员",
    dataIndex: "inUserName",
    align: "left",
    ellipsis: true,
    width: "15%",
  },
  {
    title: "问题详情",
    // customRender: (text,record,index) => `${record.isReOrder ? '是' : ''}`,
    scopedSlots: { customRender: "content" },
    align: "left",
    ellipsis: true,
    width: "40%",
  },

  {
    title: "操作",
    scopedSlots: { customRender: "action" },
    align: "center",
    // width: 120,   // 2290
  },
];
import { RepairStart } from "@/services/projectQae";
import { ppebuttonCheck } from "@/services/projectMake/index.js";
import { getFixRecord, getClassList, upLoadFixFile1, update1, deleteById, user, addFixRecord } from "@/services/projectMake";
import moment from "moment";
import RepairWord from "@/pages/gongcheng/projectPage/reModule/RepairWord";
export default {
  name: "registerDetails",
  components: { RepairWord },
  data() {
    return {
      meslist: "",
      dataVisibleNo: false,
      check1: false,
      checkData: [],
      checkType: "",
      spinning: false,
      columns1,
      orderData: [],
      addTr: false,
      replyTr: false,
      replyData: [],
      selectData2: [],
      addTime: moment(new Date()).format("YYYY-MM-DD "),
      form: {
        orderNo: "", //订单号
        questionType: "", //问题类型
        conent: "", // 问题描述
      },
      StepName1: "",
      askType: 0, // 问客类型（0：问题确认，1：文件确认）
      picUrl: "", // 编辑上传图片图片返回地址上传文件返回地址
      fileUrl: "", //上传文件返回地址
      dataVisible1: false,
      solution: "", //解决方案
      fileList: [],
      fileListData: [],
      fileListData3: [],
      fileList4: [],
      isFileType: true,
      previewVisible: false,
      arrData: [],
      arrData1: [],
      dataVisible2: false,
      dataVisible3: false,
      editForm: {
        id: "",
        conent: "", // 问题描述
        picUrl: "", // 图片地址
        fileUrl: "", // 文件地址
        askType: 0,
      }, // 编辑问题弹窗数据
      replyForm: {
        Quiz: "",
        id: "",
      },
      previewpicUrl: "",
      eqData: [],
      user: "",
      isMovedown: false,
      startPosition: { x: 0, y: 0, offsetX: 0, offsetY: 0 },
      OrderNo: "",
      modu: "",
      showCopyType: "",
      keyVisible: false,
    };
  },
  watch: {
    askType(newNal, oldval) {
      if (newNal == 1) {
        this.form.conent = "文件确认";
        this.form.proposalA = "文件无误";
        this.form.proposalB = "文件有误";
        this.form.proposalC = "";
      } else {
        this.form.conent = "";
        this.form.proposalA = "";
        this.form.proposalB = "";
        this.form.proposalC = "";
      }
    },
  },

  methods: {
    checkClick() {
      if (this.checkType == "fxks") {
        this.rework();
      }
      this.dataVisibleNo = false;
    },
    keyWords() {
      this.dataVisible1 = true;
      this.StepName1 = this.form.questionType;
    },

    //返修提交全部登记问题
    reworkbegins() {
      ppebuttonCheck(this.$route.query.pid, "FixStart").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData = res.data;
            this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
            this.checkType = "fxks";
            this.dataVisibleNo = true;
            this.meslist = "返修开始按钮检查";
          } else {
            this.rework();
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    rework() {
      this.spinning = true;
      RepairStart({ id: this.$route.query.pid })
        .then(res => {
          if (res.code) {
            this.$message.success("返修开始");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 获取订单问客记录
    getOrderList() {
      this.spinning = true;
      let pid = this.$route.query.pid;
      getFixRecord(pid)
        .then(res => {
          if (res.code) {
            let arrData = [];
            this.orderData = res.data;
            res.data.forEach(item => {
              if (item.picUrl) {
                var a = item.picUrl.split(",");
                item.picUrl = a;
              }
            });
            console.log("this.orderData", this.orderData);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 添加问题
    addClick() {
      this.addTr = true;
      user().then(res => {
        if (res.code) {
          this.user = res.data.realName_;
        }
      });
    },
    // 获取问题下拉选择
    getSelect2() {
      let params = [1119];
      getClassList(params).then(res => {
        if (res.code) {
          this.selectData2 = res.data;
          console.log("this.selectData2", this.selectData2);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    reportHandleCancel() {
      this.dataVisible2 = false;
      this.replyForm.Quiz = "";
      this.fileListData3 = [];
      this.arrData1 = [];
      this.fileList4 = [];
      this.dataVisibleNo = false;
      this.dataVisible1 = false;
    },
    changeDescription() {
      this.$forceUpdate();
    },
    //
    handleOk1() {
      this.dataVisible1 = false;
      this.form.conent = this.$refs.RepairWord.selectData.problemDescription;
      this.form.questionType = this.$refs.RepairWord.selectData.stepName;
    },
    click1() {
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData);
      }
      if (this.isFileType) {
        this.fileListData = fileList;
        let Array = this.fileListData.map(item => item.response);
        this.arrData1 = Array;
        this.picUrl = this.arrData1.toString(",");
        console.log("this.fileListData", this.fileListData);
        if (this.fileListData.length == 0) {
          this.picUrl = "";
        }
      }

      // console.log('this.fileData:',this.fileData)
    },
    handleChange2({ fileList }, data) {
      if (this.isFileType) {
        this.fileList = fileList;
        if (this.fileList.length == 0) {
          this.picUrl = "";
        }
      }

      // console.log('this.fileData:',this.fileData)
    },
    handleChange3({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData3);
      }
      if (this.isFileType) {
        this.fileListData3 = fileList;
        let Array = this.fileListData3.map(item => item.response);
        this.arrData1 = Array;
        if (this.fileListData3.length == 0) {
          this.editForm.picUrl = "";
        }
      }
    },
    handleChange4({ fileList }, data) {
      if (this.isFileType) {
        this.fileList4 = fileList;
        if (this.fileList4.length == 0) {
          this.editForm.picUrl = "";
        }
      }

      // console.log('this.fileData:',this.fileData)
    },
    beforeUpload1(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;

      if (!this.isFileType) {
        this.$message.error("只支持.jpg/.png图片格式文件");
      }
      return this.isFileType;
    },
    beforeUpload2(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;

      if (!this.isFileType) {
        this.$message.error("文件只支持.rar或.zip格式文件");
      }
      return this.isFileType;
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFixFile1(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.arrData.push(res.data);
          this.picUrl = this.arrData.toString(",");
          console.log("新增图", this.picUrl);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest2(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFixFile1(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.fileUrl = res.data;
          this.editForm.picUrl = res.data;
          console.log("新增文件", this.fileUrl);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest3(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFixFile1(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.arrData1.push(res.data);
          console.log("图", this.arrData1);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest4(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFixFile1(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.editForm.fileUrl = res.data;
          console.log("文件", this.editForm.fileUrl);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 点击查看上传图片
    handleCancelPreview() {
      this.previewVisible = false;
    },
    handlePreview(file) {
      this.previewVisible = true;
      this.previewpicUrl = file.response || file.thumbUrl;
    },
    // 提交
    uploadclick() {
      if (!this.form.questionType) {
        this.$message.warning("请选择问题类型");
        return;
      }
      if (!this.form.conent) {
        this.$message.warning("请填写问题内容");
        return;
      }
      let params = this.form;
      console.log("params", params, this.picUrl);
      params.pid = this.$route.query.pid;
      params.inUserName = this.user;
      params.picUrl = this.picUrl;
      params.fileUrl = this.fileUrl;
      console.log("params", params);
      addFixRecord(params).then(res => {
        if (res.code) {
          this.$message.success("提交成功");
          this.fileListData = [];
          this.fileList = [];
          this.arrData = [];
          this.picUrl = "";
          this.fileUrl = "";
          this.solution = "";
          this.addTr = false;
          this.form = {};
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 取消提交
    cancelClick() {
      this.addTr = false;
      this.askType = 0;
      this.arrData = [];
      this.fileListData = [];
      this.fileList = [];
      this.form = {};
      this.picUrl = "";
      this.solution = "";
    },
    // 编辑问题
    editClick(item) {
      this.dataVisible2 = true;
      console.log("item", item);
      this.editForm.id = item.id;
      this.editForm.picUrl = item.picUrl;
      this.editForm.fileUrl = item.fileUrl;
      this.editForm.conent = item.conent;
      this.editForm.proposalA = item.contentA;
      this.editForm.proposalB = item.contentB;
      this.editForm.proposalC = item.contentC;
      this.editForm.askType = item.askType;
    },
    handleOk2() {
      this.dataVisible2 = false;
      let params = this.editForm;
      console.log("params", params, this.editForm.picUrl, this.arrData1);
      params.picUrl = this.editForm.picUrl.concat(this.arrData1).toString(",");
      console.log("params", params);
      update1(params).then(res => {
        if (res.code) {
          this.$message.success("编辑成功");
          this.fileListData3 = [];
          this.arrData1 = [];
          this.fileList4 = [];
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 编辑删除图片
    delClick(item) {
      if (confirm("确认删除该图片？")) {
        this.editForm.picUrl.splice(this.editForm.picUrl.indexOf(item), 1);
        // console.log('this.editForm.imgpicUrl',this.editForm.picUrl)
      }
    },
    // 删除
    backClick(item) {
      if (confirm("确认撤回该问题吗？")) {
        deleteById(item.id).then(res => {
          if (res.code) {
            this.getOrderList();
            this.$message.success("撤回成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 下载工程文件
    down(item) {
      if (item) {
        //window.location.href = item
        let ordermodel1 = item.split("5C").slice(-1)[0].split(".")[0];
        let a = item.split(".").slice(-1)[0];
        const xhr = new XMLHttpRequest();
        xhr.open("GET", item, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = ordermodel1 + "." + a;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      }
    },
    showCopy(type) {
      window.addEventListener("paste", this.getClipboardFiles);
      this.showCopyType = type;
      try {
        navigator.clipboard.read().then(res => {
          const clipboardItems = res;
          if (clipboardItems[0].types[0].indexOf("image") > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then(b => {
              const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, { type: clipboardItems[0].types[0] });
              this.file = files;
              this.getClipboardFiles();
            });
          } else {
            this.$message.error("粘贴内容不是图片");
            return;
          }
        });
      } catch (e) {
        ////console.log('出错了')
      }

      // this.show = true;
      // this.$nextTick(() => { // 监听粘贴事件
      //   document.getElementById('Msg').addEventListener('paste', this.getClipboardFiles);
      // });
    },
    bodyClick() {
      //console.log('bodyClick')
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      //console.log('file',file)
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload1(file); // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload1(file)) {
        // **** return true 之后进行上传
        const formData = new FormData();
        // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append("file", file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSTPpe-fix-record/up-load-fix-file", // 接口地址
          method: "post",
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => {
            if (res.code) {
              //console.log('res',res)
              file.status = "done";
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              //console.log('this.showCopyType',this.showCopyType)
              if (this.showCopyType == "1") {
                this.handleChange1(file, arr);
              } else if (this.showCopyType == "3") {
                this.handleChange3(file, arr);
              }
            } else {
              this.$message.error(data.message || "网络异常,请重试或检查网络连接状态");
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
  },
  mounted() {
    this.getOrderList();
    this.getSelect2();
    this.OrderNo = this.$route.query.OrderNo;
    this.modu = this.$route.query.modu;
  },
  beforeDestroy() {
    window.removeEventListener("paste", this.getClipboardFiles);
  },
};
</script>

<style scoped lang="less">
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  text-align: left;
}
/deep/.ant-input {
  font-weight: 500;
}
.box {
  overflow-y: auto;
  background: white;
  padding: 10px;
}
p {
  margin-bottom: 4px;
}
/deep/ .tab {
  width: 100% !important;
  text-align: center;
}
.left {
  text-align: left !important;
}
#app {
  // font-family: Avenir, Helvetica, Arial, sans-serif;
  //font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}
table {
  width: 100%;
  /*height: 100%;*/
}
</style>
