<!--控制台- 市场分析页 -->
<template>
  <div class="analysis">
    <a-row style="margin-top: 0" :gutter="[24, 24]" :class="{analyHeaderActive:!loading, analyHeader: loading}" >
      <a-col :sm="24" :md="12" :xl="4" class="top">
        <chart-card
          :loading="loading"         
        >
          <div class="newfl">
            <card-pie-new :echartdata="card1Data" el="card1Dom" :loading="loading" minAngle="30"/>
          </div> 
          <div class="newfr">
            <p class="cl-f90">询盘价 {{xpnum + " 件"}}</p>
            <ul>
              <li v-for="(item,index) in card1Data" :key="index">
                <span class="bgspan" :class="'bgspan'+index"></span>{{ item.name}} <br>
                <span style="" class="indentSpan">{{item.value + " 件"}}</span>
              </li>
            </ul>
          </div>          
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="4" class="top">
        <chart-card
          :loading="loading"         
        >
          <div class="newfl">
            <card-pie-new :echartdata="card2Data" el="card2Dom" :loading="loading" minAngle="30"/>
          </div> 
          <div class="newfr">
            <p class="cl-f90">下单量 {{offlinenum + " 件"}}</p>
            <ul>
              <li v-for="(item,index) in card2Data" :key="index">
                <span class="bgspan" :class="'bgspan'+index"></span>{{ item.name}} <br>
                <span style="" class="indentSpan">{{item.value + " 件"}}</span>
              </li>
            </ul>
          </div>          
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="4" class="top">
        <chart-card
          :loading="loading"         
        >
          <div class="newfl">
            <card-pie-new :echartdata="card3Data" el="card3Dom" :loading="loading" minAngle="30"/>
          </div> 
          <div class="newfr">
            <p class="cl-f90">询盘价 {{xpnum + " 件"}}</p>
            <ul>
              <li v-for="(item,index) in card3Data" :key="index">
                <span class="bgspan" :class="'bgspan'+index"></span>{{ item.name}} <br>
                <span style="" class="indentSpan">{{item.value + " 件"}}</span>
              </li>
            </ul>
          </div>          
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="4" class="top">
        <chart-card
          :loading="loading"         
        >
          <div class="newfl">
            <card-pie-new :echartdata="card4Data" el="card4Dom" :loading="loading" minAngle="30"/>
          </div> 
          <div class="newfr">
            <p class="cl-f90">下单量 {{offlinenum + " 件"}}</p>
            <ul>
              <li v-for="(item,index) in card4Data" :key="index">
                <span class="bgspan" :class="'bgspan'+index"></span>{{ item.name}} <br>
                <span style="" class="indentSpan">{{item.value + " 件"}}</span>
              </li>
            </ul>
          </div>          
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="4" class="top">
        <chart-card
          :loading="loading"         
        >
          <div class="newfl">
            <card-pie-new :echartdata="card5Data" el="card5Dom" :loading="loading" minAngle="30"/>
          </div> 
          <div class="newfr">
            <p class="cl-f90">结存量 {{jiecun + " 件"}}</p>
            <ul>
              <li v-for="(item,index) in card5Data" :key="index">
                <span class="bgspan" :class="'bgspan'+index"></span>{{ item.name}} <br>
                <span style="" class="indentSpan">{{item.value + " 件"}}</span>
              </li>
            </ul>
          </div>          
        </chart-card>
      </a-col>
      
    </a-row>
    <a-card
      :loading="loading"
      style="margin-top: 24px;margin-right:24px;"
      :bordered="false"
      :body-style="{ padding: '0px' }"
    >
      <div class="salesCard">
        <a-row>          
          <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24" style="border-right:10px solid  #F0F2F5;padding:10px;width:50%">           
              <table border="1" style="width:100%; color:#000000;">
                <thead>
                  <tr>
                    <td >BP</td>
                    <td>勤茂</td>
                    <td>外销</td>
                    <td>深圳销售</td>
                    <td>勤申</td>
                    <td>勤硕</td>
                  </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in TableData" :key="index">
                  <td>{{ item.bp}}</td>
                  <td>{{ item.kinmao }}</td>
                  <td>{{ item.wx }}</td>
                  <td>{{ item.sz  }}</td>
                  <td>{{ item.kinshen }}</td>
                  <td>{{ item.kinshuo}}</td>
                </tr>
                </tbody>
              </table>
        
            <bar title="工程拼板" :barData="barData" :dayComplatePanel="dayComplatePanel" :pinBanDetaileOrderNum="pinBanDetaileOrderNum"/>
          </a-col>
          <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24" style="width:50%;height:600px;">
            <div class='bodiv'>              
              <h3 >
                总商机达成率：{{bo}}
              </h3>
              <h3>
                新单商机达成率：{{newbo}}
              </h3>
              <h3>
                返单商机达成率：{{rebo}}
              </h3>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script>
// import BarEchart from "./BarEchart"
import ChartCard from "../../../components/card/ChartCard";
import CardPieNew from '../../../components/chart/CardPieNew';
// import CardPieCustom from '../../../components/chart/CardPieCustom';
import Bar from "../../../components/chart/Bar1";
import { newGetCamData, getPanelData,getCamData,orderDataStatistics } from "@/services/analysis";
export default {
  name: "Analysis",
  components: { Bar, ChartCard, CardPieNew,},
  i18n: require("./i18n"),
  data() {
    return {
      card1Data: [], // 面板1数据集合
      xpnum:'',
      card1Total: 0,
      card1TotalArea: 0,
      card2Data: [], // 面板2数据集合
      offlinenum:'',
      card2Total: 0,
      card3Data: [], // 面板2数据集合
      card4Data: [], // 面板4数据集合
      card4Total: 0,
      card4TotalArea: 0,
      card5Data: [], // 面板5数据集合
      jiecun:'',
      card5HeardSort: '', // 面板5头部数据
      card6Data: [], // 面板6数据集合
      card6HeardSort:'', // 面板6头部数据
      loading: true,
      dayComplatePanel: "",
      barData: [],
      rankList: [],
      rankGCList:[],
      pinBanDetaileOrderNum: 0,
      TableData:[
        {
          'bp':'询盘量',
          'kinmao':'',
          'wx':'',
          'sz':'',
          'kinshen':'',
          'kinshuo':'',
        },
        {
          'bp':'下单量',
          'kinmao':'',
          'wx':'',
          'sz':'',
          'kinshen':'',
          'kinshuo':'',
        }
      ],
      bo:'',
      newbo:'',
      rebo:'',
    };
  },
  methods: {
    getCardData() {
      orderDataStatistics().then((res)=>{
        let data=res.data
        let xpnum  = {} // 询单 新/返
        this.xpnum = data.xpnum
        xpnum.xnnewnum = data.xnnewnum  
        xpnum.xnRenum = data.xnRenum
        let offlinenum = {} // 下单 新/返
        this.offlinenum = data.offlinenum
        offlinenum.offlinenewnum = data.offlinenewnum
        offlinenum.offlineRenum = data.offlineRenum
        let xnInnum = {} // 询单 内/外
        xnInnum.xnInnum = data.xnInnum
        xnInnum.xnOutnum = data.xnOutnum
        let offlineInnum = {} // 下单 内/外
        offlineInnum.offlineInnum = data.offlineInnum
        offlineInnum.offlineOutnum = data.offlineOutnum
        let jiecun = {}   // 结存    
        jiecun.prejcnum = data.prejcnum
        jiecun.checkjcnum = data.checkjcnum
        jiecun.dpfacnum = data.dpfacnum
        this.jiecun = Number(data.prejcnum) + Number(data.checkjcnum) + Number(data.dpfacnum) || 0
        this.card1Data = this.card1DataFilter(xpnum) // 面板1数据处理        
        this.card2Data = this.card2DataFilter(offlinenum) // 面板1数据处理
        this.card3Data = this.card3DataFilter(xnInnum) // 面板1数据处理
        this.card4Data = this.card4DataFilter(offlineInnum) // 面板1数据处理
        this.card5Data = this.card5DataFilter(jiecun) // 面板1数据处理
        this.TableData[0].kinmao = data.kinmaoXpnum
        this.TableData[0].wx = data.wxXpnum
        this.TableData[0].sz = data.szXpnum
        this.TableData[0].kinshen = data.kinshenXpnum
        this.TableData[0].kinshuo = data.kinshuoXpnum
        this.TableData[1].kinmao = data.kinmaoxdnum
        this.TableData[1].wx = data.wxxdnum
        this.TableData[1].sz = data.szxdnum
        this.TableData[1].kinshen = data.kinshenxdnum
        this.TableData[1].kinshuo = data.kinshuoxdnum
        this.barData = this.projectEchartDataFilter(this.TableData) 
        console.log('this.barData11',this.barData) 
        this.bo = data.bo
        this.newbo = data.newbo      
        this.rebo = data.rebo
      }).finally(()=>{
        this.loading = !this.loading;
      })     
    },
    card1DataFilter(res){
      return [
        {
          "name": "新单",
          "value": res.xnnewnum||0,
          "area": res.xnnewnum || 0
        },
        {
          "name": "返单",
          "value": res.xnRenum||0,
          "area": res.xnRenum || 0
        },       
      ]

    },
    card2DataFilter(res){
        return [
          {
            "name": "新单",
            "value": res.offlinenewnum || 0,
            "num": res.offlinenewnum|| 0
          },
          {
            "name": "返单",
            "value":res.offlineRenum || 0,
             "num": res.offlineRenum|| 0
          },
        
         
        ]
    },
    card3DataFilter(res){
      return [
        {
          "name": "国内询单量",
          "value": res.xnInnum||0,
          "area": res.xnInnum || 0
        },
        {
          "name": "国外询单量",
          "value": res.xnOutnum || 0,
          "area": res.xnOutnum || 0
        },        
      ]

    },
    card4DataFilter(res){
      return [
        {
          "name": "国内下单量",
          "value": res.offlineInnum || 0,
          "area": res.offlineInnum || 0
        },
        {
          "name": "国外下单量",
          "value": res.offlineOutnum || 0,
          "area": res.offlineOutnum || 0
        },        
      ]

    },    
    card5DataFilter(res){
      return [
        {
          name: "订单预审结存",
          value: res.prejcnum||0,
          autoThroughValue: res.prejcnum||0
        },
        {
          name: "待报价结存",
          value: res.checkjcnum||0,
          autoThroughValue:res.checkjcnum||0
        },
        {
          name: "待派工厂结存",
          value:  res.dpfacnum||0,
          autoThroughValue: res.dpfacnum||0
        },        
      ]
    }, 
    projectEchartDataFilter(arr,keyValue) {
      var xDataArr = ['勤茂','外销','深圳销售','勤申','勤硕'];
      var y1DataArr = [];
      var y2DataArr = [];
      y1DataArr = Object.values(arr[0])
      y2DataArr = Object.values(arr[1])
      y1DataArr.shift()
      y2DataArr.shift()
      console.log('y1DataArr',y1DataArr,y2DataArr)
      return {"xData": xDataArr, 'y1Data': y1DataArr, 'y2Data': y2DataArr};
    },
    Sumsort(val) {
      return function (a, b) {
        var value1 = a[val];
        var value2 = b[val];
        return value2 - value1;
      };
    },
  },
  async created() {
    this.getCardData();
    console.log(this.TableData)
  },
 
};
</script>

<style lang="less" scoped>
/deep/td{
  text-align: center;
}
.salesCard{
  height:600px;
}
.bodiv{
  display: inline-block;
  position: absolute;
  top:37%;
  left:2%;
  /deep/h3{
    color:red;
    font-size: 20px;
    margin-bottom: 20px;
    font-weight:500;
    }
}
.extra-wrap {
  .extra-item {
    display: inline-block;
    margin-right: 24px;
    a:not(:first-child) {
      margin-left: 24px;
    }
  }
}
.analysis{
  height:828px;
  min-width: 1670px;
  .top{
    width:20%;
  }
}
.analysis /deep/.chart-card-content{
  height: 130px!important;
}
.cl-f90{
  color: #f90;
}
  .newfl{
    float: left;
    width: 55%;
  }
  .newfr{
    float: right;
    width: 45%;
    padding-left: 5%;
    // margin-top: -20px;
    p.cl-f90{
      margin-bottom: 5px;
      font-weight: 500;
    }
    .floatUl{
      overflow: hidden;
      li{
        float: left;
        width: 50%;
        color:#000000;
        margin-bottom: 6px;
      }
    }
    p{
      margin-bottom: 0;
    }
    ul{
      padding-left: 0;
      li{
        color:#000000;
        list-style: none;
      }
    }
    .bgspan{
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      vertical-align: middle;
      margin-right: 5px;
    }
    .indentSpan{
      display:inline-block; width:50px;text-indent: 15px;
    }
    .bgspan0{
      background: #FFC977;
    }
    .bgspan1{
      background: #B280FF;
    }
    .bgspan2{
      background: #7EA4FF;
    }
    .bgspan3{
      background: #97E34D;
    }
    .bgspan4{
      background: #1CE297;
    }
    .bgspan5{
      background: rgb(163, 26, 94);
    }
  }
.cardList {
  width: 60%;

  table{
          border-spacing: 0;
          border-collapse: collapse;
          width: 100%;
          margin-top: 5px;
  }
  th{
      border:1px solid #ddd;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      font-weight: 600;
      list-style-type: none;
      padding: 0 2px;
  }
  td{
      border:1px solid #ddd;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      list-style-type: none;
      padding: 0 2px;
  }
  ul {
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 10px;
    li {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      font-weight: 600;
      list-style-type: none;
      span {
        color: #ff9900;
      }
    }
  }
  
}
.analyHeader {
  width: 100%;
  display: flex;
  white-space: nowrap;
  overflow: hidden;
}
.analyHeaderActive {
  width: 100%;
  display: flex;
  overflow-y: hidden;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-user-select:none; 
  -moz-user-select:none; 
  -ms-user-select:none; user-select:none;
   &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    // -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background-color: #ff9900;
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    //-webkit-box-shadow: 0;
    border-radius: 1px;
    background: #F0F0F0;
  }
}
.salesClass {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  font-weight: 500;
  line-height: 1.5;
  background: #fff;
  border-radius: 2px;
}
@media screen and (max-width: 992px) {
  .extra-wrap .extra-item {
    display: none;
  }
}
@media screen and (max-width: 576px) {
  .extra-wrap {
    display: none;
  }
}
</style>