<template>
  <a-table
      :columns="columns"
      :dataSource="dataSource"      
      :customRow="onClickRow"
      :pagination="pagination"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      @change="handleTableChange"
      :rowClassName="isRedRow"
      bordered
      :row-selection="{
        selectedRowKeys: selectedRowKeysArray,
        onChange: onSelectChange,
        type: 'radio',
      }"
      
  >

    <span slot="num" slot-scope="text, record, index">
        {{(pagination.current-1)*pagination.pageSize+parseInt(index)+1}}
      </span>
    <template slot="orderNo" slot-scope="text,record">
      <a style="color: #0000CC" @click.stop="goDetail(record)">{{record.orderNo}}</a>
    </template>
    <template slot="tag" slot-scope="text, record">
      <a-tag
          color="#2D221D"
          style="font-size: 12px; color:#ff9900;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;"
          v-if="record.isHighQuality!= 0"
      >
        {{record.isHighQuality == 1 ? '优' :'精'}}
      </a-tag>

      <a-tag
          v-if="record.isLock"
          color="#2D221D"
          style="font-size: 12px; color:#ff9900;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;"
      >
        锁
      </a-tag>

      <a-tag
          v-if="record.smtFactoryId > 0"
          color="#2D221D"
          style="font-size: 12px; color:#ff9900;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;"
      >
        贴
      </a-tag>
      <a-tag
          v-if="record.confirmWorkingDraft"
          color="#2D221D"
          style="font-size: 12px; color:#ff9900;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;"
      >
        确
      </a-tag>
      <a-tag
          v-if="record.isBigCus"
          color="#2D221D"
          style="font-size: 12px; color:#ff9900;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;"
      >
        KA
      </a-tag>
      <a-tag
          v-if="record.isJiaji"
          color="#2D221D"
          style="font-size: 12px; color:#ff9900;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;"
      >
        急
      </a-tag>
    </template>
    
  </a-table>
</template>

<script>
import {checkPermission} from "@/utils/abp";
export default {
  props:{
    dataSource: {
      type: Array,
      require: true,
      default: () => []
    },
    orderListTableLoading: {
      type: Boolean,
      require: true
    },
    columns:{
      type: Array,
      require: true
    },
    pagination:{
      type: Object,
      require:true,
      default: ()=>{
        return {          
          pagination:{
            current: 1,
            pageSize: 25,        
            showTotal: (total) => `总计 ${total} 条`,            
            total: 0
          },
          selectedRows:{},
        }
      }
    },
    rowKey:{
      type: String,
      require:true
    },
  },
  name: "LeftTableMake",
  data() {
    return {
      selectedRowKeysArray: [],
      selectedRowsData:[],
      activeClass:'smallActive', 
    }
  },
  watch:{
    'pagination':{
      handler(val){
        console.log(val)
      }
    }
  },
  created() {
    // console.log('dataSource',this.dataSource)
  },
  methods: {
    checkPermission,
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysArray = selectedRowKeys;
      this.selectedRowsData = selectedRows[0]
    },
    isRedRow(record){
      if (record.isReOrder == 1 || record.status == 24) {
        return 'fontRed'
      }      
    },    
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record
            this.$emit('getOrderDetail', record)
            this.$emit('getJobInfo', record.id)
            console.log('this.selectedRowKeysArray',this.selectedRowKeysArray)           
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
        }
      }
    },
    handleTableChange(pagination){
      this.pageStat=false
      this.$emit('tableChange', pagination)
    },
    // 退单
    ChargebackClick(record){
      this.$emit('ChargebackClick',record)
    },
    // 查看日志
    viewLogClick(record){
      this.$emit('viewLogClick', record)    
    },
    // 编辑参数
    EditParametersClick(record){
      this.$emit('EditParametersClick',record)
    },
    // 跳转订单详情
    goDetail(record,){
      let keys = [];
      keys.push(record.id);
      this.onSelectChange(keys,record)
      this.$router.push({path:'orderDetailEX',query:{ id:record.mbId,} })
    },
    down(record){
      if(record.pcbFilePath ){
        window.location.href = record.pcbFilePath 
      }
    }
  },
  mounted() {
  }
}
</script>


<style lang="less" scoped>
/deep/ .ant-table {
  .fontRed {
    td {
      color: #DC143C;
    }
  }  
  .displayFlag{
    display: none;
  }
}

</style>