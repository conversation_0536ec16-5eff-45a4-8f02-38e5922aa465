<!--客诉定性-->
<template>
    <a-spin :spinning="spinning">  
    <div class="qualitative">
        <a-tabs  :activeKey="activeKey"  @tabClick="tabClick">
            <a-tab-pane key="0" tab="公共池"> 
            </a-tab-pane>
            <a-tab-pane key="10" tab="个人池">
            </a-tab-pane>
            <a-tab-pane key="20" tab="已定性池">                
            </a-tab-pane>
        </a-tabs>
        <div style="display: flex;padding-bottom: 7px;">
            <a-input placeholder="请输入订单编号/客户编号" style="width: 250px;margin-left: 15px;" 
            @keydown.enter.native="searchclick()" v-model="searchdata.CommonOrderNo"></a-input>
            <a-button type="primary" style="margin-left: 15px;" @click="searchclick"><a-icon type="search"></a-icon>查询</a-button>
        </div>
        <public-pools 
        :Complaintdata="Complaintdata" 
        :activeKey="activeKey" 
        :pagination="pagination"
        @send="send"
        :columns="columns"
        @Pickuptheorder="Pickuptheorder"
        @tableChange="handleTableChange"/>
        <div class="footerAction" style='user-select:none;margin-left: -2px;'>
          <div>
            <!-- <a-button type="primary" class="box">
                取单
            </a-button>  -->
          </div>             
        </div>
    </div>
</a-spin>
</template>
<script>
const columns = [
{
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 65,
    ellipsis:true,
    fixed: 'left',
},   
{
    title: "客户编号",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width:100,       
},
{
    title: "订单编号",
    dataIndex: "orderNo",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "生产型号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,   
    width:200,   
},
{
    title: "状态",
    dataIndex: "statusStr",
    align: "left",
    ellipsis: true,   
    width:100,   
},
{
    title: "联系人",
    dataIndex: "contactName",
    align: "left",
    ellipsis: true,
    width:100,       
},
{
    title: "联系电话",
    dataIndex: "contactNumber",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "投诉日期",
    dataIndex: "complaintDate",
    align: "left",
    ellipsis: true,
    width:180,       
},
{
    title: "客户要求",
    dataIndex: "takeStepsStr",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "8D报告",
    align: "center",
    customRender: (text,record,index) => `${record.need8DReport?'是':'否'}`,
    ellipsis: true,
    width:60,       
},
{
    title: "问题描述",
    dataIndex: "problemDescription",
    align: "left",
    ellipsis: true,
    width:200,       
},
{
    title: "文件下载",
    align: "center",
    ellipsis: true,
    width:100,       
    scopedSlots:{customRender:'filedown'}
},
{
    title: "操作",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:100,       
    scopedSlots:{customRender:'operation'}
},
];
import {custcomplaintspagelist,getwaitverdictorder,qualitativeasyncv2} from "@/services/complaint/QualitativeComplaint.js";
import PublicPools from "@/pages/complaint/QualitativeComplaint/module/PublicPools";
export default {
    name:'QualitativeComplaint',
    components:{PublicPools},
    data(){
        return{
            columns,
            activeKey:'0',
            Complaintdata:[],
            spinning:false,
            searchdata:{},
            pagination: {
                pageSize: 20,
                current: 1,
                total:0,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: [ "20", "50", "100"],//每页中显示的数据
                showTotal: (total) => `总计 ${total} 条`,
            },
        }
    },
    created(){
    },
    mounted(){
        let activeKey = localStorage.getItem('tabkey')
        if(activeKey){
            this.activeKey = activeKey
        }
        this.getdata({status:this.activeKey})
    },
    methods:{
        searchclick(){
            if(JSON.stringify(this.searchdata)=='{}'){
                this.$message.error('请输入查询条件')
                return
            }
            let params = {...this.searchdata,...{status:this.activeKey}}
            this.getdata(params)
        },
        tabClick(key){
            this.activeKey = key
            this.searchdata={}
            this.getdata({status:this.activeKey})
        },
        //发送
        send(val){
            qualitativeasyncv2(val.id).then(res=>{
                if(res.code){
                    this.$message.success('发送成功')
                    this.activeKey = '10'
                    this.getdata({status:this.activeKey})
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        //取单
        Pickuptheorder(val,inde){
            getwaitverdictorder(val.id).then(res=>{
                if(res.code){
                    this.$message.success('取单成功')
                    this.activeKey = '10'
                    this.getdata({status:this.activeKey})
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        getdata(val){
            this.spinning=true
            let params = {
            'PageIndex': this.pagination.current,
            'PageSize' : this.pagination.pageSize,        
            }  
        params = {...params,...val}
        if(params.status=='20'){
            params.statusList=[20,30,40,50,60,100]
        }
        params.status=Number(params.status)
        custcomplaintspagelist(params).then(res=>{
            if(res.code){
                this.Complaintdata =res.data.items
            }else{
                this.$message.error(res.message)
            }
        }).finally(() => {
            this.spinning = false;
            localStorage.removeItem('tabkey')
        });
        },
          // 订单表变化change
        handleTableChange(pagination, filters, sorter) {
            this.pagination.current=pagination.current
            this.pagination.pageSize=pagination.pageSize
            if(JSON.stringify(this.searchdata)=='{}'){
                this.getdata({status:this.activeKey})
            }else{
                let params = {...this.searchdata,...{status:this.activeKey}}
                this.getdata(params)
            }
        },  
    }
}

</script>
<style lang="less" scoped>
.qualitative{
    height: 100%;
    background: white;
/deep/ .ant-table .ant-table-tbody > tr > td {
    height: 33px;
}
}
.footerAction {
    width: 100%;
    height:46px;
    background: white;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    .box{
        float: right;
        margin-top: 6px;
        margin-right:10px;
        width: 90px;
    }
}
</style>