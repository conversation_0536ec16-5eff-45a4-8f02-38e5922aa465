import { openDB } from "idb";

class IndexedDB {
  constructor() {
    this.dbName = "FactoryDataDB";
    this.storeNames = {
      coreListDataMkt: "coreListDataMkt",
      coreListDataPro: "coreListDataPro",
    };
    this.version = 1;
    this.dbPromise = this.initDB();
  }

  async initDB() {
    return openDB(this.dbName, this.version, {
      upgrade(db) {
        // 创建两个对象存储空间（类似表）
        if (!db.objectStoreNames.contains("coreListDataMkt")) {
          db.createObjectStore("coreListDataMkt", { keyPath: ["token", "factory", "mode"] });
        }
        if (!db.objectStoreNames.contains("coreListDataPro")) {
          db.createObjectStore("coreListDataPro", { keyPath: ["token", "factory", "mode"] });
        }
      },
    });
  }

  // 通用存储方法
  async setItem(storeName, { token, factory, mode, data }) {
    const db = await this.dbPromise;
    const tx = db.transaction(storeName, "readwrite");
    await tx.store.put({ token, factory, mode, data });
    return tx.done;
  }

  // 通用获取方法
  async getItem(storeName, { token, factory, mode }) {
    const db = await this.dbPromise;
    return db.get(storeName, [token, factory, mode]);
  }
  //清除所有缓存
  async clearStore(storeName) {
    const db = await this.dbPromise;
    const tx = db.transaction(storeName, "readwrite");
    await tx.store.clear();
    return tx.done;
  }
}

export default new IndexedDB();
