<template>
    <a-card>
        <div>
            <a-alert message="在浮层中可以选择或者输入日期。" type="success" class='alert'/>
            <a-date-picker @change="onChange" />
            <p class='marginRight'></p>
            <a-month-picker placeholder="Select month" @change="onChange" />
            <p class='marginRight'></p>
            <a-range-picker @change="onChange" />
            <p class='marginRight'></p>
            <a-alert message="禁用状态" type="success" class='alert'/>
            <a-week-picker placeholder="Select week" @change="onChange" disabled />
            <a-alert message="可用 disabledDate 和 disabledTime 分别禁止选择部分日期和时间，其中 disabledTime 需要和 showTime 一起使用。" type="success" class='alert'/>
            <a-date-picker
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                :disabled-time="disabledDateTime"
                :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                />
            <p class='marginRight'></p>
            <a-month-picker :disabled-date="disabledDate" placeholder="Select month" />
            <p class='marginRight'></p>
            <a-range-picker
            :disabled-date="disabledDate"
            :disabled-time="disabledRangeTime"
            :show-time="{
                hideDisabledOptions: true,
                defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('11:59:59', 'HH:mm:ss')],
            }"
            format="YYYY-MM-DD HH:mm:ss"
            />
        </div>
    </a-card>
</template>
<script>
import moment from 'moment';
export default({
    methods: {
        onChange(date, dateString) {
          console.log(date, dateString);
        },
        moment,
        range(start, end) {
        const result = [];
        for (let i = start; i < end; i++) {
            result.push(i);
        }
        return result;
        },

        disabledDate(current) {
        // Can not select days before today and today
        return current && current < moment().endOf('day');
        },

        disabledDateTime() {
        return {
            disabledHours: () => this.range(0, 24).splice(4, 20),
            disabledMinutes: () => this.range(30, 60),
            disabledSeconds: () => [55, 56],
        };
        },

        disabledRangeTime(_, type) {
        if (type === 'start') {
            return {
            disabledHours: () => this.range(0, 60).splice(4, 20),
            disabledMinutes: () => this.range(30, 60),
            disabledSeconds: () => [55, 56],
            };
        }
        return {
            disabledHours: () => this.range(0, 60).splice(20, 4),
            disabledMinutes: () => this.range(0, 31),
            disabledSeconds: () => [55, 56],
        };
    },
    }
})
</script>
<style lang="less" scoped>
.marginRight {
    margin-right:10px
}
.alert {
    margin-bottom:10px;
    margin-top: 10px
}
</style>
