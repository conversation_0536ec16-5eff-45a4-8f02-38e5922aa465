<!-- 工具管理- 开料拼版-材料设置 -->
<template>
  <a-table
      :rowKey="(record, index) => {
          return index;
        }"
      :columns="columns"
      :data-source="tableData"
      :pagination="false"
      :loading="materialLoading"
      :scroll="{ y: 450 }"
  >
         <span slot="isTwin" slot-scope="text, record">
            <a-checkbox v-model="record.isTwin_" disabled>
            </a-checkbox>
          </span>
         <span slot="isCalc" slot-scope="text, record">
            <!-- <a-checkbox v-model="record.isCalc_" @change="update($event,record)"> </a-checkbox> -->
            <a-checkbox v-model="record.isCalc_" > </a-checkbox>
          </span>
  </a-table>
</template>
<script>
const columns = [
  {
    title: '长',
    dataIndex: 'sheetLen',
    width: '4%',
    align:'center'
  },
  {
    title: '宽',
    dataIndex: 'sheetWidth',
    width: '4%',
    align:'center'
  },
  {
    title: '双辅料',
    dataIndex: 'isTwin_',
    width: '4%',
    align:'center',
    scopedSlots: { customRender: 'isTwin' }
  },
  {
    title: '计算',
    dataIndex: 'isCalc_',
    width: '4%',
    align:'center',
    scopedSlots: { customRender: 'isCalc' }
  },

]
import { updateMaterialData } from "@/services/cutting"

export default {
  name: "materialSetting",
  props: {
    tableData:{
      type: Array
    },
    materialSettingVisible:{
      type: Boolean,
      default: false
    },
    materialLoading:{
      type: Boolean,
    }
  },
  data(){
    return {
      columns,
    }
  },
  methods:{
    materialSetting(){
      this.MaterialSetting = true
    },
    update(e,record){
      let params = {
        "id": record.id,
        "isCalc_": e.target.checked
      }
      updateMaterialData(params).then(res => {
        if (res.code == 1) {
          this.$message.success("修改成功")
        } else {
          this.$message.error('不存在材料数据，请核实');
        }
        console.log(res)
      })
    }

  },

}
</script>

<style scoped>


</style>
