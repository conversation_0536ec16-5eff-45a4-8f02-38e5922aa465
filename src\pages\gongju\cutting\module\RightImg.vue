<!-- 工具管理- 开料拼版-右边图表 -->
<template>
  <div class="rightImg">
    <a-collapse v-model="activeKey" :bordered="false" :expandIconPosition="'right'">
      <a-collapse-panel key="2" header="大料图">
        <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!imgData.sheetCut"/>
          <div class='img-box'  v-else>
            <img :src="' data:image/png;base64,'+ imgData.sheetCut" data-name="sheetCut" :class="[{'active':targetDom=='sheetCut'}]" @click="sendImg($event,imgData.sheetCut)"/>
          </div>
        </a-spin>
      </a-collapse-panel>
      <a-collapse-panel key="1" header="A板图">
        <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!imgData.aPnl"/>
          <div class='img-box' v-else >
            <img :src="' data:image/png;base64,'+ imgData.aPnl" data-name="aPnl"  :class="[{'active':targetDom=='aPnl'}]" @click="sendImg($event,imgData.aPnl)"/>
          </div>
        </a-spin>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="B板图">
        <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!imgData.bPnl"/>
          <div class='img-box' v-else>
            <img :src="' data:image/png;base64,'+ imgData.bPnl" data-name="bPnl" :class="[{'active':targetDom=='bPnl'}]" @click="sendImg($event,imgData.bPnl)"/>
            <!-- <img :src="' data:image/svg+xml;base64,'+ imgData.bPnl" data-name="bPnl" :class="[{'active':targetDom=='bPnl'}]" @click="sendImg($event,imgData.bPnl)"/> -->
          </div>
        </a-spin>
      </a-collapse-panel>
      <a-collapse-panel key="4" header="SET图">
        <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!imgData.tbPnl"/>
          <div class='img-box' v-else>
            <img :src="' data:image/png;base64,'+ imgData.tbPnl" data-name="SPnl"  :class="[{'active':targetDom=='SPnl'}]" @click="sendImg($event,imgData.tbPnl)"/>
          </div>
        </a-spin>
      </a-collapse-panel>
      <template #expandIcon="props">
        <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0"/>
      </template>
    </a-collapse>
  </div>
</template>

<script>
export default {
  name: "RightImg",
  props:{
    imgData:{
    },
    imageLoading: {
      type: Boolean
    },
    TaoBan:{
      type: Boolean
    },
    ABcutting:{
      type: Boolean
    }
    
  },
  data() {
    return {
      activeKey: ['1','2','3','4'],
      targetDom:'aPnl'
    };
  },
  methods:{
    sendImg(event,url){      
      if(this.targetDom != event.target.getAttribute('data-name')) {
         this.$emit('updateImgUrl',url,event.target.getAttribute('data-name'))
      }
      this.targetDom = event.target.getAttribute('data-name')
      console.log('this.targetDom',this.targetDom)
    
    },
  },
  watch: {
    activeKey(key) {
      console.log('key',key);
    },
  },
  mounted(){
    console.log('tbPnl',this.imgData)
  }
}
</script>

<style scoped lang="less">
.rightImg {
  /deep/ .ant-collapse {
    background: #FFFFFF;
    .ant-collapse-item {
      border: 0;
      .ant-collapse-header {
        padding: 10px ;
        color: rgba(51, 51, 51, 1);
        font-size: 14px;
        font-weight: 500;
      }
      .ant-collapse-content {
        .ant-collapse-content-box {
          height: 25%;
          padding: 10px;
        }

      }
    }
  }
}
.img-box{
  width:100px;
  height:130px;
  margin:0 auto;
}
img{
  width:100%;
  height:100%;
  padding:4px;
}
.active{
  border:2px solid #FF9900;
}



</style>