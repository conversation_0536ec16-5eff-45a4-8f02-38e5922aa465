<!-- 系统管理-脚本管理-查询 -->
<template>
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="主机名称">
      <a-input   v-model='queryForm.pcName' placeholder="请输入主机名称" />
    </a-form-item>
  </a-form>
</template>

<script>
export default {
    name:'QueryInfo',
    props:[],
  data() {
    return {
      queryForm:{
        pcName:''
      },
      autoFocus:true
    };
  },
  cerated(){
  },
  methods: {

  },
};
</script>
<style scoped lang="less">
/deep/.ant-input{
  font-weight: 500;
}
</style>
