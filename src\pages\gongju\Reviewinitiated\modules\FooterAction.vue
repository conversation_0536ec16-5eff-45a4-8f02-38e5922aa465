<!-- 工具模块 - 评审发起 - 按钮 -->
<template>
  <div class="active" ref="active">
    <div
      class="box"
      v-if="checkPermission('MES.ToolModule.ReviewStart.ReviewStartSearch')"
      :class="checkPermission('MES.ToolModule.ReviewStart.ReviewStartSearch') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ToolModule.ReviewStart.ReviewStartEdit')"
      :class="checkPermission('MES.ToolModule.ReviewStart.ReviewStartEdit') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="editClick"> 编辑 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ToolModule.ReviewStart.ReviewStartCheck')"
      :class="checkPermission('MES.ToolModule.ReviewStart.ReviewStartCheck') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="checkClick"> 审核 </a-button>
    </div>
    <!-- <div
      class="box"
      v-if="checkPermission('MES.ToolModule.ReviewStart.ReviewStartDelete')"
      :class="checkPermission('MES.ToolModule.ReviewStart.ReviewStartDelete') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="deleteClick"> 删除 </a-button>
    </div> -->
    <!-- <div
      class="box"
      v-if="checkPermission('MES.ToolModule.ReviewStart.ReviewStartBack')"
      :class="checkPermission('MES.ToolModule.ReviewStart.ReviewStartBack') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="returnClick"> 退回 </a-button>
    </div> -->

    <div
      class="box"
      v-if="checkPermission('MES.ToolModule.ReviewStart.ReviewStartFinish')"
      :class="checkPermission('MES.ToolModule.ReviewStart.ReviewStartFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="finishClick"> 完结 </a-button>
    </div>
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px; width: 100px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
            <a-menu-item @click="addClick">添加</a-menu-item>
            <a-menu-item @click="editClick">编辑</a-menu-item>
            <a-menu-item @click="checkClick">审核</a-menu-item>
            <a-menu-item @click="deleteClick">删除</a-menu-item>
            <a-menu-item @click="finishClick">完结</a-menu-item>
            <a-menu-item @click="returnClick">退回</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "MakeAction",
  props: {
    total: {
      type: Number,
    },
  },
  data() {
    return {
      advanced: false,
      width: 762,
      showBtn: false,
      nums: "",
      buttonsmenu: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      this.nums = num;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      this.handleResize();
      window.addEventListener("resize", this.handleResize, true);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    queryClick() {
      this.$emit("queryClick");
    },
    addClick() {
      this.$emit("addClick");
    },
    editClick() {
      this.$emit("editClick");
    },
    deleteClick() {
      this.$emit("deleteClick");
    },
    returnClick() {
      this.$emit("returnClick");
    },
    finishClick() {
      this.$emit("finishClick");
    },
    checkClick() {
      this.$emit("checkClick");
    },
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      let num = "";
      if (!this.advanced && this.showBtn) {
        num = 8 * 104;
      } else {
        num = (elements.length + 1) * 104;
      }
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      console.log(paginnum * 25 + 200 + num, window.innerWidth - 150, num / 104);
      if (paginnum * 25 + 200 + num > window.innerWidth - 150) {
        this.buttonsmenu = true;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
      } else {
        this.buttonsmenu = false;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      this.nums = elements.length;
      if (this.advanced) {
        width_ = 1000;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
      this.handleResize();
    },
    checkPermission,
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-btn {
  padding: 0;
}
.active {
  // width:762px;
  // display: flex;
  // float: right;
  // flex-wrap: wrap;
  // justify-content: space-around;
  // transition: all .2s;
  // -moz-transition: all .2s;
  // -webkit-transition: all .2s;
  // -o-transition: all .2s;
  // padding: 0 30px;
  height: 100%;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    width: 90px;
    margin-top: 11px;
    // margin-right: 20px;
    text-align: center;

    .ant-btn {
      width: 90%;
    }
    .ant-btn-primary:hover {
      background-color: #ffb029 !important;
      border-color: #ffb029 !important;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        // background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
