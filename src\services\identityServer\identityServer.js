import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
// const BASE_URL = process.env.VUE_APP_API_MAIN_URL
export async function getTypes(params) {
    return request("/api/identity-server/identity-resources", METHOD.GET, transformAbpListQuery(params))
}

export async function createResource(params) {
    return request("/api/identity-server/identity-resources/create-standard-resources", METHOD.POST, params)
}
export async function del(id) {
    return request(`/api/identity-server/identity-resources?id=` + id, METHOD.DELETE)
}
export async function createIdentity(params) {
    return request("/api/identity-server/identity-resources", METHOD.POST, params)
}

export async function edit(id) {
    return request(`/api/identity-server/identity-resources/${id}`, METHOD.GET)
}
export async function editHandle(id,params) {
    return request(`/api/identity-server/identity-resources/${id}`, METHOD.PUT,params)
}

//api资源
export async function getApi(params) {
    return request("/api/identity-server/api-resources", METHOD.GET, transformAbpListQuery(params))
}

export async function delApi(id) {
    return request(`/api/identity-server/api-resources?id=` + id, METHOD.DELETE)
}
export async function createApi(params) {
    return request("/api/identity-server/api-resources", METHOD.POST, params)
}

export async function editApi(id) {
    return request(`/api/identity-server/api-resources/${id}`, METHOD.GET)
}

export async function editApiHandle(id,params) {
    return request(`/api/identity-server/api-resources/${id}`, METHOD.PUT,params)
}

//ApiScopes

export async function getScopes(params) {
    return request(`/api/identity-server/apiScopes`, METHOD.GET, transformAbpListQuery(params))
}

export async function createApiScope(params) {
    return request("/api/identity-server/apiScopes", METHOD.POST, params)
}

export async function delApiScope(id) {
    return request(`/api/identity-server/apiScopes?id=` + id, METHOD.DELETE)
}

export async function editApiScope(id) {
    return request(`/api/identity-server/apiScopes/${id}`, METHOD.GET)
}

export async function editApiScopeHandle(id,params) {
    return request(`/api/identity-server/apiScopes/${id}`, METHOD.PUT,params)
}
export default {
    createResource,
    del,
    getTypes,
    createIdentity,
    edit,
    editHandle,
    getApi,
    delApi,
    createApi,
    editApi,
    editApiHandle,
    getScopes,
    createApiScope,
    delApiScope,
    editApiScope,
    editApiScopeHandle
}
