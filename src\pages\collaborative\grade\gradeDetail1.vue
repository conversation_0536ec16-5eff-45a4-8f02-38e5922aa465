<!-- 供应链 - 品类详情 -->
<template>
<div class="gradeDetails">
  <a-card :loading="loading">
    <div class="firft">
      <table border="1">
        <thead>
        <tr>
          <td>{{name}}板工厂数量</td>
        </tr>
        </thead>
        <tbody>
        <tr>
          <td style="font-weight: 500;">{{ this.count1 }}</td>
        </tr>
        </tbody>
      </table>
    </div>
  </a-card>
  <div class="second">
    <a-card :loading="loading" title="品级分布">
      <div class="sc_left">
        <table border="1">
          <thead>
            <tr>
              <td>品级</td>
              <td>工厂数量</td>
              <td>总占比（%)</td>
            </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in data.orderLevel || []"  :key="index">
            <td>{{ item.category || '' }}</td>
            <td>{{ item.factoryNum || 0 }}</td>
            <td>{{ item.proportion || 0 }}</td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="sc_right">
        <pie-chart :el="'sc_echart'" :echartdata="data.orderLevel | areaData"/>
      </div>
    </a-card>
  </div>
  <div class="second">
    <a-card :loading="loading" title="品类分布">
      <div class="sc_left">
        <table border="1">
          <thead>
          <tr>
            <td>订单类型</td>
            <td>工厂数量</td>
            <td>占比（%)</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in data.orderType || []" :key="index">
            <td>{{ item.category || '' }}</td>
            <td>{{ item.factoryNum || 0 }}</td>
            <td>{{ item.proportion || 0 }}</td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="sc_right">
        <pie-chart :el="'th_echart'" :echartdata="data.orderType | areaData"/>
      </div>
    </a-card>
  </div>
  <div class="third">
    <a-card :loading="loading" title="产能负荷">
      <div class="th_left">
        <a-table :columns="columns" 
        :data-source="data.capacityLoad || []" 
        :pagination="false" rowKey="id"   
        :scroll="{ y: 258 }" 
        :class="data.capacityLoad.length ? 'mintable':''"
        bordered
        ></a-table>
      </div>
      <div class="th_right">
        <bar-chart :barData="data.capacityLoad | th_barData" el="'th_echart'" :scroll="true"  :echartHeight='294'/>
      </div>
    </a-card>
  </div>

   <div class="fourth">
    <a-card :loading="loading" title="工厂分布">
      <div class="th_left">
        <a-table 
        :columns="columns2"
         :data-source="data.factoryDistribution || []" 
         :pagination="false" 
         rowKey="id"   
         :scroll="{ y: 258 }"
         :class="data.factoryDistribution.length ? 'mintable1':''"
          bordered
          ></a-table>
      </div>
    </a-card>
  </div>
  <div class="fifth">
    <a-card :loading="loading" title="工厂评级">
      <div class="th_left">
<!--        <a-table :columns="columns3" :data-source="data.factoryRating || []" :pagination="false" rowKey="id"   :scroll="{ y: 300 }" bordered>-->
<!--          <template slot="factoryCode_" slot-scope="text,record"><a @click = "details(record.id)">{{text}}</a></template>-->
<!--          <template slot="action" slot-scope="text,record"><a @click = "details(record.id)">点击前往</a></template>-->
<!--        </a-table>-->
        <a-table
            :columns="columns3"
            :data-source="data.factoryRating || []"
            :pagination="false"
            rowKey="id"
            :scroll="{ y: 264 }"
            bordered
            :class="data.factoryRating.length ? 'mintable1':''"
        >
          <template slot="factoryCode_" slot-scope="text,record"><a @click = "details(record.id)">{{text}}</a></template>-->
        </a-table>
      </div>
    </a-card>
  </div>

</div>
</template>

<script>
import PieChart from "@/pages/collaborative/grade/component/PieChart";
import BarChart from "@/pages/collaborative/grade/component/BarChart";
import {getAllData} from "@/services/grade";
import Cookie from 'js-cookie'
const columns = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,

  },
  {
    title: '名称',
    dataIndex: 'name_',
    key: 'name_',
    align:'center',
    ellipsis: true,
    width:240
  },
  {
    title: '工厂',
    dataIndex: 'factoryCode_',
    key: 'factoryCode_',
    align:'center',
    ellipsis: true,
    width:160
  },
  {
    title: '类别',
    dataIndex: 'cooperationMode',
    key: 'cooperationMode',
    align:'center',
    width:80
  },
  {
    title: '协议产能(㎡/月)',
    dataIndex: 'agreedCapacity',
    key: 'agreedCapacity',
    align:'center',
    width:120
  },
  {
    title: '实际产出(㎡/月)',
    dataIndex: 'produce',
    key: 'produce',
    align:'center',
    width:120
  },
  {
    title: '产能负荷率',
    key: 'id',
    align:'center',
    customRender: (text,record,index) =>{
      let proportion = (record.produce/record.agreedCapacity) *100 || 0
      return proportion.toFixed() + '%'
      // return proportion.toFixed(2) + '%' || '0%'
    },
    // width:120
  },
]
const columns2 = [

  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,

  },
  {
    title: '名称',
    dataIndex: 'name_',
    key: 'name_',
    align:"center",
    ellipsis: true,
    width:240
  },
  {
    title: '工厂',
    dataIndex: 'factoryCode_',
    key: 'factoryCode_',
    align:'center',
    ellipsis: true,
    width:160
  },
  {
    title: '类别',
    dataIndex: 'cooperationMode',
    key: 'cooperationMode',
    align:'center',
    width:80
  },
  {
    title: '所在地',
    dataIndex: 'address_',
    key: 'address_',
    align:'center',
    ellipsis: true,
    // width:400
  },
]
const columns3 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,

  },
  {
    title: '品类',
    dataIndex: 'category',
    key: '',
    align:'center',
    width:83
  },
  {
    title: '名称',
    dataIndex: 'name_',
    key: 'name_',
    align:"center",
    ellipsis: true,
    width:240
  },
  {
    title: '工厂',
    dataIndex: 'factoryCode_',
    key: 'factoryCode_',
    scopedSlots: { customRender: 'factoryCode_' },
    align:'center',
    ellipsis: true,
    width:160
  },
  {
    title: '品级',
    dataIndex: 'grade',
    key: 'grade',
    align:'center',
    ellipsis: true,
    width:160
  },
  {
    title: '1月',
    dataIndex: 'm1',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '2月',
    dataIndex: 'm2',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '3月',
    dataIndex: 'm3',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '4月',
    dataIndex: 'm4',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '5月',
    dataIndex: 'm5',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '6月',
    dataIndex: 'm6',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '7月',
    dataIndex: 'm7',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '8月',
    dataIndex: 'm8',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '9月',
    dataIndex: 'm9',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '10月',
    dataIndex: 'm10',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '11月',
    dataIndex: 'm11',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },
  {
    title: '12月',
    dataIndex: 'm12',
    key: '',
    align:'center',
    ellipsis: true,
    width:60
  },

]
// const columns3 = [
//   {
//     dataIndex: "index",
//     title: '序号',
//     slots: { title: 'customTitle' },
//     key: 'index',
//     width: 40,
//     align: 'center',
//     scopedSlots: {customRender: 'index'},
//     customRender: (text,record,index) => `${index+1}`,
//
//   },
//   {
//     title: '名称',
//     dataIndex: 'name_',
//     key: 'name_',
//     align:"center",
//     ellipsis: true,
//     width:240
//   },
//   {
//     title: '工厂',
//     dataIndex: 'factoryCode_',
//     key: 'factoryCode_',
//     scopedSlots: { customRender: 'factoryCode_' },
//     align:'center',
//     ellipsis: true,
//     width:160
//   },
//   {
//     title: '类别',
//     dataIndex: 'cooperationMode',
//     key: 'cooperationMode',
//     align:'center',
//     width:83
//   },
//   {
//     title: '品级',
//     dataIndex: 'grade',
//     key: 'grade',
//     align:'center',
//     ellipsis: true,
//     width:160
//   },
//   {
//     title: '订单类型',
//     dataIndex: 'orderType_',
//     key: 'orderType_',
//     align:'center',
//     ellipsis: true,
//     width:160
//   },
//   {
//     title: '交期评级',
//     dataIndex: 'deliveryRating',
//     key: 'deliveryRating',
//     align:'center',
//     width:100
//   },
//   {
//     title: '质量评级',
//     dataIndex: 'qualityRating',
//     key: 'qualityRating',
//     align:'center',
//     width:100
//   },
//   {
//     title: '财务评级',
//     dataIndex: 'financialRating',
//     key: 'financialRating',
//     align:'center',
//     width:100
//   },
//   {
//     title: '综合评估',
//     // width:80,
//     align:"center",
//     scopedSlots: { customRender: 'action' }
//   },
//
//
// ]
export default {
  name: "gradeDetail",
  components: { BarChart, PieChart},
  data(){
    return {
      data1:{},
      columns,
      columns2,
      columns3,
      data:{
        orderLevel:[

        ],     // 订单品级
        orderType:[

        ],      // 订单类型
        capacityLoad: [


        ],  // 产能负荷
        factoryDistribution:[

        ],//工厂分布
        factoryRating:[

        ],   // 工厂评级
      },
      count1: 0,
      count2: 0,
      name: this.$route.query.name,
      loading: false,
    }
  },
  filters: {
    areaData(data) {
      let arr = []
      if (data){
        data.forEach(item => {
          let obj = {}
          obj['name'] = item.category
          obj['value'] = item.factoryNum
          arr.push(obj)
        })
      }
      return arr
    },
    th_barData: function (data) {
      // console.error('11',data)
      let name = [], value1=[],value2=[],value3=[];
      if (data) {
        name = data.map(item => {
          return item.factoryCode_
        })
        value1 = data.map(ite => {
          return ite.produce  // 实际
        })
        value2 = data.map(itm => {
          return itm.agreedCapacity  // 协议
        })
        value3 = data.map(ite => {
          return parseInt((ite.produce / ite.agreedCapacity) * 100)   // 实际
        })
      }
      // console.log('value1',value1)
      return {
        'name':name,
        'value':[
            {
              'type': 'bar',
              'data' : value1,
              itemStyle: {
                normal: {
                  label: {
                    show: true,//是否显示
                    position: 'top',//显示位置
                    textStyle: { //文字样式
                      color: '#333',
                      fontWeight: 500,
                    },
                    formatter: function (params) {//核心部分 formatter 可以为字符串也可以是回调
                      if (params.value) {//如果当前值存在则拼接
                        return params.value + '㎡'
                      } else {//否则返回个空
                        return '';
                      }
                    }
                  }
                },
              }
            },
          {
            'type': 'bar',
            'data': value2,
            itemStyle: {
              normal: {
                label: {
                  show: true,//是否显示
                  position: 'top',//显示位置
                  textStyle: { //文字样式
                    color: '#333',
                    fontWeight: 500,
                  },
                  formatter: function (params) {//核心部分 formatter 可以为字符串也可以是回调
                    if (params.value) {//如果当前值存在则拼接
                      return params.value + '㎡'
                    } else {//否则返回个空
                      return '';
                    }
                  }
                }
              },
            }
          },
            {
            'type': 'bar',
            'data' : value3,
              itemStyle: {
                normal: {
                  label: {
                    show: true,//是否显示
                    position: 'top',//显示位置
                    textStyle: { //文字样式
                      color: '#333',
                      fontWeight: 500,
                    },
                    formatter: function (params) {//核心部分 formatter 可以为字符串也可以是回调
                      if (params.value) {//如果当前值存在则拼接
                        return params.value + '㎡'
                      } else {//否则返回个空
                        return '';
                      }
                    }
                  }
                },
              }
            }
        ]
      }
    },
  }, 
  methods:{
    async getData(){
      this.loading = true;
      let type = this.$route.query.type
      let res = await getAllData(type)
      let name = this.$route.query.name
      localStorage.setItem('gradeName', JSON.stringify({data: name, }))
      localStorage.setItem('gradeType', JSON.stringify({data: type, }))
      
      this.data1 = res.data
      if(res && type == '协同') {
        this.count1 = res.data.categoryDistributionDtos.find(item => {return item.category == name}).factoryNum      // 协同工厂数量
        let data_ = res.data.supplierOutputs
        this.levelDataFilter(data_)
        this.typeDataFilter(data_)
        this.capacityLoadDataFilter(data_)
        this.factoryDistributionDataFilter(data_)
        this.factoryRatingDataFilter(data_)
      }else{
        this.count1 = res.data.categoryDistributionDtos.find(item => {return item.category == name}).zeroOrderFactoryNum      // 零单工厂数量
        let data_ = res.data.supplierOutputs
        this.levelDataFilter(data_)
        this.typeDataFilter(data_)
        this.capacityLoadDataFilter(data_)
        this.factoryDistributionDataFilter(data_)
        this.factoryRatingDataFilter(data_)
      }
      this.loading = false;
    },
    levelDataFilter(data){
      let _arr = [];
      let nameKey = this.objectKeys(data,this.$route.query.name)
      let boutique = data.filter(item => {
        if(item.boutique && item[nameKey] ) {
          return item  // 精品
        }
      })
      // console.log('精品',boutique)
      let superiorProduct = data.filter(item => {
        if(item.superiorProduct && item[nameKey] ) {
          return item  // 优品
        }
      })
      let standardProduct = data.filter(item => {
        if(item.standardProduct && item[nameKey] ) {
          return item  // 标品
        }
      })
      let boutique1 = data.filter(item => {
        if(item.boutique && item[nameKey] ) {
          return item  // 精品
        }
      })
      let superiorProduct1 = data.filter(item => {
        if(item.superiorProduct && item[nameKey] ) {
          return item  // 优品
        }
      })
      let standardProduct1 = data.filter(item => {
        if(item.standardProduct && item[nameKey]) {
          return item  // 标品
        }
      })
      this.data.orderLevel = [
        {
            category: '精品',
            factoryNum:boutique.length,
            proportion: (parseInt((boutique.length/this.count1 * 100).toFixed()) || 0) + '%'
          },
          {
            category: '优品',
            factoryNum: superiorProduct.length,
            proportion: (parseInt((superiorProduct.length/this.count1 * 100).toFixed()) || 0) + '%'
          },
          {
            category: '标品',
            factoryNum: standardProduct.length,
            proportion: (parseInt((standardProduct.length/this.count1 * 100).toFixed()) || 0) + '%'
          }
      ]
    // console.error(this.data.orderLevel)
    },
    objectKeys(data,name){
      let obj_ = {'singlePanel':"单面", 'doublePanel':'双面', 'multiPanel':'多层','aluminumPanel':'铝基', 'fpcPanel':'FPC'}
      for(var i in obj_){
        if(obj_[i] == name) {
          return i
        }
      }
    },
    typeDataFilter(data){
      let _arr = [];
      let nameKey = this.objectKeys(data,this.$route.query.name)
      let proofing = data.filter(item => {
        if(item.proofing && item[nameKey] ) {
          return item
        }
      })
      let smallbatch = data.filter(item => {
        if(item.smallbatch && item[nameKey] ) {
          return item
        }
      })
      let massProduction = data.filter(item => {
        if(item.massProduction && item[nameKey] ) {
          return item
        }
      })
      let proofing_1 = data.filter(item => {
        if(item.proofing && item[nameKey] ) {
          return item
        }
      })
      let smallbatch1 = data.filter(item => {
        if(item.smallbatch && item[nameKey] ) {
          return item
        }
      })
      let massProduction1 = data.filter(item => {
        if(item.massProduction && item[nameKey] ) {
          return item
        }
      })
      this.data.orderType = [
        {
            category: '打样',
            factoryNum:proofing.length,
            proportion: (parseInt(proofing.length /this.count1  * 100 )|| 0) + '%'
          },
          {
            category: '中小批量',
            factoryNum: smallbatch.length,
            proportion: (parseInt( smallbatch.length/this.count1 * 100) || 0) + '%'
          },
          {
            category: '大批量',
            factoryNum: massProduction.length,
            proportion: (parseInt(massProduction.length/this.count1 * 100) || 0) + '%'
          }
      ]
    },
    capacityLoadDataFilter(data){
      let _arr = [];
      let nameKey = this.objectKeys(data,this.$route.query.name)
      for(var i=0;i<data.length;i++){
        if(data[i][nameKey]) {
          _arr.push(data[i])
        }
      }
      _arr.sort((a, b) =>{return b.cooperationMode.localeCompare(a.cooperationMode, 'zh')})
      this.data.capacityLoad = _arr
    },
    factoryDistributionDataFilter(data){
      let _arr = [];
      let nameKey = this.objectKeys(data,this.$route.query.name)
      for (var a=0;a<data.length;a++){
        if(data[a][nameKey]){
        _arr.push(data[a])
      }
    }
    _arr.sort((a, b) =>{return b.cooperationMode.localeCompare(a.cooperationMode, 'zh')})
    this.data.factoryDistribution = _arr
    },
    factoryRatingDataFilter(data){
      let _arr = [];
      // let Grade ={'boutique' :'精品', 'superiorProduct':'优品', 'standardProduct' :'标品'}
      // let category={'proofing': '打样','smallbatch':'中小批量','massProduction':'大批量'}
      let nameKey = this.objectKeys(data,this.$route.query.name)
      for(var i=0;i<data.length;i++){
        if(data[i][nameKey]){
          _arr.push(data[i])
        }
      }
      // console.log('data[i]',nameKey)
      // console.log('_arr',_arr)
      let category = ''
      if(nameKey == 'singlePanel'){
        category = "单面"
      }else if(nameKey == 'doublePanel'){
        category = "双面"
      }else if(nameKey == 'multiPanel'){
        category = "多层"
      }else if(nameKey == 'aluminumPanel'){
        category = "铝基"
      }else if(nameKey == 'fpcPanel'){
        category = "FPC"
      }
      for(var j=0;j<_arr.length;j++){
        _arr[j]['category'] = category
      }
      for (var a=0;a<_arr.length;a++){
         let Grade = [];
        if(_arr[a].boutique){
          Grade.push('精品')
        }
        if(_arr[a].superiorProduct){
          Grade.push('优品')
        }
        if(_arr[a].standardProduct){
          Grade.push('标品')
        }
         _arr[a]['grade'] = Grade.join(',')
      }
       for ( var b=0;b<_arr.length;b++){
        let orderType_ = [];
        if(_arr[b].proofing){
          orderType_ .push('打样')
        }
        if(_arr[b].smallbatch){
          orderType_ .push('中小批量')
        }
        if(_arr[b].massProduction){
          orderType_ .push('大批量')
        }
        _arr[b]['orderType_'] = orderType_.join(',')
      }
      _arr.sort((a, b) =>{return b.cooperationMode.localeCompare(a.cooperationMode, 'zh')})
      this.data.factoryRating = _arr
      // console.log('_arr2',_arr)
      },
       details(name){
      this.$router.push({
         path: `/collaborative/detail?id=${name}&type=1&factory=详情`,
       })
  }

  },
  created() {
    this.getData()
  },
  mounted() {
  }


}
</script>

<style scoped lang="less">
/deep/.ant-card-head-title{
  font-weight: 500;
}
.gradeDetails {
  background: #FFFFFF;
  min-width:1670px;
  /deep/ .ant-card-body {
    font-weight: 600;
      .firft {
        td {
          text-align: center;
          height: 35px;
          width: 120px;
         
          a {
            text-decoration:underline!important;
          }
        }

      }
  }
  .second {

    /deep/ .ant-card-body {
      display: flex;
      .ant-card-loading-content {
        flex: 1;
      }
      .sc_left {
        width:50%;
        height: 200px;
        padding-top: 33px;
        flex: 1;
        td {
          text-align: center;
          height: 35px;
          width: 120px;
          font-weight: 500;
        }
      }
      .sc_right {
        width:50%;
        height: 200px;
        padding-top: 14px;
        flex: 1;
      }
    }

  }
  /deep/ .ant-table {
    .ant-table-scroll {
      .ant-table-thead {
        th {
          padding: 7px 0 !important;
        }
      }
      .ant-table-body {
        td {
          padding: 5px 0 !important;
        }
        tr {
          td:nth-child(2){
            padding-left: 5px;
          }
        }
      }
    }
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background:#F8F8F8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  .third {
    height: 400px;
    /deep/ .ant-card-body {
      display: flex;
      .ant-card-loading-content {
        flex: 1;
      }
      .th_left {
        width:55%;
        .mintable{
          .ant-table-body{
            min-height: 258px;
            border-bottom: 1px solid #f0f0f0;
            border-left: 1px solid #f0f0f0;
          }
        }
      }

      .th_right {
        width:45%;
      }
      }
    }
  }
  .fourth{
    height: 400px;
    /deep/ .ant-card-body {
      .th_left {
        width:80%;
        .mintable1{
          .ant-table-body{
            min-height: 258px;
            border-bottom: 1px solid #f0f0f0;
            border-left: 1px solid #f0f0f0;
          }
        }
      }
    }

  }
  .fifth {
    height: 400px;
    /deep/ .ant-card-body {
      .th_left {
        width: 90%;
        overflow: auto;
        .mintable1{
          .ant-table-body{
            min-height: 258px;
            border-bottom: 1px solid #f0f0f0;
            border-left: 1px solid #f0f0f0;
          }
        }
      }
    }
  }
</style>
