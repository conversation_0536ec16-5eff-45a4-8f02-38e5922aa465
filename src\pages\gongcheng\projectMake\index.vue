<!-- 工程管理 - 工程制作 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectMake" style="position: relative">
      <span style="position: absolute; top: -4%; right: 0%; color: #ff9900; font-size: 16px">{{ showNotes }}</span>
      <div style="width: 100%; display: flex">
        <div class="leftContent">
          <left-table-make
            v-if="showModel"
            :columns="columns1"
            @downAll="downAll"
            :data-source="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'proOrderId'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            @getOrderDetail="getOrderDetail"
            @jigsawPuzzleClick="jigsawPuzzleClick"
            @getJobInfo="getJobInfo"
            @getprocessstepnotes="getprocessstepnotes"
            @ChargebackClick="ChargebackClick"
            @viewLogClick="viewLogClick"
            @modifyInfoClick="modifyInfoClick"
            @EditParametersClick="EditParametersClick"
            @getmultiples="getmultiples"
            @webSocketLink="webSocketLink"
            @CustomerRulesClick="CustomerRulesClick"
            @StatusSynchronization="StatusSynchronization"
            @RepairRecordClick="RepairRecordClick"
            ref="orderTable"
            class="leftstyle"
            :cookieId="cookieId"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
          </left-table-make>
          <a-menu
            :style="menuStyle"
            v-show="menuVisible"
            class="tabRightClikBox"
            v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.EngineeringProductionFileReplace')"
          >
            <a-menu-item>
              <a-upload accept=".rar,.zip" name="file" :before-upload="beforeUpload" :customRequest="httpRequest0"> 文件替换 </a-upload>
            </a-menu-item>
          </a-menu>
        </div>
        <div class="rightContent" style="display: none">
          <div class="centerTable">
            <a-tabs type="card">
              <a-tab-pane tab="">
                <order-detail
                  :columns="columns2"
                  :data-source="orderDetailDataFilter"
                  :orderDetailTableLoading="orderDetailTableLoading"
                  :rowKey="'projectName'"
                  :class="orderDetailDataFilter.length ? 'minTable' : ''"
                >
                </order-detail>
              </a-tab-pane>
            </a-tabs>
          </div>
          <div class="rightTable" style="width: 100%">
            <div class="note" v-viewer>
              <div class="textNote" v-html="allNote"></div>
            </div>
            <div class="jobNote" ref="tableWrapper">
              <a-table
                :columns="columns4"
                :dataSource="jobData"
                :customRow="onClickRow"
                :pagination="false"
                rowKey="id"
                bordered
                :scroll="{ y: 358 }"
                :class="jobData.length ? 'minTable' : ''"
                :loading="jobTableLoading"
              >
                <template slot="picUrl" slot-scope="text, record">
                  <viewer :images="picFilter(record)">
                    <img :src="item" v-for="(item, index) in picFilter(record)" :key="index" style="width: 20px; height: 20px" />
                  </viewer>
                </template>
                <template slot="fileUrl" slot-scope="text, record">
                  <a-tooltip title="编辑">
                    <a-icon type="edit" style="color: rgb(0, 0, 204)" @click="editClick(record)"></a-icon>
                  </a-tooltip>
                  <span style="color: rgb(0, 0, 204)">/</span>
                  <a-tooltip title="下载附件">
                    <a-icon type="download" v-if="record.fileUrl" style="color: rgb(0, 0, 204)" @click="downFile(record.fileUrl)"></a-icon>
                  </a-tooltip>
                  <span v-if="record.fileUrl" style="color: rgb(0, 0, 204)">/</span>
                  <a-tooltip title="删除">
                    <a-icon type="close-square" style="color: rgb(0, 0, 204)" @click="delFile(record)"></a-icon>
                  </a-tooltip>
                </template>
              </a-table>
              <a-menu :style="menuStyle1" v-show="menuVisible1" class="tabRightClikBox1">
                <a-menu-item @click="down1" v-if="showText">复制</a-menu-item>
              </a-menu>
            </div>

            <!-- <div  class="jobauto" style='user-select: none;'> 12-15按要求取消表格            
            <a-table
                :columns="columns5"
                :dataSource="jobautoData"
                :pagination="false"
                rowKey="id"
                bordered
                :scroll="{y: 400}"
                :loading="jobTableLoading">
            </a-table>
          </div>
          <div  class="pronotes" style='user-select: none;'>
           <a-table
               :columns="columns6"
               :dataSource="pronotesData"
               :pagination="false"
               rowKey="id"
               bordered
               :scroll="{y: 400}">
           </a-table>
          </div> -->
          </div>
        </div>
      </div>
      <div class="footerAction" ref="footeraction">
        <make-action
          ref="action"
          @ProductionOrder="ProductionOrder"
          @Releasewarning="Releasewarning"
          :assignLoading="assignLoading"
          @wenkeclick="wenkeclick"
          @queryClick="queryClick"
          :total="pagination.total"
          @TakeOrderClick="TakeOrderClick"
          @ceshi="ceshi"
          @MakeStartClick="MakeStartClick"
          @RepairCompletedClick="RepairCompletedClick"
          @ProductionStandardClick="ProductionStandardClick"
          @OverlayCopyClick="OverlayCopyClick"
          @mattersNeedingAttentionClick="mattersNeedingAttentionClick"
          @GenerateStackClick="GenerateStackClick"
          @StackImpedanceClick="StackImpedanceClick"
          @CuttingClick="CuttingClick"
          @finishClick="finishClick"
          @RegisterClick="RegisterClick"
          @MarketmodClick="MarketmodClick"
          @PerformanceClick="PerformanceClick"
          @Confirmmodification="Confirmmodification"
        />
      </div>
      <a-modal title="拼版图" :visible="makeupVisible" :footer="null" @cancel="handleCancel">
        <makeup-pic ref="makeup"></makeup-pic>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        centered
      >
        <query-info ref="queryInfo" />
      </a-modal>
      <!-- 修改信息 -->
      <a-modal
        title="Vcut"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
      >
        <modify-info-make ref="ModifyInfoMake" :selectedRowsData="selectedRowsData" />
      </a-modal>
      <!-- 系数倍数修改 -->
      <a-modal
        title="系数倍数修改"
        :visible="dataVisible11"
        @cancel="reportHandleCancel"
        @ok="handleOk10"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
      >
        <xishu-Modify ref="xishuModify" :selectedRowsData="selectedRowsData" />
      </a-modal>
      <!-- 退单弹窗 -->
      <a-modal
        title="回退订单"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <chargeback-make ref="ChargebackMake" />
      </a-modal>
      <!--市场修改弹窗 -->
      <a-modal
        title="市场修改"
        :visible="dataVisible13"
        @cancel="reportHandleCancel"
        @ok="handleOk13"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="610"
        :confirmLoading="confirmLoading"
        centered
      >
        <market-make ref="MarketMake" />
      </a-modal>
      <!-- 编辑参数 -->
      <a-modal
        title="CAM参数设置"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <edit-parameters-make ref="EditParametersMake" :selectedRowsData="selectedRowsData" :ParameterData="ParameterData" />
      </a-modal>
      <!-- 注意事项弹窗 -->
      <!-- 注意事项弹窗 -->
      <a-modal
        title="信息传递"
        :visible="dataVisibleMatter"
        @cancel="reportHandleCancel"
        @ok="handleOkMatter"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="550"
        :confirmLoading="confirmLoading"
        centered
      >
        <matters-needing-attention-info ref="mattersNeedingAttention" :editData="editData" />
      </a-modal>
      <!-- 生产备注弹窗 -->
      <a-modal
        title="生产备注"
        :visible="dataVisible10"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="550"
        :confirmLoading="confirmLoading"
      >
      </a-modal>
      <!-- 生成叠层弹窗 -->
      <a-modal
        title="叠层"
        :visible="dataVisible5"
        @cancel="reportHandleCancel"
        @ok="handleOk5"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        class="modalSty"
        :confirmLoading="confirmLoading"
      >
        <generate-stack-info ref="GenerateStack" :stackListData="stackListData" />
      </a-modal>
      <!-- 返修记录弹窗 -->
      <a-modal
        title="返修记录"
        :visible="dataVisible6"
        @cancel="reportHandleCancel"
        @ok="handleOk6"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        :confirmLoading="confirmLoading"
      >
        <repair-record-info ref="RepairRecord" :RepairRecordData="RepairRecordData" />
      </a-modal>
      <!-- 客户规则弹窗 -->
      <a-modal
        title="客户规则"
        :visible="dataVisible7"
        @cancel="reportHandleCancel"
        @ok="handleOk7"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        :confirmLoading="confirmLoading"
      >
        <customer-rules-info ref="CustomerRules" :CustomerData="CustomerData" />
      </a-modal>
      <!-- 日志弹窗 -->
      <a-modal
        title="日志"
        :visible="dataVisible8"
        @cancel="reportHandleCancel"
        @ok="handleOk8"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="450"
        :confirmLoading="confirmLoading"
      >
        <view-log-info ref="viewLogInfo" :viewLogData="viewLogData" />
      </a-modal>
      <!-- 制作完成 -->
      <a-modal
        title="CAM参数设置"
        :visible="dataVisible9"
        @cancel="reportHandleCancel"
        @ok="handleOk9"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <production-completed ref="ProductionCompleted" :ParameterData2="ParameterData2" :code="code" />
        <name></name>
      </a-modal>
      <productRemark
        @handleCancelRemark="handleCancelRemark"
        @handleOkRemark="handleOkRemark"
        :orderNoRemark="orderNoRemark"
        :dataRemark="dataRemark"
        :visible="visibleRemark"
        :confirmLoading="confirmLoadingRemark"
      >
      </productRemark>
      <a-modal
        title=" 确认弹窗"
        :visible="dataVisibleMode"
        @cancel="reportHandleCancel"
        @ok="handleOkMode"
        ok-text="确定"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="font-size: 14px; color: #000000">【{{ orderno }}】</span>
        <span style="font-size: 14px; color: #000000">{{ messageMode }}</span>
      </a-modal>
      <!--红马生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="HMnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="hmnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="850"
      >
        <report-hmnoticeform :ttype="'EMS | 工程制作'" :HMnoticedata="HMnoticedata" ref="hmnotice"></report-hmnoticeform>
      </a-modal>
      <!--联合多层生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="LHDCnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="lhdcnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1400"
      >
        <report-lhdcnoticeform :ttype="'EMS | 工程后端'" :LHDCnoticedata="LHDCnoticedata" ref="lhdcnotice"></report-lhdcnoticeform>
      </a-modal>
      <!-- 制作完成错误提示弹窗 -->
      <a-modal :title="meslist" :visible="dataVisibleNo" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
        <template slot="footer">
          <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
          <a-button @click="reportHandleCancel">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500" v-if="checkType == 'zzwc' || checkType == 'ks' || checkType == 'qdqd'">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.result }}</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.result }}</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.result }}</span>
            </span>
          </p>
          <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
        <div class="class" style="font-size: 16px" v-else>
          <p v-for="(item, index) in checkData" :key="index" style="color: red">
            <a-icon type="star"></a-icon>
            <span>{{ item.result }}</span>
          </p>
        </div>
      </a-modal>
      <!-- 制作完成是否QAE -->
      <a-modal
        :title="'【' + orderno + '】 制作完成'"
        :visible="dataVisibleqae"
        @cancel="reportHandleCancel"
        @ok="handleOkqae"
        :confirmLoading="confload"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div class="class" style="font-size: 16px">是否QAE: <a-checkbox v-model="IsQae" :disabled="!this.IsQAEEnble"> </a-checkbox></div>
      </a-modal>
      <!-- <a-modal
      title="返修完成"
      :visible="dataVisible12"
      @cancel="reportHandleCancel"
      @ok="handleOk12"
      ok-text="确定"
      :width="400"
      centered
      > 
   </a-modal>  -->
      <!-- 绩效管理 -->
      <performance ref="performanceModal" :selectedRowsData="selectedRowsData" :type="0"> </performance>
    </div>
  </a-spin>
</template>

<script>
import ReportLhdcnoticeform from "@/pages/mkt/OrderOffer/report/ReportLhdcnoticeform";
import ReportHmnoticeform from "@/pages/mkt/OrderOffer/report/ReportHmnoticeform";
import { noticereviewinfo, productionreport } from "@/services/mkt/OrderReview.js";
import { ppebuttonCheck, isqaeenble } from "@/services/projectMake/index.js";
import { setEngineeringMake } from "@/utils/request";
import { checkPermission } from "@/utils/abp";
import {
  TakeOrderList,
  projectBackEndJobInfo,
  getJobAutoInfo,
  projectBackEndOrderDetail,
  proOrderInfo,
  projectMakeOrderList,
  MakeStart,
  getModifyInformation,
  RepairCompleted,
  ReorderUpdateStack,
  ImportOrder,
  getWenkeUrl,
  getProductionStandard,
  BackStart,
  mattersNeedingAttention,
  getParameter,
  SaveParameter,
  getGenerateStack,
  getRepairRecord,
  saveRepairRecord,
  getStackImpedance,
  getCutting,
  getCustomerInfo,
  getViewLog,
  getFactoryList,
  makeInfo,
  fileReplacement,
  finish,
  stateSync,
  delInfo,
  makesetreleasewarning,
  setsureordermodify,
  gerberDownloadPath,
  processstepnotes,
  multiples,
  setcAMMiflowinfostoerp,
  mIFinish,
  Marketmake,
  repierMatters,
  datapath,
} from "@/services/projectMake";
import { postProcess } from "@/services/projectApi";
import LeftTableMake from "@/pages/gongcheng/projectMake/subassembly/LeftTableMake";
import productRemark from "@/pages/gongcheng/projectMake/subassembly/productRemark";
import OrderDetail from "@/pages/gongcheng/projectMake/subassembly/OrderDetail";
import MakeAction from "@/pages/gongcheng/projectMake/subassembly/MakeAction";
import MakeupPic from "@/pages/gongcheng/projectMake/subassembly/MakeupPic";
import QueryInfo from "@/pages/gongcheng/projectMake/subassembly/QueryInfo";
import ModifyInfoMake from "@/pages/gongcheng/projectMake/subassembly/ModifyInfoMake";
import ChargebackMake from "@/pages/gongcheng/projectMake/subassembly/ChargebackMake";
import MarketMake from "@/pages/gongcheng/projectMake/subassembly/MarketMake";
import EditParametersMake from "@/pages/gongcheng/projectMake/subassembly/EditParametersMake";
import mattersNeedingAttentionInfo from "@/pages/gongcheng/projectMake/subassembly/mattersNeedingAttentionInfo";
import GenerateStackInfo from "@/pages/gongcheng/projectMake/subassembly/GenerateStackInfo.vue";
import RepairRecordInfo from "@/pages/gongcheng/projectMake/subassembly/RepairRecordInfo.vue";
import CustomerRulesInfo from "@/pages/gongcheng/projectMake/subassembly/CustomerRulesInfo.vue";
import viewLogInfo from "@/pages/gongcheng/projectMake/subassembly/viewLogInfo.vue";
import xishuModify from "@/pages/gongcheng/projectMake/subassembly/xishuModify.vue";
import Performance from "@/pages/gongcheng/projectMake/subassembly/Performance";
import ProductionCompleted from "@/pages/gongcheng/projectMake/subassembly/ProductionCompleted";
import Cookie from "js-cookie";

const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    fixed: "left",
    // customRender: (text,record,index) => `${index+1}`,
    scopedSlots: { customRender: "num" },
    width: 40,
  },

  {
    title: "生产编号",
    dataIndex: "orderNo",
    align: "left",
    fixed: "left",
    ellipsis: true,
    width: 115,
    className: "userStyle",
    scopedSlots: { customRender: "orderNo" },
  },

  {
    title: "订单状态",
    dataIndex: "statusType",
    className: "userStyle",
    ellipsis: true,
    align: "left",
    width: 70,
    scopedSlots: { customRender: "customRender" },
    sorter: (a, b) => {
      return a.statusType.localeCompare(b.statusType);
    },
  },
  {
    title: "订单类型",
    customRender: (text, record, index) =>
      `${record.isReOrder == 0 ? "新单" : record.isReOrder == 1 ? "返单" : record.isReOrder == 2 ? "返单更改" : ""}`,
    align: "left",
    ellipsis: true,
    width: 70,
    sorter: (a, b) => {
      return a.isReOrder.toString().localeCompare(b.isReOrder.toString());
    },
  },

  {
    title: "制作人",
    dataIndex: "frontEndRealName",
    width: 65,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      return a.frontEndRealName.localeCompare(b.frontEndRealName);
    },
  },

  {
    title: "交期",
    dataIndex: "deliveryDate",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 130,
    sorter: (a, b) => {
      return a.deliveryDate.localeCompare(b.deliveryDate);
    },
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "板厚",
    dataIndex: "boardThickness",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "面积",
    dataIndex: "boardArea",
    align: "left",
    ellipsis: true,
    width: 50,
  },
  {
    title: "数量",
    dataIndex: "num",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "耗时",
    width: 45,
    dataIndex: "proStayTime",
    sorter: (a, b) => {
      return a.proStayTime - b.proStayTime;
    },
    ellipsis: true,
    align: "left",
  },
  {
    title: "原稿",
    scopedSlots: { customRender: "down" },
    width: 70,
    ellipsis: true,
    className: "noCopy",
    align: "center",
  },
  {
    title: "全套资料",
    scopedSlots: { customRender: "AllDataPath" },
    width: 70,
    ellipsis: true,
    className: "noCopy",
    align: "center",
  },
  {
    title: "表面工艺",
    dataIndex: "surfaceFinishStr",
    align: "left",
    ellipsis: true,
    width: 190,
  },
  {
    title: "系数",
    scopedSlots: { customRender: "score" },
    align: "left",
    width: 60,
  },
  // {
  //   title: "生产备注",
  //   width: 65,
  //   ellipsis: true,
  //   align: "center",
  //   scopedSlots:{customRender:'isProcessStepNotes'}
  // },
  {
    title: "转换",
    scopedSlots: { customRender: "p2GStatus" },
    width: 110,
    ellipsis: true,
    align: "left",
  },
  {
    title: "客规",
    width: 45,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "isCustRule" },
  },

  {
    title: "EQ完耗时(h)",
    width: 90,
    sorter: (a, b) => {
      return a.eqCostTime - b.eqCostTime;
    },
    scopedSlots: { customRender: "eqCostTime" },
    ellipsis: true,
    align: "left",
  },
  {
    title: "服务耗时",
    dataIndex: "taskCostTime",
    align: "left",
    ellipsis: true,
    width: 120,
  },
  {
    title: "剩余时间(h)",
    dataIndex: "timeOut",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  // {
  //   title: "操作",
  //   scopedSlots: { customRender: 'action' },
  //   align: "left",
  //   fixed:'right',
  //   width: 140,   // 2290
  // },
  {
    title: "下单时间",
    dataIndex: "createTime",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 150,
    sorter: (a, b) => {
      if (a.createTime === null) {
        return 1;
      }
      if (b.createTime === null) {
        return -1;
      }
      return a.createTime.localeCompare(b.createTime);
    },
  },
  {
    title: "派单时间",
    dataIndex: "camSendDate",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 138,
    sorter: (a, b) => {
      if (a.camSendDate === null) {
        return 1;
      }
      if (b.camSendDate === null) {
        return -1;
      }
      return a.camSendDate.localeCompare(b.camSendDate);
    },
  },

  {
    title: "预审人",
    dataIndex: "preName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "报价人",
    dataIndex: "checkName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "前端",
    scopedSlots: { customRender: "down1" },
    width: 70,
    ellipsis: true,
    className: "noCopy",
    align: "center",
  },
  {
    title: "拼版图",
    dataIndex: "jigsawPuzzle",
    align: "left",
    width: 60,
    ellipsis: true,
    scopedSlots: { customRender: "jigsawPuzzle" },
  },
  {
    title: "加工工厂",
    dataIndex: "orderChannel",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "钢网文件",
    scopedSlots: { customRender: "WorkFile" },
    width: 70,
    ellipsis: true,
    className: "noCopy",
    align: "center",
  },
  {
    title: "版本",
    dataIndex: "proRev",
    className: "userStyle",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  // {
  //   title: "操作",
  //   fixed: "right",
  //   align: "center",
  //   scopedSlots: { customRender: "labelUrl" },
  //   width: 50,
  //   className: "userStyle noCopy",
  // },
  {
    title: "MI",
    scopedSlots: { customRender: "process" },
    align: "center",
    fixed: "right",
    width: 40, // 2290
  },
  {
    title: "指示",
    scopedSlots: { customRender: "action1" },
    align: "center",
    fixed: "right",
    className: "noCopy",
    width: 40,
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 19,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "项目",
    dataIndex: "projectName",
    width: 62,
    align: "left",
    customCell: (record, rowIndex) => {
      if (record.projectName == "铜厚(外)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // copperThickness
      } else if (record.projectName == "铜厚(内)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // innerCopperThickness 铜厚(内)oz
      } else if (record.projectName == "字符颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; // fontColor 字符颜色(顶)
      } else if (record.projectName == "阻焊颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; //solderColor 阻焊颜色(顶)
      } else if (record.projectName == "过孔处理" && record.parameter == "过孔开窗") {
        return { style: { color: "#ff00ff!important" } }; // solderCover 过孔处理
      } else if (record.projectName == "过孔处理" && (record.parameter.indexOf("塞油") >= 0 || record.parameter.indexOf("树脂塞") >= 0)) {
        return { style: { color: "#ff0000!important" } }; // solderCover 过孔处理
      }
      // else if (record.projectName == '订单数量' && record.boardArea > 1.0) {
      //   return { style: { 'background': '#ff0000!important', } } // num 数量
      // }
      else if (record.projectName == "阻抗报告" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } }; // impedanceReport 阻抗报告
      } else if (record.projectName == "阻抗" && record.parameter !== "无") {
        return { style: { color: "#ff0000!important" } }; // impedanceSize 阻抗
      } else if (record.projectName == "图形转移工艺" && record.parameter == "丝网印刷") {
        return { style: { color: "#ff0000!important" } }; // imageTranster 图形转移工艺
      } else if (record.projectName == "文件名" && record.count > 0) {
        return { style: { color: "#ff0000!important" } }; // fileUploadedCount 文件上传次数>0 文件名显示红色
      } else if (record.projectName == "盘中孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "半边孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "异形孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "板边包金" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "沉孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "印序列号" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "蓝胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指面积" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指金厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指镍厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "压接孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "背钻孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "通孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "阶梯孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "碳油" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "高温胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "斜边" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲槽" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋铜" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋阻" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "铜浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "银浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      }
    },
  },
  {
    title: "参数",
    dataIndex: "parameter",
    width: 62,
    align: "left",
    className: "userStyle heightSTY",
    ellipsis: true,
    scopedSlots: { customRender: "parameter" },
    customCell: (record, rowIndex) => {
      if (record.projectName == "铜厚(外)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // copperThickness
      } else if (record.projectName == "铜厚(内)oz" && record.parameter > 1.0) {
        return { style: { color: "#ff0000!important" } }; // innerCopperThickness 铜厚(内)oz
      } else if (record.projectName == "字符颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; // fontColor 字符颜色(顶)
      }
      // else if (record.projectName == '字符颜色(底)' && record.parameter == '无') {
      //   return { style: { 'background': '#ff0000!important', } } // fontColorBottom 字符颜色(底)
      // }
      else if (record.projectName == "阻焊颜色(顶)/(底)" && record.parameter == "无/无") {
        return { style: { color: "#ff0000!important" } }; //solderColor 阻焊颜色(顶)
      }
      // else if (record.projectName == '阻焊颜色(底)' && record.parameter == '无') {
      //   return { style: { 'background': '#ff0000!important', } } // solderColorBottom 阻焊颜色(底)
      // }
      else if (record.projectName == "过孔处理" && record.parameter == "过孔开窗") {
        return { style: { color: "#ff00ff!important" } }; // solderCover 过孔处理
      } else if (record.projectName == "过孔处理" && (record.parameter.indexOf("塞油") >= 0 || record.parameter.indexOf("树脂塞") >= 0)) {
        return { style: { color: "#ff0000!important" } }; // solderCover 过孔处理
      }
      // else if (record.projectName == '订单数量' && record.boardArea > 1.0) {
      //   return { style: { 'background': '#ff0000!important', } } // num 数量
      // }
      else if (record.projectName == "阻抗报告" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } }; // impedanceReport 阻抗报告
      } else if (record.projectName == "阻抗" && record.parameter !== "无") {
        return { style: { color: "#ff0000!important" } }; // impedanceSize 阻抗
      } else if (record.projectName == "图形转移工艺" && record.parameter == "丝网印刷") {
        return { style: { color: "#ff0000!important" } }; // imageTranster 图形转移工艺
      } else if (record.projectName == "文件名" && record.count > 0) {
        return { style: { color: "#ff0000!important" } }; // fileUploadedCount 文件上传次数>0 文件名显示红色
      } else if (record.projectName == "盘中孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "半边孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "异形孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "板边包金" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "沉孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "印序列号" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "蓝胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指面积" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指金厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "金手指镍厚" && record.parameter) {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "压接孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "背钻孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "通孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲孔控深" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "阶梯孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "碳油" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "高温胶" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "斜边" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "盲槽" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋铜" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "埋阻" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "铜浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      } else if (record.projectName == "银浆塞孔" && record.parameter == "是") {
        return { style: { color: "#ff0000!important" } };
      }
    },
  },
];
const columns4 = [
  {
    title: "操作人",
    dataIndex: "inUserName",
    width: "15%",
    align: "left",
    ellipsis: true,
  },
  {
    title: "创建时间",
    dataIndex: "indate",
    width: "15%",
    align: "left",
    ellipsis: true,
  },
  {
    title: "备注信息",
    dataIndex: "conent",
    width: "39%",
    align: "left",
    ellipsis: true,
  },
  {
    title: "图片",
    width: "18%",
    scopedSlots: { customRender: "picUrl" },
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: "fileUrl" },
    className: "noCopy",
    align: "left",
  },
];
const columns5 = [
  {
    title: "订单号",
    dataIndex: "orderNo_",
    width: "25%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "类型",
    dataIndex: "type_",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "状态",
    dataIndex: "status_",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    width: "30%",
    align: "left",
  },
];
const columns6 = [
  {
    title: "工序",
    dataIndex: "codeName",
    width: "23%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "备注",
    dataIndex: "note",
    width: "70%",
    ellipsis: true,
    align: "left",
  },
];
const projectArray = [
  { name: "工厂编号", value: "orderNo" },
  { name: "层数", value: "boardLayers" },
  { name: "数量", value: "num" },
  { name: "板材信息", value: "fR4TypeStr" },
  { name: "板材型号", value: "boardBrandStr" },
  { name: "成品板厚", value: "boardThickness" },
  // {name:'内/外完成铜厚',value:'copperThicknessStr'},
  { name: "铜厚(外)oz", value: "copperThicknessStr" },
  { name: "铜厚(内)oz", value: "innerCopperThicknessStr" },
  { name: "阻焊颜色(顶)/(底)", value: "solderColorStr" },
  // {name:'阻焊颜色(底)',value:'solderColorBottom'},
  { name: "字符颜色(顶)/(底)", value: "fontColorStr" },
  // {name:'字符颜色(底)',value:'fontColorBottom'},
  { name: "表面处理", value: "surfaceFinishStr" },
  { name: "过孔处理", value: "solderCoverStr" },
  { name: "单元尺寸(长X宽)", value: "boardHeight" },
  { name: "成品尺寸(长X宽)", value: "boardHeightSet" },
  { name: "出货类型", value: "pinBanType" },
  //{name:'测试方式',value:'flyingProbeStr'},
  { name: "文件名", value: "pcbFileName" },
  { name: "拼版数量", value: "pinBanNum" },
  { name: "电镀工艺", value: "beforePlating" },
  { name: "图形转移工艺", value: "imageTranster" },
  { name: "最小孔", value: "vias" },
  { name: "线宽线距(mil)", value: "lineWeight" },
  // {name:'订单数量',value:'num'},
  // {name:'成型方式',value:'formingTypeStr'},
  // {name:'阻抗报告',value:'impedanceReport'},
  { name: "阻抗", value: "isImpedance" },
  // 特殊工艺
  { name: "盘中孔", value: "isDiscHole" },
  { name: "半边孔", value: "isHalfHole" },
  { name: "异形孔", value: "isProfileHole" },
  { name: "板边包金", value: "isPlateEdge" },
  { name: "沉孔", value: "stepHole" },
  { name: "印序列号", value: "isSerialNumber" },
  { name: "蓝胶", value: "isBlueGum" },
  { name: "金手指", value: "isGoldfinger" },
  { name: "金手指面积", value: "goldenFingerAreaRe" },
  { name: "金手指金厚", value: "goldFingerThicknessStr" },
  { name: "金手指镍厚", value: "goldfingerNickelThickness" },
  { name: "压接孔", value: "isCrimpHole" },
  { name: "背钻孔", value: "isBackDrilling" },
  { name: "通孔控深", value: "isThroughHoleControl" },
  { name: "盲孔控深", value: "isBlindHoleControl" },
  { name: "阶梯孔", value: "steppedHole" },
  { name: "碳油", value: "isCarbonOil" },
  { name: "高温胶", value: "heatTape" },
  { name: "斜边", value: "isGoldfingerBevel" },
  { name: "盲槽", value: "isBlindSlot" },
  { name: "埋铜", value: "buriedCopper" },
  { name: "埋阻", value: "buriedResistance" },
  { name: "铜浆塞孔", value: "cuPlugHole" },
  { name: "银浆塞孔", value: "silverPlugHole" },
];
export default {
  name: "projectMake",
  components: {
    MakeupPic,
    MakeAction,
    OrderDetail,
    QueryInfo,
    ModifyInfoMake,
    LeftTableMake,
    ChargebackMake,
    EditParametersMake,
    mattersNeedingAttentionInfo,
    GenerateStackInfo,
    RepairRecordInfo,
    CustomerRulesInfo,
    viewLogInfo,
    ProductionCompleted,
    productRemark,
    xishuModify,
    MarketMake,
    Performance,
    ReportHmnoticeform,
    ReportLhdcnoticeform,
  },
  inject: ["reload"],
  data() {
    return {
      ooeder: "",
      LHDCnoticedata: {},
      LHDCnoticeVisible: false,
      HMnoticeVisible: false,
      HMnoticedata: {},
      confload: false,
      checkType: "",
      check1: false,
      isCtrlPressed: false,
      showText: false,
      text: "",
      ooeder1: "",
      proAdminName: "",
      pcbFileName1: "",
      meslist: "",
      checkData: [],
      connection: null,
      confirmLoading: false,
      factoryList: [],
      spinning: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      query: {
        OrderNo: "",
      },
      columns1,
      orderListData: [],
      orderListTableLoading: false,
      columns2,
      orderDetailData: [],
      orderDetailTableLoading: false,
      // peopleOrderInfoList:[],
      // peopleOrderInfoTableLoading:false,
      columns4,
      columns5,
      columns6,
      jobData: [],
      jobautoData: [],
      jobTableLoading: false,
      pronotesData: [],
      note: "",
      cnNote: "",
      erpKey: "",
      proOrderId: "",
      assignLoading: false,
      selectedRowsData: {},
      makeupVisible: false, // 拼版图弹窗开关
      allNote: "",
      dataVisible: false, // 查询弹窗开关
      dataVisible1: false, // 修改信息开关
      dataVisible2: false, // 退单开关
      dataVisible3: false, // 编辑参数开关
      dataVisibleMatter: false, // 注意事项开关
      dataVisible5: false, // 生成叠层开关
      dataVisible6: false, // 返修记录开关
      dataVisible7: false, // 客户规则开关
      dataVisible8: false, // 查看日志
      dataVisible9: false, // 制作完成
      dataVisible10: false, // 生产备注
      dataVisible11: false, // 系数修改
      dataVisible13: false,
      ParameterData: {},
      menuVisible: false,
      menuVisible1: false,
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      stackListData: {}, // 生成叠层
      RepairRecordData: [], // 返修记录
      StackImpedanceData: [], // 叠层阻抗
      CuttingData: [], // 开料拼版
      CustomerData: [], // 客户规则
      viewLogData: [], // 日志数据
      wsUrl: "",
      websocket: null,
      showNotes: "",
      selectId: "",
      ParameterData2: {}, //制作完成获取订单的参数
      message: "11",
      code: "",

      visibleRemark: false,
      confirmLoadingRemark: false,
      dataRemark: [],
      flowSelect: [],
      orderNoRemark: "",
      cookieId: "",
      dataVisibleMode: false,
      dataVisibleNo: false,
      dataVisible12: "",
      messageMode: "",
      orderno: "",
      recordId: "",
      queryParam: {},
      pageStat: false,
      editData: {},
      showModel: true,
      dataVisibleqae: false,
      IsQae: false,
      IsQAEEnble: false,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      var leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerWidth < 1912) {
        leftContent.style.height = window.innerHeight - 94 + "px";
      } else {
        leftContent.style.height = "819px";
      }
      let pageCurrent = localStorage.getItem("pageCurrent2");
      if (pageCurrent) {
        this.pagination.current = pageCurrent;
      }
      this.getOrderList();
      this.getMakeInfo();
      this.getJobAutoInfo();
      var paginationstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var paginationstyle1 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1]
          .children[0];
      var paginationstyle2 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[2].children[1]
          .children[0];
      if (paginationstyle) {
        paginationstyle.style.height = window.innerHeight - 174 + "px";
        paginationstyle1.style.height = window.innerHeight - 174 + "px";
        paginationstyle2.style.height = window.innerHeight - 174 + "px";
      }
    });
  },
  computed: {
    orderDetailDataFilter() {
      let arr_ = [];
      if (this.orderDetailData.length != 0) {
        projectArray.forEach(item => {
          arr_.push({
            projectName: item.name,
            parameter: this.orderDetailData[item.value],
            count: this.orderDetailData.fileUploadedCount,
            boardArea: this.orderDetailData.boardArea,
          });
        });
        arr_ = arr_.filter(
          item => item.parameter != "否" && item.parameter != "" && item.parameter != "不需要" && item.parameter != null && item.parameter != "无"
        );
      }
      return arr_;
    },
  },
  watch: {
    message(newName, oldName) {
      var str = newName.split(",")[0];
      this.orderListData = this.orderListData.filter(item => item.orderNo != str);
    },
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  mounted() {
    window.addEventListener("resize", this.dehandleResize, true);
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    this.getcookie("orderno");
  },

  methods: {
    checkClick() {
      if (this.checkType == "zzwc") {
        this.dataVisibleNo = false;
        this.Isitqae(this.$refs.orderTable.selectedRowKeysArray[0]);
      }
      if (this.checkType == "ks") {
        this.dataVisibleNo = false;
        this.type = "1";
        this.handleOkMode();
      }
      if (this.checkType == "qdqd") {
        this.spinning = true;
        this.dataVisibleNo = false;
        TakeOrderList()
          .then(res => {
            if (res.code) {
              this.$message.success("成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    handleResize() {
      var maintablestyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var paginationstyle1 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1]
          .children[0];
      var paginationstyle2 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[2].children[1]
          .children[0];
      var leftContent = document.getElementsByClassName("leftContent")[0];
      if (window.innerWidth < 1912 && this.orderListData.length != 0) {
        leftContent.style.height = window.innerHeight - 94 + "px";
      } else {
        leftContent.style.height = "819px";
      }
      if (maintablestyle && this.orderListData.length != 0) {
        maintablestyle.style.height = window.innerHeight - 174 + "px";
        paginationstyle1.style.height = window.innerHeight - 174 + "px";
        paginationstyle2.style.height = window.innerHeight - 174 + "px";
      } else {
        maintablestyle.style.height = 0;
        paginationstyle1.style.height = "15px";
        paginationstyle2.style.height = "15px";
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.action.nums * 104;
      if (window.innerWidth < 1920) {
        if (num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        this.reportHandleCancel();
        this.dataVisible = true;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisibleMode) {
        this.handleOkMode();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisibleqae) {
        this.handleOkqae();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (
        e.keyCode == "81" &&
        this.isCtrlPressed &&
        checkPermission("MES.EngineeringModule.EngineeringProduction.GetEngineeringProductionOrder")
      ) {
        this.TakeOrderClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "83" && this.isCtrlPressed && checkPermission("MES.EngineeringModule.EngineeringProduction.MakeStart")) {
        this.MakeStartClick();
        this.dataVisible = false;
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    onClickRow(record) {
      return {
        on: {
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.rightClick1(e, text, record);
          },
        },
      };
    },
    rightClick1(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.localName == "path") {
        event = e.target.parentNode.parentNode;
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split(" ")[0];
      }
      const tableWrapper = this.$refs.tableWrapper;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible1 = true;
      let offsetx = event.offsetLeft + event.offsetWidth + 1300;
      let offsety = event.offsetTop + 390;
      if (event.cellIndex == this.columns4.length - 1) {
        this.menuStyle1.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle1.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle1.top = cellRect.top - wrapperRect.top + 350 + "px";
        this.menuStyle1.left = cellRect.left - wrapperRect.left + event.offsetWidth + 1300 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle1.top = offsety + "px";
        this.menuStyle1.left = offsetx + "px";
      }
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    down1() {
      let input = document.createElement("input");
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    // 返修登记
    RegisterClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      let OrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      let id = this.$refs.orderTable.selectedRowsData.proOrderId;
      const routeOne = this.$router.resolve({
        path: "/registerDetails",
        query: {
          OrderNo: OrderNo,
          pid: id,
          modu: "make",
        },
      });
      window.open(routeOne.href, "_blank", routeOne.query);
    },
    checkPermission,
    // 获取当日制作数据
    getMakeInfo() {
      makeInfo().then(res => {
        if (res.code) {
          this.showNotes = res.data;
        }
      });
    },
    getprocessstepnotes(orderno) {
      processstepnotes(orderno)
        .then(res => {
          if (res.code == 1) {
            this.visibleRemark = true;
            this.dataRemark = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.$message.error(res.message);
        });
    },
    handleCancelRemark(val) {
      this.visibleRemark = val;
    },
    handleOkRemark(data) {
      this.confirmLoadingRemark = true;
      postProcess(data).then(res => {
        this.confirmLoadingRemark = false;
        if (res.code == 1) {
          this.visibleRemark = false;
          this.$message.success("更新成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取订单
    // getOrderList(queryData){
    //   let params = {
    //     ...this.pagination,
    //   }
    //   if(queryData) {
    //     params.OrderNo = queryData.OrderNo;
    //     params.States = queryData.States
    //     params.TradeTypeSrc=queryData.TradeTypeSrc
    //   }
    //   this.orderListTableLoading = true;
    //   projectMakeOrderList (params).then(res => {
    //     if (res.items) {
    //       this.orderListData = res.items;
    //       this.pagination.total = res.totalCount;
    //       this.orderListData.forEach(item=>{
    //         if(item.isReOrder == 0){
    //           item.isReOrder = false
    //         }else{
    //           item.isReOrder = true
    //         }
    //       })
    //       //console.log('this.orderListData',this.orderListData)
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   }).finally(()=> {
    //     this.orderListTableLoading = false;
    //   })
    // },

    getOrderList(queryData) {
      this.pageStat = localStorage.getItem("stat2") == "true" ? true : false;
      if (this.pageStat) {
        let pageCurrent = localStorage.getItem("pageCurrent2");
        if (pageCurrent != undefined && pageCurrent != "") {
          this.pagination.current = parseInt(pageCurrent);
        }
        let pageSize = localStorage.getItem("pageSize2");
        if (pageSize != undefined && pageSize != "") {
          this.pagination.pageSize = parseInt(pageSize);
        }
        let data = localStorage.getItem("queryParam2");
        if (data != null && data != undefined && data != "") {
          this.queryParam = JSON.parse(data);
        }
      }
      this.queryParam.pageIndex = this.pagination.current;
      this.queryParam.pageSize = this.pagination.pageSize;
      let data = {
        ...this.queryParam,
      };
      localStorage.setItem("queryParam2", JSON.stringify(data));
      let params = {
        ...queryData,
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      this.orderListTableLoading = true;
      let record = localStorage.getItem("record2");
      let indexId = localStorage.getItem("OrderId2");
      params.PcbFileName = params.PcbFileName ? params.PcbFileName.replace(/\s+/g, " ").trim() : "";
      projectMakeOrderList(params)
        .then(res => {
          if (res.items) {
            this.orderListData = res.items;
            const pagination = { ...this.pagination };
            pagination.total = res.totalCount;
            this.pagination = pagination;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            if ((params.OrderNo || params.States || params.PcbFileName) && this.orderListData.length) {
              this.$refs.orderTable.selectedRowKeysArray[0] = this.orderListData[0].proOrderId;
              this.$refs.orderTable.proOrderId = this.orderListData[0].proOrderId;
              this.$refs.orderTable.selectedRowsData.orderNo = this.orderListData[0].orderNo;
              this.$refs.orderTable.selectedRowsData = this.orderListData[0];
            }
          } else {
            this.$message.error(res.message);
          }
          if (indexId !== "" && indexId != null) {
            this.$refs.orderTable.proOrderId = indexId;
            this.$refs.orderTable.selectedRowKeysArray[0] = indexId;
          }
          if (record != null) {
            this.$refs.orderTable.selectedRowsData = JSON.parse(record);
            // this.getOrderDetail(JSON.parse(record))
          }
          if (this.pageStat) {
            localStorage.removeItem("OrderId2");
            localStorage.removeItem("record2");
            localStorage.removeItem("pageCurrent2");
            localStorage.removeItem("pageSize2");
            localStorage.removeItem("stat2");
            localStorage.removeItem("queryParam2");
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    // 获取订单详情
    getOrderDetail(record) {
      this.orderDetailTableLoading = true;
      var obj = {};
      proOrderInfo(record.proOrderId)
        .then(res => {
          if (res.data) {
            this.note = res.data.proOrderInfoDto.note;
            this.cnNote = res.data.proOrderInfoDto.cnNote;
            this.noteHtml(res.data.proOrderInfoDto);
            obj.boardLayers = res.data.proOrderInfoDto.boardLayers; // 层数
            obj.num = res.data.proOrderInfoDto.num;
            obj.orderNo = res.data.proOrderInfoDto.orderNo; // 本场编号
            obj.pcbFileName = res.data.proOrderInfoDto.pcbFileName; // 客户型号
            obj.boardThickness = res.data.proOrderInfoDto.boardThickness; // 板厚
            // obj.flyingProbeStr = res.data.proOrderInfoDto.flyingProbeStr;// 测试方式
            obj.boardHeight = res.data.proOrderInfoDto.boardHeight + "x" + res.data.proOrderInfoDto.boardWidth; // 单元尺寸
            obj.boardHeightSet = res.data.proOrderInfoDto.boardHeightSet + "x" + res.data.proOrderInfoDto.boardWidthSet; // 成品尺寸
            if (res.data.proOrderInfoDto.formingTypeStr && res.data.proOrderInfoDto.pinBanType && res.data.proOrderInfoDto.boardTypeStr) {
              obj.pinBanType =
                res.data.proOrderInfoDto.formingTypeStr +
                "," +
                res.data.proOrderInfoDto.pinBanType +
                "(" +
                res.data.proOrderInfoDto.boardTypeStr +
                ")";
            } else if (res.data.proOrderInfoDto.formingTypeStr && res.data.proOrderInfoDto.pinBanType && !res.data.proOrderInfoDto.boardTypeStr) {
              obj.pinBanType = res.data.proOrderInfoDto.formingTypeStr + "," + res.data.proOrderInfoDto.pinBanType;
            } else if (res.data.proOrderInfoDto.pinBanType && res.data.proOrderInfoDto.boardTypeStr && !res.data.proOrderInfoDto.formingTypeStr) {
              obj.pinBanType = res.data.proOrderInfoDto.pinBanType + "(" + res.data.proOrderInfoDto.boardTypeStr + ")";
            } else {
              obj.pinBanType = res.data.proOrderInfoDto.pinBanType;
            }
            // obj.pinBanType = res.data.proOrderInfoDto.formingTypeStr+ ','+res.data.proOrderInfoDto.pinBanType + '('+res.data.proOrderInfoDto.boardTypeStr+')'; // 拼版方式
            obj.pinBanNum = res.data.proOrderInfoDto.pinBanNum; // 合拼款数
            obj.isImpedance = res.data.proOrderInfoDto.isImpedance ? "是" : ""; //阻抗
            obj.surfaceFinishStr = res.data.proOrderInfoDto.surfaceFinishStr; //表面处理
            obj.fR4TypeStr =
              res.data.proOrderInfoDto.sheetTraderStr + "" + res.data.proOrderInfoDto.fR4TypeStr + "" + res.data.proOrderInfoDto.fR4TgStr; //板材信息
            obj.boardBrandStr = res.data.proOrderInfoDto.boardBrandStr; //板材型号
            obj.solderColorStr = res.data.proOrderInfoDto.solderColorStr + "/" + res.data.proOrderInfoDto.solderColorBottomStr; //阻焊颜色顶/底
            obj.fontColorStr = res.data.proOrderInfoDto.fontColorStr + "/" + res.data.proOrderInfoDto.fontColorBottomStr; //字符颜色顶/底
            obj.solderCoverStr = res.data.proOrderInfoDto.solderCoverStr; // 过孔处理
            obj.formingTypeStr = res.data.proOrderInfoDto.formingTypeStr; // 成型方式
            // obj.num = res.data.proOrderInfoDto.num +' (' + res.data.proOrderInfoDto.boardArea +'㎡)'   // 数量加面积
            obj.isDiscHole = res.data.proOrderInfoDto.isDiscHole ? "是" : ""; // 盘中孔
            obj.isHalfHole = res.data.proOrderInfoDto.isHalfHole ? "是" : ""; // 半边孔
            obj.isProfileHole = res.data.proOrderInfoDto.isProfileHole ? "是" : ""; // 异形孔
            obj.isPlateEdge = res.data.proOrderInfoDto.isPlateEdge ? "是" : ""; // 板边包金
            obj.stepHole = res.data.proOrderInfoDto.stepHole ? "是" : ""; // 沉孔
            obj.isSerialNumber = res.data.proOrderInfoDto.isSerialNumber ? "是" : ""; // 印序列号
            obj.isBlueGum = res.data.proOrderInfoDto.isBlueGum ? "是" : ""; // 蓝胶
            obj.isGoldfinger = res.data.proOrderInfoDto.isGoldfinger ? "是" : ""; // 金手指
            obj.goldenFingerAreaRe = res.data.proOrderInfoDto.goldenFingerAreaRe; // 金手指面积
            obj.goldFingerThicknessStr = res.data.proOrderInfoDto.goldFingerThicknessStr; // 金手指金厚
            obj.goldfingerNickelThickness = res.data.proOrderInfoDto.goldfingerNickelThickness; // 金手指镍厚
            obj.isCrimpHole = res.data.proOrderInfoDto.isCrimpHole ? "是" : ""; // 压接孔
            obj.isBackDrilling = res.data.proOrderInfoDto.isBackDrilling ? "是" : ""; // 背钻孔
            obj.isThroughHoleControl = res.data.proOrderInfoDto.isThroughHoleControl ? "是" : ""; // 通孔控深
            obj.isBlindHoleControl = res.data.proOrderInfoDto.isBlindHoleControl ? "是" : ""; // 盲孔控深
            obj.steppedHole = res.data.proOrderInfoDto.steppedHole ? "是" : ""; // 阶梯孔
            obj.isCarbonOil = res.data.proOrderInfoDto.isCarbonOil ? "是" : ""; // 碳油
            obj.heatTape = res.data.proOrderInfoDto.heatTape ? "是" : ""; // 高温胶
            obj.isGoldfingerBevel = res.data.proOrderInfoDto.isGoldfingerBevel ? "是" : ""; // 斜边
            obj.isBlindSlot = res.data.proOrderInfoDto.isBlindSlot ? "是" : ""; // 盲槽
            obj.buriedCopper = res.data.proOrderInfoDto.buriedCopper ? "是" : ""; // 埋铜
            obj.buriedResistance = res.data.proOrderInfoDto.buriedResistance ? "是" : ""; // 埋阻
            obj.cuPlugHole = res.data.proOrderInfoDto.cuPlugHole ? "是" : ""; // 铜浆塞孔
            obj.silverPlugHole = res.data.proOrderInfoDto.silverPlugHole ? "是" : ""; // 银浆塞孔
            if (obj.boardLayers >= 2) {
              obj.copperThicknessStr = res.data.proOrderInfoDto.copperThickness + "/" + res.data.proOrderInfoDto.copperThickness; //外完成铜厚
            } else {
              obj.copperThicknessStr = res.data.proOrderInfoDto.copperThickness + "/0"; //外完成铜厚
            }

            if (obj.boardLayers > 2) {
              var srt = obj.boardLayers - 2;
              var a = res.data.proOrderInfoDto.innerCopperThickness;
              var arr = [];
              for (var i = 0; i < srt; i++) {
                arr.push(a);
              }
              obj.innerCopperThicknessStr = arr.join("/");
            }
            this.orderDetailData = obj;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderDetailTableLoading = false;
        });
    },
    // 取单
    TakeOrderClick() {
      this.spinning = true;
      this.$refs.action.isDisabled = true;
      setTimeout(() => {
        this.$refs.action.isDisabled = false;
      }, 2000);
      ppebuttonCheck("dd87eabd-4d6e-dfc2-3202-3a10fbe46577", "GetEngineeringProductionOrder")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "qdqd";
              this.dataVisibleNo = true;
              this.meslist = "制作取单按钮检查";
            } else {
              TakeOrderList().then(res => {
                if (res.code) {
                  this.$message.success(res.message);
                  this.getOrderList();
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //全套资料下载
    downAll(record) {
      this.orderListTableLoading = true;
      datapath(record.proOrderId)
        .then(res => {
          if (res.code) {
            if (res.data) {
              let name = record.orderNo + ".zip";
              const xhr = new XMLHttpRequest();
              xhr.open("GET", res.data, true);
              xhr.responseType = "blob";
              xhr.onload = function () {
                if (xhr.status === 200) {
                  const blob = xhr.response;
                  const link = document.createElement("a");
                  link.href = window.URL.createObjectURL(blob);
                  link.download = name;
                  link.style.display = "none";
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  window.URL.revokeObjectURL(link.href);
                }
              };
              xhr.send();
              this.orderListTableLoading = false;
            } else {
              this.$message.error("暂无文件可下载");
              this.orderListTableLoading = false;
            }
          } else {
            this.$message.error(res.message);
            this.orderListTableLoading = false;
          }
        })
        .finally(() => {
          //this.orderListTableLoading = false;
        });
    },
    //问客
    wenkeclick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      let data = this.$refs.orderTable.selectedRowsData;
      this.$router.push({
        path: "eqDetails",
        query: {
          OrderNo: data.orderNo,
          eQSource: 1,
          businessOrderNo: data.businessOrderNo,
          joinFactoryId: data.joinFactoryId,
          Jump: "制作页",
          id: data.proOrderId,
        },
      });
    },
    //生产单
    ProductionOrder() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowKeysArray.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 69) {
        this.HMnoticeform();
      } else if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 38) {
        this.LHDCnoticeform();
      } else {
        noticereviewinfo(this.$refs.orderTable.selectedRowKeysArray[0], 2).then(res => {
          if (res.code) {
            this.downloadByteArrayFromString(res.data, res.message);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //红马生产通知单预览
    HMnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowKeysArray[0], 2).then(res => {
        if (res.code) {
          this.HMnoticedata = res.data;
          this.HMnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    hmnoticedown() {
      this.$refs.hmnotice.getnoticePdf();
    },
    //联合多层生产通知单预览
    LHDCnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowKeysArray[0], 2).then(res => {
        if (res.code) {
          this.LHDCnoticedata = res.data;
          this.LHDCnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    lhdcnoticedown() {
      this.$refs.lhdcnotice.getnoticePdf();
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    // 开始
    MakeStartClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "MakeStart")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "ks";
              this.dataVisibleNo = true;
              this.meslist = "订单开始按钮检查";
            } else {
              this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
              this.messageMode = "确定开始制作吗？";
              this.type = "1";
              this.dataVisibleMode = true;
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    Confirmmodification() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (!this.$refs.orderTable.selectedRowsData.isOrderModify) {
        this.$message.warning("该订单不允许修改");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确定修改吗？";
      this.type = "5";
      this.dataVisibleMode = true;
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible = false; // 查询弹窗
      this.dataVisible1 = false; // 修改信息弹窗
      this.dataVisible2 = false; // 退单开关
      this.dataVisible3 = false; // 编辑参数开关
      this.dataVisibleMatter = false; // 注意事项
      this.dataVisible5 = false; // 生成叠层
      this.dataVisible6 = false; // 返修记录
      this.dataVisible7 = false; // 客户规则
      this.dataVisible8 = false; // 查看日志
      this.dataVisible9 = false; // 制作完成
      this.dataVisible10 = false;
      this.dataVisible11 = false;
      this.dataVisible12 = false;
      this.dataVisible13 = false; //市场修改
      this.dataVisibleMode = false;
      this.dataVisibleNo = false;
      this.checkData = [];
      this.HMnoticeVisible = false;
      this.LHDCnoticeVisible = false;
      this.dataVisibleqae = false;
    },
    handleOk12() {
      this.dataVisible12 = false;
    },

    //市场修改
    MarketmodClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.dataVisible13 = true;
    },
    handleOk13() {
      var data = this.$refs.MarketMake.MarketForm;
      if (data.content != "" || data.filePaths != "") {
        this.confirmLoading = true;
        let params = {
          proOrderNo: this.$refs.orderTable.selectedRowsData.orderNo,
          orderNo: this.$refs.orderTable.selectedRowsData.businessOrderNo,
          content: data.content,
          filePath: data.filePaths,
          orderModifyType: 2,
        };
        this.spinning = true;
        Marketmake(params)
          .then(res => {
            if (res.code) {
              this.$message.success("市场修改提交成功");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
            this.confirmLoading = false;
            this.dataVisible13 = false;
          });
      } else {
        this.$message.error("请至少填写一项");
      }
    },
    handleOkMode() {
      // 开始
      if (this.type == "1") {
        this.spinning = true;
        MakeStart(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("标记开始成功");
            } else {
              this.$message.error(res.message);
            }
            this.getOrderList();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.type == "2") {
        this.spinning = true;
        RepairCompleted(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("返修完成");
              this.getOrderList();
            } else {
              if (res.data && res.data.length) {
                this.checkData = res.data;
                this.dataVisibleNo = true;
                this.meslist = "返修完成确认";
              } else {
                this.$message.error(res.message);
              }
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.type == "3") {
        this.spinning = true;
        delInfo(this.recordId)
          .then(res => {
            if (res.code) {
              this.$message.success("已删除");
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      //解除警告
      if (this.type == "4") {
        this.spinning = true;
        makesetreleasewarning(this.$refs.orderTable.selectedRowKeysArray[0])
          .then(res => {
            if (res.code) {
              this.$message.success("解除警告成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      //确认修改
      if (this.type == "5") {
        this.spinning = true;
        setsureordermodify(this.$refs.orderTable.selectedRowsData.proOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success("修改成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      this.dataVisibleMode = false;
    },
    // 查询
    queryClick() {
      this.dataVisible = true;
    },
    handleOk() {
      var payload = this.$refs.queryInfo.OrderNumber;
      var arr1 = payload.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      payload = arr1.join("");
      (this.ooeder1 = this.$refs.queryInfo.OrderStates),
        (this.pcbFileName1 = this.$refs.queryInfo.pcbFileName),
        (this.proAdminName = this.$refs.queryInfo.proAdminName),
        (this.ooeder = payload);
      if (payload && typeof payload === "string" && payload.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产编号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      this.showModel = false;
      this.pagination.current = 1;
      let queryData = {
        OrderNo: payload,
        States: this.$refs.queryInfo.OrderStates,
        PcbFileName: this.$refs.queryInfo.pcbFileName,
        proAdminName: this.$refs.queryInfo.proAdminName,
      };
      this.getOrderList(queryData);
      this.$nextTick(() => {
        this.showModel = true;
      });
    },
    // 修改信息
    modifyInfoClick(record) {
      if (this.factoryList.find(item => item.valueMember == record.joinFactoryId)) {
        record.joinFactoryId = this.factoryList.find(item => item.valueMember == record.joinFactoryId).valueMember;
      } else {
        record.joinFactoryId = "";
      }
      this.selectedRowsData = record;
      // //console.log('this.selectedRowsData:',this.selectedRowsData)
      // //console.log(record.joinFactoryId)
      this.dataVisible1 = true;
    },

    handleOk1() {
      this.confirmLoading = true;
      // //console.log(this.$refs.ModifyInfoMake.infoForm,this.selectedRowsData.proOrderId)
      let params = this.$refs.ModifyInfoMake.infoForm;
      params.id = this.selectedRowsData.proOrderId;
      this.spinning = true;
      getModifyInformation(params)
        .then(res => {
          if (res.code) {
            this.$message.success("修改成功");
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible1 = false;
        });
    },
    // 返修完成
    RepairCompletedClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确定返修完成吗？";
      this.dataVisibleMode = true;
      this.type = "2";
    },
    Releasewarning() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确认解除警告吗？";
      this.dataVisibleMode = true;
      this.type = "4";
    },
    // 制作标准
    ProductionStandardClick() {
      getProductionStandard().then(res => {
        if (res.code) {
          window.open(res.data, "_blank").location;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //叠层复制
    OverlayCopyClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.spinning = true;
      ReorderUpdateStack(this.$refs.orderTable.selectedRowKeysArray[0])
        .then(res => {
          if (res.code) {
            this.$message.success("复制成功");
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
        });
    },

    // 注意事项
    mattersNeedingAttentionClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.dataVisibleMatter = true;
    },
    handleOk4() {
      this.confirmLoading = true;
      let params = this.$refs.mattersNeedingAttention.form;
      params.id = this.$refs.orderTable.selectedRowKeysArray[0];
      this.spinning = true;
      mattersNeedingAttention(params)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
          //this.getJobInfo(this.$refs.orderTable.selectedRowKeysArray[0])
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisibleMatter = false;
        });
    },

    getmultiples(record) {
      this.selectedRowsData = record;
      this.dataVisible11 = true;
    },
    handleOk10() {
      let params = {
        id: this.$refs.xishuModify.proOrderId,
        scoreMultiples: this.$refs.xishuModify.scoreMultiples,
      };
      //console.log('系数倍数点击事件',params)
      multiples(params)
        .then(res => {
          if (res.code) {
            this.$message.success("修改成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible11 = false;
        });
    },

    // 编辑参数
    EditParametersClick(record, code) {
      this.selectedRowsData = record;
      this.code = code;
      getParameter(this.selectedRowsData.proOrderId).then(res => {
        if (res.code) {
          // this.dataVisible3 = true
          // this.ParameterData = res.data
          this.dataVisible9 = true;
          this.ParameterData2 = res.data;
          // const arr = res.data
          // var obj = {}
          //   arr.forEach(item => {
          //   obj = {...obj, ...item}
          // })
          // //console.log(obj)

          //console.log('this.ParameterData',this.ParameterData2)
          // this.$message.success('')
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk3() {
      this.confirmLoading = true;
      this.spinning = true;
      let formData = this.$refs.EditParametersMake.ParameterForm;
      let params = {
        FactoryNo: formData.factoryNo ? "true" : "false",
        IsShuChu: formData.isShuChu ? "true" : "false",
        VCutToWX: formData.vCutToWX ? "true" : "false",
        PosNeg: formData.posNeg || "",
        OilBlockGreaterThan20_20: formData.oilBlockGreaterThan20_20 ? "true" : "false",
        BMCLToWZ: formData.bMCLToWZ ? "true" : "false",
        MinLineWS: formData.minLineWS || "",
        InnerMinLineWS: formData.innerMinLineWS || "",
        ThickCopperBaseThickness: formData.thickCopperBaseThickness || "",
        BgalcSmallAlert: formData.bgalcSmallAlert ? "true" : "false",
        VCut: formData.vCut ? "true" : "false",
        CounterboreCopper: formData.counterboreCopper ? "true" : "false",
        CounterboreNoCopper: formData.counterboreNoCopper ? "true" : "false",
        Etching2RL: formData.etching2RL ? "true" : "false",
        FakeDoubleSide: formData.fakeDoubleSide ? "true" : "false",
        Special2RL: formData.special2RL ? "true" : "false",
        IsPlankWatermark: formData.isPlankWatermark ? "true" : "false",
        // 'msg_':formData.msg_ || '',
      };
      params["Id"] = this.selectedRowsData.proOrderId;
      //console.log('params',params)
      // this.spinning = true
      SaveParameter(params)
        .then(res => {
          if (res.code) {
            this.$message.success("编辑保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible3 = false;
        });
    },
    // 退单
    ChargebackClick(record) {
      this.selectedRowsData = record;
      this.dataVisible2 = true;
    },
    handleOk2() {
      if (
        (this.$refs.ChargebackMake.ChargebackForm.remark != "" && this.$refs.ChargebackMake.ChargebackForm.isMemberConfirm == true) ||
        this.$refs.ChargebackMake.ChargebackForm.isParamError == true ||
        this.$refs.ChargebackMake.ChargebackForm.isFileError == true
      ) {
        this.confirmLoading = true;
        let params = this.$refs.ChargebackMake.ChargebackForm;
        params.id = this.selectedRowsData.proOrderId;
        this.spinning = true;
        BackStart(params)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
            this.confirmLoading = false;
            this.dataVisible2 = false;
          });
      } else {
        this.$message.error("备注不能为空且至少勾选一项");
      }
    },
    // 查看日志
    viewLogClick(payload) {
      let params = payload.proOrderId;
      if (params) {
        getViewLog(params).then(res => {
          if (res.code) {
            this.MsgSort(res.data);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 排序
    MsgSort(obj) {
      obj.sort((a, b) => {
        let t1 = new Date(Date.parse(a.createTime.replace(/-/g, "/")));
        let t2 = new Date(Date.parse(b.createTime.replace(/-/g, "/")));
        return t2.getTime() - t1.getTime();
      });
      // console.error('obj',obj)
      this.dataVisible8 = true;
      return (this.viewLogData = obj);
    },
    handleOk8() {
      this.dataVisible8 = false;
    },
    // 生成叠层
    GenerateStackClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      getGenerateStack(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        this.dataVisible5 = true;
        this.stackListData = res.data;
      });
    },
    handleOk5() {
      this.confirmLoading = true;
      this.spinning = true;
      //console.log('this.$refs.GenerateStack.selectedRowKeysArray:',this.$refs.GenerateStack.paramInfo)
      let params = {
        ProOrderId: this.$refs.orderTable.selectedRowKeysArray[0],
        id: this.$refs.GenerateStack.selectedRowKeysArray[0],
        paramInfo: this.$refs.GenerateStack.paramInfo,
        isSpecistack: this.$refs.GenerateStack.isSpecistack,
      };
      saveRepairRecord(params)
        .then(res => {
          if (res.code) {
            this.$message.success("叠层保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible5 = false;
        });
    },
    // 返修记录
    RepairRecordClick(record) {
      let OrderNo = record.orderNo;
      let id = record.proOrderId;
      const routeOne = this.$router.resolve({
        path: "/registerDetails",
        query: {
          OrderNo: OrderNo,
          pid: id,
          modu: "back",
        },
      });
      window.open(routeOne.href, "_blank", routeOne.query);
      // getRepairRecord(record.proOrderId).then(res=>{
      //   if(res.code){
      //     this.RepairRecordData = res.data
      //     this.dataVisible6=true;
      //   }else{
      //     this.$message.error(res.message)
      //   }
      // })
    },
    handleOk6() {
      this.dataVisible6 = false;
    },
    // 叠层阻抗
    StackImpedanceClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      getStackImpedance(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          if (res.data.drills.length) {
            let drillsData = res.data.drills;
            localStorage.setItem("drillsData", JSON.stringify({ drillsData }));
          } else {
            localStorage.removeItem("drillsData");
          }
          this.StackImpedanceData = res.data;
          // //console.log('res.data1111:',res.data)
          const routeOne = this.$router.resolve({
            path: "/impedance",
            query: {
              boardType: res.data.boardType,
              finishBoardThickness: res.data.finishBoardThickness,
              layers: res.data.layers,
              pdctno: res.data.pdctno,
              InCopperThickness: res.data.innerCopperThickness,
              OutCopperThickness: res.data.copperThickness,
              href: "projectMake",
            },
          });
          window.open(routeOne.href, "_blank", routeOne.query);
          // this.$router.push({path: '/impedance', query:{boardType:res.data.boardType,finishBoardThickness:res.data.finishBoardThickness,layers:res.data.layers,pdctno:res.data.pdctno}})
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 开料拼版
    CuttingClick() {
      getCutting(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          this.CuttingData = res.data;
          this.$router.push({
            path: "/gongju/cutting",
            query: {
              boardThink: res.data.boardThink,
              cpLen: res.data.cpLen,
              cpWidth: res.data.cpWidth,
              job: res.data.job,
              layCount: res.data.layCount,
              pcsset: res.data.pcsset,
              pcssetName: res.data.pcssetName,
              joinFactoryId: res.data.joinFactoryId,
              numUDSix: res.data.numUDSix,
              txtCoupDL: res.data.txtCoupDL,
              txtCoupDW: res.data.txtCoupDW,
            },
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    ceshi() {
      this.dataVisible11 = true;
    },
    // 客户规则
    CustomerRulesClick(record) {
      this.dataVisible7 = true;
      let CustNo = record.custNo;
      let factory = record.joinFactoryId;
      //console.log('CustNo',CustNo,'factory',factory);
      getCustomerInfo(CustNo, factory, 1, record.businessOrderNo, record.orderNo).then(res => {
        if (res.code) {
          this.CustomerData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk7() {
      this.dataVisible7 = false;
    },
    // 获取对应的订单人员
    getJobInfo(id) {
      this.jobTableLoading = true;
      projectBackEndJobInfo(id)
        .then(res => {
          if (res.code) {
            this.jobData = res.data;
          }
        })
        .finally(() => {
          this.jobTableLoading = false;
        });
    },
    getJobAutoInfo() {
      getJobAutoInfo()
        .then(res => {
          if (res.code) {
            this.jobautoData = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {});
    },
    // 图片字符串裁切
    picFilter(val) {
      if (val.picUrl) {
        return val.picUrl.split(",");
      } else {
        return [];
      }
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.pageStat = false;
      localStorage.removeItem("stat2");
      let queryData = {
        OrderNo: this.ooeder,
        States: this.ooeder1,
        PcbFileName: this.pcbFileName1,
        proAdminName: this.proAdminName,
      };
      if (JSON.stringify(queryData) != "{}") {
        this.getOrderList(queryData);
      } else {
        this.getOrderList();
      }
    },
    noteHtml(record) {
      let cnNoteRegStr = "";
      let noteRegStr = "";
      if (this.cnNote && this.cnNote.indexOf("https://admin.jiepei.com/") == -1) {
        cnNoteRegStr = this.cnNote.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${"http://admin.jiepei.com/"}$2$3`);
      } else {
        cnNoteRegStr = this.cnNote;
      }

      if (this.note && this.note.indexOf("https://admin.jiepei.com/") == -1) {
        noteRegStr = this.note.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${"http://admin.jiepei.com/"}$2$3`);
      } else {
        noteRegStr = this.note;
      }
      // const noteRegStr = this.note.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${'http://admin.jiepei.com/'}$2$3`)
      // const cnNoteRegStr = this.cnNote.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${'http://admin.jiepei.com/'}$2$3`)
      // <div class="${record.errChk_ ? '' : 'displayFlag'}"><p>分析项</p><div>${record.errChk_}</div></div>
      // <div class="${record.errMsg_ ? '' : 'displayFlag'}"><p>输出提示</p><div>${record.errMsg_}</div></div>
      // 工程制作 直通提示不需要关闭 2023/6/12   <div class="${record.autoTaskMsg ? 'divClass' : 'displayFlag'}"><p>直通提示</p><div>${record.autoTaskMsg}</div></div>
      let str_ = `<div class="${this.note ? "divClass" : "displayFlag"}"><p>客户备注</p><div>${noteRegStr}</div></div>
                  <div class="${this.cnNote ? "divClass" : "displayFlag"}"><p>业务员备注</p><div>${cnNoteRegStr}</div></div>
                  <div class="${record.outTaskMsg ? "divClass" : "displayFlag"}"><p>输出提示</p><div>${record.outTaskMsg}</div></div>
                  <div class="${record.specialRemarks ? "divClass" : "displayFlag"}"><p>工程指示</p><div>${record.specialRemarks}</div></div>
                `;
      this.allNote = str_;
    },
    jigsawPuzzleClick(record) {
      this.makeupVisible = true;

      this.$nextTick(() => {
        debugger;
        this.$refs.makeup.impositionInformationExample(
          record.boardHeight,
          record.boardWidth,
          record.pinban_x || 1,
          record.pinban_y || 1,
          record.processeEdge_x || "none",
          record.processeEdge_y || 0,
          record.vCut || "none",
          record.cao_x || 0,
          record.cao_y || 0
        );
      });

      //console.log(record)
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    encode(str) {
      // 对字符串进行编码
      var encode = encodeURI(str);
      // 对编码的字符串转化base64
      var base64 = btoa(encode); //这一行就可以字符串转base64了
      return base64;
    },
    webSocketLink(val) {
      let proOrderId = val;
      // if(!proOrderId){
      //   this.$message.warning('请选择订单')
      //   return
      // }
      let params = JSON.stringify({ Token: Cookie.get("Authorization"), Type: "make", Task: "make", Uid: proOrderId, Data: {} });
      //console.log('orz',params);
      let url = "genesiscam://?" + params;
      window.open(url, "_blank");
      setEngineeringMake({
        token: proOrderId,
      });
      this.getcookie("orderno");

      // //console.log('params',this.encode(params))
      // this.$axios({
      //     method: 'post',
      //     url: 'http://127.0.0.1:18188/',
      //     data: params,
      //   }).then((response) => { // 因为层级比较深，匿名函数会导致this指向发生改变// 这个时候使用箭头函数解决
      //     setEngineeringMake ({
      //         token:proOrderId,
      //       });
      //       this.getcookie('orderno')
      //     })
      //     .catch(e => {
      //       this.$message.error('正在打开小程序，请稍等')
      //       let url = 'genesiscam://?' + params
      //       window.open(url,"_blank")
      //       setEngineeringMake ({
      //           token:proOrderId,
      //         });
      //       this.getcookie('orderno')
      //     });
    },
    // 获取cookie缓存订单id
    getcookie(orderno) {
      //获取指定名称的cookie的值
      var arrstr = document.cookie.split("; ");
      for (var i = 0; i < arrstr.length; i++) {
        var temp = arrstr[i].split("=");
        if (temp[0] == orderno) {
          //console.log('temp',unescape(temp[1]))
          this.cookieId = unescape(temp[1]);
          // return unescape(temp[1]);
        }
      }
    },
    // 文件替换
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
      //console.log('file',file)
      if (!isFileType) {
        this.$message.error("只支持.rar或.zip格式文件");
      }
      return isFileType;
    },
    async httpRequest0(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await fileReplacement(this.$refs.orderTable.menuData.proOrderId, formData).then(res => {
        if (res.code == 1) {
          this.$message.success("上传成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 制作完成
    async finishClick(code) {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.spinning = true;
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      ppebuttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "MIFinish")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "zzwc";
              this.dataVisibleNo = true;
              this.meslist = "制作完成按钮检查";
            } else {
              this.Isitqae(this.$refs.orderTable.selectedRowKeysArray[0]);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
      this.code = code;
    },
    Isitqae(id) {
      isqaeenble(id).then(res => {
        if (res.code) {
          this.IsQae = res.data.isQAE;
          this.IsQAEEnble = res.data.isQAEEnble;
          this.dataVisibleqae = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOkqae() {
      let params = {};
      params.id = this.$refs.orderTable.selectedRowKeysArray[0];
      params.IsQae = this.IsQae;
      this.confload = true;
      let fac = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      if ([12,78,79,80].includes(Number(fac))) {
        setcAMMiflowinfostoerp(this.$refs.orderTable.selectedRowsData.joinFactoryId, this.$refs.orderTable.selectedRowsData.orderNo).then(res => {
          if (res.code) {
            mIFinish(params)
              .then(res => {
                if (res.code) {
                  this.$message.success("制作完成");
                  this.$refs.orderTable.selectedRowKeysArray = [];
                  this.getOrderList();
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.confload = false;
                this.dataVisibleqae = false;
              });
          } else {
            this.$message.error(res.message);
            this.confload = false;
            this.dataVisibleqae = false;
          }
        });
      } else {
        mIFinish(params)
          .then(res => {
            if (res.code) {
              this.$message.success("制作完成");
              this.$refs.orderTable.selectedRowKeysArray = [];
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.confload = false;
            this.dataVisibleqae = false;
          });
      }
    },
    handleOk9() {
      let form = this.$refs.ProductionCompleted.ParameterForm;
      let params = {
        FactoryNo: form.factoryNo ? "true" : "false",
        IsShuChu: form.isShuChu ? "true" : "false",
        VCutToWX: form.vCutToWX ? "true" : "false",
        PosNeg: form.posNeg || "",
        OilBlockGreaterThan20_20: form.oilBlockGreaterThan20_20 ? "true" : "false",
        BMCLToWZ: form.bMCLToWZ ? "true" : "false",
        MinLineWS: form.minLineWS || "",
        InnerMinLineWS: form.innerMinLineWS || "",
        ThickCopperBaseThickness: form.thickCopperBaseThickness || "",
        BgalcSmallAlert: form.bgalcSmallAlert ? "true" : "false",
        VCut: form.vCut ? "true" : "false",
        CounterboreCopper: form.counterboreCopper ? "true" : "false",
        CounterboreNoCopper: form.counterboreNoCopper ? "true" : "false",
        Etching2RL: form.etching2RL ? "true" : "false",
        FakeDoubleSide: form.fakeDoubleSide ? "true" : "false",
        Special2RL: form.special2RL ? "true" : "false",
        IsPlankWatermark: form.isPlankWatermark ? "true" : "false",
        IsSecant: form.isSecant ? "true" : "false",
        AcceptBblt: form.acceptBblt ? "true" : "false",
        // 'msg_':formData.msg_ || '',
      };
      //console.log(params)
      if (this.code == "1") {
        params["Id"] = this.selectedRowsData.proOrderId;
        SaveParameter(params)
          .then(res => {
            if (res.code) {
              this.$message.success("编辑保存成功");
            } else {
              this.$message.error(res.message);
            }
            this.getOrderList();
          })
          .finally(() => {
            this.spinning = false;
            this.confirmLoading = false;
          });
      } else {
        params.Id = this.$refs.orderTable.selectedRowKeysArray[0];
        params.Path = this.$refs.ProductionCompleted.ParameterForm.path;
        if (!params.Path) {
          this.$message.error("请上传文件");
          return;
        }
        finish(params)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("制作完成");
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      this.dataVisible9 = false;
    },
    // 状态同步
    StatusSynchronization(record) {
      stateSync(record.proOrderId).then(res => {
        if (res.code == 1) {
          this.$message.success("状态同步成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 下载附件文件
    downFile(record) {
      window.location.href = record;
    },
    // 删除注意事项
    delFile(record) {
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确认删除此条注意事项吗？";
      this.type = "3";
      this.recordId = record.id;
      this.dataVisibleMode = true;
    },
    // 绩效管理
    PerformanceClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.selectedRowsData = this.$refs.orderTable.selectedRowsData;
      this.$refs.performanceModal.openModal(this.selectedRowsData);
    },
    editClick(record) {
      this.dataVisibleMatter = true;
      this.editData = record;
    },
    // 注意事项
    handleOkMatter() {
      this.confirmLoading = true;
      let params = this.$refs.mattersNeedingAttention.form;
      params.id = this.editData.id;
      params.isbefor = false;
      this.spinning = true;
      // //console.log('params',params)
      repierMatters(params)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
          //this.getOrderList()
          //this.getJobInfo(this.$refs.orderTable.selectedRowKeysArray[0])
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisibleMatter = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.formclass {
  /deep/.ant-modal-body {
    padding: 0 !important;
  }
  /deep/.ant-modal-footer {
    padding: 10px 100px;
  }
}
/deep/.ant-pagination.mini .ant-pagination-total-text,
.ant-pagination.mini .ant-pagination-simple-pager {
  height: 24px;
  line-height: 24px;
  font-size: 13px;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
.tabRightClikBox1 {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    background-color: white !important;
    color: #000000;
  }
}
/deep/ .ant-table-placeholder {
  width: 100%;
  position: absolute;
  top: 35px;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-spin-container {
  user-select: none;
}
/deep/.ant-input {
  color: #000000;
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}

/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
  .ant-table-column-sorter {
    display: none;
  }
}
.projectMake {
  background: #ffffff;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  /deep/.leftContent {
    .ant-table-body {
      .ant-table-fixed {
        width: 1411px !important;
      }
    }
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: none;
    .tabRightClikBox {
      // border:2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
  /deep/ .userStyle {
    user-select: none !important;
  }
  /deep/.heightSTY {
    line-height: 1.5;
  }
  .rightContent {
    // border-bottom: 2px solid rgb(233, 233, 240);
    border-bottom: 0;
    width: 40%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    /deep/ .centerTable {
      width: 45%;
      height: 780px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
    }
    .rightTable {
      width: 55%;
      height: 780px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
      .jobNote {
        /deep/ .ant-table-row-cell-break-word {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        height: 400px;
        // border-top: 4px solid #e9e9f0;
        /deep/ .ant-table-wrapper {
          height: 200px;
        }
        .minTable {
          /deep/ .ant-table-body {
            min-height: 358px;
          }
        }
      }
      .jobauto {
        /deep/ .ant-table-wrapper {
          height: 200px;
        }
      }
      .pronotes {
        /deep/ .ant-table-wrapper {
          height: 200px;
        }
      }
      .peopleOrderSt {
        height: 220px;
      }
      .note {
        height: 360px;
      }
    }
  }
  .footerAction {
    width: 100%;
    height: 48px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
    margin-top: -40px;
  }
  //.leftTable {
  //  width: 60%;
  //  border: 2px solid rgb(233, 233, 240)
  //}
  /deep/.ant-table-tbody {
    user-select: none !important;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display: none;
    }
    // .ant-tabs-nav-container{
    //   height: 28px;
    //   .ant-tabs-tab {
    //     margin: 0;
    //     height: 28px;
    //     line-height: 28px;
    //     width:302px;
    //     text-align: center;
    //   }
    // }
  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
      .eqBackground {
        background: #ffff00;
      }
      .statuBackground {
        background: #b0e0e6;
      }
      .backUserBackground {
        background: #a7a2c9;
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    .userStyle {
      user-select: none !important;
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table {
    // .ant-table-thead > tr > th{
    //   padding: 6px 4px;
    //   border-color: #f0f0f0;
    // }
    .ant-table-tbody > tr > td {
      padding: 7px 2px !important;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        color: #000000;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 6px 0 0 10px;
    position: relative;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
<style lang="less">
.note {
  .textNote {
    // padding-top:4px;
    user-select: text !important;
    background: #d6d6d6;
    word-wrap: break-word;
    max-height: 360px;
    overflow-y: auto;
    p {
      line-height: 35px;
      font-weight: 700;
      margin: 0;
      img {
        width: 100%;
      }
    }
    .divClass {
      margin: 0 5px;
      padding-top: 4px;
    }
    .displayFlag {
      display: none;
    }
  }
}
.modalSty {
  /deep/.ant-modal .ant-modal-content .ant-modal-body {
    padding: 0;
  }
}
</style>
