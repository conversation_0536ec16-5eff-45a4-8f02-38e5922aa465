<!-- 市场管理 - 订单报价- 报价单模板-中文 -->
<template>
  <div class="pdfDom" >
    <div id="pdfDom">
      <div>
        <div class="headerTitle">
          <div  class='divclass' >
            <h2>{{supperDto.detailName}}</h2>
            <p>地址: 香港特别行政区九龙湾上悦道20号国际广场706-707室</p>
            <p>总部: 广东省深圳市福田区天安数码城天发大厦AB座6楼</p>
            <p>电话: 0086-755-83585818 传真: 0086-755-83585267 手机: 13632536515</p>
            <p>网址: www.kinjipcb.com 邮箱: <EMAIL></p>
          </div>
        </div>
        <div>
          <h2 style="text-align: center;font-size:24px;margin-bottom: 0px">报价单</h2>
          <div >
          <table class='box' style="border:1px solid black;">
            <tbody>
              <tr>
                <th style="font-weight:700;width:124px;">买方:</th>
                <th style="font-weight:700;">{{custModuleNoDto.custName}}</th>
                <th style="font-weight:700;width:14%;">卖方:</th>
                <th style="font-weight:700;width:30%;">Kinji Electronics Limited</th>
              </tr>
              <tr>
                <th style="font-weight:700;">联系人:</th>
                <th style="font-weight:700;">{{custModuleNoDto.businessPerson}}</th>
                <th>联系人:</th>
                <th>{{pcbOrderStrDto.checkName}}</th>
              </tr>
              <tr>
                <th style="font-weight:700;">电话:</th>
                <th style="font-weight:700;">{{custModuleNoDto.tel}}</th>
                <th style="font-weight:700;">电话:</th>
                <th style="font-weight:700;">0086-755-83585818</th>
              </tr>
              <tr>
                <th style="font-weight:700;">装运期限:</th>
                <th style="font-weight:700;">{{pcbOrderStrDto.shipmentTerm}}&nbsp;{{pcbOrderStrDto.shipLocation}}</th>
                <th style="font-weight:700;">日期:</th>
                <th style="font-weight:700;">{{dataTime}}</th>
              </tr>                       
            </tbody>
          </table>          
          <table class='box1' style="border:1px solid black;border-top:0;">
                <tbody>
                  <tr>
                    <th style="width:78px;">订单号</th>
                    <th style="width:65px;">物料号</th>
                    <th style="width:160px;">订单参数</th>
                    <th style="width:80px;">订单数(PCS)</th>
                    <th style="width:70px;">成长(mm)</th>
                    <th style="width:70px;">成宽(mm)</th>
                    <th style="width:50px;">连片数</th>
                    <th style="width:60px;">面积(㎡)</th>
                    <th style="width:100px;">单价(PCS)</th>
                    <th style="width:52px;">工程费</th>
                    <th style="width:50px;">测试费</th>
                    <th style="width:50px;">加急费</th>
                    <th style="width:50px;">运费</th>
                    <th style="width:90px;">合计</th>
                    <th style="width:70px;">交期天数</th>
                    <th style="width:73px;">重量(KG)</th>
                  </tr>
                  <tr v-for="(item ,index) in dataSource2" :key="index" >
                    <th v-if="index == 0" :rowspan="index">{{pcbOrderStrDto.pcbFileName}}</th>
                    <th v-if="index == 0" :rowspan="index" >{{pcbOrderStrDto.orderNo}}</th>
                    <th v-if="index == 0" :rowspan="index" style="text-align:left; padding-left:2px;" > 
                      <span v-for="(ite,index) in pcbSpecEnglish" :key="index" >
                        <p style="margin-bottom:0;">{{ite}}</p>
                      </span>
                      <!-- {{pcbSpecEnglish}} -->
                      <!-- <p style="margin-bottom: 0" v-if="pcbOrderStrDto.boardLayers">Layer:{{pcbOrderStrDto.boardLayers}}</p> 
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.setBoardWidth && pcbOrderStrDto.setBoardHeight">UnitSize:{{pcbOrderStrDto.setBoardWidth}}*{{pcbOrderStrDto.setBoardHeight}} mm</p>  
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.fR4TypeStr && pcbOrderStrDto.fR4TgStr">Material:{{pcbOrderStrDto.fR4TypeStr}}({{pcbOrderStrDto.fR4TgStr}})</p> 
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.boardThickness">Board thickness:{{pcbOrderStrDto.boardThickness}}</p>     
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.cuThickness">internal/external copper:{{pcbOrderStrDto.cuThickness}} oz</p> 
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.boardLayers ">Min hole copper: {{pcbOrderStrDto.holeCopper}}</p> 
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.solderColor">Solder mask: {{pcbOrderStrDto.solderColor}}</p> 
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.fontColor">Silkscreen: {{pcbOrderStrDto.fontColor}}</p> 
                      <p style="margin-bottom: 0" v-if="pcbOrderStrDto.surfaceFinish">Surface:{{pcbOrderStrDto.surfaceFinish}};gold:{{pcbOrderStrDto.surfaceFinishJsonDto.goldThickness}} </p>                 -->
                    </th>
                    <th >{{item.para4DelQty_}}</th>
                    <th >{{pcbOrderStrDto.setBoardHeight}}</th>
                    <th >{{pcbOrderStrDto.setBoardWidth}}</th>
                    <th >{{pcbOrderStrDto.su}}</th>
                    <th >{{item.para4Area_}}</th>
                    <th >{{item.pcsPrice_}}</th>
                    <th >{{item.engPrice_}}</th>
                    <th >{{item.testPrice_ }}</th>
                    <th >{{item.expeditePrice_}}</th>
                    <th >{{item.payFreight}}</th>
                    <th >{{item.totalAmountPrice_}}</th>
                    <th >{{item.para4IntDelivery_}}</th>  
                    <th >{{item.para4Weight_}}</th>                   
                  </tr>                               
                </tbody>               
          </table>
          <table class='box1' style="border:1px solid black;border-top:0;"> 
            <tbody>             
            <tr>
                <th style="width:67px;"  >备注:</th>
                <th style="width:943px;text-align:left;">
                  <span style="margin-left:5px;">
                    {{ pcbOrderStrDto.note }}
                  </span>
                </th>
              </tr>
              
            </tbody>              
          </table>
          <table class='box1' style="border:1px solid black;border-top:0;"> 
            <tbody>
              <tr>               
                <th style="text-align:left;margin-left:7px;display: inline-block;border-bottom: 0;border-right:0;width:993px" >Remarks:  
                <p style="margin-bottom:0" > 1. 交货币种: {{custModuleNoDto.currencyStr}} </p> 
                <p style="margin-bottom:0" >  2. 付款期限: {{custModuleNoDto.payDay}} 天</p>
                <p style="margin-bottom:0" > 3. 交货期是从EQ完成后开始计算。 </p>
                <p style="margin-bottom:0" > 4.报价有效:自报价之日起30天。    </p>
                <p style="margin-bottom:0" > 5. 包装方法：硅胶，泡沫塑料薄膜，普通真空包装。</p>
                <p style="margin-bottom:0" >6. 如果需要特殊工艺,质量验收是基于IPC类2的。</p>
                <p style="margin-bottom:0" >7. 价格基于上层信息，如果有任何特殊的过程需要额外的计算。</p> 
              </th>          
              </tr>
            </tbody>
          </table>
          
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  const columns = [
    // {
    //   title: '序号',
    //   dataIndex: 'index',
    //   key: 'index',
    //   align: "center",
    //   customRender: (text,record,index) => `${index+1}`,
    //   width: "10%",    
    // },    
    {
      title: 'QNo',
      dataIndex: '',
      align: "left",
      width:40,    
    },
    {
      title: 'P/N',
      dataIndex: '',
      align: "left",
      width:40,    
    },
    {
    title: "PCB Spec",
    width:200,
    align: "left",
    scopedSlots:{customRender:'craft'}
    },
    
    
  ]
  const columns1 = [
    // {
    //   title: '序号',
    //   dataIndex: 'index',
    //   key: 'index',
    //   align: "center",
    //   customRender: (text,record,index) => `${index+1}`,
    //   width: "2%",    
    // },
    // {
    //   title: 'QNo',
    //   dataIndex: '',
    //   align: "left",
    //   width:50, 
    //   scopedSlots:{customRender:'QNo'} ,
    //   customCell: (_, index,row) => {       
    //         return { rowSpan: 2 };         
    //     }, 
    // },
    // {
    //   title: 'P/N',
    //   dataIndex: '',
    //   align: "left",
    //   width:50, 
    //   scopedSlots:{customRender:'P/N'}   
    // },
    // {
    // title: "PCB Spec",
    // width:100,
    // align: "left",
    // scopedSlots:{customRender:'PCBSpec'}
    // },
    {
      title: 'Quantity (Pcs)',
      dataIndex: 'para4DelQty_',
      align: "center",
      width: "7%",    
    },
    {
      title: 'X(mm)',
      dataIndex: '',
      align: "center",
      width:"7%",    
    },
    {
    title: "y(mm)",
    width: "6%",
    dataIndex:'',
    align: "center",    
    },
    {
      title: 'Array',
      dataIndex: '',
      align: "center",
      width: "6%",    
    },
    {
      title: 'Sq(㎡)',
      dataIndex: 'para4Area_',
      align: "center",
      width:"6%",    
    },
    {
      title: 'Unit Price  40px(Pcs)(UsD)',
      dataIndex: '',
      align: "center",
      width: "9%",   
      scopedSlots:{customRender:'craft'} 
    },
    {
      title: 'tooling    (UsD)',
      dataIndex: '',
      align: "center",
      width:"8%",    
    },
    {
      title: 'E-test   (UsD)',
      align: "center",
      width: "8%", 
      dataIndex: 'testPrice_',   
    },
    {
      title: 'Air shipping cost(UsD)',
      align: "center",
      width: "9%",
      dataIndex: '',   
    },
    {
      title: 'Total (included air shpping cost)  (UsD)',
      dataIndex: 'totalAmountPrice_',
      align: "center",
      width:"12%",    
    },
    {
      title: 'Lead Time  (days)',
      dataIndex: 'para4Delivery_',
      align: "center",
      width: "8%",    
    },
    {
      title: 'Estimate weight  (KG)',
      dataIndex: 'para4Weight_',
      align: "center",
      width: "8%",    
    },
    
    
    
  ]
import htmlToPdf from '@/utils/htmlToPdfa3';
import $ from 'jquery';
export default {
  name: "ReportInfo",
  props:{
    reportData: {
      type: Object,
      required: true,
    }
  },
  
  computed:{  },
  data() {
    return {
      data: [],
      that: this,
      height: window.document.documentElement.clientHeight - 158,
      style_:0,
      dataSource1:[],
      dataSource2:[],
      columns,
      columns1,
      pcbOrderStrDto:{},
      custModuleNoDto:{}, 
      supperDto:{},
      dataTime:'',  
      pcbSpecEnglish:[],   
    }
  },
  methods: { 
    getReportPdf(){
      htmlToPdf('pdfDom',this.pcbOrderStrDto.orderNo)
    },
   
    
   },
  mounted() {    
    this.dataSource2 = this.reportData.priceDtos   
    this.pcbOrderStrDto =  this.reportData.pcbOrderStrDto
    this.custModuleNoDto = this.reportData.custModuleNoDto
    this.supperDto = this.reportData.supperDto
    this.pcbSpecEnglish = this.reportData.pcbSpec != '' ? this.reportData.pcbSpec.split('\n\r'):''
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth()+ 1;
    let dd = new Date().getDate();
    if (mm < 10) {
      mm = '0' + mm;
    }
    if (dd < 10) {
      dd = '0' + dd;
    }
    this.dataTime =  yy+'-'+mm+'-'+dd
    console.log('报表数据',this.dataTime,this.reportData.custModuleNoDto)
  }
}
</script>

<style lang="less" scoped>

.pdfDom {
  overflow: auto;
  margin: -25px;
}
#pdfDom {
  padding: 20px;
}
.headerTitle {
  border: 1px solid #000000;  
  border-bottom:0;
  margin-bottom: 0px !important;
  .divclass{  
    width: 100%;
    text-align: center;
    margin-right: 145px;
  }
  
  // display: flex;
  margin-bottom: 20px;
  img {
    margin-left: 50px;
    height: 70px;
  }
  h2 {
    font-size: 24px;
    color: #FFFFFF;
    background: #000000;
    font-weight: 800;
    margin-bottom:0;
    height:44px;
    line-height: 44px;
    border-left: 1px solid #000000;
    border-right: 1px solid #000000;
  }
  p{
    font-size: 14px;
    color: #000000;
    font-weight: 700;
    border-bottom: 1px solid #000000;
    margin-bottom:0;
    line-height: 22px;

  }
  span {
    font-size: 16px;
    color: #990099;
  }
}
.box {
    width:100%;
    color:#000000;
    font-size: 10px;
    font-size: 700;
  }
  .box1 {
    width:100%;
    color:#000000;
    font-size: 10px;
    font-size: 700;
  }
  .box tbody> tr > th{
    border-right:1px solid #000000;
    border-bottom:1px solid #000000;    
    padding:0 5px; 
    font-size: 400!important;   
  }
  .box1 tbody> tr > th{
    border-right:1px solid #000000;
    border-bottom:1px solid #000000;    
    padding:0 ;
    font-size: 400!important;
    overflow-wrap: anywhere;
    text-align: center;
  }
 /deep/ .ant-table-thead > tr:first-child > th:last-child {
    border-top-right-radius: 0!important; 
}
/deep/ .ant-table-bordered .ant-table-body > table{
  border:0!important;
}
/deep/ .ant-table-body {
  .ant-table-thead {
    tr>th {
      padding: 5px 0;
      border-bottom:1px solid #000000; 
      border-color: #000000;
      &:first-child {
        // border-left: 1px solid #000000 ;
        border-top-left-radius: 0;
      }
    }
  }
  .ant-table-tbody {
    tr>td {
      padding: 7px 0;
      border-color: #000000;
      &:first-child {
        // border-left: 1px solid #000000;
        border-top-left-radius: 0;
      }
    }

  }
}
</style>
