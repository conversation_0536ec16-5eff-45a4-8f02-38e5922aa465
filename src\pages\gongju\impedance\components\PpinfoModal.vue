<!-- 工具管理- 叠层阻抗-pp选择弹窗-->
<template>
  <div class="ppmodal">
    <div style="align-items: center; display: flex; justify-content: space-between; margin: 10px 0">
      <a-input style="width: 70%; margin-left: 7px" placeholder="请输入查询" v-model="textInput" @keydown.native.enter.prevent="search"></a-input>
      <a-button type="primary" style="float: right; margin-right: 7px; margin-left: 7px" @click="search">查询</a-button>
      <a-button type="primary" style="float: right; margin-right: 7px" :disabled="this.dataSourcePP.length == 0" @click="submitPP">提交</a-button>
      <a-button style="float: right; margin-right: 7px" @click="handleCancel">取消</a-button>
    </div>
    <a-table
      :columns="columnsPP"
      :data-source="dataSourcePP"
      :pagination="false"
      :loading="loading"
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      :scroll="{ y: 145 }"
      :class="dataSourcePP.length ? 'mintable1' : ''"
      bordered
    >
      <span slot="pP_" slot-scope="record, text, index">
        <a-select v-model="record.pP_" @change="handleChangePP($event, record, index)" :showArrow="false" style="width: 100%; height: 100%">
          <a-select-option v-for="item in ppChange" :value="item" :key="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </span>
      <span slot="rC_" slot-scope="record, text, index">
        <a-select v-model="record.rC_" @change="handleChangeRc($event, record, index)" :showArrow="false" style="width: 100%; height: 100%">
          <a-select-option v-for="item in record.changes" :value="item" :key="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </span>
    </a-table>
    <a-list bordered :data-source="modalPPlist" :loading="loading" class="modalList" :style="{ height: ppheighty + 'px' }">
      <a-list-item
        slot="renderItem"
        slot-scope="item, index"
        :class="index % 2 == 0 ? '' : 'bgactive'"
        @click="ppTableBtn(item, index)"
        style="display: flex"
      >
        {{ item.ppComb_ }}
        <a-icon type="check" v-if="flag == index" style="color: green" />
      </a-list-item>
      <div slot="header">PPComb_</div>
    </a-list>
  </div>
</template>

<script>
const columnsPP = [
  {
    title: "PP",
    key: "pP_",
    width: "27%",
    // dataIndex: "pP_",
    scopedSlots: { customRender: "pP_" },
    align: "center",
  },
  {
    title: "RC",
    width: "18%",
    key: "rC_",
    scopedSlots: { customRender: "rC_" },
    align: "center",
  },
  // {
  //     title: "Mil",
  //     dataIndex: "Mil",
  // },
  {
    title: "THK",
    width: "17%",
    dataIndex: "thicknesS_",
    align: "center",
  },
  {
    title: "MM",
    width: "19%",
    dataIndex: "thicknessmM_",
    align: "center",
  },
  {
    title: "DK",
    dataIndex: "ppdK_",
    align: "center",
    width: "14%",
  },
  {
    title: "玻纤布",
    dataIndex: "ppThickness4NY_",
    width: "16%",
    align: "center",
  },
];
export default {
  name: "PpInfoModal",
  props: {
    ppList: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      required: true,
    },
    dataSourcePP: {
      type: Array,
      required: true,
    },
    ppId: {
      type: String,
      required: true,
    },
    ppChange: {
      type: Array,
    },
    ppheighty: {
      type: Number,
    },
  },
  computed: {
    filterData: () => {
      const newArray = [];
      var setArray = arr.filter(a => !res.has(a[attrName]) && res.set(a[attrName], 1));
    },
  },
  data() {
    return {
      columnsPP,
      textInput: "",
      modalPPlist: [],
      flag: Number,
      selectedRowKeysArray: [],
    };
  },
  watch: {
    ppList() {
      this.modalPPlist = this.ppList;
    },
  },
  methods: {
    ppTableBtn(val, index) {
      this.flag = index;
      this.$emit("ppTableFiltet", val);
    },
    submitPP() {
      this.$emit("submitPP");
    },
    handleCancel() {
      this.$emit("handleCancel");
    },
    search() {
      if (this.textInput != "") {
        var reg = new RegExp(this.textInput);
        var arr = [];
        for (var i = 0; i < this.ppList.length; i++) {
          if (reg.test(this.ppList[i].ppComb_)) {
            arr.push(this.ppList[i]);
          }
        }
        this.modalPPlist = arr;
      } else {
        this.modalPPlist = this.ppList;
      }
    },
    handleChangeRc(val, recoed, index) {
      this.$emit("rcChange", { val: val, index: index, pP_: recoed.pP_ });
    },
    handleChangePP(val, recoed, index) {
      this.$emit("PPChange", { val: val, index: index });
    },
  },
  mounted() {
    // console.log('---------------------------------',this.dataSourcePP)
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-table-placeholder {
  padding: 0;
}
/deep/.ant-select-selection--single {
  position: relative;
  height: 27px;
  cursor: pointer;
  margin: 4px;
}
/deep/.ant-select-no-arrow .ant-select-selection__rendered {
  margin-right: 8px;
}
/deep/.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 7px 2px;
  overflow-wrap: break-word;
}
.mintable1 {
  /deep/.ant-table-body {
    min-height: 145px;
    border-bottom: 1px solid #e8e8e8;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
.ppmodal {
  /deep/ .ant-select-dropdown-menu-item {
    text-align: center !important;
  }
  /deep/ .ant-table-body {
    background: #ffffff !important;
    tr td:nth-child(1) {
      padding: 0 !important;
      .ant-select-selection {
        border: 0;
        .ant-select-selection-selected-value {
          width: 100%;
          text-align: center;
        }
      }
    }
    tr td:nth-child(2) {
      padding: 0 !important;
      .ant-select-selection {
        border: 0;
        .ant-select-selection-selected-value {
          width: 100%;
          text-align: center;
        }
      }
    }
    .ant-table-thead {
      th {
        padding: 8px 8px;
      }
    }
  }
}
.modalList {
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #ff9900;
    // #fff9e6
  }
  overflow-y: scroll;
  /deep/ .ant-list-header {
    padding: 5px 24px;
    text-align: center;
    background-color: #f0f0f0;
  }
  .ant-list-item {
    padding: 5px 12px;
  }
  .bgactive {
    background: #f8f8f8;
  }
}
/deep/ .ant-table-tbody tr td {
  padding: 5px 5px !important;
}
</style>
