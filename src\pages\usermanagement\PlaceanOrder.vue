<!--用户管理 立即下单-->
<template>
  <a-spin :spinning="spinning">
    <div class="PlaceanOrder">
      <img src="@/assets/img/liucheng1.png" alt="" style="padding: 20px 0 10px 30px" />
      <div style="padding-left: 30px">
        <a-form-model ref="ruleForm" :model="formData" :rules="rules">
          <a-form-model-item label="上传文件" :label-col="{ span: 1 }" :wrapper-col="{ span: 12 }">
            <a-input style="width: 350px; margin-right: 10px" @click="uploadfile" v-model="formData.PcbFileName"></a-input>
            <a-button type="primary" @click="uploadfile">浏览文件</a-button>
            <a-upload
              accept=".zip,.rar,.7z,"
              name="file"
              ref="fileRef"
              :showUploadList="false"
              :before-upload="beforeUpload1"
              :customRequest="downloadFilesCustomRequest"
              :file-list="fileListData"
              @change="handleChange1"
            >
            </a-upload>
            <a-button style="display: none"><a-icon type="upload" /> </a-button>
          </a-form-model-item>
          <a-form-model-item label="订单数量" :label-col="{ span: 1 }" :wrapper-col="{ span: 12 }" prop="Num">
            <a-input
              style="width: 447px; margin-right: 10px"
              addon-after="PCS"
              v-model="formData.Num"
              oninput="value=value.replace(/[^0-9]/g,'')"
            ></a-input>
            <a-tooltip title="PCS数量为交货的最小单元数，如用户需要拼板出货，此处也应填写小板的数量">
              <a-icon type="question-circle" style="font-size: 20px"></a-icon>
            </a-tooltip>
          </a-form-model-item>
          <a-form-model-item label="订单备注" :label-col="{ span: 1 }" :wrapper-col="{ span: 12 }">
            <a-textarea style="width: 447px; margin-right: 10px" :auto-size="{ minRows: 3, maxRows: 6 }" v-model="formData.MktNote" />
          </a-form-model-item>
        </a-form-model>
      </div>
      <div class="tishisize" style="padding-left: 30px">
        <p style="margin-top: 13px">温馨提示：</p>
        <p>系统审核文件的目标如下：</p>
        <p>1、PCB板的层数，是否存在假层；</p>
        <p>2、最小宽/间距；</p>
        <p>3、最小孔到线，最小孔到边；</p>
        <p>4、最小金属孔。</p>
        <p>5、文件只支持 rar、zip、7z格式并且压缩包大小不能超过 50MB!</p>
        <p style="color: #ff9900">6、(需要上传PCB文件或者Gerber文件才能完成下单)</p>
      </div>
      <div style="padding-left: 30px; padding-top: 20px">
        <a-button type="primary" @click="handleOK">确定</a-button>
        <a-button style="margin-left: 20px" @click="formData = {}">取消</a-button>
      </div>
    </div>
  </a-spin>
</template>
<script>
import { platformcreateorder } from "@/services/usermanagement/index.js";
import axios from "axios";
export default {
  name: "PlaceanOrder",
  props: ["user"],
  data() {
    return {
      spinning: false,
      fileListData: [],
      formData: {},
      rules: {},
    };
  },
  methods: {
    handleResize() {
      let mainContent = document.getElementsByClassName("PlaceanOrder")[0];
      mainContent.style.height = window.innerHeight - 90 + "px";
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const filesize = Number(file.size / 1024 / 1024) < 500;
        const isJpgOrPng =
          file.name.toLowerCase().indexOf(".rar") != -1 ||
          file.name.toLowerCase().indexOf(".zip") != -1 ||
          file.name.toLowerCase().indexOf(".7z") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("上传文件只支持.zip/.rar/.7z格式文件");
          reject();
        } else if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = [];
      }
      if (fileList.length > 1) {
        fileList = fileList.slice(-1);
      }
      this.fileListData = fileList;
      this.formData.PcbFilePath = fileList[0]?.response?.split(",")[0];
      this.formData.PcbFileName = fileList[0]?.name.slice(0, fileList[0]?.name.lastIndexOf("."));
    },
    uploadfile() {
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    //分段上传MD5
    async downloadFilesCustomRequest(data) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("CustNo", this.user.custNo);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      formData.append("JoinFactoryId", this.user.tradeType); //工厂ID
      this.spinning = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.data);
              this.spinning = false;
            }
          } else {
            this.$message.error(res.message);
            data.onError(res.message);
            this.spinning = false;
            i = shardCount;
          }
        });
      }
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    handleOK() {
      if (!this.formData.PcbFilePath) {
        this.$message.error("请上传文件");
        return;
      }
      let params = this.formData;
      params.TradeId = this.user.tradeType;
      params.CustNo = this.user.custNo;
      params.Num = Number(this.formData.Num);
      params.orderType = "0";
      params.IsPcba = false;
      params.uid = "";
      this.spinning = true;
      platformcreateorder(params)
        .then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.formData = {};
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  created() {
    this.$nextTick(() => {
      this.handleResize();
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
};
</script>
<style lang="less" scoped>
.PlaceanOrder {
  background-color: white;
}
.tishisize p {
  font-size: 12px;
  letter-spacing: 2px;
  margin-bottom: 0.5em;
}
/deep/.ant-col-1 {
  width: 72px;
}
</style>
