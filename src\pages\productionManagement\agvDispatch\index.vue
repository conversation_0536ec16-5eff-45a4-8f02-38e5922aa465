<!-- 车间管理-agv管理 -->
<template style="height: 100%">
<div class="over-order-wrap">
  <div class="code-search">
    <div class="code-search-box scan">
      <a-input ref="userNameInput" v-model="orderNum" placeholder="请扫制造流程卡条码" @pressEnter="addOrderList" autofocus="autofocus">
        <a-icon type="scan"  slot="prefix" style="font-size: 24px; color: #ff9900"/>
      </a-input>
    </div>
  </div>
  <div class="order-list">
    <ul class="order-list-box">
      <li class="order-list-li" v-for="(item, index) in orderList" :key="index">
        <p>{{item}}</p>
        <div class="moveOrderList" @click="moveItem(index)">移除</div>
      </li>
    </ul>
  </div>
  <div class="operator-form">
    <div class="operator-btn-wrap">
      <a-button type="primary" @click="callAgv" :loading="loading">
        呼叫
      </a-button>
      <a-button type="primary" @click="okAgv" :loading="loading">
        确认
      </a-button>
      <a-button type="primary" @click="cancelAgv" :loading="loading">
        取消
      </a-button>
    </div>
  </div>
</div>
</template>

<script>
import 'whatwg-fetch'
import {agvAction} from "@/services/gongju/agvDispatch";
export default {
  name: "index",
  data(){
    return {
      orderNum:"",
      loading:false,
      orderList:[ ]
    }
  },
  methods:{
    moveItem(index){
      this.orderList.splice(index,1)
    },
    addOrderList(){
      this.orderList.push(this.orderNum)
    },
    async getApi(operation) {
      this.loading = true;
      let data ={
        "Operation": operation,  //1 呼叫 // 2 确认  // 3 取消
        "OrderNo": this.orderList,
        "IsConfirm": false
      }
      agvAction(data).then(res => {
        if (res.code == 1) {
          this.$message.success(`呼叫`+res.message)
          if (operation != 1) {
            this.orderList = []
          }
        }
        this.loading = false;
      })
    },
    callAgv(){
      this.getApi(1)
      // if (this.orderList.length) {
      //
      // } else {
      //   this.$message.error('暂无可呼叫订单');
      // }
    },
    okAgv(){
      this.getApi(2)
      // if (this.orderList.length) {
      //
      // } else {
      //   this.$message.error('暂无可确认订单');
      // }
    },
    cancelAgv(){
      this.getApi(3)
      // if (this.orderList.length) {
      //
      // } else {
      //   this.$message.error('暂无可取消订单');
      // }
    }
  }
}
</script>

<style scoped lang="less">
.over-order-wrap {
  width: 100%;
  height: 100%;
  position: relative;
  .code-search {
    padding: 0.19rem 0.15rem 0.17rem;
    font-size: 0.12rem;
    line-height: 0.35rem;
    display: flex;
    background: url('../../assets/img/mobelBg.jpg') no-repeat center center;
    background-size: cover;
    .code-search-box {
      border-radius: 0.03rem;
      background-color: #fff;
      width: 100%;
      height: 0.45rem;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      /deep/ .ant-input-affix-wrapper {
        height: 100%;
        .ant-input-prefix {}
        .ant-input {
          height: 100%;
          padding-left: 40px;
        }
      }
    }
  }
  .operator-form {
    /*height: calc(100% - 1.31rem);*/
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 0.1rem 0.15rem;
    box-sizing: border-box;
    overflow-y: auto;
    background-color: #fff;
    .operator-btn-wrap {
      display: flex;
      /deep/  .ant-btn-primary {
        flex: 1;
        height: 0.35rem;
        font-size: 0.18rem;
        line-height: 0.35rem;
        text-align: center;
        border-radius: 0.05rem;
        box-sizing: border-box;
        margin-right: 0.11rem;
        color: #fff;
      }
    }
  }
  .order-list-box {
    padding: 0;
    margin: 0;
    p {
      margin: 0;
      padding: 0;
    }
  }
  .order-list .order-list-box li{
    height: 0.4rem;
    padding: 0 .2rem;
    line-height: 0.4rem;
    background-color: #fff;
    margin-bottom: 0.05rem;
    color: #ff9900;
    font-size:.16rem ;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .order-list .order-list-box .moveOrderList {
    width: 0.5rem;
    height: 0.28rem;
    font-size: .14rem;
    line-height: 0.28rem;
    background: #ff9900;
    text-align: center;
    color: #fff;
    border-radius: 4px;
  }
  .order-list .order-list-box .moveOrderList:hover {
    background-color: #ffb029;
  }
}
</style>