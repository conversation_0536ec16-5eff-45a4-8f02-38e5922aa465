/*
 * @Author: CJP
 * @Date: 2022-06-16 15:08:05
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-27 10:00:31
 * @FilePath: \vue-antd-admin\src\services\projectDisptch\index.js
 * @Description:
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved.
 */
import { request, METHOD } from '@/utils/request';
// 获取订单列表
export async function projectDispathgetOrderList(params) {
    return request("/api/app/pro-order-list/m-iDispatch-detail", METHOD.GET,params)
}
//获取日志信息
export async function proorderlog(params) {
    return request(`/api/app/pro-order-log?OrderId=${params}`, METHOD.GET)
}
//系数明细展示接口
export async function proordercoefficient(id,type) {
    return request(`api/app/pro-order/coefficient/${id}?type=${type}`, METHOD.GET)
}
// 获取订单列表 带分页
export async function projectDispathgetOrderList1(params) {
    return request("/api/app/pro-order-list/m-iDispatch-detail-page-list" , METHOD.GET,params)
}
// 制作人员详情 派单新版
export async function projectPeopleInfo1(id,type) {
    return request("/api/app/e-mSMake-module-no/m-iUser-order?userid="+id + "&type=" +type, METHOD.GET)
}
// 获取制作人员列表
export async function projectPeopleList() {
    return request("/api/app/pro-order-list/m-iUser-info", METHOD.GET)
}
// 获取统计列表
export async function projectTotalList() {
    return request("/api/app/pro-order-list/m-iTotal-orders", METHOD.GET)
}
// 制作人员详情
export async function projectPeopleInfo(id) {
    return request("/api/app/e-mSMake-module-no/m-iUser-order?userid="+id, METHOD.GET)
}
// 订单分配人员
export async function projectOrderAssign(params) {
    return request("/api/app/engineering-dispatch/m-iSend-order", METHOD.POST, params)
}
// // 人员是否请假
// export async function projectOrderInfo(id) {
//     return request("/api/app/e-mSMake-module-no/m-iIs-leave1?userid=" + id, METHOD.POST)
// }
// // 请假确认
// export async function projectOrderLeave(params) {
//     return request("/api/app/e-mSMake-module-no/m-iIs-leave", METHOD.POST, params)
// }
// 获取取单设置
export async function projectOrderInfo(id) {
    return request("/api/app/e-mSTSys-user-assignment/order-retrieval-settings?userid=" + id, METHOD.GET)
}
// 取单设置
export async function projectOrderLeave(params) {
    return request("/api/app/e-mSTSys-user-assignment/order-retrieval-settings", METHOD.POST, params)
}

// 获取搜索列表
export async function projectBoardInfoList(params) {
    return request('/api/app/e-mSMake-module-no/m-iBoard',METHOD.GET,params)
}
// 工程派单管理汇总
export async function dispatchTotal(params) {
    return request('/api/app/e-mSMake-module-no/m-iTotal',METHOD.GET,params)
}
// 分派回退
export async function BackOrder(Id) {
    return request(`/api/app/engineering-dispatch/m-iBack-send-order/${Id}`,METHOD.POST,)
}
export async function getTradeTypeSrcList() {
    return request("/api/app/e-mSUser-data-info/trade-type-src-list", METHOD.GET)
}

// 账号修改密码接口
 // 获取旧密码
export async function oldPassword(params) {
    return request(`/api/app/synchronize-user/check-password?oldPassword=${params}`, METHOD.POST)
}
// export async function newPassword(params) {
//     return request(`/api/app/synchronize-user/password?newPassword=${params}`, METHOD.PUT)
// }
export async function newPassword(params) {
    return request(`/api/app/synchronize-user/update-password?newPassword=${params}`, METHOD.POST)
}
// 上传原稿
export async function upLoadPcbFile(Id,params) {
    return request(`/api/app/e-mSMake-module-no/up-load-pcb-file?OrderNo=${Id}`, METHOD.POST,params)
}
// 暂停暂停
export async function setPause(params) {
    return request(`/api/app/engineering-dispatch/set-pause`, METHOD.POST,params)
}
// 取消暂停
export async function setCancel(params) {
    return request(`/api/app/engineering-dispatch/set-cancel`, METHOD.POST,params)
}
// 设置加急
export async function SetUrgent(Id) {
    return request(`/api/app/engineering-dispatch/set-is-jia-ji/${Id}`, METHOD.POST)
}
// 修改工厂
export async function joinFactoryId() {
    return request(`/api/app/e-mSMake-module-no/join-factory-id`, METHOD.GET)
}
export async function setUpJoinFactoryId(params) {
    return request(`/api/app/e-mSMake-module-no/set-up-join-factory-id`, METHOD.POST,params)
}
export async function miTotalordersdetail(type) {
    return request(`/api/app/pro-order-list/m-iTotal-orders-detail?type=${type}`, METHOD.GET)
}
export default {
    projectDispathgetOrderList1,
    projectTotalList
}