<!-- 市场管理 - 价格体系 -  新增-->
<template>
  <div ref="SelectBox">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="ruleForm" :model="form" :rules="rules" layout="inline">
        <template v-if="activeKey == '1'">
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="层数"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.layer_"
              >
                <a-input :title="form.calcBasicPriceList.layer_" v-model="form.calcBasicPriceList.layer_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="1㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceA_"
              >
                <a-input :title="form.calcBasicPriceList.priceA_" v-model="form.calcBasicPriceList.priceA_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="2㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceB_"
              >
                <a-input :title="form.calcBasicPriceList.priceB_" v-model="form.calcBasicPriceList.priceB_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="3㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceC_"
              >
                <a-input :title="form.calcBasicPriceList.priceC_" v-model="form.calcBasicPriceList.priceC_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="4㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceD_"
              >
                <a-input :title="form.calcBasicPriceList.priceD_" v-model="form.calcBasicPriceList.priceD_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="5㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceE_"
              >
                <a-input :title="form.calcBasicPriceList.priceE_" v-model="form.calcBasicPriceList.priceE_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="6㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceF_"
              >
                <a-input :title="form.calcBasicPriceList.priceF_" v-model="form.calcBasicPriceList.priceF_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="7㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceG_"
              >
                <a-input :title="form.calcBasicPriceList.priceG_" v-model="form.calcBasicPriceList.priceG_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="8㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceH_"
              >
                <a-input :title="form.calcBasicPriceList.priceH_" v-model="form.calcBasicPriceList.priceH_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="9㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.rX_PriceA_"
              >
                <a-input :title="form.calcBasicPriceList.rX_PriceA_" v-model="form.calcBasicPriceList.rX_PriceA_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="10㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.rX_PriceB_"
              >
                <a-input :title="form.calcBasicPriceList.rX_PriceB_" v-model="form.calcBasicPriceList.rX_PriceB_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="10-20㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.sB_PriceA_"
              >
                <a-input :title="form.calcBasicPriceList.sB_PriceA_" v-model="form.calcBasicPriceList.sB_PriceA_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="20-30㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceL_"
              >
                <a-input :title="form.calcBasicPriceList.priceL_" v-model="form.calcBasicPriceList.priceL_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="30-40㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceZ_"
              >
                <a-input :title="form.calcBasicPriceList.priceZ_" v-model="form.calcBasicPriceList.priceZ_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="40-50"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceX_"
              >
                <a-input :title="form.calcBasicPriceList.priceX_" v-model="form.calcBasicPriceList.priceX_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="50-100㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceV_"
              >
                <a-input :title="form.calcBasicPriceList.priceV_" v-model="form.calcBasicPriceList.priceV_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label=">100㎡"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcBasicPriceList.priceN_"
              >
                <a-input :title="form.calcBasicPriceList.priceN_" v-model="form.calcBasicPriceList.priceN_" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <template v-if="activeKey == '2'">
          <a-row>
            <a-col :span="10">
              <a-form-model-item
                label="层数"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                prop="calcENGPriceList.layer_"
              >
                <a-input :title="form.calcENGPriceList.layer_" v-model="form.calcENGPriceList.layer_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="工程费(元/款)"
                :label-col="{ span: 12 }"
                :wrapper-col="{ span: 12 }"
                style="width: 100%; margin: 0"
                prop="calcENGPriceList.priceA_"
              >
                <a-input :title="form.calcENGPriceList.priceA_" v-model="form.calcENGPriceList.priceA_" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <template v-if="activeKey == '4'">
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="判断类别"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-select
                  v-model="form.calcPriceFormulaList.calcNameID_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.CalcNameID_)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="判断类型"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-select
                  v-model="form.calcPriceFormulaList.conditionType_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.ConditionType_)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="判断区域"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-select
                  v-model="form.calcPriceFormulaList.conditionAreaID_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.ConditionAreaID_)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="判断公式"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-input v-model="form.calcPriceFormulaList.condition_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="结果类型"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-select
                  v-model="form.calcPriceFormulaList.expressionType_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.ConditionType_)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="结果区域"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-select
                  v-model="form.calcPriceFormulaList.expressionAreaID_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.ConditionAreaID_)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="结果公式"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-input v-model="form.calcPriceFormulaList.expression_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="说明1" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0" class="required">
                <a-input v-model="form.calcPriceFormulaList.captions_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="说明2" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaList.captions2_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="基价类型" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-select
                  v-model="form.calcPriceFormulaList.basicPriceType_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.ConditionType_)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="基价类型区域"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-select
                  v-model="form.calcPriceFormulaList.basicPriceAreaID_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.ConditionAreaID_)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="基价计算条件" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaList.basicPrice_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="基价因子" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaList.factor_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="基价min类型" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-select
                  v-model="form.calcPriceFormulaList.basicPrice4MinType_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.ConditionType_)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="基价min区域" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-select
                  v-model="form.calcPriceFormulaList.basicPrice4MinAreaID_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.ConditionAreaID_)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="基价min计算条件" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaList.basicPrice4Min_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="基价max类型" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-select
                  v-model="form.calcPriceFormulaList.basicPrice4MaxType_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.ConditionType_)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="基价max区域" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-select
                  v-model="form.calcPriceFormulaList.basicPrice4MaxAreaID_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.ConditionAreaID_)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="基价max计算条件" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaList.basicPrice4Max_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="条件有效"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-select
                  v-model="form.calcPriceFormulaList.isChk_"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectOption.IsChk_)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="系统计算模式" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaList.calcFormulaType_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="是否评审" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isReview_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="显示排序"
                :label-col="{ span: 10 }"
                :wrapper-col="{ span: 14 }"
                style="width: 100%; margin: 0"
                class="required"
              >
                <a-input v-model="form.calcPriceFormulaList.calcDisplays_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="汇总显示" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isShow_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="样板费" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isSamplePrice_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="平米价" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isSquareMeterPrice_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="其他费" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isOtherPrice_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="编辑计算" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isDetailEdit_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="编辑加减" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isCollectEdit_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="编辑实际报价" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isActualEdit_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="选择" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isChooseEdit_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="客规条件" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-checkbox v-model="form.calcPriceFormulaList.isCustNo_" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <template v-if="activeKey == '5'">
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="计算条件" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaAreaList.calcCondition_" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="计算条件说明" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }" style="width: 100%; margin: 0">
                <a-input v-model="form.calcPriceFormulaAreaList.captions_" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <template v-if="activeKey == '3'">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="分类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="分类序号" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="条件1" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="条件2" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="条件3" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="条件4" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="样板费" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="批量费" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="工程费" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
                <a-input />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <template v-if="activeKey == '6'">
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="客户代码"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="calcCustAreaList.custNameStr"
              >
                <a-select
                  placeholder="请选择客户代码"
                  v-model="form.calcCustAreaList.custNameStr"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  :dropdownMatchSelectWidth="false"
                  showSearch
                  :autoFocus="autoFocus"
                  optionFilterProp="children"
                  @popupScroll="handlePopupScroll"
                  allowClear
                  @search="supValue"
                >
                  <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item.id">
                    {{ item.value }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <template v-if="activeKey == '7'">
          <a-row>
            <!-- <a-col :span='12'>
            <a-form-model-item label="条数始"  :label-col="{ span: 5}" :wrapper-col="{ span: 19}" style="width:100%; margin:0">
              <a-input/>
            </a-form-model-item>
          </a-col>        -->
            <a-col :span="12">
              <a-form-model-item
                label="条数末"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="goldfinger.count4To_"
              >
                <a-input v-model="form.goldfinger.count4To_" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="<=10u"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="goldfinger.price_A"
              >
                <a-input v-model="form.goldfinger.price_A" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="15u"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="goldfinger.price_B"
              >
                <a-input v-model="form.goldfinger.price_B" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="20u"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="goldfinger.price_C"
              >
                <a-input v-model="form.goldfinger.price_C" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="30u"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="goldfinger.price_D"
              >
                <a-input v-model="form.goldfinger.price_D" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                label="40u"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="goldfinger.price_E"
              >
                <a-input v-model="form.goldfinger.price_E" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                label="50u"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 19 }"
                style="width: 100%; margin: 0"
                prop="goldfinger.price_F"
              >
                <a-input v-model="form.goldfinger.price_F" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
      </a-form-model>
    </a-spin>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  props: {
    activeKey: {
      type: String,
    },
    selectRowData: {
      type: Object,
    },
    editFlg: {
      type: Boolean,
    },
    selectOption: {
      type: Object,
    },
    dropdownselection: {
      type: Array,
    },
  },
  name: "costInfo",
  inject: ["reload"],
  data() {
    var checknum = (rule, value, callback) => {
      var myreg = /^(\d|[1-9]\d+)(\.\d+)?$/;
      if (!myreg.test(value) && value) {
        callback(new Error("请输入正确的数据"));
      } else {
        callback();
      }
    };
    return {
      rules: {
        calcBasicPriceList: {
          layer_: [
            { required: true, message: "请输入", trigger: "blur" },
            { required: true, validator: checknum, trigger: "blur" },
          ],
          priceA_: [{ validator: checknum, trigger: "blur" }],
          priceB_: [{ validator: checknum, trigger: "blur" }],
          priceC_: [{ validator: checknum, trigger: "blur" }],
          priceD_: [{ validator: checknum, trigger: "blur" }],
          priceE_: [{ validator: checknum, trigger: "blur" }],
          priceF_: [{ validator: checknum, trigger: "blur" }],
          priceG_: [{ validator: checknum, trigger: "blur" }],
          priceH_: [{ validator: checknum, trigger: "blur" }],
          rX_PriceA_: [{ validator: checknum, trigger: "blur" }],
          rX_PriceB_: [{ validator: checknum, trigger: "blur" }],
          priceL_: [{ validator: checknum, trigger: "blur" }],
          priceZ_: [{ validator: checknum, trigger: "blur" }],
          priceX_: [{ validator: checknum, trigger: "blur" }],
          priceV_: [{ validator: checknum, trigger: "blur" }],
          priceN_: [{ validator: checknum, trigger: "blur" }],
          sB_PriceA_: [{ validator: checknum, trigger: "blur" }],
          sB_PriceB_: [{ validator: checknum, trigger: "blur" }],
        },
        calcENGPriceList: {
          layer_: [
            { required: true, message: "请输入", trigger: "blur" },
            { required: true, validator: checknum, trigger: "blur" },
          ],
          priceA_: [{ validator: checknum, trigger: "blur" }],
        },
        calcCustAreaList: {
          custNameStr: [{ required: true, message: "请选择", trigger: "blur" }],
        },
        goldfinger: {
          count4To_: [
            { required: true, message: "请输入", trigger: "blur" },
            { required: true, validator: checknum, trigger: "blur" },
          ],
          price_A: [{ validator: checknum, trigger: "blur" }],
          price_B: [{ validator: checknum, trigger: "blur" }],
          price_C: [{ validator: checknum, trigger: "blur" }],
          price_D: [{ validator: checknum, trigger: "blur" }],
          price_E: [{ validator: checknum, trigger: "blur" }],
          price_F: [{ validator: checknum, trigger: "blur" }],
        },
      },
      frontDataZSupplier: [], // 供应商 100条数据的集合
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      autoFocus: false,
      showDom: false,
      visible: false,
      confirmLoading: false,
      form: {
        calcBasicPriceList: {
          layer_: "",
          priceA_: "",
          priceB_: "",
          priceC_: "",
          priceD_: "",
          priceE_: "",
          priceF_: "",
          priceG_: "",
          priceH_: "",
          rX_PriceA_: "",
          rX_PriceB_: "",
          priceL_: "",
          priceZ_: "",
          priceX_: "",
          priceV_: "",
          priceN_: "",
          sB_PriceA_: "",
          sB_PriceB_: "",
        },
        calcENGPriceList: {
          layer_: "",
          priceA_: "",
        },
        calcPriceFormulaList: {
          calcArea_: "",
          calcNameID_: "",
          conditionType_: "",
          conditionAreaID_: "",
          condition_: "",
          expressionType_: "",
          expressionAreaID_: "",
          expression_: "",
          captions_: "",
          captions2_: "",
          inDate_: "",
          basicPriceType_: "",
          basicPriceAreaID_: "",
          basicPrice_: "",
          factor_: "",
          basicPrice4MinType_: "",
          basicPrice4MinAreaID_: "",
          basicPrice4Min_: "",
          basicPrice4MaxType_: "",
          basicPrice4MaxAreaID_: "",
          basicPrice4Max_: "",
          isChk_: "",
          calcFormulaType_: "",
          isReview_: false,
          calcDisplays_: "",
          isShow_: false,
          isSamplePrice_: false,
          isSquareMeterPrice_: false,
          isOtherPrice_: false,
          isDetailEdit_: false,
          isCollectEdit_: false,
          isActualEdit_: false,
          isChooseEdit_: false,
          isCustNo_: false,
        },
        calcPriceFormulaAreaList: {
          calcCondition_: "",
          captions_: "",
        },
        calcCustAreaList: {
          custNameStr: "",
        },
        goldfinger: {
          count4To_: "",
          price_A: "",
          price_B: "",
          price_C: "",
          price_D: "",
          price_E: "",
          price_F: "",
        },
      },
    };
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  watch: {
    "form.calcCustAreaList.custNameStr": {
      handler(val) {},
    },
  },
  mounted() {
    this.frontDataZSupplier = this.dropdownselection.slice(0, 20);
    const keyToFieldMap = {
      1: "calcBasicPriceList",
      2: "calcENGPriceList",
      4: "calcPriceFormulaList",
      5: "calcPriceFormulaAreaList",
      6: "calcCustAreaList",
      7: "goldfinger",
    };
    if (this.editFlg) {
      const fieldName = keyToFieldMap[this.activeKey];
      if (fieldName) {
        this.Datasplitting(this.form[fieldName], fieldName);
      }
    }
  },
  methods: {
    Datasplitting(dataA, type) {
      let dataB = this.selectRowData;
      for (let a = 0; a < Object.keys(dataB).length; a++) {
        if (Object.keys(dataA).indexOf(Object.keys(dataB)[a]) >= 0) {
          dataA[Object.keys(dataB)[a]] = dataB[Object.keys(dataB)[a]];
        }
        this.form[type] = dataA;
      }
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.dropdownselection.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.dropdownselection.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.dropdownselection.forEach((item, index) => {
              if (item.value.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.dropdownselection.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }
          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value;
        that.scrollPage = 1;
        let arr = that.dropdownselection.filter(m => m.value.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.dropdownselection.slice(0, 20);
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select-dropdown {
  .ant-select-dropdown-content {
    .ant-select-dropdown-menu {
      .ant-select-dropdown-menu-item {
        font-size: 12px !important;
        font-weight: 500;
        color: #000000;
      }
    }
  }
}
// .ant-modal-content{
//   .ant-modal-body{
//     .ant-spin-nested-loading{
//       border-left: 1px solid #ddd;
//       border-top: 1px solid #ddd;
//     }
//   }
// }
.ant-row {
  margin-bottom: 0;
  .ant-col {
    .ant-form-item {
      /deep/.ant-form-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
        font-weight: 500;
        // color: #666;
        // background-color: #fafafa;
        // border-right: 1px solid #ddd;
        // border-bottom: 1px solid #ddd;
        height: 35px;
        label {
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          font-weight: 500;
        }
      }
      /deep/.ant-form-item-control-wrapper {
        font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
        font-weight: 500;
        .ant-form-item-control {
          line-height: inherit;
          padding: 2px 4px;
          // border-right: 1px solid #ddd;
          // border-bottom: 1px solid #ddd;
          // height: 35px;
          .ant-form-item-children {
            .ant-input {
              height: 28px;
              min-height: 20px;
              line-height: 16px;
              font-size: 12px;
              font-weight: 500;
            }
            .ant-checkbox-wrapper {
              .ant-checkbox {
                padding-top: 7px !important;
              }
            }
            .ant-select {
              .ant-select-selection {
                height: 28px;
                .ant-select-selection__rendered {
                  line-height: 28px;
                  .ant-select-selection-selected-value {
                    font-size: 12px;
                    font-weight: 500;
                  }
                }
              }
              .ant-input-affix-wrapper {
                .ant-input {
                  height: 28px;
                  padding: 0 5px;
                }
              }
              .ant-calendar-picker {
                .ant-calendar-picker-input {
                  height: 28px;
                  width: 272px;
                }
              }
            }
          }
        }
      }
    }
  }
}
/deep/.required {
  .ant-form-item-label label {
    color: red !important;
  }
}
.lastRow {
  /deep/.ant-form-item-control {
    line-height: 12px;
  }
}
.ant-form label {
  width: 20%;
  margin-left: 28px;
  margin-top: 10px;
}
/deep/.ant-checkbox-wrapper {
  margin: 0 !important;
}
</style>
