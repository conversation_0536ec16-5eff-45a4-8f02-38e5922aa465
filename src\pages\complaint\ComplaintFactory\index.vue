<!--投诉工厂-->
<template>
    <a-spin :spinning="spinning">  
        <div class="ComplaintFactory">
            <a-tabs  :activeKey="activeKey"  @tabClick="tabClick">
            <a-tab-pane key="30" tab="待回复"> 
            </a-tab-pane>
            <a-tab-pane key="50" tab="已回复">
            </a-tab-pane>
            <a-tab-pane key="60" tab="待回访">                
            </a-tab-pane>
            <a-tab-pane key="100" tab="已完结">                
            </a-tab-pane>
        </a-tabs>
        <div style="display: flex;padding-bottom: 7px;">
            <a-input placeholder="请输入订单编号/客户编号" style="width: 250px;margin-left: 15px;" 
            @keydown.enter.native="searchclick()" v-model="searchdata.CommonOrderNo"></a-input>
            <a-range-picker  :format="'YYYY-MM-DD'" style="margin-left: 15px;" @change="onChange" v-model="timeValue"/>
            <a-button type="primary" style="margin-left: 15px;" @click="searchclick"><a-icon type="search"></a-icon>查询</a-button>
        </div>
            <factory-table 
            :columns="columns" 
            :facdata="facdata" 
            :activeKey="activeKey" 
            :pagination="pagination" 
            @tableChange="handleTableChange"
            @getdata="getdata"/>
        </div>
        <div class="footerAction" style='user-select:none;margin-left: -2px;'>
          <div>
            <!-- <a-button type="primary" class="box">
                取单
            </a-button>  -->
          </div>             
        </div>
    </a-spin>
</template>
<script>
const columns = [
{
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 65,
    ellipsis:true,
    fixed: 'left',
},   
{
    title: "工厂编号",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "客诉编号",
    dataIndex: "orderNo",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "生产编号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,   
},
{
    title: "联系人",
    dataIndex: "factoryContactName",
    align: "left",
    ellipsis: true,
    width:80,       
},
{
    title: "联系电话",
    dataIndex: "factoryContactNumber",
    align: "left",
    ellipsis: true,
    width:150,       
},
{
    title: "投诉日期",
    dataIndex: "complaintDate",
    align: "left",
    ellipsis: true,
    width:180,       
},
{
    title: "当前状态",
    dataIndex: "statusStr",
    align: "left",
    ellipsis: true,
    width:200,       
},
{
    title: "操作",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:120,       
    scopedSlots:{customRender:'operation'}
},
];
import {factorycomplaintspagelist} from "@/services/complaint/QualitativeComplaint.js";
import FactoryTable from '@/pages/complaint/ComplaintFactory/module/FactoryTable'
export default {
    name:'ComplaintFactory',
    components:{FactoryTable},
    data(){
        return{
            timeValue:undefined,
            spinning:false,
            columns,
            activeKey:'30',
            searchdata:{},
            pagination: {
                pageSize: 20,
                current: 1,
                total:0,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: [ "20", "50", "100"],//每页中显示的数据
                showTotal: (total) => `总计 ${total} 条`,
            },
            facdata:[],
        }
    },
    created(){
    },
    mounted(){
        this.getdata({status:this.activeKey})
    },
    methods:{
        onChange(date, dateString) {
                this.searchdata.StartDate=dateString[0]
                this.searchdata.EndDate=dateString[1]
            },
        searchclick(){
            if(JSON.stringify(this.searchdata)=='{}'){
                this.$message.error('请输入查询条件')
                return
            }
            let params = {...this.searchdata,...{status:this.activeKey}}
            this.getdata(params)
        },
        addnewcomplaint(){
            this.$router.push({path:'Addcomplaint',query:{} })
        },
        tabClick(key){
            this.activeKey = key
            this.searchdata={}
            this.timeValue=undefined
            this.getdata({status:this.activeKey})
        },
        // 订单表变化change
        handleTableChange(pagination, filters, sorter) {
            this.pagination.current=pagination.current
            this.pagination.pageSize=pagination.pageSize
            if(JSON.stringify(this.searchdata)=='{}'){
                this.getdata({status:this.activeKey})
            }else{
                let params = {...this.searchdata,...{status:this.activeKey}}
                this.getdata(params)
            }
           
        },  
        getdata(val){
            this.spinning=true
            let tabkey = localStorage.getItem('tabkey')
            if(tabkey){
                this.activeKey = tabkey
                val.status = tabkey
            }
            let params = {
            'PageIndex': this.pagination.current,
            'PageSize' : this.pagination.pageSize,        
            }  
        params = {...params,...val}
        factorycomplaintspagelist(params).then(res=>{
            if(res.code){
                this.facdata = res.data.items
            }else{
                this.$message.error(res.message)
            }
        }).finally(() => {
            this.spinning = false;
            localStorage.removeItem('tabkey')
        });
        },
    }
}

</script>
<style lang="less" scoped>
.footerAction {
    width: 100%;
    height:46px;
    background: white;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    .box{
        float: right;
        margin-top: 6px;
        margin-right:10px;
        width: 90px;
    }
}
.ComplaintFactory{
    background-color: white;
    width: 100%;
}
</style>