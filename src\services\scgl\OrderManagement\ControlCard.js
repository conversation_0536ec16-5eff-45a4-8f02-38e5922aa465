import { request, METHOD } from '@/utils/request'
// 获取订单列表
export async function proCardInfoList(params) {
    return request("api/app/pro-card-info/pro-card-info-list", METHOD.GET, params)
}
// 获取管制卡编辑参数
export async function cardInfoEdit(Id) {
    return request(`/api/app/pro-card-info/card-info-edit/${Id}`, METHOD.GET, )
}
// 保存流程卡编辑参数
export async function saveCardInfoEdit(params) {
    return request(`/api/app/pro-card-info/save-card-info-edit`, METHOD.POST, params)
}
// 获取过序数量 
export async function getPassFlowNum(params) {
    return request(`/api/app/pro-card-info/is-allow-pass-flow-num?CardNo=${params}`, METHOD.GET, )
}
// 获取操作日志 
export async function cardInfoLogList(Id) {
    return request(`/api/app/pro-card-info/pro-card-info-log-list/${Id}`, METHOD.GET, )
}
// 删除过序
export async function deleteStep(params) {
    return request(`/api/app/pro-card-info/delete-step`, METHOD.POST,params )
}
// 压合合卡列表
export async function mergeCardList(params) {
    return request(`/api/app/pro-card-info/merge-card-list`, METHOD.GET,params )
}
// 压合合卡
export async function mergeCard(params) {
    return request(`/api/app/pro-card-info/merge-card?cardNo=${params}`, METHOD.POST )
}
// 设置补料
export async function setFeeded(Id) {
    return request(`/api/app/pro-card-info/set-feeded/${Id}`, METHOD.POST, )
}
// 取消补料
export async function setNotFeeded(Id) {
    return request(`/api/app/pro-card-info/set-not-feeded/${Id}`, METHOD.POST, )
}
// 设置加急
export async function seturgent(Id) {
    return request(`/api/app/pro-card-info/set-urgent/${Id}`, METHOD.POST, )
}
// 取消加急
export async function setnoturgent(Id) {
    return request(`/api/app/pro-card-info/set-not-urgent/${Id}`, METHOD.POST, )
}
// 暂停生产
export async function setstop(Id) {
    return request(`/api/app/pro-card-info/set-stop/${Id}`, METHOD.POST, )
}
// 启动生产
export async function setnotstop(Id) {
    return request(`/api/app/pro-card-info/set-not-stop/${Id}`, METHOD.POST, )
}
// 工厂下拉值
export async function factorCode() {
    return request(`/api/app/pro-card-info/factory-code`, METHOD.GET, )
}
export default {
    proCardInfoList,
    cardInfoEdit,
    saveCardInfoEdit,
    getPassFlowNum,
    cardInfoLogList,
    deleteStep,
    mergeCardList,
    mergeCard,
    seturgent,
    setnoturgent,
    setstop,
    setnotstop,
}