<template>
    <a-card :bordered="false" style="padding:20px;">
       <quill-editor ref="myTextEditor" style="height: 420px" v-model="form.crnteContent" @blur="onEditorBlur($event)" @focus="onEditorFocus($event)" @ready="onEditorReady($event)" @change="onEditorChange($event)">
        :options="editorOption">
      </quill-editor>
    </a-card>
</template>


<script>
import PageLayout from "../../layouts/PageLayout";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { quillEditor } from "vue-quill-editor";

export default {
  name: "editor",
  data: function() {
    return {
      form: {
        crnteTitle: "",
        crnteContent: ""
      },
      editorOption: {
        placeholder: "请输入您要发布的内容"
      }
    };
  },
  components: {
    quillEditor
  },
  methods: {
    onEditorBlur(quill) {
    },
    onEditorFocus(quill) {
    },
    onEditorReady(quill) {
    },
    onEditorChange({ quill, html, text }) {
      this.content = html;
    }
  }
};
</script>

<style lang="less" scoped>
.title {
  color: @title-color;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  
}
</style>
