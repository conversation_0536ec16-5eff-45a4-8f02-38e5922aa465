<!-- 市场管理-订单详情基本分片1  -->
<template>
  <div class="contentInfo" ref="SelectBox">
    <a-card :bordered="false">
      <a-form-model layout="inline" id="formDataElem1" v-show="!editFlag" class="bborder">
        <div
          class="div1"
          style="
            width: 85%;
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            justify-content: start;
            max-height: 612px;
            align-content: flex-start;
          "
        >
          <a-form-model-item
            :class="showData.plateTypeStr ? 'divitem' : ''"
            label="文件名称"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pcbFileName"
            class="pcbFileName"
          >
            <div>
              <a v-if="showData.pcbFileName" :title="showData.pcbFileName" @click="down1" class="tmp">{{ showData.pcbFileName }}</a>
            </div>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.custNo ? 'divitem' : ''"
            label="客户代码"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.custNo"
            class="pcbFileName"
          >
            <div>
              <span v-if="showData.custNo" :title="showData.custNo" class="tmp">{{ showData.custNo }}</span>
            </div>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.plateTypeStr ? 'divitem' : ''"
            label="订单类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.plateTypeStr"
          >
            <span>{{ showData.plateTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fileFormat ? 'divitem' : ''"
            label="文件格式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fileFormat"
          >
            <span>{{ showData.fileFormat }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardThickness ? 'divitem' : ''"
            label="成品板厚"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardThickness"
          >
            <span v-if="!editFlag">{{ showData.boardThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardThicknessTol ? 'divitem' : ''"
            label="板厚公差"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardThicknessTol"
          >
            <span v-if="!editFlag">{{ showData.boardThicknessTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardLayers || showData.boardLayers == '0' ? 'divitem' : ''"
            label="层数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardLayers || showData.boardLayers == '0'"
          >
            <span>{{ showData.boardLayers }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="(Number(showData.boardLayers) > 2 && showData.innerCopperThickness) || showData.copperThickness ? 'divitem' : ''"
            label="成品铜厚(oz)"
            :label-col="{ span: 7 }"
            class="ThicknessTreatmentoz"
            :wrapper-col="{ span: 17 }"
            v-show="(Number(showData.boardLayers) > 2 && showData.innerCopperThickness) || showData.copperThickness"
          >
            <span style="word-break: break-all">
              <span v-if="showData.innerCopperThickness && Number(showData.boardLayers) > 2">[内]{{ showData.innerCopperThickness }} </span>
              <span v-if="showData.copperThickness">[外] {{ showData.copperThickness }}</span>
              <span v-if="showData.cuThickness && !showData.isCopperThickConversion">[铜厚]{{ showData.cuThickness }}</span>
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="
              showData.cuThickness || (Number(showData.boardLayers) > 2 && showData.innerCopperThickness) || showData.copperThickness ? 'divitem' : ''
            "
            label="成品铜厚(um)"
            class="ThicknessTreatmentum"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-if="showData.isCopperThickConversion"
            v-show="showData.cuThickness || (Number(showData.boardLayers) > 2 && showData.innerCopperThickness2) || showData.copperThickness2"
          >
            <span style="word-break: break-all">
              <span v-if="showData.innerCopperThickness2 && Number(showData.boardLayers) > 2">[内]{{ showData.innerCopperThickness2 }} </span>
              <span v-if="showData.copperThickness2">[外] {{ showData.copperThickness2 }}</span>
              <span v-if="showData.cuThickness">[铜厚]{{ showData.cuThickness }}</span>
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.surfaceFinishStr ? 'divitem' : ''"
            class="SurfaceTreatment"
            label="表面处理"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-if="showData.surfaceFinishJsonDto"
            v-show="showData.surfaceFinishStr"
          >
            <span v-if="!editFlag" style="width: 70%">
              <span style="width: 30%">
                {{ showData.surfaceFinishStr }}
              </span>
              <span v-if="showData.surfaceFinish == 'hardgoldplating'" style="margin: 0 0.5%; width: 36%"
                >水金:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                  showData.surfaceFinish == 'immersiongold' ||
                  showData.surfaceFinish == 'immersiongoldandosp' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                  showData.surfaceFinish == 'wqpxandjbdj' ||
                  showData.surfaceFinish == 'cjandjbdj' ||
                  showData.surfaceFinish == 'zbdjandosp' ||
                  showData.surfaceFinish == 'tinprecipitation' ||
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'chemicalsilver' ||
                  showData.surfaceFinish == 'fullgoldplating' ||
                  showData.surfaceFinish == 'hardgoldplating' ||
                  showData.surfaceFinish == 'waterplatedgold' ||
                  showData.surfaceFinish == 'tinplating' ||
                  showData.surfaceFinish == 'goldplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'goldplating' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating' ||
                  showData.surfaceFinish == 'goldnickelplatingandchemical' ||
                  showData.surfaceFinish == 'goldnickelplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'fullgilding' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'djszandyqpx' ||
                  showData.surfaceFinish == 'djszandcj' ||
                  showData.surfaceFinish == 'zbcjandcjsz' ||
                  showData.surfaceFinish == 'hjandty' ||
                  showData.surfaceFinish == 'cyandty' ||
                  showData.surfaceFinish == 'djszandwqpx' ||
                  showData.surfaceFinish == 'dnanddjsz' ||
                  showData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                  showData.surfaceFinish == 'electrogold' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'immersionnickelgold' ||
                  showData.surfaceFinish == 'thickgoldplating' ||
                  showData.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                  showData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                  showData.surfaceFinish == 'nickelgoldplating' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  showData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                  showData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                  showData.surfaceFinish == 'Antioxidant+golddeposition' ||
                  showData.surfaceFinish == 'Tinspraying+goldplating' ||
                  showData.surfaceFinish == 'Leadfreetinspraying+goldplating' ||
                  showData.surfaceFinish == 'immersiongoldanddj'
                "
                style="margin: 0 0.5%; width: 26%"
              >
                面积:{{ showData.surfaceFinishJsonDto.platedArea }}%</span
              >
              <span v-if="showData.surfaceFinish == 'djszandcj'" style="margin: 0 0.5%; width: 36%"
                >化金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'djszandcj'" style="margin: 0 0.5%; width: 36%"
                >镀金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'wholegoldplating'" style="margin: 0 0.5%; width: 36%"
                >镀金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiongold' ||
                  showData.surfaceFinish == 'goldplatingandosp' ||
                  showData.surfaceFinish == 'wholegoldplating'
                "
                style="margin: 0 0.5%; width: 26%"
              >
                镀面积:{{ showData.surfaceFinishJsonDto.platedArea }}%</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiongold' ||
                  showData.surfaceFinish == 'goldplatingandosp' ||
                  showData.surfaceFinish == 'djszandyqpx' ||
                  showData.surfaceFinish == 'djszandwqpx' ||
                  showData.surfaceFinish == 'dnanddjsz'
                "
                style="margin: 0 0.5%; width: 36%"
                >镀金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'goldplatingandimmersiongold'" style="margin: 0 0.5%; width: 26%">
                面积:{{ showData.surfaceFinishJsonDto.platedArea2 }}%</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'immersiongold' ||
                  showData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'nickelgoldplating' ||
                  showData.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                  showData.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                  showData.surfaceFinish == 'immersionnickelgold' ||
                  showData.surfaceFinish == 'thickgoldplating' ||
                  showData.surfaceFinish == 'goldplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'immersiongoldandosp' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating' ||
                  showData.surfaceFinish == 'goldnickelplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandchemical' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'fullgilding' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'electrogold' ||
                  showData.surfaceFinish == 'nbjandcj' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'zbdjandjbelectrogold' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                  showData.surfaceFinish == 'wqpxandjbdj' ||
                  showData.surfaceFinish == 'cjandjbdj' ||
                  showData.surfaceFinish == 'zbdjandosp' ||
                  showData.surfaceFinish == 'haslwithleadandGoldfinger' ||
                  showData.surfaceFinish == 'nbjandgoldplating' ||
                  showData.surfaceFinish == 'immersiongoldanddj' ||
                  showData.surfaceFinish == 'ospandfinger' ||
                  showData.surfaceFinish == 'immersiongoldandty' ||
                  showData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                  showData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                  showData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                  showData.surfaceFinish == 'Antioxidant+golddeposition' ||
                  showData.surfaceFinish == 'Tinspraying+goldplating' ||
                  showData.surfaceFinish == 'Leadfreetinspraying+goldplating'
                "
                style="margin: 0 0.5%; width: 18%"
                >金:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              ><span
                v-if="
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                style="margin: 0 0.5%; width: 20%"
                >薄金:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'nbjandcj' ||
                  showData.surfaceFinish == 'nbjandgoldplating'
                "
                style="margin: 0 0.5%; width: 23%"
                >钯厚:{{ showData.surfaceFinishJsonDto.paThickness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'haslwithlead' ||
                  showData.surfaceFinish == 'tinnedcerium' ||
                  showData.surfaceFinish == 'haslwithfree' ||
                  showData.surfaceFinish == 'spraytin' ||
                  showData.surfaceFinish == 'tinplating' ||
                  showData.surfaceFinish == 'haslwithleadandGoldfinger' ||
                  showData.surfaceFinish == 'wqpxandty' ||
                  showData.surfaceFinish == 'yqpxandty' ||
                  showData.surfaceFinish == 'haslwithfreeandGoldfinger'
                "
                style="margin: 0 0.5%; width: 40%"
                >锡厚:{{ showData.surfaceFinishJsonDto.newTinThickness }}um</span
              >
              <span v-if="showData.surfaceFinish == 'tinprecipitation'" style="margin: 0 0.5%; width: 40%"
                >锡厚:{{ showData.surfaceFinishJsonDto.newTinThickness2 }}um</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'eletrolyticnickel' ||
                  showData.surfaceFinish == 'hardgoldplating' ||
                  showData.surfaceFinish == 'waterplatedgold' ||
                  showData.surfaceFinish == 'immersiongold' ||
                  showData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                  showData.surfaceFinish == 'immersiongoldandosp' ||
                  showData.surfaceFinish == 'nickelpalladiumgold' ||
                  showData.surfaceFinish == 'nickelgoldplating' ||
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'immersionnickelgold' ||
                  showData.surfaceFinish == 'thickgoldplating' ||
                  showData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                  showData.surfaceFinish == 'nbjandcj' ||
                  showData.surfaceFinish == 'nbjandgoldplating' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'wholegoldplating' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating' ||
                  showData.surfaceFinish == 'goldnickelplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandchemical' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp' ||
                  showData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                  showData.surfaceFinish == 'fullgilding' ||
                  showData.surfaceFinish == 'electrogold' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                  showData.surfaceFinish == 'wqpxandjbdj' ||
                  showData.surfaceFinish == 'cjandjbdj' ||
                  showData.surfaceFinish == 'zbdjandosp' ||
                  showData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                  showData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                  showData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                  showData.surfaceFinish == 'Antioxidant+golddeposition' ||
                  showData.surfaceFinish == 'Tinspraying+goldplating' ||
                  showData.surfaceFinish == 'Leadfreetinspraying+goldplating'
                "
                style="margin: 0 0.5%; width: 30%"
              >
                镍:{{ showData.surfaceFinishJsonDto.cjNickelThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'osp' ||
                  showData.surfaceFinish == 'ospandfinger' ||
                  showData.surfaceFinish == 'ospandty' ||
                  showData.surfaceFinish == 'wholegoldplatingandosp'
                "
                style="margin: 0 0.5%; width: 40%"
                >膜厚:{{ showData.surfaceFinishJsonDto.filmThickness }}um</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'chemicalsilver' ||
                  showData.surfaceFinish == 'cyandty' ||
                  showData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                  showData.surfaceFinish == 'silverplating' ||
                  showData.surfaceFinish == 'outsourcingsilverplating' ||
                  showData.surfaceFinish == 'electroplatingsilverelectroplatinggold'
                "
                style="margin: 0 0.5%; width: 40%"
                >银厚:{{ showData.surfaceFinishJsonDto.newSilverThickness }}um</span
              >
              <span v-if="showData.surfaceFinish == 'goldplatingandimmersiontin'" style="margin: 0 0.5%; width: 31%"
                >镀金面积:{{ showData.surfaceFinishJsonDto.platedArea }}</span
              >
              <span v-if="showData.surfaceFinish == 'goldplatingandimmersiontin'" style="margin: 0 0.5%; width: 18%"
                >镀金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiontin' ||
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating'
                "
                style="margin: 0 0 0 0.5%; width: 30%"
                >面积:{{ showData.surfaceFinishJsonDto.platedArea2 }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'fullgoldplating' ||
                  showData.surfaceFinish == 'goldplating' ||
                  showData.surfaceFinish == 'waterplatedgold' ||
                  showData.surfaceFinish == 'zbcjandcjsz' ||
                  showData.surfaceFinish == 'yqpxandcjsz' ||
                  showData.surfaceFinish == 'wqpxandcjsz' ||
                  showData.surfaceFinish == 'ospandcjsz' ||
                  showData.surfaceFinish == 'goldplatingandhaslwithfree' ||
                  showData.surfaceFinish == 'hjandty'
                "
                style="margin: 0 0.5%; width: 36%"
              >
                金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness }}{{ showData.surfaceFinishThickUnit }}</span
              >

              <span
                v-if="
                  showData.surfaceFinish == 'goldplatingandimmersiongold' ||
                  showData.surfaceFinish == 'nbjandgoldplating' ||
                  showData.surfaceFinish == 'immersiongoldanddj'
                "
                style="margin: 0 0.5%; width: 36%"
                >金厚:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                "
                style="margin: 0 0.5%; width: 36%"
                >厚金:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'waterhardgold' ||
                  showData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                  showData.surfaceFinish == 'immersiongoldandgoldplating'
                "
                style="margin: 0 0.5%; width: 36%"
                >金123:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span v-if="showData.surfaceFinish == 'hardgoldplating'" style="margin: 0 0.5%; width: 36%"
                >硬金:{{ showData.surfaceFinishJsonDto.imGoldThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
              <span
                v-if="
                  showData.surfaceFinish == 'hardgoldplating' ||
                  showData.surfaceFinish == 'goldplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                  showData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                  showData.surfaceFinish == 'Immersionnickelgoldplatinghardgold'
                "
                style="margin: 0 0.5%; width: 26%"
              >
                面积:{{ showData.surfaceFinishJsonDto.platedArea2 }}%</span
              >
              <span
                v-if="showData.surfaceFinish == 'hardgoldplating' || showData.surfaceFinish == 'immersiongoldandjbelectrogold'"
                style="margin: 0 0.5%; width: 26%"
              >
                镍:{{ showData.surfaceFinishJsonDto.cjNickelThinckness2 }}{{ showData.surfaceFinishThickUnit }}</span
              >
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderColorStr ? 'divitem' : ''"
            label="阻焊颜色"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderColorStr || showData.solderColorBottomStr"
          >
            <span>
              <span v-if="showData.solderColorStr" style="margin-right: 0.5%">[顶层]</span>{{ showData.solderColorStr }}
              <span v-if="showData.solderColorBottomStr" style="margin: 0 0.5%">[底层]</span>{{ showData.solderColorBottomStr }}</span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderResistInkStr ? 'divitem' : ''"
            label="阻焊油墨"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderResistInkStr"
          >
            <span :title="showData.solderResistInkStr">{{ showData.solderResistInkStr }} </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderInkThickness ? 'divitem' : ''"
            label="阻焊油墨厚:"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderInkThickness"
          >
            <span>{{ showData.solderInkThickness }}um</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.solderCoverStr ? 'divitem' : ''"
            label="过孔处理"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.solderCoverStr"
          >
            <span>{{ showData.solderCoverStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isInkNotHalogen ? 'divitem' : ''"
            label="无卤油墨"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isInkNotHalogen"
          >
            <span>{{ showData.isInkNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fontColorStr || showData.fontColorBottomStr ? 'divitem' : ''"
            label="字符颜色"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fontColorStr || showData.fontColorBottomStr"
          >
            <span
              ><span v-if="showData.fontColorStr" style="margin-right: 0.5%">[顶层]</span>{{ showData.fontColorStr }}
              <span v-if="showData.fontColorBottomStr" style="margin: 0 0.5%">[底层]</span>{{ showData.fontColorBottomStr }}
            </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.characterResistInkStr ? 'divitem' : ''"
            label="字符油墨"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.characterResistInkStr"
          >
            <span :title="showData.characterResistInkStr">{{ showData.characterResistInkStr }} </span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.characterNotHalogen ? 'divitem' : ''"
            label="字符无卤"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.characterNotHalogen"
          >
            <span>{{ showData.characterNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.pinBanType ? 'divitem' : ''"
            label="拼版方式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pinBanType"
          >
            <span>{{ showData.pinBanType }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.su ? 'divitem' : ''"
            label="SU数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.su"
          >
            <span>{{ showData.su }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardHeight ? 'divitem' : ''"
            label="单元长度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardHeight"
          >
            <span>{{ showData.boardHeight }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardWidth ? 'divitem' : ''"
            label="单元宽度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardWidth"
          >
            <span>{{ showData.boardWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.setBoardHeight ? 'divitem' : ''"
            label="成品长度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.setBoardHeight"
          >
            <span>{{ showData.setBoardHeight }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.setBoardWidth ? 'divitem' : ''"
            label="成品宽度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.setBoardWidth"
          >
            <span>{{ showData.setBoardWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.formingTypeStr ? 'divitem' : ''"
            label="成型方式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.formingTypeStr"
          >
            <span>{{ showData.formingTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.formingTol ? 'divitem' : ''"
            label="成型公差"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.formingTol"
          >
            <span>{{ showData.formingTol }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.packagRequireStr ? 'divitem' : ''"
            label="包装方式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.packagRequireStr"
          >
            <span>{{ showData.packagRequireStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardTypeStr ? 'divitem' : ''"
            label="拼版单位"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardTypeStr"
          >
            <span>{{ showData.boardTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.sheetTraderStr ? 'divitem' : ''"
            label="板材厂商"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.sheetTraderStr"
          >
            <span>{{ showData.sheetTraderStr }}</span>
          </a-form-model-item>

          <a-form-model-item
            :class="showData.isCustomerBoard ? 'divitem' : ''"
            label="客供板材"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCustomerBoard"
          >
            <span>{{ showData.isCustomerBoard ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fR4TypeStr ? 'divitem' : ''"
            label="板材类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fR4TypeStr"
          >
            <span>{{ showData.fR4TypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isNotHalogen ? 'divitem' : ''"
            label="无卤板材"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isNotHalogen"
          >
            <span>{{ showData.isNotHalogen ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardBrandStr ? 'divitem' : ''"
            label="板材型号"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardBrandStr"
          >
            <span>{{ showData.boardBrandStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.cafResistance ? 'divitem' : ''"
            label="耐CAF"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.cafResistance"
          >
            <span>{{ showData.cafResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fR4TgStr ? 'divitem' : ''"
            label="板材TG"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fR4TgStr"
          >
            <span>{{ showData.fR4TgStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.plateCtiStr ? 'divitem' : ''"
            label="板材CTI"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.plateCtiStr"
          >
            <span>{{ showData.plateCtiStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.sheetSize ? 'divitem' : ''"
            label="大料尺寸"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.sheetSize"
          >
            <span>{{ showData.sheetSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.sheetUtilization ? 'divitem' : ''"
            label="利用率(%)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.sheetUtilization"
          >
            <span>{{ showData.sheetUtilization }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.notAcceptPatching ? 'divitem' : ''"
            label="不接受补线"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.notAcceptPatching"
          >
            <span>{{ showData.notAcceptPatching ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.acceptCrossed ? 'divitem' : ''"
            label="不接受叉板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.acceptCrossed"
          >
            <span>{{ showData.acceptCrossed ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ipcLevelStr ? 'divitem' : ''"
            label="验收标准"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ipcLevelStr"
          >
            <span>{{ showData.ipcLevelStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.warpageStr ? 'divitem' : ''"
            label="板翘"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.warpageStr"
          >
            <span>{{ showData.warpageStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.markPositionStr ? 'divitem' : ''"
            label="标记位置"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.markPositionStr"
          >
            <span>{{ showData.markPositionStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ulTypeStr ? 'divitem' : ''"
            label="UL类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ulTypeStr"
          >
            <span>{{ showData.ulTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.markTypeStr ? 'divitem' : ''"
            label="标记类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.markTypeStr"
          >
            <span>{{ showData.markTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.markFaceStr ? 'divitem' : ''"
            label="标记面向"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.markFaceStr"
          >
            <span>{{ showData.markFaceStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.periodicFormatStr ? 'divitem' : ''"
            label="周期格式"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.periodicFormatStr"
          >
            <span>{{ showData.periodicFormatStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.productUsageStr ? 'divitem' : ''"
            label="产品用途"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.productUsageStr"
          >
            <span>{{ showData.productUsageStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.custAssignment ? 'divitem' : ''"
            label="客户指定"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.custAssignment"
          >
            <span>{{ showData.custAssignment ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.counterPressure ? 'divitem' : ''"
            label="对压"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.counterPressure"
          >
            <span>{{ showData.counterPressure ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.lineWidth ? 'divitem' : ''"
            label="外层最小线宽mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.lineWidth"
          >
            <span>{{ showData.lineWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.lineSpacing ? 'divitem' : ''"
            label="外层最小线距mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.lineSpacing"
          >
            <span>{{ showData.lineSpacing }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.icSize ? 'divitem' : ''"
            label="最小IC尺寸mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.icSize"
          >
            <span>{{ showData.icSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.bgaSize ? 'divitem' : ''"
            label="最小BGA(mil)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.bgaSize"
          >
            <span>{{ showData.bgaSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.vias ? 'divitem' : ''"
            label="通孔最小孔mm"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.vias"
          >
            <span>{{ showData.vias }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.totalHoleNum ? 'divitem' : ''"
            label="单元通孔孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.totalHoleNum"
          >
            <span>{{ showData.totalHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.slotHoleNum ? 'divitem' : ''"
            label="单元槽(散孔)数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.slotHoleNum"
          >
            <span>{{ showData.slotHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testPointSize ? 'divitem' : ''"
            label="最小测试点mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testPointSize"
          >
            <span>{{ showData.testPointSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testPointNum ? 'divitem' : ''"
            label="单元测试点数量"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testPointNum"
          >
            <span>{{ showData.testPointNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.holetoline ? 'divitem' : ''"
            label="内层孔到线mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.holetoline && showMore"
          >
            <span>{{ showData.holetoline }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.vCutKnifeNum ? 'divitem' : ''"
            label="V-CUT刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.vCutKnifeNum"
          >
            <span>{{ showData.vCutKnifeNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.jumpCutXt ? 'divitem' : ''"
            label="跳V刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.jumpCutXt"
          >
            <span>{{ showData.jumpCutXt }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.impGroupNum ? 'divitem' : ''"
            label="阻抗组数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.impGroupNum"
          >
            <span
              ><span v-if="showData.impGroupNum"> {{ showData.impGroupNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isImpUnTol ? 'divitem' : ''"
            label="阻抗非常规公差"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isImpUnTol"
          >
            <span>{{ showData.isImpUnTol ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.inLineWidth ? 'divitem' : ''"
            label="内层最小线宽mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.inLineWidth && showMore"
          >
            <span>{{ showData.inLineWidth }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.inLineSpacing ? 'divitem' : ''"
            label="内层最小线距mil"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.inLineSpacing && showMore"
          >
            <span>{{ showData.inLineSpacing }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isChangeLayerPres ? 'divitem' : ''"
            label="层压不可更改"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isChangeLayerPres"
          >
            <span>{{ showData.isChangeLayerPres ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.gbNum ? 'divitem' : ''"
            label="光板数1"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.gbNum"
          >
            <span>{{ showData.gbNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ppNum ? 'divitem' : ''"
            label="PP张数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ppNum"
          >
            <span>{{ showData.ppNum }}</span>
          </a-form-model-item>

          <a-form-model-item
            :class="showData.specialPpNum ? 'divitem' : ''"
            label="特殊PP张数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.specialPpNum"
          >
            <span>{{ showData.specialPpNum }}</span>
          </a-form-model-item>

          <a-form-model-item
            :class="showData.pressTimesStr || showData.drillTimesStr ? 'divitem' : ''"
            label="压合/钻孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pressTimesStr || showData.drillTimesStr"
          >
            <span v-if="showData.drillTimesStr && showData.pressTimesStr">{{ showData.pressTimesStr }}/{{ showData.drillTimesStr }}</span>
            <span v-else-if="showData.drillTimesStr">{{ showData.drillTimesStr }}</span>
            <span v-else>{{ showData.pressTimesStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindBuryOrderStr ? 'divitem' : ''"
            label="盲埋阶数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindBuryOrderStr"
          >
            <span>{{ showData.blindBuryOrderStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindHoleNum ? 'divitem' : ''"
            label="机械盲孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindHoleNum"
          >
            <span>{{ showData.blindHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindHoleMin ? 'divitem' : ''"
            label="最小盲埋孔mm"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindHoleMin"
          >
            <span>{{ showData.blindHoleMin }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindHoleThickMedium ? 'divitem' : ''"
            label="盲孔厚介质"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindHoleThickMedium"
          >
            <span>{{ showData.blindHoleThickMedium ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserOrderStr ? 'divitem' : ''"
            label="激光阶数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserOrderStr"
          >
            <span>{{ showData.laserOrderStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserNumStr || showData.laserTypeStr ? 'divitem' : ''"
            label="激光次数/类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserNumStr || showData.laserTypeStr"
          >
            <span>{{ showData.laserNumStr }}/{{ showData.laserTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserMinHole ? 'divitem' : ''"
            label="激光最小孔mm"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserMinHole"
          >
            <span v-if="!editFlag">{{ showData.laserMinHole }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.laserHoleNum ? 'divitem' : ''"
            label="激光孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.laserHoleNum"
          >
            <span v-if="!editFlag">{{ showData.laserHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isDiscHole ? 'divitem' : ''"
            label="盘中孔"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isDiscHole"
          >
            <span>{{ showData.isDiscHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.halfSthNum ? 'divitem' : ''"
            label="半边孔边数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.halfSthNum"
          >
            <span v-if="showData.halfSthNum">{{ showData.halfSthNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.profileHoleNum ? 'divitem' : ''"
            label="异形孔孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.profileHoleNum"
          >
            <span v-if="showData.profileHoleNum">{{ showData.profileHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.intPlateEdge ? 'divitem' : ''"
            label="板边包金边数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.intPlateEdge"
          >
            <span v-if="showData.intPlateEdge">{{ showData.intPlateEdge }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isMetalSlot ? 'divitem' : ''"
            label="金属铣槽"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isMetalSlot"
          >
            <span v-if="!editFlag">{{ showData.isMetalSlot ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.stepHoleNum ? 'divitem' : ''"
            label="沉孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.stepHoleNum"
          >
            <span v-if="showData.stepHoleNum">{{ showData.stepHoleNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.serialNumber ? 'divitem' : ''"
            label="序列号个数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.serialNumber"
          >
            <span
              ><span v-if="showData.serialNumber">{{ showData.serialNumber }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isBlueGum ? 'divitem' : ''"
            label="蓝胶"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isBlueGum"
          >
            <span v-if="!showData.blueGumPercentage">{{ showData.isBlueGum ? "是" : "" }}</span>
            <span v-if="showData.blueGumPercentage">{{ showData.blueGumPercentage }}%</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldenFingerAreaRe ? 'divitem' : ''"
            label="金手指面积(%)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldenFingerAreaRe"
          >
            <span>{{ showData.goldenFingerAreaRe }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldenFingerArea ? 'divitem' : ''"
            label="金手指条数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldenFingerArea"
          >
            <span>{{ showData.goldenFingerArea }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerThickness ? 'divitem' : ''"
            label='金手指金厚U"'
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerThickness"
          >
            <span>{{ showData.goldfingerThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerNieThickness ? 'divitem' : ''"
            label='金手指镍厚U"'
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerNieThickness"
          >
            <span>{{ showData.goldfingerNieThickness }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerSize ? 'divitem' : ''"
            label="金手指尺寸"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerSize"
          >
            <span>{{ showData.goldfingerSize }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.specialMaterialSpecStr ? 'divitem' : ''"
            label="特殊料规格"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.specialMaterialSpecStr"
          >
            <span>{{ showData.specialMaterialSpecStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isCrimpHole ? 'divitem' : ''"
            label="压接孔"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCrimpHole"
          >
            <span>{{ showData.isCrimpHole ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.backDrillNum ? 'divitem' : ''"
            label="背钻孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.backDrillNum"
          >
            <span
              ><span v-if="showData.backDrillNum">{{ showData.backDrillNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.throughHoleNum ? 'divitem' : ''"
            label="通孔控深次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.throughHoleNum"
          >
            <span
              ><span v-if="showData.throughHoleNum">{{ showData.throughHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.depthControl ? 'divitem' : ''"
            label="控深深度(mm)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.depthControl"
          >
            <span
              ><span v-if="showData.depthControl">{{ showData.depthControl }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ctrlBlindHole ? 'divitem' : ''"
            label="盲孔控深次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ctrlBlindHole"
          >
            <span
              ><span v-if="showData.ctrlBlindHole">{{ showData.ctrlBlindHole }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.countersinkAngle ? 'divitem' : ''"
            label="沉孔角度"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.countersinkAngle"
          >
            <span
              ><span v-if="showData.countersinkAngle">{{ showData.countersinkAngle }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.steppedHoleNum ? 'divitem' : ''"
            label="阶梯孔孔数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.steppedHoleNum"
          >
            <span
              ><span v-if="showData.steppedHoleNum">{{ showData.steppedHoleNum }}</span></span
            >
          </a-form-model-item>

          <a-form-model-item
            :class="showData.isCarbonOil ? 'divitem' : ''"
            label="碳油"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCarbonOil"
          >
            <span v-if="!showData.carbonOilPercentage">{{ showData.isCarbonOil ? "是" : "" }}</span>
            <span v-if="showData.carbonOilPercentage">{{ showData.carbonOilPercentage }}%</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.pasteRedTape ? 'divitem' : ''"
            label="高温胶(处)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pasteRedTape"
          >
            <span>{{ showData.pasteRedTape }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.innerBevelNum ? 'divitem' : ''"
            label="内斜边刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.innerBevelNum"
          >
            <span>{{ showData.innerBevelNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.conventionBevelNum ? 'divitem' : ''"
            label="常规斜边刀数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.conventionBevelNum"
          >
            <span>{{ showData.conventionBevelNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.blindSlotNum ? 'divitem' : ''"
            label="盲槽(个数)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.blindSlotNum"
          >
            <span>{{ showData.blindSlotNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.depthControlArea ? 'divitem' : ''"
            label="控深面积(m/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.depthControlArea"
          >
            <span>{{ showData.depthControlArea }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.buriedCopper ? 'divitem' : ''"
            label="埋铜"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.buriedCopper"
          >
            <span>{{ showData.buriedCopper ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.silverPlugHoleNum ? 'divitem' : ''"
            label="银浆塞孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.silverPlugHoleNum"
          >
            <span
              ><span v-if="showData.silverPlugHoleNum">{{ showData.silverPlugHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.cuPlugHoleNum ? 'divitem' : ''"
            label="铜浆塞孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.cuPlugHoleNum"
          >
            <span
              ><span v-if="showData.cuPlugHoleNum">{{ showData.cuPlugHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.resinPlugHoleNum ? 'divitem' : ''"
            label="树脂塞孔次数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.resinPlugHoleNum"
          >
            <span
              ><span v-if="showData.resinPlugHoleNum">{{ showData.resinPlugHoleNum }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.plateNumber ? 'divitem' : ''"
            label="板材张数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.plateNumber"
          >
            <span>{{ showData.plateNumber }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.apertureRatio ? 'divitem' : ''"
            label="板厚孔径比"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.apertureRatio"
          >
            <span>{{ showData.apertureRatio }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.routLength ? 'divitem' : ''"
            label="锣带长度(m/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.routLength"
          >
            <span>{{ showData.routLength }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.poreDensity ? 'divitem' : ''"
            label="孔密度(W/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.poreDensity"
          >
            <span>{{ showData.poreDensity }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testPointsm2 ? 'divitem' : ''"
            label="测试点数(W/㎡)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testPointsm2"
          >
            <span>{{ (showData.testPointsm2 / 1000).toFixed(2) || 0 }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isDifficultyBoard ? 'divitem' : ''"
            label="难度板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isDifficultyBoard"
          >
            <span>{{ showData.isDifficultyBoard ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.minHoleCopper ? 'divitem' : ''"
            label="最小孔铜um"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.minHoleCopper"
          >
            <span>{{ showData.minHoleCopper }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.totalLayer ? 'divitem' : ''"
            label="假层"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.totalLayer"
          >
            <span>{{ showData.totalLayer }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.pinBanNum ? 'divitem' : ''"
            label="合拼款数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.pinBanNum"
          >
            <span>{{ showData.pinBanNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isFacHp ? 'divitem' : ''"
            label="工厂合拼"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isFacHp"
          >
            <span>{{ showData.isFacHp }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isCoilPlate ? 'divitem' : ''"
            label="线圈板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isCoilPlate"
          >
            <span>{{ showData.isCoilPlate ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.ppap ? 'divitem' : ''"
            label="PPAP"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.ppap"
          >
            <span>{{ showData.ppap ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.thermalConductivity ? 'divitem' : ''"
            label="导热系数w/m.k"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.thermalConductivity"
          >
            <span>{{ showData.thermalConductivity }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isSolderMaskIpc3 ? 'divitem' : ''"
            label="阻焊三级标准"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isSolderMaskIpc3"
          >
            <span>{{ showData.isSolderMaskIpc3 ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.buriedResistance ? 'divitem' : ''"
            label="埋阻"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.buriedResistance"
          >
            <span>{{ showData.buriedResistance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isTransferOrder ? 'divitem' : ''"
            label="客户转单"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isTransferOrder"
          >
            <span>{{ showData.isTransferOrder ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.lowResistanceTest ? 'divitem' : ''"
            label="低阻测试"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.lowResistanceTest"
          >
            <span>{{ showData.lowResistanceTest ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.confirmWorkingDraft ? 'divitem' : ''"
            label="确认工作稿"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.confirmWorkingDraft"
          >
            <span>{{ showData.confirmWorkingDraft ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.confirmImpedance ? 'divitem' : ''"
            label="确认阻抗"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.confirmImpedance"
          >
            <span>{{ showData.confirmImpedance ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.confirmStacking ? 'divitem' : ''"
            label="确认叠层"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.confirmStacking"
          >
            <span>{{ showData.confirmStacking ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.attachedShippingFilm ? 'divitem' : ''"
            label="附出货菲林"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.attachedShippingFilm"
          >
            <span>{{ showData.attachedShippingFilm ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.humidityCardStr ? 'divitem' : ''"
            label="放湿度卡"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.humidityCardStr"
          >
            <span>{{ showData.humidityCardStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.boardSpacingPaperStr ? 'divitem' : ''"
            label="板间隔纸"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.boardSpacingPaperStr"
          >
            <span>{{ showData.boardSpacingPaperStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.withoutDesiccantStr ? 'divitem' : ''"
            label="干燥剂"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.withoutDesiccantStr"
          >
            <span>{{ showData.withoutDesiccantStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.packagingQuantity ? 'divitem' : ''"
            label="包装数量(PCS)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.packagingQuantity"
          >
            <span
              ><span v-if="showData.packagingQuantity">{{ showData.packagingQuantity }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.stampETStr ? 'divitem' : ''"
            label="加盖ET章"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.stampETStr"
          >
            <span>{{ showData.stampETStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.spareQuantity ? 'divitem' : ''"
            label="备品数量(PCS)"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.spareQuantity"
          >
            <span
              ><span v-if="showData.spareQuantity">{{ showData.spareQuantity }}</span></span
            >
          </a-form-model-item>
          <a-form-model-item
            :class="showData.relatedBrandStr ? 'divitem' : ''"
            label="相关品牌"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.relatedBrandStr"
          >
            <span>{{ showData.relatedBrandStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.brandMark ? 'divitem' : ''"
            label="品牌标记"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.brandMark"
          >
            <span>{{ showData.brandMark }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.goldfingerHpNum ? 'divitem' : ''"
            label="金手指合拼款数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.goldfingerHpNum"
          >
            <span>{{ showData.goldfingerHpNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isOrderReview ? 'divitem' : ''"
            label="订单评审"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isOrderReview"
          >
            <span>{{ showData.isOrderReview ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.freePanel ? 'divitem' : ''"
            label="自由拼版"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.freePanel"
          >
            <span>{{ showData.freePanel ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isLedPlate ? 'divitem' : ''"
            label="LED板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isLedPlate"
          >
            <span>{{ showData.isLedPlate ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isUncoverPlate ? 'divitem' : ''"
            label="揭盖板"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isUncoverPlate"
          >
            <span>{{ showData.isUncoverPlate ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isHva ? 'divitem' : ''"
            label="HVA"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isHva"
          >
            <span>{{ showData.isHva ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.isImpTestStrip ? 'divitem' : ''"
            label="提供阻抗条"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.isImpTestStrip"
          >
            <span>{{ showData.isImpTestStrip ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.npFilmsNum ? 'divitem' : ''"
            label="NP更改菲林数"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.npFilmsNum"
          >
            <span>{{ showData.npFilmsNum }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.npChangeTypeStr ? 'divitem' : ''"
            label="NP更改类型"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.npChangeTypeStr"
          >
            <span>{{ showData.npChangeTypeStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.testTimesDenseStr ? 'divitem' : ''"
            label="测试架倍密"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.testTimesDenseStr"
          >
            <span>{{ showData.testTimesDenseStr }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.fourWireTest ? 'divitem' : ''"
            label="四线测试"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.fourWireTest"
          >
            <span>{{ showData.fourWireTest ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.layProtectiveFilm ? 'divitem' : ''"
            label="贴保护膜"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.layProtectiveFilm"
          >
            <span>{{ showData.layProtectiveFilm ? "是" : "" }}</span>
          </a-form-model-item>
          <a-form-model-item
            label="二维码方式"
            :title="showData.qrCodeWay"
            v-show="showData.qrCodeWay"
            :class="showData.qrCodeWay ? 'divitem' : ''"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
          >
            <span>{{ showData.qrCodeWay }}</span>
          </a-form-model-item>
          <a-form-model-item
            :class="showData.waterMark_ ? 'divitem' : ''"
            label="水印"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
            v-show="showData.waterMark_"
          >
            <span>{{ showData.waterMark_ ? "是" : "" }}</span>
          </a-form-model-item>
        </div>
        <div style="width: 85%">
          <div class="div2">
            <a-form-model-item
              label="型号备注"
              :class="showData.note ? 'div22' : ''"
              v-show="showData.note"
              style="border-top: 1px solid #ddd; width: 100%"
              class="editWrapper1"
            >
              <span>{{ showData.note }}</span>
            </a-form-model-item>
            <a-form-model-item
              label="备注确认"
              :class="showData.noteSure ? 'div22' : ''"
              v-show="showData.noteSure"
              style="border-top: 1px solid #ddd; width: 100%"
              class="editWrapper1"
            >
              <span>{{ showData.noteSure }}</span>
            </a-form-model-item>
            <a-form-model-item
              label="特殊要求"
              style="border-top: 1px solid #ddd"
              :class="showData.specialRequire ? 'div22' : ''"
              class="editWrapper1"
              v-show="showData.specialRequire"
            >
              <span>{{ showData.specialRequire }}</span>
            </a-form-model-item>
          </div>
        </div>
      </a-form-model>
      <a-form-model layout="inline" id="formDataElem" v-show="editFlag">
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="文件名称"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="requiredLinkConfigList.pcbFileName && requiredLinkConfigList.pcbFileName.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper" v-if="editFlag">
                <span v-if="!formData.pcbFileName">不存在</span>
                <a v-else :title="showData.pcbFileName" @click="down1" class="tmp">{{ showData.pcbFileName }}</a>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="拼版方式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.pinBanType1 && requiredLinkConfigList.pinBanType1.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper" v-if="editFlag">
                <a-input
                  v-model="formData.pinBanType1"
                  style="margin-left: 0px; width: 42px; border-bottom-right-radius: 0; border-top-right-radius: 0"
                  @change="numChange"
                />
                <span
                  style="
                    display: block;
                    background: #f3f3f3;
                    height: 22px;
                    line-height: 22px;
                    width: 12px;
                    text-align: center;
                    border-top: 1px solid #d9d9d9;
                    border-bottom: 1px solid #d9d9d9;
                  "
                  >X</span
                >
                <a-input
                  v-model="formData.pinBanType2"
                  style="margin-right: 0px; width: 40px; border-top-left-radius: 0; border-bottom-left-radius: 0"
                  @change="numChange"
                />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="SU数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.su && requiredLinkConfigList.su.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper" v-if="editFlag">
                <a-input v-model="formData.su" @change="numChange" disabled> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="不接受补线"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.notAcceptPatching && requiredLinkConfigList.notAcceptPatching.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper" v-if="editFlag">
                <a-checkbox v-model="formData.notAcceptPatching" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="客户代码"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="requiredLinkConfigList.custNo && requiredLinkConfigList.custNo.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper" v-if="editFlag">
                <a-select
                  placeholder="请选择客户代码"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  v-model="formData.custNo"
                  :dropdownMatchSelectWidth="false"
                  showSearch
                  optionFilterProp="children"
                  @popupScroll="handlePopupScroll"
                  allowClear
                  @change="supValue1"
                  @search="supValue"
                >
                  <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item" :title="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="单元长度"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.boardHeight && iseval(requiredLinkConfigList.boardHeight.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="formData.boardHeight" allowClear @change="numChange"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="单元宽度"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.boardWidth && iseval(requiredLinkConfigList.boardWidth.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="formData.boardWidth" allowClear @change="numChange"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="不接受叉板"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.acceptCrossed && iseval(requiredLinkConfigList.acceptCrossed.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.acceptCrossed" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3">
            <a-form-model-item
              label="订单类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.plateType && requiredLinkConfigList.plateType.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.plateType"
                  style="width: 99px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="change1"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    style="color: blue"
                    v-for="(item, index) in mapKey(selectOption.PlateType)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="文件格式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.fileFormat && requiredLinkConfigList.fileFormat.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.fileFormat"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.FileFormat)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="成品长度"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.setBoardHeight && iseval(requiredLinkConfigList.setBoardHeight.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="formData.setBoardHeight" @change="numChange" allowClear> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="成品宽度"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.setBoardWidth && iseval(requiredLinkConfigList.setBoardWidth.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input v-model="formData.setBoardWidth" @change="numChange" allowClear> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="验收标准"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.ipcLevel && iseval(requiredLinkConfigList.ipcLevel.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.ipcLevel"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="iPCLevelC"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in IPCLevel" :key="index" :value="item.value" :lable="item.lable" :title="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="3">
            <a-form-model-item
              label="成品板厚"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.boardThickness && iseval(requiredLinkConfigList.boardThickness.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  style="display: inline-block"
                  v-model="formData.boardThickness"
                  showSearch
                  allowClear
                  :filter-option="filterOption"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="setEstimate($event, mapKey(selectOption.BoardThickness))"
                  @search="handleSearch($event, mapKey(selectOption.BoardThickness))"
                  @blur="handleBlur($event, mapKey(selectOption.BoardThickness))"
                  :defaultActiveFirstOption="false"
                  @keyup.enter.native="$event.target.blur()"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.BoardThickness)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <a-select
                  style="display: inline-block"
                  dropdown-class-name="dropdownTol"
                  v-model="formData.boardThicknessTol"
                  showSearch
                  allowClear
                  :filter-option="filterOption"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  :defaultActiveFirstOption="false"
                  @keyup.enter.native="$event.target.blur()"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.BoardThicknessTol)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="层数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.boardLayers && iseval(requiredLinkConfigList.boardLayers.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.boardLayers"
                  @change="changeCoper"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.BoardLayers)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="成型方式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.formingType && iseval(requiredLinkConfigList.formingType.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.formingType"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="changeforming"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.FormingType)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="成型公差"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.formingTol && iseval(requiredLinkConfigList.formingTol.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.formingTol"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.FormingTol)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item label="包装方式" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div class="editWrapper">
                <a-select
                  v-model="formData.packagRequire"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.PackagRequire)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="9">
            <a-form-model-item
              label="表面处理"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              :class="requiredLinkConfigList.surfaceFinish && iseval(requiredLinkConfigList.surfaceFinish.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper" style="display: flex">
                <a-select
                  v-model="formData.surfaceFinish"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 24%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="changesurface"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.SurfaceFinish)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin: 0 0.5%; width: 33%" v-if="formData.surfaceFinish == 'hardgoldplating'"
                  >水金
                  <a-select
                    style="display: inline-block; width: 35%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span style="margin: 0 0.5%; width: 27%" v-if="formData.surfaceFinish == 'wholegoldplating'"
                  >镀金厚
                  <a-select
                    style="display: inline-block; width: 47%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span style="margin: 0 0.5%; width: 27%" v-if="formData.surfaceFinish == 'hardgoldplating'"
                  >面积
                  <a-input
                    style="display: inline-block; width: 32%; padding: 0.4% 0"
                    :title="formData.surfaceFinishJsonDto.platedArea"
                    v-model="formData.surfaceFinishJsonDto.platedArea"
                  />%
                </span>
                <span style="margin: 0 0 0 0.5%; width: 33%" v-if="formData.surfaceFinish == 'hardgoldplating'">
                  镍<a-input
                    style="display: inline-block; width: 50%; padding: 0.4% 0 0.4% 2px"
                    :title="formData.surfaceFinishJsonDto.cjNickelThinckness"
                    v-model="formData.surfaceFinishJsonDto.cjNickelThinckness"
                  />
                  {{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0.5%; width: 22%"
                  v-if="
                    formData.surfaceFinish == 'immersiongold' ||
                    formData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    formData.surfaceFinish == 'immersiongoldandosp' ||
                    formData.surfaceFinish == 'nickelpalladiumgold' ||
                    formData.surfaceFinish == 'tinprecipitation' ||
                    formData.surfaceFinish == 'chemicalsilver' ||
                    formData.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    formData.surfaceFinish == 'zbcjandcjsz' ||
                    formData.surfaceFinish == 'fullgoldplating' ||
                    formData.surfaceFinish == 'goldplating' ||
                    formData.surfaceFinish == 'waterplatedgold' ||
                    formData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                    formData.surfaceFinish == 'tinplating' ||
                    formData.surfaceFinish == 'waterhardgold' ||
                    formData.surfaceFinish == 'immersiongoldandgoldplating' ||
                    formData.surfaceFinish == 'djszandcj' ||
                    formData.surfaceFinish == 'goldnickelplatingandosp' ||
                    formData.surfaceFinish == 'goldnickelplatingandchemical' ||
                    formData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    formData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    formData.surfaceFinish == 'wholegoldplatingandosp' ||
                    formData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    formData.surfaceFinish == 'fullgilding' ||
                    formData.surfaceFinish == 'electrogold' ||
                    formData.surfaceFinish == 'hjandty' ||
                    formData.surfaceFinish == 'cyandty' ||
                    formData.surfaceFinish == 'djszandwqpx' ||
                    formData.surfaceFinish == 'djszandyqpx' ||
                    formData.surfaceFinish == 'dnanddjsz' ||
                    formData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                    formData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    formData.surfaceFinish == 'wqpxandjbdj' ||
                    formData.surfaceFinish == 'cjandjbdj' ||
                    formData.surfaceFinish == 'zbdjandosp' ||
                    formData.surfaceFinish == 'immersionnickelgold' ||
                    formData.surfaceFinish == 'thickgoldplating' ||
                    formData.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    formData.surfaceFinish == 'goldplatinglocalthickgold' ||
                    formData.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    formData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    formData.surfaceFinish == 'nickelgoldplating' ||
                    formData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    formData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    formData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    formData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    formData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    formData.surfaceFinish == 'Antioxidant+golddeposition' ||
                    formData.surfaceFinish == 'Tinspraying+goldplating' ||
                    formData.surfaceFinish == 'Leadfreetinspraying+goldplating' ||
                    formData.surfaceFinish == 'immersiongoldanddj'
                  "
                  :class="
                    (formData.surfaceFinish == 'immersiongoldandosp' ||
                      formData.surfaceFinish == 'cyandty' ||
                      formData.surfaceFinish == 'djszandwqpx' ||
                      formData.surfaceFinish == 'djszandyqpx' ||
                      formData.surfaceFinish == 'immersiongold' ||
                      formData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                      formData.surfaceFinish == 'zbcjandcjsz' ||
                      formData.surfaceFinish == 'nickelpalladiumgold' ||
                      formData.surfaceFinish == 'dnanddjsz' ||
                      formData.surfaceFinish == 'hjandty' ||
                      formData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                      formData.surfaceFinish == 'electrogold' ||
                      formData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                      formData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                      formData.surfaceFinish == 'Antioxidant+golddeposition' ||
                      formData.surfaceFinish == 'Tinspraying+goldplating' ||
                      formData.surfaceFinish == 'Leadfreetinspraying+goldplating') &&
                    required
                      ? 'require11'
                      : ''
                  "
                  >面积
                  <a-input
                    style="display: inline-block; width: 43%; padding: 0.4% 0"
                    :title="formData.surfaceFinishJsonDto.platedArea"
                    v-model="formData.surfaceFinishJsonDto.platedArea"
                  />%
                </span>
                <span
                  style="margin: 0 0.5%; width: 26%"
                  :class="
                    (formData.surfaceFinish == 'goldplatingandimmersiongold' || formData.surfaceFinish == 'goldplatingandosp') && required
                      ? 'require11'
                      : ''
                  "
                  v-if="
                    formData.surfaceFinish == 'goldplatingandimmersiongold' ||
                    formData.surfaceFinish == 'goldplatingandosp' ||
                    formData.surfaceFinish == 'wholegoldplating'
                  "
                  >镀面积
                  <a-input
                    style="display: inline-block; width: 30%; padding: 0.4% 0"
                    :title="formData.surfaceFinishJsonDto.platedArea"
                    v-model="formData.surfaceFinishJsonDto.platedArea"
                  />%
                </span>
                <span
                  style="margin: 0 0.5%; width: 26%"
                  :class="
                    (formData.surfaceFinish == 'goldplatingandimmersiongold' ||
                      formData.surfaceFinish == 'djszandwqpx' ||
                      formData.surfaceFinish == 'djszandyqpx' ||
                      formData.surfaceFinish == 'dnanddjsz' ||
                      formData.surfaceFinish == 'goldplatingandosp') &&
                    required
                      ? 'require11'
                      : ''
                  "
                  v-if="
                    formData.surfaceFinish == 'goldplatingandimmersiongold' ||
                    formData.surfaceFinish == 'goldplatingandosp' ||
                    formData.surfaceFinish == 'djszandyqpx' ||
                    formData.surfaceFinish == 'djszandwqpx' ||
                    formData.surfaceFinish == 'dnanddjsz'
                  "
                  >镀金厚
                  <a-select
                    style="display: inline-block; width: 41%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span style="margin: 0 0.5%; width: 29%" v-if="formData.surfaceFinish == 'djszandcj'"
                  >化金厚
                  <a-select
                    style="display: inline-block; width: 45%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span style="margin: 0 0.5%; width: 29%" v-if="formData.surfaceFinish == 'djszandcj'"
                  >镀金厚
                  <a-select
                    style="display: inline-block; width: 41%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate4($event)"
                    @search="handleSearch4($event)"
                    @blur="handleBlur4($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span style="margin: 0 0.5%; width: 22%" v-if="formData.surfaceFinish == 'goldplatingandimmersiongold'"
                  >面积
                  <a-input
                    style="display: inline-block; width: 48%; padding: 0.4% 0"
                    :title="formData.surfaceFinishJsonDto.platedArea2"
                    v-model="formData.surfaceFinishJsonDto.platedArea2"
                  />%
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 23%"
                  v-if="
                    formData.surfaceFinish == 'immersiongold' ||
                    formData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    formData.surfaceFinish == 'nickelpalladiumgold' ||
                    formData.surfaceFinish == 'nickelgoldplating' ||
                    formData.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    formData.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    formData.surfaceFinish == 'immersionnickelgold' ||
                    formData.surfaceFinish == 'thickgoldplating' ||
                    formData.surfaceFinish == 'goldplatingandhaslwithlead' ||
                    formData.surfaceFinish == 'nbjandcj' ||
                    formData.surfaceFinish == 'waterhardgold' ||
                    formData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                    formData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    formData.surfaceFinish == 'immersiongoldandosp' ||
                    formData.surfaceFinish == 'immersiongoldandgoldplating' ||
                    formData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    formData.surfaceFinish == 'wqpxandjbdj' ||
                    formData.surfaceFinish == 'cjandjbdj' ||
                    formData.surfaceFinish == 'zbdjandosp' ||
                    formData.surfaceFinish == 'goldnickelplatingandosp' ||
                    formData.surfaceFinish == 'goldnickelplatingandchemical' ||
                    formData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    formData.surfaceFinish == 'wholegoldplatingandosp' ||
                    formData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    formData.surfaceFinish == 'fullgilding' ||
                    formData.surfaceFinish == 'immersiongoldanddj' ||
                    formData.surfaceFinish == 'electrogold' ||
                    formData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                    formData.surfaceFinish == 'zbdjandjbelectrogold' ||
                    formData.surfaceFinish == 'nbjandgoldplating' ||
                    formData.surfaceFinish == 'haslwithleadandGoldfinger' ||
                    formData.surfaceFinish == 'ospandfinger' ||
                    formData.surfaceFinish == 'immersiongoldandty' ||
                    formData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    formData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    formData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    formData.surfaceFinish == 'Antioxidant+golddeposition' ||
                    formData.surfaceFinish == 'Tinspraying+goldplating' ||
                    formData.surfaceFinish == 'Leadfreetinspraying+goldplating'
                  "
                  :class="
                    (formData.surfaceFinish == 'immersiongoldandosp' ||
                      formData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                      formData.surfaceFinish == 'immersiongoldanddj' ||
                      formData.surfaceFinish == 'haslwithleadandGoldfinger' ||
                      formData.surfaceFinish == 'immersiongoldandty' ||
                      formData.surfaceFinish == 'nbjandgoldplating' ||
                      formData.surfaceFinish == 'electrogold' ||
                      formData.surfaceFinish == 'nbjandcj' ||
                      formData.surfaceFinish == 'nickelpalladiumgold' ||
                      formData.surfaceFinish == 'ospandfinger' ||
                      formData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                      formData.surfaceFinish == 'immersiongold' ||
                      formData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                      formData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                      formData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                      formData.surfaceFinish == 'Antioxidant+golddeposition' ||
                      formData.surfaceFinish == 'Tinspraying+goldplating' ||
                      formData.surfaceFinish == 'Leadfreetinspraying+goldplating') &&
                    required
                      ? 'require11'
                      : ''
                  "
                  >金
                  <a-select
                    style="display: inline-block; width: 45%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  v-if="
                    formData.surfaceFinish == 'goldplatinglocalthickgold' ||
                    formData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    formData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    formData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                  "
                  style="margin: 0 0.5%; width: 25%"
                  >薄金
                  <a-select
                    style="display: inline-block; width: 43%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0.5%; width: 23%"
                  v-if="
                    formData.surfaceFinish == 'nickelpalladiumgold' ||
                    formData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                    formData.surfaceFinish == 'nbjandcj' ||
                    formData.surfaceFinish == 'nbjandgoldplating'
                  "
                  :class="
                    (formData.surfaceFinish == 'nickelpalladiumgold' || formData.surfaceFinish == 'nickelpalladiumgoldandcjsz') && required
                      ? 'require11'
                      : ''
                  "
                  >钯厚
                  <a-input
                    style="display: inline-block; width: 40%; padding: 0.4% 0"
                    :title="formData.surfaceFinishJsonDto.paThickness"
                    v-model="formData.surfaceFinishJsonDto.paThickness"
                  />{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 40%"
                  v-if="
                    formData.surfaceFinish == 'haslwithlead' ||
                    formData.surfaceFinish == 'tinnedcerium' ||
                    formData.surfaceFinish == 'haslwithfree' ||
                    formData.surfaceFinish == 'spraytin' ||
                    formData.surfaceFinish == 'tinplating' ||
                    formData.surfaceFinish == 'wqpxandty' ||
                    formData.surfaceFinish == 'yqpxandty' ||
                    formData.surfaceFinish == 'haslwithleadandGoldfinger' ||
                    formData.surfaceFinish == 'haslwithfreeandGoldfinger'
                  "
                  >锡厚
                  <a-select
                    style="display: inline-block; width: 52.8%"
                    v-model="formData.surfaceFinishJsonDto.newTinThickness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate10($event)"
                    @search="handleSearch10($event)"
                    @blur="handleBlur10($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.Tinthickness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                  <!-- <a-input style="display: inline-block; width: 52.8%" v-model="formData.surfaceFinishJsonDto.newTinThickness" />um -->
                </span>
                <span style="margin: 0 0 0 0.5%; width: 40%" v-if="formData.surfaceFinish == 'tinprecipitation'"
                  >锡厚
                  <a-select
                    style="display: inline-block; width: 52.8%"
                    v-model="formData.surfaceFinishJsonDto.newTinThickness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate11($event)"
                    @search="handleSearch11($event)"
                    @blur="handleBlur11($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.Tinthickness2)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 19%"
                  v-if="
                    formData.surfaceFinish == 'eletrolyticnickel' ||
                    formData.surfaceFinish == 'nickelgoldplating' ||
                    formData.surfaceFinish == 'goldplatinglocalthickgold' ||
                    formData.surfaceFinish == 'immersionnickelgold' ||
                    formData.surfaceFinish == 'thickgoldplating' ||
                    formData.surfaceFinish == 'nickelpalladiumgold' ||
                    formData.surfaceFinish == 'nickelpalladiumgoldandcjsz' ||
                    formData.surfaceFinish == 'nbjandcj' ||
                    formData.surfaceFinish == 'nbjandgoldplating' ||
                    formData.surfaceFinish == 'waterhardgold' ||
                    formData.surfaceFinish == 'wholegoldplating' ||
                    formData.surfaceFinish == 'immersiongoldandgoldplating' ||
                    formData.surfaceFinish == 'goldnickelplatingandosp' ||
                    formData.surfaceFinish == 'goldnickelplatingandchemical' ||
                    formData.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    formData.surfaceFinish == 'wholegoldplatingandosp' ||
                    formData.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    formData.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    formData.surfaceFinish == 'fullgilding' ||
                    formData.surfaceFinish == 'electrogold' ||
                    formData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                    formData.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    formData.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    formData.surfaceFinish == 'Antioxidant+golddeposition' ||
                    formData.surfaceFinish == 'Tinspraying+goldplating' ||
                    formData.surfaceFinish == 'Leadfreetinspraying+goldplating'
                  "
                  :class="
                    (formData.surfaceFinish == 'eletrolyticnickel' || formData.surfaceFinish == 'nickelpalladiumgold') &&
                    required &&
                    this.$route.query.factory != 58 &&
                    this.$route.query.factory != 59 &&
                    this.$route.query.factory != 38
                      ? 'require11'
                      : ''
                  "
                >
                  镍<a-input
                    style="display: inline-block; width: 45%; padding: 0.4% 0 0.4% 2px"
                    :title="formData.surfaceFinishJsonDto.cjNickelThinckness"
                    v-model="formData.surfaceFinishJsonDto.cjNickelThinckness"
                    allowClear
                  />
                  {{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0.5%; width: 26%"
                  :class="
                    (formData.surfaceFinish == 'nbjandgoldplating' || formData.surfaceFinish == 'immersiongoldanddj') && required ? 'require11' : ''
                  "
                  v-if="
                    formData.surfaceFinish == 'goldplatingandimmersiongold' ||
                    formData.surfaceFinish == 'nbjandgoldplating' ||
                    formData.surfaceFinish == 'immersiongoldanddj'
                  "
                  >金厚
                  <a-select
                    style="display: inline-block; width: 48%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate4($event)"
                    @search="handleSearch4($event)"
                    @blur="handleBlur4($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0.5%; width: 24%"
                  v-if="
                    formData.surfaceFinish == 'goldplatinglocalthickgold' ||
                    formData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    formData.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    formData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                  "
                  >厚金
                  <a-select
                    style="display: inline-block; width: 41%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate4($event)"
                    @search="handleSearch4($event)"
                    @blur="handleBlur4($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 24%"
                  v-if="
                    formData.surfaceFinish == 'immersiongold' ||
                    formData.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    formData.surfaceFinish == 'immersiongoldandosp' ||
                    formData.surfaceFinish == 'waterplatedgold' ||
                    formData.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    formData.surfaceFinish == 'wqpxandjbdj' ||
                    formData.surfaceFinish == 'cjandjbdj' ||
                    formData.surfaceFinish == 'zbdjandosp'
                  "
                  :class="
                    (formData.surfaceFinish == 'immersiongold' || formData.surfaceFinish == 'immersiongoldandGoldfinger') &&
                    required &&
                    this.$route.query.factory != 58 &&
                    this.$route.query.factory != 59 &&
                    this.$route.query.factory != 38
                      ? 'require11'
                      : ''
                  "
                >
                  镍<a-select
                    style="display: inline-block; width: 60%; padding: 0.4% 0 0.4% 2px"
                    v-model="formData.surfaceFinishJsonDto.cjNickelThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate2($event)"
                    @search="handleSearch2($event)"
                    @blur="handleBlur2($event)"
                  >
                    <a-select-option :value="120" :lable="120" :title="'120'">120</a-select-option>
                  </a-select>
                  {{ showData.surfaceFinishThickUnit }}
                </span>
                <span style="margin: 0 0.5%; width: 33%" v-if="formData.surfaceFinish == 'hardgoldplating'"
                  >硬金
                  <a-select
                    style="display: inline-block; width: 35%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate4($event)"
                    @search="handleSearch4($event)"
                    @blur="handleBlur4($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 40%"
                  v-if="
                    formData.surfaceFinish == 'osp' ||
                    formData.surfaceFinish == 'ospandfinger' ||
                    formData.surfaceFinish == 'ospandty' ||
                    formData.surfaceFinish == 'wholegoldplatingandosp'
                  "
                  >膜厚
                  <a-input
                    style="display: inline-block; width: 54%"
                    :title="formData.surfaceFinishJsonDto.filmThickness"
                    v-model="formData.surfaceFinishJsonDto.filmThickness"
                  />um
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 40%"
                  v-if="
                    formData.surfaceFinish == 'chemicalsilver' ||
                    formData.surfaceFinish == 'cyandty' ||
                    formData.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    formData.surfaceFinish == 'silverplating' ||
                    formData.surfaceFinish == 'outsourcingsilverplating' ||
                    formData.surfaceFinish == 'electroplatingsilverelectroplatinggold'
                  "
                  :class="formData.surfaceFinish == 'cyandty' && required ? 'require11' : ''"
                  >银厚
                  <a-input
                    style="display: inline-block; width: 54%"
                    :title="formData.surfaceFinishJsonDto.newSilverThickness"
                    v-model="formData.surfaceFinishJsonDto.newSilverThickness"
                  />um
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 33%"
                  v-if="formData.surfaceFinish == 'goldplatingandimmersiontin'"
                  :class="formData.surfaceFinish == 'goldplatingandimmersiontin' && required ? 'require11' : ''"
                  >镀金面积
                  <a-input
                    style="display: inline-block; width: 50%"
                    :title="formData.surfaceFinishJsonDto.platedArea"
                    v-model="formData.surfaceFinishJsonDto.platedArea"
                  />%
                </span>
                <span
                  style="margin: 0 0.5%; width: 28%"
                  v-if="formData.surfaceFinish == 'goldplatingandimmersiontin'"
                  :class="formData.surfaceFinish == 'goldplatingandimmersiontin' && required ? 'require11' : ''"
                  >镀金厚
                  <a-select
                    style="display: inline-block; width: 52%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 24%"
                  v-if="
                    formData.surfaceFinish == 'goldplatingandimmersiontin' ||
                    formData.surfaceFinish == 'waterhardgold' ||
                    formData.surfaceFinish == 'immersiongoldandgoldplating'
                  "
                  >面积
                  <a-input
                    style="display: inline-block; width: 47%; padding: 0 2px 2px 2px"
                    :title="formData.surfaceFinishJsonDto.platedArea2"
                    v-model="formData.surfaceFinishJsonDto.platedArea2"
                  />%
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 20%"
                  v-if="
                    formData.surfaceFinish == 'waterhardgold' ||
                    formData.surfaceFinish == 'immersiongoldandjbelectrogold' ||
                    formData.surfaceFinish == 'immersiongoldandgoldplating'
                  "
                  >金
                  <a-select
                    style="display: inline-block; width: 44%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate4($event)"
                    @search="handleSearch4($event)"
                    @blur="handleBlur4($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 20%"
                  v-if="
                    formData.surfaceFinish == 'hardgoldplating' ||
                    formData.surfaceFinish == 'goldplatinglocalthickgold' ||
                    formData.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    formData.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    formData.surfaceFinish == 'Immersionnickelgoldplatinghardgold'
                  "
                  >面积
                  <a-input
                    style="display: inline-block; width: 37%; padding: 0.4% 0 0.4% 2px"
                    :title="formData.surfaceFinishJsonDto.platedArea2"
                    v-model="formData.surfaceFinishJsonDto.platedArea2"
                  />%
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 26%"
                  v-if="formData.surfaceFinish == 'hardgoldplating' || formData.surfaceFinish == 'immersiongoldandjbelectrogold'"
                >
                  镍<a-input
                    style="display: inline-block; width: 40%; padding: 0.4% 0 0.4% 2px"
                    :title="formData.surfaceFinishJsonDto.cjNickelThinckness2"
                    v-model="formData.surfaceFinishJsonDto.cjNickelThinckness2"
                  />
                  {{ showData.surfaceFinishThickUnit }}
                </span>
                <span
                  style="margin: 0 0 0 0.5%; width: 36%"
                  v-if="
                    formData.surfaceFinish == 'fullgoldplating' ||
                    formData.surfaceFinish == 'waterplatedgold' ||
                    formData.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    formData.surfaceFinish == 'goldplating' ||
                    formData.surfaceFinish == 'zbcjandcjsz' ||
                    formData.surfaceFinish == 'hjandty' ||
                    formData.surfaceFinish == 'yqpxandcjsz' ||
                    formData.surfaceFinish == 'wqpxandcjsz' ||
                    formData.surfaceFinish == 'ospandcjsz'
                  "
                  :class="
                    (formData.surfaceFinish == 'hjandty' ||
                      formData.surfaceFinish == 'ospandcjsz' ||
                      formData.surfaceFinish == 'wqpxandcjsz' ||
                      formData.surfaceFinish == 'zbcjandcjsz' ||
                      formData.surfaceFinish == 'yqpxandcjsz') &&
                    required
                      ? 'require11'
                      : ''
                  "
                  >金厚
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="formData.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="setEstimate5($event)"
                    @search="handleSearch5($event)"
                    @blur="handleBlur5($event)"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectOption.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ showData.surfaceFinishThickUnit }}
                </span>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="拼版单位"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.boardType && iseval(requiredLinkConfigList.boardType.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select v-model="formData.boardType" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.BoardType)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="板翘"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.warpage && iseval(requiredLinkConfigList.warpage.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select v-model="formData.warpage" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.Warpage)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <two-mkt
          @ApertureRatio="ApertureRatio"
          :spinning="spinning"
          :editFlag="editFlag"
          :showData="showData"
          :selectOption="selectOption"
          :boardBrandList="boardBrandList"
          :sheetTraderList="sheetTraderList"
          :boardtgList="boardtgList"
          :supList="supList"
          :joinFacId="joinFacId"
          :ManufacturerTG="ManufacturerTG"
          :show0="show0"
          :show1="show1"
          :show2="show2"
          :showMore="showMore"
          :showCG="showCG"
          :showHDI="showHDI"
          :showMM="showMM"
          :reOrder="reOrder"
          :required="required"
          :frontDataZSupplierf="frontDataZSupplierf"
          :requiredLinkConfigList="requiredLinkConfigList"
          @changeCoper="changeCoper"
          @change1="change1"
          ref="twoMkt"
          :formData="formData"
          @cutchange="cutchange"
          :sheetTrader="sheetTrader"
          :obj="obj"
          :copperdata="copperdata"
        ></two-mkt>
        <div :class="editFlag ? 'special' : 'special1'">
          <a-row>
            <a-col :span="24">
              <a-form-model-item
                label="特殊要求"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18 }"
                :class="requiredLinkConfigList.specialRequire && requiredLinkConfigList.specialRequire.isNullRules && required ? 'require' : ''"
              >
                <span>{{ showData.specialRequire }}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
      </a-form-model>
    </a-card>
    <!-- <a-modal
            title="拼版图"
            :visible="makeupVisible"
            :footer="null"
            @cancel="handleCancel"
        >
          <makeup-pic ref="makeup"></makeup-pic>
        </a-modal>     -->
  </div>
  <!-- </spin>  -->
</template>
<script>
//const TwoMkt = () => import('@/pages/mkt/OrderDetail/subassembly/TwoMkt')
import TwoMkt from "@/pages/mkt/OrderDetail/subassembly/TwoMkt";
//import MakeupPic from "@/pages/mkt/OrderDetail/subassembly/MakeupPic";
import $ from "jquery";
export default {
  name: "OneMkt",
  props: [
    "editFlag",
    "spinning",
    "showData",
    "selectOption",
    "boardBrandList",
    "sheetTraderList",
    "messageList",
    "boardtgList",
    "supList",
    "frontDataZSupplierf",
    "reOrder",
    "requiredLinkConfigList",
    "joinFacId",
    "required",
    "ManufacturerTG",
    "copperdata",
  ],
  components: { TwoMkt },
  data() {
    return {
      formData: {},
      dataVisible: false,
      dataVisible1: false,
      makeupVisible: false, // 拼版图弹窗开关
      ReportList: [], // 出货报告列表
      boardLayers1: false,
      blindBuryStr1: false,
      plateTypeStr1: false,
      show0: false,
      show1: false,
      show2: false,
      showMore: false,
      showCG: false,
      showHDI: false,
      showMM: false,
      obj: [],
      sheetTrader: [],
      boardBrand: [],
      src: "",
      IPCLevel: [],
      frontDataZSupplier: [],
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      spinning1: false,
    };
  },
  filters: {
    invoiceFilter(val) {
      let val_ = "";
      switch (val) {
        case 0:
          val_ = "默认";
          break;
        case 1:
          val_ = "1.0w";
          break;
        case 2:
          val_ = "1.5w";
          break;
        case 3:
          val_ = "2.0w";
          break;
        case 4:
          val_ = "0.5w";
          break;
        case 5:
          val_ = "0.7w";
          break;
        case 6:
          val_ = "5w";
          break;
        case 8:
          val_ = "8w";
          break;
        case 9:
          val_ = "3w";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
    camFilter(val) {
      let val_ = "";
      switch (val) {
        case 1:
          val_ = "中级";
          break;
        case 2:
          val_ = "高级";
          break;
        case 3:
          val_ = "资深";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
  },
  created() {
    this.$nextTick(() => {
      this.getEditData();
    });
  },
  mounted() {},
  watch: {
    messageList: {
      handler(val) {
        this.get(val);
      },
    },
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    supList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.frontDataZSupplier = newval.slice(0, 20);
        });
      },
    },
  },

  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().trim().startsWith(input.toLowerCase()) == true;
    },
    cutchange() {
      if (this.$route.query.factory == 12) {
        let ape = null;
        if (this.formData.apertureRatio) {
          ape = this.formData.apertureRatio.split(":")[0];
        }
        if (
          Number(this.formData.boardLayers) >= 10 ||
          Number(this.formData.laserOrder) >= 3 ||
          this.formData.productUsage == "opticalmodule" ||
          Number(this.formData.surfaceFinishJsonDto.imGoldThinckness) >= 30 ||
          Number(this.formData.surfaceFinishJsonDto.imGoldThinckness2) >= 30 ||
          this.formData.buriedResistance ||
          this.formData.buriedCopper ||
          this.formData.blindSlotNum ||
          this.formData.solderCover == "copperplughole" ||
          ape >= 15 ||
          Number(this.formData.innerCopperThickness) >= 6 ||
          Number(this.formData.copperThickness) >= 6
        ) {
          this.formData.isHva = true;
        } else {
          this.formData.isHva = false;
        }
      }
    },
    compare(property) {
      return function (a, b) {
        var value1 = a[property];
        var value2 = b[property];
        return value1 - value2;
      };
    },
    setStyle() {
      const elements = document.getElementsByClassName("divitem");
      const num = elements.length;
      var nums = 24;
      var num1 = 0;
      const SurfaceTreatment = document.getElementsByClassName("SurfaceTreatment");
      const OZ = document.getElementsByClassName("ThicknessTreatmentoz");
      const UM = document.getElementsByClassName("ThicknessTreatmentum");
      for (let index = 0; index < SurfaceTreatment.length; index++) {
        const element1 = SurfaceTreatment[index].innerText;
        if (
          element1.indexOf("水金") != -1 ||
          element1.indexOf("镀金+沉金") != -1 ||
          element1.indexOf("镀水金+镀软金") != -1 ||
          element1.indexOf("整板镀金") != -1 ||
          element1.indexOf("镍钯金+金手指") != -1 ||
          element1.indexOf("镍钯金+镀金") != -1 ||
          element1.indexOf("镀金手指&化金") != -1 ||
          element1.indexOf("沉金+局部电金") != -1 ||
          element1.indexOf("选择性镀金") != -1 ||
          element1.indexOf("镀金+局部厚金") != -1 ||
          element1.indexOf("镍钯金") != -1 ||
          element1.indexOf("镀金不镀镍+局部厚金") != -1 ||
          element1.indexOf("沉金+局部厚金") != -1
        ) {
          SurfaceTreatment[index].children[0].style.height = "49px";
          SurfaceTreatment[index].children[1].children[0].style.height = "49px";
          num1 = 1;
        } else {
          SurfaceTreatment[index].children[0].style.height = "24.5px";
          SurfaceTreatment[index].children[1].children[0].style.height = "24.5px";
        }
      }
      if (UM.length > 0) {
        for (let index = 0; index < UM.length; index++) {
          const element1 = UM[index].innerText;
          num1 = Math.floor(element1.length / 50) + num1;
          UM[index].children[0].style.height = 24.5 * Math.ceil(element1.length / 50) + "px";
          UM[index].children[1].children[0].style.height = 24.5 * Math.ceil(element1.length / 50) + "px";
        }
      }
      if (OZ.length > 0) {
        for (let index = 0; index < OZ.length; index++) {
          const element1 = OZ[index].innerText;
          num1 = Math.floor(element1.length / 50) + num1;
          OZ[index].children[0].style.height = 24.5 * Math.ceil(element1.length / 50) + "px";
          OZ[index].children[1].children[0].style.height = 24.5 * Math.ceil(element1.length / 50) + "px";
        }
      }
      nums = nums - num1;
      for (var a = 0; a < elements.length; a++) {
        if (a < nums) {
          elements[a].style.width = "30%";
          elements[a].childNodes[0].style.width = "28%";
          elements[a].childNodes[1].style.width = "72%";
        } else {
          elements[a].style.width = "17%";
          elements[a].childNodes[0].style.width = "48%";
          elements[a].childNodes[1].style.width = "52%";
        }
      }
      var div2 = document.getElementsByClassName("div2")[0];
      div2.style.width = "64%";
      var div22 = document.getElementsByClassName("div22");
      for (var i = 0; i < div22.length; i++) {
        div22[i].childNodes[0].style.width = "13.2%";
        div22[i].childNodes[1].style.width = "86.8%";
      }
    },
    getEditData() {
      this.IPCLevel = this.mapKey(this.selectOption.IPCLevel);
      this.IPCLevel.forEach(item => {
        if (item.lable.indexOf("III") != -1) {
          item.key = 2;
        } else if (item.lable.indexOf("II") != -1) {
          item.key = 1;
        } else if (item.lable.indexOf("GJ") != -1) {
          item.key = 3;
        } else {
          item.key = 4;
        }
      });
      this.IPCLevel = this.IPCLevel.sort(this.compare("key"));
      if (this.showData.needReportList) {
        this.needReportList = this.showData.needReportList.split(",");
      } else {
        this.needReportList = [];
      }
      if (this.selectOption.SpecialMaterialSpec) {
        let arr = this.mapKey(this.selectOption.FR4TypeSpecialMaterialSpec);
        let arr1 = arr.filter(item => {
          return item.lable == this.showData.boardBrand;
        });
        let obj = [];
        if (arr1.length) {
          for (var a = 0; a < arr1.length; a++) {
            this.selectOption.SpecialMaterialSpec.forEach(ite => {
              if (ite.valueMember == arr1[a].value) {
                obj.push({ valueMember: ite.valueMember, text: ite.text });
              }
            });
          }
        }
        this.obj = obj;
      }
      this.formData = this.showData;
      if (this.boardBrandList.length && this.formData.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.formData.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.formData.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
      this.$refs.twoMkt.boardBrand = this.boardBrand;
      let arr_ = [];
      for (var i = 0; i < this.formData.boardLayers; i++) {
        if (i == 0 || i == this.formData.boardLayers - 1) {
          arr_.push(this.formData.copperThickness);
        } else {
          arr_.push(this.formData.innerCopperThickness);
        }
      }
      if (this.formData.processEdgesStrL == "updown" || this.formData.processEdgesStrL == "leftright" || this.formData.processEdgesStrL == "both") {
        this.formData.processEdgesStrR = 4;
      }

      if (this.formData.ipcLevel > 0) {
        this.formData.ipcLevel = this.formData.ipcLevel.toString();
      }
      if (this.showData.minHoleCopper && this.formData.minHoleCopper == null) {
        this.formData.minHoleCopper = "18";
        this.formData.ipcLevel = "4";
      }
    },
    mapKey(data) {
      if (!data || !data.length) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    // 下载工程文件
    down1() {
      if (!this.showData.pcbFilePath) {
        this.$message.error("暂无文件");
        return;
      }
      if (this.showData.pcbFilePath.indexOf("myhuaweicloud") != -1) {
        let ordermodel1 = "";
        if (this.$route.query.factory == 12 || this.$route.query.factory == 58 || this.$route.query.factory == 59) {
          ordermodel1 = this.showData.customerModel;
        } else {
          ordermodel1 = this.showData.pcbFileName;
        }
        let a = this.showData.pcbFilePath.split(".").slice(-1)[0];
        if (this.showData.pcbFilePath) {
          const xhr = new XMLHttpRequest();
          xhr.open("GET", this.showData.pcbFilePath, true);
          xhr.responseType = "blob";
          xhr.onload = function () {
            if (xhr.status === 200) {
              const blob = xhr.response;
              const link = document.createElement("a");
              link.href = window.URL.createObjectURL(blob);
              link.download = ordermodel1 + "." + a;
              link.style.display = "none";
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }
          };
          xhr.send();
        }
      } else {
        window.location.href = this.showData.pcbFilePath;
      }
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
    changeforming() {
      this.formData.vCutKnifeNum = null;
      this.formData.jumpCutXt = null;
    },
    changesurface() {
      let surf = this.formData.surfaceFinish;
      let surfdto = this.formData.surfaceFinishJsonDto;
      surfdto.imGoldThinckness = null;
      surfdto.paThickness = null;
      surfdto.cjNickelThinckness = null;
      surfdto.filmThickness = null;
      surfdto.newSilverThickness = null;
      if (
        surf == "goldplatingandosp" ||
        surf == "goldplatingandimmersiontin" ||
        surf == "wholegoldplating" ||
        surf == "fullgilding" ||
        surf == "waterplatedgold" ||
        surf == "waterhardgold" ||
        surf == "goldplating" ||
        surf == "electrogold" ||
        surf == "zbdjandosp" ||
        surf == "zbdjandjbelectrogold"
      ) {
        surfdto.platedArea = surfdto.platedArea4;
      } else {
        surfdto.platedArea = surfdto.platedArea3;
      }
      if ((surf == "haslwithlead" || surf == "haslwithfree" || surf == "spraytin") && !surfdto.newTinThickness) {
        if (this.formData.joinFactoryId == 22) {
          surfdto.newTinThickness = "1-40";
        } else if (this.formData.joinFactoryId == 12) {
          surfdto.newTinThickness = "4-40";
        } else if (this.formData.joinFactoryId == 67) {
          surfdto.newTinThickness = "";
        } else {
          surfdto.newTinThickness = "2-25";
        }
      }
      if (surf == "osp") {
        if (this.formData.joinFactoryId == 58 || this.formData.joinFactoryId == 59) {
          surfdto.filmThickness = "0.25";
        } else {
          surfdto.filmThickness = "0.2-0.5";
        }
      }
      if (surf == "tinprecipitation") {
        surfdto.newTinThickness2 = "1-1.3";
      }
      if (surf == "chemicalsilver") {
        if (this.formData.joinFactoryId == 67) {
          surfdto.newSilverThickness = "8-12";
        } else {
          surfdto.newSilverThickness = "0.15-0.5";
        }
      }
      if ((surf == "immersiongold" || surf == "immersiongoldandosp") && this.formData.joinFactoryId == 12) {
        surfdto.cjNickelThinckness = "120-240";
      }
      if (
        (surf == "immersiongold" ||
          surf == "immersiongoldandosp" ||
          surf == "nbjandcj" ||
          surf == "immersiongoldandty" ||
          surf == "immersiongoldanddj") &&
        this.formData.joinFactoryId == 67
      ) {
        surfdto.cjNickelThinckness = "120";
      }
    },
    changeCoper(type) {
      let innerCopper = "";
      let copper = "";
      if (type == "lt") {
        copper = this.copperdata.filter(item => item.CopperThickness2 == this.formData.copperThickness2 && !item.InnerCopper)[0]?.CopperThickness;
        innerCopper = this.copperdata.filter(item => item.CopperThickness2 == this.formData.innerCopperThickness2 && item.InnerCopper)[0]
          ?.CopperThickness;
        this.formData.innerCopperThickness = "";
        this.formData.copperThickness = "";
      } else {
        innerCopper = this.formData.innerCopperThickness;
        copper = this.formData.copperThickness;
        this.formData.innerCopperThickness2 = "";
        this.formData.copperThickness2 = "";
      }
      if (this.formData.plateType != "cg" && this.formData.boardLayers <= 2) {
        this.formData.plateType = "cg";
      }

      if (this.formData.boardLayers < 2) {
        this.formData.minHoleCopper = "";
      }
      let arr_ = [];
      let layer = Number(this.formData.boardLayers);
      if (this.formData.boardLayers % 2 != 0 && this.formData.boardLayers != 1) {
        layer = layer + 1;
      }
      for (var i = 0; i < layer; i++) {
        if (i == 0 || i == layer - 1) {
          arr_.push(copper);
        } else {
          arr_.push(innerCopper);
        }
      }
      this.formData.cuThickness = arr_.join("/");
      this.cutchange();
      if (this.formData.boardLayers <= 2) {
        this.boardLayers1 = true;
      } else {
        this.boardLayers1 = false;
      }
      if (this.formData.blindBury) {
        this.blindBuryStr1 = false;
      } else {
        this.blindBuryStr1 = true;
      }
      this.change1();
    },
    numChange(type) {
      //测试点数(W/㎡)计算
      //测试点数 = 单板测试点*su数/交货尺寸x/交货尺寸y/10000 保留两位小数
      if (this.formData.testPointNum && this.formData.su && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        const testPointNum = this.formData.testPointNum; // 单板测试点
        const su = this.formData.su; // SU数
        const boardHeightM = this.formData.setBoardHeight / 1000; // 交货尺寸Y（转换为米）
        const boardWidthM = this.formData.setBoardWidth / 1000; // 交货尺寸X（转换为米）
        // 计算测试点数
        const testPoints = (testPointNum * su) / boardHeightM / boardWidthM;
        this.formData.testPointsm2 = Math.floor(testPoints);
      } else {
        this.formData.testPointsm2 = null;
      }
      if (this.formData.pinBanType1 && this.formData.pinBanType2) {
        this.formData.su = (Number(this.formData.pinBanType1) * this.formData.pinBanType2).toFixed();
      } else {
        this.formData.su = null;
      }
      if (this.formData.boardType == "set" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.boardArea = ((this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000).toFixed(2);
      }
      if (this.formData.boardType == "pcs" && this.formData.num && this.formData.setBoardHeight && this.formData.setBoardWidth && this.formData.su) {
        this.formData.boardArea = (
          (this.formData.num * this.formData.setBoardHeight * this.formData.setBoardWidth) /
          1000000 /
          this.formData.su
        ).toFixed(2);
      }
      if (type != "save") {
        if (this.formData.su >= 2) {
          this.formData.boardType = "SET";
        } else if (
          Number(this.formData.setBoardHeight) > Number(this.formData.boardHeight) ||
          Number(this.formData.setBoardWidth) > Number(this.formData.boardWidth)
        ) {
          this.formData.boardType = "SET";
        } else {
          this.formData.boardType = "PCS";
        }
      }
      if (this.formData.totalHoleNum && this.formData.su && this.formData.setBoardHeight && this.formData.setBoardWidth) {
        this.formData.poreDensity = (
          ((Number(this.formData.totalHoleNum) + Number(this.formData.slotHoleNum) + (Number(this.formData.blindHoleNum) || 0)) * this.formData.su) /
          ((this.formData.setBoardHeight * this.formData.setBoardWidth) / 1000000) /
          10000
        ).toFixed(2);
      } else {
        this.formData.poreDensity = null;
      }
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate(value, list) {
      this.formData.boardThickness = value;
      this.ApertureRatio();
    },
    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
    },
    setEstimate2(value) {
      this.formData.surfaceFinishJsonDto.cjNickelThinckness = value.toString();
    },
    handleSearch2(value) {
      this.setEstimate2(value);
    },
    handleBlur2(value) {
      this.setEstimate2(value);
    },
    setEstimate4(value) {
      this.formData.surfaceFinishJsonDto.imGoldThinckness2 = value;
      this.cutchange();
    },
    handleSearch4(value, list) {
      this.setEstimate4(value, list);
    },
    handleBlur4(value, list) {
      this.setEstimate4(value, list);
    },
    setEstimate5(value) {
      if (this.$route.query.factory != 37 && this.$route.query.factory != 57) {
        this.formData.surfaceFinishJsonDto.imGoldThinckness = value;
      }
      this.cutchange();
    },
    handleSearch5(value, list) {
      this.setEstimate5(value, list);
    },
    handleBlur5(value, list) {
      this.setEstimate5(value, list);
    },
    setEstimate10(value, list) {
      this.formData.surfaceFinishJsonDto.newTinThickness = value;
    },
    handleSearch10(value, list) {
      this.setEstimate10(value, list);
    },
    handleBlur10(value, list) {
      this.setEstimate10(value, list);
    },
    setEstimate11(value, list) {
      this.formData.surfaceFinishJsonDto.newTinThickness2 = value;
    },
    handleSearch11(value, list) {
      this.setEstimate11(value, list);
    },
    handleBlur11(value, list) {
      this.setEstimate11(value, list);
    },
    ApertureRatio() {
      // 孔径比=成品板厚除以最小孔
      if (this.formData.boardThickness && this.formData.vias) {
        this.formData.apertureRatio = (Number(this.formData.boardThickness) / this.formData.vias).toFixed(1) + ":1";
      } else {
        this.formData.apertureRatio = null;
      }
      console.log(this.formData.apertureRatio, "3799");
      this.cutchange();
      if (Number(this.formData.boardThickness) < 1) {
        this.formData.boardThicknessTol = "+/-0.1";
      } else {
        this.formData.boardThicknessTol = "+/-10%"; //'+/-'+(Number(this.formData.boardThickness)/10)
      }
      if (!this.formData.boardThickness) {
        this.formData.boardThicknessTol = "";
      }
    },
    change1() {
      if (this.formData.plateType == "cg") {
        this.showCG = true;
        this.plateTypeStr1 = true;
      } else {
        this.plateTypeStr1 = false;
        this.showCG = false;
      }
      if (this.formData.plateType == "hdi") {
        this.showHDI = true;
      } else {
        this.showHDI = false;
        this.formData.laserOrder = "";
      }
      if (this.formData.plateType == "mm") {
        this.showMM = true;
      } else {
        this.showMM = false;
      }
      if (this.formData.boardLayers == 0) {
        this.show0 = true;
      } else {
        this.show0 = false;
      }
      if (this.formData.boardLayers == 1) {
        this.show1 = true;
      } else {
        this.show1 = false;
      }
      if (this.formData.boardLayers == 2) {
        this.show2 = true;
      } else {
        this.show2 = false;
      }
      if (this.formData.boardLayers > 2) {
        this.showMore = true;
      } else {
        this.showMore = false;
        this.formData.buriedResistance = false;
        this.formData.buriedCopper = false;
        this.formData.innerCopperThickness = null;
      }
      this.cutchange();
    },
    iPCLevelC() {
      // this.$forceUpdate()
      if (this.$route.query.factory == 12) {
        if (this.formData.ipcLevel == "14") {
          this.formData.minHoleCopper = "20";
        } else if (
          this.formData.ipcLevel == "3" ||
          this.formData.ipcLevel == "15" ||
          this.formData.ipcLevel == "16" ||
          this.formData.ipcLevel == "1"
        ) {
          this.formData.minHoleCopper = "25";
        } else {
          this.formData.minHoleCopper = "";
        }
      } else if (this.$route.query.factory == 22) {
        if (this.formData.ipcLevel == "8") {
          this.formData.minHoleCopper = "25";
        }
      }
    },
    get(val) {
      $("#formDataElem .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("formData") != -1) {
        newIsNullRules = newIsNullRules.replace(/formData/g, "this.formData");
      }
      if (val.indexOf("reOrder") != -1) {
        newIsNullRules = newIsNullRules.replace(/reOrder/g, "this.reOrder");
      }
      if (val.indexOf("boardBrand") != -1) {
        newIsNullRules = newIsNullRules.replace(/boardBrand/g, "this.boardBrand");
      }

      return eval(newIsNullRules);
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.toUpperCase().indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let arr = [];
        let that = this;
        that.valueData = value.toUpperCase();
        arr = that.supList.filter(m => m.toUpperCase().indexOf(value.toUpperCase()) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.supList.slice(0, 20);
      }
    },
    supValue1(value) {
      if (!this.formData.custNo) {
        this.supValue(undefined);
      }
    },
  },
};
</script>
<style scoped lang="less">
.bborder {
  .div2 {
    /deep/.ant-form-item-control {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

/deep/b {
  font-weight: 500;
}
.div22 {
  /deep/.ant-form-item-control {
    padding: 0;
    max-height: 65px;
    min-height: 24.5px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
/deep/.require11 {
  color: red !important;
}
// .cu{
//   /deep/.ant-form-item-control{
//     height:26.67px;
//   }
// }
#formDataElem1 {
  .div1 {
    .ant-form-item {
      width: 30%;
    }
    /deep/ .ant-form-item-control-wrapper {
      // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
      font-family: PingFangSC-Regular, Sans-serif;
      font-weight: 500;
      color: #000000;
      font-size: 13px;
      .ant-form-item-control {
        .ant-form-item-children {
          display: block;
          min-height: 13.672px;
        }
        line-height: inherit;
        padding: 2px 4px !important;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        height: 24.5px;
      }
    }
    // /deep/.pcbFileName{
    //   .ant-form-item-label{
    //     width:30%;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:70%;
    //   }
    // }
  }
}
/deep/.ant-input-affix-wrapper {
  width: 100%;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 6px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 2px;
}
/deep/.line3 {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
  // .ant-form-item-control-wrapper {
  //   .ant-form-item-control{
  //     border:0!important;
  //   }
  // }
}
.bbb {
  /deep/.ant-form-item-label {
    width: 16.65%;
    // -ms-width: 101px important; // IE
    // -webkit-width:100.5%; //谷歌
    border-left: 1px solid #ddd;
  }
  /deep/.ant-form-item-control-wrapper {
    // -ms-width: 911px!important; // IE
    // -webkit-width:942.0.5%; //谷歌
    width: 83.3%;
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
/deep/.ant-select-selection--single {
  height: 22px !important;
}
/deep/.ant-select-item-option-content {
  color: red !important;
}
/deep/.ant-select-selection__rendered {
  line-height: 20px !important;
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.require1 {
  .ant-form-item-label > label {
    color: red !important;
    background-color: greenyellow;
  }
}
// /deep/.bac{
//     .ant-form-item-label > label {
//     color: red!important;
//     background-color: #ff9900;
// }

// }
span {
  font-size: 13px;
}

/deep/.ant-select {
  font-size: 13px !important;
}
/deep/.ant-input {
  font-size: 13px !important;
  font-weight: 500;
}
/deep/.dropdownTol {
  min-width: 90px !important;
}
.contentInfo {
  font-size: 13px;
  width: 1676px;
  /deep/.ant-select-dropdown--single {
    min-width: 70px;
  }
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    font: 12px / 1.14 arial;
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 10.5%;
        text-indent: 0.5%;
        font-size: 13px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 10.5%;
      }
    }
    .special {
      height: 268px;
      width: 456px;
      display: inline-block;
      position: absolute;
      right: 172px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 270px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 261px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 270px;
              width: 75px;
            }
          }
        }
      }
    }
    .special1 {
      height: 268px;
      width: 308px;
      display: inline-block;
      position: absolute;
      right: 320px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 267px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 250px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 267px;
            }
          }
        }
      }
    }
    .ant-card-body {
      // display: flex;
      padding: 0;
      .ant-form {
        // width:80.5%;

        border-left: 1px solid #ddd;
        .ant-row {
          .line2 {
            .ant-form-item-label {
              border-bottom: 0px;
            }
          }
          .line {
            .ant-form-item-control {
              border-bottom: 0px;
            }
          }
        }
      }
      .spec {
        width: 19%;
        position: absolute;
        top: 0;
        right: 0;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .tmp {
          word-break: keep-all;
          vertical-align: top;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: inline-block;
        }
        .editWrapper1 {
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            width: 99%;
            height: 22px;
          }
          .ant-input {
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 3px;
          }
          .ant-input-number {
            width: 99%;
          }
        }
        .editWrapper {
          width: 100%;
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            // width:96px;
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 3px;
          }
          .ant-input-number {
            // width: 96px;
            width: 99%;
          }
        }
        .ant-form-item-label {
          // width: 117px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font: 12px/1.14 "微软雅黑", arial;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          // border-left: 1px solid #ddd;
          label {
            // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
            font-family: PingFangSC-Regular, Sans-serif;
            font-size: 13px;
            font-weight: 500;
            color: #000000;
          }
        }
        .ant-form-item-control-wrapper {
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font-family: PingFangSC-Regular, Sans-serif;
          font-weight: 500;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
              white-space: pre-line;
            }
            line-height: inherit;
            padding: 6px 4px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
