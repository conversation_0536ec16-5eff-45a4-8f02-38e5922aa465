<!--客诉复核列表池-->
<template>
    <div class="main">
        <a-table
            :columns="columns"
            :dataSource="checkdata"
            :pagination="pagination"
            @change="handleTableChange" 
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            :scroll="{ x: 1650,y:640 }"
            :rowKey="(record,index) => index"
            bordered
            :class="checkdata.length?'mintable':''"
        >
        <template slot="operation" slot-scope="text, record, index">
            <div v-show="activeKey=='50'">
                <a-button @click="Detail(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">复核</a-button>
            </div> 
            <div v-show="activeKey=='60'">
                <a-button  type="link" size="small" style="padding: 0px;font-size: 12px" @click="interact">沟通</a-button>               
            </div>     
            <div v-show="activeKey=='100'">
                <a-button  type="link" size="small" style="padding: 0px;font-size: 12px" @click="interact">沟通详情</a-button>
            </div>        
        </template>
    </a-table>   
    <a-modal
        title="沟通记录"
        :visible="interactvisible"
        @cancel="interactvisible=false"
        width="800px"
        @ok="interactok"
        centered
        >
        <a-form-model-item  label="沟通记录" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
            <a-textarea :disabled="activeKey!=60"  placeholder="请输入沟通记录" v-model="connectRecord"  :auto-size="{ minRows: 4, maxRows: 6 }" style="width: 100%"/>
        </a-form-model-item>
    </a-modal> 
    </div>
</template>
<script>
import {complaintscustfinish} from "@/services/complaint/QualitativeComplaint.js";
export default {
    props:['checkdata','activeKey','pagination','columns'],
    name:'FactoryTable',
    data(){
        return{
            interactvisible:false,
            selectdata:{},
            proOrderId:'',
            connectRecord:'',
        }
    },
    created(){
    },
    mounted(){

    },
    methods:{
        interact(){
            this.interactvisible=true
        },
        Detail(record){
            this.$router.push({path:'Reviewdetails',query:{id:record.id} })
        },
        handleTableChange(pagination,filter,sorter){
            this.$emit('tableChange', pagination,filter,sorter)
    },
    interactok(){
            let params={
                id:this.selectdata.id,        
                connectRecord:this.connectRecord        
            }
            if(!this.connectRecord && this.activeKey=='60'){
                this.$message.error('请输入沟通记录')
                return
            }
            if(this.activeKey=='60'){
                complaintscustfinish(params).then(res=>{
                if(res.code){
                    this.$message.success(res.message)
                    this.$emit('getdata',{status:this.activeKey})
                }else{
                    this.$message.error(res.message)
                }
                }).finally(()=>{
                    this.interactvisible=false
                })   
            }else{
                this.interactvisible=false
            }           
          },
    onClickRow(record){
                return {
                    on: {
                        click: () => {
                            this.selectdata = record;
                            this.proOrderId=record.id
                            this.connectRecord=record.connectRecord
                        }
                    }
                }
            },
            isRedRow(record){
                let strGroup = []
                let str =[]
                if(record.id && record.id == this.proOrderId) {
                    strGroup.push('rowBackgroundColor')
                }
                return str.concat(strGroup)
            },
    }
}

</script>
<style lang="less" scoped>
.mintable{
    /deep/.ant-table-body{
        min-height: 640px;
    }
}
/deep/.ant-pagination-prev {
    margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input{
  padding: 0;
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager{
  margin: 0;
}
/deep/.ant-pagination-slash {
    margin: 0;
}
/deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 8px 0 0 10px;
    position: fixed;
  }
.main{
    background: white;
    height: 679px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
        background: #F8F8F8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
background: #dfdcdc;
}
/deep/ .ant-table{
        .ant-table-tbody{
            tr.ant-table-row-hover td {
             background: #dfdcdc;
            }
        }
        .rowBackgroundColor {
                background: #c9c9c9!important;
            }       
        .ant-table-thead > tr > th{
            padding: 4px 4px;
        }
        .ant-table-tbody > tr > td {
            padding: 4px 4px!important;
            max-width: 100px;
            color: #000000;
        }
        tr.ant-table-row-selected td {
        background: #dfdcdc;
        }
        tr.ant-table-row-hover td {
        background: #dfdcdc;
        }
}
</style>