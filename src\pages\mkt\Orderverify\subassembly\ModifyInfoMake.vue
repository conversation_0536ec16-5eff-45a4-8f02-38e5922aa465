<template>
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="V割类型">
      <a-select
        ref="select" 
        v-model="infoForm.vCut"   
        show-search
      >
        <a-select-option value="none">无</a-select-option>
        <a-select-option value="vcut">V割</a-select-option>
        <a-select-option value="luocao">锣槽</a-select-option>
        <a-select-option value="vcutluocao">V割+锣槽</a-select-option>
      </a-select>  
    </a-form-item>
    <a-form-item label="孔径">
      <a-select
        ref="select" 
        v-model="infoForm.vias"  
        show-search
      >
        <a-select-option value="">请选择</a-select-option>
        <a-select-option value="0.2">0.2</a-select-option>
        <a-select-option value="0.25">0.25</a-select-option>
        <a-select-option value="0.3">0.3</a-select-option>
        <a-select-option value="0.35">0.35</a-select-option>
        <a-select-option value="0.4">0.4</a-select-option>
      </a-select>  
    </a-form-item>
    <a-form-item label="协同工厂">
      <a-select 
        show-search
        option-filter-prop="children"
        :filter-option="filterOption"
        v-model='infoForm.joinFactoryId'>
        <a-select-option v-for="(item,index) in factoryList" :key="index" :value="item.valueMember" :lable="item.text" >
          {{item.text}}
        </a-select-option>
      </a-select>  
    </a-form-item>
    <a-form-item label="修改编号">      
      <a-checkbox v-model="infoForm.changeItemNum"/> 
    </a-form-item>
    <a-form-item label="修改周期">       
      <a-checkbox v-model="infoForm.changePeriod"/>
    </a-form-item>
    <a-form-item label="板高" >      
      <a-input v-model="infoForm.boardWidth2" v-if='boardWidth2<100' /> 
      <a-input v-model="infoForm.boardWidth2" v-else disabled /> 
    </a-form-item>
    <a-form-item label="板宽">
      <a-input v-model="infoForm.boardHeight2" v-if='boardHeight2<100' />  
      <a-input v-model="infoForm.boardHeight2" v-else disabled />
    </a-form-item>
  </a-form>
</template>

<script>
import { getFactoryList } from '@/services/projectMake'
export default {
  name:'ModifyInfoMake',   
  props:{
    selectedRowsData:{
      type:Object
    },
    factoryList:{
      type:Array
    }
  },
  created(){ 
    this.$nextTick(function(){
      // console.error('this.selectedRowsData',this.selectedRowsData)
      this.infoForm.vCut = this.selectedRowsData.vCut || 'none';
      this.infoForm.vias = this.selectedRowsData.vias || '';
      this.infoForm.joinFactoryId = this.selectedRowsData.joinFactoryId || '';
      this.infoForm.changeItemNum = this.selectedRowsData.changeItemNum ;
      this.infoForm.changePeriod = this.selectedRowsData.changePeriod ;
      this.infoForm.boardWidth2 = this.selectedRowsData.boardWidth2 || '0';
      this.infoForm.boardHeight2 = this.selectedRowsData.boardHeight2 || '0';
      this.boardWidth2 = this.selectedRowsData.boardWidth2;
      this.boardHeight2 = this.selectedRowsData.boardHeight2;



    })
   
  },
  data() {
    return {
      OrderNumber:'',
      // factoryList:[],
      boardWidth2:'',
      boardHeight2:'',
      infoForm:{
        "vCut": "",
        "vias":'',
        "joinFactoryId": '0',
        "changeItemNum": false,
        "changePeriod": false,
        "boardWidth2": '0',
        "boardHeight2": '0'
      }
    };
  },
  methods: { 
    // selectClick(){      
    //   getFactoryList().then(res =>{
    //    if (res.code == 1) {
    //       this.factoryList = res.data   
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   })      
    // },
    filterOption(input, option) {
      return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
  },
  mounted () {
    
  }
};
</script>