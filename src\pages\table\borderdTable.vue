<template>
  <page-layout>
    <a-card>
      <standard-table
         :bordered="bordered"
         :columns="columns"
         :dataSource="data"
         :pagination="pagination"
      >
        <template slot="name" slot-scope="{ text }">
          <a>{{ text }}</a>
        </template>
      </standard-table>
    </a-card>
  </page-layout>
</template>
<script>
import StandardTable from "@/components/table/StandardTable";
import PageLayout from "@/layouts/PageLayout";
const columns = [
  {
    title: "Name",
    dataIndex: "name",
    scopedSlots: { customRender: "name" }
  },
  {
    title: "Cash Assets",
    className: "column-money",
    dataIndex: "money"
  },
  {
    title: "Address",
    dataIndex: "address"
  }
];

const data = [
  {
    key: "1",
    name: "<PERSON>",
    money: "￥300,000.00",
    address: "New York No. 1 Lake Park"
  },
  {
    key: "2",
    name: "Jim Green",
    money: "￥1,256,000.00",
    address: "London No. 1 Lake Park"
  },
  {
    key: "3",
    name: "<PERSON>",
    money: "￥120,000.00",
    address: "Sidney No. 1 Lake Park"
  }
];

export default {
  components: { PageLayout, StandardTable },
  data() {
    return {
      data,
      columns,
      bordered: true,
      pagination: this.$store.state.setting.pagination
    };
  },
};
</script>
<style>
th.column-money,
td.column-money {
  text-align: right !important;
}
</style>
