<!-- 工程管理 - 工程制作 - 退单 -->
<template>
  <div class='ChargebackMake'>
    <a-form :label-col="{ span:2 }" :wrapper-col="{ span: 18}" >
      <a-form-item label="备注" >
        <a-textarea :rows="4"  v-model="ChargebackForm.remark"/>
      </a-form-item>
      <a-form-item >
        <a-upload
          action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
          list-type="picture-card"
          :file-list="fileList" 
          @preview="handlePreview"
          class='pictureListSty'    
          @change="handleChange"
          :before-upload='beforeUpload'          
          :customRequest="downloadFilesCustomRequest"
        >
          <a-button v-if="fileList.length < 3">
          上传图片
          </a-button>
      </a-upload>
      <!-- <a-modal :visible="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
        <img alt="example" style="width: 100%" :src="previewImage" />
      </a-modal> -->
      </a-form-item>  
      <a-form-item label="">  
          <a-checkbox v-model="ChargebackForm.isMemberConfirm">客户本人要求退单（发短信）</a-checkbox>  
          <a-checkbox v-model="ChargebackForm.isParamError">审核错误需修改参数（不发短信）</a-checkbox>
          <a-checkbox v-model="ChargebackForm.isFileError">审核错误需重新上传文件（发短信）</a-checkbox> 
      </a-form-item>     
    </a-form>
  </div>
</template>


<script>
import {UploadFile,} from  "@/services/projectMake";
export default {
    name:'ChargebackMake',
    props:['selectedRowsData'],
  data() {
    return {      
      fileList:[],
      ChargebackForm:{
        "filePaths":[],
        "isMemberConfirm": false,
        "isParamError": false,
        "isFileError": false,
        "remark": ""
      },
      previewVisible:false,
      previewTitle:'',
    };
  },
  created(){
    // console.log('this.selectedRowsData:',this.selectedRowsData)
  },
  methods: {  
    handlePreview () {
     console.log('1')
    },
    handleChange({ fileList }) {
      this.fileList = fileList;
      // console.log('this.fileList:',this.fileList)
    },
    // 上传图片路径
    downloadFilesCustomRequest(data){      
      const formData = new FormData()
      formData.append('file', data.file)
      UploadFile(formData).then(res =>{
        
        if (res.code == 1) {
          data.onSuccess(res.data);
         this.ChargebackForm.filePaths = this.fileList.map(item => {return item.response})
        }
         else {
          this.$message.error(res.message)
        }
        // console.log( 'this.ChargebackForm.filePaths:',this.ChargebackForm.filePaths)       
      })
      
    },
    // 限制上传格式
    beforeUpload(file){
      const _this = this
      return new Promise(function(resolve, reject) {
        const isJpgOrPng = file.type.toLowerCase() === 'image/jpeg' || file.type.toLowerCase() === 'image/png' || file.type.toLowerCase() === 'image/gif' || file.type.toLowerCase() === 'image/bmp' || file.type.toLowerCase() === 'image/jpg';
        if (!isJpgOrPng) {
          _this.$message.error('图片只支持|*.jpg;*.png;*.gif;*.jpeg;*.bmp 格式');
          reject()
        } else {
          resolve()
        }
      })
    },
  },
 
};
</script>
<style scoped lang="less">
.ChargebackMake{
 /deep/ .ant-form{
    .ant-form-item{
      margin: 0!important;
    }
  }
 .pictureListSty{
   width: 400px !important;
   margin-left: 50px;
    /deep/ .ant-upload-list-item-actions{
    a{
      display: none;
    }
  }
 }
  /deep/ .ant-checkbox-wrapper{
    margin-left: 0!important;
  }
}

</style>