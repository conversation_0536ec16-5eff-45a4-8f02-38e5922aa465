<!-- 车间管理-开料管理 -按钮 -->
<template>
  <div class="active" ref="active">
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingReceiveGoods')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingReceiveGoods') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ReceivingClick" :loading="btnloading1"> 收货 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingStart')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="StartOfMaterialCuttingClick" :loading="btnloading2"> 开料开始 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingSequence')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingSequence') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="DrillingSequenceClick" :loading="btnloading8"> 钻孔过序 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingCallTrolley')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingCallTrolley') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="operationClick1" :loading="btnloading5"> 呼叫小车 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingHoleAffirm')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingHoleAffirm') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="operationClick2" :loading="btnloading6"> 人员确认 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingSearch')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingSearch') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingOrderUpLoad')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingOrderUpLoad') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="UploadOrderClick"> 上传钻孔 </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingDelOrder')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingDelOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="DeleteOrderClick" :loading="btnloading4"> 删除订单 </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingIsUrgent')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingIsUrgent') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="SetUpExpeditingClick" :loading="btnloading3"> 设置加急 </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingRemarks')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingRemarks') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ExceptionRemarksClick"> 备注 </a-button>
    </div>

    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Cutting.CuttingAgvCancel')"
      :class="checkPermission('MES.ProductionModule.Cutting.CuttingAgvCancel') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="operationClick3" :loading="btnloading7"> 取消小车 </a-button>
    </div>
    <!-- <div class="box" v-if="checkPermission('MES.ProductionModule.Cutting.CuttingPassStep')">
         <a-button type="primary" @click='OverOrderClick'>
           部门过序
         </a-button>
       </div>  -->
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "Action",
  props: ["btnloading1", "btnloading2", "btnloading3", "btnloading4", "btnloading5", "btnloading6", "btnloading7", "btnloading8"],
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
    };
  },
  created() {
    // this.$nextTick(()=>{
    //   if (this.$refs.active.children.length > 7) {
    //     let domLength = this.$refs.active.children.length
    //     let sty_ = ''
    //     for (var i=0; i< domLength; i++) {
    //       if (i == this.$refs.active.children.length-1) {
    //         sty_ = "order:11";
    //       } else {
    //         sty_ = "order:"+i*2;
    //       }
    //       this.$refs.active.children[i].style.cssText = sty_
    //     }
    //     this.width = 1500
    //     this.collapsed = true
    //   } else {
    //     this.collapsed = false
    //     this.width = 762
    //   }
    // })
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 收货
    ReceivingClick() {
      this.$emit("ReceivingClick");
    },
    // 部门过序
    OverOrderClick() {
      this.$emit("OverOrderClick");
    },
    // 上传钻孔
    UploadOrderClick() {
      this.$emit("UploadOrderClick");
    },
    // 开料开始
    StartOfMaterialCuttingClick() {
      this.$emit("StartOfMaterialCuttingClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    //设置加急
    SetUpExpeditingClick() {
      this.$emit("SetUpExpeditingClick");
    },
    // 钻孔过序
    DrillingSequenceClick() {
      this.$emit("DrillingSequenceClick");
    },
    // 删除订单
    DeleteOrderClick() {
      this.$emit("DeleteOrderClick");
    },
    //异常备注
    ExceptionRemarksClick() {
      this.$emit("ExceptionRemarksClick");
    },
    // 呼叫小车
    operationClick1() {
      this.$emit("operationClick1");
    },
    // 人员确认
    operationClick2() {
      this.$emit("operationClick2");
    },
    // 取消小车
    operationClick3() {
      this.$emit("operationClick3");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 106px;
    margin: 10px 0;
    text-align: center;
    .ant-btn {
      width: 80px;
    }
    .ant-btn-loading {
      padding-left: 0 !important;
    }
  }
}
</style>
