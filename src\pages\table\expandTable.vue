<template>
<page-layout>
  <a-card>
    <standard-table
      rowKey="key"
      :columns="columns"
      :dataSource="data"
      :pagination="pagination"
    >
        <a slot="action"  href="javascript:;">Delete</a>
        <p slot="expandedRowRender" slot-scope="{ record }" style="margin: 0">
          {{ record.description }}
        </p>
    </standard-table>
  </a-card>
</page-layout>
</template>
<script>
import PageLayout from '@/layouts/PageLayout'
import StandardTable from "@/components/table/StandardTable";
const columns = [
  { title: 'Name', dataIndex: 'name', key: 'name' },
  { title: 'Age', dataIndex: 'age', key: 'age' },
  { title: 'Address', dataIndex: 'address', key: 'address' },
  { title: 'Action', dataIndex: '', key: 'x', scopedSlots: { customRender: 'action' } },
];

const data = [
  {
    key: 1,
    name: '<PERSON>',
    age: 32,
    address: 'New York No. 1 Lake Park',
    description: 'My name is <PERSON>, I am 32 years old, living in New York No. 1 Lake Park.',
  },
  {
    key: 2,
    name: '<PERSON>',
    age: 42,
    address: 'London No. 1 Lake Park',
    description: 'My name is <PERSON>, I am 42 years old, living in London No. 1 Lake Park.',
  },
  {
    key: 3,
    name: 'Joe Black',
    age: 32,
    address: 'Sidney No. 1 Lake Park',
    description: 'My name is Joe Black, I am 32 years old, living in Sidney No. 1 Lake Park.',
  },
];

export default {
    components: { PageLayout, StandardTable },
  data() {
    return {
      data,
      columns,
      pagination: this.$store.state.setting.pagination,
    };
  },
};
</script>
