<template>
  <div class="Review" style="padding: 24px">
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="ReportPdf" style="width: 100%; height: 100%; color: black">
      <img src="@/assets/img/bqdl.png" style="width: 40%; margin-left: 30%" />
      <div style="text-align: center; font-weight: bold; font-size: 24px; padding: 12px 0">非常规合同评审表</div>
      <!--基本信息-->
      <div style="width: 100%; display: flex" class="information">
        <table style="width: 100%" border="1">
          <tr>
            <td rowspan="7">基本信息</td>
            <td>评审类别</td>
            <!--评审策划发起评审 可重新定义评审类别-->
            <td colspan="3">
              <div>{{ reviewInfo.reviewType }}</div>
            </td>
            <td>提出人</td>
            <td colspan="2">
              <div>{{ reviewInfo.createUserName }}</div>
            </td>
            <td colspan="2">部门审核人</td>
            <td>
              <div>{{ reviewInfo.reviewer }}</div>
            </td>
          </tr>
          <tr>
            <td>生产型号</td>
            <td>
              <div>{{ reviewInfo.orderNo }}</div>
            </td>
            <td>客户型号</td>
            <td style="max-width: 200px">
              {{ reviewInfo.customerModel }}
            </td>
            <td>订单号</td>
            <td>
              {{ reviewInfo.businessOrderNo }}
            </td>
            <td>客户代码</td>
            <td>
              {{ reviewInfo.custNo }}
            </td>
            <td>成品板厚(MM)</td>
            <td>
              {{ reviewInfo.boardThickness }}
            </td>
          </tr>
          <tr>
            <td>最小钻咀(MM)</td>
            <td>
              <div>{{ reviewInfo.minJoran }}</div>
            </td>
            <td>内层隔离环</td>
            <td>
              <div>{{ reviewInfo.innerIsolationRing }}</div>
            </td>
            <td>外层环宽</td>
            <td>
              <div>{{ reviewInfo.outerRingWidth }}</div>
            </td>
            <td>芯板厚度</td>
            <td>
              <div>{{ reviewInfo.coreBoardTthickness }}</div>
            </td>
            <td>板材类型</td>
            <td>
              <div>{{ reviewInfo.fR4TypeStr }}</div>
            </td>
          </tr>
          <tr>
            <td>孔到孔距离</td>
            <td>
              <div>{{ reviewInfo.drlToDrl }}</div>
            </td>
            <td>内层线宽线距</td>
            <td>
              <div>{{ reviewInfo.innerMinLineSpace }}</div>
            </td>
            <td>外层线宽线距</td>
            <td>
              <div>{{ reviewInfo.outerMinLineSpace }}</div>
            </td>
            <td>孔铜(UM)</td>
            <td>
              <div>{{ reviewInfo.holeCopper }}</div>
            </td>
            <td>阻焊颜色</td>
            <td>
              <div>{{ reviewInfo.solderColorStr }}/{{ reviewInfo.solderColorBottomStr }}</div>
            </td>
          </tr>
          <tr>
            <td>纵横比</td>
            <td>
              <div>{{ reviewInfo.apertureRatio }}</div>
            </td>
            <td>内层基铜(UM)</td>
            <td>
              <div>{{ reviewInfo.innerBaseCopper }}</div>
            </td>
            <td>内层完成铜厚(UM)</td>
            <td>
              <div>{{ reviewInfo.innerCopperThickness }}</div>
            </td>
            <td>外形到铜</td>
            <td>
              <div>{{ reviewInfo.appearanceToCopper }}</div>
            </td>
            <td>过孔处理</td>
            <td>
              <div>{{ reviewInfo.solderCoverStr }}</div>
            </td>
          </tr>
          <tr>
            <td>盲(埋)孔径</td>
            <td>
              <div>{{ reviewInfo.vias }}</div>
            </td>
            <td>外层基铜(UM)</td>
            <td>
              <div>{{ reviewInfo.outerBaseCopper }}</div>
            </td>
            <td>外层完成铜厚(UM)</td>
            <td>
              <div>{{ reviewInfo.copperThickness }}</div>
            </td>
            <td>表面处理</td>
            <td>
              <div>{{ reviewInfo.surfaceFinishStr }}</div>
            </td>
            <td>订单面积(m²)</td>
            <td>
              <div>{{ reviewInfo.delArea }}</div>
            </td>
          </tr>
          <tr>
            <td>层数</td>
            <td>
              <div>{{ reviewInfo.boardLayers }}</div>
            </td>
            <td>交货尺寸</td>
            <td>
              <div>{{ reviewInfo.deliverySize }}</div>
            </td>
            <td>订单难度等级</td>
            <td>
              <div>{{ reviewInfo.orderDifficultyLevel }}</div>
            </td>
            <td colspan="2">产品应用领域</td>
            <td colspan="2">
              <div>{{ reviewInfo.productUsage }}</div>
            </td>
          </tr>
        </table>
      </div>
      <div style="width: 100%" class="information">
        <table style="width: 100%" border="1">
          <thead>
            <tr>
              <th style="width: 10%; text-align: center">评审次数</th>
              <th style="width: 5%; text-align: center">序号</th>
              <th style="width: 10%; text-align: center">操作时间</th>
              <th style="width: 10%; text-align: center">操作人员</th>
              <th style="width: 50%; text-align: center">评审详情</th>
            </tr>
          </thead>
        </table>
        <div v-for="(val, inde) in copyorderData" :key="(inde + 1).toString()" style="width: 100%">
          <table style="width: 100%" border="1">
            <tbody>
              <template v-for="(item, index) in val">
                <tr :key="'1' + index">
                  <td style="width: 10%; text-align: center" v-if="item.num" :rowspan="item.num">{{ item.reviewNumber }}</td>
                  <td style="width: 5%; text-align: center" :rowspan="item.reply ? 2 : 1">
                    Q<i>{{ qsort(item.id) }}</i>
                  </td>
                  <td style="width: 10%; text-align: center">{{ item.createTime }}</td>
                  <td style="width: 10%; text-align: center">{{ item.userName }}</td>
                  <td style="width: 50%" class="left">
                    <div>
                      <p>问题类型：{{ item.keyType }}</p>
                      <p>问题描述：{{ item.contentS }}</p>
                      <p v-if="!isEmptyOrWhitespace(item.contentA)">建议方案一：{{ item.contentA }}</p>
                      <p v-if="!isEmptyOrWhitespace(item.contentB)">建议方案二：{{ item.contentB }}</p>
                      <p v-if="!isEmptyOrWhitespace(item.contentC)">建议方案三：{{ item.contentC }}</p>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
      <!--其余信息-->
      <div style="width: 100%; display: flex" class="information">
        <table style="width: 100%" border="1">
          <template style="width: 100%">
            <tr>
              <td rowspan="11" style="text-align: center">研发评审</td>
              <td>评审人</td>
              <td colspan="3">
                <div>{{ reviewInfo.reviewUser }}</div>
              </td>
              <td colspan="2">评审类别</td>
              <td colspan="4">
                <div>{{ reviewInfo.reviewTypeSecond }}</div>
              </td>
            </tr>
            <tr>
              <td colspan="2">解决方案（难点工序分开评审）</td>
              <td colspan="4">
                产前会议

                <label> <input type="radio" v-model="reviewInfo.preproductionMeeting" value="true" /> 是 </label>
                <label> <input type="radio" v-model="reviewInfo.preproductionMeeting" value="false" /> 否 </label>
              </td>
              <td colspan="2">预计合格率</td>
              <td colspan="2">
                <div>{{ reviewInfo.expectedPassRate }}</div>
              </td>
            </tr>
            <tr>
              <td rowspan="3">订单处置意见</td>
              <td colspan="2">
                <div class="checkbox1" v-if="reviewInfo.modifyDesign">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>

                修改设计
              </td>
              <td colspan="6">
                <div>{{ reviewInfo.modifyDesignContent }}</div>
              </td>
            </tr>
            <tr>
              <td colspan="2">
                <div class="checkbox1" v-if="reviewInfo.eqOpinion">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                EQ问客意见
              </td>
              <td colspan="6">
                <div>{{ reviewInfo.eqOpinionContent }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td colspan="2">
                <div class="checkbox1" v-if="reviewInfo.cancelOrder">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>

                取消订单
              </td>
              <td colspan="6">
                <div>{{ reviewInfo.cancelOrderContent }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td>资料处理</td>
              <td colspan="8">
                <div>{{ reviewInfo.dataHandle }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td>MI处理</td>
              <td colspan="8">
                <div>{{ reviewInfo.miHandle }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td>MI备注</td>
              <td colspan="8">
                <div>{{ reviewInfo.miRemark }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td>图纸作业</td>
              <td colspan="3"></td>
              <td colspan="3"></td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td rowspan="2" colspan="2">超能力成本分析</td>
              <td style="text-align: left" colspan="8">
                <div>成本影响项:{{ reviewInfo.costImpactItem }}</div>
              </td>
            </tr>
            <tr>
              <td style="text-align: left" colspan="9">
                <span>成本增加值:</span>
                <span>{{ reviewInfo.costIncreaseValue || " " }}</span
                >元/㎡, <span>计算公式:</span>
                <span>{{ reviewInfo.calcFormula }}</span>
              </td>
            </tr></template
          >
          <!--会同评审-->
          <template style="width: 100%">
            <tr>
              <td rowspan="3" style="text-align: center">会同评审</td>
              <td>
                <div class="checkbox1" v-if="reviewInfo.quality">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                品质
              </td>
              <td colspan="8">
                <div>{{ reviewInfo.qualityContent }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td>
                <div class="checkbox1" v-if="reviewInfo.production">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                生产
              </td>
              <td colspan="8">
                <div>{{ reviewInfo.productionContent }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td>
                <div class="checkbox1" v-if="reviewInfo.purchase">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                采购
              </td>
              <td colspan="8">
                <div>{{ reviewInfo.purchaseContent }}</div>
              </td>
              <td></td>
            </tr>
          </template>
          <!--投产信息 只有评审策划时以及后面状态才可以查看-->
          <template style="width: 100%">
            <tr>
              <td rowspan="3" style="text-align: center">投产信息</td>
              <td>生产型号</td>
              <td>
                <div>{{ reviewInfo.orderNo }}</div>
              </td>
              <td>投产数量</td>
              <td>
                <div>{{ reviewInfo.num }}</div>
              </td>
              <td>压合次数</td>
              <td>
                <div>{{ reviewInfo.pressTimes }}</div>
              </td>
              <td>新/返单</td>
              <td>
                <div>{{ reviewInfo.isReOrderStr }}</div>
              </td>
              <td>是否加急</td>
              <td>
                <div>{{ reviewInfo.isJiaji ? "是" : "否" }}</div>
              </td>
            </tr>
            <tr>
              <td>交货日期</td>
              <td>
                <div>{{ reviewInfo.deliveryDate }}</div>
              </td>
              <td>投产面积</td>
              <td>
                <div>{{ reviewInfo.area }}</div>
              </td>
              <td>板材</td>
              <td>
                <div>{{ reviewInfo.boardBrand }}</div>
              </td>
              <td>补料次数</td>
              <td>
                <div>{{ reviewInfo.suppleTimes }}</div>
              </td>
              <td>HDI/HVA</td>
              <td>
                <div>{{ reviewInfo.hdiOrHva ? "是" : "否" }}</div>
              </td>
            </tr>
            <tr>
              <td>备投率</td>
              <td colspan="9">
                <div>{{ reviewInfo.planInvestRate }}</div>
              </td>
            </tr>
          </template>
          <!--产前策划 只有评审策划时以及后面状态才可以查看-->
          <template style="width: 100%">
            <tr>
              <td :rowspan="TableList.length + 2" style="text-align: center">产前策划</td>
              <td :rowspan="TableList.length + 1">实施过程监控点</td>
              <td>生产难点工序</td>
              <td>工序负责人</td>
              <td>难度等级</td>
              <td>难度信息</td>
              <td colspan="2">处置措施</td>
              <td>工艺负责人</td>
              <td>项目负责人</td>
              <td>品质负责人</td>
            </tr>
            <tr v-for="(item, index) in TableList" :key="index">
              <td>
                <div>{{ item.processDifficultPoints }}</div>
              </td>
              <td>
                <div>{{ item.processChargeUser }}</div>
              </td>
              <td>
                <div>{{ item.difficultyLevel }}</div>
              </td>
              <td>
                <div>{{ item.difficultyInfo }}</div>
              </td>
              <td colspan="2">
                <div>{{ item.solutionMethod }}</div>
              </td>
              <td>
                <div>{{ item.techChargeUser }}</div>
              </td>
              <td>
                <div>{{ item.projectChargeUser }}</div>
              </td>
              <td>
                <div>{{ item.qualityChargeUser }}</div>
              </td>
            </tr>
            <tr>
              <td>方案会签</td>
              <td>方案策划人</td>
              <td colspan="4">
                <div>{{ reviewInfo.setPlanUser }}</div>
              </td>
              <td colspan="2">技术中心负责人</td>
              <td colspan="3">
                <div>{{ reviewInfo.techUser }}</div>
              </td>
            </tr>
          </template>
          <!--品质情况 评审总结与审核可查看-->
          <template style="width: 100%">
            <tr>
              <td rowspan="2" style="text-align: center">品质情况</td>
              <td colspan="10">补投记录</td>
            </tr>
            <tr>
              <td colspan="10">无</td>
            </tr>
          </template>
          <!--工艺研发总结 评审总结与审核可查看-->
          <template style="width: 100%">
            <tr>
              <td rowspan="3" style="text-align: center">工艺研发总结</td>
              <td>生产评价</td>
              <td colspan="9">
                <div class="checkbox1" v-if="reviewInfo.planValid">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                方案有效
                <div class="checkbox1" v-if="reviewInfo.reputPlan">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                重出方案
                <div class="checkbox1" v-if="reviewInfo.sameProblem">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                同类问题固化(案例编号)
                <div class="checkbox1" v-if="reviewInfo.adjustPrice">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                调整单价
                <div class="checkbox1" v-if="reviewInfo.cancelProduce">
                  <span class="checkmark">✓</span>
                </div>
                <div class="uncheckbox" v-else></div>
                取消制作
              </td>
            </tr>
            <tr>
              <td>备注</td>
              <td colspan="8">
                <div>{{ reviewInfo.craftOrItRemark }}</div>
              </td>
              <td></td>
            </tr>
            <tr>
              <td colspan="1">工艺/研发工程师</td>
              <td colspan="2">
                <div>{{ reviewInfo.craftOrItEngineer }}</div>
              </td>
              <td colspan="2">工艺/研发部经理审核</td>
              <td colspan="2">
                <div>{{ reviewInfo.craftOrItManager }}</div>
              </td>
            </tr>
          </template>
          <!--备注 评审总结与审核可查看-->
          <template style="width: 100%">
            <tr>
              <td style="text-align: center">备注</td>
              <td colspan="10" style="text-align: left; padding-left: 15px">
                1、在制程难点评审时由研发部确认是否在生产时跟踪。<br />
                2、研发评审必须要详尽，附图。以便于资料处理的完善性。<br />
                3、产前策划需要对所有的难点识别。并责任到人。系统将在型号到达后通知相关人员跟进核实。<br />
                4、产前策划的内容品质部的稽查工程师要负责稽查。<br />
                5、品质数据收集完成后提交工艺总结评价.<br />
                6、一般超制程能力由研发与工程确认解决方案即可，非常规超能力需开产前会议的由各部门会签。
              </td>
            </tr>
          </template>
        </table>
      </div>
    </div>
  </div>
</template>
<script>
import html2pdf from "html2pdf.js";
import { reviewmain, reviewinfo } from "@/services/projectReview";
export default {
  name: "ReviewDetails",
  components: {},
  computed: {},
  props: {
    selecdata: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      printObj1: {
        id: "ReportPdf", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      reviewInfo: {},
      TableList: [],
      orderData: [],
      copyorderData: [],
    };
  },
  created() {},
  mounted() {
    this.getOrderList();
    this.getTable();
    this.printObj1.closeCallback = this.closePrintTool;
  },
  methods: {
    getreportPdf() {
      const element = document.getElementById("ReportPdf");
      // 保存原始padding
      const originalPadding = element.style.padding;
      // 设置临时padding
      element.style.padding = "25px";

      const opt = {
        margin: 0,
        filename: this.selecdata.orderNo + "评审报告" + ".pdf",
        image: { type: "jpeg", quality: 1 },
        html2canvas: { scale: 1 },
        jsPDF: {
          unit: "in",
          format: "a4",
          orientation: "portrait",
        },
        pagebreak: { mode: ["css", "legacy"] },
      };

      html2pdf()
        .set(opt)
        .from(element)
        .save()
        .then(() => {
          // 恢复原始padding
          element.style.padding = originalPadding;
        });
    },
    printpdf() {
      document.title = this.selecdata.orderNo + "评审报告";
    },
    closePrintTool() {
      document.title = "EMS | 评审查询";
    },
    qsort(id) {
      return this.orderData.findIndex(item => item.id == id) + 1;
    },
    // 获取订单评审记录
    getOrderList(type) {
      this.spinning = true;
      let OrderNo = this.selecdata.orderNo;
      let BusinessOrderNo = this.selecdata.businessOrderNo;
      let JoinFactoryId = this.selecdata.joinFactoryId;
      let ReviewNo = this.selecdata.reviewNo;
      reviewinfo(JoinFactoryId, OrderNo, BusinessOrderNo, ReviewNo)
        .then(res => {
          if (res.code) {
            this.reviewInfo = res.data.reviewInfo;
            if (type == 1) {
              this.changeReviewType();
            }
            this.TableList = res.data.reviewControlPoints.length == 0 || !res.data.reviewControlPoints ? [{}] : res.data.reviewControlPoints;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    getTable() {
      this.spinning = true;
      let OrderNo = this.selecdata.orderNo;
      let BusinessOrderNo = this.selecdata.businessOrderNo;
      let JoinFactoryId = this.selecdata.joinFactoryId;
      let ReviewNo = this.selecdata.reviewNo;
      reviewmain(JoinFactoryId, OrderNo, BusinessOrderNo, ReviewNo)
        .then(res => {
          if (res.code) {
            this.orderData = res.data.reviewMains;
            this.imglist = [];
            let pathstring = "";
            res.data.reviewMains.forEach(item => {
              if (item.image) {
                pathstring += item.image + ",";
              }
              if (item.reply && item.reply.image) {
                pathstring += item.reply.image + ",";
              }
              let content = item.content;
              var arr = content.split("|||");
              var arr_ = arr.length > 1 ? arr[1].split(";") : "";
              item.contentS = arr[0];
              item.contentA = arr_[0];
              item.contentB = arr_[1];
              item.contentC = arr_[2];
              let enContent = item.enContent;
              if (enContent) {
                var arr_en = enContent.split("|||");
                var arr_en_ = arr_en.length > 1 ? arr_en[1].split(";") : "";
                item.contentS_en = arr_en[0];
                item.contentA_en = arr_en_[0];
                item.contentB_en = arr_en_[1];
                item.contentC_en = arr_en_[2];
              }
              if (item.image) {
                var a = item.image.split(",");
                item.image = a;
              }
            });
            this.imglist = pathstring.split(",");
            this.imglist.pop();
            this.allowAddTr = true;
            if (this.orderData.filter(item => item.status == 1).length > 0) {
              this.currentreviewNum = this.orderData.filter(item => item.status == 1)[0].reviewNumber;
            } else if (this.orderData.filter(item => item.status == 2).length > 0) {
              const maxNUm = Math.max(...this.orderData.filter(item => item.status == 2).map(item => item.reviewNumber));
              this.currentreviewNum = maxNUm + 1;
              if (this.orderData.filter(item => item.status == 3).length > 0) {
                this.allowAddTr = false;
              }
            }
            this.orderData = this.addNumProperty(this.orderData);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    addNumProperty(arr) {
      if (arr.length == 0) {
        this.copyorderData = [];
      }
      const grouped = arr.reduce((acc, item) => {
        let key = item.reviewNumber;
        if (!key) {
          key = 0;
        }
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        this.copyorderData = Object.values(acc);
        return acc;
      }, {});
      let newArr = [];
      let fornum = 0;
      for (const [_, items] of Object.entries(grouped)) {
        fornum += items.length;
        let replyNum = 0;
        for (let i = 0; i < items.length; i++) {
          if (i == 0) {
            items[i].num = items.length;
          } else {
            items[i].num = 0;
          }
          if (items[i].reply) {
            replyNum++;
          }
          newArr.push(items[i]);
        }
        newArr[newArr.length - items.length].num = newArr[newArr.length - items.length].num + replyNum;
      }
      return newArr;
    },
    isEmptyOrWhitespace(content) {
      if (!content) {
        return true;
      } else {
        return /^\s*$/.test(content);
      }
    },
  },
};
</script>
<style lang="less" scoped>
td {
  padding: 4px;
  text-align: left;
  word-wrap: break-word !important;
  white-space: normal !important;
}
@media print {
  td {
    word-wrap: break-word !important;
    white-space: normal !important;
    font-size: 8px !important;
  }
}
.information {
  font-size: 12px !important;
}
@media print {
  .information {
    font-size: 8px !important;
  }
}
.left {
  text-align: left !important;
}
.Review {
  background: white;
  height: 747px;
  overflow: auto;
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
}
.printstyle {
  position: absolute;
  top: 813px;
  right: 26px;
}
p {
  margin-bottom: 4px;
}
.uncheckbox {
  width: 10px;
  height: 10px;
  background-color: rgb(255, 255, 255);
  display: inline-block;
  position: relative;
  border-radius: 2px; /* 可选，添加轻微的圆角 */
  border: 1px solid;
}
.checkbox1 {
  width: 10px;
  height: 10px;
  background-color: rgb(255, 255, 255);
  display: inline-block;
  border: 7px solid;
  position: relative;
  border-radius: 2px; /* 可选，添加轻微的圆角 */
}

.checkmark {
  color: white;
  font-size: 10px; /* 调整勾选符号的大小 */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
}
</style>
