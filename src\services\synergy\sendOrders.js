import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'




export async function getOrder(id) {
    return request("/api/app/e-mSMake-module-no/x-tDispatch-detail?Pdctno="+id, METHOD.GET)
}

export async function getPeople() {
    return request("/api/app/e-mSMake-module-no/w-fUser-info", METHOD.GET)
}

export async function Customer0rders(id) {
    return request("/api/app/e-mSMake-module-no/user-order?userid="+id, METHOD.GET)
}

export async function assignStaff(params) {
    return request("/api/app/e-mSMake-module-no/x-tSend-order", METHOD.POST,params)
}
export async function urgent(params) {
    return request("/api/app/e-mSMake-module-no/x-tIs-urgent", METHOD.POST,params)
}
export async function rollback(params) {
    return request("/api/app/e-mSMake-module-no/x-tBack-send-order", METHOD.POST,params)
}

export async function getMakes(params) {
    return request(`/api/app/e-mSMake-module-no/x-tMI`, METHOD.GET,params)
}
//回退制作
export async function rollbackMake(params) {
    return request("/api/app/e-mSMake-module-no/update-xTBack-make", METHOD.POST,params)
}
//退单
export async function chargeback(params) {
    return request("/api/app/e-mSMake-module-no/update-xTBack-mkt-order", METHOD.POST,params)
}
//开始
export async function beginMake(params) {
    return request("/api/app/e-mSMake-module-no/update-xTMake-start", METHOD.POST,params)
}
//取单
export async function getAlone(params) {
    return request("/api/app/e-mSMake-module-no/auto-get-order", METHOD.POST,params)
}
//完成
export async function completeMake(params) {
    return request("/api/app/e-mSMake-module-no/update-xTMake-finish", METHOD.POST,params)
}


//获取详情参数列表
export async function getRightMake(Id) {
    return request(`/api/app/e-mSMake-module-no/m-iXTPar-list/${Id}`, METHOD.GET)
}

//获取详情信息
export async function getMakeQ(Id) {
    return request(`/api/app/e-mSMake-module-no/m-iXTPar/${Id}`, METHOD.GET)
}

export async function seleKey(uiguid) {
    return request("/api/app/e-mSFrm-ui-group-config/com-box-items?uiguid="+uiguid, METHOD.POST)
}
export async function amendQ(params) {
    return request("/api/app/e-mSMake-module-no/m-iXTPar", METHOD.POST,params)
}


export async function generateFlow(Id) {
    return request(`/api/app/e-mSMake-module-no/cam-auto-produce-flow/${Id}`, METHOD.POST)
}

export default {
    getOrder,
    getPeople,
    Customer0rders,
    assignStaff,
    urgent,
    rollback,
    getMakes,
    rollbackMake,
    chargeback,
    beginMake,
    getAlone,
    getRightMake,
    completeMake,
    getMakeQ,
    seleKey,
    amendQ,
    generateFlow
}