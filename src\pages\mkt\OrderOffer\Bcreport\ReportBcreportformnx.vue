<!--本川报价单  -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="Bcreport" style="padding: 25px; color: black">
      <div class="formstyle">
        <div style="display: flex; justify-content: space-between">
          <img src="@/assets/img/bclogo.png" style="height: 80px" />
          <div style="text-align: center; font-weight: bold; font-size: 24px">
            <div style="color: #000080">Jiangsu Allfavor Intelligent Circuits Technology CO., Ltd.</div>
            <div style="color: #000080">江苏本川智能电路科技股份有限公司</div>
            <div style="color: #000080">Product Manufacturing Contract</div>
            <div style="color: #000080">产品报价单</div>
          </div>
          <div style="margin-top: 30px; text-align: center">
            <table border="1">
              <tr>
                <td style="min-width: 200px">合同编号:</td>
                <td style="min-width: 200px">{{ Bcreportdata.value_1 }}</td>
              </tr>
              <tr>
                <td style="min-width: 200px">订单日期:</td>
                <td style="min-width: 200px">{{ Bcreportdata.value_2 }}</td>
              </tr>
              <tr>
                <td style="min-width: 200px">签订地点:</td>
                <td style="min-width: 200px">{{ Bcreportdata.value_3 }}</td>
              </tr>
              <tr>
                <td style="min-width: 200px">客户订单号:</td>
                <td style="min-width: 200px">{{ Bcreportdata.value_4 }}</td>
              </tr>
            </table>
          </div>
        </div>
        <div style="display: flex; justify-content: space-between">
          <div>
            <div>
              卖方（承揽方）:
              <span style="min-width: 400px; border-bottom: 1px solid #000; display: inline-block">&emsp;{{ Bcreportdata.value_5 }}</span>
            </div>
            <div>
              地址: &emsp;&emsp;&emsp;&emsp;&emsp;<span style="min-width: 400px; border-bottom: 1px solid #000; display: inline-block"
                >&emsp;{{ Bcreportdata.value_6 }}</span
              >
            </div>
            <div>
              电话: &emsp;&emsp;&emsp;&emsp;&emsp;<span style="min-width: 200px; border-bottom: 1px solid #000; display: inline-block"
                >&emsp;{{ Bcreportdata.value_7 }}</span
              >
              传真:
              <span style="min-width: 200px; border-bottom: 1px solid #000; display: inline-block">&emsp;{{ Bcreportdata.value_8 }}</span>
              联系人:<span style="min-width: 100px; border-bottom: 1px solid #000; display: inline-block">&emsp;{{ Bcreportdata.value_9 }}</span>
            </div>
          </div>
          <div style="min-width: 400px">
            <div>
              买方（定作方）:
              <span style="min-width: 400px; border-bottom: 1px solid #000; display: inline-block">&emsp;{{ Bcreportdata.value_10 }}</span>
            </div>
            <div>
              地址: &emsp;&emsp;&emsp;&emsp;&emsp;<span style="min-width: 400px; border-bottom: 1px solid #000; display: inline-block"
                >&emsp;{{ Bcreportdata.value_11 }}</span
              >
            </div>
            <div>
              电话: &emsp;&emsp;&emsp;&emsp;&emsp;<span style="min-width: 200px; border-bottom: 1px solid #000; display: inline-block"
                >&emsp;{{ Bcreportdata.value_12 }}</span
              >
              传真:
              <span style="min-width: 200px; border-bottom: 1px solid #000; display: inline-block">&emsp;{{ Bcreportdata.value_13 }}</span> 联系人:
              <span style="min-width: 100px; border-bottom: 1px solid #000; display: inline-block">&emsp;{{ Bcreportdata.value_14 }}</span>
            </div>
          </div>
        </div>
        <div style="margin-top: 20px">
          一、产品名称、规格、数量、单价（人民币）及交货期
          <table border="1" style="width: 100%; text-align: center">
            <tr>
              <th>序</th>
              <th>客户型号</th>
              <th>完成板厚(MM)</th>
              <th colspan="2">尺寸(MM)</th>
              <th>拼版</th>
              <th>层数</th>
              <th>数量</th>
              <th>单位</th>
              <th>面积</th>
              <th>含税单价</th>
              <th>工程费</th>
              <th>测试架费</th>
              <th>其他费用</th>
              <th>总金额</th>
              <th>发货日期</th>
              <th>生产型号</th>
            </tr>
            <tr v-for="(item, index) in Bcreportdata.price" :key="index">
              <td>{{ item.price1 }}</td>
              <td>{{ item.price2 }}</td>
              <td>{{ item.price3 }}</td>
              <td>{{ item.price4 }}</td>
              <td>{{ item.price5 }}</td>
              <td>{{ item.price6 }}</td>
              <td>{{ item.price7 }}</td>
              <td>{{ item.price8 }}</td>
              <td>{{ item.price9 }}</td>
              <td>{{ item.price10 }}</td>
              <td>{{ item.price11 }}</td>
              <td>{{ item.price12 }}</td>
              <td>{{ item.price13 }}</td>
              <td>{{ item.price14 }}</td>
              <td>{{ item.price15 }}</td>
              <td>{{ item.price16 }}</td>
              <td>{{ item.price17 }}</td>
            </tr>
            <tr>
              <td colspan="4">费用合计</td>
              <td colspan="4" style="color: #000080; font-weight: bold">￥{{ amountto }}</td>
              <td colspan="4">人民币大写</td>
              <td colspan="5" style="color: red; font-weight: bold">{{ convertToChineseNum(amountto) }}</td>
            </tr>
          </table>
        </div>
        <div style="margin-top: 20px">
          二、产品制作要求
          <table border="1" style="width: 100%; text-align: center">
            <tr>
              <th>序</th>
              <th>外形</th>
              <th>测试</th>
              <th>类型</th>
              <th>板材</th>
              <th>工艺要求</th>
              <th>完成铜厚</th>
              <th>过孔</th>
              <th>阻焊颜色</th>
              <th>字符颜色</th>
              <th>标记</th>
              <th>备注</th>
            </tr>
            <tr v-for="(item, index) in Bcreportdata.orderPar" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.formingType }}</td>
              <td>{{ item.flyingProbe }}</td>
              <td>{{ item.isnewpdctno_ }}</td>
              <td>{{ item.fR4Type }}</td>
              <td>{{ item.surfaceType_ }}</td>
              <td>{{ item.cuThickness }}</td>
              <td>{{ item.minHoleCopper }}</td>
              <td>{{ item.solderColor }}</td>
              <td>{{ item.fontColor }}</td>
              <td>{{ item.proOrderNo }}</td>
              <td>{{ item.remark_ }}</td>
            </tr>
          </table>
        </div>
        <div>
          <a-row>
            <a-col :span="8">
              <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'三、交货地点：'" labelAlign="left">
                {{ Bcreportdata.value_15 }}
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货方式：'" labelAlign="left">
                {{ Bcreportdata.value_20 }}
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'四、运费及运保费由：'" labelAlign="left">
                {{ Bcreportdata.value_16 }}
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'包装方式：'" labelAlign="left">
                {{ Bcreportdata.nO_ }}
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'五、验收标准：'" labelAlign="left">
                {{ Bcreportdata.value_17 }}
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'验收期限：'" labelAlign="left">
                请在<span
                  style="color: red; font-weight: bold; min-width: 30px; border-bottom: 1px solid #000; text-align: center; display: inline-block"
                  >{{ Bcreportdata.nO1_ }}</span
                >天内以书面形式通知卖方，否则视为接受。
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'六、票据形式：'" labelAlign="left">
                {{ Bcreportdata.value_18 }}
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'结算方式：'" labelAlign="left">
                {{ Bcreportdata.nO2_ }}
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'账期：'" labelAlign="left">
                {{ Bcreportdata.value_19 }}
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <div style="line-height: 3ch">
          <div>七、交货时间的天数以卖方收到合同回签后开始计算；</div>
          <div>
            八、在卖方投料后买方提出更改设计资料或停止加工，应承担卖方因此受到的材料损失。在不造成报废的前提下，卖方应尽量满足买方的更改要求，并相应顺延货期；
          </div>
          <div>九、保密要求：卖方对买方提供之资料负有保密义务，长期合作关系的客户可按照双方签订的保密协议执行；</div>
          <div>十、遇不可抗拒力因素造成卖方未能按期交货，卖方应及时通知买方，否则责任由卖方承担；</div>
          <div>十一、因卖方产品质量原因造成了买方的产品报废，按照行业惯例，卖方的赔偿以不超过电路板自身价值为最高额度；</div>
          <div>十二、在履行合同过程中，若发生争议，由双方友好协商解决，协商未果，均应向卖方所在地（南京市溧水区）有管辖权的人民法院起诉；</div>
          <div>十三、双方业务中所发传真、邮件等同原件具有同等法律效力，其它相关协议、手册等文件、资料和本合同有冲突的，以本合同为准；</div>
          <div>
            十四、特殊说明：其它费用为：<span
              style="font-weight: bold; min-width: 80px; border-bottom: 1px solid #000; text-align: center; display: inline-block"
              >&emsp;{{ Bcreportdata.nO3_ }}</span
            >，到货日期不包含货运时间。
          </div>
          <div>
            十五、备注：<span style="font-weight: bold; min-width: 600px; border-bottom: 1px solid #000; display: inline-block"
              >&emsp;{{ Bcreportdata.nO3_ }}</span
            >
          </div>
          <div style="padding-top: 20px; display: flex; justify-content: space-between">
            <div>
              卖方：&emsp;&emsp;<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">{{
                Bcreportdata.value_5
              }}</span>
            </div>
            <div>
              买方：&emsp;&emsp;<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">&emsp; </span
              >（盖章）
            </div>
          </div>
          <div style="padding-top: 10px; display: flex; justify-content: space-between">
            <div>代表签字：<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">&emsp;</span></div>
            <div>
              代表签字：<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">&emsp; </span
              >&emsp;&emsp;&emsp;&emsp;
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //竖版a4
import convertToChineseNum from "@/utils/convertToChineseNum";
export default {
  name: "",
  props: ["Bcreportdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj1: {
        id: "Bcreport", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.Bcreportdata.price.length; index++) {
      if (this.Bcreportdata.price[index].price15 && this.Bcreportdata.price[index].price15 != "/") {
        this.amountto += Number(this.Bcreportdata.price[index].price15);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.Bcreportdata.pcbFileName;
    },
    getreportPdf() {
      htmlToPdfa3("Bcreport", this.Bcreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
@media print {
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
}
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.tableclass > table > tr > td {
  text-align: center;
  min-width: 200px;
}
.formstyle {
  /deep/.ant-form-item-children {
    color: black;
  }
  /deep/.ant-form-item-label {
    height: 25px;
    line-height: 25px;
  }
  /deep/.ant-form-item-control {
    height: 25px;
    line-height: 25px;
  }
}
.Bcreport {
  .tableclass > table > tr > td {
    text-align: center;
    min-width: 200px;
  }
  table > tr > td {
    padding: 0px 5px;
    word-break: break-word;
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
