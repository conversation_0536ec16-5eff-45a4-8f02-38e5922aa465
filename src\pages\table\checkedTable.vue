<template>
  <page-layout>
    <a-card>
      <standard-table
        rowKey="key"
        :columns="columns"
        :dataSource="data"
        :pagination="pagination"
        :selectedRows.sync="selectedRows"
        @selectedRowChange="onSelectChange"
        :disabledName="disabledName"
      >
        <a slot="name" slot-scope="{ text }">{{ text }}</a>
      </standard-table>
    </a-card>
  </page-layout>
</template>
<script>
import StandardTable from "@/components/table/StandardTable";
import PageLayout from "@/layouts/PageLayout";
const columns = [
  {
    title: "Name",
    dataIndex: "name",
    scopedSlots: { customRender: "name" }
  },
  {
    title: "Age",
    dataIndex: "age"
  },
  {
    title: "Address",
    dataIndex: "address"
  }
];
const data = [
  {
    key: "1",
    name: "<PERSON>",
    age: 32,
    address: "New York No. 1 Lake Park"
  },
  {
    key: "2",
    name: "<PERSON>",
    age: 42,
    address: "London No. 1 Lake Park"
  },
  {
    key: "3",
    name: "<PERSON>",
    age: 32,
    address: "Sidney No. 1 Lake Park"
  },
  {
    key: "4",
    name: "Disabled User",
    age: 99,
    address: "Sidney No. 1 Lake Park"
  }
];

export default {
  components: { PageLayout, StandardTable },
  data() {
    return {
      data,
      columns,
      pagination: this.$store.state.setting.pagination,
      selectedRows: [],
      disabledName: 'Disabled User'
    };
  },
  methods: {
    onSelectChange(selectedRowKeys, selectedRows) {
      console.log(selectedRowKeys, selectedRows)
    }
  }
};
</script>
