<!-- 工程管理 - ECN管理 -->
<template>
  <a-spin :spinning="spinning">
    <div class="process">
      <div class="content" style="width: 100%; display: flex">
        <div class="content-left" style="width: 20%; max-height: 775px; user-select: none" ref="tableWrapper">
          <a-table
            :columns="ficolumns"
            :pagination="false"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :dataSource="fidataSource"
            :customRow="fionClickRow"
            :scroll="{ y: 710 }"
            :class="fidataSource.length ? 'fimintable' : ''"
            :rowClassName="isRedRow"
            :loading="tableload"
          >
          </a-table>
        </div>
        <div class="content-center" style="position: relative; width: 45%; max-height: 775px; user-select: none" ref="tableWrapper1">
          <div style="min-height: 771px">
            <a-table
              :columns="columns1"
              :expandIcon="expandIcon1"
              :pagination="false"
              rowKey="guid4ecn"
              :dataSource="dataSource1"
              :scroll="{ y: 710 }"
              :class="dataSource1.length ? 'mintable1' : ''"
              childrenColumnName="childrenEcn"
              :expandIconColumnIndex="1"
              :rowClassName="isRedRow1"
              :loading="tableload1"
              :customRow="data1ClickRow"
            >
              <span slot="num1" slot-scope="text, record">
                {{ record.level1 }}
              </span>
            </a-table>
          </div>
          <div class="footerAction" style="user-select: none">
            <ecn-action
              @toexamine="toexamine"
              @queryClick="queryClick"
              @newlyadded="newlyadded"
              @compile="compile"
              @preserve="preserve"
              :editflag="editflag"
              :ttype="ttype"
              @cancelcompile="cancelcompile"
              @onlineecn="onlineecn"
              @omission="omission"
              @deapproval="deapproval"
            />
          </div>
        </div>
        <div class="content-right" style="width: 35%; max-height: 775px">
          <div class="righttop">
            <div>
              <div style="height: 246px; border-bottom: 2px solid #e9e9f0; overflow-y: auto; overflow-x: hidden" ref="SelectBox">
                <div style="border-top: 2px solid #e9e9f0">
                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="变更前" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
                        <a-textarea :auto-size="{ minRows: 2, maxRows: 2 }" :disabled="editflag" v-model="rightdata.befCHG_"></a-textarea>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="变更后" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
                        <a-textarea :auto-size="{ minRows: 2, maxRows: 2 }" :disabled="editflag" v-model="rightdata.remark_"></a-textarea>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row class="inputstyle">
                    <a-col :span="24">
                      <a-form-item label="执行时机" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
                        <a-select
                          v-model="rightdata.newBoardDoType_"
                          showSearch
                          allowClear
                          style="width: 100%"
                          optionFilterProp="lable"
                          :disabled="editflag"
                        >
                          <a-select-option
                            :title="item.lable"
                            v-for="(item, index) in executiontiming"
                            :key="index"
                            :value="item.valueMember"
                            :lable="item.text"
                          >
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row class="inputstyle">
                    <a-col :span="12">
                      <a-form-item label="旧版本处理方法" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                        <a-select
                          v-model="rightdata.oldBoardDoType_"
                          showSearch
                          style="width: 100%"
                          allowClear
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                          @change="setEstimate($event, 'oldBoardDoType_')"
                          @search="handleSearch($event, 'oldBoardDoType_')"
                          @blur="handleBlur($event, 'oldBoardDoType_')"
                          :disabled="editflag"
                        >
                          <a-select-option
                            :title="item.lable"
                            v-for="(item, index) in oldVersion"
                            :key="index"
                            :value="item.valueMember"
                            :lable="item.text"
                          >
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="在线板处理方式" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                        <a-select
                          v-model="rightdata.onlineBoardDoType_"
                          showSearch
                          allowClear
                          style="width: 100%"
                          optionFilterProp="lable"
                          :getPopupContainer="() => this.$refs.SelectBox"
                          @change="setEstimate($event, 'onlineBoardDoType_')"
                          @search="handleSearch($event, 'onlineBoardDoType_')"
                          @blur="handleBlur($event, 'onlineBoardDoType_')"
                          :disabled="editflag"
                        >
                          <a-select-option
                            :title="item.lable"
                            v-for="(item, index) in onlineBoard"
                            :key="index"
                            :value="item.valueMember"
                            :lable="item.text"
                          >
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row class="inputstyle">
                    <a-col :span="12">
                      <a-form-item label="库存板处理方式" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                        <a-input v-model="rightdata.inventoryBoardDoType_" :disabled="editflag"></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="工具处理方式" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                        <a-select
                          v-model="rightdata.toolDoType_"
                          showSearch
                          allowClear
                          style="width: 100%"
                          optionFilterProp="lable"
                          :disabled="editflag"
                        >
                          <a-select-option
                            :title="item.lable"
                            v-for="(item, index) in ToolDoType_"
                            :key="index"
                            :value="item.valueMember"
                            :lable="item.text"
                          >
                            {{ item.text }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row class="inputstyle">
                    <a-col :span="12">
                      <a-form-item label="旧版本库存" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                        <a-input v-model="rightdata.qtyNum" disabled></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="在制数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                        <a-input v-model="rightdata.onLineNum" disabled></a-input>
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </div>
              <div class="rightcenter">
                <a-table
                  :columns="columns3"
                  :pagination="false"
                  rowKey="toolID_"
                  :dataSource="dataSource3"
                  :scroll="{ y: 141 }"
                  :class="dataSource3.length ? 'mintable3' : ''"
                >
                  <span slot="selected_" slot-scope="text, record">
                    <span><a-checkbox v-model="record.selected_" :disabled="editflag" /> </span>
                  </span>
                </a-table>
              </div>
              <div class="rightbottom">
                <div class="rightbottom1">
                  <a-table
                    :columns="columns4"
                    :pagination="false"
                    rowKey="drawID"
                    :dataSource="dataSource4"
                    :scroll="{ y: 142 }"
                    :class="dataSource4.length ? 'mintable4' : ''"
                  >
                    <span slot="selected_" slot-scope="text, record">
                      <span><a-checkbox v-model="record.selected_" :disabled="editflag" /> </span>
                      <!-- <span>
                <span v-if="editflag"> <a-checkbox :checked=" record.selected_ "/>  </span>
                <span v-else> <a-checkbox v-model=" record.selected_ " />  </span>
              </span> -->
                    </span>
                  </a-table>
                </div>
              </div>
            </div>
            <div class="rightsec">
              <a-table
                :columns="columns2"
                :pagination="false"
                rowKey="pTechNo_"
                :dataSource="dataSource2"
                :scroll="{ y: 112 }"
                :class="dataSource2.length ? 'mintable2' : ''"
              >
                <span slot="selected_" slot-scope="text, record">
                  <span><a-checkbox v-model="record.selected_" :disabled="editflag" /> </span>
                </span>
              </a-table>
            </div>
          </div>
        </div>
        <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="down1" v-if="showText">复制</a-menu-item>
        </a-menu>
      </div>
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel1"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form>
          <a-row>
            <a-col :span="24">
              <a-form-item label="生产型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
                <a-input autoFocus v-model="formdata.OrderNo" allowClear> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <a-modal
        :title="treat == '1' && fac == 22 ? '确认流程' : '确认弹窗'"
        :visible="dataVisible1"
        :confirmLoading="moload"
        @cancel="reportHandleCancel1"
        @ok="handleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div v-if="treat == '1' && fac == 22">
          <a-radio-group v-model="OnLineOrRecordEcn" style="display: flex; justify-content: space-around">
            <a-radio :value="1">在线订单</a-radio>
            <a-radio :value="2">建档信息</a-radio>
          </a-radio-group>
        </div>
        <div v-else>{{ oderno1 }}{{ messagelist }}</div>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import {
  ecnorderno,
  ecnorderdetail,
  ecnorderadd,
  ecninfo,
  ecnordersave,
  classlistecn,
  ecnorderdelete,
  ecnorderverify,
  ecnorderbackverify,
  upversion,
} from "@/services/projectECN";
import EcnAction from "@/pages/gongcheng/projectECN/subassembly/EcnAction";
const ficolumns = [
  {
    title: "序号",
    align: "center",
    width: 50,
    dataIndex: "key",
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "生产型号",
    ellipsis: true,
    align: "left",
    dataIndex: "orderNo",
  },
  {
    title: "工厂",
    ellipsis: true,
    align: "left",
    dataIndex: "factoryStr",
    width: 100,
  },
];
const columns1 = [
  {
    title: "序号",
    align: "center",
    scopedSlots: { customRender: "num1" },
    width: 45,
  },
  {
    title: "生产型号",
    align: "left",
    width: 170,
    dataIndex: "orderNo4aft",
  },
  {
    title: "原始型号",
    align: "left",
    dataIndex: "orderNo4bef",
    width: 160,
  },
  {
    title: "ECN创建人",
    align: "left",
    dataIndex: "realName",
    width: 80,
  },
  {
    title: "ECN创建时间",
    align: "left",
    dataIndex: "inDate",
    width: 130,
  },
  {
    title: "状态",
    align: "left",
    dataIndex: "ecnState",
    width: 80,
  },
];
const columns2 = [
  {
    title: "序号",
    align: "center",
    width: 50,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "受影响工序",
    ellipsis: true,
    align: "left",
    dataIndex: "techName_",
    width: 250,
  },
  {
    title: "ECN",
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "selected_" },
  },
];
const columns3 = [
  {
    title: "序号",
    align: "center",
    width: 50,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "受影响工具",
    ellipsis: true,
    align: "left",
    dataIndex: "listName_",
    width: 250,
  },
  {
    title: "ECN",
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "selected_" },
  },
];
const columns4 = [
  {
    title: "序号",
    align: "center",
    width: 50,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "受影响图纸",
    ellipsis: true,
    align: "left",
    width: 120,
    dataIndex: "listName",
  },
  {
    title: "发送时间",
    ellipsis: true,
    align: "left",
    dataIndex: "sendDate",
    width: 160,
  },
  {
    title: "ECN",
    ellipsis: true,
    align: "left",
    //dataIndex:'ECN',
    scopedSlots: { customRender: "selected_" },
  },
];

export default {
  components: { EcnAction },
  data() {
    return {
      fac: "",
      OnLineOrRecordEcn: 0,
      executiontiming: [],
      spinning: false,
      oldVersion: [],
      ToolDoType_: [],
      onlineBoard: [],
      tableload: false,
      tableload1: false,
      order: "",
      oderno1: "",
      ficolumns,
      columns1,
      columns2,
      columns3,
      columns4,
      oderno: "",
      messagelist: "",
      treat: "",
      moload: false,
      guid4ecn: "",
      editflag: true,
      ttype: " ",
      dataVisible: false,
      dataVisible1: false,
      formdata: {
        OrderNo: "",
      },
      rightdata: [],
      fidataSource: [],
      dataSource1: [],
      dataSource2: [],
      dataSource3: [],
      dataSource4: [],
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      showText: false,
      isCtrlPressed: false,
    };
  },
  created() {
    this.getleftdata();
    this.classlistecn();
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  methods: {
    setEstimate(value, val) {
      this.rightdata[val] = value;
    },
    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.reportHandleCancel1();
        this.queryClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible1) {
        this.handleOk1();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    rightClick11(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.localName == "path") {
        event = e.target.parentNode.parentNode;
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split(" ")[0];
      }
      const tableWrapper1 = this.$refs.tableWrapper1;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper1.getBoundingClientRect();
      this.menuVisible = true;
      let offsetx = event.offsetLeft + event.offsetWidth - 10;
      let offsety = event.offsetTop + 40;
      if (event.cellIndex == this.columns1.length - 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 15 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 310 + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 15 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth + 240 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle.top = offsety + 15 + "px";
        this.menuStyle.left = offsetx + 240 + "px";
      }

      document.body.addEventListener("click", this.bodyClick);
    },
    rightClick1(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.localName == "path") {
        event = e.target.parentNode.parentNode;
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split(" ")[0];
      }
      const tableWrapper = this.$refs.tableWrapper;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;
      let offsetx = event.offsetLeft + event.offsetWidth - 10;
      let offsety = event.offsetTop + 40;
      if (event.cellIndex == this.ficolumns.length - 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle.top = offsety + "px";
        this.menuStyle.left = offsetx + "px";
      }

      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    down1() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    expandIcon1(props) {
      if (props.record.childrenEcn && props.record.childrenEcn.length > 0 && props.record.childrenEcn != "null") {
        if (props.expanded) {
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder-open" style="margin-right:5px" />
            </a>
          );
        } else {
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder" style="margin-right:5px" />
            </a>
          );
        }
      } else {
        return (
          <span style="margin-right:0px">
            <a-icon type=" " />
          </span>
        );
      }
    },
    addPropertyToTree(tree, prop, parentLevel) {
      tree.forEach((node, index) => {
        node[prop] = parentLevel ? `${parentLevel}-${index + 1}` : `${index + 1}`;
        if (node.childrenEcn) {
          this.addPropertyToTree(node.childrenEcn, prop, node[prop]);
        }
      });
    },
    handleOk() {
      this.dataVisible = false;
      let params = this.formdata.OrderNo;
      var arr1 = params.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      params = arr1.join("");
      this.getleftdata(params);
    },
    reportHandleCancel1() {
      this.dataVisible = false;
      this.dataVisible1 = false;
    },
    compile() {
      if (!this.guid4ecn) {
        this.$message.warning("请选择需要编辑的订单");
        return;
      }
      this.editflag = false;
    },
    classlistecn() {
      classlistecn().then(res => {
        if (res.code) {
          this.executiontiming = res.data.filter(item => {
            return item.iType == 761;
          });
          this.oldVersion = res.data.filter(item => {
            return item.iType == 762;
          });
          this.onlineBoard = res.data.filter(item => {
            return item.iType == 763;
          });
          this.ToolDoType_ = res.data.filter(item => {
            return item.iType == 764;
          });
        }
      });
    },
    toexamine() {
      if (!this.editflag) {
        this.$message.warning("编辑状态中不允许进行其他操作");
        return;
      }
      if (!this.guid4ecn) {
        this.$message.warning("请选择订单");
        return;
      }
      if (!this.order) {
        this.$message.warning("该操作不能选择子集订单");
        return;
      }
      this.spinning = true;
      ecnorderverify(this.guid4ecn).then(res => {
        if (res.code) {
          let params = {
            id: res.data.split(",")[0],
            orderNo: res.data.split(",")[1],
            orderNo2: res.data.split(",")[2],
            isUp: true,
          };
          upversion(params)
            .then(ite => {
              if (ite.code) {
                this.$router.push({ path: "/gongcheng/technologicalProcess", query: { pdctno: this.order, factoryId: this.fac } });
              } else {
                this.$message.error(ite.message);
              }
            })
            .finally(() => {
              this.spinning = false;
            });
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      });
    },
    preserve() {
      if (this.editflag == true) {
        this.$message.warning("订单未编辑不允许保存");
        return;
      }
      const data1 = this.rightdata;
      ecnordersave(data1).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
        } else {
          this.$message.warning(res.message);
        }
      });
      this.editflag = true;
    },
    cancelcompile() {
      this.editflag = true;
      this.getrightdata(this.guid4ecn);
    },
    queryClick() {
      if (!this.editflag) {
        this.$message.warning("编辑状态中不允许进行其他操作");
        return;
      }
      this.formdata.OrderNo = "";
      this.dataVisible = true;
    },
    newlyadded() {
      if (!this.editflag) {
        this.$message.warning("编辑状态中不允许进行其他操作");
        return;
      }
      if (!this.oderno) {
        this.$message.warning("请选择订单");
        return;
      }
      this.OnLineOrRecordEcn = 0;
      this.dataVisible1 = true;
      this.messagelist = "确认新增吗？";
      this.treat = "1";
      this.oderno1 = this.oderno;
    },
    onlineecn() {
      if (!this.oderno) {
        this.$message.warning("请选择订单");
        return;
      }
      this.dataVisible1 = true;
      this.messagelist = "确认在线ECN吗?";
      this.treat = "2";
      this.oderno1 = this.oderno;
    },
    omission() {
      if (!this.editflag) {
        this.$message.warning("编辑状态中不允许进行其他操作");
        return;
      }
      if (!this.guid4ecn) {
        this.$message.warning("请选择订单");
        return;
      }
      if (!this.order) {
        this.$message.warning("该操作不能选择子集订单");
        return;
      }
      this.dataVisible1 = true;
      this.messagelist = "确认删除吗?";
      this.treat = "3";
      this.oderno1 = this.order;
    },
    deapproval() {
      if (!this.editflag) {
        this.$message.warning("编辑状态中不允许进行其他操作");
        return;
      }
      if (!this.guid4ecn) {
        this.$message.warning("请选择订单");
        return;
      }
      if (!this.order) {
        this.$message.warning("该操作不能选择子集订单");
        return;
      }
      this.dataVisible1 = true;
      this.messagelist = "确认反审核吗?";
      this.treat = "4";
      this.oderno1 = this.order;
    },
    handleOk1() {
      if (this.OnLineOrRecordEcn == 0 && this.fac == 22 && this.treat == "1") {
        this.$message.error("请选择ECN流程");
        this.fdloading = false;
        return;
      }
      this.dataVisible1 = false;
      this.spinning = true;
      this.moload = true;
      if (this.treat == "1") {
        ecnorderadd(this.oderno, this.OnLineOrRecordEcn, this.fac)
          .then(res => {
            if (res.code) {
              this.$message.success("新增成功");
              this.getcenter(this.oderno, this.fac);
            } else {
              this.$message.warning(res.message);
            }
          })
          .finally(() => {
            this.moload = false;
            this.spinning = false;
          });
      }
      if (this.treat == "3") {
        ecnorderdelete(this.guid4ecn)
          .then(res => {
            if (res.code) {
              this.$message.success("删除成功");
              this.getcenter(this.oderno, this.fac);
              this.guid4ecn = "";
              this.dataSource2 = [];
              this.dataSource3 = [];
              this.dataSource4 = [];
              this.rightdata = [];
            } else {
              this.$message.warning(res.message);
            }
          })
          .finally(() => {
            this.moload = false;
            this.spinning = false;
          });
      }
      if (this.treat == "4") {
        ecnorderbackverify(this.guid4ecn)
          .then(res => {
            if (res.code) {
              this.$message.success("反审核成功");
              this.getcenter(this.oderno, this.fac);
            } else {
              this.$message.warning(res.message);
            }
          })
          .finally(() => {
            this.moload = false;
            this.spinning = false;
          });
      }
    },
    data1ClickRow(record) {
      return {
        on: {
          click: () => {
            this.guid4ecn = record.guid4ecn;
            this.order = record.orderNo;
            this.getrightdata(this.guid4ecn);
          },
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.rightClick11(e, text, record);
          },
        },
      };
    },
    fionClickRow(record) {
      return {
        on: {
          click: () => {
            this.oderno = record.orderNo;
            this.fac = Number(record.factory);
            this.getcenter(record.orderNo, record.factory);
            this.guid4ecn = "";
            this.dataSource2 = [];
            this.dataSource3 = [];
            this.dataSource4 = [];
            this.rightdata = [];
          },
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.rightClick1(e, text, record);
          },
        },
      };
    },
    isRedRow(record) {
      let str = [];
      if (record.orderNo && record.orderNo == this.oderno && record.factory == this.fac) {
        str.push("rowBackgroundColor");
      }
      return str;
    },
    isRedRow1(record) {
      let str1 = [];
      if (record.guid4ecn && record.guid4ecn == this.guid4ecn) {
        str1.push("rowBackgroundColor");
      }
      return str1;
    },
    getleftdata(params) {
      let obj = { OrderNo: params };
      this.tableload = true;
      ecnorderno(obj)
        .then(res => {
          this.fidataSource = res;
        })
        .finally(() => {
          this.tableload = false;
        });
    },
    getcenter(orderNo, JoinFactoryId) {
      let obj1 = { OrderNo: orderNo };
      this.tableload1 = true;
      ecnorderdetail(JoinFactoryId, obj1)
        .then(res => {
          this.dataSource1 = res;
          this.addPropertyToTree(this.dataSource1, "level1");
        })
        .finally(() => {
          this.tableload1 = false;
        });
    },
    getrightdata(guid4ecn) {
      ecninfo(guid4ecn).then(res => {
        this.rightdata = res;
        if (this.rightdata.lsEcnTechFlowDto == null) {
          this.dataSource2 = [];
        } else {
          this.dataSource2 = this.rightdata.lsEcnTechFlowDto; //受影响工序
        }
        if (this.rightdata.lsEcnTechToolDto == null) {
          this.dataSource3 = [];
        } else {
          this.dataSource3 = this.rightdata.lsEcnTechToolDto; //受影响工具
        }
        if (this.rightdata.lsEcnTechDrawDto == null) {
          this.dataSource4 = [];
        } else {
          this.dataSource4 = this.rightdata.lsEcnTechDrawDto; //受影响图纸
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
.inputstyle {
  /deep/.ant-form-item-label {
    height: 28px;
    line-height: 28px;
  }
  /deep/.ant-input {
    height: 28px;
    line-height: 28px;
  }
  /deep/.ant-select-selection {
    height: 28px;
    line-height: 28px;
  }
  /deep/.ant-form-item-control {
    height: 28px;
    line-height: 28px;
  }
}
/deep/.ant-select-selection-selected-value {
  line-height: 24px;
}
.ant-form-item {
  margin: 0;
}
// .ant-input-disabled {
//   background-color: white !important;
// }
.tabRightClikBox {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    background-color: white !important;
    color: #000000;
  }
}
.rightbottom1 {
  border-top: 2px solid rgb(233, 233, 240);
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.footerAction {
  margin-left: -344px;
  margin-top: 2px;
  width: 224%;
  height: 48px;
  border: 3px solid #e9e9f0;
  -webkit-box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background: #ffffff;
  border-top: 0;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
.sort-move {
  transition: transform 0.3s;
}
.sort-item {
  margin-bottom: 5px;
  border: 1px solid;
}
.process {
  min-width: 1670px;
  background: #ffffff;
  /deep/.rowBackgroundColor {
    background: #dfdcdc !important;
  }
  /deep/.ant-table-body {
    .ant-table-tbody {
      tr {
        td {
          padding: 6.8px 7px;
        }
      }
    }
  }
  .content {
    border: 1px solid #d8d8d8;
    display: flex;
    /deep/ .ant-card-head {
      display: none;
      padding: 0;
      font-weight: 500;
      color: #333333;
      text-align: center;
      font-size: 15px;
      margin: 0;
      border: 0;
      min-height: 30px;
      .ant-card-head-title {
        padding: 10px 0;
      }
    }
    /deep/ .ant-card-body {
      padding: 0;
    }
    /deep/ .content-left {
      position: relative;
      .fimintable {
        .ant-table-body {
          min-height: 733px;
        }
      }
      .ant-table {
        border-top: 2px solid rgb(233, 233, 240);
        .ant-table-row-selected {
          td {
            background: #dcdcdc;
          }
        }
      }
      .ant-card {
        .ant-card-body {
          border-top: 2px solid rgb(233, 233, 240);
        }
        position: unset;
      }
      .ant-spin-nested-loading {
        position: unset;
      }
      .ant-spin-container {
        position: unset;
        .ant-pagination {
          margin: 0;
        }
      }
      .ant-pagination {
        position: absolute;
        bottom: 6px;
        left: 153px;
      }
      width: 24%;
      border: 2px solid rgb(233, 233, 240);
    }
    /deep/ .content-center {
      .mintable1 {
        .ant-table-body {
          min-height: 733px;
        }
      }
      .ant-card {
        .ant-card-body {
          border-top: 2px solid rgb(233, 233, 240);
        }
        position: unset;
      }
      width: 20%;
      border: 2px solid rgb(233, 233, 240);
      border-top: 4px solid rgb(233, 233, 240);
    }
    /deep/ .content-right {
      width: 56%;
      border: 2px solid rgb(233, 233, 240);
      .rightsec {
        .ant-table-placeholder {
          height: 130px;
        }
        .mintable2 {
          .ant-table-body {
            min-height: 112px;
          }
        }
      }
      .rightcenter {
        .ant-table-placeholder {
          height: 142px;
        }
        .mintable3 {
          .ant-table-body {
            min-height: 141px;
          }
        }
      }
      .rightbottom {
        border-bottom: 2px solid rgb(233, 233, 240);
        .ant-table-placeholder {
          height: 143px;
        }
        .mintable4 {
          .ant-table-body {
            min-height: 142px;
          }
        }
      }
      .ant-card {
        .ant-card-body {
          height: 779px;
          display: flex;
          .righttop {
            .min-table {
              .ant-table-body {
                min-height: 411px;
              }
            }

            .ant-table-wrapper {
              height: 200px;
              border-bottom: 2px solid rgb(233, 233, 240);
            }
            position: relative;
            border-top: 2px solid rgb(233, 233, 240);
            border-right: 4px solid rgb(233, 233, 240);
            border-bottom: 2px solid rgb(233, 233, 240);
          }
          .right {
            width: 65%;
            border-top: 2px solid rgb(233, 233, 240);
            border-bottom: 2px solid rgb(233, 233, 240);
          }
        }
      }
    }
  }
  /deep/ .ant-table {
    tr th {
      padding: 7px 7px;
    }
  }
}
.righttop {
  .ant-form-item {
    margin: 2px;
  }
  /deep/.ant-input {
    // height:21px;
    font-weight: 500;
  }
}
/deep/ .ant-modal {
  top: 40px;
  .Ontable {
    width: 48%;
    overflow: hidden;
    margin-top: 20px;
    border: 2px solid rgb(233, 233, 240);
  }

  .Ontable table {
    width: 100%;
  }

  .Ontable table tr {
    height: 30px;
  }
  .Ontable .person-list {
    height: 654px;
    overflow-y: scroll;
  }

  .Ontable .table1 {
    background: #e8e8e8;
    text-align: center;
    height: 34px;
    line-height: 34px;
    color: #6c747f;
  }

  .Ontable table {
    text-align: center;
  }
}
/deep/ .ant-table {
  .ant-table-thead > tr > th {
    border-right: 1px solid #efefef;
  }

  .ant-table-tbody > tr > td {
    border-right: 1px solid #efefef;
  }

  tr.ant-table-row-selected td {
    background: #dfdcdc;
  }

  tr.ant-table-row-hover td {
    background: #dfdcdc;
  }

  .rowBackgroundColor {
    background: #dfdcdc !important;
  }

  .ant-table-selection-col {
    width: 20px !important;
  }
}
</style>
