<!-- 车间管理-磨板管理 -->
<template>
 <div class="CuttingManagement" >
   <a-spin :spinning="spinning">
   <div class="content">
     <div class="left" ref="letfDom" style="width:80%;">
       <a-card :bordered="false" style="border: 1px solid #E9E9F0;"  >
         <a-table
             rowKey="guid_"
             :columns="columns"
             :dataSource="data1Source"
             :pagination="false"
             :loading="table1Loading"
             :keyboard="false"
             :bordered="true"
             :maskClosable="false"
             :customRow="eventTouch"
             :scroll="{ y:700 }"             
             :class="{'minClass':data1Source.length > 0}" 
             :rowClassName="setRowClassName"                     
         >          
         </a-table>
       </a-card> 
     </div>
     <div class="right" style="width:20%;border: 1px solid rgb(233, 233, 240);">
       <div class='top' style="width:100%;height:181px">
        <center
            :machineStatuList="data4Source"
            :machineStatuLoad="table4Loading" 
            ref="cuttingCenter"
        ></center>
       </div>
       <div class='bto'>
         <a-card :bordered="false" style="height: 598px;border: 1px solid #E9E9F0;"  >
         <a-table
             rowKey="guid_"
             :columns="columns1"
             :dataSource="data2Source"
             :pagination="false"
             :loading="table2Loading"
             :keyboard="false"
             :bordered="true"
             :maskClosable="false"
             :scroll="{ y:598 }"             
             :class="{'minClass':data2Source.length > 0}" 
             :rowClassName="setRowClassName"                     
         >          
         </a-table>
       </a-card> 
       </div>
     </div> 
   </div>
   <div class="footer">
      <div class="actionBox">
          <action 
            @handleDispatchMachine="handleDispatchMachine"        
             @Scancodetodispatch="Scancodetodispatch"  
            @OverOrderClick="OverOrderClick"
            @queryClick="queryClick"
          ></action>
      </div>
   </div>
   <!-- 查询弹窗 -->
   <a-modal
          title="订单查询"
          :visible="dataVisible"
          @cancel="reportHandleCancel"
          @ok="handleOk"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
          centered
   >
   <query-info ref='queryInfo' @keyupEnter1="keyupEnter1" />
   </a-modal>
   </a-spin>
 </div>
</template>

<script>
import {
  getStatisticsList,
  getWaitOrderList,
  GrindingPlateStart,
  GrindingPlateEnd,
  grindPlateFinishOrder,

} from "@/services/grindingplate";
import Center from './module/Center'
import Action from "./module/Action";
import QueryInfo from "./module/QueryInfo"
const columns = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell: (record, rowIndex) => {
      if(record.color_ == '#FF0000') {
        return { style: { 'background': '#FF0000', } }
      }
    },
  },
  {
    title: "拼板编号",
    dataIndex: "pdctno_",
    width: 120,
    ellipsis: true,
    className:"orderClass",
    align: 'center',
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 45,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "PNL数",
    dataIndex: "count4Pnl_",
    width: 55,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "面积",
    width: 60,
    dataIndex: "area_",
    ellipsis: true,
    align: 'center',
  },  
  {
    title: "板厚",
    dataIndex: "boardthick_",
    width: 60,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "订单工厂",
    width: 100,
    dataIndex: "fac_",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "交货日期",
    width: 100,
    ellipsis: true,
    dataIndex: "delDate_",
    align: 'center',
  },
  {
    title: "加工完成时间",
    width: 100,
    dataIndex: "toolEndDate_",
    ellipsis: true,
    align: 'center',
   
  },  
  {
    title: "开始时间",
    width: 100,
    dataIndex: "mbStartDate_",
    ellipsis: true,
    align: 'center',    
  },
  {
    title: "完成时间",
    width: 100,
    dataIndex: "mbEndDate_",
    ellipsis: true,
    align: 'center',
    
  },  
  {
    title: "状态",
    width: 60,
    ellipsis: true,
    dataIndex: "mbState_",
    align: 'center',
  },
];
const columns1 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell: (record, rowIndex) => {
      if(record.color_ == '#FF0000') {
        return { style: { 'background': '#FF0000', } }
      }
    },
  },
  {
    title: "机台名",
    dataIndex: "machineName_",
    width: 100,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "PNL数",
    dataIndex: "pnlQty_",
    width: 55,
    ellipsis: true,
    align: 'center',
  },
  
];

export default {
  name: "grindingPlate",
  components:{Action, Center,QueryInfo},
  inject:['reload'],
  data () {
    return {
      spinning:false,
      columns,
      columns1,
      loading:false,
      table1Loading:false,  // 待磨板列表load
      table2Loading:false,  // 对应机台信息load
      table4Loading: false, // 机台状态load
      data1Source:[],       //  待磨板列表
      data2Source:[],       //  对应机台信息列表
      data4Source:[],       // 机台状态集合
      selectedRowList: [],
      selectedRows:[],
      dataVisible:false,   //查询弹窗不显示
    }
  },
  methods: {  
    // 待磨板列表 
    getorderList(orderNum){
      let params = {}
      if(orderNum) {
        params.orderno = orderNum
      }
      this.table1Loading = true
      getWaitOrderList(params).then(res => {
        if (res.code == 1) {
          this.copyData1Source = res.data || []
          this.data1Source = JSON.parse(JSON.stringify(res.data)) || []
        }
      }).finally(
          ()=>{
            this.table1Loading = false
          }
      )
    },
   
    // 状态统计列表  
    getMachineStatuList(){
      this.table4Loading = true
      getStatisticsList().then(res => {
        if (res.code == 1) {
          this.data4Source = res.data
        }
      }).finally(()=> {
        this.table4Loading = false
      })
    },
    setRowClassName (record)  { 
      var classStr = ''
      // 单击设置背景色
      if(record.guid_ == this.selectedRowList){
        classStr = classStr + 'bacStyle' + ' '
      }      
      return classStr
    },
    
    // 选择分配订单
    // onSelectChange(selectedRowKeys, selectedRows) {
    //   this.selectedRowList = selectedRowKeys;
    //   this.selectedRows=selectedRows      
    //   // console.log("待分配订单",this.selectedRows)
    // },
    // 开始磨板
    handleDispatchMachine(){ 
      if(this.selectedRowList.length <= 0) {
        this.$message.warning('请选择待磨板订单')
        return
      }    
      this.spinning = true         
      GrindingPlateStart(this.selectedRowList[0]).then(res =>{
        if(res.code == 1){
            this.$message.success('磨板开始')
            
        }else{
          this.$message.error(res.message)
        } 
        this.reload()
      }).finally(()=>{
        this.spinning = false
      })
    },
    // 磨板完成
    OverOrderClick(){ 
      if(this.selectedRowList.length <= 0) {
        this.$message.warning('请选择订单')
        return
      }    
      this.spinning = true;         
      GrindingPlateEnd(this.selectedRowList[0]).then(res =>{
        if(res.code == 1){
            this.$message.success('磨板完成')
            
        }else{
          this.$message.error(res.message)
        } 
        this.reload()
      }).finally(()=>{
        this.spinning = false
      })
    },
    
    eventTouch(record,index) {
      return {
        props: {},
        on: { // 事件
          click: () => {
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowList = keys;  
            this.machineInfo(record)          
          },
          
          // dblclick: () => { //双击
          //   this.$refs.cuttingCenter.flagClick()
          //   this.rowId1 = record.pdctno_;
          //   this.$refs.cuttingCenter.rowId = '' ;
          //   getOrderMuIdList(record.guid_).then(res =>{
          //     if(res.code == 1){
          //       this.idList = res.data
          //     }else{
          //       this.$message.error(res.message)
          //     }
          //     console.log(this.idList )
          //   })
          // },
        }
      }
    },
    // 对应机台信息
    machineInfo(record){ 
      this.table2Loading = true;   
      let  orderno = record.pdctno_
      grindPlateFinishOrder({'orderno':orderno}).then(res =>{
        if(res.code == 1){ 
          console.log('res.data:',res) 
          this.data2Source = res.data
        }else{
          this.$message.error(res.message)
        } 
      }).finally(()=>{
        this.table2Loading = false
      })

    },
    // 弹窗关闭控制
    reportHandleCancel(){
      this.dataVisible = false;
    }, 
    //查询
    queryClick(){
      this.dataVisible= true
    },
    keyupEnter1(){
      this.dataVisible= false      
      this.getorderList(this.$refs.queryInfo.OrderNumber) 
    },
    handleOk(){
      this.dataVisible= false
      console.log(this.$refs.queryInfo.OrderNumber)
      this.getorderList(this.$refs.queryInfo.OrderNumber) 
    },
    
  },
  watch:{
    
  },   
  mounted() {
    this.getorderList();
    this.getMachineStatuList();
  },
  
}
</script>

<style lang="less" scoped>
/deep/.ant-input{
  font-weight: 500;
  color:#000000;
}
/deep/.ant-select{
  font-weight: 500;
  color:#000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color:#000000;
}
/deep/.ant-form-item-label > label{
  color:#000000;
}

.CuttingManagement {
  min-width: 1670px;
  user-select: none;
 /deep/ .content {
   .ant-card{
     .ant-card-body{
        padding: 0 !important;
        .bacStyle{
          background: #aba5a5!important;
        }
        .ant-table-row:hover{
          td {
            background: #aba5a5 !important;
          }
        }
     }
   }
    display: flex;
    height: 780px;   
  }
  // .rowcolor {
  //     background: #fff9e6;
  //     -moz-user-select:none;
  //     -webkit-user-select:none;
  //     user-select:none;
  //   }
  .footer {
    .actionBox {
      overflow: hidden;
      width: 100%;
      border: 2px solid #E9E9F0;
      height: 48px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      form {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-weight: 500;
      }
    }
    
  }
  .left {
  /deep/  .ant-card{
      height:780px !important;
    }
      .ant-table-tbody{       
        // .redStyle.ant-table-row-hover{
        //   td{
        //     background-color: #FF0000 !important;
        //   }
        // }
        // .redStyle.ant-table-row-hover.ant-table-row-selected{
        //   td{
        //     background-color: #FAFAFA !important;
        //   }
        // }
        .ant-table-row {
            .orderClass{
              user-select: all;
          }
        }
    }  
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    /deep/  .tabRightClikBox{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }
  }
  /deep/.right{
    .top{
      .ant-table-thead{
        height:32px;
      }
      // height:151px;
    }
  }
  /deep/ .ant-table {
    .ant-table-selection-col {
      width: 30px;
    }
  }
  background: #FFFFFF;
  // .minClass {
  //   /deep/ .ant-table-body {
  //     min-height: 720px;
  //   }
  // }
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 5px 0;
        }
      }
    }
    .ant-table-tbody {
      .ant-input {
      padding: 0;
      border: 0;
      border-radius: 0;
      margin: -5px 0;
      text-align: center;
    }
      tr {
        td {
          padding: 5px 5px;
        }
      }
      
    }
  }

  /deep/ .ant-table-body {    
      // &::-webkit-scrollbar {
      //   //整体样式
      //   width: 6px; //y轴滚动条粗细
      //   height: 6px;
      // }

      // &::-webkit-scrollbar-thumb {
      //   //滑动滑块条样式
      //   border-radius: 2px;
      //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      //   background: #ff9900;
      //   // #fff9e6
      // }
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }

      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 0;
        }
      }
    }
  }
  /deep/ .ant-table-scroll .mouseentered {
    overflow-x: scroll!important;
  }
}


</style>