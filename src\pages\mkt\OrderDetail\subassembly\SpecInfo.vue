<template>
  <div class="contentInfo" ref='SelectBox' @contextmenu.prevent.stop="cancel"> 
    <div  style="width:83.5%;display: flex;">
      <div :style="{width: tableWidth +'px',position:'relative'}"  class="left">
      <a-collapse v-model="activeKey" :bordered="false" :expandIconPosition="'right'">
        <a-spin :spinning="infoLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!infoDto.specPath"/>
          <div class='top1'  v-else>
            <ul>
              <li  class="liitem" @click="click1(infoDto.specPath,infoDto.extractContent)"   :class="infoDto.extractContent == list1?'licolor':''" >
              <img v-if="infoDto.specPath.toLowerCase().includes('pdf')" style='width:25px;padding-left: 5px;' src="@/assets/icon/pdf.png">
              <img v-else-if="infoDto.specPath.toLowerCase().includes('jpg') || infoDto.specPath.includes('png') || infoDto.specPath.includes('jpeg')" style='width:25px;padding-left: 5px;' src="@/assets/icon/jpg.png">
              <img v-else-if="infoDto.specPath.toLowerCase().includes('xlsx')||infoDto.specPath.includes('xls') " style='width:25px;padding-left: 5px;' src="@/assets/icon/12.png">
              <img v-else-if="infoDto.specPath.toLowerCase().includes('txt')" style='width:25px;padding-left: 5px;' src="@/assets/icon/txt.png">
              <img v-else-if="infoDto.specPath.toLowerCase().includes('docx')" style='width:25px;padding-left: 5px;' src="@/assets/icon/docx.png">
              <img v-else-if="infoDto.specPath.toLowerCase().includes('zip') ||infoDto.specPath.includes('rar') " style='width:25px;padding-left: 5px;' src="@/assets/icon/zip.png">
              <img v-else style='width:25px;padding-left: 5px;' src="@/assets/icon/none.png"> <span style="padding-left: 1px;"></span>{{infoDto.specPath}}</li>
            </ul>              
          </div>
        </a-spin>
      <a-collapse-panel key="2" header="原始附件(已识别)">
        <a-spin :spinning="infoLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!gerberLists.length"/>
          <div class='img-box' v-else  >
            <ul >
              <li v-for="(item,index) in gerberLists " :key="index" @click="click2(item.filename,item.filrpath,item.previewpath)"  @contextmenu.prevent.stop="rightClick1($event,item.filrpath,item.filename)" 
              class="liitem" :class="item.filrpath == list2 || item.filrpath == checklist?'licolor':''">
              <img v-if="item.filename.toLowerCase().includes('pdf')" style='width:25px;padding-left: 5px;' src="@/assets/icon/pdf.png">
              <img v-else-if="item.filename.toLowerCase().includes('jpg') || item.filename.toLowerCase().includes('png')
              || item.filename.toLowerCase().includes('bmp') ||item.filename.toLowerCase().includes('jpeg')" style='width:25px;padding-left: 5px;' src="@/assets/icon/jpg.png">
              <img v-else-if="item.filename.toLowerCase().includes('xlsx') || item.filename.toLowerCase().includes('xls')" style='width:25px;padding-left: 5px;' src="@/assets/icon/12.png">
              <img v-else-if="item.filename.toLowerCase().includes('txt')" style='width:25px;padding-left: 5px;' src="@/assets/icon/txt.png">
              <img v-else-if="item.filename.toLowerCase().includes('tif')" style='width:25px;padding-left: 5px;' src="@/assets/icon/tiff.png">
              <img v-else-if="item.filename.toLowerCase().includes('docx') || item.filename.toLowerCase().includes('doc')" style='width:25px;padding-left: 5px;' src="@/assets/icon/docx.png">
              <img v-else-if="item.filename.toLowerCase().includes('zip') ||item.filename.toLowerCase().includes('rar') " style='width:25px;padding-left: 5px;' src="@/assets/icon/zip.png">
              <img v-else-if="item.filename.toLowerCase().includes('csv') " style='width:25px;padding-left: 5px;' src="@/assets/icon/csv.png">
              <img v-else style='width:25px;padding-left: 5px;' src="@/assets/icon/inf.png">
              {{item.filename}}</li>
              <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
                <a-menu-item @click="down">下载</a-menu-item>
                <!-- <a-menu-item >更改下载路径</a-menu-item> -->
              </a-menu>        
            </ul>           
          </div>
        </a-spin>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="原始附件(未识别)">
        <a-spin :spinning="infoLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!nogerberLists.length"/>
          <div class='img-box' v-else>  
            <ul >
              <li v-for="(item,index) in nogerberLists " :key="index"  @contextmenu.prevent.stop="rightClick2($event,item.filrpath,item.filename)" 
              class="liitem" :class="item.filrpath == docxshow|| item.filrpath == checklist?'licolor':''">
              <img style='width:30px;padding-left: 5px;' src="@/assets/icon/none.png">{{item.filename}}</li>
              <a-menu :style="menuStyle1" v-show="menuVisible1" class="tabRightClikBox">
                <a-menu-item @click="down">下载</a-menu-item>
                <!-- <a-menu-item >更改下载路径</a-menu-item> -->
              </a-menu>     
            </ul>                
          </div>
        </a-spin>
      </a-collapse-panel>      
      <template #expandIcon="props">
        <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0"/>
      </template>
         </a-collapse> 
      </div> 
      <div id="touchmove" style="height:730px;width:5px;margin-right:2px;margin-left:2px;background: #ababab;cursor: e-resize"></div>
      <div :style="{width: innerWidth-tableWidth-12 +'px'}"  class="right" @mousewheel="handleWheel1">
         <div v-if="list2 || tifshow || imgShow || docxshow ||xlsxshow || csvshow" 
         style="height: 25px; position: relative;bottom: 5px;">
           <a-icon style="font-size: 25px;margin-left: 15px;" type="zoom-in"  @click="zoomIn"></a-icon>  
           <a-icon  style="font-size: 25px;margin-left: 15px;margin-bottom: 12px;"   type="zoom-out"  @click="zoomOut"></a-icon>
         </div>    
         <div v-else>
           <div style="height: 25px;width: 100%;"></div>
         </div>    
       <div  :class="list2 ? 'right2':'right1'">
        <a-spin :spinning="tifloading" style="width:100%;height:100%">
          <div v-show="list1">            
            <div  style='height:100%'   v-html="list1"></div>
          </div>
          <div v-show="list2" style="width:100%;height:100%;">            
              <file-preview v-show="list2" ref="filePreview" class="pdfstyle" type="SC"/>       
          </div>
          <div v-show="docxshow" style="width:100%;height:100%">
            <!-- <VueOfficeDocx  style='height:100%;width:100%' :src="docxshow" id="docximg"  /> -->
            <div id="imageContainerDocx" style="width:100%;position:absolute">
              <vue-office-docx v-show="docxshow" style='height:100%;width:100%' :src="docxshow" id="docximg"   ref="docxViewer" />
            </div>           
            <!-- <div id="grabberdocx" :style="docxStyle" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; cursor: grab;"></div> -->
          </div>
          <div v-show="listdoc" style="width:100%;height:100%">
            <iframe style="height: 100%; width: 100%;" :src="listdoc"></iframe>
          </div>          
          <div v-show="xlsxshow" style="width:100%;height:100%" >
            <vue-office-excel v-show="xlsxshow" class="excel" style='height:100%;width:100%' :src="xlsxshow" ref="xlsxViewer" />         
          </div>         
          <div v-show="txtShow" style='height:100%;width:100%'> 
            <div v-if="txtData!=''" v-html="txtData" style='height:98%;width:100%;font-size: 14px;'></div>
            <div v-else style="font-size: 20px;text-align: center;line-height: 600px;">当前文件不支持预览 请下载查看</div>
            <!-- <iframe style='height:98%;width:100%' :src="txtPre" type="text/plain"> </iframe> -->
          </div>
          <div v-show="imgShow" style="width:100%;height:100%;overflow:auto;">
              <div id="imageContainer" style="position:absolute;">
                  <img :src="list5" ref="img1" />
              </div>
              <div id="grabber" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; cursor: grab;"></div>
          </div>
          <div v-show="tifshow" style="width:100%;height:100%;overflow:auto;">
              <div id="imageContainer1" style="position:absolute;">
                  <img :src="listtif" ref="tifimg" />
              </div>
              <div id="grabber1" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; cursor: grab;"></div>
          </div>
          <div v-show="csvshow"  style="width:100%;height:100%;overflow:auto;">
            <vue-office-excel v-show="csvshow" class="excel" style='height:100%;width:100%' :src="csvdata" ref="csvViewer"/>   
          </div>
        </a-spin>   
       </div>        
      </div>
      <!-- <div txtPre  class="left">
        <a-collapse v-model="activeKey" :bordered="false" :expandIconPosition="'right'">
      <a-collapse-panel key="1" header="附件信息">
        <a-spin :spinning="infoLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!infoDto"/>
          <div class='img-box'  v-else>
            <ul>
              <li @click="click1(infoDto.extractContent)" >{{infoDto.specPath}}</li>
            </ul>              
          </div>
        </a-spin>
      </a-collapse-panel>
      <a-collapse-panel key="2" header="可识别文件">
        <a-spin :spinning="infoLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!gerberLists.length"/>
          <div class='img-box' v-else  >
            <ul v-for="(item,index) in gerberLists " :key="index">
              <li @click="click2(item.filrpath)">{{item.filename}}</li>
            </ul>
           
          </div>
        </a-spin>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="不可识别文件">
        <a-spin :spinning="infoLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!nogerberLists.length"/>
          <div class='img-box' v-else>           
          </div>
        </a-spin>
      </a-collapse-panel>      
      <template #expandIcon="props">
        <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0"/>
      </template>
         </a-collapse>        
      </div> 
      <div   class="right">
        <div v-if="list1">
          <div  style='height:100%' v-html="list1" ></div>
        </div>
        <div v-if="list2" style="width:100%;height:100%">
          <embed style='height:100%;width:100%' :src="list2" />
        </div>
       
      </div> -->
    </div>
     
  </div>  
</template>
<script >
const VueOfficeDocx = () => import('@vue-office/docx')
const VueOfficeExcel = () => import('@vue-office/excel')
const FilePreview = () => import('@/components/FilePreview/index')
import * as XLSX from 'xlsx'
import Tiff from 'tiff.js'
import '@vue-office/docx/lib/index.css' 
import '@vue-office/excel/lib/index.css'
import {specFileInfo} from "@/services/mkt/orderInfo";
export default {
  name: "",
  props:[''],
  components: { VueOfficeDocx, VueOfficeExcel,FilePreview},
  data(){
    return {
      specFileData:{},
      fname:'',
      innerWidth: window.innerWidth, // 获取窗宽度
      tableWidth: window.innerWidth/5 -40, // 设置第一个div的宽度
      list1:'',
      list2:'',
      csvshow:false,
      csvdata: null,
      txtData:'',
      docxshow:'',
      listdoc:'',
      xlsxshow:'',
      list5:'',
      listtif:'',
      tifloading:false,
      tifshow:false,
      txtPre:'',
      xlsShow:false,
      workbook:null,
      txtShow:false,
      imgShow: false,
      checklist:'',
      infoLoading:false,
      activeKey:['2','3',],
      infoDto:{},
      gerberLists:[],
      nogerberLists:[],
      menuVisible:false,
      menuVisible1:false,
      filename:'',
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      zoomScale: 1,
      docxStyle:{
        height:'100%',
      },
      tiflist:[],
      zoomNum:0,
      PdfStarHeight:0,
      PdfStarWidth:0,
    }
  }, 
  created() { 
   
  },
 async mounted(){    
    document.getElementById("touchmove").onmousedown = this.textWidthChange;
    let boid = this.$route.query.boid
   await specFileInfo(boid).then(res=>{
      if(res.code){
        this.specFileData = res.data
        this.tiflist =[]
        for (let index = 0; index < this.specFileData.gerberLists.length; index++) {
          const element = this.specFileData.gerberLists[index].filrpath;
          let arr = element.split('.')
          let name = arr[arr.length-1]
          if(name.toLowerCase()=='doc' || name.toLowerCase()=='xls'){
            this.listdoc = "https://view.officeapps.live.com/op/view.aspx?src=" + encodeURI(element) 
          } 
          if(name.toLowerCase() == 'tif' ||  name.toLowerCase() == 'tiff'){ 
            fetch(element).then(response => response.arrayBuffer()).then(buffer => {
                const tiff = new Tiff({ buffer: buffer });
                this.tiflist.push(tiff.toDataURL()+'文件名'+this.specFileData.gerberLists[index].filename)
              }).catch(error => {
                console.error("Error fetching or processing data:", error);
              });
      }
        }
        this.getList()
      }
    })   
    const imageContainer = document.getElementById('imageContainer');
    const grabber = document.getElementById('grabber');    
    let isDragging = false;
    let startX, startY, offsetX, offsetY;    
    grabber.addEventListener('mousedown', (event) => {
        isDragging = true;
        startX = event.clientX - imageContainer.offsetLeft;
        startY = event.clientY - imageContainer.offsetTop;
        offsetX = imageContainer.offsetLeft;
        offsetY = imageContainer.offsetTop;
        grabber.style.cursor = 'grabbing';
    });
    document.addEventListener('mousemove', (event) => {
        if (isDragging) {
            const newX = event.clientX - startX;
            const newY = event.clientY - startY;
            imageContainer.style.left = `${newX}px`;
            imageContainer.style.top = `${newY}px`;
        }
    });
    const imageContainer1 = document.getElementById('imageContainer1');
    const grabber1 = document.getElementById('grabber1');    
    let isDragging1 = false;
    let startX1, startY1, offsetX1, offsetY1;    
    grabber1.addEventListener('mousedown', (event) => {
        isDragging1 = true;
        startX1 = event.clientX - imageContainer1.offsetLeft;
        startY1 = event.clientY - imageContainer1.offsetTop;
        offsetX1 = imageContainer1.offsetLeft;
        offsetY1 = imageContainer1.offsetTop;
        grabber1.style.cursor = 'grabbing';
    });
    document.addEventListener('mousemove', (event) => {
        if (isDragging1) {
            const newX = event.clientX - startX1;
            const newY = event.clientY - startY1;
            imageContainer1.style.left = `${newX}px`;
            imageContainer1.style.top = `${newY}px`;
        }
    });
    //docx 拖动
    // const imageContainerDocx = document.getElementById('imageContainerDocx');
    // const grabberdocx = document.getElementById('grabberdocx')
    // let isDraggingdocx = false;
    // let startXdocx, startYdocx, offsetXdocx, offsetYdocx;    
    // grabberdocx.addEventListener('mousedown', (event) => {
    //   isDraggingdocx = true;
    //     startXdocx = event.clientX - imageContainerDocx.offsetLeft;
    //     startYdocx = event.clientY - imageContainerDocx.offsetTop;
    //     offsetXdocx = imageContainerDocx.offsetLeft;
    //     offsetYdocx = imageContainerDocx.offsetTop;
    //     grabberdocx.style.cursor = 'grabbing';
    // });
    // document.addEventListener('mousemove', (event) => {
    //     if (isDraggingdocx) {
    //         const newX = event.clientX - startXdocx;
    //         const newY = event.clientY - startYdocx;
    //         imageContainerDocx.style.left = `${newX}px`;
    //         imageContainerDocx.style.top = `${newY}px`;
    //     }
    // });   
    document.addEventListener('mouseup', () => {
      isDragging = false;
      isDragging1 = false;
    //  isDraggingdocx = false;
      grabber.style.cursor = 'grab';
      grabber1.style.cursor = 'grab';
    //  grabberdocx.style.cursor = 'grab';
    });
    
  }, 
  watch:{
    'messageList':{
      handler(val){        
        this.get(val)
      }
    },
    sheetTraderList:{
        deep: true,
        handler: function(newval, oldval){
            this.$nextTick(() => {
                this.sheetTrader = newval
            })
        }
    },
    boardBrandList:{
        deep: true,
        handler: function(newval, oldval){
            this.$nextTick(() => {
                this.boardBrand = newval
            })
        }
    }, 
         
  },   
  methods:{ 
    
    handleWheel1(e){
      if (e.ctrlKey) {
        e.preventDefault(); 
        const { deltaY } = e;   
        if(Math.sign(deltaY) >0){
          this.zoomScale -= 0.05
          if (this.zoomScale < 0.5) {
            this.zoomScale = 0.5
          }
        }else{
          this.zoomScale += 0.05
        }      
        if(this.list2){
          const scale = Math.sign(deltaY) * -0.05;
          this.zoomImage(scale);
        }else{
          this.zoomImage(Number(this.zoomScale).toFixed(2));
        }       
      }
    },
    zoomIn() {     
      this.zoomScale += 0.05 
      if(this.list2){
        this.zoomImage(0.05)
      }else{
        this.zoomImage(Number(this.zoomScale).toFixed(2))
      }  
    },
    zoomOut() {    
      this.zoomScale -= 0.05
      if (this.zoomScale < 0.5) {
        this.zoomScale = 0.5
      } 
      if(this.list2){
        this.zoomImage(-0.05)
      }else{
        this.zoomImage(Number(this.zoomScale).toFixed(2))
      }     
    },
    zoomImage(scale) {
      let img 
      if(this.list2){
        let pdf = document.getElementsByClassName('pdfstyle')[0].children[0].children[1].children[0].children[0]
        img = pdf
      }else if (this.listtif){
        img = this.$refs.tifimg
      }else if(this.imgShow){
        img = this.$refs.img1
      }else if(this.docxshow){      
      const viewer = this.$refs.docxViewer
      const container = viewer.$el.querySelector('.docx')
      container.style.transform = `scale(${scale})`
      let num =Number((scale-1)/0.05).toFixed(0)
        let top = 30*num
        let left = 10*num
      container.style.top = `${top}px`
      container.style.left = `${left}px`
      //this.docxStyle.height = container.offsetHeight + 'px'
      }else if(this.xlsxshow){
        const xlsxViewer = this.$refs.xlsxViewer
        const container = xlsxViewer.$el.querySelector('.x-spreadsheet')
        container.style.transform = `scale(${scale})`
        let num =Number((scale-1)/0.05).toFixed(0)      
        let top = 17*num
        let left = 26*num
        container.style.position = 'relative'
        container.style.top = `${top}px`
        container.style.left = `${left}px`
      }else if (this.csvshow){
        const csvViewer = this.$refs.csvViewer
        const container = csvViewer.$el.querySelector('.x-spreadsheet')
        container.style.transform = `scale(${scale})`
        let num =Number((scale-1)/0.05).toFixed(0)      
        let top = 17*num
        let left = 26*num
        container.style.position = 'relative'
        container.style.top = `${top}px`
        container.style.left = `${left}px`
      }
      if(img){
        if(this.list2){
        const currentHeight = img.offsetHeight;
        const currentWidth = img.offsetWidth;
        if(this.zoomNum == 0){
          this.PdfStarWidth = currentWidth;
          this.PdfStarHeight = currentHeight;
        }
        const newHeight = currentHeight * (1 + scale);
        const newWidth = currentWidth * (1 + scale);
        img.style.height = `${newHeight}px`;
        img.style.width = `${newWidth}px`;       
        }else{
          img.style.transform = `scale(${scale})`
        } 
      }
      this.zoomNum++
    },
    getList(){
      this.infoDto = this.specFileData.infoDto
      this.gerberLists = this.specFileData.gerberLists
      this.nogerberLists = this.specFileData.nogerberLists
      if(this.nogerberLists.length==0 && this.gerberLists.length==0 ){
        this.activeKey = []
      }else if (this.nogerberLists.length==0 && this.gerberLists.length!=0 ){
        this.activeKey = ['2']
      }else if (this.nogerberLists.length!=0 && this.gerberLists.length!=0 ){
        this.activeKey = ['2','3']
      }else if (this.nogerberLists.length!=0 && this.gerberLists.length==0 ){
        this.activeKey = ['3']
      }
      this.click1(this.infoDto.specPath,this.infoDto.extractContent)
    },
    click1(name,val){
      this.list1 = val
      this.list2 = '';    
      this.listdoc = ''
      this.docxshow =''
      this.xlsxshow = ''
      this.list5=''
      this.listtif = ''
      this.tifshow = false
      this.csvshow=false
      this.txtPre=''
      this.txtShow=false
      this.imgShow = false
      this.xlsShow=false
      this.checklist = ''
      this.filename = name      
    },
    click2(name,val,previewpath){
      // let parts = val.split('\\');
      // let lastPart = parts[parts.length - 1];
      // let encodedLastPart = encodeURIComponent(lastPart);
      // parts[parts.length - 1] = encodedLastPart;
      // val = parts.join('\\');
      this.tifloading = true
      this.filename = ''
      this.list1 ='';
      this.list2 = '';    
      this.docxshow =''
      this.listdoc= ''
      this.xlsxshow = ''
      this.list5=''
      this.listtif = ''
      this.txtPre=''
      this.txtShow=false
      this.imgShow = false
      this.tifshow = false
      this.csvshow=false
      this.csvdata='',
      this.txtData='',
      this.xlsShow=false
      this.checklist = val
      this.filename = name      
      this.fname = name 
      let arr = val.split('.')
      var index = arr.length - 1    
      this.zoomScale = 1.0 
      const imageContainer = document.getElementById('imageContainer');
      if(imageContainer){
        imageContainer.style.left ='0px'
        imageContainer.style.top = '0px'
      }
      const imageContainer1 = document.getElementById('imageContainer1');
      if(imageContainer1){
        imageContainer1.style.left ='0px'
        imageContainer1.style.top = '0px'
      }
      if(arr[index].toLowerCase() == 'docx' ){        
        this.docxshow = val
        this.tifloading = false
        const viewer = this.$refs.docxViewer        
        const container = viewer.$el.querySelector('.docx')
        if(container){
          container.style.transform = `scale(${1.0})`
          container.style.top = `${0}px`
          container.style.left = `${0}px`
        }        
      }else if(arr[index].toLowerCase() == 'doc' || arr[index].toLowerCase() == 'xls'){ 
        this.listdoc = "https://view.officeapps.live.com/op/view.aspx?src=" + encodeURI(val) 
        this.tifloading = false
      }else if(arr[index].toLowerCase() == 'xlsx' || arr[index].toLowerCase() == 'xlsm'){ 
        this.xlsxshow = val
        this.tifloading = false
        const xlsxViewer = this.$refs.xlsxViewer
        const container = xlsxViewer.$el.querySelector('.x-spreadsheet')
        if(container){
          container.style.transform = `scale(${1.0})`
          container.style.top = `${0}px`
          container.style.left = `${0}px`
        }    
      }else if(arr[index].toLowerCase() == 'png'||arr[index].toLowerCase() == 'jpg' ||arr[index].toLowerCase() == 'jpeg'||arr[index].toLowerCase() == 'bmp'){ 
        this.imgShow = true 
        this.list5=val
        this.tifloading = false       
        this.getimgsize(this.$refs.img1)
        let img = this.$refs.img1
        img.style.transform = `scale(${1.0})`
      }else if(arr[index].toLowerCase() == 'tif' ||  arr[index].toLowerCase() == 'tiff'){ 
        this.tiflist.forEach((item,index)=>{
          if(item.split('文件名')[1]==name){
             this.listtif = item.split('文件名')[0]
             this.tifshow = true
             this.tifloading = false
             this.getimgsize(this.$refs.tifimg)
             let img = this.$refs.tifimg
             img.style.transform = `scale(${1.0})`
          }
        })
        // fetch(val).then(response => response.arrayBuffer()).then(buffer => {
        //     const tiff = new Tiff({ buffer: buffer });
        //     this.listtif = tiff.toDataURL();
        //     this.tifloading = false
        //     this.tifshow = true           
        //     this.getimgsize(this.$refs.tifimg)
        //     let img = this.$refs.tifimg
        //     img.style.transform = `scale(${1.0})`
        //   }).catch(error => {
        //     this.tifloading = false
        //     console.error("Error fetching or processing data:", error);
        //   });
      }else if(arr[index].toLowerCase() == 'pdf'){    
        this.list2 = val;       
        this.$refs.filePreview.showView(this.list2)
        this.tifloading = false
       let img =  document.getElementsByClassName('pdfstyle')[0].children[0].children[1].children[0].children[0]
       let imageContainer2 =  document.getElementsByClassName('pdfstyle')[0].children[0].children[1].children[0]
       img.style.height='340px'
       imageContainer2.style.left = `${0}px`;
       imageContainer2.style.top = `${0}px`;
       this.$nextTick(()=>{    
        if(this.zoomNum ==0){
          this.PdfStarHeight = img.offsetHeight;
          this.PdfStarWidth = img.offsetWidth;          
        }
        img.style.height = `${this.PdfStarHeight}px`;
        img.style.width = `${this.PdfStarWidth}px`; 
        this.zoomNum =0;  
       })       
      }else if (arr[index].toLowerCase() == 'csv'){      
        this.tifloading = false
        fetch(val).then(response => response.text()).then(csvData => {
            this.csvshow = true
            this.tifloading = false
            const lines = csvData.replace('\r','').trim().split('\n');
            const keys = lines[0].split(','); 
            let num
            if(lines.length>1){
              num = 1
            }else{
              num = 0
            }
            const data = lines.slice(num).map(line => {
              const values = line.split(',');
              return keys.reduce((obj, key, index) => {
                obj[key] = values[index].replace('\r','');
                return obj;
              }, {});
            });
            this.exportExcelFile3(data, '',num)           
        })
        .catch(error => console.error('Error fetching CSV file:', error));
      }else{
        this.txtShow = true 
        this.txtPre = previewpath
        fetch(this.txtPre).then(response => response.text()).then(txt => {  
          this.txtData = txt
        })
        .catch(error => console.error('Error fetching TXT file:', error));
        this.tifloading = false
      }          
    }, 
    exportExcelFile3(array, sheetName,num) {
      const jsonWorkSheet = XLSX.utils.json_to_sheet(array);
      let obj = Object.keys(jsonWorkSheet)
      for (let index = 0; index < obj.length; index++) {
        if(obj[index].indexOf('2')!=-1 && num == 0)
         delete jsonWorkSheet[obj[index]]
      }
      const workBook = {
        SheetNames: [sheetName],
        Sheets: {
          [sheetName]: jsonWorkSheet,
        }
      };
      const excelBuffer = XLSX.write(workBook, { bookType: 'xlsx', type: 'array' });
      const excelBlob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const excelUrl = URL.createObjectURL(excelBlob);
      this.csvdata = excelUrl;
      const csvViewer = this.$refs.csvViewer
        const container = csvViewer.$el.querySelector('.x-spreadsheet')
        if(container){
          container.style.transform = `scale(${1.0})`
          container.style.top = `${0}px`
          container.style.left = `${0}px`
        }   
    },
    getimgsize(imgElement){
      if(imgElement){
        imgElement.onload = function() {
        var renderedWidth = imgElement.naturalWidth;
        var renderedHeight = imgElement.naturalHeight;
        imgElement.style.width = renderedWidth + 'px'
        imgElement.style.height= renderedHeight + 'px'
        if(renderedWidth>1124  && renderedHeight>=650){
          let widthRatio = 1124 /renderedWidth;
          let heightRatio = 650 /renderedHeight;
          let ratio = Math.min(widthRatio, heightRatio);
          let newWidth = renderedWidth * ratio;
          let newHeight = renderedHeight * ratio;
          imgElement.style.width = newWidth + "px";
          imgElement.style.height = newHeight + "px";
        } else if (renderedWidth>1124 && renderedHeight<650){
          imgElement.style.width = "1000px";
          imgElement.style.height='';
        }else if(renderedWidth<=1124 && renderedHeight>650){
          imgElement.style.height = "630px";
          imgElement.style.width = '';
        }
      }
      }  
    },
    cancel(){
      this.menuVisible = false;
    },
    rightClick1(e,url,name){    
      this.fname = name 
      this.menuVisible = true;
      this.menuVisible1 = false
      this.menuStyle.top = e.clientY- 270 +  "px";
      this.menuStyle.left =  e.clientX - document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
      document.body.addEventListener("click", this.bodyClick);
      this.checklist = url
    },  
    rightClick2(e,url,name){ 
      this.fname = name 
      this.menuVisible = false
      this.menuVisible1 = true;
      this.menuStyle1.top = e.clientY- 530 +  "px";
      this.menuStyle1.left =  e.clientX - document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
      document.body.addEventListener("click", this.bodyClick);
      this.checklist = url
    },  
     // 右键事件
     bodyClick() {
        this.menuVisible = false;
        this.menuVisible1 = false;
        document.body.removeEventListener("click", this.bodyClick);
      },
    changemark(){
      if(!this.formData.markPosition){
        this.formData.ulType=null ;
        this.formData.markType=null;
        this.formData.markFace=null;
        this.formData.periodicFormat=null;
      }
    },
    mapKey(data){
      if (!data) {
        return []
      } else {
        return Object.keys(data).map(item => {
          return {'value':item, 'lable': data[item]}
        })
      }
    },
    mapKey1(data){       
        if (!data) {
          return []
        } else {         
          return Object.keys(data).sort().map(item => {
            return {'value':item, 'lable': data[item]}
          })
        }
      },
    // 查看拼版图
    jigsawPuzzleClick(){
      var arr = this.formData.pinBanType.toLowerCase().split('x')
      this.makeupVisible = true
      this.$nextTick(()=>{
        this.$refs.makeup.impositionInformationExample(this.formData.boardHeight, this.formData.boardWidth, arr[0] || 1, arr[1] || 1, this.formData.processEdgesStrL || "无", this.formData.processEdgesStrR || 0, this.formData.vCut || "none", this.formData.grooveWidth || 0, this.formData.grooveHeight || 0);
      })      
    },
    // 下载文件
    down(){
      // let parts = this.checklist.split('\\');
      // let lastPart = parts[parts.length - 1];
      // let encodedLastPart = encodeURIComponent(lastPart);
      // parts[parts.length - 1] = encodedLastPart;
      // this.checklist = parts.join('\\');
      if(this.checklist){
       this.downloadByteArrayFromURL(this.checklist,this.fname)
      }else{
        this.$message.warning('文件无法下载')
      }
    },  
    downloadByteArrayFromURL(url, fileName) {
          fetch(url)
            .then(response => response.arrayBuffer())
            .then(arrayBuffer => {
              const byteArray = new Uint8Array(arrayBuffer);
              const blob = new Blob([byteArray], { type: 'application/octet-stream' });
              const url = URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = fileName;
              link.click();
              URL.revokeObjectURL(url);
            })
            .catch(error => {
              console.error('Error downloading file:', error);
            });
        }, 
    textWidthChange(e) {
      let that = this
      let dx = e.clientX;//当你第一次单击的时候，存储X轴的坐标。
      document.onmousemove = e => {
        if (e.clientX + 42 >= that.innerWidth) {
          return
        }
        if(e.clientX < 200) {
          return
        }
        if (e.clientX < dx) {
          that.tableWidth -= (dx - e.clientX)
        } else {
          that.tableWidth += (e.clientX - dx)
        }
        dx = e.clientX
      };
      document.onmouseup = e => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
      e.stopPropagation();
      e.preventDefault();
      return false;
    },  
    },
    
 }

</script>
<style scoped lang="less">
/deep/.ant-spin-container{
  height: 100%;
}
/deep/.x-spreadsheet-overlayer{
  width: 100% !important;
  height: 100% !important;
}
/deep/.x-spreadsheet-sheet{
  width: 100% !important;
  height: 100% !important;
}
/deep/.x-spreadsheet-overlayer-content{
  width: 100% !important;
  height: 100% !important;
}
.top1{
  position: relative;
  ul{
    margin:0 !important;
    color: #000000;
    padding:6px !important;
  }
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header{
  color:#000000;
}
/deep/.x-spreadsheet-sheet{
  width:100%!important;
  height: 623px;
}
.tabRightClikBox{
  border:2px solid rgb(238, 238, 238) !important;
  li{
    height:24px;
    line-height:10px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color:#000000;
  }
  .ant-menu-item:not(:last-child){
    margin-bottom: 4px;
  }
}
/deep/.ant-collapse{
  min-width: 248px;
  overflow: auto;
}
.contentInfo {
  font-size: 12px; 
  //width:1676px; 
  /deep/.ant-collapse-header{
    background:#b3b3b3 !important;
    height: 34px;
    padding: 6px 16px !important;
  }
  /deep/.ant-collapse{
    border-radius: 0;
  }
  .left{
    width:25%;
    height:730px;
    overflow: auto;
    //border:2px solid #F0F2F5;
    border-top:1px solid #F0F2F5!important;
  }
  }
  .right{
    padding:10px;
    &::-webkit-scrollbar {
      //整体样式
      width: 10px !important; //y轴滚动条粗细
      height: 10px !important; //x轴滚动条粗细
    }
    &::-webkit-scrollbar-thumb {
      //滑动滑块条样式
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      background: #a5a5a5;
    }
    overflow: auto;
    width:75%;
    height:728px;  
  }
  .right1{
    height:668px; 
    width: 100%;
    &::-webkit-scrollbar {
      //整体样式
      width: 10px !important; //y轴滚动条粗细
      height: 10px !important; //x轴滚动条粗细
    }
    &::-webkit-scrollbar-thumb {
      //滑动滑块条样式
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      background: #a5a5a5;
    }
    overflow: auto; 
    border:2px solid #F0F2F5!important;
  }
  .right2{
    height:650px; 
    width: 100%;
    &::-webkit-scrollbar {
      //整体样式
      width: 10px !important; //y轴滚动条粗细
      height: 10px !important; //x轴滚动条粗细
    }
    &::-webkit-scrollbar-thumb {
      //滑动滑块条样式
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      background: #a5a5a5;
    }
    overflow: auto; 
    border:2px solid #F0F2F5!important;
  }
  /deep/.ant-collapse-content{
    border-top:1px solid #d9d9d9!important;
    background-color: #fff;
  }
  /deep/.ant-collapse-content-box{
    padding:0;     
    ul{
      border-bottom:1px solid #d9d9d9;
      margin-bottom: 0; 
      padding-left:0;
     li{
      padding:6px;
      width:100%;
      white-space: nowrap;
      color:#000000;
      overflow: hidden;
      
     }
    }
  }
  .tabRightClikBox{
    li:hover{
      color:black!important;     
      font-weight: 500;
    }
  }
  li:hover{
      color:white;     
      font-weight: 500;
      background-color: #ff9900;
    }
    .licolor{
    color:white;
    font-weight: 500;
    background-color: #ff9900;
  }
  .rightcolor{
    color:white;
    font-weight: 500;
    background-color: #ff9900;    
  }

</style>