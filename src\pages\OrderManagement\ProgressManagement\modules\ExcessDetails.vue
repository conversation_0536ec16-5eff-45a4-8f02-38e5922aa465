<!--生产管理- 生产进度 -过数详情 -->
<template>
    <div id="tab_warp" class="tab_warp" style="height:500px;overflow-y:auto">
        <div class="clearfix">
            <ul class="instruction clearfix" >
                <li><i class="status-default"></i>尚未开始</li>
                <li><i class="status-default status-jump"></i>跳过</li>
                <li><i class="status-default status-success"></i>正常</li>
                <li><i class="status-default status-fail"></i>延期</li>
            </ul>
            <ul class="instruction clearfix" style="float:left;">
                <li><i class="status-default"></i>计划：{{ExcessData.totalTimeSpan}}</li>
                <li><i class="status-default"></i>耗时：{{ExcessData.pastTimeSpan}}</li>
                <li v-if="ExcessData.syTimeSpan > 0 "><i class="status-default status-success"></i>剩余：{{ExcessData.syTimeSpan}}</li>
                <li v-else><i class="status-default status-fail"></i>超出：{{ExcessData.syTimeSpan}}</li>
            </ul>
        </div>
        <table class="tabcolor" style="width: 100%; table-layout: fixed; font-size: 14px;" data-grid="">
        <tbody>
            <tr class="info_title">
                <th class="mid_line" style="width:14px;"></th>
                <th class="mid_line" style="text-align:left; padding-left: 30px; width:115px;">工序</th>
                <th class="mid_line" style="width:72px;text-align:center;">操作人员</th>
                <th class="mid_line" style="width:70px;text-align:center;">数量</th>
                <th class="mid_line" style="width:70px;text-align:center;">寄存报废数</th>
                <th class="mid_line" style="width:80px;text-align:center;">时间</th>
                <th class="mid_line" style="width:96px;text-align:center;">耗时(分钟)</th>
                <th class="mid_line" style="width:96px;text-align:center;">逾期(分钟)</th>
            </tr>
            <tr data-row="" class="info_cont" template=""  v-for="(item,index) in ExcessData.flowListDtos" :key="index">                
                <td style="position: relative; border-right: 2px solid #ddd;">
                    <i class="status-default" v-if="item.status == 0 "></i>
                    <i class="status-default status-jump" v-else-if="item.status == 1 "></i>
                    <i class="status-default status-success" v-else-if="item.status == 2 "></i>
                    <i class="status-default status-fail" v-else-if="item.status == 3 "></i>
                </td>
                <td style="text-align:left;  padding-left: 30px;" class="tip">{{item.stepName}}</td>
                <td style="text-align:center;">{{item.operatorName}}</td>
                <td style="text-align:center;">{{item.num>0?item.num+"("+ item.unit+")":""}}</td>
                <td style="text-align:center;">{{item.damageNum>0?item.damageNum:""}}{{ item.damageNum>0 && item.scrapUnit?"("+item.scrapUnit+")":""}}</td>
                <td style="text-align:center;">{{item.createTime}}</td>
                <td style="text-align:center;">{{item.timeSpan}}</td>
                <td style="text-align:center;">{{item.expireTimeSpan>0?item.expireTimeSpan:""}}</td>
            </tr>
                        
        </tbody>

    </table>
    </div>    
</template>
  
  <script>
  import moment from "moment";
  export default {
    name: "ExcessDetails",
    props:['ExcessData',],
    data(){
      return{          
      }
    },   
    methods: {
      moment,
      
      
      
    }
  }
  </script>
  
  <style scoped>
   table {
        border-collapse: collapse;
        border-spacing: 0;
    }

    td {
        word-wrap: break-word;
        word-break: break-all;
    }

    .tab_warp th, .tab_warp td {
        padding-bottom: 10px;
        /* font-family: "Microsoft YaHei"; */
        font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;
    }

    .status-default {
        display: block;
        width: 14px;
        height: 14px;
        background-color: #ddd;
        -webkit-border-radius: 50%;
        -webkit-border-radius: 50%;
        border-radius: 50%;
    }

    .status-success {
        background: url('../../../../assets/web/icon_status.png') no-repeat 0 0;
        -webkit-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
    }

    .status-fail {
        background: url('../../../../assets/web/icon_status.png') no-repeat 0 -14px;
        -webkit-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
    }

    .status-jump {
        background: url('../../../../assets/web/icon_status.png') no-repeat 0 -28px;
        -webkit-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
    }

    .info_cont .status-default {
        position: absolute;
        left: 9px;
        top: 2px;
    }

    .instruction {
        float: right;
    }

        .instruction li {
            float: left;
            margin-right: 10px;
            font-size: 14px;
            /* font-family: "Microsoft YaHei"; */
            font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;
            list-style: none;
        }

        .instruction .status-default {
            display: inline-block;
            margin-right: 6px;
            vertical-align: text-bottom;
        }
  </style>