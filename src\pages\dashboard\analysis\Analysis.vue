<template>
  <div style="background-color: white">
    <a-tabs :activeKey="activeKey" @change="tabClick">
      <a-tab-pane key="1" tab="第一页">
        <div class="engineering-analysis">
          <div class="header">
            <a-form-model-item label="工厂筛选" :label-col="{ span: 1 }" :wrapper-col="{ span: 3 }">
              <div>
                <a-select showSearch allowClear optionFilterProp="lable" placeholder="授权工厂" v-model="FactoryId" @change="Factorychange">
                  <a-select-option
                    style="color: blue"
                    v-for="(item, index) in mapKey(factroyList)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </div>
          <div class="content">
            <!-- 工程分析内容 -->
            <a-spin :spinning="Statisticsloading">
              <div class="bac">
                <!-- 统计 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr">
                  <div style="height: 60px; align-items: center; justify-content: space-around; display: flex">
                    <a-button type="primary" shape="round" :class="Timetype == 'day' ? 'choose' : 'unchoose'" @click="Getthetotal('day')">
                      当日
                    </a-button>
                    <a-button type="primary" shape="round" :class="Timetype == 'week' ? 'choose' : 'unchoose'" @click="Getthetotal('week')">
                      周度
                    </a-button>
                    <a-button type="primary" shape="round" :class="Timetype == 'month' ? 'choose' : 'unchoose'" @click="Getthetotal('month')">
                      月度
                    </a-button>
                    <a-range-picker style="width: 260px; border-radius: 5px" @change="intervalChange" v-model="dateString" allowClear />
                  </div>
                </div>
                <div class="Statistics">
                  <div style="margin-bottom: 0">
                    <div style="margin-top: 24px">
                      <div class="Orderingsituation">
                        <div class="labelSTY">
                          <div>总接收品种(款)</div>
                          <div>总接收面积(㎡)</div>
                          <div style="height: 28px; background-color: white"></div>
                          <div>总下线品种(款)</div>
                          <div>总下线面积(㎡)</div>
                        </div>
                        <div class="labelSTY">
                          <div>{{ Orderingdata.allReceivenum }}款</div>
                          <div>{{ Orderingdata.allReceivearea }}㎡</div>
                          <div style="height: 28px; background-color: white"></div>
                          <div>{{ Orderingdata.allofflinenum }}款</div>
                          <div>{{ Orderingdata.allofflinearea }}㎡</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="DonutChart">
                      <div id="receiveLayertype"></div>
                      <div id="receiveOrdertype"></div>
                      <div id="receiveHDItype"></div>
                      <div id="receiveHVAtype"></div>
                    </div>
                    <div class="DonutChart">
                      <div id="OfflineLayertype"></div>
                      <div id="OfflineOrdertype"></div>
                      <div id="OfflineHDItype"></div>
                      <div id="OfflineHVAtype"></div>
                    </div>
                  </div>
                </div>
              </div>
            </a-spin>
            <a-spin :spinning="Receptionloading">
              <div class="TotalOrder bac">
                <div>
                  <div id="Placeanorder"></div>
                  <div v-show="ReceptionData.length > 0">
                    <table
                      border="1"
                      style="width: 13%; color: rgba(121, 128, 137, 0); font-size: 8px; text-align: center; margin-top: -48px; margin-left: 53px"
                    >
                      <thead style="height: 21px">
                        &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
                      </thead>
                      <tbody>
                        <tr>
                          <td style="height: 21px">&emsp;&emsp;&emsp;&emsp;&emsp;</td>
                        </tr>
                      </tbody>
                    </table>
                    <table
                      border="1"
                      style="width: 71%; color: rgb(121, 128, 137); margin-top: -64px; margin-left: 158px; font-size: 8px; text-align: center"
                    >
                      <thead>
                        <tr>
                          <td v-for="index in 12" :key="index" style="width: 48px; height: 21px">{{ index }}月</td>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in ReceptionData" :key="index">
                          <td style="height: 21px">{{ item.month1 }}</td>
                          <td style="height: 21px">{{ item.month2 }}</td>
                          <td style="height: 21px">{{ item.month3 }}</td>
                          <td style="height: 21px">{{ item.month4 }}</td>
                          <td style="height: 21px">{{ item.month5 }}</td>
                          <td style="height: 21px">{{ item.month6 }}</td>
                          <td style="height: 21px">{{ item.month7 }}</td>
                          <td style="height: 21px">{{ item.month8 }}</td>
                          <td style="height: 21px">{{ item.month9 }}</td>
                          <td style="height: 21px">{{ item.month10 }}</td>
                          <td style="height: 21px">{{ item.month11 }}</td>
                          <td style="height: 21px">{{ item.month12 }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div>
                  <div id="Reception"></div>
                  <div v-show="PlaceanorderData.length > 0">
                    <table
                      border="1"
                      style="width: 13%; color: rgba(121, 128, 137, 0); font-size: 8px; text-align: center; margin-top: -48px; margin-left: 53px"
                    >
                      <thead style="height: 21px">
                        &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
                      </thead>
                      <tbody>
                        <tr>
                          <td style="height: 21px">&emsp;&emsp;&emsp;&emsp;&emsp;</td>
                        </tr>
                      </tbody>
                    </table>
                    <table
                      border="1"
                      style="width: 71%; color: rgb(121, 128, 137); margin-top: -64px; margin-left: 158px; font-size: 8px; text-align: center"
                    >
                      <thead>
                        <tr>
                          <td v-for="index in 12" :key="index" style="width: 48px; height: 21px">{{ index }}月</td>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in PlaceanorderData" :key="index">
                          <td style="height: 21px">{{ item.month1 }}</td>
                          <td style="height: 21px">{{ item.month2 }}</td>
                          <td style="height: 21px">{{ item.month3 }}</td>
                          <td style="height: 21px">{{ item.month4 }}</td>
                          <td style="height: 21px">{{ item.month5 }}</td>
                          <td style="height: 21px">{{ item.month6 }}</td>
                          <td style="height: 21px">{{ item.month7 }}</td>
                          <td style="height: 21px">{{ item.month8 }}</td>
                          <td style="height: 21px">{{ item.month9 }}</td>
                          <td style="height: 21px">{{ item.month10 }}</td>
                          <td style="height: 21px">{{ item.month11 }}</td>
                          <td style="height: 21px">{{ item.month12 }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </a-spin>
            <div class="efficiency bac">
              <div id="Averagelayer"></div>
              <div id="CAMefficiency"></div>
            </div>
            <div class="efficiency bac">
              <div id="OnceEQ"></div>
              <div id="QuadraticEQ"></div>
            </div>
            <a-spin :spinning="mktloading">
              <div class="bac">
                <div>
                  <a-select v-model="orderdayormoth" @change="ordchange" style="margin-left: 15px">
                    <a-select-option value="1"> 日 </a-select-option>
                    <a-select-option value="2"> 月 </a-select-option>
                  </a-select>
                  <a-select v-model="YearDate" style="margin-left: 15px" @change="ordchange" showSearch optionFilterProp="label">
                    <a-select-option v-for="item in years" :key="item.label" :value="item.value" :label="item.label">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                  <span style="font-size: 30px; margin-left: 21%; font-weight: bold; color: #ff9900">市场下至工程分析表</span>
                  <div style="padding: 15px">
                    <a-table
                      :columns="orderdayormoth == '1' ? columns6 : columns7"
                      :dataSource="ordertabledata"
                      :pagination="false"
                      :rowKey="(record, index) => `${index + 1}`"
                      :scroll="{ x: 600, y: 800 }"
                      bordered
                    >
                      <template slot="customCell" slot-scoped="">
                        <div class="rectangle">
                          <div style="display: inline-block; position: relative; right: 38px; top: 22px">类别</div>
                          <div style="display: inline-block; position: relative; left: 38px; top: 4px">时间</div>
                        </div>
                      </template>
                    </a-table>
                  </div>
                </div>
              </div>
            </a-spin>
            <a-spin :spinning="Engineeringloading">
              <div class="bac">
                <div>
                  <a-select v-model="prodayormoth" @change="prochange" style="margin-left: 15px">
                    <a-select-option value="1"> 日 </a-select-option>
                    <a-select-option value="2"> 月 </a-select-option>
                  </a-select>
                  <a-select v-model="YearDate1" style="margin-left: 15px" @change="prochange" showSearch optionFilterProp="label">
                    <a-select-option v-for="item in years" :key="item.label" :value="item.value" :label="item.label">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                  <span style="font-size: 30px; margin-left: 20%; font-weight: bold; color: #ff9900">工程下线订单分析表</span>
                  <div>
                    <div style="padding: 15px">
                      <a-table
                        :columns="prodayormoth == '1' ? columns6 : columns7"
                        :dataSource="Protabledata"
                        :pagination="false"
                        :rowKey="(record, index) => `${index + 1}`"
                        :scroll="{ x: 600, y: 800 }"
                        bordered
                      >
                        <template slot="customCell" slot-scoped="">
                          <div class="rectangle">
                            <div style="display: inline-block; position: relative; right: 38px; top: 22px">类别</div>
                            <div style="display: inline-block; position: relative; left: 38px; top: 4px">时间</div>
                          </div>
                        </template>
                      </a-table>
                    </div>
                  </div>
                </div>
              </div>
            </a-spin>
            <a-spin :spinning="Amalgamationloading">
              <div class="bac">
                <a-select v-model="Dailyandmonthly" style="margin-left: 15px; margin-top: 15px" @change="dychange">
                  <a-select-option value="1"> 日 </a-select-option>
                  <a-select-option value="2"> 月 </a-select-option>
                </a-select>
                <a-select v-model="YearDate2" style="margin-left: 15px" @change="dychange" showSearch optionFilterProp="label">
                  <a-select-option v-for="item in years" :key="item.label" :value="item.value" :label="item.label">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
                <span style="font-size: 30px; margin-left: 21%; font-weight: bold; color: #ff9900">每日合拼达成</span>
                <div style="padding: 15px">
                  <a-table
                    :columns="Dailyandmonthly == '1' ? Hpcolumnsmonth : Hpcolumnsyear"
                    :dataSource="comtabledata"
                    :pagination="false"
                    :rowKey="(record, index) => `${index + 1}`"
                    :scroll="{ x: 600, y: 800 }"
                    bordered
                  >
                  </a-table>
                </div>
                <div id="Mergeanalysis"></div>
              </div>
            </a-spin>
          </div></div
      ></a-tab-pane>
      <a-tab-pane key="2" tab="第二页" force-render> <analysis-two></analysis-two> </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import * as echarts from "echarts/core"; // 引入echarts
import { TooltipComponent, LegendComponent } from "echarts/components"; // 引入提示框和图例组件
import { PieChart } from "echarts/charts"; // 引入饼图
import { LabelLayout } from "echarts/features"; // 引入标签布局
import { SVGRenderer } from "echarts/renderers"; // 引入svg渲染器
echarts.use([TooltipComponent, LegendComponent, PieChart, SVGRenderer, LabelLayout]); // 使用echarts
import moment from "moment";
import {
  factroyList,
  ppEOrderdatastatisticsv2,
  ppEOrderdatastatisticsv1,
  ppEOrderdatastatistics,
  ppEOrderdatastatisticsv3,
  ppEOrderyeardatastatistics,
  ppEOrdereQDatastatistics,
  camdatastatistics,
  datastatisticsv1,
  datastatisticsv2,
  mkt2Ppedatav1,
  mkt2Ppedatav2,
  ppeofflinedatav1,
  ppeofflinedatav2,
} from "@/services/analysis";
import AnalysisTwo from "@/pages/dashboard/analysis/AnalysisTwo";
export default {
  name: "EngineeringAnalysis",
  components: { AnalysisTwo },
  data() {
    return {
      activeKey: "1",
      Amalgamationloading: false,
      Engineeringloading: false,
      mktloading: false,
      Receptionloading: false,
      Statisticsloading: false,
      FactoryId: "", // 工厂ID
      factroyList: [], // 工厂列表
      Orderingdata: {},
      Timetype: "day",
      dateString: [],
      PlaceanorderData: [],
      ReceptionData: [],
      comtabledata: [],
      Dailyandmonthly: "1",
      YearDate: moment().format("YYYY"),
      YearDate1: moment().format("YYYY"),
      YearDate2: moment().format("YYYY"),
      date: moment().format("YYYY-MM-DD"),
      Hpcolumnsmonth: [
        {
          title: "日期",
          dataIndex: "name",
          align: "center",
          width: 150,
          fixed: "left",
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 80,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 80,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 80,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 80,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 80,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 80,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 80,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 80,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 80,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 80,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 80,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 80,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 80,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 80,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 80,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 80,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 80,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 80,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 80,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      Hpcolumnsyear: [
        {
          title: "日期",
          dataIndex: "name",
          align: "center",
          width: 150,
        },
        {
          title: "1",
          dataIndex: "month1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "month2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "month3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "month4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "month5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "month6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "month7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "month8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "month9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "month10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "month11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "month12",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      columns6: [
        {
          slots: { title: "customCell" },
          dataIndex: "name",
          align: "center",
          width: 200,
          fixed: "left",
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 80,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 80,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 80,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 80,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 80,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 80,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 80,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 80,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 80,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 80,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 80,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 80,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 80,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 80,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 80,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 80,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 80,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 80,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 80,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      columns7: [
        {
          dataIndex: "name",
          align: "center",
          width: 150,
          slots: { title: "customCell" },
        },
        {
          title: "1",
          dataIndex: "month1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "month2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "month3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "month4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "month5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "month6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "month7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "month8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "month9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "month10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "month11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "month12",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      orderdayormoth: "1",
      prodayormoth: "1",
      ordertabledata: [],
      Protabledata: [],
      years: [], // 用于存储年份的数组
    };
  },
  created() {
    const currentYear = new Date().getFullYear(); // 获取当前年份
    for (let year = currentYear; year >= 2024; year--) {
      this.years.push({ value: year.toString(), label: year.toString() }); // 从当前年份递减到2024年，并转换为字符串
    }
  },
  mounted() {
    this.getFactoryList();
    this.Combiningrate();
    this.EQrate();
    this.getorderdata();
    this.getprodata();
    this.Camefficiency();
    this.Getthetotal("day");
    this.Totalareawithvariety();
  },

  methods: {
    tabClick(key) {
      this.activeKey = key;
    },
    Factorychange() {
      if (!this.FactoryId) {
        this.FactoryId = "";
      }
      this.Getthetotal(this.Timetype);
      this.Totalareawithvariety();
      this.dychange();
      this.EQrate();
      this.Camefficiency();
      this.prochange();
      this.ordchange();
    },
    Camefficiency() {
      let CAMdata = [];
      camdatastatistics(this.FactoryId).then(res => {
        if (res.code) {
          for (let index = 0; index < res.data.offlineAverageNumdtos.length; index++) {
            res.data.offlineAverageNumdtos[index].dtos.forEach(ite => {
              CAMdata.push(ite.value);
            });
          }
        }
        CAMdata.pop();
        this.Linechart("CAMefficiency", CAMdata, "CAM制作人均效率", "");
      });
    },
    prochange() {
      this.Protabledata = [];
      if (this.prodayormoth == "1") {
        this.getprodata();
      } else {
        this.getprodata1();
      }
    },
    getprodata() {
      this.Protabledata = [];
      let date;
      if (this.YearDate1 && this.YearDate1 !== moment().format("YYYY")) {
        date = moment(`${this.YearDate1}-${moment().format("MM")}-01`)
          .endOf("month")
          .format("YYYY-MM-DD");
      } else {
        // 否则设置为当前日期
        date = moment().format("YYYY-MM-DD");
      }
      this.fetchOrderData(date, "day", ppeofflinedatav1, "pro");
    },
    getprodata1() {
      this.Protabledata = [];
      let date;
      if (this.YearDate1 && this.YearDate1 !== moment().format("YYYY")) {
        date = `${this.YearDate1}-12-31`;
      } else {
        date = moment().format("YYYY-MM-DD");
      }
      this.fetchOrderData(date, "month", ppeofflinedatav2, "pro");
    },
    ordchange() {
      this.ordertabledata = [];
      if (this.orderdayormoth === "1") {
        this.getorderdata();
      } else {
        this.getorderdata1();
      }
    },
    getorderdata() {
      this.ordertabledata = [];
      let date;
      if (this.YearDate && this.YearDate !== moment().format("YYYY")) {
        date = moment(`${this.YearDate}-${moment().format("MM")}-01`)
          .endOf("month")
          .format("YYYY-MM-DD");
      } else {
        date = moment().format("YYYY-MM-DD");
      }
      this.fetchOrderData(date, "day", mkt2Ppedatav1, "mkt");
    },
    getorderdata1() {
      this.ordertabledata = [];
      let date;
      if (this.YearDate && this.YearDate !== moment().format("YYYY")) {
        date = `${this.YearDate}-12-31`;
      } else {
        date = moment().format("YYYY-MM-DD");
      }
      this.fetchOrderData(date, "month", mkt2Ppedatav2, "mkt");
    },
    fetchOrderData(date, period, apife, type) {
      const apiFunction = apife;
      if (type == "mkt") {
        this.mktloading = true;
      } else {
        this.Engineeringloading = true;
      }
      apiFunction(date, this.FactoryId)
        .then(res => {
          if (res.code) {
            if (type == "mkt") {
              this.ordertabledata = this.processOrderData(res.data, period);
            } else {
              this.Protabledata = this.processOrderData(res.data, period);
            }
          }
        })
        .finally(() => {
          this.mktloading = false;
          this.Engineeringloading = false;
        });
    },
    processOrderData(data, period) {
      const keys = [
        "offlineAreadtos",
        "offlineNumdtos",
        "offlinePCTANdtos",
        "offlineNewNumdtos",
        "offlineNewPCTANdtos",
        "offlineNewAreadtos",
        "offlineNewPCTAreadtos",
        "offlineReNumdtos",
        "offlineRePCTANdtos",
        "offlineReAreadtos",
        "offlineRePCTAreadtos",
        "offline2LayersNumdtos",
        "offline2LPCTANdtos",
        "offline2LayersAreadtos",
        "offline2LPCTAreadtos",
        "offline4LayersNumdtos",
        "offline4LPCTANdtos",
        "offline4LayersAreadtos",
        "offline4LPCTAreadtos",
        "offline6LayersNumdtos",
        "offline6LPCTANdtos",
        "offline6LayersAreadtos",
        "offline6LPCTAreadtos",
        "offline10LayersNumdtos",
        "offline10LPCTANdtos",
        "offline10LayersAreadtos",
        "offline10LPCTAreadtos",
        "offlineHDINumdtos",
        "offlineHDIPCTANdtos",
        "offlineHDIAreadtos",
        "offlineHDIPCTAreadtos",
        "offlineHVANumdtos",
        "offlineHVAPCTANdtos",
        "offlineHVAAreadtos",
        "offlineHVAPCTAreadtos",
        "offlineJiajiNumdtos",
        "offlineJiajiPCTANdtos",
        "offlineJiajiAreadtos",
        "offlineJiajiPCTAreadtos",
        "offlineAverageLayerdtos",
      ];

      return keys.map(key => {
        const dataItem = data[key][0];
        return this.generateDayData(dataItem, period, period === "day" ? "dDtos" : "dtos");
      });
    },
    EQrate() {
      let EQdata = [];
      let MultipleEQs = [];
      ppEOrdereQDatastatistics(this.FactoryId).then(res => {
        if (res.code) {
          for (let index = 0; index < res.data.offlineEQNumdtos.length; index++) {
            res.data.offlineEQNumdtos[index].dtos.forEach(ite => {
              EQdata.push(ite.value);
            });
          }
          for (let index = 0; index < res.data.offlineEQ2Numdtos.length; index++) {
            res.data.offlineEQ2Numdtos[index].dtos.forEach(ite => {
              MultipleEQs.push(ite.value);
            });
          }
        }
        EQdata.pop();
        MultipleEQs.pop();
        this.Linechart("OnceEQ", EQdata, "一次问客率", "%");
        this.Linechart("QuadraticEQ", MultipleEQs, "二次问客率", "%");
      });
    },
    Totalareawithvariety() {
      let offlineAreadtos = [];
      let offlineNumdtos = [];
      let receiveAreadtos = [];
      let receiveNumdtos = [];
      let offlineAvgLayerdtos = [];
      this.Receptionloading = true;
      ppEOrderyeardatastatistics(this.FactoryId)
        .then(res => {
          if (res.code) {
            // "总下线面积(㎡)", "总下线品种(款)"
            for (let index = 0; index < res.data.offlineAreadtos.length; index++) {
              res.data.offlineAreadtos[index].dtos.forEach(ite => {
                offlineAreadtos.push(ite.value);
              });
            }
            for (let index = 0; index < res.data.offlineNumdtos.length; index++) {
              res.data.offlineNumdtos[index].dtos.forEach(ite => {
                offlineNumdtos.push(ite.value);
              });
            }
            offlineAreadtos.pop();
            offlineNumdtos.pop();
            this.Totalorderplacement("Reception", offlineAreadtos, offlineNumdtos, "总下线面积(㎡)", "总下线品种(款)");
            this.PlaceanorderData = [
              this.generateDayData(res.data.offlineAreadtos[0], "month", "dtos"),
              this.generateDayData(res.data.offlineNumdtos[0], "month", "dtos"),
            ];
            // "总接收面积(㎡)", "总接收品种(款)"
            for (let index = 0; index < res.data.receiveAreadtos.length; index++) {
              res.data.receiveAreadtos[index].dtos.forEach(ite => {
                receiveAreadtos.push(ite.value);
              });
            }
            for (let index = 0; index < res.data.receiveNumdtos.length; index++) {
              res.data.receiveNumdtos[index].dtos.forEach(ite => {
                receiveNumdtos.push(ite.value);
              });
            }
            receiveAreadtos.pop();
            receiveNumdtos.pop();
            this.Totalorderplacement("Placeanorder", receiveAreadtos, receiveNumdtos, "总接收面积(㎡)", "总接收品种(款)");
            this.ReceptionData = [
              this.generateDayData(res.data.receiveAreadtos[0], "month", "dtos"),
              this.generateDayData(res.data.receiveNumdtos[0], "month", "dtos"),
            ];
            //平均订单层数数据
            for (let index = 0; index < res.data.offlineAvgLayerdtos.length; index++) {
              res.data.offlineAvgLayerdtos[index].dtos.forEach(ite => {
                offlineAvgLayerdtos.push(ite.value);
              });
            }
            offlineAvgLayerdtos.pop();
            this.Linechart("Averagelayer", offlineAvgLayerdtos, "订单平均层数", "");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.Receptionloading = false;
        });
    },
    intervalChange(value, dateString) {
      this.dateString = dateString;
      this.Getthetotal("interval");
    },
    generateDayData(data, time, dtos) {
      return {
        name: data.name,
        ...data[dtos].reduce((acc, dayData, index) => {
          if (!dayData[time]) {
            acc.Summary = dayData.value;
          } else {
            acc[time + (index + 1)] = dayData.value;
          }
          acc.name = data.name;
          return acc;
        }, {}),
      };
    },
    dychange() {
      this.comtabledata = [];
      if (this.Dailyandmonthly == "1") {
        this.Combiningrate();
      } else {
        this.Combiningrate1();
      }
    },
    //平均子板数
    Combiningrate() {
      let averagebar = [];
      let averageline = [];
      this.Combinationrate(averageline, averagebar, "", "", []);
      let date;
      if (this.YearDate2 && this.YearDate2 !== moment().format("YYYY")) {
        date = moment(`${this.YearDate2}-${moment().format("MM")}-01`)
          .endOf("month")
          .format("YYYY-MM-DD");
      } else {
        // 否则设置为当前日期
        date = moment().format("YYYY-MM-DD");
      }
      this.Amalgamationloading = true;
      datastatisticsv1(date, this.FactoryId)
        .then(res => {
          if (res.code) {
            this.comtabledata = [
              this.generateDayData(res.data.waitHPNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.zhHPNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.offlineNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.hpNumdtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.hpRatedtos[0], "day", "monthDtos"),
              this.generateDayData(res.data.avgHPzbNumdtos[0], "day", "monthDtos"),
            ];
            res.data.avgHPzbNumdtos[0].monthDtos.forEach(ite => {
              averagebar.push(Number(ite.value));
            });
            res.data.hpRatedtos[0].monthDtos.forEach(ite => {
              averageline.push(Number(ite.value));
            });
            averageline.pop();
            averagebar.pop();
            let xdata = Array.from({ length: 31 }, (_, i) => i + 1 + "日");
            this.Combinationrate(averageline, averagebar, "当日合拼平均子板数", "合拼率", xdata);
          }
        })
        .finally(() => {
          this.Receptionloading = false;
          this.Amalgamationloading = false;
        });
    },
    //月
    Combiningrate1() {
      let averagebar = [];
      let averageline = [];
      this.Combinationrate(averageline, averagebar, "", "", []);
      let date;
      if (this.YearDate2 && this.YearDate2 !== moment().format("YYYY")) {
        date = `${this.YearDate2}-12-31`;
      } else {
        date = moment().format("YYYY-MM-DD");
      }
      this.Amalgamationloading = true;
      datastatisticsv2(date, this.FactoryId)
        .then(res => {
          if (res.code) {
            res.data.avgHPzbNumdtos[0].yearDtos.forEach(ite => {
              averagebar.push(Number(ite.value));
            });
            res.data.hpRatedtos[0].yearDtos.forEach(ite => {
              averageline.push(Number(ite.value));
            });
            averageline.pop();
            averagebar.pop();
            this.comtabledata = [
              this.generateDayData(res.data.waitHPNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.zhHPNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.offlineNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.hpNumdtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.hpRatedtos[0], "month", "yearDtos"),
              this.generateDayData(res.data.avgHPzbNumdtos[0], "month", "yearDtos"),
            ];
            let xdata = Array.from({ length: 12 }, (_, i) => i + 1 + "月");
            this.Combinationrate(averageline, averagebar, "当月合拼平均子板数", "合拼率", xdata);
          }
        })
        .finally(() => {
          this.Receptionloading = false;
          this.Amalgamationloading = false;
        });
    },
    Combinationrate(linedata, bardata, leg1, leg2, xdata) {
      const option = {
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            var seriesType = params.seriesType; // 获取数据点所属的系列类型
            if (seriesType === "line") {
              return params.seriesName + "<br/>" + params.name + "日 : " + params.value + "%";
            } else if (seriesType === "bar") {
              return params.seriesName + "<br/>" + params.name + "日: " + params.value;
            }
          },
        },
        xAxis: [{ type: "category", data: xdata }],
        yAxis: [
          {
            type: "value",

            data: [],
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
            },
          },
          {
            type: "value",
            data: [],
            axisLine: { show: false },
            position: "right", // 将右边的 y 轴显示在右侧
            axisTick: { show: false },
            axisLabel: {
              formatter: "{value} %",
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
              axisPointer: { snap: true },
            },
          },
        ],
        grid: {
          left: "3%", // 左边距
          right: "3%", // 右边距
          top: "25%", // 上边距
          bottom: "10%", // 下边距
        },
        legend: [
          {
            data: [leg1, leg2],
            textStyle: {
              color: "rgb(121,128,137)",
              fontSize: 12,
            },
            orient: "horizontal",
            x: "600",
            y: "0",
            itemWidth: 20,
            itemHeight: 10,
          },
        ],
        color: ["rgb(254,146,123)", "rgb(123,126,223)"],
        series: [
          {
            name: leg1,
            type: "bar",
            data: bardata,
            barWidth: "50%",
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 10,
              },
            },
            smooth: false,
            symbol: "circle",
            symbolSize: 8,
          },
          {
            name: leg2,
            yAxisIndex: 1, // 使用右侧的 y 轴
            type: "line",
            data: linedata,
            label: {
              formatter: function (params) {
                return params.value + "%";
              },
              show: true,
              position: "top",
              textStyle: {
                fontSize: 10,
              },
            },
            smooth: false,
            symbol: "circle",
            symbolSize: 8,
          },
        ],
      };
      let chartDom = document.getElementById("Mergeanalysis");
      let myChart = echarts.init(chartDom);
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    //环形图
    createDonutChart(chartId, data) {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
          },
          legend: {
            // 设置图例的布局方式为水平，图标为矩形，图标大小为8*8，位置在中心顶部，数据为data中的name
            orient: "horizontal",
            icon: "rect",
            itemHeight: 8,
            itemWidth: 8,
            x: "center",
            y: "0%",
            align: "left",
            data: data.map(item => item.name),
            textStyle: {
              color: "rgb(121,128,137)",
              fontSize: 10,
            },
          },
          color: ["rgb(72,116,203)", "rgb(242,186,2)", "rgb(238,130,47)", "rgb(117,189,66)"],
          series: [
            {
              name: "",
              type: "pie",
              radius: ["40%", "80%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              center: ["50%", "50%"],
              label: {
                normal: {
                  position: "inner",
                  show: true,
                  textStyle: {
                    fontWeight: 600,
                    fontSize: 8,
                  },
                  formatter: "{d}" + "%",
                },
              },
              minAngle: 50 || 0,
              data: data,
            },
          ],
        };
        let chartDom = document.getElementById(chartId);
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    //总接收与总下单  柱状+折线
    Totalorderplacement(chartId, orderingArea, orderVariety, seriesName1, seriesName2) {
      this.$nextTick(() => {
        // 在下次 DOM 更新循环结束之后执行延迟回调
        const option = {
          tooltip: {
            trigger: "item", // 设置提示框触发类型
            formatter: function (params) {
              var seriesType = params.seriesType; // 获取数据点所属的系列类型
              if (seriesType === "line") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value;
              } else if (seriesType === "bar") {
                return params.seriesName + "<br/>" + params.name + " : " + params.value;
              }
            },
          },
          xAxis: [
            {
              type: "category",
              data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                textStyle: {
                  color: "rgba(121, 128, 137, 0)",
                  fontSize: 10,
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              data: [],
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "rgb(121,128,137)",
                  fontSize: 10,
                },
                axisPointer: { snap: true },
              },
            },
          ],
          grid: {
            left: "20%",
          },
          color: ["rgb(254,146,123)", "rgb(68,189,220)"],
          legend: [
            {
              data: [seriesName1, seriesName2],
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 10,
              },
              orient: "vertical",
              x: "53",
              y: "235",
              itemWidth: 20,
              itemHeight: 10,
            },
            {
              data: [seriesName1, seriesName2],
              textStyle: {
                color: "rgb(121,128,137)",
                fontSize: 12,
              },
              orient: "horizontal",
              y: "0",
              itemWidth: 20,
              itemHeight: 10,
            },
          ],
          series: [
            {
              name: seriesName1,
              type: "bar",
              barWidth: "50%",
              data: orderingArea,
              itemStyle: {
                borderColor: "rgb(253,94,58)",
                borderWidth: 2,
                borderType: "solid",
              },
            },
            {
              data: orderVariety, // 设置折线图数据
              type: "line", // 设置图表类型为折线图
              name: seriesName2,
              label: {
                show: false,
                position: "top", // 设置标签位置为顶部
                textStyle: {
                  fontSize: 14,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
          ],
        };
        let chartDom = document.getElementById(chartId);
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    //订单平均层数数据 cam效率 问客率
    Linechart(chartId, data, seriesName, proportion) {
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              return params.seriesName + "<br/>" + params.name + " : " + params.value + proportion;
            },
          },
          xAxis: {
            type: "category",
            data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
            axisLabel: {
              textStyle: {
                fontSize: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "gray", // 设置 x 轴轴线的颜色为灰色
              },
            },
            axisTick: {
              lineStyle: {
                color: "gray", // 设置 x 轴坐标刻度线的颜色为灰色
              },
            },
          },
          yAxis: [
            {
              type: "value",
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                formatter: "{value} " + proportion,
                textStyle: {
                  color: "rgb(121,128,137)",
                  fontSize: 10,
                },
                axisPointer: { snap: true },
              },
            },
          ],
          color: ["rgb(68,189,220)"],
          series: [
            {
              name: seriesName,
              data: data,
              type: "line",
              label: {
                formatter: function (params) {
                  return params.value + proportion;
                },
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 10,
                },
              },
              smooth: false,
              symbol: "circle",
              symbolSize: 8,
            },
          ],
          legend: {
            data: [seriesName], // 确保这里的名称与 series.name 一致
            textStyle: {
              color: "rgb(121,128,137)",
              fontSize: 12,
            },
          },
        };
        let chartDom = document.getElementById(chartId);
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      });
    },
    //获取总数据 接受与下线订单数据
    Getthetotal(type) {
      let date = this.date;
      let Enddate = "";
      if (type == "interval") {
        date = this.dateString[0];
        Enddate = this.dateString[1];
      } else {
        this.dateString = [];
      }
      this.Timetype = type;
      let dateType = "";
      switch (type) {
        case "day":
          dateType = 0;
          break;
        case "week":
          dateType = 1;
          break;
        case "month":
          dateType = 2;
          break;
        case "interval":
          dateType = 3;
          break;
        default:
          break;
      }
      this.Statisticsloading = true;
      ppEOrderdatastatistics(date, this.FactoryId, dateType, Enddate)
        .then(res => {
          if (res.code) {
            this.Orderingdata = res.data;
            this.createDonutChart("OfflineLayertype", [
              { value: this.Orderingdata.lay2offlinenum, name: "双面板" },
              { value: this.Orderingdata.lay4offlinenum, name: "四层板" },
              { value: this.Orderingdata.lay6offlinenum, name: "4-8层板" },
              { value: this.Orderingdata.lay8Upofflinenum, name: " 8层以上" },
            ]);

            this.createDonutChart("OfflineOrdertype", [
              { value: this.Orderingdata.newOrderofflinenum, name: "新单" },
              { value: this.Orderingdata.reOrderofflinenum, name: "返单" },
              { value: this.Orderingdata.reOrderAlterofflinenum, name: "返单更改" },
            ]);

            this.createDonutChart("OfflineHDItype", [
              { value: this.Orderingdata.hdiofflinenum, name: "HDI" },
              { value: this.Orderingdata.notHdiofflinenum, name: "其他" },
            ]);

            this.createDonutChart("OfflineHVAtype", [
              { value: this.Orderingdata.hvaofflinenum, name: "HVA" },
              { value: this.Orderingdata.notHvaofflinenum, name: "其他" },
            ]);
            this.createDonutChart("receiveLayertype", [
              { value: this.Orderingdata.lay2Receivenum, name: "双面板" },
              { value: this.Orderingdata.lay4Receivenum, name: "四层板" },
              { value: this.Orderingdata.lay6Receivenum, name: "4-8层板" },
              { value: this.Orderingdata.lay8UpReceivenum, name: " 8层以上" },
            ]);

            this.createDonutChart("receiveOrdertype", [
              { value: this.Orderingdata.newOrderReceivenum, name: "新单" },
              { value: this.Orderingdata.reOrderReceivenum, name: "返单" },
              { value: this.Orderingdata.reOrderAlterReceivenum, name: "返单更改" },
            ]);

            this.createDonutChart("receiveHDItype", [
              { value: this.Orderingdata.hdiReceivenum, name: "HDI" },
              { value: this.Orderingdata.notHdiReceivenum, name: "其他" },
            ]);

            this.createDonutChart("receiveHVAtype", [
              { value: this.Orderingdata.hvaReceivenum, name: "HVA" },
              { value: this.Orderingdata.notHvaReceivenum, name: "其他" },
            ]);
          }
        })
        .finally(() => {
          this.Statisticsloading = false;
        });
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    getFactoryList() {
      factroyList().then(res => {
        if (res.code) {
          this.factroyList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.rectangle {
  position: relative;
  width: 195px;
  height: 39px;
  background: transparent; /* 背景透明 */
  overflow: hidden; /* 防止伪元素超出边界 */
}

.rectangle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(192deg, transparent 49%, #818181, transparent 51%);
}
/deep/ .ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
.DonutChart {
  display: grid;
  justify-content: space-between;
  grid-template-columns: 1fr 1fr 1fr 1fr; // 设置网格布局，每列宽度为1fr
  align-items: center;
  justify-items: center;
  margin-bottom: 0 !important;
  margin-left: 0 !important;
  div {
    width: 250px;
    height: 160px;
  }
}
#Placeanorder {
  width: 820px;
  height: 282px;
}
#Reception {
  width: 820px;
  height: 282px;
}
#Mergeanalysis {
  width: 100%;
  height: 282px;
}
.TotalOrder {
  // 设置TotalOrder的显示方式为网格布局，水平间距为space-between，每列宽度为1fr，水平居中对齐，垂直居中对齐
  display: grid;
  justify-content: space-between;
  grid-template-columns: 1fr 1fr;
  justify-items: center;
  align-items: center;
}
.efficiency {
  display: grid;
  justify-content: space-between;
  grid-template-columns: 1fr 1fr;
  justify-items: center;
  align-items: center;
  div {
    width: 820px;
    height: 282px;
  }
}
@media (min-width: 1810px) {
  .Statistics {
    grid-template-columns: 550px 1fr;
  }
  .DonutChart {
    margin-left: 0px !important;
  }
}
@media (max-width: 1810px) {
  .DonutChart {
    grid-template-columns: 1fr 1fr;
    margin-left: 12px !important;
  }
}
@media (max-width: 533px) {
  .DonutChart {
    grid-template-columns: 1fr;
  }
}
@media (max-width: 1847px) {
  .TotalOrder {
    grid-template-columns: 1fr;
  }
  .efficiency {
    grid-template-columns: 1fr;
  }
}
.Orderingsituation {
  display: grid;
  grid-template-columns: 1fr 1fr;
}
.Statistics {
  display: grid;
}
.bac {
  margin: 12px 12px;
  border: 1px solid green;
}
.labelSTY > div {
  background: rgb(72, 116, 203);
  height: 50px;
  line-height: 48px;
  text-align: center;
  color: #ffffff;
  border-radius: 16px;
  font-size: 17px;
  margin: 10px;
}
.engineering-analysis {
  background: white;
  /deep/.ant-table-thead > tr > th,
  /deep/.ant-table-tbody > tr > td {
    padding: 4px;
    overflow-wrap: break-word;
  }
  /deep/.ant-select-selection--single {
    position: relative;
    height: 32px;
    cursor: pointer;
    width: 150px;
    border: #ff9900 1px solid;
  }
  /deep/.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
    border-color: #ff9900 !important;
  }
  /deep/.ant-input {
    border: #ff9900 1px solid;
  }
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
  .header {
    width: 100%;
    padding-left: 8px;
    margin-top: 8px;
    /deep/.ant-select {
      width: 100%;
    }
    /deep/.ant-col-1 {
      width: 70px;
    }
    /deep/.ant-col-3 {
      width: 210px;
    }
  }
}
.choose {
  background-color: #ff9900;
  border: #ff9900 1px solid;
  color: #ffffff;
}
.unchoose {
  background-color: rgb(250, 250, 250);
  border: #ff9900 1px solid;
  color: #ff9900;
}
</style>
