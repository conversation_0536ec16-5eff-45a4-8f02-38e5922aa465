<template>
  <a-spin :spinning="spinning">
  <div class="orderDetail">
    <order-action
        :editFlag="editFlag"
        @editInfo="editInfo"
        @dataSave="dataSave"
    ></order-action>
    <a-tabs default-active-key="1" >
      <a-tab-pane key="1" tab="PCB订单详情" >
        <order-info ref="editForm" :editFlag="editFlag" :showData="showData"></order-info>
      </a-tab-pane>      
      <a-tab-pane key="2" tab="操作日志"  >
        <a-table 
        :rowKey="'id'"
        :columns="columns1" 
        :pagination= false        
        :dataSource='viewLogData' 
        :scroll='{y:672}'
        :class="'viewInfo'"
        >
        
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="3" tab="工程问客" disabled>
        Content of Tab Pane 3
      </a-tab-pane>
    </a-tabs>
    
  </div>
  </a-spin>
</template>

<script>
import OrderAction from "@/pages/mkt/moduleInput/OrderAction";
import OrderInfo from "@/pages/mkt/moduleInput/OrderInfo";
import {getOrderInfo, updateOrderInfo,orderLog,getOrderWF} from "@/services/mkt/orderInfo";
const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 40,    
  },
  {
    title: '操作时间',
    dataIndex: 'createTime',
    align: "center",
    width: 100,    
  },
  {
    title: '操作详情',
    dataIndex: 'content',
    align: "left",
    width:700,    
  },
  
]
export default {
  name: "OrderDetailEX",
  components: {OrderInfo, OrderAction,},
  data(){
    return {
      editFlag:false,
      showData:{},
      spinning:false,
      columns1,
      viewLogData:[],
      add:false,
      
    }
  },
  created() {
    this.getDetailInfo()
    this.getViewLog()
  },
  watch:{
    watch:{
    'pagination':{
      handler(val){
        console.log(val)
      }
    }
  },
  },
  methods: {    
    // 参数信息
    getDetailInfo(){      
      let id = this.$route.query.id
      getOrderInfo(id).then(res => {
        this.spinning = true        
        if (res.code) {
          res.data.holeDensity = res.data.holeDensity ? res.data.holeDensity:'0'
          this.showData = res.data 
          let arr =this.showData.pinBanType.toLowerCase().split('x')
          this.$set(this.showData,'showNum',arr[0]*arr[1]*this.showData.num)  
          let arr1 =  this.showData.processEdges.split(':')
          if(arr1[0] == 'none'){
             arr1[0] =  '无'
          }else if(arr1[0] == 'updown'){
            arr1[0] =  '上下方向'
          }else if(arr1[0] == 'leftright'){
             arr1[0] =  '左右方向'
          }else if(arr1[0] == 'both'){
             arr1[0] =  '四边方向'
          }else if(arr1[0] == ''){
             arr1[0] =  ''
          }
          this.showData.processEdges = arr1[0]+ ':' + arr1[1]
          let w ; // 拼版宽度
          let h ; // 拼版高度
          if (res.data.vCut.indexOf('锣槽') == -1 ){
            if(arr1[0] == '左右方向' ){
              w =  res.data.boardWidth*arr[1] + + arr1[1]*2 
              h =  res.data.boardHeight*arr[0]
            }else if(arr1[0] =='上下方向' ){
              w =  res.data.boardWidth*arr[1]
              h =  res.data.boardHeight*arr[0] + arr1[1]*2 
            }else if(arr1[0] == '四边方向' ){
              w =  res.data.boardWidth*arr[1] + + arr1[1]*2 
              h =  res.data.boardHeight*arr[0] + arr1[1]*2 
            }else if(arr1[0] == '无' || '' ){
              w =  res.data.boardWidth*arr[1]
              h =  res.data.boardHeight*arr[0]
            }
          }else{
            if(arr1[0] == '左右方向'){
              w =  res.data.boardWidth*arr[1] + arr1[1]*2 + (arr[1]-1)*res.data.grooveHeight
              h =  res.data.boardHeight*arr[0] + (arr[0]-1)*res.data.grooveWidth
            }
            if(arr1[0] =='上下方向'){
              w =  res.data.boardWidth*arr[1] + (arr[1]-1)*res.data.grooveHeight
              h =  res.data.boardHeight*arr[0] + arr1[1]*2  + (arr[0]-1)*res.data.grooveWidth
            }
            if(arr1[0] == '四边方向' ){
              w =  res.data.boardWidth*arr[1] + arr1[1]*2 +  (arr[1]-1)*res.data.grooveHeight
              h =  res.data.boardHeight*arr[0] + arr1[1]*2 + (arr[0]-1)*res.data.grooveWidth
            }
            if(arr1[0] == '无' || '' ){
              w =  res.data.boardWidth*arr[1]  + (arr[1]-1)*res.data.grooveHeight
              h =  res.data.boardHeight*arr[0] + (arr[0]-1)*res.data.grooveWidth
            }
          } 
          this.$set(this.showData,'showArea',w*h) 
          console.log('this.showData',this.showData)
        }
      }).finally(()=>{
        this.spinning = false
      })
    
   
      
    },
    editInfo(){
      this.editFlag = !this.editFlag
      this.$refs.editForm.getEditData()
    },     
    dataSave(){
      let params = this.$refs.editForm.formData
      params.boardLayers = Number(params.boardLayers)
      params.innerCopperThickness = Number(params.innerCopperThickness)
      params.copperThickness = Number(params.copperThickness)
      params.vias = Number(params.vias)
      params.goldfinger = Number(params.goldfinger)
      params.impedanceSize = Number(params.impedanceSize)
      params.impedanceReport = Number(params.impedanceReport)
      params.zkTestStrip = Number(params.zkTestStrip)
      params.invoice = Number(params.invoice)
      params.reportMaterial = Number(params.reportMaterial)
      params.camEngineer = Number(params.camEngineer) 
      if(params.processEdges1 == '无'){
        params.processEdges1 =  'none'
      }else if(params.processEdges1 == '上下方向'){
        params.processEdges1 =  'updown'
      }else if(params.processEdges1 == '左右方向'){
        params.processEdges1 =  'leftright'
      }else if(params.processEdges1 == '四边方向'){
        params.processEdges1 =  'both'
      }else if(params.processEdges1 == ''){
        params.processEdges1 =  ''
      }
      params.processEdges = params.processEdges1 + ":" + params.ProcessEdgeWidth  
      params.pinBanType =  params.pinBanType.toLowerCase()
      params.needReportList =  this.$refs.editForm.ReportList.toString()
      this.$delete(params,'status')     
      
      updateOrderInfo(params).then(res=> {
        if (res.code){
          this.editFlag = false;
          this.$message.success(res.message)
          this.getDetailInfo()
          this.getViewLog()
        }else{
           this.$message.error(res.message)
           this.editFlag = false;
        }
      })
    },
    // 查看日志
    getViewLog(){      
      let OrderId = this.$route.query.id
      
        this.spinning = true
        orderLog(OrderId).then(res => {
        if (res.code){  
            this.viewLogData = res.data
          } else {
            this.$message.error(res.message)
          }
      }).finally(()=>{
        this.spinning = false
      })
    
      
    },
    

  }
}
</script>

<style scoped lang="less">
/deep/.ant-form-item-label > label::after{
  margin:0 4px 0 2px;
}
.orderDetail {
  padding: 10px;
  background: #ffffff;
  /deep/ .ant-tabs {
    .viewInfo{
      .ant-table-thead{
        .ant-table-align-left{
          text-align: center!important;;
        }
      }
    }
    margin-top: 10px;
    .ant-tabs-bar{
      margin: 0;
      border-bottom: 1px solid #ccc;
      .ant-tabs-nav-wrap {
        .ant-tabs-ink-bar {
          display: none!important;
        }
      }
      .ant-tabs-tab {
        margin: 0;
        padding: 0 10px;
        border: 1px solid #ccc;
        font-size: 14px;
        height: 34px;
        line-height: 34px;
        border-left: 0;
        font-weight: 500;
        &:nth-child(1){
          border-left: 1px solid #ccc;;
        }
      }
      .ant-tabs-tab-active {
        border-top: 2px solid #f90 !important;
        border-bottom-color:#ffffff ;
        background: #ffffff;

      }

    }
  }
}
</style>