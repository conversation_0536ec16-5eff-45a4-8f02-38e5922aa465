import { request, METHOD } from '@/utils/request'
// 服务器管理
export async function getCombineManageList(params) {
    return request("/api/app/e-mSTPub-combine-manage/combine-manage-list", METHOD.GET, params)
}
// 作业信息管理
export async function getJobinformation(params) {
    return request("/api/app/e-mSTPub-combine-manage/combine-manage-jobinformation", METHOD.GET, params)
}
// 配置参数
export async function getparConfig(params) {
    return request("/api/app/e-mSTPub-combine-manage/par-config", METHOD.GET, params)
}
// 合拼明细清单
export async function getManageData(params) {
    return request("/api/app/e-mSTPub-combine-manage/combine-manage-data-del", METHOD.GET, params)
}




export default {
    getCombineManageList,
    getJobinformation,
    getparConfig,
    getManageData,
}