<!--龙腾报价表单  -->
<template>
  <div class="pdfDom1" style="font-size: 13px">
    <a-button
      v-print="printObj1"
      @click="printpdf"
      type="primary"
      class="printstyle"
      >打印</a-button
    >
    <div
      id="ltreport0100"
      style="
        padding: 25px;
        font-family: 等线;
        color: black;
        font-weight: 600;
        position: relative;
      "
    >
      <div>
        <table
          border="1"
          style="
            text-align: center;
            margin-top: 5px;
            width: 100%;
            border-top: 1px solid black;
            border-left: 1px solid black;
          "
        >
          <thead>
            <tr style="font-size: 15px">
              <td>序号</td>
              <td>日期</td>
              <td>客户物料号</td>
              <td>客户型号</td>
              <td>阻焊颜色</td>
              <td>字符颜色</td>
              <td>成品板厚</td>
              <td>成品铜厚</td>
              <td>产品类型</td>
              <td colspan="3">交货尺寸及形式</td>
              <td style="background-color: #92d050">2024.01.04最新平米价</td>
              <td style="background-color: #92d050">2024.01.04最新单价</td>
              <td>备注</td>
            </tr>
          </thead>
          <tbody class="tbodystyle">
            <tr v-for="(item, index) in LTreportdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.price1 }}</td>
              <td>{{ item.price2 }}</td>
              <td>{{ item.price3 }}</td>
              <td>{{ item.price4 }}</td>
              <td>{{ item.price5 }}</td>
              <td>{{ item.price6 }}</td>
              <td>{{ item.price7 }}</td>
              <td>{{ item.price8 }}</td>
              <td>{{ item.price9 }}</td>
              <td>{{ item.price10 }}</td>
              <td>{{ item.price11 }}</td>
              <td>{{ item.price12 }}</td>
              <td>{{ item.price13 }}</td>
              <td>{{ item.price14 }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["LTreportdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj1: {
        id: "ltreport0100", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.LTreportdata.price.length; index++) {
      if (
        this.LTreportdata.price[index].total &&
        this.LTreportdata.price[index].total != "/"
      ) {
        this.amountto += Number(this.LTreportdata.price[index].total);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.LTreportdata.pcbFileName;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getreportPdf() {
      htmlToPdfa3("ltreport0100", this.LTreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.tbodystyle > tr > td {
  background-color: #ffff00;
}
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.Underline {
  position: relative;
  display: inline-block;
}
.Underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 200px; /* 下划线宽度 */
  height: 2px; /* 下划线高度 */
  background-color: rgb(107, 106, 106); /* 下划线颜色 */
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
  table > thead > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
  table > tbody > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
}
</style>
