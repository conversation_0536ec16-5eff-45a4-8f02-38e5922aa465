import Vue from "vue";
import Router from "vue-router";
import { formatRoutes } from "@/utils/routerUtil";

Vue.use(Router);

// 不需要登录拦截的路由配置
const loginIgnore = {
  names: ["404", "403", "loginNew", "flyingProbe", "loginOA", "impedance", "loginICam", "eqDetails1", "问客详情", "nbook", "costanalysis"], //根据路由名称匹配
  paths: [
    "/login",
    "/AuditParametersMkt",
    "/AuditParameters",
    "/drillStructure",
    "/Goldfinger",
    "/loginpe",
    "/wippe",
    "/userlogin",
    "/usermanagement",
  ], //根据路由fullPath匹配
  /**
   * 判断路由是否包含在该配置中
   * @param route vue-router 的 route 对象
   * @returns {boolean}
   */
  includes(route) {
    return this.names.includes(route.name) || this.paths.includes(route.path);
  },
};
/**
 * 初始化路由实例
 * @param isAsync 是否异步路由模式
 * @returns {VueRouter}
 */
// function initRouter(isAsync) {
//   const options = isAsync ? require('./async/config.async').default : require('./config').default
//   formatRoutes(options.routes)
//   return new Router(options)
// }
function initRouter(isAsync) {
  const options = isAsync ? require("./async/config.async").default : require("./config").default;
  formatRoutes(options.routes);
  const router = new Router(options);
  router.onError(error => {
    // 检查错误信息，如果是加载代码块失败的错误，则执行页面刷新操作
    if (error.message.includes("Loading chunk")) {
      window.location.reload();
    }
  });

  return router;
}

export { loginIgnore, initRouter };
