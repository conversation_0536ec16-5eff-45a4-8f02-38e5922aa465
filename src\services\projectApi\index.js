import { request, METHOD } from "@/utils/request";
// 工程后端获取订单
export function projectBackEndOrderList(params) {
  return request("/api/app/pro-order-list/engineering-backend-list", METHOD.GET, params);
}
//EQ管理列表
export function eqmanagementlist(params) {
  return request("/api/app/pro-order-list/eq-management-list", METHOD.GET, params);
}
export function updateFilepro(params) {
  return request(`/api/app/e-mSEQMain/update-file-pro`, METHOD.POST, params);
}
//EQ管理统计列表
export function managementuserinfo() {
  return request("/api/app/pro-order-list/eq-management-user-info", METHOD.GET);
}
//进入评审时调取接口
export function setreviewinfo(fac, orderNo, businessOrderNo, source, ReviewNo) {
  return request(
    `api/app/e-mSReview-main/set-review-info/${fac}?OrderNo=${orderNo}&BusinessOrderNo=${businessOrderNo}&Source=${source}&ReviewNo=${ReviewNo}`,
    METHOD.POST
  );
}
//问客开始
export function emSEQMaineqStart(Id) {
  return request(`/api/app/e-mSEQMain/e-qStart/${Id}`, METHOD.POST);
}
//邮件信息列表
export function emSEQMaineqRepair(Id) {
  return request(`/api/app/e-mSEQMain/e-qRepair/${Id}`, METHOD.POST);
}
// 查看日志
export function getViewLog(params) {
  return request(`/api/app/pro-order-log?OrderId=${params}`, METHOD.GET);
}
//邮件信息修改保存
export function emSEQMaineqSave(params) {
  return request(`/api/app/e-mSEQMain/e-qSave`, METHOD.POST, params);
}
//邮件发送
export function eqSendmail(Id) {
  return request(`/api/app/e-mSEQMain/e-qSend-mail/${Id}`, METHOD.POST);
}
//市场进入邮件发送
export function eqSendmailmkt(id) {
  return request(`/api/app/e-mSEQMain/${id}/e-qSend-mail-mkt`, METHOD.POST);
}
//问客回退
export function eqBack(Id) {
  return request(`/api/app/e-mSEQMain/e-qBack/${Id}`, METHOD.POST);
}
//EQ发送 问客管理
export function emSEQMaineqSend(Id) {
  return request(`/api/app/e-mSEQMain/e-qSend/${Id}`, METHOD.POST);
}
//EQ发送 市场问客邮件信息
export function eqMktSendMail(params) {
  return request(`/api/app/e-mSEQMain/e-qMkt-send-mail`, METHOD.POST, params);
}

// 工程后端订单详情
export function projectBackEndOrderDetail(Id) {
  return request(`/api/app/engineering-backend/pro-order-par/${Id}`, METHOD.GET);
}

// 工程后端人员清单
export function projectBackEndPeopleList() {
  return request("/api/app/engineering-backend/engineering-backend-user-list", METHOD.GET);
}
// 工程后端作业记录
export function projectBackEndJobInfo(id) {
  return request(`/api/app/engineering-backend/information-transfer/${id}`, METHOD.GET);
}
// 人员对应订单
export function peopleOrderList(params) {
  return request("/api/app/engineering-backend/engineering-backend-user-order-list", METHOD.GET, params);
}

// 后端订单分派
export function projectBackEndAssign(params) {
  return request("/api/app/engineering-backend/send-order", METHOD.POST, params);
}

// 后端订单分派回退
export function projectBackEndAssignBack(id) {
  return request(`/api/app/engineering-backend/back-send/${id}`, METHOD.POST);
}
// 取单
export function TakeOrderList() {
  return request("/api/app/engineering-backend/engineering-backend-order", METHOD.GET);
}

// 返回状态
// export function iIsLeave1 (userid) {
//     return request(`/api/app/e-mSMake-module-no/m-iIs-leave1?userid=${userid}`, METHOD.POST)
// }

// 获取后端取单设置
export function orderRetrievalSettings(params) {
  return request(`/api/app/e-mSTSys-user-assignment-hd/order-retrieval-settings?userid=${params}`, METHOD.GET);
}
// 取单设置
export function RetrievalSettings(params) {
  return request(`/api/app/e-mSTSys-user-assignment-hd/order-retrieval-settings-hd`, METHOD.POST, params);
}

// 退单（回退业务）
export function BackStart(params) {
  return request(`/api/app/engineering-production/back-order`, METHOD.POST, params);
}
// 回退前端
export function FallbackFront(Id, params) {
  return request(`/api/app/engineering-backend/engineering-back-orders/${Id}`, METHOD.GET, params);
}
// 修改信息
export function getModifyInformation(params) {
  return request(`/api/app/engineering-production/modify-information`, METHOD.POST, params);
}
// 注意事项
export async function mattersNeedingAttention(params) {
  return request(`/api/app/engineering-production/matters-needing-attention`, METHOD.POST, params);
}
// 返修记录
export async function getRepairRecord(Id) {
  return request(`/api/app/engineering-production/fix-record/${Id} `, METHOD.GET);
}
// 获取客户信息
export function getCustomerInfo(CustNo, factory, type, businessOrderNo, orderNo) {
  return request(
    `/api/app/order-pre-button/rule-show-info?CustNo=${CustNo}&factory=${factory}&type=${type}&businessOrderNo=${businessOrderNo}&orderNo=${orderNo}`,
    METHOD.GET
  );
}
// 下载CAM文件
export async function DownloadCAMFile(Id) {
  return request(`/api/app/engineering-backend/down-file-cAM/${Id}`, METHOD.GET);
}
// 获取今日做单情况
export async function makeInfo() {
  return request(`/api/app/engineering-backend/make-info`, METHOD.GET);
}
// 下载CAM文件更改订单状态
export async function downFileCAM(Id) {
  return request(`/api/app/engineering-backend/down-file-cAM/${Id} `, METHOD.GET);
}
// 下载前端文件（获取已完成任务）
export async function proQuestLog(params) {
  return request(`/api/app/engineering-backend/pro-quest-log`, METHOD.GET, params);
}

// 获取工序选择项
export async function getFlowSelect(Id) {
  return request(`/api/app/engineering-production/flow-select`, METHOD.GET);
}
// 更新生产备注
export async function postProcess(params) {
  return request(`/api/app/engineering-production/set-process-step-notes`, METHOD.POST, params);
}
// cam 小程序
export async function toCam(params) {
  return request(`http://127.0.0.1:18181/cam`, METHOD.POST, params);
}

export default {
  projectBackEndOrderList,
  projectBackEndOrderDetail,
  projectBackEndPeopleList,
  projectBackEndJobInfo,
  peopleOrderList,
  projectBackEndAssign,
  projectBackEndAssignBack,
  getModifyInformation,
  TakeOrderList,
  BackStart,
  FallbackFront,
  mattersNeedingAttention,
  getRepairRecord,
  getCustomerInfo,
  DownloadCAMFile,
  orderRetrievalSettings,
  RetrievalSettings,
  makeInfo,
  downFileCAM,
  proQuestLog,
  getFlowSelect,
  postProcess,
  toCam,
  eqmanagementlist,
  emSEQMaineqRepair,
  getViewLog,
  emSEQMaineqSave,
  managementuserinfo,
  emSEQMaineqStart,
  emSEQMaineqSend,
  eqSendmail,
  updateFilepro,
};
