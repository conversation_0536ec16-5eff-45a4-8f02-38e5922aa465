import { request, METHOD } from "@/utils/request";

//获取物料参数列表
export async function getOrderInfo(id) {
  return request(`/api/app/pcb-order/${id}/by-id`, METHOD.GET);
}
//新增厂商信息
export async function addPcbMessage(params) {
  return request("/api/app/e-mSTMtr-vendor-no/vendor-no", METHOD.POST, params);
}
//新增物料信息
// export async function addMaterialMessage (params) {
//     return request('/api/app/e-mSTMtr-vendor-no/mater-no', METHOD.POST,params)
// }
export async function addMaterialMessage(params) {
  return request("/api/app/e-mSTMtr-vendor-no/new-mater-no", METHOD.POST, params);
}
//更新物料信息
// export async function putMaterialMessage (params) {
//     return request('/api/app/e-mSTMtr-vendor-no/add-mater-no', METHOD.POST,params)
// }
// export async function putMaterialMessage (params) {
//     return request('/api/app/e-mSTMtr-vendor-no/up-mater-no', METHOD.POST,params)
// }
export async function putMaterialMessage(params) {
  return request("/api/app/e-mSTMtr-vendor-no/up-new-mater-no ", METHOD.POST, params);
}
// export async function putMaterialMessage (params) {
//     return request('/api/app/e-mSTMtr-vendor-no/up-new-mater-no ', METHOD.POST,params)
// }
//新增[添加分类]信息
export async function addfl(params) {
  return request("/api/app/e-mSTMtr-vendor-no/type", METHOD.POST, params);
}
//获取厂商分类[122]/物料类别/进货单位/厂商级别[121]选项
export async function getkind(params) {
  return request("/api/app/e-mSData-class-list/get-class-list", METHOD.POST, params);
}
//获取厂商
export async function matervendornolist() {
  return request("/api/app/e-mSTMtr-vendor-no/mater-vendor-no-list", METHOD.GET);
}
//调取[供应商]选项
export async function getsupplier(params) {
  return request("/api/app/e-mSTMtr-vendor-no/page-list", METHOD.GET, params);
}
//调取[板材类型]下拉框选选项
export async function getPlatetype() {
  return request("/api/app/e-mSTMtr-vendor-no/core-type-list", METHOD.GET);
}
//调取添加类别[对应工厂]选项
export async function getfactory() {
  return request("/api/app/e-mSTMtr-vendor-no/get-factory", METHOD.GET);
}
//获取新增-半固化片-[对应PP类别]选项
export async function getPPtype() {
  return request("/api/app/e-mSTMtr-vendor-no/problem", METHOD.GET);
}
//获取新增-半固化片-[PP类型]选项
export async function getPPcategory() {
  return request("/api/app/e-mSTMtr-vendor-no/pp-list", METHOD.GET);
}
//物料录入删除按钮
export async function softdelmaterno(id) {
  return request(`/api/app/e-mSTMtr-vendor-no/${id}/soft-del-mater-no`, METHOD.POST);
}
//获取新增-半固化片-[TG值]选项
export async function getPPtgvalue() {
  return request("/api/app/e-mSTMtr-vendor-no/pp-tGList", METHOD.GET);
}
//调取添加类别[板材TG类型]选项
export async function getTgtype() {
  return request("/api/app/e-mSTMtr-vendor-no/t-gList", METHOD.GET);
}
//获取[物料类型]选项
export async function getmaterialtype(params) {
  return request(`/api/app/e-mSData-class-list/mtr-type-list?TypeNo=${params}`, METHOD.GET, params);
}
//获取[产品类别]选项
export async function getproducttype() {
  return request("/api/app/e-mSTMtr-vendor-no/category-list", METHOD.GET);
}
//获取顶铜底铜下拉
export async function getcoreozlist() {
  return request("/api/app/e-mSTMtr-vendor-no/core-oz-list", METHOD.GET);
}
//ERP编码工厂下拉
export async function factroylist() {
  return request("/api/app/e-mSTMtr-vendor-no/factroy-list", METHOD.GET);
}
//列表展示与查询
// export async function pageMaterList (params) {
//     return request('/api/app/e-mSTMtr-vendor-no/page-mater-list', METHOD.GET,params)
// }
export async function pageMaterList(params) {
  return request("/api/app/e-mSTMtr-vendor-no/page-mater-list-v2", METHOD.GET, params);
}
export default {
  getOrderInfo,
  addPcbMessage,
  addMaterialMessage,
  putMaterialMessage,
  getkind,
  getmaterialtype,
  getproducttype,
  getPPtype,
  getTgtype,
  getsupplier,
  getPlatetype,
  getfactory,
  getPPcategory,
  getPPtgvalue,
  getcoreozlist,
  factroylist,
};
