<!-- 系统管理-用户管理 -->
<template>
  <a-card>
    <div style="display: flex; min-width: 1670px; background: #ffffff">
      <div style="width: 10%" class="treestyle">
        <a-spin :spinning="spinning" style="width: 100%; height: 100%">
          <!-- :defaultExpandAll="true" -->
          <a-tree
            v-if="departGroupPostDto1.length"
            :replaceFields="{ title: 'name', key: 'levlName' }"
            :show-line="showLine"
            :tree-data="departGroupPostDto1"
            @select="onSelect"
          ></a-tree>
        </a-spin>
      </div>
      <div style="width: 90%">
        <div :class="advanced ? 'search' : null">
          <a-form layout="horizontal">
            <div :class="advanced ? null : 'fold'">
              <a-row>
                <a-col :md="7" :sm="24">
                  <a-form-item label="搜索" :labelCol="{ span: 3 }" :wrapperCol="{ span: 18, offset: 1 }">
                    <a-input v-model="queryParam.filter" placeholder="用户名/邮箱/姓名/手机号" allowClear @keyup.enter.native="refresh" />
                  </a-form-item>
                </a-col>
                <!-- <a-col :md="7" :sm="24">
                  <a-form-item
                    label="角色"
                    :labelCol="{ span: 3 }"
                    :wrapperCol="{ span: 18, offset: 1 }"
                  >
                    <a-select
                      v-model="queryParam.roleName"
                      allowClear
                      @keyup.enter.native="refresh"
                      @change="handleChange"
                    >
                      <a-select-option v-for="r in roles" :key="r.id" :value="r.name">
                        {{ r.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col> -->
                <a-col :md="8" :sm="18">
                  <a-form-item :labelCol="{ span: 1 }" :wrapperCol="{ span: 18, offset: 1 }">
                    <!--                <a-checkbox-->
                    <!--                    v-model="queryParam.loack"-->
                    <!--                >锁定用户</a-checkbox>-->
                    <a-button type="primary" @click="refresh" style="margin-right: 20px">查询</a-button>
                    <a-button @click="$refs.createModal.openModal({}, 'POST')" type="primary" style="margin-right: 20px"> 新建 </a-button>
                    <a-button type="primary" @click="deleteClick" style="margin-right: 20px">删除</a-button>
                    <!-- <a-button                        
                        @click="() => (this.queryParam = {})"
                    >重置</a-button> -->
                  </a-form-item>
                </a-col>

                <!-- <a-col :md="8" :sm="24">
                  <a-form-item
                    label="组织单元"
                    :labelCol="{ span: 5 }"
                    :wrapperCol="{ span: 18, offset: 1 }"
                  >
                    <a-tree-select
                      v-model="queryParam.organizationUnitId"
                      style="width: 100%"
                      :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                      :tree-data="organizations"
                      :replaceFields="replaceFields"
                      placeholder="请选择"
                      tree-default-expand-all
                      allowClear
                    >
                    </a-tree-select>
                  </a-form-item>
                </a-col> -->
              </a-row>
            </div>
          </a-form>
        </div>
        <div class="content">
          <!--      <div class="operator">-->
          <!--        <a-button-->
          <!--          v-if="checkPermission('AbpIdentity.Users.Create')"-->
          <!--          @click="$refs.createModal.openModal({},'POST')"-->
          <!--          type="primary"-->
          <!--          >新建</a-button-->
          <!--        >-->
          <!--      </div>-->

          <div style="width: 100%">
            <standard-table
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :selectedRows.sync="selectedRows"
              @change="handleTableChange"
              :pagination="pagination"
              :loading="loading"
              @selctedData="selctedData"
              :class="dataSource.length ? 'mintable' : ''"
            >
              <span slot="roleName" slot-scope="{ text, record }">
                {{ text }}
                <a-tag color="red" v-if="record.isStatic">系统</a-tag>
                <a-tag color="blue" v-if="record.isDefault">默认</a-tag>
                <a-tag color="blue" v-if="record.isPublic">公开</a-tag>
              </span>
              <span slot="sex" slot-scope="{ text }">{{ text.Sex | sexFilter }}</span>
              <template slot="num" slot-scope="{ index }">
                {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
              </template>
              <template slot="isDeleted" slot-scope="{ record }">
                <a-checkbox :checked="record.isDeleted"></a-checkbox>
              </template>
              <template slot="isSys" slot-scope="{ record }">
                <a-checkbox :checked="record.isSys"></a-checkbox>
              </template>
              <template slot="isIpcbCheck" slot-scope="{ record }">
                <a-checkbox :checked="record.isIpcbCheck"></a-checkbox>
              </template>
              <template slot="IsIpcbCam" slot-scope="{ record }">
                <a-checkbox :checked="record.isIpcbCam"></a-checkbox>
              </template>
              <div slot="action" slot-scope="{ record }">
                <template>
                  <a-dropdown>
                    <a class="ant-dropdown-link" href="#" style="font-weight: 500">
                      操作
                      <a-icon type="down" />
                    </a>
                    <a-menu slot="overlay">
                      <!-- v-if="checkPermission('AbpIdentity.Users.Update')" -->
                      <a-menu-item>
                        <a href="javascript:;" @click="$refs.createModal.openModal(record, 'POST')" style="font-weight: 500; color: #000000">编辑</a>
                      </a-menu-item>
                      <!-- v-if="checkPermission('AbpIdentity.Users.ManagePermissions')" -->
                      <a-menu-item>
                        <a href="javascript:;" @click="$refs.permissionModal.openModal(record)" style="font-weight: 500; color: #000000">权限</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a @click="OrderInheritance(record)" href="javascript:;" style="font-weight: 500; color: #000000">离职继承</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a href="javascript:;" @click="passBtn(record)" style="font-weight: 500; color: #000000">初始密码</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a href="javascript:;" @click="forceUpdate(record.id)" style="font-weight: 500; color: #000000">强制更新</a>
                      </a-menu-item>

                      <!-- v-if="checkPermission('AbpIdentity.Users.Delete')" -->
                      <a-menu-item>
                        <a-popconfirm :title="renderTitle" @confirm="handleDel(record.id)" style="font-weight: 500; color: #000000">
                          <a href="javascript:;">禁用</a>
                        </a-popconfirm>
                      </a-menu-item>
                      <!-- v-if="checkPermission('AbpIdentity.Users.Delete')" -->
                      <a-menu-item>
                        <a-popconfirm :title="renderTitle1" @confirm="Recovery(record.id)" style="font-weight: 500; color: #000000">
                          <a href="javascript:;">启用</a>
                        </a-popconfirm>
                      </a-menu-item>
                    </a-menu>
                  </a-dropdown>
                </template>
              </div>
            </standard-table>
          </div>
        </div>
        <div class="bto"></div>
      </div>
    </div>
    <create-form
      ref="createModal"
      @ok="handleOk"
      :factoryList="factoryList"
      :departGroupPostDto="departGroupPostDto"
      :allData="allData"
      @getDepartGroupPostList="getDepartGroupPostList"
    />
    <permission-form ref="permissionModal" provider-name="U" />
    <a-modal
      title=" 确认弹窗"
      :visible="dataVisibleMode"
      @cancel="reportHandleCancel"
      @ok="handleOkMode"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span style="font-size: 16px">【{{ orderno }}】</span>
      <span style="font-size: 16px">{{ messageMode }}</span>
    </a-modal>
    <a-modal
      title="离职继承"
      :visible="inheritvisble"
      @cancel="reportHandleCancel"
      @ok="inheritok"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form-model ref="ruleForm" :model="formdata" :rules="rules" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
        <a-form-model-item label="离职人员" ref="dimissionUser" prop="dimissionUser">
          <a-input allowClear v-model="formdata.dimissionUser" disabled />
        </a-form-model-item>
        <a-form-model-item label="继承人员" ref="inheritUser" prop="inheritUser">
          <a-select v-model="formdata.inheritUser" showSearch allowClear optionFilterProp="label" style="width: 100%">
            <a-select-option v-for="(item, index) in inheritList" :key="index" :value="item.valueMember" :label="item.text">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!-- <a-modal
            title="修改密码"
            :visible="visibleXG"
            @ok="handle"
            :maskClosable="false"
            @cancel="handleCancel"
    >
      <div style="display: flex;align-items: center;justify-content: center">
        <span>新密码：</span>
        <a-input style="width: 200px;" v-model="password"></a-input>
      </div>
    </a-modal> -->
  </a-card>
</template>

<script>
import { mapState } from "vuex";
import StandardTable from "@/components/table/StandardTable";
import {
  getListWithDetails,
  dimissionuserlist,
  del,
  password,
  delRecovery,
  ForceUpdate,
  setdimissionuser2Otheruser,
  getFactoryList,
  getDepartGroupPostList,
  departGroupPostList,
  delUser,
} from "@/services/identity/user";
import { getAll } from "@/services/identity/role";
import { getOrganizationsAll } from "@/services/identity/organization";
import CreateForm from "@/pages/system/UserList/modules/UserForm";
import PermissionForm from "@/pages/system/components/PermissionForm";
import { checkPermission } from "@/utils/abp";
import Cookie from "js-cookie";
const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "num" },
    width: 45,
    key: "index",
    ellipsis: true,
    fixed: "left",
    align: "center",
  },
  {
    title: "登录名",
    dataIndex: "userName",
    fixed: "left",
    width: 85,
    ellipsis: true,
  },
  {
    title: "姓名",
    dataIndex: "name",
    fixed: "left",
    width: 70,
    ellipsis: true,
  },
  {
    title: "工厂",
    fixed: "left",
    dataIndex: "factoryName",
    width: 90,
    ellipsis: true,
  },
  {
    title: "部门",
    dataIndex: "departmentName",
    width: 70,
    ellipsis: true,
  },
  {
    title: "小组",
    dataIndex: "groupName",
    width: 70,
    ellipsis: true,
  },
  {
    title: "岗位",
    dataIndex: "postName",
    width: 100,
    ellipsis: true,
  },
  {
    title: "邮箱地址",
    dataIndex: "email",
    width: 140,
    ellipsis: true,
  },
  {
    title: "手机号",
    dataIndex: "phoneNumber",
    width: 120,
    ellipsis: true,
  },
  {
    title: "创建时间",
    dataIndex: "creationTime",
    width: 160,
    ellipsis: true,
  },
  {
    title: "最后登录时间",
    dataIndex: "lastLoginTime",
    width: 160,
    ellipsis: true,
    sorter: (a, b) => {
      const time1 = new Date(a.lastLoginTime).getTime();
      const time2 = new Date(b.lastLoginTime).getTime();
      return time1 - time2;
    },
  },
  // {
  //   title: "性别",
  //   dataIndex: "extraProperties",
  //   scopedSlots: { customRender: "sex" },
  // },
  {
    title: "系统管理",
    dataIndex: "isSys",
    scopedSlots: { customRender: "isSys" },
    width: 85,
    ellipsis: true,
    align: "center",
    sorter: (a, b) => {
      if (a.isSys === b.isSys) {
        return 0;
      } else if (a.isSys) {
        return 1;
      } else {
        return -1;
      }
    },
  },
  {
    title: "iPCB审核",
    dataIndex: "isIpcbCheck",
    scopedSlots: { customRender: "isIpcbCheck" },
    width: 90,
    ellipsis: true,
    align: "center",
    sorter: (a, b) => {
      if (a.isIpcbCheck === b.isIpcbCheck) {
        return 0;
      } else if (a.isIpcbCheck) {
        return 1;
      } else {
        return -1;
      }
    },
  },
  {
    title: "iPCBCAM",
    dataIndex: "IsIpcbCam",
    scopedSlots: { customRender: "IsIpcbCam" },
    width: 90,
    ellipsis: true,
    align: "center",
    sorter: (a, b) => {
      if (a.isIpcbCam === b.isIpcbCam) {
        return 0;
      } else if (a.isIpcbCam) {
        return 1;
      } else {
        return -1;
      }
    },
  },
  {
    title: "已禁用",
    scopedSlots: { customRender: "isDeleted" },
    width: 80,
    ellipsis: true,
    align: "center",
  },
  {
    title: "版本",
    dataIndex: "iPCBVerSion",
    width: 80,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      if (a.iPCBVerSion === b.iPCBVerSion) {
        return 0;
      } else if (a.iPCBVerSion) {
        return 1;
      } else {
        return -1;
      }
    },
  },
  {
    title: "最后登录时间",
    dataIndex: "iPCBLoginDate",
    width: 170,
    ellipsis: true,
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: "action" },
    width: 60,
    ellipsis: true,
    fixed: "right",
  },
];
const sexMap = {
  0: {
    name: "Unknown",
    text: "未知",
  },
  1: {
    name: "Man",
    text: "男",
  },
  2: {
    name: "Woman",
    text: "女",
  },
};
let that;
export default {
  name: "UserList",
  components: { StandardTable, CreateForm, PermissionForm },
  inject: ["reload"],
  data() {
    return {
      inheritvisble: false,
      formdata: {
        dimissionUser: "",
        inheritUser: "",
        dimissionUserid: "",
      },
      inheritList: [],
      rules: {
        dimissionUser: [{ required: true, message: "请输入离职人员", trigger: "blur" }],
        inheritUser: [{ required: true, message: "请选择继承人员", trigger: "blur" }],
      },
      spinning: false,
      advanced: true,
      columns: columns,
      dataSource: [],
      selectedRows: [],
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      sorter: {
        field: "id",
        order: "desc",
      },
      jump: false,
      loading: false,
      queryParam: {},
      roles: [],
      organizations: [],
      replaceFields: {
        children: "children",
        title: "displayName",
        key: "id",
        value: "id",
      },
      visibleXG: false,
      password: "",
      id: "",
      factoryList: [],
      departGroupPostDto: [],
      departGroupPostDto1: [], // 左边组织架构数据
      showLine: true,
      allData: {},
      levlName: "",
      selectedKeys: "",
      dataVisibleMode: false,
      orderno: "",
      messageMode: "",
      type1: "",
    };
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  async mounted() {
    that = this;
    this.getDepartGroupPostList();
    this.loadData();
    this.getRoles();
    this.getList();
    //this.getOrganizations();
    this.spinning = true;
    await getDepartGroupPostList().then(res => {
      this.allData = res?.data;
      var arrFac = res?.data.comBoxItems;
      var arrD = res?.data.departmentDtos;
      var arrG = res?.data.userGroupDtos;
      var arrP = res?.data.userPostDtos;
      var newArrD = [];
      var newArrG = [];
      var newArrP = [];
      for (var f = 0; f < arrFac.length; f++) {
        var objFac = {};
        newArrD = arrD.filter(item => {
          return item.factoryId == arrFac[f].valueMember;
        });
        objFac.id = arrFac[f].valueMember;
        objFac.label = arrFac[f].text;
        objFac.ids = objFac.id + "F";
        objFac.children = [];
        for (var d = 0; d < newArrD.length; d++) {
          var objD = {};
          newArrG = arrG.filter(item => {
            return item.departId == newArrD[d].id;
          });
          objD.id = newArrD[d].id;
          objD.ID = newArrD[d].factoryId;
          objD.label = newArrD[d].departmentName;
          objD.ids = objD.id + "D";
          objD.level = "D";
          objD.children = [];
          for (var g = 0; g < newArrG.length; g++) {
            var objG = {};
            newArrP = arrP.filter(ite => {
              return ite.groupId == newArrG[g].id;
            });
            objG.id = newArrG[g].id;
            objG.ids = objG.id + "G";
            objG.ID = newArrG[g].departId;
            objG.label = newArrG[g].groupName;
            objG.level = "G";
            objG.children = [];
            for (var p = 0; p < newArrP.length; p++) {
              var objP = {};
              objP.id = newArrP[p].id;
              objP.ids = objP.id + "P";
              objP.ID = newArrP[p].groupId;
              objP.label = newArrP[p].postName;
              objP.level = "P";
              objG.children.push(objP);
            }
            objD.children.push(objG);
          }
          objFac.children.push(objD);
        }
        this.departGroupPostDto.push(objFac);
      }
    });
  },
  filters: {
    sexFilter(key) {
      if (!key) {
        return "";
      }
      return sexMap[key].text;
    },
  },
  methods: {
    getList() {
      dimissionuserlist().then(res => {
        if (res.code) {
          this.inheritList = res.data;
        }
      });
    },
    OrderInheritance(record) {
      this.inheritvisble = true;
      this.formdata.inheritUser = "";
      this.formdata.dimissionUser = record.name;
      this.formdata.dimissionUserid = record.userName;
    },
    inheritok() {
      const form = this.$refs.ruleForm;
      let params = {
        dimissionUser: this.formdata.dimissionUserid,
        inheritUser: this.formdata.inheritUser,
      };
      form.validate(valid => {
        if (valid) {
          this.loading = true;
          this.inheritvisble = false;
          setdimissionuser2Otheruser(params)
            .then(res => {
              if (res.code) {
                this.$message.success("设置成功");
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    renderTitle() {
      return <span style="font-weight: 500;">确定要禁用吗？</span>;
    },
    renderTitle1() {
      return <span style="font-weight: 500;">确定要启用吗？</span>;
    },
    passBtn(val) {
      // this.visibleXG=true
      let params = {
        password: "123456",
        id: val.id,
      };
      password(params).then(res => {
        if (res) {
          this.$message.success("初始化成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    forceUpdate(id) {
      ForceUpdate(id).then(res => {
        this.loadData();
        this.$message.info("设置成功");
      });
    },
    // handle(){
    //   this.visibleXG=false
    //   password(this.id).then(res=>{
    //     if(res.code==1){
    //       this.$message.info(res.message)
    //     }else {
    //       this.$message.info(res.message)
    //     }
    //   })
    // },
    // handleCancel(){
    //   this.visibleXG=false
    // },
    checkPermission,
    handleChange() {},
    handleDel(id) {
      del(id).then(res => {
        this.loadData();
        this.$message.info("删除成功");
      });
    },
    Recovery(id) {
      delRecovery(id).then(res => {
        this.loadData();
        this.$message.info("恢复成功");
      });
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    onSelectChange() {
      this.$message.info("选中行改变了");
    },
    handleOk() {
      this.loadData();
    },

    handleTableChange(pagination, filters, sorter) {
      console.log(this.pagination.pageSize, pagination.pageSize);
      if (this.pagination.pageSize != pagination.pageSize) {
        this.pagination.current = 1;
        this.pagination.pageSize = pagination.pageSize;
        this.loadData();
      } else if (this.pagination.current != pagination.current) {
        this.pagination.current = pagination.current;
        this.pagination.pageSize = pagination.pageSize;
        this.loadData();
      }

      // this.pagination.current=pagination.current
      // this.pagination.pageSize=pagination.pageSize
      // localStorage.setItem('pageCurrent',this.pagination.current)
      // localStorage.setItem('pageSize',pagination.pageSize)
      // this.pageStat=false
      // localStorage.removeItem('stat')
      // this.loadData();
    },
    // handleTableChange(pagination, filters, sorter) {
    //   const pager = { ...this.pagination };
    //   pager.current = pagination.current;
    //   this.pagination = pager;
    //   if (sorter.field) this.sorter = sorter;
    //   this.loadData();
    // },
    selctedData(val) {
      this.selectedRows = [];
      this.selectedRows.push(val);
    },
    loadData(val) {
      this.loading = true;
      let params = {
        ...this.pagination,
        ...this.queryParam,
        sorter: this.sorter,
      };
      // let params =this.pagination
      if (val) {
        params.LevlName = val[0];
      } else {
        params.LevlName = this.selectedKeys;
      }
      getListWithDetails(params)
        .then(res => {
          const pagination = { ...this.pagination };
          this.pagination = pagination;
          this.dataSource = res.items;
          if (res.items.length) {
            pagination.total = res.totalCount;
          } else {
            pagination.total = 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    refresh() {
      this.pagination.current = 1;
      this.loadData();
    },
    reportHandleCancel() {
      this.inheritvisble = false;
      this.dataVisibleMode = false;
    },
    deleteClick() {
      if (!this.selectedRows.length) {
        this.$message.warning("请选择用户");
        return;
      }

      this.dataVisibleMode = true;
      this.orderno = this.selectedRows[0].userName;
      this.messageMode = "确认删除此用户吗？";
      this.type1 = "1";
    },
    handleOkMode() {
      if (this.type1 == "1") {
        delUser(this.selectedRows[0].id)
          .then(res => {
            if (res.code) {
              this.$message.success("删除成功");
              this.selectedRows = [];
              this.loadData();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.dataVisibleMode = false;
          });
      }
    },
    getRoles() {
      getAll().then(res => {
        this.roles = res.items;
      });
    },
    getOrganizations() {
      getOrganizationsAll().then(res => {
        this.organizations = res.items;
      });
    },
    onSelect(selectedKeys, event) {
      this.pagination.current = 1;
      this.selectedKeys = selectedKeys[0];
      this.loadData(selectedKeys);
      // event.node.onExpand()
    },

    async getDepartGroupPostList() {
      this.departGroupPostDto1 = [];
      this.spinning = true;
      await departGroupPostList()
        .then(res => {
          this.departGroupPostDto1 = res?.data;
          // var arrFac = res?.data.comBoxItems
          // var arrD = res?.data.departmentDtos
          // var arrG = res?.data.userGroupDtos
          // var arrP = res?.data.userPostDtos
          //   var newArrD=[]
          //   var newArrG=[]
          //   var newArrP=[]
          //   for(var f=0;f < arrFac.length;f++){
          //     var objFac={}
          //     newArrD = arrD.filter(item => { return item.factoryId == arrFac[f].valueMember})
          //     objFac.id = arrFac[f].valueMember;
          //     objFac.label = arrFac[f].text;
          //     objFac.ids = objFac.id + 'F'
          //     objFac.children=[];
          //     for(var d=0;d < newArrD.length;d++){
          //     var objD={}
          //     newArrG = arrG.filter(item => { return item.departId == newArrD[d].id })
          //     objD.id = newArrD[d].id;
          //     objD.ID = newArrD[d].factoryId;
          //     objD.label = newArrD[d].departmentName;
          //     objD.ids = objD.id + 'D'
          //     objD.level ='D';
          //     objD.children=[];
          //     for(var g=0;g < newArrG.length;g++){
          //       var objG={}
          //       newArrP = arrP.filter(ite => { return ite.groupId == newArrG[g].id })
          //       objG.id = newArrG[g].id;
          //       objG.ids = objG.id + 'G';
          //       objG.ID = newArrG[g].departId;
          //       objG.label = newArrG[g].groupName;
          //       objG.level ='G';
          //       objG.children=[];
          //       for(var p=0;p < newArrP.length;p++){
          //         var objP={}
          //         objP.id = newArrP[p].id;
          //         objP.ids = objP.id + 'P';
          //         objP.ID = newArrP[p].groupId;
          //         objP.label = newArrP[p].postName;
          //         objP.level ='P';
          //         objG.children.push(objP)
          //       }
          //       objD.children.push(objG)
          //     }
          //     objFac.children.push(objD)
          //   }
          //   this.departGroupPostDto1.push(objFac)
          //   }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.treestyle {
  border-right: 2px solid #f5f5f5;
  height: 824px;
  overflow: auto;
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #ff9900;
    // #fff9e6
  }
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.ant-spin-dot {
  top: 30%;
}
/deep/.ant-tree.ant-tree-show-line li span.ant-tree-switcher {
  color: #ffc552 !important;
}
/deep/ .ant-tree-node-content-wrapper {
  border: 1px solid #efefef !important;
}
/deep/.ant-table-thead > tr > th {
  padding: 6px 6px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 6px 6px !important;
  border-right: 1px solid #efefef;
}
/deep/ .ant-card-body {
  padding: 0px;
}
/deep/ .ant-form-item {
  padding-bottom: 0;
  margin-bottom: 0;
}
.content {
  display: flex;
  height: 728px;
  border: 2px solid #f5f5f5;
  /deep/.mintable {
    .ant-table-body {
      min-height: 697px;
    }
    .ant-table-pagination.ant-pagination {
      margin: 13px 0;
      z-index: 99;
      position: absolute;
      bottom: -7%;
      margin-left: 1%;
    }
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-tbody > tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
}
.bto {
  height: 50px;
  border: 2px solid #f5f5f5;
}

.search {
  margin-bottom: 3px;
  margin-top: 3px;
}
.fold {
  width: calc(100% - 216px);
  display: inline-block;
}
.operator {
  margin-bottom: 0;
  // display: inline-block;
  // margin-left: 25px;
  margin-right: 20px;
  float: right;
}
@media screen and (max-width: 900px) {
  .fold {
    width: 100%;
  }
}
</style>
