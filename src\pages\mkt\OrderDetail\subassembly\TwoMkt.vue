<!-- 市场管理-订单详情基本信息分片2  -->
<template>
  <div class="contentInfo" ref="SelectBox">
    <a-card :bordered="false">
      <a-form-model layout="inline" id="formDataElem" v-show="editFlag">
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="成品铜厚oz"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="
                requiredLinkConfigList.copperThickness && iseval(requiredLinkConfigList.copperThickness.isNullRules) && required ? 'require' : ''
              "
            >
              <div class="editWrapper" style="display: flex">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%" v-if="Number(formData.boardLayers) > 2">内</span>
                <a-select
                  v-model="formData.innerCopperThickness"
                  v-if="Number(formData.boardLayers) > 2"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  style="width: 16%"
                  @change="changeCoper()"
                  showSearch
                  allowClear
                  :filter-option="filterOption"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.InnerCopperThickness)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%">外</span>
                <a-select
                  v-model="formData.copperThickness"
                  style="width: 16%"
                  @change="changeCoper()"
                  showSearch
                  allowClear
                  :filter-option="filterOption"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.CopperThickness)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-left: 0.5%; margin-right: 0.5%; width: 10%" v-if="!formData.isCopperThickConversion">铜厚</span>
                <a-input v-model="formData.cuThickness" style="background: #fff; width: 45.5%" allowClear v-if="!formData.isCopperThickConversion" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="板材类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.fR4Type && iseval(requiredLinkConfigList.fR4Type.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.fR4Type"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="changefr4"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.FR4Type)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="客供板材"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="
                requiredLinkConfigList.isCustomerBoard && iseval(requiredLinkConfigList.isCustomerBoard.isNullRules) && required ? 'require' : ''
              "
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.isCustomerBoard" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="标记位置"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.markPosition && iseval(requiredLinkConfigList.markPosition.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.markPosition"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="changemark"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.MarkPosition)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="成品铜厚um"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              v-if="showData.isCopperThickConversion"
              :class="
                requiredLinkConfigList.copperThickness && iseval(requiredLinkConfigList.copperThickness.isNullRules) && required ? 'require' : ''
              "
            >
              <div class="editWrapper" style="display: flex">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%" v-if="Number(formData.boardLayers) > 2">内</span>
                <a-select
                  v-model="formData.innerCopperThickness2"
                  v-if="Number(formData.boardLayers) > 2"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  style="width: 16%"
                  @change="changeCoper('lt')"
                  showSearch
                  allowClear
                  :filter-option="filterOption"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.InnerCopperThickness2)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 5%">外</span>
                <a-select
                  v-model="formData.copperThickness2"
                  style="width: 16%"
                  @change="changeCoper('lt')"
                  showSearch
                  allowClear
                  :filter-option="filterOption"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.CopperThickness2)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-left: 0.5%; margin-right: 0.5%; width: 10%">铜厚</span>
                <a-input v-model="formData.cuThickness" style="background: #fff; width: 45.5%" allowClear />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="阻焊颜色"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="requiredLinkConfigList.solderColor && iseval(requiredLinkConfigList.solderColor.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 10%">顶层</span>
                <a-select
                  v-model="formData.solderColor"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 40%"
                  @change="solderColorC('change')"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.SolderColor)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 10%">底层</span>
                <a-select
                  v-model="formData.solderColorBottom"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="solderColorC('change', 'bot')"
                  style="width: 39%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.SolderColor)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="板材厂商"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.sheetTrader && iseval(requiredLinkConfigList.sheetTrader.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.sheetTrader"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  @change="changeSheet"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="item in sheetTrader"
                    :key="item.valueMember"
                    :value="item.valueMember"
                    :label="item.text"
                    :title="item.text"
                  >
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="无卤板材"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.isNotHalogen && iseval(requiredLinkConfigList.isNotHalogen.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.isNotHalogen" @change="changelogen" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="UL类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.ulType && iseval(requiredLinkConfigList.ulType.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.ulType"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  :disabled="!formData.markPosition"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.ULType)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="阻焊油墨"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="
                requiredLinkConfigList.solderResistInk && iseval(requiredLinkConfigList.solderResistInk.isNullRules) && required ? 'require' : ''
              "
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.solderResistInk"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 100%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option v-for="(item, index) in SolderResistInk1" :key="index" :value="item.value" :lable="item.lable" :title="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="板材型号"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.boardBrand && iseval(requiredLinkConfigList.boardBrand.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  dropdown-class-name="dropdown-board"
                  v-model="formData.boardBrand"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="changeType"
                >
                  <a-select-option v-for="item in boardBrand" :key="item.valueMember" :value="item.valueMember" :label="item.text" :title="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="耐CAF"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.cafResistance && iseval(requiredLinkConfigList.cafResistance.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.cafResistance" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="标记类型"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.markType && iseval(requiredLinkConfigList.markType.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.markType"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  :disabled="!formData.markPosition"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.MarkType)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="过孔处理"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="
                requiredLinkConfigList.solderCover &&
                requiredLinkConfigList.solderCover.isNullRules != '0' &&
                requiredLinkConfigList.solderCover.isNullRules &&
                required
                  ? 'require'
                  : ''
              "
            >
              <div class="editWrapper guokong">
                <a-select
                  v-model="formData.solderCover"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="cutchange"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.SolderCover)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-left: 0.5%; margin-right: 0.5%">阻焊厚度</span>
                <a-input allowClear v-model="formData.solderInkThickness" :title="formData.solderInkThickness" style="width: 13%" />um
                <span style="margin-left: 2.5%; margin-right: 0.5%">无卤油墨</span>
                <a-checkbox v-model="formData.isInkNotHalogen" @change="solderColorC('change', 'bot')" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="板材TG"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.fR4Tg && iseval(requiredLinkConfigList.fR4Tg.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select v-model="formData.fR4Tg" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.FR4Tg)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="板材CTI"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.plateCti && iseval(requiredLinkConfigList.plateCti.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select v-model="formData.plateCti" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.PlateCti)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="标记面向"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.markFace && iseval(requiredLinkConfigList.markFace.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.markFace"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  :disabled="!formData.markPosition"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.MarkFace)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="字符颜色"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="requiredLinkConfigList.fontColor && iseval(requiredLinkConfigList.fontColor.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 10%">顶层</span>
                <a-select
                  v-model="formData.fontColor"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  style="width: 40%"
                  @change="fontColorC('change')"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.FontColor)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 0.5%; margin-left: 0.5%; width: 10%">底层</span>
                <a-select
                  v-model="formData.fontColorBottom"
                  showSearch
                  allowClear
                  @change="fontColorC('change', 'bot')"
                  optionFilterProp="lable"
                  style="width: 39%"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.FontColor)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="大料尺寸"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.sheetSize && iseval(requiredLinkConfigList.sheetSize.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select v-model="formData.sheetSize" showSearch allowClear optionFilterProp="lable" :getPopupContainer="() => this.$refs.SelectBox">
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.SheetSize)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="利用率(%)"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="
                requiredLinkConfigList.sheetUtilization && iseval(requiredLinkConfigList.sheetUtilization.isNullRules) && required ? 'require' : ''
              "
            >
              <div class="editWrapper">
                <a-input v-model="formData.sheetUtilization" allowClear> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="周期格式"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.periodicFormat && iseval(requiredLinkConfigList.periodicFormat.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.periodicFormat"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  :disabled="!formData.markPosition"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.PeriodicFormat)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="6">
            <a-form-model-item
              label="字符油墨"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              :class="requiredLinkConfigList.characterResistInk && requiredLinkConfigList.characterResistInk.isNullRules && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.characterResistInk"
                  style="width: 50%"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option
                    v-for="(item, index) in CharacterResistInk1"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-right: 1%; margin-left: 1%">字符无卤</span>
                <a-checkbox v-model="formData.characterNotHalogen" @change="fontColorC('change', 'bot')" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="特殊料规格"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="
                requiredLinkConfigList.specialMaterialSpec && iseval(requiredLinkConfigList.specialMaterialSpec.isNullRules) && required
                  ? 'require'
                  : ''
              "
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.specialMaterialSpec"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  :disabled="
                    formData.boardBrand == 'RO4350B' ||
                    formData.boardBrand == 'RO4003C' ||
                    formData.boardBrand == 'SH260' ||
                    formData.boardBrand == 'CT350' ||
                    formData.boardBrand == 'CT338' ||
                    formData.fR4Type == 'highboard' ||
                    formData.fR4Type == 'Compositeboard' ||
                    formData.fR4Type == 'Hightemperatureplate' ||
                    formData.boardBrand == 'RO4534'
                      ? false
                      : true
                  "
                >
                  <a-select-option :title="item.lable" v-for="(item, index) in mapKey(copyobj)" :key="index" :value="item.value" :lable="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="板材张数"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.plateNumber && iseval(requiredLinkConfigList.plateNumber.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-input
                  allowClear
                  v-model="formData.plateNumber"
                  :disabled="
                    formData.boardBrand == 'RO4350B' ||
                    formData.boardBrand == 'RO4003C' ||
                    formData.boardBrand == 'SH260' ||
                    formData.boardBrand == 'CT350' ||
                    formData.boardBrand == 'CT338' ||
                    formData.fR4Type == 'highboard' ||
                    formData.fR4Type == 'Compositeboard' ||
                    formData.fR4Type == 'Hightemperatureplate' ||
                    formData.boardBrand == 'RO4534'
                      ? false
                      : true
                  "
                />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="产品用途"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.productUsage && iseval(requiredLinkConfigList.productUsage.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-select
                  v-model="formData.productUsage"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  @change="cutchange"
                >
                  <a-select-option
                    v-for="(item, index) in mapKey(selectOption.ProductUsage)"
                    :key="index"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="客户指定"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="requiredLinkConfigList.custAssignment && iseval(requiredLinkConfigList.custAssignment.isNullRules) && required ? 'require' : ''"
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.custAssignment" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="3">
            <a-form-model-item
              label="对压"
              :label-col="{ span: 12 }"
              :wrapper-col="{ span: 12 }"
              :class="
                requiredLinkConfigList.counterPressure && iseval(requiredLinkConfigList.counterPressure.isNullRules) && required ? 'require' : ''
              "
            >
              <div class="editWrapper">
                <a-checkbox v-model="formData.counterPressure" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-divider style="height: 6px; background-color: #c2c1c2; margin: 4px 0" />
        <three-mkt
          @ApertureRatio="$emit('ApertureRatio')"
          :spinning="spinning"
          :editFlag="editFlag"
          :showData="showData"
          :selectOption="selectOption"
          :boardBrandList="boardBrandList"
          :sheetTraderList="sheetTraderList"
          :boardtgList="boardtgList"
          :supList="supList"
          :joinFacId="joinFacId"
          :ManufacturerTG="ManufacturerTG"
          :show0="show0"
          :show1="show1"
          :show2="show2"
          :showMore="showMore"
          :showCG="showCG"
          :showHDI="showHDI"
          :showMM="showMM"
          :reOrder="reOrder"
          :required="required"
          :frontDataZSupplierf="frontDataZSupplierf"
          :requiredLinkConfigList="requiredLinkConfigList"
          :formData="formData"
          @cutchange="cutchange"
          ref="threeMkt"
        ></three-mkt>
      </a-form-model>
    </a-card>
  </div>
</template>
<script>
import { soldercolorresistinkrelation, fontcolorresistinkrelation } from "@/services/projectIndicate";
import ThreeMkt from "@/pages/mkt/OrderDetail/subassembly/ThreeMkt";
export default {
  name: "",
  props: [
    "editFlag",
    "spinning",
    "showData",
    "selectOption",
    "boardBrandList",
    "sheetTraderList",
    "boardtgList",
    "supList",
    "frontDataZSupplierf",
    "reOrder",
    "obj",
    "copperdata",
    "requiredLinkConfigList",
    "joinFacId",
    "required",
    "ManufacturerTG",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "formData",
    "sheetTrader",
  ],
  components: { ThreeMkt },
  data() {
    return {
      SolderResistInk: [],
      SolderResistInk1: [],
      CharacterResistInk: [],
      CharacterResistInk1: [],
      dataVisible: false,
      copyobj: [],
      dataVisible1: false,
      ReportList: [], // 出货报告列表
      boardLayers1: false,
      blindBuryStr1: false,
      plateTypeStr1: false,
      boardBrand: [],
      src: "",
      IPCLevel: [],
      tslge: false,
      frontDataZSupplier: [],
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      spinning1: false,
    };
  },
  created() {},
  mounted() {
    setTimeout(() => {
      this.getsolddata();
      this.getfontdata();
    }, 1000);
  },
  watch: {
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    obj: {
      handler(val) {
        this.copyobj = val;
      },
    },
    showData: {
      handler(val) {
        if (
          val.boardBrand == "RO4350B" ||
          val.boardBrand == "RO4003C" ||
          val.boardBrand == "SH260" ||
          val.boardBrand == "CT350" ||
          val.boardBrand == "CT338" ||
          val.boardBrand == "RO4534"
        ) {
          this.tslge = true;
        } else {
          this.tslge = false;
        }
      },
    },
    supList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.frontDataZSupplier = newval.slice(0, 20);
        });
      },
    },
  },

  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().trim().startsWith(input.toLowerCase()) == true;
    },
    cutchange() {
      this.$emit("cutchange");
    },
    changelogen() {
      if (this.formData.isNotHalogen == true) {
        this.formData.isInkNotHalogen = true;
        this.formData.characterNotHalogen = true;
      } else {
        this.formData.isInkNotHalogen = false;
        this.formData.characterNotHalogen = false;
      }
    },
    changefr4() {
      this.formData.sheetTrader = null;
      this.formData.boardBrand = null;
      if (this.formData.fR4Type != "highboard" && this.formData.fR4Type != "Compositeboard" && this.formData.fR4Type != "Hightemperatureplate") {
        this.formData.plateNumber = null;
        this.formData.specialMaterialSpec = null;
      }
      if (this.$route.query.factory == 70 && this.formData.fR4Type == "fr4") {
        this.formData.sheetTrader = "sy";
        this.formData.boardBrand = "S1000-2M";
        this.formData.fR4Tg = "TG180";
      }
    },
    changemark() {
      if (!this.formData.markPosition) {
        this.formData.ulType = null;
        this.formData.markType = null;
        this.formData.markFace = null;
        this.formData.periodicFormat = null;
      }
    },
    mapKey(data) {
      if (!data || !data.length) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    changeCoper(type) {
      this.$emit("changeCoper", type);
    },
    setEstimate2(value) {
      this.formData.surfaceFinishJsonDto.cjNickelThinckness = value.toString();
    },
    handleSearch2(value) {
      this.setEstimate2(value);
    },
    handleBlur2(value) {
      this.setEstimate2(value);
    },
    setEstimate5(value) {
      this.formData.surfaceFinishJsonDto.imGoldThinckness = value;
    },
    handleSearch5(value, list) {
      this.setEstimate5(value, list);
    },
    handleBlur5(value, list) {
      this.setEstimate5(value, list);
    },
    getsolddata() {
      soldercolorresistinkrelation(this.$route.query.factory).then(res => {
        if (res.code) {
          this.SolderResistInk = res.data;
          this.solderColorC();
        } else {
          this.SolderResistInk1 = this.mapKey(this.selectOption.SolderResistInk);
        }
      });
    },
    getfontdata() {
      fontcolorresistinkrelation(this.$route.query.factory).then(res => {
        if (res.code) {
          this.CharacterResistInk = res.data;
          this.fontColorC();
        } else {
          this.CharacterResistInk1 = this.mapKey(this.selectOption.CharacterResistInk);
        }
      });
    },
    //2025/2/19 BQ LT字符阻焊区分是否无卤、未配置下拉清空（市场）
    fontColorC(type, val) {
      let font = this.formData.fontColor ? this.formData.fontColor : this.showData.fontColor;
      if (font && type == "change" && val != "bot") {
        this.formData.fontColorBottom = font;
      }
      let font_color = "";
      if (font == "none" || font == "noResistance") {
        font_color = this.formData.fontColorBottom;
      } else {
        font_color = font;
      }
      this.CharacterResistInk1 = [];
      let data = [];
      //未规范接口内字段 solderColor为对应的字符颜色value  solderResistInk为对应的字符油墨value
      if (this.$route.query.factory == 22) {
        if (this.formData.characterNotHalogen) {
          data = this.CharacterResistInk.filter(ite => ite.solderColor == font_color && ite.hf == this.formData.characterNotHalogen);
        } else {
          data = this.CharacterResistInk.filter(ite => ite.solderColor == font_color);
        }
      } else {
        data = this.CharacterResistInk.filter(ite => ite.solderColor == font_color && ite.hf == this.formData.characterNotHalogen);
      }
      if (data.length != 0) {
        for (let index = 0; index < data.length; index++) {
          this.selectOption.CharacterResistInk.forEach(item => {
            if (item.valueMember == data[index].solderResistInk) {
              this.CharacterResistInk1.push({
                lable: item.text,
                value: data[index].solderResistInk,
              });
            }
          });
        }
      } else {
        if (this.$route.query.factory == 67 || this.$route.query.factory == 12) {
          this.CharacterResistInk1 = [];
        } else {
          this.CharacterResistInk1 = this.mapKey(this.selectOption.CharacterResistInk);
        }
      }
      if (type == "change") {
        this.formData.characterResistInk = "";
      } else {
        this.formData.characterResistInk = this.formData.characterResistInk ? this.formData.characterResistInk : "";
      }
    },
    solderColorC(type, val) {
      let solder = this.formData.solderColor ? this.formData.solderColor : this.showData.solderColor;
      if (solder && type == "change" && val != "bot") {
        this.formData.solderColorBottom = solder;
      }
      let solder_color = "";
      if (solder == "none" || solder == "noResistance") {
        solder_color = this.formData.solderColorBottom;
      } else {
        solder_color = solder;
      }
      this.SolderResistInk1 = [];
      let data = [];
      if (this.formData.isInkNotHalogen) {
        data = this.SolderResistInk.filter(ite => ite.solderColor == solder_color && ite.hf == true);
      } else {
        if (this.$route.query.factory == 67 || this.$route.query.factory == 12) {
          data = this.SolderResistInk.filter(ite => ite.solderColor == solder_color && ite.hf == false);
        } else {
          data = this.SolderResistInk.filter(ite => ite.solderColor == solder_color);
        }
      }
      if (data.length != 0) {
        for (let index = 0; index < data.length; index++) {
          this.selectOption.SolderResistInk.forEach(item => {
            if (item.valueMember == data[index].solderResistInk) {
              this.SolderResistInk1.push({
                lable: item.text,
                value: data[index].solderResistInk,
              });
            }
          });
        }
      } else {
        if (this.$route.query.factory == 67 || this.$route.query.factory == 12) {
          this.SolderResistInk1 = [];
        } else {
          this.SolderResistInk1 = this.mapKey(this.selectOption.SolderResistInk);
        }
      }
      if (type == "change") {
        this.formData.solderResistInk = "";
      } else {
        this.formData.solderResistInk = this.formData.solderResistInk ? this.formData.solderResistInk : "";
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("formData") != -1) {
        newIsNullRules = newIsNullRules.replace(/formData/g, "this.formData");
      }
      if (val.indexOf("reOrder") != -1) {
        newIsNullRules = newIsNullRules.replace(/reOrder/g, "this.reOrder");
      }
      if (val.indexOf("tslge") != -1) {
        newIsNullRules = newIsNullRules.replace(/tslge/g, "this.tslge");
      }
      if (val.indexOf("boardBrand") != -1) {
        newIsNullRules = newIsNullRules.replace(/boardBrand/g, "this.boardBrand");
      }

      return eval(newIsNullRules);
    },
    changeType() {
      this.formData.fR4Tg = null;
      let fR4Tg = [];
      if (this.boardtgList.length) {
        fR4Tg = this.formData.boardBrand
          ? this.boardtgList.filter(item => {
              return item.valueMember1 == this.formData.boardBrand;
            })
          : [];
      }
      if (!this.formData.fR4Tg && fR4Tg.length) {
        this.formData.fR4Tg = fR4Tg[0].valueMember;
      }
      if (this.ManufacturerTG.length && !this.formData.sheetTrader) {
        let data = this.formData.boardBrand
          ? this.ManufacturerTG.filter(item => {
              return item.coreType_ == this.formData.boardBrand;
            })
          : [];
        if (data.length) {
          this.formData.fR4Tg = data[0].tgValue;
          this.formData.sheetTrader = data[0].verdorName_Value;
        }
      }
      if (this.boardBrandList.length && this.formData.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.formData.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.formData.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
      let arr = this.mapKey(this.selectOption.FR4TypeSpecialMaterialSpec);
      let arr1 = arr.filter(item => {
        return item.lable == this.formData.boardBrand;
      });
      if (
        this.formData.boardBrand == "RO4350B" ||
        this.formData.boardBrand == "RO4003C" ||
        this.formData.boardBrand == "SH260" ||
        this.formData.boardBrand == "CT350" ||
        this.formData.boardBrand == "CT338" ||
        this.formData.boardBrand == "RO4534"
      ) {
        this.tslge = true;
      } else {
        this.tslge = false;
      }
      let obj = [];
      if (arr1.length) {
        for (var a = 0; a < arr1.length; a++) {
          this.selectOption.SpecialMaterialSpec.forEach(ite => {
            if (ite.valueMember == arr1[a].value) {
              obj.push({ valueMember: ite.valueMember, text: ite.text });
            }
          });
        }
      }
      this.formData.specialMaterialSpec = "";
      this.copyobj = obj;
      this.formData.plateNumber = "";
    },
    changeSheet() {
      this.formData.boardBrand = null;
      if (this.boardBrandList.length && this.formData.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.formData.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.formData.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.toUpperCase().indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let arr = [];
        let that = this;
        that.valueData = value.toUpperCase();
        arr = that.supList.filter(m => m.toUpperCase().indexOf(value.toUpperCase()) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.supList.slice(0, 20);
      }
    },
    supValue1(value) {
      if (!this.formData.custNo) {
        this.supValue(undefined);
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.dropdown-board {
  min-width: 145px !important;
}
.guokong {
  /deep/.ant-select {
    width: 36% !important;
  }
  /deep/.ant-input {
    padding: 0 !important;
  }
}
.widthclass {
  .select1 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 37px !important;
    }
  }
  .select2 {
    /deep/.ant-select-selection--single {
      height: 22px !important;
      width: 56px !important;
    }
  }
}
.bborder {
  .div2 {
    /deep/.ant-form-item-control {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

/deep/b {
  font-weight: 500;
}
.div22 {
  /deep/.ant-form-item-control {
    padding: 0;
    max-height: 65px;
    min-height: 24.5px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
/deep/.require11 {
  color: red !important;
}
// .cu{
//   /deep/.ant-form-item-control{
//     height:26.67px;
//   }
// }
#formDataElem1 {
  .div1 {
    .ant-form-item {
      width: 30%;
    }
    /deep/ .ant-form-item-control-wrapper {
      // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
      font-family: PingFangSC-Regular, Sans-serif;
      font-weight: 500;
      color: #000000;
      font-size: 13px;
      .ant-form-item-control {
        .ant-form-item-children {
          display: block;
          min-height: 13.672px;
        }
        line-height: inherit;
        padding: 2px 4px !important;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
      }
    }
    // /deep/.pcbFileName{
    //   .ant-form-item-label{
    //     width:30%;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:70%;
    //   }
    // }
  }
}
/deep/.ant-input-affix-wrapper {
  width: 100%;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 13px;
  font-weight: 500;
  color: #000000;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 6px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 2px;
}
/deep/.line3 {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
  // .ant-form-item-control-wrapper {
  //   .ant-form-item-control{
  //     border:0!important;
  //   }
  // }
}
.bbb {
  /deep/.ant-form-item-label {
    width: 16.65%;
    // -ms-width: 101px important; // IE
    // -webkit-width:100.5%; //谷歌
    border-left: 1px solid #ddd;
  }
  /deep/.ant-form-item-control-wrapper {
    // -ms-width: 911px!important; // IE
    // -webkit-width:942.0.5%; //谷歌
    width: 83.3%;
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
// .bbb{
//   /deep/.ant-form-item-label{
//     width:105px;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   /deep/.ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     // width:1038px;
//     width:100%;
//   }
//   /deep/textarea.ant-input {
//     min-height:24px;
//   }
//   /deep/.ant-form-item-control{
//     padding: 2px !important;
//     // width: 942px;
//     width:100%;
//   }
//   /deep/.ant-input{
//     height:24px;
//   }
// }
// /deep/.editWrapper1 {
//   .ant-form-item-label{
//     width:10%;
//     // -ms-width: 101px important; // IE
//     // -webkit-width:100.5%; //谷歌
//     border-left:1px solid #ddd;
//   }
//   .ant-form-item-control-wrapper{
//     // -ms-width: 911px!important; // IE
//     // -webkit-width:942.0.5%; //谷歌
//     width:1092px;
//   }
// }

/deep/.ant-select-selection--single {
  height: 22px !important;
}
/deep/.ant-select-item-option-content {
  color: red !important;
}
/deep/.ant-select-selection__rendered {
  line-height: 20px !important;
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.require1 {
  .ant-form-item-label > label {
    color: red !important;
    background-color: greenyellow;
  }
}
// /deep/.bac{
//     .ant-form-item-label > label {
//     color: red!important;
//     background-color: #ff9900;
// }

// }
span {
  font-size: 13px;
}

/deep/.ant-select {
  font-size: 13px !important;
}
/deep/.ant-input {
  font-size: 13px !important;
  font-weight: 500;
}
.contentInfo {
  font-size: 13px;
  width: 1676px;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    font: 12px / 1.14 arial;
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 10.5%;
        text-indent: 0.5%;
        font-size: 13px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 10.5%;
      }
    }
    .special {
      height: 268px;
      width: 456px;
      display: inline-block;
      position: absolute;
      right: 172px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 270px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 261px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 270px;
              width: 75px;
            }
          }
        }
      }
    }
    .special1 {
      height: 268px;
      width: 308px;
      display: inline-block;
      position: absolute;
      right: 320px;
      top: 1px;
      .ant-row {
        .ant-col {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              .ant-form-item-control {
                height: 267px;
                .ant-form-item-children {
                  vertical-align: top;
                  overflow: auto;
                  width: 100%;
                  height: 250px;
                  display: inline-block;
                  word-wrap: break-word;
                  .editWrapper {
                    .ant-input {
                      height: 261px;
                      margin-top: 245px;
                    }
                  }
                }
              }
            }
            .ant-form-item-label {
              height: 267px;
            }
          }
        }
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        .ant-row {
          .line2 {
            .ant-form-item-label {
              border-bottom: 0px;
            }
          }
          .line {
            .ant-form-item-control {
              border-bottom: 0px;
            }
          }
        }
      }
      .spec {
        width: 19%;
        position: absolute;
        top: 0;
        right: 0;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .tmp {
          word-break: keep-all;
          vertical-align: top;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: inline-block;
        }
        .editWrapper1 {
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            width: 99%;
          }
        }
        .editWrapper {
          width: 100%;
          display: flex;
          align-items: center;
          height: 14px;
          .ant-select {
            // width:96px;
            width: 99%;
            height: 22px;
          }
          .ant-input {
            // -ms-width: 92px; // IE
            // -webkit-width:96px; //谷歌
            width: 99%;
            height: 22px;
            line-height: 22px;
            padding: 0 10px 0 7px;
          }
          .ant-input-number {
            // width: 96px;
            width: 99%;
          }
        }
        .ant-form-item-label {
          // width: 117px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font: 12px/1.14 "微软雅黑", arial;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          // border-left: 1px solid #ddd;
          label {
            // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
            font-family: PingFangSC-Regular, Sans-serif;
            font-size: 13px;
            font-weight: 500;
            color: #000000;
          }
        }
        .ant-form-item-control-wrapper {
          // font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          font-family: PingFangSC-Regular, Sans-serif;
          font-weight: 500;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
              white-space: pre-line;
            }
            line-height: inherit;
            padding: 6px 4px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
