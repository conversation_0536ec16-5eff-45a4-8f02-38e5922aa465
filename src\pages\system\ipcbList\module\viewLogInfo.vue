<!--
 * @Author: CJP
 * @Date: 2022-05-30 08:20:52
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-27 16:27:03
 * @FilePath: \vue-antd-admin\src\pages\projectPage\module\viewLogInfo.vue
 * @Description: 
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved. 
-->
<template>
  <div class='viewLog'>
    <a-empty v-if="!viewLogData.length>0"  /> 
    <a-steps progress-dot  direction="vertical" v-else :current="current1">
        <a-step :title="item.name" :description='item.dateTimeStr' v-for="(item,index) in viewLogData" :key="index"  >        
        </a-step>
    </a-steps>
  </div>
</template>

<script>

export default{
    name: "viewLogInfo",
    props:['viewLogData'],
    data(){
        return{
            current1:0,

        }
    },
    mounted(){       
    },
    watch:{
        viewLogData: function (val) {
        if(val){
            this.getData()            
        }
      
      }
    },
    methods:{
        getData(){
         console.log('this.viewLogData',this.viewLogData)
          let arr =   this.viewLogData.filter(item =>{return item.dateTimeStr})
          console.log('arr',arr,arr.length -1)
          if(arr.length){
            this.current1 = arr[arr.length -1].display
          }
          console.log('this.current1arr',this.current1)
        }
    }

}
</script>
<style scoped lang='less'>
/deep/.ant-steps{
    color:#000000;
}
/deep/.ant-steps-item-container{
    display: flex;
}
/deep/.ant-steps-vertical .ant-steps-item-content{
    min-height:30px;
}
.viewLog{
    // padding-left:50px;
 /deep/ .ant-steps-item-content{
     .ant-steps-item-title{
         display:inline-block;
         width:45%;
     }
     .ant-steps-item-description{
         display:inline-block;
         width:54%;
         padding-bottom: 0;
     }
        margin-left:10px;
        width:98%;
        display: inline-block;
    }
    max-height:800px;
    // overflow-y:auto;
    // overflow-y:scroll;
}
</style>
