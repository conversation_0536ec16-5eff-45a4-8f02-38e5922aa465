<template>
  <a-spin :spinning="spinning">
    <div class="PendingStorage" ref="tableWrapper">
      <div class="top">
        <a-select v-model="searchdata.type">
          <a-select-option v-for="(item, index) in selectlist" :key="index" :value="item.value">{{ item.label }}</a-select-option>
        </a-select>
        <a-input placeholder="生产单号/管制卡号" allowClear autoFocus @keydown.enter.native="searchclick" v-model="searchdata.orderNo"></a-input>
        <div style="padding-left: 15px">
          <a-button type="primary" @click="searchclick"><a-icon type="search" />查询</a-button>
        </div>
        <div style="padding-left: 15px"><a-button type="primary">重置</a-button></div>
        <div style="padding-left: 15px" v-if="checkPermission('MES.ProManagement.WarehousingList.Warehousing')">
          <a-button type="primary" @click="Warehouseentry">入库</a-button>
        </div>
        <div style="padding-left: 15px"><a-button type="primary">批量入库</a-button></div>
        <div style="padding-left: 15px"><a-button type="primary">打印标签</a-button></div>
      </div>
      <div style="padding: 15px 0 0 15px; height: 694px; border-bottom: 2px solid #e9e9f0">
        <a-table
          :columns="columns"
          :customRow="customRow"
          :rowClassName="isRedRow"
          :data-source="pendingdata"
          :pagination="false"
          :rowKey="(record, index) => index + 1"
          bordered
          :scroll="{ y: 643 }"
          :class="pendingdata.length ? 'mintable' : ''"
        >
        </a-table>
        <right-copy ref="RightCopy" />
      </div>
      <div class="footerAction" style="user-select: none; margin-left: -2px"></div>
    </div>
    <a-modal title="确认弹窗" :visible="modalvisible" @ok="handleOk" @cancel="handleCancel" centered :width="400">
      <span>{{ showmessage }}</span>
    </a-modal>
  </a-spin>
</template>
<script>
import RightCopy from "@/pages/RightCopy";
import { checkPermission } from "@/utils/abp";
import { mapState } from "vuex";
import { orderlistbycardinfo, pcbstoragewarehousing } from "@/services/scgl/OrderManagement/PackagingStorage";
const columns = [
  {
    title: "序号",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "生产编号",
    align: "left",
    dataIndex: "proOrderNo",
    ellipsis: true,
    width: 120,
  },
  {
    title: "业务编号",
    align: "left",
    dataIndex: "businessOrderNo",
    ellipsis: true,
    width: 120,
  },
  {
    title: "管制卡编号",
    align: "left",
    dataIndex: "cardNo",
    ellipsis: true,
    width: 120,
  },
  {
    title: "拼板号",
    align: "left",
    dataIndex: "groupNo",
    ellipsis: true,
    width: 100,
  },
  {
    title: "入库类型",
    align: "left",
    customRender: (text, record, index) => `${record.type == 1 ? "正常入库" : "退货入库"}`,
    ellipsis: true,
    width: 70,
  },
  {
    title: "入库数量",
    align: "left",
    dataIndex: "num",
    ellipsis: true,
    width: 70,
  },
  {
    title: "备品数量",
    align: "left",
    dataIndex: "sparesNum",
    ellipsis: true,
    width: 70,
  },
  {
    title: "创建时间",
    align: "left",
    dataIndex: "createTime",
    ellipsis: true,
    width: 150,
  },
  {
    title: "仓库编号",
    align: "left",
    dataIndex: "warehouse",
    ellipsis: true,
    width: 100,
  },
  {
    title: "仓库编号区域",
    align: "left",
    dataIndex: "warehouseArea",
    ellipsis: true,
    width: 100,
  },
  {
    title: "仓库编号区域架子",
    align: "left",
    dataIndex: "warehouseShelf ",
    ellipsis: true,
    width: 130,
  },
  {
    title: "仓库审核状态",
    align: "left",
    customRender: (text, record, index) => `${record.state == 0 ? "未审核" : "已审核"}`,
    ellipsis: true,
    width: 100,
  },
  {
    title: "审核时间",
    align: "left",
    dataIndex: "verifyTime",
    ellipsis: true,
    width: 100,
  },
  {
    title: "审核人",
    align: "left",
    dataIndex: "verifyTimeUserName",
    ellipsis: true,
    width: 80,
  },
];
export default {
  name: "PendingStorage",
  components: {
    RightCopy,
  },
  data() {
    return {
      showmessage: "",
      modaltype: "",
      modalvisible: false,
      selectedRowKeysArray: [],
      selectedRowsData: {},
      startIndex: -1,
      shiftKey: false,
      text: "",
      spinning: false,
      columns,
      pendingdata: [],
      searchdata: {
        type: 1,
        orderNo: "",
      },
      selectlist: [
        { value: 0, label: "生产单" },
        { value: 1, label: "管制卡号" },
      ],
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        isCtrlPressed: false,
        size: "",
        simple: false,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  created() {},
  mounted() {
    //this.getpendingdata(this.searchdata)
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  methods: {
    checkPermission,
    handleCancel() {
      this.modalvisible = false;
    },
    handleOk() {
      if (this.modaltype == "rk") {
        let params = {
          orderNo: this.selectedRowsData.cardNo,
          type: this.searchdata.type,
          joinFactoryId: this.user.factoryId,
        };
        pcbstoragewarehousing(params).then(res => {
          if (res.code) {
            this.$message.success("入库成功");
            this.getpendingdata();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      this.modalvisible = false;
    },
    Warehouseentry() {
      if (this.selectedRowKeysArray.length != 1) {
        this.$message.error("请选择一条数据进行入库");
        return;
      }
      this.modalvisible = true;
      this.showmessage = "确认需要入库吗?";
      this.modaltype = "rk";
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },

    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeysArray;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowKeysArray = rowKeys;
        if (this.selectedRowKeysArray.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowKeysArray = [record.id];
        }
      }
      this.proOrderId = this.selectedRowsData.id;
      this.shiftKey = false;
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.pendingdata.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      this.selectedRowKeysArray = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.pendingdata.filter(item => {
          return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.pendingdata.filter(item => {
          return item.id == this.selectedRowKeysArray[0];
        })[0];
      }
    },
    searchclick() {
      this.getpendingdata(this.searchdata);
    },
    getpendingdata() {
      // let params={
      //   'PageIndex':this.pagination.current,
      //   'PageSize':this.pagination.pageSize,
      // }
      // if(querydata){
      //   params = {...params,...querydata}
      // }

      if (!this.searchdata.orderNo) {
        this.$message.error("请输入生产单号或者管制卡号进行查询");
        return;
      }
      this.spinning = true;
      orderlistbycardinfo(this.user.factoryId, this.searchdata.orderNo, this.searchdata.type)
        .then(res => {
          if (res.code) {
            this.pendingdata = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.tabRightClikBox {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.footerAction {
  width: 100%;
  height: 46px;
  background: white;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}
.mintable {
  border: 2px solid #e9e9f0;
  /deep/ .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
    position: relative;
    background: #fff;
    min-height: 643px;
  }
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  position: fixed;
  font-size: small;
  margin: 7px;
}
/deep/.ant-table-bordered.ant-table-fixed-header
  .ant-table-scroll
  .ant-table-header.ant-table-hide-scrollbar
  .ant-table-thead
  > tr:only-child
  > th:last-child {
  border-right-color: #efefef;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.PendingStorage {
  background-color: white;
  user-select: none;
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 3px 6px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-thead > tr > th {
      padding: 4px 6px;
      height: 30px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 6px;
      height: 30px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
.top {
  display: flex;
  .ant-select {
    width: 250px;
    padding: 0 15px;
  }
  /deep/.ant-input-affix-wrapper {
    width: 250px !important;
  }
}
</style>
