<!-- 市场管理-订单详情 --按钮2  -->
<template>
  <a-form-model layout="inline">
    <a-form-model-item v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreFanHui') && ttype == 2">
      <a-button @click="returnClick" type="primary">
        <!-- 返回订单列表 -->
        返回订单预审
      </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.MarketModule.Check.CheckFanHui') && ttype == 1">
      <a-button @click="returnClick1" type="primary">
        <!-- 返回订单列表 -->
        返回订单报价
      </a-button>
    </a-form-model-item>
    <a-form-model-item
      v-if="
        checkPermission('MES.MarketModule.Prequalificationproduction.PreEdit') &&
        !editFlag &&
        (activeKey == 1 || activeKey == 2 || activeKey == 4 || activeKey == 6 || activeKey == 7) &&
        ttype == 2
      "
    >
      <a-button @click="edit" type="primary"> 编辑(E) </a-button>
    </a-form-model-item>
    <a-form-model-item
      v-if="
        checkPermission('MES.MarketModule.Check.CheckEdit') &&
        !editFlag &&
        (activeKey == 1 || activeKey == 2 || activeKey == 4 || activeKey == 6 || activeKey == 7) &&
        ttype == 1
      "
    >
      <a-button @click="edit" type="primary"> 编辑(E) </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="editFlag && (activeKey == 1 || activeKey == 2 || activeKey == 4 || activeKey == 6 || activeKey == 7)">
      <a-button type="primary" @click="save"> 保存(S) </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PrePreview') && (ttype == 2 || preMode == 2)">
      <a-button type="primary" @click="previewClick"> 图形预览 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreStack') && (ttype == 2 || preMode == 2)">
      <a-button type="primary" @click="laminationClick"> 叠层阻抗 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PrePnl') && (ttype == 2 || preMode == 2)">
      <a-button type="primary" @click="CuttingClick"> 开料拼板 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="isCustRule">
      <a-button type="primary" @click="CustomerRulesClick"> 客户规则 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="checkPermission('MES.MarketModule.Prequalificationproduction.PreEnd') && ttype == 2">
      <a-button type="primary" @click="modifyInfoClick" :disabled="editFlag"> 预审完成 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="orderModify != 3 && orderModify && checkPermission('MES.MarketModule.Check.CheckEnd')">
      <a-button type="primary" @click="$emit('ordermodifyInfo')" :disabled="editFlag"> 报价完成 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="showData.status == '预审中'">
      <a-button type="primary" @click="uploadpBT()"> 上传拼版图 </a-button>
      <a-upload
        accept=".jpg,.jpeg,.png,"
        name="file"
        ref="fileRef"
        :showUploadList="false"
        :before-upload="beforeUpload1"
        :customRequest="downloadFilesCustomRequest"
        :file-list="fileListData"
      >
      </a-upload>
    </a-form-model-item>
    <a-form-model-item v-if="showData.status == '预审中'">
      <a-button type="primary" @click="backGroudKLClick"> 后台拼板 </a-button>
    </a-form-model-item>
    <a-form-model-item v-if="ttype == 2 && checkPermission('MES.MarketModule.Prequalificationproduction.PreReview')">
      <a-button type="primary" @click="review">评审</a-button>
    </a-form-model-item>
    <a-form-model-item>
      <a-button type="primary" @click="$emit('diffPreview')">差异预览</a-button>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import { setreviewinfo } from "@/services/projectApi";
import { preuploadpBT } from "@/services/mkt/orderInfo";
import { checkPermission } from "@/utils/abp";
export default {
  name: "OrderAction",
  props: ["editFlag", "ttype", "isCustRule", "activeKey", "orderModify", "joinFacId", "preMode", "showData"],
  data() {
    return { fileListData: [] };
  },
  methods: {
    checkPermission,

    edit() {
      this.$emit("editInfo");
    },
    save() {
      this.$emit("dataSave");
    },
    // 图形预览
    previewClick() {
      this.$emit("previewClick");
    },
    // 叠层
    laminationClick() {
      this.$emit("laminationClick");
    },
    // 开料
    CuttingClick() {
      this.$emit("CuttingClick");
    },
    //市场评审
    review() {
      setreviewinfo(this.$route.query.factory, this.$route.query.orderNo, this.$route.query.orderNo, 0).then(res => {
        if (res.code) {
          this.$router.push({
            path: "/gongju/Judgingcontent",
            query: {
              OrderNo: this.$route.query.orderNo,
              businessOrderNo: this.$route.query.orderNo,
              joinFactoryId: this.$route.query.factory,
              id: this.$route.query.id,
              reviewSource: 0, //工程进入评审=新增  工程1 市场0
              review_Type: "add",
              page: "0",
            },
          });
        }
      });
    },
    returnClick() {
      this.$nextTick(function () {
        this.$router.push({ path: "/shichang/Orderverify" });
        this.$forceUpdate();
      });
    },
    returnClick1() {
      this.$nextTick(function () {
        this.$router.push({ path: "/shichang/OrderOffer" });
        this.$forceUpdate();
        // this.$router.push({path: '/shichang/OrderReview', })
      });
    },
    CustomerRulesClick() {
      this.$emit("CustomerRulesClick");
    },
    modifyInfoClick() {
      this.$emit("modifyInfoClick");
    },
    backGroudKLClick() {
      this.$emit("backGroudKLClick");
    },
    uploadpBT() {
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng =
          file.name.toLowerCase().indexOf(".jpeg") != -1 ||
          file.name.toLowerCase().indexOf(".png") != -1 ||
          file.name.toLowerCase().indexOf(".jpg") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("上传开料拼版图只支持.jpg,.jpeg,.png,格式文件");
          reject();
        } else {
          resolve();
        }
      });
    },
    async downloadFilesCustomRequest(data) {
      const formData = new FormData();
      let id = Array.isArray(this.$route.query.id) ? this.$route.query.id[0] : this.$route.query.id;
      formData.append("file", data.file);
      await preuploadpBT(id, formData).then(res => {
        if (res.code) {
          data.onSuccess(res.data);
          this.$message.success("上传成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
  created() {
    // console.log(this.orderModify,'orderModify');
  },
};
</script>

<style scoped></style>
