<!-- 市场管理 - 订单管理- 按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <!-- <div class="box" v-if="checkPermission('MES.MarketModule.OrderManage.OrderOffline')"
    :class='checkPermission("MES.MarketModule.OrderManage.OrderOffline")?"showClass":""'  >
      <a-button type="primary" @click="auditClick" >
        订单下线
      </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.MarketModule.OrderManage.OrderOffline2')"
    :class='checkPermission("MES.MarketModule.OrderManage.OrderOffline2")?"showClass":""'  >
      <a-button type="primary" @click="auditClick1" >
        供应链下单
      </a-button>
    </div> -->
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.OrderOffline')"
      :class="checkPermission('MES.MarketModule.OrderManage.OrderOffline') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Orderoffline')"> 订单下线 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.OrderBackToVerify')"
      :class="checkPermission('MES.MarketModule.OrderManage.OrderBackToVerify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="orderRollback"> 订单回退 </a-button>
    </div>

    <!-- <div class="box" v-if="checkPermission('MES.MarketModule.OrderManage.CheckContractReviewInfo')" 
    :class='checkPermission("MES.MarketModule.OrderManage.CheckContractReviewInfo")?"showClass":""'> 
        <a-button type="primary" @click="ReviewSheet">
          合同评审单
        </a-button>
      </div> -->
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.NopeAlterReport')"
      :class="checkPermission('MES.MarketModule.OrderManage.NopeAlterReport') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="changeOrder"> 更改单 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.NoticeReviewInfo')"
      :class="checkPermission('MES.MarketModule.OrderManage.NoticeReviewInfo') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Productionnotification"> 生产通知单 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.SalesContract')"
      :class="checkPermission('MES.MarketModule.OrderManage.SalesContract') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="quotationcontract"> 销售合同(H) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.ContractFiling')"
      :class="checkPermission('MES.MarketModule.OrderManage.ContractFiling') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Contractarchiving"> 合同存档 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.OrderCancel')"
      :class="checkPermission('MES.MarketModule.OrderManage.OrderCancel') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ordercancellation"> 订单取消 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.BackToWaitOffline')"
      :class="checkPermission('MES.MarketModule.OrderManage.BackToWaitOffline') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="BackToWaitOffline"> 回退待下线 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.MarketModule.OrderManage.OrderVerify')"
      :class="checkPermission('MES.MarketModule.OrderManage.OrderVerify') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Orderapproval')"> 订单审批 </a-button>
    </div>
    <!-- <div class="box" v-show="checkPermission('MES.MarketModule.Check.MktOrderModify') " 
      :class='checkPermission("MES.MarketModule.Check.MktOrderModify")?"showClass":""'> 
        <a-button type="primary" @click="marketmodification">
          市场修改 
        </a-button>
      </div> -->
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px; width: 100px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="BackToWaitOffline" v-if="checkPermission('MES.MarketModule.OrderManage.BackToWaitOffline')">回退待下线</a-menu-item>
            <a-menu-item @click="ordercancellation" v-if="checkPermission('MES.MarketModule.OrderManage.OrderCancel')">订单取消</a-menu-item>
            <a-menu-item @click="quotationcontract" v-if="checkPermission('MES.MarketModule.OrderManage.SalesContract')">销售合同(H)</a-menu-item>
            <a-menu-item @click="Contractarchiving" v-if="checkPermission('MES.MarketModule.OrderManage.ContractFiling')">合同存档</a-menu-item>
            <a-menu-item @click="Productionnotification" v-if="checkPermission('MES.MarketModule.OrderManage.NoticeReviewInfo')"
              >生产通知单</a-menu-item
            >
            <a-menu-item @click="changeOrder" v-if="checkPermission('MES.MarketModule.OrderManage.NopeAlterReport')">更改单</a-menu-item>
            <a-menu-item @click="orderRollback" v-if="checkPermission('MES.MarketModule.OrderManage.OrderBackToVerify')">订单回退</a-menu-item>
            <!-- <a-menu-item @click="auditClick1" v-if="checkPermission('MES.MarketModule.OrderManage.OrderOffline2')" >供应链下单</a-menu-item> -->
            <a-menu-item @click="$emit('Orderoffline')" v-if="checkPermission('MES.MarketModule.OrderManage.OrderOffline')">订单下线</a-menu-item>
            <a-menu-item @click="queryClick">查询(F)</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <!-- <div class="box">
      <a-button type="primary" @click="delClick"  >
        订单取消
      </a-button>
    </div> -->
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";

export default {
  name: "BackendAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    total: {
      type: Number,
    },
  },
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      nums: 0,
      buttonsmenu: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const elements1 = document.getElementsByClassName("separate");
      const num = elements.length;
      this.nums = elements.length + elements1.length;
      let buttonsToShow = 6;
      if (this.$refs.active.children.length > 6) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 6 && !elements1.length) {
        this.showBtn = false;
      } else if (num > 6 && !elements1.length) {
        this.showBtn = true;
        this.advanced = false;
      } else if (num <= 6 && elements1.length) {
        this.showBtn = true;
        this.advanced = false;
      } else if (num > 6 && elements1.length) {
        this.showBtn = true;
        this.advanced = false;
      }
      if (elements1.length) {
        for (let i = 0; i < elements1.length; i++) {
          elements1[i].style.display = "none";
        }
      }
      this.handleResize();
    });
  },
  methods: {
    checkPermission,
    handleResize() {
      var paginnum = "";
      var elements = document.getElementsByClassName("showClass");
      var elements1 = document.getElementsByClassName("separate");
      let num = "";
      if (!this.advanced) {
        num = 7 * 104;
      } else {
        num = (elements.length + 1 + Number(elements1.length)) * 104;
      }
      if (Math.ceil(this.total / 20) > 10) {
        paginnum = 6;
      } else {
        paginnum = Math.ceil(this.total / 20);
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (paginnum * 25 + 200 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            if (i < 6) {
              elements[i].style.display = "inline-block";
            }
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            elements1[0].style.display = "none";
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.advanced = false;
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              elements1[0].style.display = "none";
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                if (i < 6) {
                  elements[i].style.display = "inline-block";
                }
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          if (i < 6) {
            elements[i].style.display = "inline-block";
          }
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("showClass");
      const elements1 = document.getElementsByClassName("separate");
      if (this.advanced) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        for (let i = 0; i < elements1.length; i++) {
          elements1[i].style.display = "inline-block";
        }
      } else {
        let buttonsToShow = 6;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
          for (let i = 0; i < elements1.length; i++) {
            elements1[i].style.display = "none";
          }
        }
      }
    },
    //市场修改
    marketmodification() {
      this.$emit("marketmodification");
    },
    //销售合同
    quotationcontract() {
      this.$emit("quotationcontract");
    },
    //合同存档
    Contractarchiving() {
      this.$emit("Contractarchiving");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 新增
    addClick() {
      this.$emit("addClick");
    },
    // 编辑
    editClick() {
      this.$emit("editClick");
    },
    // 订单下线
    auditClick() {
      this.$emit("auditClick");
    },
    auditClick1() {
      this.$emit("auditClick1");
    },
    //订单回退
    orderRollback() {
      this.$emit("orderRollback");
    },
    //订单取消
    ordercancellation() {
      this.$emit("ordercancellation");
    },
    //回退待下线
    BackToWaitOffline() {
      this.$emit("BackToWaitOffline");
    },
    // 订单确认
    // sureClick(){
    //   this.$emit('sureClick')
    // },
    // 型号删除
    delClick() {
      this.$emit("delClick");
    },
    ReviewSheet() {
      this.$emit("ReviewSheet");
    },
    changeOrder() {
      this.$emit("changeOrder");
    },
    Productionnotification() {
      this.$emit("Productionnotification");
    },
  },
  mounted() {
    // 监听屏幕宽度和高度变化
    window.addEventListener("resize", this.handleResize, true);
  },
  // 销毁监听
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.active {
  button {
    text-align: center;
    margin-top: 8px;
    margin-right: 10px;
  }
  height: 50px;
  line-height: 40px;
  float: right;
  // width:57%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  // .box {
  //   width: 106px;
  //   margin-top: 10px;
  //   text-align: center;
  //   .ant-btn {
  //     width: 80px;
  //     padding:0;
  //   }
  // }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
