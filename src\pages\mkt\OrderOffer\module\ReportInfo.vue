<!-- 市场管理 - 订单报价- 报价单 -->
<template>
  <div class="pdfDom" >
    <div id="pdfDom">
      <div>
        <div class="headerTitle">
          <div>
            <div style="text-align: center; font-weight: 500; font-size: 16px;">报价单</div>
          </div>
        </div>
        <div>
          <table>
            <tr>
              <td>名称</td>
              <td>值</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import htmlToPdf from '@/utils/htmlToPdf';
import $ from 'jquery';
export default {
  name: "ReportInfo",
  props:{
    laminationData: {
      type: Object,
      required: true,
    }
  },
  
  computed:{  },
  data() {
    return {
      data: [],
      that: this,
      height: window.document.documentElement.clientHeight - 158,
      style_:0
    }
  },
  methods: {  },
  mounted() {  }
}
</script>

<style lang="less" scoped>
.drillClss {
  left: 0;
  top: 0;
  padding: 23px 20px 20px;
  width: 100%;
  position: absolute;
  height: 100%;
}
.drillItem {
  width: 9px;
  position: absolute;
  background: #0000CC;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItem:after{
  content: '';
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #0000CC;
  position: absolute;
  bottom: -8px;
  left:-3px;
}
.pdfDom {
  overflow: auto;
  margin: -25px;
}
#pdfDom {

  padding: 25px;
}
.headerTitle {
  display: flex;
  margin-bottom: 20px;
  img {
    margin-left: 50px;
    height: 70px;
  }
  h2 {
    font-size: 22px;
    color: #000000;
    font-weight: 500;
  }
  p{
    font-size: 12px;
    color: #000000;
    font-weight: 500;
  }
  span {
    font-size: 16px;
    color: #990099;
    font-weight: 500;
  }
}
.reportTable{
  border: 1px solid;
  display: flex;
  flex-wrap: wrap;
  .thickness {
    width: 100%;
    text-align: right;
    padding-right: 15px;
    line-height: 30px;
    font-weight: 500;
    font-size: 16px;
    border: 1px solid;
  }
  .layerName {
    width: 60px;
    height: 100%;
    border: 2px solid;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
  }
  .ozlastClass {
    align-items: center;
  }
  .OZclass {
    height: 30px;
    display: flex;
    overflow: hidden;
    align-items: center;
    .ozContent{
      //border-bottom: 1px solid;
      width: 100%;
      margin-left: 20px;
      display: flex;
      align-items: end;
      .oz_bg {
        width: 100%;
        border-radius: 7px;
        height: 10px;
        overflow: hidden;
        //background: url("../../../assets/img/pp.png") repeat-x;
        background-position-x: -12px;
      }
      .oz_active {
        //background: url("../../../assets/img/pp2.png") repeat-x;
        background-position-x: -12px;
      }
    }
  }
  .PPclass {
    overflow: hidden;
    .PPContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 12px;
      margin: 5px 0;
      background: #9ACD32;
      border-radius: 5px;

    }
  }
  .coreClass {
    .ozActive:nth-of-type(1) {
      margin-bottom: 20px;
    }
    position: relative;
    .coreActive {
      position: absolute;
      width: 100%;
      top: 16px;
    }
    .ozContent2 {
      border-bottom: 2px solid black;
      width: 100%;
    }
    .core-box {
      height: 48px;
      width: 100%;
      overflow: hidden;
      .CoreContent {
        height: 100%;
        float: right;
        background: #FCB505;
        width: calc(100% - 68px);
        background: #FCB408;
      }
    }

  }
  .GBClass{
    overflow: hidden;
    .gbContent{
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 22px;
      margin: 3px 0;
      background: #FCB505;
      border-radius: 5px;
    }
  }
  .parameterClass {
    flex: 1;
    table tr th {
      text-align: center;
      width: 80px;
    }
    table tr th:nth-child(2) {
      width: 100px;
    }
    table tr th:nth-child(3) {
      width: 140px;
    }
    table tr th:nth-child(4) {
      width: 60px;
    }
    table tr th:nth-child(5) {
      width: 100px;
    }
    table {
      border-left: 1px solid black;
    }
    table tr:nth-child(1) {
      border: 1px solid black;
      border-left: none;

    }
    table tr th {
      border-right: 1px solid black;
    }
    table tr td {
      text-align: center;
      color: #0000CC;
      border-right:1px solid black;
      border-bottom:1px solid black ;
    }
    .paramOZ{
      height: 27px;
      line-height: 27px;
    }
    .paramCore {
      height: 80px;
      td {
        white-space: pre;
      }

      //line-height: 30px;
    }
    .paramPP {
      height: 19px;
      line-height: 19px;
    }
  }
}
.impedance {
  display: flex;
  flex-wrap: wrap;
  padding-left: 5px;
  h4 {
    width: 100%;
  }
  h3 {
    display: flex;
    width: 250px;
    justify-content: space-between;
    p{
      margin: 0;
      margin-right: 10px;
    }
    p:nth-child(1) {
      margin: 0;
    }
  }
  // .imp_left{
  // }
  .line_flex {
    display: flex;
    p{
      margin: 0;
      margin-right: 15px;
      font-size: 14px;
      line-height: 25px;
      font-weight: 500;
    }
    p:nth-child(2) {
      width: 60px;
    }
  }
  .imp_center{
    margin: 10px 40px;
    img {
      padding: 15px;
      background: #008181;
    }
  }
  .imp_right{
    .line_flex {
      p:nth-child(1) {
        width: 40px;
      }
    }
  }
}
/deep/ .ant-table-body {
  .ant-table-thead {
    tr>th {
      padding: 5px 0;
      border-color: black;
      &:last-child {
        border-right: 0;
      }
    }
  }
  .ant-table-tbody {
    tr>td {
      padding: 7px 0;
      border-color: black;
      &:last-child {
        border-right: 0;
      }
    }

  }
}
</style>
