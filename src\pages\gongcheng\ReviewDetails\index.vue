<template>
  <a-spin :spinning="spinning">
    <div class="Review" @click="bodyClick">
      <div style="display: flex; justify-content: space-between">
        <div
          style="
            margin-top: 0;
            margin-bottom: 0.5em;
            color: #cf1b26;
            font-weight: 700;
            text-align: left;
            margin-left: 10px;
            float: left;
            font-size: 24px;
          "
        >
          {{ OrderNo }}
        </div>
        <div>
          <a-button
            type="primary"
            @click="Submitforreview"
            style="margin-right: 10px"
            v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementReviewPageLickSending')"
            >提交评审</a-button
          >
          <a-button
            type="primary"
            @click="Savereview"
            style="margin-right: 10px"
            v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementReviewSave') && edit"
            >保存评审</a-button
          >
          <a-button
            type="primary"
            @click="Cancelreview"
            style="margin-right: 10px"
            v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementReviewSave') && edit"
            >取消编辑</a-button
          >
          <a-button
            type="primary"
            @click="Editreview"
            style="margin-right: 10px"
            v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementReviewSave') && !edit"
            >编辑评审</a-button
          >
        </div>
      </div>
      <div style="width: 100%; display: flex; padding: 10px" class="information">
        <div style="border: 1px solid #ddd; width: 7%; text-align: center; height: 211px; line-height: 211px; color: #000000">基本信息</div>
        <div style="width: 93%; border-top: 1px solid #ddd; border-left: 1px solid #ddd" id="ReviewformData">
          <a-row>
            <a-col :span="10">
              <a-form-model-item :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }" :label="'评审类别'">
                <a-select
                  v-if="edit"
                  showSearch
                  allowClear
                  v-model="reviewInfo.reviewType"
                  :filter-option="filterOption"
                  @change="setEstimate($event, mapKey(selectData.ReviewCategory), 'reviewType')"
                  @search="handleSearch($event, mapKey(selectData.ReviewCategory), 'reviewType')"
                  @blur="handleBlur($event, mapKey(selectData.ReviewCategory), 'reviewType')"
                >
                  <a-select-option v-for="(item, index) in mapKey(selectData.ReviewCategory)" :key="index" :value="item.value">{{
                    item.lable
                  }}</a-select-option>
                </a-select>
                <div v-else>{{ reviewInfo.reviewType }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="10">
              <a-form-model-item :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }" :label="'提出人'">
                <div>{{ reviewInfo.createUserName }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'部门审核人'">
                <a-input v-if="edit" v-model="reviewInfo.reviewer"></a-input>
                <div v-else>{{ reviewInfo.reviewer }}</div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'生产型号'">
                <div :title="reviewInfo.orderNo">{{ reviewInfo.orderNo }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'客户型号'">
                <div :title="reviewInfo.customerModel">{{ reviewInfo.customerModel }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'订单号'">
                <div>{{ reviewInfo.businessOrderNo }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'客户代码'">
                <div>{{ reviewInfo.custNo }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'成品板厚(mm)'">
                <div>{{ reviewInfo.boardThickness }}</div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'最小钻咀(mm)'">
                <div>{{ reviewInfo.minJoran }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'内层隔离环(mm)'">
                <a-input v-if="edit" v-model="reviewInfo.innerIsolationRing"></a-input>
                <div v-else>{{ reviewInfo.innerIsolationRing }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'外层环宽(mm)'">
                <a-input v-if="edit" v-model="reviewInfo.outerRingWidth"></a-input>
                <div v-else>{{ reviewInfo.outerRingWidth }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'芯板厚度(mm)'">
                <a-input v-model="reviewInfo.coreBoardTthickness" v-if="edit"></a-input>
                <div v-else>{{ reviewInfo.coreBoardTthickness }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'板材类型'">
                <div>{{ reviewInfo.fR4TypeStr }}</div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'孔到孔间距(mm)'">
                <a-input v-if="edit" v-model="reviewInfo.drlToDrl"></a-input>
                <div v-else>{{ reviewInfo.drlToDrl }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'内层线宽线距(mm)'">
                <a-input v-if="edit" v-model="reviewInfo.innerMinLineSpace"></a-input>
                <div v-else>{{ reviewInfo.innerMinLineSpace }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'外层线宽线距(mm)'">
                <a-input v-if="edit" v-model="reviewInfo.outerMinLineSpace"></a-input>
                <div v-else>{{ reviewInfo.outerMinLineSpace }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'孔铜(um)'">
                <div>{{ reviewInfo.holeCopper }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'阻焊颜色'">
                <div>{{ reviewInfo.solderColorStr }}/{{ reviewInfo.solderColorBottomStr }}</div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'纵横比'">
                <div>{{ reviewInfo.apertureRatio }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'内层基铜(um)'">
                <div>{{ reviewInfo.innerBaseCopper }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'内层完成铜厚(um)'">
                <div>{{ reviewInfo.innerCopperThickness }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'外形到铜(mm)'">
                <a-input v-if="edit" v-model="reviewInfo.appearanceToCopper"></a-input>
                <div v-else>{{ reviewInfo.appearanceToCopper }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'过孔处理'">
                <div>{{ reviewInfo.solderCoverStr }}</div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'盲埋孔径(mm)'">
                <div>{{ reviewInfo.vias }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'外层基铜(um)'">
                <div>{{ reviewInfo.outerBaseCopper }}</div>
              </a-form-model-item></a-col
            >
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'外层完成铜厚(um)'">
                <div>{{ reviewInfo.copperThickness }}</div>
              </a-form-model-item></a-col
            >
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'表面处理'">
                <div>{{ reviewInfo.surfaceFinishStr }}</div></a-form-model-item
              ></a-col
            >
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'订单面积㎡'">
                <div>{{ reviewInfo.delArea }}</div>
              </a-form-model-item></a-col
            >
          </a-row>
          <a-row>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'层数'">
                <div>{{ reviewInfo.boardLayers }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :label="'交货尺寸(mm)'">
                <div>{{ reviewInfo.deliverySize }}</div>
              </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 24 }"> </a-form-model-item>
            </a-col>
            <a-col :span="5">
              <a-form-model-item :label-col="{ span: 24 }"> </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item :label-col="{ span: 24 }"> </a-form-model-item>
            </a-col>
          </a-row>
        </div>
      </div>
      <div style="padding: 0 10px">
        <table border style="border-color: #e1e1e2; color: #000000; width: 100%">
          <thead>
            <tr>
              <th style="width: 5%; text-align: center">评审次数</th>
              <th style="width: 5%; text-align: center">序号</th>
              <th style="width: 10%; text-align: center">操作时间</th>
              <th style="width: 5%; text-align: center">操作人员</th>
              <th style="width: 50%; text-align: center">评审详情</th>
              <th style="width: 15%; text-align: center">附件</th>
              <th style="width: 10%; text-align: center">操作</th>
            </tr>
          </thead>
          <tr colspan="6" style="position: relative; height: 34px">
            <span style="line-height: 34px; position: absolute; right: 50%; color: #ff9900; cursor: pointer" @click="addClick">+添加评审</span>
          </tr>
        </table>
      </div>
      <div style="height: 420px; overflow: auto; border: 1px solid #e1e1e2" class="scorllclass">
        <a-collapse :activeKey="copyorderData.length">
          <a-collapse-panel v-for="(val, inde) in copyorderData" :key="(inde + 1).toString()">
            <template #header>
              <div style="text-align: left">
                第 <span style="color: rgb(207, 27, 38); font-weight: 700; font-size: 16px">{{ inde + 1 }}</span> 次评审
              </div>
            </template>
            <table border style="border-color: #e1e1e2; color: #000000; border-top-color: #ffffff00">
              <tbody>
                <template v-for="(item, index) in val">
                  <tr :key="'1' + index">
                    <td style="width: 5%" v-if="item.num" :rowspan="item.num">{{ item.reviewNumber }}</td>
                    <td style="width: 5%" :rowspan="item.reply ? 2 : 1">
                      Q<i>{{ qsort(item.id) }}</i>
                    </td>
                    <td style="width: 10%">{{ item.createTime }}</td>
                    <td style="width: 5%">{{ item.userName }}</td>
                    <td style="width: 50%" class="left">
                      <div>
                        <p>问题描述：{{ item.contentS }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentA)">建议方案一：{{ item.contentA }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentB)">建议方案二：{{ item.contentB }}</p>
                        <p v-if="!isEmptyOrWhitespace(item.contentC)">建议方案三：{{ item.contentC }}</p>
                      </div>
                    </td>
                    <th style="width: 15%; text-align: center">
                      <div v-if="item.image != []" v-viewer>
                        <span v-for="(ite, ind) in item.image" :key="ind" style="color: #0068ff; cursor: pointer; text-decoration: underline">
                          <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                          ><br />
                        </span>
                        <div v-viewer v-if="currentImageUrl" style="display: none">
                          <img :src="currentImageUrl" alt="附件图片" />
                        </div>
                      </div>
                      <div v-if="item.filePath != '' && item.filePath != ',' && item.filePath">
                        <span style="color: red; cursor: pointer" @click="down(item.filePath)"
                          >工程上传文件<br />（可点击下载{{ item.filePath.split(",").length }}个文件）</span
                        >
                      </div>
                    </th>
                    <td style="width: 10%" :rowspan="item.reply ? 2 : 1">
                      <div>
                        <p @click="editClick(item)" v-if="item.status == 1" style="cursor: pointer">+编辑问题</p>
                        <p @click="deleteClick(item)" v-if="item.status == 1" style="cursor: pointer">+删除评审</p>
                        <p @click="replyClick(item)" v-if="item.status == 3" style="cursor: pointer">+回复评审</p>
                        <p @click="backClick(item)" v-if="item.status == 3" style="cursor: pointer">+撤回评审</p>
                        <p @click="copy1Click(item)" v-if="item.status == 2" style="cursor: pointer">+复制评审</p>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="item.reply" :key="'2' + index">
                    <td style="width: 10%">{{ item.reply.solutionTime }}</td>
                    <td style="width: 5%">{{ item.reply.userName }}</td>
                    <td style="width: 50%" class="left">
                      <div>
                        <p style="color: #ff9900">回复内容：{{ item.reply.content }}</p>
                      </div>
                    </td>
                    <th style="width: 15%; text-align: center">
                      <div v-if="item.reply.image" v-viewer>
                        <span
                          v-for="(ite, ind) in item.reply.image.split(',')"
                          :key="ind"
                          style="color: #0068ff; cursor: pointer; text-decoration: underline"
                        >
                          <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                          ><br />
                        </span>
                      </div>
                      <div v-if="item.reply.filePath">
                        <span style="color: red; cursor: pointer" @click="down(item.reply.filePath)"
                          >文件（可点击下载{{ item.reply.filePath.split(",").length }}个文件）</span
                        >
                      </div>
                    </th>
                  </tr>
                </template>
              </tbody>
            </table>
          </a-collapse-panel>
        </a-collapse>
        <table border="1" v-if="addTr">
          <tr>
            <td style="width: 5%">
              <p>{{ currentreviewNum }}</p>
            </td>
            <td style="width: 5%">
              <p>
                Q<i>{{ orderData.length + 1 }}</i>
              </p>
            </td>
            <td style="width: 10%">
              <p>{{ addTime }}</p>
            </td>
            <td style="width: 5%">
              <p>{{ user }}</p>
            </td>
            <td style="text-align: left; width: 50%">
              <template>
                <a-form layout="inline">
                  <a-row>
                    <a-col :span="12">
                      <a-form-item label="评审类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-select showSearch allowClear optionFilterProp="label" v-model="askType">
                          <a-select-option v-for="(item, index) in selectData3" :key="index" :value="item.display_" :label="item.caption_">{{
                            item.caption_
                          }}</a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item style="width: 100%; margin: 0">
                        <span style="color: red; margin-left: 10px; font-size: 13px">*如为问题确认，不需要上传工程文件；反之则不需上传图片</span>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="12">
                      <a-form-item label="问题类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-select showSearch allowClear optionFilterProp="lable" v-model="reviewdata.keyType">
                          <a-select-option v-for="(item, index) in selectData1" :key="index" :value="item.text" :lable="item.text"
                            >{{ item.text }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item style="margin-left: 50px">
                        <a-button style="font-weight: 500" @click="click2">关键字</a-button>
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="问题描述" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-textarea :auto-size="{ minRows: 2, maxRows: 5 }" allowClear v-model="reviewdata.problemDescription" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="建议方案一" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-textarea
                          :auto-size="{ minRows: 1, maxRows: 3 }"
                          type="text"
                          placeholder="请撰写方案一"
                          allowClear
                          v-model="reviewdata.proposalA"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="建议方案二" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-textarea
                          :auto-size="{ minRows: 1, maxRows: 3 }"
                          type="text"
                          placeholder="请撰写方案二"
                          allowClear
                          v-model="reviewdata.proposalB"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item
                        label="建议方案三"
                        prop="proposalC"
                        :label-col="{ span: 4 }"
                        :wrapper-col="{ span: 16 }"
                        style="width: 100%; margin: 0"
                      >
                        <a-textarea
                          :auto-size="{ minRows: 1, maxRows: 3 }"
                          type="text"
                          placeholder="请撰写方案三"
                          allowClear
                          v-model="reviewdata.proposalC"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <span style="color: #ff9900; margin-left: 80px; font-size: 13px">如多方案请按相应输入框输入建议方案</span>
                </a-form>
              </template>
            </td>
            <td style="width: 15%">
              <template v-if="askType == 0">
                <a-upload
                  name="file"
                  ref="fileRef"
                  :before-upload="beforeUpload1"
                  :customRequest="httpRequest"
                  :file-list="fileListData"
                  @change="handleChange1"
                  @preview="handlePreview"
                >
                  <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                  <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(1)" title="点击 ctrl+V粘贴上传">
                    粘贴图片
                  </a-button>
                </a-upload>
                <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible = false" :width="900">
                  <img style="width: 100%; height: 100%" :src="previewImage" />
                </a-modal>
              </template>
              <template v-else>
                <a-upload
                  name="file"
                  ref="fileRef"
                  :before-upload="beforeUpload1"
                  :customRequest="httpRequest"
                  :file-list="fileList"
                  @change="handleChange2"
                  :show-upload-list="true"
                >
                  <a-button> 上传文件 </a-button>
                </a-upload>
              </template>
            </td>
            <td style="width: 10%">
              <p><a-button type="primary" @click="uploadclick()" :loading="spinning1"> 提交</a-button></p>
              <p><a-button type="primary" @click="cancelclick"> 取消</a-button></p>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <a-modal
      title="关键字索引"
      :visible="dataVisible1"
      @cancel="reportHandleCancel"
      @ok="handleOk1"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="1100"
    >
      <key-word :selectData1="selectData1" ref="keyWord" :eqQuestion="eqQuestion" :StepName1="StepName1"></key-word>
    </a-modal>
    <!--    编辑问题弹窗-->
    <a-modal
      title="请输入问题描述"
      :visible="dataVisible2"
      @cancel="reportHandleCancel"
      @ok="handleOk2"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="600"
    >
      <div @click="bodyClick">
        <div>
          <a-form :model="editForm">
            <a-form-item>
              <a-textarea
                :auto-size="{ minRows: 1, maxRows: 3 }"
                v-model="editForm.problemDescription"
                placeholder="问题描述"
                allowClear
              ></a-textarea>
            </a-form-item>
            <a-form-item>
              <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalA" placeholder="输入建议方案一" allowClear></a-textarea>
            </a-form-item>
            <a-form-item>
              <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalB" placeholder="输入建议方案二" allowClear></a-textarea>
            </a-form-item>
            <a-form-item>
              <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalC" placeholder="输入建议方案三" allowClear></a-textarea>
            </a-form-item>
          </a-form>
        </div>
        <a-form-item style="padding: 10px">
          <template v-if="editForm.askType == '0'">
            <a-upload
              name="file"
              ref="fileRef"
              :before-upload="beforeUploadnew"
              :customRequest="httpRequest"
              :file-list="fileListData3"
              @change="handleChange3"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
              <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(3)" title="ctrl+V 粘贴上传">
                粘贴图片
              </a-button>
            </a-upload>
            <a-modal :visible="previewVisible" :footer="null" centered @cancel="handleCancelPreview">
              <img style="width: 100%; height: 100%" :src="previewImage" />
            </a-modal>
          </template>
          <template v-else>
            <a-upload name="file" ref="fileRef" :customRequest="httpRequest" :file-list="fileList4" @change="handleChange4">
              <a-button> 上传文件 </a-button>
            </a-upload>
          </template>
        </a-form-item>
      </div>
    </a-modal>
    <!--    回复问题弹窗-->
    <a-modal
      title="请输入回复内容"
      :visible="dataVisible3"
      @cancel="reportHandleCancel"
      @ok="handleOk3"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="1000"
    >
      <div @click="bodyClick">
        <a-form>
          <!-- <template>
          <a-textarea v-model="replyForm.Quiz" :auto-size="{ minRows: 2, maxRows:5 }" ></a-textarea>  
        </template> -->
          <div style="display: flex">
            <div style="width: 48%">
              <div>问题描述：{{ replyForm.contentS }}</div>
              <div v-if="replyForm.image1 != '' || replyForm.image1 != []" v-viewer>
                <img style="height: 50px; width: 50px; margin: 10px" v-for="(ite, index) in replyForm.image1" :key="index" :src="ite" />
              </div>
              <a-radio-group v-model="replyForm.value" @change="radioChange" style="display: flex; flex-direction: column">
                <a-radio style="height: 30px" :value="1" v-if="!isEmptyOrWhitespace(replyForm.contentA)">方案一： {{ replyForm.contentA }}</a-radio>
                <a-radio style="height: 30px" :value="2" v-if="!isEmptyOrWhitespace(replyForm.contentB)">方案二：{{ replyForm.contentB }}</a-radio>
                <a-radio style="height: 30px" :value="4" v-if="!isEmptyOrWhitespace(replyForm.contentC)">方案三：{{ replyForm.contentC }}</a-radio>
                <a-radio :value="3"
                  ><span style="display: inline-block; line-height: 60px">其他方案:</span>
                  <a-textarea
                    v-model="replyForm.proposalC"
                    style="width: 80%"
                    :auto-size="{ minRows: 2, maxRows: 5 }"
                    :disabled="replyForm.value == '3' ? false : true"
                  ></a-textarea>
                </a-radio>
              </a-radio-group>
            </div>
          </div>
          <div>
            <a-upload
              name="file"
              ref="fileRef"
              :before-upload="beforeUpload1"
              :customRequest="httpRequest"
              :file-list="fileListData2"
              @change="handleChange5"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
              <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(5)" title="ctrl+V 粘贴上传">
                粘贴图片
              </a-button>
            </a-upload>
            <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview" :width="900">
              <img style="width: 100%; height: 100%" :src="previewImage" />
            </a-modal>
            <a-upload
              name="file"
              ref="fileRef"
              :before-upload="beforeUpload11"
              :customRequest="httpRequest"
              :file-list="fileList2"
              @change="handleChange22"
            >
              <a-button v-if="fileList2.length < 1"> 上传文件 </a-button>
            </a-upload>
          </div>
        </a-form>
      </div>
    </a-modal>
    <!--数据填写错误提示弹窗-->
    <a-modal
      title="提示信息"
      :visible="Promptpopup"
      @cancel="Promptpopup = false"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      centered
      :width="400"
    >
      <div v-for="(item, index) in messageList" :key="index" style="line-height: 3ch">
        <span>{{ item.label }}</span>
        {{ item.value }}
      </div>
      <template #footer>
        <a-button type="primary" @click="Promptpopup = false"> 关闭 </a-button>
      </template>
    </a-modal>
  </a-spin>
</template>
<script>
import $ from "jquery";
import { checkPermission } from "@/utils/abp";
import moment from "moment";
import keyWord from "@/pages/gongcheng/projectPage/eqModule/keyWord";
import axios from "axios";
import { upLoadFlyingFile, getClassList, BigClassList, eqQuestion, getEqList, user } from "@/services/projectMake";
import {
  reviewmain,
  reviewcategory,
  setreviewinfosave,
  submit,
  editingproblems,
  replyproblems,
  setdeleetproblems,
  backProblems,
  toSendProblems,
} from "@/services/projectReview";
import { message } from "ant-design-vue";
export default {
  name: "ReviewDetails",
  components: { keyWord },
  data() {
    return {
      addTime: moment(new Date()).format("YYYY-MM-DD "),
      previewVisible: false,
      spinning1: false,
      Promptpopup: false,
      edit: false,
      fileListData2: [],
      spinning: false,
      currentreviewNum: 1,
      askType: 0, // 评审类型（0：问题确认，1：文件确认）
      eqQuestion: [],
      addTr: false,
      OrderNo: this.$route.query.OrderNo,
      reviewdata: {},
      isFileType: false,
      messageList: [],
      fileListData: [],
      fileList: [],
      arrData: [],
      orderData: [],
      fileList2: [],
      user: "",
      copyorderData: [],
      path: "",
      previewImage: "",
      showCopyType: "",
      file: null,
      startloading: false,
      show: false,
      reviewInfo: {},
      Copyreview: {},
      StepName1: "",
      selectData3: [],
      editForm: {
        id: "",
        problemDescription: "", // 问题描述
        proposalA: "", // 建议A
        proposalB: "", //建议B
        proposalC: "", //建议C
        path: "", // 图片地址
        filePath: "", // 文件地址
        askType: 0,
      },
      dataVisible1: false,
      dataVisible2: false,
      selectData1: [],
      allowAddTr: false,
      currentImageUrl: "",
      activeKey: ["1"],
      replyForm: {},
      selectData: {},
      fileListData3: [],
      fileList4: [],
      dataVisible3: false,
    };
  },
  created() {},
  mounted() {
    this.getreviewcategory();
    this.getOrderList();
    this.getSelect();
    this.getSelect1();
    let params = {
      StepName: "",
      Problem: "",
      KeyWord: "",
      Factoryid: this.$route.query.joinFactoryId,
    };
    eqQuestion(params).then(res => {
      if (res.code) {
        this.eqQuestion = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
  },
  methods: {
    checkPermission,
    handleCancelPreview() {
      this.previewVisible = false;
    },
    // 编辑问题
    editClick(item) {
      this.dataVisible2 = true;
      this.editForm.id = item.id;
      this.editForm.path = item.image;
      this.editForm.filePath = item.filePath;
      this.editForm.problemDescription = item.contentS;
      this.editForm.proposalA = item.contentA;
      this.editForm.proposalB = item.contentB;
      this.editForm.proposalC = item.contentC;
      this.editForm.askType = item.askType;
      this.fileListData3 = [];
      this.fileList4 = [];
      if (item.image) {
        item.image.forEach((e, index) => {
          const fileName = e.split("/").pop(); // 提取文件名
          this.fileListData3.push({
            uid: index,
            name: fileName,
            status: "done",
            url: e,
            thumbUrl: e, //缩略图地址
          });
        });
      }
      if (item.filePath) {
        item.filePath.split(",").forEach((e, index) => {
          const fileName = e.split("/").pop(); // 提取文件名
          this.fileList4.push({
            uid: index,
            name: fileName,
            status: "done",
            url: e,
          });
        });
      }
    },
    //删除问题
    deleteClick(item) {
      if (confirm("确认删除该问题吗？")) {
        let params = {
          id: item.id,
          reviewSource: Number(this.$route.query.reviewSource),
        };
        setdeleetproblems(item.id).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 回复问题
    replyClick(item) {
      this.dataVisible3 = true;
      this.replyForm = {};
      this.fileListData2 = [];
      this.replyForm.id = item.id;
      this.replyForm.contentS = item.contentS;
      this.replyForm.contentA = item.contentA;
      this.replyForm.contentB = item.contentB;
      this.replyForm.contentC = item.contentC;
      this.replyForm.image1 = item.image;
      this.replyForm.path = "";
      this.replyForm.image = "";
    },
    // 撤回问题
    backClick(item) {
      if (confirm("确认撤回该问题吗？")) {
        let params = {
          id: item.id,
          reviewSource: Number(this.$route.query.reviewSource),
        };
        backProblems(item.id).then(res => {
          if (res.code) {
            this.getOrderList();
            this.$message.success("撤回成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //复制问题
    copy1Click(item) {
      if (!this.allowAddTr) {
        this.$message.error("该订单有未回复的评审,暂不可以复制！");
        return;
      }
      if (confirm("确认复制该评审吗？")) {
        const newItem = { ...item };
        delete newItem.reply;
        newItem.status = 1;
        newItem.createTime = moment(new Date()).format("MM-DD HH:mm");
        this.orderData.push(newItem);
        this.uploadclick(newItem);
      }
    },
    handleChange5({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData2);
      }
      let namesArray = fileList.map(item => item.response);
      this.arrData2 = namesArray;
      this.replyForm.image = this.arrData2.toString(",");
      if (this.isFileType) {
        this.fileListData2 = fileList;
        if (this.fileListData2.length == 0) {
          this.replyForm.image = "";
        }
      }
    },
    handleChange22({ fileList }, data) {
      if (this.isFileType) {
        this.fileList2 = fileList;
        if (this.fileList2.length == 0) {
          this.replyForm.path = "";
        }
        this.replyForm.path = fileList.map(item => item.response)[0];
      }
    },
    handleOk2() {
      let params = this.editForm;
      if (this.isEmptyOrWhitespace(params.problemDescription)) {
        this.$message.warning("请输入问题描述");
        return;
      }
      if (this.isEmptyOrWhitespace(params.proposalA) && this.isEmptyOrWhitespace(params.proposalB) && this.isEmptyOrWhitespace(params.proposalC)) {
        this.$message.warning("请填写建议方案");
        return;
      }
      let str = "";
      this.fileListData3.forEach(e => {
        if (e.response) {
          str += e.response + ",";
        } else if (e.url) {
          str += e.url + ",";
        }
      });
      str = str.substring(0, str.length - 1);
      params.path = str;
      let str1 = "";
      this.fileList4.forEach(e => {
        if (e.response) {
          str1 += e.response + ",";
        } else if (e.url) {
          str1 += e.url + ",";
        }
      });
      str1 = str1.substring(0, str1.length - 1);
      params.filePath = str1;
      if (this.editForm.askType == "1" && !params.filePath) {
        this.$message.warning("文件确认请上传附件");
        return;
      }
      this.dataVisible2 = false;
      editingproblems(params).then(res => {
        if (res.code) {
          this.$message.success("编辑成功");
          this.fileListData3 = [];
          this.arrData1 = [];
          this.fileList4 = [];
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 编辑删除图片
    delClick(item) {
      if (confirm("确认删除该图片？")) {
        this.editForm.path.splice(this.editForm.path.indexOf(item), 1);
      }
    },
    radioChange() {
      if (this.replyForm.value == "1") {
        this.replyForm.Quiz = this.replyForm.contentA;
      }
      if (this.replyForm.value == "2") {
        this.replyForm.Quiz = this.replyForm.contentB;
      }
      if (this.replyForm.value == "3") {
        this.replyForm.Quiz = this.replyForm.proposalC;
      }
      if (this.replyForm.value == "4") {
        this.replyForm.Quiz = this.replyForm.contentC;
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().trim().startsWith(input.toLowerCase()) == true;
    },
    setEstimate(value, list, type) {
      this.reviewInfo[type] = value;
    },
    handleSearch(value, list, type) {
      this.setEstimate(value, list, type);
    },
    handleBlur(value, list, type) {
      this.setEstimate(value, list, type);
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    //下载图片
    downimg(path) {
      this.currentImageUrl = path; // 设置当前图片URL
      this.$nextTick(() => {
        this.$viewerApi({
          images: [this.currentImageUrl],
        });
      });
    },
    // 下载工程文件
    down(item) {
      if (item) {
        var data = item.split(",");
        for (var i = 0; i < data.length; i++) {
          (function (i) {
            var name = "";
            if (data[i].indexOf("EQ%5C") != -1) {
              name = decodeURIComponent(data[i].split("EQ%5C")[1]);
            } else {
              name = decodeURIComponent(data[i].split("/").slice(-1)[0]);
            }
            const xhr = new XMLHttpRequest();
            xhr.open("GET", data[i], true);
            xhr.responseType = "blob";
            xhr.onload = function () {
              if (xhr.status === 200) {
                const blob = xhr.response;
                const link = document.createElement("a");
                link.href = window.URL.createObjectURL(blob);
                link.download = name;
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }
            };
            xhr.send();
          })(i);
        }
      }
    },
    sortby(img) {
      return this.imglist.findIndex(item => item == img) + 1;
    },
    qsort(id) {
      return this.orderData.findIndex(item => item.id == id) + 1;
    },
    handleOk3() {
      if (this.replyForm.value == undefined) {
        this.$message.warning("请选择方案！");
        return;
      }
      if (this.replyForm.value == "3") {
        this.replyForm.Quiz = this.replyForm.proposalC;
      }
      if (this.replyForm.Quiz == "") {
        this.$message.warning("请填写其他方案！");
        return;
      }

      this.dataVisible3 = false;
      let params = {
        id: this.replyForm.id,
        Quiz: this.replyForm.Quiz,
        image: this.replyForm.image,
        path: this.replyForm.path,
        reviewInfoId: this.reviewInfo.id,
      };
      replyproblems(params).then(res => {
        if (res.code) {
          this.$message.success("回复成功");
          this.replyForm.Quiz = "";
          this.replyForm.image = "";
          this.replyForm.path = "";
          this.replyForm.value = undefined;
          this.replyForm.proposalC = "";
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取订单评审记录
    getOrderList() {
      this.spinning = true;
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      reviewmain(JoinFactoryId, OrderNo, BusinessOrderNo)
        .then(res => {
          if (res.code) {
            this.imglist = [];
            this.orderData = res.data.reviewMain;
            this.reviewInfo = res.data.reviewInfo;
            let pathstring = "";
            res.data.reviewMain.forEach(item => {
              if (item.image) {
                pathstring += item.image + ",";
              }
              if (item.reply && item.reply.image) {
                pathstring += item.reply.image + ",";
              }
              if (item.parentId <= 0) {
                let content = item.content;
                var arr = content.split("|||");
                var arr_ = arr.length > 1 ? arr[1].split(";") : "";
                item.contentS = arr[0];
                item.contentA = arr_[0];
                item.contentB = arr_[1];
                item.contentC = arr_[2];
                let enContent = item.enContent;
                if (enContent) {
                  var arr_en = enContent.split("|||");
                  var arr_en_ = arr_en.length > 1 ? arr_en[1].split(";") : "";
                  item.contentS_en = arr_en[0];
                  item.contentA_en = arr_en_[0];
                  item.contentB_en = arr_en_[1];
                  item.contentC_en = arr_en_[2];
                }
                if (item.image) {
                  var a = item.image.split(",");
                  item.image = a;
                }
              }
            });
            this.imglist = pathstring.split(",");
            this.imglist.pop();
            this.allowAddTr = true;
            if (this.orderData.filter(item => item.status == 1).length > 0) {
              this.currentreviewNum = this.orderData.filter(item => item.status == 1)[0].reviewNumber;
            } else if (this.orderData.filter(item => item.status == 2).length > 0) {
              const maxNUm = Math.max(...this.orderData.filter(item => item.status == 2).map(item => item.reviewNumber));
              this.currentreviewNum = maxNUm + 1;
              if (this.orderData.filter(item => item.status == 3).length > 0) {
                this.allowAddTr = false;
              }
            }
            this.orderData = this.addNumProperty(this.orderData);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    isEmptyOrWhitespace(content) {
      if (!content) {
        return true;
      } else {
        return /^\s*$/.test(content);
      }
    },
    handleChange3({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData3);
      }
      let namesArray = fileList.map(item => item.response);
      this.arrData1 = namesArray;
      this.fileListData3 = fileList;
      if (this.fileListData3.length == 0) {
        this.editForm.path = "";
      }
    },
    handleChange4({ fileList }, data) {
      this.fileList4 = fileList;
      var a = [];
      if (this.fileList4.length == 0) {
        this.editForm.filePath = "";
      } else {
        for (let index = 0; index < fileList.length; index++) {
          a.push(fileList[index].response);
        }
        this.editForm.filePath = a.join(",");
      }
    },
    addNumProperty(arr) {
      if (arr.length == 0) {
        this.copyorderData = [];
      }
      const grouped = arr.reduce((acc, item) => {
        let key = item.reviewNumber;
        if (!key) {
          key = 0;
        }
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        this.copyorderData = Object.values(acc);
        return acc;
      }, {});
      let newArr = [];
      let fornum = 0;
      for (const [_, items] of Object.entries(grouped)) {
        fornum += items.length;
        let replyNum = 0;
        for (let i = 0; i < items.length; i++) {
          if (i == 0) {
            items[i].num = items.length;
          } else {
            items[i].num = 0;
          }
          if (items[i].reply) {
            replyNum++;
          }
          newArr.push(items[i]);
        }
        newArr[newArr.length - items.length].num = newArr[newArr.length - items.length].num + replyNum;
      }
      return newArr;
    },
    handleOk1() {
      this.dataVisible1 = false;
      let formData = this.$refs.keyWord.selectData;
      this.$set(this.reviewdata, "keyType", formData.stepName);
      this.$set(this.reviewdata, "problemDescription", formData.problemDescription);
      this.$set(this.reviewdata, "proposalA", formData.proposalA);
      this.$set(this.reviewdata, "proposalB", formData.proposalB);
      this.$set(this.reviewdata, "proposalC", formData.proposalC);
    },
    reportHandleCancel() {
      this.dataVisible4 = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.replyForm.Quiz = "";
      this.replyForm.image = "";
      this.replyForm.path = "";
      this.replyForm.value = undefined;
      this.replyForm.proposalC = "";
      this.fileListData3 = [];
      this.arrData1 = [];
      this.arrData2 = [];
      this.fileList4 = [];
    },
    // 关键字
    click2() {
      this.StepName1 = this.reviewdata.keyType;
      this.dataVisible1 = true;
    },
    getSelect() {
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      BigClassList(JoinFactoryId, OrderNo, BusinessOrderNo).then(res => {
        if (res.code) {
          this.selectData1 = res.data.sort((a, b) => a.iType - b.iType);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取评审下拉选择
    getSelect1() {
      let params = [1118];
      getClassList(params).then(res => {
        if (res.code) {
          this.selectData3 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getreviewcategory() {
      let data = this.$route.query;
      reviewcategory(data.joinFactoryId).then(res => {
        if (res.code) {
          this.selectData = res.data;
          console.log(this.mapKey(this.selectData.ReviewCategory), "11");
        }
      });
    },
    cancelclick() {
      this.addTr = false;
      this.fileListData = [];
      this.path = "";
    },
    // 提交
    uploadclick(item) {
      var val = {};
      if (item) {
        val.askType = item.askType;
        val.businessOrderNo = item.businessOrderNo;
        val.reviewSource = item.reviewSource;
        val.joinFactoryId = item.joinFactoryId;
        val.keyType = item.keyType;
        val.orderNo = item.orderNo;
        val.problemDescription = item.contentS;
        val.proposalA = item.contentA;
        val.proposalB = item.contentB;
        val.proposalC = item.contentC;
        val.reviewNumber = item.reviewNumber;
        if (item.image) {
          this.path = item.image.join(",");
        }
      } else {
        val = this.reviewdata;
      }
      if (!this.path && this.askType == 1) {
        this.$message.warning("请上传文件");
        return;
      }
      if (!val.keyType) {
        val.keyType = " ";
      }
      if (this.askType == 0 && !item) {
        if (this.isEmptyOrWhitespace(val.problemDescription)) {
          this.$message.warning("请填写问题描述");
          return;
        }
        if (this.isEmptyOrWhitespace(val.proposalA) && this.isEmptyOrWhitespace(val.proposalB) && this.isEmptyOrWhitespace(val.proposalC)) {
          this.$message.warning("请填写建议方案");
          return;
        }
      }
      let params = val;
      if (params.problemDescription) {
        var arr1 = params.problemDescription.split("");
        if (arr1.length > 500) {
          arr1 = arr1.slice(0, 500);
        }
        params.problemDescription = arr1.join("");
      }

      if (params.proposalA) {
        var arr2 = params.proposalA.split("");
        if (arr2.length > 200) {
          arr2 = arr2.slice(0, 200);
        }
        params.proposalA = arr2.join("");
      }

      if (params.proposalB) {
        var arr3 = params.proposalB.split("");
        if (arr3.length > 200) {
          arr3 = arr3.slice(0, 200);
        }
        params.proposalB = arr3.join("");
      }

      if (params.proposalC) {
        var arr4 = params.proposalC.split("");
        if (arr4.length > 200) {
          arr4 = arr4.slice(0, 200);
        }
        params.proposalC = arr4.join("");
      }
      params.orderNo = this.$route.query.OrderNo;
      if (this.$route.query.reviewSource) {
        params.reviewSource = Number(this.$route.query.reviewSource);
      }
      params.joinFactoryId = this.$route.query.joinFactoryId;
      params.businessOrderNo = this.$route.query.businessOrderNo;
      params.askType = this.askType;
      this.spinning1 = true;
      params.reviewNumber = this.currentreviewNum;
      if (this.path) {
        const pathArray = this.path.split(",").filter(element => element !== "" && element);
        this.path = pathArray.join(",");
      }
      params.path = this.path;
      submit(params)
        .then(res => {
          if (res.code) {
            if (!item) {
              this.$message.success("提交成功");
            }
            this.fileListData = [];
            this.fileList = [];
            this.arrData = [];
            this.path = "";
            this.askType = 0;
            this.addTr = false;
            this.reviewdata = {};
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning1 = false;
        });
    },
    Editreview() {
      this.edit = true;
      this.Copyreview = JSON.parse(JSON.stringify(this.reviewInfo));
    },
    Cancelreview() {
      this.reviewInfo = this.Copyreview;
      this.edit = false;
    },
    Savereview() {
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      this.messageList = [];
      let params = {
        innerIsolationRing: "内层隔离环(mm)",
        outerRingWidth: "外层环宽(mm)",
        coreBoardTthickness: "芯板厚度(mm)",
        drlToDrl: "孔到孔间距(mm)",
        innerMinLineSpace: "内层线宽线距(mm)",
        outerMinLineSpace: "外层线宽线距(mm)",
        appearanceToCopper: "外形到铜(mm)",
      };
      Object.keys(params).forEach(key => {
        if (this.reviewInfo[key] && !x.test(this.reviewInfo[key])) {
          this.messageList.push({ label: params[key], value: "请输入正确格式" });
        }
      });
      if (this.messageList.length) {
        this.setstyle(this.messageList);
        this.Promptpopup = true;
        return;
      }
      this.spinning = true;
      setreviewinfosave(this.reviewInfo)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.edit = false;
        });
    },
    setstyle(val) {
      $("#ReviewformData .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele.label)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
    Submitforreview() {
      let params = {
        orderNo: this.$route.query.OrderNo,
        reviewSource: Number(this.$route.query.reviewSource),
        joinFactoryId: this.$route.query.joinFactoryId,
        businessOrderNo: this.$route.query.businessOrderNo,
      };
      if (confirm("您确认要提交全部评审吗？")) {
        toSendProblems(params).then(res => {
          if (res.code) {
            this.getOrderList();
            this.$message.success("提交成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    addClick() {
      if (!this.allowAddTr) {
        this.$message.error("该订单有未回复的评审,暂不可以新增加问题！");
        return;
      }
      this.addTr = true;
      this.reviewdata = {};
      user().then(res => {
        if (res.code) {
          this.user = res.data.realName_;
        }
      });
    },
    beforeUpload1(file) {
      if (this.askType == 1) {
        // const hasChinese = /[\u4e00-\u9fa5]/.test(file.name);
        // this.isFileType =  !hasChinese
        // if (!this.isFileType) {
        //   this.$message.error('文件确认上传文件不得含有中文字符');
        // }
        // return this.isFileType
      } else {
        this.isFileType = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!this.isFileType) {
          this.$message.error("问题确认只支持.jpg/.png图片格式文件");
        }
        return this.isFileType;
      }
    },
    async httpRequest(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    beforeUploadnew(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("图片只支持.jpg/.png图片格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    beforeUpload11(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
      if (!this.isFileType) {
        this.$message.error("文件确认只支持.rar或.zip格式文件");
      }
      return this.isFileType;
    },
    handleChange2({ fileList }, data) {
      if (this.isFileType) {
        this.fileList = fileList;
        var a = [];
        if (this.fileList.length == 0) {
          this.path = "";
        } else {
          for (let index = 0; index < fileList.length; index++) {
            a.push(fileList[index].response);
          }
          this.path = a.join(",");
        }
      }
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData);
      }
      if (this.isFileType) {
        const removedFiles = this.fileListData.filter(file => !fileList.includes(file));
        removedFiles.forEach(file => {
          const removedElement = file.response;
          if (removedElement && this.arrData.includes(removedElement)) {
            this.arrData = this.arrData.filter(element => !element.includes(removedElement));
            this.path = this.arrData.toString(",");
          }
        });
        this.fileListData = fileList;
        for (let index = 0; index < this.fileListData.length; index++) {
          const element = this.fileListData[index].response;
          if (element && !this.arrData.includes(element)) {
            this.arrData.push(element);
            this.path = this.arrData.toString(",");
          }
        }

        if (this.fileListData.length === 0) {
          this.path = "";
        }
      }
    },
    showCopy(type) {
      window.addEventListener("paste", this.getClipboardFiles);
      this.showCopyType = type;
      try {
        navigator.clipboard.read().then(res => {
          const clipboardItems = res;
          if (clipboardItems[0].types[0].indexOf("image") > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then(b => {
              const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {
                type: clipboardItems[0].types[0],
              });
              this.file = files;
              this.getClipboardFiles();
            });
          } else {
            this.$message.error("粘贴内容不是图片");
            return;
          }
        });
      } catch (e) {
        ////console.log('出错了')
      }
    },
    bodyClick() {
      //console.log('bodyClick')
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload1(file); // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload1(file)) {
        this.startloading = true;
        const formData = new FormData();
        formData.append("file", file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file", // 接口地址
          method: "post",
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => {
            if (res.code) {
              //console.log('res',res)
              file.status = "done";
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              if (this.showCopyType == "1") {
                this.handleChange1(file, arr);
              } else if (this.showCopyType == "5") {
                this.handleChange5(file, arr);
              } else if (this.showCopyType == "3") {
                this.handleChange3(file, arr);
              }
              this.startloading = false;
              this.show = false;
            } else {
              this.$message.error(data.message || "网络异常,请重试或检查网络连接状态");
              this.startloading = false;
              this.show = false;
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
          });
      }
    },
    handlePreview(file) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 14px / 1.17 "微软雅黑", arial, \5b8b\4f53;
}
/deep/.searchPosElem1 {
  background-color: #ffffff;
  font: 14px / 1.17 "微软雅黑", arial, \5b8b\4f53;
}
.left {
  text-align: left !important;
}
.scorllclass {
  margin: 0 10px 10px 10px;
  table {
    border-color: #e1e1e2;
    color: #000000;
    border-top-color: #ffffff00;
    width: 100%;
  }
  table tr td {
    text-align: center;
  }
  /deep/.ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }
  &::-webkit-scrollbar {
    //整体样式
    width: 1px; //y轴滚动条粗细
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    background: #b6b5b4;
    // #fff9e6
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
.information {
  /deep/.ant-form-item .ant-form-item-label {
    font: 14px / 1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    height: 30px;
    line-height: 30px;
  }
  /deep/.ant-form-item .ant-form-item-control-wrapper .ant-form-item-control {
    line-height: inherit;
    padding: 0px 5px;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    height: 30px;
  }
  /deep/.ant-form-item-children {
    line-height: 30px;
    div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  /deep/.ant-input {
    height: 26px;
    margin-top: 2px;
  }
  /deep/.ant-select-selection {
    height: 26px;
    margin-top: 2px;
  }
  /deep/.ant-select {
    width: 100%;
  }
  /deep/ .ant-select-selection__rendered {
    line-height: 26px;
  }
}
.Review {
  height: 825px;
  min-width: 1670px;
  background: white;
  padding: 10px;
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
}
p {
  margin-bottom: 4px;
}
</style>
