<!--供应链  - 供应商 -->
<template>
  <div ref="SelectBox">
    <a-card class="boox">
      <div style="display: flex; justify-content: space-between; margin-top: 6px; margin-bottom: 6px">
        <div>
          <a-button
            type="primary"
            class="margin10"
            @click="$refs.addSupplier.openModal()"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementNew')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementNew') ? 'showclass' : ''"
            >{{ $t("add_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="btnSte('', '1')"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementTJ')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementTJ') ? 'showclass' : ''"
            >{{ $t("submit_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="btnSte('', '2')"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementTG')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementTG') ? 'showclass' : ''"
            >{{ $t("approve_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="btnSte('', '3')"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementBH')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementBH') ? 'showclass' : ''"
            >{{ $t("reject_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="audit()"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierTj')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierTj') ? 'showclass' : ''"
            >{{ $t("modifyapplication_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="audit1()"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierBH')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierBH') ? 'showclass' : ''"
            >{{ $t("rejectmodification_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="audit2()"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierTG')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierTG') ? 'showclass' : ''"
            >{{ $t("acceptmodification_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="exportData()"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierDC')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierDC') ? 'showclass' : ''"
            >{{ $t("export_button") }}</a-button
          >
          <a-button
            type="primary"
            class="margin10"
            @click="bill()"
            v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SenOrder')"
            :class="checkPermission('MES.CoordinationModule.SupplierManagement.SenOrder') ? 'showclass' : ''"
            >{{ $t("billingsettings_button") }}</a-button
          >
          <div v-if="buttonsmenu">
            <a-dropdown>
              <a-button type="primary" style="margin-left: 10px; width: 100px" @click.prevent> 按钮菜单栏 </a-button>
              <template #overlay>
                <a-menu class="tabRightClikBox3">
                  <a-menu-item
                    @click="$refs.addSupplier.openModal()"
                    v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementNew')"
                    >{{ $t("add_button") }}</a-menu-item
                  >
                  <a-menu-item @click="btnSte('', '1')" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementTJ')">{{
                    $t("submit_button")
                  }}</a-menu-item>
                  <a-menu-item @click="btnSte('', '2')" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementTG')">{{
                    $t("approve_button")
                  }}</a-menu-item>
                  <a-menu-item @click="btnSte('', '3')" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementBH')">{{
                    $t("reject_button")
                  }}</a-menu-item>
                  <a-menu-item @click="audit()" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierTj')">{{
                    $t("modifyapplication_button")
                  }}</a-menu-item>
                  <a-menu-item @click="audit1()" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierBH')">{{
                    $t("rejectmodification_button")
                  }}</a-menu-item>
                  <a-menu-item @click="audit2()" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierRapierTG')">{{
                    $t("acceptmodification_button")
                  }}</a-menu-item>
                  <a-menu-item @click="exportData()" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierDC')">{{
                    $t("export_button")
                  }}</a-menu-item>
                  <a-menu-item @click="bill()" v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SenOrder')">{{
                    $t("billingsettings_button")
                  }}</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
        <a-button type="primary" class="margin10" @click="refresh" style="margin-right: 10px">{{ $t("search_button") }}</a-button>
      </div>
      <div class="content">
        <a-table
          rowKey="guid_"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="pagination"
          @change="handleTableChange"
          :loading="loading"
          :scroll="{ y: 713, x: 1200 }"
          :customRow="onClickRow"
          :rowClassName="isRedRow"
          class="mainstyle"
        >
          <p slot="isUpLicense_" slot-scope="text">
            {{ text ? "有" : "无" }}
          </p>
          <p slot="isQuality_" slot-scope="text">
            {{ text ? "是" : "否" }}
          </p>
          <p slot="visit_" slot-scope="text">
            {{ text ? "是" : "否" }}
          </p>
          <p slot="isShow_" slot-scope="text">
            {{ text ? "是" : "否" }}
          </p>
          <div style="width: 80px" slot="dataIntegrity" slot-scope="text">
            <a-progress :percent="parseFloat(parseFloat(text * 100).toFixed(2))" size="small" />
          </div>
          <span slot="num" slot-scope="text, record, index" class="topCss">
            {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
          </span>

          <div slot="supplierName_" slot-scope="text, record">
            <span>
              {{ record.supplierName_ }}
            </span>
          </div>

          <span slot="action" slot-scope="record">
            <a
              href="javascript:;"
              @click="toDetail(record, '2')"
              v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SupplierManagementEdit')"
              >{{ $t("edit_button") }}</a
            >
            <a-divider type="vertical" />
            <a href="javascript:;" @click="toDetail(record, '1')">{{ $t("details_button") }}</a>
            <a-divider type="vertical" />
            <!-- <a href="javascript:;" @click="btnSte(record,'4')" >跟进</a>
                 <a-divider type="vertical" /> -->
            <a href="javascript:;" @click="toDetail(record, '1', '18')">{{ $t("calendar_button") }}</a>
            <a-divider type="vertical" />
            <a href="javascript:;" @click="toDetail(record, '1', '17')">{{ $t("configuration_button") }}</a>
          </span>
        </a-table>
      </div>
      <div class="bto"></div>

      <!--<div @keyup.enter="refresh">-->
      <!--</div>-->
      <!-- 修改申请弹窗 -->
      <a-modal
        title="确认弹窗"
        :visible="querenvisible"
        @ok="dequerenhandleOk"
        :confirmLoading="loading1"
        @cancel="handleCancel"
        centered
        :width="400"
      >
        <span>{{ messagelist }}</span>
      </a-modal>
      <!-- 查询 -->
      <a-modal title="提示" :visible="visible3" :confirm-loading="confirmLoading" @cancel="handleCancel3" centered>
        <template slot="footer">
          <a-button type="primary" @click="handleCancel5">重置</a-button>
          <a-button type="primary" @click="handleCancel3">取消</a-button>
          <a-button type="primary" @click="dehandleOk3">确定</a-button>
        </template>
        <a-form layout="horizontal">
          <a-form-item label="省市区" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-cascader :options="cityData" change-on-select placeholder="请选择" @change="onChangeData" v-model="chooseData" />
          </a-form-item>
          <a-form-item label="建档时间" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-range-picker v-model="timeValue" @change="onChange" />
          </a-form-item>

          <a-form-item label="工厂代码" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-input v-model="queryParam.factoryCode" placeholder="工厂代码" />
          </a-form-item>

          <a-form-item label="供应商名称" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-input v-model="queryParam.supplierName" placeholder="供应商名称" />
          </a-form-item>

          <a-form-item label="审核状态" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.approval" placeholder="审核状态" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option value=""> 请选择 </a-select-option>
              <a-select-option value="-1"> 待审批 </a-select-option>
              <a-select-option value="0"> 驳回 </a-select-option>
              <a-select-option value="1"> 已审批 </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="合作阶段" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.cooperation" placeholder="合作阶段" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option value=""> 请选择 </a-select-option>
              <a-select-option value="无意向"> 无意向 </a-select-option>
              <a-select-option value="有意向"> 有意向 </a-select-option>
              <a-select-option value="合作中"> 合作中 </a-select-option>
              <a-select-option value="试单中"> 试单中 </a-select-option>
              <a-select-option value="终止合作"> 终止合作 </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="跟进人" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.followUpUser" placeholder="跟进人" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option v-for="item in peopleList" :value="item.valueMember" :key="item.text">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="录入人" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.inUserNo_" placeholder="录入人" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option v-for="item in peopleList" :value="item.valueMember" :key="item.text">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="合作模式" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.cooperationMode_" placeholder="合作模式" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option value=""> 请选择 </a-select-option>
              <a-select-option value="零单"> 零单 </a-select-option>
              <a-select-option value="协同"> 协同 </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="协同品类" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.category" placeholder="协同品类" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option value=""> 请选择 </a-select-option>
              <a-select-option value="单面"> 单面 </a-select-option>
              <a-select-option value="铝基"> 铝基 </a-select-option>
              <a-select-option value="双面"> 双面 </a-select-option>
              <a-select-option value="多层"> 多层 </a-select-option>
              <a-select-option value="FPC"> FPC </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="协同品级" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.grade" placeholder="协同品级" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option value=""> 请选择 </a-select-option>
              <a-select-option value="精品"> 精品 </a-select-option>
              <a-select-option value="优品"> 优品 </a-select-option>
              <a-select-option value="标品"> 标品 </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="订单类型" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select v-model="queryParam.orderType" placeholder="订单类型" :getPopupContainer="() => this.$refs.SelectBox">
              <a-select-option value=""> 请选择 </a-select-option>
              <a-select-option value="打样"> 打样 </a-select-option>
              <a-select-option value="中小批量"> 中小批量 </a-select-option>
              <a-select-option value="大批量"> 大批量 </a-select-option>
            </a-select>
          </a-form-item>
          <!--</div>-->
        </a-form>
      </a-modal>
      <a-modal
        title="发单制作"
        :visible="visible4"
        :confirm-loading="confirmLoading"
        @ok="dehandleOk4"
        @cancel="handleCancel4"
        width="1300px"
        class="modelD"
      >
        <a-button
          type="primary"
          style="width: 80px"
          v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SenOrderChange') && !billEdit"
          @click="start"
        >
          编辑
        </a-button>
        <a-button
          type="primary"
          style="width: 80px"
          v-if="checkPermission('MES.CoordinationModule.SupplierManagement.SenOrderChange') && billEdit"
          @click="Cancelstart"
        >
          取消编辑
        </a-button>
        <a-button type="primary" @click="dehandleOk4" style="margin-left: 14px; margin-bottom: 14px; margin-top: 14px; width: 80px">保存</a-button>
        <a-button type="primary" @click="handleCancel4" style="margin-left: 14px; margin-bottom: 14px; margin-top: 14px; width: 80px">关闭</a-button>
        <div class="billContent" style="height: 600px">
          <a-table
            rowKey="index"
            :columns="billColumns"
            :dataSource="billSource"
            :pagination="false"
            @change="handleTableChange"
            :loading="loading"
            :scroll="{ y: 560 }"
            :customRow="onClickRow"
            class="bulltab"
          >
            <span slot="num1" slot-scope="text, record, index" class="topCss">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <div slot="negativeArea" slot-scope="record, text, index">
              <p v-if="billStat">{{ record }}</p>
              <a-input v-else v-model="text.negativeArea" :ref="'neg' + index"></a-input>
            </div>
            <div slot="positiveArea" slot-scope="record, text, index">
              <p v-if="billStat">{{ record }}</p>
              <a-input v-else v-model="text.positiveArea" :ref="'pos' + index"></a-input>
            </div>
            <div slot="orderMinArea" slot-scope="record, text, index">
              <p v-if="billStat">{{ record }}</p>
              <a-input v-else v-model="text.orderMinArea" :ref="'order' + index"></a-input>
            </div>
            <div slot="posiAndNegative" slot-scope="record, text">
              <select
                v-if="billStat"
                style="width: 80px; -webkit-appearance: none; border: none; color: black; text-align: center; font-weight: 500"
                v-model="text.posiAndNegative"
                disabled
                defalutOpen
              >
                <option v-for="list in billList" :value="list.key" :key="list.val">
                  {{ list.val }}
                </option>
              </select>
              <select
                style="width: 84px; height: 24px; border: 1px solid #d9d9d9; border-radius: 4px; font-weight: 500"
                v-model="text.posiAndNegative"
                v-else
              >
                <option v-for="item in billList" :value="item.key" :key="item.val" style="font-weight: 500">
                  {{ item.val }}
                </option>
              </select>
            </div>
            <div slot="orderForm" slot-scope="record, text">
              <!--<p v-if="billStat">{{record}}</p>-->
              <a-checkbox v-if="billStat" v-model="text.orderForm" disabled> </a-checkbox>
              <a-checkbox v-else v-model="text.orderForm"> </a-checkbox>
            </div>
          </a-table>
        </div>

        <template slot="footer">
          <div></div>
        </template>
      </a-modal>
      <add-supplier ref="addSupplier" @ok="getMarketData"></add-supplier>

      <a-modal title="提示" :visible="visible" :confirm-loading="confirmLoading" @ok="dehandleOk" @cancel="handleCancel" centered :width="400">
        <p v-if="typeBtn != 4">{{ ModalText }}</p>
        <div v-if="typeBtn == 4" style="display: flex; align-content: center; justify-content: center; align-items: center">
          <span>跟进人:</span>
          <a-select v-model="followUpUser" placeholder="跟进人" style="width: 200px; margin-left: 10px">
            <a-select-option v-for="item in peopleList" :value="item.valueMember" :key="item.text">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </div>
      </a-modal>
      <!-- 导出 -->
      <a-modal title="提示" :visible="visible2" :confirm-loading="confirmLoading" @ok="dehandleOk2" @cancel="handleCancel2" centered :width="400">
        <a-form-model :model="form1" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-item label="录入人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-select style="width: 100px" v-model="form1.inUserNo_" placeholder="录入人">
              <a-select-option v-for="item in peopleList" :value="item.valueMember" :key="item.text" :getPopupContainer="() => this.$refs.SelectBox">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="建档时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 17, offset: 1 }">
            <a-range-picker v-model="timeValue1" @change="onChange1" />
            <br />
          </a-form-item>
        </a-form-model>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import axios from "axios";
import addSupplier from "./components/addSupplier";
import {
  getData,
  commite,
  reject,
  agree,
  followPeople,
  getfollow,
  applicationChange,
  ignoreChange,
  passChange,
  finishChange,
  authorityChange,
  exportD,
  buillData,
  buillRepair,
} from "@/services/supplier/index";
import jsonList from "../../../../public/index";
import { checkPermission } from "../../../utils/abp";
let columnsEX = [
  {
    title: "供应商名称",
    dataIndex: "supplierName",
  },
  {
    title: "供应商代码",
    dataIndex: "factoryCode",
  },
  {
    title: "建档日期",
    dataIndex: "inDate",
  },
  {
    title: "录入人",
    dataIndex: "inName",
  },
  {
    title: "跟进人",
    dataIndex: "followName",
  },
  {
    title: "合作意向",
    dataIndex: "cooperation",
  },
  {
    title: "资料完整度",
    dataIndex: "dataIntegrity",
  },
];
let columns = [
  {
    title: "",
    dataIndex: "index",
    width: 50,
    scopedSlots: { customRender: "num" },
    key: "serial_num", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    dataIndex: "supplierName_",
    width: 200,
    scopedSlots: { customRender: "supplierName_" },
    align: "left",
    key: "company_name", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    dataIndex: "supplierNo_",
    width: 80,
    align: "left",
    ellipsis: true,
    key: "factory_code", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    width: 120,
    dataIndex: "inDate_",
    align: "left",
    key: "create_date", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    width: 120,
    dataIndex: "upDate_",
    align: "left",
    key: "modify_date", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    dataIndex: "editStatus",
    width: 90,
    align: "left",
    key: "modify_status", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    width: 120,
    dataIndex: "category",
    align: "left",
    key: "category", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    width: 100,
    dataIndex: "cooperation_",
    align: "left",
    key: "coop_stage", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    dataIndex: "cooperationMode_",
    width: 100,
    align: "left",
    key: "coop_mode", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    width: 100,
    dataIndex: "activeStatus",
    align: "left",
    key: "coop_status", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    width: 90,
    dataIndex: "inUserNo_",
    align: "left",
    key: "creator", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    dataIndex: "followUpName",
    width: 85,
    align: "left",
    key: "follower", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    dataIndex: "dataIntegrity",
    width: 110,
    scopedSlots: { customRender: "dataIntegrity" },
    align: "left",
    key: "completeness", // 添加 key 属性，对应翻译文件中的键名
  },
  {
    title: "",
    key: "actions",
    width: 300,
    scopedSlots: { customRender: "action" },
    align: "left",
  },
];
let billColumns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 50,
    align: "center",
    scopedSlots: { customRender: "num1" },
  },
  {
    title: "工厂",
    dataIndex: "code",
  },
  {
    title: "正负片",
    dataIndex: "posiAndNegative",
    scopedSlots: { customRender: "posiAndNegative" },
  },
  {
    title: "款数-",
    dataIndex: "negativeNum",
  },
  {
    title: "Pnl总数-",
    dataIndex: "negativePnlNum",
  },
  {
    title: "已发面积-",
    dataIndex: "negativeSendArea",
  },
  {
    title: "目标面积-",
    dataIndex: "negativeArea",
    scopedSlots: { customRender: "negativeArea" },
  },
  {
    title: "款数+",
    dataIndex: "positiveNum",
    width: 80,
  },
  {
    title: "Pnl总数+",
    dataIndex: "positivePnlNum",
  },
  {
    title: "已发面积+",
    dataIndex: "positiveSendArea",
  },
  {
    title: "目标面积+",
    dataIndex: "positiveArea",
    scopedSlots: { customRender: "positiveArea" },
  },
  {
    title: "合拼工单最小面积",
    dataIndex: "orderMinArea",
    scopedSlots: { customRender: "orderMinArea" },
    width: 150,
  },
  {
    title: "是否发单",
    dataIndex: "orderForm",
    scopedSlots: { customRender: "orderForm" },
  },
];
export default {
  name: "",
  inject: ["reload"],
  i18n: require("@/components/language/modules/supplierS/supplierS_i18n.js"),
  data() {
    return {
      add_button: "",
      loading1: false,
      messagelist: "",
      gid: "",
      form1: {},
      visible2: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      timeValue: undefined,
      timeValue1: undefined,
      visible: false,
      confirmLoading: false,
      ModalText: "",
      baseUrl: process.env.VUE_APP_API_JSON_URL,
      queryParam: {},
      loading: false,
      disabled: true,
      colNumber: 3,
      columns: columns,
      dataSource: [],
      selectedRowKeys: [],
      rootSubmenuKeys: ["sub1", "sub2", "sub4"],
      openKeys: ["0"],
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      menuData: [
        {
          name: "分类",
          id: "0",
          childer: [
            {
              name: "制程能力",
              id: "01",
            },
            {
              name: "主要客户",
              id: "02",
              childer: [],
            },
            {
              name: "生产设备",
              id: "03",
              childer: [],
            },
            {
              name: "体系认证",
              id: "04",
              childer: [],
            },
            {
              name: "工厂图库",
              id: "05",
              childer: [],
            },
            {
              name: "评估结果",
              id: "06",
              childer: [],
            },
            {
              name: "板料物料",
              id: "07",
              childer: [],
            },
            {
              name: "摆放日志",
              id: "08",
              childer: [],
            },
          ],
        },
      ],
      groupsData: [],
      trIndex: null,
      cityData: [],
      chooseData: [],
      typeBtn: "", //按钮类型
      guid: "",
      jsonList,
      peopleList: [],
      followUpUser: "", //分配跟进人
      pageStat: false, //分页点击不清除
      selectedRowKeys_cnt: [],
      recordData: {},
      columnsEX: columnsEX,
      // moreStat:false,
      visible3: false,
      visible4: false,
      querenvisible: false,
      billColumns: billColumns,
      billStat: true,
      billSource: [],
      billList: [
        {
          key: "0",
          val: "不限",
        },
        {
          key: "1",
          val: "正片",
        },
        {
          key: "2",
          val: "负片",
        },
        {
          key: "3",
          val: "铝基板",
        },
        {
          key: "4",
          val: "负片导电膜",
        },
      ],
      billEdit: false,
      buttonsmenu: false,
    };
  },
  watch: {
    "$i18n.locale": function (newLocale) {
      this.updateTableTitle();
    },
  },
  components: { addSupplier },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.dequerenhandleOk = this.debounce(this.querenhandleOk, 500);
    this.dehandleOk = this.debounce(this.handleOk, 500);
    this.dehandleOk2 = this.debounce(this.handleOk2, 500);
    this.dehandleOk4 = this.debounce(this.handleOk4, 500);
    this.dehandleOk3 = this.debounce(this.handleOk3, 500);
    this.$nextTick(() => {
      this.handleResize();
      this.getMarketData();
      this.getCityData();
      this.getpeople();
    });
  },
  methods: {
    updateTableTitle() {
      this.columns.forEach(column => {
        if (column.key) {
          column.title = this.$t(column.key);
        }
      });
    },
    handleResize() {
      var mainstyle = document.getElementsByClassName("mainstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (mainstyle && this.dataSource.length != 0) {
        mainstyle.style.height = window.innerHeight - 214 + "px";
      } else {
        mainstyle.style.height = 0;
      }
      var content = document.getElementsByClassName("content")[0];
      if (window.innerHeight <= 924) {
        content.style.height = window.innerHeight - 174 + "px";
      } else {
        content.style.height = "735px";
      }
      const elements = document.getElementsByClassName("showclass");
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (elements.length * 95 < window.innerWidth - 240) {
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "inline-block";
            this.buttonsmenu = false;
          }
        } else {
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "none";
            this.buttonsmenu = true;
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
          this.buttonsmenu = false;
        }
      }
      var paginnum = "";
      var footerwidth = window.innerWidth - 224;
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      } else {
        this.pagination.simple = false;
        this.pagination.size = "small";
        this.pagination.showSizeChanger = false;
        this.pagination.showQuickJumper = false;
      }
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.guid_ && record.guid_ == this.gid) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    //发单设置
    handleOk4() {
      if (!this.billEdit) {
        this.$message.error("该数据未进行编辑，请先编辑后再保存");
        return;
      }
      var x = /^(\d|[0-9]\d+)(\.\d+)?$/; //正浮点数
      var y = /^[-+]?\d+(\.\d+)?$/; //浮点数
      for (let i = 0; i < this.billSource.length; i++) {
        const element = this.billSource[i];
        if (element.negativeArea && !y.test(element.negativeArea)) {
          this.$message.error("目标面积-请输入正确格式");
          this.$refs["neg" + i].focus();
          this.$refs["neg" + i].select();
          return;
        }
        if (element.positiveArea && !x.test(element.positiveArea)) {
          this.$message.error("目标面积+请输入正确格式");
          this.$refs["pos" + i].focus();
          this.$refs["pos" + i].select();
          return;
        }
        if (element.orderMinArea && !x.test(element.orderMinArea)) {
          this.$message.error("合拼工单最小面积请输入正确格式");
          this.$refs["order" + i].focus();
          this.$refs["order" + i].select();
          return;
        }
      }
      this.loading = true;
      buillRepair(this.billSource)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
          this.billEdit = false;
          this.billStat = true;
        });
    },
    handleCancel4() {
      this.visible4 = false;
      this.billStat = true;
      this.billEdit = false;
    },
    start() {
      this.billStat = false;
      this.billEdit = true;
    },
    Cancelstart() {
      this.billStat = true;
      this.billEdit = false;
    },
    bill() {
      this.visible4 = true;
      this.loading = true;
      buillData()
        .then(res => {
          res.forEach((item, i) => {
            item.index = i;
          });
          this.billSource = res;
          this.$forceUpdate();
        })
        .finally(() => {
          this.loading = false;
        });
    },

    refresh() {
      this.visible3 = true;
    },
    handleOk3() {
      this.pagination.current = 1;
      this.getMarketData();
    },
    handleCancel3() {
      this.visible3 = false;
      this.queryParam = {};
      this.chooseData = [];
      this.eliminate();
    },
    handleCancel5() {
      this.queryParam = {};
      this.eliminate();
      this.chooseData = [];
    },

    // moreBtn(){
    //  this.moreStat=!this.moreStat
    // },

    handleOk2() {
      this.confirmLoading = true;
      let parmas = {
        StartDate: this.form1.Filingtimebegin,
        EndDate: this.form1.FilingtimeEnd,
        InUserNo: this.form1.inUserNo_,
      };
      exportD(parmas)
        .then(res => {
          if (res.code == 0) {
            this.$message.info(res.message);
          } else {
            // 导出表格的表头设置
            let allColumns = this.columnsEX;
            var columnNames = [];
            var columnValues = [];
            for (var i = 0; i < allColumns.length; i++) {
              columnNames[i] = allColumns[i].title;
              columnValues[i] = allColumns[i].dataIndex;
            }
            require.ensure([], () => {
              const { export_json_to_excel } = require("../../../vendor/Export2Excel");
              const tHeader = columnNames;
              const filterVal = columnValues;
              const list = res;
              const data = this.formatJson(filterVal, list);
              export_json_to_excel(tHeader, data, "导出excel列表demo");
              this.visible2 = false;
              this.timeValue1 = undefined;
            });
          }
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    handleCancel2() {
      this.visible2 = false;
      this.timeValue1 = undefined;
    },
    //导出
    exportData() {
      this.visible2 = true;
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]));
    },
    //中间列表勾选获取数据
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            this.gid = record.guid_;
            keys.push(record.guid_);
            this.selectedRowKeys_cnt = keys;
            this.recordData = record;
          },
        },
      };
    },
    //中间列表勾选获取数据
    onSelectChange_content(selectedRowKeys, selectedRows) {
      this.selectedRowKeys_cnt = selectedRowKeys;
    },

    audit() {
      if (this.recordData.guid_ == "" || this.recordData.guid_ == undefined) {
        this.$message.info("请选择数据");
      } else {
        this.querenvisible = true;
        this.ttype = "1";
        this.messagelist = "请确认提交修改申请吗？";
        // if(index=='1'){
        //     applicationChange(this.recordData.guid_).then(res=>{
        //         if(res.code!=1){
        //             this.$message.info(res.message)
        //         }
        //         this.getMarketData()
        //     })
        // }
        // if(index=='2'){
        //     ignoreChange(this.recordData.guid_).then(res=>{
        //         if(res.code!=1){
        //             this.$message.info(res.message)
        //         }
        //         this.getMarketData()
        //     })
        // }
        // if(index=='3'){
        //     passChange(this.recordData.guid_).then(res=>{
        //         if(res.code!=1){
        //             this.$message.info(res.message)
        //         }
        //         this.getMarketData()
        //     })
        // }
      }
    },
    audit1() {
      if (this.recordData.guid_ == "" || this.recordData.guid_ == undefined) {
        this.$message.info("请选择数据");
      } else {
        this.querenvisible = true;
        this.ttype = "2";
        this.messagelist = "请确认驳回修改吗？";
      }
    },
    audit2() {
      if (this.recordData.guid_ == "" || this.recordData.guid_ == undefined) {
        this.$message.info("请选择数据");
      } else {
        this.querenvisible = true;
        this.ttype = "3";
        this.messagelist = "请确认同意修改吗？";
      }
    },
    querenhandleOk() {
      this.loading1 = true;
      if (this.ttype == "1") {
        applicationChange(this.recordData.guid_)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("提交修改申请成功");
            } else {
              this.$message.info(res.message);
            }
            this.querenvisible = false;
            this.getMarketData();
          })
          .finally(() => {
            this.loading1 = false;
          });
      }
      if (this.ttype == "2") {
        ignoreChange(this.recordData.guid_)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("提交修改申请成功");
            } else {
              this.$message.info(res.message);
            }
            this.querenvisible = false;
            this.getMarketData();
          })
          .finally(() => {
            this.loading1 = false;
          });
      }
      if (this.ttype == "3") {
        passChange(this.recordData.guid_)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("提交修改申请成功");
            } else {
              this.$message.info(res.message);
            }
            this.querenvisible = false;
            this.getMarketData();
          })
          .finally(() => {
            this.loading1 = false;
          });
      }
    },

    eliminate() {
      // this.queryParam={}
      this.timeValue = undefined;
      this.timeValue1 = undefined;
    },
    checkPermission,
    //获取跟进人数据
    getpeople() {
      followPeople().then(res => {
        this.peopleList = res.data;
        this.peopleList.unshift({
          text: "请选择",
          valueMember: "",
        });
      });
    },
    onChange(date, dateString) {
      this.queryParam.Filingtimebegin = dateString[0];
      this.queryParam.FilingtimeEnd = dateString[1];
    },
    onChange1(date, dateString) {
      this.form1.Filingtimebegin = dateString[0];
      this.form1.FilingtimeEnd = dateString[1];
    },
    btnSte(val, index) {
      if (this.recordData.guid_ != "" && this.recordData.guid_ != undefined) {
        this.typeBtn = index;
        this.guid = this.recordData.guid_;
        this.visible = true;
        if (index == 1) {
          this.ModalText = "是否提交审批";
        } else if (index == 2) {
          this.ModalText = "是否通过审批";
        } else if (index == 3) {
          this.ModalText = "是否驳回审批";
        }
      } else {
        this.$message.info("请选择数据");
      }
    },
    onChangeData(val) {
      this.chooseData = val;
    },
    // //获取省市级数据
    getCityData() {
      // axios.get(`${this.baseUrl}/index.json`).then((data)=>{
      this.cityData = jsonList;
      // })
    },
    //获取供应商数据
    getMarketData() {
      this.pageStat = localStorage.getItem("stat");
      if (this.pageStat) {
        let pageCurrent = localStorage.getItem("pageCurrent");
        if (pageCurrent != undefined && pageCurrent != "") {
          this.pagination.current = parseInt(pageCurrent);
        }
        let pageSize = localStorage.getItem("pageSize");
        if (pageSize != undefined && pageSize != "") {
          this.pagination.pageSize = parseInt(pageSize);
        }
        let data = localStorage.getItem("queryParam");
        if (data != null && data != undefined && data != "") {
          this.queryParam = JSON.parse(data);
        }
      }
      if (this.chooseData[0] == undefined) {
        this.queryParam.province = "";
      } else {
        this.queryParam.province = this.chooseData[0];
      }
      if (this.chooseData[1] == undefined) {
        this.queryParam.city = "";
      } else {
        this.queryParam.city = this.chooseData[1];
      }
      if (this.chooseData[2] == undefined) {
        this.queryParam.area = "";
      } else {
        this.queryParam.area = this.chooseData[2];
      }
      this.queryParam.pageIndex = this.pagination.current;
      this.queryParam.pageSize = this.pagination.pageSize;
      let data = {
        ...this.queryParam,
      };
      localStorage.setItem("queryParam", JSON.stringify(data));
      this.loading = true;
      let indexId = localStorage.getItem("guid_");
      let record1 = localStorage.getItem("record1");
      getData(data)
        .then(res => {
          res.items.forEach((res, index) => {
            res.index = index + 1;
          });
          this.visible3 = false;
          this.eliminate();

          // let newSlotArr=[]
          // res.items.map(item=>{
          //     console.log(item.activeStatus);
          //     item.activeStatus=="休眠"?newSlotArr.push(item):newSlotArr.unshift(item)
          // })
          // this.dataSource = newSlotArr

          this.dataSource = res.items;
          setTimeout(() => {
            this.handleResize();
          }, 0);
          if (indexId !== "" && indexId != null) {
            let keys = [];
            keys.push(indexId);
            this.selectedRowKeys_cnt = keys;
            this.gid = indexId;
          }
          if (record1 !== "" && record1 != null) {
            this.recordData = JSON.parse(record1);
          }
          if (this.pageStat) {
            localStorage.removeItem("guid_");
            localStorage.removeItem("pageCurrent");
            localStorage.removeItem("pageSize");
            localStorage.removeItem("stat");
            localStorage.removeItem("record1");
          }
          // setTimeout(function () {
          //     res.items.find((item,index)=>{
          //         if( indexId==item.guid_){
          //             document.getElementsByClassName('ant-table-row')[index].style.background=''
          //         }
          //     })
          // },500)
          this.pagination.total = res.totalCount;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //查询详情
    toDetail(data, type, ids) {
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", this.pagination.pageSize);
      localStorage.setItem("record1", JSON.stringify(data));
      if (type == "1") {
        localStorage.setItem("guid_", data.guid_);
        localStorage.setItem("stat", true);
        this.pageStat = true;
        if (ids) {
          this.$router.push({ path: "/collaborative/detail", query: { id: data.guid_, type: type, ids: ids, factory: "供应商" } });
        } else {
          this.$router.push({ path: "/collaborative/detail", query: { id: data.guid_, type: type, factory: "供应商" } });
        }
      }
      if (type == "2") {
        localStorage.setItem("guid_", data.guid_);
        localStorage.setItem("stat", true);
        this.pageStat = true;
        this.$router.push({ path: "/collaborative/detail", query: { id: data.guid_, type: type, factory: "供应商" } });
      }
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      this.pageStat = false;
      localStorage.removeItem("stat");
      this.getMarketData();
    },
    onOpenChange(openKeys) {
      const latestOpenKey = openKeys.find(key => this.openKeys.indexOf(key) === -1);
      if (this.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
        this.openKeys = openKeys;
      } else {
        this.openKeys = latestOpenKey ? [latestOpenKey] : [];
      }
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },

    handleOk() {
      this.confirmLoading = true;
      if (this.typeBtn == 1) {
        commite(this.guid)
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              this.getMarketData();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.confirmLoading = false;
            this.visible = false;
          });
      } else if (this.typeBtn == "2") {
        agree(this.guid)
          .then(res => {
            this.$message.info(res.message);
            this.getMarketData();
          })
          .finally(() => {
            this.visible = false;
            this.confirmLoading = false;
          });
      } else if (this.typeBtn == "3") {
        reject(this.guid)
          .then(res => {
            this.$message.info(res.message);
            this.getMarketData();
          })
          .finally(() => {
            this.visible = false;
            this.confirmLoading = false;
          });
      } else if (this.typeBtn == "4") {
        let param = {};
        param.id = this.guid;
        param.followUpUser = this.followUpUser;
        getfollow(param)
          .then(res => {
            this.getMarketData();
          })
          .finally(() => {
            this.visible = false;
            this.confirmLoading = false;
          });
      }
    },
    handleCancel(e) {
      this.visible = false;
      this.followUpUser = "";
      this.querenvisible = false;
    },
  },
  mounted() {
    this.updateTableTitle();
    window.addEventListener("resize", this.dehandleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.dehandleResize, true);
  },
};
</script>

<style scoped lang="less">
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  margin-left: 15px;
  margin-top: 9px;
  position: absolute;
}
/deep/.ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
.billContent {
  /deep/.ant-table-tbody > tr > td {
    height: 37px;
  }
  /deep/ p {
    margin-top: 0;
    margin-bottom: 0;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-scroll {
    border: 1px solid #efefef;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/.ant-input {
    height: 24px;
    width: 84px;
  }
}

/deep/.ant-progress-text {
  color: #000000;
  // font-size:14px;
}
/deep/.select-dropdown-menu a {
  font-weight: 500;
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/.ant-table-thead > tr > th {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}
/deep/.ant-calendar-range-picker-input {
  font-weight: 500;
}

/deep/.ant-modal-title {
  font-weight: 500;
}
.margin10 {
  margin-left: 10px;
}
.ant-form-item {
  margin-bottom: 10px !important;
}
/deep/.ant-card-body {
  padding: 0 !important;
}
.content {
  border: 2px solid #f5f5f5;
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-tbody > tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
}
.bto {
  height: 42px;
  border: 2px solid #f5f5f5;
}
/deep/.ant-table-thead > tr > th {
  text-align: center;
}
/deep/.ant-table-tbody > tr > td {
  text-align: center;
}
/deep/.ant-table-tbody > tr > td:first-child {
  text-align: center;
  padding: 0;
}
// /deep/.ant-table-row-selected td{
//     background: #FFFAF2!important;
//     color: #FF9900!important;
// }
/deep/.ant-table-thead > tr > th {
  padding: 6px 7px;
  border-right: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 2px 7px;
  border-right: 1px solid #efefef;
}
/deep/ .ant-table-body {
  // overflow: auto !important;
  overflow-y: scroll !important;
}
.modelD /deep/.ant-modal-body {
  padding: 0 25px;
}
</style>
