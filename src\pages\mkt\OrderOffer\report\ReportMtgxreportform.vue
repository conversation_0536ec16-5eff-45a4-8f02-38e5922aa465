<!--明天高新报价表单  -->
<template>
  <div class="pdfDom1" style="font-size: 13px">
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="Mtgxreport" style="padding: 25px; font-family: 宋体; color: black; font-weight: 600; position: relative">
      <div style="text-align: center; width: 100%; font-size: 20px; font-weight: bold; padding-bottom: 10px">
        <div style="display: flex; justify-content: center">
          <img src="@/assets/img/mtgxlogo.png" />
          <div style="padding-left: 20px">
            <div style="font-size: 30px; font-family: 黑体">成都明天高新产业有限责任公司</div>
            <div>Chengdu Tomorrow High Technology Co.,Ltd</div>
            <div>报 价 单</div>
          </div>
        </div>
      </div>
      <a-divider style="height: 4px; background-color: black" />
      <a-divider style="height: 2px; background-color: black" />
      <div style="display: flex; line-height: 4ch; margin-top: 15px">
        <div style="width: 30%; z-index: 99">
          <div>客户名称:{{ Mtgxreportdata.value_1 }}</div>
          <div>联系人:{{ Mtgxreportdata.value_2 }}</div>
          <div>电话:{{ Mtgxreportdata.value_3 }}</div>
        </div>
        <div style="z-index: 99; width: 50%">
          <div style="float: right">
            <div>报价单号:{{ Mtgxreportdata.value_5 }}</div>
            <div>报价日期:{{ date }}</div>
          </div>
        </div>
      </div>
      <div>
        <div style="margin-top: 10px">一、报价明细</div>
        <table border="1" style="text-align: center; margin-top: 5px; width: 100%; border-top: 1px solid black; border-left: 1px solid black">
          <thead>
            <tr style="font-size: 15px">
              <td rowspan="2">序号</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price1 !== '/')">产品名称</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price2 !== '/')">加工数量</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price3 !== '/')">层数</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price4 !== '/')">材质</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price5 !== '/')" style="max-width: 80px">成品基铜厚度(um)</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price6 !== '/')">加工工艺</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price7 !== '/')">成品板厚</td>
              <td :colspan="cscol" v-if="cscol != 0">尺寸</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price10 !== '/')">图形数量</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price11 !== '/')">单价</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price12 !== '/')">工程费</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price13 !== '/')">合拼费</td>
              <td :colspan="tsgyf" v-if="tsgyf != 0">特殊工艺费</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price35 !== '/')">杂色阻焊/字符</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price31 !== '/')">加急费</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price32 !== '/')">合计金额</td>
              <td rowspan="2" v-if="Mtgxreportdata.price.some(item => item.price33 !== '/')">交期</td>
            </tr>
            <tr>
              <td v-if="Mtgxreportdata.price.some(item => item.price8 !== '/')">长</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price9 !== '/')">宽</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price34 !== '/')">最小线宽/线间距(加收元/c㎡)</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price14 !== '/')">压合费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price15 !== '/')">盲埋孔费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price16 !== '/')">背钻费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price17 !== '/')">树脂塞孔费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price18 !== '/')">铜浆塞孔</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price19 !== '/')">台阶费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price20 !== '/')">盲槽费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price21 !== '/')">HDI费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price22 !== '/')">激光钻孔费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price23 !== '/')">金属包边费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price24 !== '/')">半孔费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price25 !== '/')">激光成型费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price26 !== '/')">阻抗费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price27 !== '/')">测试费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price28 !== '/')">表面工艺费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price29 !== '/')">金相切片费</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price30 !== '/')">流水号费</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in Mtgxreportdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price1 !== '/')" style="max-width: 120px">{{ item.price1 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price2 !== '/')">{{ item.price2 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price3 !== '/')">{{ item.price3 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price4 !== '/')" style="max-width: 300px">{{ item.price4 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price5 !== '/')" style="max-width: 80px">{{ item.price5 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price6 !== '/')">{{ item.price6 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price7 !== '/')">{{ item.price7 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price8 !== '/')">{{ item.price8 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price9 !== '/')">{{ item.price9 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price10 !== '/')">{{ item.price10 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price11 !== '/')">{{ item.price11 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price12 !== '/')">{{ item.price12 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price13 !== '/')">{{ item.price13 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price34 !== '/')">{{ item.price34 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price14 !== '/')">{{ item.price14 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price15 !== '/')">{{ item.price15 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price16 !== '/')">{{ item.price16 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price17 !== '/')">{{ item.price17 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price18 !== '/')">{{ item.price18 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price19 !== '/')">{{ item.price19 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price20 !== '/')">{{ item.price20 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price21 !== '/')">{{ item.price21 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price22 !== '/')">{{ item.price22 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price23 !== '/')">{{ item.price23 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price24 !== '/')">{{ item.price24 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price25 !== '/')">{{ item.price25 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price26 !== '/')">{{ item.price26 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price27 !== '/')">{{ item.price27 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price28 !== '/')">{{ item.price28 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price29 !== '/')">{{ item.price29 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price30 !== '/')">{{ item.price30 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price35 !== '/')">{{ item.Price35 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price31 !== '/')">{{ item.price31 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price32 !== '/')">{{ item.price32 }}</td>
              <td v-if="Mtgxreportdata.price.some(item => item.price33 !== '/')">{{ item.price33 }}</td>
            </tr>
            <tr>
              <td colspan="2">合计</td>
              <td colspan="2">大写</td>
              <td :colspan="3 + tsgyf">{{ convertToChineseNum(amountto) }}</td>
              <td colspan="2">小写</td>
              <td colspan="8">{{ amountto }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="margin-top: 10px">二、以上报价含13%增值税及正常物流、包装费；</div>
      <div style="margin-top: 10px">三、交货期：订单下达后；</div>
      <div style="margin-top: 10px">四、结算方式：月结；</div>
      <div style="margin-top: 10px">五、以上报价有效期为报价之日起 5 天，请及时回签。</div>
      <div style="display: flex; line-height: 4ch; margin-top: 15px">
        <div style="width: 30%; z-index: 99">
          <div>客户确认：(签章)</div>
        </div>
        <div style="z-index: 99; width: 50%">
          <div style="float: right">
            <div>供方: 成都明天高新产业有限责任公司</div>
            <div>制表:{{ Mtgxreportdata.value_6 }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
import moment from "moment";
export default {
  name: "",
  props: ["Mtgxreportdata", "ttype"],
  computed: {},
  data() {
    return {
      tsgyf: 0,
      date: moment().format("YYYY-MM-DD"),
      cscol: 0,
      amountto: 0,
      printObj1: {
        id: "Mtgxreport", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    this.tsgyf = 0;
    this.cscol = 0;
    for (let index = 0; index < this.Mtgxreportdata.price.length; index++) {
      if (this.Mtgxreportdata.price[index].price32 && this.Mtgxreportdata.price[index].price32 != "/") {
        this.amountto += Number(this.Mtgxreportdata.price[index].price32);
      }
    }
    let arr = [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 34];
    let arr1 = [8, 9];
    for (let index = 0; index < arr.length; index++) {
      let price = "price" + arr[index];
      if (this.Mtgxreportdata.price.some(item => item[price] !== "/")) {
        this.tsgyf += 1;
      }
    }
    for (let index = 0; index < arr1.length; index++) {
      let price = "price" + arr1[index];
      if (this.Mtgxreportdata.price.some(item => item[price] !== "/")) {
        this.cscol += 1;
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.Mtgxreportdata.pcbFileName;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getreportPdf() {
      htmlToPdfa3("Mtgxreport", this.Mtgxreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
@media print {
  .custom-divider {
    height: 4px;
    background-color: black;
  }
  .custom-divider-thin {
    height: 2px;
    background-color: black;
  }
}
/deep/.ant-divider-horizontal {
  margin: 4px 0;
}
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.Underline {
  position: relative;
  display: inline-block;
}
.Underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 200px; /* 下划线宽度 */
  height: 2px; /* 下划线高度 */
  background-color: rgb(107, 106, 106); /* 下划线颜色 */
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
  table > thead > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
  table > tbody > tr > td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
}
</style>
