<template>      
    
    <a-form-model
      :model="data"
      layout="inline"
    >
    <a-row>
      <a-col :span='8'>
        <a-form-item label="雇员名称"  :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.realName" disabled  />  
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="订单标签" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
           <a-select
            ref="select" 
            v-model="data.labels"
          >
            <a-select-option value="0">样板</a-select-option>
            <a-select-option value="1">批量</a-select-option>
            <a-select-option value="2">多层</a-select-option>
          </a-select>  
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="目标数量" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.targetCount_" />  
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span='8'>
        <a-form-item label="每日总量" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.maxNum"/>  
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="单次获取"  :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.getNum" />  
        </a-form-item>
      </a-col>      
      <a-col :span='8'>
        <a-form-item label="停留数量"  :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.stayNum"/>  
        </a-form-item>
      </a-col>
    </a-row> 

    <a-row>           
      <a-col :span='8'>
        <a-form-item label="长" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.length"/>  
        </a-form-item>
      </a-col>
      <a-col :span='8'>
        <a-form-item label="宽" :label-col="{ span: 10 }" :wrapper-col="{ span: 10 }" style="width:100%; margin:0">
          <a-input v-model="data.width" />  
        </a-form-item>
      </a-col>     
      <a-col :span='8'>       
        <a-form-model-item label="当前状态" :label-col="{ span: 10 }" :wrapper-col="{ span: 14}" style="width:100%; margin:0">
          <a-tag :color="data.isLeave_ ? '#87d068' : '#f50'">
            {{ data.isLeave_ ? "休息中" : "正常" }}
          </a-tag>
          <a-button            
            :type="data.isLeave_ ? 'danger' : 'primary'"
            size="small"
            @click="statusClick()"
          >
            {{ data.isLeave_ ? "销假" : "请假" }}
          </a-button>
        </a-form-model-item>
      </a-col> 
    </a-row>    

    <a-row>
      <a-col :span='4'>
        <a-form-item label="铝基"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.aluminum"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="非铝基" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.nonAluminum"/>  
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>      
      <a-col :span='4'>
        <a-form-item label="单面板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}" style="width:100%; margin:0">
          <a-checkbox v-model="data.layer1"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="双面板"  :label-col="{ span: 16 }" :wrapper-col="{ span:4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.layer2"/>  
        </a-form-item>
      </a-col>
       <a-col :span='4'>
        <a-form-item label="4层板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.layer4" />  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="6层板" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.layer6"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="8层板"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.layer8"/>  
        </a-form-item>
      </a-col>
    </a-row>

    <a-row> 
      <a-col :span='4'>
        <a-form-item label="内贸订单" :label-col="{ span: 16 }" :wrapper-col="{ span:4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.domesticTrade"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="外贸订单" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.foreignTrade"/>  
        </a-form-item>
      </a-col>
    </a-row>    

    <a-row>      
      <a-col :span='4'>
        <a-form-item label="普通"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.ordinary"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="优品" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.premiumProducts"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="精品" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}" style="width:100%; margin:0">
          <a-checkbox v-model="data.boutique"/>  
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>
      <a-col :span='4'>
        <a-form-item label="小于0.5平"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.area1"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="0.5-2平" :label-col="{ span: 14}" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.area2"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="2-5平" :label-col="{ span: 14 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.area3"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="5-10平"  :label-col="{ span: 14 }" :wrapper-col="{ span:4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.area4"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="10-30平" :label-col="{ span: 14}" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.area5"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="大于30平" :label-col="{ span: 16}" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.area6"/>  
        </a-form-item>
      </a-col>
    </a-row>

    <a-row>      
    <a-col :span='4'>
        <a-form-item label="单片出货"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.pcs"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="客户拼版" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}" style="width:100%; margin:0">
          <a-checkbox v-model="data.customerSet"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="捷配代拼" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.jpSet"/>  
        </a-form-item>
      </a-col>
    </a-row>   

    <a-row>      
      <a-col :span='4'>
        <a-form-item label="协同订单"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.coordination"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="非协同单" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.noCoordination"/>  
        </a-form-item>
      </a-col>      
    </a-row>

    <a-row>
      <a-col :span='4'>
        <a-form-item label="大客订单" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.bigCus"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="非大客单"  :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.noBigCus"/>  
        </a-form-item>
      </a-col>      
    </a-row>   

    <a-row>     
      <a-col :span='4'>
        <a-form-item label="外接JDB" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.jdbOrder"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="外接德群" :label-col="{ span: 16 }" :wrapper-col="{ span: 4 }" style="width:100%; margin:0">
          <a-checkbox v-model="data.dqOrder"/>  
        </a-form-item>
      </a-col>
      <a-col :span='4'>
        <a-form-item label="工程脚本" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}" style="width:100%; margin:0">
          <a-checkbox v-model="data.isScript_" />  
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-form-item label="显示顺序">
        <a-input-number v-model="data.sort" :min="1" :max="peopleOrderInfoList.length"/>
      </a-form-item>
    </a-row>
  </a-form-model>
</template>

<script>
export default {
  name:'OrderRetrievalSettings',   
  props:["data","peopleOrderInfoList", ],

  created(){        
  },
  data() {
    return {  
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
    };
  },
  methods: {    
    statusClick(){
      this.data.isLeave_ = !this.data.isLeave_
      console.log('this.data.isLeave_',this.data.isLeave_)  
      
    }
  },
  mounted () {

  }
};
</script>