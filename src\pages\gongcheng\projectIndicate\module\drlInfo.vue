<template>
  <a-spin :spinning="spinning">
    <div class="box" ref="SelectBox1">
      <a-form-model layout="inline" style="width: 100%"> </a-form-model>
      <div>
        <a-table
          :columns="columns1"
          :dataSource="proOrderDrillingDto.drillParameterDtos"
          :scroll="{ y: 420, x: 1200 }"
          :pagination="false"
          :rowKey="(record, index) => `${index + 1}`"
          class="Tab1"
          :customRow="onClickRow"
          :rowClassName="isRedRow"
        >
          <template slot="positiveNegative" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.positiveNegative }}</span>
            <div v-else>
              <a-select
                v-model="record.positiveNegative"
                showSearch
                optionFilterProp="label"
                :getPopupContainer="getPopupContainer"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-positiveNegative`"
                @change="getPosNeg('change', record)"
              >
                <a-select-option v-for="item in mapKey(selectData.PosNeg)" :key="item.value" :value="item.lable" :label="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="drillName_" slot-scope="text, record">
            <span v-if="!editFlg1">{{ record.drillName_ }}</span>
            <div v-else>
              <a-input v-model="record.drillName_" allowClear></a-input>
            </div>
          </template>
          <template slot="stretchX" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.stretchX }}</span>
            <div v-else>
              <a-input
                v-model="record.stretchX"
                allowClear
                @change="changex1(record, index)"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-stretchX`"
              >
              </a-input>
            </div>
          </template>
          <template slot="stretchY" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.stretchY }}</span>
            <div v-else>
              <a-input
                v-model="record.stretchY"
                allowClear
                @change="changey1(record, index)"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-stretchY`"
              >
              </a-input>
            </div>
          </template>
          <template slot="resinPlugHole" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.resinPlugHole"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-resinPlugHole`"
            >
            </a-checkbox>
          </template>
          <template slot="nutCap" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.nutCap"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-nutCap`"
            >
            </a-checkbox>
          </template>
          <template slot="resinPlugSelect" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.resinPlugSelect }}</span>
            <div v-else>
              <a-select
                v-model="record.resinPlugSelect"
                showSearch
                optionFilterProp="label"
                :getPopupContainer="getPopupContainer"
                allowClear
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-resinPlugSelect`"
              >
                <a-select-option v-for="item in mapKey(selectData.ResinPlugSelect)" :key="item.value" :value="item.lable" :label="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="fillHole4Plating_" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.fillHole4Plating_"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-fillHole4Plating_`"
            >
            </a-checkbox>
          </template>
          <template slot="platedHole" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.platedHole"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-platedHole`"
            >
            </a-checkbox>
          </template>
          <template slot="thickening" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.thickening"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-thickening`"
            >
            </a-checkbox>
          </template>
          <template slot="thickSurface" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.thickSurface }}</span>
            <div v-else>
              <a-input
                v-model="record.thickSurface"
                allowClear
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-thickSurface`"
              ></a-input>
            </div>
          </template>
          <template slot="copperReduction" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.copperReduction"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-copperReduction`"
            >
            </a-checkbox>
          </template>
          <template slot="thickSurface" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.thickSurface }}</span>
            <div v-else>
              <a-select
                v-model="record.thickSurface"
                showSearch
                allowClear
                optionFilterProp="label"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-thickSurface`"
              >
                <a-select-option v-for="item in mapKey(selectData.ThickSurface)" :key="item.value" :value="item.value" :label="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="thickenTo" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.thickenTo }}</span>
            <div v-else>
              <a-input
                v-model="record.thickenTo"
                allowClear
                @blur="ThickenToBlur(record)"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-thickenTo`"
              >
              </a-input>
            </div>
          </template>
          <template slot="copperReduction" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.copperReduction"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-copperReduction`"
            >
            </a-checkbox>
          </template>
          <template slot="copperReducSurface" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.copperReducSurface }}</span>
            <div v-else>
              <a-select
                v-model="record.copperReducSurface"
                showSearch
                allowClear
                optionFilterProp="label"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-copperReducSurface`"
              >
                <a-select-option v-for="item in mapKey(selectData.CopperReducSurface)" :key="item.value" :value="item.value" :label="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="reduceCopperTo" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.reduceCopperTo }}</span>
            <div v-else>
              <a-input
                v-model="record.reduceCopperTo"
                allowClear
                @blur="CopperToBlur(record)"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-reduceCopperTo`"
              ></a-input>
            </div>
          </template>
          <template slot="copperPlateElectricity" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.copperPlateElectricity"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-copperPlateElectricity`"
            >
            </a-checkbox>
          </template>
          <template slot="minCuHole_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.minCuHole_ }}</span>
            <div v-else>
              <a-input
                v-model="record.minCuHole_"
                allowClear
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-minCuHole_`"
              ></a-input>
            </div>
          </template>
          <template slot="minAverageHole_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.minAverageHole_ }}</span>
            <div v-else>
              <a-input
                v-model="record.minAverageHole_"
                allowClear
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-minAverageHole_`"
              ></a-input>
            </div>
          </template>
          <template slot="holeToLine" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.holeToLine }}</span>
            <div v-else>
              <a-input
                v-model="record.holeToLine"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-holeToLine`"
              ></a-input>
            </div>
          </template>
          <template slot="plugResinSurface" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.plugResinSurface }}</span>
            <div v-else>
              <a-select
                v-model="record.plugResinSurface"
                showSearch
                allowClear
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-plugResinSurface`"
              >
                <a-select-option value="TL"> TL</a-select-option>
                <a-select-option value="BL"> BL</a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="vIP_" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.viP_"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-viP_`"
            >
            </a-checkbox>
          </template>
          <template slot="concavity" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.concavity }}</span>
            <div v-else>
              <a-input
                v-model="record.concavity"
                allowClear
                @blur="ConcavityBlur(record)"
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-concavity`"
              ></a-input>
            </div>
          </template>
          <template slot="chcr" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.chcr"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-chcr`"
            >
            </a-checkbox>
          </template>
          <template slot="controlTol" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.controlTol }}</span>
            <div v-else>
              <a-input
                v-model="record.controlTol"
                allowClear
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-controlTol`"
              ></a-input>
            </div>
          </template>
          <template slot="controlSize" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.controlSize }}</span>
            <div v-else>
              <a-select
                v-model="record.controlSize"
                showSearch
                allowClear
                @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.drillName_}-col-controlSize`"
              >
                <a-select-option v-for="item in mapKey(selectData.ControlSize)" :key="item.value" :value="item.value" :label="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="noPth" slot-scope="text, record, index, dataIndex">
            <a-checkbox
              v-model="record.noPth"
              :disabled="!editFlg1"
              @keydown.enter.native="handlePressEnter1(record, index, dataIndex.dataIndex)"
              :ref="`row-${record.drillName_}-col-noPth`"
            >
            </a-checkbox>
          </template>
        </a-table>
      </div>
      <div>
        <a-table
          :columns="columns2"
          :dataSource="proOrderLineDto.lineParameterDtos"
          :scroll="{ y: 630, x: 1200 }"
          :pagination="false"
          :rowKey="(record, index) => `${index + 1}`"
          class="Tab2"
        >
          <template slot="minLineWTitle" slot-scope="">
            <span v-if="metricBritishSystem == 'mm'">线宽mm</span>
            <span v-else>线宽mil</span>
          </template>
          <template slot="minLineSpaceWTitle" slot-scope="">
            <span v-if="metricBritishSystem == 'mm'">间距mm</span>
            <span v-else>间距mil</span>
          </template>
          <template slot="hole2CopperTitle" slot-scope="">
            <span v-if="metricBritishSystem == 'mm'">孔到线mm</span>
            <span v-else>孔到线mil</span>
          </template>
          <template slot="bCTitle" slot-scope="">
            <span v-if="metricBritishSystem == 'mm'">补偿mm</span>
            <span v-else>补偿mil</span>
          </template>
          <template slot="minPadDiameterTitle" slot-scope="">
            <span v-if="metricBritishSystem == 'mm'">最小IC(mm)</span>
            <span v-else>最小IC(mil)</span>
          </template>
          <template slot="bgaSizeTitle" slot-scope="">
            <span v-if="metricBritishSystem == 'mm'">最小BGA(mm)</span>
            <span v-else>最小BGA(mil)</span>
          </template>
          <template slot="minRingTitle" slot-scope="">
            <span v-if="metricBritishSystem == 'mm'">最小焊环mm</span>
            <span v-else>最小焊环mil</span>
          </template>
          <template slot="minLineW_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.minLineW_ }}</span>
            <div v-else>
              <a-input
                v-model="record.minLineW_"
                allowClear
                @blur="CminLineWBlur(record)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-minLineW_`"
              >
              </a-input>
            </div>
          </template>
          <!--T/B-->
          <template slot="orgLayTB_" slot-scope="record">
            <span v-if="!editFlg1">{{ record.orgLayTB_ }}</span>
            <div v-else>
              <a-select v-model="record.orgLayTB_">
                <a-select-option value="t"> t</a-select-option>
                <a-select-option value="b"> b</a-select-option>
              </a-select>
            </div>
          </template>
          <!-- 线宽公差 -->
          <template slot="minLineW_Tol_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.minLineW_Tol_ }}</span>
            <div v-else>
              <a-select
                v-model="record.minLineW_Tol_"
                showSearch
                allowClear
                optionFilterProp="label"
                :getPopupContainer="getPopupContainer"
                @change="change3(index)"
                @keydown.enter.native="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-minLineW_Tol_`"
              >
                <a-select-option v-for="item in mapKey(selectData.LineWidthTol)" :key="item.value" :value="item.value" :label="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="minLineSpace_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.minLineSpace_ }}</span>
            <div v-else>
              <a-input
                v-model="record.minLineSpace_"
                allowClear
                @blur="MinLineSpaceBlur(record)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-minLineSpace_`"
              >
              </a-input>
            </div>
          </template>
          <template slot="hole2Copper_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.hole2Copper_ }}</span>
            <div v-else>
              <a-input
                v-model="record.hole2Copper_"
                allowClear
                @blur="Hole2CopperBlur(record)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-hole2Copper_`"
              >
              </a-input>
            </div>
          </template>

          <template slot="bC_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.bC_ }}</span>
            <div v-else>
              <a-input
                v-model="record.bC_"
                allowClear
                @blur="BcBlur(record, index)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-bC_`"
                @change="cuBuc(index)"
              >
              </a-input>
              <!-- <span>{{buC}}</span> -->
            </div>
          </template>

          <template slot="copperRatio_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.copperRatio_ }}</span>
            <div v-else>
              <a-input
                v-model="record.copperRatio_"
                allowClear
                @blur="CopperRatioBlur(record)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-copperRatio_`"
              >
              </a-input>
            </div>
          </template>
          <template slot="bgaSize" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.bgaSize }}</span>
            <div v-else>
              <a-input
                v-model="record.bgaSize"
                allowClear
                @blur="BgaSizeBlur(record)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-bgaSize`"
              >
              </a-input>
            </div>
          </template>
          <template slot="dryWetFilm" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.dryWetFilm }}</span>
            <div v-else>
              <!-- <a-input v-model="record.dryWetFilm" allowClear
            @keydown.enter.prevent="handlePressEnter(record,index,dataIndex.dataIndex, )" :ref="`row-${record.lineID_}-col-dryWetFilm`">  </a-input> -->
              <a-select
                v-model="record.dryWetFilm"
                showSearch
                allowClear
                optionFilterProp="label"
                :getPopupContainer="getPopupContainer"
                @change="change4(index)"
                @keydown.enter.native="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-dryWetFilm`"
              >
                <a-select-option v-for="item in mapKey(selectData.DryWetFilm)" :key="item.value" :value="item.value" :label="item.lable">
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </template>
          <template slot="minRing" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.minRing }}</span>
            <div v-else>
              <a-input
                v-model="record.minRing"
                allowClear
                @change="change5(index)"
                @blur="MinRingBlur(record, index)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-minRing`"
              >
              </a-input>
            </div>
          </template>
          <template slot="minPadDiameter" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.minPadDiameter }}</span>
            <div v-else>
              <a-input
                v-model="record.minPadDiameter"
                allowClear
                @blur="MinicBlur(record)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-minPadDiameter`"
              >
              </a-input>
            </div>
          </template>
          <!-- 基铜 cuChange 2025/2/26-->
          <template slot="cu_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.cU4Org_ }}</span>
            <div v-else>
              <span v-if="index == 0 || index == proOrderLineDto.lineParameterDtos.length - 1">
                <a-select
                  v-model="record.cU4Org_"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="getPopupContainer"
                  @change="coreChange(index, record)"
                  @keydown.enter.native="handlePressEnter(record, index, dataIndex.dataIndex)"
                  :ref="`row-${record.lineID_}-col-cu_`"
                >
                  <a-select-option v-for="item in mapKey(selectData.OutBaseCopper)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </span>
              <span v-else>
                <a-select
                  v-model="record.cU4Org_"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="getPopupContainer"
                  @change="coreChange(index, record)"
                  @keydown.enter.native="handlePressEnter(record, index, dataIndex.dataIndex)"
                  :ref="`row-${record.lineID_}-col-cu_`"
                >
                  <a-select-option v-for="item in mapKey(selectData.InBaseCopper)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </span>
            </div>
          </template>
          <!-- 完成铜厚 cuChange2 2025/2/26-->
          <template slot="cuThickness_" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.cU4Finished_ }}</span>
            <div v-else>
              <span v-if="index == 0 || index == proOrderLineDto.lineParameterDtos.length - 1">
                <a-select
                  v-model="record.cU4Finished_"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="getPopupContainer"
                  @change="coreChange(index, record)"
                  @keydown.enter.native="handlePressEnter(record, index, dataIndex.dataIndex)"
                  :ref="`row-${record.lineID_}-col-cuThickness_`"
                >
                  <a-select-option v-for="item in mapKey(selectData.CopperThickness)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </span>
              <span v-else>
                <a-select
                  v-model="record.cU4Finished_"
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  :getPopupContainer="getPopupContainer"
                  @change="coreChange(index, record)"
                  @keydown.enter.native="handlePressEnter(record, index, dataIndex.dataIndex)"
                  :ref="`row-${record.lineID_}-col-cuThickness_`"
                >
                  <a-select-option v-for="item in mapKey(selectData.InnerCopperThickness)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </span>
            </div>
          </template>
          <template slot="specialCuThickness" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.specialCuThickness }}</span>
            <div v-else>
              <a-input
                v-model="record.specialCuThickness"
                allowClear
                @blur="SpecialBlur(record)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-specialCuThickness`"
              >
              </a-input>
            </div>
          </template>
          <template slot="stretchX" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.stretchX }}</span>
            <div v-else>
              <a-input
                v-model="record.stretchX"
                allowClear
                @blur="Blurx(record)"
                @change="changex(record, index)"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
                :ref="`row-${record.lineID_}-col-stretchX`"
              >
              </a-input>
            </div>
          </template>
          <template slot="stretchY" slot-scope="text, record, index, dataIndex">
            <span v-if="!editFlg1">{{ record.stretchY }}</span>
            <div v-else>
              <a-input
                v-model="record.stretchY"
                allowClear
                @blur="Blury(record)"
                @change="changey(record, index)"
                :ref="`row-${record.lineID_}-col-stretchY`"
                @keydown.enter.prevent="handlePressEnter(record, index, dataIndex.dataIndex)"
              >
              </a-input>
            </div>
          </template>
        </a-table>
      </div>
      <!-- <div class="bto">
        <a-button @click="editClick1" type="primary" style="margin-top:8px;margin-right:10px;" v-if="!editFlg1" >编辑</a-button>
        <a-button @click="editClick1" type="primary" style="margin-top:8px;margin-right:10px;" v-else >取消</a-button>  
        <a-button @click="saveClick" type="primary" style="margin-top:8px;margin-right:10px;"  >保存</a-button>
      </div> -->
    </div>
  </a-spin>
</template>
<script>
import { drillingInformation, baseCu_ } from "@/services/projectIndicate";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    width: 40,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    className: "index_",
  },
  {
    title: "钻孔名",
    dataIndex: "drillName_",
    scopedSlots: { customRender: "drillName_" },
    align: "center",
    ellipsis: true,
    width: 80,
  },
  {
    title: "最小钻孔",
    dataIndex: "minBorehole",
    align: "center",
    ellipsis: true,
    width: 60,
  },
  {
    title: "厚径比",
    dataIndex: "thkDiaRatio1",
    align: "center",
    ellipsis: true,
    width: 55,
  },
  {
    title: "深度",
    dataIndex: "holeDepth",
    align: "center",
    ellipsis: true,
    width: 55,
  },
  {
    title: "正负片",
    dataIndex: "positiveNegative",
    scopedSlots: { customRender: "positiveNegative" },
    className: "select-sty",
    align: "center",
    ellipsis: true,
    width: 80,
  },
  {
    title: "塞树脂",
    dataIndex: "resinPlugHole",
    scopedSlots: { customRender: "resinPlugHole" },
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "盖帽",
    dataIndex: "nutCap",
    scopedSlots: { customRender: "nutCap" },
    align: "center",
    ellipsis: true,
    width: 45,
  },
  {
    title: "树脂塞孔",
    dataIndex: "resinPlugSelect",
    scopedSlots: { customRender: "resinPlugSelect" },
    align: "center",
    ellipsis: true,
    width: 110,
  },
  {
    title: "镀孔填实",
    dataIndex: "fillHole4Plating_",
    scopedSlots: { customRender: "fillHole4Plating_" },
    align: "center",
    ellipsis: true,
    width: 60,
  },
  {
    title: "镀孔",
    dataIndex: "platedHole",
    scopedSlots: { customRender: "platedHole" },
    align: "center",
    ellipsis: true,
    width: 45,
  },
  {
    title: "加厚",
    dataIndex: "thickening",
    scopedSlots: { customRender: "thickening" },
    align: "center",
    ellipsis: true,
    width: 45,
  },
  {
    title: "加厚面",
    dataIndex: "thickSurface",
    scopedSlots: { customRender: "thickSurface" },
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 75,
  },
  {
    title: "加厚到",
    dataIndex: "thickenTo",
    scopedSlots: { customRender: "thickenTo" },
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 75,
  },
  {
    title: "减铜",
    dataIndex: "copperReduction",
    scopedSlots: { customRender: "copperReduction" },
    align: "center",
    ellipsis: true,
    width: 45,
  },
  {
    title: "减铜面",
    dataIndex: "copperReducSurface",
    scopedSlots: { customRender: "copperReducSurface" },
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 70,
  },
  {
    title: "减铜到",
    dataIndex: "reduceCopperTo",
    scopedSlots: { customRender: "reduceCopperTo" },
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 70,
  },
  {
    title: "沉铜板电",
    dataIndex: "copperPlateElectricity",
    scopedSlots: { customRender: "copperPlateElectricity" },
    align: "center",
    ellipsis: true,
    width: 60,
  },
  // {
  //   title: "孔铜",
  //   dataIndex: "minCuHole_",
  //   scopedSlots: { customRender: 'minCuHole_' },
  //   align:'center',
  //   className: "select-sty",
  //   ellipsis: true,
  //   width:70,
  // },
  // {
  //   title: "平均孔铜",
  //   dataIndex: "minAverageHole_",
  //   scopedSlots: { customRender: 'minAverageHole_' },
  //   align:'center',
  //   className: "select-sty",
  //   ellipsis: true,
  //   width:70,
  // },
  // {
  //   title: "孔到线",
  //   scopedSlots: { customRender: 'holeToLine' },
  //   align:'center',
  //   ellipsis: true,
  //   width:60,
  // },
  {
    title: "VIP",
    scopedSlots: { customRender: "vIP_" },
    dataIndex: "viP_",
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 40,
  },
  {
    title: "塞树脂面",
    scopedSlots: { customRender: "plugResinSurface" },
    dataIndex: "plugResinSurface",
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 70,
  },
  {
    title: "凹陷度",
    scopedSlots: { customRender: "concavity" },
    dataIndex: "concavity",
    align: "center",
    ellipsis: true,
    width: 70,
  },
  {
    title: "盖孔减铜",
    scopedSlots: { customRender: "chcr" },
    dataIndex: "chcr",
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 60,
  },
  {
    title: "拉伸X",
    dataIndex: "stretchX",
    scopedSlots: { customRender: "stretchX" },
    align: "center",
    width: 80,
  },
  {
    title: "拉伸Y",
    dataIndex: "stretchY",
    scopedSlots: { customRender: "stretchY" },
    width: 80,
    align: "center",
  },
  {
    title: "非PTH",
    scopedSlots: { customRender: "noPth" },
    dataIndex: "noPth",
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 70,
  },
  {
    title: "总孔数",
    dataIndex: "totalNumberHoles",
    align: "center",
    ellipsis: true,
    width: 70,
  },
  {
    title: "控深锣深度及公差",
    dataIndex: "controlTol",
    scopedSlots: { customRender: "controlTol" },
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 120,
  },
  {
    title: "控深大小公差",
    dataIndex: "controlSize",
    scopedSlots: { customRender: "controlSize" },
    align: "center",
    className: "select-sty",
    ellipsis: true,
    width: 110,
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    align: "center",
    key: "index",
    width: 50,
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "层",
    dataIndex: "layName_",
    align: "center",
    ellipsis: true,
    width: 70,
  },
  {
    title: "T/B",
    scopedSlots: { customRender: "orgLayTB_" },
    className: "select-sty",
    ellipsis: true,
    align: "center",
    width: 70,
  },
  {
    //title: "线宽",
    slots: { title: "minLineWTitle" },
    scopedSlots: { customRender: "minLineW_" },
    className: "select-sty",
    dataIndex: "minLineW_",
    ellipsis: true,
    align: "center",
    width: 90,
  },
  {
    //title: "间距",
    slots: { title: "minLineSpaceWTitle" },
    scopedSlots: { customRender: "minLineSpace_" },
    className: "select-sty",
    dataIndex: "minLineSpace_",
    ellipsis: true,
    align: "center",
    width: 90,
  },
  {
    title: "线宽公差",
    scopedSlots: { customRender: "minLineW_Tol_" },
    className: "select-sty",
    dataIndex: "minLineW_Tol_",
    ellipsis: true,
    align: "center",
    width: 110,
  },
  {
    //title: "孔到线",
    slots: { title: "hole2CopperTitle" },
    scopedSlots: { customRender: "hole2Copper_" },
    className: "select-sty",
    dataIndex: "hole2Copper_",
    ellipsis: true,
    align: "center",
    width: 80,
  },
  {
    // title: "补偿",
    slots: { title: "bCTitle" },
    scopedSlots: { customRender: "bC_" },
    dataIndex: "bC_",
    className: "select-sty",
    ellipsis: true,
    align: "center",
    width: 80,
  },
  {
    title: "残铜率",
    scopedSlots: { customRender: "copperRatio_" },
    className: "select-sty",
    dataIndex: "copperRatio_",
    ellipsis: true,
    align: "center",
    width: 90,
  },
  {
    //title: "最小IC",
    slots: { title: "minPadDiameterTitle" },
    scopedSlots: { customRender: "minPadDiameter" },
    className: "select-sty",
    dataIndex: "minPadDiameter",
    ellipsis: true,
    align: "center",
    width: 90,
  },
  {
    //title: "最小BGA",
    slots: { title: "bgaSizeTitle" },
    scopedSlots: { customRender: "bgaSize" },
    className: "select-sty",
    dataIndex: "bgaSize",
    ellipsis: true,
    align: "center",
    width: 90,
  },
  {
    title: "干湿膜",
    scopedSlots: { customRender: "dryWetFilm" },
    className: "select-sty",
    dataIndex: "dryWetFilm",
    ellipsis: true,
    align: "center",
    width: 90,
  },
  {
    //title: "最小焊环",
    slots: { title: "minRingTitle" },
    scopedSlots: { customRender: "minRing" },
    className: "select-sty",
    dataIndex: "minRing",
    ellipsis: true,
    align: "center",
    width: 90,
  },
  {
    title: "基铜",
    scopedSlots: { customRender: "cu_" },
    className: "select-sty",
    ellipsis: true,
    dataIndex: "cu_",
    align: "center",
    width: 85,
  },
  {
    title: "完成铜厚",
    scopedSlots: { customRender: "cuThickness_" },
    className: "select-sty",
    ellipsis: true,
    align: "center",
    dataIndex: "cuThickness_",
    width: 85,
  },
  {
    title: "特殊铜厚",
    scopedSlots: { customRender: "specialCuThickness" },
    className: "specialCuThickness",
    ellipsis: true,
    align: "center",
    dataIndex: "specialCuThickness",
    width: 80,
  },
  {
    title: "拉伸X",
    dataIndex: "stretchX",
    scopedSlots: { customRender: "stretchX" },
    align: "center",
    width: 90,
  },
  {
    title: "拉伸Y",
    dataIndex: "stretchY",
    scopedSlots: { customRender: "stretchY" },
    width: 90,
    align: "center",
  },
];
export default {
  name: "drlInfo",
  props: ["proOrderDrillingDto", "selectData", "editFlg1", "drillToolDiameterDtos", "proOrderLineDto", "metricBritishSystem", "proOrderInfoDto"],
  data() {
    return {
      spinning: false,
      columns1,
      columns2,
      selectedRowKeysArray: [],
      proOrderId: "",
      boardLayers1: false,
      factory: "",
      buC: "",
      cuC: "",
      finc: "",
      Ind: "",
    };
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
    var Id = this.$route.query.factory;
    this.factory = Id;
    baseCu_(Id).then(res => {
      if (res.code) {
        this.buC = res.data;
      }
    });
    this.$nextTick(() => {
      const popupContainer = this.$refs.SelectBox1;
    });
  },
  methods: {
    // 获取正负片
    getPosNeg(type, drlval) {
      // PosNeg 1:其他 2:正片 3:负片
      const lineParameters = this.proOrderLineDto.lineParameterDtos; // 线路参数
      const drillParameters = [...this.proOrderDrillingDto.drillParameterDtos]; // 钻带参数  浅拷贝排序后不影响原数组
      // 钻带有共同起始层或结束层层时，取差值较大的参数
      drillParameters.sort((a, b) => {
        // 比较 endLyr_ 和 beginLyr_ 的值
        const diffA = a.endLyr_ - a.beginLyr_;
        const diffB = b.endLyr_ - b.beginLyr_;
        return diffB - diffA;
      });
      // 线路参数为空，直接返回
      if (!lineParameters.length) return;

      // 钻带参数为空，直接将所有线路参数的 PosNeg 设置为 "3"（负片）
      if (!drillParameters.length) {
        lineParameters.forEach(line => {
          line.PosNeg = "3";
        });
        return;
      }

      // 遍历线路参数，根据钻带参数设置 PosNeg 值
      lineParameters.forEach((line, index) => {
        let isMatched = false; // 标记当前线路是否匹配到钻带参数

        for (const drl of drillParameters) {
          // 判断线路层是否为钻带的开始层或结束层，并且钻带类型为 "d" 或 "m"
          if ((line.lineID_ === drl.beginLyr_ || line.lineID_ === drl.endLyr_) && (drl.drillType4Stack_ === "d" || drl.drillType4Stack_ === "m")) {
            line.PosNeg = drl.positiveNegative === "正片" ? "2" : "3";
            isMatched = true;

            // 改变正负片之后，只更改当前钻带开始层与结束层的线路补偿值
            if (type === "change" && drlval && drlval === drl) {
              this.coreChange(index, line, "1");
            }

            break; // 匹配到后退出内层循环
          }
        }

        // 如果没有匹配到钻带参数，设置 PosNeg 为 "1"（其他）
        if (!isMatched) {
          line.PosNeg = "1";
        }
      });

      // 更新线路参数数据
      this.proOrderLineDto.lineParameterDtos = lineParameters;
    },
    handleResize() {
      var boxstyle = document.getElementsByClassName("box")[1];
      boxstyle.style.height = window.innerHeight - 140 + "px";
      boxstyle.style.width = window.innerWidth - 375 + "px";
      let tab1 = document.getElementsByClassName("Tab1")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      let tab2 = document.getElementsByClassName("Tab2")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (tab1 && this.proOrderDrillingDto.drillParameterDtos && this.proOrderDrillingDto.drillParameterDtos.length != 0) {
        tab1.style.height = (window.innerHeight - 220) * 0.4 + "px";
      }
      if (tab2 && (this.proOrderLineDto.lineParameterDtos.length != 0 || !this.proOrderLineDto.lineParameterDtos)) {
        tab2.style.height = (window.innerHeight - 220) * 0.6 + "px";
      }
    },
    getPopupContainer() {
      return this.$refs.SelectBox1;
    },
    ThickenToBlur(record) {
      if (record.thickenTo) {
        var thickenTo = record.thickenTo.split("");
        if (thickenTo.length > 6) {
          thickenTo = thickenTo.slice(0, 6);
          this.$message.warning("加厚填写不超过6个字符");
        }
        record.thickenTo = thickenTo.join("");
      }
    },
    CopperToBlur(record) {
      if (record.reduceCopperTo) {
        var reduceCopperTo = record.reduceCopperTo.split("");
        if (reduceCopperTo.length > 6) {
          reduceCopperTo = reduceCopperTo.slice(0, 6);
          this.$message.warning("减铜填写不超过6个字符");
        }
        record.reduceCopperTo = reduceCopperTo.join("");
      }
    },
    ConcavityBlur(record) {
      if (record.concavity) {
        var concavity = record.concavity.split("");
        if (concavity.length > 6) {
          concavity = concavity.slice(0, 6);
          this.$message.warning("凹陷度填写不超过6个字符");
        }
        record.concavity = concavity.join("");
      }
    },
    CminLineWBlur(record) {
      if (record.minLineW_) {
        var minLineW_ = record.minLineW_.split("");
        if (minLineW_.length > 6) {
          minLineW_ = minLineW_.slice(0, 6);
          this.$message.warning("线宽填写不超过6个字符");
        }
        record.minLineW_ = minLineW_.join("");
      }
    },
    MinLineSpaceBlur(record) {
      if (record.minLineSpace_) {
        var minLineSpace_ = record.minLineSpace_.split("");
        if (minLineSpace_.length > 6) {
          minLineSpace_ = minLineSpace_.slice(0, 6);
          this.$message.warning("间距填写不超过6个字符");
        }
        record.minLineSpace_ = minLineSpace_.join("");
      }
    },
    Hole2CopperBlur(record) {
      if (record.hole2Copper_) {
        var hole2Copper_ = record.hole2Copper_.split("");
        if (hole2Copper_.length > 6) {
          hole2Copper_ = hole2Copper_.slice(0, 6);
          this.$message.warning("孔到线填写不超过6个字符");
        }
        record.hole2Copper_ = hole2Copper_.join("");
      }
    },
    BcBlur(record, index) {
      if (record.bC_) {
        var bC_ = record.bC_.split("");
        if (bC_.length > 6) {
          bC_ = bC_.slice(0, 6);
          this.$message.warning("补偿填写不超过6个字符");
        }
        record.bC_ = bC_.join("");
      }
      this.cuBuc(index);
    },
    CopperRatioBlur(record) {
      if (record.copperRatio_) {
        var copperRatio_ = record.copperRatio_.split("");
        if (copperRatio_.length > 6) {
          copperRatio_ = copperRatio_.slice(0, 6);
          this.$message.warning("残铜率填写不超过6个字符");
        }
        record.copperRatio_ = copperRatio_.join("");
      }
    },
    MinicBlur(record) {
      if (record.minPadDiameter) {
        var minPadDiameter = record.minPadDiameter.split("");
        if (minPadDiameter.length > 6) {
          minPadDiameter = minPadDiameter.slice(0, 6);
          this.$message.warning("最小IC填写不超过6个字符");
        }
        record.minPadDiameter = minPadDiameter.join("");
      }
    },
    BgaSizeBlur(record) {
      if (record.bgaSize) {
        var bgaSize = record.bgaSize.split("");
        if (bgaSize.length > 6) {
          bgaSize = bgaSize.slice(0, 6);
          this.$message.warning("最小BGA填写不超过6个字符");
        }
        record.bgaSize = bgaSize.join("");
      }
    },
    MinRingBlur(record, index) {
      if (record.minRing) {
        var minRing = record.minRing.split("");
        if (minRing.length > 6) {
          minRing = minRing.slice(0, 6);
          this.$message.warning("最小焊环填写不超过6个字符");
        }
        record.minRing = minRing.join("");
      }
      this.change5(index);
    },
    SpecialBlur(record) {
      if (record.specialCuThickness) {
        var specialCuThickness = record.specialCuThickness.split("");
        if (specialCuThickness.length > 6) {
          specialCuThickness = specialCuThickness.slice(0, 6);
          this.$message.warning("特殊铜厚填写不超过6个字符");
        }
        record.specialCuThickness = specialCuThickness.join("");
      }
    },
    Blurx(record) {
      if (record.stretchX) {
        var stretchX = record.stretchX.split("");
        if (stretchX.length > 8) {
          stretchX = stretchX.slice(0, 8);
          this.$message.warning("拉伸X填写不超过8个字符");
        }
        record.stretchX = stretchX.join("");
      }
    },
    Blury(record) {
      if (record.stretchY) {
        var stretchY = record.stretchY.split("");
        if (stretchY.length > 8) {
          stretchY = stretchY.slice(0, 8);
          this.$message.warning("拉伸Y填写不超过8个字符");
        }
        record.stretchY = stretchY.join("");
      }
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate(value, list) {
      this.proOrderDrillingDto.vCutKnifeNum = value;
      let a = this.getPrice(this.proOrderDrillingDto.vCutKnifeNum, list, value);
    },
    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
    },
    editClick1() {
      this.editFlg1 = !this.editFlg1;
      if (!this.editFlg1) {
        this.$emit("GetProOrderInfo");
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    saveClick() {
      if (!this.editFlg1) {
        this.$message.warning("非编辑状态不可保存");
        return;
      }
      let params = this.proOrderDrillingDto;
      delete params.drillToolDiameterDtos;
      params = JSON.parse(JSON.stringify(params).replace(/drillParameterDtos/g, "drillParameterInputs"));
      drillingInformation(params).then(res => {
        if (res.code) {
          this.$emit("GetProOrderInfo");
          this.$message.success("保存成功");
          this.editFlg1 = false;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.proOrderId = record.id;
            this.proOrderDrillingDto.drillToolDiameterDtos = this.drillToolDiameterDtos.filter(v => v.guid4Drill_ == this.selectedRowKeysArray[0]);
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.id && record.id == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    formingChange() {
      this.$emit("formingChange");
    },
    coreChange(index, record, type) {
      //cU4Org_ 基铜 ;cU4Finished_ 完成铜厚 ;bC_ 补偿
      let core = "";
      let length = this.proOrderLineDto.lineParameterDtos.length;
      let data = this.proOrderLineDto.lineParameterDtos;
      if (record.cU4Org_ && record.cU4Finished_) {
        core = record.cU4Org_ + "_PT_" + record.cU4Finished_;
        let arr = this.buC.filter(item => {
          return item.ozpt == core;
        });
        if (arr.length) {
          // 替换规则中的占位符为当前记录的 PosNeg 值
          let reg = arr[0].bcReg.replace(/"#PosNeg#"/g, "record.PosNeg");
          reg = reg.replace(/"#SurfaceFinish#"/g, "this.proOrderInfoDto.surfaceFinish");
          if (reg) {
            //表面处理为镍钯金时  更改外层 补偿值为0
            if (this.proOrderInfoDto.surfaceFinish == "nickelgoldplating" && (index == 0 || index == length - 1)) {
              record.bC_ = 0;
            } else {
              // 使用 eval 动态计算补偿值  单位为mm时进行换算，保留3位小数
              record.bC_ = this.metricBritishSystem == "mm" ? this.getFloat(Number(eval(reg)) * 0.0254, 3) : eval(reg);
            }
          } else {
            record.bC_ = "";
          }
        } else {
          record.bC_ = "";
        }
      } else {
        record.bC_ = "";
      }
      // 钻带参数正负片改变以及递归时不需要联动
      if (!type) {
        if (index == 0) {
          data[length - 1].cU4Org_ = data[0].cU4Org_;
          data[length - 1].cU4Finished_ = data[0].cU4Finished_;
          this.coreChange(length - 1, data[length - 1], "1");
        } else {
          if (index > 0 && index < length - 1) {
            for (var a = 1; a < length - 1; a++) {
              if (a >= index) {
                data[a].cU4Org_ = data[index].cU4Org_;
                data[a].cU4Finished_ = data[index].cU4Finished_;
                this.coreChange(a, data[a], "1"); //调用递归 根据正负片重新计算补偿值(传入参数1避免死循环)
              }
            }
          }
        }
      }
    },
    cuChange(index, cu, record) {
      //1外层  0内层
      var arr = this.proOrderLineDto.lineParameterDtos;
      var length = this.proOrderLineDto.lineParameterDtos.length;
      this.cuC = cu;
      if (index == 0) {
        arr[length - 1].cU4Org_ = arr[index].cU4Org_;
      }
      if (index > 0 && index < length - 1) {
        for (var a = 1; a < length - 1; a++) {
          if (a >= index) {
            arr[a].cU4Org_ = arr[index].cU4Org_;
          }
        }
      }
      if (this.$route.query.factory == 37) {
        if (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) {
          this.Ind = 1;
        } else {
          this.Ind = 0;
        }
        const buC = this.buC.filter(item => {
          return item.oZ_ == this.cuC && item.innerOrOuter_ == this.Ind;
        });
        if (!buC.length) {
          record.bC_ = "";
        } else {
          record.bC_ = buC[0].bc;
        }
        if (index == 0) {
          arr[length - 1].bC_ = arr[index].bC_;
        }
        if (index > 0 && index < length - 1) {
          for (var q = 1; q < length - 1; q++) {
            if (q >= index) {
              arr[q].bC_ = arr[index].bC_;
            }
          }
        }
      } else if (this.$route.query.factory == 58 || this.$route.query.factory == 59) {
        if (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) {
          const buC = this.buC.filter(item => {
            return item.oZ_ == this.cuC && item.innerOrOuter_ == 1;
          });
          if (!buC.length) {
            record.bC_ = "";
          } else {
            record.bC_ = buC[0].bc;
          }
          if (index == 0) {
            arr[length - 1].bC_ = arr[index].bC_;
          }
        }
      } else if (this.$route.query.factory == 12) {
        if (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) {
          this.Ind = 1;
        } else {
          if (cu != record.cU4Finished_) {
            this.Ind = 1;
          } else {
            this.Ind = 0;
          }
        }
        const buC = this.buC.filter(item => {
          return item.oZ_ == this.cuC && item.innerOrOuter_ == this.Ind;
        });
        if (!buC.length) {
          record.bC_ = "";
        } else {
          record.bC_ = buC[0].bc;
        }
        if (index == 0) {
          arr[length - 1].bC_ = arr[index].bC_;
        }
        if (index > 0 && index < length - 1) {
          for (let q = 1; q < length - 1; q++) {
            if (q >= index) {
              arr[q].bC_ = arr[index].bC_;
            }
          }
        }
      } else if (this.$route.query.factory == 22) {
        this.cuChange2(index, record.cU4Finished_, record);
      }
    },
    cuBuc(index) {
      var arr = this.proOrderLineDto.lineParameterDtos;
      var length = this.proOrderLineDto.lineParameterDtos.length;
      if (index == 0) {
        arr[length - 1].bC_ = arr[index].bC_;
      }
      if (index > 0 && index < length - 1) {
        for (var a = 1; a < length - 1; a++) {
          if (a >= index) {
            arr[a].bC_ = arr[index].bC_;
          }
        }
      }
    },
    changex(record, index) {
      let length = this.proOrderLineDto.lineParameterDtos.length;
      if (index == 0) {
        this.proOrderLineDto.lineParameterDtos[length - 1].stretchX = this.proOrderLineDto.lineParameterDtos[index].stretchX;
      }
      if (index > 0 && index < length - 1) {
        if (record.orgLayTB_ == "t" && this.proOrderLineDto.lineParameterDtos[index + 1].orgLayTB_ == "b") {
          this.proOrderLineDto.lineParameterDtos[index + 1].stretchX = record.stretchX;
        }
      }
    },
    changey(record, index) {
      let length = this.proOrderLineDto.lineParameterDtos.length;
      if (index == 0) {
        this.proOrderLineDto.lineParameterDtos[length - 1].stretchY = this.proOrderLineDto.lineParameterDtos[index].stretchY;
      }
      if (record.orgLayTB_ == "t" && this.proOrderLineDto.lineParameterDtos[index + 1].orgLayTB_ == "b") {
        this.proOrderLineDto.lineParameterDtos[index + 1].stretchY = record.stretchY;
      }
      // if (index > 0 && index < length - 1) {
      //   for (let a = 1; a < length - 1; a++) {
      //     if (a >= index) {
      //       this.proOrderLineDto.lineParameterDtos[a].stretchY = this.proOrderLineDto.lineParameterDtos[index].stretchY;
      //     }
      //   }
      // }
    },
    changex1(record, index) {
      let length = this.proOrderLineDto.drillParameterDtos.length;
      if (index == 0) {
        this.proOrderLineDto.drillParameterDtos[length - 1].stretchX = this.proOrderLineDto.drillParameterDtos[index].stretchX;
      }
      if (index > 0 && index < length - 1) {
        for (let a = 1; a < length - 1; a++) {
          if (a >= index) {
            this.proOrderLineDto.drillParameterDtos[a].stretchX = this.proOrderLineDto.drillParameterDtos[index].stretchX;
          }
        }
      }
    },
    changey1(record, index) {
      let length = this.proOrderLineDto.drillParameterDtos.length;
      if (index == 0) {
        this.proOrderLineDto.drillParameterDtos[length - 1].stretchY = this.proOrderLineDto.drillParameterDtos[index].stretchY;
      }
      if (index > 0 && index < length - 1) {
        for (let a = 1; a < length - 1; a++) {
          if (a >= index) {
            this.proOrderLineDto.drillParameterDtos[a].stretchY = this.proOrderLineDto.drillParameterDtos[index].stretchY;
          }
        }
      }
    },
    cuChange2(index, cU4Finished_, record) {
      var arr = this.proOrderLineDto.lineParameterDtos;
      var length = this.proOrderLineDto.lineParameterDtos.length;
      if (index == 0) {
        arr[length - 1].cU4Finished_ = arr[index].cU4Finished_;
      }
      if (index > 0 && index < length - 1) {
        for (var a = 1; a < length - 1; a++) {
          if (a >= index) {
            arr[a].cU4Finished_ = arr[index].cU4Finished_;
          }
        }
      }
      this.finc = cU4Finished_;
      if (this.$route.query.factory == 58 || this.$route.query.factory == 59) {
        if (index > 0 && index < length - 1) {
          const buC = this.buC.filter(item => {
            return item.oZ_ == this.finc && item.innerOrOuter_ == 0;
          });
          if (!buC.length) {
            record.bC_ = "";
          } else {
            record.bC_ = buC[0].bc;
          }
          if (index > 0 && index < length - 1) {
            for (let q = 1; q < length - 1; q++) {
              if (q >= index) {
                arr[q].bC_ = arr[index].bC_;
              }
            }
          }
        }
      } else if (this.$route.query.factory == 22) {
        if (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) {
          this.Ind = 1;
        } else {
          this.Ind = 0;
        }
        if (cU4Finished_ == 0.5 && (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1)) {
          this.finc = 0;
        }
        const buC = this.buC.filter(item => {
          return item.oZ_ == this.finc && item.innerOrOuter_ == this.Ind;
        });
        if (!buC.length) {
          record.bC_ = "";
        } else {
          record.bC_ = buC[0].bc;
        }
        if (
          cU4Finished_ == 1 &&
          (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) &&
          this.proOrderLineDto.lineParameterDtos[index].cU4Org_ == 0.33
        ) {
          record.bC_ = 1.0;
        } else if (
          cU4Finished_ == 1 &&
          (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) &&
          this.proOrderLineDto.lineParameterDtos[index].cU4Org_ == 0.43
        ) {
          record.bC_ = 1.2;
        } else if (
          cU4Finished_ == 1 &&
          (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) &&
          this.proOrderLineDto.lineParameterDtos[index].cU4Org_ == 0.5
        ) {
          record.bC_ = 1.6;
        } else if (cU4Finished_ == 1 && (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1)) {
          record.bC_ = 1.6;
        }
        if (index == 0) {
          arr[length - 1].bC_ = arr[index].bC_;
        }
        if (index > 0 && index < length - 1) {
          for (let q = 1; q < length - 1; q++) {
            if (q >= index) {
              arr[q].bC_ = arr[index].bC_;
            }
          }
        }
      } else if (this.$route.query.factory != 37 && this.$route.query.factory != 12) {
        if (index == 0 || index == this.proOrderLineDto.lineParameterDtos.length - 1) {
          this.Ind = 1;
        } else {
          this.Ind = 0;
        }
        const buC = this.buC.filter(item => {
          return item.oZ_ == this.finc && item.innerOrOuter_ == this.Ind;
        });
        if (!buC.length) {
          record.bC_ = "";
        } else {
          record.bC_ = buC[0].bc;
        }
        if (index == 0) {
          arr[length - 1].bC_ = arr[index].bC_;
        }
        if (index > 0 && index < length - 1) {
          for (let q = 1; q < length - 1; q++) {
            if (q >= index) {
              arr[q].bC_ = arr[index].bC_;
            }
          }
        }
      } else if (this.$route.query.factory == 12) {
        this.cuChange(index, record.cU4Org_, record);
      }
    },
    change3(index) {
      var arr = this.proOrderLineDto.lineParameterDtos;
      var length = this.proOrderLineDto.lineParameterDtos.length;
      if (index == 0) {
        arr[length - 1].minLineW_Tol_ = arr[index].minLineW_Tol_;
      }
      if (index > 0 && index < length - 1) {
        for (var a = 1; a < length - 1; a++) {
          if (a >= index) {
            arr[a].minLineW_Tol_ = arr[index].minLineW_Tol_;
          }
        }
      }
    },
    change4(index) {
      var arr = this.proOrderLineDto.lineParameterDtos;
      var length = this.proOrderLineDto.lineParameterDtos.length;
      if (index == 0) {
        arr[length - 1].dryWetFilm = arr[index].dryWetFilm;
      }
      if (index > 0 && index < length - 1) {
        for (var a = 1; a < length - 1; a++) {
          if (a >= index) {
            arr[a].dryWetFilm = arr[index].dryWetFilm;
          }
        }
      }
    },
    change5(index) {
      var arr = this.proOrderLineDto.lineParameterDtos;
      var length = this.proOrderLineDto.lineParameterDtos.length;
      if (index == 0) {
        arr[length - 1].minRing = arr[index].minRing;
      }
      if (index > 0 && index < length - 1) {
        for (var a = 1; a < length - 1; a++) {
          if (a >= index) {
            arr[a].minRing = arr[index].minRing;
          }
        }
      }
    },
    handlePressEnter(record, index, dataIndex) {
      const currentRef = this.$refs[`row-${record.lineID_}-col-${dataIndex}`];
      const columnRefIndex = this.columns2.findIndex(column => column.dataIndex === dataIndex);
      if (index === this.proOrderLineDto.lineParameterDtos.length - 1) {
        const nextColumnIndex = columnRefIndex + 1;
        const nextColumnDataIndex = this.columns2[nextColumnIndex].dataIndex;
        if (columnRefIndex < this.columns2.length) {
          this.setFocus(nextColumnDataIndex, 1);
        }
      } else {
        const nextColumnIndex1 = columnRefIndex;
        const nextColumnDataIndex1 = this.columns2[nextColumnIndex1].dataIndex;
        this.setFocus(nextColumnDataIndex1, record.lineID_ + 1);
      }
      currentRef.blur();
    },

    handlePressEnter1(record, index, dataIndex) {
      const currentRef = this.$refs[`row-${record.drillName_}-col-${dataIndex}`];
      const columnRefIndex = this.columns1.findIndex(column => column.dataIndex === dataIndex);
      if (index === this.proOrderDrillingDto.drillParameterDtos.length - 1) {
        const nextColumnIndex = columnRefIndex + 1;
        const nextColumnDataIndex = this.columns1[nextColumnIndex].dataIndex;
        if (columnRefIndex < this.columns1.length) {
          this.setFocus(nextColumnDataIndex, record.drillName_);
        }
        if (columnRefIndex == this.columns1.length && this.proOrderLineDto.lineParameterDtos.length) {
          this.setFocus("minLineW_", 1);
        }
      } else {
        const nextColumnIndex1 = columnRefIndex;
        const nextColumnDataIndex1 = this.columns1[nextColumnIndex1].dataIndex;
        const nextColumnDatadrillName_ = this.columns1[nextColumnIndex1].drillName_;
        this.setFocus(nextColumnDataIndex1, nextColumnDatadrillName_);
      }
      currentRef.blur();
    },

    setFocus(dataIndex, rowId) {
      const refName = `row-${rowId}-col-${dataIndex}`;
      const ref = this.$refs[refName];
      ref.focus();
    },
    getRow(record) {
      return document.querySelector(`[data-row-key='${record.lineID_}']`);
    },
    getNextVisibleRowIndex(currentRowId) {
      let nextVisibleRowIndex = -1;
      const currentIndex = this.proOrderLineDto.lineParameterDtos.findIndex(item => item.lineID_ === currentRowId);
      for (let i = currentIndex + 1; i < this.proOrderLineDto.lineParameterDtos.length; i++) {
        const row = this.getRow(this.proOrderLineDto.lineParameterDtos[i]);
        if (row && !row.classList.contains("ant-table-row-hidden")) {
          nextVisibleRowIndex = i;
          break;
        }
      }

      return nextVisibleRowIndex;
    },
    getNextVisibleRowIndex1(currentRowId) {
      let nextVisibleRowIndex = -1;
      const currentIndex = this.proOrderDrillingDto.drillParameterDtos.findIndex(item => item.lineID_ === currentRowId);
      for (let i = currentIndex + 1; i < this.proOrderDrillingDto.drillParameterDtos.length; i++) {
        const row = this.getRow(this.proOrderDrillingDto.drillParameterDtos[i]);
        if (row && !row.classList.contains("ant-table-row-hidden")) {
          nextVisibleRowIndex = i;
          break;
        }
      }

      return nextVisibleRowIndex;
    },
  },
  created() {
    //  this.$nextTick(()=>{
    //    this.handleResize()
    //  })
  },
};
</script>
<style scoped lang="less">
// /deep/.ant-select-dropdown-menu-item{
//   font-weight: 500;
// }
/deep/.ant-empty-normal {
  padding: 57px 0 !important;
  margin: 0;
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-row-selected {
  td {
    background: #dfdcdc;
  }
}
/deep/ .rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/.disable {
  background: #f5f5f5 !important;
}
/deep/.Tab1 {
  border-top: 1px solid #efefef;
}
/deep/.Tab2 {
  border-top: 1px solid #efefef;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 0 !important;
}
/deep/.disable {
  background: #f5f5f5 !important;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table {
  tr.ant-table-row-hover td {
    background: rgb(223, 220, 220) !important;
  }
}
/deep/.ant-table-row {
  .ant-select {
    width: 95%;
    height: 24px;
  }
  .ant-checkbox-wrapper {
    height: 20px;
    line-height: 20px;
  }

  .ant-select-selection--single {
    height: 24px;
  }
  .ant-select-selection__rendered {
    line-height: 24px;
  }
  .ant-input {
    height: 24px;
    width: 95%;
    font-weight: 500;
    padding: 5px;
  }
  .ant-input-affix-wrapper {
    padding-left: 3px;
  }
}
.box {
  border-left: 1px solid rgb(233 230 230);
  position: relative;
  .bto {
    position: absolute;
    bottom: -42px;
    right: 374px;
  }
}
/deep/.ant-form {
  border-left: 1px solid #ddd;
  border-top: 1px solid #ddd;
}
/deep/ .ant-table-thead > tr > th {
  padding: 4px 0;
  border-right: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 2px 0;
  border-right: 1px solid #efefef;
}
/deep/.select-sty {
  padding: 2px 0 !important;
}
/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;

  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 28px;
    .ant-select {
      width: 120px;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-family: PingFangSC-Regular, Sans-serif;
    font-size: 14px;
    color: #000000;
    // font: 14px/1.17 "微软雅黑",arial,\5b8b\4f53;
    // color: #666;
    background-color: #fafafa;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font-family: PingFangSC-Regular, Sans-serif;
      font-size: 14px;
      color: #000000;
    }
  }
  .ant-form-item-control-wrapper {
    font-family: PingFangSC-Regular, Sans-serif;
    font-size: 14px;
    color: #000000;
    .ant-form-item-control {
      .ant-form-item-children {
        display: block;
        min-height: 28px;
        line-height: 26px;
        .ant-checkbox-wrapper {
          height: 28px;
          // line-height: 28px;
        }
        .ant-select-selection--single {
          height: 28px;
        }
        .ant-select-selection__rendered {
          line-height: 28px;
        }
        .ant-select {
          height: 28px;
        }
        .ant-input {
          height: 28px;
        }
      }
      line-height: inherit;
      padding: 2px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
    }
  }
}
</style>
