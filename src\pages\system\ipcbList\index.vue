<!-- 系统管理-iPCB任务监控 -->
<template>
 <a-spin :spinning="spinning">
    <div class="main" style="position: relative;" ref="SelectBox">
        <div class="Content" style="position: relative;bottom: 7px;"> 
          <!-- <a-input  placeholder="GerberID" v-model="formData.GerberID" style="width:183px;margin-right:0.5%;margin-bottom: 0.4%;" allowClear @keyup.enter.native="searchClick"></a-input> -->
          <a-input  placeholder="文件名" v-model="formData.SpecPath" style="width:183px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input>
          <a-input  placeholder="ID查询" v-model="formData.BizObjectID" style="width:183px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick"></a-input>
          <a-select  placeholder="订单状态" v-model="formData.State" style="width:183px;margin-right:0.5%;" allowClear @keyup.enter.native="searchClick" :getPopupContainer="()=>this.$refs.SelectBox">
            <a-select-option v-for="(item,index) in mapKey(selectData)" :key="index" :value="item.value">{{item.lable}}</a-select-option>
          </a-select>
          <a-date-picker  
              style="margin-right:0.5%;"              
              format="YYYY-MM-DD "               
              placeholder="提交时间(开始)"
              @change="onChange1"
              
          />
          <a-date-picker  
              style="margin-right:0.5%;"              
              format="YYYY-MM-DD "               
              placeholder="提交时间(结束)"
              @change="onChange2"
              
          />          
          <a-button type="primary" @click="searchClick" style="margin-right:0.5%;">搜索</a-button>
        </div>
        <div style='width:100%;display:flex;'>
          <div class="leftContent">
          <a-table
          :columns="columns1" 
          :pagination=false
          :scroll="{y:740}"
          rowKey="factoryCode"
          :dataSource="dataSource1"  
          :class="dataSource1.length ? 'min-table':''"
          >
          <div  slot="factoryCode" slot-scope="text, record,"  @contextmenu.prevent.stop="rightClick1($event,text)">
              <a :title='record.factoryCode' style="color: #000000">{{record.factoryCode}}</a>
            </div> 
            <div  slot="counts" slot-scope="text, record,"  @contextmenu.prevent.stop="rightClick1($event,text)">
              <a :title='record.counts' style="color: #000000">{{record.counts}}</a>
            </div> 
        </a-table>
        </div>
        <div class="rightContent">
          <a-table
            v-if="pageshow"            
            :columns="columns"
            :dataSource="dataSource"  
            :pagination="pagination"
            :loading="orderListTableLoading"
            :rowKey="'bizObjectID'"
            :scroll="{x: 1346,y: 697 }"
            @change="handleTableChange"
            :class="dataSource.length ? 'min-table':''"
          >
          <template slot="labelUrl" slot-scope="record">
          <span  style="color: #428bca;" @click.stop="previewClick(record)" > 操作</span>
          <a  style="color: #428bca;opacity:0.2" > | </a>
          <a style="color:rgb(66, 139, 202)" @click="click2(record)">详情</a>
          <a  style="color: #428bca;opacity:0.2" > | </a>
          <a style="color:rgb(66, 139, 202)" @click="resetting(record)">重置</a>
        </template>  
            <span slot="num" slot-scope="text, record, index" >
                {{(pagination.pageIndex-1)*pagination.pageSize+parseInt(index)+1}}
            </span>  
            <div  slot="specPath" slot-scope="text, record,"  @contextmenu.prevent.stop="rightClick1($event,text)">
              <a :title='record.specPath' style="color:rgb(66, 139, 202);overflow: hidden; text-overflow: ellipsis; width: 223px;display: block;" @click="click1(record)">{{record.specPath}}</a>
            </div> 
            <span  slot="bizObjectID" slot-scope="text, record,"  @contextmenu.prevent.stop="rightClick1($event,text)">
              <a :title='record.bizObjectID' style=" color:#000000;" >{{record.bizObjectID}}</a>
            </span>
            <div  slot="name" slot-scope="text, record,"  @contextmenu.prevent.stop="rightClick1($event,text)">
              <a :title='record.name' style=" color:#000000;">{{record.name}}</a>
            </div> 
            <div  slot="zndrWaitTime" slot-scope="text, record,"  @contextmenu.prevent.stop="rightClick1($event,text)">
              <a :title='record.zndrWaitTime' style=" color:#000000;">{{record.zndrWaitTime}}</a>
            </div> 
            <span slot="title_slot">
              智能导入等待时间<a-tooltip title="点击排序"><a-icon type="question-circle-o"/></a-tooltip>      
            </span>
            <span slot="content_slot" slot-scope="text, record," style="cursor: pointer" >
            {{record.zndrWaitTime}}
            </span>
          </a-table>
          <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="down">复制</a-menu-item>
        </a-menu> 
        </div>
        </div>
       
        <!-- <div class="bto"></div> -->
        <a-modal title="日志" 
        :visible="dataVisible" 
        @cancel="reportHandleCancel"  
        centered
        destroyOnClose 
        :maskClosable="false" 
        :width='650' 
        :confirmLoading='confirmLoading'>
        <template slot="footer">
          <a-button   @click="reportHandleCancel">取消</a-button>
        </template>
        <view-log-info ref='viewLogInfo' :viewLogData='viewLogData' />
      </a-modal>
    </div>
    <a-modal
      title="确认弹窗"
      :visible="confirmdatavisible"
      @ok="handleOk"
      @cancel="reportHandleCancel"
      centered
      :width="400">
        <span>{{ messagelist }}</span>
     </a-modal>
 </a-spin>
</template>
<script>
import {graphicPreview2,} from "@/services/mkt/PrequalificationProduction.js";
import {taskMonitoringPageList,sightState,taskMonitoring,monitoringListCount,settaskcz}from "@/services/identity/ipcb";
import viewLogInfo from "@/pages/system/ipcbList/module/viewLogInfo";
import moment from "moment";
const columns =[
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    fixed:'left',
    scopedSlots: { customRender: 'num' },
    width: 40,
  },
  
  // {
  //   title: "用户",
  //   dataIndex: "userid",
  //   align: "left",
  //   // fixed:'left',
  //   ellipsis: true,
  //   width: 80,
  //   className:'userStyle',
  //   // scopedSlots: { customRender: 'orderNo' },
  // },
  {
    title: "名称",
    dataIndex: "name",
    align: "left",
    fixed:'left',
    width: 85,
    ellipsis: true, 
    className:'userStyle', 
    scopedSlots: { customRender: 'name' },
  },
  {
    title: "文件名",
    dataIndex: "specPath",
    scopedSlots: { customRender: 'specPath' },
    width: 225,
    ellipsis: true,
    align: "left",
    className:'userStyle',
  },
  // {
  //   title: "GerberID",
  //   dataIndex: "bizObjectID",
  //   align: "left",
  //    fixed:'left',
  //   ellipsis: true,
  //   width: 260,
  //   className:'userStyle',
  //  },
  {
    title: "智能导入等待时间",
    dataIndex: "zndrWaitTime",
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: 'zndrWaitTime' },
    // className:'userStyle',
     // fixed:'left',
    width:140,
    sorter: (a, b) => {
      return a.zndrWaitTime - b.zndrWaitTime      
    },
  },
  {
    title: "Gerber分析等待时间",
    align: "left",
    dataIndex:'gerberAnalysisWaitTime',
     // fixed:'left',
    ellipsis: true,
    width:155,
    sorter: (a, b) => {
      return a.gerberAnalysisWaitTime - b.gerberAnalysisWaitTime      
    }  
  },
  {
    title: "OCR等待时间",
    align: "left",
    dataIndex:'ocrWaitTime',
     // fixed:'left',
    ellipsis: true,
    width: 110,
    sorter: (a, b) => {
      return a.ocrWaitTime - b.ocrWaitTime      
    } 
  },
  {
    title: "PCB转Gerber等待时间",
    dataIndex: "gerberWaitTime",
    align: "left",
    //  // fixed:'left',
    ellipsis: true,
    width: 165,
    sorter: (a, b) => {
      return a.gerberWaitTime - b.gerberWaitTime      
    } 
  },
  {
    title: "智能导入用时",
    dataIndex: "zndrTime",
    align: "left",
    //  // fixed:'left',
    ellipsis: true,
    width: 110,
    sorter: (a, b) => {
      return a.zndrTime - b.zndrTime      
    } 
  },
  {
    title: "PCB转Gerber用时",
    dataIndex: "gerberTime",
    align: "left",
    //  // fixed:'left',
    ellipsis: true,
    width: 140,
    sorter: (a, b) => {
      return a.gerberTime - b.gerberTime      
    } 
  },
  {
    title: "Gerber分析用时",
    dataIndex: "gerberAnalysisTime",
    align: "left",
    ellipsis: true,
    width: 130,
    sorter: (a, b) => {
      return a.gerberAnalysisTime - b.gerberAnalysisTime      
    } 
  },
  {
    title: "OCR转换用时",
    dataIndex: "ocrTime",
    align: "left",
    ellipsis: true,
    width: 110,
    sorter: (a, b) => {
      return a.ocrTime - b.ocrTime      
    } 
  },
  {
    title: "SPEC分析用时",
    dataIndex: "specTime",
    align: "left",
    ellipsis: true,
    width: 120,
    sorter: (a, b) => {
      return a.specTime - b.specTime      
    } 
  },
  {
    title: "审核总时间",
    dataIndex: "auditTime",
    width: 138,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      return a.auditTime - b.auditTime      
    } 
  },  
 
  // {
  //   title: "GSate",
  //   width: 65,
  //   dataIndex:'gSatus',
  //   ellipsis: true,
  //   align: "left",
  // },
  {
    title: "状态",
    width: 170,
    dataIndex:'satus',
    ellipsis: true,
    align: "center",
  },  
  // {
  //   title: "路径",
  //   dataIndex: "gerberPath",
  //   width:260,
  //   ellipsis: true,
  //   align: "left",
  //   className:'userStyle',
  // },
  {
    title: "Input_IP",
    width: 120,
    dataIndex:'input_IP',
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      const ip1 = a.input_IP.split('.').map(e=>e.padStart(3,'0')).join('')    
      const ip2 = b.input_IP.split('.').map(e=>e.padStart(3,'0')).join('')    
      return ip1 - ip2          
    } 
  },
  {
    title: "P2G_IP",
    dataIndex: "p2G_IP",
    width: 120,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      const ip1 = a.p2G_IP?a.p2G_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000'   
      const ip2 = b.p2G_IP?b.p2G_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000' 
      return ip1 - ip2          
    }
  },
  {
    title: "Gerber_IP",
    dataIndex: "gerber_IP",
    width: 120,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      const ip1 = a.gerber_IP?a.gerber_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000'    
      const ip2 = b.gerber_IP?b.gerber_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000'   
      return ip1 - ip2          
    }
  },
  {
    title: "OCR_IP",
    align: "center",
    dataIndex:'ocR_IP',
    width:120,  
    ellipsis: true, 
    sorter: (a, b) => {
      const ip1 = a.ocR_IP?a.ocR_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000'   
      const ip2 = b.ocR_IP?b.ocR_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000'  
      return ip1 - ip2          
    } 
  },
  {
    title: "SPEC_IP",
    align: "center",
    dataIndex:'speC_IP',
    width: 120,  
    ellipsis: true, 
    sorter: (a, b) => {
      const ip1 = a.speC_IP?a.speC_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000'    
      const ip2 = b.speC_IP?b.speC_IP.split('.').map(e=>e.padStart(3,'0')).join('') :'000000000000'    
      return ip1 - ip2          
    } 
  },
  {
    title: "提交时间",
    dataIndex: "createtime",
    width: 150,
    ellipsis: true,
    align: "left",
  },
  {
    title: "pcb转Gerber",
    dataIndex: "cowGUID",
    width: 100,
    ellipsis: true,
    align: "left",
  },
  {
    title: "ID",
    dataIndex: "bizObjectID",
    scopedSlots:{customRender:'bizObjectID'},
    width: 280,
    ellipsis: true,
    align: "left",
  },
  // {
  //   title: "详情",    
  //   width: 45,
  //   ellipsis: true,
  //   fixed:'right',
  //   align: "left",
  //   scopedSlots:{customRender:'info'},
  // },
  {
    title: "操作",
    align: "center",
    fixed:'right',
    width: 140, 
    scopedSlots:{customRender:'labelUrl'},
  },
  // {
  //   title: "pcb上传时间",
  //   dataIndex: "uploadTime",
  //   width: 120,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "转换开始时间",
  //   dataIndex: "conversionStartTime",
  //   width: 120,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "转换结束时间",
  //   dataIndex: "conversionEndTime",
  //   width:120,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "智能导入开始时间",
  //   dataIndex: "auditDateB",
  //   width:140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "智能导入结束时间",
  //   dataIndex: "gerberDateE",
  //   width: 140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "OCR建立时间",
  //   dataIndex: "ocrCreatedDate",
  //   width: 100,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "OCR开始时间",
  //   dataIndex: "ocrStartTime",
  //   width: 100,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "OCR结束时间",
  //   dataIndex: "ocrEndTime",
  //   width: 100,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Gerber分析任务创建时间",
  //   dataIndex: "gerberAnalysisCreateTime",
  //   width: 150,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Gerber分析开始时间",
  //   dataIndex: "gerberAnalysisStartTime",
  //   width: 140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "Gerber分析结束时间",
  //   dataIndex: "gerberAnalysisEndTime",
  //   width: 140,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "SPEC审核开始时间",
  //   dataIndex: "specDateB",
  //   width: 130,
  //   ellipsis: true,
  //   align: "left",
  // },
  // {
  //   title: "SPEC审核结束时间",
  //   dataIndex: "auditDateE",
  //   width: 130,
  //   ellipsis: true,
  //   align: "left",
  // },

]
const columns1 = [ 
  {
    title: "工厂",
    align: "left",
    width: 100,
    ellipsis: true, 
    className:'userStyle', 
    dataIndex:'factoryCode',
    scopedSlots:{customRender:'factoryCode'},
  },
  {
    title: "数量",
    align: "left",
    ellipsis: true,
    width: 80,
    className:'userStyle',
    dataIndex:'counts',
    scopedSlots:{customRender:'counts'},
  },
]
export default {
    name:'',
    components:{viewLogInfo},
    data(){
        return{
       menuVisible:false,
       confirmdatavisible:false,
       messagelist:'',
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      menuData:{},
          spinning:false, 
          columns,
          columns1,
          dataSource:[], 
          dataSource1:[],
          orderListTableLoading:false,
          pagination: {
              pageSize: 20,
              pageIndex: 1,
              total:0,
              showQuickJumper: true,
              showSizeChanger: true,
              pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
              showTotal: (total) => `总计 ${total} 条`,
          },  
          formData:{
            GerberID:'',
            SpecPath:'',
            BizObjectID:'',
            State:undefined,
            StartTime:'',
            EndTime:'',
          },  
          pageshow:true,  
          dataVisible:false,
          confirmLoading:false, 
          viewLogData:[],  
          selectData:[],
          queryData:{},
          jump:false,
          selectrowData:{},
        }
    },
    mounted(){
      this.getList1()
      this.getList()
      sightState().then(res=>{
        if(res.code){
          this.selectData = res.data
        }else{
          this.$message.error(res.message)
        }
      })
    },
    watch:{

    },
    methods:{  
      previewClick(record){
        graphicPreview2(record.bizObjectID).then(res=>{
        if(res.data){
          window.open(res.data,"_blank")
        }else{
          this.$message.error(res.message)
        }        
      })      

    },   
      handleTableChange(pagination,filters, sorter,){
        if(this.pagination.pageSize != pagination.pageSize){
          this.jump = true
          pagination.current = 1
        }else{
          this.jump = false
        }
          this.pagination.pageIndex=pagination.current;
          this.pagination.pageSize=pagination.pageSize;
          if(pagination.current != pagination.pageIndex && !this.jump){ 
          if(JSON.stringify(this.queryData) != '{}'){ 
            this.getList(this.queryData) 
          } else{
            this.getList() 
          }
          }
          if(this.jump){
            this.pageshow = false      
            if(JSON.stringify(this.queryData) != '{}'){ 
             this.getList(this.queryData)            
            } else{
              this.getList()               
            } 
           let that = this 
           this.$nextTick(() => {
            that.pageshow = true
            }) 
                    
          }
                    
      },
      rightClick1(e,text){     
      if(text || text == 0){
        this.text = text 
        this.menuVisible = true;
        this.menuStyle.top = e.clientY- 110 +  "px";
        this.menuStyle.left =  e.clientX - document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
        document.body.addEventListener("click", this.bodyClick);
      }
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },   
    down(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
      },
      getList1(params){
        monitoringListCount(params).then(res=>{
          if(res.code){
            this.dataSource1 = res.data
           }else{
            this.$message.error(res.message)
          }
        })
      },
      getList(queryData){
        let params = {
          'PageIndex':this.pagination.pageIndex,
          'PageSize':this.pagination.pageSize,
        }  
        let obj = {}
        if(queryData){
          obj = Object.assign(params, queryData)
        }else{
          obj = params
        }
        this.orderListTableLoading = true
        taskMonitoringPageList(obj).then(res=>{
          if(res.code){
            this.dataSource = res.data.items
            this.pagination.total = res.data.totalCount

          }else{
            this.$message.error(res.message)
          }
        }).finally(()=>{
          this.orderListTableLoading = false
        })         
      },
      moment,
      onChange1 (value, dateString) {
        this.formData.StartTime = dateString      
      },   
      onChange2 (value, dateString) {
        this.formData.EndTime = dateString
      },
      searchClick(){
        let params = this.formData
        var arr1 = params.GerberID.split('')
        if(arr1.length >150){
          arr1 = arr1.slice(0,150)
        }
        params.GerberID = arr1.join('')

        var arr2 = params.SpecPath.split('')
        if(arr2.length >150){
          arr2 = arr2.slice(0,150)
        }
        params.SpecPath = arr2.join('') 

        var arr3 = params.BizObjectID.split('')
        if(arr3.length >150){
          arr3 = arr3.slice(0,150)
        }
        params.BizObjectID = arr3.join('') 
        this.queryData = params     
        this.pageshow = false
        this.pagination.pageIndex = 1;
        this.getList(params)
        this.getList1(params)
        this.$nextTick(() => {
        this.pageshow = true
          })
      },
      click1(record) {
          if (record.gerberPath) {
            let path = 'http://sight.bninfo.com/Downloadsave.aspx?fileNameAbs=' + encodeURIComponent(record.gerberPath);
            // let a = record.gerberPath.split('.');
            // this.downloadByteArrayFromURL(path, record.bizObjectID + '.' + a[a.length - 1]);
            window.open(path, '_blank', 'noreferrer');
          }
        },
        downloadByteArrayFromURL(url, fileName) {
          fetch(url)
            .then(response => response.arrayBuffer())
            .then(arrayBuffer => {
              const byteArray = new Uint8Array(arrayBuffer);
              const blob = new Blob([byteArray], { type: 'application/octet-stream' });
              const url = URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = fileName;
              link.click();
              URL.revokeObjectURL(url);
            })
            .catch(error => {
              console.error('Error downloading file:', error);
            });
        },
      click2(record){
        taskMonitoring(record.bizObjectID).then(res=>{
          if(res.code){
            this.viewLogData = res.data
          }else{
            this.$message.error(res.message)
          }
        })       
        this.dataVisible = true        
      },
      resetting(record){
        this.selectrowData = record
        this.messagelist = '确认重置吗?'
        this.confirmdatavisible = true
      },
      reportHandleCancel(){
        this.dataVisible = false
        this.confirmdatavisible = false
      },
      handleOk(){
        settaskcz(this.selectrowData.bizObjectID).then(res=>{
          if(res.code){
            this.$message.success('重置成功')
          }else{
            this.$message.error(res.message)
          }
        })  
        this.confirmdatavisible = false
      },
      mapKey(data){
        if (!data) {
          return []
        } else {
          return Object.keys(data).map(item => {
            return {'value':item, 'lable': data[item]}
          })
        }
      },
    }
}
</script>
<style scoped lang="less">
.tabRightClikBox{
  border:2px solid rgb(238, 238, 238) !important;
  li{
    height:24px;
    line-height:24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color:#000000
  }
}
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  color:#000000;
}

/deep/.ant-input{
  font-weight: 500;
  color:#000000;
}
 /deep/.ant-tag {
    font-size: 12px;   
    font-weight: 500; 
     color:#ff9900;
     padding: 0 2px; 
     margin: 0; 
     margin-right: 3px; 
     height: 21px;
     user-select: none;
  }
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
.main {
  background: #FFFFFF;
  padding-top:7px;
  min-width: 1670px;
  .content{
    height:44px;
    padding-left:6px;
    background: #FFFFFF;
    /deep/.ant-select-selection__placeholder{
      display: block;
    }
  }
  .ant-input,.ant-select{
    width:8%;
    margin-right:0.5%;
    margin-top:6px;
  }
  /deep/.userStyle{
    user-select: none!important;
  }
  // height: 834px;
  width: 100%;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  /deep/.leftContent {
    .ant-table-selection-column{
      padding:0!important;
    }
    background: #FFFFFF;
    .min-table {      
      .ant-table-body{
        min-height:740px;
      }
    }
    height:776px;
    width: 17%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
    
  }
  /deep/.rightContent {
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
    width:83%;
    // max-width: 1346px;
    height:776px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    //  overflow: auto;
    overflow: hidden;
    .min-table {
      width:100%;
      .ant-table-body{
        min-height:697px;
      }
    }
   }
   /deep/.ant-table-wrapper{
    width:100%;
   }
  .bto{
  height:60px;
  background: #FFFFFF;
  border:2px solid #E9E9F0;
}
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  // /deep/ .ant-table-thead > tr > th {
  //   // padding:3px 0!important;
  //   .ant-table-column-sorter {
  //     display: none;
  //   }
  // }
  /deep/ .ant-table{
    // min-height: 728px;
    .ant-table-thead > tr > th{
      padding: 6px 4px;
      // border-color: #f0f0f0;
      border-right:1px solid #efefef;
    }    
    .ant-table-tbody > tr > td {
      padding: 6px 4px!important;
      border-right:1px solid #efefef;
      // border-color: #f0f0f0;
      position:relative;
      .topCss{
        position:absolute;
        top:3%;        
      }
      .topCss1{
        position:absolute;
        top:3%;
        // left:30%;
        
      }
      .topCss2{
        position:absolute;
        top:3%;
        // left:25%;
        
      }
    }
    tr.ant-table-row-selected td {
     background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 2px 0 0 10px;
  }

}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background:#F8F8F8;
  // background: #F0F2F5;
}
</style>
