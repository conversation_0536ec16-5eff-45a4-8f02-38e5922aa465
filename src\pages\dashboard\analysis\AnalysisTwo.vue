<template>
  <a-spin :spinning="spinning">
    <div class="analysis" id="dataContainer">
      <div style="height: 100%">
        <div class="factoryscreening">
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="工厂筛选" :label-col="{ span: 1 }" :wrapper-col="{ span: 5 }">
                <div>
                  <a-select showSearch allowClear optionFilterProp="lable" placeholder="授权工厂" v-model="FactoryId" @change="Factorychange">
                    <a-select-option
                      style="color: blue"
                      v-for="(item, index) in mapKey(factroyList)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="时间筛选" :label-col="{ span: 1 }" :wrapper-col="{ span: 5 }">
                <div>
                  <a-select v-model="orderdayormoth" @change="ordchange">
                    <a-select-option value="1"> 日 </a-select-option>
                    <a-select-option value="2"> 月 </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <div style="background: white">
          <div>
            <span class="centered-span">工程订单制作下线率</span>
            <div>
              <a-table
                :dataSource="ReceiveNumdtos"
                :columns="orderdayormoth == '1' ? Linecolumns : generalcolumnsY"
                :pagination="false"
                :rowKey="(record, index) => `${index + 1}`"
                bordered
                :scroll="{ x: 600 }"
              >
                <template slot="customCell" slot-scoped="">
                  <div class="rectangle">
                    <div style="display: inline-block; position: relative; right: 38px; top: 22px">指标</div>
                    <div style="display: inline-block; position: relative; left: 38px; top: 4px">时间</div>
                  </div>
                </template>
              </a-table>
            </div>
          </div>
          <div>
            <span class="centered-span">{{ orderdayormoth == "1" ? "工程每日产出" : "工程每月产出" }}</span>
            <div>
              <a-table
                :dataSource="Dailyoutputoftheproject"
                :columns="orderdayormoth == '1' ? outputcolumns : generalcolumnsY"
                :pagination="false"
                :rowKey="(record, index) => `${index + 1}`"
                bordered
                :scroll="{ x: 600 }"
              >
                <template slot="customCell" slot-scoped="">
                  <div class="rectangle">
                    <div style="display: inline-block; position: relative; right: 38px; top: 22px">指标</div>
                    <div style="display: inline-block; position: relative; left: 38px; top: 4px">时间</div>
                  </div>
                </template>
              </a-table>
            </div>
          </div>
          <div>
            <span class="centered-span">工程订单制作下线时效达成率</span>
            <div style="padding-bottom: 10px">
              <a-table
                :dataSource="Deliveryrateofofflineorders"
                :columns="orderdayormoth == '1' ? Reachedcolumns : generalcolumnsY"
                :pagination="false"
                :rowKey="(record, index) => `${index + 1}`"
                bordered
                :scroll="{ x: 600 }"
              >
                <template slot="customCell" slot-scoped="">
                  <div class="rectangle">
                    <div style="display: inline-block; position: relative; right: 38px; top: 22px">款数</div>
                    <div style="display: inline-block; position: relative; left: 38px; top: 4px">时间</div>
                  </div>
                </template>
              </a-table>
            </div>
          </div>
          <!-- <div>
          <span class="centered-span">工程各组产出达成率</span>
          <template>
            <div>
              <a-table
                :dataSource="Departmentaloutputachievementrate"
                :columns="productioncolumns2"
                :pagination="false"
                :rowKey="(record, index) => `${index + 1}`"
                bordered
                :scroll="{ x: 600 }"
              >
              </a-table>
            </div>
          </template>
        </div> -->
        </div>
      </div>
    </div>
  </a-spin>
</template>
<script>
import * as echarts from "echarts/core";
import { TooltipComponent, LegendComponent } from "echarts/components";
import { PieChart } from "echarts/charts";
import { LabelLayout } from "echarts/features";
import { SVGRenderer } from "echarts/renderers";
import moment from "moment";
import axios from "axios";
echarts.use([TooltipComponent, LegendComponent, PieChart, SVGRenderer, LabelLayout]);
import { factroyList, ppEOrdermonthdatastatistics, ppEOrderyeardatastatistics } from "@/services/analysis";

export default {
  props: [],
  data() {
    return {
      spinning: false,
      ReceiveNumdtos: [],
      Linecolumns: [
        {
          dataIndex: "name",
          key: "name",
          width: 200,
          fixed: "left",
          align: "center",
          slots: { title: "customCell" },
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 80,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 80,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 80,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 80,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 80,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 80,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 80,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 80,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 80,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 80,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 80,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 80,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 80,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 80,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 80,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 80,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 80,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 80,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 80,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      generalcolumnsY: [
        {
          dataIndex: "name",
          key: "name",
          width: 200,
          fixed: "left",
          align: "center",
          slots: { title: "customCell" },
        },
        {
          title: "1",
          dataIndex: "month1",
          align: "center",
          width: 110,
        },
        {
          title: "2",
          dataIndex: "month2",
          align: "center",
          width: 110,
        },
        {
          title: "3",
          dataIndex: "month3",
          align: "center",
          width: 110,
        },
        {
          title: "4",
          dataIndex: "month4",
          align: "center",
          width: 110,
        },
        {
          title: "5",
          dataIndex: "month5",
          align: "center",
          width: 110,
        },
        {
          title: "6",
          dataIndex: "month6",
          align: "center",
          width: 110,
        },
        {
          title: "7",
          dataIndex: "month7",
          align: "center",
          width: 110,
        },
        {
          title: "8",
          dataIndex: "month8",
          align: "center",
          width: 110,
        },
        {
          title: "9",
          dataIndex: "month9",
          align: "center",
          width: 110,
        },
        {
          title: "10",
          dataIndex: "month10",
          align: "center",
          width: 110,
        },
        {
          title: "11",
          dataIndex: "month11",
          align: "center",
          width: 110,
        },
        {
          title: "12",
          dataIndex: "month12",
          align: "center",
          width: 110,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          align: "center",
        },
      ],
      Departmentaloutputachievementrate: [],
      productioncolumns2: [
        {
          title: "组别",
          dataIndex: "name",
          key: "name",
          width: 200,
          fixed: "left",
          align: "center",
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                rowSpan: row.nameRowSpan,
              },
            };
          },
        },
        {
          title: "单元",
          dataIndex: "name1",
          key: "name1",
          width: 200,
          align: "center",
          customRender(text, row) {
            return {
              children: text,
              attrs: {
                rowSpan: row.name1RowSpan,
              },
            };
          },
        },
        {
          title: "指标",
          dataIndex: "outputindicators",
          key: "outputindicators",
          width: 200,
          align: "center",
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 80,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 80,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 80,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 80,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 80,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 80,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 80,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 80,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 80,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 80,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 80,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 80,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 80,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 80,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 80,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 80,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 80,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 80,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 80,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      Deliveryrateofofflineorders: [
        {
          key: "1",
          name: "下线订单（款数）",
        },
        {
          key: "2",
          name: "时效达标订单（款数）",
        },
        {
          key: "3",
          name: "时效达成率（%）",
        },
      ],
      Reachedcolumns: [
        {
          dataIndex: "name",
          key: "name",
          width: 200,
          align: "center",
          fixed: "left",
          slots: { title: "customCell" },
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 80,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 80,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 80,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 80,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 80,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 80,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 80,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 80,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 80,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 80,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 80,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 80,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 80,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 80,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 80,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 80,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 80,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 80,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 80,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      Dailyoutputoftheproject: [],
      outputcolumns: [
        {
          dataIndex: "name",
          key: "name",
          width: 200,
          align: "center",
          fixed: "left",
          slots: { title: "customCell" },
        },
        {
          title: "1",
          dataIndex: "day1",
          align: "center",
          width: 80,
        },
        {
          title: "2",
          dataIndex: "day2",
          align: "center",
          width: 80,
        },
        {
          title: "3",
          dataIndex: "day3",
          align: "center",
          width: 80,
        },
        {
          title: "4",
          dataIndex: "day4",
          align: "center",
          width: 80,
        },
        {
          title: "5",
          dataIndex: "day5",
          align: "center",
          width: 80,
        },
        {
          title: "6",
          dataIndex: "day6",
          align: "center",
          width: 80,
        },
        {
          title: "7",
          dataIndex: "day7",
          align: "center",
          width: 80,
        },
        {
          title: "8",
          dataIndex: "day8",
          align: "center",
          width: 80,
        },
        {
          title: "9",
          dataIndex: "day9",
          align: "center",
          width: 80,
        },
        {
          title: "10",
          dataIndex: "day10",
          align: "center",
          width: 80,
        },
        {
          title: "11",
          dataIndex: "day11",
          align: "center",
          width: 80,
        },
        {
          title: "12",
          dataIndex: "day12",
          align: "center",
          width: 80,
        },
        {
          title: "13",
          dataIndex: "day13",
          align: "center",
          width: 80,
        },
        {
          title: "14",
          dataIndex: "day14",
          align: "center",
          width: 80,
        },
        {
          title: "15",
          dataIndex: "day15",
          align: "center",
          width: 80,
        },
        {
          title: "16",
          dataIndex: "day16",
          align: "center",
          width: 80,
        },
        {
          title: "17",
          dataIndex: "day17",
          align: "center",
          width: 80,
        },
        {
          title: "18",
          dataIndex: "day18",
          align: "center",
          width: 80,
        },
        {
          title: "19",
          dataIndex: "day19",
          align: "center",
          width: 80,
        },

        {
          title: "20",
          dataIndex: "day20",
          align: "center",
          width: 80,
        },
        {
          title: "21",
          dataIndex: "day21",
          align: "center",
          width: 80,
        },
        {
          title: "22",
          dataIndex: "day22",
          align: "center",
          width: 80,
        },
        {
          title: "23",
          dataIndex: "day23",
          align: "center",
          width: 80,
        },
        {
          title: "24",
          dataIndex: "day24",
          align: "center",
          width: 80,
        },
        {
          title: "25",
          dataIndex: "day25",
          align: "center",
          width: 80,
        },
        {
          title: "26",
          dataIndex: "day26",
          align: "center",
          width: 80,
        },
        {
          title: "27",
          dataIndex: "day27",
          align: "center",
          width: 80,
        },
        {
          title: "28",
          dataIndex: "day28",
          align: "center",
          width: 80,
        },
        {
          title: "29",
          dataIndex: "day29",
          align: "center",
          width: 80,
        },
        {
          title: "30",
          dataIndex: "day30",
          align: "center",
          width: 80,
        },
        {
          title: "31",
          dataIndex: "day31",
          align: "center",
          width: 80,
        },
        {
          title: "合计",
          dataIndex: "Summary",
          fixed: "right",
          align: "center",
          width: 80,
        },
      ],
      comloading2: false,
      factroyList: [],
      FactoryId: "",
      orderdayormoth: "1",
    };
  },

  created() {},
  async mounted() {
    factroyList().then(res => {
      if (res.code) {
        this.factroyList = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
    (this.Departmentaloutputachievementrate = [
      { name: "CAM", name1: "CAM(内)", outputindicators: "产量" },
      { name: "CAM", name1: "CAM(内)", outputindicators: "目标" },
      { name: "CAM", name1: "CAM(内)", outputindicators: "目标达成率" },
      { name: "CAM", name1: "CAM(外)", outputindicators: "产量" },
      { name: "CAM", name1: "CAM(外)", outputindicators: "目标" },
      { name: "CAM", name1: "CAM(外)", outputindicators: "目标达成率" },
      { name: "QAE", name1: "QAE", outputindicators: "产量" },
      { name: "QAE", name1: "QAE", outputindicators: "目标" },
      { name: "QAE", name1: "QAE", outputindicators: "目标达成率" },
      { name: "后处理", name1: "助工组", outputindicators: "产量" },
      { name: "后处理", name1: "助工组", outputindicators: "目标" },
      { name: "后处理", name1: "助工组", outputindicators: "目标达成率" },
      { name: "后处理", name1: "返单组", outputindicators: "产量" },
      { name: "后处理", name1: "返单组", outputindicators: "目标" },
      { name: "后处理", name1: "返单组", outputindicators: "目标达成率" },
      { name: "后处理", name1: "合拼组", outputindicators: "产量" },
      { name: "后处理", name1: "合拼组", outputindicators: "目标" },
      { name: "后处理", name1: "合拼组", outputindicators: "目标达成率" },
      { name: "后处理", name1: "拼版组", outputindicators: "产量" },
      { name: "后处理", name1: "拼版组", outputindicators: "目标" },
      { name: "后处理", name1: "拼版组", outputindicators: "目标达成率" },
    ]),
      this.rowSpan2("name"); // 合并 'name' 列
    this.rowSpan2("name1"); // 合并 'name1' 列
    this.OfflineandoutputM();
  },
  methods: {
    Factorychange() {
      this.ReceiveNumdtos = [];
      this.Dailyoutputoftheproject = [];
      this.Deliveryrateofofflineorders = [];
      if (this.orderdayormoth === "1") {
        this.OfflineandoutputM();
      } else {
        this.OfflineandoutputY();
      }
    },
    ordchange() {
      this.ReceiveNumdtos = [];
      this.Dailyoutputoftheproject = [];
      this.Deliveryrateofofflineorders = [];
      if (this.orderdayormoth === "1") {
        this.OfflineandoutputM();
      } else {
        this.OfflineandoutputY();
      }
    },
    OfflineandoutputY() {
      this.spinning = true;
      ppEOrderyeardatastatistics(this.FactoryId)
        .then(res => {
          if (res.code) {
            this.ReceiveNumdtos = [
              this.generateDayData(res.data.receiveNumdtos[0], "month", "dtos"), //总接单款数
              this.generateDayData(res.data.offlineNumdtos[0], "month", "dtos"), //总下线品种
              this.generateDayData(res.data.offlineRatedtos[0], "month", "dtos"), //款数下线率
            ];
            this.Dailyoutputoftheproject = [
              this.generateDayData(res.data.offlineNewNumdtos[0], "month", "dtos"), //新单
              this.generateDayData(res.data.offlineReAlterNumdtos[0], "month", "dtos"), //返单更改
              this.generateDayData(res.data.offlineReNumdtos[0], "month", "dtos"), //返单
              this.generateDayData(res.data.offlineNumdtos[0], "month", "dtos"), //总计
            ];
            this.Deliveryrateofofflineorders = [
              this.generateDayData(res.data.offlineNumdtos[0], "month", "dtos"), //下线订单(款数)
              this.generateDayData(res.data.offlineTimeReachNumdtos[0], "month", "dtos"), //时效达标订单(款数)
              this.generateDayData(res.data.offlineTimeReachRatedtos[0], "month", "dtos"), //时效达成率(%)
            ];
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    OfflineandoutputM() {
      // this.FactoryId 筛选工厂
      this.spinning = true;
      ppEOrdermonthdatastatistics(this.FactoryId)
        .then(res => {
          if (res.code) {
            this.ReceiveNumdtos = [
              this.generateDayData(res.data.receiveNumdtos[0], "day", "dDtos"), //总接单款数
              this.generateDayData(res.data.offlineNumdtos[0], "day", "dDtos"), //总下线品种
              this.generateDayData(res.data.offlineRatedtos[0], "day", "dDtos"), //款数下线率
            ];
            this.Dailyoutputoftheproject = [
              this.generateDayData(res.data.offlineNewNumdtos[0], "day", "dDtos"), //新单
              this.generateDayData(res.data.offlineReAlterNumdtos[0], "day", "dDtos"), //返单更改
              this.generateDayData(res.data.offlineReNumdtos[0], "day", "dDtos"), //返单
              this.generateDayData(res.data.offlineNumdtos[0], "day", "dDtos"), //总计
            ];
            this.Deliveryrateofofflineorders = [
              this.generateDayData(res.data.offlineNumdtos[0], "day", "dDtos"), //下线订单(款数)
              this.generateDayData(res.data.offlineTimeReachNumdtos[0], "day", "dDtos"), //时效达标订单(款数)
              this.generateDayData(res.data.offlineTimeReachRatedtos[0], "day", "dDtos"), //时效达成率(%)
            ];
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    rowSpan2(key) {
      let _list = this.Departmentaloutputachievementrate;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组

      for (let i = 0; i < _list.length; i++) {
        // 最后一条的 +1 时会为undefined 报错，所以需要给判断
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        // 当与下一条数据不同时，添加当前数据到_num,并进行处理
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); // 获取相同的下标

          // 遍历相同数据的数组
          for (let z = 0; z < _num.length; z++) {
            // 第一个设置需要合并的数值
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              // 其他设置为0
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          // 当与下一行不同时，切记清除数组，因为一列会有多个合并表格
          _num = [];
          indexList = [];
          continue;
        } else {
          // 当与下一条数据相同时，添加当前数据到_num,不进行处理
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.Departmentaloutputachievementrate = _list;
    },
    generateDayData(data, time, dtos) {
      return {
        name: data.name,
        ...data[dtos].reduce((acc, dayData, index) => {
          if (!dayData.day && time == "day") {
            acc.Summary = dayData.value;
          } else if (!dayData.month && time == "month") {
            acc.Summary = dayData.value;
          } else {
            acc[time + (index + 1)] = dayData.value;
          }
          acc.name = data.name;
          return acc;
        }, {}),
      };
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.rectangle {
  position: relative;
  width: 195px;
  height: 39px;
  background: transparent; /* 背景透明 */
  overflow: hidden; /* 防止伪元素超出边界 */
}

.rectangle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(192deg, transparent 49%, #818181, transparent 51%);
}
/deep/.ant-form-item {
  padding-left: 23px;
  margin-bottom: 0px;
}
/deep/.ant-form-item-label {
  width: 70px;
}
/deep/.ant-select {
  width: 200px;
}
/deep/.factoryscreening .ant-select {
  width: 130px;
}
/deep/.ant-table-thead > tr > th {
  height: 27px !important;
}
/deep/.ant-table-tbody > tr > td {
  height: 27px !important;
}

/deep/ .ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
  .rowBackgroundColor {
    background: #dcdcdc !important;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
/deep/.ant-table-body {
  overflow-x: scroll;
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    // background: #ff9900;
    background: #e7e7e7;
    // #fff9e6
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
/deep/.ant-table-wrapper {
  border-top: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
}
/deep/ .ant-table-thead > tr > th {
  height: 27px !important;
}
/deep/ .ant-table .ant-table-tbody > tr > td {
  height: 27px !important;
}
/deep/.ant-table-thead > tr > th,
/deep/.ant-table-tbody > tr > td {
  padding: 4px;
  overflow-wrap: break-word;
}
/deep/.ant-select-selection--single {
  border: #ff9900 1px solid;
}
/deep/.analysishp {
  .ant-calendar-picker {
    position: relative;
    height: 32px;
    cursor: pointer;
    width: 200px;
    top: 18px;
    left: 20px;
    .anticon svg {
      display: inline-block;
      position: relative;
      top: -10px;
    }
  }
  .ant-select-selection--single {
    width: 200px;
    border: #ff9900 1px solid;
    margin: 15px 15px 0 15px;
  }
  .ant-table-align-center {
    background-color: white;
  }
  .ant-table-bordered .ant-table-thead > tr > th,
  .ant-table-bordered .ant-table-tbody > tr > td {
    border-left: 1px solid #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: white;
}
/deep/.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: #ff9900 !important;
}
/deep/.ant-input {
  border: #ff9900 1px solid;
}
.analysis {
  width: 100%;
  height: 100%;
  background: #ffffff;
  min-height: 780px;
}
/deep/.centered-span {
  font-size: 30px;
  font-weight: bold;
  color: rgb(255, 153, 0);
  display: block;
  text-align: center;
}
</style>
