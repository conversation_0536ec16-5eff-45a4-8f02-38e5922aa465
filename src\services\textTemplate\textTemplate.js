import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
// const BASE_URL = process.env.VUE_APP_API_MAIN_URL
export async function getText(params) {
    return request("/api/text-template-management/template-definitions", METHOD.GET, transformAbpListQuery(params))
}
export async function editText(params) {
    return request("/api/text-template-management/template-contents", METHOD.PUT, params)
}


export default {
    getText,
    editText
}