<template>
  <div style="user-select: none">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :scroll="{ y: 665, x: 300 }"
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      :pagination="false"
      :rowClassName="isRedRow"
      :customRow="onClickRow"
    >
      <div slot="orderNo" slot-scope="text, record">
        <a style="color: black" :title="record.orderNo">{{ record.orderNo }}</a
        >&nbsp;
        <span
          class="noCopy"
          v-if="record.isJiaji"
          style="
            font-size: 14px;
            font-weight: 500;
            color: #ff9900;
            padding: 0;
            display: inline-block;
            height: 19px;
            width: 18px;
            margin-left: -10px;
            user-select: none;
          "
          ><a-icon type="thunderbolt" theme="filled"></a-icon>
        </span>
      </div>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>
<script>
import RightCopy from "@/pages/RightCopy";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "本厂编码",
    align: "left",
    // dataIndex: 'orderNo',
    width: 130,
    scopedSlots: { customRender: "orderNo" },
    ellipsis: true,
  },
  {
    title: "订单类型",
    align: "left",
    dataIndex: "reOrderType",
    width: 65,
    ellipsis: true,
  },
  {
    title: "层数",
    align: "left",
    dataIndex: "boardLayers",
    width: 50,
    ellipsis: true,
  },
  {
    title: "制作人",
    align: "left",
    dataIndex: "proAdminName",
    width: 70,
    ellipsis: true,
  },
  {
    title: "审核人",
    align: "left",
    dataIndex: "camCheckName",
    width: 70,
    ellipsis: true,
  },
  {
    title: "制作完成时间",
    align: "left",
    dataIndex: "miEndDate_",
    width: 130,
    ellipsis: true,
  },
  {
    title: "审核完成时间",
    align: "left",
    dataIndex: "camEndTime",
    width: 130,
    ellipsis: true,
  },
  {
    title: "类型",
    align: "left",
    dataIndex: "type",
    width: 90,
    ellipsis: true,
  },
  {
    title: "发送人",
    align: "left",
    dataIndex: "sendName",
    width: 85,
    ellipsis: true,
  },
  {
    title: "发送时间",
    align: "left",
    dataIndex: "sendDate",
    width: 130,
    ellipsis: true,
  },
  {
    title: "接收人",
    align: "left",
    dataIndex: "reciveName",
    width: 70,
    ellipsis: true,
  },
  {
    title: "接收时间",
    align: "left",
    dataIndex: "reciveDate",
    width: 110,
    ellipsis: true,
  },
  {
    title: "交货日期",
    align: "left",
    dataIndex: "delDate",
    width: 150,
    ellipsis: true,
  },
  {
    title: "尺寸",
    align: "left",
    dataIndex: "boardSize",
    width: 120,
    ellipsis: true,
  },
  {
    title: "订单面积",
    align: "left",
    dataIndex: "boardArea",
    width: 65,
    ellipsis: true,
  },
  {
    title: "测试点数量",
    align: "left",
    dataIndex: "testPointNum",
    width: 80,
    ellipsis: true,
  },
  {
    title: "拼板款数",
    align: "left",
    dataIndex: "pinBanNum",
    width: 80,
    ellipsis: true,
  },
  {
    title: "总测试点",
    align: "left",
    dataIndex: "delQtyAll",
    width: 80,
    ellipsis: true,
  },
];
export default {
  name: "",
  components: { RightCopy },
  props: ["dataSource", "loading"],
  data() {
    return {
      columns,
      selectrowdata: {},
      selectedRowKeysArray: [],
      isDragging: false,
      startIndex: -1,
      shiftKey: false,
    };
  },
  methods: {
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeysArray;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowKeysArray = rowKeys;
        if (this.selectedRowKeysArray.length == 1) {
          this.selectrowdata = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectrowdata = record;
          this.selectedRowKeysArray = [record.id];
        }
      }
      this.$emit("getfixture", record, index, "top");
      this.shiftKey = false;
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectrowdata = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectrowdata.length; a++) {
        arr.push(selectrowdata[a].id);
      }
      this.selectedRowKeysArray = arr;
      if (startIndex < endIndex) {
        this.selectrowdata = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length - 1];
        })[0];
      } else {
        this.selectrowdata = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[0];
        })[0];
      }
    },
    onClickRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
};
</script>
<style lang="less" scoped>
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
</style>
