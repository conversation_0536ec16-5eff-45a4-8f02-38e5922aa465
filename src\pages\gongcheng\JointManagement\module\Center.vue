<!-- 工程管理 - 合拼管理 -列表 -->
<template>
  <a-card :bordered="false">
    <div class="machine" style="border: 0px solid #E9E9F0;">
      <div class="left" style="border: 1px solid #E9E9F0;height: 780px">
        <div class="top" style="border-bottom: 2px solid #E9E9F0;">
          <!-- 配置参数 -->
          <a-table
              :rowKey="(record,index)=>{return index}"
              :columns="columns2"
              :dataSource="machineStatuList"
              :pagination="false"
              :loading="machineStatuLoad"
              :keyboard="false"
              :bordered="true"
              :scroll="{y:215}"
              :maskClosable="false"
              :class="machineStatuList.length? 'mintable':''"
          >
          <template slot='fristValue_' slot-scope="text,record">
            <a-input v-model="record.fristValue_" @blur="save1(record)"/>
          </template>
          <template slot='secondValue_' slot-scope="text,record">
            <a-input v-model="record.secondValue_" @blur="save2(record)"/>
          </template>
          <template>
          </template>
          </a-table>
        </div>
        <div class="bot" style=''>
          <!-- 明细订单 -->
          <a-table              
              :rowKey="(record,index)=>{return index}"
              :columns="columns3"
              :dataSource="drillList"
              :pagination="false"
              :loading="drillLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :scroll="{y:468}"
          >
          </a-table>       
        </div>
      </div>      
    </div>
  </a-card>
</template>

<script>
const columns2 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 20,
    align: 'center',
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "项目参数",
    dataIndex: "captions_",
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "参数1",
    dataIndex: "fristValue_",
    width: 45,
    align: 'center',
    className:'heightSTY',
    scopedSlots: { customRender: 'fristValue_' }  

  },
  {
    title: "参数2",
    dataIndex: "secondValue_",
    ellipsis: true,
    width: 45,
    align: 'center',
    className:'heightSTY',
    scopedSlots:{ customRender:'secondValue_'}
  },  
]
const columns3 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 30,
    align: 'center',
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "生产型号",
    dataIndex: "pdctno_",
    width: 70,
    ellipsis: true,
    className:"orderClass",
    align: 'center',
  },
  {
    title: "交数",
    dataIndex: "qty_",
    width: 40,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "投数",
    dataIndex: "used_no",
    width: 40,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "拼数",
    dataIndex: "units_no",
    width: 40,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "加投",
    dataIndex: "reArea_",
    width: 40,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 40,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 40,
    ellipsis: true,
    align: 'center',
  },
]
export default {
  name:'Center',  
  props:{      
    machineStatuList:{
      type: Array
    },
    machineStatuLoad:{
      type: Boolean
    },
    drillList:{
      type: Array
    },
    drillLoad:{
      type: Boolean
    },    
  },
  data () {
    return {
      columns2,
      columns3,     
    }
  },
  methods:{
    rightClick(e,text){
      this.$emit('rightClick',e,text)
    },
    // 修改参数1
    save1(record){  
      console.log(record)     
      // updatePnl(record.guid_,{'qty':record.pnlQty_}).then(res => {
      //   if (res.code == 1) {
      //     this.$message.success('成功')
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
    },  
    // 修改参数2
    save2(record){  
      console.log(record)    
      // updatePnl(record.guid_,{'qty':record.pnlQty_}).then(res => {
      //   if (res.code == 1) {
      //     this.$message.success('成功')
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
    },     
  },
 
}
</script>

<style lang="less" scoped>
/deep/.heightSTY{
}
.machine {
  height: 780px;
  display: flex;
  .left {
    width: 100%;
    /deep/.top{
      height:254px;
      .mintable{
        .ant-table-body{
          min-height: 215px;
        }
       
      }
      .ant-table .ant-table-tbody > tr > td {
          height: 36px!important;
      }
    }
   /deep/ .bot{
    height:540px;
      .orderClass{
        user-select: all;
      }
    }
  }

  /deep/ .ant-table-tbody {
    // .ant-table-row{
    //   td:first-child{
    //     user-select: all;
    //   }
    // }
    .clickRowStyl {
      background: #fff9e6;
    }
    
  }
  
}


</style>