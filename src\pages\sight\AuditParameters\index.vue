<template>
  <div ref="SelectBox" style="background: #d5e4f2; height: 100%" @click="click($event)">
    <div class="header">
      <a-button type="primary" style="background-color: #02906c; border-color: #02906c; width: 90px; padding: 0" @click="foldClick" v-if="fold">
        <a-icon type="minus-square"></a-icon>全部收起
      </a-button>
      <a-button type="primary" style="background-color: #02906c; border-color: #02906c; width: 90px; padding: 0" @click="foldClick" v-else>
        <a-icon type="plus-square" />全部展开
      </a-button>
      <div class="labelSearch" style="display: flex">
        <a-input type="text" autocomplete="off" id="labelSearchInput" allow-clear placeholder="搜索" v-model="searchText" @change="search" />
        <div class="searchRight">
          <span class="item" style="margin-right: 5px"> {{ num === 0 ? "0/0" : indexNum + 1 + "/" + num }}</span>
          <a-icon type="up" circle @click="prev"></a-icon>
          <a-icon type="down" circle @click="next"></a-icon>
        </div>
      </div>
    </div>
    <div class="content">
      <a-collapse v-model="activeNames" @change="changeActivekey">
        <template slot="expandIcon" slot-scope="record">
          <a-icon type="minus-square" v-if="record.isActive" />
          <a-icon type="plus-square" v-else />
        </template>
        <a-collapse-panel :key="String(itemIndex)" :header="item.typename" v-for="(item, itemIndex) in QuoteDataList" :forceRender="true">
          <div class="capacity" :dataIndex="itemIndex + 1">
            <a-form-model :model="QuoteForm" :label-col="{ span: 11 }" :wrapper-col="{ span: 13 }" id="formDataElem" ref="ulScroll">
              <span v-for="(it, Index) in item.dtos" :key="Index">
                <a-form-model-item
                  :label="it.descript"
                  v-if="it.descript != '金厚' && it.descript != '金面积比' && it.descript != '镍厚' && it.descript != '钯厚' && it.isview == true"
                  @click.native="labelClick(it.name, it.viewfield, it.funcstr)"
                  :class="[
                    it.isview == true ? (it.funcstr != '' && it.funcstr != null ? 'backsty2' : 'backsty') : '',
                    it.isrequired == true ? 'red' : '',
                  ]"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template v-if="it.name == 'InnerCuThick' || it.name == 'OuterCuThick' || it.name == 'PinBanType' || it.name == 'DrillStructs'">
                      <a-input
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="it.type == '文本输入' || it.type == '参数栏'"
                        readOnly
                        @change="change(it.name)"
                        @click.stop="click1(it.name)"
                      />
                    </template>
                    <template v-else>
                      <a-input
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click.stop="click1(it.name)"
                      />
                    </template>
                    <a-checkbox v-model="QuoteForm[it.name].Value" v-if="it.type == '勾选项'" @change="change(it.name)" />
                    <a-textarea
                      :title="QuoteForm[it.name].Value"
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                      :getPopupContainer="() => $refs.SelectBox"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      :getPopupContainer="() => $refs.SelectBox"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      mode="multiple"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      class="sty"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'InnerCuThick' && visible && list.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list" :key="index">
                          第{{ index + 1 }}层:
                          <a-input
                            v-model="formList[index]"
                            @change="listChange(index, formList[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList[index]" @change="listChange(index, formList[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'OuterCuThick' && visible1 && list1.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list1" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">顶层:</span>
                          <span v-else style="margin: 0 5px">底层:</span>
                          <a-input
                            v-model="formList1[index]"
                            @change="listChange(index, formList1[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList1[index]" @change="listChange(index, formList1[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'PinBanType' && visible2 && list2.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list2" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">X:</span>
                          <span v-else style="margin: 0 5px">Y:</span>
                          <a-input
                            v-model="formList2[index]"
                            @change="listChange(index, formList2[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList2[index]" @change="listChange(index, formList2[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                  </div>
                </a-form-model-item>
                <a-form-model-item
                  :label="it.descript"
                  v-else-if="it.descript != '金厚' && it.descript != '金面积比' && it.descript != '镍厚' && it.descript != '钯厚'"
                  :class="[it.isrequired == true ? 'red' : '']"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template v-if="it.name == 'InnerCuThick' || it.name == 'OuterCuThick' || it.name == 'PinBanType'">
                      <a-input
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="it.type == '文本输入' || it.type == '参数栏'"
                        readOnly
                        @change="change(it.name)"
                        @click.stop="click1(it.name)"
                      />
                    </template>
                    <template v-else>
                      <a-input
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click="click1(it.name)"
                      />
                    </template>

                    <a-checkbox v-model="QuoteForm[it.name].Value" v-if="it.type == '勾选项'" @change="change(it.name)" />
                    <a-textarea
                      :title="QuoteForm[it.name].Value"
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      :getPopupContainer="() => $refs.SelectBox"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      mode="multiple"
                      class="sty"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'InnerCuThick' && visible && list.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list" :key="index">
                          第{{ index + 1 }}层:
                          <a-input
                            v-model="formList[index]"
                            @change="listChange(index, formList[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList[index]" @change="listChange(index, formList[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'OuterCuThick' && visible1 && list1.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list1" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">顶层:</span>
                          <span v-else style="margin: 0 5px">底层:</span>
                          <a-input
                            v-model="formList1[index]"
                            @change="listChange(index, formList1[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList1[index]" @change="listChange(index, formList1[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'PinBanType' && visible2 && list2.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list2" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">X:</span>
                          <span v-else style="margin: 0 5px">Y:</span>
                          <a-input
                            v-model="formList2[index]"
                            @change="listChange(index, formList2[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList2[index]" @change="listChange(index, formList2[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                  </div>
                </a-form-model-item>
                <a-form-model-item
                  :label="it.descript"
                  v-else-if="
                    (it.descript == '金厚' || it.descript == '金面积比' || it.descript == '镍厚') &&
                    JSON.stringify(QuoteForm) != '{}' &&
                    JSON.stringify(QuoteForm.SurfaceTreat) != '{}' &&
                    QuoteForm.SurfaceTreat.Value &&
                    (QuoteForm.SurfaceTreat.Value.includes('BS018004') ||
                      QuoteForm.SurfaceTreat.Value.includes('BS018005') ||
                      QuoteForm.SurfaceTreat.Value.includes('BS018007') ||
                      QuoteForm.SurfaceTreat.Value.includes('BS018014'))
                  "
                  :class="[it.isrequired == true ? 'red' : '']"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template>
                      <a-input
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click="click1(it.name)"
                      />
                    </template>

                    <a-checkbox
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '勾选项'"
                      @change="change(it.name)"
                    />
                    <a-textarea
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                      :getPopupContainer="() => $refs.SelectBox"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      mode="multiple"
                      class="sty"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-form-model-item>
                <a-form-model-item
                  :label="it.descript"
                  v-else-if="
                    it.descript == '钯厚' &&
                    JSON.stringify(QuoteForm) != '{}' &&
                    JSON.stringify(QuoteForm.SurfaceTreat) != '{}' &&
                    QuoteForm.SurfaceTreat.Value &&
                    QuoteForm.SurfaceTreat.Value.includes('BS018014')
                  "
                  :class="[it.isrequired == true ? 'red' : '']"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template>
                      <a-input
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click="click1(it.name)"
                      />
                    </template>

                    <a-checkbox v-model="QuoteForm[it.name].Value" v-if="it.type == '勾选项'" @change="change(it.name)" />
                    <a-textarea
                      :title="QuoteForm[it.name].Value"
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      :getPopupContainer="() => $refs.SelectBox"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      mode="multiple"
                      class="sty"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-form-model-item>
              </span>
              <template> </template>
            </a-form-model>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
    <div class="buton">
      <a-button type="primary" @click="handleSubmit" :disabled="submitFormBtn" style="background-color: #02906c; border-color: #02906c">
        保存
      </a-button>
      <a-button type="primary" @click="openGoldfinger" style="margin-left: 2%; background-color: #02906c; border-color: #02906c">
        金手指计算
      </a-button>
    </div>
  </div>
</template>
<script>
import { checkList, checkBillInfo, setCheckBillInfo, setSightInfoToEMS } from "@/services/sight/sight.js";
import $ from "jquery";
import { QWebChannel } from "@/utils/qwebchannel.js";
// import QWebChannel from '@/utils/qwebchannel'// qt通讯
const delay = (function () {
  let timer = 0;
  return function (callback, ms) {
    clearTimeout(timer);
    timer = setTimeout(callback, ms);
  };
})();
export default {
  name: "AuditParameters",
  inject: ["reload"],
  data() {
    return {
      QuoteDataList: [],
      activeKey: [0],
      fold: false,
      QuoteForm: {},
      QuoteData: {},
      submitFormBtn: false,
      contextHtml: "",
      nowNum: 0,
      totalNum: 0,
      inputUpDisable: true,
      inputDownDisable: false,
      visible: false,
      list: [],
      formList: {},
      list1: [],
      formList1: {},
      visible1: false,
      list2: [],
      formList2: {},
      visible2: false,
      autoFocus: true,
      searchText: "",
      index: 1,
      query: "",
      indexNum: 0,
      num: 0,
      main: null,
      showArr: [],
      showIndex: 0,
      activeNames: [0],
      routeList: {},
      isV3Version: false,
    };
  },

  async created() {
    var obj = {};
    var tempKey = Object.keys(this.$route.query);
    for (var ite = 0; ite < tempKey.length; ite++) {
      obj[tempKey[ite].toLowerCase()] = this.$route.query[tempKey[ite]];
    }
    this.routeList = obj;
    if (this.routeList.version == "iPCB-V3") {
      this.isV3Version = true;
    }
    await this.getList();
    await this.getData();
  },
  async mounted() {
    this.$nextTick(() => {
      new QWebChannel(qt.webChannelTransport, function (channel) {
        window.click_listener = channel.objects.click_listener;
      });
    });
    window.vue = this;
    // await this.chatTest();
    window.addEventListener("keydown", this.keydown);
    const UserOption = window.external.GerCheckBill(JSON.stringify({}));
    this.submitFormBtn = !JSON.parse(UserOption).UserOption;
  },
  filters: {
    filterData(data, list) {
      let name = "";
      list.filter(item => {
        if (item.key == data) {
          name = item.Value;
        }
      });
      return name;
    },
  },
  watch: {
    QuoteForm: {
      handler(val, ol) {
        if (JSON.stringify(val) != "{}") {
          let val1 = "";
          let val2 = "";
          let val3 = "";
          let arr1 = this.QuoteDataList.filter(item => {
            return item.typename == "自动SPEC参数";
          })[0];
          if (JSON.stringify(arr1) != "{}" && arr1) {
            let arr2 = arr1.dtos.filter(item => {
              return item.name == "SolderMaskColor";
            })[0];
            let obj = arr2.pars;
            if ((val.SolderMaskColor && val.SolderMaskColor.Value != null) || val.SolderMaskColor.Value != "") {
              let colorStr = "";
              if (Object.keys(obj).indexOf(val.SolderMaskColor.Value) >= 0) {
                colorStr = obj[val.SolderMaskColor.Value];
              }
              if (colorStr.indexOf("蓝") > -1) {
                val2 = "0x002BAA";
              } else if (colorStr.indexOf("黑") > -1) {
                val2 = "0x151B23";
              } else if (colorStr.indexOf("红") > -1) {
                val2 = "0xC62525";
              } else if (colorStr.indexOf("黄") > -1) {
                val2 = "0xFD901C";
              } else if (colorStr.indexOf("白") > -1) {
                val2 = "0xFDF4EE";
              } else if (colorStr.indexOf("紫") > -1) {
                val2 = "0x800080";
              } else {
                val2 = "0x1d7e18";
              }
            } else {
              val2 = "0x1d7e18";
            }
            if ((val.SilkColor && val.SilkColor.Value != null) || val.SilkColor.Value != "") {
              let colorStr = "";
              if (Object.keys(obj).indexOf(val.SilkColor.Value) >= 0) {
                colorStr = obj[val.SilkColor.Value];
              }
              if (colorStr.indexOf("蓝") > -1) {
                val3 = "0x002BAA";
              } else if (colorStr.indexOf("黑") > -1) {
                val3 = "0x151B23";
              } else if (colorStr.indexOf("黄") > -1) {
                val3 = "0xFFFF00";
              } else if (colorStr.indexOf("红") > -1) {
                val3 = "0xC62525";
              } else {
                val3 = "0xFFFFFF";
              }
            } else {
              val3 = "0xFFFFFF";
            }
          }
          if (val.SurfaceTreat) {
            if (JSON.stringify(val.SurfaceTreat) != "{}" && val.SurfaceTreat.Value && val.SurfaceTreat.Value.length) {
              if (val.SurfaceTreat.Value[0] == "BS018001" || val.SurfaceTreat.Value[0] == "BS018002") {
                val1 = "0xC1CBD0";
              } else if (val.SurfaceTreat.Value[0] == "BS018003") {
                val1 = "0xB7B7B7";
              } else {
                val1 = "0xFFD700";
              }
            } else {
              val1 = "0xFFD700";
            }
          }
          if (val.BoardThick) {
            window.external.ModifyReviewData(
              JSON.stringify({
                Thickness: { Value: val.BoardThick.Value, Unit: "mm" },
                Color: { TopSignal: val1, TopSolder: val2, TopSilkScreen: val3 },
              })
            );
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    setCreateSetInfo(pars) {
      let Form = JSON.parse(pars);
      for (var a = 0; a < Object.keys(Form).length; a++) {
        if (Object.keys(this.QuoteForm).indexOf(Object.keys(Form)[a]) >= 0) {
          this.QuoteForm[Object.keys(Form)[a]] = { Value: Form[Object.keys(Form)[a]] };
        }
      }
    },
    setGerberData(pars) {
      // alert(pars)
      let obj = JSON.parse(pars).BaseResult;
      // for (const key in obj) {
      //     obj[key].value = obj[key].Value;
      //     delete obj[key].Value;
      // }
      for (const key in obj) {
        if (obj[key].value !== undefined) {
          obj[key].Value = obj[key].value;
          delete obj[key].value;
        }
      }
      let copyQuoteForm = obj;
      for (var a = 0; a < Object.keys(copyQuoteForm).length; a++) {
        if (Object.keys(this.QuoteForm).indexOf(Object.keys(copyQuoteForm)[a]) >= 0) {
          this.QuoteForm[Object.keys(copyQuoteForm)[a]] = copyQuoteForm[Object.keys(copyQuoteForm)[a]];
        }
      }
    },
    async getList() {
      let softcode = "";
      if (this.routeList.softcode) {
        softcode = this.routeList.softcode;
      }
      var configtype = "0,2";
      await checkList(softcode, configtype).then(res => {
        if (res.code) {
          this.QuoteDataList = res.data;
          for (var i = 0; i < this.QuoteDataList.length; i++) {
            var obj = {};
            var tempKey = Object.keys(this.QuoteDataList[i]);
            for (var ite = 0; ite < tempKey.length; ite++) {
              obj[tempKey[ite].toLowerCase()] = this.QuoteDataList[i][tempKey[ite]];
            }
            this.QuoteDataList[i] = obj;
            this.QuoteDataList[i].dtos = this.convertKeysToLowerCase(this.QuoteDataList[i].dtos);
          }
          var objlist = {};
          for (var a = 0; a < this.QuoteDataList.length; a++) {
            for (var b = 0; b < this.QuoteDataList[a].dtos.length; b++) {
              var name = this.QuoteDataList[a].dtos[b].name;
              if (!Object.keys(this.QuoteForm).includes(name)) {
                var str1 = name;
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  objlist[str1] = { Value: false };
                }
                if (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == true) {
                  objlist[str1] = { Value: [] };
                }
                if (
                  (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == false) ||
                  this.QuoteDataList[a].dtos[b].type == "文本输入" ||
                  this.QuoteDataList[a].dtos[b].type === "参数栏" ||
                  this.QuoteDataList[a].dtos[b].type === "下拉输入栏"
                ) {
                  objlist[str1] = { Value: "" };
                }
              }
            }
          }
          this.QuoteForm = objlist;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    convertKeysToLowerCase(arr) {
      return arr.map(obj =>
        Object.keys(obj).reduce((newObj, key) => {
          newObj[key.toLowerCase()] = obj[key];
          return newObj;
        }, {})
      );
    },
    async getData(aa) {
      let Boid = "";
      if (this.routeList.boid != undefined) {
        Boid = this.routeList.boid;
      }
      let sightsite = "";
      if (this.routeList.sight != undefined) {
        sightsite = this.routeList.sight;
      }
      await checkBillInfo(Boid, sightsite).then(res => {
        if (res.code) {
          const obj = JSON.parse(res.data).BaseResult;
          // for (const key in obj) {
          //     //console.log('obj[key]',obj[key])
          //     obj[key].value = obj[key].Value;
          //     delete obj[key].Value;
          // }
          for (const key in obj) {
            if (obj[key].value !== undefined) {
              obj[key].Value = obj[key].value;
              delete obj[key].value;
            }
          }
          this.QuoteForm = obj;
          for (var a = 0; a < this.QuoteDataList.length; a++) {
            for (var b = 0; b < this.QuoteDataList[a].dtos.length; b++) {
              var name = this.QuoteDataList[a].dtos[b].name;
              var str = name;
              if (!this.QuoteForm[str]) {
                this.QuoteForm[str] = { Value: "" };
              }
              if (this.QuoteForm[str].Value == "" || this.QuoteForm[str].Value == "0") {
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str] = { Value: false };
                }
                if (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == true) {
                  this.QuoteForm[str] = { Value: [] };
                }
              }
              if (this.QuoteForm[str].Value == "true") {
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str] = { Value: true };
                }
              }
              if (this.QuoteForm[str].Value == "false") {
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str] = { Value: false };
                }
              }
              if (!Object.keys(this.QuoteForm).includes(name)) {
                var str1 = name;
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str1] = { Value: false };
                }
                if (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == true) {
                  this.QuoteForm[str1] = { Value: [] };
                } else {
                  this.QuoteForm[str1] = { Value: "" };
                }
              }
            }
          }
        }
      });
    },
    // 控制全部展开、折叠
    changeActivekey(val) {
      // this.activeKey = val
      if (val.length == this.QuoteDataList.length) {
        this.fold = true;
      } else {
        this.fold = false;
      }
    },
    foldClick() {
      if (this.fold) {
        this.activeNames = [];
      } else {
        for (var a = 0; a < this.QuoteDataList.length; a++) {
          if (!this.activeNames.includes(a)) {
            var stra = a.toString();
            this.activeNames.push(stra);
          }
          // this.activeKey.push(a)
        }
      }
      this.fold = !this.fold;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { Value: item, lable: data[item] };
        });
      }
    },
    mapKey1(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data)
          .sort()
          .map(item => {
            return { Value: item, lable: data[item] };
          });
      }
    },
    change(val) {
      if (val == "Layer") {
        let arr_ = [];
        for (var i = 0; i < this.QuoteForm.Layer.Value - 2; i++) {
          arr_.push(1);
        }
        this.QuoteForm.InnerCuThick.Value = arr_.join("/");
        this.list = arr_;
        for (var key in this.list) {
          this.formList[key] = this.list[key];
        }
        // this.QuoteForm.OuterCuThickBotOuterCuThick.value = '1/1'
        // this.list1 = [1,1]
        // for (var key1 in this.list1) {
        // this.formList1[key1] = this.list1[key1];
        // }
      }
      //    alert(this.QuoteForm.Layer.value)
      this.$forceUpdate();
    },
    blur(Name, IsRegex, RegexInfo) {
      if (IsRegex && RegexInfo) {
        var z = new RegExp(RegexInfo);
        if (!z.test(this.QuoteForm[Name].Value)) {
          this.QuoteForm[Name].Value = "";
        }
      }
    },
    setEstimate(Value, name) {
      this.QuoteForm[name].Value = Value;
    },
    handleSearch(Value, name) {
      this.setEstimate(Value, name);
    },
    handleBlur(Value, name) {
      this.setEstimate(Value, name);
    },
    click1(val) {
      if (val == "InnerCuThick") {
        if (this.QuoteForm.InnerCuThick.Value) {
          this.list = this.QuoteForm.InnerCuThick.Value.split("/");
          for (var keyy in this.list) {
            this.formList[keyy] = this.list[keyy];
          }
        } else {
          let arr_ = [];
          for (var i = 0; i < this.QuoteForm.Layer.Value - 2; i++) {
            arr_.push(1);
          }
          this.list = arr_;
          for (var key in this.list) {
            this.formList[key] = this.list[key];
          }
        }
        this.visible = true;
        this.visible1 = false;
        this.visible2 = false;
      }
      if (val == "OuterCuThickBotOuterCuThick") {
        if (this.QuoteForm.OuterCuThickBotOuterCuThick.Value) {
          this.list1 = this.QuoteForm.OuterCuThickBotOuterCuThick.Value.split("/");
          for (var key1 in this.list1) {
            this.formList1[key1] = this.list1[key1];
          }
        } else {
          this.list1 = ["", ""];
          for (var key2 in this.list1) {
            this.formList1[key2] = this.list1[key2];
          }
        }
        this.visible = false;
        this.visible1 = true;
        this.visible2 = false;
      }
      if (val == "PinBanType") {
        if (this.QuoteForm.PinBanType.Value) {
          this.list2 = this.QuoteForm.PinBanType.Value.toLowerCase().split("x");
          for (var aa in this.list2) {
            this.formList2[aa] = this.list2[aa];
          }
        } else {
          this.list2 = [1, 1];
          for (var bb in this.list2) {
            this.formList2[bb] = this.list2[bb];
          }
          this.QuoteForm.PinBanType.Value = this.list2.join("x");
        }
        this.visible = false;
        this.visible1 = false;
        this.visible2 = true;
      }
    },
    click(e) {
      this.visible = false;
      this.visible1 = false;
      this.visible2 = false;
    },
    listChange(index, item, name) {
      if (name == "InnerCuThick") {
        for (var i = 0; i < this.list.length; i++) {
          if (index == 0) {
            if (i >= index) {
              this.list[i] = item;
            }
          } else {
            if (i == index) {
              this.list[i] = item;
            }
          }
        }
        for (var key in this.list) {
          this.formList[key] = this.list[key];
        }
        this.QuoteForm.InnerCuThick.Value = this.list.join("/");
      }
      if (name == "OuterCuThickBotOuterCuThick") {
        for (var j = 0; j < this.list1.length; j++) {
          if (j >= index) {
            this.list1[j] = item;
          }
        }
        for (var key1 in this.list1) {
          this.formList1[key1] = this.list1[key1];
        }
        this.QuoteForm.OuterCuThickBotOuterCuThick.Value = this.list1.join("/");
      }
      if (name == "PinBanType") {
        for (var A = 0; A < this.list2.length; A++) {
          if (A == index) {
            this.list2[A] = item;
          }
        }
        for (var AA in this.list2) {
          if (this.list2[AA] == "") {
            this.list2[AA] = "1";
          }
          this.formList2[AA] = this.list2[AA];
        }
        this.QuoteForm.PinBanType.Value = this.list2.join("x");
      }
    },
    handleSubmit() {
      var arr = [];
      for (var a = 0; a < this.QuoteDataList.length; a++) {
        for (var b = 0; b < this.QuoteDataList[a].dtos.length; b++) {
          var name = this.QuoteDataList[a].dtos[b].name;
          var descript = this.QuoteDataList[a].dtos[b].descript;
          var isrequired = this.QuoteDataList[a].dtos[b].isrequired;
          var mulselect = this.QuoteDataList[a].dtos[b].mulselect;
          if (isrequired && !this.QuoteForm[name].Value && !mulselect) {
            arr.push(descript);
          }
          if (isrequired && mulselect && (!this.QuoteForm[name].Value || this.QuoteForm[name].Value.length == 0)) {
            arr.push(descript);
          }
        }
      }
      if (arr.length) {
        this.$message.warning("请填写必填项" + arr);
        return;
      }
      var obj = {};
      // this.QuoteForm.DrillStructs.value = JSON.parse(this.QuoteForm.DrillStructs.value)
      obj.BaseResult = this.QuoteForm;
      let str = JSON.stringify(obj);
      let params = {
        boid: this.routeList.boid,
        softcode: this.routeList.softcode ? this.routeList.softcode : "",
        mac: "",
        str: str,
        sightsite: this.routeList.sight,
      };
      let params1 = {
        boid: this.routeList.boid,
        softcode: this.routeList.softcode ? this.routeList.softcode : "",
        // "mac":"",
        // "str": str,
      };
      setCheckBillInfo(params).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          this.getData("1");
          setSightInfoToEMS(params1.boid, params1.softcode).then(re => {
            if (!re.code) {
              this.$message.error(re.message);
            }
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    labelClick(name, viewfield, FuncStr) {
      if (FuncStr != "" && FuncStr != null) {
        if (FuncStr == "openIaterial") {
          this.openIaterial(name);
        }
        if (FuncStr == "openDrillStruct") {
          this.openDrillStruct(name);
        }
      } else {
        //ReviewInfo
        if (this.isV3Version) {
          if (window.click_listener) {
            // 发送数据到 C++
            window.click_listener.ReviewInfo(
              JSON.stringify({
                name: viewfield,
                Value: this.QuoteForm[name].Value,
                unit: "",
              })
            );
          } else {
            //console.error('Qt WebEngine 未初始化，无法创建 QWebChannel');
          }
        } else {
          window.external.ReviewInfo(
            JSON.stringify({
              name: viewfield,
              Value: this.QuoteForm[name].Value,
              unit: "",
            })
          );
        }
      }
    },
    openIaterial(name) {
      // if(window.external && typeof window.external.OpenIaterial != 'undefined' && typeof window.external.OpenIaterial != undefined){
      var PCSWidth = "";
      if (this.QuoteForm.PCSWidth.Value) {
        PCSWidth = this.QuoteForm.PCSWidth.Value.toString();
      }
      var PCSHeight = "";
      if (this.QuoteForm.PCSHeight.Value) {
        PCSHeight = this.QuoteForm.PCSHeight.Value.toString();
      }
      var PCSHeightUnit = "mm";
      var SetWidth = "";
      if (this.QuoteForm.SetWidth.Value) {
        SetWidth = this.QuoteForm.SetWidth.Value.toString();
      }
      var SetHeight = "";

      if (this.QuoteForm.SetHeight.Value) {
        SetHeight = this.QuoteForm.SetHeight.Value.toString();
      }
      var SetHeightUnit = "mm";
      var Layer = "";
      if (this.QuoteForm.Layer.Value) {
        Layer = this.QuoteForm.Layer.Value.toString();
      }
      var SetX =
        this.QuoteForm.PinBanType.Value && this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[0]
          ? this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[0]
          : "";
      var SetY =
        this.QuoteForm.PinBanType.Value && this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[1]
          ? this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[1]
          : "";
      window.external.OpenIaterial(
        JSON.stringify({
          Layer: Layer,
          PCSWidth: PCSWidth,
          PCSHeight: PCSHeight,
          SetWidth: SetWidth,
          SetHeight: SetHeight,
          SetX: SetX,
          SetY: SetY,
        })
      );
      // }
    },
    setCuttingInfo(data) {
      //点击(开料按钮)保存IPCB-SIGHT后，软件会返回开料信息
      if (data) {
        data = JSON.parse(data);
        // alert(data.SetX)
        // alert(data.SetHeight)
        // alert(data.SetWidth)
        // alert(data.SetY)
        if (Object.keys(this.QuoteForm).indexOf("SheetSize") >= 0) {
          this.QuoteForm.SheetSize.Value = data.SheetSizeHeight + "*" + data.SheetSizeWidth;
        }
        if (Object.keys(this.QuoteForm).indexOf("SetHeight") >= 0) {
          this.QuoteForm.SetHeight.Value = data.SetHeight;
        }
        if (Object.keys(this.QuoteForm).indexOf("SetWidth") >= 0) {
          this.QuoteForm.SetWidth.Value = data.SetWidth;
        }
        if (Object.keys(this.QuoteForm).indexOf("PinBanType") >= 0) {
          this.QuoteForm.PinBanType.Value = data.SetX + "x" + data.SetY;
        }
        if (Object.keys(this.QuoteForm).indexOf("PanelWidth") >= 0) {
          this.QuoteForm.PanelWidth.Value = data.PanelWidth;
        }
        if (Object.keys(this.QuoteForm).indexOf("PanelHeight") >= 0) {
          this.QuoteForm.PanelHeight.Value = data.PanelHeight;
        }
        if (Object.keys(this.QuoteForm).indexOf("SheetPerPanel") >= 0) {
          this.QuoteForm.SheetPerPanel.Value = data.PanelPerSheet;
        }
        if (Object.keys(this.QuoteForm).indexOf("SetPerPanel") >= 0) {
          this.QuoteForm.SetPerPanel.Value = data.SetPerPanel;
        }
        if (Object.keys(this.QuoteForm).indexOf("PCSPerPNL") >= 0) {
          this.QuoteForm.PCSPerPNL.Value = data.PCSPerPNL;
        }
        if (Object.keys(this.QuoteForm).indexOf("PCSPerSheet") >= 0) {
          this.QuoteForm.PCSPerSheet.Value = data.PCSPerSheet;
        }
        if (Object.keys(this.QuoteForm).indexOf("SheetSizeWidth") >= 0) {
          this.QuoteForm.SheetSizeWidth.Value = data.SheetSizeWidth;
        }
        if (Object.keys(this.QuoteForm).indexOf("SheetSizeHeight") >= 0) {
          this.QuoteForm.SheetSizeHeight.Value = data.SheetSizeHeight;
        }
        if (Object.keys(this.QuoteForm).indexOf("SheetUtilizationRate") >= 0) {
          this.QuoteForm.SheetUtilizationRate.Value = data.SheetUtilizationRate;
        }
        if (Object.keys(this.QuoteForm).indexOf("SU") >= 0) {
          this.QuoteForm.SU.Value = data.SetPcsNum;
        }
        // for(var a=0;a<Object.keys(data).length;a++){
        //   alert(Object.keys(data)[a])
        //   if(Object.keys(this.QuoteForm).indexOf(Object.keys(data)[a])>=0){
        //     this.QuoteForm[Object.keys(data)[a]] = data[Object.keys(data)[a]]
        //   }
        // }
      }
    },
    openDrillStruct(name) {
      //钻孔结构
      //console.error('钻孔结构',this.isV3Version,window.click_listener,'click_listener',this.webChannel)
      if (this.isV3Version) {
        if (window.click_listener) {
          // 发送数据到 C++
          window.click_listener.DrillStruct(
            location.protocol + "//" + location.host + "/drillStructure?data=" + this.QuoteForm[name].Value + "&version=iPCB-V3"
          );
        } else {
          //console.error('Qt WebEngine 未初始化，无法创建 QWebChannel');
        }
      } else {
        // this.QuoteForm[name].value = '[{"index":1,"FromLayer":"1","ToLayer":"4","IsLaser":"0","HoleCount":"197","MinHoleSize":"0.3"}]'
        // window.external.DrillStruct( 'http://cam.pcbpp.com:8088/ipcb/sight/drillStructure?data=' + this.QuoteForm[name].value);
        if (window.external && typeof window.external.DrillStruct != "undefined" && typeof window.external.DrillStruct != undefined) {
          window.external.DrillStruct(location.protocol + "//" + location.host + "/drillStructure?data=" + this.QuoteForm[name].Value);
        } else {
          window.open(location.protocol + "//" + location.host + "/drillStructure?data=" + this.QuoteForm[name].Value);
        }
      }
    },
    openDrillStructCallBack(data) {
      //钻孔结构数据
      if (data) {
        this.QuoteForm.DrillStructs.Value = data;
        // alert(this.QuoteForm.DrillStructs.value)
        this.$forceUpdate();
      }
    },
    openGoldfinger(name) {
      //计算金手指
      window.external.PopWindow(
        JSON.stringify({
          url: location.protocol + "//" + location.host + "/Goldfinger",
          width: 620,
          height: 550,
        })
      );
    },
    goldFingerCalculateCallBack(data) {
      // alert(data)
      data = JSON.parse(data);
      if (data) {
        this.QuoteForm.ValueExt1.Value = data.ValueExt1; // 金厚
        this.QuoteForm.ValueExt2.Value = Number(data.CSGoldenFingersCount) + Number(data.SSGoldenFingersCount); // 条数      CSGoldenFingersCount
        this.QuoteForm.ValueExt3.Value = data.ValueExt3; // 镍厚
        this.QuoteForm.SurfaceTreatExt2.Value = Number(data.CSGoldenFingersArea) + Number(data.SSGoldenFingersArea); // 面积
        this.$forceUpdate();
      }
      // if(data){
      // 	data = JSON.parse(data);
      // 	var surround = $("#PartSurfaceTreats_parameter_box>.surround[data-id='BS019008']");

      // 	for(var j in data){
      // 		if(j.substring(j.length - 4,j.length) != 'Unit'){
      // 			var elem = surround.find("[data-name='"+j+"']");

      // 			if(elem.attr("data-unit") && data[j+'Unit'] != undefined){
      // 				if(elem.attr("data-unit") != data[j+'Unit']){
      // 					elem[0].value = unitConversion[data[j+'Unit'] + 'Conversion' + elem.attr("data-unit")](data[j]);
      // 				}else{
      // 					elem.val(data[j])
      // 				}
      // 			}else{
      // 				elem.val(data[j])
      // 			}
      // 		}
      // 	}
      // }
    },
    // 搜索
    keydown(e) {
      if (e.ctrlKey && e.keyCode == 70) {
        //Ctrl + F
        $("#labelSearchInput").focus();
        // e.preventDefault ? e.preventDefault() : e.returnValue = false;
      }
    },
    search() {
      delay(() => {
        this.num = 0;
        this.indexNum = 0;
        this.query = this.searchText;
        var Reg = new RegExp(this.query, "i");
        if (this.query) {
          $(".capacity #formDataElem .ant-form-item-label label").each(function (index, label) {
            if (label.innerText.match(Reg)) {
              label.innerHTML = label.innerText.replace(Reg, function (text) {
                return `<em class="searchPosElem" data-index="${index + 1}" >` + text + `</em>`;
              });
            } else {
              label.innerHTML = label.innerText.replace(label.innerText, function (text) {
                return `<span >` + text + `</span>`;
              });
            }
          });
        } else {
          $(".capacity #formDataElem .ant-form-item-label label").each(function (index, label) {
            label.innerHTML = label.innerText.replace(label.innerText, function (text) {
              return `<span >` + text + `</span>`;
            });
          });
        }

        this.getSearchList();
      }, 500);
    },
    getSearchList() {
      const num = document.getElementsByTagName("em").length;
      this.num = num;
      if (num != 0) {
        document.getElementsByTagName("em")[0].innerHTML = '<strong style="background-color: #ff9632" >' + this.searchText + "</strong>";
        let active = 0;
        let active1 = document.getElementsByTagName("em")[this.indexNum].dataset["index"] - 1;
        let count = 0;
        for (let index = 0; index < this.QuoteDataList.length; index++) {
          count += this.QuoteDataList[index].dtos.length;
          if (active1 <= count) {
            active = index;
            break;
          }
        }
        if (!this.activeNames.includes(active)) {
          this.activeNames = [];
          this.activeNames.push(active);
        }
        // 滚动到第一个关键字位置
        document.getElementsByTagName("em")[0].scrollIntoView({
          block: "center",
          behavior: "smooth",
          inline: "start",
        });
      }
    },
    // 下一个
    next() {
      if (this.num !== 0) {
        for (let i = 0; i < document.getElementsByTagName("em").length; i++) {
          document.getElementsByTagName("em")[i].innerHTML = this.searchText;
        }
        if (this.indexNum === this.num - 1) {
          this.indexNum = 0;
        } else {
          this.indexNum = this.indexNum + 1;
        }
        let active = 0;
        let active1 = document.getElementsByTagName("em")[this.indexNum].dataset["index"] - 1;
        let count = 0;
        for (let index = 0; index < this.QuoteDataList.length; index++) {
          count += this.QuoteDataList[index].dtos.length;
          if (active1 <= count) {
            active = index;
            break;
          }
        }
        if (!this.activeNames.includes(active)) {
          this.activeNames = [];
          this.activeNames.push(active);
        }
        document.getElementsByTagName("em")[this.indexNum].innerHTML = '<strong style="background-color: #ff9632">' + this.searchText + "</strong>";
        document.getElementsByTagName("em")[this.indexNum].scrollIntoView({
          block: "center",
          behavior: "smooth",
          inline: "start",
        });
      }
    },
    // 上一个
    prev() {
      if (this.num !== 0) {
        for (let i = 0; i < document.getElementsByTagName("em").length; i++) {
          document.getElementsByTagName("em")[i].innerHTML = this.searchText;
        }
        if (this.indexNum === 0) {
          this.indexNum = this.num - 1;
        } else {
          this.indexNum = this.indexNum - 1;
        }
        document.getElementsByTagName("em")[this.indexNum].innerHTML = '<strong style="background-color: #ff9632">' + this.searchText + "</strong>";
        let active = 0;
        let active1 = document.getElementsByTagName("em")[this.indexNum].dataset["index"] - 1;
        let count = 0;
        for (let index = 0; index < this.QuoteDataList.length; index++) {
          count += this.QuoteDataList[index].dtos.length;
          if (active1 <= count) {
            active = index;
            break;
          }
        }
        if (!this.activeNames.includes(active)) {
          this.activeNames = [];
          this.activeNames.push(active);
        }
        document.getElementsByTagName("em")[this.indexNum].scrollIntoView({
          block: "center",
          behavior: "smooth",
          inline: "start",
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.content {
  /deep/.ant-form label {
    font-size: 12px !important;
  }
  /deep/.ant-input {
    font-size: 12px !important;
  }
  /deep/.ant-select {
    font-size: 12px !important;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 12px;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 10px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 2px;
}

/deep/.ant-select-selection--single {
  height: 25px;
}
/deep/.ant-select-selection__rendered {
  line-height: 25px;
}
/deep/.ant-input {
  height: 25px;
}
/deep/.ant-select-selection--multiple {
  min-height: 25px;
}
/deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
  height: 20px;
  line-height: 18px;
}
/deep/.ant-select-selection--multiple .ant-select-selection__choice {
  margin-right: 2px;
  padding: 0 10px 0 1px;
}
/deep/.ant-select-selection--multiple .ant-select-selection__choice__remove {
  right: 0;
}
/deep/.ant-select-allow-clear .ant-select-selection--multiple .ant-select-selection__rendered {
  margin-right: 2px;
}
// 多选样式溢出隐藏
// /deep/.sty {
//     .ant-select-selection__rendered{
//         height:25px;
//     }
//     ul{
//         display: flex;
//         overflow: hidden;
//     }
// }
// /deep/.test{
//     height:25px;
// }
/deep/.backsty {
  label {
    text-decoration: underline;
    color: blue;
  }
}
/deep/.backsty2 {
  label {
    text-decoration: underline;
    color: rgb(207, 11, 191);
  }
}
/deep/.red {
  label {
    color: red;
  }
}

/deep/.ant-form-item-children {
  min-height: 24px;
  display: block;
}
#header .labelSearch .pageChangeBtn {
  font-size: 14px;
  color: #bbb;
  font-weight: 500;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px 0 0;
  cursor: not-allowed;
}
#header .labelSearch .pageChangeBtn.action {
  color: #000;
  cursor: pointer;
}

/deep/.searchPosElem {
  background-color: #ff0;
  font-style: normal;
}
/deep/ .ant-form-item-label .action1 {
  color: #ff0;
  background-color: #f00;
}

@media screen and (min-width: 50px) {
  /deep/.ant-form-item-label label::after {
    display: contents !important;
    text-align: right !important;
  }
}

@media (max-width: 575px) {
  .ant-form-item {
    width: 100% !important;
    display: inline-block;
    margin-bottom: 5px;
  }
  /deep/.ant-form-item-label {
    text-align: left !important;
    line-height: 25px !important;
  }
}
.header {
  width: 100%;
  height: 40px;
  position: fixed;
  top: 0;
  left: 0;
  padding: 8px 2px;
  box-sizing: border-box;
  border-bottom: 1px solid #d5d5d5;
  box-shadow: 0 2px 5px rgb(0 0 0 / 20%);
  z-index: 99;
  background: #d5e4f2;
  display: flex;
  .ant-btn {
    height: 25px;
    font-size: 12px;
    background: #01a279;
    i {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }
  }
  /deep/.ant-input {
    height: 25px;
    width: 110px;
    padding-right: 6px;
    padding-left: 6px;
  }
  /deep/.ant-input-affix-wrapper {
    width: 110px;
  }
}
.searchRight {
  float: right;
}
#formDataElem .form-label .searchPosElem {
  background-color: #ff0;
  font-style: normal;
}
#formDataElem .form-label .searchPosElem.action {
  color: #ff0;
  background-color: #f00;
}
/deep/.ant-form-item-label {
  line-height: 25px;
}
/deep/.ant-form-item-control {
  line-height: 25px;
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  height: 35px;
  padding: 7px 34px;
  background: -webkit-linear-gradient(#f9f9f9, #eeeff1);
}
/deep/.ant-btn-primary:hover {
  background-color: #02906c !important;
  border-color: #02906c !important;
}
.content {
  background: #d5e4f2;
  position: fixed;
  top: 40px;
  bottom: 40px;
  left: 0;
  right: 0;
  overflow: auto;
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
}
/deep/.ant-collapse-content {
  background: #d5e4f2;
  overflow: unset;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 5px;
}
// /deep/.ant-checkbox-checked .ant-checkbox-inner {
//     background-color: #16438C;
//     border-color: #16438C;
// }
// /deep/.ant-input:hover {
//     border-color: #16438C;
// }
// /deep/.ant-select-selection:hover {
//     border-color: #16438C;
// }
// /deep/.ant-checkbox-input:hover {
//     border-color: #16438C;
// }
// /deep/.ant-checkbox-checked::after{
//     border-color: #16438C;
// }
/deep/.ant-btn-primary[disabled] {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
/deep/.ant-form-item-label {
  padding: 0 8px 0 0 !important;
  // line-height: 40px!important;
}
/deep/.ant-form-item-label > label::after {
  content: ":";
  position: absolute;
  top: -0.5px;
  margin: 0 8px 0 2px;
}
.buton {
  height: 40px;
  border-top: 1px solid #d5d5d5;
  position: fixed;
  width: 100%;
  text-align: center;
  // background: #C4CAD9;
  background: #d5e4f2;
  padding: 7px;
  bottom: 0;
  .ant-btn {
    height: 26px;
    font-size: 12px;
    background: #01a279;
  }
}
.labelSearch {
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  left: 100px;
  white-space: nowrap;
}
.labelSearch input[type="text"] {
  width: 80px !important;
  font-size: 13px;
  color: #000;
  vertical-align: middle;
  border: none;
  padding: 0 0 0 0;
  box-sizing: border-box;
  outline: none;
}
.labelSearch .layui-icon-search {
  font-size: 16px;
  color: #000;
  position: absolute;
  left: 6px;
  top: 4px;
  z-index: 999;
}
.labelSearch .page {
  font-size: 12px;
  color: #000;
  display: inline-block;
  vertical-align: middle;
  padding: 0 5px;
  margin-bottom: 5px;
}
.labelSearch .pageChangeBtn {
  font-size: 14px;
  color: #bbb;
  font-weight: 500;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px 5px 0;
  cursor: not-allowed;
}
.labelSearch .pageChangeBtn.action {
  color: #000;
  cursor: pointer;
}
.capacity .ant-form-item {
  width: 250px;
  display: inline-block;
  margin-bottom: 5px;
}
.table-filter-view {
  width: auto;
  min-width: 150px;
  max-width: 200px;
  background: #f3f3f4;
  border-top: 1px solid #d2d2d2;
  box-shadow: 1px 1px 10px #d2d2d2;
  position: absolute;
  top: 100%;
  left: 0px;
  z-index: 100000;
  ul {
    padding: 0;
    list-style: none;
    li {
      margin-left: 4px;
    }
  }

  /deep/.ant-input {
    height: 22px;
    width: 60px;
  }
}
.filter-input-view {
  min-width: 120px !important;
}
</style>
