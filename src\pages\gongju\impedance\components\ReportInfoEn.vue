<!-- 工具管理- 叠层阻抗-叠层报表 英文 -->
<template>
  <div class="pdfDom" >
    <div id="pdfDom">
      <div>
        <div class="headerTitle">        
          <div>
            <img :src="laminationData.stackUpInfo.enterpriseLogo"/>
            <div style="text-align: right; font-weight: 500; font-size: 16px;margin-top: 5px;color:#000000;">Product Number:{{this.laminationData.stackUpInfo.pdctno}}</div> 
            <div style="text-align: right; font-weight: 500; font-size: 16px;margin-top: 5px;position: absolute;top:158px;left:300px;color:#000000;">
              Customer Number:{{this.laminationData.stackUpInfo.customerNumber}}</div>
          </div>
          <div style="text-align: center;margin-left: 50px;">
            <h2>{{ laminationData.stackUpInfo.enterpriseName }}</h2>
            <h2 style="margin-top:-13px">{{laminationData.stackUpInfo.enterpriseEngName}}</h2>
            <!-- <span>叠层阻抗信息</span> -->
            <!-- <span>Engineering Stackup & Impedance</span> -->
          </div>
        </div>
        <h3 style="margin: 0;border: 2px solid black;border-bottom: 0;text-align: center;color: #ff9900;">Stackup Information</h3>
        <div class="reportTable">
          <div style="background: #1AA65F;width: 65px;align-items: center;display: flex; color: #FFFFFF; font-weight: 500; text-align: center">
            Finish<br/>Board<br/>Thickness<br/>{{laminationData.stackUpInfo.finishBoardThickness | floatFilter}}<br/>(mm)</div>
          <div style="width: 239px; padding: 45px 10px 2px 10px; border: 1px solid #000000;">
<!--            这是叠构图 -->
            <div v-for="(item,index) in tableData" :key="index">
              <div v-if="item.stackUpMTR_ == 'OZ'" class="OZclass" :class="[index == tableData.length-1 ? 'ozlastClass' :'']">
                <div v-if="item.stackUpLayerNo_" :class="['layerName','L'+item.stackUpLayerNo_]" >{{item.stackUpLayerNo_ | layerFilter(that)}}</div>
                <div class="ozContent">
                  <div class="oz_bg" :class="[index == tableData.length-1 ? 'oz_active' :'']"></div>
                </div>
              </div>
              <div v-if="item.stackUpMTR_ == 'Core'" class="coreClass">
                <div v-for=" (ite,idx) in item.child" :key="idx" :class="[ite.stackUpMTR_ == 'Core'?'coreActive' :'ozActive']">
                  <div v-if="ite.stackUpMTR_ == 'OZ'" class="OZclass">
                    <div v-if="ite.stackUpLayerNo_" :class="['layerName','L'+ite.stackUpLayerNo_]">L{{ite.stackUpLayerNo_}}</div>
                    <div class="ozContent2">
                    </div>
                  </div>
                  <div v-if="ite.stackUpMTR_ == 'Core'" class="core-box">
                    <div v-if="ite.stackUpLayerNo_" :class="['layerName','L'+ite.stackUpLayerNo_]">L{{ite.stackUpLayerNo_}}</div>
                    <div class="CoreContent">
                    </div>
                  </div>
                </div>

              </div>
              <div v-if="item.stackUpMTR_ == '金属基' || item.stackUpMTR_ == '补强'" class="jsjclass">
                <div  :class="['layerName','金属基']">{{item.stackUpMTR_}}</div>
                <div class="jsjContent"></div>
              </div>
              <div v-if="item.stackUpMTR_ == 'PP'"  :class="item.stackUpMTRFoil_.split('+').length <3 ?'PPclass':'PPclass1'">
                <div v-if="item.stackUpLayerNo_" :class="['layerName','L'+item.stackUpLayerNo_]">L{{item.stackUpLayerNo_}}</div>
                <div class="PPContent"></div>
              </div>
              <div v-if="item.stackUpMTR_ == 'GB'" class="GBClass">
                <div v-if="item.stackUpLayerNo_" :class="['layerName','L'+item.stackUpLayerNo_]">L{{item.stackUpLayerNo_}}</div>
                <div class="gbContent"></div>
              </div>

            </div>
<!--            这是箭头盒子 会有多个箭头吗 嗯嗯 那你的箭头也要循环啊 。。。 放里面不行呀，不一定有这个盒子 这里的显示是根据条件出来的 -->
            <div v-if="laminationData.stackUpDrills.length >0" class="drillClss">
              <div v-for="(list,index) in laminationData.stackUpDrills" :key="index" :ref="'dir_' + index" class="drillItem">
              </div>
            </div>
          </div>
          <div class="parameterClass">
            <table>
              <tr>
                <th>Type</th>
                <th>Material</th>
                <th>Parameter</th>
                <th>Residual Copper</th>
                <th>Finished Thickness(mm)</th>
              </tr>
              <tr v-for="(item,index) in tableData" :key="index" :class="[item.stackUpMTR_ =='OZ' ? 'paramOZ' : (item.stackUpMTR_ =='Core' ? 'paramCore':'paramPP')]">
                <td>{{item.stackUpMTR_ | typeFilter}}</td>
                <td>{{item | nameFilter}}</td>
                <!--:class="item.stackUpMTRFoil_.split('+').length <3 ?'tdclass':'tdclass1'" -->
                <td :class="[item.stackUpMTR_ =='PP' ? (item.stackUpMTRFoil_.split('+').length <3 ?'tdclass':'tdclass1') : '']">{{item | paramFilter}}</td>
                <td>{{item | ctlFilter}}</td>
                <td>{{item | cphdFilter}}</td>
              </tr>
            </table>
          </div>
          <div class="thickness">PressBoardThickness: {{laminationData.stackUpInfo.pressingThickness | floatFilter}}mm</div>
        </div>
      </div>

      <div style="border: 2px solid; border-top: 0;">
        <h3 style="margin: 0; border-bottom:2px solid black;text-align: center;color: #ff9900;">Impedance Information</h3>
        <div>
          <a-table
              :columns="columns"
              :data-source="impedanceTable"
              :rowKey="(record,index)=>{return index}"
              bordered
              :pagination="false"
          ></a-table>
        </div>
        <div v-for="(item,index) in impedanceData" :key="index" class="impedance">
          <h4>{{ impTypeFilter(item.imp_Type_)}}<br/> {{item.imp_PoLarName}}</h4>
          <div class="imp_left">
            <div class="line_flex"><p>Original LineWidth</p><p>{{item.imp_LineWidth_}}</p><p>Mil</p></div>
            <div class="line_flex"><p>Adjust LineWidth</p><p>{{item.imp_OKLineWidth_}}</p><p>Mil</p></div>
            <div class="line_flex"><p>Original LineSpacing</p><p>{{item.imp_LineSpace_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex"><p>Adjust LineSpacing</p><p>{{item.imp_OKLineSpace_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex"><p>Original Line To Copper</p><p>{{item.imp_LineCuSpace_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex"><p>Adjust Line To Copper</p><p>{{item.imp_OKLineCuSpace_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex"><p>Required Resistance</p><p>{{item.imp_Value_Req_}}</p><p>Ω</p></div>
          </div>
          <div class="imp_center">
            <img :src="'data:image/png;base64,'+item.impModelImage">
          </div>
          <div class="imp_right">
            <div class="line_flex"><p>H1</p><p>{{item.imp_H1_}}</p><p>Mil</p></div>
            <div class="line_flex"><p>Er1</p><p>{{item.imp_Er1_}}</p><p></p></div>
            <div class="line_flex"><p>W1</p><p>{{item.imp_W1_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex"><p>W2</p><p>{{item.imp_W2_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex" ><p>D1</p><p>{{item.imp_D1_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex" v-if="item.imp_S1_"><p>S1</p><p>{{item.imp_S1_ || '/'}}</p><p>Mil</p></div>
            <div class="line_flex"><p>T1</p><p>{{item.imp_T1_}}</p><p>Mil</p></div>
            <div class="line_flex"><p>C1</p><p>{{item.imp_C1_}}</p><p>Mil</p></div>
            <div class="line_flex"><p>C2</p><p>{{item.imp_C2_}}</p><p>Mil</p></div>
            <div class="line_flex"><p>C3</p><p>{{item.imp_C3_}}</p><p>Mil</p></div>
            <div class="line_flex"><p>CEr</p><p>{{item.imp_CEr_}}</p><p></p></div>
            <div class="line_flex"><p>Imp</p><p>{{item.imp_TrueValue_}}</p><p>Ω</p></div>
          </div>
          <h3><p style="margin: 0">Control Layer:{{item.imp_ControlLay_}};</p><p style="margin: 0">Upper reference:{{item.imp_UpLay_||'/'}};</p><p style="margin: 0">Lower reference:{{item.imp_DownLay_||'/'}};</p></h3>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
const columns = [
  {
    title: "NO",
    dataIndex: "index",
    width:40,
    align: 'center',
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: 'Imp Type',
    dataIndex: 'type',
    // width:100,
    align: 'center',
  },
  {
    title: 'Control Layer',
    dataIndex: 'controlLayer',
    width:50,
    align: 'center',
  },
  {
    title: 'ReferenceLayer',
    dataIndex: 'referenceLayer',
    width:65,
    align: 'center',
  },
  {
    title: 'Original Line Width',
    dataIndex: 'primarylLinewidth',
    width:55,
    align: 'center',
  },
  {
    title: 'Original Line Spacing',
    dataIndex: 'primaryLinedistance',
    width:55,
    align: 'center',
  },
  {
    title: 'Original Line ToCopper',
    dataIndex: 'primaryCopper',
    width:65,
    align: 'center',
  },
  {
    title: 'Adjust Line Width',
    dataIndex: 'adjustLineweight',
    width:55,
    align: 'center',
  },
  {
    title: 'Adjust Line Spacing',
    dataIndex: 'adjustLinedistance',
    width:55,
    align: 'center',
  },
  {
    title: 'Adjust Line ToCopper',
    dataIndex: 'adjustingWirecopper',
    width:65,
    align: 'center',
  },
  {
    title: 'Required Resistance',
    dataIndex: 'filmLinewidth',
    width:68,
    align: 'center',
  },
  {
    title: 'Actual Resistance& Tolerance',
    align: 'center',
    width:80,
    dataIndex: 'resistanceTolerance',
  },
];
import htmlToPdf from '@/utils/htmlToPdf';
import $ from 'jquery';
export default {
  name: "ReportInfo",
  props:{
    laminationData: {
      type: Object,
      required: true,
    }
  },
  filters:{
    layerFilter(val,that){
      return  'L'+val
      // if (val ==1 ){
      //   return 'GTL'
      // } else if (val == that.laminationData.stackUps[that.laminationData.stackUps.length-1].stackUpLayerNo_) {
      //   return 'GBL'
      // } else {
      //   return  'L'+val
      // }
    },
    floatFilter(val) {
      return val.toFixed(2)
    },
    typeFilter(val) {
      if (val =='OZ') {
        return 'OZ'
      } else if (val == 'PP') {
        return 'PP'
      } else if (val == 'Core') {
        return 'Core'
      }else if (val == 'GB') {
        return 'GB'
      }else{
        return val
      }
    },
    nameFilter(val) {
      if (val.child) {
        let str = ''
        val.child.forEach(item => {
          if(item.stackUpMTR_ == 'Core') {
            str = item.stackUpMTRType_
          }
        })
        return str
      }else {
        return val.stackUpMTRType_
      }
    },
    paramFilter(val){
      console.log('val',val,val.stackUpMTRFoil_)
      if (val.child) {
        let val_ = val.child
        let str_ = ``
        val_.forEach(item => {
          console.log('物料参数',item.stackUpMTR_)
          if (item.stackUpMTR_ =='OZ') {
            str_ += item.stackUpMTRFoil_ +'OZ'+ "\n"
          } else {
            // let corStr_ = item.tdFlag_ ? '含铜）' : '不含铜）'
            let corStr_ = item.stackUpCoreDS_ ? 'Copper)' : 'Copper free)'
            str_ += item.stackUpMTRFoil_ +'(' + corStr_ + "\n"
          }

        })
        return str_
      } else {
        if (val.stackUpMTR_ =='OZ') {
          return val.stackUpMTRFoil_ + '  OZ'
        } else {
          // 2022/08/05 屏蔽PP型号去重
          // let str = ''
          // let _data  = val.stackUpMTRFoil_.split('+').reduce(function (a, b) {
          //   if (b in a) {
          //     a[b]++
          //
          //   } else {
          //     a[b] = 1
          //   }
          //   return a
          // }, {})
          // var _dataKeys = Object.keys(_data)
          // str = _dataKeys.join('+')
          // return  str
          return val.stackUpMTRFoil_
        }
      }
    },
    ctlFilter(val) {
      if (val.child){
        let val_ = val.child
        let str_ = ``
        val_.forEach(item => {
          if (item.stackUpCTLMI_) {
            str_ += item.stackUpCTLMI_ + "\n"
          } else {
            str_ += '/' + "\n"
          }

        })
        return str_ || '/'
      }else {
        return val.stackUpCTLMI_ || '/'
      }
    },
    cphdFilter(val) {
      if (val.child) {
        let val_ = val.child
        let str_ = ``
        val_.forEach(item => {
          str_ += item.stackUpThichnessMM_ + "\n"
        })
        return str_
      } else {
        return val.stackUpThichnessMM_
      }
    },
  },

  computed:{
    tableData: function(){
      let _self = this;
      let newData = []
      let structure = _self.laminationData.stackUps.map(item => item.stackUpMTR_);
      let data = _self.laminationData.stackUps
      data.forEach((item,index)=> {
        let child = []
        if (item.stackUpMTR_ == 'OZ') {
          if (data[index+1]?.stackUpMTR_ == 'Core' && data[index+2]?.stackUpMTR_ == 'OZ') {
            child.push(item)
            child.push(data[index+1])
            child.push(data[index+2])
            newData.push({'stackUpMTR_':'Core', 'child': child})
          } else {
            if (data[index-1]?.stackUpMTR_ != 'Core' || index==0)
              newData.push(item)
          }
        } else if (item.stackUpMTR_ == 'PP') {
          newData.push(item)
        }else if(item.stackUpMTR_ == 'GB' || item.stackUpMTR_ == '金属基' || item.stackUpMTR_ == '补强'){
          newData.push(item)
        }
      })
      console.log('tableData',newData)
      return newData
    },
    impedanceData: function (){
      let _self = this;
      let im_data = _self.laminationData.stackIMPs
      // im_data.forEach(item => {
      //
      // })
      return im_data
    },
    impedanceTable(){
      const arr =[]
      this.laminationData.stackIMPs.forEach(item => {
        // debugger
        let _obj = {
          "type": this.impTypeFilter(item.imp_Type_),
          "controlLayer" : item.imp_ControlLay_,
          "referenceLayer": item.imp_UpLay_ ? (item.imp_DownLay_ ? (item.imp_UpLay_+'/'+ item.imp_DownLay_) : item.imp_UpLay_) : item.imp_DownLay_,
          "primarylLinewidth": item.imp_LineWidth_,
          "primaryLinedistance": item.imp_LineSpace_,
          "primaryCopper": item.imp_LineCuSpace_,
          "adjustLineweight": item.imp_OKLineWidth_,
          "adjustLinedistance": item.imp_OKLineSpace_,
          "adjustingWirecopper": item.imp_OKLineCuSpace_,
          "filmLinewidth" : item.imp_Value_Req_,
          "resistanceTolerance": item.imp_TrueValue_ + '±' + item.imp_Value_Tol_
        }
        arr.push(_obj)
      })
      return arr;
    },
  },
  data() {
    return {
      columns,
      data: [],
      that: this,
      height: window.document.documentElement.clientHeight - 158,
      style_:0
    }
  },
  methods: {
    getReportPdf(){
      htmlToPdf('pdfDom',this.laminationData.stackUpInfo.pdctno)
    },
    impTypeFilter(val) {
      var arr = [{"text":"外层单端(SM)","valueMember":"O_S(SM)","imp_CtlThicknessInH":0},{"text":"外层差分(SM)","valueMember":"O_D(SM)","imp_CtlThicknessInH":0},{"text":"内层单端","valueMember":"I_S_2","imp_CtlThicknessInH":2},{"text":"内层差分","valueMember":"I_D_2","imp_CtlThicknessInH":2},{"text":"外层单端共面地(SM)","valueMember":"O_S_C(SM)","imp_CtlThicknessInH":0},{"text":"外层差分共面地(SM)","valueMember":"O_D_C(SM)","imp_CtlThicknessInH":0},{"text":"内层单端(1层屏蔽)","valueMember":"I_S_1","imp_CtlThicknessInH":2},{"text":"内层差分(1层屏蔽)","valueMember":"I_D_1","imp_CtlThicknessInH":2},{"text":"内层单端共面地","valueMember":"I_S_C_2","imp_CtlThicknessInH":2},{"text":"内层单端共面地(1层屏蔽)","valueMember":"I_S_C_1","imp_CtlThicknessInH":2},{"text":"内层差分共面地","valueMember":"I_D_C_2","imp_CtlThicknessInH":2},{"text":"内层差分共面地(1层屏蔽)","valueMember":"I_D_C_1","imp_CtlThicknessInH":2},{"text":"外层单端","valueMember":"O_S","imp_CtlThicknessInH":0},{"text":"外层差分","valueMember":"O_D","imp_CtlThicknessInH":0},{"text":"外层单端共面地","valueMember":"O_S_C","imp_CtlThicknessInH":0},{"text":"外层差分共面地","valueMember":"O_D_C","imp_CtlThicknessInH":0},{"text":"外层单端共面地(无屏蔽)","valueMember":"O_S_C_0","imp_CtlThicknessInH":0},{"text":"外层单端共面地(无屏蔽)(SM)","valueMember":"O_S_C_0(SM)","imp_CtlThicknessInH":0},{"text":"外层差分共面地(无屏蔽)","valueMember":"O_D_C_0","imp_CtlThicknessInH":0},{"text":"外层差分共面地(无屏蔽)(SM)","valueMember":"O_D_C_0(SM)","imp_CtlThicknessInH":0},{"text":"内层单端共面地(无屏蔽)","valueMember":"I_S_C_0","imp_CtlThicknessInH":2},{"text":"内层差分共面地(无屏蔽)","valueMember":"I_D_C_0","imp_CtlThicknessInH":2}]
      let str = arr.find(item => item.valueMember == val).text
      return str
    },
    styleHeight_(val){
      console.log(this.$refs)
      let newArr = [],topArr=[]
      let arr=[]
      this.laminationData.stackUpDrills.forEach(item=>{
        item.count = item.endLayer- item.startLayer
        arr.push(item)
      })
      arr.sort((a,b)=>{ return b.count- a.count})
      this.laminationData.stackUpDrills = arr
      this.laminationData.stackUpDrills.forEach((ite,index) => {
        console.log('ite',ite)
        let height_ = 0;
        let top_ = 0
        let startIndex = this.laminationData.stackUps.findIndex(item=>{return item.stackUpLayerNo_ == ite.startLayer});
        console.log('startIndex',startIndex)
        let endIndex = this.laminationData.stackUps.findIndex(item=>{return item.stackUpLayerNo_ == ite.endLayer});
        console.log('startIndex',startIndex)
        newArr = this.laminationData.stackUps.slice(startIndex,endIndex+1);
        topArr = this.laminationData.stackUps.slice(0,startIndex+1);
        newArr.forEach(list=> {
          if (list.stackUpMTR_ == 'OZ') {
            height_+=30
          } else if (list.stackUpMTR_ == 'Core') {
            height_+=20
          } else {
            height_+=22
          }
        })
        topArr.forEach(res => {
          if (res.stackUpMTR_ == 'OZ') {
            top_ +=30
          } else if (res.stackUpMTR_ == 'Core') {
            top_ +=20
          } else {
            top_ +=22
          }
        })
        this.$refs['dir_'+index][0].style.height = height_ - 30+'px'
        this.$refs['dir_'+index][0].style.left = (index+1) * 18 + 80 +'px'
        this.$refs['dir_'+index][0].style.top = top_ -1 +'px'
      })
    }
  },
  mounted() {
    this.styleHeight_()
  }
}
</script>

<style lang="less" scoped>
.drillClss {
  left: 0;
  top: 0;
  padding: 23px 20px 20px;
  width: 100%;
  position: absolute;
  height: 100%;
}
.drillItem {
  width: 9px;
  position: absolute;
  background: #0000CC;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItem:after{
  content: '';
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #0000CC;
  position: absolute;
  bottom: -8px;
  left:-3px;
}
.pdfDom {
  overflow: auto;
  margin: -25px;
}
#pdfDom {

  padding: 25px;
}
.headerTitle {
  display: flex;
  margin-bottom: 20px;
  img {
    margin-left: 50px;
    height: 70px;
  }
  h2 {
    font-size: 22px;
    color: #000000;
    // font-weight: 600;
  }
  p{
    font-size: 12px;
    color: #000000;
    // font-weight: 600;
  }
  span {
    font-size: 16px;
    color: #990099;
  }
}
.reportTable{
  border: 1px solid;
  display: flex;
  flex-wrap: wrap;
  .thickness {
    width: 100%;
    text-align: right;
    padding-right: 15px;
    line-height: 30px;
    font-weight: 500;
    font-size: 16px;
    border: 1px solid;
    color:#000000;
  }
  .layerName {
    width: 70px;
    height: 100%;
    // border: 2px solid;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
    color:#0000CC;
  }
  .ozlastClass {
    align-items: center;
  }
  .OZclass {
    height: 30px;
    display: flex;
    overflow: hidden;
    align-items: center;
    .ozContent{
      //border-bottom: 1px solid;
      width: 100%;
      margin-left: 20px;
      display: flex;
      align-items: end;
      .oz_bg {
        width: 100%;
        // border-radius: 7px;
        height: 10px;
        overflow: hidden;
        // background: url("../../../assets/img/pp.png") repeat-x;
        // background-position-x: -12px;
        background: #F4A460;
      }
      .oz_active {
        // background: url("../../../assets/img/pp2.png") repeat-x;
        // background-position-x: -12px;
        background: #F4A460;
      }
    }
  }
  .PPclass {
    overflow: hidden;
    .PPContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 12px;
      margin: 5px 0;
      background: #228B22;
      // background: #9ACD32;
      // border-radius: 5px;

    }
  }
  .PPclass1 {
    overflow: hidden;
    .PPContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 12px;
      margin: 14px 0;
      background: #228B22;
      // background: #9ACD32;
      // border-radius: 5px;

    }
  }
  .jsjclass {
    display: flex;
    overflow: hidden;
    .jsjContent {
      float: right;
      margin-left: 15px;
      margin-top: 10px;
      margin-bottom: 10px;
      width: 100%;
      height: 12px;
      //margin: 10px 0;
      background: #FCB505;
      border-radius: 5px;

    }
  }
  .coreClass {
    .ozActive:nth-of-type(1) {
      margin-bottom: 20px;
    }
    position: relative;
    .coreActive {
      position: absolute;
      width: 100%;
      top: 16px;
    }
    .ozContent2 {
      border-bottom: 2px solid black;
      width: 100%;
    }
    .core-box {
      height: 48px;
      width: 100%;
      overflow: hidden;
      .CoreContent {
        height: 100%;
        float: right;
        background: #FCB505;
        width: calc(100% - 68px);
        background: #FCB408;
      }
    }

  }
  .GBClass{
    overflow: hidden;
    .gbContent{
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 22px;
      margin: 3px 0;
      background: #FCB505;
      border-radius: 5px;
    }
  }
  .parameterClass {
    flex: 1;
    table tr th {
      text-align: center;
      width:79px;
      color:#000000;
    }
    table tr th:nth-child(2) {
      width: 99px;
    }
    table tr th:nth-child(3) {
      width: 212px;
    }
    table tr th:nth-child(4) {
      width: 60px;
    }
    table tr th:nth-child(5) {
      width: 100px;
    }
    table {
      border-left: 1px solid black;
    }
    table tr:nth-child(1) {
      border: 1px solid black;
      border-left: none;

    }
    table tr th {
      border-right: 1px solid black;
    }
    table tr td {
      text-align: center;
      color: #0000CC;
      border-right:1px solid black;
      border-bottom:1px solid black ;
    }
    .paramOZ{
      height: 27px;
      line-height: 27px;
    }
    .paramCore {
      height: 80px;
      td {
        white-space: pre;
      }

      //line-height: 30px;
    }
    .paramPP {
      height: 19px;
      line-height: 19px;    
      // td:nth-child(3) {
      //   width: 200px;      
      //   display: inline-block;
      // }
    }
  }
}
 .tdclass1{
    width: 200px;      
    display: block;
  }
.impedance {
  display: flex;
  flex-wrap: wrap;
  padding-left: 5px;
  h4 {
    width: 100%;
    color:#000000;
  }
  h3 {
    display: flex;
    width: 250px;
    justify-content: space-between;
    p{
      margin: 0;
      margin-right: 10px;
    }
    p:nth-child(1) {
      margin: 0;
    }
  }
  // .imp_left{
  // }
  .line_flex {
    display: flex;
    p{
      margin: 0;
      margin-right: 15px;
      font-size: 14px;
      line-height: 25px;
      font-weight: 500;
    }
    p:nth-child(2) {
      width: 60px;
    }
  }
  .imp_center{
    margin: 10px 40px;
    img {
      padding: 15px;
      background: #008181;
    }
  }
  .imp_right{
    .line_flex {
      p:nth-child(1) {
        width: 40px;
      }
    }
  }
}
/deep/ .ant-table-body {
  .ant-table-thead {
    tr>th {
      padding: 5px 0;
      border-color: black;
      &:last-child {
        border-right: 0;
      }
    }
  }
  .ant-table-tbody {
    tr>td {
      padding: 7px 0;
      border-color: black;
      &:last-child {
        border-right: 0;
      }
    }

  }
}
</style>
