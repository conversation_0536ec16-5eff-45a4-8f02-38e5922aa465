module.exports = {
  messages: {
    CN: {
      pageDesc: '将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。',
      input: '请填写转账信息',
      confirm: '确认转账信息',
      complete: '完成',
      payment: '付款账户',
      collection: '收款账户',
      alipay: '支付宝',
      wechat: '微信',
      collectionName: '收款人姓名',
      transferAmount: '转账金额',
      nextStep: '下一步',
      preStep: '上一步',
      submit: '提交',
      note: '确认转账后，资金将直接打入对方账户，无法退回。',
      success: '支付完成',
      doAgain: '再转一笔',
      bill: '查看账单'
    },
    HK: {
      pageDesc: '將一個冗長或用戶不熟悉的表單任務分成多個步驟，指導用戶完成。',
      input: '請填寫轉賬信息',
      confirm: '確認轉賬信息',
      complete: '完成',
      payment: '付款賬戶',
      collection: '收款賬戶',
      alipay: '支付寶',
      wechat: '微信',
      collectionName: '收款人姓名',
      transferAmount: '裝張金額',
      nextStep: '下一步',
      preStep: '上一步',
      submit: '提交',
      note: '確認轉賬後，現金將直接打入對方賬戶，無法退回。',
      success: '支付完成',
      doAgain: '再轉一筆',
      bill: '查看賬單'
    },
    US: {
      pageDesc: 'Divide a long or unfamiliar form task into several steps to guide the user to complete it.',
      input: 'Fill transfer information',
      confirm: 'Confirm transfer information',
      complete: 'Complete',
      payment: 'Payment account',
      collection: 'Collection account',
      alipay: 'Alipay',
      wechat: 'Wechat',
      collectionName: 'Name of payee',
      transferAmount: 'Transfer amount',
      nextStep: 'Next',
      preStep: 'Back',
      submit: 'Submit',
      note: 'After confirming the transfer, the funds will be directly transferred into the other party\'s account and cannot be returned.',
      success: 'Payment successful',
      doAgain: 'Do Again',
      bill: 'Check the bill'
    }
  }
}