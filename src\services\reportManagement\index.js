import { request, METHOD } from '@/utils/request';
// 获取报表列表
export function getReportList () {
    return request("/api/app/e-mSTPub-report-list/report-list", METHOD.GET)
}
export function getParList (id) {
    return request(`/api/app/e-mSTPub-report-list/par-list/${id}`, METHOD.GET)
}
// 获取报表数据
export function getExcelData (id,params) {
    return request(`/api/app/e-mSTPub-report-list/select-report/${id}`, METHOD.GET,params)
}
//报表数据更改为获取url
export function selectreportv2 (id,params) {
    return request(`/api/app/e-mSTPub-report-list/select-report-v2/${id}`, METHOD.POST,params)
}
// 报价数据更改为获取文档流
export function selectreportv3 (id,params) {
    return request(`/api/app/e-mSTPub-report-list/select-report-v3/${id}`, METHOD.POST,params)
}
//报表最新接口
export function selectreportv4 (id,params) {
    return request(`/api/app/e-mSTPub-report-list/select-report-v4/${id}`, METHOD.POST,params)
}
// 新增报表获取变量
export function addReportGetVariable (params) {
    return request('/api/app/e-mSTPub-report-list/reportpar-list', METHOD.GET,params)
}
// 新增报表获取变量
export function addReport (params) {
    return request('/api/app/e-mSTPub-report-list/report', METHOD.POST,params)
}
// 修改报表获取参数
export function editReportParams (id) {
    return request(`/api/app/e-mSTPub-report-list/select-byrepair-report/${id}`, METHOD.POST)
}
// 修改报表
export function editReport(params) {
    return request('/api/app/e-mSTPub-report-list/repiar-report', METHOD.POST, params)
}

export default {
    getReportList,
    getParList,
    getExcelData,
    addReportGetVariable,
    addReport,
    editReportParams,
    editReport,
    selectreportv2
}