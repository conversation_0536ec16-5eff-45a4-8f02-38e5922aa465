<!--生产管理- 生产进度-部门过序 -->
<template>
    <div style=" min-width: 1670px;">
       <div class="topStyle"> 
        <h1> 开始扫描执行工序 
            <span style="color:#cc0000;font-size:18px;font-weight:700;margin-left:10px;">(操作人：<span>{{user.name}}</span>）</span>
            <a style="color:#409eff;font-size: 14px;;" @click="viewClick">查看本车间正在生产中流程卡</a>
        </h1>
       </div>
      
        <a-form ref="formRef" :model="form" style="width:50%;margin-top:1%;position: relative;" >
            <div class="warStyle" v-if="warDom">
                <span style="color:#FF9901;font-size:16px;font-weight:700;margin-left:10px;">提醒:</span>
                <span style="color:#CC0000;font-weight:700;font-size:20px;margin-left:10px;">{{message}}</span>
            </div>
            <a-form-item label="流程卡号" :label-col="{ span:4}" :wrapper-col="{ span: 10}"  >           
            <a-input     v-model="form.CardNo"   placeholder="填写流程卡号" :autoFocus="autoFocus" allowClear 
             @keyup.enter="keyupEnter" @focus="animateWidth('CardNo','focus')" @change='CardNoChange'/>             
            <span v-if="CardNoDom" style="height:30px; line-height: 45px; font-size: 24px;color:red;display: block;"> 请填写流程卡号</span>      
            </a-form-item>       
            <a-form-item label="数量"  :label-col="{ span:4}" :wrapper-col="{ span: 10}"   >
            <a-input     v-model="form.num"  placeholder="填写数量" @focus="animateWidth('num','focus')"  @change='numChange' disabled />  
            <span v-if="numDom" style="height:30px; line-height: 45px; font-size: 24px;color:red;display: block;"> 请填写数量</span>        
            </a-form-item>           
            <a-form-item label="备注" :label-col="{ span:4}" :wrapper-col="{ span: 10}" >
            <a-textarea v-model="form.remarks"  allow-clear placeholder="填写备注" class="textarea" @focus="animateWidth('remarks','focus')" />
            </a-form-item>
        </a-form>
        <a-button type="primary" @click="startClick"  style="margin:10px 10px 10px 10%;width:6%;">过序</a-button>
        <a-button type="primary" @click="clearClick"  style="margin:10px 10px 10px 4%;width:6%;">清除</a-button>
    </div>
</template>
<script>
import {mapState,} from 'vuex';
import {
    getPassFlowNum,
    setStepFlow,
} from "@/services/scgl/OrderManagement/progress"
export default{
    name:'Supersequence',
    data(){
        return{
            form:{
                CardNo:'',
                num:'',
                remarks: "",
            },
            autoFocus:true,
            CardNoDom:false,
            numDom:false,
            warDom:false,
            message:'',
        }
    },
    computed: {
    ...mapState('account', ['user',]),  
  },  
  watch:{
    form(val){
        console.log(val,'val')
    }
  },
    methods:{
        keyupEnter(){
            var arr1 = this.form.CardNo.split('')
            if(arr1.length >30){
                arr1 = arr1.slice(0,30)
            }
            this.form.CardNo = arr1.join('')
            getPassFlowNum(this.form.CardNo).then(res=>{
                if(res.code){
                    this.form.num = res.data 
                    this.warDom = false              
                }else{                  
                    this.warDom = true
                    this.message = res.message                    
                    // this.$message.error(res.message)
                }
            })
        },
        startClick(){            
            let params = this.form           
            if(!params.CardNo || !params.num){
                if(!params.CardNo){
                    this.CardNoDom = true
                }
                if(!params.num){
                this.numDom = true                
                }
                return
            }
            
            params.account = this.user.userName,
            params.name =  this.user.name
            params.num =  Number(params.num)
            setStepFlow(params).then(res=>{
                if(res.code){
                    this.warDom = false
                    this.$message.success('过序成功')
                }else{
                    this.warDom = true
                    this.message = res.message  
                    //this.$message.error(res.message)
                }
            })
        },
        clearClick(){
        this.form.CardNo = ''
        this.form.num = ''
        this.form.remarks = ''  
        },
        viewClick(){
            this.$router.push({path:'ProgressManagement',})     
        },
        // 失去焦点，name为要验证的字段名,type为focus
        animateWidth(name, type) {
        if (name == "CardNo") {            
            this.form.CardNo = ''
            this.form.num = ''
            this.form.remarks = '' 
        } 
        if (name == "num") {
            this.form.num = ''
        } 
        if (name == "remarks") { 
            this.form.remarks = '' 
        } 
        
        },
        CardNoChange(){
            if(this.form.CardNo){
                this.CardNoDom = false
            }
        },
        numChange(){
            if(this.form.num){
                this.numDom = false
            }
        },
    },    
    directives: {
    'focusNextOnEnter':{
        bind: function(el, {
        value
        }, vnode) {
        el.addEventListener('keyup', (ev) => {
            if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value]
            if (nextInput && typeof nextInput.focus === 'function') {
                nextInput.focus()
                nextInput.select()
            }
            }
        })
        }
    }
    },
}
</script>
<style lang="less" scoped>
.topStyle{
    height: 50px;
    line-height: 50px;
    width: 98%;
    margin: 0 auto;
    border-bottom: 1px solid #d1cad1;
}
.warStyle{
    position: absolute;
    right: -3%;
    height: 38px;
    line-height: 38px;
    width: 40%;
    border: 1px solid #cbc7c7;
    border-radius: 4px;
}
/deep/.ant-form label {
    font-size: 26px!important;
}
/deep/.ant-input{
    font-size: 24px!important;
    height:40px;
    line-height:40px;
    // margin-bottom: 8%;
}
/deep/form textarea.ant-input{
    height:auto;
}
/deep/.textarea textarea{
    font-size: 24px!important;
}
</style>