import enquireJs from 'enquire.js'

export function isDef (v){
  return v !== undefined && v !== null
}

/**
 * Remove an item from an array.
 */
export function remove (arr, item) {
  if (arr.length) {
    const index = arr.indexOf(item)
    if (index > -1) {
      return arr.splice(index, 1)
    }
  }
}

export function isRegExp (v) {
  return _toString.call(v) === '[object RegExp]'
}

export function download(fileName, content, option){
  var a = document.createElement('a');
  var url = window.URL.createObjectURL(new Blob([content],
      { type: (option.type || "text/plain") + ";charset=" + (option.encoding || 'utf-8') }));
  a.href = url;
  a.download = fileName || 'file';
  a.click();
  window.URL.revokeObjectURL(url);
}

export function enquireScreen(call) {
  const handler = {
    match: function () {
      call && call(true)
    },
    unmatch: function () {
      call && call(false)
    }
  }
  // enquireJs.register('only screen and (max-width: 767.99px)', handler)  2023/3/20 顶部导航栏固定显示
  enquireJs.register('only screen and (max-width: 767.99px)', handler) 
}

const _toString = Object.prototype.toString
