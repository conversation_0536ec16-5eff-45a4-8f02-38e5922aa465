<!-- HM生产通知单 -->
<template>
    <div class="pdfDom1" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
      <div id="hmnoticedom" style="font-size: 12px;font-family:Arial, Helvetica, sans-serif;padding: 25px;color: black;" >   
        <table  style="font-family: 'Courier New', Courier, monospace;width: 784px;" class="onetable">
            <tbody>
                <tr>
                    <td colspan="6" style="font-size: 30px;text-align: center;">生产通知单</td>
                </tr>
                <tr>
                    <td colspan="4" style="text-align: left;">客户代码:{{HMnoticedata.custno}}</td>
                    <td colspan="2" style="text-align: left;">客户型号:{{HMnoticedata.custsty}}</td>
                </tr>
                <tr>
                    <td colspan="4" style="text-align: left;">生产编号:{{HMnoticedata.pdctno}}</td>
                    <td colspan="2" style="text-align: left;">合同号:{{HMnoticedata.po}}</td>
                </tr>
                <tr>
                    <td rowspan="2">新/返单</td>
                    <td>新/返</td>
                    <td>批/样</td>
                    <td>正常/加急</td>
                    <td  rowspan="2">下单日期：</td>
                    <td  rowspan="2">{{HMnoticedata.yqrkdate}}</td>
                </tr>
                <tr>
                    <td>{{HMnoticedata.isnewpdctno}}</td>
                    <td>{{HMnoticedata.orderarea}}</td>
                    <td>{{HMnoticedata.jgsj}}</td>
                </tr>
                <tr>
                    <td>数量</td>
                    <td colspan="3">{{ HMnoticedata.quantity }}</td>
                    <td>交货日期</td>
                    <td>{{ HMnoticedata.indate }}</td>
                </tr>
                <tr>
                    <td>层 数</td>
                    <td colspan="3">{{ HMnoticedata.pbqty }}</td>
                    <td>订单面积</td>
                    <td>{{ HMnoticedata.samepdctno }}</td>
                </tr>
                <tr>
                    <td>板 厚：</td>
                    <td colspan="3">{{ HMnoticedata.cpthick }}</td>
                    <td>PCS尺寸</td>
                    <td>{{ HMnoticedata.cpsize }}</td>
                </tr>
                <tr>
                    <td>板 材：</td>
                    <td colspan="3">{{ HMnoticedata.bctp }}</td>
                    <td>拼版尺寸：</td>
                    <td>{{ HMnoticedata.delunit }}</td>
                </tr>
                <tr>
                    <td>板材品名：</td>
                    <td colspan="3">{{ HMnoticedata.plate }}</td>
                    <td>出货尺寸：</td>
                    <td>{{ HMnoticedata.jhfs }}</td>
                </tr>
                <tr>
                    <td>铜厚</td>
                    <td colspan="3">{{ HMnoticedata.tbthick }}</td>
                    <td>成型方式</td>
                    <td>{{ HMnoticedata.cxkind }}</td>
                </tr>
                <tr>
                    <td>阻焊颜色：</td>
                    <td colspan="3">{{ HMnoticedata.zkcolor }}</td>
                    <td>模 具：</td>
                    <td>{{ HMnoticedata.zkfx }}</td>
                </tr>
                <tr>
                    <td>油墨型号：</td>
                    <td colspan="3">{{ HMnoticedata.freeboard }}</td>
                    <td>测试方式：</td>
                    <td>{{ HMnoticedata.etest }}</td>
                </tr>
                <tr>
                    <td>字符颜色：</td>
                    <td colspan="3">{{ HMnoticedata.zfcolor }}</td>
                    <td>测试架：</td>
                    <td>{{ HMnoticedata.cno }}</td>
                </tr>
                <tr>
                    <td>工 艺：</td>
                    <td colspan="3">{{ HMnoticedata.nospec }}</td>
                    <td>过孔要求：</td>
                    <td>{{HMnoticedata.gGcl}}</td>
                </tr>
                <tr>
                    <td>特殊要求：</td>
                    <td colspan="5" style="text-align: left;max-width: 680px;">{{ HMnoticedata.xhremark }}</td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td colspan="5" style="text-align: left;max-width: 680px;">{{ HMnoticedata.remark }}</td>
                </tr>
            </tbody>
        </table>
        <table style="font-family: 'Courier New', Courier, monospace;width: 784px;border: 1px solid black;border-top: none;" >
                <tr>
                    <td>市场：</td>
                    <td></td>
                    <td>工程：</td>
                    <td></td>
                    <td>采购:</td>
                    <td></td>
                    <td>品质:</td>
                    <td></td>
                    <td>计划：</td>
                    <td></td>
                </tr>
        </table>
        <table style="font-family: 'Courier New', Courier, monospace;width: 784px;border: 1px solid black;border-top: none;"  >
                <tr>
                    <td >制单人:{{ HMnoticedata.keyin }}</td>
                    <td style="text-align: end;">打印时间:{{ HMnoticedata.mdmktstatetime }}</td>
                </tr>
        </table>
      </div>
    </div>
  </template>
  <script>
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  export default {
    name: "",
    props:['HMnoticedata','ttype'],    
    computed:{  },
    data() {
      return { 
        printObj1:{
            id: "hmnoticedom", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.HMnoticedata.custsty
        },
      getnoticePdf(){
        htmlToPdf('hmnoticedom',this.HMnoticedata.custsty)  
      },
    },
  }
  </script>
  
  <style lang="less" scoped>
    .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
  td{
    font-size: 16px;
    color: black;
    padding: 10px 8px;
  }
  .onetable{
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    td{
         text-align: center;
         border-left: 1px solid black;
         border-top: 1px solid black;
    }
  }
  .m-t-10{
    /deep/.ant-col > label{
      color: red;
      font-weight: bold;
      font-size: 24px;
    }
    /deep/.ant-form-item-children{
      color: red;
      font-weight: bold;
      font-size: 24px;
    }
  }
  /deep/.ant-col-3{
    width: 11.1%;
  }
  /deep/.ant-col-21{
    width: 88.9%;
  }
  /deep/.ant-col-15{
    width: 65.2%;
  }
  /deep/.ant-col-9{
    width: 34.8%;
  }
  /deep/.ant-form-item{
    margin-bottom: 0;
  }
  /deep/.ant-form-item-label{
    border-bottom: 1px solid black;
    border-left: 1px solid black;
    padding-left: 7px;
    height: 32px;
    line-height: 32px;
    font-weight: bold;
  }
  /deep/.ant-form-item-control{
    border-bottom: 1px solid black;
    border-left: 1px solid black;
    padding-left: 7px;
    height: 32px;
    line-height: 32px;
    font-weight: bold;
    color: black;
  }
  /deep/.ant-form{
    border-top: 1px solid black;
    border-right: 1px solid black;
  }
  .pdfDom1{
      height: 650px;
      overflow: auto;
  }
  </style>
