<!-- 工程指示——产品特性 -->
<template>
  <div class="box">
    <a-form-model>
      <a-row>
        <a-col :span="16" style="border-left: 1px solid #ddd">
          <a-form-model-item :wrapper-col="{ span: 24 }" style="width: 490px">
            <span style="font-size: 14px"> 自定义关键工艺特性</span>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item :wrapper-col="{ span: 24 }" style="width: 246px">
            <span style="font-size: 14px"> 自定义其他特性</span>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="1" style="border-left: 1px solid #ddd">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 序号 </a-form-model-item>
        </a-col>
        <a-col :span="5" style="width: 161px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 特性名称 </a-form-model-item>
        </a-col>
        <a-col :span="2" style="width: 58px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 特性值 </a-form-model-item>
        </a-col>
        <a-col :span="1" style="width: 32px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 序号 </a-form-model-item>
        </a-col>
        <a-col :span="5" style="width: 160px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 特性名称 </a-form-model-item>
        </a-col>
        <a-col :span="2" style="width: 58px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 特性值 </a-form-model-item>
        </a-col>
        <a-col :span="1" style="width: 32px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 序号 </a-form-model-item>
        </a-col>
        <a-col :span="5" style="width: 161px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 特性名称 </a-form-model-item>
        </a-col>
        <a-col :span="2" style="width: 58px">
          <a-form-model-item :wrapper-col="{ span: 24 }"> 特性值 </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="1" class="one">
          <a-form-model-item :wrapper-col="{ span: 24 }" v-for="(item, index) in (0, 15)" :key="index">
            <div>
              {{ item }}
            </div>
          </a-form-model-item>
        </a-col>
        <a-col :span="7">
          <a-form-model-item label="阻抗板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_1"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="盲埋孔板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_2"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="金手指板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_3"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="半孔板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_4"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="汽车板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_5"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="铝基板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_6"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="电感板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_7"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="合拼板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_8"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="厚铜板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_9"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="邦定IC板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_10"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="5G板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_11"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="高频板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_12"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="假双面板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_13"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="无卤素板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_14"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="医疗板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_15"></a-checkbox>
          </a-form-model-item>
        </a-col>
        <a-col :span="1">
          <a-form-model-item :wrapper-col="{ span: 24 }" v-for="(item, index) in listq" :key="index">
            <div>
              {{ item }}
            </div>
          </a-form-model-item>
        </a-col>
        <a-col :span="7">
          <a-form-model-item label="负片板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_16"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="IPC三级标准" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_17"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="无钻化湿度卡" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_18"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="离子污染控制板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_19"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="一个星号" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_20"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="二个星号" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_21"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="三个星号" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_22"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="四个星号" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_23"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="五个星号" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_24"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="铝箔袋包装" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_25"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="QC重点管控板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_26"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="海运" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_27"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="正片板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_28"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="新客户,注意品质" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_29"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="军工板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.spec_flag_30"></a-checkbox>
          </a-form-model-item>
        </a-col>
        <a-col :span="1">
          <a-form-model-item :wrapper-col="{ span: 24 }" v-for="(item, index) in (0, 9)" :key="index">
            <div>
              {{ item }}
            </div>
          </a-form-model-item>
        </a-col>
        <a-col :span="7">
          <a-form-model-item label="控制计划" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_1"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="消费类" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_2"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="工业类" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_3"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="通信类" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_4"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="计算机" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_5"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="有插入板" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_6"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="工艺非常规" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_7"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="品质验证非常规" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_8"></a-checkbox>
          </a-form-model-item>
          <a-form-model-item label="操作非常规" :label-col="{ span: 18 }" :wrapper-col="{ span: 6 }">
            <a-checkbox v-model="formData.rspeC_FLAG_9"></a-checkbox>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
export default {
  props: ["characteristicdata"],
  data() {
    return {
      listq: [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
      formData: {},
    };
  },
  created() {
    this.formData = this.characteristicdata;
  },
};
</script>
<style scoped lang="less">
/deep/.ant-col-24 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 102%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form {
  border-top: 1px solid #ddd;
}
.one {
  border-left: 1px solid #ddd;
}
/deep/ .ant-form-item-label {
  display: flex;
  padding-left: 10px;
  align-items: center;
  justify-content: start;
  font: 12px/1.17 "微软雅黑", arial, \5b8b\4f53;
  color: #666;
  //  background-color: #f1f1f1;
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  height: 23px;
  label {
    font: 12px/1.17 "微软雅黑", arial, \5b8b\4f53;
  }
}
/deep/ .ant-form-item-control {
  display: flex;
  align-items: center;
  justify-content: center;
  font: 12px/1.17 "微软雅黑", arial, \5b8b\4f53;
  border-right: 1px solid #ddd;
  height: 23px;
  border-bottom: 1px solid #ddd;
  label {
    font: 12px/1.17 "微软雅黑", arial, \5b8b\4f53;
  }
}
</style>
