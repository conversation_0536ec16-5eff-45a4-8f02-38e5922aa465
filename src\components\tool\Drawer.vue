<template>
  <div class="dragContainerEl">
    <div :class="['mask', visible ? 'open' : 'close']" @click="close"></div>
    <div :class="['drawer', placement, visible ? 'open' : 'close']">
      <div ref="drawer" class="content beauty-scroll">
        <slot></slot>
      </div>
     
        <div v-if="showHandler" :class="['handler-container', placement, visible ? 'open' : 'close']" ref="handler" id="Drag" @click.stop="toggle"  @mousedown.prevent="mousedown($event)">         
          <v-touch @swipeup="nowTop($event)" @swipedown="nowBto($event)" class="v-touch">
            <slot v-if="$slots.handler" name="handler"></slot>
            <div v-else class="handler">
              <a-icon :type="visible ? 'close'  : 'bars'" />
            </div>            
          </v-touch>
        </div>
     
    </div>
  </div>
</template>

<script>
export default {
  name: 'Drawer',
  data () {
    return {
      DragEl: null,//拖动元素
      dragContainerEl: null,//容器元素
      disY: 0,     
      styleObj: {
        top: 0
      },
      firstTime:'',
      lastTime:'',
    }
  },
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      required: false,
      default: false
    },
    placement: {
      type: String,
      required: false,
      default: 'left'
    },
    showHandler: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  mounted() {
    this.DragEl = document.getElementById("Drag");
    this.dragContainerEl = document.getElementsByClassName("dragContainerEl")[0];  
  },
  methods: {
    open () {
      this.$emit('change', true)
    },
    close () {
      this.$emit('change', false)
    },
    toggle () {
      if((this.lastTime - this.firstTime) < 200){
        this.$emit('change', !this.visible)
      }
    },
    mousedown(event) {
      this.firstTime =  new Date().getTime()
      this.disY = event.clientY - this.DragEl.offsetTop;
      let dragHeight = this.DragEl.offsetHeight;
      let dragContainerHeight = this.dragContainerEl.offsetHeight;

      // 添加鼠标移动事件
      document.onmousemove = (el) => {       
        let moveY = el.clientY - this.disY; 
        if (moveY <= 0) {
          moveY = 0;
        }       
        if (moveY >= dragContainerHeight - dragHeight) {
          moveY = dragContainerHeight - dragHeight;
        }
        this.DragEl.style.top = moveY + "px";
        this.DragEl.style.transition = "all ease 0.1s";
      };
      /* 6，鼠标抬起解除事件 */
      document.onmouseup = () => {
        document.onmousemove = null;
        this.lastTime = new Date().getTime();
      };
    },
    nowTop($event) {
      console.log('上',)
      // 获取list
      let now = document.getElementById("Drag");
      now.style.top +=  10 + "px";
      now.style.transition = "all ease 0.1s";
    },
    nowBto($event) {
      console.log('下',)
      let now = document.getElementById("Drag");
      now.style.top -=  10 + "px";
      now.style.transition = "all ease 0.1s";
      },
  }
}
</script>

<style lang="less" scoped>
.v-touch{
  touch-action: pan-y!important;
}
  .mask{
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background-color: @shadow-color;
    transition: all 0.5s;
    z-index: 100;
    &.open{
      display: inline-block;
    }
    &.close{
      display: none;
    }
  }
  .drawer{
    position: fixed;
    transition: all 0.5s;
    height: 100vh;
    z-index: 100;
    &.left{
      left: 0px;
      &.open{
        .content{
          box-shadow: 2px 0 8px @shadow-color;
        }
      }
      &.close{
        transform: translateX(-100%);
      }
    }
    &.right{
      right: 0px;
      .content{
        float: right;
      }
      &.open{
        .content{
          box-shadow: -2px 0 8px @shadow-color;
        }
      }
      &.close{
        transform: translateX(100%);
      }
    }
  }
  .content{
    display: inline-block;
    height: 100vh;
    overflow-y: auto;
  }
  .handler-container{
    position: absolute;
    display: inline-block;
    text-align: center;
    transition: all 0.5s;
    cursor: pointer;
    top: 200px;
    z-index: 100;
    .handler {
      height: 40px;
      width: 40px;
      background-color: @base-bg-color;
      font-size: 26px;
      box-shadow: 0 2px 8px @shadow-color;
      line-height: 40px;
    }
    &.left{
      right: -40px;
      .handler{
        border-radius: 0 5px 5px 0;
      }
    }
    &.right{
      left: -40px;
      .handler{
        border-radius: 5px 0 0 5px;
      }
    }
  }
</style>
