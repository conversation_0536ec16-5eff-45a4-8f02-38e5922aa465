<!-- 车间管理-成型管理-按钮 -->
<template>
  <div class="active" ref="active" :style="[{ width: width + 'px' }]">
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingSendMachine')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingSendMachine') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="handleDispatchMachine" :loading="btnloading1"> 分派 </a-button>
    </div>
    <div class="box showClass">
      <a-button type="primary" @click="$emit('Scancodetodispatch')"> 扫码分派 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingPassStep')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingPassStep') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="OverOrderClick"> 部门过序 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingOrderUpLoad')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingOrderUpLoad') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="UploadOrderClick"> 上传订单 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingZsRegister')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingZsRegister') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="harmomegathusRegisterClick"> 涨缩登记 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingSetRemarks')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingSetRemarks') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ExceptionRemarksClick"> 异常备注 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingSearch')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingSearch') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingIsUrgent')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingIsUrgent') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="SetUpExpeditingClick" :loading="btnloading2"> 设置加急 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingDataCheck')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingDataCheck') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="DataCheckClick" :loading="btnloading3"> 数据核对 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingDelOrder')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingDelOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="DeleteOrderClick" :loading="btnloading4"> 删除订单 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingCallTrolley')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingCallTrolley') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="operationClick1" :loading="btnloading5"> 呼叫小车 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingAffirm')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingAffirm') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="operationClick2" :loading="btnloading6"> 人员确认 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.ProductionModule.Forming.FormingAgvCancel')"
      :class="checkPermission('MES.ProductionModule.Forming.FormingAgvCancel') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="operationClick3" :loading="btnloading7"> 取消小车 </a-button>
    </div>
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "Action",
  props: ["btnloading1", "btnloading2", "btnloading3", "btnloading4", "btnloading5", "btnloading6", "btnloading7"],
  data() {
    return {
      advanced: false,
      height: 50,
      width: 762,
      collapsed: false,
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },

  methods: {
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    checkPermission,
    // 锁定
    UpFileLock() {
      this.$emit("UpFileLock");
    },
    // 分派订单
    handleDispatchMachine() {
      this.$emit("handleDispatchMachine");
    },
    // 部门过序
    OverOrderClick() {
      this.$emit("OverOrderClick");
    },
    // 上传订单
    UploadOrderClick() {
      this.$emit("UploadOrderClick");
    },
    // 涨缩登记
    harmomegathusRegisterClick() {
      this.$emit("harmomegathusRegisterClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    //设置加急
    SetUpExpeditingClick() {
      this.$emit("SetUpExpeditingClick");
    },
    // 数据核对
    DataCheckClick() {
      this.$emit("DataCheckClick");
    },
    // 删除订单
    DeleteOrderClick() {
      this.$emit("DeleteOrderClick");
    },
    //异常备注
    ExceptionRemarksClick() {
      this.$emit("ExceptionRemarksClick");
    },
    // 呼叫小车
    operationClick1() {
      this.$emit("operationClick1");
    },
    // 人员确认
    operationClick2() {
      this.$emit("operationClick2");
    },
    // 取消小车
    operationClick3() {
      this.$emit("operationClick3");
    },
  },
  // created(){
  //   this.$nextTick(()=>{
  //     if (this.$refs.active.children.length > 7 ) {
  //       this.height = 104
  //       this.collapsed = true
  //     } else {
  //       this.height = 52
  //     }
  //   })
  // }
};
</script>

<style scoped lang="less">
.active {
  float: right;
  height: 50px;
  display: flex;
  justify-content: flex-end;
  // flex-wrap: wrap;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/.box {
    width: 106px;
    margin: 10px 0;
    text-align: center;
    .ant-btn {
      width: 80px;
    }
    .ant-btn-loading {
      padding-left: 0 !important;
    }
  }
}
</style>
