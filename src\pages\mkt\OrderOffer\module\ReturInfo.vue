<!-- 市场管理 - 订单报价- 返单新增 -->
<template>
  <div ref="SelectBox">
    <div>
      <a-card>
        <div
          style="
            text-align: center;
            background-color: #fafafa;
            width: 100%;
            border-top: 1px solid #ddd;
            border-right: 1px solid #ddd;
            border-left: 1px solid #ddd;
          "
        >
          类别信息
        </div>
        <a-form-model>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="客户代码" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-input v-model="val.custNo" allowClear disabled></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <!-- <a-col :span="6">
            <a-form-model-item label="交货数量" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
            <div class="editWrapper" >
                <a-input v-model="val.num"  allowClear @change="deliveryarea" ></a-input>
              </div>               
            </a-form-model-item>
          </a-col>   -->
            <a-col :span="6">
              <a-form-model-item label="交货单位" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" class="require">
                <div class="editWrapper">
                  <a-select
                    v-model="val.delType"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    style="width: 100%"
                    @change="deliveryarea"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.DelType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="交货面积" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="val.boardArea" allowClear disabled></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="产品属性" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="val.orderAttribute" allowClear disabled> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="测试方式" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="val.flyingProbe"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    style="width: 100%"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.FlyingProbe)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="总测试点数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="val.testPointNum" allowClear disabled></a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="已开测试架" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-checkbox v-model="val.openedTestrack" allowClear></a-checkbox>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="客户PO" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
                <div class="editWrapper">
                  <a-input v-model="val.custPo" allowClear></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="价格类型" :label-col="{ span: 9 }" :wrapper-col="{ span: 15 }">
                <div class="editWrapper">
                  <a-select
                    v-model="val.priceType"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    style="width: 100%"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option v-for="(item, index) in mapKey(selectOption.PriceType)" :key="index" :value="item.value" :lable="item.lable">
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="18">
              <a-form-model-item label="客户交期" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <div class="editWrapper">
                  <!-- <a-input  allowClear></a-input> -->
                  <a-date-picker v-model="val.deliveryDate" />
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="收货地址" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <div class="editWrapper">
                  <a-select
                    v-model="val.shippingAddress"
                    placeholder="请选择"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    style="width: 100%"
                    :getPopupContainer="() => this.$refs.SelectBox"
                  >
                    <a-select-option
                      v-for="(item, index) in val.shippingAddressdto"
                      :key="index"
                      :value="item.shippingAddressdto"
                      :lable="item.shippingAddressdto"
                    >
                      {{ item.shippingAddressdto }}
                    </a-select-option>
                  </a-select>
                  <!-- <a-input v-model="val.shippingAddress" allowClear></a-input> -->
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <!-- <a-divider style="height: 6px; background-color: #C2C1C2;margin:8px 0;" /> -->
      </a-card>
    </div>
    <a-divider style="height: 6px; background-color: #c2c1c2; margin: 5px 0" />
    <div style="text-align: center; background-color: #fafafa; width: 100%; border: 1px solid #ddd">类别信息</div>
    <div style="border-left: 1px solid #ddd; border-bottom: 1px solid #ddd; border-right: 1px solid #ddd">
      <a-table :columns="columns" :dataSource="val.dto" :pagination="false" :scroll="{ y: 340 }" centered>
        <template slot="vvalue" slot-scope="text, record">
          <span v-if="record.valueStr == 'False'"> 否 </span>
          <span v-else-if="record.valueStr == 'True'"> 是 </span>
          <div v-else>{{ record.valueStr }}</div>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script>
const columns = [
  {
    title: "序号",
    key: "index",
    dataIndex: "index",
    align: "center",
    width: 60,
    customRender: (text, record, index) => `${index + 1}`,
  },

  {
    title: "项目名称",
    dataIndex: "nmae",
    align: "left",
    ellipsis: true,
    width: 350,
  },
  {
    title: "参数",
    dataIndex: "valueStr",
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "vvalue" },
  },
];
export default {
  props: ["val", "selectOption"],
  data() {
    return {
      columns,
      template: false,
    };
  },
  methods: {
    mapKey(data) {
      if (!data || data.length == 0) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    deliveryarea() {
      if (this.val.delType == "PCS" && this.val.num) {
        this.val.boardArea = Number((this.val.setBoardHeight * this.val.setBoardWidth * this.val.num) / this.val.su / 1000000.0).toFixed(4);
      } else {
        this.val.boardArea = Number((this.val.setBoardHeight * this.val.setBoardWidth * this.val.num) / 1000000.0).toFixed(4);
      }
      if (this.val.num) {
        this.val.testPointNum = Number(this.val.num) * this.val.testPointNumOld;
      } else {
        this.val.testPointNum = null;
      }
      if (this.val.boardArea < 3) {
        this.val.orderAttribute = "样板";
      } else {
        this.val.orderAttribute = "批量";
      }
      if (!this.val.openedTestrack && this.val.boardArea && this.val.delType) {
        if (this.val.boardArea >= 5) {
          this.val.flyingProbe = "teststand";
        } else {
          this.val.flyingProbe = "FlyingProbe";
        }
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-table-row-cell-break-word {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  font-size: 12px;
}

/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.ant-table {
  /deep/.ant-table-thead tr {
    height: 0 !important;
  }
  .ant-table-thead tr th {
    padding: 6px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 6px 4px;
    border-right: 1px solid #efefef;
  }
  tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
  tr.ant-table-row-hover td {
    background: #dfdcdc;
  }
  .rowBackgroundColor {
    background: #dfdcdc;
  }

  .ant-table-selection-col {
    width: 20px !important;
  }
}

.ant-modal-content {
  .ant-modal-body {
    .ant-card {
      /deep/ .ant-card-body {
        padding: 0 !important;
      }
    }
  }
}
.ant-modal-content {
  .ant-modal-body {
    .ant-card {
      .ant-card-body {
        .ant-form {
          border-left: 1px solid #ddd;
          border-top: 1px solid #ddd;
        }
        .ant-row {
          margin-bottom: 0;
          .ant-col {
            .ant-form-item {
              /deep/.ant-form-item-label {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
                font-weight: 500;
                color: #666;
                background-color: #fafafa;
                border-right: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                height: 30px;
                label {
                  font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
                  font-weight: 500;
                }
              }
              /deep/.ant-form-item-control-wrapper {
                font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
                font-weight: 500;
                .ant-form-item-control {
                  line-height: inherit;
                  padding: 3px 4px;
                  border-right: 1px solid #ddd;
                  border-bottom: 1px solid #ddd;
                  // border-top: 1px solid #ddd;
                  height: 30px;
                  .ant-form-item-children {
                    .editWrapper {
                      .ant-select {
                        .ant-select-selection {
                          height: 24px;
                          .ant-select-selection__rendered {
                            line-height: 24px;
                          }
                        }
                      }
                      .ant-checkbox-wrapper {
                        .ant-checkbox {
                          margin-top: 5px;
                        }
                      }
                      .ant-input-affix-wrapper {
                        .ant-input {
                          height: 24px;
                          padding: 0 5px;
                        }
                      }
                      .ant-calendar-picker {
                        .ant-calendar-picker-input {
                          height: 24px !important;
                          width: 458px;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
