import { request, METHOD } from '@/utils/request'
export async function routList(params) {
    return request("/api/app/e-mSTProc-rOUT/rout-list", METHOD.GET, params)
}
export async function routUpload(params) {
    return request("/api/app/e-mSTProc-rOUT/up-load-rout-tgz-file", METHOD.POST, params)
}
export async function routAdd(params) {
    return request("/api/app/e-mSTProc-rOUT/rout", METHOD.POST, params)
}
export async function downLoad(Id) {
    return request(`/api/app/e-mSTProc-rOUT/down-load-path/${Id}`, METHOD.GET,)
}

export async function routCancel(Id) {
    return request(`/api/app/e-mSTProc-rOUT/cancel-order/${Id}`, METHOD.POST,)
}
export async function routSet(parmas) {
    return request(`/api/app/e-mSTProc-rOUT/set-naming`, METHOD.POST,parmas)
}
export async function getRoutSet(parmas) {
    return request(`/api/app/e-mSTProc-rOUT/naming`, METHOD.GET)
}


export  default {
    routList,
    routUpload,
    routAdd,
    downLoad,
    routCancel,
    routSet,
    getRoutSet
}
