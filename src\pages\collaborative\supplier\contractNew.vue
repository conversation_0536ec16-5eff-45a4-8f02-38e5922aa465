<!--供应链  - 合同管理-->
<template>
  <div ref="sb">
    <a-card class="boox">
      <div style="min-width: 1650px">
        <a-form-model layout="inline" :model="formInline" @submit="handleSubmit" @submit.native.prevent>
          <a-form-model-item label="公司名称">
            <a-input v-model="formInline.FactoryName" placeholder="公司名称" style="width: 100px" allowClear> </a-input>
          </a-form-model-item>
          <a-form-model-item label="工厂代码">
            <a-input v-model="formInline.FactoryCode" placeholder="工厂代码" style="width: 100px" allowClear> </a-input>
          </a-form-model-item>
          <a-form-model-item label="合同编号">
            <a-input v-model="formInline.ContractNo" placeholder="合同编号" style="width: 100px" allowClear> </a-input>
          </a-form-model-item>
          <a-form-model-item label="合同状态">
            <a-select
              v-model="formInline.ContractStatus"
              style="width: 120px"
              placeholder="合同状态"
              :getPopupContainer="() => this.$refs.sb"
              allowClear
            >
              <a-select-option value="生效中"> 生效中 </a-select-option>
              <a-select-option value="作废" name="作废"> 作废 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="合同到期时间">
            <a-range-picker v-model="timeValue" @change="onChange" style="width: 200px" />
            <br />
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit" style="padding-top: 4px" @click="findList"> {{ $t("search_button") }} </a-button>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit" style="padding-top: 4px" @click="reset"> {{ $t("reset_button") }} </a-button>
          </a-form-model-item>

          <a-button type="primary" class="margin10" @click="addlist">{{ $t("add_button") }}</a-button>
          <a-button type="primary" class="margin10" @click="review">{{ $t("review_button") }}</a-button>
          <a-button type="primary" class="margin10" @click="set">{{ $t("settings_button") }}</a-button>
          <a-button type="primary" class="margin10" @click="bianji"> {{ $t("edit_button") }}</a-button>
        </a-form-model>
      </div>
      <a-table
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        @change="handleTableChange"
        :loading="loading"
        bordered
        :rowClassName="tableRowClass"
        :row-selection="{ selectedRowKeys: selectedRowKeys_cnt, onChange: onSelectChange_content, columnWidth: 25 }"
        :customRow="onClickRow"
      >
        <span slot="action" slot-scope="record" style="cursor: pointer">
          <!--<a-popover title="Title">-->
          <!--<template slot="content">-->
          <!--{{record}}-->
          <!--</template>-->
          <span @click="logBtn(record)">日志</span>
          <!--</a-popover>-->
        </span>
        <span slot="contractExamine" slot-scope="record, text">
          <span v-if="record == '复核中'">{{ record }}</span>
          <a :href="text.contractUrl" download="" v-else style="text-decoration: underline; color: #0000cc; cursor: pointer">下载</a>
        </span>
        <span
          slot="contractExpireDate"
          slot-scope="record, text"
          :class="[classFilter(text) == '1' ? 'y_bg' : classFilter(text) == '2' ? 'r_bg' : '']"
        >
          {{ record }}
        </span>
      </a-table>
      <a-modal
        title="新增合同"
        :visible="visible"
        :confirm-loading="confirmLoading"
        centered
        @ok="handleOk"
        @cancel="handleCancel"
        :maskClosable="false"
        width="950px"
      >
        <a-form-model
          ref="ruleForm"
          :model="form"
          :rules="rules"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          style="display: flex; flex-wrap: wrap"
          class="formStyle"
        >
          <a-form-model-item ref="factoryName" label="公司名称" prop="factoryName">
            <a-input
              v-model="form.factoryName"
              @blur="
                () => {
                  $refs.factoryName.onFieldBlur();
                }
              "
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item ref="contractNo" label="合同编号" prop="contractNo">
            <a-input
              v-model="form.contractNo"
              @blur="
                () => {
                  $refs.contractNo.onFieldBlur();
                }
              "
            />
          </a-form-model-item>
          <a-form-model-item ref="factoryCode1" label="供应商编码" prop="factoryCode1">
            <a-select
              show-search
              :filter-option="filterOption"
              v-model="form.factoryCode1"
              placeholder="供应商编码"
              @change="seleChange"
              label-in-value
              :getPopupContainer="() => this.$refs.sb"
            >
              <a-select-option :value="item.supplierName" v-for="item in codeList" :key="item.supplierName">
                {{ item.factoryCode }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item ref="reV_" label="合同版本号" prop="reV_">
            <a-input
              v-model="form.reV_"
              @blur="
                () => {
                  $refs.reV_.onFieldBlur();
                }
              "
            />
          </a-form-model-item>
          <a-form-model-item ref="signingSubject" label="签约主体" prop="signingSubject">
            <a-select v-model="form.signingSubject" :getPopupContainer="() => this.$refs.sb">
              <a-select-option value="江西捷配电子科技有限公司" name="江西捷配电子科技有限公司"> 江西捷配电子科技有限公司 </a-select-option>
              <a-select-option value="安徽捷圆电子科技有限公司" name="安徽捷圆电子科技有限公司"> 安徽捷圆电子科技有限公司 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="合同性质" prop="contractNature">
            <a-select v-model="form.contractNature" :getPopupContainer="() => this.$refs.sb">
              <a-select-option value="付款加工合同" name="付款加工合同"> 付款加工合同 </a-select-option>
              <a-select-option value="品质合同" name="品质合同"> 品质合同 </a-select-option>
              <a-select-option value="交期合同" name="交期合同"> 交期合同 </a-select-option>
              <a-select-option value="合同补充条款" name="合同补充条款"> 合同补充条款 </a-select-option>
              <a-select-option value="其他" name="其他"> 其他 </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="合同开始时间" required prop="contractStartDate">
            <a-date-picker v-model="form.contractStartDate" placeholder="合同开始时间" style="width: 100%" @change="onChangetime" />
          </a-form-model-item>
          <a-form-model-item label="合同到期时间" required prop="contractExpireDate">
            <a-date-picker v-model="form.contractExpireDate" placeholder="合同到期时间" style="width: 100%" @change="onChangetime1" />
          </a-form-model-item>
          <a-form-model-item ref="paymentDay" label="付款时间" prop="paymentDay" style="position: relative">
            <a-input v-model="form.paymentDay" @focus="focusChang" />
            <table class="checkTab" v-show="timeStat" @mouseleave="blurChang()">
              <tr>
                <td v-for="(item, index) in arr" :key="index" @click="getTableContent(item.val, index)">
                  {{ item.val }}
                </td>
              </tr>
            </table>
          </a-form-model-item>
          <a-form-model-item label="合同状态" prop="contractStatus">
            <a-radio-group name="radioGroup" v-model="form.contractStatus" placeholder="合同状态">
              <a-radio :value="1"> 生效中 </a-radio>
              <a-radio :value="2"> 作废 </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item ref="contractUrl" label="文件上传" prop="contractUrl" style="position: relative">
            <div style="display: flex; align-items: center">
              <a-input v-model="form.contractUrl" disabled style="width: 300px"></a-input>
              <a-upload
                name="file"
                :multiple="false"
                :customRequest="customRequest"
                @change="handleChangeImg"
                :showUploadList="false"
                accept="application/pdf"
              >
                <a-button>
                  <a-icon type="upload" />
                </a-button>
              </a-upload>
            </div>
          </a-form-model-item>
          <a-form-model-item label="银行账号" prop="bankAccount">
            <a-input v-model="form.bankAccount" />
          </a-form-model-item>
          <a-form-model-item label="开户行" prop="bank">
            <a-input v-model="form.bank" />
          </a-form-model-item>
          <a-form-model-item label="联系人" prop="reconcilor">
            <a-input v-model="form.reconcilor" />
          </a-form-model-item>
          <a-form-model-item label="联系电话" prop="telephone">
            <a-input v-model="form.telephone" />
          </a-form-model-item>
          <a-form-model-item ref="balanceMethed" label="对账类型" prop="balanceMethed">
            <a-select v-model="form.balanceMethed" :getPopupContainer="() => this.$refs.sb">
              <a-select-option :value="0">默认</a-select-option>
              <a-select-option :value="1">全部</a-select-option>
              <a-select-option :value="2">上月</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <a-modal title="日志" :visible="visible1" :confirm-loading="confirmLoading" @ok="handleOk1" @cancel="handleCancel1">
        <p>{{ ModalText }}</p>
      </a-modal>
      <a-modal title="日志" :visible="visible2" :confirm-loading="confirmLoading" @ok="handleOk2" @cancel="handleCancel2" width="500px">
        <a-table rowKey="id" :columns="logCaozuo" :dataSource="logList" :pagination="false" :scroll="{ y: 500 }"> </a-table>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import AFormModelItem from "ant-design-vue/es/form-model/FormItem";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 60,
    align: "center",
  },
  {
    title: "公司名称",
    dataIndex: "factoryName",
    align: "left",
    width: 150,
  },
  {
    title: "工厂代码",
    dataIndex: "factoryCode",
    align: "left",
    width: 80,
  },
  {
    title: "合同开始时间",
    dataIndex: "contractStartDate",
    align: "left",
    width: 150,
  },
  {
    title: "合同到期时间",
    dataIndex: "contractExpireDate",
    align: "left",
    scopedSlots: { customRender: "contractExpireDate" },
    width: 150,
  },
  {
    title: "签约主体",
    dataIndex: "signingSubject",
    align: "left",
    width: 100,
  },
  {
    title: "合同录入时间",
    dataIndex: "contractInDate",
    align: "left",
    width: 150,
  },
  {
    title: "合同性质",
    dataIndex: "contractNature",
    align: "left",
  },
  {
    title: "合同编号",
    dataIndex: "contractNo",
    align: "left",
  },
  {
    title: "合同版本号",
    dataIndex: "reV_",
    align: "left",
  },
  {
    title: "付款时间",
    dataIndex: "paymentDay",
    align: "left",
  },
  {
    title: "合同状态",
    dataIndex: "contractStatus",
    align: "left",
  },
  {
    title: "审批状态",
    dataIndex: "contractExamine",
    align: "left",
    scopedSlots: { customRender: "contractExamine" },
  },
  {
    title: "操作日志",
    key: "action",
    align: "left",
    scopedSlots: { customRender: "action" },
    ellipsis: true,
  },
];
let logCaozuo = [
  {
    title: "时间",
    dataIndex: "inDate",
  },
  {
    title: "操作",
    dataIndex: "inUserName",
  },
  {
    title: "内容",
    dataIndex: "content",
  },
];
import {
  contractData,
  contractAdd,
  contractUpload,
  contractName,
  contractReview,
  contractSet,
  contractCX,
  contractXG,
  logData,
  sync,
} from "@/services/contract/index";
import moment from "moment";
export default {
  name: "",
  components: { AFormModelItem },
  i18n: require("@/components/language/common/buttoni18n.js"),
  data() {
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      rules: {
        factoryName: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
        factoryCode1: [{ required: true, message: "请选择编码", trigger: "change" }],
        contractStartDate: [{ required: true, message: "请选择时间", trigger: "change" }],
        contractExpireDate: [{ required: true, message: "请选择时间", trigger: "change" }],
        signingSubject: [{ required: true, message: "请选择签约主体", trigger: "blur" }],
        contractNature: [{ required: true, message: "请选择合同性质", trigger: "change" }],
        contractNo: [{ required: true, message: "请输入合同编号", trigger: "blur" }],
        contractStatus: [{ required: true, message: "请选择合同状态", trigger: "change" }],
        reV_: [{ required: true, message: "请输入合同版本号", trigger: "blur" }],
        // contractUrl:[{ required: true, message: '请上传文件', trigger: 'change' }],
        bankAccount: [{ required: true, message: "请输入银行账号", trigger: "blur" }],
        bank: [{ required: true, message: "请输入开户行", trigger: "blur" }],
        reconcilor: [{ required: true, message: "请输入联系人", trigger: "blur" }],
        telephone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
        balanceMethed: [{ required: true, message: "请输入对账类型", trigger: "change" }],
      },
      formInline: { ContractStatus: "生效中" },
      loading: false,
      pagination: {
        pageSize: 10,
        current: 1,
        total: 0,
        showSizeChanger: true,
        showLessItems: true,
        showQuickJumper: true,
        pageSizeOptions: ["10", "20", "50", "100"],
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
      },
      dataSource: [],
      columns: columns,
      timeValue: undefined,
      timeValue1: undefined,
      visible: false,
      confirmLoading: false,
      selectedRowKeys_cnt: [],
      arr: [
        { val: 1 },
        { val: 2 },
        { val: 3 },
        { val: 4 },
        { val: 5 },
        { val: 6 },
        { val: 7 },
        { val: 8 },
        { val: 9 },
        { val: 10 },
        { val: 11 },
        { val: 12 },
        { val: 13 },
        { val: 14 },
        { val: 15 },
        { val: 16 },
        { val: 17 },
        { val: 18 },
        { val: 19 },
        { val: 20 },
        { val: 21 },
        { val: 22 },
        { val: 23 },
        { val: 24 },
        { val: 25 },
        { val: 26 },
        { val: 27 },
        { val: 28 },
        { val: 29 },
        { val: 30 },
        { val: 31 },
      ],
      form: {
        reV_: "Rev1.0",
        contractStartDate: undefined,
      },
      timeStat: false,
      list: [],
      codeList: [],
      guid: "",
      ModalText: "",
      visible1: false,
      statSype: "",
      stattype: true,
      logCaozuo: logCaozuo,
      logList: [],
      visible2: false,
    };
  },
  component: {},
  created() {
    this.getList();
    this.getCode();
  },
  methods: {
    tableRowClass(record, index) {
      return record.contractStatus == "作废" ? "rowClass" : "";
    },
    reset() {
      this.formInline = {};
      this.timeValue = undefined;
      this.pagination.current = 1;
      this.getList();
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    logBtn(val) {
      logData(val.id).then(res => {
        if (res.code == 1) {
          res.data.forEach(item => {
            item.inDate = moment(item.inDate).format("YYYY-MM-DD");
          });
          this.logList = res.data;
          this.visible2 = true;
        }
      });
    },
    handleOk2() {
      this.visible2 = false;
    },
    handleCancel2() {
      this.visible2 = false;
    },
    addlist() {
      this.visible = true;
      this.stattype = true;
      this.form = {
        reV_: "Rev1.0",
        contractStartDate: undefined,
      };
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    bianji() {
      if (this.guid != "") {
        this.stattype = false;
        contractCX(this.guid).then(res => {
          if (res.code == "1") {
            if (res.data.contractStatus) {
              res.data.contractStatus = 1;
            } else {
              res.data.contractStatus = 2;
            }
            this.form = res.data;
            this.form.factoryCode1 = { key: res.data.factoryCode };
            this.visible = true;
          } else {
            this.$message.info(res.message);
          }
        });
      } else {
        this.$message.info("请选择数据");
      }
    },
    review() {
      if (!this.guid) {
        this.$message.warning("请选择订单");
        return;
      }
      this.visible1 = true;
      this.ModalText = "是否复查";
      this.statSype = "1";
    },
    set() {
      if (!this.guid) {
        this.$message.warning("请选择订单");
        return;
      }
      this.visible1 = true;
      this.ModalText = "是否设置";
      this.statSype = "2";
    },
    handleOk1() {
      if (this.statSype == "1") {
        contractReview(this.guid).then(res => {
          this.$message.info(res.message);
          if (res.code == "1") {
            this.visible1 = false;
            this.getList();
          }
        });
      } else {
        contractSet(this.guid).then(res => {
          this.$message.info(res.message);
          if (res.code == "1") {
            this.visible1 = false;
            this.getList();
          }
        });
      }
    },
    handleCancel1() {
      this.visible1 = false;
    },

    seleChange(value) {
      this.form.factoryCode = value.label;
      this.form.factoryName = value.key;
      this.$forceUpdate();
    },
    getCode() {
      contractName().then(res => {
        this.codeList = res.data;
      });
    },
    findList() {
      this.pagination.current = 1;
      this.getList("search");
      this.timeValue = undefined;
      this.timeValue1 = undefined;
    },
    onChange(date, dateString) {
      this.formInline.ContractExpireDate = dateString[0];
      this.formInline.ContractExpireDate2 = dateString[1];
    },
    onChange1(date, dateString) {
      this.formInline.ContractInDate = dateString[0];
      this.formInline.ContractInDate2 = dateString[1];
    },
    getList(type) {
      if (type == "search") {
        if (!this.formInline.FactoryName && !this.formInline.FactoryCode && !this.formInline.ContractNo && !this.formInline.ContractStatus) {
          this.$message.warn("请输入需要查询的数据");
          return;
        }
      }
      this.formInline.PageIndex = this.pagination.current;
      this.formInline.PageSize = this.pagination.pageSize;
      contractData(this.formInline).then(res => {
        this.pagination.total = res.totalCount;
        res.items.forEach((res, index) => {
          res.index = index + 1;
        });
        this.dataSource = res.items;
      });
    },
    handleSubmit(e) {},
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.getList();
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.guid = record.id;
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeys_cnt = keys;
          },
        },
      };
    },
    //列表勾选获取数据
    onSelectChange_content(selectedRowKeys, selectedRows) {
      this.selectedRowKeys_cnt = selectedRowKeys;
      this.guid = selectedRowKeys[0];
    },
    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          delete this.form.factoryCode1;
          if (this.form.contractStatus == 1) {
            this.form.contractStatus = true;
          } else {
            this.form.contractStatus = false;
          }
          if (this.stattype) {
            contractAdd(this.form).then(res => {
              if (res.code == 1) {
                this.visible = false;
                this.getList();
                this.$message.success("新增成功");
              } else {
                this.$message.error(res.message);
              }
            });
            // }else {
            // this.$message.info('请上传文件')
            // }
          } else {
            this.form.id = this.guid;
            contractXG(this.form).then(res => {
              if (res.code == 1) {
                this.visible = false;
                this.getList();
              } else {
                this.$message.info(res.message);
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    handleCancel() {
      this.visible = false;
    },
    getTableContent(val) {
      if (!this.list.includes(val)) {
        this.list.push(val);
      } else {
        this.list.splice(this.list.indexOf(val), 1);
      }
      this.form.paymentDay = this.list.toString();
      this.$forceUpdate();
    },
    onChangetime(date, dateString) {
      this.form.contractStartDate = dateString;
    },
    onChangetime1(date, dateString) {
      this.form.contractExpireDate = dateString;
    },
    blurChang() {
      this.timeStat = false;
    },
    focusChang() {
      this.timeStat = true;
    },

    handleChangeImg(info) {
      console.log(info);
    },
    customRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      if (data.file.type == "application/pdf") {
        contractUpload(formData).then(res => {
          this.form.contractUrl = res;
          this.$forceUpdate();
          console.log(res);
        });
      } else {
        this.$message.info("请上传pdf格式文件");
      }
    },
    classFilter(record) {
      if (record.contractStatus == "生效中") {
        if (
          Math.floor((new Date(record.contractExpireDate) - new Date()) / (24 * 3600 * 1000)) < 15 &&
          Math.floor((new Date(record.contractExpireDate) - new Date()) / (24 * 3600 * 1000)) > 0
        ) {
          // return { style: {'background': '#FF9900', 'padding': '0 !important', 'margin': '16px 0'}}
          return "1";
        } else if (Math.floor((new Date(record.contractExpireDate) - new Date()) / (24 * 3600 * 1000)) < 0) {
          // return { style: {'background': 'red', 'color': '#fff',}}
          return "2";
        }
      }
    },
    synchronization() {
      if (this.guid != "") {
        //contractNature
        let selectData = this.dataSource.find(item => {
          return item.id == this.guid;
        });
        if (selectData.contractNature == "付款加工合同" && selectData.contractStatus == "生效中") {
          sync(this.guid).then(res => {
            if (res.success) {
              this.$message.success("同步成功");
            } else {
              this.$message.error(res.message);
            }
          });
        } else {
          this.$message.info("数据类型有误");
        }
      } else {
        this.$message.info("请选择数据");
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-calendar-range-picker-input {
  font-weight: 500;
}
/deep/.ant-table {
  .ant-table-content {
    .ant-table-placeholder {
      height: 666px;
    }
  }
}
.boox {
  min-width: 1670px;
}
.box {
  /deep/.ant-form-item-control {
    width: 300px;
  }
}
.margin10 {
  //margin-bottom:10px;
  margin-top: 3px;
  margin-right: 10px;
}
.checkTab {
  width: 200px;
  height: 200px;
  background: #bfbfbf;
  position: absolute;
  z-index: 100;
  tr {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  td {
    width: 33px;
    height: 33px;
    text-align: center;
    line-height: 33px;
    color: white;
    cursor: pointer;
  }
  .active {
    color: red;
  }
}
.formStyle {
  display: flex;
  flex-wrap: wrap;
  .ant-row {
    width: 450px;
    margin-bottom: 5px;
  }
}
/deep/.ant-table {
  min-width: 1630px;
}
/deep/.ant-table-row-selected td {
  background: #fffaf2 !important;
  color: #ff9900 !important;
}
/deep/.ant-table-body {
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    // background: #00aaff;
    background: #eff1f7;
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #f6f8ff;
  }
}
/deep/ .ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 6px 4px !important;
}
/deep/ .ant-table-row td {
  padding: 14px 0 !important;
}
/deep/ .rowClass td {
  color: #dc143c !important;
}
.r_bg {
  background: red;
  color: #fff;
  font-weight: 500;
}
.y_bg {
  background: #ff9900;
}
</style>
<style>
.rowClass {
  color: #dc143c;
  font-weight: 600;
}
</style>
