import { request, METHOD } from "@/utils/request";
import { transformAbpListQuery } from "@/utils/abp";
import { updataSite } from "./CustInfo";

export async function getHeadList(id) {
  return request("/api/app/e-mSFrm-ui-dgv-config/get-dgv-list?names=" + id, METHOD.GET);
}
// 获取客户列表
export async function getList(params) {
  return request("/api/app/e-mSCust-module-no/cust-info", METHOD.GET, params);
}
// 获取日志信息
export async function emSCustmodulenolog(Id) {
  return request(`/api/app/e-mSCust-module-no-log/?Id=${Id}`, METHOD.GET);
}
// 通过id获取客户信息
export async function custinfoDetails(Id) {
  return request(`/api/app/e-mSCust-module-no/cust-info/${Id}`, METHOD.GET);
}
//ERP同步
export function synchronouserpcustinfo(cutsno, <PERSON><PERSON>roy) {
  return request(`/api/app/e-mSCust-module-no/synchronous-erp-cust-info?cutsno=${cutsno}&Factroy=${Factroy}`, METHOD.POST);
}
//黑名单设置
export async function setishmd(Id) {
  return request(`/api/app/e-mSCust-module-no/set-ishmd/${Id}`, METHOD.POST);
}
//黑名单解除
export async function secureishmd(Id) {
  return request(`/api/app/e-mSCust-module-no/secure-ishmd/${Id}`, METHOD.POST);
}
//点击修弹窗
export async function ordermodifylist(Id) {
  return request(`/api/app/verify-nope-add/${Id}/order-modify-list`, METHOD.GET);
}
//点击跟字弹窗
export async function followupcontent(params) {
  return request(`/api/app/verify-nope-add/follow-up-content`, METHOD.POST, params);
}
// 新增客户保存
export async function custinfoSave(params) {
  return request(`/api/app/e-mSCust-module-no/cust`, METHOD.POST, params);
}
// 编辑客户信息保存
export async function updateCust(params) {
  return request(`/api/app/e-mSCust-module-no/update-cust`, METHOD.POST, params);
}
// 删除客户
export async function deleteCust(Id) {
  return request(`/api/app/e-mSCust-module-no/delete-cust/${Id}`, METHOD.POST);
}
// 获取客户选择项
export async function custManageConfig(tradeType) {
  return request(`/api/app/e-mSCust-module-no/cust-manage-config?factory=${tradeType}`, METHOD.GET);
}
export async function selectpars(type, fatory) {
  return request(`/api/app/selection-parameters/select-pars?type=${type}&fatory=${fatory} `, METHOD.GET);
}
// 上传图片路径
export async function uploadFile(params) {
  return request("/api/app/e-mSCust-module-no/up-load-file", METHOD.POST, params);
}
// export async function getList(params) {
//     return request("/api/app/e-mSCust-module-no/cust-info", METHOD.GET,params)
// }

export async function custDetails(id) {
  return request("/api/app/e-mSCust-parameter/get-cust-parameter-info?strGuid=" + id, METHOD.GET);
}

export async function getCustConfig(id) {
  return request("/api/app/e-mSFrm-ui-group-config/get-group-list?uiguid=" + id, METHOD.GET);
}
export async function changeConfig(params) {
  return request("/api/app/e-mSCust-module-no/update-cust", METHOD.POST, params);
}

export async function addConfig(params) {
  return request("/api/app/e-mSCust-module-no/cust", METHOD.POST, params);
}

export async function deleData(params) {
  return request("/api/app/e-mSCust-module-no/delete-cust?ids=" + params, METHOD.DELETE);
}
export async function newConfig(params) {
  return request("/api/app/e-mSCust-ship-loction-list/cust-ship-loction-list", METHOD.POST, params);
}

export async function delSite(params) {
  return request("/api/app/e-mSCust-ship-loction-list/" + params + "/delete-ship-loction-list", METHOD.POST);
}

export async function comSite(params) {
  return request("/api/app/e-mSCust-ship-loction-list/update-cust-ship-loction-list", METHOD.POST, params);
}
export async function dataSite(params) {
  // return request(`/api/app/e-mSCust-ship-loction-list/${id}/get-cust-ship-loction-page-list`, METHOD.GET)
  return request(`/api/app/e-mSCust-ship-loction-list/get-cust-ship-loction-page-list`, METHOD.GET, params);
}

export async function getAsk(id) {
  return request(`/api/app/e-mSTMkt-cust-special-requirements?PGuid_=${id}`, METHOD.GET);
}
export async function saveAsk(params) {
  return request("/api/app/e-mSTMkt-cust-special-requirements", METHOD.POST, params);
}
export async function uploadImg(params) {
  return request("/api/file-management/files/supplieruploadpfile", METHOD.POST, params);
}

export async function deleAsk(id) {
  // return request(`/api/app/e-mSTMkt-cust-special-requirements/${id}/async-by-id`, METHOD.DELETE)
  return request(`/api/app/e-mSTMkt-cust-special-requirements/${id}/delete-async-by-id`, METHOD.POST);
}
export async function amendAsk(params) {
  return request(`/api/app/e-mSTMkt-cust-special-requirements/update`, METHOD.POST, params);
}
export async function userList(id) {
  return request(`/api/app/e-mSCust-module-no/user-list/${id}`, METHOD.GET);
}
export async function isIntention(Id) {
  return request(`/api/app/e-mSCust-module-no/is-intention/${Id}`, METHOD.POST);
}
export async function terminalCusts(TradeType) {
  return request(`/api/app/e-mSCust-ship-loction-list/terminal-custs?TradeType=${TradeType}`, METHOD.GET);
}
export async function CustRequire(Id) {
  return request(`/api/app/pcb-order/special-requirement-list/${Id}`, METHOD.GET);
}
export async function terminalCust(params) {
  return request(`/api/app/e-mSCust-ship-loction-list/terminal-cust-page-list`, METHOD.GET, params);
}
export async function terminalCustList(params) {
  return request(`/api/app/e-mSCust-ship-loction-list/terminal-cust-list`, METHOD.POST, params);
}
export async function deleteTerminalCust(params) {
  return request(`/api/app/e-mSCust-ship-loction-list/delete-terminal-cust?CustItem=${params}`, METHOD.POST);
}
export async function updateterminalcust(params) {
  return request(`/api/app/e-mSCust-ship-loction-list/update-terminal-cust`, METHOD.POST, params);
}
export async function setcustlinks(params) {
  return request(`/api/app/verify-nope-add/set-cust-links`, METHOD.POST, params);
}
export async function custlinks(Id) {
  return request(`/api/app/verify-nope-add/cust-links/${Id} `, METHOD.GET);
}
//销售信息删除临时联系人
export async function deletecustlinks(Ids) {
  return request(`/api/app/verify-nope-add/delete-cust-links`, METHOD.POST, Ids);
}
//获取eq联系人
export async function eqcontactperson(Id) {
  return request(`/api/app/verify-nope-add/${Id}/eq-contact-person`, METHOD.GET);
}
//新增风险警告
export async function addRiskWarning(params) {
  return request(`/api/app/risk-warning`, METHOD.POST, params);
}
//修改风险警告
export async function updateRiskWarning(params) {
  return request(`/api/app/risk-warning/update`, METHOD.POST, params);
}
//获取风险警告
export async function getRiskWarning(params) {
  return request(`/api/app/risk-warning`, METHOD.GET, params);
}
//风险警告失效
export async function updatestatus(params) {
  return request(`/api/app/risk-warning/update-status`, METHOD.POST, params);
}
export default {
  getList,
  custDetails,
  getHeadList,
  getCustConfig,
  changeConfig,
  addConfig,
  deleData,
  newConfig,
  delSite,
  comSite,
  dataSite,
  getAsk,
  saveAsk,
  uploadImg,
  deleAsk,
  amendAsk,
  terminalCusts,
  CustRequire,
  deletecustlinks,
};
