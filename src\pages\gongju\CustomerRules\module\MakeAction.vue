<!-- 工具管理- 客户规则- 按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleDeleteCustRule')">
      <a-button type="primary" @click="$emit('deletecustrule')"> 删除失效 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleRepait')">
      <a-button type="primary" @click="edtiClick1()"> 编辑规则 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleSaveNew')">
      <a-button type="primary" @click="addClick(1)"> 新增备注 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleRepait')">
      <a-button type="primary" @click="edtiClick(2)"> 编辑备注 </a-button>
    </div>

    <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleSave') && (!editFlag || editFlag1)">
      <a-button type="primary" @click="saveClick(0)" v-if="!editFlag"> 保存备注 </a-button>
      <a-button type="primary" @click="saveClick()" v-else> 保存规则 </a-button>
    </div>
    <!-- <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleCancel')">
      <a-button type="primary" @click ="cancelClick(0)">
        取消
      </a-button>
    </div> -->

    <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleUpLoadCustRuleAttFile')">
      <a-button type="primary" @click="addFlieClick"> 添加附件 </a-button>
      <a-upload ref="fileRef" :customRequest="httpRequest1">
        <a-button style="width: 80px; display: none"><a-icon type="upload" /></a-button>
      </a-upload>
    </div>

    <!-- <div class="box" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleDeleteAtt')">
      <a-button type="primary" @click ="delFlieClick">
        删除附件
      </a-button>
    </div> -->
    <!--    <span class="box">-->
    <!--      <a-button type="dashed" @click="toggleAdvanced">-->
    <!--        {{advanced ? '收起' : '展开'}}-->
    <!--        <a-icon :type="advanced ? 'right' : 'left'" />-->
    <!--      </a-button>-->
    <!--    </span>-->
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import protocolCheck from "@/utils/protocolcheck";
import TagSelectOption from "@/components/tool/TagSelectOption";
import { finish, upLoadCamFile } from "@/services/projectMake";
import { attFile } from "@/services/CustRule";

export default {
  name: "MakeAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    assignBackLoading: {
      type: Boolean,
    },
    selectId: {
      type: String,
    },
    editFlag: {
      type: Boolean,
    },
    editFlag1: {
      type: Boolean,
    },
  },
  data() {
    return {
      selectValue: "生成叠层",
      advanced: false,
      width: 762,
      collapsed: false,
      orderId: "",
    };
  },
  created() {
    this.$nextTick(() => {
      if (this.$refs.active.children.length > 7) {
        let domLength = this.$refs.active.children.length;
        let sty_ = "";
        for (var i = 0; i < domLength; i++) {
          if (i == this.$refs.active.children.length - 1) {
            sty_ = "order:11";
          } else {
            sty_ = "order:" + i * 2;
          }
          this.$refs.active.children[i].style.cssText = sty_;
        }
        this.width = 1500;
        this.collapsed = true;
      } else {
        this.collapsed = false;
        this.width = 762;
      }
    });
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      if (this.advanced) {
        width_ = 1500;
      } else {
        width_ = 762;
      }
      this.$refs.active.style.width = width_ + "px";
    },

    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 新增
    addClick(code) {
      this.$emit("addClick", code);
    },
    // 编辑
    edtiClick(code) {
      this.$emit("edtiClick", code);
    },
    edtiClick1() {
      this.$emit("edtiClick1");
    },
    // 保存
    saveClick(code) {
      this.$emit("saveClick", code);
    },
    // 取消
    cancelClick(code) {
      this.$emit("cancelClick", code);
    },
    // 添加附件
    addFlieClick() {
      this.$emit("addFlieClick");
    },
    openApp() {
      protocolCheck(
        "WebshellEmsCam://",
        fail => {
          console.log("fail", fail);
          // 没有安装 弹窗显示 引导去下载
          this.$message.error("未安装注册");
        },
        succ => {
          // 安装则直接打开
          console.log("succ", succ);
        }
      );
    },
    // beforeUpload1(file){
    //   const isFileType = file.name.indexOf('.rar') != -1 || file.name.indexOf('.zip') != -1
    //   console.log('file',file)
    //   if (!isFileType) {
    //     this.$message.error('只支持.rar或.zip格式文件');
    //   }
    //   return isFileType
    // },
    // 新增附件
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      attFile(this.orderId, formData).then(res => {
        if (res.code == 1) {
          this.$message.success("添加成功");
          this.$emit("getAttInfo", this.orderId);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    clickUpload(id) {
      this.orderId = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 90px;
    margin-top: 10px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
