/*
 * @Author: CJP
 * @Date: 2022-06-16 15:08:05
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-27 10:00:31
 * @FilePath: \vue-antd-admin\src\services\projectDisptch\index.js
 * @Description:
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved.
 */
import { request, METHOD } from '@/utils/request';
// 用户登录
export async function custverification(CustNo,ClientLoginKey) {
    return request(`/api/app/e-mSCust-module-no/cust-verification?CustNo=${CustNo}&ClientLoginKey=${encodeURIComponent(ClientLoginKey)}`, METHOD.GET,)
}
//获取问客管理数据
export async function eqmanagementlistbycustno(params) {
    return request(`/api/app/pro-order-list/eq-management-list-by-cust-no`, METHOD.GET,params)
}
//获取出货报告列表
export async function shipreport(params) {
    return request(`api/app/ship-report/eq-management-list-by-cust-no`, METHOD.GET,params)
}
//WIP左边数据
export async function wipselectlist(params) {
    return request(`/api/app/pcb-order-list/wip-select-list-by-customer`, METHOD.GET,params)
}
//WIP右边数据
export async function wipordermanegepagelist(params) {
    return request(`api/app/pcb-order-list/wip-order-manage-page-list-by-customer`, METHOD.GET,params)
}
//WIP获取生产稿文件
export async function workfilepath(params) {
    return request(`/api/app/pro-order/work-file-path`, METHOD.GET,params)
}
//登陆成功后调取接口
export async function wipdatalHDC(CustNo) {
    return request(`/api/app/verify-nope-add/wip-data-lHDC?CustNo=${CustNo}`, METHOD.GET)
}
//申请钢网
export async function serverworkfilepath(Id) {
    return request(`/api/app/pro-order/server-work-file-path/${Id}`, METHOD.GET)
}
//下载EQ表单
export async function exporteQReportbycustppe(id,joinFactoryId,eQSource,type) {
    return request(`/api/app/e-mSEQMain/${id}/export-eQReport-by-cust-ppe/${joinFactoryId}?eQSource=${eQSource}&type=${type}`, METHOD.POST)
}
//下载附件
export async function downeQAttachmentbycust(joinFactoryId,OrderNo,BusinessOrderNo) {
    return request(`api/app/e-mSEQMain/down-eQAttachment-by-cust/${joinFactoryId}?OrderNo=${OrderNo}&BusinessOrderNo=${BusinessOrderNo}`, METHOD.POST)
}
//用户问客管理详情进入回复界面时修改EQ已阅状态
export async function iseQRead(id) {
    return request(`/api/app/e-mSEQMain/${id}/update-is-eQRead`, METHOD.POST)
}
//用户问客下载留底
export async function exporteQReportcust(fac,orderNo,eQSource,BusinessOrderNo) {
    return request(`/api/app/e-mSEQMain/export-eQReport-cust/${fac}?orderNo=${orderNo}&type=0&eQSource=${eQSource}&BusinessOrderNo=${BusinessOrderNo}`, METHOD.POST)
}
//用户问客下载留底3
export async function exporteQReportcustv2(fac,orderNo,eQSource,BusinessOrderNo) {
    return request(`/api/app/e-mSEQMain/export-eQReport-cust-v2/${fac}?orderNo=${orderNo}&type=0&eQSource=${eQSource}&BusinessOrderNo=${BusinessOrderNo}`, METHOD.POST)
}
//用户问客下载留底2
export async function downeQAttachmentcust(params) {
    return request(`/api/app/e-mSEQMain/down-eQAttachment-cust`, METHOD.POST,params)
}
//修改密码 用户管理
export async function updateclientloginkey(params) {
    return request(`/api/app/e-mSCust-module-no/update-client-login-key`, METHOD.POST,params)
}
//判断是否下载生产稿
export async function hasdownloadworkfile(id) {
    return request(`/api/app/e-mSEQMain/${id}/has-down-load-work-file`, METHOD.POST)
}
//立即下单
export async function platformcreateorder(params) {
    return request(`/api/app/pcb-order/cust-form-create-order`, METHOD.POST,params)
}