<!--品质管理- 报废管理 -->
<template>
  <div ref="SelectBox">
     <a-spin :spinning="spinning">
      <div class="projectBackend">
        <div class="content"> 
              <a-input style="width:8%;margin-right:0.5%;" placeholder="拼版单号" v-model="formData.PinBanNo" allowClear></a-input>
              <!-- <a-input  placeholder="流程卡号" v-model="formData.CardNo"></a-input>             -->
              <a-select 
                show-search
                placeholder="请选择工序"  
                allowClear            
                option-filter-prop="children"
                :filter-option="filterOption"
                v-model='formData.StepKey'
                :getPopupContainer="()=>this.$refs.SelectBox"                
                >
                <a-select-option v-for="(item,index) in StepKeyList" :key="index" :value="item.text">
                  {{item.text}}
                </a-select-option>
              </a-select>
              <!-- <a-input  placeholder="责任人" v-model="formData.PersonLiable"></a-input> -->
              <a-date-picker  
                  style="margin-right:0.5%;"              
                  format="YYYY-MM-DD "               
                  placeholder="报废时间-开始"
                  @change="onChange1"
              />
              <a-date-picker  
                  style="margin-right:0.5%;"              
                  format="YYYY-MM-DD "               
                  placeholder="报废时间-结束"
                  @change="onChange2"
              />
              <a-button type="primary" @click="searchClick" style="margin-right:0.5%;">搜索</a-button>  
              <a-button type="primary" @click="addClick" style="margin-right:0.5%;">新增</a-button>                  
        </div>
        <div class="leftContent">
          <a-table 
            :columns="columns" 
            :dataSource="orderListData" 
            :customRow="onClickRow"
            :pagination="pagination" 
            :rowKey="'id'" 
            :loading="orderListTableLoading"
            @change="handleTableChange"
            :rowClassName="isRedRow"
            :scroll="{y:680}"
            :class="orderListData.length ? 'minTable':''"
          >
            <span slot="num" slot-scope="text, record, index" >
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <template slot="orderNo" slot-scope="record">
              <a  style="color: #428bca" @click.stop="details(record)">{{record.pinBanNo}} </a>
              <!-- <span>{{record.pinBanNo}}</span> -->
            </template>         
            <template slot="action" slot-scope="record" >
              <!-- <span style="color:#428bca" @click="editClick(record.id)"> <a-icon type="setting"></a-icon>编辑</span> /  -->
              <span style="color:#428bca" @click="delClick(record)"><a-icon type="setting"></a-icon>删除</span>
            </template>      
            
          </a-table>
        </div>
        <div class="footerAction" style='user-select: none;' ></div>
      </div>
      <a-modal
      title=" 确认弹窗"
      :visible="dataVisibleMode"
      @cancel="reportHandleCancel"
      @ok="handleOkMode"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
   >
   <span style="font-size:16px;">【{{orderno}}】</span> 
   <span style="font-size:16px;">{{messageMode}}</span>   
   </a-modal>
    </a-spin>
  </div>
   
</template>

<script>
import moment from "moment";
import {mapState,} from 'vuex';
import {
  stepConfigre,
  proScrapInfoList,
  deleteInfo,
  updateInfo,
} from "@/services/scgl/QualityManagement/quality"

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",    
    scopedSlots: { customRender: 'num' },
    width: "3%",
  },
  {
    title: "拼版单号",
    align: "left",
    width: "8%",
    dataIndex: 'pinBanNo',
 },
  {
    title: "作废料号",
    align: "left",
    width: "8%",
    className:'userStyle',
    scopedSlots: { customRender: 'orderNo' },
    
  },
  
  // {
  //   title: "类型",
  //   key: "tag",
  //   dataIndex:'',
  //   align: "left",
  //   width: "4%",
  // },
  {
    title: "报废数量",
    align: "left",
    width: "4%",
    dataIndex: 'num',
  },
  {
    title: "报废面积",
    align: "left",
    width: "4%",
    dataIndex: 'area',
  },
  {
    title: "报废单位",
    align: "left",
    width: "4%",
    dataIndex: 'unit',
  },
  {
    title: "责任工序",
    dataIndex:'stepKeyStr',
    align: "left",   
    width: "6%",
    
  },

  {
    title: "责任人",
    dataIndex:'personLiable',
    align: "left",
    width: "5%",  
            
  },  
  // {
  //   title: "报废面积",
  //   width: "8%",
  //   align: "left",
  //   scopedSlots:{customRender:'Process'}
  // }, 
  {
    title: "报废缺陷",
    dataIndex:'reason',    
    width: "26%",
    align: "left",
  },
  {
    title: "工厂",
    dataIndex: "adminName",
    width: "4%",
    align: "left",
  },
  // {
  //   title: "补投状态",
  //   scopedSlots: { customRender: ''},
  //   width: "6%",
  //   align: "left",
  // },
  {
    title: "经办工序",
    dataIndex:'createStep',
    align: "left",    
    width: "6%",   
  },
  {
    title: "经办人",
    dataIndex:'createName',
    width: "6%",
    align: "left",
  },
  {
    title: "经办时间",
    dataIndex:'createTime',
    align: "left",
    width: "9%",
  },
  {
    title: "操作",
    scopedSlots: { customRender: 'action' },
    align: "left",
      
  },
]
export default{
    name:'',
    data(){
        return{
          spinning:false,
          formData:{
            PinBanNo:'',
            CardNo:'',
            PersonLiable:'',
            ScrapTimeStart:'',
            StepKey:undefined,
            ScrapTimeEnd:'',
          },
          columns,
          orderListTableLoading:false,
          // pagination:{
          //   current: 1,
          //   pageSize: 25,
          //   showTotal: (total) => `总计 ${total} 条`,
          //   total: 0
          // },
          pagination: {
            pageSize: 20,
            current: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
          },
          StepKeyList:[],
          orderListData:[],
          proOrderId:'',
          dataVisibleMode:false,
          orderno:'',
          messageMode:'',
          id:'',
          modeType:'',
        }
    },
    computed: {
    ...mapState('account', ['user',]),  
  },
    mounted(){
      this.getOrderList()
      this.getStepConfigre()
    },
  methods: {
    moment,
    onChange1 (value, dateString) {
      this.formData.ScrapTimeStart = dateString
    },
    onChange2 (value, dateString) {
      this.formData.ScrapTimeEnd = dateString
    },
    // 获取责任工序选项参数
    getStepConfigre(){
      stepConfigre('','').then(res=>{
        this.StepKeyList = res.data
      })
    },  
    filterOption(input, option) {
      return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    // 获取订单
    getOrderList(queryData){
      let params = {
        'PageIndex': this.pagination.current,
        'PageSize' : this.pagination.pageSize,        
      }
      var obj = Object.assign(params,queryData)
      this.orderListTableLoading = true;
      proScrapInfoList (obj).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
          this.pagination.total = res.data.totalCount;
        }
      }).finally(()=> {
        this.orderListTableLoading = false;
      })
    },
    isRedRow(record) {
      let strGroup = []      
      if (record.proOrderId && record.proOrderId == this.proOrderId) {
        strGroup.push('rowBackgroundColor')
      }      
      return strGroup
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.proOrderId);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record           
            this.proOrderId = record.proOrderId
          },

        }
      }
    },    
    // handleTableChange(pagination) {
    //   this.pagination.current=pagination.current
    //   this.getOrderList()
    // },
    handleTableChange(pagination, ) {
      console.log('改变',pagination)
      this.pagination.current=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      this.getOrderList()
    },
    searchClick(){
      var arr1 = this.formData.PinBanNo.split('')
      if(arr1.length >30){
        arr1 = arr1.slice(0,30)
      }
      this.formData.PinBanNo = arr1.join('')
      this.pagination.current = 1
      this.getOrderList(this.formData)
    },
    //  新增（作废录入）
    addClick(){
      this.$router.push({path:'VoidEntry',})
    },
    // 编辑
    editClick(id){
      this.$router.push({path:'VoidEntry',query:{id:id}})      

    },
    // 删除
    delClick(record){      
      this.orderno = record.cardNo
      this.messageMode='确认删除吗？'
      this.id = record.id
      this.dataVisibleMode = true
      this.modeType = '1'     
    },
    handleOkMode(){
      this.dataVisibleMode = false
      if(this.modeType == '1'){
        deleteInfo(this.id).then(res=>{
        if(res.code){
          this.$message.success('删除成功')
          this.getOrderList()
        }else{
          this.$message.error(res.message)
        }
      })
      }
    },
    reportHandleCancel(){
      this.dataVisibleMode = false
    },
    // 详情跳转
    details(record){
    this.$router.push({path:'/OrderManagement/orderDetail1',query:{id:record.pinBanNo,businessOrderNo:record.pinBanNo} ,})
    }
   
              
  }
}
</script>

<style scoped lang="less">
 .footerAction {
    width: 100%;
    height:60px;
    border-bottom: 2px solid #E9E9F0;
    border-left: 2px solid #E9E9F0;
    border-right: 2px solid #E9E9F0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #FFFFFF;
  }
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
}

/deep/.ant-input{
  font-weight: 500;
}
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
.projectBackend {
  /deep/.userStyle{
    user-select: all!important;
  }
  height: 832px;
  min-width: 1670px;
  // width: 100%;
  background: #FFFFFF;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  .ant-input,.ant-select{
    width:8%;
    margin-right:0.5%;
    margin-top:6px;
  }
  .content {
    height:44px; 
    margin-left:6px; 
  }
  .leftContent {
    .minTable {      
      /deep/.ant-table-body{
        min-height:680px;
      }
    }    
    
    height:720px;
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  /deep/.ant-table .ant-table-tbody > tr > td {
    height: 34px;
  }
  /deep/ .ant-table{
   // min-height: 740px;
    .ant-table-thead > tr > th{
      padding: 4px 2px;
      border-right:1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 2px!important;
      border-right:1px solid #efefef;
    }
    tr.ant-table-row-selected td {
     background:rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
   /deep/ .ant-table-pagination.ant-pagination {
    float: right;
    margin: 16px 11px;
    position: absolute;
}

}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}
</style>

