<!-- 工程管理 - 合拼管理  -->
<template>
 <div class="JointManagement" >
  <a-spin :spinning="spinning">
   <div class="content">
     <div class="left" ref="letfDom" style="width:75%;">
       <a-card :bordered="false" style="height: 398px;border: 1px solid #E9E9F0;"  >
         <!-- 服务器管理 -->
         <a-table
             rowKey="userName_"
             :columns="columns"
             :dataSource="data1Source"
             :pagination="false"
             :loading="table1Loading"
             :keyboard="false"
             :bordered="true"
             :maskClosable="false"
             :customRow="eventTouch"
             :rowClassName="setRowClassName"
             :scroll="{ y:360 }"
             :class="{'minClass':data1Source.length > 0}"
         >
          <template slot="hpServer_Main" slot-scope="text,record">
             <a-checkbox v-model="record.hpServer_Main" disabled>
             </a-checkbox>
           </template>
           <template slot="hpServer_Sub" slot-scope="text,record">
             <a-checkbox v-model="record.hpServer_Sub" disabled>
             </a-checkbox>
           </template>
         </a-table>
       </a-card>
       <a-card :bordered="false" style="height: 382px;border: 1px solid #E9E9F0;" class='leftBto'>
        <!-- @contextmenu.prevent="rightClick($event)" -->
         <!-- 作业信息管理 -->
         <a-table
             rowKey="guid_"
             :columns="columns2"
             :dataSource="data2Source"
             :pagination="false"
             :loading="table2Loading"
             :keyboard="false"
             :bordered="true"
             :maskClosable="false"
             :customRow="eventTouch1"
             :scroll="{ x: '75%',y:341}"
             :rowClassName="setRowClassName1"
             :class="{'minClass':data2Source.length > 0}"
         >
           <template slot="isPrint_" slot-scope="text,record">
             <a-checkbox v-model="record.isPrint_">
             </a-checkbox>
           </template>
           <template slot="ispush" slot-scope="text,record">
             <a-checkbox v-model="record.ispush">
             </a-checkbox>
           </template>
           <template slot="isStop_" slot-scope="text,record">
             <a-checkbox v-model="record.isStop_">
             </a-checkbox>
           </template>
         </a-table>
         <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
        </a-menu>
         <!-- <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
           <a-menu-item>
             <a-upload
              accept=".drl"
              name="file"
              :before-upload="beforeUpload"
              :customRequest="httpRequest0"
             >
               文件上传
             </a-upload>
           </a-menu-item>
           <a-menu-item @click="down">
              流程卡
           </a-menu-item>
         </a-menu> -->
       </a-card>
     </div>
     <div class="right" style="width:25%">
       <center
           :machineStatuList="data4Source"
           :machineStatuLoad="table4Loading"
           :drillLoad="table5Loading"
           :drillList="data5Source"
           ref="cuttingCenter"
           @rightClick="rightClick"
       ></center>
     </div>
   </div>
   <div class="footer">
     <div class="actionBox">
        <action
          @queryClick='queryClick'
        ></action>
     </div>
   </div>

   <!-- 查询弹窗 -->
   <a-modal
          title="订单查询"
          :visible="dataVisible4"
          @cancel="reportHandleCancel"
          @ok="handleOk5"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
          centered
   >
   <query-info ref='queryInfo' />
   </a-modal>
  </a-spin>
 </div>
</template>

<script>
import {
  getCombineManageList,
  getJobinformation,
  getparConfig,
  getManageData,
} from "@/services/joint";
import Center from './module/Center'
import Action from "./module/Action";
import QueryInfo from './module/QueryInfo';
const columns = [
    {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell:(record) =>{
      if(record.color_ == '#FF0000'){
        return {style:{'background':'#FF0000'}}
      }
    }
  },
  {
    title: "电脑名",
    dataIndex: "computerName_",
    width: 130,
    ellipsis: true,
    className:"",
    align: 'center',
  },
  {
    title: "软件版本",
    width: 100,
    ellipsis: true,
    dataIndex: "version_",
    align: 'center',
  },
  {
    title: "IP",
    dataIndex: "pcIp",
    width: 130,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "登陆时间",
    width: 130,
    dataIndex: "inDate",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "主机",
    // dataIndex: "hpServer_Main",
    width: 60,
    ellipsis: true,
    align: 'center',
    scopedSlots: { customRender: 'hpServer_Main' },
  },
  {
    title: "从机",
    // dataIndex: "hpServer_Sub",
    width:60,
    ellipsis: true,
    align: 'center',
     scopedSlots: { customRender: 'hpServer_Sub' },
  },
  {
    title: "用户名",
    dataIndex: "userName_",
    width: 100,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "状态",
    dataIndex: "state_",
    width: 100,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "数量",
    width: 100,
    dataIndex: "counT_",
    align: 'center',
    ellipsis: true,
  },
  {
    title: "ERP锁单数",
    // width: 100,
    dataIndex: "lockNum_",
    align: 'center',
    ellipsis: true,
  },

];
const columns2 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',
    fixed: 'left',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell:(record) =>{
      if(record.color_ == '#FF0000'){
        return {style:{'background':'#FF0000'}}
      }
    }
  },
  {
    title: "合拼工号",
    dataIndex: "pdctno_",
    width: 130,
    ellipsis: true,
    className:"orderClass",
    align: 'center',
    fixed: 'left',
  },
  {
    title: "PNL数",
    width: 100,
    ellipsis: true,
    dataIndex: "fR4Type",
    align: 'center',
  },
  {
    title: "利用率",
    dataIndex: "cardNo",
    width: 115,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "SU数",
    width:60,
    dataIndex: "count4Hp_",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 45,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "生成时间",
    dataIndex: "count4Pnl_",
    width: 70,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "数据合拼",
    width:70,
    dataIndex: "area_",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "数据合拼开始时间",
    dataIndex: "copperThickness",
    width: 130,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "数据合拼结束时间",
    dataIndex: "sheetSize",
    width: 130,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "拼版封边",
    dataIndex: "materialNum_",
    width:70,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "拼版封边开始时间",
    width: 130,
    dataIndex: "boardthick_",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "拼版封边结束时间",
    dataIndex: "minHole_",
    width: 130,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "输出文件",
    dataIndex: "isPrint_",
    width: 70,
    align: 'center',
    // scopedSlots: { customRender: 'isPrint_' },
  },
  {
    title: "输出文件开始时间",
    width:130,
    ellipsis: true,
    dataIndex: "fac_",
    align: 'center',
  },
  {
    title: "输出文件结束时间",
    width: 130,
    ellipsis: true,
    dataIndex: "delDate_",
    align: 'center',
  },
  {
    title: "数据异常",
    width: 70,
    dataIndex: "ispush",
    scopedSlots: { customRender: 'ispush' },
    align: 'center',
  },
  {
    title: "拼版异常",
    width: 70,
    dataIndex: "allHoleNum_",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "输出异常",
    width: 70,
    dataIndex: "endDate_",
    align: 'center',
    ellipsis: true,
  },
  {
    title: "制作人",
    width: 60,
    dataIndex: "cutStartDate_",
    align: 'center',
    ellipsis: true,

  },
  {
    title: "单拼",
    width: 40,
    dataIndex: "note_",
    align: 'center',
    ellipsis: true,
  },
  {
    title: "板材类型",
    width: 70,
    dataIndex: "isStop_",
    scopedSlots: { customRender: 'isStop_' },
    align: 'center',
  },
  {
    title: "板厚",
    width: 40,
    dataIndex: "",
    align: 'center',
  },
];
import {mapState,} from 'vuex'
export default {
  name: "JointManagement",
  components:{Action,QueryInfo,Center},
  inject:['reload'],
  data () {
    return {
      text:'',
      showText:false,
      spinning:false,
      isCtrlPressed:false,
      columns,
      columns2,
      loading:false,
      table1Loading:false,  // 服务器管理表格load
      table2Loading:false,  // 作业信息管理表格load
      table4Loading: false, // 配置参数load
      table5Loading: false, // 明细清单load
      data1Source:[],       // 服务器管理表集合
      data2Source:[],       // 作业信息管理表集合
      data4Source:[],       // 配置参数集合
      data5Source:[],       // 明细清单集合
      data:[],
      dataVisible4:false,   //查询弹窗
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex:99
      },
      menuVisible:false,
      menuData:[],
      OrderNumber:'',
      userName_:'',
      cPdctno_:'',
    }
  },
  // 获取当前登陆账号信息
  computed: {
    ...mapState('account', ['user',]),

  },
  methods: {
    // 服务器管理
    getorderList(orderNum){
      let params = {}
      if(orderNum) {
        params.orderno = orderNum
      }
      this.table1Loading = true
      getCombineManageList(params).then(res => {
        if (res.code == 1) {
          this.data1Source = res.data
        }
      }).finally(
          ()=>{
            this.table1Loading = false
          }
      )
    },
    // 作业信息管理
    getdoOrderList(orderNum){
      let params ={}
      params.UserName = orderNum
      this.table2Loading = true;
      getJobinformation(params).then(res => {
        if (res.code == 1) {
          this.data2Source = res.data || []
        }
      }).finally(
          ()=>{
            this.table2Loading = false
          }
      )
    },
    // 配置参数
    getMachineStatuList(){
      this.table4Loading = true
      getparConfig().then(res => {
        if (res.code == 1) {
          this.data4Source = res.data
        }
      }).finally(()=> {
        this.table4Loading = false
      })
    },
    // 明细清单
    getDrillList(id){
      this.table5Loading = true
      getManageData({'strCPdctno': id}).then(res => {
        if (res.code == 1) {
          this.data5Source = res.data
        }
      }).finally(()=> {
        this.table5Loading = false
      })
    },
    // 服务器管理点击事件配置
    eventTouch(record,index) {
    return {
      props: {},
      on: { // 事件
        click: () => {
          let rowKeys = []
          rowKeys.push(record.userName_)
          this.userName_ = record.userName_
          this.getdoOrderList(record.userName_) // 获取作业信息管理表
          console.log('this.userName_',this.userName_)
        },
      }
    }
    },
    // 作业信息管理点击事件配置
    eventTouch1(record,index) {
    return {
      props: {},
      on: { // 事件
        click: () => {   // 单击
          let rowKeys1 = []
          rowKeys1.push(record.cPdctno_)
          this.cPdctno_ = record.cPdctno_
          this.getDrillList(record.cPdctno_) // 获取明细清单表
        },
        // contextmenu: e => {
        //     e.preventDefault();
        //     this.menuData = record;
        //   },
          contextmenu: e => {
            console.log('走了');
           let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
              console.log('text',text);
            } 
            e.preventDefault();
            this.menuData = record;
            this.rightClick(e, text, record)
          
          },
        // dblclick: () => { //双击
        //   this.getDrillList(record.pdctno_)
        //   this.rowId1 = record.pdctno_;
        //   console.log('this.rowId1',this.rowId1)
        //   this.aPnlOssPath = record.aPnlOssPath;
        //   this.bPnlOssPath = record.bPnlOssPath;
        //   this.sheetOssPath = record.sheetOssPath;
        //   // this.imageLoading =true
        // },
      }
    }
    },

    // 右键事件
    //  bodyClick() {
    //   this.menuVisible = false;
    //   document.body.removeEventListener("click", this.bodyClick);
    // },
    // rightClick(e){
    //   this.menuVisible = true;
    //   console.log()
    //   this.menuStyle.top = e.clientY- 450 +  "px";
    //   this.menuStyle.left = e.clientX -
    //   document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
    //   document.body.addEventListener("click", this.bodyClick);
    // },
    rightClick(e,text,record){ 
       this.text=text;
      if(!this.text){
        this.text = ''
        this.showText = false
      }else{
        this.text = text 
        this.showText = true
      }
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX -
      document.getElementsByClassName('fixed-side')[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },    
    down(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
      },
    // 服务器管理背景设置
    setRowClassName (record)  {
      if (record.userName_ == this.userName_) {
        return 'rowBackgroundColor';
      }
    },
    // 作业信息管理背景设置
    setRowClassName1 (record)  {
      if (record.cPdctno_ && record.cPdctno_ == this.cPdctno_) {
        return 'rowBackgroundColor';
      }
    },

    // 弹窗关闭控制
    reportHandleCancel(){
      this.dataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.dataVisible4 = false;
    },
    quantityChange(payload){
      this.quantity = payload
    },

    //查询
    queryClick(){
      this.dataVisible4= true
    },
    handleOk5(){
      this.dataVisible4= false     
      var OrderNumber = this.$refs.queryInfo.OrderNumber
      var arr1 = OrderNumber.split('')
        if(arr1.length >20){
          arr1 = arr1.slice(0,20)
        }
        OrderNumber = arr1.join('')
        this.$refs.queryInfo.OrderNumber = OrderNumber
      this.getorderList(this.$refs.queryInfo.OrderNumber)
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber)
    },
    keydown(e){
      if (e.key === 'Control') {
      this.isCtrlPressed = true;
      }
      if(e.keyCode=='70' && this.isCtrlPressed ){
        this.reportHandleCancel()
        this.queryClick()
        this.isCtrlPressed = false;
        e.preventDefault()
      }else if(e.keyCode == '13' && this.dataVisible4){
        this.handleOk5()
        this.isCtrlPressed = false;
        e.preventDefault()
      }
    },
    keyup(e){
      if (e.key === 'Control') {
      this.isCtrlPressed = false;
      }  
    },
  },
  mounted() {
    this.getorderList();
    this.getMachineStatuList();
    window.addEventListener('keydown',this.keydown, true)
    window.addEventListener('keyup',this.keyup, true) 
  },
  beforeDestroy(){
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
   },
}
</script>

<style lang="less" scoped>
 /deep/.ant-form-item-label > label{
    color: #000000;
  }
/deep/.ant-input{
  color: #000000;
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}

.JointManagement {
  /deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dcdcdc;
}
 /deep/ .rowBackgroundColor {
    background: #dcdcdc!important;
  }
  user-select: none;
  .content {
    display: flex;
    height: 779px;
  }
  .rowcolor {
      background: #fff9e6;
      -moz-user-select:none;
      -webkit-user-select:none;
      user-select:none;
    }
  .footer {
    .actionBox {
      width: 100%;
      height:48px;
      border: 2px solid #e9e9f0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        form {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background:#fff;
    }
    }
  }
 
  /deep/ .ant-card {
    .ant-card-head {
      // min-height: 0;
      // padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #000000;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
      // .clickRowSty2{
      //   background: #fff9e6;
      // }
      .bacStyle{
        background:#aba5a5 !important;
      }
    }
  }
  .left {

   /deep/ .ant-table-fixed{
      .ant-table-tbody{
        .ant-table-row {
            .orderClass{
              user-select: all;
          }
        }
      }
    }
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    /deep/  .tabRightClikBox{
      //  border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        text-align:center;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color:#000000
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }
  }
  /deep/ .ant-table {
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .ant-table-selection-col {
      width: 30px;
    }
  }
  background: #FFFFFF;
  .minClass {
    /deep/ .ant-table-body {
      min-height: 341px;
    }
  }
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 7px 2px;
        }
      }
    }
    .ant-table-tbody {
      .ant-input {
      padding: 0;
      border: 0;
      border-radius: 0;
      margin: -5px 0;
      height: 27px;
      text-align: center;
    }
      tr {
        td {
          padding: 7px 2px;
        }
      }

    }
  }

  /deep/ .ant-table-body {
      // &::-webkit-scrollbar {
      //   //整体样式
      //   width: 6px; //y轴滚动条粗细
      //   height: 6px;
      // }

      // &::-webkit-scrollbar-thumb {
      //   //滑动滑块条样式
      //   border-radius: 2px;
      //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      //   background: #ff9900;
      //   // #fff9e6
      // }
    .ant-table-thead {
      tr {
        th {
          padding: 7px 2px;
          border-right: 1px solid #efefef;
        }

      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 7px 2px;
          border-right: 1px solid #efefef;
        }
      }
    }
  }
  /deep/ .ant-table-scroll .mouseentered {
    overflow-x: scroll!important;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}

</style>
