<!--品质管理- 作废录入 -->
<template>
    <a-spin :spinning="spinning">
    <div class="orderDetail">  
       <order-action
       @saveClick="saveClick"
       ></order-action>
      <a-tabs default-active-key="1" >       
        <a-tab-pane key="1" tab="基本信息">        
         <order-info  ref="formInfo" v-if= 'updateForm !=null ' 
         :updateForm="updateForm" :StepKeyList="StepKeyList" :flg="flg" @getStepConfigre="getStepConfigre"
         :ConfigreList="ConfigreList" @defectConfigre="defectConfigre"
         ></order-info>
        </a-tab-pane>
        <!-- <a-tab-pane key="2" tab="操作日志"  > 
          <a-table 
          :rowKey="'id'"
          :columns="columns1" 
          :pagination= false        
          :dataSource='viewLogData' 
          :scroll='{y:672}'
          :class="'viewInfo'"
          >          
          </a-table>         
        </a-tab-pane>               -->
      </a-tabs>      
    </div>
    <a-modal    
      title=" 作废录入基本信息确认"
      :visible="dataVisible"
      @cancel="reportHandleCancel"      
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered      
   >
    <template slot="footer">       
        <a-button  type="primary"  @click="reportHandleCancel">取消</a-button>
    </template>
    <div style="height:200px;overflow-y: auto;">
      <div v-for="(ite,index) in message" :key="index" >
        <p>{{ite}}</p>
      </div>
    </div>
   </a-modal> 
    </a-spin>
  </template>
  <script>
  import moment from "moment";
  import {mapState,} from 'vuex';
  import OrderInfo from "@/pages/QualityManagement/VoidEntry/modules/OrderInfo";
  import OrderAction from "@/pages/QualityManagement/VoidEntry/modules/OrderAction";
import {    
    proScrapInfo,
    updateInfo,
    getUpdateInfo,
    stepConfigre,
    defectConfigre,
} from "@/services/scgl/QualityManagement/quality"


const columns1 = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: "center",
      customRender: (text,record,index) => `${index+1}`,
      width: 40,    
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      align: "center",
      width: 100,    
    },
    {
      title: '操作详情',
      dataIndex: 'content',
      align: "center",
      width:700,    
    },
    
  ]
  export default {
    name:"",
    components:{OrderInfo,OrderAction},
    data(){
        return{
            spinning:false,
            columns1,
            viewLogData:[],
            updateForm:{},
            boardBrandList:[],
            dataVisible:false,
            message:[],
            type:'',
            deletId:'',
            StepKeyList:[],
            flg:false,
            ConfigreList:[]
        }
    },
    created() {  
      if(this.$route.query.id){
        this.flg = true
      this.getEidtInfo(this.$route.query.id)     
      }  
      
    }, 
    mounted(){  
      // this.defectConfigre() 
    },
    computed: {
    ...mapState('account', ['user',]),  
  },
    methods: {
      moment,     
      saveClick(){
        let params = this.$refs.formInfo.formInfo
        if(!params.cardNo|| !params.orders || !params.unit|| !params.stepKey  || !params.createStep ){
        if(!params.cardNo){
          this.message.push('请填写工单编号')
        }
        if(!params.orders){
          this.message.push('请选择报废订单')
       }
     
       if(!params.unit){
          this.message.push('请选择报废单位')
        }
     
        if(!params.createStep){
          this.message.push('请填写录入工序')
        } 
        if(!params.stepKey){
          this.message.push('请选择责任工序')
        } 
        this.dataVisible = true;
        return }
        var arry = []
        for(var i=0;i<this.ConfigreList.length;i++){
          if(this.ConfigreList[i].num){
            arry.push(this.ConfigreList[i].num)
          }          
        }       
        if(arry.length){
          params.reason = JSON.stringify(this.ConfigreList)
        }else{
          this.$message.warning('请填写报废数量')
          return
        }
       
        if(this.$route.query.id){
          params.id = this.$route.query.id
          params.num = Number(params.num)
          params.createName = this.user.name 
          params.createAccount = this.user.userName
          console.log('params1',params) 
          updateInfo(params).then(res=>{
            if(res.code){
              this.$message.success('保存成功')

            }else{
              this.$message.error(res.message)
            }
          })
        }
        else{    
          params.num = Number(params.num)      
          params.createName = this.user.name 
          params.createAccount = this.user.userName
          console.log('params',params)        
          proScrapInfo(params).then(res=>{
            if(res.code){
              this.$message.success('保存成功')
              this.$refs.formInfo.formInfo={}
              this.ConfigreList = []
              
            }else{
              this.$message.error(res.message)
            }
          })
        }
        
      },
      handleOk(){
      this.dataVisible=false;
      this.message =[]
    },
    reportHandleCancel(){
      this.dataVisible = false; 
      this.message =[]
    },
      // 编辑
    async  getEidtInfo(id){        
      await  getUpdateInfo(id).then(res=>{
          console.log('id',id)
          if(res.code){
            this.updateForm = res.data
          }else{
            this.$message.error(res.message)
          }
        })
      },
      // 获取责任工序选项参数
     getStepConfigre(PinBanNo,CardNo){
      console.log('责任工序',PinBanNo,CardNo)
      stepConfigre(PinBanNo,CardNo).then(res=>{
        this.StepKeyList = res.data
      })
    },
      // 查看日志
      getViewLog(){
        this.spinning = true
        let Id = this.$route.query.id
        cardInfoLogList(Id).then(res => {
          if (res.code){  
              this.viewLogData = res.data
            } else {
              this.$message.error(res.message)
            }
        }).finally(()=>{
          this.spinning = false
        })
      },
      defectConfigre(val){
        let str = val
      defectConfigre(str).then(res=>{
        if(res.code){
          this.ConfigreList = res.data
          for(var a=0;a<this.ConfigreList.length;a++){
            this.ConfigreList[a].num  = ''
          }

          console.log(' this.ConfigreList', this.ConfigreList)
        }
      })
    }
    }
    
  }
</script>
<style scoped lang="less">
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-input{
  font-weight: 500;
}

  .orderDetail {
    padding: 10px;
    background: #ffffff;
    min-width: 1670px;
    /deep/.rowBackgroundColor {
    background: #FFF9E6!important;
  }
    /deep/ .ant-tabs {
      .viewInfo{
        .ant-table-thead{
          .ant-table-align-left{
            text-align: center!important;;
          }
        }
      }
      margin-top: 10px;
      .ant-tabs-bar{
        margin: 0;
        border-bottom: 1px solid #ccc;
        .ant-tabs-nav-wrap {
          .ant-tabs-ink-bar {
            display: none!important;
          }
        }        
        .ant-tabs-tab {
          margin: 0;
          padding: 0 10px;
          border: 1px solid #ccc;
          font-size: 14px;
          height: 34px;
          line-height: 34px;
          border-left: 0;
          font-weight: 500;
          &:nth-child(1){
            border-left: 1px solid #ccc;;
          }
        }
        .ant-tabs-tab-active {
          border-top: 2px solid #f90 !important;
          border-bottom-color:#ffffff ;
          background: #ffffff;
  
        }
  
      }
    }
  }
 
  /deep/.ant-table{
          font-size: 12px!important;
        }
  </style>