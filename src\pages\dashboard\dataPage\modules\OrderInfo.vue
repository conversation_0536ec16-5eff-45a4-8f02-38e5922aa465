<template>
  <div class="contentInfo" >
    <table id="tab" cellpadding="1" cellspacing="1" border="1"  style="text-align:center;width:55%;">
      <tr>
        <th rowspan="2" style='width:25%;'>指标</th>
        <th rowspan="2" style='width:25%;'>项目</th>
        <th colspan="2" style='width:50%;color:#ff9900;'>{{selectDate}}</th>
      </tr>
      <tr>
        <th >金额（万元）</th>
        <th >占总产值比(%)</th>
      </tr>
      <tr>
        <th rowspan="4">产值</th>
        <th >内部CAM销售额</th>
        <th >{{CostAnalysisData.nbSales.summoney}}</th>
        <th >{{CostAnalysisData.nbSales.proportion}}</th>
      </tr>
      <tr>
        <th >外部CAM销售额</th>
        <th >{{CostAnalysisData.wbSales.summoney}}</th>
        <th >{{CostAnalysisData.wbSales.proportion}}</th>
      </tr>
      <tr>
        <th >折款</th>
        <th>{{CostAnalysisData.conversion.summoney}}</th>
        <th >{{CostAnalysisData.conversion.proportion}}</th>
      </tr>
      <tr>
        <th >总CAM销售产值</th>
        <th >{{CostAnalysisData.totalSales.summoney}}</th>
        <th >{{CostAnalysisData.totalSales.proportion}}</th>
      </tr>
      <tr>
        <th rowspan="7">制作单数</th>
        <th >内部CAM前端文件数</th>
        <th >{{CostAnalysisData.nbFileNum.summoney}}</th>
        <th>{{CostAnalysisData.nbFileNum.proportion}}</th>
      </tr>
      <tr>
        <th>内部CAM后端文件数</th>
        <th>{{CostAnalysisData.nbHdFileNum.summoney}}</th>
        <th>{{CostAnalysisData.nbHdFileNum.proportion}}</th>
      </tr>
      <tr>
        <th>外部CAM前端文件数</th>
        <th>{{CostAnalysisData.wbFileNum.summoney}}</th>
        <th>{{CostAnalysisData.wbFileNum.proportion}}</th>
      </tr>
      <tr>
        <th>外部CAM后端文件数</th>
        <th>{{CostAnalysisData.wbHdFileNum.summoney}}</th>
        <th>{{CostAnalysisData.wbHdFileNum.proportion}}</th>
      </tr>
      <tr>
        <th>总制作文件数</th>
        <th>{{CostAnalysisData.totalFileNum.summoney}}</th>
        <th>{{CostAnalysisData.totalFileNum.proportion}}</th>
      </tr>
      <tr>
        <th>平均单价（元/个）</th>
        <th>{{CostAnalysisData.averageprice.summoney}}</th>
        <th>{{CostAnalysisData.averageprice.proportion}}</th>
      </tr>
      <tr>
        <th style='color:#ff9900;' @click="click1(selectDate)">直通率</th>
        <th>{{CostAnalysisData.passThroughRate.summoney}}</th>
        <th>{{CostAnalysisData.passThroughRate.proportion}}</th>
      </tr>
      <tr>
        <th rowspan="3">工程成本</th>
        <th>内部人工成本</th>
        <th>{{CostAnalysisData.nblaborcost.summoney}}</th>
        <th>{{CostAnalysisData.nblaborcost.proportion}}</th>
      </tr>
      <tr>
        <th>外部人工成本</th>
        <th>{{CostAnalysisData.wblaborcost.summoney}}</th>
        <th>{{CostAnalysisData.wblaborcost.proportion}}</th>
      </tr>
      <tr>
        <th>总人工成本</th>
        <th>{{CostAnalysisData.totallaborcost.summoney}}</th>
        <th>{{CostAnalysisData.totallaborcost.proportion}}</th>
      </tr>

      <tr>
        <th rowspan="3">单位人数</th>
        <th>总人数</th>
        <th>{{CostAnalysisData.totalpeoplenum.summoney}}</th>
        <th>{{CostAnalysisData.totalpeoplenum.proportion}}</th>
      </tr>
      <tr>
        <th>人均产出（个/人）</th>
        <th>{{CostAnalysisData.percapitaoutputPcs.summoney}}</th>
        <th>{{CostAnalysisData.percapitaoutputPcs.proportion}}</th>
      </tr>
      <tr>
        <th>人均产出（元/人）</th>
        <th>{{CostAnalysisData.percapitaoutputYuan.summoney}}</th>
        <th>{{CostAnalysisData.percapitaoutputYuan.proportion}}</th>
      </tr>
      <tr>
        <th>工程盈利</th>
        <th>工程毛利额</th>
        <th>{{CostAnalysisData.grossprofit.summoney}}</th>
        <th>{{CostAnalysisData.grossprofit.proportion}}</th>
      </tr>
    </table>
  </div>
</template>

<script>
import {costAnalysis} from "@/services/dataPage";
export default {
  name: "OrderInfo",
  props:['CostAnalysisData','selectDate'],
  data(){
    return {

    }
  },
  computed:{

  },
  methods:{
    click1(params){      
      // window.open("https://emstv.jiepei.com/CommonData/ztkb?month="+params,"_blank").location
    },
  }
}
</script>

<style scoped lang="less">
.contentInfo{
  tr{
    th{
      padding:6px 0;
    }
  }
}
</style>
