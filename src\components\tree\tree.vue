<template>
    <div>
        <a-tree
                checkable
                :tree-data="treeData"
                @select="onSelect"
                @check="onCheck"
            >
        </a-tree>
    </div>
</template>
<script>
export default {
    props:{
       treeData: {
        type: Array,
        default: null
       },
    },
    methods: {
        // 点击树节点触发
        onSelect(info) {
          this.$emit('onSelect',info)
        },
        // 点击复选框触发
        onCheck(info) {
          this.$emit('onCheck',info)
        },
    }
}
</script>
<style lang="less" scoped>

</style>