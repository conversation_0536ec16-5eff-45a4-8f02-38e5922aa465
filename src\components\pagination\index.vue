<template>
  <a-pagination
    class="my-pagination"
    v-model="current"
    :total="total"
    :size="size"
    :showTotal="(total, range)=> `共 ${total} 条`"
    :hideOnSinglePage="hideOnSinglePage"
    :showQuickJumper="showQuickJumper"
    :showSizeChanger="showSizeChanger"
    :pageSize.sync="currentPageSize"
    :pageSizeOptions="pageSizeOptions"
    @change="change"
    @showSizeChange="showSizeChange"></a-pagination>
</template>

<script>
export default {
  name: 'my-pagination',
  props: {
    value: { // 当前页
      type: Number,
    },
    hideOnSinglePage: {
      type: <PERSON>olean,//只有一页时是否隐藏分页器
      default: false,
    },
    pageSize: {//每页条数
      type: Number,
      default: 10,
    },
    pageSizeOptions: {//指定每页可以显示多少条
      type: Array,
      default () {
        return [ '10', '20', '50', '100', ]
      },
    },
    showQuickJumper: {//是否可以快速跳转至某页
      type: Boolean,
      default: true,
    },
    showSizeChanger: {//是否可以改变 pageSize
      type: Boolean,
      default: true,
    },
    simple: {//当添加该属性时，显示为简单分页
      type: Boolean,
      default: false,
    },
    size: {//当为「small」时，是小尺寸分页
      type: String,
      default: '',
    },
    total: {//数据总数
      type: Number,
      default: 1,
    },
  },
  data () {
    return {
      current: this.value,
      currentPageSize: this.pageSize,
    }
  },
  watch: {
    value (val) {
      this.current = val
    },
    pageSize (val) {
      this.currentPageSize = val
    },
  },
  methods: {
    // 页码改变的回调，参数是改变后的页码及每页条数
    change (page, pageSize) {
      this.$emit('change', page, pageSize)
    },
    // pageSize 变化的回调
    showSizeChange (current, size) {
      this.$emit('showSizeChange', current, size)
    },
  },
}
</script>