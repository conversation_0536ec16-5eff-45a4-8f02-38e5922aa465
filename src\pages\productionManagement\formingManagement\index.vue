<!-- 车间管理-成型管理-->
<template>
  <div class="CuttingManagement">
    <a-spin :spinning="spinning">
      <div class="content">
        <div class="left" ref="letfDom" style="width: 65%">
          <a-card :bordered="false" style="height: 390px; border: 1px solid #e9e9f0" @contextmenu.prevent="rightClick($event)">
            <a-table
              rowKey="guid_"
              :columns="columns"
              :dataSource="data1Source"
              :pagination="false"
              :loading="table1Loading"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :customRow="eventTouch"
              :scroll="{ x: '65%', y: 355 }"
              :rowClassName="setRowClassName"
              :class="{ minClass: data1Source.length > 0 }"
            >
              <span slot="routC" slot-scope="tetx, record, index">
                <a-input v-model="record.routC" @blur="saveRout(record, index)" />
              </span>
              <template slot="isczflie" slot-scope="text, record">
                <a-checkbox v-model="record.isczflie"> </a-checkbox>
              </template>
              <template slot="isrouflie" slot-scope="text, record">
                <a-checkbox v-model="record.isrouflie"> </a-checkbox>
              </template>

              <template slot="opr" slot-scope="text, record" v-if="checkPermission('MES.ProductionModule.Forming.FormingIsLocks')">
                <a-button @click="UpFileLock(record)">锁定</a-button>
              </template>
            </a-table>
            <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
              <a-menu-item>
                <a-upload accept=".rou" name="file" :before-upload="beforeUpload" :customRequest="httpRequest0"> 上传上机文件 </a-upload>
              </a-menu-item>
              <a-menu-item @click="down0">下载上机文件</a-menu-item>
              <a-menu-item>
                <a-upload name="file" :customRequest="httpRequest1"> 上传管位文件 </a-upload>
              </a-menu-item>
              <a-menu-item @click="down1">下载管位文件</a-menu-item>
              <a-menu-item>
                <a-upload name="file" :customRequest="httpRequest2"> 上传碎料文件 </a-upload>
              </a-menu-item>
              <a-menu-item @click="down2">下载碎料文件</a-menu-item>
              <a-menu-item @click="down3">下载确认文件</a-menu-item>
              <a-menu-item @click="down4">下载钻孔文件</a-menu-item>
            </a-menu>
          </a-card>
          <a-card :bordered="false" style="height: 390px; border: 1px solid #e9e9f0">
            <a-table
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :columns="columns2"
              :dataSource="data2Source"
              :pagination="false"
              :loading="table2Loading"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :customRow="eventTouch1"
              :scroll="{ x: '65%', y: 355 }"
              :rowClassName="setRowClassName1"
              :class="{ minClass: data2Source.length > 0 }"
            >
              <template slot="isStop_" slot-scope="text, record">
                <a-checkbox v-model="record.isStop_"> </a-checkbox>
              </template>
            </a-table>
          </a-card>
        </div>
        <div class="right" style="width: 35%">
          <center
            :tableData="data3Source"
            :table3Loading="table3Loading"
            :machineStatuList="data4Source"
            :machineStatuLoad="table4Loading"
            :drillLoad="table5Loading"
            :drillList="data5Source"
            :dispatchLoad="table6Loading"
            :dispatchList="data6Source"
            @getAssignmentList="getAssignmentList"
            @getDispatchFallback="getDispatchFallback"
            @childClick="childClick"
            @getDrillList="getDrillList"
            ref="cuttingCenter"
            :idList="idList"
            @getMesaList="getMesaList"
          ></center>
        </div>
      </div>
      <div class="footer">
        <div class="actionBox">
          <action
            @UpFileLock="UpFileLock"
            @handleDispatchMachine="handleDispatchMachine"
            @Scancodetodispatch="Scancodetodispatch"
            @OverOrderClick="OverOrderClick"
            @UploadOrderClick="UploadOrderClick"
            @harmomegathusRegisterClick="harmomegathusRegisterClick"
            @SetUpExpeditingClick="SetUpExpeditingClick"
            @DeleteOrderClick="DeleteOrderClick"
            @ExceptionRemarksClick="ExceptionRemarksClick"
            @DataCheckClick="DataCheckClick"
            @queryClick="queryClick"
            @operationClick1="operationClick1"
            @operationClick2="operationClick2"
            @operationClick3="operationClick3"
            :btnloading1="btnloading1"
            :btnloading2="btnloading2"
            :btnloading3="btnloading3"
            :btnloading4="btnloading4"
            :btnloading5="btnloading5"
            :btnloading6="btnloading6"
            :btnloading7="btnloading7"
          ></action>
        </div>
      </div>
      <!--扫码分派-->
      <a-modal
        title="扫码分派"
        :visible="scancodeVisible"
        @cancel="scancodeVisible = false"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :destroyOnClose="true"
        centered
      >
        <a-form-item label="扫码分派" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
          <a-input v-model="scancode" :autoFocus="true" @keyup.enter="scancodeOk" />
        </a-form-item>
        <template #footer>
          <a-button key="back" @click="scancodeVisible = false">取消</a-button>
        </template>
      </a-modal>
      <!-- 部门过序弹窗 -->
      <a-modal
        title="部门过序"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        :destroyOnClose="true"
      >
        <over-order-info
          @keyupEnter="keyupEnter"
          :quantity="quantity"
          @quantityChange="quantityChange"
          :quantityCopy="quantityCopy"
          ref="overOrder"
        />
      </a-modal>
      <!-- 上传订单弹窗 -->
      <a-modal
        title="录入订单"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <enter-order-info ref="enterOderForm" />
      </a-modal>
      <!-- 涨缩登记弹窗 -->
      <a-modal
        title="涨缩登记"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
      >
        <harmomegathus-register ref="harmomeForm"></harmomegathus-register>
      </a-modal>
      <!-- 异常备注弹窗 -->
      <a-modal
        title="异常备注"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <exception-remarks-info ref="exceptionRemarksInfo" />
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk5"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        centered
      >
        <query-info ref="queryInfo" @keyupEnter1="keyupEnter1" />
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import { processorder } from "@/services/management";
import {
  getDispatchList,
  getDoingOrderList,
  getDrillHoleList,
  getMachineList,
  getStatisticsList,
  getWaitOrderList,
  getDispatchMachineList,
  getDispatchFallbackList,
  getpassStepNum,
  OverSequence,
  UploadOrder,
  getHarmomegathusRegister,
  SetUpExpediting,
  DeleteOrder,
  ExceptionRemarks,
  DataCheck,
  CallTrolley,
  Confirm,
  AgvCancel,
  updateRout,
  getOrderMuIdList,
  downLoad,
  UploadFile,
  orderFileUpload,
  UploadFile1,
  FileLock,
} from "@/services/forming-management";
import Center from "./module/Center";
import Action from "./module/Action";
import OverOrderInfo from "./module/OverOrderInfo";
import EnterOrderInfo from "./module/EnterOrderInfo";
import HarmomegathusRegister from "./module/HarmomegathusRegister";
import ExceptionRemarksInfo from "./module/ExceptionRemarksInfo";
import QueryInfo from "./module/QueryInfo";
import { checkPermission } from "@/utils/abp";
import { download } from "@/utils/util";
const columns = [
  {
    dataIndex: "index",
    title: "序号",
    slots: { title: "customTitle" },
    key: "index",
    width: 40,
    align: "center",
    fixed: "left",
    scopedSlots: { customRender: "index" },
    customRender: (text, record, index) => `${index + 1}`,
    customCell: (record, rowIndex) => {
      if (record.color_ == "#FF0000") {
        return { style: { background: "#FF0000" } };
      }
    },
  },
  {
    title: "拼板编号",
    dataIndex: "orderNo",
    width: 120,
    ellipsis: true,
    className: "orderClass",
    align: "center",
    fixed: "left",
  },
  {
    title: "板材类别",
    width: 60,
    dataIndex: "fR4Type",
    ellipsis: true,
    align: "center",
  },
  {
    title: "子单数",
    width: 50,
    ellipsis: true,
    dataIndex: "cardNumber",
    align: "center",
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "PNL数",
    dataIndex: "totalNum",
    width: 55,
    ellipsis: true,
    align: "center",
  },
  {
    title: "面积",
    width: 60,
    dataIndex: "area_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "锣程",
    dataIndex: "routC",
    width: 60,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "routC" },
  },
  {
    title: "机台数",
    dataIndex: "machineCount_",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "工序",
    dataIndex: "craftKey_",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "订单工厂",
    width: 100,
    dataIndex: "orderFactoryKey",
    ellipsis: true,
    align: "center",
  },
  {
    title: "逾期天数",
    width: 80,
    dataIndex: "beOverdue",
    ellipsis: true,
    align: "center",
  },
  {
    title: "交货日期",
    width: 100,
    ellipsis: true,
    dataIndex: "deliveryDate",
    align: "center",
  },
  {
    title: "参考文件",
    width: 80,
    dataIndex: "isczflie",
    align: "center",
    scopedSlots: { customRender: "isczflie" },
  },
  {
    title: "锣带文件",
    width: 80,
    dataIndex: "isrouflie",
    align: "center",
    scopedSlots: { customRender: "isrouflie" },
  },
  {
    title: "异常备注",
    width: 80,
    dataIndex: "note_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "拉伸状态",
    width: 70,
    dataIndex: "stretchState_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "到序时间",
    width: 100,
    dataIndex: "createTime",
    ellipsis: true,
    align: "center",
  },
  {
    title: "机台派单",
    width: 100,
    dataIndex: "startDate_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "加工完成时间",
    width: 100,
    dataIndex: "finishTime",
    ellipsis: true,
    align: "center",
  },
  {
    title: "暂停",
    width: 45,
    dataIndex: "isStop_",
    scopedSlots: { customRender: "isStop_" },
    align: "center",
  },
  {
    title: "操作",
    width: 60,
    align: "center",
    // fixed: 'right',
    scopedSlots: { customRender: "opr" },
  },
];
const columns2 = [
  {
    dataIndex: "index",
    title: "序号",
    slots: { title: "customTitle" },
    key: "index",
    width: 40,
    align: "center",
    fixed: "left",
    scopedSlots: { customRender: "index" },
    customRender: (text, record, index) => `${index + 1}`,
    customCell: (record, rowIndex) => {
      if (record.color_ == "#FF0000") {
        return { style: { background: "#FF0000" } };
      }
    },
  },
  {
    title: "拼板编号",
    dataIndex: "orderNo",
    width: 120,
    ellipsis: true,
    className: "orderClass",
    align: "center",
    fixed: "left",
  },
  {
    title: "板材类别",
    width: 60,
    dataIndex: "fR4Type_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "子单数",
    width: 50,
    dataIndex: "cardNumber",
    ellipsis: true,
    align: "center",
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "宽",
    dataIndex: "wSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "PNL数",
    dataIndex: "totalNum",
    width: 55,
    ellipsis: true,
    align: "center",
  },
  {
    title: "面积",
    width: 60,
    ellipsis: true,
    dataIndex: "area_",
    align: "center",
  },
  {
    title: "锣程",
    dataIndex: "routC",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "机台数",
    dataIndex: "machineCount_",
    ellipsis: true,
    width: 60,
    align: "center",
  },
  {
    title: "工序",
    dataIndex: "craftKey_",
    ellipsis: true,
    width: 60,
    align: "center",
  },
  {
    title: "订单工厂",
    width: 100,
    ellipsis: true,
    dataIndex: "orderFactoryKey",
    align: "center",
  },
  {
    title: "逾期天数",
    width: 80,
    ellipsis: true,
    dataIndex: "beOverdue",
    align: "center",
  },
  {
    title: "交货日期",
    width: 100,
    ellipsis: true,
    dataIndex: "deliveryDate",
    align: "center",
  },
  {
    title: "到序时间",
    width: 100,
    ellipsis: true,
    dataIndex: "createTime",
    align: "center",
  },
  {
    title: "派单时间",
    width: 100,
    dataIndex: "startDate_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "预计耗时（分）",
    width: 100,
    dataIndex: "yjTime_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "暂停",
    width: 45,
    dataIndex: "isStop_",
    scopedSlots: { customRender: "isStop_" },
    align: "center",
  },
];

export default {
  name: "CuttingManagement",
  components: { Action, Center, OverOrderInfo, EnterOrderInfo, HarmomegathusRegister, ExceptionRemarksInfo, QueryInfo },
  inject: ["reload"],
  data() {
    return {
      spinning: false,
      columns,
      columns2,
      loading: false,
      table1Loading: false, // 待分派表格load
      table2Loading: false, // 已分派表格load
      table3Loading: false, // 机台表格load
      table4Loading: false, // 机台状态load
      table5Loading: false, // 钻刀表格load
      table6Loading: false, // 分派状态load
      data1Source: [], // 待分派集合
      data2Source: [], // 已分派集合
      data3Source: [], // 机台集合
      data4Source: [], // 机台状态集合
      data5Source: [], // 钻刀集合
      data6Source: [], // 分派集合
      copyData1Source: [],
      copyData6Source: [],
      selectedRowList: [],
      selectedRowList1: [],
      selectedRows: [],
      assignMachineList: [],
      rowId1: "",
      dataVisible: false, //部门过序弹窗不显示
      dataVisible1: false, //上传订单弹窗不显示
      dataVisible2: false, //涨缩登记弹窗不显示
      dataVisible3: false, //异常备注弹窗不显示
      dataVisible4: false, //查询弹窗
      quantity: "",
      quantityCopy: "",
      cardNo: "",
      confirmLoading: false,
      note: "",
      idList: [],
      routC: "",
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      menuVisible: false,
      menuData: [],
      btnloading1: false, // 分派按钮loading
      btnloading2: false, // 设置加急按钮loading
      btnloading3: false, // 数据核对按钮loading
      btnloading4: false, // 删除订单按钮loading
      btnloading5: false, // 呼叫小车按钮loading
      btnloading6: false, // 人员确认按钮loading
      btnloading7: false, // 取消小车按钮loading
      agvID: "",
      type: "",
      scancodeVisible: false,
      scancode: "",
      // letfDom: null,
      // clientStartX: 0
    };
  },
  methods: {
    checkPermission,
    // 待上机列表
    getorderList(orderNum) {
      let params = {};
      params.process = "CHENGXING";
      if (orderNum) {
        params.Pdctno = orderNum;
      }
      this.table1Loading = true;
      getWaitOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.copyData1Source = res.data || [];
            this.data1Source = JSON.parse(JSON.stringify(res.data)) || [];
          }
        })
        .finally(() => {
          this.table1Loading = false;
        });
    },
    // 已上机列表
    getdoOrderList(orderNum) {
      let params = {};
      params.process = "CHENGXING";
      if (orderNum) {
        params.Pdctno = orderNum;
      }
      this.table2Loading = true;
      getDoingOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data2Source = res.data || [];
          }
        })
        .finally(() => {
          this.table2Loading = false;
        });
    },
    // 机台列表
    getMesaList() {
      this.table3Loading = true;
      getMachineList({ type: 1 })
        .then(res => {
          if (res.code == 1) {
            this.data3Source = res.data;
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    // 状态统计列表
    getMachineStatuList() {
      this.table4Loading = true;
      getStatisticsList({ craftKey: "CHENGXING" })
        .then(res => {
          if (res.code == 1) {
            this.data4Source = res.data;
          }
        })
        .finally(() => {
          this.table4Loading = false;
        });
    },
    // 锣刀列表
    getDrillList(id) {
      this.table5Loading = true;
      // console.log(id)
      getDrillHoleList({ pdctno: id })
        .then(res => {
          if (res.code == 1) {
            this.data5Source = res.data;
          }
        })
        .finally(() => {
          this.table5Loading = false;
        });
    },
    // 机台上列表
    getAssignmentList(id) {
      this.table6Loading = true;
      getDispatchList(id)
        .then(res => {
          if (res.code == 1) {
            // this.copyData6Source = res.data || []
            // this.data6Source = JSON.parse(JSON.stringify(res.data)) || []
            this.data6Source = res.data;
          }
        })
        .finally(() => {
          this.table6Loading = false;
        });
    },
    // 选择分配订单
    // onSelectChange(selectedRowKeys, selectedRows) {
    //   this.selectedRowList = selectedRowKeys;
    //   this.selectedRows=selectedRows
    //   // console.log("待分配订单",this.selectedRows)
    // },

    // 锁定
    UpFileLock(record) {
      console.log(record);
      if (confirm("确定文件锁定吗？")) {
        this.spinning = true;
        FileLock(record.guid_)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("文件锁定成功");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    //扫码分派
    Scancodetodispatch() {
      if (this.$refs.cuttingCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      this.scancodeVisible = true;
    },
    scancodeOk() {
      processorder(this.$refs.cuttingCenter.selectedRowKeys, 1, this.scancode).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.scancode = "";
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 分派
    handleDispatchMachine() {
      if (this.selectedRowList.length <= 0) {
        this.$message.warning("请选择待分派订单");
        return;
      } else if (this.$refs.cuttingCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      // console.log(this.$refs.cuttingCenter.selectedRowKeys)  // 这个是机台ID 集合
      // console.log(this.data3Source)  // 这个是机台集合
      // 在机台集合数据里面筛选机台集合里面对应ID的机台详细信息。去做判断  如果负责人为空不允许分派，
      var id_arr = this.$refs.cuttingCenter.selectedRowKeys;
      var arr = this.data3Source;
      var str_arr = [];
      id_arr.forEach(item => {
        if (
          !arr.find(itm => {
            return itm.iD_ == item;
          }).listParams_
        ) {
          str_arr.push(
            arr.find(itm => {
              return itm.iD_ == item;
            }).caption_
          );
        }
      });
      if (str_arr.length > 0) {
        this.$message.warning(str_arr.join("、") + "负责人为空不允许分派");
        return;
      }
      this.btnloading1 = true;
      this.spinning = true;
      // 获取待分派订单id和机台id
      let assignmentData = {
        id: this.selectedRowList[0],
        equId: this.$refs.cuttingCenter.selectedRowKeys,
      };
      getDispatchMachineList(assignmentData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("分派成功");
            this.data1Source.splice(
              this.data1Source.findIndex(item => item.guid_ == this.selectedRowList[0]),
              1
            );
            this.selectedRowList = [];
            this.$refs.cuttingCenter.selectedRowKeys.forEach(ite => {
              this.data3Source.filter(item => item.iD_ == ite)[0].numMachine_ += 1;
            });
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.btnloading1 = false;
          this.spinning = false;
        });
    },
    // 分派回退
    getDispatchFallback(record) {
      //console.log('分派回退',record)
      let params = {};
      params.id = record.guid_;
      if (confirm("确定回退吗？")) {
        getDispatchFallbackList(params)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("分派回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.getAssignmentList(record.iD_);
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    eventTouch(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowList = keys;
            this.$refs.cuttingCenter.flagClick();
            this.getDrillList(record.pdctno_);
            this.rowId1 = record.pdctno_;
            this.$refs.cuttingCenter.rowId = "";
            // getOrderMuIdList(record.guid_).then(res =>{
            //   if(res.code == 1){
            //     this.idList = res.data
            //   }else{
            //     this.$message.error(res.message)
            //   }
            //   console.log(this.idList )
            // })
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
          // dblclick: () => { //双击
          //   this.$refs.cuttingCenter.flagClick()
          //   this.getDrillList(record.pdctno_)
          //   this.rowId1 = record.pdctno_;
          //   this.$refs.cuttingCenter.rowId = '' ;
          //   getOrderMuIdList(record.guid_).then(res =>{
          //     if(res.code == 1){
          //       this.idList = res.data
          //     }else{
          //       this.$message.error(res.message)
          //     }
          //     console.log(this.idList )
          //   })
          // },
        },
      };
    },
    eventTouch1(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowList1 = keys;
            this.$refs.cuttingCenter.flagClick();
            this.getDrillList(record.pdctno_);
            this.rowId1 = record.pdctno_;
            this.$refs.cuttingCenter.rowId = "";
            getOrderMuIdList(record.guid_).then(res => {
              if (res.code == 1) {
                this.idList = res.data;
              } else {
                this.$message.error(res.message);
              }
              console.log(this.idList);
            });
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          },
          // dblclick: () => { //双击
          //   this.$refs.cuttingCenter.flagClick()
          //   this.getDrillList(record.pdctno_)
          //   this.rowId1 = record.pdctno_;
          //   this.$refs.cuttingCenter.rowId = '' ;
          //   getOrderMuIdList(record.guid_).then(res =>{
          //     if(res.code == 1){
          //       this.idList = res.data
          //     }else{
          //       this.$message.error(res.message)
          //     }
          //     console.log(this.idList )
          //   })
          // },
        },
      };
    },
    // 已上机列表背景设置
    setRowClassName(record) {
      // console.log(record)
      // console.log('this.selectedRowList1',this.selectedRowList1)
      var classStr = "";
      // 单击设置背景色
      if (record.guid_ == this.selectedRowList) {
        classStr = classStr + "bacStyle" + " ";
      }
      // 双击选中背景色
      // if(record.pdctno_ == this.rowId1 ){
      //   classStr =  'clickRowSty2'
      // }
      return classStr;
    },
    setRowClassName1(record) {
      var classStr = "";
      // 单击设置背景色
      if (record.guid_ == this.selectedRowList1) {
        classStr = classStr + "bacStyle" + " ";
      }
      // 双击选中背景色
      // if(record.pdctno_ == this.rowId1 ){
      //   classStr =  'clickRowSty2'
      // }
      return classStr;
    },
    // 待上机列表背景设置
    // setRowClassName1(record){
    //   if(record.color_ == '#FF0000'){
    //     return 'redStyle'
    //   }
    // },

    childClick() {
      this.rowId1 = "";
    },
    // 弹窗关闭控制
    reportHandleCancel() {
      this.dataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.dataVisible4 = false;
    },
    quantityChange(payload) {
      this.quantity = payload;
    },
    // 部门过序
    OverOrderClick() {
      this.dataVisible = true;
    },
    // 获取部门过序数量
    async keyupEnter(cardNo) {
      this.confirmLoading = true;
      let params = {
        cardNo: cardNo,
      };
      await getpassStepNum(params).then(res => {
        if (res.code == 1) {
          this.cardNo = cardNo;
          this.quantity = res.data;
          this.quantityCopy = JSON.parse(JSON.stringify(res.data));
          this.$message.success("获取过序数量成功");
        } else {
          this.$message.error(res.message);
        }
        this.confirmLoading = false;
      });
    },
    handleOk1() {
      this.confirmLoading = true;
      this.spinning = true;
      // 钻孔过序
      let paramsData = {
        cardNo: this.cardNo,
        num: this.quantity,
        craftKey: "CHENGXING",
      };
      console.log("部门过序传参", paramsData);
      OverSequence(paramsData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("过序成功");
          } else {
            this.$message.error(res.message);
          }
          this.getorderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible = false;
        });
    },
    // 上传订单
    UploadOrderClick() {
      this.dataVisible1 = true;
    },
    handleOk2() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = this.$refs.enterOderForm.enterOrderForm;
      params.craftKey = "CHENGXING";
      console.log(params);
      UploadOrder(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
          this.getorderList();
        })
        .finally(() => {
          this.spinning = false;
          this.dataVisible1 = false;
          this.confirmLoading = false;
        });
    },
    // 涨缩登记
    harmomegathusRegisterClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.dataVisible2 = true;
    },
    handleOk3() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = {
        id: this.selectedRowList[0],
        x: this.$refs.harmomeForm.harmomeForm.valuex,
        y: this.$refs.harmomeForm.harmomeForm.valuey,
        CraftKey: "CHENGXING",
      };
      // console.log('params',params)
      getHarmomegathusRegister(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("登记成功");
          } else {
            this.$message.error(res.message);
          }
          this.getorderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible2 = false;
        });
    },

    // 设置加急
    SetUpExpeditingClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.btnloading2 = true;
      this.spinning = true;
      SetUpExpediting(this.selectedRowList[0])
        .then(res => {
          if (res.code == 1) {
            this.$message.success("设置成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading2 = false;
          this.spinning = false;
        });
    },
    // 删除订单
    DeleteOrderClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.btnloading4 = true;
      this.spinning = true;
      DeleteOrder(this.selectedRowList[0])
        .then(res => {
          if (res.code == 1) {
            this.$message.success("设置成功");
          } else {
            this.$message.error(res.message);
          }
          this.getorderList();
        })
        .finally(() => {
          this.btnloading4 = false;
          this.spinning = false;
        });
    },
    // 异常备注
    ExceptionRemarksClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.dataVisible3 = true;
    },
    handleOk4() {
      this.confirmLoading = true;
      this.spinning = true;
      let note = this.$refs.exceptionRemarksInfo.note;
      ExceptionRemarks(this.selectedRowList[0], { note: note })
        .then(res => {
          if (res.code == 1) {
            this.$message.success("备注成功");
          } else {
            this.$message.error(res.message);
          }
          this.getorderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible3 = false;
        });
    },
    //数据核对
    DataCheckClick() {
      this.btnloading3 = true;
      this.spinning = true;
      DataCheck()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("数据核对");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading3 = false;
          this.spinning = false;
        });
    },
    //查询
    queryClick() {
      this.dataVisible4 = true;
    },
    keyupEnter1() {
      this.dataVisible4 = false;
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    handleOk5() {
      this.dataVisible4 = false;
      console.log(this.$refs.queryInfo.OrderNumber);
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    // 呼叫小车
    operationClick1() {
      this.btnloading5 = true;
      this.spinning = true;
      CallTrolley()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("呼叫成功");
            this.agvID = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading5 = false;
          this.spinning = false;
        });
    },
    // 人员确认
    operationClick2() {
      this.btnloading6 = true;
      this.spinning = true;
      Confirm()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("人员已确认");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading6 = false;
          this.spinning = false;
        });
    },
    // 取消小车
    operationClick3() {
      this.btnloading7 = true;
      this.spinning = true;
      AgvCancel()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("取消成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading7 = false;
          this.spinning = false;
        });
    },
    // 修改锣程
    saveRout(record, index) {
      if (record.routC != this.copyData1Source[index].routC) {
        updateRout(record.guid_, { RoutC: record.routC }).then(res => {
          console.log("record:", record);
          if (res.code == 1) {
            this.$message.success("成功");
            this.copyData1Source = this.data1Source;
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },

    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      console.log();
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 限制文件上传格式
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".rou") != -1;
      if (!isFileType) {
        this.$message.error("只支持.rou格式文件");
      }
      return isFileType;
    },
    // 0上传上机文件
    async httpRequest0(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });
      if (resData.code == 1) {
        orderFileUpload(this.menuData.guid_, { path: resData.data, type: 0 }).then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 1上传管位文件
    async httpRequest1(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });
      if (resData.code == 1) {
        console.log;
        orderFileUpload(this.menuData.guid_, { path: resData.data, type: 1 }).then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 2上传碎料文件
    async httpRequest2(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });
      if (resData.code == 1) {
        console.log;
        orderFileUpload(this.menuData.guid_, { path: resData.data, type: 2 }).then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 0下载上机文件
    down0() {
      downLoad(this.menuData.guid_, { type: 0 }).then(res => {
        if (res.code == 1) {
          window.location.href = res.data.wPath;
          //  let files = Object.values(res.data)  // 所有文件
          //  files.forEach(url => {
          //   if(url) {
          //      download(url,url,{})
          //   }
          // })
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 1下载管位文件
    down1() {
      downLoad(this.menuData.guid_, { type: 1 }).then(res => {
        if (res.code == 1) {
          window.location.href = res.data.tubePath;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 2下载碎料文件
    down2() {
      downLoad(this.menuData.guid_, { type: 2 }).then(res => {
        if (res.code == 1) {
          window.location.href = res.data.crushedPath;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 3下载确认文件
    down3() {
      downLoad(this.menuData.guid_, { type: 3 }).then(res => {
        if (res.code == 1) {
          window.location.href = res.data.btfPath;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 4下载钻孔文件
    down4() {
      downLoad(this.menuData.guid_, { type: 4 }).then(res => {
        if (res.code == 1) {
          window.location.href = res.data.hpFilePathDrl;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
  watch: {},
  mounted() {
    this.getorderList();
    this.getdoOrderList();
    this.getMesaList();
    this.getMachineStatuList();
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}

.CuttingManagement {
  user-select: none;
  min-width: 1670px;
  .content {
    display: flex;
    height: 780px;
  }
  .rowcolor {
    background: #fff9e6;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
  }
  .footer {
    .actionBox {
      height: 48px;
      overflow: hidden;
      width: 100%;
      border: 2px solid #e9e9f0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      form {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
      .clickRowSty2 {
        background: #fff9e6;
      }
      .bacStyle {
        background: #aba5a5;
      }
    }
  }
  .left {
    /deep/ .ant-table-body {
      .ant-table-tbody {
        .bacStyle {
          .ant-input {
            background-color: #aba5a5 !important;
          }
        }
        .bacStyle.ant-table-row-hover {
          .ant-input {
            background-color: #aba5a5 !important;
          }
        }
        .bacStyle.ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
        .ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
        .ant-table-row-hover {
          .ant-input {
            background-color: #aba5a5;
          }
        }
      }
    }
    /deep/ .ant-table-fixed {
      .ant-table-tbody {
        .bacStyle.ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
        .ant-table-row {
          .orderClass {
            user-select: all;
          }
        }
        .ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
      }
    }
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    /deep/ .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
    button {
      padding: 0 5px;
      border-radius: 6px;
      height: 24px;
      line-height: 24px;
      background-color: #ff9900;
      color: white;
    }
  }
  /deep/ .ant-table {
    .ant-table-selection-col {
      width: 30px;
    }
  }
  background: #ffffff;
  .minClass {
    /deep/ .ant-table-body {
      min-height: 355px;
    }
  }
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 5px 0;
        }
      }
    }
    .ant-table-tbody {
      .ant-input {
        padding: 0;
        border: 0;
        border-radius: 0;
        margin: -5px 0;
        text-align: center;
      }
      tr {
        td {
          padding: 5px 5px;
        }
      }
    }
  }

  /deep/ .ant-table-body {
    // &::-webkit-scrollbar {
    //   //整体样式
    //   width: 6px; //y轴滚动条粗细
    //   height: 6px;
    // }

    // &::-webkit-scrollbar-thumb {
    //   //滑动滑块条样式
    //   border-radius: 2px;
    //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    //   background: #ff9900;
    //   // #fff9e6
    // }
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 0;
        }
      }
    }
  }
  /deep/ .ant-table-scroll .mouseentered {
    overflow-x: scroll !important;
  }
}
</style>
