<!--JZ报价单  -->
<template>
  <div class="pdfDom1" style="font-size: 12px">
    <a-button v-print="printObj1" v-if="selectlength >= 2" type="primary" class="printstyle" @click="printpdf">打印</a-button>
    <a-button v-print="printObj" v-else @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="jzreport1" style="padding: 25px; font-family: '宋体'; color: black" v-if="selectlength >= 2">
      <div style="width: 100%; display: flex">
        <div style="text-align: center; width: 100%">
          <img src="@/assets/img/jz.png" style="height: 50px" />
          <div>
            <span style="font-size: 12px">地址:广东省深圳市宝安区松岗镇江边工业园创业三路82-83号</span> <br />
            <span style="font-size: 12px">Add:No.82-83,3rd <PERSON><PERSON>ye RowsData,Jiangbian1,Songgang,Bao'An,Shenzhen,China</span> <br />
            <span style="font-size: 12px">Tel:0755-784541221 Fax:0755-965412 Postcode:518105 Website:www.szjzpcb.com.cn</span> <br />
            <span style="font-size: 22px; font-weight: bold">QUOTATION报价单</span><br />
          </div>
        </div>
      </div>
      <div style="display: flex; line-height: 3ch; font-weight: bold; font-size: 14px">
        <div style="width: 50%; z-index: 99">
          <div>{{ JZreportdata.party_ }}</div>
          <div>{{ JZreportdata.factory_ }}</div>
          <div>Model Number/型号：{{ JZreportdata.pcbFileName }}</div>
        </div>
        <div style="z-index: 99; width: 50%">
          <div style="float: right">
            <div>{{ JZreportdata.orderNo_ }}</div>
            <div>{{ JZreportdata.date_ }}</div>
            <div>{{ JZreportdata.currency_ }}</div>
          </div>
        </div>
      </div>
      <div>
        <table border="1" style="text-align: center; margin-top: 5px; border-color: black">
          <thead>
            <tr style="font-weight: bold">
              <td style="width: 110px">No<br />序号</td>
              <td style="width: 110px">PN<br />产品型号</td>
              <td style="width: 110px">Panel Size<br />成品尺寸</td>
              <td style="width: 110px">PCS/<br />SET</td>
              <td style="width: 110px">Area<br />面积</td>
              <td style="width: 110px">QTY<br />数量</td>
              <td style="width: 110px">UNIT<br />单位</td>
              <td style="width: 120px">Price perunit<br />单价</td>
              <td style="width: 110px">Engineer fee<br />工程费</td>
              <td style="width: 110px">Surface fee<br />表面费</td>
              <td style="width: 120px">Quick charge<br />加急费</td>
              <td style="width: 120px">Flying probe<br />飞测费</td>
              <td style="width: 110px">ET<br />测试架</td>
              <td style="width: 110px">Other Fee<br />其他费</td>
              <td style="width: 110px">Total<br />总金额</td>
              <td style="width: 110px">Lead time<br />交期</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in JZreportdata.price" :key="index">
              <td>{{ item.no }}</td>
              <td>{{ item.custName }}</td>
              <td>{{ item.size }}</td>
              <td>{{ item.su }}</td>
              <td>{{ item.area }}</td>
              <td>{{ item.qty }}</td>
              <td>{{ item.bType }}</td>
              <td>{{ item.pcs }}</td>
              <td>{{ item.eng }}</td>
              <td>{{ item.surface }}</td>
              <td>{{ item.urgent }}</td>
              <td>{{ item.fly }}</td>
              <td>{{ item.test }}</td>
              <td>{{ item.other }}</td>
              <td>{{ item.total }}</td>
              <td>{{ item.custdate }}</td>
            </tr>
          </tbody>
        </table>
        <div style="padding-top: 15px; font-weight: bold">PCB Specifications / 制版工艺</div>
        <table border="1" style="text-align: center; margin-top: 5px; border-color: black">
          <thead>
            <tr style="font-weight: bold">
              <td style="width: 110px">No<br />序号</td>
              <td style="width: 110px">Order Type<br />订单类型</td>
              <td style="width: 110px">Layer<br />层数</td>
              <td style="width: 110px">board thickness<br />板厚</td>
              <td style="width: 110px">Material<br />板材</td>
              <td style="width: 110px">TG<br />板材TG值</td>
              <td style="width: 110px">Solder Mask Color<br />阻焊</td>
              <td style="width: 120px">Screen Printing Color<br />文字</td>
              <td style="width: 110px">Finish process<br />表面工艺</td>
              <td style="width: 110px">受镀面积</td>
              <td style="width: 120px">Finished copper thickness<br />成品铜厚</td>
              <td style="width: 120px">Min Hole Copper<br />最小孔铜</td>
              <td style="width: 110px">Hole Density<br />孔密度</td>
              <td style="width: 110px">Test Points<br />测试点数</td>
              <td style="width: 110px">Test Mode<br />测试方式</td>
              <td style="width: 110px">Outline<br />成型方式</td>
            </tr>
          </thead>
          <tbody v-for="(item, index) in JZreportdata.orderPar" :key="index">
            <tr>
              <td>{{ item.no }}</td>
              <td>{{ item.reOrder }}</td>
              <td>{{ item.boardLayers }}</td>
              <td>{{ item.boardThickness }}</td>
              <td>{{ item.fR4Type }}</td>
              <td>{{ item.fR4Tg }}</td>
              <td>{{ item.solderColor }}</td>
              <td>{{ item.fontColor }}</td>
              <td>{{ item.surfaceFinish }}</td>
              <td>{{ item.area_ }}</td>
              <td>{{ item.cuThickness }}</td>
              <td>{{ item.minHoleCopper }}</td>
              <td>{{ item.poreDensity }}</td>
              <td>{{ item.testPointNum }}</td>
              <td>{{ item.flyingProbe }}</td>
              <td>{{ item.formingType }}</td>
            </tr>
            <tr style="text-align: left">
              <td colspan="16">
                <span style="font-weight: bold">PCB Special requirement / 特殊要求:</span> <br /><span style="padding-left: 3ch">{{
                  item.remark_
                }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="z-index: 99; position: relative">
        <div style="display: flex; line-height: 3ch; margin-top: 8px; font-weight: bold">
          <div>
            <div>备注/Remark:</div>
            <div>1.我们保留根据最终gerber重新报价的权利</div>
            <div>We reserve the right to requote according to the gerber file .</div>
            <div>2.报价有效期30天./validity : 30days.</div>
            <div>3.按IPC-600-G Ⅱ标准报价./ Quote according to IPC-600-G Ⅱstandard</div>
          </div>
        </div>
      </div>
      <div style="float: right; font-weight: bold">表格编号:JZ-QR-DCC-243C</div>
    </div>
    <div id="jzreport2" style="padding: 25px; font-family: '宋体'; color: black" v-if="selectlength == 1">
      <div style="width: 100%; display: flex">
        <!-- <img   src="@/assets/img/jzlogo.png" style="height: 70px;position: relative;left: 70px;">  -->
        <div style="text-align: center; width: 100%">
          <img src="@/assets/img/jz.png" style="height: 50px" />
          <!-- <span style="font-size: 30px;letter-spacing:9px;font-weight: bold;">深圳市精焯电路科技有限公司</span><br/>
                        <span style="font-size: 22px;font-weight: bold;">SHENZHEN JINGZHUO CIRCUIT TECHNNOLOGY CO.,LTD</span><br/> -->
          <div style="position: relative">
            <span style="font-size: 12px">地址:广东省深圳市宝安区松岗镇江边工业园创业三路82-83号</span> <br />
            <span style="font-size: 12px">Add:No.82-83,3rd Chuangye RowsData,Jiangbian1,Songgang,Bao'An,Shenzhen,China</span> <br />
            <span style="font-size: 12px">Tel:0755-784541221 Fax:0755-965412 Postcode:518105 Website:www.szjzpcb.com.cn</span> <br />
            <span style="font-size: 22px; font-weight: bold">QUOTATION报价单</span><br />
          </div>
        </div>
      </div>
      <div style="display: flex; line-height: 3ch; font-weight: bold; font-size: 12px">
        <div style="width: 60%; z-index: 99">
          <div>{{ JZreportdata.party_ }}</div>
          <div>{{ JZreportdata.factory_ }}</div>
          <div>Model Number/型号：{{ JZreportdata.pcbFileName }}</div>
        </div>
        <div style="z-index: 99; width: 40%">
          <div style="float: right">
            <div>{{ JZreportdata.orderNo_ }}</div>
            <div>{{ JZreportdata.date_ }}</div>
            <div>{{ JZreportdata.currency_ }}</div>
          </div>
        </div>
      </div>
      <div>
        <table border="1" style="text-align: center; margin-top: 5px; border-color: black">
          <thead>
            <tr style="font-weight: bold">
              <td style="width: 110px">No</td>
              <td style="width: 110px">QTY<br />数量</td>
              <td style="width: 110px">Area<br />面积</td>
              <td style="width: 120px">Price perunit<br />单价</td>
              <td style="width: 110px">Engineer fee<br />工程费</td>
              <td style="width: 110px">ET<br />测试架</td>
              <td style="width: 110px">Flying probe<br />飞针费</td>
              <td style="width: 110px">Film<br />菲林费</td>
              <td style="width: 110px">Punch tooling<br />模具费</td>
              <td style="width: 110px">Quick charge<br />加急费</td>
              <td style="width: 110px">Price/m²<br />平米价</td>
              <td style="width: 110px">Lead time<br />交期</td>
              <td style="width: 110px">Total<br />金额合计</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in JZreportdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.qty }}</td>
              <td>{{ item.area }}</td>
              <td>{{ item.pcs }}</td>
              <td>{{ item.eng }}</td>
              <td>{{ item.test }}</td>
              <td>{{ item.fly }}</td>
              <td>{{ item.film }}</td>
              <td>{{ item.mould }}</td>
              <td>{{ item.urgent }}</td>
              <td>{{ item.plate }}</td>
              <td>{{ item.custdate }}</td>
              <td>{{ item.total }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="margin-top: 8px">
        <div style="font-weight: bold">PCB Specifications / 制版工艺</div>
        <div style="display: flex; width: 100%">
          <div style="width: 58%">
            <table border="1" style="text-align: center; margin-top: 5px; border-color: black">
              <tbody>
                <tr>
                  <td style="width: 200px; font-weight: bold" rowspan="2">Panel Size<br />拼版尺寸</td>
                  <td style="width: 130px; font-weight: bold">Width<br />宽度</td>
                </tr>
                <tr>
                  <td style="height: 21px">{{ JZreportdata.setBoardWidth }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Layer Count<br />层数</td>
                  <td>{{ JZreportdata.boardLayers }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Finish board thickness<br />成品板厚</td>
                  <td>{{ JZreportdata.boardThickness }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Finish Outer Copper<br />外层成品铜厚</td>
                  <td>{{ JZreportdata.copperThickness }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Finsh process<br />表面处理</td>
                  <td>{{ JZreportdata.surface }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Solder Mask Color<br />阻焊颜色</td>
                  <td>{{ JZreportdata.solderColor }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Peel Mask<br />蓝胶</td>
                  <td>{{ JZreportdata.isBlueGum }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Kapton tape<br />高温胶</td>
                  <td>{{ JZreportdata.isPasteRedTape }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Carbon oil<br />碳油</td>
                  <td>{{ JZreportdata.isCarbonOil }}</td>
                </tr>
                <tr>
                  <td style="font-weight: bold">Impedance control<br />阻抗</td>
                  <td>{{ JZreportdata.impGroupNum }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div>
            <table border="1" style="text-align: center; margin-top: 5px; border-color: black">
              <tbody>
                <tr>
                  <td style="width: 80px; font-weight: bold">Length<br />长度mm</td>
                  <td style="width: 250px; font-weight: bold">Pcs<br />拼版数</td>
                </tr>
                <tr>
                  <td style="height: 21px">{{ JZreportdata.setBoardHeight }}</td>
                  <td>{{ JZreportdata.pinBanType }}</td>
                </tr>
              </tbody>
            </table>
            <table border="1" style="text-align: center; margin-top: -1px; border-color: black">
              <tbody>
                <tr>
                  <td style="width: 200px; font-weight: bold">Material<br />材质</td>
                  <td style="width: 130px">{{ JZreportdata.fR4Type }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; height: 39px; font-weight: bold">TG</td>
                  <td style="width: 130px">{{ JZreportdata.fR4Tg }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; font-weight: bold">Finish Inner Copper<br />内层完成铜厚</td>
                  <td style="width: 130px">{{ JZreportdata.innerCopperThickness }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; font-weight: bold">Gold Finger<br />金手指</td>
                  <td style="width: 130px">{{ JZreportdata.goldfingerThickness }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; font-weight: bold">Screen Printing Color<br />字符颜色</td>
                  <td style="width: 130px">{{ JZreportdata.fontColor }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; font-weight: bold">Chamfer<br />斜边</td>
                  <td style="width: 130px">{{ JZreportdata.goldfingerBevel }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; font-weight: bold">Outline<br />成型方式</td>
                  <td style="width: 130px">{{ JZreportdata.formingType }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; font-weight: bold">E-Test<br />电测</td>
                  <td style="width: 130px">{{ JZreportdata.flyingProbe }}</td>
                </tr>
                <tr>
                  <td style="width: 200px; font-weight: bold">Material usage<br />开料利用率(%)</td>
                  <td style="width: 130px">{{ JZreportdata.sheetUtilization }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div style="margin-top: 5px; font-weight: bold">PCB Special requirement / 特殊要求:</div>
      <div>
        <div style="padding-left: 3ch; border: 1px solid; min-height: 60px; white-space: pre-wrap">{{ JZreportdata.specialrequirements }}</div>
        <div style="padding-left: 3ch; border: 1px solid; border-top: none; min-height: 60px; white-space: pre-wrap">{{ JZreportdata.noteSure }}</div>
      </div>
      <div style="display: flex; line-height: 3ch; margin-top: 8px; font-weight: bold">
        <div>
          <div>备注/Remark:</div>
          <div>1.我们保留根据最终gerber重新报价的权利</div>
          <div>We reserve the right to requote according to the gerber file .</div>
          <div>2.报价有效期30天./validity : 30days.</div>
          <div>3.按IPC-600-G Ⅱ标准报价./ Quote according to IPC-600-G Ⅱstandard</div>
        </div>
      </div>
      <div style="float: right; font-weight: bold">表格编号:JZ-QR-DCC-243C</div>
    </div>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["selectlength", "JZreportdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj: {
        id: "jzreport2", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      printObj1: {
        id: "jzreport1", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.JZreportdata.price.length; index++) {
      if (this.JZreportdata.price[index].total && this.JZreportdata.price[index].total != "/") {
        this.amountto += Number(this.JZreportdata.price[index].total);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.JZreportdata.pcbFileName;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getreportPdf() {
      if (this.selectlength == 1) {
        htmlToPdf("jzreport2", this.JZreportdata.pcbFileName);
      } else {
        htmlToPdfa3("jzreport1", this.JZreportdata.pcbFileName);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
