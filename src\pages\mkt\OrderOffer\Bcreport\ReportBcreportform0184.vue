<!--本川报价单  -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="Bcreport0184" style="padding: 25px; color: black">
      <div class="formstyle">
        <div>
          <img src="@/assets/img/bclogo.png" style="height: 80px; position: absolute" />
          <div style="text-align: center; font-size: 40px; font-weight: bold; color: rgb(0 0 128)">
            <div>Allfavor Technology (Hong Kong) Limited.</div>
            <div>Quotation Matrix</div>
          </div>
        </div>
        <div style="padding-top: 15px">
          <table border="1" style="width: 100%; text-align: center">
            <tr>
              <td>Customer Name</td>
              <td colspan="2">{{ Bcreportdata.value_1 }}</td>
              <td>Quote Date</td>
              <td colspan="2">{{ Bcreportdata.value_2 }}</td>
            </tr>
            <tr>
              <td>Customer P/N</td>
              <td colspan="2">{{ Bcreportdata.value_3 }}</td>
              <td>Quotation No.</td>
              <td colspan="2">{{ Bcreportdata.value_4 }}</td>
            </tr>
            <tr>
              <td colspan="6" style="text-align: left; font-weight: bold">1: Basic Information</td>
            </tr>
            <tr>
              <td>layer Count</td>
              <td colspan="2">{{ Bcreportdata.value_5 }}</td>
              <td>Base Material Type</td>
              <td colspan="2">{{ Bcreportdata.value_6 }}</td>
            </tr>
            <tr>
              <td>Unit Size</td>
              <td colspan="2">{{ Bcreportdata.value_7 }}</td>
              <td>Array Size</td>
              <td colspan="2">{{ Bcreportdata.value_8 }}</td>
            </tr>
            <tr>
              <td>Set Form Format</td>
              <td colspan="2">{{ Bcreportdata.value_9 }}</td>
              <td>Array Form Area</td>
              <td colspan="2">{{ Bcreportdata.value_10 }}</td>
            </tr>
            <tr>
              <td>Finished Thickness</td>
              <td colspan="2">{{ Bcreportdata.value_11 }}</td>
              <td>Finished Cu Thickness</td>
              <td colspan="2">{{ Bcreportdata.value_12 }}</td>
            </tr>
            <tr>
              <td>Surface Treatment</td>
              <td colspan="2">{{ Bcreportdata.value_13 }}</td>
              <td>Hole Density (Qty/Sq M)</td>
              <td colspan="2">{{ Bcreportdata.value_14 }}</td>
            </tr>
            <tr>
              <td>Minimum hole size</td>
              <td colspan="2">{{ Bcreportdata.value_15 }}</td>
              <td>Min Line Width/Spacing</td>
              <td colspan="2">{{ Bcreportdata.value_16 }}</td>
            </tr>
            <tr>
              <td>Special Material</td>
              <td colspan="2">{{ Bcreportdata.value_17 }}</td>
            </tr>
            <tr>
              <td>Remarks</td>
              <td colspan="5" style="text-align: left">{{ Bcreportdata.value_18 }}</td>
            </tr>
            <tr>
              <td colspan="6" style="text-align: left; font-weight: bold">2: Quotation matrix with different Qty</td>
            </tr>
            <tr>
              <td>Delivery Qty</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price1 }}PCS</td>
            </tr>
            <tr>
              <td>Delivery Area</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price2 }}Sq Ft</td>
            </tr>
            <tr>
              <td>Startup Cost (USD)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price3 }}USD</td>
            </tr>
            <tr>
              <td>Extra Cost</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price4 }}USD</td>
            </tr>
            <tr>
              <td>Gloss weight (KG)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price5 }}KG</td>
            </tr>
            <tr>
              <td>Totally Freight Cost (USD)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price6 }}USD</td>
            </tr>
            <tr>
              <td>Freight Cost (USD/Sq In)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price7 }}USD/Sq In</td>
            </tr>
            <tr>
              <td>Unit Price (excluding Freight)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price8 }}USD/Sq In</td>
            </tr>
            <tr>
              <td>Total Unit Price (Including Freight)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price9 }}USD/Sq In</td>
            </tr>
            <tr>
              <td>Single Unit Price (CIF USA)(USD/PCS)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price10 }}USD/PCS</td>
            </tr>
            <tr>
              <td>FCA HK Price (USD/PCS)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price11 }}USD/PCS</td>
            </tr>
            <tr>
              <td>Total Amount(CIF USA)(USD)</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price12 }}USD</td>
            </tr>
            <tr>
              <td>Work Days</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price13 }}Days</td>
            </tr>
            <tr>
              <td>Payment Term</td>
              <td v-for="(item, index) in Bcreportdata.price" :key="index">{{ item.price14 }}</td>
            </tr>
            <tr>
              <td>Remarks</td>
              <td colspan="5" style="text-align: left; color: red">{{ Bcreportdata.value_19 }}</td>
            </tr>
            <tr>
              <td>Prepared By</td>
              <td colspan="2" style="font-size: 30px; font-weight: bold; color: #0000ff; font-style: italic">{{ Bcreportdata.value_20 }}</td>
              <td>Approved By</td>
              <td colspan="2" style="font-size: 30px; font-weight: bold; color: #0000ff; font-style: italic">{{ Bcreportdata.nO1_ }}</td>
            </tr>
            <tr>
              <td>Date</td>
              <td colspan="2" style="font-size: 30px; font-weight: bold; color: #0000ff">{{ Bcreportdata.nO2_ }}</td>
              <td>Date</td>
              <td colspan="2" style="font-size: 30px; font-weight: bold; color: #0000ff">{{ Bcreportdata.nO2_ }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //竖版a4
import convertToChineseNum from "@/utils/convertToChineseNum";
export default {
  name: "",
  props: ["Bcreportdata", "ttype"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj1: {
        id: "Bcreport0184", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.Bcreportdata.price.length; index++) {
      if (this.Bcreportdata.price[index].price15 && this.Bcreportdata.price[index].price15 != "/") {
        this.amountto += Number(this.Bcreportdata.price[index].price15);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
    if (this.Bcreportdata.price.length < 5) {
      let additionalArraysNeeded = 5 - this.Bcreportdata.price.length;
      for (let i = 0; i < additionalArraysNeeded; i++) {
        this.Bcreportdata.price.push([]);
      }
    }
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.Bcreportdata.pcbFileName;
    },
    getreportPdf() {
      htmlToPdfa3("Bcreport0184", this.Bcreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
@media print {
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
}
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.tableclass > table > tr > td {
  text-align: center;
  min-width: 200px;
}
.formstyle {
  font-family: Arial;
  position: relative;
  /deep/.ant-form-item-children {
    color: black;
  }
  /deep/.ant-form-item-label {
    height: 25px;
    line-height: 25px;
  }
  /deep/.ant-form-item-control {
    height: 25px;
    line-height: 25px;
  }
}
.Bcreport0184 {
  .tableclass > table > tr > td {
    text-align: center;
    min-width: 200px;
  }
  table > tr > td {
    padding: 0px 5px;
    word-break: break-word;
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
