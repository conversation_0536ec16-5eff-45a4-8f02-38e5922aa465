<!-- 工具管理- 客户规则 -->
<template>
  <div ref="SelectBox">
    <a-spin :spinning="spinning">
      <div class="projectMake" style="position: relative;">
        <div style='width:100%;display:flex;'>
          <div class="leftContent">
            <left-table-make v-if="showModel" :columns="columns1" :data-source="orderListData"
              :orderListTableLoading="orderListTableLoading" :rowKey="(record, index) => `${index + 1}`"
              :pagination="pagination" ref="orderTable" :class="orderListData.length ? 'min-table' : ''"
              @getOrderDetail="getOrderDetail" :editFlag="editFlag" @tableChange="handleTableChange">
              <span slot="num" slot-scope="text, record, index">
                {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
              </span>
            </left-table-make>
          </div>
          <div class="rightContent">
            <div class='leftBox'>
              <div class='top' >  
                <a-table :columns="columns4" 
                rowKey="itemsConfigId" 
                :pagination="false" 
                :data-source="ruleItemsData"
                :loading="orderListTableLoading2" 
                :class="ruleItemsData.length ? 'min-table' : ''" 
                :scroll="{y:722}">                  
                    <template slot="value" slot-scope="text,record">   
                      <a-checkbox  @change="Valuechange"   :checked="record.value ? true : false" :disabled="!editFlag1" v-if="record.dataType == 'bit' && !editFlag1 && record.value" ></a-checkbox>
                      <a-checkbox  @change="Valuechange"   v-model="record.value"  v-if="record.dataType == 'bit' && editFlag1"  ></a-checkbox>   
                        <a-input v-if="record.dataType != 'bit' && editFlag1"  @blur="Valueblur()" v-model="record.value" style="width:100%;"  />
                        <span v-if="!editFlag1 && record.dataType != 'bit'">{{ record.value }}</span>
                      </template>
                    <template slot="isOrderPre" slot-scope="text,record">
                      <a-checkbox v-model="record.isOrderPre" v-if="!editFlag1 && record.isOrderPre" disabled />
                     <a-checkbox v-if="editFlag1" v-model="record.isOrderPre"  /> 
                    </template>
                    <template slot="isPpeMake" slot-scope="text,record">
                      <a-checkbox v-model="record.isPpeMake" v-if="!editFlag1 && record.isPpeMake" disabled />
                     <a-checkbox v-if="editFlag1" v-model="record.isPpeMake"  /> 
                    </template>
                    <template slot="isLoseEfficacy" slot-scope="text,record">
                      <a-checkbox v-model="record.isLoseEfficacy" :disabled="!editFlag1" />
                    </template> 
                  </a-table>               
                <!-- <a-collapse v-model="activeKey" :bordered="true" :expandIconPosition="'right'" >
                <a-collapse-panel  :header="item[0].groups" v-for="(item,index) in ruleItemsData" :key="String(index+1)">
                  <a-table :columns="columns4" rowKey="itemsConfigId" :pagination="false" :data-source="item"
                    :loading="orderListTableLoading2" >                  
                    <template slot="value" slot-scope="text,record">   
                      <a-checkbox     @change="Valuechange"   :checked="record.value ? true : false" :disabled="!editFlag1" v-if="record.dataType == 'bit' && !editFlag1"  ></a-checkbox>
                      <a-checkbox     @change="Valuechange"   v-model="record.value"  v-if="record.dataType == 'bit' && editFlag1"  ></a-checkbox>       
                      <a-input v-if="record.dataType != 'bit'"  @blur="Valueblur()" v-model="record.value" style="width:100%;"  :disabled="!editFlag1" />
                    </template>
                    <template slot="isOrderPre" slot-scope="text,record">
                      <a-checkbox v-model="record.isOrderPre" :disabled="!editFlag1" />
                    </template>
                    <template slot="isPpeMake" slot-scope="text,record">
                      <a-checkbox v-model="record.isPpeMake" :disabled="!editFlag1" />
                    </template>
                    <template slot="isLoseEfficacy" slot-scope="text,record">
                      <a-checkbox v-model="record.isLoseEfficacy" :disabled="!editFlag1" />
                    </template> 
                  </a-table>                 
                </a-collapse-panel>                  
                <template #expandIcon="props">
                  <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0"/>
                </template>
                </a-collapse>  -->
              </div>             
            </div>
            <div class='rightBox'>
              <div style="display: flex;height:398px;">
                <div class='top'>
                  <a-table 
                    :columns="columns2" 
                    rowKey="guid_" 
                    :pagination="false" 
                    :data-source="CustomerData"
                    :loading="orderListTableLoading2" 
                    :customRow="onClickRow2"
                    :rowClassName="isRedRow2"
                    :scroll="{y: 360 }" 
                    :row-selection="{ selectedRowKeys: selectedRowKeysArray, onChange: onSelectChange_content , columnWidth: 25,}"
                    :class="CustomerData.length ? 'minClass' : ''">
                    <template slot="isAble_" slot-scope="text,record">
                      <a-checkbox v-model="record.isAble_" disabled />
                    </template>
                    <template slot="isAuto_" slot-scope="text,record">
                      <a-checkbox v-model="record.isAuto_" disabled />
                    </template>

                  </a-table>
                </div>
                <div class='bto'>
                  <a-table :columns="columns3" :data-source="attData" :pagination="false"
                    :loading="orderListTableLoading3" rowKey="guid_" :customRow="onClickRow3" :rowClassName="isRedRow3"
                    :scroll="{ x: 420, y: 360 }" :class="attData.length ? 'minClass' : ''">
                    <template slot="realFileName_" slot-scope="text,record">
                      <span style='color:blue;' :title="record.realFileName_" @click="down(record)">{{ record.realFileName_ }}</span>
                    </template>

                    <template slot="fileUrl" slot-scope="text,record">
                      <a @click.stop="delFlieClick(record)" v-if="checkPermission('MES.ToolModule.CustRule.CustRuleDeleteAtt')">删除</a>
                    </template>

                  </a-table>
                </div>
              </div>
              <right-box :rightData="cloneObject(rightData)" @update:rightData="updateMainData" :editFlag="editFlag"
                :optionList='optionList' :code="code" ref="rightData"></right-box>
            </div>
          </div>
        </div>
        <div class="footerAction" style='user-select: none;'>
          <make-action ref="action" :assignLoading="assignLoading" :editFlag="editFlag" :editFlag1="editFlag1" @queryClick="queryClick" @addClick="addClick"
            @edtiClick="edtiClick" @edtiClick1="edtiClick1" @saveClick="saveClick" @cancelClick="cancelClick" @addFlieClick="addFlieClick"
            @getAttInfo="getAttInfo" @deletecustrule="deletecustrule" />
        </div>
        <!-- 查询弹窗 -->
        <a-modal title="订单查询" :visible="dataVisible" @cancel="reportHandleCancel" @ok="handleOk" ok-text="确定"
          destroyOnClose :maskClosable="false" :width="400" centered :confirmLoading='confirmLoading'>
          <query-info ref='queryInfo' />
        </a-modal>
        <a-modal title="确认弹窗" :visible="modelvisible" @cancel="modelvisible=false" @ok="modelhandleOk" ok-text="确定"
          destroyOnClose :maskClosable="false" :width="400" centered >
          <span>确认删除所选客户规则吗?</span>
        </a-modal>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import LeftTableMake from "@/pages/gongju/CustomerRules/module/LeftTableMake";
import { checkPermission } from "@/utils/abp";
import RightBox from "@/pages/gongju/CustomerRules/module/RightBox.vue";
import {
  custRuleOrderList,
  ruleShowInfo,
  attInfo,
  repait,
  saveNew,deletecustrule,
  ClassList, custClassList, getClassList, delFile,ruleItemsList,setRuleItemsList,custClassListv2
} from "@/services/CustRule";
import MakeAction from "@/pages/gongju/CustomerRules/module/MakeAction";
import QueryInfo from "@/pages/gongju/CustomerRules/module/QueryInfo";
import { queryData } from '@/services/standardlist';
const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    scopedSlots: { customRender: 'num' },
    // customRender: (text,record,index) => `${index+1}`,
    width: 45,
  },
  {
    title: "工厂",
    dataIndex: "tradeTypeStr",
    align: "left",
    ellipsis: true,
    width: 70,
    className: 'userStyle'
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 75,
    className: 'userStyle'
  },
  {
    title: "客规",
    width: 40,
    dataIndex: "isCustRule",
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: 'isCustRule' }
  },
  // {
  //   title: "操作人",
  //   dataIndex: "",
  //   align: "left",
  //   ellipsis: true,
  //   width:140,
  // },
  // {
  //   title: "操作时间",
  //   dataIndex: "",
  //   align: "left",
  //   ellipsis: true,
  //   width: 60,
  // },

]
const columns2 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "规则类型",
    dataIndex: "ruleSketch_",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "规则失效",
    dataIndex: "isAble_",
    width: 65,
    ellipsis: true,
    scopedSlots: { customRender: 'isAble_' },
    align: "center",
  },
  {
    title: "自动规则",
    dataIndex: "isAuto_",
    width: 65,
    ellipsis: true,
    scopedSlots: { customRender: 'isAuto_' },
    align: "center",
  },
  // {
  //   title: "规则描述",
  //   dataIndex: "ruleDescribe_",
  //   width: 195,
  //   ellipsis: true,
  //   align: "left",
  // },
  {
    title: "修改人",
    dataIndex: "inUserName",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "修改时间",
    dataIndex: "inDate_",
    width: 148,
    ellipsis: true,
    align: "left",
  }
]
const columns3 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: 45,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "附件名",
    width: 120,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: 'realFileName_' }
  },
  {
    title: "录入人",
    dataIndex: "inUser_",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "录入时间",
    dataIndex: "inDate_",
    width: 155,
    ellipsis: true,
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: 'fileUrl' },
    align: "left",
    width: 70,
  }

]
const columns4 = [
  {
    title: '分类',
    dataIndex: 'groups',
    align: "center",
    width: 60,
    key: 'groups',
    ellipsis: true,
    customRender(text, row) {
      return {
        children: text,
        attrs: {
          // 列纵向合并
          rowSpan: row.groupsRowSpan
        }
      };
    },

  },
  {
    title: '序号',
    dataIndex:'index',
    align: "center",
    ellipsis: true,
    width: 45,
    customRender: (text,record,index) => `${index+1}`,

  },
  {
    title: '要求',
    dataIndex: 'items',
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: '值',
    dataIndex: 'value',
    align: "center",
    width: 60,
    ellipsis: true,
    className:'userStyle',
    scopedSlots: { customRender: 'value' },
  },
  {
    title: '订单预审',
    dataIndex: "isOrderPre",
    align: "center",
    width: 50,
    ellipsis: true,
    scopedSlots: { customRender: 'isOrderPre' },
  },
  {
    title: '工程制作',
    dataIndex: 'isPpeMake',
    align: "center",
    width: 50,
    ellipsis: true,
    scopedSlots: { customRender: 'isPpeMake' },
  },
  //  {
  //   title: '失效',
  //   dataIndex: 'isLoseEfficacy',
  //   align: "center",
  //   width: 60,
  //   scopedSlots: { customRender: 'isLoseEfficacy' },
  // },
]
export default {
  name: "",
  components: { MakeAction, QueryInfo, LeftTableMake, RightBox },
  inject: ['reload'],
  data() {
    return {
      modelvisible:false,
      custno: '',
      confirmLoading: false,
      spinning: false,
      orderListTableLoading: false,
      orderListTableLoading2: false,
      orderListTableLoading3: false,
      columns1,
      columns2,
      columns3,
      columns4,
      ruleItemsData:[],      
      orderListData: [],
      CustomerData: [],
      attData: [],
      dataVisible: false,   // 查询弹窗开关
      assignLoading: false,
      pagination: {
        pageSize: 20,
        pageIndex: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"],//每页中显示的数据
        showTotal: (total) => `总计 ${total} 条`,
      },
      selectedRowKeysArray: [],
      selectedRowKeysArray1: [],
      selectedRowsData: {},
      code: 0,
      editFlag: true,
      editFlag1:false,
      optionList: [],
      rightData: {},
      showModel: true,
      activeKey:['1','2','3'],
      datasource:[],
    }
  },
  created() {
    this.getOrderList()
    this.getClassList()
  },
  watch: {
    'rightData': {
      handler(val) {
        // console.log('主val',val)

      }
    }
  },

  methods: {
      deletecustrule(){
        if(this.selectedRowKeysArray.length == 0){
          this.$message.error('请选择要删除的数据')
          return
       }
       this.modelvisible=true
    },
    modelhandleOk(){
      deletecustrule(this.selectedRowKeysArray).then(res=>{
        if(res.code){
          this.$message.success(res.message)
          this.getOrderDetail(this.$refs.orderTable.custNo, this.$refs.orderTable.tradeType, 1)
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.modelvisible=false
        this.selectedRowKeysArray=[]
      })
    },
    // 订单表变化change
    // handleTableChange(pagination) {
    //   this.pagination.current=pagination.current
    //   this.getOrderList()
    // },
    cloneObject(obj) {
      return cloneDeep(obj);
    },
    updateMainData(value) {
      this.rightData = value;
    },
    handleTableChange(pagination,) {
      this.pagination.pageIndex = pagination.current
      this.pagination.pageSize = pagination.pageSize
      localStorage.setItem('pageCurrent', this.pagination.current)
      localStorage.setItem('pageSize', pagination.pageSize)
      this.pageStat = false
      localStorage.removeItem('stat')
      this.getOrderList()
    },
    checkPermission,
    // // 获取订单
    // getOrderList(queryData){
    //   let params = {
    //     'PageIndex': this.pagination.current,
    //     'PageSize' : this.pagination.pageSize,
    //   }
    //   if(queryData) {
    //     params.custNo= queryData
    //   }
    //   this.orderListTableLoading = true;
    //   custRuleOrderList (params).then(res => {
    //     if (res.code) {
    //       this.pagination.total = res.data.totalCount;
    //       this.orderListData = res.data.items;
    //       console.log(this.orderListData,'this.orderListData')
    //       this.orderListData.forEach(item=>{
    //         if(item.isCustRule == 0){
    //           item.isCustRule = false
    //         }else{
    //           item.isCustRule = true
    //         }
    //       })
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   }).finally(()=> {
    //     this.orderListTableLoading = false;
    //   })
    // },  
    // 获取订单
    getOrderList(key) {
      if (key) {
        this.activeKey = key
      }
      let params = {
        ...this.pagination,
      }
      params.custNo = this.custno
      this.orderListTableLoading = true;
      custRuleOrderList(params).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
          this.pagination.total = res.data.totalCount;
          this.orderListData.forEach(item => {
            if (item.isCustRule == 0) {
              item.isCustRule = false
            } else {
              item.isCustRule = true
            }
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.orderListTableLoading = false;
      })
    },
    // 获取订单详情
    getOrderDetail(record, trade, type) {
      let params = record
      this.orderListTableLoading2 = true
      this.getRuleItemsList(record,trade)     
      ruleShowInfo(params, trade,2).then(res => {
        if (res.code) {
          this.CustomerData = res.data
          if (type == 1) {
            var data = this.CustomerData.filter(item => { return item.guid_ == this.selectedRowKeysArray[0] })[0]
            this.rightData = data
            this.selectedRowsData = data
            this.selectedRowsData.ruleType_ = this.selectedRowsData.ruleType_.toString()
          } else {
            this.attData = []
            this.selectedRowKeysArray = []
            this.selectedRowsData = {}
            this.rightData = {}
          }
        } else {
          this.$message.error(res.message)
        }

      }).finally(() => {
        this.orderListTableLoading2 = false
      })
    },
    // 子项列表数据
    getRuleItemsList(CustNo,TradeType){
      ruleItemsList(CustNo,TradeType).then(res=>{
        if(res.code){
          for(var b=0;b<res.data.length;b++){            
            if (res.data[b].dataType == 'bit') {
              if(res.data[b].value == 'true'){
                res.data[b].value = true
              }else{
                res.data[b].value = false
              }
            }
          } 
          this.ruleItemsData = res.data .filter(item => { return item.isOrderPre || item.value || item.isPpeMake })
          this.datasource = res.data      
          // if(!this.editFlag1){
          //   this.ruleItemsData =this.ruleItemsData.filter(item => { return item.isOrderPre==true || item.value==true || item.isPpeMake ==true})
          // }
          // let arr = []
          // let data1 = this.ruleItemsData.filter(item => { return item.groups == '包装要求' })
          // let data2 = this.ruleItemsData.filter(item => { return item.groups == 'FQC' })
          // let data3 = this.ruleItemsData.filter(item => { return item.groups == '工程制作' })
          // arr.push(data1)
          // arr.push(data2)
          // arr.push(data3)
          // console.log('arr',arr)
          // this.ruleItemsData = arr
          this.rowSpan1("groups");
        }else{
          this.$message.error(res.message)
        }
      })
    },
    //合并列
    rowSpan1(key) {
      let _list = this.ruleItemsData;
      let _num = []; 
      let indexList = []; 

      for (let i = 0; i < _list.length; i++) {
        let downKey = _list[i + 1] ? _list[i + 1][key] : ''
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i); 
          for (let z = 0; z < _num.length; z++) {
            _list[indexList[0]][`${key}RowSpan`] = _num.length
            if (z != 0) {
              _list[indexList[z]][`${key}RowSpan`] = 0
            }
          }
          _num = []
          indexList = []
          continue;
        } else {
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      this.ruleItemsData = _list;      
    },
    // 订单详情列表点击事件
    isRedRow2(record) {
      let strGroup = []
      let str = []
      if (record.guid_ && record.guid_ == this.selectedRowKeysArray[0]) {
        strGroup.push('rowBackgroundColor')
      }
      return str.concat(strGroup)
    },
      //列表勾选获取数据
      onSelectChange_content(selectedRowKeys, selectedRows) {
          this.selectedRowKeysArray = selectedRowKeys;
      },
    onClickRow2(record) {
      return {
        on: {
          click: () => {
            if (this.editFlag) {
              if(this.selectedRowKeysArray.indexOf(record.guid_) != -1){
                this.selectedRowKeysArray.splice(this.selectedRowKeysArray.indexOf(record.guid_),1)
              }else{
                this.selectedRowKeysArray.push(record.guid_);
              }        
              this.selectedRowsData = record              
              this.selectedRowsData.ruleType_ = this.selectedRowsData.ruleType_.toString()    
              this.rightData = this.selectedRowsData
              this.getAttInfo(record.guid_,'1')
              if(this.selectedRowKeysArray.length==0){
                this.selectedRowsData = {}
                this.rightData ={}         
              }
            } else {
              this.$message.warning('编辑状态不可选择其他订单')
            }
          },

        }
      }
    },
    // 附件列表点击事件
    isRedRow3(record) {
      let strGroup = []
      let str = []
      if (record.guid_ && record.guid_ == this.selectedRowKeysArray1[0]) {
        strGroup.push('rowBackgroundColor')
      }
      return str.concat(strGroup)
    },
    onClickRow3(record) {
      return {
        on: {
          click: () => {
            if (this.editFlag) {
              let keys = [];
              keys.push(record.guid_);
              this.selectedRowKeysArray1 = keys;
            } else {
              this.$message.warning('新增/编辑状态不可选择其他订单')
            }
          },

        }
      }
    },
    // 获取附件列表
    getAttInfo(record,type) {
      this.orderListTableLoading3 = true
      attInfo(record).then(res => {
        if (res.code) {
          this.attData = res.data
          if(this.selectedRowKeysArray.length==0 && type==1){
            this.attData=[]
          }
        } else {
          this.$message.error(res.data)
        }
      }).finally(() => {
        this.orderListTableLoading3 = false
      })
    },
    // 获取下拉选择
    getClassList() {
      custClassListv2().then(res => {
        if (res.code) {
          this.optionList = res.data
        } else {
          this.$message.error(res.data)
        }
      })
    },
    // 新增
    addClick(code) {
      if (this.$refs.orderTable.custNo) {
        this.editFlag = false
        this.code = code
        this.getOrderDetail(this.$refs.orderTable.custNo, this.$refs.orderTable.tradeType)
      } else {
        this.$message.warning('请选择客户代码')
      }
    },
    // 编辑
    edtiClick(code) {
      this.editFlag1 = false
      if(this.selectedRowKeysArray.length>1){
        this.$message.warning('只能选择一条数据进行编辑')
        return
      }
      if (this.selectedRowKeysArray[0]) {
        this.editFlag = false
        this.code = code
        this.rightData = this.selectedRowsData
      } else {
        this.$message.warning('请选择客户规则')
      }
      this.ruleItemsData = this.ruleItemsData .filter(item => { return item.isOrderPre || item.value || item.isPpeMake })
      this.rowSpan1("groups");
    },
    // 列表编辑
    edtiClick1(){
      this.editFlag = true
      if (this.$refs.orderTable.selectedRowKeysArray.length) {
        this.editFlag1 = true
      } else {
        this.$message.warning('请选择客户代码')
      }
      this.ruleItemsData = this.datasource
      this.rowSpan1("groups")
    },
    // 保存
    saveClick() {
      // if (this.editFlag) {
      //   this.$message.warning('非详情编辑/新增状态不能保存')
      //   return
      // }
      // if (!this.editFlag1) {
      //   this.$message.warning('非列表编辑状态不能保存')
      //   return
      // }
      var r = /^\+?[1-9][0-9]*$/  //正整数正则
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/  //正数正则
      if (!this.editFlag) {
        let params = this.$refs.rightData.selectedRowsData
        let arr = this.$refs.rightData.custItemList
        let ItemExt = []
        for (var a = 0; a < arr.length; a++) {
          if (arr[a].value && arr[a].dataType == 'int' && !r.test(arr[a].value)) {
            this.$message.warning(arr[a].caption + '请输入正整数')
            return
          }
          if (arr[a].value && arr[a].dataType == 'float' && !x.test(arr[a].value)) {
            this.$message.warning(arr[a].caption + '请输入正数')
            return
          }
          if (arr[a].value) {
            ItemExt.push(arr[a].id + ':' + arr[a].value)
          }
        }
        params.ItemExt = ItemExt.join(',')
        // console.log('ItemExt',ItemExt)
        if (!params.ruleType_) {
          this.$message.warning('请选择规则类型')
          return
        }
        if (!params.isMktPre_ && !params.isMIMake_) {
          this.$message.warning('订单预审、工程制作请至少勾选一个')
          return
        }
        // if(!params.ruleDescribe_ ){
        //   this.$message.warning('请填写规则描述')
        //   return
        // }  
        if (!params.ruleDescribe_ && !ItemExt.length) {
          this.$message.warning('请填写规则描述')
          return
        }
        this.editFlag = true
        params.ruleSketch_ = this.optionList.filter(item => { return item.valueMember == params.ruleType_ })[0].text // caption_
        params.TechNo_ = this.optionList.filter(item => { return item.valueMember == params.ruleType_ })[0].valueMember
        if (this.code == 2) {
          params.custNo = this.$refs.orderTable.custNo
          params.tradeType_ = this.$refs.orderTable.selectedRowsData.tradeType
          repait(params).then(res => {
            if (res.code) {
              this.$message.success('编辑成功')
              this.getOrderDetail(this.$refs.orderTable.custNo, this.$refs.orderTable.tradeType, 1)
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.code = 0
          })
        } else {
          params.custNo = this.$refs.orderTable.custNo
          params.tradeType_ = this.$refs.orderTable.tradeType
          saveNew(params).then(res => {
            if (res.code) {
              this.$message.success('新增成功')
              this.getOrderDetail(this.$refs.orderTable.custNo, this.$refs.orderTable.tradeType)
              this.getOrderList()
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.code = 0
          })
        }
      }
      if(this.editFlag1){
        let arr = this.ruleItemsData
        for(var aa=0;aa<arr.length;aa++){
          if (arr[aa].value && arr[aa].dataType == 'int' && !r.test(arr[aa].value)) {
            this.$message.warning(arr[aa].groups + '请输入正整数')
            return
          }
          if (arr[aa].value && arr[aa].dataType == 'float' && !x.test(arr[aa].value)) {
            this.$message.warning(arr[aa].groups + '请输入正数')
            return
          }
        }
        for(var bb=0;bb<arr.length;bb++){                
          if (arr[bb].dataType == 'bit') {
            if(arr[bb].value == true){
              arr[bb].value = 'true'
            }else{
              arr[bb].value = 'false'
            }
          }
        }        
        this.editFlag1 = false
        let record =  this.$refs.orderTable.selectedRowsData
        setRuleItemsList(arr).then(res=>{          
          if(res.code){
            this.$message.success('列表编辑成功')
            this.getRuleItemsList(record.custNo,record.tradeType)
          }else{
            this.$message.error(res.message)           
           this.getRuleItemsList(record.custNo,record.tradeType)
          }
        })
      }
      

      // this.selectedRowsData = {}
    },
    // 取消
    cancelClick() {
      this.code = 0
      this.editFlag = true
      this.rightData = this.selectedRowsData
      // this.$refs.rightData.selectedRowsData = this.rightData
    },
    // 下载附件
    down(record) {
      window.location.href = record.alypath
    },
    // 新增附件
    addFlieClick() {
      if (!this.editFlag) {
        this.$message.warning('编辑/新增状态不能新增附件')
        return
      }
      if(this.selectedRowKeysArray.length>1){
        this.$message.warning('只能选择一条数据进行新增附件')
        return
      }
      if (this.selectedRowKeysArray[0]) {
        this.$refs.action.clickUpload(this.selectedRowKeysArray[0])
      } else {
        this.$message.warning('请选择客户规则')
      }
    },
    // 删除附件
    delFlieClick(record) {
      if (!this.editFlag) {
        this.$message.warning('编辑/新增状态不能删除')
        return
      } else {
        if (confirm('确认删除该附件吗？')) {
          delFile(record.guid_).then(res => {
            if (res.code) {
              this.$message.success('已删除')
              this.getAttInfo(this.selectedRowKeysArray[0])
            }
            else {
              this.$message.error(res.message)
            }
          })
        }
      }
      //  console.log(record.guid_)

    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible = false;   // 查询弹窗
    },
    // 查询
    queryClick() {
      if (!this.editFlag) {
        this.$message.warning('编辑/新增状态不能查询')
        return
      }
      this.dataVisible = true;
    },
    handleOk() {
      this.dataVisible = false;
      this.custno = this.$refs.queryInfo.OrderNumber
      this.showModel = false
      this.pagination.pageIndex = 1
      this.getOrderList(this.custno)
      this.$nextTick(() => {
        this.showModel = true
      })
    },
    Valuechange(){
      console.log('custItemList',this.custItemList)
      this.$forceUpdate()     
    },
    Valueblur(value){
      this.$forceUpdate()
    },
  },

  beforeDestroy() {
  },

}
</script>

<style scoped lang="less">
/deep/ .ant-table-tbody>tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)>td {
  background: #dfdcdc !important;
}

/deep/.ant-table-thead>tr>th {
  border: 1px solid #f0f2f5;
}

/deep/ .ant-table-tbody>tr>td {
  border: 1px solid #f0f2f5;
}

/deep/.ant-table .ant-table-tbody>tr>td {
  padding: 6.75px 4px;
  border-color: #f0f0f0;
}

/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  font-weight: 500;
}

/deep/.ant-pagination-options {
  margin-left: 5px;
}

/deep/.ant-pagination {
  margin: 0;
  margin: 16px 0 0 20px; 
  float:left;
  width: 700px;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-input {
  font-weight: 500;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

.projectMake {
  height: 814px;
  min-width: 1670px;
  // width: 100%;
  // display: flex;
  background: #FFFFFF;

  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important
  }
 .min-table {
      /deep/.ant-table-body {
        min-height: 720px;
      }
    }
  /deep/.leftContent {
    height: 764px;
    width: 200px;
    user-select: none;

   

    //.ant-table-body{
    //  .ant-table-fixed{
    //    width: 500px !important;
    //  }
    //}
    width:15%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 2px solid rgb(233, 233, 240);

    .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;

      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
      }

      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }

  }

  /deep/ .userStyle {
    user-select: all !important;
  }

  .rightContent {
    border-bottom: 0;
    width: 85%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    /deep/ .leftBox {
      width: 43%;
      height: 764px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 2px solid rgb(233, 233, 240);

      .top {
        height: 410px;
        // height:756px;
        // overflow-y: auto;
        .userStyle{
          padding:0 4px!important;
        }
      }
      .bto {
        height: 305px;
      }
      /deep/ .minClass{
          .ant-table-body{
            min-height:722px;
          }
        }
    }

    .rightBox {
      .top {
        width: 60%;    
        border-right: 1px solid #ddd;
        border-bottom: 2px solid #ddd;
      }
      .bto {
        width: 40%;
        border-left: 1px solid #ddd;
        border-bottom: 2px solid #ddd;
      }
      /deep/ .minClass{
          .ant-table-body{
            min-height:360px;
          }
        }
      width: 57%;
      height: 764px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 2px solid rgb(233, 233, 240);

      .ant-form-item {
        margin-bottom: 10px !important;
      }
    }
  }

  .footerAction {
    width: 100%;
    height: 51px;
    border: 2px solid #E9E9F0;
    border-top: none;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #FFFFFF;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display: none;
    }

    // .ant-tabs-nav-container{
    //   height: 28px;
    //   .ant-tabs-tab {
    //     margin: 0;
    //     height: 28px;
    //     line-height: 28px;
    //     width:302px;
    //     text-align: center;
    //   }
    // }

  }

  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #DC143C;
        }
      }

      .eqBackground {
        background: #FFFF00;
      }

      .statuBackground {
        background: #B0E0E6;
      }

      .backUserBackground {
        background: #A7A2C9;
      }

    }

    .ant-table-row-selected {
      td {
        background: #aba5a5;
      }
    }

    .userStyle {
      user-select: all !important;
    }

  }

  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table {

    .ant-table-thead>tr>th {
      padding: 3px 2px;
      border-color: #f0f0f0;
    }

    .ant-table-tbody>tr>td {
      padding: 3px 2px;
      border-color: #f0f0f0;
    }

    tr.ant-table-row-selected td {
      background: #aba5a5;
    }

    tr.ant-table-row-hover td {
      background: #aba5a5;
    }

    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;

      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }

    .ant-card-body {
      padding: 0;
    }
  }

  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }




}

/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: rgb(248, 248, 248);
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #D6D6D6;

    p {
      line-height: 35px;
      font-weight: 700;
      margin: 0;

      img {
        width: 100%;
      }
    }

    .displayFlag {
      display: none;

    }
  }
}

.modalSty {
  /deep/.ant-modal .ant-modal-content .ant-modal-body {

    padding: 0;
  }
}
</style>
