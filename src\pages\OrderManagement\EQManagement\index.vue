<!--生产管理- EQ列表 -->
<template>
    <a-spin :spinning="spinning">
      <div class="projectBackend" ref="SelectBox">
        <div class="content">         
          <a-input  placeholder="拼版编号" v-model="formData.OrderNo" style="width:183px;margin-right:0.5%;margin-top:6px;" allowClear @keyup.enter.native="searchClick"></a-input>              
          <a-select placeholder="请选择问客状态" allowClear v-model="formData.eqStatus" 
          @keyup.enter.native="searchClick" style="width:150px;margin-right:0.5%;" :getPopupContainer="()=>this.$refs.SelectBox"
          showSearch optionFilterProp="label">
            <a-select-option  :key="1" label="已问客"> 已问客</a-select-option>
            <a-select-option  :key="2" label="已回复"> 已回复</a-select-option>
          </a-select>
          <a-button type="primary" @click="searchClick" style="margin-right:0.5%;margin-top:6px;">搜索</a-button>
        </div>
        <div class="leftContent" ref="tableWrapper" style="position:relative;">
          <a-table 
            v-if="pageshow"
            :columns="columns" 
            :dataSource="orderListData" 
            :customRow="onClickRow"
            :pagination="pagination" 
            :rowKey="'id'" 
            :scroll="{y: 697,x: 1300}"
            :loading="orderListTableLoading"
            @change="handleTableChange"
            :rowClassName="isRedRow"
            class="maintable"
          >
            <span slot="num" slot-scope="text, record, index" class="topCss">
              {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>           
            <div slot="orderNo" slot-scope="record" class="topCss">
                <a  style="color: #428bca" @click.stop="details(record)"  >{{record.orderNo}} </a>&nbsp;
                <span class="tagNum" style="display:inline-block;">
                <span v-if="record.IsUrgent" style="font-size: 14px;font-weight: 500; color:#ff9900;padding: 0 2px; margin: 0; display:inline-block;height: 19px;width:14px;margin-right:4px;margin-left:-10px;user-select: none;" ><a-icon type="thunderbolt" theme="filled"></a-icon> </span>
                <a-tag  v-if="record.isHighQuality != 0" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca">  {{ record.isHighQuality == 1 ? '优' : '精' }}</a-tag>
                <a-tag v-if="record.isLock" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca" > 锁  </a-tag>
                <a-tag v-if="record.smtFactoryId" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca" > 贴 </a-tag>
                <a-tag v-if="record.confirmWorkingDraft " style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca" >确</a-tag>
                <a-tag v-if="record.isBigCus" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca" > KA</a-tag>              
                <a-tag v-if="record.isReverse == '1' || record.isReverse == '2'" style="font-size: 12px; background:#428bca;color: white;padding: 0 2px; margin: 0; margin-right: 3px; height: 21px;user-select: none;border: 1px solid #428bca"> 反 </a-tag>
            </span>
            </div>
            <template slot="status" slot-scope="record">
              <span  class="topCss">{{record.statusStr}} </span> 
            </template>
            <template slot="wenke" slot-scope="record">
              <span  class="topCss">{{record.eqStatusStr}} </span> 
            </template>
            <template slot="joinFactoryIdStr" slot-scope="record">
              <span  class="topCss">{{record.joinFactoryIdStr}} </span> 
            </template>
            <template slot="confirmJoin" slot-scope="record">
                <div :style="record.confirmJoin ? 'color:blue': ''"  class="topCss1"><a-icon :type="record.confirmJoin ? 'check': 'lock'"> </a-icon>{{record.confirmJoin ? '是': '否'}} </div> 
             
            </template>
            <template slot="Process" slot-scope="record" >
              <span  style="line-height:1.5;">
                <template>材料:{{record.fR4TypeStr}} </template><br/>                
                <template>板厚:{{record.boardThickness}}</template><br/> 
                <template>层数:{{record.boardLayers}}</template><br/> 
                <template>工艺:{{record.fR4TypeStr}}</template> <br/> 
                <template>导热系数:{{record.boardLayers}} W</template> <br/>                
                <template>阻焊颜色:
                  <div v-if="record.solderColorStr.indexOf('绿') != -1 " style="width: 13px;height:13px;background: green;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.solderColorStr.indexOf('红') != -1 " style="width: 13px;height:13px;background: red;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.solderColorStr.indexOf('白') != -1 " style="width: 13px;height:13px;background: white;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.solderColorStr.indexOf('黑') != -1 " style="width: 13px;height:13px;background: black;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.solderColorStr.indexOf('黄') != -1 " style="width: 13px;height:13px;background: yellow;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.solderColorStr.indexOf('蓝') != -1 " style="width: 13px;height:13px;background: blue;display: inline-block;border:1px solid black"></div>
                  <a-icon v-if="record.solderColorStr == 'none' "  type="close-square" style="color:#d1cbcb;"></a-icon>
                  {{record.solderColorStr}}                                 
                </template>
                <br/>
                <template>文字颜色:
                  <div v-if="record.fontColorStr.indexOf('绿') != -1 " style="width: 13px;height:13px;background: green;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.fontColorStr.indexOf('红') != -1 " style="width: 13px;height:13px;background: red;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.fontColorStr.indexOf('白') != -1 " style="width: 13px;height:13px;background: white;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.fontColorStr.indexOf('黑') != -1 " style="width: 13px;height:13px;background: black;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.fontColorStr.indexOf('黄') != -1 " style="width: 13px;height:13px;background: yellow;display: inline-block;border:1px solid black"></div>
                  <div v-if="record.fontColorStr.indexOf('蓝') != -1 " style="width: 13px;height:13px;background: blue;display: inline-block;border:1px solid black"></div>
                  <a-icon v-if="record.fontColorStr == 'none' "  type="close-square" style="color:#d1cbcb;"></a-icon>
                  {{record.fontColorStr}}                  
                </template>
              </span>
            </template>
            <template slot="timeInfo" slot-scope="record" >
              <span class="topCss" style="line-height:1.5;">
                <template>拼板时间:{{record.createTime}} </template>  <br/>               
                <template>交期:{{record.deliveryDate}}</template><br/>
                <template>加急:{{record.deliveryDays + "天"}} </template><br/>
              </span>
            </template>    
            <template slot="shipmentInfo" slot-scope="record" >
              <span class="topCss" style="line-height:1.5;">
                <template>尺寸信息:{{record.boardWidth + " * " + record.boardHeight}} </template><br/>                 
                <template>数量:{{record.pinBanNum}}</template><br/>
                <template v-if="record.boardWidth && record.boardHeight && record.PinBanNum">面积:{{((record.boardWidth * record.boardHeight * record.PinBanNum) / 1000000.0).toFixed(2) || "" + " m2"}} </template>
                <template v-else> 面积:{{record.shipArea}} m2</template>
              </span>
            </template> 
            <template slot="flyingProbeStr" slot-scope="record">
              <span class="topCss2" >{{record.flyingProbeStr}} </span> 
            </template>  
            <template slot="operatorUser" slot-scope="record" >
              <span class="topCss" style="line-height:1.5;">
                <template>分配人员:{{record.adminName}} </template> <br/>
                <template>锣带人员: </template><br/>
                <template>测试人员: </template>
              </span>
            </template>  
            <template slot="adminName" slot-scope="record" >
              <span class="topCss">
                <p>{{record.adminName}} </p>                 
              </span>
            </template>    
            <template slot="copyRemark" slot-scope="record" >
              <span class="topCss">
                <p>{{record.copyRemark}} </p>                 
              </span>
            </template>                  
            <template slot="action" slot-scope="record" >
              <span class="topCss">               
                <p style="color:#428bca;" class="noCopy" @click="eqClick(record)">问客</p>              
              </span>
            </template>      
            <template slot="tag" slot-scope="record" >
              <!--      IsHighQuality   0-标品；1-优品；2-精品   int-->
              <!--      IsLock   1锁  bool-->
              <!--      SMTFactoryId  贴片工厂Id，>0为贴片订单  int-->
              <!--      ProductFileSure  >0为需要客户确认文件 int-->
              <!--      IsBigCus  1是大客（KA） bool-->
              <!--      IsUrgent  1加急  bool-->
              <span class="topCss">
              <a-tag color="#2D221D"  v-if="record.isHighQuality != 0">  {{ record.isHighQuality == 1 ? '优' : '精' }}</a-tag>
              <a-tag v-if="record.isLock" color="#2D221D" > 锁  </a-tag>
              <a-tag v-if="record.smtFactoryId"  color="#2D221D" > 贴 </a-tag>
              <a-tag v-if="record.confirmWorkingDraft " color="#2D221D" >确</a-tag>
              <a-tag v-if="record.isBigCus" color="#2D221D" > KA</a-tag>
              <a-tag v-if="record.IsUrgent" color="#2D221D"> 急 </a-tag>
              <a-tag v-if="record.isReverse == '1' || record.isReverse == '2'" color="#2D221D" > 反 </a-tag>
            </span>
            </template> 
          </a-table>
          <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="down11" v-if="showText">复制</a-menu-item>
        </a-menu> 
        </div>
        <div class="bto"></div>
      </div> 
      <a-modal
        title="修改工厂"
        :visible="dataVisible"
        @cancel="reportHandleCancel1"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
       >
       <query-info ref='queryInfo' :factoryData="factoryData" :selectedId="selectedId" />
       </a-modal> 
       <a-modal
          title=" 确认弹窗"
          :visible="dataVisibleMode"
          @cancel="reportHandleCancel1"
          @ok="handleOkMode"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
          centered

      >
      <span style="font-size:16px;">【{{orderno}}】</span> 
      <span style="font-size:16px;">{{messageMode}}</span>   
      </a-modal>     
    </a-spin>
</template>
<script>
import moment from "moment";
import {
  proPinBanOrderEQList,
} from "@/services/scgl/OrderManagement/eq";
import QueryInfo from '@/pages/OrderManagement/EQManagement/QueryInfo.vue'; 
import {checkPermission} from "@/utils/abp";
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",    
    scopedSlots: { customRender: 'num' },
    width: 45,
  },
  {
    title: "拼版编号",
    align: "left",
    width:200,
    scopedSlots: { customRender: 'orderNo' },
    
  },
  // {
  //   title: "订单标记",
  //   key: "tag",
  //   scopedSlots: { customRender: 'tag' },
  //   align: "left",
  //   width: "6%",
  //   class:'noCopy'
  // },
  
  {
    title: "工厂",
    // dataIndex:'joinFactoryIdStr',
    align: "left",   
    width: 75,
    scopedSlots: { customRender: 'joinFactoryIdStr' },
    
  },
  {
    title: "状态",
    align: "left",
    width: 60,   
    scopedSlots: { customRender: 'status' },
  },
  {
    title: "问客状态",
    align: "left",
    width:80,
    // hideInTable:true,
    scopedSlots: { customRender: 'wenke' },
  },
  {
    title: "锣带/测试状态",
    align: "left",    
    width: 120,
    scopedSlots: { customRender: '' },   
  },
  {
    title: "协同确认",
    align: "left",
    width: 90, 
    class:'noCopy' ,
    scopedSlots: { customRender: 'confirmJoin' },         
  },  
  {
    title: "工艺信息",
    width: 130,
    align: "left",
    scopedSlots:{customRender:'Process'}
  },
  {
    title: "时间信息",
    width: 130,
    align: "left",
    scopedSlots:{customRender:'timeInfo'}
  },
  {
    title: "出货信息",
    align: "left",
    width: 120,
    scopedSlots: { customRender: 'shipmentInfo' },
  },
  {
    title: "测试方式",
    width:80,
    align: "left",
    scopedSlots: { customRender: 'flyingProbeStr' },   
  },
  {
    title: "操作人员",
    scopedSlots: { customRender: 'operatorUser' },
    width: 80,
    align: "left",
  },
  {
    title: "审核人员",
    width:80,
    align: "left",
    scopedSlots: { customRender: 'adminName' },
  },
  {
    title: "特殊需求",
    scopedSlots: { customRender: 'copyRemark'},
    width: 90,
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: 'action' },
    align: "left",
    class:"noCopy",
    width: 80,   
  },

]
export default{
    name:'',
    components:{QueryInfo,},
    data(){
        return{
          showText:false,
          menuVisible:false,
          menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex:99
      },
          spinning:false,
          formData:{
            OrderNo:'',
            eqStatus:undefined,
            Status:undefined,
            StartDeliveryDate:'',
            EndDeliveryDate:'',
          },
          columns,
          orderListTableLoading:false,
          pagination: {
            pageSize: 20,
            pageIndex: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
          },
          orderListData:[],
          proOrderId:'',
          selectedRowsData:{},
          dataVisible1:false,
          dataVisible:false,
          factoryData:[],
          filePaths:'',
          selectedId:'',
          dataVisibleMode:false,
          orderno:'',
          businessOrderNo:'',
          messageMode:'',
          pageshow:true,
        }
    },
    mounted(){
      this.getOrderList()
      window.addEventListener('resize', this.handleResize, true)
      // factoryList().then(res=>{
      //   if(res.code){
      //     this.factoryData = res.data
      //   }
      // })
      // if(this.checkPermission('MES.ProManagement.Feeding.WenKe')){
      //   this.columns[5].title = '问客状态'  
      //   this.columns[5].width = 70
      // }else{   
      //   this.columns[5].title = ''  
      //   this.columns[5].width = 0
      // }
    },   
    beforeDestroy(){
    window.removeEventListener('resize', this.handleResize);
   },
  methods: {
    checkPermission,
    moment,
    handleResize(){
      var leftstyle = document.getElementsByClassName('maintable')[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var leftContent = document.getElementsByClassName('leftContent')[0]
      if(leftstyle && this.orderListData.length!=0){
        leftstyle.style.height =  window.innerHeight - 228 +'px'
      }else{
        leftstyle.style.height = 0
      }
      if(window.innerHeight<=911 ){
        leftContent.style.height = window.innerHeight - 188 +'px'
      }else{
        leftContent.style.height = '726px'
      }  
      var footerwidth =  window.innerWidth-224
      var paginnum = ''
        if(Math.ceil(this.pagination.total/20)>10){
          paginnum = 7
        }else{
          paginnum = Math.ceil(this.pagination.total/20)
        }
      if((paginnum*50)+310<footerwidth ){
        this.pagination.simple=false
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
      }
      if(window.innerWidth < 1920){
        if(((paginnum*50)+310)<footerwidth){
        this.pagination.simple=false
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
      }else{
        this.pagination.simple=false
        this.pagination.size = 'small'
        this.pagination.showSizeChanger = false
        this.pagination.showQuickJumper = false
      } 
        if((paginnum*25)< window.innerWidth-150  && window.innerWidth>766){
          if(footerwidth < (paginnum*25) +200){
            this.pagination.simple=true
          }else{
            this.pagination.simple=false
          }
        }else{
          if(window.innerWidth>766){
            if(footerwidth < paginnum*45){
            this.pagination.simple=true
            }else{
              this.pagination.simple=false
            }
          }else{
            this.pagination.simple=true
           }
          }
      }else{
       if(window.innerWidth > 1920 ){
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
        this.pagination.simple=false
      }
     }
    },
    onChange1 (value, dateString) {
      this.formData.StartDeliveryDate = dateString      
    },   
    onChange2 (value, dateString) {
      this.formData.EndDeliveryDate = dateString
    },      
    // 获取订单
    getOrderList(queryData){      
      let params = {
        ...this.pagination,
      }
      var obj = Object.assign(params,queryData)
      console.log('par' ,obj)
      this.orderListTableLoading = true;
      proPinBanOrderEQList(obj).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
          setTimeout(() => {
            this.handleResize();
          },0); 
          this.pagination.total = res.data.totalCount;       
        }
      }).finally(()=> {
        this.orderListTableLoading = false;
      })
    },
    isRedRow(record) {
      let strGroup = []      
      if (record.id && record.id == this.proOrderId) {
        strGroup.push('rowBackgroundColor')
      }      
      return strGroup
    },
    down11(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
      },
    rightClick1(e,text,record){ 
      let event = e.target 
      if(e.target.localName != 'td'){
        event = e.target.parentNode
      }   
      if(e.target.parentNode.localName == 'span'){
        event = e.target.parentNode.parentNode.parentNode
      }  
      if(e.target.localName == 'path' || e.target.localName == 'svg'){
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode
        if(event.className.indexOf('tagNum') != -1){
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode
        }
      } 
      if(e.target.parentNode.localName == 'div'){
        event = e.target.parentNode.parentNode
      }     
      console.log('e.target.parentNode.localName',event.localName)  
      this.text=event.innerText;
      if(event.className.indexOf('noCopy') != -1 || !this.text){
        this.showText = false
      }else{    
        this.showText = true
      } 
      if(event.cellIndex == 1  || event.cellIndex == undefined){
        this.text = this.text.split(" ")[0]
      }
      console.log('this.text',this.text)
      this.menuVisible = true;   
      let  offsetx= event.offsetLeft  + event.offsetWidth - 10
      let offsety = event.offsetTop + 80; 
      const tableWrapper = this.$refs.tableWrapper;    
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();    
      if(event.cellIndex == this.columns.length -1){
        this.menuStyle.top = offsety + "px";
        this.menuStyle.left = offsetx - 50 + "px";
      }else if(cellRect.left >= 1236){
        this.menuStyle.top = cellRect.top - wrapperRect.top  + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left  + "px";
      }else{
        this.menuStyle.top = offsety + "px";
        this.menuStyle.left = offsetx  + "px";
      }      
      document.body.addEventListener("click", this.bodyClick);
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record   
            this.selectedId = this.selectedRowsData.joinFactoryId.toString()      
            this.proOrderId = record.id
          },
          contextmenu: e => {
           let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
            } 
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record)
          
          },
        }
      }
    }, 
    handleTableChange(pagination, ) {
      this.pagination.pageIndex=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      let params = this.formData
      if(params.Status != undefined){
        params.Status = Number(this.formData.Status)
      } 
      this.getOrderList(params)
    }, 
    searchClick(){
      let params = this.formData
      var arr1 = params.OrderNo.split('')
      if(arr1.length >30){
        arr1 = arr1.slice(0,30)
      }
      params.OrderNo = arr1.join('')

      if(params.Status != undefined){
        // params.Status = '0'
        params.Status = Number(this.formData.Status)
      }     
      this.pageshow = false
      this.getOrderList(params)
      this.pagination.pageIndex = 1;
      this.$nextTick(() => {
        this.pageshow = true
      })    
    },
    // 打印流程卡
    PrintClick(){      
      if(!this.proOrderId){
        this.$message.warning('请选择流程卡')
        return
      }
      console.log('打印',this.proOrderId)
      
      this.dataVisible1 = true
    },
    handleCancel2(){
      this.dataVisible1 = false
      this.getOrderList()
     
    },
    // 详情跳转
    details(record){
      this.$router.push({path:'orderDetail1',query:{ id:record.orderNo,businessOrderNo:record.businessOrderNo} ,})
    },
    // 下载TGZ
    TGZdown(record){
      let URL = process.env.VUE_APP_API_BASE_URL
      this.spinning = true
      pinbanFilePath(record.orderNo,'OssTgzPath',record.businessOrderNo).then(res=>{
        if(res.code){
          console.log(URL + '/' + res.data)
          if(res.data){
            window.location.href = res.data
          }else{
            this.$message.error('暂无TGZ文件')
          }         
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
            this.spinning = false
          })
    },
    down(record){
      let URL = process.env.VUE_APP_API_BASE_URL
      this.spinning = true
      pinbanFilePath(record.orderNo,'OssYgPath',record.businessOrderNo).then(res=>{
        if(res.code){        
          if(res.data){
            window.location.href = res.data
          }else{
            this.$message.error('暂无原稿文件')
          }         
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
            this.spinning = false
          })
    },
    // 下载其他
    down1(record){
      let URL = process.env.VUE_APP_API_BASE_URL
      this.spinning = true
      pinbanFilePath(record.orderNo,'OssOthPath',record.businessOrderNo).then(res=>{
        if(res.code){
          if(res.data){
            window.location.href = res.data
          }else{
            this.$message.error('暂无其他文件')
          } 
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
            this.spinning = false
          })
    },
    // 下载图纸文件
    down5(record){
      let URL = process.env.VUE_APP_API_BASE_URL
      this.spinning = true
      pinbanFilePath(record.orderNo,'OssMapPath',record.businessOrderNo).then(res=>{
        if(res.code){
          if(res.data){
            window.location.href = res.data
          }else{
            this.$message.error('暂无图纸文件')
          } 
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
            this.spinning = false
          })
    }, 
    down9(record){
      this.orderno = record.orderNo
      this.businessOrderNo = record.businessOrderNo
      this.messageMode = '确认协同确认?'
      this.dataVisibleMode = true
    },
    eqClick(record){
      let OrderNo = record.id
      let OrderNo2= record.orderNo
      const routeOne = this.$router.resolve({
              path: '/gongcheng/eqDetails',
              query:{
                OrderNo:OrderNo,
                eQSource:4,
                OrderNo2:OrderNo2,
                businessOrderNo:record.businessOrderNo,
                joinFactoryId:record.joinFactoryId,
                Jump:'EQ列表'
              }
            })
        window.open(routeOne.href, "_self",routeOne.query)
    },
    reportHandleCancel1(){
      this.dataVisible = false;
      this.dataVisibleMode = false;
    },
    handleOkMode(){
      let params={
        "orderNo": this.orderno,
        "businessOrderNo": this.businessOrderNo
      }
      setXtSure(params).then(res=>{
        if(res.code){
          this.$message.success('确认成功')
          this.getOrderList()
        }else{
          this.$message.error(res.message)
        }
      })
      this.dataVisibleMode = false;
    },
    ModifyFactory(){
      if(!this.proOrderId){
        this.$message.warning('请选择订单')
        return
      }
      this.dataVisible = true;
      
    },
    handleOk(){
      let params = {
        'id':this.proOrderId,
        "joinFactoryId":Number(this.$refs.queryInfo.form.joinFactoryId)
      }
      setFactory(params).then(res=>{
        if(res.code){
          this.$message.success('修改成功')
          this.selectedId = this.$refs.queryInfo.form.joinFactoryId
          this.proOrderId = ''
          this.getOrderList()

        }else{
          this.$message.error(res.message)
        }
      })
      this.dataVisible = false;
    },  
    uploadClick(){
      if(!this.proOrderId){
        this.$message.warning('请选择订单')
        return
      }     
      this.$refs.fileup.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent('click')) 
    },
     beforeUpload(file){
      const isFileType = file.name.toLowerCase().indexOf('.rar') != -1 || file.name.toLowerCase().indexOf('.zip') != -1
      console.log('file',file)
      if (!isFileType) {
        this.$message.error('只支持.rar或.zip格式文件');
      }
      return isFileType
    },
    async httpRequest0(data,type) {
      this.spinning = true
      const formData = new FormData();
      formData.append("file", data.file);     
      await upLoadFile(formData).then(res=>{
        if(res.code){        
          let params = {
            "orderNo": this.selectedRowsData.orderNo,
            "businessOrderNo": this.selectedRowsData.businessOrderNo,
            "typeKeys": "OssOthPath",
            "filePaths": res.data,
          }
          setPinbanFilePath(params).then(res=>{
            if(res.code){
              this.$message.success('上传成功')              
            }else{
              this.$message.error(res.message)
            }
          }).finally(()=>{
            this.spinning = false
          })
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.spinning = false
      })
     
    },     
  }
}
</script>
<style scoped lang="less">
 .tabRightClikBox{
      // border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color:#000000
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
}

/deep/.ant-input{
  font-weight:500
}
 /deep/.ant-tag {
    font-size: 12px;    
     color:#ff9900;
     padding: 0 2px; 
     margin: 0; 
     margin-right: 3px; 
     height: 21px;
     user-select: none;
  }
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
.projectBackend {
  padding-top:10px;
  .content{
    height:44px;
    padding-left:6px;
    background: #FFFFFF;
    /deep/.ant-select-selection__placeholder{
      display: block;
    }
  } 
  /deep/.userStyle{
    user-select: all!important;
  }
  // height: 834px;
  width: 100%;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  /deep/.leftContent {
    background: #FFFFFF;
    user-select: none;
    .min-table {      
      .ant-table-body{
        min-height:680px;
      }
    }    
    
    height:718px;
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }
  .bto{
  height:55px;
  background: #FFFFFF;
  border:2px solid #E9E9F0;
  border-top: none;
}
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table-thead > tr > th {
    // padding:3px 0!important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table{
    .ant-table-thead > tr > th{
      padding: 3px 4px;
      // border-color: #f0f0f0;
      border-right:1px solid #efefef;
    }    
    .ant-table-tbody > tr > td {
      padding: 3px 4px!important;
      border-right:1px solid #efefef;
      // border-color: #f0f0f0;
      position:relative;
      .topCss{
        position:absolute;
        top:3%;        
      }
      .topCss1{
        position:absolute;
        top:3%;
        // left:30%;
        
      }
      .topCss2{
        position:absolute;
        top:3%;
        // left:25%;
        
      }
    }
    tr.ant-table-row-selected td {
     background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 14px 0 0 10px;
  }

}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background:#F8F8F8;
  // background: #F0F2F5;
}
</style>
