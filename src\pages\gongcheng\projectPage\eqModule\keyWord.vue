<template>
  <div ref="SelectBox">
    <a-form>
      <a-row>
        <a-col :span="5">
          <a-form-item label="问题类型" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-select
              ref="select"
              v-model="StepName"
              allowClear
              showSearch
              optionFilterProp="lable"
              @change="onClick"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in selectData1" :key="index" :value="item.text" :lable="item.text"
                >{{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="问题描述" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
            <a-input v-model="Problem" allowClear @keyup.enter="onClick"> </a-input>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="问题简述" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
            <a-input v-model="KeyWord" allowClear @keyup.enter="onClick"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item>
            <a-button type="primary" style="margin-left: 40px" @click="onClick"> 搜索</a-button>
            <a-button type="primary" style="margin-left: 10px" @click="reClick"> 取消搜索</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-table
      :columns="columns1"
      rowKey="id"
      :pagination="false"
      :dataSource="showData"
      :loading="TableLoading"
      :scroll="{ x: 700, y: 400 }"
      bordered
      :customRow="onClickRow"
      :rowClassName="isRedRow"
      :class="showData.length ? 'min-table' : ''"
    >
      <template slot="english" slot-scope="text, record">
        <a-checkbox :checked="record.problemDescription_en ? true : false"></a-checkbox>
      </template>
    </a-table>
  </div>
</template>

<script>
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "有无英文",
    align: "center",
    width: 70,
    className: "userStyle",
    scopedSlots: { customRender: "english" },
  },
  {
    title: "问题类型",
    dataIndex: "stepName",
    align: "left",
    // ellipsis: true,
    width: 80,
    className: "userStyle",
  },
  {
    title: "问题简述",
    dataIndex: "keyWord",
    align: "left",
    width: 150,
  },
  {
    title: "问题描述",
    dataIndex: "problemDescription",
    align: "left",
    // ellipsis: true,
    width: 200,
    className: "userStyle",
  },
  {
    title: "建议A",
    dataIndex: "proposalA",
    align: "left",
    // ellipsis: true,
    width: 200,
  },
  {
    title: "建议B",
    dataIndex: "proposalB",
    align: "left",
    // ellipsis: true,
    width: 200,
  },

  {
    title: "建议C",
    width: 200,
    dataIndex: "proposalC",
    // ellipsis: true,
    align: "left",
  },
];
import { eqQuestion, BigClassList } from "@/services/projectMake";
export default {
  name: "keyWord",
  props: ["selectData1", "StepName1", "eqQuestion"],
  data() {
    return {
      StepName: "",
      KeyWord: "",
      Problem: "",
      showData: [],
      columns1,
      TableLoading: false,
      id: "",
      selectData: {},
    };
  },
  created() {},
  mounted() {
    if (this.StepName1) {
      this.selectData1.forEach(item => {
        if (item.valueMember == this.StepName1 || item.text == this.StepName1) {
          this.$set(this, "StepName", item.text);
        }
      });
    }
    if (this.StepName) {
      this.onClick();
    }
    if (this.eqQuestion.length) {
      this.showData = this.eqQuestion;
    }
  },
  methods: {
    reClick() {
      let params = {
        Factoryid: this.$route.query.joinFactoryId,
      };
      this.StepName = "";
      this.Problem = "";
      this.KeyWord = "";
      this.TableLoading = true;
      eqQuestion(params)
        .then(res => {
          if (res.code) {
            this.showData = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.TableLoading = false;
        });
    },
    onClick() {
      if (!this.StepName && !this.Problem && !this.KeyWord) {
        this.$message.error("请输入搜索条件进行搜索");
        return;
      }
      if (this.StepName || this.Problem || this.KeyWord) {
        let params = {
          StepName: this.StepName,
          Problem: this.Problem,
          KeyWord: this.KeyWord,
          Factoryid: this.$route.query.joinFactoryId,
        };
        this.TableLoading = true;
        eqQuestion(params)
          .then(res => {
            if (res.code) {
              this.showData = res.data;
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.TableLoading = false;
          });
      }
    },
    // 表格行点击事件
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.id = record.id;
            this.selectData = record;
            console.log(record, "174");
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && record.id == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
  },
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-table-column-title {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-form-item {
  margin-bottom: 0 !important;
}
/deep/.ant-table-thead > tr > th {
  padding: 0 4px !important;
}
/deep/.ant-table-tbody > tr > td {
  padding: 0 4px !important;
}
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}

//.min-table{
//  .ant-table-body{
//    min-height:200px;
//  }
//}
</style>
