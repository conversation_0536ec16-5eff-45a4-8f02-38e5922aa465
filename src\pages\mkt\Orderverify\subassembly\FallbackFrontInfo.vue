<template>
  <div class='ChargebackMake'>
    <a-form :label-col="{ span:2 }" :wrapper-col="{ span: 18}" >
      <a-form-item label="备注" >
        <a-textarea :rows="4"  v-model="BackRemark"/>
      </a-form-item>         
    </a-form>
  </div>
</template>


<script>
export default {
    name:'FallbackFrontInfo',    
  data() {
    return {      
      BackRemark:''
    };
  },
  created(){
    // console.log('this.selectedRowsData:',this.selectedRowsData)
  },
  methods: {  
    
   
    
  },
 
};
</script>
<style scoped lang="less">
.ChargebackMake{
 /deep/ .ant-form{
    .ant-form-item{
      margin: 0!important;
    }
  }
 .pictureListSty{
   width: 400px !important;
   margin-left: 50px;
    /deep/ .ant-upload-list-item-actions{
    a{
      display: none;
    }
  }
 }
  /deep/ .ant-checkbox-wrapper{
    margin-left: 0!important;
  }
}

</style>