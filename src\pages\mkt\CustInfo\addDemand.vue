<!-- 市场管理 - 客户管理详情 - 特殊要求弹窗 -->
<template>
    <div ref="SelectBox" @click="bodyClick">
        <a-modal
            :title="model.id? '编辑' : '新增'"
            :width="600"
            :visible="visible"
            :confirmLoading="confirmLoading"
            @ok="handleOk"
            @cancel="handleCancel"
            ok-text="确定"
            cancel-text="取消"
            centered
            :mask="false"  
            :maskClosable="false"
    >
        <a-spin :spinning="confirmLoading">
            <div @click="bodyClick">
                <a-form-model
                        ref="ruleForm"
                        :model="form"
                        :rules="rules"
                        :label-col="labelCol"
                        :wrapper-col="wrapperCol"
                >
                    <a-form-model-item  label="要求类别" ref="category_" prop="category_" :labelCol="{span: 5}" :wrapperCol="{span: 17, }">
                        <a-select  v-model="form.category_" showSearch allowClear  :getPopupContainer="()=>this.$refs.SelectBox">
                            <a-select-option value="">
                                请选择
                            </a-select-option>
                            <a-select-option value="文件">
                                文件
                            </a-select-option>
                            <a-select-option value="板材">
                                板材
                            </a-select-option>
                            <a-select-option value="线路">
                                线路
                            </a-select-option>
                            <a-select-option value="阻焊">
                                阻焊
                            </a-select-option>
                            <a-select-option value="字符">
                                字符
                            </a-select-option>
                            <a-select-option value="包装">
                                包装
                            </a-select-option>
                            <a-select-option value="工程备注">
                                工程备注
                            </a-select-option>
                            <a-select-option value="客户要求">
                                客户要求
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item label="要求简述" ref="askToTell_" prop="askToTell_" :labelCol="{span: 5}" :wrapperCol="{span: 17, }">
                        <a-input v-model="form.askToTell_" allowClear />
                    </a-form-model-item>
                    <a-form-model-item label="要求详情" ref="askDetails_" prop="askDetails_" :labelCol="{span: 5}" :wrapperCol="{span: 17, }">
                        <a-input v-model="form.askDetails_" allowClear />
                    </a-form-model-item>
                    <a-form-model-item  label="上传图片"  ref="imgs" prop="imgs" :labelCol="{span: 5}" :wrapperCol="{span: 17, }">
                        <div class="clearfix">
                            <a-upload
                                    accept=".image/jpeg,image/png,image/gif,image/bmp,image/jpg"
                                    :action="`${baseURL}/api/file-management/files/supplieruploadpfile`"
                                    list-type="picture-card"
                                    :multiple="true"
                                    :file-list="fileList"
                                    :before-upload="beforeUpload"
                                    @preview="handlePreview"
                                    @change="handleChange"
                                    :customRequest="downloadFilesCustomRequest"
                            >
                                <div>
                                    <!-- <a-icon type="plus" />
                                    <div class="ant-upload-text">
                                        Upload
                                    </div>
                                    <a-button style="font-weight:500;padding:4px;height:28px;margin-top:2px"  @click.stop="showCopy(1)" title="ctrl+V 粘贴上传"> 
                                        粘贴图片
                                    </a-button>   -->
                                    <a-button style="font-weight:500;height:40px;">
                                        上传图片
                                    </a-button>
                                    <a-button style="font-weight:500;margin-top:6px;height:40px;" @click.stop="showCopy(1)" title="ctrl+V 粘贴上传" > 
                                        粘贴图片
                                    </a-button> 
                                </div>
                            </a-upload>
                            <a-modal 
                            :visible="previewVisible" 
                            :footer="null" 
                            @cancel="handlecancel"
                            centered>
                                <img alt="example" style="width: 100%" :src="previewImage" />
                            </a-modal>
                        </div>
                    </a-form-model-item>
                </a-form-model>
            </div>
        </a-spin>
    </a-modal>
    </div>
</template>

<script>
import axios from 'axios'
    import { addFactory, getPhoto } from "@/services/supplier/index";
    import {saveAsk,uploadImg,amendAsk,uploadFile} from '@/services/mkt/CustInfoNew'
function getBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

export default {
name: "addDemand",
props: {
    demandId: {
        type: String,
        default () {
            return ''
        }
    }
},
data(){
    return{
        baseURL: process.env.VUE_APP_API_BASE_URL,
        isCtrlPressed:false,
        labelCol: { span: 7 },
        wrapperCol: { span: 17 },
        visible: false,
        confirmLoading: false,
        form: {
            category_: "",
            askToTell_:"",
            askDetails_:"",
            picUrl: "",
        },
        rules: {
            category_: [
                { required: true, message: "类别必须填写", trigger: "blur" },
            ],
            askToTell_: [
                { required: true, message: "简述必须填写", trigger: "blur" },
            ],
            askDetails_: [
                { required: true, message: "详情必须填写", trigger: "blur" },
            ],
        },
        uploading: false,
        model: '',
        previewVisible: false,
        previewImage: '',
        fileList: [],
        imgList:'',
        showCopyType:'',
    }
},
methods: {
    keydown(e){
    if (e.key === 'Control') {
    this.isCtrlPressed = true;
    }
    if(e.keyCode == '13' && this.visible){
      e.preventDefault()
      this.handleOk()
      this.isCtrlPressed = false;
     }
   },
    keyup(e){
    if (e.key === 'Control') {
     this.isCtrlPressed = false;
    }  
    },
    handlecancel({ fileList },data) {
        // this.fileList = fileList;
        this.previewVisible = false;
    },
    beforeUpload(file) {
        const _this = this
        return new Promise(function(resolve, reject) {
            const isJpgOrPng = file.type.toLowerCase() === 'image/jpeg' || file.type.toLowerCase() === 'image/png' || file.type.toLowerCase() === 'image/gif' || file.type.toLowerCase() === 'image/bmp' || file.type.toLowerCase() === 'image/jpg';
            if (!isJpgOrPng) {
            _this.$message.error('图片只支持|*.jpg;*.png;*.gif;*.jpeg;*.bmp 格式');
            reject()
            } else {
            resolve()
            }
        })
    },
    // 上传图片路径
    downloadFilesCustomRequest(data){
    const formData = new FormData()
    formData.append('file', data.file)
    uploadFile(formData).then(res =>{
        if (res.code == 1) {
        data.onSuccess(res.data);
        this.form.picUrl = this.fileList.map(item => {return item.response}).join(',')
        }
        else {
        this.$message.error(res.message)
        }
    })
},
    async handlePreview(file) {
        if (!file.url && !file.preview) {
            file.preview = await getBase64(file.originFileObj);
        }
        this.previewImage = file.url || file.preview;
        this.previewVisible = true;
    },
    handleChange({ fileList },data) {
        if(!fileList){        
            fileList = data.concat(this.fileList)
        } 
        this.fileList = fileList;
        this.form.picUrl = this.fileList.map(item => {return item.response}).join(',')
    },
    openModal(model) {
        this.visible = true;
        this.model = model
        this.fileList = []
        if(model && model.id) {
            this.form = {
                id:model.id,
                askDetails_: model.askDetails_,
                askToTell_:model.askToTell_,
                category_:model.category_,
            };
            if(model.image_!=''){
                let list=model.image_.split(',')
                list.forEach((e,index)=>{
                    let info = {
                        uid:index,
                        name: 'image.png',
                        status: 'done',
                        url: e
                    }
                    this.fileList.push(info)
                })
            }
        }else {
            this.form ={
                category_: '',
            };
        }
    },
    handleCancel() {
        this.visible = false;
        this.currentStep = 0;
        this.$refs.ruleForm.resetFields()
    },
    handleOk() {
        const form = this.$refs.ruleForm;
        this.confirmLoading = true;
        form.validate((valid) => {
            if (valid) {
                    let str = ''
                    this.fileList.forEach(e=> {
                        if(e.response) {
                            str += e.response + ','
                        }else if(e.url) {
                            str += e.url + ','
                        }
                    })
                    str = str.substring(0,str.length-1)
                this.imgList=str
                let params = {
                    pGuid_: this.demandId,
                    category_: this.form.category_,
                    askToTell_:this.form.askToTell_,
                    askDetails_:this.form.askDetails_,
                    image_:str
                }

                if( this.model  &&  this.model .id){
                    params.id= this.model.id
                    amendAsk(params).then(res=>{
                            this.visible = false;
                            form.resetFields();
                            this.$message.info(res.message)
                            this.$emit("ok");
                    })
                        .finally(() => {
                            this.confirmLoading = false;
                        });

                }else {
                    saveAsk(params) .then((res) => {
                            this.visible = false;
                            form.resetFields();
                            this.$message.info("操作成功");
                            this.fileList = []
                            this.$emit("ok");
                        })
                        .finally(() => {
                            this.confirmLoading = false;
                        });
                }



            } else {
                this.confirmLoading = false;
            }
        });
    },
    showCopy(type) {     
window.addEventListener('paste', this.getClipboardFiles);    
this.showCopyType = type;
try{    
navigator.clipboard.read().then( res=>{
    const clipboardItems = res 
    if (clipboardItems[0].types[0].indexOf('image') > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then( b=>{
            const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {type: clipboardItems[0].types[0]}); 
            this.file = files
            this.getClipboardFiles()                
        });
        }else{
        this.$message.error('粘贴内容不是图片');
        return;
        }
    })
    }catch (e) {
        //console.log('出错了')        
    }
    },    
    bodyClick(){
    //console.log('bodyClick')
    window.removeEventListener('paste', this.getClipboardFiles);
    },    
    getClipboardFiles(event) {
    let file = null;
    if(event){
    const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
        // 检索剪切板items,类数组，不能使用forEach
        for (let i = 0; i < items.length; i += 1) { 
            if (items[i].type.indexOf('image') !== -1) {
            file = items[i].getAsFile();
            }
        }
    } 
    }else{
        file = this.file
    }
    if (!file) {
        this.$message.error('粘贴内容不是图片');
        return;
    }
    const fileSize = file.size / 1024 / 1024 < 10;
    if (!fileSize) {
        this.$message.error('请上传小于10M,并且格式正确的图片');
        return;
    }
    this.beforeUpload(file);   // 上传组件自带的函数，这里有些图片校验规则
    if (this.beforeUpload(file)) {  // **** return true 之后进行上传
        this.startloading = true;
        const formData = new FormData();
    // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append('file', file);
        axios({
        url: process.env.VUE_APP_API_BASE_URL + '/api/app/e-mSCust-module-no/up-load-file',   
        method: 'post',
        data: formData,
        //headers: this.headers,  // 请求头
        })
        .then(res => { 
            if (res.code) {
            file.status = 'done';
            file.response = res.data;
            file.thumbUrl = res.data;
            file.url = res.data;
            file.uid = new Date().valueOf();
            const arr = [];
            arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
            });
            if(this.showCopyType == '1'){
                this.handleChange(file, arr); 
            } 
            } else {
            this.$message.error(data.message || '网络异常,请重试或检查网络连接状态');
            
            }
        })
        .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
        });
    }
    },
},
mounted(){
    window.addEventListener('keydown',this.keydown, true)
    window.addEventListener('keyup',this.keyup, true) 
  },
  beforeDestroy(){
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener('paste', this.getClipboardFiles);
   },
}
</script>

<style scoped lang="less">
/deep/.ant-modal-title{
    color: #000000;
}
/deep/.ant-form-item label{
    color: #000000;
}
/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  color:#000000;
}
/deep/.ant-input{
    font-weight:500;
    color:#000000;    
}
/deep/.ant-select{
    font-weight:500;
    color:#000000;
}

</style>