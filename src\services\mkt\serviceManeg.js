import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'

export async function getList(params) {
    return request("/api/app/edit-pcb-order-info/page-list", METHOD.GET,params)
}
export async function pcborderinfostart(Id) {
    return request(`/api/app/edit-pcb-order-info/start/${Id}`, METHOD.POST)
}
export async function pcborderinfoend(params) {
    return request("/api/app/edit-pcb-order-info/end", METHOD.POST,params)
}
export async function orderrefuse(params) {
    return request("/api/app/edit-pcb-order-info/order-refuse ", METHOD.POST,params)
}
export async function getOrderInfo(Id) {
    return request(`/api/app/edit-pcb-order-info/${Id}/pcb-order-info`, METHOD.GET)
}



export default {
    getList,
    pcborderinfostart,
    pcborderinfoend,
    orderrefuse
}