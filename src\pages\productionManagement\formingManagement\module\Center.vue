<!-- 车间管理-成型管理-右边数据 -->
<template>
  <a-card :bordered="false">
    <div class="machine" style="border: 1px solid #E9E9F0;">
      <div class="left" style="border: 1px solid #E9E9F0;" @contextmenu.prevent="rightClick($event)">
        <a-table
            rowKey="iD_"
            :columns="columns"
            :dataSource="tableData"
            :pagination="false"
            :loading="table3Loading"
            :keyboard="false"
            :bordered="true"
            :maskClosable="false"
            :scroll="{ x: 800, y: 744 }"
            :customRow="eventTouch"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange,getCheckboxProps: getCheckboxProps}"
            :rowClassName="setRowClassName"
            :class="{'minClass':tableData.length > 0}" 
        >
        </a-table>
        <a-menu :style="menuStyle" v-if="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="signError">{{statusText}}</a-menu-item>
        </a-menu>
      </div>
      <div class="right" style="border: 1px solid #E9E9F0;">
        <div class="top" style="border-bottom: 3px solid #E9E9F0;">
          <a-table
              :rowKey="(record,index)=>{return index}"
              :columns="columns2"
              :dataSource="machineStatuList"
              :pagination="false"
              :loading="machineStatuLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
          >
          </a-table>
        </div>
        <div class="bot">
          <!-- 钻刀列表 -->
          <a-table
              v-if="showDrill"
              :rowKey="(record,index)=>{return index}"
              :columns="columns3"
              :dataSource="drillList"
              :pagination="false"
              :loading="drillLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :scroll="{y:544 }"
          >
          </a-table>
          <!-- 分配列表 -->
          <a-table
              v-if="showAssign"
              :rowKey="(record,index)=>{return index}"
              :columns="columns4"
              :dataSource="dispatchList"
              :pagination="false"
              :loading="dispatchLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
              :scroll="{ x: 300,y:685 }"              
              
          >
          <span slot="pnlQty_" slot-scope="tetx,record">
           <a-input v-model="record.pnlQty_" @blur="savePnl(record)"/>
         </span>
          <template slot="name" slot-scope="text, record" v-if="checkPermission('MES.ProductionModule.Forming.FormingBackSend')">
            <a-button @click="onSelectChange1(record)">回退</a-button>
          </template>
          </a-table>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import {signMachineStatus, updatePnl} from "@/services/forming-management";
import { checkPermission } from "@/utils/abp";

const columns2 = [

  {
    title: "状态",
    dataIndex: "state_",
    width: 50,
    align: 'center',
  },
  {
    title: "机台数",
    dataIndex: "muCount_",
    width: 60,
    align: 'center',
  },
  {
    title: "面积(㎡)",
    dataIndex: "area_",
    align: 'center',
    customRender: (text, record) => {
      return text.toFixed(2)
    }
  },
  {
    title: "PNL数",
    dataIndex: "pnL_",
    width: 65,
    align: 'center',
  },
  {
    title: "款数",
    dataIndex: "count_",
    width: 50,
    align: 'center',
  },
]
const columns3 = [
  {
    title: "锣刀号",
    dataIndex: "tool_",
    width: 50,
    align: 'center',
  },
  {
    title: "刀径",
    dataIndex: "type_",
    width: 50,
    align: 'center',
  },
  {
    title: "类型",
    dataIndex: "size_",
    width: 50,
    align: 'center',
  },
  {
    title: "锣程(m)",
    dataIndex: "routPath_",
    width: 50,
    align: 'center',
  },
  {
    title: "层名",
    dataIndex: "layerName_",
    width: 50,
    align: 'center',
  },
]
const columns4 = [  
  {
    title: "本厂编码",
    dataIndex: "pdctno_",    
    width: 120,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "pnl数",
    dataIndex: "pnlQty_",
    ellipsis: true,
    width: 45,
    align: 'center',
    scopedSlots: { customRender: 'pnlQty_' },
  },   
  {
    title: "分派时间",
    dataIndex: "sendDate_",
    width:130,
    align: 'center',
   
  }, 
  {
    title: "操作",    
    width: 60,
    align: 'center',
    // fixed: 'right',
    scopedSlots: { customRender: 'name' },
  },
]
export default {
  name:'Center',
  props:{
    tableData:{
      type: Array
    },
    table3Loading:{
      type: Boolean
    },
    machineStatuList:{
      type: Array
    },
    machineStatuLoad:{
      type: Boolean
    },
    drillList:{
      type: Array
    },
    drillLoad:{
      type: Boolean
    },
    dispatchList:{
      type: Array
    },
    copyDispatchList:{
      type:Array
    },
    dispatchLoad:{
      type: Boolean
    },
    idList:{
      type: Array
    }
  },
  data () {
    return {
      columns: [        
        {
          title: "机台",
          dataIndex: "caption_",
          width: 65,
          align: 'center',
          fixed: 'left',
          customCell: (record, rowIndex) => {
            if(record.caption_1 == "1") {
              return { style: { 'background': '#595959', } }
            }else if(this.idList.indexOf(record.iD_) != -1) {
              return { style: { 'background': '#ff9900', } }
            }
          },
        },
        {
          title: "轴",
          dataIndex: "num_",
          width: 35,
          align: 'center',
          customCell: (record, rowIndex) => {
              return { style: { 'background': record.color_1 || 'rgba(0, 0, 0, 0.65)', } }
          },
        },
        {
          title: "资料",
          dataIndex: "numMachine_",
          width: 40,
          align: 'center',
        },
        {
          title: "负责人",
          dataIndex: "listParams_",
          width: 60,
          align: 'center',
          // customCell: (record, rowIndex) => {
          //   if(record.nowPdc && record.nowPdc.indexOf('异常') != -1) {
          //     return { style: { 'background': 'red', } }
          //   }
          // },
        },
        {
          title: "品牌",
          dataIndex: "machineType_",
          width: 62,
          align: 'center',
        },
        {
          title: "台面（1）",
          dataIndex: "nowPdc",
          ellipsis: true,
          align: 'center',
        },
        {
          title: "台面（2）",
          dataIndex: "nowPdc_1",
          ellipsis: true,
          align: 'center',
        }
      ],
      columns2,
      columns3,
      columns4,
      showDrill:false,
      showAssign:false,
      selectedRowKeys: [],
      rowId:'',
      rowId1:'',
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      menuVisible:false,
      menuData:[],
      statusText: "标记故障"
    }
  },
  methods:{
    checkPermission,
    eventTouch(record,index) {
      return {
        props: {},
        on: { // 事件
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (record.caption_1 !=1 && record.listParams_) {
              if (rowKeys.length > 0 && rowKeys.includes(record.iD_)) {
                rowKeys.splice(rowKeys.indexOf(record.iD_), 1);
              } else {
                rowKeys.push(record.iD_);
              }
            }
            this.selectedRowKeys = rowKeys;
          },
          dblclick: () => { //双击
            this.flagClick('assign')
            this.$emit('getAssignmentList',record.iD_)
            this.rowId =  record.iD_
            this.$emit('childClick')
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
          }
        }
      }
    },    
    setRowClassName (record)  {
      return record.iD_ === this.rowId ? 'clickRowStyl' : ''
    },
    flagClick(type){
      if(type == "assign"){
        this.showDrill = false;
        this.showAssign = true
      } else {
         this.showDrill = true;
        this.showAssign = false
      }
    },
    //选择待分派机台
    onSelectChange(selectedRowKeys, selectedRows) {
      const newSelectedRowKeys=[], newSelectedRows=[];
      selectedRows.forEach(item => {
        if (item.caption_1 != 1) {
          newSelectedRowKeys.push(item.iD_)
          newSelectedRows.push(item)
        }
      })
      this.selectedRowKeys = newSelectedRowKeys;
      this.selectedRows = newSelectedRows
    }, 
    //选择待回退
    onSelectChange1(record){
      this.rowId1 = record.guid_
      this.$emit('getDispatchFallback',{"guid_":this.rowId1, "iD_":this.rowId})
    },
    // 修改pnl数
    savePnl(record){  
      let paramsData ={
        "id":record.guid_,
        "qty": record.pnlQty_
      }       
        updatePnl(paramsData).then(res => {
        if (res.code == 1) {
          this.$message.success('成功')
        } else {
          this.$message.error(res.message)
        }

      })
    },
    
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e){
      if(this.menuData.caption_1 == 1) {
        this.statusText = '取消故障'
      } else {
        this.statusText = '标记故障'
      }
      this.menuVisible = true;
      this.menuStyle.top = e.clientY- 110 +  "px";
      this.menuStyle.left = e.clientX -
          document.getElementsByClassName('fixed-side')[0].offsetWidth -
          document.getElementsByClassName('left')[0].offsetWidth + "px";
          document.body.addEventListener("click", this.bodyClick);
          
    },
    signError(){
      signMachineStatus(this.menuData.iD_).then(res => {
        if(res.code == 1) {
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
        this.$emit('getMesaList')
      })
    },
    getCheckboxProps(record){
      return ({
        props: {
          disabled: record.caption_1 == 1 
        },
      })
    }
  }
}
</script>

<style lang="less" scoped>
.machine {
  height: 100%;
  display: flex;
  .left {
    .minClass {
    /deep/ .ant-table-body {
      min-height: 744px;
    }
  }
    width: 50%;
    height:779px;
    .tabRightClikBox {
      border:2px solid rgb(238, 238, 238) !important;
      .ant-menu-item {
        height: 28px;
        line-height: 28px;
        margin: 0;
        // background: #ff9900;
        // font-size: 14px;
        // width: 100px;
        // text-align: center;
        // font-weight: 500;
        // &:hover{
        //   color: rgba(0, 0, 0, 0.65);
        // }
      }
    }
  }
  .right {
    width: 50%;
    .bot,.top {
      /deep/ .ant-table-body {
        min-height: 0;        
      }
    }
    .top {
      /deep/ .ant-table-wrapper .ant-table-thead tr th {
        padding: 5px 0
      }
    }
    button{
      padding:0  5px;
      border-radius: 6px;
      height: 24px;
      line-height: 24px;
      background-color: #ff9900;
      color:white;
      
    }
  }
  /deep/ .ant-table-tbody {
    .ant-table-row{
      td:first-child{
        user-select: all;
      }
    }
    .clickRowStyl {
      background: #fff9e6;
    }
    .ant-input {
      padding: 0;
      border: 0;
      border-radius: 0;
      margin: -5px 0;
      text-align: center;
    }
  }
  
}


</style>