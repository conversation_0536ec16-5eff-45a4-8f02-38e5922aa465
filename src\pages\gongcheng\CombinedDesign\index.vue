<!-- 工程管理 - 合拼设计  -->
<template>
  <a-spin :spinning="spinning">
    <div class="CuttingManagement">
      <div class="content">
        <div class="left" ref="tableWrapper" style="width: 68%">
          <span class="iconstyle1">
            <a-tooltip style="position: relative; top: 4px" @click="caretleft" v-if="Foldleftandright" title="展开合拼明细表"
              ><a-icon type="unordered-list"></a-icon
            ></a-tooltip>
            <a-tooltip style="position: relative; top: 4px" @click="caretright" v-if="!Foldleftandright" title="折叠合拼明细表"
              ><a-icon type="unordered-list"></a-icon
            ></a-tooltip>
          </span>
          <a-card :bordered="false" style="border: 1px solid #e9e9f0">
            <vxe-table
              border
              stripe
              show-overflow
              class="toptable"
              ref="xTable1"
              :height="left1height"
              :data="data1Source"
              :loading="table1Loading"
              :row-config="{ isHover: true, height: 36, keyField: 'guild_' }"
              @current-change="currentChangeEvent"
              :loading-config="{ icon: 'vxe-icon-spinner roll', text: ' ' }"
              :scroll-y="{ enabled: true }"
              @visibleMethod="visibleMethod"
              :menu-config="{ enabled: true }"
              @cell-menu="cellContextMenuEvent"
              :row-class-name="rowClassName"
              @cell-mouseleave="mouseleave"
              :tooltip-config="{ enterable: true }"
              @sort-change="sortChangeEvent"
              :sort-config="{ trigger: 'cell' }"
            >
              <vxe-column type="seq" title=" " width="45" style="text-align: center" align="center" fixed="left"></vxe-column>
              <vxe-column field="pdctno_" title="生产编号" width="195" show-overflow fixed="left" sortable>
                <template #default="{ row }">
                  <div style="display: flex; align-items: center">
                    <span v-if="row.isNotNeedHP" style="color: red" :title="row.pdctno_">{{ row.pdctno_ }}</span>
                    <span v-else style="color: black" :title="row.pdctno_">{{ row.pdctno_ }}</span
                    >&nbsp;
                    <span class="tagNum" style="display: inline-block; height: 19px">
                      <a-tooltip title="极限加急" v-if="row.isExtremeJiaji">
                        <span
                          class="noCopy"
                          style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
                          >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                        </span>
                        <span
                          class="noCopy"
                          style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
                          >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                        </span>
                      </a-tooltip>
                      <a-tooltip title="加急" v-else-if="row.extraUrgent">
                        <span
                          class="noCopy"
                          style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
                          >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                        </span>
                      </a-tooltip>
                      <a-tooltip title="ECN改版不升级" v-if="row.customClassification == 'ECN改版不升级' && row.isReOrder_ == 1">
                        <a-tag
                          style="
                            font-size: 12px;
                            background: #428bca;
                            color: white;
                            padding: 0 2px;
                            margin: 0;
                            margin-right: 3px;
                            height: 21px;
                            user-select: none;
                            border: 1px solid #428bca;
                          "
                        >
                          改
                        </a-tag>
                      </a-tooltip>
                      <a-tag
                        v-if="row.isPanel === false"
                        style="
                          font-size: 12px;
                          background: #428bca;
                          color: white;
                          padding: 0 2px;
                          margin: 0;
                          margin-right: 3px;
                          height: 21px;
                          user-select: none;
                          border: 1px solid #428bca;
                        "
                      >
                        单
                      </a-tag>
                    </span>
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="pcbMark" title="编码" width="50" show-overflow align="center" fixed="left" sortable></vxe-column>
              <vxe-column field="custNo" title="客户代码" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="boardNo" title="标记" width="120" show-overflow sortable v-if="this.user.factoryId == 37"></vxe-column>
              <vxe-column field="isReOrder" title="订单类型" width="70" show-overflow sortable>
                <template #default="{ row }">
                  <div>{{ row.isReOrder_ === "0" || !row.isReOrder_ ? "新单" : row.isReOrder_ === "1" ? "返单" : "返单更改" }}</div>
                </template>
              </vxe-column>
              <vxe-column field="orderType_" title="板材类型" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="fR4Tg_" title="TG" width="55" show-overflow sortable></vxe-column>
              <vxe-column field="layer_" title="层数" width="40" show-overflow sortable> </vxe-column>
              <vxe-column field="boardthick_" title="板厚" width="40" show-overflow sortable></vxe-column>
              <vxe-column field="cuThk_" title="外铜" width="40" show-overflow sortable></vxe-column>
              <vxe-column field="innerCuThk_" title="内铜" width="40" show-overflow sortable></vxe-column>
              <vxe-column field="surfaceTechStr_" title="表面工艺" width="90" show-overflow sortable></vxe-column>
              <vxe-column field="imGoldThinckness" title="金厚" width="70" show-overflow sortable></vxe-column>
              <vxe-column field="solderColor_" title="顶阻" width="70" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.solderColor_.indexOf('无') > -1 || !row.solderColor_">无</div>
                  <div v-else>{{ row.solderColor_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="solderColorBottom_" title="底阻" width="70" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.solderColorBottom_.indexOf('无') > -1 || !row.solderColorBottom_">无</div>
                  <div v-else>{{ row.solderColorBottom_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="silkColor_" title="顶字" width="45" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.silkColor_.indexOf('无') > -1 || !row.silkColor_">无</div>
                  <div v-else>{{ row.silkColor_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="silkColorBottom_" title="底字" width="45" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.silkColorBottom_.indexOf('无') > -1 || !row.silkColorBottom_">无</div>
                  <div v-else>{{ row.silkColorBottom_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="deldate_" title="交期" width="50" show-overflow sortable></vxe-column>
              <vxe-column field="delQty_" title="数量" width="45" show-overflow sortable></vxe-column>
              <vxe-column field="saleArea_" title="面积" width="76" show-overflow sortable></vxe-column>
              <vxe-column field="hpStayTime" title="耗时" width="55" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.hpOverTime" style="color: red">{{ row.hpStayTime }}</div>
                  <div v-else>{{ row.hpStayTime }}</div>
                </template>
              </vxe-column>
              <vxe-column field="remainHPTime" title="倒计时" width="55" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.remainHPTime <= 8" style="color: red">{{ row.remainHPTime }}</div>
                  <div v-else>{{ row.remainHPTime }}</div>
                </template>
              </vxe-column>
              <vxe-column field="ctlt" title="残铜(顶)" width="60" show-overflow align="center" sortable></vxe-column>
              <vxe-column field="ctlb" title="残铜(底)" width="60" show-overflow align="center" sortable></vxe-column>
              <vxe-column field="lSize_" title="长" width="75" show-overflow sortable></vxe-column>
              <vxe-column field="wSize_" title="宽" width="76" show-overflow sortable></vxe-column>
              <vxe-column field="boardType_" title="单位" width="50" show-overflow sortable></vxe-column>
              <vxe-column field="posNeg" title="负片" width="50" show-overflow sortable align="center">
                <template #default="{ row }">
                  <!-- <span >{{ row.posNeg == 1 ?'是':'' }}</span> -->
                  <span v-if="row.posNeg == '正片' || row.posNeg == null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="invoice_" title="导热系数" width="70" show-overflow sortable>
                <template #default="{ row }">
                  <span v-if="row.invoice_ == 0 || row.invoice_ === null"></span>
                  <span v-else> {{ row.invoice_ }} </span>
                </template>
              </vxe-column>
              <vxe-column field="impedanceSize" title="阻抗" width="40" show-overflow align="center" sortable>
                <template #default="{ row }">
                  <span v-if="row.impedanceSize == 0 || row.impedanceSize === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                  <!-- <span v-else> {{row.impedanceSize}} Ω</span> -->
                </template>
              </vxe-column>
              <vxe-column field="solderCover" title="过孔处理" width="70" show-overflow sortable></vxe-column>
              <vxe-column field="metalEdging" title="金属包边" width="70" show-overflow align="center" sortable>
                <template #default="{ row }">
                  <span v-if="row.metalEdging == 0 || row.metalEdging === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="isSpecistack" title="指定叠层" width="60" show-overflow align="center" sortable>
                <template #default="{ row }">
                  <span v-if="row.isSpecistack == 0 || row.isSpecistack === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="waterMark_" title="水印" width="40" show-overflow align="center" sortable>
                <template #default="{ row }">
                  <span v-if="row.waterMark_ == 0 || row.waterMark_ === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="plateType" title="HDI" width="70" show-overflow align="center" sortable>
                <template #default="{ row }">
                  <span v-if="row.plateType == 'hdi'">是</span>
                  <span v-else> </span>
                </template>
              </vxe-column>
              <vxe-column field="status_HPStr" title="状态" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="checkEndTime" title="下单时间" width="170" show-overflow sortable></vxe-column>
              <vxe-column field="factoryName" title="工厂" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="proRev" title="版本" width="45" show-overflow sortable align="center"></vxe-column>
              <vxe-column field="Stacking" title="叠层" width="45" show-overflow align="center" fixed="right" sortable>
                <template #default="{ row }">
                  <svg
                    @click="stackclick(row)"
                    style="cursor: pointer"
                    t="1726732143714"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="5543"
                    width="15"
                    height="15"
                  >
                    <path
                      d="M935.68 740.928l3.2 5.632a48 48 0 0 1-15.808 61.056l-5.632 3.328-378.24 189.12a60.8 60.8 0 0 1-46.912 3.2l-7.488-3.2-378.24-189.12a48 48 0 0 1 36.864-88.384l6.016 2.56L512 906.24l362.56-181.248a48 48 0 0 1 61.056 15.872l0.064 0.064z m0-256l3.2 5.632a48 48 0 0 1-15.808 61.056l-5.632 3.328-378.24 189.12a60.8 60.8 0 0 1-46.912 3.2l-7.488-3.2-378.24-189.12A48 48 0 0 1 143.36 466.56l6.016 2.56L512 650.24l362.56-181.248a48 48 0 0 1 61.056 15.872l0.064 0.064zM484.736 23.936a60.8 60.8 0 0 1 54.4 0l355.392 177.664a60.8 60.8 0 0 1 0 108.8L539.2 488.064a60.8 60.8 0 0 1-54.4 0L129.408 310.4a60.8 60.8 0 0 1 0-108.8L484.736 23.936zM512 117.632L235.264 256 512 394.304 788.672 256 512 117.632z"
                      fill="#ff9900"
                      p-id="5544"
                    ></path>
                  </svg>
                </template>
              </vxe-column>
              <vxe-column field="remarks" title="备注" width="45" show-overflow align="center" fixed="right" sortable>
                <template #default="{ row }">
                  <svg
                    @click="remarksclick(row)"
                    style="cursor: pointer"
                    t="1726732433380"
                    class="icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="9639"
                    id="mx_n_1726732433381"
                    width="15"
                    height="15"
                  >
                    <path
                      d="M625.728 57.472c19.264 0 34.688 6.848 48.128 20.16l208.96 207.04c14.272 13.12 21.568 29.568 21.568 49.28v504.576c0 71.808-56.256 127.744-128.576 127.744H252.16c-72.128 0-128.576-55.68-128.576-127.744V184.704c0-71.68 56.256-127.232 128.576-127.232z m-34.304 76.8H252.16c-30.144 0-51.776 21.376-51.776 50.432v653.824c0 29.44 21.888 50.944 51.776 50.944h523.648c30.016 0 51.84-21.632 51.84-50.944l-0.128-464.512H687.488A96 96 0 0 1 591.936 287.36l-0.448-9.216V134.208zM665.6 704a38.4 38.4 0 0 1 0 76.8H294.4a38.4 38.4 0 0 1 0-76.8h371.2z m0-192a38.4 38.4 0 0 1 0 76.8H294.4a38.4 38.4 0 0 1 0-76.8h371.2z m-192-192a38.4 38.4 0 1 1 0 76.8H294.4a38.4 38.4 0 1 1 0-76.8h179.2z m181.824-152.512v110.592a32 32 0 0 0 26.24 31.488l5.76 0.512h111.872L655.424 167.424z"
                      fill="#ff9900"
                      p-id="9640"
                    ></path>
                  </svg>
                </template>
              </vxe-column>
            </vxe-table>
            <a-menu :style="menuStyle" v-show="menuVisible1" class="tabRightClikBox">
              <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
              <a-menu-item @click="Productionnotification" v-if="Notice">生产通知单</a-menu-item>
              <!--合拼中读取与清除-->
              <a-menu-item v-if="readdata && checkPermission('MES.EngineeringModule.Combinate.ReadData')" @click="readDataClick"
                >读取数据</a-menu-item
              >
              <a-menu-item v-if="cleardata && checkPermission('MES.EngineeringModule.Combinate.CombinateClearSize')" @click="clearSizeClick"
                >清除数据</a-menu-item
              >
              <a-menu-item v-if="cleardata" @click="Edgebandingoutput">封边输出</a-menu-item>
              <a-menu-item v-if="cleardata" @click="Engineeringinstructions">工程指示</a-menu-item>
              <!--待合拼订单清理-->
              <a-menu-item v-if="Notice && this.user.factoryId != 12" @click="Ordercleaning">订单清理</a-menu-item>
            </a-menu>
          </a-card>
          <a-card :bordered="false" style="border: 1px solid #e9e9f0">
            <vxe-table
              border
              stripe
              show-overflow
              class="bottable"
              ref="xTable2"
              :row-class-name="rowClassName1"
              :height="left2height"
              :data="data2Source"
              :loading="table2Loading"
              :row-config="{ isHover: true, height: 36, keyField: 'id' }"
              @current-change="currentChangeEvent2"
              :loading-config="{ icon: 'vxe-icon-spinner roll', text: ' ' }"
              :scroll-y="{ enabled: true }"
              @visibleMethod="visibleMethod"
              :menu-config="{ enabled: true }"
              @cell-menu="cellContextMenuEvent2"
              @cell-mouseleave="mouseleave"
              :tooltip-config="{ enterable: true }"
              :edit-config="{ trigger: 'click', mode: 'cell' }"
              :cell-style="cellStyle"
              @sort-change="sortChangeEvent1"
              :sort-config="{ trigger: 'cell' }"
            >
              <!-- :checkbox-config="{trigger: 'row', highlight: true, range: true}"   isCurrent: true,  
          <vxe-column type="checkbox" width="30" fixed="left" align="center"></vxe-column> -->
              <vxe-column
                type="seq"
                title="序号"
                width="45"
                style="text-align: center; background-color: red"
                align="center"
                fixed="left"
              ></vxe-column>
              <vxe-column field="pdctno_" title="生产编号" width="195" show-overflow fixed="left" sortable>
                <template #default="{ row }">
                  <div style="display: flex; align-items: center">
                    <span style="color: black" :title="row.pdctno_">{{ row.pdctno_ }}</span
                    >&nbsp;
                    <span class="tagNum" style="display: inline-block; height: 19px">
                      <a-tooltip title="加急" v-if="row.urgentType_">
                        <span
                          class="noCopy"
                          style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
                          >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                        </span>
                      </a-tooltip>
                    </span>
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="active_" title="激活" width="50" show-overflow fixed="left" align="center" sortable>
                <template #default="{ row }">
                  <a-checkbox @change="active(row.active_, row.id)" v-model="row.active_"> </a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="custNo" title="客户代码" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="boardNo" title="标记" width="120" show-overflow sortable v-if="this.user.factoryId == 37"></vxe-column>
              <vxe-column field="orderType_" title="板材类型" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="fR4Tg_" title="TG" width="70" show-overflow sortable></vxe-column>
              <vxe-column field="layer_" title="层数" width="55" show-overflow sortable></vxe-column>
              <vxe-column field="boardthick_" title="板厚" width="40" show-overflow sortable></vxe-column>
              <vxe-column field="cuThk_" title="外铜" width="40" show-overflow sortable></vxe-column>
              <vxe-column field="innerCuThk_" title="内铜" width="40" show-overflow sortable></vxe-column>
              <vxe-column field="surfaceTechStr_" title="表面工艺" width="130" show-overflow sortable></vxe-column>
              <vxe-column field="imGoldThinckness" title="金厚" width="70" show-overflow sortable></vxe-column>
              <vxe-column field="solderMask_" title="顶阻" width="70" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.solderMask_.indexOf('无') > -1 || !row.solderMask_">无</div>
                  <div v-else>{{ row.solderMask_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="solderColorBottom_" title="底阻" width="70" sortable show-overflow>
                <template #default="{ row }">
                  <div v-if="row.solderColorBottom_.indexOf('无') > -1 || !row.solderColorBottom_">无</div>
                  <div v-else>{{ row.solderColorBottom_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="silkColor_" title="顶字" width="45" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.silkColor_.indexOf('无') > -1 || !row.silkColor_">无</div>
                  <div v-else>{{ row.silkColor_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="silkColorBottom_" title="底字" width="45" show-overflow sortable>
                <template #default="{ row }">
                  <div v-if="row.silkColorBottom_.indexOf('无') > -1 || !row.silkColorBottom_">无</div>
                  <div v-else>{{ row.silkColorBottom_ }}</div>
                </template>
              </vxe-column>
              <vxe-column field="deldate_" title="交期" width="50" show-overflow sortable></vxe-column>
              <vxe-column field="delQty2Org_" title="数量" width="45" show-overflow sortable></vxe-column>
              <vxe-column field="area_" title="面积" width="76" show-overflow sortable></vxe-column>
              <vxe-column field="delQty_" title="改数" width="55" show-overflow sortable>
                <template #default="{ row }">
                  <vxe-input
                    v-model="row.delQty_"
                    @focus="focusQty(row.delQty_)"
                    @blur="blurQty(row.delQty_, row.id)"
                    @keyup.enter.native="$event.target.blur()"
                    :clearable="true"
                  ></vxe-input>
                </template>
              </vxe-column>

              <vxe-column field="ctlt" title="残铜(顶)" width="60" show-overflow sortable align="center"></vxe-column>
              <vxe-column field="ctlb" title="残铜(底)" width="60" show-overflow sortable align="center"></vxe-column>
              <vxe-column field="lSize_" title="成品长" width="76" show-overflow sortable></vxe-column>
              <vxe-column field="wSize_" title="成品宽" width="65" show-overflow sortable></vxe-column>
              <vxe-column field="lSizeOrg_" title="原始长" width="55" show-overflow sortable></vxe-column>
              <vxe-column field="wSizeOrg_" title="原始宽" width="55" show-overflow sortable></vxe-column>
              <vxe-column field="boardType_" title="单位" width="50" show-overflow sortable></vxe-column>
              <vxe-column field="posNeg" title="负片" width="50" show-overflow sortable align="center">
                <template #default="{ row }">
                  <!-- <span >{{ row.posNeg == 1 ?'是':'' }}</span> -->
                  <span v-if="row.posNeg == '正片' || row.posNeg == null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="invoice_" title="导热系数" width="70" show-overflow sortable>
                <template #default="{ row }">
                  <span v-if="row.invoice_ == 0 || row.invoice_ === null"></span>
                  <span v-else> {{ row.invoice_ }} </span>
                </template>
              </vxe-column>
              <vxe-column field="impedanceSize" title="阻抗" width="40" show-overflow sortable align="center">
                <template #default="{ row }">
                  <span v-if="row.impedanceSize == 0 || row.impedanceSize === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="solderCover" title="过孔处理" width="70" show-overflow sortable></vxe-column>
              <vxe-column field="metalEdging" title="金属包边" width="70" show-overflow sortable align="center">
                <template #default="{ row }">
                  <!-- <span >{{ row.metalEdging == 1 ?'是':'' }}</span> -->
                  <span v-if="row.metalEdging == 0 || row.metalEdging === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="isSpecistack" title="指定叠层" width="60" show-overflow sortable align="center">
                <template #default="{ row }">
                  <span v-if="row.isSpecistack == 0 || row.isSpecistack === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="waterMark_" title="水印" width="40" show-overflow align="center" sortable>
                <template #default="{ row }">
                  <span v-if="row.waterMark_ == 0 || row.waterMark_ === null"></span>
                  <a-checkbox v-else checked></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="combinateRealName" title="拼板人员" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="createTime" title="导入时间" width="110" show-overflow sortable></vxe-column>
              <vxe-column field="checkEndTime" title="下单时间" width="170" show-overflow sortable></vxe-column>
              <vxe-column field="factoryName" title="工厂" width="80" show-overflow sortable></vxe-column>
              <vxe-column field="proRev" title="版本" width="45" show-overflow sortable align="center"></vxe-column>
            </vxe-table>
          </a-card>
        </div>
        <div class="right" style="width: 32%; position: relative" ref="tableWrapper1">
          <center
            ref="centerTable"
            :data3Source="data3Source"
            :data4Source="data4Source"
            :data5Source="data5Source"
            :data6Source="data6Source"
            :loading1="loading1"
            :loading2="loading2"
            :loading3="loading3"
            :loading4="loading4"
            :loading5="loading5"
            @gethpINGDetailInfo="gethpINGDetailInfo"
            @deleteHPClick="deleteHPClick"
            @getProcess="getProcess"
            @rightClick1="rightClick1"
            @down="down"
            @Ttype="Ttype"
            :showText="showText"
            :menuStyle="menuStyle"
            @mouseleave="mouseleave"
            class="righttable"
          ></center>
        </div>
      </div>
      <div class="footer">
        <div class="actionBox">
          <span class="iconstyle">
            <a-tooltip @click="caretup" v-if="fold" title="展开合拼池订单"><a-icon type="unordered-list"></a-icon></a-tooltip>
            <a-tooltip @click="caretdown" v-if="!fold" title="折叠合拼池订单"><a-icon type="unordered-list"></a-icon></a-tooltip>
          </span>
          <action
            :orderload="orderload"
            @changefac="changefac"
            @ReceivingClick="ReceivingClick"
            @editClick="editClick"
            @sigFinishClick="sigFinishClick"
            @hpPoolHandClick="hpPoolHandClick"
            @back2WaitHPClick="back2WaitHPClick"
            @backHPClick="backHPClick"
            @QueryClick="QueryClick"
            @ProcessNotesClick="ProcessNotesClick"
            @OrderCombinationHandClick="OrderCombinationHandClick"
            @deleteDataClick="deleteDataClick"
            @importErpDataMClick="importErpDataMClick"
            @clearSizeClick="clearSizeClick"
            @dataMerging="dataMerging"
            @UpdateFile="UpdateFile"
            @panelEdgeBanding="panelEdgeBanding"
            @ChargebackClick="ChargebackClick"
            @outputFile="outputFile"
            @combineFinish="combineFinish"
            @parClick="parClick"
            @readDataClick="readDataClick"
            @productClick="productClick"
            @orderClick="orderClick"
            :btnloading1="btnloading1"
          ></action>
        </div>
      </div>
      <!--叠层信息弹窗-->
      <a-modal
        title="叠层信息"
        :visible="stackdataVisible"
        @cancel="stackdataVisible = false"
        destroyOnClose
        :maskClosable="false"
        :width="1200"
        centered
      >
        <template #footer>
          <a-button key="back" @click="stackdataVisible = false" type="primary">关闭</a-button>
        </template>
        <img :src="'data:image/png;base64,' + photoImage" v-if="photoImage" />
        <stack-info ref="stackInfo" :stackUpOutputs="stackUpOutputs" v-else />
      </a-modal>
      <!--备注信息弹窗-->
      <a-modal
        title="备注信息"
        :visible="remarksdataVisible"
        @cancel="remarksdataVisible = false"
        destroyOnClose
        class="remarkstayle"
        :maskClosable="false"
        :width="1200"
        centered
      >
        <template #footer>
          <a-button key="back" @click="remarksdataVisible = false" type="primary">关闭</a-button>
        </template>
        <a-table :columns="columns4" :dataSource="jobData" :pagination="false" rowKey="id" :scroll="{ y: 500, x: 800 }">
          <template slot="picUrl" slot-scope="text, record">
            <viewer :images="picFilter(record)">
              <img :src="item" v-for="(item, index) in picFilter(record)" :key="index" style="width: 30px; height: 30px; padding-left: 5px" />
            </viewer>
          </template>
        </a-table>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        centered
      >
        <query-info ref="queryInfo" :facList="facList" />
      </a-modal>
      <a-modal
        title="修改工厂"
        :visible="facvisible"
        @cancel="reportHandleCancel"
        @ok="fachandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        centered
      >
        <a-form-item class="fac">
          <a-select showSearch allowClear optionFilterProp="lable" v-model="changfac">
            <a-select-option
              style="color: blue"
              v-for="(item, index) in mapKey(factroyList)"
              :key="index"
              :value="item.value"
              :lable="item.lable"
              :title="item.lable"
            >
              {{ item.lable }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-modal>
      <a-modal
        title="确认弹窗"
        :visible="sigdataVisible"
        @cancel="reportHandleCancel"
        @ok="sighandleOk"
        centered
        ok-text="确定"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
      >
        <span v-if="ttype == '1' && this.selectedRowList.length > 1">请确认是否多款订单单板下线！</span>
        <span
          v-else-if="this.ttype == 4 || this.ttype == 5 || (this.selectedRowList1.length > 1 && ttype == '3') || this.selectedRowList.length > 1"
          style="font-size: 14px; color: #000000"
          >{{ mmessage }}</span
        >
        <span v-else style="font-size: 14px; color: #000000">【{{ this.id1 }}】{{ mmessage }}</span>
      </a-modal>
      <!-- 编写参数 -->
      <a-modal
        title="参数设置"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="600"
        :confirmLoading="confirmLoading"
      >
        <enter-order-info ref="enterOrderInfo" :ClassList="ClassList" :selectRow1="selectRow1" :editData="editData"></enter-order-info>
      </a-modal>
      <!-- 流程备注 -->
      <a-modal
        title="流程备注"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="600"
      >
        <process-notes-info ref="ProcessNotesInfo" :ProcessNotesData="ProcessNotesData" :ProcessNotesId="ProcessNotesId"></process-notes-info>
      </a-modal>
      <!-- 订单参数弹窗 -->
      <a-modal
        title="参数信息"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        :maskClosable="false"
        :width="800"
        :destroyOnClose="true"
      >
        <parameter-dom ref="parameter" :data7Source="data7Source" />
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import { noticereviewinfo } from "@/services/mkt/OrderReview.js";
import { checkPermission } from "@/utils/abp";
import {
  getPageList,
  hpAppConfig,
  hpFac,
  hpFLFac,
  hpINGDetailInfo,
  hpINGOrderInfo,
  hpPoolOrderInfo,
  Receiving,
  getClassList,
  paraGet,
  paraSet,
  sigFinish,
  sigBack,
  hpPoolHand,
  hpPoolHand1,
  back2WaitHP,
  combineBack,
  stackupimpinfo,
  pinbanfinished,
  deleteHP,
  autoProduce,
  getFactory,
  flowReMarks,
  deleteData,
  importErpDataM,
  clearSize,
  setwaithPstate,
  byorderno,
  activation,
  generatingStructure,
  autoMations,
  engineeringInstructions,
  setHPAppConfig,
  setChangeNum,
  setOrderNoToErp,
  getfactory,
  combinateerpinfoupfactory,
  waithPOrderid,
} from "@/services/Combinate";
import { mapState } from "vuex";
import moment from "moment";
import Center from "@/pages/gongcheng/CombinedDesign/module/Center";
import Action from "@/pages/gongcheng/CombinedDesign/module/Action";
import QueryInfo from "@/pages/gongcheng/CombinedDesign/module/QueryInfo";
import parameterDom from "@/pages/gongcheng/CombinedDesign/module/parameterDom";
import EnterOrderInfo from "@/pages/gongcheng/CombinedDesign/module/EnterOrderInfo";
import ProcessNotesInfo from "@/pages/gongcheng/CombinedDesign/module/ProcessNotesInfo";
import StackInfo from "@/pages/gongcheng/CombinedDesign/module/StackInfo";
import * as signalR from "@microsoft/signalr";
import Cookie from "js-cookie";
const columns4 = [
  {
    dataIndex: "index",
    title: "序号",
    key: "index",
    width: 50,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作人",
    dataIndex: "inUserName",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "创建时间",
    dataIndex: "indate",
    width: 110,
    ellipsis: true,
    align: "left",
  },
  {
    title: "备注信息",
    dataIndex: "conent",
    // width: 300,
    ellipsis: true,
    align: "left",
  },
  {
    title: "图片",
    width: 150,
    scopedSlots: { customRender: "picUrl" },
    align: "left",
  },
];
export default {
  name: "CuttingManagement",
  components: { EnterOrderInfo, Action, Center, QueryInfo, ProcessNotesInfo, parameterDom, StackInfo },
  inject: ["reload"],
  data() {
    return {
      jobData: [],
      stackUpOutputs: [],
      photoImage: "",
      columns4,
      isCtrlPressed: false,
      stackdataVisible: false,
      remarksdataVisible: false,
      orderload: false,
      factroyList: [],
      Notice: false,
      fold: false,
      Foldleftandright: false,
      changfac: null,
      iscolumnKey: "",
      noid: "",
      changetype: null,
      facid: "",
      facvisible: false,
      isorder: "",
      iscolumnKey1: "",
      isorder1: "",
      params1: {},
      form: {
        Id: "",
      },
      text: "",
      left2height: "",
      left1height: "",
      id1: "",
      messageMode: "",
      orderno: "",
      dataVisibleMode: false,
      showText: false,
      cleardata: false,
      readdata: false,
      readclear: [],
      selectedRowsData: [],
      mmessage: "",
      ttype: "",
      spinning: false,
      selectedRowList: [],
      selectedRowList1: [],
      selectedRows: {},
      selectedRows1: {},
      loading: false,
      table1Loading: false, // 待合拼表格load
      table2Loading: false, // 待合拼表格load
      data1Source: [], // 待合拼集合
      copydata1Source: [],
      data2Source: [], // 已分派集合
      data3Source: [],
      data4Source: [],
      data5Source: [],
      data6Source: [],
      data7Source: [],
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      loading5: false,
      confirmLoading: false,
      menuVisible: false,
      menuVisible1: false,
      selectTime: "",
      restTime: "",
      msTime: "",
      isDragging: false,
      startIndex: -1,
      shiftKey: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      menuData: {},
      checked1: true,
      btnloading1: false,
      sigdataVisible: false,
      dataVisible: false, // 编写参数弹窗
      dataVisible1: false, // 查询弹窗
      dataVisible2: false, // 流程备注
      dataVisible3: false, // 参数信息展示
      ClassList: [],
      editData: {},
      selectRow1: {},
      selectedRowsData1: [],
      size: 0,
      facList: [],
      ProcessNotesId: "",
      ProcessNotesOrderNo: "",
      ProcessNotesData: [],
      websocket: null,
      executionOrder: {
        UserId: 61137, //用户id
        ShtSize: ["20.354", "24.370"], //拼版尺寸
        ReArea: 0.02, //加投面积
        Angle: "90", //角度
        TarRate: 0.1, //利用率
        Accuracy: 0.1, //计算精度
        SortArea: true, //排序
        HpNum: 2, //合拼数
        DelFile: false, //删除文件
        PnlNum: 3000, //设置pnl数量
        Odd: false,
        NestType: true, //设置类型0 - Pnl / 1 - Ratio
        IfCommitPnl: 0, //pnl最小数
        CheckPart: true, //是否检查外形
        CheckAcc: 0.1, //检查外形精度（默认0.1）
        IsDxfMode: false, //是否DXF模式
        AutoHepin: 1, //新自动合拼 1--NestLib 0--Rect
        fbAreaID: "",
        lol: "",
        col: "",
        tableWrapper: "",
        combineEn: "",
        rowIndex: "",
      },
      signalR: null,
      record: {},
      record2: {},
      fixedColumnsIndexes: [0],
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.handleResize();
      let a = localStorage.getItem("fold");
      let b = localStorage.getItem("Foldleftandright");
      if (this.loadclick) {
        a == "true" ? this.caretdown() : this.caretup();
        b == "true" ? this.caretright() : this.caretleft();
      } else {
        localStorage.removeItem("fold");
        localStorage.removeItem("Foldleftandright");
      }
    });
  },
  // 获取当前登陆账号信息
  computed: {
    ...mapState("account", ["user"]),
    ...mapState("setting", ["loadclick"]),
  },
  methods: {
    checkPermission,
    // 图片字符串裁切
    picFilter(val) {
      if (val.picUrl) {
        return val.picUrl.split(",");
      } else {
        return [];
      }
    },
    stackclick(row) {
      stackupimpinfo({ orderNo: row.pdctno_, businessOrderNo: row.businessOrderNo, JoinFactoryId: row.joinFactoryId }).then(res => {
        if (res.code) {
          this.stackdataVisible = true;
          this.stackUpOutputs = res.data.stackUpOutputs;
          this.photoImage = res.data.photoImage;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    remarksclick(row) {
      byorderno(row.joinFactoryId, { orderNo: row.pdctno_, businessOrderNo: row.businessOrderNo }).then(res => {
        if (res.code) {
          this.jobData = res.data;
          this.remarksdataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getfactorylist() {
      getfactory(this.facid, this.changetype).then(res => {
        if (res.code) {
          this.factroyList = res.data;
        }
      });
    },
    fachandleOk() {
      combinateerpinfoupfactory(this.facid, this.changetype, this.changfac).then(res => {
        if (res.code) {
          this.$message.success("修改工厂成功");
          if (this.changetype == 0) {
            this.getorderList();
            this.selectedRowList = [];
          } else {
            this.gethpINGOrderInfo();
            this.$refs.centerTable.id = "";
            this.$refs.centerTable.pdctno_ = "";
            this.$refs.centerTable.selectRow1 = "";
          }
        } else {
          this.$message.error(res.message);
        }
      });
      this.facvisible = false;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    Productionnotification() {
      noticereviewinfo(this.noid, 3).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    caretup() {
      this.fold = false;
      localStorage.setItem("fold", this.fold);
      let bot = document.getElementsByClassName("bottable")[0];
      bot.style.display = "";
      this.handleResize();
    },
    caretdown() {
      this.fold = true;
      localStorage.setItem("fold", this.fold);
      let bot = document.getElementsByClassName("bottable")[0];
      bot.style.display = "none";
      this.left1height = window.innerHeight - 155;
    },
    caretleft() {
      this.Foldleftandright = false;
      localStorage.setItem("Foldleftandright", this.Foldleftandright);
      let righttable = document.getElementsByClassName("righttable")[0];
      righttable.style.display = "";
      document.getElementsByClassName("left")[0].style.width = "68%";
    },
    caretright() {
      this.Foldleftandright = true;
      localStorage.setItem("Foldleftandright", this.Foldleftandright);
      let righttable = document.getElementsByClassName("righttable")[0];
      righttable.style.display = "none";
      document.getElementsByClassName("left")[0].style.width = "100%";
    },
    sortChangeEvent(e) {
      let { field, order } = e;
      this.iscolumnKey = field;
      this.isorder = order;
      for (let index = 0; index < this.data7Source.length; index++) {
        const element = this.data7Source[index].captions_;
        if (element == "规则编码(1|2)") {
          var ConfigRuleType = this.data7Source[index].fristValue_;
        }
      }
      this.params1.ConfigRuleType = ConfigRuleType;
      if (JSON.stringify(this.params1) != "{}") {
        this.getorderList(this.params1);
      } else {
        if (this.iscolumnKey) {
          this.data1Source = localStorage.getItem("orderList") ? JSON.parse(localStorage.getItem("orderList")) : [];
          this.data1Source.sort((a, b) => {
            let aValue = a[this.iscolumnKey];
            let bValue = b[this.iscolumnKey];
            if (typeof aValue === "string" && typeof bValue === "string") {
              return this.isorder === "asc" ? aValue.localeCompare(bValue) : this.isorder === "desc" ? bValue.localeCompare(aValue) : 0;
            } else {
              if (aValue === bValue) {
                return this.data1Source.indexOf(a) - this.data1Source.indexOf(b);
              }
              if (this.isorder === "asc") {
                return aValue > bValue ? 1 : -1;
              } else if (this.isorder === "desc") {
                return aValue < bValue ? 1 : -1;
              }
              return 0;
            }
          });
        }
      }
      this.selectedRowList = [];
    },
    sortChangeEvent1(e) {
      let { field, order } = e;
      this.iscolumnKey1 = field;
      this.isorder1 = order;
      if (JSON.stringify(this.params1) != "{}") {
        this.gethpPoolOrderInfo(this.params1);
      } else {
        if (this.iscolumnKey1) {
          this.data2Source = localStorage.getItem("orderList1") ? JSON.parse(localStorage.getItem("orderList1")) : [];
          this.data2Source.sort((a, b) => {
            let aValue = a[this.iscolumnKey1];
            let bValue = b[this.iscolumnKey1];
            if (typeof aValue === "string" && typeof bValue === "string") {
              return this.isorder1 === "asc" ? aValue.localeCompare(bValue) : this.isorder1 === "desc" ? bValue.localeCompare(aValue) : 0;
            } else {
              if (aValue === bValue) {
                return this.data2Source.indexOf(a) - this.data2Source.indexOf(b);
              }
              if (this.isorder1 === "asc") {
                return aValue > bValue ? 1 : -1;
              } else if (this.isorder1 === "desc") {
                return aValue < bValue ? 1 : -1;
              }
              return 0;
            }
          });
        }
      }
      this.selectedRowList1 = [];
    },
    handleResize() {
      if (this.fold == false) {
        if ((window.innerHeight - 155) / 2 <= 378) {
          this.left1height = (window.innerHeight - 155) / 2;
          this.left2height = (window.innerHeight - 155) / 2;
        } else {
          this.left1height = "378";
          this.left2height = "378";
        }
      } else {
        if (window.innerHeight <= 911) {
          this.left1height = window.innerHeight - 155;
        } else {
          this.left1height = "756";
        }
      }
    },
    Engineeringinstructions() {
      waithPOrderid(this.menuData.id).then(res => {
        if (res.code && res.data) {
          localStorage.setItem("procust", true);
          this.$router.push({
            path: "/gongcheng/engineering",
            query: { OrderNo: this.menuData.pdctno_, id: res.data, factory: this.menuData.tradeType, typee: "6" },
          });
        } else {
          this.$message.error("该订单不允许跳转工程指示");
        }
      });
    },
    orderClick(record) {
      if (this.$refs.centerTable.pdctno_ == "") {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderload = true;
      let id = this.$refs.centerTable.id;
      engineeringInstructions(id)
        .then(res => {
          if (res.code) {
            this.combineEn = res.data;
            localStorage.setItem("procust", true);
            this.$router.push({
              path: "/gongcheng/engineering",
              query: { OrderNo: this.combineEn.orderNo, id: this.combineEn.id, factory: this.combineEn.joinFactoryId, typee: "5" },
            });
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderload = false;
        });
    },
    active(x, y) {
      // let y = this.selectedRowList1[0]
      activation(y, x).then(res => {
        if (res.code) {
          this.$message.success("设置成功");
          return;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    focusQty(x) {
      this.delQty_ = x;
    },
    blurQty(x, y) {
      var r = /^\+?[1-9][0-9]*$/; //正整数正则
      if (!r.test(x)) {
        this.$message.warning("请输入正整数");
        return;
      }
      if (this.delQty_ != x) {
        setChangeNum(y, x).then(res => {
          if (res.code) {
            this.$message.success("改数成功");
            return;
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    rightClick1(e, text, record) {
      this.noid = record.guild_;
      this.menuData = record;
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.localName == "path") {
        event = e.target.parentNode.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode.parentNode;
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split("\n")[0];
      }
      if (this.lol == 1 || this.lol == 2) {
        this.tableWrapper = this.$refs.tableWrapper;
        this.menuVisible1 = true;
        this.$refs.centerTable.menuVisible = false;
      } else {
        this.tableWrapper = this.$refs.tableWrapper1;
        this.$refs.centerTable.menuVisible = true;
        this.menuVisible1 = false;
      }
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = this.tableWrapper.getBoundingClientRect();
      let offsetx = event.offsetLeft + event.offsetWidth - 10;
      let offsety = event.offsetTop + 40;
      if (this.lol == 1) {
        this.cleardata = false;
        this.readdata = false;
        this.Notice = true;
      } else if (this.lol == 2) {
        this.cleardata = true;
        this.readdata = true;
        this.Notice = false;
      } else if (this.lol == 3) {
        this.col = this.$refs.centerTable.columns1.length - 1;
        this.cleardata = false;
        this.readdata = false;
        this.Notice = false;
      } else if (this.lol == 4) {
        this.col = this.$refs.centerTable.columns2.length - 1;
        this.cleardata = false;
        this.readdata = false;
        this.Notice = false;
      }
      this.menuStyle.top = cellRect.top - wrapperRect.top + 2 + "px";
      this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 55 + "px";
      if (this.lol == 3 && event.cellIndex != this.col && cellRect.left >= 1820) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 2 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left - event.offsetWidth - 10 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 2 + "px";
        this.menuStyle.left = offsetx + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 2 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }
      document.body.addEventListener("click", this.bodyClick);
    },
    Ttype(ttype) {
      this.lol = ttype;
      if (ttype == 3) {
        this.selectedRowList = [];
      }
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    down() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
      this.showText = false;
    },
    // 定时器
    // timeChange(){
    //   if(this.checked1){
    //     // this.msTime  = 5
    //     this.msTime  = this.selectTime * 60
    //     let m = parseInt(this.msTime / 60 % 60);
    //     m = m < 10 ? "0" + m : m
    //     let s = parseInt(this.msTime % 60);
    //     s = s < 10 ? "0" + s : s
    //     this.restTime =  m + ':' + s
    //     this.getTime()
    //   }
    // },
    // countDown(){
    //   let m = parseInt(this.msTime / 60 % 60);
    //   m = m < 10 ? "0" + m : m
    //   let s = parseInt(this.msTime % 60);
    //   s = s < 10 ? "0" + s : s
    //   this.restTime =  m + ':' + s
    // },
    // getTime() {
    //   const clock = setInterval(() => {
    //     this.msTime -= 1
    //     this.countDown()
    //     if (this.msTime === 0) {
    //       this.restTime = ''
    //       clearInterval(clock)
    //       if(this.checked1){
    //         this.timeChange()
    //       }
    //     }
    //     if(!this.checked1){
    //       clearInterval(clock)
    //       this.restTime = ''
    //     }
    //   }, 1000)
    // },
    click1() {
      this.checked1 = !this.checked1;
    },
    // 待合拼列表
    getorderList(orderNum) {
      let params = {};
      if (orderNum) {
        params = orderNum;
      }
      this.table1Loading = true;
      getPageList(params)
        .then(res => {
          if (res.code == 1) {
            localStorage.setItem("orderList", JSON.stringify(res.data));
            this.data1Source = res.data;
            if (this.iscolumnKey) {
              this.data1Source.sort((a, b) => {
                let aValue = a[this.iscolumnKey];
                let bValue = b[this.iscolumnKey];
                if (typeof aValue === "string" && typeof bValue === "string") {
                  return this.isorder === "asc" ? aValue.localeCompare(bValue) : this.isorder === "desc" ? bValue.localeCompare(aValue) : 0;
                } else {
                  if (aValue === bValue) {
                    return this.data1Source.indexOf(a) - this.data1Source.indexOf(b);
                  }
                  if (this.isorder === "asc") {
                    return aValue > bValue ? 1 : -1;
                  } else if (this.isorder === "desc") {
                    return aValue < bValue ? 1 : -1;
                  }
                  return 0;
                }
              });
            }
            this.copydata1Source = JSON.parse(JSON.stringify(res.data));
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.table1Loading = false;
        });
    },
    // 选择待收货订单列表
    onSelectChange(selectedRowKeys, selectedRows) {
      const newSelectedRowKeys = [],
        newSelectedRows = [];
      selectedRows.forEach(item => {
        newSelectedRowKeys.push(item.guild_);
        newSelectedRows.push(item);
      });
      this.selectedRowList = newSelectedRowKeys;
      this.selectedRows = newSelectedRows;
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "13" && this.sigdataVisible) {
        this.sighandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "70" && this.isCtrlPressed) {
        this.QueryClick();
        this.isCtrlPressed = false;
        this.reportHandleCancel();
        this.dataVisible1 = true;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible1) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "67" && this.sigdataVisible) {
        this.isCtrlPressed = false;
        this.reportHandleCancel();
        e.preventDefault();
      }
    },
    handleMouseDown(event, record, index) {
      this.lol = 1;
      this.id1 = record.pdctno_;
      // window.addEventListener('keydown', this.keydown)
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
        // //console.log('鼠标拖动',event, record, index)
      }
    },
    handleMouseUp(event, record, index) {
      // //console.log('鼠标离开',this.shiftKey, )
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowList;
        if (rowKeys.length > 0 && rowKeys.includes(record.guild_)) {
          rowKeys.splice(rowKeys.indexOf(record.guild_), 1);
        } else {
          rowKeys.push(record.guild_);
        }
        this.selectedRowList = rowKeys;
        if (this.selectedRowList.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowList = [record.guild_];
        }
      }
      this.shiftKey = false;
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.data1Source.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].guild_);
      }
      this.selectedRowList = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.data1Source.filter(item => {
          return item.id == this.selectedRowList[this.selectedRowList.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.data1Source.filter(item => {
          return item.id == this.selectedRowList[0];
        })[0];
      }
    },
    handleMouseDown1(event, record, index) {
      //console.log('handleMouseDown1', );
      this.lol = 2;
      this.id1 = record.pdctno_;
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove1(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0) {
        this.updateSelectedItems1(this.startIndex, index);
      }
    },
    handleMouseUp1(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowList1;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowList1 = rowKeys;
        if (this.selectedRowList1.length == 1) {
          this.selectedRowsData1 = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData1 = record;
          this.selectedRowList1 = [record.id];
        }
      }
      this.shiftKey = false;
    },
    updateSelectedItems1(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.data2Source.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      this.selectedRowList1 = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData1 = this.data2Source.filter(item => {
          return item.id == this.selectedRowList1[this.selectedRowList1.length - 1];
        })[0];
      } else {
        this.selectedRowsData1 = this.data2Source.filter(item => {
          return item.id == this.selectedRowList1[0];
        })[0];
      }
    },
    // 点击事件配置
    eventTouch(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            let rowKeys = this.selectedRowList;
            if (rowKeys.length > 0 && rowKeys.includes(record.guild_)) {
              rowKeys.splice(rowKeys.indexOf(record.guild_), 1);
            } else {
              rowKeys.push(record.guild_);
            }
            this.selectedRowList = rowKeys;
          },
        },
      };
    },
    // isRedRow(record){
    //   var classStr = ''
    //   if(record.guild_ == this.selectedRowList){
    //     classStr = classStr + 'rowBackgroundColor' + ' '
    //   }
    //   return classStr

    // },
    isRedRow(record) {
      let strGroup = [];
      if (record.guild_ && this.selectedRowList.includes(record.guild_)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    // 选择待收货订单列表
    onSelectChange1(selectedRowKeys, selectedRows) {
      const newSelectedRowKeys = [],
        newSelectedRows = [];
      selectedRows.forEach(item => {
        newSelectedRowKeys.push(item.id);
        newSelectedRows.push(item);
      });
      this.selectedRowList1 = newSelectedRowKeys;
      this.selectedRows1 = newSelectedRows;
    },
    // 点击事件配置
    eventTouch1(record, index) {
      return {
        props: {},
        on: {
          // 事件
          click: () => {
            let rowKeys = this.selectedRowList1;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowList1 = rowKeys;
          },
        },
      };
    },
    isRedRow1(record) {
      let strGroup = [];
      if (record.id && this.selectedRowList1.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      if (!record.lSize_ || !record.wSize_) {
        strGroup.push("rowColor");
      }
      return strGroup;
    },
    // 获取订单池订单
    gethpPoolOrderInfo(orderNum) {
      let params = {};
      if (orderNum) {
        params = orderNum;
      }
      this.table2Loading = true;
      hpPoolOrderInfo(params)
        .then(res => {
          if (res.code == 1) {
            localStorage.setItem("orderList1", JSON.stringify(res.data));
            this.data2Source = res.data;
            if (this.iscolumnKey1) {
              this.data2Source.sort((a, b) => {
                let aValue = a[this.iscolumnKey1];
                let bValue = b[this.iscolumnKey1];
                if (typeof aValue === "string" && typeof bValue === "string") {
                  return this.isorder1 === "asc" ? aValue.localeCompare(bValue) : this.isorder1 === "desc" ? bValue.localeCompare(aValue) : 0;
                } else {
                  if (aValue === bValue) {
                    return this.data2Source.indexOf(a) - this.data2Source.indexOf(b);
                  }
                  if (this.isorder1 === "asc") {
                    return aValue > bValue ? 1 : -1;
                  } else if (this.isorder1 === "desc") {
                    return aValue < bValue ? 1 : -1;
                  }
                  return 0;
                }
              });
            }
          }
        })
        .finally(() => {
          this.table2Loading = false;
        });
    },
    // 获取已数据合拼的订单
    gethpINGOrderInfo(orderNum) {
      let params = {};
      if (orderNum) {
        params = orderNum;
      }
      this.loading1 = true;
      hpINGOrderInfo(params)
        .then(res => {
          if (res.code == 1) {
            this.data3Source = res.data;
          }
        })
        .finally(() => {
          this.loading1 = false;
        });
    },
    // 子板明细
    gethpINGDetailInfo(record) {
      if (record) {
        this.ProcessNotesId = record.id;
        this.ProcessNotesOrderNo = record.pdctno_;
        this.fbAreaID = record.factory_;
      } else {
        this.ProcessNotesOrderNo = "";
      }
      this.loading2 = true;
      hpINGDetailInfo(this.ProcessNotesId, this.ProcessNotesOrderNo)
        .then(res => {
          if (res.code == 1) {
            this.data4Source = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
    },
    // 获取合拼工厂
    gethpFac(orderNum) {
      // if(orderNum) {
      //   params.orderno = orderNum
      // }
      this.loading3 = true;
      hpFac()
        .then(res => {
          if (res.code == 1) {
            this.data5Source = res.data;
          }
        })
        .finally(() => {
          this.loading3 = false;
        });
    },
    // 获取合拼信息 菲林工厂
    gethpFLFac(orderNum) {
      // if(orderNum) {
      //   params.orderno = orderNum
      // }
      this.loading4 = true;
      hpFLFac()
        .then(res => {
          if (res.code == 1) {
            this.data6Source = res.data;
          }
        })
        .finally(() => {
          this.loading4 = false;
        });
    },
    // 获取合拼配置
    async gethpAppConfig(orderNum) {
      this.loading5 = true;
      await hpAppConfig()
        .then(res => {
          if (res.code == 1) {
            this.data7Source = res.data;
          }
        })
        .finally(() => {
          this.loading5 = false;
        });
    },
    // ERP导入
    ReceivingClick() {
      this.spinning = true;
      this.btnloading1 = true;
      let params = 1;
      if (confirm("请确认是否ERP导入?")) {
        Receiving(params)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("导入成功");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.btnloading1 = false;
            this.spinning = false;
          });
      }
    },
    getClassList() {
      let params = [2021];
      getClassList(params).then(res => {
        if (res.code) {
          this.ClassList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 编写参数
    async editClick() {
      if (!this.$refs.centerTable.id) {
        this.$message.warning("请选择合拼订单");
        return;
      }
      this.dataVisible = true;
      this.selectRow1 = this.$refs.centerTable.selectRow1;
      let params = this.selectRow1.id;
      await paraGet(params).then(res => {
        if (res.code) {
          this.editData = res.data;
          this.editData.pnlQty_ = this.selectRow1.pnlQty_;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel() {
      this.facvisible = false;
      this.sigdataVisible = false;
      this.dataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible3 = false;
      if (this.$refs.ProcessNotesInfo) {
        if (this.$refs.ProcessNotesInfo.editFlg) {
          this.$message.warning("编辑状态不可关闭弹窗");
          return;
        } else {
          this.dataVisible2 = false;
        }
      }
    },
    handleOk3() {
      this.dataVisible3 = false;
      this.data7Source = this.$refs.parameter.configureData;
      for (let index = 0; index < this.data7Source.length; index++) {
        const element = this.data7Source[index].captions_;
        if (element == "规则编码(1|2)") {
          var ConfigRuleType = this.data7Source[index].fristValue_;
        }
      }
      let data = {};
      data.ConfigRuleType = ConfigRuleType;
      this.getorderList(data);
    },
    parClick() {
      this.dataVisible3 = true;
    },
    changefac() {
      if (this.selectedRowList.length == 0 && !this.$refs.centerTable.id) {
        this.$message.warning("请选择需要更改工厂的订单");
        return;
      }
      if (this.selectedRowList.length > 1) {
        this.$message.warning("只能选择一个订单进行更改");
        return;
      }
      if (this.selectedRowList.length != 0) {
        this.changetype = 0;
        this.facid = this.selectedRowList[0];
        this.changfac = this.selectedRowsData.joinFactoryId;
      } else if (this.$refs.centerTable.id) {
        this.changetype = 1;
        this.facid = this.$refs.centerTable.id;
        this.changfac = this.$refs.centerTable.selectRow1.factory_;
      }
      console.log(this.changfac, "2178");
      this.getfactorylist();

      this.facvisible = true;
    },
    readDataClick() {
      // if(!this.selectedRowList1.length){
      //   this.$message.warning('请选择订单')
      //   return
      // }
      var factory = "";
      let arr = [];
      //2023/10/24更改为读取数据时只要序号为红色就一起传入
      // this.selectedRowList1.forEach(ite=>{
      //   let obj = this.data2Source.filter(a=>{return a.id == ite})
      //   if(obj.length){
      //     arr.push(obj[0].pdctno_)
      //   }
      // })
      let obj1 = this.data2Source.filter(a => {
        return !a.lSize_ || !a.wSize_;
      });
      if (obj1.length) {
        obj1.forEach(item => {
          arr.push(item.tgzName_);
          factory = item.joinFactoryId;
        });
      }
      let str = arr.join("/").toLocaleLowerCase();
      let userName = Cookie.get("userName");
      if (str) {
        let url = "h2ginterface://?TCL=EXE&Command=CreateOutLine&AreaID=" + factory + "&ListJOB=" + str + "&webUser=" + userName;
        window.open(url, "_blank");
      } else {
        this.$message.warning("当前没有可读取数据的订单");
      }
    },
    productClick() {
      if (this.$refs.centerTable.pdctno_ == "") {
        this.$message.warning("请选择生产流程的订单");
        return;
      }
      this.id1 = this.$refs.centerTable.pdctno_;
      this.mmessage = "确认生产流程吗？";
      this.sigdataVisible = true;
      this.ttype = "6";
    },
    handleOk1() {
      let id = this.$refs.centerTable.selectRow1.id;
      let params = this.$refs.enterOrderInfo.enterOrderForm;
      params.pnlQty_ = params.pnlQty_.toString();
      paraSet(id, params).then(res => {
        if (res.code) {
          this.$message.success("编辑保存成功");
          this.$refs.centerTable.id = "";
          this.gethpINGOrderInfo();
        } else {
          this.$message.error(res.message);
        }
      });
      this.dataVisible = false;
    },
    sighandleOk() {
      let params = {};
      if (this.ttype == "1") {
        params.guilds = this.selectedRowList;
        sigFinish(params).then(res => {
          if (res.code) {
            this.$message.success("下线成功");
            for (let index = 0; index < this.data7Source.length; index++) {
              const element = this.data7Source[index].captions_;
              if (element == "规则编码(1|2)") {
                var ConfigRuleType = this.data7Source[index].fristValue_;
              }
            }
            let data = {};
            data.ConfigRuleType = ConfigRuleType;
            this.getorderList(data);
            this.gethpINGOrderInfo();
            this.selectedRowList = [];
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "2") {
        importErpDataM(this.selectedRowList).then(res => {
          if (res.code) {
            this.$message.success("手动导入成功");
            this.gethpPoolOrderInfo();
            for (let index = 0; index < this.data7Source.length; index++) {
              const element = this.data7Source[index].captions_;
              if (element == "规则编码(1|2)") {
                var ConfigRuleType = this.data7Source[index].fristValue_;
              }
            }
            let data = {};
            data.ConfigRuleType = ConfigRuleType;
            this.getorderList(data);
          } else {
            this.$message.error(res.message);
          }
        });
      }
      // 回退待合拼
      if (this.ttype == "3") {
        let params = this.selectedRowList1;
        back2WaitHP({ ids: params }).then(res => {
          if (res.code) {
            this.$message.success("回退成功");
            for (let index = 0; index < this.data7Source.length; index++) {
              const element = this.data7Source[index].captions_;
              if (element == "规则编码(1|2)") {
                var ConfigRuleType = this.data7Source[index].fristValue_;
              }
            }
            let data = {};
            data.ConfigRuleType = ConfigRuleType;
            this.getorderList(data);
            this.gethpPoolOrderInfo();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      //合拼订单
      if (this.ttype == "4") {
        let params = this.data7Source;
        hpPoolHand(params).then(res => {
          if (res.code) {
            this.$message.success("成功");
            for (let index = 0; index < this.data7Source.length; index++) {
              const element = this.data7Source[index].captions_;
              if (element == "规则编码(1|2)") {
                var ConfigRuleType = this.data7Source[index].fristValue_;
              }
            }
            let data = {};
            data.ConfigRuleType = ConfigRuleType;
            this.getorderList(data);
            this.gethpPoolOrderInfo();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "5") {
        let params = this.data7Source;
        hpPoolHand1(params).then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.spinning = true;
          } else {
            this.$message.error(res.message);
          }
        });
        setTimeout(() => {
          this.spinning = false;
          this.gethpPoolOrderInfo(); // 合拼池
          this.gethpINGOrderInfo(); // 右上
          this.data4Source = []; // 右下
        }, 30000);
      }
      if (this.ttype == "6") {
        let id = this.$refs.centerTable.id;
        generatingStructure(id).then(res => {
          if (res.code) {
            autoMations(id).then(res => {
              if (res.code) {
                this.$message.success("成功");
              } else this.$message.error(res.message);
            });
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.ttype == "7") {
        let params1 = this.$refs.centerTable.selectRow1;
        let params = this.$refs.centerTable.selectRow1.id;
        combineBack({ ids: [params] }).then(res => {
          if (res.code) {
            this.$message.success("回退成功");
            for (let index = 0; index < this.data7Source.length; index++) {
              const element = this.data7Source[index].captions_;
              if (element == "规则编码(1|2)") {
                var ConfigRuleType = this.data7Source[index].fristValue_;
              }
            }
            let data = {};
            data.ConfigRuleType = ConfigRuleType;
            this.getorderList(data);
            this.gethpINGOrderInfo();
            this.gethpPoolOrderInfo();
            this.gethpINGDetailInfo(params1);
            this.id1 = "";
            this.mmessage = "";
            this.$refs.centerTable.selectRow1 = {};
          } else {
            this.$message.error(res.message);
          }
        });
      }
      //单板回退
      if (this.ttype == "8") {
        let params = this.selectedRowList;
        this.table1Loading = true;
        sigBack(params)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
              for (let index = 0; index < this.data7Source.length; index++) {
                const element = this.data7Source[index].captions_;
                if (element == "规则编码(1|2)") {
                  var ConfigRuleType = this.data7Source[index].fristValue_;
                }
              }
              let data = {};
              data.ConfigRuleType = ConfigRuleType;
              this.getorderList(data);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.table1Loading = false;
          });
      }
      if (this.ttype == "9") {
        let ids = this.ProcessNotesId;
        pinbanfinished(ids).then(res => {
          if (res.code) {
            this.$message.success("合拼完成");
            this.gethpINGOrderInfo();
            this.gethpINGDetailInfo();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      this.selectedRowList = [];
      this.selectedRowList1 = [];
      this.sigdataVisible = false;
    },
    // 单板下线
    sigFinishClick() {
      this.select1();
      if (!this.selectedRowList.length > 0) {
        this.$message.warning("请选择待下线订单");
        return;
      }
      this.sigdataVisible = true;
      this.mmessage = "确认单板下线吗？";
      this.ttype = "1";
    },
    // 单板回退
    ChargebackClick(record) {
      if (this.selectedRowList.length == 0) {
        this.$message.warning("请选择待合拼列表订单进行单板回退");
        return;
      }
      if (this.selectedRowList.length > 1) {
        this.$message.warning("单板回退只能选择一个订单");
        return;
      }
      this.sigdataVisible = true;
      this.id1 = this.selectedRowsData.pdctno_;
      this.mmessage = "确定单板回退吗？";
      this.ttype = "8";
    },

    backHPClick() {
      if (this.$refs.centerTable.selectRow1.id == undefined) {
        this.$message.warning("请选择回退合拼订单");
      } else {
        this.sigdataVisible = true;
        this.id1 = this.$refs.centerTable.selectRow1.pdctno_;
        this.mmessage = "确定回退合拼吗？";
      }
      this.ttype = "7";
    },
    // 作废合拼
    deleteHPClick(record) {
      if (confirm("确认作废合拼吗？")) {
        deleteHP(record.id, record.pdctno_).then(res => {
          if (res.code) {
            this.$message.success("作废成功");
            for (let index = 0; index < this.data7Source.length; index++) {
              const element = this.data7Source[index].captions_;
              if (element == "规则编码(1|2)") {
                var ConfigRuleType = this.data7Source[index].fristValue_;
              }
            }
            let data = {};
            data.ConfigRuleType = ConfigRuleType;
            this.getorderList(data);
            this.gethpINGOrderInfo();
            this.gethpINGDetailInfo();
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 自动流程 autoProduce
    getProcess(record) {
      if (confirm("确认自动生成流程吗？")) {
        autoProduce(record.id).then(res => {
          if (res.code) {
            this.$message.success("自动流程成功");
            for (let index = 0; index < this.data7Source.length; index++) {
              const element = this.data7Source[index].captions_;
              if (element == "规则编码(1|2)") {
                var ConfigRuleType = this.data7Source[index].fristValue_;
              }
            }
            let data = {};
            data.ConfigRuleType = ConfigRuleType;
            this.getorderList(data);
            this.gethpINGOrderInfo();
            this.gethpINGDetailInfo();
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 合拼订单
    hpPoolHandClick() {
      this.sigdataVisible = true;
      this.mmessage = "确认合拼订单吗？";
      this.ttype = "4";
    },
    // hpPoolHandClick(){
    //   hpPoolHand().then(res =>{
    //     if(res.code){
    //       this.$message.success('成功')
    //       this.getorderList()
    //       this.gethpPoolOrderInfo()
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   })
    // },

    // 回退待合拼
    back2WaitHPClick() {
      this.select2();
      if (this.selectedRowList1.length == 0) {
        this.$message.warning("请选择回退待合拼订单");
        return;
      }
      this.sigdataVisible = true;
      this.mmessage = "确认回退待合拼订单吗？";
      this.ttype = "3";
    },
    // back2WaitHPClick(){
    //   let params = this.selectedRowList1
    //   //console.log(params,"params")
    //   back2WaitHP({ids:params}).then(res=>{
    //     if(params.length>0){
    //       this.$message.success('回退成功')
    //       this.getorderList()
    //       this.gethpPoolOrderInfo()
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   })
    // },

    // 右键事件
    bodyClick() {
      this.menuVisible1 = false;
      let visible1 = this.$refs.centerTable.menuVisible;
      visible1 = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 450 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 获取查询弹窗工厂
    getFac() {
      getFactory().then(res => {
        if (res.code) {
          this.facList = res.data;
        }
      });
    },
    QueryClick() {
      this.dataVisible1 = true;
    },
    updateUrl(urlPath) {
      let _this = this;
      if (urlPath.indexOf("sockjs") != -1) {
        _this.wsUrl = "http://" + urlPath;
      } else {
        if (window.location.protocol == "http:") {
          _this.wsUrl = "ws://" + urlPath;
        } else {
          _this.wsUrl = "ws://" + urlPath;
        }
      }
    },
    //  订单合拼
    // OrderCombinationHandClick(){
    //  let _this = this;
    //   var heartCheck = {
    //     timeout: 5000,//5秒
    //     timeoutObj: null,
    //     reset: function(){
    //       clearInterval(this.timeoutObj); return this;
    //     },
    //     start: function(){
    //       this.timeoutObj = setInterval(function(){ _this.websocket.send("HeartBeat");}, this.timeout)
    //     }
    //   };
    //   if ('WebSocket' in window) {
    //     _this.updateUrl("127.0.0.1:18181");
    //       _this.websocket = new WebSocket(_this.wsUrl);
    //   } else if ('MozWebSocket' in window) {
    //     _this.updateUrl("127.0.0.1:18181");
    //     _this.websocket = new MozWebSocket(_this.wsUrl) || null;
    //   } else {
    //     _this.updateUrl("sockjs/webSocketServer");
    //     _this.websocket = new SockJS(_this.wsUrl)
    //   }
    //   let ext=this.executionOrder;
    //   let arr =[]
    //   if(this.$refs.parameter.configureData.length >0){
    //     arr = this.$refs.parameter.configureData
    //   }else{
    //    arr = this.data7Source
    //   }
    //   _this.websocket.onopen = function(){
    //     _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'CombinationBz',"Task":'btn_ExecutionOrder',"Uid":'','Data':JSON.stringify(arr)}))
    //     // heartCheck.reset().start();
    //   };
    // },
    OrderCombinationHandClick() {
      this.sigdataVisible = true;
      this.mmessage = "确认订单合拼吗？";
      this.ttype = "5";
    },
    handleOk() {
      let params = {};
      let arr = [];
      for (let index = 0; index < this.data7Source.length; index++) {
        const element = this.data7Source[index].captions_;
        if (element == "规则编码(1|2)") {
          var ConfigRuleType = this.data7Source[index].fristValue_;
        }
      }
      params.ConfigRuleType = ConfigRuleType;
      if (this.$refs.parameter && this.$refs.parameter.configureData.length > 0) {
        arr = this.$refs.parameter.configureData;
      } else {
        arr = this.data7Source;
      }
      if (arr.filter(item => item.captions_ == "查询开关(0|1|2|3)")[0]) {
        let Search = arr.filter(item => item.captions_ == "查询开关(0|1|2|3)")[0].fristValue_;
        params.Search = Search;
      }
      params.Pdctno_ = this.$refs.queryInfo.queryForm.Pdctno_;
      var arr1 = params.Pdctno_.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      if (params.Pdctno_ && typeof params.Pdctno_ === "string" && params.Pdctno_.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产编号查询不能小于5位");
        return;
      }
      params.Pdctno_ = arr1.join("");
      params.JoinFactoryId = this.$refs.queryInfo.queryForm.JoinFactoryId;
      this.params1 = params;
      this.getorderList(params);
      this.gethpPoolOrderInfo(params);
      this.gethpINGOrderInfo(params);
      this.dataVisible1 = false;
    },
    // 流程备注
    async ProcessNotesClick() {
      if (!this.ProcessNotesId) {
        this.$message.warning("请选择已合拼订单");
        return;
      }
      await flowReMarks(this.ProcessNotesId).then(res => {
        this.ProcessNotesData = res.data;
      });
      this.dataVisible2 = true;
    },
    handleOk2() {
      if (this.$refs.ProcessNotesInfo.editFlg) {
        this.$message.warning("编辑状态不可关闭弹窗");
        return;
      }
      this.dataVisible2 = false;
    },
    // 删除数据
    deleteDataClick() {
      if (!this.selectedRowList1.length > 0) {
        this.$message.warning("请选择订单");
        return;
      }
      let params = this.selectedRowList1;
      deleteData(params).then(res => {
        if (res.code) {
          this.$message.success("删除数据成功");
          this.gethpPoolOrderInfo();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 手动导入
    importErpDataMClick() {
      this.select1();
      if (!this.selectedRowList.length > 0) {
        this.$message.warning("请选择订单");
        return;
      }
      this.sigdataVisible = true;
      this.mmessage = "确认手动导入吗？";
      this.ttype = "2";
    },
    Ordercleaning() {
      setwaithPstate(this.menuData.guild_).then(res => {
        if (res.code) {
          this.$message.success("清除数据成功");
          this.getorderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 清除数据
    clearSizeClick() {
      clearSize({ ids: this.selectedRowList1 }).then(res => {
        if (res.code) {
          this.$message.success("清除数据成功");
          this.gethpPoolOrderInfo(); // 刷新订单池列表
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //文件上传
    UpdateFile() {
      if (!this.ProcessNotesId) {
        this.$message.warning("请选择已合拼订单");
        return;
      }
      let url =
        "H2GInterface://?TCL=UpLoadFile&JOB=" +
        this.ProcessNotesOrderNo +
        "&AreaID=" +
        this.fbAreaID +
        "&Guid=" +
        this.$refs.centerTable.id +
        "&mode=HP";
      window.open(url, "_blank");
    },
    //  数据/图形合拼
    dataMerging() {
      if (!this.ProcessNotesId) {
        this.$message.warning("请选择已合拼订单");
        return;
      }
      setOrderNoToErp(this.ProcessNotesId).then(res => {
        if (res.code) {
          let url = "H2GInterface://?TCL=EMS&JOB=" + this.ProcessNotesOrderNo + "&EMS_flows=CreateHP&AreaID=" + this.fbAreaID;
          //console.log('url',url)
          window.open(url, "_blank");
        } else {
          this.$message.error(res.message);
        }
      });

      //  let _this = this;
      //   var heartCheck = {
      //     timeout: 5000,//5秒
      //     timeoutObj: null,
      //     reset: function(){
      //       clearInterval(this.timeoutObj); return this;
      //     },
      //     start: function(){
      //       this.timeoutObj = setInterval(function(){ _this.websocket.send("HeartBeat");}, this.timeout)
      //     }
      //   };
      //   if ('WebSocket' in window) {
      //     _this.updateUrl("127.0.0.1:18181");
      //       _this.websocket = new WebSocket(_this.wsUrl);
      //   } else if ('MozWebSocket' in window) {
      //     _this.updateUrl("127.0.0.1:18181");
      //     _this.websocket = new MozWebSocket(_this.wsUrl) || null;
      //   } else {
      //     _this.updateUrl("sockjs/webSocketServer");
      //     _this.websocket = new SockJS(_this.wsUrl)
      //   }
      //   let processNotesId=this.ProcessNotesId
      //   let processNotesOrderNo=this.ProcessNotesOrderNo
      //   _this.websocket.onopen = function(){
      //     _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'CombinationBz',"Task":'btn_ExecutionGS',"Uid":processNotesId,'Data':processNotesOrderNo}))
      //   };
    },
    //封边输出
    panelEdgeBanding() {
      if (!this.ProcessNotesId) {
        this.$message.warning("请选择已合拼订单");
        return;
      }
      let url =
        "H2GInterface://?TCL=EMS&JOB=" + this.ProcessNotesOrderNo + "&EMS_flows=PANELEXPORT&AreaID=" + this.fbAreaID + "&Uid=" + this.ProcessNotesId;
      window.open(url, "_blank");
    },
    Edgebandingoutput() {
      waithPOrderid(this.menuData.id).then(res => {
        if (res.code && res.data) {
          let url =
            "H2GInterface://?TCL=EMS&JOB=" + this.menuData.pdctno_ + "&EMS_flows=PANELEXPORT&AreaID=" + this.menuData.tradeType + "&Uid=" + res.data;
          window.open(url, "_blank");
        } else {
          this.$message.error("该订单不允许封边输出");
        }
      });
    },
    //输出文件
    outputFile() {
      if (!this.ProcessNotesId) {
        this.$message.warning("请选择已合拼订单");
        return;
      }
      let url = "H2GInterface://?TCL=EMS&JOB=" + this.ProcessNotesOrderNo + "&EMS_flows=EXPORT_FILE&AreaID=" + this.fbAreaID;
      window.open(url, "_blank");
    },
    //合拼完成
    combineFinish() {
      if (!this.ProcessNotesId) {
        this.$message.warning("请选择已合拼订单");
        return;
      }
      this.sigdataVisible = true;
      this.mmessage = "确认合拼完成吗？";
      this.ttype = "9";
      this.id1 = this.$refs.centerTable.pdctno_;
      //  let _this = this;
      //   var heartCheck = {
      //     timeout: 5000,//5秒
      //     timeoutObj: null,
      //     reset: function(){
      //       clearInterval(this.timeoutObj); return this;
      //     },
      //     start: function(){
      //       this.timeoutObj = setInterval(function(){ _this.websocket.send("HeartBeat");}, this.timeout)
      //     }
      //   };
      //   if ('WebSocket' in window) {
      //     _this.updateUrl("127.0.0.1:18181");
      //       _this.websocket = new WebSocket(_this.wsUrl);
      //   } else if ('MozWebSocket' in window) {
      //     _this.updateUrl("127.0.0.1:18181");
      //     _this.websocket = new MozWebSocket(_this.wsUrl) || null;
      //   } else {
      //     _this.updateUrl("sockjs/webSocketServer");
      //     _this.websocket = new SockJS(_this.wsUrl)
      //   }
      //   let processNotesId=this.ProcessNotesId
      //   let processNotesOrderNo=this.ProcessNotesOrderNo
      //   _this.websocket.onopen = function(){
      //     _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'CombinationBz',"Task":'btn_CombineFinish',"Uid":processNotesId,'Data':processNotesOrderNo}))
      //   };
    },
    mouseleave() {
      // this.select1()
      // this.select2()
    },
    visibleMethod({ type, options, column }) {
      return true;
    },
    select1() {
      const selectRecords = this.$refs.xTable1.getCheckboxRecords();
      if (selectRecords.length) {
        this.selectedRowsData = selectRecords[selectRecords.length - 1];
        this.id1 = selectRecords[selectRecords.length - 1].pdctno_;
        let arr = [];
        for (var a = 0; a < selectRecords.length; a++) {
          arr.push(selectRecords[a].guild_);
        }
        this.selectedRowList = arr;
      }
      this.id1 = this.selectedRowsData.pdctno_;
    },
    currentChangeEvent({ row }) {
      this.selectedRowsData = row;
      this.id1 = row.pdctno_;
      let arr = [];
      arr.push(row.guild_);
      this.selectedRowList = arr;
      this.isSelecting = true;
    },
    cellContextMenuEvent({ row, column, $event }) {
      $event.preventDefault();
      // this.$refs.xTable1.setCurrentRow(row)
      this.menuData = row;
      this.lol = 1;
      this.rightClick1($event, row[column.field], row);
    },
    select2() {
      const selectRecords = this.$refs.xTable2.getCheckboxRecords();
      if (selectRecords.length) {
        this.selectedRowsData1 = selectRecords[selectRecords.length - 1];
        this.id1 = selectRecords[selectRecords.length - 1].pdctno_;
        let arr = [];
        for (var a = 0; a < selectRecords.length; a++) {
          arr.push(selectRecords[a].id);
        }
        this.selectedRowList1 = arr;
      }
      this.id1 = this.selectedRowsData1.pdctno_;
      // //console.log('selectedRowsData1',this.selectedRowsData1)
      // //console.log('this.selectedRowList1',this.selectedRowList1)
    },
    currentChangeEvent2({ row }) {
      this.selectedRowsData1 = row;
      this.id1 = row.pdctno_;
      let arr = [];
      arr.push(row.id);
      this.selectedRowList1 = arr;
      //console.log('已选择数据',this.selectedRowsData1, this.selectedRowList1)
    },
    cellContextMenuEvent2({ row, column, $event }) {
      $event.preventDefault();
      // this.$refs.xTable2.setCurrentRow(row)
      this.menuData = row;
      this.lol = 2;
      this.rightClick1($event, row[column.field], row);
    },
    select3() {
      const selectRecords = this.$refs.xTable2.getCheckboxRecords();
      if (selectRecords.length) {
        this.selectedRowsData1 = selectRecords[selectRecords.length - 1];
        this.id1 = selectRecords[selectRecords.length - 1].pdctno_;
        let arr = [];
        for (var a = 0; a < selectRecords.length; a++) {
          arr.push(selectRecords[a].id);
        }
        this.selectedRowList1 = arr;
      }
      // //console.log('selectedRowsData1',this.selectedRowsData1)
      // //console.log('this.selectedRowList1',this.selectedRowList1)
    },
    chatTest() {
      let hubUrl = process.env.VUE_APP_API_BASE_URL + "/signalr-hubs/chat";
      let userName = Cookie.get("userName");
      const connection = new signalR.HubConnectionBuilder()
        .withAutomaticReconnect() //断线自动重连
        .withUrl(hubUrl)
        .build();
      //启动
      this.signalR = connection;
      connection.start().catch(err => {
        //console.log("err",err);
      });
      //自动重连成功后的处理
      connection.onreconnected(connectionId => {
        //console.log(connectionId);
      });
      connection.on("ReceiveMessage", (key, message) => {
        // //console.log('key',key)
        if (key == userName) {
          this.spinning = false;
          this.gethpPoolOrderInfo(); // 合拼池
          this.gethpINGOrderInfo(); // 右上
          this.data4Source = []; // 右下
        }
      });
    },
    tbodymousedown(event, row) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = this.getTrPosition(event.target.parentElement).record.index;
        this.record = this.getTrPosition(event.target.parentElement).record.item;
      }
      this.$refs.centerTable.id = "";
      this.$refs.centerTable.pdctno_ = "";
      this.$refs.centerTable.selectRow1 = "";
    },
    tbodymousemove(event) {
      event.stopPropagation();
      //按住鼠标左键并拖动多选
      if (this.isDragging && event.button === 0) {
        let index = this.getTrPosition(event.target.parentElement).record.index;
        this.tbodySelectedItems(this.startIndex, index);
      }
    },
    tbodymouseup(event) {
      //左键
      let index = this.getTrPosition(event.target.parentElement).record.index;
      let record = this.getTrPosition(event.target.parentElement).record.item;
      this.isDragging = false;
      if (this.shiftKey && record.guild_ == this.record.guild_) {
        let rowKeys = this.selectedRowList;
        if (rowKeys.length > 0 && rowKeys.includes(record.guild_)) {
          rowKeys.splice(rowKeys.indexOf(record.guild_), 1);
        } else {
          rowKeys.push(record.guild_);
        }
        this.selectedRowList = rowKeys;
        if (this.selectedRowList.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowList = [record.guild_];
        }
      }
      this.shiftKey = false;
    },
    tbodySelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.data1Source.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].guild_);
      }
      var aa = new Set(this.selectedRowList.concat(arr));
      var arr1 = Array.from(aa);
      if (this.shiftKey) {
        this.selectedRowList = arr1;
      } else {
        this.selectedRowList = arr;
      }
      if (startIndex < endIndex) {
        this.selectedRowsData = this.data1Source.filter(item => {
          return item.guild_ == this.selectedRowList[this.selectedRowList.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.data1Source.filter(item => {
          return item.guild_ == this.selectedRowList[0];
        })[0];
      }
    },
    // 获取单元格位置
    getTrPosition(tr, type) {
      while (tr.tagName !== "TR") {
        tr = tr.parentElement;
      }
      let record = {};
      if (type == 2) {
        record = this.$refs.xTable2.getRowNode(tr);
      } else {
        record = this.$refs.xTable1.getRowNode(tr);
      }
      return {
        record,
      };
    },
    rowClassName(row) {
      if (row.rowid && this.selectedRowList.includes(row.rowid)) {
        return "row-background";
      }
      return null;
    },
    rowClassName1(row) {
      if (row.rowid && this.selectedRowList1.includes(row.rowid)) {
        return "row-background1";
      }
      return null;
    },
    tbodymousedown2(event, row) {
      event.stopPropagation();
      //一定是左键左键
      if (event.button === 0) {
        // 记录选择操作起始位置
        this.isDragging = true;
        this.startIndex = this.getTrPosition(event.target.parentElement, 2).record.index;
        this.record2 = this.getTrPosition(event.target.parentElement, 2).record.item;
      }
    },
    tbodymousemove2(event) {
      event.stopPropagation();
      //按住鼠标左键并拖动多选
      if (this.isDragging && event.button === 0) {
        let index = this.getTrPosition(event.target.parentElement, 2).record.index;
        this.tbodySelectedItems2(this.startIndex, index);
      }
    },
    tbodymouseup2(event) {
      //左键
      let index = this.getTrPosition(event.target.parentElement, 2).record.index;
      let record = this.getTrPosition(event.target.parentElement, 2).record.item;
      this.isDragging = false;
      if (this.shiftKey && record.id == this.record2.id) {
        let rowKeys = this.selectedRowList1;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowList1 = rowKeys;
        if (this.selectedRowList1.length == 1) {
          this.selectedRowsData1 = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData1 = record;
          this.selectedRowList1 = [record.id];
        }
      }
      this.shiftKey = false;
    },
    tbodySelectedItems2(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.data2Source.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      var aa = new Set(this.selectedRowList1.concat(arr));
      var arr1 = Array.from(aa);
      if (this.shiftKey) {
        this.selectedRowList1 = arr1;
      } else {
        this.selectedRowList1 = arr;
      }
      //  //console.log('this.selectedRowList1', this.selectedRowList1)

      if (startIndex < endIndex) {
        this.selectedRowsData1 = this.data2Source.filter(item => {
          return item.id == this.selectedRowList1[this.selectedRowList1.length - 1];
        })[0];
      } else {
        this.selectedRowsData1 = this.data2Source.filter(item => {
          return item.id == this.selectedRowList1[0];
        })[0];
      }
    },
    cellStyle({ row, column }) {
      if (column.type === "seq") {
        if (!row.lSize_ || !row.wSize_) {
          return {
            backgroundColor: "red",
          };
        }
      }
    },
  },
  mounted() {
    this.getorderList();
    this.gethpPoolOrderInfo();
    this.gethpINGOrderInfo();
    this.gethpFac();
    this.gethpFLFac();
    this.gethpAppConfig(); // 配置参数
    this.getClassList();
    // this.getFac();
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
    window.addEventListener("resize", this.dehandleResize, true);
    this.$nextTick(() => {
      this.chatTest();
      let tbody = this.$refs.xTable1.$el.querySelector("table tbody");
      setTimeout(() => {
        var toptable = document.getElementsByClassName("toptable")[0].children[1].children[1].children[0].children[1].children[1].children[1];
        var bottable = document.getElementsByClassName("bottable")[0].children[1].children[1].children[0].children[1].children[1].children[1];
        if (toptable) {
          toptable.addEventListener("mousedown", this.tbodymousedown);
          toptable.addEventListener("mouseup", this.tbodymouseup);
          toptable.addEventListener("mousemove", this.tbodymousemove);
        }
        if (bottable) {
          bottable.addEventListener("mousedown", this.tbodymousedown2);
          bottable.addEventListener("mouseup", this.tbodymouseup2);
          bottable.addEventListener("mousemove", this.tbodymousemove2);
        }
      }, 500);
      let tbody2 = this.$refs.xTable2.$el.querySelector("table tbody");
      if (tbody) {
        //主要给表体添加事件
        tbody.addEventListener("mousedown", this.tbodymousedown);
        tbody.addEventListener("mouseup", this.tbodymouseup);
        tbody.addEventListener("mousemove", this.tbodymousemove);
      }
      if (tbody2) {
        // //主要给表体添加事件
        tbody2.addEventListener("mousedown", this.tbodymousedown2, true);
        tbody2.addEventListener("mouseup", this.tbodymouseup2, true);
        tbody2.addEventListener("mousemove", this.tbodymousemove2, true);
      }
    });
  },
  beforeDestroy() {
    this.signalR.off("ReceiveMessage");
    if (this.signalR) {
      this.signalR
        .stop()
        .then(() => {
          console.log("连接已断开");
        })
        .catch(err => {
          console.error("连接断开失败: ", err);
        });
    }
    window.removeEventListener("resize", this.dehandleResize, true);
  },
};
</script>

<style lang="less" scoped>
.remarkstayle {
  /deep/.ant-spin-nested-loading {
    border-top: 1px solid #efefef;
  }
  /deep/.ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 4px 6px !important;
    border-left: 1px solid #f0f0f0;
    height: 36px;
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 4px 6px !important;
    border-left: 1px solid #f0f0f0;
    height: 36px;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
}
.fac {
  /deep/.ant-row {
    margin-bottom: 0;
  }
}
/deep/.ant-select-selection {
  width: 354px;
}
/deep/.vxe-table .vxe-body--row.row-background {
  background: #dfdcdc !important;
}
/deep/.vxe-table .vxe-body--row.row-background1 {
  background: #dfdcdc !important;
}
.iconstyle {
  color: #ff9900;
  position: absolute;
  margin: 0px;
  top: 9px;
  z-index: 99;
  left: 13px;
  font-size: 17px;
}
.iconstyle1 {
  font-size: 17px;
  color: #ff9900;
  position: absolute;
  margin: 0px;
  top: 3px;
  z-index: 99;
  right: 1px;
  height: 33px;
  background: #f8f8f9;
}
/deep/.anticon-caret-down {
  position: relative;
  float: left;
}
/deep/.anticon-caret-up {
  position: relative;
  float: left;
}
/deep/.vxe-input.is--suffix .vxe-input--inner {
  padding-right: 0.8em;
}
/deep/.vxe-table .vxe-cell--sort {
  display: none;
}
/deep/.haoshi {
  background-color: #0000ff;
  height: 35px;
  width: 75px;
  line-height: 35px;
}
/deep/.td-mouse-active {
  background: #ddd !important;
}
/deep/.vxe-table--render-default .vxe-cell {
  padding: 0 2px;
}
/deep/.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 7px 0;
}
// /deep/.vxe-body--column{
//   user-select: all;
// }
/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-modal-body {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-input {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}
/deep/.vxe-header--column {
  font-weight: 500;
}
/deep/.vxe-table .vxe-table--header-wrapper {
  color: #000000;
  font-family: PingFangSC-Regular, Sans-serif;
}
/deep/.vxe-table--render-default {
  color: #000000;
  font-family: PingFangSC-Regular, Sans-serif;
}
/deep/.vxe-loading > .vxe-loading--chunk {
  color: #ff9900;
  font-size: 22px;
}
/deep/.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon {
  color: #ff9900;
}
/deep/ .vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon {
  color: #ff9900;
}
/deep/.vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon {
  color: #ff9900;
}
/deep/.vxe-input.is--active .vxe-input--inner {
  border: 1px solid #ff9900;
}
/deep/.vxe-input .vxe-input--suffix {
  color: #c0c4cc;
}
/deep/.vxe-input:not(.is--disabled) .vxe-input--suffix:active .vxe-input--clear-icon {
  color: #c0c4cc;
}
/deep/.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
  font-weight: 500;
  color: #adafb5;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-table-body {
  overflow-y: scroll !important;
  // overflow: overlay !important; //谷歌
}
/deep/.ant-modal-body {
  padding: 24px 24px !important;
}
.CuttingManagement {
  /deep/ .userStyle {
    user-select: none !important;
  }
  /deep/.rowBackgroundColor {
    background: #dfdcdc !important;
  }
  /deep/.rowColor {
    color: red;
  }
  // /deep/.ant-table-body {
  //   overflow: auto!important; //谷歌
  // }
  user-select: none;
  .content {
    display: flex;
    /deep/ .ant-table-scroll .ant-table-header.ant-table-hide-scrollbar .ant-table-thead > tr:only-child > th:last-child {
      border-right-color: #f0f0f0;
    }
  }
  .rowcolor {
    background: #fff9e6;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
  }
  .footer {
    .actionBox {
      overflow: hidden;
      width: 100%;
      border: 2px solid #e9e9f0;
      border-top: none;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      form {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #000000;
        font-weight: 500;
      }
    }
    .ant-card-body {
      .ant-table-pagination.ant-pagination {
        float: right;
        margin: 4px 0 6px 10px;
        position: absolute;
        bottom: -40px;
      }
      padding: 0;
      // .clickRowSty2{
      //   background: #fff9e6;
      // }
      .bacStyle {
        background: #dfdcdc !important;
      }
    }
  }
  .left {
    /deep/ .ant-table-fixed {
      .ant-table-tbody {
        .ant-table-row {
          .orderClass {
            user-select: all;
          }
        }
      }
    }
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    .tabRightClikBox1 {
      display: flex;
      align-items: center;
      flex-direction: column;
      /deep/.vxe-contextmenu-item {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        text-align: center;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
    }
    .tabRightClikBox {
      //  border:2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        text-align: center;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
  .right {
    .machine {
      display: flex;
      .minClass2 {
        /deep/ .ant-table-body {
          min-height: 342px;
        }
      }
      .minClass3 {
        /deep/ .ant-table-body {
          min-height: 322px;
        }
      }
      .left {
        width: 50%;
      }
      .right {
        width: 50%;
        /deep/ .ant-tabs-top-bar {
          height: 32px !important;
          margin-bottom: 0 !important;
          line-height: 32px !important;
        }
      }

      // /deep/ .ant-table {
      //   tr.ant-table-row-selected td {
      //     background: #DFDCDC!important;
      //   }
      //   tr.ant-table-row-hover td {
      //     background: #DFDCDC!important;
      //   }
      // }
    }
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
  /deep/.vxe-table--render-default .vxe-body--row.row--current {
    background: none;
  }
  /deep/.vxe-table--render-default .vxe-body--row.row--hover {
    background: #dfdcdc !important;
  }
  /deep/.vxe-table--render-default .vxe-body--row.row--checked {
    background: #dfdcdc;
  }
  /deep/ .ant-table {
    .ant-table-selection-col {
      width: 30px;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
  }
  background: #ffffff;
  .minClass {
    /deep/ .ant-table-body {
      min-height: 342px;
    }
  }
  .minClass1 {
    /deep/ .ant-table-body {
      min-height: 342px;
    }
  }
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 7px 2px;
        }
      }
    }
    .ant-table-tbody {
      .ant-input {
        padding: 0;
        border: 0;
        border-radius: 0;
        margin: -5px 0;
        text-align: center;
      }
      tr {
        td {
          padding: 7px 2px;
        }
      }
    }
  }

  /deep/ .ant-table-body {
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 7px 2px;
        }
      }
    }
  }
  /deep/ .ant-table-scroll .mouseentered {
    overflow-x: scroll !important;
  }
}
</style>
