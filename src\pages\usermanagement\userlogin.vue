<template>
  <common-layout>
    <div class="login_box" ref="login_box">
      <div class="login_right">
        <div class="top">
          <div class="header" style="display: flex; position: relative; left: 15%">
            <img class="title" style="width: 40px; margin: 0 10px" src="@/assets/img/bn2.png" />
            <span class="title">{{ systemName }}</span>
          </div>
        </div>
        <div style="display: flex">
          <div style="width: 100%">
            <a-tabs default-active-key="1">
              <a-tab-pane key="1" tab="用户登录" force-render>
                <div class="login">
                  <a-form :form="form" @submit="onSubmit">
                    <a-alert type="error" :closable="true" v-show="error" :message="error" showIcon style="margin-bottom: 24px" />
                    <a-form-item>
                      <a-input
                        autocomplete="autocomplete"
                        class="input1"
                        size="large"
                        placeholder=""
                        v-decorator="['name', { rules: [{ required: true, message: '请输入账户名', whitespace: true }] }]"
                      >
                        <a-icon slot="prefix" type="user" />
                      </a-input>
                    </a-form-item>
                    <a-form-item>
                      <a-input
                        size="large"
                        class="input2"
                        placeholder="*"
                        autocomplete="autocomplete"
                        type="password"
                        v-decorator="['password', { rules: [{ required: true, message: '请输入密码', whitespace: true }] }]"
                      >
                        <a-icon slot="prefix" type="lock" />
                      </a-input>
                    </a-form-item>
                    <a-form-item>
                      <a-button
                        style="width: 100%; margin-top: 24px"
                        size="large"
                        htmlType="submit"
                        type="primary"
                        :loading="logging"
                        class="login_btn"
                        >登录</a-button
                      >
                    </a-form-item>
                  </a-form>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </div>
    </div>
  </common-layout>
</template>
<script>
import { custverification, wipdatalHDC } from "@/services/usermanagement/index.js";
import CommonLayout from "@/layouts/CommonLayout";
import { mapMutations } from "vuex";
import { setAuthorizationuser } from "@/utils/request";
export default {
  name: "userlogin",
  components: { CommonLayout },
  data() {
    return {
      form: this.$form.createForm(this),
      error: "",
      UserInformation: {},
      logging: false,
    };
  },
  computed: {
    systemName() {
      return this.$store.state.setting.systemName;
    },
  },
  methods: {
    ...mapMutations("account", ["setUserinfo"]),
    onSubmit(e) {
      e.preventDefault();
      this.form.validateFields(err => {
        if (!err) {
          this.logging = true;
          const name = this.form.getFieldValue("name");
          const password = this.form.getFieldValue("password");
          custverification(name, password)
            .then(res => {
              if (res.code) {
                const { data } = res;
                this.UserInformation = data;
                this.setUserinfo(this.UserInformation);
                if (data.tradeType == 38) {
                  // 对于 tradeType == 38，调用 wipdatalHDC 并等待完成后再跳转
                  return wipdatalHDC(data.custNo).then(() => {
                    this.handleLoginSuccess();
                  });
                } else {
                  wipdatalHDC(data.custNo).then(() => {});
                  // 对于其他情况，直接调用登录成功逻辑
                  this.handleLoginSuccess();
                }
              } else {
                throw new Error(res.message || "登录失败，请检查用户名和密码");
              }
            })
            .catch(err => {
              console.error("登录失败:", err);
              this.$message.error(err.message || "登录失败，请稍后重试");
            })
            .finally(() => {
              this.logging = false;
            });
        }
      });
    },

    handleLoginSuccess() {
      this.$message.success("登录成功");
      this.$router.push("/usermanagement");
      localStorage.setItem("UserInformation", JSON.stringify(this.UserInformation));
      setAuthorizationuser({
        token: this.UserInformation.clientLoginKey,
        expireAt: new Date(new Date().getTime() + 3600000),
      });
      localStorage.removeItem("pageinfo");
      localStorage.removeItem("tablist1");
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 24px;
}
.qrcode iframe {
  padding-left: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
/deep/.ant-btn-lg {
  padding: 0 3px !important;
  font-size: 14px;
}
.common-layout {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .login_box {
    background-color: transparent;
    height: 460px;
    display: flex;
    top: 50%;
    transform: translate(0, 2px);
    .login_left {
      width: 50%;
      height: 100%;
      float: left;
      background: #f90;
      display: none;
    }
    .login_right {
      width: 100%;
      height: 100%;
      background: #fff;
      padding: 60px 45px 0;
    }
  }
  .top {
    text-align: center;
    margin-bottom: 30px;
    .header {
      height: 44px;
      line-height: 44px;
      a {
        text-decoration: none;
      }
      .logo {
        height: 44px;
        vertical-align: top;
        margin-right: 16px;
      }
      .title {
        font-size: 28px;
        color: @title-color;
        font-weight: 600;
        position: relative;
        top: 2px;
      }
    }
    .desc {
      font-size: 14px;
      color: @text-color-second;
      margin-top: 12px;
      margin-bottom: 40px;
    }
  }
  .login {
    width: 350px;
    margin: 0 auto;
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
</style>
