<template>
    <a-card>
       <my-pagination v-if="total !== 0" :total="total" v-model="pageNum" :pageSize.sync="pageSize" @change="handlePageChange" @showSizeChange="showSizeChange"></my-pagination>
    </a-card>
</template>
<script>
import myPagination  from '@/components/pagination/index'
export default {
    components: {
        myPagination 
    },
     data() {
        return {
            total: 50,
            pageNum: 1,
            pageSize: 10,
        };
    },
    methods: {
        handlePageChange (pageNum, pageSize) {
            this.pageNum = pageNum
            this.pageSize = pageSize
        },
        showSizeChange (pageNum, pageSize) {
            this.pageNum = pageNum
            this.pageSize = pageSize
        },
    }
}
</script>
<style lang="less" scoped>

</style>