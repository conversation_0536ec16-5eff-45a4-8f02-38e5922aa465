import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'





export async function getHeadList(id) {
    return request("/api/app/e-mSFrm-ui-dgv-config/get-dgv-list?names="+id, METHOD.GET)
}

export async function getList(params) {
    return request("/api/app/e-mSCust-module-no/cust-info", METHOD.GET,params)
}

export async function custDetails(id) {
    return request("/api/app/e-mSCust-parameter/get-cust-parameter-info?strGuid="+id, METHOD.GET)
}

export async function getCustConfig(id) {
    return request("/api/app/e-mSFrm-ui-group-config/get-group-list?uiguid="+id, METHOD.GET)
}
export async function changeConfig(params) {
    return request("/api/app/e-mSCust-module-no/update-cust", METHOD.POST,params)
}


export async function addConfig(params) {
    return request("/api/app/e-mSCust-module-no/cust", METHOD.POST,params)
}


export async function deleData(params) {
    return request("/api/app/e-mSCust-module-no/delete-cust?ids="+params, METHOD.DELETE)
}
export async function newConfig(params) {
    return request("/api/app/e-mSCust-ship-loction-list/cust-ship-loction-list", METHOD.POST,params)
}


export async function delSite(params) {
    return request("/api/app/e-mSCust-ship-loction-list/"+params+"/ship-loction-list", METHOD.DELETE)
}


export async function comSite(params) {
    return request("/api/app/e-mSCust-ship-loction-list/update-cust-ship-loction-list", METHOD.POST,params)
}

export async function updataSite(params) {
    return request(`/api/app/e-mSCust-ship-loction-list/${id}/get-cust-ship-loction-list`, METHOD.GET,params)
}

export default {
    getList,
    custDetails,
    getHeadList,
    getCustConfig,
    changeConfig,
    addConfig,
    deleData,
    newConfig,
    delSite,
    comSite,
    updataSite
}