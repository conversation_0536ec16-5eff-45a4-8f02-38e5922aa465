<template>
  <a-spin :spinning="spinning">
    <div class="projectReview">
      <div
        class="mainContent"
        style="width: 100%; border: 1px solid rgb(233, 233, 240); border-right: 2px solid rgb(233, 233, 240); user-select: none; height: 100%"
        ref="tableWrapper"
      >
        <main-table
          :reviewcolumns="reviewcolumns"
          :pagination="pagination"
          @handleTableChange="handleTableChange"
          :rowKey="(record, index) => `${index + 1}`"
          :Reviewsource="Reviewsource"
          class="leftstyle"
          ref="mainTable"
        />
      </div>
      <div class="footerAction" style="user-select: none; margin-left: -2px">
        <footer-action
          @queryClick="queryClick"
          :total="pagination.total"
          @summary="summary"
          @checkClick="checkClick"
          @FinishClick="FinishClick"
          @ReturnClick="ReturnClick"
        />
      </div>
      <!--按钮检查-->
      <a-modal title="检查信息" :visible="checkVisible" @cancel="checkVisible = false" destroyOnClose centered :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back1" type="primary" v-if="check" @click="continueclick">继续</a-button>
          <a-button key="back" @click="checkVisible = false">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.info == '1'" style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="dataVisible = false"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form>
          <a-row>
            <a-col :span="24">
              <a-form-item label="流水编号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input allowClear autoFocus v-model="formdata.serialNo"> </a-input>
              </a-form-item>
              <a-form-item label="生产型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input allowClear v-model="formdata.orderNo"> </a-input>
              </a-form-item>
              <a-form-item label="订单号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input allowClear v-model="formdata.businessOrderNo"> </a-input>
              </a-form-item>
              <a-form-item label="客户代码" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-input allowClear v-model="formdata.custNo"> </a-input>
              </a-form-item>
              <a-form-item label="评审类别" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-select allowClear v-model="formdata.source">
                  <a-select-option :value="0">市场评审</a-select-option>
                  <a-select-option :value="1">工程评审</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="状态" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-select allowClear v-model="formdata.status">
                  <a-select-option v-for="(item, index) in StatusList" :key="index" :value="item.key">{{ item.value }}</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="评审创建时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-date-picker format="YYYY-MM-DD" @change="StartTime"></a-date-picker>
              </a-form-item>
              <a-form-item label="结束时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
                <a-date-picker format="YYYY-MM-DD" @change="EndTime" :disabled="formdata.StartTime ? false : true"></a-date-picker>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <a-modal
        title="原因填写"
        :visible="confirmvisible"
        @cancel="confirmvisible = false"
        @ok="dehandleOk1"
        ok-text="确定"
        :confirmLoading="confirmload"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        centered
      >
        <div>
          <a-form>
            <a-row>
              <a-col :span="24">
                <a-form-item
                  :label="confirmtype == 'return' ? '*退回原因' : '*完结原因'"
                  :labelCol="{ span: 4 }"
                  :wrapperCol="{ span: 20 }"
                  class="required"
                >
                  <a-textarea placeholder="请输入原因" v-model="Reason" allowClear autoFocus :auto-size="{ minRows: 4, maxRows: 8 }"> </a-textarea>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import MainTable from "@/pages/gongju/Reviewsummary/modules/MainTable";
import FooterAction from "@/pages/gongju/Reviewsummary/modules/FooterAction";
import { checkPermission } from "@/utils/abp";
import { reviewsumuplist, reviewsumupback, allreviewfinish, reviewbuttoncheck } from "@/services/gongju/Reviewinitiated";
const reviewcolumns = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "流水编号",
    align: "left",
    dataIndex: "serialNo",
    ellipsis: true,
    width: 130,
  },
  {
    title: "评审类别",
    align: "left",
    ellipsis: true,
    dataIndex: "reviewSource",
    width: 80,
  },
  {
    title: "订单号",
    align: "left",
    dataIndex: "businessOrderNo",
    ellipsis: true,
    width: 110,
  },
  {
    title: "生产型号",
    dataIndex: "orderNo",
    align: "left",
    ellipsis: true,
    width: 120,
  },
  {
    title: "客户型号",
    align: "left",
    dataIndex: "customerModel",
    ellipsis: true,
    width: 250,
  },
  {
    title: "状态",
    align: "left",
    dataIndex: "statusStr",
    ellipsis: true,
    width: 80,
  },
  {
    title: "订单状态",
    align: "left",
    dataIndex: "",
    ellipsis: true,
    width: 70,
  },
  {
    title: "退回原因",
    width: 200,
    ellipsis: true,
    dataIndex: "reviewBackReason",
    align: "left",
  },

  {
    title: "提出人",
    align: "left",
    dataIndex: "createName",
    ellipsis: true,
    width: 80,
  },
  {
    title: "提出时间",
    align: "left",
    dataIndex: "createTime",
    ellipsis: true,
    width: 130,
  },
  {
    title: "评审人",
    align: "left",
    dataIndex: "reviewUser",
    ellipsis: true,
    width: 80,
  },
  {
    title: "策划人",
    align: "left",
    dataIndex: "setPlanUser",
    ellipsis: true,
    width: 100,
  },
  {
    title: "策划时间",
    align: "left",
    dataIndex: "setPlanUserSign",
    ellipsis: true,
    width: 130,
  },
];
export default {
  data() {
    return {
      checkVisible: false,
      check: false,
      checkType: "",
      checkData: [],
      StatusList: [
        { key: 0, value: "待发起提交" },
        { key: 10, value: "待管理审核" },
        { key: 20, value: "待评审分配" },
        { key: 30, value: "待评审" },
        { key: 40, value: "会同评审中" },
        { key: 50, value: "待策划" },
        { key: 60, value: "待策划审核" },
        { key: 70, value: "待总结" },
        { key: 80, value: "待总结审核" },
        { key: 90, value: "已完结" },
        { key: 100, value: "已删除" },
      ],
      Reason: "",
      confirmmessage: "",
      spinning: false,
      confirmtype: "",
      confirmvisible: false,
      confirmload: false,
      formdata: {},
      dataVisible: false,
      reviewcolumns,
      Reviewsource: [],
      buttonsmenu: false,
      text: "",
      isCtrlPressed: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  components: {
    MainTable,
    FooterAction,
  },
  created() {
    this.dehandleOk1 = this.debounce(this.handleOk1, 500);
    this.$nextTick(() => {
      this.getreviewdata();
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    checkPermission,
    FinishClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要完成的订单");
        return;
      }
      this.confirmvisible = true;
      this.confirmtype = "finish";
      this.Reason = "";
    },
    ReturnClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要退回的订单");
        return;
      }
      this.confirmvisible = true;
      this.confirmtype = "return";
      this.Reason = ""; //清空回退原因
    },
    StartTime(value, dateString) {
      this.$set(this.formdata, "StartTime", dateString);
    },
    EndTime(value, dateString) {
      this.$set(this.formdata, "EndTime", dateString);
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        this.dataVisible = true;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        e.preventDefault();
      } else if (e.keyCode == "13" && this.confirmvisible) {
        this.dehandleOk1();
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    queryClick() {
      this.dataVisible = true;
      this.formdata = {};
    },
    continueclick() {
      if (this.checkType == "ReviewSumUp") {
        this.reviewJump("allcheck");
      } else if (this.checkType == "ReviewSumUpCheck") {
        this.reviewJump("all");
      }
    },
    buttonCheck(key, name) {
      reviewbuttoncheck(this.$refs.mainTable.proOrderId, key).then(res => {
        if (res.code) {
          if (res.data.length) {
            this.checkData = res.data;
            this.checkVisible = true;
            this.checkType = name;
            this.check = !this.checkData.some(item => item.error == 1);
          } else {
            this.Jumpreview(name);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Jumpreview(review_Type) {
      this.$router.push({
        path: "Judgingcontent",
        query: {
          OrderNo: this.$refs.mainTable.selecdata.orderNo,
          businessOrderNo: this.$refs.mainTable.selecdata.businessOrderNo,
          joinFactoryId: this.$refs.mainTable.selecdata.joinFactoryId,
          id: this.$refs.mainTable.proOrderId,
          reviewSource: this.$refs.mainTable.selecdata.reviewsource,
          review_Type: review_Type,
          page: "2",
          reviewNo: this.$refs.mainTable.selecdata.reviewNo,
        },
      });
    },
    checkClick() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要总结审核的订单");
        return;
      }
      this.buttonCheck("ReviewSumUpCheck", "allcheck");
    },
    summary() {
      if (!this.$refs.mainTable.proOrderId) {
        this.$message.warn("请选择需要评审总结的订单");
        return;
      }
      this.buttonCheck("ReviewSumUp", "all");
    },
    handleOk1() {
      if (!this.Reason) {
        this.$message.warn("请输入原因");
        return;
      }
      this.spinning = true;
      this.confirmvisible = false;
      let fetch = this.confirmtype == "return" ? reviewsumupback : allreviewfinish;
      fetch(this.$refs.mainTable.proOrderId, this.Reason)
        .then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.getreviewdata();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleOk() {
      if (JSON.stringify(this.formdata) != "{}") {
        this.getreviewdata(this.formdata);
      }
      this.dataVisible = false;
    },
    getreviewdata(data) {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      this.spinning = true;
      var obj = Object.assign(params, data);
      reviewsumuplist(obj)
        .then(res => {
          this.Reviewsource = res.items;
          setTimeout(() => {
            this.handleResize();
          }, 0);
          this.pagination.total = res.totalCount;
          this.proOrderId = "";
          this.selecdata = {};
          if (params.orderNo) {
            this.proOrderId = this.Reviewsource[0].id;
            this.selecdata = this.Reviewsource[0];
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      let params = this.formdata;
      if (JSON.stringify(params) != "{}") {
        this.getreviewdata(params);
      } else {
        this.getreviewdata();
      }
    },
    handleResize() {
      let screenHeight = window.innerHeight;
      let mainContent = document.getElementsByClassName("mainContent")[0];
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (this.Reviewsource.length == 0) {
        this.$refs.tableWrapper.style.height = screenHeight - 143 + "px";
      } else {
        this.$refs.tableWrapper.style.height = 0;
      }
      mainContent.style.height = screenHeight < 920 ? screenHeight - 135 + "px" : "775px";
      if (leftstyle && this.Reviewsource.length != 0) {
        leftstyle.style.height = screenHeight - 173 + "px";
      } else {
        leftstyle.style.height = 0;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
.box {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.box1 {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.footerAction {
  width: 100%;
  height: 50px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  form {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #fff;
  }
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
.tabRightClikBox1 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-table-fixed-header .ant-table-body-inner {
  overflow: hidden;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  margin-left: 10px;
  position: fixed;
  margin: 11px 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectReview {
  background-color: #ffffff;
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/.ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 7px 2px !important ;
    overflow-wrap: break-word;
  }
  /deep/ .ant-table {
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-thead > tr > th {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
  /deep/.ant-table-row-cell-break-word {
    border-right: 1px solid #efefef;
  }
}
/deep/.mainContent {
  .mintable {
    .ant-table-body-inner {
      max-height: 737px !important;
    }
    .ant-table-pagination {
      margin: 6px 0;
      z-index: 99;
      position: absolute;
      bottom: -6.5%;
      margin-left: 1%;
    }
    .ant-table-body {
      min-height: 738px;
    }
  }
  border: 2px solid rgb(233, 233, 240);
  border-bottom: 4px solid rgb(233, 233, 240);
}
</style>
