import { request, METHOD } from '@/utils/request';
// 获取代派分页列表接口
export async function projectDispathgetOrderList(params) {
    return request("/api/app/pcb-order-list/page-list", METHOD.GET,params)
}
// 人员列表
export async function projectPeopleList() {
    return request("/api/app/pcb-order-list/user-list", METHOD.GET)
}
// 人员订单列表
export async function projectPeopleInfo(params) {
    return request(`/api/app/pcb-order-list/by-user?userLoginID=${params}`, METHOD.GET,)
}
// 订单分派     
export async function projectOrderAssign(params) {
    return request("/api/app/order-dispatch/send-order", METHOD.POST, params)
}
// 分派回退     
export async function backSendOrder(id) {
    return request(`/api/app/order-dispatch/${id}/back-send-order`, METHOD.POST, )
}
//订单回退
export async function backtoenquiry(id) {
    return request(`/api/app/order-dispatch/${id}/back-to-enquiry`, METHOD.POST, )
}
// // 人员是否请假
// export async function projectOrderInfo(id) {
//     return request("/api/app/e-mSMake-module-no/m-iIs-leave1?userid=" + id, METHOD.POST)
// }
// // 请假确认
// export async function projectOrderLeave(params) {
//     return request("/api/app/e-mSMake-module-no/m-iIs-leave", METHOD.POST, params)
// }
// 获取取单设置
export async function projectOrderInfo(id) {
    return request(`/api/app/e-mSTSys-user-assignment-pre/order-retrieval-settings?username=${id}`, METHOD.GET)
}
// 取单设置
export async function projectOrderLeave(params) {
    return request("/api/app/e-mSTSys-user-assignment-pre/set-order-retrieval-settings", METHOD.POST, params)
}

// 获取搜索列表
export async function projectBoardInfoList(params) {
    return request('/api/app/e-mSMake-module-no/m-iBoard',METHOD.GET,params)
}
// 工程派单管理汇总
export async function dispatchTotal(params) {
    return request('/api/app/e-mSMake-module-no/m-iTotal',METHOD.GET,params)
}