<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1" style="font-size: 13px;" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ltreport1220" style=" padding: 25px;font-family: 宋体;color: black;font-weight: bold;" >
            <div style="width: 100%;display: flex;">
                    <div style="text-align: center;width: 100%;font-size: 32px;">湖北龙腾电子科技股份有限公司<div>
                            <span style="font-size: 32px;letter-spacing: 5px;">报价单</span>
                        </div>    
                    </div>                            
            </div>
            <div style="display: flex;line-height: 3ch;">
                <div style="width:50%;z-index: 99;font-size: 15px;">
                    <div>客户名称(Customer name):{{ LTreportdata.name_ }}</div>
                    <div>联系人(connect person):{{ LTreportdata.link_ }}</div>
                    <div>电话(tel No.):{{ LTreportdata.meTel_ }}</div>
                    <div>E-MAIL:{{ LTreportdata.eml_ }}</div>
                </div>
                <div style="z-index: 99;width:50%">
                    <div style="float: right;font-size: 15px;">
                        <div>名称(marketing name):{{ LTreportdata.factory_  }}</div>
                        <div>联系人(connect person):{{ LTreportdata.party_  }}</div>
                        <div>电话(tel No.):{{ LTreportdata.facPhone_ }}</div>
                        <div>E-MAIL:{{ LTreportdata.http_ }}</div>
                        <div>报价币种:{{ LTreportdata.currency_ }}</div>
                    </div>                   
                </div>               
            </div>
            <div>
                <table border="1" style="text-align: center;margin-top: 5px;width: 100%;border-top: 1px solid black;border-left: 1px solid black;">
                    <thead>
                        <tr>
                            <td rowspan="2" style="background: #fce4d6;">No</td>
                            <td rowspan="2" style="background: #fce4d6;">物料</td>
                            <td rowspan="2" style="background: #fce4d6;">物料说明</td>
                            <td rowspan="2" style="background: #fce4d6;">单位</td>
                            <td rowspan="2" style="background: #fce4d6;">需求数量</td>
                            <td rowspan="2" style="background: #fce4d6;">需求时间</td>
                            <td rowspan="2" style="background: #fce4d6;">层数</td>
                            <td rowspan="2" style="background: #fce4d6;">板厚</td>
                            <td colspan="3" style="background: #fce4d6;">尺寸(MM)</td>
                            <td rowspan="2" style="background: #fce4d6;">材料&板厚&成品铜厚</td>
                            <td rowspan="2" style="background: #fce4d6;">表面处理</td>
                            <td colspan="6" style="background: #ffff00;">样品</td>
                            <td colspan="4" style="background: #92d050;">批量</td>
                            <td rowspan="2" style="background: #ffe699;">此批次需求均价</td>
                            <td rowspan="2" style="background: #ffe699;">总金额</td>
                            <td rowspan="2" style="background: #ffe699;">交期</td>
                            <td rowspan="2" style="background: #fce4d6;">备注</td>
                        </tr>
                        <tr>
                            <td style="background: #fce4d6;">长</td>
                            <td style="background: #fce4d6;">宽</td>
                            <td style="background: #fce4d6;">拼版</td>
                            <td style="background: #ffff00;">含税单价</td>
                            <td style="background: #ffff00;">表面费</td>
                            <td style="background: #ffff00;">工程费</td>
                            <td style="background: #ffff00;">加急费</td>
                            <td style="background: #ffff00;">其他费用</td>
                            <td style="background: #ffff00;">MOV数量</td>
                            <td style="background: #92d050;">含税单价</td>
                            <td style="background: #92d050;">MPQ</td>
                            <td style="background: #92d050;">MOQ</td>
                            <td style="background: #92d050;">测试架</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in LTreportdata.price' :key="index">
                            <td>{{ item.no}}</td>
                            <td>{{ item.custPo }}</td>
                            <td>{{ item.custName }}</td>
                            <td>{{ item.bType }}</td>
                            <td>{{ item.qty }}</td>
                            <td>{{ item.diff }}</td>
                            <td>{{ item.lay }}</td>
                            <td>{{ item.boardT }}</td>
                            <td>{{ item.setBoardWidth }}</td>
                            <td>{{ item.setBoardHeight }}</td>
                            <td>{{ item.su }}</td>
                            <td>{{ item.solder }}</td>
                            <td>{{ item.surf }}</td>
                            <td>{{ item.pcs }}</td>
                            <td>{{ item.surface }}</td>
                            <td>{{ item.eng }}</td>
                            <td>{{ item.urgent }}</td>
                            <td>{{ item.zkPrice }}</td>
                            <td>{{ item.setQty }}</td>
                            <td>{{ item.zhPrice }}</td>
                            <td>{{ item.zfPrice }}</td>
                            <td>{{ item.test }}</td>
                            <td>{{ item.other }}</td>
                            <td>{{ item.total }}</td>
                            <td>{{ item.custdate }}</td>
                            <td>{{ item.notes }}</td>
                        </tr>
                    </tbody>                
                </table>
            </div>
            <div style="z-index:99;position: relative;">
                <div style="display: flex;line-height:2.8ch;margin-top: 8px;font-size: 15px;position: relative;">
                    <div>
                    <div style="font-size: 16px;">备注(Remark):{{ LTreportdata.noteSure }}</div>  
                       &nbsp;1 返单最低消费(MOQ):{{LTreportdata.freight}}<br/>
                       &nbsp;2 交货方式(Delivery):{{LTreportdata.deliveryType}}<br/>
                       &nbsp;3 交货地点(Delivery add):{{LTreportdata.deliveryAdd}}<br/>
                       &nbsp;4 付款方式(Payment method):{{LTreportdata.clearingForm}}<br/>
                       &nbsp;5 价格含税状况(Tax):{{LTreportdata.consignee}}<br/>
                       &nbsp;6 此报价有效期(Period of time):{{LTreportdata.consigneeTel}}<br/>
                       &nbsp;7 验收标准:{{LTreportdata.ipcLevel}}<br/>
                    </div>
                </div>
            </div>
            <div style="display: flex;width: 100%;padding-top: 15px;font-size: 15px;">
                <div style="width: 40%;line-height: 5ch;">
                    <div>客户确认: <span class="Underline"></span></div>
                    <div>&emsp;&emsp;日期: <span class="Underline"></span></div>
                </div>
                <div style="width: 35%;line-height: 5ch;">
                    <div>核准: <span class="Underline"></span>{{LTreportdata.discer}}</div>
                    <div>日期: <span class="Underline"></span>{{LTreportdata.date_}}</div>
                </div>
                <div style="width: 25%;line-height: 5ch;display: flex;position: relative;">
                    <div style="z-index: 99;">
                        <div>审核: <span class="Underline"></span>{{LTreportdata.quoter}}</div>
                        <div>日期: <span class="Underline"></span>{{LTreportdata.date_}}</div>
                    </div>
                    <img  src="@/assets/img/lthtz.png" 
                    style="position: relative;
                            z-index: 0;
                            top:-20px ;
                            left: -100px;
                            display: block;
                            width: 150px;
                            transform: rotate(353deg);"  >    
                </div>
            </div>
         
        </div>
    </div>
  </template>
  <script>
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        amountto:0,
        printObj1:{
            id: "ltreport1220", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
        this.amountto =0 
        for (let index = 0; index < this.LTreportdata.price.length; index++) {
            if(this.LTreportdata.price[index].total && this.LTreportdata.price[index].total!='/'){
                this.amountto +=Number(this.LTreportdata.price[index].total)
            } 
        }
        this.amountto = this.amountto ? this.getFloat(this.amountto,4) : 0
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            htmlToPdfa3('ltreport1220',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
   .Underline {
    position: relative;
    display: inline-block;
    }
    .Underline::after {
    content: "";
    position: absolute;
    left: 0;
    bottom:-8px; /* 下划线距离文字底部的距离 */
    width: 200px; /* 下划线宽度 */
    height: 2px; /* 下划线高度 */
    background-color: rgb(107, 106, 106); /* 下划线颜色 */
    }
  .pdfDom1{
      height: 650px;
      overflow: auto;
      table >thead> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
    table >tbody> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
  }
  </style>
