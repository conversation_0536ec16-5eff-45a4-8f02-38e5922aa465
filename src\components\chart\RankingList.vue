<template>
  <div class="rank">
    <h4 class="title">{{title}}</h4>
    <a href="#" class="showMoreClick" v-if="this.list.length > 12" @click="showMoreClick">
      <a-icon :type="showFlag ? 'down' : 'up'" />
      {{showFlag ? "收起全部" : "显示全部"}}
    </a>
    <ul class="list">
      <li :key="index" v-for="(item, index) in listSplice">
        <span :class="index < 3 ? 'active' : null">{{index + 1}}</span>
        <span >{{item.code}}</span>
        <span >{{item.area}}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'RankingList',
  props: ['title', 'list'],
  data () {
    return {
      filterList: this.list,
      showFlag: false,
    }
  },
  computed: {
    listSplice () {
      if (this.showFlag) {
        return this.filterList;
      } else {
        if(this.filterList.length > 12) {
          return this.filterList.slice(0, 13);
        } else {
          return this.filterList;
        }
      }
    }
  },
  methods:{
    showMoreClick () {
      this.showFlag = !this.showFlag
    }
  }
}
</script>

<style lang="less" scoped>
  .rank{
    padding: 0 32px 32px 72px;
    position: relative;
    .title{
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      line-height: 22px;
      font-weight: 600;
    }
    .showMoreClick {
      position: absolute;
      right: 5px;
      top: 5px;
      text-decoration: revert;
    }
    .list{
      margin: 25px 0 0;
      padding: 0;
      list-style: none;
      li {
        margin-top: 16px;
        span {
          color: @text-color-second;
          font-size: 14px;
          line-height: 22px;
        }
        span:first-child {
          background-color: @layout-bg-color;
          border-radius: 20px;
          display: inline-block;
          font-size: 12px;
          font-weight: 600;
          margin-right: 24px;
          height: 20px;
          line-height: 20px;
          width: 20px;
          text-align: center;
        }
        span.active {
          background-color: #314659 !important;
          color: @text-color-inverse !important;
        }
        span:last-child {
          float: right;
        }
      }
    }
  }
</style>
