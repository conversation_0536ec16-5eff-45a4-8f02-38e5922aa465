<!--品质管理- 切片报告 -->
<template>
  <div>
    <div class="projectBackend">
      <div class="topcontent">
        <a-input style="width: 170px; margin-right: 0.5%" placeholder="生产编号" v-model="formData.PinBanNo" allowClear></a-input>
        <a-button type="primary" style="margin-right: 0.5%" @click="searchClick">调出数据</a-button>
        <a-button type="primary" style="margin-right: 0.5%" @click="exportReport">导出报告</a-button>
        <a-button type="primary" v-if="!edit" style="margin-right: 0.5%" @click="editdata">编辑</a-button>
        <a-button type="primary" v-if="edit" style="margin-right: 0.5%" @click="cancel">取消</a-button>
        <a-button type="primary" style="margin-right: 0.5%" @click="savedata">保存</a-button>
      </div>
      <div class="maincontent">
        <div class="Leftlist">
          <a-table :columns="leftcolumns" :pagination="false" :dataSource="LeftListData" class="lefttable" rowKey="id" :scroll="{ y: 753, x: 600 }">
            <span slot="actualCu_" slot-scope="text, record">
              <a-input v-if="edit" v-model="record.actualCu_" allowClear />
              <span v-else>{{ record.actualCu_ }}</span>
            </span>
            <span slot="mediumLayer_" slot-scope="text, record">
              <a-input v-if="edit" v-model="record.mediumLayer_" allowClear />
              <span v-else>{{ record.mediumLayer_ }}</span>
            </span>
            <span slot="mClaimCu_" slot-scope="text, record">
              <a-input v-if="edit" v-model="record.mClaimCu_" allowClear />
              <span v-else>{{ record.mClaimCu_ }}</span>
            </span>
            <span slot="mActualCu_" slot-scope="text, record">
              <a-input v-if="edit" v-model="record.mActualCu_" allowClear />
              <span v-else>{{ record.mActualCu_ }}</span>
            </span>
          </a-table>
        </div>
        <div class="Rightcontent">
          <div class="toplist">
            <a-table :columns="topcolumns" :pagination="false" class="toptable" :dataSource="topListData" rowKey="id" :scroll="{ y: 355, x: 600 }">
              <span slot="valueA_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.valueA_" allowClear />
                <span v-else>{{ record.valueA_ }}</span>
              </span>
              <span slot="valueB_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.valueB_" allowClear />
                <span v-else>{{ record.valueB_ }}</span>
              </span>
              <span slot="valueC_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.valueC_" allowClear />
                <span v-else>{{ record.valueC_ }}</span>
              </span>
              <span slot="valueD_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.valueD_" allowClear />
                <span v-else>{{ record.valueD_ }}</span>
              </span>
              <span slot="valueF_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.valueF_" allowClear />
                <span v-else>{{ record.valueF_ }}</span>
              </span>
              <span slot="valueE_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.valueE_" allowClear />
                <span v-else>{{ record.valueE_ }}</span>
              </span>
              <span slot="minValue_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.minValue_" allowClear />
                <span v-else>{{ record.minValue_ }}</span>
              </span>
              <span slot="maxValue_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.maxValue_" allowClear />
                <span v-else>{{ record.maxValue_ }}</span>
              </span>
              <span slot="averageValue_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.averageValue_" allowClear />
                <span v-else>{{ record.averageValue_ }}</span>
              </span>
              <span slot="roughness_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.roughness_" allowClear />
                <span v-else>{{ record.roughness_ }}</span>
              </span>
            </a-table>
          </div>
          <div class="centerlist">
            <a-table
              class="centertable"
              :columns="centercolumns"
              :pagination="false"
              :dataSource="centerListData"
              rowKey="id"
              :scroll="{ y: 355, x: 600 }"
            >
              <span slot="dataA_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.dataA_" allowClear />
                <span v-else>{{ record.dataA_ }}</span>
              </span>
              <span slot="dataB_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.dataB_" allowClear />
                <span v-else>{{ record.dataB_ }}</span>
              </span>
              <span slot="dataC_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.dataC_" allowClear />
                <span v-else>{{ record.dataC_ }}</span>
              </span>
              <span slot="surfaceH_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.surfaceH_" allowClear />
                <span v-else>{{ record.surfaceH_ }}</span>
              </span>
              <span slot="nickelN_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.nickelN_" allowClear />
                <span v-else>{{ record.nickelN_ }}</span>
              </span>
              <span slot="surfaceC_" slot-scope="text, record">
                <a-input v-if="edit" v-model="record.surfaceC_" allowClear />
                <span v-else>{{ record.surfaceC_ }}</span>
              </span>
            </a-table>
          </div>
          <div class="bottomlist">
            <div style="padding: 20px" v-if="edit">
              <a-upload
                accept=".jpg,.png,.jpeg,"
                name="file"
                @preview="handlePreview"
                list-type="picture-card"
                ref="fileRef"
                :before-upload="beforeUpload1"
                :customRequest="httpRequest1"
                :file-list="fileListData"
                @change="handleChange1"
              >
                <a-button style="font-weight: 500; height: 40px"> 上传切片图片 </a-button>
              </a-upload>
            </div>
            <div v-else style="display: flex; padding: 20px">
              <div style="padding-left: 15px; font-weight: bold; font-size: 16px">切片上传图片：</div>
              <div v-if="this.sliceImg_">
                <span v-for="(item, index) in this.sliceImg_.split(';')" :key="index">
                  <img :src="item" style="max-height: 220px; padding-left: 15px" v-viewer />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { upLoadFlyingFile } from "@/services/projectMake";
import { mapState } from "vuex";
import { slicereportlist, setslicereportlist, reliabilitytestreport } from "@/services/scgl/QualityManagement/SliceReport";
const leftcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "铜厚层",
    align: "left",
    width: 80,
    ellipsis: true,
    dataIndex: "lnLayer_",
  },
  {
    title: "要求",
    dataIndex: "claimCu_",
    align: "left",
    width: 60,
    ellipsis: true,
  },
  {
    title: "实际",
    dataIndex: "actualCu_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "actualCu_" },
  },
  {
    title: "介质层",
    dataIndex: "mediumLayer_",
    align: "left",
    width: 80,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "mediumLayer_" },
  },
  {
    title: "介质要求",
    dataIndex: "mClaimCu_",
    align: "left",
    width: 70,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "mClaimCu_" },
  },
  {
    title: "介质实际",
    dataIndex: "mActualCu_",
    align: "left",
    width: 70,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "mActualCu_" },
  },
];
const topcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "A",
    align: "left",
    width: 60,
    ellipsis: true,
    dataIndex: "valueA_",
    className: "editSty",
    scopedSlots: { customRender: "valueA_" },
  },
  {
    title: "B",
    dataIndex: "valueB_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "valueB_" },
  },
  {
    title: "C",
    dataIndex: "valueC_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "valueC_" },
  },
  {
    title: "D",
    dataIndex: "valueD_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "valueD_" },
  },
  {
    title: "E",
    dataIndex: "valueE_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "valueE_" },
  },
  {
    title: "F",
    dataIndex: "valueF_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "valueF_" },
  },
  {
    title: "最小值",
    dataIndex: "minValue_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "minValue_" },
  },
  {
    title: "最大值",
    dataIndex: "maxValue_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "maxValue_" },
  },
  {
    title: "平均值",
    dataIndex: "averageValue_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "averageValue_" },
  },
  {
    title: "粗糙值",
    dataIndex: "roughness_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "roughness_" },
  },
];
const centercolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
  },
  {
    title: "阻焊厚度A",
    align: "left",
    width: 120,
    ellipsis: true,
    dataIndex: "dataA_",
    className: "editSty",
    scopedSlots: { customRender: "dataA_" },
  },
  {
    title: "阻焊厚度B",
    dataIndex: "dataB_",
    align: "left",
    width: 120,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "dataB_" },
  },
  {
    title: "阻焊厚度C",
    dataIndex: "dataC_",
    align: "left",
    width: 120,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "dataC_" },
  },
  {
    title: "锡",
    dataIndex: "surfaceH_",
    align: "left",
    width: 120,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "surfaceH_" },
  },
  {
    title: "镍",
    dataIndex: "nickelN_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "nickelN_" },
  },
  {
    title: "金",
    dataIndex: "surfaceC_",
    align: "left",
    width: 60,
    ellipsis: true,
    className: "editSty",
    scopedSlots: { customRender: "surfaceC_" },
  },
];
export default {
  name: "",
  data() {
    return {
      leftcolumns,
      topcolumns,
      centercolumns,
      fileListData: [],
      form: {},
      sliceImg_: "",
      LeftListData: [],
      topListData: [],
      centerListData: [],
      edit: false,
      formData: {
        PinBanNo: "",
      },
    };
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  mounted() {
    this.handleResize();
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.indexOf(".jpg") != -1 || file.name.indexOf(".png") != -1 || file.name.indexOf(".jpeg") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("只能上传jpg/png/jpeg文件");
          reject();
        } else {
          resolve();
        }
      });
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handlePreview(file) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
    },
    handleChange1({ fileList }, data) {
      this.fileListData = fileList;
      if (this.fileListData.length > 2) {
        this.$message.warning("该操作最多上传两张张图片！默认按照最新上传图片为准");
        this.fileListData.splice(0, 1);
      }
      this.sliceImg_ = "";
      let arr = [];
      if (this.fileListData.length) {
        this.fileListData.forEach(item => {
          arr.push(item.response);
        });
        this.sliceImg_ = arr.join(";");
      } else {
        this.sliceImg_ = "";
      }
    },
    handleResize() {
      let lefttable = document.getElementsByClassName("lefttable")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      let lefttable1 = document.getElementsByClassName("lefttable")[0];
      let centertable = document.getElementsByClassName("centertable")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      let toptable = document.getElementsByClassName("toptable")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      let bottomtable = document.getElementsByClassName("bottomlist")[0];
      if (this.LeftListData.length != 0) {
        lefttable.style.height = window.innerHeight - 170 + "px";
        lefttable1.style.height = window.innerHeight - 130 + "px";
      } else {
        lefttable1.style.height = window.innerHeight - 130 + "px";
        lefttable.style.height = "0px";
      }
      if (this.topListData.length != 0) {
        toptable.style.height = window.innerHeight / 3 - 85 + "px";
      }
      if (this.centerListData.length != 0) {
        centertable.style.height = window.innerHeight / 3 - 85 + "px";
      }
    },
    editdata() {
      if (this.LeftListData.length == 0) {
        this.$message.warning("该状态不允许进行编辑");
        return;
      }
      this.edit = true;
    },
    //数据保存
    savedata() {
      if (!this.edit) {
        this.$message.warning("非编辑状态不可执行此操作！");
        return;
      }
      for (let index = 0; index < this.topListData.length; index++) {
        const element = this.topListData[index];
        if (element.valueA_ == "") {
          element.valueA_ = null;
        }
        if (element.valueB_ == "") {
          element.valueB_ = null;
        }
        if (element.valueC_ == "") {
          element.valueC_ = null;
        }
        if (element.valueD_ == "") {
          element.valueD_ = null;
        }
        if (element.valueE_ == "") {
          element.valueE_ = null;
        }
        if (element.valueF_ == "") {
          element.valueF_ = null;
        }
        if (element.minValue_ == "") {
          element.minValue_ = null;
        }
        if (element.maxValue_ == "") {
          element.maxValue_ = null;
        }
        if (element.averageValue_ == "") {
          element.averageValue_ = null;
        }
        if (element.roughness_ == "") {
          element.roughness_ = null;
        }
      }
      this.form.sliceImg_ = this.sliceImg_;
      let params = {
        mainDto: this.form,
        reportADtos: this.LeftListData,
        reportBDtos: this.topListData,
        reportCDtos: this.centerListData,
      };

      setslicereportlist(params).then(res => {
        if (res.code) {
          this.$message.success("保存成功！");
        } else {
          this.$message.error(res.message);
        }
      });
      this.edit = false;
    },
    cancel() {
      this.edit = false;
      this.getOrderList(this.formData.PinBanNo);
    },
    //调出数据
    searchClick() {
      if (!this.formData.PinBanNo) {
        this.$message.warning("请输入编号！");
        return;
      }
      var arr1 = this.formData.PinBanNo.split("");
      if (arr1.length > 30) {
        arr1 = arr1.slice(0, 30);
      }
      this.formData.PinBanNo = arr1.join("");
      this.getOrderList(this.formData.PinBanNo);
    },
    //获取数据
    getOrderList(queryData) {
      this.spinning = true;
      queryData = queryData.trim();
      slicereportlist(queryData)
        .then(res => {
          if (res.code) {
            this.LeftListData = res.data.reportADtos;
            this.topListData = res.data.reportBDtos;
            this.centerListData = res.data.reportCDtos;
            this.form = res.data.mainDto;
            this.sliceImg_ = this.form.sliceImg_;
            if (this.form.sliceImg_) {
              let imgList = this.form.sliceImg_.split("|");
              this.fileListData = [];
              imgList.forEach((item, index) => {
                this.fileListData.push({
                  uid: index + 1,
                  name: "image.png",
                  status: "done",
                  url: item,
                  thumbUrl: item, //缩略图地址
                  response: item,
                });
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.handleResize();
        });
    },
    //导出报告
    exportReport() {
      if (this.LeftListData.length == 0 && this.topListData.length == 0 && this.centerListData.length == 0) {
        this.$message.warning("请先获取数据！");
        return;
      }
      reliabilitytestreport(this.form.pdctno_).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-empty-normal {
  margin: 0;
  color: rgba(0, 0, 0, 0.25);
}
/deep/ .editSty {
  padding: 0 2px !important;
  .ant-input-affix-wrapper .ant-input:not(:last-child) {
    padding: 2px 5px;
    height: 28px;
  }
}
.projectBackend {
  background: #ffffff;
  .topcontent {
    height: 44px;
    margin-left: 6px;
    padding-top: 5px;
  }
  .maincontent {
    width: 100%;
    display: flex;
    /deep/.ant-table-thead > tr > th {
      padding: 7px 2px;
      overflow-wrap: break-word;
      border-right: 1px solid rgb(233, 233, 240);
    }
    /deep/.ant-table-tbody > tr > td {
      padding: 7px 2px;
      overflow-wrap: break-word;
      border-right: 1px solid rgb(233, 233, 240);
    }
    /deep/.ant-table .ant-table-tbody > tr > td {
      height: 36px;
    }
    /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
    /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
      background: #f8f8f8;
    }
    .Leftlist {
      width: 40%;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
    }
    .Rightcontent {
      width: 60%;
      .toplist {
        width: 100%;
        height: 33%;
        border: 2px solid rgb(233, 233, 240);
      }
      .centerlist {
        width: 100%;
        height: 33%;
        border: 2px solid rgb(233, 233, 240);
        border-top: 0px solid rgb(233, 233, 240);
      }
      .bottomlist {
        width: 100%;
        height: 34%;
        border: 2px solid rgb(233, 233, 240);
        border-top: 0px solid rgb(233, 233, 240);
        border-bottom: 4px solid rgb(233, 233, 240);
      }
    }
  }
}
</style>
