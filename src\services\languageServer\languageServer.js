import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
// const BASE_URL = process.env.VUE_APP_API_MAIN_URL
export async function getLang(params) {
    return request("/api/language-management/languages", METHOD.GET, transformAbpListQuery(params))
}

export async function createLang(params) {
    if (params.id) {
        return request(`/api/language-management/languages/${params.id}`, METHOD.PUT, params)
    }
    return request("/api/language-management/languages", METHOD.POST, params)
}

export async function delLang(id) {
    return request(`/api/language-management/languages/${id}`, METHOD.DELETE)
}

export async function setLang(id) {
    return request(`/api/language-management/languages/${id}/set-as-default`, METHOD.PUT)
}



export default {
    getLang,
    createLang,
    delLang,
    setLang
}