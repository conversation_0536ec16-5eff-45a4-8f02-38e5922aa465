import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
// 列表操作
export async function actionList(params) {
    //  PUT 修改 POST 新增
    return request("/api/app/e-mSTSys-user-enterprise-info", METHOD[params.type], transformAbpListQuery(params.obj))
}
// 查询
export async function find(params) {
    // 查询 GET  删除 DELETE
    return request(`/api/app/e-mSTSys-user-enterprise-info/${params.id}/by-id`, METHOD[params.type])
}
// 获取列表
export async function getList(params) {
    return request('/api/app/e-mSTSys-user-enterprise-info/page-list', METHOD.GET, params)
}

// 上传图片
export async function imgUpload(params) {
    return request("/api/file-management/files/camuploadpfile", METHOD.POST, params)
}
export default {
    getList,
    find,
    actionList,
    imgUpload
}
