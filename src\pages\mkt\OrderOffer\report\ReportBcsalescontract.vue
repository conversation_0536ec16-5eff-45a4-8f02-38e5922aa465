<!-- 市场管理 - 订单报价- 本川销售合同 -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj" type="primary" class="printstyle" @click="printpdf">打印</a-button>
    <div id="pdfDombc" style="color: black; font-weight: 600">
      <div style="width: 100%; font-size: 30px; font-weight: bold; padding-bottom: 10px">
        <div style="display: flex; text-align: center; position: relative">
          <img src="@/assets/img/bctitle.png" style="position: relative; right: 15px; height: 80px" />
          <div style="position: absolute; right: 30%">
            <div>江苏本川智能电路科技股份有限公司</div>
            <div>产品加工合同</div>
          </div>
        </div>
      </div>
      <div style="display: flex">
        <div style="width: 50%; z-index: 99">
          <div>卖方(承揽方):{{ BCsalesdata.value_1 }}</div>
          <div>地址:{{ BCsalesdata.value_2 }}</div>
          <div>
            <span style="width: 200px; display: inline-block">电话:{{ BCsalesdata.value_3 }} </span>
            <span style="width: 200px; display: inline-block">传真:{{ BCsalesdata.value_4 }} </span>
            <span style="width: 150px; display: inline-block">联系人:{{ BCsalesdata.value_5 }}</span>
          </div>
          <div>税号:{{ BCsalesdata.value_6 }}</div>
          <div>
            <span style="width: 200px; display: inline-block">开户行:{{ BCsalesdata.value_7 }} </span>
            <span>账号:{{ BCsalesdata.value_8 }}</span>
          </div>
          <div>一、产品名称、规格、数量、单价(人民币)及交货期</div>
        </div>
        <div style="z-index: 99; width: 50%" class="RightTable">
          <table border="1" style="float: right">
            <tr>
              <td>客户名称</td>
              <td>{{ BCsalesdata.value_9 }}</td>
            </tr>
            <tr>
              <td>客户简称</td>
              <td>{{ BCsalesdata.value_10 }}</td>
            </tr>
            <tr>
              <td>报价编号</td>
              <td>{{ BCsalesdata.value_11 }}</td>
            </tr>
            <tr>
              <td>报价日期</td>
              <td>{{ BCsalesdata.value_12 }}</td>
            </tr>
            <tr>
              <td>签订地点</td>
              <td>{{ BCsalesdata.value_13 }}</td>
            </tr>
          </table>
        </div>
      </div>
      <!-- <div @click="addcontract" style="color: #4b82ac; cursor: pointer; float: right" v-if="showadd && act != 'dis'">
        点击添加 <a-icon style="font-size: 16px; top: 2px; position: relative" type="plus-circle"></a-icon>
      </div> -->
      <div>
        <table border="1" style="width: 100%">
          <thead>
            <tr>
              <th>序号</th>
              <th>客户型号</th>
              <th>客户料号</th>
              <th>版本号</th>
              <th>板厚(mm)</th>
              <th colspan="3">出货尺寸(MM)</th>
              <th>拼版数量</th>
              <th>层数</th>
              <th>报价数量</th>
              <th>出货单位</th>
              <th>面积平米数</th>
              <th>单价(未税)</th>
              <th>工程费(未税)</th>
              <th>测试费用(未税)</th>
              <th>加急费(未税)</th>
              <th>模具费(未税)</th>
              <th>返单MOV(未税)</th>
              <th>总价(未税)</th>
              <th>交期(EQ确认后)</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in BCsalesdata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.price2 }}</td>
              <td>{{ item.price3 }}</td>
              <td>{{ item.price4 }}</td>
              <td>{{ item.price5 }}</td>
              <td>{{ item.price6 }}</td>
              <td>&nbsp;*&nbsp;</td>
              <td>{{ item.price7 }}</td>
              <td>{{ item.price8 }}</td>
              <td>{{ item.price9 }}</td>
              <td>{{ item.price10 }}</td>
              <td>{{ item.price11 }}</td>
              <td>{{ item.price12 }}</td>
              <td>{{ item.price13 }}</td>
              <td>{{ item.price14 }}</td>
              <td>{{ item.price15 }}</td>
              <td>{{ item.price16 }}</td>
              <td>{{ item.price17 }}</td>
              <td>{{ item.price18 }}</td>
              <td>{{ item.price19 }}</td>
              <td>{{ item.price20 }}</td>
            </tr>
            <tr>
              <td colspan="4" style="text-align: center">费用合计(含税)</td>
              <td style="text-align: center">￥</td>
              <td colspan="4" style="text-align: center">{{ amountto }}</td>
              <td colspan="4" style="text-align: right">人民币大写：</td>
              <td colspan="4">{{ convertToChineseNum(amountto) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div>
        二、产品制作要求
        <table border="1" style="width: 100%">
          <thead>
            <tr>
              <th>序</th>
              <th>外形</th>
              <th>测试</th>
              <th>类型</th>
              <th>板材</th>
              <th>工艺要求</th>
              <th>铜厚</th>
              <th>过孔</th>
              <th>阻焊颜色</th>
              <th>字符颜色</th>
              <th>标记</th>
              <th>报价有效期</th>
              <th>报价备注</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in BCsalesdata.orderPar" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ item.formingType }}</td>
              <td>{{ item.flyingProbe }}</td>
              <td>{{ item.reOrder }}</td>
              <td>{{ item.fR4Type }}</td>
              <td>{{ item.sufaceFinish }}</td>
              <td>{{ item.cuThickness }}</td>
              <td>{{ item.functionary_ }}</td>
              <td>{{ item.solderColor }}</td>
              <td>{{ item.fontColor }}</td>
              <td>{{ item.version }}</td>
              <td>{{ item.size }}</td>
              <td>{{ item.remark_ }}</td>
            </tr>
            <tr>
              <td colspan="2" style="text-align: center">备注：</td>
              <td colspan="11">{{ BCsalesdata.value_14 }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div>
        <a-row>
          <a-col :span="8">
            <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'三、交货地点：'" labelAlign="left">
              {{ BCsalesdata.value_15 }}
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货方式：'" labelAlign="left">
              {{ BCsalesdata.value_19 }}
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'四、运费及运保费由：'" labelAlign="left">
              {{ BCsalesdata.value_16 }}
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'包装方式：'" labelAlign="left">
              {{ BCsalesdata.value_20 }}
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'五、验收标准：'" labelAlign="left">
              {{ BCsalesdata.value_17 }}
            </a-form-model-item>
          </a-col>
          <a-col :span="16">
            <a-form-model-item :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }" :label="'验收期限：'" labelAlign="left">
              请在<span
                style="color: red; font-weight: bold; min-width: 30px; border-bottom: 1px solid #000; text-align: center; display: inline-block"
                >{{ BCsalesdata.value_21 }}</span
              >天内以书面形式通知卖方，否则视为接受。
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'六、票据形式：'" labelAlign="left">
              {{ BCsalesdata.value_18 }}
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'结算方式：'" labelAlign="left">
              {{ BCsalesdata.value_23 }}
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'付款方式：'" labelAlign="left">
              {{ BCsalesdata.value_22 }}
            </a-form-model-item>
          </a-col>
        </a-row>
      </div>
      <div style="line-height: 3ch">
        <div>七、交货时间的天数以卖方收到合同回签后开始计算；</div>
        <div>
          八、在卖方投料后买方提出更改设计资料或停止加工，应承担卖方因此受到的所有损失。在不造成报废的前提下，卖方应尽量满足买方的更改要求，并相应顺延货期；
        </div>
        <div>九、保密要求：卖方对买方提供之资料负有保密义务，长期合作关系的客户可按照双方签订的保密协议执行；</div>
        <div>十、遇不可抗拒力因素造成卖方未能按期交货，卖方应及时通知买方，否则责任由卖方承担；</div>
        <div>十一、因卖方产品质量原因造成了买方的产品报废，按照行业惯例，卖方的赔偿以不超过电路板自身价值为最高额度；</div>
        <div>十二、在履行合同过程中，若发生争议，由双方友好协商解决，协商未果，以法律手段在卖方所在地法院诉讼；</div>
        <div class="indented">
          十三、买方的知识产权（包括但不限于著作权、专利权、专利申请权、商标权、专有技术、商业机密、包括但不限于发明、发现、设计、计划、计算机程序、硬件设计、图表和制程等）卖方不得抄袭或在原基础上修改向用于自己的研发、生产或给第三方，如果卖方违反上诉规定或侵犯买方的知识产权，卖方应当赔偿买方的损失。
        </div>
        <div>十四、双方业务中所发传真、邮件等同原件具有同等法律效力，其它相关协议、手册等文件、资料和本合同有冲突的，以本合同为准；</div>
        <div>十五、特殊说明：到货日期不包含货运时间。</div>
        <div>十六、备注：</div>
        <div style="padding-top: 20px; display: flex; justify-content: space-between">
          <div>
            卖方：&emsp;&emsp;<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">{{
              BCsalesdata.value_24
            }}</span>
          </div>
          <div>
            买方：&emsp;&emsp;<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">&emsp; </span
            >（盖章）
          </div>
        </div>
        <div style="padding-top: 10px; display: flex; justify-content: space-between">
          <div>
            代表签字：<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">{{
              BCsalesdata.value_25
            }}</span>
          </div>
          <div>
            代表签字：<span style="font-weight: bold; min-width: 400px; border-bottom: 1px solid #000; display: inline-block">&emsp; </span
            >&emsp;&emsp;&emsp;&emsp;
          </div>
        </div>
      </div>
    </div>
    <a-modal title="合同数据添加" :visible="modalvisible" @ok="handleOk" @cancel="handleCancel" centered :width="800">
      <a-row>
        <a-col :span="10">
          <a-form-item label="订单号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入订单号进行查询" v-model="OrderNo" @keyup.enter="queryclick" :auto-focus="true" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="客户型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入客户型号进行查询" v-model="PcbFileName" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" style="margin-top: 2px" @click="queryclick">查询</a-button>
        </a-col>
      </a-row>
      <a-table
        :columns="columns"
        :row-selection="{ selectedRowKeys: selectedRowKeysind, onChange: onSelectChange, columnWidth: 25 }"
        :pagination="false"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ y: 300 }"
        :dataSource="datasource"
        :rowClassName="isRedRow"
      >
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import convertToChineseNum from "@/utils/convertToChineseNum";
import { verifyPageList, deletecontractno, contractNo } from "@/services/mkt/OrderReview.js";
import htmlToPdf from "@/utils/htmlToPdfa3";
export default {
  name: "ReportInfoyxd",
  props: ["BCsalesdata", "joinFactoryId", "salescustno", "ContractNoSech", "ttype", "act"],
  computed: {},
  data() {
    return {
      printObj: {
        id: "pdfDombc", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      selectedRowKeys: [],
      showadd: true,
      selectedRowKeysind: [],
      PcbFileName: "",
      OrderNo: "",
      datasource: [],
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 35,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "订单号",
          align: "left",
          ellipsis: true,
          width: 80,
          dataIndex: "orderNo",
        },
        {
          title: "客户代码",
          dataIndex: "custNo",
          align: "left",
          ellipsis: true,
          width: 50,
        },

        {
          title: "订单类型",
          dataIndex: "reOrder",
          align: "left",
          ellipsis: true,
          width: 50,
          customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
        },
      ],
      amountto: 0,
      modalvisible: false,
      ids: [],
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.amountto = 0;
    this.ids = [];
    this.BCsalesdata.price.forEach(item => {
      if (this.ids.indexOf(item.id) == -1) {
        this.ids.push(item.id);
      }
    });
    for (let index = 0; index < this.BCsalesdata.price.length; index++) {
      if (this.BCsalesdata.price[index].price19 && this.BCsalesdata.price[index].price19 != "/") {
        this.amountto += Number(this.BCsalesdata.price[index].price19);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
    this.showadd = !this.BCsalesdata.price.some(ite => ite.status == 30);
  },
  methods: {
    convertToChineseNum,
    getsalesPdf() {
      this.showadd = false;
      setTimeout(() => {
        htmlToPdf("pdfDombc", this.BCsalesdata.pcbFileName);
        this.showadd = !this.BCsalesdata.price.some(ite => ite.status == 30);
      }, 500);
    },
    closePrintTool() {
      document.title = this.ttype;
      this.showadd = !this.BCsalesdata.price.some(ite => ite.status == 30);
    },
    printpdf() {
      document.title = this.BCsalesdata.pcbFileName;
      this.showadd = false;
    },
    delclick(id) {
      this.ids = [];
      this.BCsalesdata.price.forEach(item => {
        if (this.ids.indexOf(item.id) == -1) {
          this.ids.push(item.id);
        }
      });
      deletecontractno(id).then(res => {
        if (res.code) {
          this.ids.forEach(item => {
            if (item == id) {
              this.ids.splice(this.ids.indexOf(item), 1);
            }
          });
          this.$message.success("删除成功");
          this.$emit("LTsalescontract", this.ids, "BC");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeys = rowKeys;
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysind = selectedRowKeys;
      this.selectedRowKeys = [];
      selectedRows.forEach(item => {
        this.selectedRowKeys.push(item.id);
      });
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    queryclick() {
      if (!this.OrderNo && !this.PcbFileName) {
        this.$message.error("请输入订单号或客户型号进行查询");
        return;
      }
      let params = {
        PageIndex: 1,
        PageSize: 20,
        CustNo: this.salescustno,
        ContractNoSech: this.ContractNoSech,
      };
      if (this.OrderNo) {
        params.OrderNo = this.OrderNo;
      }
      if (this.PcbFileName) {
        params.PcbFileName = this.PcbFileName;
      }
      verifyPageList(params).then(res => {
        if (res.code) {
          this.datasource = res.data.items;
          if (this.datasource.length == 0) {
            this.$message.error("未查询到相关订单");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    addcontract() {
      console.log("11111");
      this.modalvisible = true;
      this.selectedRowKeys = [];
      this.datasource = [];
      this.OrderNo = "";
      this.PcbFileName = "";
      this.selectedRowKeysind = [];
    },
    handleOk() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择订单");
        return;
      }
      this.ids = [...this.ids, ...this.selectedRowKeys];
      contractNo(this.ids).then(res => {
        if (res.code) {
          this.$emit("LTsalescontract", this.ids, "BC");
        } else {
          this.$message.error(res.message);
        }
      });
      this.modalvisible = false;
    },
    handleCancel() {
      this.modalvisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.indented {
  margin-left: 3em; /* 首行缩进 */
  padding-left: 0; /* 避免额外的内边距 */
  text-indent: -3em; /* 取消首行的缩进 */
}

.indented p {
  text-indent: 3em; /* 除了第一行之外，其他行首行缩进 */
}
@media print {
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
}
/deep/.ant-form-item-children {
  color: black;
}
/deep/.ant-form-item-label {
  height: 25px;
  line-height: 25px;
}
/deep/.ant-form-item-control {
  height: 25px;
  line-height: 25px;
}
.RightTable td {
  padding: 0px 15px;
}
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.agreement {
  padding-bottom: 20px;
  position: relative;
  z-index: 99;
  div {
    padding-top: 10px;
  }
}
.tableclass {
  border: 1px solid black;
  margin-top: 10px;
  margin-bottom: 10px;
  td {
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding-left: 7px;
  }
  tbody {
    tr {
      height: 24px;
    }
  }
}
.rowBackgroundColor {
  background: #dcdcdc !important;
}
.ant-table-row-selected {
  background: #dcdcdc !important;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-table-tbody > tr.ant-table-row-selected td {
  background: #dcdcdc;
}
/deep/.ant-table {
  border: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-thead > tr > th {
  padding: 4px 4px;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 4px 4px !important;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
  color: #000000;
}
.pdfDom1 {
  padding: 25px;
  height: 650px;
  overflow: auto;
}
</style>
