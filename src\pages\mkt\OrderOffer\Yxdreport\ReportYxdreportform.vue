<!--YXD报价单  -->
<template>
  <div class="pdfDom1" style="font-size: 14px">
    <a-button v-print="printObj" @click="printpdf" class="printstyle" type="primary">打印</a-button>
    <div id="yxdreport1" style="padding: 25px; font-family: '宋体'">
      <div style="width: 100%; display: flex; text-align: center">
        <img src="@/assets/img/yxdlogo.png" style="height: 80px" />
        <div style="width: 100%; position: relative; left: -64px">
          <span style="font-size: 30px; font-weight: bold">{{ YXDreportdata.factory_ }}</span
          ><br />
          <span style="font-size: 30px; font-weight: bold; letter-spacing: 5px">报价单</span>
        </div>
      </div>
      <div style="float: right; padding-right: 60px">日期:{{ YXDreportdata.factorydete_ }}</div>
      <div>
        <table border="1" style="text-align: center; margin-top: 5px; width: 100%">
          <thead>
            <tr>
              <td rowspan="2" style="width: 50px">No</td>
              <td rowspan="2">品名</td>
              <td rowspan="2">客户物料号</td>
              <td colspan="4">尺寸规格</td>
              <td rowspan="2" colspan="2">板材信息</td>
              <td rowspan="2" colspan="2">完成铜厚</td>
              <td rowspan="2">层数</td>
              <td rowspan="2" colspan="2">油墨颜色</td>
              <td rowspan="2">表面处理<br />漏铜面积</td>
              <td rowspan="2" v-if="salescustno == '1320' || salescustno == '1325'">平米价</td>
              <td rowspan="2" v-if="salescustno == '1610'">不含税单价PCS</td>
              <td rowspan="2" v-if="salescustno != '1610'">单价PCS</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.eng && item.eng != '/' && item.eng != 0)">工程费<br />(RMB)</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.urgent && item.urgent != '/' && item.urgent != 0)">加急费<br />(RMB)</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.test && item.test != '/' && item.test != 0)">测试架<br />(RMB)</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.fly && item.fly != '/' && item.fly != 0)">飞测费<br />(RMB)</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.price1 && item.price1 != '/' && item.price1 != 0)">包干价</td>
              <td rowspan="2">订单数量</td>
              <td rowspan="2">合计</td>
              <td rowspan="2">交期</td>
              <td rowspan="2">备注</td>
            </tr>
            <tr>
              <td>项目</td>
              <td>长mm</td>
              <td>宽mm</td>
              <td>PCS</td>
            </tr>
          </thead>
          <tbody v-for="(item, index) in YXDreportdata.price" :key="index">
            <tr>
              <td rowspan="2">{{ item.no }}</td>
              <td rowspan="2">{{ item.name }}</td>
              <td rowspan="2">{{ item.price3 }}</td>
              <td>单支</td>
              <td>{{ item.boardHeight }}</td>
              <td>{{ item.boardWidth }}</td>
              <td>1</td>
              <td colspan="2">{{ item.boardBrand }}</td>
              <td>内</td>
              <td>{{ item.incu }}</td>
              <td rowspan="2">{{ item.lay }}</td>
              <td>阻焊</td>
              <td>{{ item.mask }}</td>
              <td rowspan="2">{{ item.surf }}</td>
              <td rowspan="2" v-if="salescustno == '1320' || salescustno == '1325'">{{ item.price4 }}</td>
              <td rowspan="2">{{ item.pcs }}</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.eng && item.eng != '/' && item.eng != 0)">{{ item.eng }}</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.urgent && item.urgent != '/' && item.urgent != 0)">{{ item.urgent }}</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.test && item.test != '/' && item.test != 0)">{{ item.test }}</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.fly && item.fly != '/' && item.fly != 0)">{{ item.fly }}</td>
              <td rowspan="2" v-if="YXDreportdata.price.some(item => item.price1 && item.price1 != '/' && item.price1 != 0)">{{ item.price1 }}</td>
              <td rowspan="2">{{ item.qty }}</td>
              <td rowspan="2">{{ item.price2 }}</td>
              <td rowspan="2">{{ item.custdate }}</td>
              <td rowspan="2" style="word-break: break-all; max-width: 200px">{{ item.notes }}</td>
            </tr>
            <tr>
              <td>拼版</td>
              <td>{{ item.setBoardHeight }}</td>
              <td>{{ item.setBoardWidth }}</td>
              <td>{{ item.su }}</td>
              <td>板厚</td>
              <td>{{ item.boardT }}</td>
              <td>外</td>
              <td>{{ item.oucu }}</td>
              <td>文字</td>
              <td>{{ item.char_ }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="width: 100%">备注:{{ YXDreportdata.specialrequirements }}</div>
      <div style="position: relative; margin-top: 25px; height: 130px">
        <div style="line-height: 3ch; margin-top: 8px; width: 100%; z-index: 99; position: relative">
          <div>一、以上报价含税</div>
          <div>二、如原材料价格浮动太大,根据原材料价格上调下跌,双方协商再议</div>
          <div style="display: flex; width: 100%">
            <div style="width: 70%">供货方:{{ YXDreportdata.factory_ }}</div>
            <div style="width: 30%">订货方:{{ YXDreportdata.party_ }}</div>
          </div>
          <div>电话:{{ YXDreportdata.value_1 }}</div>
          <div>工厂地址:{{ YXDreportdata.value_2 }}</div>
        </div>
        <img
          v-if="YXDreportdata.factory_.indexOf('惠州') != -1"
          src="@/assets/img/hzyxd.png"
          style="position: absolute; top: -27px; z-index: 0; display: block; left: 90px; width: 150px"
        />
        <img
          v-if="YXDreportdata.factory_.indexOf('江西') != -1"
          src="@/assets/img/jxyxd.png"
          style="position: absolute; top: -27px; z-index: 0; display: block; left: 90px; width: 150px"
        />
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
export default {
  name: "",
  props: ["YXDreportdata", "ttype", "salescustno"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj: {
        id: "yxdreport1", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.amountto = 0;
    // for (let index = 0; index < this.YXDreportdata.price.length; index++) {
    //     if(this.YXDreportdata.price[index].total && this.YXDreportdata.price[index].total!='/'){
    //         this.amountto +=Number(this.YXDreportdata.price[index].total)
    //     }
    // }
  },
  methods: {
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.YXDreportdata.pcbFileName;
    },
    getreportPdf() {
      htmlToPdfa3("yxdreport1", this.YXDreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
