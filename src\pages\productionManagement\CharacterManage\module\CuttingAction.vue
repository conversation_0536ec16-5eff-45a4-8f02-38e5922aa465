<!-- 车间管理-字符管理-按钮 -->
<template>
  <div class="active" ref="active" :style="[{ width: width + 'px' }]">
    <div class="box" v-if="checkPermission('MES.ProductionModule.Character.CharacterSendMachine')">
      <a-button type="primary" @click="handleDispatchMachine" :loading="btnloading1"> 分派 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ProductionModule.Character.CharacterPassStep')">
      <a-button type="primary" @click="OverOrderClick"> 部门过序 </a-button>
    </div>
    <!--       <div class="box" v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleOrderUpLoad')">-->
    <!--         <a-button type="primary" @click='UploadOrderClick'>-->
    <!--           上传订单-->
    <!--         </a-button>-->
    <!--       </div>-->
    <!--       <div class="box" v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleRyRegister')">-->
    <!--           <a-button type="primary" @click='PersonnelRegistrationClick' >-->
    <!--             人员登记-->
    <!--           </a-button>-->
    <!--         </div>       -->
    <!--       <div class="box" v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleIsUrgent')">-->
    <!--          <a-button type="primary" @click='SetUpExpeditingClick' :loading="btnloading2">-->
    <!--             设置加急-->
    <!--          </a-button>-->
    <!--       </div>       -->
    <div class="box" v-if="checkPermission('MES.ProductionModule.Character.CharacterSetRemarks')">
      <a-button type="primary" @click="ExceptionRemarksClick"> 异常备注 </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="finishClick"> 单机完成 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ProductionModule.Character.CharacterDataCheck')">
      <a-button type="primary" @click="DataCheckClick" :loading="btnloading3"> 数据核对 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ProductionModule.Character.CharacterSearch')">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>

    <!--          <div v-if='advanced' >-->
    <!--         <div class="box "  v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleDataCheck')">-->
    <!--           <a-button type="primary" @click='DataCheckClick' :loading="btnloading3">-->
    <!--             数据核对-->
    <!--           </a-button>-->
    <!--         </div>-->
    <!--         </div>          -->
    <!--          <div v-if='advanced'  >-->
    <!--         <div class="box "  v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleDelOrder')">-->
    <!--           <a-button type="primary" @click='DeleteOrderClick' :loading="btnloading4">-->
    <!--             删除订单-->
    <!--           </a-button>-->
    <!--         </div>-->
    <!--         </div>          -->
    <!--          <div v-if='advanced'  >-->
    <!--         <div class="box "  v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleYcRegister')">-->
    <!--           <a-button type="primary" @click='AbnormalRegistrationClick' >-->
    <!--             异常登记-->
    <!--           </a-button>-->
    <!--         </div>-->
    <!--         </div>          -->
    <!--          <div v-if='advanced'  >-->
    <!--         <div class="box "  v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleSetRemarks')">-->
    <!--          <a-button type="primary" @click='ExceptionRemarksClick'>-->
    <!--             异常备注-->
    <!--           </a-button>-->
    <!--         </div>-->
    <!--         </div>          -->
    <!--          <div v-if='advanced'  >-->
    <!--          <div class="box "  v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtCallTrolley')">-->
    <!--            <a-button type="primary" @click='operationClick1' :loading="btnloading5">-->
    <!--              呼叫小车-->
    <!--            </a-button>-->
    <!--          </div>          -->
    <!--          </div>          -->
    <!--          <div v-if='advanced' >-->
    <!--         <div class="box "  v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtAffirm')">-->
    <!--           <a-button type="primary" @click='operationClick2' :loading="btnloading6">-->
    <!--             人员确认-->
    <!--           </a-button>-->
    <!--         </div>-->
    <!--         </div>          -->
    <!--          <div v-if='advanced' >-->
    <!--         <div class="box "  v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtAgvCancel')">-->
    <!--          <a-button type="primary" @click='operationClick3' :loading="btnloading7">-->
    <!--            取消小车-->
    <!--          </a-button>-->
    <!--        </div>-->
    <!--        </div>-->

    <!--       <div class="box">-->
    <!--          <a-button type="dashed" @click="toggleAdvanced" >-->
    <!--            {{advanced ? '收起' : '展开'}}-->
    <!--            <a-icon :type="advanced ? 'right' :  'left'" />-->
    <!--          </a-button>-->
    <!--      </div>-->
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "CuttingAction",
  props: ["btnloading1", "btnloading2", "btnloading3", "btnloading4", "btnloading5", "btnloading6", "btnloading7"],
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
    };
  },
  created() {
    this.$nextTick(() => {
      if (this.$refs.active.children.length > 7) {
        let domLength = this.$refs.active.children.length;
        let sty_ = "";
        for (var i = 0; i < domLength; i++) {
          if (i == this.$refs.active.children.length - 1) {
            sty_ = "order:11";
          } else {
            sty_ = "order:" + i * 2;
          }
          this.$refs.active.children[i].style.cssText = sty_;
        }
        // this.height = 100
        this.width = 1500;
        this.collapsed = true;
      } else {
        this.collapsed = false;
        this.width = 762;
      }
    });
  },

  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      if (this.advanced) {
        width_ = 1500;
      } else {
        width_ = 762;
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 分派订单
    handleDispatchMachine() {
      this.$emit("handleDispatchMachine");
    },
    // 部门过序
    OverOrderClick() {
      this.$emit("OverOrderClick");
    },
    //  查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 异常备注
    ExceptionRemarksClick() {
      this.$emit("ExceptionRemarksClick");
    },
    // 单击完成
    finishClick() {
      this.$emit("finishClick");
    },
    // 数据核对
    DataCheckClick() {
      this.$emit("DataCheckClick");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  float: right;
  width: 45%;
  height: 50px;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    width: 106px;
    margin: 8px 0;
    text-align: center;
    .ant-btn {
      width: 80px;
    }
    .ant-btn-loading {
      padding-left: 0 !important;
    }
  }
}
</style>
