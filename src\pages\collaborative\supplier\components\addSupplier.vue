<template>
  <a-modal
    title="新增供应商"
    :width="640"
    :visible="visible"
    centered
    :confirmLoading="confirmLoading"
    @ok="dehandleOk()"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="供应商名称" ref="suppliername" prop="suppliername">
          <a-input v-model="form.suppliername" placeholder="供应商名称" />
        </a-form-model-item>
        <a-form-model-item label="供应商代码" ref="factorycode" prop="factorycode">
          <a-input v-model="form.factorycode" placeholder="供应商代码" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { addSupplier } from "@/services/supplier/index";
export default {
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {},
      rules: {
        suppliername: [{ required: true, message: "名称必须填写", trigger: "blur" }],
        factorycode: [{ required: true, message: "代码必须填写", trigger: "blur" }],
      },
      fileList: [],
      uploading: false,
    };
  },
  created() {
    this.dehandleOk = this.debounce(this.handleOk, 500);
  },
  methods: {
    openModal() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
      });
    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate(valid => {
        if (valid) {
          addSupplier(this.form)
            .then(res => {
              this.visible = false;
              form.resetFields();
              this.$message.info("操作成功");
              this.$emit("ok");
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/.ant-modal-title {
  font-weight: 500;
}
</style>
