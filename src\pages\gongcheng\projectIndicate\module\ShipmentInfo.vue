<template>
  <a-spin :spinning="spinning">
    <div class="box">
      <a-form-model layout="inline" style="width: 100%; border-left: 1px solid #ddd; margin-top: 10px; border-top: 1px solid #ddd">
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="是接受打叉板" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-checkbox v-if="editFlg1" v-model="proOrderShippingDto.acceptCrossed"></a-checkbox>
              <span v-else>{{ proOrderShippingDto.acceptCrossed ? "是" : "" }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="加干燥剂" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-checkbox v-if="editFlg1" v-model="proOrderShippingDto.isAddDesiccant"></a-checkbox>
              <span v-else>{{ proOrderShippingDto.isAddDesiccant ? "是" : "" }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="板间隔纸" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-checkbox v-if="editFlg1" v-model="proOrderShippingDto.isBoardSpacer"></a-checkbox>
              <span v-else>{{ proOrderShippingDto.isBoardSpacer ? "是" : "" }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="湿度卡" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-checkbox v-if="editFlg1" v-model="proOrderShippingDto.isHumidityCard"></a-checkbox>
              <span v-else>{{ proOrderShippingDto.isHumidityCard ? "是" : "" }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="包装要求" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <div v-if="editFlg1">
                <a-select v-model="proOrderShippingDto.packagRequire" showSearch allowClear optionFilterProp="label">
                  <a-select-option v-for="item in mapKey(selectData.PackagRequire)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
              <span v-else>{{ proOrderShippingDto.packagRequireStr }}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="出货报告" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <div v-if="editFlg1">
                <a-select v-model="proOrderShippingDto.needReportList" mode="multiple" showSearch allowClear optionFilterProp="label">
                  <a-select-option v-for="item in mapKey(selectData.NeedReportList)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
              <span v-else>{{ proOrderShippingDto.needReportListStr }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div style="display: flex; padding: 10px; border-bottom: 1px solid #ddd; border-right: 1px solid #ddd">
          <span style="width: 28px; margin: 0 70px">工程指示</span>
          <a-textarea :rows="4" :disabled="!editFlg1" v-model="proOrderShippingDto.specialRemarks" allowClear />
        </div>
        <div style="display: flex; padding: 10px; border-bottom: 1px solid #ddd; border-right: 1px solid #ddd">
          <span style="width: 28px; margin: 0 70px">业务备注</span>
          <a-textarea :rows="4" :disabled="!editFlg1" v-model="proOrderShippingDto.cnNote" allowClear />
        </div>
        <div style="display: flex; padding: 10px; border-bottom: 1px solid #ddd; border-right: 1px solid #ddd">
          <span style="width: 28px; margin: 0 70px">客户备注</span>
          <a-textarea :rows="4" :disabled="!editFlg1" v-model="proOrderShippingDto.note" allowClear />
        </div>
      </a-form-model>
      <div class="bto">
        <!-- <a-button @click="editClick" type="primary" style="margin-top:20px;margin-right:10px;" v-if="!editFlg1" >编辑</a-button>
        <a-button @click="editClick" type="primary" style="margin-top:20px;margin-right:10px;" v-else >取消</a-button>
        <a-button @click="saveClick" type="primary" style="margin-top:20px;margin-right:10px;"  >保存</a-button> -->
      </div>
    </div>
  </a-spin>
</template>
<script>
import { shippingInformation } from "@/services/projectIndicate";
export default {
  name: "ShipmentInfo",
  props: ["proOrderShippingDto", "selectData", "editFlg1"],
  data() {
    return {
      spinning: false,
    };
  },
  methods: {
    editClick() {
      this.editFlg1 = !this.editFlg1;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    saveClick() {
      if (!this.editFlg1) {
        this.$message.warning("非编辑状态不可保存");
        return;
      }
      let params = this.proOrderShippingDto;
      params.needReportList = params.needReportList.toString();
      shippingInformation(params).then(res => {
        if (res.code) {
          this.$emit("GetProOrderInfo");
          this.$message.success("保存成功");
          this.editFlg1 = false;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
.box {
  // height:760px;
  height: 590px;
  position: relative;
  .bto {
    position: absolute;
    bottom: -48px;
    right: 374px;
  }
}
/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;

  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 28px;
    .ant-select {
      width: 120px;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 12px/1.16 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #fafafa;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 12px/1.16 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 12px/1.16 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        display: block;
        min-height: 28px;
        line-height: 26px;
        .ant-checkbox-wrapper {
          height: 28px;
        }
        .ant-select-selection--multiple {
          min-height: 28px;
          height: 28px;
        }
        .ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
          height: 22px;
          margin-top: 2px;
          line-height: 20px;
        }
        .ant-select-selection--single {
          height: 28px;
        }
        .ant-select-selection__rendered {
          line-height: 28px;
        }
        .ant-select {
          height: 28px;
        }
        .ant-input {
          height: 28px;
        }
      }
      line-height: inherit;
      padding: 2px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
    }
  }
}
</style>
