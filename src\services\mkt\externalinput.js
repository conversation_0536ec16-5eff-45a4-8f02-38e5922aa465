import { request, METHOD } from '@/utils/request';
// 订单列表
export function projectMakeOrderList (params) {
    return request("/api/app/e-mSPcb-order-wf/wf-management", METHOD.GET,params)
}
// 新增录入订单
export function OrderWF (params) {
    return request("/api/app/e-mSPcb-order-wf", METHOD.POST,params)
}
// 获取订单详情
export function projectBackEndOrderDetail (id) {
    return request(`/api/app/pcb-order/${id}/by-id`, METHOD.GET)
}
// 取单
export function TakeOrderList () {
    return request("/api/app/order-pre-button/check-order", METHOD.GET,)
}
// 开始
export function MakeStart (id) {
    return request(`/api/app/pcb-order/${id}/start-order`, METHOD.POST,)
}
// 退单
export function BackStart (params) {
    return request(`/api/app/engineering-production/back-order`, METHOD.POST,params)
}
// 审核通过
export function getModifyInformation (id) {
    return request(`/api/app/pcb-order/${id}/finished-order`, METHOD.POST)
}
// 上传文件
export function uploadPCBFile (id, file) {
    return request(`/api/app/pcb-order/${id}/upload-pCBFile`, METHOD.POST, file)
}
// 返修完成
export function RepairCompleted (Id) {
    return request(`/api/app/engineering-production/fix-finish/${Id}`, METHOD.POST,)
}
// 获取问客地址
export function getWenkeUrl (Id) {
    return request(`/api/app/engineering-production/wen-ke-url/${Id}`, METHOD.GET)
}
// 获取制作标准地址
export function getProductionStandard () {
    return request(`/api/app/engineering-production/fabricati-url`, METHOD.GET)
}
// 上传图片返回地址
export async function UploadFile(params) {
    return request(`/api/app/e-mSTProc-dRMake/up-load-drill-file`, METHOD.POST, params)
}

// 获取工厂Id列表
export async function getFactoryList() {
    return request(`/api/app/e-mSTPub-factory-configure/factory-id-and-code-list`, METHOD.POST,)
}
// 注意事项
export async function mattersNeedingAttention(params) {
    return request(`/api/app/engineering-production/matters-needing-attention`, METHOD.POST,params)
}
// 获取参数
export async function getParameter(Id) {
    return request(`/api/app/engineering-production/modify-par/${Id}`, METHOD.GET,)
}
// 获取底层铜厚
export async function getBtoParameter(params) {
    return request(`/api/app/e-mSData-class-list/data-class-list/?TypeNo=${params}`, METHOD.POST,)
}
// 参数保存
export async function SaveParameter(params) {
    return request(`/api/app/engineering-production/modify-par`, METHOD.POST,params)
}
// 生成叠层
export async function getGenerateStack(Id) {
    return request(`/api/app/engineering-make/auto-stack/${Id}`, METHOD.GET,)
}
// 叠层修改保存
export async function saveRepairRecord(params) {
    return request(`/api/app/engineering-production/auto-stack-double-click`, METHOD.POST,params)
}
// 返修记录
export async function getRepairRecord(Id) {
    return request(`/api/app/engineering-production/fix-record/${Id} `, METHOD.GET,)
}
// 作业记录
export function projectBackEndJobInfo (id) {
    return request(`/api/app/engineering-backend/information-transfer/${id}`, METHOD.GET)
}
// 叠层阻抗
export function getStackImpedance (Id) {
    return request(`/api/app/engineering-make/imp/${Id}`, METHOD.GET)
}
// 开料拼版
export function getCutting (Id) {
    return request(`/api/app/engineering-make/auto-tool-pnl/${Id}`, METHOD.GET)
}
// 上传GERBER文件 
export function UpGerber (Id) {
    return request(`/api/app/engineering-production/up-gerber-file/${Id}`, METHOD.POST)
}
// 下载GERBER文件 
export function downGerber (Id) {
    return request(`/api/app/engineering-production/down-load-path/${Id}`, METHOD.GET)
}
// 获取客户信息
export function getCustomerInfo (CustNo,factory,type) {
    return request(`/api/app/order-pre-button/rule-show-info?CustNo=${CustNo}&factory=${factory}&type=${type}`, METHOD.GET)
}
// 客户规则录入
export function getRuleEntry (Id) {
    return request(`/api/app/engineering-make/att-info/${Id}`, METHOD.GET)
}
// 查看日志 
export function getViewLog (params) {
    return request(`/api/app/pro-order-log?OrderId=${params}`, METHOD.GET,)
}
// 下载文件  /api/app/engineering-production/down-file/{Id}
export function downFile (Id) {
    return request(`/api/app/engineering-qae/down-file/${Id}`, METHOD.GET,)
}




export default {
    projectMakeOrderList,
    OrderWF,
    TakeOrderList,
    MakeStart,
    BackStart,
    getModifyInformation,
    uploadPCBFile,
    projectBackEndOrderDetail,
    projectBackEndJobInfo,
    getFactoryList,
    RepairCompleted,
    getWenkeUrl,
    getProductionStandard,
    UploadFile,
    mattersNeedingAttention,
    getParameter,
    SaveParameter,
    getBtoParameter,
    getGenerateStack,
    saveRepairRecord,
    getRepairRecord,
    getStackImpedance,
    getCutting,
    getCustomerInfo,
    getRuleEntry,
    UpGerber,
    downGerber,
    getViewLog,
    downFile,

}