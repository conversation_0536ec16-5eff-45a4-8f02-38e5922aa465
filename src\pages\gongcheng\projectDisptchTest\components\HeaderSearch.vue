<template>
<!--  <div class="searchBox">-->
    <a-form-model layout="horizontal" class="search" :modal="searchForm">
        <a-col :md="6" :sm="24">
          <a-form-model-item
              label="生产编号："
              :labelCol="{span: 8}"
              :wrapperCol="{span: 15, offset: 1}"
          >
            <a-input placeholder="请输入" v-model="searchForm.Pdctno" size="small"/>
          </a-form-model-item>
        </a-col>
        <a-col :md="6" :sm="24">
          <a-form-model-item
              label="Set样式："
              :labelCol="{span: 8}"
              :wrapperCol="{span: 15, offset: 1}"
          >
            <a-select placeholder="请选择" v-model="searchForm.PinBanType_" size="small">
              <a-select-option value="0">1X1</a-select-option>
              <a-select-option value="1">非1X1</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :md="6" :sm="24">
          <a-form-model-item
              label="板材信息："
              :labelCol="{span: 8}"
              :wrapperCol="{span: 15, offset: 1}"
          >
            <a-select
                :allowClear="true"
                show-search
                v-model="searchForm.Board_"
                placeholder="请选择..."
                @change="handleChange"
                size="small"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="fetching ? undefined : 'Not Found'"
                @search="fetchUser"
            >
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option v-for="d in searchSelectData" :key="d.board_">
                {{ d.board_ }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :md="6" :sm="24" v-show="advanced">
          <a-form-model-item
              label="交货面积："
              :labelCol="{span: 8}"
              :wrapperCol="{span: 15, offset: 1}"
          >
            <a-select placeholder="请选择" v-model="searchForm.BoardArea" size="small">
              <a-select-option value="0">样板（＜2㎡）</a-select-option>
              <a-select-option value="1">批量（≥2平方米）</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col >
        <a-col :md="6" :sm="24" v-show="advanced">
          <a-form-model-item
              label="分类："
              :labelCol="{span: 8}"
              :wrapperCol="{span: 15, offset: 1}"
          >
            <a-select placeholder="请选择" v-model="searchForm.Quality" size="small">
              <a-select-option value="0">标品</a-select-option>
              <a-select-option value="1">优品</a-select-option>
              <a-select-option value="2">精品</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :md="6" :sm="24" v-show="advanced">
          <a-form-model-item
              label="层数："
              :labelCol="{span: 8}"
              :wrapperCol="{span: 15, offset: 1}"
          >
            <a-select placeholder="请选择" v-model="searchForm.BoardLayers" size="small">
              <a-select-option value="1">1层</a-select-option>
              <a-select-option value="2">2层</a-select-option>
              <a-select-option value="4">4层</a-select-option>
              <a-select-option value="5">大于4层</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :md="6" :sm="24" v-show="advanced">
          <a-form-model-item
              label="是否大客："
              :labelCol="{span: 8}"
              :wrapperCol="{span: 15, offset: 1}"
          >
            <a-radio-group name="radioGroup" size="small" :default-value="1" v-model="searchForm.IsBigCus">
              <a-radio :value="1">
                是
              </a-radio>
              <a-radio :value="0">
                否
              </a-radio>
            </a-radio-group>
          </a-form-model-item>
        </a-col>
        <a-col :md="6" :sm="24">
          <a-form-model-item>
            <span style="float: right;">
            <a-button type="primary" @click="search">查询</a-button>
            <a-button style="margin-left: 8px" @click="reset">重置</a-button>
            <a @click="toggleAdvanced" style="margin-left: 8px">
              {{advanced ? '收起' : '展开'}}
              <a-icon :type="advanced ? 'up' : 'down'" />
            </a>
          </span>
          </a-form-model-item>
        </a-col>
    </a-form-model>
</template>

<script>
import debounce from 'lodash/debounce';
import {projectBoardInfoList} from "@/services/projectDisptch";
export default {
  inject: ["reload"],
  name: "HeaderSearch",
  data () {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      searchSelectData: [],
      fetching: false,
      advanced: false,
      searchForm: {
        Pdctno: undefined,
        BoardLayers: undefined,
        Quality: undefined,
        PinBanType_: undefined,
        BoardArea:undefined,
        Board_:undefined,
        IsBigCus:''
      }
    }
  },
  methods: {
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    search(){
      this.$emit('onSearch', this.searchForm)
    },
    fetchUser(value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.data = [];
      this.fetching = true;
      projectBoardInfoList({"Board_": value}).then(res => {
        // const data = res.map((item,index) => ({
        //   text: item.board_,
        //   value: item.board_
        // }));
        this.fetching = false;
        this.searchSelectData = res
      })
    },
    handleChange(value) {
      Object.assign(this, {
        value,
        data: [],
        fetching: false,
      });
    },
    reset(){
      this.searchForm = {
        Pdctno: undefined,
        BoardLayers: undefined,
        Quality: undefined,
        PinBanType_: undefined,
        BoardArea:undefined,
        Board_:undefined,
        IsBigCus:''
      }
      this.$emit('onSearch', this.searchForm)
    }
  }
}
</script>

<style lang="less" scoped>
.search {
    width: 70%;
    overflow: hidden;
    /deep/ .ant-col-offset-1 {
      margin-left: 0;
    }
    /deep/ .ant-form-item {
      margin: 5px 0 !important;
    }
}

</style>