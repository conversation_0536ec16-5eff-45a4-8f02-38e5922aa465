<template>
  <div ref="SelectBox">
    <div>
      <a-form-model layout="inline" style="margin-top: 10px; border-top: 1px solid #ddd" id="formDataElemTwo2">
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="字符颜色"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.fontColor && iseval(requiredLinkConfigpro.fontColor.isNullRules) ? 'require' : ''"
            >
              <div>
                <b style="color: red; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [顶] </b>
                <a-select
                  v-model="proOrderInfoDto.fontColor"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  @change="fontColorC('change')"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="firstInput"
                  v-focus-next-on-tab="'2'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FontColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <b style="color: green; font-family: PingFangSC-Regular, Sans-serif; font-size: 12px"> [底] </b>
                <a-select
                  v-model="proOrderInfoDto.fontColorBottom"
                  style="width: 90px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="2"
                  v-focus-next-on-tab="'3'"
                  @change="fontColorC('change', 'bot')"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.FontColor)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="margin-left: 10px">字符特性:</span>
                <a-select
                  v-model="proOrderInfoDto.characterInkCharacteristics"
                  style="width: 83px; margin-left: 5px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="3"
                  v-focus-next-on-tab="'4'"
                >
                  <a-select-option
                    v-for="item in mapKey(selectData.CharacterInkCharacteristics)"
                    :key="item.value"
                    :value="item.value"
                    :lable="item.lable"
                    :title="item.lable"
                  >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="字符油墨"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :class="requiredLinkConfigpro.characterResistInk && iseval(requiredLinkConfigpro.characterResistInk.isNullRules) ? 'require' : ''"
            >
              <div>
                <a-select
                  v-model="proOrderInfoDto.characterResistInk"
                  style="width: 231px"
                  showSearch
                  allowClear
                  optionFilterProp="lable"
                  :getPopupContainer="() => this.$refs.SelectBox"
                  ref="4"
                  v-focus-next-on-tab="'5'"
                >
                  <a-select-option v-for="item in CharacterResistInk1" :key="item.value" :value="item.value" :lable="item.lable" :title="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                <span style="padding-right: 10px; padding-left: 10px">字符无卤 :</span>
                <a-checkbox v-model="proOrderInfoDto.characterNotHalogen" ref="5" v-focus-next-on-tab="'6'" @change="fontColorC('change', 'bot')" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8" class="colSTY">
            <a-form-model-item
              label="表面处理"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              class="surSTY"
              :class="requiredLinkConfigpro.surfaceFinish && iseval(requiredLinkConfigpro.surfaceFinish.isNullRules) ? 'require' : ''"
            >
              <div style="display: flex; flex-wrap: wrap">
                <span style="width: 37%">
                  <a-select
                    v-model="proOrderInfoDto.surfaceFinish"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    @change="changesurface"
                    ref="6"
                    @keydown.native.enter="handleLastInputEnter"
                    v-focus-next-on-tab="'7'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.SurfaceFinish)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </span>
                <span style="color: red; margin: 0 0.1%; width: 30%" v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold'"
                  >水金:
                  <a-select
                    style="display: inline-block; width: 49%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="7"
                    v-focus-next-on-tab="'8'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 23%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'waterhardgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj' ||
                    proOrderInfoDto.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+golddeposition' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+goldplating'
                  "
                  >面积:
                  <a-input
                    style="display: inline-block; width: 46%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                    ref="8"
                    v-focus-next-on-tab="'9'"
                    @blur="truncationspa()"
                  />%
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 32%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin'
                  "
                  >镀金面积
                  <a-input
                    style="display: inline-block; width: 37%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                    ref="9"
                    v-focus-next-on-tab="'10'"
                    @blur="truncationspa()"
                  />
                  %
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 34%"
                  :class="proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ? 'sss' : ''"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'fullgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'hardgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold'
                  "
                  >镀金厚:
                  <a-select
                    style="display: inline-block; width: 49%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="10"
                    v-focus-next-on-tab="'11'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 34%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'waterplatedgold' ||
                    proOrderInfoDto.surfaceFinish == 'waterhardgold' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj' ||
                    proOrderInfoDto.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+golddeposition' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadspraytin+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+GoldPlatedFingers'
                  "
                  >镍:
                  <a-select
                    style="display: inline-block; width: 70%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate7($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch7($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur7($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="11"
                    v-focus-next-on-tab="'12'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 34%" v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold'"
                  >软金:
                  <a-select
                    style="display: inline-block; width: 50%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="12"
                    v-focus-next-on-tab="'13'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 30%"
                  v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold'"
                  :class="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ? 'sss' : ''"
                  >化金厚:
                  <a-select
                    style="display: inline-block; width: 42%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="13"
                    v-focus-next-on-tab="'14'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 32%; height: 30px"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'immersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'electrogold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'cjandjbdj' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' ||
                    proOrderInfoDto.surfaceFinish == 'wholeimmersiongoldandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandcarbonink' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandchemical' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldHaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'fullgilding' ||
                    proOrderInfoDto.surfaceFinish == 'goldnickelplatingandhaslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'nickelgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersionnickelgold' ||
                    proOrderInfoDto.surfaceFinish == 'thickgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingwithoutnickelplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldanddj' ||
                    proOrderInfoDto.surfaceFinish == 'sunkengoldgold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'SelectiveThickGoldPlating' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+golddeposition' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+goldplating' ||
                    proOrderInfoDto.surfaceFinish == 'Leadspraytin+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Antioxidant+GoldPlatedFingers'
                  "
                  >金:
                  <a-select
                    style="display: inline-block; width: 52%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="14"
                    v-focus-next-on-tab="'15'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span v-if="proOrderInfoDto.surfaceFinish == 'immersiongold'" style="width: 17%">
                  封孔:
                  <a-checkbox style="display: inline-block" v-model="proOrderInfoDto.holeSealing" />
                </span>
                <span v-if="proOrderInfoDto.surfaceFinish == 'immersiongold'" style="width: 50%">
                  封孔盐雾时间(H):
                  <a-input style="display: inline-block; width: 30%" v-model="proOrderInfoDto.holeSealingTime" />
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 20%; height: 30px"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                  "
                  >薄金:
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="15"
                    v-focus-next-on-tab="'16'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 23%" v-if="proOrderInfoDto.surfaceFinish == 'waterhardgold'"
                  >面积:
                  <a-input
                    style="display: inline-block; width: 46%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                    ref="16"
                    v-focus-next-on-tab="'17'"
                    @blur="truncationspa2()"
                  />%
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 29%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandgoldplating'
                  "
                  >金:
                  <a-select
                    style="display: inline-block; width: 64%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="17"
                    v-focus-next-on-tab="'18'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 25%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold'
                  "
                  >厚金:
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="18"
                    v-focus-next-on-tab="'19'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 62%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'osp' ||
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wholegoldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandGoldfinger'
                  "
                  :class="
                    proOrderInfoDto.surfaceFinish == 'immersiongoldandosp' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandosp' ||
                    proOrderInfoDto.surfaceFinish == 'ospandimmersiongoldfinger'
                      ? 'sss'
                      : ''
                  "
                  >膜厚:
                  <a-input
                    style="display: inline-block; width: 74%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.filmThickness"
                    ref="19"
                    v-focus-next-on-tab="'20'"
                    @blur="truncationsft()"
                  />um
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 62%"
                  v-if="
                    (proOrderInfoDto.surfaceFinish == 'chemicalsilver' && [78, 79, 80].indexOf(Number($route.query.factory)) == -1) ||
                    proOrderInfoDto.surfaceFinish == 'sinksilverandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'silverplating' ||
                    proOrderInfoDto.surfaceFinish == 'outsourcingsilverplating' ||
                    proOrderInfoDto.surfaceFinish == 'electroplatingsilverelectroplatinggold' ||
                    proOrderInfoDto.surfaceFinish == 'chemicalsilverandgoldplating'
                  "
                  >银厚:
                  <a-input
                    style="display: inline-block; width: 74%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness"
                    ref="20"
                    v-focus-next-on-tab="'21'"
                    @blur="truncationsnst()"
                  />um
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 62%"
                  v-if="proOrderInfoDto.surfaceFinish == 'chemicalsilver' && [78, 79, 80].indexOf(Number($route.query.factory)) != -1"
                  >银厚:
                  <a-select
                    style="display: inline-block; width: 55%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="20"
                    v-focus-next-on-tab="'21'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.HeavySilver_)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 100%"
                  :class="proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree' ? 'sss' : ''"
                  v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandhaslwithfree'"
                  >无铅锡厚 :
                  <a-input
                    style="display: inline-block; width: 73%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newTinThickness"
                    ref="21"
                    v-focus-next-on-tab="'22'"
                  />um
                </span>
                <span style="color: red; margin: 0 0.1%; width: 29%" v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger'"
                  >镀镍厚:
                  <a-select
                    style="display: inline-block; width: 55%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate7($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch7($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur7($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="22"
                    v-focus-next-on-tab="'23'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 29%" v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger'"
                  >镍面积 :
                  <a-input
                    style="display: inline-block; width: 52%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea"
                    ref="23"
                    v-focus-next-on-tab="'24'"
                    @blur="truncationspa()"
                  />
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 62%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'haslwithlead' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'spraytin' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiontin' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandcarbonoil' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree' ||
                    proOrderInfoDto.surfaceFinish == 'tinplating' ||
                    proOrderInfoDto.surfaceFinish == 'tinnedcerium' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'wqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'yqpxandty' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandGoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'Leadspraytin+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Leadfreetinspraying+gold-platedfingers' ||
                    proOrderInfoDto.surfaceFinish == 'Tinspraying+goldplating'
                  "
                  :class="
                    proOrderInfoDto.surfaceFinish == 'haslwithleadandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'haslwithfreeandimmersiongoldfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatedfingerandhaslwithfree'
                      ? 'sss'
                      : ''
                  "
                  >锡:
                  <a-select
                    style="display: inline-block; width: 80%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newTinThickness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate10($event, mapKey(selectData.Tinthickness))"
                    @search="handleSearch10($event, mapKey(selectData.Tinthickness))"
                    @blur="handleBlur10($event, mapKey(selectData.Tinthickness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="24"
                    v-focus-next-on-tab="'25'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.Tinthickness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >um
                </span>
                <span style="color: red; margin: 0 0.1%; width: 62%" v-if="proOrderInfoDto.surfaceFinish == 'tinprecipitation'"
                  >锡:
                  <a-select
                    style="display: inline-block; width: 80%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate11($event, mapKey(selectData.Tinthickness2))"
                    @search="handleSearch11($event, mapKey(selectData.Tinthickness2))"
                    @blur="handleBlur11($event, mapKey(selectData.Tinthickness2))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="25"
                    v-focus-next-on-tab="'26'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.Tinthickness2)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >um
                </span>
                <span style="margin: 0 0.1%; width: 62%" v-if="proOrderInfoDto.surfaceFinish == 'tinprecipitation'"
                  >是否含有SMT贴片:
                  <a-checkbox style="display: inline-block" v-model="proOrderInfoDto.isSmt" />
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 29%"
                  v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' || proOrderInfoDto.surfaceFinish == 'hardgoldplating'"
                  >镍厚 :
                  <a-select
                    style="display: inline-block; width: 55%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate8($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch8($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur8($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="26"
                    v-focus-next-on-tab="'27'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 29%; height: 30px" v-if="proOrderInfoDto.surfaceFinish == 'fullgoldplating'"
                  >镍厚 :
                  <a-select
                    style="display: inline-block; width: 55%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate8($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch8($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur8($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="27"
                    v-focus-next-on-tab="'28'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 44%; height: 30px"
                  v-if="proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' && [78, 79, 80].indexOf(Number($route.query.factory)) == -1"
                  >钯 :
                  <a-input
                    style="display: inline-block; width: 62%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.paThickness"
                    ref="28"
                    v-focus-next-on-tab="'29'"
                    @blur="truncationspt()"
                  />{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 44%; height: 30px"
                  v-if="proOrderInfoDto.surfaceFinish == 'nickelpalladiumgold' && [78, 79, 80].indexOf(Number($route.query.factory)) != -1"
                  >钯 :
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.paThickness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="28"
                    v-focus-next-on-tab="'29'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.PalladiumThick_)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span style="color: red; margin: 0 0.1%; width: 28%" v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold'"
                  >化金面积:
                  <a-input
                    style="display: inline-block; width: 39%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                    ref="29"
                    v-focus-next-on-tab="'30'"
                    @blur="truncationspa2()"
                  />%
                </span>
                <span style="color: red; margin: 0 0.1%; width: 20%" v-if="proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger'"
                  >金:
                  <a-select
                    style="display: inline-block; width: 60%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="30"
                    v-focus-next-on-tab="'31'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
                <span
                  style="color: red; margin: 0 0.1%; width: 24%"
                  v-if="
                    proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold' ||
                    proOrderInfoDto.surfaceFinish == 'nickelplatingandgoldplatedfinger' ||
                    proOrderInfoDto.surfaceFinish == 'goldplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Goldplatingwithoutnickelplatinglocalthickgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersionnickelgoldplatinghardgold' ||
                    proOrderInfoDto.surfaceFinish == 'Immersiongoldlocalthickgold'
                  "
                  >面积:
                  <a-input
                    style="display: inline-block; width: 39%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.platedArea2"
                    ref="31"
                    v-focus-next-on-tab="'32'"
                    @blur="truncationspa2()"
                  />%
                </span>

                <span style="color: red; margin: 0 0.1%; width: 39%" v-if="proOrderInfoDto.surfaceFinish == 'goldplatedfingerandimmersiongold'"
                  >化金镍厚:
                  <a-select
                    style="display: inline-block; width: 59%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    @change="setEstimate8($event, mapKey(selectData.CJNickelThinckness))"
                    @search="handleSearch8($event, mapKey(selectData.CJNickelThinckness))"
                    @blur="handleBlur8($event, mapKey(selectData.CJNickelThinckness))"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="32"
                    v-focus-next-on-tab="'33'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.CJNickelThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>

                <span
                  style="color: red; margin: 0 0.1%; width: 29%; height: 30px"
                  v-if="proOrderInfoDto.surfaceFinish == 'goldplatingandimmersiongold'"
                  >化金厚:
                  <a-select
                    style="display: inline-block; width: 42%"
                    v-model="proOrderInfoDto.surfaceFinishJsonDto.imGoldThinckness2"
                    showSearch
                    allowClear
                    optionFilterProp="lable"
                    :getPopupContainer="() => this.$refs.SelectBox"
                    ref="33"
                    v-focus-next-on-tab="'34'"
                  >
                    <a-select-option
                      v-for="(item, index) in mapKey(selectData.ImGoldThinckness)"
                      :key="index"
                      :value="item.value"
                      :lable="item.lable"
                      :title="item.lable"
                    >
                      {{ item.lable }}
                    </a-select-option> </a-select
                  >{{ proOrderInfoDto.surfaceFinishThickUnit }}
                </span>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>
<script>
let index = -1;
let focusInput = false;
import $ from "jquery";
export default {
  name: "ProThree",
  props: ["shouldjump", "proOrderInfoDto", "selectData", "editFlg1", "messageList", "requiredLinkConfigpro", "CharacterResistInk1"],
  inject: ["reload"],
  components: {},
  data() {
    return {};
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  directives: {
    focusNextOnTab: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode !== 13 || focusInput) return; // 非回车键直接返回

          // 清理无效的 refs 引用
          const refs = vnode.context.$refs;
          for (const key in refs) {
            if (!refs[key]) delete refs[key];
          }

          const refKeys = Object.keys(refs);
          const currentIndex = refKeys.indexOf(value); // 当前元素索引

          // 情况1:目标元素不存在于 refs 列表
          if (currentIndex === -1) {
            console.log("目标元素不存在于 refs 列表");
            const nextIndex = typeof index !== "undefined" ? index + 1 : 0;
            if (nextIndex < refKeys.length) {
              const nextInput = refs[refKeys[nextIndex]];
              if (nextInput && !nextInput.disabled) {
                index = nextIndex;
                nextInput.focus();
              }
            }
          }
          // 情况2:目标元素存在且可用
          else if (!refs[value].disabled) {
            console.log("目标元素存在且可用");
            index = currentIndex;
            refs[value].focus();
          }
          // 情况3:向后查找最多12个可用元素
          else {
            console.log("目标元素存在但不可用");
            for (let offset = 1; offset <= 12; offset++) {
              const targetIndex = currentIndex + offset;
              if (targetIndex >= refKeys.length) break;

              const targetKey = refKeys[targetIndex];
              const targetEl = refs[targetKey];

              if (targetEl && !targetEl.disabled) {
                targetEl.focus();
                break; // 找到第一个可用元素后停止
              }
            }
          }
        });
      },
    },
  },
  watch: {
    shouldjump: {
      handler(newVal) {
        if (newVal) {
          // 聚焦第一个输入框
          focusInput = true;
          this.$refs.firstInput.focus();
          index = -1; // 重置索引
          // 通知父组件重置状态
          this.$emit("resetjump");
        }
        setTimeout(() => {
          focusInput = false;
        }, 500);
      },
    },
    messageList: {
      handler(val) {
        this.get(val);
      },
    },
  },
  methods: {
    handleLastInputEnter(e) {
      event.preventDefault(); // 阻止默认行为（如打开下拉框）
      this.$emit("jumptothree");
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("val") != -1) {
        newIsNullRules = newIsNullRules.replace(/val/g, "this.proOrderInfoDto");
      }
      return eval(newIsNullRules);
    },

    truncationspa() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.platedArea) {
        var platedArea = this.proOrderInfoDto.surfaceFinishJsonDto.platedArea.split("");
        if (platedArea.length > 5) {
          platedArea = platedArea.slice(0, 5);
          this.$message.warning("表面处理面积不能超过5个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.platedArea = platedArea.join("");
      }
    },
    truncationspa2() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.platedArea2) {
        var platedArea2 = this.proOrderInfoDto.surfaceFinishJsonDto.platedArea2.split("");
        if (platedArea2.length > 5) {
          platedArea2 = platedArea2.slice(0, 5);
          this.$message.warning("表面处理面积不能超过5个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.platedArea2 = platedArea2.join("");
      }
    },
    truncationsft() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.filmThickness) {
        var filmThickness = this.proOrderInfoDto.surfaceFinishJsonDto.filmThickness.split("");
        if (filmThickness.length > 10) {
          filmThickness = filmThickness.slice(0, 10);
          this.$message.warning("表面处理膜厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.filmThickness = filmThickness.join("");
      }
    },
    truncationsnst() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness) {
        var newSilverThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness.split("");
        if (newSilverThickness.length > 10) {
          newSilverThickness = newSilverThickness.slice(0, 10);
          this.$message.warning("表面处理膜厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newSilverThickness = newSilverThickness.join("");
      }
    },
    truncationspt() {
      if (this.proOrderInfoDto.surfaceFinishJsonDto.paThickness) {
        var paThickness = this.proOrderInfoDto.surfaceFinishJsonDto.paThickness.split("");
        if (paThickness.length > 10) {
          paThickness = paThickness.slice(0, 10);
          this.$message.warning("表面处理镍钯厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.paThickness = paThickness.join("");
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    changesurface() {
      if (
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandhaslwithfree" ||
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandimmersiongold" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithfreeandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithleadandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "nickelplatingandgoldplatedfinger" ||
        this.proOrderInfoDto.surfaceFinish == "ospandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "wholeimmersiongoldandimmersiongoldfinger"
      ) {
        this.proOrderInfoDto.isGoldfinger = true;
      } else {
        this.proOrderInfoDto.isGoldfinger = false;
      }
    },
    fontColorC(type, val) {
      this.$emit("fontColorC", type, val);
    },
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },
    setEstimate7(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = value;
    },
    handleSearch7(value, list) {
      this.setEstimate7(value, list);
    },
    handleBlur7(value, list) {
      this.setEstimate7(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness) {
        var cjNickelThinckness = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness.split("");
        if (cjNickelThinckness.length > 10) {
          cjNickelThinckness = cjNickelThinckness.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = cjNickelThinckness.join("");
      }
    },
    setEstimate8(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = value;
    },
    handleSearch8(value, list) {
      this.setEstimate8(value, list);
    },
    handleBlur8(value, list) {
      this.setEstimate8(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2) {
        var cjNickelThinckness2 = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2.split("");
        if (cjNickelThinckness2.length > 10) {
          cjNickelThinckness2 = cjNickelThinckness2.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = cjNickelThinckness2.join("");
      }
    },
    setEstimate10(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = value;
    },
    handleSearch10(value, list) {
      this.setEstimate10(value, list);
    },
    handleBlur10(value, list) {
      this.setEstimate10(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness) {
        var newTinThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness.split("");
        if (newTinThickness.length > 10) {
          newTinThickness = newTinThickness.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = newTinThickness.join("");
      }
    },
    setEstimate11(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = value;
    },
    handleSearch11(value, list) {
      this.setEstimate11(value, list);
    },
    handleBlur11(value, list) {
      this.setEstimate11(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2) {
        var newTinThickness2 = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2.split("");
        if (newTinThickness2.length > 10) {
          newTinThickness2 = newTinThickness2.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = newTinThickness2.join("");
      }
    },
    get(val) {
      $("#formDataElemTwo2 .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
  },
  created() {},
};
</script>
<style scoped lang="less">
/deep/.ant-col-20 {
  width: 1291px;
}
/deep/.ant-col-4 {
  width: 258px;
}
/deep/.ant-col-3 {
  width: 194px;
}
/deep/.ant-col-8 {
  width: 516px;
}
/deep/.ant-col-9 {
  width: 582px;
}
.div1 {
  /deep/.ant-form-item-control {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.div2 {
  .div22 {
    /deep/.ant-form-item-control {
      padding: 0;
      min-height: 28px !important;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}

.autoo {
  /deep/.ant-form-item-control {
    height: 100% !important;
    width: 1184px;
  }
  /deep/.ant-form-item-control {
    // height: auto!important;
    background: #f5f5f5 !important;
    .ant-input {
      margin-top: 4px !important;
      margin-bottom: 0 !important;
    }
  }
}
/deep/textarea.ant-input {
  min-height: 24px;
  line-height: 1.3;
}
.speclass {
  /deep/ .ant-form-item-label {
    width: 107px;
  }
  /deep/ .ant-form-item-label > label {
    font-size: 13px !important;
  }
  /deep/ .ant-form-item-control-wrapper {
    width: 1178px;
  }
}
/deep/ .div1 {
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px !important;
    }
  }
  .ant-form-item-children {
    overflow: inherit !important;
    font-size: 13px;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  /* border-color: #ff9900; */
  border: 2px solid red;
}
/deep/.ant-select-focused .ant-select-selection {
  border: 2px solid red;
}
/deep/.ant-input:focus {
  border: 2px solid red;
}
/deep/.colSTY {
  border-left: 1px solid #ddd;
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px;
    }
  }
}
// /deep/.ant-col{
//   border-left:1px solid #ddd;
// }
/deep/.sss {
  height: 30px;
}
/deep/.ant-col-3 {
  .ant-col-14 {
    width: 58.5%;
  }
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-input {
  padding: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 11px !important;
  margin-left: 6px !important;
}

/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
// /deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
//     height: 16px!important;
//     margin-top: 3px;
//     line-height: 14px!important;
//   }
/deep/.surSTY {
  // height:56px;
  .ant-form-item-control-wrapper {
    // min-height:20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
      }
    }
  }
}
/deep/.heightSty {
  height: 28px;
  .ant-select {
    margin-top: 2px;
  }
  .ant-select-selection--multiple {
    height: 20px;
    min-height: 20px;
  }
  .ant-select-allow-clear {
    .ant-select-selection--multiple {
      height: 23px;
    }
    .ant-select-selection__rendered {
      ul {
        display: flex;
        li {
          height: 16px;
          margin-top: 2px;
          line-height: 16px;
          user-select: none;
          padding-left: 0 !important;
        }
      }
    }
    .ant-select-selection__clear {
      top: 13px;
    }
  }
}
/deep/.heightSty1 {
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
        // .edit{
        //  line-height: 28px!important;
        // text-overflow: unset!important;
        // white-space: unset!important;
        // }
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 17px !important;
            margin-top: 3px;
            line-height: 15px !important;
            width: 92%;
          }

          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}

/deep/.ant-select-dropdown-menu-item {
  font-size: 14px;
  padding: 0.5%;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-size: 14px !important;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-size: 14px !important;
  font-weight: 500;

  color: #000000;
}

.ant-row {
  .ant-col-22 {
    .ant-form-item-control {
      .ant-input-affix-wrapper {
        line-height: 29px;
      }
    }
  }
  .ant-col-17 {
    .ant-form-item {
      /deep/.ant-input {
        min-height: 23px !important;
        height: 23px !important;
        line-height: 15px !important;
      }
    }
  }
  .ant-col-24 {
    /deep/.ant-form-item-label {
      width: 106px !important;
    }
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.disable {
  background: #f5f5f5 !important;
}

/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;
  min-height: 28px;
  .tmp1 {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 370px;
    display: inline-block;
  }
  .tmp {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 140px;
    display: inline-block;
  }
  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 25px;
    .ant-select {
      width: 20;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #f1f1f1;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        // display: block;
        // min-height: 25px;
        line-height: 25px;
        // // vertical-align: top;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
        // width: 100%;
        // display: inline-block;

        // .ant-select-allow-clear{
        //   // .ant-select-selection--multiple{
        //   //   height: 23px;
        //   //   margin-top:2px;
        //   // }
        //   .ant-select-selection__rendered{
        //     ul{
        //       display: flex;
        //       li{
        //         margin-top:-1px;
        //       }
        //     }
        //     .ant-select-selection__choice{
        //       height: 18px;
        //       margin-top: 2px;
        //       line-height: 14px;
        //       user-select: none;
        //     }
        //   }
        //   .ant-select-selection__clear{
        //     top:11px;
        //   }
        // }
        .ant-checkbox-wrapper {
          min-height: 28px;
        }
        .ant-select-selection--single {
          height: 22px;
        }
        .ant-select-selection__rendered {
          line-height: 18px;
        }
        .ant-select {
          height: 22px;
        }
        .ant-input {
          height: 22px;
          padding-top: 2.6px;
        }
      }
      line-height: inherit;
      padding: 0px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      height: 28px;
    }
  }
}
.div2 {
  /deep/ .ant-form-item {
    margin: 0;
    width: 100%;
    display: flex;
    min-height: 28px;
    .tmp1 {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 370px;
      display: inline-block;
    }
    .tmp {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 140px;
      display: inline-block;
    }
    .editWrapper {
      display: flex;
      align-items: center;
      min-height: 25px;
      .ant-select {
        width: 20;
      }
      .ant-input {
        width: 120px;
      }
      .ant-input-number {
        width: 120px;
      }
    }
    .ant-form-item-label {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      color: #666;
      background-color: #f1f1f1;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      label {
        font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      }
    }
    .ant-form-item-control-wrapper {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      .ant-form-item-control {
        .ant-form-item-children {
          // display: block;
          // min-height: 25px;
          line-height: 25px;
          // // vertical-align: top;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          // overflow: hidden;
          // width: 100%;
          // display: inline-block;

          // .ant-select-allow-clear{
          //   // .ant-select-selection--multiple{
          //   //   height: 23px;
          //   //   margin-top:2px;
          //   // }
          //   .ant-select-selection__rendered{
          //     ul{
          //       display: flex;
          //       li{
          //         margin-top:-1px;
          //       }
          //     }
          //     .ant-select-selection__choice{
          //       height: 18px;
          //       margin-top: 2px;
          //       line-height: 14px;
          //       user-select: none;
          //     }
          //   }
          //   .ant-select-selection__clear{
          //     top:11px;
          //   }
          // }
          .ant-checkbox-wrapper {
            min-height: 28px;
          }
          .ant-select-selection--single {
            height: 22px;
          }
          .ant-select-selection__rendered {
            line-height: 18px;
          }
          .ant-select {
            height: 22px;
          }
          .ant-input {
            height: 22px;
            padding-top: 2.6px;
          }
        }
        line-height: inherit;
        padding: 0px 5px;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        height: auto !important;
      }
    }
  }
}
</style>
