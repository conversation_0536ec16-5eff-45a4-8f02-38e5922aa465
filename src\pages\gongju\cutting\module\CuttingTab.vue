<!-- 工具管理- 开料拼版-开料表格 -->
<template>
  <div class="cuttingTable">
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="tableLoading"
      :pagination="false"
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      :scroll="{ y: 124, x: 1000 }"
      :rowClassName="setRowClassName"
      :customRow="customRow"
      :class="tableData.length ? 'mintable' : ''"
    >
      <template slot="isGDKL" slot-scope="text, record">
        <a-checkbox v-model="record.isGDKL"> </a-checkbox>
      </template>

      <template slot="isCutRLTB" slot-scope="text, record">
        <a-checkbox v-model="record.isCutRLTB"> </a-checkbox>
      </template>
    </a-table>
  </div>
</template>

<script>
// 多余4%
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 45,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "Panel长",
    dataIndex: "cpanelc",
    width: 55,
    align: "center",
  },
  {
    title: "Panel宽",
    dataIndex: "cpanelk",
    width: 55,
    align: "center",
  },
  {
    title: "上边距",
    dataIndex: "csbj",
    width: 55,
    align: "center",
  },
  {
    title: "下边距",
    dataIndex: "cxbj",
    width: 55,
    align: "center",
  },
  {
    title: "左边距",
    dataIndex: "czbj",
    width: 55,
    align: "center",
  },
  {
    title: "右边距",
    dataIndex: "cybj",
    width: 55,
    align: "center",
  },
  {
    title: "板材长",
    dataIndex: "cbcc",
    width: 50,
    align: "center",
  },
  {
    title: "板材宽",
    dataIndex: "cbck",
    width: 50,
    align: "center",
  },
  {
    title: "板材(inch)",
    dataIndex: "boardWL",
    width: 80,
    align: "center",
  },
  {
    title: "单元数",
    dataIndex: "allunitnum",
    width: 50,
    align: "center",
  },
  {
    title: "拼版数",
    dataIndex: "cpbs",
    width: 45,
    align: "center",
  },
  {
    title: "开料数",
    dataIndex: "ckls",
    width: 50,
    align: "center",
  },
  {
    title: "成品率",
    dataIndex: "cplyl",
    width: 50,
    align: "center",
  },
  {
    title: "拼版利用率",
    dataIndex: "cpblyl",
    width: 90,
    align: "center",
  },
  {
    title: "板材利用率",
    dataIndex: "cbclyl",
    width: 90,
    align: "center",
  },
  {
    title: "净面积",
    dataIndex: "pcsArea",
    width: 50,
    align: "center",
  },
  {
    title: "固边",
    dataIndex: "isGDKL",
    width: 45,
    align: "center",
    scopedSlots: { customRender: "isGDKL" },
  },
  {
    title: "减边",
    dataIndex: "isCutRLTB",
    width: 45,
    align: "center",
    scopedSlots: { customRender: "isCutRLTB" },
  },
];
export default {
  name: "CuttingTab",
  props: {
    tableData: {
      type: Array,
    },
    tableLoading: {
      type: Boolean,
    },
    TaoBan: {
      type: Boolean,
    },
    ABcutting: {
      type: Boolean,
    },
    showtag: {
      type: Boolean,
    },
  },
  data() {
    return {
      columns,
      colID: 0,
      tabIndex: 0,
      selectRow: {},
      dbid: null,
    };
  },
  methods: {
    setRowClassName(record) {
      if (record.indexid == this.tabIndex) {
        // console.log(record.indexid, this.tabIndex)
        return "rowcolor";
      }
    },
    customRow(record) {
      return {
        on: {
          dblclick: () => {
            //双击
            this.handlerCustomRowdblClick(record);
            if (this.showtag) {
              this.dbid = record.indexid;
            }
            this.selectRow = record;
          },
        },
      };
    },
    handlerCustomRowdblClick(record) {
      this.tabIndex = record.indexid;
      this.$emit("getImgData2", record, "dbclick");
    },
  },
  watch: {
    TaoBan(val) {
      if (val && this.columns[11].title != "套板数") {
        columns.splice(11, 0, {
          title: "套板数",
          dataIndex: "uResult.tbDataInfo.smallRecNum",
          width: 45,
          align: "center",
        });
      } else if (!val && this.columns[11].title == "套板数") {
        columns.splice(11, 1);
      }
    },
    ABcutting(val) {
      if (val && this.columns.length < 21) {
        columns.splice(
          this.columns[11].title == "套板数" ? 20 : 19,
          0,
          {
            title: "BPanel长",
            dataIndex: "cpanelc4B",
            width: 70,
            align: "center",
          },
          {
            title: "BPanel宽",
            dataIndex: "cpanelk4B",
            width: 70,
            align: "center",
          },
          {
            title: "B上边距",
            dataIndex: "csbj4B",
            width: 60,
            align: "center",
          },
          {
            title: "B下边距",
            dataIndex: "cxbj4B",
            width: 60,
            align: "center",
          },
          {
            title: "B左边距",
            dataIndex: "czbj4B",
            width: 60,
            align: "center",
          },
          {
            title: "B右边距",
            dataIndex: "cybj4B",
            width: 60,
            align: "center",
          },
          {
            title: "B拼版数",
            dataIndex: "cpbs4B",
            width: 60,
            align: "center",
          },
          {
            title: "B开料数",
            dataIndex: "ckls4B",
            width: 60,
            align: "center",
          },
          {
            title: "B拼利用率",
            dataIndex: "cpblyl4B",
            width: 70,
            align: "center",
          },
          {
            title: "B净面积",
            dataIndex: "pcsArea4B",
            width: 60,
            align: "center",
          },
          {
            title: "AB总拼数",
            dataIndex: "abSumPCS",
            width: 90,
            align: "center",
          }
        );
      } else {
        columns.splice(this.columns[11].title == "套板数" ? 20 : 19, 11);
      }
    },
  },
  mounted() {
    if (this.TaoBan && this.columns[11].title != "套板数") {
      columns.splice(11, 0, {
        title: "套板数",
        dataIndex: "uResult.tbDataInfo.smallRecNum",
        width: 45,
        align: "center",
      });
    } else if (!this.TaoBan && this.columns[11].title == "套板数") {
      columns.splice(11, 1);
    }
    if (this.ABcutting && this.columns.length < 21) {
      columns.splice(
        this.columns[11].title == "套板数" ? 20 : 19,
        0,
        {
          title: "BPanel长",
          dataIndex: "cpanelc4B",
          width: 70,
          align: "center",
        },
        {
          title: "BPanel宽",
          dataIndex: "cpanelk4B",
          width: 70,
          align: "center",
        },
        {
          title: "B上边距",
          dataIndex: "csbj4B",
          width: 60,
          align: "center",
        },
        {
          title: "B下边距",
          dataIndex: "cxbj4B",
          width: 60,
          align: "center",
        },
        {
          title: "B左边距",
          dataIndex: "czbj4B",
          width: 60,
          align: "center",
        },
        {
          title: "B右边距",
          dataIndex: "cybj4B",
          width: 60,
          align: "center",
        },
        {
          title: "B拼版数",
          dataIndex: "cpbs4B",
          width: 60,
          align: "center",
        },
        {
          title: "B开料数",
          dataIndex: "ckls4B",
          width: 60,
          align: "center",
        },
        {
          title: "B拼利用率",
          dataIndex: "cpblyl4B",
          width: 70,
          align: "center",
        },
        {
          title: "B净面积",
          dataIndex: "pcsArea4B",
          width: 60,
          align: "center",
        },
        {
          title: "AB总拼数",
          dataIndex: "abSumPCS",
          width: 90,
          align: "center",
        }
      );
    } else {
      columns.splice(this.columns[11].title == "套板数" ? 20 : 19, 11);
    }
  },
};
</script>
<style scoped lang="less">
.tabwidth {
  z-index: 99;
  position: absolute;
  width: 81%;
}
.mintable {
  /deep/.ant-table-body {
    height: 124px;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.cuttingTable {
  border-top: 1px solid #efefef;
  /deep/.ant-table {
    line-height: 10px !important  ;
    height: 147px;
  }
  /deep/.ant-empty {
    margin: 2px;
  }
  /deep/ .ant-table {
    .ant-table-scroll {
      .ant-table-thead {
        th {
          padding: 14px 0 !important;
          border-right: 1px solid #efefef;
          color: rgba(108, 116, 127, 1);
        }
      }
    }

    .ant-table-body {
      td {
        padding: 10px 0;
        color: rgba(51, 51, 51, 1);
        font-size: 14px;
        border-right: 1px solid #efefef;
      }
      .rowcolor {
        background: #dfdcdc;
        -moz-user-select: none;
        -webkit-user-select: none;
        user-select: none;
      }
    }
  }
}
</style>
