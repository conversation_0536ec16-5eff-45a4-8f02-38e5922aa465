// suppliers.i18n.js
const MODULES = {
  table: require("./maintablei18n.js"),
  tabs: require("./supplierStabsi18n.js"),
  label: require("./Basiclabeli18n.js"),
  tableo: require("./othertablei18n.js"),
  tablo: require("./Supplierconfigurationtranslationi18n.js"),
  // 通用组件单独声明
  button: require("../../common/buttoni18n.js"),
};

function mergeMessages() {
  const messages = { CN: {}, HK: {}, US: {}, TH: {} };

  Object.values(MODULES).forEach(module => {
    Object.keys(messages).forEach(lang => {
      Object.assign(messages[lang], module.messages[lang]);
    });
  });

  return messages;
}

module.exports = {
  messages: mergeMessages(),
};
