<template>
  <div style="height: 830px; background-color: white">
    <iframe
      :src="`https://nbook.kinji.cn:9001/?userId=${this.user.userName}&userName=${this.user.name}&dataSource=2`"
      width="100%"
      height="100%"
    ></iframe>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  name: "nbook",
  computed: { ...mapState("account", ["user"]) },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="less" scoped></style>
