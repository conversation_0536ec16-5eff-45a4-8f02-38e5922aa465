import { request, METHOD } from '@/utils/request'

export async function shipReportList(OrderNo) {
    return request(`/api/app/ship-report/ship-report-list?OrderNo=${OrderNo}`, METHOD.GET,)
}
export async function procardwip(params) {
    return request(`/api/app/ship-report/pro-card-wip`, METHOD.GET,params)
}
// 报告编辑保存
export async function setReportList(params) {
    return request(`/api/app/ship-report/set-report-list`, METHOD.POST,params)
}
//出货报告
export async function inspectionreport(orderno) {
    return request(`/api/app/ship-report/inspection-report?orderno=${orderno}`, METHOD.GET,)
}
//奔强出货报告
export async function importinspectionreportv2(params) {
    return request(`api/app/ship-report/import-inspection-report-v2`, METHOD.POST,params)
}
//奔强出货报告修改参数

export async function reportvalue(params) {
    return request(`api/app/ship-report/report-value`, METHOD.GET,params)
}
//奔强出货报告获取参数
export async function setreportselectvalue(OrderNo) {
    return request(`/api/app/ship-report/report-select-value?OrderNo=${OrderNo}`, METHOD.GET)
}