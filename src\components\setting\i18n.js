module.exports = {
  messages: {
    CN: {
      theme: {
        title: '整体风格设置',
        light: '亮色菜单风格',
        dark: '暗色菜单风格',
        night: '深夜模式',
        color: '主题色'
      },
      navigate: {
        title: '导航设置',
        side: '侧边导航',
        head: '顶部导航',
        mix: '混合导航',
        content: {
          title: '内容区域宽度',
          fluid: '流式',
          fixed: '定宽'
        },
        fixedHeader: '固定Header',
        fixedSideBar: '固定侧边栏',
      },
      other: {
        title: '其他设置',
        weekMode: '色弱模式',
        multiPages: '多页签模式',
        hideSetting: '隐藏设置抽屉'
      },
      animate: {
        title: '页面切换动画',
        disable: '禁用动画',
        effect: '动画效果',
        direction: '动画方向'
      },
      alert: '拷贝配置后，直接覆盖文件 src/config/config.js 中的全部内容，然后重启即可。（注意：仅会拷贝与默认配置不同的项）',
      copy: '拷贝配置',
      save: '保存配置',
      reset: '重置配置',
    },
    HK: {
      theme: {
        title: '整體風格設置',
        light: '亮色菜單風格',
        dark: '暗色菜單風格',
        night: '深夜模式',
        color: '主題色'
      },
      navigate: {
        title: '導航設置',
        side: '側邊導航',
        head: '頂部導航',
        content: {
          title: '內容區域寬度',
          fluid: '流式',
          fixed: '定寬'
        },
        fixedHeader: '固定Header',
        fixedSideBar: '固定側邊欄',
      },
      other: {
        title: '其他設置',
        weekMode: '色弱模式',
        multiPages: '多頁簽模式',
        hideSetting: '隱藏設置抽屜'
      },
      animate: {
        title: '頁面切換動畫',
        disable: '禁用動畫',
        effect: '動畫效果',
        direction: '動畫方向'
      },
      alert: '拷貝配置后，直接覆蓋文件 src/config/config.js 中的全部內容，然後重啟即可。（注意：僅會拷貝與默認配置不同的項）',
      copy: '拷貝配置',
      save: '保存配置',
      reset: '重置配置',
    },
    US: {
      theme: {
        title: 'Page Style Setting',
        light: 'Light Style',
        dark: 'Dark Style',
        night: 'Night Style',
        color: 'Theme Color'
      },
      navigate: {
        title: 'Navigation Mode',
        side: 'Side Menu Layout',
        head: 'Top Menu Layout',
        mix: 'Mix Menu Layout',
        content: {
          title: 'Content Width',
          fluid: 'Fluid',
          fixed: 'Fixed'
        },
        fixedHeader: 'Fixed Header',
        fixedSideBar: 'Fixed SideBar',
      },
      other: {
        title: 'Other Setting',
        weekMode: 'Week Mode',
        multiPages: 'Multi Pages',
        hideSetting: 'Hide Setting Drawer'
      },
      animate: {
        title: 'Page Toggle Animation',
        disable: 'Disable',
        effect: 'Effect',
        direction: 'Direction'
      },
      alert: 'After copying the configuration code, directly cover all contents in the file src/config/config.js, then restart the server. (Note: only items that are different from the default configuration will be copied)',
      copy: 'Copy Setting',
      save: 'Save',
      reset: 'Reset',
    }
  }
}
