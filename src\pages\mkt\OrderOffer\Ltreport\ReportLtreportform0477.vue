<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1" style="font-size: 16px;" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ltreport0477" style=" padding: 25px;color: black;font-family: 等线;position: relative;" >
            <div style="width: 100%;text-align: center;font-weight: bolder;font-size: 30px;">报价单</div>
            <div style="z-index: 99;position: relative">
                <table style="width: 100%;" border="1">
                <tr>
                    <td style="font-weight: bolder;">供货方:</td>                   
                    <td colspan="2">{{ LTreportdata.factory_}}</td>
                    <td style="font-weight: bolder;">需求方:</td>
                    <td colspan="2">{{ LTreportdata.facFax_}}</td>
                </tr>
                <tr>
                    <td style="font-weight: bolder;">地&nbsp;址:</td>
                    <td colspan="2">{{ LTreportdata.factoryEnglish_}}</td>
                    <td style="font-weight: bolder;">地&nbsp;址:</td>
                    <td colspan="2">{{ LTreportdata.tel_}}</td>
                </tr>
                <tr>
                    <td style="font-weight: bolder;">联系人:</td>
                    <td colspan="2">{{ LTreportdata.contract_}}</td>
                    <td style="font-weight: bolder;">联系人:</td>
                    <td colspan="2">{{ LTreportdata.fax_}}</td>
                </tr>
                <tr>
                    <td style="font-weight: bolder;">手&nbsp;机:</td>
                    <td colspan="2">{{ LTreportdata.contractEnglish_}}</td>
                    <td style="font-weight: bolder;">手&nbsp;机:</td>
                    <td colspan="2">{{ LTreportdata.eml_}}</td>
                </tr>
                <tr>
                    <td style="font-weight: bolder;">邮&nbsp;箱:</td>
                    <td colspan="2">{{ LTreportdata.factoryadd_ }}</td>
                    <td style="font-weight: bolder;">邮&nbsp;箱:</td>
                    <td colspan="2">{{ LTreportdata.http_}}</td>
                </tr>
                <tr>
                    <td>物料编码:</td>
                    <td>{{ LTreportdata.address_ }}</td>
                    <td>物料名称:</td>
                    <td>{{ LTreportdata.facPhone_}}</td>
                    <td>项目名:</td>
                    <td>{{ LTreportdata.party_ }}</td>
                </tr>
                <tr>
                    <td style="text-align: center;font-weight: bolder;font-size: 18px;">成本项</td>
                    <td style="font-weight: bolder;font-size: 18px;">明细</td>
                    <td style="font-weight: bolder;font-size: 18px;">其他变动因素</td>
                    <td style="font-weight: bolder;font-size: 18px;">输入变量</td>
                    <td style="text-align: center;font-weight: bolder;font-size: 18px;">价格</td>
                    <td>备注</td>
                </tr>
                <tr>
                    <td rowspan="9" style="text-align: center;">原材料成本</td>
                    <td rowspan="6">变量1:板材</td>
                    <td>板材型号</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.link_ }}</td>
                    <td rowspan="6" style="text-align: center;background-color:#ffc000 ;">{{LTreportdata.factoryR_}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>TG值</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.date_  }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>厚度 mm</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.orderNo_ }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>铜厚 Oz</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.pact_}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>尺寸（长*宽）mm</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO_}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>是否有卤</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO1_}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td rowspan="2">变量2:铜箔</td>
                    <td>张数</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO2_}}</td>
                    <td rowspan="2" style="text-align: center;background-color:#ffc000 ;">{{ LTreportdata.factoryAddR_ }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>铜厚 Oz</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO3_}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>变量3:PP</td>
                    <td>张数</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO4_}}</td>
                    <td style="text-align: center;background-color:#ffc000 ;">{{ LTreportdata.factorydelegate_ }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align: center;background-color: #92d050;">合计</td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="text-align: center;background-color: #92d050;">{{ LTreportdata.factorydete_ }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align: center;">制造成本</td>
                    <td>固定值</td>
                    <td></td>
                    <td></td>
                    <td style="text-align: center;background-color:#ffc000 ;">{{ LTreportdata.setBoardHeight }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align: center;background-color: #92d050;">基准单价</td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="text-align: center;background-color: #92d050;">{{ LTreportdata.boardLayers }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td rowspan="9" style="text-align: center;">特殊项目</td>
                    <td colspan="2" style="text-align: center;">表面处理工艺</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO5_ }}</td>
                    <td style="text-align: center;background-color:#ffc000">{{ LTreportdata.boardThickness }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">完成孔径</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO6_ }}</td>
                    <td style="text-align: center;background-color:#ffc000">{{ LTreportdata.copperThickness}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">孔数(万)</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO7_ }}</td>
                    <td style="text-align: center;background-color:#ffc000">{{ LTreportdata.surface }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">锣程/m</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.nO8_ }}</td>
                    <td style="text-align: center;background-color:#ffc000">{{ LTreportdata.solderColor}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">阻抗</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.pcbFileName }}</td>
                    <td style="text-align: center;background-color:#ffc000">{{ LTreportdata.isBlueGum }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">树脂塞孔</td>
                    <td style="background-color: #ffff00;">{{LTreportdata.sura}}</td>
                    <td style="text-align: center;background-color:#ffc000">{{ LTreportdata.isPasteRedTape}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">半孔板(边数)</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.currency_}}</td>
                    <td style="text-align: center;background-color:#ffc000">{{LTreportdata.isCarbonOil}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">利用率</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.meTel_}}</td>
                    <td style="text-align: center;background-color:#ffc000">{{LTreportdata.impGroupNum}}</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">不接受打叉板</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.custL_ }}</td>
                    <td style="text-align: center;background-color:#ffc000">{{ LTreportdata.setBoardWidth }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align: center;background-color: #92d050;">总价</td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="text-align: center;background-color: #92d050;">{{ LTreportdata.pinBanType }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td rowspan="3"></td>
                    <td style="text-align: center;" rowspan="2">拼版尺寸</td>
                    <td style="text-align: center;">长(mm)</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.custAddL_ }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align: center;">宽(mm)</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.custdelegate_ }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align: center;">拼版尺寸</td>
                    <td style="text-align: center;">数量</td>
                    <td style="background-color: #ffff00;">{{ LTreportdata.custdate_ }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td style="text-align: center;background-color: #92d050;">单价合计</td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="background-color: #92d050;"></td>
                    <td style="text-align: center;background-color: #92d050;">{{ LTreportdata.fR4Type }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>商务条件:</td>
                    <td colspan="2"></td>
                    <td>MOQ</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="4">说明：1.以上报价币种为人民币含税，如为其它币种请标识</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="4">&nbsp;&nbsp;&nbsp;2.报价单必须签提供签字盖章.PDF格式文件，同时提供Excel格式文件；</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="4">&nbsp;&nbsp;&nbsp;3.报价有效期:6个月，在未接到更新报价前以此份报价单价格为准</td>
                    <td></td>
                    <td></td>
                </tr>
                </table>
            </div>
            <img  src="@/assets/img/lthtz.png" 
             style="position: relative;
                    top: -100px;
                    z-index: 0;
                    display: block;
                    left: 54px;
                    width: 150px;
                    transform: rotate(353deg);"  >    
        </div>
    </div>
  </template>
  <script>
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        printObj1:{
            id: "ltreport0477", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            htmlToPdf('ltreport0477',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
    table > tr > td {
       padding:3px 5px;
       border: 1px solid black;
    }
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
  .pdfDom1{
      height: 650px;
      overflow: auto;
  }
  </style>
