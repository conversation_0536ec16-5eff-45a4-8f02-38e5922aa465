<!--  市场管理 - 订单询价 - 查询-->
<template>
  <a-form>
    <a-row>
      <a-col :span="24">
        <a-form-item label="订单号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input :autoFocus="autoFocus" ref="select1" v-model="form.OrderNo" v-focus-next-on-enter="'select2'" allowClear> </a-input>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="24">
        <a-form-item label="客户代码" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input ref="select2" v-model="form.custNo" v-focus-next-on-enter="'select3'" allowClear> </a-input>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="24">
        <a-form-item label="客户型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <a-input ref="select3" v-model="form.PcbFileName" allowClear> </a-input>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { getTradeTypeSrcList } from "@/services/projectDisptch";
export default {
  name: "QueryInfoBackend",
  data() {
    return {
      form: {
        OrderNo: "",
        custNo: "",
        PcbFileName: "",
      },
      autoFocus: true,
      tradeTypeSrcList: [],
    };
  },
  created() {
    // getTradeTypeSrcList().then(res => {
    //   this.tradeTypeSrcList = res?.data
    // });
  },
  methods: {
    //  keyupEnter1(){
    //     this.$emit('keyupEnter1')
    // }
    //  onChange(e){
    //     this.form[e.target.name] = e.target.checked
    //   }
  },
  mounted() {},
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
  //  computed:{
  //     disabled(){
  //       if (this.form.OrderNo == '') {
  //         return false
  //       } else {
  //         return true
  //       }
  //     }
  //   },
};
</script>
<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
</style>
