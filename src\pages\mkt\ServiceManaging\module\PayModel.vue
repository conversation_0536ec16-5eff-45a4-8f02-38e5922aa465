<!-- 市场管理 - 客服管理 - 完结信息-->
<template>
  <a-form-model
    :model="form"
    :rules="rules" 
    ref="ruleForm"
  >
  <a-form-model-item label="是否拒绝：" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}"  v-if="allow_to_refuse">
      <a-checkbox v-model="form.is_refuse" @change="changerefuse" :disabled="!allow_to_refuse" />
    </a-form-model-item>
    <a-form-model-item label="差价：" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}" v-if="allow_to_pay && !form.is_refuse">
      <a-input v-model="form.cost" :disabled="form.is_refuse || !allow_to_pay"  style="width:200px" type="number" placeholder="请输入差价" />
    </a-form-model-item>
    <a-form-model-item label="订单类型：" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}" v-if="Returnorder">
        <a-select v-model="form.orderType">
            <a-select-option value="1">返单无改</a-select-option>
            <a-select-option value="2">返单更改</a-select-option>
        </a-select>
    </a-form-model-item>
    <a-form-model-item label="备注信息：" :labelCol="{span: 6}" :wrapperCol="{span: 15, offset: 1}" ref="verified_detail" prop="verified_detail">
      <a-textarea :auto-size="{ minRows: 3, maxRows:5 }" v-model="form.verified_detail"  ref="input1" allowClear  placeholder="请输入备注信息" />
    </a-form-model-item>      
  </a-form-model>
</template>

<script>
export default {
    name:'PayModelservice',
    props:{
      allow_to_pay:{
        type:Boolean,
        default:false
      },
      allow_to_refuse:{
        type:Boolean,
        default:false
      },
      Returnorder:{
        type:Boolean,
        default:false
      },
      ordertype:{
        type:String,
      }
    },
  data() {
    return {   
      form:{  
        is_refuse:false,     
        cost:null,  // 差价
        verified_detail: '',    // 原因
        orderType:null,
      },
      rules: {
        verified_detail:[{required : true , message: "备注信息请必须填写", trigger: "blur"}]
      },

      
    };
  },  
  methods:{
    changerefuse(){
      if(this.form.is_refuse){
        this.form.cost = null
      }
    }
  },
  created(){
    this.form.orderType =this.ordertype?this.ordertype:null
  }
};
</script>
<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 10px;
}

</style>