.week-mode{
  overflow: hidden;
  filter: invert(80%);
}
// .ant-layout-sider-children{
//   background-color: #fff !important
// }
// .ant-menu-submenu, .ant-menu-submenu-inline{
//   color:#000000 !important
// }
// .ant-menu-dark .ant-menu-inline.ant-menu-sub{
//   background-color:#fff !important
// }
// .ant-menu-dark .ant-menu-item, .ant-menu-dark .ant-menu-item-group-title, .ant-menu-dark .ant-menu-item > a{
//   color:#000000 !important
// }
// .ant-menu-dark .ant-menu-submenu-open{
//   color:#000000 !important
// }
// .ant-menu-dark .ant-menu-submenu-title:hover{
//   color:#000000 !important
// }
// .ant-menu-dark .ant-menu-item:hover, .ant-menu-dark .ant-menu-item-active{
//   color:#000000 !important
// }
// .ant-menu-dark .ant-menu-item:hover > a{
//   color:#000000  !important
// }
.beauty-scroll{
  // scrollbar-color: @primary-color @primary-2;
  // scrollbar-width: thin;
  -ms-overflow-style:none;
  position: relative;
  height: 100%;
  &::-webkit-scrollbar{
    width: 3px;
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #c2c1c2;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 1px rgba(0,0,0,0);
    border-radius: 3px;
    background: #e7e5e7;
  }
}
.split-right{
  &:not(:last-child) {
    border-right: 1px solid rgba(98, 98, 98, 0.2);
  }
}
.disabled{
  cursor: not-allowed;
  color: @disabled-color;
  pointer-events: none;
}
