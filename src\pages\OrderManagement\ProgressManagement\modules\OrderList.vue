<!--生产管理- 生产进度-流程卡列表 -->
<template>
  <div class="contentInfo" ref="tableWrapper" style="position: relative" @click="bodyClick">
    <a-table
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      :columns="columns"
      :pagination="pagination"
      :loading="orderListTableLoading"
      :data-source="orderListData"
      :customRow="onClickRow"
      :rowClassName="isRedRow"
      @change="handleTableChange"
      :scroll="{ x: 4500, y: 667 }"
    >
      <span slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </span>
      <div slot="cardNo" slot-scope="record">
        <span :title="record.cardNo" style="color: #428bca" @click.stop="cardNoClick(record)">{{ record.cardNo }} </span>&nbsp;
        <span class="tagNum" style="display: inline-block">
          <span
            title="加急"
            v-if="record.isUrgent"
            style="
              font-size: 14px;
              font-weight: 500;
              color: #ff9900;
              padding: 0 2px;
              margin: 0;
              display: inline-block;
              height: 19px;
              width: 14px;
              margin-right: 4px;
              margin-left: -10px;
              user-select: none;
            "
          >
            <a-icon type="thunderbolt" theme="filled"></a-icon>
          </span>
          <a-tag
            v-if="record.isFeeded"
            title="补料"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            补
          </a-tag>
          <a-tag
            v-if="record.isStop"
            title="暂停"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            停
          </a-tag>

          <a-tag
            v-if="record.isLock"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            锁
          </a-tag>

          <a-tag
            v-if="record.smtFactoryId > 0"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            贴
          </a-tag>
          <a-tag
            v-if="record.confirmWorkingDraft"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            确
          </a-tag>
          <a-tag
            v-if="record.isBigCus"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            KA
          </a-tag>
          <a-tag
            v-if="record.isJiaji"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            急
          </a-tag>
          <a-tag
            v-if="record.isReverse == '1' || record.isReverse == '2'"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            反
          </a-tag>
        </span>
      </div>
      <template slot="orderNo" slot-scope="record">
        <a style="color: #428bca" @click.stop="details(record)">{{ record.orderNo }} </a>
      </template>
      <template slot="isPrint" slot-scope="record">
        <span v-if="record.isPrint"> 已打印 </span>
        <span style="color: red" v-else> 未打印 </span>
      </template>
      <template slot="online" slot-scope="record">
        <p>{{ record.currentStepName }}</p>
      </template>
      <template slot="mask" slot-scope="record">
        <div
          v-if="record.solderColorStr.indexOf('绿') != -1"
          style="width: 13px; height: 13px; background: green; display: inline-block; border: 1px solid black"
        ></div>
        <div
          v-if="record.solderColorStr.indexOf('红') != -1"
          style="width: 13px; height: 13px; background: red; display: inline-block; border: 1px solid black"
        ></div>
        <div
          v-if="record.solderColorStr.indexOf('白') != -1"
          style="width: 13px; height: 13px; background: white; display: inline-block; border: 1px solid black"
        ></div>
        <div
          v-if="record.solderColorStr.indexOf('黑') != -1"
          style="width: 13px; height: 13px; background: black; display: inline-block; border: 1px solid black"
        ></div>
        <div
          v-if="record.solderColorStr.indexOf('黄') != -1"
          style="width: 13px; height: 13px; background: yellow; display: inline-block; border: 1px solid black"
        ></div>
        <div
          v-if="record.solderColorStr.indexOf('蓝') != -1"
          style="width: 13px; height: 13px; background: blue; display: inline-block; border: 1px solid black"
        ></div>
        <a-icon v-if="record.solderColorStr == 'none'" type="close-square" style="color: #d1cbcb"></a-icon>
        {{ record.solderColorStr }}
      </template>
      <template slot="feeding" slot-scope="record">
        <span>{{ record.currentNum }} / {{ record.currentStepCount }} </span>
      </template>
      <template slot="opening" slot-scope="record">
        <span>{{ record.klCreateTime }} </span>
        <span v-if="record.klTimeSpan">{{ record.klTimeSpan }}分钟 </span>
      </template>
      <template slot="innerline" slot-scope="record">
        <span>{{ record.ncxltmCreateTime }} </span>
        <span v-if="record.ncxltmTimeSpan">{{ record.ncxltmTimeSpan }}分钟 </span>
      </template>
      <template slot="innercheck" slot-scope="record">
        <span>{{ record.ncxljyCreateTime }} </span>
        <span v-if="record.ncxljyTimeSpan">{{ record.ncxljyTimeSpan }}分钟</span>
      </template>
      <template slot="inetching" slot-scope="record">
        <span>{{ record.ncskCreateTime }} </span>
        <span v-if="record.ncskTimeSpan">{{ record.ncskTimeSpan }}分钟</span>
      </template>
      <template slot="inetchingch" slot-scope="record">
        <span>{{ record.ncskjyCreateTime }} </span>
        <span v-if="record.ncskjyTimeSpan">{{ record.ncskjyTimeSpan }}分钟</span>
      </template>
      <template slot="pressing" slot-scope="record">
        <span>{{ record.yhCreateTime }} </span>
        <span v-if="record.yhTimeSpan">{{ record.yhTimeSpan }}分钟</span>
      </template>
      <template slot="drill" slot-scope="record">
        <span>{{ record.yczkCreateTime }} </span>
        <span v-if="record.yczkTimeSpan">{{ record.yczkTimeSpan }}分钟</span>
      </template>
      <template slot="pth" slot-scope="record">
        <span>{{ record.ctCreateTime }} </span>
        <span v-if="record.ctTimeSpan">{{ record.ctTimeSpan }}分钟</span>
      </template>
      <template slot="electroplate" slot-scope="record">
        <span>{{ record.qbddCreateTime }} </span>
        <span v-if="record.qbddTimeSpan">{{ record.qbddTimeSpan }}分钟</span>
      </template>
      <template slot="dryfilm" slot-scope="record">
        <span>{{ record.gmtmCreateTime }} </span>
        <span v-if="record.gmtmTimeSpan">{{ record.gmtmTimeSpan }}分钟</span>
      </template>
      <template slot="drychi" slot-scope="record">
        <span>{{ record.gmxljyCreateTime }} </span>
        <span v-if="record.gmxljyTimeSpan">{{ record.gmxljyTimeSpan }}分钟</span>
      </template>
      <template slot="outetching" slot-scope="record">
        <span>{{ record.skCreateTime }} </span>
        <span v-if="record.skTimeSpan">{{ record.skTimeSpan }}分钟</span>
      </template>
      <template slot="etchch" slot-scope="record">
        <span>{{ record.skxljyCreateTime }} </span>
        <span v-if="record.skxljyTimeSpan">{{ record.skxljyTimeSpan }}分钟</span>
      </template>
      <template slot="soldermask" slot-scope="record">
        <span>{{ record.fhCreateTime }} </span>
        <span v-if="record.fhTimeSpan">{{ record.fhTimeSpan }}分钟</span>
      </template>
      <template slot="soldermaskch" slot-scope="record">
        <span>{{ record.fhdwjyCreateTime }} </span>
        <span v-if="record.fhdwjyTimeSpan">{{ record.fhdwjyTimeSpan }}分钟</span>
      </template>
      <template slot="character" slot-scope="record">
        <span>{{ record.wzCreateTime }} </span>
        <span v-if="record.wzTimeSpan">{{ record.wzTimeSpan }}分钟</span>
      </template>
      <template slot="surface" slot-scope="record">
        <span>{{ record.bmclCreateTime }} </span>
        <span v-if="record.bmclTimeSpan">{{ record.bmclTimeSpan }}分钟</span>
      </template>
      <template slot="test" slot-scope="record">
        <span>{{ record.csCreateTime }} </span>
        <span v-if="record.csTimeSpan">{{ record.csTimeSpan }}分钟</span>
      </template>
      <template slot="wxTime" slot-scope="record">
        <span>{{ record.wxCreateTime }} </span>
        <span v-if="record.wxTimeSpan">{{ record.wxTimeSpan }}分钟</span>
      </template>
      <template slot="testrack" slot-scope="record">
        <span>{{ record.csjCreateTime }} </span>
        <span v-if="record.csjTimeSpan">{{ record.csjTimeSpan }}分钟</span>
      </template>
      <template slot="fqc" slot-scope="record">
        <span>{{ record.zjbzCreateTime }} </span>
        <span v-if="record.zjbzTimeSpan">{{ record.zjbzTimeSpan }}分钟</span>
      </template>
    </a-table>
    <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
      <a-menu-item @click="down11" v-if="showText">复制</a-menu-item>
    </a-menu>
  </div>
</template>

<script>
//获取接口方法
import $ from "jquery";
//数据模型
const columns = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 50,
    fixed: "left",
  },
  {
    title: "管制卡编号",
    align: "left",
    scopedSlots: { customRender: "cardNo" },
    width: 170,
    fixed: "left",
    ellipsis: true,
  },
  {
    title: "拼版编号",
    align: "left",
    width: 140,
    scopedSlots: { customRender: "orderNo" },
    fixed: "left",
    ellipsis: true,
  },
  {
    title: "逾期天数",
    align: "center",
    width: 100,
    dataIndex: "overdueDays",
    ellipsis: true,
  },
  {
    title: "在线工序",
    align: "center",
    width: 100,
    scopedSlots: { customRender: "online" },
    ellipsis: true,
    customCell: record => {
      if (record.currentStatus == 1) {
        return { style: { background: "#cb0000", color: "#fff" } };
      }
      if (record.currentStatus == 2) {
        return { style: { background: "#ffff00", color: "#fff" } };
      } else {
        return { style: { background: "#008000", color: "#fff" } };
      }
    },
  },
  {
    title: "过序时间",
    align: "center",
    width: 135,
    ellipsis: true,
    dataIndex: "currentTime",
  },
  {
    title: "数量",
    align: "center",
    width: 80,
    ellipsis: true,
    dataIndex: "currentNum",
  },
  {
    title: "订单(U)",
    align: "center",
    width: 80,
    ellipsis: true,
    dataIndex: "orderNum",
  },
  {
    title: "本卡(U)",
    align: "center",
    width: 80,
    ellipsis: true,
    dataIndex: "orderOkNum",
  },
  {
    title: "交货时间",
    align: "center",
    width: 105,
    dataIndex: "deliveryDate",
    ellipsis: true,
  },
  {
    title: "投料时间",
    align: "center",
    width: 135,
    ellipsis: true,
    dataIndex: "createTime",
  },
  // {
  //   title: "板材领料申请",
  //   align: "center",
  //   width: 130,
  //   className:'userStyle',
  //   scopedSlots: { customRender: 'picking' },
  //   ellipsis: true,
  // },
  {
    title: "打印",
    align: "center",
    width: 105,
    scopedSlots: { customRender: "isPrint" },
    ellipsis: true,
  },
  {
    title: "工厂",
    align: "center",
    width: 105,
    dataIndex: "joinFactoryCode",
    ellipsis: true,
  },

  {
    title: "加急类型",
    align: "center",
    width: 100,
    dataIndex: "deliveryType",
    ellipsis: true,
  },
  {
    title: "清理时间",
    align: "center",
    width: 130,
    ellipsis: true,
    dataIndex: "clearTime",
  },
  {
    title: "子订单数",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "orderCount",
  },
  {
    title: "材料",
    align: "center",
    width: 160,
    ellipsis: true,
    dataIndex: "fR4TypeStr",
  },
  {
    title: "板厚",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "boardThickness",
  },
  {
    title: "层数",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "boardLayers",
  },
  {
    title: "拼板尺寸",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "pinBanSize",
  },
  {
    title: "订单类型",
    align: "center",
    width: 80,
    ellipsis: true,
    dataIndex: "orderTypeStr",
  },
  {
    title: "面积",
    align: "center",
    width: 80,
    ellipsis: true,
    dataIndex: "cardArea",
  },
  {
    title: "阻焊油墨",
    align: "center",
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: "mask" },
  },
  {
    title: "表面工艺",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "surfaceFinishStr",
  },
  {
    title: "过数/投料",
    align: "center",
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: "feeding" },
  },
  {
    title: "总耗时",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "totalTimeOut",
  },
  {
    title: "投料人",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "adminName",
  },
  {
    title: "备注",
    align: "center",
    width: 100,
    ellipsis: true,
    dataIndex: "remark",
  },
  {
    title: "开料",
    align: "center",
    width: 200,
    ellipsis: true,
    // className:'tdStyle',

    scopedSlots: { customRender: "opening" },
  },
  {
    title: "内层线路贴膜",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "innerline" },
  },
  {
    title: "内层线路检查",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "innercheck" },
  },
  {
    title: "内层蚀刻",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "inetching" },
  },
  {
    title: "内层蚀刻检查",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "inetchingch" },
  },
  {
    title: "压合",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "pressing" },
  },
  {
    title: "钻孔",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "drill" },
  },
  {
    title: "沉铜",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "pth" },
  },
  {
    title: "全板电镀",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "electroplate" },
  },
  {
    title: "干膜贴膜",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "dryfilm" },
  },
  {
    title: "干膜线路检查",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "drychi" },
  },
  {
    title: "蚀刻",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "outetching" },
  },
  {
    title: "蚀刻检查",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "etchch" },
  },
  {
    title: "防焊",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "soldermask" },
  },
  {
    title: "防焊对位检验",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "soldermaskch" },
  },
  {
    title: "文字",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "character" },
  },
  {
    title: "表面处理",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "surface" },
  },
  {
    title: "测试",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "test" },
  },
  {
    title: "外形",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "wxTime" },
  },
  {
    title: "测试架",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "testrack" },
  },
  {
    title: "终检包装",
    align: "center",
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: "fqc" },
  },
];

export default {
  name: "OrderList",
  //获取子组件信息
  props: ["orderListData", "pagination", "orderListTableLoading", "rowKey", "params1"],

  data() {
    return {
      showText: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      columns,
      id: "",
      selectedRowKeysArray: [],
      selectedRowsData: {},
    };
  },
  computed: {
    ozValue() {
      let arr_ = [];
      for (var i = 0; i < this.formData.boardLayers; i++) {
        if (i == 0 || i == this.formData.boardLayers - 1) {
          arr_.push(this.formData.copperThickness);
        } else {
          arr_.push(this.formData.innerCopperThickness);
        }
      }
      return arr_.join("/");
    },
  },
  watch: {
    pagination: {
      handler(val) {
        console.log(val);
      },
    },
    orderListData: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0) {
              this.columns[1].width = "160px";
              this.columns[2].width = "140px";
              this.columns[3].width = "100px";
              this.columns[4].width = "100px";
              this.columns[5].width = "135px";
              this.columns[6].width = "80px";
              this.columns[7].width = "80px";
              this.columns[8].width = "80px";
              this.columns[9].width = "105px";
              this.columns[10].width = "135px";
              this.columns[11].width = "105px";
              this.columns[12].width = "105px";
              this.columns[13].width = "100px";
            }
            if (result >= 1) {
              this.columns[1].width = 160 + result * 20 + "px";
              this.columns[2].width = 140 - result * 3 + "px";
              this.columns[3].width = 100 - result * 2 + "px";
              this.columns[4].width = 100 - result * 1 + "px";
              this.columns[5].width = 135 - result * 1 + "px";
              this.columns[6].width = 80 - result * 2 + "px";
              this.columns[7].width = 80 - result * 2 + "px";
              this.columns[8].width = 80 - result * 2 + "px";
              this.columns[9].width = 105 - result * 2 + "px";
              this.columns[10].width = 135 - result * 2 + "px";
              this.columns[11].width = 105 - result * 1 + "px";
              this.columns[12].width = 105 - result * 1 + "px";
              this.columns[13].width = 100 - result * 1 + "px";
            }
          });
        }
      },
    },
  },
  mounted() {
    // window.addEventListener("mousedown",this.bodyClick)
  },
  methods: {
    handleTableChange(pagination) {
      this.$emit("tableChange", pagination);
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.id && record.id == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.id = record.id;
          },
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record);
          },
        },
      };
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      };
      document.body.removeEventListener("click", this.bodyClick);
    },
    down11() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    rightClick1(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode.parentNode;
      }
      if (e.target.localName == "path" || e.target.localName == "svg") {
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode;
        if (event.className.indexOf("tagNum") != -1) {
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
        }
      }
      if (e.target.parentNode.localName == "div") {
        event = e.target.parentNode.parentNode;
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split(" ")[0];
      }
      console.log("this.text", this.text);
      const tableWrapper = this.$refs.tableWrapper;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;
      let offsetx = event.offsetLeft + event.offsetWidth - 10;
      let offsety = event.offsetTop + 40;
      if (event.cellIndex == this.columns.length - 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }
      // if(event.cellIndex == 0 || event.cellIndex == 1){
      //   this.menuStyle.top = offsety - 36 + "px";
      //   this.menuStyle.left = offsetx  + "px";
      // }
      document.body.addEventListener("click", this.bodyClick);
    },
    // 详情跳转
    details(record) {
      localStorage.setItem("scpageCurrent", this.pagination.current);
      localStorage.setItem("scpageSize", this.pagination.pageSize);
      localStorage.setItem("scid", record.id);
      localStorage.setItem("screcord", JSON.stringify(record));
      localStorage.setItem("scstat", true);
      localStorage.setItem("scconciliation", JSON.stringify(this.params1));
      this.$router.push({ path: "orderDetail1", query: { id: record.orderNo, businessOrderNo: record.businessOrderNo, ttype: 2 } });
    },
    // 管制卡号点击
    cardNoClick(record) {
      this.$emit("cardNoClick", record);
    },
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox {
  li {
    height: 30px;
    line-height: 30px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-tag {
  font-size: 14px;
  color: #ff9900;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
}
.contentInfo {
  /deep/ p {
    margin-bottom: 0 !important;
  }
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 32px;
          .ant-select {
            width: 120px;
          }
          .ant-input {
            width: 120px;
          }
          .ant-input-number {
            width: 120px;
          }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          label {
            font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          }
        }
        .ant-form-item-control-wrapper {
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 8px 10px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
