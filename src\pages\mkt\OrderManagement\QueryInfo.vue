<!-- 市场管理 - 订单管理- 查询 -->
<template>
  <div ref="SelectBox">
    <a-form>
      <a-row>
        <a-col :span="24">
          <a-form-item label="订单号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input :autoFocus="autoFocus" ref="select1" v-model="form.OrderNo" v-focus-next-on-enter="'select2'" allowClear> </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="客户代码" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input ref="select2" v-model="form.custNo" v-focus-next-on-enter="'select3'" allowClear> </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="客户型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input ref="select3" v-model="form.PcbFileName" v-focus-next-on-enter="'select4'" allowClear> </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="生产型号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input ref="select4" v-model="form.proOrderNo" v-focus-next-on-enter="'select5'" allowClear> </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="客户订单号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input ref="select5" v-model="form.custPo" v-focus-next-on-enter="'select6'" allowClear> </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="订单状态" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-select
              ref="select6"
              v-model="form.Status"
              showSearch
              allowClear
              optionFilterProp="lable"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(ite, index) in list" :key="index" :value="ite.value" :lable="ite.text">{{ ite.text }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="订单类型" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-select v-model="form.ReOrder">
              <a-select-option value="0">新单 </a-select-option>
              <a-select-option value="1">返单 </a-select-option>
              <a-select-option value="2">返单更改 </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="开始时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-date-picker format="YYYY-MM-DD" @change="StartTime"></a-date-picker>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="结束时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-date-picker format="YYYY-MM-DD" @change="EndTime" :disabled="form.StartTime ? false : true"></a-date-picker>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item label="合同号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input v-model="form.contractNo" v-focus-next-on-enter="'input5'" ref="input4" allowClear />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item label="建立人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input v-model="form.createName" allowClear />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item label="客户物料号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
            <a-input v-model="form.customerMaterialNo" allowClear />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- <a-row>
      <a-col :span='24'>
          <a-form-model-item label="客户型号" prop="tradeTypeSrc" :label-col="{ span: 6}" :wrapper-col="{ span: 16 }" style="width:100%; margin:0">
                <a-select v-model="form.tradeTypeSrc"  placeholder="请选择" showSearch optionFilterProp="label">
                    <a-select-option  v-for=" item in tradeTypeSrcList" :key="item.valueMember" :value="item.valueMember" :label="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
            </a-form-model-item>
        </a-col>
    </a-row> -->
    </a-form>
  </div>
</template>

<script>
import { getTradeTypeSrcList } from "@/services/projectDisptch";
export default {
  name: "QueryInfoBackend",
  data() {
    return {
      form: {
        OrderNo: "",
        custNo: "",
        PcbFileName: "",
        proOrderNo: "",
        ReOrder: "",
        Status: "",
        StartTime: null,
        EndTime: null,
        custPo: "",
        contractNo: "",
      },
      autoFocus: true,
      tradeTypeSrcList: [],
      list: [
        { value: "0", text: "待确认" },
        { value: "1", text: "待分派" },
        { value: "5", text: "待预审" },
        { value: "10", text: "预审中" },
        { value: "12", text: "已问客" },
        { value: "15", text: "待报价" },
        { value: "20", text: "审核中" },
        { value: "23", text: "待审核" },
        { value: "25", text: "待下线" },
        { value: "30", text: "已完成" },
        { value: "40", text: "取消" },
      ],
    };
  },
  created() {
    // getTradeTypeSrcList().then(res => {
    //   this.tradeTypeSrcList = res?.data
    // });
  },
  methods: {
    StartTime(value, dateString) {
      this.form.StartTime = dateString;
    },
    EndTime(value, dateString) {
      this.form.EndTime = dateString;
    },
    //  keyupEnter1(){
    //     this.$emit('keyupEnter1')
    // }
    //  onChange(e){
    //     this.form[e.target.name] = e.target.checked
    //   }
  },
  mounted() {},
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
  //  computed:{
  //     disabled(){
  //       if (this.form.OrderNo == '') {
  //         return false
  //       } else {
  //         return true
  //       }
  //     }
  //   },
};
</script>
<style lang="less" scoped>
/deep/.ant-calendar-picker {
  width: 241px !important;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}
</style>
