<template>
  <div>
  <a-dropdown>
    <div class="header-avatar" style="cursor: pointer">
      <a-avatar class="avatar" size="small" shape="circle" style="background: #A6DBFF;" :src="require('@/assets/icon/user.png')"/>     
      <span class="name">{{user.tenantName?user.tenantName+'/':''}}{{user.userName}}</span>
    </div>
    <a-menu :class="['avatar-menu']" slot="overlay">
      <a-menu-item @click="ChangePasswordClick">
        <a-icon type="user" />
        <span>修改密码</span>
      </a-menu-item>
      <a-menu-item @click="wechatClick1">
        <a-icon type="wechat" />
        <span>微信授权</span>
      </a-menu-item>
      <!-- <a-menu-item @click="goERP">
        <a-icon type="user" />
        <span>ERP</span>
      </a-menu-item> -->
      <!-- <a-menu-item>
        <a-icon type="setting" />
        <span>设置</span>
      </a-menu-item> -->
      <a-menu-divider />
      <a-menu-item @click="logout">
        <a-icon style="margin-right: 8px;" type="poweroff" />
        <span>退出登录</span>
      </a-menu-item>
    </a-menu><!--    修改密码弹窗-->
  </a-dropdown>
    <a-modal
        title="修改密码"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
    >
      <change-password ref='ChangePassword' />
    </a-modal>
    <a-modal
        title="微信授权绑定"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        centered
        destroyOnClose
        :maskClosable="false"
        :width="400"
    >
      <div id="weixin" class="qrcode" style="height:280px;padding-left:20px;"> </div>
    </a-modal>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {logout,getErp} from '@/services/user'
import ChangePassword from '@/layouts/header/ChangePassword'
import {newPassword} from "@/services/projectDisptch";
import QRCode from 'qrcodejs2';
import Cookie from 'js-cookie';
export default {
  name: 'HeaderAvatar',
  computed: {
    ...mapGetters('account', ['user']),
  },
  components:{ChangePassword,},
  data(){
    return{
      dataVisible:false,
      dataVisible1:false,
      qrcode: null,
      text:'',
    }
  },
  mounted() {
    const code = (new URLSearchParams(window.location.search)).get('code'); 
    if (code) {
      const appid = 'YOUR_APP_ID';
      const requestUrl = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appid}&secret=YOUR_APP_SECRET&code=${code}&grant_type=authorization_code`; 
      fetch(requestUrl)
        .then(response => response.json())
        .then(data => {
          if (data && data.openid) {
            const openid = data.openid;
            // 在这里可以存储或使用用户的openid
            // ...
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
  },
  methods: {
    logout() {
      logout()
      this.$router.push('/login')
    },
    goERP(){
      getErp().then(res=>{
        console.log(res)
        window.open(res.message, '_blank');
      })
    },
    // 修改密码
    ChangePasswordClick(){
      this.dataVisible = true
    },
    reportHandleCancel(){
      this.dataVisible = false
      this.dataVisible1=false
    },
    handleOk(){
      let params = this.$refs.ChangePassword.newNumber
      newPassword(params).then(res=> {
        if (res) {
          this.$message.success('密码修改成功')
        }
      }).finally(()=>{
        this.dataVisible = false
      })

    },
    wechatClick1(){
      this.dataVisible1=true
      let userName = Cookie.get('userName')
      this.text = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxd2228bd81b55231a&redirect_uri=http%3A%2F%2Fems.bninfo.com%2FloginICam%2Findex.html%3Fuserneme%3D'+userName+'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
      console.log('this.text',this.text)
      this.$nextTick(()=>{  
          // 微信登录第一步：申请微信登录二维码
        let _this = this;
        new WxLogin({
            id: "weixin",
            appid: "wx564945107027ca92",  // 这个appid要填死
            scope: "snsapi_login",
            // 扫码成功后重定向的接口
           // redirect_uri: 'http://emsapi.bninfo.com/api/app/e-mSUser/wei-xin-url',
            redirect_uri: 'http://ems.bninfo.com/callback',
            self_redirect:true,
            // state填写编码后的url
            //state: encodeURIComponent(window.btoa("http://127.0.0.1:8080" + _this.$route.path)),
            // 调用样式文件
            href: "data:text/css;base64,LmltcG93ZXJCb3ggLnRpdGxle2Rpc3BsYXk6IG5vbmU7fQouaW1wb3dlckJveCAucXJjb2RlIHsgd2lkdGg6IDIwMHB4OyBtYXJnaW4tdG9wOiAxNXB4OyBib3JkZXI6IDFweCBzb2xpZCAjZTJlMmUyO30KLmltcG93ZXJCb3ggLnN0YXR1cy5zdGF0dXNfYnJvd3NlciAgcDpudGgtY2hpbGQoMil7ZGlzcGxheTogbm9uZTt9Cg==",
        });    
        console.log('WxLogin',WxLogin)  
      })     
    },
    wechatClick(){
      this.dataVisible1 = true 
      this.$nextTick(()=>{
        
        this.tt()   
      })    
      
    },
    tt(){
      if (this.qrcode!=null) {
        this.qrcode.clear() // 清除原来的二维码
      }     
      let userName = Cookie.get('userName')
      //this.text = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxd2228bd81b55231a&redirect_uri=http%3A%2F%2Fems.bninfo.com%2FloginICam%2Findex.html%3Fuserneme%3D'+userName+'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
     this.text ='https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxd2228bd81b55231a&redirect_uri=http%3A%2F%2Fems.bninfo.com%2Foauth.html%26userneme%3D'+userName+'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
     let _this = this;
        new WxLogin({
            id: "weixin",
            appid: "wx564945107027ca92",  // 这个appid要填死
            scope: "snsapi_login",
            // 扫码成功后重定向的接口
            redirect_uri: 'http://ems.bninfo.com/callback.html',
            // state填写编码后的url
            // state: encodeURIComponent(window.btoa("http://127.0.0.1:8080" + _this.$route.path)),
            // 调用样式文件
            href: "data:text/css;base64,LmltcG93ZXJCb3ggLnRpdGxle2Rpc3BsYXk6IG5vbmU7fQouaW1wb3dlckJveCAucXJjb2RlIHsgd2lkdGg6IDIwMHB4OyBtYXJnaW4tdG9wOiAxNXB4OyBib3JkZXI6IDFweCBzb2xpZCAjZTJlMmUyO30KLmltcG93ZXJCb3ggLnN0YXR1cy5zdGF0dXNfYnJvd3NlciAgcDpudGgtY2hpbGQoMil7ZGlzcGxheTogbm9uZTt9Cg==",
        });    
        console.log('WxLogin',WxLogin)  
    },
    handleOk1(){
      this.dataVisible1=false
    },
  }
}
</script>

<style lang="less">
.qrcode iframe{
  height:280px;
  padding-left:0;
  position: absolute;
  left:50%;
  top:50%;
  transform: translate(-50%, -50%);
}
  .header-avatar{
    display: inline-flex;
    .avatar, .name{
      align-self: center;
    }
    .avatar{
      margin-right: 8px;
    }
    .name{
      font-weight: 500;
      // color:#000000;
    }
  }
  .avatar-menu{
    width: 150px;
  }

</style>
