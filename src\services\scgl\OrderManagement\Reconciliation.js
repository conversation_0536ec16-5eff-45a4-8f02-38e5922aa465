import { request, METHOD } from '@/utils/request'
// 获取订单列表
export async function proPinBanOrderList(params) {
    return request("/api/app/reconciliation/pro-pin-ban-order-balance-list", METHOD.GET, params)
}
// 账单导出
export async function exportbalance(params) {
    return request("/api/app/reconciliation/export-balance", METHOD.GET,params)
}
// 协同确认
export async function batchsynaudit(ids) {
    return request("/api/app/reconciliation/batch-syn-audit", METHOD.POST,ids)
}
// 退回协同
export async function backpropinban(ids) {
    return request("/api/app/reconciliation/reconcile-back-pro-pin-ban", METHOD.POST,ids)
}
//获取价格结果
export async function pricedetaillist(Id) {
    return request(`/api/app/reconciliation/balance-price-detail-list/${Id}`, METHOD.GET)
}
//获取价格明细
export async function orderpricedetaillist(Id,Ids,ID) {
    return request(`/api/app/reconciliation/order-price-calc-result-detail-list/${ID}?Guid4Parameter=${Id}&CalcNameID=${Ids}`, METHOD.GET)
}



export default {
    proPinBanOrderList,
    exportbalance,
    batchsynaudit,
    backpropinban,
    pricedetaillist,
    orderpricedetaillist,
}