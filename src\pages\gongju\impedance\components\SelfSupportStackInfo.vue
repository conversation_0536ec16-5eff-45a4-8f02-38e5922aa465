<!-- 工具管理- 叠层阻抗-调出模板 -->
<template>
  <div class='table'>
<!--    <a-form style="width:100%;" >-->
<!--      <a-row>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item label="板材型号" :label-col="{span:8}"  :wrapper-col="{span:14}" >-->
<!--            <a-select-->
<!--              v-model="form.coretype"-->
<!--              show-search-->
<!--              >-->
<!--            <a-select-option  v-for="item in boardTypeList" :key="item.text" :value="item.valueMember" >-->
<!--                {{item.text}}-->
<!--              </a-select-option>-->
<!--            </a-select>-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item label="PP 型号"  :label-col="{span:8}"  :wrapper-col="{span:14}">-->
<!--            <a-select  v-model="form.pptype">-->
<!--              <a-select-option  v-for="item in ppTypeList" :key="item.text" :value="item.valueMember">-->
<!--                {{item.text}}-->
<!--              </a-select-option>-->
<!--            </a-select>-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item label="内层铜厚" :label-col="{span:8}"  :wrapper-col="{span:14}" >-->
<!--            <a-select v-model="form.InCopperThickness">-->
<!--              <a-select-option v-for="item in cuListData" :key="item.valueMember" :value="item.valueMember" >{{item.text}}</a-select-option>-->
<!--            </a-select>-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item label="外层铜厚" :label-col="{span:8}"  :wrapper-col="{span:14}" >-->
<!--            <a-select v-model="form.OutCopperThickness">-->
<!--              <a-select-option v-for="item in cuListData" :key="item.valueMember" :value="item.valueMember" >{{item.text}}</a-select-option>-->
<!--            </a-select>-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--      </a-row>-->
<!--      <a-row>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item label="板厚" :label-col="{span:8}"  :wrapper-col="{span:14}">-->
<!--            <a-input v-model="form.BoardThickness">-->
<!--            </a-input>-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item label="层数" :label-col="{span:8}"  :wrapper-col="{span:14}">-->
<!--            <a-input-number  v-model="form.LayerCount" :min="0" :precision="0" style="width: 100%"/>-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--        <a-col :span="6">-->
<!--          <a-form-item  >-->
<!--            <a-button type="primary" @click="searchClick">搜索</a-button>-->
<!--          </a-form-item>-->
<!--        </a-col>-->
<!--      </a-row>-->
<!--    </a-form>-->
    <div style="display: flex">
    <div class='leftBox'>
      <p style='text-align:center;border: 1px solid #d5cdcd;margin-bottom: 0;background: #FAFAFA;color: black;'>文件层信息</p>
      <a-table
          :columns="columns1"
          bordered
          :loading="tableLoading"
          rowKey="id"
          :pagination="false"
          :data-source="stackListData1"
          :customRow="onClickRow"
          :orderListTableLoading="orderListTableLoading"
          :row-selection="{
            selectedRowKeys: selectedRowKeysArray,
            onChange: onSelectChange,
            type: 'radio',
          }"
      >
        <!-- <template slot="isChange" slot-scope="text,record">
          <a-checkbox v-model="record.isChange"/>
        </template> -->
      </a-table>
    </div>
    <div class='rightBox'>
      <p style='text-align:center;border: 1px solid #d5cdcd;margin-bottom: 0;background: #FAFAFA;color: black'>叠层剖面结构图</p>
      <div v-for='(item,index) in imgData' :key='index' style="margin-top: 10px;margin-left: 40px;">
        <div v-if="item.indexOf('oz') != -1" class="lineSty" style="background:#F4A460;"><span class="fontSty">铜箔({{item}})</span></div>
        <div v-if="item.indexOf('*') != -1" class="lineSty" style="background:#228B22;"><span class="fontSty" >{{item}}</span> </div>
        <div v-if="item.indexOf('mm') != -1" class="lineSty" style="background:#F0E68C;height:20px;"><span class="fontSty" style="margin-top: -2px;">芯板({{item}})</span> </div>

      </div>
    </div>
    </div>

  </div>
</template>

<script>

import { TemplateList} from "@/services/impedance";

const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: 30,
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "方案名",
    dataIndex: "name",
    width: 180,
    ellipsis: true,
    align: "left",

  },
  {
    title: "参数信息",
    dataIndex: "paramInfo",
    width: 180,
    ellipsis: true,
    align: "left",
  },
  // {
  //   title: "当前方案",
  //   dataIndex: "isChange",
  //   scopedSlots: { customRender: 'isChange' },
  //   align: "left",
  // },
]
export default {
    name:'SelfSupportStackInfo',
    props:['stackListData','boardTypeList','ppTypeList','templateForm','cuListData'],
  created(){
    // console.log('stackListData:',this.stackListData)
    this.form.LayerCount = this.stackListData.layers
    this.form.BoardThickness = this.stackListData.finishBoardThickness
    this.form.coretype = this.stackListData.coretype
    this.form.pptype = this.stackListData.pptype
    this.form.InCopperThickness = this.stackListData.InCopperThickness
    this.form.OutCopperThickness = this.stackListData.OutCopperThickness
//     let arr_ = this.stackListData.find(item => {return item.isChange == true})
//     if(arr_) {
//       this.selectedRowKeysArray.push(arr_.id);
//       this.imgData=arr_.paramInfo.split(',')
//     }
//       let arr = this.stackListData.find(item =>{return item.isSpecistack==true})
//       if(arr){
//         this.isSpecistack = true
//       }else{
//         this.isSpecistack = false
//       }
  },
  data() {
    return {
      columns1,
      isSpecistack: false,
      orderListTableLoading:false,
      orderListTableLoading1:false,
      imgData:[],
      selectedRowKeysArray:[],
      selectedRows:{},
      paramInfo:{},
      stackListData1:[],
      form:{
        "LayerCount": 0,
        "BoardThickness": 0,
        "InCopperThickness": 0,
        "OutCopperThickness": 0,
        "coretype":0,
        "pptype":0,
      },
      tableLoading:false,
    };
  },

  methods: {
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            var str = record.paramInfo
            this.paramInfo =  record.paramInfo
            this.imgData = str.split(',')
            console.log('点this.paramInfo',this.paramInfo)
          },
        }
      }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysArray = selectedRowKeys;
      this.selectedRows = selectedRows
      this.paramInfo = this.selectedRows[0].paramInfo
      console.log('选',this.paramInfo)
      this.imgData = this.selectedRows[0].paramInfo.split(',')
    },
    // 搜索按钮
    searchClick(){
      let params = this.form
      // if(params.coretype == '' ||params.pptype == '' ||params.InCopperThickness == '' ||params.OutCopperThickness == '' ||params.BoardThickness == '' ||params.LayerCount == '' ){
      //   this.$message.warning('请选择完整参数信息')
      //   return
      // }
      if(!params.InCopperThickness){
        params.InCopperThickness = 0
      }
      if(!params.OutCopperThickness){
        params.OutCopperThickness = 0
      }
      this.tableLoading = true
      TemplateList(params).then(res =>{
        if(res.code){
          this.stackListData1 = res.data
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.tableLoading = false
      })
    },

  },
  mounted() {
      this.searchClick()
  }

};
</script>
<style scoped lang='less'>
.ant-modal-body{
  padding: 0;
}
.table{
/deep/  .leftBox{
  .rowBackgroundColor {
    td{
      background: #aba5a5!important;
    }
  }
    width:50%;
    .ant-table-thead {
      tr{
        th{
          padding: 0;
        }
      }
    }
    .ant-form-item{
      margin-bottom: 0;
    }
   .ant-table-tbody{
     tr{
       td{
         padding: 4px 4px;
       }

     }
     .ant-table-row-selected{
       td{
         background: #aba5a5;
       }
     }
     .ant-table-row:hover{
       td{
         background: #aba5a5!important;
       }
     }
   }

  }
/deep/.rightBox{

    width:50%;
    margin-left:10px;
    .lineSty{
      width:150px;
      height:10px;
      margin-bottom: 0px;
      position: relative;
    }
    .fontSty{
      position: absolute;
      width:120px;
      margin-left: 160px;
      margin-top: -6px;
      color:#0000FF

    }
  }
}
</style>
