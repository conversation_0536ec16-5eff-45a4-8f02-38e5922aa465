<!-- JZ销售合同 -->
<template>
    <div class="pdfDom1" style="font-size: 12px;font-weight: bold;" >
        <a-button v-print='printObj1' v-if="JZsalesdata.orderPar" type="primary" class="printstyle" @click="printpdf" >打印</a-button>
        <a-button v-print='printObj' v-else type="primary" class="printstyle" @click="printpdf">打印</a-button>
        <div id="pdfDomjz" v-if="JZsalesdata.orderPar" style="font-family: monospace;">
            <div style="width: 100%;display: flex;">
                    <!-- <img   src="@/assets/img/jzlogo.png" style="height: 70px;position: relative;left: 350px;">  -->
                    <div style="text-align: center;width: 100%;">
                        <!-- <span style="font-size: 20px;letter-spacing:9px;font-weight: bold;">深圳市精焯电路科技有限公司</span><br/>
                        <span style="font-size: 13px;font-weight: bold;">SHENZHEN JINGZHUO CIRCUIT TECHNNOLOGY CO.,LTD</span><br/> -->
                        <img   src="@/assets/img/jz.png" style="height: 49px;">
                        <div style="font-size: 26px;letter-spacing:7px;font-weight: bold;">销售合同书</div> <br/>
                    </div>                            
            </div>
            <div style="display: flex;margin-top: 20px;">
                <div style="width:734px ;">
                    <div>销售方:深圳市精焯电路科技有限公司 </div>
                    <div>地址:深圳市宝安区松岗镇江边工业创业三路82-83栋 </div>
                    <div>电话:0755-27910198  联系人:邓坤鹏  </div> 
                    <div> {{ JZsalesdata.contractNO }} {{ JZsalesdata.date_ }}</div>
                    <div> 开户行：中国工商银行深圳西乡支行:账号4000023419200132914</div>
                    <div> (1)产品型号、数量、金额及交货期(本条款手写无效)</div>
                </div>
                <div>
                    <div>表格编号:12-QB-I-101A</div>
                    <div> {{ JZsalesdata.factoryR_ }}</div>
                    <div> {{ JZsalesdata.factoryAddR_ }} </div>
                    <div> {{ JZsalesdata.tel_ }}  {{ JZsalesdata.link_ }}</div>
                    <div> {{ JZsalesdata.consignee }}  {{ JZsalesdata.consigneeTel }}</div>                    
                    <div> {{ JZsalesdata.deliveryAdd }} </div>
                </div>                
            </div>
            <div>
                <table border="1" style="text-align: center">
                    <thead>
                        <tr>
                            <td style="width: 50px;" >序号</td>
                            <td style="width: 180px;" >顾客型号</td>
                            <td style="width: 180px;" >物料代码</td>
                            <td style="width: 160px;" >客户PO号</td>
                            <td style="width: 70px;" >数量</td>
                            <td style="width: 70px;" >单位</td>
                            <td style="width: 70px;" >工程费</td>
                            <td style="width: 70px;" >单价</td>
                            <td style="width: 70px;" >测试费</td>
                            <td style="width: 70px;" >沉金费</td>
                            <td style="width: 70px;" >金额</td>
                            <td style="width: 160px;" >交货日期</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in JZsalesdata.price' :key="index">
                            <td>  {{item.no}}  </td>
                            <td>  {{item.custName}}  </td>
                            <td>  {{item.mat}}  </td>
                            <td>  {{item.custPo}}  </td>
                            <td>  {{item.qty}}  </td>
                            <td>  {{item.bType}}  </td>
                            <td>  {{item.pcs}}  </td>
                            <td>  {{item.eng}}  </td>
                            <td>  {{item.test}}  </td>
                            <td>  {{ item.surf }}  </td>
                            <td>  {{item.total}}  </td>
                            <td>  {{item.custdate}}  </td>                    
                        </tr>
                    </tbody>                  
                </table>
            </div>
            <div style="margin-top: 10px;">(2)产品描述</div>
            <div>
                <table border="1" style="text-align: center">
                    <thead>
                        <tr>
                            <td style="width: 50px;" >序号</td>
                            <td style="width: 180px;" >生产型号</td>
                            <td style="width: 180px;" >版本号</td>
                            <td style="width: 160px;" >订单类型</td>
                            <td style="width: 70px;" >层数</td>
                            <td style="width: 70px;" >板厚mm</td>
                            <td style="width: 120px;" >规格(mm*mm)</td>
                            <td style="width: 70px;" >表面处理</td>
                            <td style="width: 70px;" >订单面积</td>
                            <td style="width: 300px;" >备注</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in JZsalesdata.orderPar' :key="index">
                            <td>  {{item.no}}  </td>
                            <td>  {{item.proOrderNo}}  </td>
                            <td>  {{item.version}}  </td>
                            <td>  {{item.reOrder}}  </td>
                            <td>  {{item.boardLayers}}  </td>
                            <td>  {{item.boardThickness}}  </td>
                            <td>  {{item.size}}</td>
                            <td>  {{item.surfaceFinish}}  </td>
                            <td>  {{item.area_}}  </td>
                            <td>  {{item.remark_}}  </td>                     
                        </tr>
                    </tbody>                  
                </table>
            </div>
            <div style="z-index:99;position: relative;">
                <div style="display: flex;margin-top: 15px;">
                    <div>
                        <div style="display:flex">
                            <span style="width:300px">{{JZsalesdata.deliveryType}} </span>
                            <span style="margin-left:100px">{{ JZsalesdata.freight }}</span>
                        </div>
                        <div>{{JZsalesdata.ipcLevel}}</div>
                        <div style="display:flex">
                            <span style="width:300px">{{JZsalesdata.defend}}</span> 
                            <span style="margin-left:100px">{{JZsalesdata.discer}}</span>
                        </div>
                        <div style="display:flex">
                            <span style="width:300px">{{JZsalesdata.clearingForm}}</span>
                            <span style="margin-left:100px">付款凭证仅为银行结算凭证或加盖有承揽人财务章的收据。逾期付款按月息2%计算。</span>
                        </div>
                        <div>（7）定作人委托销售方加工的线路版权应当时定作人合法持有的,如有侵犯第三方版权的行为,全部责任由定作人承担,与销售方无关</div>
                        <div>（8）双方业务中所发传真件、扫描件等同原件具同等法律效力 </div>
                        <div>（9）本合同及双方之前所签订的所有合同如产生争议,双方同意交由深圳仲裁委员会仲裁</div>               
                    </div>
                </div>
                <div style="margin-top: 20px;display: flex;">
                    <div style="width: 720px;">
                        <div>特销售方:深圳市精焯电路科技有限公司</div>
                        <div>代表签字:</div>
                    </div>
                    <div style="width: 465px;">
                        <div>{{ JZsalesdata.factoryR_ }}</div>
                        <div>代表签字:</div>
                    </div>
                </div>
            </div>
        </div>
        <div id="pdfDomjz1" v-else style="font-family: monospace;">
            <div style="width: 100%;display: flex;">
                     <!-- <img   src="@/assets/img/jzlogo.png" style="height: 70px;position: relative;left: 350px;">  -->
                     <div style="text-align: center;width: 100%;">
                        <!-- <span style="font-size: 20px;letter-spacing:9px;font-weight: bold;">深圳市精焯电路科技有限公司</span><br/>
                        <span style="font-size: 13px;font-weight: bold;">SHENZHEN JINGZHUO CIRCUIT TECHNNOLOGY CO.,LTD</span><br/> -->
                        <img   src="@/assets/img/jz.png" style="height: 49px;">
                        <div style="font-size: 26px;letter-spacing:7px;font-weight: bold;">销售合同书</div> <br/>
                    </div>                            
            </div>
            <div style="display: flex;margin-top: 20px;width: 100%;">
                <div style="width: 48%;">{{ JZsalesdata.date_ }}</div>
                <div>签订地点:深圳市宝安区</div>
            </div>
            <a-divider/>
            <div style="display: flex;line-height: 3ch;">
                <div style="width:735px;z-index: 99;" >
                    <div> {{ JZsalesdata.contract_ }} </div>
                    <div> {{ JZsalesdata.address_ }} </div>
                    <div> {{ JZsalesdata.tel_ }} </div>
                    <div> {{ JZsalesdata.fax_ }}</div>
                    <div>需方代表签字:<span style="padding-left: 100px;">(需方盖章)</span> </div>
                    <div> 供需双方经友好协商,签订本合同,以资信守。</div>
                    <div> 需方代表签字:</div>
                </div>
                <div style="z-index: 99;">
                    <div> 供方:深圳市精焯电路科技有限公司</div>
                    <div> 地址:深圳市宝安区松岗镇江边工业创业三路82-83栋 </div>
                    <div> 电话:0755-66655537 27910198 </div>
                    <div> 传真:0755-27904608 </div>
                    <div> 供方: <img src="@/assets/img/xufang.jpg"></div>
                </div>  
                <div style="z-index: 0;position: relative;top: -53px;left: -106px;height: 130px;">
                    <img src="@/assets/img/jzsales.png">
                </div>            
            </div>
            <div>
                <table border="1" style="text-align: center;">
                    <thead>
                        <tr>
                            <td style="width: 130px;" rowspan="2">合同编号</td>
                            <td style="width: 280px;" rowspan="2">品名</td>
                            <td style="width: 70px;" rowspan="2">层数</td>
                            <td style="width: 70px;" rowspan="2">板厚</td>
                            <td style="width: 120px;" rowspan="2">工艺</td>
                            <td style="width: 100px;" rowspan="2">字符</td>
                            <td style="width: 100px;" rowspan="2">规格mm*mm</td>
                            <td style="width: 90px;" rowspan="2">拼版方式</td>
                            <td style="width: 70px;"  colspan="2">交货数量</td>
                            <td style="width: 70px;" rowspan="2">单价</td>
                            <td style="width: 100px;" rowspan="2">工程费</td>
                            <td style="width: 100px;" rowspan="2">光绘费</td>
                            <td style="width: 100px;" rowspan="2">测试费</td>
                            <td style="width: 100px;" rowspan="2">模具费</td>
                            <td style="width: 100px;" rowspan="2">加急费</td>
                            <td style="width: 120px;" rowspan="2">交货面积</td>
                            <td style="width: 120px;" rowspan="2">合计金额</td>
                            <td style="width: 160px;" rowspan="2">交期</td>
                        </tr>
                        <tr>
                            <td style="width: 70px;" >Pcs</td>
                            <td style="width: 70px;" >Sets</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in JZsalesdata.price' :key="index">
                            <td>  {{ item.orderNo_ }} </td>
                            <td>  {{ item.custName }}  </td>
                            <td>  {{ item.lay }}  </td>
                            <td>  {{ item.boardThickness}}  </td>
                            <td>  {{ item.surfaceFinish }}  </td>
                            <td>  {{ item.char_ }}  </td>
                            <td>  {{ item.size }} </td>
                            <td>  {{ item.pinBanType }}  </td>
                            <td>  {{ item.qty }}  </td>
                            <td>  {{ item.setQty }}  </td>
                            <td>  {{ item.pcs }}  </td>
                            <td>  {{ item.eng }}  </td>
                            <td>  {{ item.film }}  </td>
                            <td>  {{ item.test }}  </td>        
                            <td>  {{ item.mould }}  </td>
                            <td>  {{ item.urgent }} </td>
                            <td>  {{ item.area }}   </td>
                            <td>  {{ item.total }}  </td>   
                            <td>  {{ item.custdate }}  </td>
                        </tr>
                    </tbody>  
                </table>
            </div>
            <div style="display: flex;border: 1px solid gray;border-top: none;height: 30px;line-height: 30px;">
                        <div style="width: 40%;">合计金额(大写)<span style="padding-left: 8ch;">人民币{{ convertToChineseNum(amountto)}}整</span></div>
                        <div style="width: 30%;">税率:</div>
                        <div style="width: 30%;">合计:{{ amountto }}</div>
                    </div>
            <div style="z-index:99;position: relative;">
                <div style="line-height: 3ch;margin-top: 15px;width: 100%;">
                    <div>
                        <div> {{ JZsalesdata.orderNo_ }} </div>
                        <div> {{ JZsalesdata.specialrequirements }} </div>
                        <div> 
                            <span style="width: 60%;display: inline-block;">{{ JZsalesdata.clearingForm }}</span> 
                            <span style="width: 30%;display: inline-block;">{{ JZsalesdata.defend }}</span> 
                        </div>
                        <div>  
                            <span style="width: 30%;display: inline-block;">{{ JZsalesdata.deliveryType }}</span> 
                            <span style="width: 30%;display: inline-block;">{{ JZsalesdata.ipcLevel }}</span> 
                            <span style="width: 30%;display: inline-block;">{{ JZsalesdata.deliveryAdd }}</span>
                        </div>  
                        <div> 客户要求:</div>   
                        <div>附注: 1.请采取汇款及开支票形式结账，并必须在支票上填写本公司抬头，如购方以现金方式或货款支付到个人信用卡上，必须有供方财务部门收款证明，否则发生意外责任全部由购方负责。<br/>
                            &nbsp; &nbsp; 2.需方在收货以后两周内反馈质量情况，逾期供方不负责赔偿责任。<br/>
                            &nbsp; &nbsp; 3.供方有替需方保护产品专利的责任，其他事宜与供方无关。<br/>
                            &nbsp; &nbsp; 4.因质量问题导致需方产品受损，供方需承担不多于产品货价总额之赔偿。<br/>
                            &nbsp; &nbsp; 5.本合同签约地及执行在供方所在地。<br/>
                            表格编号:JZ-QR-DCC-244C</div>               
                    </div>
                </div>
            </div>
        </div>
    </div>
  </template>
  <script>
  import convertToChineseNum from '@/utils/convertToChineseNum';
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['JZsalesdata','ttype'],    
    computed:{  },
    data() {
      return { 
        amountto:0,
        printObj:{
            id: "pdfDomjz1", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
         printObj1:{
            id: "pdfDomjz", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj.closeCallback = this.closePrintTool;
        this.printObj1.closeCallback = this.closePrintTool;
        this.amountto =0 
        for (let index = 0; index < this.JZsalesdata.price.length; index++) {
            if(this.JZsalesdata.price[index].total && this.JZsalesdata.price[index].total!='/'){
                this.amountto +=Number(this.JZsalesdata.price[index].total)
            } 
        }
        this.amountto = this.amountto ? this.getFloat(this.amountto,4) : 0
    },
    methods: { 
        convertToChineseNum,
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.JZsalesdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getsalesPdf(){
            if(this.JZsalesdata.orderPar){
                htmlToPdfa3('pdfDomjz',this.JZsalesdata.pcbFileName)
            }else{
                htmlToPdfa3('pdfDomjz1',this.JZsalesdata.pcbFileName)
            }            
        },
        getReportPdf(){
            htmlToPdf('pdfDomjz','精焯销售合同')
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
    .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
    /deep/.ant-divider-horizontal {
    display: block;
    clear: both;
    width: 100%;
    min-width: 100%;
    height: 1px;
    margin: 8px 0;
    background: black;
  }
  .agreement{
    padding-bottom: 20px;
    position: relative;
    z-index: 99;
    div{
        padding-top: 10px;
    }
  }
  .tableclass {
    border:1px solid black;
    margin-top: 10px;
    margin-bottom: 10px;
    td{
        border-right:1px solid black;
        border-bottom:1px solid black;
        padding-left: 7px;
    }
    tbody{
        tr{
            height: 24px;
        }
    }

  }
  .pdfDom1{
      padding: 25px;
      height: 650px;
      overflow: auto;
  }
  </style>
