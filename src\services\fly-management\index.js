import { request, METHOD } from '@/utils/request';
import qs from 'querystring'
// 待发订单
export async function getWaitOrderList(params) {
    return request("/api/app/e-mSTProc-production-process/cnc-wait-distribute", METHOD.GET, params)
}
// 已发订单
export async function getDoingOrderList(params) {
    return request("/api/app/e-mSTProc-production-process/cnc-finish-distribute", METHOD.GET, params)
}
// 获取机台列表
export async function getMachineList(params) {
    return request("/api/app/e-mSTProc-production-process/cnc-machine-list", METHOD.GET, params)
}
// 当班统计
export async function getStatisticsList(params) {
    return request("/api/app/e-mSTProc-production-process/get-ondutystatistics", METHOD.GET, params)
}
// 机台上订单
export async function getDispatchList(id, params) {
    return request(`/api/app/e-mSTProc-production-process/${id}/get-probe-finish-order`, METHOD.GET, params)
}
// 分派机台
export async function getDispatchMachineList(params) {
    return request('/api/app/e-mSTProc-production-process/send-machine', METHOD.POST, params)
}
// 分派回退
export async function getDispatchFallbackList(params) {
    return request(`/api/app/e-mSTProc-production-process/drill-back-machine`, METHOD.POST, params)
}
// 获取部门过序数量
export async function getpassStepNum(params) {
    return request(`/api/app/e-mSTProc-production-process/character-num`, METHOD.GET, params)
}
// 飞针部门过序
export async function OverSequence(params) {
    return request(`/api/app/e-mSTProc-production-process/character-over-count`, METHOD.POST, params)
}
// 获取工厂Id列表
export async function getFactoryList() {
    return request(`/api/app/e-mSTPub-factory-configure/factory-id-list`, METHOD.POST,)
}
// 上传订单-按钮上传文件
export async function UploadFile(params) {
    return request(`/api/app/e-mSTProc-production-process/up-load-file`, METHOD.POST, params)
}
// 右键上传文件
export async function UploadFile1(params) {
    return request(`/api/app/e-mSTProc-production-process/up-file`, METHOD.POST, params)
}
// 上传订单
export async function UploadOrder(params) {
    return request(`/api/app/e-mSTProc-production-process/out-rout-order-up-load`, METHOD.POST, params)
}
// 设置加急
export async function SetUpExpediting(Id) {
    return request(`/api/app/e-mSTProc-production-process/is-urgent/${Id}`, METHOD.POST, Id)
}
// 数据核对
export async function DataCheck() {
    return request(`/api/app/e-mSTProc-production-process/data-check`, METHOD.POST,)
}
// 删除订单
export async function DeleteOrder(Id) {
    return request(`/api/app/e-mSTProc-production-process/flying-probe-del-order/${Id}`, METHOD.POST, Id)
}
// 异常备注
export async function ExceptionRemarks(Id, params) {
    return request(`/api/app/e-mSTProc-production-process/flying-probe-remarks/${Id}`, METHOD.GET, params)
}
// 异常登记
export async function AbnormalRegistration(params) {
    return request(`/api/app/e-mSTProc-production-process/et-erro?Pdctno_=${params}`, METHOD.POST)
}
// 人员登记
export async function Personnelregistration(params) {
    return request('/api/app/e-mSTProc-production-process/et-step-password', METHOD.POST, params)
}
// 设置故障
export async function SetFault(Id) {
    return request(`/api/app/e-mSTProc-production-process/flying-probe-sign-bad/${Id}`, METHOD.GET,)
}
// 下机
// export async function getDoneOrderList(Id) {
//     return request(`/api/app/e-mSTProc-rOUTMake/rout-order-finish/${Id}`, METHOD.POST)
// }
// 下机/单击完成
export async function getDoneOrderList (Id) {
    return request(`/api/app/e-mSTProc-production-process/finish-character`, METHOD.POST,Id)
}
// 获取订单已上机台Id数组
export async function getOrderMuIdList(Id) {
    return request(`/api/app/e-mSTProc-production-process/cnc-order-mu-id-list/${Id}`, METHOD.GET,)
}
// 呼叫小车
export async function CallTrolley() {
    return request(`/api/app/e-mSTProc-production-process/call-trolley`, METHOD.POST)
}
// 人员确认
export async function Confirm() {
    return request(`/api/app/e-mSTProc-production-process/confirm`, METHOD.POST)
}
// 取消小车
export async function AgvCancel() {
    return request(`/api/app/e-mSTProc-production-process/agv-cancel`, METHOD.POST)
}
// 修改Pnl数
export async function updatePnl(params) {
    return request(`/api/app/e-mSTProc-production-process/up4Character-pnl-qty`, METHOD.POST, params)
}
// 标记/取消 机台故障
export async function signMachineStatus(Id) {
    return request(`/api/app/e-mSTProc-dRMake/set-sign-bad/${Id}`, METHOD.POST,)
}
// 下载
export async function downLoad(Id) {
    return request(`/api/app/e-mSTProc-production-process/down-load-path/${Id}`, METHOD.GET,)
}
// 上传Tgz(地址和Pdctno)
export async function orderFileUpload(params) {
    return request(`/api/app/e-mSTProc-production-process/up-load-flying-file`, METHOD.GET, params)
}
// 上传mnf2(地址和Pdctno)
export async function orderFileUploadMnf2(params) {
    return request(`/api/app/e-mSTProc-production-process/up-load-flying-mnf2File`, METHOD.GET, params)
}
// 批量修改数量(地址和Pdctno)
export async function BatchModificationQuantity(params) {
    return request(`/api/app/e-mSTProc-production-process/batch-update-qty`, METHOD.POST, params)
}

export default {
    getWaitOrderList,
    getDoingOrderList,
    getMachineList,
    getStatisticsList,
    getDispatchList,
    getDispatchMachineList,
    getDispatchFallbackList,
    getpassStepNum,
    getDoneOrderList,
    SetUpExpediting,
    DeleteOrder,
    ExceptionRemarks,
    SetFault,
    DataCheck,
    OverSequence,
    getFactoryList,
    UploadFile,
    UploadFile1,
    UploadOrder,
    getOrderMuIdList,
    updatePnl,
    AbnormalRegistration,
    Personnelregistration,
    CallTrolley,
    Confirm,
    AgvCancel,
    orderFileUpload,
    downLoad,
    orderFileUploadMnf2,
    BatchModificationQuantity,
}