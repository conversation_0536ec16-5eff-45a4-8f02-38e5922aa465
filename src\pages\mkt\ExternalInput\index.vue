<template>
<a-spin :spinning="spinning">
  <div class="projectMake">  
    <div style='width:100%;display:flex;'>   
      <div class="leftContent" @contextmenu.prevent="rightClick($event)">
        <left-table-make
            :columns="columns1"
            :dataSource="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'mbId'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            @getOrderDetail="getOrderDetail"          
            @jigsawPuzzleClick="jigsawPuzzleClick"
            @getJobInfo="getJobInfo"
            ref="orderTable"
        >
        <!--  :class="orderListData.length ? 'min-table':''" -->
        <span slot="num" slot-scope="text, record, index">
          {{(pagination.current-1)*pagination.pageSize+parseInt(index)+1}}
        </span>
        </left-table-make>        
      </div>
      <div class="rightContent">
        <div class="centerTable">
          <a-tabs type="card" >
            <a-tab-pane tab=''>
              <order-detail
                  :columns="columns2"
                  :data-source="orderDetailDataFilter"
                  :orderDetailTableLoading="orderDetailTableLoading"
                  :rowKey="'projectName'"
              >
              </order-detail>
            </a-tab-pane>        
          </a-tabs>
        </div>
        
      </div>
    </div>
    <div class="footerAction">
        <make-action
            ref="action"
            :assignLoading="assignLoading"
            @queryClick="queryClick"
            @TakeOrderClick="TakeOrderClick"
            @MakeStartClick="MakeStartClick" 
            @uploadPCBFileClick='uploadPCBFileClick'            
            @modifyInfoClick='modifyInfoClick'
            @getOrderList='getOrderList' 
        />
      </div>
  
    <!-- 查询弹窗 -->
   <a-modal
          title="订单查询"
          :visible="dataVisible"
          @cancel="reportHandleCancel"
          @ok="handleOk"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"

   >
   <query-info ref='queryInfo' />
   </a-modal> 
  </div>

</a-spin>
</template>

<script>

import LeftTableMake from "@/pages/mkt/moduleInput/LeftTableMake";
import {
  TakeOrderList,
  projectBackEndJobInfo,
  projectBackEndOrderDetail,
  projectMakeOrderList,
  MakeStart,
  getModifyInformation,  
  getFactoryList,
} from "@/services/mkt/externalinput.js";
import MakeAction from "@/pages/mkt/ExternalInput/moduleInput/MakeAction";
import QueryInfo from "@/pages/mkt/ExternalInput/moduleInput/QueryInfo";
import OrderDetail from "@/pages/mkt/Orderverify/subassembly/OrderDetail";


const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    // fixed:'left',
    // customRender: (text,record,index) => `${index+1}`,
    scopedSlots: { customRender: 'num' },
    width: 40,    
  },
  {
    title: "订单编号",
    align: "center",    
    ellipsis: true,
    width: 120,
    scopedSlots: { customRender: 'orderNo' },
    customCell: (record, rowIndex) => {
      let strGroup = []     
      if (record.guesEndDate_  && new Date() - new Date(record.guesEndDate_)>3600000 ) {
        // 'eqBackground'
        strGroup.push({'background': '#FFFF00!important' })
      }
      if (record.status == 20 && record.isEQ ==1) {
        // 'statuBackground'
        strGroup.push({'background': '#B0E0E6!important'})
      }
      if (record.backUserAccount  > 0) {
        // backUserBackground
        strGroup.push({'background': '#A7A2C9!important'})
      }
      if (strGroup.length > 1) {
        strGroup = strGroup[0]
      }
      return {style: strGroup[0]}
    },
    className:'userStyle'
  },
  // {
  //   title: "订单标记",
  //   key: "tag",
  //   scopedSlots: { customRender: 'tag' },
  //   align: "left",
  //   fixed:'left',
  //   ellipsis: true,
  //   width:140,
  // },  
  {
    title: "会员Id",
    dataIndex: "mbId",
    align: "center",
    ellipsis: true,
    width: 60,
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "center",
    ellipsis: true,
    width: 40,
  },
  {
    title: "交货面积",
    dataIndex: "boardArea",
    align: "center",
    ellipsis: true,
    width: 80,
  },
  {
    title: "状态",
    dataIndex: "status",
    align: "center",
    ellipsis: true,
    width:80,
  },
  
  // {
  //   title: "拼版图",
  //   dataIndex: "jigsawPuzzle",
  //   align: "center",
  //   width: 55,
  //   ellipsis: true,
  //   scopedSlots: { customRender: 'jigsawPuzzle'},
  //   // customRender: function(num) {
  //   //   return num.toFixed(2)
  //   // }
  // },
  {
    title: "尺寸",
    dataIndex: "boardSize",
    width: 80,
    ellipsis: true,
    align: "center",
  },
  {
    title: "创建人",
    dataIndex: "createUser",
    ellipsis: true,
    align: "center",
    width: 80,
  },
  {
    title: "交货日期",
    dataIndex: "deliveryDate",
    align: "center",
    ellipsis: true,
    width:120,
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    // width: 130,
    ellipsis: true,
    align: "center",
    width:120,
  },  
 
]
const columns2 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: 19,
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "项目",
    dataIndex: "projectName",
    width: 60,
    align: "left",
    customCell: (record, rowIndex) => { 
      if (record.projectName == '铜厚(外)oz' && record.parameter > 1.0) {
        return { style: { 'background': '#ff0000!important', } } // copperThickness
      }
      else if (record.projectName == '铜厚(内)oz' && record.parameter > 1.0) {
        return { style: { 'background': '#ff0000!important', } } // innerCopperThickness 铜厚(内)oz
      }
      else if (record.projectName == '字符颜色(顶)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } // fontColor 字符颜色(顶)
      }
      else if (record.projectName == '字符颜色(底)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } // fontColorBottom 字符颜色(底)
      }
      else if (record.projectName == '阻焊颜色(顶)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } //solderColor 阻焊颜色(顶)
      }
      else if (record.projectName == '阻焊颜色(底)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } // solderColorBottom 阻焊颜色(底)
      }
      else if (record.projectName == '过孔处理' && record.parameter == '过孔开窗') {
        return { style: { 'background': '#ff00ff!important', } } // solderCover 过孔处理
      }
      else if (record.projectName == '过孔处理' && record.parameter == '过孔塞油') {
        return { style: { 'background': '#ff0000!important', } } // solderCover 过孔处理
      }
      else if (record.projectName == '数量' && record.boardArea > 1.0) {
        return { style: { 'background': '#ff0000!important', } } // num 数量
      }     
      else if (record.projectName == '阻抗报告' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // impedanceReport 阻抗报告
      }
      else if (record.projectName == '阻抗' && record.parameter !== '无') {
        return { style: { 'background': '#ff0000!important', } } // impedanceSize 阻抗
      }
      else if (record.projectName == '金手指倒斜边' && record.parameter == '是' ) {
        return { style: { 'background': '#ff0000!important', } } // Goldfinger 金手指倒斜边
      }
      else if (record.projectName == '树脂塞孔' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // resinPlugHole 树脂塞孔
      }
      else if (record.projectName == '碳油' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // carbonOil_ 碳油
      }
      else if (record.projectName == '孔铜25um' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // holeThickness_ 孔铜25um
      }
      else if (record.projectName == '蓝胶' && record.parameter == '是' ) {
        return { style: { 'background': '#ff0000!important', } } // blueGlue_ 蓝胶
      }
      else if (record.projectName == '喇叭孔/台阶孔' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // stepHole_ 喇叭孔/台阶孔
      }
      else if (record.projectName == '压接孔' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // pressfit_ 压接孔
      }
      else if (record.projectName == '金属包边' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // metalEdging 金属包边
      }
      else if (record.projectName == 'CTI≥600' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // ctI_ CTI≥600
      }
      else if (record.projectName == '返单编号' && record.parameter !== '') {
        return { style: { 'background': '#ff0000!important', } } // reOrderNo 返单编号
      }
      else if (record.projectName == '修改编号' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // changeItemNum 修改编号
      }
      else if (record.projectName == '修改周期' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // changePeriod 修改周期
      }
      else if (record.projectName == '图形转移工艺' && record.parameter == '丝网印刷') {
        return { style: { 'background': '#ff0000!important', } } // imageTranster 图形转移工艺
      }
      else if (record.projectName == '文件名' && record.count >0) {
       return { style: { 'background': '#ff0000!important', } } // fileUploadedCount 文件上传次数>0 文件名显示红色
      }
    },
  },
  {
    title: "参数",
    dataIndex: "parameter",
    width: 60,
    align: "left",
    className:'userStyle',
    customCell: (record, rowIndex,) => { 
      if (record.projectName == '铜厚(外)oz' && record.parameter > 1.0) {
        return { style: { 'background': '#ff0000!important', } } // copperThickness
      }
      else if (record.projectName == '铜厚(内)oz' && record.parameter > 1.0) {
        return { style: { 'background': '#ff0000!important', } } // innerCopperThickness 铜厚(内)oz
      }
      else if (record.projectName == '字符颜色(顶)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } // fontColor 字符颜色(顶)
      }
      else if (record.projectName == '字符颜色(底)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } // fontColorBottom 字符颜色(底)
      }
      else if (record.projectName == '阻焊颜色(顶)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } //solderColor 阻焊颜色(顶)
      }
      else if (record.projectName == '阻焊颜色(底)' && record.parameter == '无') {
        return { style: { 'background': '#ff0000!important', } } // solderColorBottom 阻焊颜色(底)
      }
      else if (record.projectName == '过孔处理' && record.parameter == '过孔开窗') {
        return { style: { 'background': '#ff00ff!important', } } // solderCover 过孔处理
      }
      else if (record.projectName == '过孔处理' && record.parameter == '过孔塞油') {
        return { style: { 'background': '#ff0000!important', } } // solderCover 过孔处理
      }
      else if (record.projectName == '数量' && record.boardArea > 1.0) {
        return { style: { 'background': '#ff0000!important', } } // num 数量
      }     
      else if (record.projectName == '阻抗报告' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // impedanceReport 阻抗报告
      }
      else if (record.projectName == '阻抗' && record.parameter !== '无') {
        return { style: { 'background': '#ff0000!important', } } // impedanceSize 阻抗
      }
      else if (record.projectName == '金手指倒斜边' && record.parameter == '是' ) {
        return { style: { 'background': '#ff0000!important', } } // Goldfinger 金手指倒斜边
      }
      else if (record.projectName == '树脂塞孔' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // resinPlugHole 树脂塞孔
      }
      else if (record.projectName == '碳油' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // carbonOil_ 碳油
      }
      else if (record.projectName == '孔铜25um' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // holeThickness_ 孔铜25um
      }
      else if (record.projectName == '蓝胶' && record.parameter == '是' ) {
        return { style: { 'background': '#ff0000!important', } } // blueGlue_ 蓝胶
      }
      else if (record.projectName == '喇叭孔/台阶孔' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // stepHole_ 喇叭孔/台阶孔
      }
      else if (record.projectName == '压接孔' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // pressfit_ 压接孔
      }
      else if (record.projectName == '金属包边' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // metalEdging 金属包边
      }
      else if (record.projectName == 'CTI≥600' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // ctI_ CTI≥600
      }
      else if (record.projectName == '返单编号' && record.parameter !== '') {
        return { style: { 'background': '#ff0000!important', } } // reOrderNo 返单编号
      }
      else if (record.projectName == '修改编号' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // changeItemNum 修改编号
      }
      else if (record.projectName == '修改周期' && record.parameter == '是') {
        return { style: { 'background': '#ff0000!important', } } // changePeriod 修改周期
      }
      else if (record.projectName == '图形转移工艺' && record.parameter == '丝网印刷') {
        return { style: { 'background': '#ff0000!important', } } // imageTranster 图形转移工艺
      }else if (record.projectName == '文件名' && record.count >0) {
       return { style: { 'background': '#ff0000!important', } } // fileUploadedCount 文件上传次数>0 文件名显示红色
      }
    },
  },
]

const projectArray = [
  {name:'工厂编号',value:'orderNo'},
  {name:'层数',value:'boardLayers'},
  {name:'板材信息',value:'boardBrand'},
  {name:'文件上传次数',value:'fileUploadedCount'},
  {name:'板厚(mm)',value:'boardThickness'},
  {name:'铜厚(外)oz',value:'copperThickness'},
  {name:'铜厚(内)oz',value:'innerCopperThickness'},
  {name:'阻焊颜色(顶)',value:'solderColor'},
  {name:'阻焊颜色(底)',value:'solderColorBottom'},
  {name:'字符颜色(顶)',value:'fontColor'},
  {name:'字符颜色(底)',value:'fontColorBottom'},
  {name:'表面处理',value:'surfaceFinish'},
  {name:'图形转移工艺',value:'imageTranster'},
  {name:'过孔处理',value:'solderCover'},
  {name:'高(mm)',value:'boardHeight'},
  {name:'宽(mm)',value:'boardWidth'},
  {name:'出货类型',value:'pinBanType'},
  {name:'成型方式',value:'vCut'},
  {name:'V割类型',value:'formingType'},
  {name:'金手指倒斜边',value:'goldfinger'},
  {name:'阻抗报告',value:'impedanceReport'},
  {name:'阻抗',value:'impedanceSize'},
  {name:'电镀工艺',value:'beforePlating'},
  {name:'测试：飞针测试,测试架',value:'flyingProbe'},
  {name:'带测试架的返单订单号',value:'testRackOrderNo'},
  {name:'订单编号',value:'businessOrderNo'},
  {name:'下单时间',value:'createTime'},
  {name:'交货日期',value:'deliveryDate'},
  {name:'数量',value:'num'},
  {name:'孔径(mm)',value:'vias'},
  {name:'导热系数',value:'invoice'},
  {name:'线宽线距(mil)',value:'lineWeight'},
  {name:'文件名',value:'pcbFileName'},
  {name:'拼版数量',value:'pinBanNum'},
  {name:'返单编号',value:'reOrderNo'},
  {name:'修改周期',value:'changePeriod'},
  {name:'半孔',value:'isHalfHole'},
  {name:'树脂塞孔',value:'resinPlugHole'},
  {name:'碳油',value:'carbonOil_'},
  {name:'孔铜25um',value:'holeThickness_'},
  {name:'蓝胶',value:'blueGlue_'},
  {name:'喇叭孔/台阶孔',value:'stepHole_'},
  {name:'压接孔',value:'pressfit_'},
  {name:'金属包边',value:'metalEdging'},
  {name:'CTI≥600',value:'ctI_'},
  {name:'修改编号',value:'changeItemNum'},
  
]
export default {
  name: "PrequalificationProduction",
  components: {MakeAction,  QueryInfo,LeftTableMake,OrderDetail},
  inject:['reload'],
  data(){
    return {
      factoryList:[],
      spinning:false,      
      pagination:{
        current: 1,
        pageSize: 25,        
        showTotal: (total) => `总计 ${total} 条`,
        total: 0
      },
      query:{
        'OrderNo':''
      },
      columns1,
      orderListData:[],
      orderListTableLoading:false,
      columns2,
      orderDetailData:[],
      orderDetailTableLoading:false,  
      jobTableLoading:false, 
      note:'',
      cnNote:'',
      erpKey:'',
      proOrderId:'',
      assignLoading: false,
      selectedRowsData:{},  
      makeupVisible:false,    // 拼版图弹窗开关
      allNote:'',
      dataVisible: false,   // 查询弹窗开关
      dataVisible1:false,   // 修改信息开关
      dataVisible2:false,   // 退单开关
      dataVisible3:false,   // 编辑参数开关
      dataVisible4:false,   // 注意事项开关
      dataVisible5:false,   // 生成叠层开关
      dataVisible6:false,   // 返修记录开关
      dataVisible7:false,   // 客户规则开关
      ParameterData:{},
      menuVisible:false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      stackListData:{},   // 生成叠层
      RepairRecordData:[],   // 返修记录
      StackImpedanceData:[],  // 叠层阻抗
      CuttingData:[],        // 开料拼版
      CustomerData:[],      // 客户规则
      
    }
  },
  created() {
    // this.jobTableLoading = true;
    // this.orderDetailTableLoading = true;
    this.getOrderList();
    this.selectClick();
  },
  computed:{
    orderDetailDataFilter(){
      let arr_ = []
      if (this.orderDetailData.length != 0) {
        projectArray.forEach(item => {
          arr_.push({'projectName':item.name, 'parameter': this.orderDetailData[item.value], 'count':this.orderDetailData.fileUploadedCount,'boardArea':this.orderDetailData.boardArea,})
        })
      }      
      return arr_      
    }
  },
  methods:{
    
    // 获取订单
    getOrderList(queryData){      
      let params = {
        'PageIndex': this.pagination.current,
        'PageSize' : this.pagination.pageSize,
      }
      if(queryData) {
        params.OrderNo = queryData.OrderNo;  // 生产编号
        params.PcbFileName = queryData.PcbFileName       // 生产编号   
      }
      this.orderListTableLoading = true;
      projectMakeOrderList (params).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
          // console.log('this.orderListData',this.orderListData)
          this.pagination.total = res.data.totalCount;
        }
      }).finally(()=> {
        this.orderListTableLoading = false;
      })
    },
    // 获取订单详情
    getOrderDetail(record){
      this.orderDetailTableLoading = true;
      projectBackEndOrderDetail(record.id).then(res => {           
          if (res.data) {
            console.log('res.data:',res.data)
            console.log('record:',record)
            this.note = res.data.note;
            this.cnNote = res.data.cnNote;
            this.noteHtml(record)
            this.orderDetailData = res.data;
            // console.log('this.orderDetailData:',this.orderDetailData)
          }
      }).finally(() => {
        this.orderDetailTableLoading = false;
      })
    },
   
    // 取单
    TakeOrderClick(){
      if(confirm('确定取单吗？')){
        this.spinning = true
        TakeOrderList().then(res =>{
          if (res.code){
            this.$message.success('成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(()=>{
          this.spinning = false
        })
      }
    },
    // 开始
    MakeStartClick(){
      console.log(this.$refs.orderTable.selectedRowKeysArray[0])
      if(!this.$refs.orderTable.selectedRowKeysArray[0]){
        this.$message.warning("请选择订单")
        return
      }
      if(confirm('确定开始制作吗？')){
        this.spinning = true
        MakeStart(this.$refs.orderTable.selectedRowKeysArray[0]).then(res =>{        
          if (res.code){
            this.$message.success('标记开始成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(()=>{
          this.spinning = false
        })
      }
    },
    // 弹窗关闭
    reportHandleCancel(){
      this.dataVisible = false;   // 查询弹窗
      this.dataVisible1 = false;  // 修改信息弹窗 
      this.dataVisible4=false;  // 注意事项   
      this.dataVisible8=false;  // 查看日志
    },
    // 查询
    queryClick(){
      this.dataVisible = true;      
    },    
    handleOk(){
      this.dataVisible = false;
      let queryData = this.$refs.queryInfo.form
      this.getOrderList(queryData) 
    },
    // 获取协同工厂列表
    selectClick(){      
      getFactoryList().then(res =>{
       if (res.code == 1) {
         
          this.factoryList = res.data   
        }else{
          this.$message.error(res.message)
        }
      })      
    },    
    // 审核通过（完成）
    modifyInfoClick(){ 
      if(!this.$refs.orderTable.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }      
      if(confirm('确认审核通过吗？')){
        this.spinning = true
        getModifyInformation(this.$refs.orderTable.selectedRowKeysArray[0]).then(res =>{
        if (res.code){
          this.$message.success('审核通过')
        } else {
          this.$message.error(res.message)
        }
        this.reload()
      }).finally(()=>{
        this.spinning = false
      })
     }      
    },   
    // 上传pcb文件
    uploadPCBFileClick(){
      if(!this.$refs.orderTable.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.$refs.action.clickUpload(this.$refs.orderTable.selectedRowKeysArray[0])
      
    },
    // 获取对应的订单人员
    getJobInfo(id){      
      this.jobTableLoading = true;
      projectBackEndJobInfo(id).then(res => {
        if (res.code) {
          this.jobData = res.data;
        }
      }).finally(()=>{
        this.jobTableLoading = false;
      })
    },    
    // 图片字符串裁切
    picFilter(val){
      if (val.picUrl) {
        return val.picUrl.split(',')
      } else {
        return []
      }
    },
    // 订单表变化change
    handleTableChange(pagination) {
      this.pagination.current=pagination.current
      this.getOrderList()
    },    
    // 行点击事件    
    isRedRow (record) {
      if (record.proOrderId == this.proOrderId) {
        return 'rowBackgroundColor'
      } else {
        return ''
      }
    },
    noteHtml(record){
      // console.log(this.note, this.cnNote, record.errChk_, record.errMsg_)
      // const noteRegStr = this.note.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${'http://admin.jiepei.com/'}$2$3`)
      // const cnNoteRegStr = this.cnNote.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${'http://admin.jiepei.com/'}$2$3`)

      // let str_ = `<div class="${this.note ? '' : 'displayFlag'}"><p>客户备注</p><div>${noteRegStr}</div></div>
      //             <div class="${this.cnNote ? '' : 'displayFlag'}"><p>业务员备注</p><div>${cnNoteRegStr}</div></div>
      //             <div class="${record.errChk_ ? '' : 'displayFlag'}"><p>分析项</p><div>${record.errChk_}</div></div>
      //             <div class="${record.errMsg_ ? '' : 'displayFlag'}"><p>输出提示</p><div>${record.errMsg_}</div></div>
      //           `
      // this.allNote = str_

    },
    jigsawPuzzleClick(record){
      this.makeupVisible = true

      this.$nextTick(()=>{
        debugger
        this.$refs.makeup.impositionInformationExample(record.boardHeight, record.boardWidth, record.pinban_x || 1, record.pinban_y || 1, record.processeEdge_x || "none", record.processeEdge_y || 0, record.vCut || "none", record.cao_x || 0, record.cao_y || 0);
      })

      console.log(record)
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e){
      this.menuVisible = true;
      console.log()
      this.menuStyle.top = e.clientY- 110 +  "px";
      this.menuStyle.left = e.clientX -
          document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
      document.body.addEventListener("click", this.bodyClick);
    },         
    
    
  }
}
</script>

<style scoped lang="less">
.projectMake {
  user-select: none;
  height: 834px;
  width: 100%;
  background: #FFFFFF;
  /deep/.leftContent {
    border:2px solid rgb(238, 238, 238);
    border-bottom: 4px solid #e9e9f0;
    height:782px;
    .min-table {
      .ant-table-body{        
        min-height:776px;
      }
      .ant-table-placeholder{
        display: none;
      }
    }
    .ant-table{
      height:782px;
    }

    .ant-table-body{
      overflow: inherit !important;;
      .ant-table-fixed{
        width: 900px !important;
      }     
    }    
    width: 75%;    
    // border: 2px solid rgb(233, 233, 240);
     .tabRightClikBox{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color:#000000
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }
    
  }
  /deep/ .userStyle{
      user-select: all !important;
    }
  .rightContent {
    border-bottom: 2px solid rgb(233, 233, 240);
    width: 25%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
   /deep/ .centerTable {
     .ant-table-bordered.ant-table-empty .ant-table-placeholder{
        border-left:0;
        border-right:0;
      }
      .ant-table-body{
      max-height: 747px!important;
      }
      width: 100%;
      height: 780px;
      border: 2px solid rgb(233, 233, 240) ;
      // border-bottom: 4px solid #e9e9f0;     
      } 
      
  // /deep/  .rightTable {  
  //   .ant-table-bordered.ant-table-empty .ant-table-placeholder{
  //       border-left:0;
  //       border-right:0;
  //     }  
  //     width: 55%;
  //     height: 782px;
  //     border: 2px solid rgb(233, 233, 240);
  //     border-bottom: 4px solid #e9e9f0;
  //     .jobNote {
  //       /deep/ .ant-table-wrapper {
  //         height: 400px;
  //       }
  //     }
  //     .peopleOrderSt{
  //       height:220px;
  //     }
  //     .note {
  //       height: 300px;
  //       overflow-y: auto;
  //     }
      
  //   }
    
  }
    //.leftTable {
    //  width: 60%;
    //  border: 2px solid rgb(233, 233, 240)
    //}
    .footerAction {
      width: 100%;
      height: 52px;
      overflow: hidden;
      background: #FFFFFF;
    }

  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display:none;
    }
    // .ant-tabs-nav-container{
    //   height: 28px;
    //   .ant-tabs-tab {
    //     margin: 0;
    //     height: 28px;
    //     line-height: 28px;
    //     width:302px;
    //     text-align: center;
    //   }
    // }

  }
  /deep/ .ant-table-fixed {    
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #DC143C;
        }
      }
      .eqBackground {
        background: #FFFF00;
      }
      .statuBackground {
        background: #B0E0E6;
      }
      .backUserBackground {
        background: #A7A2C9;
      }
      
    }
    .ant-table-row-selected {
      td {
        background: #aba5a5;
      }
    }
    .userStyle{
      user-select: all !important;
    }
   
  }
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: #aba5a5!important;
    }
  /deep/ .ant-table{
   
    .ant-table-thead > tr > th{
      padding: 3px 4px;
      border-color: #f0f0f0;
    }
    .ant-table-tbody > tr > td {
      padding: 3px 4px;
      border-color: #f0f0f0;
    }
    tr.ant-table-row-selected td {
     background: #aba5a5;
    }
    tr.ant-table-row-hover td {
     background: #aba5a5;
    }
    .rowBackgroundColor {
      background: #aba5a5;
    }
   
    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 10px 0 0 10px;
  }

 

}
</style>
<style lang="less">
.note {
  .textNote {
    background: #D6D6D6;

    p {      
      line-height: 35px;
      font-weight: 700;
      margin: 0;
      img{
        width:100%;
      }
    }
  .displayFlag {
      display: none;
      
    }
  }
}
.modalSty {
  /deep/.ant-modal .ant-modal-content .ant-modal-body {
   
    padding: 0;
  }
}
</style>
