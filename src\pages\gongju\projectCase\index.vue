<!-- 工具管理- 工程案例 -->
<template>
  <div ref="SelectBox" @click="bodyClick">
    <a-spin :spinning="spinning">
    <div class="projectCase" style="position: relative;">
      <div style='width:100%;display:flex;'>
        <div class="leftContent" >
          <a-table
              :columns="columns1"
              :data-source="orderListData"
              :loading="loading"
              :rowKey="'guid_'"
              :scroll="{y:723}"
              :pagination="false"
              ref="orderTable"
              :customRow="onClickRow"
              :rowClassName="isRedRow"
              :class="orderListData.length ? 'min-table':''"
          >
          </a-table>
        </div>
        <div class="rightContent">
          <div style="width:100%;height:40px;float: left;margin-top:5px;">
            <span style="color:red;margin-right:5px;"> 案例类型</span>
            <a-select style="width:20%;" v-if="editFlg" v-model="showData.caseType_" :getPopupContainer="()=>this.$refs.SelectBox">
              <a-select-option v-for="(ite,index) in Parameter" :value="ite.text" :key="index" >{{ ite.text }}</a-select-option>
            </a-select>
            <a-input style="width:20%;" v-else disabled v-model="showData.caseType_"></a-input>
            <span style="color:red;margin-right:5px;"> 案例简述</span>
            <a-input style="width:67%;" :disabled='!editFlg' v-model="showData.caseSketch_"></a-input>
          </div>
          <div style="width:100%;height:200px;float: left;margin-top:5px;">
            <div style="display: flex;padding-right:6px;" >
              <span style="color:red;width:2%;margin-left:2%;margin-right: 2%;"> 案例描述</span>
              <a-textarea style="width:96%" :disabled='!editFlg' v-model="showData.caseDescribe_"></a-textarea>
            </div>
            <div style="display: flex;margin-top:10px;padding-right:6px;">
              <span style="width:2%;margin-left:2%;margin-right: 2%;"> 案例解答</span>
              <a-textarea style="width:96%;" :disabled='!editFlg' v-model="showData.caseAnswer_"></a-textarea>
            </div>
          </div>
          <div style="height:490px;border:2px solid rgb(233, 233, 240);overflow: auto;margin-right:6px;" >
            <div  v-if="editFlg && imgData.length" v-viewer >
              <img :src="item" v-for="(item,index) in imgData" :key="index" style="width: 100px; height: 100px;margin:10px;" @contextmenu.prevent="delClick(item)"/>
            </div>
            <div v-viewer v-else-if="!editFlg && imgData.length">
              <img :src="item" v-for="(item,index) in imgData" :key="index" style="width: 606px; height: 380px;margin:10px;" />
            </div>
            <a-upload
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                list-type="picture-card"
                :file-list="fileList"
                class='pictureListSty'
                @change="handleChange"
                :before-upload='beforeUpload'
                :customRequest="downloadFilesCustomRequest"
                @preview="handlePreview"
                v-if="editFlg"
            >            
              <a-button style=" font-weight: 500;height:40px;" v-if="fileList.length < 3 ">
                上传图片
              </a-button>
              <a-button style="font-weight:500;margin-top:6px;height:40px;" @click.stop="showCopy(1)" title="点击 ctrl+V粘贴上传" v-if="fileList.length < 3 "> 
                粘贴图片
              </a-button>
            </a-upload>
            <a-modal
                :visible="previewVisible"
                :footer="null"
                @cancel="handleCancelPreview"
            >
              <img style="width: 100%;height:100%;" :src="path"  v-if="path"/>
            </a-modal>
          </div>
        </div>
      </div>
      <div class="footerAction" style='user-select: none;'>
        <make-action
          ref="action"
          :type="type" 
          @editClick="editClick"
          @saveClick="saveClick"
          @cancelClick="cancelClick"
          @queryClick="queryClick"
          @addClick="addClick"
          @deleteclick="deleteclick"
        />
      </div>
      <!-- 查询弹窗 -->
      <a-modal
          title="订单查询"
          :visible="dataVisible"
          centered
          @cancel="reportHandleCancel"
          @ok="handleOk"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
          :confirmLoading='confirmLoading'
      >
        <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
            <a-form-item label="关键词">
              <a-input   v-model='KeyWord' placeholder="请输入关键词"  :autoFocus="autoFocus" />
            </a-form-item>

          </a-form>
      </a-modal>
      <!--确认弹窗 -->
      <a-modal
          title="确认弹窗"
          :visible="Popupsvisible"
          centered
          @cancel="Popupsvisible=false"
          @ok="PopupsOk"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
      >
      <span>确认删除该条工程案例吗?</span>
      </a-modal>
    </div>
  </a-spin>
  </div>
  
</template>

<script>
import axios from 'axios'
import MakeAction from "@/pages/gongju/projectCase/modules/MakeAction";
import {caseOrderList, caseAttFile,caseRepait,caseSaveNew,getBtoParameter,deleteengineeringcase } from "@/services/projectCase";
import {UploadFile} from "@/services/projectMake";
const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: 35,
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "案例类型",
    dataIndex: "caseType_",
    align: "left",
    ellipsis: true,
    width: 80,
    className:'userStyle'
  },
  {
    title: "案例简述",
    dataIndex: "caseSketch_",
    align: "left",
    ellipsis: true,
    width:140,
    className:'userStyle'
  },
  {
    title: "建立人",
    dataIndex: "inUser_",
    align: "left",
    ellipsis: true,
    width: 35,
  },
  {
    title: "建立时间",
    dataIndex: "inDate_",
    align: "left",
    ellipsis: true,
    width: 85,
  },
]
export default{
  name:'projectCase',
  components:{MakeAction},
  data(){
    return{
      spinning:false, // 全局加载
      loading:false, // 订单列表加载
      Parameter:[],
      orderListData:[], // 订单列表数据
      columns1,
      guid_:'',
      showData:{}, // 右边列表展示数据
      editFlg:false, // 控制是否可编辑
      fileList:[], // 上传图片集合
      originalFileList:{},
      type: "1",
      previewVisible:false,
      path:null,
      imgData:[],
      code:'',
      confirmLoading:false,
      dataVisible:false,
      autoFocus:true,
      KeyWord:'',
      showCopyType:'',
      Popupsvisible:false,
    }
  },
  mounted(){
    this.getCaseOrderList()
    this.getParameter()
  },
  beforeDestroy(){
      window.removeEventListener('paste', this.getClipboardFiles);
    },
  methods:{
    getParameter(){
      getBtoParameter(615).then(res=>{
        if(res.code){
          this.Parameter=res.data
        }
      })
    },
    // 获取订单列表
    getCaseOrderList(KeyWord){
      let params = ''
      if(KeyWord){
        params = KeyWord
      }
      this.loading = true
      caseOrderList(params).then(res =>{
        if(res.code){
          this.orderListData = res.data
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.loading = false

      })
    },
    // 行点击事件
    onClickRow(record){
      return{
        on:{
          click: () => {
            if(!this.editFlg){
              this.guid_ = record.guid_
              this.showData = record
              if(record.path){
                this.imgData = record.path.split(',')
              }else{
                this.imgData = []
              }
            }else{
              this.$message.warning('编辑状态不可选择其他订单')
            }
          }
        }
      }
    },
    // 行选中背景色
    isRedRow(record){
      if(record.guid_ == this.guid_){
        return 'rowBackgroundColor'
      }else{
        return  ''
      }
    },
    // 编辑
    editClick(){
      if(!this.guid_){
        this.$message.warning('请选择订单')
        return
      }
      this.type = "0";
      this.editFlg = true
      this.originalFileList = JSON.parse(JSON.stringify(this.showData));
    },
    handleChange({ fileList },data) {
      if(!fileList){        
          fileList = data.concat(this.fileList)
        }
      this.fileList = fileList;
      this.path = this.fileList.map(item => {return item.response})
    },
    delClick(item){
      if(confirm('确认删除该图片？')){
        this.imgData.splice(this.imgData.indexOf(item), 1)
      }
    },
    // 取消
    cancelClick(){
      this.type = "1";
      this.editFlg = false
      this.showData = JSON.parse(JSON.stringify(this.originalFileList));
      this.path = this.fileList.map(item => {return item.response})
    },
    // 上传图片路径
    downloadFilesCustomRequest(data){
      const formData = new FormData()
      formData.append('file', data.file)
      caseAttFile(formData).then(res =>{
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.path = this.fileList.map(item => {return item.response})
        }
        else {
          this.$message.error(res.message)
        }
        // console.log( 'this.ChargebackForm.filePaths:',this.ChargebackForm.filePaths)
      })

    },
    // 限制上传格式
    beforeUpload(file){
      const _this = this
      return new Promise(function(resolve, reject) {
        const isJpgOrPng = file.type.toLowerCase() === 'image/jpeg' || file.type.toLowerCase() === 'image/png' || file.type.toLowerCase() === 'image/gif' || file.type.toLowerCase() === 'image/bmp' || file.type.toLowerCase() === 'image/jpg';
        if (!isJpgOrPng) {
          _this.$message.error('图片只支持|*.jpg;*.png;*.gif;*.jpeg;*.bmp 格式');
          reject()
        } else {
          resolve()
        }
      })
    },
    handleCancelPreview () {
      this.previewVisible = false;
    },
     handlePreview (file) {
      this.previewVisible = true;
    },
    // 保存
    saveClick(){
      if(!this.editFlg){
        this.$message.error('当前非编辑状态,不允许保存')
        return
      }
      if(!this.showData.caseType_ || !this.showData.caseSketch_ || !this.showData.caseDescribe_ ){
        this.$message.error('标红项请必填')
        return
      }
      let params = this.showData
      params.path = this.path?this.path.concat(this.imgData).toString():this.imgData.join(',')
      params.caseSort = this.Parameter.filter(item => item.text==params.caseType_)[0]?.valueMember
      if(this.code == '2'){
        caseSaveNew(params).then(res =>{
          if(res.code){
            this.$message.success('新增成功')
            this.imgData = params.path?params.path.split(','):[]            
            this.getCaseOrderList()
          }else{
            this.$message.error(res.message)
          }
          this.code = ''
        })
      }else{
        caseRepait(params).then(res =>{
          if(res.code){
            this.$message.success('编辑成功')
            this.imgData =  params.path?params.path.split(','):[]  
            this.getCaseOrderList()
          }else{
            this.$message.error(res.message)
          }
        })
      }
      this.fileList = []
      this.editFlg = false

    },
    reportHandleCancel(){
      this.dataVisible = false
    },
    //查询
    queryClick(){
      this.dataVisible = true
    },
    handleOk(){
      this.getCaseOrderList(this.KeyWord)
      this.dataVisible = false
    },
    // 新增
    addClick(){
      this.editFlg = true;
      this.showData ={};
      this.imgData = [];
      this.guid_='';
      this.code = '2';
    },
    //删除
    deleteclick(){
      if(!this.guid_){
        this.$message.warning('请选择需要删除的数据')
        return
      }
      if(this.editFlg){
        this.$message.warning('编辑状态不可删除')
        return
      }
      this.Popupsvisible=true
    },
    PopupsOk(){
      deleteengineeringcase(this.guid_).then(res=>{
        if(res.code){
          this.$message.success(res.message)
          this.getCaseOrderList()
          this.showData ={};
          this.imgData = [];
          this.guid_='';
        }else{
          this.$message.error(res.message)      
        }
      })
      this.Popupsvisible=false
    },
    showCopy(type) {     
      window.addEventListener('paste', this.getClipboardFiles);    
      this.showCopyType = type;
      try{    
      navigator.clipboard.read().then( res=>{
          const clipboardItems = res 
           if (clipboardItems[0].types[0].indexOf('image') > -1) {
                clipboardItems[0].getType(clipboardItems[0].types[0]).then( b=>{
                const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {type: clipboardItems[0].types[0]}); 
                this.file = files
                this.getClipboardFiles()                
              });
            }else{
              this.$message.error('粘贴内容不是图片');
              return;
            }
          })
        }catch (e) {
            ////console.log('出错了')        
        }
   
      // this.show = true;
      // this.$nextTick(() => { // 监听粘贴事件
      //   document.getElementById('Msg').addEventListener('paste', this.getClipboardFiles);
      // });
    },    
    bodyClick(){
      //console.log('bodyClick')
      window.removeEventListener('paste', this.getClipboardFiles);
    },    
    getClipboardFiles(event) {
      let file = null;
      if(event){
       const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
        // 检索剪切板items,类数组，不能使用forEach
        for (let i = 0; i < items.length; i += 1) {         
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf('image') !== -1) {
              file = items[i].getAsFile();
            }
        }
      } 
      }else{
        file = this.file
      }
      //console.log('file',file)
      if (!file) {
        this.$message.error('粘贴内容不是图片');
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error('请上传小于10M,并且格式正确的图片');
        return;
      }
      this.beforeUpload(file);   // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload(file)) {  // **** return true 之后进行上传
        const formData = new FormData();
       // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append('file', file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + '/api/app/e-mSTWFEngineering-case/up-load-engineering-case-att-file',   // 接口地址
          method: 'post',
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => { 
            if (res.code) {
              //console.log('res',res)
              file.status = 'done';
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              //console.log('this.showCopyType',this.showCopyType)
              if(this.showCopyType == '1'){
                this.handleChange(file, arr); 
              }          
             
            } else {
              this.$message.error(data.message || '网络异常,请重试或检查网络连接状态');             
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
  }
}
</script>
<style scoped lang="less">
/deep/.ant-form-item{
  margin-bottom: 0;
}
/deep/.ant-table-row-cell-break-word{
  border-right: 1px solid rgb(239, 239, 239);
}
/deep/.ant-table-row-cell-ellipsis{
 border-right: 1px solid rgb(239, 239, 239);
}

/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.ant-input{
  font-weight: 500;
}
.projectCase {
  height: 814px;
  min-width: 1670px;
  // width: 100%;
   //display: flex;
  background: #FFFFFF;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  /deep/.leftContent {
    height:762px;
    user-select: normal;
    width: 40%;
    border: 2px solid rgb(233, 233, 240);
    .min-table {
      .ant-table-body{
        min-height:723px;
        border-bottom: 2px solid #efefef;
      }
    }
    .tabRightClikBox{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }

  }
  /deep/.rightContent {
    padding: 4px;
    width: 60%;
    height:762px;

    border: 2px solid rgb(233, 233, 240);
    }
  .footerAction {
    width: 100%;
    height:60px;
    border: 2px solid #E9E9F0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #FFFFFF;
  }
  /deep/ .userStyle{
    user-select: normal !important;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display:none;
    }
  }
  /deep/ .ant-table-fixed {
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #DC143C;
        }
      }
      .eqBackground {
        background: #FFFF00;
      }
      .statuBackground {
        background: #B0E0E6;
      }
      .backUserBackground {
        background: #A7A2C9;
      }

    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    .userStyle{
      user-select: all !important;
    }

  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
    border-left:2px solid #ff9900!important;
  }
  /deep/ .ant-table{

    .ant-table-thead > tr > th{
      padding: 3px 4px;
      border-color: #f0f0f0;
    }
    .ant-table-tbody > tr > td {
      padding: 3px 4px;
      border-color: #f0f0f0;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #aba5a5;
    }
    .rowBackgroundColor {
      background: #dfdcdc!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 10px 0 0 10px;
  }



}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #D6D6D6;

    p {
      line-height: 35px;
      font-weight: 700;
      margin: 0;
      img{
        width:100%;
      }
    }
    .displayFlag {
      display: none;

    }
  }
}
.modalSty {
  /deep/.ant-modal .ant-modal-content .ant-modal-body {

    padding: 0;
  }
}
</style>
