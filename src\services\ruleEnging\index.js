import { request, METHOD } from '@/utils/request';
// 工程QAE订单列表
export function getTableList (params) {
    return request("/api/app/g-sProduct-class", METHOD.GET,params)
}
// 获取层级选择数据
export  function GetProductClassTree(params){
    return request('/api/app/g-sProduct-class/class-setting-list', METHOD.GET, params)
}

// 新增分类数据
export  function addTable(params){
    return request('/api/app/g-sProduct-class', METHOD.POST, params)
}

// 编辑分类数据
export  function editTable(params){
    return request('/api/app/g-sProduct-class/update', METHOD.POST, params)
}

// 获取配置属性数据
export  function getSettingData(id){
    return request(`/api/app/g-sProduct-class/property-setting-list/${id}`, METHOD.GET)
}

// 保存配置属性
export  function saveSettingData(params){
    return request('/api/app/g-sProduct-class/update-property', METHOD.POST, params)
}

// 获取商品列表
export  function getGoodsList(params){
    return request('/api/app/e-mSGSProduct-list/page-list', METHOD.GET, params)
}

// 新增明细
export  function postAddDetails(params){
    return request('/api/app/e-mSGSProduct-list/product-detail', METHOD.POST, params)
}
// 列表
export  function getDetailsList(params){
    return request('/api/app/e-mSGSProduct-list/product-detail-list', METHOD.GET, params)
}
// 编辑
export  function putEditDetails(params){
    return request('/api/app/e-mSGSProduct-list/update', METHOD.POST, params)
}
