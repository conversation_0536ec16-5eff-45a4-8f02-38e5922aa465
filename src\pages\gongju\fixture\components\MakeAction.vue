<template>
   <div class="active" ref='active'>
    <div class="box showClass"  >
      <a-button type="primary" @click="$emit('queryclick')">
        查询
      </a-button>
    </div> 
     <div class="box showClass"  v-if="checkPermission('MES.ToolModule.JigManagementModule.JigManageStart')" >
      <a-button type="primary" @click="$emit('startclick')">
        开始
      </a-button>
    </div> 
    <div class="box showClass"  v-if="checkPermission('MES.ToolModule.JigManagementModule.JigManageEdit')" >
      <a-button type="primary" v-if="!edit" @click="$emit('editclick')">
        编辑
      </a-button>
       <a-button type="primary" v-if="edit" @click="$emit('cancleclick')">
        取消编辑
      </a-button>
    </div> 
    <div class="box showClass"  v-if="checkPermission('MES.ToolModule.JigManagementModule.JigManageEdit')" >
      <a-button type="primary" @click="$emit('saveclick')">
        保存
      </a-button>
    </div> 
     <div class="box showClass"  v-if="checkPermission('MES.ToolModule.JigManagementModule.JigManageUse')">
      <a-button type="primary" @click="$emit('useclick')">
        领用
      </a-button>
    </div> 
       <div class="box showClass"  v-if="checkPermission('MES.ToolModule.JigManagementModule.JigManageFinish')">
      <a-button type="primary" @click="$emit('finishclick')">
        完成
      </a-button>
    </div> 
    <div class="box showClass">
      <a-button type="primary" @click="$emit('addclick')">
        新增
      </a-button>
    </div> 
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{advanced ? '收起' : '展开'}}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
 <a-dropdown>
   <a-button type="primary"  style="margin-top: 9px;margin-right: 10px;width: 100px;" @click.prevent>
     按钮菜单栏
   </a-button>
   <template #overlay>
     <a-menu class="tabRightClikBox3">
       <a-menu-item >查询</a-menu-item>
       <a-menu-item >开始</a-menu-item>
       <a-menu-item >完成</a-menu-item>
       <a-menu-item >新增</a-menu-item>
       <a-menu-item >编辑</a-menu-item>
       <a-menu-item >保存</a-menu-item>
       <a-menu-item >取消</a-menu-item>
       <a-menu-item >领用</a-menu-item>
     </a-menu>
   </template>
 </a-dropdown>
</div>
    </div>
</template>
<script>
import {checkPermission} from "@/utils/abp";
export default {
    name: '',
    props:['edit'],
    components: {},
    data() {
        return {
        advanced: false,
        showBtn:false, 
        buttonsmenu:false,
        }
    },
    methods: {
    checkPermission,
    toggleAdvanced () {   
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("showClass")      
      if (this.advanced) {
        for (let i = 0; i < elements.length; i++){
          elements[i].style.display = "inline-block"; 
        }
      } else {
        let buttonsToShow = 6
        for (let i = 0; i < elements.length; i++) {
        if (i < buttonsToShow) {
          elements[i].style.display = "inline-block";
        } else {
          elements[i].style.display = "none";          
        }
      }
      }
      this.handleResize()
    },
    handleResize(){
      var elements = document.getElementsByClassName("showClass")     
      let num = ''
      if(!this.advanced){
        num = 7 * 84;
        }else{
        num = (elements.length+1) * 84;
      }
        if(window.innerWidth < 1920 ||  window.innerHeight < 911){
          if(num < window.innerWidth-150  && window.innerWidth>766){
          for (let i = 0; i < elements.length; i++){
            if(i<7){
              elements[i].style.display = "inline-block"; 
            }
        }
        this.buttonsmenu = false
          }else{
          if(window.innerWidth>766){
            for (let i = 0; i < elements.length; i++){
                elements[i].style.display = "none"; 
              }
              this.advanced=false
              this.buttonsmenu = true
          }else{
            if( window.innerWidth-4-num < 70){
              for (let i = 0; i < elements.length; i++){
                elements[i].style.display = "none"; 
                this.buttonsmenu = true
              }
            }else{
              for (let i = 0; i < elements.length; i++){
                if(i<6){
                  elements[i].style.display = "inline-block"; 
                }
                
              }
              this.buttonsmenu = false
            }
           }
          }
        }else{
          for (let i = 0; i < elements.length; i++){
            if(i<6){
              elements[i].style.display = "inline-block"; 
              }
        }
        this.buttonsmenu = false
        }
      
    },
    },
    mounted() {
    window.addEventListener('resize', this.handleResize)
    },
    created() {
    this.$nextTick(()=>{
      const elements = document.getElementsByClassName("showClass")       
      const num = elements.length;  
      let buttonsToShow = 7
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
        if (i < buttonsToShow) {
          elements[i].style.display = "inline-block";
        } else {
          elements[i].style.display = "none";
        }
      }
      } else {
        for (let i = 0; i < elements.length; i++){
          elements[i].style.display = "inline-block"; 
        }
      }
      if(num  <= 7 ){
        this.showBtn = false
      }else{
        this.showBtn = true
        this.advanced = false
      }
      this.handleResize()
    })  
  },
}
</script>
<style lang="less" scoped>
 .tabRightClikBox3{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        width:100px;
        line-height:22px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color:#666666
      }
      li:hover{
        background-color: #ff9900 !important;
        color:white;
        font-size:16px;
        height:32px;
        line-height:22px
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }
.active {
  button {
  text-align: center;
  margin-top: 14px;
  margin-right: 10px;
  }
  height: 50px;
  line-height: 40px;
  float: right;
  display: flex;
  overflow: hidden;  
}

</style>