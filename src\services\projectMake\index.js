import { request, METHOD } from "@/utils/request";
// 工程制作列表
export function projectMakeOrderList(params) {
  return request("/api/app/pro-order-list/engineering-production-list", METHOD.GET, params);
}
// 取单
export function TakeOrderList() {
  return request("/api/app/engineering-make/engineering-production-order", METHOD.GET);
}
// 开始
export function MakeStart(Id) {
  return request(`/api/app/engineering-make/make-start/${Id}`, METHOD.POST);
}
// 退单
export function BackStart(params) {
  return request(`/api/app/engineering-production/back-order`, METHOD.POST, params);
}
// 修改信息
export function getModifyInformation(params) {
  return request(`/api/app/engineering-production/modify-information`, METHOD.POST, params);
}
// 返修完成
export function RepairCompleted(Id) {
  return request(`/api/app/engineering-production/fix-finish/${Id}`, METHOD.POST);
}
// 后端完成
export function BackMakeinish(Id) {
  return request(`/api/app/engineering-backend/back-make-finish/${Id} `, METHOD.POST);
}
// 叠层复制
export function ReorderUpdateStack(Id) {
  return request(`/api/app/engineering-production/re-order-update-stack/${Id}`, METHOD.POST);
}
//下载全套资料
export function datapath(Id) {
  return request(`/api/app/engineering-make/data-path/${Id}`, METHOD.GET);
}
// 导入订单
export function ImportOrder(id) {
  return request(`/api/app/e-mSMake-module-no/order-by-excel`, METHOD.POST, id);
}
// 获取问客地址
export function getWenkeUrl(Id) {
  return request(`/api/app/engineering-production/wen-ke-url/${Id}`, METHOD.GET);
}

// 获取制作标准地址
export function getProductionStandard() {
  return request(`/api/app/engineering-production/fabricati-url`, METHOD.GET);
}
// 上传图片返回地址
export async function UploadFile(params) {
  return request(`/api/app/engineering-production/up-load-back-file`, METHOD.POST, params);
}
// 工程制作订单详情
export function projectBackEndOrderDetail(Id) {
  return request(`/api/app/engineering-backend/pro-order-par/${Id}`, METHOD.GET);
}
// 2023/5/16 更换接口
export function proOrderInfo(Id) {
  return request(`/api/app/engineering-make-info/pro-order-info?OrderNo=${Id}`, METHOD.GET);
}

// 获取工厂Id列表
export async function getFactoryList() {
  return request(`/api/app/e-mSTPub-factory-configure/factory-id-and-code-list`, METHOD.POST);
}
// 注意事项
export async function mattersNeedingAttention(params) {
  return request(`/api/app/engineering-production/matters-needing-attention`, METHOD.POST, params);
}
// 获取参数
export async function getParameter(Id) {
  return request(`/api/app/engineering-production/modify-par/${Id}`, METHOD.GET);
}
// 获取底层铜厚
export async function getBtoParameter(params) {
  return request(`/api/app/e-mSData-class-list/data-class-list/?TypeNo=${params}`, METHOD.POST);
}
// 参数保存
export async function SaveParameter(params) {
  return request(`/api/app/engineering-production/modify-par`, METHOD.POST, params);
}
// 生成叠层
export async function getGenerateStack(Id) {
  return request(`/api/app/engineering-make/auto-stack/${Id}`, METHOD.GET);
}
// 叠层修改保存
export async function saveRepairRecord(params) {
  return request(`/api/app/engineering-production/auto-stack-double-click`, METHOD.POST, params);
}
// 返修记录
export async function getRepairRecord(Id) {
  return request(`/api/app/engineering-production/fix-record/${Id} `, METHOD.GET);
}
// 作业记录
export function projectBackEndJobInfo(Id) {
  return request(`/api/app/engineering-backend/information-transfer/${Id} `, METHOD.GET);
}
// 提交记录
export function getJobAutoInfo() {
  return request(`/api/app/e-mSTPub-task-center`, METHOD.GET);
}
// 叠层阻抗
export function getStackImpedance(Id) {
  return request(`/api/app/engineering-make/imp/${Id}`, METHOD.GET);
}
// 开料拼版
export function getCutting(Id) {
  return request(`/api/app/engineering-make/auto-tool-pnl/${Id}`, METHOD.GET);
}
// 上传GERBER文件
export function UpGerber(Id) {
  return request(`/api/app/engineering-production/up-gerber-file/${Id}`, METHOD.POST);
}
// 下载GERBER文件
export function downGerber(Id) {
  return request(`/api/app/engineering-production/down-load-path/${Id}`, METHOD.GET);
}
// 获取客户信息
export function getCustomerInfo(CustNo, factory, type, businessOrderNo, orderNo) {
  return request(
    `/api/app/order-pre-button/rule-show-info?CustNo=${CustNo}&factory=${factory}&type=${type}&businessOrderNo=${businessOrderNo}&orderNo=${orderNo}`,
    METHOD.GET
  );
}

// 客户规则录入
export function getRuleEntry(Id) {
  return request(`/api/app/engineering-make/att-info/${Id}`, METHOD.GET);
}
// 查看日志
export function getViewLog(params) {
  return request(`/api/app/pro-order-log?OrderId=${params}`, METHOD.GET);
}
// 下载文件
export function downFile(Id) {
  return request(`/api/app/engineering-make/make-start/${Id}`, METHOD.POST);
}

// 获取今日做单情况
export async function makeInfo() {
  return request(`/api/app/engineering-make/make-info`, METHOD.GET);
}
// 文件替换
export async function fileReplacement(Id, params) {
  return request(`/api/app/engineering-make/file-replacement/${Id}`, METHOD.POST, params);
}
// 完成按钮上传文件
export async function upLoadCamFile(Id, params) {
  return request(`/api/app/engineering-make/up-load-cam-file/${Id}`, METHOD.POST, params);
}
// 制作完成确认按钮
export async function finish(params) {
  return request(`/api/app/engineering-production/m-iFinish`, METHOD.POST, params);
}
// 制作完成获取订单参数
// export  async  function modifyPar2(Id){
//     return request(`/api/app/engineering-production/modify-par2/${Id}`, METHOD.GET)
// }
export async function mIFinish(params) {
  // return request(`/api/app/engineering-production/m-iFinish/${Id}`, METHOD.POST)
  return request(`/api/app/engineering-production/m-iFinish`, METHOD.POST, params);
}
//BQ制作完成前调取接口
export async function setcAMMiflowinfostoerp(factory, orderNo) {
  return request(`/api/app/tech-flow-to-erp-app-sevice/set-cAMMi-flow-infos-to-erp/${factory}?orderNo=${orderNo}`, METHOD.POST);
}
// 状态同步
export async function stateSync(Id) {
  return request(`/api/app/engineering-production/state-sync/${Id}`, METHOD.POST);
}
// 删除注意事项
export async function delInfo(id) {
  return request(`/api/app/engineering-production/${id}/del-information-transfer-by-id`, METHOD.POST);
}
//工程制作解除警告
export async function makesetreleasewarning(Id) {
  return request(`/api/app/engineering-make/set-release-warning/${Id}`, METHOD.POST);
}
//确认修改
export async function setsureordermodify(Id) {
  return request(`/api/app/engineering-make/set-sure-order-modify/${Id}`, METHOD.POST);
}
// 获取问客回复订单列表
export function getEqList(JoinFactoryId, OrderNo, BusinessOrderNo) {
  return request(`/api/app/e-mSEQMain/e-qMain/${JoinFactoryId}?OrderNo=${OrderNo}&BusinessOrderNo=${BusinessOrderNo}`, METHOD.GET);
}
// 获取问客问题类型
export function BigClassList(JoinFactoryId, OrderNo, BusinessOrderNo) {
  return request(`/api/app/e-mSEQMain/big-class?factoryid=${JoinFactoryId}&OrderNo=${OrderNo}&BusinessOrderNo=${BusinessOrderNo}`, METHOD.GET);
}
// 获取问客问题类型
export function getClassList(params) {
  return request(`/api/app/e-mSData-class-list/get-class-list`, METHOD.POST, params);
}
// EQ回导
export function emSEQMaineqExcel(JoinFactoryId, OrderNo, BusinessOrderNo, params) {
  return request(`/api/app/e-mSEQMain/e-qExcel/${JoinFactoryId}?OrderNo=${OrderNo}&BusinessOrderNo=${BusinessOrderNo}`, METHOD.POST, params);
}
//订单预审进入问客EQ回导
export function eqExcelmkt(JoinFactoryId, OrderNo, BusinessOrderNo, params, id) {
  return request(
    `/api/app/e-mSEQMain/${id}/e-qExcel-mkt/${JoinFactoryId}?OrderNo=${OrderNo}&BusinessOrderNo=${BusinessOrderNo}`,
    METHOD.POST,
    params
  );
}

// 获取问客配置内容
export function eqQuestion(params) {
  return request(`/api/app/e-mSEQMain/e-qQuestion-config`, METHOD.GET, params);
}
// 问客上传文件
export function upLoadFlyingFile(params) {
  return request(`/api/app/e-mSEQMain/up-load-flying-file`, METHOD.POST, params);
}
// 问客上传附件
export function upeQAttachment(params) {
  return request(`/api/app/e-mSEQMain/up-eQAttachment`, METHOD.POST, params);
}
//问客下载附件地址
export function downeQAttachment(params) {
  return request(`/api/app/e-mSEQMain/down-eQAttachment`, METHOD.POST, params);
}
// 问客上传图片
export function submit(params) {
  return request(`/api/app/e-mSEQMain/submit`, METHOD.POST, params);
}
// 编辑问题
export function editingProblems(params) {
  return request(`/api/app/e-mSEQMain/editing-problems`, METHOD.POST, params);
}
// 回复问题
export function replyProblems(params) {
  return request(`/api/app/e-mSEQMain/reply-problems`, METHOD.POST, params);
}
// 用户问客详情回复问题
export function replyproblembycust(params) {
  return request(`api/app/e-mSEQMain/reply-problem-by-cust`, METHOD.POST, params);
}
//用户问客详情回复按钮
export function replyproblemsbycust(params) {
  return request(`api/app/e-mSEQMain/reply-problems-by-cust`, METHOD.POST, params);
}
// 发送问题
export function sendProblems(params) {
  return request(`/api/app/e-mSEQMain/send-problems`, METHOD.POST, params);
}
// 一键发送问题
export function toSendProblems(params) {
  return request(`/api/app/e-mSEQMain/to-send`, METHOD.POST, params);
}
//撤回问题
export function yjbackproblems(params) {
  return request(`/api/app/e-mSEQMain/yj-back-problems`, METHOD.POST, params);
}
// 复制链接
export function timestampVerifyCode(OrderNo) {
  return request(`api/app/e-mSEQMain/timestamp-verify-code`, METHOD.GET);
}
//获取proOrder订单信息
export function proorderinformation(JoinFactoryId, params) {
  return request(`api/app/pro-order/pro-order-information/${JoinFactoryId}`, METHOD.GET, params);
}
export function eqTemplate(JoinFactoryId, params) {
  return request(`/api/app/pro-order/e-qTemplate/${JoinFactoryId}`, METHOD.GET, params);
}
//同步邮箱信息
export function teQMail(id) {
  // return request(`/api/app/e-mSEQMain/${id}/t-eQMail`, METHOD.POST)
  return request(`/api/app/e-mSEQMain/${id}/update-tEQMail`, METHOD.POST);
}
// 客户问客列表
export function eqMainByCust(JoinFactoryId, OrderNo, Timestamp, VerifyCode, BusinessOrderNo) {
  return request(
    `/api/app/e-mSEQMain/e-qMain-by-cust/${JoinFactoryId}?OrderNo=${OrderNo}&Timestamp=${Timestamp}&VerifyCode=${VerifyCode}&BusinessOrderNo=${BusinessOrderNo}`,
    METHOD.GET
  );
}
// 撤回问题
export function backProblems(params) {
  return request(`/api/app/e-mSEQMain/back-problems`, METHOD.POST, params);
}
//删除问题
export function setdeleetproblems(params) {
  return request(`/api/app/e-mSEQMain/set-deleet-problems`, METHOD.POST, params);
}
// 撤回问题
export function eqXls(params) {
  return request(`/api/app/e-mSEQMain/e-qXls?OrderNo=${params}`, METHOD.GET);
}
// 获取添加问题操作人
export function user(params) {
  return request(`/api/app/e-mSEQMain/user`, METHOD.GET);
}
//
export function exportEQReport(JoinFactoryId, OrderNo, type, eQSource, BusinessOrderNo) {
  return request(
    `/api/app/e-mSEQMain/export-eQReport/${JoinFactoryId}?orderNo=${OrderNo}&type=${type}&eQSource=${eQSource}&BusinessOrderNo=${BusinessOrderNo}`,
    METHOD.POST
  );
}
//EQ回导新接口
export function exportEQReportv2(JoinFactoryId, OrderNo, type, eQSource, BusinessOrderNo) {
  return request(
    `api/app/e-mSEQMain/export-eQReport-v2/${JoinFactoryId}?orderNo=${OrderNo}&type=${type}&eQSource=${eQSource}&BusinessOrderNo=${BusinessOrderNo}`,
    METHOD.POST
  );
}
//BQ 客户端EQ下载接口
export function exportEQReportv3(JoinFactoryId, OrderNo, type, eQSource, BusinessOrderNo) {
  return request(
    `/api/app/e-mSEQMain/export-eQReport-v3/${JoinFactoryId}?orderNo=${OrderNo}&type=${type}&eQSource=${eQSource}&BusinessOrderNo=${BusinessOrderNo}`,
    METHOD.POST
  );
}
// 测试文件
export function gerberDownloadPath(params) {
  return request(`/api/app/engineering-production/gerber-download-path?OrderNo=${params}`, METHOD.GET);
}
//获取生产备注
export function processstepnotes(params) {
  return request(`/api/app/engineering-production/process-step-notes?orderNo=${params}`, METHOD.GET);
}
//系数倍数修改
export function multiples(params) {
  return request(`/api/app/engineering-production/set-score-multiples`, METHOD.POST, params);
}
// 返修登记
//列表
export function getFixRecord(Pid) {
  return request(`/api/app/e-mSTPpe-fix-record?Pid=${Pid}`, METHOD.GET);
}
//新增
export function addFixRecord(params) {
  return request(`/api/app/e-mSTPpe-fix-record`, METHOD.POST, params);
}
// 图片、文件上传
export function upLoadFixFile1(params) {
  return request(`/api/app/e-mSTPpe-fix-record/up-load-fix-file`, METHOD.POST, params);
}
//修改
export function update1(params) {
  return request(`/api/app/e-mSTPpe-fix-record/update`, METHOD.POST, params);
}
//删除
export function deleteById(id) {
  return request(`/api/app/e-mSTPpe-fix-record/${id}/delete-by-id`, METHOD.POST);
}
//市场修改提交
export function Marketmake(params) {
  return request(`/api/app/engineering-production/set-order-modify-list`, METHOD.POST, params);
}
export function eqTemplteSelectList(OrderNo, eQSource) {
  return request(`/api/app/e-mSEQMain/e-qTemplte-select-list?OrderNo=${OrderNo}&eQSource=${eQSource}`, METHOD.GET);
}
export function coefficient(Id, type) {
  return request(`/api/app/pro-order/coefficient/${Id}?type=${type}`, METHOD.GET);
}
export function setBCCoefficient(type, params) {
  return request(`/api/app/pro-order/set-bc-coefficient?type=${type}`, METHOD.POST, params);
}
export function checkCoefficient(id) {
  return request(`/api/app/pro-order/${id}/check-coefficient`, METHOD.POST);
}
//补偿系数删除
export function bccoefficientdel(id) {
  return request(`/api/app/pro-order/${id}/bc-coefficient-del`, METHOD.POST);
}
export function repierMatters(params) {
  return request(`/api/app/engineering-production/repier-matters-needing-attention`, METHOD.POST, params);
}
// 工程按钮检查
export function ppebuttonCheck(Id, OfflineOrder, ProName) {
  return request(`/api/app/engineering-make/ppe-button-check/${Id}?buttonname=${OfflineOrder}&ProName=${ProName}`, METHOD.POST);
}
//制作完成是否qae
export function isqaeenble(Id) {
  return request(`/api/app/engineering-make/is-qae-enble/${Id}`, METHOD.GET);
}
//返修问题获取接口
export function fixquestionconfig(params) {
  return request(`/api/app/e-mSTPpe-fix-record/fix-question-config`, METHOD.GET, params);
}
export default {
  projectMakeOrderList,
  isqaeenble,
  TakeOrderList,
  ppebuttonCheck,
  downeQAttachment,
  upeQAttachment,
  MakeStart,
  BackStart,
  getModifyInformation,
  projectBackEndOrderDetail,
  proOrderInfo,
  projectBackEndJobInfo,
  getFactoryList,
  RepairCompleted,
  BackMakeinish,
  ReorderUpdateStack,
  getWenkeUrl,
  getProductionStandard,
  UploadFile,
  mattersNeedingAttention,
  getParameter,
  SaveParameter,
  getBtoParameter,
  getGenerateStack,
  saveRepairRecord,
  getRepairRecord,
  getStackImpedance,
  getCutting,
  getCustomerInfo,
  getRuleEntry,
  UpGerber,
  downGerber,
  getViewLog,
  downFile,
  makeInfo,
  fileReplacement,
  upLoadCamFile,
  finish,
  stateSync,
  delInfo,
  getEqList,
  eqQuestion,
  getClassList,
  emSEQMaineqExcel,
  BigClassList,
  upLoadFlyingFile,
  submit,
  editingProblems,
  replyProblems,
  sendProblems,
  backProblems,
  toSendProblems,
  yjbackproblems,
  eqXls,
  user,
  exportEQReport,
  gerberDownloadPath,
  processstepnotes,
  multiples,
  eqExcelmkt,
};
