<!-- 市场管理 - 客户管理 -->
<template>
  <a-card class="box" :bordered="false">
    <div class="box_left">
      <div class="operator" ref="active">
        <a-input
          placeholder="客户名称"
          v-model="formNew.custname"
          style="width: 130px; margin-left: 7px; margin-right: 5px; margin-top: 8px"
          @keyup.enter.native="handleOk"
          allowClear
        ></a-input>
        <a-input
          placeholder="客户代码"
          v-model="formNew.custcode"
          style="width: 130px; margin-left: 5px; margin-right: 5px"
          @keyup.enter.native="handleOk"
          allowClear
        ></a-input>
        <a-button type="primary" @click="handleOk" style="margin-left: 5px; margin-right: 5px" class="showClass"> 查询 </a-button>
        <a-button type="primary" @click="handleOk('reset')" style="margin-left: 5px; margin-right: 5px" class="showClass"> 重置 </a-button>
        <a-button
          v-show="checkPermission('MES.MarketModule.CustomerManagement.CustAdd')"
          icon="plus"
          type="primary"
          @click="add()"
          style="margin-left: 5px; margin-right: 5px"
          :class="checkPermission('MES.MarketModule.CustomerManagement.CustAdd') ? 'showClass' : ''"
          >新建</a-button
        >
        <a-button
          type="primary"
          v-show="checkPermission('MES.MarketModule.CustomerManagement.CustDel')"
          @click="delClick()"
          style="margin-left: 5px; margin-right: 5px"
          :class="checkPermission('MES.MarketModule.CustomerManagement.CustDel') ? 'showClass' : ''"
          >删除</a-button
        >
        <a-button
          type="primary"
          v-show="checkPermission('MES.MarketModule.CustomerManagement.IsIntention')"
          @click="becomeClick()"
          style="margin-left: 5px; margin-right: 5px"
          :class="checkPermission('MES.MarketModule.CustomerManagement.IsIntention') ? 'showClass' : ''"
          >意向转正</a-button
        >
        <a-button
          type="primary"
          v-show="checkPermission('MES.MarketModule.CustomerManagement.SynchronousErpCustInfo')"
          @click="erpsynchronous()"
          style="margin-left: 5px; margin-right: 5px"
          :class="checkPermission('MES.MarketModule.CustomerManagement.SynchronousErpCustInfo') ? 'showClass' : ''"
          >ERP同步</a-button
        >
        <a-button
          type="primary"
          v-show="checkPermission('MES.MarketModule.CustomerManagement.SetIshmd')"
          @click="Blacklistsettings()"
          style="margin-left: 5px; margin-right: 5px"
          :class="checkPermission('MES.MarketModule.CustomerManagement.SetIshmd') ? 'showClass' : ''"
          >列为黑名单</a-button
        >
        <a-button
          type="primary"
          v-show="checkPermission('MES.MarketModule.CustomerManagement.SecureIshmd')"
          @click="Blacklistlifting()"
          :class="checkPermission('MES.MarketModule.CustomerManagement.SecureIshmd') ? 'showClass' : ''"
          >解除黑名单</a-button
        >
        <span class="box" v-if="showBtn">
          <a-button type="dashed" @click="toggleAdvanced"> {{ advanced ? "收起" : "展开" }} <a-icon :type="advanced ? 'right' : 'left'" /> </a-button>
        </span>
        <span v-if="buttonsmenu">
          <a-dropdown>
            <a-button type="primary" style="margin-top: 9px; margin-right: 10px" @click.prevent> 按钮菜单栏 </a-button>
            <template #overlay>
              <a-menu class="tabRightClikBox3">
                <a-menu-item @click="add()" v-if="checkPermission('MES.MarketModule.CustomerManagement.add')">新建</a-menu-item>
                <a-menu-item @click="delClick()" v-if="checkPermission('MES.MarketModule.CustomerManagement.CustDel')">删除</a-menu-item>
                <a-menu-item @click="becomeClick()" v-if="checkPermission('MES.MarketModule.CustomerManagement.IsIntention')">意向转正</a-menu-item>
                <a-menu-item @click="erpsynchronous()" v-if="checkPermission('MES.MarketModule.CustomerManagement.SynchronousErpCustInfo')"
                  >ERP同步</a-menu-item
                >
                <a-menu-item @click="Blacklistsettings()" v-if="checkPermission('MES.MarketModule.CustomerManagement.SetIshmd')"
                  >列为黑名单</a-menu-item
                >
                <a-menu-item @click="Blacklistlifting()" v-if="checkPermission('MES.MarketModule.CustomerManagement.SecureIshmd')"
                  >解除黑名单</a-menu-item
                >
              </a-menu>
            </template>
          </a-dropdown>
        </span>
      </div>
      <div class="content" ref="tableWrapper">
        <a-table
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          @change="handleTableChange"
          :pagination="pagination"
          :loading="loading"
          class="leftstyle"
          :customRow="onClickRow"
          :rowClassName="isRedRow"
          :scroll="{ y: 700, x: 1650 }"
        >
          <template slot="ishmd" slot-scope="text, record">
            <a-checkbox :checked="record.ishmd" />
          </template>
          <template slot="labelUrl" slot-scope="record">
            <span @click.stop="OperationLog(record)" style="color: #428bca">日志</span>
          </template>
          <template slot="num" slot-scope="text, record, index">
            {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
          </template>
          <template slot="action" slot-scope="record">
            <a href="javascript:;" @click="rowClick(record)">详情</a>
          </template>
        </a-table>
        <right-copy ref="RightCopy" />
      </div>
      <div class="bto" style="user-select: none"></div>
    </div>
    <!-- 操作日志弹窗 -->
    <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose :maskClosable="false" @cancel="reportHandleCancel" :width="800" centered>
      <div class="projectackend">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
        </a-table>
      </div>
      <template slot="footer">
        <a-button type="primary" @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
    <!-- 确认弹窗 -->
    <a-modal
      title=" 确认弹窗"
      :visible="dataVisible3"
      @cancel="reportHandleCancel"
      @ok="handleOk3"
      ok-text="确定"
      cancel-text="取消(C)"
      :confirmLoading="moloading"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span style="font-size: 16px">【{{ orderno }}】</span>
      <span style="font-size: 16px">{{ message }}</span>
    </a-modal>
    <a-modal
      title="ERP同步"
      :visible="erpvisible"
      @cancel="reportHandleCancel"
      @ok="erphandleOk"
      ok-text="确定"
      :confirmLoading="moloading"
      cancel-text="取消"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form-model-item label="客户代码：" :labelCol="{ span: 5 }" :wrapperCol="{ span: 17 }">
        <a-input v-model="erpcustno" :autoFocus="true" allowClear />
      </a-form-model-item>
    </a-modal>
  </a-card>
</template>
<script>
import {
  getList,
  getCustConfig,
  deleteCust,
  isIntention,
  synchronouserpcustinfo,
  emSCustmodulenolog,
  secureishmd,
  setishmd,
} from "@/services/mkt/CustInfoNew";
import RightCopy from "@/pages/RightCopy";
import { requiredLinkConfig } from "@/services/mkt/orderInfo";
import { checkPermission } from "@/utils/abp";
import { mapState } from "vuex";
import Cookie from "js-cookie";
import moment from "moment";
export default {
  name: "ClientOperating",
  components: { RightCopy },
  inject: ["reload"],
  data() {
    return {
      moloading: false,
      buryingpoint: [],
      isCtrlPressed: false,
      advanced: false,
      width: 910,
      erpcustno: "",
      showBtn: false,
      dataList: [],
      loading: false,
      laborcolumns: [
        {
          title: "序号",
          align: "center",
          dataIndex: "index",
          key: "index",
          width: 20,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "操作时间",
          align: "left",
          dataIndex: "createTime",
          width: 65,
        },
        {
          title: "操作人",
          align: "left",
          dataIndex: "userName",
          width: 30,
        },
        {
          title: "内容",
          align: "left",
          dataIndex: "content",
          width: 185,
        },
      ],
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "num" },
          key: "index",
          // align:'center',
          width: 45,
          className: "userStyle",
          align: "center",
        },
        {
          title: "工厂",
          dataIndex: "factoryName",
          width: 80,
          className: "userStyle",
        },
        {
          title: "客户代码",
          dataIndex: "custNo",
          width: 80,
          className: "userStyle",
        },
        {
          title: "客户名称",
          dataIndex: "custName",
          width: 300,
          className: "userStyle",
        },
        {
          title: "销售工程师",
          dataIndex: "functionaryStr",
          width: 80,
          className: "userStyle",
        },
        {
          title: "跟单员(业助)",
          dataIndex: "ddgdyStr",
          width: 80,
          className: "userStyle",
        },
        {
          title: "黑客户",
          dataIndex: "ishmd",
          scopedSlots: { customRender: "ishmd" },
          align: "center",
          ellipsis: true,
          width: 40,
          className: "userStyle",
        },
        {
          title: "原始客户代码",
          dataIndex: "origCustNo",
          align: "left",
          ellipsis: true,
          width: 100,
          className: "userStyle",
        },
        {
          title: "创建人",
          dataIndex: "createName",
          width: 100,
          className: "userStyle",
        },
        {
          title: "创建时间",
          dataIndex: "createTime",
          width: 120,
          className: "userStyle",
        },
        {
          title: "操作列表",
          scopedSlots: { customRender: "action" },
          width: 50,
          className: "userStyle noCopy",
        },
        {
          title: "日志",
          scopedSlots: { customRender: "labelUrl" },
          width: 30,
          className: "userStyle noCopy",
        },
      ],
      visible: false,
      menuData: {},
      dataSource: [],
      record: {}, //点击的顾客信息
      guid_: "", //顾客id
      Qinji: "",
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      addName: "",
      addPhone: "",
      address: "",
      formInline: [],
      labordata: [],
      labordataVisible: false,
      // layout: {
      //   labelCol: { span: 8 },
      //   wrapperCol: { span: 12 },
      // },
      formNew: {
        custcode: "", //查询信息
        custname: "",
      },
      dataVisible3: false,
      erpvisible: false,
      message: "",
      orderno: "",
      type: "",
      deletId: "",
      selectedRowKeysArray: [],
      selectedRowsData: [],
      queryData: {},
      requiredCustomConfig: {},
      buttonsmenu: false,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      if (elements.length > 8) {
        for (let i = 0; i < elements.length; i++) {
          if (i < 8) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 8) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
      elements[0].style.display = "inline-block";
      if (elements.length * 95 + 500 < window.innerWidth) {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      } else {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
        this.buttonsmenu = true;
      }
      var leftContent = document.getElementsByClassName("content")[0];
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 175 + "px";
      } else {
        leftContent.style.height = "738px";
      }
    });
    this.getdataList();
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  mounted() {
    this.getRequiredLink();
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var leftstyle = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftContent = document.getElementsByClassName("content")[0];
      const elements = document.getElementsByClassName("showClass");
      elements[0].style.display = "inline-block";
      if (elements.length * 95 + 500 < window.innerWidth) {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
        this.buttonsmenu = false;
      } else {
        for (let i = 1; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
        this.buttonsmenu = true;
      }
      if (leftstyle && this.dataSource.length != 0) {
        leftstyle.style.height = window.innerHeight - 214 + "px";
      } else {
        leftstyle.style.height = 0;
      }
      if (window.innerHeight <= 911) {
        leftContent.style.height = window.innerHeight - 175 + "px";
      } else {
        leftContent.style.height = "738px";
      }
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      if (window.innerWidth < 1920) {
        if (paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "13" && this.dataVisible3 && !this.isCtrlPressed) {
        this.handleOk3();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "67" && this.dataVisible3) {
        this.reportHandleCancel();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          if (i < 8) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    handleCancel() {
      this.visible = false;
    },
    //获取列表
    getdataList() {
      this.loading = true;
      let parmas = {
        ...this.queryData,
      };
      parmas.PageIndex = this.pagination.current;
      parmas.PageSize = this.pagination.pageSize;
      getList(parmas)
        .then(res => {
          if (res.code) {
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            pagination.current = res.data.pageIndex;
            this.pagination = pagination;
            this.dataSource = res.data.items;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            for (var x = 0; x < this.dataSource.length; x++) {
              this.dataSource[x].factoryName;
            }
          } else {
            //this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    OperationLog(record) {
      this.labordataVisible = true;
      emSCustmodulenolog(record.id).then(res => {
        if (res.code) {
          this.labordata = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //详情
    rowClick(record) {
      let factory = record.tradeType;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("requiredCustomConfig"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.requiredCustomConfig = data[a].data; //本地缓存
          }
        }
      } else {
        requiredLinkConfig(factory, 1).then(res => {
          if (res.code) {
            this.requiredCustomConfig = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.requiredCustomConfig) != "{}") {
              if (data == null) {
                arr.push({ data: this.requiredCustomConfig, token, factory });
                localStorage.setItem("requiredCustomConfig", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.requiredCustomConfig, token, factory });
                localStorage.setItem("requiredCustomConfig", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
      this.record = record;
      this.guid_ = record.id;
      this.Qinji = record.factoryName;
      this.$router.push({
        path: "/shichang/CRMNewDetail",
        query: { factoryName: this.Qinji, id: this.guid_, tradeType: record.tradeType, custNo: record.custNo },
      });
    },
    // 删除客户
    delClick() {
      if (!this.selectedRowKeysArray[0]) {
        this.$message.warning("请选择客户");
        return;
      }
      this.orderno = this.selectedRowsData.custNo;
      this.dataVisible3 = true;
      this.message = "确认删除该客户？";
      this.type = "1";
    },
    //意向转正
    becomeClick() {
      if (!this.selectedRowKeysArray[0]) {
        this.$message.warning("请选择客户");
        return;
      }
      this.orderno = this.selectedRowsData.custNo;
      this.dataVisible3 = true;
      this.message = "确认转正？";
      this.type = "2";
    },
    //erp同步
    erpsynchronous() {
      if (!this.selectedRowKeysArray[0]) {
        this.erpvisible = true;
        this.erpcustno = "";
      } else {
        this.orderno = this.selectedRowsData.custNo;
        this.dataVisible3 = true;
        this.message = "确认ERP同步吗?";
        this.type = "3";
      }
    },
    //黑名单设置
    Blacklistsettings() {
      if (!this.selectedRowKeysArray[0]) {
        this.$message.warning("请选择客户");
        return;
      }
      setishmd(this.selectedRowsData.id).then(res => {
        if (res.code) {
          this.$message.success("设置黑名单成功");
          this.getdataList();
          this.selectedRowKeysArray = [];
          this.selectedRowsData = [];
          this.deletId = "";
        } else {
          this.$message.error(res.message);
        }
      });
      // this.orderno = this.selectedRowsData.custNo
      // this.dataVisible3 = true;
      // this.message = '确认设置为黑名单客户吗?'
      // this.type = '4'
    },
    //黑名单解除
    Blacklistlifting() {
      if (!this.selectedRowKeysArray[0]) {
        this.$message.warning("请选择客户");
        return;
      }
      secureishmd(this.selectedRowsData.id).then(res => {
        if (res.code) {
          this.$message.success("黑名单解除成功");
          this.getdataList();
          this.selectedRowKeysArray = [];
          this.selectedRowsData = [];
          this.deletId = "";
        } else {
          this.$message.error(res.message);
        }
      });
      // this.orderno = this.selectedRowsData.custNo
      // this.dataVisible3 = true;
      // this.message = '确认解除黑名单吗?'
      // this.type = '5'
    },
    reportHandleCancel() {
      this.dataVisible3 = false;
      this.labordataVisible = false;
      this.erpvisible = false;
    },
    erphandleOk() {
      this.erpcustno = this.erpcustno ? this.erpcustno : "";
      this.moloading = true;
      synchronouserpcustinfo(this.erpcustno, this.user.factoryId)
        .then(res => {
          if (res.code) {
            this.$message.success("ERP同步成功");
            this.getdataList();
            this.selectedRowKeysArray = [];
            this.selectedRowsData = [];
            this.deletId = "";
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.moloading = false;
        });
      this.erpvisible = false;
    },
    handleOk3() {
      // 删除
      this.moloading = true;
      if (this.type == "1") {
        deleteCust(this.deletId)
          .then(res => {
            if (res.code) {
              this.$message.success("删除成功");
              this.getdataList();
              this.selectedRowKeysArray = [];
              this.selectedRowsData = [];
              this.deletId = "";
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.moloading = false;
          });
      }
      // 转正
      if (this.type == "2") {
        isIntention(this.deletId)
          .then(res => {
            if (res.code) {
              this.$message.success("转正成功");
              this.getdataList();
              this.selectedRowKeysArray = [];
              this.selectedRowsData = [];
              this.deletId = "";
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.moloading = false;
          });
      }
      //ERP同步
      if (this.type == "3") {
        synchronouserpcustinfo(this.orderno, this.selectedRowsData.tradeType)
          .then(res => {
            if (res.code) {
              this.$message.success("ERP同步成功");
              this.getdataList();
              this.selectedRowKeysArray = [];
              this.selectedRowsData = [];
              this.deletId = "";
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.moloading = false;
          });
      }
      // //黑名单设置
      // if(this.type == '4'){
      //   setishmd(this.selectedRowsData.id).then(res=>{
      //     if(res.code){
      //       this.$message.success('设置黑名单成功')
      //       this.getdataList()
      //       this.selectedRowKeysArray = []
      //       this.selectedRowsData =[]
      //       this.deletId = ''
      //     }else{
      //       this.$message.error(res.message)
      //     }
      //   })
      // }
      // if(this.type == '5'){
      //   secureishmd(this.selectedRowsData.id).then(res=>{
      //     if(res.code){
      //       this.$message.success('黑名单解除成功')
      //       this.getdataList()
      //       this.selectedRowKeysArray = []
      //       this.selectedRowsData =[]
      //       this.deletId = ''
      //     }else{
      //       this.$message.error(res.message)
      //     }
      //   })
      // }
      this.dataVisible3 = false;
    },

    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.id && record.id == this.deletId) {
        strGroup.push("rowBackgroundColor");
      }

      return str.concat(strGroup);
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.deletId = record.id;
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e, record);
          },
        },
      };
    },
    //刷新
    del() {
      this.reload();
    },
    //新增
    add() {
      this.$router.push({ path: "/shichang/CRMNewDetail", query: { id: "", tradeType: this.user.factoryId } });
    },

    //查询
    showModal() {
      this.visible = true;
      this.pagination.current = 1;
    },
    handleOk(type) {
      if (type == "reset") {
        this.formNew.custname = "";
        this.formNew.custcode = "";
      }
      if (!this.formNew.custname && !this.formNew.custcode && type != "reset") {
        this.$message.error("请输入客户名称或客户代码再进行查询");
        return;
      }
      this.queryData = this.formNew;
      this.visible = false;
      this.formNew.PageIndex = "1";
      this.formNew.PageSize = this.pagination.pageSize;
      var arr1 = this.formNew.custname.split("");
      if (arr1.length > 30) {
        arr1 = arr1.slice(0, 30);
      }
      this.formNew.custname = arr1.join("");
      var arr2 = this.formNew.custcode.split("");
      if (arr2.length > 10) {
        arr2 = arr2.slice(0, 10);
      }
      this.formNew.custcode = arr2.join("");
      getList(this.formNew).then(res => {
        this.dataSource = res.data.items;
        if ((this.formNew.custname || this.formNew.custcode) && this.dataSource.length) {
          this.selectedRowKeysArray[0] = this.dataSource[0].id;
          this.deletId = this.dataSource[0].id;
          this.selectedRowsData.custNo = this.dataSource[0].custNo;
          this.selectedRowsData.tradeType = this.dataSource[0].tradeType;
          this.selectedRowsData = this.dataSource[0];
        } else {
          this.selectedRowKeysArray = [];
          this.deletId = "";
          this.selectedRowsData.custNo = "";
          this.selectedRowsData.tradeType = "";
          this.selectedRowsData = {};
        }
        this.pagination.current = 1;
        this.pagination.total = res.data.totalCount;
        setTimeout(() => {
          this.handleResize();
        }, 0);
      });
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      localStorage.removeItem("stat");
      if (this.queryData != "{}") {
        this.getdataList(this.queryData);
      } else {
        this.getdataList();
      }
    },
    // handleTableChange(pagination, filters, sorter) {
    //   this.pagination.current=pagination.current
    //     this.getdataList()
    // },
    // 获取表格对应的行id
    Rowclick(record, index) {
      return {
        on: {
          click: () => {
            if (!this.stat) {
              if (this.trIndex == null) {
                this.trIndex = index;
                document.getElementsByClassName("ant-table-row")[index].style.background = "#FFF9E6";
              } else if (this.trIndex != index) {
                document.getElementsByClassName("ant-table-row")[this.trIndex].style.background = "";
                document.getElementsByClassName("ant-table-row")[index].style.background = "#FFF9E6";
                this.trIndex = index;
              }
            }
            this.record = record;
            this.guid_ = record.guid_;
            this.$router.push({ path: "/shichang/CRMNewDetail", query: { id: this.guid_ } });
          },
        },
      };
    },
    getRequiredLink() {
      var factory = "";
      if (this.tradeType) {
        factory = this.tradeType;
      } else {
        factory = this.user.factoryId;
      }
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("requiredCustomConfig"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.requiredCustomConfig = data[a].data; //本地缓存
          }
        }
      } else {
        requiredLinkConfig(factory, 1).then(res => {
          if (res.code) {
            this.requiredCustomConfig = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.requiredCustomConfig) != "{}") {
              if (data == null) {
                arr.push({ data: this.requiredCustomConfig, token, factory });
                localStorage.setItem("requiredCustomConfig", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.requiredCustomConfig, token, factory });
                localStorage.setItem("requiredCustomConfig", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.showClass {
  width: 83px;
  padding: 0;
}
/deep/.ant-table-pagination {
  float: left;
  position: absolute;
  margin: 5px 8px;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc !important;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectackend {
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
/deep/.ant-table .ant-table-tbody > tr > td {
  height: 34px;
}
/deep/.userStyle {
  user-select: none !important;
}
.tabRightClikBox {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.ant-input {
  font-weight: 500;
}
/deep/.rowBackgroundColor {
  background: #dfdcdc !important;
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 2px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 7px 2px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-card-body {
  padding: 0px !important;
}
.operator {
  margin-bottom: 0px;
  height: 49px;
  padding-left: 6px;
  background: #ffffff;
}
.box_left {
  width: 100%;
  z-index: 10;
}
.content {
  border: 2px solid #e9e9f0;
  /deep/.mintable {
    .ant-table-pagination.ant-pagination {
      margin: 9px 0;
      z-index: 99;
      position: absolute;
      bottom: -54px;
      margin-left: 1%;
    }
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-tbody > tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
}
.bto {
  width: 100%;
  height: 42px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background: #ffffff;
  border-top: 0;
}
.form {
  width: 50%;
  padding-top: 20px;
  max-height: calc(100vh - 230px);
  overflow-y: auto;
}
.forms {
  width: 100%;
  padding-top: 20px;
  // margin-bottom: 300px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}
</style>
