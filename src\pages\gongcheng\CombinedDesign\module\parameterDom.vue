<!-- 工程管理 - 合拼设计 -配置参数 -->
<template>
  <a-table
      :rowKey="(record,index)=>{return index}"
      :columns="columns4"
      :dataSource="data7Source"
      :pagination="false"
      :keyboard="false"
      :bordered="true"
      :maskClosable="false"
      :scroll="{ x: 500, y:555 }"
  >
    <template slot="fristValue_" slot-scope="text,record">
      <a-input v-model="record.fristValue_"  allowClear>
      </a-input>
    </template>
  </a-table>
</template>

<script>
const columns4 = [
  {
    dataIndex: "index",
    title: '',
    key: 'index',
    width: 14,
    align: 'center',
    // scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "参数项目",
    width: 60,
    dataIndex: "captions_",
    align: 'left',
    ellipsis: true,
  },
  {
    title: "参数",
    width: 60,
    // dataIndex: "fristValue_",
    align: 'left',
    ellipsis: true,
    scopedSlots: { customRender: 'fristValue_' },
  },


]
export default {
    name:'parameterDom',
  props:['data7Source'],
  data() {
    return {
      columns4,
      configureData:[]

    };
  },
  created() {
    this.$nextTick(function () {
      this.configureData = this.data7Source
    })

  },
  methods: {
    // 配置参数编辑更改
    fristValueChange(record){
      let arr = this.data7Source.filter(item => item.captions_ != record.captions_);
      arr.push(record)
      this.configureData = arr
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-table-row-cell-ellipsis .ant-table-column-title{
  color: #000000;
}
 /deep/ .ant-table-thead > tr > th{
    padding: 2px 2px!important;
    overflow-wrap: break-word;
}
/deep/ .ant-table-tbody > tr > td {
    color: #000000;
    padding: 2px 2px!important;
    overflow-wrap: break-word;
}
</style>
