<!-- 工程管理 - 合拼设计 -订单列表 -->
<template>
  <a-card :bordered="false">
    <div class="machine" style="border: 0" ref="tableWrapper">
      <div class="left" style="border: 0">
        <!-- <span class="iconstyle">
          <a-tooltip  @click="caretup"  v-if="fold" title="展开合拼池订单"><a-icon type="unordered-list"  style="font-size: 24px;"></a-icon></a-tooltip>
          <a-tooltip   @click="caretdown" v-if="!fold" title="折叠合拼池订单"><a-icon type="unordered-list"  style="font-size: 24px;"></a-icon></a-tooltip>
        </span>  -->
        <a-card class="top" style="border: 1px solid #e9e9f0">
          <vxe-table
            border
            stripe
            show-overflow
            ref="xTable2"
            :height="right1height"
            :data="data3Source"
            :loading="loading1"
            :row-class-name="rowClassName"
            :row-config="{ isHover: true, isCurrent: true, height: 36, keyField: 'id' }"
            :sort-config="{ trigger: 'cell' }"
            :loading-config="{ icon: 'vxe-icon-spinner roll', text: ' ' }"
            :scroll-y="{ enabled: true }"
            :menu-config="{ enabled: true }"
            @current-change="currentChangeEvent3"
            @cell-menu="cellContextMenuEvent3"
            :tooltip-config="{ enterable: true }"
          >
            <vxe-column type="seq" title="序号" width="45" style="text-align: center" fixed="left" align="center"></vxe-column>
            <vxe-column field="pdctno_" title="合拼工号" width="135" show-overflow fixed="left">
              <template #default="{ row }">
                {{ row.pdctno_ }}
                <a-tooltip title="作废合拼" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateDeleteHP')">
                  <a-icon type="file-excel" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click="deleteHPClick(row)" />
                </a-tooltip>
                <a-tooltip title="叠层阻抗" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateImp')">
                  <a-icon type="menu" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click="impClick(row)" />
                </a-tooltip>
                <a-tooltip title="开料拼板" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateAutoToolPnl')">
                  <a-icon type="border-left" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click="cuttingClick(row)" />
                </a-tooltip>
                <a-tooltip title="工艺流程" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateTechFlow')">
                  <a-icon type="ordered-list" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click="processClick(row)" />
                </a-tooltip>
                <a-tooltip title="自动流程" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateAutoProduceFlow')">
                  <a-icon type="menu-unfold" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click="getProcess(row)" />
                </a-tooltip>
              </template>
            </vxe-column>
            <vxe-column field="pnlQty_" title="pnl数" width="60" show-overflow>
              <template #default="{ row }">
                <vxe-input
                  v-model="row.pnlQty_"
                  @focus="focusQty(row.pnlQty_)"
                  @blur="blurQty(row.id, row.pnlQty_, row)"
                  @keyup.enter.native="$event.target.blur()"
                  :clearable="true"
                ></vxe-input>
              </template>
            </vxe-column>
            <vxe-column field="pnlUsed_" title="利用率" width="60" show-overflow></vxe-column>
            <vxe-column field="pnlSU_" title="su数" width="50" show-overflow></vxe-column>
            <vxe-column field="lSize_" title="长" width="60" show-overflow></vxe-column>
            <vxe-column field="wSize_" title="宽" width="60" show-overflow></vxe-column>
            <vxe-column field="saleArea_" title="交货面积" width="90" show-overflow></vxe-column>
            <vxe-column field="sendArea" title="投料面积" width="90" show-overflow>
              <template #default="{ row }">
                <span> {{ Number(row.sendArea).toFixed(3) }} </span>
              </template>
            </vxe-column>
            <!-- <vxe-column field="isOneHP_" title="单拼" width="50" show-overflow  ></vxe-column> -->
            <vxe-column field="layer_" title="层数" width="50" show-overflow></vxe-column>
            <vxe-column field="orderType_" title="板材类型" width="80" show-overflow></vxe-column>
            <vxe-column field="boardthick_" title="板厚" width="40" show-overflow></vxe-column>
            <!-- <vxe-column field="outCuThk_" title="外铜" width="45" show-overflow></vxe-column>
          <vxe-column field="innerCuThk_" title="内铜" width="45" show-overflow></vxe-column> -->
            <vxe-column field="surfaceTech_" title="表面工艺" width="80" show-overflow></vxe-column>
            <vxe-column field="solderColor_" title="顶阻" width="60" show-overflow>
              <template #default="{ row }">
                <div v-if="row.solderColor_.indexOf('无') > -1 || !row.solderColor_">无</div>
                <div v-else>{{ row.solderColor_ }}</div>
              </template>
            </vxe-column>
            <vxe-column field="solderColorBottom_" title="底阻" width="60" show-overflow>
              <template #default="{ row }">
                <div v-if="row.solderColorBottom_.indexOf('无') > -1 || !row.solderColorBottom_">无</div>
                <div v-else>{{ row.solderColorBottom_ }}</div>
              </template>
            </vxe-column>
            <vxe-column field="silkColor_" title="顶字" width="45" show-overflow>
              <template #default="{ row }">
                <div v-if="row.silkColor_.indexOf('无') > -1 || !row.silkColor_">无</div>
                <div v-else>{{ row.silkColor_ }}</div>
              </template>
            </vxe-column>
            <vxe-column field="silkColorBottom_" title="底字" width="45" show-overflow>
              <template #default="{ row }">
                <div v-if="row.silkColorBottom_.indexOf('无') > -1 || !row.silkColorBottom_">无</div>
                <div v-else>{{ row.silkColorBottom_ }}</div>
              </template>
            </vxe-column>
            <vxe-column field="hpflag_" title="状态" width="60" show-overflow>
              <template #default="{ row }">
                <span v-if="row.hpflag_ == '1'"> 完成 </span>
                <span v-else-if="row.hpflag_ == '0'"> 处理中 </span>
              </template>
            </vxe-column>
            <vxe-column field="realName" title="制作人" width="70" show-overflow></vxe-column>
            <vxe-column field="inDate_" title="创建时间" width="100" show-overflow></vxe-column>
            <vxe-column field="factoryName" title="工厂" width="70" show-overflow></vxe-column>
          </vxe-table>
          <!-- <a-table
              :rowKey="(record,index)=>{return index}"
              :columns="columns1"
              :dataSource="data3Source"
              :pagination="false"
              :loading="loading1"
              :keyboard="false"
              :bordered="true"
              :scroll="{ x: 430, y:340 }"
              :maskClosable="false"
              :customRow="eventTouch"
              :rowClassName="isRedRow"
              :class="{'minClass2':data3Source.length > 0}"
          >
            <template slot="pdctno_" slot-scope="text,record">
              <a-tooltip title="作废合拼" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateDeleteHP')">
                <a-icon type="file-excel" style="color: #ff9900; font-size: 18px; margin-right: 10px"
                        @click='deleteHPClick(record)' />
              </a-tooltip>
              <a-tooltip title="叠层阻抗" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateImp')">
                <a-icon type="menu" style="color: #ff9900; font-size: 18px; margin-right: 10px"
                        @click='impClick(record)' />
              </a-tooltip>
              <a-tooltip title="开料拼板" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateAutoToolPnl')">
                <a-icon type="border-left" style="color: #ff9900; font-size: 18px; margin-right: 10px"
                        @click='cuttingClick(record)' />
              </a-tooltip>
              <a-tooltip title="工艺流程" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateTechFlow')">
                <a-icon type="ordered-list" style="color: #ff9900; font-size: 18px; margin-right: 10px"
                        @click='processClick(record)' />
              </a-tooltip>
              <a-tooltip title="自动流程" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateAutoProduceFlow')">
                <a-icon type="menu-unfold" style="color: #ff9900; font-size: 18px; margin-right: 10px"
                        @click='getProcess(record)' />
              </a-tooltip>
            </template>
          </a-table> -->
        </a-card>
        <a-card class="bot" style="border: 1px solid #e9e9f0">
          <vxe-table
            border
            stripe
            show-overflow
            ref="xTable2"
            :height="right2height"
            :data="data4Source"
            :loading="loading2"
            :row-config="{ isHover: true, height: 36, keyField: 'id' }"
            :sort-config="{ trigger: 'cell' }"
            :loading-config="{ icon: 'vxe-icon-spinner roll', text: ' ' }"
            :scroll-y="{ enabled: true }"
            :menu-config="{ enabled: true }"
            @cell-menu="cellContextMenuEvent4"
            :tooltip-config="{ enterable: true }"
            class="bottable1"
          >
            <vxe-column type="seq" title="序号" width="45" style="text-align: center" fixed="left" align="center"></vxe-column>
            <vxe-column field="pdctno" title="生产型号" width="170" show-overflow fixed="left"></vxe-column>
            <vxe-column field="urgentType_" title="加急" width="45" show-overflow></vxe-column>
            <vxe-column field="delqty" title="交数" width="50" show-overflow></vxe-column>
            <!--          <vxe-column field="" title="原始数" width="60" show-overflow ></vxe-column>-->
            <vxe-column field="used_no" title="投数" width="50" show-overflow></vxe-column>
            <vxe-column field="scale" title="拼数" width="50" show-overflow></vxe-column>
            <vxe-column field="jtl" title="加投率" width="60" show-overflow></vxe-column>
            <vxe-column field="jtArea_" title="加投面积" width="60" show-overflow></vxe-column>
            <!--          <vxe-column field="lSize_" title="长" width="55" show-overflow  ></vxe-column>-->
            <!--          <vxe-column field="wSize_" title="宽" width="55" show-overflow></vxe-column>-->
            <!--          <vxe-column field="boardthick_" title="板厚" width="40" show-overflow ></vxe-column>-->
            <!--          <vxe-column field="surfaceTech_" title="表面工艺" width="80" show-overflow  ></vxe-column>-->
            <!--          <vxe-column field="solderColor_" title="顶阻" width="60" show-overflow ></vxe-column>-->
            <!--          <vxe-column field="solderColorBottom_" title="底阻" width="60" show-overflow></vxe-column>-->
            <!--          <vxe-column field="silkColor_" title="顶字" width="45" show-overflow></vxe-column>-->
            <!--          <vxe-column field="silkColorBottom_" title="底字" width="45" show-overflow  ></vxe-column>-->
            <!--          <vxe-column field="" title="正负片" width="80" show-overflow></vxe-column>-->
            <vxe-column field="saleArea_" title="交货面积" width="70" show-overflow></vxe-column>
          </vxe-table>
          <!-- <a-table
              :rowKey="(record,index)=>{return index}"
              :columns="columns2"
              :dataSource="data4Source"
              :pagination="false"
              :loading="loading2"
              :keyboard="false"
              :bordered="true"
              :customRow="eventTouch1"
              :maskClosable="false"
              :scroll="{ x: 430, y:340 }"
              :class="{'minClass2':data4Source.length > 0}"
          >
          </a-table>           -->
        </a-card>
        <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
          <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
        </a-menu>
      </div>
    </div>
  </a-card>
</template>

<script>
import { autoToolPnl, getImp, upHPFLFac, setchangepnlnum } from "@/services/Combinate";
import { checkPermission } from "@/utils/abp";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    width: 40,
    align: "center",
    fixed: "left",
    // scopedSlots: {customRender: 'index'},
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "合拼工号",
    dataIndex: "pdctno_",
    width: 120,
    ellipsis: true,
    align: "left",
    fixed: "left",
    className: "userStyle",
  },
  {
    title: "pnl数",
    dataIndex: "pnlQty_",
    width: 50,
    ellipsis: true,
    align: "center",
  },
  {
    title: "长",
    dataIndex: "lSize_",
    width: 45,
    ellipsis: true,
    align: "center",
  },
  {
    title: "宽",
    width: 45,
    dataIndex: "wSize_",
    ellipsis: true,
    align: "center",
  },
  {
    title: "利用率",
    dataIndex: "pnlUsed_",
    width: 50,
    ellipsis: true,
    align: "center",
  },
  {
    title: "su数",
    width: 50,
    ellipsis: true,
    dataIndex: "pnlSU_",
    align: "center",
  },
  {
    title: "拼板人",
    dataIndex: "realName",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "工厂",
    dataIndex: "joinFactoryId",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "交货面积",
    dataIndex: "saleArea_",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "投料面积",
    width: 90,
    dataIndex: "sendArea",
    align: "left",
    ellipsis: true,
    customRender: (text, record) => {
      if (record.sendArea) {
        return Number(record.sendArea).toFixed(3);
      }
    },
  },
  {
    title: "层数",
    width: 50,
    dataIndex: "layer_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "板材类型",
    width: 70,
    dataIndex: "orderType_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "板厚",
    width: 60,
    dataIndex: "boardthick_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "外铜",
    width: 50,
    dataIndex: "outCuThk_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "内铜",
    width: 50,
    dataIndex: "innerCuThk_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "表面工艺",
    width: 70,
    dataIndex: "surfaceTech_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "顶阻",
    width: 60,
    dataIndex: "solderColor_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "底阻",
    width: 60,
    dataIndex: "solderColorBottom_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "顶字",
    width: 50,
    dataIndex: "silkColor_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "底字",
    width: 50,
    dataIndex: "silkColorBottom_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "状态",
    width: 50,
    dataIndex: "",
    align: "left",
    ellipsis: true,
  },
];
const columns2 = [
  {
    dataIndex: "index",
    title: "序号",
    key: "index",
    width: 40,
    align: "center",
    fixed: "left",
    // scopedSlots: {customRender: 'index'},
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "生产型号",
    width: 135,
    dataIndex: "pdctno",
    align: "left",
    ellipsis: true,
    fixed: "left",
    className: "userStyle",
  },
  {
    title: "加急",
    width: 65,
    dataIndex: "urgentType_",
    align: "left",
    ellipsis: true,
  },
  {
    title: "交数",
    width: 60,
    dataIndex: "delqty",
    align: "left",
    ellipsis: true,
  },
  {
    title: "原始数",
    width: 60,
    dataIndex: "",
    align: "left",
    ellipsis: true,
  },
  {
    title: "投数",
    width: 50,
    dataIndex: "used_no",
    align: "left",
    ellipsis: true,
  },
  {
    title: "拼数",
    width: 55,
    dataIndex: "scale",
    align: "left",
    ellipsis: true,
  },
  {
    title: "加投",
    width: 55,
    dataIndex: "",
    align: "left",
    ellipsis: true,
  },
  {
    title: "长",
    width: 50,
    dataIndex: "lSize",
    align: "left",
    ellipsis: true,
  },
  {
    title: "宽",
    width: 50,
    ellipsis: true,
    dataIndex: "wSize",
    align: "left",
  },
  {
    title: "板厚",
    width: 70,
    dataIndex: "boardthick_",
    ellipsis: true,
    align: "left",
  },
  {
    title: "表面工艺",
    width: 80,
    ellipsis: true,
    dataIndex: "surfaceTech_",
    align: "left",
  },
  {
    title: "顶阻",
    width: 80,
    dataIndex: "solderColor_",
    ellipsis: true,
    align: "left",
  },
  {
    title: "底阻",
    width: 80,
    ellipsis: true,
    dataIndex: "solderColorBottom_",
    align: "left",
  },
  {
    title: "顶字",
    width: 80,
    dataIndex: "silkColor_",
    ellipsis: true,
    align: "left",
  },
  {
    title: "底字",
    width: 100,
    ellipsis: true,
    dataIndex: "silkColorBottom_",
    align: "left",
  },
  {
    title: "正负片",
    width: 80,
    dataIndex: "",
    ellipsis: true,
    align: "left",
  },
  {
    title: "交货面积",
    width: 90,
    ellipsis: true,
    dataIndex: "reArea_",
    align: "left",
  },
];
export default {
  name: "Center",
  props: [
    "data3Source",
    "data4Source",
    "data5Source",
    "data6Source",
    "data7Source",
    "loading1",
    "loading2",
    "loading3",
    "loading4",
    "loading5",
    "showText",
    "menuStyle",
  ],
  created() {
    this.$nextTick(function () {
      this.configureData = this.data7Source;
      this.handleResize();
    });
  },
  data() {
    return {
      columns1,
      columns2,
      activeKey: "1",
      right1height: "378",
      right2height: "378",
      id: "",
      lol: "",
      pdctno_: "",
      selectRow1: {},
      configureData: [],
      fold: false,
      menuVisible: false,
    };
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    focusQty(x) {
      this.delQty_ = x;
    },
    blurQty(id, num, record) {
      var r = /^\+?[1-9][0-9]*$/; //正整数正则
      if (!r.test(num)) {
        this.$message.warning("请输入正整数");
        return;
      }
      if (this.delQty_ != num) {
        setchangepnlnum(id, num).then(res => {
          if (res.code) {
            this.$message.success("更改pnl数成功");
            this.$emit("gethpINGDetailInfo", record);
            return;
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    caretup() {
      this.fold = false;
      let bot = document.getElementsByClassName("bottable1")[0];
      bot.style.display = "";
      this.handleResize();
    },
    caretdown() {
      this.fold = true;
      let bot = document.getElementsByClassName("bottable1")[0];
      bot.style.display = "none";
      this.right1height = window.innerHeight - 155;
    },
    handleResize() {
      if ((window.innerHeight - 155) / 2 <= 378) {
        this.right1height = (window.innerHeight - 155) / 2;
        this.right2height = (window.innerHeight - 155) / 2;
      } else {
        this.right1height = "378";
        this.right2height = "378";
      }
    },
    down() {
      this.$emit("down");
    },
    rightClick1(e, text, record) {
      this.$emit("rightClick1", e, text, record);
    },
    // 已合拼列表点击事件
    checkPermission,
    eventTouch(record, index, e) {
      return {
        on: {
          // 事件
          click: () => {
            this.id = record.id;
            this.pdctno_ = record.pdctno_;
            this.selectRow1 = record;
            this.$emit("gethpINGDetailInfo", record);
            this.$emit("Ttype", 3);
          },
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            this.$emit("Ttype", 3);
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record);
          },
        },
      };
    },
    eventTouch1(record, index) {
      this.lol = 4;
      return {
        on: {
          // 事件
          click: () => {
            this.$emit("Ttype", 4);
          },
          contextmenu: e => {
            console.log("走了");
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
              console.log("text", text);
            }
            this.$emit("Ttype", 4);
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record);
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.id && record.id == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    rowClassName(row) {
      if (row.rowid && row.rowid == this.id) {
        return "row-background";
      }
      return null;
    },
    // 作废合拼
    deleteHPClick(record) {
      this.$emit("deleteHPClick", record);
    },
    // 叠层阻抗
    impClick(record) {
      getImp(record.id).then(res => {
        if (res.code) {
          const routeOne = this.$router.resolve({
            path: "/impedance",
            query: {
              // boardType:res.data.boardType,
              finishBoardThickness: res.data.finishBoardThickness,
              layers: res.data.layers,
              pdctno: res.data.pdctno,
              InCopperThickness: res.data.innerCopperThickness,
              OutCopperThickness: res.data.copperThickness,
              type: "HP",
            },
          });
          window.open(routeOne.href, "_blank", routeOne.query);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 开料拼板
    cuttingClick(record) {
      autoToolPnl(record.id).then(res => {
        if (res.code) {
          const routeOne = this.$router.resolve({
            path: "/gongju/cutting",
            query: {
              boardThink: res.data.boardThink,
              cpLen: res.data.cpLen,
              cpWidth: res.data.cpWidth,
              job: res.data.job,
              layCount: res.data.layCount,
              pcsset: res.data.pcsset,
              pcssetName: res.data.pcssetName,
              type: "HP",
            },
          });
          window.open(routeOne.href, "_blank", routeOne.query);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 工艺流程
    processClick(record) {
      const routeOne = this.$router.resolve({
        path: "/gongju/technologicalProcess",
        query: {
          pdctno: record.pdctno_ + "-HP",
          type: "HP",
          factoryId: record.joinFactoryId,
        },
      });
      window.open(routeOne.href, "_blank", routeOne.query);
    },
    // 生成流程
    getProcess(record) {
      this.$emit("getProcess", record);
    },
    // 更新菲林工厂参数
    isFLFacChange(record) {
      upHPFLFac(record.id, record.isFLFac_).then(res => {
        if (res.code) {
          this.$message.success("成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 配置参数编辑更改
    fristValueChange(record) {
      let arr = this.data7Source.filter(item => item.captions_ != record.captions_);
      arr.push(record);
      this.configureData = arr;
      console.log("record", record, this.data7Source);
    },
    mouseleave() {
      this.$emit("mouseleave");
    },
    currentChangeEvent3({ row }) {
      this.id = row.id;
      this.pdctno_ = row.pdctno_;
      this.selectRow1 = row;
      this.$emit("gethpINGDetailInfo", row);
      this.$emit("Ttype", 3);
    },
    cellContextMenuEvent3({ row, column, $event }) {
      $event.preventDefault();
      this.$emit("Ttype", 3);
      this.menuData = row;
      this.rightClick1($event, row[column.field], row);
    },
    cellContextMenuEvent4({ row, column, $event }) {
      $event.preventDefault();
      this.$emit("Ttype", 4);
      this.menuData = row;
      this.rightClick1($event, row[column.field], row);
    },
  },
};
</script>

<style lang="less" scoped>
.iconstyle {
  color: #6d6d6d;
  position: absolute;
  margin: 0px;
  top: 7px;
  z-index: 99;
  left: 12px;
}
/deep/.anticon-caret-down {
  position: relative;
  float: left;
}
/deep/.anticon-caret-up {
  position: relative;
  float: left;
}
.machine {
  display: flex;
  /deep/.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
  /deep/ .ant-table {
    tr.ant-table-row-selected td {
      background: #dfdcdc !important;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc !important;
    }
  }
  .minClass2 {
    /deep/ .ant-table-body {
      min-height: 342px;
    }
  }
  .minClass3 {
    /deep/ .ant-table-body {
      min-height: 322px;
    }
  }
  .left {
    width: 100%;
    position: relative !important;
  }
  .right {
    width: 50%;
    /deep/ .ant-tabs-top-bar {
      height: 32px !important;
      margin-bottom: 0 !important;
      line-height: 32px !important;
    }
  }
  .tabRightClikBox {
    //  border:2px solid rgb(238, 238, 238) !important;
    li {
      height: 30px;
      line-height: 30px;
      margin-top: 0;
      margin-bottom: 0;
      text-align: center;
      border-bottom: 1px solid rgb(225, 223, 223);
      background-color: white !important;
      color: #000000;
    }
    .ant-menu-item:not(:last-child) {
      margin-bottom: 4px;
    }
  }
}
</style>
