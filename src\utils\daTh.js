
/**
 * 闭包函数
 * 
 * 防抖：对于短时间内连续触发的事件（滚动事件、表单重复提交、页面resize事件，常见于需要做页面适配的时候），让某个时间期限内，事件处理函数只执行一次。
 * 节流：对于短时间内大量触发同一事件（滚动事件、输入框实时搜索），那么在函数执行一次之后，该函数在指定的时间期限内不再工作，直至过了这段时间才重新生效
 */
 export default {
  // 防抖
  debounce: function (fn, time) {
    time = time || 200
    // 定时器
    let timer = null
    return function(...args) {
      var _this = this
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(function() {
        timer = null
        fn.apply(_this, args)
      }, time)
    }
  },
  // 节流 定时器 + 时间戳
  throttle: function(fn, time) {
    let timer = null
    time = time || 1000
    return function(...args) {
      if (timer) {
        return
      }
      const _this = this
      timer = setTimeout(() => {
        timer = null
      }, time)
      fn.apply(_this, args)
    }
  }
}


