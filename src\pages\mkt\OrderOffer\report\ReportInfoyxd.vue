<!-- 市场管理 - 订单报价- yxd -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj1" v-if="joinFactoryId == 38" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <a-button v-print="printObj" v-else @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div
      id="pdfDom"
      v-if="joinFactoryId == 58 || joinFactoryId == 59 || joinFactoryId == 65"
      style="color: black; font-family: '微软雅黑'; font-size: 12px; padding: 25px"
    >
      <div style="display: flex; width: 100%; justify-content: space-between">
        <img v-if="joinFactoryId == '58' || joinFactoryId == '59'" src="@/assets/img/yxdlogo.png" style="height: 80px" />
        <div v-else style="width: 140px">
          <!-- <img :src="yxddata.logo_" crossorigin="anonymous" style="width: 140px;">  -->
        </div>
        <div style="text-align: center">
          <span style="font-size: 26px">{{ yxddata.factory_ }}</span
          ><br />
          <span style="font-size: 26px">{{ yxddata.contract_ }}</span>
        </div>
        <div>
          <div>工厂地址 : {{ yxddata.factoryadd_ }}</div>
          <div>公司地址 : {{ yxddata.address_ }}</div>
          <div>{{ yxddata.tel_ }}&nbsp;&nbsp;&nbsp;&nbsp;{{ yxddata.fax_ }}</div>
          <div>{{ yxddata.eml_ }}&nbsp;&nbsp;&nbsp;&nbsp;{{ yxddata.http_ }}</div>
        </div>
      </div>
      <div style="padding-top: 20px; width: 100%; display: flex">
        <div style="width: 27%">{{ yxddata.party_ }}</div>
        <div style="width: 27%">{{ yxddata.link_ }}</div>
        <div style="width: 27%">签约日期： {{ yxddata.date_ }}</div>
        <div style="width: 19%">订单编号： {{ yxddata.orderNo_ }}</div>
      </div>
      <div style="background-color: black; height: 2px; width: 100%; margin-bottom: 10px"></div>
      <div>
        <span style="font-size: 16px; font-weight: bold">【1】加工品名称、规格、数量及交期:</span>
        <span @click="addcontract" style="color: #4b82ac; float: right" v-if="showadd && act != 'dis'"
          >点击添加 <a-icon style="font-size: 16px; top: 2px; position: relative" type="plus-circle"></a-icon
        ></span>
      </div>
      <table class="tableclass">
        <thead>
          <tr>
            <td style="width: 60px">序号</td>
            <td style="width: 130px">产品型号</td>
            <td style="max-width: 200px; min-width: 120px">客户型号</td>
            <td style="max-width: 200px; min-width: 120px">客户物料号</td>
            <td style="width: 60px">单位</td>
            <td style="width: 60px">数量</td>
            <td style="width: 60px">单价</td>
            <td style="width: 85px">测试费</td>
            <td style="width: 85px">飞测费</td>
            <td style="width: 85px">工程费</td>
            <td style="width: 85px">加急费</td>
            <td style="width: 85px">其他费</td>
            <td style="width: 85px">包干价</td>
            <td style="width: 120px">合计</td>
            <td style="width: 120px">交期</td>
            <td style="width: 40px" v-if="showadd && act != 'dis'">操作</td>
          </tr>
        </thead>
        <tbody v-for="(item, index) in yxddata.price" :key="index">
          <tr>
            <td style="width: 60px" rowspan="2">{{ index + 1 }}</td>
            <td style="width: 130px">{{ item.name }}</td>
            <td style="max-width: 200px; min-width: 120px">{{ item.custName }}</td>
            <td style="max-width: 200px; min-width: 120px">{{ item.mat }}</td>
            <td style="width: 60px">{{ item.bType }}</td>
            <td style="width: 60px">{{ item.qty }}</td>
            <td style="width: 60px">{{ item.pcs }}</td>
            <td style="width: 85px">{{ item.test }}</td>
            <td style="width: 85px">{{ item.fly }}</td>
            <td style="width: 85px">{{ item.eng }}</td>
            <td style="width: 85px">{{ item.urgent }}</td>
            <td style="width: 85px">{{ item.other }}</td>
            <td style="width: 85px">{{ item.price1 }}</td>
            <td style="width: 120px">{{ item.total }}</td>
            <td style="width: 120px">{{ item.custdate }}</td>
            <td style="width: 40px" rowspan="2" v-if="showadd && act != 'dis'">
              <a-tooltip placement="top" title="删除当前行数据">
                <a-icon @click="delclick(item.id)" style="font-size: 14px; color: #4b82ac; padding-left: 5px" type="close-circle"></a-icon>
              </a-tooltip>
            </td>
          </tr>
          <tr>
            <td style="width: 130px" colspan="14">{{ item.notes }}</td>
          </tr>
        </tbody>
        <tbody>
          <tr>
            <td colspan="5">合计 :</td>
            <td colspan="5">合计(大写) :{{ yxddata.value_1 == "rmb" ? "人民币" : "美元" }}{{ convertToChineseNum(amountto) }}</td>
            <td colspan="3">{{ amountto }}</td>
          </tr>
        </tbody>
      </table>
      <!-- <img v-if="yxddata.sura" :src="yxddata.sura" style="height: 240px;width: 240px;position: relative;top: -123px;left: 135px;"> -->
      <div style="position: relative">
        <div style="position: relative; z-index: 99; height: 410px">
          <div class="agreement">
            <div style="font-size: 16px; font-weight: bold">{{ yxddata.pact_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO1_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO2_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO3_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO4_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO5_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO6_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO7_ }}</div>
            <div style="padding-left: 40px">{{ yxddata.nO8_ }}</div>
          </div>
          <div>
            <span>供方（乙方）签名盖章：<span style="position: relative; left: 500px"> 需方（甲方）签名盖章：</span></span
            ><br />
            <span style="position: relative; left: 50px; top: 10px">日&nbsp;&nbsp;&nbsp;期 : </span
            ><span style="position: relative; left: 650px; top: 10px">日&nbsp;&nbsp;&nbsp;期 : </span>
          </div>
        </div>
        <img
          v-if="joinFactoryId == '65'"
          src="@/assets/img/jmz.png"
          style="position: absolute; top: 250px; z-index: 0; display: block; left: 70px; width: 150px"
        />
        <img
          v-if="yxddata.factory_.indexOf('惠州') != -1"
          src="@/assets/img/hzyxd.png"
          style="position: absolute; top: 250px; z-index: 0; display: block; left: 70px; width: 150px"
        />
        <img
          v-if="yxddata.factory_.indexOf('江西') != -1"
          src="@/assets/img/jxyxd.png"
          style="position: absolute; top: 250px; z-index: 0; display: block; left: 70px; width: 150px"
        />
      </div>
    </div>
    <div
      id="pdfDomlhdc"
      v-else-if="joinFactoryId == 38"
      style="font-size: 12px; color: black; font-family: '微软雅黑'; font-size: 12px; padding: 25px"
    >
      <div style="display: flex; width: 100%">
        <img src="@/assets/img/lhdc.png" style="height: 70px; padding-left: 100px" />
        <div style="width: 840px; text-align: center">
          <span style="font-size: 18px; letter-spacing: 5px">{{ yxddata.factory_ }}</span
          ><br />
          <span style="font-size: 13px; font-weight: bold">{{ yxddata.factoryEnglish_ }}</span
          ><br />
          <span style="font-size: 18px; letter-spacing: 5px">{{ yxddata.contract_ }}</span> <br />
          <span style="font-size: 13px; font-weight: bold">{{ yxddata.contractEnglish_ }}</span
          ><br />
        </div>
        <div style="margin-top: 15px; font-size: 10px; width: 181px">
          <table border="1" style="float: right">
            <tr>
              <td style="padding-left: 1ch">合同编号:</td>
              <td style="width: 100px; padding-left: 1ch">{{ yxddata.orderNo_.split("：")[1] }}</td>
            </tr>
            <tr>
              <td style="padding-left: 1ch">日&nbsp;&nbsp;期:</td>
              <td style="padding-left: 1ch">{{ yxddata.date_.split("：")[1] }}</td>
            </tr>
            <tr>
              <td style="padding-left: 1ch">币&nbsp;&nbsp;种:</td>
              <td style="padding-left: 1ch">{{ yxddata.currency_.split("：")[1] }}</td>
            </tr>
          </table>
        </div>
      </div>
      <div style="display: flex">
        <div style="width: 800px">
          <div>{{ yxddata.name_ }}</div>
          <div>{{ yxddata.link_ }}</div>
          <div>{{ yxddata.tel_ }}</div>
          <div>经双方友好协议达成一致,订立本合同 :</div>
        </div>
        <div style="width: 307px">
          <div>{{ yxddata.factoryR_ }}</div>
          <div>{{ yxddata.meTel_ }} &nbsp;&nbsp; {{ yxddata.fax_ }}</div>
          <div>{{ yxddata.eml_ }}</div>
        </div>
        <span @click="addcontract" style="color: #4b82ac; margin-top: 33px" v-if="showadd && act != 'dis'"
          >点击添加 <a-icon style="font-size: 16px; top: 2px; position: relative" type="plus-circle"></a-icon
        ></span>
      </div>
      <div>
        <table border="1" style="font-size: 10px; text-align: center; font-weight: bold; width: 100%">
          <thead>
            <tr>
              <td style="width: 30px" rowspan="2">序号</td>
              <td style="width: 100px" rowspan="2">客户型号</td>
              <td style="width: 100px" v-if="showcustPo" rowspan="2">客户PO</td>
              <td style="width: 100px" v-if="showporeDensity" rowspan="2">客户物料号</td>
              <td :colspan="workmanship">成品工艺要求</td>
              <td v-if="showqty" style="width: 40px" rowspan="2">数量</td>
              <td v-if="pcsprice" style="width: 50px" rowspan="2">单价</td>
              <td v-if="samplprice" style="width: 50px" rowspan="2">样板费</td>
              <td v-if="urgentprice" style="width: 50px" rowspan="2">加急费</td>
              <td v-if="otherprice" style="width: 50px" rowspan="2">PP费</td>
              <td v-if="ctkprice" style="width: 50px" rowspan="2">沉头孔费</td>
              <td v-if="bgPrice" style="width: 50px" rowspan="2">光板费</td>
              <td v-if="bbPrice" style="width: 50px" rowspan="2">金属包边费</td>
              <td v-if="densityPrice" style="width: 50px" rowspan="2">超孔费</td>
              <td v-if="ktPrice" style="width: 50px" rowspan="2">孔铜费</td>
              <td v-if="ljPrice" style="width: 50px" rowspan="2">蓝胶费</td>
              <td v-if="nxbPrice" style="width: 50px" rowspan="2">内斜边费</td>
              <td v-if="bcPrice" style="width: 50px" rowspan="2">板材费</td>
              <td v-if="tyPrice" style="width: 50px" rowspan="2">碳油费</td>
              <td v-if="wxbPrice" style="width: 50px" rowspan="2">外斜边费</td>
              <td v-if="wlPrice" style="width: 50px" rowspan="2">无卤素费</td>
              <td v-if="yjkPrice" style="width: 50px" rowspan="2">压接孔费</td>
              <td v-if="zfPrice" style="width: 50px" rowspan="2">杂色字符费</td>
              <td v-if="zhPrice" style="width: 50px" rowspan="2">杂色阻焊费</td>
              <td v-if="zkPrice" style="width: 50px" rowspan="2">钻孔孔径费</td>
              <td v-if="plateprice" style="width: 50px" rowspan="2">测试架费</td>
              <td v-if="flyprice" style="width: 50px" rowspan="2">飞针费</td>
              <td v-if="filmprice" style="width: 50px" rowspan="2">菲林费</td>
              <td v-if="engprice" style="width: 50px" rowspan="2">工程费</td>
              <td v-if="hpprice" style="width: 50px" rowspan="2">合拼费</td>
              <td v-if="surfaceprice" style="width: 50px" rowspan="2">表面处理费</td>
              <td v-if="diffprice" style="width: 50px" rowspan="2">难度费</td>
              <td v-if="impprice" style="width: 50px" rowspan="2">阻抗费</td>
              <td v-if="halfHoleprice" style="width: 50px" rowspan="2">半孔费</td>
              <td v-if="thicknessprice" style="width: 50px" rowspan="2">板厚费</td>
              <td v-if="cvutprice" style="width: 50px" rowspan="2">其他费</td>
              <td v-if="cuthicprice" style="width: 50px" rowspan="2">铜厚费</td>
              <td v-if="resinprice" style="width: 50px" rowspan="2">树脂塞孔费</td>
              <td v-if="fingerprice" style="width: 50px" rowspan="2">金手指费</td>
              <td v-if="preferentialprice" style="width: 50px" rowspan="2">优惠费</td>
              <td v-if="taxprice" style="width: 50px" rowspan="2">税金</td>
              <td v-if="freightprice" style="width: 50px" rowspan="2">运费</td>
              <td v-if="discountprice" style="width: 50px" rowspan="2">优惠金额</td>
              <td style="width: 50px" rowspan="2">合计金额</td>
              <td v-if="showcustdate" style="width: 80px" rowspan="2">交期</td>
              <td v-if="showmat" style="width: 40px" rowspan="2">加急</td>
              <td v-if="shownotes" style="width: 160px" rowspan="2">备注</td>
              <td style="width: 40px" rowspan="2" v-if="showadd && act != 'dis'">操作</td>
            </tr>
            <tr>
              <td v-if="showboardBrand" style="width: 60px">板材</td>
              <td v-if="showboardT" style="width: 40px">板厚</td>
              <td v-if="showincu" style="width: 40px">内铜</td>
              <td v-if="showoucu" style="width: 40px">外铜</td>
              <td v-if="showlay" style="width: 40px">层数</td>
              <td v-if="showsize" style="width: 95px">交货尺寸(mm)</td>
              <td v-if="showsu" style="width: 40px">拼版</td>
              <td v-if="showsurf" style="width: 60px">表面处理</td>
              <td v-if="showmask" style="width: 60px">阻焊</td>
              <td v-if="showchar_" style="width: 60px">字符</td>
              <td v-if="showsolder" style="width: 60px">过孔工艺</td>
              <td v-if="showtest" style="width: 60px">测试</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in yxddata.price" :key="index">
              <td>{{ index + 1 }}</td>
              <td style="max-width: 200px; word-wrap: break-word">{{ item.custName }}</td>
              <td v-if="showcustPo">{{ item.custPo }}</td>
              <td v-if="showporeDensity">{{ item.poreDensity }}</td>
              <td v-if="showboardBrand">{{ item.boardBrand }}</td>
              <td v-if="showboardT">{{ item.boardT }}</td>
              <td v-if="showincu">{{ item.incu }}</td>
              <td v-if="showoucu">{{ item.oucu }}</td>
              <td v-if="showlay">{{ item.lay }}</td>
              <td v-if="showsize">{{ item.size }}</td>
              <td v-if="showsu">{{ item.su }}</td>
              <td v-if="showsurf">{{ item.surf }}</td>
              <td v-if="showmask">{{ item.mask }}</td>
              <td v-if="showchar_">{{ item.char_ }}</td>
              <td v-if="showsolder">{{ item.solder }}</td>
              <td v-if="showtest">{{ item.test }}</td>
              <td v-if="showqty">{{ item.qty }}</td>
              <td v-if="pcsprice">{{ item.pcs }}</td>
              <td v-if="samplprice">{{ item.sampl }}</td>
              <td v-if="urgentprice">{{ item.urgent }}</td>
              <td v-if="otherprice">{{ item.other }}</td>
              <td v-if="ctkprice">{{ item.ctkPrice }}</td>
              <td v-if="bgPrice">{{ item.bgPrice }}</td>
              <td v-if="bbPrice">{{ item.bbPrice }}</td>
              <td v-if="densityPrice">{{ item.density }}</td>
              <td v-if="ktPrice">{{ item.ktPrice }}</td>
              <td v-if="ljPrice">{{ item.ljPrice }}</td>
              <td v-if="nxbPrice">{{ item.nxbPrice }}</td>
              <td v-if="bcPrice">{{ item.bcPrice }}</td>
              <td v-if="tyPrice">{{ item.tyPrice }}</td>
              <td v-if="wxbPrice">{{ item.wxbPrice }}</td>
              <td v-if="wlPrice">{{ item.wlPrice }}</td>
              <td v-if="yjkPrice">{{ item.yjkPrice }}</td>
              <td v-if="zfPrice">{{ item.zfPrice }}</td>
              <td v-if="zhPrice">{{ item.zhPrice }}</td>
              <td v-if="zkPrice">{{ item.zkPrice }}</td>
              <td v-if="plateprice">{{ item.plate }}</td>
              <td v-if="flyprice">{{ item.fly }}</td>
              <td v-if="filmprice">{{ item.film }}</td>
              <td v-if="engprice">{{ item.eng }}</td>
              <td v-if="hpprice">{{ item.hpPrice }}</td>
              <td v-if="surfaceprice">{{ item.surface }}</td>
              <td v-if="diffprice">{{ item.diff }}</td>
              <td v-if="impprice">{{ item.imp }}</td>
              <td v-if="halfHoleprice">{{ item.halfHole }}</td>
              <td v-if="thicknessprice">{{ item.boardPrice }}</td>
              <td v-if="cvutprice">{{ item.cvut }}</td>
              <td v-if="cuthicprice">{{ item.cuthic }}</td>
              <td v-if="resinprice">{{ item.resin }}</td>
              <td v-if="fingerprice">{{ item.finger }}</td>
              <td v-if="preferentialprice">{{ item.preferential }}</td>
              <td v-if="taxprice">{{ item.tax }}</td>
              <td v-if="freightprice">{{ item.freight }}</td>
              <td v-if="discountprice">{{ item.discount }}</td>
              <td>{{ item.total }}</td>
              <td v-if="showcustdate">{{ item.custdate }}</td>
              <td v-if="showmat">{{ item.mat }}</td>
              <td v-if="shownotes" style="text-align: left">{{ item.notes }}</td>
              <td v-if="showadd && act != 'dis'">
                <a-tooltip placement="top" title="删除当前行数据">
                  <a-icon @click="delclick(item.id)" style="font-size: 14px; color: #4b82ac" type="close-circle"></a-icon>
                </a-tooltip>
              </td>
            </tr>
            <tr>
              <td style="text-align: right; padding-right: 15px; font-weight: bold" colspan="14">
                总金额({{ currency }}/大写):{{ convertToChineseNum(amountto) }}
              </td>
              <td style="text-align: left; padding-left: 6px; font-weight: bold" colspan="14">￥{{ amountto }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="z-index: 99; position: relative; font-weight: bold; font-size: 10px">
        <span>{{ yxddata.pact_ }} </span><br /><br />
        <div style="display: flex">
          <div style="width: 490px">
            <div>{{ yxddata.nO_ }}</div>
            <div>{{ yxddata.nO1_ }}</div>
            <div>{{ yxddata.nO2_ }}</div>
            <div>{{ yxddata.nO3_ }}</div>
            <div>{{ yxddata.nO4_ }}</div>
            <div>{{ yxddata.nO5_ }}</div>
            <div>{{ yxddata.nO6_ }}</div>
            <div>{{ yxddata.nO8_ }}</div>
            <div style="color: red">{{ yxddata.sura }}</div>
          </div>
          <div style="width: 690px">
            <div style="white-space: break-spaces">{{ term(yxddata.nO7_) }}</div>
          </div>
        </div>
        <div style="margin-top: 20px; display: flex; font-size: 10px">
          <div style="width: 720px">
            <div>甲 &nbsp; &nbsp; &nbsp; 方: {{ yxddata.custL_.split("：")[1] }}</div>
            <div>{{ yxddata.custAddL_ }}</div>
            <div>
              代 &nbsp; &nbsp; &nbsp; 表: {{ yxddata.custdelegate_.split("：")[1] }}<span style="padding-left: 15ch">{{ yxddata.custdate_ }}</span>
            </div>
          </div>
          <div style="width: 465px">
            <div>乙 &nbsp; &nbsp; &nbsp; 方: {{ yxddata.factoryadd_.split("：")[1] }}</div>
            <div>{{ yxddata.factoryAddR_ }}</div>
            <div>
              代 &nbsp; &nbsp; &nbsp; 表: {{ yxddata.factorydelegate_.split("：")[1] }}
              <span style="padding-left: 5ch">{{ yxddata.factorydete_ }}</span>
            </div>
          </div>
        </div>
      </div>
      <img
        v-if="joinFactoryId == '38'"
        src="@/assets/img/lhdcz.png"
        style="height: 120px; width: 120px; position: relative; top: -96px; left: 769px; z-index: 0; display: block"
      />
    </div>
    <a-modal title="合同数据添加" :visible="modalvisible" @ok="handleOk" @cancel="handleCancel" centered :width="800">
      <a-row>
        <a-col :span="10">
          <a-form-item label="订单号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入订单号进行查询" v-model="OrderNo" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="客户型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-input placeholder="输入客户型号进行查询" v-model="PcbFileName" @keyup.enter="queryclick" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" style="margin-top: 2px" @click="queryclick">查询</a-button>
        </a-col>
      </a-row>
      <a-table
        :columns="columns"
        :row-selection="{ selectedRowKeys: selectedRowKeysind, onChange: onSelectChange, columnWidth: 25 }"
        :pagination="false"
        :rowKey="
          (record, index) => {
            return index;
          }
        "
        :scroll="{ y: 300 }"
        :dataSource="datasource"
        :rowClassName="isRedRow"
      >
        <!-- :customRow="onClickRow" -->
      </a-table>
    </a-modal>
  </div>
</template>
<script>
import html2pdf from "html2pdf.js";
import { verifyPageList, deletecontractno, contractNo } from "@/services/mkt/OrderReview.js";
import convertToChineseNum from "@/utils/convertToChineseNum";
import htmlToPdf from "@/utils/htmlToPdfa3";
//htmlToPdfa3  htmlToPdf
export default {
  name: "ReportInfoyxd",
  props: ["yxddata", "joinFactoryId", "salescustno", "ContractNoSech", "ttype", "act"],
  computed: {},
  data() {
    return {
      selectedRowKeys: [],
      currency: "",
      printObj: {
        id: "pdfDom", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      printObj1: {
        id: "pdfDomlhdc", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
      showadd: true,
      selectedRowKeysind: [],
      PcbFileName: "",
      OrderNo: "",
      datasource: [],
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 35,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "订单号",
          align: "left",
          ellipsis: true,
          width: 80,
          dataIndex: "orderNo",
        },
        {
          title: "客户代码",
          dataIndex: "custNo",
          align: "left",
          ellipsis: true,
          width: 50,
        },

        {
          title: "订单类型",
          dataIndex: "reOrder",
          align: "left",
          ellipsis: true,
          width: 50,
          customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
        },
      ],
      amountto: 0,
      modalvisible: false,
      urgentprice: false,
      showmat: false,
      ctkprice: false,
      bgPrice: false,
      bbPrice: false,
      densityPrice: false,
      ktPrice: false,
      ljPrice: false,
      nxbPrice: false,
      bcPrice: false,
      tyPrice: false,
      wxbPrice: false,
      wlPrice: false,
      yjkPrice: false,
      zfPrice: false,
      zhPrice: false,
      zkPrice: false,
      otherprice: false,
      pcsprice: false,
      samplprice: false,
      showboardBrand: false,
      showboardT: false,
      showincu: false,
      showoucu: false,
      showlay: false,
      showsize: false,
      showsu: false,
      showsurf: false,
      showmask: false,
      showchar_: false,
      showsolder: false,
      showtest: false,
      shownotes: false,
      showcustdate: false,
      showqty: false,
      showcustPo: false,
      showporeDensity: false,
      discountprice: false,
      freightprice: false,
      taxprice: false,
      preferentialprice: false,
      fingerprice: false,
      resinprice: false,
      cuthicprice: false,
      cvutprice: false,
      thicknessprice: false,
      halfHoleprice: false,
      impprice: false,
      diffprice: false,
      surfaceprice: false,
      hpprice: false,
      engprice: false,
      filmprice: false,
      flyprice: false,
      plateprice: false,
      workmanship: 0,
      ids: [],
    };
  },
  mounted() {
    this.printObj.closeCallback = this.closePrintTool;
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    this.workmanship = 0;
    this.ids = [];
    this.currency = this.yxddata.currency_
      ? this.yxddata.currency_.split("：")[1] == "RMB"
        ? "人民币"
        : this.yxddata.currency_.split("：")[1] == "USD"
        ? "美元"
        : ""
      : "";
    this.yxddata.price.forEach(item => {
      if (this.ids.indexOf(item.id) == -1) {
        this.ids.push(item.id);
      }
    });
    this.showadd = !this.yxddata.price.some(ite => ite.status == 30);
    for (let index = 0; index < this.yxddata.price.length; index++) {
      if (this.yxddata.price[index].total && this.yxddata.price[index].total != "/") {
        this.amountto += Number(this.yxddata.price[index].total);
      }
      if (this.yxddata.price[index].urgent != "/" && this.yxddata.price[index].urgent) {
        this.urgentprice = true;
      }
      if (this.yxddata.price[index].mat == "是") {
        this.showmat = true;
      }
      if (this.yxddata.price[index].ctkPrice != "/" && this.yxddata.price[index].ctkPrice) {
        this.ctkprice = true;
      }
      if (this.yxddata.price[index].bgPrice != "/" && this.yxddata.price[index].bgPrice) {
        this.bgPrice = true;
      }
      if (this.yxddata.price[index].bbPrice != "/" && this.yxddata.price[index].bbPrice) {
        this.bbPrice = true;
      }
      if (this.yxddata.price[index].density != "/" && this.yxddata.price[index].density) {
        this.densityPrice = true;
      }
      if (this.yxddata.price[index].ktPrice != "/" && this.yxddata.price[index].ktPrice) {
        this.ktPrice = true;
      }
      if (this.yxddata.price[index].ljPrice != "/" && this.yxddata.price[index].ljPrice) {
        this.ljPrice = true;
      }
      if (this.yxddata.price[index].nxbPrice != "/" && this.yxddata.price[index].nxbPrice) {
        this.nxbPrice = true;
      }
      if (this.yxddata.price[index].bcPrice != "/" && this.yxddata.price[index].bcPrice) {
        this.bcPrice = true;
      }
      if (this.yxddata.price[index].tyPrice != "/" && this.yxddata.price[index].tyPrice) {
        this.tyPrice = true;
      }
      if (this.yxddata.price[index].wxbPrice != "/" && this.yxddata.price[index].wxbPrice) {
        this.wxbPrice = true;
      }
      if (this.yxddata.price[index].wlPrice != "/" && this.yxddata.price[index].wlPrice) {
        this.wlPrice = true;
      }
      if (this.yxddata.price[index].yjkPrice != "/" && this.yxddata.price[index].yjkPrice) {
        this.yjkPrice = true;
      }
      if (this.yxddata.price[index].zfPrice != "/" && this.yxddata.price[index].zfPrice) {
        this.zfPrice = true;
      }
      if (this.yxddata.price[index].zhPrice != "/" && this.yxddata.price[index].zhPrice) {
        this.zhPrice = true;
      }
      if (this.yxddata.price[index].zkPrice != "/" && this.yxddata.price[index].zkPrice) {
        this.zkPrice = true;
      }
      if (this.yxddata.price[index].other != "/" && this.yxddata.price[index].other) {
        this.otherprice = true;
      }
      if (this.yxddata.price[index].sampl != "/" && this.yxddata.price[index].sampl) {
        this.samplprice = true;
      }
      if (this.yxddata.price[index].pcs != "/" && this.yxddata.price[index].pcs) {
        this.pcsprice = true;
      }
      if (this.yxddata.price[index].discount != "/" && this.yxddata.price[index].discount) {
        this.discountprice = true;
      }
      if (this.yxddata.price[index].boardBrand != "/" && this.yxddata.price[index].boardBrand) {
        this.showboardBrand = true;
      }
      if (this.yxddata.price[index].boardT != "/" && this.yxddata.price[index].boardT) {
        this.showboardT = true;
      }
      if (this.yxddata.price[index].incu != "/" && this.yxddata.price[index].incu) {
        this.showincu = true;
      }
      if (this.yxddata.price[index].oucu != "/" && this.yxddata.price[index].oucu) {
        this.showoucu = true;
      }
      if (this.yxddata.price[index].lay != "/" && this.yxddata.price[index].lay) {
        this.showlay = true;
      }
      if (this.yxddata.price[index].size != "/" && this.yxddata.price[index].size) {
        this.showsize = true;
      }
      if (this.yxddata.price[index].su != "/" && this.yxddata.price[index].su) {
        this.showsu = true;
      }
      if (this.yxddata.price[index].surf != "/" && this.yxddata.price[index].surf) {
        this.showsurf = true;
      }
      if (this.yxddata.price[index].mask != "/" && this.yxddata.price[index].mask) {
        this.showmask = true;
      }
      if (this.yxddata.price[index].char_ != "/" && this.yxddata.price[index].char_) {
        this.showchar_ = true;
      }
      if (this.yxddata.price[index].solder != "/" && this.yxddata.price[index].solder) {
        this.showsolder = true;
      }
      if (this.yxddata.price[index].test != "/" && this.yxddata.price[index].test) {
        this.showtest = true;
      }
      if (this.yxddata.price[index].notes != "/" && this.yxddata.price[index].notes) {
        this.shownotes = true;
      }
      if (this.yxddata.price[index].custdate != "/" && this.yxddata.price[index].custdate) {
        this.showcustdate = true;
      }
      if (this.yxddata.price[index].qty != "/" && this.yxddata.price[index].qty) {
        this.showqty = true;
      }
      if (this.yxddata.price[index].custPo != "/" && this.yxddata.price[index].custPo) {
        this.showcustPo = true;
      }
      if (this.yxddata.price[index].poreDensity != "/" && this.yxddata.price[index].poreDensity) {
        this.showporeDensity = true;
      }
      if (this.yxddata.price[index].freight != "/" && this.yxddata.price[index].freight) {
        this.freightprice = true;
      }
      if (this.yxddata.price[index].tax != "/" && this.yxddata.price[index].tax) {
        this.taxprice = true;
      }
      if (this.yxddata.price[index].preferential != "/" && this.yxddata.price[index].preferential) {
        this.preferentialprice = true;
      }
      if (this.yxddata.price[index].finger != "/" && this.yxddata.price[index].finger) {
        this.fingerprice = true;
      }
      if (this.yxddata.price[index].resin != "/" && this.yxddata.price[index].resin) {
        this.resinprice = true;
      }
      if (this.yxddata.price[index].cuthic != "/" && this.yxddata.price[index].cuthic) {
        this.cuthicprice = true;
      }
      if (this.yxddata.price[index].cvut != "/" && this.yxddata.price[index].cvut) {
        this.cvutprice = true;
      }
      if (this.yxddata.price[index].boardPrice != "/" && this.yxddata.price[index].boardPrice) {
        this.thicknessprice = true;
      }
      if (this.yxddata.price[index].halfHole != "/" && this.yxddata.price[index].halfHole) {
        this.halfHoleprice = true;
      }
      if (this.yxddata.price[index].imp != "/" && this.yxddata.price[index].imp) {
        this.impprice = true;
      }
      if (this.yxddata.price[index].diff != "/" && this.yxddata.price[index].diff) {
        this.diffprice = true;
      }
      if (this.yxddata.price[index].surface != "/" && this.yxddata.price[index].surface) {
        this.surfaceprice = true;
      }
      if (this.yxddata.price[index].hpPrice != "/" && this.yxddata.price[index].hpPrice) {
        this.hpprice = true;
      }
      if (this.yxddata.price[index].eng != "/" && this.yxddata.price[index].eng) {
        this.engprice = true;
      }
      if (this.yxddata.price[index].film != "/" && this.yxddata.price[index].film) {
        this.filmprice = true;
      }
      if (this.yxddata.price[index].fly != "/" && this.yxddata.price[index].fly) {
        this.flyprice = true;
      }
      if (this.yxddata.price[index].plate != "/" && this.yxddata.price[index].plate) {
        this.plateprice = true;
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
    if (this.showboardBrand) {
      this.workmanship += 1;
    }
    if (this.showboardT) {
      this.workmanship += 1;
    }
    if (this.showincu) {
      this.workmanship += 1;
    }
    if (this.showoucu) {
      this.workmanship += 1;
    }
    if (this.showlay) {
      this.workmanship += 1;
    }
    if (this.showsize) {
      this.workmanship += 1;
    }
    if (this.showsu) {
      this.workmanship += 1;
    }
    if (this.showsurf) {
      this.workmanship += 1;
    }
    if (this.showmask) {
      this.workmanship += 1;
    }
    if (this.showchar_) {
      this.workmanship += 1;
    }
    if (this.showsolder) {
      this.workmanship += 1;
    }
    if (this.showtest) {
      this.workmanship += 1;
    }
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
      this.showadd = !this.yxddata.price.some(ite => ite.status == 30);
    },
    printpdf() {
      document.title = this.yxddata.pcbFileName;
      this.showadd = false;
    },
    delclick(id) {
      this.ids = [];
      this.yxddata.price.forEach(item => {
        if (this.ids.indexOf(item.id) == -1) {
          this.ids.push(item.id);
        }
      });
      deletecontractno(id).then(res => {
        if (res.code) {
          this.ids.forEach(item => {
            if (item == id) {
              this.ids.splice(this.ids.indexOf(item), 1);
            }
          });
          this.$message.success("删除成功");
          // this.$emit('getOrderList')
          this.$emit("YXDform", this.ids, "YXD");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowKeys;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowKeys = rowKeys;
          },
        },
      };
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeys.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysind = selectedRowKeys;
      this.selectedRowKeys = [];
      selectedRows.forEach(item => {
        this.selectedRowKeys.push(item.id);
      });
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    queryclick() {
      if (!this.OrderNo && !this.PcbFileName) {
        this.$message.error("请输入订单号或客户型号进行查询");
        return;
      }
      let params = {
        PageIndex: 1,
        PageSize: 20,
        CustNo: this.salescustno,
        ContractNoSech: this.ContractNoSech,
      };
      if (this.OrderNo) {
        params.OrderNo = this.OrderNo;
      }
      if (this.PcbFileName) {
        params.PcbFileName = this.PcbFileName;
      }
      verifyPageList(params).then(res => {
        if (res.code) {
          this.datasource = res.data.items;
          if (this.datasource.length == 0) {
            this.$message.error("未查询到相关订单");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    addcontract() {
      this.modalvisible = true;
      this.selectedRowKeys = [];
      this.datasource = [];
      this.OrderNo = "";
      this.PcbFileName = "";
      this.selectedRowKeysind = [];
    },
    handleOk() {
      if (this.selectedRowKeys.length == 0) {
        this.$message.error("请选择订单");
        return;
      }
      this.ids = [...this.ids, ...this.selectedRowKeys];
      contractNo(this.ids).then(res => {
        if (res.code) {
          this.$emit("getOrderList");
          if (this.joinFactoryId == "58" || this.joinFactoryId == "59" || this.joinFactoryId == "65" || this.joinFactoryId == "38") {
            this.$emit("YXDform", this.ids, "YXD");
          }
        } else {
          this.$message.error(res.message);
        }
      });
      this.modalvisible = false;
    },
    handleCancel() {
      this.modalvisible = false;
    },
    getReportPdf() {
      this.showadd = false;
      let dom;
      setTimeout(() => {
        if (this.joinFactoryId == 38) {
          dom = "pdfDomlhdc";
        } else {
          dom = "pdfDom";
        }
        const element = document.getElementById(dom);
        const opt = {
          margin: 0,
          filename: this.yxddata.pcbFileName + ".pdf",
          image: { type: "jpeg", quality: 1 },
          html2canvas: { scale: 1.5 },
          jsPDF: {
            unit: "in",
            format: "a4",
            orientation: "landscape",
          },
          pagebreak: { mode: ["css", "legacy"] },
        };
        html2pdf().set(opt).from(element).save();
        this.showadd = !this.yxddata.price.some(ite => ite.status == 30);
      }, 500);
    },
  },
};
</script>

<style lang="less" scoped>
td {
  word-wrap: break-word !important;
  white-space: normal !important;
}
@media print {
  td {
    word-wrap: break-word !important;
    white-space: normal !important;
  }
}
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
/deep/.rowBackgroundColor {
  background: #dcdcdc !important;
}
/deep/.ant-table-row-selected {
  background: #dcdcdc !important;
}
/deep/.ant-table-tbody > tr.ant-table-row-selected td {
  background: #dcdcdc;
}
/deep/.ant-table {
  border: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-thead > tr > th {
  padding: 4px 4px;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
}
/deep/ .ant-table-tbody > tr > td {
  padding: 4px 4px !important;
  border-right: 1px solid #efefef;
  border-left: 1px solid #efefef;
  max-width: 100px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
  color: #000000;
}
//   .transparent-image {
//   filter: invert(1) hue-rotate(180deg);
// }
.agreement {
  padding-bottom: 20px;
  position: relative;
  z-index: 99;
  div {
    padding-top: 10px;
  }
}
.tableclass {
  border: 1px solid black;
  margin-top: 10px;
  margin-bottom: 10px;
  td {
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding-left: 7px;
  }
  tbody {
    tr {
      height: 24px;
    }
  }
}
.pdfDom1 {
  font-family: "微软雅黑";
  font-size: 12px;
  height: 650px;
  overflow: auto;
}
</style>
