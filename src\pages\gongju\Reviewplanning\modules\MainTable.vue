<template>
  <div>
    <a-table
      :columns="reviewcolumns"
      :rowClassName="isRedRow"
      :pagination="pagination"
      @change="handleTableChange"
      :customRow="onClickRow"
      :scroll="{ y: 738, x: 1200 }"
      :rowKey="(record, index) => `${index + 1}`"
      :dataSource="Reviewsource"
      class="leftstyle"
    >
      <span slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </span>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>
<script>
import RightCopy from "@/pages/RightCopy";
export default {
  name: "MainTable",
  props: ["reviewcolumns", "pagination", "Reviewsource"],
  data() {
    return {
      proOrderId: "",
      selecdata: {},
    };
  },
  components: {
    RightCopy,
  },
  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.$emit("handleTableChange", pagination);
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && record.id == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.selecdata = record;
            this.proOrderId = record.id;
          },
          contextmenu: e => {
            e.preventDefault();
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
  },
};
</script>
<style lang="less" scoped></style>
