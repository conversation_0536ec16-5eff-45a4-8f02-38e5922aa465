<!-- 工程管理 - 工程制作 - 项目参数 中间列表 -->
<template>
<a-table
    style="word-break: break-all"
    :columns="columns"
    :dataSource="dataSource"
    class="centerTable1"
    :scroll="{y: 704,x:340}"
    :pagination="false"
    :rowKey="rowKey"
    :customRow="onClickRow"
    :rowClassName="isRedRow"
    :loading="orderDetailTableLoading"
   >
    <template slot="customRender" slot-scope="text,record" >
      <template >
        {{ text }}
        <a-tag color="orange" v-if="record.isLeave_" style="font-size: 10px;font-weight: 600; padding: 0 2px;height: 21px;" >
        休
      </a-tag>
      </template>
     
    </template>
    <template slot="action" slot-scope="text,record" >
      <a-tooltip title="取单设置" v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendMIIsLeave')"
        :style="record.realName=='合计' ?{display:'none'}:''">
        <a-icon type="edit" style="color: #ff9900; font-size: 18px;" @click.stop="OrderRetrievalSettingsClick(record)"/>
      </a-tooltip>
    </template>
    <span slot="num" slot-scope="text, record, index" > 
      {{index+1}}
    </span>
  <div slot="parameter" slot-scope="text,record" :title="text" style="height: 19px">
    <span  v-if="(record.projectName == '阻焊颜色(顶)/(底)' || record.projectName == '字符颜色(顶)/(底)') && record.parameter != '无/无'" 
    style="width: 121px;display: inline-block;overflow: hidden;text-overflow: ellipsis;">
      <div v-if="record.parameter.split('/')[0].indexOf('绿') >= 0" style="width: 13px;height:13px;background: green;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('红')>= 0" style="width: 13px;height:13px;background: red;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('白')>= 0" style="width: 13px;height:13px;background: white;display: inline-block;border:1px solid black"> </div>
      <div v-if="record.parameter.split('/')[0].indexOf('黑')>= 0" style="width: 13px;height:13px;background: black;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('黄')>= 0" style="width: 13px;height:13px;background: yellow;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('蓝')>= 0" style="width: 13px;height:13px;background: blue;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('咖')>= 0" style="width: 13px;height:13px;background: #5D2000;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('紫')>= 0" style="width: 13px;height:13px;background: #4a12e4;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('橙')>= 0" style="width: 13px;height:13px;background: #e45f12;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('灰')>= 0" style="width: 13px;height:13px;background: #979797;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[0].indexOf('透明')>= 0" style="width: 13px;height:13px;background: #fefdff00;display: inline-block;border:1px solid black"></div>
      {{text.split('/')[0]}}/
      <div v-if="record.parameter.split('/')[1].indexOf('绿') >= 0" style="width: 13px;height:13px;background: green;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('红')>= 0" style="width: 13px;height:13px;background: red;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('白')>= 0" style="width: 13px;height:13px;background: white;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('黑')>= 0" style="width: 13px;height:13px;background: black;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('黄')>= 0" style="width: 13px;height:13px;background: yellow;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('蓝')>= 0" style="width: 13px;height:13px;background: blue;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('咖')>= 0" style="width: 13px;height:13px;background: #5D2000;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('紫')>= 0" style="width: 13px;height:13px;background: #4a12e4;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('橙')>= 0" style="width: 13px;height:13px;background: #e45f12;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('灰')>= 0" style="width: 13px;height:13px;background: #979797;display: inline-block;border:1px solid black"></div>
      <div v-if="record.parameter.split('/')[1].indexOf('透明')>= 0" style="width: 13px;height:13px;background: #ffffff00;display: inline-block;border:1px solid black"></div>
      {{ text.split('/')[1] }}
    </span>     
    <span style="width: 121px;display: inline-block;overflow: hidden;text-overflow: ellipsis;" v-else> {{ text }}</span>
  </div>
</a-table>
</template>

<script>
import {checkPermission} from "@/utils/abp";
export default {
  name: "OrderDetail",
  props: {
    columns:{
      type: Array,
      require: true
    },
    dataSource:{
      type: Array,
      require: true
    },
    orderDetailTableLoading:{
      type: Boolean,
      require: true
    },
    rowKey:{
      type: String,
      require: true
    }
  },
  data(){
    return {
      userLoginID_:'',
    }
  },
  methods:{
    checkPermission,
    onClickRow(record) {
      return {
        on: {
          click: () => {
            if (record.userLoginID_ && this.userLoginID_ != record.userLoginID_){
              this.$emit('getPeopleOrderList', record.userLoginID_)
            }
            this.$emit('saveErpKey', record)
            this.userLoginID_ = record.userLoginID_
          }
        }
      }
    },
    isRedRow (record) {
      // console.log(record)
      let strGroup = []
      let str =[]
      if (record.userLoginID_ && record.userLoginID_ == this.userLoginID_) {
        strGroup.push('rowBackgroundColor')
      }
      if(record.isLeave_ == true){ // 是否请假
        str.push('rowSty')
      }

      // if(str.length > 1){
      //   str = [str[0]]
      // }
      // console.log('str.concat(strGroup):',str.concat(strGroup))
      return str.concat(strGroup)

    },
    OrderRetrievalSettingsClick(record){
      this.$emit('OrderRetrievalSettingsClick',record)
    }
  },
}
</script>

<style scoped lang="less">
.tablestyle{
  /deep/.ant-table-body{
    min-height: 702px;
  }
}
@media screen and (min-width: 1700px) {
  .centerTable1 {
    overflow-x: auto !important;
    /deep/ .ant-table-body {
      .ant-table-tbody {
        .ant-table-row:last-child {
          .contentTabAction {
            display: none;
          }
        }
      }
      //   min-height: 743px;
    }
    
    /deep/.rowBackgroundColor {
      background: rgb(223, 220, 220) !important;
    }
  }
}
///deep/ .ant-table-body {
//  overflow-y: auto!important;
//}

/deep/ .ant-table {
  // .ant-table-tbody{
  //   tr{
  //     td{
  //       padding:3px 0!important;
  //     }
  //   }
  // }
  .rowSty {
    td {
      color: #DC143C;
    }
  }
  .peopleTag {
    position: absolute;
    font-size: 12px;
    font-weight: 600;
    left: 0;
    padding: 0 2px;
  }
  .rowSty1{
    td{
      color: #2828FF;
    }
  }
  .rowBackgroundColor {
      background: #aba5a5;
    }
}


</style>
