import html2Canvas from 'html2canvas'
import JsPDF  from 'jspdf'
const getPdf = async (dom, title) => {
    await new Promise((resolve, reject) => {
        try {
            // 避免模糊，放大倍数,倍数可调整
            const el = document.getElementById(dom)
            var c = document.createElement('canvas')      
            html2Canvas(el, {
                allowTaint: false,
                taintTest: false,
                logging: false,
                useCORS: true,
                dpi: 3, // 将分辨率提高到特定的DPI 提高四倍
                scale: 3 // 按比例增加分辨率
              }).then(canvas => {
                let contentWidth = canvas.width
                let contentHeight = canvas.height
                // let pageHeight = contentWidth / 592.28 * 841.89 // 一页pdf显示html页面生成的canvas高度;
                let pageHeight = contentWidth /  841.89 * 595.28 // 一页pdf显示html页面生成的canvas高度(横版pdf);
                let leftHeight = contentHeight //未生成pdf的html页面高度
                let position = 20 //pdf页面偏移

                //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
                // let imgWidth = 595.28
                // let imgWidth = 560.28  //宽度
                // let imgHeight = 592.28 / contentWidth * contentHeight
                let imgWidth = 808  //宽度（横版pdf）
                let imgHeight = 841.89 / contentWidth * contentHeight//（横版pdf）
                let pageData = canvas.toDataURL('image/jpeg', 1.0)
                let PDF = new JsPDF('l', 'pt', 'a4')
                if (leftHeight < pageHeight) {
                  PDF.addImage(pageData, 'JPEG', 20, 20, imgWidth, imgHeight)
                } else {
                  while (leftHeight > 0) {
                    PDF.addImage(pageData, 'JPEG', 20, position, imgWidth, imgHeight)
                    leftHeight -= pageHeight
                    position -= 595.28//(横版pdf)
                    if (leftHeight > 0) {
                      PDF.addPage()
                    }
                  }
                }
                // 保存文件
                 PDF.save(title + '.pdf')
                resolve()
              })
        } catch (error) {
            reject(error)
        }
    })
}
export default getPdf
