<template>
  <a-modal
    :title="model.id? '编辑' : '新增'"
    :width="640"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="等级" ref="productType_" prop="grade">
           <a-select  style="width: 120px" v-model="form.grade">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="A">
                    A
                </a-select-option>
                <a-select-option value="B">
                    B
                </a-select-option>
                <a-select-option value="C">
                    C
                </a-select-option>
               <a-select-option value="D">
                 D
               </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="备注" ref="orderType_">
          <a-input
              v-model="form.note"
              placeholder="请输入"
          />
        </a-form-model-item>
        <a-form-model-item  label="评级时间" ref="machineType_" prop="gradeDate">
          <a-month-picker placeholder="选择月份" v-model="form.gradeDate" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import {addBoard, updateBoard, progressNum, addDeliveryData, updateDeliveryData} from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
      compileApply:{
          type: String,
          default () {
              return ''
          }
      },
      message:{
          type: String,
          default () {
              return ''
          }
      },
      dataType:{
          type: Number
      }
  },
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {
        note: '',
        grade: '',
        gradeDate: '',
        number:'',
      },
      rules: {
        grade: [
          { required: true, message: "请选择产品等级", trigger: "blur" },
        ],
        gradeDate: [
            { required: true, message: "请选择评级时间", trigger: "change" },
        ],
        // number:[
        //   { required: true, message: "请输入评分", trigger: "blur" },
        // ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
      console.log('model',model)
      //  if(this.compileApply=='1'){
           this.visible = true;
           this.model = model
           if(model && model.id) {
               this.form = {
                 grade: model.grade,
                 note: model.note,
                 gradeDate: model.gradeDate,
               };
           }else {
               //清空form操作过的数据
               this.form= {
                 gradeDate: '',
                 grade: '',
                 note: '',
               }
           }
      //  }else {
      //      this.$message.info(this.message)
      //  }


    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
       this.form= {
         gradeDate: '',
         grade: '',
         note: '',
        },
      this.$refs.ruleForm.resetFields()
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          console.log(this.dataType)
          debugger
          let params = {
            pGuid: this.suppId,
            ratingType: this.dataType,
            ...this.form
          }
          if(this.model.id) {
            params['id'] = this.model.id
            updateDeliveryData(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info(res.message);
                    this.$emit("ok", this.dataType);
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }else{
            addDeliveryData(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info(res.message);
                    this.$emit("ok",this.dataType);
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped>
</style>
