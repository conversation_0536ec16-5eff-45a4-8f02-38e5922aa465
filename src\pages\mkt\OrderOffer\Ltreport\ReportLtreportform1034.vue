<!--龙腾报价表单  -->
<template>
  <div class="pdfDom1" style="font-size: 16px">
    <a-button
      v-print="printObj1"
      @click="printpdf"
      type="primary"
      class="printstyle"
      >打印</a-button
    >
    <div
      id="ltreport1034"
      style="padding: 25px; color: black; font-family: 宋体"
    >
      <div style="display: flex">
        <div
          style="
            font-size: 30px;
            font-weight: bold;
            padding-left: 30px;
            width: 30%;
          "
        >
          供应商
        </div>
        <div style="font-size: 30px; font-weight: bold; padding-left: 30px">
          湖北龙腾电子科技股份有限公司
        </div>
      </div>

      <div style="display: flex">
        <div style="width: 45%">
          <img src="@/assets/img/ats.png" style="padding-left: 10px" />
        </div>
        <div style="font-size: 30px; font-weight: bold; letter-spacing: 1ch">
          报价单
        </div>
      </div>
      <div style="text-align: right">报价日期:{{ LTreportdata.value_19 }}</div>
      <div>
        <table border="1" style="width: 100%">
          <tr>
            <td>TO:</td>
            <td colspan="3" style="text-align: left">
              {{ LTreportdata.value_1 }}
            </td>
            <td>FROM:</td>
            <td colspan="11" style="text-align: left">
              {{ LTreportdata.value_7 }}
            </td>
          </tr>
          <tr>
            <td>联系人</td>
            <td colspan="3" style="text-align: left">
              {{ LTreportdata.value_2 }}
            </td>
            <td>联系人</td>
            <td colspan="11" style="text-align: left">
              {{ LTreportdata.value_8 }}
            </td>
          </tr>
          <tr>
            <td>手机</td>
            <td colspan="3" style="text-align: left">
              {{ LTreportdata.value_3 }}
            </td>
            <td>手机</td>
            <td colspan="11" style="text-align: left">
              {{ LTreportdata.value_9 }}
            </td>
          </tr>
          <tr>
            <td>电话</td>
            <td colspan="3" style="text-align: left">
              {{ LTreportdata.value_4 }}
            </td>
            <td>电话</td>
            <td colspan="11" style="text-align: left">
              {{ LTreportdata.value_10 }}
            </td>
          </tr>
          <tr>
            <td>传真</td>
            <td colspan="3" style="text-align: left">
              {{ LTreportdata.value_5 }}
            </td>
            <td>传真</td>
            <td colspan="11" style="text-align: left">
              {{ LTreportdata.value_11 }}
            </td>
          </tr>
          <tr>
            <td>地址</td>
            <td colspan="3" style="text-align: left">
              {{ LTreportdata.value_6 }}
            </td>
            <td>地址</td>
            <td colspan="11" style="text-align: left">
              {{ LTreportdata.value_12 }}
            </td>
          </tr>
          <tr style="height: 20px">
            <td style="border: none; border-left: 1px solid white"></td>
            <td style="border: none" colspan="3"></td>
            <td style="border: none"></td>
            <td
              style="border: none; border-right: 1px solid white"
              colspan="11"
            ></td>
          </tr>
          <tr>
            <td style="width: 60px">序号</td>
            <td>品名/料号</td>
            <td>规格型号品牌</td>
            <td>规格要求</td>
            <td colspan="2">尺寸(SIZE)MM</td>
            <td>拼板panel</td>
            <td>层数</td>
            <td>数量</td>
            <td>单价含税(RMB/PCS)</td>
            <td>最小包装M.P.Q</td>
            <td>测试费</td>
            <td>样品费</td>
            <td>平米价</td>
            <td>总平米价</td>
            <td>备 注</td>
          </tr>
          <tr v-for="(item, index) in LTreportdata.price" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ item.price1 }}</td>
            <td>{{ item.price2 }}</td>
            <td>{{ item.price3 }}</td>
            <td style="width: 60px">{{ item.price4 }}</td>
            <td>{{ item.price5 }}</td>
            <td>{{ item.price6 }}</td>
            <td>{{ item.price7 }}</td>
            <td>{{ item.price8 }}</td>
            <td>{{ item.price9 }}</td>
            <td>{{ item.price10 }}</td>
            <td>{{ item.price11 }}</td>
            <td>{{ item.price12 }}</td>
            <td>{{ item.price13 }}</td>
            <td>{{ item.price14 }}</td>
            <td>{{ item.price15 }}</td>
          </tr>
          <tr>
            <td></td>
            <td></td>
            <td>合计</td>
            <td>{{ amountto }}</td>
            <td colspan="11"></td>
          </tr>
          <tr>
            <td>1</td>
            <td colspan="15" style="text-align: left">
              所有报价均须为含税价, 并按实际货物开具13%的增值税专用发票。
            </td>
          </tr>
          <tr>
            <td>2</td>
            <td colspan="15" style="text-align: left">
              交易支付方式： <a-checkbox :checked="true" /> 月结30天
              <a-checkbox :checked="false" /> 预付30%,其它货到合格后15天付款
              <a-checkbox :checked="false" /> 款到发货
            </td>
          </tr>
          <tr>
            <td>3</td>
            <td style="text-align: left">货期时间</td>
            <td colspan="14" style="text-align: left">
              {{ LTreportdata.discer }}
            </td>
          </tr>
          <tr>
            <td>4</td>
            <td style="text-align: left">运费</td>
            <td colspan="14" style="text-align: left">
              {{ LTreportdata.value_16 }}
            </td>
          </tr>
          <tr>
            <td>5</td>
            <td style="text-align: left">送货</td>
            <td colspan="14" style="text-align: left">
              送货到深圳爱图仕工厂 <a-checkbox :checked="true" /> 其它：
            </td>
          </tr>
          <tr>
            <td>6</td>
            <td colspan="15" style="text-align: left">
              必须符合欧盟RoHS标准，符合各国法律法规要求，详细的质量要求按深圳市爱图仕影像器材有限公司《质量协议》执行。
            </td>
          </tr>
          <tr>
            <td>7</td>
            <td colspan="15" style="text-align: left">
              报价单供方必须签字盖章，否则需方将视为无效报价。
            </td>
          </tr>
        </table>
      </div>
      <div
        style="display: flex; padding-top: 15px; line-height: 5ch; width: 100%"
      >
        <div style="width: 50%">
          <div>
            供&emsp;&emsp;方： <span class="Underline"></span
            >{{ LTreportdata.value_18 }}
          </div>
          <div>
            日&emsp;&emsp;期： <span class="Underline"></span
            >{{ LTreportdata.value_19 }}
          </div>
          <div>签字盖章： <span class="Underline"></span></div>
        </div>
        <div>
          <div>客户确认： <span class="Underline"></span></div>
          <div>日&emsp;&emsp;期： <span class="Underline"></span></div>
          <div>签字盖章： <span class="Underline"></span></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import htmlToPdfa3 from "@/utils/htmlToPdfa3"; //横版a4
import { Checkbox } from "vxe-table";
export default {
  name: "",
  props: ["LTreportdata", "ttype"],
  computed: {},
  data() {
    return {
      printObj1: {
        id: "ltreport1034", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
    this.amountto = 0;
    for (let index = 0; index < this.LTreportdata.price.length; index++) {
      if (
        this.LTreportdata.price[index].total &&
        this.LTreportdata.price[index].total != "/"
      ) {
        this.amountto += Number(this.LTreportdata.price[index].total);
      }
    }
    this.amountto = this.amountto ? this.getFloat(this.amountto, 4) : 0;
  },
  methods: {
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.LTreportdata.pcbFileName;
    },
    term(text) {
      return text.replace(/\|/g, "\n");
    },
    getreportPdf() {
      htmlToPdf("ltreport1034", this.LTreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.Underline {
  position: relative;
  display: inline-block;
}
.Underline::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px; /* 下划线距离文字底部的距离 */
  width: 250px; /* 下划线宽度 */
  height: 2px; /* 下划线高度 */
  background-color: rgb(107, 106, 106); /* 下划线颜色 */
}
/deep/.ant-checkbox-checked .ant-checkbox-inner {
  background-color: black;
  border-color: black;
}
/deep/.ant-checkbox-inner {
  border: 1px solid black;
}
/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: black;
}
/deep/.ant-checkbox-input:hover + .ant-checkbox-inner {
  border-color: black;
}
/deep/.ant-checkbox-checked::after {
  border-color: black;
}
table > tr > td {
  padding: 3px 5px;
  border: 1px solid black;
  text-align: center;
}
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
