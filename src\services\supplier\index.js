import { request, METHOD } from '@/utils/request'
export async function getColumn(params) {
    return request("/api/app/e-mSFrm-ui-dgv-config/get-dgv-list", METHOD.GET, params)
}

export async function getData(params) {
    return request("/api/app/e-mSSupplier-module-no/get-sup-module-list", METHOD.GET, params)
}
export async function getGroupList(params) {
    return request("/api/app/e-mSFrm-ui-group-config/get-group-list", METHOD.GET, params)
}
export async function getDataInfo(params) {
    return request("/api/app/e-mSSupplier-module-no/{id}/getsup-parinfo", METHOD.GET, params)
}
export async function getSup(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/getsup-parinfo`, METHOD.GET)
}
export async function editData(params) {
    return request(`/api/app/e-mSSupplier-module-no/updatesuplierinfo`, METHOD.POST,params)
}
export async function addSupplier(params) {
    return request(`/api/app/e-mSSupplier-module-no/supplier`, METHOD.POST,params)
}
export async function deleteCust(id) {
    return request(`/api/app/e-mSSupplier-customer-info/${id}/delete-supplier-customer-info`, METHOD.DELETE)
}
export async function getCust(id,params) {
    return request(`/api/app/e-mSSupplier-customer-info/${id}/get-supplier-customer-info`, METHOD.GET,params)
}
export async function addCust(params) {
    return request(`/api/app/e-mSSupplier-customer-info/create-update-supplier-customer-info`, METHOD.POST,params)
}
export async function deleteEquip(id) {
    return request(`/api/app/e-mSSupplier-product-equipment/${id}/delete-supplier-product-equipment`, METHOD.DELETE)
}
export async function getEquip(id,params) {
    return request(`/api/app/e-mSSupplier-product-equipment/${id}/get-supplier-product-equipment`, METHOD.GET,params)
}
export async function addEquip(params) {
    return request(`/api/app/e-mSSupplier-product-equipment/create-update-supplier-product-equipment`, METHOD.POST,params)
}
export async function deleteQualify(id) {
    return request(`/api/app/e-mSSupplier-qualification/supplier-qualification/${id}`, METHOD.DELETE)
}
export async function getQualify(id,params) {
    return request(`/api/app/e-mSSupplier-qualification/supplier-qualification/${id}`, METHOD.GET,params)
}
export async function addQualify(params) {
    return request(`/api/app/e-mSSupplier-qualification/a-dDSupplier-qualification`, METHOD.POST,params)
}
export async function updateQualify(id,params) {
    return request(`/api/app/e-mSSupplier-qualification/update-supplier-qualification/${id}`, METHOD.POST,params)
}
export async function addFactory(params) {
    return request(`/api/app/e-mSSupplier-photo-certification/create-update-supplier-photo-certification`, METHOD.POST,params)
}
export async function getFactory(id,params) {
    return request(`/api/app/e-mSSupplier-photo-certification/${id}/get-sSupplier-photo-certification`, METHOD.GET,params)
}
export async function deleteFactory(id) {
    return request(`/api/app/e-mSSupplier-photo-certification/${id}/supplier-photo-certification`, METHOD.DELETE)
}
export async function addAssest(params) {
    return request(`/api/app/e-mSSupplier-evaluation-result/supplier-evaluation-result`, METHOD.POST,params)
}
export async function deleteAssest(id) {
    return request(`/api/app/e-mSSupplier-evaluation-result/supplier-evaluation-result/${id}`, METHOD.DELETE)
}
export async function updateAssest(id,params) {
    return request(`/api/app/e-mSSupplier-evaluation-result/update-supplier-evaluation-result/${id}`, METHOD.POST,params)
}
export async function getAssest(id,params) {
    return request(`/api/app/e-mSSupplier-evaluation-result/supplier-evaluation-result/${id}`, METHOD.GET,params)
}
export async function getBoard(id,params) {
    return request(`/api/app/e-mSSupplier-board/supplier-board/${id}`, METHOD.GET,params)
}
export async function updateBoard(id,params) {
    return request(`/api/app/e-mSSupplier-board/update-supplier-board/${id}`, METHOD.POST,params)
}
export async function deleteBoard(id) {
    return request(`/api/app/e-mSSupplier-board/supplier-board/${id}`, METHOD.DELETE)
}
export async function addBoard(params) {
    return request(`/api/app/e-mSSupplier-board/supplier-board`, METHOD.POST,params)
}
export async function getlog(id,params) {
    return request(`/api/app/e-mSSupplier-visit-info/supplier-visit-info-list/${id}`, METHOD.GET,params)
}
export async function addlog(params) {
    return request(`/api/app/e-mSSupplier-visit-info/supplier-visit-info`, METHOD.POST,params)

}
export async function updatelog(id,params) {
    return request(`/api/app/e-mSSupplier-visit-info/update-supplier-visit-info/${id}`, METHOD.POST,params)
}
export async function deletelog(id) {
    return request(`/api/app/e-mSSupplier-visit-info/supplier-visit-info/${id}`, METHOD.DELETE)
}
export async function getLink(id,params) {
    return request(`/api/app/e-mSSupplier-linkman/${id}/get-supplier-linkmans`, METHOD.GET,params)
}
export async function addLink(params) {
    return request(`/api/app/e-mSSupplier-linkman/create-update-supplier-linkman`, METHOD.POST,params)
}
export async function deleteLink(id) {
    return request(`/api/app/e-mSSupplier-linkman/${id}/delete-supplier-linkman`, METHOD.DELETE)
}
export async function getPhoto(id,params) {
    return request(`/api/app/e-mSSupplier-imge-to-oss/${id}/getphotolist`, METHOD.GET,params)
}
export async function uploadFile(params) {
    return request(`/api/file-management/files/supplieruploadpfile`, METHOD.POST,params)
}
export async function uploadImg(params) {
    return request(`/api/app/e-mSSupplier-imge-to-oss/supplier-imge-to-oss`, METHOD.POST,params)
}
export async function modulenouploadfile(params) {
    return request(`/api/app/e-mSSupplier-module-no/up-load-file`, METHOD.POST,params)
}
export async function getSelectFact(params) {
    return request(`/api/app/e-mSTMkt-manufacturer`, METHOD.GET,params)
}
export async function getSelectFactPage(params) {
    return request(`/api/app/e-mSTMkt-manufacturer/page-list`, METHOD.GET,params)
}
export async function deleteFactPage(param) {
    return request(`/api/app/e-mSTMkt-manufacturer/async-by-key`, METHOD.DELETE,param)
}
export async function addSelectFact(params) {
    if(params.id) {
        return request(`/api/app/e-mSTMkt-manufacturer/update`, METHOD.POST,params)
    }else{
        return request(`/api/app/e-mSTMkt-manufacturer`, METHOD.POST,params)
    }
}

export async function commite(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/commite-audit`, METHOD.POST,params)
}
export async function reject(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/reject-audit`, METHOD.POST,params)
}

export async function agree(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/agree-audit`, METHOD.POST,params)
}
export async function followPeople(id,params) {
    return request(`/api/app/e-mSUser/follow-items`, METHOD.GET,params)
}

export async function getfollow(param) {
    return request(`/api/app/e-mSSupplier-module-no/send-user`, METHOD.POST,param)
}
export async function check(id,params) {
  return request(`/api/app/e-mSSupplier-visit-info/check-supplier-visit-info/${id}`, METHOD.POST,params)
}
export async function submitLog(Id ,params) {
  return request(`/api/app/e-mSSupplier-visit-info/commite-supplier-visit-info/${Id}`, METHOD.POST,params)
}
export async function roundLog(id,params) {
  return request(`/api/app/e-mSSupplier-visit-info/visit-supplier-visit-info/${id}`, METHOD.POST,params)
}

export async function applicationChange(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/commite-edit`, METHOD.POST)
}
export async function ignoreChange(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/reject-edit`, METHOD.POST)
}
export async function passChange(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/agree-edit`, METHOD.POST)
}
export async function finishChange(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/end-edit`, METHOD.POST)
}

export async function authorityChange(id,params) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/is-allow-edit`, METHOD.POST)
}

export async function caozuoLog(Id,params) {
    return request(`/api/app/e-mSSupplier-module-no/supplier-log/${Id}`, METHOD.GET)
}
export async function progressNum(Id,params) {
    return request(`/api/app/e-mSSupplier-module-no/set-data-completion/${Id}`, METHOD.POST)
}
export async function exportD(params) {
    return request("/api/app/e-mSSupplier-module-no/supplier-data-output", METHOD.GET,params)
}
export async function buillData(params) {
    return request("/api/app/e-mSTPub-factory-configure/order-form-list", METHOD.POST,params)
}
export async function buillRepair(params) {
    return request("/api/app/e-mSTPub-factory-configure/update", METHOD.POST,params)
}

export async function billList(params) {
    return request("/api/app/e-mSTMkt-supplier-lssuing-settings?Pid="+params, METHOD.GET)
}
export async function billAdd(params) {
    return request("/api/app/e-mSTMkt-supplier-lssuing-settings", METHOD.POST,params)
}

export async function setBill(id) {
    return request(`/api/app/e-mSTMkt-supplier-lssuing-settings/${id}/async-by-id`, METHOD.GET,)
}
export async function deleBill(id) {
    return request(`/api/app/e-mSTMkt-supplier-lssuing-settings/${id}/async-by-id`, METHOD.DELETE,)
}
export async function billAmand(params) {
    return request("/api/app/e-mSTMkt-supplier-lssuing-settings/update", METHOD.POST,params)
}

// 制程能力参数配置获取
export async function getcapacityConfig() {
    return request(`/api/app/e-mSSupplier-module-no/manufacturing-capacity-config`, METHOD.GET)
}

// 制程能力信息获取
export async function getProcessCapability(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/manufacturing-capacity`, METHOD.GET)
}
// 制程能力信息修改
export async function editProcessCapability(params) {
    return request("/api/app/e-mSSupplier-module-no/update-manufacturing-capacity", METHOD.POST,params)
}
// 交期评级数据
export async function getDeliveryData(params) {
    return request("/api/app/e-mSTMkt-supplier-rating/page-list", METHOD.GET,params)
}
// 质量评级数据
export async function qualityRatingList(params) {
    return request("/api/app/e-mSTMkt-supplier-rating/get-quality-rating-list", METHOD.POST,params)
}
// 质量评级综合数据
export async function qualityRatingList2(params) {
    return request("/api/app/e-mSTMkt-supplier-rating/get-quality-rating-list2", METHOD.POST,params)
}
// 添加评级数据
export async function addDeliveryData(params) {
    return request("/api/app/e-mSTMkt-supplier-rating", METHOD.POST, params)
}
// 添加质量评级数据
export async function addQualityRating(params) {
    return request("/api/app/e-mSTMkt-supplier-rating/set-quality-rating", METHOD.POST, params)
}
// 更新评级数据
export async function updateDeliveryData(params) {
    return request("/api/app/e-mSTMkt-supplier-rating/update", METHOD.POST, params)
}
// 删除评级数据
export async function deleteDeliveryData(id) {
    return request(`/api/app/e-mSTMkt-supplier-rating/${id}/by-id`, METHOD.DELETE)
}
// 组织架构 新增部门节点
export async function AddDepart(params) {
    return request(`/api/app/e-mSSupplier-module-no/depart`, METHOD.POST,params)
}
// 组织架构 新增小组节点
export async function AddGroup(params) {
    return request(`/api/app/e-mSSupplier-module-no/user-group`, METHOD.POST,params)
}
// 组织架构 新增岗位节点
export async function AddPost(params) {
    return request(`/api/app/e-mSSupplier-module-no/user-post`, METHOD.POST,params)
}
// 组织架构 编辑部门节点
export async function updateDepart(params) {
    return request(`/api/app/e-mSSupplier-module-no/update-depart`, METHOD.POST,params)
}
// 组织架构 编辑小组节点
export async function updateGroup(params) {
    return request(`/api/app/e-mSSupplier-module-no/update-user-group`, METHOD.POST,params)
}
// 组织架构 编辑岗位节点
export async function updatePost(params) {
    return request(`/api/app/e-mSSupplier-module-no/update-user-post`, METHOD.POST,params)
}
// 配置页-Table页信息获取
export async function getsupParinfo(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/getsup-parinfo`, METHOD.GET,)
}
// 配置页保存信息
export async function setSupplierConfig(params) {
    return request(`/api/app/e-mSSupplier-module-no/set-supplier-config`, METHOD.POST,params)
}
// 获取系数配置页
export async function ruleItemsList(FactoryId) {
    return request(`/api/app/e-mSSupplier-module-no/rule-items-list/${FactoryId}`, METHOD.GET)
}
// 保存系数配置页
export async function setRuleItemsList(params) {
    return request(`/api/app/e-mSSupplier-module-no/set-rule-items-list`, METHOD.POST,params)
}

export default {
    getColumn,
    getData,
    getGroupList,
    getDataInfo,
    getSup,
    editData,
    addSupplier,
    deleteCust,
    getCust,
    addCust,
    deleteEquip,
    getEquip,
    addEquip,
    deleteQualify,
    getQualify,
    addQualify,
    updateQualify,
    addFactory,
    getFactory,
    deleteFactory,
    addAssest,
    deleteAssest,
    updateAssest,
    getAssest,
    getBoard,
    updateBoard,
    deleteBoard,
    addBoard,
    getlog,
    addlog,
    deletelog,
    updatelog,
    getLink,
    addLink,
    deleteLink,
    getPhoto,
    uploadFile,
    uploadImg,
    modulenouploadfile,
    getSelectFact,
    getSelectFactPage,
    deleteFactPage,
    addSelectFact,
    commite,
    reject,
    agree,
    followPeople,
    getfollow,
    check,
    submitLog,
    roundLog,
    applicationChange,
    ignoreChange,
    passChange,
    finishChange,
    authorityChange,
    caozuoLog,
    progressNum,
    exportD,
    buillData,
    buillRepair,
    billList,
    billAdd,
    setBill,
    deleBill,
    billAmand,
    getcapacityConfig,
    getProcessCapability,
    editProcessCapability,
    getDeliveryData,
    addDeliveryData,
    updateDeliveryData,
    deleteDeliveryData,
    qualityRatingList,
    addQualityRating,
    qualityRatingList2,
}
