<template>
  <div>
    <a-row>
      <a-col :span="12">
        <a-form-item label="机台名称" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
          <a-input v-model="machinedata.name_" allowClear />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="台面数" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
          <a-input v-model="machinedata.tableNo_" allowClear ref="tableNo_" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="机台状态" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
          <a-select v-model="machinedata.status_" allowClear style="width: 100%">
            <a-select-option :value="0">正常</a-select-option>
            <a-select-option :value="1">故障</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="轴个数" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
          <a-input v-model="machinedata.num_" allowClear ref="num_" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="允许取单" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
          <a-checkbox v-model="machinedata.isGetOrder" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="取单方式" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
          <a-select v-model="machinedata.takeOrderType" allowClear style="width: 100%">
            <a-select-option :value="0">自动取单</a-select-option>
            <a-select-option :value="1">扫码取单</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="扫码取单" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
          <a-select v-model="machinedata.orderNoType" allowClear style="width: 100%">
            <a-select-option :value="0">订单号</a-select-option>
            <a-select-option :value="1">管制卡号</a-select-option>
            <a-select-option :value="2">二维码</a-select-option>
            <a-select-option :value="3">其他</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="允许订单数上限" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
          <a-input v-model="machinedata.accCount_" allowClear ref="accCount_" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
          <template v-slot:label> <span>排</span><span style="padding-left: 28px">序</span> </template>
          <a-input v-model="machinedata.display_" allowClear ref="display_" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="完成后自动取单" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
          <a-select v-model="machinedata.isDoneGetOrder" allowClear style="width: 100%">
            <a-select-option :value="0">不取单</a-select-option>
            <a-select-option :value="1">取单</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-form-item label="物理地址" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }">
          <a-input v-model="machinedata.maC_" allowClear />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="下载不清空文件" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
          <a-select v-model="machinedata.keepFolder" allowClear style="width: 100%">
            <a-select-option :value="false">否</a-select-option>
            <a-select-option :value="true">是</a-select-option>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>
  </div>
</template>
<script>
export default {
  props: ["machinedata"],
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="less" scoped></style>
