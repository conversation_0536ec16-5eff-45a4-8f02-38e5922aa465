<template>
  <a-form-model
        :modal="form"
    >
      <a-form-model-item label="订单数量：" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}">
        <a-input v-model="form.para4DelQty_"  :autoFocus='autoFocus' />
      </a-form-model-item>
    </a-form-model>
</template>

<script>
export default {
    name:'addInquiry',
    
  data() {
    return {      
      autoFocus:true,
      form:{
        para4DelQty_:'',  // 订单编号        
      }
    };
  },
  methods: {  
  //  keyupEnter1(){
  //     this.$emit('keyupEnter1')
  // }
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
};
</script>