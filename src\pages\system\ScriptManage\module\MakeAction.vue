<!-- 系统管理-脚本管理-按钮 -->
<template>
  <div class="active">
    <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.GetEngineeringProductionOrder')">
      <a-button type="primary" @click="deleteBtn"> 删除 </a-button>
    </div>

    <div class="box" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.MakeStart')">
      <a-button type="primary" @click="editAdd"> 编辑 </a-button>
    </div>

    <div class="box">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "MakeAction",
  props: {
    assignLoading: {
      type: <PERSON>olean,
    },
    assignBackLoading: {
      type: Boolean,
    },
  },
  data() {
    return {};
  },
  methods: {
    checkPermission,
    // 新增物料
    deleteBtn() {
      this.$emit("deleteBtn");
    },
    // 编辑
    editAdd() {
      this.$emit("editAdd");
    },
    //查询
    queryClick() {
      this.$emit("queryClick");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  float: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    margin-top: 9px;
    padding: 0 6px;
    text-align: center;
    .ant-btn {
      width: 75px;
    }
    .ant-btn-primary:hover {
      background-color: #ffb029 !important;
      border-color: #ffb029 !important;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        // background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
