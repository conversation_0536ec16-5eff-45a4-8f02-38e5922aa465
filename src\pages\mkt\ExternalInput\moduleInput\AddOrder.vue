<template>
  <div class="contentInfo" style="position: relative">
    <a-button type="primary" style="position: absolute; z-index: 99; top: 1%; left: 4%" @click="addClick">提交</a-button>
    <a-card title="参数信息" :bordered="false">
      <a-form-model layout="inline">
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="板材类别" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.pcbProType">
                  <a-select-option v-for="item in mapKey(selectOption.PcbProType)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="样/批" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper" style="flex-wrap: wrap">
                <a-select v-model="formData.orderType">
                  <a-select-option v-for="item in mapKey(selectOption.OrderType)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="客户料号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input />
                <button>文件上传</button>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="订单编号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="层数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper" style="flex-wrap: wrap">
                <a-select v-model="formData.boardLayers">
                  <a-select-option v-for="item in mapKey(selectOption.BoardLayers)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="订单面积" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper" style="flex-wrap: wrap"><a-input v-model="formData.boardArea" /> m2</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="出货方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper" style="flex-wrap: wrap">
                <a-select v-model="formData.boardType">
                  <a-select-option v-for="item in mapKey(selectOption.BoardType)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="分割方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper" style="flex-wrap: wrap">
                <a-select v-model="formData.vCut">
                  <a-select-option v-for="item in mapKey(selectOption.VCut)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="是否接受打叉板" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div class="editWrapper">
                <a-checkbox v-model="formData.acceptCrossed" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="是否阻抗" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div class="editWrapper">
                <a-checkbox v-model="formData.isImpedance" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="成品板厚" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.boardThickness">
                  <a-select-option v-for="item in mapKey(selectOption.BoardThickness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="工艺边" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.processEdges">
                  <a-select-option v-for="item in mapKey(selectOption.ProcessEdges)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="2">
            <a-form-model-item label="半孔" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div class="editWrapper">
                <a-checkbox v-model="formData.isHalfHole" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="2">
            <a-form-model-item label="盲埋孔" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div class="editWrapper">
                <a-checkbox v-model="formData.isBlindVias" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="是否加急" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <div class="editWrapper">
                <a-checkbox v-model="formData.isJiaji" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="板材品牌" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.fR4Type" style="width: 200px">
                  <a-select-option v-for="item in mapKey(selectOption.FR4Type)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="TG" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.fR4Tg">
                  <a-select-option v-for="item in mapKey(selectOption.FR4Tg)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="验收标准" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.ipcLevel">
                  <a-select-option v-for="item in mapKey(selectOption.ipcLevel)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="交货日期" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input v-model="formData.deliveryDate"> </a-input>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="外层铜厚" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.copperThickness">
                  <a-select-option v-for="item in mapKey(selectOption.CopperThickness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="内层铜厚" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.innerCopperThickness">
                  <a-select-option v-for="item in mapKey(selectOption.InnerCopperThickness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="测试方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.flyingProbe">
                  <a-select-option v-for="item in mapKey(selectOption.FlyingProbe)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="导热系数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.invoice">
                  <a-select-option v-for="item in mapKey(selectOption.Invoice)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="线宽线距" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.lineWeight">
                  <a-select-option v-for="item in mapKey(selectOption.LineWeight)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="拼版款数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input v-model="formData.pinBanNum" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="交货数量" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input v-model="formData.num" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="拼版方式" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input v-model="formData.pinBanType" />
                X
                <a-input v-model="formData.pinBanType" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="最小孔径" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.vias">
                  <a-select-option v-for="item in mapKey(selectOption.Vias)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="孔铜25um" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-checkbox v-model="formData.holeThickness_"></a-checkbox>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="4">
            <a-form-model-item label="成长" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input v-model="formData.boardHeight" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="成宽" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-input v-model="formData.boardWidth" />
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="表面处理" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.surfaceFinish">
                  <a-select-option v-for="item in mapKey(selectOption.SurfaceFinish)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="厚度" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.imGoldThinckness">
                  <a-select-option v-for="item in mapKey(selectOption.ImGoldThinckness)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="过孔处理" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.solderCover">
                  <a-select-option v-for="item in mapKey(selectOption.SolderCover)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="阻焊(顶)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.solderColor">
                  <a-select-option v-for="item in mapKey(selectOption.SolderColor)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="阻焊(底)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.solderColorBottom">
                  <a-select-option v-for="item in mapKey(selectOption.SolderColorBottom)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="字符(顶)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.fontColor">
                  <a-select-option v-for="item in mapKey(selectOption.FontColor)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="字符(底)" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.fontColorBottom">
                  <a-select-option v-for="item in mapKey(selectOption.FontColorBottom)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="协同工厂" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <div class="editWrapper">
                <a-select v-model="formData.joinFactoryId">
                  <a-select-option v-for="item in mapKey(selectOption.JoinFactoryId)" :key="item.value" :value="item.value">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="业务员备注" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <div class="editWrapper" style="height: 96px">
                <a-input v-model="formData.cnNote" style="width: 80%" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="审单备注" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <div class="editWrapper" style="height: 96px">
                <a-input v-model="formData.checkNote" style="width: 80%" />
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>

    <a-modal title="拼版图" :visible="makeupVisible" :footer="null" @cancel="handleCancel">
      <makeup-pic ref="makeup"></makeup-pic>
    </a-modal>
  </div>
</template>

<script>
import { getEditOrderInfo, getSelectOption1 } from "@/services/mkt/orderInfo";
import { OrderWF } from "@/services/mkt/externalinput";
import MakeupPic from "@/pages/mkt/moduleInput/MakeupPic";
import $ from "jquery";
export default {
  name: "AddOrder",
  components: { MakeupPic },
  data() {
    return {
      selectOption: [],
      formData: {
        pcbProType: "", // 板材类别
        orderType: "", // 样1/批2
        pcbFileName: "", // 文件名
        pcbFilePath: "", // 文件路径
        orderNo: "", // 生产编号
        boardLayers: "", // 层数
        boardArea: "", // 面积
        boardType: "", // 出货类型
        vCut: "", // v割方式
        acceptCrossed: false, // 是否接受打叉板
        isImpedance: false, // 是否接受阻抗
        boardThickness: "", // 板厚
        processEdges: "", // 工艺边
        isHalfHole: false, // 半孔
        deliveryType: "", // 交期
        isBlindVias: false, // 盲埋孔
        isJiaji: false, // 是否加急
        fR4Type: "", // 板材类型
        ipcLevel: "", // 验收标准
        deliveryDate: "", // 交货日期
        copperThickness: "", // 铜厚
        innerCopperThickness: "", // 内层铜厚
        flyingProbe: "", // 测试方法
        invoice: "", // 导热系数
        lineWeight: "", // 线宽线距
        pinBanNum: "", // 拼版款数
        num: "", // 交货数量
        pinBanType: "", // 拼版方式
        vias: "", // 最小孔径
        boardHeight: "", // 成长
        boardWidth: "", // 成宽
        surfaceFinish: "", // 表面处理
        imGoldThinckness: "", // 沉金厚度
        solderCover: "", // 过孔处理
        solderColor: "", // 阻焊（顶）
        solderColorBottom: "", // 阻焊（底）
        fontColor: "", // 字符(顶)
        fontColorBottom: "", // 字符（底）
        joinFactoryId: "", // 协同工厂
        holeThickness_: false, // 孔铜25
        cnNote: "", // 业务备注
        checkNote: "", // 审单备注
      },
      dataVisible: false,
      dataVisible1: false,
      ProcessEdgeWidth: 0,
      makeupVisible: false, // 拼版图弹窗开关
      ReportList: [], // 出货报告列表
    };
  },
  computed: {
    ozValue() {
      let arr_ = [];
      for (var i = 0; i < this.formData.boardLayers; i++) {
        if (i == 0 || i == this.formData.boardLayers - 1) {
          arr_.push(this.formData.copperThickness);
        } else {
          arr_.push(this.formData.innerCopperThickness);
        }
      }
      return arr_.join("/");
    },
  },
  filters: {
    invoiceFilter(val) {
      let val_ = "";
      switch (val) {
        case 0:
          val_ = "默认";
          break;
        case 1:
          val_ = "1.0w";
          break;
        case 2:
          val_ = "1.5w";
          break;
        case 3:
          val_ = "2.0w";
          break;
        case 4:
          val_ = "0.5w";
          break;
        case 5:
          val_ = "0.7w";
          break;
        case 6:
          val_ = "5w";
          break;
        case 8:
          val_ = "8w";
          break;
        case 9:
          val_ = "3w";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
    camFilter(val) {
      let val_ = "";
      switch (val) {
        case 1:
          val_ = "中级";
          break;
        case 2:
          val_ = "高级";
          break;
        case 3:
          val_ = "资深";
          break;
        default:
          val_ = "";
      }
      return val_;
    },
  },
  created() {
    this.getData();
  },

  methods: {
    getData() {
      getSelectOption1().then(res => {
        if (res.code) {
          var arr = res.data;
          var obj = {};
          arr.forEach(item => {
            var aaa = {};
            for (let i = 0; i < item.keyValue.length; i++) {
              aaa[item.keyValue[i].name] = item.keyValue[i].value;
              obj[item.keyName] = aaa;
            }
          });
          this.selectOption = obj;
          // console.log('this.selectOption',this.selectOption)
        }
      });
    },

    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
    // 提交
    addClick() {
      let params = this.formData;
      params.orderType = Number(params.orderType);
      params.boardLayers = Number(params.boardLayers);
      params.boardArea = Number(params.boardArea);
      params.boardThickness = Number(params.boardThickness);
      params.ipcLevel = Number(params.ipcLevel);
      params.copperThickness = Number(params.copperThickness);
      params.innerCopperThickness = Number(params.innerCopperThickness);
      params.invoice = Number(params.invoice);
      params.pinBanNum = Number(params.pinBanNum);
      params.num = Number(params.num);
      params.vias = Number(params.vias);
      params.boardHeight = Number(params.boardHeight);
      params.boardWidth = Number(params.boardWidth);
      params.imGoldThinckness = Number(params.imGoldThinckness);
      params.joinFactoryId = Number(params.joinFactoryId);
      console.log(params, this.formData);
      OrderWF(params).then(res => {
        if (res.code) {
          this.$message.success("提交成功");
        } else {
          this.$message.error(res.message);
        }
      });
      if (this.formData.orderType) {
        this.formData.orderType = params.orderType.toString();
      } else {
        this.formData.orderType = "";
      }
      if (this.formData.boardLayers) {
        this.formData.boardLayers = params.boardLayers.toString();
      } else {
        this.formData.boardLayers = "";
      }
      if (this.formData.boardArea) {
        this.formData.boardArea = params.boardArea.toString();
      } else {
        this.formData.boardArea = "";
      }
      if (this.formData.boardThickness) {
        this.formData.boardThickness = params.boardThickness.toString();
      } else {
        this.formData.boardThickness = "";
      }
      if (this.formData.ipcLevel) {
        this.formData.ipcLevel = params.ipcLevel.toString();
      } else {
        this.formData.ipcLevel = "";
      }
      if (this.formData.copperThickness) {
        this.formData.copperThickness = params.copperThickness.toString();
      } else {
        this.formData.copperThickness = "";
      }
      if (this.formData.innerCopperThickness) {
        this.formData.innerCopperThickness = params.innerCopperThickness.toString();
      } else {
        this.formData.innerCopperThickness = "";
      }
      if (this.formData.invoice) {
        this.formData.invoice = params.invoice.toString();
      } else {
        this.formData.invoice = "";
      }
      if (this.formData.pinBanNum) {
        this.formData.pinBanNum = params.pinBanNum.toString();
      } else {
        this.formData.pinBanNum = "";
      }
      if (this.formData.num) {
        this.formData.num = params.num.toString();
      } else {
        this.formData.num = "";
      }
      if (this.formData.vias) {
        this.formData.vias = params.vias.toString();
      } else {
        this.formData.vias = "";
      }
      if (this.formData.boardHeight) {
        this.formData.boardHeight = params.boardHeight.toString();
      } else {
        this.formData.boardHeight = "";
      }
      if (this.formData.boardWidth) {
        this.formData.boardWidth = params.boardWidth.toString();
      } else {
        this.formData.boardWidth = "";
      }
      if (this.formData.imGoldThinckness) {
        this.formData.imGoldThinckness = params.imGoldThinckness.toString();
      } else {
        this.formData.imGoldThinckness = "";
      }
      if (this.formData.joinFactoryId) {
        this.formData.joinFactoryId = params.joinFactoryId.toString();
      } else {
        this.formData.joinFactoryId = "";
      }

      console.log("提交数据", this.formData);
    },
  },
};
</script>

<style scoped lang="less">
.contentInfo {
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 32px;
          .ant-select {
            width: 120px;
          }
          .ant-input {
            width: 120px;
          }
          .ant-input-number {
            width: 120px;
          }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          label {
            font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          }
        }
        .ant-form-item-control-wrapper {
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 8px 10px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}
</style>
