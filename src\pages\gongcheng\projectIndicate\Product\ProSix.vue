<template>
  <div ref="SelectBox">
    <a-form-model layout="inline" style="margin-top: 10px; border-top: 1px solid #ddd; width: 582px" id="formDataElemSix">
      <a-row>
        <a-col :span="3" class="colSTY">
          <a-form-model-item
            label="盘中孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.isDiscHole" ref="firstInput" v-focus-next-on-tab="'2'" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="半孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="
              (showCG && show2) ||
              (showCG && showMore) ||
              (showHDI && showMore) ||
              (showMM && showMore) ||
              (this.$route.query.factory == '12' && this.proOrderInfoDto.boardLayers == 1)
            "
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.isHalfHole" ref="2" v-focus-next-on-tab="'3'"> </a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="异形孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.isProfileHole" ref="3" v-focus-next-on-tab="'4'"> </a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="板边包金"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.isPlateEdge" ref="4" v-focus-next-on-tab="'5'"> </a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="沉孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.stepHole" ref="5" v-focus-next-on-tab="'6'"> </a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item label="印序列号" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.isSerialNumber" ref="6" v-focus-next-on-tab="'7'" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="印序列号颜色"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.serialColour && iseval(requiredLinkConfigpro.serialColour.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-select
                v-model="proOrderInfoDto.serialColour"
                showSearch
                allowClear
                optionFilterProp="lable"
                :getPopupContainer="() => this.$refs.SelectBox"
                ref="7"
                v-focus-next-on-tab="'8'"
              >
                <a-select-option
                  v-for="item in mapKey(selectData.SerialColour)"
                  :key="item.value"
                  :value="item.value"
                  :lable="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="印序列号面次"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.serialFace && iseval(requiredLinkConfigpro.serialFace.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-select
                v-model="proOrderInfoDto.serialFace"
                showSearch
                allowClear
                optionFilterProp="lable"
                :getPopupContainer="() => this.$refs.SelectBox"
                ref="8"
                v-focus-next-on-tab="'9'"
              >
                <a-select-option
                  v-for="item in mapKey(selectData.SerialFace)"
                  :key="item.value"
                  :value="item.value"
                  :lable="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="蓝胶"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.isBlueGum" ref="9" v-focus-next-on-tab="'10'" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="蓝胶方式"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.blueWay && iseval(requiredLinkConfigpro.blueWay.isNullRules) ? 'require' : ''"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-input v-model="proOrderInfoDto.blueWay" allowClear ref="10" v-focus-next-on-tab="'11'"> </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="蓝胶型号"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.blueModel && iseval(requiredLinkConfigpro.blueModel.isNullRules) ? 'require' : ''"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-input v-model="proOrderInfoDto.blueModel" allowClear ref="11" v-focus-next-on-tab="'12'"> </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="蓝胶厚度最小"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.blueMin && iseval(requiredLinkConfigpro.blueMin.isNullRules) ? 'require' : ''"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-input v-model="proOrderInfoDto.blueMin" allowClear ref="12" v-focus-next-on-tab="'13'"> </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="蓝胶厚度最大"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.blueMax && iseval(requiredLinkConfigpro.blueMax.isNullRules) ? 'require' : ''"
            v-if="(showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-input v-model="proOrderInfoDto.blueMax" allowClear ref="13" v-focus-next-on-tab="'14'"> </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="埋铜"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.buriedCopper ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.buriedCopper" v-focus-next-on-tab="'15'" ref="14" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="埋阻"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.buriedResistance ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.buriedResistance" v-focus-next-on-tab="'16'" ref="15" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="铜浆塞孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.cuPlugHole ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.cuPlugHole" v-focus-next-on-tab="'17'" ref="16"> </a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="阻抗"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore) || (showCG && show1)"
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.isImpedance" v-focus-next-on-tab="'18'" ref="17" @change="impchange"> </a-checkbox>
            </div>
          </a-form-model-item>
        </a-col>
        <a-col :span="3" class="colSTY">
          <a-form-model-item
            label="压接孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.isCrimpHole ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.isCrimpHole" v-focus-next-on-tab="'19'" ref="18" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="背钻孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.isBackDrilling ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.isBackDrilling" v-focus-next-on-tab="'20'" ref="19" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="通孔控深"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.isThroughHoleControl ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.isThroughHoleControl" v-focus-next-on-tab="'21'" ref="20" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="盲孔控深"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showHDI && showMore) || (showMM && showMore)"
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.isBlindHoleControl ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.isBlindHoleControl" v-focus-next-on-tab="'22'" ref="21" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="阶梯孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <span v-if="!editFlg1">{{ proOrderInfoDto.steppedHole ? "是" : "" }}</span>
            <div v-else>
              <a-checkbox v-model="proOrderInfoDto.steppedHole" v-focus-next-on-tab="'23'" ref="22"> </a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="斜边"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.goldfingerBevel && iseval(requiredLinkConfigpro.goldfingerBevel.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-select
                v-model="proOrderInfoDto.goldfingerBevel"
                showSearch
                allowClear
                optionFilterProp="label"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'24'"
                ref="23"
                @change="changxb"
              >
                <a-select-option
                  v-for="item in mapKey(selectData.GoldfingerBevel)"
                  :key="item.value"
                  :value="item.value"
                  :label="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="盲槽"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="
              (showCG && show0) || (showCG && show1) || (showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)
            "
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.isBlindSlot" v-focus-next-on-tab="'25'" ref="24" />
            </div>
          </a-form-model-item>

          <a-form-model-item
            label="银浆塞孔"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            v-if="(showCG && show2) || (showCG && showMore) || (showHDI && showMore) || (showMM && showMore)"
          >
            <div>
              <a-checkbox v-model="proOrderInfoDto.silverPlugHole" v-focus-next-on-tab="'26'" ref="25" />
            </div>
          </a-form-model-item>
          <a-form-model-item label="跳V" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <div>
              <a-checkbox
                v-model="proOrderInfoDto.jumpCut"
                v-if="['machinemold+vcut', 'vcut', 'vcut+rtr', 'vcut+rtp'].includes(proOrderInfoDto.formingType)"
                v-focus-next-on-tab="'27'"
                ref="26"
              ></a-checkbox>
              <a-checkbox v-model="proOrderInfoDto.jumpCut" v-else disabled></a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item label="局部镀厚金" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.isPartPlatingThickGold" v-focus-next-on-tab="'28'" ref="27"></a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="特殊工艺"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.specialType && iseval(requiredLinkConfigpro.specialType.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-select
                v-model="proOrderInfoDto.specialType"
                showSearch
                allowClear
                optionFilterProp="label"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'29'"
                ref="28"
              >
                <a-select-option
                  v-for="item in mapKey(selectData.SpecialType)"
                  :key="item.value"
                  :value="item.value"
                  :label="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="PIM值要求"
            :label-col="{ span: 12 }"
            :wrapper-col="{ span: 12 }"
            :class="requiredLinkConfigpro.pimAsk && iseval(requiredLinkConfigpro.pimAsk.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input v-model="proOrderInfoDto.pimAsk" allowClear ref="29" v-focus-next-on-tab="'30'"> </a-input>
            </div>
          </a-form-model-item>

          <a-form-model-item label="微带线露铜" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.copperWire" v-focus-next-on-tab="'31'" ref="30"></a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item label="板边露铜" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.expCopper" v-focus-next-on-tab="'32'" ref="31"></a-checkbox>
            </div>
          </a-form-model-item>
        </a-col>

        <a-col :span="3" class="colSTY">
          <a-form-model-item label="金属铣槽" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.isMetalSlot" v-focus-next-on-tab="'33'" ref="32" @change="MetalSlotChange"></a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="金手指"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.goldenfinger && iseval(requiredLinkConfigpro.goldenfinger.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-select
                v-model="proOrderInfoDto.goldenfinger"
                showSearch
                allowClear
                optionFilterProp="label"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'34'"
                ref="33"
                @change="goldenfingerChange"
              >
                <a-select-option
                  v-for="item in mapKey(selectData.Goldenfinger)"
                  :key="item.value"
                  :value="item.value"
                  :label="item.lable"
                  :title="item.lable"
                >
                  {{ item.lable }}
                </a-select-option>
              </a-select>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label='金手指金厚U"'
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.goldFingerThickness && iseval(requiredLinkConfigpro.goldFingerThickness.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input
                v-model="proOrderInfoDto.goldFingerThickness"
                showSearch
                allowClear
                optionFilterProp="label"
                :disabled="proOrderInfoDto.goldenfinger ? false : true"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'35'"
                ref="34"
                @blur="truncationgft()"
              >
              </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            :label="'金手指镍厚' + proOrderInfoDto.goldfingerNieThicknessUnit"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="
              requiredLinkConfigpro.goldfingerNickelThickness && iseval(requiredLinkConfigpro.goldfingerNickelThickness.isNullRules) ? 'require' : ''
            "
          >
            <div>
              <a-input
                v-model="proOrderInfoDto.goldfingerNickelThickness"
                showSearch
                allowClear
                optionFilterProp="label"
                :disabled="proOrderInfoDto.goldenfinger ? false : true"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'36'"
                ref="35"
                @blur="truncationgnt()"
              >
              </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边角度"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.goldfingerBevelAngle && iseval(requiredLinkConfigpro.goldfingerBevelAngle.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input
                v-model="proOrderInfoDto.goldfingerBevelAngle"
                showSearch
                allowClear
                optionFilterProp="label"
                :disabled="proOrderInfoDto.goldfingerBevel ? false : true"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'37'"
                ref="36"
                @blur="truncationgfba()"
              >
              </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边深度"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.goldfingerBevelDepth && iseval(requiredLinkConfigpro.goldfingerBevelDepth.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input
                v-model="proOrderInfoDto.goldfingerBevelDepth"
                showSearch
                allowClear
                optionFilterProp="label"
                :disabled="proOrderInfoDto.goldfingerBevel ? false : true"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'38'"
                ref="37"
                @blur="truncationgfbd()"
              >
              </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="金手指斜边余厚"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.goldfingerBevelSurplus && iseval(requiredLinkConfigpro.goldfingerBevelSurplus.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input
                v-model="proOrderInfoDto.goldfingerBevelSurplus"
                showSearch
                allowClear
                optionFilterProp="label"
                :disabled="proOrderInfoDto.goldfingerBevel ? false : true"
                :getPopupContainer="() => this.$refs.SelectBox"
                v-focus-next-on-tab="'39'"
                ref="38"
                @blur="truncationgfbs()"
              >
              </a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item label="去独立PAD" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.deSinglePad" v-focus-next-on-tab="'40'" ref="39" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="最小孔铜"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.minHoleCopper && iseval(requiredLinkConfigpro.minHoleCopper.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input v-model="proOrderInfoDto.minHoleCopper" allowClear v-focus-next-on-tab="'41'" ref="40" @blur="truncationmhc()"></a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="平均孔铜"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.avgHoleCopper && iseval(requiredLinkConfigpro.avgHoleCopper.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input v-model="proOrderInfoDto.avgHoleCopper" allowClear v-focus-next-on-tab="'42'" ref="41" @blur="truncationgahc()"></a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item
            label="光板数"
            :label-col="{ span: 13 }"
            :wrapper-col="{ span: 11 }"
            :class="requiredLinkConfigpro.gbNum && iseval(requiredLinkConfigpro.gbNum.isNullRules) ? 'require' : ''"
          >
            <div>
              <a-input v-model="proOrderInfoDto.gbNum" allowClear v-focus-next-on-tab="'43'" ref="42"></a-input>
            </div>
          </a-form-model-item>
          <a-form-model-item label="对压" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.counterPressure" v-focus-next-on-tab="'44'" ref="43"></a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item label="水印" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.waterMark_" v-focus-next-on-tab="'45'" ref="44"></a-checkbox>
            </div>
          </a-form-model-item>
          <a-form-model-item label="外层泪滴" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.outerLineTeardrop" v-focus-next-on-tab="'46'" ref="45" />
            </div>
          </a-form-model-item>
          <a-form-model-item label="内层泪滴" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }">
            <div>
              <a-checkbox v-model="proOrderInfoDto.innerLineTeardrop" v-focus-next-on-tab="'47'" ref="46" />
            </div>
          </a-form-model-item> </a-col
      ></a-row>
    </a-form-model>
  </div>
</template>
<script>
import { productInformation, getvcutselect } from "@/services/projectIndicate";
import $ from "jquery";
let index = -1;
let focusInput = false;
export default {
  name: "ProductInfo",
  props: [
    "shouldjump",
    "proOrderInfoDto",
    "selectData",
    "factoryData",
    "editFlg1",
    "boardBrandList",
    "sheetTraderList",
    "messageList",
    "show0",
    "show1",
    "show2",
    "showMore",
    "showCG",
    "showHDI",
    "showMM",
    "requiredLinkConfigpro",
    "boardtgList",
    "ManufacturerTG",
  ],
  inject: ["reload"],
  data() {
    return {
      Vcutdata: [],
      ymdata: [],
      SolderResistInk: [],
      SolderResistInk1: [],
      CharacterResistInk: [],
      CharacterResistInk1: [],
      spinning: false,
      prohibit: false,
      prohibit1: false,
      copyOld: "",
      copyNewVal: "",
      selectData1: {},
      selectValue: "",
      index: -1,
      sheetTrader: [],
      boardBrand: [],
      selectedValue: "",
      editValue: "",
      editable: false,
      options: [
        { value: "option1", label: "Option 1" },
        { value: "option2", label: "Option 2" },
        { value: "option3", label: "Option 3" },
      ],
      // showData: {
      //   orderNo: "",
      //   businessOrderNo: "",
      //   joinFactoryId: 0,
      //   joinFactoryIdStr: "",
      //   createTime: "",
      //   deliveryType: "",
      //   deliveryDays: 0,
      //   deliveryDate: "",
      //   isReOrder: 0,
      //   isReOrderStr: "",
      //   status: 0,
      //   statusStr: 0,
      //   proAdminAcount: "",
      //   proAdminName: "",
      //   verifyAccount: "",
      //   verifyName: "",
      //   camCheckAdminAccount: "",
      //   camCheckName: "",
      //   boardLayers: 0,
      //   boardThickness: 0,
      //   boardThicknessTol: "",
      //   pinBanNum: 0,
      //   boardWidth: 0,
      //   boardHeight: 0,
      //   boardType: "",
      //   pinBanType: "",
      //   processEdges: "",
      //   grooveHeight: 0,
      //   grooveWidth: 0,
      //   boardArea: 0,
      //   customerMaterialNo: "",
      //   ipcLevel: 0,
      //   ipcLevelStr: "",
      //   flyingProbe: "",
      //   flyingProbeStr: "",
      //   isStamping: false,
      //   isLowResistanceTest: false,
      //   isInductanceTest: false,
      //   pcbFileName: "",
      //   pcbFilePath: "",
      //   camFileName: "",
      //   camFilePath: "",
      //   isHighVoltageTest: false,
      //   changeItemNum: false,
      //   isFeed: false,
      //   changePeriod: false,
      //   isOutFactory: false
      // },
    };
  },
  mounted() {
    console.log(this.proOrderInfoDto.isPartPlatingThickGold, "697697");
    this.sheetTrader = this.sheetTraderList;
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  directives: {
    focusNextOnTab: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode !== 13 || focusInput) return; // 非回车键直接返回

          // 清理无效的 refs 引用
          const refs = vnode.context.$refs;
          for (const key in refs) {
            if (!refs[key]) delete refs[key];
          }

          const refKeys = Object.keys(refs);
          const currentIndex = refKeys.indexOf(value); // 当前元素索引

          // 情况1：目标元素不存在于 refs 列表
          if (currentIndex === -1) {
            const nextIndex = typeof index !== "undefined" ? index + 1 : 0;
            if (nextIndex < refKeys.length) {
              const nextInput = refs[refKeys[nextIndex]];
              if (nextInput && !nextInput.disabled) {
                index = nextIndex;
                nextInput.focus();
              }
            }
          }
          // 情况2：目标元素存在且可用
          else if (!refs[value].disabled) {
            index = currentIndex;
            refs[value].focus();
          }
          // 情况3：向后查找最多12个可用元素
          else {
            for (let offset = 1; offset <= 12; offset++) {
              const targetIndex = currentIndex + offset;
              if (targetIndex >= refKeys.length) break;

              const targetKey = refKeys[targetIndex];
              const targetEl = refs[targetKey];

              if (targetEl && !targetEl.disabled) {
                targetEl.focus();
                break; // 找到第一个可用元素后停止
              }
            }
          }
        });
      },
    },
  },
  watch: {
    shouldjump: {
      handler(newVal) {
        if (newVal) {
          // 聚焦第一个输入框
          focusInput = true;
          this.$refs.firstInput.focus();
          index = -1; // 重置索引
          // 通知父组件重置状态
          this.$emit("resetjump");
        }
        setTimeout(() => {
          focusInput = false;
        }, 500);
      },
    },
    messageList: {
      handler(val) {
        this.get(val);
      },
    },
    sheetTraderList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.sheetTrader = newval;
        });
      },
    },
    boardBrandList: {
      deep: true,
      handler: function (newval, oldval) {
        this.$nextTick(() => {
          this.boardBrand = newval;
        });
      },
    },
    "proOrderInfoDto.boardThickness"(newVal, oldVal) {
      setTimeout(() => {
        if (newVal != oldVal && newVal && oldVal) {
          this.copyOld = oldVal;
          this.copyNewVal = newVal;
        }
      }, 700);
    },
  },
  methods: {
    impchange() {
      this.proOrderInfoDto.isChangeLayerPres = this.proOrderInfoDto.isImpedance;
    },
    changeType() {
      this.proOrderInfoDto.fR4Tg = null;
      let fR4Tg = [];
      if (this.boardtgList.length) {
        fR4Tg = this.proOrderInfoDto.boardBrand
          ? this.boardtgList.filter(item => {
              return item.valueMember1 == this.proOrderInfoDto.boardBrand;
            })
          : [];
      }
      if (!this.proOrderInfoDto.fR4Tg && fR4Tg.length) {
        this.proOrderInfoDto.fR4Tg = fR4Tg[0].valueMember;
      }
      if (this.ManufacturerTG.length) {
        let data = this.proOrderInfoDto.boardBrand
          ? this.ManufacturerTG.filter(item => {
              return item.coreType_ == this.proOrderInfoDto.boardBrand;
            })
          : [];
        if (data.length) {
          this.proOrderInfoDto.fR4Tg = data[0].tgValue;
          this.proOrderInfoDto.sheetTrader = data[0].verdorName_Value;
        }
      }
      if (this.boardBrandList.length && this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.proOrderInfoDto.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("val") != -1) {
        newIsNullRules = newIsNullRules.replace(/val/g, "this.proOrderInfoDto");
      }
      return eval(newIsNullRules);
    },
    handleResize() {
      var boxstyle = document.getElementsByClassName("box")[0];
      console.log("boxstyle.style.height", boxstyle, window.innerHeight);
      boxstyle.style.height = window.innerHeight - 140 + "px";
    },
    truncationgft() {
      if (this.proOrderInfoDto.goldFingerThickness) {
        var goldFingerThickness = this.proOrderInfoDto.goldFingerThickness.split("");
        if (goldFingerThickness.length > 10) {
          goldFingerThickness = goldFingerThickness.slice(0, 10);
          this.$message.warning("金手指金厚不能超过10个字符");
        }
        this.proOrderInfoDto.goldFingerThickness = goldFingerThickness.join("");
      }
    },
    truncationgnt() {
      if (this.proOrderInfoDto.goldfingerNickelThickness) {
        var goldfingerNickelThickness = this.proOrderInfoDto.goldfingerNickelThickness.split("");
        if (goldfingerNickelThickness.length > 10) {
          goldfingerNickelThickness = goldfingerNickelThickness.slice(0, 10);
          this.$message.warning("金手指镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerNickelThickness = goldfingerNickelThickness.join("");
      }
    },
    truncationgfba() {
      if (this.proOrderInfoDto.goldfingerBevelAngle) {
        var goldfingerBevelAngle = this.proOrderInfoDto.goldfingerBevelAngle.split("");
        if (goldfingerBevelAngle.length > 10) {
          goldfingerBevelAngle = goldfingerBevelAngle.slice(0, 10);
          this.$message.warning("金手指斜边角度不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerBevelAngle = goldfingerBevelAngle.join("");
      }
    },
    truncationgfbd() {
      if (this.proOrderInfoDto.goldfingerBevelDepth) {
        var goldfingerBevelDepth = this.proOrderInfoDto.goldfingerBevelDepth.split("");
        if (goldfingerBevelDepth.length > 10) {
          goldfingerBevelDepth = goldfingerBevelDepth.slice(0, 10);
          this.$message.warning("金手指斜边深度不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerBevelDepth = goldfingerBevelDepth.join("");
      }
    },
    truncationgfbs() {
      if (this.proOrderInfoDto.goldfingerBevelSurplus) {
        var goldfingerBevelSurplus = this.proOrderInfoDto.goldfingerBevelSurplus.split("");
        if (goldfingerBevelSurplus.length > 10) {
          goldfingerBevelSurplus = goldfingerBevelSurplus.slice(0, 10);
          this.$message.warning("金手指斜边余厚不能超过10个字符");
        }
        this.proOrderInfoDto.goldfingerBevelSurplus = goldfingerBevelSurplus.join("");
      }
    },
    truncationmhc() {
      if (this.proOrderInfoDto.minHoleCopper) {
        var minHoleCopper = this.proOrderInfoDto.minHoleCopper.split("");
        if (minHoleCopper.length > 4) {
          minHoleCopper = minHoleCopper.slice(0, 4);
          this.$message.warning("最小孔铜不能超过4个字符");
        }
        this.proOrderInfoDto.minHoleCopper = minHoleCopper.join("");
      }
    },
    truncationgahc() {
      if (this.proOrderInfoDto.avgHoleCopper) {
        var avgHoleCopper = this.proOrderInfoDto.avgHoleCopper.split("");
        if (avgHoleCopper.length > 4) {
          avgHoleCopper = avgHoleCopper.slice(0, 4);
          this.$message.warning("平均孔铜不能超过4个字符");
        }
        this.proOrderInfoDto.avgHoleCopper = avgHoleCopper.join("");
      }
    },
    editClick1() {
      this.editFlg1 = !this.editFlg1;
    },
    changeSheet(val) {
      if (val) {
        this.proOrderInfoDto.sheetTrader = val;
      }
      if (this.boardBrandList.length && this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList.filter(item => {
          return item.valueMember1 == this.proOrderInfoDto.sheetTrader;
        });
      } else if (this.boardBrandList.length && !this.proOrderInfoDto.sheetTrader) {
        this.boardBrand = this.boardBrandList;
      }
      this.proOrderInfoDto.boardBrand = "";
    },
    // saveClick(){
    //   if(!this.editFlg1){
    //     this.$message.warning('非编辑状态不可保存')
    //     return
    //   }
    //   let params = this.proOrderInfoDto
    //   productInformation(params).then(res=>{
    //     if(res.code){
    //       this.$emit('GetProOrderInfo')
    //       this.$message.success('保存成功')
    //       this.editFlg1 = false
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    // 下载客户文件
    // dwon1(val){
    //   if(val){
    //     window.location.href = val
    //   }else{
    //     this.$message.error('客户文件不存在')
    //   }
    // },
    // 下载工程文件
    dwon2(val) {
      if (val) {
        window.location.href = val;
      } else {
        this.$message.error("工程文件不存在");
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    // 板厚更改 板厚公差=+/-板厚*0.1
    boardThickness() {
      this.$emit("boardThickness");
      if (this.proOrderInfoDto.boardThickness) {
        if (this.proOrderInfoDto.boardThickness <= 1.0) {
          this.proOrderInfoDto.boardThicknessTol = "+/-0.1";
        } else {
          this.proOrderInfoDto.boardThicknessTol = "+/-" + (this.proOrderInfoDto.boardThickness * 0.1).toFixed(2);
        }
      }
    },
    changxb() {
      if (!this.proOrderInfoDto.goldfingerBevel) {
        this.proOrderInfoDto.goldfingerBevelAngle = null;
        this.proOrderInfoDto.goldfingerBevelDepth = null;
        this.proOrderInfoDto.goldfingerBevelSurplus = null;
      }
    },
    goldenfingerChange() {
      if (!this.proOrderInfoDto.goldenfinger) {
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },
    MetalSlotChange() {
      if (this.$route.query.factory == 12) {
        if (this.proOrderInfoDto.isMetalSlot) {
          this.proOrderInfoDto.cncHoleTol = "+/-0.075";
        } else {
          this.proOrderInfoDto.cncHoleTol = "";
        }
      }
    },
    // 拼版方式更改
    pinBanType() {
      if (this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1) {
        if (this.proOrderInfoDto.boardType == "pcs") {
          this.$message.warning("拼版方式大于1x1,出货单位请选择set");
        }
      }
      // if(this.proOrderInfoDto.pinBanType1 > 1 || this.proOrderInfoDto.pinBanType2 > 1 ){
      //   this.proOrderInfoDto.boardType = 'set'
      // }else{
      //   this.proOrderInfoDto.boardType = 'pcs'
      // }
    },
    //
    flyChange() {
      if (this.proOrderInfoDto.flyingProbe == "FlyingProbe") {
        this.proOrderInfoDto.fpTestMethod = "capacitance";
        this.proOrderInfoDto.testFixtureNumber = "";
      }
      if (
        this.proOrderInfoDto.flyingProbe == "custstand" ||
        this.proOrderInfoDto.flyingProbe == "newstand" ||
        this.proOrderInfoDto.flyingProbe == "sharestand" ||
        this.proOrderInfoDto.flyingProbe == "teststand"
      ) {
        this.proOrderInfoDto.testFixtureNumber = this.proOrderInfoDto.orderNo;
        this.proOrderInfoDto.fpTestMethod = "";
      }
    },
    changeplugoil() {
      if (
        this.proOrderInfoDto.solderCover == "plugoil" ||
        this.proOrderInfoDto.solderCover == "bgaplugoil" ||
        this.proOrderInfoDto.solderCover == "aluminiumplugoil" ||
        this.proOrderInfoDto.solderCover == "openwindowplusplugoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+converoil" ||
        this.proOrderInfoDto.solderCover == "plugoil+openminwindow"
      ) {
        if (this.$route.query.factory == 38) {
          this.proOrderInfoDto.plugOilTool = "consentprinting";
        } else {
          this.proOrderInfoDto.plugOilTool = "aluminumsheet";
        }
        if (this.$route.query.factory != 58 && this.$route.query.factory != 59) {
          this.proOrderInfoDto.plugOilSide = "toplayer";
        }
      } else {
        this.proOrderInfoDto.plugOilTool = null;
        this.proOrderInfoDto.plugOilSide = null;
      }
    },
    changemark() {
      if (!this.proOrderInfoDto.markPosition) {
        this.proOrderInfoDto.ulType = [];
        this.proOrderInfoDto.markType = [];
        this.proOrderInfoDto.markFace = null;
        this.proOrderInfoDto.periodicFormat = null;
      }
      if (!this.proOrderInfoDto.markPosition || this.proOrderInfoDto.markPosition == null || this.proOrderInfoDto.markPosition == "") {
        this.prohibit = true;
      } else {
        this.prohibit = false;
      }
    },
    changegold() {
      if (!this.proOrderInfoDto.isGoldfinger) {
        this.prohibit1 = true;
      } else {
        this.prohibit1 = false;
      }
      if (!this.proOrderInfoDto.isGoldfinger) {
        this.proOrderInfoDto.goldenFingerAreaRe = null;
        this.proOrderInfoDto.goldFingerThickness = null;
        this.proOrderInfoDto.goldfingerNickelThickness = null;
      }
    },

    changesurface() {
      if (
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandhaslwithfree" ||
        this.proOrderInfoDto.surfaceFinish == "goldplatedfingerandimmersiongold" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithfreeandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "haslwithleadandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "nickelplatingandgoldplatedfinger" ||
        this.proOrderInfoDto.surfaceFinish == "ospandimmersiongoldfinger" ||
        this.proOrderInfoDto.surfaceFinish == "wholeimmersiongoldandimmersiongoldfinger"
      ) {
        this.proOrderInfoDto.isGoldfinger = true;
      } else {
        this.proOrderInfoDto.isGoldfinger = false;
      }
    },
    changelayers() {
      this.$emit("changelayers");
    },
    changesu() {
      if (this.proOrderInfoDto.pinBanType1 && this.proOrderInfoDto.pinBanType2) {
        this.proOrderInfoDto.su = (Number(this.proOrderInfoDto.pinBanType1) * this.proOrderInfoDto.pinBanType2).toFixed();
      } else {
        this.proOrderInfoDto.su = null;
      }
    },
    optionClick() {},
    getPrice(item, list, value) {
      let a = { Price: value };
      for (let i = 0; i < list.length; i++) {
        if (list[i].item == item) {
          let Price = list[i].Price == "" ? value : list[i].Price;
          a.Price = Price;
        }
      }
      return a;
    },

    setEstimateb(value, list) {
      this.proOrderInfoDto.deliveryMethod = value;
    },
    handleBlurb(value, list) {
      this.setEstimateb(value, list);
    },
    handleSearchb(value, list) {
      this.setEstimateb(value, list);
    },
    labelClick(e) {
      console.log("dianji", e);
      this.editable = !this.editable;
    },

    setEstimate1(value, list) {
      this.proOrderInfoDto.boardLayers = value;
      let a = this.getPrice(this.proOrderInfoDto.boardLayers, list, value);
      this.changelayers();
    },
    handleSearch1(value, list) {
      this.setEstimate1(value, list);
    },
    handleBlur1(value, list) {
      this.setEstimate1(value, list);
      if (this.proOrderInfoDto.boardLayers) {
        var boardLayers = this.proOrderInfoDto.boardLayers.split("");
        if (boardLayers.length > 2) {
          boardLayers = boardLayers.slice(0, 2);
          this.$message.warning("层数不能超过2个字符");
        }
        this.proOrderInfoDto.boardLayers = boardLayers.join("");
      }
    },
    setEstimate2(value, list) {
      this.proOrderInfoDto.innerCopperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.innerCopperThickness, list, value);
    },
    handleSearch2(value, list) {
      this.setEstimate2(value, list);
    },
    handleBlur2(value, list) {
      this.setEstimate2(value, list);
      if (this.proOrderInfoDto.innerCopperThickness) {
        var innerCopperThickness = this.proOrderInfoDto.innerCopperThickness.split("");
        if (innerCopperThickness.length > 3) {
          innerCopperThickness = innerCopperThickness.slice(0, 3);
          this.$message.warning("成品铜厚内层不能超过3个字符");
        }
        this.proOrderInfoDto.innerCopperThickness = innerCopperThickness.join("");
      }
    },
    setEstimate3(value, list) {
      this.proOrderInfoDto.copperThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.copperThickness, list, value);
    },
    handleSearch3(value, list) {
      this.setEstimate3(value, list);
    },
    handleBlur3(value, list) {
      this.setEstimate3(value, list);
      if (this.proOrderInfoDto.copperThickness) {
        var copperThickness = this.proOrderInfoDto.copperThickness.toString().split("");
        if (copperThickness.length > 3) {
          copperThickness = copperThickness.slice(0, 3);
          this.$message.warning("成品铜厚外层不能超过3个字符");
        }
        this.proOrderInfoDto.copperThickness = copperThickness.join("");
      }
    },
    setEstimate4(value, list) {
      this.proOrderInfoDto.warpage = value;
      let a = this.getPrice(this.proOrderInfoDto.warpage, list, value);
    },
    handleSearch4(value, list) {
      this.setEstimate4(value, list);
    },
    handleBlur4(value, list) {
      this.setEstimate4(value, list);
      if (this.proOrderInfoDto.warpage) {
        var warpage = this.proOrderInfoDto.warpage.split("");
        if (warpage.length > 6) {
          warpage = warpage.slice(0, 6);
          this.$message.warning("翘曲度不能超过6个字符");
        }
        this.proOrderInfoDto.warpage = warpage.join("");
      }
    },
    setEstimate5(value, list) {
      this.proOrderInfoDto.vcutSurplusThickness = value;
      let a = this.getPrice(this.proOrderInfoDto.vcutSurplusThickness, list, value);
    },
    handleSearch5(value, list) {
      this.setEstimate5(value, list);
    },
    handleBlur5(value, list) {
      this.setEstimate5(value, list);
    },
    setEstimate6(value, list) {
      this.proOrderInfoDto.minSolderBridge = value;
      let a = this.getPrice(this.proOrderInfoDto.minSolderBridge, list, value);
    },
    handleSearch6(value, list) {
      this.setEstimate6(value, list);
    },
    handleBlur6(value, list) {
      this.setEstimate6(value, list);
    },
    setEstimate7(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness, list, value);
    },
    handleSearch7(value, list) {
      this.setEstimate7(value, list);
    },
    handleBlur7(value, list) {
      this.setEstimate7(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness) {
        var cjNickelThinckness = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness.split("");
        if (cjNickelThinckness.length > 10) {
          cjNickelThinckness = cjNickelThinckness.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness = cjNickelThinckness.join("");
      }
    },
    setEstimate8(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = value;
      let a = this.getPrice(this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2, list, value);
    },
    handleSearch8(value, list) {
      this.setEstimate8(value, list);
    },
    handleBlur8(value, list) {
      this.setEstimate8(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2) {
        var cjNickelThinckness2 = this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2.split("");
        if (cjNickelThinckness2.length > 10) {
          cjNickelThinckness2 = cjNickelThinckness2.slice(0, 10);
          this.$message.warning("表面处理镍厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.cjNickelThinckness2 = cjNickelThinckness2.join("");
      }
    },
    setEstimate9(value, list) {
      this.proOrderInfoDto.boardThicknessTol = value;
      let a = this.getPrice(this.proOrderInfoDto.boardThicknessTol, list, value);
    },
    handleSearch9(value, list) {
      this.setEstimate9(value, list);
    },
    handleBlur9(value, list) {
      this.setEstimate9(value, list);
    },
    setEstimate10(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = value;
    },
    handleSearch10(value, list) {
      this.setEstimate10(value, list);
    },
    handleBlur10(value, list) {
      this.setEstimate10(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness) {
        var newTinThickness = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness.split("");
        if (newTinThickness.length > 10) {
          newTinThickness = newTinThickness.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness = newTinThickness.join("");
      }
    },
    setEstimate11(value, list) {
      this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = value;
    },
    handleSearch11(value, list) {
      this.setEstimate11(value, list);
    },
    handleBlur11(value, list) {
      this.setEstimate11(value, list);
      if (this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2) {
        var newTinThickness2 = this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2.split("");
        if (newTinThickness2.length > 10) {
          newTinThickness2 = newTinThickness2.slice(0, 10);
          this.$message.warning("表面处理锡厚不能超过10个字符");
        }
        this.proOrderInfoDto.surfaceFinishJsonDto.newTinThickness2 = newTinThickness2.join("");
      }
    },
    get(val) {
      $("#formDataElemSix .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
.box {
  /deep/.ant-select-dropdown--single {
    min-width: 90px;
  }
}
/deep/.ant-col-20 {
  width: 1291px;
}
/deep/.ant-col-4 {
  width: 258px;
}
/deep/.ant-col-3 {
  width: 194px;
}
/deep/.ant-col-8 {
  width: 516px;
}
/deep/.ant-col-9 {
  width: 582px;
}
.div1 {
  /deep/.ant-form-item-control {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.div2 {
  .div22 {
    /deep/.ant-form-item-control {
      padding: 0;
      min-height: 28px !important;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}

.autoo {
  /deep/.ant-form-item-control {
    height: 100% !important;
    width: 1184px;
  }
  /deep/.ant-form-item-control {
    // height: auto!important;
    background: #f5f5f5 !important;
    .ant-input {
      margin-top: 4px !important;
      margin-bottom: 0 !important;
    }
  }
}
/deep/textarea.ant-input {
  min-height: 24px;
  line-height: 1.3;
}
.speclass {
  /deep/ .ant-form-item-label {
    width: 107px;
  }
  /deep/ .ant-form-item-label > label {
    font-size: 13px !important;
  }
  /deep/ .ant-form-item-control-wrapper {
    width: 1178px;
  }
}
/deep/ .div1 {
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px !important;
    }
  }
  .ant-form-item-children {
    overflow: inherit !important;
    font-size: 13px;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}

/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  /* border-color: #ff9900; */
  border: 2px solid red;
}
/deep/.ant-select-focused .ant-select-selection {
  border: 2px solid red;
}
/deep/.ant-input:focus {
  border: 2px solid red;
}
/deep/.colSTY {
  border-left: 1px solid #ddd;
  .ant-form-item {
    .ant-form-item-label > label {
      font-size: 13px;
    }
  }
}
// /deep/.ant-col{
//   border-left:1px solid #ddd;
// }
/deep/.sss {
  height: 30px;
}
/deep/.ant-col-3 {
  .ant-col-14 {
    width: 58.5%;
  }
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-input {
  padding: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 11px !important;
  margin-left: 6px !important;
}

/deep/.searchPosElem {
  background-color: aquamarine;
  font: 13px / 1.14 arial;
}
/deep/.searchPosElem1 {
  background-color: #f1f1f1;
  font: 13px / 1.14 arial;
  font-weight: 500;
}
// /deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
//     height: 16px!important;
//     margin-top: 3px;
//     line-height: 14px!important;
//   }
/deep/.surSTY {
  // height:56px;
  .ant-form-item-control-wrapper {
    // min-height:20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
      }
    }
  }
}
/deep/.heightSty {
  height: 28px;
  .ant-select {
    margin-top: 2px;
  }
  .ant-select-selection--multiple {
    height: 20px;
    min-height: 20px;
  }
  .ant-select-allow-clear {
    .ant-select-selection--multiple {
      height: 23px;
    }
    .ant-select-selection__rendered {
      ul {
        display: flex;
        li {
          height: 16px;
          margin-top: 2px;
          line-height: 16px;
          user-select: none;
          padding-left: 0 !important;
        }
      }
    }
    .ant-select-selection__clear {
      top: 13px;
    }
  }
}
/deep/.heightSty1 {
  .ant-form-item-control-wrapper {
    min-height: 20px;
    .ant-form-item-control {
      height: 100% !important;
      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
        // .edit{
        //  line-height: 28px!important;
        // text-overflow: unset!important;
        // white-space: unset!important;
        // }
      }
      height: 100%;
      .ant-select {
        height: 100% !important;
        .ant-select-selection--multiple {
          min-height: 20px;
          padding-bottom: 0 !important;
          .ant-select-selection__rendered {
            width: 100%;
          }
          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }
          .ant-select-selection__rendered > ul > li {
            height: 17px !important;
            margin-top: 3px;
            line-height: 15px !important;
            width: 92%;
          }

          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}

/deep/.ant-select-dropdown-menu-item {
  font-size: 14px;
  padding: 0.5%;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-size: 14px !important;
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-size: 14px !important;
  font-weight: 500;

  color: #000000;
}

.ant-row {
  .ant-col-22 {
    .ant-form-item-control {
      .ant-input-affix-wrapper {
        line-height: 29px;
      }
    }
  }
  .ant-col-17 {
    .ant-form-item {
      /deep/.ant-input {
        min-height: 23px !important;
        height: 23px !important;
        line-height: 15px !important;
      }
    }
  }
  .ant-col-24 {
    /deep/.ant-form-item-label {
      width: 106px !important;
    }
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}
/deep/.disable {
  background: #f5f5f5 !important;
}
.box {
  overflow: auto;
  border-left: 1px solid rgb(233 230 230);
  position: relative;
  .bto {
    position: absolute;
    bottom: -48px;
    right: 374px;
  }
}

/deep/ .ant-form-item {
  margin: 0;
  width: 100%;
  display: flex;
  min-height: 28px;
  .tmp1 {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 370px;
    display: inline-block;
  }
  .tmp {
    word-break: keep-all;
    vertical-align: top;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 140px;
    display: inline-block;
  }
  .editWrapper {
    display: flex;
    align-items: center;
    min-height: 25px;
    .ant-select {
      width: 20;
    }
    .ant-input {
      width: 120px;
    }
    .ant-input-number {
      width: 120px;
    }
  }
  .ant-form-item-label {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    background-color: #f1f1f1;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    label {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    }
  }
  .ant-form-item-control-wrapper {
    font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
    .ant-form-item-control {
      .ant-form-item-children {
        // display: block;
        // min-height: 25px;
        line-height: 25px;
        // // vertical-align: top;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
        // width: 100%;
        // display: inline-block;

        // .ant-select-allow-clear{
        //   // .ant-select-selection--multiple{
        //   //   height: 23px;
        //   //   margin-top:2px;
        //   // }
        //   .ant-select-selection__rendered{
        //     ul{
        //       display: flex;
        //       li{
        //         margin-top:-1px;
        //       }
        //     }
        //     .ant-select-selection__choice{
        //       height: 18px;
        //       margin-top: 2px;
        //       line-height: 14px;
        //       user-select: none;
        //     }
        //   }
        //   .ant-select-selection__clear{
        //     top:11px;
        //   }
        // }
        .ant-checkbox-wrapper {
          min-height: 28px;
        }
        .ant-select-selection--single {
          height: 22px;
        }
        .ant-select-selection__rendered {
          line-height: 18px;
        }
        .ant-select {
          height: 22px;
        }
        .ant-input {
          height: 22px;
          padding-top: 2.6px;
        }
      }
      line-height: inherit;
      padding: 0px 5px;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      height: 28px;
    }
  }
}
.div2 {
  /deep/ .ant-form-item {
    margin: 0;
    width: 100%;
    display: flex;
    min-height: 28px;
    .tmp1 {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 370px;
      display: inline-block;
    }
    .tmp {
      word-break: keep-all;
      vertical-align: top;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 140px;
      display: inline-block;
    }
    .editWrapper {
      display: flex;
      align-items: center;
      min-height: 25px;
      .ant-select {
        width: 20;
      }
      .ant-input {
        width: 120px;
      }
      .ant-input-number {
        width: 120px;
      }
    }
    .ant-form-item-label {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      color: #666;
      background-color: #f1f1f1;
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      label {
        font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      }
    }
    .ant-form-item-control-wrapper {
      font: 14px/1.17 "微软雅黑", arial, \5b8b\4f53;
      .ant-form-item-control {
        .ant-form-item-children {
          // display: block;
          // min-height: 25px;
          line-height: 25px;
          // // vertical-align: top;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          // overflow: hidden;
          // width: 100%;
          // display: inline-block;

          // .ant-select-allow-clear{
          //   // .ant-select-selection--multiple{
          //   //   height: 23px;
          //   //   margin-top:2px;
          //   // }
          //   .ant-select-selection__rendered{
          //     ul{
          //       display: flex;
          //       li{
          //         margin-top:-1px;
          //       }
          //     }
          //     .ant-select-selection__choice{
          //       height: 18px;
          //       margin-top: 2px;
          //       line-height: 14px;
          //       user-select: none;
          //     }
          //   }
          //   .ant-select-selection__clear{
          //     top:11px;
          //   }
          // }
          .ant-checkbox-wrapper {
            min-height: 28px;
          }
          .ant-select-selection--single {
            height: 22px;
          }
          .ant-select-selection__rendered {
            line-height: 18px;
          }
          .ant-select {
            height: 22px;
          }
          .ant-input {
            height: 22px;
            padding-top: 2.6px;
          }
        }
        line-height: inherit;
        padding: 0px 5px;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        height: auto !important;
      }
    }
  }
}
</style>
