<!-- 车间管理-字符管理-按钮 -->
<template>
  <div class="active" ref="active" :style="[{ width: width + 'px' }]">
    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Vcut.VcutSendMachine')"
      :class="checkPermission('MES.ProductionModule.Vcut.VcutSendMachine') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="handleDispatchMachine" :loading="btnloading1"> 分派 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Vcut.VcutScanCode')"
      :class="checkPermission('MES.ProductionModule.Vcut.VcutScanCode') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Scancodetodispatch')"> 扫码分派 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Vcut.VcutEditMachine')"
      :class="checkPermission('MES.ProductionModule.Vcut.VcutEditMachine') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Editingmachine')"> 编辑机台 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Vcut.VcutIsUrgent')"
      :class="checkPermission('MES.ProductionModule.Vcut.VcutIsUrgent') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="SetUpExpeditingClick"> 设置加急 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Vcut.VcutSetRemarks')"
      :class="checkPermission('MES.ProductionModule.Vcut.VcutSetRemarks') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ExceptionRemarksClick"> 生产备注 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.ProductionModule.Vcut.VcutOrderFinish')"
      :class="checkPermission('MES.ProductionModule.Vcut.VcutOrderFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Productioncompleted')"> 生产完成 </a-button>
    </div>
    <div class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "CuttingAction",
  props: ["btnloading1"],
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },

  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 分派订单
    handleDispatchMachine() {
      this.$emit("handleDispatchMachine");
    },
    //  查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 设置加急
    SetUpExpeditingClick() {
      this.$emit("SetUpExpeditingClick");
    },
    // 生产备注
    ExceptionRemarksClick() {
      this.$emit("ExceptionRemarksClick");
    },
  },
};
</script>

<style scoped lang="less">
.active {
  float: right;
  width: 45%;
  height: 50px;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  /deep/ .box {
    width: 86px;
    margin: 6px 0;
    text-align: center;
    .ant-btn {
      width: 80px;
    }
    .ant-btn-loading {
      padding-left: 0 !important;
    }
  }
}
</style>
