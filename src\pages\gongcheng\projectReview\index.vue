<template>
  <a-spin :spinning="spinning">
    <div class="projectReview">
      <div
        class="mainContent"
        style="width: 100%; border: 1px solid rgb(233, 233, 240); border-right: 2px solid rgb(233, 233, 240); user-select: none; height: 100%"
        ref="tableWrapper"
      >
        <a-table
          :columns="reviewcolumns"
          :rowClassName="isRedRow"
          :pagination="pagination"
          @change="handleTableChange"
          :customRow="onClickRow"
          :scroll="{ y: 738, x: 1200 }"
          rowKey="proOrderId"
          :dataSource="Reviewsource"
          class="leftstyle"
        >
          <span slot="num" slot-scope="text, record, index">
            {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
          </span>
          <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
            <span>{{ record.orderNo }}</span
            >&nbsp;
            <span class="tagNum" style="display: inline-block; height: 19px">
              <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
                <span
                  class="noCopy"
                  style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
                  >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
                <span
                  class="noCopy"
                  style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
                  >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
              </a-tooltip>
              <a-tooltip title="加急" v-else-if="record.isJiaji">
                <span
                  class="noCopy"
                  style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
                  >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                </span>
              </a-tooltip>
              <a-tooltip title="新客户" v-if="record.isNewCust">
                <a-tag class="noCopy tagstyle"> 新 </a-tag>
              </a-tooltip>
              <a-tag v-if="record.ka" class="noCopy tagstyle"> KA </a-tag>
              <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.isReOrder == 1">
                <a-tag class="noCopy tagstyle"> 改 </a-tag>
              </a-tooltip>
              <a-tag v-if="record.pauseCancelState == 2" class="noCopy tagstyle"> 取消 </a-tag>
              <a-tag v-if="record.pauseCancelState == 3" class="noCopy tagstyle"> 暂停 </a-tag>
              <a-tag v-if="record.pauseCancelState == 4" class="noCopy tagstyle"> 暂停取消 </a-tag>
              <a-tag v-if="record.isJunG" class="noCopy tagstyle"> {{ record.joinFactoryId == 70 ? "J" : "军" }} </a-tag>
            </span>
          </div>
        </a-table>
        <right-copy ref="RightCopy" />
      </div>
      <div class="footerAction" style="user-select: none; margin-left: -2px">
        <div>
          <a-button
            type="primary"
            class="box"
            @click="startClick()"
            v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementReviewStart')"
          >
            开始
          </a-button>
          <a-button
            type="primary"
            class="box"
            @click="reviewClick()"
            v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementGetReviewUrl')"
          >
            评审
          </a-button>
          <a-button type="primary" class="box" @click="queryClick()"> 查询(F) </a-button>
        </div>
        <div v-if="buttonsmenu">
          <a-dropdown>
            <a-button type="primary" class="box1" @click.prevent> 按钮菜单栏 </a-button>
            <template #overlay>
              <a-menu class="tabRightClikBox1">
                <a-menu-item @click="queryClick()">查询</a-menu-item>
                <a-menu-item @click="startClick()" v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementReviewStart')"
                  >开始(F)</a-menu-item
                >
                <a-menu-item @click="reviewClick()" v-if="checkPermission('MES.EngineeringModule.ReviewManagement.ReviewManagementGetReviewUrl')"
                  >评审</a-menu-item
                >
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="dataVisible = false"
        @ok="handleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form>
          <a-row>
            <a-col :span="24">
              <a-form-item label="生产编号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
                <a-input v-model="formdata.OrderNo" placeholder="请输入生产编号" allowClear autoFocus> </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        title="确认弹窗"
        :visible="confirmvisible"
        @cancel="confirmvisible = false"
        @ok="handleOk1"
        ok-text="确定"
        :confirmLoading="confirmload"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <div>{{ confirmmessage }}</div>
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import RightCopy from "@/pages/RightCopy";
import { checkPermission } from "@/utils/abp";
import { engreviewlist, reviewstart } from "@/services/projectReview";
const reviewcolumns = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 45,
  },
  {
    title: "生产编号",
    align: "left",
    dataIndex: "orderNo",
    ellipsis: true,
    scopedSlots: { customRender: "orderNo" },
    width: 115,
  },
  {
    title: "下单时间",
    align: "left",
    dataIndex: "createTime",
    ellipsis: true,
    width: 135,
  },
  {
    title: "订单交期",
    align: "left",
    ellipsis: true,
    dataIndex: "deliveryDate",
    width: 95,
  },
  {
    title: "类型",
    dataIndex: "isReOrder",
    customRender: (text, record, index) =>
      `${record.isReOrder == 0 ? "新单" : record.isReOrder == 1 ? "返单" : record.isReOrder == 2 ? "返单更改" : ""}`,
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "状态",
    align: "left",
    dataIndex: "statusType",
    ellipsis: true,
    width: 85,
  },
  {
    title: "评审状态",
    align: "left",
    dataIndex: "reviewTypeStr",
    ellipsis: true,
    width: 85,
  },
  {
    title: "评审次数",
    align: "left",
    dataIndex: "reviewNo",
    ellipsis: true,
    width: 60,
  },
  {
    title: "评审日期",
    align: "left",
    dataIndex: "reviewCreate",
    ellipsis: true,
    width: 135,
    sorter: (a, b) => {
      return a.reviewCreate.localeCompare(b.reviewCreate);
    },
  },
  {
    title: "发出时间",
    align: "left",
    dataIndex: "reviewSendDate",
    ellipsis: true,
    width: 135,
    sorter: (a, b) => {
      return a.reviewSendDate.localeCompare(b.reviewSendDate);
    },
  },
  {
    title: "来源",
    width: 100,
    ellipsis: true,
    dataIndex: "reviewsourceStr",
    align: "left",
  },

  {
    title: "评审发送后耗时",
    align: "left",
    dataIndex: "reviewtime",
    ellipsis: true,
    width: 110,
  },
  {
    title: "评审完成时间",
    align: "left",
    dataIndex: "reviewEndTime",
    ellipsis: true,
    width: 110,
  },
];
export default {
  data() {
    return {
      confirmmessage: "",
      selecdata: {},
      spinning: false,
      confirmtype: "",
      confirmvisible: false,
      confirmload: false,
      formdata: {},
      dataVisible: false,
      reviewcolumns,
      Reviewsource: [],
      buttonsmenu: false,
      showText: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      text: "",
      isCtrlPressed: false,
      proOrderId: "",
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  components: {
    RightCopy,
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.getreviewdata();
      this.handleResize();
    });
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  methods: {
    checkPermission,
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        this.dataVisible = true;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    queryClick() {
      this.dataVisible = true;
      this.formdata = {};
    },
    startClick() {
      if (!this.proOrderId) {
        this.$message.warn("请选择需要开始的订单");
        return;
      }
      this.confirmmessage = this.selecdata.orderNo + "确定开始评审吗？";
      this.confirmvisible = true;
      this.confirmtype = "start";
    },
    handleOk1() {
      this.confirmload = true;
      if (this.confirmtype == "start") {
        reviewstart(this.proOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success("开始成功");
              this.$router.push({
                path: "ReviewDetails",
                query: {
                  OrderNo: this.selecdata.orderNo,
                  businessOrderNo: this.selecdata.businessOrderNo,
                  joinFactoryId: this.selecdata.joinFactoryId,
                  id: this.proOrderId,
                  reviewSource: this.selecdata.reviewsource,
                },
              });
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.confirmload = false;
          });
      }
    },
    reviewClick() {
      if (!this.proOrderId) {
        this.$message.warn("请选择订单进入评审页面");
        return;
      }
      this.$router.push({
        path: "ReviewDetails",
        query: {
          OrderNo: this.selecdata.orderNo,
          businessOrderNo: this.selecdata.businessOrderNo,
          joinFactoryId: this.selecdata.joinFactoryId,
          id: this.proOrderId,
          reviewSource: this.selecdata.reviewsource,
        },
      });
    },
    handleOk() {
      if (JSON.stringify(this.formdata) != "{}") {
        if (this.formdata.OrderNo && typeof this.formdata.OrderNo === "string" && this.formdata.OrderNo.replace(/\s+/g, "").length < 5) {
          this.$message.error("生产编号查询不能小于5位");
          return;
        }
        this.getreviewdata(this.formdata);
      }
      this.dataVisible = false;
    },
    getreviewdata(data) {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      var obj = Object.assign(params, data);
      this.spinning = true;
      engreviewlist(obj)
        .then(res => {
          this.Reviewsource = res.items;
          setTimeout(() => {
            this.handleResize();
          }, 0);
          this.pagination.total = res.totalCount;
          this.proOrderId = "";
          this.selecdata = {};
          if (params.OrderNo) {
            this.proOrderId = this.Reviewsource[0].proOrderId;
            this.selecdata = this.Reviewsource[0];
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      let params = this.formdata;
      if (JSON.stringify(params) != "{}") {
        this.getreviewdata(params);
      } else {
        this.getreviewdata();
      }
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.proOrderId && record.proOrderId == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.selecdata = record;
            this.proOrderId = record.proOrderId;
          },
          contextmenu: e => {
            e.preventDefault();
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    handleResize() {
      let screenHeight = window.innerHeight;
      let mainContent = document.getElementsByClassName("mainContent")[0];
      var leftstyle = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      if (this.Reviewsource.length == 0) {
        this.$refs.tableWrapper.style.height = screenHeight - 143 + "px";
      } else {
        this.$refs.tableWrapper.style.height = 0;
      }
      mainContent.style.height = screenHeight < 920 ? screenHeight - 135 + "px" : "775px";
      if (leftstyle && this.Reviewsource.length != 0) {
        leftstyle.style.height = screenHeight - 173 + "px";
      } else {
        leftstyle.style.height = 0;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
.box {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.box1 {
  float: right;
  margin-top: 9px;
  margin-right: 12px;
  width: 90px;
}
.footerAction {
  width: 100%;
  height: 50px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  form {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #fff;
  }
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
.tabRightClikBox1 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-table-fixed-header .ant-table-body-inner {
  overflow: hidden;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  margin-left: 10px;
  position: fixed;
  margin: 11px 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.projectReview {
  background-color: #ffffff;
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/.ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 7px 2px !important ;
    overflow-wrap: break-word;
  }
  /deep/ .ant-table {
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-thead > tr > th {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px;
      height: 36px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
  /deep/.ant-table-row-cell-break-word {
    border-right: 1px solid #efefef;
  }
}
/deep/.mainContent {
  .mintable {
    .ant-table-body-inner {
      max-height: 737px !important;
    }
    .ant-table-pagination {
      margin: 6px 0;
      z-index: 99;
      position: absolute;
      bottom: -6.5%;
      margin-left: 1%;
    }
    .ant-table-body {
      min-height: 738px;
    }
  }
  border: 2px solid rgb(233, 233, 240);
  border-bottom: 4px solid rgb(233, 233, 240);
}
</style>
