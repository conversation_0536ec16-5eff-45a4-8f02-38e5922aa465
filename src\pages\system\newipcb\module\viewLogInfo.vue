<!--
 * @Author: CJP
 * @Date: 2022-05-30 08:20:52
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-27 16:27:03
 * @FilePath: \vue-antd-admin\src\pages\projectPage\module\viewLogInfo.vue
 * @Description: 
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved. 
-->
<template>
  <div class="viewLog">
    <a-empty v-if="!viewLogData.length > 0" />
    <a-steps progress-dot direction="vertical" v-else :current="-1">
      <a-step
        :title="gettitle(item)"
        :description="getdescription(item)"
        v-for="(item, index) in viewLogData"
        :key="index"
        :status="item.state == '完成' ? 'finish' : item.state == '处理中' ? 'process ' : 'wait '"
      >
      </a-step>
    </a-steps>
  </div>
</template>

<script>
export default {
  name: "viewLogInfo",
  props: ["viewLogData"],
  data() {
    return {
      current1: 0,
      status1: "",
    };
  },
  mounted() {},
  watch: {
    viewLogData: function (val) {
      if (val) {
        this.getData();
      }
    },
  },
  methods: {
    gettitle(item) {
      return (
        <div style="font-size:13px;line-height:3.15ch;    padding:0 0 15px 0;">
          <div>
            <span style="font-size:16px;">{item.taskType}</span> {item.state} {item.serverIP ? "(IP:" + item.serverIP + ")" : null}
          </div>
          <div> 创建时间: {item.createTime}</div>
        </div>
      );
    },
    getdescription(item) {
      return (
        <div style="font-size:13px;line-height:3.15ch">
          <div>开始:{item.startTime}</div>
          <div> 结束:{item.endTime}</div>
        </div>
      );
    },
    getData() {
      let arr = this.viewLogData.filter(item => {
        return item.endTime;
      });
      this.current1 = arr.length;
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: green;
}
/deep/.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
  background-color: green;
}
/deep/.ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: gray;
}
/deep/.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-tail::after {
  background-color: gray;
}
/deep/.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: #ff9900;
}
/deep/.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {
  background-color: #ff9900;
}
/deep/.ant-steps {
  color: #000000;
}
/deep/.ant-steps-item-container {
  display: flex;
}
/deep/.ant-steps-vertical .ant-steps-item-content {
  min-height: 30px;
}
.viewLog {
  // padding-left:50px;
  /deep/ .ant-steps-item-content {
    .ant-steps-item-title {
      display: inline-block;
      width: 45%;
    }
    .ant-steps-item-description {
      display: inline-block;
      width: 54%;
      padding-bottom: 0;
    }
    margin-left: 10px;
    width: 98%;
    display: inline-block;
  }
  max-height: 800px;
  // overflow-y:auto;
  // overflow-y:scroll;
}
</style>
