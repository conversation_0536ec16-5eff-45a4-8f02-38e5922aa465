<!--
 * @Author: CJP
 * @Date: 2022-05-30 08:20:52
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-27 11:19:20
 * @FilePath: \vue-antd-admin\src\pages\projectPage\module\QueryInfo.vue
 * @Description:
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved.
-->
<template >
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}"  >
    <a-form-item label="原密码" prop="password" >
      <a-input-password  id="el" v-model='oldNumber' placeholder="请输入原密码"  :autoFocus="autoFocus" @blur="checkNum" @keyup.enter="submit"   />
    </a-form-item>
    <div v-if='errFlag' style="position: absolute;top:120px;left:130px">
      <span  style="color: red">*输入的原密码错误,请重新输入*</span>
     </div>
    <a-form-item label="新密码">
      <a-input-password  id="newEL" v-model='newNumber' placeholder="请输入新密码"  :disabled="!editFlag" @blur="animateWidth()" @change="animateWidth1()"  v-focus-next-on-enter="'input3'"  ref="input2"/>
      <!-- <span v-if="Dom1" style="font-size: 14px;line-height:24px;margin-bottom:-20px;color:red;display: block;"> 请输⼊密码</span>  
      <span v-if="Dom2" style=" font-size: 14px;line-height:24px;margin-bottom:-20px;color:red;display: block;"> 请输⼊6~20位密码</span>   -->
      <span v-if="Dom3" style=" font-size: 14px;line-height:24px;margin-bottom:-20px;color:red;display: block;"> 密码必须6-20位英文字母、数字或者符号（除空格），且字母、数字和标点符号至少包含两种</span>  
    </a-form-item>
    <a-form-item label="确认密码" style="margin-bottom: 0">
      <a-input-password   id="id" v-model='checkNewNumber' placeholder="请确认新密码" @blur="checkNum1"  @keyup.enter="submit1" :disabled="!editFlag"  ref="input3" />
    </a-form-item>
    <div v-if='errFlag1' style="position: absolute;left:130px">
     <span  style="color: red">*与新密码不一致,请重新输入*</span>
    </div>
    <div v-if="errFlag2" style="position: absolute;left:130px">
      <span  style="color: #ff9900">*确认新密码成功，请点击确定提交*</span>
    </div>
  </a-form>
</template>

<script>
import {
  oldPassword,
} from "@/services/projectDisptch";
export default {
    name:'ChangePassword',
  data() {
    return {
      oldNumber:'',
      newNumber:'',
      checkNewNumber:'',
      editFlag:false,
      autoFocus:true,
      errFlag:false,
      errFlag1:false,
      errFlag2:false,
      Dom1:false,
      Dom2:false,
      Dom3:false,
    };
  },
  methods: {
    submit(){
      this.checkNum()
    },
    submit1(){
      this.checkNum1()
    },
    // 失去
    animateWidth() {
      let name = this.newNumber
      // console.log('name',name)
      var regex = new RegExp(/^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$)([^\u4e00-\u9fa5\s]){6,20}$/);
        // if (name === "") {
        //   this.Dom1 = true
        // } else if (name.length < 6 || name.length > 20) {
        //   this.Dom2= true
        // } else if (!regex.test(name)) {
        //   this.Dom3 = true
        // }
        if (!regex.test(name)) {
          this.Dom3 = true
        }
    },
    // 获得
    animateWidth1() {
      let name = this.newNumber
      console.log('name1',name,name.length,)
      var regex = new RegExp(/^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$)([^\u4e00-\u9fa5\s]){6,20}$/);
        // if (name != "") {
        //   this.Dom1 = false         
        // } 
        // if (name.length > 6 && name.length < 20) {    
        //   console.log('长度',name.length)      
        //   this.Dom2= false
        // } 
         if (regex.test(name)) {  
          this.Dom3= false
        }
    },
    checkNum(){
      if(this.oldNumber){
        oldPassword(this.oldNumber).then(res=>{
          if(res){
            this.editFlag = true
            this.errFlag = false
            document.getElementById('newEL').focus();
          }else{
            this.errFlag = true
            this.editFlag = false
            document.getElementById('el').focus();
            this.oldNumber = ''
          }
        })
      }


    },
    checkNum1(){
      if(this.checkNewNumber != this.newNumber){
        this.errFlag1 = true
        this.checkNewNumber = ''
        // document.getElementById('id').focus();
      }else{
        this.errFlag1 = false
        this.errFlag2 = true
      }
    },
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
          }
        }
      })
    }
  }
},
};
</script>
