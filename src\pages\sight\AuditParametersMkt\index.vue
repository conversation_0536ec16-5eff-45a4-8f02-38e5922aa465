<template>
  <div ref="SelectBox" style="background: #d5e4f2; height: 100%" @click="click($event)">
    <div class="header">
      <a-button type="primary" style="background-color: #02906c; border-color: #02906c; width: 90px; padding: 0" @click="foldClick" v-if="fold">
        <a-icon type="minus-square"></a-icon>全部收起
      </a-button>
      <a-button type="primary" style="background-color: #02906c; border-color: #02906c; width: 90px; padding: 0" @click="foldClick" v-else>
        <a-icon type="plus-square" />全部展开
      </a-button>
      <div class="labelSearch" style="display: flex">
        <a-input type="text" autocomplete="off" id="labelSearchInput" allow-clear placeholder="搜索" v-model="searchText" @change="search" />
        <div class="searchRight">
          <span class="item" style="margin-right: 5px"> {{ num === 0 ? "0/0" : indexNum + 1 + "/" + num }}</span>
          <a-icon type="up" circle @click="prev"></a-icon>
          <a-icon type="down" circle @click="next"></a-icon>
        </div>
      </div>
    </div>
    <div class="content">
      <a-collapse v-model="activeNames" @change="changeActivekey">
        <template slot="expandIcon" slot-scope="record">
          <a-icon type="minus-square" v-if="record.isActive" />
          <a-icon type="plus-square" v-else />
        </template>
        <a-collapse-panel :key="String(itemIndex)" :header="item.typename" v-for="(item, itemIndex) in QuoteDataList" :forceRender="true">
          <div class="capacity" :dataIndex="itemIndex + 1">
            <a-form-model :model="QuoteForm" :label-col="{ span: 11 }" :wrapper-col="{ span: 13 }" id="formDataElem" ref="ulScroll">
              <span v-for="(it, Index) in item.dtos" :key="Index">
                <a-form-model-item
                  :label="it.descript"
                  v-if="it.descript != '金厚' && it.descript != '金面积比' && it.descript != '镍厚' && it.descript != '钯厚' && it.isview == true"
                  @click.native="labelClick(it.name, it.funcstr)"
                  :class="[
                    it.isview == true ? (it.funcstr != '' && it.funcstr != null ? 'backsty2' : 'backsty') : '',
                    it.isrequired == true ? 'red' : '',
                  ]"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template v-if="it.name == 'InnerCuThick' || it.name == 'OuterCuThick' || it.name == 'PinBanType' || it.name == 'DrillStructs'">
                      <a-input
                        :disabled="!edit"
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="it.type == '文本输入' || it.type == '参数栏'"
                        readOnly
                        @change="change(it.name)"
                        @click.stop="click1(it.name)"
                      />
                    </template>
                    <template v-else>
                      <a-input
                        :disabled="!edit"
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click.stop="click1(it.name)"
                      />
                    </template>
                    <a-checkbox :disabled="!edit" v-model="QuoteForm[it.name].Value" v-if="it.type == '勾选项'" @change="change(it.name)" />
                    <a-textarea
                      :title="QuoteForm[it.name].Value"
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                      :getPopupContainer="() => $refs.SelectBox"
                      :disabled="!edit"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      :getPopupContainer="() => $refs.SelectBox"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                      :disabled="!edit"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      mode="multiple"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      class="sty"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                      :disabled="!edit"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'InnerCuThick' && visible && list.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list" :key="index">
                          第{{ index + 1 }}层:
                          <a-input
                            v-model="formList[index]"
                            @change="listChange(index, formList[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList[index]" @change="listChange(index, formList[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'OuterCuThick' && visible1 && list1.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list1" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">顶层:</span>
                          <span v-else style="margin: 0 5px">底层:</span>
                          <a-input
                            v-model="formList1[index]"
                            @change="listChange(index, formList1[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList1[index]" @change="listChange(index, formList1[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'PinBanType' && visible2 && list2.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list2" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">X:</span>
                          <span v-else style="margin: 0 5px">Y:</span>
                          <a-input
                            v-model="formList2[index]"
                            @change="listChange(index, formList2[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList2[index]" @change="listChange(index, formList2[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                  </div>
                </a-form-model-item>
                <a-form-model-item
                  :label="it.descript"
                  v-else-if="it.descript != '金厚' && it.descript != '金面积比' && it.descript != '镍厚' && it.descript != '钯厚'"
                  :class="[it.isrequired == true ? 'red' : '']"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template v-if="it.name == 'InnerCuThick' || it.name == 'OuterCuThick' || it.name == 'PinBanType'">
                      <a-input
                        :disabled="!edit"
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="it.type == '文本输入' || it.type == '参数栏'"
                        readOnly
                        @change="change(it.name)"
                        @click.stop="click1(it.name)"
                      />
                    </template>
                    <template v-else>
                      <a-input
                        :disabled="!edit"
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click="click1(it.name)"
                      />
                    </template>

                    <a-checkbox :disabled="!edit" v-model="QuoteForm[it.name].Value" v-if="it.type == '勾选项'" @change="change(it.name)" />
                    <a-textarea
                      :title="QuoteForm[it.name].Value"
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      :getPopupContainer="() => $refs.SelectBox"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      mode="multiple"
                      class="sty"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'InnerCuThick' && visible && list.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list" :key="index">
                          第{{ index + 1 }}层:
                          <a-input
                            v-model="formList[index]"
                            @change="listChange(index, formList[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList[index]" @change="listChange(index, formList[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'OuterCuThick' && visible1 && list1.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list1" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">顶层:</span>
                          <span v-else style="margin: 0 5px">底层:</span>
                          <a-input
                            v-model="formList1[index]"
                            @change="listChange(index, formList1[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList1[index]" @change="listChange(index, formList1[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                    <div
                      class="table-filter-view filter-input-view editInnerInfo"
                      v-if="it.name == 'PinBanType' && visible2 && list2.length"
                      @click.stop
                    >
                      <ul>
                        <li v-for="(item, index) in list2" :key="index">
                          <span v-if="index == '0'" style="margin: 0 5px">X:</span>
                          <span v-else style="margin: 0 5px">Y:</span>
                          <a-input
                            v-model="formList2[index]"
                            @change="listChange(index, formList2[index], it.name)"
                            :autoFocus="autoFocus"
                            v-if="index == '0'"
                          ></a-input>
                          <a-input v-model="formList2[index]" @change="listChange(index, formList2[index], it.name)" v-else></a-input>
                        </li>
                      </ul>
                    </div>
                  </div>
                </a-form-model-item>
                <a-form-model-item
                  :label="it.descript"
                  v-else-if="
                    (it.descript == '金厚' || it.descript == '金面积比' || it.descript == '镍厚') &&
                    JSON.stringify(QuoteForm) != '{}' &&
                    JSON.stringify(QuoteForm.SurfaceTreat) != '{}' &&
                    QuoteForm.SurfaceTreat.Value &&
                    (QuoteForm.SurfaceTreat.Value.includes('BS018004') ||
                      QuoteForm.SurfaceTreat.Value.includes('BS018005') ||
                      QuoteForm.SurfaceTreat.Value.includes('BS018007') ||
                      QuoteForm.SurfaceTreat.Value.includes('BS018014'))
                  "
                  :class="[it.isrequired == true ? 'red' : '']"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template>
                      <a-input
                        :disabled="!edit"
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click="click1(it.name)"
                      />
                    </template>

                    <a-checkbox
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '勾选项'"
                      @change="change(it.name)"
                    />
                    <a-textarea
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                      :getPopupContainer="() => $refs.SelectBox"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      mode="multiple"
                      class="sty"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-form-model-item>
                <a-form-model-item
                  :label="it.descript"
                  v-else-if="
                    it.descript == '钯厚' &&
                    JSON.stringify(QuoteForm) != '{}' &&
                    JSON.stringify(QuoteForm.SurfaceTreat) != '{}' &&
                    QuoteForm.SurfaceTreat.Value &&
                    QuoteForm.SurfaceTreat.Value.includes('BS018014')
                  "
                  :class="[it.isrequired == true ? 'red' : '']"
                >
                  <div v-if="QuoteForm[it.name]" :class="it.mulselect == true ? 'test' : ''">
                    <template>
                      <a-input
                        :disabled="!edit"
                        :title="QuoteForm[it.name].Value"
                        v-model="QuoteForm[it.name].Value"
                        v-if="(it.type == '文本输入' || it.type == '参数栏') && it.multiline == false"
                        @blur="blur(it.name, it.isregex, it.regexinfo)"
                        @change="change(it.name)"
                        @click="click1(it.name)"
                      />
                    </template>

                    <a-checkbox :disabled="!edit" v-model="QuoteForm[it.name].Value" v-if="it.type == '勾选项'" @change="change(it.name)" />
                    <a-textarea
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      :auto-size="{ minRows: it.multilinenum, maxRows: it.multilinenum }"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.multiline == true && (it.type == '文本输入' || it.type == '参数栏')"
                      @change="change(it.name)"
                    />
                    <a-select
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == false"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉输入栏'"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      :getPopupContainer="() => $refs.SelectBox"
                      @change="setEstimate($event, it.name)"
                      @search="handleSearch($event, it.name)"
                      @blur="handleBlur($event, it.name)"
                    >
                      <a-select-option v-for="(ite, index) in mapKey(it.pars)" :title="ite.lable" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                    <a-select
                      :disabled="!edit"
                      :title="QuoteForm[it.name].Value"
                      mode="multiple"
                      class="sty"
                      v-model="QuoteForm[it.name].Value"
                      v-if="it.type == '下拉参数栏' && it.mulselect == true"
                      :getPopupContainer="() => $refs.SelectBox"
                      showSearch
                      allowClear
                      optionFilterProp="label"
                      @change="change(it.name)"
                    >
                      <a-select-option :title="ite.lable" v-for="(ite, index) in mapKey(it.pars)" :key="index" :value="ite.Value" :label="ite.lable">
                        {{ ite.lable }}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-form-model-item>
              </span>
              <template> </template>
            </a-form-model>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
    <div class="buton">
      <a-button
        type="primary"
        v-if="edit"
        @click="handleSubmit"
        :disabled="submitFormBtn"
        style="margin-left: 45%; background-color: #02906c; border-color: #02906c"
      >
        保存
      </a-button>
      <a-button type="primary" v-if="!edit" @click="editSubmit" style="margin-left: 45%; background-color: #02906c; border-color: #02906c">
        编辑
      </a-button>
    </div>
  </div>
</template>

<script>
import { checkList, checkBillInfo, setCheckBillInfo, setSightInfoToEMS } from "@/services/sight/sight.js";
import { graphicPreview } from "@/services/mkt/PrequalificationProduction.js";
import $ from "jquery";
const delay = (function () {
  let timer = 0;
  return function (callback, ms) {
    clearTimeout(timer);
    timer = setTimeout(callback, ms);
  };
})();
export default {
  name: "AuditParameters",
  inject: ["reload"],
  data() {
    return {
      edit: false,
      routeList: "",
      QuoteDataList: [],
      copyQuoteDataList: [],
      activeKey: [0],
      fold: false,
      QuoteForm: {},
      copyQuoteForm: {},
      QuoteData: {},
      submitFormBtn: false,
      contextHtml: "",
      nowNum: 0,
      totalNum: 0,
      inputUpDisable: true,
      inputDownDisable: false,
      visible: false,
      list: [],
      formList: {},
      list1: [],
      formList1: {},
      visible1: false,
      list2: [],
      formList2: {},
      visible2: false,
      autoFocus: true,
      searchText: "",
      index: 1,
      query: "",
      indexNum: 0,
      num: 0,
      main: null,
      showArr: [],
      showIndex: 0,
      activeNames: [0],
    };
  },
  async created() {
    localStorage.setItem("savesuccess", false);
    graphicPreview(this.$route.query.id).then(res => {
      if (res.data) {
        this.routeList = res.data.split("softcode=")[1].split("&")[0];
      } else {
        this.$message.error(res.message);
      }
    });
    await this.getList();
    await this.getData();
  },
  async mounted() {
    window.vue = this;
    window.addEventListener("keydown", this.keydown);
    // const UserOption =  window.external.GerCheckBill(JSON.stringify({}));
    // this.submitFormBtn = !JSON.parse(UserOption).UserOption;
  },
  filters: {
    filterData(data, list) {
      let name = "";
      list.filter(item => {
        if (item.key == data) {
          name = item.Value;
        }
      });
      return name;
    },
  },
  // watch:{
  //     'QuoteForm':{
  //         handler(val,ol){
  //             if(val.BoardThick){
  //                 // alert(val.BoardThick.Value)
  //                if(val.BoardThick.Value !=null || val.BoardThick.Value !=''){
  //                 window.external.ModifyReviewData(JSON.stringify({
  //                     Thickness:{"Value":val.BoardThick.Value,"Unit":"mm"}
  //                 }));
  //                }
  //             }
  //         },
  //         deep: true,  // 可以深度检测到 obj 对象的属性值的变化
  //         immediate: true //刷新加载  立马触发一次handler
  //     }
  // },
  methods: {
    setCreateSetInfo(pars) {
      // alert(pars)
      let Form = JSON.parse(pars);
      for (var a = 0; a < Object.keys(Form).length; a++) {
        if (Object.keys(this.QuoteForm).indexOf(Object.keys(Form)[a]) >= 0) {
          this.QuoteForm[Object.keys(Form)[a]] = { Value: Form[Object.keys(Form)[a]] };
        }
      }
    },
    setGerberData(pars) {
      // alert(pars)
      let copyQuoteForm = JSON.parse(pars).BaseResult;
      for (var a = 0; a < Object.keys(copyQuoteForm).length; a++) {
        if (Object.keys(this.QuoteForm).indexOf(Object.keys(copyQuoteForm)[a]) >= 0) {
          this.QuoteForm[Object.keys(copyQuoteForm)[a]] = copyQuoteForm[Object.keys(copyQuoteForm)[a]];
        }
      }
    },
    async getList() {
      let softcode = "";
      if (this.$route.query.softcode) {
        softcode = this.$route.query.softcode;
      }
      var configtype = "";
      if (this.$route.query.configtype) {
        configtype = this.$route.query.configtype;
      }
      configtype = configtype + ",2";
      await checkList(softcode, configtype).then(res => {
        if (res.code) {
          this.QuoteDataList = res.data;
          for (var i = 0; i < this.QuoteDataList.length; i++) {
            var obj = {};
            var tempKey = Object.keys(this.QuoteDataList[i]);
            for (var ite = 0; ite < tempKey.length; ite++) {
              obj[tempKey[ite].toLowerCase()] = this.QuoteDataList[i][tempKey[ite]];
            }
            this.QuoteDataList[i] = obj;
            this.QuoteDataList[i].dtos = this.convertKeysToLowerCase(this.QuoteDataList[i].dtos);
          }
          var objlist = {};
          for (var a = 0; a < this.QuoteDataList.length; a++) {
            for (var b = 0; b < this.QuoteDataList[a].dtos.length; b++) {
              var name = this.QuoteDataList[a].dtos[b].name;
              if (!Object.keys(this.QuoteForm).includes(name)) {
                var str1 = name;
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  objlist[str1] = { Value: false };
                }
                if (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == true) {
                  objlist[str1] = { Value: [] };
                }
                if (
                  (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == false) ||
                  this.QuoteDataList[a].dtos[b].type == "文本输入" ||
                  this.QuoteDataList[a].dtos[b].type === "参数栏" ||
                  this.QuoteDataList[a].dtos[b].type === "下拉输入栏"
                ) {
                  objlist[str1] = { Value: "" };
                }
              }
            }
          }
          this.QuoteForm = objlist;
          console.log(this.QuoteForm, "426");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    convertKeysToLowerCase(arr) {
      return arr.map(obj =>
        Object.keys(obj).reduce((newObj, key) => {
          newObj[key.toLowerCase()] = obj[key];
          return newObj;
        }, {})
      );
    },
    async getData(aa) {
      let Boid = "";
      if (this.$route.query.boid != undefined) {
        Boid = this.$route.query.boid;
      }
      let sightsite = "";
      if (this.$route.query.sight != undefined) {
        sightsite = this.$route.query.sight;
      }
      await checkBillInfo(Boid, sightsite).then(res => {
        if (res.code) {
          const obj = JSON.parse(res.data).BaseResult;
          for (const key in obj) {
            if (obj[key].value !== undefined) {
              obj[key].Value = obj[key].value;
              delete obj[key].value;
            }
          }
          this.QuoteForm = obj;
          if (this.QuoteForm.OuterCuThick.Value != "") {
            if (this.QuoteForm.OuterCuThick.Value.toString().indexOf("/") != -1) {
              let a = this.QuoteForm.OuterCuThick.Value.split("/");
              if (a[0] == 0) {
                this.QuoteForm.OuterCuThick.Value = "";
              } else {
                this.QuoteForm.OuterCuThick.Value = a[0];
              }
            } else {
              this.QuoteForm.OuterCuThick.Value = this.QuoteForm.OuterCuThick.Value == 0 ? "" : this.QuoteForm.OuterCuThick.Value;
            }
          } else {
            this.QuoteForm.OuterCuThick.Value = "";
          }

          if (this.QuoteForm.InnerCuThick.Value != "" && this.QuoteForm.InnerCuThick.Value.toString() != "0") {
            if (this.QuoteForm.InnerCuThick.Value.toString().indexOf("/") != -1) {
              let a = this.QuoteForm.InnerCuThick.Value.split("/");
              if (a[0] == 0) {
                this.QuoteForm.InnerCuThick.Value = "";
              } else {
                this.QuoteForm.InnerCuThick.Value = a[0];
              }
            } else {
              this.QuoteForm.InnerCuThick.Value = this.QuoteForm.InnerCuThick.Value == 0 ? "" : this.QuoteForm.InnerCuThick.Value;
            }
          } else {
            this.QuoteForm.InnerCuThick.Value = "";
          }
          for (var a = 0; a < this.QuoteDataList.length; a++) {
            for (var b = 0; b < this.QuoteDataList[a].dtos.length; b++) {
              var name = this.QuoteDataList[a].dtos[b].name;
              var str = name;
              if (!this.QuoteForm[str]) {
                this.QuoteForm[str] = { Value: "" };
              }
              if (this.QuoteForm[str].Value == "" || this.QuoteForm[str].Value == "0") {
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str] = { Value: false };
                }
                if (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == true) {
                  this.QuoteForm[str] = { Value: [] };
                }
              }
              if (this.QuoteForm[str].Value == "true") {
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str] = { Value: true };
                }
              }
              if (this.QuoteForm[str].Value == "false") {
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str] = { Value: false };
                }
              }
              if (!Object.keys(this.QuoteForm).includes(name)) {
                var str1 = name;
                if (this.QuoteDataList[a].dtos[b].type == "勾选项") {
                  this.QuoteForm[str1] = { Value: false };
                }
                if (this.QuoteDataList[a].dtos[b].type == "下拉参数栏" && this.QuoteDataList[a].dtos[b].mulselect == true) {
                  this.QuoteForm[str1] = { Value: [] };
                } else {
                  this.QuoteForm[str1] = { Value: "" };
                }
              }
            }
          }
          this.copyQuoteDataList = JSON.parse(JSON.stringify(this.QuoteDataList));
          this.copyQuoteForm = JSON.parse(JSON.stringify(this.QuoteForm));
          this.editclick();
        }
      });
    },
    editclick() {
      if (!this.edit) {
        let data = [];
        for (const key in this.QuoteForm) {
          const value = this.QuoteForm[key].Value;
          if (value === "" || value === false || !value || JSON.stringify(value) === "[]") {
            delete this.QuoteForm[key];
          }
        }
        for (let i = 0; i < this.QuoteDataList[0].dtos.length; i++) {
          if (Object.keys(this.QuoteForm).includes(this.QuoteDataList[0].dtos[i].name)) {
            data.push(this.QuoteDataList[0].dtos[i]);
          }
        }
        this.QuoteDataList[0].dtos = [...new Set(data)];
      } else {
        this.QuoteDataList = this.copyQuoteDataList;
        this.QuoteForm = this.copyQuoteForm;
      }
    },
    // 控制全部展开、折叠
    changeActivekey(val) {
      // this.activeKey = val
      if (val.length == this.QuoteDataList.length) {
        this.fold = true;
      } else {
        this.fold = false;
      }
    },
    foldClick() {
      if (this.fold) {
        this.activeNames = [];
      } else {
        for (var a = 0; a < this.QuoteDataList.length; a++) {
          if (!this.activeNames.includes(a)) {
            var stra = a.toString();
            this.activeNames.push(stra);
          }
          // this.activeKey.push(a)
        }
      }
      console.log("2", this.activeNames);
      this.fold = !this.fold;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { Value: item, lable: data[item] };
        });
      }
    },
    mapKey1(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data)
          .sort()
          .map(item => {
            return { Value: item, lable: data[item] };
          });
      }
    },
    change(val) {
      if (val == "Layer") {
        let arr_ = [];
        for (var i = 0; i < this.QuoteForm.Layer.Value - 2; i++) {
          arr_.push(1);
        }
        // this.QuoteForm.InnerCuThick.Value = arr_.join('/')
        this.QuoteForm.InnerCuThick.Value = "1";
        this.list = arr_;
        for (var key in this.list) {
          this.formList[key] = this.list[key];
        }
        // this.QuoteForm.OuterCuThick.Value = '1/1'
        // this.list1 = [1,1]
        // for (var key1 in this.list1) {
        // this.formList1[key1] = this.list1[key1];
        // }
      }
      if (val <= 2) {
        this.QuoteForm.InnerCuThick.Value = "";
      }
      //    alert(this.QuoteForm.Layer.Value)
      console.log("改变了");
      this.$forceUpdate();
    },
    blur(name, isregex, regexinfo) {
      if (isregex && regexinfo) {
        var z = new RegExp(regexinfo);
        if (!z.test(this.QuoteForm[name].Value)) {
          this.QuoteForm[name].Value = "";
        }
      }
    },
    setEstimate(Value, name) {
      this.QuoteForm[name].Value = Value;
      // console.log('手动输入', this.QuoteForm[name].Value,)
    },
    handleSearch(Value, name) {
      this.setEstimate(Value, name);
    },
    handleBlur(Value, name) {
      this.setEstimate(Value, name);
    },
    click1(val) {
      if (val == "InnerCuThick") {
        if (this.QuoteForm.InnerCuThick.Value) {
          // this.list = this.QuoteForm.InnerCuThick.Value.split('/')
          let arr_ = [];
          for (var b = 0; b < this.QuoteForm.Layer.Value - 2; b++) {
            arr_.push(this.QuoteForm.InnerCuThick.Value);
          }
          this.list = arr_;
          for (var keyy in this.list) {
            this.formList[keyy] = this.list[keyy];
          }
        } else {
          let arr_ = [];
          for (var i = 0; i < this.QuoteForm.Layer.Value - 2; i++) {
            arr_.push(1);
          }
          this.list = arr_;
          for (var key in this.list) {
            this.formList[key] = this.list[key];
          }
        }
        this.visible = true;
        this.visible1 = false;
        this.visible2 = false;
      }
      if (val == "OuterCuThick") {
        if (this.QuoteForm.OuterCuThick.Value) {
          // this.list1 = this.QuoteForm.OuterCuThick.Value.split('/')
          let arr_ = [];
          for (var a = 0; a < 2; a++) {
            arr_.push(this.QuoteForm.OuterCuThick.Value);
          }
          this.list1 = arr_;
          for (var key1 in this.list1) {
            this.formList1[key1] = this.list1[key1];
          }
        } else {
          this.list1 = ["", ""];
          for (var key2 in this.list1) {
            this.formList1[key2] = this.list1[key2];
          }
        }
        this.visible = false;
        this.visible1 = true;
        this.visible2 = false;
      }
      if (val == "PinBanType") {
        if (this.QuoteForm.PinBanType.Value) {
          this.list2 = this.QuoteForm.PinBanType.Value.toLowerCase().split("x");
          for (var aa in this.list2) {
            this.formList2[aa] = this.list2[aa];
          }
        } else {
          this.list2 = [1, 1];
          for (var bb in this.list2) {
            this.formList2[bb] = this.list2[bb];
          }
          this.QuoteForm.PinBanType.Value = this.list2.join("x");
        }
        this.visible = false;
        this.visible1 = false;
        this.visible2 = true;
      }
    },
    click(e) {
      this.visible = false;
      this.visible1 = false;
      this.visible2 = false;
    },
    listChange(index, item, name) {
      if (name == "InnerCuThick") {
        for (var i = 0; i < this.list.length; i++) {
          if (index == 0) {
            if (i >= index) {
              this.list[i] = item;
            }
          } else {
            if (i == index) {
              this.list[i] = item;
            }
          }
        }
        for (var key in this.list) {
          this.formList[key] = this.list[key];
        }
        // this.QuoteForm.InnerCuThick.Value = this.list.join('/')
        const maxNumber = Math.max(...this.list);
        console.log("InnerCuThick", maxNumber);
        this.QuoteForm.InnerCuThick.Value = maxNumber;
      }
      if (name == "OuterCuThick") {
        for (var j = 0; j < this.list1.length; j++) {
          if (j >= index) {
            this.list1[j] = item;
          }
        }
        for (var key1 in this.list1) {
          this.formList1[key1] = this.list1[key1];
        }
        // this.QuoteForm.OuterCuThick.Value = this.list1.join('/')
        const maxNumber = Math.max(...this.list1);
        console.log("maxNumber", maxNumber);
        this.QuoteForm.OuterCuThick.Value = maxNumber;
      }
      if (name == "PinBanType") {
        for (var A = 0; A < this.list2.length; A++) {
          if (A == index) {
            this.list2[A] = item;
          }
        }
        for (var AA in this.list2) {
          if (this.list2[AA] == "") {
            this.list2[AA] = "1";
          }
          this.formList2[AA] = this.list2[AA];
        }
        this.QuoteForm.PinBanType.Value = this.list2.join("x");
      }
    },
    editSubmit() {
      this.edit = true;
      this.editclick();
    },
    handleSubmit() {
      var arr = [];
      for (var a = 0; a < this.QuoteDataList.length; a++) {
        for (var b = 0; b < this.QuoteDataList[a].dtos.length; b++) {
          var name = this.QuoteDataList[a].dtos[b].name;
          var descript = this.QuoteDataList[a].dtos[b].descript;
          var isrequired = this.QuoteDataList[a].dtos[b].isrequired;
          var mulselect = this.QuoteDataList[a].dtos[b].mulselect;
          if (isrequired && !this.QuoteForm[name].Value && !mulselect) {
            arr.push(descript);
          }
          if (isrequired && mulselect && (!this.QuoteForm[name].Value || this.QuoteForm[name].Value.length == 0)) {
            arr.push(descript);
          }
        }
      }
      if (arr.length) {
        this.$message.warning("请填写必填项" + arr);
        return;
      }
      this.edit = false;
      var obj = {};
      // this.QuoteForm.DrillStructs.Value = JSON.parse(this.QuoteForm.DrillStructs.Value)
      obj.BaseResult = this.QuoteForm;
      console.log("this.QuoteForm ", this.QuoteForm);
      // const OuterCuThickarr = this.QuoteForm.OuterCuThick.Value.split('/');
      // const OuterCuThickMax = Math.max(...OuterCuThickarr);
      // const InnerCuThickarr = this.QuoteForm.InnerCuThick.Value.split('/');
      // const InnerCuThickMax = Math.max(...InnerCuThickarr);
      // obj.BaseResult.OuterCuThick.Value = OuterCuThickMax.toString();
      // obj.BaseResult.InnerCuThick.Value = InnerCuThickMax.toString();
      console.log("666", obj.BaseResult);
      let str = JSON.stringify(obj);
      let params = {
        BOID: this.$route.query.boid,
        sightsite: this.$route.query.sight,
        softcode: this.routeList,
        mac: "",
        str: str,
      };
      setCheckBillInfo(params).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
          localStorage.setItem("savesuccess", true);
          this.getData("1");
          setSightInfoToEMS(params.BOID, params.softcode).then(re => {
            if (!re.code) {
              this.$message.error(re.message);
            }
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    labelClick(name, funcstr) {
      // if(funcstr != '' && funcstr != null){
      //     if(funcstr == 'openIaterial'){
      //         this.openIaterial(name)
      //     }
      //     if(funcstr == 'openDrillStruct'){
      //         this.openDrillStruct(name)
      //     }
      // }else{
      //     window.external.ReviewInfo(JSON.stringify({
      //     name:name,
      //     Value: this.QuoteForm[name].Value,
      //     unit: '',
      // }));
      // }
      // alert(this.QuoteForm[name].Value)
    },
    openIaterial(name) {
      // if(window.external && typeof window.external.OpenIaterial != 'undefined' && typeof window.external.OpenIaterial != undefined){
      var PCSWidth = this.QuoteForm.PCSWidth.Value.toString() || "100";
      var PCSHeight = this.QuoteForm.PCSHeight.Value.toString();
      var PCSHeightUnit = "mm";
      var SetWidth = this.QuoteForm.SetWidth.Value.toString();
      var SetHeight = this.QuoteForm.SetHeight.Value.toString();
      var SetHeightUnit = "mm";
      var Layer = this.QuoteForm.Layer.Value.toString();
      var SetX =
        this.QuoteForm.PinBanType.Value && this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[0]
          ? this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[0]
          : "1";
      var SetY =
        this.QuoteForm.PinBanType.Value && this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[1]
          ? this.QuoteForm.PinBanType.Value.toLowerCase().split("x")[1]
          : "1";
      // console.log(Layer,PCSWidth,PCSHeight,SetWidth,SetHeight,SetX,SetY,this.QuoteForm.PinBanType.Value)
      window.external.OpenIaterial(
        JSON.stringify({
          Layer: Layer,
          PCSWidth: PCSWidth,
          PCSHeight: PCSHeight,
          SetWidth: SetWidth,
          SetHeight: SetHeight,
          SetX: SetX,
          SetY: SetY,
        })
      );
      // }
    },
    setCuttingInfo(data) {
      //点击(开料按钮)保存IPCB-SIGHT后，软件会返回开料信息
      if (data) {
        // alert(data)
        data = JSON.parse(data);
        if (Object.keys(this.QuoteForm).indexOf("SheetSize") >= 0) {
          this.QuoteForm.SheetSize.Value = data.SheetSizeHeight + "*" + data.SheetSizeWidth;
        }
        if (Object.keys(this.QuoteForm).indexOf("SetHeight") >= 0) {
          this.QuoteForm.SetHeight.Value = data.SetWidth;
        }
        if (Object.keys(this.QuoteForm).indexOf("PCSWidth") >= 0) {
          this.QuoteForm.SetWidth.Value = data.SetHeight;
        }
        if (Object.keys(this.QuoteForm).indexOf("SequentialMode") >= 0) {
          this.QuoteForm.SequentialMode.Value = data.SetX + "*" + data.SetY;
        }
        // for(var a=0;a<Object.keys(data).length;a++){
        //   alert(Object.keys(data)[a])
        //   if(Object.keys(this.QuoteForm).indexOf(Object.keys(data)[a])>=0){
        //     this.QuoteForm[Object.keys(data)[a]] = data[Object.keys(data)[a]]
        //   }
        // }
      }
    },
    openDrillStruct(name) {
      //钻孔结构
      console.log("钻孔结构", name);
      console.log("this.QuoteForm[name]", this.QuoteForm[name]);
      // this.QuoteForm[name].Value = '[{"index":1,"FromLayer":"1","ToLayer":"4","IsLaser":"0","HoleCount":"197","MinHoleSize":"0.3"}]'
      // window.external.DrillStruct( 'http://cam.pcbpp.com:8088/ipcb/sight/drillStructure?data=' + this.QuoteForm[name].Value);
      if (window.external && typeof window.external.DrillStruct != "undefined" && typeof window.external.DrillStruct != undefined) {
        window.external.DrillStruct(location.protocol + "//" + location.host + "/drillStructure?data=" + this.QuoteForm[name].Value);
      } else {
        window.open(location.protocol + "//" + location.host + "/drillStructure?data=" + this.QuoteForm[name].Value);
      }
    },
    openDrillStructCallBack(data) {
      //钻孔结构数据
      if (data) {
        this.QuoteForm.DrillStructs.Value = data;
        // alert(this.QuoteForm.DrillStructs.Value)
        this.$forceUpdate();
      }
    },
    // 搜索
    keydown(e) {
      if (e.ctrlKey && e.keyCode == 70) {
        //Ctrl + F
        $("#labelSearchInput").focus();
        // e.preventDefault ? e.preventDefault() : e.returnValue = false;
      }
    },
    search() {
      delay(() => {
        console.log("this.searchText", this.searchText);
        this.num = 0;
        this.indexNum = 0;
        this.query = this.searchText;
        var Reg = new RegExp(this.query, "i");
        if (this.query) {
          console.log("Reg1", Reg);
          $(".capacity #formDataElem .ant-form-item-label label").each(function (index, label) {
            if (label.innerText.match(Reg)) {
              console.log("label.innerHTML", label.innerHTML);
              label.innerHTML = label.innerText.replace(Reg, function (text) {
                return `<em class="searchPosElem" data-index="${index + 1}" >` + text + `</em>`;
              });
            } else {
              label.innerHTML = label.innerText.replace(label.innerText, function (text) {
                return `<span >` + text + `</span>`;
              });
            }
          });
        } else {
          $(".capacity #formDataElem .ant-form-item-label label").each(function (index, label) {
            console.log("label.innerHTML", label.innerHTML);
            label.innerHTML = label.innerText.replace(label.innerText, function (text) {
              return `<span >` + text + `</span>`;
            });
          });
        }

        this.getSearchList();
      }, 500);
    },
    getSearchList() {
      const num = document.getElementsByTagName("em").length;
      this.num = num;
      if (num != 0) {
        document.getElementsByTagName("em")[0].innerHTML = '<strong style="background-color: #ff9632" >' + this.searchText + "</strong>";
        let active = 0;
        let active1 = document.getElementsByTagName("em")[this.indexNum].dataset["index"] - 1;
        let count = 0;
        for (let index = 0; index < this.QuoteDataList.length; index++) {
          count += this.QuoteDataList[index].dtos.length;
          if (active1 <= count) {
            active = index;
            break;
          }
        }
        if (!this.activeNames.includes(active)) {
          this.activeNames = [];
          this.activeNames.push(active);
        }
        console.log("active", active, this.activeNames.includes(active), this.activeNames);
        // 滚动到第一个关键字位置
        document.getElementsByTagName("em")[0].scrollIntoView({
          block: "center",
          behavior: "smooth",
          inline: "start",
        });
      }
      console.log("this.num", this.num, this.indexNum);
    },
    // 下一个
    next() {
      if (this.num !== 0) {
        for (let i = 0; i < document.getElementsByTagName("em").length; i++) {
          document.getElementsByTagName("em")[i].innerHTML = this.searchText;
        }
        if (this.indexNum === this.num - 1) {
          this.indexNum = 0;
        } else {
          this.indexNum = this.indexNum + 1;
        }
        let active = 0;
        let active1 = document.getElementsByTagName("em")[this.indexNum].dataset["index"] - 1;
        let count = 0;
        for (let index = 0; index < this.QuoteDataList.length; index++) {
          count += this.QuoteDataList[index].dtos.length;
          if (active1 <= count) {
            active = index;
            break;
          }
        }
        if (!this.activeNames.includes(active)) {
          this.activeNames = [];
          this.activeNames.push(active);
        }
        document.getElementsByTagName("em")[this.indexNum].innerHTML = '<strong style="background-color: #ff9632">' + this.searchText + "</strong>";
        console.log("active", document.getElementsByTagName("em")[this.indexNum], [this.indexNum]);
        document.getElementsByTagName("em")[this.indexNum].scrollIntoView({
          block: "center",
          behavior: "smooth",
          inline: "start",
        });
      }
    },
    // 上一个
    prev() {
      if (this.num !== 0) {
        for (let i = 0; i < document.getElementsByTagName("em").length; i++) {
          document.getElementsByTagName("em")[i].innerHTML = this.searchText;
        }
        if (this.indexNum === 0) {
          this.indexNum = this.num - 1;
        } else {
          this.indexNum = this.indexNum - 1;
        }
        document.getElementsByTagName("em")[this.indexNum].innerHTML = '<strong style="background-color: #ff9632">' + this.searchText + "</strong>";
        let active = 0;
        let active1 = document.getElementsByTagName("em")[this.indexNum].dataset["index"] - 1;
        let count = 0;
        for (let index = 0; index < this.QuoteDataList.length; index++) {
          count += this.QuoteDataList[index].dtos.length;
          if (active1 <= count) {
            active = index;
            break;
          }
        }
        if (!this.activeNames.includes(active)) {
          this.activeNames = [];
          this.activeNames.push(active);
        }
        document.getElementsByTagName("em")[this.indexNum].scrollIntoView({
          block: "center",
          behavior: "smooth",
          inline: "start",
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-input {
  padding-left: 8px !important;
}
/deep/.ant-select-selection__rendered {
  color: rgb(48, 48, 48);
}
/deep/.ant-input {
  color: rgb(48, 48, 48);
}
/deep/.ant-select-selection__choice {
  color: rgb(48, 48, 48) !important;
}
/deep/.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #ff9900;
}
/deep/.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #ff9900;
}
/deep/.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #ff9900;
  border-color: #ff9900;
}
/deep/.ant-input:hover {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-input:focus {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-select-selection:hover {
  border-color: #ff9900;
}
/deep/.ant-select-open .ant-select-selection {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-select-focused {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-select-enabled {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-select-selection:focus {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-select-selection:active {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
.ant-select-selection:active {
  border-color: #ff9900;
  box-shadow: 0 0 0 !important;
  -webkit-box-shadow: 0 0 0;
}
/deep/.ant-checkbox-input:hover {
  border-color: #ff9900;
}
/deep/.ant-checkbox-checked::after {
  border-color: #ff9900;
}
.content {
  /deep/.ant-form label {
    font-size: 13px !important;
    font-weight: 500;
  }
  /deep/.ant-input {
    font-size: 12px !important;
  }
  /deep/.ant-select {
    font-size: 12px !important;
  }
  &::-webkit-scrollbar {
    //整体样式
    width: 10px !important; //y轴滚动条粗细
    height: 10px !important; //x轴滚动条粗细
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #a5a5a5;
  }
  overflow-y: scroll;
}
/deep/.ant-select-dropdown-menu-item {
  font-size: 12px;
  padding: 0.5%;
}
/deep/.ant-input-affix-wrapper .ant-input-suffix {
  right: 4px;
}
/deep/.ant-select-selection__clear {
  right: 4px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 10px;
}
/deep/.ant-select-selection__rendered {
  margin-left: 8px;
}

/deep/.ant-select-selection--single {
  height: 21px;
}
/deep/.ant-select-selection__rendered {
  line-height: 21px;
}
/deep/.ant-input {
  height: 21px;
}
/deep/.ant-select-selection--multiple {
  min-height: 21px;
}
/deep/.ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
  height: 15px;
  line-height: 14px;
}
/deep/.ant-select-selection--multiple .ant-select-selection__choice {
  margin-right: 2px;
  padding: 0 10px 0 1px;
}
/deep/.ant-select-selection--multiple .ant-select-selection__choice__remove {
  right: 0;
}
/deep/.ant-select-allow-clear .ant-select-selection--multiple .ant-select-selection__rendered {
  margin-right: 2px;
}
// 多选样式溢出隐藏
// /deep/.sty {
//     .ant-select-selection__rendered{
//         height:25px;
//     }
//     ul{
//         display: flex;
//         overflow: hidden;
//     }
// }
// /deep/.test{
//     height:25px;
// }
/deep/.backsty {
  label {
    text-decoration: underline;
    color: blue;
  }
}
/deep/.backsty2 {
  label {
    text-decoration: underline;
    color: rgb(207, 11, 191);
  }
}
/deep/.red {
  label {
    color: red;
  }
}
/deep/.ant-form-item-label {
  padding: 0 0 5px !important;
}

/deep/.ant-form-item-children {
  min-height: 21px;
  display: block;
}
#header .labelSearch .pageChangeBtn {
  font-size: 14px;
  color: #bbb;
  font-weight: 500;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px 0 0;
  cursor: not-allowed;
}
#header .labelSearch .pageChangeBtn.action {
  color: #000;
  cursor: pointer;
}

/deep/.searchPosElem {
  background-color: #ff0;
  font-style: normal;
}
/deep/ .ant-form-item-label .action1 {
  color: #ff0;
  background-color: #f00;
}

@media screen and (min-width: 50px) {
  /deep/.ant-form-item-label label::after {
    display: contents !important;
    text-align: right !important;
  }
}

@media (max-width: 575px) {
  .ant-form-item {
    width: 100% !important;
    display: inline-block;
    margin-bottom: 0;
  }
  /deep/.ant-form-item-label {
    text-align: left !important;
    line-height: 21px !important;
  }
}
.header {
  width: 100%;
  height: 40px;
  position: fixed;
  top: 0;
  left: 0;
  padding: 8px 2px;
  box-sizing: border-box;
  border-bottom: 1px solid #d5d5d5;
  box-shadow: 0 2px 5px rgb(0 0 0 / 20%);
  z-index: 99;
  background: #d5e4f2;
  display: flex;
  .ant-btn {
    height: 21px;
    font-size: 12px;
    background: #01a279;
    i {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }
  }
  /deep/.ant-input {
    color: #000000;
    height: 21px;
    width: 110px;
    padding-right: 6px;
    padding-left: 6px;
  }
  /deep/.ant-input-affix-wrapper {
    width: 110px;
  }
}
.searchRight {
  float: right;
}
#formDataElem .form-label .searchPosElem {
  background-color: #ff0;
  font-style: normal;
}
#formDataElem .form-label .searchPosElem.action {
  color: #ff0;
  background-color: #f00;
}
/deep/.ant-form-item-label {
  line-height: 21px;
}
/deep/.ant-form-item-control {
  line-height: 21px;
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  height: 35px;
  padding: 7px 34px;
  background: -webkit-linear-gradient(#f9f9f9, #eeeff1);
}
/deep/.ant-btn-primary:hover {
  background-color: #02906c !important;
  border-color: #02906c !important;
}
.content {
  background: #d5e4f2;
  position: fixed;
  top: 40px;
  bottom: 40px;
  left: 0;
  right: 0;
  overflow: auto;
  &::-webkit-scrollbar {
    //整体样式
    width: 6px; //y轴滚动条粗细
    height: 6px;
  }
}
/deep/.ant-collapse-content {
  background: #d5e4f2;
  overflow: unset;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 5px;
}
// /deep/.ant-checkbox-checked .ant-checkbox-inner {
//     background-color: #ff9900;
//     border-color: #ff9900;
// }
// /deep/.ant-input:hover {
//     border-color: #ff9900;
// }
// /deep/.ant-select-selection:hover {
//     border-color: #ff9900;
// }
// /deep/.ant-checkbox-input:hover {
//     border-color: #ff9900;
// }
// /deep/.ant-checkbox-checked::after{
//     border-color: #ff9900;
// }
/deep/.ant-btn-primary[disabled] {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  text-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
/deep/.ant-form-item-label {
  // padding:0 8px 0 0!important;
  // line-height: 40px!important;
}
/deep/.ant-form-item-label > label::after {
  content: ":";
  position: absolute;
  top: -0.5px;
  margin: 0 8px 0 2px;
}
.buton {
  height: 40px;
  border-top: 1px solid #d5d5d5;
  position: fixed;
  width: 100%;
  // background: #C4CAD9;
  background: #d5e4f2;
  padding: 7px;
  bottom: 0;
  .ant-btn {
    height: 26px;
    font-size: 12px;
    background: #01a279;
  }
}
.labelSearch {
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  left: 100px;
  white-space: nowrap;
}
.labelSearch input[type="text"] {
  width: 80px !important;
  font-size: 13px;
  color: #000;
  vertical-align: middle;
  border: none;
  padding: 0 0 0 0;
  box-sizing: border-box;
  outline: none;
}
.labelSearch .layui-icon-search {
  font-size: 16px;
  color: #000;
  position: absolute;
  left: 6px;
  top: 4px;
  z-index: 999;
}
.labelSearch .page {
  font-size: 12px;
  color: #000;
  display: inline-block;
  vertical-align: middle;
  padding: 0 5px;
  margin-bottom: 5px;
}
.labelSearch .pageChangeBtn {
  font-size: 14px;
  color: #bbb;
  font-weight: 500;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px 5px 0;
  cursor: not-allowed;
}
.labelSearch .pageChangeBtn.action {
  color: #000;
  cursor: pointer;
}
.capacity .ant-form-item {
  width: 250px;
  display: inline-block;
  margin-bottom: 0 !important;
}
.table-filter-view {
  width: auto;
  min-width: 150px;
  max-width: 200px;
  background: #f3f3f4;
  border-top: 1px solid #d2d2d2;
  box-shadow: 1px 1px 10px #d2d2d2;
  position: absolute;
  top: 100%;
  left: 0px;
  z-index: 100000;
  ul {
    padding: 0;
    list-style: none;
    li {
      margin-left: 4px;
    }
  }

  /deep/.ant-input {
    height: 22px;
    width: 60px;
  }
}
.filter-input-view {
  min-width: 120px !important;
}
</style>
