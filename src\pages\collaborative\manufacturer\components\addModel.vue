<template>
  <a-modal
    centered
    :title="form.id ? '编辑' : '新增'"
    :width="640"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="工厂名称" ref="company" prop="company">
          <a-input v-model="form.company" autoFocus placeholder="工厂名称" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { addSelectFact } from "@/services/supplier/index";
export default {
  props: {
    suppId: {
      type: String,
      default() {
        return "";
      },
    },
  },
  data() {
    return {
      autofocus: false,
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {},
      rules: {
        company: [{ required: true, message: "名称必须填写", trigger: "blur" }],
      },
      fileList: [],
      uploading: false,
      model: "",
    };
  },
  methods: {
    openModal(model) {
      this.visible = true;
      this.model = model;
      this.form = {
        id: model.id,
        company: model.company,
      };
    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate(valid => {
        if (valid) {
          let params = {
            id: this.model.id,
            ...this.form,
          };
          addSelectFact(params)
            .then(res => {
              if (res.success) {
                this.visible = false;
                form.resetFields();
                this.$message.info("操作成功");
                this.$emit("ok");
              }
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped></style>
