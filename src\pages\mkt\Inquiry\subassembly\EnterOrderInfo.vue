<!-- 市场管理 - 订单询价 - 新增弹窗 -->
<template>
  <div ref="SelectBox" @dragover.prevent @drop.prevent="onDrop">
    <a-form-model
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      :model="enterOrderForm"
      :rules="user.factoryId == 65 ? rules65 : rules"
      ref="ruleForm"
    >
      <template v-if="user.factoryId == 58 || user.factoryId == 59 || user.factoryId == 67">
        <a-form-model-item label="接单工厂" ref="TradeType" prop="TradeType">
          <a-select v-model="TradeType" @change="$emit('getSupplier', TradeType)">
            <a-select-option v-for="(item, index) in mapKey(factoryList)" :key="index" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </template>
      <a-form-model-item label="客户代码" ref="custNo" prop="custNo">
        <a-select
          placeholder="请选择客户代码"
          :getPopupContainer="() => this.$refs.SelectBox"
          v-model="enterOrderForm.custNo"
          :dropdownMatchSelectWidth="false"
          showSearch
          v-focus-next-on-enter="'input2'"
          ref="input1"
          :autoFocus="autoFocus"
          optionFilterProp="children"
          @popupScroll="handlePopupScroll"
          allowClear
          @search="supValue"
          @change="custchange()"
        >
          <a-select-option v-for="(item, index) in frontDataZSupplier" :key="index" :value="item.custNo" :item="item">
            {{ item.custNo }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <template v-if="checkPermission('MES.MarketModule.OrderManagement.WritePoNumJjJq')">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="数量" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" ref="num" prop="num">
              <a-input v-model="enterOrderForm.num" v-focus-next-on-enter="'input3'" ref="input2" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="交货单位" :label-col="{ span: 8 }" :wrapper-col="{ span: 12 }" ref="delType" prop="delType">
              <a-select
                v-model="enterOrderForm.delType"
                showSearch
                allowClear
                optionFilterProp="lable"
                :getPopupContainer="() => this.$refs.SelectBox"
              >
                <a-select-option key="1" value="PCS" lable="PCS">PCS</a-select-option>
                <a-select-option key="2" value="SET" lable="SET">SET</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-form-model-item label="客户PO">
          <a-input v-model="enterOrderForm.custPo" v-focus-next-on-enter="'input4'" ref="input3" />
        </a-form-model-item>
        <a-form-model-item label="加急">
          <a-checkbox v-model="enterOrderForm.isJiaji" ref="input4" />
        </a-form-model-item>
        <a-form-model-item label="订单来源">
          <a-select
            v-model="enterOrderForm.orderSource"
            v-focus-next-on-enter="'input6'"
            ref="input5"
            placeholder="请选择订单来源"
            showSearch
            allowClear
          >
            <a-select-option value="电商">电商</a-select-option>
            <a-select-option value="线下">线下</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="文件名" prop="pcbFileName">
          <a-input v-model="enterOrderForm.pcbFileName" ref="input6"> </a-input>
        </a-form-model-item>
        <!-- <a-form-model-item label="交货单位"> 
        <a-select v-model="enterOrderForm.delType"  placeholder="交货单位" showSearch allowClear optionFilterProp="lable" :getPopupContainer="()=>this.$refs.SelectBox">
          <a-select-option   key="1" value="PCS" lable="PCS">PCS</a-select-option>
          <a-select-option   key="2" value="SET" lable="SET">SET</a-select-option>
        </a-select>
        </a-form-model-item> -->
        <!-- <a-form-model-item label="交期">
          <a-date-picker  format="YYYY-MM-DD" placeholder="请选择交期" @change="TimeChange" style="width:266px;"> </a-date-picker>
        </a-form-model-item> -->
      </template>
      <template v-if="checkPermission('MES.MarketModule.OrderManagement.WriteIsOrderOff')">
        <a-form-model-item label="是否需要下单">
          <a-checkbox v-model="enterOrderForm.isNeedOrderOff" />
        </a-form-model-item>
      </template>
      <template v-if="checkPermission('MES.MarketModule.OrderManagement.IsCreateProOrderNo')">
        <a-form-model-item label="生成生产型号">
          <a-checkbox v-model="enterOrderForm.isCreateProOrderNo" />
        </a-form-model-item>
      </template>
      <a-upload
        :accept="acceptshow ? '.zip,.rar,.7z' : ''"
        :multiple="true"
        :file-list="fileList"
        :directory="!acceptshow"
        :customRequest="downloadFilesCustomRequest"
        :before-upload="beforeUpload"
        @change="handleChange"
        :openFileDialogOnClick="enterOrderForm.custNo ? true : false"
      >
        <!-- downloadFilesCustomRequestAA -->
        <div style="height: 120px" @click="upfile">
          <a-button
            v-if="type == '1'"
            style="margin-left: 10%; margin-top: 40px"
            @click="upfile"
            ref="input6"
            :disabled="enterOrderForm.custNo ? false : true"
          >
            <a-icon type="upload" />上传文件
          </a-button>
          <a-button
            v-if="type != '1'"
            style="margin-left: 10%; margin-top: 40px"
            @click="upfile"
            ref="input6"
            :disabled="enterOrderForm.custNo ? false : true"
          >
            <a-icon type="upload" />替换文件
          </a-button>
        </div>
      </a-upload>
      <div style="padding-left: 5px">
        <div v-for="(file, index) in fileList" :key="file.uid">
          <span v-if="mmessage[index]" style="font-size: 16px; color: red">{{ file.name }}:{{ mmessage[index] }}</span>
        </div>
      </div>
      <div class="btnStyle">
        <a-button @click="reportHandleCancel" style="margin-right: 10px; margin-top: 10px">取消</a-button>
        <a-button type="primary" :disabled="disabled ? true : false" @click="debouncedhandleOk1()" style="margin-right: 10px; margin-top: 10px"
          >保存</a-button
        >
        <a-button type="primary" :disabled="disabled ? true : false" @click="debouncedhandleOk1('ok')" style="margin-right: 16px; margin-top: 10px"
          >确认</a-button
        >
      </div>
    </a-form-model>
  </div>
</template>
<script>
import { riskwarning } from "@/services/gongju/RiskWarning.js";
import { mapState } from "vuex";
import { checkPermission } from "@/utils/abp";
import {
  upLoadEnquiryFile,
  upLoadEnquiryFileV2,
  mktCustNo,
  upLoadEnquiryFileV3,
  upLoadEnquiryFileV4,
  checkorderfile,
  inquirycheckorderfile,
} from "@/services/mkt/Inquiry.js";
import { terminalCusts } from "@/services/mkt/CustInfoNew";
import Cookie from "js-cookie";
import { type } from "os";
import axios from "axios";
import { status } from "nprogress";
export default {
  name: "EnterOrderInfo",
  props: ["selectedData", "type", "supList", "frontDataZSupplierf", "factoryList"],
  data() {
    return {
      mmessage: [],
      acceptshow: false,
      mmessage1: [],
      autoFocus: false,
      enterOrderForm: {
        proOrderNo: "", // 本场编码
        terminalCust: undefined,
        custNo: "", // 客户代码
        num: "", // 交货数
        delType: "", // 交货单位
        custPo: "", // 客户PO
        orderSource: "", // 订单来源
        pcbFileName: "", // 文件名
        isJiaji: false, // 加急
        PcbFilePath: "", // 文件地址
        isNeedOrderOff: false,
        isCreateProOrderNo: false,
        PcbFileData: [],
        MD5Code2: [],
        pcbName: [],
        customerModel: "", //更改后文件名
        // DeliveryDateStr:undefined, // 交期
      },
      rules65: {
        custNo: [{ required: true, message: "请选择客户代码", trigger: "blur" }],
        num: [{ required: true, message: "请填写交货数量", trigger: "blur" }],
        delType: [{ required: true, message: "请选择交货单位", trigger: "blur" }],
      },
      rules: {
        custNo: [{ required: true, message: "请选择客户代码", trigger: "blur" }],
      },
      disabled: true,
      mktCustNoInfo: {},
      // supList: [], //从后端查询的所有数据（不会改变）
      frontDataZSupplier: [], // 供应商 100条数据的集合
      sourceOwnerSystems: [], // 供应商名称的集合（过滤）
      treePageSize: 20,
      scrollPage: 1,
      valueData: undefined,
      fileList: [],
      isFileType: false,
      valueData2: undefined,
      supList2: [], //从后端查询的所有数据（不会改变）
      TradeType: null,
      frontDataZSupplier2: [], // 供应商 100条数据的集合
      sourceOwnerSystems2: [], // 供应商名称的集合（过滤）
    };
  },
  created() {
    this.debouncedhandleOk1 = this.debounce(this.handleOk1, 500);
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  watch: {
    frontDataZSupplierf(val) {
      this.frontDataZSupplier = val;
    },
  },
  methods: {
    checkPermission,
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(key => {
          return { value: Number(key), label: data[key] };
        });
      }
    },
    onDrop(e) {
      e.preventDefault();
    },
    TimeChange(value, dateString) {
      const now = new Date();
      let hours = now.getHours();
      if (hours < 10) {
        hours = "0" + hours;
      }
      let minutes = now.getMinutes();
      if (minutes < 10) {
        minutes = "0" + minutes;
      }
      let seconds = now.getSeconds();
      if (seconds < 10) {
        seconds = "0" + seconds;
      }
      this.enterOrderForm.DeliveryDateStr = dateString + " " + hours + ":" + minutes + ":" + seconds;
    },
    reportHandleCancel() {
      this.$refs.ruleForm.resetFields();
      this.$emit("reportHandleCancel");
    },
    handleOk1(type) {
      if (!this.enterOrderForm.custNo) {
        this.$message.warning("请选择客户代码");
        return;
      }
      this.$emit("handleOk1", type);
    },
    calculateFileMD5(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
          const fileContent = new Uint8Array(e.target.result); // 将 ArrayBuffer 转换为 Uint8Array
          const hash = this.$md5(fileContent); // 假设 $md5 支持二进制数据
          resolve(hash);
        };
        reader.onerror = err => {
          reject(err);
        };
        reader.readAsArrayBuffer(file); // 使用 readAsArrayBuffer 读取文件
      });
    },
    //分段上传MD5
    async downloadFilesCustomRequest(data) {
      let GUID;
      let shardSize;
      let shardCount;
      let next;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      let FileMD5 = await this.calculateFileMD5(data.file);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("CustNo", this.enterOrderForm.custNo);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileMD5", FileMD5); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      let params = {
        custNo: this.enterOrderForm.custNo,
        fileName: data.file.name,
        MD5Code: GUID,
      };
      await inquirycheckorderfile(params).then(res => {
        if (res.code) {
          if (res.message) {
            if (confirm("" + data.file.name + ":" + res.message)) {
              next = true;
            }
          } else {
            next = true;
          }
        } else {
          next = false;
          data.onError(res.message);
        }
      });
      if (next == true) {
        for (var i = 0; i < shardCount; i++) {
          const start = i * shardSize;
          const end = Math.min(size, start + shardSize);
          formData.set("FileSeg", data.file.slice(start, end));
          formData.set("startpos", i * shardSize);
          let headers = {};
          headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
          headers["Content-Type"] = "text/html;charset=UTF-8";
          await axios({
            url: process.env.VUE_APP_API_BASE_URL + "/api/app/order-inquiry/up-load-enquiry-file-v2", // 接口地址
            method: "post",
            data: formData,
            headers: headers, // 请求头
          }).then(res => {
            if (res.code == 1) {
              if (i == shardCount - 1) {
                data.onSuccess(res.data);
              }
            } else {
              data.onError(res.message);
              i = shardCount;
            }
          });
        }
      }

      // await upLoadEnquiryFileV2(formData).then(res => {
      //   if (res.code == 1) {
      //     if (res.message) {
      //       if (confirm("" + data.file.name + ":" + res.message)) {
      //         if(i==shardCount-1){
      //           data.onSuccess(res.data);
      //         }
      //       } else {
      //         data.onError(res.message);
      //         i=shardCount
      //       }
      //     } else {
      //       if(i==shardCount-1){
      //         data.onSuccess(res.data);
      //       }
      //     }
      //   } else {
      //     data.onError(res.message);
      //     i=shardCount
      //   }

      // })
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    custchange() {
      this.fileList = [];
      this.mmessage = [];
      this.enterOrderForm.pcbFileName = "";
      this.enterOrderForm.PcbFileData = [];
      this.MD5Code2 = [];
      this.pcbName = [];
      this.disabled = true;
      this.risk();
    },
    risk() {
      if (this.enterOrderForm.custNo) {
        const selectedItem = this.frontDataZSupplier.find(item => item.custNo == this.enterOrderForm.custNo);
        let params = {
          linkType: 1,
          custNo: this.enterOrderForm.custNo,
          JoinFactoryId: selectedItem.joinFactoryId,
          Status: 1,
        };
        let str = "";
        riskwarning(params).then(res => {
          if (res.code && res.data.length) {
            res.data.forEach((item, index) => {
              str += index + 1 + "." + item.content + "\n";
            });
            if (confirm("" + str)) {
              //
            } else {
              this.enterOrderForm.custNo = "";
            }
          }
        });
      }
      this.supValue();
    },
    async downloadFilesCustomRequestAA(data) {
      const formData = new FormData();
      const str = data.file.name;
      // 使用 DOMParser 解析字符串
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      // 获取解析后的文本内容
      const parsedText = doc.body.textContent;
      // 将解析后的文本内容中的空白字符替换为空格
      const replacedText = parsedText.replace(/\s+/g, " ");
      let GUID = this.guid(data.file.name, data.file.lastModified, data.file.size, data.file.type);
      const renamedFile = new File([data.file], replacedText, { type: data.file.type });
      formData.append("file", renamedFile);
      await upLoadEnquiryFile(formData, this.enterOrderForm.custNo, GUID).then(res => {
        if (res.code == 1) {
          if (res.message) {
            if (confirm("" + data.file.name + ":" + res.message)) {
              data.onSuccess(res.data);
            } else {
              data.onError(res.message);
            }
          } else {
            data.onSuccess(res.data);
          }
        } else {
          data.onError(res.message);
        }
      });
    },
    beforeUpload(file) {
      if (!this.enterOrderForm.custNo) {
        this.$message.error("请先选择客户代码");
        this.isFileType = false;
        return false;
      }
      this.isFileType =
        file.name.toLowerCase().indexOf(".zip") != -1 ||
        file.name.toLowerCase().indexOf(".rar") != -1 ||
        file.name.toLowerCase().indexOf(".7z") != -1;
      if (!this.isFileType) {
        this.$message.error("文件上传只支持.rar或.zip或.7z格式文件");
        return false;
      }
      const filesize = Number(file.size / 1024 / 1024) < 150;
      if (!filesize) {
        this.isFileType = false;
        this.$message.error("文件大小不能超过150M");
        return false;
      }
      const uploadedFileNames = this.fileList.map(file => file.name);
      if (uploadedFileNames.includes(file.name)) {
        this.$message.error("已存在该文件名，请不要重复上传！");
        const index = this.fileList.findIndex(item => item.name === file.name);
        console.log(index, "index");
        if (index !== -1) {
          this.fileList.splice(index, 1); // 手动删除重复文件
        }
      }
      return true;
    },
    SpecialMaterial() {
      this.isJiaji = !this.isJiaji;
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    //下拉框下滑事件
    handlePopupScroll(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.supList.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.supList.length;
          }
          // 判断是否有搜索
          if (this.valueData) {
            this.supList.forEach((item, index) => {
              if (item.custNo.indexOf(this.valueData) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.supList.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    supValue(value) {
      if (value) {
        let that = this;
        that.valueData = value.toUpperCase();
        let arr = that.supList.filter(m => m.custNo.toUpperCase().indexOf(value.toUpperCase()) != -1);
        if (arr.length) {
          that.frontDataZSupplier = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier = [];
        }
      } else {
        this.valueData = undefined;
        this.frontDataZSupplier = this.supList.slice(0, 20);
      }
    },
    upfile() {
      this.acceptshow = true;
    },
    handleChange({ fileList }, data, xhr) {
      if (this.isFileType) {
        if (this.type != 1 && fileList.length > 1) {
          this.$message.error("替换文件只能上传一个文件");
          fileList.splice(1);
        }
        this.fileList = fileList;
        this.enterOrderForm.PcbFileData = [];
        this.MD5Code2 = [];
        this.pcbName = [];
        if (this.fileList.length == 0) {
          this.mmessage = [];
          this.enterOrderForm.pcbFileName = "";
          this.enterOrderForm.PcbFileData = [];
          this.MD5Code2 = [];
          this.pcbName = [];
        } else {
          var arr = [];
          const fileName = this.fileList[0].name;
          const lastDotIndex = fileName.lastIndexOf(".");
          if (lastDotIndex !== -1 && this.fileList.length == 1) {
            this.enterOrderForm.pcbFileName = fileName.substring(0, lastDotIndex);
          } else {
            this.enterOrderForm.pcbFileName = "";
          }
          arr = this.fileList.filter(item => {
            return item.status == "done";
          });
          this.mmessage = [];
          for (var a = 0; a < arr.length; a++) {
            this.enterOrderForm.PcbFileData.push(arr[a].response);
            this.MD5Code2.push(this.guid(arr[a].name, arr[a].lastModified, arr[a].size, arr[a].type));
            this.pcbName.push(arr[a].name);
          }
          if (arr.length == this.fileList.length) {
            this.disabled = false;
          } else {
            this.disabled = true;
            this.mmessage = [];
            for (var b = 0; b < this.fileList.length; b++) {
              if (this.fileList[b].error) {
                this.mmessage[b] = this.fileList[b].error;
              }
            }
          }
        }
        this.acceptshow = false;
      }
    },
    filter(inputValue, path) {
      // console.log(inputValue,path)
      return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
    },
    handlePopupScroll2(e) {
      const { target } = e;
      const scrollHeight = target.scrollHeight - target.scrollTop;
      const clientHeight = target.clientHeight;
      // 下拉框不下拉的时候
      if (scrollHeight === 0 && clientHeight === 0) {
        this.scrollPage = 1;
      } else {
        // 当下拉框滚动条到达底部的时候
        if (scrollHeight < clientHeight + 5) {
          this.scrollPage = this.scrollPage + 1;
          const scrollPage = this.scrollPage; // 获取当前页
          const treePageSize = this.treePageSize * (scrollPage || 1); // 新增数据量
          const newData = []; // 存储新增数据
          let max = ""; // max 为能展示的数据的最大条数
          if (this.terminalCustList2.length > treePageSize) {
            // 如果总数据的条数大于需要展示的数据
            max = treePageSize;
          } else {
            // 否则
            max = this.terminalCustList2.length;
          }
          // 判断是否有搜索
          if (this.valueData2) {
            this.terminalCustList2.forEach((item, index) => {
              if (item.valueMember.indexOf(this.valueData2) != -1) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          } else {
            this.terminalCustList2.forEach((item, index) => {
              if (index < max) {
                // 当data数组的下标小于max时
                newData.push(item);
              }
            });
          }

          this.frontDataZSupplier2 = newData; // 将新增的数据赋值到要显示的数组中
        }
      }
    },
    terminalChange() {
      this.$forceUpdate();
    },
    supValue1(value) {
      if (value) {
        let that = this;
        that.valueData2 = value;
        let arr = that.terminalCustList2.filter(m => m.valueMember.indexOf(value) != -1);
        if (arr.length) {
          that.frontDataZSupplier2 = arr.slice(0, 20);
        } else {
          that.frontDataZSupplier2 = [];
        }

        console.log("搜索", this.frontDataZSupplier2);
      } else {
        this.valueData2 = undefined;
        this.frontDataZSupplier2 = this.terminalCustLis2.slice(0, 20);
      }
    },
  },
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
  mounted() {
    if (this.user.factoryId == 59 || this.user.factoryId == 58) {
      this.TradeType = this.user.factoryId;
    }
    if (this.type != "1") {
      this.disabled = false;
    }
    this.frontDataZSupplier = this.frontDataZSupplierf;
    if (this.type == "1") {
      if (this.frontDataZSupplier.length == 1) {
        this.enterOrderForm.custNo = this.frontDataZSupplier[0].custNo;
        this.risk();
      } else {
        this.enterOrderForm.custNo = "";
      }
      this.enterOrderForm.num = "";
      this.enterOrderForm.delType = "";
      this.enterOrderForm.custPo = "";
      this.enterOrderForm.orderSource = "";
      this.enterOrderForm.pcbFileName = "";
      this.enterOrderForm.isJiaji = false;
      this.enterOrderForm.isNeedOrderOff = false;
      if (this.user.factoryId == 65) {
        this.enterOrderForm.isCreateProOrderNo = true;
      } else {
        this.enterOrderForm.isCreateProOrderNo = false;
      }
    } else {
      this.enterOrderForm.custNo = this.selectedData.custNo;
      this.enterOrderForm.num = this.selectedData.num;
      this.enterOrderForm.delType = this.selectedData.delType;
      this.enterOrderForm.custPo = this.selectedData.custPo;
      this.enterOrderForm.orderSource = this.selectedData.orderSource;
      this.enterOrderForm.pcbFileName = this.selectedData.customerModel;
      this.enterOrderForm.isJiaji = this.selectedData.isJiaji;
      this.enterOrderForm.isNeedOrderOff = this.selectedData.isNeedOrderOff;
      this.enterOrderForm.isCreateProOrderNo = this.selectedData.isCreateProOrderNo;
    }
  },
};
</script>
<style scoped lang="less">
/deep/.ant-upload {
  border: 1px dashed #ccc; /* 设置虚线边框 */
  height: 120px;
  display: block;
}
/deep/.ant-btn {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}

.picker {
  width: 226px;
}
.btnStyle {
  border-top: 1px solid #f0f0f0;
  display: flex;
  width: 100%;
  justify-content: flex-end;
}
.ant-form-item {
  margin-bottom: 0;
}
.ant-form-item-children {
  width: 180px;
  border-right: 0;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.ant-upload {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.avatar-uploader > .ant-upload {
  position: absolute;
  height: 32px;
}
.ant-upload-select-picture-card i {
  font-size: 12px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  color: #666;
}
</style>
