<template>
  <div class="contentInfo">
    <a-card :bordered="false">
      <div ref="SelectBox">
        <a-form-model layout="inline" :model="formData" v-if="!editFlag">
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="贴片点数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span class="tmp">{{ formData.pasterCount }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="插件点数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.pluginCount }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="贴片面数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.patchNumber == 0 ? "单面" : formData.patchNumber == 1 ? "双面" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="物料种类数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.materialKindNum }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="X-Ray测试" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span class="tmp">{{
                    formData.xRayTest == 0 ? "否" : formData.xRayTest == 1 ? "抽测" : formData.xRayTest == 2 ? "全测" : ""
                  }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="分板出货" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.splitPlateShipGoods == 0 ? "否" : formData.splitPlateShipGoods == 1 ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="三防漆喷涂" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.conformalCoatings == 0 ? "否" : formData.conformalCoatings == 1 ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="老化测试" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.agingTest == 0 ? "否" : formData.agingTest == 1 ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="功能测试" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span class="tmp">{{ formData.functionalTest == 0 ? "否" : formData.functionalTest == 1 ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="程序烧录" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.programBurn == 0 ? "否" : formData.programBurn == 1 ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="电子装联" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.endProductInstall == 0 ? "否" : formData.endProductInstall == 1 ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="洗板" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.washBoard == 0 ? "普洗" : formData.washBoard == 1 ? "精洗" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="点胶" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span class="tmp">{{ formData.fluidDispensing == 0 ? "否" : formData.fluidDispensing == 1 ? "是" : "" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="包装" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <span>{{ formData.packageType == 0 ? "常规气泡袋" : "静电屏蔽袋" }}</span>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="客供元器件" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <div class="editWrapper">
                  <span>{{ formData.custComponents }}</span>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="16">
              <a-form-model-item label="其它特别要求" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
                <div>{{ formData.pcbaSpecialRequire }}</div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <a-form-model layout="inline" :model="formData" v-if="editFlag">
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="贴片点数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-input v-model="formData.pasterCount"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="插件点数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-input v-model="formData.pluginCount"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="贴片面数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.patchNumber" style="width: 100%" allowClear>
                    <a-select-option :value="0">单面</a-select-option>
                    <a-select-option :value="1">双面</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="物料种类数" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-input v-model="formData.materialKindNum"> </a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="X-Ray测试" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.xRayTest" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">抽测</a-select-option>
                    <a-select-option :value="2">全测</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="分板出货" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.splitPlateShipGoods" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="三防漆喷涂" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.conformalCoatings" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="老化测试" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.agingTest" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="功能测试" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.functionalTest" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="程序烧录" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.programBurn" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="电子装联" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.endProductInstall" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="洗板" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.washBoard" style="width: 100%" allowClear>
                    <a-select-option :value="0">普洗</a-select-option>
                    <a-select-option :value="1">精洗</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="4">
              <a-form-model-item label="点胶" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.fluidDispensing" style="width: 100%" allowClear>
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="4">
              <a-form-model-item label="包装" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <div class="editWrapper">
                  <a-select v-model="formData.packageType" style="width: 100%" allowClear>
                    <a-select-option :value="0">常规气泡袋</a-select-option>
                    <a-select-option :value="1">静电屏蔽袋 </a-select-option>
                  </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="客供元器件" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <div class="editWrapper">
                  <a-select v-model="formData.custComponents" style="width: 39%" allowClear @change="custComponentsChange">
                    <a-select-option value="全部">全部</a-select-option>
                    <a-select-option value="部分">部分</a-select-option>
                  </a-select>
                  <a-input
                    v-model="custComponents"
                    placeholder="部分器件名称"
                    v-show="formData.custComponents == '部分'"
                    style="margin-left: 20px; width: 264px"
                  ></a-input>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="16">
              <a-form-model-item label="其它特别要求" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }" class="bbb">
                <a-textarea
                  v-model="formData.pcbaSpecialRequire"
                  :auto-size="{ minRows: 1, maxRows: 5 }"
                  style="width: 100%; min-height: 24px; line-height: 14px; margin-bottom: 0px"
                  placeholder="请输入其它特别要求"
                >
                </a-textarea>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </a-card>
  </div>
</template>
<script>
import { copy } from "clipboard";

export default {
  name: "OneStop",
  data() {
    return {
      formData: {},
      custComponents: "",
      copydata: {},
    };
  },
  props: ["editFlag", "proOrderInfoDto", "showData"],
  components: {},
  computed: {},
  watch: {},
  methods: {
    geteditdata() {
      if (this.formData.custComponents != "全部" && this.formData.custComponents != "部分") {
        this.custComponents = this.formData.custComponents;
        this.$nextTick(() => {
          this.formData.custComponents = "部分";
        });
      }
    },
  },
  custComponentsChange() {
    this.custComponents = "";
  },
  created() {},
  mounted() {
    this.formData = this.showData;
    // this.copydata = JSON.parse(JSON.stringify(this.showData))
  },
};
</script>
<style scoped lang="less">
.bbb {
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
/deep/ .tmp {
  word-break: keep-all;
  vertical-align: top;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 28%;
  display: inline-block;
}
/deep/ .tmp1 {
  word-break: keep-all;
  vertical-align: top;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 67%;
  display: inline-block;
}
/deep/.list-item-content {
  display: flex;
  border: 1px solid #dfdfdf;
}
/deep/.ant-list-vertical .ant-list-item-meta {
  margin-bottom: 0;
}
/deep/.ant-list-bordered .ant-list-item {
  padding: 4px;
  margin-bottom: 0;
}
/deep/.ant-list-vertical .ant-list-item-meta-title {
  margin-bottom: 4px;
}

/deep/.ant-form-item-label {
  line-height: 27px;
}
/deep/.ant-select-selection-selected-value {
  float: left;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 21px;
}
/deep/ .ant-select-selection__rendered {
  height: 24px;
}
/deep/.ant-select-selection {
  height: 24px;
}
/deep/ .ant-input {
  width: 177px;
  height: 24px;
}

/deep/.ant-input:not(:last-child) {
  padding-right: 18px;
}

/deep/.ant-input-suffix {
  right: 5px;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}

/deep/.ant-select-selection--multiple .ant-select-selection__choice {
  color: #000000;
}

/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
  padding-left: 10px;
}

/deep/.userStyle {
  .ant-select-selection {
    user-select: all;
  }
}

/deep/.zzz {
  .ant-form-item-label {
    width: 117px;
  }
  .ant-form-item-control {
    width: 1002px;
  }
  .ant-select {
    min-height: 24px !important;
    height: auto !important;

    .ant-select-selection {
      display: inline-block;
      height: auto !important;
      width: 100%;
    }

    .ant-select-selection-selected-value {
      overflow: inherit;
      white-space: inherit;
    }
  }
}
.div22 {
  /deep/.ant-form-item-label {
    width: 117px !important;
  }
  /deep/.ant-form-item-control {
    padding: 0;
    max-height: 45px !important;
    min-height: 29px !important;
    width: 1002px !important;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
/deep/.heightSty2 {
  .ant-form-item-label {
    width: 117px !important;
    height: 60px !important;
  }
  .ant-form-item-control {
    width: 1002px !important;
    height: 60px !important;
  }
}

/deep/.heightSty1 {
  .ant-form-item-label {
    width: 117px;
  }
  .ant-form-item-control {
    width: 1002px;
  }
  .ant-form-item-control-wrapper {
    min-height: 20px;

    .ant-form-item-control {
      height: 100% !important;

      .ant-form-item-children {
        min-height: 20px;
        overflow: inherit !important;
        text-overflow: unset !important;
        white-space: unset !important;
      }
      height: 100%;
      .ant-select {
        .ant-select-selection--multiple {
          min-height: 24px;
          padding-bottom: 0 !important;

          .ant-select-selection--multiple .ant-select-selection__choice {
            padding: 0 10px 0 5px;
          }

          .ant-select-selection__rendered > ul > li {
            height: 19px !important;
            margin-top: 0px;
            line-height: 20px !important;
            width: 11%;
          }

          .ant-select-selection__clear {
            top: 12px;
          }
        }
      }
    }
  }
}
.editWrapper1 {
  display: flex;
  align-items: center;
  height: 14px;
  .ant-select {
    width: 99%;
    height: 22px;
  }
  .ant-input {
    width: 99%;
    height: 22px;
    line-height: 22px;
    padding: 0 10px 0 7px;
  }
  .ant-input-number {
    width: 99%;
  }
}
/deep/.ant-select-selection-selected-value {
  span:nth-child(1) {
    display: none !important;
  }

  span:nth-child(2) {
    display: none !important;
  }

  span:nth-child(3) {
    width: 100% !important;
  }
}
/deep/.require {
  .ant-form-item-label > label {
    color: red !important;
  }
}

.contentInfo {
  height: 100%;
  .autoHeight {
    /deep/ .ant-form-item-control {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;

      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid #ddd;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: 500;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;
      }
    }

    .ant-card-body {
      padding: 0;

      .ant-form {
        border-left: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }

      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;
        .tmp {
          word-break: keep-all;
          vertical-align: top;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 100%;
          display: inline-block;
        }

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 22px;
        }

        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;
          color: #666;
          background-color: #f1f1f1;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;

          label {
            font-family: PingFangSC-Regular, Sans-serif;
            color: #000000;
            font-size: 14px;
            font-weight: 500;
          }
        }

        .ant-form-item-control-wrapper {
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;

          .ant-form-item-control {
            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }

            line-height: inherit;
            padding: 2px 4px;
            min-height: 29px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    }
  }
}

/deep/.ant-modal-body {
  .ant-form {
    border-left: 1px solid #ddd;
    border-top: 1px solid #ddd;

    .ant-form-item {
      margin: 0;
      width: 100%;
      display: flex;

      .editWrapper {
        display: flex;
        align-items: center;
        min-height: 40px;

        /deep/.ant-select {
          width: 120px;
          height: 34px;
        }

        /deep/ .ant-input {
          width: 120px;
          height: 34px;
        }
        .ant-input-number {
          width: 120px;
        }
      }

      .ant-form-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-family: PingFangSC-Regular, Sans-serif;
        color: #000000;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        background-color: #fafafa;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;

        label {
          font-family: PingFangSC-Regular, Sans-serif;
          color: #000000;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .ant-form-item-control-wrapper {
        font-family: PingFangSC-Regular, Sans-serif;
        color: #000000;
        font-size: 14px;
        font-weight: 500;

        .ant-form-item-control {
          .ant-form-item-children {
            display: block;
            min-height: 34px;
            line-height: 34px;

            .ant-checkbox-wrapper {
              height: 34px;
              line-height: 34px;
            }

            /deep/.ant-select-selection--single {
              height: 24px !important;
              line-height: 24px;
            }

            .ant-select-selection__rendered {
              line-height: 34px;
              height: 34px;
            }

            .ant-select {
              height: 34px !important;
              line-height: 34px;
            }
            .ant-select-selection-selected-value {
              line-height: 34px !important;
            }

            .ant-input {
              height: 34px;
              width: 100%;
            }
          }

          line-height: inherit;
          padding: 2px 10px;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
        }
      }
    }
  }
}
</style>
