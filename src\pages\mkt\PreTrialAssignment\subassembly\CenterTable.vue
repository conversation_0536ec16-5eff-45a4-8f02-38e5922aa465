<!-- 市场管理 - 预审分派-人员列表 -->
<template>
  <a-table
    style="word-break: break-all"
    :columns="columns"
    :dataSource="dataSource"
    :rowKey="'userLoginID'"
    class="centerTable"
    :scroll="{ y: 738, x: 380 }"
    :pagination="false"
    :customRow="onClickRow"
    :loading="producerTabLoading"
    :rowClassName="isRedRow"
  >
    <!-- :row-selection="{
        selectedRowKeys: selectedRowKeysArray,
        onChange: onSelectChange,
        type: 'radio',
      }" -->
    <!-- <div
        slot="filterDropdown"
        slot-scope="{
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
                column,
              }"
        style="padding: 8px"
    >
      <a-input
          v-ant-ref="(c) => (searchInput = c)"
          placeholder="请输入姓名"
          :value="selectedKeys[0]"
          style="width: 188px; margin-bottom: 8px; display: block"
          @change="
                  (e) => setSelectedKeys(e.target.value ? [e.target.value] : [])
                "
          @pressEnter="
                  () => handleSearch(selectedKeys, confirm, column.dataIndex)
                "
      />
      <a-button
          type="primary"
          icon="search"
          size="small"
          style="width: 90px; margin-right: 8px"
          @click="
                  () => handleSearch(selectedKeys, confirm, column.dataIndex)
                "
      >
        查找
      </a-button>
      <a-button
          size="small"
          style="width: 90px"
          @click="() => handleReset(clearFilters)"
      >
        重置
      </a-button>
    </div> -->
    <a-icon slot="filterIcon" slot-scope="filtered" type="search" :style="{ color: filtered ? '#ff9900' : '' }" />
    <template slot="stopCount" slot-scope="record">
      <span @click="stopCount(record)">{{ record.stopCount }}</span>
    </template>
    <template slot="finishCount" slot-scope="record">
      <span @click="finishCount(record)">{{ record.finishCount }}</span>
    </template>
    <template slot="customRender" slot-scope="text, record, index, column">
      <span v-if="searchText && searchedColumn === column.dataIndex">
        <!-- <template
                    v-for="(fragment, i) in text
                    .toString()
                    .split(
                      new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i')
                    )"
                >
                  <mark
                      v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                      :key="i"
                      class="highlight"
                  >{{ fragment }}</mark
                  >
                  
                  <template v-else>{{ fragment }}</template>
                </template> -->
      </span>
      <template v-else>
        {{ text }}
      </template>
      <a-tag color="orange" v-if="record.isLeave_" class="peopleTag"> 休 </a-tag>
    </template>
    <span slot="action" slot-scope="record">
      <a @click="showModal(record)" :style="record.realName_ == '合计' ? { display: 'none' } : ''">
        <a-tooltip>
          <template slot="title"> 编辑 </template>
          <a-icon type="edit" />
        </a-tooltip>
      </a>
    </span>
  </a-table>
</template>

<script>
import { projectOrderInfo } from "@/services/projectDisptch";

export default {
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    producerTabLoading: {
      type: Boolean,
      required: true,
    },
  },
  name: "CenterTable.vue",
  data() {
    return {
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: "13%",
          customRender: (text, record, index) => `${index + 1}`,
        },
        // {
        //   title: "组别",
        //   dataIndex: "",
        //   align: "left",
        //   width: '13%',
        // },
        {
          title: "姓名",
          dataIndex: "realName_",
          align: "left",
          width: "20%",
          scopedSlots: {
            customRender: "customRender",
          },
        },
        {
          title: "目标",
          dataIndex: "targetCount",
          align: "left",
          width: "15%",
        },
        // {
        //   title: "停留",
        //   align: "center",
        //   width: '12%',
        //   scopedSlots:{customRender:'stopCount'}
        // },
        // {
        //   title: "问客",
        //   dataIndex: "wenkeCounts",
        //   align: "center",
        //   width: '11%',
        // },
        {
          title: "已发",
          align: "left",
          width: "15%",
          dataIndex: "dispatch",
          // scopedSlots: {
          //    customRender: "finishCount",
          //  },
        },
        {
          title: "工厂",
          dataIndex: "facName",
          align: "left",
          width: "20%",
          ellipsis: true,
        },
        {
          title: "操作",
          align: "center",
          scopedSlots: { customRender: "action" },
        },
      ],
      searchInput: null,
      searchText: "",
      selectedRowKeysArray: [],
      selectedRowsData: [],
      userLoginID: "",
    };
  },
  created() {
    console.log("this.dataSource:", this.dataSource);
  },
  methods: {
    stopCount(record) {
      this.$emit("stopCount", record);
    },
    finishCount(record) {
      this.$emit("finishCount", record);
    },
    // onSelectChange(selectedRowKeys, selectedRows) {
    //   this.selectedRowKeysArray = selectedRowKeys;
    //   this.selectedRowsData = selectedRows[0]
    //   console.log('this.selectedRowKeysArray,选',this.selectedRowKeysArray)
    // },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.userLoginID);
            this.$emit("getProducerInfo", record.userLoginID); // 点击请求用户详情
            this.$emit("assignPeopleChange", record); // 更新父组件的值
            this.userLoginID = record.userLoginID;
            // this.selectedRowKeysArray = keys;
            // this.selectedRowsData = record
            // console.log('this.selectedRowKeysArray,点',this.selectedRowKeysArray)
          },
        },
      };
    },
    handleSearch(selectedKeys, confirm, dataIndex) {
      confirm();
      this.searchText = selectedKeys[0];
      this.searchedColumn = dataIndex;
    },

    handleReset(clearFilters) {
      clearFilters();
      this.searchText = "";
    },
    showModal(record) {
      this.$emit("geteditInfodata", record);
    },
    isRedRow(record) {
      if (record.isLeave_ == true && record.userLoginID == this.userLoginID) {
        // 是否请假
        return "rowSty rowBackgroundColor";
      } else if (record.userLoginID == this.userLoginID) {
        return "rowBackgroundColor";
      } else if (record.isLeave_) {
        return "rowSty";
      }
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
/deep/.ant-table-row:last-child {
  .contentTabAction {
    display: none;
  }
}
/deep/.ant-table-row:first-child {
  .contentTabAction {
    display: inline-block !important;
  }
}
.min-table {
  /deep/ .ant-table-body {
    height: 764px;
  }
}
/deep/.userStyle {
  user-select: all !important;
}
.centerTable {
  /deep/.rowSty {
    td {
      color: #dc143c;
    }
  }
  /deep/ .rowBackgroundColor {
    td {
      background: rgb(223, 220, 220);
    }
  }
  .peopleTag {
    position: absolute;
    font-size: 12px;
    font-weight: 600;
    left: 34px;
    padding: 0 2px;
  }
  /deep/ .ant-table-body {
    .ant-table-tbody {
      .ant-table-row:first-child {
        .contentTabAction {
          display: none;
        }
      }
    }
    // min-height: 330px;
  }
}
</style>
