<!-- 工程管理 - 工程制作 - 返修记录-->
<template>
  <div class='table'>
    <div class='leftBox'>      
      <p style='text-align:center;border: 1px solid #d5cdcd;margin-bottom: 0;background: #FAFAFA;color: black;'>返修问题</p>
      <a-table
          :columns="columns1"
          bordered
          rowKey="id"
          :pagination="false"
          :data-source="RepairRecordData"
          :customRow="onClickRow"
          :rowClassName="isRedRow"
          :orderListTableLoading="orderListTableLoading" 
      >        
      </a-table>        
    </div>
    <div class='rightBox'>
      <p style='text-align:center;border: 1px solid #d5cdcd;margin-bottom: 0;background: #FAFAFA;color: black'>图</p>
      <div >
        <a-spin :spinning="imageLoading" style="height: 100%;width: 100%">
          <a-empty v-if="!imgData.length>0"/>
          <div  style="height:100px;width:260px;margin-top:10px;margin-right:10px;" v-for="(item,index) in imgData" :key='index' v-viewer >
            <img :src='item' style="height:100%;width:100%;" />            
          </div>          
        </a-spin>
      </div>       
    </div>
    
  </div>
</template>

<script>

const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: 30,
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "返修问题",
    dataIndex: "conent",
    width: 180,
    ellipsis: true,
    align: "left",
    
  },
  
]
export default {
    name:'RepairRecordInfo',
    props:['RepairRecordData'],
  created(){
   // console.log('this.RepairRecordData:',this.RepairRecordData) 

  },

  data() {
    return {
      columns1,
      isSpecistack: false,
      orderListTableLoading:false,
      orderListTableLoading1:false,
      imgData:[],
      selectedRowKeysArray:[],
      imageLoading:false,
     
    };
  },
  
  methods: {  
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;            
            var str = record.picUrl 
            if(record.picUrl != ''){
               this.imgData = str.split(',')
            }else{
              this.imgData = []
            }
            // console.log('imgData:',this.imgData)                
          },
        }
      }
    },  
    isRedRow(record){
      if (record.id == this.selectedRowKeysArray[0]) {
        return 'rowBackgroundColor'
      }      
    }, 
  },

};
</script>
<style scoped lang='less'>

.ant-modal-body{
  padding: 0!important;
}
.table{
  min-height: 150px;
  max-height: 500px;
  display:flex;
/deep/  .leftBox{
    width:40%;
    overflow: auto;
    .rowBackgroundColor {
      background: #aba5a5;
    }    
    .ant-table-thead {
      tr{
        th{
          padding: 0;
        }
      }
    } 
    .ant-form-item{
      margin-bottom: 0;
    }
   .ant-table-tbody{
     tr{
       td{
         padding: 4px 4px;
       }
     }
     .ant-table-row:hover{
      td{
        background: #aba5a5;
      }
    }
   }
  }
/deep/  .rightBox{
    width:60%;
    margin-left:10px;
    .ant-spin-container{
      display:flex;
      flex-wrap: wrap;
      .ant-empty{
        margin: 10px auto;
        // margin-top:10px;
        height:90px;
      }
    }
  }
}
</style>