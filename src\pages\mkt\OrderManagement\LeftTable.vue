<!-- 市场管理 - 订单管理- 列表 -->
<template>
  <div ref="tableWrapper">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :customRow="customRow"
      :scroll="{ x: 1270, y: 737 }"
      :pagination="pagination"
      class="textpa"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      @change="handleTableChange"
      :rowClassName="isRedRow"
    >
      <template slot="sales" slot-scope="text, record">
        <a style="color: #428bca" type="primary" @click="$emit('goDetail1', record)">销售</a>
        <a-divider type="vertical" />
        <a style="color: #428bca" type="primary" @click="$emit('getDetailInfo', record.id)">预审</a>
      </template>
      <template slot="isNewCust" slot-scope="text, record">
        <a-checkbox v-model="record.isNewCust"></a-checkbox>
      </template>
      <template slot="Action" slot-scope="text, record">
        <a-popover :open="noteVisible" title="预审确认与回复" placement="topLeft">
          <template #content>
            <div @click.stop>
              <span style="color: red; font-weight: bold" v-if="record.noteSure">预审确认：</span>{{ record.noteSure }}<br />
              <span style="color: red; font-weight: bold" v-if="record.customerResponse">客户回复：</span>{{ record.customerResponse }}
            </div>
          </template>
          <a-icon
            v-if="record.noteSure || record.customerResponse"
            type="question-circle"
            class="userStyle"
            style="color: #ff9900; font-size: 18px"
            @click.stop="togglePopover(record)"
          />
          <a-icon v-if="record.edit_content" type="question-circle" class="userStyle" style="color: #ff9900; font-size: 18px" />
        </a-popover>
        <!-- <a-popover :visible="record.popoverVisible" title="预审确认与回复" placement="topLeft" trigger="click">
          <template #content>
            <div @click.stop>
              <span style="color: red; font-weight: bold" v-if="record.noteSure">预审确认：</span>{{ record.noteSure }}<br />
              <span style="color: red; font-weight: bold" v-if="record.customerResponse">客户回复：</span>{{ record.customerResponse }}
              <div style="text-align: right; margin-top: 10px">
                <a-button type="link" size="small" @click.stop="closePopover(record)">关闭</a-button>
              </div>
            </div>
          </template>
          <a-icon
            v-if="record.noteSure || record.customerResponse"
            type="question-circle"
            class="userStyle"
            style="color: #ff9900; font-size: 18px"
            @click.stop="togglePopover(record)"
          />
        </a-popover> -->
      </template>
      <template slot="isWarProc" slot-scope="text, record">
        <a-checkbox v-model="record.isWarProc"></a-checkbox>
      </template>
      <template slot="isCarPlate" slot-scope="text, record">
        <a-checkbox v-model="record.isCarPlate"></a-checkbox>
      </template>
      <span slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </span>
      <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
        <span :title="record.orderNo"> {{ record.orderNo }}</span
        >&nbsp;
        <span class="tagNum" style="display: inline-block">
          <span v-if="record.isExtremeJiaji">
            <span style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; margin-left: -10px; user-select: none"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
            <span style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; margin-left: -10px; user-select: none"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </span>
          <span
            v-else-if="record.isJiaji"
            style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; margin-left: -10px; user-select: none"
            >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
          </span>
          <a-tag
            v-if="record.ka"
            style="
              font-size: 10px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            KA
          </a-tag>
          <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.reOrder == 1">
            <a-tag
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              改
            </a-tag>
          </a-tooltip>
          <a-tooltip v-if="record.onLineEcnState > 0" :title="record.onLineOrRecordEcn == 2 ? '更改存档升级' : 'ECN在线改版'">
            <a-tag
              style="
                font-size: 10px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
                cursor: pointer;
              "
            >
              升
            </a-tag>
          </a-tooltip>
          <a-tag
            v-if="record.isOrderModify && record.orderModify == 1"
            @click.stop="xiudisplay(record)"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            修
          </a-tag>
          <a-tag
            v-if="record.isOrderModify && record.orderModify == 2"
            @click.stop="xiudisplay(record)"
            style="
              font-size: 12px;
              background: #ff9900;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #ff9900;
            "
          >
            修
          </a-tag>
          <a-tooltip v-if="record.identificationType == 1" title="按新单制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              新 </span
            >&nbsp;
          </a-tooltip>
          <a-tooltip v-if="record.identificationType == 2" title="按返单有改制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              改 </span
            >&nbsp;
          </a-tooltip>
          <a-tag
            v-if="record.isOrderModify && record.orderModify == 3"
            @click.stop="xiudisplay(record)"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            退
          </a-tag>
          <a-tooltip title="生产通知单已下载">
            <a-icon v-if="record.printProductionOrder" type="file-done" style="color: #ff9900; margin-left: 4px"></a-icon>
          </a-tooltip>
        </span>
      </div>
      <div slot="proOrderNo" slot-scope="text, record">
        <span v-if="record.isLongDelivery" style="color: red">{{ record.proOrderNo }}</span>
        <span v-else>{{ record.proOrderNo }}</span>
      </div>
    </a-table>
    <right-copy ref="RightCopy" />
    <a-modal title="修改内容" :width="1300" :visible="xiuvisible" destroyOnClose centered :mask="false" :maskClosable="false" @cancel="handleCancel">
      <div>
        <a-table
          :columns="columns4"
          :dataSource="dataSource4"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 400 }"
          :class="dataSource4.length ? 'min-table' : ''"
          :pagination="false"
        >
          <template slot="isPrintContract" slot-scope="record">
            <a-checkbox :checked="record.isPrintContract"></a-checkbox>
          </template>
          <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
            <div v-for="photo in record.filePath.split(',')" :key="index + '-' + photo">
              <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer />
            </div>
          </div>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="handleCancel">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
import { checkPermission } from "@/utils/abp";
import { setEngineeringBack } from "@/utils/request";
import { downFileCAM } from "@/services/projectApi";
import { ordermodifylist } from "@/services/mkt/CustInfoNew";
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "30%",
    align: "left",
  },
  // {
  //     title: "附件地址",
  //     dataIndex: "filePath",
  //     ellipsis: true,
  //     align: "left",
  // },
  {
    title: "图片",
    //dataIndex: "filePath",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    // dataIndex: "isPrintContract",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  components: { RightCopy },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            current: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
          selectedRows: {},
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
  },
  name: "LeftTable",
  data() {
    return {
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      columns4,
      xiuvisible: false,
      dataSource4: [],
      selectedRowKeysArray: [],
      selectedRowsData: [],
      selectedRowList: [],
      alldata: [],
      selectedRows: [],
      proOrderId: "",
      shiftKey: false,
      menuData: {},
      noteVisible: false,
    };
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < val.length; i++) {
              let obj2 = val[i].proOrderNo;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (longestChars.length > 12 && result == 0) {
              this.columns[1].width = "120px";
              this.columns[6].width = 150 + (longestChars.length - 12) * 5 + "px";
              this.columns[8].width = 280 - (longestChars.length - 12) * 5 + "px";
            }
            if (longestChars.length > 12 && result >= 1) {
              this.columns[1].width = 120 + result * 20 + "px";
              this.columns[6].width = 150 + (longestChars.length - 12) * 5 + "px";
              this.columns[8].width = 280 - result * 20 - (longestChars.length - 12) * 5 + "px";
            }
            if (result == 0 && longestChars.length <= 12) {
              this.columns[1].width = "120px";
              this.columns[6].width = "150px";
              this.columns[8].width = "280px";
            }
            if (result >= 1 && longestChars.length <= 12) {
              this.columns[1].width = 120 + result * 20 + "px";
              this.columns[8].width = 280 - result * 20 + "px";
              this.columns[6].width = "150px";
            }
          });
        }
      },
    },
    pagination: {
      handler(val) {
        // console.log(val)
      },
    },
  },
  created() {
    // console.log(this.dataSource)
  },
  methods: {
    checkPermission,
    xiudisplay(record) {
      console.log(record, "record");
      ordermodifylist(record.id).then(res => {
        if (res.code) {
          this.dataSource4 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
      this.xiuvisible = true;
    },
    handleCancel() {
      this.xiuvisible = false;
    },
    // 选择待收货订单列表
    onSelectChange(selectedRowKeys, selectedRows) {
      const newSelectedRowKeys = [],
        newSelectedRows = [];
      selectedRows.forEach(item => {
        newSelectedRowKeys.push(item.id);
        newSelectedRows.push(item);
      });
      this.selectedRowList = newSelectedRowKeys;
      this.selectedRows = newSelectedRows;
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowList.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    // isRedRow(record) {
    //   let strGroup = []
    //   if (record.id && record.id == this.proOrderId) {
    //     strGroup.push('rowBackgroundColor')
    //   }
    //   return strGroup
    // },
    // 切换当前记录的 Popover 状态
    togglePopover(record) {
      if (record.popoverVisible === undefined) {
        this.$set(record, "popoverVisible", true); // 初始化并响应式更新
      } else {
        record.popoverVisible = !record.popoverVisible;
      }
    },

    // 关闭当前记录的 Popover
    closePopover(record) {
      record.popoverVisible = false;
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      var data = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
        data.push(selectedRowsData[a]);
      }
      this.selectedRowList = arr;
      this.alldata = data;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowList[this.selectedRowList.length - 1];
        })[0];
        this.selectedRows = this.selectedRowsData;
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowList[0];
        })[0];
        this.selectedRows = this.selectedRowsData;
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowList;
        let record1 = this.alldata;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
          record1.splice(record1.indexOf(record), 1);
        } else {
          rowKeys.push(record.id);
          record1.push(record);
        }
        this.selectedRowList = rowKeys;
        this.alldata = record1;
        if (this.selectedRowList.length == 1) {
          this.selectedRowsData = record;
          this.selectedRows = record;
          this.alldata = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRows = record;
          this.selectedRowList = [record.id];
          this.alldata = [record];
        }
      }
      this.proOrderId = this.selectedRowsData.id;
      if ([58, 59, 77].includes(Number(this.selectedRowsData.joinFactoryId))) {
        this.$emit("gettopdata", this.proOrderId);
      } //获取多套价格
      this.shiftKey = false;
    },
    handleTableChange(pagination) {
      this.$emit("tableChange", pagination);
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-modal-body {
  padding: 0 4px;
}
/deep/.ant-modal-body {
  padding: 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table {
  .ant-table-thead > tr > th {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr {
    .lastTd {
      padding: 0 4px !important;
    }
  }
}
/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }

  .cookieIdColor {
    td:first-child {
      //border-left:2px solid #ff9900!important;
      background: #ff9900 !important;
    }

    //td:nth-child(2){
    //  color:#ff9900!important;
    //}
  }

  .rowBackgroundColor {
    background: #aba5a5;
  }

  .displayFlag {
    display: none;
  }
}

.peopleTag {
  margin: 0;
  padding: 0;
  width: 24px;
  border-radius: 12px;
  background: #2d221d;
  border-color: #2d221d;
  color: #ff9900;
  text-align: center;
  margin-left: 2px;
}
</style>
