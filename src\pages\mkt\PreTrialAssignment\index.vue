<!-- 市场管理 - 预审分派主组件 -->
<template>
  <div class="projectDispatch">
    <div class="contentBox">
      <div class="contentLeft">
        <left-table
          :dataSource="orderListData"
          :orderListTableLoading="orderListTableLoading"
          @assignOrderListChange="assignOrderListChange"
          @assignOrderListChange1="assignOrderListChange1"
          ref="leftTable"
          :rowKey="'id'"
          class="maintablestyle"
          :pagination="pagination"
          @tableChange="handleTableChange"
        >
        </left-table>
      </div>
      <div style="width: 24%; height: 100%; display: flex; flex-wrap: wrap">
        <div class="contentCenter">
          <center-table
            :dataSource="producerListData"
            :producerTabLoading="producerTabLoading"
            @assignPeopleChange="assignPeopleChange"
            @geteditInfodata="geteditInfodata"
            ref="centerTable"
            @getProducerInfo="getProducerInfo"
            class="centertablestyle"
          />
        </div>
      </div>
      <div style="width: 24%; height: 100%; display: flex; flex-wrap: wrap">
        <div class="contentRight">
          <right-table
            :dataSource="producerPeopleInfo"
            :producerInfoLoading="producerInfoLoading"
            @ChargebackClick="ChargebackClick"
            class="righttablestyle"
          />
        </div>
      </div>
      <!-- <a-tooltip :title="advanced ? '收起' : '展开'"  >
        <a-icon :type="advanced ? 'team' : 'team'"  style="color: #ff9900; font-size: 26px;position:absolute;top:-14px;right:0;" @click="showDrawer" />
      </a-tooltip>         -->
    </div>
    <div class="actionBox">
      <a-form-model layout="inline">
        <a-form-model-item
          v-if="checkPermission('MES.MarketModule.Pretrialassignment.BackToEnquiry')"
          :class="checkPermission('MES.MarketModule.Pretrialassignment.BackToEnquiry') ? 'showClass' : ''"
        >
          <a-button type="primary" @click="Returnorder"> 回退 </a-button>
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" @click="onSearch" class="showClass"> 查询(F) </a-button>
        </a-form-model-item>
        <a-form-model-item
          v-if="checkPermission('MES.MarketModule.Pretrialassignment.FenPai')"
          :class="checkPermission('MES.MarketModule.Pretrialassignment.FenPai') ? 'showClass' : ''"
        >
          <a-button type="primary" @click="assign"> 分派(S) </a-button>
        </a-form-model-item>
        <a-form-model-item>
          <div v-if="buttonsmenu">
            <a-dropdown>
              <a-button type="primary" @click.prevent> 按钮菜单栏 </a-button>
              <template #overlay>
                <a-menu class="tabRightClikBox">
                  <a-menu-item @click="Returnorder" v-if="checkPermission('MES.MarketModule.Pretrialassignment.BackToEnquiry')">回退</a-menu-item>
                  <a-menu-item @click="onSearch">查询(F)</a-menu-item>
                  <a-menu-item @click="assign" v-if="checkPermission('MES.MarketModule.Pretrialassignment.FenPai')"> 分派(S)</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </a-form-model-item>
      </a-form-model>
    </div>
    <a-modal
      title="分派信息"
      :visible="assignModalVisible"
      :confirm-loading="confirmLoading"
      @ok="assingOk"
      @cancel="handleCancel"
      cancel-text="取消(C)"
      centered
    >
      <h3>是否将订单号为：</h3>
      <div class="tabBox">
        <a-tag color="orange" v-for="(item, index) in assignOrderList1" :key="index + '_assign'"> {{ item }} </a-tag>
      </div>
      <h3 style="display: inline-block; margin-right: 10px">分派给:</h3>
      <span style="color: #f0f0f0; background: #bd4541; border: 1px solid #bd4541; border-radius: 4px; padding: 0 5px">{{
        assignPeople.realName_
      }}</span>
    </a-modal>
    <!-- 查询弹窗 -->
    <a-modal
      title="分派查询"
      :visible="searchModalVisible"
      @cancel="searchHandleCancel"
      @ok="searchHandleOk"
      ok-text="确定"
      cancel-text="取消"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <search-modal ref="queryInfo" />
    </a-modal>
    <!-- 取单设置弹窗 -->
    <a-modal
      title="取单设置"
      :visible="editInfoModalVisible"
      @cancel="leaveHandleCancel"
      @ok="leaveHandleOk"
      ok-text="确定"
      cancel-text="取消"
      destroyOnClose
      :maskClosable="false"
      :width="800"
      centered
    >
      <orderickup-modal ref="OrderickupModal" :data="editInfoList" @statusClick="statusClick" />
    </a-modal>
    <!-- 确认弹窗 -->
    <a-modal
      title=" 确认弹窗"
      :visible="dataVisible3"
      @cancel="reportHandleCancel"
      @ok="handleOk3"
      ok-text="确定"
      cancel-text="取消(C)"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span style="font-size: 16px">【{{ orderno }}】</span>
      <span style="font-size: 16px">{{ message }}</span>
    </a-modal>
    <!-- <search-modal
      :visible="searchModalVisible"
      @searchHandleOk="searchHandleOk"
      @searchHandleCancel="searchHandleCancel"      
    /> -->
  </div>
</template>

<script>
import moment from "moment";
import {
  projectOrderAssign,
  projectOrderInfo,
  projectOrderLeave,
  projectPeopleInfo,
  projectPeopleList,
  dispatchTotal,
  backSendOrder,
  backtoenquiry,
  projectDispathgetOrderList,
} from "@/services/mkt/PreTrialAssignment.js";
import SearchModal from "@/pages/mkt/PreTrialAssignment/subassembly/SearchModal";
import OrderickupModal from "@/pages/mkt/PreTrialAssignment/subassembly/OrderickupModal";
import LeftTable from "@/pages/mkt/PreTrialAssignment/subassembly/LeftTable";
import CenterTable from "@/pages/mkt/PreTrialAssignment/subassembly/CenterTable";
import RightTable from "@/pages/mkt/PreTrialAssignment/subassembly/RightTable";
import { checkPermission } from "@/utils/abp";
export default {
  name: "PreTrialAssignment",
  components: { RightTable, LeftTable, CenterTable, OrderickupModal, SearchModal },
  inject: ["reload"],
  data() {
    return {
      orderListTableLoading: false,
      producerTabLoading: false,
      producerInfoLoading: false,
      conditions: {},
      orderListData: [],
      producerListData: [],
      producerPeopleInfo: [],
      assignPeople: "", // 分派人员
      assignOrderList: [], // 分派订单id列表
      assignOrderList1: [], // 分派订单生产编号列表
      assignModalVisible: false, // 分派弹窗
      confirmLoading: false, // 分派确认按钮loading
      editInfoList: {
        realName: "",
        targetNum: 0,
        singleAcquisition: 0,
        stayNum: 0,
        isAskForLeave: false, //当前状态
        isAutoSendOrder: false,
        autoSendStartDate: null, // 开始时间
        autoSendEndDate: null, // 结束时间
        sort: 0,
        beonDuty: false, //工程值班
      },
      editOkBtnLoading: false, // 编辑制作人信息确定按钮loading
      editInfoLoading: false,
      editInfoModalVisible: false, // 制作人员信息编辑弹窗
      searchModalVisible: false,
      dispatchTotalData: [],
      dispatchTotalLoading: false,
      buttonsmenu: false,
      advanced: true, //  人员关闭，
      visibleDrawer: false,
      topName: "",
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      dataVisible3: false,
      message: "",
      orderno: "",
      type: "",
      deletId: "",
      queryData: {},
      isCtrlPressed: false,
    };
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  methods: {
    checkPermission,
    handleResize() {
      var maintablestyle =
        document.getElementsByClassName("maintablestyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var centertablestyle =
        document.getElementsByClassName("centertablestyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var righttablestyle =
        document.getElementsByClassName("righttablestyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var elements = document.getElementsByClassName("showClass");
      maintablestyle.style.height = window.innerHeight - 173 + "px";
      centertablestyle.style.height = window.innerHeight - 173 + "px";
      righttablestyle.style.height = window.innerHeight - 173 + "px";
      var footerwidth = window.innerWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 410 < footerwidth) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = elements.length * 104;
      if (window.innerWidth <= 1920) {
        if (num + paginnum * 50 + 410 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 + 300 + num < window.innerWidth - 150 && window.innerWidth > 766) {
          if (footerwidth / 2 < paginnum * 25 + 300) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
          this.buttonsmenu = false;
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "inline-block";
          }
        } else {
          if (window.innerWidth > 766) {
            if (footerwidth / 2.8 < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
            this.buttonsmenu = true;
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
          } else {
            console.log(this.pagination.simple, "dio");
            this.pagination.simple = true;
            if (window.innerWidth - 4 - num < 70) {
              this.buttonsmenu = true;
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
              }
            } else {
              this.buttonsmenu = false;
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "inline-block";
              }
            }
          }
        }
      } else {
        if (window.innerWidth > 1920) {
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
          this.pagination.simple = false;
        }
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.onSearch();
        this.handleCancel();
        this.reportHandleCancel();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "83" && this.isCtrlPressed && checkPermission("MES.MarketModule.Pretrialassignment.FenPai")) {
        this.assign();
        this.reportHandleCancel();
        this.searchHandleCancel();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "67" && !this.isCtrlPressed) {
        this.reportHandleCancel();
        this.handleCancel();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.searchModalVisible) {
        this.searchHandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.assignModalVisible) {
        this.assingOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible3) {
        this.handleOk3();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.editInfoModalVisible) {
        this.leaveHandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    // 获取订单列表
    getOrderList(payload) {
      let params = {
        ...this.pagination,
      };
      params.pageIndex = params.current;
      let obj = Object.assign(params, payload);
      this.orderListTableLoading = true;
      obj.PcbFileName = obj.PcbFileName ? obj.PcbFileName.replace(/\s+/g, " ").trim() : "";
      projectDispathgetOrderList(obj)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            if (this.orderListData.length && (obj.OrderNo || obj.custNo || obj.PcbFileName)) {
              this.$refs.leftTable.selectedRowKeys1 = [this.orderListData[0].orderNo];
              this.deletId = this.orderListData[0].id;
              this.$refs.leftTable.selectedRowKeys = [this.orderListData[0].id];
              this.assignOrderList = [this.orderListData[0].id];
              this.assignOrderList1 = [this.orderListData[0].orderNo];
            }
            const pagination = { ...this.pagination };
            this.pagination.total = res.data.totalCount;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    // 获取工程派单管理汇总
    // getDispatchTotal(){
    //   this.dispatchTotalLoading = true
    //   dispatchTotal().then((res) => {
    //     this.dispatchTotalLoading = false;
    //     if (res.code) {
    //       this.dispatchTotalData = res.data;
    //     }
    //   });
    // },

    // 显示
    showDrawer() {
      this.advanced = !this.advanced;
    },
    // 抽屉打开
    // counts(record){
    //   this.getProducerInfo(record.userNo,'2',) // 停留
    //   console.log('点击',record)
    //   this.topName = record.realName + " - "  + "停留" + " - " + "明细"
    //   this.visibleDrawer = true;
    // },

    // 抽屉关闭
    // onClose(){
    //   this.visibleDrawer = false;
    // },
    // 获取制作人员列表
    getProducerList() {
      this.producerTabLoading = true;
      projectPeopleList().then(res => {
        this.producerTabLoading = false;
        if (res) {
          this.producerListData = res.data;
          setTimeout(() => {
            this.handleResize();
          }, 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取制作人员详情
    getProducerInfo(userLoginID, type) {
      let params = {
        userLoginID: userLoginID,
      };
      this.producerInfoLoading = true;
      projectPeopleInfo(userLoginID).then(res => {
        if (res.code) {
          this.producerInfoLoading = false;
          this.producerPeopleInfo = res.data;
          setTimeout(() => {
            this.handleResize();
          }, 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //回退
    Returnorder() {
      if (this.$refs.leftTable.selectedRowKeys1.length == 0) {
        this.$message.warning("请选择需要回退的订单！");
        return;
      }
      if (this.$refs.leftTable.selectedRowKeys1.length > 1) {
        this.$message.warning("只能选择一条订单！");
        return;
      }
      this.dataVisible3 = true;
      this.message = "确定订单回退吗？";
      this.orderno = this.$refs.leftTable.selectedRowKeys1.join(",");
      this.type = "5";
    },
    // 查询
    onSearch() {
      this.searchModalVisible = true;
      this.pagination.current = 1;
    },
    searchHandleOk() {
      const params = this.$refs.queryInfo.form;
      this.queryData = this.$refs.queryInfo.form;
      var arr1 = params.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      params.OrderNo = arr1.join("");
      var arr2 = params.custNo.split("");
      if (arr2.length > 10) {
        arr2 = arr2.slice(0, 10);
      }
      params.custNo = arr2.join("");
      var arr3 = params.PcbFileName.split("");
      if (arr3.length > 100) {
        arr3 = arr3.slice(0, 100);
      }
      params.PcbFileName = arr3.join("");
      this.getOrderList(params);
      this.searchModalVisible = false;
    },
    searchHandleCancel() {
      this.searchModalVisible = false;
    },

    assignPeopleChange(payload) {
      this.assignPeople = payload;
    },
    assignOrderListChange(payload) {
      this.assignOrderList = payload;
    },
    assignOrderListChange1(payload) {
      this.assignOrderList1 = payload;
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      localStorage.setItem("pageCurrent", this.pagination.current);
      localStorage.setItem("pageSize", pagination.pageSize);
      this.pageStat = false;
      localStorage.removeItem("stat");
      if (JSON.stringify(this.queryData) != "{}") {
        this.getOrderList(this.queryData);
      } else {
        this.getOrderList();
      }
    },
    // 分派
    assign() {
      if (this.assignOrderList.length > 0 && this.assignPeople != "") {
        this.assignModalVisible = true;
      } else if (this.assignOrderList.length == 0) {
        this.$message.warning("请选择订单");
      } else if (this.assignPeople == "") {
        this.$message.warning("请选择制作人员");
      }
    },
    // 分派确定
    assingOk() {
      let params = {
        ids: this.assignOrderList,
        userLoginID: this.assignPeople.userLoginID,
      };
      projectOrderAssign(params).then(res => {
        if (res) {
          if (res?.code == 1) {
            this.$message.success("分派成功");
            this.getOrderList();
            this.getProducerList();
            this.getProducerInfo(this.assignPeople.userLoginID);
            setTimeout(() => {
              this.visible = false;
              this.confirmLoading = false;
              // this.reload();
            }, 1000);
          } else {
            this.$message.error("分派失败");
            this.visible = false;
            this.confirmLoading = false;
          }
        }
      });
      this.assignOrderList = [];
      this.assignModalVisible = false;
      this.confirmLoading = false;
    },
    handleCancel() {
      this.assignModalVisible = false;
    },
    // 分派回退
    ChargebackClick(record) {
      this.orderno = record.orderNo;
      this.deletId = record.id;
      this.dataVisible3 = true;
      this.message = "确定回退吗？";
      this.type = "1";
    },
    // 取单设置
    geteditInfodata(record) {
      this.editInfoModalVisible = true;
      projectOrderInfo(record.userLoginID).then(res => {
        if (res.code) {
          res.data.realName = record.realName_;
          res.data.groups = record.groups;
          if (res.data.labels == 0) {
            res.data.labels = "0";
          } else if (res.data.labels == 1) {
            res.data.labels = "1";
          } else {
            res.data.labels = "2";
          }
          if (res.data.autoSendStartDate) {
            res.data.autoSendStartDate = moment(res.data.autoSendStartDate, "HH:mm");
          }
          if (res.data.autoSendEndDate) {
            res.data.autoSendEndDate = moment(res.data.autoSendEndDate, "HH:mm");
          }
          // if(res.data.layersRange){
          //   res.data.layerstart = res.data.layersRange.split('-')[0]
          //   res.data.layerend = res.data.layersRange.split('-')[1]
          // }else{
          //   res.data.layerstart = ''
          //   res.data.layerend = ''
          // }
          if (res.data.autoSendStartDate) {
            res.data.autoSendStartDate = moment(res.data.autoSendStartDate, "HH:mm");
          }
          if (!res.data.custNo) {
            res.data.custNo = [];
          } else {
            res.data.custNo = res.data.custNo.split(",");
          }
          if (!res.data.orderByCustNo) {
            res.data.orderByCustNo = [];
          } else {
            res.data.orderByCustNo = String(res.data.orderByCustNo).split(",");
          }
          this.editInfoList = res.data;
        }
      });
    },
    leaveHandleCancel(e) {
      this.editInfoModalVisible = false;
    },
    leaveHandleOk(e) {
      this.editInfoLoading = true;
      if (this.editInfoList.labels == "0") {
        this.editInfoList.labels = 0;
      } else if (this.editInfoList.labels == "1") {
        this.editInfoList.labels = 1;
      } else {
        this.editInfoList.labels = 2;
      }
      var r = /^(0|[1-9]\d*)$/;
      // if(this.editInfoList.layerstart && !r.test(this.editInfoList.layerstart)){
      //   this.$message.error("请输入正确的起始层级");
      //   return ;
      // }
      // if(this.editInfoList.layerend && !r.test(this.editInfoList.layerend)){
      //   this.$message.error("请输入正确的结束层级");
      //   return ;
      // }
      // if((this.editInfoList.layerstart && !this.editInfoList.layerend) || (!this.editInfoList.layerstart && this.editInfoList.layerend)){
      //   this.$message.error("层数区间必须为闭合状态,请输入完整区间");
      //   return
      // }
      // if(Number(this.editInfoList.layerstart) > Number(this.editInfoList.layerend)){
      //   this.$message.error("结束层级不能大于起始层级");
      //   return
      // }
      const params = {
        ...this.editInfoList,
        targetNum: Number(this.editInfoList.targetNum),
        stayNum: Number(this.editInfoList.stayNum),
        autoSendStartDate: this.editInfoList.autoSendStartDate ? this.transformTimestamp(this.editInfoList.autoSendStartDate) : null,
        autoSendEndDate: this.editInfoList.autoSendEndDate ? this.transformTimestamp(this.editInfoList.autoSendEndDate) : null,
        // layersRange:this.editInfoList.layerstart?this.editInfoList.layerstart + '-' + this.editInfoList.layerend:'',
      };
      params.custNo = params.custNo.join(",");
      params.orderByCustNo = params.orderByCustNo.join(",");
      projectOrderLeave(params).then(res => {
        if (res.code == 1) {
          this.$message.success("操作成功");
        } else {
          this.$message.error("操作失败");
        }
        this.getProducerList();
        this.editInfoModalVisible = false;
        this.editInfoLoading = false;
      });
    },
    // 处理时间格式
    transformTimestamp(timestamp) {
      let a = new Date(timestamp).getTime();
      const date = new Date(a);
      const h = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      const m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      const dateString = h + m;
      return dateString;
    },
    statusClick() {
      this.editInfoList.isAskForLeave = !this.editInfoList.isAskForLeave;
    },
    reportHandleCancel() {
      this.dataVisible3 = false;
    },
    // 确认弹窗
    handleOk3() {
      // 回退
      if (this.type == "1") {
        this.spinning = true;
        backSendOrder(this.deletId)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
              this.getProducerList();
              this.getOrderList();
              this.getProducerInfo(this.assignPeople.userLoginID);
            } else {
              this.$message.error(res.message);
            }
            // this.reload()
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.type == "5") {
        this.spinning = true;
        backtoenquiry(this.$refs.leftTable.selectedRowKeys)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      this.dataVisible3 = false;
    },
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.handleResize();
    });
  },
  mounted() {
    this.getOrderList();
    // this.getDispatchTotal();
    this.getProducerList();
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-pagination.mini .ant-pagination-total-text,
.ant-pagination.mini .ant-pagination-simple-pager {
  height: 24px;
  line-height: 24px;
  font-size: 13px;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
.tabRightClikBox {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-table-wrapper {
  height: 100%;
}
/deep/ h1,
h2,
h3,
h4,
h5,
h6 {
  color: #000000 !important;
}
/deep/.ant-modal-body {
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}

/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}

/deep/ .ant-table-tbody > tr.ant-table-row-selected td {
  background-color: #dcdcdc !important;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dcdcdc !important;
}
.projectDispatch {
  user-select: none !important;
  width: 100%;
  background: #fff;
  /deep/ .ant-table-thead > tr > th {
    border-right: 1px solid #efefef;
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/.ant-table-thead > tr {
    .ant-table-selection-column {
      padding: 7px 0 !important;
      width: 50px;
    }
  }
  /deep/ .ant-table-tbody > tr > td {
    border-right: 1px solid #efefef;
    padding: 7px 2px !important;
  }
  //.divider {
  //  height: 10px;
  //  margin: 0 -20px;
  //  background-color: ##F8F8F8;
  //}
  .contentBox {
    width: 100%;
    display: flex;
    border: 1px solid #e9e9f0;
    border-bottom: none;
    .contentLeft {
      border: 2px solid #e9e9f0;
      width: 52%;
      display: inline-block;
    }
    .contentCenter {
      border: 2px solid #e9e9f0;
      width: 100%;
      height: 100%;
      flex-wrap: wrap;
      align-content: start;
    }
    .contentRight {
      width: 100%;
      border: 2px solid #e9e9f0;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      align-content: start;
      /deep/ .ant-table-scroll {
        overflow: hidden !important;
      }
      .top {
        width: 100%;
        height: 364px;
        // overflow-y: scroll;
      }
      /deep/ .bto {
        .ant-table-scroll {
          overflow: hidden;
          overflow-x: unset;
        }
        width: 100%;
      }
    }
  }
  /deep/ .actionBox {
    width: 100%;
    height: 48px;
    border: 2px solid #e9e9f0;
    overflow: hidden;
    background: #ffffff;
    .ant-form-inline .ant-form-item {
      margin-right: 20px;
    }
    form {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .tabBox {
    display: flex;
    flex-wrap: wrap;
    span {
      display: inline-block;
      width: 100px;
      text-align: center;
      margin-bottom: 5px;
    }
  }
  /deep/ .ant-table-selection-column {
    // padding: 11px 3px!important;
    .ant-checkbox {
      margin-left: 0;
    }
  }
  /deep/ .ant-table-selection-col {
    width: 20px !important;
  }
  /deep/ .ant-table-thead > tr > th,
  /deep/ .ant-table-tbody > tr > td {
    padding: 5px 5px;
  }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin: 9px 0 9px 9px;
  }
  /deep/ .ant-table-placeholder {
    width: 100%;
    position: absolute;
    top: 35px;
  }
  .smallActive {
    /deep/ .ant-table {
      .ant-table-header {
        tr th:nth-child(4) {
          width: 45px !important;
          min-width: 45px !important;
        }
      }
      .ant-table-tbody {
        tr td:nth-child(4) {
          width: 45px !important;
          min-width: 45px !important;
        }
      }
    }
  }
  .Active {
    /deep/ .ant-table {
      .ant-table-header {
        tr th:nth-child(4) {
          width: 70px !important;
          min-width: 70px !important;
        }
      }
      .ant-table-tbody {
        tr td:nth-child(4) {
          width: 70px !important;
          min-width: 70px !important;
        }
      }
    }
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
