<!-- 车间管理-阻焊管理-过序 -->
<template>
  <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 12 }">
    <a-form-item label="流程卡号" style="display: block; position: relative">
      <!--      <a-input   type="password" v-model='cardNo' @keyup="handleKeyUp" :autoFocus="autoFocus"/>-->
      <a-input allowClear v-model="cardNo" type="password" @keyup.enter="keyupEnter" :autoFocus="autoFocus" style="color: rgba(255, 255, 255, 1)" />
      <p style="margin: 0; position: absolute; left: 12px; top: 0">{{ this.cardNo }}</p>
    </a-form-item>

    <a-form-item label="过序数量">
      <a-input allowClear v-model="value" @change="countChange" />
    </a-form-item>
  </a-form>
</template>

<script>
export default {
  name: "OverOrderInfo",
  props: ["quantity", "quantityCopy"],
  data() {
    return {
      cardNo: "",
      autoFocus: true,
      keyupLastTime: undefined,
      realBarcode: "",
      value: "",
    };
  },
  watch: {
    quantity: {
      handler(val) {
        this.value = val;
      },
    },
  },
  methods: {
    // handleKeyUp(e) {
    //   let gap = 0;
    //   if (this.keyupLastTime) {
    //     gap = new Date().getTime() - this.keyupLastTime;
    //     if (gap > 50) {
    //       gap = 0;
    //       this.realBarcode = "";
    //     }
    //   }
    //   this.keyupLastTime = new Date().getTime();
    //   // 输入法会触发keyup事件，key为Process，跳过即可
    //   if (e.key != "Process" && gap < 50) {
    //     console.log(e.key.trim())
    //     if (e.key.trim().length == 1) {
    //       // 输入单个字母或者数字
    //       this.realBarcode += e.key;
    //     } else if (e.key.trim() == "ArrowDown") {
    //       // 根据规则，判断barcode类型，返回数据（自定义规则）
    //       if (this.realBarcode) {
    //         this.cardNo = this.realBarcode;
    //         this.keyupEnter()
    //         this.realBarcode = "";
    //       } else {
    //         return;
    //       }
    //     }
    //   }
    // },
    keyupEnter() {
      this.$emit("keyupEnter", this.cardNo);
    },
    countChange() {
      this.$emit("quantityChange", this.value);
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .ant-form-item {
  .ant-form-item-control {
    span {
      display: block;
    }
  }
}
</style>
