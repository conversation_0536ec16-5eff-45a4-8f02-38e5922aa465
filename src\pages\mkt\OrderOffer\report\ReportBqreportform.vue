<!--BQ报价单  -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="bqreport" style="padding: 25px; color: black; font-family: 宋体">
      <div v-if="bqreport_type == 'nx'">
        <div style="width: 100%; display: flex">
          <div style="width: 25%">
            <img src="@/assets/img/bq.jpg" v-if="Bqreportdata.factory_.indexOf('深圳') != -1" />
            <img src="@/assets/img/jdlogo.png" v-else-if="Bqreportdata.factory_.indexOf('重庆') != -1" style="height: 70px" />
            <img src="@/assets/img/bqlogo.png" v-else style="height: 70px" />
          </div>
          <div style="text-align: center; width: 50%">
            <div style="font-size: 36px; font-weight: bold; letter-spacing: 70px">报价单</div>
          </div>
          <div style="width: 25%; text-align: center; line-height: 70px; font-size: 20px; font-weight: bold">
            {{ Bqreportdata.orderNo_ }}
          </div>
        </div>
        <div style="font-weight: bold; font-size: 16px">
          <div>公司名称:{{ Bqreportdata.factory_ }}</div>
          <div>加工地址:{{ Bqreportdata.address_ }}</div>
          <div>
            {{ Bqreportdata.party_ }}<span style="padding-left: 70px"> {{ Bqreportdata.link_ }} </span>
          </div>
          <div>FROM:{{ Bqreportdata.factory_ }} <span style="padding-left: 70px"> 销售工程师/电话: </span></div>
        </div>
        <table style="border-top: 1px solid black; border-left: 1px solid black" border>
          <tr v-for="(item, index) in Bqreportdata.bqPar" :key="index">
            <td style="width: 9%">{{ item.aa }}</td>
            <td style="width: 16.2%">{{ item.field0 }}</td>
            <td style="width: 16.2%">{{ item.field1 }}</td>
            <td style="width: 16.2%">{{ item.field2 }}</td>
            <td style="width: 16.2%">{{ item.field3 }}</td>
            <td style="width: 16.2%">{{ item.field4 }}</td>
          </tr>
        </table>
        <div style="white-space: pre">
          {{ Bqreportdata.specialrequirements }}
        </div>
      </div>
      <div v-else-if="bqreport_type == 'wx'">
        <div style="display: flex; align-items: center; flex-wrap: wrap; align-content: center; justify-content: center">
          <div>
            <img src="@/assets/img/bqlogo.png" style="height: 55px" />
          </div>
          <div style="padding-left: 15px; font-size: 20px">
            深圳市奔强电路有限公司<br />
            Shenzhen Benqiang Circuit Co.,Ltd
          </div>
        </div>
        <div style="text-align: center; font-weight: bold; font-size: 24px">PCB Quotation</div>
        <div style="display: flex; justify-content: space-around">
          <span>From: Shenzhen Benqiang Circuit</span>
          <span>To: {{ Bqreportdata.value_1 }}</span>
        </div>
        <table style="border-top: 1px solid black; border-left: 1px solid black; width: 100%" border>
          <tr>
            <td>RFQ Number:</td>
            <td colspan="2">{{ Bqreportdata.value_2 }}</td>
            <td>Date:</td>
            <td colspan="2">{{ Bqreportdata.value_3 }}</td>
          </tr>
          <tr>
            <td colspan="6">Technical Specification</td>
          </tr>
          <tr>
            <td rowspan="2">Delivery PNL Size:</td>
            <td>Length(mm)</td>
            <td>Width(mm)</td>
            <td rowspan="2">Unit Size:</td>
            <td>Length(mm)</td>
            <td>Width(mm)</td>
          </tr>
          <tr>
            <td>{{ Bqreportdata.value_4 }}</td>
            <td>{{ Bqreportdata.value_5 }}</td>
            <td>{{ Bqreportdata.value_6 }}</td>
            <td>{{ Bqreportdata.value_7 }}</td>
          </tr>
          <tr>
            <td>PCS Per Delivery PNL:</td>
            <td>{{ Bqreportdata.value_ }}</td>
            <td>UP</td>
            <td>Max X-out Lot Scrap Rate：</td>
            <td colspan="2">{{ Bqreportdata.value_8 }}</td>
          </tr>
          <tr>
            <td>Type:</td>
            <td colspan="2">{{ Bqreportdata.value_9 }}</td>
            <td>IPC Class:</td>
            <td colspan="2">{{ Bqreportdata.value_10 }}</td>
          </tr>
          <tr>
            <td>Layers Count:</td>
            <td colspan="2">{{ Bqreportdata.value_11 }}</td>
            <td>Impedance:</td>
            <td colspan="2">{{ Bqreportdata.value_12 }}</td>
          </tr>
          <tr>
            <td>Material:</td>
            <td colspan="2">{{ Bqreportdata.value_13 }}</td>
            <td>Solder Mask Color:</td>
            <td colspan="2">{{ Bqreportdata.value_14 }}</td>
          </tr>
          <tr>
            <td>Laminate Thickness(mm):</td>
            <td colspan="2">{{ Bqreportdata.value_15 }}</td>
            <td>Screen Printing color:</td>
            <td colspan="2">{{ Bqreportdata.value_16 }}</td>
          </tr>
          <tr>
            <td>Finished Board Thickness(mm):</td>
            <td colspan="2">{{ Bqreportdata.value_17 }}</td>
            <td>Peelable Mask:</td>
            <td colspan="2">{{ Bqreportdata.value_18 }}</td>
          </tr>
          <tr>
            <td>Outer L Cu Thickness(oz):</td>
            <td colspan="2">{{ Bqreportdata.value_19 }}</td>
            <td>Surface Finishing:</td>
            <td colspan="2">{{ Bqreportdata.value_20 }}</td>
          </tr>
          <tr>
            <td>Inner L Cu Thickness(oz):</td>
            <td colspan="2">{{ Bqreportdata.nO_ }}</td>
            <td>Plug Via Hole:</td>
            <td colspan="2">{{ Bqreportdata.nO1_ }}</td>
          </tr>
          <tr>
            <td>Hole Quantity/pcs:</td>
            <td colspan="2">{{ Bqreportdata.nO2_ }}</td>
            <td>Routing (m/sqm)：</td>
            <td colspan="2">{{ Bqreportdata.nO3_ }}</td>
          </tr>
          <tr>
            <td>TG:</td>
            <td colspan="2">{{ Bqreportdata.nO4_ }}</td>
            <td>E-Test：</td>
            <td colspan="2">{{ Bqreportdata.nO5_ }}</td>
          </tr>
        </table>
        <div style="font-weight: bold; font-size: 24px">Price And General Terms</div>
        <table style="border-top: 1px solid black; border-left: 1px solid black; width: 100%" border>
          <tr>
            <td>Price(USD)</td>
            <td>Qty(pcs)</td>
            <td>SC/Tooling</td>
            <td>Unit Price</td>
            <td>Surface finish</td>
            <td>Flying Proble/E-test Fixture</td>
            <td>Film Charge</td>
            <td>Other</td>
            <td>Lead Time</td>
            <td>Total</td>
          </tr>
          <tr v-for="(item, index) in Bqreportdata.price" :key="index">
            <td>{{ item.price1 }}</td>
            <td>{{ item.price2 }}</td>
            <td>{{ item.price3 }}</td>
            <td>{{ item.price4 }}</td>
            <td>{{ item.price5 }}</td>
            <td>{{ item.price6 }}</td>
            <td>{{ item.price7 }}</td>
            <td>{{ item.price8 }}</td>
            <td>{{ item.price9 }}</td>
            <td>{{ item.price10 }}</td>
          </tr>
          <tr>
            <td>Remark:</td>
            <td colspan="9">{{ Bqreportdata.nO7_ }}</td>
          </tr>
          <tr>
            <td colspan="10">1.The valid time of this quotation: 30 days.</td>
          </tr>
          <tr>
            <td colspan="10">2.Payment Terms: 100%TT in advance for new clients, AMS 30 days for stable clients FOB Shenzhen.</td>
          </tr>
          <tr>
            <td colspan="10">3.If manufacturing data is different from information above, the price may be changed accordingly.</td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdf from "@/utils/htmlToPdf"; //竖版a4
import convertToChineseNum from "@/utils/convertToChineseNum";
export default {
  name: "",
  props: ["Bqreportdata", "ttype", "bqreport_type"],
  computed: {},
  data() {
    return {
      amountto: 0,
      printObj1: {
        id: "bqreport", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    console.log(this.bqreport_type, "bqreport_type");
    this.amountto = 0;
    this.printObj1.closeCallback = this.closePrintTool;
  },
  methods: {
    convertToChineseNum,
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.Bqreportdata.pcbFileName;
    },
    getreportPdf() {
      htmlToPdf("bqreport", this.Bqreportdata.pcbFileName);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
#bqreport {
  table > tr > td {
    padding: 0px 5px;
    word-break: break-word;
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
