<template>
  <page-layout>
    <a-card>
      <standard-table
        rowKey="key"
        :columns="columns"
        :dataSource="data"
        :pagination="pagination"
        :row-selection="rowSelection"
        :selectedRows.sync="selectedRows"
        @selectedRowChange="onSelectedRowChange"
      >
      </standard-table>
    </a-card>
  </page-layout>
</template>
<script>
import PageLayout from "@/layouts/PageLayout";
import StandardTable from "@/components/table/StandardTable";
const columns = [
  {
    title: "Name",
    dataIndex: "name",
    key: "name"
  },
  {
    title: "Age",
    dataIndex: "age",
    key: "age",
    width: "12%"
  },
  {
    title: "Address",
    dataIndex: "address",
    width: "30%",
    key: "address"
  }
];

const data = [
  {
    key: 1,
    name: "<PERSON> sr.",
    age: 60,
    address: "New York No. 1 Lake Park",
    children: [
      {
        key: 11,
        name: "<PERSON>",
        age: 42,
        address: "New York No. 2 Lake Park"
      },
      {
        key: 12,
        name: "<PERSON> jr.",
        age: 30,
        address: "New York No. 3 Lake Park",
        children: [
          {
            key: 121,
            name: "<PERSON>",
            age: 16,
            address: "New York No. 3 Lake Park"
          }
        ]
      },
      {
        key: 13,
        name: "<PERSON> Green sr.",
        age: 72,
        address: "London No. 1 Lake Park",
        children: [
          {
            key: 131,
            name: "Jim Green",
            age: 42,
            address: "London No. 2 Lake Park",
            children: [
              {
                key: 1311,
                name: "Jim Green jr.",
                age: 25,
                address: "London No. 3 Lake Park"
              },
              {
                key: 1312,
                name: "Jimmy Green sr.",
                age: 18,
                address: "London No. 4 Lake Park"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    key: 2,
    name: "Joe Black",
    age: 32,
    address: "Sidney No. 1 Lake Park"
  }
];

const rowSelection = {
  onChange: (selectedRowKeys, selectedRows) => {
    console.log(
      `selectedRowKeys: ${selectedRowKeys}`,
      "selectedRows: ",
      selectedRows
    );
  },
  onSelect: (record, selected, selectedRows) => {
    console.log(record, selected, selectedRows);
  },
  onSelectAll: (selected, selectedRows, changeRows) => {
    console.log(selected, selectedRows, changeRows);
  }
};

export default {
  components: { PageLayout, StandardTable },
  data() {
    return {
      data,
      columns,
      rowSelection,
      selectedRows: [],
      pagination: this.$store.state.setting.pagination,
    };
  },
  methods: {
    onSelectedRowChange(selectedRowKeys, selectedRows) {
      console.log(selectedRowKeys, selectedRows)
    }
  }
};
</script>
