import { request, METHOD } from '@/utils/request'
// 待磨板列表
export async function getWaitOrderList(params) {
    return request("/api/app/grind-plate/get-grind-plate-out-list", METHOD.GET, params)
}
// 当班统计
export async function getStatisticsList(params) {
    return request("/api/app/grind-plate/get-mo-ban-ondutystatistics", METHOD.GET, params)
}

// 磨板开始
export async function GrindingPlateStart(Id) {
    return request(`/api/app/grind-plate/grind-plate-start/${Id}`, METHOD.GET,)
}
// 磨板完成
export async function GrindingPlateEnd(Id) {
    return request(`/api/app/grind-plate/grind-plate-end/${Id}`, METHOD.GET,)
}
// 呼叫小车
export async function CallTrolley() {
    return request(`/api/app/e-mSTProc-rOUTMake/call-trolley`, METHOD.POST)
}
// 人员确认
export async function Confirm() {
    return request(`/api/app/e-mSTProc-rOUTMake/confirm`, METHOD.POST)
}
// 取消小车
export async function AgvCancel() {
    return request(`/api/app/e-mSTProc-rOUTMake/agv-cancel`, METHOD.POST)
}
// 对应机台信息
export async function grindPlateFinishOrder(params) {
    return request('/api/app/grind-plate/grind-plate-finish-order', METHOD.GET,params)
}

 

export default {
    getWaitOrderList,
    getStatisticsList,
    GrindingPlateStart,
    GrindingPlateEnd,
    CallTrolley,
    Confirm,
    AgvCancel,
    grindPlateFinishOrder,
}