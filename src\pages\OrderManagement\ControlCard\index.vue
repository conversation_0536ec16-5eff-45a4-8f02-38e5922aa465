<!--生产管理- 管制卡 -->
<template>
    <a-spin :spinning="spinning">
        <div class="projectCard">
          <div class="content"> 
            <!-- <a-button style="width:4%;margin-right:0.2%;">刷新</a-button> 
            <a-button style="width:6%;margin-right:0.2%;">打印管制卡</a-button> 
            <a-button style="width:7%;margin-right:0.2%;">打印V割条形码</a-button> 
            <a-button style="width:9%;margin-right:0.2%;">打印测试架条形码</a-button> 
            <a-button style="width:8%;margin-right:0.2%;">取消个人结束过序</a-button> 
            <a-button style="width:6%;margin-right:0.2%;">外发工厂</a-button>    
            <a-button style="width:6%;margin-right:0.2%;">导入过序</a-button>   
            <a-button style="width:6%;margin-right:0.2%;">删除过序</a-button>                 
            <br> -->
            <a-input style="width:8%;margin-right:0.5%;" placeholder="管制卡编号" v-model="formData.CardNo"  @keyup.enter.native="searchClick"></a-input>  
            <a-input style="width:8%;margin-right:0.5%;" placeholder="拼版订单编号" v-model="formData.OrderNo"  @keyup.enter.native="searchClick"></a-input>
            <a-select  style="width:6%;margin-right:0.5%;" v-model="formData.Status"  @keyup.enter.native="searchClick">
                <a-select-option :key="0" > 请选择状态</a-select-option>
                  <a-select-option :key="10" > 生产中</a-select-option>
                  <a-select-option :key="20" > 完成</a-select-option>
                  <a-select-option :key="30" > 作废</a-select-option>
                  <a-select-option :key="40" > 暂停</a-select-option>
                  <a-select-option :key="50" > 待审核</a-select-option>
            </a-select> 
            <!-- <a-radio-group name="radioGroup" :default-value="0">
                <a-radio style="width:22%;margin-right:0.5%;" :value="1">生产中</a-radio>
                <a-radio style="width:17%;margin-right:0.5%;" :value="2">完成</a-radio>
                <a-radio style="width:17%;margin-right:0.5%;" :value="3">作废</a-radio>
                <a-radio style="width:17%;margin-right:0.5%;" :value="4">暂停</a-radio>
                <a-radio style="width:22%;margin-right:0.5%;" :value="5">待审核</a-radio>               
          </a-radio-group> 
          <br>
            <a-select  style="width:10%;margin-right:0.5%;">
                  <a-select-option :key="1" > 包装车间</a-select-option>
                  <a-select-option :key="2" > 品质检验</a-select-option>
            </a-select> 
            <a-radio style="width:3.5%;margin-right:0.5%;"  :value="6">全部</a-radio> 
            <a-select  style="width:7%;margin-right:0.5%;">
                  <a-select-option default-value="-是否打印" :key="1" > -是否打印</a-select-option>
                  <a-select-option :key="2" > 未打印</a-select-option>
                  <a-select-option :key="3" > 已打印</a-select-option>
            </a-select>                        -->
              <a-date-picker  
                  style="margin-right:0.5%;"              
                  format="YYYY-MM-DD HH:mm:ss"                
                  :showTime="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"                
                  placeholder="开始时间(投料时间)"
                  @change="onChange1"
                  @ok="onOk1"
              />
              <a-date-picker  
                  style="margin-right:0.5%;"              
                  format="YYYY-MM-DD HH:mm:ss"                
                  :showTime="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"                
                  placeholder="结束时间(投料时间)"
                  @change="onChange2"
                  @ok="onOk2"
              />     
              <a-button type="primary" @click="searchClick" style="margin-right:0.5%;">搜索</a-button>
              <a-button type="primary" @click="delClick" style="margin-right:0.5%;">删除过序</a-button>
          </div>
        <div class="leftContent">
          <a-table 
            :columns="columns" 
            :dataSource="cardListData" 
            :customRow="onClickRow"
            :pagination="pagination" 
            :rowKey="'id'"  
            :scroll="{y: 670 }"          
            :loading="cardListTableLoading"
            @change="handleTableChange"
            :rowClassName="isRedRow"
            :class="cardListData.length ? 'min-table':''"
          >
            <span slot="num" slot-scope="text, record, index" >
              {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
            <template slot="orderNo" slot-scope="record">
              <a  style="color: #428bca" @click.stop="details(record)">{{record.orderNo}} </a>
            </template>
            <template slot="cardNo" slot-scope="record">
              <a  style="color: #428bca" @click.stop="CarDetails(record)">{{record.cardNo}} </a>
            </template>  
            <template slot="currentStepName" slot-scope="record">
              <a  style="color: #428bca" @click.stop="cardNoClick(record)">{{record.currentStepName}} </a>
            </template>                    
            <template slot="action" slot-scope="" >             
              <p style="color:#428bca;" >异常警示</p>
              <p style="color:#428bca;" >表面处理~外形</p>
              <p style="color:#428bca;" >表面处理~飞针</p>
              <p style="color:#428bca;" >飞针~外形</p>
              <p style="color:#428bca;" >表面处理~测试架</p>
              <p style="color:#428bca;" >外形~测试架</p>
            </template>             
            <template slot="currentStatus" slot-scope="record">
              <span  >{{record.statusStr}} / <br/>{{record.isPrint == 0 ? '未打印' : '/已打印'}} </span> 
            </template>
            <template slot="currentNum" slot-scope="record">
              <div>               
                <span  >{{record.currentNum === null ? 0 : record.currentNum }} {{'/' + record.num}} </span>                
              </div>
            </template>
            <template slot="deliveryType" slot-scope="record" >
              <span >
                <p>{{record.deliveryType.indexOf("小时") != -1 ? record.deliveryType : record.deliveryType + "天"}} </p>
              </span>
            </template> 
            <!-- <template slot="proportion"  >
              <span style="position:relative">   
                <i style="position:absolute;color:white;left:20%;z-index:99;">{{show}}</i>                
                <a-progress :strokeWidth="20"                
                :show-info="false"
                :percent="(percent*100)"
                status="active"
                strokeColor="#ff9900" >
                
              </a-progress>
              </span>
            </template>               -->
          </a-table>
        </div>
      </div>
      <a-modal
        title="过数详情"
        :visible="modelVisible1"
        @cancel="handleCancel"       
        :footer="null"
        :width="750"
      >
        <excess-details  ref="ExcessDetails"  :ExcessData="ExcessData" />      
      </a-modal>
    </a-spin>
</template>
<script>
import moment from "moment";
import {mapState,} from 'vuex';
import ExcessDetails from "@/pages/OrderManagement/ProgressManagement/modules/ExcessDetails";
import {
  proCardInfoList,
  deleteStep,
} from "@/services/scgl/OrderManagement/ControlCard"
import {
  cardFlowList,
} from "@/services/scgl/OrderManagement/progress"
const columns = [
  {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: "center",    
      scopedSlots: { customRender: 'num' },
      width: "4%",
    },
    {
      title: "操作",
      scopedSlots: { customRender: 'action' },
      align: "left",
      width: "10%", 
    },
    {
      title: "管制卡编号",
      scopedSlots: { customRender: 'cardNo' },   
      className:'userStyle',
      align: "left",
      width: "9%",
    },
    {
      title: "拼版编号",
      align: "left",
      width: "8%",
      className:'userStyle',
      scopedSlots: { customRender: 'orderNo' },  
    },
    {
      title: "交期类型",
      width: "5%",
      align: "left",
      scopedSlots:{customRender:'deliveryType'}
    },
    {
      title: "投料时间",
      dataIndex:"createTime",
      width: "6%",
      align: "left",
    }, 
    {
      title: "投料人员",
      dataIndex: "adminName",
      width: "4%",
      align: "left",
    },
    {
      title: "状态/打印",
      align: "left",
      width: "4%",
      scopedSlots: { customRender: 'currentStatus' },  
    },
    {
      title:'当前工序',
      className:'btosty',
     
      children:[
      {
      title: "工序名",
      // dataIndex: "currentStepName",  
      align: "left",
      scopedSlots: { customRender: 'currentStepName' },  
      width: "3%",
    },
    {
      title: "过数/投料",  
      align: "left",
      width: "4%",
      scopedSlots: { customRender: 'currentNum' },  
    },
    {
      title: "过数时间",
      dataIndex: "currentTime", 
      align: "left",
      width: "5%",
    },
    {
      title: "计划/分",
      dataIndex: "totalTimeSpan",      
      align: "left",
      width: "4%",
    },
    {
      title: "耗时/分",
      dataIndex: "pastTimeSpan",    
      align: "left",
      width: "4%",
    },
    {
      title: "剩余/分",
      dataIndex: "syTimeSpan",  
      align: "left",
      width: "4%",
    },
    {
      title: "完成比例",
      align: "left",
      width: "4%",
      scopedSlots: { customRender: 'proportion' },  
    },
    {
      title: "起始时间",
      dataIndex: "stepUserTime",    
      align: "left",
      width: "4%"
    },
    {
      title: "结束时间",
      dataIndex: "stepUserEndTime",  
      align: "left",
      width: "4%",
    },
    {
      title: "板材价格",
      dataIndex: "boardPrice",       
      align: "left",
      width: "4%", 
    },
      ]
    },
    
    {
      title: "特殊需求",
      dataIndex: "remark",
      align: "left",
      width: "4%",
    },
  ]
export default{
    name:'',
    inject:['reload'],
    components:{ExcessDetails,},
    data(){
        return{
          spinning:false,
          formData:{
              Status:0,
              CardNo:"",
              OrderNo:"",             
              StartCreateTime:"",
              EndCreateTime:"",
          },
          columns,
          cardListTableLoading:false,
          pagination: {
            pageSize: 20,
            pageIndex: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
          },
          cardListData:[],
          cardNo:'',
          modelVisible1:false,
          ExcessData:[],
          percent:1/17,
          show:'1/17',
        }
    },
    mounted(){
      this.getOrderList()
    },
    computed: {
    ...mapState('account', ['user',]),  
  }, 
  methods: {
    moment,
    onChange1 (value, dateString) {
      this.formData.StartCreateTime = dateString
        console.log('Selected Time: ', value)
        console.log('Formatted Selected Time: ', dateString)
    },
    onOk1 (value) {
        console.log('onOk: ', value)
    },
    onChange2 (value, dateString) {
      this.formData.EndCreateTime = dateString
        console.log('Selected Time: ', value)
        console.log('Formatted Selected Time: ', dateString)
    },
    onOk2 (value) {
        console.log('onOk: ', value)
    },  
    // 获取订单
    getOrderList(queryData){
      // let params = {
      //   'PageIndex': this.pagination.current,
      //   'PageSize' : this.pagination.pageSize,
      // }
      let params = {
        ...this.pagination,
      }
      var obj = Object.assign(params,queryData)
      console.log('par' ,obj)
      this.cardListTableLoading = true;
      proCardInfoList (obj).then(res => {
        if (res.code) {
          this.cardListData = res.data.items;
          this.pagination.total = res.data.totalCount;
        }
      }).finally(()=> {
        this.cardListTableLoading = false;

      })
    },
    isRedRow(record) {
      let strGroup = []      
      if (record.cardNo && record.cardNo == this.cardNo) {
        strGroup.push('rowBackgroundColor')
      }      
      return strGroup
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.cardNo);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record           
            this.cardNo = record.cardNo
          },
        }
      }
    },    
    // handleTableChange(pagination) {
    //   this.pagination.current=pagination.current
    //   this.getOrderList()
    // }, 
    handleTableChange(pagination, ) {
      this.pagination.pageIndex=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      this.getOrderList()
    }, 
    searchClick(){
      let params = this.formData
      console.log("XXY",params)
      params.Status = Number(this.formData.Status)
      this.getOrderList(params)
      this.pagination.pageIndex = 1;
      console.log('搜索',this.formData,params)
    }, 
    // 删除过序
    delClick(){
      if(!this.cardNo){
        this.$message.warning('请选择工序')
        return
      }
      let params = {
        "cardNo": this.cardNo,
        "account": this.user.userName,
        "name": this.user.name,
      }
      if (confirm('确认删除过序？')) {
        this.spinning = true
        deleteStep(params).then(res => {
          if (res.code) {
            this.$message.success('已删除')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })
      }

    },
     // 订单详情跳转
     details(record){
      this.$router.push({path:'orderDetail1',query:{ id:record.orderNo, } ,})
    },
     // 管制卡详情跳转
     CarDetails(record){
      console.log('121',record)
      this.$router.push({path:'cardDetail',query:{id:record.id} ,})
    },  
    // 过数详情
    cardNoClick(record){
      cardFlowList(record.cardNo).then(res=>{
        if(res.code){
          this.ExcessData = res.data
        }else{
          this.$message.error(res.message)
        }
      })
      this.modelVisible1 = true
    },   
    handleCancel(){
      this.modelVisible1 = false
    },             
  }
}
</script>
<style scoped lang="less">
/deep/.ant-progress-inner{
  border-radius: 0 !important;
  background-color: #cdcccc;
}
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
  .content{
    height:44px;
    margin-left:6px;
  }
  .ant-input,.ant-select{
    width:8%;
    margin-right:0.5%;
    margin-top:6px;
  }
.projectCard {
  /deep/.userStyle{
    user-select: all!important;
  }
  height: 834px;
  width: 100%;
  background: #FFFFFF;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  .leftContent {
    .min-table {      
      .ant-table-body{
        min-height:730px;
        overflow: auto;
      }
    }    
    position: relative;
    height:734px;
    width: 100%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    position: absolute;
    margin: 10px 0 0 10px;
  }

  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table-thead > tr > th {
    // padding:3px 0!important;
    // border-right:1px solid #efefef;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table{
    .btosty{
      border-bottom:1px solid #efefef;
    }
    min-height: 740px;
    .ant-table-thead > tr > th{
      padding: 3px 4px; 
      border-top:1px  solid #efefef;     
      border-right:1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 3px 4px!important;
      border-right:1px solid #efefef;
    }
    tr.ant-table-row-selected td {
     background:rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}
</style>

