import { request, METHOD } from "@/utils/request";
// 订单列表
export function orderEnquiryPageList(params) {
  return request("/api/app/pcb-order-list/order-enquiry-page-list", METHOD.GET, params);
}
// 上传文件
export function upLoadEnquiryFile(params, CustNo, GUID) {
  return request(`/api/app/pcb-order/up-load-enquiry-file?CustNo=${CustNo}&MD5Code=${GUID}`, METHOD.POST, params);
}
export function upLoadEnquiryFileV3(params, CustNo) {
  return request(`/api/app/pcb-order/up-load-enquiry-file-v3?CustNo=${CustNo}`, METHOD.POST, params);
}
export function upLoadEnquiryFileV4(params, CustNo) {
  return request(`/api/app/pcb-order/up-load-enquiry-file-v4?CustNo=${CustNo}`, METHOD.POST, params);
}
//文件检查
export function checkorderfile(params) {
  return request(`/api/app/pcb-order/check-order-file`, METHOD.POST, params);
}
//2024/5/8文件检查接口更改 订单询价
export function inquirycheckorderfile(params) {
  return request(`api/app/order-inquiry/check-order-file`, METHOD.POST, params);
}
// 分片上传
export function upLoadEnquiryFileV2(params) {
  return request(`/api/app/pcb-order/up-load-enquiry-file-v2`, METHOD.POST, params);
}

// 新增
export function orderAdd(params) {
  return request("/api/app/pcb-order/order-add", METHOD.POST, params);
}
// 新增 2024/5/8订单询价
export function inquiryorderAdd(params) {
  return request("/api/app/order-inquiry/order-add", METHOD.POST, params);
}
// 编辑
export function orderUpdate(params) {
  return request("/api/app/pcb-order/order-update", METHOD.POST, params);
}
// 订单确认
export function orderAudit(Id) {
  return request(`/api/app/pcb-order/order-audit`, METHOD.POST, Id);
}
// export function orderAudit (Id) {
//     return request(`/api/app/pcb-order/order-audit`, METHOD.POST,Id)
// }
// 订单确认 2024/5/8订单询价
export function inquiryorderAudit(Id) {
  return request(`/api/app/order-inquiry/order-audit`, METHOD.POST, Id);
}
// 删除型号
export function orderDelete(Id) {
  return request(`/api/app/order-inquiry/order-delete`, METHOD.POST, Id);
}
// 删除型号 2024/5/8订单询价
export function inquiryorderDelete(Id) {
  return request(`/api/app/order-inquiry/order-delete`, METHOD.POST, Id);
}
// 获取市场信息中的选择项
export function mktConfig() {
  return request(`/api/app/order-pre2/mkt-config `, METHOD.GET);
}
// 获取订单信息
export function pcbOrder(id) {
  return request(`/api/app/pcb-order/${id}/by-id`, METHOD.GET);
}
// 客户代码选择项 2024/5/9
export function mktCustNo() {
  return request(`/api/app/order-inquiry/mkt-cust-no`, METHOD.GET);
}
//客户代码最新（带黑名单客户版本）
export function mktCustNov2(TradeType) {
  return request(TradeType ? `/api/app/order-inquiry/mkt-cust-no-v2?TradeType=${TradeType}` : `/api/app/order-inquiry/mkt-cust-no-v2`, METHOD.GET);
}
// 客户代码选择项yxd
export function mktcustnobyfAC(fac) {
  return request(`/api/app/order-inquiry/mkt-cust-no-by-fAC/${fac}`, METHOD.GET);
}
//客户代码最新（带黑名单客户版本）yxd
export function mktcustnobyfACV2(fac) {
  return request(`/api/app/order-inquiry/mkt-cust-no-by-fACV2/${fac}`, METHOD.GET);
}
// 客户代码--取单设置
export function mktcustnobyfACV3(UserId) {
  return request(`/api/app/order-inquiry/mkt-cust-no-by-fACV3/${UserId}`, METHOD.GET);
}
//订单询价接单工厂
export function factroylist() {
  return request(`/api/app/e-mSTMtr-vendor-no/factroy-list`, METHOD.GET);
}
export function updateFile(params) {
  return request(`/api/app/verify-nope-add/update-file`, METHOD.POST, params);
}
export default {};
