<!-- 车间管理-磨板管理-右边数据 -->
<template>
  <a-card :bordered="false">
          <a-table
              :rowKey="(record,index)=>{return index}"
              :columns="columns2"
              :dataSource="machineStatuList"
              :pagination="false"
              :loading="machineStatuLoad"
              :keyboard="false"
              :bordered="true"
              :maskClosable="false"
          >
          </a-table>
  </a-card>
</template>

<script>

const columns2 = [

  {
    title: "状态",
    dataIndex: "state_",
    width: 50,
    align: 'center',
  },
  {
    title: "总孔数",
    dataIndex: "muCount_",
    width: 60,
    align: 'center',
  },
  {
    title: "面积(㎡)",
    dataIndex: "area_",
    align: 'center',
    customRender: (text, record) => {
      return text.toFixed(2)
    }
  },
  {
    title: "PNL数",
    dataIndex: "pnL_",
    width: 65,
    align: 'center',
  },
  {
    title: "款数",
    dataIndex: "count_",
    width: 50,
    align: 'center',
  },
]

export default {
  name:'Center',
  props:['machineStatuLoad','machineStatuList'],
  data () {
    return {
      columns2,
    }
  },
}
</script>

<style lang="less" scoped>
.machine {
  user-select: none;
  height: 100%;
  display: flex;
  
  .right {
    width: 70%;
    .bot,.top {
      /deep/ .ant-table-body {
        min-height: 0;        
      }
    }
    .top {
      /deep/ .ant-table-wrapper .ant-table-thead tr th {
        padding: 5px 0
      }
    }
    button{
      padding:0  5px;
      border-radius: 6px;
      height: 24px;
      line-height: 24px;
      background-color: #ff9900;
      color:white;
      
    }
  }
  /deep/ .ant-table-tbody {
    // .ant-table-row{
    //   td:first-child{
    //     user-select: all;
    //   }
    // }
    .clickRowStyl {
      background: #fff9e6;
    }
    .ant-input {
      padding: 0;
      border: 0;
      border-radius: 0;
      margin: -5px 0;
      text-align: center;
    }
  }
  
}


</style>