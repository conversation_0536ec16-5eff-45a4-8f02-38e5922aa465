<!-- 物料管理-物料录入-查询 -->
<template>
  <div ref="SelectBox">
    <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 14 }">
      <a-form-item label="物料类型">
        <a-select
          allowClear
          v-model="queryForm.MaterType_"
          placeholder="请选择物料类型"
          :getPopupContainer="() => this.$refs.SelectBox"
          showSearch
          :filter-option="filterOption"
        >
          <a-select-option v-for="(item, index) in materialtype" :key="index" :value="item.id" :label="item.caption_">
            {{ item.caption_ }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="板材型号" v-if="queryForm.MaterType_ == 908">
        <a-select
          allowClear
          v-model="queryForm.coreTypeCodes_"
          :getPopupContainer="() => this.$refs.SelectBox"
          showSearch
          optionFilterProp="label"
          :filter-option="filterOption"
        >
          <a-select-option v-for="(item, index) in platetypenew" :key="index" :value="item.coreType_" :label="item.coreType_">
            {{ item.coreType_ }}
          </a-select-option>
          <!-- <a-select-option  v-for="(item,index) in mapKey(selectOption.FR4Type)" :key="index" :value="item.value" :lable="item.lable" :title="item.lable">
          {{ item.lable }}
      </a-select-option> -->
        </a-select>
      </a-form-item>
      <a-form-item label="TG值" v-if="queryForm.MaterType_ == 908">
        <a-select v-model="queryForm.tGType_" allowClear :getPopupContainer="() => this.$refs.SelectBox" :filter-option="filterOption">
          <a-select-option v-for="(item, index) in pptgvalue" :key="index" :value="item.tG_">{{ item.tG_ }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="板厚" v-if="queryForm.MaterType_ == 908">
        <a-input allowClear v-model="queryForm.core_" />
      </a-form-item>
      <a-form-item label="顶铜" v-if="queryForm.MaterType_ == 908">
        <!-- <a-input allowClear  v-model='queryForm.topOZ_' /> -->
        <a-select
          v-model="queryForm.topOZ_"
          showSearch
          option-filter-prop="children"
          :filter-option="filterOption"
          allowClear
          :getPopupContainer="() => this.$refs.SelectBox"
        >
          <a-select-option v-for="(item, index) in topoz" :key="index" :value="item">{{ item }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="底铜" v-if="queryForm.MaterType_ == 908">
        <!-- <a-input allowClear  v-model='queryForm.bomOZ_' /> -->
        <a-select
          v-model="queryForm.bomOZ_"
          showSearch
          option-filter-prop="children"
          :filter-option="filterOption"
          allowClear
          :getPopupContainer="() => this.$refs.SelectBox"
        >
          <a-select-option v-for="(item, index) in bomoz" :key="index" :value="item">{{ item }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="内外铜箔" v-if="queryForm.MaterType_ == 909">
        <a-select v-model="queryForm.innerOrOuter" allowClear :getPopupContainer="() => this.$refs.SelectBox" :filter-option="filterOption">
          <a-select-option value="910"> 外层 </a-select-option>
          <a-select-option value="911"> 内层 </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="铜厚" v-if="queryForm.MaterType_ == 909">
        <a-input allowClear v-model="queryForm.cU_" />
      </a-form-item>
      <a-form-item label="厚度" v-if="queryForm.MaterType_ == 909">
        <a-input allowClear v-model="queryForm.cuThickness_" />
      </a-form-item>
      <!-- <a-form-item label="工厂" v-if="queryForm.MaterType_ == 909">
      <a-input allowClear  v-model='queryForm.joinFactoryId' />
    </a-form-item>     -->
      <a-form-item label="PP类别" v-if="queryForm.MaterType_ == 914">
        <a-select
          v-model="queryForm.ppTypeCodes_"
          showSearch
          option-filter-prop="children"
          allowClear
          :filter-option="filterOption"
          :getPopupContainer="() => this.$refs.SelectBox"
        >
          <a-select-option v-for="(item, index) in pptype" :key="index" :value="item.valueMember">{{ item.text }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="TG值" v-if="queryForm.MaterType_ == 914">
        <!-- <a-input allowClear  v-model='queryForm.tGType_' /> -->
        <a-select v-model="queryForm.ppTg" allowClear :filter-option="filterOption" :getPopupContainer="() => this.$refs.SelectBox">
          <a-select-option v-for="(item, index) in pptgvalue" :key="index" :value="item.tG_">{{ item.tG_ }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="PP类型" v-if="queryForm.MaterType_ == 914">
        <a-input allowClear v-model="queryForm.pP_" />
      </a-form-item>
      <a-form-item label="内DK" v-if="queryForm.MaterType_ == 914">
        <a-input allowClear v-model="queryForm.dkInner_" />
      </a-form-item>
      <a-form-item label="外DK" v-if="queryForm.MaterType_ == 914">
        <a-input allowClear v-model="queryForm.dkOuter_" />
      </a-form-item>
      <a-form-item label="规格" v-if="queryForm.MaterType_">
        <a-input allowClear v-model="queryForm.materSpec_" />
      </a-form-item>
      <a-form-item label="厂商" v-if="queryForm.MaterType_ == 908 || queryForm.MaterType_ == 914">
        <a-select v-model="queryForm.materVendor_" allowClear :filter-option="filterOption" :getPopupContainer="() => this.$refs.SelectBox">
          <a-select-option v-for="(item, index) in VendorData" :key="index" :value="item.id">{{ item.verdorName_ }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="编码" v-if="queryForm.MaterType_">
        <a-input allowClear v-model="queryForm.ERPKey_" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
export default {
  name: "QueryInfo",
  props: ["materialCategory", "materialtype", "platetypenew", "selectOption", "pptype", "pptgvalue", "topoz", "bomoz", "VendorData"],
  data() {
    return {
      queryForm: {
        MaterType_: "",
        materSpec_: "",
      },
      autoFocus: true,
    };
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-input {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}
.ant-modal-body {
  .ant-form-item {
    margin-bottom: 8px;
  }
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
</style>
