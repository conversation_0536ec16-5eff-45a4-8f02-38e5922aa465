<template>
  <page-layout>
    <a-card>
      <div>
        <p>sub-element align left</p>
        <a-row type="flex" justify="start">
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
        </a-row>

        <p>sub-element align center</p>
        <a-row type="flex" justify="center">
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
        </a-row>

        <p>sub-element align right</p>
        <a-row type="flex" justify="end">
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
        </a-row>

        <p>sub-element monospaced arrangement</p>
        <a-row type="flex" justify="space-between">
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
        </a-row>

        <p>sub-element align full</p>
        <a-row type="flex" justify="space-around">
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
          <a-col :span="4">col-4</a-col>
        </a-row>
      </div>
    </a-card>
  </page-layout>
</template>
<script>
import PageLayout from "@/layouts/PageLayout";
export default {
  components: { PageLayout },
  data() {
    return {};
  }
};
</script>
<style lang="less">
p {
  text-align: center;
  padding: 5px;
}
.ant-col:nth-child(odd) {
  background: #49b9ed;
  padding: 8px;
}
.ant-col:nth-child(even) {
  background: #00a0e9;
  padding: 8px;
}
</style>
