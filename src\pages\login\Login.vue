<template>
  <common-layout>
    <div class="login_box" ref="login_box">
      <div class="login_left">
        <!-- <img src="../../assets/img/loginlogo.png" alt=""> -->
      </div>
      <div class="login_right">
        <div class="top">
          <!-- <img class="title" style='width:40px;margin:0 15px;' src="@/assets/img/bn2.png">
          <span style="color:@title-color;font-size: 30px;font-weight: 600;">百 能</span> -->
          <div class="header" style="display: flex; position: relative; left: 25%">
            <img class="title" style="width: 40px; margin: 0 15px" src="@/assets/img/bn2.png" />
            <span class="title">{{ systemName }}</span>
          </div>
        </div>
        <div style="display: flex">
          <div style="padding-right: 30px">
            <div style="font-size: 16px; padding-left: 24px; padding-top: 11px">
              <a-icon type="wechat" style="font-size: 24px; color: rgb(9, 210, 79)"></a-icon> 微信扫码登录
            </div>
            <div style="display: flex">
              <a-spin :spinning="loggingwx" :tip="loggingwx ? '登录中...' : ''">
                <div id="weixin" class="qrcode" v-show="!isShowForm" style="height: 280px; width: 200px"></div>
              </a-spin>
              <!-- <a-divider type="vertical" style="height: 260px; background-color: #ccc9c9;" /> -->
            </div>
          </div>
          <div style="width: 58%">
            <a-tabs default-active-key="1" @change="callback" v-if="login">
              <a-tab-pane key="1" tab="账号登录">
                <div class="login">
                  <a-form @submit="onSubmit" :form="form">
                    <a-alert type="error" :closable="true" v-show="error" :message="error" showIcon style="margin-bottom: 24px" />
                    <a-form-item>
                      <a-input
                        autocomplete="autocomplete"
                        class="input1"
                        size="large"
                        placeholder=""
                        v-decorator="[
                          'name',
                          {
                            rules: [{ required: true, message: '请输入账户名', whitespace: true }],
                          },
                        ]"
                        @blur="verification"
                      >
                        <a-icon slot="prefix" type="user" />
                      </a-input>
                    </a-form-item>
                    <a-form-item>
                      <a-input
                        size="large"
                        class="input2"
                        placeholder="*"
                        autocomplete="autocomplete"
                        type="password"
                        v-decorator="[
                          'password',
                          {
                            rules: [{ required: true, message: '请输入密码', whitespace: true }],
                          },
                        ]"
                      >
                        <a-icon slot="prefix" type="lock" />
                      </a-input>
                    </a-form-item>
                    <a-form-item v-if="inputStat">
                      <a-row :gutter="8" style="margin: 0 -4px">
                        <a-col :span="16">
                          <a-input
                            size="large"
                            placeholder="captcha"
                            v-decorator="[
                              'smsCode',
                              {
                                rules: [{ required: true, message: '请输入验证码', whitespace: true }],
                              },
                            ]"
                          >
                            <a-icon slot="prefix" type="mail" />
                          </a-input>
                        </a-col>
                        <a-col :span="8" style="padding-left: 4px">
                          <a-button style="width: 100%" class="captcha-button" size="large" :disabled="disabledBtn" @click="countDown">{{
                            content
                          }}</a-button>
                        </a-col>
                      </a-row>
                    </a-form-item>
                    <a-form-item>
                      <a-button
                        :loading="logging"
                        style="width: 100%; margin-top: 24px"
                        size="large"
                        htmlType="submit"
                        type="primary"
                        class="login_btn"
                        >登录</a-button
                      >
                    </a-form-item>
                  </a-form>
                </div>
              </a-tab-pane>
              <a-tab-pane key="2" tab="短信登录" force-render>
                <phone-login :typpe="'web'" />
              </a-tab-pane>
            </a-tabs>
            <register-form v-else-if="activekey != 3" @register="register"></register-form>
            <div style="width: 100%; text-align: center; position: relative; top: -11px; font-size: 13px" v-if="activekey != 3">
              {{ login ? "还没有账号？" : "已有账号？" }}<a disabled @click="register">{{ login ? "立即注册" : "马上登录" }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-modal
      title="修改密码"
      :visible="dataVisible"
      @cancel="reportHandleCancel"
      @ok="handleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
    >
      <div v-if="changType == 'reset'" style="color: red; font-size: 12px; padding-left: 100px">*{{ infoMessage }}</div>
      <change-password ref="ChangePassword" />
    </a-modal>
  </common-layout>
</template>

<script>
import db from "@/utils/db";
import { getinformationremind } from "@/services/system/InformationRemind.js";
import CommonLayout from "@/layouts/CommonLayout";
import { login, loginWX, applicationConfiguration, verificationName, getVerification, getHomePage } from "@/services/user";
import { getUserInfo } from "@/services/identity/user";
import PhoneLogin from "@/pages/login/modules/PhoneLogin";
import { setAuthorization } from "@/utils/request";
import { loadRoutes } from "@/utils/routerUtil";
import { mapMutations } from "vuex";
import RegisterForm from "@/pages/login/modules/RegisterForm";
import ChangePassword from "@/layouts/header/ChangePassword";
import { newPassword } from "@/services/projectDisptch";
import { mapState } from "vuex";
import { setuserName } from "@/utils/request";
import Cookie from "js-cookie";
import wxlogin from "vue-wxlogin";
import axios from "axios";
import qs from "querystring";
export default {
  name: "Login",
  components: { RegisterForm, CommonLayout, PhoneLogin, ChangePassword },
  data() {
    return {
      logging: false,
      error: "",
      form: this.$form.createForm(this),
      visible: false,
      confirmLoading: false,
      tenantName: "",
      content: "发送验证码", // 按钮里显示的内容
      totalTime: 60, //记录具体倒计时时间
      disabledBtn: false,
      inputStat: false, //验证码输入框
      login: true,
      dataVisible: false,
      changType: "",
      infoMessage: "",
      isShowForm: false,
      activekey: "1",
      loggingwx: false,
      loginWXFlg: false,
    };
  },
  computed: {
    systemName() {
      return this.$store.state.setting.systemName;
    },
    ...mapState("account", ["user", "buryingpoint"]),
  },
  watch: {
    totalTime(val) {
      if (val == 0) {
        this.disabledBtn = false;
        this.content = "发送验证码";
      }
    },
  },
  mounted() {
    let data = this.$route.query;
    if (data.isRegister == 1) {
      this.login = false;
    } else {
      this.login = true;
    }
    this.dataprocessing(this.buryingpoint);
    if (data.code) {
      this.activekey = 3;
      this.loggingwx = true;
      (this.loginWXFlg = true),
        loginWX(data.code)
          .then(this.afterLogin)
          .catch(() => {
            this.logging = false;
          });
    }
    let _this = this;
    new WxLogin({
      id: "weixin",
      appid: "wx564945107027ca92", // 这个appid要填死
      scope: "snsapi_login",
      // 扫码成功后重定向的接口
      redirect_uri: "http://ems.bninfo.com/login",
      // state填写编码后的url
      // state: encodeURIComponent(window.btoa("http://127.0.0.1:8080" + _this.$route.path)),
      // 调用样式文件
      href: "data:text/css;base64,LmltcG93ZXJCb3ggLnRpdGxle2Rpc3BsYXk6IG5vbmU7fQouaW1wb3dlckJveCAucXJjb2RlIHsgd2lkdGg6IDIwMHB4OyBtYXJnaW4tdG9wOiAxNXB4OyBib3JkZXI6IDFweCBzb2xpZCAjZTJlMmUyO30KLmltcG93ZXJCb3ggLnN0YXR1cy5zdGF0dXNfYnJvd3NlciAgcDpudGgtY2hpbGQoMil7ZGlzcGxheTogbm9uZTt9Cg==",
    });
    window.addEventListener("resize", this.handleResize, true);
    if (window.innerWidth <= 576) {
      this.$refs.login_box.style.width = "471px";
    } else if (window.innerWidth <= 801 && window.innerWidth >= 576) {
      this.$refs.login_box.style.width = "554px";
      var a = document.getElementsByClassName("input1")[0];
      a.style.width = "290px";
      var b = document.getElementsByClassName("input2")[0];
      b.style.width = "290px";
    } else {
      this.$refs.login_box.style.width = "";
      this.$refs.login_box.style.position = "relative";
      this.$refs.login_box.style.left = "0";
    }
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  methods: {
    ...mapMutations("account", ["setUser", "setPermissions", "setRoles", "clearBuryingPoint", "dataprocessing"]),
    ...mapMutations("setting", ["setinfolength"]),
    //账号验证
    handleResize() {
      this.$nextTick(() => {
        var a = document.getElementsByClassName("input1")[0];
        var b = document.getElementsByClassName("input2")[0];
        if (window.innerWidth <= 576) {
          this.$refs.login_box.style.width = "471px";
          a.style.width = "100%";
          b.style.width = "100%";
        } else if (window.innerWidth <= 801 && window.innerWidth >= 576) {
          this.$refs.login_box.style.width = "554px";
          a.style.width = "290px";
          b.style.width = "290px";
        } else {
          this.$refs.login_box.style.width = "";
          this.$refs.login_box.style.position = "relative";
          this.$refs.login_box.style.left = "0";
          a.style.width = "100%";
          b.style.width = "100%";
        }
      });
    },
    verification() {
      let userName = this.form.getFieldValue("name");
      verificationName(userName).then(res => {
        if (res.data) {
          this.inputStat = true;
        } else {
          this.inputStat = false;
        }
      });
    },
    callback(key) {
      this.activekey = key;
    },
    countDown() {
      this.disabledBtn = true;
      let clock = window.setInterval(() => {
        this.totalTime--;
        this.content = this.totalTime + "s后重新发送";
        if (this.totalTime == 0) {
          this.disabledBtn = false;
          this.content = "发送验证码";
          this.totalTime = 60;
          clearInterval(clock);
        }
      }, 1000);
      let parmas = {};
      parmas.userName = this.form.getFieldValue("name");
      parmas.smsFrom = "MES后台";
      parmas.sendType = "登录验证";
      getVerification(parmas).then(res => {});
    },
    onSubmit(e) {
      e.preventDefault();
      this.form.validateFields(err => {
        if (!err) {
          this.logging = true;
          var name = "";
          if ([...this.form.getFieldValue("name")].length > 20) {
            name = [...this.form.getFieldValue("name")].splice(0, 20).join("");
          } else {
            name = this.form.getFieldValue("name");
          }
          var password = "";
          if ([...this.form.getFieldValue("password")].length > 20) {
            password = [...this.form.getFieldValue("password")].splice(0, 20).join("");
          } else {
            password = this.form.getFieldValue("password");
          }
          // const tenant = this.tenant;
          const smsCode = this.form.getFieldValue("smsCode");
          login(name, password, smsCode)
            // login(name, password, )
            .then(this.afterLogin)
            .catch(() => {
              this.logging = false;
            });
        }
      });
    },
    reportHandleCancel() {
      this.dataVisible = false;
      this.logging = false;
      this.form.setFieldsValue({
        name: "",
        password: "",
      });
    },
    handleOk() {
      let params = this.$refs.ChangePassword.newNumber;
      newPassword(params)
        .then(res => {
          if (res) {
            this.$message.success("密码修改成功");
          }
        })
        .finally(() => {
          this.dataVisible = false;
          this.logging = false;
          this.form.setFieldsValue({
            name: "",
            password: "",
          });
        });
    },
    async clearAllCache() {
      try {
        await db.clearStore("coreListDataMkt");
        await db.clearStore("coreListDataPro");
      } catch (error) {
        console.error("清空失败:", error);
      }
    },
    afterLogin(res) {
      const loginRes = res;
      if (loginRes) {
        let userinfo = JSON.parse(localStorage.getItem("UserInformation"));
        localStorage.clear();
        this.clearAllCache();
        localStorage.setItem("UserInformation", JSON.stringify(userinfo));
        // const { user, permissions, roles } = loginRes.data;
        // this.setUser(user);
        // this.setPermissions(permissions);
        // this.setRoles(roles);
        setAuthorization({
          token: loginRes.access_token,
          expireAt: new Date(new Date().getTime() + loginRes.expires_in),
        });
        if (!this.loginWXFlg) {
          const password = this.form.getFieldValue("password");
          var regex = new RegExp(/^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$)([^\u4e00-\u9fa5\s]){6,20}$/);
          if (!regex.test(password)) {
            // console.log('密码',password)
            this.dataVisible = true;
            this.changType = "other";
            return;
          }
        }

        applicationConfiguration().then(res => {
          res.currentUser.tenantName = res.currentTenant.name;
          this.setUser(res.currentUser);
          let permissions = this.handlePermissions(res.auth.grantedPolicies);
          this.setPermissions(permissions);
          this.setRoles(res.currentUser.roles);
          loadRoutes();
          setuserName({
            token: this.user.userName,
          });
          getUserInfo(this.user.id).then(res => {
            let that = this;
            if (res.code != undefined && res.code == 0) {
              if (res.message.indexOf("重置密码") != -1) {
                that.dataVisible = true;
                this.changType = "reset";
                this.infoMessage = res.message;
              } else {
                this.$message.error(res.message);
              }
              this.logging = false;
              return;
            } else {
              this.setUser(res);
              this.logging = false;
              this.$message.success("登录成功", 3);
              this.clearBuryingPoint();
              getinformationremind()
                .then(res => {
                  if (res.code) {
                    localStorage.setItem("infolength", res.data.length);
                  }
                })
                .finally(() => {
                  getHomePage()
                    .then(res => {
                      if (res.data) {
                        if (res.data == "/") {
                          this.$router.push(res.data);
                        } else {
                          this.$router.push("/" + res.data);
                        }
                      } else {
                        this.$router.push("/");
                      }
                    })
                    .finally(() => {
                      // this.$router.go();
                    });
                });
            }
          });

          // if(res.currentUser.roles.indexOf('AGV') != -1) {
          //   this.$router.push('/productionManagement/agvDispatch')
          // } else{

          //   this.$router.push('/dashboard/analysis')
          // }
        });
      } else {
        this.error = loginRes.message;
      }
    },
    handlePermissions(obj) {
      let permissions = [];
      if (!obj) {
        return permissions;
      }
      permissions = Object.keys(obj).map(x => {
        return {
          id: x,
          operation: [],
        };
      });
      return permissions;
      // let list = Object.keys(obj).map((x) => {
      //   let n = x.split(".").length - 1;
      //   return {
      //     val: x,
      //     num: n,
      //   };
      // });
      // let idList = list.filter((x) => x.num == 1);
      // permissions = idList.map((x) => {
      //   let operation = list
      //     .filter((y) => y.num == 2 && y.val.indexOf(x.val) > -1)
      //     .map((y) => {
      //       return y.val.split(".")[2];
      //     });
      //   return {
      //     id: x.val,
      //     operation: operation,
      //   };
      // });
      // return permissions;
    },
    register() {
      this.login = !this.login;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 24px;
}
.qrcode iframe {
  padding-left: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
/deep/.ant-btn-lg {
  padding: 0 3px !important;
}
.common-layout {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .login_box {
    background-color: transparent;
    // width: 1080px;
    // width: 540px;
    width: 677px;
    // height: 690px;
    height: 510px;
    display: flex;
    top: 50%;
    transform: translate(0, 2px);
    .login_left {
      width: 50%;
      height: 100%;
      float: left;
      background: #f90;
      // overflow: hidden;
      display: none;
    }
    // .login_right{
    //   float: left;
    //   width: 50%;
    //   height: 100%;
    //   background: #fff;
    //   padding: 160px 75px 0;
    //   // margin-left: 25%;
    // }  //2023/3/21 登录页适配居中
    .login_right {
      width: 100%;
      height: 100%;
      background: #fff;
      // padding: 160px 75px 0;
      padding: 70px 60px 0;
    }
  }
  .top {
    text-align: center;
    margin-bottom: 30px;
    .header {
      height: 44px;
      line-height: 44px;
      a {
        text-decoration: none;
      }
      .logo {
        height: 44px;
        vertical-align: top;
        margin-right: 16px;
      }
      .title {
        font-size: 30px;
        color: @title-color;
        // font-family: "Myriad Pro", "Helvetica Neue", Arial, Helvetica,sans-serif;
        //font-family: PingFangSC-Regular,"Helvetica Neue",Helvetica,Arial,Sans-serif;
        font-weight: 600;
        position: relative;
        top: 2px;
      }
    }
    .desc {
      font-size: 14px;
      color: @text-color-second;
      margin-top: 12px;
      margin-bottom: 40px;
    }
  }
  .login {
    // width: 368px;
    width: 320px;
    margin: 0 auto;
    @media screen and (max-width: 576px) {
      width: 95%;
    }
    @media screen and (max-width: 320px) {
      .captcha-button {
        font-size: 14px;
      }
    }
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
@media (max-width: 800px) {
  .common-layout {
    .login_box {
      width: 100%;
      height: 500px;
      .login_left {
        display: none;
      }
      .login_right {
        width: 100%;
        float: none;
        padding: 60px 20px 0;
        .title {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
