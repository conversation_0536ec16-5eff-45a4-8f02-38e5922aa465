/*
 * @Author: C<PERSON>
 * @Date: 2022-01-07 13:43:58
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-08-04 13:51:31
 * @FilePath: \vue-antd-admin\src\services\identity\user.js
 * @Description:
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved.
 */
import { request, METHOD } from "@/utils/request";
import { transformAbpListQuery } from "@/utils/abp";
export async function getList(params) {
  return request("/api/identity/users", METHOD.GET, transformAbpListQuery(params));
}
export async function getListWithDetails(params) {
  return request("api/app/synchronize-user/abp-user-list", METHOD.GET, transformAbpListQuery(params));
}
//获取继承人员下拉
export async function dimissionuserlist() {
  return request("/api/app/e-mSTSys-user-dimission/user-list", METHOD.GET);
}
//离职继承
export async function setdimissionuser2Otheruser(params) {
  return request("/api/app/e-mSTSys-user-dimission/set-dimission-user2Other-user", METHOD.POST, params);
}
export async function createUpdate(params) {
  if (params.id) {
    return request(`/api/identity/users/${params.id}`, METHOD.PUT, params);
  }
  return request("/api/app/synchronize-user/", METHOD.POST, params);
}
export async function del(id) {
  // return request(`/api/identity/users/${id}`, METHOD.DELETE) 2023/3/1修改
  return request(`/api/app/synchronize-user/${id}/delete-user`, METHOD.POST);
}
export async function delRecovery(id) {
  return request(`/api/app/synchronize-user/recovery-delete/${id}`, METHOD.POST);
}
export async function ForceUpdate(id) {
  return request(`/api/app/synchronize-user/force-update/${id}`, METHOD.POST);
}
export async function get(id) {
  return request(`/api/identity/users/${id}`, METHOD.GET);
}
export function getAssignableRoles() {
  return request("/api/app/synchronize-user/assignable-roles-v2 ", METHOD.GET);
}
export function getRolesByUserId(id) {
  return request(`/api/app/synchronize-user/${id}/roles-v2`, METHOD.GET);
}
export function getOrganizationsByUserId(id, includeDetails = false) {
  return request(`/api/identity/users/${id}/organizations`, METHOD.GET, includeDetails);
}
// export function password(id) {
//     return request(`/api/app/synchronize-user/${id}/password`, METHOD.PUT)
// }
export function password(params) {
  return request(`/api/app/synchronize-user/update-user-password`, METHOD.POST, params);
}
// 获取工厂id列表
export function getFactoryList() {
  return request("/api/app/e-mSTPub-factory-configure/factory-id-list", METHOD.POST);
}
// 获取订单渠道列表
export function getTradeTypeSrcList() {
  return request("api/app/e-mSData-class-list/data-class-list?TypeNo=1002", METHOD.POST);
}
// 新增用户
export function createUser(params, type) {
  return request("api/app/synchronize-user/user", METHOD[type], params);
}
// 修改用户
export function updateUser(params, type) {
  return request("/api/app/synchronize-user/update-user", METHOD.POST, params);
}
// 用户管理模块获取用户信息
export function getUserInfo(id) {
  return request(`/api/app/synchronize-user/${id}/user`, METHOD.GET);
}
// 获取组织架构信息
export function getDepartGroupPostList() {
  return request(`/api/app/e-mSSupplier-module-no/depart-group-post-list`, METHOD.GET);
}
// 获取具有组织架构工厂Id列表
export function getFactoryListByGroup() {
  return request("/api/app/synchronize-user/factory-id-by-group-list", METHOD.POST);
}
// 左边工厂列表与人员统计
export function departGroupPostList() {
  return request("/api/app/synchronize-user/depart-group-post-list", METHOD.GET);
}
// 删除用户
export function delUser(id) {
  return request(`/api/app/synchronize-user/${id}/del`, METHOD.POST);
}
//获取服务器地址
export function parameterlist(JoinFactoryId, Key) {
  return request(`/api/app/pcb-page-types/parameter-list?JoinFactoryId=${JoinFactoryId}&Key=${Key}`, METHOD.GET);
}
export default {
  getList,
  getListWithDetails,
  createUpdate,
  del,
  get,
  getAssignableRoles,
  getRolesByUserId,
  getOrganizationsByUserId,
  password,
  getFactoryList,
  getUserInfo,
};
