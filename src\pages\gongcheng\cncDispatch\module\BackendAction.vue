<!-- 工程管理 - 锣带分派  -按钮 -->
<template>
  <div class="active" ref="active">
    <!--    v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendSend')"-->
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.FormingDispatch.FormingDispatchCNCSend')"
      :class="checkPermission('MES.EngineeringModule.FormingDispatch.FormingDispatchCNCSend') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="() => this.$emit('assignClick')" :loading="assignLoading"> 分派 </a-button>
    </div>
    <!--    <div class="box"  >-->
    <!--      <a-button type="primary" @click=" ()=>this.$emit('backClick')" :loading="backLoading" >-->
    <!--        分派回退-->
    <!--      </a-button>-->
    <!--    </div>-->
    <div class="box showClass">
      <a-button type="primary" @click="orderClick"> 取单 </a-button>
    </div>
    <!-- <div class="box"  v-if="checkPermission('MES.EngineeringModule.FormingDispatch.FormingDispatchCNCOrderFinish')">
      <a-button type="primary" @click=" ()=>this.$emit('finishClick')" :loading="finishLoading" >
        订单完结
      </a-button>
    </div> -->

    <!--    <div class="box" >-->
    <!--      <a-button type="primary" @click ="NodeFallback">-->
    <!--        节点回退-->
    <!--      </a-button>-->
    <!--    </div>-->

    <div class="box showClass">
      <a-button type="primary" @click="FileReplacement"> 完成 </a-button>
      <a-upload accept=".rar,.zip" name="file" ref="fileRef" :before-upload="beforeUpload" :customRequest="httpRequest0">
        <a-button style="width: 80px; display: none"><a-icon type="upload" /></a-button>
      </a-upload>
    </div>
    <div class="box showClass">
      <a-button type="primary" @click="ReplacementClick"> 文件替换 </a-button>
      <a-upload accept=".rar,.zip" name="file" ref="fileRef" :before-upload="beforeUpload1" :customRequest="httpRequest1">
        <a-button style="width: 80px; display: none"><a-icon type="upload" /></a-button>
      </a-upload>
    </div>

    <div class="box showClass">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
    <span class="box" v-if="showBtn">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>

    <!--    <span class="box" style="display: none;">-->
    <!--      <a-button type="dashed" @click="toggleAdvanced">-->
    <!--        {{advanced ? '收起' : '展开'}}-->
    <!--        <a-icon :type="advanced ? 'right' : 'left'" />-->
    <!--      </a-button>-->
    <!--    </span>-->
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { ossUp, upLoadFlyingFile } from "@/services/cncDispatch";

export default {
  name: "BackendAction",
  props: {
    assignLoading: {
      type: Boolean,
    },
    backLoading: {
      type: Boolean,
    },
    finishLoading: {
      type: Boolean,
    },
  },
  data() {
    return {
      advanced: false,
      width: 762,
      collapsed: false,
      orderId: "",
      alypath_rou: "",
      showBtn: false,
    };
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 7;
      if (this.$refs.active.children.length > 7) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = false;
      }
    });
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 762;
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
    // 自动取单
    orderClick() {
      this.$emit("orderClick");
    },
    // 文件替换
    ReplacementClick() {
      this.$emit("ReplacementClick");
    },

    // 节点回退
    // NodeFallback(){
    //   this.$emit('NodeFallback')
    // },
    //完成
    FileReplacement() {
      this.$emit("FileReplacement");
    },
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
      console.log("file", file);
      if (!isFileType) {
        this.$message.error("只支持.rar或.zip格式文件");
      }
      return isFileType;
    },
    async httpRequest0(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          // this.$message.success('添加成功')
          this.$emit("completeClick", res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 文件替换
    beforeUpload1(file) {
      const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
      console.log("file", file);
      if (!isFileType) {
        this.$message.error("只支持.rar或.zip格式文件");
      }
      return isFileType;
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      upLoadFlyingFile(formData).then(res => {
        if (res.code) {
          this.alypath_rou = res.data;
          if (this.alypath_rou) {
            ossUp(this.orderId, this.alypath_rou).then(res => {
              if (res.code == 1) {
                this.$message.success("文件替换成功");
              } else {
                this.$message.error(res.message);
              }
            });
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    clickUpload(id) {
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    clickUpload1(id) {
      this.orderId = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 90px;
    margin-top: 8px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
