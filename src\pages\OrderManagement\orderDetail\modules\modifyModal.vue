<!--生产管理- 订单详情-修改 -->
<template>
  <a-form ref="formRef" :model="formInfo"  :rules="rules" >
    <a-form-item label="板材类型" :label-col="{ span:8}" :wrapper-col="{ span: 16}" >
      <a-select  style="width:60%;margin-right:0.5%;" v-model="formInfo.fR4Type">        
        <a-select-option value="fr4" >FR-4生益</a-select-option>   
        <a-select-option value="fr4jt" >FR-4建滔</a-select-option>  
        <a-select-option value="gj" >FR-4国纪</a-select-option>  
        <a-select-option value="cem1" >CEM-1建滔黄芯</a-select-option> 
        <a-select-option value="aluminum" >国纪铝基板</a-select-option>   
        <a-select-option value="gzaluminum" >广州铝基板</a-select-option>  
        <a-select-option value="fr4pyab1" >FR-4普源</a-select-option>  
        <a-select-option value="ny" >FR-4南亚</a-select-option> 
        <a-select-option value="byaluminum" >广东博钰铝基板</a-select-option>
        <a-select-option value="fr4yja3" > FR-4元集A3（非阻燃）</a-select-option>
        <a-select-option value="fr4jxa3" > FR-4俊萱A3（非阻燃）</a-select-option>
        <a-select-option value="fr4fzrrandom" > 非阻燃不指定板材</a-select-option>
        <a-select-option value="fr4gjab01" > FR-4国纪GF21系列AB0</a-select-option>  
        <a-select-option value="fr4hzh150" >FR-4华正</a-select-option>
        <a-select-option value="fr1jt" > FR-1建涛</a-select-option>
        <a-select-option value="22fjt" > 22F建涛</a-select-option>
        <a-select-option value="cem3jt" > CEM-3建涛</a-select-option>
        <a-select-option value="94hbjt" > 94HB建涛</a-select-option>  
        <a-select-option value="xtaluminum" >鑫泰铝基板</a-select-option>
        <a-select-option value="94hbwlb" > 94HB威利邦</a-select-option>
        <a-select-option value="22fwlb94v0" >22F威利邦94V0</a-select-option>
        <a-select-option value="cem1wlb" >CME-1威利邦</a-select-option>
        <a-select-option value="qxaluminum" > 台湾清晰铝基板</a-select-option>         
        <a-select-option value="22fjtw" >22F建滔（白）</a-select-option>  
        <a-select-option value="94hbmk" >94HB明康</a-select-option>
        <a-select-option value="22fpzw" >22F鹏州（白）</a-select-option>
        <a-select-option value="22fpzy" >22F鹏州（黄）</a-select-option>
        <a-select-option value="22fjb" > 22F金宝</a-select-option>
        <a-select-option value="fr4hrx" >FR-4宏瑞兴</a-select-option>  
        <a-select-option value="f4hrxab" >F-4宏瑞兴(非阻燃)</a-select-option>
        <a-select-option value="rogers4003c" > 罗杰斯/RO4003C</a-select-option>
        <a-select-option value="rogers4350b" >罗杰斯/RO4350B</a-select-option>
        <a-select-option value="cem1jtw" >CEM-1建涛白芯</a-select-option>
        <a-select-option value="22fwlbvi" >22F威利邦/22FVI</a-select-option>
      </a-select> 
    </a-form-item>
    <a-form-item label="铜厚（内/外）" :label-col="{ span:8}" :wrapper-col="{ span: 16}" name="copperThickness">
      <a-input  style="width:20%;margin-right:0.5%;" v-model="formInfo.innerCopperThickness" /> /
      <a-input  style="width:20%;margin-right:0.5%;" v-model="formInfo.copperThickness" /> H填写0.5
         
    </a-form-item>
    <a-form-item label="板厚"  :label-col="{ span:8}" :wrapper-col="{ span: 10}"  prop="boardThickness" refs="boardThickness">
      <a-input     v-model="formInfo.boardThickness"     />        
    </a-form-item>
    <a-form-item label="板材TG值" :label-col="{ span:8}" :wrapper-col="{ span: 16}" name="fR4Tg">
      <a-select  style="width:60%;margin-right:0.5%;"  v-model="formInfo.fR4Tg">        
        <a-select-option value="110" > ≥110</a-select-option>
        <a-select-option value="tg130" > TG130</a-select-option> 
        <a-select-option value="tg150" > TG150</a-select-option>
        <a-select-option value="tg170" > TG170</a-select-option> 
        <a-select-option value="tg135" > TG135</a-select-option> 
        <a-select-option value="tg140" > TG140</a-select-option>
        <a-select-option value="tg125" > TG125</a-select-option> 
        <a-select-option value="280" > >280</a-select-option>             
      </a-select> 
    </a-form-item>
    <a-form-item label="交期" :label-col="{ span:8}" :wrapper-col="{ span: 16}" name="deliveryDate">
      <a-date-picker 
        v-model="formInfo.deliveryDate"              
        valueFormat="YYYY-MM-DD "               
        placeholder="开始交期"
        @change="onChange1"
      />
    </a-form-item>    
    <a-form-item label="申请备注" :label-col="{ span:8}" :wrapper-col="{ span: 10}" name="reMarks">
      <a-textarea v-model="formInfo.reMarks"  allow-clear  />
    </a-form-item>
  </a-form>
</template>

<script>
import moment from "moment";
export default {
  name: "modifyModal",
  props:['formInfo',],
  data(){
    return{ 
      inputStat:false, 
      rules: {
        boardThickness: [
          { required: true, message: "分类名称必须填写", trigger: "blur" },
        ],
      }, 
    }
  },   
  methods: {
    moment,
    onChange1 (value, dateString) {
      this.formInfo.deliveryDate = dateString
    },
    
    
  }
}
</script>

<style scoped>
.ant-calendar-picker{
  width:197px;
}
</style>