.shadow{
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
}
.side-menu{
  min-height: 100vh;
  overflow-y: auto;
  z-index: 10;
  .logo{
    height: 44px;
    position: relative;
    line-height: 44px;
    //padding-left: 24px;
    -webkit-transition: all .3s;
    transition: all .3s;
    overflow: hidden;
    // background-color: @layout-trigger-background;
    background-color: #7c5a23;
    &.light{
      // background-color: #fff;
      background-color: #7c5a23;
      h1{
        // color: @primary-color;
        color: #ffff;
        font-size: 16px;
      }
    }
    h1{
      color: @menu-dark-highlight-color;
      font-size: 20px;
      margin: 0;
      display: inline-block;
      vertical-align: middle;
    }
    img{
      vertical-align: middle;
      margin: 0 13px;
      // margin-top: 4px;
    }
  }
}
.menu{
  padding: 16px 0;
}
