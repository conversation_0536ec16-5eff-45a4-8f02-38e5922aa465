<template>
  <div class='table'>
    <div class='leftBox'>
      <a-form :label-col="{ span:9 }" :wrapper-col="{ span: 7}" >
        <a-form-item label="客户指定阻抗/指定叠层">      
          <a-checkbox v-model="isSpecistack"/> 
        </a-form-item>
      </a-form>
      <p style='text-align:center;border: 1px solid #d5cdcd;margin-bottom: 0;background: #FAFAFA;color: black;'>文件层信息</p>
      <a-table
          :columns="columns1"
          bordered
          rowKey="id"
          :pagination="false"
          :data-source="stackListData"
          :customRow="onClickRow"
          :orderListTableLoading="orderListTableLoading" 
          :row-selection="{
            selectedRowKeys: selectedRowKeysArray,
            onChange: onSelectChange,
            type: 'radio',
          }"
      >
        <!-- <template slot="isChange" slot-scope="text,record">
          <a-checkbox v-model="record.isChange"/>
        </template> -->
      </a-table>        
    </div>
    <div class='rightBox'>
      <p style='text-align:center;border: 1px solid #d5cdcd;margin-bottom: 0;background: #FAFAFA;color: black'>叠层剖面结构图</p>      
      <div v-for='(item,index) in imgData' :key='index' style="margin-top: 10px;margin-left: 40px;">
        <div v-if="item.indexOf('oz') != -1" class="lineSty" style="background:#F4A460;"><span class="fontSty">铜箔({{item}})</span></div>
        <div v-if="item.indexOf('*') != -1" class="lineSty" style="background:#228B22;"><span class="fontSty" >{{item}}</span> </div>
        <div v-if="item.indexOf('mm') != -1" class="lineSty" style="background:#F0E68C;height:20px;"><span class="fontSty" style="margin-top: -2px;">芯板({{item}})</span> </div>

      </div>
    </div>
    
  </div>
</template>

<script>

const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: 30,
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "方案名",
    dataIndex: "name",
    width: 180,
    ellipsis: true,
    align: "left",
    
  },
  {
    title: "参数信息",
    dataIndex: "paramInfo",
    width: 180,
    ellipsis: true,
    align: "left",    
  },
  // {
  //   title: "当前方案",
  //   dataIndex: "isChange",
  //   scopedSlots: { customRender: 'isChange' },
  //   align: "left",    
  // },
]
export default {
    name:'GenerateStackInfo',
    props:['stackListData'],
  created(){
    console.log('this.stackListData:',this.stackListData)  
    let arr_ = this.stackListData.find(item => {return item.isChange == true})     
    if(arr_) {
      this.selectedRowKeysArray.push(arr_.id);
      this.imgData=arr_.paramInfo.split(',') 
    } 
      let arr = this.stackListData.find(item =>{return item.isSpecistack==true})
      if(arr){
        this.isSpecistack = true
      }else{
        this.isSpecistack = false
      }
      
   

  },
  data() {
    return {
      columns1, 
      isSpecistack: false,
      orderListTableLoading:false,
      orderListTableLoading1:false,
      imgData:[],
      selectedRowKeysArray:[],
      selectedRows:{},
      paramInfo:{}
    };
  },
  
  methods: {  
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;            
            var str = record.paramInfo
            this.paramInfo =  record.paramInfo
            this.imgData = str.split(',') 
            // console.log('imgData:',this.imgData)                
          },
        }
      }
    }, 
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeysArray = selectedRowKeys;
      this.selectedRows = selectedRows
      // console.log(this.selectedRows[0])
      this.imgData = this.selectedRows[0].paramInfo.split(',') 
    }, 
   
  },

};
</script>
<style scoped lang='less'>
.ant-modal-body{
  padding: 0;
}
.table{
  display:flex;
/deep/  .leftBox{
  .rowBackgroundColor {
    td{
      background: #aba5a5!important;
    }
  }
    width:50%;
    .ant-table-thead {
      tr{
        th{
          padding: 0;
        }
      }
    } 
    .ant-form-item{
      margin-bottom: 0;
    }
   .ant-table-tbody{
     tr{
       td{
         padding: 4px 4px;
       }  
           
     }
     .ant-table-row-selected{
       td{
         background: #aba5a5;
       }
     }
     .ant-table-row:hover{
       td{
         background: #aba5a5!important;
       }
     }
   }
   
  }
/deep/.rightBox{ 
  margin-top:40px; 
    width:50%;
    margin-left:10px;
    .lineSty{
      width:150px;
      height:10px;
      margin-bottom: 0px;
      position: relative;
    }
    .fontSty{
      position: absolute;
      width:120px;
      margin-left: 160px;
      margin-top: -6px;
      color:#0000FF

    }
  }
}
</style>