<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1" style="font-size: 13px;" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ltreport1206" style=" padding: 25px;font-family: serif;color: black;font-weight: bold;position: relative;" > 
            <img   src="@/assets/img/lt1206.png" style="position: absolute;top: 40px;left: 40px;">
            <div style="font-weight: bold;font-size: 20px;text-align: center;">供应商报价模板/SupplierQuotationTemplate</div>
            <div style="display: flex;line-height: 3ch;padding-top: 20px;line-height: 4ch;">
                <div style="width:60%;z-index: 99;font-size: 15px;">
                    <div>Customer:{{ LTreportdata.value_1 }}</div>
                    <div>Address:{{ LTreportdata.value_2  }}</div>
                    <div>Contact:{{ LTreportdata.value_3 }}</div>
                    <div>Telephone:{{ LTreportdata.value_4 }}</div>
                    <div>Email:{{ LTreportdata.value_5 }}</div>
                    <div>Quote Submitted Date:{{ LTreportdata.value_6 }}</div>
                </div>
                <div style="z-index: 99;">
                    <div style="font-size: 15px;">
                        <div>Supplier:{{ LTreportdata.value_7 }}</div>
                        <div>Address:{{ LTreportdata.value_8  }}</div>
                        <div>Quotedby:{{ LTreportdata.value_9 }}</div>
                        <div>Telephone:{{ LTreportdata.value_10 }}</div>
                        <div>Email:{{ LTreportdata.value_11 }}</div>
                        <div>Price Finalized Date:{{ LTreportdata.value_12 }}</div>
                    </div>                   
                </div>               
            </div>
            <div style="padding-top: 10px;">
                <table border="1" style="text-align: center;margin-top: 5px;width: 100%;border-top: 1px solid black;border-left: 1px solid black;color:#002060;">
                    <thead>
                        <tr>
                            <td>博康ERP编号</td>
                            <td>规格</td>
                            <td>层数</td>
                            <td>板厚</td>
                            <td>外层完成铜厚</td>
                            <td>拼板数量</td>
                            <td>拼板长度mm</td>
                            <td>拼板宽度mm</td>
                            <td>拼板面积㎡</td>
                            <td>量产单价(人民币含税13%)</td>
                            <td>生效时间</td>
                            <td>平米价</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in LTreportdata.price' :key="index">
                            <td style="color: black;">{{ item.price1 }}</td>
                            <td style="color: black;max-width: 250px;">{{ item.price2 }}</td>
                            <td>{{ item.price3 }}</td> 
                            <td>{{ item.price4 }}</td>
                            <td>{{ item.price5 }}</td>
                            <td>{{ item.price6 }}</td>
                            <td>{{ item.price7 }}</td>
                            <td>{{ item.price8 }}</td>
                            <td>{{ item.price9 }}</td>
                            <td>{{ item.price10 }}</td>
                            <td>{{ item.price11 }}</td>
                            <td>{{ item.price12 }}</td>
                        </tr>
                    </tbody>                
                </table>
            </div>
        </div>
    </div>
  </template>
  <script>
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        amountto:0,
        printObj1:{
            id: "ltreport1206", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
        this.amountto =0 
        for (let index = 0; index < this.LTreportdata.price.length; index++) {
            if(this.LTreportdata.price[index].total && this.LTreportdata.price[index].total!='/'){
                this.amountto +=Number(this.LTreportdata.price[index].total)
            } 
        }
        this.amountto = this.amountto ? this.getFloat(this.amountto,4) : 0
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            htmlToPdfa3('ltreport1206',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
   .Underline {
    position: relative;
    display: inline-block;
    }
    .Underline::after {
    content: "";
    position: absolute;
    left: 0;
    bottom:-8px; /* 下划线距离文字底部的距离 */
    width: 200px; /* 下划线宽度 */
    height: 2px; /* 下划线高度 */
    background-color: rgb(107, 106, 106); /* 下划线颜色 */
    }
  .pdfDom1{
      height: 650px;
      overflow: auto;
      table >thead> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
    table >tbody> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
  }
  </style>
