import { request, METHOD } from '@/utils/request'
// 获取厂商列表
export async function vendorNo(params) {
    return request(`/api/app/e-mSTMtr-vendor-no/vendor-no?VerdorName=${params}`, METHOD.GET,)
}
// 获取板材
export async function coreType(params) {
    return request(`/api/app/e-mSTMtr-vendor-no/core-type?VendorCodes=${params}`, METHOD.GET,)
}
// 获取pp
export async function ppType(params) {
    return request(`/api/app/e-mSTMtr-vendor-no/p-pType?VendorCodes=${params}`, METHOD.GET,)
}

export default {
    vendorNo,
    coreType,
    ppType,
}
