<template>
  <div ref="tableWrapper">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :customRow="customRow"
      :scroll="{ y: 738, x: 251 }"
      :loading="producerInfoLoading"
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      :pagination="false"
    >
      <div slot="score" slot-scope="text, record">
        <a-tooltip :title="xstitle">
          <span v-if="record.score" style="color: #428bca; cursor: help" @mouseover="Coefficientdetails(record.guid_)">{{
            record.score.toFixed(2)
          }}</span>
        </a-tooltip>
      </div>
      <div slot="pdct_NAME_" slot-scope="text, record" style="display: flex; align-items: center">
        <span style="color: black" :title="record.pdct_NAME_">{{ record.pdct_NAME_ }}</span
        >&nbsp;
        <span class="tagNum" style="display: inline-block; height: 19px">
          <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
            <span
              class="noCopy"
              style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
            <span
              class="noCopy"
              style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tooltip title="加急" v-else-if="record.isJiaji">
            <span
              class="noCopy"
              style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tag v-if="!record.isFullSet" class="noCopy tagstyle"> 直 </a-tag>
        </span>
      </div>
      <template
        slot="action"
        slot-scope="text, record"
        v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngineeringDispatchMIBackSendOrder')"
      >
        <a-tooltip title="回退订单">
          <a-icon type="rollback" style="color: #ff9900; font-size: 18px; cursor: default" @click="backClick(record)" />
        </a-tooltip>
      </template>
      <template slot="customRender" slot-scope="text, record" style="display: flex; align-items: center">
        <template>
          {{ text }}
        </template>
        <a-tooltip title="二次投产">
          <a-tag v-if="record.backUserAccount > 0" class="noCopy tagstyle"> 二 </a-tag>
        </a-tooltip>
        <span v-if="record.isEQ == 1 && record.state_ != '问客已回复' && record.state_ != '问客已审核' && record.state_ != '问客'">
          <a-tag class="noCopy tagstyle"> 问 </a-tag>
          <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -4px" v-if="record.eqNumber">{{ record.eqNumber }}</span>
        </span>
      </template>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
import { proordercoefficient } from "@/services/projectDisptch";
import { checkPermission } from "@/utils/abp";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "生产编号",
    dataIndex: "pdct_NAME_",
    align: "left",
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: "pdct_NAME_" },
  },
  {
    title: "状态",
    dataIndex: "state_",
    align: "left",
    ellipsis: true,
    width: 100,
    scopedSlots: { customRender: "customRender" },
  },
  {
    title: "派单时间",
    dataIndex: "dispatchDate_",
    align: "left",
    width: 130,
    ellipsis: true,
    sorter: (a, b) => {
      return a.dispatchDate_.localeCompare(b.dispatchDate_);
    },
  },
  {
    title: "系数",
    scopedSlots: { customRender: "score" },
    align: "left",
    width: 50,
  },
  {
    title: "停留时间",
    align: "left",
    dataIndex: "proStayTime",
    width: 70,
  },
  {
    title: "加工工厂",
    dataIndex: "facName",
    align: "left",
    width: 90,
    ellipsis: true,
  },

  {
    title: "操作",
    align: "center",
    fixed: "right",
    width: 40,
    scopedSlots: { customRender: "action" },
  },
];
export default {
  components: {
    RightCopy,
  },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    producerInfoLoading: {
      type: Boolean,
      required: true,
    },
    yScroll: {
      type: Number,
      require: true,
      default: 0,
    },
  },
  name: "RightTable",
  data() {
    return {
      columns,
      xstitle: "",
      menuVisible: false,
      menuData: {},
    };
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < val.length; i++) {
              let obj2 = val[i].pdct_NAME_;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            if (longestChars.length > 12) {
              this.columns[1].width = 140 + (longestChars.length - 12) * 5 + "px";
              this.columns[2].width = 100 - (longestChars.length - 12) * 5 + "px";
            } else {
              this.columns[1].width = "140px";
              this.columns[2].width = "100px";
            }
          });
        }
      },
    },
  },
  methods: {
    checkPermission,
    Coefficientdetails(id) {
      proordercoefficient(id, -1).then(res => {
        if (res.code) {
          let tit = [];
          let data = res.data[0].coefficientInfos;
          for (let index = 0; index < data.length; index++) {
            tit.push("【" + data[index].description + ": " + data[index].score + "】");
          }
          this.xstitle = tit.join(" + ");
        }
      });
    },
    // 分派回退
    backClick(record) {
      this.$emit("backClick", record);
    },
    customRow(record, index) {
      return {
        on: {
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
@media screen and (min-width: 1700px) {
  .rightTable {
    height: 40%;
    ///deep/  .ant-table-body{
    //  overflow:auto !important;
    //}
  }
}

.rightTable {
  height: 100%;
  .peopleTag {
    margin: 0;
    padding: 0;
    width: 24px;
    border-radius: 12px;
    background: #2d221d;
    border-color: #2d221d;
    color: #ff9900;
    text-align: center;
    margin-left: 2px;
  }
}
</style>
