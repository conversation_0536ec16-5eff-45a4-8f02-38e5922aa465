<!-- 工程管理 - 锣带分派  -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend" style="position:relative">
      <div style='width:100%;display:flex;'>
        <div class="leftContent"  style='user-select: none;'>
          <a-table
              :columns="columns1"
              :data-source="orderListData"
              :loading="orderListTableLoading"
              rowKey="pdctno_"
              :pagination="pagination"
              @change="handleTableChange"
              :scroll="{y:720}"
              :customRow="onClickRow"
              :rowClassName="isRedRow"
              :class="orderListData.length ? 'min-table':''"
          >
            <template slot="smT_" slot-scope="text,record">
              <a-checkbox v-model="record.smT_">
              </a-checkbox>
            </template>
            <template slot="action" slot-scope="text,record">
              <a-tooltip title="文件下载">
                <a-icon type="download" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop='filedownloadClick(record)'/>
              </a-tooltip>
            </template>
          </a-table>
          <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
            <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
          </a-menu> 
        </div>
        <div class="rightContent">
          <div class="centerTable" style='user-select: none;'>
            <order-detail
                :columns="columns3"
                :pagination="false"
                :data-source="peopleOrderInfoList"
                :orderDetailTableLoading="peopleOrderInfoTableLoading"
                rowKey="realName"
                @getPeopleOrderList="getPeopleOrderList"
                @OrderRetrievalSettingsClick="OrderRetrievalSettingsClick"
                ref="peopleOrder"
            >
            </order-detail>
          </div>
          <div class="rightTable" style='user-select: none;'>
            <div class="top">
              <a-table
                :columns="columns2"
                rowKey="fac_"
                :pagination="false"
                :scroll="{ y: 180}"
                :data-source="factoryList"
                :loading="factoryListLoading"
                :class="factoryList.length ? 'min-table2':''"
              >
              </a-table>
            </div>
            <div class="bto" >
              <a-table
                  :columns="columns4"
                  :pagination="false"
                  :dataSource="peopleOrderListData"
                  rowKey="pdctno_"
                  :scroll="{y:504}"
                  :loading="peopleOrderListTableLoading"
                  :customRow="onClickRow4"
                  :rowClassName="isRedRow4"
                  :class="peopleOrderListData.length ? 'min-table5':''"
              >
                <template slot="action" slot-scope="text,record" >
                  <a-tooltip title="回退订单" v-if="checkPermission('MES.EngineeringModule.FormingDispatch.FormingDispatchCNCSendBack')" >
                    <a-icon type="rollback" style="color: #ff9900; font-size: 18px;" @click="backClick(record)"/>
                  </a-tooltip>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
      <div class="footerAction" style='user-select: none;' >
        <backend-action
            ref="action"
            :assignLoading="assignLoading"
            :finishLoading="finishLoading"
            @assignClick="assignClick"
            @queryClick="queryClick"
            @finishClick="finishClick"
            @orderClick="orderClick"
            @FileReplacement="FileReplacement"
            @ReplacementClick="ReplacementClick"
        />
      </div>
      <!--分派弹窗-->
      <a-modal
          title="分派信息"
          :visible="assignModalVisible "
          :confirmLoading="confirmLoading"
          @ok="assingOk"
          @cancel="handleCancel"
      >
        <span style="font-weight:; color: #000000;font-size: 14px;">是否将订单号为：</span>
<!--        <div class="tabBox">-->
<!--          <a-tag color="orange" v-for="(item,index) in assignOrderList" :key="index +'_assign'"> {{item}} </a-tag>-->
<!--        </div>-->
        <a-tag color="orange">{{pdctno_}}</a-tag>
        <div style="font-weight:500">分派给：{{user_}}</div>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
          title="订单查询"
          :visible="dataVisible"
          @cancel="handleCancel"
          @ok="handleOk"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="400"
          centered
      >
        <query-info-backend ref='queryInfo' :factoryData="factoryData" />
      </a-modal>
      <!-- 取单设置 -->
      <a-modal
          title="取单设置"
          :visible="dataVisible2"
          @cancel="handleCancel"
          @ok="handleOk2"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="640"
          :confirmLoading='confirmLoading'
      >
        <order-retrieval-settings ref='OrderRetrievalSettings' :data='data' :peopleOrderInfoList='peopleOrderInfoList' :factoryData="factoryData"/>
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import {checkPermission} from "@/utils/abp";
import {
  cncDispatchList,
  protoollist,
  cncMucount,
  peopleOrderList,
  projectBackEndPeopleList,
  cncSend,
  cncSendBack,
  cncOrderFinish,
  orderRetrievalSettings,
  RetrievalSettings,
  getFactoryList,
  cnCToolInfo,
  finish, backmake,
  finishFileDownload,
} from "@/services/cncDispatch";
import OrderDetail from "@/pages/gongcheng/cncDispatch/module/OrderDetail";
import BackendAction from "@/pages/gongcheng/cncDispatch/module/BackendAction";
import QueryInfoBackend from '@/pages/gongcheng/cncDispatch/module/QueryInfoBackend.vue';
import OrderRetrievalSettings from '@/pages/gongcheng/cncDispatch/module/OrderRetrievalSettings';
import {setEngineeringROUT} from "@/utils/request";
// import Cookie from "_js-cookie@2.2.1@js-cookie";

// const columns1 = [
//   {
//     title: '序号',
//     dataIndex: 'index',
//     key: 'index',
//     align: "center",
//     customRender: (text,record,index) => `${index+1}`,
//     width: 40,
//   },
//   {
//     title: "本厂编码",
//     dataIndex: "pdctno_",
//     align: "left",
//     ellipsis: true,
//     width: 135,
//     className:'userStyle',
//   },
//   {
//     title: "工厂",
//     dataIndex: "caption_",
//     align: "left",
//     ellipsis: true,
//     width: 70,
//   },
//   {
//     title: "接单时间",
//     dataIndex: "indate_",
//     width: 95,
//     ellipsis: true,
//     align: "left",
//   },
//   {
//     title: "交货日期",
//     dataIndex: "deldate_",
//     width: 90,
//     ellipsis: true,
//     align: "left",
//   },
//   {
//     title: "制作人",
//     dataIndex: "user_",
//     align: "left",
//     ellipsis: true,
//     width: 75,
//   },
//   // {
//   //   title: "流程开始时间",
//   //   dataIndex: "flowStartDate_",
//   //   width: 110,
//   //   ellipsis: true,
//   //   align: "center",
//   // },
//   {
//     title: "面积",
//     dataIndex: "area_",
//     align: "left",
//     ellipsis: true,
//     width: 60,
//   },
//   {
//     title: "子板数",
//     dataIndex: "pnlSU_",
//     align: "center",
//     ellipsis: true,
//     width: 60,
//   },
  
//   {
//     title: "SMT_",
//     dataIndex: "smT_",
//     align: "center",
//     ellipsis: true,
//     width: 45,
//     scopedSlots: { customRender: 'smT_' },
//   },
//   {
//     title: "板材类型",
//     dataIndex: "orderType_",
//     align: "left",
//     ellipsis: true,
//     width:70,
//   },
//   {
//     title: "ERP工序",
//     dataIndex: "erpCraftKey_",
//     align: "left",
//     ellipsis: true,
//     width: 80,   // 2290

//   },
//   {
//     title: "操作",
//     scopedSlots: { customRender: 'action' },
//     align: "center",
//     //width: 120,
//   },

// ]
const columns1 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 40,
  },
  {
    title: "生产编号",
    dataIndex: "orderNo_",
    align: "left",
    ellipsis: true,
    width: 80,
    className:'userStyle',
  },
  {
    title: "类型",
    dataIndex: "type_",
    align: "left",
    ellipsis: true,
    width: 40,
  },
  {
    title: "状态",
    dataIndex: "status_",
    align: "left",
    ellipsis: true,
    width: 40,
  },
  {
    title: "作业次数",
    dataIndex: "count_",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: "开始时间",
    dataIndex: "startDate_",
    width: 75,
    ellipsis: true,
    align: "left",
  },
  {
    title: "完成时间",
    dataIndex: "finishDate_",
    align: "left",
    ellipsis: true,
    width: 75,
  },
  {
    title: "输入文件",
    dataIndex: "inputFile_",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: "制作完成文件",
    dataIndex: "exportFile_",
    align: "left",
    ellipsis: true,
    width: 90,
  },
  {
    title: "错误信息",
    dataIndex: "errMsg_",
    align: "center",
    ellipsis: true,
    width: 75,
  },
  
  {
    title: "作业主机",
    dataIndex: "computer_",
    align: "center",
    ellipsis: true,
    width: 75,
  },
  {
    title: "排序",
    dataIndex: "disPlay_",
    align: "left",
    ellipsis: true,
    width:45,
  },
  {
    title: "交期",
    dataIndex: "deliveryDate",
    align: "left",
    ellipsis: true,
    width: 60,  

  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
  },

]
const columns2 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 40,
  },
  {
    title: "工厂",
    dataIndex: "fac_",
    align: "center",
    ellipsis: true,
    width: 80,
    className:'userStyle',
  },
  {
    title: "待分派",
    dataIndex: "waitDispatch_",
    width: 80,
    align: "center",
  },
  {
    title: "已完结",
    dataIndex: "wancheng",
    width: 80,
    align: "center",
  },

]
const columns3 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width: '15%',
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "小组",
    dataIndex: "groups",
    width: '15%',
    ellipsis: true,
    align: "left",
  },
  {
    title: "姓名",
    dataIndex: "realName",
    width: '16%',
    ellipsis: true,
    align: "left",
    scopedSlots: {
      customRender: "customRender",
    },
  },
  {
    title: "目标",
    dataIndex: "targetCount_",
    width: '13%',
    ellipsis: true,
    align: "center",
  },
  {
    title: "停留",
    dataIndex: "counts",
    width: '13%',
    ellipsis: true,
    align: "center",
  },
  {
    title: "完成",
    dataIndex: "numendCounts",
    width: '13%',
    ellipsis: true,
    align: "center",
  },
  {
    title: "操作",
    align: "center",
    scopedSlots: {customRender: "action"}
  },
]
const columns4 = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    width:40,
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: "本厂编码",
    dataIndex: "pdctno_",
    width: 110,
    ellipsis: true,
    align: "center",
    className:'userStyle',
  },
  {
    title: "工厂",
    dataIndex: "caption_",
    width: 70,
    ellipsis: true,
    align: "center",
  },
  {
    title: "状态",
    dataIndex: "state_",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "派单时间",
    dataIndex: "beginDate_",
    width:80,
    ellipsis: true,
    align: "center",
  },
  // {
  //   title: "完成时间",
  //   ellipsis: true,
  //   dataIndex: 'endDate_',
  //   width:80,
  //   align: "center",
  //
  // },
  {
    title: '操作',
    align: "center",
    width:60,
    scopedSlots: {customRender: "action"}
  }

]
export default {
  name: "cnc",
  components: { BackendAction, OrderDetail, QueryInfoBackend,OrderRetrievalSettings},
  inject:['reload'],
  data(){
    return {
      showText:false,
      text:'',
      confirmLoading:false,
      spinning:false,
      columns1,
      columns2,
      orderListData:[],
      factoryList:[],
      factoryListLoading:false,
      orderListTableLoading:false,
      orderDetailData:[],
      orderDetailTableLoading:false,
      columns3,
      peopleOrderInfoList:[],
      peopleOrderInfoTableLoading:false,
      columns4,
      peopleOrderListData:[],
      peopleOrderListTableLoading:false,
      pdctno_:'',
      assignLoading: false,
      backLoading:false,
      finishLoading:false,
      dataVisible: false,   // 查询弹窗开关
      dataVisible2:false, // 取单设置弹窗
      selectedRowsData:{},
      selectedData:{},
      assignModalVisible:false,
      user_:'',
      pdctnoNum:'',
      data:{
        "userNo_": 0,  //
        "userName_":'',// 用户名
        "isLeave_": false, // 请假
        "targetCount_": 0, // 目标款数
        "labels": 0,  // 0样板，1批量，2多层
        "getNum": 0,  // 每次可获取订单数
        "stayNum": 0, // 停留订单数
        "maxNum": 0,  // 可获取订单总数
        "length": 0,  // 长
        "width": 0,  // 宽
        "aluminum": false,  // 铝基
        "noAluminum": false,  // 非铝基
        "smt": false,  // SMT
        "noSmt": false,  // 非SMT
        "isKaYoupin": false,  // 大客优品
        "noKaYoupin": false,  // 非大客优品
        "sort":0, // 显示顺序
      },
      factoryData:[],
      menuVisible:false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      OrderNumber1:"",
      menuData:{},
      cookieId:'',
      pagination: {
        pageSize: 20,
        pageIndex: 1,
        total:0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
        showTotal: (total) => `总计 ${total} 条`,
      },
    }
  },
  created() {
    this.getOrderList();
    this.getPeopleList();
    this.getCncMucount();
    this.getfactory()
  },
  mounted() {
    this.getcookie('ordernoROUT')
  },
  methods:{
    checkPermission,
    handleTableChange(pagination,) {
      this.pagination.pageIndex=pagination.current
      this.pagination.pageSize=pagination.pageSize
      let queryData ={
        'OrderNo':this.OrderNumber1,
      }
      if(JSON.stringify(queryData) != '{}'){
          this.getOrderList(queryData)
        }else{
          this.getOrderList();
        }
    },  
    // 分派明细列表
    // getOrderList(OrderNo,fac){
    //   this.orderListTableLoading = true;
    //   cncDispatchList (OrderNo || '',fac || '').then(res => {
    //     if (res.code) {
    //       this.orderListData = res.data;
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   }).finally(()=> {
    //     this.orderListTableLoading = false;
    //   })
    // },
      getOrderList(queryData){
      let params = {
        ...queryData,
        'PageIndex': this.pagination.pageIndex,
        'PageSize' : this.pagination.pageSize,       
      }
      protoollist (params).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
        }else{
          this.$message.error(res.message)
        }
      })
    },
    // 显示工厂订单
    getCncMucount(){
      this.factoryListLoading = true;
      cncMucount().then(res =>{
        if(res.code){
         this.factoryList = res.data
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.factoryListLoading = false
      })
    },
    // 获取人员
    getPeopleList(){
      this.peopleOrderInfoTableLoading = true;
      projectBackEndPeopleList().then(res => {
        if(res.code) {
          this.peopleOrderInfoList = res.data;
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=> {
        this.peopleOrderInfoTableLoading = false;
      })
    },
    // 获取人员对应的订单(显示用户详细订单)
    getPeopleOrderList(id,name){
      this.peopleOrderListTableLoading = true;
      this.user_ = name
      peopleOrderList({'UserNo':id}).then(res => {
        if (res.code) {
          this.peopleOrderListData = res.data;
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.peopleOrderListTableLoading = false;
      })
    },
    // 获取工厂
    getfactory (){
      getFactoryList().then(res =>{
        if (res.code == 1){
          this.factoryData = res.data
        }else{
          this.$message.error(res.message)
        }
      })
    },
    // 分派事件
    assignClick(){
      let Pdctno_ = this.pdctno_;
      let AcceptUserNo = this.$refs.peopleOrder.userNo;
      if (Pdctno_.length == 0) {
        this.$message.warning("请先选择订单")
        return
      }
      if (!AcceptUserNo) {
        this.$message.warning("请选择要分派的人员")
        return
      }
      this.assignModalVisible = true

    },
    assingOk(){
      let params = {
        "Pdctno_": this.pdctno_,
        "AcceptUserNo": this.$refs.peopleOrder.userNo
      }
      this.confirmLoading = true
      cncSend(params).then(res => {
        if (res.code){
          this.$message.success('分派成功')
          this.getOrderList()
          this.getPeopleList()
        } else {
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.confirmLoading = false
        // this.spinning = false
        this.assignModalVisible = false
      })
    },
    // 回退
    backClick(record){
      let params = {
        'Pdctno_':record.pdctno_
      }
      this.spinning = true
      cncSendBack(params).then(res =>{
        if(res.code){
          this.$message.success('回退成功')
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.spinning = false
      })
    },
    // 订单完结
    finishClick(){
      let params = {
        'Pdctno_' :this.pdctno_
      }
      if(!this.pdctno_){
        return this.$message.error('请选择订单')
      }
      this.finishLoading = true
      cncOrderFinish(params).then(res =>{
        if(res.code){
          this.$message.success('订单已完结')
          this.getOrderList()
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.finishLoading = false
      })
    },
    // 弹窗关闭
    handleCancel(){
      this.dataVisible = false  // 查询
      this.dataVisible2 = false // 取单设置
      this.assignModalVisible = false // 分派弹窗
    },
    // 查询
    queryClick(){
      this.dataVisible = true;
    },
    handleOk(){
      this.dataVisible = false;
      var payload = this.$refs.queryInfo.OrderNumber
      var arr1 = payload.split('')
      if(arr1.length >20){
        arr1 = arr1.slice(0,20)
      }
      payload = arr1.join('')
      let queryData ={
        'OrderNo':payload,
      }
      this.OrderNumber1 = payload
      this.getOrderList(queryData)
    },
    // 分派明细列表行点击事件
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.pdctno_ = record.pdctno_
          },
          dblclick: (event) => {
            // this.$emit('socketSendMessage')
            this.webSocketLink()
            setEngineeringROUT ({
              token: record.pdctno_,
            });
            this.getcookie('ordernoROUT')
          },
          contextmenu: e => {
            console.log('走了');
           let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
              console.log('text',text);
            } 
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record)
          
          },
        }
      }
    },
    isRedRow (record) {
      let strGroup = []
      if(record.pdctno_ == this.cookieId ){
        strGroup.push('cookieIdColor')
      }
      if (record.pdctno_ == this.pdctno_) {
        strGroup.push('rowBackgroundColor')
      }
        return strGroup
    },
    // 人员对应的订单行点击事件
    onClickRow4(record) {
      return {
        on: {
          click: () => {
            this.pdctnoNum = record.pdctno_
          },
          contextmenu: e => {
            console.log('走了');
           let text = ''
            if(e.target.innerText){           
             text = e.target.innerText
              console.log('text',text);
            } 
            e.preventDefault();
            this.menuData = record;
            this.rightClick1(e, text, record)
          
          },
        }
      }
    },
    isRedRow4 (record) {
      if (record.pdctno_ == this.pdctnoNum) {
        return 'rowBackgroundColor'
      } else {
        return ''
      }
    },
    // 获取取单设置信息
    OrderRetrievalSettingsClick(record){
      this.dataVisible2 = true
      orderRetrievalSettings(record.userNo).then(res => {
        this.data = res.data
        if( res.data.labels == 0){
          res.data.labels = '0';
        }else if( res.data.labels == 1){
          res.data.labels = '1';
        }else if( res.data.labels == 2){
          res.data.labels = '2';
        }else{
          res.data.labels = '3';
        }

        console.log('this.data',this.data)
      })
    },
   // 文件下载
   filedownloadClick(record){
    console.log(record)
    finishFileDownload(record.pdctno_).then(res=>{
      if(res.code && res.data){        
        window.location.href = res.data
      }else{
        this.$message.warning("当前订单未完成")
      }
    })
    
    },
    // 取单设置
    handleOk2(){
      this.confirmLoading = true;
      this.spinning = true
      if( this.data.labels == '0'){
        this.data.labels = 0;
      }else if( this.data.labels == '1'){
        this.data.labels = 1;
      }else if( this.data.labels == '2'){
        this.data.labels = 2;
      }else{
        this.data.labels = 3;
      }
      const params = {
        ...this.data,
        targetCount_: Number(this.data.targetCount_),
        getNum: Number(this.data.getNum),
        stayNum: Number(this.data.stayNum),
        maxNum: Number(this.data.maxNum),
        length: Number(this.data.length),
        width: Number(this.data.width),
      };
      params.factroy = this.$refs.OrderRetrievalSettings.factory_List.toString()
      console.log('params',params)
      RetrievalSettings(params).then(res => {
        if (res.code){
          this.$message.success('设置成功')
        } else {
          this.$message.error(res.message)
        }
        this.getPeopleList()
      }).finally(()=>{
        this.spinning = false
        this.confirmLoading = false
        this.dataVisible2 = false
      })
    },
    // 自动取单
    orderClick(){
      cnCToolInfo().then(res =>{
        if(res.code){
          this.$message.success('取单成功')
        }else{
          this.$message.error(res.message)
        }
      })
    },
    // 右键事件
    // bodyClick() {
    //   this.menuVisible = false;
    //   document.body.removeEventListener("click", this.bodyClick);
    // },
    // rightClick(e){
    //   if(this.menuData ){
    //     this.menuVisible = true;
    //   }
    //   console.log()
    //   this.menuStyle.top = e.clientY- 110 +  "px";
    //   this.menuStyle.left = e.clientX -
    //       document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
    //   document.body.addEventListener("click", this.bodyClick);
    // },
    rightClick1(e,text,record){ 
       this.text=text;
      if(!this.text){
        this.text = ''
        this.showText = false
      }else{
        this.text = text 
        this.showText = true
      }
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX -
      document.getElementsByClassName('fixed-side')[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuStyle= {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex:99
      },
      document.body.removeEventListener("click", this.bodyClick);
    },    
    down(){
      let input = document.createElement('input');
      //input.value = this.text                        
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand('copy')
      bool ? this.$message.success('复制成功') : this.$message.error('复制失败');
      document.body.removeChild(input);
      },
    // 单只文件下载
    down0(){
      this.webSocketLink('1')
    },
    // 参考文件下载
    down1(){
      this.webSocketLink('2')
    },
    // 下载上机文件
    down2(){
      this.webSocketLink('3')
    },
    // 下载BTF文件
    down3(){
      this.webSocketLink('4')
    },
    // 打开下载目录
    down4(){
      this.webSocketLink('5')
    },
    // 参考文件上传
    down5(){
      this.webSocketLink('6')
    },
    // 文件替换
    down6(){
      this.webSocketLink('7')
    },
    updateUrl(urlPath){
      let _this = this;
      if (urlPath.indexOf('sockjs') != -1) {
        _this.wsUrl = 'http://'  + urlPath  ;
      } else {
        if (window.location.protocol == 'http:') {
          _this.wsUrl = 'ws://' + urlPath  ;
        } else {
          _this.wsUrl = 'ws://' + urlPath  ;
        }
      }
    },
    webSocketLink(key){
      let _this = this;
      var heartCheck = {
        timeout: 5000,//5秒
        timeoutObj: null,
        reset: function(){
          clearInterval(this.timeoutObj); return this;
        },
        start: function(){
          this.timeoutObj = setInterval(function(){ _this.websocket.send("HeartBeat"); console.log("HeartBeat");}, this.timeout)
        }
      };
      if ('WebSocket' in window) {
        _this.updateUrl("127.0.0.1:18181");
        _this.websocket = new WebSocket(_this.wsUrl);
      } else if ('MozWebSocket' in window) {
        _this.updateUrl("127.0.0.1:18181");
        _this.websocket = new MozWebSocket(_this.wsUrl) || null;
      } else {
        _this.updateUrl("sockjs/webSocketServer");
        _this.websocket = new SockJS(_this.wsUrl)
      }
      let proOrderId = this.pdctno_
      _this.websocket.onopen = function(){
        _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"ROUT","Uid":proOrderId,'Data':{}}))
        // heartCheck.reset().start();
      };
      // 单只文件
      if(key == 1){
        _this.websocket.onopen = function(){
          _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"tsm_DownFileClick","Uid":proOrderId,'Data':{}}))
          // heartCheck.reset().start();
        };
      }
      // 参考文件
      if(key == 2){
        _this.websocket.onopen = function(){
          _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"tsm_DownFileClickCZ","Uid":proOrderId,'Data':{}}))
          // heartCheck.reset().start();
        };
      }
      // 上机文件
      if(key == 3){
        _this.websocket.onopen = function(){
          _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"tsm_DownLoadOkRoutClick","Uid":proOrderId,'Data':{}}))
        };
      }
      // BTF文件
      if(key == 4){
        _this.websocket.onopen = function(){
          _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"tsm_DownLoadbtfClick","Uid":proOrderId,'Data':{}}))
        };
      }
      // 打开下载目录
      if(key == 5){
        _this.websocket.onopen = function(){
          _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"tsm_OpenFileClick","Uid":proOrderId,'Data':{}}))
        };
      }
      // 参考文件上传
      if(key == 6){
        _this.websocket.onopen = function(){
          _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"tsm_UpLoadFileClickCZ","Uid":proOrderId,'Data':{}}))
        };
      }
      // 文件替换
      if(key == 7){
        _this.websocket.onopen = function(){
          _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'ROUT',"Task":"","Uid":proOrderId,'Data':{}}))
        };
      }

    },
    // 获取cookie缓存订单id
    getcookie(ordernoROUT){//获取指定名称的cookie的值
      var arrstr = document.cookie.split("; ");
      for(var i = 0;i < arrstr.length;i ++){
        var temp = arrstr[i].split("=");
        if(temp[0] == ordernoROUT) {
          // console.log('temp',unescape(temp[1]))
          this.cookieId = unescape(temp[1])
          // return unescape(temp[1]);
        }
      }
    },
    // 完成
    FileReplacement(){
      if(this.pdctno_){
        this.$refs.action.clickUpload(this.pdctno_)
      }else{
        this.$message.warning('请选择订单')
      }
    },
    // 替换文件
    ReplacementClick(){
      if(this.pdctno_){
        this.$refs.action.clickUpload1(this.pdctno_)
      }else{
        this.$message.warning('请选择订单')
      }
    },
    // 节点回退
    NodeFallback(){
      if(this.pdctno_){
        backmake(this.pdctno_).then(res=>{
          if(res.code){
            this.$message.success('节点回退成功')
          }else{
            this.$message.error(res.message)
          }
        })
      }else{
        this.$message.warning('请选择订单')
      }

    },
  },

}
</script>

<style scoped lang="less">
/deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin: 18px 0 0 14px;
}
/deep/.ant-form-item-label > label{
  color: #000000;
}
/deep/.ant-input{
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}

.projectBackend {
  /deep/.userStyle{
    user-select:none;
  }
  height: 814px;
  min-width: 1670px;
  // width: 100%;
  background: #FFFFFF;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  /deep/.leftContent {
    .min-table {
      .ant-table-body{
        min-height:720px;
      }
    }
    .ant-table-body{
      .ant-table-fixed{
        width: 990px !important;
      }
    }
    .tabRightClikBox{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important; 
        color:#000000
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }
    height:762px;
    width: 54%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }

  /deep/ .rightContent {
    width: 46%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .centerTable {
      .peopleTag {
        position: absolute;
        font-size: 12px;
        font-weight: 600;
        left: 0;
        padding: 0 2px;
      }
      .ant-table-body{
        .ant-table-tbody{
          .ant-table-row:first-child{
            .anticon{
              display: none;
            }
          }
        }
        max-height:730px!important;

      }
      width: 42%;
      height: 762px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);

    }
    .rightTable {
      .top{
        .min-table2{
          min-height: 216px;
        }
      }
      .bto{
        border-top: 2px solid rgb(233, 233, 240);
        .ant-table-body{
          tr{
            td{
              padding: 3px 1px!important;
            }
          }
        }
      }
      width: 58%;
      height: 762px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
      .jobNote {
        /deep/ .ant-table-wrapper {
          user-select: none;
          height: 444px;
        }
        .minTable{
          min-height:387px;
        }
        .note {
          height: 300px;
          overflow-y: auto;
        }
      }
    }
    .min-table5 {
      .ant-table-body{
        min-height:504px;
      }
    }
  }
  .footerAction {
    width: 100%;
    height:60px;
    border-bottom: 2px solid #E9E9F0;
    border-left: 2px solid #E9E9F0;
    border-right: 2px solid #E9E9F0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #FFFFFF;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 28px;
      .ant-tabs-tab {
        margin: 0;
        height: 28px;
        line-height: 28px;
      }
    }

  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #DC143C;
        }
      }
      .eqBackground {
        background: #FFFF00;
      }
      .statuBackground {
        background: #B0E0E6;
      }
      .backUserBackground {
        background: #A7A2C9;
      }

    }
    .ant-table-row-selected {
      td {
        background: #dcdcdc;
      }
    }
    /deep/.userStyle{
      color:red!important;
      user-select: none!important;
    }
    //td[class~='userStyle'] {
    //  user-select: all !important;
    //}

  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dcdcdc;
  }
  /deep/ .ant-table-thead > tr > th {
    // padding:3px 0!important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table{

    .ant-table-thead > tr > th{
      padding: 7px 2px;
      border-right: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding:  7px 2px;
      border-right: 1px solid #efefef;
    }
    tr.ant-table-row-selected td {
      background: #dcdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dcdcdc;
    }
    .rowBackgroundColor {
      background: #dcdcdc!important;
    }

    .cookieIdColor{
      td:first-child{
        //border-left:2px solid #ff9900!important;
        background: #ff9900!important;
      }
    }
    .cookieIdColor:hover{
      td:first-child{
        //border-left:2px solid #ff9900!important;
        background: #ff9900!important;
      }
    }
    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        color: #000000;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }


}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #D6D6D6;
    .imgTable{
      img{
        width:100px;
        height:50px;
      }
    }
    p {
      height: 100%;
      line-height: 35px;
      font-weight: 700;
      margin: 0;

    }
    .displayFlag {
      display: none;
    }
  }
}
</style>
