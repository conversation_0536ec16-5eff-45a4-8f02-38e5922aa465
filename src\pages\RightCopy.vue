<template>
  <div>
    <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox1">
      <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
    </a-menu>
  </div>
</template>
<script>
export default {
  name: "rightcopy",
  props: [],
  data() {
    return {
      text: "",
      showText: false,
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
    };
  },
  methods: {
    rightClick(e) {
      let event = e.target;
      const mouseX = e.clientX;
      const mouseY = e.clientY;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.localName == "path") {
        event = e.target.parentNode.parentNode;
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split("\n")[0];
      }
      this.menuVisible = true;
      this.menuStyle.top = mouseY - 100 + "px";
      this.menuStyle.left = mouseX - 225 + "px";
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    down() {
      let input = document.createElement("input");
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
  },
};
</script>
<style lang="less" scoped>
.tabRightClikBox1 {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    background-color: white !important;
    color: #000000;
  }
}
</style>
