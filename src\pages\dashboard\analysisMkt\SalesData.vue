<template>
  <a-card :loading="loading" :body-style="{padding: '20px 24px 8px',position: 'relative'}" :bordered="false">
      <a-tooltip :title="$t('introduce')" style="position:absolute; right:15px">
        <a-icon type="info-circle-o" />
      </a-tooltip>
    <p class="chartCardP">
      {{total}}
    </p>
    <div :id="el" style="width:100%; height:142px">
    </div>
  </a-card>
</template>
<script>
import * as echarts from 'echarts/core';
import { TooltipComponent, LegendComponent } from 'echarts/components';
import { PieChart } from 'echarts/charts';
import { LabelLayout } from 'echarts/features';
import { SVGRenderer } from 'echarts/renderers';

echarts.use([
  TooltipComponent,
  LegendComponent,
  PieChart,
  SVGRenderer,
  LabelLayout
]);
export default {
  props:["loading", "total","el"],
  data() {
    return {
      data:[]
    };
  },
  watch: {
    echartdata (val) {
      this.data = val
    }
  },
  methods:{
    drawLine(opt){
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center',
            data: []
          },
          color: ["#FFDC60", "#91CC75", "#ff9900"],
          series: [
            {
              type:'pie',
                radius: ['0%', '95%'],
                center: ['50%', '50%'],
                avoidLabelOverlap: false,
                hoverAnimation: false, 
                label: {
                    normal: {
                      position: 'inner',
                      show: true,
                      textStyle : {
                        fontWeight : 600 ,
                        fontSize : 10
                      },
                      formatter:'{d}%'
                    },
                },
              data: opt || []
            }
          ]
        }
        let chartDom = document.getElementById(this.el);
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      })
    }
  },
};
</script>
<style scoped>
  .mini-chart {
    position: relative;
    width: 100%
  }
  .mini-chart .chart-content{
    position: absolute;
    bottom: -28px;
    width: 100%;
  }
  .chartCardP {
    position: absolute;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
    font-weight: 600;
  }
</style>
