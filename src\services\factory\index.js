/*
 * @Author: CJP
 * @Date: 2022-05-30 16:34:59
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-05-30 16:51:00
 * @FilePath: \vue-antd-admin\src\services\factory\index.js
 * @Description:
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved.
 */
import { request, METHOD } from '@/utils/request'
import { transformAbpListQuery } from '@/utils/abp'
// 列表操作
export async function actionList(params) {
    //  PUT 修改 POST 新增
    return request("/api/app/e-mSTSys-user-enterprise-info", METHOD[params.type], transformAbpListQuery(params.obj))
}
// 查询
export async function find(params) {
    // 查询 GET  删除 DELETE
    return request(`/api/app/e-mSTSys-user-enterprise-info/${params.id}/by-id`, METHOD[params.type])
}
// 获取列表
export async function getList(params) {
    return request('/api/app/e-mSTPub-factory-configure/page-list', METHOD.GET, params)
}
// 工厂详情
export async function getOrderInfo(id) {
    return request(`/api/app/e-mSTPub-factory-configure/${id}/async-by-id`, METHOD.GET)
}
// 保存
export async function saveConfigure(params) {
    return request(`/api/app/e-mSTPub-factory-configure/update`, METHOD.POST,params)
}
export default {
    getList,
    find,
    actionList,
    getOrderInfo,
    saveConfigure
}
