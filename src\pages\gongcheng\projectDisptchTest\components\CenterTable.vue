<template>
  <a-table
    style="word-break: break-all"
    :columns="columns"
    :dataSource="dataSource"
    :scroll="{ y: 666, x: 300 }"
    :rowKey="
      (record, index) => {
        return index;
      }
    "
    class="centerTable"
    :pagination="false"
    :customRow="onClickRow"
    :rowClassName="isRedRow"
  >
    <!-- <div
        slot="filterDropdown"
        slot-scope="{
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
                column,
              }"
        style="padding: 8px"
    >
      <a-input
          v-ant-ref="(c) => (searchInput = c)"
          placeholder="请输入姓名"
          :value="selectedKeys[0]"
          style="width: 188px; margin-bottom: 8px; display: block"
          @change="
                  (e) => setSelectedKeys(e.target.value ? [e.target.value] : [])
                "
          @pressEnter="
                  () => handleSearch(selectedKeys, confirm, column.dataIndex)
                "
      />
      <a-button
          type="primary"
          icon="search"
          size="small"
          style="width: 90px; margin-right: 8px"
          @click="
                  () => handleSearch(selectedKeys, confirm, column.dataIndex)
                "
      >
        查找
      </a-button>
      <a-button
          size="small"
          style="width: 90px"
          @click="() => handleReset(clearFilters)"
      >
        重置
      </a-button>
    </div> -->
    <a-icon slot="filterIcon" slot-scope="filtered" type="search" :style="{ color: filtered ? '#ff9900' : '' }" />
    <template slot="counts" slot-scope="record, index">
      <span v-if="record.counts" @click="counts(record, index)">{{ record.counts }}</span>
      <span v-else>{{ record.counts }}</span>
    </template>
    <template slot="wenkeCounts" slot-scope="record">
      <span v-if="record.wenkeCounts" @click="wenkeCounts(record)">{{ record.wenkeCounts }}</span>
      <span v-else>{{ record.wenkeCounts }}</span>
    </template>
    <template slot="numendCounts" slot-scope="record">
      <span v-if="record.numendCounts" @click="numendCounts(record)">{{ record.numendCounts }}</span>
      <span v-else>{{ record.numendCounts }}</span>
    </template>

    <template slot="customRender" slot-scope="text, record, index, column">
      <span v-if="searchText && searchedColumn === column.dataIndex">
        <!-- <template
                    v-for="(fragment, i) in text
                    .toString()
                    .split(
                      new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i')
                    )"
                >
                  <mark
                      v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                      :key="i"
                      class="highlight"
                  >{{ fragment }}</mark
                  >
                  <template v-else>{{ fragment }}</template>
                </template> -->
      </span>
      <template v-else>
        {{ text }}
      </template>
      <a-tag color="orange" v-if="record.isLeave_" class="peopleTag"> 休 </a-tag>
      <a-tag color="orange" v-if="record.isFullSet" class="peopleTag"> 全 </a-tag>
    </template>
    <span
      slot="action"
      slot-scope="record"
      :style="record.realName == '合计' ? { display: 'none' } : ''"
      v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngineeringDispatchMIIsLeave')"
    >
      <a @click="showModal(record)">
        <a-tooltip>
          <template slot="title"> 取单设置 </template>
          <a-icon type="edit" />
        </a-tooltip>
      </a>
    </span>
    <span slot="num" slot-scope="text, record, index">
      {{ index + 1 }}
    </span>
  </a-table>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { projectOrderInfo } from "@/services/projectDisptch";

export default {
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    producerTabLoading: {
      type: Boolean,
      required: true,
    },
    yScroll: {
      type: Number,
      require: true,
      default: 0,
    },
    xScroll: {
      type: Number,
      require: true,
      default: 0,
    },
  },
  name: "CenterTable",
  data() {
    return {
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 34,
          scopedSlots: { customRender: "num" },
        },
        {
          title: "小组",
          dataIndex: "groups",
          width: 55,
          ellipsis: true,
          align: "left",
        },
        {
          title: "姓名",
          dataIndex: "realName",
          align: "left",
          width: 84,
          ellipsis: true,
          scopedSlots: { customRender: "customRender" },
        },
        {
          title: "目标",
          dataIndex: "targetCount_",
          align: "left",
          width: 34,
        },
        {
          title: "已发",
          align: "left",
          width: 34,
          scopedSlots: { customRender: "numendCounts" },
        },
        {
          title: "问客",
          align: "left",
          width: 34,
          scopedSlots: { customRender: "wenkeCounts" },
        },
        {
          title: "操作",
          align: "center",
          width: 34,
          scopedSlots: { customRender: "action" },
        },
        {
          title: "系数",
          customRender: (text, record, index) => (record.score ? record.score.toFixed(2) : ""),
          //dataIndex: "score",
          align: "left",
          width: 60,
        },
        {
          title: "工厂",
          dataIndex: "facName",
          align: "left",
          width: 90,
        },
      ],
      searchInput: null,
      searchText: "",
      selectedRowKeysArray: [],
      userNo: "",
    };
  },
  methods: {
    // onSelectChange(selectedRowKeys, selectedRows) {
    //   this.selectedRowKeysArray = selectedRowKeys;
    //   this.$emit('assignPeopleChange', this.selectedRowKeysArray)
    // },
    checkPermission,
    counts(record) {
      if (record.realName != "管理员") {
        this.$emit("counts", record);
        let keys = [];
        keys.push(record.userNo);
        this.selectedRowKeysArray = keys;
        this.$emit("assignPeopleChange", record); // 更新父组件的值
        this.userNo = record.userNo;
      }
    },
    wenkeCounts(record) {
      if (record.realName != "管理员") {
        this.$emit("wenkeCounts", record);
        let keys = [];
        keys.push(record.userNo);
        this.selectedRowKeysArray = keys;
        this.$emit("assignPeopleChange", record); // 更新父组件的值
        this.userNo = record.userNo;
      }
    },
    numendCounts(record) {
      if (record.realName != "管理员") {
        this.$emit("numendCounts", record);
        let keys = [];
        keys.push(record.userNo);
        this.selectedRowKeysArray = keys;
        this.$emit("assignPeopleChange", record); // 更新父组件的值
        this.userNo = record.userNo;
      }
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.userNo);
            this.selectedRowKeysArray = keys;
            this.$emit("assignPeopleChange", record); // 更新父组件的值
            this.$emit("getProducerInfo", record.userNo); // 点击请求用户详情
            this.userNo = record.userNo;
          },
        },
      };
    },
    handleSearch(selectedKeys, confirm, dataIndex) {
      confirm();
      this.searchText = selectedKeys[0];
      this.searchedColumn = dataIndex;
    },

    handleReset(clearFilters) {
      clearFilters();
      this.searchText = "";
    },
    showModal(record) {
      this.$emit("showEditInfoModal", record);
    },
    isRedRow(record) {
      // console.log('record',record)
      let strGroup = [];
      let str = [];
      if (record.userNo && record.userNo == this.userNo) {
        // return 'rowBackgroundColor'
        strGroup.push("rowBackgroundColor");
      }
      if (record.isLeave_ == true) {
        // 是否请假
        str.push("rowSty");
        // return 'rowSty'
      }
      // if(record.isBigCus == true){ // 是否大客
      //   str.push('rowSty1')
      // }
      // if(str.length > 1){
      //   // console.error('str',[str[0]])
      //   str = [str[0]]
      // }
      // console.log('str.concat(strGroup):',str.concat(strGroup))
      return str.concat(strGroup);
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.ant-tag {
  margin: 0;
}
@media screen and (min-width: 1700px) {
  .centerTable {
    overflow-x: auto !important;
    /deep/ .ant-table-body {
      .ant-table-tbody {
        .ant-table-row:last-child {
          .contentTabAction {
            display: none;
          }
        }
      }
      //   min-height: 743px;
    }

    /deep/.rowBackgroundColor {
      background: rgb(223, 220, 220) !important;
    }
  }
}
/deep/.rowSty {
  td {
    color: #dc143c;
  }
}
.actionStyle {
  display: none;
}

.peopleTag {
  // position: absolute;
  font-size: 12px;
  font-weight: 600;
  // left: 136px;
  padding: 0 2px;
  height: 20px;
}
</style>
