export default {
  state: {
    _categoryForm: {
      pdctno: "", // 编号
      layers: "", // 层数
      boardType: "", // 板材型号
      // tg: '',                        // Tg值
      ppType: "", // PP型号
      finishBoardThickness: 0, // 完成板厚
      highLimit: "", // 上限公差
      engineeringHiht: 0, // 工程上限
      lowLimit: "", // 下限公差
      engineeringLow: 0, // 公差下限
      pressingThickness: 0, // 压合板厚
      finishedThickness: 0, // 成品板厚
      stackUpTol: "", // 压合公差
      isPressure: false, // 芯板对压
      gBStackUp: false, // 光板
      gbcount: 0, // 光板下拉
      isChangeLayerPres: false,
    },
    _drillHoleTableData: [],
    _joinFactoryId: "0",
  },
  getters: {
    categoryForm: state => {
      return state._categoryForm;
    },
  },
  actions: {},
  mutations: {
    changeInfo(state, payload) {
      // console.log('完成板厚',payload)
      state._categoryForm = { ...state._categoryForm, ...payload };
    },
    drillHoleTableChange(state, payload) {
      const data = JSON.parse(localStorage.getItem("drillsData"));
      if (data && data.drillsData.length && !payload.length) {
        //console.log('缓存数据存在 传参不存在')
        state._drillHoleTableData = data.drillsData;
      } else {
        state._drillHoleTableData = payload || [];
        //  console.log('更新钻带数据为传参',payload)
        if (payload.length && payload[0].type && payload[0].type == "get") {
          // console.log('调取接口缓存数据存在,drl数据接口返回为空',payload)
          delete payload[0].type;
        } else {
          localStorage.removeItem("drillsData");
          // console.log('销毁了缓存数据')
        }
      }
    },
    factoryChange(state, payload) {
      state._joinFactoryId = payload;
    },
  },
};
