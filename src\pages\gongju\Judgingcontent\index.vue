<template>
  <a-spin :spinning="spinning">
    <div class="Review" @click="bodyClick">
      <div style="display: flex; justify-content: space-between">
        <div
          style="
            margin-top: 0;
            margin-bottom: 0.5em;
            color: #cf1b26;
            font-weight: 700;
            text-align: left;
            margin-left: 10px;
            float: left;
            font-size: 24px;
          "
        >
          {{ OrderNo }} ({{ reviewInfo.statusStr }})<br />
          <div v-if="reviewInfo.backReason" style="font-size: 16px">评审退回原因:{{ reviewInfo.backReason }}</div>
        </div>
        <div>
          <a-button
            type="primary"
            @click="Submitforreview"
            style="margin-right: 10px"
            v-if="checkPermission('MES.ToolModule.ReviewInfo.ReviewInfoSubmit')"
            >提交</a-button
          >
          <a-button type="primary" @click="ReturnClick" style="margin-right: 10px" v-if="checkPermission('MES.ToolModule.ReviewInfo.ReviewInfoBack')"
            >退回</a-button
          >
          <a-button
            type="primary"
            @click="Savereview"
            style="margin-right: 10px"
            v-if="edit && checkPermission('MES.ToolModule.ReviewInfo.ReviewInfoSave')"
            >保存</a-button
          >
          <a-button
            type="primary"
            @click="Cancelreview"
            style="margin-right: 10px"
            v-if="edit && checkPermission('MES.ToolModule.ReviewInfo.ReviewInfoSave')"
            >取消编辑</a-button
          >
          <a-button
            type="primary"
            @click="Editreview"
            style="margin-right: 10px"
            v-if="
              !edit &&
              review_Type != 'check' &&
              review_Type != 'allcheck' &&
              review_Type != 'plancheck' &&
              checkPermission('MES.ToolModule.ReviewInfo.ReviewInfoSave')
            "
            >编辑</a-button
          >
        </div>
      </div>
      <a-modal title="检查信息" :visible="checkVisible" @cancel="checkVisible = false" destroyOnClose centered :maskClosable="false" :width="600">
        <template #footer>
          <a-button key="back1" type="primary" v-if="check" @click="continueclick">继续</a-button>
          <a-button key="back" @click="checkVisible = false">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.info == '1'" style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
      <!--page  0评审发起||工程指示进入评审  1评审策划 2评审总结;
        review_Type  
        add添加 
        edit编辑 
        check审核
        assign分配 
        review评审 
        jointreview会同评审
        planning策划
        plancheck策划审核
        all总结 
        allcheck总结审核-->
      <!--基本信息-->
      <div style="width: 100%; display: flex; padding: 10px" class="information">
        <table border="1" style="width: 100%">
          <tr>
            <td rowspan="7">基本信息</td>
            <td>评审类别<a style="color: red">*</a></td>
            <!--评审策划发起评审 可重新定义评审类别-->
            <td colspan="3">
              <a-select
                v-if="edit && (page == 0 || review_Type == 'review')"
                showSearch
                allowClear
                mode="multiple"
                v-model="reviewInfo.reviewType"
                :filter-option="filterOption"
                @change="changeReviewType('change')"
              >
                <a-select-option v-for="(item, index) in mapKey(selectData.ReviewCategory)" :key="index" :value="item.value">{{
                  item.lable
                }}</a-select-option>
              </a-select>
              <div v-else>{{ reviewInfo.reviewType }}</div>
            </td>
            <td>提出人</td>
            <td colspan="3">
              <div>{{ reviewInfo.createUserName }}</div>
            </td>
            <td>部门审核人</td>
            <td>
              <a-button
                style="float: right"
                v-if="review_Type == 'check' && !reviewInfo.reviewer"
                type="primary"
                size="small"
                @click="signature('reviewer')"
                >签名</a-button
              >
              <div>{{ reviewInfo.reviewer }}</div>
            </td>
          </tr>
          <tr>
            <td>生产型号<a style="color: red">*</a></td>
            <td style="width: 200px">
              <div>{{ reviewInfo.orderNo }}</div>
            </td>
            <td>客户型号<a style="color: red">*</a></td>
            <td style="width: 200px">
              <div>{{ reviewInfo.customerModel }}</div>
            </td>
            <td>订单号</td>
            <td style="width: 200px">
              <div>{{ reviewInfo.businessOrderNo }}</div>
            </td>
            <td>客户代码</td>
            <td style="width: 200px">
              <div>{{ reviewInfo.custNo }}</div>
            </td>
            <td>成品板厚(MM)</td>
            <td style="width: 200px">
              <div>{{ reviewInfo.boardThickness }}</div>
            </td>
          </tr>
          <tr>
            <td>最小钻咀(MM)</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.minJoran"></a-input>
              <div v-else>{{ reviewInfo.minJoran }}</div>
            </td>
            <td>内层隔离环</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.innerIsolationRing"></a-input>
              <div v-else>{{ reviewInfo.innerIsolationRing }}</div>
            </td>
            <td>外层环宽</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.outerRingWidth"></a-input>
              <div v-else>{{ reviewInfo.outerRingWidth }}</div>
            </td>
            <td>芯板厚度</td>
            <td>
              <a-input v-model="reviewInfo.coreBoardTthickness" v-if="edit && page == 0"></a-input>
              <div v-else>{{ reviewInfo.coreBoardTthickness }}</div>
            </td>
            <td>板材类型<a style="color: red">*</a></td>
            <td>
              <div>{{ reviewInfo.fR4TypeStr }}</div>
            </td>
          </tr>
          <tr>
            <td>孔到孔距离</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.drlToDrl"></a-input>
              <div v-else>{{ reviewInfo.drlToDrl }}</div>
            </td>
            <td>内层线宽线距</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.innerMinLineSpace"></a-input>
              <div v-else>{{ reviewInfo.innerMinLineSpace }}</div>
            </td>
            <td>外层线宽线距</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.outerMinLineSpace"></a-input>
              <div v-else>{{ reviewInfo.outerMinLineSpace }}</div>
            </td>
            <td>孔铜(UM)</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.holeCopper"></a-input>
              <div v-else>{{ reviewInfo.holeCopper }}</div>
            </td>
            <td>阻焊颜色<a style="color: red">*</a></td>
            <td>
              <div>{{ reviewInfo.solderColorStr }}/{{ reviewInfo.solderColorBottomStr }}</div>
            </td>
          </tr>
          <tr>
            <td>纵横比</td>
            <td>
              <div>{{ reviewInfo.apertureRatio }}</div>
            </td>
            <td>内层基铜(UM)</td>
            <td>
              <div>{{ reviewInfo.innerBaseCopper }}</div>
            </td>
            <td>内层完成铜厚(UM)</td>
            <td>
              <div>{{ reviewInfo.innerCopperThickness }}</div>
            </td>
            <td>外形到铜</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.appearanceToCopper"></a-input>
              <div v-else>{{ reviewInfo.appearanceToCopper }}</div>
            </td>
            <td>过孔处理</td>
            <td>
              <div>{{ reviewInfo.solderCoverStr }}</div>
            </td>
          </tr>
          <tr>
            <td>盲(埋)孔径</td>
            <td>
              <div>{{ reviewInfo.vias }}</div>
            </td>
            <td>外层基铜(UM)</td>
            <td>
              <div>{{ reviewInfo.outerBaseCopper }}</div>
            </td>
            <td>外层完成铜厚(UM)</td>
            <td>
              <div>{{ reviewInfo.copperThickness }}</div>
            </td>
            <td>表面处理</td>
            <td>
              <div>{{ reviewInfo.surfaceFinishStr }}</div>
            </td>
            <td>订单面积(m²)<a style="color: red">*</a></td>
            <td>
              <div>{{ reviewInfo.delArea }}</div>
            </td>
          </tr>
          <tr>
            <td>层数</td>
            <td>
              <div>{{ reviewInfo.boardLayers }}</div>
            </td>
            <td>交货尺寸</td>
            <td>
              <div>{{ reviewInfo.deliverySize }}</div>
            </td>
            <td>订单难度等级</td>
            <td>
              <a-input v-if="edit && page == 0" v-model="reviewInfo.orderDifficultyLevel"></a-input>
              <div v-else>{{ reviewInfo.orderDifficultyLevel }}</div>
            </td>
            <td colspan="2">产品应用领域</td>
            <td colspan="2">
              <a-input v-if="edit && page == 0" v-model="reviewInfo.productUsage" />
              <div v-else>{{ reviewInfo.productUsage }}</div>
            </td>
          </tr>
        </table>
      </div>
      <!--评审详情-->
      <div style="padding: 0 10px">
        <table border style="border-color: #e1e1e2; color: #000000; width: 100%">
          <thead>
            <tr>
              <th style="width: 5%; text-align: center">评审次数</th>
              <th style="width: 5%; text-align: center">序号</th>
              <th style="width: 10%; text-align: center">操作时间</th>
              <th style="width: 5%; text-align: center">操作人员</th>
              <th style="width: 50%; text-align: center">评审详情</th>
              <th style="width: 15%; text-align: center">附件</th>
              <th style="width: 10%; text-align: center">操作</th>
            </tr>
          </thead>
          <tr colspan="6" style="position: relative; height: 34px" v-if="edit && page == 0">
            <span style="line-height: 34px; position: absolute; right: 50%; color: #ff9900; cursor: pointer" @click="addClick">+添加评审</span>
          </tr>
        </table>
      </div>
      <div style="max-height: 420px; overflow: auto; border: 1px solid #e1e1e2" class="scorllclass">
        <a-empty v-if="orderData.length == 0 && !addTr" />
        <!-- <a-collapse :activeKey="copyorderData.length">
          <a-collapse-panel v-for="(val, inde) in copyorderData" :key="(inde + 1).toString()">
            <template #header>
              <div style="text-align: left">
                第 <span style="color: rgb(207, 27, 38); font-weight: 700; font-size: 16px">{{ inde + 1 }}</span> 次评审
              </div>
            </template> -->
        <table border style="border-color: #e1e1e2; color: #000000; border-top-color: #ffffff00">
          <tbody>
            <template v-for="(item, index) in orderData">
              <tr :key="'1' + index">
                <td style="width: 5%" v-if="item.num" :rowspan="item.num">{{ item.reviewNumber }}</td>
                <td style="width: 5%" :rowspan="item.reply ? 2 : 1">
                  Q<i>{{ qsort(item.id) }}</i>
                </td>
                <td style="width: 10%">{{ item.createTime }}</td>
                <td style="width: 5%">{{ item.userName }}</td>
                <td style="width: 50%" class="left">
                  <div>
                    <p>问题类型：{{ item.keyType }}</p>
                    <p>问题描述：{{ item.contentS }}</p>
                    <p v-if="!isEmptyOrWhitespace(item.contentA)">建议方案一：{{ item.contentA }}</p>
                    <p v-if="!isEmptyOrWhitespace(item.contentB)">建议方案二：{{ item.contentB }}</p>
                    <p v-if="!isEmptyOrWhitespace(item.contentC)">建议方案三：{{ item.contentC }}</p>
                  </div>
                </td>
                <th style="width: 15%; text-align: center">
                  <div v-if="item.image != []" v-viewer>
                    <span v-for="(ite, ind) in item.image" :key="ind" style="color: #0068ff; cursor: pointer; text-decoration: underline">
                      <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                      ><br />
                    </span>
                    <div v-viewer v-if="currentImageUrl" style="display: none">
                      <img :src="currentImageUrl" alt="附件图片" />
                    </div>
                  </div>
                  <div v-if="item.filePath != '' && item.filePath != ',' && item.filePath">
                    <span style="color: red; cursor: pointer" @click="down(item.filePath)"
                      >工程上传文件<br />（可点击下载{{ item.filePath.split(",").length }}个文件）</span
                    >
                  </div>
                </th>
                <td style="width: 10%" :rowspan="item.reply ? 2 : 1">
                  <div>
                    <p @click="editClick(item)" v-if="reviewInfo.status < 10" style="cursor: pointer">+编辑问题</p>
                    <p @click="deleteClick(item)" v-if="reviewInfo.status < 10" style="cursor: pointer">+删除评审</p>
                    <!--<p @click="replyClick(item)" v-if="item.status == 3" style="cursor: pointer">+回复评审</p>
                         <p @click="backClick(item)" v-if="item.status == 3" style="cursor: pointer">+撤回评审</p>
                        <p @click="copy1Click(item)" v-if="item.status == 2" style="cursor: pointer">+复制评审</p> -->
                  </div>
                </td>
              </tr>
              <tr v-if="item.reply" :key="'2' + index">
                <td style="width: 10%">{{ item.reply.solutionTime }}</td>
                <td style="width: 5%">{{ item.reply.userName }}</td>
                <td style="width: 50%" class="left">
                  <div>
                    <p style="color: #ff9900">回复内容：{{ item.reply.content }}</p>
                  </div>
                </td>
                <th style="width: 15%; text-align: center">
                  <div v-if="item.reply.image" v-viewer>
                    <span
                      v-for="(ite, ind) in item.reply.image.split(',')"
                      :key="ind"
                      style="color: #0068ff; cursor: pointer; text-decoration: underline"
                    >
                      <span @click="downimg(ite)">附件{{ sortby(ite) }}</span
                      ><br />
                    </span>
                  </div>
                  <div v-if="item.reply.filePath">
                    <span style="color: red; cursor: pointer" @click="down(item.reply.filePath)"
                      >文件（可点击下载{{ item.reply.filePath.split(",").length }}个文件）</span
                    >
                  </div>
                </th>
              </tr>
            </template>
          </tbody>
        </table>
        <!-- </a-collapse-panel> 
        </a-collapse>-->
        <table border="1" v-if="addTr">
          <tr>
            <td style="width: 5%">
              <p>{{ currentreviewNum }}</p>
            </td>
            <td style="width: 5%">
              <p>
                Q<i>{{ orderData.length + 1 }}</i>
              </p>
            </td>
            <td style="width: 10%">
              <p>{{ addTime }}</p>
            </td>
            <td style="width: 5%">
              <p>{{ userName }}</p>
            </td>
            <td style="text-align: left; width: 50%">
              <template>
                <a-form layout="inline">
                  <!-- <a-row>
                    <a-col :span="12">
                      <a-form-item label="评审类型" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-select showSearch allowClear optionFilterProp="label" v-model="askType">
                          <a-select-option v-for="(item, index) in selectData3" :key="index" :value="item.display_" :label="item.caption_">{{
                            item.caption_
                          }}</a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item style="width: 100%; margin: 0">
                        <span style="color: red; margin-left: 10px; font-size: 13px">*如为问题确认，不需要上传工程文件；反之则不需上传图片</span>
                      </a-form-item>
                    </a-col>
                  </a-row> -->
                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="问题类型" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-select
                          showSearch
                          allowClear
                          optionFilterProp="label"
                          v-model="reviewdata.keyType"
                          @change="setEstimate($event, secondaryCategory)"
                          @search="handleSearch($event, secondaryCategory)"
                          @blur="handleBlur($event, secondaryCategory)"
                        >
                          <a-select-option v-for="(item, index) in secondaryCategory" :key="index" :value="item.category" :label="item.category">{{
                            item.category
                          }}</a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                    <!-- <a-col :span="8">
                      <a-form-item style="margin-left: 50px">
                        <a-button style="font-weight: 500" @click="click2">关键字</a-button>
                      </a-form-item>
                    </a-col> -->
                  </a-row>

                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="问题描述" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-textarea :auto-size="{ minRows: 2, maxRows: 5 }" allowClear v-model="reviewdata.problemDescription" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="建议方案一" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-textarea
                          :auto-size="{ minRows: 1, maxRows: 3 }"
                          type="text"
                          placeholder="请撰写方案一"
                          allowClear
                          v-model="reviewdata.proposalA"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item label="建议方案二" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" style="width: 100%; margin: 0">
                        <a-textarea
                          :auto-size="{ minRows: 1, maxRows: 3 }"
                          type="text"
                          placeholder="请撰写方案二"
                          allowClear
                          v-model="reviewdata.proposalB"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="24">
                      <a-form-item
                        label="建议方案三"
                        prop="proposalC"
                        :label-col="{ span: 4 }"
                        :wrapper-col="{ span: 16 }"
                        style="width: 100%; margin: 0"
                      >
                        <a-textarea
                          :auto-size="{ minRows: 1, maxRows: 3 }"
                          type="text"
                          placeholder="请撰写方案三"
                          allowClear
                          v-model="reviewdata.proposalC"
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <span style="color: #ff9900; margin-left: 80px; font-size: 13px">如多方案请按相应输入框输入建议方案</span>
                </a-form>
              </template>
            </td>
            <td style="width: 15%">
              <template v-if="askType == 0">
                <a-upload
                  name="file"
                  ref="fileRef"
                  :before-upload="beforeUpload1"
                  :customRequest="httpRequest"
                  :file-list="fileListData"
                  @change="handleChange1"
                  @preview="handlePreview"
                >
                  <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
                  <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(1)" title="点击 ctrl+V粘贴上传">
                    粘贴图片
                  </a-button>
                </a-upload>
                <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible = false" :width="900">
                  <img style="width: 100%; height: 100%" :src="previewImage" />
                </a-modal>
              </template>
              <template v-else>
                <a-upload
                  name="file"
                  ref="fileRef"
                  :before-upload="beforeUpload1"
                  :customRequest="httpRequest"
                  :file-list="fileList"
                  @change="handleChange2"
                  :show-upload-list="true"
                >
                  <a-button> 上传文件 </a-button>
                </a-upload>
              </template>
            </td>
            <td style="width: 10%">
              <p><a-button type="primary" @click="uploadclick()" :loading="spinning1"> 提交</a-button></p>
              <p><a-button type="primary" @click="cancelclick"> 取消</a-button></p>
            </td>
          </tr>
        </table>
      </div>
      <!--其余信息-->
      <div style="width: 100%; display: flex; padding: 10px" class="information" v-if="page != 0">
        <!--分派只编辑查看评审人-->
        <table border="1" style="width: 100%" v-if="review_Type == 'assign'">
          <tr>
            <td>研发评审</td>
            <td>评审人</td>
            <td colspan="4" style="width: 200px">
              <a-select v-if="edit" v-model="reviewInfo.reviewUser">
                <a-select-option v-for="(item, index) in reviewUserList" :key="index" :value="item.text">{{ item.text }}</a-select-option>
              </a-select>
              <div v-else>{{ reviewInfo.reviewUser }}</div>
            </td>
          </tr>
        </table>
        <table border="1" style="width: 100%" v-else>
          <!--研发评审 评审时不允许编辑评审人 可更改基本信息评审类别-->
          <template style="width: 100%">
            <tr>
              <td rowspan="11">研发评审</td>
              <td>评审人</td>
              <td colspan="4" style="width: 200px">
                <div>{{ reviewInfo.reviewUser }}</div>
              </td>
              <td>评审类别</td>
              <td colspan="4">
                <a-select
                  v-if="edit && review_Type == 'review'"
                  showSearch
                  allowClear
                  mode="multiple"
                  v-model="reviewInfo.reviewTypeSecond"
                  :filter-option="filterOption"
                >
                  <a-select-option v-for="(item, index) in secondaryCategory" :key="index" :value="item.category">{{
                    item.category
                  }}</a-select-option>
                </a-select>
                <div v-else>{{ reviewInfo.reviewTypeSecond }}</div>
              </td>
            </tr>
            <tr>
              <td colspan="2">解决方案（难点工序分开评审）</td>
              <td colspan="4">
                产前会议
                <a-radio-group v-if="edit && review_Type == 'review'" v-model="reviewInfo.preproductionMeeting">
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
                <span v-else
                  ><a-radio :checked="reviewInfo.preproductionMeeting === true">是</a-radio>
                  <a-radio :checked="reviewInfo.preproductionMeeting === false">否</a-radio></span
                >
              </td>
              <td>预计合格率</td>
              <td colspan="3">
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.expectedPassRate" />
                <div v-else>{{ reviewInfo.expectedPassRate }}</div>
              </td>
            </tr>
            <tr>
              <td rowspan="3">订单处置意见</td>
              <td>
                <a-checkbox
                  v-if="edit && review_Type == 'review'"
                  v-model="reviewInfo.modifyDesign"
                  @change="Disposalopinion(reviewInfo.modifyDesign, 'modifyDesignContent', 'modifyDesignAtt')"
                  >修改设计</a-checkbox
                >
                <a-checkbox v-else :checked="reviewInfo.modifyDesign">修改设计</a-checkbox>
              </td>
              <td colspan="7">
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.modifyDesignContent" :disabled="!reviewInfo.modifyDesign" />
                <div v-else>{{ reviewInfo.modifyDesignContent }}</div>
              </td>
              <td style="width: 100px">
                <a-button
                  v-if="edit && review_Type == 'review'"
                  size="small"
                  type="primary"
                  @click="getAttachment('modifyDesignAtt', '修改设计', 'upload')"
                  >添加附件</a-button
                >
                <a-button v-else size="small" type="primary" @click="getAttachment('modifyDesignAtt', '修改设计', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>
                <a-checkbox
                  v-if="edit && review_Type == 'review'"
                  v-model="reviewInfo.eqOpinion"
                  @change="Disposalopinion(reviewInfo.eqOpinion, 'eqOpinionContent', 'eqOpinionAtt')"
                  >EQ问客意见</a-checkbox
                >
                <a-checkbox v-else :checked="reviewInfo.eqOpinion">EQ问客意见</a-checkbox>
              </td>
              <td colspan="7">
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.eqOpinionContent" :disabled="!reviewInfo.eqOpinion" />
                <div v-else>{{ reviewInfo.eqOpinionContent }}</div>
              </td>
              <td>
                <a-button
                  v-if="edit && review_Type == 'review'"
                  size="small"
                  type="primary"
                  @click="getAttachment('eqOpinionAtt', 'EQ问客意见', 'upload')"
                  >添加附件</a-button
                >
                <a-button v-else size="small" type="primary" @click="getAttachment('eqOpinionAtt', 'EQ问客意见', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>
                <a-checkbox
                  v-if="edit && review_Type == 'review'"
                  v-model="reviewInfo.cancelOrder"
                  @change="Disposalopinion(reviewInfo.cancelOrder, 'cancelOrderContent', 'cancelOrderAtt')"
                  >取消订单</a-checkbox
                >
                <a-checkbox v-else :checked="reviewInfo.cancelOrder">取消订单</a-checkbox>
              </td>
              <td colspan="7">
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.cancelOrderContent" :disabled="!reviewInfo.cancelOrder" />
                <div v-else>{{ reviewInfo.cancelOrderContent }}</div>
              </td>
              <td>
                <a-button
                  v-if="edit && review_Type == 'review'"
                  size="small"
                  type="primary"
                  @click="getAttachment('cancelOrderAtt', '取消订单', 'upload')"
                  >添加附件</a-button
                >
                <a-button v-else size="small" type="primary" @click="getAttachment('cancelOrderAtt', '取消订单', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>资料处理</td>
              <td colspan="8">
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.dataHandle" />
                <div v-else>{{ reviewInfo.dataHandle }}</div>
              </td>
              <td>
                <a-button
                  v-if="edit && review_Type == 'review'"
                  size="small"
                  type="primary"
                  @click="getAttachment('dataHandleAtt', '资料处理', 'upload')"
                  >添加附件</a-button
                >
                <a-button v-else size="small" type="primary" @click="getAttachment('dataHandleAtt', '资料处理', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>MI处理</td>
              <td colspan="8">
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.miHandle" />
                <div v-else>{{ reviewInfo.miHandle }}</div>
              </td>
              <td>
                <a-button v-if="edit && review_Type == 'review'" size="small" type="primary" @click="getAttachment('miHandleAtt', 'MI处理', 'upload')"
                  >添加附件</a-button
                >
                <a-button v-else size="small" type="primary" @click="getAttachment('miHandleAtt', 'MI处理', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>MI备注</td>
              <td colspan="8">
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.miRemark" />
                <div v-else>{{ reviewInfo.miRemark }}</div>
              </td>
              <td>
                <a-button v-if="edit && review_Type == 'review'" size="small" type="primary" @click="getAttachment('miRemarkAtt', 'MI备注', 'upload')"
                  >添加附件</a-button
                >
                <a-button v-else size="small" type="primary" @click="getAttachment('miRemarkAtt', 'MI备注', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>图纸作业</td>
              <td colspan="3">
                <a-button
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'review'"
                  @click="getAttachment('flowImageAtt', '流程图', 'upload')"
                  >添加流程图附件</a-button
                >
                <a-button size="small" type="primary" v-else @click="getAttachment('flowImageAtt', '流程图', 'view')">查看流程图附件</a-button>
              </td>
              <td colspan="3">
                <a-button
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'review'"
                  @click="getAttachment('stackImageAtt', '压合图', 'upload')"
                  >添加压合图附件</a-button
                >
                <a-button size="small" type="primary" v-else @click="getAttachment('stackImageAtt', '压合图', 'view')">查看压合图附件</a-button>
              </td>
              <td colspan="3">
                <a-button
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'review'"
                  @click="getAttachment('otherImageAtt', '其他图纸', 'upload')"
                  >添加其他图纸附件</a-button
                >
                <a-button size="small" type="primary" v-else @click="getAttachment('otherImageAtt', '其他图纸', 'view')">查看其他图纸附件</a-button>
              </td>
            </tr>
            <tr>
              <td rowspan="2">超能力成本分析</td>
              <td style="text-align: left" colspan="9">
                <div>成本影响项:</div>
                <a-textarea v-if="edit && review_Type == 'review'" :auto-size="{ minRows: 2 }" v-model="reviewInfo.costImpactItem" />
                <div v-else>{{ reviewInfo.costImpactItem }}</div>
              </td>
            </tr>
            <tr>
              <td style="text-align: left" colspan="9">
                <span>成本增加值:</span>
                <a-input v-if="edit && review_Type == 'review'" style="width: 300px" v-model="reviewInfo.costIncreaseValue" />
                <span v-else>{{ reviewInfo.costIncreaseValue || " " }}</span
                >元/㎡, <span>计算公式:</span>
                <a-input v-if="edit && review_Type == 'review'" v-model="reviewInfo.calcFormula" style="width: 300px" />
                <span v-else>{{ reviewInfo.calcFormula }}</span>
              </td>
            </tr></template
          >
          <!--会同评审-->
          <template style="width: 100%">
            <tr>
              <td rowspan="3">会同评审</td>
              <td>
                <a-checkbox v-if="edit && review_Type == 'review'" v-model="reviewInfo.quality">品质</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.quality">品质</a-checkbox>
              </td>
              <td colspan="8">
                <input
                  v-if="edit && review_Type == 'jointreview' && reviewInfo.deparment == '11'"
                  v-model="reviewInfo.qualityContent"
                  style="width: 100%"
                />
                <div v-else>{{ reviewInfo.qualityContent }}</div>
              </td>
              <td>
                <a-button
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'jointreview' && reviewInfo.deparment == '11'"
                  @click="getAttachment('qualityAtt', '品质', 'upload')"
                  >添加附件</a-button
                >
                <a-button size="small" type="primary" v-else @click="getAttachment('qualityAtt', '品质', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>
                <a-checkbox v-if="edit && review_Type == 'review'" v-model="reviewInfo.production">生产</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.production">生产</a-checkbox>
              </td>
              <td colspan="8">
                <input
                  v-if="edit && review_Type == 'jointreview' && reviewInfo.deparment == '9'"
                  v-model="reviewInfo.productionContent"
                  style="width: 100%"
                />
                <div v-else>{{ reviewInfo.productionContent }}</div>
              </td>
              <td>
                <a-button
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'jointreview' && reviewInfo.deparment == '9'"
                  @click="getAttachment('productionAtt', '生产', 'upload')"
                  >添加附件</a-button
                >
                <a-button size="small" type="primary" v-else @click="getAttachment('productionAtt', '生产', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td>
                <a-checkbox v-if="edit && review_Type == 'review'" v-model="reviewInfo.purchase">采购</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.purchase">采购</a-checkbox>
              </td>
              <td colspan="8">
                <input
                  v-if="edit && review_Type == 'jointreview' && reviewInfo.deparment == '采购'"
                  v-model="reviewInfo.purchaseContent"
                  style="width: 100%"
                />
                <div v-else>{{ reviewInfo.purchaseContent }}</div>
              </td>
              <td>
                <a-button
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'jointreview' && reviewInfo.deparment == '采购'"
                  @click="getAttachment('purchaseAtt', '采购', 'upload')"
                  >添加附件</a-button
                >
                <a-button size="small" type="primary" v-else @click="getAttachment('purchaseAtt', '采购', 'view')">查看附件</a-button>
              </td>
            </tr>
          </template>
          <!--投产信息 只有评审策划时以及后面状态才可以查看-->
          <template style="width: 100%" v-if="review_Type != 'assign' && review_Type != 'review' && review_Type != 'jointreview'">
            <tr>
              <td rowspan="3">投产信息</td>
              <td>生产型号</td>
              <td>
                <div>{{ reviewInfo.orderNo }}</div>
              </td>
              <td>投产数量</td>
              <td>
                <div>{{ reviewInfo.num }}</div>
              </td>
              <td>压合次数</td>
              <td>
                <div>{{ reviewInfo.pressTimes }}</div>
              </td>
              <td>新/返单</td>
              <td>
                <div>{{ reviewInfo.isReOrderStr }}</div>
              </td>
              <td>是否加急</td>
              <td>
                <div>{{ reviewInfo.isJiaji ? "是" : "否" }}</div>
              </td>
            </tr>
            <tr>
              <td>交货日期</td>
              <td>
                <div>{{ reviewInfo.deliveryDate }}</div>
              </td>
              <td>投产面积</td>
              <td>
                <div>{{ reviewInfo.area }}</div>
              </td>
              <td>板材</td>
              <td>
                <div>{{ reviewInfo.boardBrand }}</div>
              </td>
              <td>补料次数</td>
              <td>
                <div>{{ reviewInfo.suppleTimes }}</div>
              </td>
              <td>HDI/HVA</td>
              <td>
                <div>{{ reviewInfo.hdiOrHva ? "是" : "否" }}</div>
              </td>
            </tr>
            <tr>
              <td>备投率</td>
              <td :colspan="edit ? 7 : 9">
                <div>{{ reviewInfo.planInvestRate }}</div>
              </td>
              <td v-if="edit && review_Type == 'planning'">
                <a-button size="small" type="primary" @click="addRow">添加监控点</a-button>
              </td>
              <td v-if="edit && review_Type == 'planning'">
                <a-button size="small" type="primary" @click="deleteRow" style="background-color: #cf1b26; border: #cf1b26">删除最后一行</a-button>
              </td>
            </tr>
          </template>
          <!--产前策划 只有评审策划时以及后面状态才可以查看-->
          <template style="width: 100%" v-if="review_Type != 'assign' && review_Type != 'review' && review_Type != 'jointreview'">
            <tr>
              <td :rowspan="TableList.length + 2">产前策划</td>
              <td :rowspan="TableList.length + 1">实施过程监控点</td>
              <td>生产难点工序<a style="color: red">*</a></td>
              <td>工序负责人<a style="color: red">*</a></td>
              <td>难度等级<a style="color: red">*</a></td>
              <td>难度信息<a style="color: red">*</a></td>
              <td colspan="2">处置措施<a style="color: red">*</a></td>
              <td>工艺负责人<a style="color: red">*</a></td>
              <td>项目负责人<a style="color: red">*</a></td>
              <td>品质负责人<a style="color: red">*</a></td>
            </tr>
            <tr v-for="(item, index) in TableList" :key="index">
              <td>
                <a-select
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-model="item.processDifficultPoints"
                  v-if="edit && review_Type == 'planning'"
                  @change="processDifficultPointsChange(item.processDifficultPoints, index)"
                >
                  <a-select-option v-for="(item, index) in processflow" :key="index" :value="item.valueMember" :label="item.valueMember">
                    {{ item.valueMember }}
                  </a-select-option>
                </a-select>

                <div v-else>{{ item.processDifficultPoints }}</div>
              </td>
              <td>
                <a-select
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-model="item.processChargeUser"
                  v-if="edit && review_Type == 'planning'"
                  @change="setEstimate1($event, index, 'processChargeUser')"
                  @search="handleSearch1($event, index, 'processChargeUser')"
                  @blur="handleBlur1($event, index, 'processChargeUser')"
                >
                  <a-select-option v-for="(item, index) in HeadList" :key="index" :value="item" :label="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
                <div v-else>{{ item.processChargeUser }}</div>
              </td>
              <td>
                <a-input v-if="edit && review_Type == 'planning'" v-model="item.difficultyLevel" />
                <div v-else>{{ item.difficultyLevel }}</div>
              </td>
              <td>
                <a-input v-if="edit && review_Type == 'planning'" v-model="item.difficultyInfo" />
                <div v-else>{{ item.difficultyInfo }}</div>
              </td>
              <td colspan="2">
                <a-input v-if="edit && review_Type == 'planning'" v-model="item.solutionMethod" />
                <div v-else>{{ item.solutionMethod }}</div>
              </td>
              <td>
                <a-select
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-model="item.techChargeUser"
                  v-if="edit && review_Type == 'planning'"
                  @change="setEstimate1($event, index, 'techChargeUser')"
                  @search="handleSearch1($event, index, 'techChargeUser')"
                  @blur="handleBlur1($event, index, 'techChargeUser')"
                >
                  <a-select-option v-for="(item, index) in filtration(userList, 10)" :key="index" :value="item.text" :label="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
                <div v-else>{{ item.techChargeUser }}</div>
              </td>
              <td>
                <a-select
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-model="item.projectChargeUser"
                  v-if="edit && review_Type == 'planning'"
                  @change="setEstimate1($event, index, 'projectChargeUser')"
                  @search="handleSearch1($event, index, 'projectChargeUser')"
                  @blur="handleBlur1($event, index, 'projectChargeUser')"
                >
                  <a-select-option v-for="(item, index) in filtration(userList, 16)" :key="index" :value="item.text" :label="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
                <div v-else>{{ item.projectChargeUser }}</div>
              </td>
              <td>
                <a-select
                  showSearch
                  allowClear
                  optionFilterProp="label"
                  v-model="item.qualityChargeUser"
                  v-if="edit && review_Type == 'planning'"
                  @change="setEstimate1($event, index, 'qualityChargeUser')"
                  @search="handleSearch1($event, index, 'qualityChargeUser')"
                  @blur="handleBlur1($event, index, 'qualityChargeUser')"
                >
                  <a-select-option v-for="(item, index) in filtration(userList, 11)" :key="index" :value="item.text" :label="item.text">
                    {{ item.text }}
                  </a-select-option>
                </a-select>

                <div v-else>{{ item.qualityChargeUser }}</div>
              </td>
            </tr>
            <tr>
              <td>方案会签</td>
              <td>方案策划人</td>
              <td colspan="4">
                <a-button
                  style="float: right"
                  v-if="edit && review_Type == 'planning' && !reviewInfo.setPlanUser"
                  type="primary"
                  size="small"
                  @click="signature('setPlanUser')"
                  >签名</a-button
                >
                <div v-else>{{ reviewInfo.setPlanUser }}</div>
              </td>
              <td colspan="2">技术中心负责人</td>
              <td colspan="3">
                <a-button
                  style="float: right"
                  v-if="review_Type == 'plancheck' && !reviewInfo.techUser"
                  type="primary"
                  size="small"
                  @click="signature('techUser')"
                  >签名</a-button
                >
                <div v-else>{{ reviewInfo.techUser }}</div>
              </td>
            </tr>
          </template>
          <!--品质情况 评审总结与审核可查看-->
          <template style="width: 100%" v-if="page == 2 && (review_Type == 'all' || review_Type == 'allcheck')">
            <tr>
              <td rowspan="2">品质情况</td>
              <td colspan="10">补投记录</td>
            </tr>
            <tr>
              <td colspan="10">无</td>
            </tr>
          </template>
          <!--工艺研发总结 评审总结与审核可查看-->
          <template style="width: 100%" v-if="page == 2 && (review_Type == 'all' || review_Type == 'allcheck')">
            <tr>
              <td rowspan="3">工艺研发总结</td>
              <td>生产评价</td>
              <td colspan="9">
                <a-checkbox v-if="edit && review_Type == 'all'" v-model="reviewInfo.planValid">方案有效</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.planValid">方案有效</a-checkbox>
                <a-checkbox v-if="edit && review_Type == 'all'" v-model="reviewInfo.reputPlan">重出方案</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.reputPlan">重出方案</a-checkbox>
                <a-checkbox v-if="edit && review_Type == 'all'" v-model="reviewInfo.sameProblem">同类问题固化(案例编号)</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.sameProblem">同类问题固化(案例编号)</a-checkbox>
                <a-checkbox v-if="edit && review_Type == 'all'" v-model="reviewInfo.adjustPrice">调整单价</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.adjustPrice">调整单价</a-checkbox>
                <a-checkbox v-if="edit && review_Type == 'all'" v-model="reviewInfo.cancelProduce">取消制作</a-checkbox>
                <a-checkbox v-else :checked="reviewInfo.cancelProduce">取消制作</a-checkbox>
              </td>
            </tr>
            <tr>
              <td>备注</td>
              <td colspan="8">
                <a-input v-if="edit && review_Type == 'all'" v-model="reviewInfo.craftOrItRemark" />
                <div v-else>{{ reviewInfo.craftOrItRemark }}</div>
              </td>
              <td>
                <a-button
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'all'"
                  @click="getAttachment('otherImageAtt', '研发总结备注', 'upload')"
                  >添加附件</a-button
                >
                <a-button size="small" type="primary" v-else @click="getAttachment('otherImageAtt', '研发总结备注', 'view')">查看附件</a-button>
              </td>
            </tr>
            <tr>
              <td colspan="1">工艺/研发工程师</td>
              <td colspan="2">
                <a-button
                  style="float: right"
                  size="small"
                  type="primary"
                  v-if="edit && review_Type == 'all' && !reviewInfo.craftOrItEngineer"
                  @click="signature('craftOrItEngineer')"
                  >签名</a-button
                >
                <div v-else>{{ reviewInfo.craftOrItEngineer }}</div>
              </td>
              <td colspan="2">工艺/研发部经理审核</td>
              <td colspan="2">
                <a-button
                  size="small"
                  style="float: right"
                  type="primary"
                  v-if="review_Type == 'allcheck' && !reviewInfo.craftOrItManager"
                  @click="signature('craftOrItManager')"
                  >签名</a-button
                >
                <div v-else>{{ reviewInfo.craftOrItManager }}</div>
              </td>
            </tr>
          </template>
          <!--备注 评审总结与审核可查看-->
          <template style="width: 100%" v-if="page == 2 && (review_Type == 'all' || review_Type == 'allcheck')">
            <tr>
              <td>备注</td>
              <td colspan="10" style="text-align: left; padding-left: 15px">
                1、在制程难点评审时由研发部确认是否在生产时跟踪。<br />
                2、研发评审必须要详尽，附图。以便于资料处理的完善性。<br />
                3、产前策划需要对所有的难点识别。并责任到人。系统将在型号到达后通知相关人员跟进核实。<br />
                4、产前策划的内容品质部的稽查工程师要负责稽查。<br />
                5、品质数据收集完成后提交工艺总结评价.<br />
                6、一般超制程能力由研发与工程确认解决方案即可，非常规超能力需开产前会议的由各部门会签。
              </td>
            </tr>
          </template>
        </table>
      </div>
    </div>
    <a-modal
      title="关键字索引"
      :visible="dataVisible1"
      @cancel="reportHandleCancel"
      @ok="handleOk1"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="1100"
    >
      <key-word :selectData1="selectData1" ref="keyWord" :eqQuestion="eqQuestion" :StepName1="StepName1"></key-word>
    </a-modal>
    <!--    编辑问题弹窗-->
    <a-modal
      title="请输入问题描述"
      :visible="dataVisible2"
      @cancel="reportHandleCancel"
      @ok="handleOk2"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="600"
    >
      <div @click="bodyClick">
        <div>
          <a-form :model="editForm">
            <a-form-item>
              <a-textarea
                :auto-size="{ minRows: 1, maxRows: 3 }"
                v-model="editForm.problemDescription"
                placeholder="问题描述"
                allowClear
              ></a-textarea>
            </a-form-item>
            <a-form-item>
              <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalA" placeholder="输入建议方案一" allowClear></a-textarea>
            </a-form-item>
            <a-form-item>
              <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalB" placeholder="输入建议方案二" allowClear></a-textarea>
            </a-form-item>
            <a-form-item>
              <a-textarea :auto-size="{ minRows: 1, maxRows: 3 }" v-model="editForm.proposalC" placeholder="输入建议方案三" allowClear></a-textarea>
            </a-form-item>
          </a-form>
        </div>
        <a-form-item style="padding: 10px">
          <template v-if="editForm.askType == '0'">
            <a-upload
              name="file"
              ref="fileRef"
              :before-upload="beforeUploadnew"
              :customRequest="httpRequest"
              :file-list="fileListData3"
              @change="handleChange3"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
              <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(3)" title="ctrl+V 粘贴上传">
                粘贴图片
              </a-button>
            </a-upload>
            <a-modal :visible="previewVisible" :footer="null" centered @cancel="handleCancelPreview">
              <img style="width: 100%; height: 100%" :src="previewImage" />
            </a-modal>
          </template>
          <template v-else>
            <a-upload name="file" ref="fileRef" :customRequest="httpRequest" :file-list="fileList4" @change="handleChange4">
              <a-button> 上传文件 </a-button>
            </a-upload>
          </template>
        </a-form-item>
      </div>
    </a-modal>
    <!--    回复问题弹窗-->
    <a-modal
      title="请输入回复内容"
      :visible="dataVisible3"
      @cancel="reportHandleCancel"
      @ok="handleOk3"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="1000"
    >
      <div @click="bodyClick">
        <a-form>
          <!-- <template>
          <a-textarea v-model="replyForm.Quiz" :auto-size="{ minRows: 2, maxRows:5 }" ></a-textarea>  
        </template> -->
          <div style="display: flex">
            <div style="width: 48%">
              <div>问题描述：{{ replyForm.contentS }}</div>
              <div v-if="replyForm.image1 != '' || replyForm.image1 != []" v-viewer>
                <img style="height: 50px; width: 50px; margin: 10px" v-for="(ite, index) in replyForm.image1" :key="index" :src="ite" />
              </div>
              <a-radio-group v-model="replyForm.value" @change="radioChange" style="display: flex; flex-direction: column">
                <a-radio style="height: 30px" :value="1" v-if="!isEmptyOrWhitespace(replyForm.contentA)">方案一： {{ replyForm.contentA }}</a-radio>
                <a-radio style="height: 30px" :value="2" v-if="!isEmptyOrWhitespace(replyForm.contentB)">方案二：{{ replyForm.contentB }}</a-radio>
                <a-radio style="height: 30px" :value="4" v-if="!isEmptyOrWhitespace(replyForm.contentC)">方案三：{{ replyForm.contentC }}</a-radio>
                <a-radio :value="3"
                  ><span style="display: inline-block; line-height: 60px">其他方案:</span>
                  <a-textarea
                    v-model="replyForm.proposalC"
                    style="width: 80%"
                    :auto-size="{ minRows: 2, maxRows: 5 }"
                    :disabled="replyForm.value == '3' ? false : true"
                  ></a-textarea>
                </a-radio>
              </a-radio-group>
            </div>
          </div>
          <div>
            <a-upload
              name="file"
              ref="fileRef"
              :before-upload="beforeUpload1"
              :customRequest="httpRequest"
              :file-list="fileListData2"
              @change="handleChange5"
              list-type="picture-card"
              @preview="handlePreview"
            >
              <a-button style="font-weight: 500; height: 40px"> 上传图片 </a-button>
              <a-button style="font-weight: 500; margin-top: 6px; height: 40px" @click.stop="showCopy(5)" title="ctrl+V 粘贴上传">
                粘贴图片
              </a-button>
            </a-upload>
            <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview" :width="900">
              <img style="width: 100%; height: 100%" :src="previewImage" />
            </a-modal>
            <a-upload
              name="file"
              ref="fileRef"
              :before-upload="beforeUpload11"
              :customRequest="httpRequest"
              :file-list="fileList2"
              @change="handleChange22"
            >
              <a-button v-if="fileList2.length < 1"> 上传文件 </a-button>
            </a-upload>
          </div>
        </a-form>
      </div>
    </a-modal>
    <!--数据填写错误提示弹窗-->
    <a-modal
      title="提示信息"
      :visible="Promptpopup"
      @cancel="Promptpopup = false"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      centered
      :width="400"
    >
      <div v-for="(item, index) in messageList" :key="index" style="line-height: 3ch">
        <span>{{ item.label }}</span>
        {{ item.value }}
      </div>
      <template #footer>
        <a-button type="primary" @click="Promptpopup = false"> 关闭 </a-button>
      </template>
    </a-modal>
    <!-- 订单退回 -->
    <a-modal
      title="订单退回"
      :visible="returnVisible"
      @cancel="returnVisible = false"
      @ok="dehandleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="500"
      centered
    >
      <a-form>
        <a-row>
          <a-col :span="24">
            <a-form-item label="*退回原因" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }" class="required">
              <a-textarea placeholder="请输入退回原因" v-model="returnReason" allowClear autoFocus :auto-size="{ minRows: 4, maxRows: 8 }">
              </a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <!--附件列表-->
    <a-modal
      :title="AttlistTitle"
      :visible="AttachmentVisible"
      @cancel="AttachmentVisible = false"
      @ok="deAttOk"
      ok-text="确定"
      destroyOnClose
      centered
      :maskClosable="false"
      :width="800"
    >
      <div :loading="Uploading">
        <a-empty v-if="Attfiledata.length == 0 && Imgfiledata.length == 0 && Operationtype == 'view'" />
        <a-upload
          accept=".jpg,.png,.jpeg"
          name="file"
          :disabled="Operationtype == 'view'"
          :before-upload="AddbeforeUpload"
          :customRequest="AtthttpRequest"
          :file-list="Imgfiledata"
          @change="ImghandleChange"
          @preview="handlePreview"
          list-type="picture-card"
        >
          <a-button v-if="Operationtype != 'view'"> 上传图片 </a-button>
        </a-upload>
        <a-upload name="file" :customRequest="AtthttpRequest" :disabled="Operationtype == 'view'" :file-list="Attfiledata" @change="AtthandleChange">
          <a-button v-if="Operationtype != 'view'"> 上传文件 </a-button>
        </a-upload>
      </div>
    </a-modal>
  </a-spin>
</template>
<script>
import $ from "jquery";
import { checkPermission } from "@/utils/abp";
import moment from "moment";
import keyWord from "@/pages/gongcheng/projectPage/eqModule/keyWord";
import axios from "axios";
import { upLoadFlyingFile, getClassList, BigClassList, eqQuestion, getEqList, user } from "@/services/projectMake";
import {
  reviewmain,
  reviewinfo,
  reviewcategory,
  setreviewinfosave,
  submit,
  editingproblems,
  replyproblems,
  setdeleetproblems,
  backProblems,
  techflowlist,
  techflowuserlist,
  totalreviewuserlist,
} from "@/services/projectReview";
import { allreviewsubmit, setreviewsignname, reviewcategorylist, emSReviewmainuserlist, allreviewback } from "@/services/gongju/Reviewinitiated";
import { reviewbuttoncheck } from "@/services/gongju/Reviewinitiated";
import { mapState } from "vuex";
import { remove } from "nprogress";
export default {
  name: "ReviewDetails",
  components: { keyWord },
  computed: {
    ...mapState("account", ["user"]),
  },
  data() {
    return {
      checkVisible: false,
      checkData: [],
      checkType: "",
      check: false,
      ButtonName: "",
      returnReason: "",
      returnVisible: false,
      reviewUserList: [],
      secondaryCategory: [],
      processflow: [],
      processList: [],
      userList: [],
      HeadList: [],
      Uploading: false,
      AttlistTitle: "",
      Imgfiledata: [],
      Attfiledata: [],
      Addtype: "",
      Operationtype: "",
      page: 0,
      review_Type: "",
      TableList: [],
      addTime: moment(new Date()).format("YYYY-MM-DD "),
      previewVisible: false,
      spinning1: false,
      Promptpopup: false,
      edit: false,
      fileListData2: [],
      spinning: false,
      currentreviewNum: 1,
      askType: 0, // 评审类型（0：问题确认，1：文件确认）
      eqQuestion: [],
      addTr: false,
      OrderNo: this.$route.query.OrderNo,
      reviewdata: {},
      isFileType: false,
      messageList: [],
      fileListData: [],
      fileList: [],
      arrData: [],
      orderData: [],
      fileList2: [],
      userName: "",
      copyorderData: [],
      path: "",
      previewImage: "",
      showCopyType: "",
      file: null,
      startloading: false,
      show: false,
      reviewInfo: {},
      Copyreview: {},
      StepName1: "",
      selectData3: [],
      editForm: {
        id: "",
        problemDescription: "", // 问题描述
        proposalA: "", // 建议A
        proposalB: "", //建议B
        proposalC: "", //建议C
        path: "", // 图片地址
        filePath: "", // 文件地址
        askType: 0,
      },
      dataVisible1: false,
      dataVisible2: false,
      selectData1: [],
      allowAddTr: false,
      currentImageUrl: "",
      activeKey: ["1"],
      replyForm: {},
      selectData: {},
      fileListData3: [],
      fileList4: [],
      dataVisible3: false,
      AttachmentVisible: false,
    };
  },
  created() {
    this.dehandleOk = this.debounce(this.handleOk, 500);
    this.deAttOk = this.debounce(this.AttOk, 500);
  },
  mounted() {
    this.review_Type = this.$route.query.review_Type;
    this.page = this.$route.query.page;
    this.getreviewcategory();
    this.getOrderList(1);
    this.getTable();
    this.getSelect();
    this.getSelect1();
    this.getUserlist();
    this.Productionprocessflow();
    let params = {
      StepName: "",
      Problem: "",
      KeyWord: "",
      Factoryid: this.$route.query.joinFactoryId,
    };
    eqQuestion(params).then(res => {
      if (res.code) {
        this.eqQuestion = res.data;
      } else {
        this.$message.error(res.message);
      }
    });
    this.ButtonName = "";
    if (this.page == 0) {
      if (this.review_Type == "edit") {
        this.ButtonName = "编辑";
      } else if (this.review_Type == "add") {
        this.ButtonName = "编辑";
      } else if (this.review_Type == "check") {
        this.ButtonName = "发起审核";
      }
    } else if (this.page == 1) {
      if (this.review_Type == "assign") {
        this.ButtonName = "分配";
      } else if (this.review_Type == "review") {
        this.ButtonName = "评审";
      } else if (this.review_Type == "jointreview") {
        this.ButtonName = "会同评审";
      } else if (this.review_Type == "planning") {
        this.ButtonName = "策划";
      } else if (this.review_Type == "plancheck") {
        this.ButtonName = "策划审核";
      }
    } else if (this.page == 2) {
      if (this.review_Type == "all") {
        this.ButtonName = "评审总结";
      } else if (this.review_Type == "allcheck") {
        this.ButtonName = "总结审核";
      }
    }
  },
  methods: {
    checkPermission,
    setEstimate(value, list) {
      this.$set(this.reviewdata, "keyType", value);
    },
    handleSearch(value, list) {
      this.setEstimate(value, list);
    },
    handleBlur(value, list) {
      this.setEstimate(value, list);
    },
    setEstimate1(value, index, key) {
      this.$set(this.TableList[index], key, value);
    },
    handleSearch1(value, index, key) {
      this.setEstimate1(value, index, key);
    },
    handleBlur1(value, index, key) {
      this.setEstimate1(value, index, key);
    },
    //获取评审人员下拉
    getUserlist() {
      emSReviewmainuserlist(this.$route.query.joinFactoryId).then(res => {
        if (res.code) {
          this.reviewUserList = res.data;
        }
      });
    },
    continueclick() {
      if (this.checkType == "edit") {
        this.EditreviewNew();
      } else if (name == "Submit") {
        this.SubmitforreviewNew();
      }
    },
    //一级评审类别改变时 触发二级评审类别选择
    changeReviewType(type) {
      this.secondaryCategory = [];
      if (type == "change") {
        this.$set(this.reviewInfo, "reviewTypeSecond", null);
        this.$set(this.reviewdata, "keyType", null);
      }
      if (!this.reviewInfo.reviewType || this.reviewInfo.reviewType.length == 0) {
        return;
      }
      let review_type = type == "change" ? this.reviewInfo.reviewType.join(";") : this.reviewInfo.reviewType;
      if (this.review_Type == "review" || this.review_Type == "add" || this.review_Type == "edit") {
        reviewcategorylist(this.$route.query.joinFactoryId, { reviewType: review_type }).then(res => {
          if (res.code) {
            this.secondaryCategory = res.data;
          }
        });
      }
    },
    AttOk() {
      this.AttachmentVisible = false;
      if (this.Operationtype == "upload") {
        let arr = [];
        this.Imgfiledata.forEach(item => {
          arr.push(item.response);
        });
        this.Attfiledata.forEach(item => {
          arr.push(item.response);
        });
        this.reviewInfo[this.Addtype] = arr.length == 0 ? null : arr.join(",");
      }
    },
    //添加/编辑/查看 附件 通用方法
    getAttachment(Addtype, title, type) {
      this.Addtype = Addtype;
      this.AttlistTitle = type == "upload" ? `添加/编辑【${title}】附件` : `查看【${title}】附件`;
      this.Operationtype = type;
      let arr = this.reviewInfo[this.Addtype]?.split(",") || [];
      this.Imgfiledata = [];
      this.Attfiledata = [];
      if (arr.length > 0) {
        arr.forEach((item, index) => {
          let fileType = item.split(".")[item.split(".").length - 1].toLowerCase();
          const fileName = decodeURIComponent(item.split("/").pop()); // 提取文件名
          //图片文件与其他文件进行分类
          if (fileType == "jpg" || fileType == "png" || fileType == "jpeg") {
            this.Imgfiledata.push({
              uid: index,
              name: fileName,
              response: item,
              status: "done",
              url: item,
              thumbUrl: item,
            });
          } else {
            this.Attfiledata.push({
              uid: index,
              name: fileName,
              status: "done",
              response: item,
              url: item,
              thumbUrl: item,
            });
          }
        });
      }
      this.AttachmentVisible = true;
    },
    Disposalopinion(val, key, att) {
      if (val == false) {
        this.reviewInfo[key] = ""; //清空内容
        this.reviewInfo[att] = null; //清空文件
      }
    },
    //签名
    signature(key) {
      this.reviewInfo[key] = this.user.name;
    },
    //添加监控点
    addRow() {
      let keys = [
        "processDifficultPoints",
        "processChargeUser",
        "difficultyLevel",
        "difficultyInfo",
        "solutionMethod",
        "techChargeUser",
        "projectChargeUser",
        "qualityChargeUser",
      ];
      let list = this.TableList[this.TableList.length - 1];
      for (let index = 0; index < keys.length; index++) {
        if (!list[keys[index]]) {
          this.$message.error("请先填写完整监控点之后再添加");
          return;
        }
      }
      this.TableList.push({});
    },
    //删除监控点
    deleteRow() {
      if (this.TableList.length > 1) {
        this.TableList.splice(this.TableList.length - 1, 1);
      } else {
        this.$message.error("最少需要录入一个监控点");
      }
    },
    handleCancelPreview() {
      this.previewVisible = false;
    },
    // 评审详情编辑问题
    editClick(item) {
      this.dataVisible2 = true;
      this.editForm.id = item.id;
      this.editForm.path = item.image;
      this.editForm.filePath = item.filePath;
      this.editForm.problemDescription = item.contentS;
      this.editForm.proposalA = item.contentA;
      this.editForm.proposalB = item.contentB;
      this.editForm.proposalC = item.contentC;
      this.editForm.askType = item.askType;
      this.fileListData3 = [];
      this.fileList4 = [];
      if (item.image) {
        item.image.forEach((e, index) => {
          const fileName = e.split("/").pop(); // 提取文件名
          this.fileListData3.push({
            uid: index,
            name: fileName,
            status: "done",
            url: e,
            thumbUrl: e, //缩略图地址
          });
        });
      }
      if (item.filePath) {
        item.filePath.split(",").forEach((e, index) => {
          const fileName = e.split("/").pop(); // 提取文件名
          this.fileList4.push({
            uid: index,
            name: fileName,
            status: "done",
            url: e,
          });
        });
      }
    },
    //删除问题
    deleteClick(item) {
      if (confirm("确认删除该问题吗？")) {
        let params = {
          id: item.id,
          reviewSource: Number(this.$route.query.reviewSource),
        };
        setdeleetproblems(item.id).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getTable();
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 回复问题
    replyClick(item) {
      this.dataVisible3 = true;
      this.replyForm = {};
      this.fileListData2 = [];
      this.replyForm.id = item.id;
      this.replyForm.contentS = item.contentS;
      this.replyForm.contentA = item.contentA;
      this.replyForm.contentB = item.contentB;
      this.replyForm.contentC = item.contentC;
      this.replyForm.image1 = item.image;
      this.replyForm.path = "";
      this.replyForm.image = "";
    },
    // 撤回问题
    backClick(item) {
      if (confirm("确认撤回该问题吗？")) {
        let params = {
          id: item.id,
          reviewSource: Number(this.$route.query.reviewSource),
        };
        backProblems(item.id).then(res => {
          if (res.code) {
            this.getOrderList();
            this.$message.success("撤回成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //复制问题
    copy1Click(item) {
      if (!this.allowAddTr) {
        this.$message.error("该订单有未回复的评审,暂不可以复制！");
        return;
      }
      if (confirm("确认复制该评审吗？")) {
        const newItem = { ...item };
        delete newItem.reply;
        newItem.status = 1;
        newItem.createTime = moment(new Date()).format("MM-DD HH:mm");
        this.orderData.push(newItem);
        this.uploadclick(newItem);
      }
    },
    handleChange5({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData2);
      }
      let namesArray = fileList.map(item => item.response);
      this.arrData2 = namesArray;
      this.replyForm.image = this.arrData2.toString(",");
      if (this.isFileType) {
        this.fileListData2 = fileList;
        if (this.fileListData2.length == 0) {
          this.replyForm.image = "";
        }
      }
    },
    handleChange22({ fileList }, data) {
      if (this.isFileType) {
        this.fileList2 = fileList;
        if (this.fileList2.length == 0) {
          this.replyForm.path = "";
        }
        this.replyForm.path = fileList.map(item => item.response)[0];
      }
    },
    handleOk2() {
      let params = this.editForm;
      if (this.isEmptyOrWhitespace(params.problemDescription)) {
        this.$message.warning("请输入问题描述");
        return;
      }
      if (this.isEmptyOrWhitespace(params.proposalA) && this.isEmptyOrWhitespace(params.proposalB) && this.isEmptyOrWhitespace(params.proposalC)) {
        this.$message.warning("请填写建议方案");
        return;
      }
      let str = "";
      this.fileListData3.forEach(e => {
        if (e.response) {
          str += e.response + ",";
        } else if (e.url) {
          str += e.url + ",";
        }
      });
      str = str.substring(0, str.length - 1);
      params.path = str;
      let str1 = "";
      this.fileList4.forEach(e => {
        if (e.response) {
          str1 += e.response + ",";
        } else if (e.url) {
          str1 += e.url + ",";
        }
      });
      str1 = str1.substring(0, str1.length - 1);
      params.filePath = str1;
      if (this.editForm.askType == "1" && !params.filePath) {
        this.$message.warning("文件确认请上传附件");
        return;
      }
      this.dataVisible2 = false;
      editingproblems(params).then(res => {
        if (res.code) {
          this.$message.success("编辑成功");
          this.fileListData3 = [];
          this.arrData1 = [];
          this.fileList4 = [];
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 编辑删除图片
    delClick(item) {
      if (confirm("确认删除该图片？")) {
        this.editForm.path.splice(this.editForm.path.indexOf(item), 1);
      }
    },
    radioChange() {
      if (this.replyForm.value == "1") {
        this.replyForm.Quiz = this.replyForm.contentA;
      }
      if (this.replyForm.value == "2") {
        this.replyForm.Quiz = this.replyForm.contentB;
      }
      if (this.replyForm.value == "3") {
        this.replyForm.Quiz = this.replyForm.proposalC;
      }
      if (this.replyForm.value == "4") {
        this.replyForm.Quiz = this.replyForm.contentC;
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().trim().startsWith(input.toLowerCase()) == true;
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    //下载图片
    downimg(path) {
      this.currentImageUrl = path; // 设置当前图片URL
      this.$nextTick(() => {
        this.$viewerApi({
          images: [this.currentImageUrl],
        });
      });
    },
    // 下载工程文件
    down(item) {
      if (item) {
        var data = item.split(",");
        for (var i = 0; i < data.length; i++) {
          (function (i) {
            var name = "";
            if (data[i].indexOf("EQ%5C") != -1) {
              name = decodeURIComponent(data[i].split("EQ%5C")[1]);
            } else {
              name = decodeURIComponent(data[i].split("/").slice(-1)[0]);
            }
            const xhr = new XMLHttpRequest();
            xhr.open("GET", data[i], true);
            xhr.responseType = "blob";
            xhr.onload = function () {
              if (xhr.status === 200) {
                const blob = xhr.response;
                const link = document.createElement("a");
                link.href = window.URL.createObjectURL(blob);
                link.download = name;
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }
            };
            xhr.send();
          })(i);
        }
      }
    },
    sortby(img) {
      return this.imglist.findIndex(item => item == img) + 1;
    },
    qsort(id) {
      return this.orderData.findIndex(item => item.id == id) + 1;
    },
    handleOk3() {
      if (this.replyForm.value == undefined) {
        this.$message.warning("请选择方案！");
        return;
      }
      if (this.replyForm.value == "3") {
        this.replyForm.Quiz = this.replyForm.proposalC;
      }
      if (this.replyForm.Quiz == "") {
        this.$message.warning("请填写其他方案！");
        return;
      }

      this.dataVisible3 = false;
      let params = {
        id: this.replyForm.id,
        Quiz: this.replyForm.Quiz,
        image: this.replyForm.image,
        path: this.replyForm.path,
        reviewInfoId: this.reviewInfo.id,
      };
      replyproblems(params).then(res => {
        if (res.code) {
          this.$message.success("回复成功");
          this.replyForm.Quiz = "";
          this.replyForm.image = "";
          this.replyForm.path = "";
          this.replyForm.value = undefined;
          this.replyForm.proposalC = "";
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Productionprocessflow() {
      let JoinFactoryId = this.$route.query.joinFactoryId;
      techflowlist(JoinFactoryId).then(res => {
        if (res.code) {
          this.processflow = res.data;
        }
      });
      techflowuserlist(JoinFactoryId).then(res => {
        if (res.code) {
          this.processList = res.data;
        }
      });
      totalreviewuserlist(JoinFactoryId).then(res => {
        if (res.code) {
          this.userList = res.data;
        }
      });
    },
    processDifficultPointsChange(val, index) {
      this.HeadList = [];
      this.processList.forEach(item => {
        if (val.trim() == item.valueMember1.trim()) {
          this.TableList[index].processChargeUser = item.valueMember;
          this.HeadList.push(item.valueMember);
        }
      });
      if (this.HeadList.length == 0) {
        this.TableList[index].processChargeUser = "";
      }
    },
    filtration(arr, iType) {
      if (arr.length == 0) {
        return [];
      } else {
        let arr_ = arr.filter(item => {
          return item.iType == iType;
        });
        return arr_;
      }
    },
    // 获取订单评审记录
    getOrderList(type) {
      this.spinning = true;
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      reviewinfo(JoinFactoryId, OrderNo, BusinessOrderNo, this.$route.query.reviewNo)
        .then(res => {
          if (res.code) {
            this.reviewInfo = res.data.reviewInfo;
            if (type == 1) {
              this.changeReviewType();
            }
            this.TableList = res.data.reviewControlPoints.length == 0 || !res.data.reviewControlPoints ? [{}] : res.data.reviewControlPoints;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    getTable() {
      this.spinning = true;
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      reviewmain(JoinFactoryId, OrderNo, BusinessOrderNo, this.$route.query.reviewNo)
        .then(res => {
          if (res.code) {
            this.orderData = res.data.reviewMains;
            this.imglist = [];
            let pathstring = "";
            res.data.reviewMains.forEach(item => {
              if (item.image) {
                pathstring += item.image + ",";
              }
              if (item.reply && item.reply.image) {
                pathstring += item.reply.image + ",";
              }
              let content = item.content;
              var arr = content.split("|||");
              var arr_ = arr.length > 1 ? arr[1].split(";") : "";
              item.contentS = arr[0];
              item.contentA = arr_[0];
              item.contentB = arr_[1];
              item.contentC = arr_[2];
              let enContent = item.enContent;
              if (enContent) {
                var arr_en = enContent.split("|||");
                var arr_en_ = arr_en.length > 1 ? arr_en[1].split(";") : "";
                item.contentS_en = arr_en[0];
                item.contentA_en = arr_en_[0];
                item.contentB_en = arr_en_[1];
                item.contentC_en = arr_en_[2];
              }
              if (item.image) {
                var a = item.image.split(",");
                item.image = a;
              }
            });
            this.imglist = pathstring.split(",");
            this.imglist.pop();
            this.allowAddTr = true;
            if (this.orderData.filter(item => item.status == 1).length > 0) {
              this.currentreviewNum = this.orderData.filter(item => item.status == 1)[0].reviewNumber;
            } else if (this.orderData.filter(item => item.status == 2).length > 0) {
              const maxNUm = Math.max(...this.orderData.filter(item => item.status == 2).map(item => item.reviewNumber));
              this.currentreviewNum = maxNUm + 1;
              if (this.orderData.filter(item => item.status == 3).length > 0) {
                this.allowAddTr = false;
              }
            }
            this.orderData = this.addNumProperty(this.orderData);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    isEmptyOrWhitespace(content) {
      if (!content) {
        return true;
      } else {
        return /^\s*$/.test(content);
      }
    },
    handleChange3({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData3);
      }
      let namesArray = fileList.map(item => item.response);
      this.arrData1 = namesArray;
      this.fileListData3 = fileList;
      if (this.fileListData3.length == 0) {
        this.editForm.path = "";
      }
    },
    handleChange4({ fileList }, data) {
      this.fileList4 = fileList;
      var a = [];
      if (this.fileList4.length == 0) {
        this.editForm.filePath = "";
      } else {
        for (let index = 0; index < fileList.length; index++) {
          a.push(fileList[index].response);
        }
        this.editForm.filePath = a.join(",");
      }
    },
    addNumProperty(arr) {
      if (arr.length == 0) {
        this.copyorderData = [];
      }
      const grouped = arr.reduce((acc, item) => {
        let key = item.reviewNumber;
        if (!key) {
          key = 0;
        }
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        this.copyorderData = Object.values(acc);
        return acc;
      }, {});
      let newArr = [];
      let fornum = 0;
      for (const [_, items] of Object.entries(grouped)) {
        fornum += items.length;
        let replyNum = 0;
        for (let i = 0; i < items.length; i++) {
          if (i == 0) {
            items[i].num = items.length;
          } else {
            items[i].num = 0;
          }
          if (items[i].reply) {
            replyNum++;
          }
          newArr.push(items[i]);
        }
        newArr[newArr.length - items.length].num = newArr[newArr.length - items.length].num + replyNum;
      }
      return newArr;
    },
    handleOk1() {
      this.dataVisible1 = false;
      let formData = this.$refs.keyWord.selectData;
      this.$set(this.reviewdata, "keyType", formData.stepName);
      this.$set(this.reviewdata, "problemDescription", formData.problemDescription);
      this.$set(this.reviewdata, "proposalA", formData.proposalA);
      this.$set(this.reviewdata, "proposalB", formData.proposalB);
      this.$set(this.reviewdata, "proposalC", formData.proposalC);
    },
    reportHandleCancel() {
      this.dataVisible4 = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.replyForm.Quiz = "";
      this.replyForm.image = "";
      this.replyForm.path = "";
      this.replyForm.value = undefined;
      this.replyForm.proposalC = "";
      this.fileListData3 = [];
      this.arrData1 = [];
      this.arrData2 = [];
      this.fileList4 = [];
    },
    // 关键字
    click2() {
      this.StepName1 = this.reviewdata.keyType;
      this.dataVisible1 = true;
    },
    getSelect() {
      let OrderNo = this.$route.query.OrderNo;
      let BusinessOrderNo = this.$route.query.businessOrderNo;
      let JoinFactoryId = this.$route.query.joinFactoryId;
      BigClassList(JoinFactoryId, OrderNo, BusinessOrderNo).then(res => {
        if (res.code) {
          this.selectData1 = res.data.sort((a, b) => a.iType - b.iType);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取评审下拉选择
    getSelect1() {
      let params = [1118];
      getClassList(params).then(res => {
        if (res.code) {
          this.selectData3 = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getreviewcategory() {
      let data = this.$route.query;
      reviewcategory(data.joinFactoryId).then(res => {
        if (res.code) {
          this.selectData = res.data;
        }
      });
    },
    cancelclick() {
      this.addTr = false;
      this.fileListData = [];
      this.path = "";
    },
    // 提交
    uploadclick(item) {
      var val = {};
      if (item) {
        val.askType = item.askType;
        val.businessOrderNo = item.businessOrderNo;
        val.reviewSource = item.reviewSource;
        val.joinFactoryId = item.joinFactoryId;
        val.keyType = item.keyType;
        val.orderNo = item.orderNo;
        val.problemDescription = item.contentS;
        val.proposalA = item.contentA;
        val.proposalB = item.contentB;
        val.proposalC = item.contentC;
        val.reviewNumber = item.reviewNumber;
        if (item.image) {
          this.path = item.image.join(",");
        }
      } else {
        val = this.reviewdata;
      }
      if (!this.path && this.askType == 1) {
        this.$message.warning("请上传文件");
        return;
      }
      if (!val.keyType) {
        val.keyType = " ";
      }
      if (this.askType == 0 && !item) {
        if (this.isEmptyOrWhitespace(val.problemDescription)) {
          this.$message.warning("请填写问题描述");
          return;
        }
        if (this.isEmptyOrWhitespace(val.proposalA) && this.isEmptyOrWhitespace(val.proposalB) && this.isEmptyOrWhitespace(val.proposalC)) {
          this.$message.warning("请填写建议方案");
          return;
        }
      }
      let params = val;
      if (params.problemDescription) {
        var arr1 = params.problemDescription.split("");
        if (arr1.length > 500) {
          arr1 = arr1.slice(0, 500);
        }
        params.problemDescription = arr1.join("");
      }

      if (params.proposalA) {
        var arr2 = params.proposalA.split("");
        if (arr2.length > 200) {
          arr2 = arr2.slice(0, 200);
        }
        params.proposalA = arr2.join("");
      }

      if (params.proposalB) {
        var arr3 = params.proposalB.split("");
        if (arr3.length > 200) {
          arr3 = arr3.slice(0, 200);
        }
        params.proposalB = arr3.join("");
      }

      if (params.proposalC) {
        var arr4 = params.proposalC.split("");
        if (arr4.length > 200) {
          arr4 = arr4.slice(0, 200);
        }
        params.proposalC = arr4.join("");
      }
      params.orderNo = this.$route.query.OrderNo;
      if (this.$route.query.reviewSource) {
        params.reviewSource = Number(this.$route.query.reviewSource);
      }
      params.joinFactoryId = this.$route.query.joinFactoryId;
      params.businessOrderNo = this.$route.query.businessOrderNo;
      params.askType = this.askType;
      this.spinning1 = true;
      params.reviewNumber = this.$route.query.reviewNo;
      if (this.path) {
        const pathArray = this.path.split(",").filter(element => element !== "" && element);
        this.path = pathArray.join(",");
      }
      params.path = this.path;
      submit(params)
        .then(res => {
          if (res.code) {
            if (!item) {
              this.$message.success("提交成功");
            }
            this.fileListData = [];
            this.fileList = [];
            this.arrData = [];
            this.path = "";
            this.askType = 0;
            this.addTr = false;
            this.reviewdata = {};
            this.getTable();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning1 = false;
        });
    },
    buttonCheck(key, name) {
      reviewbuttoncheck(this.reviewInfo.id, key).then(res => {
        if (res.code) {
          if (res.data.length) {
            this.checkData = res.data;
            this.checkVisible = true;
            this.checkType = name;
            this.check = !this.checkData.some(item => item.error == 1);
          } else {
            if (name == "edit") {
              this.EditreviewNew();
            } else if (name == "Submit") {
              this.SubmitforreviewNew();
            }
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Editreview() {
      if (Number(this.$route.query.reviewSource) === 0 || Number(this.$route.query.reviewSource) === 1) {
        this.buttonCheck("reviewStartEdit", "edit");
      } else {
        this.EditreviewNew();
      }
      this.TableList.forEach((item, index) => {
        this.processDifficultPointsChange(item.processDifficultPoints, index);
      });
    },
    EditreviewNew() {
      this.edit = true;
      if (this.page == 0 || this.review_Type == "review") {
        this.reviewInfo.reviewType = this.reviewInfo.reviewType ? this.reviewInfo.reviewType.split(";") : [];
      }
      if (this.page == 1 && this.review_Type == "review") {
        this.reviewInfo.reviewTypeSecond = this.reviewInfo.reviewTypeSecond ? this.reviewInfo.reviewTypeSecond.split(";") : [];
      }
      this.Copyreview = JSON.parse(JSON.stringify(this.reviewInfo));
    },
    Cancelreview() {
      this.reviewInfo = this.Copyreview;
      if (this.page == 0 || this.review_Type == "review") {
        this.reviewInfo.reviewType = this.reviewInfo.reviewType ? this.reviewInfo.reviewType.join(";") : null;
      }
      if (this.page == 1 && this.review_Type == "review") {
        this.reviewInfo.reviewTypeSecond = this.reviewInfo.reviewTypeSecond ? this.reviewInfo.reviewTypeSecond.join(";") : null;
      }
      this.edit = false;
    },
    Savereview() {
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      this.messageList = [];
      let params = {
        innerIsolationRing: "内层隔离环(mm)",
        outerRingWidth: "外层环宽(mm)",
        coreBoardTthickness: "芯板厚度(mm)",
        drlToDrl: "孔到孔间距(mm)",
        // innerMinLineSpace: "内层线宽线距(mm)",
        // outerMinLineSpace: "外层线宽线距(mm)",
        appearanceToCopper: "外形到铜(mm)",
      };
      Object.keys(params).forEach(key => {
        if (this.reviewInfo[key] && !x.test(this.reviewInfo[key])) {
          this.messageList.push({ label: params[key], value: "请输入正确格式" });
        }
      });
      if (this.messageList.length) {
        this.setstyle(this.messageList);
        this.Promptpopup = true;
        return;
      }
      if (this.addTr) {
        this.$message.warning("请先提交保存评审内容后再进行保存");
        return;
      }
      if (typeof this.reviewInfo.reviewType == "object") {
        this.reviewInfo.reviewType = this.reviewInfo.reviewType ? this.reviewInfo.reviewType.join(";") : null;
      }
      if (typeof this.reviewInfo.reviewTypeSecond == "object") {
        this.reviewInfo.reviewTypeSecond = this.reviewInfo.reviewTypeSecond ? this.reviewInfo.reviewTypeSecond.join(";") : null;
      }
      this.spinning = true;
      this.TableList.forEach(item => {
        item.id = item.id ? item.id : null;
      });
      this.reviewInfo.controlPointListInput = this.TableList;
      this.reviewInfo.reviewNo = this.$route.query.reviewNo;
      setreviewsignname(this.reviewInfo).then(res => {
        if (res.code) {
          setreviewinfosave(this.reviewInfo)
            .then(res => {
              if (res.code) {
                this.$message.success("保存成功");
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.getOrderList();
              this.spinning = false;
              this.edit = false;
            });
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      });
    },
    setstyle(val) {
      $("#ReviewformData .ant-form-item-label label").each(function (index, label) {
        if (val.some(ele => label.innerText == ele.label)) {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem">' + text + "</i>";
          });
        } else {
          label.innerHTML = label.innerText.replace(label.innerText, function (text) {
            return '<i class="searchPosElem1">' + text + "</i>";
          });
        }
      });
    },
    ReturnClick() {
      this.returnReason = "";
      this.returnVisible = true;
    },
    handleOk() {
      if (!this.returnReason) {
        this.$message.warn("请输入回退原因");
        return;
      }
      this.spinning = true;
      this.returnVisible = false;
      allreviewback(this.reviewInfo.id, this.ButtonName, this.returnReason)
        .then(res => {
          if (res.code) {
            this.getOrderList();
            this.$message.success(res.message);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    Submitforreview() {
      if (Number(this.$route.query.reviewSource) === 0 || Number(this.$route.query.reviewSource) === 1) {
        this.buttonCheck("reviewInfoSubmit", "Submit");
      } else {
        this.SubmitforreviewNew();
      }
    },
    SubmitforreviewNew() {
      if (this.edit) {
        this.$message.error("请先保存评审信息后再进行提交");
        return;
      }
      let keys = [
        "processDifficultPoints",
        "processChargeUser",
        "difficultyLevel",
        "difficultyInfo",
        "solutionMethod",
        "techChargeUser",
        "projectChargeUser",
        "qualityChargeUser",
      ];
      if (this.page == 0 || this.review_Type == "review") {
        if (!this.reviewInfo.reviewType || this.reviewInfo.reviewType.length == 0) {
          this.$message.error("请选择评审类别!!");
          return;
        }
      } else if (this.review_Type == "planning") {
        const isValid = this.TableList.every(item => {
          return keys.every(key => {
            if (!item[key]) {
              this.$message.error("请填写完整监控点");
              return false; // 表示当前 item 校验失败
            }
            return true; // 表示当前 key 校验通过
          });
        });

        if (!isValid) {
          // 如果校验失败，直接返回，不执行后续提交操作
          return;
        }
      }

      if (confirm("您确认要提交全部评审吗？")) {
        setreviewsignname(this.reviewInfo).then(res => {
          if (res.code) {
            allreviewsubmit(this.reviewInfo.id, this.ButtonName).then(re => {
              if (re.code) {
                this.getOrderList();
                this.$message.success("提交成功");
              } else {
                this.$message.error(re.message);
              }
            });
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    addClick() {
      if (!this.allowAddTr) {
        this.$message.error("该订单有未回复的评审,暂不可以新增加问题！");
        return;
      }
      this.addTr = true;
      this.reviewdata = {};
      user().then(res => {
        if (res.code) {
          this.userName = res.data.realName_;
        }
      });
    },
    AtthandleChange({ fileList }, data) {
      this.Attfiledata = fileList;
    },
    ImghandleChange({ fileList }, data) {
      this.Imgfiledata = fileList;
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    async AtthttpRequest(data, type) {
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, "text/html");
      const parsedText = doc.body.textContent;
      const replacedText = parsedText.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      this.Uploading = true;
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSReview-main/up-load-flying-file-v2", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess(res.message);
            }
          } else {
            this.$message.error(res.message);
            i = shardCount;
            this.Uploading = false;
          }
        });
      }
    },
    AddbeforeUpload(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isFileType =
          file.name.toLowerCase().indexOf(".jpg") != -1 ||
          file.name.toLowerCase().indexOf(".png") != -1 ||
          file.name.toLowerCase().indexOf(".jpeg") != -1;
        if (!isFileType) {
          _this.$message.error("上传图片支持.jpg/.png/.jpeg格式文件");
          reject();
          return isFileType;
        } else {
          resolve();
        }
      });
    },
    beforeUpload1(file) {
      if (this.askType == 1) {
        // const hasChinese = /[\u4e00-\u9fa5]/.test(file.name);
        // this.isFileType =  !hasChinese
        // if (!this.isFileType) {
        //   this.$message.error('文件确认上传文件不得含有中文字符');
        // }
        // return this.isFileType
      } else {
        this.isFileType = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!this.isFileType) {
          this.$message.error("问题确认只支持.jpg/.png图片格式文件");
        }
        return this.isFileType;
      }
    },
    async httpRequest(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadFlyingFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    beforeUploadnew(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.toLowerCase().indexOf(".jpg") != -1 || file.name.toLowerCase().indexOf(".png") != -1;
        if (!isJpgOrPng) {
          _this.$message.error("图片只支持.jpg/.png图片格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    beforeUpload11(file) {
      this.isFileType = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
      if (!this.isFileType) {
        this.$message.error("文件确认只支持.rar或.zip格式文件");
      }
      return this.isFileType;
    },
    handleChange2({ fileList }, data) {
      if (this.isFileType) {
        this.fileList = fileList;
        var a = [];
        if (this.fileList.length == 0) {
          this.path = "";
        } else {
          for (let index = 0; index < fileList.length; index++) {
            a.push(fileList[index].response);
          }
          this.path = a.join(",");
        }
      }
    },
    handleChange1({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileListData);
      }
      if (this.isFileType) {
        const removedFiles = this.fileListData.filter(file => !fileList.includes(file));
        removedFiles.forEach(file => {
          const removedElement = file.response;
          if (removedElement && this.arrData.includes(removedElement)) {
            this.arrData = this.arrData.filter(element => !element.includes(removedElement));
            this.path = this.arrData.toString(",");
          }
        });
        this.fileListData = fileList;
        for (let index = 0; index < this.fileListData.length; index++) {
          const element = this.fileListData[index].response;
          if (element && !this.arrData.includes(element)) {
            this.arrData.push(element);
            this.path = this.arrData.toString(",");
          }
        }

        if (this.fileListData.length === 0) {
          this.path = "";
        }
      }
    },
    showCopy(type) {
      window.addEventListener("paste", this.getClipboardFiles);
      this.showCopyType = type;
      try {
        navigator.clipboard.read().then(res => {
          const clipboardItems = res;
          if (clipboardItems[0].types[0].indexOf("image") > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then(b => {
              const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, {
                type: clipboardItems[0].types[0],
              });
              this.file = files;
              this.getClipboardFiles();
            });
          } else {
            this.$message.error("粘贴内容不是图片");
            return;
          }
        });
      } catch (e) {
        ////console.log('出错了')
      }
    },
    bodyClick() {
      //console.log('bodyClick')
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload1(file); // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload1(file)) {
        this.startloading = true;
        const formData = new FormData();
        formData.append("file", file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/e-mSEQMain/up-load-flying-file", // 接口地址
          method: "post",
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => {
            if (res.code) {
              //console.log('res',res)
              file.status = "done";
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              if (this.showCopyType == "1") {
                this.handleChange1(file, arr);
              } else if (this.showCopyType == "5") {
                this.handleChange5(file, arr);
              } else if (this.showCopyType == "3") {
                this.handleChange3(file, arr);
              }
              this.startloading = false;
              this.show = false;
            } else {
              this.$message.error(res.message || "网络异常,请重试或检查网络连接状态");
              this.startloading = false;
              this.show = false;
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
          });
      }
    },
    handlePreview(file) {
      this.$nextTick(() => {
        this.$viewerApi({
          images: [file.response || file.thumbUrl],
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.required /deep/ .ant-form-item-label > label {
  color: red !important;
}
/deep/.ant-input {
  border: 1px solid #595959;
}
/deep/.ant-select-selection {
  border: 1px solid #595959;
}
/deep/.searchPosElem {
  background-color: aquamarine;
  font: 14px / 1.17 "微软雅黑", arial, \5b8b\4f53;
}
/deep/.searchPosElem1 {
  background-color: #ffffff;
  font: 14px / 1.17 "微软雅黑", arial, \5b8b\4f53;
}
.left {
  text-align: left !important;
}
.scorllclass {
  margin: 0 10px 10px 10px;
  table {
    border-color: #e1e1e2;
    color: #000000;
    border-top-color: #ffffff00;
    width: 100%;
  }
  /deep/.ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }
  &::-webkit-scrollbar {
    //整体样式
    width: 1px; //y轴滚动条粗细
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    background: #b6b5b4;
    // #fff9e6
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}
td {
  padding: 5px;
  text-align: left;
  height: 31px;
}

.information {
  /deep/.ant-form-item .ant-form-item-label {
    font: 14px / 1.17 "微软雅黑", arial, \5b8b\4f53;
    color: #666;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    height: 30px;
    line-height: 30px;
  }
  /deep/.ant-form-item .ant-form-item-control-wrapper .ant-form-item-control {
    line-height: inherit;
    padding: 0px 5px;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    height: 30px;
  }
  /deep/.ant-form-item-children {
    line-height: 30px;
    div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  /deep/.ant-input {
    height: 26px;
    margin-top: 2px;
  }
  /deep/.ant-select-selection {
    height: 26px;
    margin-top: 2px;
  }
  /deep/.ant-select {
    width: 100%;
  }
  /deep/ .ant-select-selection__rendered {
    line-height: 26px;
  }
}
.Review {
  height: 100%;
  min-width: 1670px;
  background: white;
  padding: 10px;
  /deep/.ant-form-item {
    margin-bottom: 0;
  }
}
p {
  margin-bottom: 4px;
}
</style>
