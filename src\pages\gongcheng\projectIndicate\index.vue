<!-- 工程管理 - 工程指示  -->
<template>
  <a-spin :spinning="spinning">
    <div class="box1">
      <div class="leftContent" style="height: 100%; border: 2px solid rgb(233 230 230); border-bottom: none; overflow: auto; border-right: none">
        <ul>
          <!-- <p style="padding-left:5px;color:rgba(0, 0, 0, 0.85);line-height: 31px;border-bottom: 1px solid rgb(233 230 230);margin-bottom:0;" >{{OrderNo}}</p>              
            <li  @click="liClick('1')" :class="key == '1' ?'licolor':''"  >产品信息 </li> -->
          <li @click="liClick('1')" :class="key == '1' ? 'licolor' : ''">
            <span
              style="
                word-break: keep-all;
                vertical-align: top;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                width: 100%;
                display: inline-block;
              "
              >产品信息</span
            >
          </li>
          <!-- <li  @click="liClick('2')" :class="key == '2' ?'licolor':''"  >线路参数</li> -->
          <li @click="liClick('7')" :class="key == '7' ? 'licolor' : ''">特殊参数</li>
          <li @click="liClick('3')" :class="key == '3' ? 'licolor' : ''">阻抗信息</li>
          <li @click="liClick('4')" :class="key == '4' ? 'licolor' : ''">拼版设计</li>
          <li @click="liClick('8')" :class="key == '8' ? 'licolor' : ''">文件管理</li>
          <li @click="liClick('9')" :class="key == '9' ? 'licolor' : ''">说明文件</li>
          <li @click="liClick('10')" :class="key == '10' ? 'licolor' : ''">
            <span v-if="allNote"> 直通提示(1)</span>
            <span v-else>直通提示</span>
          </li>
          <li @click="liClick('11')" :class="key == '11' ? 'licolor' : ''">
            <span v-if="Numberofpieces"> 注意事项({{ Numberofpieces }})</span>
            <span v-else>注意事项</span>
          </li>
          <!-- <li  @click="liClick('5')" :class="key == '5' ?'licolor':''"  >阻焊丝印</li> -->
          <!-- <li  @click="liClick('6')" :class="key == '6' ?'licolor':''"  >出货信息</li> -->
        </ul>
      </div>
      <div class="rightContent" v-show="JSON.stringify(proOrderInfoDto) != '{}'">
        <template v-show="key == '1'">
          <pro-one
            v-show="key == '1'"
            :messageList="messageList"
            :proOrderInfoDto="proOrderInfoDto"
            :selectData="selectData"
            :factoryData="factoryData"
            :boardBrandList="boardBrandList"
            :solderRequire1="solderRequire"
            :Soldermaskparameters="Soldermaskparameters"
            :solderRequireStr="solderRequireStr"
            :sheetTraderList="sheetTraderList"
            ref="dom1"
            :editFlg1="editFlg1"
            @boardThickness="boardThickness"
            @changelayers="changelayers"
            :show0="show0"
            :show1="show1"
            :show2="show2"
            :showMore="showMore"
            :showCG="showCG"
            :showHDI="showHDI"
            :showMM="showMM"
            :requiredLinkConfigpro="requiredLinkConfigpro"
            :ManufacturerTG="ManufacturerTG"
            :boardtgList="boardtgList"
          ></pro-one>
        </template>
        <template>
          <drl-info
            v-show="key == '7'"
            :proOrderDrillingDto="proOrderDrillingDto"
            :drillToolDiameterDtos="drillToolDiameterDtos"
            :proOrderInfoDto="proOrderInfoDto"
            :selectData="selectData"
            :proOrderLineDto="proOrderLineDto"
            ref="dom7"
            :editFlg1="editFlg1"
            @formingChange="formingChange"
            :metricBritishSystem="metricBritishSystem"
          ></drl-info>
        </template>
        <!-- <template v-if="key ==  '2'">
                <line-info  
                :proOrderInfoDto="proOrderInfoDto"
                :proOrderLineDto="proOrderLineDto"
                :selectData="selectData"               
                ref="dom3"
                :editFlg1="editFlg1"
                :disFlg="disFlg"
                ></line-info>         
            </template> -->
        <template>
          <lamination-info
            v-show="key == '3'"
            :proOrderStackUpImpDto="proOrderStackUpImpDto"
            :selectData="selectData"
            :proOrderLineDto="proOrderLineDto"
            :editFlg1="editFlg1"
            ref="dom4"
            :ttype="'pro'"
            :boardBrandList="boardBrandList"
            :proOrderInfoDto="proOrderInfoDto"
            @GetProOrderInfo="GetProOrderInfo"
          ></lamination-info>
        </template>
        <template>
          <cutting-info
            v-show="key == '4'"
            :editFlg1="editFlg1"
            :pnlParameterDto="pnlParameterDto"
            :selectData="selectData"
            ref="domcutting"
          ></cutting-info>
        </template>
        <template v-show="key == '8'">
          <div v-show="key == '8'" style="width: 100%" class="div8">
            <a-table
              :columns="columns8"
              :dataSource="dataSource8"
              :pagination="false"
              :rowKey="
                (record, index) => {
                  return record.key;
                }
              "
              :loading="TableLoading8"
              :expandIcon="expandIcon"
              :expandIconColumnIndex="0"
              :defaultExpandAllRows="false"
              :customRow="onClickRow"
              :scroll="{ y: 739, x: 1300 }"
              :rowClassName="isRedRow1"
              class="listtable"
              ref="FlowList"
            >
              <span slot="num" slot-scope="text, record">
                {{ record.level }}
              </span>
              <template slot="action" slot-scope="record">
                <a href="javascript:;" @click="down(record)">下载</a>
                <a-divider type="vertical" />
                <a href="javascript:;" @click="del(record)">删除</a>
              </template>
            </a-table>
          </div>
        </template>
        <template v-show="key == '9'">
          <div style="width: 100%" class="div8" v-show="key == '9'">
            <spec-info :jobId="jobId" :proOrderInfoDto="proOrderInfoDto" ref="specInfoRef"></spec-info>
          </div>
        </template>
        <template v-show="key == '10'">
          <div style="width: 100%; height: 771px" class="Directprompt" v-show="key == '10'">
            <div v-if="allNote" class="note1">
              <div class="textNote" v-html="allNote"></div>
            </div>
            <div v-else>
              <a-empty :image="simpleImage" />
              <div style="height: 1px; background-color: #f0f0f0; width: 1543px"></div>
            </div>
          </div>
        </template>
        <template v-show="key == '11'">
          <div style="width: 100%; height: 771px" class="bzxx" v-show="key == '11'">
            <a-table
              :columns="columns4"
              :dataSource="jobData"
              :pagination="false"
              rowKey="id"
              :scroll="{ y: 739, x: 1300 }"
              :loading="jobTableLoading"
              class="jobstyle"
            >
              <template slot="picUrl" slot-scope="text, record">
                <viewer :images="picFilter(record)">
                  <img :src="item" v-for="(item, index) in picFilter(record)" :key="index" style="width: 20px; height: 20px" />
                </viewer>
              </template>
              <template slot="fileUrl" slot-scope="text, record">
                <a-tooltip title="编辑">
                  <a-icon type="edit" style="color: rgb(0, 0, 204)" @click="editClick(record)"></a-icon>
                </a-tooltip>
                <span style="color: rgb(0, 0, 204)">/</span>
                <a-tooltip title="下载附件">
                  <a-icon type="download" v-if="record.fileUrl" style="color: rgb(0, 0, 204)" @click="downFile(record.fileUrl)"></a-icon>
                </a-tooltip>
                <span v-if="record.fileUrl" style="color: rgb(0, 0, 204)">/</span>
                <a-tooltip title="删除">
                  <a-icon type="close-square" style="color: rgb(0, 0, 204)" @click="delFile(record)"></a-icon>
                </a-tooltip>
              </template>
            </a-table>
          </div>
        </template>
        <!-- <template v-if="key ==  '5'">  
                <solder-screen  
                :proOrderSolderDto="proOrderSolderDto"
                :selectData="selectData"
                @GetProOrderInfo="GetProOrderInfo"
                ref="dom6"
                :editFlg1="editFlg1"
                ></solder-screen>       
            </template>
            <template v-if="key ==  '6'">
                <shipment-info 
                :proOrderShippingDto="proOrderShippingDto"
                :selectData="selectData"
                ref="dom7"
                :editFlg1="editFlg1"
                ></shipment-info>
            </template>      -->
        <div class="btn">
          <div class="right">
            <a-upload
              accept=".zip,.rar"
              name="file"
              :multiple="false"
              :customRequest="customRequest"
              :showUploadList="false"
              ref="fileRef"
              :before-upload="beforeUpload1"
              v-show="false"
            >
              <a-button type="primary"> </a-button>
            </a-upload>
            <span class="box" v-if="showBtn">
              <a-button type="dashed" @click="toggleAdvanced" style="margin-top: 8px; margin-right: 5px; float: right">
                {{ advanced ? "收起" : "展开" }}
                <a-icon :type="advanced ? 'right' : 'left'" />
              </a-button>
            </span>
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionGeneratingProcessTest')"
              type="primary"
              @click="processClick('test')"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionGeneratingProcessTest') ? 'showclass' : ''"
              >新MI生成</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGraphicPreview')"
              type="primary"
              @click="previewClick"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGraphicPreview') ? 'showclass' : ''"
              >iPCB-CAM</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionIndicatesCheck')"
              @click="CheckClick"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionIndicatesCheck') ? 'showclass' : ''"
              >指示检查</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetPar')"
              @click="getInfoClick"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetPar') ? 'showclass' : ''"
              >获取参数</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionGeneratingProcess')"
              @click="processClick('formal')"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionGeneratingProcess') ? 'showclass' : ''"
              >生成流程</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngStack')"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              @click="laminationClick"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngStack') ? 'showclass' : ''"
              >叠层信息</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngPnl')"
              @click="CuttingClick"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngPnl') ? 'showclass' : ''"
              >开料拼版</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetWenKeUrl') && $route.query.typee != '5'"
              type="primary"
              @click="wenkeClick"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetWenKeUrl') && $route.query.typee != '5' ? 'showclass' : ''"
              >问客
            </a-button>
            <a-button
              type="primary"
              @click="mattersNeedingAttentionClick"
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngMattersNeedingAttention')"
              style="margin-top: 8px; margin-right: 5px; float: right"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngMattersNeedingAttention') ? 'Notcommon' : ''"
              >注意事项</a-button
            >
            <a-button
              type="primary"
              @click="webSocketLink"
              style="margin-top: 8px; margin-right: 5px; float: right"
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngSendScript')"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngSendScript') ? 'showclass' : ''"
              >发送脚本(D)</a-button
            >
            <a-button
              type="primary"
              @click="UpdateFile"
              style="margin-top: 8px; margin-right: 5px; float: right"
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngUploadFile')"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngUploadFile') ? 'Notcommon' : ''"
              >上传文件</a-button
            >
            <a-button
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              v-if="isCustRule"
              @click="CustomerRulesClick"
              :class="isCustRule ? 'showclass' : ''"
              >客户规则</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.ProductFeatures')"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              @click="Productcharacteristics"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.ProductFeatures') ? 'showclass' : ''"
              >产品特性</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.UpLoadWorkFile')"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              @click="Steelmeshupload"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.UpLoadWorkFile') ? 'Notcommon' : ''"
              >上传钢网</a-button
            >
            <a-button
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              @click="ProductionOrder"
              v-show="$route.query.typee == 2 && checkPermission('MES.EngineeringModule.EngineeringInstruction.NoticeReviewInfoV2')"
              :class="
                $route.query.typee == 2 && checkPermission('MES.EngineeringModule.EngineeringInstruction.NoticeReviewInfoV2') ? 'showclass' : ''
              "
              >生产单</a-button
            >
            <a-button
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.PpeSyncToMkt')"
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              @click="synchronous"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.PpeSyncToMkt') ? 'Notcommon' : ''"
              >同步市场</a-button
            >
            <a-button
              type="primary"
              style="margin-top: 8px; margin-right: 5px; float: right"
              @click="review"
              :class="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetReviewUrl') ? 'showclass' : ''"
              v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetReviewUrl')"
              >评审</a-button
            >
            <div v-if="buttonsmenu">
              <a-dropdown>
                <a-button type="primary" style="margin-top: 8px; margin-right: 5px; float: right" @click.prevent> 按钮菜单栏 </a-button>
                <template #overlay>
                  <a-menu class="tabRightClikBox3">
                    <a-menu-item
                      @click="processClick('test')"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionGeneratingProcessTest')"
                      >新MI生成</a-menu-item
                    >
                    <a-menu-item @click="previewClick" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGraphicPreview')"
                      >iPCB-CAM</a-menu-item
                    >
                    <a-menu-item
                      @click="CheckClick"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionIndicatesCheck')"
                      >指示检查</a-menu-item
                    >
                    <a-menu-item @click="getInfoClick" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetPar')"
                      >获取参数</a-menu-item
                    >
                    <a-menu-item
                      @click="processClick('formal')"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionGeneratingProcess')"
                      >生成流程</a-menu-item
                    >
                    <a-menu-item @click="laminationClick" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngStack')"
                      >叠层信息</a-menu-item
                    >
                    <a-menu-item @click="CuttingClick" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngPnl')"
                      >开料拼版</a-menu-item
                    >
                    <a-menu-item
                      @click="wenkeClick"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetWenKeUrl') && $route.query.typee != '5'"
                      >问客</a-menu-item
                    >
                    <a-menu-item
                      @click="mattersNeedingAttentionClick"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngMattersNeedingAttention')"
                      >注意事项</a-menu-item
                    >
                    <a-menu-item @click="webSocketLink" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngSendScript')"
                      >发送脚本(D)</a-menu-item
                    >
                    <a-menu-item @click="UpdateFile" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngUploadFile')"
                      >上传文件</a-menu-item
                    >
                    <a-menu-item @click="CustomerRulesClick" v-if="isCustRule">客户规则</a-menu-item>
                    <a-menu-item
                      @click="Productcharacteristics"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.ProductFeatures')"
                      >产品特性</a-menu-item
                    >
                    <a-menu-item @click="Steelmeshupload" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.UpLoadWorkFile')"
                      >上传钢网</a-menu-item
                    >
                    <a-menu-item
                      @click="ProductionOrder"
                      v-if="$route.query.typee == 2 && checkPermission('MES.EngineeringModule.EngineeringInstruction.NoticeReviewInfoV2')"
                      >生产单</a-menu-item
                    >
                    <a-menu-item @click="synchronous" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.PpeSyncToMkt')"
                      >同步市场</a-menu-item
                    >
                    <a-menu-item @click="review" v-if="checkPermission('MES.EngineeringModule.EngineeringInstruction.EngGetReviewUrl')"
                      >评审</a-menu-item
                    >
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
          <div>
            <a-button
              @click="editClick1('bianji')"
              type="primary"
              style="margin-top: 8px; margin-left: 10px"
              v-if="
                !editFlg1 &&
                checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionEdit') &&
                (key == 1 || key == 4 || key == 7 || key == 3)
              "
              >编辑(E)</a-button
            >
            <a-button
              @click="editClick1('quxiao')"
              type="primary"
              style="margin-top: 8px; margin-left: 10px"
              v-else-if="
                editFlg1 &&
                checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionEdit') &&
                (key == 1 || key == 4 || key == 7 || key == 3)
              "
              >取消</a-button
            >
            <a-button
              v-if="
                checkPermission('MES.EngineeringModule.EngineeringInstruction.EngineeringInstructionSave') &&
                (key == 1 || key == 4 || key == 7 || key == 3)
              "
              @click="saveClick"
              type="primary"
              style="margin-top: 8px; margin-left: 10px"
              >保存(S)</a-button
            >
          </div>
        </div>
      </div>
    </div>
    <a-upload
      name="file"
      ref="fileRef7"
      :customRequest="httpRequest7"
      :file-list="fileList7"
      :show-upload-list="false"
      :maxCount="1"
      :before-upload="beforeUpload"
      @change="handleChange7"
    >
    </a-upload>
    <a-modal
      title=" 确认弹窗"
      :visible="dataVisible7"
      @cancel="reportHandleCancel"
      @ok="handleOk7"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <div style="overflow-y: auto">
        <p>{{ message1 }}</p>
        <p v-if="type == 1" style="color: red; font-size: 12px; font-weight: 500">点击取消重新编辑余厚,点击确认余厚更改为当前板厚1/3</p>
        <p v-if="type == 2" style="color: red; font-size: 12px; font-weight: 500">点击取消重新编辑尺寸,点击确认保存当前尺寸</p>
      </div>
      <!-- </div>  -->
    </a-modal>
    <!-- 注意事项弹窗 -->
    <a-modal
      title="信息传递"
      :visible="dataVisible4"
      @cancel="reportHandleCancel"
      @ok="handleOk41"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="800"
      :confirmLoading="confirmLoading"
      centered
    >
      <matters-needing-attention-info ref="mattersNeedingAttention" @handleOk4="handleOk4" :id="id" />
    </a-modal>
    <a-modal title=" 订单详情确认" :visible="dataVisible6" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
      <template slot="footer">
        <a-button type="primary" @click="reportHandleCancel">取消</a-button>
      </template>
      <div style="max-height: 500px; overflow-y: auto">
        <div v-for="(ite, index) in message" :key="index">
          <p>{{ ite }}</p>
        </div>
      </div>
    </a-modal>
    <!-- 注意事项 -->
    <a-modal
      title="信息传递"
      :visible="dataVisibleMatter"
      @cancel="reportHandleCancel"
      @ok="handleOkMatter"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="600"
      centered
    >
      <matters-needing-attention-info ref="mattersNeedingAttention" :editData="editData" />
    </a-modal>
    <a-modal
      title=" 确认弹窗"
      :visible="dataVisibleMode"
      @cancel="reportHandleCancel"
      @ok="handleOkMode"
      ok-text="确定"
      cancel-text="取消"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span style="font-weight: 500; font-size: 14px; color: #000000">【{{ OrderNo }}】</span>
      <span style="font-weight: 500; font-size: 14px; color: #000000">{{ showmsg }}</span>
    </a-modal>
    <a-modal
      title="产品特性"
      :visible="prodataVisible"
      @cancel="reportHandleCancel"
      @ok="prohandleOk"
      ok-text="保存"
      cancel-text="取消"
      destroyOnClose
      :maskClosable="false"
      :width="800"
      centered
    >
      <productcharacteristics-info ref="productcharacteristics" :characteristicdata="characteristicdata" />
    </a-modal>
    <a-drawer
      :title="this.$route.query.OrderNo + ' -- 客户规则 '"
      :visible="dataopen"
      @close="dataCancel"
      :maskClosable="false"
      :mask="false"
      placement="right"
      style="margin-top: 88px"
      :class="dataopen ? 'drawerclass' : ''"
    >
      <div style="overflow-y: auto" :style="{ height: heighty }">
        <div v-for="(ite, index) in ruleDescribe_" :key="index" style="display: flex">
          <p style="color: red; white-space: pre">{{ convertToLetter(index + 1) }}、</p>
          <p style="white-space: pre-wrap">{{ ite }}</p>
        </div>
      </div>
    </a-drawer>
    <a-modal :title="meslist" :visible="dataVisible2" @cancel="reportHandleCancel" destroyOnClose centered :maskClosable="false" :width="600">
      <template #footer>
        <a-button
          key="back1"
          type="primary"
          v-if="check && (checkType == 'bianji' || checkType == 'sclc' || checkType == 'sclctest' || checkType == 'review')"
          @click="continueclick"
          >继续</a-button
        >
        <a-button key="back" @click="reportHandleCancel">取消</a-button>
      </template>
      <div
        class="class"
        style="font-size: 16px; font-weight: 500"
        v-if="checkType == 'bianji' || checkType == 'sclc' || checkType == 'sclctest' || checkType == 'review'"
      >
        <p v-for="(item, index) in checkData" :key="index">
          <span v-if="item.error == '1'" style="color: red">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}</span>
          </span>
          <span v-if="item.warn == '1'" style="color: CornflowerBlue">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}</span>
          </span>
          <span v-if="item.info == '1'" style="color: black">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}</span>
          </span>
        </p>
        <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
      </div>
      <div class="class" style="font-size: 16px; font-weight: 500" v-else>
        <p v-for="(item, index) in checkData" :key="index">
          <span v-if="item.error == '1'" style="color: red">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-if="item.warn == '1'" style="color: CornflowerBlue">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-if="item.info == '1'" style="color: black">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
        </p>
        <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
      </div>
    </a-modal>
    <!--红马生产通知单-->
    <a-modal
      title="生产通知单"
      :visible="HMnoticeVisible"
      @cancel="reportHandleCancel"
      @ok="hmnoticedown"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="850"
    >
      <report-hmnoticeform :ttype="'EMS | 工程指示'" :HMnoticedata="HMnoticedata" ref="hmnotice"></report-hmnoticeform>
    </a-modal>
    <!--联合多层生产通知单-->
    <a-modal
      title="生产通知单"
      :visible="LHDCnoticeVisible"
      @cancel="reportHandleCancel"
      @ok="lhdcnoticedown"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="1400"
    >
      <report-lhdcnoticeform :ttype="'EMS | 工程指示'" :LHDCnoticedata="LHDCnoticedata" ref="lhdcnotice"></report-lhdcnoticeform>
    </a-modal>
  </a-spin>
</template>
<script>
import { setstackupitemv21 } from "@/services/project";
import db from "@/utils/db";
import ReportLhdcnoticeform from "@/pages/mkt/OrderOffer/report/ReportLhdcnoticeform";
import ReportHmnoticeform from "@/pages/mkt/OrderOffer/report/ReportHmnoticeform";
import { noticereviewinfo, productionreport } from "@/services/mkt/OrderReview.js";
import { requiredLinkConfig, boardtgitems, coretypeverdors, copperthickconversion } from "@/services/mkt/orderInfo";
import { ppebuttonCheck } from "@/services/projectMake/index.js";
import * as signalR from "@microsoft/signalr";
import { Empty } from "ant-design-vue";
import { projectBackEndJobInfo, setreviewinfo } from "@/services/projectApi";
import { getCustomerInfo } from "@/services/projectMake";
const ProductInfo = () => import("@/pages/gongcheng/projectIndicate/module/ProductInfo");
const ProOne = () => import("@/pages/gongcheng/projectIndicate/Product/ProOne");
const drlInfo = () => import("@/pages/gongcheng/projectIndicate/module/drlInfo");
const LaminationInfo = () => import("@/pages/gongcheng/projectIndicate/module/LaminationInfo");
const ProductcharacteristicsInfo = () => import("@/pages/gongcheng/projectIndicate/module/ProductcharacteristicsInfo");
const cuttingInfo = () => import("@/pages/gongcheng/projectIndicate/module/cuttingInfo");
const SpecInfo = () => import("@/pages/gongcheng/projectIndicate/module/SpecInfo");
import { checkPermission } from "@/utils/abp";
import { repierMatters, delInfo } from "@/services/projectMake";
import { coreList, ozList, gbList } from "@/services/impedance";
import { coreTypes, ppTypes } from "@/services/gongju/stackUp";
import Cookie from "js-cookie";
import {
  proOrderInfo,
  selectpars,
  qutoConfig,
  factoryList,
  mIIndicationCheck,
  productInformation,
  drillingInformation,
  lineInformation,
  solderInformation,
  shippingInformation,
  setCombinInfo,
  setMMKBAllData,
  ceshicamAutoFlow,
  camAutoFlow,
  setStackImpInformation,
  sheetTraderItems,
  boardBrandItems,
  setProFile,
  tableInformation,
  setpnlengparinformation,
  gsFileList,
  setGsFileDelete,
  graphicPreview,
  productfeatures,
  setproductfeatures,
  uploadworkfile,
  campnlfile,
  deletecampnlfile,
  proorderinfosyncmkt,
  syncmkt2Erp,
} from "@/services/projectIndicate";
import { mapState, mapMutations } from "vuex";
import { mattersNeedingAttention } from "@/services/projectApi";
import mattersNeedingAttentionInfo from "@/pages/gongcheng/projectMake/subassembly/mattersNeedingAttentionInfo";
import { getCutting, getStackImpedance } from "@/services/projectMake";
const columns8 = [
  {
    title: "文件",
    dataIndex: "name",
    ellipsis: true,
    align: "left",
    width: 160,
  },
  {
    title: "大小",
    dataIndex: "size",
    ellipsis: true,
    align: "left",
    width: 60,
  },
  {
    title: "修改时间",
    dataIndex: "lastModified",
    ellipsis: true,
    align: "center",
    width: 160,
  },
  {
    title: "操作",
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: "action" },
  },
];
const columns4 = [
  {
    title: "操作人",
    dataIndex: "inUserName",
    width: 110,
    ellipsis: true,
    align: "left",
  },
  {
    title: "创建时间",
    dataIndex: "indate",
    width: 150,
    ellipsis: true,
    align: "left",
  },
  {
    title: "备注信息",
    dataIndex: "conent",
    width: 800,
    ellipsis: true,
    align: "left",
  },
  {
    title: "图片",
    width: 200,
    scopedSlots: { customRender: "picUrl" },
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: "fileUrl" },
    className: "noCopy",
    align: "left",
    width: 100,
  },
];
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
export default {
  name: "",
  components: {
    //ProductInfo,
    ProOne,
    LaminationInfo,
    cuttingInfo,
    drlInfo,
    mattersNeedingAttentionInfo,
    SpecInfo,
    ProductcharacteristicsInfo,
    ReportLhdcnoticeform,
    ReportHmnoticeform,
  },
  inject: ["reload"],
  data() {
    return {
      Soldermaskparameters: [
        { value: "viaNotYellow", lable: "不允许过孔发黄" },
        { value: "leakCu", lable: "不允许线角漏铜" },
        { value: "filmSeal", lable: "不允许菲林印" },
        { value: "notOil", lable: "不允许补油" },
        { value: "solderBridge", lable: "必须保证阻焊桥" },
        { value: "notLnkHole", lable: "不允许油墨入孔" },
        { value: "exposedCu", lable: "不允许假性漏铜" },
        { value: "notRubBoard", lable: "不允许磨板" },
      ],
      solderRequireStr: "",
      solderRequire: [],
      showBtn: false,
      advanced: false,
      LHDCnoticedata: {},
      heighty: 600,
      copperdata: [],
      LHDCnoticeVisible: false,
      HMnoticeVisible: false,
      HMnoticedata: {},
      boardtgList: [],
      metricBritishSystem: "",
      fileList7: [],
      ManufacturerTG: [],
      requiredLinkConfigpro: {},
      signalR: null,
      meslist: "",
      checkType: "",
      jobTableLoading: false,
      characteristicdata: {},
      prodataVisible: false,
      aaaa: {},
      jobData: [],
      isCustRule: "",
      dataVisibleMode: false,
      showmsg: "",
      modaltyep: "",
      spinning: false,
      key: "1",
      ruleDescribe_: [],
      proOrderInfoDto: {},
      proOrderDrillingDto: {},
      proOrderLineDto: {},
      proOrderSolderDto: {},
      proOrderShippingDto: {},
      proOrderStackUpImpDto: [],
      pnlParameterDto: {},
      selectData: {},
      OrderNo: "",
      Order: "",
      id: "",
      factoryData: {},
      autoFocus: true,
      dataVisible2: false,
      dataVisible6: false,
      dataVisible7: false,
      checkData: [],
      check: false,
      editFlg1: false,
      drillToolDiameterDtos: [],
      formDataList: [],
      boardBrandList: [],
      sheetTraderList: [],
      disFlg: false,
      message: [],
      message1: "",
      type: "",
      deletId: "",
      messageList: [],
      allNote: "",
      dataopen: false,
      show0: false,
      show1: false,
      show2: false,
      showMore: false,
      buttonsmenu: false,
      showCG: false,
      showHDI: false,
      showMM: false,
      backEndRealName: "",
      columns8,
      columns4,
      simpleImage,
      dataSource8: [],
      TableLoading8: false,
      orderNo: "",
      businessOrderNo: "",
      dataVisible4: false,
      confirmLoading: false,
      statusStr: "",
      X: "",
      Y: "",
      jobId: "",
      isCtrlPressed: false,
      editData: [],
      recordId: "",
      Numberofpieces: "",
      dataVisibleMatter: false,
      num: "",
      level: "",
      isFolder: false,
      // prohibit:false
    };
  },
  updated() {
    if (this.$refs.dom1) {
      this.$refs.dom1.setStyle();
    }
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.handleResize, true);
    this.signalR.off("ReceiveMessage");
    if (this.signalR) {
      this.signalR
        .stop()
        .then(() => {
          console.log("连接已断开");
        })
        .catch(err => {
          console.error("连接断开失败: ", err);
        });
    }
  },

  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.handleResize, true);
    this.$nextTick(() => {
      this.chatTest();
    });
    this.setedit(this.editFlg1);
    let procust = localStorage.getItem("procust") == "true" ? true : false;
    if (Number(this.isCustRule) == 1 && procust && this.key == "1") {
      this.CustomerRulesClick();
    }
    this.getcopper();
    this.getCore();
  },
  beforeRouteLeave: function (to, from, next) {
    if (this.editstatus) {
      if (confirm("目前为编辑状态,直接跳转页面当前数据不会进行保存,确认跳转吗?")) {
        next();
        this.setedit(false);
      } else {
        next(false);
      }
    } else {
      next();
      this.setedit(false);
    }
  },
  created() {
    this.OrderNo = this.$route.query.OrderNo;
    this.isCustRule = this.$route.query.isCustRule;
    this.backEndRealName = this.$route.query.backEndRealName;
    this.StatusType = this.$route.query.StatusType;
    this.getJobInfo();
    this.getData();
    this.GetProOrderInfo();
    this.GetQutoConfig();
    this.getTG();
    this.ManufacturerandTG();
    // this.GetFactoryList()
    this.getFileData();
    //   this.$nextTick(()=>{
    //    this.handleResize()
    //  })
    if (this.$route.query.id) {
      this.id = this.$route.query.id;
    }
    boardBrandItems(this.$route.query.factory).then(res => {
      if (res.code) {
        this.boardBrandList = res.data;
      }
    });
    sheetTraderItems(this.$route.query.factory).then(res => {
      if (res.code) {
        this.sheetTraderList = res.data;
      }
    });
    this.getRequiredLink();
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("Notcommon");
      const elements1 = document.getElementsByClassName("showclass");
      if (elements.length && elements1.length > 8) {
        this.showBtn = true;
        this.advanced = false;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
      }
    });
  },
  computed: {
    ...mapState("account", ["user"]),
    ...mapState("setting", ["editstatus"]),
  },
  methods: {
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
      const elements = document.getElementsByClassName("Notcommon");
      if (elements.length) {
        this.showBtn = true;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = this.advanced ? "inline-block" : "none";
        }
      }
      this.ButtonResize(); //按钮自适应
    },
    getcopper() {
      let joinFactoryId = 0;
      if (this.$route.query.factory) {
        joinFactoryId = this.$route.query.factory;
      }
      const token = Cookie.get("Authorization");
      const data3 = JSON.parse(localStorage.getItem("copperdata"));
      if (
        data3 &&
        token &&
        data3.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a3 = 0; a3 < data3.length; a3++) {
          if (data3[a3].token == token && data3[a3].joinFactoryId == joinFactoryId) {
            this.copperdata = data3[a3].data; //本地缓存
          }
        }
      } else {
        copperthickconversion(joinFactoryId).then(res => {
          if (res.code) {
            this.copperdata = JSON.parse(res.data);
            let token = Cookie.get("Authorization");
            if (JSON.parse(res.data).length) {
              let arr = [];
              if (data3 == null) {
                arr.push({ data: JSON.parse(res.data), token, joinFactoryId: joinFactoryId });
                localStorage.setItem("copperdata", JSON.stringify(arr)); //本地缓存
              } else {
                data3.push({ data: JSON.parse(res.data), token, joinFactoryId: joinFactoryId });
                localStorage.setItem("copperdata", JSON.stringify(data3)); //本地缓存
              }
            }
          }
        });
      }
    },
    //生产单
    ProductionOrder() {
      if (this.$route.query.factory == 69) {
        this.HMnoticeform();
      } else if (this.$route.query.factory == 38) {
        this.LHDCnoticeform();
      } else {
        noticereviewinfo(this.$route.query.id, 2).then(res => {
          if (res.code) {
            this.downloadByteArrayFromString(res.data, res.message);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //红马生产通知单预览
    HMnoticeform() {
      productionreport(this.$route.query.id, 2).then(res => {
        if (res.code) {
          this.HMnoticedata = res.data;
          this.HMnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    hmnoticedown() {
      this.$refs.hmnotice.getnoticePdf();
    },
    //联合多层生产通知单预览
    LHDCnoticeform() {
      productionreport(this.$route.query.id, 2).then(res => {
        if (res.code) {
          this.LHDCnoticedata = res.data;
          this.LHDCnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    lhdcnoticedown() {
      this.$refs.lhdcnotice.getnoticePdf();
    },
    Steelmeshupload() {
      this.fileList7 = [];
      this.$refs.fileRef7.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    handleChange7({ fileList }) {
      this.fileList7 = [fileList[fileList.length - 1]];
    },
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
      if (!isFileType) {
        this.$message.error("只支持.rar或.zip格式文件");
      }
      return isFileType;
    },
    async httpRequest7(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      uploadworkfile(formData, this.$route.query.id).then(res => {
        if (res.code == 1) {
          this.$message.success("上传成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getTG() {
      let params = {};
      let factory = this.$route.query.factory;
      params.factory = factory;
      params.type = "2";
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("boardtgitemspro"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.boardtgList = data[a].data; //本地缓存
          }
        }
      } else {
        boardtgitems(params).then(res => {
          if (res.code) {
            this.boardtgList = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.boardtgList) != "{}") {
              if (data == null) {
                arr.push({ data: this.boardtgList, token, factory });
                localStorage.setItem("boardtgitemspro", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.boardtgList, token, factory });
                localStorage.setItem("boardtgitemspro", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    ManufacturerandTG() {
      let factory = this.$route.query.factory;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("coretypeverdors"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.ManufacturerTG = data[a].data; //本地缓存
          }
        }
      } else {
        coretypeverdors(factory).then(res => {
          if (res.code) {
            this.ManufacturerTG = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.ManufacturerTG) != "{}") {
              if (data == null) {
                arr.push({ data: this.ManufacturerTG, token, factory });
                localStorage.setItem("coretypeverdors", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.ManufacturerTG, token, factory });
                localStorage.setItem("coretypeverdors", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    iseval(val) {
      let newIsNullRules = val;
      if (val.indexOf("val") != -1) {
        newIsNullRules = newIsNullRules.replace(/val/g, "this.$refs.dom1.proOrderInfoDto");
      }
      return eval(newIsNullRules);
    },
    ...mapMutations("setting", ["setedit"]),
    ButtonResize() {
      let elements = document.getElementsByClassName("showclass");
      let elements1;
      if (this.advanced == true) {
        elements1 = document.getElementsByClassName("Notcommon");
      } else {
        elements1 = null;
      }
      if ((elements.length + (elements1?.length || 0)) * 95 + 520 > window.innerWidth) {
        this.buttonsmenu = true;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "none";
        }
        if (elements1 && elements1.length) {
          for (let i = 0; i < elements1.length; i++) {
            elements1[i].style.display = "none";
          }
        }
      } else {
        this.buttonsmenu = false;
        if (elements1 && elements1.length) {
          for (let i = 0; i < elements1.length; i++) {
            elements1[i].style.display = this.advanced ? "inline-block" : "none";
          }
        }
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
    },
    handleResize() {
      var rightContent = document.getElementsByClassName("rightContent")[0];
      rightContent.style.width = window.innerWidth - 370 + "px";
      var box1 = document.getElementsByClassName("box1")[0];
      box1.style.height = window.innerHeight - 90 + "px";
      this.ButtonResize();
      if (this.key == "7") {
        this.$refs.dom7.handleResize();
      }
      if (this.key == "8") {
        let listtable = document.getElementsByClassName("listtable")[0].children[0].children[0].children[0].children[0].children[0].children[1];
        let listtable1 = document.getElementsByClassName("listtable")[0];
        if (this.dataSource8.length != 0) {
          listtable.style.height = window.innerHeight - 180 + "px";
        } else {
          listtable.style.height = 0;
          listtable1.style.height = window.innerHeight - 140 + "px";
        }
      }
      if (this.key == "10") {
        if (this.allNote) {
          let note1 = document.getElementsByClassName("note1")[0];
          note1.style.height = window.innerHeight - 145 + "px";
        } else {
          let Directprompt = document.getElementsByClassName("Directprompt")[0];
          Directprompt.style.height = window.innerHeight - 140 + "px";
        }
      }
      if (this.key == "11") {
        let jobstyle = document.getElementsByClassName("jobstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
        let jobstyle1 = document.getElementsByClassName("jobstyle")[0];
        if (this.jobData.length != 0) {
          jobstyle.style.height = window.innerHeight - 180 + "px";
        } else {
          jobstyle1.style.height = window.innerHeight - 140 + "px";
        }
      }
      const drawerclass = document.getElementsByClassName("drawerclass")[0];
      if (drawerclass) {
        if (window.innerHeight < 924) {
          drawerclass.style.height = window.innerHeight - 150 + "px";
          this.heighty = window.innerHeight - 150;
        } else {
          drawerclass.style.height = "766px";
          this.heighty = 600;
        }
      }
    },
    chatTest() {
      let hubUrl = process.env.VUE_APP_API_BASE_URL + "/signalr-hubs/chat";
      let userName = this.user.userName;
      const connection = new signalR.HubConnectionBuilder()
        .withAutomaticReconnect() //断线自动重连
        .withUrl(hubUrl)
        .build();
      //启动
      this.signalR = connection;
      connection.start().catch(err => {
        //console.log("err",err);
      });
      //自动重连成功后的处理
      connection.onreconnected(connectionId => {
        //console.log(connectionId);
      });
      connection.on("ReceiveMessage", (key, message) => {
        if (key == userName) {
          //this.spinning = false
          this.GetProOrderInfo();
        }
      });
    },
    // 获取对应的订单人员
    getJobInfo() {
      this.jobTableLoading = true;
      let id = this.$route.query.id;
      projectBackEndJobInfo(id)
        .then(res => {
          if (res.code) {
            this.jobData = res.data;
            this.Numberofpieces = this.jobData.length;
            this.$nextTick(() => {
              this.handleResize();
            });
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.jobTableLoading = false;
        });
    },
    // 图片字符串裁切
    picFilter(val) {
      if (val.picUrl) {
        return val.picUrl.split(",");
      } else {
        return [];
      }
    },
    Productcharacteristics() {
      productfeatures(this.$route.query.id).then(res => {
        if (res.code) {
          this.characteristicdata = res.data;
          this.prodataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    prohandleOk() {
      let params = this.$refs.productcharacteristics.formData;
      params.id = this.$route.query.id;
      setproductfeatures(params).then(res => {
        if (res.code) {
          this.$message.success("保存成功");
        } else {
          this.$message.error(res.message);
        }
      });
      this.prodataVisible = false;
    },
    delFile(record) {
      this.dataVisibleMode = true;
      this.recordId = record.id;
      this.showmsg = "确认删除此条注意事项吗？";
      this.modaltyep = "del";
    },
    synchronous() {
      this.dataVisibleMode = true;
      this.showmsg = "确认该订单同步市场吗？";
      this.modaltyep = "tb";
    },
    // 下载附件文件
    downFile(record) {
      window.location.href = record;
    },
    editClick(record) {
      this.dataVisibleMatter = true;
      this.editData = record;
    },
    handleOkMode() {
      this.spinning = true;
      if (this.modaltyep == "del") {
        delInfo(this.recordId)
          .then(res => {
            if (res.code) {
              this.$message.success("已删除");
              this.getJobInfo();
            } else {
              this.$message.errer(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.dataVisibleMode = false;
          });
      } else if (this.modaltyep == "tb") {
        proorderinfosyncmkt(this.$route.query.id)
          .then(res => {
            if (res.code) {
              syncmkt2Erp(res.message).then(res => {
                if (res.code) {
                  this.$message.success("同步成功");
                } else {
                  this.$message.error(res.message);
                }
              });
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.dataVisibleMode = false;
          });
      }
    },
    handleOkMatter() {
      let params = this.$refs.mattersNeedingAttention.form;
      if (!params.conent && !params.picUrl && !params.fileUrl) {
        this.$message.error("请至少传递一项信息");
        return;
      }
      params.id = this.editData.id;
      params.isbefor = false;
      this.spinning = true;
      repierMatters(params)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getJobInfo();
        })
        .finally(() => {
          this.dataVisibleMatter = false;
          this.spinning = false;
        });
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.key == "e" && this.isCtrlPressed && !this.editFlg1 && (this.key == 1 || this.key == 4 || this.key == 7)) {
        this.editClick1("bianji");
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.key == "s" && this.isCtrlPressed && (this.key == 1 || this.key == 4 || this.key == 7)) {
        this.saveClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.key == "d" && this.isCtrlPressed) {
        this.webSocketLink();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    CustomerRulesClick() {
      this.ruleDescribe_ = [];
      let CustNo = this.$route.query.custNo;
      let factory = this.$route.query.factory;
      getCustomerInfo(CustNo, factory, 1, this.$route.query.businessOrderNo, this.$route.query.OrderNo).then(res => {
        if (res.code) {
          this.dataopen = true;
          this.CustomerData = res.data;
          for (let index = 0; index < this.CustomerData.length; index++) {
            const element = this.CustomerData[index].ruleDescribe_;
            this.ruleDescribe_.push(element);
          }
          this.$nextTick(() => {
            this.handleResize();
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    convertToLetter(amount) {
      const capitalNumbers = ["", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
      const unit = ["", "十", "百", "千", "万"]; // 加入万单位
      const numStr = amount.toString();
      let chineseStr = "";
      for (let i = 0; i < numStr.length; i++) {
        const digit = parseInt(numStr[i]); // 获取每一位的数字
        const unitIndex = (numStr.length - 1 - i) % 5; // 获取单位索引
        if (digit !== 0) {
          if (capitalNumbers[digit] == "一" && unit[unitIndex]) {
            chineseStr += unit[unitIndex];
          } else {
            chineseStr += capitalNumbers[digit] + unit[unitIndex];
          }
        }
      }
      return chineseStr;
    },
    dataCancel() {
      this.dataopen = false;
      localStorage.removeItem("procust");
    },
    //发送脚本
    webSocketLink() {
      let Jumppage = this.$route.query.typee;
      let proOrderId = this.id;
      //工程制作
      if (Jumppage == "2") {
        let params = JSON.stringify({
          Token: Cookie.get("Authorization"),
          Type: "make",
          Task: "make",
          Uid: proOrderId,
          Data: {},
        });
        let url = "genesiscam://?" + params;
        window.open(url, "_blank");
      }
      //工程后端
      else if (Jumppage == "1") {
        if (this.statusStr == "后端制作中" || this.statusStr == "返修中" || this.statusStr == "已问客" || this.statusStr == "EQ中") {
          let params = JSON.stringify({
            Token: Cookie.get("Authorization"),
            Type: "backend",
            Task: "backend",
            Uid: proOrderId,
            Data: {},
          });
          let url = "genesiscam://?" + params;
          window.open(url, "_blank");
        } else {
          this.$message.error("当前订单状态无法发送脚本");
        }
      }
      //QAE审核
      else if (Jumppage == "3") {
        if ((this.statusStr != "已完成" && this.statusStr != "已回传") || this.user.factoryId == 0) {
          let params = JSON.stringify({
            Token: Cookie.get("Authorization"),
            Type: "QAE",
            Task: "QAE",
            Uid: proOrderId,
            Data: {},
          });
          let url = "genesiscam://?" + params;
          window.open(url, "_blank");
        } else {
          this.$message.error("当前订单状态无法发送脚本");
        }
      } else if (Jumppage == "4" || Jumppage == "6") {
        let params = JSON.stringify({
          Token: Cookie.get("Authorization"),
          Type: "make",
          Task: "make",
          Uid: proOrderId,
          Data: {},
        });
        let url = "genesiscam://?" + params;
        window.open(url, "_blank");
      } else if (Jumppage == "5") {
        this.$message.error("合拼设计进入工程指示无法发送脚本");
      }
    },
    // 注意事项
    mattersNeedingAttentionClick() {
      this.dataVisible4 = true;
    },
    handleOk4() {
      let params = this.$refs.mattersNeedingAttention.form;
      params.id = this.id;
      if (!params.conent) {
        params.conent = "";
      }
      if (this.$route.query.typee == 1) {
        params.isbefor = false;
      } else if (this.$route.query.typee == 2) {
        params.isbefor = true;
      }
      mattersNeedingAttention(params)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
            this.$refs.mattersNeedingAttention.getJobInfo(this.id);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleOk41() {
      let params = this.$refs.mattersNeedingAttention.form;
      if ((params.conent || params.fileUrl || params.picUrl) && this.$refs.mattersNeedingAttention.payattentionto) {
        this.$message.warning("有数据未进行保存，不允许直接点确定！！");
        return;
      }
      this.getJobInfo();
      this.dataVisible4 = false;
    },
    getFileData() {
      this.TableLoading8 = true;
      gsFileList(this.OrderNo, this.$route.query.factory)
        .then(res => {
          if (res.code) {
            if (res.data != null) {
              this.dataSource8 = [res.data];
              this.addPropertyToTree1(this.dataSource8, "key");
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.TableLoading8 = false;
        });
    },
    getData() {
      let joinFactoryId = 0;
      if (this.$route.query.factory) {
        joinFactoryId = this.$route.query.factory;
      }
      const token = Cookie.get("Authorization");
      const data3 = JSON.parse(localStorage.getItem("boardTypeList"));
      // this.boardTypeList
      if (
        data3 &&
        token &&
        data3.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a3 = 0; a3 < data3.length; a3++) {
          if (data3[a3].token == token && data3[a3].joinFactoryId == joinFactoryId) {
            this.boardTypeList = data3[a3].data; //本地缓存
          }
        }
      } else {
        coreTypes(joinFactoryId, 0).then(res => {
          if (res.code) {
            this.boardTypeList = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data3 == null) {
                arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("boardTypeList", JSON.stringify(arr)); //本地缓存
              } else {
                data3.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("boardTypeList", JSON.stringify(data3)); //本地缓存
              }
            }
          }
        });
      }
      // this.ozListData
      const data = JSON.parse(localStorage.getItem("ozListData"));
      if (
        data &&
        token &&
        data.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].joinFactoryId == joinFactoryId) {
            this.ozListData = data[a].data; //本地缓存
          }
        }
      } else {
        ozList(joinFactoryId).then(res => {
          if (res.code) {
            this.ozListData = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data == null) {
                arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ozListData", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ozListData", JSON.stringify(data)); //本地缓存
              }
            }
          }
        });
      }
      // this.ppTypeList
      const data2 = JSON.parse(localStorage.getItem("ppTypeList"));
      if (
        data2 &&
        token &&
        data2.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a2 = 0; a2 < data2.length; a2++) {
          if (data2[a2].token == token && data2[a2].joinFactoryId == joinFactoryId) {
            this.ppTypeList = data2[a2].data; //本地缓存
          }
        }
      } else {
        ppTypes(joinFactoryId).then(res => {
          if (res.code) {
            this.ppTypeList = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              if (data2 == null) {
                arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ppTypeList", JSON.stringify(arr)); //本地缓存
              } else {
                data2.push({ data: res.data, token, joinFactoryId: joinFactoryId });
                localStorage.setItem("ppTypeList", JSON.stringify(data2)); //本地缓存
              }
            }
          }
        });
      }

      // this.GBListData
      const data4 = JSON.parse(localStorage.getItem("GBListData"));
      if (
        data4 &&
        token &&
        data4.filter(item => {
          return item.joinFactoryId == joinFactoryId;
        }).length
      ) {
        for (var a4 = 0; a4 < data4.length; a4++) {
          if (data4[a4].token == token && data4[a4].joinFactoryId == joinFactoryId) {
            this.GBListData = data4[a4].data; //本地缓存
          }
        }
      } else {
        gbList(joinFactoryId).then(res => {
          if (res.code) {
            this.GBListData = res.data;
            let token = Cookie.get("Authorization");
            if (res.data.length) {
              let arr = [];
              arr.push({ data: res.data, token, joinFactoryId: joinFactoryId });
              if (this.getStringSizeInBytes(JSON.stringify(localStorage)) + this.getStringSizeInBytes(JSON.stringify(arr)) < 5) {
                localStorage.setItem("GBListData", JSON.stringify(arr)); //本地缓存
              }

              // if(data4 == null){
              //   arr.push({data: res.data, token,joinFactoryId:joinFactoryId})
              //   localStorage.setItem('GBListData', JSON.stringify(arr));//本地缓存
              // }else{
              //   data4.push({data: res.data, token,joinFactoryId:joinFactoryId})
              //   localStorage.setItem('GBListData', JSON.stringify(data4));//本地缓存
              // }
            }
          }
        });
      }
    },
    async getCore() {
      let factory = 0;
      if (this.$route.query.factory) {
        factory = Number(this.$route.query.factory);
      }
      const token = Cookie.get("Authorization");
      let mode = 1;
      let coreType = "coreListDataPro";
      try {
        // 尝试从IndexedDB获取
        const cached = await db.getItem(coreType, { token, factory, mode });

        if (cached && cached.data) {
          this.coreListData = cached.data;
          return;
        }
        // 无缓存则请求接口
        const res = await coreList(factory, mode);
        if (res.code) {
          this.coreListData = res.data;

          // 存储到IndexedDB
          if (JSON.stringify(this.coreListData) !== "{}") {
            await db.setItem(coreType, {
              token,
              factory,
              mode,
              data: this.coreListData,
            });
          }
        } else {
          this.$message.error(res.message);
        }
      } catch (error) {
        console.error("IndexedDB操作失败:", error);
      }
    },
    getStringSizeInBytes(str) {
      // 使用UTF-8编码计算字符串的字节长度
      let totalBytes = new Blob([str]).size;

      // 将字节长度转换为兆字节（MB）
      let sizeInMB = totalBytes / (1024 * 1024);

      // 返回结果
      return sizeInMB;
    },
    editClick1(type) {
      if (JSON.stringify(this.selectData) == "{}") {
        this.$message.warning("下拉资源加载中，请稍后再试");
        return;
      }
      if (type == "bianji") {
        this.spinning = true;
        ppebuttonCheck(this.$route.query.id, "PpeButtonCheck")
          .then(res => {
            if (res.code) {
              if (res.data && res.data.length) {
                this.checkData = res.data;
                this.check = this.checkData.findIndex(v => v.error == "1") < 0;
                this.checkType = "bianji";
                this.dataVisible2 = true;
                this.meslist = "编辑按钮检查";
              } else {
                this.editFlg1 = !this.editFlg1;
                console.log(this.proOrderInfoDto.isPartPlatingThickGold, "18391839");
                if (this.proOrderStackUpImpDto.stackIMPOutputs == null) {
                  this.proOrderStackUpImpDto.stackIMPOutputs = [];
                }
                this.proOrderStackUpImpDto.stackIMPOutputs.splice(this.proOrderStackUpImpDto.stackIMPOutputs.length, 0, {
                  imp_LineWidth_: "",
                  imp_LineSpace_: "",
                  imp_LineCuSpace_: "",
                  imp_Value_Req_: "",
                  imp_OKLineWidth_: "",
                  imp_Value_Tol_: "",
                  imp_Type_: "",
                  imp_ControlLay_: "",
                  imp_UpLay_: "",
                  imp_DownLay_: "",
                  imp_type_PRC: "",
                });
                if (this.key == "3") {
                  this.$refs.dom4.addaction();
                  this.$refs.dom4.handleResize();
                }
                if (this.key == "7") {
                  this.$refs.dom7.getPosNeg();
                }
                this.setedit(this.editFlg1);
              }
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            if (this.key == "1") {
              this.$refs.dom1.solderColorC();
              this.$refs.dom1.fontColorC();
            }
          });
      } else {
        this.editFlg1 = !this.editFlg1;
        this.setedit(this.editFlg1);
        this.GetProOrderInfo();
        if (this.key == "4") {
          this.$refs.domcutting.cancle();
        }
        if (this.key == "3") {
          this.$refs.dom4.delaction();
        }
      }
    },
    continueclick() {
      if (this.checkType == "bianji") {
        this.editFlg1 = !this.editFlg1;
        this.setedit(this.editFlg1);
      }
      if (this.checkType == "sclc" || this.checkType == "sclctest") {
        if (this.id) {
          this.spinning = true;
          let Buildtheprocess;
          if (this.checkType == "sclctest") {
            Buildtheprocess = ceshicamAutoFlow;
          } else {
            Buildtheprocess = camAutoFlow;
          }
          setMMKBAllData(this.id).then(res => {
            if (res.code) {
              Buildtheprocess(this.id).then(res => {
                if (res.code) {
                  if (this.checkType == "sclc") {
                    //生成流程后调取数据导入接口 固定流程入参层压工序ID(105) 生成叠层实体表
                    //2025/5/26 更改为传参416  CTLType_
                    setstackupitemv21(this.OrderNo, this.$route.query.factory)
                      .then(ite => {})
                      .finally(() => {
                        this.spinning = false;
                        this.$router.push({
                          path: "/gongcheng/technologicalProcess",
                          query: { pdctno: this.OrderNo, factoryId: this.$route.query.factory },
                        });
                      });
                  } else {
                    this.spinning = false;
                    this.$router.push({
                      path: "/gongcheng/technologicalProcess",
                      query: { pdctno: this.OrderNo, factoryId: this.$route.query.factory },
                    });
                  }
                } else {
                  this.spinning = false;
                  this.$message.error(res.message);
                }
              });
            } else {
              this.$message.error(res.message);
              this.spinning = false;
            }
          });
        }
      }
      this.dataVisible2 = false;
      if (this.checkType == "review") {
        this.dataVisible7 = true;
        this.type = "review";
        if (!this.proOrderInfoDto.reviewNo) {
          this.message1 = this.proOrderInfoDto.orderNo + "确认该订单发起评审吗?";
        } else {
          this.message1 = `该订单已发起过${this.proOrderInfoDto.reviewNo}次评审 请确认是否要再次发起评审`;
        }
      }
    },
    returnClick() {
      this.$nextTick(function () {
        if (this.backEndRealName) {
          this.$router.push({ path: "/gongcheng/projectBackend" });
        } else {
          this.$router.push({ path: "/gongcheng/projectMake" });
        }
      });
    },
    getRequiredLink() {
      let factory = this.$route.query.factory;
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("requiredLinkConfigpro"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.requiredLinkConfigpro = data[a].data; //本地缓存
          }
        }
      } else {
        requiredLinkConfig(factory, 3).then(res => {
          if (res.code) {
            this.requiredLinkConfigpro = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.requiredLinkConfigpro) != "{}") {
              if (data == null) {
                arr.push({ data: this.requiredLinkConfigpro, token, factory });
                localStorage.setItem("requiredLinkConfigpro", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.requiredLinkConfigpro, token, factory });
                localStorage.setItem("requiredLinkConfigpro", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    islink() {
      this.messageList = [];
      var x = /^\+?[1-9][0-9]*$/;
      var y = /^\+?[0-9][0-9]*$/;
      var r = /^0{1}(\.\d*)|(^[1-9][0-9]*)+(\.\d*)?$/;
      var z = /^0\.\d*[1-9]\d*$/;
      var i = /^99.99999999999999$|^(\d|[0-9]\d)(\.\d{1,10})*$/;
      let val = this.$refs.dom1.proOrderInfoDto;
      if (!val.plateType) {
        this.message.push("请选择板类型");
        this.messageList.push("板类型");
      }
      if (!val.boardThickness || !r.test(val.boardThickness)) {
        this.message.push("成品板厚请选择或者输入正数");
        this.messageList.push("成品板厚mm");
      }
      if ((val.boardLayers <= 2 && val.plateType == "hdi") || (val.boardLayers <= 2 && val.plateType == "mm")) {
        this.message.push("板类型与层数不匹配请重新选择");
        this.messageList.push("层数");
        this.messageList.push("板类型");
      }
      if (!val.boardLayers || !y.test(val.boardLayers)) {
        this.message.push("层数请选择或输入正整数");
        this.messageList.push("层数");
      }
      if (val.gbNum && !y.test(val.gbNum)) {
        this.message.push("光板数请输入正整数");
        this.messageList.push("光板数");
      }
      if (val.boardLayers == 1 || (val.boardLayers == 2 && val.surfaceFinish != "lightplate")) {
        if (!val.copperThickness || !r.test(val.copperThickness)) {
          this.message.push("成品铜厚请选择或者输入正数");
          this.messageList.push("成品铜厚oz");
        }
      }
      if (val.boardLayers > 2 && val.surfaceFinish != "lightplate") {
        if (!val.innerCopperThickness || !r.test(val.innerCopperThickness) || !val.copperThickness || !r.test(val.copperThickness)) {
          this.message.push("成品铜厚请选择或者输入正数");
          this.messageList.push("成品铜厚oz");
        }
      }
      if (val.boardLayers != 0) {
        if (!val.surfaceFinish) {
          this.message.push("请选择表面处理");
          this.messageList.push("表面处理");
        }
      }
      if (val.boardLayers != 0) {
        if (!val.solderColor || !val.solderColorBottom) {
          this.message.push("请选择阻焊颜色");
          this.messageList.push("阻焊颜色");
        }
      }
      if (
        val.surfaceFinish == "nickelpalladiumgold" ||
        val.surfaceFinish == "goldplatedfingerandimmersiongold" ||
        val.surfaceFinish == "goldplatingandimmersiongold" ||
        val.surfaceFinish == "immersiongold" ||
        val.surfaceFinish == "electrogold" ||
        val.surfaceFinish == "cjandjbdj" ||
        val.surfaceFinish == "immersiongoldandcarbonink" ||
        val.surfaceFinish == "immersiongoldandosp" ||
        val.surfaceFinish == "immersiongoldHaslwithfree"
      ) {
        if (!val.surfaceFinishJsonDto.platedArea || !r.test(val.surfaceFinishJsonDto.platedArea)) {
          this.message.push("面积请输入正数");
          this.messageList.push("表面处理");
        }
      }
      if (val.boardLayers != 0) {
        if (!val.fontColor || !val.fontColorBottom) {
          this.message.push("请选择字符颜色");
          this.messageList.push("字符颜色");
        }
      }
      if (!val.pinBanType1 || !x.test(val.pinBanType1) || !val.pinBanType2 || !x.test(val.pinBanType2)) {
        this.message.push("拼版方式请输入正整数");
        this.messageList.push("拼版方式");
      }
      if (!val.boardHeight || !r.test(val.boardHeight)) {
        this.message.push("单元长请输入正数");
        this.messageList.push("单元长mm");
      }
      if (!val.boardWidth || !r.test(val.boardWidth)) {
        this.message.push("单元宽请输入正数");
        this.messageList.push("单元宽mm");
      }
      if (!val.boardHeightSet || !r.test(val.boardHeightSet)) {
        this.message.push("成品长请输入正数");
        this.messageList.push("成品长mm");
      }
      if (!val.boardWidthSet || !r.test(val.boardWidthSet)) {
        this.message.push("成品宽请输入正数");
        this.messageList.push("成品宽mm");
      }
      if (!val.formingType) {
        this.message.push("请选择成型方式");
        this.messageList.push("成型方式");
      }
      if (!val.formingTol && this.$route.query.factory == 12) {
        this.message.push("请选择成型公差");
        this.messageList.push("成型公差");
      }
      if (!val.boardType) {
        this.message.push("请选择出货单位");
        this.messageList.push("出货单位");
      }
      if (val.boardLayers != 0) {
        if (!val.flyingProbe) {
          this.message.push("请选择测试方式");
          this.messageList.push("测试方式");
        }
      }
      if (!val.ipcLevel) {
        this.message.push("请选择检验标准");
        this.messageList.push("检验标准");
      }
      if (
        val.solderCover == "plugoil" ||
        val.solderCover == "openwindowplusplugoil" ||
        val.solderCover == "bgaplugoil" ||
        val.solderCover == "aluminiumplugoil"
      ) {
        if (!val.plugOilTool) {
          this.message.push("请选择塞孔工具");
          this.messageList.push("塞孔工具");
        }
      }
      if (
        val.solderCover == "plugoil" ||
        val.solderCover == "openwindowplusplugoil" ||
        val.solderCover == "bgaplugoil" ||
        val.solderCover == "aluminiumplugoil"
      ) {
        // if (!val.plugOilSide) {
        //   this.message.push("请选择塞孔面次");
        //   this.messageList.push("塞孔面次");
        // }
      }
      if (val.solderThickness && !r.test(val.solderThickness)) {
        this.message.push("基材油墨厚度请输入正数");
        this.messageList.push("基材油墨厚度um");
      }
      if (val.footLineInkThickness && !r.test(val.footLineInkThickness)) {
        this.message.push("线角油墨厚度请输入正数");
        this.messageList.push("线角油墨厚度um");
      }
      if (val.cuInkThickness && !r.test(val.cuInkThickness)) {
        this.message.push("铜面油墨厚度请输入正数");
        this.messageList.push("铜面油墨厚度um");
      }
      if (val.sheetUtilization && !i.test(val.sheetUtilization)) {
        this.message.push("板材利用率请输入不大于100的正数");
        this.messageList.push("板材利用率(%)");
      }
      if (val.vcutSurplusThickness && !r.test(val.vcutSurplusThickness)) {
        this.message.push("余厚请输入正数");
        this.messageList.push("余厚mm");
      }
      if (val.minSolderBridge && !z.test(val.minSolderBridge)) {
        this.message.push("最小阻焊桥请输入大于0且小于1的小数");
        this.messageList.push("最小阻焊桥(mm)");
      }
      if (val.pinBanNum && !x.test(val.pinBanNum)) {
        this.message.push("合拼款数请输入正整数");
        this.messageList.push("合拼款数");
      }
      if (val.vCutKnifeNum && !x.test(val.vCutKnifeNum)) {
        this.message.push("V-cut刀数请输入正整数");
        this.messageList.push("V-CUT刀数");
      }
      if (val.goldFingerThickness && !r.test(val.goldFingerThickness)) {
        this.message.push('金手指金厚U"请输入正数');
        this.messageList.push('金手指金厚U"');
      }
      // if(val.goldfingerNickelThickness && !r.test(val.goldfingerNickelThickness)){
      // this.message.push('金手指镍厚um请输入正数')
      // this.messageList.push('金手指镍厚um')
      // }
      if (val.goldfingerBevelAngle && !x.test(val.goldfingerBevelAngle)) {
        this.message.push("金手指斜边角度请输入正整数");
        this.messageList.push("金手指斜边角度");
      }
      if (val.goldfingerBevelDepth && !r.test(val.goldfingerBevelDepth)) {
        this.message.push("金手指斜边深度请输入正数");
        this.messageList.push("金手指斜边深度");
      }
      if (val.goldfingerBevelSurplus && !r.test(val.goldfingerBevelSurplus)) {
        this.message.push("金手指斜边余厚请输入正数");
        this.messageList.push("金手指斜边余厚");
      }
    },
    saveClick() {
      this.messageList = [];
      var x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      var y = /^\+?[0-9][0-9]*$/;
      var r = /^0{1}(\.\d*)|(^[1-9][0-9]*)+(\.\d*)?$/;
      var z = /^0\.\d*[1-9]\d*$/;
      var i = /^99.99999999999999$|^(\d|[0-9]\d)(\.\d{1,10})*$/;
      if (!this.editFlg1) {
        this.$message.warning("非编辑状态不可保存");
        return;
      }
      if (this.key == "1") {
        let val = this.$refs.dom1.proOrderInfoDto;
        let solderRequire = this.$refs.dom1.solderRequire;
        val.solderRequireJsonDto = {
          viaNotYellow: false,
          leakCu: false,
          filmSeal: false,
          notOil: false,
          solderBridge: false,
          notLnkHole: false,
          exposedCu: false,
          notRubBoard: false,
        };
        val.orderNo = this.OrderNo;
        val.boardHeightSet = Number(val.boardHeightSet);
        val.boardHeight = Number(val.boardHeight);
        val.boardWidthSet = Number(val.boardWidthSet);
        val.boardWidth = Number(val.boardWidth);
        if (!val.pinBanNum) {
          val.pinBanNum = 1;
        }
        if (val.ulType) {
          val.ulType = val.ulType.toString();
        }
        if (val.markType) {
          val.markType = val.markType.toString();
        }
        if (val.needReportList) {
          val.needReportList = val.needReportList.toString();
        }
        if (!val.pinBanNum) {
          val.pinBanNum = null;
        }
        if (!val.goldfingerNickelThickness) {
          val.goldfingerNickelThickness = null;
        }
        if (!val.packagingQuantity) {
          val.packagingQuantity = null;
        }
        if (!val.spareQuantity) {
          val.spareQuantity = null;
        }
        if (!val.minHoleCopper) {
          val.minHoleCopper = null;
        }
        if (!val.avgHoleCopper) {
          val.avgHoleCopper = null;
        }
        if (!val.gbNum) {
          val.gbNum = null;
        }
        if (!val.vcutSurplusThickness) {
          val.vcutSurplusThickness = null;
        }
        if (val.pinBanType1 && val.pinBanType2) {
          val.pinBanType = val.pinBanType1 + "x" + val.pinBanType2;
        }
        val.id = this.id;
        if (val.pinBanType1 > 1 || val.pinBanType2 > 1) {
          if (val.boardType == "pcs") {
            this.message.push("拼版方式大于1x1,出货单位请选择set");
            this.messageList.push("出货单位");
          }
        }
        if (val.boardType == "pcs") {
          if (val.boardHeightSet != val.boardHeight || val.boardWidthSet != val.boardWidth) {
            this.message.push("交货单位为pcs,请核实成品尺寸");
            this.messageList.push("成品长mm");
            this.messageList.push("成品宽mm");
          }
        }
        if (val.boardType == "set") {
          if (val.boardHeightSet < val.boardHeight || val.boardWidthSet < val.boardWidth) {
            this.message.push("交货单位为set,请核实成品尺寸");
            this.messageList.push("成品长mm");
            this.messageList.push("成品宽mm");
          }
        }
        if (val.surfaceFinish == "tinprecipitation") {
          val.surfaceFinishJsonDto.newTinThickness = null;
        } else {
          val.surfaceFinishJsonDto.newTinThickness2 = null;
        }
        if (val.boardLayers <= 2) {
          if (val.plateType == "mm") {
            this.$message.error("订单类型为盲埋,层数必须大于2层");
            this.messageList.push("板类型");
            this.messageList.push("层数");
            return;
          }
          if (val.plateType == "hdi") {
            this.$message.error("订单类型为HDI,层数必须大于2层");
            this.messageList.push("板类型");
            this.messageList.push("层数");
            return;
          }
        }
        if (val.isPartPlatingThickGold == true) {
          val.isPartPlatingThickGold = 1;
        } else {
          val.isPartPlatingThickGold = 0;
        }
        // if (val.isCopperThickConversion) {
        //   val.copperThickness = this.copperdata.filter(item => item.CopperThickness2 == val.copperThickness2)[0]?.CopperThickness;
        //   val.innerCopperThickness = this.copperdata.filter(item => item.CopperThickness2 == val.innerCopperThickness2)[0]?.CopperThickness;
        // }
        if (!val.innerCopperThickness2) {
          val.innerCopperThickness2 = null;
        } else {
          val.innerCopperThickness2 = Number(val.innerCopperThickness2);
        }
        if (!val.copperThickness2) {
          val.copperThickness2 = null;
        } else {
          val.copperThickness2 = Number(val.copperThickness2);
        }

        if (!val.innerCopperThickness) {
          val.innerCopperThickness = null;
        } else {
          val.innerCopperThickness = Number(val.innerCopperThickness);
        }
        if (!val.copperThickness) {
          val.copperThickness = null;
        } else {
          val.copperThickness = Number(val.copperThickness);
        }
        for (let key in this.requiredLinkConfigpro) {
          let require = this.requiredLinkConfigpro[key];
          let surf = key.includes("surfaceFinishJsonDto");
          let r = require.linkRules ? eval(require.linkRules) : "";
          if (val.boardLayers == 0) {
            val.boardLayers = val.boardLayers.toString();
          }
          let val1 = surf ? val[key.split(".")[0]][key.split(".")[1]] : val[key];
          if ((require && this.iseval(require.isNullRules) && !val1) || (require.linkRules && val1 && !r.test(val1))) {
            this.message.push(require.message);
            this.messageList.push(require.lable);
          }
        }
        if (this.message.length) {
          this.dataVisible6 = true;
          return;
        }
        if (val.boardHeightSet * val.boardWidthSet - val.boardHeight * val.boardWidth * val.su < 0) {
          this.dataVisible7 = true;
          this.type = "2";
          this.message1 = "成品尺寸面积小于单元面积，请确认";
          return;
        }
        delete val.note;
        delete val.pcbFilePath;
        delete val.pcbFileName;
        delete val.specialRemarks;
        Object.keys(val.solderRequireJsonDto).forEach(key => {
          if (solderRequire.indexOf(key) != -1) {
            val.solderRequireJsonDto[key] = true;
          }
        });
        productInformation(val).then(res => {
          if (res.code) {
            this.GetProOrderInfo();
            this.$message.success("保存成功");
            this.editFlg1 = false;
            this.setedit(this.editFlg1);
          } else {
            this.$message.error(res.message);
          }
        });
      }
      // 特殊信息保存
      if (this.key == "7") {
        let obj = {};
        obj.orderNo = this.OrderNo;
        obj.id = this.id;
        let params = this.$refs.dom7.proOrderLineDto;
        params = JSON.parse(JSON.stringify(params).replace(/lineParameterDtos/g, "lineParameterInputs"));
        let arr = params.lineParameterInputs;
        if (arr) {
          arr.forEach(item => {
            let val = [
              "bC_",
              "minLineW_",
              "minLineSpace_",
              "hole2Copper_",
              "stretchX",
              "stretchY",
              "minRing",
              "copperRatio_",
              "bgaSize",
              "minPadDiameter",
            ];
            val.forEach(v => {
              if (item[v] || item[v] === 0) {
                item[v] = Number(item[v]);
              } else {
                item[v] = null;
              }
            });
          });
        }
        params.lineParameterInputs = arr;
        params.innerCopperThickness = Number(params.innerCopperThickness);
        params.copperThickness = Number(params.copperThickness);
        params.invoice = Number(params.invoice);
        if (params.inBaseCopper) {
          params.inBaseCopper = Number(params.inBaseCopper);
        }
        if (params.outBaseCopper) {
          params.outBaseCopper = Number(params.outBaseCopper);
        }

        let params1 = this.$refs.dom7.proOrderDrillingDto;
        delete params1.drillToolDiameterDtos;
        params1 = JSON.parse(JSON.stringify(params1).replace(/drillParameterDtos/g, "drillParameterInputs"));
        obj.lineParameterInputs = params.lineParameterInputs;
        obj.drillParameterInputs = params1.drillParameterInputs;
        if (params1.drillParameterInputs) {
          for (let index = 0; index < params1.drillParameterInputs.length; index++) {
            const element = params1.drillParameterInputs[index];
            if (!element.drillName_) {
              this.$message.error("请填写第" + (index + 1) + "行的钻孔名");
              return;
            }
            if (element.stretchX) {
              element.stretchX = Number(element.stretchX);
            } else {
              element.stretchX = null;
            }
            if (element.stretchY) {
              element.stretchY = Number(element.stretchY);
            } else {
              element.stretchY = null;
            }
            if (!element.minCuHole_) {
              element.minCuHole_ = null;
            }
            if (!element.minAverageHole_) {
              element.minAverageHole_ = null;
            }
          }
        }
        let params2 = this.$refs.dom7.proOrderLineDto.lineParameterDtos;
        if (params2) {
          for (var b = 0; b < params2.length; b++) {
            this.aaaa = params2[b];
            if (params2[b].minLineW_ && !x.test(params2[b].minLineW_)) {
              this.message.push("线宽请输入非负数");
            }
            if (params2[b].minLineSpace_ && !x.test(params2[b].minLineSpace_)) {
              this.message.push("间距请输入非负数");
            }
            if (params2[b].hole2Copper_ && !x.test(params2[b].hole2Copper_)) {
              this.message.push("孔到线请输入非负数");
            }
            if (this.$route.query.factory == 58 || this.$route.query.factory == 59) {
              if (!x.test(params2[b].bC_)) {
                this.message.push("补偿请输入非负数");
              }
            } else {
              if (params2[b].bC_ && !x.test(params2[b].bC_)) {
                this.message.push("补偿请输入非负数");
              }
            }

            if (
              (params2[b].copperRatio_ && !x.test(params2[b].copperRatio_)) ||
              Number(params2[b].copperRatio_) < 0 ||
              Number(params2[b].copperRatio_) > 100
            ) {
              this.message.push("残铜率请输入小于100的非负数");
            }
            if (params2[b].minRing && !x.test(params2[b].minRing) && params2[b].minRing != "0") {
              this.message.push("最小焊环请输入0或正数");
            }
            if (params2[b].minPadDiameter && !x.test(params2[b].minPadDiameter)) {
              this.message.push("最小ic请输入非负数");
            }
            if (params2[b].bgaSize && !x.test(params2[b].bgaSize)) {
              this.message.push("最小bga请输入非负数");
            }
            if (params2[b].specialCuThickness && !r.test(params2[b].specialCuThickness)) {
              this.message.push("特殊铜厚请输入正数");
            }
            if (params2[b].stretchX && !r.test(params2[b].stretchX) && params2[b].stretchX != "0") {
              this.message.push("拉伸X请输入0或正数");
            }
            if (params2[b].stretchY && !r.test(params2[b].stretchY) && params2[b].stretchY != "0") {
              this.message.push("拉伸Y请输入0或正数");
            }
            // if (!r.test(params2[b].stretchX) && this.$route.query.factory==12 && this.proOrderInfoDto.boardLayers>2 && params2[b].layName_ !='L1' && params2[b].layName_ !='L'+this.proOrderInfoDto.boardLayers) {
            //   this.message.push('内层拉伸X请输入正数')
            // }
            // if (!r.test(params2[b].stretchY) && this.$route.query.factory==12 && this.proOrderInfoDto.boardLayers>2 && params2[b].layName_ !='L1' && params2[b].layName_ !='L'+this.proOrderInfoDto.boardLayers) {
            //   this.message.push('内层拉伸Y请输入正数')
            // }
          }
        }
        this.message = this.message.filter((item, index, array) => {
          return array.indexOf(item) === index;
        });
        if (this.message.length) {
          this.dataVisible6 = true;
          return;
        }
        tableInformation(obj).then(res => {
          if (res.code) {
            this.GetProOrderInfo();
            this.editFlg1 = false;
            this.setedit(this.editFlg1);
          } else {
            this.$message.error(res.message);
          }
        });
      }
      // 叠层阻抗信息保存
      if (this.key == "3") {
        this.$refs.dom4.imphandleOk();
        this.$refs.dom4.delaction();
        this.editFlg1 = false;
        this.setedit(this.editFlg1);
      }
      //拼版设计保存
      if (this.key == "4") {
        let params = {};
        params.orderNo = this.$route.query.OrderNo;
        params.id = this.$route.query.id;
        params.makePnlEngParameterInputs = this.$refs.domcutting.dataSource;
        for (let index = 0; index < params.makePnlEngParameterInputs.length; index++) {
          const element = params.makePnlEngParameterInputs[index];
          if (element.itemPar_ && !r.test(element.itemPar_) && (element.item_ == "A板PNL SET数" || element.item_ == "A板PNL 单元数")) {
            this.$message.warn(element.item_ + "请输入正确格式");
            return;
          }
        }
        setpnlengparinformation(params).then(res => {
          if (res.code) {
            this.$message.success("保存成功");
            this.GetProOrderInfo();
          } else {
            this.$message.error(res.message);
          }
        });
        this.editFlg1 = false;
        this.setedit(this.editFlg1);
      }
    },
    LayersChange() {
      let val = this.$refs.dom1.proOrderInfoDto;
      if (val.boardLayers <= 2) {
        this.disFlg = true;
        // this.$refs.dom3.proOrderLineDto.inBaseCopper = null
        this.$refs.dom3.proOrderLineDto.innerCopperThickness = this.$refs.dom3.proOrderLineDto.copperThickness;
      } else {
        this.disFlg = false;
      }
    },
    //拼版点击确认弹窗
    handleOk7() {
      if (this.type == "1") {
        this.message1 = "";
        this.dataVisible7 = false;
        let val = this.$refs.dom1.proOrderInfoDto;
        val.vcutSurplusThickness = Number((val.boardThickness * 1) / 3).toFixed(2);
      }
      if (this.type == "2") {
        this.dataVisible7 = false;
        this.message1 = "";
        let val = this.$refs.dom1.proOrderInfoDto;
        productInformation(val).then(res => {
          if (res.code) {
            this.GetProOrderInfo();
            this.$message.success("保存成功");
            this.editFlg1 = false;
            this.setedit(this.editFlg1);
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.type == "8") {
        this.dataVisible7 = false;
        this.message1 = "";
        // setGsFileDelete(record.id).then(res => {
        //   if (res.code) {
        //     this.$message.success('删除成功')
        //     this.getFileData()
        //   } else {
        //     this.$message.error(res.message)
        //   }
        // })
        deletecampnlfile(this.level, this.$route.query.factory, this.isFolder, this.$route.query.OrderNo).then(res => {
          if (res.code) {
            this.$message.success("删除成功");
            this.getFileData();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.type == "review") {
        let num = 1;
        if (this.proOrderInfoDto.reviewNo) {
          num = Number(this.proOrderInfoDto.reviewNo + 1);
        }
        setreviewinfo(this.$route.query.factory, this.orderNo, this.proOrderInfoDto.businessOrderNo, 1, num).then(res => {
          if (res.code) {
            this.$router.push({
              path: "/gongju/Judgingcontent",
              query: {
                OrderNo: this.orderNo,
                businessOrderNo: this.proOrderInfoDto.businessOrderNo,
                joinFactoryId: this.proOrderInfoDto.joinFactoryId,
                id: this.$route.query.id,
                reviewSource: 1, //工程进入评审=新增  工程1 市场0
                review_Type: "add",
                page: "0",
                reviewNo: num,
              },
            });
          }
        });
      }
    },
    setEstimate5(value, list) {
      if (!this.proOrderInfoDto.vcutSurplusThickness) {
        this.proOrderInfoDto.vcutSurplusThickness = value;
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    // 获取参数
    GetProOrderInfo() {
      let parmas = this.$route.query.id;
      if (parmas) {
        this.spinning = true;
        proOrderInfo(parmas)
          .then(res => {
            if (res.code) {
              this.proOrderInfoDto = res.data.proOrderInfoDto;
              this.metricBritishSystem = res.data.proOrderInfoDto.metricBritishSystem;
              this.allNote = res.data.proOrderInfoDto.autoTaskMsg;
              this.solderRequire = [];
              this.solderRequireStr = "";
              if (this.proOrderInfoDto.solderRequireJsonDto && JSON.stringify(this.proOrderInfoDto.solderRequireJsonDto) != "{}") {
                Object.keys(this.proOrderInfoDto.solderRequireJsonDto).forEach(key => {
                  if (this.proOrderInfoDto.solderRequireJsonDto[key]) {
                    this.solderRequire.push(key);
                  }
                });
                this.Soldermaskparameters.forEach(item => {
                  if (this.solderRequire.includes(item.value)) {
                    this.solderRequireStr += item.lable + " ";
                  }
                });
              }
              if (this.proOrderInfoDto.boardLayers == 0) {
                this.show0 = true;
              } else {
                this.show0 = false;
              }
              if (this.proOrderInfoDto.boardLayers == 1) {
                this.show1 = true;
              } else {
                this.show1 = false;
              }
              if (this.proOrderInfoDto.boardLayers == 2) {
                this.show2 = true;
              } else {
                this.show2 = false;
              }
              if (this.proOrderInfoDto.boardLayers >= 3) {
                this.showMore = true;
              } else {
                this.showMore = false;
              }
              if (this.proOrderInfoDto.plateType == "cg") {
                this.showCG = true;
              } else {
                this.showCG = false;
              }
              if (this.proOrderInfoDto.plateType == "hdi") {
                this.showHDI = true;
              } else {
                this.showHDI = false;
              }
              if (this.proOrderInfoDto.plateType == "mm") {
                this.showMM = true;
              } else {
                this.showMM = false;
              }
              this.num = this.proOrderInfoDto.num;
              this.orderNo = this.proOrderInfoDto.orderNo;
              this.businessOrderNo = this.proOrderInfoDto.businessOrderNo;
              this.id = this.proOrderInfoDto.id;
              this.jobId = this.proOrderInfoDto.jobId;
              this.$refs.specInfoRef.update(this.jobId);
              this.statusStr = this.proOrderInfoDto.statusStr;
              if (this.proOrderInfoDto.sheetTrader && this.$refs.dom1) {
                this.$refs.dom1.changeSheet(this.proOrderInfoDto.sheetTrader);
              }
              if (this.proOrderInfoDto.isPartPlatingThickGold == 1) {
                this.proOrderInfoDto.isPartPlatingThickGold = true;
              } else {
                this.proOrderInfoDto.isPartPlatingThickGold = false;
              }
              console.log(this.proOrderInfoDto.isPartPlatingThickGold, "获取时");
              if (this.proOrderInfoDto.needReportList) {
                this.proOrderInfoDto.needReportList = this.proOrderInfoDto.needReportList.split(",");
              } else {
                this.proOrderInfoDto.needReportList = [];
              }
              this.proOrderInfoDto.boardLayers = this.proOrderInfoDto.boardLayers.toString();
              this.proOrderInfoDto.orderNo = this.$route.query.OrderNo;
              if (this.proOrderInfoDto.ipcLevel != null && this.proOrderInfoDto.ipcLevel != "") {
                this.proOrderInfoDto.ipcLevel = this.proOrderInfoDto.ipcLevel.toString();
              }
              if (this.proOrderInfoDto.ulType != null && this.proOrderInfoDto.ulType != "") {
                this.proOrderInfoDto.ulType = this.proOrderInfoDto.ulType.split(",");
              } else {
                this.proOrderInfoDto.ulType = [];
              }
              if (this.proOrderInfoDto.markType != null && this.proOrderInfoDto.markType != "") {
                this.proOrderInfoDto.markType = this.proOrderInfoDto.markType.split(",");
              } else {
                this.proOrderInfoDto.markType = [];
              }
              if (this.proOrderInfoDto.pinBanType) {
                let arr = this.proOrderInfoDto.pinBanType.toLowerCase().split("x");
                this.$set(this.proOrderInfoDto, "pinBanType1", arr[0]);
                this.$set(this.proOrderInfoDto, "pinBanType2", arr[1]);
              } else {
                this.$set(this.proOrderInfoDto, "pinBanType1", null);
                this.$set(this.proOrderInfoDto, "pinBanType2", null);
              }
              this.proOrderDrillingDto = res.data.proOrderDrillingDto;
              this.proOrderDrillingDto.orderNo = this.$route.query.OrderNo;
              this.drillToolDiameterDtos = res.data.proOrderDrillingDto.drillToolDiameterDtos;
              if (this.drillToolDiameterDtos) {
                this.proOrderDrillingDto.drillToolDiameterDtos = this.drillToolDiameterDtos.filter(
                  v => v.guid4Drill_ == this.proOrderDrillingDto.drillParameterDtos[0].id
                );
              }
              // 线路
              this.proOrderLineDto = res.data.proOrderLineDto;
              this.proOrderStackUpImpDto = res.data.proOrderStackUpImpDto;
              if (this.proOrderStackUpImpDto.stackIMPOutputs == null) {
                this.proOrderStackUpImpDto.stackIMPOutputs = [];
              }
              if (this.key == "3") {
                this.$nextTick(() => {
                  this.$refs.dom4.handleResize();
                });
              }
              this.pnlParameterDto = res.data.pnlParameterDto;
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    // 获取工厂ID
    GetFactoryList() {
      factoryList().then(res => {
        if (res.code) {
          this.factoryData = res.data;
        } else {
          this.$message, error(res.message);
        }
      });
    },
    // 获取下拉选择
    GetQutoConfig() {
      let factory = 0;
      if (this.$route.query.factory || this.$route.query.factory == "0") {
        factory = this.$route.query.factory;
      }
      const token = Cookie.get("Authorization");
      const data = JSON.parse(localStorage.getItem("EngSelectPar"));
      if (
        data &&
        data.filter(item => {
          return item.factory == factory;
        }).length &&
        token
      ) {
        for (var a = 0; a < data.length; a++) {
          if (data[a].token == token && data[a].factory == factory) {
            this.selectData = data[a].data; //本地缓存
          }
        }
      } else {
        selectpars(2, factory).then(res => {
          if (res.code) {
            this.selectData = res.data;
            let token = Cookie.get("Authorization");
            let arr = [];
            if (JSON.stringify(this.selectData) != "{}") {
              if (data == null) {
                arr.push({ data: this.selectData, token, factory });
                localStorage.setItem("EngSelectPar", JSON.stringify(arr)); //本地缓存
              } else {
                data.push({ data: this.selectData, token, factory });
                localStorage.setItem("EngSelectPar", JSON.stringify(data)); //本地缓存
              }
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    datachuli(dataObject) {
      const transformedObject = {};
      // 遍历dataObject的每个属性
      for (const key in dataObject) {
        if (dataObject[key]) {
          transformedObject[key] = dataObject[key].reduce((obj, item) => {
            obj[item.valueMember] = item.text;
            return obj;
          }, {});
        }
      }
      return transformedObject;
    },
    liClick(val) {
      if (this.key == "1") {
        if (this.$refs.dom1.editFlg1) {
          this.$message.warning("编辑状态不可执行此操作");
          return;
        }
      }
      if (this.key == "7") {
        if (this.$refs.dom7.editFlg1) {
          this.$message.warning("编辑状态不可执行此操作");
          return;
        }
      }
      if (this.key == "3") {
        if (this.$refs.dom4.editFlg1) {
          this.$message.warning("编辑状态不可执行此操作");
          return;
        }
      }
      this.key = val;
      if (this.key == "7") {
        this.$refs.dom7.handleResize();
      }
      if (this.key == "3") {
        this.$refs.dom4.handleResize();
      }
      if (this.key == "4") {
        this.$refs.domcutting.handleResize();
        this.$refs.domcutting.cancle();
      }
      if (this.key != "1") {
        this.dataopen = false;
      } else {
        let procust = localStorage.getItem("procust") == "true" ? true : false;
        if (Number(this.isCustRule) == 1 && procust && this.key == "1") {
          this.dataopen = true;
        }
        this.$refs.dom1.handleResize();
      }
      if (this.key == "9") {
        this.$nextTick(() => {
          this.$refs.specInfoRef.update(this.jobId);
        });
      }
      setTimeout(() => {
        this.handleResize();
      }, 0);
    },
    // 开料拼版
    CuttingClick() {
      // console.log(this.$refs.dom1.proOrderInfoDto.boardHeightSet)
      if (!this.proOrderInfoDto.boardHeightSet) {
        this.$message.warning("请填写成长");
        return;
      }
      if (!this.proOrderInfoDto.boardWidthSet) {
        this.$message.warning("请填写成宽");
        return;
      }
      if (this.id) {
        getCutting(this.id).then(res => {
          if (res.code) {
            this.CuttingData = res.data;
            this.$router.push({
              path: "/gongju/cutting",
              query: {
                boardThink: res.data.boardThink,
                cpLen: res.data.cpLen,
                cpWidth: res.data.cpWidth,
                job: res.data.job,
                layCount: res.data.layCount,
                pcsset: res.data.pcsset,
                pcssetName: res.data.pcssetName,
                joinFactoryId: res.data.joinFactoryId,
                numUDSix: res.data.numUDSix,
                txtCoupDL: res.data.txtCoupDL,
                txtCoupDW: res.data.txtCoupDW,
                type: "GC",
              },
            });
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    review() {
      ppebuttonCheck(this.$route.query.id, "PpeReview").then(res => {
        if (res.code) {
          if (res.data && res.data.length) {
            this.checkData = res.data;
            this.check = this.checkData.findIndex(v => v.error == "1") < 0;
            this.checkType = "review";
            this.dataVisible2 = true;
            this.meslist = "评审发起检查";
          } else {
            this.dataVisible7 = true;
            this.type = "review";
            if (!this.proOrderInfoDto.reviewNo) {
              this.message1 = this.proOrderInfoDto.orderNo + "确认该订单发起评审吗?";
            } else {
              this.message1 = `该订单已发起过${this.proOrderInfoDto.reviewNo}次评审 请确认是否要再次发起评审`;
            }
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //上传文件
    UpdateFile() {
      let Jumppage = this.$route.query.typee;
      let type = "";
      if (Jumppage == 1 || Jumppage == 2) {
        type = "CAM";
      } else if (Jumppage == 3) {
        type = "QAE";
      } else if (Jumppage == 5 || Jumppage == 6) {
        type = "HP";
      } else {
        this.$message.warning("当前状态不允许文件上传");
        return;
      }
      let url =
        "H2GInterface://?TCL=UpLoadFile&JOB=" +
        this.$route.query.OrderNo +
        "&AreaID=" +
        this.$route.query.factory +
        "&Guid=" +
        this.$route.query.id +
        "&mode=" +
        type;
      window.open(url, "_blank");
    },
    wenkeClick() {
      let OrderNo = this.orderNo;
      let lx = this.$route.query.typee;
      if (lx == 2) {
        this.$router.push({
          path: "eqDetails",
          query: {
            OrderNo: OrderNo,
            eQSource: 1,
            businessOrderNo: this.proOrderInfoDto.businessOrderNo,
            joinFactoryId: this.proOrderInfoDto.joinFactoryId,
            Jump: "制作",
            id: this.$route.query.id,
          },
        });
      } else if (lx == 1) {
        this.$router.push({
          path: "eqDetails",
          query: {
            OrderNo: OrderNo,
            eQSource: 2,
            businessOrderNo: this.proOrderInfoDto.businessOrderNo,
            joinFactoryId: this.proOrderInfoDto.joinFactoryId,
            Jump: "后端",
            id: this.$route.query.id,
          },
        });
      } else if (lx == 3) {
        this.$router.push({
          path: "eqDetails",
          query: {
            OrderNo: OrderNo,
            eQSource: 3,
            businessOrderNo: this.proOrderInfoDto.businessOrderNo,
            joinFactoryId: this.proOrderInfoDto.joinFactoryId,
            Jump: "QAE",
            id: this.$route.query.id,
          },
        });
      } else if (lx == 4 || lx == 6) {
        this.$router.push({
          path: "eqDetails",
          query: {
            OrderNo: OrderNo,
            eQSource: this.$route.query.eqSource,
            businessOrderNo: this.proOrderInfoDto.businessOrderNo,
            joinFactoryId: this.proOrderInfoDto.joinFactoryId,
            Jump: "问客",
            id: this.$route.query.id,
          },
        });
      } else if (lx == 5) {
        this.$message.warning("合拼设计进入工程指示不允许进入问客");
      }
    },
    // 叠层阻抗
    laminationClick() {
      if (this.id) {
        getStackImpedance(this.id).then(res => {
          if (res.code) {
            if (res.data.drills.length) {
              let drillsData = res.data.drills;
              localStorage.setItem("drillsData", JSON.stringify({ drillsData }));
            } else {
              localStorage.removeItem("drillsData");
            }
            let category = this.boardTypeList.filter(item => item.text.trim() == this.proOrderInfoDto.boardBrandStr)[0]?.category;
            let arr = this.boardTypeList.find(item => {
              return item.valueMember == res.data.boardType;
            });
            this.$router.push({
              path: "/gongju/impedance",
              query: {
                category: category == "高频" ? 1 : 0,
                boardType: res.data.boardType,
                ppType: arr ? (arr.ppCode ? arr.ppCode.toString() : "") : "",
                finishBoardThickness: res.data.finishBoardThickness,
                layers: res.data.layers,
                pdctno: res.data.pdctno,
                InCopperThickness: res.data.innerCopperThickness,
                OutCopperThickness: res.data.copperThickness,
                joinFactoryId: this.$route.query.factory,
                inCu: res.data.inCu,
                mode: 1,
                outCu: res.data.outCu,
                id: this.id,
              },
            });
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 弹窗取消
    reportHandleCancel() {
      this.dataVisibleMatter = false;
      this.prodataVisible = false;
      if (this.dataVisible4) {
        this.dataVisible4 = false;
        this.getJobInfo();
      }
      this.dataVisibleMode = false;
      this.dataVisible6 = false;
      this.message = [];
      this.dataVisible2 = false;
      this.dataVisible7 = false;
      this.HMnoticeVisible = false;
      this.LHDCnoticeVisible = false;
      this.message1 = "";
    },
    // 获取参数
    getInfoClick() {
      let url =
        "H2GInterface://?TCL=EMS&JOB=" +
        this.OrderNo.toLocaleLowerCase() +
        "&EMS_flows=StackUpInfo&AreaID=" +
        this.$route.query.factory +
        "&webUser=" +
        this.user.userName;
      window.open(url, "_blank");
      // this.spinning = true
    },
    // 指示检查
    CheckClick() {
      if (this.id) {
        let type = 0;
        if (this.$route.query.typee == 1) {
          // 工程后端
          type = 2;
        } else if (this.$route.query.typee == 2) {
          // 工程制作
          type = 1;
        }
        mIIndicationCheck(this.id, type).then(res => {
          if (res.code) {
            this.checkData = res.data;
            this.check = this.checkData.findIndex(v => v.error == "1") < 0;
            this.dataVisible2 = true;
            this.meslist = "指示检查信息";
            this.checkType = "zsjc";
          } else {
            this.$message.error(res, message);
          }
        });
      }
    },
    Testbuild() {},
    // 生成流程
    processClick(type) {
      let Buildtheprocess;
      if (type == "test") {
        Buildtheprocess = ceshicamAutoFlow;
      } else {
        Buildtheprocess = camAutoFlow;
      }
      ppebuttonCheck(this.$route.query.id, "SetMMKBAllData")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              if (type == "test") {
                this.checkType = "sclctest";
              } else {
                this.checkType = "sclc";
              }
              this.dataVisible2 = true;
              this.meslist = "生成流程按钮检查";
            } else {
              if (this.id) {
                this.spinning = true;
                setMMKBAllData(this.id).then(res => {
                  if (res.code) {
                    Buildtheprocess(this.id).then(res => {
                      if (res.code) {
                        console.log("导入叠层");
                        if (type == "formal") {
                          //生成流程后调取数据导入接口 固定流程入参层压工序ID(105) 生成叠层实体表
                          //2025/5/26 更改为传参416  CTLType_
                          setstackupitemv21(this.OrderNo, this.$route.query.factory)
                            .then(ite => {})
                            .finally(() => {
                              this.spinning = false;
                              this.$router.push({
                                path: "/gongcheng/technologicalProcess",
                                query: { pdctno: this.OrderNo, factoryId: this.$route.query.factory },
                              });
                            });
                        } else {
                          this.spinning = false;
                          this.$router.push({
                            path: "/gongcheng/technologicalProcess",
                            query: { pdctno: this.OrderNo, factoryId: this.$route.query.factory },
                          });
                        }
                      } else {
                        this.spinning = false;
                        this.$message.error(res.message);
                      }
                    });
                  } else {
                    this.$message.error(res.message);
                    this.spinning = false;
                  }
                });
              }
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //文件上传
    uploadfileClick() {
      let url = "H2GInterface://?TCL=UpLoadFile&JOB=" + this.OrderNo.toLocaleLowerCase() + "&EMS_flows=StackUpInfo&AreaID=0" + "&Id=" + this.id;
      window.open(url, "_blank");
    },
    // 文件夹上传
    uploadClick() {
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    customRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      setProFile(this.id, formData).then(res => {
        if (res.code) {
          this.$message.success("上传成功");
          this.$emit("getOrderList");
        } else {
          this.$message.error(res.message);
        }
      });
      // console.log(this.enterOrderForm.pdctno,this.enterOrderForm.tgzOssPath)
    },
    beforeUpload1(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
        // console.log("str_esc:",str_esc)
        if (!isFileType) {
          _this.$message.error("只支持.rar或.zip格式文件");
          reject();
          return isFileType;
        } else {
          resolve();
        }
      });
    },
    // 图形预览
    previewClick() {
      graphicPreview(this.$route.query.id).then(res => {
        if (res.data) {
          window.open(res.data, "_blank");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    formingChange() {
      if (this.proOrderDrillingDto.formingType != "machinemold+vcut" && this.proOrderDrillingDto.formingType != "vcut") {
        this.proOrderDrillingDto.vCut = null;
        this.proOrderDrillingDto.jumpCut = false;
        this.proOrderDrillingDto.vcutSurplusThickness = null;
        this.proOrderDrillingDto.vcutSurplusThicknessTol = null;
        this.proOrderDrillingDto.vcutAngle = null;
        this.proOrderDrillingDto.vcutAngleTol = null;
        this.proOrderDrillingDto.vCutKnifeNum = null;
      } else {
        this.proOrderDrillingDto.vCut = "ncvcut";
        if (this.proOrderInfoDto.boardThickness) {
          this.proOrderDrillingDto.vcutSurplusThickness = ((this.proOrderInfoDto.boardThickness * 1) / 3).toFixed(2);
        }
        this.proOrderDrillingDto.vcutSurplusThicknessTol = "1";
        this.proOrderDrillingDto.vcutAngle = "1";
        this.proOrderDrillingDto.vcutAngleTol = "1";
        // this.proOrderDrillingDto.vCutKnifeNum = null
      }
    },
    boardThickness() {
      if (this.proOrderDrillingDto.formingType == "machinemold+vcut" || this.proOrderDrillingDto.formingType == "vcut") {
        if (this.proOrderInfoDto.boardThickness) {
          this.proOrderDrillingDto.vcutSurplusThickness = ((this.proOrderInfoDto.boardThickness * 1) / 3).toFixed(2);
        }
      }
    },
    changelayers() {
      if (this.proOrderInfoDto.boardLayers == 0) {
        this.show0 = true;
      } else {
        this.show0 = false;
      }
      if (this.proOrderInfoDto.boardLayers == 1) {
        this.show1 = true;
      } else {
        this.show1 = false;
      }
      if (this.proOrderInfoDto.boardLayers == 2) {
        this.show2 = true;
      } else {
        this.show2 = false;
      }
      if (this.proOrderInfoDto.boardLayers >= 3) {
        this.showMore = true;
      } else {
        this.showMore = false;
      }
      if (this.proOrderInfoDto.plateType == "cg") {
        this.showCG = true;
      } else {
        this.showCG = false;
      }
      if (this.proOrderInfoDto.plateType == "hdi") {
        this.showHDI = true;
      } else {
        this.showHDI = false;
      }
      if (this.proOrderInfoDto.plateType == "mm") {
        this.showMM = true;
      } else {
        this.showMM = false;
      }
    },
    down(record) {
      //console.log('下载', record)
      // if (record.serverPath_) {
      //   window.location.href = record.serverPath_
      // } else {
      //   this.$message.error('无文件地址，无法下载')
      // }
      campnlfile(record.key, this.$route.query.factory, record.isFolder).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    del(record) {
      //console.log('删除', record.id)
      this.dataVisible7 = true;
      this.type = "8";
      this.isFolder = record.isFolder;
      this.message1 = "确认删除【" + record.key + "】文件吗？";
      // setGsFileDelete(record.id).then(res=>{
      //   if(res.code){
      //     this.$message.success('删除成功')
      //     this.getFileData()
      //   }else{
      //     this.$message.error(res.message)
      //   }
      // })
    },
    // 自定义树形选择图标
    expandIcon(props) {
      if (props.record.children.length > 0 && props.record.children != "null") {
        if (props.expanded) {
          //有数据-展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder-open" style="margin-right:5px" />
            </a>
          );
        } else {
          //有数据-未展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder" style="margin-right:5px" />
            </a>
          );
        }
      } else {
        //无数据-图标
        return (
          <span style="margin-right:0px">
            <a-icon type=" " />
          </span>
        );
      }
    },
    isRedRow1(record) {
      let strGroup = [];
      let str = [];
      let level = record.key;
      if (level == this.level) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    // 订单列表点击事件
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.level = record.key;
          },
        },
      };
    },
    addPropertyToTree1(tree, prop, parentLevel) {
      tree.forEach((node, index) => {
        node[prop] = parentLevel ? `${parentLevel}/${node.name}` : `${node.name}`;
        if (node.children) {
          this.addPropertyToTree1(node.children, prop, node[prop]);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-btn {
  padding: 0 10px;
}
.formclass {
  /deep/.ant-modal-body {
    padding: 0 !important;
  }
  /deep/.ant-modal-footer {
    padding: 10px 100px;
  }
}
/deep/.ant-drawer-content-wrapper {
  width: 350px !important;
}
/deep/.ant-drawer-body {
  padding: 7px;
}
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-empty-normal {
  padding: 76px 0;
  margin: 0;
  color: rgba(0, 0, 0, 0.25);
}
.Directprompt {
  border: 1px solid #d6d6d6;
  overflow: auto;
}
.listtable {
  height: 771px;
}
.note1 {
  color: black;
  background-color: #ececec;
}
.textNote {
  padding: 10px 0 10px 20px;
  background: #ececec;
}
.bzxx {
  border-left: 1px solid #efefef;
  .min-table1 {
    /deep/.ant-table-body {
      min-height: 739px;
    }
  }
  /deep/ .ant-table-thead > tr > th {
    padding: 4px 16px;
    border-right: 1px solid rgb(233, 233, 240);
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 4px 16px;
    border-right: 1px solid rgb(233, 233, 240);
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
.drawerclass {
  margin-right: 10px;
}
/deep/b {
  font-weight: 500;
}
/deep/.ant-form-item-label > label::after {
  margin: 0 2px 0 2px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
.div8 {
  border-left: 1px solid #efefef;
  /deep/ .ant-table-thead > tr > th {
    padding: 4px 16px;
  }
  /deep/ .ant-table-tbody > tr > td {
    padding: 4px 16px;
  }
}

/deep/.ant-modal-body {
  overflow-y: auto;
  max-height: 650px;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-spin-nested-loading {
  width: 100% !important;
  .ant-form {
    margin-top: 0 !important;
    border-top: 2px solid #f0f2f5;
  }
}
.box1 {
  display: -webkit-box;
  border-top: none !important;
  border: 2px solid rgb(233, 233, 240);
  border-bottom: 4px solid rgb(233, 233, 240);
  background: #ffffff;
  /deep/.rowBackgroundColor {
    background: #dfdcdc !important;
  }
  .leftContent {
    width: 150px;
    ul {
      margin-bottom: 0;
      padding-left: 0;
    }
    ul li {
      list-style: none;
      margin: 0;
      border-bottom: 1px solid rgb(233 230 230);
      padding-left: 10px;
      font: 14px;
      color: #000000;
      height: 28px;
      line-height: 28px;
    }
    li:hover {
      color: white;
      background-color: #ffc664;
      // background-color: #FFE7A3;
    }
    .licolor {
      color: white;
      background-color: #ff9900;
    }
  }
  /deep/ .rightContent {
    flex-wrap: wrap;
    height: 100%;
    .box {
      height: 771px;
    }
    .btn {
      height: 48px;
      border: 2px solid #e9e9f0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      background: #ffffff;
    }
    .min-table {
      .ant-table-body {
        min-height: 702px !important;
      }
    }
  }
}
.orderDetail {
  width: 75%;
  padding: 10px;
  background: #ffffff;
  /deep/.rowBackgroundColor {
    background: #fff9e6 !important;
  }
  // /deep/ .ant-tabs {
  //   // .viewInfo{
  //   //   .ant-table-thead{
  //   //     .ant-table-align-left{
  //   //       text-align: center!important;;
  //   //     }
  //   //   }
  //   // }
  //   margin-top: 10px;
  //   .ant-tabs-bar{
  //     margin: 0;
  //     border-bottom: 1px solid #ccc;
  //     .ant-tabs-nav-wrap {
  //       .ant-tabs-ink-bar {
  //         display: none!important;
  //       }
  //     }
  //     // .ant-tabs-tab {
  //     //   margin: 0;
  //     //   padding: 0 10px;
  //     //   border: 1px solid #ccc;
  //     //   font-size: 14px;
  //     //   height: 34px;
  //     //   line-height: 34px;
  //     //   border-left: 0;
  //     //   font-weight: 500;
  //     //   &:nth-child(1){
  //     //     border-left: 1px solid #ccc;;
  //     //   }
  //     // }
  //     // .ant-tabs-tab-active {
  //     //   border-top: 2px solid #f90 !important;
  //     //   border-bottom-color:#ffffff ;
  //     //   background: #ffffff;

  //     // }

  //   }
  // }
}
/deep/.ant-table {
  font-size: 14px !important;
  font-weight: 500;
}
</style>
