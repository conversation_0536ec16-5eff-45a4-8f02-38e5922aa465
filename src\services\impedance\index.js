/*
 * @Author: CJP
 * @Date: 2022-07-08 11:41:37
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-07-11 15:34:45
 * @FilePath: \vue-antd-admin\src\services\impedance\index.js
 * @Description:
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved.
 */
import { request, METHOD } from "@/utils/request";
import { transformAbpListQuery } from "@/utils/abp";

export async function construction(params) {
  return request("/api/app/e-mSTPub-stack-up-mode/stack-up-create", METHOD.POST, params);
}
//数据存盘上传pdf与中英文图片
export async function uploadstackimpall(params) {
  return request("/api/app/e-mSTPub-stack-up/up-load-stack-imp-all", METHOD.POST, params);
}
export async function uploadstackimptable(params) {
  return request("/api/app/e-mSTPub-stack-iMP/up-load-stack-imp-table", METHOD.POST, params);
}
export async function uploadstackimages(params) {
  return request("/api/app/e-mSTPub-stack-iMP/up-load-stack-images", METHOD.POST, params);
}
export async function uploadstackimagesv2(params) {
  return request("/api/app/e-mSTPub-stack-iMP/up-load-stack-images-v2", METHOD.POST, params);
}
export async function ozList(id) {
  return request(`/api/app/e-mSTPub-stack-up-basic/c-uList/${id}`, METHOD.GET);
}
export async function coreList(id, mode) {
  return request(`/api/app/e-mSTMtr-core/core-list/${id}?mode=${mode}`, METHOD.GET);
}
export async function gbList(joinFactoryId) {
  return request(`/api/app/e-mSTMtr-core/g-bList/${joinFactoryId}`, METHOD.GET);
}
export async function ppTable(pptype, factory) {
  return request(`/api/app/e-mSTPub-stack-up-basic/stack-up-pPComb?pptype=${encodeURIComponent(pptype)}&factory=${factory}`, METHOD.GET);
}
export async function linebcvalue(joinFactoryId, orderno) {
  return request(`/api/app/e-mSTPub-stack-iMP/line-bc-value/${joinFactoryId}?orderno=${orderno}`, METHOD.GET);
}
export async function ppInfo(params) {
  return request(`/api/app/e-mSTPub-stack-up-basic/stack-up-pPInfo/${params}`, METHOD.GET);
}
export async function saveData(params) {
  return request("/api/app/e-mSTPub-stack-up/data-save", METHOD.POST, params);
}
//叠层保存更新拉伸系数
export async function settensilecoef(id, JoinFactoryId, orderNo) {
  return request(`/api/app/e-mSTPub-stack-up/${id}/set-tensilecoef/${JoinFactoryId}?Pdctno=${orderNo}`, METHOD.POST);
}
//计算板厚调取接口
export async function stackcalcv2(params) {
  return request("/api/app/e-mSTPub-stack-up/stack-calc-v2", METHOD.POST, params);
}
//盲孔损耗
export async function stackcalcv3(JoinFactoryId, OrderNo, mode) {
  return request(`/api/app/e-mSTPub-stack-up/stack-calc-v3/${JoinFactoryId}?OrderNo=${OrderNo}&mode=${mode}`, METHOD.POST);
}
export async function stackcalc(params) {
  return request("/api/app/e-mSTPub-stack-up/stack-calc", METHOD.POST, params);
}
export async function seleZK(params) {
  return request("/api/app/e-mSTPub-stack-iMP/imp-type", METHOD.POST, params);
}
//工程指示阻抗信息新增
export async function creatstackimp(params) {
  return request("/api/app/engineering-make-info-up/creat-stack-imp", METHOD.POST, params);
}
export async function seleCU(params) {
  return request(`/api/app/e-mSTPub-stack-iMP/stack-iMPT1C1W1/${params}`, METHOD.POST);
}
// 计算
export async function impedanceCount(params) {
  return request("/api/app/e-mSTPub-stack-iMP/i-mPCalc-result", METHOD.POST, params);
}
//阻抗报告
export async function impoutreportinfo(OrderNo, Factory) {
  return request(`/api/app/pcb-order/imp-out-report-info?OrderNo=${OrderNo}&Factory=${Factory}`, METHOD.GET);
}
// 反算
export async function impedanceInverseCount(params) {
  return request("/api/app/e-mSTPub-stack-iMP/i-mPInverse-calc-result", METHOD.POST, params);
}
// 调出数据
export async function getInpedanceData(params, joinFactoryId) {
  return request(`/api/app/e-mSTPub-stack-up-mode/data-out-put/${joinFactoryId}`, METHOD.GET, params);
}
//后端直接导出阻抗报表
export async function stackreport(params) {
  return request(`api/app/e-mSMake-module-no/stack-report`, METHOD.GET, params);
}
//阻抗信息导出
export async function stackiMP(factory, orderNo) {
  return request(`/api/app/e-mSMake-module-no/stack-iMPExcel/${factory}?orderNo=${orderNo}`, METHOD.GET);
}
// 获取工厂列表
export async function getFactoryList() {
  return request("/api/app/e-mSTPub-factory-configure/factory-id-by-stack-list", METHOD.POST);
}

// 华秋导出文本
export async function getTextOrJson(params) {
  return request("/api/app/e-mSTPub-stack-up/export-json-for-hua-qiu", METHOD.POST, params);
}

// 调出模板
export async function getTemplateData(params) {
  return request("/api/app/e-mSTPub-stack-up-mode/stack-up-temp", METHOD.POST, params);
}
// 调出数据(自营)
export async function getTemplateData4M01(params) {
  return request("/api/app/e-mSTPub-stack-up-mode/cascade-out-template", METHOD.POST, params);
}
// 获取铜厚下拉选择
export async function TemplateCu(params) {
  return request("/api/app/e-mSTPub-stack-up-mode/template-cu", METHOD.POST, params);
}
//
export async function TemplateList(params) {
  return request("/api/app/e-mSTPub-stack-up-mode/template-list", METHOD.GET, params);
}
// 获取自动叠层基础信息、线路
export async function getAutoStackInfoData(params) {
  return request("/api/app/e-mSTPub-stack-up/set-auto-stackinfo", METHOD.POST, params);
}
export async function setautostackinfov2(params) {
  return request("/api/app/e-mSTPub-stack-up/set-auto-stackinfo-v2", METHOD.POST, params);
}
export async function setautostackinfov3(params) {
  // return request("api/app/e-mSTPub-stack-up/set-auto-stackinfo-v3", METHOD.POST, params)
  return request("/api/app/e-mSTPub-stack-up/set-auto-stackinfo-general", METHOD.POST, params);
}
// 自动叠层基
export async function getAutoStackResultData(params) {
  return request("/api/app/e-mSTPub-stack-up/auto-stack", METHOD.POST, params);
}
export async function calcStackUpCu(params) {
  return request("/api/app/e-mSTPub-stack-up/calc-stack-up-cu", METHOD.POST, params);
}
//导入残铜率
export async function setstackupcopperratio(params) {
  return request("/api/app/e-mSTPub-stack-up/set-stack-up-copper-ratio", METHOD.POST, params);
}

export default {
  construction,
  ozList,
  coreList,
  ppTable,
  ppInfo,
  saveData,
  seleZK,
  seleCU,
  getFactoryList,
  TemplateCu,
  TemplateList,
  getAutoStackInfoData,
  setautostackinfov2,
  getAutoStackResultData,
};
