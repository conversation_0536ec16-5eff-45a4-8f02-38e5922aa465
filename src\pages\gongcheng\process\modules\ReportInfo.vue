<!--工程管理- 工艺流程- 预下载PDF -->
<template>
  <div :class="ttype == 'scgj' ? 'pdfDom1' : 'pdfDom'">
    <a-spin :spinning="spinning">
      <div v-if="JSON.stringify(showData) != '{}' && flowData.length && [70, 78, 79, 80].indexOf(Number(CardId)) == -1">
        <button v-print="printObj">打印</button>
        <div id="pdfDomMIother" ref="commentBox">
          <div id="printMeother">
            <div style="page-break-after: auto" v-for="(ite, index) in flowData" :key="index">
              <div class="ifPagingNode print-page">
                <div style="font-size: 20px; font-weight: 700; text-align: center; letter-spacing: 5px" v-if="index == 0">
                  {{ showData.factoryname }}
                </div>
                <div id="titleDom" style="color: #000000" v-if="ite.flIndxe.length || ite.drlIndxe.length">
                  <div style="font-size: 22px; text-align: center; letter-spacing: 15px">生产指示</div>
                  <div style="font-size: 16px; margin-right: 10px; text-align: left">客户代码：{{ showData.custno }}</div>
                  <div style="font-size: 16px; margin-right: 10px; text-align: left">
                    生产型号：{{ showData.orderno }}
                    <span v-if="ite.startLayer != 1 || ite.endLayer != lay">{{ getSubcard(ite.startLayer, ite.endLayer) }}</span>
                  </div>
                  <div style="font-size: 16px; margin-right: 10px; text-align: left">客户型号: {{ showData.pcbfilename }}</div>
                </div>
                <!--制造加工指示信息 -->
                <div v-if="ite.flIndxe.length" style="margin-bottom: 40px" id="table1Dom">
                  <a-table
                    :columns="columns"
                    :dataSource="ite.flIndxe"
                    :pagination="false"
                    :rowKey="
                      (record, index) => {
                        return index;
                      }
                    "
                    bordered
                    class="table1"
                    :rowClassName="getRowClass"
                  >
                    <template slot="parameter" slot-scope="text, record">
                      <div v-html="record.parameter"></div>
                    </template>
                  </a-table>
                  <table border="1" style="border-color: #000000; border-top: 0; width: 100%; color: #000000">
                    <tr style="height: 24px; text-align: center">
                      <td style="border-top: 0; width: 100%; text-align: left" colspan="8">制作记录统计：</td>
                    </tr>
                    <tr style="height: 24px; width: 100%; text-align: center">
                      <td>制作人:</td>
                      <td>{{ showData.proAdminName }}</td>
                      <td>审核人:</td>
                      <td>{{ showData.camCheckName }}</td>
                      <td colspan="4"></td>
                    </tr>
                    <tr style="height: 24px; text-align: center">
                      <td style="width: 10%">投入数：</td>
                      <td style="width: 15%"></td>
                      <td style="width: 10%">产出数：</td>
                      <td style="width: 15%"></td>
                      <td style="width: 10%">报废数：</td>
                      <td style="width: 15%"></td>
                      <td style="width: 10%">合格率：</td>
                      <td style="width: 15%"></td>
                    </tr>
                  </table>
                </div>
              </div>
              <!--钻咀表  -->
              <div v-if="ite.drlIndxe.length" style="margin-bottom: 40px" id="table2Dom" class="ifPagingNode print-page">
                <div style="text-align: center; font-size: 20px; font-weight: 600">钻咀表</div>
                <a-table
                  :columns="columns2"
                  :dataSource="ite.drlIndxe"
                  :pagination="false"
                  :rowKey="
                    (record, index) => {
                      return index;
                    }
                  "
                  bordered
                  class="table2"
                  :rowClassName="getRowClass1"
                >
                </a-table>
              </div>
              <!-- 叠层图 -->
              <div v-if="ite.stack.length" style="margin-bottom: 40px" class="ifPagingNode print-page">
                <div style="text-align: center; font-size: 20px; font-weight: 600">叠层阻抗</div>
                <a-table
                  :columns="columns3"
                  :dataSource="ite.stack"
                  :pagination="false"
                  :rowKey="
                    (record, index) => {
                      return index;
                    }
                  "
                  bordered
                >
                  <template slot="tG_" slot-scope="text, record">
                    <div>{{ record.bfloor_ }}&emsp;{{ record.tG_ }}</div>
                  </template>
                </a-table>
              </div>
            </div>
            <!-- 开料图 -->
            <div v-if="showData.panel || showData.sheet" class="ifPagingNode print-page">
              <div style="text-align: center; font-size: 20px; font-weight: 600">开料图</div>
              <div style="display: flex; justify-content: center">
                <div class="div1" style="width: 280px; margin-right: 5%" v-if="showData.panel">
                  <img crossorigin="anonymous" :src="showData.panel + '?a=' + new Date().getTime()" style="width: 100%; min-height: 400px" />
                </div>
                <div class="div2" style="width: 350px" v-if="showData.sheet">
                  <img crossorigin="anonymous" :src="showData.sheet + '?a=' + new Date().getTime()" style="width: 100%; min-height: 400px" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="JSON.stringify(showData) != '{}' && flowData.length && [70, 78, 79, 80].indexOf(Number(CardId)) != -1">
        <button v-print="printObj1">打印</button>
        <div>
          <div id="pdfDomMImtgx" ref="commentBox">
            <div id="printMemtgx">
              <div v-for="(ite, index) in flowData" :key="index" class="print-page">
                <div style="font-size: 26px; font-weight: bold; text-align: center; letter-spacing: 5px; color: #000000" v-if="index == 0">
                  {{ showData.factoryname }}
                </div>
                <div class="ifPagingNode" style="color: #000000" v-if="ite.flIndxe.length || ite.drlIndxe.length">
                  <div style="font-size: 22px; text-align: center; letter-spacing: 15px">生产指示</div>
                  <div style="font-size: 16px; margin-right: 10px; text-align: left">客户代码：{{ showData.custno }}</div>
                  <div style="font-size: 16px; margin-right: 10px; text-align: left">
                    生产型号：{{ showData.orderno }}
                    <span v-if="ite.startLayer != 1 || ite.endLayer != lay">{{ getSubcard(ite.startLayer, ite.endLayer) }}</span>
                  </div>
                  <div style="font-size: 16px; margin-right: 10px; text-align: left">客户型号: {{ showData.pcbfilename }}</div>
                </div>
                <!-- 抬头 只有主卡才展示抬头信息-->
                <div style="padding: 5px 0" v-if="ite.startLayer == 1 && ite.endLayer == lay">
                  <h1 style="font-weight: bold; text-align: center">{{ showData.value_31 }}</h1>
                  <table border="1" style="width: 100%; border-color: #000000; color: #000000; font-weight: bold; font-size: 16px">
                    <tr>
                      <td>批次:{{ showData.value_1 }}</td>
                      <td>板材规格:{{ showData.value_2 }} 成品板厚:{{ showData.value_3 }} 公差:{{ showData.value_29 }}</td>
                      <td>层数:{{ showData.value_4 }} 成品铜厚:{{ showData.value_5 }}</td>
                      <td>最小孔径:{{ showData.value_6 }}</td>
                    </tr>
                    <tr>
                      <td>图号:{{ showData.value_27 }}</td>
                      <td>单元尺寸:{{ showData.value_8 }}</td>
                      <td colspan="2">
                        本卡开料(pnl):&emsp;&emsp;&emsp;{{ showData.value_9 }} 开料拼版:{{ showData.value_10 }} 交货尺寸:{{ showData.value_11 }}
                      </td>
                    </tr>
                    <tr>
                      <td>
                        阻焊:{{ showData.value_12 }} <br />
                        字符:{{ showData.value_13 }}
                      </td>
                      <td>验收标准:{{ showData.value_14 }}</td>
                      <td colspan="2">
                        开料尺寸(mm):{{ showData.value_15 }} A板:{{ showData.value_16 }} 开料数A:{{ showData.value_17 }} 面积:{{ showData.value_18 }}
                      </td>
                    </tr>
                    <tr>
                      <td>编码:{{ showData.value_19 }}</td>
                      <td>多投数量:{{ showData.value_20 }}</td>
                      <td colspan="2">订货数量:{{ showData.value_21 }} （订单面积:{{ showData.value_22 }} 本卡开料:{{ showData.value_23 }}）</td>
                    </tr>
                    <tr>
                      <td>交货日期:{{ showData.value_30 }} 返单号:{{ showData.value_24 }}</td>
                      <td>合同号:{{ showData.value_25 }}</td>
                      <td>计划完工日期:{{ showData.value_26 }}</td>
                      <td>最后处理时间:{{ showData.value_28 }}</td>
                    </tr>
                  </table>
                </div>
                <!-- 制造加工指示信息 -->
                <div v-if="ite.flIndxe.length" style="margin-bottom: 40px" id="table1Dom">
                  <a-table
                    :columns="columns"
                    :dataSource="ite.flIndxe"
                    :pagination="false"
                    :rowKey="
                      (record, index) => {
                        return index;
                      }
                    "
                    bordered
                    class="table1"
                    :rowClassName="getRowClass"
                  >
                    <template slot="parameter" slot-scope="text, record">
                      <div v-html="record.parameter"></div>
                    </template>
                  </a-table>
                  <table border="1" style="border-color: #000000; border-top: 0; width: 100%; color: #000000">
                    <tr style="height: 24px; text-align: center">
                      <td style="border-top: 0; width: 100%; text-align: left" colspan="8">制作记录统计：</td>
                    </tr>
                    <tr style="height: 24px; width: 100%; text-align: center">
                      <td>制作人:</td>
                      <td>{{ showData.proAdminName }}</td>
                      <td>审核人:</td>
                      <td>{{ showData.camCheckName }}</td>
                      <td colspan="4"></td>
                    </tr>
                    <tr style="height: 24px; text-align: center">
                      <td style="width: 10%">投入数：</td>
                      <td style="width: 15%"></td>
                      <td style="width: 10%">产出数：</td>
                      <td style="width: 15%"></td>
                      <td style="width: 10%">报废数：</td>
                      <td style="width: 15%"></td>
                      <td style="width: 10%">合格率：</td>
                      <td style="width: 15%"></td>
                    </tr>
                  </table>
                </div>
                <!-- 钻咀表 -->
                <div v-if="ite.drlIndxe.length" style="margin-bottom: 40px" id="table2Dom" class="print-page">
                  <div style="text-align: center; font-size: 20px; font-weight: 600" class="ifPagingNode">钻咀表</div>
                  <a-table
                    :columns="columns2"
                    :dataSource="ite.drlIndxe"
                    :pagination="false"
                    :rowKey="
                      (record, index) => {
                        return index;
                      }
                    "
                    bordered
                    class="table2"
                    :rowClassName="getRowClass1"
                  >
                  </a-table>
                </div>
                <!-- 叠层图 -->
                <div v-if="ite.stack.length" style="margin-bottom: 40px" class="print-page">
                  <div style="text-align: center; font-size: 20px; font-weight: 600">叠层阻抗</div>
                  <a-table
                    :columns="columns3"
                    :dataSource="ite.stack"
                    :pagination="false"
                    :rowKey="
                      (record, index) => {
                        return index;
                      }
                    "
                    bordered
                  >
                    <template slot="tG_" slot-scope="text, record">
                      <div>{{ record.bfloor_ }}&emsp;{{ record.tG_ }}</div>
                    </template>
                  </a-table>
                </div>
              </div>
              <!-- 开料图 -->
              <div v-if="showData.panel || showData.sheet" class="print-page">
                <div style="text-align: center; font-size: 20px; font-weight: 600">开料图</div>
                <div style="display: flex; justify-content: center">
                  <div class="div1" style="width: 280px; margin-right: 5%" v-if="showData.panel">
                    <img crossorigin="anonymous" :src="showData.panel + '?a=' + new Date().getTime()" style="width: 100%; min-height: 400px" />
                  </div>
                  <div class="div2" style="width: 350px" v-if="showData.sheet">
                    <img crossorigin="anonymous" :src="showData.sheet + '?a=' + new Date().getTime()" style="width: 100%; min-height: 400px" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <a-empty v-else />
    </a-spin>
  </div>
</template>

<script>
const columns = [];
const columnsmain = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: "5%",
  },
  {
    title: "工序",
    dataIndex: "man",
    align: "center",
    className: "manstyle",
    width: "15%",
    customRender(text, row) {
      return {
        children: text,
        attrs: {
          rowSpan: row.manRowSpan,
        },
      };
    },
  },
  {
    title: "子工序",
    dataIndex: "name",
    align: "center",
    width: "15%",
  },
  {
    title: "加工要求与验收标准",
    align: "left",
    scopedSlots: { customRender: "parameter" },
  },
];
const columnsname = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: "5%",
  },
  {
    title: "工序",
    dataIndex: "name",
    className: "manstyle",
    align: "center",
    width: "15%",
  },
  {
    title: "加工要求与验收标准",
    align: "left",
    scopedSlots: { customRender: "parameter" },
  },
];
const columnsmainmtgx = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: "5%",
  },
  {
    title: "工序",
    dataIndex: "man",
    align: "center",
    className: "manstyle",
    width: "10%",
    customRender(text, row) {
      return {
        children: text,
        attrs: {
          rowSpan: row.manRowSpan,
        },
      };
    },
  },
  {
    title: "子工序",
    dataIndex: "name",
    align: "center",
    width: "10%",
  },
  {
    title: "加工要求与验收标准",
    align: "left",
    scopedSlots: { customRender: "parameter" },
  },
  {
    title: "设备",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "数量",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "完成时间",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "操作员",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "不合格品状态",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "检验员",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "备注",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
];
const columnsnamemtgx = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: "5%",
  },
  {
    title: "工序",
    dataIndex: "name",
    className: "manstyle",
    align: "center",
    width: "10%",
  },
  {
    title: "加工要求与验收标准",
    align: "left",
    width: "36%",
    scopedSlots: { customRender: "parameter" },
  },
  {
    title: "设备",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "数量",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "完成时间",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "操作员",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "不合格品状态",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "检验员",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
  {
    title: "备注",
    dataIndex: "",
    align: "center",
    width: "7%",
  },
];
const columns2 = [
  {
    title: "刀号",
    dataIndex: "toolno",
    align: "left",
    width: "8%",
  },
  {
    title: "钻带名称",
    dataIndex: "drillname",
    align: "left",
    width: "9%",
  },
  {
    title: "成品孔径(mm)",
    dataIndex: "storedia",
    align: "left",
    width: "12%",
  },
  {
    title: "PTH",
    dataIndex: "toolkind",
    align: "left",
    width: "8%",
  },
  {
    title: "公差(mm)",
    dataIndex: "minustol",
    align: "left",
    width: "8%",
  },
  {
    title: "钻咀(mm)",
    align: "left",
    width: "8%",
    dataIndex: "tooldia",
  },
  {
    title: "总孔数(个)",
    dataIndex: "qty",
    align: "left",
    width: "8%",
  },
  {
    title: "备注",
    dataIndex: "captions",
    align: "left",
  },
];
const columns3 = [
  {
    title: "层号",
    dataIndex: "sectionNo_",
    width: 60,
    align: "center",
  },
  {
    title: "剖面",
    dataIndex: "thick_",
    width: 135,
    align: "center",
  },
  {
    title: "物料要求",
    align: "center",
    scopedSlots: { customRender: "tG_" },
  },
  {
    title: "厚度",
    dataIndex: "inControl_",
    width: 70,
    align: "center",
  },
  {
    title: "残铜率",
    dataIndex: "ctL_",
    width: 70,
    align: "center",
  },
  {
    title: "RC要求",
    dataIndex: "rC_",
    align: "center",
    width: 70,
  },
];
import htmlToPdfurl from "@/utils/htmlToPdfurl";
import { mapState } from "vuex";
// import printHtml from "@/utils/print.js"
import htmlToPdf from "@/utils/htmlToPdf";
import { getInpedanceData } from "@/services/impedance";
import { getfrontflowmI } from "@/services/project";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
export default {
  name: "reportInfo",
  // components: {barcode},
  props: {
    CardId: {
      type: Number,
    },
    lay: {
      type: Number,
    },
    orderNo: {
      type: String,
    },
    businessOrderNo: {
      type: String,
    },
    ttype: {
      type: String,
    },
  },
  data() {
    return {
      spinning: false,
      formData: null,
      showData: {},
      flowData: [],
      drlData: [],
      columns,
      columns2,
      columns3,
      columnsmain,
      columnsname,
      columnsmainmtgx,
      columnsnamemtgx,
      vcutData: [],
      proData: [],
      data: [],
      that: this,
      height: window.document.documentElement.clientHeight - 158,
      style_: 0,
      pageNumber: 0,
      printObj: {
        id: "printMeother", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
        previewBeforeOpenCallback(vue) {
          console.log("正在加载预览窗口");
        },
        previewOpenCallback(vue) {
          console.log("已经加载完预览窗口");
        },
        clickMounted: vue => {
          console.log("触发点击打印回调", vue);
          vue.isShowPrint = true; //弹框显示条码
        },
        beforeOpenCallback(vue) {
          console.log("打开之前", vue);
        },
        openCallback(vue) {
          vue.isShowPrint = false; // 关闭条码显示弹框
          console.log("执行了打印", vue);
        },
      },
      printObj1: {
        id: "printMemtgx", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
        previewBeforeOpenCallback(vue) {
          console.log("正在加载预览窗口");
        },
        previewOpenCallback(vue) {
          console.log("已经加载完预览窗口");
        },
        clickMounted: vue => {
          console.log("触发点击打印回调", vue);
          vue.isShowPrint = true; //弹框显示条码
        },
        beforeOpenCallback(vue) {
          console.log("打开之前", vue);
        },
        openCallback(vue) {
          vue.isShowPrint = false; // 关闭条码显示弹框
          console.log("执行了打印", vue);
        },
      },
      cardNum: 0,
      cardNum1: 0,
      allCardNum: 0,
      cardArea: 0,
      allCardArea: 0,
      createTime: "",
      laminationData: {},
      finishBoardThickness: "",
      isFeeded: false,
      pinBanType: "",
      pageSum: 0,
      pageHeight: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
      },
      pageInfo: [],
      pages: [],
    };
  },
  async mounted() {
    await this.getData();
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  methods: {
    rowSpan2(key) {
      let _list = this.flowData[0].flIndxe;
      let _num = []; // 相同的数据先存储在一起
      let indexList = []; // 相同数据的下标数组
      let count = 0;
      for (let i = 0; i < _list.length; i++) {
        let downKey = _list[i + 1] ? _list[i + 1][key] : "";
        if (_list[i][key] != downKey) {
          _num.push(_list[i]);
          indexList.push(i);
          for (let z = 0; z < _num.length; z++) {
            _list[indexList[0]][`${key}RowSpan`] = _num.length;
            if (z != 0) {
              _list[indexList[z]][`${key}RowSpan`] = 0;
            }
          }
          _num = [];
          indexList = [];
          continue;
        } else {
          _num.push(_list[i]);
          indexList.push(i);
        }
      }
      // 重新渲染数据
      this.flowData[0].flIndxe = _list;
    },
    getReporturl() {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          let dom = this.CardId == "70" ? "pdfDomMImtgx" : "pdfDomMIother";
          const pdfBlob = htmlToPdfurl(dom, "MI.pdf");
          pdfBlob
            .then(result => {
              this.formData = this.arrayBufferToFormData(result, "pdf");
            })
            .catch(error => {
              reject(error);
            });
        });
      });
    },
    arrayBufferToFormData(arrayBuffer, type) {
      const formData = new FormData();
      const blob = new Blob([arrayBuffer]);
      if (type == "pdf") {
        formData.append("file", blob, "MI.pdf");
      }
      return formData;
    },
    getSubcard(start, end) {
      let s = start < 10 ? "0" + start : start;
      let e = end < 10 ? "0" + end : end;
      return "-" + s + e;
    },
    getRowClass({ row, index }) {
      return "ifPagingNode";
    },
    getRowClass1(row) {
      if (row.toolno == "合计") {
        return "totalclass";
      } else {
        return "ifPagingNode";
      }
    },
    async getData() {
      this.spinning = true;
      await getfrontflowmI(this.CardId, this.businessOrderNo)
        .then(res => {
          if (res.code) {
            this.showData = res.data;
            this.flowData = res.data.flow;
            this.rowSpan2("man");
            if (this.flowData[0].flIndxe.length && this.flowData[0].flIndxe[0].man) {
              this.columns = [70, 78, 79, 80].indexOf(Number(this.CardId)) != -1 ? this.columnsmainmtgx : this.columnsmain;
            } else {
              this.columns = [70, 78, 79, 80].indexOf(Number(this.CardId)) != -1 ? this.columnsnamemtgx : this.columnsname;
            }
          }
        })
        .finally(() => {
          this.spinning = false;
          this.getReporturl();
        });
    },
  },
};
</script>
<style lang="less">
.manstyle {
  font-weight: bold;
}
//@page { margin: 24px  24px 0 24px; }

@media print {
  .print-page {
    page-break-before: always; //始终在元素前插入分页符
  }
  .print-page:first-child {
    page-break-before: auto; //避免在页面开头插入分页符。这样可以确保第一个子卡不会在打印时单独占据一页，而是从打印页面的顶部开始
  }
  .ifPagingNode {
    page-break-inside: avoid; //确保不会被分页符分割
  }
  td {
    -webkit-print-color-adjust: exact; /* 强制打印背景色 */
  }
  table {
    page-break-after: auto;
  }
  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }
  td {
    page-break-inside: avoid;
    page-break-after: auto;
  }
  thead {
    display: table-header-group;
  } //表格的行头
  tfoot {
    display: table-footer-group;
  } //表格的行尾
}

.table1 {
  page-break-after: auto;
}
.table2 {
  page-break-after: auto;
}
.ifPagingNode {
  page-break-inside: avoid;
  page-break-after: auto;
}
.totalclass {
  page-break-inside: avoid;
  page-break-after: auto;
  background: #dfdcdc !important;
}
.pdfDom {
  overflow: auto;
  height: 735px;
  // margin: -5px;
  color: #000000;
  break-after: auto;
}
.pdfDom1 {
  overflow: auto;
  height: 1px;
  color: #000000;
  break-after: auto;
}
#pdfDomMIother {
  padding: 24px;
  break-after: auto;
}
#pdfDomMImtgx {
  padding: 24px;
  break-after: auto;
  color: #000000;
}
/deep/ .ant-card {
  .ant-card-head {
    padding: 0;
    min-height: auto;
    border: 0;
    .ant-card-head-title {
      padding: 0;
      border-bottom: 1px solid black;
      height: 29px;
      line-height: 20px;
      margin-bottom: 15px;
      text-indent: 5px;
      font-size: 14px;
      font-weight: normal;
      color: #000;
      padding-bottom: 8px;
      margin-top: 15px;
    }
  }
  .ant-card-body {
    padding: 0;
    .ant-form {
      border-left: 1px solid black;
      border-top: 1px solid black;
    }
    .ant-form-item {
      margin: 0;
      width: 100%;
      display: flex;

      .editWrapper {
        display: flex;
        align-items: center;
        min-height: 32px;
        .ant-select {
          width: 120px;
        }
        .ant-input {
          width: 120px;
        }
        .ant-input-number {
          width: 120px;
        }
      }
      .ant-form-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
        color: #666;
        background-color: #fafafa;
        border-right: 1px solid black;
        border-bottom: 1px solid black;
        label {
          font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
        }
      }
      .ant-form-item-control-wrapper {
        font: 12px/1.14 "微软雅黑", arial, \5b8b\4f53;
        .ant-form-item-control {
          .ant-form-item-children {
            display: block;
            min-height: 13.672px;
          }
          line-height: inherit;
          padding: 8px 10px;
          border-right: 1px solid black;
          border-bottom: 1px solid black;
        }
      }
    }
  }
}
.ant-table-thead > tr > th {
  padding: 6px 0 0 5px;
  color: black;
  // border-color:black!important;
}
.ant-table-tbody > tr > td {
  padding: 5px 0 0 5px;
  color: black;
  // border-color:black!important;
}
.box {
  width: 100%;
}
// .ant-table-body > table{
//   border-color:black;
// }
.box tbody > tr > th {
  border-right: 1px solid black;
  border-bottom: 1px solid black;
  width: 12.6%;
  padding: 0 4px;
  color: black;
}
</style>
<style scoped lang="less">
/deep/.ant-table-body > table {
  border-color: black !important;
}
/deep/.ant-table-thead > tr > th {
  padding: 6px 0 0 2px !important;
  border-color: black !important;
}
/deep/.ant-table-tbody > tr > td {
  padding: 4px 0 0 2px !important;
  border-color: black !important;
}

.drillClss {
  left: 0;
  top: 0;
  padding: 23px 20px 20px;
  width: 100%;
  position: absolute;
  height: 100%;
}
.drillItem {
  width: 9px;
  position: absolute;
  background: #0000cc;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItem:after {
  content: "";
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #0000cc;
  position: absolute;
  bottom: -8px;
  left: -3px;
}
.reportTable {
  border: 1px solid;
  border-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  .thickness {
    width: 100%;
    text-align: right;
    padding-right: 15px;
    line-height: 30px;
    font-weight: 500;
    font-size: 16px;
    border-top: 1px solid;
    border-bottom: 1px solid;
  }
  .layerName {
    width: 60px;
    height: 100%;
    border: 2px solid;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
  }
  .ozlastClass {
    align-items: center;
  }
  .OZclass {
    height: 30px;
    display: flex;
    overflow: hidden;
    align-items: center;
    .ozContent {
      //border-bottom: 1px solid;
      width: 100%;
      margin-left: 20px;
      display: flex;
      align-items: flex-end;
      .oz_bg {
        width: 100%;
        border-radius: 7px;
        height: 10px;
        overflow: hidden;
        background: url("../../../../assets/img/pp.png") repeat-x;
        background-position-x: -12px;
      }
      .oz_active {
        background: url("../../../../assets/img/pp2.png") repeat-x;
        background-position-x: -12px;
      }
    }
  }
  .PPclass {
    overflow: hidden;
    .PPContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 12px;
      margin: 5px 0;
      background: #9acd32;
      border-radius: 5px;
    }
  }
  .coreClass {
    .ozActive:nth-of-type(1) {
      margin-bottom: 20px;
    }
    position: relative;
    .coreActive {
      position: absolute;
      width: 100%;
      top: 16px;
    }
    .ozContent2 {
      border-bottom: 2px solid black;
      width: 100%;
    }
    .core-box {
      height: 48px;
      width: 100%;
      overflow: hidden;
      .CoreContent {
        height: 100%;
        float: right;
        background: #fcb505;
        width: calc(100% - 68px);
        background: #fcb408;
      }
    }
  }
  .GBClass {
    overflow: hidden;
    .gbContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 22px;
      margin: 3px 0;
      background: #fcb505;
      border-radius: 5px;
    }
  }
  .parameterClass {
    flex: 1;
    table tr th {
      text-align: center;
      width: 80px;
    }
    table tr th:nth-child(2) {
      width: 100px;
    }
    table tr th:nth-child(3) {
      width: 140px;
    }
    table tr th:nth-child(4) {
      width: 60px;
    }
    table tr th:nth-child(5) {
      width: 100px;
    }
    table {
      border-left: 1px solid black;
    }
    table tr:nth-child(1) {
      border: 1px solid black;
      border-left: none;
    }
    table tr th {
      border-right: 1px solid black;
    }
    table tr td {
      text-align: center;
      color: #0000cc;
      border-right: 1px solid black;
      border-bottom: 1px solid black;
    }
    .paramOZ {
      height: 27px;
      line-height: 27px;
    }
    .paramCore {
      height: 80px;
      td {
        white-space: pre;
      }

      //line-height: 30px;
    }
    .paramPP {
      height: 19px;
      line-height: 19px;
    }
  }
}
.impedance {
  display: flex;
  flex-wrap: wrap;
  padding-left: 5px;
  h4 {
    width: 100%;
  }
  h3 {
    display: flex;
    width: 250px;
    justify-content: space-between;
    p {
      margin: 0;
      margin-right: 10px;
    }
    p:nth-child(1) {
      margin: 0;
    }
  }
  // .imp_left{
  // }
  .line_flex {
    display: flex;
    p {
      margin: 0;
      margin-right: 15px;
      font-size: 14px;
      line-height: 25px;
      font-weight: 500;
    }
    p:nth-child(2) {
      width: 60px;
    }
  }
  .imp_center {
    margin: 10px 40px;
    img {
      padding: 15px;
      background: #008181;
    }
  }
  .imp_right {
    .line_flex {
      p:nth-child(1) {
        width: 40px;
      }
    }
  }
}
</style>
