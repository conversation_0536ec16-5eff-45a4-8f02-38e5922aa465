<!-- 工程管理 - 工程制作- 左边订单列表 -->
<template>
  <div ref="tableWrapper" style="position: relative">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ x: 1411, y: 738 }"
      :customRow="onClickRow"
      :pagination="pagination"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      @change="handleTableChange"
      :rowClassName="isRedRow"
    >
      <template slot="labelUrl" slot-scope="record">
        <span @click.stop="OperationLog(record)" style="color: #428bca">日志</span>
      </template>
      <div slot="score" slot-scope="text, record">
        <a-tooltip :title="xstitle">
          <span v-if="record.score" style="color: #428bca" @mouseover="Coefficientdetails(record.proOrderId)">{{ record.score.toFixed(2) }}</span>
        </a-tooltip>
      </div>
      <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
        <span :title="record.orderNo">{{ text }}</span
        >&nbsp;
        <span class="tagNum" style="display: inline-block; height: 19px">
          <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
            <span
              class="noCopy"
              style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
            <span
              class="noCopy"
              style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tooltip title="加急" v-else-if="record.isJiaji">
            <span
              class="noCopy"
              style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none; line-height: normal"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tooltip title="新客户" v-if="record.isNewCust">
            <a-tag class="noCopy tagstyle"> 新 </a-tag>
          </a-tooltip>
          <a-tooltip v-if="record.identificationType == 1" title="按新单制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              新 </span
            >&nbsp;
          </a-tooltip>
          <a-tooltip v-if="record.identificationType == 2" title="按返单有改制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              改 </span
            >&nbsp;
          </a-tooltip>
          <a-tag v-if="record.ka" class="noCopy tagstyle"> KA </a-tag>
          <a-tag v-if="!record.isFullSet" class="noCopy tagstyle"> 直 </a-tag>
          <a-tooltip :title="record.fileUploadedTime ? '重传文件时间:' + record.fileUploadedTime : ''" v-if="record.fileUploadedCount > 0">
            <span>
              <a-tag class="noCopy tagstyle" style="background: red; border-color: red"> 重 </a-tag>
              <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -2px">{{ record.fileUploadedCount }}</span>
            </span>
          </a-tooltip>
          <a-tooltip v-if="record.onLineEcnState > 0" :title="record.onLineOrRecordEcn == 2 ? '更改存档升级' : 'ECN在线改版'">
            <a-tag class="noCopy tagstyle"> 升 </a-tag>
          </a-tooltip>
          <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.isReOrder == 1">
            <a-tag class="noCopy tagstyle"> 改 </a-tag>
          </a-tooltip>
          <a-tag
            @click.stop="xiudisplay(record)"
            v-if="record.isOrderModify"
            class="noCopy tagstyle"
            style="background: #ff9900; border-color: #ff9900"
          >
            修
          </a-tag>
          <a-tooltip title="新MI ERP已通过" v-if="record.cymi == '1'">
            <a-tag class="noCopy tagstyle">MI</a-tag>
          </a-tooltip>
          <a-tooltip title="二次投产" v-if="record.backUserAccount > 0">
            <a-tag class="noCopy tagstyle"> 二 </a-tag>
          </a-tooltip>
          <span v-if="record.isEQ == 1 && record.statusType != '问客已回复' && record.statusType != '问客已审核' && record.statusType != '问客'">
            <a-tag class="noCopy tagstyle"> 问 </a-tag>
            <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -4px" v-if="record.eqNumber">{{ record.eqNumber }}</span>
          </span>
          <a-tooltip v-if="record.isFix" title="返修中">
            <a-tag class="noCopy tagstyle" @click.stop="RepairRecordClick(record)"> 修 </a-tag>
          </a-tooltip>
          <a-tooltip title="确认工作稿" v-if="record.confirmWorkingDraft">
            <a-tag class="noCopy tagstyle"> 确 </a-tag>
          </a-tooltip>
          <a-tooltip v-if="record.notfdzjhp">
            <template #title v-if="record.notfdzjhp">
              <div style="white-space: pre-line; font-size: 12px; line-height: 3ch">
                {{ record.notfdzjhp }}
              </div>
            </template>
            <a-icon type="file-text" style="color: #ff9900; font-size: 12px; margin: 0 0px" />
          </a-tooltip>
          <a-tag v-if="record.isJunG" class="noCopy tagstyle"> {{ record.joinFactoryId == 70 ? "J" : "军" }} </a-tag>
          <a-tag v-if="record.pauseCancelState == 2" class="noCopy tagstyle"> 取消 </a-tag>
          <a-tag v-if="record.pauseCancelState == 3" class="noCopy tagstyle"> 暂停 </a-tag>
          <a-tag v-if="record.pauseCancelState == 4" class="noCopy tagstyle"> 暂停取消 </a-tag>
          <a-tag v-if="record.isLock" class="noCopy tagstyle"> 锁 </a-tag>
          <a-tag v-if="record.smtFactoryId > 0" class="noCopy tagstyle"> 贴 </a-tag>
          <a-tooltip title="风险警告" v-if="record.riskWarning">
            <a-icon type="warning" style="color: #ff9900" class="noCopy" />
          </a-tooltip>

          <a-tag v-if="record.isReverse == '1' || record.isReverse == '2'" class="noCopy tagstyle"> 反 </a-tag>
        </span>
      </div>
      <span slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </span>
      <template slot="isProcessStepNotes" slot-scope="text, record">
        <a-button type="link" v-if="text.isProcessStepNotes" @click="lookProductRemark(record)" class="noCopy">查看</a-button>
      </template>
      <template slot="isCustRule" slot-scope="text, record">
        <a-tooltip title="客户规则" v-if="record.isCustRule == 1">
          <a-icon
            type="file-text"
            style="color: #ff9900; font-size: 18px; margin-right: 10px"
            @click.stop="CustomerRulesClick(record)"
            class="noCopy"
          />
        </a-tooltip>
      </template>
      <template slot="eqCostTime" slot-scope="text, record">
        <span v-if="record.eqCostTimeColor == 1" style="color: red">{{ record.eqCostTime }}</span>
        <span v-else-if="record.eqCostTime">{{ record.eqCostTime }}</span>
      </template>
      <div slot="process" slot-scope="text, record">
        <a-tooltip title="工艺流程">
          <a-icon type="file-text" style="color: #ff9900; font-size: 18px" @click.stop="orderClick1(record)" />
        </a-tooltip>
      </div>
      <template slot="action" slot-scope="text, record">
        <a-tooltip title="退单" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.BackOrder')">
          <a-icon type="rollback" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="ChargebackClick(record)" />
        </a-tooltip>
        <a-tooltip title="查看日志" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.BackOrder')">
          <a-icon type="bars" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="viewLogClick(record)" />
        </a-tooltip>
        <a-tooltip title="修改信息">
          <a-icon type="form" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="modifyInfoClick(record)" />
        </a-tooltip>
        <a-tooltip title="编辑参数">
          <a-icon type="edit" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="EditParametersClick(record, '1')" />
        </a-tooltip>
        <!-- <a-tooltip title="状态同步" v-if="checkPermission('MES.EngineeringModule.EngineeringProduction.EngineeringProductionStateSync')">
        <a-icon type="file-sync" style="color: #ff9900; font-size: 18px" @click.stop='StatusSynchronization(record)' />
      </a-tooltip> -->
        <a-tooltip title="系数修改">
          <a-icon type="highlight" style="color: #ff9900; font-size: 18px" @click.stop="getmultiples(record)" />
        </a-tooltip>
      </template>
      <template slot="action1" slot-scope="text, record">
        <a-tooltip title="工程指示">
          <a-icon type="container" class="noCopy" style="color: #ff9900; font-size: 18px" @click.stop="orderClick(record, record.proOrderId)" />
        </a-tooltip>
      </template>
      <template slot="jigsawPuzzle" slot-scope="text, record">
        <a @click="() => $emit('jigsawPuzzleClick', record)" v-if="text" class="noCopy">拼版图</a>
      </template>
      <template slot="customRender" slot-scope="text">
        <template>
          {{ text }}
        </template>
      </template>
      <template slot="down" slot-scope="text, record">
        <a class="noCopy" style="color: #428bca" v-if="record.pcbFilePath" @click.stop="down(record, record.pcbFilePath, 'orig')">下载</a>
      </template>
      <template slot="AllDataPath" slot-scope="text, record">
        <a class="noCopy" style="color: #428bca" v-if="record.joinFactoryId == 12" @click.stop="downAll(record)">下载</a>
      </template>
      <template slot="down1" slot-scope="text, record">
        <a class="noCopy" style="color: #428bca" @click.stop="down11(record)">下载</a>
      </template>
      <template slot="WorkFile" slot-scope="text, record">
        <a class="noCopy" v-if="record.workFilePath" style="color: #428bca" @click.stop="WorkFiledown(record)">下载</a>
      </template>
      <template slot="p2GStatus" slot-scope="text, record">
        <a-tooltip v-bind:title="record.tipInfo">
          <span>
            {{
              record.p2GStatus != undefined && record.p2GStatus.length > 0
                ? record.p2GStatus
                : record.designType != undefined && record.designType.length > 0 && record.designType != "null"
                ? record.designType
                : ""
            }}
          </span>
        </a-tooltip>
      </template>
    </a-table>
    <right-copy ref="RightCopy" />
    <a-modal title="修改内容" :width="1300" :visible="xiuvisible" destroyOnClose centered :maskClosable="false" @cancel="handleCancel">
      <div>
        <a-table
          class="xiu"
          :columns="columns4"
          :dataSource="dataSource4"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 400 }"
          :class="dataSource4.length ? 'min-table' : ''"
          :pagination="false"
          :loading="loading2"
        >
          <template slot="isPrintContract" slot-scope="record">
            <a-checkbox :checked="record.isPrintContract"></a-checkbox>
          </template>
          <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
            <template v-for="photo in record.filePath.split(',')">
              <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer :key="index + '-' + photo" />
            </template>
          </div>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="handleCancel">取消</a-button>
      </template>
    </a-modal>
    <!-- 操作日志弹窗 -->
    <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose :maskClosable="false" @cancel="handleCancel" :width="800" centered>
      <div class="projectackend">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
        </a-table>
      </div>
      <template slot="footer">
        <a-button type="primary" @click="Timenodes" style="float: left">{{ buttonname }}</a-button>
        <a-button type="primary" @click="handleCancel">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
import { proorderlog, proordercoefficient } from "@/services/projectDisptch";
import { checkPermission } from "@/utils/abp";
import { ordermodifylist } from "@/services/mkt/CustInfoNew";
import { proQuestLog } from "@/services/projectApi";
import { setEngineeringMake } from "@/utils/request";
import { downFile, datapath } from "@/services/projectMake";
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "30%",
    align: "left",
  },
  {
    title: "图片",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  components: {
    RightCopy,
  },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            current: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
          selectedRows: {},
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
    cookieId: {
      type: String,
      require: true,
    },
  },
  name: "LeftTableMake",
  data() {
    return {
      laborcolumns: [
        {
          title: "序号",
          align: "center",
          dataIndex: "index",
          key: "index",
          width: 20,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "操作时间",
          align: "left",
          dataIndex: "createTime",
          width: 65,
        },
        {
          title: "操作人",
          align: "left",
          dataIndex: "userName",
          width: 30,
        },
        {
          title: "内容",
          align: "left",
          dataIndex: "content",
          width: 185,
        },
      ],
      xstitle: "",
      labordataVisible: false,
      labordata: [],
      copylabordata: [],
      showText: false,
      dataSource4: [],
      xiuvisible: false,
      columns4,
      text: "",
      selectedRowKeysArray: [],
      loading2: false,
      selectedRowsData: [],
      activeClass: "smallActive",
      proOrderId: "",
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        // border: "1px solid #eee",
        zIndex: 99,
      },
      tit: "测试",
      buttonname: "显示全部节点",
    };
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let pauseCancelState = val.some(val => val.pauseCancelState == 4) ? 20 : 0;
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < val.length; i++) {
              let obj2 = val[i].orderNo;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0 && longestChars.length > 12) {
              this.columns[1].width = 115 + (longestChars.length - 12) * 8 + "px";
              this.columns[5].width = 130 - (longestChars.length - 12) * 2 + "px";
              this.columns[18].width = 120 - (longestChars.length - 12) * 2 + "px";
              this.columns[13].width = 190 - (longestChars.length - 12) * 2 + "px";
              this.columns[15].width = 110 - (longestChars.length - 12) * 2 + "px";
            }
            if (result >= 1 && longestChars.length > 12) {
              this.columns[1].width = 115 + (longestChars.length - 12) * 8 + result * 27 + pauseCancelState + "px";
              this.columns[5].width = 130 - result * 6 - (longestChars.length - 12) * 2 + "px";
              this.columns[18].width = 120 - result * 6 - (longestChars.length - 12) * 2 + "px";
              this.columns[13].width = 190 - result * 6 - (longestChars.length - 12) * 2 + "px";
              this.columns[15].width = 110 - result * 6 - (longestChars.length - 12) * 2 + "px";
            }
            if (result == 0 && longestChars.length <= 12) {
              this.columns[1].width = "115px";
              this.columns[5].width = "130px";
              this.columns[18].width = "120px";
              this.columns[13].width = "190px";
              this.columns[15].width = "110px";
            }
            if (result >= 1 && longestChars.length <= 12) {
              this.columns[1].width = 115 + result * 27 + pauseCancelState + "px";
              this.columns[5].width = 130 - result * 6 + "px";
              this.columns[18].width = 120 - result * 6 + "px";
              this.columns[13].width = 190 - result * 6 + "px";
              this.columns[15].width = 110 - result * 6 + "px";
            }
          });
        }
      },
    },
    pagination: {
      handler(val) {
        //console.log(val)
      },
    },
  },
  created() {
    // console.log('创建',this.pagination)
  },
  methods: {
    checkPermission,
    Timenodes() {
      if (this.buttonname == "显示全部节点") {
        this.buttonname = "显示关键节点";
        this.labordata = this.copylabordata;
      } else if (this.buttonname == "显示关键节点") {
        this.buttonname = "显示全部节点";
        this.labordata = this.copylabordata.length ? this.copylabordata.filter(item => item.keyNo == 1) : [];
      }
    },
    Coefficientdetails(id) {
      proordercoefficient(id, 0).then(res => {
        if (res.code) {
          let tit = [];
          let data = res.data[0].coefficientInfos;
          for (let index = 0; index < data.length; index++) {
            tit.push("【" + data[index].description + ": " + data[index].score + "】");
          }
          this.xstitle = tit.join(" + ");
        }
      });
    },
    OperationLog(record) {
      proorderlog(record.proOrderId).then(res => {
        if (res.code) {
          this.labordata = res.data.length ? res.data.filter(item => item.keyNo == 1) : [];
          this.copylabordata = JSON.parse(JSON.stringify(res.data));
          this.labordataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    xiudisplay(record) {
      this.loading2 = true;
      ordermodifylist(record.proOrderId)
        .then(res => {
          if (res.code) {
            this.dataSource4 = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
      this.xiuvisible = true;
    },
    handleCancel() {
      this.labordataVisible = false;
      this.buttonname = "显示全部节点";
      this.xiuvisible = false;
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.proOrderId == this.cookieId) {
        strGroup.push("cookieIdColor");
      }
      if (record.proOrderId && record.proOrderId == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },

    keydown(e) {
      // console.log('ctrlKey',e.ctrlKey,)
      this.shiftKey = e.ctrlKey;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.proOrderId);
            this.selectedRowKeysArray = keys;
            this.selectedRowsData = record;
            this.proOrderId = record.proOrderId;
          },
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    lookProductRemark(record) {
      this.$emit("getprocessstepnotes", record.orderNo);
    },
    // 工艺流程
    orderClick1(record) {
      this.$router.push({ path: "/gongcheng/technologicalProcess", query: { pdctno: record.orderNo, factoryId: record.joinFactoryId } });
    },
    orderClick(record, id) {
      if (record.status == "10" || record.status == "0") {
        this.$message.warning("当前订单状态不能进入工程指示");
        return;
      }
      //可进入工程指示时，做本地缓存
      this.$nextTick(function () {
        localStorage.setItem("pageCurrent2", this.pagination.current);
        localStorage.setItem("pageSize2", this.pagination.pageSize);
        localStorage.setItem("OrderId2", record.proOrderId);
        localStorage.setItem("record2", JSON.stringify(record));
        localStorage.setItem("stat2", true);
        localStorage.setItem("procust", true);
        this.$router.push({
          path: "/gongcheng/engineering",
          query: {
            OrderNo: record.orderNo,
            id: id,
            factory: record.joinFactoryId,
            typee: "2",
            isCustRule: record.isCustRule,
            custNo: record.custNo,
            fileUploadedCount: record.fileUploadedCount,
            businessOrderNo: record.businessOrderNo,
          },
        });
      });
    },
    handleTableChange(pagination) {
      this.$emit("tableChange", pagination);
    },
    // 退单
    ChargebackClick(record) {
      this.$emit("ChargebackClick", record);
    },
    // 查看日志
    viewLogClick(record) {
      this.$emit("viewLogClick", record);
    },
    // 修改信息
    modifyInfoClick(record) {
      this.$emit("modifyInfoClick", record);
    },
    // 编辑参数
    EditParametersClick(record, code) {
      this.$emit("EditParametersClick", record, code);
    },
    // 客户规则
    CustomerRulesClick(record) {
      // this.proOrderId = record.proOrderId
      this.$emit("CustomerRulesClick", record);
    },
    // 系数修改
    getmultiples(record) {
      this.$emit("getmultiples", record);
    },
    // 获取cookie缓存订单id
    getcookie(orderno) {
      //获取指定名称的cookie的值
      var arrstr = document.cookie.split("; ");
      for (var i = 0; i < arrstr.length; i++) {
        var temp = arrstr[i].split("=");
        if (temp[0] == orderno) {
          this.cookieId = unescape(temp[1]);
        }
      }
    },
    // 状态同步
    StatusSynchronization(record) {
      this.$emit("StatusSynchronization", record);
    },
    parseQueryParams(url) {
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);
      const queryParams = {};
      for (const [key, value] of params.entries()) {
        queryParams[key] = value;
      }
      return queryParams;
    },
    downorig(record, Path, type) {
      if (record.status == 10 && type == "orig") {
        this.$message.error("该订单状态不允许下载原稿！");
        return;
      }
      if (Path) {
        window.location.href = Path;
      }
    },
    downAll(record) {
      this.$emit("downAll", record);
    },
    down(record, Path, type) {
      if (record.status == 10 && type == "orig") {
        this.$message.error("该订单状态不允许下载原稿！");
        return;
      }
      if (!Path) {
        this.$message.error("暂无文件可下载");
        return;
      }
      if (Path.indexOf("myhuaweicloud") != -1) {
        let filetype = Path.substring(Path.lastIndexOf("."));
        let name = type == "orig" ? record.orderNo + "+" + record.pcbFileName + filetype : record.orderNo + ".zip";
        const xhr = new XMLHttpRequest();
        xhr.open("GET", Path, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = name;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
          }
        };
        xhr.send();
      } else {
        window.location.href = Path;
      }
    },
    down11(record) {
      // window.location.href = record.exportFile
      let params = {
        OrderNo: record.orderNo,
        Type: 0,
      };
      const BASE_URL = process.env.VUE_APP_API_BASE_URL;
      proQuestLog(params).then(res => {
        if (res.code) {
          if (res.data && res.data.filePath) {
            // let str = res.data.filePath
            // let newStr = BASE_URL + '/' + str
            // window.location.href = res.data.filePath
            var ordermodel1 = res.data.orderNo;
            const ordermodel = ordermodel1.toLowerCase().replace(/([a-z])([A-Z])/g, "$1-$2");
            let a = res.data.filePath.split(".").slice(-1)[0];
            const xhr = new XMLHttpRequest();
            xhr.open("GET", res.data.filePath, true);
            xhr.responseType = "blob";
            xhr.onload = function () {
              if (xhr.status === 200) {
                const blob = xhr.response;
                const link = document.createElement("a");
                link.href = window.URL.createObjectURL(blob);
                link.download = ordermodel + "." + a;
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }
            };
            xhr.send();
          } else {
            this.$message.error("文件不存在");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    WorkFiledown(record) {
      window.location.href = record.workFilePath;
    },
    RepairRecordClick(record) {
      this.$emit("RepairRecordClick", record);
    },
    webSocketLink(record) {
      if (record.statusType == "制作中" || record.statusType == "返修中") {
        this.$emit("webSocketLink", record.proOrderId);
      } else {
        this.$message.error("当前订单状态无法发送脚本");
      }
    },
  },
  mounted() {
    // this.getcookie('orderno')
  },
};
</script>

<style lang="less" scoped>
.tagstyle {
  font-size: 12px;
  background: #428bca;
  color: white;
  padding: 0 2px;
  margin: 0;
  margin-right: 3px;
  height: 21px;
  user-select: none;
  border: 1px solid #428bca;
}
.projectackend {
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
.xiu {
  /deep/.ant-table-row-cell-ellipsis,
  .ant-table-row-cell-ellipsis .ant-table-column-title {
    overflow: overlay;
    white-space: normal;
    text-overflow: inherit;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-content {
  border: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }
  .ant-table-fixed-left {
    .cookieIdColor {
      td:first-child {
        background: #ff9900 !important;
      }
    }
  }
  .rowBackgroundColor {
    background: #dfdcdc !important;
  }
  .displayFlag {
    display: none;
  }
}
.peopleTag {
  margin: 0;
  padding: 0;
  width: 24px;
  border-radius: 12px;
  background: #2d221d;
  border-color: #2d221d;
  color: #ff9900;
  text-align: center;
  margin-left: 2px;
}
/deep/.ant-table-row {
  height: 29px !important;
}
</style>
