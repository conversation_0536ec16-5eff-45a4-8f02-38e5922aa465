<!-- 工具管理- 叠层阻抗-钻孔信息 -->
<template>
  <div class="deillInfo">
    <a-collapse v-model="activeKey" expand-icon-position="right">
      <a-collapse-panel key="1" header="钻孔信息">
        <a-table
          :columns="columns"
          :data-source="_drillHoleTableData"
          bordered
          :pagination="false"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :customRow="onClickRow"
          :rowClassName="isRedRow"
        >
          <template v-for="col in ['drillName', 'startLayer', 'endLayer']" :slot="col" slot-scope="text, record">
            <div :key="col">
              <a-input
                v-if="record.editable"
                style="margin: -5px 0"
                :value="text"
                @change="e => handleChange(e.target.value, record.key, col, record)"
              />
              <template v-else>
                {{ text }}
              </template>
            </div>
          </template>
          <template slot="operation" slot-scope="text, record, index">
            <div class="editable-row-operations">
              <span v-if="record.editable">
                <a @click="() => save(record.key, record)">保存</a>
                <a-popconfirm title="Sure to cancel?" @confirm="() => cancel(record.key)">
                  <a @click="() => onDelete(record.key, record)">删除</a>
                </a-popconfirm>
              </span>
              <span v-else>
                <a :disabled="editingKey !== ''" @click="() => edit(index, record.key, record)">编辑</a>
              </span>
            </div>
          </template>
        </a-table>
        <a-tooltip slot="extra" placement="topLeft" title="增加一行" arrow-point-at-center>
          <a-icon type="plus" @click="handleAdd" />
        </a-tooltip>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script>
import { mapGetters, mapState, mapMutations } from "vuex";
const columns = [
  {
    title: "钻带名",
    dataIndex: "drillName",
    width: "25%",
    align: "center",
    scopedSlots: { customRender: "drillName" },
  },
  {
    title: "开始层",
    dataIndex: "startLayer",
    width: "15%",
    align: "center",
    scopedSlots: { customRender: "startLayer" },
  },
  {
    title: "结束层",
    dataIndex: "endLayer",
    width: "15%",
    align: "center",
    scopedSlots: { customRender: "endLayer" },
  },
  {
    title: "厚度",
    dataIndex: "drillThickness",
    width: "15%",
    align: "center",
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    scopedSlots: { customRender: "operation" },
  },
];
export default {
  props: {
    laminationObj: {
      type: Array,
    },
  },
  watch: {
    NewDataList: {
      handler: function (newV, oldV) {
        newV.forEach((newItem, index) => {
          const oldItem = oldV[index];
          for (let key in newItem) {
            if (key != "id" && key != "editable" && oldItem && newItem && newItem[key] != oldItem[key]) {
              console.log(`钻带信息发生了变化`);
              this.setedit(true);
            }
          }
        });
      },
      deep: true,
    },
  },
  data() {
    return {
      activeKey: ["1"],
      selectedRows: {},
      // data:[
      // ],
      columns,
      editingKey: "",
      selectid: "",
    };
  },
  mounted() {},
  computed: {
    NewDataList() {
      // 新的赋值
      return JSON.parse(JSON.stringify(this._drillHoleTableData));
    },
    ...mapState({ _drillHoleTableData: state => state.impedance._drillHoleTableData }),
    ...mapGetters({ form: "categoryForm" }),
  },
  methods: {
    ...mapMutations("setting", ["setedit"]),
    onClickRow(record, index) {
      return {
        on: {
          click: () => {
            this.selectedRows = record;
            this.$emit("changeDrillHoleTableData", record);
            this.selectid = index + 1;
          },
        },
      };
    },
    isRedRow(record, index) {
      let strGroup = [];
      if (index + 1 && this.selectid == index + 1) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    handleChange(value, key, column, record) {
      const newData = [...this._drillHoleTableData];
      let target = {};
      if (key) {
        target = newData.filter(item => key === item.key)[0];
      } else {
        target = newData.filter(item => record.id === item.id)[0];
      }
      if (target) {
        target[column] = value;
        this.$store.commit("drillHoleTableChange", newData);
      }
    },
    edit(index, key, record) {
      const newData = [...this._drillHoleTableData];
      if (key) {
        const target = newData.filter(item => key === item.key)[0];
        this.editingKey = key;
        if (target) {
          target.editable = true;
          this.$store.commit("drillHoleTableChange", newData);
        }
      } else {
        const target = newData.filter(item => record.id === item.id)[0];
        this.editingKey = key;
        if (target) {
          target.editable = true;
          this.$store.commit("drillHoleTableChange", newData);
        }
      }
    },
    save(key, record) {
      if (!record.drillName || !record.startLayer || !record.endLayer) {
        this.$message.warning("无名称及起始层不允许保存，请完善第" + key + "行参数");
        return;
      }
      if (
        Number(record.startLayer) > Number(this.form.layers) ||
        Number(record.endLayer) > Number(this.form.layers) ||
        Number(record.startLayer) == 0 ||
        Number(this.form.layers) == 0
      ) {
        this.$message.warning("0<起始层、结束层层数<" + this.form.layers + "，请修改第" + key + "行参数");
        return;
      }
      const newData = [...this._drillHoleTableData];
      if (key) {
        const target = newData.filter(item => key === item.key)[0];
        if (target) {
          let left = target.drillName.slice(0, 2);
          let right = target.drillName.slice(-2);
          if (target.drillName == "2nd") {
            target.drillType4Stack = "2";
          } else if (left == "dr" && target.startLayer == "1" && target.endLayer == this.form.layers) {
            target.drillType4Stack = "d";
          } else if (target.drillName == "drl") {
            target.drillType4Stack = "d";
          } else if (left == "dr" && this.form.layers == 0) {
            target.drillType4Stack = "d";
          } else if (left == "dr" && right != "ks") {
            target.drillType4Stack = "m";
          } else if (left == "ld") {
            target.drillType4Stack = "l";
          } else if (left == "ls") {
            target.drillType4Stack = "l";
          } else {
            target.drillType4Stack = null;
          }
          delete target.editable;
          this.$store.commit("drillHoleTableChange", newData);
        }
        this.editingKey = "";
      } else {
        const target = newData.filter(item => record.id === item.id)[0];
        if (target) {
          let left = target.drillName.slice(0, 2);
          let right = target.drillName.slice(-2);
          if (target.drillName == "2nd") {
            target.drillType4Stack = "2";
          } else if (left == "dr" && target.startLayer == "1" && target.endLayer == this.form.layers) {
            target.drillType4Stack = "d";
          } else if (target.drillName == "drl") {
            target.drillType4Stack = "d";
          } else if (left == "dr" && this.form.layers == 0) {
            target.drillType4Stack = "d";
          } else if (left == "dr" && right != "ks") {
            target.drillType4Stack = "m";
          } else if (left == "ld") {
            target.drillType4Stack = "l";
          } else if (left == "ls") {
            target.drillType4Stack = "l";
          } else {
            target.drillType4Stack = null;
          }
          delete target.editable;
          this.$store.commit("drillHoleTableChange", newData);
        }
        this.editingKey = "";
      }
    },
    onDelete(key, record) {
      const data = [...this._drillHoleTableData];
      if (key) {
        this.$store.commit(
          "drillHoleTableChange",
          data.filter(item => item.key !== key)
        );
        this.editingKey = "";
      } else {
        this.$store.commit(
          "drillHoleTableChange",
          data.filter(item => item.id !== record.id)
        );
        this.editingKey = "";
      }
      this.setedit(true);
      console.log("删除钻带");
    },
    cancel(key) {
      const newData = [...this._drillHoleTableData];
      const target = newData.filter(item => key === item.key)[0];
      this.editingKey = "";
    },
    handleAdd(event) {
      if (this.form.layers != "" && this.form.pdctno != "" && this.form.finishBoardThickness != "") {
        const { _drillHoleTableData } = this;
        const newData = {
          key: this._drillHoleTableData.length + 1 || 0,
          drillName: "",
          startLayer: "",
          endLayer: ``,
          editable: true,
          drillType4Stack: null,
        };
        this.$store.commit("drillHoleTableChange", [..._drillHoleTableData, newData]);
        event.stopPropagation();
      } else {
        event.stopPropagation();
        this.$message.error("请先完成参数信息");
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.rowBackgroundColor {
  background: #d1d0d0 !important;
}

.deillInfo {
  margin-top: 16px;
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n + 1) {
    background: #ffffff;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/ .ant-collapse-icon-position-right .ant-collapse-item {
    .ant-collapse-header {
      padding: 6px 18px;
      padding-right: 40px;
      color: #000000;
    }
    .ant-collapse-content-box {
      padding: 0;
    }
    .ant-table-thead > tr > th {
      padding: 5px 0;
      text-align: center;
      // font-weight: 500;
    }
    .ant-table-tbody > tr > td {
      padding: 0;
      height: 36px;
    }
  }
}
.editable-row-operations a {
  margin-right: 8px;
}
</style>
