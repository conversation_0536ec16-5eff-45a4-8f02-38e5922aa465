const avatars = [
  'https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png',
  'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
  'https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png',
  'https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png',
  'https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png',
  'https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png'
]

const positions = [
  {
    CN: 'Java工程师 | 蚂蚁金服-计算服务事业群-微信平台部',
    HK: 'Java工程師 | 螞蟻金服-計算服務事業群-微信平台部',
    US: 'Java engineer | Ant financial - Computing services business group - WeChat platform division'
  },{
    CN: '前端工程师 | 蚂蚁金服-计算服务事业群-VUE平台',
    HK: '前端工程師 | 螞蟻金服-計算服務事業群-VUE平台',
    US: 'Front-end engineer | Ant Financial - Computing services business group - VUE platform'
  },{
    CN: '前端工程师 | 蚂蚁金服-计算服务事业群-REACT平台',
    HK: '前端工程師 | 螞蟻金服-計算服務事業群-REACT平台',
    US: 'Front-end engineer | Ant Financial - Computing services business group - REACT platform'
  },{
    CN: '产品分析师 | 蚂蚁金服-计算服务事业群-IOS平台部',
    HK: '產品分析師 | 螞蟻金服-計算服務事業群-IOS平台部',
    US: 'Product analyst | Ant Financial - Computing services business group - IOS platform division'
  }
]

const admins = ['ICZER', 'JACK', 'LUIS', 'DAVID']

export {positions, avatars, admins}
