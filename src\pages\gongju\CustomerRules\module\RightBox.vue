<!-- 工具管理- 客户规则- 客规信息 右边 -->
<template>
  <div ref="SelectBox">
    <a-form >
      <a-row style="margin-top:10px;">
        <a-col :span="9">
          <a-form-item label="规则类型" :label-col="{ span:7 }" :wrapper-col="{ span: 16 }"   >
            <a-input  v-model="selectedRowsData.ruleSketch_" :disabled="editFlag" v-if="editFlag"/>
            <a-select allowClear showSearch optionFilterProp="lable" v-else v-model="selectedRowsData.ruleType_" :disabled="editFlag" 
            :getPopupContainer="()=>$refs.SelectBox" @change="ruleChange">          
              <a-select-option v-for="(item,index) in optionList" :key="index" :value="item.valueMember" :lable="item.text" >{{item.text}}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item  label="订单预审" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.isMktPre_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="3" >
          <a-form-item  label="工程制作" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.isMIMake_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="3" >
          <a-form-item  label="风险警告" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.riskWarning" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>        
        <a-col :span="3">
          <a-form-item label="自动规则" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}" >
            <a-checkbox  v-model="selectedRowsData.isAuto_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="3">          
          <a-form-item label="规则失效" :label-col="{ span: 16}" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.isAble_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- <a-row>
        <a-col :span="6">
          <a-form-item  label="订单预审" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.isMktPre_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="6" >
          <a-form-item  label="工程制作" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.isMIMake_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="6" >
          <a-form-item  label="风险警告" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.riskWarning" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>        
        <a-col :span="6">
          <a-form-item label="自动规则" :label-col="{ span: 16 }" :wrapper-col="{ span: 4}" >
            <a-checkbox  v-model="selectedRowsData.isAuto_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
        <a-col :span="6">          
          <a-form-item label="规则失效" :label-col="{ span: 14}" :wrapper-col="{ span: 4}">
            <a-checkbox v-model="selectedRowsData.isAble_" :disabled="editFlag" ></a-checkbox>
          </a-form-item>
        </a-col>
        
      </a-row> -->
      <div style="width:100%;margin-bottom:10px;" v-if="custItemList.length">
        <a-table
          :columns="columns"
          rowKey="id"
          :pagination="false"
          :scroll="{y:360}"
          :data-source="custItemList"
          :loading="TableLoading1"
          :customRow="onClickRow1"
          :rowClassName="isRedRow1"      
        >     
          <template slot="action" slot-scope="text,record">   
            <a-checkbox     @change="Valuechange"   v-model="record.value" :disabled="editFlag" v-if="record.dataType == 'bit'"  ></a-checkbox>       
            <a-input v-else  @blur="Valueblur()" v-model="record.value" style="width:120px;"  :disabled="editFlag" />
          </template>
        </a-table>
      </div>
      <a-row>
        <a-col >
          <a-textarea allowClear :rows="24" v-model="selectedRowsData.ruleDescribe_" v-if="!editFlag"  placeholder="规则描述" :auto-size="{ minRows: 10, maxRows: 10 }"/>  
          <div class="areastyle"  v-else>
            <span style="word-wrap: break-word;white-space:pre-line;">{{ selectedRowsData.ruleDescribe_ }}</span>
          </div>
        </a-col>
      </a-row>    
    </a-form>  
    <!-- <div style="display:flex;flex-wrap:wrap;width:100%;justify-content: flex-start;padding:0 40px;" >  
      <span  v-for="(item) in custItemList" :key="item.id" style="color:#000000;display:flex;justify-content: space-evenly;">
        {{item.caption}} :
        <a-checkbox  v-if="item.caption.indexOf('#')< 0" @change="Valuechange"   v-model="item.value" :disabled="editFlag"  style="margin-left:5px;margin-right:20px;"  ></a-checkbox>       
        <a-input v-else  v-model="item.value" @blur="Valueblur()" style="width:80px;height:24px;margin-left:5px;margin-right:20px;"  :disabled="editFlag" />
      </span>    
    </div> -->
    
  </div>
</template>

<script>
import {custClassItemList,
} from "@/services/CustRule";
import { cloneDeep } from "lodash";
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: "center",
    customRender: (text,record,index) => `${index+1}`,
    width: 50,
  },  
  {
    title: "规则类型",
    // width: 65,
    dataIndex: "caption",
    ellipsis: true,
    align: "left",
    className:'userStyle',
    // scopedSlots:{customRender:'caption'}
  },
  {
    title: "操作",
    dataIndex: "",
    align: "left",
    ellipsis: true,
    width:140,
    className:'userStyle',
    scopedSlots:{customRender:'action'}
  },
  

]
export default {
  props:{
    rightData: {
      type: Object,
      require: true,
      default: () => {}
    },
    editFlag:{
      type:Boolean,
    },
    optionList:{
      type:Array
    },
    code:{},
  },
  name: "RightBox",
  data() {
    return {
      selectedRowsData:{},
      custItemList:[],
      itemExt:[],
      columns,
      TableLoading1:false,
    }
  },
  watch:{
    'rightData':{
      handler(val,old){      
        this.selectedRowsData = cloneDeep(val)
        // if(JSON.stringify(val) !== '{}'){        
        //   this.itemExt = val.itemExt?.split(',')
        //   if(this.selectedRowsData.ruleType_){
        //     this.custItem1(this.selectedRowsData.ruleType_)
        //   }
        // }else{
        //   this.custItemList = []          
        // }
               
      }
    },   
    editFlag(val){    
      // console.log('editFlag',this.editFlag)
        // if(!val && this.code == 2){
        //   this.custItem(this.selectedRowsData.ruleType_)
        // }    
    },
    code(val){    
      // console.log('editFlag',this.editFlag)
        if(val == 1){
          this.custItemList = []           
          // this.selectedRowsData.ruleSketch_ = ''
          // this.selectedRowsData.isMktPre_ = false
          // this.selectedRowsData.isMIMake_ = false
          // this.selectedRowsData.riskWarning = false
          // this.selectedRowsData.isAuto_ = false
          // this.selectedRowsData.isAble_ = false
          // this.selectedRowsData.ruleDescribe_ = ''
        }    
    },
  },

  created() {

  },
  methods: {
    updateParentValue() {
      // 对局部变量进行更改
      this.selectedRowsData = "new value";
      // 将更改后的局部变量通过事件传递给父组件
      this.$emit("update:value", cloneDeep(this.selectedRowsData));
    },
    ruleChange(){
      // if(this.selectedRowsData.ruleType_){
      //   this.custItem(this.selectedRowsData.ruleType_)
      // }
    },
    Valuechange(){
      console.log('custItemList',this.custItemList)
      this.$forceUpdate()     
    },
    Valueblur(value){
      this.$forceUpdate()
    },
    custItem1(ruleType_){
      custClassItemList(ruleType_).then(res=>{
        if(res.code){
          this.custItemList = res.data       
          if(this.itemExt.length && this.code != 1){
            for(var i=0;i<this.itemExt.length;i++){              
              for(var a=0;a<this.custItemList.length;a++){
                if(this.custItemList[a].id == this.itemExt[i].split(':')[0]){
                  if(this.custItemList[a].dataType == 'bit'){
                    this.custItemList[a].value = this.itemExt[i].split(':')[1] =='true'? true:false
                  }else{
                    this.custItemList[a].value = this.itemExt[i].split(':')[1]
                  }
                }
              }
            }
          } 
          this.custItemList = this.custItemList.filter(item=>{return item.value})        
          // console.log('this.custItemList11',this.custItemList)
        }else{
          this.$message.error(res.message)
        }
      })
    },     
    custItem(ruleType_){
      custClassItemList(ruleType_).then(res=>{
        if(res.code){
          this.custItemList = res.data
          if(this.itemExt.length && this.code == 2){
            for(var i=0;i<this.itemExt.length;i++){              
              for(var a=0;a<this.custItemList.length;a++){
                if(this.custItemList[a].id == this.itemExt[i].split(':')[0]){
                  if(this.custItemList[a].dataType == 'bit'){
                    this.custItemList[a].value = this.itemExt[i].split(':')[1] =='true'? true:false
                  }else{
                    this.custItemList[a].value = this.itemExt[i].split(':')[1]
                  }
                }
              }
            }

          }else{
            for(var a1=0;a1<this.custItemList.length;a1++){            
              if(this.custItemList[a1].dataType == 'bit'){
                this.custItemList[a1].value = false
              }else{
                this.custItemList[a1].value = ''
              }
            }
          }         
          // console.log('this.custItemList',this.custItemList)
        }else{
          this.$message.error(res.message)
        }
      })
    }, 
     // 订单详情列表点击事件
     isRedRow1(record){
      // let strGroup = []
      // let str =[]
      // if (record.guid_ && record.guid_ == this.selectedRowKeysArray[0]) {
      //   strGroup.push('rowBackgroundColor')
      // }
      // return str.concat(strGroup)
    },    
    onClickRow1(record) {
      return {
        on: {
          click: () => {
            // if(this.editFlag){
            //   let keys = [];
            //   keys.push(record.guid_);
            //   this.selectedRowKeysArray = keys;  
            //   this.selectedRowsData = record           
            //   this.rightData = this.selectedRowsData             
            //   this.getAttInfo(record.guid_)             
            // }else{
            //   this.$message.warning('编辑状态不可选择其他订单')
            // }
          },

        }
      }
    },  
  },
  
}
</script>


<style scoped lang="less">
.areastyle{
  border: 2px solid rgb(221, 221, 221);
    width: 800px;
    height: 200px;
    overflow: auto;
    margin-left: 10px;
    border-radius: 5px;
}
/deep/.userStyle{
  padding:0 4px!important;
}
/deep/.ant-col-6{
  position: relative;
  // left: 32px;
  width: 17%;
}
/deep/.ant-col-7{
  position: relative;
  // left: 32px;
  width: 23%;
}
/deep/.ant-col-3 {
  width: 12%;
}
 /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
          background: #dfdcdc !important;
        }
/deep/.ant-table-thead > tr > th{
  border: 1px solid #f0f2f5;
}
/deep/ .ant-table-tbody > tr > td{
  border: 1px solid #f0f2f5;
}
/deep/.ant-table .ant-table-tbody > tr > td{
  padding: 6.75px 4px ;
  border-color: #f0f0f0;
}

/deep/.ant-pagination-simple .ant-pagination-simple-pager input{
  font-weight: 500;
}
/deep/.ant-pagination-options{
  margin-left: 5px;
}
/deep/.ant-pagination{
  width: 767px;
  bottom: -2px;
  position: relative;
  left: 528px;
}
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
}

/deep/.ant-input{
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
.projectMake {
  height: 814px;
  min-width: 1670px;
  // width: 100%;
  // display: flex;
  background: #FFFFFF;
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  /deep/.leftContent {
    height:764px;
    width: 200px;
    user-select: none;
    .min-table {
      .ant-table-body{
        min-height:730px;
      }
    }
    //.ant-table-body{
    //  .ant-table-fixed{
    //    width: 500px !important;
    //  }
    //}
    width:15%;
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
     .tabRightClikBox{
      border:2px solid rgb(238, 238, 238) !important;
      li{
        height:30px;
        line-height:30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
      }
      .ant-menu-item:not(:last-child){
        margin-bottom: 4px;
      }
    }

  }
  /deep/ .userStyle{
      user-select: all !important;
    }
  .rightContent {
    border-bottom: 0;
    width: 85%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
   /deep/ .leftBox {
      width: 50%;
      height: 764px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
     .top{
       height:350px;
     }
     .bto{
       height:350px;
     }
    }
    .rightBox {
      padding: 4px;
      width: 50%;
      height: 764px;
      border: 2px solid rgb(233, 233, 240);
      border-bottom: 4px solid rgb(233, 233, 240);
      .ant-form-item{
        margin-bottom:10px!important;
      }
    }
  }
  .footerAction {
      width: 100%;
      height:56px;
      border: 2px solid #E9E9F0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      background: #FFFFFF;
    }
    //.leftTable {
    //  width: 60%;
    //  border: 2px solid rgb(233, 233, 240)
    //}

  /deep/ .ant-tabs {
    .ant-tabs-bar {
      display:none;
    }
    // .ant-tabs-nav-container{
    //   height: 28px;
    //   .ant-tabs-tab {
    //     margin: 0;
    //     height: 28px;
    //     line-height: 28px;
    //     width:302px;
    //     text-align: center;
    //   }
    // }

  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #DC143C;
        }
      }
      .eqBackground {
        background: #FFFF00;
      }
      .statuBackground {
        background: #B0E0E6;
      }
      .backUserBackground {
        background: #A7A2C9;
      }

    }
    .ant-table-row-selected {
      td {
        background: #aba5a5;
      }
    }
    .userStyle{
      user-select: all !important;
    }

  }

  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
    border-left:2px solid #ff9900!important;
  }
  /deep/ .ant-table{

    .ant-table-thead > tr > th{
      padding: 3px 4px;
      border-color: #f0f0f0;
    }
    .ant-table-tbody > tr > td {
      padding: 3px 4px;
      border-color: #f0f0f0;
    }
    tr.ant-table-row-selected td {
     background: #aba5a5;
    }
    tr.ant-table-row-hover td {
     background: #aba5a5;
    }
    .rowBackgroundColor {
      background: #dfdcdc!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }




}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #F0F2F5;
  }
</style>
<style lang="less">
.note {
  .textNote {
    background: #D6D6D6;

    p {
      line-height: 35px;
      font-weight: 700;
      margin: 0;
      img{
        width:100%;
      }
    }
  .displayFlag {
      display: none;

    }
  }
}
.modalSty {
  /deep/.ant-modal .ant-modal-content .ant-modal-body {

    padding: 0;
  }
}
</style>
