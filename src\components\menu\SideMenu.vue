<template>
  <a-layout-sider :theme="sideTheme" :class="['side-menu', 'beauty-scroll', isMobile ? null : 'shadow']" width="200px" :collapsible="collapsible" v-model="collapsed" :trigger="null">
    <div :class="['logo', theme,collapsed?'aa':'bb']" >
      <router-link to="/dashboard/analysis">
        <img style='width:24px;' src="@/assets/img/bn2.png">
        <!-- <h1 style="margin:0 14px;font-size:20px;">百能</h1> -->
        <h1 style="color: white;font-weight: 500;font-size: 20px;" >{{systemName}}</h1>
      </router-link>
    </div>
    <i-menu :theme="theme" :collapsed="collapsed" :options="menuData" @select="onSelect" class="menu" @toggleCollapse="toggleCollapse"/>
  </a-layout-sider>
</template>

<script>
import IMenu from './menu'
import {mapState,mapMutations} from 'vuex'
export default {
  name: 'SideMenu',
  components: {IMenu},
  props: {
    collapsible: {
      type: Boolean,
      required: false,
      default: false
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    menuData: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
  },
  computed: {
    sideTheme() {
      return this.theme == 'light' ? this.theme : 'dark'
    },
    ...mapState('setting', ['isMobile', 'systemName'])
  },
  methods: {
    ...mapMutations('setting', ['setload']),
    onSelect (obj) {
      this.setload(false)
      this.$emit('menuSelect', obj)
    },
    toggleCollapse(val){
      this.$emit('toggleCollapse',val)
    }
  },
 
}
</script>

<style lang="less" scoped>
/deep/.aa{
  width:80px;
  img{
    margin:0 27px!important;
  }
 
}
/deep/.bb{
  width:200px;
  img{
    margin:0 10px!important;
  }
}
/deep/.ant-menu{
  color:#000000;
}
/deep/.ant-menu-item > a{
  color:#000000;  
}
/deep/.ant-menu-item-selected > a{
  color:#ff9900!important;
}
@import "index";
</style>
