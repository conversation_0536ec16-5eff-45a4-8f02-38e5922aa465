<!-- 市场管理 - 订单报价- 查询-->
<template>
  <a-modal
    title="查询"
    :width="500"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :mask="false" 
    :maskClosable="false"
    centered
    >
  <a-form-model  :modal="form" >
    <a-form-model-item label="订单号：" :labelCol="{span: 5}" :wrapperCol="{span: 17, }">
      <a-input v-model="form.custItem_"  :autoFocus='autoFocus'  allowClear/>
    </a-form-model-item>
  </a-form-model>
  </a-modal>
</template>

<script>
export default {
    name:'QueryInfo',
    
  data() {
    return {      
      autoFocus:true,
      visible:false,
      form:{
        custItem_:'',          
      }
    };
  },
  methods: { 
    openModal(model) {
      this.visible = true;
    },
    handleCancel(){
      this.visible = false;      
    },
    handleOk(){
      this.visible = false; 
      this.$emit('queryok')       
    }
  
  },
  directives: {
  'focusNextOnEnter':{
    bind: function(el, {
      value
    }, vnode) {
      el.addEventListener('keyup', (ev) => {
        if (ev.keyCode === 13) {
          let nextInput = vnode.context.$refs[value]
          if (nextInput && typeof nextInput.focus === 'function') {
            nextInput.focus()
            nextInput.select()
          }
        }
      })
    }
  }
},
};
</script>
<style scoped lang="less">
/deep/.ant-form-item .ant-form-item-label{
  border:none !important;
  background-color: white !important;
}
/deep/.ant-form-item .ant-form-item-control-wrapper .ant-form-item-control{
  border:none !important;
}
/deep/.ant-form{
  border:none !important
}
</style>