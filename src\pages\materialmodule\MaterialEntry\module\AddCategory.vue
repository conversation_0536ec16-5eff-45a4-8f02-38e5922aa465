<!-- 物料管理-物料录入-添加类别 -->
<template>
  <div ref="SelectBox">
  <a-form :label-col="{ span:7 }" :wrapper-col="{ span: 14}" >
    <a-form-item label="物料类别" class="required">
      <a-select  v-model='queryForm.type_' :getPopupContainer="()=>this.$refs.SelectBox"  showSearch allowClear optionFilterProp="lable"  >
        <a-select-option key="" value=934>板材类别</a-select-option>
        <a-select-option key="" value=935>PP类别</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="CORE类别名称" v-if="queryForm.type_ ==934" class="required">
      <a-input   v-model='queryForm.coreType_' allowClear/>
    </a-form-item>
    <a-form-item label="对应PP类别名称" v-if="queryForm.type_ ==934" class="required">
      <a-select v-model='queryForm.ppTypeCodes_' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear
      :getPopupContainer="()=>this.$refs.SelectBox" >
        <a-select-option v-for="(item,index) in pptype" :key="index" :value="item.valueMember">{{item.text}}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="产品类别" v-if="queryForm.type_ ==934" class="required"> 
      <a-select v-model='queryForm.Category_' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear
      :getPopupContainer="()=>this.$refs.SelectBox">
        <a-select-option v-for="(item,index) in producttype" :key="index" :value="item.category_">{{item.category_}}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="TG值" v-if="queryForm.type_ ==934" class="required">
      <a-select v-model='queryForm.TGValue_' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear
      :getPopupContainer="()=>this.$refs.SelectBox">
        <a-select-option v-for="(item,index) in tgtype" :key="index" :value="item.tgValue_">{{item.tgValue_}}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="供应商" v-if="queryForm.type_ ==934" class="required">
      <a-select v-model='queryForm.VendorCodes_' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear
      :getPopupContainer="()=>this.$refs.SelectBox">
        <a-select-option v-for="(item,index) in materialCategory2" :key="index" :value="item.id">{{item.verdorName_}}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="排序" v-if="queryForm.type_ ==934">
      <a-input   v-model='queryForm.coreDisplay_' allowClear/>
    </a-form-item>
    <a-form-item label="层数" v-if="queryForm.type_ ==934" >
      <a-input   v-model='queryForm.Layers' placeholder="格式:x-x 例如1-99" allowClear/>
    </a-form-item>
    <a-form-item label="工厂" v-if="queryForm.type_ ==934" class="required">
      <a-select v-model='queryForm.JoinFactoryId' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear
      :getPopupContainer="()=>this.$refs.SelectBox">
      <a-select-option  v-for="(item,index) in mapKey(factory)" :key="index" :value="item.value" :lable="item.lable" :title="item.lable">
          {{ item.lable }}
      </a-select-option>
      </a-select>
    </a-form-item>
    <!-- 物料类别:pp类别 -->
    <a-form-item label="PP类别名称" v-if="queryForm.type_ ==935" class="required">
      <a-input   v-model='queryForm.ppType_' allowClear/>
    </a-form-item>
    <a-form-item label="供应商" v-if="queryForm.type_ ==935" class="required">
      <a-select v-model='queryForm.VendorCodes_' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear
    :getPopupContainer="()=>this.$refs.SelectBox">
        <a-select-option v-for="(item,index) in materialCategory2" :key="index" :value="item.id">{{item.verdorName_}}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="工厂" v-if="queryForm.type_ ==935" class="required">
      <a-select v-model='queryForm.JoinFactoryId' showSearch option-filter-prop="children" :filter-option="filterOption" allowClear
      :getPopupContainer="()=>this.$refs.SelectBox">
      <a-select-option  v-for="(item,index) in mapKey(factory)" :key="index" :value="item.value" :lable="item.lable" :title="item.lable">
          {{ item.lable }}
      </a-select-option>
      </a-select>
    </a-form-item>
  </a-form>
  </div>
  
</template>

<script>
export default {
    name:'AddCategory',
    props:['pptype','tgtype','materialCategory2','producttype','factory',],
  data() {
    return {
      queryForm:{},
      autoFocus:true
    };
  },
  cerated(){
      console.log('1212',this.test)
  },
  methods: {
    filterOption(input, option) {
  return (option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0);
  }, 
  mapKey(data){
      if (!data) {
        return []
      } else {
        return Object.keys(data).map(item => {
          return {'value':item, 'lable': data[item]}
        })
      }
    },
  },
};
</script>

<style scoped lang="less">
.required{
  /deep/.ant-form-item-label > label{
    color: red;
  }
}
/deep/.ant-input{
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
 color:#000000;
}
.ant-modal-body{
    .ant-form-item {
  margin-bottom: 10px;
}
}

// /deep/  .ant-modal-content{
//   min-height: 500px !important;
// }

</style>