<!-- 车间管理-V-cut管理 -->
<template>
  <div class="Vcutmanagement">
    <a-spin :spinning="spinning">
      <div class="content">
        <div class="left" ref="letfDom" style="width: 65%">
          <a-card :bordered="false" style="border: 1px solid #e9e9f0" @contextmenu.prevent="rightClick($event)">
            <vxe-table
              border
              stripe
              show-overflow
              :scroll-y="{ enabled: true, gt: 20 }"
              :height="left1height"
              :loading="table1Loading"
              :row-config="{ isCurrent: true, isHover: true }"
              @cell-click="cellClickEvent"
              :menu-config="{ enabled: true }"
              @cell-menu="cellContextMenuEvent"
              :data="data1Source"
            >
              <vxe-column type="seq" title="序号" width="50" fixed="left" style="text-align: center" align="center"></vxe-column>
              <vxe-column field="orderNo" title="生产型号" fixed="left" show-overflow width="150">
                <template #default="{ row }">
                  {{ row.orderNo }}&nbsp;
                  <a-icon type="thunderbolt" theme="filled" v-if="row.isUrgent" style="color: #ff9900"></a-icon>
                </template>
              </vxe-column>
              <vxe-column field="craftKey_" title="工序" width="60" show-overflow></vxe-column>
              <vxe-column field="lSize_" title="长" width="60" show-overflow></vxe-column>
              <vxe-column field="wSize_" title="宽" width="60" show-overflow></vxe-column>
              <vxe-column field="totalNum" title="PNL数" width="60" show-overflow></vxe-column>
              <vxe-column field="area_" title="面积" width="60" show-overflow></vxe-column>
              <vxe-column field="boardThickness" title="板厚" width="50" show-overflow></vxe-column>
              <vxe-column field="orderFactoryKey" title="订单工厂" width="90" show-overflow></vxe-column>
              <vxe-column field="deliveryDate" title="交货日期" width="70" show-overflow></vxe-column>
              <vxe-column field="createTime" title="到序时间" width="100" show-overflow></vxe-column>
              <vxe-column field="beOverdue" title="逾期天数" width="80" show-overflow></vxe-column>
              <vxe-column field="startDate_" title="机台派单" width="100" show-overflow></vxe-column>
              <vxe-column field="finishTime" title="加工完成时间" width="100" show-overflow></vxe-column>
              <vxe-column field="isBigCus" title="KA" width="80" show-overflow>
                <template #default="{ row }">
                  <a-checkbox :checked="row.isBigCus"></a-checkbox>
                </template>
              </vxe-column>
              <vxe-column field="note" title="生产备注" width="200" show-overflow></vxe-column>
            </vxe-table>
            <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
              <a-menu-item @click="down">下载</a-menu-item>
              <a-menu-item>
                <a-upload accept=".tgz" name="file" :before-upload="beforeUpload" :customRequest="httpRequest"> 上传tgz </a-upload>
              </a-menu-item>
              <a-menu-item>
                <a-upload accept=".mnf2" name="file" :before-upload="beforeUploadMnf2" :customRequest="httpRequestMnf2"> 上传Mnf2 </a-upload>
              </a-menu-item>
            </a-menu>
          </a-card>
          <a-card :bordered="false" style="border: 1px solid #e9e9f0" @contextmenu.prevent="rightClick1($event)">
            <vxe-table
              border
              stripe
              show-overflow
              :scroll-y="{ enabled: true, gt: 20 }"
              :height="left2height"
              :loading="table2Loading"
              :row-config="{ isCurrent: true, isHover: true }"
              @cell-click="cellClickEvent1"
              :menu-config="{ enabled: true }"
              @cell-menu="cellContextMenuEvent1"
              :data="data2Source"
            >
              <vxe-column type="seq" title="序号" width="50" fixed="left" style="text-align: center" align="center"></vxe-column>
              <vxe-column field="orderNo" title="生产型号" fixed="left" show-overflow width="150"></vxe-column>
              <vxe-column field="craftKey_" title="工序" width="60" show-overflow></vxe-column>
              <vxe-column field="lSize_" title="长" width="60" show-overflow></vxe-column>
              <vxe-column field="wSize_" title="宽" width="60" show-overflow></vxe-column>
              <vxe-column field="totalNum" title="PNL数" width="60" show-overflow></vxe-column>
              <vxe-column field="area_" title="面积" width="60" show-overflow></vxe-column>
              <vxe-column field="boardThickness" title="板厚" width="50" show-overflow></vxe-column>
              <vxe-column field="orderFactoryKey" title="订单工厂" width="90" show-overflow></vxe-column>
              <vxe-column field="deliveryDate" title="交货日期" width="70" show-overflow></vxe-column>
              <vxe-column field="createTime" title="到序时间" width="100" show-overflow></vxe-column>
              <vxe-column field="beOverdue" title="逾期天数" width="100" show-overflow></vxe-column>
              <vxe-column field="startDate_" title="派单时间" width="120" show-overflow></vxe-column>
              <vxe-column field="machineCount_" title="机台数" width="80" show-overflow></vxe-column>
              <vxe-column field="isBigCus" title="KA" width="50" show-overflow>
                <template #default="{ row }">
                  <a-checkbox :checked="row.isBigCus"></a-checkbox>
                </template>
              </vxe-column>
            </vxe-table>
            <a-menu :style="menuStyle" v-show="menuVisible1" class="tabRightClikBox">
              <a-menu-item @click="down">下载</a-menu-item>
            </a-menu>
          </a-card>
        </div>
        <div class="right" style="width: 35%">
          <cutting-center
            :tableData="data3Source"
            :table3Loading="table3Loading"
            :machineStatuList="data4Source"
            :machineStatuLoad="table4Loading"
            :drillList="data5Source"
            :dispatchLoad="table6Loading"
            :dispatchList="data6Source"
            @getDispatchFallback="getDispatchFallback"
            @getAssignmentList="getAssignmentList"
            @getMesaList="getMesaList"
            @clearlist="data6Source = []"
            ref="cuttingCenter"
            :idList="idList"
          ></cutting-center>
        </div>
      </div>
      <div class="footer">
        <div class="actionBox">
          <cutting-action
            @Editingmachine="Editingmachine"
            @finishorder="finishorder"
            @Productioncompleted="Productioncompleted"
            @handleDispatchMachine="handleDispatchMachine"
            @Scancodetodispatch="Scancodetodispatch"
            @SetUpExpeditingClick="SetUpExpeditingClick"
            @ExceptionRemarksClick="ExceptionRemarksClick"
            @queryClick="queryClick"
            :btnloading1="btnloading1"
            :btnloading2="btnloading2"
          ></cutting-action>
        </div>
      </div>
      <!--扫码分派-->
      <a-modal
        title="扫码分派"
        :visible="scancodeVisible"
        @cancel="scancodeVisible = false"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :destroyOnClose="true"
        centered
      >
        <a-form-item label="扫码分派" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
          <a-input v-model="scancode" :autoFocus="true" @keyup.enter="scancodeOk" />
        </a-form-item>
        <template #footer>
          <a-button key="back" @click="scancodeVisible = false">取消</a-button>
        </template>
      </a-modal>
      <!--修改机台信息-->
      <a-modal
        title="修改机台信息"
        :visible="machineVisible"
        @cancel="machineVisible = false"
        centered
        @ok="machinehandleOk"
        :confirmLoading="machineload"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="700"
      >
        <machine :machinedata="machinedata"></machine>
      </a-modal>
      <!-- 生产备注弹窗 -->
      <a-modal
        title="生产备注"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        centered
        :confirmLoading="confirmLoading"
      >
        <exception-remarks-info ref="exceptionRemarksInfo" />
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk5"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        centered
      >
        <query-info ref="queryInfo" />
      </a-modal>
      <!--确认弹窗-->
      <a-modal
        title="确认弹窗"
        :visible="qrdataVisible"
        @cancel="qrdataVisible = false"
        centered
        @ok="qrhandleOk"
        ok-text="确定"
        :confirmLoading="btnloading2"
        destroyOnClose
        :maskClosable="false"
        :width="400"
      >
        <span>{{ messagelist }}</span>
      </a-modal>
    </a-spin>
  </div>
</template>
<script>
import { processorder } from "@/services/management";
import {
  getDispatchList,
  getDoingOrderList,
  getMachineList,
  getStatisticsList,
  getWaitOrderList,
  getDispatchMachineList,
  getDispatchFallbackList,
  SetUpExpediting,
  ExceptionRemarks,
  getOrderMuIdList,
  downLoad,
  byid,
  UploadFile1,
  orderFileUpload,
  processfinishorder,
  allfinishorder,
  updatemachineinfo,
} from "@/services/management";
import { Personnelregistration, orderFileUploadMnf2 } from "@/services/fly-management";
import CuttingCenter from "./module/CuttingCenter";
import CuttingAction from "./module/CuttingAction";
import ExceptionRemarksInfo from "./module/ExceptionRemarksInfo";
import QueryInfo from "./module/QueryInfo";
import machine from "@/pages/productionManagement/machine";
export default {
  name: "",
  components: { CuttingCenter, CuttingAction, ExceptionRemarksInfo, QueryInfo, machine },
  inject: ["reload"],
  data() {
    return {
      left1height: "",
      left2height: "",
      spinning: false,
      machineVisible: false,
      machineload: false,
      machinedata: {},
      isCtrlPressed: false,
      loading: false,
      table1Loading: false, // 待分派表格load
      table2Loading: false, // 已分派表格load
      table3Loading: false, // 机台表格load
      table4Loading: false, // 机台状态load
      table6Loading: false, // 分派状态load
      data1Source: [], // 待分派集合
      data2Source: [], // 已分派集合
      data3Source: [], // 机台集合
      data3SourceCopy: [], // 暂存机台集合
      data4Source: [], // 机台状态集合
      data5Source: [], // 钻刀集合
      data6Source: [], // 分派集合
      selectedRowList: [],
      selectedRowList1: [],
      selectedRows1: {},
      selectedRows: [],
      selectedRowsPnts: "",
      selectedRowsPdctno: "",
      assignMachineList: [],
      rowId1: "",
      dataVisible2: false, //涨缩登记弹窗不显示
      dataVisible3: false, //生产备注弹窗不显示
      dataVisible4: false, //查询弹窗
      qrdataVisible: false,
      modaltype: "",
      messagelist: "",
      quantity: "",
      quantityCopy: "",
      orderNo: "",
      confirmLoading: false,
      note: "",
      idList: [],
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      menuVisible: false,
      menuVisible1: false,
      menuData: [],
      btnloading1: false, // 分派按钮loading
      btnloading2: false, // 设置加急按钮loading
      agvID: "",
      scancodeVisible: false,
      scancode: "",
    };
  },
  methods: {
    handleResize() {
      if ((window.innerHeight - 140) / 2 <= 390) {
        this.left1height = (window.innerHeight - 140) / 2;
        this.left2height = (window.innerHeight - 140) / 2;
      } else {
        this.left1height = "390";
        this.left2height = "390";
      }
    },
    //编辑机台信息
    Editingmachine() {
      if (this.$refs.cuttingCenter.selectedRowKeys.length == 0) {
        this.$message.error("请选择需要编辑的机台");
        return;
      }
      if (this.$refs.cuttingCenter.selectedRowKeys.length > 1) {
        this.$message.error("只能选择一个机台进行编辑");
        return;
      }
      byid(this.$refs.cuttingCenter.selectedRowKeys[0]).then(res => {
        if (res.code) {
          this.machinedata = res.data;
          this.machineVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    machinehandleOk() {
      var x = /^\+?[0-9][0-9]*$/;
      let inttype = ["accCount_", "num_", "tableNo_", "display_"];
      if (this.machinedata.tableNo_ && !x.test(this.machinedata.tableNo_)) {
        this.$message.error("台面数请输入正确格式");
        this.$refs.tableNo_.focus();
        this.$refs.tableNo_.select();
        return;
      }
      if (this.machinedata.num_ && !x.test(this.machinedata.num_)) {
        this.$message.error("轴个数请输入正确格式");
        this.$refs.num_.focus();
        this.$refs.num_.select();
        return;
      }
      if (this.machinedata.accCount_ && !x.test(this.machinedata.accCount_)) {
        this.$message.error("允许订单数上限请输入正确格式");
        this.$refs.accCount_.focus();
        this.$refs.accCount_.select();
        return;
      }

      if (this.machinedata.display_ && !x.test(this.machinedata.display_)) {
        this.$message.error("排序请输入正确格式");
        this.$refs.display_.focus();
        this.$refs.display_.select();
        return;
      }
      inttype.forEach(key => {
        this.machinedata[key] = this.machinedata[key] ? Number(this.machinedata[key]) : 0;
      });
      this.machineload = true;
      updatemachineinfo(this.machinedata)
        .then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.getMesaList();
            this.getAssignmentList(this.$refs.cuttingCenter.rowId);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.machineVisible = false;
          this.machineload = false;
        });
    },
    // 设置加急
    SetUpExpeditingClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.messagelist = "【" + this.menuData.orderNo + "】确认设置/取消加急该订单吗?";
      this.modaltype = "urgent";
      this.qrdataVisible = true;
    },
    //完成
    finishorder() {
      if (JSON.stringify(this.selectedRows1) == "{}") {
        this.$message.warning("请选择订单");
        return;
      }
      this.qrdataVisible = true;
      this.messagelist = "【" + this.selectedRows1.orderNo + "】确认完成该订单吗?";
      this.modaltype = "finish";
    },
    //生产完成
    Productioncompleted() {
      if (JSON.stringify(this.selectedRows1) == "{}") {
        this.$message.warning("请选择订单");
        return;
      }
      this.qrdataVisible = true;
      this.messagelist = "【" + this.selectedRows1.orderNo + "】确认该订单生产完成吗?";
      this.modaltype = "Production";
    },
    qrhandleOk() {
      this.btnloading2 = true;
      this.qrdataVisible = false;
      this.spinning = true;
      if (this.modaltype == "Production") {
        allfinishorder({ id: this.selectedRows1.guid_ })
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              if (this.$refs.cuttingCenter.rowId) {
                this.getAssignmentList(this.$refs.cuttingCenter.rowId);
              }
              this.getdoOrderList();
              this.getMesaList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.btnloading2 = false;
          });
      }
      if (this.modaltype == "finish") {
        processfinishorder({ id: this.selectedRowList1[0] })
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              if (this.$refs.cuttingCenter.rowId) {
                this.getAssignmentList(this.$refs.cuttingCenter.rowId);
              }
              this.getdoOrderList();
              this.getMesaList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
            this.btnloading2 = false;
          });
      }
      if (this.modaltype == "urgent") {
        SetUpExpediting(this.selectedRowList[0])
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              this.getorderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.btnloading2 = false;
            this.spinning = false;
          });
      }
    },
    // 待上机列表
    getorderList(orderNum) {
      let params = {};
      params.process = "VCUT";
      if (orderNum) {
        params.Pdctno_ = orderNum;
      }
      this.table1Loading = true;
      getWaitOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data1Source = res.data;
          }
        })
        .finally(() => {
          this.table1Loading = false;
        });
    },
    // 已上机列表
    getdoOrderList(orderNum) {
      let params = {};
      params.process = "VCUT";
      if (orderNum) {
        params.Pdctno_ = orderNum;
      }
      this.table2Loading = true;
      getDoingOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data2Source = res.data || [];
          }
        })
        .finally(() => {
          this.table2Loading = false;
        });
    },
    // 机台列表
    getMesaList() {
      this.table3Loading = true;
      getMachineList({ type: 4 })
        .then(res => {
          if (res.code == 1) {
            this.data3Source = res.data;
            this.data3SourceCopy = res.data;
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    // 状态统计列表
    getMachineStatuList() {
      this.table4Loading = true;
      getStatisticsList("VCUT")
        .then(res => {
          if (res.code == 1) {
            this.data4Source = res.data;
          }
        })
        .finally(() => {
          this.table4Loading = false;
        });
    },
    // 机台上订单
    getAssignmentList(id) {
      this.table6Loading = true;
      getDispatchList(id)
        .then(res => {
          if (res.code == 1) {
            this.data6Source = res.data;
          }
        })
        .finally(() => {
          this.table6Loading = false;
        });
    },
    cellContextMenuEvent({ row, column, $event }) {
      $event.preventDefault();
      this.menuData = row;
    },
    cellClickEvent({ row, column }) {
      let keys = [];
      keys.push(row.guid_);
      this.selectedRowList = keys;
      let rowData = [];
      rowData.push(row.pnts_);
      this.selectedRowsPnts = rowData[0];
      let pdctnoData = [];
      pdctnoData.push(row.orderNo);
      this.selectedRowsPdctno = pdctnoData[0];
      this.menuData = row;
    },
    cellContextMenuEvent1({ row, column, $event }) {
      $event.preventDefault();
      this.menuData = row;
    },
    cellClickEvent1({ row, column }) {
      let keys = [];
      keys.push(row.guid_);
      this.selectedRowList1 = keys;
      this.selectedRows1 = row;
      let rowData = [];
      rowData.push(row.pnts_);
      this.selectedRowsPnts = rowData[0];
      let pdctnoData = [];
      pdctnoData.push(row.orderNo);
      this.selectedRowsPdctno = pdctnoData[0];
      getOrderMuIdList(row.guid_).then(res => {
        if (res.code == 1) {
          this.idList = res.data;
          this.rowId1 = row.pdctno_;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 弹窗关闭控制
    reportHandleCancel() {
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.dataVisible4 = false;
    },
    //扫码分派
    Scancodetodispatch() {
      if (this.$refs.cuttingCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      this.scancodeVisible = true;
    },
    scancodeOk() {
      if (!this.scancode) {
        return;
      }
      processorder(this.$refs.cuttingCenter.selectedRowKeys, 4, this.scancode).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.$refs.cuttingCenter.selectedRowKeys.forEach(ite => {
            this.data3Source.filter(item => item.iD_ == ite)[0].numMachine_ += 1;
          });
          this.scancode = "";
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 分派
    handleDispatchMachine() {
      if (this.selectedRowList.length <= 0) {
        this.$message.warning("请选择待分派订单");
        return;
      } else if (this.$refs.cuttingCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      let type_ = false;
      const selectedData = this.data3Source.filter(item => this.$refs.cuttingCenter.selectedRowKeys.includes(item.iD_));
      if (selectedData.length) {
        selectedData.forEach(item => {
          if (item.status_ == 1) {
            type_ = true;
          }
        });
      }
      if (type_) {
        this.$message.error("选择机台中有故障机台，不允许分派");
        return;
      }
      this.btnloading1 = true;
      this.spinning = true;
      // 获取待分派订单id和机台id
      let assignmentData = {
        id: this.selectedRowList[0],
        equId: this.$refs.cuttingCenter.selectedRowKeys,
      };
      getDispatchMachineList(assignmentData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("分派成功");
            this.data1Source.splice(
              this.data1Source.findIndex(item => item.guid_ == this.selectedRowList[0]),
              1
            );
            this.selectedRowList = [];
            this.$refs.cuttingCenter.selectedRowKeys.forEach(ite => {
              this.data3Source.filter(item => item.iD_ == ite)[0].numMachine_ += 1;
            });
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.btnloading1 = false;
          this.spinning = false;
        });
    },
    // 分派回退
    getDispatchFallback(record) {
      let params = {
        id: record.guid_,
      };
      console.log("record", record);
      if (confirm("确定回退吗？")) {
        this.spinning = true;
        getDispatchFallbackList(params)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("分派回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.getAssignmentList(record.iD_);
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    // 生产备注
    ExceptionRemarksClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.dataVisible3 = true;
    },
    handleOk4() {
      this.confirmLoading = true;
      this.spinning = true;
      let note = this.$refs.exceptionRemarksInfo.note;
      ExceptionRemarks({ note: note, id: this.selectedRowList[0] })
        .then(res => {
          if (res.code == 1) {
            this.$message.success("备注成功");
          } else {
            this.$message.error(res.message);
          }
          this.getorderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible3 = false;
        });
    },
    //查询
    queryClick() {
      this.dataVisible4 = true;
    },
    keyupEnter1() {
      this.dataVisible4 = false;
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    handleOk5() {
      this.dataVisible4 = false;
      this.getorderList(this.$refs.queryInfo.OrderNumber);
      this.getdoOrderList(this.$refs.queryInfo.OrderNumber);
    },
    handleOk6() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = {
        lsId: this.$refs.cuttingCenter.selectedRowKeys,
        stepPassword: this.$refs.PersonnelRegistration.stepPassword,
      };
      console.log(this.$refs.cuttingCenter.selectedRowKeys);
      Personnelregistration(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("登记成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
        });
    },
    bodyClick() {
      this.menuVisible = false;
      this.menuVisible1 = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    rightClick1(e) {
      this.menuVisible1 = true;
      this.menuStyle.top = e.clientY - 480 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 下载
    down() {
      console.log("this.menuData.guid_:", this.menuData.guid_);
      downLoad(this.menuData.guid_).then(res => {
        console.log("res.data:", res.data);
        if (res.code == 1) {
          window.location.href = res.data.wPath;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 上传tgz
    async httpRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });
      console.log("resData:", resData);
      if (resData.code == 1) {
        orderFileUpload({ Pdctno: this.menuData.pdctno_, path: resData.data }).then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".tgz") != -1;
      if (!isFileType) {
        this.$message.error("只支持.tgz格式文件");
      }
      return isFileType;
    },
    // 上传Mnf2
    async httpRequestMnf2(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });

      if (resData.code == 1) {
        orderFileUploadMnf2({ Pdctno: this.menuData.pdctno_, path: resData.data }).then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    beforeUploadMnf2(file) {
      const isFileType = file.name.toLowerCase().indexOf(".mnf2") != -1;

      if (!isFileType) {
        this.$message.error("只支持.Mnf2格式文件");
      }
      return isFileType;
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible4) {
        this.handleOk5();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
  },
  mounted() {
    this.getorderList();
    this.getdoOrderList();
    this.getMesaList();
    this.getMachineStatuList();
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("resize", this.dehandleResize, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.handleResize();
    });
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.vxe-loading > .vxe-loading--chunk {
  color: #ff9900 !important;
}
/deep/.vxe-header--column {
  font-weight: 500;
}
/deep/.vxe-table--render-default {
  position: relative;
  font-size: 14px;
  color: #000000;
  font-weight: 500;
  font-family: sans-serif;
  direction: ltr;
}
/deep/.vxe-table .vxe-table--header-wrapper {
  color: #000000;
  font-family: sans-serif;
}
/deep/.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 4px;
}
/deep/.vxe-table--render-default .vxe-cell {
  padding: 0;
}
/deep/.vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default.vxe-editable .vxe-body--column,
.vxe-table--render-default .vxe-footer--column.col--ellipsis,
.vxe-table--render-default .vxe-header--column.col--ellipsis {
  height: 36px;
  .c--tooltip {
    padding: 5px;
  }
}
/deep/.vxe-table--render-default .vxe-body--row.row--current {
  background: rgb(223 220 220);
}
/deep/.vxe-table--render-default .vxe-body--row.row--hover {
  background: #dfdcdc !important;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
.Vcutmanagement {
  user-select: normal;
  .content {
    display: flex;
  }
  .rowcolor {
    background: #fff9e6;
    -moz-user-select: normal;
    -webkit-user-select: normal;
    user-select: normal;
  }

  .footer {
    .actionBox {
      overflow: hidden;
      width: 100%;
      height: 47px;
      border: 2px solid #e9e9f0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      form {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
      .clickRowSty2 {
        background: #fff9e6;
      }
    }
  }
  .left {
    /deep/ .ant-table-body {
      .ant-table-tbody {
        .ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
      }
    }
    /deep/ .ant-table-fixed {
      .ant-table-tbody {
        .ant-table-row-hover {
          td {
            background-color: #aba5a5;
          }
        }
        .ant-table-row {
          .orderClass {
            user-select: normal;
          }
        }
      }
    }
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    /deep/ .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
  }
  /deep/ .ant-table {
    .ant-table-selection-col {
      width: 30px;
    }
  }
  background: #ffffff;
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 5px 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 5px;
        }
      }
    }
  }

  /deep/ .ant-table-body {
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 0;
        }
      }
    }
  }
  /deep/ .ant-table-scroll .mouseentered {
    overflow-x: scroll !important;
  }
}
</style>
