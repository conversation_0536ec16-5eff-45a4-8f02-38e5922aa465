<!-- 市场管理 - 预审分派-已发订单列表 -->
<template>
  <div ref="tableWrapper" style="width: 100%">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ y: 738, x: 245 }"
      :loading="producerInfoLoading"
      :customRow="customRow"
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      class="rightTable"
      :pagination="false"
    >
      <template slot="action" slot-scope="text, record">
        <a-tooltip title="分派回退">
          <a-icon type="rollback" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="ChargebackClick(record)" />
        </a-tooltip>
      </template>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>

<script>
import RightCopy from "@/pages/RightCopy";
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    align: "left",
    width: 125,
    ellipsis: true,
  },
  {
    title: "状态",
    dataIndex: "statusStr",
    align: "left",
    width: 65,
    ellipsis: true,
  },
  {
    title: "派单时间",
    dataIndex: "preSendTime",
    align: "left",
    width: 155,
    ellipsis: true,
  },
  {
    title: "工厂",
    dataIndex: "facName",
    align: "left",
    width: 80,
    ellipsis: true,
  },
  {
    title: "操作",
    align: "center",
    width: 40,
    scopedSlots: { customRender: "action" },
  },
];
export default {
  components: {
    RightCopy,
  },
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    producerInfoLoading: {
      type: Boolean,
      required: true,
    },
  },
  name: "RightTable",
  data() {
    return {
      columns,
      menuData: {},
    };
  },
  methods: {
    customRow(record, index) {
      return {
        on: {
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    // 分派回退
    ChargebackClick(record) {
      this.$emit("ChargebackClick", record);
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.min-table {
  /deep/ .ant-table-body {
    height: 738px;
  }
}
.rightTable {
  /deep/ .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 6px 0px;
    overflow-wrap: break-word;
  }
}
</style>
