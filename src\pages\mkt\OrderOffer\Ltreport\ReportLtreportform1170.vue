<!--龙腾报价表单  -->
<template>
    <div class="pdfDom1" style="font-size: 13px;" >
        <a-button v-print='printObj1'   @click="printpdf" type="primary" class="printstyle">打印</a-button>
        <div id="ltreport1170" style=" padding: 25px;font-family: 等线;color: black;font-weight: 600;" >
            <div style="width: 100%;display: flex;">
                    <div style="text-align: center;width: 100%;">
                        <img   src="@/assets/img/lt.png" style="height: 70px;">
                        <div>
                            <span style="font-size: 32px;font-weight: bold;letter-spacing: 5px;">报价单</span>
                        </div>    
                    </div>                            
            </div>
            <div style="display: flex;line-height: 3ch;">
                <div style="width:50%;z-index: 99;font-size: 15px;">
                    <div>客户名称(Customer name):{{ LTreportdata.name_ }}</div>
                    <div>联系人(connect person):{{ LTreportdata.party_  }}</div>
                    <div>电话(tel No.):{{ LTreportdata.facPhone_ }}</div>
                    <div>E-MAIL:{{ LTreportdata.facFax_ }}</div>
                </div>
                <div style="z-index: 99;width:50%">
                    <div style="float: right;font-size: 15px;">
                        <div>名称(marketing name):{{ LTreportdata.factory_ }}</div>
                        <div>联系人(connect person):{{ LTreportdata.link_ }}</div>
                        <div>电话(tel No.):{{ LTreportdata.tel_ }}</div>
                        <div>E-MAIL:{{ LTreportdata.fax_ }}</div>
                        <div>报价日期:{{ LTreportdata.date_ }}</div>
                    </div>                   
                </div>               
            </div>
            <div>
                <table border="1" style="text-align: center;margin-top: 5px;width: 100%;border-top: 1px solid black;border-left: 1px solid black;">
                    <thead>
                        <tr style="font-size: 15px;">
                            <td>No</td>
                            <td style="width: 110px;">产品名称<br/>Part Number</td>
                            <td>规格要求<br/>Description</td>
                            <td colspan="2">尺寸(SIZE)MM</td>
                            <td>拼板<br/>panel</td>
                            <td>表面处理<br/>(Finish)</td>
                            <td>单 价/PCS(RMB 未税)</td>
                            <td>MOQ/PCS</td>
                            <td>备注/交期</td>
                        </tr>
                    </thead>  
                    <tbody>
                        <tr v-for='(item,index) in LTreportdata.price' :key="index">
                            <td>{{ index+1 }}</td>
                            <td>{{ item.custName }}</td>
                            <td>{{ item.pinBanType }}</td>
                            <td>{{ item.setBoardWidth }}</td> 
                            <td>{{ item.setBoardHeight }}</td>
                            <td>{{ item.su }}</td>
                            <td>{{ item.surface }}</td>
                            <td>{{ item.poreDensity }}</td>
                            <td>{{ item.setQty }}</td>
                            <td>{{ item.notes }}</td>
                        </tr>
                    </tbody>                
                </table>
            </div>
            <div style="z-index:99;position: relative;">
                <div style="display: flex;line-height:2.8ch;margin-top: 8px;font-size: 15px;">
                    <div>
                        <div style="font-size: 16px;">备注(Remark):{{ LTreportdata.noteSure }}</div>  
                       &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 1 返单最低消费(MOQ):{{ LTreportdata.freight }}<br/>
                       &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 2 交货方式(Delivery):{{ LTreportdata.deliveryType }}<br/>
                       &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 3 交货地点(Deliveryadd):{{LTreportdata.currency_}}<br/>
                       &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 4 付款方式(Paymentmethod):{{ LTreportdata.clearingForm }}<br/>
                       &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 5 价格含税状况(Tax):{{LTreportdata.taxPercentage_}}<br/>
                       &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 6 此报价有效期(Period of time):{{LTreportdata.pact_}}<br/>
                       &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 7 验收标准:{{ LTreportdata.ipcLevel }}<br/>
                    </div>
                </div>
            </div>
            <div style="display: flex;width: 100%;padding-top: 15px;font-size: 15px;z-index: 99;position: relative;">
                <div style="width: 40%;line-height: 5ch;">
                    <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;客户确认: <span class="Underline"></span></div>
                    <div>日期(DATE): <span class="Underline"></span></div>
                </div>
                <div style="width: 35%;line-height: 5ch;">
                    <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;核准: <span class="Underline"></span>{{LTreportdata.discer}}</div>
                    <div>日期(DATE): <span class="Underline"></span>{{LTreportdata.date_}}</div>
                </div>
                <div style="width: 25%;line-height: 5ch;">
                    <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;报价人: <span class="Underline"></span>{{LTreportdata.quoter}}</div>
                    <div>日期(DATE): <span class="Underline"></span>{{LTreportdata.date_}}</div>
                </div>
            </div>
            <img  src="@/assets/img/lthtz.png" 
             style="position: relative;
                    top: -160px;
                    z-index: 0;
                    display: block;
                    left: 554px;
                    width: 150px;
                    transform: rotate(353deg);"  >    
        </div>
    </div>
  </template>
  <script>
  import htmlToPdf from '@/utils/htmlToPdf'; //竖版a4
  import htmlToPdfa3 from '@/utils/htmlToPdfa3'; //横版a4
  export default {
    name: "",
    props:['LTreportdata','ttype'],    
    computed:{  },
    data() {
      return { 
        amountto:0,
        printObj1:{
            id: "ltreport1170", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '打印预览', // 预览页面的标题
            popTitle: '&nbsp;', // 打印页面的页眉
            scanStyles: false,  
         },
      }
    },
    mounted() {
        this.printObj1.closeCallback = this.closePrintTool;
        this.amountto =0 
        for (let index = 0; index < this.LTreportdata.price.length; index++) {
            if(this.LTreportdata.price[index].total && this.LTreportdata.price[index].total!='/'){
                this.amountto +=Number(this.LTreportdata.price[index].total)
            } 
        }
        this.amountto = this.amountto ? this.getFloat(this.amountto,4) : 0
    },
    methods: { 
        closePrintTool(){
            document.title=this.ttype
        },
        printpdf(){
            document.title=this.LTreportdata.pcbFileName
        },
        term(text){
            return text.replace(/\|/g, '\n');
        },
        getreportPdf(){
            htmlToPdfa3('ltreport1170',this.LTreportdata.pcbFileName)
        },
     },
  }
  </script>
  
  <style lang="less" scoped>
   .printstyle{
    position: absolute;
    top: 716px;
    right: 26px;
  }
   .Underline {
    position: relative;
    display: inline-block;
    }
    .Underline::after {
    content: "";
    position: absolute;
    left: 0;
    bottom:-8px; /* 下划线距离文字底部的距离 */
    width: 200px; /* 下划线宽度 */
    height: 2px; /* 下划线高度 */
    background-color: rgb(107, 106, 106); /* 下划线颜色 */
    }
  .pdfDom1{
      height: 650px;
      overflow: auto;
      table >thead> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
    table >tbody> tr > td {
        border-bottom: 1px solid black;
        border-right: 1px solid black;
    }
  }
  </style>
