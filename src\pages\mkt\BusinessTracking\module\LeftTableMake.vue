<!-- 市场管理 - 业务跟单- 订单列表 -->
<template>
  <a-spin :spinning="spinning">
    <div ref="tableWrapper" class="table1">
      <a-table
        :columns="leftcolumns"
        :scroll="{ y: 737, x: 600 }"
        :dataSource="dataSource"
        :pagination="pagination"
        :customRow="customRow"
        :loading="orderListload"
        :rowKey="rowKey"
        :rowClassName="isRedRow"
        @change="handleTableChange"
      >
        <template slot="labelUrl" slot-scope="record">
          <span @click.stop="$emit('OperationLog', record, 'journal')" style="color: #428bca"> 日志</span>
        </template>
        <template slot="sales" slot-scope="text, record">
          <a style="color: #428bca" type="primary" @click="$emit('getDetailInfo', record.id)">预审</a>
        </template>
      </a-table>
      <right-copy ref="RightCopy" />
    </div>
    <a-modal title="修改内容" :width="1300" :visible="xiuvisible" destroyOnClose centered :mask="false" :maskClosable="false" @cancel="handleCancel">
      <div>
        <a-table
          class="xiu"
          :columns="columns4"
          :dataSource="dataSource4"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 400 }"
          :class="dataSource4.length ? 'min-table' : ''"
          :pagination="false"
          :loading="loading2"
        >
          <template slot="isPrintContract" slot-scope="record">
            <a-checkbox :checked="record.isPrintContract"></a-checkbox>
          </template>
          <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
            <template v-for="photo in record.filePath.split(',')">
              <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer :key="index + '-' + photo" />
            </template>
          </div>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="handleCancel">取消</a-button>
      </template>
    </a-modal>
  </a-spin>
</template>
<script>
import RightCopy from "@/pages/RightCopy";
import { ordermodifylist } from "@/services/mkt/CustInfoNew";
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
    // scopedSlots: { customRender: 'index' },
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "30%",
    align: "left",
  },
  // {
  //     title: "附件地址",
  //     dataIndex: "filePath",
  //     ellipsis: true,
  //     align: "left",
  // },
  {
    title: "图片",
    //dataIndex: "filePath",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    // dataIndex: "isPrintContract",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  props: ["dataSource", "leftcolumns", "pagination", "rowKey", "orderListload"],
  components: { RightCopy },
  data() {
    return {
      columns4,
      spinning: false,
      selectedRowKeysArray: [],
      selectedRowsData: {},
      startIndex: -1,
      dataSource4: [],
      loading2: false,
      xiuvisible: false,
      shiftKey: false,
      proOrderId: "",
      menuData: {},
    };
  },
  methods: {
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleCancel() {
      this.xiuvisible = false;
      this.dataSource4 = [];
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowKeysArray.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    xiudisplay(record) {
      this.loading2 = true;
      ordermodifylist(record.id)
        .then(res => {
          if (res.code) {
            this.dataSource4 = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
      this.xiuvisible = true;
    },
    handleTableChange(pagination, filter, sorter) {
      this.$emit("tableChange", pagination, filter, sorter);
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e);
          },
        },
      };
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowKeysArray;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowKeysArray = rowKeys;
        if (this.selectedRowKeysArray.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowKeysArray = [record.id];
        }
      }
      this.proOrderId = this.selectedRowsData.id;
      this.$emit("gettopdata", this.proOrderId);
      this.shiftKey = false;
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      this.selectedRowKeysArray = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[0];
        })[0];
      }
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
  watch: {
    pagination: {
      handler(val) {},
    },
  },
};
</script>
<style lang="less" scoped>
.xiu {
  /deep/.ant-table-row-cell-ellipsis,
  .ant-table-row-cell-ellipsis .ant-table-column-title {
    overflow: overlay;
    white-space: normal;
    text-overflow: inherit;
  }
}
.ant-table .ant-table-row-selected {
  background-color: #dcdcdc;
}
/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }
  .rowBackgroundColor {
    background: #dcdcdc;
  }
  .displayFlag {
    display: none;
  }
  /deep/.ant-modal-body {
    padding: 0 4px;
  }
}
/deep/.ant-modal-body {
  padding: 0;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table {
  .ant-table-thead > tr > th {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr {
    .lastTd {
      padding: 0 4px !important;
    }
  }
}
.table1 {
  /deep/.ant-table {
    .rowBackgroundColor {
      background: #dcdcdc !important;
    }
  }
}
</style>
