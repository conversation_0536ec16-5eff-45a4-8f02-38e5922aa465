<template>
  <a-layout-header :class="[headerTheme, 'admin-header']">
    <div :class="['admin-header-wide', layout, pageWidth]">
      <router-link v-if="isMobile || layout === 'head'" to="/" :class="['logo', isMobile ? null : 'pc', headerTheme]">
        <!-- <img width="32" src="@/assets/img/bn.jpg" /> -->
        <h1 v-if="!isMobile">{{ systemName }}</h1>
      </router-link>
      <a-icon v-if="!isMobile" class="trigger" :type="collapsed ? 'menu-unfold' : 'menu-fold'" @click="toggleCollapse(collapsed)" />
      <div v-if="layout !== 'side' && !isMobile" class="admin-header-menu" :style="`width: ${menuWidth};`">
        <i-menu class="head-menu" :theme="headerTheme" mode="horizontal" :options="menuData" @select="onSelect" />
      </div>
      <div :class="['admin-header-right', headerTheme]">
        <div class="header-item" style="display: flex" @click="deepseek">
          <svg t="1739962219545" class="icon" viewBox="0 0 1408 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2042" height="20">
            <path
              d="M1361.92 83.136c-14.272-7.04-20.416 6.272-28.736 12.992-2.816 2.24-5.248 5.12-7.68 7.68-20.8 22.336-45.056 36.864-76.8 35.136-46.464-2.56-86.08 12.032-121.152 47.616-7.552-43.904-32.256-70.08-69.888-86.912-19.712-8.768-39.68-17.472-53.376-36.48-9.664-13.44-12.288-28.48-17.216-43.264-3.008-8.96-6.08-18.112-16.32-19.712-11.2-1.728-15.552 7.68-19.968 15.424-17.536 32.128-24.32 67.52-23.68 103.296 1.6 80.448 35.52 144.576 103.04 190.144 7.68 5.312 9.6 10.56 7.168 18.176-4.608 15.68-10.048 30.976-14.912 46.592-3.072 10.112-7.68 12.352-18.304 8a308.224 308.224 0 0 1-97.28-66.176c-48-46.4-91.392-97.664-145.472-137.792a655.36 655.36 0 0 0-38.528-26.432c-55.232-53.76 7.232-97.792 21.632-103.04 15.104-5.376 5.312-24.128-43.52-23.936C652.032 24.704 607.36 41.024 550.4 62.72a156.8 156.8 0 0 1-26.048 7.68 542.016 542.016 0 0 0-161.408-5.696c-105.6 11.904-189.888 61.824-251.904 147.2C36.608 314.24 19.072 430.848 40.512 552.32c22.528 128 87.808 234.048 188.16 316.992 104 85.888 223.808 128 360.512 120 82.944-4.864 175.424-16 279.68-104.32 26.368 13.056 53.888 18.24 99.712 22.272 35.2 3.328 69.184-1.792 95.424-7.232 41.216-8.704 38.4-46.848 23.424-53.888-120.576-56.32-94.208-33.408-118.272-51.84 61.376-72.768 153.792-148.224 189.952-392.768 2.816-19.392 0.384-31.552 0-47.36-0.256-9.536 1.92-13.312 12.8-14.4a231.04 231.04 0 0 0 86.592-26.56c78.272-42.88 109.696-113.024 117.184-197.184 1.088-12.928-0.256-26.24-13.76-32.96z m-681.408 757.76c-116.928-92.096-173.696-122.368-197.12-120.96-21.888 1.152-17.984 26.304-13.184 42.624 5.12 16.128 11.648 27.328 20.8 41.408 6.464 9.408 10.752 23.424-6.272 33.92-37.76 23.424-103.232-7.872-106.24-9.472-76.288-44.8-140.032-104.192-184.96-185.344-43.264-78.08-68.48-161.92-72.576-251.328-1.152-21.632 5.184-29.312 26.688-33.152a265.6 265.6 0 0 1 85.696-2.24c119.296 17.472 220.928 71.04 306.048 155.52 48.768 48.32 85.504 105.92 123.392 162.176 40.256 59.776 83.648 116.672 138.88 163.392 19.392 16.32 35.072 28.8 49.92 37.952-44.928 5.056-119.872 6.08-171.008-34.496z m56.064-361.024a17.152 17.152 0 1 1 2.752 9.6 16.896 16.896 0 0 1-2.752-9.664z m174.08 89.472a102.72 102.72 0 0 1-33.024 8.96 70.528 70.528 0 0 1-44.736-14.272c-15.296-12.8-26.176-19.968-30.848-42.496a99.264 99.264 0 0 1 0.832-32.96c4.032-18.368-0.384-30.08-13.248-40.768-10.624-8.768-23.872-11.072-38.592-11.072a31.168 31.168 0 0 1-14.272-4.416c-6.144-3.072-11.136-10.752-6.336-20.16 1.536-3.008 8.96-10.304 10.752-11.712 19.84-11.328 42.88-7.68 64.192 0.896 19.712 8.064 34.56 22.848 56 43.776 21.952 25.28 25.792 32.384 38.4 51.328 9.856 14.848 18.816 30.208 24.96 47.616 3.776 10.88-1.152 19.776-14.08 25.28z"
              p-id="2043"
              fill="#ffffff"
            ></path>
          </svg>
        </div>
        <div class="header-item" style="display: flex">
          <a-popover :open="visible" trigger="click">
            <template #content>
              <a-tabs :activeKey="activeKey" @change="changeTabs">
                <a-tab-pane key="1" :tab="`消息(${infolength})`">
                  <a-spin :spinning="loading">
                    <div class="info" v-if="infoList.length > 0">
                      <div v-for="(item, index) in infoList" :key="index" style="line-height: 3.5ch">
                        <div style="font-weight: bolder; color: black">{{ item.title }}</div>
                        <div>{{ item.content }}</div>
                        <div style="display: flex; justify-content: space-between; color: #ff9900">
                          <div>{{ item.createTime }}</div>
                          <div style="cursor: pointer" @click="Setasread(item.id)" v-if="item.status === 0">[设为已读]</div>
                        </div>
                        <a-divider />
                      </div>
                    </div>
                    <div v-else style="text-align: center">
                      <svg
                        t="1730449165796"
                        class="icon"
                        viewBox="0 0 1309 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="1861"
                        width="40"
                        height="40"
                      >
                        <path
                          d="M483.621169 991.877103H16.959827a16.073295 16.073295 0 1 0 0 32.098966h466.661342a16.073295 16.073295 0 0 0 11.382274-27.407945 16.073295 16.073295 0 0 0-11.382274-4.691021z m638.800381-65.721919H780.310388c-8.917702 0-16.085201 7.38181-16.085201 16.263793 0 8.89389 7.167499 16.2757 16.073295 16.2757h342.123068c8.905796 0 16.085201-7.38181 16.085202-16.2757a16.073295 16.073295 0 0 0-16.073296-16.263793z m171.055581 0h-74.770589c-8.905796 0-16.073295 7.38181-16.073295 16.263793 0 8.89389 7.167499 16.2757 16.073295 16.2757h74.770589c8.929609 0 16.097108-7.38181 16.097108-16.2757a16.073295 16.073295 0 0 0-16.097108-16.263793z m-638.145543 65.721919h-75.211117a16.073295 16.073295 0 1 0 0 32.098966h75.211117a16.073295 16.073295 0 0 0 11.382274-27.407945 16.073295 16.073295 0 0 0-11.382274-4.691021zM169.977599 828.119988h49.124753c9.120107 0 16.299512-7.38181 16.299512-16.263793 0-8.89389-7.38181-16.2757-16.311418-16.2757h-49.112847c-8.905796 0-16.311418 7.38181-16.311418 16.2757-0.202404 8.89389 7.179405 16.263794 16.311418 16.263793z m736.180739-16.263793c0 8.89389 7.38181 16.263794 16.299512 16.263793h119.97822c9.132013 0 16.311418-7.38181 16.311418-16.263793 0-8.89389-7.393716-16.2757-16.311418-16.2757H922.45785c-8.905796 0-16.311418 7.167499-16.311418 16.2757z m-639.895747 0c0 8.89389 7.393716 16.263794 16.311418 16.263793h152.791555c9.132013 0 16.311418-7.38181 16.311418-16.263793 0-8.89389-14.573121-18.656929-23.478917-18.656929l6.953189 2.381229H282.562103c-8.905796 0-16.299512 7.167499-16.299512 16.2757z m111.286735 49.231908c-8.905796 0-16.311418 7.38181-16.311419 16.263794 0 8.89389 7.405622 16.2757 16.311419 16.275699h599.67679c8.917702 0 16.311418-7.38181 16.311418-16.275699 0-8.881984-7.393716-16.263794-16.311418-16.263794H377.549326zM621.029985 38.671158c127.895806 0 231.979323 109.798466 231.979323 244.89749v28.372343c0 135.003775-104.083517 244.909396-231.979323 244.909397h-54.60158c-94.332384 0-125.955105 76.913695-144.731095 122.811882-1.750203 4.095714-3.595656 8.905796-5.548263 13.31107-6.357881-9.012952-13.954002-21.002439-20.121385-30.836915-12.203798-19.252236-25.955395-41.076199-41.981066-61.864328-38.183006-49.672436-84.986061-89.415147-86.938669-91.058194l-3.893309-3.476595-5.238704-2.262167c-3.4885-1.631142-85.402776-41.671506-85.402776-191.53415v-28.372343c0-134.991869 104.083517-244.909396 231.979324-244.909396H621.029985z m0-38.623533H404.457213C254.904129 0.047625 133.782919 126.967127 133.782919 283.568648v28.372343c0 179.961377 108.286386 226.776338 108.286386 226.776338s45.160007 38.099663 81.199907 85.009873c35.396968 46.088686 61.483331 100.89267 81.188001 113.394122 6.262632 3.988558 11.70374 5.738762 16.430479 5.738762C464.178434 742.860086 455.355981 595.35486 566.416499 595.35486h54.50633c149.445928 0 270.674294-126.907596 270.674294-283.521024v-28.372343C891.704278 126.967127 770.487819 0.047625 621.029985 0.047625z"
                          fill="#D5D5D5"
                          p-id="1862"
                        ></path>
                        <path
                          d="M927.422712 309.07161c-9.858288 0-18.371181 7.38181-19.407016 17.41869a19.407016 19.407016 0 0 0 17.359159 21.192937c69.48426 6.965095 118.037519 62.388198 118.037519 134.801371v15.573237c-1.547799 79.378267-51.434545 111.131955-53.482402 112.358288l-2.357417 1.643048c-1.131084 0.916773-28.122314 23.240794-50.08915 51.208328-9.239168 11.668022-17.144848 23.967069-24.229005 34.932628-4.929144 7.477059-11.191776 17.299628-14.882681 22.324022-1.845452-3.690905-4.000465-8.703392-5.738761-12.799106-10.679812-25.407713-28.539029-67.912649-81.402312-67.912649H774.976436c-33.253862 0-47.624579-0.61912-67.745964-17.311535a19.335579 19.335579 0 0 0-12.310953-4.405273c-5.810199 0-11.310837 2.583633-14.989836 7.07225a19.204611 19.204611 0 0 0 2.678882 27.14601c31.717969 26.110175 58.506795 26.110175 92.367871 26.110175h36.444709c24.943373 0 34.182541 17.513939 45.362411 44.147985 7.810431 18.430712 16.537635 39.433151 39.111685 39.433151 6.465037 0 13.144384-2.047857 19.716576-6.143571 10.358346-6.345975 18.573586-19.156987 29.967766-36.766175 6.572192-10.144035 14.061157-21.716808 22.169241-31.956092 17.347253-22.026368 38.897375-40.778546 43.42171-44.5647 10.572656-6.965095 68.865141-49.470031 70.7225-144.111975v-16.073295c0-92.903647-62.923975-164.197642-152.946335-173.210593-0.30956-0.107155-0.916773-0.107155-1.535893-0.107156zM420.566226 217.953885a23.574167 23.574167 0 0 0-23.609885 23.550354v22.978859a23.574167 23.574167 0 0 0 23.609885 23.574167 23.574167 23.574167 0 0 0 23.609885-23.574167v-22.978859a23.574167 23.574167 0 0 0-23.609885-23.550354z m184.24759 0a23.574167 23.574167 0 0 0-23.609885 23.550354v22.978859a23.574167 23.574167 0 0 0 23.609885 23.574167 23.574167 23.574167 0 0 0 23.609885-23.574167v-22.978859a23.574167 23.574167 0 0 0-23.609885-23.550354zM454.474926 389.247589a23.764665 23.764665 0 0 0 32.622837-6.607911c0.095249-0.142874 10.286909-14.894587 25.824428-14.894587 15.108898 0 24.502846 13.644442 25.145777 14.60884a23.645603 23.645603 0 0 0 32.372808 7.369903 23.538448 23.538448 0 0 0 7.59612-32.444244c-8.477175-13.62063-31.717969-36.65902-65.126611-36.659019-33.218144 0-56.756592 22.871704-65.424265 36.373272a23.40748 23.40748 0 0 0 6.988906 32.265652z"
                          fill="#D5D5D5"
                          p-id="1863"
                        ></path>
                      </svg>
                      <div>暂无数据</div>
                    </div>
                  </a-spin>
                </a-tab-pane>
                <a-tab-pane key="2" tab="公告(0)" force-render style="text-align: center">
                  <div>
                    <svg
                      t="1730449165796"
                      class="icon"
                      viewBox="0 0 1309 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="1861"
                      width="40"
                      height="40"
                    >
                      <path
                        d="M483.621169 991.877103H16.959827a16.073295 16.073295 0 1 0 0 32.098966h466.661342a16.073295 16.073295 0 0 0 11.382274-27.407945 16.073295 16.073295 0 0 0-11.382274-4.691021z m638.800381-65.721919H780.310388c-8.917702 0-16.085201 7.38181-16.085201 16.263793 0 8.89389 7.167499 16.2757 16.073295 16.2757h342.123068c8.905796 0 16.085201-7.38181 16.085202-16.2757a16.073295 16.073295 0 0 0-16.073296-16.263793z m171.055581 0h-74.770589c-8.905796 0-16.073295 7.38181-16.073295 16.263793 0 8.89389 7.167499 16.2757 16.073295 16.2757h74.770589c8.929609 0 16.097108-7.38181 16.097108-16.2757a16.073295 16.073295 0 0 0-16.097108-16.263793z m-638.145543 65.721919h-75.211117a16.073295 16.073295 0 1 0 0 32.098966h75.211117a16.073295 16.073295 0 0 0 11.382274-27.407945 16.073295 16.073295 0 0 0-11.382274-4.691021zM169.977599 828.119988h49.124753c9.120107 0 16.299512-7.38181 16.299512-16.263793 0-8.89389-7.38181-16.2757-16.311418-16.2757h-49.112847c-8.905796 0-16.311418 7.38181-16.311418 16.2757-0.202404 8.89389 7.179405 16.263794 16.311418 16.263793z m736.180739-16.263793c0 8.89389 7.38181 16.263794 16.299512 16.263793h119.97822c9.132013 0 16.311418-7.38181 16.311418-16.263793 0-8.89389-7.393716-16.2757-16.311418-16.2757H922.45785c-8.905796 0-16.311418 7.167499-16.311418 16.2757z m-639.895747 0c0 8.89389 7.393716 16.263794 16.311418 16.263793h152.791555c9.132013 0 16.311418-7.38181 16.311418-16.263793 0-8.89389-14.573121-18.656929-23.478917-18.656929l6.953189 2.381229H282.562103c-8.905796 0-16.299512 7.167499-16.299512 16.2757z m111.286735 49.231908c-8.905796 0-16.311418 7.38181-16.311419 16.263794 0 8.89389 7.405622 16.2757 16.311419 16.275699h599.67679c8.917702 0 16.311418-7.38181 16.311418-16.275699 0-8.881984-7.393716-16.263794-16.311418-16.263794H377.549326zM621.029985 38.671158c127.895806 0 231.979323 109.798466 231.979323 244.89749v28.372343c0 135.003775-104.083517 244.909396-231.979323 244.909397h-54.60158c-94.332384 0-125.955105 76.913695-144.731095 122.811882-1.750203 4.095714-3.595656 8.905796-5.548263 13.31107-6.357881-9.012952-13.954002-21.002439-20.121385-30.836915-12.203798-19.252236-25.955395-41.076199-41.981066-61.864328-38.183006-49.672436-84.986061-89.415147-86.938669-91.058194l-3.893309-3.476595-5.238704-2.262167c-3.4885-1.631142-85.402776-41.671506-85.402776-191.53415v-28.372343c0-134.991869 104.083517-244.909396 231.979324-244.909396H621.029985z m0-38.623533H404.457213C254.904129 0.047625 133.782919 126.967127 133.782919 283.568648v28.372343c0 179.961377 108.286386 226.776338 108.286386 226.776338s45.160007 38.099663 81.199907 85.009873c35.396968 46.088686 61.483331 100.89267 81.188001 113.394122 6.262632 3.988558 11.70374 5.738762 16.430479 5.738762C464.178434 742.860086 455.355981 595.35486 566.416499 595.35486h54.50633c149.445928 0 270.674294-126.907596 270.674294-283.521024v-28.372343C891.704278 126.967127 770.487819 0.047625 621.029985 0.047625z"
                        fill="#D5D5D5"
                        p-id="1862"
                      ></path>
                      <path
                        d="M927.422712 309.07161c-9.858288 0-18.371181 7.38181-19.407016 17.41869a19.407016 19.407016 0 0 0 17.359159 21.192937c69.48426 6.965095 118.037519 62.388198 118.037519 134.801371v15.573237c-1.547799 79.378267-51.434545 111.131955-53.482402 112.358288l-2.357417 1.643048c-1.131084 0.916773-28.122314 23.240794-50.08915 51.208328-9.239168 11.668022-17.144848 23.967069-24.229005 34.932628-4.929144 7.477059-11.191776 17.299628-14.882681 22.324022-1.845452-3.690905-4.000465-8.703392-5.738761-12.799106-10.679812-25.407713-28.539029-67.912649-81.402312-67.912649H774.976436c-33.253862 0-47.624579-0.61912-67.745964-17.311535a19.335579 19.335579 0 0 0-12.310953-4.405273c-5.810199 0-11.310837 2.583633-14.989836 7.07225a19.204611 19.204611 0 0 0 2.678882 27.14601c31.717969 26.110175 58.506795 26.110175 92.367871 26.110175h36.444709c24.943373 0 34.182541 17.513939 45.362411 44.147985 7.810431 18.430712 16.537635 39.433151 39.111685 39.433151 6.465037 0 13.144384-2.047857 19.716576-6.143571 10.358346-6.345975 18.573586-19.156987 29.967766-36.766175 6.572192-10.144035 14.061157-21.716808 22.169241-31.956092 17.347253-22.026368 38.897375-40.778546 43.42171-44.5647 10.572656-6.965095 68.865141-49.470031 70.7225-144.111975v-16.073295c0-92.903647-62.923975-164.197642-152.946335-173.210593-0.30956-0.107155-0.916773-0.107155-1.535893-0.107156zM420.566226 217.953885a23.574167 23.574167 0 0 0-23.609885 23.550354v22.978859a23.574167 23.574167 0 0 0 23.609885 23.574167 23.574167 23.574167 0 0 0 23.609885-23.574167v-22.978859a23.574167 23.574167 0 0 0-23.609885-23.550354z m184.24759 0a23.574167 23.574167 0 0 0-23.609885 23.550354v22.978859a23.574167 23.574167 0 0 0 23.609885 23.574167 23.574167 23.574167 0 0 0 23.609885-23.574167v-22.978859a23.574167 23.574167 0 0 0-23.609885-23.550354zM454.474926 389.247589a23.764665 23.764665 0 0 0 32.622837-6.607911c0.095249-0.142874 10.286909-14.894587 25.824428-14.894587 15.108898 0 24.502846 13.644442 25.145777 14.60884a23.645603 23.645603 0 0 0 32.372808 7.369903 23.538448 23.538448 0 0 0 7.59612-32.444244c-8.477175-13.62063-31.717969-36.65902-65.126611-36.659019-33.218144 0-56.756592 22.871704-65.424265 36.373272a23.40748 23.40748 0 0 0 6.988906 32.265652z"
                        fill="#D5D5D5"
                        p-id="1863"
                      ></path>
                    </svg>
                    <div>暂无数据</div>
                  </div>
                </a-tab-pane>
              </a-tabs>
              <a-button
                type="primary"
                shape="round"
                size="small"
                style="position: absolute; top: 30px; right: 20px; font-size: 14px"
                @click="Setasread()"
                v-if="infoList.length"
                >一键已读</a-button
              >
            </template>
            <a-badge :count="infolength" @click="showpopover">
              <a-icon type="bell" style="font-size: 22px"></a-icon>
            </a-badge>
          </a-popover>
        </div>
        <!-- <a-tooltip class="header-item" title="供应链看板" placement="bottom"  v-if="checkPermission('MES.BillBoard.TvModule')" >
          <a href="http://emstv.bninfo.com/TV/scmenter?IsOauthed=1" style="display: flex;align-items: center;" target="_blank">
            <a-icon v-if="checkPermission('MES.BillBoard')" type="desktop" style="font-size: 22px"/>
          </a>
        </a-tooltip> -->
        <a-dropdown class="lang header-item" style="display: flex" v-if="checkPermission('MES.BillBoard')">
          <div>
            <a-icon type="desktop" style="font-size: 22px" />
          </div>
          <a-menu slot="overlay">
            <a-menu-item v-if="checkPermission('MES.BillBoard.TvModule')" @click="scmenterClick">供应链看板</a-menu-item>
          </a-menu>
        </a-dropdown>
        <!-- <a-tooltip class="header-item" title="工程数据中心" placement="bottom" v-if="checkPermission('MES.TvModule')" >
          <a href="https://emstv.jiepei.com/EngDataCenter/EngDataCenter" style="display: flex;align-items: center;" target="_blank">
            <a-icon type="cluster" style="font-size: 22px"/>
          </a>
        </a-tooltip> -->
        <a-dropdown class="lang header-item" style="display: flex" v-if="checkPermission('MES.EmsDownload')">
          <div>
            <a-icon type="cloud-download" style="font-size: 22px" />
          </div>
          <a-menu slot="overlay">
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsIpcbCam')"
              ><a :href="IPCBurl + '/flupdate/IPCB-CAM-Setup.exe'">下载IPCB-CAM</a></a-menu-item
            >
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsIpcbCamV3') && os == 'Windows'"
              ><a href="https://bninfofile2.obs.cn-south-1.myhuaweicloud.com/update/iPCB-CAM-V3.exe">下载IPCB-CAMV3</a></a-menu-item
            >
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsIpcbCamV3') && os == 'Mac'"
              ><a href="https://bninfofile2.obs.cn-south-1.myhuaweicloud.com/update/iPCB-CAM-V3.dmg">下载IPCB-CAMV3</a></a-menu-item
            >
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsIpcbCamV3') && os == 'Linux'"
              ><a href="https://bninfofile2.obs.cn-south-1.myhuaweicloud.com/update/iPCB-CAM-V3.deb">下载IPCB-CAMV3</a></a-menu-item
            >
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsCamProcess')">
              <a :href="CamUrl">下载CAM助手</a>
            </a-menu-item>
            <!--@click="downloadCAM"  -->
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsProcess')"><a :href="Url + '/flupdate/MUI.ZIP'">下载设备程序</a></a-menu-item>
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsProcess')"
              ><a :href="Url + '/flupdate/framework.zip'">下载程序环境</a></a-menu-item
            >
            <a-menu-item v-if="checkPermission('MES.EmsDownload.EmsUpdateSpec')"
              ><a :href="Url + '/flupdate/UpdateSpec.zip'">下载Spec更新</a></a-menu-item
            >
          </a-menu>
        </a-dropdown>
        <!-- <header-notice class="header-item"/> -->
        <header-avatar class="header-item" style="display: flex" />
        <a-dropdown class="lang header-item">
          <div><a-icon type="global" /> {{ langAlias }}</div>
          <a-menu @click="val => setLang(val.key)" :selected-keys="[lang]" slot="overlay">
            <a-menu-item v-for="lang in langList" :key="lang.key">{{ lang.key.toLowerCase() + " " + lang.name }}</a-menu-item>
          </a-menu>
        </a-dropdown>
      </div>
    </div>
  </a-layout-header>
</template>

<script>
// import HeaderSearch from './HeaderSearch'
// import HeaderNotice from './HeaderNotice'
import { checkPermission } from "@/utils/abp";
import HeaderAvatar from "./HeaderAvatar";
import IMenu from "@/components/menu/menu";
import { mapState, mapMutations } from "vuex";
import { getinformationremind, updateremindstatus } from "@/services/system/InformationRemind.js";
export default {
  name: "AdminHeader",
  components: { IMenu, HeaderAvatar },
  props: ["collapsed", "menuData"],
  data() {
    return {
      loading: false,
      visible: false,
      activeKey: "1",
      langList: [
        { key: "CN", name: "简体中文", alias: "简体" },
        // { key: "HK", name: "繁體中文", alias: "繁體" },
        { key: "US", name: "English", alias: "English" },
        { key: "TH", name: "ภาษาไทย", alias: "ภาษาไทย" },
      ],
      searchActive: false,
      Url: "",
      IPCBurl: "",
      os: "",
      infoList: [],
      CamUrl: "",
    };
  },
  computed: {
    ...mapState("setting", ["theme", "isMobile", "layout", "systemName", "lang", "pageWidth", "infolength"]),
    ...mapState("account", ["user", "buryingpoint"]),
    headerTheme() {
      if (this.layout == "side" && this.theme.mode == "dark" && !this.isMobile) {
        return "light";
      }
      return this.theme.mode;
    },
    langAlias() {
      let lang = this.langList.find(item => item.key == this.lang);
      return lang.alias;
    },
    menuWidth() {
      const { layout, searchActive } = this;
      const headWidth = layout === "head" ? "100% - 188px" : "100%";
      const extraWidth = searchActive ? "600px" : "400px";
      return `calc(${headWidth} - ${extraWidth})`;
    },
  },
  mounted() {
    this.setLang(this.user.defaultLanguage || "CN");
    let num = localStorage.getItem("infolength") || 0;
    this.setinfolength(num);
    this.os = this.getOs();
    if (process.env.VUE_APP_API_BASE_URL == "http://testemsapi.bninfo.com") {
      this.IPCBurl = "http://testemsapi.bninfo.com";
      this.CamUrl = "http://testemsapi.bninfo.com/flupdate/BnCAM.exe";
    } else {
      this.IPCBurl = "http://emsapi.bninfo.com";
      this.CamUrl = "https://bninfofile2.obs.cn-south-1.myhuaweicloud.com/update/BnCAM.exe";
    }
    this.Url = process.env.VUE_APP_API_BASE_URL;
  },
  methods: {
    changeTabs(key) {
      this.activeKey = key;
    },
    showpopover() {
      this.activeKey = "1";
      this.visible = true;
      this.loading = true;
      getinformationremind({})
        .then(res => {
          if (res.code) {
            this.infoList = res.data;
            this.setinfolength(res.data.length);
            localStorage.setItem("infolength", res.data.length);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    Setasread(id) {
      this.loading = true;
      updateremindstatus(id || "all")
        .then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.showpopover();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    checkPermission,
    downloadCAM() {
      let url = this.Url + "/flupdate/BnCAM.exe"; // 实际的下载链接
      let xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        let blob = xhr.response;
        let href = window.URL.createObjectURL(blob);
        let a = document.createElement("a");
        a.style.display = "none";
        a.href = href;
        a.download = "BnCAM.exe"; // 设置下载时的文件名
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(href);
      };
      xhr.send();
    },
    ContractDownload(os) {
      var contractFilePath = "";
      var downloadname = "";
      if (os == "Windows") {
        contractFilePath = this.IPCBurl + "/flupdate/IPCB-CAM-Setup.exe";
        downloadname = "IPCB-CAM-Setup.exe";
      } else if (os == "Linux") {
        contractFilePath = this.IPCBurl + "/DownLoad?name=flupdate%5ciPCB-CAM-*******-Linux.deb";
        downloadname = "iPCB-CAM-*******-Linux.deb";
      } else if (os == "Mac") {
        contractFilePath = this.IPCBurl + "/DownLoad?name=flupdate%5ciPCB-CAM-*******-Darwin.dmg";
        downloadname = "iPCB-CAM-*******-Darwin.dmg";
      }
      const xhr = new XMLHttpRequest();
      xhr.open("GET", contractFilePath, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = downloadname;
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      };
      xhr.send();
    },
    getOs() {
      const ua = navigator.userAgent;
      const os = ua.toLowerCase();
      if (os.indexOf("windows") > -1) {
        return "Windows";
      } else if (os.indexOf("mac") > -1) {
        return "Mac";
      } else if (os.indexOf("android") > -1) {
        return "Android";
      } else if (os.indexOf("ios") > -1) {
        return "iOS";
      } else if (os.indexOf("linux") > -1) {
        return "Linux";
      } else {
        return "其他";
      }
    },
    toggleCollapse(val) {
      this.$emit("toggleCollapse", val);
    },
    onSelect(obj) {
      this.$emit("menuSelect", obj);
    },
    deepseek() {
      this.$router.push({
        path: "/gongcheng/nbook",
      });
    },
    scmenterClick() {
      let url = "http://emstv.bninfo.com/TV/scmenter?IsOauthed=1&TradeType=" + this.user.factoryId + "&userName=" + btoa(this.user.userName);
      window.open(url, "_blank");
    },
    ...mapMutations("setting", ["setLang", "setinfolength"]),
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-tabs-content {
  width: 460px !important;
}
/deep/.info {
  margin-left: 0%;
  width: 450px;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    background: #ff9900;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #ffffff;
  }
}

/deep/.ant-divider-horizontal {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 8px 0;
  background-color: #d6d6d6;
}
/deep/.ant-badge-count {
  min-width: 14px;
  height: 14px;
  line-height: 14px;
}
@import "index";
</style>
