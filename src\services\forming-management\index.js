import { request, METHOD } from '@/utils/request'
// 待上机
export async function getWaitOrderList(params) {
    return request("/api/app/e-mSTProc-production-process/cnc-wait-distribute", METHOD.GET, params)
}
// 已上机
export async function getDoingOrderList(params) {
    return request("/api/app/e-mSTProc-production-process/cnc-finish-distribute", METHOD.GET, params)
}
// 当班统计
export async function getStatisticsList(params) {
    return request("/api/app/e-mSTProc-production-process/get-ondutystatistics", METHOD.GET, params)
}
// 获取机台列表
export async function getMachineList(params) {
    return request("/api/app/e-mSTProc-production-process/cnc-machine-list", METHOD.GET, params)
}
// 机台上订单(机台id)
export async function getDispatchList(id) {
    return request(`/api/app/e-mSTProc-production-process/${id}/get-probe-finish-order`, METHOD.GET)
}
// 锣刀表(pdctno)
export async function getDrillHoleList(params) {
    return request(`/api/app/e-mSTProc-production-process/get-rout-t0output`, METHOD.GET, params)
}
// 分派机台
export async function getDispatchMachineList(params) {
    return request('/api/app/e-mSTProc-production-process/rout-send-machine', METHOD.POST, params)
}
// 分派回退
export async function getDispatchFallbackList(params) {
    return request('/api/app/e-mSTProc-production-process/drill-back-machine', METHOD.POST, params)
}
// 下机
export async function getDoneOrderList(Id) {
    return request(`/api/app/e-mSTProc-rOUTMake/rout-order-finish/${Id}`, METHOD.POST)
}
// 获取部门过序数量
export async function getpassStepNum(params) {
    return request(`/api/app/e-mSTProc-production-process/character-num`, METHOD.GET, params)
}
// 成型部门过序
export async function OverSequence(params) {
    return request(`/api/app/e-mSTProc-production-process/character-over-count`, METHOD.POST, params)
}
// 获取工厂Id列表
export async function getFactoryList() {
    return request(`/api/app/e-mSTPub-factory-configure/factory-id-list`, METHOD.POST,)
}
// 按钮上传ROU文件
export async function UploadFile(params) {
    return request(`/api/app/e-mSTProc-rOUTMake/up-load-routing-file`, METHOD.POST, params)
}
// 按钮上传TGZ文件
export async function UploadFileTgz(params) {
    return request(`/api/app/e-mSTProc-rOUTMake/up-tgzpath-file`, METHOD.POST, params)
}
// 上传订单
export async function UploadOrder(params) {
    return request(`/api/app/e-mSTProc-production-process/out-rout-order-up-load`, METHOD.POST, params)
}
// 涨缩登记
export async function getHarmomegathusRegister(params) {
    return request(`/api/app/e-mSTProc-production-process/zs-register`, METHOD.POST, params)
}
// 设置加急
export async function SetUpExpediting(Id) {
    return request(`/api/app/e-mSTProc-production-process/is-urgent/${Id}`, METHOD.POST, Id)
}
// 数据核对
export async function DataCheck() {
    return request(`/api/app/e-mSTProc-production-process/data-check`, METHOD.POST,)
}
// 删除订单
export async function DeleteOrder(Id) {
    return request(`/api/app/e-mSTProc-production-process/del-order/${Id}`, METHOD.POST, Id)
}
// 异常备注
export async function ExceptionRemarks(Id, params) {
    return request(`/api/app/e-mSTProc-production-process/flying-probe-remarks/${Id}`, METHOD.GET, params)
}
// 获取订单已上机台Id数组
export async function getOrderMuIdList(Id) {
    return request(`/api/app/e-mSTProc-production-process/cnc-order-mu-id-list/${Id}`, METHOD.GET,)
}
// 修改Pnl数
export async function updatePnl(params) {
    return request(`/api/app/e-mSTProc-production-process/up4Character-pnl-qty`, METHOD.POST, params)
}
// 修改锣程
export async function updateRout(Id, params) {
    return request(`/api/app/e-mSTProc-production-process/up-rout-c/${Id}`, METHOD.GET, params)
}
// 标记/取消 机台故障
export async function signMachineStatus(Id) {
    return request(`/api/app/e-mSTProc-rOUTMake/set-sign-bad/${Id}`, METHOD.POST,)
}
// 右键上传文件（内部订单）
export async function UploadFile1(params) {
    return request(`/api/app/e-mSTProc-production-process/up-file`, METHOD.POST, params)
}
// 上传(地址、ID、type)
export async function orderFileUpload(Id, params) {
    return request(`/api/app/e-mSTProc-production-process/up-guid4CNCFile/${Id}`, METHOD.GET, params)
}
// 下载
export async function downLoad(Id, params) {
    return request(`/api/app/e-mSTProc-production-process/down-load-path/${Id}`, METHOD.GET, params)
}
// 呼叫小车
export async function CallTrolley() {
    return request(`/api/app/e-mSTProc-production-process/call-trolley`, METHOD.POST)
}
// 人员确认
export async function Confirm() {
    return request(`/api/app/e-mSTProc-production-process/confirm`, METHOD.POST)
}
// 取消小车
export async function AgvCancel() {
    return request(`/api/app/e-mSTProc-production-process/agv-cancel`, METHOD.POST)
}
// 锁定文件
export async function FileLock(Id) {
    return request(`/api/app/e-mSTProc-production-process/file-lock/${Id}`, METHOD.GET)
}

export default {
    getWaitOrderList,
    getDoingOrderList,
    getStatisticsList,
    getMachineList,
    getDrillHoleList,
    getDispatchList,
    getDispatchMachineList,
    getDispatchFallbackList,
    getDoneOrderList,
    getpassStepNum,
    OverSequence,
    getFactoryList,
    UploadFile,
    UploadFileTgz,
    UploadFile1,
    orderFileUpload,
    UploadOrder,
    updatePnl,
    getHarmomegathusRegister,
    SetUpExpediting,
    DataCheck,
    DeleteOrder,
    ExceptionRemarks,
    getOrderMuIdList,
    signMachineStatus,
    downLoad,
    CallTrolley,
    Confirm,
    AgvCancel,
    updateRout,
    FileLock,
}