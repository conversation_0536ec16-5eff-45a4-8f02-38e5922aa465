<!-- 工程管理 - 工程派单 -->
<template>
  <div class="projectDispatchParent">
    <div class="projectDispatch" ref="SelectBox">
      <div class="contentBox" ref="contentBox">
        <div class="contentLeft">
          <left-table
            :dataSource="orderListData"
            :pagination="pagination"
            @tableChange="handleTableChange"
            :orderListTableLoading="orderListTableLoading"
            @assignOrderListChange="assignOrderListChange"
            ref="orderTable"
            class="leftstyle"
          />
          <!-- <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
            <a-menu-item >
              <a-upload
                   accept=".rar,.zip"
                   name="file"
                   :before-upload="beforeUpload"
                   :customRequest="httpRequest0"
               >
                 上传原稿
               </a-upload>
            </a-menu-item>             
          </a-menu> -->
        </div>
        <div v-if="advanced" style="width: 19.5%; height: 100%; display: flex; flex-wrap: wrap">
          <div class="contentRC" :class="showtop ? 'back' : ''">
            <a-table
              :dataSource="TotalListData"
              :columns="columns"
              :pagination="false"
              :scroll="{ x: 300 }"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
            >
              <template slot="pauseCount" slot-scope="record">
                <div @click.stop="detailsdata(record, 'PauseCount')" style="color: #428bca; cursor: pointer">
                  {{ record.pauseCount }}
                </div>
              </template>
              <template slot="verifyCount" slot-scope="record">
                <div @click.stop="detailsdata(record, 'VerifyCount')" style="color: #428bca; cursor: pointer">
                  {{ record.verifyCount }}
                </div>
              </template>
              <template slot="backMakeCount" slot-scope="record">
                <div @click.stop="detailsdata(record, 'BackMakeCount')" style="color: #428bca; cursor: pointer">
                  {{ record.backMakeCount }}
                </div>
              </template>
              <template slot="makeCount" slot-scope="record">
                <div @click.stop="detailsdata(record, 'MakeCount')" style="color: #428bca; cursor: pointer">
                  {{ record.makeCount }}
                </div>
              </template>
            </a-table>
          </div>
          <div class="contentCenter">
            <center-table
              :dataSource="producerListData"
              @counts="counts"
              @wenkeCounts="wenkeCounts"
              @numendCounts="numendCounts"
              :producerTabLoading="producerTabLoading"
              @getProducerInfo="getProducerInfo"
              @assignPeopleChange="assignPeopleChange"
              @showEditInfoModal="showEditInfoModal"
              ref="centerTable"
              :yScroll="yScroll - 23"
              class="centerstyle"
            />
          </div>
        </div>
        <div style="width: 29.5%; height: 100%; display: flex; flex-wrap: wrap">
          <div class="contentRight">
            <div class="bto" v-if="!showtop">
              <right-table
                :yScroll="yScroll"
                :dataSource="producerPeopleInfo"
                :producerInfoLoading="producerInfoLoading"
                @backClick="backClick"
                class="rightstyle"
              />
            </div>
            <div class="bto" v-if="showtop">
              <a-table
                :columns="columns1"
                :dataSource="data1Source"
                :scroll="{ y: 738, x: 251 }"
                class="rightstyle1"
                :rowKey="
                  (record, index) => {
                    return index;
                  }
                "
                :pagination="false"
              >
                <div slot="pdct_NAME_" slot-scope="text, record">
                  <span style="color: black" :title="record.pdct_NAME_">{{ record.pdct_NAME_ }}</span
                  >&nbsp;
                  <span class="tagNum" style="display: inline-block; height: 19px">
                    <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
                      <span
                        class="noCopy"
                        style="
                          font-size: 14px;
                          font-weight: 500;
                          color: #ff9900;
                          padding: 0;
                          display: inline-block;
                          height: 19px;
                          width: 18px;
                          margin-left: -10px;
                          user-select: none;
                        "
                        ><a-icon type="thunderbolt" theme="filled"></a-icon>
                      </span>
                      <span
                        class="noCopy"
                        style="
                          font-size: 14px;
                          font-weight: 500;
                          color: #ff9900;
                          padding: 0;
                          display: inline-block;
                          height: 19px;
                          width: 18px;
                          margin-left: -10px;
                          user-select: none;
                        "
                        ><a-icon type="thunderbolt" theme="filled"></a-icon>
                      </span>
                    </a-tooltip>
                    <a-tooltip title="加急" v-else-if="record.isJiaji">
                      <span
                        class="noCopy"
                        style="
                          font-size: 14px;
                          font-weight: 500;
                          color: #ff9900;
                          padding: 0;
                          display: inline-block;
                          height: 19px;
                          width: 18px;
                          margin-left: -10px;
                          user-select: none;
                        "
                        ><a-icon type="thunderbolt" theme="filled"></a-icon>
                      </span>
                    </a-tooltip>
                    <a-tag
                      class="noCopy"
                      v-if="record.isJunG"
                      color="#2D221D"
                      style="
                        font-size: 12px;
                        background: #428bca;
                        color: white;
                        padding: 0 2px;
                        margin: 0;
                        margin-right: 3px;
                        height: 21px;
                        user-select: none;
                        border: 1px solid #428bca;
                      "
                    >
                      {{ record.joinFactoryId == 70 ? "J" : "军" }}
                    </a-tag>
                    <a-tooltip title="新客户" v-if="record.isNewCust">
                      <a-tag
                        class="noCopy"
                        style="
                          font-size: 12px;
                          background: #428bca;
                          color: white;
                          padding: 0 2px;
                          margin: 0;
                          margin-right: 3px;
                          height: 21px;
                          user-select: none;
                          border: 1px solid #428bca;
                        "
                      >
                        新
                      </a-tag>
                    </a-tooltip>
                  </span>
                </div>
                <template slot="labelUrl" slot-scope="record">
                  <span @click.stop="OperationLog(record.guid_)" style="color: #428bca; cursor: pointer">日志</span>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
      <div class="actionBox" ref="active" style="width: 100%">
        <a-form-model layout="inline">
          <a-form-model-item class="showClass">
            <a-button type="primary" @click="onSearch"> 查询(F) </a-button>
          </a-form-model-item>
          <a-form-model-item
            v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngineeringDispatchMISendOrder')"
            :class="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngineeringDispatchMISendOrder') ? 'showClass' : ''"
          >
            <a-button type="primary" @click="assign"> 分派(S) </a-button>
          </a-form-model-item>
          <!-- <a-form-model-item  v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.OrderInput')">
            <a-button type="primary" @click ="excelClick">
            订单导入
            </a-button>
            <a-upload
                accept=".xlsx,.xls"
                name="file"
                ref="fileRef"
                :before-upload="beforeUpload1"
                :customRequest="httpRequest1"
            >
              <a-button style="width: 80px;display: none;"><a-icon type="upload" /></a-button>
            </a-upload>
        </a-form-model-item>
        <a-form-model-item v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.UpLoadYg')" >
            <a-button type="primary" @click ="uploadClick">
            上传原稿
            </a-button>
            <a-upload
              accept=".rar,.zip"
              name="file"
              ref="fileup"
              :before-upload="beforeUpload"
              :customRequest="httpRequest0"
            > 
            <a-button style="width: 80px;display: none;"><a-icon type="upload" /></a-button>             
            </a-upload>
        </a-form-model-item> -->

          <a-form-model-item
            v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.Pause')"
            :class="checkPermission('MES.EngineeringModule.EngineeringDispatch.Pause') ? 'showClass' : ''"
          >
            <a-button type="primary" @click="suspend"> 暂停 </a-button>
          </a-form-model-item>
          <a-form-model-item
            v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.Cancel')"
            :class="checkPermission('MES.EngineeringModule.EngineeringDispatch.Cancel') ? 'showClass' : ''"
          >
            <a-button type="primary" @click="Unpause"> 取消 </a-button>
          </a-form-model-item>
          <a-form-model-item class="showClass">
            <a-button type="primary" @click="PerformanceClick"> 绩效管理 </a-button>
          </a-form-model-item>
          <a-form-model-item
            v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngPreContractReviewInfo')"
            :class="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngPreContractReviewInfo') ? 'showClass' : ''"
          >
            <a-button type="primary" @click="ReviewSheet"> 评审单 </a-button>
          </a-form-model-item>
          <a-form-model-item
            v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngPreContractNoticeReview')"
            :class="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngPreContractNoticeReview') ? 'showClass' : ''"
          >
            <a-button type="primary" @click="ProductionOrder"> 生产单 </a-button>
          </a-form-model-item>
          <a-form-model-item
            v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.JiaJi')"
            :class="checkPermission('MES.EngineeringModule.EngineeringDispatch.JiaJi') ? 'showClass' : ''"
          >
            <a-button type="primary" @click="SetUrgent"> 加急 </a-button>
          </a-form-model-item>
          <a-form-model-item v-if="showBtn">
            <span class="box">
              <a-button type="dashed" @click="toggleAdvanced">
                {{ advanced1 ? "收起" : "展开" }}
                <a-icon :type="advanced1 ? 'right' : 'left'" />
              </a-button>
            </span>
          </a-form-model-item>
          <a-form-model-item>
            <div v-if="buttonsmenu">
              <a-dropdown>
                <a-button type="primary" @click.prevent> 按钮菜单栏 </a-button>
                <template #overlay>
                  <a-menu class="tabRightClikBox">
                    <a-menu-item @click="onSearch">查询(F)</a-menu-item>
                    <a-menu-item @click="assign" v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngineeringDispatchMISendOrder')"
                      >分派(S)</a-menu-item
                    >
                    <a-menu-item @click="SetUrgent" v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.JiaJi')">加急</a-menu-item>
                    <a-menu-item @click="suspend" v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.Pause')">暂停</a-menu-item>
                    <a-menu-item @click="Unpause" v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.Cancel')">取消</a-menu-item>
                    <a-menu-item @click="PerformanceClick">绩效管理</a-menu-item>
                    <a-menu-item @click="ReviewSheet" v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngPreContractReviewInfo')"
                      >评审单</a-menu-item
                    >
                    <a-menu-item
                      @click="ProductionOrder"
                      v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngPreContractNoticeReview')"
                      >生产单</a-menu-item
                    >
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </a-form-model-item>
          <!-- <a-form-model-item v-if="checkPermission('MES.EngineeringModule.EngineeringDispatch.EngineeringDispatchUpFactoryId')" >
          <a-button type="primary" @click="edit">修改工厂</a-button>
        </a-form-model-item> -->
        </a-form-model>
      </div>
      <a-modal
        title="分派信息"
        :visible="assignModalVisible"
        :confirmLoading="confirmLoading"
        ok-text="确定"
        cancel-text="取消(C)"
        @ok="assingOk"
        @cancel="handleCancel"
        centered
      >
        <h3 style="font-weight: 500; color: #000000; font-size: 14px">是否将订单号为：</h3>
        <div class="tabBox">
          <a-tag color="orange" v-for="(item, index) in assignOrderList1" :key="index + '_assign'">
            {{ item }}
          </a-tag>
        </div>
        <div style="font-weight: 500; color: #000000">分派给：{{ assignPeople.realName }}</div>
      </a-modal>
      <!-- <a-modal title="检查项提示" 
      :visible="dalVisible1"
      @ok="clickok"
      @cancel="clicktocancel" 
        ><div class="class" style="font-size:16px"> -->
      <!-- <span v-if="producerPeopleInfo.length>20">该人员未完成订单数大于20个,不能分派！</span> -->
      <!-- <div v-if></div>
          <h3>该订单：</h3>
        <div class="tabBox">
          <a-tag color="orange" v-for="(item, index) in assignOrderList1" :key="index + '_assign'"> {{ item }} </a-tag>
        </div> -->
      <!-- <p v-for="(item,index) in checkData" :key="index" >
      <span v-if="item.error == '1'" style="color:red">
        <a-icon type="star"></a-icon>
        <span>{{item.caption}}:</span>
        <span>{{item.result}}!</span> 
      </span>
    </p> -->
      <!-- </div>
      </a-modal> -->
      <model-form
        :data="editInfoList"
        :btnLoading="editOkBtnLoading"
        :leaveVisible="editInfoModalVisible"
        :leaveLoading="editInfoLoading"
        @leaveHandleCancel="leaveHandleCancel"
        @leaveHandleOk="leaveHandleOk"
        @statusClick="statusClick"
        :producerListData="producerListData"
        :confirmLoading="confirmLoading"
        ref="editInfo"
      />
      <a-modal
        title="订单查询"
        :visible="searchModalVisible"
        @cancel="searchHandleCancel"
        @ok="searchHandleOk"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        centered
      >
        <search-modal ref="queryInfo" />
      </a-modal>
      <!--红马生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="HMnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="hmnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="850"
      >
        <report-hmnoticeform :ttype="'EMS | 工程派单'" :HMnoticedata="HMnoticedata" ref="hmnotice"></report-hmnoticeform>
      </a-modal>
      <!--联合多层生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="LHDCnoticeVisible"
        @cancel="reportHandleCancel"
        @ok="lhdcnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1400"
      >
        <report-lhdcnoticeform :ttype="'EMS | 工程派单'" :LHDCnoticedata="LHDCnoticedata" ref="lhdcnotice"></report-lhdcnoticeform>
      </a-modal>
      <!--龙腾生产通知单-->
      <a-modal
        title="生产通知单"
        :visible="LTnoticeVisible"
        @cancel="LTnoticeVisible = false"
        @ok="ltnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1400"
      >
        <report-ltnoticeform :ttype="'EMS | 工程派单'" :LTnoticedata="LTnoticedata" ref="ltnotice"></report-ltnoticeform>
      </a-modal>
      <!--雅信达评审单-->
      <a-modal
        title="评审单"
        :visible="YXDnoticeVisible"
        @cancel="YXDnoticeVisible = false"
        @ok="yxdnoticedown"
        ok-text="下载"
        destroyOnClose
        :maskClosable="false"
        class="formclass"
        :width="1200"
      >
        <report-yxdnoticeform :ttype="'EMS | 工程派单'" :YXDnoticedata="YXDnoticedata" ref="yxdnotice"></report-yxdnoticeform>
      </a-modal>
      <!-- 确认弹窗 -->
      <a-modal
        :title="state == 'cancel' ? '订单取消' : state == 'suspend' ? '订单暂停' : '确认弹窗'"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="450"
        centered
      >
        <div v-if="state == 'cancel'">
          <span style="font-weight: bold"
            >【<span style="color: red">{{ orderno }}</span
            >】取消原因:</span
          >
          <div>
            <a-textarea v-model="CancelCause" :autoFocus="true" :auto-size="{ minRows: 2, maxRows: 5 }" style="width: 100%; margin-top: 8px" />
          </div>
        </div>
        <div v-else-if="state == 'suspend'">
          <span style="font-weight: bold"
            >【<span style="color: red">{{ orderno }}</span
            >】暂停原因:</span
          >
          <div>
            <a-textarea v-model="PauseCause" :autoFocus="true" :auto-size="{ minRows: 2, maxRows: 5 }" style="width: 100%; margin-top: 8px" />
          </div>
        </div>
        <div v-else>
          <span style="font-size: 15px; color: #000000">【{{ orderno }}】</span>
          <span style="font-size: 15px; color: #000000">{{ message }}</span>
        </div>
      </a-modal>
      <!-- 修改工厂 -->
      <a-modal
        title="修改工厂"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <a-form-model-item label="工厂" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }" style="width: 100%; margin: 0">
          <a-select
            v-model="factoryId"
            optionFilterProp="label"
            showSearch
            allowClear
            style="width: 150px"
            :getPopupContainer="() => this.$refs.SelectBox"
          >
            <a-select-option v-for="item in comBoxItems" :key="item.factoryId" :value="item.factoryId" :label="item.factoryIdName">
              {{ item.factoryIdName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-modal>
      <!-- 绩效管理 -->
      <performance
        ref="performanceModal"
        :ids="ids"
        @getProducerList="getProducerList"
        @getProducerInfo="getProducerInfo"
        :selectedRowsData="selectedRowsData"
        :type="-1"
      >
      </performance>
    </div>
  </div>
</template>

<script>
import ReportLhdcnoticeform from "@/pages/mkt/OrderOffer/report/ReportLhdcnoticeform";
import ReportLtnoticeform from "@/pages/mkt/OrderOffer/report/ReportLtnoticeform";
import ReportYxdnoticeform from "@/pages/mkt/OrderOffer/report/ReportYxdnoticeform";
import ReportHmnoticeform from "@/pages/mkt/OrderOffer/report/ReportHmnoticeform";
import { checkPermission } from "@/utils/abp";
import { contractReviewInfo, noticereviewinfo, orderproductionreport, productionreport } from "@/services/mkt/OrderReview.js";
import {
  projectDispathgetOrderList1,
  projectOrderAssign,
  projectOrderInfo,
  projectOrderLeave,
  projectPeopleInfo1,
  projectPeopleList,
  dispatchTotal,
  BackOrder,
  upLoadPcbFile,
  projectTotalList,
  setPause,
  setCancel,
  SetUrgent,
  joinFactoryId,
  setUpJoinFactoryId,
  miTotalordersdetail,
} from "@/services/projectDisptch";
import SearchModal from "@/pages/gongcheng/projectDisptchTest/components/SearchModal";
import ModelForm from "@/pages/gongcheng/projectDisptchTest/components/ModelForm";
import LeftTable from "@/pages/gongcheng/projectDisptchTest/components/LeftTable";
import CenterTable from "@/pages/gongcheng/projectDisptchTest/components/CenterTable";
import RightTable from "@/pages/gongcheng/projectDisptchTest/components/RightTable";
import moment from "moment";
import { ImportOrder } from "@/services/projectMake";
import Performance from "@/pages/gongcheng/projectMake/subassembly/Performance";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 40,
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "本厂编码",
    dataIndex: "pdct_NAME_",
    scopedSlots: { customRender: "pdct_NAME_" },
    align: "left",
    width: 190,
    ellipsis: true,
  },
  {
    title: "状态",
    dataIndex: "state_",
    align: "left",
    ellipsis: true,
    width: 75,
  },
  {
    title: "派单时间",
    dataIndex: "dispatchDate_",
    align: "left",
    width: 126,
    ellipsis: true,
    sorter: (a, b) => {
      return a.dispatchDate_.localeCompare(b.dispatchDate_);
    },
  },
  {
    title: "操作",
    align: "center",
    scopedSlots: { customRender: "labelUrl" },
    width: 40,
    className: "userStyle noCopy",
  },
];
export default {
  name: "ProjectDispatchTest",
  components: {
    RightTable,
    LeftTable,
    CenterTable,
    ModelForm,
    SearchModal,
    Performance,
    ReportHmnoticeform,
    ReportLhdcnoticeform,
    ReportLtnoticeform,
    ReportYxdnoticeform,
  },
  inject: ["reload"],
  data() {
    return {
      state: "",
      ids: 0,
      LHDCnoticedata: {},
      LHDCnoticeVisible: false,
      LTnoticedata: {},
      LTnoticeVisible: false,
      YXDnoticedata: {},
      YXDnoticeVisible: false,
      HMnoticedata: {},
      HMnoticeVisible: false,
      showtop: false,
      PauseCause: "",
      CancelCause: "",
      iscolumnKey: false,
      isorder: false,
      showBtn: false,
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      advanced1: false,
      checkData: [], // 提示检查数据
      spinning: false,
      orderListTableLoading: false,
      producerTabLoading: false,
      producerInfoLoading: false,
      conditions: {},
      orderListData: [],
      producerListData: [],
      TotalListData: [],
      producerPeopleInfo: [],
      assignPeople: "", // 分派人员
      assignOrderList: [], // 分派底单列表
      assignOrderList1: [], // 分派单号
      assignModalVisible: false, // 分派弹窗
      dalVisible1: false,
      visibleDrawer: false, // 明细抽屉
      advanced: true, //  人员关闭
      confirmLoading: false, // 分派确认按钮loading
      editInfoList: {
        labels: 0,
        targetCount_: 0,
        maxNum: 0,
        getNum: 0,
        stayNum: 0,
        boardSzie: "",
        isLeave_: false,
        layer1: false,
        layer2: false,
        layer4: false,
        layer6: false,
        layer8: false,
        warIndustry: false,
        blindVias: false,
        pcs: false,
        customerSet: false,
        jpSet: false,
        tradeTypeSrc: [],
        isRandom: false,
        autoStartDate: null, // 开始时间
        autoEndDate: null, // 结束时间
        urgent: false,
        sort: 0,

        // userNo_: "",
        // isScript_: false,
        // isHighQuality: 0,
        // // "lengthNew": "",
        // // "widthNew": "",
        // aluminum: false,
        // nonAluminum: false,
        // domesticTrade: false,
        // foreignTrade: false,
        // ordinary: false,
        // premiumProducts: false,
        // boutique: false,
        // area1: false,
        // area2: false,
        // area3: false,
        // area4: false,
        // area5: false,
        // area6: false,
        // noCoordination: false,
        // isOutAssist:false,
        // coordination: false,
        // bigCus: false,
        // noBigCus: false,
        // tradeType: 0,
        // jdbOrder: false,
        // dqOrder: false,
        // jpOrder: false,

        // isReverse: false,
        // jlcOrder: false, // 嘉立创
        // hqOrder: false, // 华秋
        // lhdcOrder: false, // 联合多层
        // plOrder: false, // 普林
        // tlOrder: false, // 塔联
        // hzxOrder: false, // 合众鑫
        // xsjOrder: false, // 兴晟捷
        // jdOrder: false, // 吉达
        // imp: false, // 阻抗
        // newOrder: false, // 新单
        // returnOrder: false, // 新单
      },
      editOkBtnLoading: false, // 编辑制作人信息确定按钮loading
      editInfoLoading: false,
      editInfoModalVisible: false, // 制作人员信息编辑弹窗
      searchModalVisible: false,
      dispatchTotalData: [],
      loading: false,
      yScroll: 0,
      pagination: {
        pageSize: 20,
        current: 1,
        simple: false,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计${total}条`,
      },
      topName: "",
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      dataVisible3: false,
      buttonsmenu: false,
      message: "",
      orderno: "",
      recordGuid_: "",
      dataVisible4: false,
      factoryId: "",
      comBoxItems: [],
      columns1,
      data1Source: [],
      columns: [
        {
          title: "总计",
          dataIndex: "totalCount",
        },
        {
          title: "停留",
          dataIndex: "stayCount",
        },
        {
          title: "待派",
          dataIndex: "waitSendCount",
        },
        {
          title: "前端",
          scopedSlots: { customRender: "makeCount" },
        },
        {
          title: "后端",
          scopedSlots: { customRender: "backMakeCount" },
        },
        {
          title: "审核",
          scopedSlots: { customRender: "verifyCount" },
        },
        {
          title: "暂停",
          scopedSlots: { customRender: "pauseCount" },
        },
        {
          title: "问客",
          dataIndex: "eqCount",
        },
      ],
      selectedRowsData: {},
      isCtrlPressed: false,
    };
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      let buttonsToShow = 8;
      if (this.$refs.active.children.length > 8) {
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      if (num <= 8) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced1 = false;
      }
      var paginationstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var paginationstyle1 =
        document.getElementsByClassName("centerstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var paginationstyle2 =
        document.getElementsByClassName("rightstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var paginationstyle3 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1]
          .children[0];
      if (paginationstyle) {
        paginationstyle.style.height = window.innerHeight - 174 + "px";
        paginationstyle3.style.height = window.innerHeight - 174 + "px";
      }
      if (paginationstyle1) {
        paginationstyle1.style.height = window.innerHeight - 245 + "px";
      }
      if (paginationstyle2) {
        paginationstyle2.style.height = window.innerHeight - 173 + "px";
      }
    });
  },
  methods: {
    checkPermission,
    OperationLog(guid_) {
      this.$refs.orderTable.OperationLog(guid_);
    },
    handleResize() {
      this.screenWidth = window.innerWidth;
      this.screenHeight = window.innerHeight;
      var paginationstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var paginationstyle3 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1]
          .children[0];
      var paginationstyle1 =
        document.getElementsByClassName("centerstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];

      if (this.showtop) {
        var paginationstyle4 =
          document.getElementsByClassName("rightstyle1")[0].children[0].children[0].children[0].children[0].children[0].children[1];
        if (paginationstyle4) {
          paginationstyle4.style.height = this.screenHeight - 173 + "px";
        }
      } else {
        var paginationstyle2 =
          document.getElementsByClassName("rightstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
        var paginationstyle6 =
          document.getElementsByClassName("rightstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1];
        if (paginationstyle2) {
          paginationstyle2.style.height = this.screenHeight - 173 + "px";
          paginationstyle6.style.height = this.screenHeight - 173 + "px";
        }
      }
      if (paginationstyle) {
        paginationstyle.style.height = this.screenHeight - 174 + "px";
        paginationstyle3.style.height = this.screenHeight - 174 + "px";
      }
      if (paginationstyle1) {
        paginationstyle1.style.height = this.screenHeight - 245 + "px";
      }

      var elements = document.getElementsByClassName("showClass");
      const num = elements.length * 104;
      var footerwidth = this.screenWidth - 224;
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth / 2) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      if (this.screenWidth < 1920 || this.screenHeight < 923) {
        if (paginnum * 50 + 310 < footerwidth / 2 && elements.length * 104 + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }

        if (paginnum * 25 + 200 + num < this.screenWidth - 150 && this.screenWidth > 766) {
          if (footerwidth / 2 < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
          this.buttonsmenu = false;
          for (let i = 0; i < elements.length; i++) {
            elements[i].style.display = "inline-block";
          }
        } else {
          if (this.screenWidth > 766) {
            if (footerwidth / 2 < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
            this.buttonsmenu = true;
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
          } else {
            this.pagination.simple = true;
            if (this.screenWidth - 4 - num < 70) {
              this.buttonsmenu = true;
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
              }
            } else {
              this.buttonsmenu = false;
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "inline-block";
              }
            }
          }
        }
      } else {
        this.buttonsmenu = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
        this.pagination.simple = false;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      }
      this.$forceUpdate();
    },
    detailsdata(record, type) {
      this.$refs.centerTable.selectedRowKeysArray = [];
      this.$refs.centerTable.userNo = "";
      this.assignPeople = "";
      this.showtop = true;
      if (type == "PauseCount" && columns1.length == "5") {
        columns1.splice(4, 0, {
          title: "暂停原因",
          dataIndex: "pauseCause",
          align: "left",
          width: 200,
          ellipsis: true,
        });
      } else if (type != "PauseCount" && columns1.length == "6") {
        columns1.splice(4, 1);
      }
      miTotalordersdetail(type).then(res => {
        this.data1Source = res;
      });
      setTimeout(() => {
        this.handleResize();
      }, 0);
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.onSearch(); //查询
        this.isCtrlPressed = false;
        this.assignModalVisible = false;
        this.dataVisible3 = false;
        this.dataVisible4 = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.assignModalVisible) {
        this.assingOk(); //分派确定
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.searchModalVisible) {
        this.searchHandleOk(); //查询确定
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible3) {
        this.handleOk3(); //确认弹窗确定
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (
        e.keyCode == "67" &&
        !this.isCtrlPressed &&
        (this.assignModalVisible || (this.dataVisible3 && this.state != "cancel" && this.state != "suspend"))
      ) {
        this.assignModalVisible = false; //分派取消
        this.dataVisible3 = false; //确认弹窗取消
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (
        e.keyCode == "83" &&
        this.isCtrlPressed &&
        checkPermission("MES.EngineeringModule.EngineeringDispatch.EngineeringDispatchMISendOrder")
      ) {
        this.assign();
        this.isCtrlPressed = false;
        this.searchModalVisible = false;
        this.dataVisible3 = false;
        this.dataVisible4 = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    toggleAdvanced() {
      this.advanced1 = !this.advanced1;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced1) {
        width_ = 1697;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        width_ = 1697;
        let buttonsToShow = 8;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.$refs.active.style.width = width_ + "px";
    },
    // 获取订单列表
    getOrderList() {
      let params = {
        pageIndex: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };

      const { conditions } = this;
      var data = { ...conditions };
      var b = JSON.stringify(data) == "{}";
      if (!b) {
        params.Pdctno = data.Pdctno;
        params.States = data.States;
        params.pcbFileName = data.pcbFileName;
      }
      params.pcbFileName = params.pcbFileName ? params.pcbFileName.replace(/\s+/g, " ").trim() : "";
      this.orderListTableLoading = true;
      projectDispathgetOrderList1(params)
        .then(result => {
          if (result.code) {
            this.orderListData = result.data.items;
            if (this.iscolumnKey) {
              this.orderListData.sort((a, b) => {
                let aValue = a[this.iscolumnKey];
                let bValue = b[this.iscolumnKey];
                // 添加备用排序规则，使用行索引
                if (aValue === bValue) {
                  return this.orderListData.indexOf(a) - this.orderListData.indexOf(b);
                }
                if (this.iscolumnKey == "boardSize" || this.iscolumnKey == "pinBanType_") {
                  if (this.isorder == "ascend") {
                    return eval(aValue.replace(/x/g, "*")) > eval(bValue.replace(/x/g, "*")) ? 1 : -1;
                  } else if (this.isorder == "descend") {
                    return eval(bValue.replace(/x/g, "*")) > eval(aValue.replace(/x/g, "*")) ? 1 : -1;
                  }
                  return 0;
                } else {
                  if (this.isorder == "ascend") {
                    return aValue > bValue ? 1 : -1;
                  } else if (this.isorder == "descend") {
                    return aValue < bValue ? 1 : -1;
                  }
                  return 0;
                }
              });
            }
            const pagination = { ...this.pagination };
            pagination.total = result.data.totalCount;
            this.pagination = pagination;
            setTimeout(() => {
              this.handleResize();
            }, 0);
            if ((params.Pdctno || params.States || params.pcbFileName) && this.orderListData.length) {
              this.$refs.orderTable.selectedRowKeys[0] = this.orderListData[0].proOrderId;
              this.assignOrderList = [this.orderListData[0].proOrderId];
              this.assignOrderList1[0] = this.orderListData[0].pdct_NAME_;
              this.$refs.orderTable.selectedRowsData = this.orderListData[0];
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    // 获取工程派单管理汇总
    getDispatchTotal() {
      this.loading = true;
      dispatchTotal()
        .then(res => {
          if (res.code) {
            this.dispatchTotalData = res.data;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取制作人员列表
    getProducerList() {
      this.producerTabLoading = true;
      projectPeopleList().then(res => {
        this.producerTabLoading = false;
        if (res) {
          this.producerListData = res;
        }
      });
    },
    // 获取统计列表
    getTotalList() {
      projectTotalList().then(res => {
        if (res) {
          this.TotalListData = res;
        }
      });
    },
    // 抽屉打开
    counts(record) {
      this.getProducerInfo(record.userNo, "2"); // 停留
      this.topName = record.realName + " - " + "停留" + " - " + "明细";
      this.visibleDrawer = true;
    },
    wenkeCounts(record) {
      this.getProducerInfo(record.userNo, "1");
      this.topName = record.realName + " - " + "问客" + " - " + "明细";
      this.visibleDrawer = true;
    },
    numendCounts(record) {
      this.getProducerInfo(record.userNo, "3");
      this.topName = record.realName + " - " + "完成" + " - " + "明细";
      this.visibleDrawer = true;
    },
    // 抽屉关闭
    onClose() {
      this.visibleDrawer = false;
    },
    // 获取制作人员详情
    getProducerInfo(id, type) {
      this.producerInfoLoading = true;
      var type1 = 0;
      this.ids = id;
      projectPeopleInfo1(id, type1).then(res => {
        this.producerPeopleInfo = res;
        this.showtop = false;
        setTimeout(() => {
          this.handleResize();
        }, 0);
        this.producerInfoLoading = false;
      });
    },
    onSearch() {
      this.searchModalVisible = true;
    },
    searchHandleOk() {
      let payload = this.$refs.queryInfo.form;
      var arr1 = payload.Pdctno.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      payload.Pdctno = arr1.join("");
      if (payload.Pdctno && typeof payload.Pdctno === "string" && payload.Pdctno.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产编号查询不能小于5位");
        return;
      }
      this.confirmLoading = true;
      let _newPar = {};
      for (let key in payload) {
        if ((payload[key] === 0 || payload[key]) && payload[key].toString().replace(/(^\s*)|(\s*$)/g, "") !== "") {
          _newPar[key] = payload[key];
        }
      }
      this.pagination.current = 1;
      this.conditions = _newPar;
      this.getOrderList();
      this.confirmLoading = false;
      this.searchModalVisible = false;
    },
    searchHandleCancel() {
      this.searchModalVisible = false;
    },
    assignPeopleChange(payload) {
      this.assignPeople = payload;
    },
    assignOrderListChange(payload) {
      var arr = [];
      for (var i = 0; i < payload.length; i++) {
        var str = this.orderListData.filter(ite => {
          return ite.proOrderId == payload[i];
        })[0].pdct_NAME_;
        arr.push(str);
      }
      this.assignOrderList1 = arr;
      this.assignOrderList = payload;
    },
    // 显示
    showDrawer() {
      this.advanced = !this.advanced;
    },

    // 分派
    assign() {
      if (this.assignOrderList.length > 0 && this.assignPeople != "") {
        this.assignModalVisible = true;
      } else if (this.assignOrderList.length == 0 && this.assignPeople == "") {
        this.$message.warning("请选择订单和制作人员");
      } else if (this.assignOrderList.length == 0) {
        this.$message.warning("请选择订单");
      } else if (this.assignPeople == "") {
        this.$message.warning("请选择制作人员");
      }
    },
    //分派指示检查
    clicktocancel() {
      this.dalVisible1 = false;
    },
    clickok() {
      this.dalVisible1 = false;
    },
    // 分派确定
    assingOk() {
      this.confirmLoading = true;
      let params = {
        ids: this.assignOrderList,
        miAccID_: this.assignPeople?.userNo,
      };
      projectOrderAssign(params).then(res => {
        if (res) {
          if (res?.code == 1) {
            this.$message.success("分配成功");
            setTimeout(() => {
              this.visible = false;
              this.confirmLoading = false;
              this.getOrderList();
              this.getProducerList();
              this.getProducerInfo(this.assignPeople.userNo);
            }, 1000);
          } else {
            this.$message.error(res.message);
            this.visible = false;
            this.confirmLoading = false;
          }
        }
      });
      this.assignModalVisible = false;
      this.confirmLoading = false;
      this.assignOrderList = [];
      this.$refs.orderTable.selectedRowKeys = [];
    },
    // 分派回退
    backClick(record) {
      this.recordGuid_ = record.guid_;
      this.orderno = record.pdct_NAME_;
      this.message = "确认回退订单吗？";
      this.dataVisible3 = true;
      this.type = "4";
    },
    handleCancel() {
      this.assignModalVisible = false;
    },
    showEditInfoModal(record) {
      this.editInfoModalVisible = true;
      projectOrderInfo(record.userNo).then(res => {
        if (res.code) {
          res.data.realName = record.realName;
          res.data.groups = record.groups;
          if (!res.data.tradeTypeSrc) {
            res.data.tradeTypeSrc = [];
          }

          res.data.autoStartDate = moment(res.data.autoStartDate, "HH:mm");
          res.data.autoEndDate = moment(res.data.autoEndDate, "HH:mm");
          this.editInfoList = res.data;
          this.editInfoList.facName = record.facName;
          this.$refs.editInfo.getSupplier(record.userNo);
          if (!res.data.custNo) {
            res.data.custNo = [];
          } else {
            res.data.custNo = String(res.data.custNo).split(",");
          }
          if (!res.data.orderByCustNo) {
            res.data.orderByCustNo = [];
          } else {
            res.data.orderByCustNo = String(res.data.orderByCustNo).split(",");
          }
        }
      });
    },
    leaveHandleCancel(e) {
      this.editInfoModalVisible = false;
    },
    leaveHandleOk(e) {
      let a = /^\d+(\.\d+)?-\d+(\.\d+)?$/;
      let x = /^(\d|[1-9]\d+)(\.\d+)?$/;
      let y = /^\+?[0-9][0-9]*$/;
      let z = /^(\d+(\.\d+)?-\d+(\.\d+)?)(,(\d+(\.\d+)?-\d+(\.\d+)?))*$/;
      if (this.editInfoList.targetCount_ && !y.test(this.editInfoList.targetCount_)) {
        this.$message.error("目标数量请输入正确格式");
        this.$refs.editInfo.$refs.targetCount_.focus();
        this.$refs.editInfo.$refs.targetCount_.select();
        return;
      }
      if (this.editInfoList.maxNum && !y.test(this.editInfoList.maxNum)) {
        this.$message.error("每日总量请输入正确格式");
        this.$refs.editInfo.$refs.maxNum.focus();
        this.$refs.editInfo.$refs.maxNum.select();
        return;
      }
      if (this.editInfoList.getNum && !y.test(this.editInfoList.getNum)) {
        this.$message.error("单次获取请输入正确格式");
        this.$refs.editInfo.$refs.getNum.focus();
        this.$refs.editInfo.$refs.getNum.select();
        return;
      }
      if (this.editInfoList.stayNum && !y.test(this.editInfoList.stayNum)) {
        this.$message.error("停留数量请输入正确格式");
        this.$refs.editInfo.$refs.stayNum.focus();
        this.$refs.editInfo.$refs.stayNum.select();
        return;
      }
      if (!a.test(this.editInfoList.boardSzie) && this.editInfoList.boardSzie) {
        this.$message.error("出货尺寸请输入正确的区间格式 如：0-5");
        this.$refs.editInfo.$refs.boardSzie.focus();
        this.$refs.editInfo.$refs.boardSzie.select();
        return;
      }
      if (this.editInfoList.areaInterval && !z.test(this.editInfoList.areaInterval)) {
        this.$message.error("面积请输入正确的区间格式 如：0-5,如果多个区间的请用,分隔");
        this.$refs.editInfo.$refs.areaInterval.focus();
        this.$refs.editInfo.$refs.areaInterval.select();
        return;
      }
      this.confirmLoading = true;
      this.editInfoLoading = true;
      const params = {
        ...this.editInfoList,
        targetCount_: Number(this.editInfoList.targetCount_),
        getNum: Number(this.editInfoList.getNum),
        stayNum: Number(this.editInfoList.stayNum),
        maxNum: Number(this.editInfoList.maxNum),
        BoardSzie: this.editInfoList.boardSzie,
        autoStartDate: this.transformTimestamp(this.editInfoList.autoStartDate),
        autoEndDate: this.transformTimestamp(this.editInfoList.autoEndDate),
      };
      params.custNo = params.custNo ? params.custNo.join(",") : "";
      params.orderByCustNo = params.orderByCustNo ? params.orderByCustNo.join(",") : "";

      projectOrderLeave(params).then(res => {
        if (res.code == 1) {
          this.$message.success("操作成功");
        } else {
          this.$message.error("操作失败");
        }
        this.getProducerList();
        this.editInfoModalVisible = false;
        this.editInfoLoading = false;
        this.confirmLoading = false;
      });
    },
    // 处理时间格式
    transformTimestamp(timestamp) {
      let a = new Date(timestamp).getTime();
      const date = new Date(a);
      const h = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      const m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      // const s = date.getSeconds(); // 秒
      const dateString = h + m;
      return dateString;
    },
    statusClick() {
      // this.editOkBtnLoading = true;
      this.editInfoList.isLeave_ = !this.editInfoList.isLeave_;
      // const params = {
      //   isLeave_: !this.editInfoList.isLeave_,
      //   userNo_: this.editInfoList.userNo_,
      // };
      // projectOrderLeave(params).then((res) => {
      //   this.editOkBtnLoading = false;
      //   if (res.code == 1) {
      //     this.editInfoList.isLeave_ = !this.editInfoList.isLeave_;
      //     this.getProducerList();
      //   }
      // });
    },
    handleTableChange(pagination, filter, sorter) {
      let { columnKey, order } = sorter;
      this.iscolumnKey = columnKey;
      this.isorder = order;
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.pageStat = false;
      localStorage.removeItem("stat");
      this.getOrderList();
    },
    // 订单导入
    excelClick() {
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    beforeUpload1(file) {
      const isFileType = file.name.toLowerCase().indexOf(".xlsx") != -1 || file.name.toLowerCase().indexOf(".xls") != -1;
      if (!isFileType) {
        this.$message.error("只支持.xlsx或.xls格式文件");
      }
      return isFileType;
    },
    async httpRequest1(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      ImportOrder(formData).then(res => {
        if (res.code == 1) {
          this.$message.success("导入完成");
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 右键事件
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    // 上传原稿
    uploadClick() {
      if (this.$refs.orderTable.selectedRowKeys.length == 1) {
        this.$refs.fileup.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
      } else {
        this.$message.warning("请选择一个订单");
      }
    },
    beforeUpload(file) {
      const isFileType = file.name.toLowerCase().indexOf(".rar") != -1 || file.name.toLowerCase().indexOf(".zip") != -1;
      if (!isFileType) {
        this.$message.error("只支持.rar或.zip格式文件");
      }
      return isFileType;
    },
    async httpRequest0(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await upLoadPcbFile(this.$refs.orderTable.selectedRowKeys[0], formData).then(res => {
        if (res.code == 1) {
          this.$message.success("上传成功");
          this.getOrderList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel() {
      this.dataVisible3 = false;
      this.dataVisible4 = false;
      this.HMnoticeVisible = false;
      this.LHDCnoticeVisible = false;
    },
    handleOk3() {
      // 暂停
      let params = {
        id: this.$refs.orderTable.selectedRowKeys[0],
        pauseCancelState: 0,
        pauseCause: this.PauseCause,
        cancelCause: this.CancelCause,
      };
      if (this.type == "1") {
        if (this.state == "suspend" && this.PauseCause == "") {
          this.$message.warning("请填写暂停原因");
          return;
        }
        this.spinning = true;
        setPause(params)
          .then(res => {
            if (res.code) {
              this.$message.success("设置成功");
            } else {
              this.$message.error(res.message);
            }
            this.getTotalList();
            this.getOrderList();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.type == "2") {
        if (this.state == "cancel" && this.CancelCause == "") {
          this.$message.warning("请填写取消原因");
          return;
        }
        this.spinning = true;
        setCancel(params)
          .then(res => {
            if (res.code) {
              this.$message.success("设置成功");
            } else {
              this.$message.error(res.message);
            }
            this.getTotalList();
            this.getOrderList();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.type == "3") {
        this.spinning = true;
        SetUrgent(this.$refs.orderTable.selectedRowKeys[0])
          .then(res => {
            if (res.code) {
              this.$message.success("设置成功");
            } else {
              this.$message.error(res.message);
            }
            this.getOrderList();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      // 回退
      if (this.type == "4") {
        this.spinning = true;
        BackOrder(this.recordGuid_)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
              this.getOrderList();
              this.getProducerList();
              this.getProducerInfo(this.assignPeople.userNo);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }

      this.dataVisible3 = false;
    },
    // 暂停设置
    suspend() {
      if (this.$refs.orderTable.selectedRowKeys.length != 1) {
        this.$message.warning("请选择一个订单");
        return;
      }
      this.CancelCause = "";
      this.PauseCause = "";
      let str = this.orderListData.filter(ite => {
        return ite.proOrderId == this.$refs.orderTable.selectedRowKeys[0];
      })[0].pauseCancelState;
      this.orderno = this.assignOrderList1[0];
      if (str == "0" || str == "1" || str == "2") {
        this.message = "确认设置为【暂停】状态吗？";
        this.dataVisible3 = true;
        this.state = "suspend";
      }
      if (str == "3") {
        this.message = "确认恢复为【正常】状态吗？";
        this.dataVisible3 = true;
        this.state = "";
      }
      if (str == "4") {
        this.message = "确认恢复为【取消】状态？";
        this.dataVisible3 = true;
        this.state = "";
      }
      this.type = "1";
    },
    // 取消设置
    Unpause() {
      if (this.$refs.orderTable.selectedRowKeys.length != 1) {
        this.$message.warning("请选择一个订单");
        return;
      }
      this.CancelCause = "";
      this.PauseCause = "";
      let str = this.orderListData.filter(ite => {
        return ite.proOrderId == this.$refs.orderTable.selectedRowKeys[0];
      })[0].pauseCancelState;
      this.orderno = this.assignOrderList1[0];
      if (str == "0" || str == "1" || str == "3") {
        this.message = "确认设置为【取消】状态吗？";
        this.state = "cancel";
        this.dataVisible3 = true;
      }
      if (str == "2") {
        this.message = "确认恢复为【正常】状态吗？";
        this.state = "";
        this.dataVisible3 = true;
      }
      if (str == "4") {
        this.message = "确认恢复为【暂停】状态？";
        this.dataVisible3 = true;
        this.state = "";
      }
      this.type = "2";
    },
    // 设置加急
    SetUrgent() {
      if (this.$refs.orderTable.selectedRowKeys.length != 1) {
        this.$message.warning("请选择一个订单");
        return;
      }
      let str = this.orderListData.filter(ite => {
        return ite.proOrderId == this.$refs.orderTable.selectedRowKeys[0];
      })[0].isJiaji;
      this.orderno = this.assignOrderList1[0];
      if (str == true) {
        this.message = "确认设置为【非急件】吗？";
        this.dataVisible3 = true;
      }
      if (str == false) {
        this.message = "确认设置为【急件】吗？";
        this.dataVisible3 = true;
      }
      this.type = "3";
    },
    // 修改工厂
    edit() {
      if (this.$refs.orderTable.selectedRowKeys.length != 1) {
        this.$message.warning("请选择一个订单");
        return;
      }
      joinFactoryId().then(res => {
        if (res.code) {
          this.comBoxItems = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
      this.dataVisible4 = true;
    },
    handleOk4() {
      let params = {
        id: this.$refs.orderTable.selectedRowKeys[0],
        joinFactoryId: this.factoryId,
      };
      setUpJoinFactoryId(params).then(res => {
        if (res.code) {
          this.$message.success("修改成功");
          this.getOrderList();
          this.dataVisible4 = false;
        } else {
          this.message.error(res.message);
        }
      });
    },
    // 绩效管理
    PerformanceClick() {
      if (!this.$refs.orderTable.selectedRowKeys[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.selectedRowsData = this.$refs.orderTable.selectedRowsData;
      this.$refs.performanceModal.openModal(this.selectedRowsData);
    },
    //生产单
    ProductionOrder() {
      if (this.$refs.orderTable.selectedRowKeys.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowKeys.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 69) {
        this.HMnoticeform();
      } else if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 38) {
        this.LHDCnoticeform();
      } else if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 67) {
        this.LTnoticeform();
      } else {
        noticereviewinfo(this.$refs.orderTable.selectedRowKeys[0], 2).then(res => {
          if (res.code) {
            this.downloadByteArrayFromString(res.data, res.message);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    //联合多层生产通知单预览
    LHDCnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowKeys[0], 2).then(res => {
        if (res.code) {
          this.LHDCnoticedata = res.data;
          this.LHDCnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    lhdcnoticedown() {
      this.$refs.lhdcnotice.getnoticePdf();
    },
    //龙腾生产通知单预览
    LTnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowKeys[0], 2).then(res => {
        if (res.code) {
          this.LTnoticedata = res.data;
          this.LTnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    ltnoticedown() {
      this.$refs.ltnotice.getnoticePdf();
    },
    //雅信达评审单
    YXDnoticeform() {
      orderproductionreport(this.$refs.orderTable.selectedRowKeys[0], 2).then(res => {
        if (res.code) {
          this.YXDnoticedata = res.data;
          this.YXDnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    yxdnoticedown() {
      this.$refs.yxdnotice.getnoticePdf();
    },
    //红马生产通知单预览
    HMnoticeform() {
      productionreport(this.$refs.orderTable.selectedRowKeys[0], 2).then(res => {
        if (res.code) {
          this.HMnoticedata = res.data;
          this.HMnoticeVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    hmnoticedown() {
      this.$refs.hmnotice.getnoticePdf();
    },
    // 评审单
    ReviewSheet() {
      if (this.$refs.orderTable.selectedRowKeys.length == 0) {
        this.$message.warning("请选择一个订单");
        return;
      } else if (this.$refs.orderTable.selectedRowKeys.length != 1) {
        this.$message.warning("当前操作只能选择一个订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowsData.joinFactoryId == 58 || this.$refs.orderTable.selectedRowsData.joinFactoryId == 59) {
        this.YXDnoticeform();
      } else {
        contractReviewInfo(this.$refs.orderTable.selectedRowKeys[0], 2).then(res => {
          if (res.code) {
            this.downloadByteArrayFromString(res.data, res.message);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
  },
  mounted() {
    this.getTotalList();
    this.getOrderList();
    // this.getDispatchTotal(); 工程统计总表
    this.getProducerList();
    this.yScroll = this.$refs.contentBox.offsetHeight;
    if (1200 < document.body.clientWidth) {
      this.xScroll = 0;
    } else {
      this.xScroll = 1200;
    }
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
};
</script>

<style lang="less" scoped>
.formclass {
  /deep/.ant-modal-body {
    padding: 0 !important;
  }
  /deep/.ant-modal-footer {
    padding: 10px 100px;
  }
}
.back {
  /deep/.ant-table-tbody {
    background: rgb(223, 220, 220);
  }
}
/deep/ .ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
}
/deep/.ant-pagination.mini .ant-pagination-total-text,
.ant-pagination.mini .ant-pagination-simple-pager {
  height: 24px;
  line-height: 24px;
  font-size: 13px;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
.tabRightClikBox {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}
// /deep/.ant-modal{
//   margin-top:200px
// }
/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}
/deep/ .ant-table-thead > tr > th {
  padding: 7px 2px;
  border-right: 1px solid #efefef;
  .ant-table-column-sorter {
    display: none;
  }
}
/deep/ .ant-table-tbody > tr > td {
  padding: 7px 2px;
  border-right: 1px solid #efefef;
}
.projectDispatch {
  //height: 780px;
  background: #fff;
  // /deep/ .ant-table-placeholder {
  //   top: 28px !important;
  // }
  //.divider {
  //  height: 10px;
  //  margin: 0 -20px;
  //  background-color: #f0f2f5;
  //}
  .contentBox {
    width: 100%;
    height: 100%;
    // height: 97%;
    display: flex;
    border: 1px solid #e9e9f0;
    border-bottom: none;
    .contentLeft {
      border: 1px solid #e9e9f0;
      width: 51%;
      height: 100%;
      display: inline-block;
      .tabRightClikBox {
        border: 2px solid rgb(238, 238, 238) !important;
        li {
          height: 30px;
          line-height: 30px;
          margin-top: 0;
          margin-bottom: 0;
          border-bottom: 1px solid rgb(225, 223, 223);
          background-color: white !important;
          color: #000000;
        }
        .ant-menu-item:not(:last-child) {
          margin-bottom: 4px;
        }
      }
    }
    .contentRC {
      /deep/.ant-table-placeholder {
        height: 36px;
        padding: 7px 4px;
        border-bottom: 0;
      }
      /deep/.ant-empty-normal {
        margin: -6px 0;
      }
      /deep/.ant-empty-normal .ant-empty-image {
        height: 0;
      }
      border: 2px solid #e9e9f0;
      width: 100%;
      height: 75px;
      display: inline-block;
      // overflow-x: auto !important;
      border-bottom: 0;
    }
    .contentCenter {
      border: 2px solid #e9e9f0;
      border-bottom: 1px solid #e9e9f0;
      width: 100%;
      //height:705px;
      display: inline-block;
      // overflow-x: auto !important;
      border-bottom: 0 !important;
      /deep/.ant-table-thead > tr > th {
        height: 33px;
      }
    }
    .contentRight {
      /deep/.ant-table-placeholder {
        border: 0;
        width: 100%;
        position: absolute;
        //top: 76px;
        font-weight: 500;
        color: #000000;
      }
      // border: 2px solid #e9e9f0;
      border-top: 0;
      width: 100%;
      //height:292px;
      display: flex;
      flex-wrap: wrap;
      align-content: start;
      .top {
        width: 100%;
        height: 50%;
      }
      .bto {
        width: 100%;
        border-top: 2px solid #e9e9f0;
      }
    }
  }
  /deep/.ant-upload-list {
    display: none;
  }
  .actionBox {
    height: 48px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    form {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      background: #fff;
    }
  }
  .tabBox {
    display: flex;
    flex-wrap: wrap;
    span {
      display: inline-block;
      width: 100px;
      text-align: center;
      margin-bottom: 5px;
    }
  }
  /deep/ .ant-table-selection-column {
    padding: 11px 0;
  }
  /deep/ .ant-table-selection-col {
    width: 20px !important;
  }
  // /deep/ .ant-table-thead > tr > th,
  // /deep/ .ant-table-tbody > tr > td {
  //   padding: 5px 5px;
  // }
  /deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin-left: 10px;
    z-index: 1;
  }
  /deep/ .ant-table-tbody > tr.ant-table-row-selected td {
    background-color: rgb(223, 220, 220) !important;
  }
  /deep/ .ant-table-placeholder {
    width: 100%;
    position: absolute;
    top: 35px;
    font-weight: 500;
    color: #000000;
  }
  /deep/.ant-table-scroll {
    overflow: hidden !important;
  }
  /deep/.ant-input {
    font-size: 12px !important;
    color: #000000;
    font-weight: 500;
  }
  // .smallActive {
  //   /deep/ .ant-table {
  //     .ant-table-header {
  //       tr th:nth-child(4) {
  //         width: 45px !important;
  //         min-width: 45px !important;
  //       }
  //     }
  //     .ant-table-tbody {
  //       tr td:nth-child(4) {
  //         width: 45px !important;
  //         min-width: 45px !important;
  //       }
  //     }
  //   }
  // }
  // .Active {
  //   /deep/ .ant-table {
  //     .ant-table-header {
  //       tr th:nth-child(4) {
  //         width: 70px !important;
  //         min-width: 70px !important;
  //       }
  //     }
  //     .ant-table-tbody {
  //       tr td:nth-child(4) {
  //         width: 70px !important;
  //         min-width: 70px !important;
  //       }
  //     }
  //   }
  // }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: rgb(223, 220, 220) !important;
}
.projectDispatchParent {
  // height:810px;
  // min-width: 1670px;
  height: calc(100vh - 140px) !important;
}
</style>
