<template>
  <a-spin :spinning="spinning" class="spinSTY">
  <div id="pdfDom" ref="pdfDom">
    <button v-print='printObj' @click="printStatus">打印</button>
      <div id='printMe' > 
        <div style="page-break-after:always"> 
          <h3 style="font-size:22px;font-weight: 600;text-align: center;">{{showData.detailName}}</h3>              
        <div style="display:flex;">
          <span style="margin:16px auto;color:red;font-size:30px;font-weight: 600;border: 2px solid black;">{{showData.orderNo}}</span>      
        </div>         
        <div style="float:left;margin-top:-70px;">
          <span style="font-size:22px;font-weight: 600;margin-right:10px;">{{showData.joinFactoryIdStr}}</span>
          <span style="font-size:22px;font-weight: 600;margin-right:10px;">(<span v-if="showData.jiajiType > 0"> {{showData.jiajiType}}天/</span>交货日期：{{showData.deliveryDate}})</span>
          <span style="font-size:22px;font-weight: 600;margin-right:10px;">{{showData.fR4TypeStr}} {{showData.fR4Tg}}</span> 
          <br/>      
          <span style="font-size:16px;font-weight: 600;margin-right:10px;">{{showData.boardLayers}} {{showData.surfaceFinishStr}} </span>
          <br/>
          <div v-if="isFeeded" style="font-size:20px;font-weight: 600;color:red;border: 1px solid black;text-align: center;width:60px;">
            补投
          </div>
          <!-- <p> <span>{{showData.orderNo}}</span></p> -->
        </div>  
        <div style="display:flex;float:right;width:520px;margin-top:-75px;margin-right:-120px;">
          <svg id="barcode"  style="width:80%" ></svg>
          <!-- <barcode  :value="showData.orderNo" :options="barcode_option" ></barcode> -->
          <!-- <div style="margin-right:5px;width:80px;"><span style="margin-left:25%;font-size:28px;font-weight: 600;color:black;">{{showData.orderCount}}</span><br/><span style="font-size:16px;font-weight: 600;color:black;">子料号数</span></div> -->
          <div style="margin-right:5px;width:70px;padding-top:10px;"><span style="margin-left:20%;font-size:28px;font-weight: 600;color:black;">{{cardNum}}</span><br/><span style="font-size:16px;font-weight: 600;;color:black;">工单数</span></div>
        
        </div>            
       
        <div style="margin-bottom:0%;margin-top:25px;" v-if="proData.length">
          <table class='box' style="border:1px solid black;">           
            <tbody>
                <tr>                
                  <th>下单日期：</th>
                <th>{{showData.createTime}}</th>
                <th>投料日期：</th>
                <th>{{createTime}}</th>
                  <th  style="font-weight:800;">客户编号:</th>
                  <th style="font-weight:800;"></th>                  
                  <th  style="font-weight:800;">订单类别:</th>
                  <th style="font-weight:800;">{{showData.shipArea>2?"批量":"样板"}}</th>
                </tr>
                <tr>                 
                  <th>厂内编号：</th>
                 <th>{{showData.orderNo1}}</th>
                 
                  <th>合同编号:</th>
                  <th>{{showData.businessOrderNo}}</th>                 
                  <th>订单数量:</th>
                  <th>{{proData[0].num}}</th>
                  <th style="font-weight:800;">成品板厚(mm)：</th>
                  <th  style="font-weight:800;">{{showData.boardThickness}}</th>                
                </tr>
                <tr>
                  <th style="font-weight:800;">订单面积(㎡):</th>
                  <th  style="font-weight:800;">{{showData.shipArea}}</th>
                  <th>铜厚oz(内/外):</th>
                  <th>{{showData.innerCopperThickness}}/{{showData.copperThickness}}</th>
                   <th  style="font-weight:800;">子料号数：</th>
                  <th style="font-weight:800;">{{showData.orderCount}}</th>                  
                   <th  >开料尺寸A(mm):</th>
                  <th style="font-weight:800;">{{showData.apnlLength}}<span v-if="showData.apnlLength">*</span>{{showData.apnlWidth}}</th>                  
                </tr>
                <tr>
                  <th>拼板方式:</th>
                  <th>{{ pinBanType }}</th>                  
                  <th>单只尺寸(mm):</th>
                  <th >{{proData[0].boardWidth}}<span v-if="proData[0].boardWidth">*</span>{{proData[0].boardHeight}}</th>
                  <th>交货尺寸(mm):</th>
                  <th>{{proData[0].boardWidthSet}}<span v-if="proData[0].boardWidthSet">*</span>{{proData[0].boardHeightSet}}</th>
                  <th  >开料尺寸B(mm):</th>
                  <th style="font-weight:800;" >{{showData.bpnlLength>1?showData.bpnlLength:""}}<span v-if="showData.bpnlLength>0">*</span>{{showData.bpnlWidth>1?showData.bpnlWidth:""}}</th>                  
                </tr>   
                <tr>
                  <th style="font-weight:800;">投料面积(㎡):</th>
                  <th>{{cardArea}}/{{allCardArea}}</th>
                  <th style="font-weight:800;">投料PNL数:</th>
                  <th style="font-weight:800;">{{cardNum1}}/{{allCardNum}}</th>
                  <th>预投比率</th>
                  <th>{{(showData.ytlyl*100).toFixed(2)}}%</th>
                  <th></th>
                  <th></th>
                </tr>
                <tr>                 
                  <th>客户信息:</th>
                  <th colspan="7">{{showData.custNote}}</th>                  
                </tr>       
                <tr>                 
                  <th style="height:60px">重要提示:</th>
                  <th colspan="7">{{proData[0].specialRemarks}}</th>                 
                </tr>     
              </tbody>
          </table>
        </div>        
         <!--制造加工指示信息 -->
        <div v-if="sopData.length" style="margin-bottom:8%;">
          <h3 style="text-align: center;">《制造加工指示信息》</h3>           
          <a-table
              :columns="columns"
              :dataSource="sopData"
              :pagination='false'
              :rowKey="(record,index)=>{return index}"
              bordered             
          > 
          <template slot="craft" slot-scope="record" >
            <span v-for="(item,index) in record.paraList || []"  :key="index">
              <span >{{ item.name}}:<span style="font-weight:800;">{{ item.values}}</span>; </span> 
              <!-- <br/> -->
            </span>
          </template>
          </a-table>
        </div>
        <!--管制卡内容  -->       
        <!-- <div v-if="proData.length" style="margin-bottom:10%;">
          <h3 style="text-align: center;">《管制卡内容》</h3>           
          <a-table
              :columns="columns1"
              :dataSource="proData"
              :pagination='false'
              :rowKey="'orderNo'" 
              bordered             
          > 
          <template slot="craft" slot-scope="record" >
              <span >{{ record.boardHeight}}*{{ record.boardWidth}}; </span>            
          </template>
          <template slot="zuhan" slot-scope="record" >
              <span >{{ record.solderColorStr}}/{{ record.solderColorBottomStr}}; </span>            
          </template>
          <template slot="wenzi" slot-scope="record" >
              <span >{{ record.fontColorStr}}/{{ record.fontColorBottomStr}}; </span>            
          </template>
          </a-table>
        </div> -->
         <!--钻孔表  --> 
        <div v-if="viasData.length" style="margin-bottom:8%;">
          <h3 style="text-align: center;">《钻孔表》</h3>           
          <a-table
              :columns="columns2"
              :dataSource="viasData"
              :pagination='false'
              :rowKey="(record,index)=>{return index}"
              bordered             
          >          
          </a-table>          
        </div>
        <div style="margin:0 auto 8%;display: flex;">
              <div class="div1" style="width:280px;margin-right:5%;"  v-if="showData.klFileUrl">
                <h3 style="text-align: center;">《拼板开料图》</h3> 
                <img :src="showData.klFileUrl" style="width:100%"/>
              </div> 
              <div class="div2" style="width:350px;"  v-if="showData.pnlleUrlA">
                <h3 style="margin-left:30%;">《A板图》</h3> 
                <img :src="showData.pnlleUrlA" style="width:100%"/>
              </div>
              <div class="div3" style="width:350PX;"  v-if="showData.pnlleUrlB">
                <h3 style="margin-left:30%;">《B板图》</h3> 
                <img :src="showData.pnlleUrlB" style="width:100%"/>
              </div>
          </div>  
         <!--叠层阻抗信息  -->
        <div v-if="(tableData.length || impedanceTable.length) && showData.boardLayers > 2">
          <h3 style="text-align: center;">《叠层阻抗信息》</h3> 
          <div v-if="tableData.length">         
            <h3 style="margin: 0;border: 1px solid black;border-bottom: 0;text-align: center;">叠层信息</h3>
            <div class="reportTable">
              <div style="background: #1AA65F;width: 40px;align-items: center;display: flex;  font-weight: 500; text-align: center">
                完成<br/>板厚<br/>{{finishBoardThickness | floatFilter }}<br/>(mm)</div>
              <div style="width: 300px; padding: 23px 20px 20px; border: 1px solid; border-right: 0; border-left: 0; position: relative;border-bottom: 0">
    <!--            这是叠构图 -->
                <div v-for="(item,index) in tableData" :key="index">
                  <div v-if="item.stackUpMTR_ == 'OZ'" class="OZclass" :class="[index == tableData.length-1 ? 'ozlastClass' :'']">
                    <div v-if="item.stackUpLayerNo_" :class="['layerName','L'+item.stackUpLayerNo_]" >{{item.stackUpLayerNo_ | layerFilter(that)}}</div>
                    <div class="ozContent">
                      <div class="oz_bg" :class="[index == tableData.length-1 ? 'oz_active' :'']"></div>
                    </div>
                  </div>
                  <div v-if="item.stackUpMTR_ == 'Core'" class="coreClass">
                    <div v-for=" (ite,idx) in item.child" :key="idx" :class="[ite.stackUpMTR_ == 'Core'?'coreActive' :'ozActive']">
                      <div v-if="ite.stackUpMTR_ == 'OZ'" class="OZclass">
                        <div v-if="ite.stackUpLayerNo_" :class="['layerName','L'+ite.stackUpLayerNo_]">L{{ite.stackUpLayerNo_}}</div>
                        <div class="ozContent2">
                        </div>
                      </div>
                      <div v-if="ite.stackUpMTR_ == 'Core'" class="core-box">
                        <div v-if="ite.stackUpLayerNo_" :class="['layerName','L'+ite.stackUpLayerNo_]">L{{ite.stackUpLayerNo_}}</div>
                        <div class="CoreContent">
                        </div>
                      </div>
                    </div>

                  </div>
                  <div v-if="item.stackUpMTR_ == 'PP'" class="PPclass">
                    <div v-if="item.stackUpLayerNo_" :class="['layerName','L'+item.stackUpLayerNo_]">L{{item.stackUpLayerNo_}}</div>
                    <div class="PPContent"></div>
                  </div>
                  <div v-if="item.stackUpMTR_ == 'GB'" class="GBClass">
                    <div v-if="item.stackUpLayerNo_" :class="['layerName','L'+item.stackUpLayerNo_]">L{{item.stackUpLayerNo_}}</div>
                    <div class="gbContent"></div>
                  </div>

                </div>
    <!--            这是箭头盒子 会有多个箭头吗 嗯嗯 那你的箭头也要循环啊 。。。 放里面不行呀，不一定有这个盒子 这里的显示是根据条件出来的 -->
                <div v-if="laminationData.stackUpDrills.length >0" class="drillClss">
                  <div v-for="(list,index) in laminationData.stackUpDrills" :key="index" :ref="'dir_' + index" class="drillItem">
                  </div>
                </div>
              </div>
              <div class="parameterClass">
                <table>
                  <tr>
                    <th>类型</th>
                    <th>物料名称</th>
                    <th>物料参数</th>
                    <th>残铜率</th>
                    <th>成品厚度(mm)</th>
                  </tr>
                  <tr v-for="(item,index) in tableData" :key="index" :class="[item.stackUpMTR_ =='OZ' ? 'paramOZ' : (item.stackUpMTR_ =='Core' ? 'paramCore':'paramPP')]">
                    <td>{{item.stackUpMTR_ | typeFilter}}</td>
                    <td>{{item | nameFilter}}</td>
                    <td>{{item | paramFilter}}</td>
                    <td>{{item | ctlFilter}}</td>
                    <td>{{item | cphdFilter}}</td>
                  </tr>
                </table>
              </div>
              <div class="thickness">压合完成理论厚度： {{laminationData.stackUpInfo.pressingThickness | floatFilter}}mm</div>
            </div>
          </div>

          <div v-if="impedanceTable.length" style="border: 1px solid; border-top: 0;">
            <h3 style="margin: 0; border-bottom:1px solid black;text-align: center;border-bottom: 0;">阻抗信息</h3>
            <div>
              <a-table
                  :columns="columns3"
                  :data-source="impedanceTable"
                  :rowKey="(record,index)=>{return index}"
                  bordered
                  :pagination="false"
              ></a-table>
            </div>
            <div v-for="(item,index) in impedanceData" :key="index" class="impedance">
              <h4>{{ impTypeFilter(item.imp_Type_)}}<br/> {{item.imp_PoLarName}}</h4>
              <div class="imp_left">
                <div class="line_flex"><p>原稿线宽</p><p>{{item.imp_LineWidth_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>调整线宽</p><p>{{item.imp_OKLineWidth_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>原稿线距</p><p>{{item.imp_LineSpace_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>调整线距</p><p>{{item.imp_OKLineSpace_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>原稿地面</p><p>{{item.imp_LineCuSpace_ || '/'}}</p><p>Mil</p></div>
                <div class="line_flex"><p>调整共面</p><p>{{item.imp_OKLineCuSpace_ || '/'}}</p><p>Mil</p></div>
                <div class="line_flex"><p>要求阻值</p><p>{{item.imp_Value_Req_}}</p><p>Ω</p></div>
              </div>
              <div class="imp_center">
                <img :src="'data:image/png;base64,'+item.impModelImage">
              </div>
              <div class="imp_right">
                <div class="line_flex"><p>H1</p><p>{{item.imp_H1_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>Er1</p><p>{{item.imp_Er1_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>W1</p><p>{{item.imp_W1_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>W2</p><p>{{item.imp_W2_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>S1</p><p>{{item.imp_S1_ || '/'}}</p><p>Mil</p></div>
                <div class="line_flex"><p>T1</p><p>{{item.imp_T1_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>C1</p><p>{{item.imp_C1_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>C2</p><p>{{item.imp_C2_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>C3</p><p>{{item.imp_C3_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>CEr</p><p>{{item.imp_CEr_}}</p><p>Mil</p></div>
                <div class="line_flex"><p>Imp</p><p>{{item.imp_TrueValue_}}</p><p>Ω</p></div>
              </div>
              <h3><p style="margin: 0">控制层:{{item.imp_ControlLay_}};</p><p style="margin: 0">上参考:{{item.imp_UpLay_||'/'}};</p><p style="margin: 0">下参考:{{item.imp_DownLay_||'/'}};</p></h3>
            </div>
          </div>
        </div> 
        </div> 
         <!-- vcut信息 -->
          <div v-if="vcutData.length " style="height:100%;page-break-after:always" >
          <div v-for="(item,index) in vcutData" :key="index" style="height:1300px;">
            <div  style="margin-bottom:8%;">
              <svg :id="'jsbarcodeImg' + index" style="width:240px;"></svg>            
              <table class='box' style="border:1px solid black;">
                <tbody>
                  <tr>
                    <th>径向*纵向：</th>
                    <th>{{item.boardHeight}}*{{item.boardWidth}}</th>
                    <th>层数：</th>
                    <th>{{item.boardLayers}}</th>
                    <th>板厚:</th>
                    <th>{{item.boardThickness}}</th>
                    <th>工艺:</th>
                    <th>{{item.surfaceFinishStr}}</th>
                  </tr>
                  <tr>
                    <th>款数：</th>
                    <th>{{item.pinBanNum}}</th>
                    <th>阻焊文字：</th>
                    <th>{{item.solderColorStr}}/{{item.fontColorStr}}</th>
                    <th>set样式:</th>
                    <th>{{item.pinBanType}}</th>
                    <th>接受打叉:</th>
                    <th></th>
                  </tr>
                  <tr>
                    <th>拼版款式：</th>
                    <th>{{item.eachPinBanNum}}</th>
                    <th>交货数量：</th>
                    <th>{{item.num}}</th>
                    <th>投料数:</th>
                    <th>{{item.actualNum}}</th>
                    <th>出货面积:</th>
                    <th>{{item.boardArea}}</th>
                  </tr>
                  <tr>
                    <th>交期：</th>
                    <th>{{item.deliveryDate}}</th>
                    <th>业务员：</th>
                    <th>{{item.followAdminName}}</th>
                    <th>金手指倒斜边:</th>
                    <th>{{item.goldfinger}}</th>
                    <th>拼版编号:</th>
                    <th>{{item.orderNo}}</th>
                  </tr>
                  <tr>
                    <th>是否KA客户:</th>
                    <th colspan="3">{{item.isBigCus ? "是": "否"}}</th>
                    <th>测试方式:</th>
                    <th  colspan="3">{{item.flyingProbeStr}}</th>
                  </tr>
                  <tr>
                    <th>业务员备注：</th>
                    <th colspan="7">{{item.cnNote}}</th>
                    
                  </tr>
                  <tr>
                    <th>客户备注：</th>
                    <th colspan="7">{{item.note}}</th>
                  </tr>           
                </tbody>
              </table>             
            </div>
          </div>
        </div>    
 
      </div> 
  </div>
</a-spin>
</template>

<script>
const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: "center",
      customRender: (text,record,index) => `${index+1}`,
      width: "4%",    
    },
    {
      title: '工序名称',
      dataIndex: 'stepName',
      align: "left",
      width: "8%",    
    },
    {
      title: '标时',
      dataIndex: 'timeSpan',
      align: "left",
      width:"4%",    
    },
    {
    title: "工艺参数",
    // width: "18%",
    align: "left",
    scopedSlots:{customRender:'craft'}
    },
    {
      title: '数量',
      dataIndex: '',
      align: "left",
      width: "4%",    
    },
    {
      title: '签名/时间',
      dataIndex: '',
      align: "left",
      width:"8%",    
    },
    // {
    //   title: '操作人',
    //   dataIndex: '',
    //   align: "left",
    //   width: "6%",    
    // },
    {
      title: '报废数量',
      dataIndex: '',
      align: "left",
      width:"8%",    
    },
    {
      title: 'QA',
      dataIndex: '',
      align: "left",
      width: "8%",    
    },
    // {
    //   title: 'IPQC',
    //   dataIndex: '',
    //   align: "left",
    //   width:"8%",    
    // },
    // {
    //   title: '品质备注',
    //   dataIndex: '',
    //   align: "left",
    //   // width: "18%",    
    // },
    
    
  ]
  const columns1 = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: "center",
      customRender: (text,record,index) => `${index+1}`,
      width: "2%",    
    },
    {
      title: '订单编号',
      dataIndex: 'orderNo',
      align: "left",
      width: "4%",    
    },
    {
      title: '客编',
      dataIndex: '',
      align: "left",
      width:"4%",    
    },
    {
    title: "径向*纵向",
    width: "4%",
    align: "left",
    scopedSlots:{customRender:'craft'}
    },
    {
      title: '层数',
      dataIndex: 'boardLayers',
      align: "left",
      width: "4%",    
    },
    {
      title: '板厚',
      dataIndex: 'boardThickness',
      align: "left",
      width:"4%",    
    },
    {
      title: '工艺',
      dataIndex: 'surfaceFinishStr',
      align: "left",
      width: "4%",    
    },
    {
      title: '款数',
      dataIndex: 'pinBanNum',
      align: "left",
      width:"4%",    
    },
    {
      title: '阻焊顶/底',
      align: "left",
      width: "4%", 
      scopedSlots:{customRender:'zuhan'}   
    },
    {
      title: '文字顶/底',
      align: "left",
      width: "4%", 
      scopedSlots:{customRender:'wenzi'}   
    },
    {
      title: 'set样式',
      dataIndex: 'pinBanType',
      align: "left",
      width:"4%",    
    },
    {
      title: '接受打叉',
      dataIndex: '',
      align: "left",
      width: "4%",    
    },
    {
      title: 'v割',
      dataIndex: 'vCut',
      align: "left",
      width: "4%",    
    },
    {
      title: '拼版',
      dataIndex: '',
      align: "left",
      width:"4%",    
    },
    {
      title: '数量',
      dataIndex: 'actualNum',
      align: "left",
      width: "4%",    
    },
    {
      title: '入库数',
      dataIndex: '',
      align: "left",
      width:"4%",    
    },
    {
      title: '投料数',
      dataIndex: '',
      align: "left",
      width: "4%",    
    },
    {
      title: '面积',
      dataIndex: 'boardArea',
      align: "left",
      width: "4%",    
    },
    {
      title: '交期',
      dataIndex: 'deliveryDate',
      align: "left",
      width:"4%",    
    },
    {
      title: '特殊报告',
      dataIndex: '',
      align: "left",
      width: "4%",    
    },
    {
      title: '文件名',
      dataIndex: 'pcbFileName',
      align: "left",
      width:"4%",    
    },
    {
      title: '业务备注',
      dataIndex: 'cnNote',
      align: "left",
      width: "4%",    
    },
    {
      title: '客户备注',
      dataIndex: 'note',
      align: "left",
        
    },
    
    
  ]
  const columns2 = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: "center",
      customRender: (text,record,index) => `${index+1}`,
      width: "6%",    
    },
    {
      title: '层名',
      dataIndex: 'drillName',
      align: "left",
      width: "8%",    
    },
    {
      title: '刀序',
      dataIndex: 'toolNo',
      align: "left",
      width:"8%",    
    },
    {
    title: "类型",
    width: "8%",
    align: "left",
    dataIndex: 'toolKind',
    },
    {
      title: '钻刀尺寸',
      dataIndex: 'toolDia',
      align: "left",
      width: "8%",    
    },
    {
      title: '成品尺寸',
      dataIndex: 'storeDia',
      align: "left",
      width:"8%",    
    },
    {
      title: '槽长度',
      dataIndex: 'blength',
      align: "left",
      width: "8%",    
    },
    {
      title: '数量(A)',
      dataIndex: 'qty',
      align: "left",
      width:"8%",    
    },
    {
      title: '数量(B)',
      align: "left",
      width: "8%", 
      dataIndex: '', 
    },    
    {
      title: '正公差',
      dataIndex: 'posittol',
      align: "left",
      width:"8%",    
    },
    {
      title: '负公差',
      dataIndex: 'minustol',
      align: "left",
      width: "8%",    
    },    
    {
      title: '备注',
      dataIndex: 'captions',
      align: "left",
        
    },
    
    
  ]
  const columns3 = [
  {
    title: "序号",
    dataIndex: "index",
    width:45,
    align: 'center',
    customRender: (text,record,index) => `${index+1}`,
  },
  {
    title: '类型',
    dataIndex: 'type',
    width:70,
    align: 'center',
  },
  {
    title: '控制层',
    dataIndex: 'controlLayer',
    width:55,
    align: 'center',
  },
  {
    title: '参考层',
    dataIndex: 'referenceLayer',
    width:60,
    align: 'center',
  },
  {
    title: '原线宽',
    dataIndex: 'primarylLinewidth',
    width:55,
    align: 'center',
  },
  {
    title: '原线距',
    dataIndex: 'primaryLinedistance',
    width:55,
    align: 'center',
  },
  {
    title: '原线铜',
    dataIndex: 'primaryCopper',
    width:55,
    align: 'center',
  },
  {
    title: '调整线宽',
    dataIndex: 'adjustLineweight',
    width:70,
    align: 'center',
  },
  {
    title: '调整线距',
    dataIndex: 'adjustLinedistance',
    width:70,
    align: 'center',
  },
  {
    title: '调整线铜',
    dataIndex: 'adjustingWirecopper',
    width:70,
    align: 'center',
  },
  {
    title: '要求阻值',
    dataIndex: 'filmLinewidth',
    width:70,
    align: 'center',
  },
  {
    title: '实际阻值及公差',
    align: 'center',
    dataIndex: 'resistanceTolerance',
  },
];
import barcode from "@xkeshi/vue-barcode";
import JsBarcode from 'jsbarcode'
import {mapState,} from 'vuex';
// import printHtml from "@/utils/print.js"
import htmlToPdf from '@/utils/htmlToPdf';
import {getInpedanceData,} from "@/services/impedance";
import { 
  printCard,
  printStatus,
  } from "@/services/scgl/OrderManagement/Composition";
export default{
    name:"reportInfo",
    // components: {barcode},
    // props:{      
    //     CardId:{
    //       type:String
    //     },
    //     orderNo:{
    //       type:String          
    //     }
    // },
    data(){
        return{
          spinning:false,
          showData:{},
          sopData:[],
          vcutData:[],
          proData:[],
          viasData:[],          
          barcode_option: {
            displayValue: true, //是否默认显示条形码数据
            background: '#fff', //条形码背景颜色
            height: '50px',
            fontSize: '12px',
            margin:2,
            width:1,
            // format:"CODE128",
            lineColor: '#606266',
          },
          columns,
          columns1,
          columns2,
          columns3,
          data: [],
          that: this,
          height: window.document.documentElement.clientHeight - 158,
          style_:0,
          printObj:{
            id: "printMe", // 打印的区域
            preview: false, // 预览工具是否启用
            previewTitle: '流程卡打印', // 预览页面的标题
            popTitle: '', // 打印页面的页眉
            previewBeforeOpenCallback(vue) {
              console.log('正在加载预览窗口')
            },
            previewOpenCallback(vue) {
              console.log('已经加载完预览窗口')
            },
            clickMounted: (vue) => {
              console.log("触发点击打印回调",);
              vue.isShowPrint = true  //弹框显示条码              
            },
            beforeOpenCallback(vue) {
              console.log('打开之前',vue)
            },
            openCallback (vue) {
              vue.isShowPrint = false  // 关闭条码显示弹框 
              console.log('执行了打印')
            },
         },
         cardNum:0,
         allCardNum:0,
         cardArea:0,
         createTime:'',
         pinBanType:'',
         pnlNum:0,
         allCardArea:0,
         laminationData:{},
         finishBoardThickness:'',
         CardId:'',
         orderNo:'',
         isFeeded:false,
         cardNum1:'',
        }
    },       
    created(){     
           
    },
    async mounted(){   
      console.log('this.$route.query',this.$route.query) 
      this.CardId =  this.$route.query.id
      await this.getData() 
      this.openReport()  
      
        
      
    },
    computed:{
      ...mapState('account', ['user',]),  
    tableData: function(){
      let _self = this;
      let newData = []
      // let structure = _self.laminationData.stackUps.map(item => item.stackUpMTR_);
      let data = _self.laminationData.stackUps
      if(data){
        data.forEach((item,index)=> {
        let child = []
        if (item.stackUpMTR_ == 'OZ') {
          if (data[index+1]?.stackUpMTR_ == 'Core' && data[index+2]?.stackUpMTR_ == 'OZ') {
            child.push(item)
            child.push(data[index+1])
            child.push(data[index+2])
            newData.push({'stackUpMTR_':'Core', 'child': child})
          } else {
            if (data[index-1]?.stackUpMTR_ != 'Core' || index==0)
              newData.push(item)
          }
        } else if (item.stackUpMTR_ == 'PP') {
          newData.push(item)
        }else if(item.stackUpMTR_ == 'GB'){
          newData.push(item)
        }
      })
      }
      
      console.log('tableData',newData)
      return newData
    },
    impedanceData: function (){
      let _self = this;
      let im_data = _self.laminationData.stackIMPs
      // im_data.forEach(item => {
      //
      // })
      return im_data
    },
    impedanceTable(){
      const arr =[]
      if(this.laminationData.stackIMPs){
        this.laminationData.stackIMPs.forEach(item => {
        // debugger
        let _obj = {
          "type": this.impTypeFilter(item.imp_Type_),
          "controlLayer" : item.imp_ControlLay_,
          "referenceLayer": item.imp_UpLay_ ? (item.imp_DownLay_ ? (item.imp_UpLay_+'/'+ item.imp_DownLay_) : item.imp_UpLay_) : item.imp_DownLay_,
          "primarylLinewidth": item.imp_LineWidth_,
          "primaryLinedistance": item.imp_LineSpace_,
          "primaryCopper": item.imp_LineCuSpace_,
          "adjustLineweight": item.imp_OKLineWidth_,
          "adjustLinedistance": item.imp_OKLineSpace_,
          "adjustingWirecopper": item.imp_OKLineCuSpace_,
          "filmLinewidth" : item.imp_Value_Req_,
          "resistanceTolerance": item.imp_TrueValue_ + '±' + item.imp_Value_Tol_
        }
        arr.push(_obj)
      })
      }
     
      return arr;
    },
  },
  filters:{
    layerFilter(val,that){
      if (val ==1 ){
        return 'GTL'
      } else if (val == that.laminationData.stackUps[that.laminationData.stackUps.length-1].stackUpLayerNo_) {
        return 'GBL'
      } else {
        return  'L'+val
      }
    },
    floatFilter(val) {    
      return val.toFixed(2)
    },
    typeFilter(val) {
      if (val =='OZ') {
        return '铜箔'
      } else if (val == 'PP') {
        return '半固化片'
      } else if (val == 'Core') {
        return '芯板'
      }else if (val == 'GB') {
        return '光板'
      }
    },
    nameFilter(val) {
      if (val.child) {
        let str = ''
        val.child.forEach(item => {
          if(item.stackUpMTR_ == 'Core') {
            str = item.stackUpMTRType_
          }
        })
        return str
      }else {
        return val.stackUpMTRType_
      }
    },
    paramFilter(val){
      if (val.child) {
        let val_ = val.child
        let str_ = ``
        val_.forEach(item => {
          // console.log('物料参数',item.stackUpMTR_)
          if (item.stackUpMTR_ =='OZ') {
            str_ += item.stackUpMTRFoil_ +'OZ'+ "\n"
          } else {
            // let corStr_ = item.tdFlag_ ? '含铜）' : '不含铜）'
            let corStr_ = item.stackUpCoreDS_ ? '含铜）' : '不含铜）'
            str_ += item.stackUpMTRFoil_ +'(' + corStr_ + "\n"
          }

        })
        return str_
      } else {
        if (val.stackUpMTR_ =='OZ') {
          return val.stackUpMTRFoil_ + '  OZ'
        } else {
          // 2022/08/05 屏蔽PP型号去重
          // let str = ''
          // let _data  = val.stackUpMTRFoil_.split('+').reduce(function (a, b) {
          //   if (b in a) {
          //     a[b]++
          //
          //   } else {
          //     a[b] = 1
          //   }
          //   return a
          // }, {})
          // var _dataKeys = Object.keys(_data)
          // str = _dataKeys.join('+')
          // return  str
          return val.stackUpMTRFoil_
        }
      }
    },
    ctlFilter(val) {
      if (val.child){
        let val_ = val.child
        let str_ = ``
        val_.forEach(item => {
          if (item.stackUpCTLMI_) {
            str_ += item.stackUpCTLMI_ + "\n"
          } else {
            str_ += '/' + "\n"
          }

        })
        return str_ || '/'
      }else {
        return val.stackUpCTLMI_ || '/'
      }
    },
    cphdFilter(val) {
      if (val.child) {
        let val_ = val.child
        let str_ = ``
        val_.forEach(item => {
          str_ += item.stackUpThichnessMM_ + "\n"
        })
        return str_
      } else {
        return val.stackUpThichnessMM_
      }
    },
  },
    methods: {
      printStatus(){
        let params={
          "cardId": this.CardId,        
          // "account":  this.user.userName,
          // "name":  this.user.name
        }
        printStatus(params).then(res=>{
          if(!res.code){
            this.$message.error(res.message)
          }
        })       
      },
     async getData(){
        this.spinning = true
      await  printCard(this.CardId,this.$route.query.businessOrderNo).then(res=>{
          if(res.code){   
            this.isFeeded = res.data.isFeeded            
            this.showData = res.data.pinBanOrder
            this.cardNum = res.data.cardNum
            this.cardNum1=res.data.num
            this.allCardNum=res.data.allCardNum
            this.cardArea=res.data.cardArea
            this.createTime=res.data.createTime
            this.pinBanType=res.data.pinBanType
            this.allCardArea=res.data.allCardArea
            JsBarcode("#barcode", res.data.cardNo,{width:1.5, height:70,})
            this.orderNo = this.showData.orderNo
            this.showData.orderNo = this.showData.orderNo.substr(-7).substr(0,5)
            this.sopData = res.data.sops
            this.vcutData = res.data.vcuts
            this.proData = res.data.proOrders
            this.viasData = res.data.holeTables 
            console.log(' this.orderNo11', this.orderNo)
          }
        }).finally(()=>{
          this.generateJSBarcodeImg()
          this.spinning = false          
        })
        
      },
      generateJSBarcodeImg(){
        console.log('this.vcutData',this.vcutData)
        this.vcutData.forEach((v,index)=>{
        // 根据动态id，动态赋值，动态生成条形码
          JsBarcode('#jsbarcodeImg' + index, v.orderNo, {
            format: 'CODE39',
            width: 1.5,
            height: 70,
          })
      })
      },     
      impTypeFilter(val) {
      var arr = [{"text":"外层单端(SM)","valueMember":"O_S(SM)","imp_CtlThicknessInH":0},{"text":"外层差分(SM)","valueMember":"O_D(SM)","imp_CtlThicknessInH":0},{"text":"内层单端","valueMember":"I_S_2","imp_CtlThicknessInH":2},{"text":"内层差分","valueMember":"I_D_2","imp_CtlThicknessInH":2},{"text":"外层单端共面地(SM)","valueMember":"O_S_C(SM)","imp_CtlThicknessInH":0},{"text":"外层差分共面地(SM)","valueMember":"O_D_C(SM)","imp_CtlThicknessInH":0},{"text":"内层单端(1层屏蔽)","valueMember":"I_S_1","imp_CtlThicknessInH":2},{"text":"内层差分(1层屏蔽)","valueMember":"I_D_1","imp_CtlThicknessInH":2},{"text":"内层单端共面地","valueMember":"I_S_C_2","imp_CtlThicknessInH":2},{"text":"内层单端共面地(1层屏蔽)","valueMember":"I_S_C_1","imp_CtlThicknessInH":2},{"text":"内层差分共面地","valueMember":"I_D_C_2","imp_CtlThicknessInH":2},{"text":"内层差分共面地(1层屏蔽)","valueMember":"I_D_C_1","imp_CtlThicknessInH":2},{"text":"外层单端","valueMember":"O_S","imp_CtlThicknessInH":0},{"text":"外层差分","valueMember":"O_D","imp_CtlThicknessInH":0},{"text":"外层单端共面地","valueMember":"O_S_C","imp_CtlThicknessInH":0},{"text":"外层差分共面地","valueMember":"O_D_C","imp_CtlThicknessInH":0},{"text":"外层单端共面地(无屏蔽)","valueMember":"O_S_C_0","imp_CtlThicknessInH":0},{"text":"外层单端共面地(无屏蔽)(SM)","valueMember":"O_S_C_0(SM)","imp_CtlThicknessInH":0},{"text":"外层差分共面地(无屏蔽)","valueMember":"O_D_C_0","imp_CtlThicknessInH":0},{"text":"外层差分共面地(无屏蔽)(SM)","valueMember":"O_D_C_0(SM)","imp_CtlThicknessInH":0},{"text":"内层单端共面地(无屏蔽)","valueMember":"I_S_C_0","imp_CtlThicknessInH":2},{"text":"内层差分共面地(无屏蔽)","valueMember":"I_D_C_0","imp_CtlThicknessInH":2}]
      let str = arr.find(item => item.valueMember == val).text
      return str
      },
    async  openReport(){ 
      console.log(' this.orderNo22', this.orderNo)

      await  getInpedanceData({OrderNo:this.orderNo}).then(res => {
          if (res.code == 1) {
            this.laminationData = res.data
            this.finishBoardThickness = this.laminationData.stackUpInfo.finishBoardThickness
            console.log('11',this.finishBoardThickness)
            this.styleHeight_()
          } 
        })
      },
      styleHeight_(val){
        console.log(this.$refs)
        let newArr = [],topArr=[]
        let arr=[]
        console.log('叠层数据arr',  this.laminationData.stackUpDrills)
        this.laminationData.stackUpDrills.forEach(item=>{
          item.count = item.endLayer- item.startLayer
          arr.push(item)
        })
        console.log('叠层数据arr',arr)
        arr.sort((a,b)=>{ return b.count- a.count})
        console.log('叠层数据排序',arr)
        this.laminationData.stackUpDrills = arr
        this.laminationData.stackUpDrills.forEach((ite,index) => {
          console.log('ite',ite)
          let height_ = 0;
          let top_ = 0
          let startIndex = this.laminationData.stackUps.findIndex(item=>{return item.stackUpLayerNo_ == ite.startLayer});
          console.log('startIndex',startIndex)
          let endIndex = this.laminationData.stackUps.findIndex(item=>{return item.stackUpLayerNo_ == ite.endLayer});
          console.log('startIndex',startIndex)
          newArr = this.laminationData.stackUps.slice(startIndex,endIndex+1);
          topArr = this.laminationData.stackUps.slice(0,startIndex+1);
          newArr.forEach(list=> {
            if (list.stackUpMTR_ == 'OZ') {
              height_+=30
            } else if (list.stackUpMTR_ == 'Core') {
              height_+=20
            } else {
              height_+=22
            }
          })
          topArr.forEach(res => {
            if (res.stackUpMTR_ == 'OZ') {
              top_ +=30
            } else if (res.stackUpMTR_ == 'Core') {
              top_ +=20
            } else {
              top_ +=22
            }
          })
          this.$refs['dir_'+index][0].style.height = height_ - 30+'px'
          this.$refs['dir_'+index][0].style.left = (index+1) * 18 + 80 +'px'
          this.$refs['dir_'+index][0].style.top = top_ +5 +'px'
        })
      }
    
       
    },
    
}

</script>
<style lang="less">
#vcut{
  page-break-after:always!important;
}
// /deep/.ant-table-bordered {
//   .ant-table-thead > tr > th{
//   border-color:black!important;
// }
// } 
// /deep/.ant-table-bordered .ant-table-tbody > tr > td{
//   border-color:black!important;
// }

/deep/ .ant-card {
    .ant-card-head {
      padding: 0;
      min-height: auto;
      border: 0;
      .ant-card-head-title {
        padding: 0;
        border-bottom: 1px solid black;
        height: 29px;
        line-height: 20px;
        margin-bottom: 15px;
        text-indent: 5px;
        font-size: 14px;
        font-weight: normal;
        color: #000;
        padding-bottom: 8px;
        margin-top: 15px;

      }
    }
    .ant-card-body {
      padding: 0;
      .ant-form {
        border-left:1px solid black;
        border-top:1px solid black;
      }
      .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 32px;
          .ant-select {
            width: 120px;
          }
          .ant-input {
            width: 120px;
          }
          .ant-input-number {
            width: 120px;
          }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid black;
          border-bottom: 1px solid black;
          label {
            font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          }
        }
        .ant-form-item-control-wrapper {
          font: 12px/1.14 "微软雅黑",arial,\5b8b\4f53;
          .ant-form-item-control {

            .ant-form-item-children {
              display: block;
              min-height: 13.672px;
            }
            line-height: inherit;
            padding: 8px 10px;
            border-right: 1px solid black;
            border-bottom: 1px solid black;
          }
        }
      }
    }
  }
  .ant-table-thead > tr > th{
    padding: 6px 0 0 5px ;
    color:black;
    // border-color:black!important;
  }
  .ant-table-tbody > tr > td{
    padding:  5px 0 0 5px;
    color:black;
    // border-color:black!important;
  }
  .box {
    width:100%;
  }
  // .ant-table-body > table{
  //   border-color:black;
  // }
  .box tbody> tr > th{
    border-right:1px solid black;
    border-bottom:1px solid black;
    width:12.5%;
    padding:0 5px;
    color:black;
  }
  
  </style>
<style scoped lang="less">

.spinSTY{
  width:60%;
  margin-left:10px;
}
  /deep/.ant-table-body > table{
    border-color:black!important;
  }
   /deep/.ant-table-thead > tr > th{
    border-color:black!important;
  }
  /deep/.ant-table-tbody > tr > td{
    border-color:black!important;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #FFFFFF!important;
  }
  .drillClss {
  left: 0;
  top: 0;
  padding: 23px 20px 20px;
  width: 100%;
  position: absolute;
  height: 100%;
}
.drillItem {
  width: 9px;
  position: absolute;
  background: #0000CC;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  box-shadow: 0px 2px 2px 0px;
}
.drillItem:after{
  content: '';
  display: block;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #0000CC;
  position: absolute;
  bottom: -8px;
  left:-3px;
}
.reportTable{
  border: 1px solid;
  border-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  .thickness {
    width: 100%;
    text-align: right;
    padding-right: 15px;
    line-height: 30px;
    font-weight: 500;
    font-size: 16px;
    border-top: 1px solid;
    border-bottom: 1px solid;
  }
  .layerName {
    width: 60px;
    height: 100%;
    border: 2px solid;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
  }
  .ozlastClass {
    align-items: center;
  }
  .OZclass {
    height: 30px;
    display: flex;
    overflow: hidden;
    align-items: center;
    .ozContent{
      //border-bottom: 1px solid;
      width: 100%;
      margin-left: 20px;
      display: flex;
      align-items: flex-end;
      .oz_bg {
        width: 100%;
        border-radius: 7px;
        height: 10px;
        overflow: hidden;
        background: url("../../../assets/img/pp.png") repeat-x;
        background-position-x: -12px;
      }
      .oz_active {
        background: url("../../../assets/img/pp2.png") repeat-x;
        background-position-x: -12px;
      }
    }
  }
  .PPclass {
    overflow: hidden;
    .PPContent {
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 12px;
      margin: 5px 0;
      background: #9ACD32;
      border-radius: 5px;

    }
  }
  .coreClass {
    .ozActive:nth-of-type(1) {
      margin-bottom: 20px;
    }
    position: relative;
    .coreActive {
      position: absolute;
      width: 100%;
      top: 16px;
    }
    .ozContent2 {
      border-bottom: 2px solid black;
      width: 100%;
    }
    .core-box {
      height: 48px;
      width: 100%;
      overflow: hidden;
      .CoreContent {
        height: 100%;
        float: right;
        background: #FCB505;
        width: calc(100% - 68px);
        background: #FCB408;
      }
    }

  }
  .GBClass{
    overflow: hidden;
    .gbContent{
      float: right;
      margin-left: 10px;
      width: calc(100% - 68px);
      height: 22px;
      margin: 3px 0;
      background: #FCB505;
      border-radius: 5px;
    }
  }
  .parameterClass {
    flex: 1;
    table tr th {
      text-align: center;
      width: 80px;
    }
    table tr th:nth-child(2) {
      width: 100px;
    }
    table tr th:nth-child(3) {
      width: 140px;
    }
    table tr th:nth-child(4) {
      width: 60px;
    }
    table tr th:nth-child(5) {
      width: 100px;
    }
    table {
      border-left: 1px solid black;
    }
    table tr:nth-child(1) {
      border: 1px solid black;
      border-left: none;

    }
    table tr th {
      border-right: 1px solid black;
    }
    table tr td {
      text-align: center;
      color: #0000CC;
      border-right:1px solid black;
      border-bottom:1px solid black ;
    }
    .paramOZ{
      height: 27px;
      line-height: 27px;
    }
    .paramCore {
      height: 80px;
      td {
        white-space: pre;
      }

      //line-height: 30px;
    }
    .paramPP {
      height: 19px;
      line-height: 19px;
    }
  }
}
.impedance {
  display: flex;
  flex-wrap: wrap;
  padding-left: 5px;
  h4 {
    width: 100%;
  }
  h3 {
    display: flex;
    width: 250px;
    justify-content: space-between;
    p{
      margin: 0;
      margin-right: 10px;
    }
    p:nth-child(1) {
      margin: 0;
    }
  }
  // .imp_left{
  // }
  .line_flex {
    display: flex;
    p{
      margin: 0;
      margin-right: 15px;
      font-size: 14px;
      line-height: 25px;
      font-weight: 500;
    }
    p:nth-child(2) {
      width: 60px;
    }
  }
  .imp_center{
    margin: 10px 40px;
    img {
      padding: 15px;
      background: #008181;
    }
  }
  .imp_right{
    .line_flex {
      p:nth-child(1) {
        width: 40px;
      }
    }
  }
}
</style>

