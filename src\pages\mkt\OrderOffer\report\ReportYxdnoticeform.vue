<!-- 雅信达生产通知单 -->
<template>
  <div class="pdfDom1">
    <a-button v-print="printObj1" @click="printpdf" type="primary" class="printstyle">打印</a-button>
    <div id="yxdnoticedom" style="font-size: 12px; font-family: '宋体'; padding: 25px; color: black; font-weight: bold">
      <div style="font-size: 26px; text-align: center">{{ YXDnoticedata.value_1 }}</div>
      <div style="font-size: 22px; text-align: center">订单制作指示及可制造性评审单</div>
      <div style="font-size: 25px">{{ YXDnoticedata.value_43 }}</div>
      <div style="display: flex; align-items: center; justify-content: space-between">
        <div style="font-size: 25px">{{ YXDnoticedata.value_2 }}</div>
        <div>打印日期: {{ YXDnoticedata.value_3 }}</div>
      </div>
      <div style="display: flex; justify-content: space-between">
        <div style="font-size: 25px">销售订单号:{{ YXDnoticedata.value_4 }}</div>
        <div style="font-size: 25px">订单类型:{{ YXDnoticedata.value_5 }}</div>
      </div>
      <div style="padding-top: 15px">
        <table style="width: 100%" class="onetable">
          <tr>
            <td style="min-width: 150px">本厂编号:</td>
            <td>{{ YXDnoticedata.value_6 }}</td>
            <td>建档日期:</td>
            <td>{{ YXDnoticedata.value_7 }}</td>
          </tr>
          <tr>
            <td>接单工厂:</td>
            <td>{{ YXDnoticedata.value_8 }}</td>
            <td colspan="2">交货尺寸:{{ YXDnoticedata.value_9 }}</td>
          </tr>
          <tr>
            <td>订单审核日期:</td>
            <td>{{ YXDnoticedata.value_10 }}</td>
            <td>交货日期:</td>
            <td>{{ YXDnoticedata.value_11 }}</td>
          </tr>
          <tr>
            <td>客户型号:</td>
            <td>{{ YXDnoticedata.value_12 }}</td>
            <td>交货数量:</td>
            <td>{{ YXDnoticedata.value_13 }}</td>
          </tr>
          <tr>
            <td>客户物料号:</td>
            <td>{{ YXDnoticedata.value_14 }}</td>
            <td>客户订单号:</td>
            <td>{{ YXDnoticedata.value_15 }}</td>
          </tr>
          <tr>
            <td
              colspan="4"
              style="border-top: 2px solid black; border-bottom: 2px solid black; font-weight: bold; font-size: 16px; text-align: center"
            >
              制作要求
            </td>
          </tr>
          <tr>
            <td>1.基材类型:</td>
            <td>{{ YXDnoticedata.value_16 }}</td>
            <td>13.层数:</td>
            <td>{{ YXDnoticedata.value_17 }}</td>
          </tr>
          <tr>
            <td>2.客户要求TG值:</td>
            <td>{{ YXDnoticedata.value_18 }}</td>
            <td>14.厂内要求TG值:</td>
            <td>{{ YXDnoticedata.value_19 }}</td>
          </tr>
          <tr>
            <td>3.板材厂商/型号:</td>
            <td>{{ YXDnoticedata.value_20 }}</td>
            <td>15.CTI值:</td>
            <td>{{ YXDnoticedata.value_21 }}</td>
          </tr>
          <tr>
            <td>4.成品板厚:</td>
            <td>{{ YXDnoticedata.value_22 }}</td>
            <td>16.孔铜:</td>
            <td>{{ YXDnoticedata.value_23 }}</td>
          </tr>
          <tr>
            <td>5.外层铜厚:</td>
            <td>{{ YXDnoticedata.value_24 }}</td>
            <td>17.内层铜厚:</td>
            <td>{{ YXDnoticedata.value_25 }}</td>
          </tr>
          <tr>
            <td>6.表面工艺:</td>
            <td>{{ YXDnoticedata.value_26 }}</td>
            <td>18.表面工艺厚度:</td>
            <td>{{ YXDnoticedata.value_27 }}</td>
          </tr>
          <tr>
            <td>7.阻焊颜色:</td>
            <td>{{ YXDnoticedata.value_28 }}</td>
            <td>19.字符颜色:</td>
            <td>{{ YXDnoticedata.value_29 }}</td>
          </tr>
          <tr>
            <td>8.油墨型号:</td>
            <td>{{ YXDnoticedata.value_30 }}</td>
            <td>20.字符油墨型号:</td>
            <td>{{ YXDnoticedata.value_31 }}</td>
          </tr>
          <tr>
            <td>9.过孔方式:</td>
            <td>{{ YXDnoticedata.value_32 }}</td>
            <td>21.标记要求:</td>
            <td>{{ YXDnoticedata.value_33 }}</td>
          </tr>
          <tr>
            <td>10.成型方式:</td>
            <td>{{ YXDnoticedata.value_34 }}</td>
            <td>22.测试要求:</td>
            <td>{{ YXDnoticedata.value_35 }}</td>
          </tr>
          <tr>
            <td>11.验收标准:</td>
            <td>{{ YXDnoticedata.value_36 }}</td>
            <td>23.包装备注:</td>
            <td>{{ YXDnoticedata.value_37 }}</td>
          </tr>
          <tr>
            <td>12.出货附件:</td>
            <td>{{ YXDnoticedata.value_38 }}</td>
            <td>24.交货方式:</td>
            <td>{{ YXDnoticedata.value_39 }}</td>
          </tr>
          <tr>
            <td
              colspan="4"
              style="border-top: 2px solid black; border-bottom: 2px solid black; font-weight: bold; font-size: 16px; text-align: center"
            >
              产品特别信息
            </td>
          </tr>
          <tr>
            <td colspan="4" style="height: 25px">
              {{ YXDnoticedata.value_40 }}
            </td>
          </tr>
          <tr>
            <td
              colspan="4"
              style="border-top: 2px solid black; border-bottom: 2px solid black; font-weight: bold; font-size: 16px; text-align: center"
            >
              订单明细备注
            </td>
          </tr>
          <tr>
            <td colspan="4" style="height: 25px">
              {{ YXDnoticedata.value_41 }}
            </td>
          </tr>
          <tr>
            <td style="text-align: center; font-size: 16px; font-weight: bold">流程</td>
            <td colspan="3">订单预审--订单指派--开始制作--EQ发放--EQ回复--送检--审核--投产下线</td>
          </tr>
          <tr>
            <td style="text-align: center; font-size: 16px; font-weight: bold" rowspan="6">评审栏</td>
            <td>销售中心</td>
            <td>
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审通过
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审不通过(原因&emsp;&emsp;&emsp;&emsp;&emsp;)
            </td>
            <td>签名/日期:</td>
          </tr>
          <tr>
            <td>工程部</td>
            <td>
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审通过
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审不通过(原因&emsp;&emsp;&emsp;&emsp;&emsp;)
            </td>
            <td>签名/日期:</td>
          </tr>
          <tr>
            <td>品质部</td>
            <td>
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审通过
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审不通过(原因&emsp;&emsp;&emsp;&emsp;&emsp;)
            </td>
            <td>签名/日期:</td>
          </tr>
          <tr>
            <td>采购部</td>
            <td>
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审通过
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审不通过(原因&emsp;&emsp;&emsp;&emsp;&emsp;)
            </td>
            <td>签名/日期:</td>
          </tr>
          <tr>
            <td>计划部</td>
            <td>
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审通过
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审不通过(原因&emsp;&emsp;&emsp;&emsp;&emsp;)
            </td>
            <td>签名/日期:</td>
          </tr>
          <tr>
            <td>工艺部</td>
            <td>
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审通过
              <a-checkbox :checked="false" disabled></a-checkbox>&nbsp;评审不通过(原因&emsp;&emsp;&emsp;&emsp;&emsp;)
            </td>
            <td>签名/日期:</td>
          </tr>
        </table>
        <div style="display: flex; justify-content: space-between">
          <div style="font-size: 25px">CAM工程师:{{ YXDnoticedata.value_42 }}</div>
          <div style="font-size: 25px">一审工程师:</div>
          <div style="font-size: 25px">二审工程师:</div>
          <div style="width: 20px"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import htmlToPdf from "@/utils/htmlToPdf"; //横版a4
export default {
  name: "",
  props: ["YXDnoticedata", "ttype"],
  computed: {},
  data() {
    return {
      printObj1: {
        id: "yxdnoticedom", // 打印的区域
        preview: false, // 预览工具是否启用
        previewTitle: "打印预览", // 预览页面的标题
        popTitle: "&nbsp;", // 打印页面的页眉
        scanStyles: false,
      },
    };
  },
  mounted() {
    this.printObj1.closeCallback = this.closePrintTool;
  },
  methods: {
    closePrintTool() {
      document.title = this.ttype;
    },
    printpdf() {
      document.title = this.YXDnoticedata.pdctno;
    },
    getnoticePdf() {
      htmlToPdf("yxdnoticedom", this.YXDnoticedata.pdctno);
    },
  },
};
</script>

<style lang="less" scoped>
.printstyle {
  position: absolute;
  top: 716px;
  right: 26px;
}
td {
  font-size: 14px;
  color: black;
}
.onetable {
  border-right: 1px solid black;
  border-bottom: 1px solid black;
  td {
    text-align: left;
    border-left: 1px solid black;
    border-top: 1px solid black;
    padding: 4px 8px;
  }
}
.m-t-10 {
  /deep/.ant-col > label {
    color: red;
    font-weight: bold;
    font-size: 24px;
  }
  /deep/.ant-form-item-children {
    color: red;
    font-weight: bold;
    font-size: 24px;
  }
}
/deep/.ant-col-3 {
  width: 11.1%;
}
/deep/.ant-col-21 {
  width: 88.9%;
}
/deep/.ant-col-15 {
  width: 65.2%;
}
/deep/.ant-col-9 {
  width: 34.8%;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-form-item-label {
  border-bottom: 1px solid black;
  border-left: 1px solid black;
  padding-left: 7px;
  height: 32px;
  line-height: 32px;
  font-weight: bold;
}
/deep/.ant-form-item-control {
  border-bottom: 1px solid black;
  border-left: 1px solid black;
  padding-left: 7px;
  height: 32px;
  line-height: 32px;
  font-weight: bold;
  color: black;
}
/deep/.ant-form {
  border-top: 1px solid black;
  border-right: 1px solid black;
}
.pdfDom1 {
  height: 650px;
  overflow: auto;
}
</style>
