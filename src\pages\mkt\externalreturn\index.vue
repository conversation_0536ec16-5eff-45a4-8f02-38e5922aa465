<!--
 * @Author: CJP
 * @Date: 2022-06-16 18:33:32
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-06-20 22:08:55
 * @FilePath: \vue-antd-admin\src\pages\mkt\externalreturn\index.vue
 * @Description: 
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved. 
-->
<template>
<div>
    <div class='top'>
        <div class='left'>
            <a-table
             :columns="columns" 
             :data-source="data"
             bordered
             :pagination='false'
             :customRow='customRow'
             >  
             <template slot="fR4Type" slot-scope="text,record">
                <a-checkbox v-model="record.fR4Type">
                </a-checkbox>
            </template>           
                
            </a-table>
        </div>
        <div class='center'>中间</div>
        <div class='right'>右边</div>
    </div>
    <div class='footer'>
        <footer-action
        @onClick='onClick'
        @modelClick='modelClick'
        ></footer-action>
    </div>
    <a-modal
          title="取单设置"
          :visible="dataVisible3"
          @cancel="reportHandleCancel"
          @ok="handleOk3"
          ok-text="确定"
          destroyOnClose
          :maskClosable="false"
          :width="640"
          :confirmLoading='confirmLoading'
   >
   <edit-parameters-make ref='OrderRetrievalSettings'  :ParameterData='ParameterData'/>
   </a-modal>
</div>
</template>
<script>
 import { cuttingOutListt } from "@/services/mkt/e";
 import footerAction from "./compents/footerAction.vue";
 import EditParametersMake from "./compents/EditParametersMake";
import { 
  orderRetrievalSettings,RetrievalSettings
} from "@/services/projectApi";

const columns = [
    {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 40,
    align: 'center',    
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell:(record) =>{
      if(record.color_ != '#FF0000'){
        return {style:{'background':'#FF0000'}}
      }
    }    
  },
  {
    title: "拼板编号",
    dataIndex: "pdctno_",
    width: 130,
    ellipsis: true,
    className:"orderClass",
    align: 'center',    

  }, 
  {
    title: "板材类别",
    width: 100,
    ellipsis: true,
    dataIndex: "fR4Type",
    scopedSlots: {customRender: 'fR4Type'},
    align: 'center',
  }, 
  {
    title: "流程卡号",
    dataIndex: "cardNo",
    width: 110,
    ellipsis: true,
    align: 'center',
  },  
  {
    title: "板厚",
    width: 45,
    dataIndex: "boardThickness",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "铜厚",
    dataIndex: "copperThickness",
    width: 50,
    ellipsis: true,
    align: 'center',
  },
 
  
];
export default {
  components: { footerAction , EditParametersMake},
name:'',
data(){
    return{
    columns,
    data:[],
    dataVisible3:false,
    confirmLoading:false,
    ParameterData:{},
    keys:'',
    }
},
mounted(){
    this.getCuttingOutListt()
},

methods:{
    getCuttingOutListt(){
       cuttingOutListt().then(res =>{
           if(res.code){
                this.data = res.data
           }else{
               this.$message.error(res.message)
           }
       }) 
    },
    onClick(){
        this.getCuttingOutListt()
    },
    
    async modelClick(){
      console.log('弹窗')
      this.dataVisible3 = true
      let params = 611359
      await orderRetrievalSettings(params).then( res =>{
        if(res.code){
          this.ParameterData = res.data
          console.log('111',this.ParameterData) 
        }else{
          this.$message.error(res.message)
        }
      })
    },
    reportHandleCancel(){
      this.dataVisible3 = false
    },
    handleOk3(){
     let params = this.$refs.OrderRetrievalSettings.ParameterForm
     console.log('params',params)
      RetrievalSettings(params).then( res =>{
        if(res.code){
          this.$message,success('设置成功')
        }else{
          this.$message.error(res.message)
        }
      })
      this.dataVisible3 = false
    },
     customRow(record) {
      return {
        on: {
          click: () => {
            this.keys =record.guid_             
                  
          }            
        }
      }
    },
}
}
</script>
<style scoped lang='less'>
.top{
    width:100%;
    height:500px;
    border: 2px solid black;
    display:flex;
    .left{
        width:49%;
        height: 100%;
        border: 2px solid red;
        display:inline-block;
    }
    .center{
        width:28%;
        height: 100%;
        border: 2px solid green;
        display:inline-block;
    }
    .right{
        width:19%;
        height: 100%;
        border: 2px solid blue;
        display:inline-block;
    }
}
.footer{
    width:100%;
    height:50px;
    border: 2px solid black;

}
</style>
