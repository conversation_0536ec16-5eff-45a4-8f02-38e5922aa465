<template>
    <div>

    </div>
</template>

<script>
import {
  loginOA,
  applicationConfiguration,
  getHomePage
} from "@/services/user";
    import { setAuthorization } from "@/utils/request";
    import { mapMutations } from "vuex";
    import { loadRoutes } from "@/utils/routerUtil";
    export default {
        name: "",
        data() {
            return {}
        },
        component: {},
        methods: {

            ...mapMutations("account", ["setUser", "setPermissions", "setRoles"]),
            afterLogin(res) {
                const loginRes = res;
                if (loginRes) {
                    setAuthorization({
                        token: loginRes.access_token,
                        expireAt: new Date(new Date().getTime() + loginRes.expires_in),
                    });
                    applicationConfiguration().then((res) => {
                        res.currentUser.tenantName=res.currentTenant.name;
                        this.setUser(res.currentUser);
                        let permissions = this.handlePermissions(res.auth.grantedPolicies);
                        this.setPermissions(permissions);
                        this.setRoles(res.currentUser.roles);
                        loadRoutes();
                        this.$message.success("登录成功", 3);
                      getHomePage().then(res => {
                        if (res.data) {
                          this.$router.push('/'+ res.data)
                        } else {
                          this.$router.push('/dashboard/analysis')
                        }
                      })
                    });
                } else {
                    this.error = loginRes.message;
                }
            },
            handlePermissions(obj) {
                let permissions = [];
                if (!obj) {
                    return permissions;
                }
                permissions = Object.keys(obj).map((x) => {
                    return {
                        id: x,
                        operation: [],
                    };
                });
                return permissions;
                // let list = Object.keys(obj).map((x) => {
                //   let n = x.split(".").length - 1;
                //   return {
                //     val: x,
                //     num: n,
                //   };
                // });
                // let idList = list.filter((x) => x.num == 1);
                // permissions = idList.map((x) => {
                //   let operation = list
                //     .filter((y) => y.num == 2 && y.val.indexOf(x.val) > -1)
                //     .map((y) => {
                //       return y.val.split(".")[2];
                //     });
                //   return {
                //     id: x.val,
                //     operation: operation,
                //   };
                // });
                // return permissions;
            },
        },
        created() {
            let oa_iv=decodeURIComponent(this.$route.query.iv)
            let token=decodeURIComponent(this.$route.query.token)
            let timesTamp = decodeURIComponent(this.$route.query.timestamp)
            if (timesTamp > (Date.now() +5*60*1000)) {
              this.$message.error('登录已失效');
              this.$router.push('/login');
            } else {
              loginOA(oa_iv,token).then(res=>{
                console.log(res)
                this.afterLogin(res)
              })
            }

        }
    }
</script>

<style scoped>

</style>
