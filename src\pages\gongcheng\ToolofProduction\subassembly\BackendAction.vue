<!-- 工程管理 - 生产工具  -按钮 -->
<template>
  <div class="active" ref="active">
    <div class="box showClass">
      <!-- <a-button  type="primary" @click="clickUpload1">
      文件上传
    </a-button>  -->
      <!--   :directory="true"        :multiple="true"-->
      <a-upload
        :file-list="fileList"
        accept=".zip"
        ref="fileRef1"
        :customRequest="downloadFilesCustomRequest"
        :showUploadList="false"
        @change="handleChange"
        webkitdirectory
        :before-upload="beforeUpload"
        :openFileDialogOnClick="false"
      >
        <a-button style="width: 80px; display: none"><a-icon type="upload" /></a-button>
      </a-upload>
    </div>
    <div class="box showClass">
      <a-button type="primary" @click="queryclick"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolSendEmail')"
      :class="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolSendEmail') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('Sendsteelmesh')"> 发送钢网 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolSend')"
      :class="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolSend') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Dispatchorders"> 分派 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolOrderStart')"
      :class="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolOrderStart') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Orderstart"> 开始 </a-button>
    </div>
    <!-- <div class="box"  v-show="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolOrderDownload')"
    :class='checkPermission("MES.EngineeringModule.ProductionTool.ProductionToolOrderDownload")?"showClass":""' >
    <a-button type="primary" @click="FileDownload">
      文件下载
    </a-button>
  </div> -->
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolOrderFinish')"
      :class="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolOrderFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Ordercompletion"> 完成 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolGetOrder')"
      :class="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolGetOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Pickinguporders"> 取单 </a-button>
    </div>
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolProFeedback')"
      :class="checkPermission('MES.EngineeringModule.ProductionTool.ProductionToolProFeedback') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="Productionfeedback"> 生产反馈 </a-button>
    </div>
    <!-- <div class="box">
    <a-button  type="primary" @click="Fileupload">
      文件上传
    </a-button> 
    <a-upload 
        name="file"
        ref="fileRef"
        :before-upload="beforeUpload3"
        :customRequest="httpRequest6"
        :file-list="fileList6"
        :show-upload-list="false"
        v-show="false"
        :maxCount="1"
        @change="handleChange6"
      >
      <a-button style="width: 80px;"><a-icon type="upload" /></a-button>
    </a-upload>
  </div> -->
    <div
      class="box"
      v-show="checkPermission('MES.EngineeringModule.ProductionTool.DelOrder')"
      :class="checkPermission('MES.EngineeringModule.ProductionTool.DelOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="delorderno"> 删除订单 </a-button>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import JSZip from "jszip";
import { saveAs } from "file-saver";
import { checkPermission } from "@/utils/abp";
import { uploadfile, uploadfilev2 } from "@/services/ToolofProduction";

export default {
  name: "BackendAction",
  data() {
    return {
      files: [],
      type1: "",
      fileList: [],
      listlength: 0,
      filecount: 0,
      advanced: false,
      isFileType: true,
      width: 762,
      collapsed: false,
      fileList6: [],
      orderId: "",
      alypath_rou: "",
      showBtn: false,
      ids1: "",
    };
  },
  props: ["selectedRowsData"],
  created() {},
  methods: {
    checkPermission,
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    handleChange(info) {
      this.fileList = info.fileList;
    },
    async beforeUpload(file, filelist) {
      this.listlength = filelist.length;
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng = file.name.toLowerCase().indexOf(".zip") != -1 || file.name.toLowerCase().indexOf(".rar") != -1;
        const filesize = Number(file.size / 1024 / 1024) < 500;
        if (!isJpgOrPng) {
          _this.$message.error("上传文件只支持.zip/.rar压缩包格式!");
          reject();
        } else if (!filesize) {
          _this.$message.error("文件大小不能超过500MB!");
          reject();
        } else {
          resolve();
        }
      });
    },
    getFileData(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = () => reject(reader.error);
        reader.readAsArrayBuffer(file);
      });
    },
    guid(name, lastModified, size, type) {
      return this.$md5(name + "#" + lastModified + "#" + size + "#" + type);
    },
    //分段上传MD5
    async downloadFilesCustomRequest(data) {
      this.$emit("pageload", true);
      let GUID;
      let shardSize;
      let shardCount;
      const str = data.file.name;
      const replacedText = str.replace(/\s+/g, " ");
      let size = data.file.size;
      GUID = this.guid(replacedText, data.file.lastModified, data.file.size, data.file.type);
      if (size / 1024 / 1024 > 50) {
        shardSize = 1024 * 1024 * 2;
      } else {
        shardSize = 1024 * 1024;
      }
      shardCount = Math.ceil(size / shardSize); //总片数
      const formData = new FormData();
      formData.append("id", this.selectedRowsData.id);
      formData.append("Type", this.type1);
      formData.append("FileSeg", ""); //文件
      formData.append("MD5Code", GUID); // md5
      formData.append("FileName", replacedText); //文件名
      formData.append("startpos", 0); //当前开始长度
      formData.append("FileSize", size); //文件尺寸
      formData.append("FactoryId", this.selectedRowsData.joinFactoryId); //工厂
      for (var i = 0; i < shardCount; i++) {
        const start = i * shardSize;
        const end = Math.min(size, start + shardSize);
        formData.set("FileSeg", data.file.slice(start, end));
        formData.set("startpos", i * shardSize);
        let headers = {};
        headers["Content-Disposition"] = 'form-data; name="FileName"; filename="' + encodeURIComponent(name) + '"';
        headers["Content-Type"] = "text/html;charset=UTF-8";
        await axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/pro-tool/pro-tool-order-finish-v3", // 接口地址
          method: "post",
          data: formData,
          headers: headers, // 请求头
        }).then(res => {
          if (res.code == 1) {
            if (i == shardCount - 1) {
              data.onSuccess();
              this.$emit("finishv3");
            }
          } else {
            this.$message.error(res.message);
            data.onError(res.message);
            this.$emit("pageload", false);
            i = shardCount;
          }
        });
      }
    },
    handleChange6({ fileList }) {
      if (this.isFileType) {
        this.fileList6 = fileList;
      }
    },
    beforeUpload3(file) {
      this.isFileType =
        file.name.toLowerCase().indexOf(".zip") != -1 ||
        file.name.toLowerCase().indexOf(".rar") != -1 ||
        file.name.toLowerCase().indexOf(".tgz") != -1;
      if (!this.isFileType) {
        this.$message.error("该功能只支持.zip.rar.tgz");
      }
      return this.isFileType;
    },
    async httpRequest6(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      await uploadfile(this.ids1, formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.$message.success("文件上传成功");
          this.$emit("getOrderList");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    queryclick() {
      this.$emit("queryclick");
    },
    Pickinguporders() {
      this.$emit("Pickinguporders");
    },
    Productionfeedback() {
      this.$emit("Productionfeedback");
    },
    Ordercompletion() {
      this.$emit("Ordercompletion");
    },
    delorderno() {
      this.$emit("delorderno");
    },
    Orderstart() {
      this.$emit("Orderstart");
    },
    Dispatchorders() {
      this.$emit("Dispatchorders");
    },
    FileDownload() {
      this.$emit("FileDownload");
    },
    Fileupload() {
      this.$emit("Fileupload");
    },
    clickUpload(id) {
      this.ids1 = id;
      this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
    clickUpload1(type) {
      this.type1 = type;
      this.$refs.fileRef1.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
    },
  },
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  margin-right: 10px;
  .box {
    width: 90px;
    margin-top: 4px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
