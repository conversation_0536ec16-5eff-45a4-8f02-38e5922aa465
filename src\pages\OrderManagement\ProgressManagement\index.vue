<!--生产管理- 生产进度 -->
<template>
    <a-spin :spinning="spinning"> 
      <div class="projectmanagement" ref="SelectBox">
        <div class="content">   
            <!-- <span>层数：</span>             
            <a-select  style="width:8%;margin-right:0.5%;" v-model="formData.layer">
                <a-select-option :key="0" > -层数-</a-select-option>
                <a-select-option :key="1" > 1</a-select-option>
                <a-select-option :key="2" > 2</a-select-option>
                <a-select-option :key="4" > 4</a-select-option>
                <a-select-option :key="6" > 6</a-select-option>
                <a-select-option :key="8" > 8</a-select-option>
                <a-select-option :key="10" > 10</a-select-option>
                <a-select-option :key="12" > 12</a-select-option>
                <a-select-option :key="14" > 14</a-select-option>
                <a-select-option :key="16" > 16</a-select-option>
                <a-select-option :key="18" > 18</a-select-option>
                <a-select-option :key="20" > 20</a-select-option>
                </a-select> 
                <span>TG值：</span>      
                <a-select  style="width:8%;margin-right:0.5%;" v-model="formData.tg">
                <a-select-option :key="0" > -TG值-</a-select-option>
                <a-select-option :key="130" > 130</a-select-option>
                <a-select-option :key="150" > 150</a-select-option>
                <a-select-option :key="170" > 170</a-select-option>
                </a-select> 

            <span>交期：</span> 
            <a-date-picker  
                style="margin-right:0.5%;"              
                format="YYYY-MM-DD "               
                placeholder="开始交期"
                @change="onChange1"
            />
            <a-date-picker  
                style="margin-right:0.5%;"              
                format="YYYY-MM-DD "               
                placeholder="结束交期"
                @change="onChange2"
            /> -->
            <span>拼版编号：</span> 
            <a-input v-model="form.orderNo" placeholder="请输入拼版编号" allowClear @keyup.enter.native="searchClick" style="width:140px;"  ></a-input>  
            <span style="padding-left:9px">管制卡编号：</span> 
            <a-input v-model="form.CardNo" placeholder="请输入管制卡编号" allowClear @keyup.enter.native="searchClick" style="width:140px" ></a-input>  
            <span style="padding-left:9px">协同工厂：</span>
            <a-select  placeholder="请选择工厂" v-model="form.JoinFactoryId" allowClear showSearch style="width:140px;" 
             @keyup.enter.native="searchClick" optionFilterProp="lable" :getPopupContainer="()=>this.$refs.SelectBox">             
                <!-- <a-select-option :key="1" > M01</a-select-option> -->
                <a-select-option v-for="(ite,index) in factorList" :key="index" :value="ite.valueMember"  :lable="ite.text" > {{ite.text}}</a-select-option>
            </a-select>                  
            <a-button  v-if="checkPermission('MES.ProManagement.ProductionProgress.ProcChaXun')" type="primary"  @click="searchClick" style="margin-right:0.5%;">搜索</a-button>
            <a-button v-if="checkPermission('MES.ProManagement.ProductionProgress.Oversequence')" type="primary" @click="overClick" style="margin-right:0.5%;">部门过序</a-button>   
            <a-button v-if="checkPermission('MES.ProManagement.ProductionProgress.PrintCard')" type="primary" @click="PrintClick" style="margin-right:0.5%;">打印流程卡</a-button>  
            <a-button v-if="checkPermission('MES.ProManagement.ProductionProgress.FlowDel')" type="primary" @click="delClick" style="margin-right:0.5%;">删除过序</a-button> 
            <a-button v-if="checkPermission('MES.ProManagement.ProductionProgress.SetFeed')" type="primary" @click="feededClick" style="margin-right:0.5%;">设置补料</a-button>  
            <a-button v-if="checkPermission('MES.ProManagement.ProductionProgress.CancelFeed')" type="primary" @click="CancelFeedClick" style="margin-right:0.5%;">取消补料</a-button> 
            <a-button  v-if="checkPermission('MES.ProManagement.ProductionProgress.SetUrgent')" type="primary" @click="setUrgentclick" style="margin-right:0.5%;">设置加急</a-button>  
            <a-button  v-if="checkPermission('MES.ProManagement.ProductionProgress.CancelUrgent')" type="primary" @click="nCancelUrgentclick" style="margin-right:0.5%;">取消加急</a-button> 
            <a-button  v-if="checkPermission('MES.ProManagement.ProductionProgress.SetStop')" type="primary" @click="stopclick" style="margin-right:0.5%;">暂停生产</a-button>
            <a-button  v-if="checkPermission('MES.ProManagement.ProductionProgress.CancelStop')" type="primary" @click="CancelStopclick" style="margin-right:0.5%;">启动生产</a-button>  
             
            
        </div>
        <div class='box' style=" display:flex;" >
          <div class="leftContent" style="width: 12%;height: 100%;border: 2px solid rgb(233 230 230);border-bottom: none;overflow: auto;">            
              <ul v-for = "(item,index) in leftList" :key="index">                
                <li  @click="liClick(item)" :class="[item.step == step ?'licolor':'',index == 0?'indexClass':'']"  >
                  {{item.stepName}} (<span style="color:#c00;font-weight: 700;">{{item.pinBanCount}}</span > /<span style="color:#c00;font-weight: 700;">{{item.orderCount}}</span > / <span style="color:#c00;font-weight: 700;">{{item.cardCount}}</span> )
                </li>
              </ul>           
          </div> 
          <div class="rightContent" style="width: 88%;height: 100%">            
              <template>
                  <a-tabs  type="card" @change="callback" >
                      <a-tab-pane key="10" tab="生产中流程卡">                      
                        <order-list 
                        v-if="pageshow"
                        :params1="params1"
                        :orderListTableLoading="orderListTableLoading"
                        :rowKey="(record,index)=>{return index}"
                        @tableChange="handleTableChange"
                        :pagination="pagination"
                        ref="orderList" 
                        :orderListData="orderListData"
                        :class="orderListData.length ? 'min-table':''"
                        @cardNoClick="cardNoClick"
                         ></order-list> 
                      </a-tab-pane>
                      <!-- <a-tab-pane key="20" tab="已完成流程卡">
                          <order-list 
                          :orderListTableLoading="orderListTableLoading"
                          :rowKey="'id'"
                          @tableChange="handleTableChange"
                          :pagination="pagination"
                          ref="orderList" 
                          :orderListData="orderListData"
                          :class="orderListData.length ? 'min-table':''"
                          @cardNoClick="cardNoClick"
                          ></order-list>
                      </a-tab-pane>
                      <a-tab-pane key="" tab="全部流程卡">
                          <order-list 
                          :orderListTableLoading="orderListTableLoading"
                          :rowKey="'id'"
                          @tableChange="handleTableChange"
                          :pagination="pagination"
                          ref="orderList" 
                          :orderListData="orderListData"
                          :class="orderListData.length ? 'min-table':''"
                          @cardNoClick="cardNoClick"
                          ></order-list> 
                      </a-tab-pane> -->
                  </a-tabs>
              </template>
          </div> 
        </div>
        <div class="bto"></div>
      </div>
      <a-modal
        title="过数详情"
        :visible="modelVisible1"
        @cancel="handleCancel"       
        :footer="null"
        :width="750"
      >
        <excess-details  ref="ExcessDetails"  :ExcessData="ExcessData" />      
      </a-modal>
      <!--打印流程卡弹窗 -->
      <a-modal
          title="流程卡"
          :visible="dataVisible1"
          @cancel="handleCancel2"
          destroyOnClose
          :maskClosable=false
          :width="1000"
          :footer="null"
      >
      <report-info  ref="report"  :CardId="CardId" :businessOrderNo="businessOrderNo" />
      </a-modal>
      <!--确认弹窗 -->
      <a-modal
      title=" 确认弹窗"
      :visible="dataVisibleMode"
      @cancel="reportHandleCancel"
      @ok="handleOkMode"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered

   >
   <span style="font-size:16px;">【{{orderno}}】</span> 
   <span style="font-size:16px;">{{messageMode}}</span>   
   </a-modal>
    </a-spin>
</template>

<script>

import moment from "moment";
//调取子组件
import OrderList from "@/pages/OrderManagement/ProgressManagement/modules/OrderList";
import ExcessDetails from "@/pages/OrderManagement/ProgressManagement/modules/ExcessDetails";
import ReportInfo from "@/pages/OrderManagement/orderDetail/modules/ReportInfo";

import {
  progressLeftList,
  progressList,
  cardFlowList,
} from "@/services/scgl/OrderManagement/progress"
import {
  deleteStep,setFeeded,setNotFeeded,seturgent,setnoturgent,setstop,setnotstop,factorCode,
} from "@/services/scgl/OrderManagement/ControlCard"
import { urlToHttpOptions } from "url";
import {mapState,} from 'vuex';
import {checkPermission} from "@/utils/abp";
export default {
    name:'',
    //初始子组件
    components: {OrderList,ExcessDetails,ReportInfo},
    inject:['reload'],
    data(){ 
        return{
          spinning:false,      
          params1:{},    
          pagination: {
            pageSize: 20,
            current: 1,
            total:0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20",  "50", "100"],//每页中显示的数据
            showTotal: (total) => `总计 ${total} 条`,
          },
          leftList:[],
          orderListTableLoading:false,
          orderListData:[],
          form:{
            orderNo:'',
            CardNo:'',
            JoinFactoryId:undefined,                                   
          },
          step:"",
          Status:'10' ,
          factorid:"",
          queryParam:{},
          modelVisible1:false,
          ExcessData:[],
          CardId:'',
          dataVisible1:false,
          dataVisibleMode:false,
          messageMode:'',
          orderno:'',
          businessOrderNo:'',
          factorList:[],
          pageshow:true,
        }
    },
    computed: {
    ...mapState('account', ['user',]),
    },
    mounted(){
      this.getDetailInfo()
      if(this.factorid){
        this.getLeftData(this.factorid)
      }else{
        this.getLeftData()
      }
      
      factorCode().then(res=>{
        if(res.code){
          this.factorList = res.data
        }
      })
    },
   methods: {
    checkPermission,
    moment,
    // onChange1 (value, dateString) {
    //   this.formData.StartDeliveryDate = dateString
    // },
    // onChange2 (value, dateString) {
    //   this.formData.EndDeliveryDate = dateString
    // },
    //  标签页点击回调
    callback(key){
      this.Status = key
      this.getDetailInfo()
    },
    // 左边列表
    getLeftData(queryData){
      let par = {
        'Status':this.Status
      }
      if(queryData){
        par.JoinFactoryId = queryData
      }      
      progressLeftList (par).then(res => {
        if (res.code) {
          this.leftList = res.data       
        }
      }) 
    },
    // 生产列表
    getDetailInfo(queryData){ 
      this.params1 = {}
      this.orderListTableLoading = true
      let obj = JSON.parse( localStorage.getItem('scconciliation')) 
      if(obj){
        this.params1 = obj
        queryData = obj
        this.factorid = obj.JoinFactoryId
        if( this.factorid){
          this.getLeftData(this.factorid)
        }
      }
      this.pageStat=localStorage.getItem('scstat')== 'true'? true:false;  
      if(this.pageStat){
        let pageCurrent=localStorage.getItem('scpageCurrent')
        if(pageCurrent!=undefined&&pageCurrent!=''){
            this.pagination.current= parseInt(pageCurrent)
        }
        let pageSize=localStorage.getItem('scpageSize')
        if(pageSize!=undefined&&pageSize!=''){
            this.pagination.pageSize= parseInt(pageSize)
        }
        let data=localStorage.getItem('scqueryParam')
        if(data!=null&&data!=undefined&&data!=''){
            this.queryParam=JSON.parse(data)
        }
        }  
      this.queryParam.pageIndex=this.pagination.current
      this.queryParam.pageSize=this.pagination.pageSize
      let data = {
          ...this.queryParam
      }
      localStorage.setItem('scqueryParam',JSON.stringify(data))  
      if(!this.Status){
        this.Status = ''
      }else{
        this.Status = Number(this.Status)
      }      
      let params = {
        'PageIndex': this.pagination.current,
        'PageSize' :this.pagination.pageSize,
        'Status':this.Status,
        'StepKey':this.step
      }    
      var obj2 = Object.assign(params,queryData)       
      let indexId=localStorage.getItem('scid') 
      let record= localStorage.getItem('screcord')   
      // 生产列表数据
      progressList(obj2).then(res => {
        if (res.code) {
          this.orderListData = res.data.items;
          this.pagination.total = res.data.totalCount; 
          localStorage.removeItem('scconciliation')
          this.pagination.current = res.data.pageIndex 
          if(indexId!==''&&indexId!=null){
            this.$refs.orderList.id = indexId
            this.$refs.orderList.selectedRowKeysArray[0] = indexId
          }
          if(record!==''&&record!=null) {
            this.$refs.orderList.selectedRowsData = JSON.parse(record)
           }
          if(this.pageStat){  
            localStorage.removeItem('scid')
            localStorage.removeItem('screcord')
            localStorage.removeItem('scpageCurrent')
            localStorage.removeItem('scpageSize')
            localStorage.removeItem('scstat')
            }         
        }
      }).finally(()=>{
        this.orderListTableLoading = false
      })
    },
    // 左边列表点击事件
      liClick(record){
        if(record.step){
          this.step = record.step
        }else{
          this.step = ''
        }
        this.pagination.current = 1
        this.getDetailInfo(this.form)       
      },     
  
    // handleTableChange(pagination) {
    //   this.pagination.current=pagination.current
    //   this.getDetailInfo()
    // },
    handleTableChange(pagination, ) {
      this.pagination.current=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      localStorage.setItem('scconciliation',JSON.stringify(this.form))
      if(JSON.stringify(this.form) != '{}'){
            this.getDetailInfo(this.form)
          }else{
            this.getDetailInfo();
          }
      if(this.form.JoinFactoryId){
        this.getLeftData(this.form.JoinFactoryId)
      }else{
        this.getLeftData()
      }
    },

    //条件筛选请求接口
    searchClick(){
      var arr1 = this.form.orderNo.split('')
      if(arr1.length >30){
        arr1 = arr1.slice(0,30)
      }
      this.form.orderNo = arr1.join('')
      var arr2 = this.form.CardNo.split('')
      if(arr2.length >50){
        arr2 = arr2.slice(0,50)
      }
      this.form.CardNo = arr2.join('') 
      this.pageshow = false
      this.pagination.current = 1;     
      this.getDetailInfo(this.form)
      if(this.form.JoinFactoryId){
        this.getLeftData(this.form.JoinFactoryId)
      }else{
        this.getLeftData()
      }    
      this.params1 = this.form
      localStorage.setItem('scconciliation',JSON.stringify(this.params1))
      this.$nextTick(() => {
           this.pageshow = true
        })
    },
    // 过数详情
    cardNoClick(record){
      cardFlowList(record.cardNo).then(res=>{
        if(res.code){
          this.ExcessData = res.data
        }else{
          this.$message.error(res.message)
        }
      })
      this.modelVisible1 = true
    },
    handleCancel(){
      this.modelVisible1 = false
    },
    handleCancel2(){
      this.dataVisible1 = false
      this.getDetailInfo()
     
    },
    // 部门过序
    overClick(){
      this.$router.push({path:'Supersequence',})     
    },
    // 打印流程卡
    PrintClick(){
      this.CardId = this.$refs.orderList.id
      this.businessOrderNo = this.$refs.orderList.selectedRowsData.businessOrderNo
      if(!this.CardId){
        this.$message.warning('请选择流程卡')
        return
      }
      console.log('打印',this.$refs.orderList.id)
      
      this.dataVisible1 = true
    },
    // 删除过序
    delClick(){
      if(!this.$refs.orderList.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.orderno = this.$refs.orderList.selectedRowsData.cardNo
      this.messageMode = '确认删除过序吗？'
      this.type = '1'
      this.dataVisibleMode = true  

    },
   
    // 设置补料
    feededClick(){
      if(!this.$refs.orderList.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.orderno = this.$refs.orderList.selectedRowsData.cardNo
      this.messageMode = '确认设置补料吗？'
      this.type = '2'
      this.dataVisibleMode = true 
    },
    // 取消补料
    CancelFeedClick(){
      if(!this.$refs.orderList.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.orderno = this.$refs.orderList.selectedRowsData.cardNo
      this.messageMode = '确认取消补料吗？'
      this.type = '3'
      this.dataVisibleMode = true 
    },
    
     // 设置加急
     setUrgentclick(){
      if(!this.$refs.orderList.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.orderno = this.$refs.orderList.selectedRowsData.cardNo
      this.messageMode = '确认设置加急吗？'
      this.type = '4'
      this.dataVisibleMode = true  
    },
  // 取消加急
  nCancelUrgentclick(){
      if(!this.$refs.orderList.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.orderno = this.$refs.orderList.selectedRowsData.cardNo
      this.messageMode = '确认取消加急吗？'
      this.type = '5'
      this.dataVisibleMode = true  
    },
// 暂停生产
stopclick(){
      if(!this.$refs.orderList.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.orderno = this.$refs.orderList.selectedRowsData.cardNo
      this.messageMode = '确认暂停生产吗？'
      this.type = '6'
      this.dataVisibleMode = true  
    },
    // 启动生产
    CancelStopclick(){
      if(!this.$refs.orderList.selectedRowKeysArray[0]){
        this.$message.warning('请选择订单')
        return
      }
      this.orderno = this.$refs.orderList.selectedRowsData.cardNo
      this.messageMode = '确认启动生产吗？'
      this.type = '7'
      this.dataVisibleMode = true  
    },
    reportHandleCancel(){
      this.dataVisibleMode = false;
    },
    handleOkMode(){
      // 删除
      if(this.type == '1'){
        this.spinning = true
        let params = {
        "cardNo": this.$refs.orderList.selectedRowsData.cardNo,
        "account": this.user.userName,
        "name": this.user.name,
      }
        deleteStep(params).then(res => {
          if (res.code) {
            this.$message.success('已删除')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })            
      } 
     
      if(this.type == '2'){
        this.spinning = true        
        setFeeded(this.$refs.orderList.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success('设置补料成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })            
      } 
      if(this.type == '3'){
        this.spinning = true        
        setNotFeeded(this.$refs.orderList.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success('取消补料成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })            
      } 
       // 设置加急
       if(this.type == '4'){
        this.spinning = true
      seturgent(this.$refs.orderList.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success('设置加急成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })            
      } 
       // 取消加急
       if(this.type == '5'){
        this.spinning = true
        setnoturgent(this.$refs.orderList.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success('取消加急成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })            
      } 
// 暂停生产
    if(this.type == '6'){
        this.spinning = true
        setstop(this.$refs.orderList.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success('暂停生产成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })            
      } 
      // 启动生产
      if(this.type == '7'){
        this.spinning = true
        setnotstop(this.$refs.orderList.selectedRowsData.id).then(res => {
          if (res.code) {
            this.$message.success('启动生产成功')
          } else {
            this.$message.error(res.message)
          }
          this.reload()
        }).finally(() => {
          this.spinning = false
        })            
      } 
      this.dataVisibleMode = false; 
    },
   
    
                  
  }
}
</script>

<style scoped lang="less">
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.ant-select-dropdown-menu-item{
  font-weight: 500;
  text-align: left;
  padding: 5px 9px!important;
}

/deep/.ant-input{
  font-weight: 500;
}
/deep/.ant-table-thead > tr > th {
    padding: 5px 4px;
    overflow-wrap: break-word;
}
/deep/.ant-table-tbody > tr > td {
    padding:5px 4px;
    overflow-wrap: break-word;
}
  .ant-table-row-cell-break-word{
    position:relative;
  }
  .span{
    position: absolute;
    top:5%;
  }
  p{
    margin:0;
  }
.projectmanagement {
  min-width: 1670px;
  padding-top:10px;
  /deep/.userStyle{
    user-select: all!important;
  }
  // height: 834px;
  width: 100%; 
  /deep/  .ant-table-empty .ant-table-body{
    overflow-x: hidden!important;
    overflow-y: hidden !important
  }
  .content{
    height:42px;
    padding-left:6px;
    background: #FFFFFF;
  }
  .ant-input,.ant-select{
    width:8%;
    margin-right:0.5%;
    margin-top:6px;
  }
  /deep/.ant-input{
    padding:4px;
  }
  /deep/.ant-input-affix-wrapper .ant-input-suffix {
    right: 4px;
  }
  .box {
  height:734px;
  border-top:none!important;
  border: 2px solid rgb(233, 233, 240);
  border-bottom: 4px solid rgb(233, 233, 240);
  background: #ffffff;
    /deep/.rowBackgroundColor {
    background: #FFF9E6!important;
  }
  .leftContent{
    ul{
      margin-bottom: 0;
      padding-left:0;
    }
    ul li{
      list-style: none;
      margin: 0;
      border-bottom: 1px solid rgb(233 230 230) ;
      padding-left: 10px;
      font-size: 14px;
      font-weight: 500;
      height:32px;
      line-height: 32px;
    }
    .indexClass{
      height:31px;
    }
    li:hover{
      color:white;
      font-size: 16px;
      font-weight: 500;
      background-color: #ff9900;
    }
    .licolor{
    color:white;
    font-size: 16px;
    font-weight: 500;
    background-color: #ff9900;
  }
  }
 /deep/ .rightContent{
   .min-table {
      .ant-table-body{
        min-height:667px!important;
      }
    }
  }
    /deep/ .ant-tabs {
      .viewInfo{
        .ant-table-thead{
          .ant-table-align-left{
            text-align: center!important;;
          }
        }
      }
      .ant-tabs-bar{
        margin: 0;
        border-bottom: 1px solid #ccc;
        .ant-tabs-nav-wrap {
          .ant-tabs-ink-bar {
            display: none!important;
          }
        }
        .ant-tabs-tab {
          margin: 0;
          border: 1px solid #ccc;
          font-size: 14px;
          height: 34px;
          line-height: 34px;
          border-left: 0;
          font-weight: 500;
          &:nth-child(1){
            border-left: 1px solid #ccc;;
          }
        }
        .ant-tabs-tab-active {
          border-top: 2px solid #f90 !important;
          border-bottom-color:#ffffff ;
          background: #ffffff;
          font-weight: 500;
  
        }
  
      }
    }
  }
  .bto{
  height:50px;
  background: #FFFFFF;
  border:2px solid #E9E9F0;
}
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
    }
    .ant-tabs-nav-container{
      height: 33px;
      
    }

  } 
   /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(223 220 220);
    }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // } 
  /deep/ .ant-table-thead > tr > th {
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table{
    min-height:703px;
    .ant-table-thead > tr > th{
      border-right:1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      border-right:1px solid #efefef;
    }
    tr.ant-table-row-selected td {
     background: rgb(223 220 220);
    }
    tr.ant-table-row-hover td {
     background: rgb(223 220 220);
    }
    .rowBackgroundColor {
      background: rgb(223 220 220)!important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding:0;
    }
  }
  /deep/ .ant-input-disabled{
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 6px 0 0 10px;
  }

}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}


</style>

