<template>
    <div ref="SelectBox">
        <a-modal
    :title="model.id? '编辑' : '新增'"
    :width="640"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="产品类别" ref="productType_" prop="productType_">
           <a-select  style="width: 120px" v-model="form.productType_"  :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="单面板">
                    单面板
                </a-select-option>
                <a-select-option value="双面板">
                    双面板
                </a-select-option>
                <a-select-option value="多层板">
                    多层板
                </a-select-option>
                <a-select-option value="铝基板">
                    铝基板
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="	板材类型" ref="orderType_" prop="orderType_">
          <a-select  style="width: 120px" v-model="form.orderType_"  :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="FR-4">
                    FR-4
                </a-select-option>
                <a-select-option value="CEM-1">
                    CEM-1
                </a-select-option>
                <a-select-option value="铝基板">
                    铝基板
                </a-select-option>
                <a-select-option value="FR-1">
                    FR-1
                </a-select-option>
                <a-select-option value="22F">
                    22F
                </a-select-option>
                <a-select-option value="CEM-3">
                    CEM-3
                </a-select-option>
                <a-select-option value="94HB">
                    94HB
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="品牌" ref="machineType_" prop="machineType_">
            <a-select  style="width: 120px" v-model="form.machineType_"  :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="生益">
                   生益
                </a-select-option>
                <a-select-option value="建滔">
                    建滔
                </a-select-option>
                <a-select-option value="国纪">
                    国纪
                </a-select-option>
                <a-select-option value="宏瑞兴">
                    宏瑞兴
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="型号" ref="boardType_" prop="boardType_">
          <a-select  style="width: 120px" v-model="form.boardType_"  :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="S1141">
                    S1141
                </a-select-option>
                <a-select-option value="S1000H">
                    S1000H
                </a-select-option>
                <a-select-option value="S1000-2M">
                    S1000-2M
                </a-select-option>
                <a-select-option value="KB-6160">
                    KB-6160
                </a-select-option>
                <a-select-option value="KB-6165F">
                    KB-6165F
                </a-select-option>
                <a-select-option value="GF212">
                    GF212
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="板厚" ref="boardthick_" prop="boardthick_">
          <a-select  style="width: 120px" v-model="form.boardthick_"  :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="0.4">
                    0.4
                </a-select-option>
                <a-select-option value="0.6">
                    0.6
                </a-select-option>
                <a-select-option value="0.8">
                    0.8
                </a-select-option>
                <a-select-option value="1">
                    1
                </a-select-option>
                <a-select-option value="1.2">
                    1.2
                </a-select-option>
                <a-select-option value="1.6">
                    1.6
                </a-select-option>
                <a-select-option value="2">
                    2
                </a-select-option>
                <a-select-option value="2.4">
                   2.4
                </a-select-option>
                <a-select-option value="3">
                   3
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="铜厚" ref="copperThickness_" prop="copperThickness_">
          <a-select  style="width: 120px" v-model="form.copperThickness_"  :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="0.5oz">
                    0.5oz
                </a-select-option>
                <a-select-option value="0.75oz">
                    0.75oz
                </a-select-option>
                <a-select-option value="1oz">
                    1oz
                </a-select-option>
                <a-select-option value="1.5oz">
                    1.5oz
                </a-select-option>
                <a-select-option value="2oz">
                    2oz
                </a-select-option>
                <a-select-option value="3oz">
                    3oz
                </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item  label="TG值" ref="tG_" prop="tG_">
          <a-select  style="width: 120px" v-model="form.tG_"  :getPopupContainer="()=>this.$refs.SelectBox">
                <a-select-option value="">
                    请选择
                </a-select-option>
                <a-select-option value="TG130">
                    TG130
                </a-select-option>
                <a-select-option value="TG135">
                    TG135
                </a-select-option>
                <a-select-option value="TG140">
                   TG140
                </a-select-option>
                <a-select-option value="TG150">
                    TG150
                </a-select-option>
                <a-select-option value="TG170">
                    TG170
                </a-select-option>
            </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
        </a-modal>
    </div>
 
</template>

<script>
import { addBoard, updateBoard,progressNum } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
      compileApply:{
          type: String,
          default () {
              return ''
          }
      },
      message:{
          type: String,
          default () {
              return ''
          }
      }
  },
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {
            productType_: '',
            orderType_: '',
            machineType_: '',
            boardType_: '',
            boardthick_: '',
            copperThickness_: '',
            tG_: '',
      },
      rules: {
          productType_: [
          { required: true, message: "请选择产品类型", trigger: "blur" },
        ],
          orderType_: [
              { required: true, message: "请选择板材类型", trigger: "blur" },
          ],
          machineType_: [
              { required: true, message: "请选择品牌", trigger: "blur" },
          ],
          boardType_: [
              { required: true, message: "请选择型号", trigger: "blur" },
          ],
          boardthick_: [
              { required: true, message: "请选择板厚", trigger: "blur" },
          ],
          copperThickness_: [
              { required: true, message: "请选择铜厚", trigger: "blur" },
          ],
          tG_: [
              { required: true, message: "请选择TG值", trigger: "blur" },
          ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
    //    if(this.compileApply=='1'){
           this.visible = true;
           this.model = model
           if(model && model.id) {
               this.form = {
                   id:model.id,
                   productType_: model.productType_,
                   orderType_: model.orderType_,
                   machineType_: model.machineType_,
                   boardType_: model.boardType_,
                   boardthick_: model.boardthick_,
                   copperThickness_: model.copperThickness_,
                   tG_: model.tG_,
               };
           }else {
               //清空form操作过的数据
               this.form= {
                   productType_: '',
                   orderType_: '',
                   machineType_: '',
                   boardType_: '',
                   boardthick_: '',
                   copperThickness_: '',
                   tG_: '',
               }
           }
    //    }else {
    //        this.$message.info(this.message)
    //    }


    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
       this.form= {
            productType_: '',
                orderType_: '',
                machineType_: '',
                boardType_: '',
                boardthick_: '',
                copperThickness_: '',
                tG_: '',
        },
      this.$refs.ruleForm.resetFields()
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          let params = {
              id:this.model.id,
              pGuid_: this.suppId,
              ...this.form
          }
          if(this.model.id) {
            updateBoard(this.model.id,params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info("操作成功");
                    this.$emit("ok");
                    progressNum(this.suppId).then(res=>{
                        if(res.code!==1){
                            this.$message.info(res.message)
                        }
                    })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }else{
              addBoard(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.info("操作成功");
                    this.$emit("ok");
                    progressNum(this.suppId).then(res=>{
                        if(res.code!=='1'){
                            this.$message.info(res.message)
                        }
                    })
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
}

</style>
