<template>
    <div class="view">
        <div id="centent">
            <div class="form-item">
                <div class="cell line">
                    <span>金手指元素选择Profile范围：</span>
                    <select name="Profile" class="unit selctsty" style="width:calc(100% - 185px);-webkit-appearance: menulist;">
                        <option value="PCS">PCS</option>
                        <option value="SET">SET</option>
                    </select>
                </div>
                <div class="cell" style="width: calc(50% - 30px)">
                    <span class="cell-label" style="width: 40px;">金厚：</span>
                    <input type="text" name="ValueExt1" autocomplete="off" data-verify="number" data-unit="uinch" class="selctsty" style="width:calc(100% - 40px - 80px);margin-right: 5px" />
                    <select name="ValueExt1Unit" class="unit selctsty">
                        <option value="uinch">uinch</option>
                        <!-- <option value="um">um</option> -->
                    </select>
                </div>
                <div class="cell" style="width: calc(50% + 30px)">
                    <span class="cell-label" style="width: 100px;">镍厚：</span>
                    <input type="text" class="selctsty" name="ValueExt3" autocomplete="off" data-verify="number" data-unit="uinch" style="width:calc(100% - 100px - 80px);margin-right: 5px" />
                    <select name="ValueExt3Unit" class="unit selctsty">
                        <option value="uinch">uinch</option>
                        <!-- <option value="um">um</option> -->
                    </select>
                </div>
            </div>
            
            <table id="middleTable">
                <thead>
                    <tr>
                        <th></th>
                        <th>CS面</th>
                        <th>SS面</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td></td>
                        <td>
                            <span>已选择<i id="CSSelectCount" class="SelectCount" name="SelectCount" data-type="cs">0</i>个元素</span>
                            <button type="button" class="calcButton" data-type="cs" @click="CSClick('cs')">计算</button>
                        </td>
                        <td>
                            <span>已选择<i id="SSSelectCount" class="SelectCount" name="SelectCount" data-type="ss">0</i>个元素</span>
                            <button type="button" class="calcButton" data-type="ss" @click="CSClick('ss')">计算</button>
                        </td>
                    </tr>
                    <tr>
                        <td>长度*宽度</td>
                        <td>
                            <input type="text" name="Length" class="selctsty" data-type="cs" autocomplete="off" data-verify="number" data-unit="mm" style="width:calc((100% - 10px - 53px) / 2)">
                            <span class="symbol" style="display: contents">X</span>
                            <input type="text" name="Width" class="selctsty" data-type="cs" autocomplete="off" data-verify="number" data-unit="mm" style="width:calc((100% - 10px - 53px) / 2)">
                            <select name="LengthUnit" class="unit selctsty" data-type="cs">
                                <!-- <option value="mm">mm</option> -->
                                <option value="inch">inch</option>
                            </select>
                            <input type="hidden" name="WidthUnit" class="theUnit selctsty" value="mm" data-type="cs">
                        </td>
                        <td>
                            <input type="text" name="Length" class="selctsty" data-type="ss" autocomplete="off" data-verify="number" data-unit="mm" style="width:calc((100% - 10px - 53px) / 2)">
                            <span class="symbol" style="display: contents">X</span>
                            <input type="text" name="Width" class="selctsty" data-type="ss" autocomplete="off" data-verify="number" data-unit="mm" style="width:calc((100% - 10px - 53px) / 2)">
                            <select name="LengthUnit" class="unit selctsty">
                                <!-- <option value="mm">mm</option> -->
                                <option value="inch">inch</option>
                            </select>
                            <input type="hidden" name="WidthUnit" class="theUnit selctsty" value="mm" data-type="ss">
                        </td>
                    </tr>
                    <tr>
                        <td>金手指支数</td>
                        <td>
                            <input type="text" name="GoldenFingersCount" class="selctsty" autocomplete="off" data-verify="number" data-type="cs" />
                        </td>
                        <td>
                            <input type="text" name="GoldenFingersCount" data-type="ss" class="selctsty" autocomplete="off" data-verify="number" />
                        </td>
                    </tr>
                    <tr>
                        <td>面积</td>
                        <td>
                            <input type="text" name="GoldenFingersArea" class="selctsty" data-type="cs" autocomplete="off" data-verify="number" data-unit="cm2" style="width:calc(100% - 55px)" />
                            <select name="GoldenFingersAreaUnit" class="unit selctsty" data-type="cs">
                                <!-- <option value="cm2">cm2</option> -->
                                <option value="inch2">inch2</option>
                            </select>
                        </td>
                        <td>
                            <input type="text" name="GoldenFingersArea" data-type="ss" class="selctsty" autocomplete="off" data-verify="number" data-unit="cm2" style="width:calc(100% - 55px)" />
                            <select name="GoldenFingersAreaUnit" data-type="ss" class="unit selctsty">
                                <!-- <option value="cm2">cm2</option> -->
                                <option value="inch2">inch2</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>面积占比</td>
                        <td>
                            <input type="text" name="GoldenFingersAreaRatio" data-type="cs" class="selctsty" autocomplete="off" data-verify="number" style="width:calc(100% - 20px)">
                            <i style="width: 20px;font-size: 13px;color: #333;text-align: center;">%</i>
                        </td>
                        <td>
                            <input type="text" name="GoldenFingersAreaRatio" data-type="ss" class="selctsty" autocomplete="off" data-verify="number" style="width:calc(100% - 20px)">
                            <i style="width: 20px;font-size: 13px;color: #333;text-align: center;">%</i>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>
                            <button type="button" class="searchButton" data-type="cs" @click="searchButton('cs')">查看</button>
                        </td>
                        <td>
                            <button type="button" class="searchButton" data-type="ss" @click="searchButton('ss')">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div class="form-item">
                <div class="cell" style="width: 65%;white-space: nowrap;">
                    <span class="cell-label">最大长度*宽度：</span>
                    <span id="maxLength" data-unit="mm">0</span>
                    <span class="symbol" style="display: contents">X</span>
                    <span id="maxWidth" data-unit="mm">0</span>
                    <select class="unit selctsty" >
                        <!-- <option value="mm">mm</option> -->
                        <option value="inch">inch</option>
                    </select>
                </div>
                <div class="cell" style="width: 35%;white-space: nowrap;">
                    <span class="cell-label">双面总支数：</span>
                    <span id="totalGoldenFingersCount">0</span>
                </div>
                
                <div class="cell" style="width: 65%;white-space: nowrap;">
                    <span class="cell-label">面积总和：</span>
                    <span id="totalGoldenFingersArea" data-unit="cm2">0</span>
                    <select class="unit selctsty">
                        <!-- <option value="cm2">cm2</option> -->
                        <option value="inch2">inch2</option>
                    </select>
                </div>
                <div class="cell" style="width: 35%;white-space: nowrap;">
                    <span class="cell-label">面积总占比：</span>
                    <span id="totalGoldenFingersAreaRatio">0%</span>
                </div>
            </div>
            
        </div>
        <div id="foot">
            <button type="button" class="affirm" @click="submitData()">确认</button>
            <button type="button" onclick="window.external.CallCancel()">取消</button>
        </div>
    </div> 
</template>
<script>
    import $ from 'jquery'    
    export default{
    name:'openGoldfinger',
    inject:['reload'],
    data(){
        return{   
            CSLength:'',
            CSWidth:''      
        }
    }, 
    async created(){   
    },
    async mounted(){        
        window.vue = this 
        this.GetGoldenFingersPara()
        this.getData()
    },
    methods:{
        GetGoldenFingersPara(){
       let cs =  window.external.GetGoldenFingersPara('cs')
       $("#CSSelectCount").text(JSON.parse(cs).SelectCount);
       let ss =  window.external.GetGoldenFingersPara('ss')
       $("#SSSelectCount").text(JSON.parse(ss).SelectCount);
    //    alert(ss)
        },     
        getData(){
            //初始已选择的元素
				if(window.external && typeof window.external.GetGoldenFingersPara != 'undefined' && typeof window.external.GetGoldenFingersPara != undefined){
					$("#CSSelectCount").text(JSON.parse(window.external.GetGoldenFingersPara('cs')).SelectCount);
					$("#SSSelectCount").text(JSON.parse(window.external.GetGoldenFingersPara('ss')).SelectCount);
				}
				var timer = null;
				$("#centent li[data-type]").on("input","input[type='text'][data-verify]",function(){
					if($(this).attr("data-verify").indexOf('integer') > -1){ //只能输入正整数
						this.value = this.value.replace(/\D/g,'');
					}else if($(this).attr("data-verify").indexOf('number') > -1){ //只能输入数字
						this.value = this.value.replace(/[^\d.]/g,""); //只能数字或.
						this.value = this.value.replace(".","$#$").replace(/\./g,"").replace("$#$","."); //.只能出现一次
					}
					
					if(timer){clearTimeout(timer)}
					timer = setTimeout(function(){
						columnCount();
					},300)
				})
				
				
				$("#centent select.unit").change(function(){
					var newUnit = this.value;
					var formInfo = $(this).parents(".cell:first");
					if(formInfo.length > 0){
						var unitName = formInfo.find("[data-unit]");
						if(unitName.length > 0){
							var nowUnit = unitName.eq(0).attr("data-unit");
							if(nowUnit && nowUnit !== newUnit){
								unitName.each(function(index,elem){
									if(elem.tagName == 'INPUT' || elem.tagName == 'SELECT'){
										elem.value = unitConversion[nowUnit + 'Conversion' + newUnit](elem.value);
									}else{
										elem.innerText = unitConversion[nowUnit + 'Conversion' + newUnit](elem.innerText);
									}
									$(elem).attr("data-unit",newUnit);
								})
							}
						}
					}
					
					$(this).siblings("input[type='hidden'].theUnit").val(this.value); //特殊处理一个单位控制两个Input
				})
				
				
				//点击计算按钮
				// $("#centent .calcButton").click(function(){
                //     alert('点击')
                //     let s = $(this).parents('li:first').attr('data-type')
                //     console.log($(this).parents('li:first'))
                    
				// 	if(window.external && typeof window.external.CalcButton != 'undefined' && typeof window.external.CalcButton != undefined){
                //         let b =  $("#centent select[name='Profile']").val()
                //         alert(s)
                //         alert(b)
				// 		window.external.CalcButton(JSON.stringify({
				// 			LayerName: $(this).parents('li:first').attr('data-type'),                            
				// 			Profile: $("#centent select[name='Profile']").val().toLowerCase(),
				// 		}))
				// 	}
				// })
				
				//点击查看按钮
				// $("#centent .searchButton").click(function(){
                //     console.log('查看', $(this).parents('li:first').attr('data-type'))
				// 	if(window.external && typeof window.external.SearchButton != 'undefined' && typeof window.external.SearchButton != undefined){
				// 		window.external.SearchButton(JSON.stringify({
				// 			LayerName: $(this).parents('li:first').attr('data-type')
				// 		}))
				// 	}
				// })
        },
        //点击查看按钮
        searchButton(type){
            // alert(type)            
            if(window.external && typeof window.external.SearchButton != 'undefined' && typeof window.external.SearchButton != undefined){
                window.external.SearchButton(JSON.stringify({
                    LayerName: type
                }))
            }            
        },
        //点击计算按钮
        CSClick(type){
            // alert(type)
            // let data = {"LayerName":"cs","Length": {"Value": 3.33502,"Unit": "mm"},"Width": {"Value": 17.2001,"Unit": "mm"},
            // "GoldenFingersCount":26,"GoldenFingersArea": {"Value": 0.306908,"Unit": "cm2"},"GoldenFingersAreaRatio":1.36219}
            // this.SetGoldenFingersPara(data)
            if(window.external && typeof window.external.CalcButton != 'undefined' && typeof window.external.CalcButton != undefined){                        
              let obj =   window.external.CalcButton(JSON.stringify({
                    LayerName: type,                            
                    Profile: $("#centent select[name='Profile']").val(),
                }))
                // alert(obj)
                // alert(obj)
                // alert(JSON.parse(obj).Length.Value)
            } 
        },
        //C++调用
        SetGoldenFingersPara(data){
            // alert(data) 
            data = JSON.parse(data);
            // console.log('data',data)
            if(data.LayerName){
                for(var j in data){ 
                    var elem =  $("input[data-type='"+data.LayerName+"'][name='"+j+"']"); 
                    console.log('elem',elem)  
                    if(typeof data[j] == 'object'){
                        var value = data[j]['Value'];
                        var unit = data[j]['Unit'];
                        var nameUnit = elem.attr("data-unit"); //当前页面的单位
                        elem[0].value = value;
                        // if(nameUnit && nameUnit != unit){
                        //     elem[0].value = unitConversion[unit + 'Conversion' + nameUnit](value);
                        // }else{
                        //     elem[0].value = value;
                        // }
                    }else{
                        if(j == 'SelectCount'){
                            var elem1 =  $("[data-type='"+data.LayerName+"'][name='"+j+"']");                            
                            elem1.text(data[j]);
                        }else{            
                            elem.val(data[j]);                           
                        }          
                    }
                }
                
                this.columnCount();
            }
        },       
        columnCount(){                       
            var maxLength = 0;
            var maxWidth = 0;
            var totalGoldenFingersCount = 0;
            var totalGoldenFingersArea = 0;
            var totalGoldenFingersAreaRatio = 0;           
            var centent = $("#centent");           
            // $("#centent [data-type]").each(function(){                
                var CSLength = $("[data-type='cs']input[name='Length']");
                var CSWidth = $("[data-type='cs']input[name='Width']");
                var SSLength = $("[data-type='ss']input[name='Length']");
                var SSWidth = $("[data-type='ss']input[name='Width']");
                if(Number(CSLength.val()) >= Number(SSLength.val()) || Number(CSWidth.val()) >= Number(SSWidth.val())){
                    maxLength = Number(CSLength.val()).toFixed(3);
                    maxWidth = Number(CSWidth.val()).toFixed(3);
                }else{
                    maxLength = Number(SSLength.val()).toFixed(3);
                    maxWidth = Number(SSWidth.val()).toFixed(3);
                }
                var CSGoldenFingersCount = Number(centent.find("[data-type='cs'][name='GoldenFingersCount']").val()) || 0
                var SSGoldenFingersCount = Number(centent.find("[data-type='ss'][name='GoldenFingersCount']").val())  || 0               
                totalGoldenFingersCount = (CSGoldenFingersCount + SSGoldenFingersCount).toFixed(3);
                var CSGoldenFingersAreaRatio = Number(centent.find("[data-type='cs'][name='GoldenFingersAreaRatio']").val()) || 0
                var SSGoldenFingersAreaRatio = Number(centent.find("[data-type='ss'][name='GoldenFingersAreaRatio']").val())  || 0                
                totalGoldenFingersAreaRatio = (CSGoldenFingersAreaRatio + SSGoldenFingersAreaRatio).toFixed(3);
                var CSGoldenFingersArea = Number(centent.find("[data-type='cs'][name='GoldenFingersArea']").val()) || 0
                var SSGoldenFingersArea = Number(centent.find("[data-type='ss'][name='GoldenFingersArea']").val())  || 0
                totalGoldenFingersArea = (CSGoldenFingersArea + SSGoldenFingersArea).toFixed(3)
                
            $('#maxLength').text(maxLength)
            $("#maxWidth").text(maxWidth);            
            $("#totalGoldenFingersCount").text(totalGoldenFingersCount);
            $("#totalGoldenFingersArea").text(totalGoldenFingersArea);
            $("#totalGoldenFingersAreaRatio").text(totalGoldenFingersAreaRatio + '%');
        },        
        submitData(){
            var centent = $("#centent");            
            var formData = {
                ValueExt1: centent.find("input[name='ValueExt1']").val(),
                ValueExt1Unit: centent.find("select[name='ValueExt1Unit']").val(),                
                ValueExt3: centent.find("input[name='ValueExt3']").val(),
                ValueExt3Unit: centent.find("select[name='ValueExt3Unit']").val(),                
                CSLength: centent.find("input[data-type='cs'][name='Length']").val(),
                CSLengthUnit: centent.find("input[data-type='cs'][name='LengthUnit']").val(),
                CSWidth: centent.find("[data-type='cs'][name='Width']").val(),
                CSWidthUnit: centent.find("[data-type='cs'][name='WidthUnit']").val(),
                CSGoldenFingersArea: centent.find("[data-type='cs'][name='GoldenFingersArea']").val(),
                CSGoldenFingersCount: centent.find("[data-type='cs'][name='GoldenFingersCount']").val(),
                CSGoldenFingersAreaRatio: centent.find("[data-type='cs'][name='GoldenFingersAreaRatio']").val(),                
                SSLength: centent.find("[data-type='ss'][name='Length']").val(),
                SSLengthUnit: centent.find("[data-type='ss'][name='LengthUnit']").val(),
                SSWidth: centent.find("[data-type='ss'][name='Width']").val(),
                SSWidthUnit: centent.find("[data-type='ss'][name='WidthUnit']").val(),
                SSGoldenFingersCount: centent.find("[data-type='ss'][name='GoldenFingersCount']").val(),
                SSGoldenFingersArea: centent.find("[data-type='ss'][name='GoldenFingersArea']").val(),
                SSGoldenFingersAreaRatio: centent.find("[data-type='ss'][name='GoldenFingersAreaRatio']").val()
            }
            
            formData.ValueExt2 = (Number(formData.CSGoldenFingersCount) || 0) + (Number(formData.SSGoldenFingersCount) || 0);
            // console.log('formData',formData)
            window.external.CallOK('vue.goldFingerCalculateCallBack',JSON.stringify(formData));
        },
    }
}
</script>
<style lang="less" scoped>

 .view {
    padding:10px 15px;
    width: 100%;
    height: 100%;
    background-color: #F0F0F0;
    font: 13px Helvetica Neue,Helvetica,PingFang SC,Tahoma,Microsoft YaHei,Arial,sans-serif;
}
#centent .form-item .cell .cell-label {
    width: 100px;
    text-align: right;
    display: inline-block;
    box-sizing: border-box;
}
#centent {
    width: 100%;
    min-width: 450px;
    height: calc(100% - 50px);
    padding: 0 15px 0 0;
    display: inline-block;
    overflow: auto;
    box-sizing: border-box;
}
#centent .form-item .cell.line {
    width: 100%;
}
#centent .form-item .cell span {
    font-size: 13px;
    color: #000;
}
#centent .form-item .cell{
    display: inline-block;
    margin: 5px 0
}
.selctsty{
    font-size: 13px;
    // padding: 3px 0 4px 5px;
    border:1px solid  #ccc;
}
#centent select {
    width: 50px;
    font-size: 13px;
    color: #000;
    border: 1px solid #ccc;
    padding: 3px 0 4px 5px;
    outline: none;
    appearance: none;
    margin:0 0 0 5px;
    -webkit-appearance: none;
}
#centent table td select {
    width: 50px;
    font-size: 13px;
    color: #000;
    border: 1px solid #ccc;
    padding: 3px 0 4px 5px;
    outline: none;
    appearance: none;
    -webkit-appearance: none;
}
#centent table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin: 15px 0;
}
#centent table th {
    font-size: 13px;
    color: #000;
    padding: 8px 10px;
    text-align: center;
    background-color: #e6e6e6;
    border: 1px solid #c0c0c0;
}
#centent table tr td:nth-child(1) {
    width: 90px;
    background-color: #e6e6e6;
    font-size: 13px;
    font-weight: bold;
}
#centent table td {
    font-size: 0;
    color: #000;
    padding: 8px 10px;
    background-color: #fff;
    border: 1px solid #c0c0c0;
    font-weight: normal;
    text-align: center;
}
#centent table td .symbol {
    width: 20px;
    font-size: 12px;
    text-align: center;
}
#centent table td input {
    width: 100%;
    font-size: 13px;
    color: #000;
    border: 1px solid #ccc;
    padding: 3px 0 4px 5px;
    box-sizing: border-box;
    outline: none;
}
#centent .form-item  input{
    width: 100%;
    font-size: 13px;
    color: #000;
    border: 1px solid #ccc;
    padding: 3px 0 4px 5px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    outline: none;
}
#centent table td select.unit {
    margin: 0 0 0 5px;
}
#centent table td button.calcButton {
    padding: 2px 10px;
    margin: 0 0 0 10px;
}
#centent table td span {
    font-size: 13px;
    color: #000;
    display: inline-block;
    vertical-align: middle;
}
#centent table td i {
    font-style: normal;
    margin-left:8px;
}
#centent table td button {
    font-size: 12px;
    color: #000;
    padding: 2px 15px;
    display: inline-block;
    vertical-align: middle;
}

// /deep/#centent table button {
//     font-size: 12px;
//     color: #000;
//     padding: 3px 15px;
//     border: 1px solid #ABADB3;
//     border-radius: 2px;
//     background-color: #fff;
//     cursor: pointer;
// }
#foot {
    width: 100%;
    height: 50px;
    padding: 10px 30px;
    position: fixed;
    bottom: 5px;
    left: 0;
    right: 0;
    text-align: right;
    box-sizing: border-box;
    border-top: 1px solid #d2d2d2;
}
#foot button.affirm {
    background-color: #0092ff;
    border-color: #0092ff;
    color: #fff;
}
#foot button {
    font-size: 12px;
    color: #333333;
    padding: 5px 15px;
    background-color: #fff;
    border: 1px solid #ABADB3;
    margin: 0 0 0 15px;
    cursor: pointer;
    border-radius: 2px;
}
#foot button {
    font-size: 12px;
    color: #333333;
    padding: 5px 15px;
    background-color: #fff;
    border: 1px solid #ABADB3;
    margin: 0 0 0 15px;
    cursor: pointer;
    border-radius: 2px;
}
</style>