<!-- 工程管理 - 锣带分派  -人员列表  -->
<template>
<a-table
    style="word-break: break-all"
    :columns="columns"
    :dataSource="dataSource"
    :scroll="{y:730}"
    :pagination="false"
    :rowKey="rowKey"
    :customRow="onClickRow"
    :rowClassName="isRedRow"
    :loading="orderDetailTableLoading">
    <template slot="customRender" slot-scope="text,record" >
      <template >
        {{ text }}
      </template>
      <a-tag color="orange" v-if="record.isLeave_" class="peopleTag">
        休
      </a-tag>
    </template>
    <template slot="action" slot-scope="text,record" >
      <a-tooltip title="取单设置" v-if="checkPermission('MES.EngineeringModule.FormingDispatch.FormingDispatchPersonOrderInfo')">
        <a-icon type="edit" style="color: #ff9900; font-size: 18px;" @click.stop="OrderRetrievalSettingsClick(record)"/>
      </a-tooltip>
    </template>
</a-table>
</template>

<script>
import {checkPermission} from "@/utils/abp";
export default {
  name: "OrderDetail",
  props: {
    columns:{
      type: Array,
      require: true
    },
    dataSource:{
      type: Array,
      require: true
    },
    orderDetailTableLoading:{
      type: Boolean,
      require: true
    },
    rowKey:{
      type: String,
      require: true
    }
  },
  data(){
    return {
      userNo:'',
      user_:''
    }
  },
  methods:{
    checkPermission,
    onClickRow(record) {
      return {
        on: {
          click: () => {
            if (record.userNo && this.userNo != record.userNo){
              this.$emit('getPeopleOrderList', record.userNo,record.realName)
            }
            // this.$emit('saveErpKey', record)
            this.userNo = record.userNo
          }
        }
      }
    },
    isRedRow (record) {
      // console.log('record',record)
      let strGroup = []
      let str =[]
      if (record.userNo && record.userNo == this.userNo) {
        strGroup.push('rowBackgroundColor')
      }
      if(record.isLeave_ == true){ // 是否请假
        str.push('rowSty')
      }

      // if(str.length > 1){
      //   str = [str[0]]
      // }
      // console.log('str.concat(strGroup):',str.concat(strGroup))
      return str.concat(strGroup)

    },
    OrderRetrievalSettingsClick(record){
      this.$emit('OrderRetrievalSettingsClick',record)
    }
  },
}
</script>

<style scoped lang="less">
/deep/ .ant-table {
  .ant-table-tbody{
    tr{
      td{
        padding:3px 0!important;
      }
    }
  }
  .rowSty {
    td {
      color: #DC143C;
    }
  }
  .peopleTag {
    position: absolute;
    font-size: 12px;
    font-weight: 600;
    left: 0;
    padding: 0 2px;
  }
  .rowSty1{
    td{
      color: #2828FF;
    }
  }
  .rowBackgroundColor {
      background: #aba5a5;
    }
}


</style>
