<!-- 车间管理-磨板管理-按钮 -->
<template>
  <div class="active" ref="active" :style="[{ height: height + 'px' }]">
    <div class="box" v-if="checkPermission('MES.ProductionModule.GrindingPlate.GrindPlateStart')">
      <a-button type="primary" @click="handleDispatchMachine"> 磨板开始 </a-button>
    </div>
    <div class="box" v-if="checkPermission('MES.ProductionModule.GrindingPlate.GrindPlateEnd')">
      <a-button type="primary" @click="OverOrderClick"> 磨板完成 </a-button>
    </div>
    <div class="box">
      <a-button type="primary" @click="queryClick"> 查询 </a-button>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "Action",
  data() {
    return {
      height: 50,
    };
  },

  methods: {
    checkPermission,
    // toggleAdvanced () {
    //   this.advanced = !this.advanced;
    //   let height_ = 0
    //   if (this.advanced) {
    //     height_ = 100
    //   } else {
    //     height_= 50
    //   }
    //   this.$refs.active.style.height = height_ + 'px';
    // },
    // 磨板开始
    handleDispatchMachine() {
      this.$emit("handleDispatchMachine");
    },
    // 磨板完成
    OverOrderClick() {
      this.$emit("OverOrderClick");
    },
    // 查询
    queryClick() {
      this.$emit("queryClick");
    },
  },
  // created(){
  //   this.$nextTick(()=>{
  //     if (this.$refs.active.children.length > 7 ) {
  //       this.height = 104
  //       this.collapsed = true
  //     } else {
  //       this.height = 52
  //     }
  //   })
  // }
};
</script>

<style scoped lang="less">
.active {
  height: 50px;
  line-height: 40px;
  float: right;
  width: 45%;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 106px;
    margin-top: 4px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
