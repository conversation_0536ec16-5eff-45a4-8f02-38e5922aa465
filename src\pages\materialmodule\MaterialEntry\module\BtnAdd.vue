<!-- 物料管理-物料录入-新增物料 -->
<template>
  <div ref="SelectBox">
    <a-form-model :modal="form1" class="materclass">
      <!-- 物料类别行 -->
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="物料类别：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" class="required">
            <a-select
              v-model="form1.materType_"
              :autoFocus="autoFocus"
              v-focus-next-on-enter="'input2'"
              ref="input1"
              :disabled="editnew"
              showSearch
              allowClear
              :filter-option="filterOption"
              :getPopupContainer="() => this.$refs.SelectBox"
              @change="changematerType"
            >
              <a-select-option v-for="(item, index) in materialtype" :key="index" :value="item.id">{{ item.caption_ }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="板材类型："
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <a-select
              v-model="form1.coreTypeCodes_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              showSearch
              option-filter-prop="children"
              :filter-option="filterOption"
              allowClear
              @change="changematerial()"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in platetypenew" :key="index" :value="item.id">{{ item.coreType_ }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="内外铜箔："
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 909"
            class="required"
          >
            <a-select
              v-model="form1.innerOrOuter"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              :getPopupContainer="() => this.$refs.SelectBox"
              showSearch
              allowClear
              :filter-option="filterOption"
              @change="changematerial()"
            >
              <a-select-option value="910"> 外层 </a-select-option>
              <a-select-option value="911"> 内层 </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="PP类别"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            class="required"
          >
            <a-select
              v-model="form1.ppTypeCodes_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              showSearch
              allowClear
              :filter-option="filterOption"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in pptype" :key="index" :value="item.valueMember">{{ item.text }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 物料规格行 -->
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="物料规格：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-textarea
              v-model="form1.materSpec_"
              auto-size
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="板材厚度"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <a-input
              v-model="form1.core_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="铜箔名称"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 909"
            class="required"
          >
            <a-input
              v-model="form1.cU_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="PP类型"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            class="required"
          >
            <a-select
              v-model="form1.pP_"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              showSearch
              option-filter-prop="children"
              :filter-option="filterOption"
              @search="handleSearch"
              @blur="handleBlur"
              @change="handleChange"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in ppcategory" :key="index" :value="item.pP_">{{ item.pP_ }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 物料名称行 -->
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="物料名称：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }">
            <a-input
              v-model="form1.materName_"
              placeholder="铜箔/板材/半固化片"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否含铜" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 908">
            <a-checkbox
              v-model="form1.tD_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="基础铜箔厚度"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 909"
            class="required"
          >
            <a-input
              v-model="form1.cuThicknessOrg_"
              placeholder="mil"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="含胶量"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            class="required"
          >
            <a-input
              v-model="form1.rC_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
              @change="changematerial()"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 供应商行 -->
      <a-row>
        <a-col :span="12"
          ><a-form-model-item label="供应商：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" class="required">
            <a-select
              v-model="form1.materVendor_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              showSearch
              option-filter-prop="children"
              :filter-option="filterOption"
              allowClear
              allow-create
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in materialCategory2" :key="index" :value="item.id">{{ item.verdorName_ }}</a-select-option>
            </a-select>
          </a-form-model-item></a-col
        >
        <a-col :span="12">
          <a-form-model-item
            label="顶铜厚度"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <!-- <a-input v-model="form1.topOZ_"  v-focus-next-on-enter="'input3'" ref="input2" allowClear/> -->
            <a-select
              v-model="form1.topOZ_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              showSearch
              option-filter-prop="children"
              :filter-option="filterOption"
              allowClear
              @change="topozchange"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in topoz" :key="index" :value="item">{{ item }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12"
          ><a-form-model-item
            label="TG值"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            class="required"
          >
            <a-select
              v-model="form1.tG_"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in pptgvalue" :key="index" :value="item.tG_">{{ item.tG_ }}</a-select-option>
            </a-select>
          </a-form-model-item></a-col
        >
        <a-col :span="12">
          <a-form-model-item
            label="完成铜箔厚度"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 909"
            class="required"
          >
            <a-input
              v-model="form1.cuThickness_"
              placeholder="mil"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 进货单位行 -->
      <a-row>
        <a-col :span="12"
          ><a-form-model-item label="进货单位：" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" class="required">
            <a-select
              v-model="form1.units_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              allowClear
              v-focus-next-on-enter="'input3'"
              ref="input2"
              showSearch
              option-filter-prop="children"
              :filter-option="filterOption"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in materialCategory1" :key="index" :value="item.id">{{ item.caption_ }}</a-select-option>
            </a-select>
          </a-form-model-item></a-col
        >

        <a-col :span="12"
          ><a-form-model-item
            label="底铜厚度"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <!-- <a-input v-model="form1.bomOZ_"  v-focus-next-on-enter="'input3'" ref="input2" allowClear/> -->
            <a-select
              v-model="form1.bomOZ_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              showSearch
              option-filter-prop="children"
              :filter-option="filterOption"
              allowClear
              @change="changematerial()"
              :getPopupContainer="() => this.$refs.SelectBox"
            >
              <a-select-option v-for="(item, index) in bomoz" :key="index" :value="item">{{ item }}</a-select-option>
            </a-select>
          </a-form-model-item></a-col
        >
        <a-col :span="12"
          ><a-form-model-item
            label="PP厚度"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            class="required"
          >
            <a-input
              v-model="form1.thicknesS_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            /> </a-form-model-item
        ></a-col>
        <a-col :span="12"
          ><a-form-model-item
            label="工厂"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 909"
            class="required"
          >
            <a-select
              v-model="form1.joinFactoryId"
              showSearch
              option-filter-prop="children"
              :filter-option="filterOption"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              :getPopupContainer="() => this.$refs.SelectBox"
              allowClear
            >
              <a-select-option
                style="color: blue"
                v-for="(item, index) in mapKey(factoryname)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </a-form-model-item></a-col
        >
      </a-row>
      <!-- 其他判断信息行 -->
      <a-row>
        <a-col :span="12"></a-col>
        <!-- <a-col :span="12"><a-form-model-item label="ERP编码" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}"  >
                            <a-input v-model="form1.erpKey_"  v-focus-next-on-enter="'input3'" ref="input2" allowClear/>
                          </a-form-model-item> </a-col> -->
        <a-col :span="12">
          <a-form-model-item
            label="外层DK"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <a-input
              v-model="form1.dkOuter_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
              @change="dkchange"
            />
          </a-form-model-item>
          <a-form-model-item label="正公差" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 914">
            <a-input
              v-model="form1.thicknesS_TOL_UP_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="排序" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 909">
            <a-input
              v-model="form1.cuDisplay_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12"></a-col>
        <a-col :span="12">
          <a-form-model-item label="负公差" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 914">
            <a-input
              v-model="form1.thicknesS_TOL_DN_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item
            label="T值"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 909"
            class="required"
          >
            <a-input
              v-model="form1.t1Value_"
              placeholder="mil"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12"></a-col>
        <a-col :span="12">
          <a-form-model-item
            label="内层DK"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <a-input
              v-model="form1.dkInner_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="C1" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 909">
            <a-input
              v-model="form1.c1_"
              placeholder="mil"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <!-- <a-form-model-item label="TG类型" :labelCol="{span: 7}" :wrapperCol="{span: 12, offset: 1}"  v-if="form1.materType_ == 908">            
            <a-select v-model="form1.tgType_"  
                v-focus-next-on-enter="'input3'" ref="input2" 
                showSearch option-filter-prop="children"
                allowClear
                :filter-option="filterOption"
                :getPopupContainer="()=>this.$refs.SelectBox">               
              <a-select-option v-for="(item,index) in tgtype" :key="index" :value="item.tgValue_">{{item.tgValue_}}</a-select-option>
           
            </a-select>
          </a-form-model-item> -->
          <a-form-model-item
            label="内DK"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            class="required"
          >
            <a-input
              v-model="form1.dkInner_"
              @change="dkchange1"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12"> </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="大板长"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <a-input
              v-model="form1.pnlSizeX_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item
            label="外DK"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            class="required"
          >
            <a-input
              v-model="form1.dkOuter_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="C2" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 909">
            <a-input
              v-model="form1.c2_"
              placeholder="mil"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12"></a-col>
        <a-col :span="12">
          <a-form-model-item
            label="大板宽"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 908"
            class="required"
          >
            <a-input
              v-model="form1.pnlSizeY_"
              @change="changematerial()"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item
            label="玻布厚度"
            :labelCol="{ span: 7 }"
            :wrapperCol="{ span: 12, offset: 1 }"
            v-if="form1.materType_ == 914"
            :class="user.factoryId == 12 ? 'required' : ''"
          >
            <a-input
              v-model="form1.ppThickness4NY_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>

          <a-form-model-item label="C3" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 909">
            <a-input
              v-model="form1.c3_"
              placeholder="mil"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 是否CAF复选框行 -->
      <a-row>
        <a-col :span="12"></a-col>
        <a-col :span="12">
          <a-form-model-item label="排序" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 914">
            <a-input
              v-model="form1.rcIndex_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-form-model-item label="CEr" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 909">
            <a-input
              v-model="form1.cEr_"
              placeholder="mil"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-col :span="12"
            ><a-form-model-item label="是否CAF" :labelCol="{ span: 14 }" :wrapperCol="{ span: 5, offset: 1 }" v-if="form1.materType_ == 908">
              <a-checkbox
                v-model="form1.caF_"
                :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
                v-focus-next-on-enter="'input3'"
                ref="input2"
              /> </a-form-model-item
          ></a-col>
          <a-col :span="12"
            ><a-form-model-item label="是否CTI" :labelCol="{ span: 12 }" :wrapperCol="{ span: 5, offset: 1 }" v-if="form1.materType_ == 908">
              <a-checkbox
                v-model="form1.ctI_"
                :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
                v-focus-next-on-enter="'input3'"
                ref="input2"
              /> </a-form-model-item
          ></a-col>
        </a-col>
      </a-row>
      <!-- 是否水印复选框行 -->
      <a-row>
        <a-col :span="12"> </a-col>
        <a-col :span="12">
          <a-form-model-item label="线宽差值" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 909">
            <a-input
              v-model="form1.lineWidthDiff_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
              allowClear
            />
          </a-form-model-item>
          <a-col :span="12">
            <a-form-model-item label="是否水印" :labelCol="{ span: 14 }" :wrapperCol="{ span: 5, offset: 1 }" v-if="form1.materType_ == 908">
              <a-checkbox
                v-model="form1.waterMark_"
                :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
                v-focus-next-on-enter="'input3'"
                ref="input2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="电表板" :labelCol="{ span: 12 }" :wrapperCol="{ span: 5, offset: 1 }" v-if="form1.materType_ == 908">
              <a-checkbox
                v-model="form1.meterBoard_"
                :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
                v-focus-next-on-enter="'input3'"
                ref="input2"
              />
            </a-form-model-item>
          </a-col>
          <a-form-model-item label="无卤素" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12, offset: 1 }" v-if="form1.materType_ == 914">
            <a-checkbox
              v-model="form1.hF_"
              :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
              v-focus-next-on-enter="'input3'"
              ref="input2"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- PTFE复选框行 -->
      <a-row>
        <a-col :span="12"></a-col>
        <a-col :span="12">
          <a-col :span="12">
            <a-form-model-item label="PTFE" :labelCol="{ span: 14 }" :wrapperCol="{ span: 5, offset: 1 }" v-if="form1.materType_ == 908">
              <a-checkbox
                v-model="form1.ptfE_"
                :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
                v-focus-next-on-enter="'input3'"
                ref="input2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="无卤素" :labelCol="{ span: 12 }" :wrapperCol="{ span: 5, offset: 1 }" v-if="form1.materType_ == 908">
              <a-checkbox
                v-model="form1.halogen_"
                :disabled="editnew && (showaction > 2 || wuliaodata.length > 2 || puraction > 2 || purchasedata.length > 2)"
                v-focus-next-on-enter="'input3'"
                ref="input2"
                allowClear
              />
            </a-form-model-item>
          </a-col>
        </a-col>
      </a-row>
      <div style="width: 50%; height: 164px" class="costyle1" v-if="form1.materType_">
        <a-table
          :columns="columns1"
          :dataSource="wuliaodata"
          bordered
          :pagination="false"
          :scroll="{ y: 124 }"
          class="erpkey"
          :rowClassName="isRedRow"
          rowKey="ind"
        >
          <span slot="num" slot-scope="text, record, index">
            {{ index + 1 }}
          </span>
          <span slot="factory" slot-scope="text, record, index">
            <a-select
              showSearch
              allowClear
              optionFilterProp="lable"
              :disabled="editnew && index < showaction - 1"
              placeholder="工厂"
              @blur="facblur(index, record.faid, record)"
              v-model="record.faid"
              @change="fachange(index, record.faid, record)"
            >
              <a-select-option
                style="color: blue"
                v-for="(item, index) in mapKey(factoryname)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </span>
          <span slot="erpkey" slot-scope="text, record, index">
            <a-input
              v-model="record.erpkey"
              :disabled="(keys.includes(record.faid) || index == wuliaodata.length - 1 || !editnew) && record.faid ? false : true"
            ></a-input>
          </span>
          <span slot="action" slot-scope="text, record, index">
            <a-icon
              v-if="(editnew && index > showaction - 2) || !editnew"
              style="margin-left: 5px"
              type="close-circle"
              @click="closedata(index, record)"
            ></a-icon>
          </span>
        </a-table>
      </div>
      <div style="width: 50%; height: 3px" class="purcostyle" v-if="form1.materType_ == 908"></div>
      <div style="width: 50%; height: 164px" v-if="form1.materType_" :class="form1.materType_ == 908 ? 'costyle1' : 'costyle2'">
        <a-table
          :columns="columns2"
          :dataSource="purchasedata"
          bordered
          :pagination="false"
          :rowClassName="purisRedRow"
          :scroll="{ y: 124 }"
          class="erpkey"
          rowKey="ind"
        >
          <span slot="num" slot-scope="text, record, index">
            {{ index + 1 }}
          </span>
          <span slot="factory" slot-scope="text, record, index">
            <a-select
              showSearch
              allowClear
              optionFilterProp="lable"
              :disabled="editnew && index < puraction - 1"
              placeholder="工厂"
              @blur="purfacblur(index, record.faid, record)"
              v-model="record.faid"
              @change="purfachange(index, record.faid, record)"
            >
              <a-select-option
                style="color: blue"
                v-for="(item, index) in mapKey(factoryname)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </span>
          <span slot="price" slot-scope="text, record, index">
            <a-input
              v-model="record.price"
              :disabled="(keys.includes(record.faid) || index == purchasedata.length - 1 || !editnew) && record.faid ? false : true"
            ></a-input>
          </span>
          <span slot="minpur" slot-scope="text, record, index">
            <a-input
              v-model="record.minpur"
              :disabled="(keys.includes(record.faid) || index == purchasedata.length - 1 || !editnew) && record.faid ? false : true"
            ></a-input>
          </span>
          <span slot="action" slot-scope="text, record, index">
            <a-icon
              v-if="(editnew && index > puraction - 2) || !editnew"
              style="margin-left: 5px"
              type="close-circle"
              @click="purclosedata(index, record)"
            ></a-icon>
          </span>
        </a-table>
      </div>
    </a-form-model>
  </div>
</template>
<script>
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 20,
  },
  {
    title: "工厂",
    align: "left",
    dataIndex: "factory",
    scopedSlots: { customRender: "factory" },
    width: 45,
  },
  {
    title: "编码",
    align: "left",
    scopedSlots: { customRender: "erpkey" },
    width: 75,
  },
  {
    title: "操作",
    align: "center",
    scopedSlots: { customRender: "action" },
    width: 18,
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 20,
  },
  {
    title: "工厂",
    align: "left",
    dataIndex: "factory",
    scopedSlots: { customRender: "factory" },
    width: 45,
  },
  {
    title: "平米价",
    align: "left",
    scopedSlots: { customRender: "price" },
    width: 50,
  },
  {
    title: "最低采购价",
    align: "left",
    scopedSlots: { customRender: "minpur" },
    width: 50,
  },
  {
    title: "操作",
    align: "center",
    scopedSlots: { customRender: "action" },
    width: 20,
  },
];
export default {
  name: "EditAdd",
  props: [
    "materialCategory",
    "topoz",
    "bomoz",
    "materialCategory1",
    "materialCategory2",
    "platetypenew",
    "selectOption",
    "selectedRowsData",
    "editnew",
    "materialtype",
    "pptype",
    "tgtype",
    "ppcategory",
    "pptgvalue",
    "factoryname",
    "factory",
    "ide",
    "user",
  ],
  created() {
    if (this.editnew) {
      this.$nextTick(function () {
        this.data1 = JSON.parse(localStorage.getItem("maindata"));
        this.form1 = this.data1[this.ide];
        if (this.form1.materType_ == 914) {
          this.form1.dkInner_ = this.data1[this.ide].ppDKInner_;
          this.form1.dkOuter_ = this.data1[this.ide].ppDKOuter_;
        }
        this.form1.ppTypeCodes_ = this.data1[this.ide].ppTypeCodes_ ? this.data1[this.ide].ppTypeCodes_.toString() : "";
        this.form1.coreTypeCodes_ = this.data1[this.ide].coreTypeCodes_ ? Number(this.data1[this.ide].coreTypeCodes_) : "";
        //ERP编码数据获取
        if (this.form1.lsERPKey_ && this.form1.lsERPKey_[0].includes("F")) {
          this.wuliaodata = this.form1.lsERPKey_.map(function (item, index) {
            var parsedItem = JSON.parse("{" + item + "}");
            var fac = parseInt(Object.keys(parsedItem)[0].substring(1));
            var key = parsedItem[Object.keys(parsedItem)[0]];
            return { erpkey: key, faid: fac.toString(), ind: index };
          });
        }
        var c = [];
        for (let index = 0; index < this.wuliaodata.length; index++) {
          if (this.wuliaodata[index].faid || this.wuliaodata[index].faid != "") {
            c.push(this.wuliaodata[index].faid);
          }
        }
        this.keys = [...Object.keys(this.factoryname), ...Object.values(this.factoryname)];
        this.allkeys = [...c];
        if (this.wuliaodata.length >= 1 && this.wuliaodata[0].faid != "") {
          this.adddata(this.wuliaodata.length - 1);
        }
        this.showaction = this.wuliaodata.length;
        //采购价格数据获取
        if (this.form1.lsPurchasePrice && this.form1.lsPurchasePrice[0].includes("F")) {
          this.purchasedata = [];
          this.form1.lsPurchasePrice.map((str, index) => {
            const parsedObject = JSON.parse(str);
            // 提取第一个键值对并转换为指定格式的对象
            const firstKey = Object.keys(parsedObject)[0]; // 获取第一个键名
            const { price, minpur } = parsedObject[firstKey]; // 解构赋值获取价格和最小购买量
            const facNumber = firstKey.replace("F", ""); // 移除前缀'F'
            // 构造新对象
            const resultObject = {
              faid: facNumber,
              price: price.toString(),
              minpur: minpur.toString(),
              ind: index,
            };
            this.purchasedata.push(resultObject);
            // const match = str.match(/\{F(\d+):{price:(\d+),minpur:(\d+)}}/);
            //   if (match) {
            //     // 构造新的字典
            //     const formattedDict = {
            // faid: match[1],
            // price: match[2].toString(),
            // minpur: match[3].toString(),
            // ind:index
            //     };
            //     // 将字典放入数组
            //     this.purchasedata.push(formattedDict);
            //   } else {
            //     console.log("No match found");
            //   }
          });
        }
        var d = [];
        for (let index = 0; index < this.purchasedata.length; index++) {
          if (this.purchasedata[index].faid || this.purchasedata[index].faid != "") {
            d.push(this.purchasedata[index].faid);
          }
        }
        this.keys = [...Object.keys(this.factoryname), ...Object.values(this.factoryname)];
        this.purallkeys = [...d];
        if (this.purchasedata.length >= 1 && this.purchasedata[0].faid != "") {
          this.addpurdata(this.purchasedata.length - 1);
        }
        this.puraction = this.purchasedata.length;
        console.log(this.purchasedata, "purchasedata");
      });
    }
  },
  data() {
    return {
      autoFocus: true,
      data1: {},
      showaction: "",
      puraction: "",
      keys: [],
      allkeys: [],
      purallkeys: [],
      columns1,
      columns2,
      wuliaodata: [
        {
          erpkey: "",
          faid: "",
          index: 0,
        },
      ],
      purchasedata: [
        {
          price: "",
          faid: "",
          index: 0,
          minpur: "",
        },
      ],
      form1: {
        //No:0,  // 物料类别ID 908板材、909铜箔、914半固化片
        materName_: "", // 物料名称
        materSpec_: "", // 物料规格
        materVendor_: "", //供应商
        unitsName_: "", //供应单位
        //铜箔
        innerOrOuter: "", //内外铜箔
        cU_: "", //铜厚
        cuThickness_: "", //实际铜箔厚度
        cuThicknessOrg_: "",
        pP_: "",
        erpKey_: {},
      },
      selectValue: "",
    };
  },
  //  watch: {
  //     selectValue (newval,oldval) {
  //       console.log("监听",newval,oldval)
  //       if (!newval) {
  //         this.form1.pP_ = oldval;
  //       }
  //       console.log("this.form1.pP_",this.form1.pP_)
  //     }
  //   },

  methods: {
    isRedRow(record, index) {
      let strGroup = [];
      if (this.keys.includes(record.faid) || index == this.wuliaodata.length - 1 || !this.editnew) {
        //
      } else {
        strGroup.push("Rowdisiplay");
      }
      return strGroup;
    },
    purisRedRow(record, index) {
      let strGroup = [];
      if (this.keys.includes(record.faid) || index == this.purchasedata.length - 1 || !this.editnew) {
        //
      } else {
        strGroup.push("Rowdisiplay");
      }
      return strGroup;
    },
    dkchange() {
      this.$set(this.form1, "dkInner_", this.form1.dkOuter_);
      this.changematerial();
    },
    topozchange() {
      this.$set(this.form1, "bomOZ_", this.form1.topOZ_);
      this.changematerial();
    },
    fachange(index, faid, record) {
      if (!faid) {
        this.wuliaodata[index].erpkey = "";
      }
    },
    purfachange(index, faid, record) {
      if (!faid) {
        this.purchasedata[index].price = "";
        this.purchasedata[index].minpur = "";
      }
    },
    facblur(index, value) {
      if (value && !this.wuliaodata[index + 1]) {
        this.adddata(index);
      }
      if (!this.wuliaodata[index].faid && !this.wuliaodata[index].erpkey && index != this.wuliaodata.length - 1) {
        this.closedata(index);
      }
      if (this.editnew && (this.showaction > 2 || this.wuliaodata.length > 2)) {
        this.form1 = JSON.parse(localStorage.getItem("maindata"))[this.ide];
      }
      let a = [];
      for (let i = 0; i < this.wuliaodata.length - 1; i++) {
        if (this.wuliaodata[i].faid == value && value != "" && this.wuliaodata[i].faid) a.push(this.wuliaodata[i].faid);
      }
      if (this.allkeys.includes(value) || a.length > 1) {
        this.wuliaodata[index].faid = "";
        this.closedata(index);
        this.$message.error("该工厂下编码已存在,不得重复添加");
      }
    },
    purfacblur(index, value) {
      if (value && !this.purchasedata[index + 1]) {
        this.addpurdata(index);
      }
      if (!this.purchasedata[index].faid && !this.purchasedata[index].price && index != this.purchasedata.length - 1) {
        this.purclosedata(index);
      }
      if (this.editnew && (this.puraction > 2 || this.purchasedata.length > 2)) {
        this.form1 = JSON.parse(localStorage.getItem("maindata"))[this.ide];
      }
      let a = [];
      for (let i = 0; i < this.purchasedata.length - 1; i++) {
        if (this.purchasedata[i].faid == value && value != "" && this.purchasedata[i].faid) a.push(this.purchasedata[i].faid);
      }
      if (this.purallkeys.includes(value) || a.length > 1) {
        this.purchasedata[index].faid = "";
        this.purclosedata(index);
        this.$message.error("该工厂下采购价格已存在,不得重复添加");
      }
    },
    adddata(index) {
      this.wuliaodata.splice(index + 1, 0, {
        erpkey: "",
        faid: "",
        ind: index + 1,
      });
    },
    addpurdata(index) {
      this.purchasedata.splice(index + 1, 0, {
        price: "",
        minpur: "",
        faid: "",
        ind: index + 1,
      });
    },
    closedata(index, record) {
      if (this.wuliaodata[index].erpkey == "" && this.wuliaodata[index].faid == "" && index == this.wuliaodata.length - 1) {
        this.$message.error("该条数据不允许删除");
        return;
      } else {
        this.wuliaodata.splice(index, 1);
      }
    },
    purclosedata(index, record) {
      if (
        this.purchasedata[index].price == "" &&
        this.purchasedata[index].minpur == "" &&
        this.purchasedata[index].faid == "" &&
        index == this.purchasedata.length - 1
      ) {
        this.$message.error("该条数据不允许删除");
        return;
      } else {
        this.purchasedata.splice(index, 1);
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    changematerType() {
      let fac = this.mapKey(this.factoryname);
      if (this.form1.materType_ == 908 && !this.editnew) {
        this.form1.units_ = 882;
        if (fac[0].value == "38") {
          this.wuliaodata[0].erpkey = "/";
          this.wuliaodata[0].faid = "38";
          this.wuliaodata.push({
            erpkey: "",
            faid: "",
          });
          this.form1.pnlSizeX_ = "49";
          this.form1.pnlSizeY_ = "41";
        }
      }
      var a = document.getElementsByClassName("materclass");
      a[0].style.height = "580px";
      this.$set(this.form1, "materName_", "");
      this.$set(this.form1, "materSpec_", "");
      this.changematerial();
    },
    dkchange1() {
      this.$set(this.form1, "dkOuter_", this.form1.dkInner_);
    },
    changematerial() {
      var a = "";
      if (this.form1.coreTypeCodes_) {
        this.platetypenew.forEach(ite => {
          if (ite.id == this.form1.coreTypeCodes_) {
            a = ite.coreType_;
          }
        });
      }
      let b = this.form1.core_ ? " " + this.form1.core_ : "";
      let c = this.form1.tD_ == true ? " 含铜" : " 不含铜";
      let d = this.form1.topOZ_ ? " " + "(" + this.form1.topOZ_ + "/" : "";
      let e = this.form1.bomOZ_ ? this.form1.bomOZ_ + ")" : "";
      let f = this.form1.dkOuter_ ? " " + this.form1.dkOuter_ : "";
      let g = this.form1.dkInner_ ? "/" + this.form1.dkInner_ : "";
      let h = this.form1.pnlSizeX_ ? " " + "(" + this.form1.pnlSizeX_ : "";
      let i = this.form1.pnlSizeY_ ? "X" + this.form1.pnlSizeY_ + ")" : "";
      let j = this.materialCategory2.filter(ite => ite.id == this.form1.materVendor_)[0]?.verdorName_ || "";
      let k = this.form1.ppTypeCodes_ ? " " + this.pptype.filter(ite => ite.valueMember == this.form1.ppTypeCodes_)[0]?.text : "";
      let l = " " + this.form1.pP_;
      let m = this.form1.rC_ ? " RC" + this.form1.rC_ + "%" : "";
      let n = "生益" + (this.form1.innerOrOuter == 910 ? " 外层基铜" : this.form1.innerOrOuter == 911 ? " 内层基铜" : "");
      let o = this.form1.cU_ ? " " + this.form1.cU_ + " oz" : "";
      if (this.form1.materType_ == 908) {
        this.$set(this.form1, "materName_", "板材");
        this.$set(this.form1, "materSpec_", a + b + d + e + c + f + g + h + i);
      } else if (this.form1.materType_ == 914) {
        this.$set(this.form1, "materName_", "半固化片");
        this.$set(this.form1, "materSpec_", j + k + l + m);
      } else if (this.form1.materType_ == 909) {
        this.$set(this.form1, "materName_", "铜箔");
        this.$set(this.form1, "materSpec_", n + o);
      }
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    handleSearch(pP_) {
      this.handleChange(pP_);
    },
    handleChange(pP_) {
      this.pP_ = pP_ != null && pP_ != " " ? pP_ : [];
      console.log("change", this.pP_);
      if (this.pP_ != "") {
        this.selectValue = this.pP_;
      }
      this.changematerial();
    },
    handleBlur(selectValue) {
      console.log("blur_", selectValue);
      this.form1.pP_ = this.selectValue;
    },
  },
  directives: {
    focusNextOnEnter: {
      bind: function (el, { value }, vnode) {
        el.addEventListener("keyup", ev => {
          if (ev.keyCode === 13) {
            let nextInput = vnode.context.$refs[value];
            if (nextInput && typeof nextInput.focus === "function") {
              nextInput.focus();
              nextInput.select();
            }
          }
        });
      },
    },
  },
};
</script>
<style scoped lang="less">
/deep/.Rowdisiplay {
  display: none !important;
}
/deep/.required {
  .ant-form-item-label > label {
    color: red !important;
  }
}
.erpkey {
  /deep/.ant-input {
    height: 30px;
    padding: 3px;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
}
.materclass {
  height: 310px;
}
.costyle1 {
  position: relative;
  top: -330px;
}
.costyle2 {
  position: relative;
  top: -278px;
}
.purcostyle {
  position: relative;
  top: -330px;
  background: #afafaf;
  margin-bottom: 5px;
}
.costyle2 {
  position: relative;
  top: -282px;
}
.costyle3 {
  position: relative;
  top: -7px;
}
.costyle4 {
  position: relative;
  top: -53px;
}
.ant-modal-body {
  .ant-form-item {
    margin-bottom: 8px;
  }
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 4px 4px;
  overflow-wrap: break-word;
  height: 36px;
}
/deep/.ant-table-tbody > tr > td {
  padding: 4px 4px;
  overflow-wrap: break-word;
  height: 36px;
}
</style>
