<!-- 工程管理 - 工程后端-订单列表 左边 -->
<template>
  <div>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ x: 1200, y: 738 }"
      :customRow="customRow"
      :pagination="pagination"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      @change="handleTableChange"
      :rowClassName="isRedRow"
    >
      <div slot="score" slot-scope="text, record">
        <a-tooltip :title="xstitle">
          <span v-if="record.score" style="color: #428bca" @mouseover="Coefficientdetails(record.proOrderId)">{{ record.score.toFixed(2) }}</span>
        </a-tooltip>
      </div>
      <template slot="eqCostTime" slot-scope="text, record">
        <span v-if="record.eqCostTimeColor == 1" style="color: red">{{ record.eqCostTime }}</span>
        <span v-else-if="record.eqCostTime">{{ record.eqCostTime }}</span>
      </template>
      <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
        <a style="color: black" :title="record.orderNo">{{ text }}</a
        >&nbsp;
        <span class="tagNum" style="display: inline-block; height: 19px">
          <a-tooltip title="极限加急" v-if="record.isExtremeJiaji">
            <span class="noCopy" style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none">
              &nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
            <span class="noCopy" style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tooltip title="加急" v-else-if="record.isJiaji">
            <span class="noCopy" style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0; margin-left: -10px; user-select: none"
              >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
            </span>
          </a-tooltip>
          <a-tooltip title="新客户" v-if="record.isNewCust">
            <a-tag
              class="noCopy"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              新
            </a-tag>
          </a-tooltip>
          <a-tag
            v-if="record.ka"
            style="
              font-size: 10px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            KA
          </a-tag>
          <a-tooltip v-if="record.onLineEcnState > 0" :title="record.onLineOrRecordEcn == 2 ? '更改存档升级' : 'ECN在线改版'">
            <a-tag
              style="
                font-size: 10px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
                cursor: pointer;
              "
            >
              升
            </a-tag>
          </a-tooltip>
          <a-tooltip title="ECN改版不升级" v-if="record.customClassification == 'ECN改版不升级' && record.isReOrder == 1">
            <a-tag
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              改
            </a-tag>
          </a-tooltip>
          <a-tag
            class="noCopy"
            v-if="record.isJunG"
            color="#2D221D"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            {{ record.joinFactoryId == 70 ? "J" : "军" }}
          </a-tag>
          <a-tooltip title="二次投产" v-if="record.backUserAccount > 0">
            <a-tag
              class="noCopy"
              color="#2D221D"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              二
            </a-tag>
          </a-tooltip>
          <a-tag
            class="noCopy"
            v-if="record.pauseCancelState == 2"
            color="#2D221D"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            取消
          </a-tag>
          <a-tooltip v-if="record.identificationType == 1" title="按新单制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              新 </span
            >&nbsp;
          </a-tooltip>
          <a-tooltip v-if="record.identificationType == 2" title="按返单有改制作">
            <span
              style="
                background: #ff9900;
                border-radius: 50%;
                color: white;
                padding: 0 1px;
                text-align: center;
                height: 22px;
                width: 22px;
                line-height: 20px;
                display: inline-block;
                font-size: 12px;
              "
            >
              改 </span
            >&nbsp;
          </a-tooltip>
          <a-tag
            class="noCopy"
            v-if="record.pauseCancelState == 3"
            color="#2D221D"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            暂停
          </a-tag>
          <a-tag
            v-if="record.pauseCancelState == 4"
            color="#2D221D"
            class="noCopy"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            暂停取消
          </a-tag>
          <a-tag
            v-if="record.isEQ == 1 && record.statusType != '问客已回复' && record.statusType != '问客已审核' && record.statusType != '问客'"
            color="#2D221D"
            class="noCopy"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            问
          </a-tag>
          <a-tooltip title="返修中" v-if="record.isFix">
            <a-tag
              color="#2D221D"
              class="noCopy"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
              @click.stop="RepairRecordClick(record)"
            >
              修
            </a-tag>
          </a-tooltip>
          <a-tag
            v-if="record.isOrderModify"
            @click.stop="xiudisplay(record)"
            style="
              font-size: 12px;
              background: #ff9900;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #ff9900;
            "
          >
            修
          </a-tag>
          <a-tooltip title="确认工作稿" v-if="record.confirmWorkingDraft">
            <a-tag
              class="noCopy"
              style="
                font-size: 12px;
                background: #428bca;
                color: white;
                padding: 0 2px;
                margin: 0;
                margin-right: 3px;
                height: 21px;
                user-select: none;
                border: 1px solid #428bca;
              "
            >
              确
            </a-tag>
          </a-tooltip>
          <a-tooltip :title="record.fileUploadedTime ? '重传文件时间:' + record.fileUploadedTime : ''" v-if="record.fileUploadedCount > 0">
            <span>
              <a-tag
                style="
                  font-size: 12px;
                  background: red;
                  color: white;
                  padding: 0 2px;
                  margin-left: 3px;
                  margin-right: 3px;
                  height: 21px;
                  user-select: none;
                  border: 1px solid red;
                "
              >
                重
              </a-tag>
              <span style="font-size: 8px; color: red; position: relative; top: -10px; left: -2px">{{ record.fileUploadedCount }}</span>
            </span>
          </a-tooltip>
          <a-tag
            color="#2D221D"
            class="noCopy"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
            v-if="record.isHighQuality != 0"
          >
            {{ record.isHighQuality == 1 ? "优" : "精" }}
          </a-tag>
          <a-tag
            v-if="record.isLock"
            color="#2D221D"
            class="noCopy"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            锁
          </a-tag>

          <a-tag
            v-if="record.smtFactoryId > 0"
            color="#2D221D"
            class="noCopy"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            贴
          </a-tag>
          <a-tag
            v-if="record.isBigCus"
            color="#2D221D"
            class="noCopy"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            KA
          </a-tag>
          <a-tag
            v-if="record.isReverse == '1' || record.isReverse == '2'"
            color="#2D221D"
            class="noCopy"
            style="
              font-size: 12px;
              background: #428bca;
              color: white;
              padding: 0 2px;
              margin: 0;
              margin-right: 3px;
              height: 21px;
              user-select: none;
              border: 1px solid #428bca;
            "
          >
            反
          </a-tag>
        </span>
      </div>
      <div slot="labelUrl" slot-scope="record">
        <span @click.stop="OperationLog(record)" style="color: #0000cc">日志</span>
      </div>
      <div slot="process" slot-scope="text, record">
        <a-tooltip title="工艺流程">
          <a-icon type="file-text" style="color: #ff9900; font-size: 18px" @click.stop="orderClick1(record)" />
        </a-tooltip>
      </div>
      <span slot="num" slot-scope="text, record, index">
        {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </span>
      <div slot="isProcessStepNotes" slot-scope="text, record">
        <a-button type="link" v-if="text.isProcessStepNotes" @click="lookProductRemark(record)">查看</a-button>
      </div>
      <div slot="autoType" slot-scope="text, record">
        <span>{{ record.autoType ? "" : "直通" }}</span>
      </div>
      <div slot="isCustRule" slot-scope="text, record">
        <a-tooltip title="客户规则" v-if="record.isCustRule == 1">
          <a-icon
            type="file-text"
            style="color: #ff9900; font-size: 18px; margin-right: 10px"
            class="noCopy"
            @click.stop="CustomerRulesClick(record)"
          />
        </a-tooltip>
      </div>
      <div slot="adminQQ" slot-scope="text, record">
        <a style="color: #0000cc" :href="'tencent://message/?uin=' + record.adminQQ + '&Site=&Menu=yes'">{{
          record.adminName + ":" + record.adminQQ
        }}</a>
      </div>
      <div slot="checkAdminQQ" slot-scope="text, record">
        <a style="color: #0000cc" :href="'tencent://message/?uin=' + record.checkAdminQQ + '&Site=&Menu=yes'">{{
          record.checkAdminName + "：" + record.checkAdminQQ
        }}</a>
      </div>
      <div slot="followAdminQQ" slot-scope="text, record">
        <a style="color: #0000cc" :href="'tencent://message/?uin=' + record.followAdminQQ + '&Site=&Menu=yes'">{{
          record.followAdminName + "：" + record.followAdminQQ
        }}</a>
      </div>
      <div slot="action" slot-scope="text, record">
        <a-tooltip title="回退业务" v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendBackOrder')">
          <a-icon type="rollback" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="ChargebackClick(record)" />
        </a-tooltip>
        <a-tooltip title="修改信息">
          <a-icon type="form" style="color: #ff9900; font-size: 18px; margin-right: 10px" @click.stop="modifyInfoClick(record)" />
        </a-tooltip>
        <!-- <a-tooltip title="回退前端"   2023/8/29更改为弹窗模式
        v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendBackOrders')">
        <a-icon type="backward" style="color: #ff9900; font-size: 18px; margin-right: 10px"
          @click.stop='FallbackFrontClick(record)' />
      </a-tooltip> -->
        <a-tooltip title="查看日志">
          <a-icon type="bars" style="color: #ff9900; font-size: 18px" @click.stop="viewLogClick(record)" />
        </a-tooltip>
        <!-- <a-tooltip title="状态同步">
        <a-icon type="file-sync" style="color: #ff9900; font-size: 18px" @click.stop='StatusSynchronization(record)' />
      </a-tooltip> -->
      </div>
      <div slot="jigsawPuzzle" slot-scope="text, record">
        <a @click="() => $emit('jigsawPuzzleClick', record)" v-if="text" class="noCopy">拼版图</a>
      </div>
      <template slot="script" slot-scope="text, record">
        <a-tooltip title="发送脚本">
          <a-icon type="aliwangwang" style="color: #ff9900; font-size: 18px" class="noCopy" @click.stop="webSocketLink(record)" />
        </a-tooltip>
      </template>
      <template slot="action1" slot-scope="text, record">
        <a-tooltip title="工程指示">
          <a-icon
            type="container"
            style="color: #ff9900; font-size: 18px"
            class="noCopy"
            @click.stop="orderClick(record.orderNo, record.proOrderId, record.backEndRealName, record)"
          />
        </a-tooltip>
      </template>
      <div slot="down" slot-scope="text, record" class="noCopy">
        <a class="noCopy" style="color: #428bca" @click.stop="down1(record)">下载</a>
      </div>
      <div slot="down2" slot-scope="text, record" class="noCopy">
        <a class="noCopy" style="color: #428bca" @click.stop="down(record)">下载</a>
      </div>
      <div slot="isReOrder" slot-scope="text, record">
        {{ record.isReOrder == 0 ? "新单" : record.isReOrder == 1 ? "返单" : record.isReOrder == 2 ? "返单更改" : "" }}
      </div>
    </a-table>
    <!-- 操作日志弹窗 -->
    <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose :maskClosable="false" @cancel="reportHandleCancel" :width="800" centered>
      <div class="projectackend">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
        </a-table>
      </div>
      <template slot="footer">
        <a-button type="primary" @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
    <a-modal title="修改内容" :width="1300" :visible="xiuvisible" destroyOnClose centered :maskClosable="false" @cancel="handleCancel">
      <div>
        <a-table
          class="xiu"
          :columns="columns4"
          :dataSource="dataSource4"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
          :scroll="{ y: 400 }"
          :class="dataSource4.length ? 'min-table' : ''"
          :pagination="false"
          :loading="loading2"
        >
          <template slot="isPrintContract" slot-scope="record">
            <a-checkbox :checked="record.isPrintContract"></a-checkbox>
          </template>
          <div slot="filePath" slot-scope="record, index" v-if="record.filePath">
            <template v-for="photo in record.filePath.split(',')">
              <img :src="photo.trim()" style="height: 19px; padding-right: 3px" v-viewer :key="index + '-' + photo" />
            </template>
          </div>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="handleCancel">取消</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { proordercoefficient } from "@/services/projectDisptch";
import { ordermodifylist } from "@/services/mkt/CustInfoNew";
import { proorderlog } from "@/services/projectDisptch";
import { checkPermission } from "@/utils/abp";
import { setEngineeringBack } from "@/utils/request";
import { proQuestLog } from "@/services/projectApi";
const columns4 = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    width: "4%",
    align: "center",
  },
  {
    title: "发起订单位置",
    dataIndex: "orderModifyType",
    ellipsis: true,
    width: "8%",
    align: "left",
    customRender: (text, record, index) => `${record.orderModifyType == 1 ? "报价发起" : record.orderModifyType == 2 ? "工程发起" : "问客发起"}`,
  },
  {
    title: "订单号",
    dataIndex: "orderNo",
    width: "10%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "内容",
    dataIndex: "content",
    ellipsis: true,
    width: "30%",
    align: "left",
  },
  {
    title: "图片",
    scopedSlots: { customRender: "filePath" },
    ellipsis: true,
    align: "left",
    width: "14%",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    ellipsis: true,
    align: "left",
    width: "12%",
  },
  {
    title: "创建人",
    dataIndex: "createName",
    ellipsis: true,
    width: "7%",
    align: "left",
  },
  {
    title: "打印合同",
    scopedSlots: { customRender: "isPrintContract" },
    ellipsis: true,
    align: "left",
    width: "7%",
  },
];
export default {
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            current: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
          selectedRows: {},
          ttype: "",
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
    cookieId: {
      type: String,
      require: true,
    },
  },
  name: "LeftTable",
  data() {
    return {
      labordata: [],
      xstitle: "",
      dataSource4: [],
      columns4,
      loading2: false,
      xiuvisible: false,
      labordataVisible: false,
      laborcolumns: [
        {
          title: "序号",
          align: "center",
          dataIndex: "index",
          key: "index",
          width: 20,
          customRender: (text, record, index) => `${index + 1}`,
        },
        {
          title: "操作时间",
          align: "left",
          dataIndex: "createTime",
          width: 65,
        },
        {
          title: "操作人",
          align: "left",
          dataIndex: "userName",
          width: 30,
        },
        {
          title: "内容",
          align: "left",
          dataIndex: "content",
          width: 185,
        },
      ],
      selectedRowKeysArray: [],
      selectedRowsData: [],
      activeClass: "smallActive",
      proOrderId: "",
      menuData: {},
      // cookieId: '',
    };
  },
  watch: {
    dataSource: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let pauseCancelState = val.some(val => val.pauseCancelState == 4) ? 30 : 0;
            let maxLength = 0;
            let longestChars = [];
            for (let i = 0; i < val.length; i++) {
              let obj2 = val[i].orderNo;
              if (obj2) {
                var [...chars] = obj2;
                if (chars.length > maxLength) {
                  maxLength = chars.length;
                  longestChars = obj2;
                }
              }
            }
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0 && longestChars.length > 12) {
              this.columns[1].width = 120 + (longestChars.length - 12) * 6 + "px";
              this.columns[5].width = "135px";
              this.columns[7].width = "80px";
              this.columns[8].width = "70px";
            }
            if (result >= 1 && longestChars.length > 12) {
              this.columns[1].width = 120 + (longestChars.length - 12) * 6 + result * 20 + pauseCancelState + "px";
              this.columns[5].width = 135 - result * 7 - (longestChars.length - 12) * 2 + "px";
              this.columns[7].width = 80 - result * 7 - (longestChars.length - 12) * 2 + "px";
              this.columns[8].width = 70 - result * 6 - (longestChars.length - 12) * 2 + "px";
            }
            if (result == 0 && longestChars.length <= 12) {
              this.columns[1].width = "120px";
              this.columns[5].width = "135px";
              this.columns[7].width = "80px";
              this.columns[8].width = "70px";
            }
            if (result >= 1 && longestChars.length <= 12) {
              this.columns[1].width = 120 + result * 20 + pauseCancelState + "px";
              this.columns[5].width = 135 - result * 7 + "px";
              this.columns[7].width = 80 - result * 7 + "px";
              this.columns[8].width = 70 - result * 6 + "px";
            }
          });
        }
      },
    },
    pagination: {
      handler(val) {},
    },
  },
  created() {},
  methods: {
    checkPermission,
    Coefficientdetails(id) {
      proordercoefficient(id, 1).then(res => {
        if (res.code) {
          let tit = [];
          let data = res.data[0].coefficientInfos;
          for (let index = 0; index < data.length; index++) {
            tit.push("【" + data[index].description + ": " + data[index].score + "】");
          }
          this.xstitle = tit.join(" + ");
        }
      });
    },
    handleCancel() {
      this.xiuvisible = false;
    },
    xiudisplay(record) {
      this.loading2 = true;
      ordermodifylist(record.proOrderId)
        .then(res => {
          if (res.code) {
            this.dataSource4 = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
      this.xiuvisible = true;
    },
    OperationLog(record) {
      proorderlog(record.proOrderId).then(res => {
        if (res.code) {
          this.labordata = res.data;
          this.labordataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    reportHandleCancel() {
      this.labordataVisible = false;
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.proOrderId == this.cookieId) {
        strGroup.push("cookieIdColor");
      }
      if (record.proOrderId && this.selectedRowKeysArray.includes(record.proOrderId)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            this.$emit("rightClick1", e, text, record);
            e.preventDefault();
            this.menuData = record;
          },
        },
      };
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].proOrderId);
      }
      this.selectedRowKeysArray = arr;
      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[this.selectedRowKeysArray.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowKeysArray[0];
        })[0];
      }
    },
    handleMouseUp(event, record, index) {
      this.isDragging = false;
      if (this.shiftKey) {
        let keys = this.selectedRowKeysArray;
        if (keys.length > 0 && keys.includes(record.proOrderId)) {
          keys.splice(keys.indexOf(record.proOrderId), 1);
        } else {
          keys.push(record.proOrderId);
        }

        this.selectedRowKeysArray = keys;
        if (this.selectedRowKeysArray.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowKeysArray = [record.proOrderId];
        }
      }
      this.$emit("getOrderDetail", record);
      //this.$emit('getJobInfo', record.proOrderId)
      this.$emit("modifyInfoClick1", record, this.selectedRowKeysArray);
      this.shiftKey = false;
    },
    // onClickRow(record) {
    //   return {
    //     on: {
    //       click: () => {
    //         let keys = [];
    //         keys.push(record.proOrderId);
    //         this.selectedRowKeysArray = keys;
    //         this.selectedRowsData = record
    //         this.$emit('getOrderDetail', record)
    //         this.$emit('getJobInfo', record.proOrderId)
    //         this.$emit('modifyInfoClick1', record)
    //         // this.$emit('getprocessstepnotes', record.orderNo)
    //         this.proOrderId = record.proOrderId
    //       },
    //       dblclick: (event) => {
    //         // this.$emit('socketSendMessage')
    //         // this.$emit('webSocketLink')
    //         // setEngineeringBack({
    //         //   token: record.proOrderId,
    //         // });
    //         // this.getcookie('ordernoBack')
    //       },
    //       contextmenu: e => {
    //         let text = ''
    //         if(e.target.innerText){
    //          text = e.target.innerText
    //         }
    //         this.$emit('rightClick1',e,text,record)
    //         e.preventDefault();
    //         this.menuData = record;

    //       },
    //     }
    //   }
    // },
    lookProductRemark(record) {
      this.$emit("getprocessstepnotes", record.orderNo);
    },
    handleTableChange(pagination, filter, sorter) {
      this.$emit("tableChange", pagination, filter, sorter);
    },
    // 退单（回退业务）
    ChargebackClick(record) {
      this.$emit("ChargebackClick", record);
    },

    // 修改信息
    modifyInfoClick(record) {
      this.$emit("modifyInfoClick", record);
    },
    // 查看日志
    viewLogClick(record) {
      this.$emit("viewLogClick", record);
    },
    // 客户规则
    CustomerRulesClick(record) {
      // this.proOrderId = record.proOrderId
      this.$emit("CustomerRulesClick", record);
    },
    // 获取cookie缓存订单id
    getcookie(ordernoBack) {
      //获取指定名称的cookie的值
      var arrstr = document.cookie.split("; ");
      for (var i = 0; i < arrstr.length; i++) {
        var temp = arrstr[i].split("=");
        if (temp[0] == ordernoBack) {
          this.cookieId = unescape(temp[1]);
          // return unescape(temp[1]);
        }
      }
    },
    // 下载原稿文件
    // down(record) {
    //   window.location.href = record.pcbFilePath
    // },

    down(record) {
      let a = record.pcbFilePath.split(".").slice(-1)[0];
      if (record.pcbFilePath) {
        const urlObj = new URL(record.pcbFilePath);
        const path = urlObj.pathname;
        const fileName = path.substring(path.lastIndexOf("/") + 1);
        //const fileNameWithoutQuery = decodeURI(fileName.split('?')[0] )
        const fileNameWithoutQuery = record.pcbFileName + "." + a;
        const xhr = new XMLHttpRequest();
        xhr.open("GET", record.pcbFilePath, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = fileNameWithoutQuery;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      }
    },

    // 状态同步
    StatusSynchronization(record) {
      this.$emit("StatusSynchronization", record);
    },
    // 下载前端文件
    down1(record) {
      // window.location.href = record.exportFile
      let params = {
        OrderNo: record.orderNo,
        Type: 0,
      };
      const BASE_URL = process.env.VUE_APP_API_BASE_URL;
      proQuestLog(params).then(res => {
        if (res.code) {
          if (res.data && res.data.filePath) {
            // let str = res.data.filePath
            // let newStr = BASE_URL + '/' + str
            // window.location.href = res.data.filePath
            var ordermodel1 = res.data.orderNo;
            const ordermodel = ordermodel1.toLowerCase().replace(/([a-z])([A-Z])/g, "$1-$2");
            let a = res.data.filePath.split(".").slice(-1)[0];
            const xhr = new XMLHttpRequest();
            xhr.open("GET", res.data.filePath, true);
            xhr.responseType = "blob";
            xhr.onload = function () {
              if (xhr.status === 200) {
                const blob = xhr.response;
                const link = document.createElement("a");
                link.href = window.URL.createObjectURL(blob);
                link.download = ordermodel + "." + a;
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }
            };
            xhr.send();
          } else {
            this.$message.error("文件不存在");
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    RepairRecordClick(record) {
      this.$emit("RepairRecordClick", record);
    },
    // 工艺流程
    orderClick1(record) {
      this.$router.push({ path: "/gongcheng/technologicalProcess", query: { pdctno: record.orderNo, factoryId: record.joinFactoryId } });
    },
    orderClick(OrderNo, id, backEndRealName, record) {
      this.$nextTick(function () {
        localStorage.setItem("pageCurrent1", this.pagination.current);
        localStorage.setItem("pageSize1", this.pagination.pageSize);
        localStorage.setItem("OrderId1", record.proOrderId);
        localStorage.setItem("record7", JSON.stringify(record));
        localStorage.setItem("stat1", true);
        localStorage.setItem("procust", true);
        this.$router.push({
          path: "/gongcheng/engineering",
          query: {
            OrderNo: OrderNo,
            id: id,
            backEndRealName: backEndRealName,
            factory: record.joinFactoryId,
            typee: "1",
            isCustRule: record.isCustRule,
            custNo: record.custNo,
            fileUploadedCount: record.fileUploadedCount,
            businessOrderNo: record.businessOrderNo,
          },
        });
      });
    },
    webSocketLink(record) {
      if (record.statusType == "后端制作中" || record.statusType == "返修中") {
        this.$emit("webSocketLink", record.proOrderId);
      } else {
        this.$message.error("当前订单状态无法发送脚本");
      }
    },
    rightClick1(e, text, record) {
      this.$emit("rightClick1", e, text, record);
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
};
</script>

<style lang="less" scoped>
.xiu {
  /deep/.ant-table-row-cell-ellipsis,
  .ant-table-row-cell-ellipsis .ant-table-column-title {
    overflow: overlay;
    white-space: normal;
    text-overflow: inherit;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.min-table {
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/.ant-table-thead > tr > th {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-content {
  border: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 7px 3px !important;
  border-right: 1px solid #efefef;
}
.projectackend {
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc !important;
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/ .ant-table {
    .ant-table-header {
      border-top: 1px solid #efefef;
      border-right: 1px solid #efefef;
    }
    .ant-table-thead > tr > th {
      padding: 4px 4px;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
    }
    .ant-table-tbody > tr > td {
      padding: 4px 4px !important;
      border-right: 1px solid #efefef;
      border-left: 1px solid #efefef;
      max-width: 100px;
      text-overflow: ellipsis;
      white-space: normal;
      overflow: hidden;
      color: #000000;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }
}
/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }

  .ant-table-fixed-left {
    .cookieIdColor {
      td:first-child {
        //border-left:2px solid #ff9900!important;
        background: #ff9900 !important;
      }
    }

    //td:nth-child(2){
    //  color:#ff9900!important;
    //}
  }

  .rowBackgroundColor {
    background: #dcdcdc;
  }

  .displayFlag {
    display: none;
  }
}

.peopleTag {
  margin: 0;
  padding: 0;
  width: 24px;
  border-radius: 12px;
  background: #2d221d;
  border-color: #2d221d;
  color: #ff9900;
  text-align: center;
  margin-left: 2px;
}
</style>
