<template>
  <a-modal
    :title="model.id? '编辑' : '新增'"
    :width="640"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    centered
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="一次准交得分" ref="number1" >
          <a-input placeholder="如当月未管控可不输入" v-model="form.processControlScore" />
        </a-form-model-item>
        <a-form-model-item  label="KA准交得分" ref="number2" >
          <a-input placeholder="如当月未管控可不输入" v-model="form.fqaControlScore" />
        </a-form-model-item>
        <a-form-model-item  label="一类逾期得分" ref="number3" >
          <a-input placeholder="如当月未管控可不输入" v-model="form.customerControlScore" />
        </a-form-model-item>
        <a-form-model-item  label="备注" ref="orderType_">
          <a-input
              v-model="form.note"
              placeholder="请输入"
          />
        </a-form-model-item>
        <a-form-model-item  label="评级时间" ref="machineType_" prop="gradeDate">
          <a-month-picker placeholder="选择月份" v-model="form.gradeDate" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  addBoard,
  updateBoard,
  progressNum,
  addDeliveryData,
  updateDeliveryData,
  addQualityRating
} from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
      compileApply:{
          type: String,
          default () {
              return ''
          }
      },
      message:{
          type: String,
          default () {
              return ''
          }
      },
      dataType:{
          type: Number
      }
  },
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
      visible: false,
      confirmLoading: false,
      form: {
        note: '',
        grade: '',
        gradeDate: '',
        processControlScore:'',
        fqaControlScore:'',
        customerControlScore:'',
      },
      rules: {
        grade: [
          { required: true, message: "请选择产品等级", trigger: "blur" },
        ],
        gradeDate: [
            { required: true, message: "请选择评级时间", trigger: "change" },
        ],
        // number:[
        //   { required: true, message: "请输入评分", trigger: "blur" },
        // ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
      console.log('model',model)
      //  if(this.compileApply=='1'){
           this.visible = true;
           this.model = model
           if(model ) {
               this.form = {
                 // grade: model.grade,
                 note: model.note,
                 gradeDate: model.gradeDates,
                 processControlScore:model.processControlScore,
                 fqaControlScore:model.fqaControlScore,
                 customerControlScore:model.customerControlScore,
               };
           }else {
               //清空form操作过的数据
               this.form= {
                 gradeDate: '',
                 grade: '',
                 note: '',
                 processControlScore:'',
                 fqaControlScore:'',
                 customerControlScore:'',
               }
           }
      //  }else {
      //      this.$message.info(this.message)
      //  }


    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
       this.form= {
         gradeDate: '',
         grade: '',
         note: '',
         processControlScore:'',
         fqaControlScore:'',
         customerControlScore:'',
        },
      this.$refs.ruleForm.resetFields()
    },
    handleOk() {
      console.log('新增ok1')
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          console.log(this.dataType)
          // debugger
          let params = {
            pGuid: this.suppId,
            ratingType: this.dataType,
            ...this.form
          }
          if(this.model.id) {
            console.error('新增this.model.id',this.model.id)
            params['id'] = this.model.id
            updateDeliveryData(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.success('成功');
                    this.$emit("ok", this.dataType);
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }else{
            params.processControlScore = Number(params.processControlScore)
            params.fqaControlScore = Number(params.fqaControlScore)
            params.customerControlScore = Number(params.customerControlScore)
            console.log('新增params',params)
            addQualityRating(params)
                .then((res) => {
                    this.visible = false;
                    form.resetFields();
                    this.$message.success('成功');
                    this.$emit("ok",this.dataType);
                })
                .finally(() => {
                this.confirmLoading = false;
                });
          }
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/.ant-input{
  font-weight: 500;
  .ant-input:placeholder-shown{
  font-weight: 500;
}
}
</style>
