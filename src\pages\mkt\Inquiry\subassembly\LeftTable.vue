<!-- 市场管理 - 订单询价列表 -->
<template>
  <div ref="tableWrapper">
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :customRow="customRow"
      :scroll="{ y: 735, x: 1650 }"
      :pagination="pagination"
      :rowKey="rowKey"
      :loading="orderListTableLoading"
      @change="handleTableChange"
      :rowClassName="isRedRow"
    >
      <!-- :row-selection="{selectedRowKeys: selectedRowList, onChange: onSelectChange, }" -->
      <template slot="num" slot-scope="text, record, index">
        {{ (pagination.pageIndex - 1) * pagination.pageSize + parseInt(index) + 1 }}
      </template>

      <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
        <a style="color: black" :title="record.orderNo">{{ text }}</a
        >&nbsp;
        <span class="tagNum" style="display: inline-block">
          <span
            v-if="record.isJiaji"
            class="noCopy"
            style="font-size: 14px; font-weight: 500; color: #ff9900; padding: 0 2px; margin: 5px 4px 0 -10px; user-select: none"
            >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
          </span>
        </span>
      </div>
    </a-table>
    <right-copy ref="RightCopy" />
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
import { setEngineeringBack } from "@/utils/request";
import RightCopy from "@/pages/RightCopy";
import { downFileCAM } from "@/services/projectApi";
export default {
  props: {
    dataSource: {
      type: Array,
      require: true,
      default: () => [],
    },
    orderListTableLoading: {
      type: Boolean,
      require: true,
    },
    columns: {
      type: Array,
      require: true,
    },
    pagination: {
      type: Object,
      require: true,
      default: () => {
        return {
          pagination: {
            pageSize: 20,
            current: 1,
            total: 0,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
            showTotal: total => `总计 ${total} 条`,
          },
          selectedRows: {},
        };
      },
    },
    rowKey: {
      type: String,
      require: true,
    },
  },
  components: { RightCopy },
  name: "LeftTable",
  data() {
    return {
      isDragging: false,
      startIndex: -1,
      shiftKey: false,
      menuData: {},
      selectedRowKeysArray: [],
      selectedRowsData: [],
      selectedRowList: [],
      selectedRows: [],
      activeClass: "smallActive",
      proOrderId: "",
    };
  },
  watch: {
    pagination: {
      handler(val) {
        // console.log(val)
      },
    },
    // 'dataSource':{
    //   handler(val){
    //     if(val){
    //       this.$nextTick(function(){
    //         let obj =  document.getElementsByClassName('tagNum')
    //         let arr=[]
    //         for(let i=0;i<obj.length;i++){
    //           arr.push(obj[i].children.length)
    //         }
    //         let result = -Infinity;
    //         arr.forEach((item) => {
    //             if (item > result) {
    //                 result = item;
    //             }
    //         });
    //         if(result == 0){
    //           this.columns[1].width = '130px'
    //           this.columns[4].width = '400px'
    //         }
    //         if(result >= 1){
    //           this.columns[1].width = 130 + result*20 + 'px'
    //           this.columns[4].width = 400 - result*20 + 'px'
    //         }
    //       })
    //     }
    //   }
    // },
  },
  created() {
    // console.log(this.dataSource)
  },
  methods: {
    checkPermission,
    // 选择待收货订单列表
    onSelectChange(selectedRowKeys, selectedRows) {
      const newSelectedRowKeys = [],
        newSelectedRows = [];
      selectedRows.forEach(item => {
        newSelectedRowKeys.push(item.id);
        newSelectedRows.push(item);
      });
      this.selectedRowList = newSelectedRowKeys;
      this.selectedRows = newSelectedRows;
    },
    isRedRow(record) {
      let strGroup = [];
      if (record.id && this.selectedRowList.includes(record.id)) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },

    keydown(e) {
      // console.log('ctrlKey',e.ctrlKey,)
      this.shiftKey = e.ctrlKey;
    },
    handleMouseDown(event, record, index) {
      // window.addEventListener('keydown', this.keydown)
      // console.log('鼠标按下',this.shiftKey)
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      event.stopPropagation();
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        // 按住鼠标左键并拖动多选
        this.updateSelectedItems(this.startIndex, index);
        // console.log('鼠标拖动',event, record, index)
      }
    },
    handleMouseUp(event, record, index) {
      // console.log('鼠标离开',this.shiftKey, )
      this.isDragging = false;
      if (this.shiftKey) {
        let rowKeys = this.selectedRowList;
        if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
          rowKeys.splice(rowKeys.indexOf(record.id), 1);
        } else {
          rowKeys.push(record.id);
        }
        this.selectedRowList = rowKeys;
        if (this.selectedRowList.length == 1) {
          this.selectedRowsData = record;
          this.selectedRows = [record];
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRows = record;
          this.selectedRowList = [record.id];
        }
      }
      console.log(this.selectedRowList, "this.selectedRowList");
      this.proOrderId = this.selectedRowsData.id;
      this.$emit("getSelectedRowList", this.selectedRowList);
      this.shiftKey = false;
    },
    updateSelectedItems(startIndex, endIndex, sec) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.dataSource.slice(normalizedStart, normalizedEnd + 1);
      this.selectedRows = selectedRowsData;
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].id);
      }
      this.selectedRowList = arr;

      if (startIndex < endIndex) {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowList[this.selectedRowList.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.dataSource.filter(item => {
          return item.id == this.selectedRowList[0];
        })[0];
      }
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            this.menuData = record;
            this.$refs.RightCopy.rightClick(e, record);
          },
        },
      };
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let rowKeys = this.selectedRowList;
            if (rowKeys.length > 0 && rowKeys.includes(record.id)) {
              rowKeys.splice(rowKeys.indexOf(record.id), 1);
            } else {
              rowKeys.push(record.id);
            }
            this.selectedRowList = rowKeys;
            let rowData = this.selectedRows;
            if (rowData.length > 0 && rowData.includes(record)) {
              rowData.splice(rowData.indexOf(record), 1);
            } else {
              rowData.push(record);
            }
            this.selectedRows = rowData;
          },
        },
      };
    },
    handleTableChange(pagination) {
      this.$emit("tableChange", pagination);
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
  },
};
</script>

<style lang="less" scoped>
.tabRightClikBox {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 24px;
    line-height: 10px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
}
/deep/ .ant-table {
  .fontRed {
    td {
      color: #dc143c;
    }
  }

  .cookieIdColor {
    td:first-child {
      //border-left:2px solid #ff9900!important;
      background: #ff9900 !important;
    }

    //td:nth-child(2){
    //  color:#ff9900!important;
    //}
  }

  .rowBackgroundColor {
    background: #aba5a5;
  }

  .displayFlag {
    display: none;
  }
}

.peopleTag {
  margin: 0;
  padding: 0;
  width: 24px;
  border-radius: 12px;
  background: #2d221d;
  border-color: #2d221d;
  color: #ff9900;
  text-align: center;
  margin-left: 2px;
}
</style>
