<!--  - WIP查询主组件 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectmanagement" ref="tableWrapper">
      <div class="box" style="display: flex">
        <div class="leftContent" style="border: 2px solid rgb(233 230 230); border-bottom: none; overflow: auto" ref="leftstyle">
          <ul
            @click="liClick(item)"
            :class="[item.value == step ? 'licolor' : '']"
            v-for="(item, index) in mapKey(leftList)"
            :key="index"
            :value="item.value"
            :lable="item.lable"
          >
            <li>
              <span v-if="item.lable.indexOf('(') != -1">
                {{ item.lable.split("(")[0] }} (<span style="color: red">{{ item.lable.split("(")[1].split(")")[0] }}</span
                >)
              </span>
              <span v-else>{{ item.lable }}</span>
            </li>
          </ul>
        </div>
        <div class="rightContent" style="border-top: 2px solid rgb(233 230 230)" ref="mainstyle1">
          <a-table
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            :columns="columns1"
            @change="handleTableChange"
            :pagination="pagination"
            :data-source="orderListData"
            :scroll="{ y: 739, x: 1000 }"
            class="mainstyle"
            :customRow="onClickRow"
            :rowClassName="isclickcolor"
          >
            <template slot="down" slot-scope="text, record">
              <a-tooltip title="点击下载生产稿" v-if="record.steelDownload">
                <a-icon type="download" style="color: #428bca" @click="handleOk(record, 'icon')"></a-icon>
              </a-tooltip>
            </template>
            <template slot="downstackup" slot-scope="text, record">
              <a-tooltip title="点击下载叠层图" v-if="record.upDownload && record.stackImageAdress">
                <a-icon type="download" style="color: #428bca" @click="downimg(record.stackImageAdress, record)"></a-icon>
              </a-tooltip>
            </template>
            <template slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </template>
            <template slot="orderNo" slot-scope="text, record">
              <span :title="record.orderNo">{{ record.orderNo }}</span
              >&nbsp;
              <span class="tagNum" style="display: inline-block; height: 19px">
                <span v-if="record.isExtremeJiaji">
                  <span
                    style="
                      font-size: 14px;
                      font-weight: 500;
                      color: #ff9900;
                      padding: 0 2px;
                      margin: 0;
                      display: inline-block;
                      height: 19px;
                      width: 14px;
                      margin-right: 4px;
                      margin-left: -10px;
                      user-select: none;
                    "
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                  <span
                    style="
                      font-size: 14px;
                      font-weight: 500;
                      color: #ff9900;
                      padding: 0 2px;
                      margin: 0;
                      display: inline-block;
                      height: 19px;
                      width: 14px;
                      margin-right: 4px;
                      margin-left: -10px;
                      user-select: none;
                    "
                    >&nbsp;<a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </span>
                <a-tooltip title="加急" v-if="record.isJiaji">
                  <span
                    style="
                      font-size: 14px;
                      font-weight: 500;
                      color: #ff9900;
                      padding: 0 2px;
                      margin: 0;
                      display: inline-block;
                      height: 19px;
                      width: 14px;
                      margin-right: 4px;
                      margin-left: -10px;
                      user-select: none;
                    "
                    ><a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </a-tooltip>
                <a-tooltip title="新客户" v-if="record.isNewCust">
                  <a-tag
                    color="#2D221D"
                    style="
                      font-size: 12px;
                      background: #428bca;
                      color: white;
                      padding: 0 2px;
                      margin-left: 3px;
                      margin-right: 3px;
                      height: 21px;
                      user-select: none;
                      border: 1px solid #428bca;
                    "
                  >
                    新
                  </a-tag>
                </a-tooltip>
                <a-tag
                  v-if="record.isOrderModify"
                  style="
                    font-size: 12px;
                    background: #428bca;
                    color: white;
                    padding: 0 2px;
                    margin: 0;
                    margin-left: 3px;
                    height: 21px;
                    user-select: none;
                    border: 1px solid #428bca;
                  "
                >
                  修
                </a-tag>
                <a-tooltip title="生产通知单已下载">
                  <a-icon v-if="record.printProductionOrder" type="file-done" style="color: #ff9900; margin-left: 4px"></a-icon>
                </a-tooltip>
                <a-tooltip title="暂停" v-if="record.isLock">
                  <a-tag
                    color="#2D221D"
                    style="
                      font-size: 12px;
                      background: #428bca;
                      color: white;
                      padding: 0 2px;
                      margin-left: 3px;
                      margin-right: 3px;
                      height: 21px;
                      user-select: none;
                      border: 1px solid #428bca;
                    "
                  >
                    暂停
                  </a-tag>
                </a-tooltip>
              </span>
            </template>
          </a-table>
          <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
            <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
          </a-menu>
        </div>
      </div>
      <div class="bto">
        <a-button type="primary" class="bobu" @click="queryclick">查询(F)</a-button>
        <a-button type="primary" class="bobu" @click="downxlsx">导出</a-button>
        <a-button type="primary" class="bobu" @click="DataRefresh">数据刷新</a-button>
        <a-button type="primary" class="bobu" @click="Steelmesh">申请钢网</a-button>
      </div>
    </div>
    <!-- 查询弹窗 -->
    <a-modal
      title="订单查询"
      :visible="querydataVisible"
      @cancel="reportHandleCancel"
      @ok="queryhandleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form-model :modal="formdata">
        <a-form-model-item label="生产编号" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
          <a-input v-model="formdata.ProOrderNo" :autoFocus="true" allowClear />
        </a-form-model-item>
        <a-form-model-item label="客户型号：" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
          <a-input v-model="formdata.PcbFileName" allowClear />
        </a-form-model-item>
        <a-form-model-item label="订单状态" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
          <a-select v-model="formdata.Orderstatus" allowClear>
            <a-select-option value="1">已下线</a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!--确认弹窗-->
    <a-modal
      title="确认弹窗"
      :visible="modalvisible"
      @cancel="modalvisible = false"
      @ok="handleOk(selectrowdata, 'button')"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span>【{{ selectrowdata.pcbFileName }}】{{ information }}</span>
    </a-modal>
  </a-spin>
</template>
<script>
import moment from "moment";
import { mapState } from "vuex";
import {
  wipordermanegepagelist,
  serverworkfilepath,
  wipselectlist,
  workfilepath,
  wipdatalHDC,
  hasdownloadworkfile,
} from "@/services/usermanagement/index.js";
import { pcborderlistexportv2 } from "@/services/mkt/WipQurey";
let columns1 = [
  {
    title: "序号",
    key: "index",
    align: "center",
    scopedSlots: { customRender: "num" },
    width: 50,
  },
  {
    title: "预审编号",
    align: "left",
    ellipsis: true,
    width: 100,
    scopedSlots: { customRender: "orderNo" },
    sorter: (a, b) => {
      return a.orderNo.localeCompare(b.orderNo);
    },
  },
  {
    title: "生产编号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    width: 170,
    sorter: (a, b) => {
      return a.proOrderNo.localeCompare(b.proOrderNo);
    },
  },
  // {
  // title: "状态",
  // dataIndex: "statusStr",
  // align: "left",
  // ellipsis: true,
  // width:90,
  // },
  {
    title: "客编",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 60,
  },
  {
    title: "终端客户",
    dataIndex: "terminalCust",
    align: "left",
    ellipsis: true,
    width: 70,
  },
  {
    title: "客户型号",
    dataIndex: "pcbFileName",
    align: "left",
    ellipsis: true,
    width: 320,
    sorter: (a, b) => {
      return a.pcbFileName.localeCompare(b.pcbFileName);
    },
  },
  {
    title: "数量",
    dataIndex: "para4DelQty",
    width: 55,
    ellipsis: true,
    align: "left",
  },
  {
    title: "面积",
    dataIndex: "para4Area",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  // {
  // title: "总金额",
  // dataIndex: "totalAmountPrice",
  // width:95,
  // ellipsis: true,
  // align: "left",
  // },
  {
    title: "交货日期",
    dataIndex: "deliveryDate",
    align: "left",
    ellipsis: true,
    width: 95,
    sorter: (a, b) => {
      return a.deliveryDate.localeCompare(b.deliveryDate);
    },
  },
  {
    title: "工厂",
    dataIndex: "factoryName",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "工序",
    dataIndex: "selectValue",
    align: "left",
    ellipsis: true,
    width: 100,
  },
  {
    title: "备注",
    dataIndex: "memo",
    align: "left",
    ellipsis: true,
    width: 220,
  },
  {
    title: "作业员",
    dataIndex: "userName",
    align: "left",
    ellipsis: true,
    width: 90,
  },
  {
    title: "业务员",
    dataIndex: "ywName",
    align: "left",
    ellipsis: true,
    width: 90,
  },
  {
    title: "电话",
    dataIndex: "tel",
    align: "left",
    ellipsis: true,
    width: 110,
  },
  {
    title: "过序时间",
    dataIndex: "inDate",
    align: "left",
    ellipsis: true,
    width: 160,
    sorter: (a, b) => {
      return a.inDate.localeCompare(b.inDate);
    },
  },
  {
    title: "生产稿下载",
    align: "center",
    fixed: "right",
    ellipsis: true,
    width: 80,
    scopedSlots: { customRender: "down" },
  },
  {
    title: "叠层图下载",
    align: "center",
    fixed: "right",
    ellipsis: true,
    width: 80,
    scopedSlots: { customRender: "downstackup" },
  },
  {
    title: "已下载",
    align: "center",
    fixed: "right",
    ellipsis: true,
    width: 60,
    customRender: (record, index) => (record.hasDownLoadWorkFile == true ? "是" : ""),
  },
];
export default {
  name: "",
  components: {},
  data() {
    return {
      isCtrlPressed: false,
      querydataVisible: false,
      modalvisible: false,
      information: "",
      selectrowdata: {},
      step: "0",
      id: "",
      leftList: [],
      spinning: false,
      orderListData: [],
      queryData: {},
      columns1,
      showText: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      formdata: {},
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      user: {},
    };
  },
  computed: {
    ...mapState("account", ["userinfo"]),
  },
  created() {
    if (this.userinfo) {
      this.user = this.userinfo;
    } else {
      this.user = JSON.parse(localStorage.getItem("UserInformation"));
    }
    this.$nextTick(() => {
      this.handleResize();
      this.getOrderListData();
      this.getleftlisit();
    });
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.handleResize, true);
  },
  mounted() {
    document.title = "EMS | WIP查询";
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.handleResize, true);
  },
  watch: {
    orderListData: {
      handler(val) {
        if (val) {
          this.$nextTick(function () {
            let obj = document.getElementsByClassName("tagNum");
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
              arr.push(obj[i].children.length);
            }
            let result = -Infinity;
            arr.forEach(item => {
              if (item > result) {
                result = item;
              }
            });
            if (result == 0) {
              this.columns1[1].width = "100px";
              this.columns1[5].width = "300px";
            }
            if (result >= 1) {
              this.columns1[1].width = 100 + result * 35 + "px";
              this.columns1[5].width = 300 - result * 25 + "px";
            }
          });
        }
      },
    },
  },
  methods: {
    moment,
    downfile(val, record) {
      let a = val.substring(val.lastIndexOf("."));
      const xhr = new XMLHttpRequest();
      xhr.open("GET", val, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = record.proOrderNo + "(" + record.pcbFileName + ").zip";
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          hasdownloadworkfile(record.proOrderId).then(res => {
            if (res.code) {
              record.hasDownLoadWorkFile = true;
            }
          });
        }
      };
      xhr.send();
    },
    downimg(val, record) {
      const xhr = new XMLHttpRequest();
      xhr.open("GET", val, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = record.proOrderNo + "(" + record.pcbFileName + ").png";
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          hasdownloadworkfile(record.proOrderId).then(res => {
            if (res.code) {
              record.hasDownLoadWorkFile = true;
            }
          });
        }
      };
      xhr.send();
    },
    reportHandleCancel() {
      this.querydataVisible = false;
    },
    handleResize() {
      var mainstyle = document.getElementsByClassName("mainstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var mainstyle1 = document.getElementsByClassName("mainstyle")[0].children[0].children[0].children[0].children[0].children[1].children[1];
      if (mainstyle && this.orderListData.length != 0) {
        if (window.innerHeight - 153 < 756) {
          mainstyle.style.height = window.innerHeight - 172 + "px";
          mainstyle1.style.height = window.innerHeight - 172 + "px";
        } else {
          mainstyle.style.height = "739px";
          mainstyle1.style.height = "739px";
        }
      } else {
        mainstyle.style.height = 0;
        mainstyle1.style.height = 0;
      }
      var rightContent = document.getElementsByClassName("rightContent")[0];
      if (this.orderListData.length != 0) {
        if (window.innerHeight - 153 < 756) {
          rightContent.style.height = window.innerHeight - 153 + "px";
        } else {
          rightContent.style.height = "776px";
        }
      } else {
        rightContent.style.height = 0;
      }
      if (window.innerHeight - 153 < 756) {
        this.$refs.leftstyle.style.height = window.innerHeight - 137 + "px";
      } else {
        this.$refs.leftstyle.style.height = "776px";
      }
      this.$refs.leftstyle.style.width = "200px";
      this.$refs.mainstyle1.style.width = window.innerWidth - 374 + "px";
      let elements = document.getElementsByClassName("bobu");
      let buttonwidth = elements.length * 110;
      let footerwidth = window.innerWidth - 170;
      let paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      let paginationwidth = paginnum * 40 + 380;
      let paginationwidth1 = paginnum * 22 + 336;
      let paginationwidth2 = 140;
      if (buttonwidth + paginationwidth < footerwidth) {
        this.pagination.size = "default";
        this.pagination.simple = false;
        this.buttonsmenu = false;
      } else if (buttonwidth + paginationwidth > footerwidth && buttonwidth + paginationwidth1 < footerwidth) {
        this.pagination.size = "small";
        this.pagination.simple = false;
        this.buttonsmenu = false;
      } else {
        this.pagination.simple = true;
        this.buttonsmenu = false;
        if (buttonwidth + paginationwidth2 > footerwidth) {
          this.buttonsmenu = true;
        } else {
          this.buttonsmenu = false;
        }
      }
    },
    getleftlisit(data) {
      let params = {};
      if (data) {
        params = data;
        params.PcbFileName = data.PcbFileName ? data.PcbFileName.replace(/\s+/g, " ").trim() : "";
      }
      params.ClientLoginKey = this.user.clientLoginKey;
      wipselectlist(params).then(res => {
        if (res.code) {
          this.leftList = res.data;
        }
      });
    },
    getOrderListData(queryData) {
      this.spinning = true;
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
        No: this.step,
        ClientLoginKey: this.user.clientLoginKey,
      };
      var obj = Object.assign(params, queryData);
      obj.PcbFileName = obj.PcbFileName ? obj.PcbFileName.replace(/\s+/g, " ").trim() : "";
      wipordermanegepagelist(obj)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            setTimeout(() => {
              this.handleResize();
            });
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    mapKey(data) {
      if (!data) {
        return [];
      } else {
        return Object.keys(data).map(item => {
          return { value: item, lable: data[item] };
        });
      }
    },
    isclickcolor(record) {
      let strGroup = [];
      if (record.id && record.id == this.id) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.id = record.id;
            this.selectrowdata = record;
          },
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.rightClick1(e, text, record);
          },
        },
      };
    },
    rightClick1(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode;
      }
      if (e.target.localName == "path" || e.target.localName == "svg") {
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode;
        if (event.className.indexOf("tagNum") != -1) {
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
        }
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split(" ")[0].trim();
      }
      const tableWrapper = this.$refs.tableWrapper;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;
      if (event.cellIndex == this.columns1.length - 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    down() {
      let input = document.createElement("input");
      input.value = this.text;
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    DataRefresh() {
      this.spinning = true;
      wipdatalHDC(this.user.custNo).then(res => {
        if (res.code) {
          this.$message.success("刷新成功");
          this.getOrderListData();
          this.getleftlisit();
        } else {
          this.$message.error(res.message);
          this.spinning = false;
        }
      });
    },
    Steelmesh() {
      if (!this.id) {
        this.$message.error("请选择订单");
        return;
      }
      if (!this.selectrowdata.proOrderId) {
        this.$message.error("该订单无法申请钢网");
        return;
      }
      this.information = "确认申请钢网吗?";
      this.modalvisible = true;
    },
    handleOk(data, type) {
      this.modalvisible = false;
      let val = data;
      if (val.workFilePath && val.joinFactoryId != 12 && type == "icon") {
        this.downfile(val.workFilePath, val);
      } else {
        serverworkfilepath(val.proOrderId).then(res => {
          if (res.code) {
            this.$message.success(res.message);
            if (res.data && res.data != "") {
              this.downfile(res.data, val);
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    downxlsx() {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
        No: this.step,
        clientLoginKey: this.user.clientLoginKey,
      };
      pcborderlistexportv2(params).then(res => {
        this.downloadExcel(res);
      });
    },
    downloadExcel(res) {
      const blob = new Blob([res], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
      const url = window.URL.createObjectURL(blob);
      // 创建<a>标签
      const a = document.createElement("a");
      a.href = url;
      // 从Content-Disposition中获取文件名
      const contentDisposition = res["content-disposition"];
      let fileName = "WIP信息.xlsx";
      if (contentDisposition) {
        const fileNameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const matches = fileNameRegex.exec(contentDisposition);
        if (matches != null && matches[1]) {
          fileName = matches[1].replace(/['"]/g, "");
        }
      }
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    },
    queryclick() {
      this.formdata = {};
      this.querydataVisible = true;
    },
    queryhandleOk() {
      this.queryData = this.formdata;
      this.querydataVisible = false;
      this.step = 0;
      this.getOrderListData(this.queryData);
      this.getleftlisit(this.queryData);
    },
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      if (JSON.stringify(this.queryData) != "{}") {
        this.getOrderListData(this.queryData);
        this.getleftlisit(this.queryData);
      } else {
        this.getOrderListData();
        this.getleftlisit();
      }
    },
    liClick(record) {
      if (record.value) {
        this.step = record.value;
      } else {
        this.step = 0;
      }
      this.pagination.current = 1;
      this.getOrderListData(this.queryData);
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryclick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.querydataVisible) {
        this.queryhandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-table-fixed-right {
  min-height: 36px;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-table-pagination.ant-pagination {
  float: left;
  margin-left: 10px;
  position: fixed;
  font-size: small;
  margin: 11px 7px;
}
/deep/.ant-table-thead > tr > th .ant-table-column-sorter {
  display: none !important;
  vertical-align: middle;
}
/deep/.min-table {
  border: 1px solid #efefef;
  .ant-table-body {
    min-height: 400px;
  }
}
/deep/.ant-menu-item:hover {
  color: rgb(22, 22, 22);
}
/deep/.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #ffffff;
  color: black;
}
.tabRightClikBox {
  li {
    height: 24px;
    line-height: 20px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/ .ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
  .rowBackgroundColor {
    background: #dfdcdc !important;
  }
}
/deep/.ant-table-thead > tr > th {
  padding: 5px 4px;
  overflow-wrap: break-word;
  height: 36px;
  border-right: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
  padding: 5px 4px;
  height: 36px;
  overflow-wrap: break-word;
  border-right: 1px solid #efefef;
}
.projectmanagement {
  user-select: normal !important;
  .box {
    border-top: none !important;
    border: 2px solid rgb(233, 233, 240);
    background: #ffffff;
    .leftContent {
      ul {
        margin-bottom: 0;
        padding-left: 0;
      }
      ul li {
        list-style: none;
        margin: 0;
        border-bottom: 1px solid rgb(233 230 230);
        padding-left: 10px;
        font-size: 14px;
        height: 36px;
        line-height: 36px;
      }
      li:hover {
        color: white;
        font-weight: 600;
        font-size: 16px;
        background-color: #ff9900;
      }
      /deep/ .licolor li {
        color: white;
        font-weight: 600;
        font-size: 16px;
        background-color: #ff9900;
      }
      &::-webkit-scrollbar {
        //整体样式
        width: 4px; //y轴滚动条粗细
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        //滑动滑块条样式
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        background: #ff9900;
        // #fff9e6
      }
      overflow-y: scroll;
      /deep/ .ant-list-header {
        padding: 5px 24px;
        text-align: center;
        background-color: #f0f0f0;
      }
      .ant-list-item {
        padding: 5px 12px;
      }
    }
  }
  .bto {
    height: 48px;
    background: #ffffff;
    border: 2px solid #e9e9f0;
    .bobu {
      margin: 7px;
      float: right;
    }
  }

  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: rgb(223 220 220);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 12px 0 0 -185px;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
