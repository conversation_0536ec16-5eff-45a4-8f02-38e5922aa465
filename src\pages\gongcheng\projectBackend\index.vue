<!-- 工程管理 - 工程后端 -->
<template>
  <a-spin :spinning="spinning">
    <div class="projectBackend" style="position: relative">
      <span style="position: absolute; top: -4%; right: 0%; color: #ff9900; font-size: 16px">{{ showNote }}</span>
      <div style="width: 100%; display: flex">
        <div
          class="leftContent"
          style="user-select: none; position: relative"
          ref="tableWrapper"
          :class="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendPersonOrderInfo') ? 'left1' : 'left2'"
        >
          <left-table
            :columns="columns1"
            :data-source="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'proOrderId'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            @getOrderDetail="getOrderDetail"
            @getprocessstepnotes="getprocessstepnotes"
            @jigsawPuzzleClick="jigsawPuzzleClick"
            @ChargebackClick="ChargebackClick"
            @modifyInfoClick1="modifyInfoClick1"
            @viewLogClick="viewLogClick"
            @webSocketLink="webSocketLink"
            @CustomerRulesClick="CustomerRulesClick"
            @modifyInfoClick="modifyInfoClick"
            @StatusSynchronization="StatusSynchronization"
            @RepairRecordClick="RepairRecordClick"
            @rightClick1="rightClick1"
            ref="orderTable"
            :cookieId="cookieId"
            class="leftstyle"
          >
            <span slot="num" slot-scope="text, record, index">
              {{ (pagination.current - 1) * pagination.pageSize + parseInt(index) + 1 }}
            </span>
          </left-table>
          <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
            <!--          <a-menu-item @click="down0()">下载文件</a-menu-item>-->
            <a-menu-item @click="down" v-if="showText">复制</a-menu-item>
            <a-menu-item @click="down1">下载CAM文件</a-menu-item>
          </a-menu>
        </div>
        <div class="rightContent">
          <!-- <a-collapse 
        :activeKey="1"
        @change="CollapseList"
        :expandIcon="expandIcon1" >
        <a-collapse-panel key="1">
      
        </a-collapse-panel>
        </a-collapse> -->
          <div class="centerTable">
            <a-tabs type="card" @change="tabpaneChange">
              <a-tab-pane
                key="1"
                tab="人员订单信息"
                v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendPersonOrderInfo')"
              >
                <order-detail
                  :columns="columns3"
                  :data-source="peopleOrderInfoList"
                  :orderDetailTableLoading="peopleOrderInfoTableLoading"
                  :rowKey="'userNo'"
                  @getPeopleOrderList="getPeopleOrderList"
                  @saveErpKey="saveErpKey"
                  @OrderRetrievalSettingsClick="OrderRetrievalSettingsClick"
                  ref="peopleOrder"
                  class="centerstyle"
                >
                </order-detail>
              </a-tab-pane>
            </a-tabs>
          </div>
          <div class="rightTable">
            <!-- <div class="note" v-viewer>
            <div class="textNote"  v-html="allNote"></div>
          </div> -->
            <!-- <div  class="jobNote" ref="tableWrapper1">
            <a-table
                :columns="columns4"
                :customRow="onClickRow1"
                :dataSource="jobData"
                :pagination="false"
                rowKey="id"
                :scroll="{y: 218}"
                :loading="jobTableLoading"
                :class="jobData.length ? 'minTable':''"
            >
              <template slot="picUrl" slot-scope="text,record">
                <viewer :images="picFilter(record)">
                  <img :src="item" v-for="(item,index) in picFilter(record)" :key="index" style="width: 20px; height: 20px"/>
                </viewer>
              </template>
              <template slot="fileUrl" slot-scope="text,record">
                <a-tooltip title="编辑" >
                <a-icon type="edit"  style="color:rgb(0, 0, 204); " @click="editClick(record)"></a-icon>
                </a-tooltip>
                <span  style="color:rgb(0, 0, 204)">/</span>
                <a-tooltip title="下载附件" >
                <a-icon type="download" v-if="record.fileUrl" style="color:rgb(0, 0, 204); " @click="downFile(record.fileUrl)"></a-icon>
                </a-tooltip>
                <span v-if="record.fileUrl" style="color:rgb(0, 0, 204)">/</span>
                <a-tooltip title="删除" >
                <a-icon type="close-square" style="color:rgb(0, 0, 204)" @click="delFile(record)"></a-icon>
                </a-tooltip>
              </template>
            </a-table>
            <a-menu :style="menuStyle1" v-show="menuVisible1" class="tabRightClikBox1">
              <a-menu-item @click="down2" v-if="showText1">复制</a-menu-item>
            </a-menu> 
            <div  class="pronotes" style='user-select: all;'>
          </div>            
          </div> -->
            <div
              class="jobNote1"
              style="border-top: 1px solid #f1f1f1"
              v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendPersonOrderInfo')"
            >
              <a-table
                :columns="columns5"
                :dataSource="peopleOrderListData"
                :pagination="false"
                rowKey="proOrderId"
                class="rightstyle"
                :scroll="{ y: 738, x: 400 }"
                :customRow="onClickRow"
                :rowClassName="isRedRow"
                :loading="peopleOrderListTableLoading"
              >
                <div slot="score" slot-scope="text, record">
                  <a-tooltip :title="xstitle">
                    <span v-if="record.score" style="color: #428bca" @mouseover="Coefficientdetails(record.proOrderId)">{{
                      record.score.toFixed(2)
                    }}</span>
                  </a-tooltip>
                </div>
                <template slot="action" slot-scope="text, record" v-if="record.state_ != '已回传'">
                  <a-tooltip title="回退订单" v-if="checkPermission('MES.EngineeringModule.EngineeringBackend.EngineeringBackendBack')">
                    <a-icon type="rollback" style="color: #ff9900; font-size: 18px" @click="assignBackClick(record)" />
                  </a-tooltip>
                </template>
                <div slot="orderNo" slot-scope="text, record" style="display: flex; align-items: center">
                  <a style="color: black" :title="record.orderNo">{{ text }}</a
                  >&nbsp;
                  <span style="color: #ff9900; padding: 0; margin-left: -10px; user-select: none" v-if="record.isJiaji">
                    &nbsp; <a-icon type="thunderbolt" theme="filled"></a-icon>
                  </span>
                </div>
                <template slot="customRender" slot-scope="text, record" style="display: flex; align-items: center">
                  <template>
                    {{ text }}
                  </template>
                  <a-tooltip title="二次投产">
                    <a-tag
                      v-if="record.backUserAccount > 0"
                      style="
                        font-size: 12px;
                        background: #428bca;
                        color: white;
                        padding: 0 2px;
                        margin: 0;
                        margin-right: 3px;
                        height: 21px;
                        user-select: none;
                        border: 1px solid #428bca;
                      "
                    >
                      二
                    </a-tag>
                  </a-tooltip>
                  <a-tag
                    v-if="record.isEQ == 1 && record.state_ != '问客已回复' && record.state_ != '问客已审核' && record.state_ != '问客'"
                    color="#2D221D"
                    class="noCopy"
                    style="
                      font-size: 12px;
                      background: #428bca;
                      color: white;
                      padding: 0 2px;
                      margin: 0;
                      margin-right: 3px;
                      height: 21px;
                      user-select: none;
                      border: 1px solid #428bca;
                    "
                  >
                    问
                  </a-tag>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
      <div class="footerAction" style="user-select: none">
        <backend-action
          :assignLoading="assignLoading"
          :total="pagination.total"
          @assignClick="assignClick"
          @queryClick="queryClick"
          @StackImpedanceClick="StackImpedanceClick"
          @wenkeClick="wenkeClick"
          @backendcompletion="backendcompletion"
          @RepairCompletedClick="RepairCompletedClick"
          @TakeOrderClick="TakeOrderClick"
          @RegisterClick="RegisterClick"
          @MarketmodClick="MarketmodClick"
          @PerformanceClick="PerformanceClick"
          @FallbackFrontClick="FallbackFrontClick"
          @Confirmmodification="Confirmmodification"
          ref="action"
        />
      </div>
      <a-modal title="拼版图" :visible="makeupVisible" :footer="null" @cancel="handleCancel">
        <makeup-pic ref="makeup"></makeup-pic>
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <query-info-backend ref="queryInfo" />
      </a-modal>
      <!--市场修改弹窗 -->
      <a-modal
        title="市场修改"
        :visible="dataVisible13"
        @cancel="reportHandleCancel"
        @ok="handleOk13"
        ok-text="确定"
        cancel-text="取消"
        destroyOnClose
        :maskClosable="false"
        :width="610"
        :confirmLoading="confirmLoading"
        centered
      >
        <market-back ref="MarketBack" />
      </a-modal>
      <!-- 修改信息 -->
      <a-modal
        title="Vcut"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
      >
        <modify-info-make ref="ModifyInfoMake" :selectedRowsData="selectedRowsData" :factoryList="factoryList" />
      </a-modal>
      <!-- 退单弹窗 回退业务 -->
      <a-modal
        title="回退订单"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        ok-text="确定"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <chargeback-make ref="ChargebackMake" />
      </a-modal>
      <a-modal :title="meslist" :visible="checkdataVisible" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
        <template slot="footer">
          <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
          <a-button @click="reportHandleCancel">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
            <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
            <span v-else style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}</span>
            </span>
          </p>
          <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
        </div>
      </a-modal>
      <!-- 取单设置 -->
      <a-modal
        title="取单设置"
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="640"
        :confirmLoading="confirmLoading"
      >
        <order-retrieval-settings ref="OrderRetrievalSettings" :data="data" :peopleOrderInfoList="peopleOrderInfoList" />
      </a-modal>
      <!-- 返修记录弹窗 -->
      <a-modal
        title="返修记录"
        :visible="dataVisible6"
        @cancel="reportHandleCancel"
        @ok="handleOk6"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        :confirmLoading="confirmLoading"
      >
        <repair-record-info ref="RepairRecord" :RepairRecordData="RepairRecordData" />
      </a-modal>
      <!-- 客户规则弹窗 -->
      <a-modal
        title="客户规则"
        :visible="dataVisible7"
        @cancel="reportHandleCancel"
        @ok="handleOk7"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="960"
        :confirmLoading="confirmLoading"
      >
        <customer-rules-info ref="CustomerRules" :CustomerData="CustomerData" />
      </a-modal>
      <!-- 日志弹窗 -->
      <a-modal
        title="日志"
        :visible="dataVisible8"
        @cancel="reportHandleCancel"
        @ok="handleOk8"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="450"
        :confirmLoading="confirmLoading"
      >
        <view-log-info ref="viewLogInfo" :viewLogData="viewLogData" />
      </a-modal>
      <!-- 回退前端 -->
      <a-modal
        title="回退订单"
        :visible="dataVisible9"
        @cancel="reportHandleCancel"
        @ok="handleOk9"
        ok-text="确定"
        cancel-text="取消"
        centered
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <fallback-front-info ref="FallbackFrontInfo" />
      </a-modal>
      <!-- 制作完成 -->
      <a-modal
        title="CAM参数设置"
        :visible="dataVisible10"
        @cancel="reportHandleCancel"
        @ok="handleOk10"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <production-completed ref="ProductionCompleted" :ParameterData2="ParameterData2" :code="code" />
      </a-modal>
      <!-- 分派信息弹窗 -->
      <a-modal
        title="分派信息"
        :visible="assignModalVisible"
        @cancel="reportHandleCancel"
        @ok="assignhandleOk"
        ok-text="确定"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        centered
      >
        <h3 style="font-weight: 500; color: #000000; font-size: 14px">是否将订单号为：</h3>
        <div class="tabBox">
          <a-tag color="orange" v-for="(item, index) in assignOrderList1" :key="index + '_assign'"> {{ item }} </a-tag>
        </div>
        <div style="font-weight: 500; font-size: 14px; color: #000000">分派给：{{ nname }}</div>
      </a-modal>
      <productRemark
        @handleCancelRemark="handleCancelRemark"
        @handleOkRemark="handleOkRemark"
        :orderNoRemark="orderNoRemark"
        :dataRemark="dataRemark"
        :flowSelect="flowSelect"
        :visible="visibleRemark"
        :confirmLoading="confirmLoadingRemark"
      >
      </productRemark>
      <a-modal
        title=" 确认弹窗"
        :visible="dataVisibleMode"
        @cancel="reportHandleCancel"
        @ok="handleOkMode"
        :confirmLoading="confirmLoadingMode"
        ok-text="确定"
        cancel-text="取消(C)"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        centered
      >
        <span style="font-weight: 500; font-size: 14px; color: #000000">【{{ orderno }}】</span>
        <span style="font-weight: 500; font-size: 14px; color: #000000">{{ messageMode }}</span>
      </a-modal>
      <!-- 绩效管理 -->
      <performance ref="performanceModal" :selectedRowsData="selectedRowsData" :type="1"> </performance>
      <!-- 注意事项 -->
      <a-modal
        title="信息传递"
        :visible="dataVisibleMatter"
        @cancel="reportHandleCancel"
        @ok="handleOkMatter"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="550"
        :confirmLoading="confirmLoading"
        centered
      >
        <matters-needing-attention-info ref="mattersNeedingAttention" :editData="editData" />
      </a-modal>
    </div>
  </a-spin>
</template>

<script>
import { proordercoefficient } from "@/services/projectDisptch";
import { ppebuttonCheck, setsureordermodify } from "@/services/projectMake/index.js";
import { setEngineeringBack } from "@/utils/request";
import { checkPermission } from "@/utils/abp";
import {
  peopleOrderList,
  projectBackEndAssign,
  projectBackEndAssignBack,
  projectBackEndOrderDetail,
  projectBackEndOrderList,
  projectBackEndPeopleList,
  projectBackEndJobInfo,
  getModifyInformation,
  BackStart,
  FallbackFront,
  mattersNeedingAttention,
  getRepairRecord,
  TakeOrderList,
  DownloadCAMFile,
  orderRetrievalSettings,
  RetrievalSettings,
  makeInfo,
  getFlowSelect,
  postProcess,
  toCam,
} from "@/services/projectApi";
import {
  finish,
  getFactoryList,
  modifyPar2,
  SaveParameter,
  stateSync,
  proOrderInfo,
  Marketmake,
  repierMatters,
  getCustomerInfo,
} from "@/services/projectMake";
import {
  getStackImpedance,
  getViewLog,
  getWenkeUrl,
  RepairCompleted,
  delInfo,
  getJobAutoInfo,
  processstepnotes,
  BackMakeinish,
} from "@/services/projectMake";
import LeftTable from "@/pages/gongcheng/projectBackend/subassembly/LeftTable";
import BackendAction from "@/pages/gongcheng/projectBackend/subassembly/BackendAction";
import QueryInfoBackend from "@/pages/gongcheng/projectBackend/subassembly/QueryInfoBackend.vue";
import OrderRetrievalSettings from "@/pages/gongcheng/projectBackend/subassembly/OrderRetrievalSettings";
import FallbackFrontInfo from "@/pages/gongcheng/projectBackend/subassembly/FallbackFrontInfo.vue";
import MarketBack from "@/pages/gongcheng/projectBackend/subassembly/MarketBack";
import productRemark from "@/pages/gongcheng/projectMake/subassembly/productRemark";
import OrderDetail from "@/pages/gongcheng/projectMake/subassembly/OrderDetail";
import MakeupPic from "@/pages/gongcheng/projectMake/subassembly/MakeupPic";
import ChargebackMake from "@/pages/gongcheng/projectMake/subassembly/ChargebackMake";
import ModifyInfoMake from "@/pages/gongcheng/projectMake/subassembly/ModifyInfoMake";
import mattersNeedingAttentionInfo from "@/pages/gongcheng/projectMake/subassembly/mattersNeedingAttentionInfo";
import RepairRecordInfo from "@/pages/gongcheng/projectMake/subassembly/RepairRecordInfo.vue";
import CustomerRulesInfo from "@/pages/gongcheng/projectMake/subassembly/CustomerRulesInfo.vue";
import viewLogInfo from "@/pages/gongcheng/projectMake/subassembly/viewLogInfo";
import ProductionCompleted from "@/pages/gongcheng/projectMake/subassembly/ProductionCompleted";
import Performance from "@/pages/gongcheng/projectMake/subassembly/Performance";
// import Cookie from "_js-cookie@2.2.1@js-cookie";
import Cookie from "js-cookie";
import moment from "moment";
const columns1 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    fixed: "left",
    scopedSlots: { customRender: "num" },
    width: 40,
  },
  {
    title: "生产编号",
    dataIndex: "orderNo",
    align: "left",
    fixed: "left",
    ellipsis: true,
    width: 120,
    className: "userStyle",
    scopedSlots: { customRender: "orderNo" },
    sorter: (a, b) => {
      return a.orderNo.localeCompare(b.orderNo);
    },
  },

  {
    title: "订单状态",
    dataIndex: "statusType",
    align: "left",
    ellipsis: true,
    width: 100,
    sorter: (a, b) => {
      return a.statusType.localeCompare(b.statusType);
    },
  },
  {
    title: "订单类型",
    dataIndex: "isReOrder",
    align: "left",
    scopedSlots: { customRender: "isReOrder" },
    ellipsis: true,
    width: 90,
    sorter: (a, b) => {
      return a.isReOrder.toString().localeCompare(b.isReOrder.toString());
    },
  },

  {
    title: "后端制作",
    dataIndex: "backEndRealName",
    width: 90,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      return a.backEndRealName.localeCompare(b.backEndRealName);
    },
  },

  {
    title: "交期",
    dataIndex: "deliveryDate",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 135,
    sorter: (a, b) => {
      return a.deliveryDate.localeCompare(b.deliveryDate);
    },
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "left",
    ellipsis: true,
    width: 50,
    sorter: (a, b) => {
      return a.boardLayers - b.boardLayers;
    },
  },
  {
    title: "客规",
    width: 80,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "isCustRule" },
  },
  {
    title: "直通",
    // customRender: (text,record,index) => `${record.autoType ? '' : '直通'}`,
    scopedSlots: { customRender: "autoType" },
    dataIndex: "autoType",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: "前端",
    scopedSlots: { customRender: "down" },
    className: "noCopy",
    width: 50,
    ellipsis: true,
    align: "center",
  },
  {
    title: "EQ完耗时(h)",
    width: 90,
    scopedSlots: { customRender: "eqCostTime" },
    sorter: (a, b) => {
      return a.eqCostTime - b.eqCostTime;
    },
    ellipsis: true,
    align: "left",
  },
  {
    title: "拼版图",
    dataIndex: "jigsawPuzzle",
    align: "left",
    ellipsis: true,
    width: 140,
    scopedSlots: { customRender: "jigsawPuzzle" },
  },
  {
    title: "前端制作",
    dataIndex: "frontEndRealName",
    width: 70,
    ellipsis: true,
    align: "left",
    sorter: (a, b) => {
      return a.frontEndRealName.localeCompare(b.frontEndRealName);
    },
  },
  {
    title: "面积",
    dataIndex: "boardArea",
    align: "left",
    ellipsis: true,
    width: 40,
    sorter: (a, b) => {
      return a.boardArea - b.boardArea;
    },
  },
  {
    title: "厚度",
    dataIndex: "boardThickness",
    align: "left",
    ellipsis: true,
    width: 50,
    sorter: (a, b) => {
      return a.boardThickness - b.boardThickness;
    },
  },
  {
    title: "服务耗时",
    dataIndex: "taskCostTime",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "剩余时间(h)",
    dataIndex: "timeOut",
    width: 90,
    ellipsis: true,
    align: "left",
  },
  {
    title: "前端完成时间",
    dataIndex: "miEndDate_",
    width: 130,
    ellipsis: true,
    align: "left",
  },
  {
    title: "下单时间",
    dataIndex: "createTime",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 130,
  },
  {
    title: "派单时间",
    dataIndex: "camSendDate",
    align: "left",
    // fixed:'left',
    ellipsis: true,
    width: 135,
  },
  {
    title: "系数",
    scopedSlots: { customRender: "score" },
    align: "left",
    width: 60,
  },
  {
    title: "加工工厂",
    dataIndex: "orderChannel",
    width: 75,
    ellipsis: true,
    align: "left",
  },
  {
    title: "预审人",
    dataIndex: "preName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "创建人",
    dataIndex: "createName",
    className: "userStyle",
    align: "left",
    ellipsis: true,
    width: 80,
  },
  {
    title: "原稿",
    scopedSlots: { customRender: "down2" },
    className: "noCopy",
    width: 70,
    ellipsis: true,
    align: "center",
  },
  {
    title: "MI",
    scopedSlots: { customRender: "process" },
    align: "center",
    fixed: "right",
    width: 40, // 2290
  },
  {
    title: "指示",
    scopedSlots: { customRender: "action1" },
    align: "center",
    fixed: "right",
    width: 40, // 2290
  },
];
const columns4 = [
  {
    title: "操作人",
    dataIndex: "inUserName",
    width: 82,
    ellipsis: true,
    align: "left",
  },
  {
    title: "创建时间",
    dataIndex: "indate",
    width: 95,
    ellipsis: true,
    align: "left",
  },
  {
    title: "备注信息",
    dataIndex: "conent",
    // width: '23%',
    ellipsis: true,
    align: "left",
  },
  {
    title: "图片",
    width: "15%",
    scopedSlots: { customRender: "picUrl" },
    align: "left",
  },
  {
    title: "操作",
    scopedSlots: { customRender: "fileUrl" },
    className: "noCopy",
    align: "left",
    width: 60,
  },
];
const columns5 = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 45,
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "本厂编码",
    dataIndex: "orderNo",
    width: 125,
    ellipsis: true,
    align: "left",
    className: "userStyle",
    scopedSlots: { customRender: "orderNo" },
  },
  {
    title: "状态",
    dataIndex: "state_",
    ellipsis: true,
    align: "left",
    width: 130,
    scopedSlots: { customRender: "customRender" },
  },
  {
    title: "派单时间",
    dataIndex: "camStartDate",
    width: 98,
    ellipsis: true,
    align: "left",
  },
  {
    title: "系数",
    scopedSlots: { customRender: "score" },
    align: "left",
    width: 60,
  },
  {
    title: "加工工厂",
    dataIndex: "facName",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "操作",
    width: 40,
    ellipsis: true,
    align: "left",
    scopedSlots: { customRender: "action" },
  },
];
const columns6 = [
  {
    title: "订单号",
    dataIndex: "orderNo_",
    width: "25%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "类型",
    dataIndex: "type_",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "状态",
    dataIndex: "status_",
    width: "15%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "时间",
    dataIndex: "createTime",
    width: "30%",
    align: "left",
  },
];
const columns7 = [
  {
    title: "工序",
    dataIndex: "codeName",
    width: "23%",
    ellipsis: true,
    align: "left",
  },
  {
    title: "备注",
    dataIndex: "note",
    width: "70%",
    ellipsis: true,
    align: "left",
  },
];
const projectArray = [
  { name: "工厂编号", value: "orderNo" },
  { name: "层数", value: "boardLayers" },
  { name: "板材信息", value: "fR4TypeStr" },
  { name: "板材型号", value: "boardBrandStr" },
  { name: "成品板厚", value: "boardThickness" },
  // {name:'内/外完成铜厚',value:'copperThicknessStr'},
  { name: "铜厚(外)oz", value: "copperThicknessStr" },
  { name: "铜厚(内)oz", value: "innerCopperThicknessStr" },
  { name: "阻焊颜色(顶)/(底)", value: "solderColorStr" },
  // {name:'阻焊颜色(底)',value:'solderColorBottom'},
  { name: "字符颜色(顶)/(底)", value: "fontColorStr" },
  // {name:'字符颜色(底)',value:'fontColorBottom'},
  { name: "表面处理", value: "surfaceFinishStr" },
  { name: "过孔处理", value: "solderCoverStr" },
  { name: "单元尺寸(长X宽)", value: "boardHeight" },
  { name: "成品尺寸(长X宽)", value: "boardHeightSet" },
  { name: "出货类型", value: "pinBanType" },
  { name: "测试方式", value: "flyingProbeStr" },
  { name: "文件名", value: "pcbFileName" },
  { name: "拼版数量", value: "pinBanNum" },
  { name: "电镀工艺", value: "beforePlating" },
  { name: "图形转移工艺", value: "imageTranster" },
  { name: "最小孔", value: "vias" },
  { name: "线宽线距(mil)", value: "lineWeight" },
  { name: "订单数量", value: "num" },
  // {name:'成型方式',value:'formingTypeStr'},
  // {name:'阻抗报告',value:'impedanceReport'},
  { name: "阻抗", value: "isImpedance" },
  // 特殊工艺
  { name: "盘中孔", value: "isDiscHole" },
  { name: "半边孔", value: "isHalfHole" },
  { name: "异形孔", value: "isProfileHole" },
  { name: "板边包金", value: "isPlateEdge" },
  { name: "沉孔", value: "stepHole" },
  { name: "印序列号", value: "isSerialNumber" },
  { name: "蓝胶", value: "isBlueGum" },
  { name: "金手指", value: "isGoldfinger" },
  { name: "金手指面积", value: "goldenFingerAreaRe" },
  { name: "金手指金厚", value: "goldFingerThicknessStr" },
  { name: "金手指镍厚", value: "goldfingerNickelThickness" },
  { name: "压接孔", value: "isCrimpHole" },
  { name: "背钻孔", value: "isBackDrilling" },
  { name: "通孔控深", value: "isThroughHoleControl" },
  { name: "盲孔控深", value: "isBlindHoleControl" },
  { name: "阶梯孔", value: "steppedHole" },
  { name: "碳油", value: "isCarbonOil" },
  { name: "高温胶", value: "heatTape" },
  { name: "斜边", value: "isGoldfingerBevel" },
  { name: "盲槽", value: "isBlindSlot" },
  { name: "埋铜", value: "buriedCopper" },
  { name: "埋阻", value: "buriedResistance" },
  { name: "铜浆塞孔", value: "cuPlugHole" },
  { name: "银浆塞孔", value: "silverPlugHole" },
];
export default {
  name: "projectBackend",
  components: {
    MakeupPic,
    BackendAction,
    OrderDetail,
    LeftTable,
    productRemark,
    QueryInfoBackend,
    ChargebackMake,
    ModifyInfoMake,
    mattersNeedingAttentionInfo,
    RepairRecordInfo,
    MarketBack,
    CustomerRulesInfo,
    OrderRetrievalSettings,
    viewLogInfo,
    FallbackFrontInfo,
    ProductionCompleted,
    Performance,
  },
  inject: ["reload"],
  data() {
    return {
      iscolumnKey: false,
      isorder: false,
      showText1: true,
      assignOrderList1: [], // 分派单号
      okorderno: [],
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        zIndex: 99,
      },
      menuVisible1: false,
      checkdataVisible: false,
      nname: "",
      ooeder: "",
      ooeder1: "",
      confirmLoading: false,
      spinning: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
      query: {
        OrderNo: "",
      },
      columns1,
      orderListData: [],
      pronotesData: [],
      orderListTableLoading: false,
      columns7,
      orderDetailData: [],
      orderDetailTableLoading: false,
      columns3: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          width: 40,
          scopedSlots: { customRender: "num" },
        },
        {
          title: "小组",
          dataIndex: "groups",
          width: 40,
          ellipsis: true,
          align: "left",
        },
        {
          title: "姓名",
          dataIndex: "realName",
          width: 80,
          ellipsis: true,
          align: "left",
          scopedSlots: {
            customRender: "customRender",
          },
        },
        {
          title: "目标",
          dataIndex: "targetCount",
          width: 35,
          ellipsis: true,
          align: "center",
        },
        // {
        //   title: "停留",
        //   dataIndex: "count_TL",
        //   width: '10%',
        //   ellipsis: true,
        //   align: "center",
        // },
        {
          title: "已发",
          dataIndex: "count_OK",
          width: 35,
          ellipsis: true,
          align: "center",
        },
        {
          title: "问客",
          dataIndex: "count_EQ",
          width: 35,
          ellipsis: true,
          align: "center",
        },
        {
          title: "系数",
          customRender: (text, record, index) => (record.score ? record.score.toFixed(2) : ""),
          //dataIndex: "score",
          align: "left",
          ellipsis: true,
          width: 50,
        },
        {
          title: "工厂",
          dataIndex: "facName",
          width: 100,
          ellipsis: true,
          align: "left",
        },
        {
          title: "操作",
          align: "center",
          scopedSlots: { customRender: "action" },
          width: 40,
        },
      ],
      peopleOrderInfoList: [],
      peopleOrderInfoTableLoading: false,
      columns4,
      jobData: [],
      confirmLoadingMode: false,
      jobautoData: [],
      jobTableLoading: false,
      columns5,
      xstitle: "",
      columns6,
      peopleOrderListData: [],
      peopleOrderListTableLoading: false,
      jobSwitch: true,
      note: "",
      cnNote: "",
      erpKey: "",
      userNo: "",
      isScript_: "",
      proOrderId: "",
      assignLoading: false,
      makeupVisible: false, // 拼版图弹窗开关
      dataVisible: false, // 查询弹窗开关
      dataVisible1: false, // 修改信息弹窗开关
      dataVisible2: false, // 退单弹窗开关
      dataVisible3: false, // 取单设置弹窗开关
      dataVisible6: false, // 返修记录弹窗开关
      dataVisible7: false, // 客户规则弹窗开关
      dataVisible8: false, // 查看日志
      dataVisible9: false, // 回退前端弹窗开关
      dataVisible10: false, // 回传弹窗开关
      dataVisible13: false,
      assignModalVisible: false,
      dataVisibleMode: false,
      messageMode: "",
      orderno: "",
      recordId: "",
      recordProOrderId: "",
      selectedRowsData: {},
      RepairRecordData: [], // 返修记录
      CustomerData: [], // 客户规则
      StackImpedanceData: [], // 叠层阻抗
      viewLogData: [], // 日志数据
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      selectedData: {},
      data: {
        // "isBigCus":'',
        // "isLeave_": '',
        // "targetCount_": '',
        // "orderCount_":'',
        // "isScript_": '',
        // "userNo_": '',
        // "isHighQuality": '',
        // "labels":'',
        userNo_: 0,
        isLeave_: false,
        targetCount_: 0,
        isScript_: false,
        isHighQuality: 0,
        labels: 0,
        getNum: 0,
        stayNum: 0,
        maxNum: 0,
        length: 0,
        width: 0,
        aluminum: false,
        nonAluminum: false,
        layer1: false,
        layer2: false,
        layer4: false,
        layer6: false,
        layer8: false,
        domesticTrade: false,
        foreignTrade: false,
        ordinary: false,
        premiumProducts: false,
        boutique: false,
        area1: false,
        area2: false,
        area3: false,
        area4: false,
        area5: false,
        area6: false,
        pcs: false,
        customerSet: false,
        jpSet: false,
        noCoordination: false,
        coordination: false,
        bigCus: false,
        noBigCus: false,
        tradeType: 0,
        jdbOrder: false,
        dqOrder: false,
        sort: 0,
        jpOrder: false,
        isRandom: false,
        jlcOrder: false, // 嘉立创
        hqOrder: false, // 华秋
        lhdcOrder: false, // 联合多层
        plOrder: false, // 普林
        tlOrder: false, // 塔联
        hzxOrder: false, // 合众鑫
        xsjOrder: false, // 兴晟捷
        jdOrder: false, // 吉达
        msgNum: 0, //错误数
        imp: false, // 阻抗
        newOrder: false, // 新单
        returnOrder: false, // 新单
        autoStartDate: null, // 开始时间
        autoEndDate: null, // 结束时间
      },
      wsUrl: "",
      websocket: null,
      showNote: "",
      factoryList: [],
      ParameterData2: {},
      checkData: [],
      meslist: "",
      check1: false,
      checkType: "",
      code: "",
      message: "",
      visibleRemark: false,
      confirmLoadingRemark: false,
      dataRemark: [],
      flowSelect: [],
      orderNoRemark: "",
      cookieId: "",
      queryParam: {},
      showText: false,
      visiblePerformance: false,
      dataVisibleMatter: false,
      editData: {},
      foldedornot: true,
      isCtrlPressed: false,
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
    };
  },
  watch: {
    peopleOrderListData: {
      handler(val) {
        if (val) {
          let maxLength = 0;
          let longestChars = [];
          for (let i = 0; i < val.length; i++) {
            let obj2 = val[i].orderNo;
            if (obj2) {
              var [...chars] = obj2;
              if (chars.length > maxLength) {
                maxLength = chars.length;
                longestChars = obj2;
              }
            }
          }
          if (longestChars.length > 12) {
            this.columns5[1].width = 125 + (longestChars.length - 12) * 5 + "px";
            this.columns5[2].width = 130 - (longestChars.length - 12) * 5 + "px";
          } else {
            this.columns5[1].width = "125px";
            this.columns5[2].width = "130px";
          }
        }
      },
    },
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      // this.jobTableLoading = true;
      // this.orderDetailTableLoading = true;
      this.getOrderList();
      this.getPeopleList();
      this.getMakeInfo();
      this.selectClick();
      // this.getJobAutoInfo();
      // this.getFlowSelectFunc();
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftstyle1 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1]
          .children[0];
      var leftstyle2 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[2].children[1]
          .children[0];
      var nodata = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0];
      if (this.checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendPersonOrderInfo")) {
        var centerstyle = document.getElementsByClassName("centerstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
        var nodata1 = document.getElementsByClassName("centerstyle")[0].children[0].children[0].children[0].children[0].children[0].children[2];
      }

      if (leftstyle && window.innerHeight <= 911) {
        leftstyle.style.height = this.screenHeight - 174 + "px";
        leftstyle1.style.height = this.screenHeight - 174 + "px";
        leftstyle2.style.height = this.screenHeight - 174 + "px";
        nodata.style.height = this.screenHeight - 144 + "px";
      }
      if (centerstyle) {
        centerstyle.style.height = this.screenHeight - 208 + "px";
        nodata1.style.height = 0;
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  mounted() {
    this.getcookie("ordernoBack");
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    window.addEventListener("resize", this.dehandleResize, true);
  },
  methods: {
    Coefficientdetails(id) {
      proordercoefficient(id, 1).then(res => {
        if (res.code) {
          let tit = [];
          let data = res.data[0].coefficientInfos;
          for (let index = 0; index < data.length; index++) {
            tit.push("【" + data[index].description + ": " + data[index].score + "】");
          }
          this.xstitle = tit.join(" + ");
        }
      });
    },
    handleResize() {
      this.screenWidth = window.innerWidth;
      this.screenHeight = window.innerHeight;
      var nodata = document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0];
      var leftstyle =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1];
      var leftstyle1 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1].children[1]
          .children[0];
      var leftstyle2 =
        document.getElementsByClassName("leftstyle")[0].children[0].children[0].children[0].children[0].children[0].children[2].children[1]
          .children[0];
      if (this.checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendPersonOrderInfo")) {
        var centerstyle = document.getElementsByClassName("centerstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
        var rightstyle = document.getElementsByClassName("rightstyle")[0].children[0].children[0].children[0].children[0].children[0].children[1];
      }
      var footerwidth = window.innerWidth - 224;
      if (this.screenHeight <= 911) {
        nodata.style.height = this.screenHeight - 139 + "px";
      } else {
        nodata.style.height = "768px";
      }
      var paginnum = "";
      if (Math.ceil(this.pagination.total / 20) > 10) {
        paginnum = 7;
      } else {
        paginnum = Math.ceil(this.pagination.total / 20);
      }
      if (paginnum * 50 + 310 < footerwidth / 2) {
        this.pagination.simple = false;
        this.pagination.size = "";
        this.pagination.showSizeChanger = true;
        this.pagination.showQuickJumper = true;
      }
      const num = this.$refs.action.nums * 104;
      if (this.screenWidth < 1920 || this.screenHeight < 923) {
        if (paginnum * 50 + 310 < footerwidth / 2 && num + paginnum * 50 + 310 < footerwidth) {
          this.pagination.simple = false;
          this.pagination.size = "";
          this.pagination.showSizeChanger = true;
          this.pagination.showQuickJumper = true;
        } else {
          this.pagination.simple = false;
          this.pagination.size = "small";
          this.pagination.showSizeChanger = false;
          this.pagination.showQuickJumper = false;
        }
        if (paginnum * 25 + 200 + num < this.screenWidth - 150 && this.screenWidth > 766) {
          if (footerwidth / 2 < paginnum * 25 + 200) {
            this.pagination.simple = true;
          } else {
            this.pagination.simple = false;
          }
        } else {
          if (this.screenWidth > 766) {
            if (footerwidth / 2 < paginnum * 45) {
              this.pagination.simple = true;
            } else {
              this.pagination.simple = false;
            }
          } else {
            this.pagination.simple = true;
          }
        }
        if (leftstyle && this.orderListData.length != 0) {
          leftstyle.style.height = this.screenHeight - 174 + "px";
          leftstyle1.style.height = this.screenHeight - 174 + "px";
          leftstyle2.style.height = this.screenHeight - 174 + "px";
        }
        if (centerstyle && this.peopleOrderInfoList.length != 0) {
          centerstyle.style.height = this.screenHeight - 208 + "px";
        }
        if (rightstyle && this.peopleOrderListData.length != 0) {
          rightstyle.style.height = this.screenHeight - 174 + "px";
        } else {
          // rightstyle.style.height =  '0px'
        }
      } else {
        if (leftstyle && this.orderListData.length != 0) {
          leftstyle.style.height = "738px";
          leftstyle1.style.height = "738px";
          leftstyle2.style.height = "738px";
        }
        if (centerstyle && this.peopleOrderInfoList.length != 0) {
          centerstyle.style.height = "706px";
        }
        if (rightstyle && this.peopleOrderListData.length != 0) {
          rightstyle.style.height = "736px";
        } else {
          // rightstyle.style.height =  '0px'
        }
      }
      if (this.orderListData.length == 0 && leftstyle) {
        leftstyle.style.height = "0px";
        leftstyle2.style.height = "15px";
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        this.reportHandleCancel();
        this.dataVisible = true;
        e.preventDefault();
      } else if (e.keyCode == "67" && (this.dataVisibleMode || this.assignModalVisible)) {
        this.isCtrlPressed = false;
        this.reportHandleCancel();
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.assignModalVisible) {
        this.assignhandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "81" && this.isCtrlPressed && checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendOrder")) {
        e.preventDefault();
        this.TakeOrderClick();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "83" && this.isCtrlPressed && checkPermission("MES.EngineeringModule.EngineeringBackend.EngineeringBackendSend")) {
        e.preventDefault();
        this.assignClick();
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && this.dataVisibleMode) {
        this.handleOkMode();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    onClickRow1(record) {
      return {
        on: {
          contextmenu: e => {
            let text = "";
            if (e.target.innerText) {
              text = e.target.innerText;
            }
            e.preventDefault();
            this.rightClick2(e, text, record);
          },
        },
      };
    },
    rightClick2(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode.parentNode;
      }
      if (e.target.localName == "path" || e.target.localName == "svg") {
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode;
        if (event.className.indexOf("tagNum") != -1) {
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
        }
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText1 = false;
      } else {
        this.showText1 = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split("\n")[0];
      }
      const tableWrapper = this.$refs.tableWrapper1;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible1 = true;
      let offsetx = event.offsetLeft + event.offsetWidth + 1270;
      let offsety = event.offsetTop + 340;
      if (event.cellIndex == this.columns4.length - 1) {
        this.menuStyle1.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle1.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle1.top = cellRect.top - wrapperRect.top + 300 + "px";
        this.menuStyle1.left = cellRect.left - wrapperRect.left + event.offsetWidth + 1250 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle1.top = offsety + "px";
        this.menuStyle1.left = offsetx + "px";
      }
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    down2() {
      let input = document.createElement("input");
      //input.value = this.text
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    // 返修登记
    RegisterClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      let OrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      let id = this.$refs.orderTable.selectedRowsData.proOrderId;
      const routeOne = this.$router.resolve({
        path: "/registerDetails",
        query: {
          OrderNo: OrderNo,
          pid: id,
          modu: "back",
        },
      });
      window.open(routeOne.href, "_blank", routeOne.query);
    },
    down() {
      let input = document.createElement("input");
      input.value = this.text.trim();
      document.body.appendChild(input);
      input.select();
      let bool = document.execCommand("copy");
      bool ? this.$message.success("复制成功") : this.$message.error("复制失败");
      document.body.removeChild(input);
    },
    handleOkMode() {
      // 开始
      if (this.type == "1") {
        this.confirmLoadingMode = true;
        projectBackEndAssignBack(this.recordProOrderId)
          .then(res => {
            if (res.code) {
              this.$message.success(res.message);
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.confirmLoadingMode = false;
          });
      }
      if (this.type == "2") {
        if (this.$refs.orderTable.selectedRowsData.status == 18 && this.$refs.orderTable.selectedRowsData.qaeStatus == 20) {
          this.confirmLoadingMode = true;
          RepairCompleted(this.$refs.orderTable.selectedRowKeysArray[0])
            .then(res => {
              if (res.code) {
                this.$message.success("返修完成");
              } else {
                this.$message.error(res.message);
              }
              this.reload();
            })
            .finally(() => {
              this.confirmLoadingMode = false;
            });
        } else {
          this.$message.error("当前状态，不允许返修完成！");
        }
      }
      if (this.type == "3") {
        this.confirmLoadingMode = true;
        delInfo(this.recordId)
          .then(res => {
            if (res.code) {
              this.$message.success("已删除");
              //this.getJobInfo(this.$refs.orderTable.selectedRowsData.proOrderId)
            } else {
              this.$message.errer(res.message);
            }
          })
          .finally(() => {
            this.confirmLoadingMode = false;
          });
      }
      if (this.type == "4") {
        this.confirmLoadingMode = true;
        BackMakeinish(this.$refs.orderTable.selectedRowKeysArray)
          .then(res => {
            if (res.code) {
              this.$message.success("后端完成");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.confirmLoadingMode = false;
          });
      }
      //确认修改
      if (this.type == "5") {
        this.confirmLoadingMode = true;
        setsureordermodify(this.$refs.orderTable.selectedRowKeysArray)
          .then(res => {
            if (res.code) {
              this.$message.success("修改成功");
              this.getOrderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.confirmLoadingMode = false;
          });
      }
      this.dataVisibleMode = false;
    },
    checkPermission,
    getFlowSelectFunc() {
      getFlowSelect().then(res => {
        if (res.code == 1) {
          this.flowSelect = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleClickRow(record, index) {
      return {
        on: {
          click: () => {
            this.dataRemark = this.pronotesData;
          },
        },
      };
    },
    //获取生产备注
    getprocessstepnotes(orderno) {
      this.orderNoRemark = orderno;
      processstepnotes(orderno)
        .then(res => {
          if (res.code == 1) {
            this.visibleRemark = true;
            this.dataRemark = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {});
    },
    handleCancelRemark(val) {
      this.visibleRemark = val;
    },
    handleOkRemark(data) {
      this.confirmLoadingRemark = true;
      postProcess(data).then(res => {
        this.confirmLoadingRemark = false;
        if (res.code == 1) {
          this.visibleRemark = false;
          this.$message.success("更新成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取当日制作数据
    getMakeInfo() {
      makeInfo().then(res => {
        if (res.code) {
          this.showNote = res.data;
        }
      });
    },
    // 获取订单
    getOrderList(queryData) {
      this.pageStat = localStorage.getItem("stat1") == "true" ? true : false;
      if (this.pageStat) {
        let pageCurrent = localStorage.getItem("pageCurrent1");
        if (pageCurrent != undefined && pageCurrent != "") {
          this.pagination.current = parseInt(pageCurrent);
        }
        let pageSize = localStorage.getItem("pageSize1");
        if (pageSize != undefined && pageSize != "") {
          this.pagination.pageSize = parseInt(pageSize);
        }
        let data = localStorage.getItem("queryParam1");
        if (data != null && data != undefined && data != "") {
          this.queryParam = JSON.parse(data);
        }
      }
      this.queryParam.pageIndex = this.pagination.current;
      this.queryParam.pageSize = this.pagination.pageSize;
      let data = {
        ...this.queryParam,
      };
      localStorage.setItem("queryParam1", JSON.stringify(data));
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      let record = localStorage.getItem("record7");
      let indexId = localStorage.getItem("OrderId1");
      var obj = Object.assign(params, queryData);
      this.orderListTableLoading = true;
      projectBackEndOrderList(obj)
        .then(res => {
          if (res.items) {
            this.orderListData = res.items;
            if (this.iscolumnKey) {
              this.orderListData.sort((a, b) => {
                let aValue = a[this.iscolumnKey];
                let bValue = b[this.iscolumnKey];
                if (typeof aValue === "string" && typeof bValue === "string") {
                  return this.isorder === "ascend" ? aValue.localeCompare(bValue) : this.isorder === "descend" ? bValue.localeCompare(aValue) : 0;
                } else {
                  if (aValue === bValue) {
                    return this.orderListData.indexOf(a) - this.orderListData.indexOf(b);
                  }
                  if (this.isorder === "ascend") {
                    return aValue > bValue ? 1 : -1;
                  } else if (this.isorder === "descend") {
                    return aValue < bValue ? 1 : -1;
                  }
                  return 0;
                }
              });
            }
            setTimeout(() => {
              this.handleResize();
            }, 0);
            const pagination = { ...this.pagination };
            pagination.total = res.totalCount;
            this.pagination = pagination;
            if ((params.OrderNo || params.States) && this.orderListData.length) {
              this.$refs.orderTable.selectedRowKeysArray[0] = this.orderListData[0].proOrderId;
              this.$refs.orderTable.selectedRowsData.orderNo = this.orderListData[0].orderNo;
              this.assignOrderList1[0] = this.orderListData[0].orderNo;
              this.selectedRowsData.proOrderId = this.orderListData[0].proOrderId;
            }
          } else {
            this.$message.error(res.message);
          }
          if (indexId !== "" && indexId != null) {
            this.$refs.orderTable.proOrderId = indexId;
            this.$refs.orderTable.selectedRowKeysArray[0] = indexId;
          }
          if (record != null) {
            this.$refs.orderTable.selectedRowsData = JSON.parse(record);
            this.getOrderDetail(JSON.parse(record));
          }
          if (this.pageStat) {
            localStorage.removeItem("OrderId1");
            localStorage.removeItem("record7");
            localStorage.removeItem("pageCurrent1");
            localStorage.removeItem("pageSize1");
            localStorage.removeItem("stat1");
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },

    // 获取订单详情
    getOrderDetail(record) {
      this.orderDetailTableLoading = true;
      this.jobSwitch = true;
      var obj = {};
      proOrderInfo(record.proOrderId)
        .then(res => {
          if (res.code) {
            if (res.data) {
              obj.boardLayers = res.data.proOrderInfoDto.boardLayers; // 层数
              obj.orderNo = res.data.proOrderInfoDto.orderNo; // 本场编号
              obj.pcbFileName = res.data.proOrderInfoDto.pcbFileName; // 客户型号
              obj.boardThickness = res.data.proOrderInfoDto.boardThickness; // 板厚
              obj.flyingProbeStr = res.data.proOrderInfoDto.flyingProbeStr; // 测试方式
              obj.boardHeight = res.data.proOrderInfoDto.boardHeight + "x" + res.data.proOrderInfoDto.boardWidth; // 单元尺寸
              obj.boardHeightSet = res.data.proOrderInfoDto.boardHeightSet + "x" + res.data.proOrderInfoDto.boardWidthSet; // 成品尺寸
              if (res.data.proOrderInfoDto.formingTypeStr && res.data.proOrderInfoDto.pinBanType && res.data.proOrderInfoDto.boardTypeStr) {
                obj.pinBanType =
                  res.data.proOrderInfoDto.formingTypeStr +
                  "," +
                  res.data.proOrderInfoDto.pinBanType +
                  "(" +
                  res.data.proOrderInfoDto.boardTypeStr +
                  ")";
              } else if (res.data.proOrderInfoDto.formingTypeStr && res.data.proOrderInfoDto.pinBanType && !res.data.proOrderInfoDto.boardTypeStr) {
                obj.pinBanType = res.data.proOrderInfoDto.formingTypeStr + "," + res.data.proOrderInfoDto.pinBanType;
              } else if (res.data.proOrderInfoDto.pinBanType && res.data.proOrderInfoDto.boardTypeStr && !res.data.proOrderInfoDto.formingTypeStr) {
                obj.pinBanType = res.data.proOrderInfoDto.pinBanType + "(" + res.data.proOrderInfoDto.boardTypeStr + ")";
              } else {
                obj.pinBanType = res.data.proOrderInfoDto.pinBanType;
              }
              obj.pinBanNum = res.data.proOrderInfoDto.pinBanNum; // 合拼款数
              obj.isImpedance = res.data.proOrderInfoDto.isImpedance ? "是" : ""; //阻抗
              obj.surfaceFinishStr = res.data.proOrderInfoDto.surfaceFinishStr; //表面处理
              obj.fR4TypeStr =
                res.data.proOrderInfoDto.sheetTraderStr + " " + res.data.proOrderInfoDto.fR4TypeStr + " " + res.data.proOrderInfoDto.fR4TgStr; //板材信息
              obj.boardBrandStr = res.data.proOrderInfoDto.boardBrandStr; //板材型号
              obj.solderColorStr = res.data.proOrderInfoDto.solderColorStr + "/" + res.data.proOrderInfoDto.solderColorBottomStr; //阻焊颜色顶/底
              obj.fontColorStr = res.data.proOrderInfoDto.fontColorStr + "/" + res.data.proOrderInfoDto.fontColorBottomStr; //字符颜色顶/底
              obj.solderCoverStr = res.data.proOrderInfoDto.solderCoverStr; // 过孔处理
              obj.formingTypeStr = res.data.proOrderInfoDto.formingTypeStr; // 成型方式
              obj.num = res.data.proOrderInfoDto.num + " (" + res.data.proOrderInfoDto.boardArea + "㎡)";
              obj.isDiscHole = res.data.proOrderInfoDto.isDiscHole ? "是" : ""; // 盘中孔
              obj.isHalfHole = res.data.proOrderInfoDto.isHalfHole ? "是" : ""; // 半边孔
              obj.isProfileHole = res.data.proOrderInfoDto.isProfileHole ? "是" : ""; // 异形孔
              obj.isPlateEdge = res.data.proOrderInfoDto.isPlateEdge ? "是" : ""; // 板边包金
              obj.stepHole = res.data.proOrderInfoDto.stepHole ? "是" : ""; // 沉孔
              obj.isSerialNumber = res.data.proOrderInfoDto.isSerialNumber ? "是" : ""; // 印序列号
              obj.isBlueGum = res.data.proOrderInfoDto.isBlueGum ? "是" : ""; // 蓝胶
              obj.isGoldfinger = res.data.proOrderInfoDto.isGoldfinger ? "是" : ""; // 金手指
              obj.goldenFingerAreaRe = res.data.proOrderInfoDto.goldenFingerAreaRe; // 金手指面积
              obj.goldFingerThicknessStr = res.data.proOrderInfoDto.goldFingerThicknessStr; // 金手指金厚
              obj.goldfingerNickelThickness = res.data.proOrderInfoDto.goldfingerNickelThickness; // 金手指镍厚
              obj.isCrimpHole = res.data.proOrderInfoDto.isCrimpHole ? "是" : ""; // 压接孔
              obj.isBackDrilling = res.data.proOrderInfoDto.isBackDrilling ? "是" : ""; // 背钻孔
              obj.isThroughHoleControl = res.data.proOrderInfoDto.isThroughHoleControl ? "是" : ""; // 通孔控深
              obj.isBlindHoleControl = res.data.proOrderInfoDto.isBlindHoleControl ? "是" : ""; // 盲孔控深
              obj.steppedHole = res.data.proOrderInfoDto.steppedHole ? "是" : ""; // 阶梯孔
              obj.isCarbonOil = res.data.proOrderInfoDto.isCarbonOil ? "是" : ""; // 碳油
              obj.heatTape = res.data.proOrderInfoDto.heatTape ? "是" : ""; // 高温胶
              obj.isGoldfingerBevel = res.data.proOrderInfoDto.isGoldfingerBevel ? "是" : ""; // 斜边
              obj.isBlindSlot = res.data.proOrderInfoDto.isBlindSlot ? "是" : ""; // 盲槽
              obj.buriedCopper = res.data.proOrderInfoDto.buriedCopper ? "是" : ""; // 埋铜
              obj.buriedResistance = res.data.proOrderInfoDto.buriedResistance ? "是" : ""; // 埋阻
              obj.cuPlugHole = res.data.proOrderInfoDto.cuPlugHole ? "是" : ""; // 铜浆塞孔
              obj.silverPlugHole = res.data.proOrderInfoDto.silverPlugHole ? "是" : ""; // 银浆塞孔
              if (obj.boardLayers >= 2) {
                obj.copperThicknessStr = res.data.proOrderInfoDto.copperThickness + "/" + res.data.proOrderInfoDto.copperThickness; //外完成铜厚
              } else {
                obj.copperThicknessStr = res.data.proOrderInfoDto.copperThickness + "/0"; //外完成铜厚
              }

              if (obj.boardLayers > 2) {
                var srt = obj.boardLayers - 2;
                var a = res.data.proOrderInfoDto.innerCopperThickness;
                var arr = [];
                for (var i = 0; i < srt; i++) {
                  arr.push(a);
                }
                obj.innerCopperThicknessStr = arr.join("/");
                // obj.innerCopperThicknessStr = res.data.proOrderInfoDto.innerCopperThickness  // 内铜
              }
              this.orderDetailData = obj;
              this.note = res.data.proOrderInfoDto.note;
              this.cnNote = res.data.proOrderInfoDto.cnNote;
              this.noteHtml(res.data.proOrderInfoDto);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.orderDetailTableLoading = false;
        });
    },
    getJobAutoInfo() {
      getJobAutoInfo()
        .then(res => {
          if (res.code) {
            this.jobautoData = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {});
    },
    // 获取人员
    getPeopleList() {
      this.peopleOrderInfoTableLoading = true;
      projectBackEndPeopleList()
        .then(res => {
          if (res.code) {
            this.peopleOrderInfoList = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.peopleOrderInfoTableLoading = false;
        });
    },
    // 获取对应的订单人员
    // getJobInfo(id){
    //   this.jobTableLoading = true;
    //   projectBackEndJobInfo(id).then(res => {
    //     if (res.code) {
    //       this.jobData = res.data;
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   }).finally(()=>{
    //     this.jobTableLoading = false;
    //   })
    // },

    // 获取人员对应的订单
    getPeopleOrderList(id) {
      this.peopleOrderListTableLoading = true;
      peopleOrderList({ erpid: id })
        .then(res => {
          if (res.code) {
            this.peopleOrderListData = res.data;
            setTimeout(() => {
              this.handleResize();
            }, 0);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.peopleOrderListTableLoading = false;
        });
    },
    // 图片字符串裁切
    picFilter(val) {
      if (val.picUrl) {
        return val.picUrl.split(",");
      } else {
        return [];
      }
    },
    // 订单表变化change
    handleTableChange(pagination, filter, sorter) {
      let { columnKey, order } = sorter;
      this.iscolumnKey = columnKey;
      this.isorder = order;
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.pageStat = false;
      let queryData = {
        OrderNo: this.ooeder,
        States: this.ooeder1,
      };
      if (JSON.stringify(queryData) != "{}") {
        this.getOrderList(queryData);
      } else {
        this.getOrderList();
      }
    },
    // 选项卡变化change
    tabpaneChange(activeKey) {
      if (activeKey == "2") {
        this.jobSwitch = false;
      } else {
        this.jobSwitch = true;
      }
    },
    // 保存选中的人员信息
    saveErpKey(record) {
      this.nname = record.realName;
      this.erpKey = record.userLoginID_;
      this.selectedData = record;
      this.userNo = record.userNo;
      this.isScript_ = record.isScript_;
      this.jobSwitch = false;
    },
    assignhandleOk() {
      let guids = this.$refs.orderTable.selectedRowKeysArray;
      let userErpKey = this.erpKey;
      let params = {
        guids: guids,
        userErpKey: userErpKey,
      };
      this.assignLoading = true;
      this.spinning = true;
      projectBackEndAssign(params)
        .then(res => {
          if (res.code) {
            this.$message.success("分派成功");
            this.$refs.orderTable.selectedRowKeysArray = [];
            this.getOrderList();
            this.getPeopleList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.assignLoading = false;
          this.spinning = false;
        });
      this.assignModalVisible = false;
    },
    // 分派事件
    assignClick() {
      let guids = this.$refs.orderTable.selectedRowKeysArray;
      let userErpKey = this.erpKey;
      if (guids.length == 0) {
        this.$message.warning("请先选择订单");
        return;
      }
      if (!userErpKey) {
        this.$message.warning("请选择要分派的人员");
        return;
      }
      this.assignModalVisible = true;
    },
    // 分派回退
    assignBackClick(record) {
      // this.orderno = this.$refs.orderTable.selectedRowsData.orderNo
      this.orderno = record.orderNo;
      this.messageMode = "确认回退订单吗？";
      this.dataVisibleMode = true;
      this.type = "1";
      this.recordProOrderId = record.proOrderId;
    },
    // 弹窗关闭
    reportHandleCancel() {
      this.dataVisible = false; // 查询
      this.dataVisible1 = false; // 修改信息
      this.dataVisible2 = false; // 退单
      this.dataVisible3 = false; // 取单设置
      this.dataVisible6 = false; // 返修记录
      this.dataVisible7 = false; // 客户规则
      this.dataVisible8 = false; // 查看日志
      this.dataVisible9 = false; // 回退前端弹窗开关
      this.dataVisible10 = false; // 回传弹窗开关
      this.assignModalVisible = false;
      this.checkdataVisible = false; //后端完成错误信息
      this.checkData = [];
      this.dataVisibleMode = false;
      this.dataVisible13 = false;
      this.visiblePerformance = false;
      this.dataVisibleMatter = false;
    },
    // 查询
    queryClick() {
      this.dataVisible = true;
    },
    handleOk() {
      // let queryData ={
      //   'OrderNo':this.$refs.queryInfo.OrderNumber,
      //   'States':this.$refs.queryInfo.OrderStates,
      //   'Dispatch':this.$refs.queryInfo.OrderDispatch,
      //   'strLjb_':this.$refs.queryInfo.form.strLjb_
      // }
      let params = this.$refs.queryInfo.form;
      this.ooeder = params.OrderNo;
      this.ooeder1 = params.States;
      var arr1 = params.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      this.pagination.current = 1;
      params.OrderNo = arr1.join("");
      if (params.OrderNo && typeof params.OrderNo === "string" && params.OrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产编号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      this.getOrderList(params);
    },
    // 取单
    TakeOrderClick() {
      this.spinning = true;
      this.$refs.action.isDisabled = true;
      setTimeout(() => {
        this.$refs.action.isDisabled = false;
      }, 2000);
      ppebuttonCheck("dd87eabd-4d6e-dfc2-3202-3a10fbe46577", "engineeringbackendorder")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "hdqd";
              this.checkdataVisible = true;
              this.meslist = "后端取单按钮检查";
            } else {
              TakeOrderList().then(res => {
                if (res.code) {
                  this.$message.success("成功");
                  this.$refs.peopleOrder.userLoginID_ = this.erpKey;
                  this.getOrderList();
                  this.getPeopleList();
                  this.getPeopleOrderList(this.erpKey);
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 获取取单设置信息
    OrderRetrievalSettingsClick(record) {
      if (!this.selectedData) {
        this.$message.warning("请选择小组人员");
        return;
      }
      orderRetrievalSettings(record.userNo).then(res => {
        if (res.code) {
          res.data.realName = record.realName;
          res.data.groups = record.groups;
          res.data.facid = record.facid;
          if (res.data.labels == 0) {
            res.data.labels = "0";
          } else if (res.data.labels == 1) {
            res.data.labels = "1";
          } else if (res.data.labels == 2) {
            res.data.labels = "2";
          } else {
            res.data.labels = "3";
          }
          if (!res.data.tradeTypeSrc) {
            res.data.tradeTypeSrc = [];
          }
          res.data.autoStartDate = moment(res.data.autoStartDate, "HH:mm");
          res.data.autoEndDate = moment(res.data.autoEndDate, "HH:mm");
          this.data = res.data;
          if (!res.data.custNo) {
            res.data.custNo = [];
          } else {
            res.data.custNo = res.data.custNo.split(",");
          }
          if (!res.data.orderByCustNo) {
            res.data.orderByCustNo = [];
          } else {
            res.data.orderByCustNo = res.data.orderByCustNo.split(",");
          }
          this.dataVisible3 = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 取单设置
    handleOk3() {
      let a = /^\d+-\d+$/;
      let y = /^\+?[0-9][0-9]*$/;
      if (this.data.targetCount_ && !y.test(this.data.targetCount_)) {
        this.$message.error("目标数量请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.targetCount_.focus();
        this.$refs.OrderRetrievalSettings.$refs.targetCount_.select();
        return;
      }
      if (this.data.maxNum && !y.test(this.data.maxNum)) {
        this.$message.error("每日总量请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.maxNum.focus();
        this.$refs.OrderRetrievalSettings.$refs.maxNum.select();
        return;
      }
      if (this.data.getNum && !y.test(this.data.getNum)) {
        this.$message.error("单次获取请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.getNum.focus();
        this.$refs.OrderRetrievalSettings.$refs.getNum.select();
        return;
      }
      if (this.data.stayNum && !y.test(this.data.stayNum)) {
        this.$message.error("停留数量请输入正确格式");
        this.$refs.OrderRetrievalSettings.$refs.stayNum.focus();
        this.$refs.OrderRetrievalSettings.$refs.stayNum.select();
        return;
      }
      if (!a.test(this.data.boardSzie) && this.data.boardSzie) {
        this.$message.error("出货尺寸请输入正确的区间格式 如：0-5");
        this.$refs.OrderRetrievalSettings.$refs.boardSzie.focus();
        this.$refs.OrderRetrievalSettings.$refs.boardSzie.select();
        return;
      }
      this.confirmLoading = true;
      this.spinning = true;
      if (this.data.labels == "0") {
        this.data.labels = 0;
      } else if (this.data.labels == "1") {
        this.data.labels = 1;
      } else if (this.data.labels == "2") {
        this.data.labels = 2;
      } else {
        this.data.labels = 3;
      }
      const params = {
        ...this.data,
        targetCount_: Number(this.data.targetCount_),
        getNum: Number(this.data.getNum),
        stayNum: Number(this.data.stayNum),
        maxNum: Number(this.data.maxNum),
        length: Number(this.data.length),
        width: Number(this.data.width),
        autoStartDate: this.transformTimestamp(this.data.autoStartDate),
        autoEndDate: this.transformTimestamp(this.data.autoEndDate),
      };
      params.orderByCustNo = params.orderByCustNo.join(",");
      params.custNo = params.custNo.join(",");
      RetrievalSettings(params)
        .then(res => {
          if (res.code) {
            this.$message.success("设置成功");
          } else {
            this.$message.error(res.message);
          }
          this.getPeopleList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible3 = false;
        });
    },
    // 处理时间格式
    transformTimestamp(timestamp) {
      let a = new Date(timestamp).getTime();
      const date = new Date(a);
      const h = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      const m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      // const s = date.getSeconds(); // 秒
      const dateString = h + m;
      // console.log('dateString', dateString); // > dateString 2021-07-06 14:23
      return dateString;
    },
    //
    selectClick() {
      getFactoryList().then(res => {
        if (res.code == 1) {
          this.factoryList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 修改信息
    modifyInfoClick(record) {
      // if(!this.$refs.orderTable.selectedRowKeysArray[0]){
      //   this.$message.warning("请选择订单")
      //   return
      // }
      if (this.factoryList.find(item => item.valueMember == record.joinFactoryId)) {
        record.joinFactoryId = this.factoryList.find(item => item.valueMember == record.joinFactoryId).valueMember;
      } else {
        record.joinFactoryId = "";
      }
      this.selectedRowsData = record;
      this.dataVisible1 = true;
    },
    modifyInfoClick1(record, selectedRowKeysArray) {
      this.selectedRowsData = record;
      var arr = [];
      for (var i = 0; i < selectedRowKeysArray.length; i++) {
        var str = this.orderListData.filter(ite => {
          return ite.proOrderId == selectedRowKeysArray[i];
        })[0].orderNo;
        arr.push(str);
      }
      this.assignOrderList1 = arr;
    },
    handleOk1() {
      this.confirmLoading = true;
      // console.log(this.$refs.ModifyInfoMake.infoForm,this.$refs.orderTable.selectedRowKeysArray[0])
      let params = this.$refs.ModifyInfoMake.infoForm;
      params.id = this.selectedRowsData.proOrderId;
      // console.log(paramsr)
      this.spinning = true;
      getModifyInformation(params)
        .then(res => {
          if (res.code) {
            this.$message.success("修改成功");
          } else {
            this.$message.error(res.message);
          }
          this.reload();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible1 = false;
        });
    },
    // 退单（回退业务）
    ChargebackClick(record) {
      this.dataVisible2 = true;
      this.selectedRowsData = record;
      // console.log('this.selectedRowsData',this.selectedRowsData)
    },
    handleOk2() {
      if (
        (this.$refs.ChargebackMake.ChargebackForm.remark != "" && this.$refs.ChargebackMake.ChargebackForm.isMemberConfirm == true) ||
        this.$refs.ChargebackMake.ChargebackForm.isParamError == true ||
        this.$refs.ChargebackMake.ChargebackForm.isFileError == true
      ) {
        this.confirmLoading = true;
        let params = this.$refs.ChargebackMake.ChargebackForm;
        params.id = this.selectedRowsData.proOrderId;
        this.spinning = true;
        BackStart(params)
          .then(res => {
            if (res.code) {
              this.$message.success("回退成功");
            } else {
              this.$message.error(res.message);
            }
            this.getOrderList();
          })
          .finally(() => {
            this.spinning = false;
            this.confirmLoading = false;
            this.dataVisible2 = false;
          });
      } else {
        this.$message.error("备注不能为空且至少勾选一项");
      }
    },
    // 回退前端
    FallbackFrontClick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "GetEngineeringBackOrders")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "htqd";
              this.checkdataVisible = true;
              this.meslist = "回退前端按钮检查";
            } else {
              this.dataVisible9 = true;
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    handleOk9() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = {
        BackRemark: this.$refs.FallbackFrontInfo.BackRemark,
      };
      FallbackFront(this.selectedRowsData.proOrderId, params)
        .then(res => {
          if (res.code) {
            this.$message.success("回退成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible9 = false;
        });
    },
    // 返修记录
    RepairRecordClick(record) {
      let OrderNo = record.orderNo;
      let id = record.proOrderId;
      const routeOne = this.$router.resolve({
        path: "/registerDetails",
        query: {
          OrderNo: OrderNo,
          pid: id,
          modu: "back",
        },
      });
      window.open(routeOne.href, "_blank", routeOne.query);
      //     getRepairRecord(record.proOrderId).then(res=>{
      //     if(res.code){
      //       this.RepairRecordData = res.data
      //       this.dataVisible6=true;
      //     }else{
      //       this.$message.error(res.message)
      //     }
      //   })
      // },
    },

    handleOk6() {
      this.dataVisible6 = false;
    },
    // 客户规则
    CustomerRulesClick(record) {
      this.dataVisible7 = true;
      let CustNo = record.custNo;
      let factory = record.joinFactoryId;
      getCustomerInfo(CustNo, factory, 1, record.businessOrderNo, record.orderNo).then(res => {
        if (res.code) {
          this.CustomerData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk7() {
      this.dataVisible7 = false;
    },
    // 叠层阻抗
    StackImpedanceClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      getStackImpedance(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          if (res.data.drills.length) {
            let drillsData = res.data.drills;
            localStorage.setItem("drillsData", JSON.stringify({ drillsData }));
          } else {
            localStorage.removeItem("drillsData");
          }
          this.StackImpedanceData = res.data;
          // const routeOne = this.$router.resolve({
          //     path: '/impedance',
          //     query:{boardType:res.data.boardType,finishBoardThickness:res.data.finishBoardThickness,layers:res.data.layers,pdctno:res.data.pdctno}
          //   })
          //   window.open(routeOne.href, '_blank',routeOne.query)
          this.$router.push({
            path: "/gongju/impedance",
            query: {
              boardType: res.data.boardType,
              finishBoardThickness: res.data.finishBoardThickness,
              layers: res.data.layers,
              pdctno: res.data.pdctno,
              id: this.$refs.orderTable.selectedRowKeysArray[0],
            },
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 问客
    wenkeClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      let OrderNo = this.$refs.orderTable.selectedRowsData.orderNo;
      const routeOne = this.$router.resolve({
        path: "/eqDetails",
        query: {
          OrderNo: OrderNo,
          eQSource: 2,
        },
      });
      window.open(routeOne.href, "_blank", routeOne.query);
      // this.$router.push({path:'eqDetails',query:{ OrderNo:OrderNo,eQSource:2} })
      // if(this.$refs.orderTable.selectedRowsData.tradeTypeCopy == 7 || this.$refs.orderTable.selectedRowsData.tradeTypeCopy == 8){
      //   let OrderNo = this.$refs.orderTable.selectedRowsData.orderNo
      //   this.$router.push({path:'eqDetails',query:{ OrderNo:OrderNo} })
      // }else {
      //   getWenkeUrl(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
      //     if (res.code) {
      //       console.log(res)
      //       window.open(res.data, "_blank").location
      //     } else {
      //       this.$message.error(res.message)
      //     }
      //   })
      // }
    },
    //市场修改
    MarketmodClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.dataVisible13 = true;
    },
    handleOk13() {
      if (this.$refs.MarketBack.MarketForm.content != "" || this.$refs.MarketBack.MarketForm.filePaths != "") {
        this.confirmLoading = true;
        let params = {
          proOrderNo: this.$refs.orderTable.selectedRowsData.orderNo,
          orderNo: this.$refs.orderTable.selectedRowsData.businessOrderNo,
          content: this.$refs.MarketBack.MarketForm.content,
          filePath: this.$refs.MarketBack.MarketForm.filePaths,
          orderModifyType: 2,
        };

        this.spinning = true;
        Marketmake(params)
          .then(res => {
            if (res.code) {
              this.$message.success("市场修改提交成功");
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
            this.confirmLoading = false;
            this.dataVisible13 = false;
          });
      } else {
        this.$message.error("请至少填写一项");
      }
    },
    checkClick() {
      if (this.checkType == "hdwc") {
        this.checkdataVisible = false;
        this.type = "4";
        this.handleOkMode();
      }
      if (this.checkType == "htqd") {
        this.checkdataVisible = false;
        this.dataVisible9 = true;
      }
      if (this.checkType == "hdqd") {
        this.spinning = true;
        this.checkdataVisible = false;
        TakeOrderList()
          .then(res => {
            if (res.code) {
              this.$message.success("成功");
              this.$refs.peopleOrder.userLoginID_ = this.erpKey;
              this.getOrderList();
              this.getPeopleList();
              this.getPeopleOrderList(this.erpKey);
            } else {
              this.$message.error(res.message);
            }
            this.reload();
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    //后端完成
    backendcompletion() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      this.spinning = true;
      ppebuttonCheck(this.$refs.orderTable.selectedRowKeysArray[0], "backmakefinish")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check1 = this.checkData.findIndex(v => v.error == "1") < 0;
              this.checkType = "hdwc";
              this.checkdataVisible = true;
              this.meslist = "后端制作完成按钮检查";
            } else {
              this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
              this.messageMode = "确定后端制作完成吗？";
              this.dataVisibleMode = true;
              this.type = "4";
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 返修完成
    RepairCompletedClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确定返修完成吗？";
      this.dataVisibleMode = true;
      this.type = "2";
    },
    //确认修改
    Confirmmodification() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("该操作只能选择一个订单");
        return;
      }
      if (!this.$refs.orderTable.selectedRowsData.isOrderModify) {
        this.$message.warning("该订单不允许修改");
        return;
      }
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确定修改吗？";
      this.dataVisibleMode = true;
      this.type = "5";
    },
    rightClick1(e, text, record) {
      let event = e.target;
      if (e.target.localName != "td") {
        event = e.target.parentNode;
      }
      if (e.target.parentNode.localName == "span") {
        event = e.target.parentNode.parentNode.parentNode;
      }
      if (e.target.localName == "path" || e.target.localName == "svg") {
        event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode;
        if (event.className.indexOf("tagNum") != -1) {
          event = e.target.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
        }
      }
      this.text = event.innerText;
      if (event.className.indexOf("noCopy") != -1 || !this.text) {
        this.showText = false;
      } else {
        this.showText = true;
      }
      if (event.cellIndex == 1 || event.cellIndex == undefined) {
        this.text = this.text.split("\n")[0];
      }
      const tableWrapper = this.$refs.tableWrapper;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      this.menuVisible = true;
      let offsetx = event.offsetLeft + event.offsetWidth - 10;
      let offsety = event.offsetTop + 40;
      if (event.className.indexOf("noCopy1")) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 880 + "px";
      }
      if (event.cellIndex == this.columns1.length - 1) {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + 60 + "px";
      } else {
        this.menuStyle.top = cellRect.top - wrapperRect.top + 4 + "px";
        this.menuStyle.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      }
      if (event.cellIndex == 0 || event.cellIndex == 1) {
        this.menuStyle.top = offsety + "px";
        this.menuStyle.left = offsetx + "px";
      }
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      (this.menuStyle = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick);
    },
    // 右键事件
    // bodyClick() {
    //   this.menuVisible = false;
    //   document.body.removeEventListener("click", this.bodyClick);
    // },
    // rightClick(e){
    //   this.menuVisible = true;
    //   this.menuStyle.top = e.clientY- 110 +  "px";
    //   this.menuStyle.left = e.clientX -
    //       document.getElementsByClassName('fixed-side')[0].offsetWidth  + "px";
    //   document.body.addEventListener("click", this.bodyClick);
    // },
    // // 下载文件
    // down0(){
    //   window.location.href = this.$refs.orderTable.menuData.pcbFilePath
    //   // console.log('this.$refs.orderTable.menuData.pcbFilePath',this.$refs.orderTable.menuData.pcbFilePath)
    // },
    // 下载CAM文件
    down1() {
      // if(!this.$refs.orderTable.selectedRowKeysArray[0]){
      //   this.$message.warning("请选择订单")
      //   return
      // }
      DownloadCAMFile(this.$refs.orderTable.menuData.proOrderId).then(res => {
        if (res.code) {
          console.log(res.data);
          window.location.href = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 行点击事件
    onClickRow(record) {
      return {
        on: {
          click: () => {
            this.proOrderId = record.proOrderId;
          },
        },
      };
    },
    isRedRow(record) {
      if (record.proOrderId == this.proOrderId) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    noteHtml(record) {
      let cnNoteRegStr = "";
      let noteRegStr = "";
      if (this.cnNote && this.cnNote.indexOf("https://admin.jiepei.com/") == -1) {
        cnNoteRegStr = this.cnNote.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${"http://admin.jiepei.com/"}$2$3`);
      } else {
        cnNoteRegStr = this.cnNote;
      }

      if (this.note && this.note.indexOf("https://admin.jiepei.com/") == -1) {
        noteRegStr = this.note.replace(/(<img [^>]*src=['"])([^'"]+)([^>]*>)/g, `$1${"http://admin.jiepei.com/"}$2$3`);
      } else {
        noteRegStr = this.note;
      }
      // <div class="${record.errChk_ ? '' : 'displayFlag'}"><p>分析项</p><div>${record.errChk_}</div></div>
      //  <div class="${record.errMsg_ ? '' : 'displayFlag'}"><p>输出提示</p><div>${record.errMsg_}</div></div>
      let str_ = `<div class="${this.note ? "divClass" : "displayFlag"}"><p>客户备注</p><div  class="imgTable">${noteRegStr}</div></div>
                  <div class="${
                    this.cnNote ? "divClass" : "displayFlag"
                  }"><p>业务员备注</p><div  class="imgTable">${cnNoteRegStr}</div></div>                  
                  <div class="${record.autoTaskMsg ? "divClass" : "displayFlag"}"><p>直通提示</p><div>${record.autoTaskMsg}</div></div>
                  <div class="${record.outTaskMsg ? "divClass" : "displayFlag"}"><p>输出提示</p><div>${record.outTaskMsg}</div></div>
                  <div class="${record.specialRemarks ? "divClass" : "displayFlag"}"><p>工程指示</p><div>${record.specialRemarks}</div></div>
                `;
      this.allNote = str_;
    },

    jigsawPuzzleClick(record) {
      this.makeupVisible = true;
      console.log(record);
      this.$nextTick(() => {
        this.$refs.makeup.impositionInformationExample(
          record.boardHeight,
          record.boardWidth,
          record.pinban_x || 1,
          record.pinban_y || 1,
          record.processeEdge_x || "none",
          record.processeEdge_y || 0,
          record.vCut || "none",
          record.cao_x || 0,
          record.cao_y || 0
        );
      });
    },
    handleCancel(e) {
      this.makeupVisible = false;
    },
    // 查看日志
    viewLogClick(payload) {
      let params = payload.proOrderId;
      if (params) {
        getViewLog(params).then(res => {
          if (res.code) {
            this.MsgSort(res.data);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    // 排序
    MsgSort(obj) {
      obj.sort((a, b) => {
        let t1 = new Date(Date.parse(a.createTime.replace(/-/g, "/")));
        let t2 = new Date(Date.parse(b.createTime.replace(/-/g, "/")));
        return t2.getTime() - t1.getTime();
      });
      // console.error('obj',obj)
      this.dataVisible8 = true;
      return (this.viewLogData = obj);
    },
    handleOk8() {
      this.dataVisible8 = false;
    },
    // updateUrl(urlPath){
    //   let _this = this;
    //   if (urlPath.indexOf('sockjs') != -1) {
    //     _this.wsUrl = 'http://'  + urlPath  ;
    //   } else {
    //     if (window.location.protocol == 'http:') {
    //       _this.wsUrl = 'ws://' + urlPath  ;
    //     } else {
    //       _this.wsUrl = 'ws://' + urlPath  ;
    //     }
    //   }
    // },
    webSocketLink(val) {
      let _this = this;
      let proOrderId = val;
      let params = JSON.stringify({ Token: Cookie.get("Authorization"), Type: "backend", Task: "backend", Uid: proOrderId, Data: {} });
      let url = "genesiscam://?" + params;
      window.open(url, "_blank");
      setEngineeringBack({
        token: proOrderId,
      });
      this.getcookie("ordernoBack");
      // this.$axios({
      //     method: 'post',
      //     url: 'http://127.0.0.1:18188/',
      //     data: params,
      //   }).then((response) => {
      //     // 因为层级比较深，匿名函数会导致this指向发生改变
      //     // 这个时候使用箭头函数解决
      //     setEngineeringBack({
      //       token:proOrderId,
      //     });
      //       this.getcookie('ordernoBack')
      //     })

      // toCam(params).then(res=>{
      //   console.log('tocam2')
      // })

      // _this.websocket.onopen = function(){
      //   _this.websocket.send(JSON.stringify({"Token": Cookie.get('Authorization'),"Type": 'backend',"Task":"backend","Uid":proOrderId,'Data':{}}))
      //   // heartCheck.reset().start();
      // };
    },
    // 获取cookie缓存订单id
    getcookie(ordernoBack) {
      //获取指定名称的cookie的值
      var arrstr = document.cookie.split("; ");
      for (var i = 0; i < arrstr.length; i++) {
        var temp = arrstr[i].split("=");
        if (temp[0] == ordernoBack) {
          this.cookieId = unescape(temp[1]);
          // return unescape(temp[1]);
        }
      }
    },
    // 状态同步
    StatusSynchronization(record) {
      stateSync(record.proOrderId).then(res => {
        if (res.code == 1) {
          this.$message.success("状态同步成功");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 制作完成
    // async finishClick(code){
    //   if(!this.$refs.orderTable.selectedRowKeysArray[0]){
    //     this.$message.warning("请选择订单")
    //     return
    //   }
    //   this.code = code
    //   console.log('this.$refs.orderTable.selectedRowKeysArray[0]',this.$refs.orderTable.selectedRowKeysArray[0])
    //   await modifyPar2(this.$refs.orderTable.selectedRowKeysArray[0]).then(res =>{
    //     if(res.code){
    //       this.ParameterData2 = res.data
    //       this.ParameterData2.Id = this.$refs.orderTable.selectedRowKeysArray[0]
    //       this.dataVisible10 = true
    //       console.log('this.ParameterData2',this.ParameterData2)
    //     }else{
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    handleOk10() {
      let form = this.$refs.ProductionCompleted.ParameterForm;
      let params = {
        FactoryNo: form.factoryNo ? "true" : "false",
        IsShuChu: form.isShuChu ? "true" : "false",
        VCutToWX: form.vCutToWX ? "true" : "false",
        PosNeg: form.posNeg || "",
        OilBlockGreaterThan20_20: form.oilBlockGreaterThan20_20 ? "true" : "false",
        BMCLToWZ: form.bMCLToWZ ? "true" : "false",
        MinLineWS: form.minLineWS || "",
        InnerMinLineWS: form.innerMinLineWS || "",
        ThickCopperBaseThickness: form.thickCopperBaseThickness || "",
        BgalcSmallAlert: form.bgalcSmallAlert ? "true" : "false",
        VCut: form.vCut ? "true" : "false",
        CounterboreCopper: form.counterboreCopper ? "true" : "false",
        CounterboreNoCopper: form.counterboreNoCopper ? "true" : "false",
        Etching2RL: form.etching2RL ? "true" : "false",
        FakeDoubleSide: form.fakeDoubleSide ? "true" : "false",
        Special2RL: form.special2RL ? "true" : "false",
        IsPlankWatermark: form.isPlankWatermark ? "true" : "false",
        IsSecant: form.isSecant ? "true" : "false",
        // 'msg_':formData.msg_ || '',
      };
      params.Id = this.$refs.orderTable.selectedRowKeysArray[0];
      params.Path = this.$refs.ProductionCompleted.ParameterForm.path;
      if (!params.Path) {
        this.$message.error("请上传文件");
        return;
      }
      finish(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("制作完成");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
      this.dataVisible10 = false;
    },
    // 下载附件文件
    downFile(record) {
      window.location.href = record;
    },
    // 删除注意事项
    delFile(record) {
      console.log("删除注意事项", record);
      this.orderno = this.$refs.orderTable.selectedRowsData.orderNo;
      this.messageMode = "确认删除此条注意事项吗？";
      this.type = "3";
      this.recordId = record.id;
      this.dataVisibleMode = true;
    },
    // 绩效管理
    PerformanceClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.selectedRowsData = this.$refs.orderTable.selectedRowsData;
      this.$refs.performanceModal.openModal(this.selectedRowsData);
    },
    editClick(record) {
      this.dataVisibleMatter = true;
      this.editData = record;
    },
    // 注意事项
    handleOkMatter() {
      this.confirmLoading = true;
      let params = this.$refs.mattersNeedingAttention.form;
      params.id = this.editData.id;
      params.isbefor = false;
      this.spinning = true;
      // console.log('params',params)
      repierMatters(params)
        .then(res => {
          if (res.code) {
            this.$message.success("保存成功");
          } else {
            this.$message.error(res.message);
          }
          this.getOrderList();
          // this.getJobInfo(this.$refs.orderTable.selectedRowKeysArray[0])
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisibleMatter = false;
        });
    },
    expandIcon1() {
      if (this.foldedornot) {
        return (
          <a>
            <a-icon type="right" style="margin-right:5px" />
          </a>
        );
      } else {
        return (
          <a>
            <a-icon type="left" style="margin-right:5px" />
          </a>
        );
      }
    },
    CollapseList(val) {
      const elements1 = document.getElementsByClassName("centerTable");
      const elements2 = document.getElementsByClassName("rightTable");
      if (val.length) {
        this.foldedornot = true;
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.width = "100%";
          elements1[index].style.display = "";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.width = "50%";
        }
      } else {
        this.foldedornot = false;
        for (let index = 0; index < elements1.length; index++) {
          elements1[index].style.display = "none";
        }
        for (let index = 0; index < elements2.length; index++) {
          elements2[index].style.width = "100%";
        }
      }
    },
  },
  // beforeDestroy () {
  //   if(this.websocket){
  //     this.websocket.close()
  //   }
  //   this.connection.off('ReceiveMessage')
  // }
};
</script>

<style scoped lang="less">
/deep/.ant-pagination-prev {
  margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input {
  padding: 0;
  margin: 0;
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager {
  margin: 0;
}
/deep/.ant-pagination-slash {
  margin: 0;
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-table-placeholder {
  padding: 0;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 0 0 0 16px;
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
  font-size: 12px;
  margin-left: -17px;
  margin-top: 27px;
}
/deep/.ant-collapse > .ant-collapse-item {
  border: none;
  border-top: 2px solid rgb(233, 233, 240);
}
/deep/.ant-collapse {
  background-color: white;
  border-width: 0 2px 4px 0;
  border-style: solid;
  border-color: #e9e9f0;
  border-radius: inherit;
  // width:50%;
  // border-right: 2px solid rgb(233, 233, 240);
  // border:none
}
/deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding: 16px 0px 18px 0px;
  padding-left: 15px;
  padding-top: 0px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  // margin-left: -15px;
  margin-top: -19px;
}
// /deep/.ant-col-18{
//   width: 100%;
// }
.tabRightClikBox1 {
  li {
    height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    background-color: white !important;
    color: #000000;
  }
}
/deep/.ant-select-dropdown-menu-item {
  color: #000000;
  font-weight: 500;
}

/deep/.ant-input {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  color: #000000;
  font-weight: 500;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}
.projectBackend {
  background: #ffffff;
  /deep/ .ant-table-empty .ant-table-body {
    overflow-x: hidden !important;
    overflow-y: hidden !important;
  }
  /deep/.left1 {
    width: 52%;
  }
  /deep/.left2 {
    width: 100%;
  }
  /deep/.leftContent {
    //height:780px;
    .min-table {
      .ant-table-body {
        min-height: 738px;
      }
    }
    .ant-table-body {
      .ant-table-fixed {
        width: 1200px !important;
      }
    }
    .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      .ant-menu-item:not(:last-child) {
        margin-bottom: 4px;
      }
    }
    border: 2px solid rgb(233, 233, 240);
    border-bottom: 4px solid rgb(233, 233, 240);
  }

  /deep/ .rightContent {
    width: 48%;
    display: flex;
    flex-wrap: nowrap;
    // flex-wrap: wrap;
    align-content: flex-start;
    .centerTable {
      .peopleTag {
        position: absolute;
        font-size: 12px;
        font-weight: 600;
        left: 26px;
        padding: 0 2px;
      }
      .min-table {
        .ant-table-body {
          min-height: 704px !important;
        }
      }

      width: 50%;
      //height:774px;
      // height:780px;
      border: 2px solid rgb(233, 233, 240);
      border-top: 0;
      border-bottom: 0;
      border-right: 0;
      // border-bottom: 4px solid rgb(233, 233, 240);
    }
    .rightTable {
      // .ant-table-body{
      //   max-height:715px!important;
      // }
      width: 50%;
      .note {
        height: 288px;
        overflow: auto;
      }
      .jobNote {
        height: 253px;
        border-top: 1px solid #e9e9f0;
        /deep/ .ant-table-wrapper {
          user-select: all;
          height: 250px;
        }
        .minTable {
          .ant-table-body {
            min-height: 218px;
            border-bottom: 2px solid #e9e9f0;
          }
        }
      }
      .jobNote1 {
        //height:233PX;
        border-top: 1px solid #e9e9f0;
        /deep/ .ant-table-wrapper {
          user-select: all;
          height: 213px;
        }
        .minTable {
          .ant-table-body {
            min-height: 738px;
            // border-bottom: 2px solid #e9e9f0;
          }
        }
      }
      .peopleTag {
        margin: 0;
        padding: 0;
        width: 24px;
        border-radius: 12px;
        background: #2d221d;
        border-color: #2d221d;
        color: #ff9900;
        text-align: center;
        margin-left: 2px;
      }
    }
  }
  .footerAction {
    width: 100%;
    height: 48px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }
  /deep/ .ant-tabs {
    .ant-tabs-bar {
      margin: 0;
      height: 36px;
    }
    .ant-tabs-nav-container {
      height: 36px;
      .ant-tabs-tab {
        margin: 0;
        height: 36px;
        line-height: 36px;
      }
    }
  }
  /deep/ .ant-table-fixed {
    //.ant-table-selection-col {
    //  width:20px
    //}
    /deep/ .ant-table {
      .fontRed {
        td {
          color: #dc143c;
        }
      }
      .eqBackground {
        background: #ffff00;
      }
      .statuBackground {
        background: #b0e0e6;
      }
      .backUserBackground {
        background: #a7a2c9;
      }
    }
    .ant-table-row-selected {
      td {
        background: #dfdcdc;
      }
    }
    /deep/.userStyle {
      user-select: all;
    }
    //td[class~='userStyle'] {
    //  user-select: all !important;
    //}
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  // /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) >td:first-child {
  //   border-left:2px solid #ff9900!important;
  // }
  /deep/ .ant-table-thead > tr > th {
    padding: 7px 2px !important;
    .ant-table-column-sorter {
      display: none;
    }
  }
  /deep/ .ant-table {
    .ant-table-thead > tr > th {
      padding: 7px 2px;
      border-right: 1px solid #efefef;
      // border-color: #f0f0f0;
    }
    .ant-table-tbody > tr > td {
      padding: 7px 2px !important;
      border-right: 1px solid #efefef;
      // border-color: #f0f0f0;
    }
    tr.ant-table-row-selected td {
      background: #dfdcdc;
    }
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
    .rowBackgroundColor {
      background: #dfdcdc !important;
    }

    .ant-table-selection-col {
      width: 20px !important;
    }
  }

  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      .ant-card-head-title {
        padding: 0;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
    }
  }
  /deep/ .ant-input-disabled {
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 14px 0 0 10px;
    position: fixed;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
</style>
<style lang="less">
.note {
  .textNote {
    background: #d6d6d6;
    word-wrap: break-word;
    max-height: 288px;
    overflow-y: auto;
    .imgTable {
      img {
        width: 100px;
        height: 50px;
      }
    }
    p {
      height: 100%;
      line-height: 35px;
      font-weight: 700;
      margin: 0;
    }
    .divClass {
      margin: 0 5px;
      padding-top: 4px;
    }
    .displayFlag {
      display: none;
    }
  }
}
</style>
