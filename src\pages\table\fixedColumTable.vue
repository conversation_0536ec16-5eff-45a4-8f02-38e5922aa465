<template>
  <page-layout>
    <a-card>
      <standard-table
        rowKey="key"
        :columns="columns"
        :dataSource="data"
        :pagination="pagination"
        :scroll="scroll"
      >
        <a slot="action" href="javascript:;">action</a>
      </standard-table>
    </a-card>
  </page-layout>
</template>
<script>
import PageLayout from "@/layouts/PageLayout";
import StandardTable from "@/components/table/StandardTable";
const columns = [
  {
    title: "Full Name",
    width: 100,
    dataIndex: "name",
    key: "name",
    fixed: "left"
  },
  { title: "Age", width: 100, dataIndex: "age", key: "age", fixed: "left" },
  { title: "Column 1", dataIndex: "address", key: "1" },
  { title: "Column 2", dataIndex: "address", key: "2" },
  { title: "Column 3", dataIndex: "address", key: "3" },
  { title: "Column 4", dataIndex: "address", key: "4" },
  { title: "Column 5", dataIndex: "address", key: "5" },
  { title: "Column 6", dataIndex: "address", key: "6" },
  { title: "Column 7", dataIndex: "address", key: "7" },
  { title: "Column 8", dataIndex: "address", key: "8" },
  {
    title: "Action",
    key: "operation",
    fixed: "right",
    width: 100,
    scopedSlots: { customRender: "action" }
  }
];

const data = [
  {
    key: "1",
    name: "John Brown",
    age: 32,
    address: "New York Park"
  },
  {
    key: "2",
    name: "Jim Green",
    age: 40,
    address: "London Park"
  }
];

export default {
  components: { PageLayout, StandardTable },
  data() {
    return {
      data,
      columns,
      pagination: this.$store.state.setting.pagination,
      scroll: { x: 1300 }
    };
  }
};
</script>
