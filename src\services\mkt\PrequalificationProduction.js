import { request, METHOD } from "@/utils/request";
// 预审分页接口
// export function projectMakeOrderList (params) {
//     return request("/api/app/pcb-order/make-page-list", METHOD.GET,params)
// }
export function projectMakeOrderList(params) {
  return request("/api/app/pcb-order-list/pre-page-list", METHOD.GET, params);
}
// 获取订单详情
export function projectBackEndOrderDetail(id) {
  return request(`/api/app/pcb-order/${id}/by-id`, METHOD.GET);
}
// 取单
export function TakeOrderList() {
  return request("/api/app/order-pre-button/check-order", METHOD.GET);
}
// 开始
export function MakeStart(id) {
  return request(`/api/app/order-pre-button/${id}/pre-start-order`, METHOD.POST);
}
// 获取客户信息
export function getCustomerInfo(CustNo, factory, type, businessOrderNo, orderNo) {
  return request(
    `/api/app/order-pre-button/rule-show-info?CustNo=${CustNo}&factory=${factory}&type=${type}&businessOrderNo=${businessOrderNo}&orderNo=${orderNo}`,
    METHOD.GET
  );
}
// 退单
export function preBackOrder(id) {
  return request(`/api/app/order-pre-button/${id}/pre-back-order`, METHOD.POST);
}
// 解除警告
export function setreleasewarning(Id) {
  return request(`/api/app/order-pre-button/set-release-warning/${Id}`, METHOD.POST);
}
// 今日做单情况
export function ordercheckmakeinfo() {
  return request(`/api/app/order-check/make-info`, METHOD.GET);
}
export function verifyonlineeCN(Id, OnLineOrRecordEcn) {
  return request(`api/app/pcb-order/${Id}/verify-on-line-eCN?OnLineOrRecordEcn=${OnLineOrRecordEcn}`, METHOD.POST);
}
// 文件追加
export function fileswereadded(Id, file) {
  return request(`/api/app/order-pre-button/files-were-added/${Id}`, METHOD.POST, file);
}
// 预审完成
export function getModifyInformation(id) {
  return request(`/api/app/order-pre-button/${id}/pre-finished-order`, METHOD.POST);
}
// 后台开料
export function prebackgroudkL(id) {
  return request(`/api/app/order-pre-button/${id}/preback-groud-kL`, METHOD.POST);
}
// 图形预览
export function graphicPreview(Id) {
  return request(`/api/app/graphic/order-graphic-preview/${Id}`, METHOD.POST);
}
// 图形预览2（ipcb任务监控）
export function graphicPreview2(Id) {
  return request(`/api/app/graphic/order-graphic-preview2/${Id}`, METHOD.POST);
}
// 图形预览3（新ipcb任务监控）
export function graphicPreview3(Id) {
  return request(`/api/app/graphic/order-graphic-preview3/${Id}`, METHOD.POST);
}
//文件列表
export function ipCBTaskoutput(params) {
  return request(`/api/app/sight/i-pCBTask-output`, METHOD.GET, params);
}
// 上传文件
export function uploadPCBFile(id, file) {
  return request(`/api/app/pcb-order/${id}/upload-pCBFile`, METHOD.POST, file);
}
// 返修完成
export function RepairCompleted(Id) {
  return request(`/api/app/engineering-production/fix-finish/${Id}`, METHOD.POST);
}
// 获取问客地址
export function getWenkeUrl(Id) {
  return request(`/api/app/engineering-production/wen-ke-url/${Id}`, METHOD.GET);
}
// 获取制作标准地址
export function getProductionStandard() {
  return request(`/api/app/engineering-production/fabricati-url`, METHOD.GET);
}
// 上传图片返回地址
export async function UploadFile(params) {
  return request(`/api/app/e-mSTProc-dRMake/up-load-drill-file`, METHOD.POST, params);
}
//订单接入
export function pcbordertonew(params) {
  return request(`/api/app/nope-button/pcborder-to-new`, METHOD.POST, params);
}

// 获取工厂Id列表
export async function getFactoryList() {
  return request(`/api/app/e-mSTPub-factory-configure/factory-id-and-code-list`, METHOD.POST);
}
// 注意事项
export async function mattersNeedingAttention(params) {
  return request(`/api/app/engineering-production/matters-needing-attention`, METHOD.POST, params);
}
// 获取参数
export async function getParameter(Id) {
  return request(`/api/app/engineering-production/modify-par/${Id}`, METHOD.GET);
}
// 获取底层铜厚
export async function getBtoParameter(params) {
  return request(`/api/app/e-mSData-class-list/data-class-list/?TypeNo=${params}`, METHOD.POST);
}
// 参数保存
export async function SaveParameter(params) {
  return request(`/api/app/engineering-production/modify-par`, METHOD.POST, params);
}
// 生成叠层
export async function getGenerateStack(Id) {
  return request(`/api/app/engineering-make/auto-stack/${Id}`, METHOD.GET);
}
// 叠层修改保存
export async function saveRepairRecord(params) {
  return request(`/api/app/engineering-production/auto-stack-double-click`, METHOD.POST, params);
}
// 返修记录
export async function getRepairRecord(Id) {
  return request(`/api/app/engineering-production/fix-record/${Id} `, METHOD.GET);
}
// 作业记录
export function projectBackEndJobInfo(id) {
  return request(`/api/app/engineering-backend/information-transfer/${id}`, METHOD.GET);
}
//预审EQ发送邮件
export function sendeMimeeq(params) {
  return request(`/api/app/e-mime/send-eMime-eq`, METHOD.POST, params);
}
// 叠层阻抗
export function getStackImpedance(Id) {
  return request(`/api/app/engineering-make/imp/${Id}`, METHOD.GET);
}
// 开料拼版
export function getCutting(Id) {
  return request(`/api/app/engineering-make/auto-tool-pnl/${Id}`, METHOD.GET);
}
// 上传GERBER文件
export function UpGerber(Id) {
  return request(`/api/app/engineering-production/up-gerber-file/${Id}`, METHOD.POST);
}
// 下载GERBER文件
export function downGerber(Id) {
  return request(`/api/app/engineering-production/down-load-path/${Id}`, METHOD.GET);
}
// 客户规则录入
export function getRuleEntry(Id) {
  return request(`/api/app/engineering-make/att-info/${Id}`, METHOD.GET);
}
// 查看日志
export function getViewLog(params) {
  return request(`/api/app/pro-order-log?OrderId=${params}`, METHOD.GET);
}
// 下载文件  /api/app/engineering-production/down-file/{Id}
export function downFile(Id) {
  return request(`/api/app/engineering-qae/down-file/${Id}`, METHOD.GET);
}
// 订单拆分
export async function getOrderSplit(params) {
  return request(`/api/app/pcb-order-split/order-split`, METHOD.POST, params);
}
//s数量拆分
export async function ordernumsplit(params) {
  return request(`/api/app/pcb-order-split/order-num-split`, METHOD.POST, params);
}
//合同核查
export async function contractprecheck(ids) {
  return request(`/api/app/order-check/contract-pre-check`, METHOD.POST, ids);
}
export default {
  projectMakeOrderList,
  TakeOrderList,
  MakeStart,
  getModifyInformation,
  prebackgroudkL,
  uploadPCBFile,
  projectBackEndOrderDetail,
  projectBackEndJobInfo,
  getFactoryList,
  pcbordertonew,
  RepairCompleted,
  getWenkeUrl,
  getProductionStandard,
  UploadFile,
  mattersNeedingAttention,
  getParameter,
  SaveParameter,
  getBtoParameter,
  getGenerateStack,
  saveRepairRecord,
  getRepairRecord,
  getStackImpedance,
  getCutting,
  getCustomerInfo,
  getRuleEntry,
  UpGerber,
  downGerber,
  getViewLog,
  downFile,
};
