<template>
  <a-card>
    <a-alert message="默认选中第一项。" type="warning" class='alert' show-icon/>
    <a-tabs default-active-key="1" @change="callback">
      <a-tab-pane key="1" tab="Tab 1">
        Content of Tab Pane 1
      </a-tab-pane>
      <a-tab-pane key="2" tab="Tab 2" force-render>
        Content of Tab Pane 2
      </a-tab-pane>
      <a-tab-pane key="3" tab="Tab 3">
        Content of Tab Pane 3
      </a-tab-pane>
    </a-tabs>
    <a-alert message="有图标的标签" type="warning" class='alert' show-icon/>
    <a-tabs default-active-key="2">
        <a-tab-pane key="1">
        <span slot="tab">
            <a-icon type="apple" />
            Tab 1
        </span>
        Tab 1
        </a-tab-pane>
        <a-tab-pane key="2">
        <span slot="tab">
            <a-icon type="android" />
            Tab 2
        </span>
        Tab 2
        </a-tab-pane>
    </a-tabs>
    <a-alert message="可以左右、上下滑动，容纳更多标签。" type="warning" class='alert' show-icon/>
    <a-radio-group v-model="mode" :style="{ marginBottom: '8px' }">
      <a-radio-button value="top">
        Horizontal
      </a-radio-button>
      <a-radio-button value="left">
        Vertical
      </a-radio-button>
    </a-radio-group>
    <a-tabs
      default-active-key="1"
      :tab-position="mode"
      :style="{ height: '200px' }"
      @prevClick="callback"
      @nextClick="callback"
    >
      <a-tab-pane v-for="i in 30" :key="i" :tab="`Tab-${i}`"> Content of tab {{ i }} </a-tab-pane>
    </a-tabs>
    <a-alert message="另一种样式的页签，不提供对应的垂直样式。" type="warning" class='alert' show-icon/>
    <a-tabs type="card" @change="callback">
        <a-tab-pane key="1" tab="Tab 1">
        Content of Tab Pane 1
        </a-tab-pane>
        <a-tab-pane key="2" tab="Tab 2">
        Content of Tab Pane 2
        </a-tab-pane>
        <a-tab-pane key="3" tab="Tab 3">
        Content of Tab Pane 3
        </a-tab-pane>
    </a-tabs>
    <a-alert message="隐藏默认的页签增加图标，给自定义触发器绑定事件" type="warning" class='alert' show-icon/>
    <div :style="{ marginBottom: '16px' }">
      <a-button @click="add">
        ADD
      </a-button>
    </div>
    <a-tabs v-model="activeKey" hide-add type="editable-card" @edit="onEdit">
      <a-tab-pane v-for="pane in panes" :key="pane.key" :tab="pane.title" :closable="pane.closable">
        {{ pane.content }}
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>
<script>
export default {
  data() {
    const panes = [
      { title: 'Tab 1', content: 'Content of Tab 1', key: '1' },
      { title: 'Tab 2', content: 'Content of Tab 2', key: '2' },
    ]
    return {
        mode: 'top',
        activeKey: panes[0].key,
        panes,
        newTabIndex: 0,
    };
  },
  methods: {
    callback(key) {
      console.log(key);
    },
    onEdit(targetKey, action) {
      this[action](targetKey);
    },
    add() {
      const panes = this.panes;
      const activeKey = `newTab${this.newTabIndex++}`;
      panes.push({
        title: `New Tab ${activeKey}`,
        content: `Content of new Tab ${activeKey}`,
        key: activeKey,
      });
      this.panes = panes;
      this.activeKey = activeKey;
    },
    remove(targetKey) {
      let activeKey = this.activeKey;
      let lastIndex;
      this.panes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      const panes = this.panes.filter(pane => pane.key !== targetKey);
      if (panes.length && activeKey === targetKey) {
        if (lastIndex >= 0) {
          activeKey = panes[lastIndex].key;
        } else {
          activeKey = panes[0].key;
        }
      }
      this.panes = panes;
      this.activeKey = activeKey;
    },
  },
};
</script>
<style lang="less" scoped>
.alert{
    margin-bottom:10px;
    margin-top: 10px
}
</style>