<!-- 物料管理-型号录入 -->
<template>
  <a-spin :spinning="spinning">
    <div style="background: #FFFFFF;">
      <div class="content">
        <div class="left" >
          <a-table
              rowKey="id"
              :columns="columns1"
              class="leftstyle"
              :dataSource="data1Source"
              :pagination="false"
              :loading="table1Loading"
              :bordered="true"
              :maskClosable="false"
              :scroll="{ y:727,x:700 }"
              :customRow="eventTouch1"
              :rowClassName="setRowClassName1"
          >
          </a-table>
        </div>
        <div class="right" >
          <a-card :bordered="false" style="border: 1px solid #E9E9F0;" class="top"  >
            <a-table
                rowKey="id"
                :columns="columns2"
                :dataSource="data2Source"
                :pagination="false"
                class="topstyle"
                :loading="table2Loading"
                :bordered="true"
                :maskClosable="false"
                :scroll="{ y:339,x:500 }"
            >
            </a-table>
          </a-card>
          <a-card :bordered="false" style="border: 1px solid #E9E9F0;" class="bot" >
            <a-table
                rowKey="id"
                :columns="columns3"
                :dataSource="data3Source"
                :pagination="false"
                :loading="table3Loading"
                class="botstyle"
                :bordered="true"
                :maskClosable="false"
                :scroll="{ y:344,x:500 }"
            >
            </a-table>
          </a-card>
        </div>
      </div>
      <div class="actionBox">
        <action
            ref="Action"
            @queryClick="queryClick"
            @pcbSetfile="pcbSetfile"
            @addCategory="addCategory"
        >
        </action>
      </div>
    </div>
    <!-- 查询弹窗 -->
    <a-modal
        title="查询"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        centered
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
    >
      <query-info ref='queryInfo' />
    </a-modal>
    <!-- 厂商建档 -->
    <a-modal
        title="厂商录入"
        :visible="dataVisible"
        centered
        @cancel="reportHandleCancel"
        @ok="handleOk"
        ok-text="新增"
        destroyOnClose
        :maskClosable="false"
        :width="500"
    >
      <pcbSetfile ref='pcbSetfile' :factory="factory"  :proOptions='proOptions' :proOptions1='proOptions1' />
    </a-modal>
    <!-- 添加类别 -->
    <a-modal
        title="型号录入"
        :visible="dataVisible5"
        @cancel="reportHandleCancel"
        centered
        @ok="handleOk5"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
    >
      <add-category ref='addCategory'
        :pptype='pptype'
        :tgtype='tgtype'
        :materialCategory2='materialCategory2'
        :producttype='producttype'
        :factory='factory'
      />
    </a-modal>
  </a-spin>
</template>

<script>
import {factroyList,} from "@/services/analysis";
import {coreType, ppType, vendorNo} from "@/services/category";
import Action from "@/pages/materialmodule/category/modules/Action";
import QueryInfo from "@/pages/materialmodule/category/modules/QueryInfo";
import PcbSetfile from "@/pages/materialmodule/MaterialEntry/module/PcbSetfile";
import AddCategory from "@/pages/materialmodule/MaterialEntry/module/AddCategory";
import {
  addfl,
  addPcbMessage,
  getfactory,
  getkind, getPPtype,
  getproducttype,
  getsupplier,
  getTgtype
} from "@/services/mkt/materialmodule";

const columns1 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 60,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell:(record) =>{
      if(record.color_ == '#FF0000'){
        return {style:{'background':'#FF0000'}}
      }
    }
  },
  {
    title: "厂商名称",
    dataIndex: "verdorName_",
    width: 130,
    ellipsis: true,
    className:"orderClass",
    align: 'center',

  },
  {
    title: "厂商地址",
    width: 130,
    ellipsis: true,
    dataIndex: "address_",
    align: 'center',
  },
  {
    title: "厂商电话",
    dataIndex: "phone_",
    width: 110,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "电子邮箱",
    width: 150,
    dataIndex: "email_",
    ellipsis: true,
    align: 'center',
  },
  {
    title: "客户网址",
    dataIndex: "website_",
    width:150,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "发票地址",
    dataIndex: "invoaddr_",
    width:85,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "厂商分类",
    dataIndex: "kind_",
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: "厂商级别",
    dataIndex: "class_",
    width: 60,
    ellipsis: true,
    align: 'center',
  },

];
const columns2 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 50,
    align: 'center',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell:(record) =>{
      if(record.color_ == '#FF0000'){
        return {style:{'background':'#FF0000'}}
      }
    }
  },
  {
    title: "名称",
    dataIndex: "coreType_",
    width: 150,
    ellipsis: true,
    className:"orderClass",
    align: 'left',
    // fixed: 'left'

  },
  {
    title: "产品类别",
    width: 90,
    ellipsis: true,
    dataIndex: "category_",
    align: 'left',
  },
  {
    title: "TG值",
    dataIndex: "tgValue_",
    width: 60,
    ellipsis: true,
    align: 'left',
  },
  {
    title: "关联化片",
    dataIndex: "ppTypeCodes_",
    width: 100,
    ellipsis: true,
    align: 'left',
  },
  {
    title: "层数",
    dataIndex: "layers",
    width: 60,
    ellipsis: true,
    align: 'left',
  },
  {
    title: "工厂",
    dataIndex: "joinFactoryId",
    width: 80,
    ellipsis: true,
    align: 'left',
  },

];
const columns3 = [
  {
    dataIndex: "index",
    title: '序号',
    slots: { title: 'customTitle' },
    key: 'index',
    width: 50,
    align: 'center',
    // fixed: 'left',
    scopedSlots: {customRender: 'index'},
    customRender: (text,record,index) => `${index+1}`,
    customCell:(record) =>{
      if(record.color_ == '#FF0000'){
        return {style:{'background':'#FF0000'}}
      }
    }
  },
  {
    title: "名称",
    dataIndex: "ppType_",
    width: 240,
    ellipsis: true,
    className:"orderClass",
    align: 'left',
  },
  {
    title: "工厂",
    dataIndex: "joinFactoryId",
    width: 300,
    ellipsis: true,
    align: 'left',
  },

];
export default {
  name: "category",
  components: {Action,QueryInfo,PcbSetfile,AddCategory},
  data(){
    return{
      spinning:false,
      columns1,
      columns2,
      columns3,
      data1Source:[],
      data2Source:[],
      data3Source:[],
      table1Loading:false,
      table2Loading:false,
      table3Loading:false,
      key1:'',
      dataVisible:false,
      dataVisible4:false,
      dataVisible5:false,
      proOptions:[],
      proOptions1:[],
      pptype:[],//对应pp类型
      tgtype:[],//对应tg类型
      materialCategory2:[],//供应商
      producttype:[],//产品类别
      factory:[],//工厂
    }
  },
  created(){
   this.$nextTick(()=>{
    this.getvendorNo();
    this.getproKey();
    this.getproKey1();
    this.getfactory1();
    this.getproducttype1();
    this.getsupplier1();
    this.getTgtype1();
    this.getPPtype1();
    this.handleResize();
   })
  },
  mounted() {
    window.addEventListener('resize', this.handleResize, true)
  },
  beforeDestroy(){
    window.removeEventListener('resize', this.handleResize, true)
   },
  methods:{
    handleResize(){
      var mainstyle = document.getElementsByClassName('leftstyle')[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var topstyle = document.getElementsByClassName('topstyle')[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var botstyle = document.getElementsByClassName('botstyle')[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var leftContent = document.getElementsByClassName('left')[0]
      var top = document.getElementsByClassName('top')[0]
      var bot = document.getElementsByClassName('bot')[0]
      if(window.innerHeight<=911){
        leftContent.style.height = window.innerHeight - 145 +'px'
        top.style.height = (window.innerHeight - 140)/2-4 +'px'
        bot.style.height = (window.innerHeight - 140)/2-4 +'px'
      }else{
        leftContent.style.height = '763px'
        top.style.height ='381px'
        bot.style.height ='381px'
      }      
      if(mainstyle && this.data1Source.length!=0){
        mainstyle.style.height =  window.innerHeight - 185 +'px'
      }else{
        mainstyle.style.height = 0
      }
      if(topstyle && this.data2Source.length!=0){
        topstyle.style.height =  (window.innerHeight - 185)/2-20+'px'
      }else{
        topstyle.style.height = 0
      }
      if(botstyle && this.data3Source.length!=0){
        botstyle.style.height =  (window.innerHeight - 185)/2-20 +'px'
      }else{
        botstyle.style.height = 0
      }
    },
    // 获取厂商列表
    getvendorNo(paloyd){
      let params = ''
      if(paloyd){
        params = paloyd
      }
      this.table1Loading = true
      vendorNo(params).then(res=>{
        if(res.code){
          this.data1Source = res.data
          setTimeout(() => {this.handleResize()}, 0);
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.table1Loading = false
      })
    },
    // 点击事件配置
    eventTouch1(record,index) {
      return {
        props: {},
        on: { // 事件
          click: () => {
            this.key1= record.id;
            this.getcoreType(record.id)
            this.getppType(record.id)
          },
        }
      }
    },
    // 选中背景颜色
    setRowClassName1 (record)  {
      var classStr = ''
      // 单击设置背景色
      if(record.id == this.key1){
        classStr = classStr + 'bacStyle' + ' '
      }

      return classStr
    },
    // 获取板材
    getcoreType(paloyd){
      this.table2Loading = true
      coreType(paloyd).then(res=>{
        if(res.code){
          this.data2Source = res.data
          setTimeout(() => {this.handleResize()}, 0);
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.table2Loading = false
      })
    },
    // 获取pp
    getppType(paloyd){
      this.table3Loading = true
      ppType(paloyd).then(res=>{
        if(res.code){
          this.data3Source = res.data
          setTimeout(() => {this.handleResize()}, 0);
        }else{
          this.$message.error(res.message)
        }
      }).finally(()=>{
        this.table3Loading = false
      })
    },
    reportHandleCancel(){
      this.dataVisible = false;
      this.dataVisible4 = false;
      this.dataVisible5 = false;
    },
    // 获取厂商分类下拉
    getproKey() {
      this.loading = true;
      let proOptionsid=[122];
      getkind(proOptionsid).then((res) => {
        if(res.code){
          this.proOptions = res.data;
        }else{
          this.$message.error(res.message)
        }
      });
    },
    //获取厂商建档-[厂商级别]下拉列
    getproKey1() {
      this.loading = true;
      let proOptionsid=[121];
      getkind(proOptionsid).then((res) => {
        if(res.code){
          this.proOptions1 = res.data;
        }else{
          this.$message.error(res.message)
        }
      });
    },
    //获取添加类别-[对应工厂]下拉列
    getfactory1() {
      this.loading = true;
      // getfactory().then(res=>{
      //   if(res.code){
      //     this.factory = res.data;
      //   }else{
      //     this.$message.error(res.message)
      //   }
      // })
      factroyList().then(res =>{
      if(res.code){
        this.factory = res.data
      }else{
        this.$message.error(res.message)
      }
    })
    },
    // 获取添加分类对应Tg类别下拉
    getproducttype1() {
      this.loading = true;
      getproducttype().then((res) => {
        if(res.code){
          this.producttype = res.data;
        }else{
          this.$message.error(res.message)
        }
      });
    },
    //获取新增-[供应商]下拉列
    getsupplier1(){
      let params={
        'PageIndex': 1,
        'PageSize' : 9999,
      }
      getsupplier(params).then((res)=>{
        if(res.code){
          this.materialCategory2 = res.data.items;
        }else{
          this.$message.error(res.message)
        }
      });
    },
    // 获取添加分类对应Tg类别下拉
    getTgtype1() {
      this.loading = true;
      getTgtype().then((res) => {
        if(res.code){
          this.tgtype = res.data;
        }else{
          this.$message.error(res.message)
        }
      });
    },
    // 获取新增-半固化片-[PP类别]下拉
    getPPtype1() {
      this.loading = true;
      getPPtype().then((res) => {
        if(res.code){
          this.pptype = res.data;
        }else{
          this.$message.error(res.message)
        }
      });
    },
    // 查询
    queryClick(){
      this.dataVisible4 = true;
    },
    handleOk4(){
      let paloyd = this.$refs.queryInfo.queryForm.MaterSpec_
      this.getvendorNo(paloyd)
      this.dataVisible4 = false;
    },
    // 厂商建档1
    pcbSetfile(){
      this.dataVisible = true
    },
    handleOk(){    
      let queryData = this.$refs.pcbSetfile.form
      if(!queryData.verdorName_){
        this.$message.error('请输入厂商名称')
        return
      }
      this.spinning = true;
      addPcbMessage(queryData).then(res=>{
        if (res.code == 1) {
          this.$message.success('上传成功')
        }else {
          this.$message.error(res.message)
        }
      }).finally(()=> {
        this.spinning = false;
        this.dataVisible = false;
        this.getvendorNo();
      })
    },
    // 添加品类
    addCategory(){
      this.dataVisible5 = true;
    },
    handleOk5(){
      let queryData = this.$refs.addCategory.queryForm
      if(!queryData.type_){
        this.$message.error('请选择物料类别')
        return
      }
      queryData.coreDisplay_ = queryData.coreDisplay_ ? Number(queryData.coreDisplay_) : 99
      if(!queryData.Layers){
        queryData.Layers = null
      }
      if(queryData.type_ == '934'){
        if(!queryData.coreType_){
        this.$message.error('请输入CORE类别名称')
        return
      }
      if(!queryData.ppTypeCodes_){
        this.$message.error('请选择对应PP类别名称')
        return
      }
      if(!queryData.Category_){
        this.$message.error('请选择产品类别')
        return
      }
      if(!queryData.TGValue_){
        this.$message.error('请选择TG值')
        return
      }
      if(!queryData.VendorCodes_){
        this.$message.error('请选择供应商')
        return
      }
      if(!queryData.JoinFactoryId){
        this.$message.error('请选择工厂')
        return
      }
      }
      if(queryData.type_ == '935'){
        if(!queryData.ppType_){
        this.$message.error('请输入PP类别名称')
        return
      }
      if(!queryData.VendorCodes_){
        this.$message.error('请选择供应商')
        return
      }
      if(!queryData.JoinFactoryId){
        this.$message.error('请选择工厂')
        return
      }
      }
      this.spinning = true;
      addfl(queryData).then(res=>{
        if (res.code == 1) {
          this.$message.success('上传成功')
        }else {
          this.$message.error(res.message)
        }
      }).finally(()=> {
        this.spinning = false;
        this.dataVisible5 = false;
        this.getvendorNo();
      })
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.ant-table .ant-table-tbody > tr > td {
    height: 37.09px;
}
/deep/.ant-modal-title{
  color:#000000;
}
/deep/.ant-form-item-label > label{
  color:#000000;
}
/deep/.ant-select{
  color:#000000;
}
/deep/.ant-input{
  color:#000000;
}
/deep/.ant-cascader-picker{
  color:#000000;
}
/deep/.ant-select-selection--multiple .ant-select-selection__choice{
  color:#000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #F8F8F8;
}
/deep/.content {
  display: flex;
  border:2px solid #E9E9F0;
  .left{
    border-right:2px solid #E9E9F0;
    width:60%;
    height:100%;
    .bacStyle{
      background:#dfdcdc !important;
    }
  }
  .right{
    width:40%;
    height:100%;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
  .actionBox {
    overflow: hidden;
    width: 100%;
    height:47px;
    border: 2px solid #E9E9F0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    form {
    height: 100%;display: flex;
    align-items: center;
    justify-content: flex-end;
    }
  }
/deep/ .ant-card {
  .ant-card-head {
    min-height: 0;
    padding: 0;
    .ant-card-head-title {
      padding: 0;
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-weight: 500;
    }
  }
  .ant-card-body {
    padding: 0;
    .bacStyle{
      background:#dfdcdc !important;
    }
  }
}
/deep/ .ant-table-fixed{
  .ant-table-tbody{
    .ant-table-row {
      .orderClass{
        user-select: all;
      }
    }
  }
}
/deep/ .ant-table {
  .ant-table-selection-col {
    width: 30px;
  }
  tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
  tr.ant-table-row-hover td {
    background: #dfdcdc;
  }
}
/deep/ .ant-table-wrapper {
  .ant-table-thead {
    tr {
      th {
        padding: 7px 2px;
      }
    }
  }
  .ant-table-tbody {
    .ant-input {
      padding: 0;
      border: 0;
      border-radius: 0;
      margin: -5px 0;
      text-align: center;
    }
    tr {
      td {
        padding: 7px 2px;
      }
    }

  }
}

/deep/ .ant-table-body {
  // &::-webkit-scrollbar {
  //   //整体样式
  //   width: 6px; //y轴滚动条粗细
  //   height: 6px;
  // }

  // &::-webkit-scrollbar-thumb {
  //   //滑动滑块条样式
  //   border-radius: 2px;
  //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
  //   background: #ff9900;
  //   // #fff9e6
  // }
  .ant-table-thead {
    tr {
      th {
        padding: 0;
      }

    }
  }
  .ant-table-tbody {
    tr {
      td {
        padding:7px 2px;
      }
    }
  }
}

</style>
