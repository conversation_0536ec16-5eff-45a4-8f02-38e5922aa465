<template>
  <div ref="sb">
     <a-modal
    :title="form.id? '编辑' : '新增'"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    centered
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item  label="联系人" ref="contact" prop="contact">
          <a-input style="font-weight: 500;"
            v-model="form.contact"
            placeholder="联系人"
          />
        </a-form-model-item>
        <a-form-model-item  label="职位" ref="post" prop="post" :getPopupContainer="()=>this.$refs.sb" >
          <a-select   v-model="form.post">
            <a-select-option value="">
              请选择
            </a-select-option>
            <a-select-option value="董事长">
              董事长
            </a-select-option>
            <a-select-option value="总经理">
              总经理
            </a-select-option>
            <a-select-option value="生产副总">
              生产副总
            </a-select-option>
            <a-select-option value="销售副总">
              销售副总
            </a-select-option>
            <a-select-option value="采购副总">
              采购副总
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item  label="联系人类型" ref="contactType" prop="contactType">
          <a-input style="font-weight: 500;"
            v-model="form.contactType"
            placeholder="联系人类型"
          />
        </a-form-model-item>
        <a-form-model-item  label="电话" ref="phone" prop="phone">
          <a-input style="font-weight: 500;"
            v-model="form.phone"
            placeholder="电话"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
  </div>
 
</template>

<script>
import { addLink,progressNum } from "@/services/supplier/index";
export default {
  props: {
        suppId: {
           type: String,
            default () {
                return ''
            }
        },
    compileApply:{
      type: String,
      default () {
        return ''
      }
    },
    message:{
      type: String,
      default () {
        return ''
      }
    }
  },
  data() {
    return {
      labelCol: { span:6},
      wrapperCol: { span: 16 },
      visible: false,
      confirmLoading: false,
      form: {},
      rules: {
        contact: [
          { required: true, message: "名称必须填写", trigger: "blur" },
        ],
      },
      fileList: [],
      uploading: false,
      model: ''
    };
  },
  methods: {
    openModal(model) {
      // if(this.compileApply=='1'){
        this.visible = true;
        this.model = model
        this.form = {
          id:model.id,
          contact: model.contact,
          post: model.post,
          contactType: model.contactType,
          phone: model.phone
        };
      // }else {
      //   this.$message.info(this.message)
      // }

    },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
    },
    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate((valid) => {
        if (valid) {
          let params = {
              id: this.model.id,
              pGuid: this.suppId,
              ...this.form
          }
          addLink(params)
            .then((res) => {
                this.visible = false;
                form.resetFields();
                this.$message.info("操作成功");
                this.$emit("ok");
              progressNum(this.suppId).then(res=>{
                if(res.code!==1){
                  this.$message.info(res.message)
                }
              })
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        } else {
          this.confirmLoading = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-select-dropdown-menu-item{
 font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-form-item{
        margin-bottom: 5px;
    }
</style>
