<template>
  <a-form :form="registerForm" @submit="handleSubmit">
    <a-form-item v-bind="formItemLayout" label="手机号">
      <a-input
          v-decorator="[
          'phone',
          {
            rules: [{ required: true, message: '请输入您的手机号！' }],
          },
        ]"
          style="width: 100%"
      >
        <a-select
            slot="addonBefore"
            v-decorator="['prefix', { initialValue: '86' }]"
            style="width: 70px"
        >
          <a-select-option value="86">
            +86
          </a-select-option>
          <a-select-option value="87">
            +87
          </a-select-option>
        </a-select>
      </a-input>
    </a-form-item>
    <a-form-item
        v-bind="formItemLayout"
        label="验证码"
    >
      <a-row :gutter="8">
        <a-col :span="12">
          <a-input
              v-decorator="[
              'captcha',
              { rules: [{ required: true, message: '请输入验证码!' }] },
            ]"
          />
        </a-col>
        <a-col :span="12" style="text-align: right;">
          <a-button type="primary" @click="countDown" :disabled="disabledBtn">{{content}}</a-button>
        </a-col>
      </a-row>
    </a-form-item>
    <a-form-item v-bind="formItemLayout">
      <span slot="label">
        用户名&nbsp;
        <a-tooltip title="您可以使用此用户名登录">
          <a-icon type="question-circle-o" />
        </a-tooltip>
      </span>
      <a-input
          v-decorator="[
          'name',
          {
            rules: [{ required: true, message: '请输入您的用户名!', whitespace: true }],
          },
        ]"
      />
    </a-form-item>
    <a-form-item v-bind="formItemLayout" label="密码" has-feedback>
      <a-input
          v-decorator="[
          'password',
          {
            rules: [
              {
                required: true,
                message: '请设置你的密码!',
              },
              {
                validator: validateToNextPassword,
              },
            ],
          },
        ]"
          type="password"
      />
    </a-form-item>
    <a-form-item v-bind="formItemLayout" label="确认密码" has-feedback>
      <a-input
          v-decorator="[
          'confirm',
          {
            rules: [
              {
                required: true,
                message: '请确认您的密码!',
              },
              {
                validator: compareToFirstPassword,
              },
            ],
          },
        ]"
          type="password"
          @blur="handleConfirmBlur"
      />
    </a-form-item>
    <a-form-item v-bind="formItemLayout" label="邮箱">
      <a-input
          v-decorator="[
          'email',
          {
            rules: [
              {
                type: 'email',
                message: '请输入正确的电子邮箱!',
              },
              {
                required: true,
                message: '请输入您的邮箱!',
              },
            ],
          },
        ]"
      />
    </a-form-item>
    <a-form-item v-bind="tailFormItemLayout">
      <a-button type="primary" html-type="submit">
        注册
      </a-button>
    </a-form-item>
  </a-form>
</template>
<script>
import {getRegisterCode, getVerification, userRegister} from "@/services/user";
export default {
  data(){
    return {
      confirmDirty: false,
      autoCompleteResult: [],
      formItemLayout: {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
      },
      tailFormItemLayout: {
        wrapperCol: {
          xs: {
            span: 24,
            offset: 0,
          },
          sm: {
            span: 16,
            offset: 8,
          },
        },
      },
      disabledBtn:false,
      content: '发送验证码',  // 按钮里显示的内容
      totalTime: 60,      //记录具体倒计时时间
    };
  },
  watch:{
    totalTime(val){
      if(val==0){
        this.disabledBtn=false
        this.content='发送验证码'
      }
    }
  },
  beforeCreate() {
    this.registerForm = this.$form.createForm(this, { name: 'register' });
  },
  methods: {
    handleConfirmBlur(e) {
      const value = e.target.value;
      this.confirmDirty = this.confirmDirty || !!value;
    },
    compareToFirstPassword(rule, value, callback) {
      const registerForm = this.registerForm;
      if (value && value !== registerForm.getFieldValue('password')) {
        callback('两次输入的密码不一致');
      } else {
        callback();
      }
    },
    validateToNextPassword(rule, value, callback) {
      const registerForm = this.registerForm;
      if (value && this.confirmDirty) {
        registerForm.validateFields(['confirm'], { force: true });
      }
      callback();
    },
    handleWebsiteChange(value) {
      let autoCompleteResult;
      if (!value) {
        autoCompleteResult = [];
      } else {
        autoCompleteResult = ['.com', '.org', '.net'].map(domain => `${value}${domain}`);
      }
      this.autoCompleteResult = autoCompleteResult;
    },
    handleSubmit(e) {
      e.preventDefault();
      this.registerForm.validateFieldsAndScroll((err, values) => {
        console.log(values)
        if (!err) {
          let params = {}
          params.password = values.password;
          params.userName = values.name;
          params.phoneNumber = values.phone;
          params.code = values.captcha;
          params.email = values.email;
          userRegister(params).then(res=>{
            if (res) {
              this.$emit('register')
            }
          })
        }
      });
    },
    countDown() {
      console.log()
      if (this.registerForm.getFieldValue('phone')){
        this.disabledBtn=true
        let clock = window.setInterval(() => {
          this.totalTime--
          this.content = this.totalTime + 's后重新发送'
          if(this.totalTime==0){
            this.disabledBtn=false
            this.content ='发送验证码'
            this.totalTime=60
            clearInterval(clock)
          }
        },1000)
        let parmas={}
        parmas.phone=this.registerForm.getFieldValue('phone')
        parmas.smsFrom='MES后台'
        parmas.sendType='注册验证'
        getRegisterCode(parmas).then(res=>{
          if (res.code!=1) {
            this.$message.error(res.message)
          }
        })
      } else {
        this.$message.error('手机号不能为空')
      }
    },

  },
};
</script>
