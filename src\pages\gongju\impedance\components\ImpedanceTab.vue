<!-- 工具管理- 叠层阻抗-阻抗信息 -->
<template>
  <div style="width: 100%; height: 100%; user-select: none" @contextmenu.prevent="rightClick($event)" ref="SelectBox">
    <a-table
      :columns="columns"
      ref="impedanceTab"
      :data-source="impedanceTabData"
      bordered
      :scroll="{ y: heighty }"
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      class="impedance"
      :pagination="false"
      :customRow="customRow"
      :rowClassName="isRedRow"
      @keydown.native="handleTableKeyDown"
    >
      <span slot="index" slot-scope="record, text, index">
        <span>{{ index + 1 }}</span>
      </span>
      <!--阻抗类型-->
      <span slot="imp_Type_" slot-scope="record, text, index">
        <a-select
          v-model="record.imp_Type_"
          dropdownClassName="publicSelect"
          @change="typeChange(index)"
          :showArrow="false"
          :getPopupContainer="() => $refs.SelectBox"
        >
          <a-select-option v-for="item in seleDataZk" :title="item.text" :value="item.valueMember" :key="item.valueMember">
            {{ item.text }}
          </a-select-option>
        </a-select>
      </span>
      <!--控制层-->
      <span slot="imp_ControlLay_" slot-scope="record, text, index">
        <a-select
          v-model="record.imp_ControlLay_"
          v-if="record.imp_Type_ != ''"
          @change="controlChange(record, index)"
          :showArrow="false"
          :getPopupContainer="() => $refs.SelectBox"
        >
          <a-select-option v-for="item in controlLayers(record)" :value="item.value" :key="item.value">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </span>
      <!--上参-->
      <span slot="imp_UpLay_" slot-scope="record, text, index">
        <a-select
          v-model="record.imp_UpLay_"
          v-if="record.imp_ControlLay_ && record.imp_ControlLay_ != 'L1'"
          @change="upLayChange(record, index)"
          :showArrow="false"
          :getPopupContainer="() => $refs.SelectBox"
        >
          <a-select-option v-for="item in upLayFilter(record)" :value="item.value" :key="item.value">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </span>
      <!--下参-->
      <span slot="imp_DownLay_" slot-scope="record, text, index">
        <a-select
          v-model="record.imp_DownLay_"
          v-if="record.imp_ControlLay_ && record.imp_ControlLay_ != 'L' + form.layers"
          @change="downChange(record, index)"
          :showArrow="false"
          :getPopupContainer="() => $refs.SelectBox"
        >
          <a-select-option v-for="item in downLayFilter(record)" :value="item.value" :key="item.value">
            {{ item.name }}
          </a-select-option>
        </a-select>
      </span>
      <!--线宽-->
      <span slot="imp_LineWidth_" slot-scope="record, text, index">
        <a-input v-model="record.imp_LineWidth_" @click="selectall($event.target)" @change="lineWidthChage(text, index)" />
      </span>
      <!--线隙-->
      <span slot="imp_LineSpace_" slot-scope="record, text, index">
        <a-input
          v-model="record.imp_LineSpace_"
          @click="selectall($event.target)"
          v-if="typeFilter(record) == '3' || typeFilter(record) == '1'"
          @change="lineGapChange(text, index)"
        />
      </span>
      <!--线铜-->
      <span slot="imp_LineCuSpace_" slot-scope="record, text, index">
        <a-input
          v-model="record.imp_LineCuSpace_"
          @click="selectall($event.target)"
          v-if="typeFilter(record) == '1' || typeFilter(record) == '2'"
          @change="lineCopperChange(text, index)"
        />
      </span>
      <!--要求-->
      <span slot="imp_Value_Req_" slot-scope="record, text, index">
        <a-input v-model="record.imp_Value_Req_" @click="selectall($event.target)" @change="reqChange(text, index)" />
      </span>
      <!--公差-->
      <span slot="imp_Value_Tol_" slot-scope="record">
        <a-input v-model="record.imp_Value_Tol_" @click="selectall($event.target)" />
      </span>
      <!--L/W-->
      <span slot="imp_OKLineWidth_" slot-scope="record, text, index">
        <a-input
          @click="selectall($event.target)"
          v-model="record.imp_OKLineWidth_"
          @change="lwChange(text, index)"
          :style="record.imp_OKLineWidth_ != record.imp_LineWidth_ ? { color: 'red' } : ''"
          class="backgcolor"
        />
      </span>
      <!--L/S-->
      <span slot="imp_OKLineSpace_" slot-scope="record">
        <a-input
          @click="selectall($event.target)"
          v-model="record.imp_OKLineSpace_"
          @change="lsChange(text, index)"
          v-if="typeFilter(record) == '3' || typeFilter(record) == '1'"
          :style="record.imp_OKLineSpace_ != record.imp_LineSpace_ ? { color: 'red' } : ''"
          :class="typeFilter(record) == '3' || typeFilter(record) == '1' ? 'backgcolor' : ''"
        />
      </span>
      <!-- Imp-->
      <!--     :style="[{background:(record.imp_TrueValue_ == '' ? 'red':'green')}]"-->
      <span slot="imp_TrueValue_" slot-scope="record" style="display: block; width: 100%; height: 100%" @click="test(record)">
        <div style="width: 100%; height: 100%" :style="record | styleFilter">
          {{ record.imp_TrueValue_ }}
        </div>
      </span>
      <!--l/CU-->
      <span slot="imp_OKLineCuSpace_" slot-scope="record, text, index">
        <a-input
          @change="lcuChange(text, index)"
          v-model="record.imp_OKLineCuSpace_"
          v-if="typeFilter(record) == '2' || typeFilter(record) == '1'"
          :class="typeFilter(record) == '2' || typeFilter(record) == '1' ? 'backgcolor' : ''"
          :style="record.imp_OKLineCuSpace_ != record.imp_LineCuSpace_ ? { color: 'red' } : ''"
        />
        <span v-else :style="record.imp_OKLineCuSpace_ != record.imp_LineCuSpace_ ? { color: 'red' } : ''">{{ record.imp_OKLineCuSpace_ }}</span>
      </span>
      <span slot="imp_BC_" slot-scope="record, text, index">
        <a-input @click="selectall($event.target)" v-model="record.imp_BC_" @change="impBCChage(text, index)" />
      </span>
      <span slot="action" slot-scope="record">
        <a-tooltip title="删除">
          <a-icon style="margin-right: 5%; color: #0e2d5f" type="close-circle" @click.stop="removeImpedance($event, record)" />
        </a-tooltip>
        <!-- <a-tooltip  title="复制" >
        <a-icon style="margin-right: 5%;color: #0e2d5f;" type="copy" @click.stop="copyImpedance(record)" />
      </a-tooltip>
      <a-tooltip  title="对称" >
        <a-icon style="color: #0e2d5f;" type="column-height" @click.stop="symmetricImpedance(record)" />
      </a-tooltip> -->
        <!-- <a-tooltip  title="线宽还原" >
        <a-icon style="color: #0e2d5f;" type="undo" @click.stop="lineWidthChage(text,index)" />
      </a-tooltip> -->
        <!-- <a-tooltip  title="反算" >
        <a-icon style="color: #0e2d5f;" type="calculator" @click.stop="inverseCount(text,index)" />
      </a-tooltip> -->
      </span>
    </a-table>
    <a-menu :style="menuStyle1" v-if="menuVisible1" class="tabRightClikBox">
      <!-- <a-menu-item @click="addImpedance">添加</a-menu-item> -->
      <a-menu-item @click="copy1()">复制选中</a-menu-item>
      <a-menu-item @click="symmetric1()">对称选中</a-menu-item>
      <a-menu-item @click="lineWidthChage1()">线宽还原</a-menu-item>
      <a-menu-item @click="inverseCoun1t()">反算选中</a-menu-item>
      <!-- <a-menu-item @click="removeImpedance">删除</a-menu-item>
      <a-menu-item @click="resizeImpedance">刷新</a-menu-item> -->
    </a-menu>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from "vuex";
const columns = [
  {
    title: "",
    dataIndex: "index",
    width: 20,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "index" },
  },
  {
    title: "阻抗类型",
    key: "imp_Type_",
    scopedSlots: { customRender: "imp_Type_" },
    width: 120,
    ellipsis: true,
    align: "center",
  },
  {
    title: "控制层",
    key: "imp_ControlLay_",
    width: 50,
    ellipsis: true,
    scopedSlots: { customRender: "imp_ControlLay_" },
    align: "center",
  },
  {
    title: "上参",
    // dataIndex: "imp_UpLay_",
    width: 50,
    align: "center",
    ellipsis: true,
    key: "imp_UpLay_",
    scopedSlots: { customRender: "imp_UpLay_" },
  },
  {
    title: "下参",
    // dataIndex: "imp_DownLay_",
    width: 50,
    key: "imp_DownLay_",
    ellipsis: true,
    scopedSlots: { customRender: "imp_DownLay_" },
    align: "center",
  },

  {
    title: "线宽",
    key: "imp_LineWidth_",
    scopedSlots: { customRender: "imp_LineWidth_" },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "线隙",
    key: "imp_LineSpace_",
    scopedSlots: { customRender: "imp_LineSpace_" },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "线铜",
    key: "imp_LineCuSpace_",
    scopedSlots: { customRender: "imp_LineCuSpace_" },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "要求",
    key: "imp_Value_Req_",
    scopedSlots: { customRender: "imp_Value_Req_" },
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "公差",
    // dataIndex: "imp_Value_Tol_",
    width: 35,
    key: "imp_Value_Tol_",
    scopedSlots: { customRender: "imp_Value_Tol_" },
    align: "center",
    ellipsis: true,
  },
  {
    title: "L/W",
    // dataIndex: "imp_OKLineWidth_",
    key: "imp_OKLineWidth_",
    scopedSlots: { customRender: "imp_OKLineWidth_" },
    align: "center",
    ellipsis: true,
    width: 40,
  },
  {
    title: "L/S",
    //dataIndex: "imp_OKLineSpace_",
    // key: 'imp_OKLineSpace_',
    scopedSlots: { customRender: "imp_OKLineSpace_" },
    align: "center",
    ellipsis: true,
    width: 40,
  },
  {
    title: "L/Cu",
    // dataIndex: "imp_OKLineCuSpace_",
    width: 40,
    key: "imp_OKLineCuSpace_",
    align: "center",
    ellipsis: true,
    scopedSlots: { customRender: "imp_OKLineCuSpace_" },
  },
  {
    title: "Imp(Eth)",
    dataIndex: "imp_TrueValueWithOutSM_",
    align: "center",
    ellipsis: true,
    width: 60,
    class: "selectSTY",
  },
  {
    title: "Imp",
    key: "imp_TrueValue_",
    scopedSlots: { customRender: "imp_TrueValue_" },
    align: "center",
    ellipsis: true,
    width: 40,
  },
  {
    title: "H1",
    dataIndex: "imp_H1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "Er1",
    dataIndex: "imp_Er1_",
    align: "center",
    ellipsis: true,
    width: 40,
  },
  {
    title: "H2",
    dataIndex: "imp_H2_",
    align: "center",
    ellipsis: true,
    width: 35,
  },

  {
    title: "Er2",
    dataIndex: "imp_Er2_",
    align: "center",
    ellipsis: true,
    width: 40,
  },
  {
    title: "W1",
    dataIndex: "imp_W1_",
    align: "center",
    ellipsis: true,
    width: 40,
  },
  {
    title: "W2",
    dataIndex: "imp_W2_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "S1",
    dataIndex: "imp_S1_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "D1",
    dataIndex: "imp_D1_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "T1",
    dataIndex: "imp_T1_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "C1",
    dataIndex: "imp_C1_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "C2",
    dataIndex: "imp_C2_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "C3",
    dataIndex: "imp_C3_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "CEr_",
    dataIndex: "imp_CEr_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  // {
  //   title: "Imp-H1",
  //   dataIndex: "imp_H1IncludeLyrNoList_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 50,
  // },
  // {
  //   title: "Imp-H2",
  //   dataIndex: "imp_H2IncludeLyrNoList_",
  //   align:'center',
  //   ellipsis: true,
  //   width: 50,
  // },
  // {
  //   title: "补偿",
  //   key: "imp_BC_",
  //   scopedSlots: { customRender: "imp_BC_" },
  //   align: "center",
  //   ellipsis: true,
  //   width: 50,
  // },
  {
    title: "操作",
    align: "center",
    scopedSlots: { customRender: "action" },
    width: 40,
  },
];
export default {
  name: "impedanceTab",
  props: {
    laminsert: {
      type: Boolean,
      required: true,
    },
    seleDataZk: {
      type: Array,
      required: true,
    },
    someList: {
      type: Array,
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    bcdata: {
      type: Array,
    },
  },
  data() {
    return {
      currentCell: null,
      dianji: "",
      columns,
      activeIndex: "",
      ins: false,
      del: false,
      isCtrlPressed: false,
      rowIndex: "",
      menuVisible1: false,
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      impedanceTabData: [],
      menuVisible: false,
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      menuData: "",
      datasource: {},
      heighty: 120,
      customClick: record => ({
        on: {
          click: event => {},
          contextmenu: e => {
            e.preventDefault();
            this.rightClick1(event, record);
          },
        },
      }),
      selectedRowKeysArray: [],
      selectedRowsData: [],
      delKey: false,
    };
  },
  computed: {
    ...mapGetters({ form: "categoryForm" }),
    NewDataList() {
      // 新的赋值
      return JSON.parse(JSON.stringify(this.data));
    },
    newimpedance() {
      // 新的赋值
      return JSON.parse(JSON.stringify(this.impedanceTabData));
    },
  },
  watch: {
    NewDataList: {
      handler: function (newV, oldV) {
        newV.forEach((newItem, index) => {
          const oldItem = oldV[index];
          for (let key in newItem) {
            if (
              oldItem &&
              oldItem[key] &&
              newItem[key] &&
              newItem[key] !== oldItem[key] &&
              key != "tag" &&
              key != "stackUpCuType_" &&
              key != "stackUpThichnessORG_" &&
              key != "stackUpThichnessMIL_" &&
              key != "stackUpThichnessMM_"
            ) {
              this.setedit(true);
              this.impedanceTabData.forEach(item => {
                item.imp_TrueValueWithOutSM_ = "";
                item.imp_TrueValue_ = "";
                item.imp_W1_ = "";
                item.imp_W2_ = "";
              });
            }
          }
        });
      },
      deep: true,
    },
    newimpedance: {
      handler: function (newV, oldV) {
        newV.forEach((newItem, index) => {
          const oldItem = oldV[index];
          for (let key in newItem) {
            if (oldItem && newItem && newItem[key] != oldItem[key]) {
              this.setedit(true);
            }
          }
        });
      },
      deep: true,
    },
  },
  filters: {
    styleFilter(val) {
      if (val.imp_ValueColor_ == "2") {
        return {
          background: "red",
        };
      }
      if (val.imp_ValueColor_ == "1") {
        return {
          background: "green",
        };
      }
    },
  },
  methods: {
    ...mapMutations("setting", ["setedit"]),
    keyclick() {
      this.dianji = true;
    },
    selectall(target) {
      target.select();
    },
    getCurrentCellIndex() {
      const table = this.$refs.impedanceTab.$el;
      const cells = table.querySelectorAll("td");
      if (!this.currentCell || this.dianji) {
        this.currentCell = this.$refs.impedanceTab.$el.querySelector(":focus").closest("td");
      }
      return this.currentCell ? Array.from(cells).indexOf(this.currentCell) : 0;
    },
    handleTableKeyDown(event) {
      if (event.key == "ArrowDown" || event.key == "ArrowUp" || event.key == "ArrowRight" || event.key == "ArrowLeft") {
        const table = this.$refs.impedanceTab.$el;
        const cells = table.querySelectorAll("td"); //当前表格所有单元格
        setTimeout(() => {
          this.dianji = false;
        }, 500);
        const key = event.key;
        const cellIndex = this.getCurrentCellIndex(); //当前行坐标位置
        const numCols = this.columns.length; //列数
        const numRows = Math.ceil(cells.length / numCols); // 计算总行数
        if (key === "ArrowDown" && cellIndex < cells.length - numCols) {
          event.preventDefault();
          let nextRowIndex = Math.floor((cellIndex + numCols) / numCols);
          while (nextRowIndex < numRows) {
            const nextIndex = nextRowIndex * numCols + (cellIndex % numCols); //下一行×列数为起始位置index加偏移位置index获取下一行具体位置index
            const nextCell = cells[nextIndex]; //获取下一行具体位置
            if (nextCell.querySelector("input") || nextCell.querySelector("div")) {
              this.currentCell = nextCell;
              break;
            } else {
              nextRowIndex++;
            }
          }
        } else if (key === "ArrowUp" && cellIndex >= numCols) {
          event.preventDefault();
          let prevRowIndex = Math.floor((cellIndex - numCols) / numCols);
          while (prevRowIndex >= 0) {
            const prevIndex = prevRowIndex * numCols + (cellIndex % numCols);
            const prevCell = cells[prevIndex];

            if (prevCell.querySelector("input") || prevCell.querySelector("div")) {
              this.currentCell = prevCell;
              break;
            } else {
              prevRowIndex--;
            }
          }
        } else if (key === "ArrowRight") {
          event.preventDefault();
          if (this.currentCell) {
            let nextIndex = cellIndex + 1;
            while (nextIndex <= cells.length) {
              if (nextIndex % numCols === 0) {
                nextIndex = cellIndex - (cellIndex % numCols) + 1;
              }
              this.currentCell = cells[nextIndex];
              if (this.currentCell.querySelector("input") || this.currentCell.querySelector("div")) {
                // 找到具有 input 或 div 的单元格，跳出循环
                break;
              } else {
                nextIndex++;
              }
            }
          }
        } else if (key === "ArrowLeft") {
          event.preventDefault();
          if (this.currentCell) {
            let prevIndex = cellIndex - 1;
            while (prevIndex < cells.length) {
              if (prevIndex % numCols === numCols - 1) {
                prevIndex = cellIndex - (cellIndex % numCols) + 30;
              }
              this.currentCell = cells[prevIndex];
              if (this.currentCell.querySelector("input") || this.currentCell.querySelector("div")) {
                break;
              } else {
                prevIndex--;
              }
            }
          }
        }
        if (this.currentCell) {
          const input = this.currentCell.querySelector("input");
          const div = this.currentCell.querySelector("div");
          if (input) {
            input.focus();
            input.select();
          }
          if (div) {
            div.focus();
          }
        }
      }
    },
    rightClick1(e, record, data) {
      this.datasource = data;
      let event = e.target;
      this.rowIndex = event.parentNode.rowIndex;
      const tableWrapper = this.$refs.SelectBox;
      const cellRect = event.getBoundingClientRect();
      const wrapperRect = tableWrapper.getBoundingClientRect();
      if (event.className.indexOf("ant-input") != -1 || event.className.indexOf("ant-select-selection-selected-value") != -1) {
        this.menuVisible1 = false;
      } else {
        this.menuVisible1 = true;
      }
      this.menuStyle1.top = cellRect.top - wrapperRect.top - 60 + "px";
      this.menuStyle1.left = cellRect.left - wrapperRect.left + event.offsetWidth - 10 + "px";
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    controlChange(value, index, type) {
      let control = value?.imp_ControlLay_.split("L")[1];
      let medium = this.data.find(item => {
        return item.stackUpLayerNo_ == control;
      })?.stackUpMTRFoil_;
      if (!type) {
        this.impedanceTabData[index].imp_UpLay_ = "";
        this.impedanceTabData[index].imp_DownLay_ = "";
        Object.keys(this.impedanceTabData[index]).forEach(key => {
          if (key != "imp_ControlLay_" && key != "imp_Type_") {
            this.impedanceTabData[index][key] = "";
          }
        });
      }
      if (
        this.data.find(item => {
          return item.stackUpLayerNo_ == control;
        })
      ) {
        this.impedanceTabData[index].imp_CtlTB_ = this.data.find(item => {
          return item.stackUpLayerNo_ == control;
        }).stackUpCUTB_;
      }
      if (this.form.layers != "") {
        if (this.bcdata.length != 0) {
          this.bcdata.forEach(res => {
            if (res.lineID_ == control) {
              this.impedanceTabData[index].imp_BC_ = res.bC_ ? res.bC_ + "" : "0";
            }
          });
        } else {
          this.someList.forEach(res => {
            if (control == 1 || control == this.form.layers) {
              if (res.iO_ == "910" && res.cu_ == medium && res.holeCu_ == "20") {
                this.impedanceTabData[index].imp_BC_ = res.bc ? res.bc + "" : "0";
              }
            } else {
              if (res.iO_ == "911" && res.cu_ == medium && res.holeCu_ == "20") {
                this.impedanceTabData[index].imp_BC_ = res.bc ? res.bc + "" : "0";
              }
            }
          });
        }
        this.someList.forEach(res => {
          if (control == 1 || control == this.form.layers) {
            // O
            if (res.iO_ == "910" && res.cu_ == medium && res.holeCu_ == "20") {
              this.impedanceTabData[index].imp_C1_ = res.c1_ + "";
              this.impedanceTabData[index].imp_C2_ = res.c2_ + "";
              this.impedanceTabData[index].imp_C3_ = res.c3_ + "";
              this.impedanceTabData[index].imp_T1_ = res.t1_ + "";
              //this.impedanceTabData[index].imp_BC_= res.bc + ''
              this.impedanceTabData[index].imp_CEr_ = res.cEr_ + "";
            }
          } else {
            if (res.iO_ == "911" && res.cu_ == medium && res.holeCu_ == "20") {
              this.impedanceTabData[index].imp_C1_ = res.c1_ + "";
              this.impedanceTabData[index].imp_C2_ = res.c2_ + "";
              this.impedanceTabData[index].imp_C3_ = res.c3_ + " ";
              this.impedanceTabData[index].imp_T1_ = res.t1_ + " ";
              // this.impedanceTabData[index].imp_BC_= res.bc + ''
              this.impedanceTabData[index].imp_CEr_ = res.cEr_ + "";
            }
          }
        });
        this.$forceUpdate();
      } else {
        this.$message.info("请填写层数");
      }
    },
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    typeChange(index) {
      for (var i in this.impedanceTabData[index]) {
        let _obj = this.impedanceTabData[index];
        if (i != "imp_Type_") {
          _obj[i] = "";
          this.impedanceTabData[index] = _obj;
        }
      }

      this.$forceUpdate();
    },
    controlLayers(record) {
      let count = this.form.layers;
      let arr = [];
      if (record.imp_Type_.split("_")[0] == "I") {
        for (var i = 2; i < count; i++) {
          arr.push({
            name: "L" + i,
            value: "L" + i,
          });
        }
        return arr;
      } else if (record.imp_Type_.split("_")[0] == "O") {
        return [
          { name: "L1", value: "L1" },
          { name: "L" + count, value: "L" + count },
        ];
      }
    },
    upLayFilter(record) {
      const count = record.imp_ControlLay_.match(/\d+$/gi)[0],
        arr = [];
      for (var i = 1; i < count; i++) {
        arr.push({
          name: "L" + i,
          value: "L" + i,
        });
      }
      return arr;
    },
    downLayFilter(record) {
      const count = record.imp_ControlLay_.match(/\d+$/gi)[0],
        arr = [];
      let edg = Number(this.form.layers) + 1;
      for (var i = Number(count) + 1; i < edg; i++) {
        arr.push({
          name: "L" + i,
          value: "L" + i,
        });
      }
      return arr;
    },
    typeFilter(record) {
      // _C 共面地  _D差分
      if (record.imp_Type_.indexOf("_D") > 0 && record.imp_Type_.indexOf("_C") > 0) {
        return "1";
      } else if (record.imp_Type_.indexOf("_C") > 0 && record.imp_Type_.indexOf("_D") < 0) {
        //（包含_C不包含_D）
        return "2";
      } else if (record.imp_Type_.indexOf("_C") < 0 && record.imp_Type_.indexOf("_D") > 0) {
        // （包含_D不包含_C）
        return "3";
      } else {
        // 啥都不包含
        return "4";
      }
    },
    reqChange(text, index) {
      if (Number(text.imp_Value_Req_) < 50) {
        this.impedanceTabData[index].imp_Value_Tol_ = 5 + "";
      } else {
        this.impedanceTabData[index].imp_Value_Tol_ = text.imp_Value_Req_ * 0.1 + "";
      }
      this.$forceUpdate();
    },
    lineWidthChage1() {
      for (var i = 0; i < this.selectedRowKeysArray.length; i++) {
        var selectedIndex = this.selectedRowKeysArray[i];
        this.lineWidthChage(this.impedanceTabData[selectedIndex], selectedIndex);
        this.lwChange(this.impedanceTabData[selectedIndex], selectedIndex);
      }
    },
    lineWidthChage(text, index) {
      this.impedanceTabData[index].imp_W1_ = text.imp_LineWidth_;
      this.impedanceTabData[index].imp_OKLineWidth_ = text.imp_LineWidth_;
      this.impedanceTabData[index].imp_TrueValue_ = "";
      this.impedanceTabData[index].imp_TrueValueWithOutSM_ = "";
      this.impedanceTabData[index].Imp_ValueColor_ = "2";
      let controlLayer = text.imp_ControlLay_.split("L")[1];
      let count_;
      let controItem_ = this.data.find(item => {
        return item.stackUpLayerNo_ == controlLayer;
      });
      if (controlLayer == 1 || controlLayer == this.form.layers) {
        let outLayer_ = this.someList.filter(res => {
          return res.iO_ == "910";
        });
        count_ = outLayer_.filter(ite => {
          return ite.cu_ == controItem_?.stackUpMTRFoil_;
        });
      } else {
        count_ = this.someList
          .filter(res => {
            return res.iO_ == "911";
          })
          .filter(ite => {
            return ite.cu_ == controItem_?.stackUpMTRFoil_;
          });
      }
      if (count_.length > 0) {
        this.impedanceTabData[index].imp_W2_ = this.getFloat(Number(text.imp_LineWidth_) - count_[0].lineWidthDiff_, 2) + "";
      } else {
        this.impedanceTabData[index].imp_W2_ = text.imp_LineWidth_ + "";
      }
      this.$forceUpdate();
    },
    lineGapChange(text, index) {
      this.impedanceTabData[index].imp_OKLineSpace_ = text.imp_LineSpace_;
      this.impedanceTabData[index].imp_S1_ = text.imp_LineSpace_;
      this.$forceUpdate();
    },
    lineCopperChange(text, index) {
      this.impedanceTabData[index].imp_OKLineCuSpace_ = text.imp_LineCuSpace_;
      this.impedanceTabData[index].imp_D1_ = text.imp_LineCuSpace_;
      this.$forceUpdate();
    },
    controlLayChange(record, index) {
      // debugger 上参改变
      // 控制层：imp_ControlLay_
      // 上参：imp_UpLay_
      // 阻抗类型： imp_Type_
      // 开始层 startlayer_
      // 结束层 endLayer_
      var { imp_ControlLay_, imp_UpLay_, imp_Type_ } = record;
      imp_ControlLay_ = imp_ControlLay_ ? imp_ControlLay_.split("L")[1] : "";
      imp_UpLay_ = imp_UpLay_ ? imp_UpLay_.split("L")[1] : "";
      var imp_CtlThicknessInH = this.seleDataZk.find(item => item.valueMember == record.imp_Type_).imp_CtlThicknessInH;
      var _obj = this.changeComputed({ imp_ControlLay_: imp_ControlLay_, imp_UpLay_: imp_UpLay_ });
      var _doubleObj = this.changeComputed({ imp_ControlLay_: imp_ControlLay_, imp_UpLay_: this.form.layers });
      if (imp_ControlLay_ == 1 || imp_ControlLay_ == this.form.layers) {
        // 外层
        if (imp_CtlThicknessInH == 1) {
          this.impedanceTabData[index].imp_H1_ = this.getFloat(_obj.stackUpThichnessMIL + _obj.controlStackUpThichnessMIL, 3) + "";
          this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo + _obj.controlStackNo;
        } else {
          this.impedanceTabData[index].imp_H1_ = _obj.stackUpThichnessMIL + "";
          this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo;
        }
        this.impedanceTabData[index].imp_Er1_ = _obj.stackUpDK + "";
      } else {
        // 内层
        if (_obj.stackUpCUTB) {
          if (imp_CtlThicknessInH == 2) {
            this.impedanceTabData[index].imp_H2_ = this.getFloat(_obj.stackUpThichnessMIL + _obj.controlStackUpThichnessMIL, 3) + "";
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = _obj.stackNo + _obj.controlStackNo;
          } else {
            this.impedanceTabData[index].imp_H2_ = _obj.stackUpThichnessMIL + "";
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = _obj.stackNo;
          }
          this.impedanceTabData[index].imp_Er2_ = _obj.stackUpDK + "";
          if (imp_Type_.indexOf("_1") != -1) {
            if (imp_CtlThicknessInH == 2) {
              this.impedanceTabData[index].imp_H1_ = this.getFloat(_doubleObj.stackUpThichnessMIL + _doubleObj.controlStackUpThichnessMIL, 3) + "";
              this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _doubleObj.stackNo + _doubleObj.controlStackNo;
            } else {
              this.impedanceTabData[index].imp_H1_ = _doubleObj.stackUpThichnessMIL + "";
              this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _doubleObj.stackNo;
            }
            this.impedanceTabData[index].imp_Er1_ = _doubleObj.stackUpDK + "";
            this.impedanceTabData[index].imp_DownLay_ = undefined;
          }
        } else {
          // 不含T的时候
          // 不包含_1
          if (imp_CtlThicknessInH == 1) {
            this.impedanceTabData[index].imp_H1_ = this.getFloat(_obj.stackUpThichnessMIL + _obj.controlStackUpThichnessMIL, 3) + "";
            this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo + _obj.controlStackNo;
          } else {
            this.impedanceTabData[index].imp_H1_ = _obj.stackUpThichnessMIL + "";
            this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo;
          }
          this.impedanceTabData[index].imp_Er1_ = _obj.stackUpDK + "";
          if (imp_Type_.indexOf("_1") != -1) {
            this.impedanceTabData[index].imp_H2_ = this.getFloat(_doubleObj.stackUpThichnessMIL + _doubleObj.controlStackUpThichnessMIL, 3) + "";
            this.impedanceTabData[index].imp_Er2_ = _doubleObj.stackUpDK + "";
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = _doubleObj.stackNo + _doubleObj.controlStackNo;
            this.impedanceTabData[index].imp_DownLay_ = undefined;
          }
        }
      }
    },
    upLayChange(record, index) {
      var CTLLyr = record.imp_ControlLay_.split("L")[1]; //控制层；
      var LayNo = this.form.layers; //总层数；
      var OutLay = CTLLyr == 1 || CTLLyr == LayNo ? true : false; //控制层是否为外层
      var UpLyr = record.imp_UpLay_.split("L")[1]; //上参
      var Imp_CtlT1 = Number(record.imp_T1_);
      var Imp_CtlThickness = 0;
      var Imp_CtlTB;
      var Imp_HValue = 0;
      var Imp_ErValue = 0;
      var ImpType = record.imp_Type_; //阻抗类型
      var imp_CtlThicknessInH = this.seleDataZk.find(item => item.valueMember == record.imp_Type_).imp_CtlThicknessInH;
      var DicImpThickness = this.ImpThicknessCalc(CTLLyr, UpLyr, OutLay);
      if (JSON.stringify(DicImpThickness) == "{}") {
        return;
      }
      Imp_HValue = Number(DicImpThickness.HMIL);
      Imp_ErValue = DicImpThickness.Er;
      Imp_CtlThickness = Number(DicImpThickness.CtlMIL); //Number(Imp_CtlT1);
      Imp_CtlTB = DicImpThickness.CtlTB;
      if (OutLay) {
        this.impedanceTabData[index].imp_H1_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 1 ? Imp_CtlThickness : 0), 3).toString();
        this.impedanceTabData[index].imp_Er1_ = Imp_ErValue;
        this.impedanceTabData[index].imp_H1IncludeLyrNoList_ =
          DicImpThickness.stackNo + (imp_CtlThicknessInH == 1 ? DicImpThickness.controlStackNo : "");
      } else {
        if (Imp_CtlTB) {
          if (ImpType.indexOf("_1") != -1) {
            this.impedanceTabData[index].imp_H1_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 2 ? Imp_CtlThickness : 0), 3).toString();
            this.impedanceTabData[index].imp_H1IncludeLyrNoList_ =
              DicImpThickness.stackNo + (imp_CtlThicknessInH == 2 ? DicImpThickness.controlStackNo : "");
            this.impedanceTabData[index].imp_Er1_ = Imp_ErValue;
            let DicImpThicknessNoD = this.ImpThicknessCalc(CTLLyr, LayNo.toString(), false);
            this.impedanceTabData[index].imp_H2_ = DicImpThicknessNoD.HMIL.toString();
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ =
              DicImpThicknessNoD.stackNo + (imp_CtlThicknessInH == 2 ? DicImpThicknessNoD.controlStackNo : "");
            this.impedanceTabData[index].imp_Er2_ = DicImpThicknessNoD.Er;
            this.impedanceTabData[index].imp_DownLay_ = undefined;
          } else {
            this.impedanceTabData[index].imp_H2_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 2 ? Imp_CtlThickness : 0), 3).toString();
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ =
              DicImpThickness.stackNo + (imp_CtlThicknessInH == 2 ? DicImpThickness.controlStackNo : "");
            this.impedanceTabData[index].imp_Er2_ = Imp_ErValue;
          }
        } else {
          this.impedanceTabData[index].imp_H1_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 1 ? Imp_CtlThickness : 0), 3).toString();
          this.impedanceTabData[index].imp_H1IncludeLyrNoList_ =
            imp_CtlThicknessInH == 1 ? DicImpThickness.stackNo + DicImpThickness.controlStackNo : DicImpThickness.stackNo;
          this.impedanceTabData[index].imp_Er1_ = Imp_ErValue;
          if (ImpType.indexOf("_1") != -1) {
            let DicImpThicknessNoD = this.ImpThicknessCalc(CTLLyr, LayNo.toString(), false);
            this.impedanceTabData[index].imp_H2_ = this.getFloat(Number(DicImpThicknessNoD.HMIL) + Number(Imp_CtlThickness), 3).toString();
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = DicImpThicknessNoD.stackNo + DicImpThicknessNoD.controlStackNo;
            this.impedanceTabData[index].imp_Er2_ = DicImpThicknessNoD.Er;
            this.impedanceTabData[index].imp_DownLay_ = undefined;
          }
        }
      }
    },
    downChange(record, index) {
      var CTLLyr = record.imp_ControlLay_.split("L")[1]; //控制层；
      var LayNo = this.form.layers; //总层数；
      var OutLay = CTLLyr == 1 || CTLLyr == LayNo ? true : false; //控制层是否为外层
      var DownLyr = record.imp_DownLay_.split("L")[1]; //下参;
      var ImpType = record.imp_Type_; //阻抗类型;
      var Imp_CtlThickness = 0;
      var Imp_CtlTB;
      var Imp_HValue = 0;
      var Imp_ErValue = 0;
      var imp_CtlThicknessInH = this.seleDataZk.find(item => item.valueMember == record.imp_Type_).imp_CtlThicknessInH;
      var DicImpThickness = this.ImpThicknessCalc(CTLLyr, DownLyr, OutLay);
      if (JSON.stringify(DicImpThickness) == "{}") {
        return;
      }
      Imp_HValue = Number(DicImpThickness.HMIL);
      Imp_ErValue = DicImpThickness.Er;
      Imp_CtlThickness = Number(DicImpThickness.CtlMIL);
      Imp_CtlTB = DicImpThickness.CtlTB;
      if (OutLay) {
        this.impedanceTabData[index].imp_H1_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 1 ? Imp_CtlThickness : 0), 3).toString();
        this.impedanceTabData[index].imp_H1IncludeLyrNoList_ =
          imp_CtlThicknessInH == 1 ? DicImpThickness.stackNo + DicImpThickness.controlStackNo : DicImpThickness.stackNo;
        this.impedanceTabData[index].imp_Er1_ = Imp_ErValue;
      } else {
        if (Imp_CtlTB) {
          this.impedanceTabData[index].imp_H1_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 1 ? Imp_CtlThickness : 0), 3).toString();
          this.impedanceTabData[index].imp_H1IncludeLyrNoList_ =
            DicImpThickness.stackNo + (imp_CtlThicknessInH == 1 ? DicImpThickness.controlStackNo : "");
          this.impedanceTabData[index].imp_Er1_ = Imp_ErValue;
          if (ImpType.indexOf("_1") != -1) {
            let DicImpThicknessNoU = this.ImpThicknessCalc(CTLLyr, 1, false);
            this.impedanceTabData[index].imp_H2_ = this.getFloat(Number(DicImpThicknessNoU.HMIL) + Number(Imp_CtlThickness), 3).toString();
            this.impedanceTabData[index].imp_Er2_ = DicImpThicknessNoU.Er;
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = DicImpThicknessNoU.stackNo + DicImpThicknessNoU.controlStackNo;
            this.impedanceTabData[index].imp_UpLay_ = undefined;
          }
        } else {
          if (ImpType.indexOf("_1") != -1) {
            this.impedanceTabData[index].imp_H1_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 2 ? Imp_CtlThickness : 0), 3).toString();
            this.impedanceTabData[index].imp_H1IncludeLyrNoList_ =
              DicImpThickness.stackNo + (imp_CtlThicknessInH == 2 ? DicImpThickness.controlStackNo : "");
            this.impedanceTabData[index].imp_Er1_ = Imp_ErValue;
            let DicImpThicknessNoU = this.ImpThicknessCalc(CTLLyr, 1, false);
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ =
              DicImpThicknessNoU.stackNo + (imp_CtlThicknessInH == 2 ? DicImpThicknessNoU.controlStackNo : "");
            this.impedanceTabData[index].imp_H2_ = DicImpThicknessNoU.HMIL.toString();
            this.impedanceTabData[index].imp_Er2_ = DicImpThicknessNoU.Er;
            this.impedanceTabData[index].imp_UpLay_ = undefined;
          } else {
            this.impedanceTabData[index].imp_H2_ = this.getFloat(Imp_HValue + (imp_CtlThicknessInH == 2 ? Imp_CtlThickness : 0), 3).toString();
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ =
              DicImpThickness.stackNo + (imp_CtlThicknessInH == 2 ? DicImpThickness.controlStackNo : "");
            this.impedanceTabData[index].imp_Er2_ = Imp_ErValue;
          }
        }
      }
    },
    ImpThicknessCalc(StartLyr, EndLyr, OutLay) {
      if (!StartLyr || !EndLyr) {
        return;
      }
      var DicImpInfo = {};
      var BeginLay; //开始层
      var EndLay; //结束层
      var startIndex_;
      var endIndex_;
      if (Number(StartLyr) > Number(EndLyr)) {
        BeginLay = EndLyr;
        EndLay = StartLyr;
      } else {
        BeginLay = StartLyr;
        EndLay = EndLyr;
      }
      startIndex_ = this.data
        .map(item => (item.stackUpLayerNo_ ? item.stackUpLayerNo_.toString() : item.stackUpLayerNo_))
        .indexOf(BeginLay.toString());
      endIndex_ = this.data.map(item => (item.stackUpLayerNo_ ? item.stackUpLayerNo_.toString() : item.stackUpLayerNo_)).indexOf(EndLay.toString());
      var controlStackNo;
      var stackNo = "";
      var CtlThicknessMIL = 0;
      var CTLTopSide = true;
      var H1ThicknessMM = 0;
      var H1ThicknessMIL = 0;
      var PPCoreCounter = 0;
      var Er1Thickness = 0;
      this.data.forEach((item, index) => {
        var LayerName = item.stackUpLayerNo_; //层号
        var LayerMTR = item.stackUpMTR_; //物料
        if (LayerName == StartLyr) {
          CtlThicknessMIL = item.stackUpThichnessMIL_; //压合厚度;
          CTLTopSide = item.stackUpCUTB_ == "t" ? true : false;
          controlStackNo = index + 1;
        }
        if (Number(startIndex_) < Number(index) && Number(endIndex_) > Number(index)) {
          H1ThicknessMIL += Number(item.stackUpThichnessMIL_);
          H1ThicknessMM += Number(item.stackUpThichnessMM_);
          if (LayerMTR == "Core" || LayerMTR == "PP") {
            PPCoreCounter = PPCoreCounter + 1;
            Er1Thickness += Number(item.stackUpDK_);
          }
          stackNo += Number(index + 1) + "|";
        }
      });
      DicImpInfo.HMM = this.getFloat(H1ThicknessMM, 3).toString();
      DicImpInfo.HMIL = this.getFloat(H1ThicknessMIL, 3).toString();
      DicImpInfo.Er = this.getFloat(Number(Er1Thickness / PPCoreCounter), 3).toString();
      DicImpInfo.CtlMIL = this.getFloat(CtlThicknessMIL, 3).toString();
      DicImpInfo.CtlTB = CTLTopSide;
      DicImpInfo.controlStackNo = controlStackNo;
      DicImpInfo.stackNo = stackNo;
      return DicImpInfo;
    },
    botReferenceChange(record, index) {
      var { imp_ControlLay_, imp_DownLay_, imp_Type_ } = record;
      imp_ControlLay_ = imp_ControlLay_.split("L")[1];
      imp_DownLay_ = imp_DownLay_.split("L")[1];
      var imp_CtlThicknessInH = this.seleDataZk.find(item => item.valueMember == record.imp_Type_).imp_CtlThicknessInH;
      var _obj = this.changeComputed({ imp_ControlLay_: imp_ControlLay_, imp_UpLay_: imp_DownLay_ });
      var _doubleObj = this.changeComputed({ imp_ControlLay_: imp_ControlLay_, imp_UpLay_: "1" });
      if (imp_ControlLay_ == 1 || imp_ControlLay_ == this.form.layers) {
        // 外层
        if (imp_CtlThicknessInH == 1) {
          this.impedanceTabData[index].imp_H1_ = this.getFloat(_obj.stackUpThichnessMIL + _obj.controlStackUpThichnessMIL, 3) + "";
          this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo + _obj.controlStackNo;
        } else {
          this.impedanceTabData[index].imp_H1_ = _obj.stackUpThichnessMIL + "";
          this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo;
        }
        this.impedanceTabData[index].imp_Er1_ = _obj.stackUpDK + "";
      } else {
        // 内层
        if (_obj.stackUpCUTB) {
          // 含T的时候
          // 不包含_1
          if (imp_CtlThicknessInH == 1) {
            this.impedanceTabData[index].imp_H1_ = this.getFloat(_obj.stackUpThichnessMIL + _obj.controlStackUpThichnessMIL, 3) + "";
            this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo + _obj.controlStackNo;
          } else {
            this.impedanceTabData[index].imp_H1_ = _obj.stackUpThichnessMIL + "";
            this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _obj.stackNo;
          }
          this.impedanceTabData[index].imp_Er1_ = _obj.stackUpDK + "";
          // this.impedanceTabData[index].imp_Er1_ = _doubleObj.stackUpDK+''; 2023/6/7 要求调整
          if (imp_Type_.indexOf("_1") != -1) {
            // 包含_1
            this.impedanceTabData[index].imp_H2_ = this.getFloat(_doubleObj.stackUpThichnessMIL + _doubleObj.controlStackUpThichnessMIL, 3) + "";
            this.impedanceTabData[index].imp_Er2_ = _doubleObj.stackUpDK + "";
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = _doubleObj.stackNo + _doubleObj.controlStackNo;
            this.impedanceTabData[index].imp_UpLay_ = undefined;
          }
        } else {
          // 不含T的时候
          //
          // 不包含_1
          if (imp_CtlThicknessInH == 2) {
            this.impedanceTabData[index].imp_H2_ = this.getFloat(_obj.stackUpThichnessMIL + _obj.controlStackUpThichnessMIL, 3) + "";
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = _obj.stackNo + _obj.controlStackNo;
          } else {
            this.impedanceTabData[index].imp_H2_ = _obj.stackUpThichnessMIL + "";
            this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = _obj.stackNo;
          }
          this.impedanceTabData[index].imp_Er2_ = _obj.stackUpDK + "";
          if (imp_Type_.indexOf("_1") != -1) {
            // 包含_1
            if (imp_CtlThicknessInH == 2) {
              this.impedanceTabData[index].imp_H1_ = this.getFloat(_doubleObj.stackUpThichnessMIL + _doubleObj.controlStackUpThichnessMIL, 3) + "";
              this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _doubleObj.stackNo + _doubleObj.controlStackNo;
            } else {
              this.impedanceTabData[index].imp_H1_ = _doubleObj.stackUpThichnessMIL + "";
              this.impedanceTabData[index].imp_H1IncludeLyrNoList_ = _doubleObj.stackNo;
            }
            this.impedanceTabData[index].imp_Er1_ = _doubleObj.stackUpDK + "";
            this.impedanceTabData[index].imp_UpLay_ = undefined;
            // this.impedanceTabData[index].imp_H2_ = this.getFloat(_doubleObj.stackUpThichnessMIL + _doubleObj.controlStackUpThichnessMIL, 3) + '';
            // this.impedanceTabData[index].imp_Er2_ = _doubleObj.stackUpDK + '';
            // this.impedanceTabData[index].imp_H2IncludeLyrNoList_ = _doubleObj.stackNo;
          }
        }
      }
    },
    changeComputed(record, OutLay) {
      var { imp_ControlLay_, imp_UpLay_ } = record;
      if (!imp_ControlLay_ || !imp_UpLay_) {
        return;
      }
      var startlayer_,
        endLayer_,
        startIndex_,
        endIndex_,
        stackUpThichnessMM = 0, // *
        stackUpThichnessMIL = 0, // *累加
        stackUpMTR = 0,
        stackUpDK = 0, // *
        stackNo = "", // *
        controlStackNo, // *单次
        controlStackUpThichnessMIL, // *单次
        stackUpCUTB; // *

      if (Number(imp_ControlLay_) > Number(imp_UpLay_)) {
        startlayer_ = imp_UpLay_;
        endLayer_ = imp_ControlLay_;
      } else {
        startlayer_ = imp_ControlLay_;
        endLayer_ = imp_UpLay_.toString();
      }
      startIndex_ = this.data.map(item => (item.stackUpLayerNo_ ? item.stackUpLayerNo_.toString() : item.stackUpLayerNo_)).indexOf(startlayer_);
      endIndex_ = this.data.map(item => (item.stackUpLayerNo_ ? item.stackUpLayerNo_.toString() : item.stackUpLayerNo_)).indexOf(endLayer_);
      this.data.map((item, idx) => {
        if (item.stackUpLayerNo_ == imp_ControlLay_) {
          controlStackUpThichnessMIL = Number(item.stackUpThichnessMIL_);
          controlStackNo = idx + 1;
          stackUpCUTB = item.stackUpCUTB_ == "t" ? true : false;
        }
        if (idx > startIndex_ && idx < endIndex_) {
          stackUpThichnessMM += Number(item.stackUpThichnessMM_);
          stackUpThichnessMIL += Number(item.stackUpThichnessMIL_);
          if (item.stackUpMTR_ == "PP" || item.stackUpMTR_ == "Core") {
            stackUpMTR += 1;
            stackUpDK += Number(item.stackUpDK_);
          }
          stackNo += idx + 1 + "|";
        }
      });
      stackUpDK = stackUpDK / stackUpMTR || 0;
      return {
        stackUpThichnessMM: Number(stackUpThichnessMM.toFixed(3)),
        stackUpThichnessMIL: Number(stackUpThichnessMIL.toFixed(3)),
        controlStackUpThichnessMIL: Number(controlStackUpThichnessMIL.toFixed(3)),
        stackUpDK: Number(stackUpDK.toFixed(3)),
        stackNo: stackNo,
        controlStackNo: controlStackNo,
        stackUpCUTB: stackUpCUTB,
      };
    },
    impBCChage(text, index) {
      // console.log('点击补偿',text,this.impedanceTabData)
    },
    addImpedance() {
      const value = {
        imp_Type_: "", // 阻抗类型 （外层单端） O_S(SM)
        imp_ControlLay_: "", // 控制层 L1
        imp_UpLay_: "", // 上参
        imp_DownLay_: "", // 下参
        imp_LineWidth_: "", // 线宽
        imp_LineSpace_: "", // 线隙
        imp_LineCuSpace_: "", // 线铜
        imp_Value_Req_: "", // 要求
        imp_Value_Tol_: "", // 公差
        imp_OKLineWidth_: "", // L/W
        imp_OKLineSpace_: "", // L/S
        imp_OKLineCuSpace_: "", // L/CU
        imp_TrueValueWithOutSM_: "", // Imp(Eth)
        imp_TrueValue_: "", // Imp
        imp_H1_: "", // H1
        imp_Er1_: "", // Er1
        imp_H2_: "", // H2
        imp_Er2_: "", // Er2
        imp_W1_: "", // W1
        imp_W2_: "", // W2
        imp_S1_: "", // S1
        imp_D1_: "", // D1
        imp_T1_: "", // T1
        imp_BC_: "",
        imp_C1_: "", // C1
        imp_C2_: "", // C2
        imp_C3_: "", // C3
        imp_CEr_: "", // CEr
        imp_H1IncludeLyrNoList_: "", // Imp-H1
        imp_H2IncludeLyrNoList_: "", // Imp-H2
        imp_CtlTB_: "", // T/B
      };
      this.impedanceTabData.push(value);
      // if(this.form.layers){
      //   this.controlChange(value, this.impedanceTabData.indexOf(value))
      //   value.imp_DownLay_ = 'L2'
      //   this.botReferenceChange(value, this.impedanceTabData.indexOf(value))
      //   value.imp_LineWidth_ = '5'
      //   this.lineWidthChage(value, this.impedanceTabData.indexOf(value))
      //   value.imp_Value_Req_ = '50'
      //   this.reqChange(value, this.impedanceTabData.indexOf(value))
      // }
    },
    removeImpedance(e, record) {
      e.preventDefault();
      const index_ = this.impedanceTabData.indexOf(record);
      this.impedanceTabData.splice(index_, 1);
      this.delKey = true;
      this.selectedRowKeysArray = [];
      this.setedit(true);
    },
    copy1() {
      this.copyImpedance(this.datasource);
    },
    copyImpedance(record) {
      for (var a = 0; a < record.length; a++) {
        this.impedanceTabData.push({ ...record[a] }); // 使用展开运算符复制对象
      }
      this.setedit(true);
    },
    symmetric1() {
      this.symmetricImpedance(this.datasource);
    },
    symmetricImpedance(data) {
      // let _data = JSON.parse(JSON.stringify(this.menuData))
      for (var a = 0; a < data.length; a++) {
        var record = data[a];
        let _data = {
          imp_Type_: record.imp_Type_, //阻抗类型
          imp_ControlLay_: "", //控制层
          imp_DownLay_: "", //下参
          imp_UpLay_: "", //上参
        };
        this.impedanceTabData.push(_data);
        if (record.imp_ControlLay_) {
          _data.imp_ControlLay_ = "L" + (this.form.layers - record.imp_ControlLay_.split("L")[1] + 1);
          this.controlChange(_data, this.impedanceTabData.indexOf(_data));
        } else {
          _data.imp_ControlLay_ = "";
        }
        if (record.imp_DownLay_) {
          _data.imp_UpLay_ = "L" + (this.form.layers - record.imp_DownLay_.split("L")[1] + 1);
          this.upLayChange(_data, this.impedanceTabData.indexOf(_data));
        } else {
          _data.imp_UpLay_ = "";
        }
        if (record.imp_UpLay_) {
          _data.imp_DownLay_ = "L" + (this.form.layers - record.imp_UpLay_.split("L")[1] + 1);
          this.downChange(_data, this.impedanceTabData.indexOf(_data));
        } else {
          if (_data.imp_Type_.indexOf("_1") != -1) {
            _data.imp_DownLay_ = null;
          } else {
            _data.imp_DownLay_ = "";
          }
        }
        _data.imp_LineWidth_ = record.imp_LineWidth_;
        _data.imp_LineSpace_ = record.imp_LineSpace_;
        _data.imp_LineCuSpace_ = record.imp_LineCuSpace_;
        _data.imp_Value_Req_ = record.imp_Value_Req_;
        // 下参change：botReferenceChange
        // 上参change：controlLayChange
        this.lineWidthChage(_data, this.impedanceTabData.indexOf(_data));
        this.lineGapChange(_data, this.impedanceTabData.indexOf(_data));
        this.lineCopperChange(_data, this.impedanceTabData.indexOf(_data));
        this.reqChange(_data, this.impedanceTabData.indexOf(_data));
      }
      this.setedit(true);
      // 线宽change：lineWidthChage
      // 线隙change：lineGapChange
      // 线铜change：lineCopperChange
      // 要求change：reqChange
      // 2023/3/30 右键功能调整
      // if (this.menuData) {

      //   // let _data = JSON.parse(JSON.stringify(this.menuData))
      //   let _data = {
      //     'imp_Type_' : this.menuData.imp_Type_,
      //     'imp_ControlLay_': '',
      //     'imp_DownLay_': '',
      //     'imp_UpLay_': '',
      //   }
      //   this.impedanceTabData.push(_data)
      //   if (this.menuData.imp_ControlLay_) {
      //     _data.imp_ControlLay_ = 'L'+ (this.form.layers - this.menuData.imp_ControlLay_.split('L')[1] + 1)
      //     this.controlChange(_data, this.impedanceTabData.indexOf(_data))
      //   } else {
      //     _data.imp_ControlLay_ = ''
      //   }
      //   if (this.menuData.imp_DownLay_){
      //     _data.imp_UpLay_ = 'L'+ (this.form.layers - this.menuData.imp_DownLay_.split('L')[1] + 1)
      //     this.controlLayChange(_data, this.impedanceTabData.indexOf(_data))
      //   } else {
      //     _data.imp_UpLay_ = ''
      //   }
      //   if (this.menuData.imp_UpLay_) {
      //     _data.imp_DownLay_ = 'L'+ (this.form.layers - this.menuData.imp_UpLay_.split('L')[1] + 1)
      //     this.botReferenceChange(_data, this.impedanceTabData.indexOf(_data))

      //   } else {
      //     _data.imp_DownLay_ = ''
      //   }
      //   _data.imp_LineWidth_ = this.menuData.imp_LineWidth_
      //   _data.imp_LineSpace_ = this.menuData.imp_LineSpace_
      //   _data.imp_LineCuSpace_ = this.menuData.imp_LineCuSpace_
      //   _data.imp_Value_Req_ = this.menuData.imp_Value_Req_

      //   // 下参change：botReferenceChange
      //   // 上参change：controlLayChange
      //   this.lineWidthChage(_data, this.impedanceTabData.indexOf(_data))
      //   this.lineGapChange(_data, this.impedanceTabData.indexOf(_data))
      //   this.lineCopperChange(_data, this.impedanceTabData.indexOf(_data))
      //   this.reqChange(_data, this.impedanceTabData.indexOf(_data))
      //   // 线宽change：lineWidthChage
      //   // 线隙change：lineGapChange
      //   // 线铜change：lineCopperChange
      //   // 要求change：reqChange

      // } else {
      //   this.$message.info('请选择你要对称的行')
      // }
    },
    lsChange(text, index) {
      this.impedanceTabData[index].imp_SetInStackup_ = 1;
    },
    lcuChange(text, index) {
      this.impedanceTabData[index].imp_D1_ = text.imp_OKLineCuSpace_;
    },
    lwChange(text, index) {
      this.impedanceTabData[index].imp_W1_ = text.imp_OKLineWidth_;
      this.impedanceTabData[index].imp_TrueValue_ = "";
      this.impedanceTabData[index].imp_TrueValueWithOutSM_ = "";
      this.impedanceTabData[index].Imp_ValueColor_ = "2";
      this.impedanceTabData[index].imp_SetInStackup_ = 1;
      let count = 0;
      if (this.impedanceTabData[index].imp_OKLineSpace_) {
        count =
          Number(this.impedanceTabData[index].imp_LineSpace_) +
          Number(this.impedanceTabData[index].imp_LineWidth_) -
          Number(this.impedanceTabData[index].imp_OKLineWidth_);
        var floatValue = parseFloat(count).toFixed(3);
        this.impedanceTabData[index].imp_OKLineSpace_ = floatValue.replace(/\.?0+$/, "");
        this.impedanceTabData[index].imp_S1_ = this.impedanceTabData[index].imp_OKLineSpace_;
      }
      if (this.impedanceTabData[index].imp_OKLineCuSpace_) {
        count =
          Number(this.impedanceTabData[index].imp_LineCuSpace_) +
          (Number(this.impedanceTabData[index].imp_LineWidth_) - Number(this.impedanceTabData[index].imp_OKLineWidth_)) / 2;
        var floatValue1 = parseFloat(count).toFixed(3);
        this.impedanceTabData[index].imp_OKLineCuSpace_ = floatValue1.replace(/\.?0+$/, "");
        this.impedanceTabData[index].imp_D1_ = this.impedanceTabData[index].imp_OKLineCuSpace_;
      }

      let controlLayer = text.imp_ControlLay_.split("L")[1];
      let count_1;
      let controItem_ = this.data.find(item => {
        return item.stackUpLayerNo_ == controlLayer;
      });
      if (controlLayer == 1 || controlLayer == this.form.layers) {
        let outLayer_ = this.someList.filter(res => {
          return res.iO_ == "910";
        });
        count_1 = outLayer_.filter(ite => {
          return ite.cu_ == controItem_?.stackUpMTRFoil_;
        });
      } else {
        count_1 = this.someList
          .filter(res => {
            return res.iO_ == "911";
          })
          .filter(ite => {
            return ite.cu_ == controItem_?.stackUpMTRFoil_;
          });
      }
      if (count_1.length > 0) {
        this.impedanceTabData[index].imp_W2_ = this.getFloat(Number(this.impedanceTabData[index].imp_W1_) - count_1[0].lineWidthDiff_, 2) + "";
      } else {
        this.impedanceTabData[index].imp_W2_ = "";
      }
    },
    rightClick(e) {
      if (e.target.className.indexOf("impedance") != -1) {
        this.menuData = "";
      }
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 160 + "px";
      this.menuStyle.left = e.clientX - 100 + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    impHandelClick(res) {
      // let str = ''
      res.data.forEach((item, index) => {
        this.impedanceTabData[index].imp_TrueValueWithOutSM_ = item.imp_TrueValueWithOutSM_;
        this.impedanceTabData[index].imp_TrueValue_ = item.imp_TrueValue_;
        // if (item.imp_ValueColor_ == '2') {
        //   str += `第 ${index+1} 行阻抗计算错误`
        // }
        this.impedanceTabData[index]["imp_ValueColor_"] = item.imp_ValueColor_;
      });
      this.$forceUpdate();
      // this.$info({
      //   title: '阻抗计算',
      //   content: str
      // });
    },
    impInverseClick(res, ind) {
      if (ind != undefined) {
        res.forEach(item => {
          this.impedanceTabData[ind].imp_OKLineWidth_ = item.imp_OKLineWidth_;
          this.impedanceTabData[ind].imp_OKLineSpace_ = item.imp_OKLineSpace_;
          this.impedanceTabData[ind].imp_TrueValue_ = item.imp_TrueValue_;
          this.impedanceTabData[ind].imp_OKLineCuSpace_ = item.imp_OKLineCuSpace_;
          this.impedanceTabData[ind].imp_D1_ = item.imp_D1_;
          this.impedanceTabData[ind].imp_S1_ = item.imp_S1_;
          this.impedanceTabData[ind].imp_W1_ = item.imp_W1_;
          this.impedanceTabData[ind].imp_W2_ = item.imp_W2_;
          this.impedanceTabData[ind].imp_TrueValueWithOutSM_ = item.imp_TrueValueWithOutSM_;
          this.impedanceTabData[ind].imp_ValueColor_ = item.imp_ValueColor_;
        });
      } else {
        res.forEach((item, index) => {
          this.impedanceTabData[index].imp_OKLineWidth_ = item.imp_OKLineWidth_;
          this.impedanceTabData[index].imp_OKLineSpace_ = item.imp_OKLineSpace_;
          this.impedanceTabData[index].imp_TrueValue_ = item.imp_TrueValue_;
          this.impedanceTabData[index].imp_OKLineCuSpace_ = item.imp_OKLineCuSpace_;
          this.impedanceTabData[index].imp_D1_ = item.imp_D1_;
          this.impedanceTabData[index].imp_S1_ = item.imp_S1_;
          this.impedanceTabData[index].imp_W1_ = item.imp_W1_;
          this.impedanceTabData[index].imp_W2_ = item.imp_W2_;
          this.impedanceTabData[index].imp_TrueValueWithOutSM_ = item.imp_TrueValueWithOutSM_;
          this.impedanceTabData[index].imp_ValueColor_ = item.imp_ValueColor_;
          this.$forceUpdate();
        });
      }
    },
    resizeImpedance() {
      this.impedanceTabData.forEach((item, index) => {
        this.upLayChange(item, index);
        this.downChange(item, index);
      });
    },
    inverseCoun1t() {
      //this.inverseCount(this.datasource,this.rowIndex)
      for (var i = 0; i < this.selectedRowKeysArray.length; i++) {
        var selectedIndex = this.selectedRowKeysArray[i];
        this.inverseCount(this.impedanceTabData[selectedIndex], selectedIndex);
      }
    },
    // 阻抗反算
    inverseCount(text, index) {
      this.$emit("inverseCount", text, index);
    },
    isRedRow(record, index) {
      let strGroup = [];
      let str = [];
      if (this.selectedRowKeysArray.includes(index) && !this.laminsert) {
        strGroup.push("rowBackgroundColor");
      }
      return strGroup;
    },
    customRow(record, index) {
      return {
        on: {
          mousedown: event => this.handleMouseDown(event, record, index),
          mousemove: event => this.handleMouseMove(event, record, index),
          mouseup: event => this.handleMouseUp(event, record, index),
          contextmenu: e => {
            e.preventDefault();
            if (this.selectedRowsData.length != 0) {
              this.rightClick1(e, record, this.selectedRowsData);
            }
            // let text = ''
            // if(e.target.innerText){
            //  text = e.target.innerText
            // }
            // this.$emit('rightClick1',e,text,record)
            // e.preventDefault();
            // this.menuData = record;
          },
        },
      };
    },
    addRow() {
      this.impedanceTabData.splice(this.activeIndex + 1, 0, {
        imp_Type_: "",
        imp_ControlLay_: "",
        imp_UpLay_: "",
        imp_DownLay_: "",
        imp_LineWidth_: "",
        imp_LineSpace_: "",
        imp_LineCuSpace_: "",
        imp_Value_Req_: "",
        imp_Value_Tol_: "",
        imp_OKLineWidth_: "",
        imp_OKLineSpace_: "",
        imp_OKLineCuSpace_: "",
        imp_TrueValueWithOutSM_: "",
        imp_TrueValue_: "",
        imp_H1_: "",
        imp_Er1_: "",
        imp_H2_: "",
        imp_Er2_: "",
        imp_W1_: "",
        imp_W2_: "",
        imp_S1_: "",
        imp_D1_: "",
        imp_T1_: "",
        imp_BC_: "",
        imp_C1_: "",
        imp_C2_: "",
        imp_C3_: "",
        imp_CEr_: "",
        imp_H1IncludeLyrNoList_: "",
        imp_H2IncludeLyrNoList_: "",
        imp_CtlTB_: "",
      });
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
      if (e.keyCode == "45" && this.ins && !this.laminsert) {
        this.addRow();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
      if (e.keyCode == "46" && this.del && !this.laminsert) {
        e.preventDefault();
        this.removeImpedance(e, this.selectedRowsData[0]);
        this.isCtrlPressed = false;
      }
    },
    handleMouseDown(event, record, index) {
      this.activeIndex = index;
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.startIndex = index;
      }
    },
    handleMouseMove(event, record, index) {
      // event.stopPropagation();
      this.shiftKey = false;
      if (this.isDragging && event.button === 0 && this.startIndex != index) {
        this.updateSelectedItems(this.startIndex, index);
      }
    },
    updateSelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.impedanceTabData.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      // for(var a=0;a<selectedRowsData.length;a++){
      //   arr.push(a)
      // }
      for (let i = normalizedStart; i <= normalizedEnd; i++) {
        arr.push(i);
      }
      this.selectedRowKeysArray = arr;
      this.selectedRowsData = selectedRowsData;
      // if(startIndex < endIndex){
      //   this.selectedRowsData = this.impedanceTabData.filter((item,index) => {return index == this.selectedRowKeysArray[this.selectedRowKeysArray.length-1]})[0]
      // }else{
      //   this.selectedRowsData = this.impedanceTabData.filter((item,index) => {return index == this.selectedRowKeysArray[0]})[0]
      // }
    },
    handleMouseUp(event, record, index) {
      event.stopPropagation();
      this.isDragging = false;
      this.selectedRowsData = [];
      if (this.shiftKey) {
        let keys = this.selectedRowKeysArray;
        if (keys.length > 0 && keys.includes(index)) {
          keys.splice(keys.indexOf(index), 1);
        } else {
          keys.push(index);
        }
        this.selectedRowKeysArray = keys;
        if (this.selectedRowKeysArray.length == 1) {
          this.selectedRowsData = record;
        }
      }
      if (this.startIndex === index && !this.shiftKey && event.button != 2) {
        this.selectedRowKeysArray = [index];
        this.selectedRowsData = [record];
      }
      this.shiftKey = false;
      this.delKey = false;
      for (var i = 0; i < this.selectedRowKeysArray.length; i++) {
        this.selectedRowsData.push(this.impedanceTabData[this.selectedRowKeysArray[i]]);
      }
      this.activeIndex = index;
      if (this.selectedRowsData.length) {
        this.ins = true;
        this.del = true;
      } else {
        this.ins = false;
        this.del = false;
      }
      this.$emit("insert1", this.ins, 2);
    },
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
    window.addEventListener("click", this.keyclick);
  },
};
</script>

<style lang="less" scoped>
.backgcolor {
  background-color: #9acd32;
}
.tabRightClikBox {
  li {
    height: 25px;
    line-height: 25px;
    margin-top: 0;
    margin-bottom: 0;
    font-size: 12px;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: rgb(255, 255, 255) !important;
    color: #000000;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
  color: #000000;
}

/deep/ .ant-table-thead > tr > th {
  padding: 2px 4px !important;
}
// /deep/ .ant-table-tbody tr:hover td {
//   background: none!important;
// } 2023/6/25 行颜色
/deep/ .ant-table-tbody tr:hover td {
  background: #d6d6d6 !important;
}
/deep/ .ant-table-tbody tr td {
  background: #d6d6d6 !important;
}
/deep/.ant-table-tbody .rowBackgroundColor td {
  background: rgb(244, 164, 96) !important;
}
/deep/.ant-table-tbody .rowBackgroundColor:hover td {
  background: rgb(244, 164, 96) !important;
}
/deep/ .ant-table-tbody > tr > td {
  font-size: 12px !important;
  padding: 0 !important;
  .ant-select {
    width: 100%;
    .ant-select-selection__rendered {
      margin: 0;
      width: 100%;
      height: 100%;
      line-height: 22px;
      .ant-select-selection-selected-value {
        width: 100%;
        height: 100%;
        text-align: center;
      }
    }
  }
  .ant-select-selection {
    border-radius: 0 !important;
    border: 0;
    height: 100%;
  }
}
.impedance {
  height: 100%;
  /deep/ .ant-table-placeholder {
    display: none;
  }
  /deep/ .ant-table-small > .ant-table-content > .ant-table-body {
    margin: 0;
  }

  /deep/ .ant-table-thead > tr > th {
    padding: 0 !important;
    background: linear-gradient(#f7f7f7, #f0f0f0);
    border-style: solid;
    border-color: #ccc;
    border-width: 0 1px 1px 0;
    height: 30px;
    font-size: 14px;
  }
  /deep/ .ant-table-tbody > tr > td {
    font-size: 12px !important;
    padding: 0 !important;
    height: 22px !important;
    line-height: 22px !important;
    font-weight: 500 !important;

    .ant-input {
      height: 22px;
    }

    .ant-select {
      font-size: 12px;
      width: 100%;

      .ant-select-selection--single {
        height: 22px;
      }

      .ant-select-selection__rendered {
        margin: 0;
        width: 100%;
        height: 100%;

        .ant-select-selection-selected-value {
          width: 100%;
          height: 100%;
          text-align: center;
          line-height: 22px;
        }
      }
    }
  }
  width: 100%;
  // min-height: 100px;
  // min-height: 374px;
  // max-height: 726px;
  // overflow: auto;
  /deep/ .ant-spin-nested-loading {
    width: 100%;
  }

  /deep/ .ant-table-body .ant-input {
    padding: 0;
    border: 0;
    border-radius: 0;
    text-align: center;
    height: auto;
  }
}
</style>
