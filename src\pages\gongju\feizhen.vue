<!-- 工具管理 -飞针制作 -->
<template>
  <div ref="SelectBox">
    <a-spin :spinning="uploadLoading">
      <a-card style="min-width: 1670px">
        <div class="head">
          <span style="font-size: 17px; color: #ff9900; font-weight: 500">操作流程：</span>
          <div style="display: flex; align-items: center">
            <i class="indexStyle">1</i>
            <span style="margin-left: 10px">设置STEP、GKO命名 </span>
          </div>
          <img src="../../assets/img/buzhou.png" alt="" />
          <div style="display: flex; align-items: center">
            <i class="indexStyle">2</i>
            <span style="margin-left: 10px">上传需要制作的文件（.tgz） </span>
          </div>
          <img src="../../assets/img/buzhou.png" alt="" />
          <div style="display: flex; align-items: center">
            <i class="indexStyle">3</i>
            <span style="margin-left: 10px"
              >飞针资料制作完后界面状态变成制作完成，<br />
              点击下载即可下载做好的飞针资料
            </span>
          </div>
        </div>
        <div style="background: white; padding: 20px; min-height: 735px">
          <div style="display: flex; justify-content: space-between; align-items: center">
            <div>
              <a-form-model layout="inline" :model="form">
                <a-input v-model="form.id" placeholder="文件名称" style="width: 150px" />
                <a-select
                  v-model="form.stat"
                  placeholder="制作状态"
                  style="width: 150px; margin-left: 10px"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option value=""> 请选择 </a-select-option>
                  <a-select-option value="制作失败"> 制作失败 </a-select-option>
                  <a-select-option value="制作完成"> 制作完成 </a-select-option>
                  <a-select-option value="待制作"> 待制作 </a-select-option>
                </a-select>
                <a-select
                  v-model="form.uploadStat"
                  placeholder="下载状态"
                  style="width: 150px; margin-left: 10px"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option value=""> 请选择 </a-select-option>
                  <a-select-option value="已下载"> 已下载 </a-select-option>
                  <a-select-option value="未下载"> 未下载 </a-select-option>
                </a-select>
                <a-select
                  v-model="form.time"
                  placeholder="时间"
                  style="width: 150px; margin-left: 10px"
                  :getPopupContainer="() => this.$refs.SelectBox"
                >
                  <a-select-option value="半个月"> 半个月 </a-select-option>
                  <a-select-option value="三个月"> 三个月 </a-select-option>
                  <a-select-option value="全部"> 全部 </a-select-option>
                </a-select>
                <a-input v-model="form.user" placeholder="上传用户" style="width: 150px; margin-left: 10px" />
                <a-button @click="toDetail('', 1)" style="margin-left: 10px" type="primary"> 查询</a-button>
              </a-form-model>
            </div>
            <div style="display: flex">
              <a-button @click="toDetail('', 3)" style="margin-right: 10px; background: #999999; color: white"
                ><img src="../../assets/img/site.png" alt="" style="width: 20px; height: 20px; margin-right: 10px" /> 设置</a-button
              >
              <div style="position: relative" @click="uploadFileBtn">
                <a-button type="primary"
                  ><img
                    src="../../assets/img/upload.png"
                    alt=""
                    style="width: 20px; height: 20px; margin-right: 10px; margin-top: 2px"
                  />上传</a-button
                >
                <a-upload
                  name="file"
                  :multiple="false"
                  :customRequest="customRequest"
                  @change="handleChangeImg"
                  :showUploadList="false"
                  accept="application/x-compressed"
                  ref="fileRef"
                  v-show="false"
                >
                  <a-button style="width: 64px"><a-icon type="upload" /></a-button>
                </a-upload>
              </div>
            </div>
          </div>
          <div style="display: flex; margin-bottom: 10px">
            <!--<a-button type="primary"  @click="toDetail(2)" style="margin-left: 10px"> 下载</a-button>-->
            <!--<a-button type="primary"  @click="toDetail(1)" style="margin-left: 10px"> 查询</a-button>-->
            <!--<a-button type="primary"  @click="toDetail(3)" style="margin-left: 10px"> 设置</a-button>-->
            <!--<a-button type="primary"  @click="toDetail(4)" style="margin-left: 10px"> 取消</a-button>-->
          </div>

          <a-table
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="pagination"
            @change="handleTableChange"
            :loading="loading"
            :scroll="{ x: 900 }"
            :customRow="Rowclick"
          >
            <!--:row-selection="{ selectedRowKeys: selectedRowKeys_cnt, onChange: onSelectChange_content ,type: 'radio'}"-->
            <span slot="downloadStatus" slot-scope="record">
              <!--<a href="javascript:;" @click="toDetail(record,'1')" >上传</a>-->
              <!--<a-divider type="vertical" />-->
              <span href="javascript:;" v-if="record == '未下载'">
                <img src="../../assets/img/down.png" alt="" />
                {{ record }}
              </span>
              <span href="javascript:;" v-else style="color: #adadad">
                <img src="../../assets/img/downLoad.png" alt="" />
                {{ record }}
              </span>
            </span>
            <div slot="action" slot-scope="record" @click="toDetail(record, '2')">
              <span><img src="../../assets/img/xiazai.png" alt="" /></span>
            </div>
            <div slot="del" slot-scope="record" @click="toDetail(record, '4')">
              <span><img src="../../assets/img/dele.png" alt="" /></span>
            </div>
          </a-table>
        </div>

        <a-modal title="提示" :visible="visible1" :confirm-loading="confirmLoading" @ok="handleOk1" @cancel="handleCancel1" centered>
          <a-form-model :model="form1" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-form-model-item label="大板STEP命名">
              <a-input v-model="form1.stepName" />
            </a-form-model-item>
            <a-form-model-item label="GKO命名">
              <a-input v-model="form1.gkoName" />
            </a-form-model-item>
          </a-form-model>
        </a-modal>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { flyingList, flyingUpload, flyingAdd, downLoad, flyingCancel, flyingSet, getFlyingSet } from "@/services/gongju/instrument";
let columns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 80,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "文件名称",
    dataIndex: "id",
  },
  {
    title: "制作状态",
    dataIndex: "makeStatus",
  },
  {
    title: "创建时间",
    dataIndex: "createDate",
  },
  {
    title: "完成时间",
    dataIndex: "endDate",
  },
  {
    title: "制作信息",
    dataIndex: "message_",
  },
  {
    title: "下载状态",
    dataIndex: "downloadStatus",
    scopedSlots: { customRender: "downloadStatus" },
  },
  {
    title: "下载",
    key: "action",
    scopedSlots: { customRender: "action" },
  },
  {
    title: "取消",
    key: "del",
    scopedSlots: { customRender: "del" },
  },
  {
    title: "上传用户",
    dataIndex: "userLoginId",
  },
];
export default {
  name: "",
  data() {
    return {
      columns: columns,
      dataSource: [],
      pagination: {
        pageSize: 10,
        current: 1,
        total: 0,
        showTotal: total => `总计 ${total} 条`,
      },
      loading: false,
      confirmLoading: false,
      visible1: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      uploadLoading: false,
      form: {
        id: "",
        // stat: '',
        // uploadStat: '',
        time: "半个月",
        user: "",
      },
      form1: {
        stepName: "",
        gkoName: "",
      },
      imgName: "",
      ID: "",
      trIndex: null,
      selectedRowKeys_cnt: [],
    };
  },
  component: {},
  methods: {
    onSelectChange_content(selectedRowKeys, selectedRows) {
      this.selectedRowKeys_cnt = selectedRowKeys;
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.getList();
    },
    toDetail(data, index) {
      if (index == "1") {
        this.getList();
        // this.form={
        //     id:'',
        //     stat: undefined,
        //     uploadStat: undefined,
        // }
      }
      if (index == "2") {
        downLoad(data.id).then(res => {
          if (res.code == "1") {
            var link = document.createElement("a");
            link.setAttribute("download", this.ID);
            link.href = res.data;
            link.click();
            link.remove();
            this.getList();
          } else {
            this.$message.info(res.message);
          }
        });
      }
      if (index == "3") {
        this.visible1 = true;
        getFlyingSet().then(res => {
          this.form1.stepName = res.data.stepName;
          this.form1.gkoName = res.data.gkoName;
        });
      }
      if (index == "4") {
        flyingCancel(data.id).then(res => {
          this.$message.info(res.message);
          this.getList();
        });
      }
    },
    getList() {
      let parmas = {
        Id: this.form.id,
        MakeStatus: this.form.stat,
        Time: this.form.time,
        DownloadStatus: this.form.uploadStat,
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      if (this.form.user) {
        parmas["UserLoginId"] = this.form.user;
      }
      flyingList(parmas).then(res => {
        this.dataSource = res.items;
        this.pagination.total = res.totalCount;
      });
    },
    uploadFileBtn() {
      getFlyingSet().then(res => {
        if (res.data.gkoName && res.data.stepName) {
          this.$refs.fileRef.$el.children[0].children[0].children[0].dispatchEvent(new MouseEvent("click"));
        } else {
          this.$message.warning("请先设置STEP、GKO命名");
        }
      });
    },
    //文件上传
    handleChangeImg(info) {
      this.imgName = `${info.file.name}`.substring(0, `${info.file.name}`.indexOf("."));
      // this.$forceUpdate();
    },
    customRequest(data) {
      this.uploadLoading = true;
      const formData = new FormData();
      formData.append("file", data.file);
      flyingUpload(formData).then(res => {
        let parmas = {
          id: this.imgName,
          upFileData_: new Date(),
          feiZhengTgzPath_: res,
          userLoginId: JSON.parse(localStorage.getItem("admin.user")).userName,
        };
        flyingAdd(parmas).then(res => {
          this.uploadLoading = false;
          this.$message.info(res.message);
          this.getList();
        });
      });
    },
    //查询
    Rowclick(record, index) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeys_cnt = keys;
            if (this.trIndex == null) {
              this.trIndex = index;
              document.getElementsByClassName("ant-table-row")[index].style.background = "#FFF9E6";
            } else if (this.trIndex != index) {
              document.getElementsByClassName("ant-table-row")[this.trIndex].style.background = "";
              document.getElementsByClassName("ant-table-row")[index].style.background = "#FFF9E6";
              this.trIndex = index;
            }
            this.ID = record.id;
          },
        },
      };
    },
    //设置
    handleOk1() {
      this.visible1 = false;
      flyingSet(this.form1).then(res => {
        this.$message.info(res.message);
        this.getList();
        this.form1 = {
          stepName: "",
          gkoName: "",
        };
      });
    },
    handleCancel1() {
      this.visible1 = false;
      this.form1 = {
        stepName: "",
        gkoName: "",
      };
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style scoped lang="less">
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}
/deep/.ant-input {
  font-weight: 500;
}
/deep/ .ant-upload.ant-upload-select {
  opacity: 0;
}
/deep/.ant-table-body {
  tr {
    td {
      padding: 14px 16px;
    }
  }
  &::-webkit-scrollbar {
    //整体样式
    width: 0; //y轴滚动条粗细
    height: 0;
  }
  &::-webkit-scrollbar-thumb {
    //滑动滑块条样式
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    // background: #00aaff;
    background: #eff1f7;
  }
  &::-webkit-scrollbar-track {
    //轨道的样式
    -webkit-box-shadow: 0;
    border-radius: 0;
    background: #f6f8ff;
  }
}
/deep/.ant-table-row-selected td {
  background: #fffaf2 !important;
  color: #ff9900 !important;
}
.ant-card {
  background: #f4f4f3 !important;
}
.indexStyle {
  width: 36px;
  height: 36px;
  opacity: 0.8;
  display: inline-block;
  text-align: center;
  line-height: 36px;
  font-size: 22px;
  color: #ffffff;
  font-weight: 500;
  background: url("../../assets/img/1.png") no-repeat;
}
.head {
  width: 100%;
  height: 70px;
  background: white;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: space-around;
}
/deep/ .ant-card-body {
  padding: 0 !important;
}
</style>
