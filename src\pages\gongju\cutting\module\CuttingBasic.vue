<!-- 工具管理- 开料拼版-参数表 -->
<template>
  <div ref="SelectBox">
    <a-spin :spinning="spinning">
      <div class="tableInfo">
        <a-tabs type="card" @change="callback">
          <a-tab-pane key="1" tab="主要参数">
            <a-form-model :model="form">
              <a-form-model-item label="产品编号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" labelAlign="left" :colon="false">
                <a-input v-model="form.job" />
              </a-form-model-item>
              <a-row>
                <a-col :span="12">
                  <a-form-model-item label="层数" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-input-number v-model="form.layCount" :min="0" @change="layCount(form.layCount)" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="板厚" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.boardThink" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="12">
                  <a-form-model-item label="出货单位" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-select default-value="1" v-model="form.pcssetName" :getPopupContainer="() => this.$refs.SelectBox">
                      <a-select-option value="PCS"> PCS </a-select-option>
                      <a-select-option value="Set"> SET </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="PCS/SET" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-input-number v-model="form.pcsset" :min="0" @change="changechdw" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-form-model-item label="阻抗组数" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :colon="false" labelAlign="left">
                <a-input-number v-model="form.numUDSix" :min="0" @change="numUDSixChange" />
              </a-form-model-item>
              <!-- 阻抗组数大于0时 -->
              <a-row v-show="form.numUDSix > 0">
                <a-col :span="14">
                  <a-form-model-item
                    label="阻抗宽度"
                    class="labClass"
                    :label-col="{ span: 14 }"
                    :wrapper-col="{ span: 10 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input-number v-model="form.coupDW" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="10">
                  <a-form-model-item label="长度" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :colon="false" labelAlign="left">
                    <a-input-number v-model="form.coupDL" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <!-- 成品尺寸 -->
              <a-row class="marginTop">
                <a-col :span="14">
                  <a-form-model-item
                    label="成品尺寸"
                    class="labClass"
                    :label-col="{ span: 10 }"
                    :wrapper-col="{ span: 14 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model="form.cpLen" @change="changeCpLen" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="10">
                  <a-form-model-item label="X" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.cpWidth" @change="changeCpWidth" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="9">
                  <a-form-model-item
                    label="拼板间距"
                    :label-col="{ span: 16 }"
                    :wrapper-col="{ span: 8 }"
                    :colon="false"
                    labelAlign="left"
                    class="inputclass"
                  >
                    <a-input v-model="form.spaceD" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="5">
                  <a-form-model-item
                    label="X"
                    :label-col="{ span: 10 }"
                    :wrapper-col="{ span: 14 }"
                    :colon="false"
                    labelAlign="left"
                    class="inputclass"
                  >
                    <a-input v-model="form.spaceD_Y" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="10">
                  <a-form-model-item
                    label="开料间距"
                    class="inputclass"
                    :label-col="{ span: 14 }"
                    :wrapper-col="{ span: 10 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model="form.klSpaceD" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="14">
                  <a-form-model-item
                    label="最小边距"
                    class="labClass labGroup"
                    :label-col="{ span: 12 }"
                    :wrapper-col="{ span: 12 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-form-model-item
                      label="左"
                      class="borderClass"
                      :label-col="{ span: 7 }"
                      :wrapper-col="{ span: 17 }"
                      :colon="false"
                      labelAlign="left"
                    >
                      <a-input v-model.number="form.left" @change="form.right = form.left" />
                    </a-form-model-item>

                    <a-form-model-item
                      label="上"
                      class="borderClass"
                      :label-col="{ span: 7 }"
                      :wrapper-col="{ span: 17 }"
                      :colon="false"
                      labelAlign="left"
                    >
                      <a-input v-model.number="form.top" @change="form.bot = form.top" />
                    </a-form-model-item>
                  </a-form-model-item>
                </a-col>
                <a-col :span="10">
                  <a-form-model-item
                    label="右"
                    class="borderClass"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 17 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model.number="form.right" />
                  </a-form-model-item>

                  <a-form-model-item
                    label="下"
                    class="borderClass"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 17 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model.number="form.bot" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="14">
                  <a-form-model-item
                    label="Panal(长)"
                    class="labClass"
                    :label-col="{ span: 10 }"
                    :wrapper-col="{ span: 14 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model="form.minPanelLen" />
                  </a-form-model-item>
                  <a-form-model-item
                    label="Panal(宽)"
                    class="labClass"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 17 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model="form.minPanelWidth" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="10">
                  <a-form-model-item
                    label="~"
                    class="borderClass"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 17 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model="form.maxPanelLen" />
                  </a-form-model-item>

                  <a-form-model-item
                    label="~"
                    class="borderClass"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 17 }"
                    :colon="false"
                    labelAlign="left"
                  >
                    <a-input v-model="form.maxPanelWidth" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-form-model-item label="Panal最大长宽比例" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                <a-input v-model="form.maxBs" />
              </a-form-model-item>
              <a-form-model-item label="Panal最小长宽差值" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                <a-input v-model="form.minW" />
              </a-form-model-item>
              <a-form-model-item
                label="最低拼版利用率"
                :label-col="{ span: 14 }"
                :wrapper-col="{ span: 10 }"
                :colon="false"
                labelAlign="left"
                class="minPnlRate"
              >
                <a-input v-model="form.minPnlRate" />
              </a-form-model-item>

              <!--  -->
              <!-- <a-form-model-item label="纬向尺寸" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
            <a-input v-model="form.latitudinalSize" />
          </a-form-model-item> -->
              <a-row class="marginTop">
                <a-col :span="12">
                  <a-form-model-item label="套板设定" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-checkbox style="border-right: 0" v-model="form.isTaoBan" @change="TaoBanChange"> </a-checkbox>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="套板类型" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-select v-model="form.taoBanType">
                      <a-select-option value="0">0</a-select-option>
                      <a-select-option value="1">1</a-select-option>
                      <a-select-option value="2">2</a-select-option>
                      <a-select-option value="3">3</a-select-option>
                    </a-select>
                    <!-- <a-checkbox style="border-right: 0" v-model="form.isTaoBanYX" ></a-checkbox> -->
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-show="form.isTaoBan">
                <a-col :span="12">
                  <a-form-model-item label="单元长度" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.unitLen" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="单元宽度" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.unitWidth" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-show="form.isTaoBan">
                <a-col :span="12">
                  <a-form-model-item label="最大长度" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.unitLenMax" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="最小长度" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.unitLenMin" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-show="form.isTaoBan">
                <a-col :span="12">
                  <a-form-model-item label="最大宽度" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.unitWidthMax" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="最小宽度" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.unitWidthMin" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-show="form.isTaoBan">
                <a-col :span="12">
                  <a-form-model-item label="x数量" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.xnum" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="y数量" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.ynum" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-show="form.isTaoBan">
                <a-col :span="12">
                  <a-form-model-item label="x间距" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.setSpaceX" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="y间距" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.setSpaceY" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-show="form.isTaoBan">
                <a-col :span="12">
                  <a-form-model-item label="最小边距左" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.sideLeft" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="最小边距右" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.sideRight" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row v-show="form.isTaoBan">
                <a-col :span="12">
                  <a-form-model-item label="最小边距下" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.sideBot" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="最小边距上" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                    <a-input v-model="form.sideTop" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row class="marginTop">
                <a-col :span="6">
                  <p
                    style="
                      line-height: 104px;
                      height: 104px;
                      margin: 0;
                      border-bottom: 1px solid rgba(215, 215, 215, 1);
                      border-right: 1px solid rgba(215, 215, 215, 1);
                    "
                  >
                    拼板规则
                  </p>
                </a-col>
                <a-col :span="18">
                  <a-row>
                    <a-col :span="12" style="width: 44.4%">
                      <a-checkbox v-model="form.gdkl" @change="gdchange"> 固定尺寸 </a-checkbox>
                    </a-col>
                    <a-col :span="12" style="width: 55.6%">
                      <a-checkbox v-model="form.allowDifDirection"> 异向排版 </a-checkbox>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="12" style="width: 44.4%">
                      <a-checkbox v-model="form.anywayKL"> 横竖大料 </a-checkbox>
                      <!--                  <a-form-model-item label="横竖大料" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">-->
                      <!--                    <a-input v-model="form.name" />-->
                      <!--                  </a-form-model-item>-->
                    </a-col>
                    <a-col :span="12" style="width: 55.6%">
                      <a-checkbox v-model="form.isABBoard" @change="abcut"> AB开料 </a-checkbox>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="12" style="width: 44.4%">
                      <a-checkbox v-model="form.isVcut"> 大板V-cut </a-checkbox>
                    </a-col>
                    <a-col :span="12" style="width: 55.6%">
                      <a-form-model-item label="跳V(mm)" :label-col="{ span: 13 }" :wrapper-col="{ span: 11 }" labelAlign="left" :colon="false">
                        <a-input-number v-model="form.conventionalJumpVCutSize" :disabled="!form.isVcut" />
                      </a-form-model-item>
                    </a-col>
                  </a-row>
                  <a-row>
                    <a-col :span="12" style="width: 44.4%">
                      <a-checkbox v-model="form.limitBJ"> 极限边距 </a-checkbox>
                    </a-col>
                  </a-row>
                </a-col>
              </a-row>
              <a-row class="marginTop">
                <a-collapse expand-icon-position="right">
                  <a-collapse-panel key="1" header="拼版条件">
                    <a-row>
                      <a-col :span="12">
                        <a-form-model-item label="产品属性" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                          <a-select v-model="form.cpSx" :getPopupContainer="() => this.$refs.SelectBox">
                            <a-select-option value="通孔"> 通孔 </a-select-option>
                            <a-select-option value="HDI"> HDI </a-select-option>
                            <a-select-option value="双面板"> 双面板 </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-model-item label="Core数" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                          <a-select v-model="form.coreSum" :getPopupContainer="() => this.$refs.SelectBox">
                            <a-select-option value="0"> 0 </a-select-option>
                            <a-select-option value="1"> 1 </a-select-option>
                            <a-select-option value="2"> 2 </a-select-option>
                            <a-select-option value="3"> 3 </a-select-option>
                            <a-select-option value="4"> 4 </a-select-option>
                            <a-select-option value="5"> 5 </a-select-option>
                            <a-select-option value="6"> 6 </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="12">
                        <a-form-model-item label="压合次数" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                          <a-select v-model="form.yhSu" :getPopupContainer="() => this.$refs.SelectBox">
                            <a-select-option value="一次"> 一次 </a-select-option>
                            <a-select-option value="二次"> 二次 </a-select-option>
                            <a-select-option value="三次"> 三次 </a-select-option>
                            <a-select-option value="四次"> 四次 </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-model-item label="pp叠数" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                          <a-select v-model="form.ppSum" :getPopupContainer="() => this.$refs.SelectBox">
                            <a-select-option value="0"> 0 </a-select-option>
                            <a-select-option value="1"> 1 </a-select-option>
                            <a-select-option value="2"> 2 </a-select-option>
                            <a-select-option value="3"> 3 </a-select-option>
                            <a-select-option value="4"> 4 </a-select-option>
                            <a-select-option value="5"> 5 </a-select-option>
                            <a-select-option value="6"> 6 </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="12">
                        <a-form-model-item
                          label="线宽距<2.8mil"
                          :label-col="{ span: 17 }"
                          :wrapper-col="{ span: 7 }"
                          :colon="false"
                          labelAlign="left"
                        >
                          <a-checkbox v-model="form.minLinWidth" />
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-model-item label="半孔板" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                          <a-checkbox v-model="form.bhBoard" />
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="12">
                        <a-form-model-item label="芯板<=0.08mm" :label-col="{ span: 17 }" :wrapper-col="{ span: 7 }" :colon="false" labelAlign="left">
                          <a-checkbox v-model="form.xbMin" />
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-model-item label="二阶对接" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                          <a-checkbox v-model="form.twoJdJ" />
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="12">
                        <a-form-model-item label="内层走湿膜" :label-col="{ span: 17 }" :wrapper-col="{ span: 7 }" :colon="false" labelAlign="left">
                          <a-checkbox v-model="form.inDetM" />
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-model-item label="留边(mm)" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }" :colon="false" labelAlign="left">
                          <a-input v-model="form.reservedEdgeSize" :disabled="form.inDetM ? false : true" />
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                    <a-row class="lastRow">
                      <a-col :span="12">
                        <a-form-model-item label="小板开料" :label-col="{ span: 17 }" :wrapper-col="{ span: 7 }" :colon="false" labelAlign="left">
                          <a-checkbox v-model="form.bigSmallKL" />
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-model-item :label-col="{ span: 24 }" :colon="false" labelAlign="left"> </a-form-model-item>
                      </a-col>
                    </a-row>
                  </a-collapse-panel>
                </a-collapse>
              </a-row>
              <a-row class="marginTop">
                <a-col :span="12">
                  <a-form-model-item label="竖排版" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-select v-model="form.verSetDirec" default-value="1" :getPopupContainer="() => this.$refs.SelectBox">
                      <a-select-option value="0"> 无 </a-select-option>
                      <a-select-option value="1"> ↑ </a-select-option>
                      <a-select-option value="2"> → </a-select-option>
                      <a-select-option value="3"> ↓ </a-select-option>
                      <a-select-option value="4"> ← </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-checkbox v-model="form.jszDirection"> 隔行倒扣 </a-checkbox>
                  <!--              <a-form-model-item label="隔离倒扣" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">-->
                  <!--                <a-input v-model="form.jszDirection" />-->
                  <!--              </a-form-model-item>-->
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="12">
                  <a-form-model-item label="横排版" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-select default-value="1" v-model="form.horSetDirec" :getPopupContainer="() => this.$refs.SelectBox">
                      <a-select-option value="0"> 无 </a-select-option>
                      <a-select-option value="1"> ↑ </a-select-option>
                      <a-select-option value="2"> → </a-select-option>
                      <a-select-option value="3"> ↓ </a-select-option>
                      <a-select-option value="4"> ← </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="方向" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-select default-value="1" v-model="form.direction" :getPopupContainer="() => this.$refs.SelectBox">
                      <a-select-option value="0"> 无 </a-select-option>
                      <a-select-option value="1"> 上下 </a-select-option>
                      <a-select-option value="2"> 左右 </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-checkbox v-model="form.isShowSetID"> 显示拼板图内SET序号 </a-checkbox>
                </a-col>
              </a-row>
            </a-form-model>
          </a-tab-pane>
          <a-tab-pane key="2" tab="拼板信息" class="box">
            <a-form-model :model="handForm">
              <a-row>
                <a-col :span="16">
                  <a-form-model-item label="Panel尺寸" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-input v-model="handForm.panelLen" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="X"
                    :label-col="{ span: 10 }"
                    :wrapper-col="{ span: 14 }"
                    :colon="false"
                    labelAlign="left"
                    class="textCenter"
                  >
                    <a-input v-model="handForm.panelWidth" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="16">
                  <a-form-model-item label="板边距离" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-form-model-item
                      label="上下"
                      :label-col="{ span: 10 }"
                      :wrapper-col="{ span: 14 }"
                      :colon="false"
                      labelAlign="left"
                      class="border_box"
                    >
                      <a-input disabled v-model="handForm.top_bottomD" />
                    </a-form-model-item>
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item label="左右" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :colon="false" labelAlign="left">
                    <a-input disabled v-model="handForm.left_rightD" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="24">
                  <a-form-model-item label="拼版数量" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :colon="false" labelAlign="left">
                    <a-input suffix="Set/Panel" disabled v-model="handForm.pbSmallRecNum" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="24">
                  <a-form-model-item label="拼版利用率" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :colon="false" labelAlign="left">
                    <a-input suffix="%" disabled v-model="handForm.useRate1" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="16">
                  <a-form-model-item label="板材尺寸" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }" :colon="false" labelAlign="left">
                    <a-input v-model="handForm.sheetLen" @change="sheetLenChange" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="X"
                    :label-col="{ span: 10 }"
                    :wrapper-col="{ span: 14 }"
                    :colon="false"
                    labelAlign="left"
                    class="textCenter"
                  >
                    <a-input v-model="handForm.sheetWidth" @change="sheetLenChange" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="24">
                  <a-form-model-item label="开料数量" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :colon="false" labelAlign="left">
                    <a-input suffix="Panel/Sheet" disabled v-model="handForm.smallRecNum" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="24">
                  <a-form-model-item label="板材利用率" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :colon="false" labelAlign="left">
                    <a-input suffix="%" disabled v-model="handForm.useRate2" />
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-row>
                <a-col :span="24">
                  <a-form-model-item label="Set总数量" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :colon="false" labelAlign="left">
                    <a-input suffix="Set/Sheet" disabled v-model="handForm.count" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-form-model-item label="成品率" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :colon="false" labelAlign="left">
                    <a-input suffix="%" disabled v-model="handForm.useRate3" />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </a-tab-pane>
          <!-- <a-tab-pane key="3" tab="套板设定" class="box">
        <a-form-model :model="form">
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="套板设定" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
               <a-checkbox style="border-right: 0" v-model="form.isTaoBan" @change="TaoBanChange">
                  </a-checkbox>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="单元长度" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.unitLen"/>
              </a-form-model-item>
            </a-col>
          </a-row>
            <a-row>
              <a-col :span="24">
              <a-form-model-item label="单元宽度"  :label-col="{ span: 12 }" :wrapper-col="{ span: 12}" :colon="false" labelAlign="left">
                <a-input  v-model="form.unitWidth"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="套板最大长度" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.unitLenMax"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="套板最小长度" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.unitLenMin"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="套板最大宽度" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.unitWidthMax"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="套板最小宽度" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.unitWidthMin"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="单元宽度镶入尺寸" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="单元长度镶入尺寸" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input />
              </a-form-model-item>
            </a-col>
          </a-row> 
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="x间距" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input   v-model="form.setSpaceX"/>
              </a-form-model-item>
            </a-col>
              <a-col :span="12">
              <a-form-model-item label="y间距"  :label-col="{ span: 12 }" :wrapper-col="{ span: 12}" :colon="false" labelAlign="left">
                <a-input  v-model="form.setSpaceY"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="宽度工艺边1" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.sideTop"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="宽度工艺边2" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.sideBot"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="长度工艺边1" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.sideLeft"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="长度工艺边2" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <a-input v-model="form.sideRight" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="规定单元数" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
                <div style="display:flex">
                  <a-checkbox style="border-right: 0">
                  </a-checkbox>
                  <a-input style="width: 110px;height: 20px;border: 1px solid #dddddd;margin-top: 3px;margin-right: 5px"></a-input>  
                </div>
               </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="单元为间隔镶入" :label-col="{ span: 12 }" :wrapper-col="{ span:12}" :colon="false" labelAlign="left">
               <a-checkbox style="border-right: 0">
                  </a-checkbox>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-tab-pane> -->
        </a-tabs>
        <a-modal title=" 订单详情确认" :visible="dataVisible" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="400" centered>
          <template slot="footer">
            <a-button type="primary" @click="reportHandleCancel">取消</a-button>
          </template>
          <div style="height: 200px; overflow-y: auto">
            <div v-for="(ite, index) in message" :key="index">
              <p>{{ ite }}</p>
              <p style="color: red">请核实重新填写</p>
            </div>
          </div>
        </a-modal>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { dataOutput, dataInfoOutput } from "@/services/cutting";
export default {
  name: "CuttingBasic",
  props: {
    imgData: {},
    materialSettingData: {
      type: Array,
    },
  },
  data() {
    return {
      copyform: {},
      form1: {
        job: "2", // 产品编号        *****
        layCount: 2, // 层数           !!!!!!!!!
        boardThink: 1.6, // 板厚           !!!!!!!!!
        pcssetName: "PCS", // 出货单位
        pcsset: 1, // PCS/Set
        numUDSix: 0, // 阻抗组数        !!!!!!!!
        coupDW: 8,
        coupDL: 160,
        cpLen: 100, // 成品长          !!!!!!!!!
        cpWidth: 80, // 成品宽          !!!!!!!!!
        spaceD: 2, // 拼板间距 X方向   !!!!!!!!!
        spaceD_Y: 2, // 拼板间距 Y方向    !!!!!!!!
        left: 8, // 左边距           !!!!!!!!
        right: 8, // 右边距           !!!!!!!!
        top: 8, // 上边距           !!!!!!!!
        bot: 8, // 下边距           !!!!!!!!
        minPanelLen: 300, // 最小Pnl长        !!!!!!!!
        maxPanelLen: 660, // 最大Pnl长        !!!!!!!!
        minPanelWidth: 300, // 最小Pnl宽        !!!!!!!!
        maxPanelWidth: 550, // 最大Pnl宽        !!!!!!!!
        maxBs: 1.7, // Panel最大长宽比例  !!!!!!!
        minW: 15, // Panal最小长宽差值  ****
        minPnlRate: 70, // 最低拼版利用率      !!!!!!
        latitudinalSize: 622, // 纬向尺寸
        gdkl: false, // 固定尺寸           !!!!!!
        allowDifDirection: true, // 允许异向拼板        !!!!!!
        anywayKL: true, // 横竖大料            !!!!!!
        isVcut: false, // 大板V-Cut          !!!!!!
        isABBoard: false, // 是否AB开料       !!!!!!
        conventionalJumpVCutSize: 10, // 跳V(mm)         !!!!!!
        // "cpSx": "通孔",                   // 产品属性 通孔;HDI;双面板
        // 'coreSum': 0,                    // core数
        // "yhSu": "一次",                   // 压合次数 一次;二次;三次;四次
        // 'ppSu':0,                        // PP叠数
        // "minLinWidth": false,             // 线宽距小于2.8mil
        // "bhBoard": false,                 // 半孔板
        // "xbMin": false,                   // 芯板小于等于0.08mm
        // "twoJdJ": false,                  // 二阶对接
        // "inDetM": false,                  // 内层走湿膜         !!!!!!
        reservedEdgeSize: 10, // 留边
        verSetDirec: "1", // 竖排版
        jszDirection: false, // 隔排倒扣
        horSetDirec: "2", // 横排版
        direction: "1", // 方向:0无;1上下;2左右
        isShowSetID: false,

        // "minUserRate": 0,                // 最低板材利用率
        isAutoCut: true, // 是否自动开料         !!!!!!!
        isTwin: true, // 是否双幅料           !!!!!!
        // "numUDThree": 0,                 //
        // "coupDW": 0,                     // 阻抗宽度 双线
        // "coupDL": 0,                     // 阻抗长度
        // "isShowSetID": true,             // 显示拼板图内SET序号
        // "job": "string",                 // 型号
        // "imageWidth": 0,                 // 图片宽度(像素单位)
        // "imageHeight": 0,                // 图片高度(像素单位)
        // "facId": 0,                      // 工厂Id
        // "bigBorad": true,                // 是否大板开料
        // "orderType": "string"            // 板材型号
        // "sheetWidth": 200 ,                //大料宽
        // "panelLen":660,                    //Panel长
        // "panelWidth":450,                  //Panel宽
      },
      form: {},
      handForm: {
        sheetLen: 0,
        sheetWidth: 0,
        panelLen: 0,
        panelWidth: 0,
      },
      data: {},
      form3: {
        isTaoBan: false,
        unitLen: "",
        unitWidth: "",
        unitLenMax: "200",
        unitLenMin: "70",
        unitWidthMax: "200",
        unitWidthMin: "70",
        setSpaceX: "0",
        setSpaceY: "0",
        sideTop: "5",
        sideBot: "5",
        sideLeft: "0",
        sideRight: "0",
        cpSx: "通孔",
        yhSu: "一次",
        coreSum: 0,
        ppSum: 0,
        minLinWidth: false,
        bhBoard: false,
        xbMin: false,
        twoJdJ: false,
        inDetM: false,
        reservedEdgeSize: 8,
        bigSmallKL: false,
      },
      dataVisible: false,
      message: [],
      spinning: false,
    };
  },
  watch: {
    imgData: {
      handler(val) {
        if (val.ur) {
          this.handForm["left_rightD"] = val.ur.pbDataInfo.left_rightD; // PbDataInfo.Left_rightD  左右
          this.handForm["top_bottomD"] = val.ur.pbDataInfo.top_bottomD; // PbDataInfo.top_bottomD 上下
          this.handForm["pbSmallRecNum"] = val.ur.pbDataInfo.smallRecNum; // PbDataInfo.SmallRecNum 拼版数量
          this.handForm["useRate1"] = (val.ur.pbDataInfo.useRate * 100).toFixed(2); // PbDataInfo.UseRate 拼版利用率
          this.handForm["smallRecNum"] = val.ur.klDataInfo.smallRecNum; // KlDataInfo.SmallRecNum 开料数量
          this.handForm["useRate2"] = (val.ur.klDataInfo.useRate * 100).toFixed(2); // KlDataInfo.UseRate 板材利用率
          this.handForm["count"] = val.ur.pbDataInfo.smallRecNum * val.ur.klDataInfo.smallRecNum; // PbDataInfo.SmallRecNum*KlDataInfo.SmallRecNum  Set总数
          this.handForm["useRate3"] = (val.ur.klDataInfo.useRate * val.ur.pbDataInfo.useRate * 100).toFixed(2); // PbDataInfo.UseRate*KlDataInfo.UseRate 成品率
          this.$forceUpdate();
        } else {
          //this.handForm={}
        }
      },
      immediate: true,
    },
  },
  created() {
    if (this.form.pcsset > 1) {
      this.form.pcssetName = "Set";
    }
    // 1: 取路由参数    console.log(this.$route.query)
    // 2: 判断是有参数，有的话去赋值  for ( let i in obj) {
    //     return true;
    // }
    // return false
    // 3: 赋值 this.$store.commit('changeInfo', {参数值})
    var routeList = this.$route.query;
    if (JSON.stringify(routeList) !== "{}") {
      this.$nextTick(() => {
        this.spinning = true;
        dataOutput(this.$route.query.joinFactoryId, routeList.job).then(res => {
          if (res.code) {
            this.form = res.data.panelMakeInput;
            this.copyform = JSON.parse(JSON.stringify(res.data.panelMakeInput));
            //2024/5/9 取消开料间距赋值
            // if(!this.form.klSpaceD && this.$route.query.joinFactoryId!=12){
            //   this.form.klSpaceD='4'
            // }
            this.form.taoBanType = this.form.taoBanType ? this.form.taoBanType : "0";
            this.form.verSetDirec = this.form.verSetDirec.toString();
            this.form.horSetDirec = this.form.horSetDirec.toString();
            this.form.direction = this.form.direction.toString();
            this.data = res.data.modeSet.uResult.klDataInfo;
            this.handForm.panelLen = res.data.modeSet.uResult.pbDataInfo.bigRecDrawLen;
            this.handForm.panelWidth = res.data.modeSet.uResult.pbDataInfo.bigRecDrawWidth;
            this.form.boardThink = routeList.boardThink;
            this.form.cpLen = routeList.cpLen;
            this.form.cpWidth = routeList.cpWidth;
            this.form.job = routeList.job;
            this.form.layCount = routeList.layCount;
            this.form.pcsset = Number(routeList.pcsset);
            this.form.pcssetName = routeList.pcssetName;
            this.$emit("getOld", res.data.modeSet, res.data.tag);
            this.spinning = false;
          } else {
            let params = {
              layer: Number(routeList.layCount),
              cbCpSx: "",
              cbYhSu: "",
              OrderNo: routeList.job,
              joinFactoryId: routeList.joinFactoryId,
            };
            dataInfoOutput(params).then(res => {
              if (res.code) {
                this.form = res.data;
                this.copyform = JSON.parse(JSON.stringify(res.data));
                // if(!this.form.klSpaceD && this.$route.query.joinFactoryId!=12){
                // this.form.klSpaceD='4'
                // }
                this.form.taoBanType = this.form.taoBanType ? this.form.taoBanType : "0";
                this.form.boardThink = routeList.boardThink;
                this.form.cpLen = routeList.cpLen;
                this.form.cpWidth = routeList.cpWidth;
                this.changeCpLen();
                this.changeCpWidth();
                this.form.job = routeList.job;
                this.form.layCount = routeList.layCount;
                this.form.pcsset = Number(routeList.pcsset);
                this.form.pcssetName = routeList.pcssetName;
                this.form.verSetDirec = this.form.verSetDirec.toString();
                this.form.horSetDirec = this.form.horSetDirec.toString();
                this.form.direction = this.form.direction.toString();
                this.form.numUDSix = Math.ceil(routeList.numUDSix);
                this.form.coupDW = routeList.txtCoupDW;
                this.form.coupDL = routeList.txtCoupDL;
                this.$emit("autoClick");
                this.$emit("abcut", this.form.isABBoard);
                this.$emit("TaoBanChange", this.form.isTaoBan);
                this.spinning = false;
              } else {
                this.spinning = false;
              }
            });
          }
        });
      });
    } else {
      let params = {
        layer: 2,
        cbCpSx: "",
        cbYhSu: "",
        OrderNo: "",
      };
      dataInfoOutput(params).then(res => {
        if (res.code) {
          this.form = res.data;
          this.copyform = JSON.parse(JSON.stringify(res.data));
          this.changeCpLen();
          this.changeCpWidth();
          this.form.taoBanType = this.form.taoBanType ? this.form.taoBanType : "0";
          this.$emit("abcut", this.form.isABBoard);
          this.$emit("TaoBanChange", this.form.isTaoBan);
          this.form.verSetDirec = this.form.verSetDirec.toString();
          this.form.horSetDirec = this.form.horSetDirec.toString();
          this.form.direction = this.form.direction.toString();
        } else {
          this.spinning = false;
        }
      });
    }
    var a = this.materialSettingData.filter(v => v.isCalc_ == true);
    if (a.length == 1) {
      this.handForm.sheetLen = a[0].sheetLen;
      this.handForm.sheetWidth = a[0].sheetWidth;
    } else if (this.data != JSON.stringify("{}")) {
      this.handForm.sheetLen = this.data.bigRecDrawLen;
      this.handForm.sheetWidth = this.data.bigRecDrawWidth;
    } else {
      this.handForm.sheetLen = "";
      this.handForm.sheetWidth = "";
    }
  },
  methods: {
    changeCpLen() {
      if (Number(this.form.cpLen) <= 20) {
        this.$set(this.form, "minPanelLen", 250);
        this.$set(this.form, "maxPanelLen", 300);
      } else {
        this.$set(this.form, "minPanelLen", this.copyform.minPanelLen);
        this.$set(this.form, "maxPanelLen", this.copyform.maxPanelLen);
      }
    },
    changeCpWidth() {
      if (Number(this.form.cpWidth) <= 20) {
        this.$set(this.form, "minPanelWidth", 200);
        this.$set(this.form, "maxPanelWidth", 300);
      } else {
        this.$set(this.form, "minPanelWidth", this.copyform.minPanelWidth);
        this.$set(this.form, "maxPanelWidth", this.copyform.maxPanelWidth);
      }
    },
    changechdw() {
      if (this.form.pcsset > 1) {
        this.form.pcssetName = "Set";
      }
    },
    numUDSixChange() {
      // if(this.form.numUDSix==0){
      //   this.form.coupDW=0;
      //   this.form.coupDL=0;
      // }
    },
    gdchange() {
      if (this.form.gdkl) {
        this.$set(this.form, "anywayKL", false);
        this.$set(this.form, "klSpaceD", 0);
      }
    },

    layCount(val) {
      // this.form.left = val + 1 层数0<=layer<4 最小边距8 层数4<=layer<6 最小边距10 >=6 15
      // if( val >= 0 && val < 4){
      //   console.log('0.4',0<= val <4)
      //   this.form.left = 8
      //   this.form.right = 8
      //   this.form.top = 8
      //   this.form.bot = 8
      // }
      // if(val>=  4 && val < 6){
      //   console.log('4.6',4 <= val < 6)
      //   this.form.left = 10
      //   this.form.right = 10
      //   this.form.top = 10
      //   this.form.bot = 10
      // }
      // if(val >= 6){
      //   console.log('6.10',val >= 6)
      //   this.form.left = 15
      //   this.form.right = 15
      //   this.form.top = 15
      //   this.form.bot = 15
      // }
      let params = {
        layer: this.form.layCount,
        cbCpSx: "",
        cbYhSu: "",
        OrderNo: this.$route.query.job ? this.$route.query.job : "",
        joinFactoryId: this.$route.query.joinFactoryId ? this.$route.query.joinFactoryId : "",
      };
      var a = this.materialSettingData.filter(v => v.isCalc_ == true);
      if (a.length == 1) {
        this.handForm.sheetLen = a[0].sheetLen;
        this.handForm.sheetWidth = a[0].sheetWidth;
      } else if (this.data != JSON.stringify("{}")) {
        this.handForm.sheetLen = this.data.bigRecDrawLen;
        this.handForm.sheetWidth = this.data.bigRecDrawWidth;
      } else {
        this.handForm.sheetLen = "";
        this.handForm.sheetWidth = "";
      }
      dataInfoOutput(params).then(res => {
        if (res.code) {
          this.form.left = res.data.left;
          this.form.right = res.data.right;
          this.form.top = res.data.top;
          this.form.bot = res.data.bot;
        }
      });
    },
    callback(key) {
      var a = this.materialSettingData.filter(v => v.isCalc_ == true);
      if (a.length == 1) {
        this.handForm.sheetLen = a[0].sheetLen.toString();
        this.handForm.sheetWidth = a[0].sheetWidth.toString();
      } else if (this.data != JSON.stringify("{}") && (this.data.bigRecDrawLen || this.data.bigRecDrawWidth)) {
        this.handForm.sheetLen = this.data.bigRecDrawLen;
        this.handForm.sheetWidth = this.data.bigRecDrawWidth;
      }
      this.$forceUpdate();
    },
    sheetLenChange() {
      this.$forceUpdate();
    },
    onSubmit() {
      console.log("submit!", this.form);
    },
    TaoBanChange() {
      this.$emit("TaoBanChange", this.form.isTaoBan);
      this.sheathingplate();
    },
    abcut() {
      this.$emit("abcut", this.form.isABBoard);
    },
    sheathingplate() {
      if (this.form.isTaoBan == true) {
        if (this.$route.query.unitLen && this.$route.query.unitWidth) {
          this.form.unitLen = this.$route.query.unitLen;
          this.form.unitWidth = this.$route.query.unitWidth;
        } else {
          this.form.unitLen = this.form.cpLen;
          this.form.unitWidth = this.form.cpWidth;
        }
      }
    },
    measurement() {
      this.message = [];
      if (this.form.cpLen && this.form.cpWidth && Number(this.form.cpLen) < Number(this.form.cpWidth)) {
        this.form.cpLen = "";
        this.form.cpWidth = "";
        this.message.push("成品尺寸长必须大于等于成品尺寸宽");
      }
      if (this.form.unitLen && this.form.unitWidth && Number(this.form.unitLen) < Number(this.form.unitWidth)) {
        this.form.unitLen = "";
        this.form.unitWidth = "";
        this.message.push("套板单元长度必须大于等于单元宽度");
      }
      if (this.message.length) {
        this.dataVisible = true;
      }
    },
    reportHandleCancel() {
      this.dataVisible = false;
    },
  },
};
</script>

<style scoped lang="less">
.inputclass {
  /deep/.ant-input {
    border: 0;
    padding: 0 !important;
    padding-left: 4px !important;
  }
}
/deep/.ant-select-dropdown-menu-item {
  font-weight: 500;
}
/deep/.ant-select-selection--single .ant-select-selection__rendered {
  margin-right: 5px;
  margin-left: 5px;
}
/deep/.ant-select-arrow {
  right: 4px;
}
/deep/.ant-collapse-icon-position-right > .ant-collapse-item > .ant-collapse-header {
  padding: 2px 16px;
  border-radius: 0;
  border-right: 1px solid #d7d7d7;
}
/deep/.ant-collapse-content > .ant-collapse-content-box {
  padding: 0;
}
/deep/.ant-collapse-item:last-child > .ant-collapse-content {
  border-radius: 0;
}
/deep/.ant-collapse-content-box {
  .ant-checkbox-wrapper {
    border-right: 0 !important;
  }
}
/deep/.ant-collapse {
  border-radius: 0;
  border: 0;
}
.lastRow {
  /deep/.ant-form-item-label {
    border-bottom: 0 !important;
  }
  /deep/.ant-form-item-control {
    border-bottom: 0 !important;
  }
  /deep/.ant-checkbox-wrapper {
    border-bottom: 0 !important;
  }
}
.tableInfo {
  /deep/.ant-tabs-tab {
    margin-right: 0 !important;
  }
  /deep/ .ant-tabs-bar {
    margin: 0;
  }
  /deep/ .ant-tabs-content {
    padding: 4px;
    .ant-form {
      border-left: 1px solid rgba(215, 215, 215, 1);
      border-top: 1px solid rgba(215, 215, 215, 1);
      // outline: 1px solid rgba(215, 215, 215, 1);
    }
    .ant-form-item {
      margin: 0;
      .ant-form-item-label {
        background: #f6f6f6;
        border-bottom: 1px solid rgba(215, 215, 215, 1);
        border-right: 1px solid rgba(215, 215, 215, 1);
        // outline: 1px solid rgba(215, 215, 215, 1);
        height: 26px;
        line-height: 26px;
        .ant-form-item-no-colon {
          padding-left: 8px;
        }
      }
      .ant-form-item-control {
        // outline: 1px solid rgba(215, 215, 215, 1);
        border-bottom: 1px solid rgba(215, 215, 215, 1);
        border-right: 1px solid rgba(215, 215, 215, 1);
        line-height: 26px;
        height: 26px;
        input {
          border: 0;
        }
      }
    }
    .borderClass {
      .ant-form-item-control {
        border-right: 1px solid #d7d7d7 !important;
        // outline: 1px solid rgba(215, 215, 215, 1);
      }
    }
    .minPnlRate {
      /deep/.ant-input {
        height: 24px;
      }
    }
    .labClass {
      .ant-form-item-label {
        width: calc(300% / 7);
      }
      .ant-form-item-control-wrapper {
        width: calc(400% / 7);
      }
      .borderClass {
        .ant-form-item-label {
          width: 36%;
        }
        .ant-form-item-control-wrapper {
          width: 64%;
        }
      }
    }
    .labGroup {
      .ant-form-item-label {
        line-height: 52px;
        height: 52px;
      }
      .ant-form-item-control-wrapper {
        .ant-form-item-label {
          line-height: 26px;
          height: 26px;
        }
        .ant-form-item-control {
          border-right: 0;
        }
      }
    }
    .marginTop {
      margin-top: 4px;
      border-top: 1px solid #d7d7d7;
      .ant-collapse > .ant-collapse-item:last-child {
        border-radius: 0;
      }
    }
    .marginBot {
      margin-bottom: 4px;
    }
    .ant-select-selection {
      border: 0;
      height: 24px;
    }
    .ant-input-number-input {
      height: 26px;
    }
    .ant-checkbox-wrapper {
      width: 100%;
      height: 26px;
      line-height: 26px;
      border-bottom: 1px solid rgba(215, 215, 215, 1);
      border-right: 1px solid rgba(215, 215, 215, 1);
      .ant-checkbox {
        margin-left: 7px;
        margin-right: 6px;
      }
      span {
        padding: 0;
      }
    }
    .ant-input {
      height: 24px;
    }
    .ant-input-number {
      width: 100%;
      border: 0;
      height: 24px;
    }
    .border_box {
      border-right: 0;
    }
    .textbox {
      /deep/.ant-form-item-label {
        text-align: center;
      }
    }
  }
  .box {
    .textCenter {
      /deep/ label {
        padding-left: 15px !important;
      }
    }
    .border_box {
      /deep/.ant-form-item-control {
        border-right: 0;
      }
    }
  }
}
</style>
