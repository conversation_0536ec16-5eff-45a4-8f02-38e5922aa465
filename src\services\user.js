import {LOGIN, ROUTES,ApplicationConfiguration} from '@/services/api'
import {request, METHOD, removeAuthorization} from '@/utils/request'
import qs from 'querystring'
/**
 * 登录服务
 * @param name 账户名
 * @param password 账户密码
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function login(username,password,smsCode,tenant) {
  const params = {
    username: username,
    password: password,
    client_id: "MES_App",
    smsCode:smsCode,
    // client_secret: "1q2w3e*",
    grant_type: "password",
    scope:"MES"
  };
  let config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  }
  if(tenant){
    config.headers.__tenant=tenant;
  } 
  return request(LOGIN, METHOD.POST, qs.stringify(params), config)
}
export async function loginWX(code) {
  const params = {
    code: code,
    client_id: "MES_App",
    grant_type: "openid",
    scope:"MES"
  };
  let config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  }
  return request(LOGIN, METHOD.POST, qs.stringify(params), config)
}
export async function phoneLogin(params) {
  const param = {
    phoneNumber: params.phoneNumber,
    client_id: "MES_App",
    smsCode:params.smsCode,
    grant_type: "sms",
    scope:"MES"
  };
  let config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  }
  return request(LOGIN, METHOD.POST, qs.stringify(param), config)
}


export async function loginOA(username,password,tenant,smsCode) {
  const params = {
    oa_iv: username,
    oa_token: password,
    client_id: "MES_App",
    // client_secret: "1q2w3e*",
    grant_type: "oa_verify",
    scope:"MES"
  };
  let config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  }
  if(tenant){
    config.headers.__tenant=tenant;
  }
  return request(LOGIN, METHOD.POST, qs.stringify(params), config)
}


export async function getRoutesConfig() {
  return request(ROUTES, METHOD.GET)
}
export async function verificationName(params){
  return request("/api/app/e-mSUser-data-info/is-send-sms?userName="+params, METHOD.GET)
}
export async function verificationName1(params,password,ipcbversion,macaddress){
  return request(`api/app/e-mSPermission/permissions-for-iCAM?username=${params}&password=${password}&ipcbversion=${ipcbversion}&macaddress=${macaddress}`, METHOD.GET)
}

export async function getVerification(params){
  return request("/api/app/sms/send-verification-code-by-user-name", METHOD.POST,params)
}
// 验证码注册
export async function getRegisterCode(params){
  return request("/api/app/sms/send-verification-code", METHOD.POST,params)
}
//PE发送验证码
export async function wechatcode(params){
  return request("api/app/we-chat/send-verification-code", METHOD.POST,params)
}
// 验证码登录PE
export async function customerbindingopenid(params){
  return request("/api/app/we-chat/customer-binding-open-id", METHOD.POST,params)
}
//登陆成功后获取openid
export async function openidcode(params){
  return request("api/app/we-chat/openid-code", METHOD.GET,params)
}
// PL pe端列表
export async function wiporderlist(params){
  return request("/api/app/we-chat/wip-order-list", METHOD.GET,params)
}
// 注册
export async function userRegister(params){
  return request("/api/app/single-sign-on/user-registration", METHOD.POST,params)
}
// 微信登录授权
export async function setopenid(code){
  return request(`/api/app/we-chat/set-open-id?code=${code}`, METHOD.POST,)
}




/**
 * 退出登录
 */
export function logout() {
  localStorage.removeItem(process.env.VUE_APP_ROUTES_KEY)
  localStorage.removeItem(process.env.VUE_APP_PERMISSIONS_KEY)
  localStorage.removeItem(process.env.VUE_APP_ROLES_KEY)
  removeAuthorization()
}
export async function applicationConfiguration() {
  return request(ApplicationConfiguration, METHOD.GET)
}

export async function getHomePage(){
  return request('/api/app/single-sign-on/home-page', METHOD.GET)

}
export async function pcLoginAcode(params){
  return request(`/api/wechat-management/mini-programs/login/pc-login-acode`, METHOD.GET,params)
}
export async function pcLogin(params){
  return request(`/api/wechat-management/mini-programs/login/pc-code-login`, METHOD.POST,params)
}
export async function getErp(params){
  return request(`/api/app/e-mSUser/erp-login-url-by-account`, METHOD.GET,params)
}

export async function skipLogin(params,url){
  return request(url, METHOD.POST,params)
}


export default {
  login,
  loginWX,
  logout,
  getRoutesConfig,
  applicationConfiguration,
  pcLoginAcode,
  pcLogin,
  verificationName,
  getVerification,
  getErp,
  skipLogin,
  verificationName1,
}
