<!-- 系统管理-Spec更新 -->
<template>
  <a-card>
    <div>
      <div class="operator">
        <a-button  @click="addClick" type="primary"
          >新增</a-button
        >
<!--        <a-button  @click="downClick" type="primary"-->
<!--          >下载</a-button-->
<!--        >-->
      </div>
      <div class="content">
      <standard-table
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :selectedRows.sync="selectedRows"
        @change="handleTableChange"
        :pagination="pagination"
        :loading="loading"
        class="mainstyle"
      >
        <span slot="roleName" slot-scope="{ text, record }">
          {{ text }}
          <a-tag color="red" v-if="record.isStatic">系统</a-tag>
          <a-tag color="blue" v-if="record.isDefault">默认</a-tag>
          <a-tag color="blue" v-if="record.isPublic">公开</a-tag>
        </span>
        <template slot="num" slot-scope="{ index }">
        {{(pagination.current-1)*pagination.pageSize+parseInt(index)+1}}
       </template>
        <div slot="action" slot-scope="{ record }">
          <template>
                  <a  href="javascript:;" @click="editClick(record)" >编辑 
                  <div data-v-50499b08 role="separator" class="ant-divider ant-divider-vertical"></div></a>
                  <a-popconfirm :title="ddeleData"
                   @confirm="handleDel(record.id)"   >
                    <a href="javascript:;">删除</a>
                  </a-popconfirm>
                  <div data-v-50499b08 role="separator" class="ant-divider ant-divider-vertical"></div> 
                  <a href="javascript:;"   @click="renewClick(record)">更新 </a> 
                  
            <!-- <a-dropdown>
              <a class="ant-dropdown-link" href="#">
                操作
                <a-icon type="down" />
              </a>
              <a-menu slot="overlay">             
                <a-menu-item >
                  <a
                    href="javascript:;"
                    @click="editClick(record)"
                    >编辑</a
                  >
                </a-menu-item>
                <a-menu-item >
                  <a-popconfirm
                    title="确定要删除吗？"
                    @confirm="handleDel(record.id)"
                  >
                    <a href="javascript:;">删除</a>
                  </a-popconfirm>
                </a-menu-item>
                <a-menu-item >
                  <a href="javascript:;" @click="renewClick(record)">更新</a>
                </a-menu-item> 
              </a-menu>
            </a-dropdown> -->
          </template>
        </div>
      </standard-table>
      </div>
      <div class="bto"></div>
    </div>
    <a-modal v-model="rightVisible"  :mask="false"  :maskClosable="false"   @cancel="handleCancel1" centered  title='客户添加' @ok='handleRight'>
            <a-form-model
                    ref="ruleForm"
                    :model="form"
                    :rules="rules"
                    :label-col="labelCol"
                    :wrapper-col="wrapperCol"
            > 
            <a-col>

            </a-col>
                <a-form-model-item label="工厂" ref="factory" prop="factory"  :label-col="{ span:6 }" :wrapper-col="{ span: 15 }" >
                    <a-input v-model="form.factory" allowClear/>
                </a-form-model-item>
                <a-form-model-item label="连接字符串" ref="connectionString" prop="connectionString"  :label-col="{ span:6 }" :wrapper-col="{ span: 15 }" >
                    <a-textarea :rows="3" v-model="form.connectionString" allowClear/>
                </a-form-model-item>
                
            </a-form-model>
    </a-modal> 
  </a-card>
</template>

<script> 
import StandardTable from "@/components/table/StandardTable";
import { getList,createUpdate ,GetEditInfo,updateEditInfo, del ,updateFactoerSpec} from "@/services/identity/Spec";
import { checkPermission } from '@/utils/abp';
const columns = [
  {
    title: "序号",
    scopedSlots: { customRender: "num" },
    align:'center',
    width:45,
  },
  {
    title: "工厂",
    dataIndex: "factory",
    align:'left',
    ellipsis:'true',
    width:150, 
  },
  {
    title: "连接字符串",
    dataIndex: "connectionString",
   scopedSlots: { customRender: "connectionString" },
    align:'left',
    ellipsis:'true',
    width:1120,
    className:'userStyle',
  },
  {
    title: "最后更新时间",
    dataIndex: "lastUpdateTime",
    align:'left',
    ellipsis:'true',
    width:180,
  },
  {
    title: "操作",
    scopedSlots: { customRender: "action" },
    align:'left',
    ellipsis:'true',
    width:160,
  },
];
let that;
export default {
  name: "SpecList",
  components: { StandardTable, },
  data() {
    return {
      labelCol: { span: 7},
      wrapperCol: { span: 17 },
      advanced: true,
      columns: columns,
      dataSource: [],
      selectedRows: [],     
      pagination: {
          pageSize: 20,
          current: 1,
          total:0,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["20", "50", "100"],//每页中显示的数据
          showTotal: (total) => `总计 ${total} 条`,
      },
      sorter: {
        field: "id",
        order: "desc",
      },
      loading: false,
      queryParam: {},
      categorys: [],
      rightVisible:false,
      rules: {
          factory: [
              { required: true, message: "工厂必须填写", trigger: "blur" },
          ],
          connectionString: [
              { required: true, message: "连接字符串必须填写", trigger: "blur" },
          ]
      },
      form:{       
        factory:'', 
        connectionString:'',  
        lastUpdateTime:'',       
      },
    };
  },
  // authorize: {
  //   deleteRecord: "delete",
  // },
  mounted() {
    that=this;
    window.addEventListener('resize', this.handleResize, true)
    this.loadData();
  },
  beforeDestroy(){
    window.removeEventListener('resize', this.handleResize, true)
   },
  created(){
    this.$nextTick(()=>{
      this.handleResize()
    })
  },
  methods: {
    handleResize(){
      var mainstyle = document.getElementsByClassName('mainstyle')[0].children[0].children[0].children[0].children[0].children[0].children[0].children[1]
      var content = document.getElementsByClassName('content')[0]
      if(window.innerHeight<=911){
        content.style.height = window.innerHeight - 177 +'px'
      }else{
        content.style.height = '741px'
      }  
      if(mainstyle && this.dataSource.length!=0){
        mainstyle.style.height =  window.innerHeight - 215 +'px'
      }else{
        mainstyle.style.height = 0
      }
      var paginnum = ''
        var footerwidth =  window.innerWidth-224
        if(Math.ceil(this.pagination.total/20)>10){
          paginnum = 7
        }else{
          paginnum = Math.ceil(this.pagination.total/20)
        }
        if(((paginnum*50)+310)<footerwidth){
        this.pagination.simple=false
        this.pagination.size = ''
        this.pagination.showSizeChanger = true
        this.pagination.showQuickJumper = true
        }else{
        this.pagination.simple=false
        this.pagination.size = 'small'
        this.pagination.showSizeChanger = false
        this.pagination.showQuickJumper = false
       }
    },
    ddeleData(){
     return <span style="font-weight:500">确认删除吗？</span>
    },
    checkPermission,
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    onSelectChange() {
      this.$message.info("选中行改变了");
    },
    handleDel(id) {
      del(id).then((res) => {
        if(res.code){
          this.$message.success("删除成功");
          this.loadData();
        }else{
          this.$message.error(res.message)
        }
       
      });
    },       
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current=pagination.current
      this.pagination.pageSize=pagination.pageSize
      localStorage.setItem('pageCurrent',this.pagination.current)
      localStorage.setItem('pageSize',pagination.pageSize)
      this.pageStat=false
      localStorage.removeItem('stat')
      this.loadData();
    },
    loadData() {
      this.loading = true;
      let params = {
        ...this.pagination,
        ...this.queryParam,
        sorter: this.sorter,
      };
      getList(params).then((res) => {      
          const pagination = { ...this.pagination };
          pagination.total = res.data.totalCount;
          this.pagination = pagination;
          this.dataSource = res.data.items;
          setTimeout(() => this.handleResize(), 0);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    refresh() {
      this.pagination.current = 1;
      this.loadData();
    },
    handleOk() {
      this.loadData();
    },
    addClick(){
      this.rightVisible = true
      this.form={}
    },
    handleCancel1(){
      this.rightVisible = false
    },
    handleRight(){
      const form = this.$refs.ruleForm;
      form.validate((valid) => {
        if(valid){
          let params = this.form
          if(params.id){
            updateEditInfo(params).then(res=>{
            if(res.code){
              this.$message.success('编辑成功')
              this.loadData()
            }else{
              this.$message.error(res.message)
            }
          })
          }else{
            createUpdate(params).then(res=>{
            if(res.code){
              this.$message.success('新增成功')
              this.loadData()
            }else{
              this.$message.error(res.message)
            }
          })
          }          
          this.rightVisible = false
        }
      })
    },
    editClick(record){
      GetEditInfo(record.id).then(res=>{
        if(res.code){
          this.form.id = res.data.id
          this.form.factory = res.data.factory
          this.form.connectionString = res.data.connectionString
          this.form.lastUpdateTime = res.data.lastUpdateTime         
        }else{
          this.$message.error(res.data)
        }
        this.rightVisible = true
      })
      
    },
    renewClick(record){
      let url = 'UpdateSpec://?' + encodeURIComponent(record.connectionString) + '?' + encodeURIComponent(record.id)
      window.open(url,"_blank")
    },
    downClick(){
      window.location.href = 'http://emsapi.bninfo.com/flupdate/UpdateSpec.zip'
    },
    
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-table-pagination.ant-pagination {   
      margin: 9px 11px;
      float: left;
      position: absolute;
    }
 /deep/.userStyle{
    user-select: none!important;
  }
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/.ant-input{
  font-weight: 500;
}
/deep/.ant-table-thead > tr > th{
  padding: 6px 6px !important;  
  // border-right:1px solid #efefef;   
  border-right:1px solid #efefef; 
}
/deep/.ant-table-tbody > tr > td {
  padding: 6px 6px !important;  
  // border-right:1px solid #efefef;   
  border-right:1px solid #efefef; 
}
/deep/ .ant-card-body{
  padding:0px;
}
/deep/ .ant-form-item{
  padding-bottom:0;
}
.content{
  border:2px solid #f5f5f5;
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #F8F8F8;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
  /deep/.ant-table-tbody > tr.ant-table-row-selected td {    
    background: #dfdcdc;
}

}
.bto{
  height:45px;
  border:2px solid #f5f5f5;
  border-top:4px solid #f5f5f5;
}
.search {
  margin-bottom: 54px;
}
.fold {
  width: calc(100% - 216px);
  display: inline-block;
}
.operator {
  margin: 6px 10px;
}
@media screen and (max-width: 900px) {
  .fold {
    width: 100%;
  }
}
</style>