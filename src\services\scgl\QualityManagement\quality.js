import { request, METHOD } from '@/utils/request'
// 获取责任工序选择项
export async function stepConfigre(PinBanNo,CardNo) {
    return request(`/api/app/pro-scrap-info/step-configre?PinBanNo=${PinBanNo}&CardNo=${CardNo}`, METHOD.GET,)
}
// 获取订单列表
export async function proScrapInfoList(params) {
    return request("/api/app/pro-scrap-info/pro-scrap-info-list", METHOD.GET,params)
}
// 通过流程卡号获取拼版单号和子订单
export async function byCardNo(params) {
    return request(`/api/app/pro-scrap-info/no-by-card-no?CardNo=${params}`, METHOD.GET,)
}
// 创建报废记录
export async function proScrapInfo(params) {
    return request(`/api/app/pro-scrap-info`, METHOD.POST,params)
}
//编辑报废 
export async function getUpdateInfo(Id) {
    return request(`/api/app/pro-scrap-info/pro-scrap-info/${Id}`, METHOD.GET,)
}
//保存编辑报废 
export async function updateInfo(params) {
    return request(`/api/app/pro-scrap-info/update`, METHOD.POST,params)
}
//删除报废
export async function deleteInfo(Id) {
    return request(`/api/app/pro-scrap-info/delete/${Id}`, METHOD.POST,)
}
//缺陷原因
export async function defectConfigre(KeyNo) {
    return request(`/api/app/pro-scrap-info/defect-configre?KeyNo=${KeyNo}`, METHOD.GET,)
}
export default {
    stepConfigre,
    proScrapInfoList, 
    byCardNo, 
    proScrapInfo,
    updateInfo,  
    getUpdateInfo,
    deleteInfo,
    
}