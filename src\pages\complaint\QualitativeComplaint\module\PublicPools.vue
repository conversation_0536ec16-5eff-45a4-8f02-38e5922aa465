<!--客诉定性池-->
<template>
    <div class="main">
        <a-table
            :columns="columns"
            :dataSource="Complaintdata"
            :pagination="pagination"
            @change="handleTableChange" 
            :scroll="{ x: 1650,y:645 }"
            :rowKey="(record,index) => index"
            :customRow="onClickRow"
            :rowClassName="isRedRow"
            bordered
            :class="Complaintdata.length?'mintable':''"
        >
        <template slot="operation" slot-scope="text, record, index">
            <div v-show="activeKey=='0'">
                <a-button @click="Pickuptheorder(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">取单</a-button>
            </div>
            <div v-show="activeKey=='10'">
                <a-button @click="custdetail(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">定性</a-button>
                <a-divider type="vertical" style="height: 14px; background-color: #ff9900" />
                <a-button type="link" size="small" style="padding: 0px;font-size: 12px" @click="send(record,index)">发送</a-button>
            </div>
            <div v-show="activeKey=='20' && record.status==20">
                <a-button @click="addnewcomplaint(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">投诉工厂</a-button>
            </div>
            <div v-show="activeKey=='20' && record.status!=20">
                <a-button @click="custdetail(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">客诉</a-button>
                <a-divider type="vertical" style="height: 14px; background-color: #ff9900" />
                <a-button @click="addnewcomplaint(record,index)" type="link" size="small" style="padding: 0px;font-size: 12px">投诉</a-button>
            </div>
        </template>
        <template slot="filedown" slot-scope="text, record">
          <a-icon @click="filedown(record.verdictFilePath)" type="download" style="color: #428bca;"></a-icon>
        </template>
    </a-table>    
    </div>
</template>
<script>

export default {
    props:['Complaintdata','activeKey','pagination','columns'],
    name:'PublicPools',
    data(){
        return{
            proOrderId:'',
            selectdata:{},
        }
    },
    created(){
    },
    mounted(){

    },
    methods:{
        onClickRow(record){
                return {
                    on: {
                        click: () => {
                            this.selectdata = record;
                            this.proOrderId=record.id
                        }
                    }
                }
            },
            isRedRow(record){
                let strGroup = []
                let str =[]
                if(record.id && record.id == this.proOrderId) {
                    strGroup.push('rowBackgroundColor')
                }
                return str.concat(strGroup)
            },
        addnewcomplaint(record){
            this.$router.push({path:'Addcomplaint',query:{id:record.id,type:'dxts',statusbar:record.status,tabkey:this.activeKey} })
        },
        custdetail(record){
            this.$router.push({path:'PersonalCharacterization',query:{id:record.id,type:'dxts',statusbar:record.status,tabkey:this.activeKey} })
        },
        Pickuptheorder(val,index){
            this.$emit('Pickuptheorder',val,index)
        },
        send(record){
            this.$emit('send',record)
        },
        handleTableChange(pagination,filter,sorter){
          this.$emit('tableChange', pagination,filter,sorter)
        },
        filedown(path){
            if(path){
                window.location.href = path
            }else{
                this.$message.error('暂无附件,不允许下载文件')
            }        
        },
    }
}

</script>
<style lang="less" scoped>
.mintable{
    /deep/.ant-table-body{
        min-height: 645px;
    }
}
/deep/.ant-pagination-prev {
    margin-left: 8px;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager input{
  padding: 0;
  margin: 0;
}
/deep/.ant-pagination-simple .ant-pagination-simple-pager{
  margin: 0;
}
/deep/.ant-pagination-slash {
    margin: 0;
}
/deep/ .ant-table-pagination.ant-pagination {
    float: left;
    margin: 8px 0 0 10px;
    position: fixed;
  }
.main{
    background: white;
    height: 684px;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
        background: #F8F8F8;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
background: #dfdcdc;
}
/deep/ .ant-table{
        .ant-table-tbody{
            tr.ant-table-row-hover td {
             background: #dfdcdc;
            }
        }
        .rowBackgroundColor {
                background: #c9c9c9!important;
            }       
        .ant-table-thead > tr > th{
            padding: 4px 4px;
        }
        .ant-table-tbody > tr > td {
            padding: 4px 4px!important;
            max-width: 100px;
            color: #000000;
        }
        tr.ant-table-row-selected td {
        background: #dfdcdc;
        }
        tr.ant-table-row-hover td {
        background: #dfdcdc;
        }
}
</style>