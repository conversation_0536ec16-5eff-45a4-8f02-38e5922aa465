<template>
  <div>
    <h3 style="font-weight: 600; font-size: 14px; color: rgba(0, 0, 0, 0.45);">{{title}}</h3>
    <a-spin :spinning="spinning">
    <div id="BarChart" style="height: 600px;">
      </div>    
    </a-spin>
  </div>
</template>
<script>
import * as echarts from 'echarts/core';
import {
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition
]);
var app = {};

var option;
const posList = [
  'left',
  'right',
  'top',
  'bottom',
  'inside',
  'insideTop',
  'insideLeft',
  'insideRight',
  'insideBottom',
  'insideTopLeft',
  'insideTopRight',
  'insideBottomLeft',
  'insideBottomRight'
];
app.configParameters = {
    rotate: {
        min: -90,
        max: 90
    },
    align: {
        options: {
        left: 'left',
        center: 'center',
        right: 'right'
        }
    },
    verticalAlign: {
        options: {
        top: 'top',
        middle: 'middle',
        bottom: 'bottom'
        }
    },
    position: {
        options: posList.reduce(function (map, pos) {
        map[pos] = pos;
        return map;
        }, {})
    },
    distance: {
        min: 0,
        max: 100
    }
};
app.config = {
    rotate: 90,
    align: 'left',
    verticalAlign: 'middle',
    position: 'insideBottom',
    distance: 15,
    onChange: function () {
        const labelOption = {
        rotate: app.config.rotate,
        align: app.config.align,
        verticalAlign: app.config.verticalAlign,
        position: app.config.position,
        distance: app.config.distance
        };
        myChart.setOption({
        series: [
            {
            label: labelOption
            },
            {
            label: labelOption
            },
            {
            label: labelOption
            },
            {
            label: labelOption
            }
        ]
        });
    }
};
const labelOption = {
  show: true,
  position: app.config.position,
  distance: app.config.distance,
  align: app.config.align,
  verticalAlign: app.config.verticalAlign,
  rotate: app.config.rotate,
  formatter: '{c}',
  fontSize: 12,
  rich: {
    name: {}
  }
};
option = {
  color: ['#ff9900','#7C5A23', '#8EC772','#cc7e63','#724e58'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    data: ['目标', '停留', '问客', '人数', '完成']
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    feature: {
      mark: { show: true },
      dataView: { show: true, readOnly: false },
      magicType: { show: true, type: ['line', 'bar', 'stack'] },
      restore: { show: true },
      saveAsImage: { show: true }
    }
  },
  xAxis: [
    {
      type: 'category',
      axisTick: { show: false },
      axisLabel: {
       show: true,
        textStyle: {
          fontSize : 16
        }
     },
      spiltLine:{
        show:true  
      },
      data: []
    }
  ],
  yAxis: [
    {
      type: 'log',
      min: 10,
      logBase: 5 ,
      spiltLine:{
        show:true  
      }
    }
  ],
  series: [
    {
      name: '目标',
      type: 'bar',
      barGap: 0,
      label: labelOption,
      emphasis: {
        focus: 'series'
      },
      formatter: function (params) {
        if (params.value > 0) {
          return params.value;
        } else {
          return '';
        }
      },
      data: []
    },
    {
      name: '停留',
      type: 'bar',
      label: labelOption,
      emphasis: {
        focus: 'series'
      },
      formatter: function (params) {
        if (params.value > 0) {
          return params.value;
        } else {
          return '';
        }
      },
      data: []
    },
    {
      name: '问客',
      type: 'bar',
      label: labelOption,
      emphasis: {
        focus: 'series'
      },
      formatter: function (params) {
        if (params.value > 0) {
          return params.value;
        } else {
          return '';
        }
      },
      data: []
    },
    {
      name: '人数',
      type: 'bar',
      label: labelOption,
      emphasis: {
        focus: 'series'
      },
      formatter: function (params) {
        if (params.value > 0) {
          return params.value;
        } else {
          return '';
        }
      },
      data: []
    },
    {
      name: '完成',
      type: 'bar',
      label: labelOption,
      barMinHeight: 10,
      emphasis: {
        focus: 'series'
      },
      formatter: function (params) {
        if (params.value > 0) {
          return params.value;
        } else {
          return '';
        }
      },
      data: []
    }
  ]
};
export default {
    props:["gropList","title"],
    data () {
        return {
          option,
          spinning: false,
        }
    },
    watch: {
      gropList: function (val) {
        this.spinning = !this.spinning
        this.option.xAxis[0].data = val.xData;
        this.option.series[0].data = val.y1Data;
        this.option.series[1].data = val.y2Data;
        this.option.series[2].data = val.y3Data;
        this.option.series[3].data = val.y4Data;
        this.option.series[4].data = val.y5Data;
        this.echartInit()
      }
    },
    created(){
      this.spinning = !this.spinning
    },
    methods: {
        echartInit () {
            var chartDom = document.getElementById('BarChart');
            var myChart = echarts.init(chartDom);
            myChart.setOption(this.option)
        }
    }
}
</script>