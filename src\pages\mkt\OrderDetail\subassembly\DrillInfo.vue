<template>
  <div class="content">
    <div style="height: 217px; border-bottom: 2px solid #ddd; width: 100%" class="topcontent">
      <a-table
        :columns="columns2"
        :pagination="false"
        :rowKey="(record, index) => `${index + 1}`"
        :scroll="{ y: 180 }"
        :dataSource="lineInfoData"
        :class="lineInfoData.length ? 'mintable' : ''"
      >
        <template slot="orgLayTB_" slot-scope="text, record">
          <span v-if="!editFlag">{{ record.orgLayTB_ }}</span>
          <div v-else>
            <a-select v-model="record.orgLayTB_" style="width: 100%" showSearch allowClear>
              <a-select-option value="t"> t</a-select-option>
              <a-select-option value="b"> b</a-select-option>
            </a-select>
          </div>
        </template>
        <template slot="CU4Org_" slot-scope="text, record, index">
          <span v-if="!editFlag">{{ record.cU4Org_ }}</span>
          <div v-else>
            <a-select
              v-model="record.cU4Org_"
              style="width: 100%"
              showSearch
              allowClear
              optionFilterProp="lable"
              @change="orgchange(index, record.cU4Org_)"
            >
              <a-select-option
                v-for="(item, index) in mapKey1(selectOption.CopperThickness)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </template>
        <template slot="cU4Finished_" slot-scope="text, record, index">
          <span v-if="!editFlag">{{ record.cU4Finished_ }}</span>
          <div v-else>
            <a-select
              v-model="record.cU4Finished_"
              style="width: 100%"
              showSearch
              allowClear
              optionFilterProp="lable"
              @change="finishedchange(index, record.cU4Finished_)"
            >
              <a-select-option
                v-for="(item, index) in mapKey1(selectOption.CopperThickness)"
                :key="index"
                :value="item.value"
                :lable="item.lable"
                :title="item.lable"
              >
                {{ item.lable }}
              </a-select-option>
            </a-select>
          </div>
        </template>
      </a-table>
    </div>
    <div style="height: 217px; border-bottom: 2px solid #ddd" class="botcontent">
      <a-table
        :columns="columns"
        :pagination="false"
        :rowKey="'id'"
        :scroll="{ y: 180 }"
        :dataSource="drilldata"
        :customRow="onClickRow"
        :rowClassName="isRedRow"
        :class="drilldata.length ? 'mintable' : ''"
      >
        <template slot="layer" slot-scope="text, record">
          <span v-if="!editFlag">{{ record.layer }}</span>
          <div v-else>
            <a-input v-model="record.layer" style="width: 100%" showCount />
          </div>
        </template>
        <template slot="beginLayer" slot-scope="text, record">
          <span v-if="!editFlag">{{ record.beginLayer }}</span>
          <div v-else>
            <a-input v-model="record.beginLayer" style="width: 100%" showCount />
          </div>
        </template>
        <template slot="endLayer" slot-scope="text, record">
          <span v-if="!editFlag">{{ record.endLayer }}</span>
          <div v-else>
            <a-input v-model="record.endLayer" style="width: 100%" showCount />
          </div>
        </template>
        <template slot="layerNo" slot-scope="text, record">
          <span v-if="!editFlag">{{ record.layerNo }}</span>
          <div v-else>
            <a-input v-model="record.layerNo" style="width: 100%" showCount />
          </div>
        </template>
        <!-- <template slot="minBorehole" slot-scope="text,record">
              <span v-if="!editFlag">{{record.minBorehole}}</span>
              <div v-else>
                <a-input v-model="record.minBorehole" style='width:100%;'  showCount />
              </div>
            </template>  -->
        <template slot="reMark" slot-scope="text, record">
          <span v-if="!editFlag">{{ record.reMark }}</span>
          <div v-else>
            <a-input v-model="record.reMark" style="width: 100%" showCount />
          </div>
        </template>
      </a-table>
    </div>
    <div style="height: 290px">
      <a-table
        :columns="columns1"
        :pagination="false"
        :rowKey="'id'"
        :scroll="{ y: 252 }"
        :dataSource="infoData"
        :class="infoData.length ? 'mintable1' : ''"
      >
      </a-table>
    </div>
  </div>
</template>
<script>
const columns = [
  {
    title: "序号",
    key: "index",
    align: "center",
    // fixed:'left',
    customRender: (text, record, index) => `${index + 1}`,
    width: 40,
  },
  {
    title: "层名",
    align: "left",
    scopedSlots: { customRender: "layer" },
    ellipsis: true,
    width: 143,
  },
  {
    title: "开始层",
    align: "left",
    dataIndex: "beginLayer",
    scopedSlots: { customRender: "beginLayer" },
    ellipsis: true,
    width: 143,
  },

  {
    title: "结束层",
    align: "left",
    ellipsis: true,
    dataIndex: "endLayer",
    scopedSlots: { customRender: "endLayer" },
    width: 143,
  },
  {
    title: "层数",
    align: "left",
    ellipsis: true,
    dataIndex: "layerNo",
    scopedSlots: { customRender: "layerNo" },
    width: 143,
  },
  {
    title: "总孔数",
    align: "left",
    ellipsis: true,
    dataIndex: "totalNumberHoles",
    width: 143,
  },
  {
    title: "最小孔径",
    align: "left",
    ellipsis: true,
    dataIndex: "minBorehole",
    // scopedSlots: {customRender: 'minBorehole'},
    width: 143,
  },
  {
    title: "备注",
    scopedSlots: { customRender: "reMark" },
    align: "left",
    ellipsis: true,
    dataIndex: "reMark",
    width: 143,
  },
];
const columns1 = [
  {
    dataIndex: "index",
    key: "index",
    width: 40,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    className: "index_",
  },
  {
    title: "刀序",
    dataIndex: "toolno",
    align: "center",
  },
  {
    title: "形状",
    dataIndex: "toolShape",
    align: "center",
  },
  {
    title: "类型",
    dataIndex: "toolkind",
    align: "center",
  },
  {
    title: "刀径",
    dataIndex: "tooldia",
    align: "center",
  },
  // {
  //     title: "成品孔径",
  //     dataIndex: "storedia",
  //     align:'center',
  // },
  {
    title: "槽长",
    dataIndex: "length",
    align: "center",
  },

  {
    title: "孔数",
    dataIndex: "qty",
    align: "center",
  },
];
const columns2 = [
  {
    title: "序号",
    dataIndex: "index",
    align: "center",
    key: "index",
    width: 40,
    ellipsis: true,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "层名",
    dataIndex: "layName_",
    align: "center",
    ellipsis: true,
    width: 80,
  },
  {
    title: "T/B",
    align: "center",
    scopedSlots: { customRender: "orgLayTB_" },
    ellipsis: true,
    width: 60,
  },
  // {
  //   title: "原始层名",
  //   dataIndex: "orgLayName_",
  //   align:'center',
  //   ellipsis: true,
  //   width:80,
  // },
  {
    title: "线宽",
    className: "select-sty",
    dataIndex: "minLineW_",
    ellipsis: true,
    align: "center",
    width: 50,
  },
  {
    title: "线距",
    className: "select-sty",
    dataIndex: "minLineSpace_",
    ellipsis: true,
    align: "center",
    width: 50,
  },
  {
    title: "孔到线",
    className: "select-sty",
    dataIndex: "minDrlToLine",
    ellipsis: true,
    align: "center",
    width: 50,
  },
  {
    title: "焊环",
    className: "select-sty",
    dataIndex: "minRing",
    ellipsis: true,
    align: "center",
    width: 50,
  },
  {
    title: "孔距铜",
    className: "select-sty",
    dataIndex: "minDrlToCu",
    ellipsis: true,
    align: "center",
    width: 50,
  },
  {
    title: "孔到孔",
    className: "select-sty",
    dataIndex: "minDrlToDrl",
    ellipsis: true,
    align: "center",
    width: 50,
  },
  {
    title: "线到板边",
    className: "select-sty",
    ellipsis: true,
    align: "center",
    dataIndex: "minLineToBoard",
    width: 70,
  },
  {
    title: "pad到板边",
    className: "select-sty",
    ellipsis: true,
    align: "center",
    dataIndex: "minPadToBoard",
    width: 70,
  },
  {
    title: "基铜",
    className: "select-sty",
    scopedSlots: { customRender: "CU4Org_" },
    ellipsis: true,
    align: "center",
    width: 75,
  },
  {
    title: "完成铜厚",
    className: "select-sty",
    ellipsis: true,
    scopedSlots: { customRender: "cU4Finished_" },
    align: "center",
    dataIndex: "cU4Finished_",
    width: 75,
  },
  {
    title: "残铜率",
    className: "select-sty",
    dataIndex: "copperRatio_",
    ellipsis: true,
    align: "center",
    width: 65,
  },
];
export default {
  name: "",
  props: ["drilldata", "drillDiameters", "lineInfoData", "editFlag", "selectOption"],
  components: {},
  data() {
    return {
      columns,
      dataSource: [],
      selectedRowKeysArray: [],
      proOrderId: "",
      infoData: [],
      columns1,
      columns2,
    };
  },
  created() {
    console.log(this.lineInfoData, "this.drilldata");
  },
  methods: {
    orgchange(index) {
      console.log(this.lineInfoData, "this.lineInfoData");
      if (index == 0) {
        this.lineInfoData[this.lineInfoData.length - 1].cU4Org_ = this.lineInfoData[index].cU4Org_;
      }
      if (index > 0 && index < this.lineInfoData.length - 1) {
        for (var a = 1; a < this.lineInfoData.length - 1; a++) {
          if (a >= index) {
            this.lineInfoData[a].cU4Org_ = this.lineInfoData[index].cU4Org_;
          }
        }
      }
    },
    finishedchange(index, val) {
      if (index == 0) {
        this.lineInfoData[this.lineInfoData.length - 1].cU4Finished_ = this.lineInfoData[index].cU4Finished_;
      }
      if (index > 0 && index < this.lineInfoData.length - 1) {
        for (var a = 1; a < this.lineInfoData.length - 1; a++) {
          if (a >= index) {
            this.lineInfoData[a].cU4Finished_ = this.lineInfoData[index].cU4Finished_;
          }
        }
      }
    },
    onClickRow(record) {
      return {
        on: {
          click: () => {
            let keys = [];
            keys.push(record.id);
            this.selectedRowKeysArray = keys;
            this.proOrderId = record.id;
            this.infoData = this.drillDiameters.filter(v => v.pId == this.proOrderId);
          },
        },
      };
    },
    mapKey1(data) {
      if (!data && data.length == 0) {
        return [];
      } else {
        return data.map(item => {
          return { value: item.valueMember, lable: item.text };
        });
      }
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.id && record.id == this.proOrderId) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-select-selection--single {
  height: 27px !important;
}
/deep/.ant-select-selection__rendered {
  line-height: 26px;
}
.content {
  width: 70%;
  border: 1px solid rgb(233, 233, 240);
  min-height: 725px;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  // margin-bottom:4px;
  /deep/.ant-table-thead > tr > th {
    padding: 4px 7px !important;
    border-right: 1px solid rgb(233, 233, 240);
  }
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 4px 7px !important;
    border-right: 1px solid rgb(233, 233, 240);
  }
  /deep/.ant-table {
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
  }

  /deep/ .rowBackgroundColor {
    background: #dfdcdc !important;
  }
  .topcontent {
    .mintable {
      /deep/.ant-table-content > .ant-table-scroll > .ant-table-body {
        // min-height:695px
        min-height: 180px;
        border-bottom: 1px solid rgb(233, 233, 240);
      }
    }
  }
  .botcontent {
    .mintable {
      /deep/.ant-table-content > .ant-table-scroll > .ant-table-body {
        // min-height:695px
        min-height: 180px;
        border-bottom: 1px solid rgb(233, 233, 240);
      }
    }
  }

  .mintable1 {
    /deep/.ant-table-content > .ant-table-scroll > .ant-table-body {
      // min-height:695px
      min-height: 252px;
    }
  }

  /deep/.ant-table-fixed-columns-in-body:not([colspan]) {
    color: black;
  }
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #dfdcdc;
  }
}
</style>
