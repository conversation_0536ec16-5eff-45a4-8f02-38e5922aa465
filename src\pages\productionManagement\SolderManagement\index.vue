<!-- 车间管理-阻焊管理 -->
<template>
  <div class="solderManagement">
    <a-spin :spinning="spinning">
      <div class="content">
        <div class="left" ref="letfDom" style="width: 65%">
          <a-card :bordered="false" style="border: 1px solid #e9e9f0" @contextmenu.prevent="rightClick($event)">
            <vxe-table
              border
              stripe
              show-overflow
              :scroll-y="{ enabled: true, gt: 20 }"
              :height="left1height"
              :loading="table1Loading"
              :row-config="{ isCurrent: true, isHover: true }"
              @cell-click="cellClickEvent"
              :menu-config="{ enabled: true }"
              @cell-menu="cellContextMenuEvent"
              :data="data1Source"
            >
              <vxe-column type="seq" title="序号" width="50" fixed="left" style="text-align: center" align="center"></vxe-column>
              <vxe-column field="orderNo" title="拼板编号" fixed="left" show-overflow :width="orderwidth">
                <template #default="{ row }">
                  {{ row.orderNo }}&nbsp;
                  <span class="tagNum">
                    <a-icon type="thunderbolt" theme="filled" v-if="row.isUrgent" style="color: #ff9900"></a-icon>
                  </span>
                </template>
              </vxe-column>
              <vxe-column field="cardNo" title="流程卡号" width="120" show-overflow></vxe-column>
              <vxe-column field="craftKey_" title="工序" :width="Keywidth" show-overflow></vxe-column>
              <vxe-column field="totalNum" title="PNL数" width="70" show-overflow></vxe-column>
              <vxe-column field="area_" title="面积" width="70" show-overflow></vxe-column>
              <vxe-column field="boardThickness" title="厚度" width="50" show-overflow></vxe-column>
              <vxe-column field="minHole_" title="最小钻" width="60" show-overflow></vxe-column>
              <vxe-column field="stackNum" title="片/叠" width="50" show-overflow></vxe-column>
              <vxe-column field="stacks" title="叠数" width="50" show-overflow></vxe-column>
              <vxe-column field="totalcu" title="总铜厚" width="50" show-overflow></vxe-column>
              <vxe-column field="lSize_" title="长" width="70" show-overflow></vxe-column>
              <vxe-column field="wSize_" title="宽" width="70" show-overflow></vxe-column>
              <vxe-column field="allHoleNum_" title="总孔数" width="70" show-overflow></vxe-column>
              <vxe-column field="remainderTime" title="剩余交货小时" width="120" show-overflow></vxe-column>
              <vxe-column field="deliveryDate" title="交货日期" width="100" show-overflow></vxe-column>
              <vxe-column field="note_" title="备注" width="200" show-overflow></vxe-column>
              <!-- <vxe-column field="stretchState_" title="拉伸状态" width="80" show-overflow></vxe-column> -->
              <!-- <vxe-column field="isLvji_" title="是否铝基" width="80" show-overflow>
                <template #default="{ row }">
                  <a-checkbox :checked="row.isLvji_"></a-checkbox>
                </template>
              </vxe-column> -->
              <!-- <vxe-column field="" title="线圈板" width="80" show-overflow></vxe-column> -->
              <vxe-column field="createTime" title="到序时间" width="100" show-overflow></vxe-column>
              <vxe-column field="machineStart_" title="机台派单" width="100" show-overflow></vxe-column>
              <vxe-column field="drEndDate_" title="加工完成时间" width="120" show-overflow></vxe-column>
              <vxe-column field="orderFactoryKey" title="订单工厂" width="80" show-overflow></vxe-column>
              <vxe-column field="isStop_" title="暂停" width="50" show-overflow>
                <template #default="{ row }">
                  <a-checkbox :checked="row.isStop_"></a-checkbox>
                </template>
              </vxe-column>
              <!-- <vxe-column field="note" title="生产备注" width="200" show-overflow></vxe-column> -->
            </vxe-table>
            <a-menu :style="menuStyle" v-show="menuVisible" class="tabRightClikBox">
              <a-menu-item @click="down">下载</a-menu-item>
              <a-menu-item>
                <a-upload accept=".drl,.2nd" name="file" :before-upload="beforeUpload" :customRequest="httpRequest"> 上传 </a-upload>
              </a-menu-item>
            </a-menu>
          </a-card>
          <a-card :bordered="false" style="border: 1px solid #e9e9f0">
            <vxe-table
              border
              stripe
              show-overflow
              :scroll-y="{ enabled: true, gt: 20 }"
              :height="left2height"
              :loading="table2Loading"
              :row-config="{ isCurrent: true, isHover: true }"
              @cell-click="cellClickEvent1"
              :menu-config="{ enabled: true }"
              @cell-menu="cellContextMenuEvent1"
              :data="data2Source"
            >
              <vxe-column type="seq" title="序号" width="50" fixed="left" style="text-align: center" align="center"></vxe-column>
              <vxe-column field="orderNo" title="拼板编号" fixed="left" show-overflow :width="orderwidth"></vxe-column>
              <vxe-column field="cardNo" title="流程卡号" width="120" show-overflow></vxe-column>
              <vxe-column field="craftKey_" title="工序" :width="Keywidth" show-overflow></vxe-column>
              <vxe-column field="totalNum" title="PNL数" width="70" show-overflow></vxe-column>
              <vxe-column field="area_" title="面积" width="70" show-overflow></vxe-column>
              <vxe-column field="boardThickness" title="厚度" width="50" show-overflow></vxe-column>
              <vxe-column field="minHole_" title="最小钻" width="60" show-overflow></vxe-column>
              <vxe-column field="stackNum" title="片/叠" width="50" show-overflow></vxe-column>
              <vxe-column field="stacks" title="叠数" width="50" show-overflow></vxe-column>
              <vxe-column field="totalcu" title="总铜厚" width="50" show-overflow></vxe-column>
              <vxe-column field="lSize_" title="长" width="70" show-overflow></vxe-column>
              <vxe-column field="wSize_" title="宽" width="70" show-overflow></vxe-column>
              <vxe-column field="allHoleNum_" title="总孔数" width="70" show-overflow></vxe-column>
              <vxe-column field="remainderTime" title="剩余交货小时" width="120" show-overflow></vxe-column>
              <vxe-column field="deliveryDate" title="交货日期" width="100" show-overflow></vxe-column>
              <vxe-column field="machineCount_" title="已派机台" width="80" show-overflow></vxe-column>
              <vxe-column field="note_" title="备注" width="200" show-overflow></vxe-column>
              <!-- <vxe-column field="" title="线圈板" width="80" show-overflow></vxe-column> -->
              <vxe-column field="createTime" title="到序时间" width="100" show-overflow></vxe-column>
              <vxe-column field="inlDate_" title="派单时间" width="120" show-overflow></vxe-column>
              <vxe-column field="orderFactoryKey" title="订单工厂" width="80" show-overflow></vxe-column>
              <vxe-column field="isStop_" title="暂停" width="50" show-overflow>
                <template #default="{ row }">
                  <a-checkbox :checked="row.isStop_"></a-checkbox>
                </template>
              </vxe-column>
            </vxe-table>
          </a-card>
        </div>
        <div class="right" style="width: 35%">
          <solder-center
            :tableData="data3Source"
            :table3Loading="table3Loading"
            :machineStatuList="data4Source"
            :machineStatuLoad="table4Loading"
            :drillLoad="table5Loading"
            :drillList="data5Source"
            :dispatchLoad="table6Loading"
            :dispatchList="data6Source"
            @getAssignmentList="getAssignmentList"
            @clearlist="data6Source = []"
            @getDispatchFallback="getDispatchFallback"
            @getDrillList="getDrillList"
            @getMesaList="getMesaList"
            ref="solderCenter"
            :idList="idList"
          ></solder-center>
        </div>
      </div>
      <div class="footer">
        <div class="actionBox">
          <solder-action
            @handleDispatchMachine="handleDispatchMachine"
            @Scancodetodispatch="Scancodetodispatch"
            @finishorder="finishorder"
            @Productioncompleted="Productioncompleted"
            @OverOrderClick="OverOrderClick"
            @UploadOrderClick="UploadOrderClick"
            @harmomegathusRegisterClick="harmomegathusRegisterClick"
            @SetUpExpeditingClick="SetUpExpeditingClick"
            @DeleteOrderClick="DeleteOrderClick"
            @ExceptionRemarksClick="ExceptionRemarksClick"
            @DataCheckClick="DataCheckClick"
            @queryClick="queryClick"
            @operationClick1="operationClick1"
            @operationClick2="operationClick2"
            @operationClick3="operationClick3"
            @Editingmachine="Editingmachine"
            :btnloading1="btnloading1"
            :btnloading3="btnloading3"
            :btnloading4="btnloading4"
            :btnloading5="btnloading5"
            :btnloading6="btnloading6"
            :btnloading7="btnloading7"
          ></solder-action>
        </div>
      </div>
      <!-- 部门过序弹窗 -->
      <a-modal
        title="部门过序"
        :visible="dataVisible"
        @cancel="reportHandleCancel"
        @ok="handleOk1"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
        :destroyOnClose="true"
        centered
      >
        <over-order-info
          @keyupEnter="keyupEnter"
          :quantity="quantity"
          @quantityChange="quantityChange"
          :quantityCopy="quantityCopy"
          ref="overOrder"
        />
      </a-modal>
      <!-- 上传订单弹窗 -->
      <a-modal
        title="录入订单"
        :visible="dataVisible1"
        @cancel="reportHandleCancel"
        @ok="handleOk2"
        centered
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <enter-order-info ref="enterOderForm" />
      </a-modal>
      <!-- 涨缩登记弹窗 -->
      <a-modal
        title="涨缩登记"
        :visible="dataVisible2"
        @cancel="reportHandleCancel"
        centered
        @ok="handleOk3"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
      >
        <harmomegathus-register ref="harmomeForm"></harmomegathus-register>
      </a-modal>
      <!-- 生产备注弹窗 -->
      <a-modal
        title="生产备注"
        centered
        :visible="dataVisible3"
        @cancel="reportHandleCancel"
        @ok="handleOk4"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :confirmLoading="confirmLoading"
      >
        <exception-remarks-info ref="exceptionRemarksInfo" />
      </a-modal>
      <!-- 查询弹窗 -->
      <a-modal
        title="订单查询"
        :visible="dataVisible4"
        @cancel="reportHandleCancel"
        @ok="handleOk5"
        centered
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="400"
        :confirmLoading="confirmLoading"
      >
        <query-info ref="queryInfo" />
      </a-modal>
      <!--确认弹窗-->
      <a-modal
        title="确认弹窗"
        :visible="qrdataVisible"
        @cancel="qrdataVisible = false"
        centered
        @ok="qrhandleOk"
        ok-text="确定"
        :confirmLoading="btnloading2"
        destroyOnClose
        :maskClosable="false"
        :width="400"
      >
        <span>{{ messagelist }}</span>
      </a-modal>
      <!--修改机台信息-->
      <a-modal
        title="修改机台信息"
        :visible="machineVisible"
        @cancel="machineVisible = false"
        centered
        @ok="machinehandleOk"
        ok-text="确定"
        destroyOnClose
        :maskClosable="false"
        :width="700"
      >
        <machine :machinedata="machinedata"></machine>
      </a-modal>
      <!--扫码分派-->
      <a-modal
        title="扫码分派"
        :visible="scancodeVisible"
        @cancel="scancodeVisible = false"
        ok-text="确定"
        :maskClosable="false"
        :width="400"
        :destroyOnClose="true"
        centered
      >
        <a-form-item label="扫码分派" :label-col="{ span: 5 }" :wrapper-col="{ span: 16 }">
          <a-input v-model="scancode" :autoFocus="true" @keyup.enter="scancodeOk" />
        </a-form-item>
        <template #footer>
          <a-button key="back" @click="scancodeVisible = false">取消</a-button>
        </template>
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import { processorder } from "@/services/management";
import {
  getDispatchList,
  getDoingOrderList,
  byid,
  updatemachineinfo,
  getDrillHoleList,
  getMachineList,
  getStatisticsList,
  getWaitOrderList,
  getDispatchMachineList,
  processfinishorder,
  getDispatchFallbackList,
  getpassStepNum,
  DrillingSequence,
  UploadOrder,
  getHarmomegathusRegister,
  SetUpExpediting,
  DeleteOrder,
  ExceptionRemarks,
  DataCheck,
  CallTrolley,
  Confirm,
  AgvCancel,
  getOrderMuIdList,
  downLoad,
  UploadFile1,
  orderFileUpload,
  allfinishorder,
} from "@/services/management";
import SolderCenter from "@/pages/productionManagement/SolderManagement/module/SolderCenter";
import SolderAction from "@/pages/productionManagement/SolderManagement/module/SolderAction";
import OverOrderInfo from "@/pages/productionManagement/SolderManagement/module/OverOrderInfo";
import EnterOrderInfo from "@/pages/productionManagement/SolderManagement/module/EnterOrderInfo";
import HarmomegathusRegister from "@/pages/productionManagement/SolderManagement/module/HarmomegathusRegister";
import ExceptionRemarksInfo from "@/pages/productionManagement/SolderManagement/module/ExceptionRemarksInfo";
import QueryInfo from "@/pages/productionManagement/SolderManagement/module/QueryInfo";
import machine from "@/pages/productionManagement/machine";

export default {
  name: "SolderManagement",
  components: {
    SolderCenter,
    SolderAction,
    OverOrderInfo,
    EnterOrderInfo,
    HarmomegathusRegister,
    ExceptionRemarksInfo,
    QueryInfo,
    machine,
  },
  inject: ["reload"],
  data() {
    return {
      left2height: "",
      orderwidth: "130",
      Keywidth: "80",
      machinedata: {},
      machineVisible: false,
      left1height: "",
      spinning: false,
      loading: false,
      table1Loading: false, // 待分派表格load
      table2Loading: false, // 已分派表格load
      table3Loading: false, // 机台表格load
      table4Loading: false, // 机台状态load
      table5Loading: false, // 钻刀表格load
      table6Loading: false, // 分派状态load
      data1Source: [], // 待分派集合
      data2Source: [], // 已分派集合
      data3Source: [], // 机台集合
      data4Source: [], // 机台状态集合
      data5Source: [], // 钻刀集合
      data6Source: [], // 分派集合
      selectedRowList: [],
      selectedRowList1: [],
      selectedRows: [],
      assignMachineList: [],
      rowId2: "",
      dataVisible: false, //部门过序弹窗不显示
      dataVisible1: false, //上传订单弹窗不显示
      dataVisible2: false, //涨缩登记弹窗不显示
      dataVisible3: false, //生产备注弹窗不显示
      dataVisible4: false, //查询弹窗
      qrdataVisible: false,
      modaltype: "",
      selectedRows1: {},
      messagelist: "",
      quantity: "",
      quantityCopy: "",
      cardNo: "",
      confirmLoading: false,
      note: "",
      idList: [],
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      menuVisible: false,
      menuData: [],
      btnloading1: false, // 分派按钮loading
      btnloading2: false, // 确认弹窗按钮loading
      btnloading3: false, // 数据核对按钮loading
      btnloading4: false, // 删除订单按钮loading
      btnloading5: false, // 呼叫小车按钮loading
      btnloading6: false, // 人员确认按钮loading
      btnloading7: false, // 取消小车按钮loading
      agvID: "",
      scancodeVisible: false,
      scancode: "",
      // letfDom: null,
      // clientStartX: 0
    };
  },
  watch: {
    data1Source: {
      handler: function (val) {
        this.handleSourceChange(val);
      },
    },
    data2Source: {
      handler: function (val) {
        this.handleSourceChange(val);
      },
    },
  },
  methods: {
    handleSourceChange(val) {
      if (val.length) {
        this.$nextTick(() => {
          const getTagNumChildrenLengths = () => {
            const tagNumElements = document.getElementsByClassName("tagNum");
            return Array.from(tagNumElements).map(element => element.children.length);
          };
          const getLongestStringLength = (property, factor) => {
            return val.reduce((maxLength, item) => {
              const str = item[property].trim();
              return str.length * factor > maxLength ? str.length * factor : maxLength;
            }, 0);
          };
          const tagNumChildrenLengths = getTagNumChildrenLengths();
          const maxTagNumChildrenLength = Math.max(...tagNumChildrenLengths) * 20 == -Infinity ? 0 : Math.max(...tagNumChildrenLengths) * 20;
          const longestCraftKeyLength = getLongestStringLength("craftKey_", 20);
          const longestOrderNoLength = getLongestStringLength("orderNo", 11);
          const finalWidth = maxTagNumChildrenLength + longestOrderNoLength;
          this.Keywidth = longestCraftKeyLength;
          this.orderwidth = finalWidth;
        });
      }
    },
    handleResize() {
      if ((window.innerHeight - 140) / 2 <= 390) {
        this.left1height = (window.innerHeight - 140) / 2;
        this.left2height = (window.innerHeight - 140) / 2;
      } else {
        this.left1height = "390";
        this.left2height = "390";
      }
    },
    // 待上机列表
    getorderList(queryData) {
      let params = {};
      if (queryData) {
        params.Pdctno_ = queryData.Pdctno_;
        params.FacAccredit_ = queryData.FacAccredit_;
      }
      params.process = "SolderMask";
      this.table1Loading = true;
      getWaitOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data1Source = res.data;
          }
        })
        .finally(() => {
          this.table1Loading = false;
        });
    },
    // 已上机列表
    getdoOrderList(queryData) {
      let params = {};
      if (queryData) {
        params.Pdctno_ = queryData.Pdctno_;
        params.FacAccredit_ = queryData.FacAccredit_;
      }
      params.process = "SolderMask";
      this.table2Loading = true;
      getDoingOrderList(params)
        .then(res => {
          if (res.code == 1) {
            this.data2Source = res.data || [];
          }
        })
        .finally(() => {
          this.table2Loading = false;
        });
    },

    // 机台列表
    getMesaList(FacAccredit) {
      let params = {};
      if (FacAccredit) {
        params.FacAccredit = FacAccredit;
      }
      params.type = 81;
      this.table3Loading = true;
      getMachineList(params)
        .then(res => {
          if (res.code == 1) {
            this.data3Source = res.data;
          }
        })
        .finally(() => {
          this.table3Loading = false;
        });
    },
    // 状态统计列表
    getMachineStatuList() {
      this.table4Loading = true;
      getStatisticsList("SolderMask")
        .then(res => {
          if (res.code == 1) {
            this.data4Source = res.data;
          }
        })
        .finally(() => {
          this.table4Loading = false;
        });
    },
    // 钻刀列表
    getDrillList(id) {
      this.table5Loading = true;
      getDrillHoleList(id)
        .then(res => {
          if (res.code == 1) {
            this.data5Source = res.data;
          }
        })
        .finally(() => {
          this.table5Loading = false;
        });
    },
    // 分派列表
    getAssignmentList(id, length) {
      this.table6Loading = true;
      if (length && length > 2) {
        this.data6Source = [];
        this.table6Loading = false;
        this.$refs.solderCenter.Listdata = {};
      } else {
        getDispatchList(id)
          .then(res => {
            if (res.code == 1) {
              this.data6Source = res.data;
              this.$refs.solderCenter.Listdata = {};
            }
          })
          .finally(() => {
            this.table6Loading = false;
          });
      }
    },
    //扫码分派
    Scancodetodispatch() {
      if (this.$refs.solderCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      this.scancodeVisible = true;
    },
    scancodeOk() {
      if (!this.scancode) {
        return;
      }
      processorder(this.$refs.solderCenter.selectedRowKeys, 8, this.scancode).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.$refs.solderCenter.selectedRowKeys.forEach(ite => {
            this.data3Source.filter(item => item.iD_ == ite)[0].numMachine_ += 1;
          });
          this.scancode = "";
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 分派
    handleDispatchMachine() {
      if (this.selectedRowList.length <= 0) {
        this.$message.warning("请选择待分派订单");
        return;
      } else if (this.$refs.solderCenter.selectedRowKeys.length <= 0) {
        this.$message.warning("请至少选择1台机台");
        return;
      }
      let type_ = false;
      const selectedData = this.data3Source.filter(item => this.$refs.solderCenter.selectedRowKeys.includes(item.iD_));
      if (selectedData.length) {
        selectedData.forEach(item => {
          if (item.status_ == 1) {
            type_ = true;
          }
        });
      }
      if (type_) {
        this.$message.error("选择机台中有故障机台，不允许分派");
        return;
      }
      this.spinning = true;
      this.btnloading1 = true;
      // 获取待分派订单id和机台id
      let assignmentData = {
        id: this.selectedRowList[0],
        equId: this.$refs.solderCenter.selectedRowKeys,
      };
      getDispatchMachineList(assignmentData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("分派成功");
            this.data1Source.splice(
              this.data1Source.findIndex(item => item.guid_ == this.selectedRowList[0]),
              1
            );
            this.selectedRowList = [];
            this.$refs.solderCenter.selectedRowKeys.forEach(ite => {
              this.data3Source.filter(item => item.iD_ == ite)[0].numMachine_ += 1;
            });
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading1 = false;
          this.spinning = false;
        });
    },
    //完成
    finishorder() {
      if (this.selectedRowList1.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      this.qrdataVisible = true;
      this.messagelist = "【" + this.selectedRows1.orderNo + "】确认完成该订单吗?";
      this.modaltype = "finish";
    },
    //生产完成
    Productioncompleted() {
      if (JSON.stringify(this.selectedRows1) == "{}") {
        this.$message.warning("请选择订单");
        return;
      }
      this.qrdataVisible = true;
      this.messagelist = "【" + this.selectedRows1.orderNo + "】确认该订单生产完成吗?";
      this.modaltype = "Production";
    },
    qrhandleOk() {
      this.btnloading2 = true;
      this.qrdataVisible = false;
      this.spinning = true;
      if (this.modaltype == "Production") {
        allfinishorder({ id: this.selectedRows1.guid_ })
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              if (this.$refs.solderCenter.rowId) {
                this.getAssignmentList(this.$refs.solderCenter.rowId);
              }
              this.getdoOrderList();
              this.getMesaList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.btnloading2 = false;
            this.spinning = false;
          });
      }
      if (this.modaltype == "finish") {
        processfinishorder({ id: this.selectedRowList1[0] })
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              if (this.$refs.solderCenter.rowId) {
                this.getAssignmentList(this.$refs.solderCenter.rowId);
              }
              this.getdoOrderList();
              this.getMesaList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.btnloading2 = false;
            this.spinning = false;
          });
      }
      if (this.modaltype == "urgent") {
        SetUpExpediting(this.selectedRowList[0])
          .then(res => {
            if (res.code == 1) {
              this.$message.success(res.message);
              this.getorderList();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.btnloading2 = false;
            this.spinning = false;
          });
      }
    },
    // 分派回退
    getDispatchFallback(record) {
      if (confirm("确定回退吗？")) {
        this.spinning = true;
        getDispatchFallbackList({ id: record.guid_ })
          .then(res => {
            if (res.code == 1) {
              this.$message.success("分派回退成功");
              this.getAssignmentList(record.iD_);
              this.reload();
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    cellClickEvent({ row, column }) {
      let keys = [];
      keys.push(row.guid_);
      this.selectedRowList = keys;
      this.menuData = row;
      this.$refs.solderCenter.flagClick();
      this.getDrillList(row.guid_);
      this.$refs.solderCenter.rowId = "";
      getOrderMuIdList(row.guid_).then(res => {
        if (res.code == 1) {
          this.idList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    cellContextMenuEvent({ row, column, $event }) {
      $event.preventDefault();
      this.menuData = row;
    },
    cellClickEvent1({ row, column }) {
      let keys = [];
      keys.push(row.guid_);
      this.selectedRowList1 = keys;
      this.selectedRows1 = row;
      this.$refs.solderCenter.flagClick();
      this.getDrillList(row.guid_);
      this.$refs.solderCenter.rowId = "";
      getOrderMuIdList(row.guid_).then(res => {
        if (res.code == 1) {
          this.idList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    cellContextMenuEvent1({ row, column, $event }) {
      $event.preventDefault();
      this.menuData = row;
    },
    // 弹窗关闭控制
    reportHandleCancel() {
      this.dataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.dataVisible3 = false;
      this.dataVisible4 = false;
    },
    quantityChange(payload) {
      this.quantity = payload;
    },
    // 部门过序
    OverOrderClick() {
      this.dataVisible = true;
    },
    // 获取部门过序数量
    async keyupEnter(cardNo) {
      this.confirmLoading = true;
      let params = {
        cardNo: cardNo,
      };
      await getpassStepNum(params).then(res => {
        if (res.code == 1) {
          this.cardNo = cardNo;
          this.quantity = res.data;
          this.quantityCopy = JSON.parse(JSON.stringify(res.data));
          this.$message.success("获取过序数量成功");
        } else {
          this.$message.error(res.message);
        }
        this.confirmLoading = false;
      });
    },
    handleOk1() {
      this.confirmLoading = true;
      this.spinning = true;

      // 钻孔过序
      let paramsData = {
        cardNo: this.cardNo,
        num: this.quantity,
      };
      DrillingSequence(paramsData)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("过序成功");
            this.getorderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible = false;
        });
    },
    // 上传订单
    UploadOrderClick() {
      this.dataVisible1 = true;
    },
    handleOk2() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = this.$refs.enterOderForm.enterOrderForm;
      console.log("上传文件传值", params);
      UploadOrder(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("上传成功");
            this.getorderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible1 = false;
        });
    },
    // 涨缩登记
    harmomegathusRegisterClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.dataVisible2 = true;
    },
    handleOk3() {
      this.confirmLoading = true;
      this.spinning = true;
      let params = {
        id: this.selectedRowList[0],
        x: this.$refs.harmomeForm.harmomeForm.valuex,
        y: this.$refs.harmomeForm.harmomeForm.valuey,
      };
      // console.log('params',params)
      getHarmomegathusRegister(params)
        .then(res => {
          if (res.code == 1) {
            this.$message.success("登记成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.dataVisible2 = false;
          this.confirmLoading = false;
        });
    },

    // 设置加急
    SetUpExpeditingClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.messagelist = "【" + this.menuData.orderNo + "】确认设置/取消加急该订单吗?";
      this.modaltype = "urgent";
      this.qrdataVisible = true;
    },
    // 删除订单
    DeleteOrderClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.btnloading4 = true;
      this.spinning = true;
      DeleteOrder(this.selectedRowList[0])
        .then(res => {
          if (res.code == 1) {
            this.$message.success("设置成功");
            this.getorderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading4 = false;
          this.spinning = false;
        });
    },
    // 生产备注
    ExceptionRemarksClick() {
      if (this.selectedRowList.length <= 0) {
        return this.$message.warning("请选择订单");
      }
      this.dataVisible3 = true;
    },
    handleOk4() {
      this.confirmLoading = true;
      this.spinning = true;
      let note = this.$refs.exceptionRemarksInfo.note;
      ExceptionRemarks({ note: note, id: this.selectedRowList[0] })
        .then(res => {
          if (res.code == 1) {
            this.$message.success(res.message);
            this.getorderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.confirmLoading = false;
          this.dataVisible3 = false;
        });
    },
    //数据核对
    DataCheckClick() {
      this.btnloading3 = true;
      this.spinning = true;
      DataCheck()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("数据核对");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading3 = false;
          this.spinning = false;
        });
    },
    //查询
    queryClick() {
      this.dataVisible4 = true;
    },
    handleOk5() {
      this.dataVisible4 = false;
      let queryData = {
        Pdctno_: this.$refs.queryInfo.OrderNumber,
        FacAccredit_: this.$refs.queryInfo.FacAccredit,
      };
      this.getorderList(queryData);
      this.getdoOrderList(queryData);
      this.getMesaList(this.$refs.queryInfo.FacAccredit);
    },
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    rightClick(e) {
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left = e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    down() {
      downLoad(this.menuData.guid_).then(res => {
        if (res.code == 1) {
          window.location.href = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    async httpRequest(data, type) {
      const formData = new FormData();
      formData.append("file", data.file);
      const resData = await UploadFile1(formData).then(res => {
        return res;
      });
      if (resData.code == 1) {
        orderFileUpload(this.menuData.guid_, { path: resData.data }).then(res => {
          // debugger
          if (res.code == 1) {
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    beforeUpload(file) {
      const isFileType =
        (file.name.toLowerCase().indexOf(".drl") != -1 || file.name.toLowerCase().indexOf(".2nd") != -1) &&
        file.name.toLowerCase().indexOf(this.menuData.pdctno_.toLowerCase()) != -1;
      if (!isFileType) {
        this.$message.error("只支持.drl和.2nd格式文件,且文件名必须包含拼板编号。");
      }
      return isFileType;
    },
    // 呼叫小车
    operationClick1() {
      this.btnloading5 = true;
      this.spinning = true;
      CallTrolley()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("呼叫成功");
            this.agvID = res.data;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading5 = false;
          this.spinning = false;
        });
    },
    // 人员确认
    operationClick2() {
      this.btnloading6 = true;
      this.spinning = true;
      Confirm()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("人员已确认");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading6 = false;
          this.spinning = false;
        });
    },
    //编辑机台信息
    Editingmachine() {
      if (this.$refs.solderCenter.selectedRowKeys.length == 0) {
        this.$message.error("请选择需要编辑的机台");
        return;
      }
      if (this.$refs.solderCenter.selectedRowKeys.length > 1) {
        this.$message.error("只能选择一个机台进行编辑");
        return;
      }
      byid(this.$refs.solderCenter.selectedRowKeys[0]).then(res => {
        if (res.code) {
          this.machinedata = res.data;
          this.machineVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    machinehandleOk() {
      var x = /^\+?[0-9][0-9]*$/;
      let inttype = ["accCount_", "num_", "tableNo_", "display_"];
      if (this.machinedata.tableNo_ && !x.test(this.machinedata.tableNo_)) {
        this.$message.error("台面数请输入正确格式");
        this.$refs.tableNo_.focus();
        this.$refs.tableNo_.select();
        return;
      }
      if (this.machinedata.num_ && !x.test(this.machinedata.num_)) {
        this.$message.error("轴个数请输入正确格式");
        this.$refs.num_.focus();
        this.$refs.num_.select();
        return;
      }
      if (this.machinedata.accCount_ && !x.test(this.machinedata.accCount_)) {
        this.$message.error("允许订单数上限请输入正确格式");
        this.$refs.accCount_.focus();
        this.$refs.accCount_.select();
        return;
      }

      if (this.machinedata.display_ && !x.test(this.machinedata.display_)) {
        this.$message.error("排序请输入正确格式");
        this.$refs.display_.focus();
        this.$refs.display_.select();
        return;
      }
      inttype.forEach(key => {
        this.machinedata[key] = this.machinedata[key] ? Number(this.machinedata[key]) : 0;
      });
      this.machineVisible = false;
      updatemachineinfo(this.machinedata).then(res => {
        if (res.code) {
          this.$message.success(res.message);
          this.getMesaList();
          this.getAssignmentList(this.$refs.solderCenter.rowId);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 取消小车
    operationClick3() {
      this.btnloading7 = true;
      this.spinning = true;
      AgvCancel()
        .then(res => {
          if (res.code == 1) {
            this.$message.success("取消成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.btnloading7 = false;
          this.spinning = false;
        });
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.queryClick();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible4) {
        this.handleOk5();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
  },

  mounted() {
    this.getorderList();
    this.getdoOrderList();
    this.getMesaList();
    this.getMachineStatuList();
    window.addEventListener("resize", this.dehandleResize, true);
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
    window.removeEventListener("resize", this.dehandleResize, true);
  },
  created() {
    this.throttledHandleResize = this.throttle(this.handleResize, 200);
    this.dehandleResize = this.debounce(this.throttledHandleResize, 200);
    this.$nextTick(() => {
      this.handleResize();
    });
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.vxe-loading > .vxe-loading--chunk {
  color: #ff9900 !important;
}
/deep/.vxe-header--column {
  font-weight: 500;
}
/deep/.vxe-table--render-default {
  position: relative;
  font-size: 14px;
  color: #000000;
  font-weight: 500;
  font-family: sans-serif;
  direction: ltr;
}
/deep/.vxe-table .vxe-table--header-wrapper {
  color: #000000;
  font-family: sans-serif;
}
/deep/.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 4px;
}
/deep/.vxe-table--render-default .vxe-cell {
  padding: 0;
}
/deep/.vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default.vxe-editable .vxe-body--column,
.vxe-table--render-default .vxe-footer--column.col--ellipsis,
.vxe-table--render-default .vxe-header--column.col--ellipsis {
  height: 37px;
  .c--tooltip {
    padding: 5px;
  }
}
/deep/.vxe-table--render-default .vxe-body--row.row--current {
  background: rgb(223 220 220);
}
/deep/.vxe-table--render-default .vxe-body--row.row--hover {
  background: #dfdcdc !important;
}
/deep/.ant-input {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-select {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
  color: #000000;
}
/deep/.ant-form-item-label > label {
  color: #000000;
}

.solderManagement {
  user-select: normal;
  .content {
    display: flex;
    /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: #dfdcdc;
    }
  }
  .rowcolor {
    background: #fff9e6;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: normal;
  }
  .footer {
    .actionBox {
      height: 46px;
      overflow: hidden;
      width: 100%;
      border: 2px solid #e9e9f0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      form {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
  /deep/ .ant-card {
    .ant-card-head {
      min-height: 0;
      padding: 0;
      .ant-card-head-title {
        padding: 0;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-weight: 500;
      }
    }
    .ant-card-body {
      padding: 0;
      .clickRowSty2 {
        background: #aba5a5;
      }
      .bacStyle {
        background: #c9c9c9;
      }
    }
  }
  .left {
    /deep/ .ant-table-body {
      .ant-table-tbody {
        .bacStyle.ant-table-row-hover {
          td {
            background: #dfdcdc;
          }
        }
        .ant-table-row-hover {
          td {
            background: #dfdcdc;
          }
        }
      }
    }
    /deep/ .ant-table-fixed {
      .ant-table-tbody {
        .bacStyle.ant-table-row-hover {
          td {
            background: #dfdcdc;
          }
        }
        .ant-table-row {
          .orderClass {
            user-select: all;
          }
        }
        .ant-table-row-hover {
          td {
            background: #dfdcdc;
          }
        }
        // .ant-table-row-selected{
        //   td{
        //     background-color:#595959 !important;
        //   }
        // }
      }
    }
    position: relative;
    .touch-div {
      position: absolute;
      top: 0;
      height: 100%;
      left: 100%;
      width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: col-resize;
      span {
        width: 2px;
        background: #bbb;
        margin: 0 2px;
        height: 15px;
      }
    }
    /deep/ .tabRightClikBox {
      width: 80px;
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        padding: 0 15px;
        text-align: center;
        border-bottom: 1px solid rgb(225, 223, 223);
        background-color: white !important;
        color: #000000;
      }
      // .ant-menu-item:not(:last-child){
      //   margin-bottom: 4px;
      // }
    }
  }
  /deep/ .ant-table {
    .ant-table-selection-col {
      width: 30px;
    }
  }
  background: #ffffff;
  .minClass {
    /deep/ .ant-table-body {
      min-height: 355px;
    }
  }
  /deep/ .ant-table-wrapper {
    .ant-table-thead {
      tr {
        th {
          padding: 5px 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 5px;
        }
      }
    }
  }
  /deep/ .ant-table-body {
    // &::-webkit-scrollbar {
    //   //整体样式
    //   width: 6px; //y轴滚动条粗细
    //   height: 6px;
    // }

    // &::-webkit-scrollbar-thumb {
    //   //滑动滑块条样式
    //   border-radius: 2px;
    //   -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
    //   background: #ff9900;
    //   // #fff9e6
    // }
    .ant-table-thead {
      tr {
        th {
          padding: 0;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          padding: 5px 0;
        }
      }
    }
  }
}
</style>
