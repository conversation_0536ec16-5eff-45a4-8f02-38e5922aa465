import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'

const getPdf = async (dom, title) => {
    try {
        const el = document.getElementById(dom)
        const c = document.createElement('canvas')
        const opts = {
            scale: 3,
            canvas: c,
            allowTaint: false,
            logging: true,
            useCORS: true,
            width: window.getComputedStyle(el).width.replace('px', '') * 1,
            height: window.getComputedStyle(el).height.replace('px', '') * 1
        }
        c.width = window.getComputedStyle(el).width.replace('px', '') * 3
        c.height = window.getComputedStyle(el).height.replace('px', '') * 3
        c.getContext('2d').scale(1, 1)
        const canvas = await html2Canvas(el, opts)
        const contentWidth = canvas.width
        const contentHeight = canvas.height
        const pageHeight = (contentWidth / 592.28) * 841.89
        let leftHeight = contentHeight
        let position = 0
        const imgWidth = 595.28
        const imgHeight = (592.28 / contentWidth) * contentHeight
        const pageData = await canvas.toDataURL('image/jpeg', 1.0)
        const PDF = new JsPDF('', 'pt', 'a4')
        
        if (leftHeight < pageHeight) {
            await PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
        } else {
            while (leftHeight > 0) {
                await PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
                leftHeight -= pageHeight
                position -= 841.89
                if (leftHeight > 0) {
                    await PDF.addPage()
                }
            }
        }
        const pdfBlob = await PDF.output('arraybuffer');
        return pdfBlob
    } catch (error) {
        console.error('Error generating PDF:', error)
        throw error
    }
}

export default getPdf
