//跨域代理前缀
// const API_PROXY_PREFIX='/api'
// const BASE_URL = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_API_BASE_URL : API_PROXY_PREFIX
// const BASE_URL = process.env.VUE_APP_API_BASE_URL
const BASE_API_URL = process.env.VUE_APP_API_BASE_URL
const BASE_IDS4_URL = process.env.VUE_APP_IDS4_BASE_URL
module.exports = {
  LOGIN: `${BASE_IDS4_URL}/connect/token`,
  ROUTES: `/routes`,
  ApplicationConfiguration: `/api/app/abp-application-configuration`,
  GOODS: `${BASE_API_URL}/goods`,
  GOODS_COLUMNS: `${BASE_API_URL}/columns`
}

