import { request, METHOD } from '@/utils/request'
//业务跟单列表
export async function businessverifyPageList(params) {
    return request(`/api/app/business-documentary/verify-page-list`, METHOD.GET,params)
}
//业务跟单跟进备注
export async function followremarks(params) {
    return request(`/api/app/business-documentary/follow-remarks`, METHOD.POST,params)
}
//业务跟单完结
export async function updatedocumentarystatus(id) {
    return request(`/api/app/business-documentary/update-documentary-status`, METHOD.POST,id)
}
//业务跟单日志
export async function businessdocumentary(params) {
    return request(`/api/app/business-documentary`, METHOD.GET,params)
}
//跟单员下拉
export async function checkaccounts() {
    return request(`/api/app/business-documentary/check-accounts`, METHOD.GET)
}