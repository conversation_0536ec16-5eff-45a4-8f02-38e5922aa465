<!-- 车间管理-成型管理-右边数据 -->
<template>
  <a-card :bordered="false">
    <div class="machine" style="border: 1px solid #e9e9f0">
      <div class="left" @contextmenu.prevent="rightClick($event)" style="user-select: none">
        <vxe-table
          border
          stripe
          show-overflow
          :loading="table3Loading"
          @cell-menu="cellContextMenuEvent"
          :scroll-y="{ enabled: true, gt: 20 }"
          :height="centerheight"
          :cell-style="cellStyle"
          :row-class-name="rowClassName"
          :row-config="{ isHover: true }"
          class="toptable"
          ref="xTable1"
          :menu-config="{ enabled: true }"
          :data="tableData"
        >
          <vxe-column type="seq" title="序号" width="45" align="center"></vxe-column>
          <vxe-column field="caption_" title="机台" width="95" show-overflow>
            <template #default="{ row }">
              {{ row.caption_ }}
              <a-tooltip title="该机台为故障状态" v-if="row.status_ == 1">
                <svg
                  style="position: relative; top: 5px"
                  t="1728728076066"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="4830"
                  width="20"
                  height="20"
                >
                  <path
                    d="M978.304 717.141333c0 0.170667 4.010667 20.821333 4.010667 37.589334 0 15.829333-3.626667 35.370667-4.010667 37.504-2.688 13.866667-11.690667 22.784-22.954667 22.784h-1.962666a41.728 41.728 0 0 0-42.069334 41.258666c0 5.589333 2.304 12.501333 3.626667 15.658667a28.288 28.288 0 0 1-9.856 34.645333l-0.597333 0.426667-48.469334 26.24a31.829333 31.829333 0 0 1-35.84-6.869333c-4.906667-5.248-21.888-20.437333-34.474666-20.437334-11.690667 0-28.16 13.482667-34.389334 20.053334a31.573333 31.573333 0 0 1-34.688 7.168l-47.488-25.984-0.938666-0.597334a28.416 28.416 0 0 1-9.856-34.730666c0.170667-0.298667 3.712-8.576 3.712-15.616a41.813333 41.813333 0 0 0-42.112-41.258667h-1.706667c-11.477333 0-20.608-8.96-23.296-22.784a257.962667 257.962667 0 0 1-3.968-33.706667v-7.381333c0.512-14.677333 3.413333-30.805333 3.968-33.92 2.688-13.866667 11.690667-22.826667 22.954667-22.826667h1.962666a41.770667 41.770667 0 0 0 42.112-41.258666c0-7.125333-3.584-15.274667-3.712-15.616a28.288 28.288 0 0 1 9.898667-34.645334l0.682667-0.512 49.28-26.538666a30.634667 30.634667 0 0 1 10.965333-2.730667h3.370667c8.405333 0.426667 16.128 3.882667 21.333333 9.258667 4.864 4.992 21.546667 19.242667 33.834667 19.242666 12.16 0 28.714667-13.994667 33.578666-18.944a32.213333 32.213333 0 0 1 34.474667-6.741333l48.426667 26.24 0.938666 0.682667c11.136 7.594667 15.36 22.528 9.856 34.645333 0 0.042667-3.626667 8.576-3.626666 15.616 0 22.784 18.858667 41.258667 42.069333 41.258667h1.664c11.477333 0.042667 20.608 9.002667 23.296 22.826666z m-195.242667-28.16c-37.76 0-68.48 29.994667-68.48 66.901334s30.72 66.858667 68.48 66.858666c37.802667 0 68.522667-29.952 68.522667-66.858666s-30.762667-66.901333-68.522667-66.901334z m-13.824-212.736a292.522667 292.522667 0 0 0-89.301333 36.096c-83.242667-139.648-172.373333-289.152-179.2-299.904-11.861333-19.626667-12.373333-19.114667-24.234667 1.024-11.818667 20.096-304.384 516.992-319.36 541.226667-6.698667 11.349333-8.234667 18.048 6.698667 18.048h374.741333c1.024 36.778667 8.746667 71.850667 22.058667 104.106667H84.565333c-32.256 0-56.32-8.789333-63.36-22.869334-8.789333-18.773333-7.04-43.392 12.885334-76.842666 21.717333-36.352 377.173333-646.997333 388.906666-665.728 17.578667-29.909333 42.837333-49.877333 66.261334-49.877334 23.466667 0 46.933333 23.466667 61.013333 46.336l218.965333 368.384z m-287.829333 220.885334c-27.349333 0-46.890667-19.541333-46.890667-46.890667 0-13.696 3.925333-25.386667 13.696-33.237333 7.808-7.808 19.541333-13.696 33.237334-13.696s25.386667 3.925333 33.237333 13.696c7.808 7.808 13.696 19.541333 13.696 33.237333-2.048 27.349333-19.626667 46.890667-46.976 46.890667z m-35.157333-110.464l-11.733334-211.968h93.781334l-11.733334 211.968h-70.314666z"
                    fill="#d81e06"
                    p-id="4831"
                  ></path>
                </svg>
              </a-tooltip>
            </template>
          </vxe-column>
          <vxe-column field="numMachine_" title="资料" width="50" show-overflow></vxe-column>
          <vxe-column field="listParams_" title="负责人" width="55" show-overflow></vxe-column>
          <vxe-column field="listName_" title="工厂" width="63" show-overflow></vxe-column>
          <vxe-column field="screenText" title="屏幕显示" width="200" show-overflow></vxe-column>
          <vxe-column field="nowPdc" title="当前加工型号" width="300" show-overflow></vxe-column>
        </vxe-table>
      </div>
      <div class="right" style="border: 1px solid #e9e9f0">
        <div class="top" style="border-bottom: 3px solid #e9e9f0">
          <vxe-table
            border
            stripe
            show-overflow
            :loading="machineStatuLoad"
            :scroll-y="{ enabled: true, gt: 20 }"
            :height="right1height"
            :row-config="{ isHover: true }"
            :menu-config="{ enabled: true }"
            :data="machineStatuList"
          >
            <vxe-column field="state_" title="状态" width="60" show-overflow></vxe-column>
            <vxe-column field="muCount_" title="机台数" width="55" show-overflow></vxe-column>
            <vxe-column field="area_" title="面积㎡" width="55" show-overflow></vxe-column>
            <vxe-column field="pnL_" title="PNL数" width="65" show-overflow></vxe-column>
            <vxe-column field="count_" title="款数" width="55" show-overflow></vxe-column>
          </vxe-table>
        </div>
        <div class="bot">
          <vxe-table
            v-if="showAssign"
            border
            stripe
            show-overflow
            :loading="dispatchLoad"
            :scroll-y="{ enabled: true, gt: 20 }"
            :height="right2height"
            @cell-click="cellClickEvent"
            :row-config="{ isCurrent: true, isHover: true }"
            :menu-config="{ enabled: true }"
            :data="dispatchList"
          >
            <vxe-column field="pdctno_" title="本厂编码" width="115" show-overflow></vxe-column>
            <vxe-column field="pnlQty_" title="Pnl数" width="55" show-overflow>
              <template #default="{ row }">
                <a-input v-model="row.pnlQty_" @blur="savePnl(row)" style="width: 48px; padding: 0" />
              </template>
            </vxe-column>
            <vxe-column field="sendDate_" title="分派时间" width="93" show-overflow></vxe-column>
            <vxe-column field="back" title="操作" width="37" show-overflow align="center">
              <template #default="{ row }">
                <a-tooltip title="回退订单">
                  <a-icon
                    type="rollback"
                    @click="onSelectChange1(row)"
                    v-if="checkPermission('MES.ProductionModule.FlyingNeedle.EtHoleYcRegister')"
                    style="color: #ff9900"
                  ></a-icon>
                </a-tooltip>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import { signMachineStatus, updatePnl } from "@/services/management";
import { checkPermission } from "@/utils/abp";
export default {
  name: "CuttingCenter",
  props: {
    tableData: {
      type: Array,
    },
    table3Loading: {
      type: Boolean,
    },
    machineStatuList: {
      type: Array,
    },
    machineStatuLoad: {
      type: Boolean,
    },
    drillLoad: {
      type: Boolean,
    },
    dispatchList: {
      type: Array,
    },
    dispatchLoad: {
      type: Boolean,
    },
    idList: {
      type: Array,
    },
  },
  data() {
    return {
      centerheight: "",
      right1height: "",
      right2height: "",
      pnlnum: "",
      Listdata: {},
      startIndex: -1,
      selectedRowsData: {},
      shiftKey: false,
      isDragging: false,
      record: {},
      showAssign: false,
      selectedRowKeys: [],
      selectedRowKeys1: [],
      selectedRowKeys2: [],
      rowId: "",
      rowId1: "",
      menuStyle: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      },
      menuVisible: false,
      menuData: [],
      statusText: "标记故障",
    };
  },
  mounted() {
    window.addEventListener("keydown", this.keydown);
    window.addEventListener("keyup", this.keyup);
    this.$nextTick(() => {
      let tbody = this.$refs.xTable1.$el.querySelector("table tbody");
      setTimeout(() => {
        var toptable = document.getElementsByClassName("toptable")[0].children[1].children[1].children[0].children[1].children[1].children[1];
        if (toptable) {
          toptable.addEventListener("mousedown", this.tbodymousedown);
          toptable.addEventListener("mouseup", this.tbodymouseup);
          toptable.addEventListener("mousemove", this.tbodymousemove);
        }
      }, 500);
      if (tbody) {
        tbody.addEventListener("mousedown", this.tbodymousedown);
        tbody.addEventListener("mouseup", this.tbodymouseup);
        tbody.addEventListener("mousemove", this.tbodymousemove);
      }
    });
    window.addEventListener("resize", this.handleResize, true);
  },
  created() {
    this.$nextTick(() => {
      this.handleResize();
    });
  },
  methods: {
    checkPermission,
    handleResize() {
      if (window.innerHeight - 138 < 780) {
        this.centerheight = window.innerHeight - 138;
      } else {
        this.centerheight = "780";
      }
      this.right1height = (window.innerHeight - 142) * 0.3;
      this.right2height = (window.innerHeight - 142) * 0.7;
    },
    cellContextMenuEvent({ row, column, $event }) {
      $event.preventDefault();
      this.menuData = row;
    },
    rowClassName(row) {
      if (row.row.iD_ && this.selectedRowKeys.includes(row.row.iD_)) {
        return "row-background";
      }
      return null;
    },
    cellStyle({ row, column }) {
      if (column.field === "caption_") {
        if (row.caption_1 == "1") {
          return {
            backgroundColor: "#595959",
          };
        } else if (this.idList.indexOf(row.iD_) != -1) {
          return {
            backgroundColor: "#ff9900",
          };
        }
      }
    },
    keyup(e) {
      this.shiftKey = e.ctrlKey;
    },
    keydown(e) {
      this.shiftKey = e.ctrlKey;
    },
    // 获取单元格位置
    getTrPosition(tr, type) {
      while (tr.tagName !== "TR") {
        tr = tr.parentElement;
      }
      let record = {};
      record = this.$refs.xTable1.getRowNode(tr);
      return {
        record,
      };
    },
    tbodymousemove(event) {
      event.stopPropagation();
      //按住鼠标左键并拖动多选
      if (this.isDragging && event.button === 0) {
        let index = this.getTrPosition(event.target.parentElement).record.index;
        this.tbodySelectedItems(this.startIndex, index);
      }
    },
    tbodymousedown(event, row) {
      event.stopPropagation();
      if (event.button === 0) {
        this.isDragging = true;
        this.showAssign = true;
        this.startIndex = this.getTrPosition(event.target.parentElement).record.index;
        this.record = this.getTrPosition(event.target.parentElement).record.item;
        this.$emit("getAssignmentList", this.record.iD_, 1);
        this.rowId = this.record.iD_;
      }
    },
    tbodymouseup(event) {
      let index = this.getTrPosition(event.target.parentElement).record.index;
      let record = this.getTrPosition(event.target.parentElement).record.item;
      this.isDragging = false;
      if (this.shiftKey && record.iD_ == this.record.iD_) {
        let rowKeys = this.selectedRowKeys;
        if (rowKeys.length > 0 && rowKeys.includes(record.iD_)) {
          rowKeys.splice(rowKeys.indexOf(record.iD_), 1);
        } else {
          rowKeys.push(record.iD_);
        }
        this.selectedRowKeys = rowKeys;
        if (this.selectedRowKeys.length == 1) {
          this.selectedRowsData = record;
        }
      } else {
        if (this.startIndex == index) {
          this.selectedRowsData = record;
          this.selectedRowKeys = [record.iD_];
        }
      }
      this.shiftKey = false;
    },
    tbodySelectedItems(startIndex, endIndex) {
      const normalizedStart = Math.min(startIndex, endIndex);
      const normalizedEnd = Math.max(startIndex, endIndex);
      const selectedRowsData = this.tableData.slice(normalizedStart, normalizedEnd + 1);
      var arr = [];
      for (var a = 0; a < selectedRowsData.length; a++) {
        arr.push(selectedRowsData[a].iD_);
      }
      var aa = new Set(this.selectedRowKeys.concat(arr));
      var arr1 = Array.from(aa);
      if (this.shiftKey) {
        this.selectedRowKeys = arr1;
      } else {
        this.selectedRowKeys = arr;
      }
      if (startIndex < endIndex) {
        this.selectedRowsData = this.tableData.filter(item => {
          return item.iD_ == this.selectedRowKeys[this.selectedRowKeys.length - 1];
        })[0];
      } else {
        this.selectedRowsData = this.tableData.filter(item => {
          return item.iD_ == this.selectedRowKeys[0];
        })[0];
      }
      if (this.selectedRowKeys.length > 1) {
        this.$emit("clearlist");
      }
      this.Listdata = {};
    },
    //分派回退
    onSelectChange1(record) {
      this.rowId1 = record.guid_;
      this.$emit("getDispatchFallback", { guid_: this.rowId1, iD_: this.rowId });
    },
    cellClickEvent({ row, column }) {
      this.pnlnum = row.pnlQty_;
      this.Listdata = row;
    },
    // 修改pnl数
    savePnl(record) {
      var x = /^[0-9]\d*$/;
      if (!x.test(record.pnlQty_)) {
        this.$message.error("pnl数输入错误,请检查");
        record.pnlQty_ = this.pnlnum;
        return;
      }
      if (record.pnlQty_ != this.pnlnum) {
        updatePnl({ qty: record.pnlQty_, id: record.guid_ }).then(res => {
          if (res.code == 1) {
            this.$message.success(res.message);
            this.pnlnum = record.pnlQty_;
          } else {
            record.pnlQty_ = this.pnlnum;
            this.$message.error(res.message);
          }
        });
      }
    },
    // 右键事件
    rightClick(e) {
      if (this.menuData.caption_1 == 1) {
        this.statusText = "取消故障";
      } else {
        this.statusText = "标记故障";
      }
      this.menuVisible = true;
      this.menuStyle.top = e.clientY - 110 + "px";
      this.menuStyle.left =
        e.clientX - document.getElementsByClassName("fixed-side")[0].offsetWidth - document.getElementsByClassName("left")[0].offsetWidth + "px";
      document.body.addEventListener("click", this.bodyClick);
    },
    bodyClick() {
      this.menuVisible = false;
      document.body.removeEventListener("click", this.bodyClick);
    },
    // 编辑故障
    signError() {
      signMachineStatus(this.menuData.iD_).then(res => {
        if (res.code == 1) {
          this.$message.success(res.message);
        } else {
          this.$message.error(res.message);
        }
        this.$emit("getMesaList");
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.vxe-table .vxe-body--row.row-background {
  background: #dfdcdc !important;
}
/deep/ .vxe-table--render-default .vxe-body--row.row--current {
  background: rgb(223 220 220);
}
/deep/.vxe-cell {
  padding-left: 5px;
  padding-right: 0;
}
/deep/.vxe-loading > .vxe-loading--chunk {
  color: #ff9900 !important;
}
/deep/.vxe-header--column {
  font-weight: 500;
}
/deep/.vxe-table--render-default {
  position: relative;
  font-size: 14px;
  color: #000000;
  font-weight: 500;
  font-family: sans-serif;
  direction: ltr;
}
/deep/.vxe-table .vxe-table--header-wrapper {
  color: #000000;
  font-family: sans-serif;
}
/deep/.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 0;
}
/deep/.vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default.vxe-editable .vxe-body--column,
.vxe-table--render-default .vxe-footer--column.col--ellipsis,
.vxe-table--render-default .vxe-header--column.col--ellipsis {
  height: 36px;
}
/deep/.vxe-table--render-default .vxe-body--row.row--current {
  background: rgb(223 220 220);
}
/deep/.vxe-table--render-default .vxe-body--row.row--hover {
  background: #dfdcdc !important;
}
.machine {
  height: 100%;
  display: flex;
  .left {
    width: 50%;
    .tabRightClikBox {
      border: 2px solid rgb(238, 238, 238) !important;
      li {
        height: 30px;
        line-height: 30px;
        margin-top: 0;
        margin-bottom: 0;
        border-bottom: 1px solid rgb(225, 223, 223);
      }
    }
  }
  .right {
    width: 50%;
    .bot,
    .top {
      /deep/ .ant-table-body {
        min-height: 0;
      }
    }
    .top {
      /deep/ .ant-table-wrapper .ant-table-thead tr th {
        padding: 5px 0;
      }
    }
    button {
      padding: 0 5px;
      border-radius: 6px;
      height: 24px;
      line-height: 24px;
      background-color: #ff9900;
      color: white;
    }
  }
  /deep/ .ant-table-tbody {
    .ant-table-row {
      td:first-child {
        user-select: all;
      }
    }
    .ant-input {
      padding: 0;
      border: 0;
      border-radius: 0;
      margin: -5px 0;
      text-align: center;
    }
  }
}
</style>
