<!-- 工程管理 - 工程制作 -日志弹窗-->
<template>
  <div class='viewLog'>
    <a-empty v-if="!viewLogData.length>0"  /> 
    <a-steps progress-dot  direction="vertical" v-else>
        <a-step :title="item.createTime" :description='item.content' v-for="(item,index) in viewLogData" :key="index" >        
        </a-step>
    </a-steps>
  </div>
</template>

<script>

export default{
    name: "viewLogInfo",
    props:['viewLogData'],
    data(){
        return{

        }
    },
    created(){
       
    },
    methods:{
    }

}
</script>
<style scoped lang='less'>
.viewLog{
    // padding-left:50px;
 /deep/ .ant-steps-item-content{
     .ant-steps-item-title{
         display: block;
     }
    //  .ant-steps-item-description{
    //      display:inline-block;
    //  }
        margin-left:10px;
        width:100%;
    }
    max-height:500px;
    overflow-y:scroll;
}
</style>
