import { request, METHOD } from "@/utils/request";

//  审核分页接口
export async function verifyPageList(params) {
  return request(`/api/app/pcb-order-list/verify-page-list`, METHOD.GET, params);
}
//销售合同删除接口
export async function deletecontractno(Id) {
  return request(`/api/app/order-check/delete-contract-no/${Id}`, METHOD.POST);
}
//列表接口
export async function emSPcborderlog(id) {
  return request(`/api/app/e-mSPcb-order-log?OrderId=${id}`, METHOD.GET);
}
// 多套
export async function result4ParameterList(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/price-calc-result4Parameter-list/${Id}`, METHOD.GET);
}
// 价格结果
export async function resultList(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc-result-list/${Id}`, METHOD.GET);
}
// 价格明细
export async function resultDetailList(Id, ID) {
  return request(
    `/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc-result-detail-list?Guid4Parameter=${Id}&CalcNameID=${ID}`,
    METHOD.GET
  );
}
//  审核开始
export async function verifyStartOrder(id) {
  return request(`/api/app/verify-finished/${id}/verify-start-order`, METHOD.POST);
}
//  计价检查
export async function pricingInspection(id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc/${id}`, METHOD.POST);
}
//  审核完成
export async function verifyFinishedOrder(id) {
  return request(`/api/app/verify-finished/${id}/verify-finished-order-main`, METHOD.POST);
}
// BQ 跳过订单管理调用ERP客户PO检查
export async function paymodecheck(id) {
  return request(`/api/app/verify-finished/pay-mode-check/${id}`, METHOD.POST);
}
export async function settoolcheck(id) {
  return request(`/api/app/nope-button/${id}/set-tool-check`, METHOD.POST);
}
//LHDC市场报价完成调取工程自动分单接口
export async function timedautomaticsendorder(factory) {
  return request(`/api/app/engineering-make/timed-automatic-send-order?factory=${factory}`, METHOD.POST);
}
//YXD审核完成后再单独调取接口
export async function verifyFinishedOrderyXD(id) {
  return request(`/api/app/verify-finished/${id}/verify-finished-order-main-yXD`, METHOD.POST);
}
// 修改接口(审核销售信息)
export async function verifySalesInfoUpdate(params) {
  return request("/api/app/pcb-order/verify-sales-info-update", METHOD.POST, params);
}
// 计价
export async function orderPriceCalc(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc/${Id}`, METHOD.POST);
}
// 价格明细写入数据
export async function setorderpricecalcresult4Parameter(params) {
  return request("api/app/re-order/set-order-price-calc-result4Parameter", METHOD.POST, params);
}
//改价与改价完成接口
export async function verifychangeprice(Id, isBegin) {
  return request(`/api/app/verify-finished/${Id}/verify-change-price?isBegin=${isBegin}`, METHOD.POST);
}
// YXD获取加减价
export async function pricelistbypcbid(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc-result-list-by-pcb-id/${Id}`, METHOD.GET);
}
// 打印报价单
export async function quotationInfo(Id) {
  return request(`/api/app/pcb-order-quotation/quotation-info/${Id}`, METHOD.POST);
}
//报价合同
export async function getcontractinfobygrp(params) {
  return request("/api/app/pcb-order-contract/get-contract-info-by-grp", METHOD.POST, params);
}

//订单删除
export function orderverifydelete(Ids) {
  return request(`/api/app/verify-finished/order-verify-delete`, METHOD.POST, Ids);
}
//ECN信息
export async function pcbnopeinfo(id) {
  return request(`/api/app/order-pre/${id}/pcb-nope-info`, METHOD.GET, id);
}
export async function contractNo(params) {
  return request("/api/app/order-check/contract-no", METHOD.POST, params);
}
//更改单
export async function getnOPEAlterreport(Ids, params) {
  return request(`/api/app/pcb-order-contract-review/n-oPEAlter-report?Ids=${Ids}`, METHOD.POST);
}
//返单
export async function pcbordertonewtest(params) {
  return request(`/api/app/nope-button/pcborder-to-new-from-eMS`, METHOD.POST, params);
}
//返单更新 2023/12/16
export async function pcbordertonewtest2(params) {
  return request(`/api/app/nope-button/pcborder-to-new-search-info`, METHOD.POST, params);
}
//获取返单信息 2025/6/26 YXD/LT
export async function pcbordertonewsearchinfokc(params) {
  return request(`api/app/nope-button/pcborder-to-new-search-info-kc`, METHOD.POST, params);
}
export async function pcbordertonewtest3(params) {
  return request(`/api/app/nope-button/pcborder-to-new-back-info`, METHOD.POST, params);
}
export async function preaddnopenew(params) {
  return request(`/api/app/nope-button/pre-add-nope-new`, METHOD.POST, params);
}
//YXD删除已接入返单
export async function nopeorderdelete(Id) {
  return request(`api/app/nope-button/nope-order-delete/${Id}`, METHOD.POST);
}
export async function preaddnopealternew(params) {
  return request(`/api/app/nope-button/pre-add-nope-alter-new`, METHOD.POST, params);
}
export async function pcbordertest(params) {
  return request(`/api/app/nope-button/pcb-order-from-eRP`, METHOD.POST, params);
}
//YXD获取已接入订单列表
export async function reorderpriceinfo(Id) {
  return request(`/api/app/re-order/re-order-price-info/${Id}`, METHOD.GET);
}
//YXD接入订单更改数量、单价、平米价
export async function setreorderpriceinfo(params) {
  return request(`/api/app/re-order/set-re-order-price-info`, METHOD.POST, params);
}
//返单确定以及完成调取按钮检查
export async function nopeprecheck(params) {
  return request(`/api/app/nope-button/nope-pre-check`, METHOD.POST, params);
}
//生产通知单
export async function noticereviewinfo(Id, mktorppe) {
  return request(`/api/app/pcb-order-contract-review/notice-review-info?Ids=${Id}&mktorppe=${mktorppe}`, METHOD.GET);
}
// 接口直接下载
export async function quotationInfo1(Id, language, params) {
  return request(`/api/app/pcb-order/quotation-info-by-grp/${Id}`, METHOD.GET, params, { responseType: "blob" });
}
export async function getQuotationInfoByGrp(params) {
  return request("/api/app/order-check/get-quotation-info-by-grp", METHOD.POST, params);
}
export async function sendcontracteMime(params) {
  return request("/api/app/e-mime/send-contract-eMime", METHOD.POST, params);
}
// 交期计算多条
export async function orderDayCalc(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-day-calc/${Id}`, METHOD.POST);
}
// 交期计算单条
export async function calacmktorderpricedaybysig(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/calac-mkt-order-price-day-by-sig/${Id}`, METHOD.POST);
}
//交期计算之后计算价格
export async function parsignorderpricecalc(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/par-sign-order-price-calc/${Id}`, METHOD.POST);
}
// 审核回退预审
export async function verifyBackToPreOrder(Id) {
  return request(`/api/app/verify-finished/${Id}/verify-back-to-pre-order`, METHOD.POST);
}
// 改价
export async function orderPriceCalc4WinForm(params) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc4Win-form`, METHOD.POST, params);
}
// 新增询价
export async function orderPriceCalcResult4Parameter(params) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc-result4Parameter`, METHOD.POST, params);
}
//yxd返单获取客户代码
export async function custgrouplist(CustNo, TradeType) {
  return request(`/api/app/order-pre-button/cust-group-list?CustNo=${CustNo}&TradeType=${TradeType}`, METHOD.GET);
}
// 删除多套
export async function deleteOrderPriceCalcResult4Parameter(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/delete-order-price-calc-result4Parameter/${Id}`, METHOD.POST);
}
// 提前天数编辑
export async function seturgentday(params) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/set-urgent-day`, METHOD.POST, params);
}
// 修改数量 /api/app/e-mSOrder-price-calc-result4Parameter/set-is-production/{Id}
export async function setNum(params) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/set-num-day-num`, METHOD.POST, params);
}
// 设置下单
export async function setIsProduction(Id) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/set-is-production/${Id}`, METHOD.POST);
}
// 设置下单后重新计算成本价
export async function orderpricecalcone(Id, Paraid) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-calc-one/${Id}?Paraid=${Paraid}`, METHOD.POST);
}
// 指示检查
export async function indicationCheck(Id, type) {
  return request(`/api/app/order-pre-button/indication-check/${Id}?type=${type}`, METHOD.POST);
}
//多个订单完成
export async function verifyfinishorders(Id) {
  return request(`/api/app/verify-finished/${Id}/verify-finish-orders`, METHOD.POST);
}
// 上传合同
export function contractFile(Id, params) {
  return request(`/api/app/pcb-order-contract/up-load-contract-file/${Id}`, METHOD.POST, params);
}
export function contractReviewInfo(Id, mktorppe, params) {
  return request(`/api/app/pcb-order-contract-review/contract-review-info?Ids=${Id}&mktorppe=${mktorppe}`, METHOD.GET, params);
}
export function verifyQuotation(params) {
  return request(`/api/app/order-check/verify-quotation`, METHOD.POST, params);
}
//销售合同
export function frontcontractreport(params) {
  return request(`/api/app/order-check/front-contract-report`, METHOD.POST, params);
}
//生产通知单
export function productionreport(Id, mktorppe) {
  return request(`api/app/pcb-order/production-report?Ids=${Id}&mktorppe=${mktorppe}`, METHOD.GET);
}
//评审单
export function orderproductionreport(Id, mktorppe) {
  return request(`/api/app/pcb-order-contract-review/order-production-report?Ids=${Id}&mktorppe=${mktorppe}`, METHOD.GET);
}
//报价表单
export function orderformreport(params) {
  return request(`api/app/pcb-order/order-form-report`, METHOD.POST, params);
}
export function quotationmodel(Id) {
  return request(`/api/app/pcb-order-quotation/quotation-model/${Id}`, METHOD.GET);
}
export function ltQuotationEXLE(params) {
  return request(`/api/app/pcb-order-contract/l-t_Quotation_EXLE`, METHOD.POST, params);
}
export function yxDQuotationEXLE(params) {
  return request(`/api/app/pcb-order-contract/y-xDQuotation_EXLE`, METHOD.POST, params);
}
export function yxDOrderpriceEXLE(params) {
  return request(`/api/app/pcb-order-contract/y-xDOrder-price_EXLE`, METHOD.POST, params);
}
//查询获取下拉
export function checkuserlist() {
  return request(`/api/app/pcb-order-list/check-user-list`, METHOD.GET);
}
//jz评审单
export function reviewreport(id) {
  return request(`api/app/pcb-order/review-report/${id}`, METHOD.GET);
}
export function jz806EXLE(params) {
  return request(`/api/app/pcb-order-contract/j-z_806_EXLE`, METHOD.POST, params);
}
export function jz862EXLE(params) {
  return request(`/api/app/pcb-order-contract/j-z_862_EXLE`, METHOD.POST, params);
}
//LT成本分析
export function ltCostEXLE(params) {
  return request(`/api/app/pcb-order-contract/l-t_Cost_EXLE`, METHOD.POST, params);
}
//报价审批成本分析
export function ltCostEXLEV2(params) {
  return request(`/api/app/pcb-order-contract/l-t_Cost_EXLEV2`, METHOD.POST, params);
}
// export function orderRecovery (id) {
//     return request(`/api/app/pcb-order/${id}/order-recovery`, METHOD.POST,)
// }
//订单恢复可多选
export function orderrecoveryduoxuan(ids) {
  return request(`/api/app/verify-finished/order-recovery-duo-xuan`, METHOD.POST, ids);
}
export default {
  // getOrderInfo,
};
//汇率设置获取信息
export function orderpricehL() {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/order-price-hL`, METHOD.GET);
}
//汇率修改信息
export function setorderpricehL(params) {
  return request(`/api/app/e-mSOrder-price-calc-result4Parameter/set-order-price-hL`, METHOD.POST, params);
}

//市场批量修改客户合同号
export function updatebatchcustpo(Id, CustPo) {
  return request(`/api/app/verify-finished/${Id}/update-batch-cust-po?CustPo=${CustPo}`, METHOD.POST);
}
//龙腾报价审批成分分析表
export function ltCostAnalysisTable(id) {
  return request(`api/app/pcb-order-contract/l-t_Cost_Analysis_Table/${id}`, METHOD.POST);
}
