<!-- 市场管理-订单详情分片6  -->
<template>
  <div class="contentInfo" ref="SelectBox">
    <a-form-model layout="inline" id="formDataElem" v-show="editFlag">
      <a-row>
        <a-col :span="9">
          <a-form-model-item
            label="型号备注"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
            style="border-top: 1px solid #ddd; width: 100%"
            class="bbb"
          >
            <div>
              <a-textarea
                :value="copynote.note"
                @input="onTextAreaInput"
                :auto-size="{ minRows: 1 }"
                style="width: 100%; min-height: 24px; line-height: 14px; margin-bottom: 0px"
                :disabled="joinFacId == 37"
              />
            </div>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item
            label="备注确认"
            :label-col="{ span: 3 }"
            :wrapper-col="{ span: 20 }"
            style="border-top: 1px solid #ddd; width: 100%"
            class="bbb"
          >
            <div>
              <a-textarea
                :value="copynote.noteSure"
                @input="onTextAreaInput1"
                :auto-size="{ minRows: 1 }"
                style="width: 100%; min-height: 24px; line-height: 14px; margin-bottom: 0px"
              />
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
export default {
  name: "",
  props: ["editFlag", "joinFacId", "allnote", "formData"],
  components: {},
  data() {
    return {
      copynote: {},
    };
  },
  created() {},
  mounted() {},
  methods: {
    onTextAreaInput(e) {
      this.copynote.note = e.target.value;
    },
    onTextAreaInput1(e) {
      this.copynote.noteSure = e.target.value;
    },
  },
  watch: {
    editFlag: {
      handler(val) {
        if (val) {
          this.copynote = this.allnote;
        }
      },
    },
  },
};
</script>
<style scoped lang="less">
.bbb {
  /deep/.ant-form-item-label {
    width: 16.65%;
    border-left: 1px solid #ddd;
  }
  /deep/.ant-form-item-control-wrapper {
    width: 83.3%;
  }
  /deep/textarea.ant-input {
    min-height: 24px;
  }
  /deep/.ant-form-item-control {
    padding: 2px !important;
  }
  /deep/.ant-input {
    height: 24px;
  }
}
</style>
