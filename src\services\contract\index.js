import { request, METHOD } from '@/utils/request'
export async function contractList(params) {
    return request("/api/app/e-mSSupplier-module-no/contract-manage-list", METHOD.GET, params)
}
export async function contractGet(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/commite-contract-audit`, METHOD.POST)
}
export async function contractReject(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/reject-contract-audit`, METHOD.POST)
}
export async function contractPass(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/agree-contract-audit`, METHOD.POST)
}
export async function urlSave(params) {
    return request(`/api/app/e-mSSupplier-module-no/contract-url`, METHOD.PUT,params)
}
export async function urlUpload(params) {
    return request(`/api/file-management/files/supplieruploadpfile`, METHOD.POST,params)
}
export async function getLog(Id) {
    return request(`/api/app/e-mSSupplier-module-no/contract-manage-log/${Id}`, METHOD.GET)
}
export async function contractDelic(Id) {
    return request(`/api/app/e-mSSupplier-module-no/contract-base-info/${Id}`, METHOD.GET)
}
export async function contractSave(params) {
    return request(`/api/app/e-mSSupplier-module-no/contract-base-info`, METHOD.PUT,params)
}
export async function bankAdd(params) {
    return request(`/api/app/e-mSTMkt-supplier-finance`, METHOD.POST,params)
}
export async function allowAmend(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/is-allow-contract-edit`, METHOD.POST)
}
export async function Amend(params) {
    return request(`/api/app/e-mSTMkt-supplier-finance/update`, METHOD.POST,params)
}
export async function bankget(params) {
    return request(`/api/app/e-mSTMkt-supplier-finance?Pid=`+params, METHOD.GET)
}

export async function bankChaxun(id) {
    return request(`/api/app/e-mSTMkt-supplier-finance/${id}/async-by-id`, METHOD.GET)
}
export async function bankDel(id) {
    return request(`/api/app/e-mSTMkt-supplier-finance/${id}/async-by-id`, METHOD.DELETE)
}
export async function uploadCon(id) {
    return request(`/api/app/e-mSSupplier-module-no/${id}/download-contract`, METHOD.POST)
}

export async function contractData(params) {
    return request("/api/app/e-mSTMkt-contract-manage/contract-manage-list", METHOD.GET, params)
}

export async function contractAdd(params) {
    return request("/api/app/e-mSTMkt-contract-manage", METHOD.POST, params)
}
export async function contractUpload(params) {
    return request("/api/file-management/files/supplieruploadpfile", METHOD.POST, params)
}
export async function contractName(params) {
    return request("/api/app/e-mSTMkt-contract-manage/supplier-name", METHOD.GET, params)
}
export async function contractReview(id) {
    return request(`/api/app/e-mSTMkt-contract-manage/${id}/examine`, METHOD.POST)
}
export async function contractSet(id) {
    return request(`/api/app/e-mSTMkt-contract-manage/${id}/contract-cancel`, METHOD.POST)
}
export async function contractCX(id) {
    return request(`/api/app/e-mSTMkt-contract-manage/${id}/async-by-id`, METHOD.GET)
}
export async function contractXG(params) {
    return request(`/api/app/e-mSTMkt-contract-manage`, METHOD.PUT,params)
}
export async function logData(Id) {
    return request(`/api/app/e-mSSupplier-module-no/contract-manage-log/${Id}`, METHOD.GET,)
}
// 同步ERP付款时间
export async function sync(Id) {
    return request(`/api/app/e-mSTMkt-contract-manage/${Id}/contract-payment-day`, METHOD.POST,)
}

export  default {
    contractList,
    contractGet,
    contractReject,
    contractPass,
    urlSave,
    urlUpload,
    getLog,
    contractDelic,
    contractSave,
    bankAdd,
    allowAmend,
    Amend,
    bankget,
    bankChaxun,
    bankDel,
    uploadCon,
    contractData,
    contractAdd,
    contractUpload,
    contractName,
    contractReview,
    contractSet,
    contractCX,
    contractXG,
    logData
}
