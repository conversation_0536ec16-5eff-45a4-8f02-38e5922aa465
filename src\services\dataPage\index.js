import { request, METHOD } from '@/utils/request'
// 获取当月制作管理分析总表数据
export async function costAnalysis(params) {
    return request(`/api/app/cost-analysis/summary-of-cost-analysis?date=${params}`, METHOD.GET, )
}
// 获取当月外接CAM分析数据
export async function camAnalysis(params,enddate) {
    return request(`/api/app/cost-analysis/external-cam-analysis?date=${params}&enddate=${enddate}`, METHOD.GET, )
}
// 获取当月人工成本分析数据
export async function laborCostAnalysis(params,enddate,sacteam) {
    return request(`/api/app/cost-analysis/labor-cost-analysis?date=${params}&enddate=${enddate}&sacteam=${sacteam}`, METHOD.GET, )
}
// 获取工厂当月外接订单详情
export async function externalCamAnalysis(factoryId,params) {
    return request(`/api/app/cost-analysis/external-cam-analysis-by-factory-id/${factoryId}?date=${params}`, METHOD.GET, )
}

// 获取品类工效(日报表)
export async function externalCamAnalysis1(params,factoryid) {
    return request(`/api/app/cost-analysis/external-cam-analysis-by-factory-id-new-v2?date=${params}&factoryid=${factoryid}`, METHOD.GET,)
}
// 获取品类工效(周报表)
export async function externalCamAnalysisweek(params,factoryid) {
    return request(`/api/app/cost-analysis/departmental-efficiency-week?date=${params}&factoryid=${factoryid}`, METHOD.GET,)
}
// 获取品类工效(月报表)
export async function externalCamAnalysismonth(params,factoryid) {
    return request(`/api/app/cost-analysis/departmental-efficiency-month?date=${params}&factoryid=${factoryid}`, METHOD.GET,)
}
// 获取个人功效详情工程
export async function externalCamAnalysis2(params) {
    return request(`/api/app/cost-analysis/cam-artificial-efficiency2`, METHOD.GET,params)
}
// 获取QAE工效
export async function externalQaeAnalysis2(params) {
    return request(`/api/app/cost-analysis/qae-artificial-efficiency2`, METHOD.GET,params)
}
// 部门统计
export async function departordertotalanalys(params) {
    return request(`api/app/cost-analysis/depart-order-total-analys`, METHOD.GET,params)
}
// 获取个人功效详情市场预审
export async function preartificialefficiency2(params) {
    return request(`/api/app/cost-analysis/pre-artificial-efficiency2`, METHOD.GET,params)
}
// 获取个人功效详情市场报价
export async function preartificialefficiency3(params) {
    return request(`/api/app/cost-analysis/pre-artificial-efficiency3`, METHOD.GET,params)
}
export async function orderDataStatisticsV2() {
    return request(`/api/app/analysis/order-data-statistics-v2`, METHOD.GET,)
}
// 授权工厂下拉
export async function factroyList() {
    return request(`/api/app/cost-analysis/factroy-list`, METHOD.GET,)
}
export default {
    costAnalysis,
    camAnalysis,
    laborCostAnalysis,
    externalCamAnalysis,
    externalCamAnalysis1,
    externalCamAnalysisweek,
    externalCamAnalysis2,
    preartificialefficiency2,
    preartificialefficiency3,
    factroyList,
    departordertotalanalys,
}
