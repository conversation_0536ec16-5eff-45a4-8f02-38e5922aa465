import { request, METHOD } from "@/utils/request";
// 工程QAE订单列表
export function projectQaeOrderList(params) {
  return request("/api/app/pro-order-list/engineering-qae-list", METHOD.GET, params);
}
// 工程QAE人员清单
export function projectBackEndPeopleList() {
  return request("/api/app/engineering-qae-basic/engineering-qae-user-list", METHOD.GET);
}
// 获取订单参数
export function projectBackEndOrderDetail(Id) {
  return request(`/api/app/engineering-qae-basic/pro-order-par/${Id}`, METHOD.GET);
}

// 获取参数信息
export function projectBackEndJobInfo(Id) {
  return request(`/api/app/engineering-qae/make-module-no-par/${Id}`, METHOD.GET);
}
// 获取注意事项信息（作业记录）
export function informationTransfer(id) {
  return request(`/api/app/engineering-backend/information-transfer/${id}`, METHOD.GET);
}
// 人员对应订单
export function peopleOrderList(params) {
  return request("/api/app/engineering-qae-basic/engineering-qae-user-order-list", METHOD.GET, params);
}

// 订单分派
export function projectBackEndAssign(params) {
  return request("/api/app/engineering-qae-basic/send-order", METHOD.POST, params);
}

// 订单分派回退
export function projectBackEndAssignBack(Id) {
  return request(`/api/app/engineering-qae-basic/back-send/${Id}`, METHOD.POST);
}

// 返修开始
export function RepairStart(params) {
  return request(`/api/app/engineering-qae/fix-start`, METHOD.POST, params);
}
// 审核完成
export function qaeFinish(Id, IsQae) {
  return request(`/api/app/engineering-qae/finish/${Id}?IsQAE2=${IsQae}`, METHOD.POST);
}
//是否二审
export function isfinishqaeenble(Id) {
  return request(`api/app/engineering-qae/is-finish-qae-enble/${Id}`, METHOD.GET);
}
// 审核开始
export function qaeStart(Id) {
  return request(`/api/app/engineering-qae/qae-start/${Id}`, METHOD.POST);
}
// 二审完成
export function qaeFinish2(Id) {
  return request(`/api/app/engineering-qae/finish2/${Id}`, METHOD.POST);
}

// 审核回退
export function qaeBackOrder(Id) {
  return request(`/api/app/engineering-qae/qae-back-order/${Id}`, METHOD.POST);
}
// 回退cam
export function qaeBack2Cam(Id) {
  return request(`/api/app/engineering-qae/qae-back2Cam/${Id}`, METHOD.POST);
}
// 取单
export function TakeOrderList() {
  return request("/api/app/engineering-qae-basic/engineering-qae-order", METHOD.GET);
}
// 获取取单设置
export function orderRetrievalSettings(params) {
  return request(`/api/app/e-mSTSys-user-assignment-qae/order-retrieval-settings?userid=${params}`, METHOD.GET);
}
// 取单设置
export function RetrievalSettings(params) {
  return request(`/api/app/e-mSTSys-user-assignment-qae/order-retrieval-settings`, METHOD.POST, params);
}
// 获取今日做单情况
export async function makeInfo() {
  return request(`/api/app/engineering-qae-basic/make-info`, METHOD.GET);
}
// 返单复制新单数据
export async function EngineeringQaeCopyNewOrder(id) {
  return request(`/api/app/engineering-qae-copy-new-order/re-order-copy-new-order/${id}`, METHOD.POST);
}

export default {
  projectQaeOrderList,
  projectBackEndOrderDetail,
  projectBackEndPeopleList,
  projectBackEndJobInfo,
  peopleOrderList,
  projectBackEndAssign,
  projectBackEndAssignBack,
  RepairStart,
  qaeFinish,
  qaeFinish2,
  informationTransfer,
  qaeBackOrder,
  qaeBack2Cam,
  TakeOrderList,
  orderRetrievalSettings,
  RetrievalSettings,
  makeInfo,
  qaeStart,
  EngineeringQaeCopyNewOrder,
};
