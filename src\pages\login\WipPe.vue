<!-- wippe -->
<template>
    <div class="wippe">
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <div style="height: 10%;">
            <div style="display: flex;">
                <div style="font-size: 14px;width: 23%;position: relative;top:-4px;padding-left: 6px;">
                    客编:{{ custno }}<br/>
                    在线:{{ wipdata.length }}
                </div>
                <a-input v-model="params.customerPartNo"  placeholder="可输入你需要搜索的型号" ></a-input>
                <img src="@/assets/icon/sousuo.png" style="height: 22px;position: relative;left: 1%;z-index: 99;top:6px;" @click="searchclick" >
                <a-select 
                @change="changedepart" 
                v-model="params.departno" >
                <a-select-option :title="item.lable" v-for="(item,index) in list" :key="index" :value="item.value" :lable="item.text" >
                    {{ item.text }}
                </a-select-option>
                </a-select>
            </div>
            <a-divider></a-divider>
        </div>       
        <div style="height: 80%;overflow: auto;">
            <a-list
            size="small"
            :loading="loading"
            :itemKey="(record,index)=>{return index}"
            :pagination="false"
            @change="listhandleTableChange"
            :itemLayout="'vertical'"
            :data-source="wipdata">
            <template>
                    <a-list-item v-for=" (item,index) in wipdata" :key="index+1">
                        <div class="list-item-content">
                            <a-list-item-meta :title="getTitle(item)" :description="getdescription(item)"/>
                        </div>
                    </a-list-item>
                </template>
        </a-list>
        </div>      
    </div>
</template>
<script>
import {wiporderlist,openidcode} from "@/services/user";
export default {
    name:'',
    components: {},
    data(){ 
        return{
            list:[
                {value:'',text:'全部'},
                {value:'mkt',text:'下单'},
                {value:'ppe',text:'工程'},
                {value:'pro',text:'生产'},
            ],
            wipdata:[],
            params:{
                customerPartNo:'',
                departno:"",
            },
           // {proOrderNo:'12345678910',currentProcess:'诚通伴是我',overSequenceTime:'2024-04-08 10:05:00',isExtremeJiaji:true,isJiaji:false},
            openid:'',
            custno:'',
            loading:false,            
            heighty:window.innerHeight-180,
            listpagination: {
                simple:true,
                current: 1,
                pageSize: 10,
                onChange: this.listhandleTableChange,
                onShowSizeChange: this.listhandleTableChange,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: ["10"],//每页中显示的数据
                showTotal: (total) => `总计 ${total} 条`,
            },
        }
    },
    computed: {

    },
    created(){
       
    },
    mounted(){
        let params = {
            'wxcode':this.$route.query.code,
            'factoryId':37
        }
        openidcode(params).then(res=>{
            if(res.code){
                this.openid = res.data[0].text
                this.custno = res.data[0].valueMember
                this.getwipdata()
                //alert('openid'+this.openid+'custno'+this.custno)
            }
        })
    },
   methods: {
    searchclick(){
        this.getwipdata(this.params)
    },
    changedepart(){
        this.getwipdata(this.params)
    },
    getTitle(item) {
    return (
        <div>           
            <div style="display:flex;">
                <div style="font-size:16px;font-weight:bold">{item.proOrderNo}
                    {item.isExtremeJiaji ? (
                        <span>
                        <a-icon type="thunderbolt" theme="filled" style="color:#ff9900;padding:0" ></a-icon> 
                        <a-icon  type="thunderbolt" theme="filled" style="color:#ff9900;padding:0" ></a-icon> 
                        </span> ) : null}
                    {item.isJiaji && !item.isExtremeJiaji ? (
                    <span>
                    <a-icon type="thunderbolt" theme="filled" style="color:#ff9900;padding:0" ></a-icon> 
                    </span> ) : null}
                </div>
                <a-icon type='right'/>
                <div style="background-color:rgb(217,2,22);border-radius:5px;padding: 0 5px;color:white;font-size:13px;">{item.currentProcess}</div>  
            </div>
        </div>
        
    );
    },
    getdescription(item) {
    return (
        <div style="font-size:13px;line-height:3.15ch">           
            <div>{item.boardThicknessStr} , 交期:{item.overSequenceTime}</div>
            <div>{item.para4DelQtyStr}</div>
            <div>客户型号：{item.customerPartNo}</div>
        </div>
        
    );
    },
    listhandleTableChange(page, pageSize) {
        if(typeof(page) == 'object'){
            this.listpagination.current = Number(page.target._value)
        }else{
            this.listpagination.current = page
        }       
        if(pageSize){
            this.listpagination.pageSize = pageSize
        }
    },
    getwipdata(data){
        let params = {
          'openid':this.openid,
          'factoryId':37,
          'PageIndex': this.listpagination.current,
          'PageSize' : this.listpagination.pageSize,             
        }  
       // alert('146')
        if(JSON.stringify(data) != '{}' && data){
            params.CustomerModel = data.customerPartNo ? data.customerPartNo : '';
            params.departno = data.departno ? data.departno : '';
        }else{
            params.CustomerModel = '';
            params.departno = '';
        }      
       // alert('型号'+params.CustomerModel+'类型'+params.departno) 
        this.loading = true
        wiporderlist(params).then(res=>{
            if(res.code){
                this.wipdata=res.data.items;
                const pagination = { ...this.listpagination };
                pagination.total = res.data.totalCount;
                this.listpagination = pagination;
            }else{
                this.$message.error(res.message);
            }
        }).finally(()=>{
            this.loading = false;
        })
    },
   }
}
</script>

<style scoped lang="less">
/deep/.ant-select{
    width: 20%;
    position: relative;
    left: 2%;
    margin-top: 2px;
}
/deep/.ant-divider-horizontal{
    margin: 11px 0;
}
&::-webkit-scrollbar {
      //整体样式
      width: 4px; //y轴滚动条粗细
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      //滑动滑块条样式
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
      background: #dbdada;
      // #fff9e6
    }
.anticon {
    padding-top: 5px;
    padding-left: 3px;
    padding-right: 5px;
}
.ant-list-split .ant-list-item {
    border: 1px solid #f0f0f0;
}
/deep/.list-item-content{
    padding-left: 6px;
}
/deep/.list-item-content{
    display: flex;
    align-items: center;
}
.ant-list-vertical .ant-list-item-meta {
     margin-bottom: 0px; 
}
.ant-list-vertical .ant-list-item-meta-title {
    margin-bottom: 3px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 24px;
}
/deep/.ant-table-pagination.ant-pagination {
    float: left;
    margin: 10px 0;
}
tr td{
    border: 1px solid rgb(0, 0, 0);
    padding: 5px;
}
tr th{
    border: 1px solid rgb(0, 0, 0);
    padding: 5px;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
      background: rgb(255, 255, 255);
}
/deep/.ant-table-wrapper{
    border:1px solid #efefef;
}
/deep/.ant-table-thead > tr > th {
    padding: 5px 4px;
    overflow-wrap: break-word;
    border-right: 1px solid #efefef;
}
/deep/.ant-table-tbody > tr > td {
    padding:5px 4px;
    overflow-wrap: break-word;
    border-right: 1px solid #efefef;
}
.wippe{
    padding: 20px 10px;
    height: 100%;
    width: 100%;
    font-size: 12px;
    color: black;
}
input{
    border-radius: 15px;
    border: none;
    background: #f8f8f8;
    height: 36px;
    width: 49%;
    position: relative;
    left: 1%;
    border: 2px solid #f8f8f8;
}
/deep/.ant-divider{
    background-color: #dadada;
}
</style>

