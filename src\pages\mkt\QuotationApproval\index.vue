<template>
  <a-spin :spinning="spinning">
    <div class="quotation-approval">
      <div class="content">
        <div class="left-content">
          <left-table
            :columns="Approvalcolumns"
            :dataSource="orderListData"
            :orderListTableLoading="orderListTableLoading"
            :rowKey="'id'"
            @tableChange="handleTableChange"
            :pagination="pagination"
            :class="orderListData.length ? 'min-table' : ''"
            ref="leftTable"
            @gettopdata="gettopdata"
            @OperationLog="OperationLog"
            @Changecolumns="Changecolumns"
          ></left-table>
        </div>
        <div class="right-content">
          <div class="toptable">
            <a-table
              :columns="topcolumns"
              :dataSource="topdata"
              :scroll="{ y: 150 }"
              :loading="loading2"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
              :customRow="onClickRow2"
              :rowClassName="isRedRow2"
            >
              <template slot="radio" slot-scope="text, record">
                <span>
                  <a-radio :checked="record.isProduction" style="height: 26px"> </a-radio>
                </span>
              </template>
            </a-table>
          </div>
          <a-modal
            title="指示检查信息"
            :visible="dataVisible2"
            @cancel="dataVisible2 = false"
            destroyOnClose
            centered
            :maskClosable="false"
            :width="600"
          >
            <template #footer>
              <a-button key="back" @click="dataVisible2 = false">取消</a-button>
              <a-button key="back1" type="primary" v-if="check" @click="continueClick">继续</a-button>
            </template>
            <div class="class" style="font-size: 16px; font-weight: 500">
              <p v-for="(item, index) in checkData" :key="index">
                <span v-if="item.error == '1'" style="color: red">
                  <a-icon type="star"></a-icon>
                  <span>{{ item.caption }}:</span>
                  <span>{{ item.result }}!</span>
                </span>
                <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
                  <a-icon type="star"></a-icon>
                  <span>{{ item.caption }}:</span>
                  <span>{{ item.result }}!</span>
                </span>
                <span v-else style="color: black">
                  <a-icon type="star"></a-icon>
                  <span>{{ item.caption }}:</span>
                  <span>{{ item.result }}!</span>
                </span>
              </p>
              <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>指示检查通过!</p>
            </div>
          </a-modal>
          <div class="centertable">
            <a-table
              :columns="centercolumns"
              :customRow="onClickRow3"
              :rowClassName="isRedRow3"
              :dataSource="centerdata"
              :loading="loading3"
              :scroll="{ y: 288 }"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
            ></a-table>
          </div>
          <div class="bottable">
            <a-table
              :columns="botcolumns"
              :dataSource="botdata"
              :scroll="{ y: 216 }"
              :loading="loading4"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
            ></a-table>
          </div>
        </div>
      </div>
      <div class="footerAction">
        <make-action
          @queryClick="queryClick"
          @approval="approval"
          @returnClick="returnClick"
          @costanalysis="costanalysis"
          @costAnalysis1="costAnalysis1"
        ></make-action>
      </div>
    </div>
    <!-- 操作日志弹窗 -->
    <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose @cancel="reportHandleCancel" :maskClosable="false" :width="800" centered>
      <div class="projectackend">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
          <template slot="addresscontent" slot-scope="record">
            <span v-if="record.content.indexOf('地址') == -1">{{ record.content }}</span>
            <span v-else>
              <span>{{ record.content.split("地址：")[0] }}地址：</span>
              <span @click.stop="ContractDownload1(record)" style="color: rgb(68, 146, 235)">{{ record.content.split("地址：")[1] }}</span>
            </span>
          </template>
        </a-table>
      </div>
      <template slot="footer">
        <a-button type="primary" @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
    <a-modal
      :visible="costVisible"
      @cancel="costVisible = false"
      @ok="costAnalysisTableOk"
      title="成本分析表"
      centered
      :width="1000"
      :destroyOnClose="true"
    >
      <template slot="footer">
        <a-button @click="costVisible = false">关闭</a-button>
      </template>
      <cost-analysis-table ref="costAnalysisTable" :costData="costData" />
    </a-modal>
    <!-- 查询弹窗 -->
    <a-modal
      title="订单查询"
      :visible="queryVisible"
      @cancel="reportHandleCancel"
      @ok="queryHandleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form>
        <a-form-item label="订单号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-input v-model="queryData.orderNo" autoFocus />
        </a-form-item>
        <a-form-item label="客户代码" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-input v-model="queryData.custNo" />
        </a-form-item>
        <a-form-item label="客户型号：" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-input v-model="queryData.PcbFileName" allowClear />
        </a-form-item>
        <a-form-item label="生产型号" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-input v-model="queryData.proOrderNo" allowClear />
        </a-form-item>
      </a-form>
    </a-modal>
    <!--确认弹窗-->
    <a-modal
      title="确认弹窗"
      :visible="confirmVisible"
      @cancel="reportHandleCancel"
      @ok="deconfirmHandleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="600"
      centered
    >
      <div v-if="ModalType == 'approval'">确认审批该订单吗?</div>
      <div v-if="ModalType == 'return'">
        <a-form-item label="*退回原因" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" class="required">
          <a-textarea v-model="return_reson" :auto-size="{ minRows: 4, maxRows: 6 }"></a-textarea>
        </a-form-item>
      </div>
    </a-modal>
  </a-spin>
</template>
<script>
const Approvalcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
    ellipsis: true,
    fixed: "left",
  },
  {
    title: "订单号",
    align: "left",
    ellipsis: true,
    width: 130,
    dataIndex: "orderNo",
    scopedSlots: { customRender: "orderNo" },
    fixed: "left",
    sorter: (a, b) => {
      return a.orderNo.localeCompare(b.orderNo);
    },
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    ellipsis: true,
    width: 65,
    sorter: (a, b) => {
      return a.custNo.localeCompare(b.custNo);
    },
  },
  {
    title: "客户型号",
    align: "left",
    ellipsis: true,
    width: 335,
    dataIndex: "customerModel",
  },
  {
    title: "订单类型",
    dataIndex: "reOrder",
    align: "left",
    ellipsis: true,
    width: 85,
    customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
    sorter: (a, b) => {
      return a.reOrder.toString().localeCompare(b.reOrder.toString());
    },
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    width: 125,
    sorter: (a, b) => {
      return a.proOrderNo.localeCompare(b.proOrderNo);
    },
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "left",
    ellipsis: true,
    width: 40,
  },
  {
    title: "SU",
    dataIndex: "su",
    align: "left",
    width: 35,
    ellipsis: true,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 120,
    ellipsis: true,
    align: "left",
  },
  {
    title: "解析",
    dataIndex: "analysisFinishStr",
    width: 55,
    ellipsis: true,
    align: "left",
  },
  {
    title: "交货日期",
    align: "left",
    dataIndex: "deliveryDate1",
    ellipsis: true,
    width: 110,
    sorter: (a, b) => {
      if (a.deliveryDate1 === null) {
        return 1;
      }
      if (b.deliveryDate1 === null) {
        return -1;
      }
      return a.deliveryDate1.localeCompare(b.deliveryDate1);
    },
  },
  {
    title: "报价人",
    dataIndex: "checkName",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "预审人",
    dataIndex: "preName",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "业务员",
    align: "left",
    dataIndex: "ywName",
    ellipsis: true,
    width: 65,
  },
  {
    title: "上传时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 150,
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "建立人",
    dataIndex: "createName",
    align: "left",
    ellipsis: true,
    width: 65,
  },
  {
    title: "合同",
    width: 220,
    align: "left",
    ellipsis: true,
    scopedSlots: { customRender: "contractFilePath" },
  },
  {
    title: "总金额",
    width: 100,
    align: "left",
    ellipsis: true,
    dataIndex: "totalAmountPrice",
  },
  {
    title: "合同币种",
    width: 70,
    align: "center",
    dataIndex: "currencyStr",
    ellipsis: true,
  },
  {
    title: "协议币种",
    width: 70,
    align: "center",
    dataIndex: "protocolCurrencyStr",
    ellipsis: true,
  },
  {
    title: "是否打印",
    width: 70,
    align: "center",
    scopedSlots: { customRender: "isQuotationForm" },
    ellipsis: true,
  },
  {
    title: "合同号",
    width: 140,
    align: "center",
    dataIndex: "contractNo",
    ellipsis: true,
  },
  {
    title: "客户订单号",
    width: 200,
    align: "left",
    dataIndex: "custPo",
    ellipsis: true,
  },
  {
    title: "客户物料号",
    width: 200,
    align: "left",
    dataIndex: "customerMaterialNo",
    ellipsis: true,
  },
  {
    title: "工厂",
    dataIndex: "contractFactoryName",
    width: 100,
    align: "center",
    ellipsis: true,
  },
  {
    title: "加工工厂",
    dataIndex: "orderDirectionStr",
    align: "center",
    width: 80,
    ellipsis: true,
  },
  {
    title: "操作",
    width: 70,
    align: "center",
    scopedSlots: { customRender: "labelUrl" },
    class: "noCopy",
  },
];
const topcolumns = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    key: "index",
    align: "center",
    width: 37,
  },
  {
    title: "下单",
    scopedSlots: { customRender: "radio" },
    width: 37,
    ellipsis: true,
    align: "center",
    className: "inputClass",
  },
  {
    title: "数量",
    dataIndex: "para4DelQty_",
    width: 50,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "提前",
    dataIndex: "para4UrgentDate_",
    width: 40,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "面积",
    dataIndex: "para4Area_",
    ellipsis: true,
    width: 55,
    align: "left",
  },
  {
    title: "交期",
    ellipsis: true,
    dataIndex: "para4IntDelivery_",
    width: 37,
    align: "left",
  },
  {
    title: "交货日期",
    dataIndex: "para4Delivery_",
    width: 110,
    className: "inputClass",
    ellipsis: true,
    align: "left",
  },
  {
    title: "单价",
    dataIndex: "pcsPrice_",
    width: 55,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "重量(KG)",
    dataIndex: "para4Weight_",
    width: 80,
    align: "left",
  },
];
const copycentercolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    dataIndex: "upOrDownPrice",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
  },
  {
    title: "实际报价",
    width: 58,
    ellipsis: true,
    align: "left",
    dataIndex: "actualPrice",
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
const copybotcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    dataIndex: "upOrDownPrice",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
  },
  {
    title: "实际报价",
    width: 58,
    ellipsis: true,
    align: "left",
    dataIndex: "actualPrice",
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
const laborcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 20,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作时间",
    align: "left",
    dataIndex: "createTime",
    width: 65,
  },
  {
    title: "操作人",
    align: "left",
    dataIndex: "userName",
    width: 30,
  },
  {
    title: "内容",
    align: "left",
    scopedSlots: { customRender: "addresscontent" },
    width: 185,
  },
];
import { informationremind, getinformationremind } from "@/services/system/InformationRemind.js";
import { mapState, mapMutations } from "vuex";
import { checkPermission } from "@/utils/abp";
import { result4ParameterList, resultList, resultDetailList, emSPcborderlog, ltCostEXLEV2, ltCostAnalysisTable } from "@/services/mkt/OrderReview.js";
import { offerratifypagelist, orderofferratify, orderofferratifyback } from "@/services/mkt/QuotationApproval.js";
import LeftTable from "@/pages/mkt/QuotationApproval/module/LeftTable";
import MakeAction from "@/pages/mkt/QuotationApproval/module/MakeAction";
import costAnalysisTable from "@/pages/mkt/QuotationApproval/module/costAnalysisTable";
import moment from "moment";
export default {
  name: "QuotationApproval",
  components: {
    LeftTable,
    MakeAction,
    costAnalysisTable,
  },
  data() {
    return {
      labordata: [],
      laborcolumns,
      labordataVisible: false,
      id: "",
      ModalType: "",
      queryData: {},
      return_reson: "",
      isCtrlPressed: false,
      queryVisible: false,
      confirmVisible: false,
      costVisible: false,
      costData: [],
      Approvalcolumns,
      topcolumns,
      centercolumns: [],
      botcolumns: [],
      copycentercolumns,
      copybotcolumns,
      topdata: [],
      centerdata: [],
      botdata: [],
      loading2: false,
      loading3: false,
      loading4: false,
      spinning: false,
      checkType: "",
      check: false,
      checkData: [],
      dataVisible2: false,
      guid4Parameter: "",
      orderListData: [],
      orderListTableLoading: false,
      pagination: {
        pageSize: 20,
        current: 1,
        total: 0,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  created() {
    this.deconfirmHandleOk = this.debounce(this.confirmHandleOk, 500);
  },
  computed: {
    ...mapState("account", ["user"]),
  },
  mounted() {
    this.getOrderList();
    this.Changecolumns(this.user.factoryId);
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  methods: {
    checkPermission,
    ...mapMutations("setting", ["setinfolength"]),
    Changecolumns(joinFactoryId) {
      if ([67, 97, 98].indexOf(joinFactoryId) != -1) {
        this.centercolumns = this.copycentercolumns.filter(ite => ite.title != "港币价");
        this.botcolumns = this.copybotcolumns.filter(ite => ite.title != "港币价");
      } else {
        this.centercolumns = this.copycentercolumns.filter(ite => ite.title != "不含税");
        this.botcolumns = this.copybotcolumns.filter(ite => ite.title != "不含税");
      }
    },
    costAnalysisTableOk() {
      this.costVisible = false;
    },
    costAnalysis1() {
      if (this.$refs.leftTable.selectedRowList.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      ltCostAnalysisTable(this.$refs.leftTable.selectedRowList[0]).then(res => {
        if (res.code) {
          this.costData = res.data;
          this.costVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    costanalysis() {
      if (this.$refs.leftTable.selectedRowList.length == 0) {
        this.$message.warning("请选择订单");
        return;
      }
      ltCostEXLEV2({ id: this.$refs.leftTable.selectedRowList }).then(res => {
        if (res.code) {
          let content = "/costanalysis?id=" + this.$refs.leftTable.selectedRowList;
          window.open(content);
          this.downcostanalysis(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downcostanalysis(url, name) {
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          const blob = xhr.response;
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = name;
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      };
      xhr.send();
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    OperationLog(record) {
      this.labordataVisible = true;
      emSPcborderlog(record.id).then(res => {
        if (res.code) {
          this.labordata = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    ContractDownload1(record) {
      if (record.content.indexOf("地址") != -1) {
        const xhr = new XMLHttpRequest();
        const queryString = record.content.split("地址：")[1];
        let a = queryString.split(".").slice(-1)[0];
        const splitArray = record.content.split("：【")[1].split("】")[0];
        xhr.open("GET", queryString, true);
        xhr.responseType = "blob";
        xhr.onload = function () {
          if (xhr.status === 200) {
            const blob = xhr.response;
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = splitArray + "." + a;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };
        xhr.send();
      }
    },
    getOrderList(queryData) {
      let params = {
        pageIndex: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      var obj = Object.assign(params, queryData);
      this.orderListTableLoading = true;
      offerratifypagelist(obj)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            this.pagination.total = res.data.totalCount;
          }
        })
        .finally(() => {
          this.orderListTableLoading = false;
        });
    },
    //获取多套价格
    gettopdata(id) {
      if (id == undefined) {
        this.topdata = [];
        this.centerdata = [];
        this.botdata = [];
        return;
      }
      this.loading2 = true;
      result4ParameterList(id)
        .then(res => {
          if (res.data.length > 1) {
            this.topdata = res.data.filter(item => item.isProduction == true);
            var index = this.topdata.findIndex(v => v.isProduction == true);
            this.id = this.topdata[index].id;
            for (var b = 0; b < this.topdata.length; b++) {
              if (this.topdata[b].para4Weight_) {
                this.topdata[b].para4Weight_ = Number(this.topdata[b].para4Weight_).toFixed(2);
              }
              if (this.topdata[b].para4Delivery_) {
                this.topdata[b].para4Delivery_ = moment(this.topdata[b].para4Delivery_).format("YYYY-MM-DD");
              } else {
                this.topdata[b].para4Delivery_ = null;
              }
            }
            this.getcenterdata(this.topdata[index]);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
    },
    // 获取价格结果列表
    getcenterdata(record) {
      this.loading3 = true;
      resultList(record.id)
        .then(res => {
          if (res.data) {
            this.centerdata = res.data;
            let index = res.data.findIndex(v => v.priceName.indexOf("平米价") != -1);
            if (this.centerdata.length > 0 && index != -1) {
              this.guid4Parameter = this.centerdata[index].guid4Parameter + this.centerdata[index].guid4Order + this.centerdata[index].guid4Calc;
              this.getbotdata(this.centerdata[index]);
            } else {
              this.botdata = [];
            }
          }
        })
        .finally(() => {
          this.loading3 = false;
        });
    },
    // 获取价格明细列表
    getbotdata(record) {
      this.loading4 = true;
      if (record.calcNameID == "1013501" || record.calcNameID == "1013510") {
        resultDetailList(record.guid4Parameter, record.calcNameID)
          .then(res => {
            if (res.data) {
              this.botdata = res.data;
            }
          })
          .finally(() => {
            this.loading4 = false;
          });
      } else {
        this.botdata = [];
        this.loading4 = false;
      }
    },
    onClickRow2(record) {
      return {
        on: {
          click: () => {
            this.id = record.id;
            this.getcenterdata(record);
          },
        },
      };
    },
    onClickRow3(record) {
      return {
        on: {
          click: () => {
            this.guid4Parameter = record.guid4Parameter + record.guid4Order + record.guid4Calc;
            this.getbotdata(record);
          },
        },
      };
    },
    // 行点击事件
    isRedRow2(record) {
      if (record.id == this.id) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    isRedRow3(record) {
      if (record.guid4Parameter + record.guid4Order + record.guid4Calc == this.guid4Parameter) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    reportHandleCancel() {
      this.queryVisible = false;
      this.confirmVisible = false;
      this.labordataVisible = false;
      this.costVisible = false;
    },
    queryHandleOk() {
      if (JSON.stringify(this.queryData) == "{}") {
        this.$message.error("请输入查询条件");
        return;
      }
      if (this.queryData.proOrderNo && typeof this.queryData.proOrderNo === "string" && this.queryData.proOrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产型号查询不能小于5位");
        return;
      }
      this.getOrderList(this.queryData);
      this.queryVisible = false;
    },
    queryClick() {
      this.queryData = {};
      this.queryVisible = true;
    },
    approval() {
      if (this.$refs.leftTable.selectedRowList.length == 0) {
        this.$message.error("请选择需要审批的订单");
        return;
      }
      if (this.$refs.leftTable.selectedRowList.length > 1) {
        this.$message.error("该操作只能选择一条订单进行审批");
        return;
      }
      this.confirmVisible = true;
      this.ModalType = "approval";
    },
    returnClick() {
      if (this.$refs.leftTable.selectedRowList.length == 0) {
        this.$message.error("请选择需要退回的订单");
        return;
      }
      if (this.$refs.leftTable.selectedRowList.length > 1) {
        this.$message.error("该操作只能选择一条订单进行退回");
        return;
      }
      this.confirmVisible = true;
      this.ModalType = "return";
      this.return_reson = ""; //清空退回原因
    },
    confirmHandleOk() {
      if (this.ModalType == "approval") {
        orderofferratify({ ids: this.$refs.leftTable.selectedRowList }).then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.getOrderList();
          } else {
            let params = {
              title: "审批失败",
              content: `${this.$refs.leftTable.selectedRowsData.orderNo}审批完成失败,原因：${res.message}`,
              userName: this.user.userName,
            };
            if (res.message != "有检查项") {
              informationremind(params).then(res => {
                if (res.code) {
                  getinformationremind().then(res => {
                    if (res.code) {
                      this.setinfolength(res.data.length);
                      localStorage.setItem("infolength", res.data.length);
                    }
                  });
                }
              });
            }
            if (res.data && res.data.length) {
              this.checkData = res.data;
              this.check = this.checkData.findIndex(v => v.error == "1") < 0;
              this.dataVisible2 = true;
              this.checkType = "ddsp";
            } else {
              this.$message.error(this.$refs.leftTable.selectedRowsData.orderNo + res.message);
            }
          }
        });
      }
      if (this.ModalType == "return") {
        if (!this.return_reson) {
          this.$message.error("请填写退回原因");
          return;
        }
        orderofferratifyback({ id: this.$refs.leftTable.selectedRowList[0], remarks: this.return_reson }).then(res => {
          if (res.code) {
            this.$message.success(res.message);
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        });
      }
      this.confirmVisible = false;
    },
    continueClick() {
      if (this.checkType == "ddsp") {
        this.ModalType = "approval";
        this.confirmHandleOk();
      }
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed && checkPermission("MES.MarketModule.OfferRatify.OfferRatifySearch")) {
        e.preventDefault();
        this.queryClick();
        this.reportHandleCancel();
        this.queryVisible = true;
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.queryVisible) {
        this.queryHandleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    // 订单表变化change
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      this.getOrderList(this.queryData);
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .min-table {
  .ant-table-body {
    min-height: 737px;
  }
  .ant-table-placeholder {
    display: none;
  }
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-table-row-cell-break-word {
  font-weight: 500;
}
/deep/.ant-table-header-column {
  font-weight: 500;
}
/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}
/deep/ .ant-table {
  tr.ant-table-row-hover td {
    background: #dfdcdc !important;
  }
  .rowBackgroundColor {
    background: #dcdcdc !important;
  }
  /deep/.ant-modal-body {
    padding: 0 4px;
  }
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/.ant-table {
  .ant-table-thead > tr > th {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 7px 4px;
    border-right: 1px solid #efefef;
    user-select: none;
  }
}
/deep/.ant-empty-normal {
  margin: 0;
}
/deep/.ant-table-column-sorter {
  display: none !important;
}
/deep/.required {
  .ant-form-item-label label {
    color: red !important;
  }
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-table-pagination.ant-pagination {
  z-index: 99;
  position: absolute;
  margin-left: 1%;
}
.footerAction {
  width: 100%;
  height: 55px;
  border: 2px solid #e9e9f0;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  background: #ffffff;
  border-top: 0;
}
.quotation-approval {
  width: 100%;
  height: 780px;
  background-color: white;
  .content {
    display: flex;
    width: 100%;
    height: 100%;
    border-bottom: 4px solid #f0f0f0;
    .left-content {
      width: 67%;
      border: 2px solid #f0f0f0;
    }
    .right-content {
      width: 33%;
      border: 2px solid #f0f0f0;
      .toptable {
        height: 20%;
        width: 100%;
        border-bottom: 2px solid #f0f0f0;
      }
      .centertable {
        height: 45%;
        width: 100%;
        border-bottom: 2px solid #f0f0f0;
      }
      .bottable {
        height: 35%;
        width: 100%;
      }
    }
  }
}
</style>
