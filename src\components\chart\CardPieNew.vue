<!--
 * @Author: CJP
 * @Date: 2022-08-25 09:19:47
 * @LastEditors: wanmhh <EMAIL>
 * @LastEditTime: 2022-08-25 10:15:28
 * @FilePath: \vue-antd-admin\src\components\chart\CardPieNew.vue
 * @Description: 
 * QQ:1806757410
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved. 
-->
<template>
    <div :id="el" style="width:100%; height:142px;"></div>
</template>
<script>
import * as echarts from 'echarts/core';
import { TooltipComponent, LegendComponent } from 'echarts/components';
import { PieChart } from 'echarts/charts';
import { LabelLayout } from 'echarts/features';
import { SVGRenderer } from 'echarts/renderers';

echarts.use([
  TooltipComponent,
  LegendComponent,
  Pie<PERSON><PERSON>,
  SVGRenderer,
  LabelLayout
]);
export default {
  props:["el","echartdata", "minAngle"],
  data() {
    return {
      data:[]
    };
  },
  mounted(){
    this.drawLine()
  },
  methods:{
    drawLine(){
      this.$nextTick(() => {
        const option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            top: '5%',
            left: 'center',
            data: []
          },
          color: ["#FFC977", "#B280FF", "#7EA4FF", "#97E34D", "#1CE297"],
          series: [
            {
              type:'pie',
                radius: ['50%', '95%'],
                // center: ['80%', '50%'],
                avoidLabelOverlap: false,
                hoverAnimation: false, 
                label: {
                    normal: {
                      position: 'inner',
                      show: true,
                      textStyle : {
                        fontWeight : 600 ,
                        fontSize : 10
                      },
                      formatter:'{d}'
                    },
                },
                minAngle: this.minAngle || 0,
              data: this.echartdata || []
            }
          ]
        }
        // console.log('this.echartdata',this.echartdata)
        let chartDom = document.getElementById(this.el);
        let myChart = echarts.init(chartDom);
        myChart.setOption(option);
      })
    }
  },
  watch:{
    echartdata(){
      this.drawLine()
    }
  }
};
</script>
<style scoped>
</style>
