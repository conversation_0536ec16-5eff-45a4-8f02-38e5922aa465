import { request, METHOD } from '@/utils/request';
// 订单列表
export  function getOrderList(params){
  return request('/api/app/e-mSQACheck/qa-check-out-putnew', METHOD.GET, params)
}
// 异常列表
export  function getunusualList(params){
  return request(`/api/app/e-mSQACheck/qa-check-out-put2/${params.id}`, METHOD.GET,)
}
// 订单录入
export  function postAddOrder(params){
  return request(`/api/app/e-mSQACheck/q-aCheck-add-order`, METHOD.POST, params)
}
// 异常录入
export  function postAddUnusOrder(id,params){
  return request(`/api/app/e-mSQACheck/q-aCheck-add-order-info/${id}`, METHOD.POST, params)
}
// 获取工厂数据
export  function getFactoryIdList(params){
  return request('/api/app/e-mSTPub-factory-configure/factory-id-list', METHOD.POST, params)
}
