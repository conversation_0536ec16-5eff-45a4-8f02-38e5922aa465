<template>
  <div>
    <div className="pie" >
      <div id="pie1">
        <!-- 为 ECharts 准备一个具备大小（宽高）的 DOM -->
        <div :id="el" :style="[{height: echartHeight+'px'},{float:'left'},{width:'100%'}]"></div>
      </div>
    </div>
  </div>
</template>

<script>// 引入基本模板
const echarts = require('echarts/lib/echarts');
require('echarts/lib/component/grid');
require('echarts/lib/chart/bar');
require('echarts/lib/component/dataZoom');


export default {
  created() {
  },
  mounted() {
    this.initData();
    JSON.stringify()
  },
  props:{
    el:{
      type: String
    },
    barData:{
      type: Object
    },
    title:{
      type:String
    },
    scroll:{
      type: Boolean,
      default: false
    },
    echartHeight:{
      type:Number,
      default: 230
    }
  },
  methods: {
    //初始化数据
    initData() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById(this.el));
      // console.log('this.title',this.title)
      // console.log('this.barData',this.barData)

      // 绘制图表
      let option = {
        title: {
          text: this.title,//主标题
          x: 'center',//x轴方向对齐方式
        },
        tooltip: {
          trigger: 'item',
          formatter: "{b} : {c}"
        },
        grid: {
            x: 35,
            y: 70,
            x2: 10,
            y2: 0,
            containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: this.barData.name,
        },
        yAxis: {
          type: 'value'
        },
        dataZoom: [
          {
            type:"slider",
            show: this.scroll,
            start: 1,
            end: 100
          },
        ],
        series: this.barData.value
      }
      // console.log('图表数据',option)
      myChart.setOption(option);
    },
  }
}
</script>
