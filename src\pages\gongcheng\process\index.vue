<!-- 工程管理 - 工艺流程  -->
<template>
  <a-spin :spinning="spinning">
    <div class="process">
      <form-basic
        @Auditinspection="Auditinspection"
        @Modifyapplication="Modifyapplication"
        @getFlowList="getFlowList"
        @bianjiad1="bianjiad1"
        @editsteps="editsteps"
        @bianji="bianji"
        @uploaderp="uploaderp"
        @change="change"
        @cancel="cancel"
        @Sendinginstructions="Sendinginstructions"
        @Offline="Offline"
        @surftheInternet="surftheInternet"
        @printpdf="printpdf"
        @Predownloadpdf="Predownloadpdf"
        @Dataexport="Dataexport"
        @Importofreturnorders="Importofreturnorders"
        @IndicationFallback="IndicationFallback"
        @InstructionReview="InstructionReview"
        @signBtn="signBtn"
        :selectedRowKeysArray="selectedRowKeysArray"
        :selectedRowKeysArray1="selectedRowKeysArray1"
        :bianjiad="bianjiad"
        ref="formbasic1"
      ></form-basic>
      <div class="content" style="height: 772px">
        <div class="content-left">
          <a-spin :spinning="tableLoading1">
            <a-card title="订单列表" :bordered="false">
              <!--     子项参数自动展开-->
              <a-table
                v-if="dataSource.length"
                :columns="columns"
                :dataSource="dataSource"
                :pagination="false"
                :rowKey="
                  (record, index) => {
                    return record.orderNo + ',' + record.mainOrderNo + ',' + record.factory;
                  }
                "
                :expandIcon="expandIcon"
                :expandIconColumnIndex="1"
                :defaultExpandAllRows="true"
                :customRow="onClickRow"
                :scroll="{ y: 728 }"
                :rowClassName="isRedRow1"
                ref="FlowList"
              >
                <span slot="num" slot-scope="text, record">
                  {{ record.level }}
                </span>
              </a-table>
            </a-card>
          </a-spin>
        </div>
        <div class="content-center" style="position: relative">
          <div style="height: 35px; line-height: 35px; text-align: center">
            {{ ooderno }} 工艺流程
            <a-icon @click="foldClick" title="全部收起" v-if="fold" type="minus-square" style="color: #ff9900" />
            <a-icon @click="foldClick" title="全部展开" v-if="!fold" type="plus-square" style="color: #ff9900" />
          </div>
          <a-card title="工艺流程" :bordered="false">
            <!-- v-if="dataSource1.length" -->
            <a-table
              :columns="columns1"
              :expandIcon="expandIcon1"
              :expandIconColumnIndex="1"
              :loading="lcloading1"
              :pagination="false"
              :defaultExpandAllRows="true"
              :dataSource="dataSource1"
              childrenColumnName="techFlows"
              :expandedRowKeys="expandedRowKeys"
              @expandedRowsChange="expandedRowsChange"
              :customRow="onClickRow1"
              :rowClassName="isRedRow"
              :rowKey="
                (record, index) => {
                  return record.techNo_;
                }
              "
              :scroll="{ y: 692 }"
              ref="flowQ"
              :class="dataSource1.length ? 'min-table' : ''"
            >
              <span slot="num1" slot-scope="text, record">
                {{ record.level1 }}
              </span>
            </a-table>
          </a-card>
        </div>
        <div class="content-right">
          <a-card title="指示信息" :bordered="false">
            <div class="left">
              <div style="height: 35px; line-height: 35px; text-align: center; border-bottom: 1px solid rgb(233, 233, 240); margin-top: 1px">
                {{ ooderno }} {{ liuchengm }}参数
              </div>
              <div>
                <a-table
                  :columns="columns2"
                  :dataSource="dataSource2"
                  :pagination="false"
                  :scroll="{ y: 396 }"
                  row-key="itemNo_"
                  :class="dataSource2.length ? 'min-table' : ''"
                >
                  <div slot="itemPars_" slot-scope="text, record" :title="record.itemPars_">
                    <span v-if="bianjiad || record.lock_">{{ record.itemPars_ }}</span>
                    <span v-else>
                      <a-input :value="text" @change="e => handleChange(e.target.value, record.itemNo_, 'itemPars_')" />
                    </span>
                  </div>
                </a-table>
                <div style="height: 151px; border-bottom: 2px solid #e9e9f0; position: relative; border-top: 2px solid rgb(233, 233, 240)">
                  <span style="position: absolute; z-index: 999"
                    >流程备注:
                    <span v-if="bianjiad" style="height: 125px; display: block; border-top: 1px solid #e9e9f0; overflow: auto; width: 326px">{{
                      RemarkData.remark_
                    }}</span>
                    <span v-else>
                      <a-textarea v-model="RemarkData.remark_" style="width: 100%; height: 116px; margin-left: 10px; resize: none"></a-textarea>
                    </span>
                  </span>
                </div>
                <div style="height: 142px">
                  <span style="position: absolute; z-index: 999"
                    >订单备注:
                    <span v-if="bianjiad" style="height: 120px; display: block; border-top: 1px solid #e9e9f0; overflow: auto; width: 326px">{{
                      RemarkData.custRemark_
                    }}</span>
                    <span v-else>
                      <a-textarea v-model="RemarkData.custRemark_" style="width: 100%; height: 116px; margin-left: 10px; resize: none"></a-textarea>
                    </span>
                  </span>
                </div>
              </div>
            </div>
            <div class="right">
              <!-- 钻孔表 -->
              <a-table
                v-if="showDrill"
                row-key="id"
                :columns="columns3"
                :dataSource="dataSource3"
                :pagination="false"
                :scroll="{ y: 646 }"
                :class="dataSource3.length ? 'mintable' : ''"
              >
                <span slot="toolKind_" slot-scope="text, record">
                  <a-checkbox v-if="record.toolKind_.indexOf('NPTH') != -1" checked></a-checkbox>
                  <span v-else></span>
                </span>
              </a-table>
              <!-- 开料表 -->
              <a-table
                v-if="showKL"
                :rowKey="
                  (record, index) => {
                    return index;
                  }
                "
                :columns="copycolumns4"
                :dataSource="dataSource4"
                :pagination="false"
                :scroll="{ y: 214 }"
                class="min-table4"
              >
              </a-table>
              <!-- <div v-if='showKL' style="border:1px solid #d9d9d9;height:266px;display: flex;" v-viewer class="showKL">
              <div style="width:50%;border:1px solid #d9d9d9;">
                <h3 style="text-align: center;border-bottom:1px solid #d9d9d9;">开料图</h3>
                <a-spin :spinning="imageLoading" style="height: 80%;width: 100%">
                  <a-empty v-if="!klImahesUrl"/>
                  <div class='img-box'  v-else  >
                    <img :src="klImahesUrl" style="height: 100%;width: 100%" />
                  </div>
                </a-spin>
              </div> 
              <div style="width:50%;border:1px solid #d9d9d9;">
                <h3 style="text-align: center;border-bottom:1px solid #d9d9d9;">B板图</h3>
                <a-spin :spinning="imageLoading" style="height: 80%;width: 100%">
                  <a-empty v-if="!BImahesUrl"/>
                  <div class='img-box'  v-else >
                    <img :src="BImahesUrl" style="height: 100%;width: 100%" />
                  </div>
                </a-spin>
              </div>                
            </div>  -->
              <!-- 层压表 -->
              <div v-if="showKL" style="border: 1px solid #d9d9d9" class="showKL">
                <div style="width: 100%; height: 256px; border: 1px solid #d9d9d9">
                  <h3 style="text-align: center; border-bottom: 1px solid #d9d9d9">开料图</h3>
                  <a-spin :spinning="imageLoading" style="height: 70%; width: 100%">
                    <a-empty v-if="!klImahesUrl" />
                    <div class="img-box" v-else>
                      <img :src="klImahesUrl" style="height: 100%; width: 100%" v-viewer />
                    </div>
                  </a-spin>
                </div>
                <!-- <div style="width:100%;height:256px;border:1px solid #d9d9d9;"  >
                <h3 style="text-align: center;border-bottom:1px solid #d9d9d9;">B板图</h3>
                <a-spin :spinning="imageLoading" style="height: 70%;width: 100%">
                  <a-empty v-if="!BImahesUrl"/>
                  <div class='img-box'  v-else >
                    <img :src="BImahesUrl" style="height: 100%;width: 100%" v-viewer/>
                  </div>
                </a-spin>
              </div>                 -->
              </div>
              <a-table
                v-if="showCY"
                :rowKey="
                  (record, index) => {
                    return index;
                  }
                "
                :columns="columns5"
                :dataSource="dataSource5"
                :pagination="false"
                :scroll="{ y: 397 }"
                :row-class-name="addClass"
                :class="dataSource5.length ? 'mintable1' : ''"
              >
              </a-table>
              <!-- 叠层表 -->
              <a-table
                v-if="showDC"
                :rowKey="
                  (record, index) => {
                    return index;
                  }
                "
                :columns="columns6"
                :dataSource="dataSource6"
                :pagination="false"
                :scroll="{ x: 600, y: 646 }"
                :class="dataSource6.length ? 'mintable' : ''"
              >
              </a-table>
              <a-table
                v-if="showCY"
                :rowKey="
                  (record, index) => {
                    return index;
                  }
                "
                :columns="columns7"
                :dataSource="dataSource7"
                :pagination="false"
                :scroll="{ y: 295 }"
                :class="dataSource7.length ? 'mintable2' : ''"
              >
              </a-table>
            </div>
          </a-card>
        </div>
      </div>
      <a-modal title="审核检查信息" :visible="dataVisible2" @cancel="reportHandleCancel" destroyOnClose centered :maskClosable="false" :width="600">
        <template #footer>
          <!-- <a-button key="back1" type="primary" v-if="check" @click="continueclick">继续</a-button>  -->
          <a-button key="back" @click="reportHandleCancel">取消</a-button>
        </template>
        <div class="class" style="font-size: 16px; font-weight: 500">
          <p v-for="(item, index) in checkData" :key="index">
            <span v-if="item.error == '1'" style="color: red">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.warn == '1'" style="color: CornflowerBlue">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
            <span v-if="item.info == '1'" style="color: black">
              <a-icon type="star"></a-icon>
              <span>{{ item.caption }}:</span>
              <span>{{ item.result }}!</span>
            </span>
          </p>
          <p style="color: black" v-if="check"><a-icon type="star" style="color: black"></a-icon>审核检查通过!</p>
        </div>
      </a-modal>
      <!-- 上传erp弹窗 -->
      <a-modal
        :title="ttype == 'return' ? '返单回导' : '确认弹窗'"
        :visible="datavisible1"
        :maskClosable="false"
        @cancel="handleCancelBJ('1')"
        @ok="handleOkMode"
        destroyOnClose
        :width="400"
        centered
      >
        <span style="font-size: 16px" v-if="ttype != 'return'">{{ messagelist }}</span>
        <span v-else>
          <span style="margin-right: 5px">生产型号:</span>
          <a-input v-model="scbh" :autoFocus="true" placeholder="请输入生产型号" style="width: 200px" />
        </span>
      </a-modal>
      <!--修改申请-->
      <a-modal
        title="修改申请"
        :visible="revisemodal"
        :maskClosable="false"
        @cancel="revisemodal = false"
        @ok="revisefinish"
        destroyOnClose
        :width="600"
        centered
      >
        <a-form-model :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules" ref="ruleForm" :model="form">
          <a-form-model-item label="修改原因" prop="reason">
            <a-textarea v-model="form.reason" allowClear :auto-size="{ minRows: 4, maxRows: 6 }" :auto-focus="true" />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
      <!-- 编辑弹窗 -->
      <a-modal title="编辑" :visible="visibleBJ" @ok="handleBJ" :maskClosable="false" @cancel="handleCancelBJ" :scroll="{ y: 600 }" width="1000px">
        <!-- <div style="position: relative;left: 680px;display: inline-block;" v-if="ttype =='1'">
        <span style="margin-right: 5px;">流程查询:</span>
      <a-input placeholder="请输入流程" allowClear style="height: 28px;width: 180px;margin-right: 10px;" v-model="flowpath"
       @change="queryflowpath()"/>
      </div>        -->
        <div style="display: flex; justify-content: space-around">
          <div class="Ontable">
            <div class="table1" style="display: flex">
              <p style="width: 7%; text-align: center">序号</p>
              <p style="width: 40%; text-align: center">流程名称</p>
              <p style="width: 17%; text-align: center">过序</p>
              <p style="width: 13%; text-align: center">调整顺序</p>
              <p style="width: 19%; text-align: center">删除</p>
            </div>
            <div class="person-list" @dragover="dragover($event)">
              <a-empty v-if="colors.length == 0" />
              <transition-group class="transition-wrapper" name="sort">
                <div
                  v-for="(item, index) in colors"
                  :key="item.techNo_"
                  class="sort-item"
                  :draggable="true"
                  @dragstart="dragstart(item)"
                  @dragenter="dragenter(item, $event)"
                  @dragend="dragend(item, $event)"
                  @dragover="dragover($event)"
                >
                  <div style="display: flex; text-align: center; height: 24px; margin: 0; line-height: 24px">
                    <p style="width: 7%; text-align: center; margin: 0; line-height: 24px; font-size: 13px">{{ index + 1 }}</p>
                    <p style="width: 40%; text-align: center; margin: 0; line-height: 24px; font-size: 13px">{{ item.techName_ }}</p>
                    <p style="width: 20%; text-align: center; margin: 0; line-height: 24px; font-size: 13px">{{ item.isSkip_ ? "否" : "是" }}</p>
                    <p style="width: 13%; text-align: center; margin: 0; line-height: 24px; font-size: 13px" title="点击拖拽可调整流程顺序">
                      <svg
                        t="1727254702626"
                        class="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="1839"
                        width="22"
                        height="22"
                      >
                        <path
                          d="M810.666667 213.333333c23.564433 0 42.666667 19.102234 42.666666 42.666667s-19.102234 42.666667-42.666666 42.666667h-597.333334c-23.564433 0-42.666667-19.102234-42.666666-42.666667s19.102234-42.666667 42.666666-42.666667h597.333334z m0 512c23.564433 0 42.666667 19.102234 42.666666 42.666667s-19.102234 42.666667-42.666666 42.666667h-597.333334c-23.564433 0-42.666667-19.102234-42.666666-42.666667s19.102234-42.666667 42.666666-42.666667h597.333334z m0-256c23.564433 0 42.666667 19.102234 42.666666 42.666667s-19.102234 42.666667-42.666666 42.666667h-597.333334c-23.564433 0-42.666667-19.102234-42.666666-42.666667s19.102234-42.666667 42.666666-42.666667h597.333334z"
                          p-id="1840"
                          fill="#ff9900"
                        ></path>
                      </svg>
                    </p>
                    <template>
                      <a-popconfirm title="确认删除吗?" @confirm="del(item, index)">
                        <!-- <p  style="width:20%;text-align:center;margin:0;line-height: 24px;font-size: 13px;">删除</p> -->
                        <a-icon
                          type="delete"
                          style="width: 20%; text-align: center; margin: 0; line-height: 24px; font-size: 13px"
                          title="点击删除该流程"
                        ></a-icon>
                      </a-popconfirm>
                    </template>
                  </div>
                </div>
              </transition-group>
            </div>
          </div>
          <div class="Ontable" style="width: 47%">
            <div class="table1" style="display: flex">
              <p style="width: 20%; text-align: center">流程序号</p>
              <p style="width: 50%; text-align: center">流程名称</p>
              <p style="width: 28%; text-align: center">添加</p>
            </div>
            <div class="person-list">
              <a-empty v-if="list.length == 0" />
              <div v-for="(item, index) in list" :key="'oneItem' + index">
                <div style="display: flex; border-bottom: 1px solid rgb(233, 233, 240)">
                  <p style="width: 22%; text-align: center; margin: 0; line-height: 24px; font-size: 13px">{{ index + 1 }}</p>
                  <p style="width: 56%; text-align: center; margin: 0; line-height: 24px; font-size: 13px">{{ item.techName_ }}</p>
                  <p style="width: 25%; text-align: center; margin: 0; line-height: 24px; font-size: 13px" @click="add(item, index)">
                    <a-icon type="plus-circle" theme="filled"></a-icon>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <a-input
          placeholder="输入流程名称进行查询"
          :autoFocus="true"
          allowClear
          ref="myinput"
          class="myprocess"
          style="width: 200px; background-color: white; position: absolute; top: 15px; left: 520px; z-index: 99; border-radius: 4px"
          @change="queryflowpath()"
          v-model="flowpath"
        ></a-input>
      </a-modal>
      <a-modal
        title=" "
        :visible="dataVisiblePDF"
        @cancel="handleCancelPDF"
        destroyOnClose
        :maskClosable="false"
        :width="1000"
        centered
        :footer="null"
      >
        <report-info ref="report" :CardId="CardId" :businessOrderNo="businessOrderNo" :lay="lay" />
      </a-modal>
    </div>
  </a-spin>
</template>
<script>
import { checkPermission } from "@/utils/abp";
import qs from "querystring";
import {
  flowList,
  flowQ,
  reviseParameter,
  getDrag,
  reviseProcess,
  getTab,
  drillToolV2,
  klInfo,
  stackInfo,
  stackimpInfo,
  techFlowV2,
  setmiflowinfostoerp,
  setceshimiflowinfostoerp,
  techflowautolist,
  sethPMiflowinfostoerp,
  updatetechflowchild,
  setstatetoerp,
  getflowmI,
  nopecallback,
  techflowcYPMInfo,
  importdata,
  finalchecklist,
  miflowcheck,
  techflowmodifyrecord,
} from "@/services/project";
import FormBasic from "@/pages/gongcheng/process/modules/FormBasic";
import ReportInfo from "@/pages/gongcheng/process/modules/ReportInfo";
const columns = [
  {
    title: "序号",
    align: "left",
    width: 60,
    scopedSlots: { customRender: "num" },
  },
  {
    title: "本厂编号",
    dataIndex: "orderNo",
    ellipsis: true,
    align: "left",
    width: 160,
  },
  {
    title: "层数",
    dataIndex: "layer",
    ellipsis: true,
    align: "center",
    width: 60,
  },
  {
    title: "订单状态",
    dataIndex: "efg",
    ellipsis: true,
    align: "center",
  },
];
const columns1 = [
  {
    title: "序号",
    align: "center",
    //customRender: (text,record,index) => `${index+1}`,
    scopedSlots: { customRender: "num1" },
    width: 70,
  },
  {
    title: "流程名称",
    dataIndex: "techName_",
    align: "left",
    width: 120,
  },
  {
    title: "状态",
    dataIndex: "state_",
    align: "left",
    width: 70,
  },
  {
    title: "过序",
    align: "left",
    customRender: (text, record, index) => `${record.isSkip_ ? "否" : ""}`,
    //width:110
  },
];
const columns2 = [
  {
    title: "参数名称",
    dataIndex: "itemName_",
    align: "left",
    ellipsis: true,
    width: 160,
    customCell: (record, rowIndex) => {
      return { style: { background: "#FAFAFA!important" } };
    },
  },
  {
    title: "参数值",
    dataIndex: "itemPars_",
    align: "left",
    ellipsis: false,
    scopedSlots: { customRender: "itemPars_" },
  },
];
const columns3 = [
  {
    title: "刀序",
    dataIndex: "toolNo_",
    width: 45,
    align: "center",
    ellipsis: true,
  },
  {
    title: "成品孔径",
    dataIndex: "storeDia_",
    width: 75,
    align: "center",
    ellipsis: true,
  },
  {
    title: "公差",
    dataIndex: "positTol_",
    width: 100,
    align: "center",
    ellipsis: true,
  },
  {
    title: "钻孔径",
    dataIndex: "toolDia_",
    width: 60,
    align: "center",
    ellipsis: true,
  },
  // {
  //     title: "钻刀径",
  //     dataIndex: "",
  //     width:60,
  //     align:'center',
  // },
  {
    title: "孔数",
    dataIndex: "drillQty_",
    width: 70,
    align: "center",
    ellipsis: true,
  },
  {
    title: "B孔数",
    dataIndex: "drillQty_B",
    width: 70,
    align: "center",
    ellipsis: true,
  },
  // {
  //     title: "钻槽",
  //     dataIndex: "",
  //     width:45,
  //     align:'center',
  //     ellipsis:true,
  // },
  // {
  //     title: "槽孔数",
  //     dataIndex: "",
  //     width:60,
  //     align:'center',
  //     ellipsis:true,
  // },
  {
    title: "NPTH",
    //dataIndex: "toolKind_",
    width: 80,
    align: "center",
    scopedSlots: { customRender: "toolKind_" },
    // customRender: (text,record,index) => `${record.toolkind == 3 ? '是':'否' }`,
  },
  {
    title: "备注",
    dataIndex: "remark_",
    ellipsis: true,
    align: "center",
  },
];
const columns4 = [
  {
    title: "层",
    dataIndex: "bfloor_",
    width: 60,
    align: "center",
    ellipsis: true,
  },
  {
    title: "顶铜",
    dataIndex: "topOZ_",
    width: 60,
    align: "center",
    ellipsis: true,
  },
  {
    title: "底铜",
    dataIndex: "bomOZ_",
    width: 60,
    align: "center",
    ellipsis: true,
  },
  {
    title: "含铜mm",
    dataIndex: "thicknessCore_",
    width: 70,
    align: "center",
    ellipsis: true,
  },

  {
    title: "不含铜mm",
    dataIndex: "thicknessCoreOZ_",
    width: 80,
    align: "center",
    ellipsis: true,
  },
  {
    title: "供应商",
    dataIndex: "vendorNo_",
    width: 60,
    align: "center",
    ellipsis: true,
  },
  {
    title: "板材类型",
    dataIndex: "coreType_",
    width: 80,
    align: "center",
    ellipsis: true,
  },
  {
    title: "",
    dataIndex: "",
    width: 80,
    align: "center",
    ellipsis: true,
  },
];
const columns5 = [
  {
    title: "层号",
    dataIndex: "stackUpLayerNo_",
    width: 30,
    align: "center",
    ellipsis: true,
    className: "material_bg",
  },
  {
    title: "物料",
    dataIndex: "stackUpMTR_",
    width: 50,
    align: "center",
    ellipsis: true,
    className: "material_bg",
  },
  {
    title: "型号",
    dataIndex: "stackUpMTRType_",
    width: 80,
    align: "center",
    ellipsis: true,
    className: "material_bg",
  },
  {
    title: "介质",
    dataIndex: "stackUpMTRFoil_",
    width: 100,
    align: "center",
    ellipsis: true,
    className: "material_bg",
  },
  {
    title: "成品厚度(mm)",
    dataIndex: "stackUpThichnessMM_",
    width: 70,
    align: "center",
    className: "M_bg",
  },
];
const columns6 = [
  {
    title: "",
    //dataIndex: "index",
    width: 40,
    ellipsis: true,
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "阻抗类型",
    key: "imp_type_PRC",
    dataIndex: "imp_type_PRC",
    // scopedSlots: { customRender: 'imp_type_PRC' },
    width: 120,
    ellipsis: true,
    align: "center",
  },
  {
    title: "控制层",
    key: "imp_ControlLay_",
    dataIndex: "imp_ControlLay_",
    width: 60,
    ellipsis: true,
    // scopedSlots: { customRender: 'imp_ControlLay_' },
    align: "center",
  },
  {
    title: "上参",
    dataIndex: "imp_UpLay_",
    width: 60,
    align: "center",
    ellipsis: true,
    key: "imp_UpLay_",
    // scopedSlots: { customRender: 'imp_UpLay_' },
  },
  {
    title: "下参",
    dataIndex: "imp_DownLay_",
    width: 60,
    key: "imp_DownLay_",
    ellipsis: true,
    // scopedSlots: { customRender: 'imp_DownLay_' },
    align: "center",
  },

  {
    title: "线宽",
    key: "imp_LineWidth_",
    dataIndex: "imp_LineWidth_",
    // scopedSlots: { customRender: 'imp_LineWidth_' },
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "线隙",
    key: "imp_LineSpace_",
    dataIndex: "imp_LineSpace_",
    // scopedSlots: { customRender: 'imp_LineSpace_' },
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "线铜",
    key: "imp_LineCuSpace_",
    dataIndex: "imp_LineCuSpace_",
    // scopedSlots: { customRender: 'imp_LineCuSpace_' },
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "要求",
    key: "imp_Value_Req_",
    dataIndex: "imp_Value_Req_",
    // scopedSlots: { customRender: 'imp_Value_Req_' },
    align: "center",
    ellipsis: true,
    width: 55,
  },
  {
    title: "公差",
    dataIndex: "imp_Value_Tol_",
    width: 55,
    key: "imp_Value_Tol_",
    // scopedSlots: { customRender: 'imp_Value_Tol_' },
    align: "center",
    ellipsis: true,
  },
  {
    title: "L/W",
    dataIndex: "imp_OKLineWidth_",
    key: "imp_OKLineWidth_",
    // scopedSlots: { customRender: 'imp_OKLineWidth_' },
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "L/S",
    dataIndex: "imp_OKLineSpace_",
    // key: 'imp_OKLineSpace_',
    // scopedSlots: { customRender: 'imp_OKLineSpace_' },
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "L/Cu",
    dataIndex: "imp_OKLineCuSpace_",
    width: 50,
    key: "imp_OKLineCuSpace_",
    align: "center",
    ellipsis: true,
    // scopedSlots: { customRender: 'imp_OKLineCuSpace_' },
  },
  {
    title: "Imp(Eth)",
    dataIndex: "imp_TrueValueWithOutSM_",
    align: "center",
    ellipsis: true,
    width: 80,
  },
  {
    title: "Imp",
    key: "imp_TrueValue_",
    dataIndex: "imp_TrueValue_",
    //scopedSlots: { customRender: 'imp_TrueValue_' },
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "H1",
    dataIndex: "imp_H1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "Er1",
    dataIndex: "imp_Er1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "H2",
    dataIndex: "imp_H2_",
    align: "center",
    ellipsis: true,
    width: 50,
  },

  {
    title: "Er2",
    dataIndex: "imp_Er2_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "W1",
    dataIndex: "imp_W1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "W2",
    dataIndex: "imp_W2_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "S1",
    dataIndex: "imp_S1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "D1",
    dataIndex: "imp_D1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "T1",
    dataIndex: "imp_T1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "C1",
    dataIndex: "imp_C1_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "C2",
    dataIndex: "imp_C2_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "C3",
    dataIndex: "imp_C3_",
    align: "center",
    ellipsis: true,
    width: 50,
  },
  {
    title: "CEr_",
    dataIndex: "imp_CEr_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "Imp-H1",
    dataIndex: "imp_H1IncludeLyrNoList_",
    align: "center",
    ellipsis: true,
    width: 70,
  },
  {
    title: "Imp-H2",
    dataIndex: "imp_H2IncludeLyrNoList_",
    align: "center",
    ellipsis: true,
    width: 70,
  },
  {
    title: "补偿",
    key: "imp_BC_",
    dataIndex: "imp_BC_",
    // scopedSlots: { customRender: 'imp_BC_' },
    align: "center",
    ellipsis: true,
    width: 50,
  },
];
const columns7 = [
  // {
  //   title: "序号",
  //   dataIndex: "sectionNo_",
  //   width:20,
  //   ellipsis: true,
  //   align: 'center',
  // },
  {
    title: "层号",
    dataIndex: "stackUpLayerNo_",
    width: 20,
    ellipsis: true,
    align: "center",
  },
  {
    title: "剖面",
    dataIndex: "thick_",
    width: 60,
    ellipsis: true,
    align: "center",
  },
  {
    title: "TG要求",
    dataIndex: "tG_",
    width: 33,
    ellipsis: true,
    align: "center",
  },
  {
    title: "厚度",
    dataIndex: "inControl_",
    width: 33,
    align: "center",
    ellipsis: true,
  },
  {
    title: "残铜率",
    dataIndex: "ctL_",
    width: 34,
    ellipsis: true,
    align: "center",
  },

  {
    title: "层编",
    dataIndex: "materNo_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
  {
    title: "RC要求",
    dataIndex: "rC_",
    align: "center",
    ellipsis: true,
    width: 35,
  },
];
export default {
  components: { FormBasic, ReportInfo },
  data() {
    return {
      uptype: "",
      dataVisible2: false,
      searchVisible: false,
      checkData: [],
      check: false,
      spinning: false,
      flowpath: undefined,
      rrecord: "",
      bianjigongbu: [],
      columns,
      ooderno: "",
      type: "",
      ooderno2: "",
      liuchengm: "",
      techno: "",
      dataSource: [],
      expandedRowKeys: [],
      columns1,
      dataSource1: [],
      lcloading1: false,
      columns2,
      dataSource2: [],
      columns3,
      dataSource3: [],
      columns4,
      copycolumns4: [],
      dataSource4: [],
      columns5,
      dataSource5: [],
      dataSource7: [],
      columns6,
      columns7,
      dataSource6: [],
      pagination: {
        current: 1,
        pageSize: 20,
        showTotal: total => `总计 ${total} 条`,
        total: 0,
      },
      showDrill: false,
      showKL: false,
      showCY: false,
      showDC: false,
      revisemodal: false,
      rules: { reason: [{ required: true, message: "请填写修改原因", trigger: "blur" }] },
      form: { reason: "" },
      bianjiad: true, // 子项编辑
      techNo_: "",
      guid_: "",
      gguid: "",
      erpup: "",
      messagelist: "",
      ttype: "",
      selectedrowdata: [],
      lay: null,
      RemarkData: {},
      visibleBJ: false,
      datavisible1: false,
      isActive: null,
      list: [],
      copylist: [],
      alltechlist: [],
      colors: [],
      selectedRowKeysArray: [],
      selectedRowKeysArray1: [],
      pdctNo_: "",
      oldData: "",
      processId: "",
      tableLoading1: false,
      row1: {},
      selectedRows: {},
      fold: true,
      AllIds: [],
      klImahesUrl: "",
      BImahesUrl: "",
      activeKey: ["1"],
      imageLoading: false,
      scbh: "",
      dataVisiblePDF: false,
      CardId: "",
      businessOrderNo: "",
      isCtrlPressed: false,
    };
  },
  created() {
    this.pdctNo_ = this.$route.query.pdctno;
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  mounted() {
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
    this.copycolumns4 = JSON.parse(JSON.stringify(columns4));
    this.copycolumns4.forEach(item => {
      if (item.title == "" && item.dataIndex == "") {
        if ([78, 79, 80].includes(Number(this.$route.query.factoryId))) {
          item.title = "板材类别";
          item.dataIndex = "plateType_";
        } else {
          item.title = "TG值";
          item.dataIndex = "ppTypeCodes_";
        }
      }
    });
  },
  methods: {
    checkPermission, // 权限判断函数
    reportHandleCancel() {
      this.dataVisible2 = false;
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        this.$refs.formbasic1.searchReport();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "69" && this.isCtrlPressed && checkPermission("MES.EngineeringModule.TechnologicalProcess.FlowRepier")) {
        this.bianjiad1();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (
        e.keyCode == "82" &&
        this.isCtrlPressed &&
        this.selectedRowKeysArray1.length != 0 &&
        checkPermission("MES.EngineeringModule.TechnologicalProcess.ParRepier")
      ) {
        this.bianji();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "83" && this.isCtrlPressed && !this.bianjiad) {
        this.signBtn();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    keyup(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    queryflowpath(val) {
      if (val == "clear") {
        this.flowpath = "";
      }
      this.list = this.copylist.filter(item => {
        return item.techName_.indexOf(this.flowpath) != -1;
      });
    },
    //获取总流程工序数据
    getDrag(val) {
      if (val) {
        getDrag(val).then(res => {
          res.data.forEach((item, index) => {
            item.index = index + 1;
            item["guid_"] = this.guid_;
          });
          this.alltechlist = res.data;
          this.copylist = [];
          this.copylist = res.data.filter(copyItem => {
            return !this.colors.some(colorItem => colorItem.techNo_ === copyItem.techNo_);
          });
          this.list = this.copylist;
          // 工艺流程名查重
          // let a =  this.alltechlist.reduce((acc, item) => {
          //   if (!acc[item.techName_]) {
          //     acc[item.techName_] = [];
          //   }
          //   acc[item.techName_].push(item);
          //   return acc;
          // }, {});
          // Object.values(a).forEach(key => {
          //   if(key.length>1)
          //  console.log(key)
          // });
        });
      }
    },
    techflowautolist(val, techno) {
      techflowautolist(val, techno).then(res => {
        res.data.forEach((item, index) => {
          item.index = index + 1;
          item["guid_"] = this.guid_;
        });
        this.list = res.data;
      });
    },
    addPropertyToTree(tree, prop, parentLevel) {
      tree.forEach((node, index) => {
        node[prop] = parentLevel ? `${parentLevel}-${index + 1}` : `${index + 1}`;
        if (node.children) {
          this.addPropertyToTree(node.children, prop, node[prop]);
        }
      });
    },
    addPropertyToTree1(tree, prop, parentLevel) {
      tree.forEach((node, index) => {
        node[prop] = parentLevel ? `${parentLevel}-${index + 1}` : `${index + 1}`;
        if (node.techFlows) {
          this.addPropertyToTree1(node.techFlows, prop, node[prop]);
        }
      });
    },

    // 获取工艺流程列表
    getFlowList(record) {
      this.processId = "";
      this.selectedRowKeysArray1 = [];
      this.dataSource2 = []; //参数
      this.RemarkData = {}; //备注信息
      this.dataSource3 = []; //钻孔
      this.dataSource4 = []; //开料
      this.klImahesUrl = "";
      this.BImahesUrl = "";
      this.dataSource5 = []; //层压
      this.dataSource6 = []; //阻抗
      this.dataSource7 = []; //信息表
      this.dataSource1 = [];
      let params = {
        PageIndex: this.pagination.current,
        "PageSize ": this.pagination.pageSize,
      };
      if (record) {
        params.Pdctno = record;
      }
      params.factoryId = this.$route.query.factoryId;
      this.tableLoading1 = true;
      techFlowV2(params)
        .then(res => {
          if (res.code) {
            this.dataSource = res.data;
            this.selectedrowdata = this.dataSource[0];
            if (this.dataSource.length) {
              this.ooderno = this.dataSource[0].orderNo;
            }
            this.addPropertyToTree(this.dataSource, "level");
            if (this.dataSource.length) {
              this.guid_ = this.dataSource[0].orderNo + "," + this.dataSource[0].mainOrderNo + "," + this.dataSource[0].factory;
              this.gguid = this.dataSource[0].mainOrderNo + "," + this.dataSource[0].factory;
              this.row1 = this.dataSource[0];
              this.getFlowQ(this.guid_);
              this.selectedRowKeysArray.push(this.guid_);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.tableLoading1 = false;
        });
    },
    change() {
      this.selectedRowKeysArray = [];
    },
    // 自定义树形选择图标
    expandIcon(props) {
      // console.log('props',props.record.children);
      if (props.record.children.length > 0 && props.record.children != "null") {
        if (props.expanded) {
          //有数据-展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder-open" style="margin-right:5px" />
            </a>
          );
        } else {
          //有数据-未展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder" style="margin-right:5px" />
            </a>
          );
        }
      } else {
        //无数据-图标
        return (
          <span style="margin-right:0px">
            <a-icon type=" " />
          </span>
        );
      }
    },
    expandIcon1(props) {
      this.Data;
      if (props.record.techFlows && props.record.techFlows.length > 0 && props.record.techFlows != "null") {
        if (props.expanded) {
          //有数据-展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder-open" style="margin-right:5px" />
            </a>
          );
        } else {
          //有数据-未展开时候图标
          return (
            <a
              onClick={e => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="folder" style="margin-right:5px" />
            </a>
          );
        }
      } else {
        //无数据-图标
        return (
          <span style="margin-right:0px">
            <a-icon type=" " />
          </span>
        );
      }
    },
    isRedRow1(record) {
      let strGroup = [];
      let str = [];
      let guid = record.orderNo + "," + record.mainOrderNo + "," + record.factory;
      if (guid == this.guid_) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    // 订单列表点击事件
    onClickRow(record) {
      return {
        on: {
          click: () => {
            if (!this.bianjiad) {
              this.$message.warning("编辑参数状态不可执行此操作");
              return;
            }
            if (record.mainOrderNo == record.orderNo) {
              this.ooderno = record.orderNo;
            } else {
              if (record.mainOrderNo == record.parentOrderNo) {
                this.ooderno = record.mainOrderNo + "-" + record.orderNo;
              } else {
                this.ooderno = record.mainOrderNo + "-" + record.parentOrderNo + "-" + record.orderNo;
              }
            }
            this.liuchengm = "";
            this.processId = "";
            this.selectedRowKeysArray1 = [];
            this.dataSource2 = []; //参数
            this.RemarkData = {}; //备注信息
            this.dataSource3 = []; //钻孔
            this.showDrill = false;
            this.dataSource4 = []; //开料
            this.showKL = false;
            this.klImahesUrl = "";
            this.BImahesUrl = "";
            this.dataSource5 = []; //层压
            this.showCY = false;
            this.dataSource6 = []; //阻抗
            this.showDC = false;
            this.dataSource7 = []; //信息表
            this.selectedRows = {};
            this.gguid = record.mainOrderNo + "," + record.factory;
            this.guid_ = record.orderNo + "," + record.mainOrderNo + "," + record.factory;
            this.getFlowQ(this.guid_);
            let keys = [];
            keys.push(this.guid_);
            this.selectedrowdata = record;
            this.selectedRowKeysArray = keys;
            this.pdctNo_ = record.orderNo;
          },
        },
      };
    },
    // 获取流程信息
    getFlowQ(record, techName_) {
      var arr = record.split(",");
      this.lcloading1 = true;
      flowQ(arr[0], arr[1], arr[2])
        .then(res => {
          if (res.code) {
            this.dataSource1 = res.data;
            this.colors = res.data;
            this.addPropertyToTree1(this.dataSource1, "level1");
            this.AllIds = this.getAllIds(this.dataSource1, []);
            if (techName_) {
              var data = this.dataSource1.filter(item => {
                return item.techName_ == techName_;
              })[0];
              if (data) {
                this.getParameter(data);
                this.getcanshu(data);
              } else {
                for (let index = 0; index < this.dataSource1.length; index++) {
                  const element = this.dataSource1[index];
                  if (element.techFlows.length > 0) {
                    var ddata = element.techFlows.filter(item => {
                      return item.techName_ == techName_;
                    })[0];
                    this.getParameter(ddata);
                    this.getcanshu(ddata);
                  }
                }
              }
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.lcloading1 = false;
        });
    },
    // 流程信息列表点击事件
    onClickRow1(record) {
      return {
        on: {
          click: () => {
            if (!this.bianjiad) {
              this.$message.warning("编辑参数状态不可执行此操作");
              return;
            }
            this.liuchengm = record.techName_;
            this.selectedRows = record;
            this.getParameter(record);
            let keys = [];
            keys.push(record.guid_);
            this.selectedRowKeysArray1 = keys;
            this.processId = record.techNo_;
            let str = "";
            str = record.level1.split("-")[0];
            if (str) {
              for (var a = 0; a < this.dataSource1.length; a++) {
                if (this.dataSource1[a].level1 == str) {
                  this.techno = this.dataSource1[a].techNo_;
                }
              }
            }
            this.getcanshu(record);
          },
        },
      };
    },
    getcanshu(record) {
      var arr = this.selectedRowKeysArray[0].split(",");
      if (record.ctlType_ == "418" || record.ctlType_ == "415") {
        //钻孔
        this.showDrill = true;
        drillToolV2(arr[0], arr[1], arr[2], record.techNo_).then(res => {
          if (res.code) {
            this.dataSource3 = res.data;
            console.log(this.dataSource3[6].toolKind_.indexOf("NPTH"), this.dataSource3[6]);
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.showDrill = false;
      }
      if (record.ctlType_ == "1123") {
        //开料 1123
        this.showKL = true;
        // 获取开料表
        klInfo(arr[0], arr[1], arr[2]).then(res => {
          if (res.code) {
            this.dataSource4 = res.data.klinfos;
            this.klImahesUrl = res.data.klImahesUrl;
            this.BImahesUrl = res.data.BImahesUrl;
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.showKL = false;
      }
      if (record.ctlType_ == "416") {
        //	层压
        this.showCY = true;
        stackInfo(arr[0], arr[1], arr[2]).then(res => {
          if (res.code) {
            this.dataSource5 = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
        techflowcYPMInfo(arr[0], arr[1], arr[2]).then(res => {
          if (res.code) {
            this.dataSource7 = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.showCY = false;
      }
      if (record.ctlType_ == "417") {
        //	叠层
        this.showDC = true;
        stackimpInfo(arr[0], arr[1], arr[2]).then(res => {
          if (res.code) {
            this.dataSource6 = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        this.showDC = false;
      }
    },
    isRedRow(record) {
      let strGroup = [];
      let str = [];
      if (record.techNo_ && record.techNo_ == this.processId) {
        strGroup.push("rowBackgroundColor");
      }
      return str.concat(strGroup);
    },
    // 获取参数信息表
    getParameter(record) {
      this.dataSource2 = record.techFlowItemParameters;
      this.RemarkData = record.techFlowRemarks[0];
      this.techNo_ = record.techNo_;
      this.guid_ = record.orderNo + "," + record.mainOrderNo + "," + record.factoryId;
    },
    // 编辑参数
    bianji() {
      this.bianjiad = false;
    },
    // 取消子项编辑
    cancel() {
      this.bianjiad = true;
      this.getFlowQ(this.guid_, this.selectedRows.techName_);
    },
    //修改子项table数据
    handleChange(value, key, column) {
      const newData = [...this.dataSource2];
      const target = newData.filter(item => key === item.itemNo_)[0];
      if (target) {
        target[column] = value;
        this.dataSource2 = newData;
      }
    },
    // 保存子项编辑
    signBtn() {
      this.spinning = true;
      this.$refs.formbasic1.saveloading = true;
      let parmas = {
        techNo_: this.techNo_,
        techFlowItemParameters: this.dataSource2,
        orderNo: this.guid_.split(",")[0],
        mainOrderNo: this.guid_.split(",")[1],
        factoryId: Number(this.guid_.split(",")[2]),
        erpOrderKEY: this.dataSource1[0].erpOrderKEY,
        Id: this.dataSource1[0].guid_,
        techFlowRemark: {
          techNo_: this.techNo_,
          remark_: this.RemarkData.remark_,
          custRemark_: this.RemarkData.custRemark_,
          orderNo: this.guid_.split(",")[0],
          mainOrderNo: this.guid_.split(",")[1],
          factoryId: Number(this.guid_.split(",")[2]),
        },
      };
      reviseParameter(parmas)
        .then(res => {
          if (res.code == 1) {
            this.bianjiad = true;
            this.$message.info(res.message);
          } else {
            this.bianjiad = false;
            this.$message.info(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
          this.$refs.formbasic1.saveloading = false;
        });
    },
    //打印PDF
    printpdf() {
      getflowmI(this.selectedrowdata.factory, this.selectedrowdata.mainOrderNo).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    Singleprocess() {
      let params = {
        mainorderno: this.selectedrowdata.mainOrderNo,
        orderno: this.selectedrowdata.orderNo,
        factoryId: this.selectedrowdata.factory,
        statetype: this.ttype,
        custechno: this.selectedRows.cusTechno_,
        techno: this.selectedRows.techNo_,
        isall: false,
      };
      this.spinning = true;
      setstatetoerp(params)
        .then(res => {
          if (res.code) {
            this.$message.success("操作成功");
            let guid_ = this.selectedrowdata.orderNo + "," + this.selectedrowdata.mainOrderNo + "," + this.selectedrowdata.factory;
            this.getFlowQ(guid_);
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    //指示回退
    IndicationFallback() {
      this.ttype = "5";
      if (JSON.stringify(this.selectedRows) != "{}") {
        this.Singleprocess();
      } else {
        this.messagelist = "确定指示回退吗?";
        this.datavisible1 = true;
      }
    },
    //指示审核
    InstructionReview() {
      this.ttype = "4";
      if (JSON.stringify(this.selectedRows) != "{}") {
        this.Singleprocess();
      } else {
        this.messagelist = "确定指示审核吗?";
        this.datavisible1 = true;
      }
    },
    //下网
    Offline() {
      this.ttype = "3";
      if (JSON.stringify(this.selectedRows) != "{}") {
        this.Singleprocess();
      } else {
        this.messagelist = "确定下网吗?";
        this.datavisible1 = true;
      }
    },
    //上网
    surftheInternet() {
      this.ttype = "2";
      if (JSON.stringify(this.selectedRows) != "{}") {
        this.Singleprocess();
      } else {
        this.messagelist = "确定上网吗?";
        this.datavisible1 = true;
      }
    },
    //发送指示
    Sendinginstructions() {
      this.ttype = "1";
      if (JSON.stringify(this.selectedRows) != "{}") {
        this.Singleprocess();
      } else {
        this.messagelist = "确定发送指示吗?";
        this.datavisible1 = true;
      }
    },
    //上传ERP
    uploaderp(type) {
      this.uptype = type;
      this.ttype = "erp";
      this.messagelist = "确定上传ERP吗?";
      this.datavisible1 = true;
    },
    Importofreturnorders() {
      this.scbh = "";
      this.ttype = "return";
      this.datavisible1 = true;
    },
    Modifyapplication() {
      this.revisemodal = true;
    },
    revisefinish() {
      const form = this.$refs.ruleForm;
      form.validate(valid => {
        if (valid) {
          var params = {
            OrderNo: this.selectedrowdata.mainOrderNo,
            Remark: this.form.reason,
          };
          techflowmodifyrecord(this.selectedrowdata.factory, qs.stringify(params))
            .then(res => {
              if (res.code) {
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.revisemodal = false;
            });
        }
      });
    },
    //指示检查
    Auditinspection() {
      var ayya = this.gguid.split(",");
      finalchecklist(ayya[1], ayya[0]).then(res => {
        if (res.code) {
          this.checkData = res.data;
          this.check = this.checkData.findIndex(v => v.error == "1") < 0;
          this.dataVisible2 = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    Dataexport() {
      this.ttype = "data";
      this.messagelist = "确定数据导入吗?";
      this.datavisible1 = true;
    },
    handleOkMode() {
      this.spinning = true;
      this.datavisible1 = false;
      var ayya = this.gguid.split(",");
      var params = {};
      if (this.selectedrowdata && JSON.stringify(this.selectedrowdata) != "{}") {
        params = {
          mainorderno: this.selectedrowdata.mainOrderNo,
          orderno: this.selectedrowdata.orderNo,
          factoryId: this.selectedrowdata.factory,
          statetype: this.ttype,
        };
      }
      if (JSON.stringify(this.selectedRows) != "{}" && this.ttype != "erp" && this.ttype != "return" && this.ttype != "data") {
        params.custechno = this.selectedRows.cusTechno_;
        params.techno = this.selectedRows.techNo_;
        params.isall = false;
      } else {
        params.isall = true;
      }
      if (this.ttype == "erp") {
        let UploadtheERP;
        if (this.uptype == "formal") {
          UploadtheERP = setmiflowinfostoerp;
        } else {
          UploadtheERP = setceshimiflowinfostoerp;
        }
        sethPMiflowinfostoerp(ayya[1], ayya[0]).then(res => {
          if (res.code) {
            UploadtheERP(ayya[0], ayya[1])
              .then(res => {
                if (res.code) {
                  this.$message.success("上传ERP成功");
                } else {
                  this.$message.error(res.message);
                }
              })
              .finally(() => {
                this.spinning = false;
              });
          } else {
            this.$message.error(res.message);
            this.spinning = false;
          }
        });
      } else if (this.ttype == "return") {
        nopecallback(this.scbh)
          .then(ite => {
            if (ite.code) {
              this.$message.success("返单回导成功");
              this.getFlowList(this.scbh);
            } else {
              this.$message.error(ite.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else if (this.ttype == "data") {
        importdata(params.factoryId, params.orderno, params.mainorderno, this.selectedRows.techNo_)
          .then(ite => {
            if (ite.code) {
              importdata(params.factoryId, params.orderno, params.mainorderno, this.selectedRows.techNo_).then(ite => {
                if (ite.code) {
                  this.$message.success("数据导入成功");
                  this.getFlowQ(this.guid_, this.selectedRows.techName_);
                } else {
                  this.$message.error(ite.message);
                }
              });
            } else {
              this.$message.error(ite.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      } else {
        setstatetoerp(params)
          .then(res => {
            if (res.code) {
              this.$message.success("操作成功");
              let guid_ = this.selectedrowdata.orderNo + "," + this.selectedrowdata.mainOrderNo + "," + this.selectedrowdata.factory;
              this.getFlowQ(guid_);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    // 编辑流程
    bianjiad1() {
      this.getDrag(this.guid_.split(",")[2]);
      this.visibleBJ = true;
      this.ttype = "1";
      this.flowpath = undefined;
      this.$nextTick(() => {
        this.$refs.myinput.focus();
      });
    },
    editsteps() {
      this.techflowautolist(this.guid_.split(",")[2], this.techno);
      this.visibleBJ = true;
      this.$nextTick(() => {
        this.$refs.myinput.focus();
      });

      this.ttype = "2";
      var aaa = this.colors.filter(item => {
        return item.techNo_ == this.techno;
      });
      for (let index = 0; index < aaa.length; index++) {
        this.colors = aaa[index].techFlows;
      }
    },
    handleCancelBJ(type) {
      this.visibleBJ = false;
      this.datavisible1 = false;
      if (this.guid_ && type != "1") {
        this.getFlowQ(this.guid_);
      }
    },
    // 保存流程编辑
    handleBJ() {
      this.visibleBJ = false;
      this.spinning = true;
      let parmas = this.colors.map(item => {
        return {
          guid_: item.guid_,
          isSkip_: item.isSkip_,
          techNo_: item.techNo_,
          orderNo: item.orderNo,
          mainOrderNo: item.mainOrderNo,
          factoryId: Number(item.factoryId),
        };
      });
      if (this.ttype == "1") {
        reviseProcess(parmas)
          .then(res => {
            if (res.code == 1) {
              this.$message.success("编辑成功");
              this.getFlowQ(this.guid_);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
      if (this.ttype == "2") {
        updatetechflowchild(parmas, this.techno)
          .then(res => {
            if (res.code) {
              this.$message.success("编辑成功");
              this.getFlowQ(this.guid_);
            } else {
              this.$message.error(res.message);
            }
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    add(val, index) {
      let some = [];
      this.colors.forEach(res => {
        some.push(res.techNo_);
      });
      if (some.includes(val.techNo_)) {
        this.$message.info("已存在该工序");
      } else {
        val.techFlows = JSON.parse(JSON.stringify(val.techFlowAutoListChildrens));
        val.orderNo = this.guid_.split(",")[0];
        val.mainOrderNo = this.guid_.split(",")[1];
        val.factoryId = this.guid_.split(",")[2];
        this.colors.push(val);
        this.list.splice(index, 1);
      }
    },
    dragenter(value, e) {
      this.newData = value;
      e.preventDefault();
    },
    dragstart(value) {
      this.oldData = value;
    },
    dragend(value, e) {
      if (this.oldData !== this.newData) {
        let oldIndex = this.colors.indexOf(this.oldData);
        let newIndex = this.colors.indexOf(this.newData);
        let newItems = [...this.colors];
        // 删除老的节点
        newItems.splice(oldIndex, 1);
        // 在列表中目标位置增加新的节点
        newItems.splice(newIndex, 0, this.oldData);
        this.colors = [...newItems];
      }
    },
    dragover(e) {
      e.preventDefault();
    },
    del(val, index) {
      miflowcheck(val.factoryId, val.mainOrderNo, val.techName_).then(res => {
        if (res.code) {
          this.colors.splice(index, 1);
          this.list.push(
            this.alltechlist.filter(item => {
              return item.techNo_ === val.techNo_;
            })[0]
          );
          this.$forceUpdate();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getAllIds(tree, result) {
      for (const i in tree) {
        result.push(tree[i].level1);
        if (tree[i].techFlows) {
          this.getAllIds(tree[i].techFlows, result);
        }
      }
      return result;
    },
    expandedRowsChange(expandedRows) {
      this.expandedRowKeys = expandedRows;
    },
    foldClick() {
      this.fold = !this.fold;
      if (!this.fold) {
        this.expandedRowKeys = [];
      } else {
        this.expandedRowKeys = this.AllIds;
      }
    },
    addClass(record, index) {
      switch (record.stackUpMTR_) {
        case "OZ":
          return "OZ";
        case "Core":
          return "Core";
        case "PP":
          return "PP";
        case "GB":
          return "GB";
        default:
          break;
      }
    },
    //
    handleCancelPDF() {
      console.log("关闭");
      this.dataVisiblePDF = false;
    },
    // 预下载pdf
    Predownloadpdf() {
      // let url = "https://bninfofile2.obs.cn-south-1.myhuaweicloud.com/project/kailiao/1710748976002_Sheet.jpg"
      // window.location.href = url;
      this.CardId = this.selectedrowdata.factory;
      this.businessOrderNo = this.selectedrowdata.mainOrderNo;
      this.lay = this.selectedrowdata.layer;
      this.dataVisiblePDF = true;
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.myprocess {
  .ant-input {
    border: 1px solid #ff9900;
  }
}
/deep/.showKL {
  .ant-empty {
    // margin:13% 8px;
    margin: 7% 8px;
  }
}
/deep/ .ant-table-tbody .M_bg {
  background: #8fbc8b !important;
}
/deep/ .ant-table-tbody {
  // font-weight: 600!important;
  .OZ {
    .material_bg {
      background: #f4a460 !important;
    }
  }
  .Core {
    .material_bg {
      background: #f0e68c !important;
    }
  }
  .GB {
    .material_bg {
      background: #d6d6d6 !important;
    }
  }
  .PP {
    .material_bg {
      background: #9acd32 !important;
    }
  }
  .material_bg {
    .ant-select-selection {
      background: none;
    }
  }
}
/deep/.min-table4 {
  // height:498px;
  height: 250px;
}
.img-box {
  width: 170px;
  height: 200px;
  margin: 0 auto;
  //margin-top:28px;
  margin-top: 20px;
}

/deep/.ant-modal-header .ant-modal-title {
  font-weight: 500;
}

/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
.sort-move {
  transition: transform 0.3s;
}
.sort-item {
  border-bottom: 1px solid #efefef;
  background-color: #fdfdfd;
}
.process {
  /deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
    background: #f8f8f8;
  }
  min-width: 1670px;
  background: #ffffff;
  padding: 4px;
  /deep/.rowBackgroundColor {
    background: #dfdcdc !important;
  }
  /deep/.ant-table-body {
    .ant-table-tbody {
      tr {
        td {
          padding: 4px 2px;
        }
      }
    }
  }
  .content {
    border: 1px solid #d8d8d8;
    display: flex;
    /deep/ .ant-card-head {
      display: none;
      padding: 0;
      font-weight: 500;
      color: #333333;
      text-align: center;
      font-size: 15px;
      margin: 0;
      border: 0;
      min-height: 30px;
      .ant-card-head-title {
        padding: 10px 0;
      }
    }
    /deep/ .ant-card-body {
      padding: 0;
    }
    /deep/ .content-left {
      position: relative;
      .ant-table {
        height: 620px;
        .ant-table-row-selected {
          td {
            background: #dcdcdc;
          }
        }
      }
      .ant-card {
        .ant-card-body {
          border-top: 2px solid rgb(233, 233, 240);
        }
        position: unset;
      }
      .ant-spin-nested-loading {
        position: unset;
      }
      .ant-spin-container {
        position: unset;
        .ant-pagination {
          margin: 0;
        }
      }
      .ant-pagination {
        position: absolute;
        bottom: 6px;
        left: 153px;
      }
      // width: 26%;
      width: 24%;
      border: 2px solid rgb(233, 233, 240);
    }
    /deep/ .content-center {
      .ant-card {
        .ant-card-body {
          border-top: 1px solid rgb(233, 233, 240);
          border-bottom: 2px solid rgb(233, 233, 240);
        }
        position: unset;
        .min-table {
          height: 728px;
          .ant-table-body {
            min-height: 692px;
          }
        }
      }
      width: 20%;
      border: 2px solid rgb(233, 233, 240);
      border-top: 4px solid rgb(233, 233, 240);
    }
    /deep/ .content-right {
      width: 56%;
      border: 2px solid rgb(233, 233, 240);
      .ant-card {
        .ant-card-body {
          height: 768px;
          display: flex;
          .left {
            .min-table {
              .ant-table-body {
                min-height: 396px;
              }
            }
            .ant-table-body .ant-table-tbody tr td {
              padding: 4px 9px !important;
            }
            .ant-table-wrapper {
              height: 434px;
              border-bottom: 2px solid rgb(233, 233, 240);
              // border-bottom: 2px solid rgb(216, 216, 216);
            }
            width: 35%;
            position: relative;
            border-top: 2px solid rgb(233, 233, 240);
            border-right: 4px solid rgb(233, 233, 240);
            border-bottom: 2px solid rgb(233, 233, 240);
          }
          .right {
            width: 65%;
            .mintable {
              .ant-table-body {
                min-height: 728px;
              }
            }
            .mintable1 {
              .ant-table .ant-table-tbody > tr > td {
                height: 28px;
              }
              .ant-table-body {
                min-height: 397px;
              }
            }
            .mintable2 {
              .ant-table-body {
                min-height: 295px;
                border-bottom: 2px solid rgb(233, 233, 240);
              }
            }
            // border: 2px solid rgb(233, 233, 240);
            border-top: 2px solid rgb(233, 233, 240);
            border-bottom: 2px solid rgb(233, 233, 240);
          }
        }
      }
    }
  }
  /deep/ .ant-table {
    tr th {
      padding: 4px 2px;
    }
  }
}
.left {
  /deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: none;
  }
  /deep/.ant-input {
    height: 27px;
    font-weight: 500;
  }
  /deep/.ant-table tr th {
    padding: 4px 9px !important;
  }
}
/deep/ .ant-modal {
  top: 40px;
  .Ontable {
    width: 48%;
    overflow: hidden;
    border: 2px solid rgb(233, 233, 240);
    .ant-empty {
      margin: 100px 8px;
    }
  }

  .Ontable table {
    width: 100%;
  }

  .Ontable table tr {
    height: 30px;
  }
  .Ontable .person-list {
    height: 653px;
    overflow-y: scroll;
  }
  .Ontable .table1 {
    background: #e8e8e8;
    text-align: center;
    height: 30px;
    line-height: 30px;
    color: #6c747f;
  }

  .Ontable table {
    text-align: center;
  }
}
/deep/ .ant-table {
  .ant-table-thead > tr > th {
    // padding: 7px 4px;
    border-right: 1px solid #efefef;
    // border-color: #f0f0f0;
  }

  .ant-table-tbody > tr > td {
    // padding: 7px 4px;
    border-right: 1px solid #efefef;
    // border-color: #f0f0f0;
  }

  tr.ant-table-row-selected td {
    background: #dfdcdc;
  }

  tr.ant-table-row-hover td {
    background: #dfdcdc;
  }

  .rowBackgroundColor {
    background: #dfdcdc !important;
  }

  .ant-table-selection-col {
    width: 20px !important;
  }
}
</style>
