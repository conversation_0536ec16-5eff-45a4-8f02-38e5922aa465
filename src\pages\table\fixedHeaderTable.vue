<template>
 <page-layout>
  <a-card>
     <standard-table
        rowKey="key"
        :columns="columns"
        :dataSource="data"
        :pagination="pagination"
        :scroll="scroll"
     >

     </standard-table>
  </a-card>
 </page-layout>
</template>
<script>
import StandardTable from "@/components/table/StandardTable";
import PageLayout from '@/layouts/PageLayout'
const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    width: 150,
  },
  {
    title: 'Age',
    dataIndex: 'age',
    width: 150,
  },
  {
    title: 'Address',
    dataIndex: 'address',
  },
];

const data = [];
for (let i = 0; i < 100; i++) {
  data.push({
    key: i,
    name: `<PERSON> ${i}`,
    age: 32,
    address: `London, Park Lane no. ${i}`,
  });
}

export default {
  components: { PageLayout, StandardTable},
  data() {
    return {
      data,
      columns,
      scroll: { y: 240 },
      pagination: this.$store.state.setting.pagination
    };
  },
};
</script>
