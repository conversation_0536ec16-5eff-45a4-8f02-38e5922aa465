<!-- 工程管理 - 合拼设计 -按钮 -->
<template>
  <div class="active" ref="active">
    <!-- <div class="box">
          v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateErpCombineData')"
         <a-button type="primary" @click='ReceivingClick' :loading="btnloading1"  >
           ERP导入
         </a-button>
       </div> -->
    <div class="box showClass">
      <a-button type="primary" @click="QueryClick" style="padding: 0"> 查询(F) </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateWaitHPToHpPoolHand')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateWaitHPToHpPoolHand') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="hpPoolHandClick"> 订单合拼 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateExecutionOrder')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateExecutionOrder') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="OrderCombinationHandClick"> 数据合拼 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateExecutionGS')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateExecutionGS') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="dataMerging"> 图形合拼 </a-button>
    </div>

    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateAutoPnl')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateAutoPnl') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="panelEdgeBanding"> 封边输出 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.UploadFile')"
      :class="checkPermission('MES.EngineeringModule.Combinate.UploadFile') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="$emit('UpdateFile')"> 上传文件 </a-button>
    </div>
    <!-- <div class="box showClass" >
          v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateFileOut')"
           <a-button type="primary" @click='outputFile' >
             输出文件
           </a-button>
       </div> -->

    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.GongChenZhis')"
      :class="checkPermission('MES.EngineeringModule.Combinate.GongChenZhis') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="orderClick" style="padding: 0" :loading="orderload"> 工程指示 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinatePinBanFinished')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinatePinBanFinished') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="combineFinish"> 合拼完成 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateImportErpDataM')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateImportErpDataM') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="importErpDataMClick"> 手动导入 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateBack2WaitHP')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateBack2WaitHP') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="back2WaitHPClick" style="padding: 0"> 回待合拼 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateBack')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateBack') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="backHPClick"> 回退合拼 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateSigFinish')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateSigFinish') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="sigFinishClick"> 单板下线 </a-button>
    </div>
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateSigBack')"
      :class="checkPermission('MES.EngineeringModule.Combinate.CombinateSigBack') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="ChargebackClick"> 单板回退 </a-button>
    </div>
    <!-- <div class="box " v-if="checkPermission('MES.EngineeringModule.Combinate.ReadData')"
        :class='checkPermission("MES.EngineeringModule.Combinate.ReadData")?"showClass":""'>
          <a-button type="primary" @click='readDataClick' >
          读取数据
          </a-button>
        </div>
        <div class="box" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateClearSize')"
        :class='checkPermission("MES.EngineeringModule.Combinate.CombinateClearSize")?"showClass":""'>
          <a-button type="primary" @click='clearSizeClick' >
            清除数据
          </a-button>
        </div> -->
    <div
      class="box"
      v-if="checkPermission('MES.EngineeringModule.Combinate.ConfigPar')"
      :class="checkPermission('MES.EngineeringModule.Combinate.ConfigPar') ? 'showClass' : ''"
    >
      <a-button type="primary" @click="parClick" style="padding: 0"> 配置参数 </a-button>
    </div>
    <!-- <div class="box" v-if="checkPermission('MES.EngineeringModule.Combinate.ChangeFatory')"
          :class='checkPermission("MES.EngineeringModule.Combinate.ChangeFatory")?"showClass":""'>
         <a-button type="primary" @click='changefac' style="padding:0;">
           修改工厂
         </a-button>
       </div>   -->
    <!-- <div class="box showClass">
         <a-button type="primary" @click='productClick'>
           生成流程
         </a-button>
       </div> -->
    <!-- <div class="box">
        v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateParaSet')"
         <a-button type="primary" @click='editClick' >
           编写参数
         </a-button>
       </div> -->
    <!-- <div v-if='advanced' >
         <div class="box">
          v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateFlowReMarks')"
           <a-button type="primary" @click='ProcessNotesClick' style="padding:0;" >
             流程备注
           </a-button>
         </div>
       </div> -->
    <!-- <div v-if='advanced' >
         <div class="box">
          v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateDeleteData')"
           <a-button type="primary" @click='deleteDataClick' >
             删除数据
           </a-button>
         </div>
       </div> -->
    <!-- <div v-if='advanced' >
         <div class="box">
          v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateImportErpDataM')"
           <a-button type="primary" @click='importErpDataMClick' >
             手动导入
           </a-button>
         </div>
       </div> -->
    <span class="box" v-if="showBtn && !buttonsmenu">
      <a-button type="dashed" @click="toggleAdvanced">
        {{ advanced ? "收起" : "展开" }}
        <a-icon :type="advanced ? 'right' : 'left'" />
      </a-button>
    </span>
    <div v-if="buttonsmenu">
      <a-dropdown>
        <a-button type="primary" style="margin-top: 9px; margin-right: 10px" @click.prevent> 按钮菜单栏 </a-button>
        <template #overlay>
          <a-menu class="tabRightClikBox3">
            <a-menu-item @click="QueryClick">查询(F)</a-menu-item>
            <a-menu-item @click="hpPoolHandClick" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateWaitHPToHpPoolHand')"
              >订单合拼</a-menu-item
            >
            <a-menu-item @click="OrderCombinationHandClick" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateExecutionOrder')"
              >数据合拼</a-menu-item
            >
            <a-menu-item @click="dataMerging" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateExecutionGS')">图形合拼</a-menu-item>
            <a-menu-item @click="$emit('UpdateFile')">上传文件</a-menu-item>
            <a-menu-item @click="panelEdgeBanding" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateAutoPnl')">封边输出</a-menu-item>
            <a-menu-item @click="orderClick" v-if="checkPermission('MES.EngineeringModule.Combinate.GongChenZhis')">工程指示</a-menu-item>
            <a-menu-item @click="combineFinish" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinatePinBanFinished')"
              >合拼完成</a-menu-item
            >
            <a-menu-item @click="importErpDataMClick" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateImportErpDataM')"
              >手动导入</a-menu-item
            >
            <a-menu-item @click="back2WaitHPClick" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateBack2WaitHP')"
              >回待合拼</a-menu-item
            >
            <a-menu-item @click="backHPClick" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateBack')">回退合拼</a-menu-item>
            <a-menu-item @click="sigFinishClick" v-if="checkPermission('MES.EngineeringModule.Combinate.CombinateSigFinish')">单板下线</a-menu-item>
            <a-menu-item @click="parClick" v-if="checkPermission('MES.EngineeringModule.Combinate.ConfigPar')">配置参数</a-menu-item>
            <a-menu-item @click="changefac">更改工厂</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { checkPermission } from "@/utils/abp";
export default {
  name: "Action",
  props: ["btnloading1", "orderload"],
  data() {
    return {
      advanced: false,
      width: 862,
      collapsed: false,
      showBtn: false,
      buttonsmenu: false,
    };
  },
  mounted() {
    window.addEventListener("resize", this.handleResize, true);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize, true);
  },
  created() {
    this.$nextTick(() => {
      const elements = document.getElementsByClassName("showClass");
      const num = elements.length;
      // let buttonsToShow = 7
      // if (this.$refs.active.children.length > 7) {
      //   for (let i = 0; i < elements.length; i++) {
      //   if (i < buttonsToShow) {
      //     elements[i].style.display = "inline-block";
      //   } else {
      //     elements[i].style.display = "none";
      //   }
      // }
      // } else {
      //   for (let i = 0; i < elements.length; i++){
      //     elements[i].style.display = "inline-block";
      //   }

      // }
      if (num <= 7) {
        this.showBtn = false;
      } else {
        this.showBtn = true;
        this.advanced = true;
      }
      this.handleResize();
    });
  },
  methods: {
    checkPermission,
    handleResize() {
      var elements = document.getElementsByClassName("showClass");
      let num = "";
      if (!this.advanced) {
        num = 8 * 104;
      } else {
        num = (elements.length + 1) * 104;
      }
      if (window.innerWidth < 1920 || window.innerHeight < 923) {
        if (num < window.innerWidth - 150 && window.innerWidth > 766) {
          for (let i = 0; i < elements.length; i++) {
            if (i < 7) {
              elements[i].style.display = "inline-block";
            }
          }
          this.buttonsmenu = false;
        } else {
          if (window.innerWidth > 766) {
            for (let i = 0; i < elements.length; i++) {
              elements[i].style.display = "none";
            }
            this.advanced = false;
            this.buttonsmenu = true;
          } else {
            if (window.innerWidth - 4 - num < 70) {
              for (let i = 0; i < elements.length; i++) {
                elements[i].style.display = "none";
                this.buttonsmenu = true;
              }
            } else {
              for (let i = 0; i < elements.length; i++) {
                if (i < 7) {
                  elements[i].style.display = "inline-block";
                }
              }
              this.buttonsmenu = false;
            }
          }
        }
      } else {
        for (let i = 0; i < elements.length; i++) {
          if (i < 7) {
            elements[i].style.display = "inline-block";
          }
        }
        this.buttonsmenu = false;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
      let width_ = 0;
      const elements = document.getElementsByClassName("showClass");
      if (this.advanced) {
        width_ = 1500;
        for (let i = 0; i < elements.length; i++) {
          elements[i].style.display = "inline-block";
        }
      } else {
        // width_= 762
        let buttonsToShow = 7;
        for (let i = 0; i < elements.length; i++) {
          if (i < buttonsToShow) {
            elements[i].style.display = "inline-block";
          } else {
            elements[i].style.display = "none";
          }
        }
      }
      this.handleResize();
      //this.$refs.active.style.width = width_ + 'px';
    },
    // 单板回退
    ChargebackClick() {
      this.$emit("ChargebackClick");
    },
    // 参数
    parClick() {
      this.$emit("parClick");
    },
    changefac() {
      this.$emit("changefac");
    },
    // ERP导入
    ReceivingClick() {
      this.$emit("ReceivingClick");
    },
    // 工程指示
    orderClick(record) {
      this.$emit("orderClick", record);
    },
    // 编写参数
    editClick() {
      this.$emit("editClick");
    },
    // 单板下线
    sigFinishClick() {
      this.$emit("sigFinishClick");
    },
    // 合拼订单
    hpPoolHandClick() {
      this.$emit("hpPoolHandClick");
    },
    // 回退合拼
    backHPClick(record) {
      this.$emit("backHPClick", record);
    },
    // 回退待合拼
    back2WaitHPClick() {
      this.$emit("back2WaitHPClick");
    },
    // 查询
    QueryClick() {
      this.$emit("QueryClick");
    },
    //生成流程
    productClick() {
      this.$emit("productClick");
    },
    // 流程备注
    ProcessNotesClick() {
      this.$emit("ProcessNotesClick");
    },
    //订单合拼
    OrderCombinationHandClick() {
      this.$emit("OrderCombinationHandClick");
    },
    // 删除数据
    deleteDataClick() {
      this.$emit("deleteDataClick");
    },
    // 手动导入
    importErpDataMClick() {
      this.$emit("importErpDataMClick");
    },
    // 清除数据
    clearSizeClick() {
      this.$emit("clearSizeClick");
    },
    // 获取数据
    readDataClick() {
      this.$emit("readDataClick");
    },
    // 数据合拼
    dataMerging() {
      this.$emit("dataMerging");
    },
    //拼版封边
    panelEdgeBanding() {
      this.$emit("panelEdgeBanding");
    },
    //输出文件
    outputFile() {
      this.$emit("outputFile");
    },
    //合拼完成
    combineFinish() {
      this.$emit("combineFinish");
    },
  },
};
</script>

<style scoped lang="less">
.tabRightClikBox3 {
  border: 2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    width: 100px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #666666;
  }
  li:hover {
    background-color: #ff9900 !important;
    color: white;
    font-size: 16px;
    height: 32px;
    line-height: 22px;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.active {
  height: 57px;
  line-height: 50px;
  float: right;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  .box {
    width: 90px;
    margin-top: 3px;
    text-align: center;

    .ant-btn {
      width: 80px;
    }
    .selctClass {
      /deep/ .ant-select-selection {
        &:hover {
          border-color: #cccccc;
        }
        &:focus {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        &:active {
          border-color: #cccccc;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0);
        }
        border: 0;
        background: #ff9900;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
        .ant-select-selection__rendered {
          display: none;
          border-radius: 8px;
        }
        .ant-select-arrow {
          //font-size: 14px;
          right: 2px;
        }
      }
    }
  }
  /deep/ .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
    padding: 0 14px;
  }
}
</style>
