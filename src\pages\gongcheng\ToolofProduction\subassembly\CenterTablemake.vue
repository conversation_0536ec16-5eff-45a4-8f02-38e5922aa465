<!-- 工程管理 - 生产工具-订单列表 中间 -->
<template>
    <div ref="tableWrapper">
      <div>
      <a-table 
      :columns="columns" 
      :loading="centerListTableLoading"
      :dataSource="dataSource" 
      :scroll="{y:648}"
      :pagination="false" 
      :customRow="onClickRow"
      :rowClassName="Clickoncolor"
      :rowKey="rowKey" >
      </a-table>
    </div>
    </div>
    </template>
    <script> 
    import { checkPermission } from "@/utils/abp";
    export default {
      props: {
       dataSource: {
          type: Array,
          require: true,
          default: () => []
        },
       columns: {
         type: Array,
         require: true
        },
       rowKey: {
        type: String,
        require: true
        },
        centerListTableLoading:{
          type: Boolean,
          require: true,
        }
      },
      name: "LeftTable",
      data() {
        return {
          erpid:'',
          personnel:'',
        }
      },
      created() {
      },
      methods: {
        checkPermission,
        Clickoncolor(record){
          let str = []
          if(record.userLoginID_ && record.userLoginID_ == this.erpid){
            str.push ('rowBackgroundColor')
          }
          return str 
        },
        onClickRow(record) {
        return {
          on: {
            click: () => {
              let keys = [];
              keys.push(record.id);
              this.selectedRowKeysArray = keys;
              this.selectedRowsData = record
              this.erpid = record.userLoginID_
              this.personnel = record.realName
              this.$emit('getbotdata',this.erpid);
            },
          }
        }
      },
      },
    }
    </script>
    <style lang="less" scoped>
    /deep/.ant-table{
      .rowBackgroundColor {
      background: #DFDCDC!important;
    }
    }
    </style>
    