<template>
  <div @click="bodyClick">
    <a-form>
      <a-form-item>
        <a-upload
          name="file"
          ref="fileRef"
          accept="image/*"
          :file-list="fileList"
          list-type="picture-card"
          @change="handleChange"
          @preview="handlePreview"
          :before-upload="beforeUpload"
          :customRequest="downloadFilesCustomRequest"
        >
          <a-button style="font-weight: 500; height: 40px" v-if="fileList.length < 4"> 上传图片 </a-button>
          <a-button
            style="font-weight: 500; margin-top: 6px; height: 40px"
            @click.stop="showCopy(1)"
            title="点击 ctrl+V粘贴上传"
            v-if="fileList.length < 4"
          >
            粘贴图片
          </a-button>
        </a-upload>
        <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
          <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
      </a-form-item>
      <a-form-item label="注意事项" :label-col="{ span: 3 }" :wrapper-col="{ span: 18 }"
        ><br />
        <a-textarea v-model="form.conent" :autoFocus="autoFocus" :rows="6" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import axios from "axios";
import { UploadFile } from "@/services/projectMake";
export default {
  name: "RepairStartInfo",
  data() {
    return {
      fileList: [],
      form: {
        conent: "",
        picUrl: "",
        isbefor: true,
      },
      autoFocus: true,
      previewVisible: false,
      previewImage: "",
      showCopyType: "",
    };
  },
  beforeDestroy() {
    window.removeEventListener("paste", this.getClipboardFiles);
  },
  methods: {
    handleCancel() {
      this.previewVisible = false;
    },
    handlePreview(file) {
      console.log("6666");
      this.previewImage = file.url || file.thumbUrl;
      this.previewVisible = true;
    },
    handleChange({ fileList }, data) {
      if (!fileList) {
        fileList = data.concat(this.fileList);
      }
      this.fileList = fileList;
      this.form.picUrl = this.fileList
        .map(item => {
          return item.response;
        })
        .join(",");
      console.log("this.fileList:", this.fileList);
    },
    // 上传图片路径
    downloadFilesCustomRequest(data) {
      const formData = new FormData();
      formData.append("file", data.file);
      UploadFile(formData).then(res => {
        if (res.code == 1) {
          data.onSuccess(res.data);
          this.form.picUrl = this.fileList
            .map(item => {
              return item.response;
            })
            .join(",");
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 限制上传格式
    beforeUpload(file) {
      const _this = this;
      return new Promise(function (resolve, reject) {
        const isJpgOrPng =
          file.type.toLowerCase() === "image/jpeg" ||
          file.type.toLowerCase() === "image/png" ||
          file.type.toLowerCase() === "image/gif" ||
          file.type.toLowerCase() === "image/bmp" ||
          file.type.toLowerCase() === "image/jpg";
        if (!isJpgOrPng) {
          _this.$message.error("图片只支持|*.jpg;*.png;*.gif;*.jpeg;*.bmp 格式");
          reject();
        } else {
          resolve();
        }
      });
    },
    showCopy(type) {
      window.addEventListener("paste", this.getClipboardFiles);
      this.showCopyType = type;
      try {
        navigator.clipboard.read().then(res => {
          const clipboardItems = res;
          if (clipboardItems[0].types[0].indexOf("image") > -1) {
            clipboardItems[0].getType(clipboardItems[0].types[0]).then(b => {
              const files = new window.File([b], `${Math.floor(Math.random() * 2147483648).toString(36)}.png`, { type: clipboardItems[0].types[0] });
              this.file = files;
              this.getClipboardFiles();
            });
          } else {
            this.$message.error("粘贴内容不是图片");
            return;
          }
        });
      } catch (e) {
        ////console.log('出错了')
      }
    },
    bodyClick() {
      //console.log('bodyClick')
      window.removeEventListener("paste", this.getClipboardFiles);
    },
    getClipboardFiles(event) {
      let file = null;
      if (event) {
        const items = event.clipboardData && event.clipboardData.items;
        if (items && items.length) {
          // 检索剪切板items,类数组，不能使用forEach
          for (let i = 0; i < items.length; i += 1) {
            //console.log('复制items[i]',items[i],)
            if (items[i].type.indexOf("image") !== -1) {
              file = items[i].getAsFile();
            }
          }
        }
      } else {
        file = this.file;
      }
      //console.log('file',file)
      if (!file) {
        this.$message.error("粘贴内容不是图片");
        return;
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        this.$message.error("请上传小于10M,并且格式正确的图片");
        return;
      }
      this.beforeUpload(file); // 上传组件自带的函数，这里有些图片校验规则
      if (this.beforeUpload(file)) {
        // **** return true 之后进行上传
        const formData = new FormData();
        // formData.append('groupCode', 'coupon'); // 接口需要额外的参数，可根据情况自定义
        formData.append("file", file);
        axios({
          url: process.env.VUE_APP_API_BASE_URL + "/api/app/engineering-production/up-load-back-file", // 接口地址
          method: "post",
          data: formData,
          //headers: this.headers,  // 请求头
        })
          .then(res => {
            if (res.code) {
              //console.log('res',res)
              file.status = "done";
              file.response = res.data;
              file.thumbUrl = res.data;
              file.url = res.data;
              file.uid = new Date().valueOf();
              const arr = [];
              arr.push({
                name: file.name,
                uid: file.uid,
                response: file.response,
                size: file.size,
                status: file.status,
                type: file.type,
                thumbUrl: file.thumbUrl,
                url: file.url,
              });
              //console.log('this.showCopyType',this.showCopyType)
              if (this.showCopyType == "1") {
                this.handleChange(file, arr);
              }
            } else {
              this.$message.error(data.message || "网络异常,请重试或检查网络连接状态");
            }
          })
          .catch(() => {
            // this.$message.error('网络异常,请重试或检查网络连接状态');
            // this.startloading = false;
            // this.show = false;
          });
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-input {
  margin-left: -65px;
  font-weight: 500;
}
</style>
