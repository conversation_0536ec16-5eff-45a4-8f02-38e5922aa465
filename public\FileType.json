[{"id": 28, "extension": "jpg", "description": "图片格式，广泛用于数字照片和图像的压缩。"}, {"id": 21, "extension": "PCBDOC", "description": "PCB设计文件格式，通常由Altium Designer等电子设计自动化（EDA）软件使用。"}, {"id": 1, "extension": "txt", "description": "纯文本文件，不包含任何格式或特殊编码。"}, {"id": 3, "extension": "doc", "description": "Microsoft Word 文档格式。"}, {"id": 4, "extension": "xls", "description": "Excel 表格文件格式。"}, {"id": 12, "extension": "pdf", "description": "Adobe开发的可移植文档格式，用于文档的交换和共享。"}, {"id": 15, "extension": "mp3", "description": "音频压缩文件格式，广泛用于音乐和音频文件。"}, {"id": 7, "extension": "avi", "description": "音频视频交错格式，广泛用于视频存储。"}, {"id": 9, "extension": "html", "description": "网页文件格式，通常用于网页内容的存储和展示。"}, {"id": 8, "extension": "zip", "description": "压缩文件格式，用于存储多个文件和文件夹。"}, {"id": 10, "extension": "png", "description": "可移植网络图形文件格式，支持透明背景，广泛用于网页图像。"}, {"id": 11, "extension": "gif", "description": "图像格式，支持简单的动画效果。"}, {"id": 13, "extension": "svg", "description": "一种基于 XML 的矢量图形格式，用于在网络和其他环境中创建可缩放的二维图形，可通过代码精确描述图像的形状和属性，适用于图标、图表等，且文件大小通常较小，适合在不同分辨率下显示而不失真。"}, {"id": 14, "extension": "tiff", "description": "常用于高质量图像存储，支持多页、多通道、高分辨率和无损压缩，常用于印刷、出版和摄影领域，能保存图像的丰富信息，如颜色深度、透明度等。"}, {"id": 16, "extension": "bmp", "description": "一种简单的位图图像格式，存储像素的原始数据，无压缩或采用简单的无损压缩，文件通常较大，适用于 Windows 环境下的图像存储，可存储高质量图像但占用较多存储空间。"}, {"id": 17, "extension": "mp4", "description": "一种广泛使用的数字多媒体容器格式，结合了 MPEG-4 的视频编码和 AAC 的音频编码，常用于存储视频和音频，是网络视频和流媒体的常用格式，支持高压缩比和高质量视频。"}, {"id": 18, "extension": "mov", "description": "由苹果公司开发，支持多种编码，常用于存储视频、音频、文本和其他数据，是视频编辑和存储的常见格式，广泛应用于 Mac 系统和苹果设备。"}, {"id": 19, "extension": "wmv", "description": "微软开发的视频格式，通常用于 Windows 平台，支持多种编码，在流媒体和在线视频中也有一定应用，可根据网络带宽调整视频质量。"}, {"id": 20, "extension": "ppt", "description": "Microsoft PowerPoint 的演示文稿文件格式，用于创建演示文稿，包含文本、图像、图表、动画等元素，常用于商务演示、教育和培训等场合。"}, {"id": 22, "extension": "odt", "description": "开放文档格式的文本文件，是开源办公软件（如 LibreOffice）的默认文本文件格式，可存储文本、图像、表格等内容，支持复杂的文档排版和样式。"}, {"id": 23, "extension": "rtf", "description": "一种文本文件格式，支持基本的文本格式，如字体、字号、颜色、段落格式等，具有一定的跨平台兼容性，可在不同文字处理软件间交换文档。"}, {"id": 24, "extension": "wav", "description": "一种无损音频文件格式，存储未经压缩的音频数据，音质高，常用于音频编辑和专业音频制作，但文件体积较大，适合需要高保真音质的场合。"}, {"id": 25, "extension": "aac", "description": "一种高压缩比的音频格式，提供比 MP3 更好的音质，广泛用于在线音乐服务、流媒体和移动设备音频播放，是苹果设备的默认音频格式之一。"}, {"id": 26, "extension": "flac", "description": "一种无损音频压缩格式，可压缩音频文件而不损失音质，适合音乐收藏家和对音质要求较高的用户，文件大小比 WAV 小但比有损压缩格式大。"}, {"id": 27, "extension": "json", "description": "一种轻量级的数据交换格式，易于阅读和编写，常用于 Web 服务和 API 中，用于在不同系统间传递数据，基于文本，可表示复杂的数据结构，如对象、数组、字符串等。"}, {"id": 29, "extension": "xml", "description": "一种标记语言，用于存储和传输数据，可自定义标签，常用于数据存储、配置文件和文档标记，具有良好的扩展性和跨平台兼容性。"}, {"id": 30, "extension": "yaml", "description": "一种简洁的数据序列化格式，以易读的文本格式存储数据，常用于配置文件和数据交换，比 XML 更简洁，常用于自动化部署和开发环境。"}]