<template>
  <a-modal :width="640" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" class="userRoleModal">
    <h3 name="title">修改权限-{{ userName }}</h3>
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-tabs tab-position="left">
          <a-tab-pane forceRender v-for="(group, index) in permissionData.groups" :key="index + 1" :tab="group.displayName">
            <a-form-model-item :label="group.displayName">
              <a-tree
                ref="permissionTree"
                v-model="group.value"
                checkable
                autoExpandParent
                :treeData="transformPermissionTree(group.permissions)"
                :replaceFields="treeDefaultProps"
                checkStrictly
                @check="change"
              />
            </a-form-model-item>
          </a-tab-pane>
        </a-tabs>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { get, createUpdate } from "@/services/permission-management/permissions";
let that;
export default {
  name: "PermissionForm",
  props: {
    providerName: {
      type: String,
      required: true,
    },
    recordName: {},
  },
  data() {
    return {
      labelCol: { span: 7 },
      wrapperCol: { span: 10 },
      visible: false,
      confirmLoading: false,
      form: {},
      rules: {},
      permissionsQuery: { providerKey: "", providerName: "" },
      permissionData: {
        groups: [],
      },
      treeDefaultProps: {
        children: "children",
        title: "label",
        key: "name",
      },
      dataList: [],
      data: [],
      tabKey: 0,
      userName: "",
      arrKey: [],
      checkedList: {},
      // selectedPermissions:{
      //   "AbpIdentity":[],
      //   "FeatureManagement":[],
      //   "AbpTenantManagement":[],
      //   "EasyAbp.FileManagement":[],
      //   "AbpVnext":[],
      //   "AbpAuditLogging":[],
      // },
      // selectedPermissions: [
      //   [],[],[],[],[]
      // ],
    };
  },
  created() {
    that = this;
    this.permissionsQuery.providerName = this.providerName;
  },
  methods: {
    // cheshi(){
    //     console.log(this.data[this.tabKey])
    //   let list=[]
    //   this.data[this.tabKey].permissions.forEach(res=>{
    //     list.push(res.name)
    //   })
    //   console.log(list)
    //    console.log(this.permissionData.groups[this.tabKey].value.checked)
    //   console.log([...this.permissionData.groups[this.tabKey].value.checked,list])
    //   this.permissionData.groups[this.tabKey].value.checked=[...this.permissionData.groups[this.tabKey].value.checked,list]
    //   console.log(this.permissionData)
    // },
    // callback(key){
    //   console.log(key);
    //   this.tabKey=key-1
    // },
    openModal(model) {
      this.visible = true;
      // console.log(model)
      if (model.userName) {
        this.userName = model.userName;
      } else {
        this.userName = model.name;
      }

      // this.form = model;
      if (this.permissionsQuery.providerName === "R") {
        this.permissionsQuery.providerKey = model.name;
      } else if (this.permissionsQuery.providerName === "U") {
        this.permissionsQuery.providerKey = model.id;
      }
      if (model && model.id) {
        this.getFormData(model.id);
      }
    },
    resetForm() {
      this.form = {};
    },
    getFormData() {
      this.confirmLoading = true;
      get(this.permissionsQuery)
        .then(res => {
          // this.data=res.groups
          // console.log( this.data)
          // this.permissionData = res;
          for (const i in res.groups) {
            // let selectedPermissions = [];
            const keys = [];
            const group = res.groups[i];
            for (const j in group.permissions) {
              const name = group.permissions[j].name;
              const parents = group.permissions.filter(v => v.parentName === name);
              // var isallselect=false;
              // if(parents==null || parents.length==0){
              //   isallselect=true;
              // }else{
              //   isallselect = parents.every( item => item.isGranted === true);
              // }

              if (group.permissions[j].isGranted) {
                keys.push(group.permissions[j].name);
              }
            }
            group.value = { checked: keys || [] };
            // selectedPermissions = [...selectedPermissions, ...keys];
            // this.$nextTick(() => {
            //   that.selectedPermissions[group.name] = selectedPermissions||[];
            // });
          }
          this.permissionData = res;
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    // onCheck(checkedKeys, info) {
    //   console.log('onCheck', checkedKeys,info);
    //   let a=[]
    //   if(info.halfCheckedKeys.length>0){
    //     info.halfCheckedKeys.forEach(res=>{
    //       // this.dataList.push({
    //       //   isGranted: true,
    //       //   name: res,
    //       // });
    //       // this.permissionData.groups[this.tabKey].value.push(res)
    //       a.push(res)
    //
    //     })
    //   }
    //   // this.permissionData.groups[this.tabKey].value=[...this.permissionData.groups[this.tabKey].value,...a]
    //   console.log(a)
    //   console.log(this.permissionData.groups[this.tabKey].value)
    // },
    handleCancel() {
      this.visible = false;
      this.currentStep = 0;
    },
    change(checkedKeys, e) {
      // this.arrKey = e.halfCheckedKeys
      var list = e.node.dataRef;
      var Keys = [];
      var moduleDataList = list.children;
      let arr = this.getAllNodeId(Keys, moduleDataList);
      if (e.node.checked) {
        for (const i in this.permissionData.groups) {
          const group = this.permissionData.groups[i];
          const keys = arr || [];
          for (const j in keys) {
            group.value.checked = group.value.checked.filter(x => x != keys[j]);
          }
        }
      } else {
        for (const a in this.permissionData.groups) {
          const group = this.permissionData.groups[a];
          // console.log('group',group)
          const keys = arr || [];
          for (const b in keys) {
            group.value.checked.push(keys[b]);
          }
          // console.log('勾选',group.value.checked)
        }
      }
    },
    // 递归获取所选节点所有子节点key
    getAllNodeId(Keys, moduleDataList) {
      for (let i = 0; i < moduleDataList.length; i++) {
        Keys.push(moduleDataList[i].key);
        if (moduleDataList[i].children) {
          Keys = this.getAllNodeId(Keys, moduleDataList[i].children);
        }
      }
      return Keys;
    },

    handleOk() {
      const form = this.$refs.ruleForm;
      this.confirmLoading = true;
      form.validate(valid => {
        if (valid) {
          const tempData = [];
          for (const i in this.permissionData.groups) {
            const group = this.permissionData.groups[i];
            const keys = group.value.checked || group.value || [];
            // console.log('group',group)
            // const keys =key.concat(this.arrKey)
            // console.log('this.arrKey',this.arrKey)
            // console.log('keys',keys)
            for (const j in group.permissions) {
              // console.log(group.permissions[j].isGranted,keys.some((v) => v === group.permissions[j].name))
              if (group.permissions[j].isGranted && !keys.some(v => v === group.permissions[j].name)) {
                tempData.push({
                  isGranted: false,
                  name: group.permissions[j].name,
                });
              } else if (!group.permissions[j].isGranted && keys.some(v => v === group.permissions[j].name)) {
                tempData.push({
                  isGranted: true,
                  name: group.permissions[j].name,
                });
              }
              // else if (
              //   group.permissions[j].isGranted &&
              //   keys.some((v) => v === group.permissions[j].name)
              // ) {
              //   // console.log(group.permissions[j].name)
              //   tempData.push({
              //     isGranted: true,
              //     name: group.permissions[j].name,
              //   });

              // }
            }
          }
          // console.log('tempData',tempData)
          createUpdate(this.permissionsQuery, { permissions: tempData })
            .then(res => {
              this.visible = false;
              form.resetFields();
              this.$message.info("操作成功");
              // this.$emit('ok')
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        } else {
          this.confirmLoading = false;
        }
      });
    },
    transformPermissionTree(permissions, name = null) {
      const arr = [];
      if (!permissions || !permissions.some(v => v.parentName === name)) {
        return arr;
      }
      const parents = permissions.filter(v => v.parentName === name);
      // console.log(parents,'parents');
      for (const i in parents) {
        let label = "";
        if (this.permissionsQuery.providerName === "R") {
          label = parents[i].displayName;
        } else if (this.permissionsQuery.providerName === "U") {
          label =
            parents[i].displayName +
            " " +
            parents[i].grantedProviders.map(provider => {
              // return `${provider.providerName}: ${provider.providerKey}`;
              return "";
            });
        }
        arr.push({
          name: parents[i].name,
          key: parents[i].name,
          label,
          disabled: this.isGrantedByOtherProviderName(parents[i].grantedProviders),
          children: this.transformPermissionTree(permissions, parents[i].name),
        });
      }
      return arr;
    },

    isGrantedByOtherProviderName(grantedProviders) {
      if (grantedProviders.length) {
        return grantedProviders.findIndex(p => p.providerName !== this.permissionsQuery.providerName) > -1;
      }
      return false;
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-tabs {
  color: #000000;
}
/deep/.ant-form-item label {
  color: #000000;
}
/deep/.ant-tree li .ant-tree-node-content-wrapper {
  color: #000000;
}
.userRoleModal {
  /deep/ .ant-modal-content {
    .ant-modal-body {
      .ant-form-item {
        max-height: 600px;
        overflow-y: auto;
        margin: 0;
        overflow-x: hidden;
      }
    }
  }
}
</style>
