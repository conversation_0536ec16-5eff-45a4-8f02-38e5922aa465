<template>
  <a-spin :spinning="spinning" >
    <div class="box">
      <a-form-model layout="inline" style="width: 100%;margin-top:10px;" >       
          <a-row>
            <a-col :span="6">
              <a-form-model-item label="正负片信息" :label-col="{ span:8 }" :wrapper-col="{ span: 16 }" >
                <span v-if="!editFlg1">{{proOrderLineDto.posNegStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.posNeg" showSearch allowClear optionFilterProp="label" >
                  <a-select-option  v-for="(item) in mapKey(selectData.PosNeg)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="内基铜厚OZ" :label-col="{ span: 8}" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.inBaseCopperStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.inBaseCopper" showSearch allowClear optionFilterProp="label"  :disabled="disFlg">
                  <a-select-option  v-for="(item) in mapKey1(selectData.InBaseCopper)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="内完成铜厚OZ" class="require" :label-col="{ span: 8}" :wrapper-col="{ span: 16 }" >
                <span v-if="!editFlg1">{{proOrderLineDto.innerCopperThicknessStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.innerCopperThickness"  showSearch allowClear optionFilterProp="label" :disabled="disFlg">
                  <a-select-option  v-for="(item) in mapKey1(selectData.InnerCopperThickness)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="最小孔铜um" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.minHoleCopper}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.minHoleCopper"  showSearch allowClear optionFilterProp="label" >
                  <a-select-option  v-for="(item) in mapKey(selectData.MinHoleCu)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>               
                </div>
              </a-form-model-item>
            </a-col>
           
            
          </a-row>

          <a-row>  
            <a-col :span="6">
              <a-form-model-item label="线宽公差" :label-col="{ span: 8}" :wrapper-col="{ span: 16}" >
                <span v-if="!editFlg1">{{proOrderLineDto.lineWidthTolStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.lineWidthTol" showSearch allowClear optionFilterProp="label" >
                  <a-select-option  v-for="(item) in mapKey(selectData.LineWidthTol)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col>       
           
            <a-col :span="6">
              <a-form-model-item label="外基铜厚OZ" :label-col="{ span: 8}" :wrapper-col="{ span: 16}">
                <span v-if="!editFlg1">{{proOrderLineDto.outBaseCopperStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.outBaseCopper" showSearch allowClear optionFilterProp="label" >
                  <a-select-option  v-for="(item) in mapKey1(selectData.OutBaseCopper)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col> 
            <a-col :span="6">
              <a-form-model-item label="外完成铜厚OZ" class="require" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.copperThicknessStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.copperThickness" showSearch allowClear optionFilterProp="label">
                  <a-select-option  v-for="(item) in mapKey1(selectData.CopperThickness)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col>  
            <a-col :span="6">
              <a-form-model-item label="平均孔铜um" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.avgHoleCopper}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.avgHoleCopper"  showSearch allowClear optionFilterProp="label" >
                  <a-select-option  v-for="(item) in mapKey(selectData.HoleCuRequest)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>                
                </div>
              </a-form-model-item>
            </a-col>  
            
                        
          </a-row>
          

          <a-row>            
            <a-col :span="6">
              <a-form-model-item label="最小焊盘直径mil" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.minPadDiameter}}</span>
                <div  v-else>
                  <a-input v-model="proOrderLineDto.minPadDiameter" allowClear> </a-input>                  
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="3">
              <a-form-model-item label="金手指" :label-col="{ span: 16}" :wrapper-col="{ span: 8 }" >                
                <span v-if="!editFlg1">{{proOrderLineDto.isGoldfinger ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isGoldfinger"  @change="Goldfinger"> </a-checkbox>                  
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="3">
              <a-form-model-item label="撕引线" :label-col="{ span: 14}" :wrapper-col="{ span: 10}">
                <span v-if="!editFlg1">{{proOrderLineDto.isGoldfingerTearTheLead ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isGoldfingerTearTheLead" v-if="proOrderLineDto.isGoldfinger" > </a-checkbox> 
                  <a-checkbox v-model="proOrderLineDto.isGoldfingerTearTheLead" v-else disabled > </a-checkbox>                  
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="3">
              <a-form-model-item label="金手指倒斜边" :label-col="{ span:16}" :wrapper-col="{ span: 8 }">
                <span v-if="!editFlg1">{{proOrderLineDto.isGoldfingerBevel ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isGoldfingerBevel" v-if="proOrderLineDto.isGoldfinger" > </a-checkbox>   
                  <a-checkbox v-model="proOrderLineDto.isGoldfingerBevel" v-else disabled > </a-checkbox>                 
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="3">
              <a-form-model-item label="金手指镍厚" :label-col="{ span: 14 }" :wrapper-col="{ span: 10 }">
                <span v-if="!editFlg1">{{proOrderLineDto.goldfingerNickelThickness}}</span>
                <div  v-else>
                  <a-input v-model="proOrderLineDto.goldfingerNickelThickness" v-if="proOrderLineDto.isGoldfinger" allowClear > </a-input> 
                  <a-input v-model="proOrderLineDto.goldfingerNickelThickness" v-else disabled  > </a-input>                 
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="3">
              <a-form-model-item label="金手指印蓝胶" :label-col="{ span: 16 }" :wrapper-col="{ span: 8}">
                <span v-if="!editFlg1">{{proOrderLineDto.isBlueGum ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isBlueGum" v-if="proOrderLineDto.isGoldfinger" > </a-checkbox> 
                  <a-checkbox v-model="proOrderLineDto.isBlueGum" v-else disabled > </a-checkbox>                 
                </div>
              </a-form-model-item>
            </a-col> 
            <a-col :span="3">
              <a-form-model-item label="金手指金厚" :label-col="{ span:12 }" :wrapper-col="{ span: 12 }">
                <span v-if="!editFlg1">{{proOrderLineDto.goldFingerThicknessStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.goldFingerThickness" showSearch allowClear optionFilterProp="label" v-if="proOrderLineDto.isGoldfinger" 
                  @change="setEstimate($event,  mapKey(selectData.GoldFingerThickness))"
                  @search="handleSearch($event, mapKey(selectData.GoldFingerThickness))"
                  @blur="handleBlur($event,  mapKey(selectData.GoldFingerThickness))"
                  >
                    <a-select-option  v-for="(item) in mapKey(selectData.GoldFingerThickness)" :key="item.value" :value="item.value" :label="item.lable" >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select> 
                   
                  <a-select v-model="proOrderLineDto.goldFingerThickness" showSearch allowClear optionFilterProp="label" v-else disabled               
                 >
                    <a-select-option  v-for="(item) in mapKey(selectData.GoldFingerThickness)" :key="item.value" :value="item.value" :label="item.lable" >
                      {{ item.lable }}
                    </a-select-option>
                  </a-select>             
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="6">
              <a-form-model-item label="阻抗" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.isImpedance ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isImpedance" > </a-checkbox>                  
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="阻抗报告" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.impedanceReport}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.impedanceReport" style="width:170px" showSearch allowClear optionFilterProp="label">
                  <a-select-option  v-for="(item) in mapKey(selectData.ImpedanceReport)" :key="item.value" :value="item.value" :label="item.lable" >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="镀锡" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.isTinning ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isTinning" > </a-checkbox>                  
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="BGA直径mm" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">              
                <span v-if="!editFlg1">{{proOrderLineDto.bgaSize}}</span>
                <div  v-else>
                  <a-input v-model="proOrderLineDto.bgaSize" allowClear> </a-input>                  
                </div>
              </a-form-model-item>
            </a-col> 
             
                       
          </a-row>
          <a-row>
            <a-col :span="6">
              <a-form-model-item label='局部镀厚金U"' :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" >
                <span v-if="!editFlg1">{{proOrderLineDto.isLocalThickGoldPlating ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isLocalThickGoldPlating" > </a-checkbox>                  
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label='镀薄金厚度U"' :label-col="{ span: 8 }" :wrapper-col="{ span: 16}">
                <span v-if="!editFlg1">{{proOrderLineDto.goldPlatingThickness}}</span>
                <div  v-else>
                  <a-input v-model="proOrderLineDto.goldPlatingThickness" allowClear> </a-input>                  
                </div>
              </a-form-model-item>
            </a-col>  
            <a-col :span="6">
              <a-form-model-item label="贴红胶带" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" >
                <span v-if="!editFlg1">{{proOrderLineDto.isPasteRedTape ? '是':''}}</span>
                <div  v-else>
                  <a-checkbox v-model="proOrderLineDto.isPasteRedTape" > </a-checkbox>                  
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :span="6">
              <a-form-model-item label="外层特殊铜厚um" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.specialCuThickness}}</span>
                <div  v-else>
                  <a-input v-model="proOrderLineDto.specialCuThickness" allowClear> </a-input>                  
                </div>
              </a-form-model-item>
            </a-col>
            
                            
          </a-row>
          <!-- <a-row>          
            <a-col :span="6">
              <a-form-model-item label="表面处理" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <span v-if="!editFlg1">{{proOrderLineDto.surfaceFinishStr}}</span>
                <div  v-else>
                  <a-select v-model="proOrderLineDto.surfaceFinish" showSearch allowClear optionFilterProp="label">
                  <a-select-option  v-for="(item) in mapKey(selectData.SurfaceFinish)" :key="item.value" :value="item.value" :label="item.lable">
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
                </div>
              </a-form-model-item>
            </a-col>            
          </a-row> -->

          <a-row>
            <a-col :span="24">
            <a-form-model-item label="表面处理" class="require" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }" >
              <span v-if="!editFlg1" >
                {{proOrderLineDto.surfaceFinishStr}} 
                <span>受镀面积：{{proOrderLineDto.platedArea}}%</span>
                <span v-if="proOrderLineDto.surfaceFinish == 'immersiongold'">金厚：{{proOrderLineDto.surfaceFinishJsonDto.goldThickness}}U"</span>
                <span v-if="proOrderLineDto.surfaceFinish == 'haslwithlead' ">锡厚：{{proOrderLineDto.surfaceFinishJsonDto.newTinThickness}}um</span>
                <span v-if="proOrderLineDto.surfaceFinish == 'eletrolyticnickel' || proOrderLineDto.surfaceFinish == 'immersiongold'">镍厚：{{proOrderLineDto.surfaceFinishJsonDto.cjNickelThinckness}}"</span>
              </span>
              <div class="editWrapper" v-else style="display: flex;">
                <a-select v-model="proOrderLineDto.surfaceFinish" showSearch allowClear optionFilterProp="lable" @change="changcnj">
                  <a-select-option  v-for="(item,index) in mapKey(selectData.SurfaceFinish)" :key="index" :value="item.value" :lable="item.lable" >
                    {{ item.lable }}
                  </a-select-option>
                </a-select>
               <span style="color: red; margin: 0 5px" >受镀面积：<a-input style="display: inline-block;width: 60px" v-model="proOrderLineDto.platedArea"/>%</span>
                <span style="color: red;margin: 0 5px " v-if="proOrderLineDto.surfaceFinish == 'immersiongold'">金厚：
                  <a-select style="display: inline-block;width: 60px" v-model="proOrderLineDto.surfaceFinishJsonDto.goldThickness" showSearch allowClear optionFilterProp="lable" > 
                    <a-select-option  v-for="(item,index) in mapKey(selectData.ImGoldThinckness)" :key="index" :value="item.value" :lable="item.lable" >
                    {{ item.lable }}
                  </a-select-option>
                  </a-select>
                </span>
                <span style="color: red;margin:0 5px" v-if="proOrderLineDto.surfaceFinish == 'haslwithlead' ">
                  锡厚：<a-input style="display: inline-block;width: 60px" v-model="proOrderLineDto.surfaceFinishJsonDto.newTinThickness"/>um                                   
                </span>
                <span style="color: red;margin: 0 5px "  v-if="proOrderLineDto.surfaceFinish == 'immersiongold'">镍厚：
                  <a-select style="display: inline-block;width:180px" v-model="proOrderLineDto.surfaceFinishJsonDto.cjNickelThinckness" showSearch allowClear optionFilterProp="lable" 
                  @change="setEstimate1($event,  mapKey(selectData.CJNickelThinckness))"
                  @search="handleSearch1($event, mapKey(selectData.CJNickelThinckness))"
                  @blur="handleBlur1($event,  mapKey(selectData.CJNickelThinckness))"
                  > 
                    <a-select-option  v-for="(item,index) in mapKey(selectData.CJNickelThinckness)" :key="index" :value="item.value" :lable="item.lable" >
                    {{ item.lable }}
                  </a-select-option>
                  </a-select>
                </span>
                <!-- <span style="color: red;margin:  0 3px" v-if="proOrderLineDto.surfaceFinish == 'eletrolyticnickel'">
                  镍厚：<a-input style="display: inline-block;width: 60px" v-model="proOrderLineDto.surfaceFinishJsonDto.nickelThickness"/>
                </span> -->
                <span style="color: red;margin:  0 3px" v-if="proOrderLineDto.surfaceFinish == 'fullgoldplating'">
                  镀金厚度：<a-input style="display: inline-block;width: 60px" v-model="proOrderLineDto.surfaceFinishJsonDto.goldThickness"/>
                </span>
              </div>
            </a-form-model-item>
            </a-col>
           
          </a-row>
      </a-form-model>
      <div style="margin-top:10px;">
        <a-table
          :columns="columns1"
          :dataSource="proOrderLineDto.lineParameterDtos"
          :scroll="{ y: 486}"
          :pagination="false"
          :rowKey="(record, index) => `${index + 1}`"
          class="Tab1"
        >
        <template slot="minLineW_" slot-scope="text,record">
          <span v-if="!editFlg1">{{record.minLineW_}}</span>
          <div  v-else>
            <a-input v-model="record.minLineW_" allowClear>  </a-input>
          </div>
        </template>
        <template slot="minLineSpace_" slot-scope="text,record">
          <span v-if="!editFlg1">{{record.minLineSpace_}}</span>
          <div  v-else>
            <a-input v-model="record.minLineSpace_" allowClear >  </a-input>
          </div>
        </template>
        <template slot="hole2Copper_" slot-scope="text,record">
          <span v-if="!editFlg1">{{record.hole2Copper_}}</span>
          <div  v-else>
            <a-input v-model="record.hole2Copper_" allowClear>  </a-input>
          </div>
        </template>
        <template slot="bC_" slot-scope="text,record">
          <span v-if="!editFlg1">{{record.bC_}}</span>
          <div  v-else>
            <a-input v-model="record.bC_" allowClear>  </a-input>
          </div>
        </template>
        <template slot="copperRatio_" slot-scope="text,record">
          <span v-if="!editFlg1">{{record.copperRatio_}}</span>
          <div  v-else>
            <a-input v-model="record.copperRatio_" allowClear>  </a-input>
          </div>
        </template>
        <template slot="cu_" slot-scope="text,record">
          <span v-if="!editFlg1">{{record.cu_}}</span>
          <div  v-else>
            <a-input v-model="record.cu_" allowClear>  </a-input>
          </div>
        </template>
        <template slot="cuThickness_" slot-scope="text,record">
          <span v-if="!editFlg1">{{record.cuThickness_}}</span>
          <div  v-else>
            <a-input v-model="record.cuThickness_" allowClear>  </a-input>
          </div>
        </template>
      </a-table>
        
      </div>
      <div class="bto">
        <!-- <a-button @click="editClick1" type="primary" style="margin-top:20px;margin-right:10px;" v-if="!editFlg1" >编辑</a-button>
        <a-button @click="editClick1" type="primary" style="margin-top:20px;margin-right:10px;" v-else >取消</a-button>  
        <a-button @click="saveClick" type="primary" style="margin-top:20px;margin-right:10px;"  >保存</a-button> -->
      </div>
    </div>
  </a-spin>
</template>
<script>
import {
  lineInformation,
} from "@/services/projectIndicate";
  const  columns1=[
  {
      title: "序号",
      dataIndex: "index",
      key: "index",
      customRender: (text, record, index) => `${index + 1}`,
    },
    {
      title: "层",
      dataIndex: "layName_",
    },
    // {
    //   title: "物料",
    //   dataIndex: "",
    // },
    // {
    //   title: "型号",
    //   dataIndex: "",
    // },
    // {
    //   title: "介质",
    //   dataIndex: "",
    // },
    {
      title: "线宽",
      //slots: { title: 'customTitle' },
      scopedSlots: { customRender: 'minLineW_' },
      className: "select-sty",
      ellipsis: true,
    },
    {
      title: "间距",
      scopedSlots: { customRender: 'minLineSpace_' },
      className: "select-sty",
      ellipsis: true,
    },
    {
      title: "孔到线",
      scopedSlots: { customRender: 'hole2Copper_' },
      className: "select-sty",
      ellipsis: true,
    },
    {
      title: "补偿",
      scopedSlots: { customRender: 'bC_' },
      className: "select-sty",
      ellipsis: true,
    },
    {
      title: "残铜率",
      scopedSlots: { customRender: 'copperRatio_' },
      className: "select-sty",
      ellipsis: true,
    },
    {
      title: "基铜",
      scopedSlots: { customRender: 'cu_' },
      className: "select-sty",
      ellipsis: true,
    },
    {
      title: "完成铜厚",  
      scopedSlots: { customRender: 'cuThickness_' },
      className: "select-sty",
      ellipsis: true,   
    },
    {
      title: "原始层名",
      dataIndex: "orgLayName_",
    },
  ];
  
export default{
    name:'lineInfo',
    props:['proOrderLineDto','selectData','editFlg1','disFlg',],
    data(){
        return{
          spinning:false,        
          columns1,
          dataSource1:[],
              
        }
    },
    methods:{
      changcnj(){
        if(this.proOrderLineDto.surfaceFinish == 'immersiongold' && !this.proOrderLineDto.surfaceFinishJsonDto.cjNickelThinckness){                 
          this.proOrderLineDto.surfaceFinishJsonDto.cjNickelThinckness = "120-150u"
          }
          if(this.proOrderLineDto.surfaceFinish == 'immersiongold' && !this.proOrderLineDto.surfaceFinishJsonDto.goldThickness){                 
                    this.proOrderLineDto.surfaceFinishJsonDto.goldThickness = 1
          }
      },
      editClick1(){
        this.editFlg1 = !this.editFlg1
        if(!this.editFlg1){
          this.$emit('GetProOrderInfo')
        }
      },
      mapKey(data){
        if (!data) {
          return []
        } else {          
          return Object.keys(data).map(item => {          
            return {'value':item, 'lable': data[item]}
          })
        }
      },
      mapKey1(data){       
        if (!data) {
          return []
        } else {         
          return Object.keys(data).sort().map(item => {
            return {'value':item, 'lable': data[item]}
          })
        }
      },
      getPrice(item, list, value) {
        let a = { Price: value }
        for (let i = 0; i < list.length; i++) {
          if (list[i].item == item) {
            let Price = list[i].Price == '' ? value : list[i].Price
            a.Price = Price
          }
        }
        return a
        },
      setEstimate(value,  list) {
        console.log('金手指金厚 ')
        this.proOrderLineDto.goldFingerThickness = value
        let a = this.getPrice(this.proOrderLineDto.goldFingerThickness, list, value)
        console.log('金手指金厚',a,this.proOrderLineDto.goldFingerThickness)
      },
      handleSearch(value, list) {
        this.setEstimate(value,  list)
      },
      handleBlur(value,  list) {
        this.setEstimate(value, list)
      },
      setEstimate1(value,  list) {
        console.log('镍 ')
        this.proOrderLineDto.surfaceFinishJsonDto.cjNickelThinckness = value
        let a = this.getPrice(this.proOrderLineDto.surfaceFinishJsonDto.cjNickelThinckness, list, value)
        console.log('厚',a,this.proOrderLineDto.surfaceFinishJsonDto.cjNickelThinckness)
        
      },
      handleSearch1(value, list) {
        this.setEstimate1(value,  list)
      },
      handleBlur1(value,  list) {
        this.setEstimate1(value, list)
      },
      saveClick(){
        if(!this.editFlg1){
          this.$message.warning('非编辑状态不可保存')
          return
        }
        let params = this.proOrderLineDto
        params  = JSON.parse(JSON.stringify(params).replace(/lineParameterDtos/g, 'lineParameterInputs')) 
        let arr = params.lineParameterInputs
        if(arr){
          arr.forEach(item => {
          item.bC_ = Number(item.bC_)  
          item.minLineW_ = Number(item.minLineW_) 
          item.minLineSpace_ = Number(item.minLineSpace_) 
          item.hole2Copper_ = Number(item.hole2Copper_)       
          });
        }        
        params.lineParameterInputs = arr   
        if(params.impedanceReport == '需要'){
          params.impedanceReport = 1
        }else{
          params.impedanceReport = 0
        }   
        params.innerCopperThickness = Number(params.innerCopperThickness)  
        params.copperThickness = Number(params.copperThickness)
        params.invoice = Number(params.invoice) 
        console.log('params',params.lineParameterInputs)
        lineInformation(params).then(res=>{
          if(res.code){
            this.$emit('GetProOrderInfo')
            this.$message.success('保存成功')
            this.editFlg1 = false
          }else{
            this.$message.error(res.message)
          }
        })
      },
      Goldfinger(){
       if(!this.proOrderLineDto.isGoldfinger){
        this.proOrderLineDto.isGoldfingerTearTheLead = false
        this.proOrderLineDto.isGoldfingerBevel = false
        this.proOrderLineDto.goldfingerNickelThickness = null
        this.proOrderLineDto.isBlueGum = false
        this.proOrderLineDto.goldFingerThickness = null
       }
      },
    
      // minPadDiameter(){
      //   if(!this.proOrderLineDto.minPadDiameter){
      //     this.proOrderLineDto.minPadDiameter=null
      //   }

      // },
      // bgaSize(){
      //   if(!this.proOrderLineDto.bgaSize){
      //     this.proOrderLineDto.bgaSize=null
      //   }

      // }
    }
}
</script>
<style scoped lang="less">
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f0f2f5;
}
/deep/.require{
  .ant-form-item-label > label {
    color: red!important;
}
}
 .box{
    // height:760px;
    height:590px; 
    position: relative;
    .bto{
      position:absolute;
      bottom:-48px;
      right:374px;
    }
  }
  /deep/.ant-table-row{
  .ant-select {
    width: 65px;
    height:24px;
  }
  .ant-checkbox-wrapper{
        height:20px;
        line-height: 20px;
      }
      
     .ant-select-selection--single{
        height:24px;       
      }
     .ant-select-selection__rendered{
        line-height: 24px;
      }
      .ant-input{
        height:24px;
      }
}
/deep/.select-sty{
    padding: 2px 16px!important;
  }
  /deep/.ant-form {
    border-left:1px solid #ddd;
    border-top:1px solid #ddd;
  }
  /deep/  .ant-table-thead > tr > th{
    padding: 4px 16px;
  }
  /deep/ .ant-table-tbody > tr > td{
    padding: 4px 16px;
  }
    /deep/  .ant-form-item {
        margin: 0;
        width: 100%;
        display: flex;

        .editWrapper {
          display: flex;
          align-items: center;
          min-height: 28px;
          .ant-select {
            width:252px;
          }
          .ant-input {
            width: 120px;
          }
          .ant-input-number {
            width: 120px;
          }
        }
        .ant-form-item-label {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          color:#000000;
          font-family: PingFangSC-Regular,Sans-serif;
          font-size:14px;
          color: #666;
          background-color: #fafafa;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
          label {
          color:#000000;
          font-family: PingFangSC-Regular,Sans-serif;
          font-size:14px;
          }
        }
        .ant-form-item-control-wrapper {
          color:#000000;
          font-family: PingFangSC-Regular,Sans-serif;
          font-size:14px;
          .ant-form-item-control {

            .ant-form-item-children {
              display: block;
              min-height: 28px;
              line-height: 26px;
              .ant-checkbox-wrapper{
                height:28px;
                
              }
              .ant-select-selection--single{
                height:28px;
              }
              .ant-select-selection__rendered{
                line-height: 28px;
              }
              .ant-select{
                height:28px;
              }
              .ant-input{
                height:28px;
              }
            }
            line-height: inherit;
            padding: 2px 5px;
            border-right: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
          }
        }
      }
    
</style>