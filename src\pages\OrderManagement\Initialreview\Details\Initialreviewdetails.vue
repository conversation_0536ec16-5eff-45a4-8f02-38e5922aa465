<!--供应商-客诉初审-初审详情-->
<template>
    <div class="Initialreviewdetails">
        <a-collapse :activeKey="'1'" @change="CollapseList">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>投诉详情</div>
                        <div>{{ text }}</div>
                    </div>
                </template>
            <div>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'客户型号'">
                            <a-input disabled v-model="complaintdata.customerModel"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'联系人'">
                            <a-input disabled v-model="complaintdata.factoryContactName"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'联系电话'">
                            <a-input disabled v-model="complaintdata.factoryContactNumber"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'投诉日期'">
                            <a-input disabled v-model="complaintdata.complaintDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'生产型号'">
                            <a-input disabled v-model="complaintdata.proOrderNo"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货日期'">
                            <a-input disabled v-model="complaintdata.deliveryDate"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'交货总数'" >
                            <a-input disabled v-model="complaintdata.deliveryNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'不良总数'">
                            <a-input disabled v-model="complaintdata.badNum"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'周期'" >
                            <a-input disabled v-model="complaintdata.dateCode"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'问题描述'" >
                            <a-textarea  disabled v-model="complaintdata.problemDescription" :auto-size="{ minRows: 4, maxRows:6 }"></a-textarea >
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <span style="color: #428bca;cursor: pointer;" @click="filedown(complaintdata.verdictFilePath)" > 
                                <a-icon type="link" style="padding: 0 5px;"></a-icon>{{ complaintdata.verdictFileName }}
                        </span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'不良批次要求采取的措施'" >
                            <a-radio-group   disabled v-model="complaintdata.takeSteps" name="radioGroup">
                                <a-radio :value="1">退货返工</a-radio>
                                <a-radio :value="2">报废补货</a-radio>
                                <a-radio :value="3">报废扣款</a-radio>
                                <a-radio :value="4">特采</a-radio>
                                <a-radio :value="5">其他</a-radio>
                            </a-radio-group>
                            <div v-show="complaintdata.takeSteps=='2' || complaintdata.takeSteps=='1'">
                                <div v-show="complaintdata.takeSteps=='2'">补货板收货地址及收货人信息：（地址，姓名隔开）</div>
                                <div v-show="complaintdata.takeSteps=='1'">返工 OK 板收货地址及收货人信息：（地址，姓名隔开）</div>
                                <div style="display: flex;justify-content: space-around;">
                                    <a-input placeholder="姓名" v-model="complaintdata.receiveContactName" style="width: 200px;" disabled >
                                        <template #prefix>
                                            <a-icon type="user" style="color:red" />
                                        </template>
                                    </a-input>
                                    <a-input placeholder="电话" v-model="complaintdata.receiveContactNumber" style="width: 280px;" disabled>
                                        <template #prefix>
                                            <a-icon type="phone" style="color:red" />
                                        </template>
                                    </a-input>
                                    <a-input placeholder="地址" v-model="complaintdata.receiveAddress" style="width: 422px;" disabled>
                                        <template #prefix>
                                            <a-icon type="home" style="color:red" />
                                        </template>
                                    </a-input>
                                </div>    
                            </div> 
                            <a-input style="width: 484px;"  disabled v-model="complaintdata.takeStepsName" v-show="complaintdata.takeSteps=='5'"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-show="complaintdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废品形态'" >
                            <a-radio-group  disabled v-model="complaintdata.scrapType" name="radioGroup" >
                                <a-radio value="PCB">PCB</a-radio>
                                <a-radio value="PCBA">PCBA</a-radio>
                                <a-radio value="整机">整机</a-radio>
                                <a-radio value="其它">其它</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'报废品数量描述'" >
                            <a-input disabled v-model="complaintdata.scrapNumDetail"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>   
                <a-row v-show="complaintdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'不良板成本(元)'" >
                            <a-input disabled v-model="complaintdata.scrapCost"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'扣款总金额(元)'" >
                            <a-input disabled v-model="complaintdata.deductMoney"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row> 
                <a-row v-show="complaintdata.takeSteps=='3'">
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" :label="'报废总成本(元)'" >
                            <a-input v-model="complaintdata.scrapCost" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>  
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :label="'是否需要8D报告'" >
                            <a-radio-group  disabled v-model="complaintdata.need8DReport" name="radioGroup" >
                                <a-radio :value="false">否</a-radio>
                                <a-radio :value="true">是</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>    
                    <a-col :span="16" >
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'要求改善报告回复客户时间'" >
                            <a-radio-group   disabled v-model="complaintdata.needReplytime" name="radioGroup">
                                <a-radio :value="3">3天</a-radio>
                                <a-radio :value="2">2天</a-radio>
                                <a-radio :value="1">1天</a-radio>                              
                                <a-radio :value="0">其他</a-radio>
                            </a-radio-group>
                            <a-input v-model="complaintdata.needReplytimeName" disabled style="width: 100px;"></a-input>
                        </a-form-model-item>
                    </a-col>             
                </a-row>
                <a-row>
                   
                </a-row>
            </div>
            </a-collapse-panel>
        </a-collapse>
        <a-collapse :activeKey="'1'" @change="CollapseList1" style="margin-top: 15px;">
            <a-collapse-panel key="1">
                <template #header >
                    <div style="text-align: left;display: flex;display: flex;justify-content: space-between;width: 99%;" >
                        <div>厂商初审</div>
                        <div>{{ text1 }}</div>
                    </div>
                </template>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'评审结果'" >
                            <a-radio-group  v-model="complaintdata.factoryReview"  name="radioGroup" :disabled="statusbar!=30"  >
                                <a-radio :value="0">不属实</a-radio>
                                <a-radio :value="1">属实,我司责任</a-radio>
                            </a-radio-group>
                        </a-form-model-item>
                    </a-col>  
                </a-row>
                <a-row>
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'备注说明'"   >
                            <a-textarea   v-model="complaintdata.factoryReviewRemark" :auto-size="{ minRows: 4, maxRows:6 }" :disabled="statusbar!=30"></a-textarea >
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-show="complaintdata.factoryReview==1">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'解决方案'" >
                            <a-radio-group  @change="reviewchange"  name="radioGroup" v-model="complaintdata.reviewTakeSteps" :disabled="statusbar!=30" >
                                <a-radio :value="1">退货返工</a-radio>
                                <a-radio :value="2">补货</a-radio>
                                <a-radio :value="3">报废</a-radio>
                                <a-radio :value="4">特采</a-radio>
                                <a-radio :value="5">其他</a-radio>
                            </a-radio-group>
                            <a-input style="width: 300px;" v-model="complaintdata.reviewTakeStepsName" :disabled="complaintdata.reviewTakeSteps!=5 || statusbar!=30"></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-show="complaintdata.factoryReview==1">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :label="'备注情由'" >
                            <a-textarea  v-model="complaintdata.factoryRemark"  :auto-size="{ minRows: 4, maxRows:6 }" :disabled="statusbar!=30"></a-textarea >
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-if="!filepath && statusbar==30 && complaintdata.factoryReview==1">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'8D报告'" :class="complaintdata.need8DReport?'required':''">
                            <a-upload  
                            ref="fileRef"
                            :file-list="fileList"
                            :before-upload="beforeUpload"
                            :customRequest="httpRequest"
                            accept=".jpg,.bmp,.png,.zip,.rar,.7z"
                            @change="handleChange">
                            <a-button v-if="fileList.length==0" > <a-icon type="upload"/> 选择文件</a-button>
                            <span style="padding:0 15px;" v-if="fileList.length==0" >未上传任何附件</span>
                            </a-upload>                           
                        <div style="color: #ff9900;font-size: 12px;">温馨提示：附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式，文件大小不超过10M。</div>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-else-if="fileList.length==0 && statusbar==30 && complaintdata.factoryReview==1">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <span style="color: #428bca;cursor: pointer;" @click="filedown(complaintdata.need8dReportPath)"> 
                                <a-icon type="link" style="padding: 0 5px;" ></a-icon>8d报告文件
                            </span>  
                            <span  style="padding-left: 20px;" @click="deletefile" >
                                <a-tooltip title="该操作不可撤销，删除后可重新上传附件"><a-icon type="delete"></a-icon></a-tooltip>                               
                            </span>               
                        <div style="color: #ff9900;font-size: 12px;">温馨提示：附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式，文件大小不超过10M。</div>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-else-if="statusbar!=30 && complaintdata.factoryReview==1">
                    <a-col :span="16">
                        <a-form-model-item :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" :label="'附件'" >
                            <span style="color: #428bca;cursor: pointer;" @click="filedown(complaintdata.need8dReportPath)"> 
                                <a-icon type="link" style="padding: 0 5px;" ></a-icon>8d报告文件
                            </span>  
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row style="margin-top: 15px;">
                    <a-col :span="16">
                            <a-button type="primary" style="margin-left: 185px;" @click="handleOk" :disabled="statusbar!=30">确定</a-button>
                            <a-button type="normal" style="margin-left: 30px;" @click="back">返回</a-button>
                    </a-col>
                </a-row>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>
<script>
import {factorybyid,uploadcomplaintsfile,factoryfirstreview} from "@/services/complaint/QualitativeComplaint.js";
export default {
    name:'Initialreviewdetails',
    props:[],
    data() {
        return {
            text:'收起',
            text1:'收起',
            isFileType:false,
            fileList:[],
            filepath:null,
            complaintdata:{},
            statusbar:'',
        }
    },
    mounted(){
        factorybyid(this.$route.query.id).then(res=>{
            if(res.code){
                this.complaintdata=res.data
                this.filepath=JSON.parse(JSON.stringify(res.data)).need8dReportPath
                if(this.statusbar==30 && this.complaintdata.factoryReview!==0){
                    this.complaintdata.factoryReview=1
                }
            }
        })    
    },
    methods:{
        filedown(path){         
            if(path){
                window.location.href = path
            }else{
                this.$message.error('暂无附件下载')
            }
        },
        deletefile(){
            this.complaintdata.need8dReportPath=null
            this.filepath=null
        },
        handleChange({ fileList },data) { 
         if( this.isFileType){
            this.fileList = fileList;
            if(this.fileList.length > 1){
            this.fileList.splice(0,1)
            return
            }
        }
        },
        async httpRequest(data,type) {
            const formData = new FormData();
            formData.append("file", data.file);
            await uploadcomplaintsfile(formData).then(res => {
                if (res.code == 1) {
                    data.onSuccess(res.data);
                    this.complaintdata.need8dReportPath=res.data
                } else {
                    this.$message.error(res.message)
                }
            })
            },
        beforeUpload(file){
            this.isFileType = file.name.toLowerCase().indexOf('.zip') != -1 || file.name.toLowerCase().indexOf('.rar') != -1||
            file.name.toLowerCase().indexOf('.jpg') != -1 || file.name.toLowerCase().indexOf('.bmp') != -1||
            file.name.toLowerCase().indexOf('.png') != -1 || file.name.toLowerCase().indexOf('.7z') != -1
                if (!this.isFileType) {
                    this.$message.error('附件支持JPG/BMP/PNG图片格式和ZIP/RAR/7Z压缩包格式,文件大小不超过10M');
                    return false
                }
        },
        back(){
            this.$router.push({path:'Initialreview',query:{} })
        },
        handleOk(){
            if(this.complaintdata.need8DReport && !this.complaintdata.need8dReportPath &&  this.complaintdata.factoryReview==1){
                this.$message.error('请上传8D报告')
                return
            }
            if(this.complaintdata.factoryReview===0){
                this.complaintdata.need8dReportPath=''
                this.complaintdata.reviewTakeSteps=0
                this.complaintdata.reviewTakeStepsName=''
                this.complaintdata.factoryRemark=''
            }
            factoryfirstreview(this.complaintdata).then(res=>{
                if(res.code){
                    this.$message.success(res.message)
                    this.$router.push({path:'Initialreview',query:{}})
                }else{
                    this.$message.error(res.message)                
                }
            })
        },  
        CollapseList(val){
            if(val.length){
                this.text = '收起'
            }else{
                this.text = '展开'
            }
        },
        CollapseList1(val){
            if(val.length){
                this.text1 = '收起'
            }else{
                this.text1 = '展开'
            }
        },
        reviewchange(){
            if(this.complaintdata.reviewTakeSteps!=5){
                this.complaintdata.reviewTakeStepsName=''
            }
        }
    },
    created(){
        this.statusbar=this.$route.query.statusbar   
    }
}
</script>
<style lang="less" scoped>
.required{
    /deep/.ant-form-item-label label{
        color: red !important;
    }
}
.Initialreviewdetails{
    padding: 10px;
    overflow: auto;
    height: 821px;
    background-color: white;
    border: 1px solid #e8e8e8;
    &::-webkit-scrollbar {
        width: 6px; 
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
        background: #ff9900;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 0;
        background: #ffffff;
      }
    /deep/.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
    right: 16px !important;
    left: auto;
    }
    /deep/.ant-radio-disabled .ant-radio-inner::after {
        background-color: #ff9900;
    }
    /deep/.ant-radio-disabled.ant-radio-inner {
        background-color: #ffffff;
        border-color: #d9d9d9  !important;
        cursor: not-allowed;
    }
    /deep/.ant-radio-disabled + span {
        color: black;
        cursor: not-allowed;
    }
    /deep/.ant-radio-checked .ant-radio-inner{
        border-color: #ff9900  !important;
    }
    /deep/.ant-divider-horizontal {
        display: block;
        clear: both;
        width: 100%;
        min-width: 100%;
        height: 1px;
        margin: 13px 0;
    }
    /deep/.ant-form-item{
        margin-bottom: 0;
    }
}

</style>