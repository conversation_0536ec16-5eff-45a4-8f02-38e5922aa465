<!-- 市场管理 - 业务跟单 -->
<template>
  <a-spin :spinning="spinning">
    <div class="BusinessTracking" ref="BusinessTracking">
      <div style="width: 100%; display: flex">
        <div class="lefttable">
          <left-table-make
            :leftcolumns="leftcolumns"
            :dataSource="orderListData"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            @tableChange="handleTableChange"
            :pagination="pagination"
            ref="orderTable"
            @gettopdata="gettopdata"
            :orderListload="orderListload"
            :class="orderListData.length ? 'min-table' : ''"
            @OperationLog="OperationLog"
            @getDetailInfo="getDetailInfo"
          >
          </left-table-make>
        </div>
        <div class="righttable">
          <div class="toptable">
            <a-table
              :columns="topcolumns"
              :dataSource="topdata"
              :scroll="{ y: 150 }"
              :loading="loading2"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
              :customRow="onClickRow2"
              :rowClassName="isRedRow2"
            >
              <template slot="radio" slot-scope="text, record">
                <span>
                  <a-radio :checked="record.isProduction" style="height: 26px"> </a-radio>
                </span>
              </template>
            </a-table>
          </div>
          <div class="centertable">
            <a-table
              :columns="centercolumns"
              :customRow="onClickRow3"
              :rowClassName="isRedRow3"
              :dataSource="centerdata"
              :loading="loading3"
              :scroll="{ y: 288 }"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
            ></a-table>
          </div>
          <div class="bottable">
            <a-table
              :columns="botcolumns"
              :dataSource="botdata"
              :scroll="{ y: 216 }"
              :loading="loading4"
              :rowKey="
                (record, index) => {
                  return index;
                }
              "
              :pagination="false"
            ></a-table>
          </div>
        </div>
      </div>
      <div class="footeraction">
        <make-action
          @queryClick="queryClick"
          @followup="followup"
          @endclick="endclick"
          @quotationcontract="quotationcontract"
          @quotationClick="quotationClick"
          @quotationinformation="quotationinformation"
          ref="action"
        />
      </div>
    </div>
    <!-- 报价信息导出弹窗-->
    <a-modal
      title="报价信息导出"
      :visible="infoVisible"
      @cancel="infoVisible = false"
      @ok="handleOkinfo"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <a-form-item label="开始时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
        <a-date-picker format="YYYY-MM-DD" @change="Start" v-model="StartTime" style="width: 100%"></a-date-picker>
      </a-form-item>
      <a-form-item label="结束时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
        <a-date-picker format="YYYY-MM-DD" @change="End" :disabled="StartTime ? false : true" v-model="EndTime" style="width: 100%"></a-date-picker>
      </a-form-item>
      <a-form-model-item label="跟单员" :labelCol="{ span: 6 }" :wrapperCol="{ span: 15, offset: 1 }">
        <a-select v-model="checkaccount" allowClear showSearch optionFilterProp="lable" style="width: 100%">
          <a-select-option v-for="(ite, index) in accounts" :key="index" :value="ite.valueMember" :lable="ite.text"> {{ ite.text }}</a-select-option>
        </a-select>
      </a-form-model-item>
    </a-modal>
    <!-- 查询弹窗 -->
    <a-modal
      title="订单查询"
      :visible="dataVisible"
      @cancel="reportHandleCancel"
      @ok="handleOk"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <query-info ref="queryInfo" :accounts="accounts" :Quoterlist="Quoterlist" />
    </a-modal>
    <!-- 跟进数据 -->
    <a-modal
      title="跟进数据"
      :visible="dataVisible1"
      @cancel="reportHandleCancel"
      @ok="handleOk1"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="800"
      class="genjin"
      centered
    >
      <a-row>
        <a-col :span="6">
          <a-form-model-item label="价格原因" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <a-checkbox v-model="followupdata.isPriceReason"></a-checkbox>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item label="交期原因" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <a-checkbox v-model="followupdata.isDeliveryTimeReason"></a-checkbox>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item label="仅询价" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <a-checkbox v-model="followupdata.isInquireReason"></a-checkbox>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item label="其他" :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
            <a-checkbox v-model="followupdata.isOtherReason"></a-checkbox>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item
            label="跟进内容"
            :class="followupdata.isOtherReason ? 'required' : ''"
            v-if="followupdata.isOtherReason"
            :label-col="{ span: 3 }"
            :wrapper-col="{ span: 20 }"
          >
            <a-textarea
              style="font-weight: 500; width: 680px"
              v-model="followupdata.followRemarks"
              placeholder="请输入跟进内容"
              :auto-size="{ minRows: 5, maxRows: 8 }"
              :autoFocus="true"
            />
          </a-form-model-item>
        </a-col>
      </a-row>

      <div class="projectackend" style="margin-top: 15px">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
        </a-table>
      </div>
    </a-modal>
    <!-- 确认弹窗 -->
    <a-modal
      title="确认弹窗"
      :visible="dataVisible2"
      @cancel="reportHandleCancel"
      @ok="handleOk2"
      ok-text="确定"
      destroyOnClose
      :maskClosable="false"
      :width="400"
      centered
    >
      <span>确认完结所选订单吗?</span>
    </a-modal>
    <!-- 操作日志弹窗 -->
    <a-modal title="操作日志" :visible="labordataVisible" destroyOnClose @cancel="reportHandleCancel" :maskClosable="false" :width="800" centered>
      <div class="projectackend">
        <a-table
          :columns="laborcolumns"
          :dataSource="labordata"
          :pagination="false"
          :scroll="{ y: 541 }"
          :rowKey="
            (record, index) => {
              return index;
            }
          "
        >
        </a-table>
      </div>
      <template slot="footer">
        <a-button type="primary" @click="reportHandleCancel">取消</a-button>
      </template>
    </a-modal>
    <!--预审信息-->
    <a-modal title="预审信息" :visible="PredataVisible" destroyOnClose ref="preform" :width="900" centered class="yushen">
      <template slot="footer">
        <a-button type="primary" @click="reportHandleCancel">关闭</a-button>
      </template>
      <prequalification-info ref="editForm" v-if="JSON.stringify(showData) != '{}'" :showData="showData" :tabtype="'1'"></prequalification-info>
    </a-modal>
    <!--邮件信息弹窗-->
    <a-modal title="邮件信息" :visible="MaildataVisible" destroyOnClose :mask="false" :width="700" centered class="mailcalss">
      <span style="padding-left: 7px; font-weight: bold; font-size: 20px"> {{ showTitle }}</span>
      <a-button type="primary" style="float: right" @click="reportHandleCancel">关闭</a-button>
      <a-divider />
      <div class="drawre" v-html="messageList" style="background-color: #ffffff; border: 1px solid #a3a3a3"></div>
      <a-divider />
      <div class="img-box">
        <ul>
          <li
            v-for="(item, index) in attList"
            style="position: relative; user-select: none"
            :title="item.split('attid')[0]"
            :key="index"
            class="liback"
            @click.right="rightClick1($event, item, index)"
          >
            <img v-if="item.toLowerCase().includes('pdf')" style="width: 25px; padding-left: 5px" src="@/assets/icon/pdf.png" />
            <img
              v-else-if="
                item.toLowerCase().includes('jpg') ||
                item.toLowerCase().includes('png') ||
                item.toLowerCase().includes('bmp') ||
                item.toLowerCase().includes('jpeg')
              "
              style="width: 25px; padding-left: 5px"
              src="@/assets/icon/jpg.png"
            />
            <img
              v-else-if="item.toLowerCase().includes('xlsx') || item.toLowerCase().includes('xls')"
              style="width: 25px; padding-left: 5px"
              src="@/assets/icon/12.png"
            />
            <img v-else-if="item.toLowerCase().includes('txt')" style="width: 25px; padding-left: 5px" src="@/assets/icon/txt.png" />
            <img v-else-if="item.toLowerCase().includes('tif')" style="width: 25px; padding-left: 5px" src="@/assets/icon/tiff.png" />
            <img
              v-else-if="item.toLowerCase().includes('docx') || item.toLowerCase().includes('doc')"
              style="width: 25px; padding-left: 5px"
              src="@/assets/icon/docx.png"
            />
            <img
              v-else-if="item.toLowerCase().includes('zip') || item.toLowerCase().includes('rar')"
              style="width: 25px; padding-left: 5px"
              src="@/assets/icon/zip.png"
            />
            <img v-else-if="item.toLowerCase().includes('csv')" style="width: 25px; padding-left: 5px" src="@/assets/icon/csv.png" />
            <img v-else style="width: 25px; padding-left: 5px" src="@/assets/icon/inf.png" />
            <span>
              {{ item.split("attid")[0] }}
            </span>
          </li>
        </ul>
      </div>
      <a-menu :style="menuStyle1" v-if="menuVisible1" class="tabRightClikBox">
        <a-menu-item v-if="Multiple" @click="download">下载附件</a-menu-item>
      </a-menu>
    </a-modal>
    <a-modal title="检查信息" :visible="dataVisible22" @cancel="reportHandleCancel" destroyOnClose :maskClosable="false" :width="600" centered>
      <template #footer>
        <a-button key="back1" type="primary" v-if="check1" @click="checkClick">继续</a-button>
        <a-button key="back" @click="reportHandleCancel">取消</a-button>
      </template>
      <div class="class" style="font-size: 16px; font-weight: 500">
        <p v-for="(item, index) in checkData1" :key="index">
          <span v-if="item.error == '1'" style="color: red">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-else-if="item.warn == '1'" style="color: CornflowerBlue">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
          <span v-else style="color: black">
            <a-icon type="star"></a-icon>
            <span>{{ item.caption }}:</span>
            <span>{{ item.result }}!</span>
          </span>
        </p>
        <p style="color: black" v-if="check1"><a-icon type="star" style="color: black"></a-icon>检查通过!</p>
      </div>
    </a-modal>
    <!-- 红马销售合同-->
    <a-modal
      title="销售合同"
      :visible="hmVisible"
      @cancel="reportHandleCancelRep"
      @ok="handleOkhm"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="1250"
    >
      <report-infohm
        :ttype="'EMS | 业务跟单'"
        :hmsalesdata="hmsalesdata"
        :joinFactoryId="joinFactoryId"
        ref="reporthm"
        :salescustno="salescustno"
        :ContractNoSech="ContractNoSech"
        :act="'dis'"
        @hmform="hmform"
      />
    </a-modal>
    <!--雅信达 联合多层销售合同-->
    <a-modal
      title="销售合同"
      :visible="yxdVisible"
      @cancel="reportHandleCancelRep"
      @ok="handleOkyxd"
      ok-text="下载"
      destroyOnClose
      centered
      :maskClosable="false"
      class="formclass"
      :width="1250"
    >
      <template slot="footer">
        <a-button @click="reportHandleCancelRep">取消</a-button>
        <a-button type="primary" @click="handleOkyxd">下载</a-button>
      </template>
      <report-infoyxd
        :ttype="'EMS | 业务跟单'"
        :yxddata="yxddata"
        :joinFactoryId="joinFactoryId"
        ref="reportyxd"
        :salescustno="salescustno"
        :act="'dis'"
        @getOrderList="getOrderList"
        :ContractNoSech="ContractNoSech"
        @YXDform="YXDform"
      />
    </a-modal>
    <!-- 精焯销售合同 -->
    <a-modal
      title="销售合同"
      :visible="JZsalesvisible"
      @cancel="reportHandleCancelRep"
      @ok="salescontractdown"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="1250"
    >
      <report-jzsalescontract ref="jzsalescontract" :JZsalesdata="JZsalesdata" :ttype="'EMS | 业务跟单'" :act="'dis'" />
    </a-modal>
    <!-- 奔强销售合同 -->
    <a-modal
      title="销售合同"
      :visible="BQsalesvisible"
      @cancel="reportHandleCancelRep"
      @ok="bqsalescontractdown"
      ok-text="下载"
      destroyOnClose
      centered
      :maskClosable="false"
      class="formclass1"
      :width="1250"
    >
      <report-bqsalescontract
        ref="bqsalescontract"
        :ttype="'EMS | 业务跟单'"
        :act="'dis'"
        :BQsalesdata="BQsalesdata"
        @BQsalescontract="BQsalescontract"
        :salescustno="salescustno"
      />
    </a-modal>
    <!--雅信达报价表单-->
    <a-modal
      title="报价表单"
      :visible="YXDreportvisible"
      @cancel="reportHandleCancelRep"
      @ok="yxdreportdown"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="1250"
    >
      <report-yxdreportform1098 ref="yxdreportform1098" :YXDreportdata="YXDreportdata" :ttype="'EMS | 业务跟单'" v-if="salescustno == '1098'" />
      <report-yxdreportform1151 ref="yxdreportform1151" :YXDreportdata="YXDreportdata" :ttype="'EMS | 业务跟单'" v-else-if="salescustno == '1151'" />
      <report-yxdreportform1508
        ref="yxdreportform1508"
        :YXDreportdata="YXDreportdata"
        :ttype="'EMS | 业务跟单'"
        v-else-if="salescustno == '1508' || salescustno == '1508A'"
      />
      <report-yxdreportform1083 ref="yxdreportform1083" :YXDreportdata="YXDreportdata" :ttype="'EMS | 业务跟单'" v-else-if="salescustno == '1083'" />
      <report-yxdreportform ref="yxdreportform" :YXDreportdata="YXDreportdata" :ttype="'EMS | 业务跟单'" v-else />
    </a-modal>
    <!--奔强报价表单-->
    <a-modal
      title="报价表单"
      :visible="BQreportvisible"
      @cancel="reportHandleCancelRep"
      @ok="bqreportdown"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="1250"
    >
      <report-bqreportform ref="bqreportform" :ttype="'EMS | 业务跟单'" :Bqreportdata="Bqreportdata" />
    </a-modal>
    <!--明天高新报价表单-->
    <a-modal
      title="报价表单"
      :visible="Mtgxreportvisible"
      @cancel="Mtgxreportvisible = false"
      @ok="mtgxreportdown"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="1650"
    >
      <report-mtgxreportform ref="mtgxreportform" :ttype="'EMS | 业务跟单'" :Mtgxreportdata="Mtgxreportdata" />
    </a-modal>
    <!--龙腾报价表单-->
    <a-modal
      title="报价表单"
      :visible="LTreport"
      @cancel="LTreport = false"
      @ok="ltreportdown(formtypes)"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="
        formtypes == 'ltreportform0972'
          ? 1800
          : formtypes == 'ltreportform0477'
          ? 1200
          : formtypes == 'ltreportform0849' || formtypes == 'ltreportform1220' || formtypes == 'ltreportform0100'
          ? 1650
          : 1250
      "
    >
      <report-ltdefaultreport ref="ltdefaultreport" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltdefaultreport'" />
      <report-ltreportform0072 ref="ltreportform0072" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform0072'" />
      <report-ltreportform1099 ref="ltreportform1099" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform1099'" />
      <report-ltreportform0271 ref="ltreportform0271" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform0271'" />
      <report-ltreportform0972 ref="ltreportform0972" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform0972'" />
      <report-ltreportform1170 ref="ltreportform1170" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform1170'" />
      <report-ltreportform1206 ref="ltreportform1206" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform1206'" />
      <report-ltreportform0477 ref="ltreportform0477" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform0477'" />
      <report-ltreportform0849 ref="ltreportform0849" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform0849'" />
      <report-ltreportform1034 ref="ltreportform1034" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform1034'" />
      <report-ltreportform1220 ref="ltreportform1220" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform1220'" />
      <report-ltreportform1032 ref="ltreportform1032" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform1032'" />
      <report-ltreportform0100 ref="ltreportform0100" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform0100'" />
      <report-ltreportform0041 ref="ltreportform0041" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform0041'" />
      <report-ltreportform1244 ref="ltreportform1244" :LTreportdata="LTreportdata" :ttype="'EMS | 业务跟单'" v-if="formtypes == 'ltreportform1244'" />
    </a-modal>
    <!--精焯报价表单-->
    <a-modal
      title="报价表单"
      :visible="JZreportvisible"
      @cancel="reportHandleCancelRep"
      @ok="reportdown"
      ok-text="下载"
      destroyOnClose
      :maskClosable="false"
      class="formclass"
      :width="widthy1"
    >
      <report-jzreportform ref="jzreportform" :selectlength="selectlength" :JZreportdata="JZreportdata" :ttype="'EMS | 业务跟单'" />
    </a-modal>
  </a-spin>
</template>

<script>
import * as XLSX_STYLE from "xlsx-style";
import * as XLSX from "xlsx";
import moment from "moment";
import ReportJzreportform from "@/pages/mkt/OrderOffer/report/ReportJzreportform";
import ReportYxdreportform from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform";
import ReportYxdreportform1098 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1098";
import ReportYxdreportform1151 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1151";
import ReportYxdreportform1508 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1508";
import ReportYxdreportform1083 from "@/pages/mkt/OrderOffer/Yxdreport/ReportYxdreportform1083";
import ReportBqreportform from "@/pages/mkt/OrderOffer/report/ReportBqreportform";
import ReportMtgxreportform from "@/pages/mkt/OrderOffer/report/ReportMtgxreportform";
import ReportLtreportform0271 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0271";
import ReportLtreportform0972 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0972";
import ReportLtreportform0477 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0477";
import ReportLtreportform0849 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0849";
import ReportLtreportform1034 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1034";
import ReportLtreportform1170 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1170";
import ReportLtreportform1206 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1206";
import ReportLtreportform1099 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1099";
import ReportLtreportform1220 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1220";
import ReportLtreportform1032 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1032";
import ReportLtreportform1244 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform1244";
import ReportLtreportform0100 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0100";
import ReportLtreportform0041 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0041";
import ReportLtdefaultreport from "@/pages/mkt/OrderOffer/Ltreport/ReportLtdefaultreport";
import ReportLtreportform0072 from "@/pages/mkt/OrderOffer/Ltreport/ReportLtreportform0072";
import ReportJzsalescontract from "@/pages/mkt/OrderOffer/report/ReportJzsalescontract";
import ReportBqsalescontract from "@/pages/mkt/OrderOffer/report/ReportBqsalescontract";
import ReportInfoyxd from "@/pages/mkt/OrderOffer/report/ReportInfoyxd";
import ReportInfohm from "@/pages/mkt/OrderOffer/report/ReportInfohm";
import { buttonCheck } from "@/services/mkt/OrderManagement.js";
import {
  result4ParameterList,
  ltQuotationEXLE,
  yxDOrderpriceEXLE,
  resultList,
  resultDetailList,
  getcontractinfobygrp,
  contractNo,
  frontcontractreport,
  orderformreport,
  verifyQuotation,
  getQuotationInfoByGrp,
  yxDQuotationEXLE,
  checkuserlist,
  quotationmodel,
} from "@/services/mkt/OrderReview.js";
import { Base64 } from "js-base64";
import { getEditOrderInfo } from "@/services/mkt/orderInfo";
import PrequalificationInfo from "@/pages/mkt/OrderOffer/module/PrequalificationInfo";
import {
  businessverifyPageList,
  followremarks,
  updatedocumentarystatus,
  businessdocumentary,
  checkaccounts,
} from "@/services/mkt/BusinessTracking.js";
import LeftTableMake from "@/pages/mkt/BusinessTracking/module/LeftTableMake";
import QueryInfo from "@/pages/mkt/BusinessTracking/module/QueryInfo";
import MakeAction from "@/pages/mkt/BusinessTracking/module/MakeAction";
import { emimeconent, downloadbyattid } from "@/services/mkt/MailList";
import { param } from "jquery";
const leftcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    customRender: (text, record, index) => `${index + 1}`,
    width: 45,
    ellipsis: true,
    fixed: "left",
  },
  {
    title: "上传时间",
    dataIndex: "createTime",
    align: "left",
    ellipsis: true,
    width: 150,
    fixed: "left",
  },
  {
    title: "客户代码",
    dataIndex: "custNo",
    align: "left",
    fixed: "left",
    ellipsis: true,
    width: 65,
    sorter: (a, b) => {
      return a.custNo.localeCompare(b.custNo);
    },
  },
  {
    title: "客户型号",
    align: "left",
    ellipsis: true,
    fixed: "left",
    dataIndex: "customerModel",
    width: 325,
  },
  {
    title: "订单类型",
    dataIndex: "reOrder",
    align: "left",
    ellipsis: true,
    width: 70,
    customRender: (text, record, index) => `${record.reOrder == 0 ? "新单" : record.reOrder == 1 ? "返单" : "返单更改"}`,
    sorter: (a, b) => {
      return a.reOrder.toString().localeCompare(b.reOrder.toString());
    },
  },
  {
    title: "生产型号",
    dataIndex: "proOrderNo",
    align: "left",
    ellipsis: true,
    width: 180,
    sorter: (a, b) => {
      return a.proOrderNo.localeCompare(b.proOrderNo);
    },
  },
  {
    title: "层数",
    dataIndex: "boardLayers",
    align: "left",
    ellipsis: true,
    width: 45,
  },
  {
    title: "业务员",
    align: "left",
    dataIndex: "ywName",
    ellipsis: true,
    width: 80,
  },
  {
    title: "总金额",
    width: 100,
    align: "left",
    ellipsis: true,
    dataIndex: "totalAmountPrice",
  },
  {
    title: "SU",
    dataIndex: "su",
    align: "left",
    width: 45,
    ellipsis: true,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 70,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加急费",
    width: 100,
    align: "left",
    ellipsis: true,
    dataIndex: "expeditePrice",
  },
  {
    title: "交货日期",
    align: "left",
    dataIndex: "deliveryDate1",
    ellipsis: true,
    width: 110,
    sorter: (a, b) => {
      if (a.deliveryDate1 === null) {
        return 1;
      }
      if (b.deliveryDate1 === null) {
        return -1;
      }
      return a.deliveryDate1.localeCompare(b.deliveryDate1);
    },
  },
  {
    title: "报价人",
    dataIndex: "checkName",
    width: 65,
    ellipsis: true,
    align: "left",
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    width: 80,
    ellipsis: true,
    align: "left",
  },
  {
    title: "合同币种",
    width: 70,
    align: "center",
    dataIndex: "currencyStr",
    ellipsis: true,
  },
  {
    title: "操作",
    width: 70,
    align: "center",
    class: "noCopy",
    scopedSlots: { customRender: "labelUrl" },
  },
  {
    title: "预审信息",
    fixed: "right",
    width: 70,
    ellipsis: true,
    align: "center",
    scopedSlots: { customRender: "sales" },
  },
];
const topcolumns = [
  {
    title: "序号",
    customRender: (text, record, index) => `${index + 1}`,
    key: "index",
    align: "center",
    width: 37,
  },
  {
    title: "下单",
    scopedSlots: { customRender: "radio" },
    width: 37,
    ellipsis: true,
    align: "center",
    className: "inputClass",
  },
  {
    title: "数量",
    dataIndex: "para4DelQty_",
    width: 50,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "提前",
    dataIndex: "para4UrgentDate_",
    width: 40,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "面积",
    dataIndex: "para4Area_",
    ellipsis: true,
    width: 55,
    align: "left",
  },
  {
    title: "交期",
    ellipsis: true,
    dataIndex: "para4IntDelivery_",
    width: 37,
    align: "left",
  },
  {
    title: "交货日期",
    dataIndex: "para4Delivery_",
    width: 110,
    className: "inputClass",
    ellipsis: true,
    align: "left",
  },
  {
    title: "单价",
    dataIndex: "pcsPrice_",
    width: 55,
    ellipsis: true,
    align: "left",
    className: "inputClass",
  },
  {
    title: "重量(KG)",
    dataIndex: "para4Weight_",
    width: 80,
    align: "left",
  },
];
const centercolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    dataIndex: "upOrDownPrice",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
  },
  {
    title: "实际报价",
    width: 58,
    ellipsis: true,
    align: "left",
    dataIndex: "actualPrice",
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
const botcolumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    align: "center",
    width: 35,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "价格说明",
    dataIndex: "priceName",
    width: 88,
    ellipsis: true,
    align: "left",
  },
  {
    title: "标准报价",
    dataIndex: "standardPrice",
    width: 58,
    ellipsis: true,
    align: "left",
  },
  {
    title: "加减价",
    dataIndex: "upOrDownPrice",
    width: 48,
    align: "left",
    ellipsis: true,
    className: "inputClass",
  },
  {
    title: "实际报价",
    width: 58,
    ellipsis: true,
    align: "left",
    dataIndex: "actualPrice",
  },
  {
    title: "美元价",
    dataIndex: "usdPrice",
    width: 60,
    align: "left",
    ellipsis: true,
  },
  {
    title: "港币价",
    dataIndex: "hkdPrice",
    width: 65,
    align: "left",
    ellipsis: true,
  },
];
const laborcolumns = [
  {
    title: "序号",
    align: "center",
    dataIndex: "index",
    key: "index",
    width: 20,
    customRender: (text, record, index) => `${index + 1}`,
  },
  {
    title: "操作时间",
    align: "left",
    dataIndex: "createTime",
    width: 65,
  },
  {
    title: "操作人",
    align: "left",
    dataIndex: "userName",
    width: 30,
  },
  {
    title: "内容",
    align: "left",
    dataIndex: "followRemarks",
    width: 185,
  },
];
export default {
  name: "BusinessTracking",
  components: {
    LeftTableMake,
    MakeAction,
    QueryInfo,
    PrequalificationInfo,
    ReportJzreportform,
    ReportYxdreportform,
    ReportYxdreportform1151,
    ReportYxdreportform1508,
    ReportYxdreportform1083,
    ReportYxdreportform1098,
    ReportBqreportform,
    ReportLtreportform1220,
    ReportLtreportform0271,
    ReportLtreportform0972,
    ReportLtreportform0477,
    ReportLtreportform0849,
    ReportMtgxreportform,
    ReportLtdefaultreport,
    ReportLtreportform1099,
    ReportLtreportform1032,
    ReportJzsalescontract,
    ReportBqsalescontract,
    ReportInfoyxd,
    ReportInfohm,
    ReportLtreportform0072,
    ReportLtreportform1034,
    ReportLtreportform1170,
    ReportLtreportform1206,
    ReportLtreportform1244,
    ReportLtreportform0100,
    ReportLtreportform0041,
  },
  data() {
    return {
      formtypes: "ltdefaultreport",
      checkData1: [], // 指示检查数据
      infoVisible: false,
      StartTime: moment().subtract(1, "months").format("YYYY-MM-DD"),
      EndTime: moment().format("YYYY-MM-DD"),
      checkaccount: "",
      check1: false,
      Quoterlist: [],
      checkType: "",
      ContractNoSech: "",
      salescustno: "",
      modelVisible: false, // 报表弹窗,
      yxdVisible: false,
      hmVisible: false,
      menuVisible: false,
      JZsalesvisible: false,
      BQsalesvisible: false,
      yxdids: [],
      JZsalesdata: {},
      hmsalesdata: {},
      yxddata: {},
      BQsalesdata: {},
      JZreportdata: {},
      Mtgxreportdata: {},
      Mtgxreportvisible: false,
      YXDreportdata: {},
      JZreviewdata: {},
      Bqreportdata: {},
      LTreportdata: {},
      LTreport: false,
      JZreviewvisible: false,
      JZreportvisible: false,
      YXDreportvisible: false,
      BQreportvisible: false,
      selectlength: 0,
      widthy1: 0,
      dataVisible22: false,
      joinFactoryId: "",
      menuStyle1: {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
      },
      attList: [], //附件列表
      messageList: "",
      attid: "",
      mailid: "",
      menuVisible1: false,
      Multiple: false,
      showTitle: "",
      MaildataVisible: false,
      spinning: false,
      showData: {},
      PredataVisible: false,
      isCtrlPressed: false,
      dataVisible: false,
      dataVisible1: false,
      dataVisible2: false,
      labordataVisible: false,
      followupdata: {
        followRemarks: "",
        isPriceReason: false,
        isDeliveryTimeReason: false,
        isInquireReason: false,
        isOtherReason: false,
      },
      iscolumnKey: "",
      isorder: "",
      leftcolumns,
      topcolumns,
      centercolumns,
      laborcolumns,
      id: "",
      botcolumns,
      orderListData: [],
      loading2: false,
      loading3: false,
      loading4: false,
      orderListload: false,
      topdata: [],
      centerdata: [],
      botdata: [],
      labordata: [],
      guid4Parameter: "",
      accounts: [],
      pagination: {
        pageSize: 20,
        current: 1,
        size: "small",
        total: 0,
        showQuickJumper: false,
        showSizeChanger: true,
        pageSizeOptions: ["20", "50", "100"], //每页中显示的数据
        showTotal: total => `总计 ${total} 条`,
      },
    };
  },
  created() {
    checkuserlist().then(res => {
      if (res.code) {
        this.Quoterlist = res.data;
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydown, true);
    window.removeEventListener("keyup", this.keyup, true);
  },
  mounted() {
    this.getOrderList();
    this.getaccounts();
    window.addEventListener("keydown", this.keydown, true);
    window.addEventListener("keyup", this.keyup, true);
  },
  methods: {
    Start(value, dateString) {
      this.StartTime = dateString;
      if (!dateString) {
        this.EndTime = null;
      }
    },
    End(value, dateString) {
      this.EndTime = dateString;
    },
    getaccounts() {
      checkaccounts().then(res => {
        if (res.code) {
          this.accounts = res.data;
        }
      });
    },
    checkClick() {
      this.dataVisible22 = false;
      let data = this.$refs.orderTable.selectedRowsData;
      if (this.checkType == "xsht") {
        contractNo(this.$refs.orderTable.selectedRowKeysArray).then(res => {
          if (res.code) {
            if (this.joinFactoryId == "58" || this.joinFactoryId == "59" || this.joinFactoryId == "65" || this.joinFactoryId == "38") {
              this.YXDform();
            } else if (this.joinFactoryId == "22") {
              this.JZsalescontract();
            } else if (this.joinFactoryId == "69") {
              this.hmform();
            } else if (this.joinFactoryId == "12") {
              this.BQsalescontract();
            } else {
              getcontractinfobygrp(this.$refs.orderTable.selectedRowKeysArray).then(res => {
                if (res.code) {
                  this.downloadByteArrayFromString(res.data, res.message);
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          } else {
            this.$message.error(res.message);
          }
        });
      }
      if (this.checkType == "bjd") {
        if (data.factoryName == "勤基") {
          quotationInfo(this.$refs.orderTable.proOrderId).then(res => {
            if (res.code == 1) {
              this.reportData = res.data;
              let val = this.reportData.priceDtos;
              for (var a = 0; a < val.length; a++) {
                if (val[a].para4Weight_) {
                  val[a].para4Weight_ = Number(val[a].para4Weight_).toFixed(2);
                }
              }
              if (this.reportData.custModuleNoDto.currency == "rmb") {
                this.key = "2";
              } else {
                this.key = "1";
              }
              this.modelVisible = true;
            } else {
              this.$message.error(res.message);
            }
          });
        } else if (data.joinFactoryId == "22") {
          this.JZreportform();
        } else if (data.joinFactoryId == "58" || data.joinFactoryId == "59") {
          this.YXDreportform();
        }
        // else if(data.joinFactoryId=='12' && (data.custNo.indexOf('W')==-1&& data.custNo.indexOf('Y')==-1)|| data.custNo=='W115' || data.custNo=='W116' || data.custNo=='W072'){
        //   this.BQreportform()
        // }
        else {
          getQuotationInfoByGrp(this.$refs.orderTable.selectedRowKeysArray).then(res => {
            if (res.code) {
              this.downloadByteArrayFromString(res.data, res.message);
            } else {
              this.$message.error(res.message);
            }
          });
        }
      }
    },
    YXDform(ids, type, factoryId, custno) {
      //雅信达 晶美 联合多层 销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0 && type != "YXD") {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      this.yxdids = id;
      if (id.length) {
        if (this.orderListData.filter(ite => ite.id == id[0].toLowerCase())[0]) {
          this.joinFactoryId = this.orderListData.filter(ite => ite.id == id[0].toLowerCase())[0].joinFactoryId;
          this.salescustno = this.orderListData.filter(ite => ite.id == id[0].toLowerCase())[0].custNo;
        } else {
          this.joinFactoryId = factoryId;
          this.salescustno = custno;
        }
      }
      frontcontractreport(id).then(res => {
        if (res.code) {
          this.ContractNoSech = 1;
          this.yxddata = res.data;
          this.yxdVisible = true;
        } else {
          if (ids && ids.length == 0) {
            this.yxdVisible = false;
          } else {
            this.$message.error(res.message);
          }
        }
      });
    },
    MTGXreportform() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.Mtgxreportdata = res.data;
          this.Mtgxreportvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    JZreportform() {
      //精焯报价表单 除806客户代码
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.JZreportdata = res.data;
          this.JZreportvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
      this.selectlength = this.$refs.orderTable.selectedRowKeysArray.length;
      this.widthy1 = this.selectlength == "1" ? 850 : 1250;
    },
    BQreportform() {
      //奔强报价表单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 5) {
        this.$message.warning("打印报价表单最多选择五条");
        return;
      }
      orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.Bqreportdata = res.data;
          this.BQreportvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    LTreportform() {
      //龙腾报价表单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      this.spinning = true;
      contractNo(this.$refs.orderTable.selectedRowKeysArray).then(item => {
        if (item.code) {
          quotationmodel(this.$refs.orderTable.selectedRowKeysArray[0]).then(val => {
            if (val.code) {
              if (val.data == "类型1") {
                let params = {};
                params.type = val.data;
                params.Id = this.$refs.orderTable.selectedRowKeysArray;
                ltQuotationEXLE(params)
                  .then(res => {
                    if (res.code) {
                      this.downloadByteArrayFromString(res.data, res.message);
                    } else {
                      this.$message.error(res.message);
                    }
                  })
                  .finally(() => {
                    this.spinning = false;
                  });
              } else {
                orderformreport(this.$refs.orderTable.selectedRowKeysArray)
                  .then(res => {
                    if (res.code) {
                      this.LTreportdata = res.data;
                      this.LTreport = true;
                      if (val.data == null || val.data == "") {
                        this.formtypes = "ltdefaultreport";
                      } else {
                        this.formtypes = "ltreportform" + val.data;
                      }
                    } else {
                      this.$message.error(res.message);
                    }
                  })
                  .finally(() => {
                    this.spinning = false;
                  });
              }
            } else {
              this.spinning = false;
            }
          });
        } else {
          this.spinning = false;
          this.$message.error(item.message);
        }
      });
    },
    YXDreportform() {
      //雅信达报价表单
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      quotationmodel(this.$refs.orderTable.selectedRowKeysArray[0]).then(res => {
        if (res.code) {
          if (res.data == null || res.data == "") {
            orderformreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
              if (res.code) {
                this.YXDreportdata = res.data;
                this.YXDreportvisible = true;
                this.salescustno = this.$refs.orderTable.selectedRowsData.custNo;
              } else {
                this.$message.error(res.message);
              }
            });
          } else {
            let params = {};
            params.type = res.data;
            params.Id = this.$refs.orderTable.selectedRowKeysArray;
            yxDQuotationEXLE(params).then(res => {
              if (res.code) {
                this.downloadByteArrayFromString(res.data, res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          }
        }
      });
    },
    JZsalescontract() {
      //精焯销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      frontcontractreport(this.$refs.orderTable.selectedRowKeysArray).then(res => {
        if (res.code) {
          this.JZsalesdata = res.data;
          this.JZsalesvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    BQsalescontract(ids) {
      //奔强销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      frontcontractreport(id).then(res => {
        if (res.code) {
          this.salescustno = this.orderListData.filter(ite => ite.id == this.$refs.orderTable.selectedRowKeysArray[0].toLowerCase())[0].custNo;
          this.ContractNoSech = 1;
          this.BQsalesdata = res.data;
          this.BQsalesvisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    hmform(ids) {
      //红马销售合同
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要打印的订单");
        return;
      }
      let id = ids ? ids : this.$refs.orderTable.selectedRowKeysArray;
      frontcontractreport(id).then(res => {
        if (res.code) {
          this.salescustno = this.orderListData.filter(ite => ite.id == this.$refs.orderTable.selectedRowKeysArray[0])[0].custNo;
          this.ContractNoSech = 1;
          this.hmsalesdata = res.data;
          this.hmVisible = true;
        } else {
          if (ids && ids.length == 0) {
            this.hmVisible = false;
          } else {
            this.$message.error(res.message);
          }
        }
      });
    },
    salescontractdown() {
      this.$refs.jzsalescontract.getsalesPdf();
    },
    bqsalescontractdown() {
      getcontractinfobygrp(this.$refs.bqsalescontract.ids).then(res => {
        if (res.code) {
          this.downloadByteArrayFromString(res.data, res.message);
        } else {
          this.$message.error(res.message);
        }
      });
      //this.$refs.bqsalescontract.getsalesPdf()
    },
    reportdown() {
      this.$refs.jzreportform.getreportPdf();
      this.JZreportvisible = false;
    },
    yxdreportdown() {
      if (this.salescustno == "1098") {
        this.$refs.yxdreportform1098.getreportPdf();
      } else if (this.salescustno == "1151") {
        this.$refs.yxdreportform1151.getreportPdf();
      } else if (this.salescustno == "1508") {
        this.$refs.yxdreportform1508.getreportPdf();
      } else if (this.salescustno == "1083") {
        this.$refs.yxdreportform1083.getreportPdf();
      } else {
        this.$refs.yxdreportform.getreportPdf();
      }
    },
    bqreportdown() {
      this.$refs.bqreportform.getreportPdf();
    },
    ltreportdown(reportType) {
      this.$refs[reportType].getreportPdf();
    },
    mtgxreportdown() {
      this.$refs.mtgxreportform.getreportPdf();
    },
    handleOkyxd() {
      if (this.joinFactoryId == 58 || this.joinFactoryId == 59) {
        if (this.yxdids && this.yxdids.length == 0) {
          this.yxdVisible = false;
        } else {
          quotationmodel(this.yxdids[0]).then(res => {
            if (res.code) {
              if (res.data == null || res.data == "") {
                this.$refs.reportyxd.getReportPdf();
              } else {
                let params = {};
                params.type = res.data;
                params.Id = this.yxdids;
                yxDOrderpriceEXLE(params).then(res => {
                  if (res.code) {
                    this.downloadByteArrayFromString(res.data, res.message);
                  } else {
                    this.$message.error(res.message);
                  }
                });
              }
            }
          });
        }
      } else {
        this.$refs.reportyxd.getReportPdf();
      }

      //this.yxdVisible = false
    },
    handleOkhm() {
      this.$refs.reporthm.getReportPdf();
    },
    //报价信息
    quotationinformation() {
      this.StartTime = moment().subtract(1, "months").format("YYYY-MM-DD");
      this.EndTime = moment().format("YYYY-MM-DD");
      this.checkaccount = "";
      this.infoVisible = true;
    },
    //报价单
    quotationClick() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.checkType = "bjd";
      let data = this.$refs.orderTable.selectedRowsData;
      this.spinning = true;
      verifyQuotation(this.$refs.orderTable.selectedRowKeysArray)
        .then(res => {
          if (res.code) {
            if (data.custNo == "806" && data.joinFactoryId == "22") {
              jz806EXLE(this.$refs.orderTable.selectedRowKeysArray).then(res => {
                if (res.code) {
                  if (JSON.parse(res.data).length) {
                    let _self = this;
                    let str = "";
                    let cutStr = "";
                    const arr = _self.$refs.orderTable.selectedRowKeysArray;
                    for (var a = 0; a < arr.length; a++) {
                      if (
                        _self.orderListData.filter(item => {
                          return item.id == arr[a];
                        }).length
                      ) {
                        if (str == "") {
                          str = _self.orderListData.filter(item => {
                            return item.id == arr[a];
                          })[0].pcbFileName;
                        } else {
                          str =
                            str +
                            " + " +
                            _self.orderListData.filter(item => {
                              return item.id == arr[a];
                            })[0].pcbFileName;
                        }
                        cutStr =
                          "(" +
                          _self.orderListData.filter(item => {
                            return item.id == arr[a];
                          })[0].custNo +
                          ")";
                      }
                    }
                    this.exportExcelFile(JSON.parse(res.data), "表1", str + cutStr + ".xlsx");
                  } else {
                    this.$message.error("暂无数据");
                  }
                } else {
                  this.$message.error(res.message);
                }
              });
            } else if (data.joinFactoryId == "22") {
              this.JZreportform();
            } else if (data.joinFactoryId == "70") {
              this.MTGXreportform();
            } else if (data.joinFactoryId == "58" || data.joinFactoryId == "59") {
              this.YXDreportform();
            }
            // else if(data.joinFactoryId=='12' && (data.custNo.indexOf('W')==-1&& data.custNo.indexOf('Y')==-1)|| data.custNo=='W115' || data.custNo=='W116' || data.custNo=='W072'){
            //   this.BQreportform()
            // }
            else if (this.$refs.orderTable.selectedRowsData.joinFactoryId == "67") {
              this.LTreportform();
            } else {
              this.checkClick();
            }
          } else {
            this.checkData1 = res.data;
            this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
            this.dataVisible22 = true;
            this.checkType = "bjd";
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    quotationcontract() {
      if (!this.$refs.orderTable.selectedRowKeysArray[0]) {
        this.$message.warning("请选择订单");
        return;
      }
      this.joinFactoryId = this.$refs.orderTable.selectedRowsData.joinFactoryId;
      buttonCheck(this.$refs.orderTable.proOrderId, "VerifyContract")
        .then(res => {
          if (res.code) {
            if (res.data && res.data.length) {
              this.checkData1 = res.data;
              this.check1 = this.checkData1.findIndex(v => v.error == "1") < 0;
              this.checkType = "xsht";
              this.dataVisible22 = true;
            } else {
              contractNo(this.$refs.orderTable.selectedRowKeysArray).then(res => {
                if (res.code) {
                  if (this.joinFactoryId == "58" || this.joinFactoryId == "59" || this.joinFactoryId == "65" || this.joinFactoryId == "38") {
                    this.YXDform();
                  } else if (this.joinFactoryId == "22") {
                    this.JZsalescontract();
                  } else if (this.joinFactoryId == "69") {
                    this.hmform();
                  } else if (this.joinFactoryId == "12") {
                    this.BQsalescontract();
                  } else {
                    getcontractinfobygrp(this.$refs.orderTable.selectedRowKeysArray).then(res => {
                      if (res.code) {
                        this.downloadByteArrayFromString(res.data, res.message);
                      } else {
                        this.$message.error(res.message);
                      }
                    });
                  }
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    downloadByteArrayFromString(byteArrayString, fileName) {
      const byteCharacters = atob(byteArrayString);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/octet-stream" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);
    },
    rightClick1(e, item, index) {
      e.preventDefault();
      this.menuVisible1 = true;
      this.attid = item.split("attid")[1];
      this.Multiple = true;
      this.menuStyle1.top = e.clientY - 110 + "px";
      this.menuStyle1.left = e.clientX - 380 + "px";
      document.body.addEventListener("click", this.bodyClick1);
    },
    bodyClick1() {
      this.menuVisible1 = false;
      (this.menuStyle1 = {
        position: "absolute",
        top: "0",
        left: "0",
        border: "1px solid #eee",
        zIndex: 99,
      }),
        document.body.removeEventListener("click", this.bodyClick1);
    },
    download() {
      downloadbyattid(this.mailid, this.attid).then(res => {
        if (res.code) {
          if (res.data) {
            var base64String = res.data;
            var binaryString = atob(base64String);
            var binaryLen = binaryString.length;
            var bytes = new Uint8Array(binaryLen);
            for (var i = 0; i < binaryLen; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }
            var blob = new Blob([bytes], { type: "application/octet-stream" });
            var url = URL.createObjectURL(blob);
            var a = document.createElement("a");
            document.body.appendChild(a);
            a.style = "display: none";
            a.href = url;
            a.download = res.message; // 这里可以自定义文件名和扩展名
            a.click();
            URL.revokeObjectURL(url);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getmail(record) {
      this.attList = [];
      this.mailid = record.mailId;
      emimeconent(record.mailId).then(res => {
        if (res.code) {
          this.showTitle = res.data.subject;
          this.messageList = Base64.decode(res.data.htmlBody);
          res.data.atts.forEach(ite => {
            this.attList.push(ite.attName + "attid" + ite.id);
          });
          this.MaildataVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getDetailInfo(id) {
      this.spinning = true;
      getEditOrderInfo(id)
        .then(res => {
          if (res.code) {
            this.showData = res.data;
            this.PredataVisible = true;
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    OperationLog(record, type) {
      if (type == "journal") {
        this.labordataVisible = true;
      }
      let params = {
        pcbOrderId: record.id,
      };
      businessdocumentary(params).then(res => {
        if (res.code) {
          this.labordata = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    keydown(e) {
      if (e.key === "Control") {
        this.isCtrlPressed = true;
      }
      if (e.keyCode == "70" && this.isCtrlPressed) {
        e.preventDefault();
        this.queryClick();
        this.reportHandleCancel();
        this.dataVisible = true;
        this.isCtrlPressed = false;
      } else if (e.keyCode == "13" && !this.isCtrlPressed && this.dataVisible) {
        this.handleOk();
        this.isCtrlPressed = false;
        e.preventDefault();
      } else if (e.keyCode == "13" && this.dataVisible2) {
        this.handleOk2();
        this.isCtrlPressed = false;
        e.preventDefault();
      }
    },
    queryClick() {
      this.dataVisible = true;
    },
    followup() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要跟进的订单");
        return;
      }
      if (this.$refs.orderTable.selectedRowKeysArray.length > 1) {
        this.$message.warning("只能选择一条订单进行跟进");
        return;
      }
      this.dataVisible1 = true;
      this.OperationLog(this.$refs.orderTable.selectedRowsData, "reply");
      this.followupdata = {
        followRemarks: "",
        isPriceReason: false,
        isDeliveryTimeReason: false,
        isInquireReason: false,
        isOtherReason: false,
      };
    },
    endclick() {
      if (this.$refs.orderTable.selectedRowKeysArray.length == 0) {
        this.$message.warning("请选择需要跟进的订单");
        return;
      }
      this.dataVisible2 = true;
    },
    handleOk2() {
      updatedocumentarystatus(this.$refs.orderTable.selectedRowKeysArray)
        .then(res => {
          if (res.code) {
            this.$message.success("操作成功");
            this.getOrderList();
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.dataVisible2 = false;
        });
    },
    handleOk1() {
      let val = this.followupdata;
      if (!val.isOtherReason) {
        val.followRemarks = "";
      }
      if (!val.isOtherReason && !val.followRemarks && !val.isPriceReason && !val.isDeliveryTimeReason && !val.isInquireReason) {
        this.$message.warning("请选择跟进原因");
        return;
      }
      if (!val.followRemarks && val.isOtherReason) {
        this.$message.warning("请填写跟进内容");
        return;
      }
      val.isDeliveryTimeReason = Number(val.isDeliveryTimeReason);
      val.isInquireReason = Number(val.isInquireReason);
      val.isOtherReason = Number(val.isOtherReason);
      val.isPriceReason = Number(val.isPriceReason);
      let params = {
        pcbOrderId: this.$refs.orderTable.selectedRowKeysArray[0],
        ...val,
      };
      followremarks(params)
        .then(res => {
          if (res.code) {
            this.$message.success("跟进成功");
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.dataVisible1 = false;
        });
    },
    reportHandleCancel() {
      this.dataVisible = false;
      this.dataVisible1 = false;
      this.dataVisible2 = false;
      this.labordataVisible = false;
      this.PredataVisible = false;
      this.dataVisible22 = false;
    },
    reportHandleCancelRep(e) {
      this.modelVisible = false;
      this.yxdVisible = false;
      this.hmVisible = false;
      this.JZsalesvisible = false;
      this.BQsalesvisible = false;
      this.JZreportvisible = false;
      this.JZreviewvisible = false;
      this.YXDreportvisible = false;
      this.BQreportvisible = false;
    },
    handleOkinfo() {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: 10000,
        Status: 20,
      };
      params.StartTime = this.StartTime;
      params.EndTime = this.EndTime;
      params.checkaccount = this.checkaccount;
      businessverifyPageList(params).then(res => {
        if (res.code && res.data.items.length > 0) {
          let data = [];
          res.data.items.forEach((val, index) => {
            data.push({
              序号: index,
              上传时间: val.createTime,
              客户代码: val.custNo,
              客户型号: val.customerModel,
              订单类型: val.reOrder == 0 ? "新单" : val.reOrder == 1 ? "返单" : "返单更改",
              生产型号: val.proOrderNo,
              层数: val.boardLayers,
              SU: val.su,
              状态: val.status,
              总金额: val.totalAmountPrice,
              加急费: val.expeditePrice,
              交货日期: val.deliveryDate1,
              报价人: val.checkName,
              业务员: val.ywName,
              订单来源: val.orderSource,
              合同币种: val.currencyStr,
            });
          });
          this.exportExcel(data, "表1", "报价信息.xlsx");
        } else {
          if (!res.code) {
            this.$message.error(res.message);
          } else if (res.data.items.length == 0) {
            this.$message.error("暂无数据可导出");
          }
        }
      });
      this.infoVisible = false;
    },
    exportExcel(array, sheetName, fileName1) {
      var list = this.getExportDataList(array);
      const workBook = XLSX.utils.book_new();
      const workSheet = XLSX.utils.json_to_sheet(list, { skipHeader: true });
      const columnWidths = {};
      var a = "";
      list.forEach(row => {
        Object.keys(row).forEach(key => {
          const cellValue = row[key] ? row[key].toString() : "";
          const cellValue1 = key ? key.toString() : "";
          if (cellValue.length > cellValue1.length) {
            a = cellValue.length;
          } else {
            a = cellValue1.length;
          }
          if (!columnWidths[key] || a > columnWidths[key]) {
            columnWidths[key] = a;
          }
        });
      });
      workSheet["!cols"] = Object.keys(columnWidths).map(key => ({
        wch: columnWidths[key] + 5,
      }));
      for (let key in workSheet) {
        if (key == "!ref" || key == "!merges" || key == "!cols" || key == "!rows") {
          continue;
        } else {
          workSheet[key].s = {
            border: {
              top: { style: "thin" },
              bottom: { style: "thin" },
              left: { style: "thin" },
              right: { style: "thin" },
            },
            font: { sz: 12 },
            alignment: {
              horizontal: "left", // 水平（向左、向右、居中）
              vertical: "center", // 上下（向上、向下、居中）
              wrapText: false, // 设置单元格自动换行，目前仅对非合并单元格生效
              indent: 0, // 设置单元格缩进
            },
          };
          if (key.match(/\d+/g).join("") == "1") {
            workSheet[key].s = {
              ...workSheet[key].s,
              fill: {
                fgColor: { rgb: "00B050" },
              },
              font: {
                bold: true,
                sz: 12,
              },
            };
          }
        }
      }
      XLSX.utils.book_append_sheet(workBook, workSheet, sheetName);
      const tmpDown = new Blob([
        this.s2ab(
          XLSX_STYLE.write(workBook, {
            bookType: "xlsx",
            bookSST: true,
            type: "binary",
            cellStyles: true,
          })
        ),
      ]);
      this.downloadExcelFile(tmpDown, fileName1);
    },
    downloadExcelFile(obj, fileName) {
      const a_node = document.createElement("a");
      a_node.download = fileName;
      if ("msSaveOrOpenBlob" in navigator) {
        window.navigator.msSaveOrOpenBlob(obj, fileName);
      } else {
        a_node.href = URL.createObjectURL(obj);
      }
      a_node.click();
      setTimeout(() => {
        URL.revokeObjectURL(obj);
      }, 2000);
    },
    s2ab(s) {
      if (typeof ArrayBuffer !== "undefined") {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i != s.length; ++i) {
          view[i] = s.charCodeAt(i) & 0xff;
        }
        return buf;
      } else {
        const buf = new Array(s.length);
        for (let i = 0; i != s.length; ++i) {
          buf[i] = s.charCodeAt(i) & 0xff;
        }
        return buf;
      }
    },
    getExportDataList(arr) {
      arr.forEach(item => {
        Object.keys(item).forEach(key => {
          if (item[key] === null || item[key] === undefined) {
            item[key] = "";
          }
        });
      });
      const thList1 = Object.keys(arr[0]);
      const keyList1 = Object.keys(arr[0]);
      const targetList1 = arr;
      const tdList = this.formatJson(keyList1, targetList1); // 过滤字段以及转换数据格式，即：表格数据
      tdList.unshift(thList1); // 将 thList 数组添加到 tdList 数组开头，即：表格头部
      const list = tdList;
      return list;
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(item => {
          return v[item];
        })
      );
    },
    handleOk() {
      this.params1 = this.$refs.queryInfo.form;
      var r = /^\+?[0-9][0-9]*$/;
      var a = /^\d+-\d+$/;
      if (this.params1.BoardLayers && !r.test(this.params1.BoardLayers) && !a.test(this.params1.BoardLayers)) {
        this.$message.warning("层数请输入正确格式");
        return;
      }
      if (this.params1.BoardLayers) {
        if (this.params1.BoardLayers.indexOf("-") != -1) {
          var arr = this.params1.BoardLayers.split("-");
          if (Number(arr[0]) > Number(arr[1])) {
            this.$message.warning("开始层数不得大于结束层数");
            return;
          }
          this.params1.BoardLayersStart = Number(arr[0]);
          this.params1.BoardLayersEnd = Number(arr[1]);
        } else {
          this.params1.BoardLayersStart = Number(this.params1.BoardLayers);
        }
      }
      delete this.params1.BoardLayers;

      var arr1 = this.params1.OrderNo.split("");
      if (arr1.length > 20) {
        arr1 = arr1.slice(0, 20);
      }
      this.params1.OrderNo = arr1.join("");
      var arr2 = this.params1.custNo.split("");
      if (arr2.length > 10) {
        arr2 = arr2.slice(0, 10);
      }
      this.params1.custNo = arr2.join("");
      var arr3 = this.params1.PcbFileName.split("");
      if (arr3.length > 100) {
        arr3 = arr3.slice(0, 100);
      }
      this.params1.PcbFileName = arr3.join("");
      var arr4 = this.params1.proOrderNo.split("");
      if (arr4.length > 20) {
        arr4 = arr4.slice(0, 20);
      }
      this.params1.proOrderNo = arr4.join("");
      if (this.params1.proOrderNo && typeof this.params1.proOrderNo === "string" && this.params1.proOrderNo.replace(/\s+/g, "").length < 5) {
        this.$message.error("生产型号查询不能小于5位");
        return;
      }
      this.dataVisible = false;
      this.pagination.current = 1;
      this.getOrderList(this.params1);
      this.$refs.orderTable.selectedRowKeysArray = [];
      this.$refs.orderTable.selectedRowsData = {};
      this.topdata = [];
      this.centerdata = [];
      this.botdata = [];
    },
    getOrderList(queryData) {
      let params = {
        PageIndex: this.pagination.current,
        PageSize: this.pagination.pageSize,
      };
      if (queryData) {
        params = Object.assign(params, queryData);
      }
      params.PcbFileName = params.PcbFileName ? params.PcbFileName.replace(/\s+/g, " ").trim() : "";
      this.orderListload = true;
      businessverifyPageList(params)
        .then(res => {
          if (res.code) {
            this.orderListData = res.data.items;
            if (this.iscolumnKey) {
              this.orderListData.sort((a, b) => {
                let aValue = a[this.iscolumnKey];
                let bValue = b[this.iscolumnKey];
                if (this.iscolumnKey === "deliveryDate1") {
                  if (aValue === null) {
                    return 1;
                  }
                  if (bValue === null) {
                    return -1;
                  }
                  return this.isorder === "ascend" ? aValue.localeCompare(bValue) : this.isorder === "descend" ? bValue.localeCompare(aValue) : 0;
                }
                if (typeof aValue === "string" && typeof bValue === "string") {
                  return this.isorder === "ascend" ? aValue.localeCompare(bValue) : this.isorder === "descend" ? bValue.localeCompare(aValue) : 0;
                } else {
                  if (aValue === bValue) {
                    return this.orderListData.indexOf(a) - this.orderListData.indexOf(b);
                  }
                  if (this.isorder === "ascend") {
                    return aValue > bValue ? 1 : -1;
                  } else if (this.isorder === "descend") {
                    return aValue < bValue ? 1 : -1;
                  }
                  return 0;
                }
              });
            }
            const pagination = { ...this.pagination };
            pagination.total = res.data.totalCount;
            this.pagination = pagination;
          }
        })
        .finally(() => {
          this.orderListload = false;
        });
    },
    //获取多套价格
    gettopdata(id) {
      if (id == undefined) {
        this.topdata = [];
        this.centerdata = [];
        this.botdata = [];
        return;
      }
      this.loading2 = true;
      result4ParameterList(id)
        .then(res => {
          if (res.data.length > 1) {
            this.topdata = res.data.filter(item => item.tag_ != -1);
            this.topdata.pop();
            var index = 0;
            if (this.topdata.findIndex(v => v.isProduction == true) < 0) {
              index = 0;
            } else {
              index = this.topdata.findIndex(v => v.isProduction == true);
            }
            this.getcenterdata(this.topdata[index]);
          }
        })
        .finally(() => {
          this.loading2 = false;
        });
    },
    // 获取价格结果列表
    getcenterdata(record) {
      this.loading3 = true;
      resultList(record.id)
        .then(res => {
          if (res.data) {
            this.centerdata = res.data;
            let index = res.data.findIndex(v => v.priceName.indexOf("平米价") != -1);
            if (this.centerdata.length > 0 && index != -1) {
              this.guid4Parameter = this.centerdata[index].guid4Parameter + this.centerdata[index].guid4Order + this.centerdata[index].guid4Calc;
              this.getbotdata(this.centerdata[index]);
            } else {
              this.botdata = [];
            }
          }
        })
        .finally(() => {
          this.loading3 = false;
        });
    },
    // 获取价格明细列表
    getbotdata(record) {
      this.loading4 = true;
      if (record.calcNameID == "1013501" || record.calcNameID == "1013510") {
        resultDetailList(record.guid4Parameter, record.calcNameID)
          .then(res => {
            if (res.data) {
              this.botdata = res.data;
            }
          })
          .finally(() => {
            this.loading4 = false;
          });
      } else {
        this.botdata = [];
        this.loading4 = false;
      }
    },
    // 订单表变化change
    handleTableChange(pagination, filters, sorter) {
      let { columnKey, order } = sorter;
      this.iscolumnKey = columnKey;
      this.isorder = order;
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;
      if (JSON.stringify(this.params1) != "{}") {
        this.getOrderList(this.params1);
      } else {
        this.getOrderList();
      }
    },
    onClickRow2(record) {
      return {
        on: {
          click: () => {
            this.id = record.id;
            this.getcenterdata(record);
          },
        },
      };
    },
    onClickRow3(record) {
      return {
        on: {
          click: () => {
            this.guid4Parameter = record.guid4Parameter + record.guid4Order + record.guid4Calc;
            this.getbotdata(record);
          },
        },
      };
    },
    // 行点击事件
    isRedRow2(record) {
      if (record.id == this.id) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
    isRedRow3(record) {
      if (record.guid4Parameter + record.guid4Order + record.guid4Calc == this.guid4Parameter) {
        return "rowBackgroundColor";
      } else {
        return "";
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 0;
}
/deep/.ant-modal {
  padding-bottom: 0;
}
.formclass1 {
  /deep/.ant-modal-body {
    padding: 0;
  }
}
.required {
  /deep/.ant-form-item-label label {
    color: red;
  }
}
.genjin {
  .ant-row {
    display: flex;
    margin-bottom: 0;
  }
}
.projectackend {
  /deep/.ant-table-row-cell-break-word {
    white-space: normal !important;
  }
}
.formclass {
  /deep/.ant-modal-body {
    padding: 0;
  }
  /deep/.ant-modal-footer {
    padding: 10px 100px;
  }
}
.tabRightClikBox {
  // border:2px solid rgb(238, 238, 238) !important;
  li {
    height: 30px;
    line-height: 30px;
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: 1px solid rgb(225, 223, 223);
    background-color: white !important;
    color: #000000;
  }
  .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }
}
.yushen {
  /deep/.ant-modal-footer {
    padding: 8px;
    border-top: none;
  }
  /deep/.ant-modal-body {
    padding-top: 0px;
    padding-bottom: 0px;
    overflow: auto;
    // overflow-y: auto;
    // overflow-x: hidden;
  }
  /deep/.ant-modal-header {
    border-bottom: 1px solid #ddd;
    padding: 9px 24px;
  }
}
/deep/.ant-table-row-cell-break-word {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.BusinessTracking {
  user-select: none;
  height: 780px;
  min-width: 1670px;
  background: #ffffff;
  .lefttable {
    width: 67%;
    border: 2px solid rgb(233, 233, 240);
    /deep/.min-table {
      .ant-table-body {
        min-height: 734px;
      }
      .ant-table-placeholder {
        display: none;
      }
    }
  }
  .righttable {
    /deep/.ant-empty-normal {
      margin: 5px 0;
    }
    border-bottom: 2px solid rgb(233, 233, 240);
    width: 33%;
    height: 776px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .toptable {
      height: 20%;
      width: 100%;
    }
    .centertable {
      height: 45%;
      width: 100%;
    }
    .bottable {
      height: 35%;
      width: 100%;
    }
  }
  .footeraction {
    width: 100%;
    height: 48px;
    border: 2px solid #e9e9f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    background: #ffffff;
    border-top: 0;
  }
}

/deep/ .ant-table-pagination.ant-pagination {
  float: left;
  margin: 12px 0 0 10px;
  position: absolute;
}
/deep/ .ant-table-tbody .ant-table-row:nth-child(2n) {
  background: #f8f8f8;
}
/deep/.ant-table-thead > tr > th .ant-table-column-sorter {
  display: none;
  vertical-align: middle;
}
/deep/ .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #dfdcdc;
}
/deep/ .ant-table {
  .ant-table-tbody {
    tr.ant-table-row-hover td {
      background: #dfdcdc;
    }
  }
  .rowBackgroundColor {
    background: #c9c9c9 !important;
  }
  .ant-table-header {
    border-top: 1px solid #efefef;
    border-right: 1px solid #efefef;
  }

  .ant-table-thead > tr > th {
    padding: 4px 4px;
    border-right: 1px solid #efefef;
    border-left: 1px solid #efefef;
  }
  .ant-table-tbody > tr > td {
    padding: 4px 4px !important;
    border-right: 1px solid #efefef;
    border-left: 1px solid #efefef;
    max-width: 100px;
    color: #000000;
  }
  tr.ant-table-row-selected td {
    background: #dfdcdc;
  }
  tr.ant-table-row-hover td {
    background: #dfdcdc;
  }
}
</style>
